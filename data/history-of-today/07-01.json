{"date": "July 1", "url": "https://wikipedia.org/wiki/July_1", "data": {"Events": [{"year": "69", "text": "<PERSON><PERSON><PERSON> orders his Roman legions in Alexandria to swear allegiance to <PERSON><PERSON><PERSON><PERSON> as Emperor.", "html": "69 - <a href=\"https://wikipedia.org/wiki/Tiber<PERSON>_Julius_Alexander\" title=\"Tiber<PERSON> Julius Alexander\">T<PERSON><PERSON> Julius <PERSON></a> orders his <a href=\"https://wikipedia.org/wiki/Roman_legion\" title=\"Roman legion\">Roman legions</a> in <a href=\"https://wikipedia.org/wiki/Alexandria\" title=\"Alexandria\">Alexandria</a> to swear <a href=\"https://wikipedia.org/wiki/Allegiance\" title=\"Allegiance\">allegiance</a> to <a href=\"https://wikipedia.org/wiki/Vespasian\" title=\"Vespasian\">Vespasian</a> as <a href=\"https://wikipedia.org/wiki/Roman_emperor\" title=\"Roman emperor\">Emperor</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/T<PERSON><PERSON>_Julius_Alexander\" title=\"Tiberius Julius Alexander\">T<PERSON><PERSON></a> orders his <a href=\"https://wikipedia.org/wiki/Roman_legion\" title=\"Roman legion\">Roman legions</a> in <a href=\"https://wikipedia.org/wiki/Alexandria\" title=\"Alexandria\">Alexandria</a> to swear <a href=\"https://wikipedia.org/wiki/Allegiance\" title=\"Allegiance\">allegiance</a> to <a href=\"https://wikipedia.org/wiki/Vespasian\" title=\"Vespasian\">Vespasian</a> as <a href=\"https://wikipedia.org/wiki/Roman_emperor\" title=\"Roman emperor\">Emperor</a>.", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>"}, {"title": "Roman legion", "link": "https://wikipedia.org/wiki/Roman_legion"}, {"title": "Alexandria", "link": "https://wikipedia.org/wiki/Alexandria"}, {"title": "Allegiance", "link": "https://wikipedia.org/wiki/Allegiance"}, {"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Vespasian"}, {"title": "Roman emperor", "link": "https://wikipedia.org/wiki/Roman_emperor"}]}, {"year": "552", "text": "Battle of Taginae: Byzantine forces under <PERSON><PERSON><PERSON> defeat the Ostrogoths in Italy, and the Ostrogoth king, <PERSON><PERSON><PERSON>, is mortally wounded.", "html": "552 - <a href=\"https://wikipedia.org/wiki/Battle_of_Taginae\" title=\"Battle of Taginae\">Battle of Taginae</a>: <a href=\"https://wikipedia.org/wiki/Byzantine_Empire\" title=\"Byzantine Empire\">Byzantine</a> forces under <a href=\"https://wikipedia.org/wiki/Narses\" title=\"Narses\">Narses</a> defeat the <a href=\"https://wikipedia.org/wiki/Ostrogoths\" title=\"Ostrogoths\">Ostrogoths</a> in Italy, and the Ostrogoth king, <a href=\"https://wikipedia.org/wiki/Totila\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, is mortally wounded.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Battle_of_Taginae\" title=\"Battle of Taginae\">Battle of Taginae</a>: <a href=\"https://wikipedia.org/wiki/Byzantine_Empire\" title=\"Byzantine Empire\">Byzantine</a> forces under <a href=\"https://wikipedia.org/wiki/Narses\" title=\"Narses\">Narses</a> defeat the <a href=\"https://wikipedia.org/wiki/Ostrogoths\" title=\"Ostrogoths\">Ostrogoths</a> in Italy, and the Ostrogoth king, <a href=\"https://wikipedia.org/wiki/Totila\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, is mortally wounded.", "links": [{"title": "Battle of Taginae", "link": "https://wikipedia.org/wiki/Battle_of_Taginae"}, {"title": "Byzantine Empire", "link": "https://wikipedia.org/wiki/Byzantine_Empire"}, {"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Narses"}, {"title": "Ostrogoths", "link": "https://wikipedia.org/wiki/Ostrogoths"}, {"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>a"}]}, {"year": "1097", "text": "Battle of Dorylaeum: Crusaders led by prince <PERSON><PERSON><PERSON> of Taranto defeat a Seljuk army led by sultan <PERSON><PERSON><PERSON>.", "html": "1097 - <a href=\"https://wikipedia.org/wiki/Battle_of_Dorylaeum_(1097)\" title=\"Battle of Dorylaeum (1097)\">Battle of Dorylaeum</a>: <a href=\"https://wikipedia.org/wiki/Crusaders\" class=\"mw-redirect\" title=\"Crusaders\">Crusaders</a> led by prince <a href=\"https://wikipedia.org/wiki/Bohemond_I_of_Antioch\" title=\"Bohemond I of Antioch\">Bohemond of Taranto</a> defeat a <a href=\"https://wikipedia.org/wiki/Seljuk_Turks\" class=\"mw-redirect\" title=\"Seljuk Turks\">Seljuk</a> army led by sultan <a href=\"https://wikipedia.org/wiki/Kilij_A<PERSON>lan_I\" title=\"Ki<PERSON>j A<PERSON>lan <PERSON>\"><PERSON><PERSON><PERSON><PERSON> I</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Battle_of_Dorylaeum_(1097)\" title=\"Battle of Dorylaeum (1097)\">Battle of Dorylaeum</a>: <a href=\"https://wikipedia.org/wiki/Crusaders\" class=\"mw-redirect\" title=\"Crusaders\">Crusaders</a> led by prince <a href=\"https://wikipedia.org/wiki/Bohemond_I_of_Antioch\" title=\"Bohemond I of Antioch\">Bohemond of Taranto</a> defeat a <a href=\"https://wikipedia.org/wiki/Seljuk_Turks\" class=\"mw-redirect\" title=\"Seljuk Turks\">Seljuk</a> army led by sultan <a href=\"https://wikipedia.org/wiki/Kilij_A<PERSON>lan_I\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON> I</a>.", "links": [{"title": "Battle of Dorylaeum (1097)", "link": "https://wikipedia.org/wiki/Battle_of_Dorylaeum_(1097)"}, {"title": "Crusaders", "link": "https://wikipedia.org/wiki/Crusaders"}, {"title": "<PERSON><PERSON><PERSON> I of Antioch", "link": "https://wikipedia.org/wiki/Bohemond_I_of_Antioch"}, {"title": "Seljuk Turks", "link": "https://wikipedia.org/wiki/Seljuk_Turks"}, {"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1431", "text": "The Battle of La Higueruela takes place in Granada, leading to a modest advance of the Kingdom of Castile during the Reconquista.", "html": "1431 - The <a href=\"https://wikipedia.org/wiki/Battle_of_La_Higueruela\" title=\"Battle of La Higueruela\">Battle of La Higueruela</a> takes place in <a href=\"https://wikipedia.org/wiki/Granada\" title=\"Granada\">Granada</a>, leading to a modest advance of the <a href=\"https://wikipedia.org/wiki/Kingdom_of_Castile\" title=\"Kingdom of Castile\">Kingdom of Castile</a> during the <a href=\"https://wikipedia.org/wiki/Reconquista\" title=\"Reconquista\">Reconquista</a>.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Battle_of_La_Higueruela\" title=\"Battle of La Higueruela\">Battle of La Higueruela</a> takes place in <a href=\"https://wikipedia.org/wiki/Granada\" title=\"Granada\">Granada</a>, leading to a modest advance of the <a href=\"https://wikipedia.org/wiki/Kingdom_of_Castile\" title=\"Kingdom of Castile\">Kingdom of Castile</a> during the <a href=\"https://wikipedia.org/wiki/Reconquista\" title=\"Reconquista\">Reconquista</a>.", "links": [{"title": "Battle of La Higueruela", "link": "https://wikipedia.org/wiki/Battle_of_La_Higueruela"}, {"title": "Granada", "link": "https://wikipedia.org/wiki/Granada"}, {"title": "Kingdom of Castile", "link": "https://wikipedia.org/wiki/Kingdom_of_Castile"}, {"title": "Reconquista", "link": "https://wikipedia.org/wiki/Reconquista"}]}, {"year": "1520", "text": "Spanish conquistadors led by <PERSON><PERSON><PERSON> fight their way out of Tenochtitlan after nightfall.", "html": "1520 - Spanish <a href=\"https://wikipedia.org/wiki/Conquistador\" title=\"Conquistador\">conquistadors</a> led by <a href=\"https://wikipedia.org/wiki/Hern%C3%A1n_Cort%C3%A9s\" title=\"Herná<PERSON>\"><PERSON><PERSON><PERSON></a> <a href=\"https://wikipedia.org/wiki/La_Noche_Triste\" title=\"La Noche Triste\">fight their way</a> out of <a href=\"https://wikipedia.org/wiki/Tenochtitlan\" title=\"Tenochtitlan\">Tenochtitlan</a> after nightfall.", "no_year_html": "Spanish <a href=\"https://wikipedia.org/wiki/Conquistador\" title=\"Conquistador\">conquistadors</a> led by <a href=\"https://wikipedia.org/wiki/Hern%C3%A1n_Cort%C3%A9s\" title=\"Herná<PERSON>\">Her<PERSON><PERSON></a> <a href=\"https://wikipedia.org/wiki/La_Noche_Triste\" title=\"La Noche Triste\">fight their way</a> out of <a href=\"https://wikipedia.org/wiki/Tenochtitlan\" title=\"Tenochtitlan\">Tenochtitlan</a> after nightfall.", "links": [{"title": "Conquistador", "link": "https://wikipedia.org/wiki/Conquistador"}, {"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Hern%C3%A1n_Cort%C3%A9s"}, {"title": "La Noche Triste", "link": "https://wikipedia.org/wiki/La_Noche_Triste"}, {"title": "Tenochtitlan", "link": "https://wikipedia.org/wiki/Tenochtitlan"}]}, {"year": "1523", "text": "<PERSON> and <PERSON><PERSON><PERSON> become the first Lutheran martyrs, burned at the stake by Roman Catholic authorities in Brussels.", "html": "1523 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_and_<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON> and <PERSON><PERSON><PERSON>\"><PERSON> and <PERSON><PERSON><PERSON></a> become the first <a href=\"https://wikipedia.org/wiki/Lutheran\" class=\"mw-redirect\" title=\"Lutheran\">Lutheran</a> martyrs, burned at the stake by <a href=\"https://wikipedia.org/wiki/Roman_Catholic\" class=\"mw-redirect\" title=\"Roman Catholic\">Roman Catholic</a> authorities in <a href=\"https://wikipedia.org/wiki/Brussels\" title=\"Brussels\">Brussels</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_and_<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON> and <PERSON><PERSON><PERSON>\"><PERSON> and <PERSON><PERSON><PERSON></a> become the first <a href=\"https://wikipedia.org/wiki/Lutheran\" class=\"mw-redirect\" title=\"Lutheran\">Lutheran</a> martyrs, burned at the stake by <a href=\"https://wikipedia.org/wiki/Roman_Catholic\" class=\"mw-redirect\" title=\"Roman Catholic\">Roman Catholic</a> authorities in <a href=\"https://wikipedia.org/wiki/Brussels\" title=\"Brussels\">Brussels</a>.", "links": [{"title": "<PERSON> and <PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_and_<PERSON><PERSON><PERSON>_<PERSON>"}, {"title": "Lutheran", "link": "https://wikipedia.org/wiki/Lutheran"}, {"title": "Roman Catholic", "link": "https://wikipedia.org/wiki/Roman_Catholic"}, {"title": "Brussels", "link": "https://wikipedia.org/wiki/Brussels"}]}, {"year": "1569", "text": "Union of Lublin: The Kingdom of Poland and the Grand Duchy of Lithuania confirm a real union; the united country is called the Polish-Lithuanian Commonwealth or the Republic of Both Nations.", "html": "1569 - <a href=\"https://wikipedia.org/wiki/Union_of_Lublin\" title=\"Union of Lublin\">Union of Lublin</a>: The Kingdom of Poland and the <a href=\"https://wikipedia.org/wiki/Grand_Duchy_of_Lithuania\" title=\"Grand Duchy of Lithuania\">Grand Duchy of Lithuania</a> confirm a <a href=\"https://wikipedia.org/wiki/Real_union\" title=\"Real union\">real union</a>; the united country is called the <a href=\"https://wikipedia.org/wiki/Polish%E2%80%93Lithuanian_Commonwealth\" title=\"Polish-Lithuanian Commonwealth\">Polish-Lithuanian Commonwealth</a> or the Republic of Both Nations.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Union_of_Lublin\" title=\"Union of Lublin\">Union of Lublin</a>: The Kingdom of Poland and the <a href=\"https://wikipedia.org/wiki/Grand_Duchy_of_Lithuania\" title=\"Grand Duchy of Lithuania\">Grand Duchy of Lithuania</a> confirm a <a href=\"https://wikipedia.org/wiki/Real_union\" title=\"Real union\">real union</a>; the united country is called the <a href=\"https://wikipedia.org/wiki/Polish%E2%80%93Lithuanian_Commonwealth\" title=\"Polish-Lithuanian Commonwealth\">Polish-Lithuanian Commonwealth</a> or the Republic of Both Nations.", "links": [{"title": "Union of Lublin", "link": "https://wikipedia.org/wiki/Union_of_Lublin"}, {"title": "Grand Duchy of Lithuania", "link": "https://wikipedia.org/wiki/Grand_Duchy_of_Lithuania"}, {"title": "Real union", "link": "https://wikipedia.org/wiki/Real_union"}, {"title": "Polish-Lithuanian Commonwealth", "link": "https://wikipedia.org/wiki/Polish%E2%80%93Lithuanian_Commonwealth"}]}, {"year": "1643", "text": "First meeting of the Westminster Assembly, a council of theologians (\"divines\") and members of the Parliament of England appointed to restructure the Church of England, at Westminster Abbey in London.", "html": "1643 - First meeting of the <a href=\"https://wikipedia.org/wiki/Westminster_Assembly\" title=\"Westminster Assembly\">Westminster Assembly</a>, a council of theologians (\"divines\") and members of the <a href=\"https://wikipedia.org/wiki/Parliament_of_England\" title=\"Parliament of England\">Parliament of England</a> appointed to restructure the <a href=\"https://wikipedia.org/wiki/Church_of_England\" title=\"Church of England\">Church of England</a>, at <a href=\"https://wikipedia.org/wiki/Westminster_Abbey\" title=\"Westminster Abbey\">Westminster Abbey</a> in London.", "no_year_html": "First meeting of the <a href=\"https://wikipedia.org/wiki/Westminster_Assembly\" title=\"Westminster Assembly\">Westminster Assembly</a>, a council of theologians (\"divines\") and members of the <a href=\"https://wikipedia.org/wiki/Parliament_of_England\" title=\"Parliament of England\">Parliament of England</a> appointed to restructure the <a href=\"https://wikipedia.org/wiki/Church_of_England\" title=\"Church of England\">Church of England</a>, at <a href=\"https://wikipedia.org/wiki/Westminster_Abbey\" title=\"Westminster Abbey\">Westminster Abbey</a> in London.", "links": [{"title": "Westminster Assembly", "link": "https://wikipedia.org/wiki/Westminster_Assembly"}, {"title": "Parliament of England", "link": "https://wikipedia.org/wiki/Parliament_of_England"}, {"title": "Church of England", "link": "https://wikipedia.org/wiki/Church_of_England"}, {"title": "Westminster Abbey", "link": "https://wikipedia.org/wiki/Westminster_Abbey"}]}, {"year": "1690", "text": "War of the Grand Alliance: Marshal <PERSON> triumphs over an Anglo-Dutch army at the battle of Fleurus.", "html": "1690 - <a href=\"https://wikipedia.org/wiki/War_of_the_Grand_Alliance\" class=\"mw-redirect\" title=\"War of the Grand Alliance\">War of the Grand Alliance</a>: <a href=\"https://wikipedia.org/wiki/Fran%C3%<PERSON><PERSON><PERSON><PERSON>,_duc_de_<PERSON>\" title=\"<PERSON><PERSON>, duc de <PERSON>\">Marshal <PERSON></a> triumphs over an Anglo-Dutch army at the <a href=\"https://wikipedia.org/wiki/Battle_of_Fleurus_(1690)\" title=\"Battle of Fleurus (1690)\">battle of Fleurus</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/War_of_the_Grand_Alliance\" class=\"mw-redirect\" title=\"War of the Grand Alliance\">War of the Grand Alliance</a>: <a href=\"https://wikipedia.org/wiki/Fran%C3%<PERSON><PERSON><PERSON><PERSON><PERSON>,_duc_de_<PERSON>\" title=\"<PERSON><PERSON>, duc de <PERSON>\">Marshal <PERSON></a> triumphs over an Anglo-Dutch army at the <a href=\"https://wikipedia.org/wiki/Battle_of_Fleurus_(1690)\" title=\"Battle of Fleurus (1690)\">battle of Fleurus</a>.", "links": [{"title": "War of the Grand Alliance", "link": "https://wikipedia.org/wiki/War_of_the_Grand_Alliance"}, {"title": "<PERSON><PERSON><PERSON>, duc de Luxembourg", "link": "https://wikipedia.org/wiki/Fran%C3%<PERSON><PERSON><PERSON>-<PERSON>_<PERSON>,_duc_de_Luxembourg"}, {"title": "Battle of Fleurus (1690)", "link": "https://wikipedia.org/wiki/Battle_of_Fleurus_(1690)"}]}, {"year": "1690", "text": "Glorious Revolution: Battle of the Boyne in Ireland (as reckoned under the Julian calendar).", "html": "1690 - <a href=\"https://wikipedia.org/wiki/Glorious_Revolution\" title=\"Glorious Revolution\">Glorious Revolution</a>: <a href=\"https://wikipedia.org/wiki/Battle_of_the_Boyne\" title=\"Battle of the Boyne\">Battle of the Boyne</a> in Ireland (as reckoned under the <a href=\"https://wikipedia.org/wiki/Julian_calendar\" title=\"Julian calendar\">Julian calendar</a>).", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Glorious_Revolution\" title=\"Glorious Revolution\">Glorious Revolution</a>: <a href=\"https://wikipedia.org/wiki/Battle_of_the_Boyne\" title=\"Battle of the Boyne\">Battle of the Boyne</a> in Ireland (as reckoned under the <a href=\"https://wikipedia.org/wiki/Julian_calendar\" title=\"Julian calendar\">Julian calendar</a>).", "links": [{"title": "Glorious Revolution", "link": "https://wikipedia.org/wiki/Glorious_Revolution"}, {"title": "Battle of the Boyne", "link": "https://wikipedia.org/wiki/Battle_of_the_Boyne"}, {"title": "Julian calendar", "link": "https://wikipedia.org/wiki/Julian_calendar"}]}, {"year": "1766", "text": "<PERSON><PERSON><PERSON>, a young French nobleman, is tortured and beheaded before his body is burnt on a pyre along with a copy of <PERSON><PERSON>'s Dictionnaire philosophique nailed to his torso for the crime of not saluting a Roman Catholic religious procession in Abbeville, France.", "html": "1766 - <a href=\"https://wikipedia.org/wiki/Fran%C3%A<PERSON><PERSON>-<PERSON>_<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, a young French nobleman, is tortured and beheaded before his body is burnt on a <a href=\"https://wikipedia.org/wiki/Pyre\" title=\"Pyre\">pyre</a> along with a copy of <a href=\"https://wikipedia.org/wiki/Voltaire\" title=\"Vol<PERSON>\"><PERSON><PERSON></a>'s <i><a href=\"https://wikipedia.org/wiki/Dictionnaire_philosophique\" title=\"Dictionnaire philosophique\">Dictionnaire philosophique</a></i> nailed to his torso for the crime of not saluting a <a href=\"https://wikipedia.org/wiki/Roman_Catholic\" class=\"mw-redirect\" title=\"Roman Catholic\">Roman Catholic</a> religious procession in <a href=\"https://wikipedia.org/wiki/Abbeville\" title=\"Abbeville\">Abbeville</a>, France.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Fran%C3%A<PERSON><PERSON>-<PERSON>_<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, a young French nobleman, is tortured and beheaded before his body is burnt on a <a href=\"https://wikipedia.org/wiki/Pyre\" title=\"Pyre\">pyre</a> along with a copy of <a href=\"https://wikipedia.org/wiki/Voltaire\" title=\"Vol<PERSON>\"><PERSON><PERSON></a>'s <i><a href=\"https://wikipedia.org/wiki/Dictionnaire_philosophique\" title=\"Dictionnaire philosophique\">Dictionnaire philosophique</a></i> nailed to his torso for the crime of not saluting a <a href=\"https://wikipedia.org/wiki/Roman_Catholic\" class=\"mw-redirect\" title=\"Roman Catholic\">Roman Catholic</a> religious procession in <a href=\"https://wikipedia.org/wiki/Abbeville\" title=\"Abbeville\">Abbeville</a>, France.", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Fran%C3%A<PERSON><PERSON>-<PERSON>_<PERSON>_<PERSON>_<PERSON>"}, {"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Pyre"}, {"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Voltaire"}, {"title": "Dictionnaire philosophique", "link": "https://wikipedia.org/wiki/Dictionnaire_philosophique"}, {"title": "Roman Catholic", "link": "https://wikipedia.org/wiki/Roman_Catholic"}, {"title": "Abbeville", "link": "https://wikipedia.org/wiki/Abbeville"}]}, {"year": "1770", "text": "<PERSON><PERSON>'s Comet is seen closer to the Earth than any other comet in recorded history, approaching to a distance of 0.0146 astronomical units (2,180,000 km; 1,360,000 mi).", "html": "1770 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>%27s_Comet\" title=\"<PERSON><PERSON>'s Comet\"><PERSON><PERSON>'s Comet</a> is seen closer to the Earth than any other comet in recorded history, approaching to a distance of 0.0146 <a href=\"https://wikipedia.org/wiki/Astronomical_unit\" title=\"Astronomical unit\">astronomical units</a> (2,180,000 km; 1,360,000 mi).", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>%27s_Comet\" title=\"<PERSON><PERSON>'s Comet\"><PERSON><PERSON>'s Comet</a> is seen closer to the Earth than any other comet in recorded history, approaching to a distance of 0.0146 <a href=\"https://wikipedia.org/wiki/Astronomical_unit\" title=\"Astronomical unit\">astronomical units</a> (2,180,000 km; 1,360,000 mi).", "links": [{"title": "<PERSON><PERSON>'s Comet", "link": "https://wikipedia.org/wiki/Lexell%27s_Comet"}, {"title": "Astronomical unit", "link": "https://wikipedia.org/wiki/Astronomical_unit"}]}, {"year": "1782", "text": "Raid on Lunenburg: American privateers attack the British settlement of Lunenburg, Nova Scotia.", "html": "1782 - <a href=\"https://wikipedia.org/wiki/Raid_on_Lunenburg,_Nova_Scotia_(1782)\" title=\"Raid on Lunenburg, Nova Scotia (1782)\">Raid on Lunenburg</a>: American <a href=\"https://wikipedia.org/wiki/Privateer\" title=\"Privateer\">privateers</a> attack the British settlement of <a href=\"https://wikipedia.org/wiki/Lunenburg,_Nova_Scotia\" title=\"Lunenburg, Nova Scotia\">Lunenburg, Nova Scotia</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Raid_on_Lunenburg,_Nova_Scotia_(1782)\" title=\"Raid on Lunenburg, Nova Scotia (1782)\">Raid on Lunenburg</a>: American <a href=\"https://wikipedia.org/wiki/Privateer\" title=\"Privateer\">privateers</a> attack the British settlement of <a href=\"https://wikipedia.org/wiki/Lunenburg,_Nova_Scotia\" title=\"Lunenburg, Nova Scotia\">Lunenburg, Nova Scotia</a>.", "links": [{"title": "Raid on Lunenburg, Nova Scotia (1782)", "link": "https://wikipedia.org/wiki/Raid_on_Lunenburg,_Nova_Scotia_(1782)"}, {"title": "Privateer", "link": "https://wikipedia.org/wiki/Privateer"}, {"title": "Lunenburg, Nova Scotia", "link": "https://wikipedia.org/wiki/Lunenburg,_Nova_Scotia"}]}, {"year": "1819", "text": "<PERSON> discovers the Great Comet of 1819, (C/1819 N1). It is the first comet analyzed using polarimetry, by <PERSON>.", "html": "1819 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> discovers the <a href=\"https://wikipedia.org/wiki/Great_Comet_of_1819\" title=\"Great Comet of 1819\">Great Comet of 1819</a>, (C/1819 N1). It is the first comet analyzed using <a href=\"https://wikipedia.org/wiki/Polarimetry\" title=\"Polarimetry\">polarimetry</a>, by <a href=\"https://wikipedia.org/wiki/Fran%C3%A7<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> discovers the <a href=\"https://wikipedia.org/wiki/Great_Comet_of_1819\" title=\"Great Comet of 1819\">Great Comet of 1819</a>, (C/1819 N1). It is the first comet analyzed using <a href=\"https://wikipedia.org/wiki/Polarimetry\" title=\"Polarimetry\">polarimetry</a>, by <a href=\"https://wikipedia.org/wiki/Fran%C3%A7<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Great Comet of 1819", "link": "https://wikipedia.org/wiki/Great_Comet_of_1819"}, {"title": "Polarimetry", "link": "https://wikipedia.org/wiki/Polarimetry"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Fran%C3%A7ois_Arago"}]}, {"year": "1823", "text": "The five Central American nations of Guatemala, El Salvador, Honduras, Nicaragua, and Costa Rica declare independence from the First Mexican Empire after being annexed the year prior.", "html": "1823 - The five Central American nations of Guatemala, El Salvador, Honduras, Nicaragua, and Costa Rica declare independence from the <a href=\"https://wikipedia.org/wiki/First_Mexican_Empire\" title=\"First Mexican Empire\">First Mexican Empire</a> after being <a href=\"https://wikipedia.org/wiki/Central_America_under_Mexican_rule\" title=\"Central America under Mexican rule\">annexed</a> the year prior.", "no_year_html": "The five Central American nations of Guatemala, El Salvador, Honduras, Nicaragua, and Costa Rica declare independence from the <a href=\"https://wikipedia.org/wiki/First_Mexican_Empire\" title=\"First Mexican Empire\">First Mexican Empire</a> after being <a href=\"https://wikipedia.org/wiki/Central_America_under_Mexican_rule\" title=\"Central America under Mexican rule\">annexed</a> the year prior.", "links": [{"title": "First Mexican Empire", "link": "https://wikipedia.org/wiki/First_Mexican_Empire"}, {"title": "Central America under Mexican rule", "link": "https://wikipedia.org/wiki/Central_America_under_Mexican_rule"}]}, {"year": "1837", "text": "A system of civil registration of births, marriages and deaths is established in England and Wales.", "html": "1837 - A system of <a href=\"https://wikipedia.org/wiki/Civil_registration\" title=\"Civil registration\">civil registration</a> of births, marriages and deaths is established in England and Wales.", "no_year_html": "A system of <a href=\"https://wikipedia.org/wiki/Civil_registration\" title=\"Civil registration\">civil registration</a> of births, marriages and deaths is established in England and Wales.", "links": [{"title": "Civil registration", "link": "https://wikipedia.org/wiki/Civil_registration"}]}, {"year": "1841", "text": "<PERSON> and <PERSON> carve a marker on the Isle of the Dead in Van Diemen's Land to measure tidal variations, one of the earliest surviving benchmarks for sea level rise.", "html": "1841 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> and <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> carve a marker on the <a href=\"https://wikipedia.org/wiki/Isle_of_the_Dead_(Tasmania)\" title=\"Isle of the Dead (Tasmania)\">Isle of the Dead</a> in <a href=\"https://wikipedia.org/wiki/Van_Diemen%27s_Land\" title=\"Van Diemen's Land\">Van Diemen's Land</a> to measure tidal variations, one of the earliest surviving benchmarks for <a href=\"https://wikipedia.org/wiki/Sea_level_rise\" title=\"Sea level rise\">sea level rise</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> and <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> carve a marker on the <a href=\"https://wikipedia.org/wiki/Isle_of_the_Dead_(Tasmania)\" title=\"Isle of the Dead (Tasmania)\">Isle of the Dead</a> in <a href=\"https://wikipedia.org/wiki/Van_Diemen%27s_Land\" title=\"Van Diemen's Land\">Van Diemen's Land</a> to measure tidal variations, one of the earliest surviving benchmarks for <a href=\"https://wikipedia.org/wiki/Sea_level_rise\" title=\"Sea level rise\">sea level rise</a>.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "Isle of the Dead (Tasmania)", "link": "https://wikipedia.org/wiki/Isle_of_the_Dead_(Tasmania)"}, {"title": "Van Diemen's Land", "link": "https://wikipedia.org/wiki/Van_Diemen%27s_Land"}, {"title": "Sea level rise", "link": "https://wikipedia.org/wiki/Sea_level_rise"}]}, {"year": "1855", "text": "Signing of the Quinault Treaty: The Quinault and the Quileute cede their land to the United States.", "html": "1855 - Signing of the <a href=\"https://wikipedia.org/wiki/Quinault_Treaty\" title=\"Quinault Treaty\">Quinault Treaty</a>: The <a href=\"https://wikipedia.org/wiki/Quinault_people\" title=\"Quinault people\"><PERSON><PERSON><PERSON></a> and the <a href=\"https://wikipedia.org/wiki/Quileute\" title=\"Quileute\">Quileute</a> cede their land to the United States.", "no_year_html": "Signing of the <a href=\"https://wikipedia.org/wiki/Quinault_Treaty\" title=\"Quinault Treaty\">Quinault Treaty</a>: The <a href=\"https://wikipedia.org/wiki/Quinault_people\" title=\"Quinault people\"><PERSON><PERSON><PERSON></a> and the <a href=\"https://wikipedia.org/wiki/Quileute\" title=\"Quileute\">Quileute</a> cede their land to the United States.", "links": [{"title": "Quinault Treaty", "link": "https://wikipedia.org/wiki/Quinault_Treaty"}, {"title": "Quinault people", "link": "https://wikipedia.org/wiki/Quinault_people"}, {"title": "Quileute", "link": "https://wikipedia.org/wiki/Quileute"}]}, {"year": "1858", "text": "Joint reading of <PERSON> and <PERSON>'s papers on evolution to the Linnean Society of London.", "html": "1858 - Joint reading of <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> and <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>'s papers on <a href=\"https://wikipedia.org/wiki/Evolution\" title=\"Evolution\">evolution</a> to the <a href=\"https://wikipedia.org/wiki/Linnean_Society_of_London\" title=\"Linnean Society of London\">Linnean Society of London</a>.", "no_year_html": "Joint reading of <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> and <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>'s papers on <a href=\"https://wikipedia.org/wiki/Evolution\" title=\"Evolution\">evolution</a> to the <a href=\"https://wikipedia.org/wiki/Linnean_Society_of_London\" title=\"Linnean Society of London\">Linnean Society of London</a>.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "Evolution", "link": "https://wikipedia.org/wiki/Evolution"}, {"title": "Linnean Society of London", "link": "https://wikipedia.org/wiki/Linnean_Society_of_London"}]}, {"year": "1862", "text": "The Russian State Library is founded as the Library of the Moscow Public Museum.", "html": "1862 - The <a href=\"https://wikipedia.org/wiki/Russian_State_Library\" title=\"Russian State Library\">Russian State Library</a> is founded as the Library of the Moscow Public Museum.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Russian_State_Library\" title=\"Russian State Library\">Russian State Library</a> is founded as the Library of the Moscow Public Museum.", "links": [{"title": "Russian State Library", "link": "https://wikipedia.org/wiki/Russian_State_Library"}]}, {"year": "1862", "text": "Princess <PERSON> of the United Kingdom, second daughter of Queen <PERSON>, marries Prince <PERSON> of Hesse, the future <PERSON>, Grand Duke of Hesse.", "html": "1862 - <a href=\"https://wikipedia.org/wiki/Princess_Alice_of_the_United_Kingdom\" title=\"Princess <PERSON> of the United Kingdom\">Princess <PERSON> of the United Kingdom</a>, second daughter of Queen <PERSON>, marries Prince <PERSON> of Hesse, the future <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Grand_Duke_of_Hesse\" title=\"<PERSON>, Grand Duke of Hesse\"><PERSON>, Grand Duke of Hesse</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Princess_Alice_of_the_United_Kingdom\" title=\"Princess <PERSON> of the United Kingdom\">Princess <PERSON> of the United Kingdom</a>, second daughter of Queen <PERSON>, marries Prince <PERSON> of Hesse, the future <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Grand_Duke_of_Hesse\" title=\"<PERSON>, Grand Duke of Hesse\"><PERSON>, Grand Duke of Hesse</a>.", "links": [{"title": "Princess <PERSON> of the United Kingdom", "link": "https://wikipedia.org/wiki/Princess_Alice_of_the_United_Kingdom"}, {"title": "<PERSON>, Grand Duke of Hesse", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Grand_Duke_of_Hesse"}]}, {"year": "1862", "text": "American Civil War: The Battle of Malvern Hill takes place. It is the last of the Seven Days Battles, part of <PERSON>'s Peninsula Campaign.", "html": "1862 - <a href=\"https://wikipedia.org/wiki/American_Civil_War\" title=\"American Civil War\">American Civil War</a>: The <a href=\"https://wikipedia.org/wiki/Battle_of_Malvern_Hill\" title=\"Battle of Malvern Hill\">Battle of Malvern Hill</a> takes place. It is the last of the <a href=\"https://wikipedia.org/wiki/Seven_Days_Battles\" title=\"Seven Days Battles\">Seven Days Battles</a>, part of <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON><PERSON>\" title=\"<PERSON>\"><PERSON></a>'s <a href=\"https://wikipedia.org/wiki/Peninsula_Campaign\" class=\"mw-redirect\" title=\"Peninsula Campaign\">Peninsula Campaign</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/American_Civil_War\" title=\"American Civil War\">American Civil War</a>: The <a href=\"https://wikipedia.org/wiki/Battle_of_Malvern_Hill\" title=\"Battle of Malvern Hill\">Battle of Malvern Hill</a> takes place. It is the last of the <a href=\"https://wikipedia.org/wiki/Seven_Days_Battles\" title=\"Seven Days Battles\">Seven Days Battles</a>, part of <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON><PERSON>\" title=\"<PERSON>\"><PERSON></a>'s <a href=\"https://wikipedia.org/wiki/Peninsula_Campaign\" class=\"mw-redirect\" title=\"Peninsula Campaign\">Peninsula Campaign</a>.", "links": [{"title": "American Civil War", "link": "https://wikipedia.org/wiki/American_Civil_War"}, {"title": "Battle of Malvern Hill", "link": "https://wikipedia.org/wiki/Battle_of_Malvern_Hill"}, {"title": "Seven Days Battles", "link": "https://wikipedia.org/wiki/Seven_Days_Battles"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>"}, {"title": "Peninsula Campaign", "link": "https://wikipedia.org/wiki/Peninsula_Campaign"}]}, {"year": "1863", "text": "Slavery was abolished in the Dutch colony of Surinam, a date now celebrated as Ketikoti in independent Suriname.", "html": "1863 - Slavery was abolished in the Dutch colony of <a href=\"https://wikipedia.org/wiki/Surinam_(Dutch_colony)\" title=\"Surinam (Dutch colony)\">Surinam</a>, a date now celebrated as <a href=\"https://wikipedia.org/wiki/Ketikoti\" title=\"Ketikoti\">Ketikoti</a> in independent <a href=\"https://wikipedia.org/wiki/Suriname\" title=\"Suriname\">Suriname</a>.", "no_year_html": "Slavery was abolished in the Dutch colony of <a href=\"https://wikipedia.org/wiki/Surinam_(Dutch_colony)\" title=\"Surinam (Dutch colony)\">Surinam</a>, a date now celebrated as <a href=\"https://wikipedia.org/wiki/Ketikoti\" title=\"Ketikoti\">Ketikoti</a> in independent <a href=\"https://wikipedia.org/wiki/Suriname\" title=\"Suriname\">Suriname</a>.", "links": [{"title": "Surinam (Dutch colony)", "link": "https://wikipedia.org/wiki/Surinam_(Dutch_colony)"}, {"title": "Ketikoti", "link": "https://wikipedia.org/wiki/Ketikoti"}, {"title": "Suriname", "link": "https://wikipedia.org/wiki/Suriname"}]}, {"year": "1863", "text": "American Civil War: The Battle of Gettysburg begins.", "html": "1863 - American Civil War: The <a href=\"https://wikipedia.org/wiki/Battle_of_Gettysburg\" title=\"Battle of Gettysburg\">Battle of Gettysburg</a> begins.", "no_year_html": "American Civil War: The <a href=\"https://wikipedia.org/wiki/Battle_of_Gettysburg\" title=\"Battle of Gettysburg\">Battle of Gettysburg</a> begins.", "links": [{"title": "Battle of Gettysburg", "link": "https://wikipedia.org/wiki/Battle_of_Gettysburg"}]}, {"year": "1867", "text": "The British North America Act takes effect as the Province of Canada, New Brunswick, and Nova Scotia join into confederation to create the modern nation of Canada. <PERSON> is sworn in as the first Prime Minister of Canada. This date is commemorated annually in Canada as Canada Day, a national holiday.", "html": "1867 - The <a href=\"https://wikipedia.org/wiki/Constitution_Act,_1867\" title=\"Constitution Act, 1867\"><i>British North America Act</i></a> takes effect as the <a href=\"https://wikipedia.org/wiki/Province_of_Canada\" title=\"Province of Canada\">Province of Canada</a>, <a href=\"https://wikipedia.org/wiki/New_Brunswick\" title=\"New Brunswick\">New Brunswick</a>, and <a href=\"https://wikipedia.org/wiki/Nova_Scotia\" title=\"Nova Scotia\">Nova Scotia</a> <a href=\"https://wikipedia.org/wiki/Canadian_Confederation\" title=\"Canadian Confederation\">join into confederation</a> to create the modern nation of Canada. <a href=\"https://wikipedia.org/wiki/John_<PERSON>_<PERSON>\" title=\"John <PERSON>\"><PERSON></a> is sworn in as the first <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Canada\" title=\"Prime Minister of Canada\">Prime Minister of Canada</a>. This date is commemorated annually in Canada as <a href=\"https://wikipedia.org/wiki/Canada_Day\" title=\"Canada Day\">Canada Day</a>, a national holiday.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Constitution_Act,_1867\" title=\"Constitution Act, 1867\"><i>British North America Act</i></a> takes effect as the <a href=\"https://wikipedia.org/wiki/Province_of_Canada\" title=\"Province of Canada\">Province of Canada</a>, <a href=\"https://wikipedia.org/wiki/New_Brunswick\" title=\"New Brunswick\">New Brunswick</a>, and <a href=\"https://wikipedia.org/wiki/Nova_Scotia\" title=\"Nova Scotia\">Nova Scotia</a> <a href=\"https://wikipedia.org/wiki/Canadian_Confederation\" title=\"Canadian Confederation\">join into confederation</a> to create the modern nation of Canada. <a href=\"https://wikipedia.org/wiki/John_<PERSON>_<PERSON>\" title=\"John <PERSON>\"><PERSON></a> is sworn in as the first <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Canada\" title=\"Prime Minister of Canada\">Prime Minister of Canada</a>. This date is commemorated annually in Canada as <a href=\"https://wikipedia.org/wiki/Canada_Day\" title=\"Canada Day\">Canada Day</a>, a national holiday.", "links": [{"title": "Constitution Act, 1867", "link": "https://wikipedia.org/wiki/Constitution_Act,_1867"}, {"title": "Province of Canada", "link": "https://wikipedia.org/wiki/Province_of_Canada"}, {"title": "New Brunswick", "link": "https://wikipedia.org/wiki/New_Brunswick"}, {"title": "Nova Scotia", "link": "https://wikipedia.org/wiki/Nova_Scotia"}, {"title": "Canadian Confederation", "link": "https://wikipedia.org/wiki/Canadian_Confederation"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>"}, {"title": "Prime Minister of Canada", "link": "https://wikipedia.org/wiki/Prime_Minister_of_Canada"}, {"title": "Canada Day", "link": "https://wikipedia.org/wiki/Canada_Day"}]}, {"year": "1870", "text": "The United States Department of Justice formally comes into existence.", "html": "1870 - The <a href=\"https://wikipedia.org/wiki/United_States_Department_of_Justice\" title=\"United States Department of Justice\">United States Department of Justice</a> formally comes into existence.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/United_States_Department_of_Justice\" title=\"United States Department of Justice\">United States Department of Justice</a> formally comes into existence.", "links": [{"title": "United States Department of Justice", "link": "https://wikipedia.org/wiki/United_States_Department_of_Justice"}]}, {"year": "1873", "text": "Prince Edward Island joins into Canadian Confederation.", "html": "1873 - <a href=\"https://wikipedia.org/wiki/Prince_Edward_Island\" title=\"Prince Edward Island\">Prince Edward Island</a> joins into <a href=\"https://wikipedia.org/wiki/Canadian_Confederation\" title=\"Canadian Confederation\">Canadian Confederation</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Prince_Edward_Island\" title=\"Prince Edward Island\">Prince Edward Island</a> joins into <a href=\"https://wikipedia.org/wiki/Canadian_Confederation\" title=\"Canadian Confederation\">Canadian Confederation</a>.", "links": [{"title": "Prince Edward Island", "link": "https://wikipedia.org/wiki/Prince_Edward_Island"}, {"title": "Canadian Confederation", "link": "https://wikipedia.org/wiki/Canadian_Confederation"}]}, {"year": "1874", "text": "The Sholes and Glidden typewriter, the first commercially successful typewriter, goes on sale.", "html": "1874 - The <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_and_<PERSON>lidden_typewriter\" title=\"Sholes and Glidden typewriter\">S<PERSON> and Glidden typewriter</a>, the first commercially successful <a href=\"https://wikipedia.org/wiki/Typewriter\" title=\"Typewriter\">typewriter</a>, goes on sale.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_and_<PERSON>lidden_typewriter\" title=\"Sholes and Glidden typewriter\"><PERSON><PERSON> and Glidden typewriter</a>, the first commercially successful <a href=\"https://wikipedia.org/wiki/Typewriter\" title=\"Typewriter\">typewriter</a>, goes on sale.", "links": [{"title": "Sholes and Glidden typewriter", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_and_<PERSON><PERSON><PERSON>_typewriter"}, {"title": "Typewriter", "link": "https://wikipedia.org/wiki/Typewriter"}]}, {"year": "1878", "text": "Canada joins the Universal Postal Union.", "html": "1878 - Canada joins the <a href=\"https://wikipedia.org/wiki/Universal_Postal_Union\" title=\"Universal Postal Union\">Universal Postal Union</a>.", "no_year_html": "Canada joins the <a href=\"https://wikipedia.org/wiki/Universal_Postal_Union\" title=\"Universal Postal Union\">Universal Postal Union</a>.", "links": [{"title": "Universal Postal Union", "link": "https://wikipedia.org/wiki/Universal_Postal_Union"}]}, {"year": "1879", "text": "<PERSON> publishes the first edition of the religious magazine The Watchtower.", "html": "1879 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> publishes the first edition of the religious magazine <i><a href=\"https://wikipedia.org/wiki/The_Watchtower\" title=\"The Watchtower\">The Watchtower</a></i>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> publishes the first edition of the religious magazine <i><a href=\"https://wikipedia.org/wiki/The_Watchtower\" title=\"The Watchtower\">The Watchtower</a></i>.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "The Watchtower", "link": "https://wikipedia.org/wiki/The_Watchtower"}]}, {"year": "1881", "text": "The world's first international telephone call is made between St. Stephen, New Brunswick, Canada, and Calais, Maine, United States.", "html": "1881 - The world's first international <a href=\"https://wikipedia.org/wiki/Telephone_call\" title=\"Telephone call\">telephone call</a> is made between <a href=\"https://wikipedia.org/wiki/<PERSON>._Stephen,_New_Brunswick\" title=\"St. Stephen, New Brunswick\">St. Stephen, New Brunswick</a>, Canada, and <a href=\"https://wikipedia.org/wiki/Calais,_Maine\" title=\"Calais, Maine\">Calais, Maine</a>, United States.", "no_year_html": "The world's first international <a href=\"https://wikipedia.org/wiki/Telephone_call\" title=\"Telephone call\">telephone call</a> is made between <a href=\"https://wikipedia.org/wiki/St._Stephen,_New_Brunswick\" title=\"St. Stephen, New Brunswick\">St. Stephen, New Brunswick</a>, Canada, and <a href=\"https://wikipedia.org/wiki/Calais,_Maine\" title=\"Calais, Maine\">Calais, Maine</a>, United States.", "links": [{"title": "Telephone call", "link": "https://wikipedia.org/wiki/Telephone_call"}, {"title": "St. Stephen, New Brunswick", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>,_New_Brunswick"}, {"title": "Calais, Maine", "link": "https://wikipedia.org/wiki/Calais,_Maine"}]}, {"year": "1881", "text": "General Order 70, the culmination of the Cardwell and Childers reforms of the British Army, comes into effect.", "html": "1881 - General Order 70, the culmination of the <a href=\"https://wikipedia.org/wiki/Cardwell_Reforms\" title=\"Cardwell Reforms\">Cardwell</a> and <a href=\"https://wikipedia.org/wiki/Childers_Reforms\" title=\"Childers Reforms\">Childers</a> reforms of the <a href=\"https://wikipedia.org/wiki/British_Army\" title=\"British Army\">British Army</a>, comes into effect.", "no_year_html": "General Order 70, the culmination of the <a href=\"https://wikipedia.org/wiki/Cardwell_Reforms\" title=\"Cardwell Reforms\">Cardwell</a> and <a href=\"https://wikipedia.org/wiki/Childers_Reforms\" title=\"Childers Reforms\">Childers</a> reforms of the <a href=\"https://wikipedia.org/wiki/British_Army\" title=\"British Army\">British Army</a>, comes into effect.", "links": [{"title": "Cardwell Reforms", "link": "https://wikipedia.org/wiki/Cardwell_Reforms"}, {"title": "Childers Reforms", "link": "https://wikipedia.org/wiki/Childers_Reforms"}, {"title": "British Army", "link": "https://wikipedia.org/wiki/British_Army"}]}, {"year": "1885", "text": "The United States terminates reciprocity and fishery agreement with Canada.", "html": "1885 - The United States terminates <a href=\"https://wikipedia.org/wiki/Reciprocity_(Canadian_politics)\" title=\"Reciprocity (Canadian politics)\">reciprocity</a> and <a href=\"https://wikipedia.org/wiki/Fishery\" title=\"Fishery\">fishery</a> agreement with Canada.", "no_year_html": "The United States terminates <a href=\"https://wikipedia.org/wiki/Reciprocity_(Canadian_politics)\" title=\"Reciprocity (Canadian politics)\">reciprocity</a> and <a href=\"https://wikipedia.org/wiki/Fishery\" title=\"Fishery\">fishery</a> agreement with Canada.", "links": [{"title": "Reciprocity (Canadian politics)", "link": "https://wikipedia.org/wiki/Reciprocity_(Canadian_politics)"}, {"title": "Fishery", "link": "https://wikipedia.org/wiki/Fishery"}]}, {"year": "1885", "text": "The Congo Free State is established by King <PERSON> of Belgium.", "html": "1885 - The <a href=\"https://wikipedia.org/wiki/Congo_Free_State\" title=\"Congo Free State\">Congo Free State</a> is established by King <a href=\"https://wikipedia.org/wiki/Leopold_II_of_Belgium\" title=\"Leopold II of Belgium\">Leopold II of Belgium</a>.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Congo_Free_State\" title=\"Congo Free State\">Congo Free State</a> is established by King <a href=\"https://wikipedia.org/wiki/Leopold_II_of_Belgium\" title=\"Leopold II of Belgium\">Leopold II of Belgium</a>.", "links": [{"title": "Congo Free State", "link": "https://wikipedia.org/wiki/Congo_Free_State"}, {"title": "Leopold II of Belgium", "link": "https://wikipedia.org/wiki/Leopold_II_of_Belgium"}]}, {"year": "1890", "text": "Canada and Bermuda are linked by telegraph cable.", "html": "1890 - Canada and <a href=\"https://wikipedia.org/wiki/Bermuda\" title=\"Bermuda\">Bermuda</a> are linked by <a href=\"https://wikipedia.org/wiki/Telegraphy\" title=\"Telegraphy\">telegraph</a> cable.", "no_year_html": "Canada and <a href=\"https://wikipedia.org/wiki/Bermuda\" title=\"Bermuda\">Bermuda</a> are linked by <a href=\"https://wikipedia.org/wiki/Telegraphy\" title=\"Telegraphy\">telegraph</a> cable.", "links": [{"title": "Bermuda", "link": "https://wikipedia.org/wiki/Bermuda"}, {"title": "Telegraphy", "link": "https://wikipedia.org/wiki/Telegraphy"}]}, {"year": "1898", "text": "Spanish-American War: The Battle of San Juan Hill is fought in Santiago de Cuba, Cuba.", "html": "1898 - <a href=\"https://wikipedia.org/wiki/Spanish%E2%80%93American_War\" title=\"Spanish-American War\">Spanish-American War</a>: The <a href=\"https://wikipedia.org/wiki/Battle_of_San_Juan_Hill\" title=\"Battle of San Juan Hill\">Battle of San Juan Hill</a> is fought in <a href=\"https://wikipedia.org/wiki/Santiago_de_Cuba\" title=\"Santiago de Cuba\">Santiago de Cuba</a>, <a href=\"https://wikipedia.org/wiki/Cuba\" title=\"Cuba\">Cuba</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Spanish%E2%80%93American_War\" title=\"Spanish-American War\">Spanish-American War</a>: The <a href=\"https://wikipedia.org/wiki/Battle_of_San_Juan_Hill\" title=\"Battle of San Juan Hill\">Battle of San Juan Hill</a> is fought in <a href=\"https://wikipedia.org/wiki/Santiago_de_Cuba\" title=\"Santiago de Cuba\">Santiago de Cuba</a>, <a href=\"https://wikipedia.org/wiki/Cuba\" title=\"Cuba\">Cuba</a>.", "links": [{"title": "Spanish-American War", "link": "https://wikipedia.org/wiki/Spanish%E2%80%93American_War"}, {"title": "Battle of San Juan Hill", "link": "https://wikipedia.org/wiki/Battle_of_San_Juan_Hill"}, {"title": "Santiago de Cuba", "link": "https://wikipedia.org/wiki/Santiago_de_Cuba"}, {"title": "Cuba", "link": "https://wikipedia.org/wiki/Cuba"}]}, {"year": "1901", "text": "French government enacts its anti-clerical legislation Law of Association prohibiting the formation of new monastic orders without governmental approval.", "html": "1901 - French government enacts its anti-clerical legislation <i>Law of Association</i> prohibiting the formation of new monastic orders without governmental approval.", "no_year_html": "French government enacts its anti-clerical legislation <i>Law of Association</i> prohibiting the formation of new monastic orders without governmental approval.", "links": []}, {"year": "1903", "text": "Start of first Tour de France bicycle race.", "html": "1903 - Start of <a href=\"https://wikipedia.org/wiki/1903_Tour_de_France\" title=\"1903 Tour de France\">first Tour de France</a> bicycle race.", "no_year_html": "Start of <a href=\"https://wikipedia.org/wiki/1903_Tour_de_France\" title=\"1903 Tour de France\">first Tour de France</a> bicycle race.", "links": [{"title": "1903 Tour de France", "link": "https://wikipedia.org/wiki/1903_Tour_de_France"}]}, {"year": "1908", "text": "SOS is adopted as the international distress signal.", "html": "1908 - <a href=\"https://wikipedia.org/wiki/SOS\" title=\"SOS\">SOS</a> is adopted as the international <a href=\"https://wikipedia.org/wiki/Distress_signal\" title=\"Distress signal\">distress signal</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/SOS\" title=\"SOS\">SOS</a> is adopted as the international <a href=\"https://wikipedia.org/wiki/Distress_signal\" title=\"Distress signal\">distress signal</a>.", "links": [{"title": "SOS", "link": "https://wikipedia.org/wiki/SOS"}, {"title": "Distress signal", "link": "https://wikipedia.org/wiki/Distress_signal"}]}, {"year": "1911", "text": "Germany dispatches the gunboat SMS Panther to Morocco, sparking the Agadir Crisis.", "html": "1911 - Germany dispatches the gunboat <a href=\"https://wikipedia.org/wiki/SMS_Panther_(1901)\" title=\"SMS Panther (1901)\">SMS <i>Panther</i></a> to Morocco, sparking the <a href=\"https://wikipedia.org/wiki/Agadir_Crisis\" title=\"Agadir Crisis\">Agadir Crisis</a>.", "no_year_html": "Germany dispatches the gunboat <a href=\"https://wikipedia.org/wiki/SMS_Panther_(1901)\" title=\"SMS Panther (1901)\">SMS <i>Panther</i></a> to Morocco, sparking the <a href=\"https://wikipedia.org/wiki/Agadir_Crisis\" title=\"Agadir Crisis\">Agadir Crisis</a>.", "links": [{"title": "SMS Panther (1901)", "link": "https://wikipedia.org/wiki/SMS_Panther_(1901)"}, {"title": "Agadir Crisis", "link": "https://wikipedia.org/wiki/Agadir_Crisis"}]}, {"year": "1915", "text": "Leutnant <PERSON> of the then-named German Deutsches Heer's Fliegertruppe army air service achieves the first known aerial victory with a synchronized machine-gun armed fighter plane, the Fokker M.5K/MG Eindecker.", "html": "1915 - <i><a href=\"https://wikipedia.org/wiki/Leutnant\" title=\"Leutnant\">Leutnant</a></i> <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> of the then-named German <a href=\"https://wikipedia.org/wiki/German_Army_(German_Empire)\" class=\"mw-redirect\" title=\"German Army (German Empire)\"><i><PERSON><PERSON> He<PERSON>'s</i></a> <a href=\"https://wikipedia.org/wiki/Luftstreitkr%C3%A4fte\" title=\"Luftstreitkräfte\"><i>Fliegertruppe</i></a> army air service achieves <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>#First_victory_using_a_synchronized_gun\" title=\"<PERSON>\">the first known aerial victory</a> with a <a href=\"https://wikipedia.org/wiki/Synchronization_gear\" title=\"Synchronization gear\">synchronized machine-gun</a> armed fighter plane, the <a href=\"https://wikipedia.org/wiki/Fokker_E.I\" title=\"Fokker E.I\">Fokker M.5K/MG</a> <i>Eindecker</i>.", "no_year_html": "<i><a href=\"https://wikipedia.org/wiki/Leutnant\" title=\"Leutnant\">Leutnant</a></i> <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> of the then-named German <a href=\"https://wikipedia.org/wiki/German_Army_(German_Empire)\" class=\"mw-redirect\" title=\"German Army (German Empire)\"><i>Deutsches Heer's</i></a> <a href=\"https://wikipedia.org/wiki/Luftstreitkr%C3%A4fte\" title=\"Luftstreitkräfte\"><i>Fliegertruppe</i></a> army air service achieves <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>#First_victory_using_a_synchronized_gun\" title=\"<PERSON>\">the first known aerial victory</a> with a <a href=\"https://wikipedia.org/wiki/Synchronization_gear\" title=\"Synchronization gear\">synchronized machine-gun</a> armed fighter plane, the <a href=\"https://wikipedia.org/wiki/Fokker_E.I\" title=\"Fokker E.I\">Fokker M.5K/MG</a> <i>Eindecker</i>.", "links": [{"title": "Leutnant", "link": "https://wikipedia.org/wiki/Leutnant"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "German Army (German Empire)", "link": "https://wikipedia.org/wiki/German_Army_(German_Empire)"}, {"title": "Luftstreitkräfte", "link": "https://wikipedia.org/wiki/Luftstreitkr%C3%A4fte"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>#First_victory_using_a_synchronized_gun"}, {"title": "Synchronization gear", "link": "https://wikipedia.org/wiki/Synchronization_gear"}, {"title": "Fokker E.I", "link": "https://wikipedia.org/wiki/Fokker_E.I"}]}, {"year": "1916", "text": "World War I: First day on the Somme: On the first day of the Battle of the Somme 19,000 soldiers of the British Army are killed and 40,000 wounded.", "html": "1916 - <a href=\"https://wikipedia.org/wiki/World_War_I\" title=\"World War I\">World War I</a>: <a href=\"https://wikipedia.org/wiki/First_day_on_the_Somme\" title=\"First day on the Somme\">First day on the Somme</a>: On the first day of the <a href=\"https://wikipedia.org/wiki/Battle_of_the_Somme\" title=\"Battle of the Somme\">Battle of the Somme</a> 19,000 soldiers of the <a href=\"https://wikipedia.org/wiki/British_Army\" title=\"British Army\">British Army</a> are killed and 40,000 wounded.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/World_War_I\" title=\"World War I\">World War I</a>: <a href=\"https://wikipedia.org/wiki/First_day_on_the_Somme\" title=\"First day on the Somme\">First day on the Somme</a>: On the first day of the <a href=\"https://wikipedia.org/wiki/Battle_of_the_Somme\" title=\"Battle of the Somme\">Battle of the Somme</a> 19,000 soldiers of the <a href=\"https://wikipedia.org/wiki/British_Army\" title=\"British Army\">British Army</a> are killed and 40,000 wounded.", "links": [{"title": "World War I", "link": "https://wikipedia.org/wiki/World_War_I"}, {"title": "First day on the Somme", "link": "https://wikipedia.org/wiki/First_day_on_the_Somme"}, {"title": "Battle of the Somme", "link": "https://wikipedia.org/wiki/Battle_of_the_Somme"}, {"title": "British Army", "link": "https://wikipedia.org/wiki/British_Army"}]}, {"year": "1917", "text": "World War I: Russia launches an offensive against Austria-Hungary to capture Galicia, its final offensive of the war.", "html": "1917 - <a href=\"https://wikipedia.org/wiki/World_War_I\" title=\"World War I\">World War I</a>: Russia launches <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_offensive\" title=\"<PERSON><PERSON>sky offensive\">an offensive</a> against <a href=\"https://wikipedia.org/wiki/Austria-Hungary\" title=\"Austria-Hungary\">Austria-Hungary</a> to capture <a href=\"https://wikipedia.org/wiki/Galicia_(Eastern_Europe)\" title=\"Galicia (Eastern Europe)\">Galicia</a>, its final offensive of the war.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/World_War_I\" title=\"World War I\">World War I</a>: Russia launches <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_offensive\" title=\"<PERSON><PERSON>sky offensive\">an offensive</a> against <a href=\"https://wikipedia.org/wiki/Austria-Hungary\" title=\"Austria-Hungary\">Austria-Hungary</a> to capture <a href=\"https://wikipedia.org/wiki/Galicia_(Eastern_Europe)\" title=\"Galicia (Eastern Europe)\">Galicia</a>, its final offensive of the war.", "links": [{"title": "World War I", "link": "https://wikipedia.org/wiki/World_War_I"}, {"title": "<PERSON>rensky offensive", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_offensive"}, {"title": "Austria-Hungary", "link": "https://wikipedia.org/wiki/Austria-Hungary"}, {"title": "Galicia (Eastern Europe)", "link": "https://wikipedia.org/wiki/Galicia_(Eastern_Europe)"}]}, {"year": "1917", "text": "Chinese General <PERSON> seizes control of Beijing and restores the monarchy, installing <PERSON><PERSON><PERSON>, last emperor of the Qing dynasty, to the throne. The restoration is reversed just shy of two weeks later, when Republican troops regain control of the capital.", "html": "1917 - Chinese General <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> seizes control of <a href=\"https://wikipedia.org/wiki/Beijing\" title=\"Beijing\">Beijing</a> and <a href=\"https://wikipedia.org/wiki/Manchu_Restoration\" title=\"Manchu Restoration\">restores the monarchy</a>, installing <a href=\"https://wikipedia.org/wiki/Puyi\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, last emperor of the <a href=\"https://wikipedia.org/wiki/Qing_dynasty\" title=\"Qing dynasty\">Qing dynasty</a>, to the throne. The restoration is reversed just shy of two weeks later, when <a href=\"https://wikipedia.org/wiki/Republic_of_China_(1912%E2%80%931949)\" title=\"Republic of China (1912-1949)\">Republican</a> troops regain control of the capital.", "no_year_html": "Chinese General <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> seizes control of <a href=\"https://wikipedia.org/wiki/Beijing\" title=\"Beijing\">Beijing</a> and <a href=\"https://wikipedia.org/wiki/Manchu_Restoration\" title=\"Manchu Restoration\">restores the monarchy</a>, installing <a href=\"https://wikipedia.org/wiki/Puyi\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, last emperor of the <a href=\"https://wikipedia.org/wiki/Qing_dynasty\" title=\"Qing dynasty\">Qing dynasty</a>, to the throne. The restoration is reversed just shy of two weeks later, when <a href=\"https://wikipedia.org/wiki/Republic_of_China_(1912%E2%80%931949)\" title=\"Republic of China (1912-1949)\">Republican</a> troops regain control of the capital.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Beijing", "link": "https://wikipedia.org/wiki/Beijing"}, {"title": "Manchu Restoration", "link": "https://wikipedia.org/wiki/Manchu_Restoration"}, {"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Puyi"}, {"title": "Qing dynasty", "link": "https://wikipedia.org/wiki/Qing_dynasty"}, {"title": "Republic of China (1912-1949)", "link": "https://wikipedia.org/wiki/Republic_of_China_(1912%E2%80%931949)"}]}, {"year": "1921", "text": "The Chinese Communist Party is founded by <PERSON> and <PERSON>, with the help of the Far Eastern Bureau of the Russian Social Democratic Labour Party (Bolsheviks), who seized power in Russia after the 1917 October Revolution, and the Far Eastern Secretariat of the Communist International.", "html": "1921 - The <a href=\"https://wikipedia.org/wiki/Chinese_Communist_Party\" title=\"Chinese Communist Party\">Chinese Communist Party</a> is founded by <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> and <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, with the help of the Far Eastern Bureau of the <a href=\"https://wikipedia.org/wiki/Communist_Party_of_the_Soviet_Union#Name\" title=\"Communist Party of the Soviet Union\">Russian Social Democratic Labour Party (Bolsheviks)</a>, who seized power in Russia after the 1917 October Revolution, and the Far Eastern Secretariat of the Communist International.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Chinese_Communist_Party\" title=\"Chinese Communist Party\">Chinese Communist Party</a> is founded by <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> and <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, with the help of the Far Eastern Bureau of the <a href=\"https://wikipedia.org/wiki/Communist_Party_of_the_Soviet_Union#Name\" title=\"Communist Party of the Soviet Union\">Russian Social Democratic Labour Party (Bolsheviks)</a>, who seized power in Russia after the 1917 October Revolution, and the Far Eastern Secretariat of the Communist International.", "links": [{"title": "Chinese Communist Party", "link": "https://wikipedia.org/wiki/Chinese_Communist_Party"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Li Dazhao", "link": "https://wikipedia.org/wiki/Li_<PERSON>hao"}, {"title": "Communist Party of the Soviet Union", "link": "https://wikipedia.org/wiki/Communist_Party_of_the_Soviet_Union#Name"}]}, {"year": "1922", "text": "The Great Railroad Strike of 1922 begins in the United States.", "html": "1922 - The <a href=\"https://wikipedia.org/wiki/Great_Railroad_Strike_of_1922\" title=\"Great Railroad Strike of 1922\">Great Railroad Strike of 1922</a> begins in the United States.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Great_Railroad_Strike_of_1922\" title=\"Great Railroad Strike of 1922\">Great Railroad Strike of 1922</a> begins in the United States.", "links": [{"title": "Great Railroad Strike of 1922", "link": "https://wikipedia.org/wiki/Great_Railroad_Strike_of_1922"}]}, {"year": "1923", "text": "The Parliament of Canada suspends all Chinese immigration.", "html": "1923 - The <a href=\"https://wikipedia.org/wiki/Parliament_of_Canada\" title=\"Parliament of Canada\">Parliament of Canada</a> suspends all <a href=\"https://wikipedia.org/wiki/History_of_Chinese_immigration_to_Canada\" title=\"History of Chinese immigration to Canada\">Chinese immigration</a>.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Parliament_of_Canada\" title=\"Parliament of Canada\">Parliament of Canada</a> suspends all <a href=\"https://wikipedia.org/wiki/History_of_Chinese_immigration_to_Canada\" title=\"History of Chinese immigration to Canada\">Chinese immigration</a>.", "links": [{"title": "Parliament of Canada", "link": "https://wikipedia.org/wiki/Parliament_of_Canada"}, {"title": "History of Chinese immigration to Canada", "link": "https://wikipedia.org/wiki/History_of_Chinese_immigration_to_Canada"}]}, {"year": "1924", "text": "The National War Memorial for the Dominion of Newfoundland was inaugurated by <PERSON>, 1st <PERSON> in St. John's, Newfoundland. The date commemorates the first day of the Battle of the Somme, where at Beaumont-Hamel, 86 percent of the Royal Newfoundland Regiment was wiped out.", "html": "1924 - The <a href=\"https://wikipedia.org/wiki/National_War_Memorial_(Newfoundland)\" title=\"National War Memorial (Newfoundland)\">National War Memorial</a> for the <a href=\"https://wikipedia.org/wiki/Dominion_of_Newfoundland\" title=\"Dominion of Newfoundland\">Dominion of Newfoundland</a> was inaugurated by <a href=\"https://wikipedia.org/wiki/Field_Marshall\" title=\"Field Marshall\"><PERSON></a> <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_1st_<PERSON>_<PERSON>\" title=\"<PERSON>, 1st <PERSON>\"><PERSON>, 1st <PERSON></a> in <a href=\"https://wikipedia.org/wiki/St._<PERSON>%27s,_Newfoundland\" class=\"mw-redirect\" title=\"St. John's, Newfoundland\">St. John's, Newfoundland</a>. The date commemorates the first day of the <a href=\"https://wikipedia.org/wiki/Battle_of_the_Somme\" title=\"Battle of the Somme\">Battle of the Somme</a>, where at <a href=\"https://wikipedia.org/wiki/<PERSON>-<PERSON>_<PERSON>_<PERSON>\" title=\"Beaumont-Hamel <PERSON> Memorial\"><PERSON>-<PERSON></a>, 86 percent of the <a href=\"https://wikipedia.org/wiki/Royal_Newfoundland_Regiment\" title=\"Royal Newfoundland Regiment\">Royal Newfoundland Regiment</a> was wiped out.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/National_War_Memorial_(Newfoundland)\" title=\"National War Memorial (Newfoundland)\">National War Memorial</a> for the <a href=\"https://wikipedia.org/wiki/Dominion_of_Newfoundland\" title=\"Dominion of Newfoundland\">Dominion of Newfoundland</a> was inaugurated by <a href=\"https://wikipedia.org/wiki/Field_Marshall\" title=\"Field Marshall\"><PERSON></a> <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_1st_<PERSON>\" title=\"<PERSON>, 1st Earl <PERSON>\"><PERSON>, 1st <PERSON></a> in <a href=\"https://wikipedia.org/wiki/St._<PERSON>%27s,_Newfoundland\" class=\"mw-redirect\" title=\"St. John's, Newfoundland\">St. John's, Newfoundland</a>. The date commemorates the first day of the <a href=\"https://wikipedia.org/wiki/Battle_of_the_Somme\" title=\"Battle of the Somme\">Battle of the Somme</a>, where at <a href=\"https://wikipedia.org/wiki/<PERSON>-<PERSON>_<PERSON>_<PERSON>\" title=\"Beaumont-Hamel <PERSON> Memorial\"><PERSON>-<PERSON></a>, 86 percent of the <a href=\"https://wikipedia.org/wiki/Royal_Newfoundland_Regiment\" title=\"Royal Newfoundland Regiment\">Royal Newfoundland Regiment</a> was wiped out.", "links": [{"title": "National War Memorial (Newfoundland)", "link": "https://wikipedia.org/wiki/National_War_Memorial_(Newfoundland)"}, {"title": "Dominion of Newfoundland", "link": "https://wikipedia.org/wiki/Dominion_of_Newfoundland"}, {"title": "<PERSON> Marshall", "link": "https://wikipedia.org/wiki/<PERSON>_Marshall"}, {"title": "<PERSON>, 1st Earl <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_1st_Earl_<PERSON>"}, {"title": "St. John's, Newfoundland", "link": "https://wikipedia.org/wiki/<PERSON>._<PERSON>%27s,_Newfoundland"}, {"title": "Battle of the Somme", "link": "https://wikipedia.org/wiki/Battle_of_the_Somme"}, {"title": "Beaumont-<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>-Hamel_Newfoundland_Memorial"}, {"title": "Royal Newfoundland Regiment", "link": "https://wikipedia.org/wiki/Royal_Newfoundland_Regiment"}]}, {"year": "1931", "text": "United Airlines begins service (as Boeing Air Transport).", "html": "1931 - <a href=\"https://wikipedia.org/wiki/United_Airlines\" title=\"United Airlines\">United Airlines</a> begins service (as Boeing Air Transport).", "no_year_html": "<a href=\"https://wikipedia.org/wiki/United_Airlines\" title=\"United Airlines\">United Airlines</a> begins service (as Boeing Air Transport).", "links": [{"title": "United Airlines", "link": "https://wikipedia.org/wiki/United_Airlines"}]}, {"year": "1931", "text": "<PERSON> and <PERSON> become the first people to circumnavigate the globe in a single-engined monoplane aircraft.", "html": "1931 - <a href=\"https://wikipedia.org/wiki/Wiley_Post\" title=\"Wiley Post\">Wiley Post</a> and <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> become the first people to circumnavigate the globe in a <a href=\"https://wikipedia.org/wiki/Lockheed_Vega\" title=\"Lockheed Vega\">single-engined monoplane</a> aircraft.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Wiley_Post\" title=\"Wiley Post\">Wiley Post</a> and <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> become the first people to circumnavigate the globe in a <a href=\"https://wikipedia.org/wiki/Lockheed_Vega\" title=\"Lockheed Vega\">single-engined monoplane</a> aircraft.", "links": [{"title": "Wiley Post", "link": "https://wikipedia.org/wiki/Wiley_Post"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Lockheed Vega", "link": "https://wikipedia.org/wiki/Lockheed_Vega"}]}, {"year": "1932", "text": "Australia's national broadcaster, the Australian Broadcasting Corporation, was formed.", "html": "1932 - Australia's national broadcaster, the <a href=\"https://wikipedia.org/wiki/Australian_Broadcasting_Corporation\" title=\"Australian Broadcasting Corporation\">Australian Broadcasting Corporation</a>, was formed.", "no_year_html": "Australia's national broadcaster, the <a href=\"https://wikipedia.org/wiki/Australian_Broadcasting_Corporation\" title=\"Australian Broadcasting Corporation\">Australian Broadcasting Corporation</a>, was formed.", "links": [{"title": "Australian Broadcasting Corporation", "link": "https://wikipedia.org/wiki/Australian_Broadcasting_Corporation"}]}, {"year": "1935", "text": "Regina, Saskatchewan, police and Royal Canadian Mounted Police ambush strikers participating in the On-to-Ottawa Trek.", "html": "1935 - <a href=\"https://wikipedia.org/wiki/Regina,_Saskatchewan\" title=\"Regina, Saskatchewan\">Regina, Saskatchewan</a>, police and <a href=\"https://wikipedia.org/wiki/Royal_Canadian_Mounted_Police\" title=\"Royal Canadian Mounted Police\">Royal Canadian Mounted Police</a> ambush strikers participating in the <a href=\"https://wikipedia.org/wiki/On-to-Ottawa_Trek\" title=\"On-to-Ottawa Trek\">On-to-Ottawa Trek</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Regina,_Saskatchewan\" title=\"Regina, Saskatchewan\">Regina, Saskatchewan</a>, police and <a href=\"https://wikipedia.org/wiki/Royal_Canadian_Mounted_Police\" title=\"Royal Canadian Mounted Police\">Royal Canadian Mounted Police</a> ambush strikers participating in the <a href=\"https://wikipedia.org/wiki/On-to-Ottawa_Trek\" title=\"On-to-Ottawa Trek\">On-to-Ottawa Trek</a>.", "links": [{"title": "Regina, Saskatchewan", "link": "https://wikipedia.org/wiki/Regina,_Saskatchewan"}, {"title": "Royal Canadian Mounted Police", "link": "https://wikipedia.org/wiki/Royal_Canadian_Mounted_Police"}, {"title": "On-to-Ottawa Trek", "link": "https://wikipedia.org/wiki/On-to-Ottawa_Trek"}]}, {"year": "1942", "text": "World War II: First Battle of El Alamein.", "html": "1942 - <a href=\"https://wikipedia.org/wiki/World_War_II\" title=\"World War II\">World War II</a>: <a href=\"https://wikipedia.org/wiki/First_Battle_of_El_Alamein\" title=\"First Battle of El Alamein\">First Battle of El Alamein</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/World_War_II\" title=\"World War II\">World War II</a>: <a href=\"https://wikipedia.org/wiki/First_Battle_of_El_Alamein\" title=\"First Battle of El Alamein\">First Battle of El Alamein</a>.", "links": [{"title": "World War II", "link": "https://wikipedia.org/wiki/World_War_II"}, {"title": "First Battle of El Alamein", "link": "https://wikipedia.org/wiki/First_Battle_of_El_Alamein"}]}, {"year": "1942", "text": "The Australian Federal Government becomes the sole collector of income tax in Australia as State Income Tax is abolished.", "html": "1942 - The <a href=\"https://wikipedia.org/wiki/Australian_Federal_Government\" class=\"mw-redirect\" title=\"Australian Federal Government\">Australian Federal Government</a> becomes the sole collector of <a href=\"https://wikipedia.org/wiki/Income_tax_in_Australia\" title=\"Income tax in Australia\">income tax in Australia</a> as State Income Tax is abolished.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Australian_Federal_Government\" class=\"mw-redirect\" title=\"Australian Federal Government\">Australian Federal Government</a> becomes the sole collector of <a href=\"https://wikipedia.org/wiki/Income_tax_in_Australia\" title=\"Income tax in Australia\">income tax in Australia</a> as State Income Tax is abolished.", "links": [{"title": "Australian Federal Government", "link": "https://wikipedia.org/wiki/Australian_Federal_Government"}, {"title": "Income tax in Australia", "link": "https://wikipedia.org/wiki/Income_tax_in_Australia"}]}, {"year": "1943", "text": "The City of Tokyo and the Prefecture of Tokyo are both replaced by the Tokyo Metropolis.", "html": "1943 - The <a href=\"https://wikipedia.org/wiki/Tokyo_City\" title=\"Tokyo City\">City of Tokyo</a> and the <a href=\"https://wikipedia.org/wiki/Tokyo_Prefecture\" class=\"mw-redirect\" title=\"Tokyo Prefecture\">Prefecture of Tokyo</a> are both replaced by the Tokyo Metropolis.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Tokyo_City\" title=\"Tokyo City\">City of Tokyo</a> and the <a href=\"https://wikipedia.org/wiki/Tokyo_Prefecture\" class=\"mw-redirect\" title=\"Tokyo Prefecture\">Prefecture of Tokyo</a> are both replaced by the Tokyo Metropolis.", "links": [{"title": "Tokyo City", "link": "https://wikipedia.org/wiki/Tokyo_City"}, {"title": "Tokyo Prefecture", "link": "https://wikipedia.org/wiki/Tokyo_Prefecture"}]}, {"year": "1946", "text": "Crossroads Able is the first postwar nuclear weapon test.", "html": "1946 - <a href=\"https://wikipedia.org/wiki/Operation_Crossroads#Test_Able\" title=\"Operation Crossroads\">Crossroads Able</a> is the first postwar nuclear weapon test.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Operation_Crossroads#Test_Able\" title=\"Operation Crossroads\">Crossroads Able</a> is the first postwar nuclear weapon test.", "links": [{"title": "Operation Crossroads", "link": "https://wikipedia.org/wiki/Operation_Crossroads#Test_Able"}]}, {"year": "1947", "text": "The Philippine Air Force is established.", "html": "1947 - The <a href=\"https://wikipedia.org/wiki/Philippine_Air_Force\" title=\"Philippine Air Force\">Philippine Air Force</a> is established.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Philippine_Air_Force\" title=\"Philippine Air Force\">Philippine Air Force</a> is established.", "links": [{"title": "Philippine Air Force", "link": "https://wikipedia.org/wiki/Philippine_Air_Force"}]}, {"year": "1948", "text": "<PERSON> (Quaid-i-A<PERSON>) inaugurates Pakistan's central bank, the State Bank of Pakistan.", "html": "1948 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> (Quaid-i-Azam) inaugurates Pakistan's <a href=\"https://wikipedia.org/wiki/Central_bank\" title=\"Central bank\">central bank</a>, the <a href=\"https://wikipedia.org/wiki/State_Bank_of_Pakistan\" title=\"State Bank of Pakistan\">State Bank of Pakistan</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> (Quaid-i-Azam) inaugurates Pakistan's <a href=\"https://wikipedia.org/wiki/Central_bank\" title=\"Central bank\">central bank</a>, the <a href=\"https://wikipedia.org/wiki/State_Bank_of_Pakistan\" title=\"State Bank of Pakistan\">State Bank of Pakistan</a>.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "Central bank", "link": "https://wikipedia.org/wiki/Central_bank"}, {"title": "State Bank of Pakistan", "link": "https://wikipedia.org/wiki/State_Bank_of_Pakistan"}]}, {"year": "1949", "text": "The merger of two princely states of India, Cochin and Travancore, into the state of Thiru-Kochi (later re-organized as Kerala) in the Indian Union ends more than 1,000 years of princely rule by the Cochin royal family.", "html": "1949 - The merger of two princely states of India, <a href=\"https://wikipedia.org/wiki/Cochin\" class=\"mw-redirect\" title=\"Cochin\">Cochin</a> and <a href=\"https://wikipedia.org/wiki/Travancore\" title=\"Travancore\">Travancore</a>, into the state of <a href=\"https://wikipedia.org/wiki/Thiru-Kochi\" class=\"mw-redirect\" title=\"Thiru-Kochi\">Thiru-Kochi</a> (later re-organized as <a href=\"https://wikipedia.org/wiki/Kerala\" title=\"Kerala\">Kerala</a>) in the Indian Union ends more than 1,000 years of princely rule by the <a href=\"https://wikipedia.org/wiki/Cochin_royal_family\" class=\"mw-redirect\" title=\"Cochin royal family\">Cochin royal family</a>.", "no_year_html": "The merger of two princely states of India, <a href=\"https://wikipedia.org/wiki/Cochin\" class=\"mw-redirect\" title=\"Cochin\">Cochin</a> and <a href=\"https://wikipedia.org/wiki/Travancore\" title=\"Travancore\">Travancore</a>, into the state of <a href=\"https://wikipedia.org/wiki/Thiru-Kochi\" class=\"mw-redirect\" title=\"Thiru-Kochi\">Thiru-Kochi</a> (later re-organized as <a href=\"https://wikipedia.org/wiki/Kerala\" title=\"Kerala\">Kerala</a>) in the Indian Union ends more than 1,000 years of princely rule by the <a href=\"https://wikipedia.org/wiki/Cochin_royal_family\" class=\"mw-redirect\" title=\"Cochin royal family\">Cochin royal family</a>.", "links": [{"title": "Cochin", "link": "https://wikipedia.org/wiki/Cochin"}, {"title": "Travancore", "link": "https://wikipedia.org/wiki/Travancore"}, {"title": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Thiru-Kochi"}, {"title": "Kerala", "link": "https://wikipedia.org/wiki/Kerala"}, {"title": "Cochin royal family", "link": "https://wikipedia.org/wiki/Cochin_royal_family"}]}, {"year": "1957", "text": "The International Geophysical Year begins.", "html": "1957 - The <a href=\"https://wikipedia.org/wiki/International_Geophysical_Year\" title=\"International Geophysical Year\">International Geophysical Year</a> begins.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/International_Geophysical_Year\" title=\"International Geophysical Year\">International Geophysical Year</a> begins.", "links": [{"title": "International Geophysical Year", "link": "https://wikipedia.org/wiki/International_Geophysical_Year"}]}, {"year": "1958", "text": "The Canadian Broadcasting Corporation links television broadcasting across Canada via microwave.", "html": "1958 - The <a href=\"https://wikipedia.org/wiki/Canadian_Broadcasting_Corporation\" title=\"Canadian Broadcasting Corporation\">Canadian Broadcasting Corporation</a> links <a href=\"https://wikipedia.org/wiki/Television\" title=\"Television\">television</a> broadcasting across Canada via <a href=\"https://wikipedia.org/wiki/Microwave\" title=\"Microwave\">microwave</a>.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Canadian_Broadcasting_Corporation\" title=\"Canadian Broadcasting Corporation\">Canadian Broadcasting Corporation</a> links <a href=\"https://wikipedia.org/wiki/Television\" title=\"Television\">television</a> broadcasting across Canada via <a href=\"https://wikipedia.org/wiki/Microwave\" title=\"Microwave\">microwave</a>.", "links": [{"title": "Canadian Broadcasting Corporation", "link": "https://wikipedia.org/wiki/Canadian_Broadcasting_Corporation"}, {"title": "Television", "link": "https://wikipedia.org/wiki/Television"}, {"title": "Microwave", "link": "https://wikipedia.org/wiki/Microwave"}]}, {"year": "1958", "text": "Flooding of Canada's Saint Lawrence Seaway begins.", "html": "1958 - Flooding of Canada's <a href=\"https://wikipedia.org/wiki/Saint_Lawrence_Seaway\" class=\"mw-redirect\" title=\"Saint Lawrence Seaway\">Saint Lawrence Seaway</a> begins.", "no_year_html": "Flooding of Canada's <a href=\"https://wikipedia.org/wiki/Saint_Lawrence_Seaway\" class=\"mw-redirect\" title=\"Saint Lawrence Seaway\">Saint Lawrence Seaway</a> begins.", "links": [{"title": "Saint Lawrence Seaway", "link": "https://wikipedia.org/wiki/Saint_Lawrence_Seaway"}]}, {"year": "1959", "text": "Specific values for the international yard, avoirdupois pound and derived units (e.g. inch, mile and ounce) are adopted after agreement between the US, the United Kingdom and other Commonwealth countries.", "html": "1959 - Specific values for the international <a href=\"https://wikipedia.org/wiki/Yard\" title=\"Yard\">yard</a>, <a href=\"https://wikipedia.org/wiki/Avoirdupois_pound\" class=\"mw-redirect\" title=\"Avoirdupois pound\">avoirdupois pound</a> and derived units (e.g. inch, mile and <a href=\"https://wikipedia.org/wiki/Ounce\" title=\"Ounce\">ounce</a>) are adopted after <a href=\"https://wikipedia.org/wiki/International_yard_and_pound\" title=\"International yard and pound\">agreement</a> between the US, the United Kingdom and other <a href=\"https://wikipedia.org/wiki/Commonwealth_of_Nations\" title=\"Commonwealth of Nations\">Commonwealth</a> countries.", "no_year_html": "Specific values for the international <a href=\"https://wikipedia.org/wiki/Yard\" title=\"Yard\">yard</a>, <a href=\"https://wikipedia.org/wiki/Avoirdupois_pound\" class=\"mw-redirect\" title=\"Avoirdupois pound\">avoirdupois pound</a> and derived units (e.g. inch, mile and <a href=\"https://wikipedia.org/wiki/Ounce\" title=\"Ounce\">ounce</a>) are adopted after <a href=\"https://wikipedia.org/wiki/International_yard_and_pound\" title=\"International yard and pound\">agreement</a> between the US, the United Kingdom and other <a href=\"https://wikipedia.org/wiki/Commonwealth_of_Nations\" title=\"Commonwealth of Nations\">Commonwealth</a> countries.", "links": [{"title": "Yard", "link": "https://wikipedia.org/wiki/Yard"}, {"title": "Avoirdupois pound", "link": "https://wikipedia.org/wiki/Avoirdupois_pound"}, {"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Ounce"}, {"title": "International yard and pound", "link": "https://wikipedia.org/wiki/International_yard_and_pound"}, {"title": "Commonwealth of Nations", "link": "https://wikipedia.org/wiki/Commonwealth_of_Nations"}]}, {"year": "1960", "text": "The Trust Territory of Somaliland (the former Italian Somaliland) gains its independence from Italy. Concurrently, it unites as scheduled with the five-day-old State of Somaliland (the former British Somaliland) to form the Somali Republic.", "html": "1960 - The <a href=\"https://wikipedia.org/wiki/Trust_Territory_of_Somaliland\" title=\"Trust Territory of Somaliland\">Trust Territory of Somaliland</a> (the former <a href=\"https://wikipedia.org/wiki/Italian_Somaliland\" title=\"Italian Somaliland\">Italian Somaliland</a>) gains its independence from Italy. Concurrently, it unites as scheduled with the five-day-old <a href=\"https://wikipedia.org/wiki/State_of_Somaliland\" title=\"State of Somaliland\">State of Somaliland</a> (the former <a href=\"https://wikipedia.org/wiki/British_Somaliland\" title=\"British Somaliland\">British Somaliland</a>) to form the <a href=\"https://wikipedia.org/wiki/Somali_Republic\" title=\"Somali Republic\">Somali Republic</a>.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Trust_Territory_of_Somaliland\" title=\"Trust Territory of Somaliland\">Trust Territory of Somaliland</a> (the former <a href=\"https://wikipedia.org/wiki/Italian_Somaliland\" title=\"Italian Somaliland\">Italian Somaliland</a>) gains its independence from Italy. Concurrently, it unites as scheduled with the five-day-old <a href=\"https://wikipedia.org/wiki/State_of_Somaliland\" title=\"State of Somaliland\">State of Somaliland</a> (the former <a href=\"https://wikipedia.org/wiki/British_Somaliland\" title=\"British Somaliland\">British Somaliland</a>) to form the <a href=\"https://wikipedia.org/wiki/Somali_Republic\" title=\"Somali Republic\">Somali Republic</a>.", "links": [{"title": "Trust Territory of Somaliland", "link": "https://wikipedia.org/wiki/Trust_Territory_of_Somaliland"}, {"title": "Italian Somaliland", "link": "https://wikipedia.org/wiki/Italian_Somaliland"}, {"title": "State of Somaliland", "link": "https://wikipedia.org/wiki/State_of_Somaliland"}, {"title": "British Somaliland", "link": "https://wikipedia.org/wiki/British_Somaliland"}, {"title": "Somali Republic", "link": "https://wikipedia.org/wiki/Somali_Republic"}]}, {"year": "1960", "text": "Ghana becomes a republic and <PERSON><PERSON><PERSON> becomes its first President as Queen <PERSON> ceases to be its head of state.", "html": "1960 - <a href=\"https://wikipedia.org/wiki/Ghana\" title=\"Ghana\">Ghana</a> becomes a <a href=\"https://wikipedia.org/wiki/Republic\" title=\"Republic\">republic</a> and <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a> becomes its first <a href=\"https://wikipedia.org/wiki/President_of_Ghana\" title=\"President of Ghana\">President</a> as Queen <a href=\"https://wikipedia.org/wiki/Elizabeth_II\" title=\"Elizabeth II\"><PERSON> II</a> ceases to be its <a href=\"https://wikipedia.org/wiki/Head_of_state\" title=\"Head of state\">head of state</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Ghana\" title=\"Ghana\">Ghana</a> becomes a <a href=\"https://wikipedia.org/wiki/Republic\" title=\"Republic\">republic</a> and <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a> becomes its first <a href=\"https://wikipedia.org/wiki/President_of_Ghana\" title=\"President of Ghana\">President</a> as Queen <a href=\"https://wikipedia.org/wiki/Elizabeth_II\" title=\"Elizabeth II\"><PERSON> II</a> ceases to be its <a href=\"https://wikipedia.org/wiki/Head_of_state\" title=\"Head of state\">head of state</a>.", "links": [{"title": "Ghana", "link": "https://wikipedia.org/wiki/Ghana"}, {"title": "Republic", "link": "https://wikipedia.org/wiki/Republic"}, {"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>wa<PERSON>_<PERSON>ah"}, {"title": "President of Ghana", "link": "https://wikipedia.org/wiki/President_of_Ghana"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_II"}, {"title": "Head of state", "link": "https://wikipedia.org/wiki/Head_of_state"}]}, {"year": "1962", "text": "Independence of Rwanda and Burundi.", "html": "1962 - Independence of <a href=\"https://wikipedia.org/wiki/Rwanda\" title=\"Rwanda\">Rwanda</a> and <a href=\"https://wikipedia.org/wiki/Burundi\" title=\"Burundi\">Burundi</a>.", "no_year_html": "Independence of <a href=\"https://wikipedia.org/wiki/Rwanda\" title=\"Rwanda\">Rwanda</a> and <a href=\"https://wikipedia.org/wiki/Burundi\" title=\"Burundi\">Burundi</a>.", "links": [{"title": "Rwanda", "link": "https://wikipedia.org/wiki/Rwanda"}, {"title": "Burundi", "link": "https://wikipedia.org/wiki/Burundi"}]}, {"year": "1963", "text": "ZIP codes are introduced for United States mail.", "html": "1963 - <a href=\"https://wikipedia.org/wiki/ZIP_code\" class=\"mw-redirect\" title=\"ZIP code\">ZIP codes</a> are introduced for <a href=\"https://wikipedia.org/wiki/United_States_Postal_Service\" title=\"United States Postal Service\">United States mail</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/ZIP_code\" class=\"mw-redirect\" title=\"ZIP code\">ZIP codes</a> are introduced for <a href=\"https://wikipedia.org/wiki/United_States_Postal_Service\" title=\"United States Postal Service\">United States mail</a>.", "links": [{"title": "ZIP code", "link": "https://wikipedia.org/wiki/ZIP_code"}, {"title": "United States Postal Service", "link": "https://wikipedia.org/wiki/United_States_Postal_Service"}]}, {"year": "1963", "text": "The British Government admits that former diplomat <PERSON> had worked as a Soviet agent.", "html": "1963 - The British Government admits that former diplomat <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> had worked as a <a href=\"https://wikipedia.org/wiki/Soviet_Union\" title=\"Soviet Union\">Soviet</a> <a href=\"https://wikipedia.org/wiki/Espionage\" title=\"Espionage\">agent</a>.", "no_year_html": "The British Government admits that former diplomat <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> had worked as a <a href=\"https://wikipedia.org/wiki/Soviet_Union\" title=\"Soviet Union\">Soviet</a> <a href=\"https://wikipedia.org/wiki/Espionage\" title=\"Espionage\">agent</a>.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Soviet Union", "link": "https://wikipedia.org/wiki/Soviet_Union"}, {"title": "Espionage", "link": "https://wikipedia.org/wiki/Espionage"}]}, {"year": "1966", "text": "The first color television transmission in Canada takes place from Toronto.", "html": "1966 - The first <a href=\"https://wikipedia.org/wiki/Color_television\" title=\"Color television\">color television</a> transmission in Canada takes place from Toronto.", "no_year_html": "The first <a href=\"https://wikipedia.org/wiki/Color_television\" title=\"Color television\">color television</a> transmission in Canada takes place from Toronto.", "links": [{"title": "Color television", "link": "https://wikipedia.org/wiki/Color_television"}]}, {"year": "1967", "text": "Merger Treaty: The European Community is formally created out of a merger between the Common Market, the European Coal and Steel Community, and the European Atomic Energy Commission.", "html": "1967 - <a href=\"https://wikipedia.org/wiki/Merger_Treaty\" title=\"Merger Treaty\">Merger Treaty</a>: The <a href=\"https://wikipedia.org/wiki/European_Community\" class=\"mw-redirect\" title=\"European Community\">European Community</a> is formally created out of a merger between the <a href=\"https://wikipedia.org/wiki/Common_Market\" class=\"mw-redirect\" title=\"Common Market\">Common Market</a>, the <a href=\"https://wikipedia.org/wiki/European_Coal_and_Steel_Community\" title=\"European Coal and Steel Community\">European Coal and Steel Community</a>, and the <a href=\"https://wikipedia.org/wiki/Euratom\" title=\"Euratom\">European Atomic Energy Commission</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Merger_Treaty\" title=\"Merger Treaty\">Merger Treaty</a>: The <a href=\"https://wikipedia.org/wiki/European_Community\" class=\"mw-redirect\" title=\"European Community\">European Community</a> is formally created out of a merger between the <a href=\"https://wikipedia.org/wiki/Common_Market\" class=\"mw-redirect\" title=\"Common Market\">Common Market</a>, the <a href=\"https://wikipedia.org/wiki/European_Coal_and_Steel_Community\" title=\"European Coal and Steel Community\">European Coal and Steel Community</a>, and the <a href=\"https://wikipedia.org/wiki/Euratom\" title=\"Euratom\">European Atomic Energy Commission</a>.", "links": [{"title": "Merger Treaty", "link": "https://wikipedia.org/wiki/Merger_Treaty"}, {"title": "European Community", "link": "https://wikipedia.org/wiki/European_Community"}, {"title": "Common Market", "link": "https://wikipedia.org/wiki/Common_Market"}, {"title": "European Coal and Steel Community", "link": "https://wikipedia.org/wiki/European_Coal_and_Steel_Community"}, {"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Euratom"}]}, {"year": "1968", "text": "The United States Central Intelligence Agency's Phoenix Program is officially established.", "html": "1968 - The United States <a href=\"https://wikipedia.org/wiki/Central_Intelligence_Agency\" title=\"Central Intelligence Agency\">Central Intelligence Agency</a>'s <a href=\"https://wikipedia.org/wiki/Phoenix_Program\" title=\"Phoenix Program\">Phoenix Program</a> is officially established.", "no_year_html": "The United States <a href=\"https://wikipedia.org/wiki/Central_Intelligence_Agency\" title=\"Central Intelligence Agency\">Central Intelligence Agency</a>'s <a href=\"https://wikipedia.org/wiki/Phoenix_Program\" title=\"Phoenix Program\">Phoenix Program</a> is officially established.", "links": [{"title": "Central Intelligence Agency", "link": "https://wikipedia.org/wiki/Central_Intelligence_Agency"}, {"title": "Phoenix Program", "link": "https://wikipedia.org/wiki/Phoenix_Program"}]}, {"year": "1968", "text": "The Treaty on the Non-Proliferation of Nuclear Weapons is signed in Washington, D.C., London and Moscow by sixty-two countries.", "html": "1968 - The <a href=\"https://wikipedia.org/wiki/Treaty_on_the_Non-Proliferation_of_Nuclear_Weapons\" title=\"Treaty on the Non-Proliferation of Nuclear Weapons\">Treaty on the Non-Proliferation of Nuclear Weapons</a> is signed in <a href=\"https://wikipedia.org/wiki/Washington,_D.C.\" title=\"Washington, D.C.\">Washington, D.C.</a>, <a href=\"https://wikipedia.org/wiki/London\" title=\"London\">London</a> and <a href=\"https://wikipedia.org/wiki/Moscow\" title=\"Moscow\">Moscow</a> by sixty-two countries.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Treaty_on_the_Non-Proliferation_of_Nuclear_Weapons\" title=\"Treaty on the Non-Proliferation of Nuclear Weapons\">Treaty on the Non-Proliferation of Nuclear Weapons</a> is signed in <a href=\"https://wikipedia.org/wiki/Washington,_D.C.\" title=\"Washington, D.C.\">Washington, D.C.</a>, <a href=\"https://wikipedia.org/wiki/London\" title=\"London\">London</a> and <a href=\"https://wikipedia.org/wiki/Moscow\" title=\"Moscow\">Moscow</a> by sixty-two countries.", "links": [{"title": "Treaty on the Non-Proliferation of Nuclear Weapons", "link": "https://wikipedia.org/wiki/Treaty_on_the_Non-Proliferation_of_Nuclear_Weapons"}, {"title": "Washington, D.C.", "link": "https://wikipedia.org/wiki/Washington,_D.C."}, {"title": "London", "link": "https://wikipedia.org/wiki/London"}, {"title": "Moscow", "link": "https://wikipedia.org/wiki/Moscow"}]}, {"year": "1968", "text": "Formal separation of the United Auto Workers from the AFL-CIO in the United States.", "html": "1968 - Formal separation of the <a href=\"https://wikipedia.org/wiki/United_Auto_Workers\" title=\"United Auto Workers\">United Auto Workers</a> from the <a href=\"https://wikipedia.org/wiki/AFL%E2%80%93CIO\" class=\"mw-redirect\" title=\"AFL-CIO\">AFL-CIO</a> in the United States.", "no_year_html": "Formal separation of the <a href=\"https://wikipedia.org/wiki/United_Auto_Workers\" title=\"United Auto Workers\">United Auto Workers</a> from the <a href=\"https://wikipedia.org/wiki/AFL%E2%80%93CIO\" class=\"mw-redirect\" title=\"AFL-CIO\">AFL-CIO</a> in the United States.", "links": [{"title": "United Auto Workers", "link": "https://wikipedia.org/wiki/United_Auto_Workers"}, {"title": "AFL-CIO", "link": "https://wikipedia.org/wiki/AFL%E2%80%93CIO"}]}, {"year": "1972", "text": "The first Gay pride march in England takes place.", "html": "1972 - The first <a href=\"https://wikipedia.org/wiki/Gay_pride\" class=\"mw-redirect\" title=\"Gay pride\">Gay pride</a> march in <a href=\"https://wikipedia.org/wiki/Pride_London\" class=\"mw-redirect\" title=\"Pride London\">England</a> takes place.", "no_year_html": "The first <a href=\"https://wikipedia.org/wiki/Gay_pride\" class=\"mw-redirect\" title=\"Gay pride\">Gay pride</a> march in <a href=\"https://wikipedia.org/wiki/Pride_London\" class=\"mw-redirect\" title=\"Pride London\">England</a> takes place.", "links": [{"title": "Gay pride", "link": "https://wikipedia.org/wiki/Gay_pride"}, {"title": "Pride London", "link": "https://wikipedia.org/wiki/Pride_London"}]}, {"year": "1976", "text": "Portugal grants autonomy to Madeira.", "html": "1976 - Portugal grants autonomy to <a href=\"https://wikipedia.org/wiki/Madeira\" title=\"Madeira\">Madeira</a>.", "no_year_html": "Portugal grants autonomy to <a href=\"https://wikipedia.org/wiki/Madeira\" title=\"Madeira\">Madeira</a>.", "links": [{"title": "Madeira", "link": "https://wikipedia.org/wiki/Madeira"}]}, {"year": "1978", "text": "The Northern Territory in Australia is granted self-government.", "html": "1978 - The <a href=\"https://wikipedia.org/wiki/Northern_Territory\" title=\"Northern Territory\">Northern Territory</a> in Australia is granted self-government.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Northern_Territory\" title=\"Northern Territory\">Northern Territory</a> in Australia is granted self-government.", "links": [{"title": "Northern Territory", "link": "https://wikipedia.org/wiki/Northern_Territory"}]}, {"year": "1979", "text": "Sony introduces the <PERSON><PERSON>.", "html": "1979 - <a href=\"https://wikipedia.org/wiki/Sony\" title=\"Sony\">Sony</a> introduces the <a href=\"https://wikipedia.org/wiki/Walkman\" title=\"Walkman\">Walkman</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Sony\" title=\"Sony\">Sony</a> introduces the <a href=\"https://wikipedia.org/wiki/Walkman\" title=\"Walkman\">Walkman</a>.", "links": [{"title": "Sony", "link": "https://wikipedia.org/wiki/Sony"}, {"title": "<PERSON>man", "link": "https://wikipedia.org/wiki/<PERSON>man"}]}, {"year": "1980", "text": "\"O Canada\" officially becomes the national anthem of Canada.", "html": "1980 - \"<a href=\"https://wikipedia.org/wiki/O_Canada\" title=\"O Canada\">O Canada</a>\" officially becomes the <a href=\"https://wikipedia.org/wiki/National_anthem\" title=\"National anthem\">national anthem</a> of Canada.", "no_year_html": "\"<a href=\"https://wikipedia.org/wiki/O_Canada\" title=\"O Canada\">O Canada</a>\" officially becomes the <a href=\"https://wikipedia.org/wiki/National_anthem\" title=\"National anthem\">national anthem</a> of Canada.", "links": [{"title": "O Canada", "link": "https://wikipedia.org/wiki/O_Canada"}, {"title": "National anthem", "link": "https://wikipedia.org/wiki/National_anthem"}]}, {"year": "1983", "text": "A North Korean Ilyushin Il-62M jet en route to Conakry Airport in Guinea crashes into the Fouta Djallon mountains in Guinea-Bissau, killing all 23 people on board.", "html": "1983 - A <a href=\"https://wikipedia.org/wiki/North_Korea\" title=\"North Korea\">North Korean</a> <a href=\"https://wikipedia.org/wiki/Ilyushin_Il-62\" title=\"Ilyushin Il-62\">Ilyushin Il-62M</a> jet en route to <a href=\"https://wikipedia.org/wiki/Conakry_Airport\" class=\"mw-redirect\" title=\"Conakry Airport\">Conakry Airport</a> in <a href=\"https://wikipedia.org/wiki/Guinea\" title=\"Guinea\">Guinea</a> <a href=\"https://wikipedia.org/wiki/1983_CAAK_Ilyushin_Il-62_crash\" title=\"1983 CAAK Ilyushin Il-62 crash\">crashes</a> into the <a href=\"https://wikipedia.org/wiki/Fouta_Djallon\" title=\"Fouta Djallon\">Fouta Djallon</a> mountains in <a href=\"https://wikipedia.org/wiki/Guinea-Bissau\" title=\"Guinea-Bissau\">Guinea-Bissau</a>, killing all 23 people on board.", "no_year_html": "A <a href=\"https://wikipedia.org/wiki/North_Korea\" title=\"North Korea\">North Korean</a> <a href=\"https://wikipedia.org/wiki/Ilyushin_Il-62\" title=\"Ilyushin Il-62\">Ilyushin Il-62M</a> jet en route to <a href=\"https://wikipedia.org/wiki/Conakry_Airport\" class=\"mw-redirect\" title=\"Conakry Airport\">Conakry Airport</a> in <a href=\"https://wikipedia.org/wiki/Guinea\" title=\"Guinea\">Guinea</a> <a href=\"https://wikipedia.org/wiki/1983_CAAK_Ilyushin_Il-62_crash\" title=\"1983 CAAK Ilyushin Il-62 crash\">crashes</a> into the <a href=\"https://wikipedia.org/wiki/Fouta_Djallon\" title=\"Fouta Djallon\">Fouta Djallon</a> mountains in <a href=\"https://wikipedia.org/wiki/Guinea-Bissau\" title=\"Guinea-Bissau\">Guinea-Bissau</a>, killing all 23 people on board.", "links": [{"title": "North Korea", "link": "https://wikipedia.org/wiki/North_Korea"}, {"title": "Ilyushin Il-62", "link": "https://wikipedia.org/wiki/Ilyushin_Il-62"}, {"title": "Conakry Airport", "link": "https://wikipedia.org/wiki/Conakry_Airport"}, {"title": "Guinea", "link": "https://wikipedia.org/wiki/Guinea"}, {"title": "1983 CAAK Ilyushin Il-62 crash", "link": "https://wikipedia.org/wiki/1983_CAAK_Ilyushin_Il-62_crash"}, {"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}, {"title": "Guinea-Bissau", "link": "https://wikipedia.org/wiki/Guinea-Bissau"}]}, {"year": "1983", "text": "The Ministry of State Security is established as China's principal intelligence agency", "html": "1983 - The <a href=\"https://wikipedia.org/wiki/Ministry_of_State_Security_(China)\" title=\"Ministry of State Security (China)\">Ministry of State Security</a> is established as China's principal <a href=\"https://wikipedia.org/wiki/Intelligence_agency\" title=\"Intelligence agency\">intelligence agency</a>", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Ministry_of_State_Security_(China)\" title=\"Ministry of State Security (China)\">Ministry of State Security</a> is established as China's principal <a href=\"https://wikipedia.org/wiki/Intelligence_agency\" title=\"Intelligence agency\">intelligence agency</a>", "links": [{"title": "Ministry of State Security (China)", "link": "https://wikipedia.org/wiki/Ministry_of_State_Security_(China)"}, {"title": "Intelligence agency", "link": "https://wikipedia.org/wiki/Intelligence_agency"}]}, {"year": "1984", "text": "The PG-13 rating is introduced by the MPAA.", "html": "1984 - The <a href=\"https://wikipedia.org/wiki/Motion_Picture_Association_film_rating_system\" title=\"Motion Picture Association film rating system\">PG-13</a> rating is introduced by the <a href=\"https://wikipedia.org/wiki/Motion_Picture_Association\" title=\"Motion Picture Association\">MPAA</a>.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Motion_Picture_Association_film_rating_system\" title=\"Motion Picture Association film rating system\">PG-13</a> rating is introduced by the <a href=\"https://wikipedia.org/wiki/Motion_Picture_Association\" title=\"Motion Picture Association\">MPAA</a>.", "links": [{"title": "Motion Picture Association film rating system", "link": "https://wikipedia.org/wiki/Motion_Picture_Association_film_rating_system"}, {"title": "Motion Picture Association", "link": "https://wikipedia.org/wiki/Motion_Picture_Association"}]}, {"year": "1987", "text": "The American radio station WFAN in New York City is launched as the world's first all-sports radio station.", "html": "1987 - The American radio station <a href=\"https://wikipedia.org/wiki/WFAN_(AM)\" title=\"WFAN (AM)\">WFAN</a> in New York City is launched as the world's first <a href=\"https://wikipedia.org/wiki/Sports_radio\" title=\"Sports radio\">all-sports radio</a> station.", "no_year_html": "The American radio station <a href=\"https://wikipedia.org/wiki/WFAN_(AM)\" title=\"WFAN (AM)\">WFAN</a> in New York City is launched as the world's first <a href=\"https://wikipedia.org/wiki/Sports_radio\" title=\"Sports radio\">all-sports radio</a> station.", "links": [{"title": "WFAN (AM)", "link": "https://wikipedia.org/wiki/WFAN_(AM)"}, {"title": "Sports radio", "link": "https://wikipedia.org/wiki/Sports_radio"}]}, {"year": "1990", "text": "German reunification: East Germany accepts the Deutsche Mark as its currency, thus uniting the economies of East and West Germany.", "html": "1990 - <a href=\"https://wikipedia.org/wiki/German_reunification\" title=\"German reunification\">German reunification</a>: <a href=\"https://wikipedia.org/wiki/East_Germany\" title=\"East Germany\">East Germany</a> accepts the <a href=\"https://wikipedia.org/wiki/Deutsche_Mark\" title=\"Deutsche Mark\">Deutsche Mark</a> as its currency, thus uniting the economies of East and West Germany.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/German_reunification\" title=\"German reunification\">German reunification</a>: <a href=\"https://wikipedia.org/wiki/East_Germany\" title=\"East Germany\">East Germany</a> accepts the <a href=\"https://wikipedia.org/wiki/Deutsche_Mark\" title=\"Deutsche Mark\">Deutsche Mark</a> as its currency, thus uniting the economies of East and West Germany.", "links": [{"title": "German reunification", "link": "https://wikipedia.org/wiki/German_reunification"}, {"title": "East Germany", "link": "https://wikipedia.org/wiki/East_Germany"}, {"title": "Deutsche Mark", "link": "https://wikipedia.org/wiki/Deutsche_Mark"}]}, {"year": "1991", "text": "Cold War: The Warsaw Pact is officially dissolved at a meeting in Prague.", "html": "1991 - <a href=\"https://wikipedia.org/wiki/Cold_War\" title=\"Cold War\">Cold War</a>: The <a href=\"https://wikipedia.org/wiki/Warsaw_Pact\" title=\"Warsaw Pact\">Warsaw Pact</a> is officially dissolved at a meeting in <a href=\"https://wikipedia.org/wiki/Prague\" title=\"Prague\">Prague</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Cold_War\" title=\"Cold War\">Cold War</a>: The <a href=\"https://wikipedia.org/wiki/Warsaw_Pact\" title=\"Warsaw Pact\">Warsaw Pact</a> is officially dissolved at a meeting in <a href=\"https://wikipedia.org/wiki/Prague\" title=\"Prague\">Prague</a>.", "links": [{"title": "Cold War", "link": "https://wikipedia.org/wiki/Cold_War"}, {"title": "Warsaw Pact", "link": "https://wikipedia.org/wiki/Warsaw_Pact"}, {"title": "Prague", "link": "https://wikipedia.org/wiki/Prague"}]}, {"year": "1991", "text": "The Finnish operator Radiolinja is launched as the world's first GSM network.", "html": "1991 - The Finnish operator <a href=\"https://wikipedia.org/wiki/Radiolinja\" title=\"Radiolinja\">Radiolinja</a> is launched as the world's first <a href=\"https://wikipedia.org/wiki/GSM\" title=\"GSM\">GSM network</a>.", "no_year_html": "The Finnish operator <a href=\"https://wikipedia.org/wiki/Radiolinja\" title=\"Radiolinja\">Radiolinja</a> is launched as the world's first <a href=\"https://wikipedia.org/wiki/GSM\" title=\"GSM\">GSM network</a>.", "links": [{"title": "Radiolinja", "link": "https://wikipedia.org/wiki/Radiolinja"}, {"title": "GSM", "link": "https://wikipedia.org/wiki/GSM"}]}, {"year": "1997", "text": "China resumes sovereignty over the city-state of Hong Kong, ending 156 years of British colonial rule. The handover ceremony is attended by British Prime Minister <PERSON>, <PERSON>, Prince of Wales, Chinese President <PERSON> and U.S. Secretary of State <PERSON>.", "html": "1997 - China <a href=\"https://wikipedia.org/wiki/Handover_of_Hong_Kong\" title=\"Handover of Hong Kong\">resumes sovereignty</a> over the city-state of Hong Kong, ending 156 years of British <a href=\"https://wikipedia.org/wiki/Colonialism\" title=\"Colonialism\">colonial</a> rule. The handover ceremony is attended by British Prime Minister <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_the_United_Kingdom\" class=\"mw-redirect\" title=\"<PERSON> of the United Kingdom\"><PERSON>, Prince of Wales</a>, Chinese President <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> and U.S. Secretary of State <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>.", "no_year_html": "China <a href=\"https://wikipedia.org/wiki/Handover_of_Hong_Kong\" title=\"Handover of Hong Kong\">resumes sovereignty</a> over the city-state of Hong Kong, ending 156 years of British <a href=\"https://wikipedia.org/wiki/Colonialism\" title=\"Colonialism\">colonial</a> rule. The handover ceremony is attended by British Prime Minister <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_the_United_Kingdom\" class=\"mw-redirect\" title=\"<PERSON> of the United Kingdom\"><PERSON>, Prince of Wales</a>, Chinese President <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> and U.S. Secretary of State <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>.", "links": [{"title": "Handover of Hong Kong", "link": "https://wikipedia.org/wiki/Handover_of_Hong_Kong"}, {"title": "Colonialism", "link": "https://wikipedia.org/wiki/Colonialism"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "<PERSON> of the United Kingdom", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_the_United_Kingdom"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1997", "text": "Space Shuttle program: Space Shuttle Columbia is launched on STS-94, a re-flight of the prematurely-ended STS-83 mission with the same crew.", "html": "1997 - <a href=\"https://wikipedia.org/wiki/Space_Shuttle_program\" title=\"Space Shuttle program\">Space Shuttle program</a>: <a href=\"https://wikipedia.org/wiki/Space_Shuttle_Columbia\" title=\"Space Shuttle Columbia\">Space Shuttle <i>Columbia</i></a> is launched on <a href=\"https://wikipedia.org/wiki/STS-94\" title=\"STS-94\">STS-94</a>, a re-flight of the prematurely-ended <a href=\"https://wikipedia.org/wiki/STS-83\" title=\"STS-83\">STS-83</a> mission with the same crew.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Space_Shuttle_program\" title=\"Space Shuttle program\">Space Shuttle program</a>: <a href=\"https://wikipedia.org/wiki/Space_Shuttle_Columbia\" title=\"Space Shuttle Columbia\">Space Shuttle <i>Columbia</i></a> is launched on <a href=\"https://wikipedia.org/wiki/STS-94\" title=\"STS-94\">STS-94</a>, a re-flight of the prematurely-ended <a href=\"https://wikipedia.org/wiki/STS-83\" title=\"STS-83\">STS-83</a> mission with the same crew.", "links": [{"title": "Space Shuttle program", "link": "https://wikipedia.org/wiki/Space_Shuttle_program"}, {"title": "Space Shuttle Columbia", "link": "https://wikipedia.org/wiki/Space_Shuttle_Columbia"}, {"title": "STS-94", "link": "https://wikipedia.org/wiki/STS-94"}, {"title": "STS-83", "link": "https://wikipedia.org/wiki/STS-83"}]}, {"year": "1999", "text": "The Scottish Parliament is officially opened by <PERSON> on the day that legislative powers are officially transferred from the old Scottish Office in London to the new devolved Scottish Executive in Edinburgh. In Wales, the powers of the Welsh Secretary are transferred to the National Assembly.", "html": "1999 - The <a href=\"https://wikipedia.org/wiki/Scottish_Parliament\" title=\"Scottish Parliament\">Scottish Parliament</a> is officially opened by <a href=\"https://wikipedia.org/wiki/Elizabeth_II\" title=\"Elizabeth II\"><PERSON> II</a> on the day that legislative powers are officially transferred from the old <a href=\"https://wikipedia.org/wiki/Scottish_Office\" title=\"Scottish Office\">Scottish Office</a> in London to the new devolved <a href=\"https://wikipedia.org/wiki/Scottish_Executive\" class=\"mw-redirect\" title=\"Scottish Executive\">Scottish Executive</a> in <a href=\"https://wikipedia.org/wiki/Edinburgh\" title=\"Edinburgh\">Edinburgh</a>. In Wales, the powers of the <a href=\"https://wikipedia.org/wiki/Secretary_of_State_for_Wales\" title=\"Secretary of State for Wales\">Welsh Secretary</a> are transferred to the <a href=\"https://wikipedia.org/wiki/National_Assembly_for_Wales\" class=\"mw-redirect\" title=\"National Assembly for Wales\">National Assembly</a>.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Scottish_Parliament\" title=\"Scottish Parliament\">Scottish Parliament</a> is officially opened by <a href=\"https://wikipedia.org/wiki/Elizabeth_II\" title=\"<PERSON> II\"><PERSON> II</a> on the day that legislative powers are officially transferred from the old <a href=\"https://wikipedia.org/wiki/Scottish_Office\" title=\"Scottish Office\">Scottish Office</a> in London to the new devolved <a href=\"https://wikipedia.org/wiki/Scottish_Executive\" class=\"mw-redirect\" title=\"Scottish Executive\">Scottish Executive</a> in <a href=\"https://wikipedia.org/wiki/Edinburgh\" title=\"Edinburgh\">Edinburgh</a>. In Wales, the powers of the <a href=\"https://wikipedia.org/wiki/Secretary_of_State_for_Wales\" title=\"Secretary of State for Wales\">Welsh Secretary</a> are transferred to the <a href=\"https://wikipedia.org/wiki/National_Assembly_for_Wales\" class=\"mw-redirect\" title=\"National Assembly for Wales\">National Assembly</a>.", "links": [{"title": "Scottish Parliament", "link": "https://wikipedia.org/wiki/Scottish_Parliament"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_II"}, {"title": "Scottish Office", "link": "https://wikipedia.org/wiki/Scottish_Office"}, {"title": "Scottish Executive", "link": "https://wikipedia.org/wiki/Scottish_Executive"}, {"title": "Edinburgh", "link": "https://wikipedia.org/wiki/Edinburgh"}, {"title": "Secretary of State for Wales", "link": "https://wikipedia.org/wiki/Secretary_of_State_for_Wales"}, {"title": "National Assembly for Wales", "link": "https://wikipedia.org/wiki/National_Assembly_for_Wales"}]}, {"year": "2002", "text": "The International Criminal Court is established to prosecute individuals for genocide, crimes against humanity, war crimes and the crime of aggression.", "html": "2002 - The <a href=\"https://wikipedia.org/wiki/International_Criminal_Court\" title=\"International Criminal Court\">International Criminal Court</a> is established to prosecute individuals for <a href=\"https://wikipedia.org/wiki/Genocide\" title=\"Genocide\">genocide</a>, <a href=\"https://wikipedia.org/wiki/Crimes_against_humanity\" title=\"Crimes against humanity\">crimes against humanity</a>, <a href=\"https://wikipedia.org/wiki/War_crime\" title=\"War crime\">war crimes</a> and the <a href=\"https://wikipedia.org/wiki/Crime_of_aggression\" title=\"Crime of aggression\">crime of aggression</a>.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/International_Criminal_Court\" title=\"International Criminal Court\">International Criminal Court</a> is established to prosecute individuals for <a href=\"https://wikipedia.org/wiki/Genocide\" title=\"Genocide\">genocide</a>, <a href=\"https://wikipedia.org/wiki/Crimes_against_humanity\" title=\"Crimes against humanity\">crimes against humanity</a>, <a href=\"https://wikipedia.org/wiki/War_crime\" title=\"War crime\">war crimes</a> and the <a href=\"https://wikipedia.org/wiki/Crime_of_aggression\" title=\"Crime of aggression\">crime of aggression</a>.", "links": [{"title": "International Criminal Court", "link": "https://wikipedia.org/wiki/International_Criminal_Court"}, {"title": "Genocide", "link": "https://wikipedia.org/wiki/Genocide"}, {"title": "Crimes against humanity", "link": "https://wikipedia.org/wiki/Crimes_against_humanity"}, {"title": "War crime", "link": "https://wikipedia.org/wiki/War_crime"}, {"title": "Crime of aggression", "link": "https://wikipedia.org/wiki/Crime_of_aggression"}]}, {"year": "2002", "text": "Bashkirian Airlines Flight 2937, a Tupolev Tu-154, and DHL Flight 611, a Boeing 757, collide in mid-air over Überlingen, southern Germany, killing all 71 on board both planes.", "html": "2002 - Bashkirian Airlines Flight 2937, a <a href=\"https://wikipedia.org/wiki/Tupolev_Tu-154\" title=\"Tupolev Tu-154\">Tupolev Tu-154</a>, and DHL Flight 611, a <a href=\"https://wikipedia.org/wiki/Boeing_757\" title=\"Boeing 757\">Boeing 757</a>, <a href=\"https://wikipedia.org/wiki/2002_%C3%9Cberlingen_mid-air_collision\" title=\"2002 Überlingen mid-air collision\">collide in mid-air</a> over <a href=\"https://wikipedia.org/wiki/%C3%9Cberlingen\" title=\"Überlingen\">Überlingen</a>, southern Germany, killing all 71 on board both planes.", "no_year_html": "Bashkirian Airlines Flight 2937, a <a href=\"https://wikipedia.org/wiki/Tupolev_Tu-154\" title=\"Tupolev Tu-154\">Tupolev Tu-154</a>, and DHL Flight 611, a <a href=\"https://wikipedia.org/wiki/Boeing_757\" title=\"Boeing 757\">Boeing 757</a>, <a href=\"https://wikipedia.org/wiki/2002_%C3%9Cberlingen_mid-air_collision\" title=\"2002 Überlingen mid-air collision\">collide in mid-air</a> over <a href=\"https://wikipedia.org/wiki/%C3%9Cberlingen\" title=\"Überlingen\">Überlingen</a>, southern Germany, killing all 71 on board both planes.", "links": [{"title": "Tupolev Tu-154", "link": "https://wikipedia.org/wiki/Tupolev_Tu-154"}, {"title": "Boeing 757", "link": "https://wikipedia.org/wiki/Boeing_757"}, {"title": "2002 Überlingen mid-air collision", "link": "https://wikipedia.org/wiki/2002_%C3%9Cberlingen_mid-air_collision"}, {"title": "Überlingen", "link": "https://wikipedia.org/wiki/%C3%9Cberlingen"}]}, {"year": "2003", "text": "Over 500,000 people protest against efforts to pass anti-sedition legislation in Hong Kong.", "html": "2003 - Over 500,000 people protest against efforts to pass <a href=\"https://wikipedia.org/wiki/Hong_Kong_Basic_Law_Article_23\" title=\"Hong Kong Basic Law Article 23\">anti-sedition legislation</a> in Hong Kong.", "no_year_html": "Over 500,000 people protest against efforts to pass <a href=\"https://wikipedia.org/wiki/Hong_Kong_Basic_Law_Article_23\" title=\"Hong Kong Basic Law Article 23\">anti-sedition legislation</a> in Hong Kong.", "links": [{"title": "Hong Kong Basic Law Article 23", "link": "https://wikipedia.org/wiki/Hong_Kong_Basic_Law_Article_23"}]}, {"year": "2004", "text": "Saturn orbit insertion of Cassini-Huygens begins at 01:12 UTC and ends at 02:48 UTC.", "html": "2004 - <a href=\"https://wikipedia.org/wiki/Saturn\" title=\"Saturn\">Saturn</a> <a href=\"https://wikipedia.org/wiki/Orbit_insertion\" title=\"Orbit insertion\">orbit insertion</a> of <a href=\"https://wikipedia.org/wiki/Cassini%E2%80%93Huygens\" title=\"Cassini-Huygens\">Cassini-Huygens</a> begins at 01:12 <a href=\"https://wikipedia.org/wiki/UTC\" class=\"mw-redirect\" title=\"UTC\">UTC</a> and ends at 02:48 UTC.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Saturn\" title=\"Saturn\">Saturn</a> <a href=\"https://wikipedia.org/wiki/Orbit_insertion\" title=\"Orbit insertion\">orbit insertion</a> of <a href=\"https://wikipedia.org/wiki/Cassini%E2%80%93Huygens\" title=\"Cassini-Huygens\">Cassini-Huygens</a> begins at 01:12 <a href=\"https://wikipedia.org/wiki/UTC\" class=\"mw-redirect\" title=\"UTC\">UTC</a> and ends at 02:48 UTC.", "links": [{"title": "Saturn", "link": "https://wikipedia.org/wiki/Saturn"}, {"title": "Orbit insertion", "link": "https://wikipedia.org/wiki/Orbit_insertion"}, {"title": "Cassini-Huygens", "link": "https://wikipedia.org/wiki/Cassini%E2%80%93Huygens"}, {"title": "UTC", "link": "https://wikipedia.org/wiki/UTC"}]}, {"year": "2006", "text": "The first operation of Qinghai-Tibet Railway is conducted in China.", "html": "2006 - The first operation of <a href=\"https://wikipedia.org/wiki/Qinghai%E2%80%93Tibet_railway\" title=\"Qinghai-Tibet railway\">Qinghai-Tibet Railway</a> is conducted in China.", "no_year_html": "The first operation of <a href=\"https://wikipedia.org/wiki/Qinghai%E2%80%93Tibet_railway\" title=\"Qinghai-Tibet railway\">Qinghai-Tibet Railway</a> is conducted in China.", "links": [{"title": "Qinghai-Tibet railway", "link": "https://wikipedia.org/wiki/Qinghai%E2%80%93Tibet_railway"}]}, {"year": "2007", "text": "Smoking in England is banned in all public indoor spaces.", "html": "2007 - Smoking in England is <a href=\"https://wikipedia.org/wiki/Smoking_bans_in_the_United_Kingdom\" title=\"Smoking bans in the United Kingdom\">banned</a> in all public indoor spaces.", "no_year_html": "Smoking in England is <a href=\"https://wikipedia.org/wiki/Smoking_bans_in_the_United_Kingdom\" title=\"Smoking bans in the United Kingdom\">banned</a> in all public indoor spaces.", "links": [{"title": "Smoking bans in the United Kingdom", "link": "https://wikipedia.org/wiki/Smoking_bans_in_the_United_Kingdom"}]}, {"year": "2008", "text": "Riots erupt in Mongolia in response to allegations of fraud surrounding the 2008 legislative elections.", "html": "2008 - <a href=\"https://wikipedia.org/wiki/2008_riot_in_Mongolia\" title=\"2008 riot in Mongolia\">Riots erupt in Mongolia</a> in response to allegations of fraud surrounding the 2008 legislative elections.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/2008_riot_in_Mongolia\" title=\"2008 riot in Mongolia\">Riots erupt in Mongolia</a> in response to allegations of fraud surrounding the 2008 legislative elections.", "links": [{"title": "2008 riot in Mongolia", "link": "https://wikipedia.org/wiki/2008_riot_in_Mongolia"}]}, {"year": "2013", "text": "Croatia becomes the 28th member of the European Union.", "html": "2013 - <a href=\"https://wikipedia.org/wiki/Croatia\" title=\"Croatia\">Croatia</a> becomes the <a href=\"https://wikipedia.org/wiki/2013_enlargement_of_the_European_Union\" title=\"2013 enlargement of the European Union\">28th member</a> of the <a href=\"https://wikipedia.org/wiki/European_Union\" title=\"European Union\">European Union</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Croatia\" title=\"Croatia\">Croatia</a> becomes the <a href=\"https://wikipedia.org/wiki/2013_enlargement_of_the_European_Union\" title=\"2013 enlargement of the European Union\">28th member</a> of the <a href=\"https://wikipedia.org/wiki/European_Union\" title=\"European Union\">European Union</a>.", "links": [{"title": "Croatia", "link": "https://wikipedia.org/wiki/Croatia"}, {"title": "2013 enlargement of the European Union", "link": "https://wikipedia.org/wiki/2013_enlargement_of_the_European_Union"}, {"title": "European Union", "link": "https://wikipedia.org/wiki/European_Union"}]}, {"year": "2020", "text": "The United States-Mexico-Canada Agreement replaces NAFTA.", "html": "2020 - The <a href=\"https://wikipedia.org/wiki/United_States%E2%80%93Mexico%E2%80%93Canada_Agreement\" title=\"United States-Mexico-Canada Agreement\">United States-Mexico-Canada Agreement</a> replaces NAFTA.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/United_States%E2%80%93Mexico%E2%80%93Canada_Agreement\" title=\"United States-Mexico-Canada Agreement\">United States-Mexico-Canada Agreement</a> replaces NAFTA.", "links": [{"title": "United States-Mexico-Canada Agreement", "link": "https://wikipedia.org/wiki/United_States%E2%80%93Mexico%E2%80%93Canada_Agreement"}]}, {"year": "2024", "text": "At the centennial ceremony of the Dominion of Newfoundland National War Memorial, the Commonwealth War Graves Commission allowed an unprecedented second Canadian Tomb of the Unknown Soldier. The Royal Newfoundland Regiment solder was entombed in the memorial at this ceremony.", "html": "2024 - At the centennial ceremony of the <a href=\"https://wikipedia.org/wiki/National_War_Memorial_(Newfoundland)\" title=\"National War Memorial (Newfoundland)\">Dominion of Newfoundland National War Memorial</a>, the <a href=\"https://wikipedia.org/wiki/Commonwealth_War_Graves_Commission\" title=\"Commonwealth War Graves Commission\">Commonwealth War Graves Commission</a> allowed an unprecedented second Canadian Tomb of the Unknown Soldier. The <a href=\"https://wikipedia.org/wiki/Royal_Newfoundland_Regiment\" title=\"Royal Newfoundland Regiment\">Royal Newfoundland Regiment</a> solder was entombed in the memorial at this ceremony.", "no_year_html": "At the centennial ceremony of the <a href=\"https://wikipedia.org/wiki/National_War_Memorial_(Newfoundland)\" title=\"National War Memorial (Newfoundland)\">Dominion of Newfoundland National War Memorial</a>, the <a href=\"https://wikipedia.org/wiki/Commonwealth_War_Graves_Commission\" title=\"Commonwealth War Graves Commission\">Commonwealth War Graves Commission</a> allowed an unprecedented second Canadian Tomb of the Unknown Soldier. The <a href=\"https://wikipedia.org/wiki/Royal_Newfoundland_Regiment\" title=\"Royal Newfoundland Regiment\">Royal Newfoundland Regiment</a> solder was entombed in the memorial at this ceremony.", "links": [{"title": "National War Memorial (Newfoundland)", "link": "https://wikipedia.org/wiki/National_War_Memorial_(Newfoundland)"}, {"title": "Commonwealth War Graves Commission", "link": "https://wikipedia.org/wiki/Commonwealth_War_Graves_Commission"}, {"title": "Royal Newfoundland Regiment", "link": "https://wikipedia.org/wiki/Royal_Newfoundland_Regiment"}]}], "Births": [{"year": "1311", "text": "<PERSON>, Chinese military strategist, statesman and poet (d. 1375)", "html": "1311 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Chinese military strategist, statesman and poet (d. 1375)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Chinese military strategist, statesman and poet (d. 1375)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1464", "text": "<PERSON>, Italian noble (d. 1503)", "html": "1464 - <a href=\"https://wikipedia.org/wiki/<PERSON>_Go<PERSON>ga\" title=\"<PERSON> Gonzaga\"><PERSON></a>, Italian noble (d. 1503)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Gonzaga\"><PERSON></a>, Italian noble (d. 1503)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Clara_Go<PERSON>ga"}]}, {"year": "1481", "text": "<PERSON> of Denmark (d. 1559)", "html": "1481 - <a href=\"https://wikipedia.org/wiki/Christian_II_of_Denmark\" title=\"Christian II of Denmark\">Christian II of Denmark</a> (d. 1559)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Christian_II_of_Denmark\" title=\"Christian II of Denmark\">Christian II of Denmark</a> (d. 1559)", "links": [{"title": "<PERSON> of Denmark", "link": "https://wikipedia.org/wiki/Christian_II_of_Denmark"}]}, {"year": "1506", "text": "<PERSON> of Hungary (d. 1526)", "html": "1506 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Hungary\" title=\"<PERSON> II of Hungary\"><PERSON> of Hungary</a> (d. 1526)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Hungary\" title=\"<PERSON> II of Hungary\"><PERSON> of Hungary</a> (d. 1526)", "links": [{"title": "<PERSON> II of Hungary", "link": "https://wikipedia.org/wiki/Louis_II_of_Hungary"}]}, {"year": "1534", "text": "<PERSON> of Denmark (d. 1588)", "html": "1534 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Denmark\" title=\"<PERSON> II of Denmark\"><PERSON> of Denmark</a> (d. 1588)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Denmark\" title=\"<PERSON> II of Denmark\"><PERSON> of Denmark</a> (d. 1588)", "links": [{"title": "<PERSON> of Denmark", "link": "https://wikipedia.org/wiki/Frederick_<PERSON>_of_Denmark"}]}, {"year": "1553", "text": "<PERSON>, English carpenter and builder (d. 1609)", "html": "1553 - <a href=\"https://wikipedia.org/wiki/Peter_Street_(carpenter)\" title=\"Peter Street (carpenter)\">Peter Street</a>, English carpenter and builder (d. 1609)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Peter_Street_(carpenter)\" title=\"Peter Street (carpenter)\">Peter Street</a>, English carpenter and builder (d. 1609)", "links": [{"title": "<PERSON> (carpenter)", "link": "https://wikipedia.org/wiki/Peter_<PERSON>_(carpenter)"}]}, {"year": "1574", "text": "<PERSON>, English bishop and mystic (d. 1656)", "html": "1574 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(bishop)\" title=\"<PERSON> (bishop)\"><PERSON></a>, English bishop and mystic (d. 1656)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(bishop)\" title=\"<PERSON> (bishop)\"><PERSON></a>, English bishop and mystic (d. 1656)", "links": [{"title": "<PERSON> (bishop)", "link": "https://wikipedia.org/wiki/<PERSON>_(bishop)"}]}, {"year": "1586", "text": "<PERSON>, Italian lute player and composer (d. 1630)", "html": "1586 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian lute player and composer (d. 1630)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian lute player and composer (d. 1630)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1633", "text": "<PERSON>, Swiss theologian and author (d. 1698)", "html": "1633 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swiss theologian and author (d. 1698)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swiss theologian and author (d. 1698)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1646", "text": "<PERSON><PERSON><PERSON>, German mathematician and philosopher (d. 1716)", "html": "1646 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, German mathematician and philosopher (d. 1716)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, German mathematician and philosopher (d. 1716)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1663", "text": "<PERSON>, German composer and theorist (d. 1738)", "html": "1663 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German composer and theorist (d. 1738)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German composer and theorist (d. 1738)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "1725", "text": "<PERSON><PERSON><PERSON>, English painter and aristocrat (d. 1757)", "html": "1725 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, English painter and aristocrat (d. 1757)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, English painter and aristocrat (d. 1757)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Rhoda_<PERSON>aval"}]}, {"year": "1725", "text": "<PERSON><PERSON><PERSON>, comte <PERSON>, French general (d. 1807)", "html": "1725 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>_<PERSON>,_comte_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>, comte de R<PERSON>am<PERSON>au\"><PERSON><PERSON><PERSON>, comte de <PERSON></a>, French general (d. 1807)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>_<PERSON>,_comte_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>, comte de Rochambeau\"><PERSON><PERSON><PERSON>, comte de <PERSON></a>, French general (d. 1807)", "links": [{"title": "<PERSON><PERSON><PERSON>, comte <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>_<PERSON>,_comte_<PERSON>_<PERSON><PERSON><PERSON><PERSON><PERSON>"}]}, {"year": "1726", "text": "<PERSON><PERSON><PERSON><PERSON>, Jain saint (d. 1803)", "html": "1726 - <a href=\"https://wikipedia.org/wiki/A<PERSON>_<PERSON><PERSON><PERSON><PERSON>_(Jain_Monk)\" class=\"mw-redirect\" title=\"A<PERSON> <PERSON><PERSON><PERSON><PERSON> (Jain Monk)\">A<PERSON> <PERSON><PERSON><PERSON><PERSON></a>, Jain saint (d. 1803)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/A<PERSON>_<PERSON><PERSON><PERSON><PERSON>_(Jain_Monk)\" class=\"mw-redirect\" title=\"A<PERSON> <PERSON><PERSON><PERSON><PERSON> (Jain Monk)\">A<PERSON> <PERSON><PERSON><PERSON><PERSON></a>, Jain saint (d. 1803)", "links": [{"title": "<PERSON><PERSON> (Jain Monk)", "link": "https://wikipedia.org/wiki/A<PERSON>_<PERSON><PERSON><PERSON><PERSON>_(<PERSON>_<PERSON>)"}]}, {"year": "1731", "text": "<PERSON>, 1st Viscount <PERSON>, Scottish-English admiral (d. 1804)", "html": "1731 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_1st_Viscount_<PERSON>\" title=\"<PERSON>, 1st Viscount <PERSON>\"><PERSON>, 1st Viscount <PERSON></a>, Scottish-English admiral (d. 1804)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>,_1st_Viscount_<PERSON>\" title=\"<PERSON>, 1st Viscount <PERSON>\"><PERSON>, 1st Viscount <PERSON></a>, Scottish-English admiral (d. 1804)", "links": [{"title": "<PERSON>, 1st Viscount <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_1st_Viscount_<PERSON>"}]}, {"year": "1742", "text": "<PERSON>, German physicist and academic (d. 1799)", "html": "1742 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German physicist and academic (d. 1799)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German physicist and academic (d. 1799)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1771", "text": "<PERSON><PERSON>, Italian composer and conductor (d. 1839)", "html": "1771 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Italian composer and conductor (d. 1839)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Italian composer and conductor (d. 1839)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1788", "text": "<PERSON><PERSON><PERSON>, French mathematician and engineer (d. 1867)", "html": "1788 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, French mathematician and engineer (d. 1867)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, French mathematician and engineer (d. 1867)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>-<PERSON>"}]}, {"year": "1804", "text": "<PERSON>, American journalist and politician (d. 1886)", "html": "1804 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American journalist and politician (d. 1886)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American journalist and politician (d. 1886)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1804", "text": "<PERSON>, French author and playwright (d. 1876)", "html": "1804 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French author and playwright (d. 1876)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French author and playwright (d. 1876)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1807", "text": "<PERSON>, American politician and educator, founder of Clemson University (d. 1888)", "html": "1807 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American politician and educator, founder of <a href=\"https://wikipedia.org/wiki/Clemson_University\" title=\"Clemson University\">Clemson University</a> (d. 1888)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American politician and educator, founder of <a href=\"https://wikipedia.org/wiki/Clemson_University\" title=\"Clemson University\">Clemson University</a> (d. 1888)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "Clemson University", "link": "https://wikipedia.org/wiki/Clemson_University"}]}, {"year": "1808", "text": "<PERSON><PERSON><PERSON>, Mexican-American landowner (d. 1880)", "html": "1808 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_del_Valle\" title=\"Ygnacio del Valle\"><PERSON><PERSON><PERSON> Valle</a>, Mexican-American landowner (d. 1880)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_del_Valle\" title=\"Ygnacio del Valle\"><PERSON><PERSON><PERSON> Valle</a>, Mexican-American landowner (d. 1880)", "links": [{"title": "<PERSON><PERSON><PERSON> Valle", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_Valle"}]}, {"year": "1814", "text": "<PERSON>, Irish-Australian politician, 3rd Premier of South Australia (d. 1884)", "html": "1814 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Irish-Australian politician, 3rd <a href=\"https://wikipedia.org/wiki/Premier_of_South_Australia\" title=\"Premier of South Australia\">Premier of South Australia</a> (d. 1884)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Irish-Australian politician, 3rd <a href=\"https://wikipedia.org/wiki/Premier_of_South_Australia\" title=\"Premier of South Australia\">Premier of South Australia</a> (d. 1884)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Premier of South Australia", "link": "https://wikipedia.org/wiki/Premier_of_South_Australia"}]}, {"year": "1818", "text": "<PERSON><PERSON><PERSON>, Hungarian-Austrian physician and obstetrician (d. 1865)", "html": "1818 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Hungarian-Austrian physician and obstetrician (d. 1865)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Hungarian-Austrian physician and obstetrician (d. 1865)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1818", "text": "<PERSON>, German physician, psychologist and academic (d. 1884)", "html": "1818 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German physician, psychologist and academic (d. 1884)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German physician, psychologist and academic (d. 1884)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1822", "text": "<PERSON><PERSON><PERSON><PERSON>, Vietnamese poet and activist (d. 1888)", "html": "1822 - <a href=\"https://wikipedia.org/wiki/Nguy%E1%BB%85n_%C4%90%C3%ACnh_Chi%E1%BB%83u\" title=\"Nguyễn <PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Vietnamese poet and activist (d. 1888)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Nguy%E1%BB%85n_%C4%90%C3%ACnh_Chi%E1%BB%83u\" title=\"Nguyễ<PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Vietnamese poet and activist (d. 1888)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Nguy%E1%BB%85n_%C4%90%C3%ACnh_Chi%E1%BB%83u"}]}, {"year": "1834", "text": "<PERSON><PERSON><PERSON><PERSON>, Polish poet and author (d. 1908)", "html": "1834 - <a href=\"https://wikipedia.org/wiki/Jadwiga_%C5%81uszczewska\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Polish poet and author (d. 1908)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Jadwiga_%C5%81uszczewska\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Polish poet and author (d. 1908)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Jadwiga_%C5%81uszczewska"}]}, {"year": "1850", "text": "<PERSON>, American poet (d. 1927)", "html": "1850 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON> Coates\"><PERSON></a>, American poet (d. 1927)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON> Coates\"><PERSON></a>, American poet (d. 1927)", "links": [{"title": "<PERSON> Earle Coates", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1858", "text": "<PERSON>, American painter (d. 1925)", "html": "1858 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American painter (d. 1925)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American painter (d. 1925)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1858", "text": "<PERSON><PERSON><PERSON>, American editor and writer of prose and poetry (d. 1924)", "html": "1858 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American editor and writer of prose and poetry (d. 1924)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American editor and writer of prose and poetry (d. 1924)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1859", "text": "<PERSON><PERSON><PERSON><PERSON>, American painter (d. 1940)", "html": "1859 - <a href=\"https://wikipedia.org/wiki/DeL<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON> <PERSON><PERSON>\">De<PERSON><PERSON><PERSON> <PERSON><PERSON></a>, American painter (d. 1940)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/De<PERSON><PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON> W<PERSON>\">De<PERSON><PERSON><PERSON> <PERSON><PERSON></a>, American painter (d. 1940)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/De<PERSON><PERSON><PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "1863", "text": "<PERSON>, Canadian-English captain and explorer (d. 1892)", "html": "1863 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian-English captain and explorer (d. 1892)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian-English captain and explorer (d. 1892)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>air<PERSON>"}]}, {"year": "1869", "text": "<PERSON>, American author and educator (d. 1946)", "html": "1869 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>.\" title=\"<PERSON>\"><PERSON>.</a>, American author and educator (d. 1946)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>.\" title=\"<PERSON>\"><PERSON>.</a>, American author and educator (d. 1946)", "links": [{"title": "<PERSON>.", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>."}]}, {"year": "1872", "text": "<PERSON>, French pilot and engineer (d. 1936)", "html": "1872 - <a href=\"https://wikipedia.org/wiki/Louis_Bl%C3%A9riot\" title=\"<PERSON>\"><PERSON></a>, French pilot and engineer (d. 1936)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Louis_Bl%C3%A9riot\" title=\"<PERSON>\"><PERSON></a>, French pilot and engineer (d. 1936)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Louis_Bl%C3%A9riot"}]}, {"year": "1872", "text": "<PERSON>, English physicist and engineer (d. 1917)", "html": "1872 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English physicist and engineer (d. 1917)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English physicist and engineer (d. 1917)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1873", "text": "<PERSON>-<PERSON><PERSON><PERSON><PERSON>, French-American film director, producer and screenwriter (d. 1968)", "html": "1873 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>-<PERSON>ch%C3%A9\" title=\"<PERSON>\"><PERSON></a>, French-American film director, producer and screenwriter (d. 1968)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>-<PERSON>ch%C3%A9\" title=\"<PERSON>\"><PERSON></a>, French-American film director, producer and screenwriter (d. 1968)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>-Blach%C3%A9"}]}, {"year": "1873", "text": "<PERSON><PERSON>, Faroese politician, 1st Prime Minister of the Faroe Islands (d. 1954)", "html": "1873 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Faroese politician, 1st <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_the_Faroe_Islands\" class=\"mw-redirect\" title=\"Prime Minister of the Faroe Islands\">Prime Minister of the Faroe Islands</a> (d. 1954)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Faroese politician, 1st <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_the_Faroe_Islands\" class=\"mw-redirect\" title=\"Prime Minister of the Faroe Islands\">Prime Minister of the Faroe Islands</a> (d. 1954)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}, {"title": "Prime Minister of the Faroe Islands", "link": "https://wikipedia.org/wiki/Prime_Minister_of_the_Faroe_Islands"}]}, {"year": "1875", "text": "<PERSON>, American con man (d. 1976)", "html": "1875 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American con man (d. 1976)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American con man (d. 1976)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1876", "text": "<PERSON><PERSON> <PERSON><PERSON>, Australian politician, 19th Premier of Queensland (d. 1921)", "html": "1876 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> <PERSON><PERSON>\"><PERSON><PERSON> <PERSON><PERSON></a>, Australian politician, 19th <a href=\"https://wikipedia.org/wiki/Premier_of_Queensland\" title=\"Premier of Queensland\">Premier of Queensland</a> (d. 1921)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>._<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> <PERSON><PERSON>\"><PERSON><PERSON> <PERSON><PERSON></a>, Australian politician, 19th <a href=\"https://wikipedia.org/wiki/Premier_of_Queensland\" title=\"Premier of Queensland\">Premier of Queensland</a> (d. 1921)", "links": [{"title": "<PERSON><PERSON> <PERSON><PERSON>", "link": "https://wikipedia.org/wiki/T._<PERSON><PERSON>_<PERSON>"}, {"title": "Premier of Queensland", "link": "https://wikipedia.org/wiki/Premier_of_Queensland"}]}, {"year": "1878", "text": "<PERSON>, Estonian-German architect (d. 1944)", "html": "1878 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Estonian-German architect (d. 1944)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Estonian-German architect (d. 1944)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1879", "text": "<PERSON>, French union leader, Nobel Prize laureate (d. 1954)", "html": "1879 - <a href=\"https://wikipedia.org/wiki/L%C3%A<PERSON><PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French union leader, <a href=\"https://wikipedia.org/wiki/Nobel_Peace_Prize\" title=\"Nobel Peace Prize\">Nobel Prize</a> laureate (d. 1954)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/L%C3%A<PERSON><PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French union leader, <a href=\"https://wikipedia.org/wiki/Nobel_Peace_Prize\" title=\"Nobel Peace Prize\">Nobel Prize</a> laureate (d. 1954)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/L%C3%A<PERSON><PERSON>_<PERSON>"}, {"title": "Nobel Peace Prize", "link": "https://wikipedia.org/wiki/Nobel_Peace_Prize"}]}, {"year": "1881", "text": "<PERSON>, English geologist (d. 1965)", "html": "1881 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English geologist (d. 1965)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English geologist (d. 1965)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1882", "text": "<PERSON><PERSON><PERSON>, Indian physician and politician, 2nd Chief Minister of West Bengal (d. 1962)", "html": "1882 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Indian physician and politician, 2nd <a href=\"https://wikipedia.org/wiki/Chief_Minister_of_West_Bengal\" class=\"mw-redirect\" title=\"Chief Minister of West Bengal\">Chief Minister of West Bengal</a> (d. 1962)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Indian physician and politician, 2nd <a href=\"https://wikipedia.org/wiki/Chief_Minister_of_West_Bengal\" class=\"mw-redirect\" title=\"Chief Minister of West Bengal\">Chief Minister of West Bengal</a> (d. 1962)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>"}, {"title": "Chief Minister of West Bengal", "link": "https://wikipedia.org/wiki/Chief_Minister_of_West_Bengal"}]}, {"year": "1883", "text": "<PERSON>, English colonel, Victoria Cross recipient (d. 1933)", "html": "1883 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English colonel, <a href=\"https://wikipedia.org/wiki/Victoria_Cross\" title=\"Victoria Cross\">Victoria Cross</a> recipient (d. 1933)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English colonel, <a href=\"https://wikipedia.org/wiki/Victoria_Cross\" title=\"Victoria Cross\">Victoria Cross</a> recipient (d. 1933)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Victoria Cross", "link": "https://wikipedia.org/wiki/Victoria_Cross"}]}, {"year": "1885", "text": "<PERSON>, Australian author and poet (d. 1968)", "html": "1885 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian author and poet (d. 1968)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian author and poet (d. 1968)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1887", "text": "<PERSON>, New Zealand-English author and scholar (d. 1981)", "html": "1887 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, New Zealand-English author and scholar (d. 1981)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, New Zealand-English author and scholar (d. 1981)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1892", "text": "<PERSON>, American author and journalist (d. 1977)", "html": "1892 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author and journalist (d. 1977)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author and journalist (d. 1977)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1892", "text": "<PERSON><PERSON><PERSON><PERSON><PERSON>, Hungarian composer and conductor (d. 1963)", "html": "1892 - <a href=\"https://wikipedia.org/wiki/L%C3%A1szl%C3%B3_Lajtha\" title=\"<PERSON><PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON><PERSON></a>, Hungarian composer and conductor (d. 1963)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/L%C3%A1szl%C3%B3_Lajtha\" title=\"<PERSON><PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON><PERSON></a>, Hungarian composer and conductor (d. 1963)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/L%C3%A1szl%C3%B3_Lajtha"}]}, {"year": "1899", "text": "<PERSON>, American pianist and composer (d. 1993)", "html": "1899 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American pianist and composer (d. 1993)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American pianist and composer (d. 1993)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "1899", "text": "<PERSON>, English-American actor and director (d. 1962)", "html": "1899 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English-American actor and director (d. 1962)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English-American actor and director (d. 1962)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1899", "text": "<PERSON><PERSON>, Greek scholar and politician, President of Greece (d. 1987)", "html": "1899 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Greek scholar and politician, <a href=\"https://wikipedia.org/wiki/President_of_Greece\" title=\"President of Greece\">President of Greece</a> (d. 1987)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Greek scholar and politician, <a href=\"https://wikipedia.org/wiki/President_of_Greece\" title=\"President of Greece\">President of Greece</a> (d. 1987)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}, {"title": "President of Greece", "link": "https://wikipedia.org/wiki/President_of_Greece"}]}, {"year": "1901", "text": "<PERSON><PERSON>, American screenwriter (d. 1973)", "html": "1901 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American screenwriter (d. 1973)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American screenwriter (d. 1973)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1902", "text": "<PERSON>, French-American film director, producer and screenwriter (d. 1981)", "html": "1902 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French-American film director, producer and screenwriter (d. 1981)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French-American film director, producer and screenwriter (d. 1981)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1903", "text": "<PERSON>, English pilot (d. 1941)", "html": "1903 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English pilot (d. 1941)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English pilot (d. 1941)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1903", "text": "<PERSON><PERSON>, English actress (d. 1979)", "html": "1903 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, English actress (d. 1979)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, English actress (d. 1979)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1906", "text": "<PERSON>, French mathematician and academic (d. 1992)", "html": "1906 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A9\" title=\"<PERSON>\"><PERSON></a>, French mathematician and academic (d. 1992)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A9\" title=\"<PERSON>\"><PERSON></a>, French mathematician and academic (d. 1992)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A9"}]}, {"year": "1906", "text": "<PERSON><PERSON><PERSON>, American businesswoman, co-founder of Estée Lauder Companies (d. 2004)", "html": "1906 - <a href=\"https://wikipedia.org/wiki/Est%C3%A<PERSON><PERSON>_<PERSON><PERSON>_(businesswoman)\" title=\"<PERSON><PERSON><PERSON> (businesswoman)\"><PERSON><PERSON><PERSON></a>, American businesswoman, co-founder of <a href=\"https://wikipedia.org/wiki/Est%C3%A9e_Lauder_Companies\" class=\"mw-redirect\" title=\"Estée Lauder Companies\">Estée Lauder Companies</a> (d. 2004)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Est%C3%A<PERSON><PERSON>_<PERSON><PERSON>_(businesswoman)\" title=\"<PERSON><PERSON><PERSON> (businesswoman)\"><PERSON><PERSON><PERSON></a>, American businesswoman, co-founder of <a href=\"https://wikipedia.org/wiki/Est%C3%A9e_Lauder_Companies\" class=\"mw-redirect\" title=\"Estée Lauder Companies\">Estée Lauder Companies</a> (d. 2004)", "links": [{"title": "<PERSON><PERSON><PERSON> (businesswoman)", "link": "https://wikipedia.org/wiki/Est%C3%A<PERSON><PERSON>_<PERSON><PERSON>_(businesswoman)"}, {"title": "Estée Lauder Companies", "link": "https://wikipedia.org/wiki/Est%C3%A9e_Lauder_Companies"}]}, {"year": "1907", "text": "<PERSON>, Scottish-English biochemist and virologist (d. 1997)", "html": "1907 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish-English biochemist and virologist (d. 1997)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish-English biochemist and virologist (d. 1997)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1909", "text": "<PERSON><PERSON>, American sprinter (d. 1971)", "html": "1909 - <a href=\"https://wikipedia.org/wiki/Em<PERSON>_Toppino\" title=\"<PERSON><PERSON> Toppino\"><PERSON><PERSON></a>, American sprinter (d. 1971)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Em<PERSON>_Toppino\" title=\"<PERSON><PERSON> Toppino\"><PERSON><PERSON></a>, American sprinter (d. 1971)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Emmett_<PERSON>pino"}]}, {"year": "1910", "text": "<PERSON>, American hurdler (d. 1975)", "html": "1910 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American hurdler (d. 1975)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American hurdler (d. 1975)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1911", "text": "<PERSON>, Estonian landscape architect and artist (d. 1990)", "html": "1911 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Estonian landscape architect and artist (d. 1990)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Estonian landscape architect and artist (d. 1990)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1911", "text": "<PERSON>, Russian marshal and politician, Soviet Minister of Defence (d. 2012)", "html": "1911 - <a href=\"https://wikipedia.org/wiki/<PERSON>_(commander)\" class=\"mw-redirect\" title=\"<PERSON> (commander)\"><PERSON></a>, Russian marshal and politician, <a href=\"https://wikipedia.org/wiki/Minister_of_Defence_(Soviet_Union)\" title=\"Minister of Defence (Soviet Union)\">Soviet Minister of Defence</a> (d. 2012)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(commander)\" class=\"mw-redirect\" title=\"<PERSON> (commander)\"><PERSON></a>, Russian marshal and politician, <a href=\"https://wikipedia.org/wiki/Minister_of_Defence_(Soviet_Union)\" title=\"Minister of Defence (Soviet Union)\">Soviet Minister of Defence</a> (d. 2012)", "links": [{"title": "<PERSON> (commander)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(commander)"}, {"title": "Minister of Defence (Soviet Union)", "link": "https://wikipedia.org/wiki/Minister_of_Defence_(Soviet_Union)"}]}, {"year": "1912", "text": "<PERSON>, American environmentalist, founder of the Sierra Club Foundation (d. 2000)", "html": "1912 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American environmentalist, founder of the <a href=\"https://wikipedia.org/wiki/Sierra_Club_Foundation\" title=\"Sierra Club Foundation\">Sierra Club Foundation</a> (d. 2000)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American environmentalist, founder of the <a href=\"https://wikipedia.org/wiki/Sierra_Club_Foundation\" title=\"Sierra Club Foundation\">Sierra Club Foundation</a> (d. 2000)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Sierra Club Foundation", "link": "https://wikipedia.org/wiki/Sierra_Club_Foundation"}]}, {"year": "1912", "text": "<PERSON>, American journalist (d. 1989)", "html": "1912 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(editor)\" title=\"<PERSON> (editor)\"><PERSON></a>, American journalist (d. 1989)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(editor)\" title=\"<PERSON> (editor)\"><PERSON></a>, American journalist (d. 1989)", "links": [{"title": "<PERSON> (editor)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(editor)"}]}, {"year": "1913", "text": "<PERSON>, American baseball player (d. 1998)", "html": "1913 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(baseball)\" title=\"<PERSON> (baseball)\"><PERSON></a>, American baseball player (d. 1998)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(baseball)\" title=\"<PERSON> (baseball)\"><PERSON></a>, American baseball player (d. 1998)", "links": [{"title": "<PERSON> (baseball)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(baseball)"}]}, {"year": "1913", "text": "<PERSON>, American basketball player (d. 2004)", "html": "1913 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player (d. 2004)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player (d. 2004)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1913", "text": "<PERSON><PERSON><PERSON><PERSON><PERSON>, Indian politician, 3rd Chief Minister of Maharashtra (d. 1979)", "html": "1913 - <a href=\"https://wikipedia.org/wiki/Vasant<PERSON><PERSON>_<PERSON>\" title=\"Vasa<PERSON><PERSON><PERSON>\">V<PERSON><PERSON><PERSON><PERSON></a>, Indian politician, 3rd <a href=\"https://wikipedia.org/wiki/Chief_Minister_of_Maharashtra\" class=\"mw-redirect\" title=\"Chief Minister of Maharashtra\">Chief Minister of Maharashtra</a> (d. 1979)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Vasa<PERSON><PERSON><PERSON>_<PERSON>\" title=\"Vasa<PERSON><PERSON><PERSON>\">V<PERSON><PERSON><PERSON><PERSON></a>, Indian politician, 3rd <a href=\"https://wikipedia.org/wiki/Chief_Minister_of_Maharashtra\" class=\"mw-redirect\" title=\"Chief Minister of Maharashtra\">Chief Minister of Maharashtra</a> (d. 1979)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Vasantrao_Naik"}, {"title": "Chief Minister of Maharashtra", "link": "https://wikipedia.org/wiki/Chief_Minister_of_Maharashtra"}]}, {"year": "1914", "text": "<PERSON>, British Army officer (d. 2019)", "html": "1914 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(British_Army_officer,_born_1914)\" title=\"<PERSON> (British Army officer, born 1914)\"><PERSON></a>, British Army officer (d. 2019)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(British_Army_officer,_born_1914)\" title=\"<PERSON> (British Army officer, born 1914)\"><PERSON></a>, British Army officer (d. 2019)", "links": [{"title": "<PERSON> (British Army officer, born 1914)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(British_Army_officer,_born_1914)"}]}, {"year": "1914", "text": "<PERSON><PERSON>, German alpine skier (d. 2004)", "html": "1914 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, German alpine skier (d. 2004)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, German alpine skier (d. 2004)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1914", "text": "<PERSON>, American politician (d. 2016)", "html": "1914 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American politician (d. 2016)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American politician (d. 2016)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "1915", "text": "<PERSON>, American blues singer-songwriter, bass player, guitarist and producer (d. 1992)", "html": "1915 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American blues singer-songwriter, bass player, guitarist and producer (d. 1992)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American blues singer-songwriter, bass player, guitarist and producer (d. 1992)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1915", "text": "<PERSON>, 3rd Viscount <PERSON>, British peer (d. 2000)", "html": "1915 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_3rd_Viscount_<PERSON>\" title=\"<PERSON>, 3rd Viscount <PERSON>\"><PERSON>, 3rd Viscount <PERSON></a>, British peer (d. 2000)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>,_3rd_Viscount_<PERSON>\" title=\"<PERSON>, 3rd Viscount <PERSON>\"><PERSON>, 3rd Viscount <PERSON></a>, British peer (d. 2000)", "links": [{"title": "<PERSON>, 3rd Viscount <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>,_3rd_Viscount_<PERSON>"}]}, {"year": "1915", "text": "<PERSON>, American baseball pitcher (d. 1999)", "html": "1915 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball pitcher (d. 1999)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball pitcher (d. 1999)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>er"}]}, {"year": "1915", "text": "<PERSON>, American soldier and neurosurgeon (d. 2001)", "html": "1915 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American soldier and neurosurgeon (d. 2001)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American soldier and neurosurgeon (d. 2001)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1915", "text": "<PERSON><PERSON><PERSON><PERSON>, Vietnamese politician (d. 1998)", "html": "1915 - <a href=\"https://wikipedia.org/wiki/Nguy%E1%BB%85n_V%C4%83n_<PERSON><PERSON>\" title=\"<PERSON>uy<PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Vietnamese politician (d. 1998)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Nguy%E1%BB%85n_V%C4%83n_<PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Vietnamese politician (d. 1998)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Nguy%E1%BB%85n_V%C4%83n_Linh"}]}, {"year": "1916", "text": "<PERSON>, British-American actress (d. 2020) ", "html": "1916 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, British-American actress (d. 2020) ", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, British-American actress (d. 2020) ", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1916", "text": "<PERSON><PERSON><PERSON>, Ukrainian astronomer and astrophysicist (d. 1985)", "html": "1916 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Ukrainian astronomer and astrophysicist (d. 1985)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Ukrainian astronomer and astrophysicist (d. 1985)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1916", "text": "<PERSON>, American director and producer (d. 2012)", "html": "1916 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American director and producer (d. 2012)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American director and producer (d. 2012)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "1917", "text": "<PERSON><PERSON><PERSON>, Spanish aristocrat (d. 2005)", "html": "1917 - <a href=\"https://wikipedia.org/wiki/%C3%<PERSON><PERSON><PERSON>_<PERSON>_y_D%C3%ADez\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Spanish aristocrat (d. 2005)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/%C3%<PERSON><PERSON><PERSON>_<PERSON>_y_D%C3%ADez\" title=\"<PERSON><PERSON><PERSON> y <PERSON>\"><PERSON><PERSON><PERSON></a>, Spanish aristocrat (d. 2005)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/%C3%81<PERSON><PERSON>_<PERSON>_y_D%C3%ADez"}]}, {"year": "1918", "text": "<PERSON>, American singer and actor (d. 2008)", "html": "1918 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>(singer)\" title=\"<PERSON> (singer)\"><PERSON></a>, American singer and actor (d. 2008)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>(singer)\" title=\"<PERSON> (singer)\"><PERSON></a>, American singer and actor (d. 2008)", "links": [{"title": "<PERSON> (singer)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>(singer)"}]}, {"year": "1918", "text": "<PERSON>, South African writer and public speaker (d. 2005)", "html": "1918 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, South African writer and public speaker (d. 2005)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, South African writer and public speaker (d. 2005)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1918", "text": "<PERSON>, Filipino lawyer (d. 2003)", "html": "1918 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Filipino lawyer (d. 2003)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Filipino lawyer (d. 2003)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1919", "text": "<PERSON>, Estonian colonel (d. 2009)", "html": "1919 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Estonian colonel (d. 2009)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Estonian colonel (d. 2009)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1919", "text": "<PERSON>, Iraqi politician (d. 2021)", "html": "1919 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Iraqi politician (d. 2021)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Iraqi politician (d. 2021)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>-<PERSON>"}]}, {"year": "1919", "text": "<PERSON>, American vice admiral (d. 2014)", "html": "1919 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American vice admiral (d. 2014)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American vice admiral (d. 2014)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1920", "text": "<PERSON>, French historian and journalist (d. 2007)", "html": "1920 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French historian and journalist (d. 2007)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French historian and journalist (d. 2007)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1920", "text": "<PERSON>, Japanese-American wrestler and actor (d. 1982)", "html": "1920 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Japanese-American wrestler and actor (d. 1982)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Japanese-American wrestler and actor (d. 1982)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1920", "text": "<PERSON>, American-Japanese chemist (d. 2023)", "html": "1920 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American-Japanese chemist (d. 2023)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American-Japanese chemist (d. 2023)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "1921", "text": "<PERSON><PERSON><PERSON>, Batswana lawyer and politician, 1st President of Botswana (d. 1980)", "html": "1921 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Batswana lawyer and politician, 1st <a href=\"https://wikipedia.org/wiki/President_of_Botswana\" title=\"President of Botswana\">President of Botswana</a> (d. 1980)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Batswana lawyer and politician, 1st <a href=\"https://wikipedia.org/wiki/President_of_Botswana\" title=\"President of Botswana\">President of Botswana</a> (d. 1980)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>a"}, {"title": "President of Botswana", "link": "https://wikipedia.org/wiki/President_of_Botswana"}]}, {"year": "1921", "text": "<PERSON><PERSON><PERSON>, Polish gynecologist and sexologist (d. 2005)", "html": "1921 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_Wis%C5%82ocka\" title=\"<PERSON><PERSON><PERSON> Wisłocka\"><PERSON><PERSON><PERSON></a>, Polish gynecologist and sexologist (d. 2005)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_Wis%C5%82ocka\" title=\"<PERSON><PERSON><PERSON> W<PERSON>łocka\"><PERSON><PERSON><PERSON></a>, Polish gynecologist and sexologist (d. 2005)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_Wis%C5%82ocka"}]}, {"year": "1921", "text": "<PERSON>, Canadian canoeist (d. 2003)", "html": "1921 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(canoeist)\" title=\"<PERSON> (canoeist)\"><PERSON></a>, Canadian canoeist (d. 2003)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(canoeist)\" title=\"<PERSON> (canoeist)\"><PERSON></a>, Canadian canoeist (d. 2003)", "links": [{"title": "<PERSON> (canoeist)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(canoeist)"}]}, {"year": "1922", "text": "<PERSON><PERSON>, German-American activist, co-founder of the Clearwater Festival (d. 2013)", "html": "1922 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_Seeger\" title=\"<PERSON><PERSON> Seeger\"><PERSON><PERSON></a>, German-American activist, co-founder of the <a href=\"https://wikipedia.org/wiki/Clearwater_Festival\" title=\"Clearwater Festival\">Clearwater Festival</a> (d. 2013)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_Seeger\" title=\"<PERSON><PERSON> Seeger\"><PERSON><PERSON></a>, German-American activist, co-founder of the <a href=\"https://wikipedia.org/wiki/Clearwater_Festival\" title=\"Clearwater Festival\">Clearwater Festival</a> (d. 2013)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_Seeger"}, {"title": "Clearwater Festival", "link": "https://wikipedia.org/wiki/Clearwater_Festival"}]}, {"year": "1922", "text": "<PERSON><PERSON><PERSON><PERSON>, Israeli politician (d. 2023)", "html": "1922 - <a href=\"https://wikipedia.org/wiki/Mordechai_Bibi\" title=\"Mordechai Bibi\"><PERSON><PERSON><PERSON><PERSON></a>, Israeli politician (d. 2023)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Mordechai_Bibi\" title=\"Mordechai Bibi\"><PERSON><PERSON><PERSON><PERSON></a>, Israeli politician (d. 2023)", "links": [{"title": "Mordechai Bibi", "link": "https://wikipedia.org/wiki/Mordechai_Bibi"}]}, {"year": "1923", "text": "<PERSON><PERSON>, American marine, author and pimp (d. 2019)", "html": "1923 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American marine, author and pimp (d. 2019)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American marine, author and pimp (d. 2019)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1924", "text": "<PERSON><PERSON>, Spanish footballer and manager (d. 2013)", "html": "1924 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Spanish footballer and manager (d. 2013)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Spanish footballer and manager (d. 2013)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1924", "text": "<PERSON>, American actress (d. 2003)", "html": "1924 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress (d. 2003)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Stanley\"><PERSON></a>, American actress (d. 2003)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Florence_Stanley"}]}, {"year": "1924", "text": "<PERSON>, French actor (d. 2011)", "html": "1924 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A8re\" title=\"<PERSON>\"><PERSON></a>, French actor (d. 2011)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A8re\" title=\"<PERSON>\"><PERSON></a>, French actor (d. 2011)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Georges_Rivi%C3%A8re"}]}, {"year": "1925", "text": "<PERSON><PERSON>, American actor (d. 2011)", "html": "1925 - <a href=\"https://wikipedia.org/wiki/Far<PERSON>_Granger\" title=\"Farley Granger\"><PERSON><PERSON></a>, American actor (d. 2011)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Far<PERSON>_Granger\" title=\"Farley Granger\"><PERSON><PERSON></a>, American actor (d. 2011)", "links": [{"title": "Farley Granger", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>r"}]}, {"year": "1925", "text": "<PERSON>, American football referee (d. 2023)", "html": "1925 - <a href=\"https://wikipedia.org/wiki/<PERSON>_McNally\" title=\"<PERSON> McNally\"><PERSON></a>, American football referee (d. 2023)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_McNally\" title=\"<PERSON> McNally\"><PERSON></a>, American football referee (d. 2023)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Art_McNally"}]}, {"year": "1926", "text": "<PERSON>, American economist and academic, Nobel Prize laureate (d. 2013)", "html": "1926 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American economist and academic, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Economics\" class=\"mw-redirect\" title=\"Nobel Prize in Economics\">Nobel Prize</a> laureate (d. 2013)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American economist and academic, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Economics\" class=\"mw-redirect\" title=\"Nobel Prize in Economics\">Nobel Prize</a> laureate (d. 2013)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Nobel Prize in Economics", "link": "https://wikipedia.org/wiki/Nobel_Prize_in_Economics"}]}, {"year": "1926", "text": "<PERSON>, German businessman (d. 2023)", "html": "1926 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German businessman (d. 2023)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German businessman (d. 2023)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1926", "text": "<PERSON>, Somali general (d. 2017)", "html": "1926 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Somali general (d. 2017)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Somali general (d. 2017)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1926", "text": "<PERSON>, German composer and educator (d. 2012)", "html": "1926 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German composer and educator (d. 2012)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German composer and educator (d. 2012)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1927", "text": "<PERSON>, English paleontologist and author (d. 1997)", "html": "1927 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English paleontologist and author (d. 1997)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English paleontologist and author (d. 1997)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1927", "text": "<PERSON>, American politician, 43rd Governor of Tennessee (d. 2024)", "html": "1927 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American politician, 43rd <a href=\"https://wikipedia.org/wiki/Governor_of_Tennessee\" title=\"Governor of Tennessee\">Governor of Tennessee</a> (d. 2024)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American politician, 43rd <a href=\"https://wikipedia.org/wiki/Governor_of_Tennessee\" title=\"Governor of Tennessee\">Governor of Tennessee</a> (d. 2024)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Governor of Tennessee", "link": "https://wikipedia.org/wiki/Governor_of_Tennessee"}]}, {"year": "1927", "text": "<PERSON>, American bishop", "html": "1927 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American bishop", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American bishop", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1927", "text": "<PERSON>, 8th Prime Minister of India (d. 2007)", "html": "1927 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, 8th Prime Minister of India (d. 2007)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, 8th Prime Minister of India (d. 2007)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1929", "text": "<PERSON>, American biologist and  immunologist, Nobel Prize laureate (d. 2014)", "html": "1929 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American biologist and immunologist, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Physiology_or_Medicine\" title=\"Nobel Prize in Physiology or Medicine\">Nobel Prize</a> laureate (d. 2014)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American biologist and immunologist, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Physiology_or_Medicine\" title=\"Nobel Prize in Physiology or Medicine\">Nobel Prize</a> laureate (d. 2014)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}, {"title": "Nobel Prize in Physiology or Medicine", "link": "https://wikipedia.org/wiki/Nobel_Prize_in_Physiology_or_Medicine"}]}, {"year": "1930", "text": "<PERSON><PERSON><PERSON><PERSON>, Syrian-American director and producer (d. 2005)", "html": "1930 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Syrian-American director and producer (d. 2005)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Syrian-American director and producer (d. 2005)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>ust<PERSON><PERSON>_<PERSON>d"}]}, {"year": "1930", "text": "<PERSON>, American linguist and academic (d. 2008)", "html": "1930 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American linguist and academic (d. 2008)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American linguist and academic (d. 2008)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1931", "text": "<PERSON>, French actress and dancer", "html": "1931 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French actress and dancer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French actress and dancer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1932", "text": "<PERSON><PERSON><PERSON><PERSON><PERSON>, French-Israeli journalist and author (d. 2007)", "html": "1932 - <a href=\"https://wikipedia.org/wiki/Ze%27ev_Schiff\" title=\"Z<PERSON>'ev Schiff\"><PERSON><PERSON>'ev <PERSON></a>, French-Israeli journalist and author (d. 2007)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Ze%27ev_Schiff\" title=\"<PERSON><PERSON>'ev Schiff\"><PERSON><PERSON>'ev <PERSON></a>, French-Israeli journalist and author (d. 2007)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Ze%27ev_Schiff"}]}, {"year": "1933", "text": "<PERSON><PERSON>, American anthropologist and academic (d. 2010)", "html": "1933 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American anthropologist and academic (d. 2010)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American anthropologist and academic (d. 2010)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1934", "text": "<PERSON>, French actor, director and screenwriter (d. 2009)", "html": "1934 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French actor, director and screenwriter (d. 2009)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French actor, director and screenwriter (d. 2009)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1934", "text": "<PERSON>, American actor", "html": "1934 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1934", "text": "<PERSON>, English actress and screenwriter", "html": "1934 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actress and screenwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actress and screenwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1934", "text": "<PERSON>, American actor, director and producer (d. 2008)", "html": "1934 - <a href=\"https://wikipedia.org/wiki/Sydney_Pollack\" title=\"Sydney Pollack\"><PERSON>ack</a>, American actor, director and producer (d. 2008)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Sydney_Pollack\" title=\"Sydney Pollack\"><PERSON> Pollack</a>, American actor, director and producer (d. 2008)", "links": [{"title": "<PERSON>ack", "link": "https://wikipedia.org/wiki/Sydney_Pollack"}]}, {"year": "1935", "text": "<PERSON>, American singer-songwriter and harmonica player (d. 2017)", "html": "1935 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and harmonica player (d. 2017)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and harmonica player (d. 2017)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1935", "text": "<PERSON>, English actor (d. 2020)", "html": "1935 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actor (d. 2020)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actor (d. 2020)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1936", "text": "<PERSON>, American entrepreneur, founder of <PERSON> (d. 2024)", "html": "1936 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American entrepreneur, founder of <a href=\"https://wikipedia.org/wiki/<PERSON>_Amos\" title=\"<PERSON> Amos\"><PERSON></a> (d. 2024)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American entrepreneur, founder of <a href=\"https://wikipedia.org/wiki/<PERSON>_Amos\" title=\"<PERSON> Amos\"><PERSON></a> (d. 2024)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Famous Amos", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1938", "text": "<PERSON>, American baseball player and coach", "html": "1938 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(right-handed_pitcher)\" class=\"mw-redirect\" title=\"<PERSON> (right-handed pitcher)\"><PERSON></a>, American baseball player and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(right-handed_pitcher)\" class=\"mw-redirect\" title=\"<PERSON> (right-handed pitcher)\"><PERSON></a>, American baseball player and coach", "links": [{"title": "<PERSON> (right-handed pitcher)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(right-handed_pitcher)"}]}, {"year": "1938", "text": "<PERSON><PERSON><PERSON>, Indian flute player and composer", "html": "1938 - <a href=\"https://wikipedia.org/wiki/Hari<PERSON><PERSON>_Chaurasia\" title=\"Hariprasad Chaurasia\"><PERSON><PERSON><PERSON> Cha<PERSON></a>, Indian flute player and composer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_Cha<PERSON>\" title=\"Hariprasad Chaurasia\"><PERSON><PERSON><PERSON></a>, Indian flute player and composer", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_Cha<PERSON>"}]}, {"year": "1939", "text": "<PERSON>, American actress (d. 2013)", "html": "1939 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress (d. 2013)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress (d. 2013)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1939", "text": "<PERSON>, American singer-songwriter, guitarist and producer (d. 2008)", "html": "1939 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter, guitarist and producer (d. 2008)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter, guitarist and producer (d. 2008)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1940", "text": "<PERSON>, Scottish footballer and manager (d. 2023)", "html": "1940 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(footballer,_born_1940)\" title=\"<PERSON> (footballer, born 1940)\"><PERSON></a>, Scottish footballer and manager (d. 2023)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(footballer,_born_1940)\" title=\"<PERSON> (footballer, born 1940)\"><PERSON></a>, Scottish footballer and manager (d. 2023)", "links": [{"title": "<PERSON> (footballer, born 1940)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(footballer,_born_1940)"}]}, {"year": "1940", "text": "<PERSON><PERSON>, South African activist and politician", "html": "1940 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, South African activist and politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, South African activist and politician", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1940", "text": "<PERSON><PERSON><PERSON>, Turkish poet and author (d. 1987)", "html": "1940 - <a href=\"https://wikipedia.org/wiki/Cahit_Zarifo%C4%9Flu\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Turkish poet and author (d. 1987)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Cahit_Zarifo%C4%9Flu\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Turkish poet and author (d. 1987)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Cahit_Zarifo%C4%9Flu"}]}, {"year": "1941", "text": "<PERSON>, Canadian-American ice hockey player (d. 2021)", "html": "1941 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian-American ice hockey player (d. 2021)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian-American ice hockey player (d. 2021)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1941", "text": "<PERSON>, American pharmacologist and biochemist, Nobel Prize laureate (d. 2015)", "html": "1941 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American pharmacologist and biochemist, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Physiology_or_Medicine\" title=\"Nobel Prize in Physiology or Medicine\">Nobel Prize</a> laureate (d. 2015)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American pharmacologist and biochemist, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Physiology_or_Medicine\" title=\"Nobel Prize in Physiology or Medicine\">Nobel Prize</a> laureate (d. 2015)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "Nobel Prize in Physiology or Medicine", "link": "https://wikipedia.org/wiki/Nobel_Prize_in_Physiology_or_Medicine"}]}, {"year": "1941", "text": "<PERSON><PERSON>, Romanian linguist and philologist", "html": "1941 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Romanian linguist and philologist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Romanian linguist and philologist", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1941", "text": "<PERSON><PERSON>, Canadian-American economist and academic, Nobel Prize laureate", "html": "1941 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Canadian-American economist and academic, <a href=\"https://wikipedia.org/wiki/Nobel_Memorial_Prize_in_Economic_Sciences\" title=\"Nobel Memorial Prize in Economic Sciences\">Nobel Prize</a> laureate", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Canadian-American economist and academic, <a href=\"https://wikipedia.org/wiki/Nobel_Memorial_Prize_in_Economic_Sciences\" title=\"Nobel Memorial Prize in Economic Sciences\">Nobel Prize</a> laureate", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}, {"title": "Nobel Memorial Prize in Economic Sciences", "link": "https://wikipedia.org/wiki/Nobel_Memorial_Prize_in_Economic_Sciences"}]}, {"year": "1941", "text": "<PERSON><PERSON><PERSON>, American dancer and choreographer", "html": "1941 - <a href=\"https://wikipedia.org/wiki/Twyla_Tharp\" title=\"Twy<PERSON> Tharp\"><PERSON><PERSON><PERSON>harp</a>, American dancer and choreographer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Twyla_Tharp\" title=\"Twyla Tharp\"><PERSON><PERSON><PERSON>harp</a>, American dancer and choreographer", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Twyla_Tharp"}]}, {"year": "1942", "text": "<PERSON><PERSON><PERSON>, Iraqi field marshal and politician (d. 2020)", "html": "1942 - <a href=\"https://wikipedia.org/wiki/I<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Iraqi field marshal and politician (d. 2020)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/I<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Iraqi field marshal and politician (d. 2020)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1942", "text": "<PERSON><PERSON><PERSON><PERSON>, Canadian actress", "html": "1942 - <a href=\"https://wikipedia.org/wiki/Genevi%C3%A8ve_Bujold\" title=\"<PERSON><PERSON><PERSON><PERSON> Bu<PERSON>ld\"><PERSON><PERSON><PERSON><PERSON></a>, Canadian actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Genevi%C3%A8ve_Bujold\" title=\"<PERSON>vi<PERSON><PERSON> Bujold\"><PERSON><PERSON><PERSON><PERSON></a>, Canadian actress", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Genevi%C3%A8ve_<PERSON><PERSON><PERSON>ld"}]}, {"year": "1942", "text": "<PERSON><PERSON><PERSON>, American singer-songwriter, producer and pastor (d. 2015)", "html": "1942 - <a href=\"https://wikipedia.org/wiki/Andra%C3%A9_<PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American singer-songwriter, producer and pastor (d. 2015)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Andra%C3%A9_<PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American singer-songwriter, producer and pastor (d. 2015)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Andra%C3%A9_Crouch"}]}, {"year": "1942", "text": "<PERSON>, English chemist and academic", "html": "1942 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English chemist and academic", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English chemist and academic", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1943", "text": "<PERSON>, American conductor and organist", "html": "1943 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American conductor and organist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American conductor and organist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1943", "text": "<PERSON><PERSON><PERSON>, Estonian politician, 37th Mayor of Tallinn", "html": "1943 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Estonian politician, 37th <a href=\"https://wikipedia.org/wiki/Mayor_of_Tallinn\" class=\"mw-redirect\" title=\"Mayor of Tallinn\">Mayor of Tallinn</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Estonian politician, 37th <a href=\"https://wikipedia.org/wiki/Mayor_of_Tallinn\" class=\"mw-redirect\" title=\"Mayor of Tallinn\">Mayor of Tallinn</a>", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}, {"title": "Mayor of Tallinn", "link": "https://wikipedia.org/wiki/Mayor_of_Tallinn"}]}, {"year": "1943", "text": "<PERSON>, American composer, musician and lyricist", "html": "1943 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American composer, musician and lyricist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American composer, musician and lyricist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1944", "text": "<PERSON><PERSON><PERSON>, Bangladeshi professor and writer (d. 2021)", "html": "1944 - <a href=\"https://wikipedia.org/wiki/Nuru<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>uru<PERSON>\"><PERSON><PERSON><PERSON></a>, Bangladeshi professor and writer (d. 2021)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Bangladeshi professor and writer (d. 2021)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Nuru<PERSON>_<PERSON><PERSON>_<PERSON>h"}]}, {"year": "1945", "text": "<PERSON>, American actor and singer", "html": "1945 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor and singer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor and singer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1945", "text": "<PERSON>, American singer-songwriter and actress", "html": "1945 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and actress", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1946", "text": "<PERSON>, English archaeologist and academic (d. 2013)", "html": "1946 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Mick <PERSON>\"><PERSON></a>, English archaeologist and academic (d. 2013)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Mick_Aston\" title=\"Mick Aston\"><PERSON></a>, English archaeologist and academic (d. 2013)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1946", "text": "<PERSON><PERSON><PERSON>, Finnish sergeant and politician, Finnish Minister for Foreign Affairs", "html": "1946 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Finnish sergeant and politician, <a href=\"https://wikipedia.org/wiki/Minister_for_Foreign_Affairs_(Finland)\" title=\"Minister for Foreign Affairs (Finland)\">Finnish Minister for Foreign Affairs</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Finnish sergeant and politician, <a href=\"https://wikipedia.org/wiki/Minister_for_Foreign_Affairs_(Finland)\" title=\"Minister for Foreign Affairs (Finland)\">Finnish Minister for Foreign Affairs</a>", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}, {"title": "Minister for Foreign Affairs (Finland)", "link": "https://wikipedia.org/wiki/Minister_for_Foreign_Affairs_(Finland)"}]}, {"year": "1946", "text": "<PERSON><PERSON>, Ghanaian novelist and poet (d. 2017)", "html": "1946 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"Ko<PERSON> Laing\"><PERSON><PERSON></a>, Ghanaian novelist and poet (d. 2017)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> Laing\"><PERSON><PERSON></a>, Ghanaian novelist and poet (d. 2017)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>ng"}]}, {"year": "1947", "text": "<PERSON><PERSON><PERSON>, Japanese race car driver", "html": "1947 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese race car driver", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese race car driver", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1947", "text": "<PERSON>, English academic and politician (d. 2012)", "html": "1947 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English academic and politician (d. 2012)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English academic and politician (d. 2012)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1948", "text": "<PERSON>, English-American singer-songwriter and guitarist", "html": "1948 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(musician)\" title=\"<PERSON> (musician)\"><PERSON></a>, English-American singer-songwriter and guitarist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(musician)\" title=\"<PERSON> (musician)\"><PERSON></a>, English-American singer-songwriter and guitarist", "links": [{"title": "<PERSON> (musician)", "link": "https://wikipedia.org/wiki/<PERSON>_(musician)"}]}, {"year": "1949", "text": "<PERSON><PERSON><PERSON><PERSON>, Tunisian-Belgian director and screenwriter", "html": "1949 - <a href=\"https://wikipedia.org/wiki/N%C3%A9<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Tunisian-Belgian director and screenwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/N%C3%A<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Tunisian-Belgian director and screenwriter", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/N%C3%A9<PERSON><PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1949", "text": "<PERSON>, English-Australian singer-songwriter", "html": "1949 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English-Australian singer-songwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English-Australian singer-songwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1949", "text": "<PERSON>, American composer and educator (d. 1996)", "html": "1949 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(composer)\" title=\"<PERSON> (composer)\"><PERSON></a>, American composer and educator (d. 1996)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>(composer)\" title=\"<PERSON> (composer)\"><PERSON></a>, American composer and educator (d. 1996)", "links": [{"title": "<PERSON> (composer)", "link": "https://wikipedia.org/wiki/<PERSON>(composer)"}]}, {"year": "1949", "text": "<PERSON><PERSON><PERSON><PERSON>, Indian lawyer and politician", "html": "1949 - <a href=\"https://wikipedia.org/wiki/Venkai<PERSON>_Naidu\" title=\"Venkaiah Naidu\"><PERSON><PERSON><PERSON><PERSON></a>, Indian lawyer and politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Venkai<PERSON>_Naidu\" title=\"Venkai<PERSON> Naidu\"><PERSON><PERSON><PERSON><PERSON></a>, Indian lawyer and politician", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Venkaiah_Naidu"}]}, {"year": "1950", "text": "<PERSON>, American white supremacist, politician and Ku Klux Klan Grand Wizard", "html": "1950 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American white supremacist, politician and <a href=\"https://wikipedia.org/wiki/Ku_Klux_Klan\" title=\"Ku Klux Klan\">Ku Klux Klan</a> <a href=\"https://wikipedia.org/wiki/Grand_Wizard\" title=\"Grand Wizard\">Grand Wizard</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American white supremacist, politician and <a href=\"https://wikipedia.org/wiki/Ku_Klux_Klan\" title=\"Ku Klux Klan\">Ku Klux Klan</a> <a href=\"https://wikipedia.org/wiki/Grand_Wizard\" title=\"Grand Wizard\">Grand Wizard</a>", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Ku Klux Klan", "link": "https://wikipedia.org/wiki/Ku_Klux_Klan"}, {"title": "Grand Wizard", "link": "https://wikipedia.org/wiki/<PERSON>_Wizard"}]}, {"year": "1951", "text": "<PERSON>, English actor and producer", "html": "1951 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Eve\"><PERSON></a>, English actor and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Eve\"><PERSON></a>, English actor and producer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1951", "text": "<PERSON>, American singer-songwriter and activist (d. 2021)", "html": "1951 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and activist (d. 2021)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and activist (d. 2021)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1951", "text": "<PERSON>, English physicist and academic", "html": "1951 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English physicist and academic", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English physicist and academic", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>fellow"}]}, {"year": "1951", "text": "<PERSON><PERSON><PERSON>, German runner", "html": "1951 - <a href=\"https://wikipedia.org/wiki/<PERSON>-<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, German runner", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, German runner", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>-<PERSON>"}]}, {"year": "1951", "text": "<PERSON>, American basketball player", "html": "1951 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1951", "text": "<PERSON><PERSON>, American actor, singer and dancer", "html": "1951 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American actor, singer and dancer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American actor, singer and dancer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1951", "text": "<PERSON>, American singer-songwriter and keyboard player", "html": "1951 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and keyboard player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and keyboard player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1951", "text": "<PERSON>, American singer-songwriter, pianist and actor", "html": "1951 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter, pianist and actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter, pianist and actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1952", "text": "<PERSON>, Canadian actor, producer and screenwriter", "html": "1952 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian actor, producer and screenwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian actor, producer and screenwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1952", "text": "<PERSON>, American composer and performer", "html": "1952 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American composer and performer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American composer and performer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1952", "text": "<PERSON>, English oncologist and academic", "html": "1952 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(oncologist)\" class=\"mw-redirect\" title=\"<PERSON> (oncologist)\"><PERSON></a>, English oncologist and academic", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(oncologist)\" class=\"mw-redirect\" title=\"<PERSON> (oncologist)\"><PERSON></a>, English oncologist and academic", "links": [{"title": "<PERSON> (oncologist)", "link": "https://wikipedia.org/wiki/<PERSON>_(oncologist)"}]}, {"year": "1952", "text": "<PERSON>, Canadian ice hockey player and sportscaster", "html": "1952 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player and sportscaster", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player and sportscaster", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1952", "text": "<PERSON>, American pianist and composer (d. 2006)", "html": "1952 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American pianist and composer (d. 2006)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American pianist and composer (d. 2006)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1953", "text": "<PERSON>, Maltese lawyer and politician, 12th Prime Minister of Malta", "html": "1953 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Maltese lawyer and politician, 12th <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Malta\" title=\"Prime Minister of Malta\">Prime Minister of Malta</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Maltese lawyer and politician, 12th <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Malta\" title=\"Prime Minister of Malta\">Prime Minister of Malta</a>", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Prime Minister of Malta", "link": "https://wikipedia.org/wiki/Prime_Minister_of_Malta"}]}, {"year": "1953", "text": "<PERSON>, American football player", "html": "1953 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(cornerback)\" title=\"<PERSON> (cornerback)\"><PERSON></a>, American football player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(cornerback)\" title=\"<PERSON> (cornerback)\"><PERSON></a>, American football player", "links": [{"title": "<PERSON> (cornerback)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(cornerback)"}]}, {"year": "1953", "text": "<PERSON><PERSON><PERSON><PERSON>, Croatian journalist and politician, 9th Prime Minister of Croatia", "html": "1953 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Croatian journalist and politician, 9th <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Croatia\" title=\"Prime Minister of Croatia\">Prime Minister of Croatia</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Croatian journalist and politician, 9th <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Croatia\" title=\"Prime Minister of Croatia\">Prime Minister of Croatia</a>", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>"}, {"title": "Prime Minister of Croatia", "link": "https://wikipedia.org/wiki/Prime_Minister_of_Croatia"}]}, {"year": "1954", "text": "<PERSON>, American singer and guitarist (d. 1989)", "html": "1954 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer and guitarist (d. 1989)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer and guitarist (d. 1989)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1954", "text": "<PERSON><PERSON><PERSON>, Iranian artist and director", "html": "1954 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Iranian artist and director", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Iranian artist and director", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1955", "text": "<PERSON>, Russian pianist and educator", "html": "1955 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian pianist and educator", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian pianist and educator", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1955", "text": "<PERSON>, Chinese economist and politician, 7th Premier of the People's Republic of China (d. 2023)", "html": "1955 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Chinese economist and politician, 7th <a href=\"https://wikipedia.org/wiki/Premier_of_the_People%27s_Republic_of_China\" class=\"mw-redirect\" title=\"Premier of the People's Republic of China\">Premier of the People's Republic of China</a> (d. 2023)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Chinese economist and politician, 7th <a href=\"https://wikipedia.org/wiki/Premier_of_the_People%27s_Republic_of_China\" class=\"mw-redirect\" title=\"Premier of the People's Republic of China\">Premier of the People's Republic of China</a> (d. 2023)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Premier of the People's Republic of China", "link": "https://wikipedia.org/wiki/Premier_of_the_People%27s_Republic_of_China"}]}, {"year": "1955", "text": "<PERSON>, American lawyer and author", "html": "1955 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and author", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and author", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1955", "text": "<PERSON><PERSON><PERSON><PERSON>, Tongan politician and military officer, Deputy Prime Minister (d. 2021)", "html": "1955 - <a href=\"https://wikipedia.org/wiki/Ma%CA%BBafu_Tukui%CA%BBaulahi\" title=\"<PERSON>ʻ<PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Tongan politician and military officer, Deputy Prime Minister (d. 2021)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Ma%CA%BBafu_Tukui%CA%BBaulahi\" title=\"<PERSON>ʻ<PERSON><PERSON>aul<PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Tongan politician and military officer, Deputy Prime Minister (d. 2021)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Ma%CA%BBafu_Tukui%CA%BBaulahi"}]}, {"year": "1956", "text": "<PERSON>, American actor", "html": "1956 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1957", "text": "<PERSON>, American actress and producer (d. 2010)", "html": "1957 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress and producer (d. 2010)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress and producer (d. 2010)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1957", "text": "<PERSON><PERSON>, Finnish ice hockey player", "html": "1957 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>i\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Finnish ice hockey player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Finnish ice hockey player", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Hannu_Kamppuri"}]}, {"year": "1957", "text": "<PERSON>, English footballer and manager", "html": "1957 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%27Driscoll\" title=\"<PERSON>\"><PERSON></a>, English footballer and manager", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%27Driscoll\" title=\"<PERSON>\"><PERSON></a>, English footballer and manager", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>%27Driscoll"}]}, {"year": "1958", "text": "<PERSON>, American diplomat, United States Deputy National Security Advisor", "html": "1958 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_II\" title=\"<PERSON> II\"><PERSON> II</a>, American diplomat, <a href=\"https://wikipedia.org/wiki/Deputy_National_Security_Advisor_(United_States)\" class=\"mw-redirect\" title=\"Deputy National Security Advisor (United States)\">United States Deputy National Security Advisor</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON> II\"><PERSON> II</a>, American diplomat, <a href=\"https://wikipedia.org/wiki/Deputy_National_Security_Advisor_(United_States)\" class=\"mw-redirect\" title=\"Deputy National Security Advisor (United States)\">United States Deputy National Security Advisor</a>", "links": [{"title": "<PERSON> II", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_II"}, {"title": "Deputy National Security Advisor (United States)", "link": "https://wikipedia.org/wiki/Deputy_National_Security_Advisor_(United_States)"}]}, {"year": "1960", "text": "<PERSON>, Australian rugby league player and coach", "html": "1960 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(rugby_league)\" title=\"<PERSON> (rugby league)\"><PERSON></a>, Australian rugby league player and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(rugby_league)\" title=\"<PERSON> (rugby league)\"><PERSON></a>, Australian rugby league player and coach", "links": [{"title": "<PERSON> (rugby league)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(rugby_league)"}]}, {"year": "1960", "text": "<PERSON>, American runner", "html": "1960 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American runner", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American runner", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1960", "text": "<PERSON> \"<PERSON>\" <PERSON>, American soul/disco singer", "html": "1960 - <a href=\"https://wikipedia.org/wiki/<PERSON>_%22Champagne%22_King\" title='<PERSON> \"Champagne\" King'><PERSON> \"<PERSON>\" King</a>, American soul/disco singer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_%22Champagne%22_King\" title='<PERSON> \"Champagne\" King'><PERSON> \"Champagne\" King</a>, American soul/disco singer", "links": [{"title": "<PERSON> \"Champagne\" King", "link": "https://wikipedia.org/wiki/<PERSON>_%22Champagne%22_King"}]}, {"year": "1960", "text": "<PERSON>, American rugby player", "html": "1960 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American rugby player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"Kevin <PERSON>\"><PERSON></a>, American rugby player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1961", "text": "<PERSON>, English cyclist", "html": "1961 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English cyclist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English cyclist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1961", "text": "<PERSON>, English actor", "html": "1961 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1961", "text": "<PERSON>, American long jumper and runner", "html": "1961 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American long jumper and runner", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American long jumper and runner", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1961", "text": "<PERSON>, Princess of Wales (d. 1997)", "html": "1961 - <a href=\"https://wikipedia.org/wiki/<PERSON>,_Princess_of_Wales\" title=\"<PERSON>, Princess of Wales\"><PERSON>, Princess of Wales</a> (d. 1997)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>,_Princess_of_Wales\" title=\"<PERSON>, Princess of Wales\"><PERSON>, Princess of Wales</a> (d. 1997)", "links": [{"title": "<PERSON>, Princess of Wales", "link": "https://wikipedia.org/wiki/<PERSON>,_Princess_of_Wales"}]}, {"year": "1961", "text": "<PERSON>, Canadian singer-songwriter and guitarist", "html": "1961 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian singer-songwriter and guitarist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian singer-songwriter and guitarist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1962", "text": "<PERSON>, American actor (d. 2023)", "html": "1962 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor (d. 2023)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor (d. 2023)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1962", "text": "<PERSON><PERSON><PERSON><PERSON>, Malaysian businessman", "html": "1962 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Malaysian businessman", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Malaysian businessman", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON><PERSON>"}]}, {"year": "1963", "text": "<PERSON><PERSON>, American singer and keyboard player", "html": "1963 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American singer and keyboard player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American singer and keyboard player", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>um"}]}, {"year": "1963", "text": "<PERSON>, Australian actor", "html": "1963 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1963", "text": "<PERSON>, American lawyer and environmentalist (d. 2006)", "html": "1963 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(environmental_campaigner)\" title=\"<PERSON> (environmental campaigner)\"><PERSON></a>, American lawyer and environmentalist (d. 2006)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(environmental_campaigner)\" title=\"<PERSON> (environmental campaigner)\"><PERSON></a>, American lawyer and environmentalist (d. 2006)", "links": [{"title": "<PERSON> (environmental campaigner)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(environmental_campaigner)"}]}, {"year": "1964", "text": "<PERSON>, French rugby player and coach", "html": "1964 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French rugby player and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French rugby player and coach", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1965", "text": "<PERSON>, English motorcycle racer", "html": "1965 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English motorcycle racer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English motorcycle racer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1965", "text": "<PERSON>, English rugby player and coach", "html": "1965 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English rugby player and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English rugby player and coach", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1965", "text": "<PERSON>, Norwegian director and producer", "html": "1965 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Norwegian director and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Norwegian director and producer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1966", "text": "<PERSON>, Italian footballer and coach", "html": "1966 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian footballer and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian footballer and coach", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1966", "text": "<PERSON>, Canadian-American ice hockey player (d. 2013)", "html": "1966 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian-American ice hockey player (d. 2013)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian-American ice hockey player (d. 2013)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1967", "text": "<PERSON>, Canadian-American model and actress", "html": "1967 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian-American model and actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian-American model and actress", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1969", "text": "<PERSON><PERSON><PERSON><PERSON>, American-Irish singer-songwriter and guitarist", "html": "1969 - <a href=\"https://wikipedia.org/wiki/S%C3%A9amus_Egan\" title=\"Séamus Egan\"><PERSON><PERSON><PERSON><PERSON></a>, American-Irish singer-songwriter and guitarist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/S%C3%A9amus_Egan\" title=\"Séamus Egan\"><PERSON><PERSON><PERSON><PERSON></a>, American-Irish singer-songwriter and guitarist", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/S%C3%A9amus_Egan"}]}, {"year": "1971", "text": "<PERSON><PERSON>, American rapper, producer, dancer and actress", "html": "1971 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American rapper, producer, dancer and actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American rapper, producer, dancer and actress", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1971", "text": "<PERSON><PERSON>, American actress", "html": "1971 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American actress", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1974", "text": "<PERSON>, Ecuadorian race walker", "html": "1974 - <a href=\"https://wikipedia.org/wiki/Jefferson_P%C3%A9rez\" title=\"<PERSON>\"><PERSON></a>, Ecuadorian race walker", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Jefferson_P%C3%A9rez\" title=\"<PERSON>\"><PERSON></a>, Ecuadorian race walker", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Jefferson_P%C3%A9rez"}]}, {"year": "1975", "text": "<PERSON>, American basketball player and coach", "html": "1975 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player and coach", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1975", "text": "<PERSON><PERSON><PERSON>, American singer-songwriter and guitarist", "html": "1975 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American singer-songwriter and guitarist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American singer-songwriter and guitarist", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1976", "text": "<PERSON>, Dutch footballer and coach", "html": "1976 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch footballer and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch footballer and coach", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1976", "text": "<PERSON><PERSON>, Finnish footballer", "html": "1976 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Finnish footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Finnish footballer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1976", "text": "<PERSON>, Australian rugby league player", "html": "1976 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian rugby league player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian rugby league player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1976", "text": "<PERSON><PERSON><PERSON>, Dutch footballer and manager", "html": "1976 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Dutch footballer and manager", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Dutch footballer and manager", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1976", "text": "<PERSON><PERSON><PERSON>, Polish hammer thrower", "html": "1976 - <a href=\"https://wikipedia.org/wiki/Szymon_Zi%C3%B3%C5%82kowski\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Polish hammer thrower", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Szymon_Zi%C3%B3%C5%82kowski\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Polish hammer thrower", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Szymon_Zi%C3%B3%C5%82kowski"}]}, {"year": "1977", "text": "<PERSON>, Senegalese-French singer-songwriter and guitarist", "html": "1977 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Senegalese-French singer-songwriter and guitarist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Senegalese-French singer-songwriter and guitarist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1977", "text": "<PERSON><PERSON>, Japanese musician", "html": "1977 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Japanese musician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Japanese musician", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>ashi"}]}, {"year": "1977", "text": "<PERSON><PERSON><PERSON>, Canadian ice hockey player", "html": "1977 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Canadian ice hockey player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Canadian ice hockey player", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>la"}]}, {"year": "1977", "text": "<PERSON>, American actress", "html": "1977 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1979", "text": "<PERSON>, American mixed martial artist and actor", "html": "1979 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American mixed martial artist and actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American mixed martial artist and actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1980", "text": "<PERSON>, Dominican-American baseball player", "html": "1980 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dominican-American baseball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dominican-American baseball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1981", "text": "<PERSON>, South African-Italian rugby player", "html": "1981 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, South African-Italian rugby player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, South African-Italian rugby player", "links": [{"title": "Carlo <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1981", "text": "<PERSON><PERSON><PERSON>, Irish-Australian footballer", "html": "1981 - <a href=\"https://wikipedia.org/wiki/<PERSON>h<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Irish-Australian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>h<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Irish-Australian footballer", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Tadhg_<PERSON>"}]}, {"year": "1982", "text": "<PERSON>, Australian baseball player", "html": "1982 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian baseball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian baseball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1982", "text": "<PERSON>, Swedish tennis player", "html": "1982 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swedish tennis player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swedish tennis player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1982", "text": "<PERSON>, American football player", "html": "1982 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(American_football)\" title=\"<PERSON> (American football)\"><PERSON></a>, American football player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(American_football)\" title=\"<PERSON> (American football)\"><PERSON></a>, American football player", "links": [{"title": "<PERSON> (American football)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(American_football)"}]}, {"year": "1982", "text": "<PERSON><PERSON>, Indonesian actor, model, and musician", "html": "1982 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Indonesian actor, model, and musician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Indonesian actor, model, and musician", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Fedi_Nuril"}]}, {"year": "1982", "text": "<PERSON><PERSON><PERSON>, American actress", "html": "1982 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American actress", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Hilar<PERSON>_<PERSON>"}]}, {"year": "1983", "text": "<PERSON><PERSON><PERSON>, South Korean singer and entertainer", "html": "1983 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, South Korean singer and entertainer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, South Korean singer and entertainer", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>"}]}, {"year": "1984", "text": "<PERSON>, Bahamian high jumper", "html": "1984 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(athlete)\" class=\"mw-redirect\" title=\"<PERSON> (athlete)\"><PERSON></a>, Bahamian high jumper", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(athlete)\" class=\"mw-redirect\" title=\"<PERSON> (athlete)\"><PERSON></a>, Bahamian high jumper", "links": [{"title": "<PERSON> (athlete)", "link": "https://wikipedia.org/wiki/<PERSON>(athlete)"}]}, {"year": "1985", "text": "<PERSON>, American baseball player", "html": "1985 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(baseball)\" title=\"<PERSON> (baseball)\"><PERSON></a>, American baseball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(baseball)\" title=\"<PERSON> (baseball)\"><PERSON></a>, American baseball player", "links": [{"title": "<PERSON> (baseball)", "link": "https://wikipedia.org/wiki/<PERSON>_(baseball)"}]}, {"year": "1985", "text": "<PERSON><PERSON><PERSON>, French actor", "html": "1985 - <a href=\"https://wikipedia.org/wiki/L%C3%A9a_<PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, French actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/L%C3%A9a_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, French actor", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/L%C3%A9a_<PERSON><PERSON><PERSON>"}]}, {"year": "1986", "text": "<PERSON>, American baseball player", "html": "1986 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1986", "text": "<PERSON>, Australian footballer", "html": "1986 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(Australian_footballer)\" title=\"<PERSON> (Australian footballer)\"><PERSON></a>, Australian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(Australian_footballer)\" title=\"<PERSON> (Australian footballer)\"><PERSON></a>, Australian footballer", "links": [{"title": "<PERSON> (Australian footballer)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>(Australian_footballer)"}]}, {"year": "1986", "text": "<PERSON>, German footballer", "html": "1986 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1987", "text": "<PERSON>, German decathlete", "html": "1987 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German decathlete", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German decathlete", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1988", "text": "<PERSON><PERSON><PERSON>, Brazilian footballer", "html": "1988 - <a href=\"https://wikipedia.org/wiki/Ded%C3%A9_(footballer,_born_1988)\" title=\"<PERSON><PERSON><PERSON> (footballer, born 1988)\"><PERSON><PERSON><PERSON></a>, Brazilian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Ded%C3%A9_(footballer,_born_1988)\" title=\"<PERSON><PERSON><PERSON> (footballer, born 1988)\"><PERSON><PERSON><PERSON></a>, Brazilian footballer", "links": [{"title": "<PERSON><PERSON><PERSON> (footballer, born 1988)", "link": "https://wikipedia.org/wiki/Ded%C3%A9_(footballer,_born_1988)"}]}, {"year": "1988", "text": "<PERSON><PERSON><PERSON><PERSON>, Russian modern pentathlete", "html": "1988 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>san<PERSON>_<PERSON>\" title=\"Aleksan<PERSON> Lesun\"><PERSON><PERSON><PERSON><PERSON></a>, Russian modern pentathlete", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON>eksan<PERSON> Lesun\"><PERSON><PERSON><PERSON><PERSON></a>, Russian modern pentathlete", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>un"}]}, {"year": "1989", "text": "<PERSON>, American basketball player", "html": "1989 - <a href=\"https://wikipedia.org/wiki/Kent_Bazemore\" title=\"Kent Bazemore\"><PERSON></a>, American basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Kent_Bazemore\" title=\"Kent Bazemore\"><PERSON></a>, American basketball player", "links": [{"title": "Kent Bazemore", "link": "https://wikipedia.org/wiki/Kent_Bazemore"}]}, {"year": "1989", "text": "<PERSON>, English actress", "html": "1989 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actress", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1989", "text": "<PERSON>, Australian race car driver", "html": "1989 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian race car driver", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian race car driver", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1991", "text": "<PERSON>, Spanish footballer", "html": "1991 - <a href=\"https://wikipedia.org/wiki/<PERSON>_V%C3%<PERSON><PERSON><PERSON>\" title=\"<PERSON>\"><PERSON></a>, Spanish footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Lucas_V%C3%<PERSON><PERSON><PERSON>\" title=\"<PERSON>\"><PERSON></a>, Spanish footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Lucas_V%C3%<PERSON><PERSON><PERSON>"}]}, {"year": "1991", "text": "<PERSON>, American baseball player", "html": "1991 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1992", "text": "<PERSON>, American baseball player", "html": "1992 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(baseball)\" title=\"<PERSON> (baseball)\"><PERSON></a>, American baseball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(baseball)\" title=\"<PERSON> (baseball)\"><PERSON></a>, American baseball player", "links": [{"title": "<PERSON> (baseball)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(baseball)"}]}, {"year": "1994", "text": "<PERSON><PERSON><PERSON>, French tennis player", "html": "1994 - <a href=\"https://wikipedia.org/wiki/Chlo%C3%A9_Paquet\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, French tennis player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Chlo%C3%A9_Paquet\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, French tennis player", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Chlo%C3%A9_Paquet"}]}, {"year": "1995", "text": "<PERSON><PERSON><PERSON><PERSON><PERSON>, Belgian footballer", "html": "1995 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_Bo<PERSON>oli-Mbombo\" class=\"mw-redirect\" title=\"<PERSON><PERSON>-Mbombo\"><PERSON><PERSON></a>, Belgian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_Bolingoli-Mbombo\" class=\"mw-redirect\" title=\"<PERSON><PERSON>-Mbombo\"><PERSON><PERSON></a>, Belgian footballer", "links": [{"title": "<PERSON><PERSON>Mbombo", "link": "https://wikipedia.org/wiki/Boli_Bolingoli-Mbombo"}]}, {"year": "1995", "text": "<PERSON><PERSON><PERSON>, Miss America 2017", "html": "1995 - <a href=\"https://wikipedia.org/wiki/Savvy_<PERSON>\" title=\"Savvy <PERSON>\">Sav<PERSON> <PERSON></a>, <a href=\"https://wikipedia.org/wiki/Miss_America_2017\" title=\"Miss America 2017\">Miss America 2017</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Savvy_<PERSON>\" title=\"Savvy <PERSON>\">Sav<PERSON></a>, <a href=\"https://wikipedia.org/wiki/Miss_America_2017\" title=\"Miss America 2017\">Miss America 2017</a>", "links": [{"title": "Savvy <PERSON>", "link": "https://wikipedia.org/wiki/Savvy_<PERSON>"}, {"title": "Miss America 2017", "link": "https://wikipedia.org/wiki/Miss_America_2017"}]}, {"year": "1995", "text": "<PERSON><PERSON><PERSON>, South Korea  rapper", "html": "1995 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, South Korea rapper", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, South Korea rapper", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>eyong"}]}, {"year": "1996", "text": "<PERSON><PERSON><PERSON>, Russian figure skater", "html": "1996 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Russian figure skater", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Russian figure skater", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1998", "text": "<PERSON>, American singer-songwriter and actress", "html": "1998 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and actress", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1998", "text": "<PERSON>, Swiss tennis player", "html": "1998 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swiss tennis player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swiss tennis player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1998", "text": "<PERSON><PERSON>, Lithuanian figure skater", "html": "1998 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Lithuanian figure skater", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Lithuanian figure skater", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "2000", "text": "<PERSON><PERSON>, Indonesian sprinter", "html": "2000 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Indonesian sprinter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Indonesian sprinter", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>"}]}, {"year": "2001", "text": "<PERSON><PERSON>, American actor and singer", "html": "2001 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American actor and singer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American actor and singer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "2003", "text": "<PERSON>, Canadian singer-songwriter and dancer", "html": "2003 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian singer-songwriter and dancer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian singer-songwriter and dancer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2003", "text": "<PERSON>, American actress", "html": "2003 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_Reid"}]}], "Deaths": [{"year": "552", "text": "<PERSON><PERSON><PERSON>, Ostrogoth king", "html": "552 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Ostrogoth king", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Ostrogoth king", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>a"}]}, {"year": "992", "text": "<PERSON><PERSON><PERSON><PERSON>, Korean queen (b. 966)", "html": "992 - <a href=\"https://wikipedia.org/wiki/Queen_<PERSON><PERSON><PERSON><PERSON>\" title=\"Queen <PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Korean queen (b. 966)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Queen_<PERSON><PERSON><PERSON><PERSON>\" title=\"Queen <PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Korean queen (b. 966)", "links": [{"title": "Queen <PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON><PERSON><PERSON>"}]}, {"year": "1109", "text": "<PERSON>, king of León and Castile (b. 1040)", "html": "1109 - <a href=\"https://wikipedia.org/wiki/<PERSON>_VI_of_Le%C3%B3n_and_Castile\" title=\"<PERSON> VI of León and Castile\"><PERSON> VI</a>, king of León and Castile (b. 1040)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_VI_of_Le%C3%B3n_and_Castile\" title=\"<PERSON> of León and Castile\"><PERSON> VI</a>, king of León and Castile (b. 1040)", "links": [{"title": "Alfonso VI of León and Castile", "link": "https://wikipedia.org/wiki/Alfonso_VI_of_Le%C3%B3n_and_Castile"}]}, {"year": "1224", "text": "<PERSON><PERSON><PERSON>, regent of the Kamakura shogunate of Japan (b. 1163)", "html": "1224 - <a href=\"https://wikipedia.org/wiki/H%C5%8Dj%C5%8D_Yo<PERSON>toki\" title=\"<PERSON><PERSON><PERSON>shitok<PERSON>\"><PERSON><PERSON><PERSON></a>, regent of the <a href=\"https://wikipedia.org/wiki/Kamakura_shogunate\" title=\"Kamakura shogunate\">Kamakura shogunate</a> of Japan (b. 1163)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/H%C5%8Dj%C5%8D_<PERSON><PERSON>toki\" title=\"<PERSON><PERSON><PERSON>shi<PERSON>\"><PERSON><PERSON><PERSON></a>, regent of the <a href=\"https://wikipedia.org/wiki/Kamakura_shogunate\" title=\"Kamakura shogunate\">Kamakura shogunate</a> of Japan (b. 1163)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/H%C5%8Dj%C5%8D_<PERSON><PERSON><PERSON>i"}, {"title": "Kamakura shogunate", "link": "https://wikipedia.org/wiki/Kamakura_shogunate"}]}, {"year": "1242", "text": "<PERSON><PERSON><PERSON>, Mongol ruler (b. 1183)", "html": "1242 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON> Khan\"><PERSON><PERSON><PERSON></a>, Mongol ruler (b. 1183)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON> Khan\"><PERSON><PERSON><PERSON></a>, Mongol ruler (b. 1183)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1277", "text": "<PERSON><PERSON><PERSON>, Egyptian sultan (b. 1223)", "html": "1277 - <a href=\"https://wikipedia.org/wiki/Baibars\" class=\"mw-redirect\" title=\"Baibars\"><PERSON><PERSON><PERSON></a>, Egyptian sultan (b. 1223)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Baibars\" class=\"mw-redirect\" title=\"Baibars\"><PERSON><PERSON><PERSON></a>, Egyptian sultan (b. 1223)", "links": [{"title": "Baibars", "link": "https://wikipedia.org/wiki/Baibars"}]}, {"year": "1287", "text": "<PERSON><PERSON><PERSON><PERSON><PERSON>, Burmese king (b. 1238)", "html": "1287 - <a href=\"https://wikipedia.org/wiki/Narathihapate\" title=\"Narathihapate\"><PERSON><PERSON><PERSON><PERSON><PERSON></a>, Burmese king (b. 1238)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Narathihapate\" title=\"Narathihapate\"><PERSON><PERSON><PERSON><PERSON><PERSON></a>, Burmese king (b. 1238)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Narathihapate"}]}, {"year": "1321", "text": "<PERSON>, queen of Castile and León", "html": "1321 - <a href=\"https://wikipedia.org/wiki/Mar%C3%<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, queen of Castile and León", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Mar%C3%<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, queen of Castile and León", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Mar%C3%<PERSON><PERSON>_de_<PERSON><PERSON>"}]}, {"year": "1348", "text": "<PERSON>, English princess", "html": "1348 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_England_(1335%E2%80%931348)\" class=\"mw-redirect\" title=\"Joan of England (1335-1348)\"><PERSON></a>, English princess", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_England_(1335%E2%80%931348)\" class=\"mw-redirect\" title=\"Joan of England (1335-1348)\"><PERSON></a>, English princess", "links": [{"title": "<PERSON> of England (1335-1348)", "link": "https://wikipedia.org/wiki/<PERSON>_of_England_(1335%E2%80%931348)"}]}, {"year": "1555", "text": "<PERSON>, English reformer, prebendary of St. Paul's (b. 1510)", "html": "1555 - <a href=\"https://wikipedia.org/wiki/John_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English <a href=\"https://wikipedia.org/wiki/English_Reformation\" title=\"English Reformation\">reformer</a>, <a href=\"https://wikipedia.org/wiki/Prebendary\" title=\"Prebendary\">prebendary</a> of <a href=\"https://wikipedia.org/wiki/Old_St_Paul%27s_Cathedral\" title=\"Old St Paul's Cathedral\">St. Paul's</a> (b. 1510)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/John_<PERSON>\" title=\"John <PERSON>\"><PERSON></a>, English <a href=\"https://wikipedia.org/wiki/English_Reformation\" title=\"English Reformation\">reformer</a>, <a href=\"https://wikipedia.org/wiki/Prebendary\" title=\"Prebendary\">prebendary</a> of <a href=\"https://wikipedia.org/wiki/Old_St_Paul%27s_Cathedral\" title=\"Old St Paul's Cathedral\">St. Paul's</a> (b. 1510)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "English Reformation", "link": "https://wikipedia.org/wiki/English_Reformation"}, {"title": "Prebendary", "link": "https://wikipedia.org/wiki/Prebendary"}, {"title": "Old St Paul's Cathedral", "link": "https://wikipedia.org/wiki/Old_St_Paul%27s_Cathedral"}]}, {"year": "1589", "text": "Lady <PERSON><PERSON><PERSON>, Japanese concubine (b. 1552)", "html": "1589 - <a href=\"https://wikipedia.org/wiki/Lady_<PERSON>g%C5%8D\" title=\"Lady <PERSON><PERSON>\">Lady <PERSON><PERSON></a>, Japanese concubine (b. 1552)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Lady_<PERSON>g%C5%8D\" title=\"Lady <PERSON>\">Lady <PERSON><PERSON><PERSON></a>, Japanese concubine (b. 1552)", "links": [{"title": "Lady <PERSON>", "link": "https://wikipedia.org/wiki/Lady_Saig%C5%8D"}]}, {"year": "1592", "text": "<PERSON><PERSON><PERSON>, Italian composer and educator (b. 1535)", "html": "1592 - <a href=\"https://wikipedia.org/wiki/<PERSON>%27A<PERSON><PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON><PERSON></a>, Italian composer and educator (b. 1535)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>%27A<PERSON><PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON><PERSON></a>, Italian composer and educator (b. 1535)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Marc%27A<PERSON><PERSON>_<PERSON>gne<PERSON>"}]}, {"year": "1614", "text": "<PERSON>, French philologist and scholar (b. 1559)", "html": "1614 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French philologist and scholar (b. 1559)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French philologist and scholar (b. 1559)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1622", "text": "<PERSON>, 4th Baron <PERSON>, English politician (b. 1575)", "html": "1622 - <a href=\"https://wikipedia.org/wiki/<PERSON>,_4th_Baron_<PERSON>\" title=\"<PERSON>, 4th Baron <PERSON>\"><PERSON>, 4th Baron <PERSON></a>, English politician (b. 1575)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>,_4th_Baron_<PERSON>\" title=\"<PERSON>, 4th Baron <PERSON>\"><PERSON>, 4th Baron <PERSON></a>, English politician (b. 1575)", "links": [{"title": "<PERSON>, 4th Baron <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>,_4th_Baron_<PERSON>"}]}, {"year": "1681", "text": "<PERSON>, Irish archbishop and saint (b. 1629)", "html": "1681 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Irish archbishop and saint (b. 1629)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Irish archbishop and saint (b. 1629)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1736", "text": "<PERSON>, Ottoman sultan (b. 1673)", "html": "1736 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> III\"><PERSON></a>, Ottoman sultan (b. 1673)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> III\"><PERSON></a>, Ottoman sultan (b. 1673)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1749", "text": "<PERSON>, Welsh mathematician and academic (b. 1675)", "html": "1749 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(mathematician)\" title=\"<PERSON> (mathematician)\"><PERSON></a>, Welsh mathematician and academic (b. 1675)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>(mathematician)\" title=\"<PERSON> (mathematician)\"><PERSON></a>, Welsh mathematician and academic (b. 1675)", "links": [{"title": "<PERSON> (mathematician)", "link": "https://wikipedia.org/wiki/<PERSON>_(mathematician)"}]}, {"year": "1774", "text": "<PERSON>, 1st Baron <PERSON>, English politician, Secretary of State for the Southern Department (b. 1705)", "html": "1774 - <a href=\"https://wikipedia.org/wiki/<PERSON>,_1st_Baron_<PERSON>\" title=\"<PERSON>, 1st Baron <PERSON>\"><PERSON>, 1st Baron <PERSON></a>, English politician, <a href=\"https://wikipedia.org/wiki/Secretary_of_State_for_the_Southern_Department\" title=\"Secretary of State for the Southern Department\">Secretary of State for the Southern Department</a> (b. 1705)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>,_1st_Baron_<PERSON>\" title=\"<PERSON>, 1st Baron <PERSON>\"><PERSON>, 1st Baron <PERSON></a>, English politician, <a href=\"https://wikipedia.org/wiki/Secretary_of_State_for_the_Southern_Department\" title=\"Secretary of State for the Southern Department\">Secretary of State for the Southern Department</a> (b. 1705)", "links": [{"title": "<PERSON>, 1st Baron <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_1st_Baron_<PERSON>"}, {"title": "Secretary of State for the Southern Department", "link": "https://wikipedia.org/wiki/Secretary_of_State_for_the_Southern_Department"}]}, {"year": "1782", "text": "<PERSON>, 2nd Marquess of Rockingham, English politician, Prime Minister of Great Britain (b. 1730)", "html": "1782 - <a href=\"https://wikipedia.org/wiki/<PERSON>,_2nd_Marquess_of_Rockingham\" title=\"<PERSON>, 2nd Marquess of Rockingham\"><PERSON>, 2nd Marquess of Rockingham</a>, English politician, <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Great_Britain\" class=\"mw-redirect\" title=\"Prime Minister of Great Britain\">Prime Minister of Great Britain</a> (b. 1730)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>,_2nd_Marquess_of_Rockingham\" title=\"<PERSON>, 2nd Marquess of Rockingham\"><PERSON>, 2nd Marquess of Rockingham</a>, English politician, <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Great_Britain\" class=\"mw-redirect\" title=\"Prime Minister of Great Britain\">Prime Minister of Great Britain</a> (b. 1730)", "links": [{"title": "<PERSON>, 2nd Marquess of Rockingham", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_2nd_Marquess_of_Rockingham"}, {"title": "Prime Minister of Great Britain", "link": "https://wikipedia.org/wiki/Prime_Minister_of_Great_Britain"}]}, {"year": "1784", "text": "<PERSON>, German composer (b. 1710)", "html": "1784 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German composer (b. 1710)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German composer (b. 1710)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1787", "text": "<PERSON>, French marshal (b. 1715)", "html": "1787 - <a href=\"https://wikipedia.org/wiki/<PERSON>,_Prince_of_Soubise\" title=\"<PERSON>, Prince of Soubise\"><PERSON></a>, French marshal (b. 1715)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>,_Prince_of_Soubise\" title=\"<PERSON>, Prince of Soubise\"><PERSON></a>, French marshal (b. 1715)", "links": [{"title": "<PERSON>, Prince of Soubise", "link": "https://wikipedia.org/wiki/<PERSON>,_Prince_<PERSON>_<PERSON><PERSON><PERSON>"}]}, {"year": "1819", "text": "The Public Universal Friend, American evangelist (b. 1752)", "html": "1819 - The <a href=\"https://wikipedia.org/wiki/Public_Universal_Friend\" title=\"Public Universal Friend\">Public Universal Friend</a>, American evangelist (b. 1752)", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Public_Universal_Friend\" title=\"Public Universal Friend\">Public Universal Friend</a>, American evangelist (b. 1752)", "links": [{"title": "Public Universal Friend", "link": "https://wikipedia.org/wiki/Public_Universal_Friend"}]}, {"year": "1828", "text": "<PERSON><PERSON><PERSON>, a Muscogee war orphan adopted by <PERSON>", "html": "1828 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, a Muscogee war orphan adopted by <PERSON>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, a Muscogee war orphan adopted by <PERSON>", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1839", "text": "<PERSON><PERSON><PERSON> <PERSON>, Ottoman sultan (b. 1785)", "html": "1839 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_II\" title=\"Mahmud II\"><PERSON><PERSON><PERSON> <PERSON></a>, Ottoman sultan (b. 1785)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"Mahm<PERSON> II\"><PERSON><PERSON><PERSON> <PERSON></a>, Ottoman sultan (b. 1785)", "links": [{"title": "<PERSON><PERSON><PERSON> <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_II"}]}, {"year": "1860", "text": "<PERSON>, American chemist and engineer (b. 1800)", "html": "1860 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American chemist and engineer (b. 1800)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American chemist and engineer (b. 1800)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1863", "text": "<PERSON>, American general (b. 1820)", "html": "1863 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American general (b. 1820)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American general (b. 1820)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "1884", "text": "<PERSON>, Scottish-American detective and spy (b. 1819)", "html": "1884 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish-American detective and spy (b. 1819)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish-American detective and spy (b. 1819)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1896", "text": "<PERSON>, American author and activist (b. 1811)", "html": "1896 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author and activist (b. 1811)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author and activist (b. 1811)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>e"}]}, {"year": "1905", "text": "<PERSON>, American journalist and politician, 37th United States Secretary of State (b. 1838)", "html": "1905 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American journalist and politician, 37th <a href=\"https://wikipedia.org/wiki/United_States_Secretary_of_State\" title=\"United States Secretary of State\">United States Secretary of State</a> (b. 1838)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American journalist and politician, 37th <a href=\"https://wikipedia.org/wiki/United_States_Secretary_of_State\" title=\"United States Secretary of State\">United States Secretary of State</a> (b. 1838)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "United States Secretary of State", "link": "https://wikipedia.org/wiki/United_States_Secretary_of_State"}]}, {"year": "1912", "text": "<PERSON>, American pilot and screenwriter (b. 1875)", "html": "1912 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American pilot and screenwriter (b. 1875)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American pilot and screenwriter (b. 1875)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1925", "text": "<PERSON>, French pianist and composer (b. 1866)", "html": "1925 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French pianist and composer (b. 1866)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French pianist and composer (b. 1866)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1934", "text": "<PERSON>, German paramilitary commander (b. 1887)", "html": "1934 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%B6hm\" title=\"<PERSON>\"><PERSON></a>, German paramilitary commander (b. 1887)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%B6hm\" title=\"<PERSON>\"><PERSON></a>, German paramilitary commander (b. 1887)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Ernst_R%C3%B6hm"}]}, {"year": "1942", "text": "<PERSON><PERSON><PERSON>, Irish writer (b. 1857)", "html": "1942 - <a href=\"https://wikipedia.org/wiki/<PERSON>eadar_<PERSON><PERSON>_<PERSON>_<PERSON>nla<PERSON>\" title=\"<PERSON>eadar <PERSON>nla<PERSON>\"><PERSON><PERSON><PERSON></a>, Irish writer (b. 1857)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>ar_<PERSON><PERSON>_Mac_<PERSON>nlaoich\" title=\"<PERSON>eadar To<PERSON>nla<PERSON>\"><PERSON><PERSON><PERSON></a>, Irish writer (b. 1857)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON><PERSON>_<PERSON>_<PERSON>ch"}]}, {"year": "1943", "text": "<PERSON>, Dutch artist, author and anti-Nazi resistance fighter (b. 1894)", "html": "1943 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch artist, author and anti-Nazi resistance fighter (b. 1894)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch artist, author and anti-Nazi resistance fighter (b. 1894)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Willem_<PERSON>rondeus"}]}, {"year": "1944", "text": "<PERSON>, Austrian-English screenwriter (b. 1894)", "html": "1944 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Austrian-English screenwriter (b. 1894)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Austrian-English screenwriter (b. 1894)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1944", "text": "<PERSON>, Russian author (b. 1930)", "html": "1944 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian author (b. 1930)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian author (b. 1930)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1948", "text": "<PERSON><PERSON><PERSON>, Italian race car driver (b. 1904)", "html": "1948 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Italian race car driver (b. 1904)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Italian race car driver (b. 1904)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/A<PERSON><PERSON>_<PERSON>"}]}, {"year": "1950", "text": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, Swiss composer and educator (b. 1865)", "html": "1950 - <a href=\"https://wikipedia.org/wiki/%C3%89mile_<PERSON><PERSON><PERSON>-<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Swiss composer and educator (b. 1865)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/%C3%89mile_<PERSON><PERSON><PERSON>-<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Swiss composer and educator (b. 1865)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/%C3%89mile_Jaque<PERSON>-<PERSON><PERSON><PERSON><PERSON>"}]}, {"year": "1950", "text": "<PERSON><PERSON>, Finnish-American architect, co-designed the National Museum of Finland (b. 1873)", "html": "1950 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Finnish-American architect, co-designed the <a href=\"https://wikipedia.org/wiki/National_Museum_of_Finland\" title=\"National Museum of Finland\">National Museum of Finland</a> (b. 1873)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Finnish-American architect, co-designed the <a href=\"https://wikipedia.org/wiki/National_Museum_of_Finland\" title=\"National Museum of Finland\">National Museum of Finland</a> (b. 1873)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}, {"title": "National Museum of Finland", "link": "https://wikipedia.org/wiki/National_Museum_of_Finland"}]}, {"year": "1951", "text": "<PERSON><PERSON><PERSON>, Polish poet, novelist and journalist (b. 1922)", "html": "1951 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Polish poet, novelist and journalist (b. 1922)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Polish poet, novelist and journalist (b. 1922)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1961", "text": "<PERSON><PERSON><PERSON>, French physician and author (b. 1894)", "html": "1961 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>%C3%A9line\" title=\"<PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, French physician and author (b. 1894)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A9line\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, French physician and author (b. 1894)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>-<PERSON>_<PERSON>%C3%A9line"}]}, {"year": "1962", "text": "<PERSON><PERSON><PERSON><PERSON>, Indian lawyer and politician (b. 1882)", "html": "1962 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Indian lawyer and politician (b. 1882)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Indian lawyer and politician (b. 1882)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1962", "text": "<PERSON><PERSON><PERSON>, Indian physician and politician, 2nd Chief Minister of West Bengal (b. 1882)", "html": "1962 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Indian physician and politician, 2nd <a href=\"https://wikipedia.org/wiki/Chief_Minister_of_West_Bengal\" class=\"mw-redirect\" title=\"Chief Minister of West Bengal\">Chief Minister of West Bengal</a> (b. 1882)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Indian physician and politician, 2nd <a href=\"https://wikipedia.org/wiki/Chief_Minister_of_West_Bengal\" class=\"mw-redirect\" title=\"Chief Minister of West Bengal\">Chief Minister of West Bengal</a> (b. 1882)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>"}, {"title": "Chief Minister of West Bengal", "link": "https://wikipedia.org/wiki/Chief_Minister_of_West_Bengal"}]}, {"year": "1964", "text": "<PERSON>, French-American viola player and conductor (b. 1875)", "html": "1964 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French-American viola player and conductor (b. 1875)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French-American viola player and conductor (b. 1875)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1965", "text": "<PERSON>, English cricketer (b. 1903)", "html": "1965 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English cricketer (b. 1903)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English cricketer (b. 1903)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1965", "text": "<PERSON>, American journalist and author (b. 1915)", "html": "1965 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American journalist and author (b. 1915)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American journalist and author (b. 1915)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1966", "text": "<PERSON>, American runner (b. 1883)", "html": "1966 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American runner (b. 1883)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American runner (b. 1883)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1967", "text": "<PERSON>, German historian and academic (b. 1888)", "html": "1967 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German historian and academic (b. 1888)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German historian and academic (b. 1888)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1968", "text": "<PERSON>, German judge and politician (b. 1903)", "html": "1968 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German judge and politician (b. 1903)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German judge and politician (b. 1903)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1971", "text": "<PERSON>, Australian-English physicist and academic, Nobel Prize laureate (b. 1890)", "html": "1971 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, Australian-English physicist and academic, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Physics\" title=\"Nobel Prize in Physics\">Nobel Prize</a> laureate (b. 1890)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, Australian-English physicist and academic, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Physics\" title=\"Nobel Prize in Physics\">Nobel Prize</a> laureate (b. 1890)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "Nobel Prize in Physics", "link": "https://wikipedia.org/wiki/Nobel_Prize_in_Physics"}]}, {"year": "1971", "text": "<PERSON><PERSON>, Trinidadian-English cricketer, lawyer and politician (b. 1901)", "html": "1971 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Trinidadian-English cricketer, lawyer and politician (b. 1901)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Trinidadian-English cricketer, lawyer and politician (b. 1901)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1974", "text": "<PERSON>, Argentinian general and politician, President of Argentina (b. 1895)", "html": "1974 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%B3n\" title=\"<PERSON>\"><PERSON></a>, Argentinian general and politician, <a href=\"https://wikipedia.org/wiki/President_of_Argentina\" title=\"President of Argentina\">President of Argentina</a> (b. 1895)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%B3n\" title=\"<PERSON>\"><PERSON></a>, Argentinian general and politician, <a href=\"https://wikipedia.org/wiki/President_of_Argentina\" title=\"President of Argentina\">President of Argentina</a> (b. 1895)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Juan_Per%C3%B3n"}, {"title": "President of Argentina", "link": "https://wikipedia.org/wiki/President_of_Argentina"}]}, {"year": "1978", "text": "<PERSON>, German general and pilot (b. 1890)", "html": "1978 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Kurt Student\"><PERSON></a>, German general and pilot (b. 1890)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Kurt_Student\" title=\"Kurt Student\"><PERSON></a>, German general and pilot (b. 1890)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1981", "text": "<PERSON>, Portuguese author and poet (b. 1921)", "html": "1981 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Portuguese author and poet (b. 1921)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Portuguese author and poet (b. 1921)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1983", "text": "<PERSON><PERSON>, American architect, designed the Montreal B<PERSON><PERSON> (b. 1895)", "html": "1983 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_Fuller\" title=\"Buckminster Fuller\"><PERSON><PERSON> Fuller</a>, American architect, designed the <a href=\"https://wikipedia.org/wiki/Montreal_Biosph%C3%A8re\" class=\"mw-redirect\" title=\"Montreal Biosphère\">Montreal Biosphère</a> (b. 1895)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_Fuller\" title=\"Buckminster Fuller\"><PERSON><PERSON> Fuller</a>, American architect, designed the <a href=\"https://wikipedia.org/wiki/Montreal_Biosph%C3%A8re\" class=\"mw-redirect\" title=\"Montreal Biosphère\">Montreal Biosphère</a> (b. 1895)", "links": [{"title": "Buckminster Fuller", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_Fuller"}, {"title": "Montreal Biosphère", "link": "https://wikipedia.org/wiki/Montreal_Biosph%C3%A8re"}]}, {"year": "1984", "text": "<PERSON><PERSON><PERSON>, Ukrainian-Israeli physicist and academic (b. 1904)", "html": "1984 - <a href=\"https://wikipedia.org/wiki/Mosh%C3%A9_Feldenkrais\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Ukrainian-Israeli physicist and academic (b. 1904)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Mosh%C3%A9_Feldenkrais\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Ukrainian-Israeli physicist and academic (b. 1904)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Mosh%C3%A9_Feldenkrais"}]}, {"year": "1990", "text": "<PERSON><PERSON><PERSON>, Dutch sculptor, designer and educator (b. 1926)", "html": "1990 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Dutch sculptor, designer and educator (b. 1926)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Dutch sculptor, designer and educator (b. 1926)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>er"}]}, {"year": "1991", "text": "<PERSON>, American actor, director and producer (b. 1936)", "html": "1991 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor, director and producer (b. 1936)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor, director and producer (b. 1936)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1992", "text": "<PERSON>, Italian screenwriter and producer (b. 1924)", "html": "1992 - <a href=\"https://wikipedia.org/wiki/Franco_C<PERSON>aldi\" title=\"<PERSON>\"><PERSON></a>, Italian screenwriter and producer (b. 1924)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Franco_C<PERSON>aldi\" title=\"<PERSON>\"><PERSON></a>, Italian screenwriter and producer (b. 1924)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Franco_Cristaldi"}]}, {"year": "1994", "text": "<PERSON><PERSON><PERSON>, American author (b. 1908)", "html": "1994 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>l\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American author (b. 1908)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American author (b. 1908)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Me<PERSON>am_<PERSON>l"}]}, {"year": "1995", "text": "<PERSON><PERSON>, American radio host (b. 1938)", "html": "1995 - <a href=\"https://wikipedia.org/wiki/Wolfman_Jack\" title=\"Wolfman Jack\"><PERSON><PERSON></a>, American radio host (b. 1938)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Wolfman_Jack\" title=\"Wolfman Jack\"><PERSON><PERSON></a>, American radio host (b. 1938)", "links": [{"title": "Wolfman <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_Jack"}]}, {"year": "1995", "text": "<PERSON>, English guitarist (Be-Bop Deluxe) (b. 1950)", "html": "1995 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English guitarist (<a href=\"https://wikipedia.org/wiki/Be-Bop_Deluxe\" title=\"Be-Bop Deluxe\">Be-Bop Deluxe</a>) (b. 1950)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English guitarist (<a href=\"https://wikipedia.org/wiki/Be-Bop_Deluxe\" title=\"Be-Bop Deluxe\">Be-Bop Deluxe</a>) (b. 1950)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Be-Bop Deluxe", "link": "https://wikipedia.org/wiki/Be-<PERSON><PERSON>_Deluxe"}]}, {"year": "1996", "text": "<PERSON>, American lawyer and politician, 46th Governor of New Jersey (b. 1904)", "html": "1996 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and politician, 46th <a href=\"https://wikipedia.org/wiki/Governor_of_New_Jersey\" title=\"Governor of New Jersey\">Governor of New Jersey</a> (b. 1904)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and politician, 46th <a href=\"https://wikipedia.org/wiki/Governor_of_New_Jersey\" title=\"Governor of New Jersey\">Governor of New Jersey</a> (b. 1904)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>"}, {"title": "Governor of New Jersey", "link": "https://wikipedia.org/wiki/Governor_of_New_Jersey"}]}, {"year": "1996", "text": "<PERSON><PERSON><PERSON>, American model and actress (b. 1954)", "html": "1996 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American model and actress (b. 1954)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American model and actress (b. 1954)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1996", "text": "<PERSON>, Serbian-American author and screenwriter (b. 1942)", "html": "1996 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Serbian-American author and screenwriter (b. 1942)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Serbian-American author and screenwriter (b. 1942)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1997", "text": "<PERSON>, American actor (b. 1917)", "html": "1997 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor (b. 1917)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor (b. 1917)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1997", "text": "<PERSON>, American cartoonist (b. 1909)", "html": "1997 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American cartoonist (b. 1909)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American cartoonist (b. 1909)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1999", "text": "<PERSON>, Canadian-American director and producer (b. 1908)", "html": "1999 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian-American director and producer (b. 1908)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian-American director and producer (b. 1908)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1999", "text": "<PERSON>, American businessman, creator of M&M's and the Mars chocolate bar (b. 1904)", "html": "1999 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON> Sr.\"><PERSON>.</a>, American businessman, creator of <a href=\"https://wikipedia.org/wiki/M%26M%27s\" title=\"M&amp;M's\">M&amp;M's</a> and the <a href=\"https://wikipedia.org/wiki/Mars_(chocolate_bar)\" class=\"mw-redirect\" title=\"Mars (chocolate bar)\">Mars chocolate bar</a> (b. 1904)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>.\" title=\"<PERSON> Sr.\"><PERSON>.</a>, American businessman, creator of <a href=\"https://wikipedia.org/wiki/M%26M%27s\" title=\"M&amp;M's\">M&amp;M's</a> and the <a href=\"https://wikipedia.org/wiki/Mars_(chocolate_bar)\" class=\"mw-redirect\" title=\"Mars (chocolate bar)\">Mars chocolate bar</a> (b. 1904)", "links": [{"title": "<PERSON>.", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>."}, {"title": "M&M's", "link": "https://wikipedia.org/wiki/M%26M%27s"}, {"title": "Mars (chocolate bar)", "link": "https://wikipedia.org/wiki/Mars_(chocolate_bar)"}]}, {"year": "1999", "text": "<PERSON>, American actress (b. 1910)", "html": "1999 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress (b. 1910)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress (b. 1910)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1999", "text": "<PERSON><PERSON>, Chilean human rights activist (b. 1935)", "html": "1999 - <a href=\"https://wikipedia.org/wiki/Sola_Sierra\" title=\"Sola Sierra\"><PERSON><PERSON></a>, Chilean human rights activist (b. 1935)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Sola_Sierra\" title=\"Sola Sierra\"><PERSON><PERSON></a>, Chilean human rights activist (b. 1935)", "links": [{"title": "Sola Sierra", "link": "https://wikipedia.org/wiki/Sola_Sierra"}]}, {"year": "2000", "text": "<PERSON>, American actor (b. 1920)", "html": "2000 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor (b. 1920)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor (b. 1920)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2001", "text": "<PERSON><PERSON>, Russian physicist and academic, Nobel Prize laureate (b. 1922)", "html": "2001 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Russian physicist and academic, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Physics\" title=\"Nobel Prize in Physics\">Nobel Prize</a> laureate (b. 1922)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Russian physicist and academic, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Physics\" title=\"Nobel Prize in Physics\">Nobel Prize</a> laureate (b. 1922)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>v"}, {"title": "Nobel Prize in Physics", "link": "https://wikipedia.org/wiki/Nobel_Prize_in_Physics"}]}, {"year": "2001", "text": "<PERSON><PERSON><PERSON>, French race car driver (b. 1925)", "html": "2001 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, French race car driver (b. 1925)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, French race car driver (b. 1925)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>"}]}, {"year": "2003", "text": "<PERSON><PERSON>, American flute player and saxophonist (b. 1930)", "html": "2003 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American flute player and saxophonist (b. 1930)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American flute player and saxophonist (b. 1930)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "2004", "text": "<PERSON>, English playwright and screenwriter (b. 1931)", "html": "2004 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(playwright)\" title=\"<PERSON> (playwright)\"><PERSON></a>, English playwright and screenwriter (b. 1931)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>(playwright)\" title=\"<PERSON> (playwright)\"><PERSON></a>, English playwright and screenwriter (b. 1931)", "links": [{"title": "<PERSON> (playwright)", "link": "https://wikipedia.org/wiki/<PERSON>_(playwright)"}]}, {"year": "2004", "text": "<PERSON><PERSON>, American actor and director (b. 1924)", "html": "2004 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> Brand<PERSON>\"><PERSON><PERSON></a>, American actor and director (b. 1924)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American actor and director (b. 1924)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "2004", "text": "<PERSON><PERSON>, Macedonian composer and conductor (b. 1909)", "html": "2004 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Macedonian composer and conductor (b. 1909)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Macedonian composer and conductor (b. 1909)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "2005", "text": "<PERSON><PERSON>, American singer-songwriter (Four Tops) (b. 1936)", "html": "2005 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American singer-songwriter (<a href=\"https://wikipedia.org/wiki/Four_Tops\" title=\"Four Tops\">Four Tops</a>) (b. 1936)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American singer-songwriter (<a href=\"https://wikipedia.org/wiki/Four_Tops\" title=\"Four Tops\">Four Tops</a>) (b. 1936)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}, {"title": "Four Tops", "link": "https://wikipedia.org/wiki/Four_Tops"}]}, {"year": "2005", "text": "<PERSON>, Canadian ice hockey player and coach (b. 1923)", "html": "2005 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player and coach (b. 1923)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player and coach (b. 1923)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_Bo<PERSON>ar"}]}, {"year": "2005", "text": "<PERSON>, American singer-songwriter and producer (<PERSON>) (b. 1951)", "html": "2005 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and producer (<a href=\"https://wikipedia.org/wiki/Change_(band)\" title=\"Change (band)\">Change</a>) (b. 1951)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and producer (<a href=\"https://wikipedia.org/wiki/Change_(band)\" title=\"Change (band)\">Change</a>) (b. 1951)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}, {"title": "Change (band)", "link": "https://wikipedia.org/wiki/Change_(band)"}]}, {"year": "2006", "text": "<PERSON><PERSON><PERSON><PERSON>, Japanese politician, 53rd Prime Minister of Japan (b. 1937)", "html": "2006 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Japanese politician, 53rd <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Japan\" title=\"Prime Minister of Japan\">Prime Minister of Japan</a> (b. 1937)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Japanese politician, 53rd <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Japan\" title=\"Prime Minister of Japan\">Prime Minister of Japan</a> (b. 1937)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON><PERSON>"}, {"title": "Prime Minister of Japan", "link": "https://wikipedia.org/wiki/Prime_Minister_of_Japan"}]}, {"year": "2006", "text": "<PERSON>, Estonian race car driver and politician, Estonian Minister of the Interior (b. 1952)", "html": "2006 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Estonian race car driver and politician, <a href=\"https://wikipedia.org/wiki/Minister_of_the_Interior_(Estonia)\" class=\"mw-redirect\" title=\"Minister of the Interior (Estonia)\">Estonian Minister of the Interior</a> (b. 1952)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Estonian race car driver and politician, <a href=\"https://wikipedia.org/wiki/Minister_of_the_Interior_(Estonia)\" class=\"mw-redirect\" title=\"Minister of the Interior (Estonia)\">Estonian Minister of the Interior</a> (b. 1952)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}, {"title": "Minister of the Interior (Estonia)", "link": "https://wikipedia.org/wiki/Minister_of_the_Interior_(Estonia)"}]}, {"year": "2006", "text": "<PERSON>, English cricketer and sportscaster (b. 1931)", "html": "2006 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English cricketer and sportscaster (b. 1931)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English cricketer and sportscaster (b. 1931)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2008", "text": "<PERSON>, English guitarist (b. 1948)", "html": "2008 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English guitarist (b. 1948)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English guitarist (b. 1948)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Mel_Galley"}]}, {"year": "2009", "text": "<PERSON>, American actor (b. 1912)", "html": "2009 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor (b. 1912)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor (b. 1912)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2009", "text": "<PERSON><PERSON>, Finnish soldier and author (b. 1917)", "html": "2009 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Finnish soldier and author (b. 1917)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Finnish soldier and author (b. 1917)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Onni_Palaste"}]}, {"year": "2009", "text": "<PERSON><PERSON>, English actress (b. 1922)", "html": "2009 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, English actress (b. 1922)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, English actress (b. 1922)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>den"}]}, {"year": "2010", "text": "<PERSON>, American football player and coach (b. 1924)", "html": "2010 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player and coach (b. 1924)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player and coach (b. 1924)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2010", "text": "<PERSON>, American painter and illustrator (b. 1913)", "html": "2010 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American painter and illustrator (b. 1913)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American painter and illustrator (b. 1913)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2010", "text": "<PERSON><PERSON>, American actress and singer (b. 1929)", "html": "2010 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American actress and singer (b. 1929)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American actress and singer (b. 1929)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_Woods"}]}, {"year": "2012", "text": "<PERSON>, American priest and author (b. 1938)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American priest and author (b. 1938)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American priest and author (b. 1938)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "2012", "text": "<PERSON><PERSON>, Jamaican-American keyboard player and producer (b. 1950)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>bbert\" title=\"<PERSON><PERSON> Hibbert\"><PERSON><PERSON></a>, Jamaican-American keyboard player and producer (b. 1950)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>bbert\" title=\"<PERSON><PERSON> Hibbert\"><PERSON><PERSON></a>, Jamaican-American keyboard player and producer (b. 1950)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON><PERSON>"}]}, {"year": "2012", "text": "<PERSON>, American operatic soprano (b. 1926)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American operatic soprano (b. 1926)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American operatic soprano (b. 1926)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2012", "text": "<PERSON>, American captain, pilot and astronaut (b. 1961)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American captain, pilot and astronaut (b. 1961)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American captain, pilot and astronaut (b. 1961)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_Poindexter"}]}, {"year": "2012", "text": "<PERSON>, American author and playwright (b. 1934)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(writer)\" title=\"<PERSON> (writer)\"><PERSON></a>, American author and playwright (b. 1934)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(writer)\" title=\"<PERSON> (writer)\"><PERSON></a>, American author and playwright (b. 1934)", "links": [{"title": "<PERSON> (writer)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(writer)"}]}, {"year": "2013", "text": "<PERSON>, American general (b. 1926)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American general (b. 1926)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American general (b. 1926)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "2013", "text": "<PERSON>, American game designer, co-creator of <PERSON><PERSON><PERSON> (b. 1930)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(inventor)\" title=\"<PERSON> (inventor)\"><PERSON></a>, American game designer, co-creator of <a href=\"https://wikipedia.org/wiki/Twister_(game)\" title=\"Twister (game)\">Twister</a> (b. 1930)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>(inventor)\" title=\"<PERSON> (inventor)\"><PERSON></a>, American game designer, co-creator of <a href=\"https://wikipedia.org/wiki/Twister_(game)\" title=\"Twister (game)\">Twister</a> (b. 1930)", "links": [{"title": "<PERSON> (inventor)", "link": "https://wikipedia.org/wiki/<PERSON>_(inventor)"}, {"title": "<PERSON><PERSON><PERSON> (game)", "link": "https://wikipedia.org/wiki/Twister_(game)"}]}, {"year": "2013", "text": "<PERSON>, American minister and politician (b. 1941)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>(Pennsylvania_politician)\" class=\"mw-redirect\" title=\"<PERSON> (Pennsylvania politician)\"><PERSON></a>, American minister and politician (b. 1941)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>(Pennsylvania_politician)\" class=\"mw-redirect\" title=\"<PERSON> (Pennsylvania politician)\"><PERSON></a>, American minister and politician (b. 1941)", "links": [{"title": "<PERSON> (Pennsylvania politician)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>(Pennsylvania_politician)"}]}, {"year": "2014", "text": "<PERSON>, Canadian economist, lawyer and politician (b. 1938)", "html": "2014 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian economist, lawyer and politician (b. 1938)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian economist, lawyer and politician (b. 1938)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2014", "text": "<PERSON>, American activist, co-founder of The Farm (b. 1935)", "html": "2014 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American activist, co-founder of <a href=\"https://wikipedia.org/wiki/The_Farm_(Tennessee)\" title=\"The Farm (Tennessee)\">The Farm</a> (b. 1935)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American activist, co-founder of <a href=\"https://wikipedia.org/wiki/The_Farm_(Tennessee)\" title=\"The Farm (Tennessee)\">The Farm</a> (b. 1935)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}, {"title": "The Farm (Tennessee)", "link": "https://wikipedia.org/wiki/The_Farm_(Tennessee)"}]}, {"year": "2014", "text": "<PERSON>, English lawyer and politician (b. 1955)", "html": "2014 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(police_commissioner)\" title=\"<PERSON> (police commissioner)\"><PERSON></a>, English lawyer and politician (b. 1955)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(police_commissioner)\" title=\"<PERSON> (police commissioner)\"><PERSON></a>, English lawyer and politician (b. 1955)", "links": [{"title": "<PERSON> (police commissioner)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(police_commissioner)"}]}, {"year": "2014", "text": "<PERSON><PERSON><PERSON>, Ukrainian-Russian general (b. 1942)", "html": "2014 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Ukrainian-Russian general (b. 1942)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Ukrainian-Russian general (b. 1942)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "2014", "text": "<PERSON>, American author and poet (b. 1937)", "html": "2014 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author and poet (b. 1937)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author and poet (b. 1937)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "2015", "text": "<PERSON>, Irish singer and television host (b. 1927)", "html": "2015 - <a href=\"https://wikipedia.org/wiki/Val_Doo<PERSON>n\" title=\"<PERSON>\"><PERSON></a>, Irish singer and television host (b. 1927)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Val_Doo<PERSON>n\" title=\"<PERSON>\"><PERSON></a>, Irish singer and television host (b. 1927)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Val_Doo<PERSON>n"}]}, {"year": "2015", "text": "<PERSON><PERSON><PERSON>, Polish mathematician and academic (b. 1931)", "html": "2015 - <a href=\"https://wikipedia.org/wiki/Czes%C5%82aw_<PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Polish mathematician and academic (b. 1931)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Czes%C5%82aw_<PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Polish mathematician and academic (b. 1931)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Czes%C5%82aw_Olech"}]}, {"year": "2015", "text": "<PERSON>, English lieutenant and humanitarian (b. 1909)", "html": "2015 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English lieutenant and humanitarian (b. 1909)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English lieutenant and humanitarian (b. 1909)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2016", "text": "<PERSON>, English author and film director (b. 1929)", "html": "2016 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(film_director)\" title=\"<PERSON> (film director)\"><PERSON></a>, English author and film director (b. 1929)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(film_director)\" title=\"<PERSON> (film director)\"><PERSON></a>, English author and film director (b. 1929)", "links": [{"title": "<PERSON> (film director)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(film_director)"}]}, {"year": "2019", "text": "<PERSON><PERSON><PERSON>, Polish composer (b. 1929)", "html": "2019 - <a href=\"https://wikipedia.org/wiki/Bogus%C5%82aw_<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Polish composer (b. 1929)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Bogus%C5%82aw_<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Polish composer (b. 1929)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Bogus%C5%82aw_<PERSON><PERSON><PERSON><PERSON>"}]}, {"year": "2021", "text": "<PERSON>, Dutch composer (b. 1939)", "html": "2021 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch composer (b. 1939)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch composer (b. 1939)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "2023", "text": "<PERSON><PERSON><PERSON>, Dutch race car driver (b. 2004)", "html": "2023 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_%27t_Hoff\" title=\"<PERSON><PERSON><PERSON> van 't Hoff\"><PERSON><PERSON><PERSON> 't <PERSON></a>, Dutch race car driver (b. 2004)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_%27t_Hoff\" title=\"<PERSON><PERSON><PERSON> van 't Hoff\"><PERSON><PERSON><PERSON> 't <PERSON></a>, Dutch race car driver (b. 2004)", "links": [{"title": "<PERSON><PERSON><PERSON> 't <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_%27t_Hoff"}]}, {"year": "2024", "text": "<PERSON>, Albanian novelist (b. 1936)", "html": "2024 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Albanian novelist (b. 1936)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Albanian novelist (b. 1936)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2024", "text": "<PERSON>, American screenwriter (b. 1934)", "html": "2024 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American screenwriter (b. 1934)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American screenwriter (b. 1934)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}]}}