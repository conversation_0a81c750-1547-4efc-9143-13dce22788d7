{"date": "June 12", "url": "https://wikipedia.org/wiki/June_12", "data": {"Events": [{"year": "910", "text": "Battle of Augsburg: The Hungarians defeat the East Frankish army under King <PERSON>, using the famous feigned retreat tactic of the nomadic warriors.", "html": "910 - <a href=\"https://wikipedia.org/wiki/Battle_of_Lechfeld_(910)\" title=\"Battle of Lechfeld (910)\">Battle of Augsburg</a>: The <a href=\"https://wikipedia.org/wiki/Hungarians\" title=\"Hungarians\">Hungarians</a> defeat the <a href=\"https://wikipedia.org/wiki/East_Francia\" title=\"East Francia\">East Frankish</a> army under King <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_Child\" title=\"<PERSON> the Child\"><PERSON> the <PERSON></a>, using the famous <a href=\"https://wikipedia.org/wiki/Feigned_retreat\" title=\"Feigned retreat\">feigned retreat</a> tactic of the nomadic warriors.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Battle_of_Lechfeld_(910)\" title=\"Battle of Lechfeld (910)\">Battle of Augsburg</a>: The <a href=\"https://wikipedia.org/wiki/Hungarians\" title=\"Hungarians\">Hungarians</a> defeat the <a href=\"https://wikipedia.org/wiki/East_Francia\" title=\"East Francia\">East Frankish</a> army under King <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_Child\" title=\"<PERSON> the Child\"><PERSON> the <PERSON></a>, using the famous <a href=\"https://wikipedia.org/wiki/Feigned_retreat\" title=\"Feigned retreat\">feigned retreat</a> tactic of the nomadic warriors.", "links": [{"title": "Battle of Lechfeld (910)", "link": "https://wikipedia.org/wiki/Battle_of_Lechfeld_(910)"}, {"title": "Hungarians", "link": "https://wikipedia.org/wiki/Hungarians"}, {"title": "East Francia", "link": "https://wikipedia.org/wiki/East_Francia"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "Feigned retreat", "link": "https://wikipedia.org/wiki/Feigned_retreat"}]}, {"year": "1206", "text": "The Ghurid general <PERSON><PERSON><PERSON> <PERSON><PERSON><PERSON> founds the Delhi Sultanate. ", "html": "1206 - The <a href=\"https://wikipedia.org/wiki/Ghurid_dynasty\" title=\"Ghurid dynasty\"><PERSON><PERSON><PERSON></a> general <a href=\"https://wikipedia.org/wiki/Qut<PERSON>_ud-<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON> u<PERSON><PERSON><PERSON>\">Qutb u<PERSON>-<PERSON></a> founds the <a href=\"https://wikipedia.org/wiki/Delhi_Sultanate\" title=\"Delhi Sultanate\">Delhi Sultanate</a>. ", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Ghurid_dynasty\" title=\"Ghurid dynasty\"><PERSON>hur<PERSON></a> general <a href=\"https://wikipedia.org/wiki/Qutb_ud-<PERSON>_<PERSON>\" title=\"<PERSON>ut<PERSON> u<PERSON>-<PERSON>\"><PERSON>utb ud-<PERSON></a> founds the <a href=\"https://wikipedia.org/wiki/Delhi_Sultanate\" title=\"Delhi Sultanate\">Delhi Sultanate</a>. ", "links": [{"title": "Ghurid dynasty", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_dynasty"}, {"title": "<PERSON><PERSON><PERSON> <PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Qutb_ud-<PERSON>_<PERSON>"}, {"title": "Delhi Sultanate", "link": "https://wikipedia.org/wiki/Delhi_Sultanate"}]}, {"year": "1240", "text": "At the instigation of <PERSON> of <PERSON>, an inter-faith debate, known as the Disputation of Paris, starts between a Christian monk and four rabbis.", "html": "1240 - At the instigation of <a href=\"https://wikipedia.org/wiki/Louis_IX_of_France\" title=\"<PERSON> IX of France\"><PERSON> of France</a>, an inter-faith debate, known as the <a href=\"https://wikipedia.org/wiki/Disputation_of_Paris\" title=\"Disputation of Paris\">Disputation of Paris</a>, starts between a Christian monk and four <a href=\"https://wikipedia.org/wiki/Rabbi\" title=\"Rabbi\">rabbis</a>.", "no_year_html": "At the instigation of <a href=\"https://wikipedia.org/wiki/Louis_IX_of_France\" title=\"<PERSON> IX of France\"><PERSON> of France</a>, an inter-faith debate, known as the <a href=\"https://wikipedia.org/wiki/Disputation_of_Paris\" title=\"Disputation of Paris\">Disputation of Paris</a>, starts between a Christian monk and four <a href=\"https://wikipedia.org/wiki/Rabbi\" title=\"Rabbi\">rabbis</a>.", "links": [{"title": "<PERSON> of France", "link": "https://wikipedia.org/wiki/Louis_IX_of_France"}, {"title": "Disputation of Paris", "link": "https://wikipedia.org/wiki/Disputation_of_Paris"}, {"title": "Rabbi", "link": "https://wikipedia.org/wiki/Rabbi"}]}, {"year": "1381", "text": "Peasants' Revolt: In England, rebels assemble at Blackheath, just outside London.", "html": "1381 - <a href=\"https://wikipedia.org/wiki/Peasants%27_Revolt\" title=\"Peasants' Revolt\">Peasants' Revolt</a>: In <a href=\"https://wikipedia.org/wiki/Kingdom_of_England\" title=\"Kingdom of England\">England</a>, rebels assemble at <a href=\"https://wikipedia.org/wiki/Blackheath,_London\" title=\"Blackheath, London\">Blackheath</a>, just outside London.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Peasants%27_Revolt\" title=\"Peasants' Revolt\">Peasants' Revolt</a>: In <a href=\"https://wikipedia.org/wiki/Kingdom_of_England\" title=\"Kingdom of England\">England</a>, rebels assemble at <a href=\"https://wikipedia.org/wiki/Blackheath,_London\" title=\"Blackheath, London\">Blackheath</a>, just outside London.", "links": [{"title": "Peasants' Revolt", "link": "https://wikipedia.org/wiki/Peasants%27_<PERSON>olt"}, {"title": "Kingdom of England", "link": "https://wikipedia.org/wiki/Kingdom_of_England"}, {"title": "Blackheath, London", "link": "https://wikipedia.org/wiki/Blackheath,_London"}]}, {"year": "1418", "text": "Armagnac-Burgundian Civil War: Parisians slaughter sympathizers of <PERSON>, Count of Armagnac, along with all prisoners, foreign bankers, and students and faculty of the College of Navarre.", "html": "1418 - <a href=\"https://wikipedia.org/wiki/Armagnac%E2%80%93Burgundian_Civil_War\" title=\"Armagnac-Burgundian Civil War\">Armagnac-Burgundian Civil War</a>: Parisians slaughter sympathizers of <a href=\"https://wikipedia.org/wiki/<PERSON>,_Count_of_Armagnac\" title=\"<PERSON>, Count of Armagnac\"><PERSON>, Count of Armagnac</a>, along with all prisoners, foreign bankers, and students and faculty of the <a href=\"https://wikipedia.org/wiki/College_of_Navarre\" title=\"College of Navarre\">College of Navarre</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Armagnac%E2%80%93Burgundian_Civil_War\" title=\"Armagnac-Burgundian Civil War\">Armagnac-Burgundian Civil War</a>: Parisians slaughter sympathizers of <a href=\"https://wikipedia.org/wiki/<PERSON>,_Count_<PERSON>_Armagnac\" title=\"<PERSON>, Count of Armagnac\"><PERSON>, Count of Armagnac</a>, along with all prisoners, foreign bankers, and students and faculty of the <a href=\"https://wikipedia.org/wiki/College_of_Navarre\" title=\"College of Navarre\">College of Navarre</a>.", "links": [{"title": "Armagnac-Burgundian Civil War", "link": "https://wikipedia.org/wiki/Armagnac%E2%80%93Burgundian_Civil_War"}, {"title": "<PERSON>, Count of Armagnac", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Count_<PERSON>_<PERSON>"}, {"title": "College of Navarre", "link": "https://wikipedia.org/wiki/College_of_Navarre"}]}, {"year": "1429", "text": "Hundred Years' War: On the second day of the Battle of Jargeau, <PERSON> of Arc leads the French army in their capture of the city and the English commander, <PERSON>, 1st Duke of Suffolk.", "html": "1429 - <a href=\"https://wikipedia.org/wiki/Hundred_Years%27_War\" title=\"Hundred Years' War\">Hundred Years' War</a>: On the second day of the <a href=\"https://wikipedia.org/wiki/Battle_of_Jargeau\" title=\"Battle of Jargeau\">Battle of Jargeau</a>, <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_Arc\" title=\"Joan of Arc\"><PERSON> of Arc</a> leads the French army in their capture of the city and the English commander, <a href=\"https://wikipedia.org/wiki/<PERSON>,_1st_Duke_of_Suffolk\" title=\"<PERSON>, 1st Duke of Suffolk\"><PERSON>, 1st Duke of Suffolk</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Hundred_Years%27_War\" title=\"Hundred Years' War\">Hundred Years' War</a>: On the second day of the <a href=\"https://wikipedia.org/wiki/Battle_of_Jargeau\" title=\"Battle of Jargeau\">Battle of Jargeau</a>, <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"Joan of Arc\"><PERSON> of Arc</a> leads the French army in their capture of the city and the English commander, <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_1st_Duke_of_Suffolk\" title=\"<PERSON>, 1st Duke of Suffolk\"><PERSON>, 1st Duke of Suffolk</a>.", "links": [{"title": "Hundred Years' War", "link": "https://wikipedia.org/wiki/Hundred_Years%27_War"}, {"title": "Battle of Jargeau", "link": "https://wikipedia.org/wiki/Battle_of_Jargeau"}, {"title": "<PERSON> of Arc", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "<PERSON>, 1st Duke of Suffolk", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>,_1st_Duke_of_Suffolk"}]}, {"year": "1550", "text": "The city of Helsinki, Finland (belonging to Sweden at the time) is founded by King <PERSON> of Sweden.", "html": "1550 - The city of <a href=\"https://wikipedia.org/wiki/Helsinki\" title=\"Helsinki\">Helsinki</a>, Finland (belonging to Sweden at the time) is founded by King <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Sweden\" class=\"mw-redirect\" title=\"<PERSON> of Sweden\"><PERSON> of Sweden</a>.", "no_year_html": "The city of <a href=\"https://wikipedia.org/wiki/Helsinki\" title=\"Helsinki\">Helsinki</a>, Finland (belonging to Sweden at the time) is founded by King <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Sweden\" class=\"mw-redirect\" title=\"<PERSON> of Sweden\"><PERSON> of Sweden</a>.", "links": [{"title": "Helsinki", "link": "https://wikipedia.org/wiki/Helsinki"}, {"title": "<PERSON> of Sweden", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_Sweden"}]}, {"year": "1643", "text": "The Westminster Assembly is convened by the Parliament of England, without the assent of <PERSON>, in order to restructure the Church of England.", "html": "1643 - The <a href=\"https://wikipedia.org/wiki/Westminster_Assembly\" title=\"Westminster Assembly\">Westminster Assembly</a> is convened by the <a href=\"https://wikipedia.org/wiki/Parliament_of_England\" title=\"Parliament of England\">Parliament of England</a>, without the assent of <a href=\"https://wikipedia.org/wiki/Charles_<PERSON>_of_England\" title=\"Charles I of England\"><PERSON></a>, in order to restructure the <a href=\"https://wikipedia.org/wiki/Church_of_England\" title=\"Church of England\">Church of England</a>.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Westminster_Assembly\" title=\"Westminster Assembly\">Westminster Assembly</a> is convened by the <a href=\"https://wikipedia.org/wiki/Parliament_of_England\" title=\"Parliament of England\">Parliament of England</a>, without the assent of <a href=\"https://wikipedia.org/wiki/Charles_I_of_England\" title=\"Charles I of England\"><PERSON> I</a>, in order to restructure the <a href=\"https://wikipedia.org/wiki/Church_of_England\" title=\"Church of England\">Church of England</a>.", "links": [{"title": "Westminster Assembly", "link": "https://wikipedia.org/wiki/Westminster_Assembly"}, {"title": "Parliament of England", "link": "https://wikipedia.org/wiki/Parliament_of_England"}, {"title": "<PERSON> of England", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_England"}, {"title": "Church of England", "link": "https://wikipedia.org/wiki/Church_of_England"}]}, {"year": "1653", "text": "First Anglo-Dutch War: The Battle of the Gabbard begins, lasting until the following day.", "html": "1653 - <a href=\"https://wikipedia.org/wiki/First_Anglo-Dutch_War\" title=\"First Anglo-Dutch War\">First Anglo-Dutch War</a>: The <a href=\"https://wikipedia.org/wiki/Battle_of_the_Gabbard\" title=\"Battle of the Gabbard\">Battle of the Gabbard</a> begins, lasting until the following day.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/First_Anglo-Dutch_War\" title=\"First Anglo-Dutch War\">First Anglo-Dutch War</a>: The <a href=\"https://wikipedia.org/wiki/Battle_of_the_Gabbard\" title=\"Battle of the Gabbard\">Battle of the Gabbard</a> begins, lasting until the following day.", "links": [{"title": "First Anglo-Dutch War", "link": "https://wikipedia.org/wiki/First_Anglo-Dutch_War"}, {"title": "Battle of the Gabbard", "link": "https://wikipedia.org/wiki/Battle_of_the_Gabbard"}]}, {"year": "1665", "text": "<PERSON> is appointed the first mayor of New York City.", "html": "1665 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> is appointed the first mayor of New York City.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> is appointed the first mayor of New York City.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1758", "text": "French and Indian War: Siege of Louisbourg: <PERSON>'s attack at Louisbourg, Nova Scotia, commences.", "html": "1758 - <a href=\"https://wikipedia.org/wiki/French_and_Indian_War\" title=\"French and Indian War\">French and Indian War</a>: <a href=\"https://wikipedia.org/wiki/Siege_of_Louisbourg_(1758)\" title=\"Siege of Louisbourg (1758)\">Siege of Louisbourg</a>: <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>'s attack at <a href=\"https://wikipedia.org/wiki/Louisbourg,_Nova_Scotia\" class=\"mw-redirect\" title=\"Louisbourg, Nova Scotia\">Louisbourg, Nova Scotia</a>, commences.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/French_and_Indian_War\" title=\"French and Indian War\">French and Indian War</a>: <a href=\"https://wikipedia.org/wiki/Siege_of_Louisbourg_(1758)\" title=\"Siege of Louisbourg (1758)\">Siege of Louisbourg</a>: <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>'s attack at <a href=\"https://wikipedia.org/wiki/Louisbourg,_Nova_Scotia\" class=\"mw-redirect\" title=\"Louisbourg, Nova Scotia\">Louisbourg, Nova Scotia</a>, commences.", "links": [{"title": "French and Indian War", "link": "https://wikipedia.org/wiki/French_and_Indian_War"}, {"title": "Siege of Louisbourg (1758)", "link": "https://wikipedia.org/wiki/Siege_of_Louisbourg_(1758)"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Louisbourg, Nova Scotia", "link": "https://wikipedia.org/wiki/Louisbourg,_Nova_Scotia"}]}, {"year": "1772", "text": "French explorer <PERSON><PERSON><PERSON> and 25 of his men are killed by Māori in New Zealand.", "html": "1772 - French explorer <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a> and 25 of his men are killed by <a href=\"https://wikipedia.org/wiki/M%C4%81ori_people\" title=\"Māori people\">Māori</a> in <a href=\"https://wikipedia.org/wiki/New_Zealand\" title=\"New Zealand\">New Zealand</a>.", "no_year_html": "French explorer <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a> and 25 of his men are killed by <a href=\"https://wikipedia.org/wiki/M%C4%81ori_people\" title=\"Māori people\">Māori</a> in <a href=\"https://wikipedia.org/wiki/New_Zealand\" title=\"New Zealand\">New Zealand</a>.", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>_<PERSON>"}, {"title": "Māori people", "link": "https://wikipedia.org/wiki/M%C4%81ori_people"}, {"title": "New Zealand", "link": "https://wikipedia.org/wiki/New_Zealand"}]}, {"year": "1775", "text": "American War of Independence: British general <PERSON> declares martial law in Massachusetts. The British offer a pardon to all colonists who lay down their arms. There would be only two exceptions to the amnesty: <PERSON> and <PERSON>, if captured, were to be hanged.", "html": "1775 - <a href=\"https://wikipedia.org/wiki/American_War_of_Independence\" class=\"mw-redirect\" title=\"American War of Independence\">American War of Independence</a>: <a href=\"https://wikipedia.org/wiki/British_Army\" title=\"British Army\">British general</a> <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> declares <a href=\"https://wikipedia.org/wiki/Martial_law\" title=\"Martial law\">martial law</a> in <a href=\"https://wikipedia.org/wiki/Massachusetts_Colony\" class=\"mw-redirect\" title=\"Massachusetts Colony\">Massachusetts</a>. The British offer a pardon to all colonists who lay down their arms. There would be only two exceptions to the amnesty: <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> and <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, if captured, were to be hanged.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/American_War_of_Independence\" class=\"mw-redirect\" title=\"American War of Independence\">American War of Independence</a>: <a href=\"https://wikipedia.org/wiki/British_Army\" title=\"British Army\">British general</a> <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"Thomas <PERSON>\"><PERSON></a> declares <a href=\"https://wikipedia.org/wiki/Martial_law\" title=\"Martial law\">martial law</a> in <a href=\"https://wikipedia.org/wiki/Massachusetts_Colony\" class=\"mw-redirect\" title=\"Massachusetts Colony\">Massachusetts</a>. The British offer a pardon to all colonists who lay down their arms. There would be only two exceptions to the amnesty: <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> and <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, if captured, were to be hanged.", "links": [{"title": "American War of Independence", "link": "https://wikipedia.org/wiki/American_War_of_Independence"}, {"title": "British Army", "link": "https://wikipedia.org/wiki/British_Army"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Martial law", "link": "https://wikipedia.org/wiki/Martial_law"}, {"title": "Massachusetts Colony", "link": "https://wikipedia.org/wiki/Massachusetts_Colony"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1776", "text": "The Virginia Declaration of Rights is adopted.", "html": "1776 - The <a href=\"https://wikipedia.org/wiki/Virginia_Declaration_of_Rights\" title=\"Virginia Declaration of Rights\">Virginia Declaration of Rights</a> is adopted.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Virginia_Declaration_of_Rights\" title=\"Virginia Declaration of Rights\">Virginia Declaration of Rights</a> is adopted.", "links": [{"title": "Virginia Declaration of Rights", "link": "https://wikipedia.org/wiki/Virginia_Declaration_of_Rights"}]}, {"year": "1798", "text": "Irish Rebellion of 1798: Battle of Ballynahinch.", "html": "1798 - <a href=\"https://wikipedia.org/wiki/Irish_Rebellion_of_1798\" title=\"Irish Rebellion of 1798\">Irish Rebellion of 1798</a>: <a href=\"https://wikipedia.org/wiki/Battle_of_Ballynahinch\" title=\"Battle of Ballynahinch\">Battle of Ballynahinch</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Irish_Rebellion_of_1798\" title=\"Irish Rebellion of 1798\">Irish Rebellion of 1798</a>: <a href=\"https://wikipedia.org/wiki/Battle_of_Ballynahinch\" title=\"Battle of Ballynahinch\">Battle of Ballynahinch</a>.", "links": [{"title": "Irish Rebellion of 1798", "link": "https://wikipedia.org/wiki/Irish_Rebellion_of_1798"}, {"title": "Battle of Ballynahinch", "link": "https://wikipedia.org/wiki/Battle_of_Ballynahinch"}]}, {"year": "1817", "text": "The earliest form of bicycle, the dandy horse, is driven by <PERSON>.", "html": "1817 - The earliest form of <a href=\"https://wikipedia.org/wiki/Bicycle\" title=\"Bicycle\">bicycle</a>, the <a href=\"https://wikipedia.org/wiki/Dandy_horse\" title=\"Dandy horse\">dandy horse</a>, is driven by <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>.", "no_year_html": "The earliest form of <a href=\"https://wikipedia.org/wiki/Bicycle\" title=\"Bicycle\">bicycle</a>, the <a href=\"https://wikipedia.org/wiki/Dandy_horse\" title=\"Dandy horse\">dandy horse</a>, is driven by <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>.", "links": [{"title": "Bicycle", "link": "https://wikipedia.org/wiki/Bicycle"}, {"title": "Dandy horse", "link": "https://wikipedia.org/wiki/Dandy_horse"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1821", "text": "<PERSON><PERSON>, king of Sennar, surrenders his throne and realm to <PERSON><PERSON><PERSON><PERSON>, general of the Ottoman Empire, ending the existence of that Sudanese kingdom.", "html": "1821 - <a href=\"https://wikipedia.org/wiki/Badi_VII\" title=\"Badi VII\"><PERSON><PERSON> VII</a>, king of <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_(sultanate)\" class=\"mw-redirect\" title=\"<PERSON><PERSON> (sultanate)\"><PERSON><PERSON></a>, surrenders his throne and realm to <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>%27il_Pasha\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, general of the <a href=\"https://wikipedia.org/wiki/Ottoman_Empire\" title=\"Ottoman Empire\">Ottoman Empire</a>, ending the existence of that Sudanese kingdom.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Bad<PERSON>_VII\" title=\"Badi VII\"><PERSON><PERSON> VII</a>, king of <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_(sultanate)\" class=\"mw-redirect\" title=\"<PERSON><PERSON> (sultanate)\"><PERSON><PERSON></a>, surrenders his throne and realm to <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>%27il_Pasha\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, general of the <a href=\"https://wikipedia.org/wiki/Ottoman_Empire\" title=\"Ottoman Empire\">Ottoman Empire</a>, ending the existence of that Sudanese kingdom.", "links": [{"title": "Badi VII", "link": "https://wikipedia.org/wiki/Badi_VII"}, {"title": "Sennar (sultanate)", "link": "https://wikipedia.org/wiki/Sennar_(sultanate)"}, {"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Isma%27il_<PERSON>"}, {"title": "Ottoman Empire", "link": "https://wikipedia.org/wiki/Ottoman_Empire"}]}, {"year": "1830", "text": "Beginning of the Invasion of Algiers: Thirty-four thousand French soldiers land 27 kilometers west of Algiers, at Sidi Ferruch.", "html": "1830 - Beginning of the <a href=\"https://wikipedia.org/wiki/Invasion_of_Algiers_in_1830\" class=\"mw-redirect\" title=\"Invasion of Algiers in 1830\">Invasion of Algiers</a>: Thirty-four thousand French soldiers land 27 kilometers west of Algiers, at Sidi Ferruch.", "no_year_html": "Beginning of the <a href=\"https://wikipedia.org/wiki/Invasion_of_Algiers_in_1830\" class=\"mw-redirect\" title=\"Invasion of Algiers in 1830\">Invasion of Algiers</a>: Thirty-four thousand French soldiers land 27 kilometers west of Algiers, at Sidi Ferruch.", "links": [{"title": "Invasion of Algiers in 1830", "link": "https://wikipedia.org/wiki/Invasion_of_Algiers_in_1830"}]}, {"year": "1864", "text": "American Civil War, Overland Campaign: Battle of Cold Harbor: <PERSON> gives the Confederate forces under <PERSON> a victory when he pulls his Union troops from their position at Cold Harbor, Virginia and moves south.", "html": "1864 - <a href=\"https://wikipedia.org/wiki/American_Civil_War\" title=\"American Civil War\">American Civil War</a>, <a href=\"https://wikipedia.org/wiki/Overland_Campaign\" title=\"Overland Campaign\">Overland Campaign</a>: <a href=\"https://wikipedia.org/wiki/Battle_of_Cold_Harbor\" title=\"Battle of Cold Harbor\">Battle of Cold Harbor</a>: <a href=\"https://wikipedia.org/wiki/Ulysses_<PERSON>_Grant\" title=\"Ulysses <PERSON> Grant\"><PERSON></a> gives the Confederate forces under <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> a victory when he pulls his Union troops from their position at <a href=\"https://wikipedia.org/wiki/Cold_Harbor,_Virginia\" title=\"Cold Harbor, Virginia\">Cold Harbor, Virginia</a> and moves south.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/American_Civil_War\" title=\"American Civil War\">American Civil War</a>, <a href=\"https://wikipedia.org/wiki/Overland_Campaign\" title=\"Overland Campaign\">Overland Campaign</a>: <a href=\"https://wikipedia.org/wiki/Battle_of_Cold_Harbor\" title=\"Battle of Cold Harbor\">Battle of Cold Harbor</a>: <a href=\"https://wikipedia.org/wiki/Ulysses_<PERSON>_Grant\" title=\"Ulysses <PERSON> Grant\"><PERSON></a> gives the Confederate forces under <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> a victory when he pulls his Union troops from their position at <a href=\"https://wikipedia.org/wiki/Cold_Harbor,_Virginia\" title=\"Cold Harbor, Virginia\">Cold Harbor, Virginia</a> and moves south.", "links": [{"title": "American Civil War", "link": "https://wikipedia.org/wiki/American_Civil_War"}, {"title": "Overland Campaign", "link": "https://wikipedia.org/wiki/Overland_Campaign"}, {"title": "Battle of Cold Harbor", "link": "https://wikipedia.org/wiki/Battle_of_Cold_Harbor"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>"}, {"title": "Cold Harbor, Virginia", "link": "https://wikipedia.org/wiki/Cold_Harbor,_Virginia"}]}, {"year": "1898", "text": "Philippine Declaration of Independence: General <PERSON> declares the Philippines' independence from Spain.", "html": "1898 - <a href=\"https://wikipedia.org/wiki/Philippine_Declaration_of_Independence\" title=\"Philippine Declaration of Independence\">Philippine Declaration of Independence</a>: General <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> declares the <a href=\"https://wikipedia.org/wiki/Philippines\" title=\"Philippines\">Philippines</a>' independence from Spain.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Philippine_Declaration_of_Independence\" title=\"Philippine Declaration of Independence\">Philippine Declaration of Independence</a>: General <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> declares the <a href=\"https://wikipedia.org/wiki/Philippines\" title=\"Philippines\">Philippines</a>' independence from Spain.", "links": [{"title": "Philippine Declaration of Independence", "link": "https://wikipedia.org/wiki/Philippine_Declaration_of_Independence"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Philippines", "link": "https://wikipedia.org/wiki/Philippines"}]}, {"year": "1899", "text": "New Richmond tornado: The ninth deadliest tornado in U.S. history kills 117 people and injures around 200.", "html": "1899 - <a href=\"https://wikipedia.org/wiki/New_Richmond_tornado\" class=\"mw-redirect\" title=\"New Richmond tornado\">New Richmond tornado</a>: The ninth deadliest <a href=\"https://wikipedia.org/wiki/Tornado\" title=\"Tornado\">tornado</a> in U.S. history kills 117 people and injures around 200.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/New_Richmond_tornado\" class=\"mw-redirect\" title=\"New Richmond tornado\">New Richmond tornado</a>: The ninth deadliest <a href=\"https://wikipedia.org/wiki/Tornado\" title=\"Tornado\">tornado</a> in U.S. history kills 117 people and injures around 200.", "links": [{"title": "New Richmond tornado", "link": "https://wikipedia.org/wiki/New_Richmond_tornado"}, {"title": "Tornado", "link": "https://wikipedia.org/wiki/Tornado"}]}, {"year": "1900", "text": "The Reichstag approves new legislation continuing Germany's naval expansion program, providing for construction of 38 battleships over a 20-year period. Germany's fleet would be the largest in the world.", "html": "1900 - The <a href=\"https://wikipedia.org/wiki/Reichstag_building\" title=\"Reichstag building\">Reichstag</a> approves new legislation continuing Germany's naval expansion program, providing for construction of 38 battleships over a 20-year period. Germany's fleet would be the largest in the world.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Reichstag_building\" title=\"Reichstag building\">Reichstag</a> approves new legislation continuing Germany's naval expansion program, providing for construction of 38 battleships over a 20-year period. Germany's fleet would be the largest in the world.", "links": [{"title": "Reichstag building", "link": "https://wikipedia.org/wiki/Reichstag_building"}]}, {"year": "1914", "text": "Massacre of Phocaea: Turkish irregulars slaughter 50 to 100 Greeks and expel thousands of others in an ethnic cleansing operation in the Ottoman Empire.", "html": "1914 - <a href=\"https://wikipedia.org/wiki/Massacre_of_Phocaea\" title=\"Massacre of Phocaea\">Massacre of Phocaea</a>: Turkish irregulars slaughter 50 to 100 Greeks and expel thousands of others in an <a href=\"https://wikipedia.org/wiki/Ethnic_cleansing\" title=\"Ethnic cleansing\">ethnic cleansing</a> operation in the <a href=\"https://wikipedia.org/wiki/Ottoman_Empire\" title=\"Ottoman Empire\">Ottoman Empire</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Massacre_of_Phocaea\" title=\"Massacre of Phocaea\">Massacre of Phocaea</a>: Turkish irregulars slaughter 50 to 100 Greeks and expel thousands of others in an <a href=\"https://wikipedia.org/wiki/Ethnic_cleansing\" title=\"Ethnic cleansing\">ethnic cleansing</a> operation in the <a href=\"https://wikipedia.org/wiki/Ottoman_Empire\" title=\"Ottoman Empire\">Ottoman Empire</a>.", "links": [{"title": "Massacre of Phocaea", "link": "https://wikipedia.org/wiki/Massacre_of_Phocaea"}, {"title": "Ethnic cleansing", "link": "https://wikipedia.org/wiki/Ethnic_cleansing"}, {"title": "Ottoman Empire", "link": "https://wikipedia.org/wiki/Ottoman_Empire"}]}, {"year": "1921", "text": "<PERSON> orders the use of chemical weapons against the Tambov Rebellion, bringing an end to the peasant uprising.", "html": "1921 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> orders the use of <a href=\"https://wikipedia.org/wiki/Chemical_weapons\" class=\"mw-redirect\" title=\"Chemical weapons\">chemical weapons</a> against the <a href=\"https://wikipedia.org/wiki/Tambov_Rebellion\" title=\"Tambov Rebellion\">Tambov Rebellion</a>, bringing an end to the peasant uprising.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> orders the use of <a href=\"https://wikipedia.org/wiki/Chemical_weapons\" class=\"mw-redirect\" title=\"Chemical weapons\">chemical weapons</a> against the <a href=\"https://wikipedia.org/wiki/Tambov_Rebellion\" title=\"Tambov Rebellion\">Tambov Rebellion</a>, bringing an end to the peasant uprising.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}, {"title": "Chemical weapons", "link": "https://wikipedia.org/wiki/Chemical_weapons"}, {"title": "Tambov Rebellion", "link": "https://wikipedia.org/wiki/Tambov_Rebellion"}]}, {"year": "1935", "text": "A ceasefire is negotiated between Bolivia and Paraguay, ending the Chaco War.", "html": "1935 - A ceasefire is negotiated between <a href=\"https://wikipedia.org/wiki/Bolivia\" title=\"Bolivia\">Bolivia</a> and <a href=\"https://wikipedia.org/wiki/Paraguay\" title=\"Paraguay\">Paraguay</a>, ending the <a href=\"https://wikipedia.org/wiki/Chaco_War\" title=\"Chaco War\">Chaco War</a>.", "no_year_html": "A ceasefire is negotiated between <a href=\"https://wikipedia.org/wiki/Bolivia\" title=\"Bolivia\">Bolivia</a> and <a href=\"https://wikipedia.org/wiki/Paraguay\" title=\"Paraguay\">Paraguay</a>, ending the <a href=\"https://wikipedia.org/wiki/Chaco_War\" title=\"Chaco War\">Chaco War</a>.", "links": [{"title": "Bolivia", "link": "https://wikipedia.org/wiki/Bolivia"}, {"title": "Paraguay", "link": "https://wikipedia.org/wiki/Paraguay"}, {"title": "Chaco War", "link": "https://wikipedia.org/wiki/Chaco_War"}]}, {"year": "1939", "text": "Shooting begins on Paramount Pictures' <PERSON><PERSON>, the first horror film photographed in three-strip Technicolor.", "html": "1939 - Shooting begins on <a href=\"https://wikipedia.org/wiki/Paramount_Pictures\" title=\"Paramount Pictures\">Paramount Pictures</a>' <i><a href=\"https://wikipedia.org/wiki/Dr._<PERSON><PERSON><PERSON>\" title=\"Dr. Cyclops\">Dr. <PERSON><PERSON></a></i>, the first horror film photographed in <a href=\"https://wikipedia.org/wiki/Technicolor#Three-strip_Technicolor\" title=\"Technicolor\">three-strip Technicolor</a>.", "no_year_html": "Shooting begins on <a href=\"https://wikipedia.org/wiki/Paramount_Pictures\" title=\"Paramount Pictures\">Paramount Pictures</a>' <i><a href=\"https://wikipedia.org/wiki/Dr._<PERSON><PERSON><PERSON>\" title=\"Dr. Cyclops\">Dr. <PERSON><PERSON></a></i>, the first horror film photographed in <a href=\"https://wikipedia.org/wiki/Technicolor#Three-strip_Technicolor\" title=\"Technicolor\">three-strip Technicolor</a>.", "links": [{"title": "Paramount Pictures", "link": "https://wikipedia.org/wiki/Paramount_Pictures"}, {"title": "<PERSON>. <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>"}, {"title": "Technicolor", "link": "https://wikipedia.org/wiki/Technicolor#Three-strip_Technicolor"}]}, {"year": "1939", "text": "The Baseball Hall of Fame opens in Cooperstown, New York.", "html": "1939 - The <a href=\"https://wikipedia.org/wiki/Baseball_Hall_of_Fame\" class=\"mw-redirect\" title=\"Baseball Hall of Fame\">Baseball Hall of Fame</a> opens in <a href=\"https://wikipedia.org/wiki/Cooperstown,_New_York\" title=\"Cooperstown, New York\">Cooperstown, New York</a>.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Baseball_Hall_of_Fame\" class=\"mw-redirect\" title=\"Baseball Hall of Fame\">Baseball Hall of Fame</a> opens in <a href=\"https://wikipedia.org/wiki/Cooperstown,_New_York\" title=\"Cooperstown, New York\">Cooperstown, New York</a>.", "links": [{"title": "Baseball Hall of Fame", "link": "https://wikipedia.org/wiki/Baseball_Hall_of_Fame"}, {"title": "Cooperstown, New York", "link": "https://wikipedia.org/wiki/Cooperstown,_New_York"}]}, {"year": "1940", "text": "World War II: Thirteen thousand British and French troops surrender to Major General <PERSON> at Saint-Valery-en-Caux.", "html": "1940 - <a href=\"https://wikipedia.org/wiki/World_War_II\" title=\"World War II\">World War II</a>: Thirteen thousand British and French troops surrender to <a href=\"https://wikipedia.org/wiki/Major_General\" class=\"mw-redirect\" title=\"Major General\">Major General</a> <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> at <a href=\"https://wikipedia.org/wiki/Saint-Valery-en-Caux\" title=\"Saint-Valery-en-Caux\">Saint-Valery-en-Caux</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/World_War_II\" title=\"World War II\">World War II</a>: Thirteen thousand British and French troops surrender to <a href=\"https://wikipedia.org/wiki/Major_General\" class=\"mw-redirect\" title=\"Major General\">Major General</a> <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> at <a href=\"https://wikipedia.org/wiki/Saint-Valery-en-Caux\" title=\"Saint-Valery-en-Caux\">Saint-Valery-en-Caux</a>.", "links": [{"title": "World War II", "link": "https://wikipedia.org/wiki/World_War_II"}, {"title": "Major General", "link": "https://wikipedia.org/wiki/Major_General"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Saint-Valery-en-Caux", "link": "https://wikipedia.org/wiki/Saint-Valery-en-Caux"}]}, {"year": "1942", "text": "<PERSON> receives a diary for her thirteenth birthday.", "html": "1942 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> receives a <a href=\"https://wikipedia.org/wiki/Diary\" title=\"Diary\">diary</a> for her thirteenth birthday.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> receives a <a href=\"https://wikipedia.org/wiki/Diary\" title=\"Diary\">diary</a> for her thirteenth birthday.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Diary", "link": "https://wikipedia.org/wiki/Diary"}]}, {"year": "1943", "text": "The Holocaust: Germany liquidates the Jewish Ghetto in Brzeżany, Poland (now Berezhany, Ukraine). Around 1,180 Jews are led to the city's old Jewish graveyard and shot.", "html": "1943 - <a href=\"https://wikipedia.org/wiki/The_Holocaust\" title=\"The Holocaust\">The Holocaust</a>: <a href=\"https://wikipedia.org/wiki/Nazi_Germany\" title=\"Nazi Germany\">Germany</a> liquidates the <a href=\"https://wikipedia.org/wiki/Jewish_quarter_(diaspora)\" title=\"Jewish quarter (diaspora)\">Jewish Ghetto</a> in Brzeżany, Poland (now <a href=\"https://wikipedia.org/wiki/Berezhany\" title=\"Berezhany\">Berezhany</a>, <a href=\"https://wikipedia.org/wiki/Ukraine\" title=\"Ukraine\">Ukraine</a>). Around 1,180 <a href=\"https://wikipedia.org/wiki/Jews\" title=\"Jews\">Jews</a> are led to the city's old Jewish graveyard and shot.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/The_Holocaust\" title=\"The Holocaust\">The Holocaust</a>: <a href=\"https://wikipedia.org/wiki/Nazi_Germany\" title=\"Nazi Germany\">Germany</a> liquidates the <a href=\"https://wikipedia.org/wiki/Jewish_quarter_(diaspora)\" title=\"Jewish quarter (diaspora)\">Jewish Ghetto</a> in Brzeżany, Poland (now <a href=\"https://wikipedia.org/wiki/Berezhany\" title=\"Berezhany\">Berezhany</a>, <a href=\"https://wikipedia.org/wiki/Ukraine\" title=\"Ukraine\">Ukraine</a>). Around 1,180 <a href=\"https://wikipedia.org/wiki/Jews\" title=\"Jews\">Jews</a> are led to the city's old Jewish graveyard and shot.", "links": [{"title": "The Holocaust", "link": "https://wikipedia.org/wiki/The_Holocaust"}, {"title": "Nazi Germany", "link": "https://wikipedia.org/wiki/Nazi_Germany"}, {"title": "Jewish quarter (diaspora)", "link": "https://wikipedia.org/wiki/Jewish_quarter_(diaspora)"}, {"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>y"}, {"title": "Ukraine", "link": "https://wikipedia.org/wiki/Ukraine"}, {"title": "Jews", "link": "https://wikipedia.org/wiki/Jews"}]}, {"year": "1944", "text": "World War II: Operation Overlord: American paratroopers of the 101st Airborne Division secure the town of Carentan, Normandy, France.", "html": "1944 - World War II: <a href=\"https://wikipedia.org/wiki/Operation_Overlord\" title=\"Operation Overlord\">Operation Overlord</a>: American <a href=\"https://wikipedia.org/wiki/Paratrooper\" title=\"Paratrooper\">paratroopers</a> of the <a href=\"https://wikipedia.org/wiki/101st_Airborne_Division\" title=\"101st Airborne Division\">101st Airborne Division</a> <a href=\"https://wikipedia.org/wiki/Battle_of_Carentan\" title=\"Battle of Carentan\">secure</a> the town of <a href=\"https://wikipedia.org/wiki/Carentan\" title=\"Carentan\">Carentan</a>, <a href=\"https://wikipedia.org/wiki/Normandy\" title=\"Normandy\">Normandy</a>, France.", "no_year_html": "World War II: <a href=\"https://wikipedia.org/wiki/Operation_Overlord\" title=\"Operation Overlord\">Operation Overlord</a>: American <a href=\"https://wikipedia.org/wiki/Paratrooper\" title=\"Paratrooper\">paratroopers</a> of the <a href=\"https://wikipedia.org/wiki/101st_Airborne_Division\" title=\"101st Airborne Division\">101st Airborne Division</a> <a href=\"https://wikipedia.org/wiki/Battle_of_Carentan\" title=\"Battle of Carentan\">secure</a> the town of <a href=\"https://wikipedia.org/wiki/Carentan\" title=\"Carentan\">Carentan</a>, <a href=\"https://wikipedia.org/wiki/Normandy\" title=\"Normandy\">Normandy</a>, France.", "links": [{"title": "Operation Overlord", "link": "https://wikipedia.org/wiki/Operation_Overlord"}, {"title": "Paratrooper", "link": "https://wikipedia.org/wiki/Paratrooper"}, {"title": "101st Airborne Division", "link": "https://wikipedia.org/wiki/101st_Airborne_Division"}, {"title": "Battle of Carentan", "link": "https://wikipedia.org/wiki/Battle_of_Carentan"}, {"title": "Carentan", "link": "https://wikipedia.org/wiki/Carentan"}, {"title": "Normandy", "link": "https://wikipedia.org/wiki/Normandy"}]}, {"year": "1950", "text": "An Air France Douglas DC-4 crashes near Bahrain International Airport, killing 46 people.", "html": "1950 - An <a href=\"https://wikipedia.org/wiki/Air_France\" title=\"Air France\">Air France</a> <a href=\"https://wikipedia.org/wiki/Douglas_DC-4\" title=\"Douglas DC-4\">Douglas DC-4</a> <a href=\"https://wikipedia.org/wiki/1950_Air_France_multiple_Douglas_DC-4_accidents\" title=\"1950 Air France multiple Douglas DC-4 accidents\">crashes</a> near <a href=\"https://wikipedia.org/wiki/Bahrain_International_Airport\" title=\"Bahrain International Airport\">Bahrain International Airport</a>, killing 46 people.", "no_year_html": "An <a href=\"https://wikipedia.org/wiki/Air_France\" title=\"Air France\">Air France</a> <a href=\"https://wikipedia.org/wiki/Douglas_DC-4\" title=\"Douglas DC-4\">Douglas DC-4</a> <a href=\"https://wikipedia.org/wiki/1950_Air_France_multiple_Douglas_DC-4_accidents\" title=\"1950 Air France multiple Douglas DC-4 accidents\">crashes</a> near <a href=\"https://wikipedia.org/wiki/Bahrain_International_Airport\" title=\"Bahrain International Airport\">Bahrain International Airport</a>, killing 46 people.", "links": [{"title": "Air France", "link": "https://wikipedia.org/wiki/Air_France"}, {"title": "Douglas DC-4", "link": "https://wikipedia.org/wiki/Douglas_DC-4"}, {"title": "1950 Air France multiple Douglas DC-4 accidents", "link": "https://wikipedia.org/wiki/1950_Air_France_multiple_Douglas_DC-4_accidents"}, {"title": "Bahrain International Airport", "link": "https://wikipedia.org/wiki/Bahrain_International_Airport"}]}, {"year": "1954", "text": "<PERSON> <PERSON> canonises <PERSON>, who was 14 years old at the time of his death, as a saint, making him at the time the youngest unmartyred saint in the Roman Catholic Church. In 2017, <PERSON> and <PERSON><PERSON><PERSON><PERSON>, aged ten and nine at the time of their deaths, are declared as saints.", "html": "1954 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_XII\" title=\"Pope Pius XII\">Pope <PERSON></a> <a href=\"https://wikipedia.org/wiki/Canonization\" title=\"Canonization\">canonises</a> <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, who was 14 years old at the time of his death, as a <a href=\"https://wikipedia.org/wiki/Saint\" title=\"Saint\">saint</a>, making him at the time the youngest <a href=\"https://wikipedia.org/wiki/Martyr\" title=\"Martyr\">unmartyred</a> saint in the <a href=\"https://wikipedia.org/wiki/Catholic_Church\" title=\"Catholic Church\">Roman Catholic Church</a>. In 2017, <a href=\"https://wikipedia.org/wiki/Francisco_and_Jac<PERSON>a_Marto\" title=\"Francisco and Jac<PERSON>a Marto\"><PERSON> and <PERSON><PERSON><PERSON><PERSON></a>, aged ten and nine at the time of their deaths, are declared as saints.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_XII\" title=\"Pope Pius <PERSON>\">Pope <PERSON></a> <a href=\"https://wikipedia.org/wiki/Canonization\" title=\"Canonization\">canonises</a> <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, who was 14 years old at the time of his death, as a <a href=\"https://wikipedia.org/wiki/Saint\" title=\"Saint\">saint</a>, making him at the time the youngest <a href=\"https://wikipedia.org/wiki/Martyr\" title=\"Martyr\">unmartyred</a> saint in the <a href=\"https://wikipedia.org/wiki/Catholic_Church\" title=\"Catholic Church\">Roman Catholic Church</a>. In 2017, <a href=\"https://wikipedia.org/wiki/Francisco_and_Jacinta_Marto\" title=\"Francisco and Jac<PERSON>a Marto\"><PERSON> and <PERSON><PERSON><PERSON><PERSON></a>, aged ten and nine at the time of their deaths, are declared as saints.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "Canonization", "link": "https://wikipedia.org/wiki/Canonization"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Saint"}, {"title": "Martyr", "link": "https://wikipedia.org/wiki/Martyr"}, {"title": "Catholic Church", "link": "https://wikipedia.org/wiki/Catholic_Church"}, {"title": "Francisco and Jacinta Marto", "link": "https://wikipedia.org/wiki/<PERSON>_and_<PERSON><PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1963", "text": "NAACP field secretary <PERSON><PERSON><PERSON> is murdered in front of his home in Jackson, Mississippi by Ku Klux Klan member <PERSON> during the civil rights movement.", "html": "1963 - <a href=\"https://wikipedia.org/wiki/National_Association_for_the_Advancement_of_Colored_People\" class=\"mw-redirect\" title=\"National Association for the Advancement of Colored People\">NAACP</a> field secretary <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON> <PERSON>\"><PERSON><PERSON><PERSON></a> is murdered in front of his home in <a href=\"https://wikipedia.org/wiki/Jackson,_Mississippi\" title=\"Jackson, Mississippi\">Jackson, Mississippi</a> by <a href=\"https://wikipedia.org/wiki/Ku_Klux_Klan\" title=\"Ku Klux Klan\">Ku Klux Klan</a> member <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> during the <a href=\"https://wikipedia.org/wiki/Civil_rights_movement\" title=\"Civil rights movement\">civil rights movement</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/National_Association_for_the_Advancement_of_Colored_People\" class=\"mw-redirect\" title=\"National Association for the Advancement of Colored People\">NAACP</a> field secretary <a href=\"https://wikipedia.org/wiki/Me<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a> is murdered in front of his home in <a href=\"https://wikipedia.org/wiki/Jackson,_Mississippi\" title=\"Jackson, Mississippi\">Jackson, Mississippi</a> by <a href=\"https://wikipedia.org/wiki/Ku_Klux_Klan\" title=\"Ku Klux Klan\">Ku Klux Klan</a> member <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> during the <a href=\"https://wikipedia.org/wiki/Civil_rights_movement\" title=\"Civil rights movement\">civil rights movement</a>.", "links": [{"title": "National Association for the Advancement of Colored People", "link": "https://wikipedia.org/wiki/National_Association_for_the_Advancement_of_Colored_People"}, {"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Medgar_<PERSON>s"}, {"title": "Jackson, Mississippi", "link": "https://wikipedia.org/wiki/Jackson,_Mississippi"}, {"title": "Ku Klux Klan", "link": "https://wikipedia.org/wiki/Ku_Klux_Klan"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>"}, {"title": "Civil rights movement", "link": "https://wikipedia.org/wiki/Civil_rights_movement"}]}, {"year": "1963", "text": "The film <PERSON>, starring <PERSON> and <PERSON>, is released in US theaters. It was the most expensive film made at the time.", "html": "1963 - The film <i><a href=\"https://wikipedia.org/wiki/<PERSON>_(1963_film)\" title=\"<PERSON> (1963 film)\"><PERSON></a></i>, starring <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> and <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, is released in US theaters. It was the most expensive film made at the time.", "no_year_html": "The film <i><a href=\"https://wikipedia.org/wiki/<PERSON>_(1963_film)\" title=\"<PERSON> (1963 film)\"><PERSON></a></i>, starring <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> and <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, is released in US theaters. It was the most expensive film made at the time.", "links": [{"title": "<PERSON> (1963 film)", "link": "https://wikipedia.org/wiki/<PERSON>_(1963_film)"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1964", "text": "Anti-apartheid activist and ANC leader <PERSON> is sentenced to life in prison for sabotage in South Africa.", "html": "1964 - Anti-<a href=\"https://wikipedia.org/wiki/Apartheid\" title=\"Apartheid\">apartheid</a> activist and <a href=\"https://wikipedia.org/wiki/African_National_Congress\" title=\"African National Congress\">ANC</a> leader <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> is <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>#Arrest_and_Rivonia_trial\" title=\"<PERSON>\">sentenced to life in prison</a> for <a href=\"https://wikipedia.org/wiki/Sabotage\" title=\"Sabotage\">sabotage</a> in South Africa.", "no_year_html": "Anti-<a href=\"https://wikipedia.org/wiki/Apartheid\" title=\"Apartheid\">apartheid</a> activist and <a href=\"https://wikipedia.org/wiki/African_National_Congress\" title=\"African National Congress\">ANC</a> leader <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> is <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>#Arrest_and_Rivonia_trial\" title=\"<PERSON>\">sentenced to life in prison</a> for <a href=\"https://wikipedia.org/wiki/Sabotage\" title=\"Sabotage\">sabotage</a> in South Africa.", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>heid"}, {"title": "African National Congress", "link": "https://wikipedia.org/wiki/African_National_Congress"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Nelson_Mandela"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Nelson_Mandela#Arrest_and_Rivonia_trial"}, {"title": "Sabotage", "link": "https://wikipedia.org/wiki/Sabotage"}]}, {"year": "1967", "text": "The United States Supreme Court in Loving v. Virginia declares all U.S. state laws which prohibit interracial marriage to be unconstitutional.", "html": "1967 - The <a href=\"https://wikipedia.org/wiki/Supreme_Court_of_the_United_States\" title=\"Supreme Court of the United States\">United States Supreme Court</a> in <i><a href=\"https://wikipedia.org/wiki/Loving_v._Virginia\" title=\"Loving v. Virginia\">Loving v. Virginia</a></i> declares all <a href=\"https://wikipedia.org/wiki/U.S._state\" title=\"U.S. state\">U.S. state</a> laws which prohibit <a href=\"https://wikipedia.org/wiki/Interracial_marriage\" title=\"Interracial marriage\">interracial marriage</a> to be <a href=\"https://wikipedia.org/wiki/Constitutionality\" title=\"Constitutionality\">unconstitutional</a>.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Supreme_Court_of_the_United_States\" title=\"Supreme Court of the United States\">United States Supreme Court</a> in <i><a href=\"https://wikipedia.org/wiki/Loving_v._Virginia\" title=\"Loving v. Virginia\">Loving v. Virginia</a></i> declares all <a href=\"https://wikipedia.org/wiki/U.S._state\" title=\"U.S. state\">U.S. state</a> laws which prohibit <a href=\"https://wikipedia.org/wiki/Interracial_marriage\" title=\"Interracial marriage\">interracial marriage</a> to be <a href=\"https://wikipedia.org/wiki/Constitutionality\" title=\"Constitutionality\">unconstitutional</a>.", "links": [{"title": "Supreme Court of the United States", "link": "https://wikipedia.org/wiki/Supreme_Court_of_the_United_States"}, {"title": "Loving v. Virginia", "link": "https://wikipedia.org/wiki/Loving_v._Virginia"}, {"title": "U.S. state", "link": "https://wikipedia.org/wiki/U.S._state"}, {"title": "Interracial marriage", "link": "https://wikipedia.org/wiki/Interracial_marriage"}, {"title": "Constitutionality", "link": "https://wikipedia.org/wiki/Constitutionality"}]}, {"year": "1975", "text": "India, Judge <PERSON><PERSON><PERSON><PERSON><PERSON> of the city of Allahabad ruled that India's Prime Minister <PERSON><PERSON> had used corrupt practices to win her seat in the Indian Parliament, and that she should be banned from holding any public office. Mrs. <PERSON> sent word that she refused to resign.", "html": "1975 - India, Judge <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON><PERSON></a> of the city of Allahabad <a href=\"https://wikipedia.org/wiki/In<PERSON>_Gandhi#Verdict_on_electoral_malpractice\" title=\"<PERSON><PERSON> Gandhi\">ruled that India's Prime Minister <PERSON><PERSON> had used corrupt practices</a> to win her seat in the Indian Parliament, and that she should be banned from holding any public office. Mrs. <PERSON> sent word that she refused to resign.", "no_year_html": "India, Judge <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON><PERSON></a> of the city of Allahabad <a href=\"https://wikipedia.org/wiki/In<PERSON>_Gandhi#Verdict_on_electoral_malpractice\" title=\"<PERSON><PERSON> Gandhi\">ruled that India's Prime Minister <PERSON><PERSON> had used corrupt practices</a> to win her seat in the Indian Parliament, and that she should be banned from holding any public office. Mrs. <PERSON> sent word that she refused to resign.", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON><PERSON>_<PERSON>"}, {"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Indira_Gandhi#Verdict_on_electoral_malpractice"}]}, {"year": "1979", "text": "<PERSON> wins the second <PERSON><PERSON><PERSON> prize for a man-powered flight across the English Channel in the Gossamer Albatross.", "html": "1979 - <a href=\"https://wikipedia.org/wiki/<PERSON>(cyclist)\" class=\"mw-redirect\" title=\"<PERSON> (cyclist)\"><PERSON></a> wins the second <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_prize\" title=\"<PERSON><PERSON><PERSON> prize\"><PERSON><PERSON><PERSON> prize</a> for a man-powered flight across the <a href=\"https://wikipedia.org/wiki/English_Channel\" title=\"English Channel\">English Channel</a> in the <a href=\"https://wikipedia.org/wiki/Gossamer_Albatross\" class=\"mw-redirect\" title=\"Gossamer Albatross\">Gossamer Albatross</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>(cyclist)\" class=\"mw-redirect\" title=\"<PERSON> (cyclist)\"><PERSON></a> wins the second <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_prize\" title=\"<PERSON><PERSON><PERSON> prize\"><PERSON><PERSON><PERSON> prize</a> for a man-powered flight across the <a href=\"https://wikipedia.org/wiki/English_Channel\" title=\"English Channel\">English Channel</a> in the <a href=\"https://wikipedia.org/wiki/Gossamer_Albatross\" class=\"mw-redirect\" title=\"Gossamer Albatross\">Gossamer Albatross</a>.", "links": [{"title": "<PERSON> (cyclist)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>(cyclist)"}, {"title": "<PERSON><PERSON><PERSON> prize", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_prize"}, {"title": "English Channel", "link": "https://wikipedia.org/wiki/English_Channel"}, {"title": "Gossamer Albatross", "link": "https://wikipedia.org/wiki/Gossamer_Albatross"}]}, {"year": "1981", "text": "The first of the Indiana Jones film franchise, Raiders of the Lost Ark, is released in theaters.", "html": "1981 - The first of the <a href=\"https://wikipedia.org/wiki/<PERSON>_Jones\" title=\"Indiana Jones\">Indiana Jones</a> film franchise, <i><a href=\"https://wikipedia.org/wiki/Raiders_of_the_Lost_Ark\" title=\"Raiders of the Lost Ark\">Raiders of the Lost Ark</a></i>, is released in theaters.", "no_year_html": "The first of the <a href=\"https://wikipedia.org/wiki/<PERSON>_Jones\" title=\"Indiana Jones\"><PERSON> Jones</a> film franchise, <i><a href=\"https://wikipedia.org/wiki/Raiders_of_the_Lost_Ark\" title=\"Raiders of the Lost Ark\">Raiders of the Lost Ark</a></i>, is released in theaters.", "links": [{"title": "Indiana Jones", "link": "https://wikipedia.org/wiki/Indiana_Jones"}, {"title": "Raiders of the Lost Ark", "link": "https://wikipedia.org/wiki/Raiders_of_the_Lost_Ark"}]}, {"year": "1982", "text": "A nuclear disarmament rally and concert is held in New York City.", "html": "1982 - A nuclear disarmament rally and concert is held in <a href=\"https://wikipedia.org/wiki/New_York_City\" title=\"New York City\">New York City</a>.", "no_year_html": "A nuclear disarmament rally and concert is held in <a href=\"https://wikipedia.org/wiki/New_York_City\" title=\"New York City\">New York City</a>.", "links": [{"title": "New York City", "link": "https://wikipedia.org/wiki/New_York_City"}]}, {"year": "1987", "text": "The Central African Republic's former emperor <PERSON><PERSON><PERSON><PERSON><PERSON> is sentenced to death for crimes he had committed during his 13-year rule.", "html": "1987 - The <a href=\"https://wikipedia.org/wiki/Central_African_Republic\" title=\"Central African Republic\">Central African Republic</a>'s former emperor <a href=\"https://wikipedia.org/wiki/<PERSON>-<PERSON>%C3%A<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON><PERSON></a> is sentenced to death for crimes he had committed during his 13-year rule.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Central_African_Republic\" title=\"Central African Republic\">Central African Republic</a>'s former emperor <a href=\"https://wikipedia.org/wiki/<PERSON>-<PERSON>%C3%A<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON><PERSON></a> is sentenced to death for crimes he had committed during his 13-year rule.", "links": [{"title": "Central African Republic", "link": "https://wikipedia.org/wiki/Central_African_Republic"}, {"title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>-<PERSON>%C3%A9del_<PERSON>"}]}, {"year": "1987", "text": "Cold War: At the Brandenburg Gate, U.S. President <PERSON> publicly challenges <PERSON> to tear down the Berlin Wall.", "html": "1987 - <a href=\"https://wikipedia.org/wiki/Cold_War\" title=\"Cold War\">Cold War</a>: At the <a href=\"https://wikipedia.org/wiki/Brandenburg_Gate\" title=\"Brandenburg Gate\">Brandenburg Gate</a>, <a href=\"https://wikipedia.org/wiki/President_of_the_United_States\" title=\"President of the United States\">U.S. President</a> <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> publicly challenges <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> to <a href=\"https://wikipedia.org/wiki/Tear_down_this_wall!\" title=\"Tear down this wall!\">tear down</a> the <a href=\"https://wikipedia.org/wiki/Berlin_Wall\" title=\"Berlin Wall\">Berlin Wall</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Cold_War\" title=\"Cold War\">Cold War</a>: At the <a href=\"https://wikipedia.org/wiki/Brandenburg_Gate\" title=\"Brandenburg Gate\">Brandenburg Gate</a>, <a href=\"https://wikipedia.org/wiki/President_of_the_United_States\" title=\"President of the United States\">U.S. President</a> <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> publicly challenges <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> to <a href=\"https://wikipedia.org/wiki/Tear_down_this_wall!\" title=\"Tear down this wall!\">tear down</a> the <a href=\"https://wikipedia.org/wiki/Berlin_Wall\" title=\"Berlin Wall\">Berlin Wall</a>.", "links": [{"title": "Cold War", "link": "https://wikipedia.org/wiki/Cold_War"}, {"title": "Brandenburg Gate", "link": "https://wikipedia.org/wiki/Brandenburg_Gate"}, {"title": "President of the United States", "link": "https://wikipedia.org/wiki/President_of_the_United_States"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Tear down this wall!", "link": "https://wikipedia.org/wiki/Tear_down_this_wall!"}, {"title": "Berlin Wall", "link": "https://wikipedia.org/wiki/Berlin_Wall"}]}, {"year": "1988", "text": "Austral Líneas <PERSON> Flight 046, a McDonnell Douglas MD-81, crashes short of the runway at Libertador General José de San Martín Airport, killing all 22 people on board.", "html": "1988 - <a href=\"https://wikipedia.org/wiki/Austral_L%C3%ADneas_A%C3%A9reas_Flight_046\" title=\"Austral Líneas Aéreas Flight 046\">Austral Líneas Aéreas Flight 046</a>, a <a href=\"https://wikipedia.org/wiki/McDonnell_Douglas_MD-80\" title=\"McDonnell Douglas MD-80\">McDonnell Douglas MD-81</a>, crashes short of the runway at <a href=\"https://wikipedia.org/wiki/Libertador_General_Jo<PERSON>%C3%A9_de_San_Mart%C3%ADn_Airport\" title=\"Libertador General <PERSON> San Martín Airport\">Libertador General José de San Martín Airport</a>, killing all 22 people on board.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Austral_L%C3%ADneas_A%C3%A9reas_Flight_046\" title=\"Austral Líneas Aéreas Flight 046\">Austral Líneas Aéreas Flight 046</a>, a <a href=\"https://wikipedia.org/wiki/McDonnell_Douglas_MD-80\" title=\"McDonnell Douglas MD-80\">McDonnell Douglas MD-81</a>, crashes short of the runway at <a href=\"https://wikipedia.org/wiki/Libertador_General_Jo<PERSON>%C3%A9_de_San_Mart%C3%ADn_Airport\" title=\"Libertador General <PERSON> de <PERSON> Martín Airport\">Libertador General José de San Martín Airport</a>, killing all 22 people on board.", "links": [{"title": "Austral Líneas Aéreas Flight 046", "link": "https://wikipedia.org/wiki/Austral_L%C3%ADneas_A%C3%A9reas_Flight_046"}, {"title": "McDonnell Douglas MD-80", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>-80"}, {"title": "Libertador General José de San Martín Airport", "link": "https://wikipedia.org/wiki/Libertador_General_<PERSON><PERSON>%C3%A9_de_San_Mart%C3%ADn_Airport"}]}, {"year": "1990", "text": "Russia Day: The parliament of the Russian Federation formally declares its sovereignty.", "html": "1990 - <a href=\"https://wikipedia.org/wiki/Russia_Day\" title=\"Russia Day\">Russia Day</a>: The parliament of the <a href=\"https://wikipedia.org/wiki/Russia\" title=\"Russia\">Russian Federation</a> formally declares its sovereignty.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Russia_Day\" title=\"Russia Day\">Russia Day</a>: The parliament of the <a href=\"https://wikipedia.org/wiki/Russia\" title=\"Russia\">Russian Federation</a> formally declares its sovereignty.", "links": [{"title": "Russia Day", "link": "https://wikipedia.org/wiki/Russia_Day"}, {"title": "Russia", "link": "https://wikipedia.org/wiki/Russia"}]}, {"year": "1991", "text": "In modern Russia's first democratic election, <PERSON> is elected as the President of Russia.", "html": "1991 - In modern Russia's first <a href=\"https://wikipedia.org/wiki/1991_Russian_presidential_election\" title=\"1991 Russian presidential election\">democratic election</a>, <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> is elected as the <a href=\"https://wikipedia.org/wiki/President_of_Russia\" title=\"President of Russia\">President of Russia</a>.", "no_year_html": "In modern Russia's first <a href=\"https://wikipedia.org/wiki/1991_Russian_presidential_election\" title=\"1991 Russian presidential election\">democratic election</a>, <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> is elected as the <a href=\"https://wikipedia.org/wiki/President_of_Russia\" title=\"President of Russia\">President of Russia</a>.", "links": [{"title": "1991 Russian presidential election", "link": "https://wikipedia.org/wiki/1991_Russian_presidential_election"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}, {"title": "President of Russia", "link": "https://wikipedia.org/wiki/President_of_Russia"}]}, {"year": "1991", "text": "Kokkadichcholai massacre: The Sri Lankan Army massacres 152 minority Tamil civilians in the village of Kokkadichcholai near the Eastern Province town of Batticaloa.", "html": "1991 - <a href=\"https://wikipedia.org/wiki/1991_Kokkadichcholai_massacre\" title=\"1991 Kokkadichcholai massacre\">Kokkadichcholai massacre</a>: The <a href=\"https://wikipedia.org/wiki/Sri_Lankan_Army\" class=\"mw-redirect\" title=\"Sri Lankan Army\">Sri Lankan Army</a> massacres 152 minority <a href=\"https://wikipedia.org/wiki/Tamil_people\" class=\"mw-redirect\" title=\"Tamil people\">Tamil</a> civilians in the village of <a href=\"https://wikipedia.org/wiki/Kokkadichcholai\" title=\"Kokkadichcholai\">Kokkadichcholai</a> near the Eastern Province town of <a href=\"https://wikipedia.org/wiki/Batticaloa\" title=\"Batticaloa\">Batticaloa</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/1991_Kokkadichcholai_massacre\" title=\"1991 Kokkadichcholai massacre\">Kokkadichcholai massacre</a>: The <a href=\"https://wikipedia.org/wiki/Sri_Lankan_Army\" class=\"mw-redirect\" title=\"Sri Lankan Army\">Sri Lankan Army</a> massacres 152 minority <a href=\"https://wikipedia.org/wiki/Tamil_people\" class=\"mw-redirect\" title=\"Tamil people\">Tamil</a> civilians in the village of <a href=\"https://wikipedia.org/wiki/Kokkadichcholai\" title=\"Kokkadichcholai\">Kokkadichcholai</a> near the Eastern Province town of <a href=\"https://wikipedia.org/wiki/Batticaloa\" title=\"Batticaloa\">Batticaloa</a>.", "links": [{"title": "1991 Kokkadichcholai massacre", "link": "https://wikipedia.org/wiki/1991_Kokkadichcholai_massacre"}, {"title": "Sri Lankan Army", "link": "https://wikipedia.org/wiki/Sri_Lankan_Army"}, {"title": "Tamil people", "link": "https://wikipedia.org/wiki/Tamil_people"}, {"title": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Kokkadichcholai"}, {"title": "Batticaloa", "link": "https://wikipedia.org/wiki/Batticaloa"}]}, {"year": "1993", "text": "An election takes place in Nigeria and is won by <PERSON><PERSON><PERSON>. Its results are later annulled by the military government of <PERSON>.", "html": "1993 - <a href=\"https://wikipedia.org/wiki/1993_Nigerian_presidential_election\" title=\"1993 Nigerian presidential election\">An election takes place in Nigeria</a> and is won by <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_Kashimawo_Olawale_Abiola\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON> Kashimawo Olawale Abiola\"><PERSON><PERSON><PERSON> Olawale Abiola</a>. Its results are later annulled by the military government of <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/1993_Nigerian_presidential_election\" title=\"1993 Nigerian presidential election\">An election takes place in Nigeria</a> and is won by <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_Kashimawo_Olawale_Abiola\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON> Ka<PERSON>wo Olawale Abiola\"><PERSON><PERSON><PERSON> Olawale Abiola</a>. Its results are later annulled by the military government of <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>.", "links": [{"title": "1993 Nigerian presidential election", "link": "https://wikipedia.org/wiki/1993_Nigerian_presidential_election"}, {"title": "<PERSON><PERSON><PERSON> Abiola", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_Olawale_Abiola"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1999", "text": "Kosovo War: Operation Joint Guardian begins when a NATO-led United Nations peacekeeping force (KFor) enters the province of Kosovo in the Federal Republic of Yugoslavia.", "html": "1999 - <a href=\"https://wikipedia.org/wiki/Kosovo_War\" title=\"Kosovo War\">Kosovo War</a>: <a href=\"https://wikipedia.org/wiki/Operation_Joint_Guardian\" class=\"mw-redirect\" title=\"Operation Joint Guardian\">Operation Joint Guardian</a> begins when a <a href=\"https://wikipedia.org/wiki/NATO\" title=\"NATO\">NATO</a>-led <a href=\"https://wikipedia.org/wiki/United_Nations\" title=\"United Nations\">United Nations</a> peacekeeping force (<a href=\"https://wikipedia.org/wiki/Kosovo_Force\" title=\"Kosovo Force\">KFor</a>) enters the province of <a href=\"https://wikipedia.org/wiki/Kosovo\" title=\"Kosovo\">Kosovo</a> in the <a href=\"https://wikipedia.org/wiki/Federal_Republic_of_Yugoslavia\" class=\"mw-redirect\" title=\"Federal Republic of Yugoslavia\">Federal Republic of Yugoslavia</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Kosovo_War\" title=\"Kosovo War\">Kosovo War</a>: <a href=\"https://wikipedia.org/wiki/Operation_Joint_Guardian\" class=\"mw-redirect\" title=\"Operation Joint Guardian\">Operation Joint Guardian</a> begins when a <a href=\"https://wikipedia.org/wiki/NATO\" title=\"NATO\">NATO</a>-led <a href=\"https://wikipedia.org/wiki/United_Nations\" title=\"United Nations\">United Nations</a> peacekeeping force (<a href=\"https://wikipedia.org/wiki/Kosovo_Force\" title=\"Kosovo Force\">KFor</a>) enters the province of <a href=\"https://wikipedia.org/wiki/Kosovo\" title=\"Kosovo\">Kosovo</a> in the <a href=\"https://wikipedia.org/wiki/Federal_Republic_of_Yugoslavia\" class=\"mw-redirect\" title=\"Federal Republic of Yugoslavia\">Federal Republic of Yugoslavia</a>.", "links": [{"title": "Kosovo War", "link": "https://wikipedia.org/wiki/Kosovo_War"}, {"title": "Operation Joint Guardian", "link": "https://wikipedia.org/wiki/Operation_Joint_Guardian"}, {"title": "NATO", "link": "https://wikipedia.org/wiki/NATO"}, {"title": "United Nations", "link": "https://wikipedia.org/wiki/United_Nations"}, {"title": "Kosovo Force", "link": "https://wikipedia.org/wiki/Kosovo_Force"}, {"title": "Kosovo", "link": "https://wikipedia.org/wiki/Kosovo"}, {"title": "Federal Republic of Yugoslavia", "link": "https://wikipedia.org/wiki/Federal_Republic_of_Yugoslavia"}]}, {"year": "2009", "text": "A disputed presidential election in Iran leads to wide-ranging local and international protests.", "html": "2009 - A disputed <a href=\"https://wikipedia.org/wiki/2009_Iranian_presidential_election\" title=\"2009 Iranian presidential election\">presidential election</a> in Iran leads to wide-ranging <a href=\"https://wikipedia.org/wiki/2009_Iranian_presidential_election_protests\" title=\"2009 Iranian presidential election protests\">local and international protests</a>.", "no_year_html": "A disputed <a href=\"https://wikipedia.org/wiki/2009_Iranian_presidential_election\" title=\"2009 Iranian presidential election\">presidential election</a> in Iran leads to wide-ranging <a href=\"https://wikipedia.org/wiki/2009_Iranian_presidential_election_protests\" title=\"2009 Iranian presidential election protests\">local and international protests</a>.", "links": [{"title": "2009 Iranian presidential election", "link": "https://wikipedia.org/wiki/2009_Iranian_presidential_election"}, {"title": "2009 Iranian presidential election protests", "link": "https://wikipedia.org/wiki/2009_Iranian_presidential_election_protests"}]}, {"year": "2014", "text": "Between 1,095 and 1,700 Shia Iraqi people are killed in an attack by the Islamic State of Iraq and the Levant on Camp Speicher in Tikrit, Iraq. It is the second deadliest act of terrorism in history, only behind 9/11.", "html": "2014 - Between 1,095 and 1,700 <a href=\"https://wikipedia.org/wiki/Shia_Islam_in_Iraq\" title=\"Shia Islam in Iraq\">Shia Iraqi</a> people are killed in an <a href=\"https://wikipedia.org/wiki/Camp_Speicher_massacre\" title=\"Camp Speicher massacre\">attack</a> by <a href=\"https://wikipedia.org/wiki/Islamic_State\" title=\"Islamic State\">the Islamic State of Iraq and the Levant</a> on <a href=\"https://wikipedia.org/wiki/Camp_Speicher\" title=\"Camp Speicher\">Camp Speicher</a> in <a href=\"https://wikipedia.org/wiki/Tikrit\" title=\"Tikrit\">Tikrit</a>, <a href=\"https://wikipedia.org/wiki/Iraq\" title=\"Iraq\">Iraq</a>. It is the second <a href=\"https://wikipedia.org/wiki/List_of_major_terrorist_incidents\" title=\"List of major terrorist incidents\">deadliest act of terrorism</a> in history, only behind <a href=\"https://wikipedia.org/wiki/September_11_attacks\" title=\"September 11 attacks\">9/11</a>.", "no_year_html": "Between 1,095 and 1,700 <a href=\"https://wikipedia.org/wiki/Shia_Islam_in_Iraq\" title=\"Shia Islam in Iraq\">Shia Iraqi</a> people are killed in an <a href=\"https://wikipedia.org/wiki/Camp_Speicher_massacre\" title=\"Camp Speicher massacre\">attack</a> by <a href=\"https://wikipedia.org/wiki/Islamic_State\" title=\"Islamic State\">the Islamic State of Iraq and the Levant</a> on <a href=\"https://wikipedia.org/wiki/Camp_Speicher\" title=\"Camp Speicher\">Camp Speicher</a> in <a href=\"https://wikipedia.org/wiki/Tikrit\" title=\"Tikrit\">Tikrit</a>, <a href=\"https://wikipedia.org/wiki/Iraq\" title=\"Iraq\">Iraq</a>. It is the second <a href=\"https://wikipedia.org/wiki/List_of_major_terrorist_incidents\" title=\"List of major terrorist incidents\">deadliest act of terrorism</a> in history, only behind <a href=\"https://wikipedia.org/wiki/September_11_attacks\" title=\"September 11 attacks\">9/11</a>.", "links": [{"title": "Shia Islam in Iraq", "link": "https://wikipedia.org/wiki/Shia_Islam_in_Iraq"}, {"title": "Camp Speicher massacre", "link": "https://wikipedia.org/wiki/Camp_Speicher_massacre"}, {"title": "Islamic State", "link": "https://wikipedia.org/wiki/Islamic_State"}, {"title": "Camp Speicher", "link": "https://wikipedia.org/wiki/Camp_S<PERSON>icher"}, {"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Tikrit"}, {"title": "Iraq", "link": "https://wikipedia.org/wiki/Iraq"}, {"title": "List of major terrorist incidents", "link": "https://wikipedia.org/wiki/List_of_major_terrorist_incidents"}, {"title": "September 11 attacks", "link": "https://wikipedia.org/wiki/September_11_attacks"}]}, {"year": "2016", "text": "Forty-nine civilians are killed and 58 others injured in an attack on a gay nightclub in Orlando, Florida, United States; the gunman, <PERSON>, is killed in a gunfight with police.", "html": "2016 - Forty-nine civilians are killed and 58 others injured in <a href=\"https://wikipedia.org/wiki/Pulse_nightclub_shooting\" title=\"Pulse nightclub shooting\">an attack on a gay nightclub</a> in <a href=\"https://wikipedia.org/wiki/Orlando,_Florida\" title=\"Orlando, Florida\">Orlando, Florida</a>, United States; the gunman, <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, is killed in a gunfight with police.", "no_year_html": "Forty-nine civilians are killed and 58 others injured in <a href=\"https://wikipedia.org/wiki/Pulse_nightclub_shooting\" title=\"Pulse nightclub shooting\">an attack on a gay nightclub</a> in <a href=\"https://wikipedia.org/wiki/Orlando,_Florida\" title=\"Orlando, Florida\">Orlando, Florida</a>, United States; the gunman, <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, is killed in a gunfight with police.", "links": [{"title": "Pulse nightclub shooting", "link": "https://wikipedia.org/wiki/Pulse_nightclub_shooting"}, {"title": "Orlando, Florida", "link": "https://wikipedia.org/wiki/Orlando,_Florida"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2018", "text": "United States President <PERSON> and <PERSON> of North Korea held the first meeting between leaders of their two countries in Singapore.", "html": "2018 - <a href=\"https://wikipedia.org/wiki/United_States\" title=\"United States\">United States</a> President <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> and <a href=\"https://wikipedia.org/wiki/<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a> of <a href=\"https://wikipedia.org/wiki/North_Korea\" title=\"North Korea\">North Korea</a> held the <a href=\"https://wikipedia.org/wiki/2018_North_Korea%E2%80%93United_States_Singapore_Summit\" title=\"2018 North Korea-United States Singapore Summit\">first meeting</a> between leaders of their two countries in <a href=\"https://wikipedia.org/wiki/Singapore\" title=\"Singapore\">Singapore</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/United_States\" title=\"United States\">United States</a> President <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> and <a href=\"https://wikipedia.org/wiki/<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a> of <a href=\"https://wikipedia.org/wiki/North_Korea\" title=\"North Korea\">North Korea</a> held the <a href=\"https://wikipedia.org/wiki/2018_North_Korea%E2%80%93United_States_Singapore_Summit\" title=\"2018 North Korea-United States Singapore Summit\">first meeting</a> between leaders of their two countries in <a href=\"https://wikipedia.org/wiki/Singapore\" title=\"Singapore\">Singapore</a>.", "links": [{"title": "United States", "link": "https://wikipedia.org/wiki/United_States"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>-<PERSON>"}, {"title": "North Korea", "link": "https://wikipedia.org/wiki/North_Korea"}, {"title": "2018 North Korea-United States Singapore Summit", "link": "https://wikipedia.org/wiki/2018_North_Korea%E2%80%93United_States_Singapore_Summit"}, {"title": "Singapore", "link": "https://wikipedia.org/wiki/Singapore"}]}, {"year": "2019", "text": "Kassym<PERSON><PERSON><PERSON> is inaugurated as the second president of Kazakhstan.", "html": "2019 - <a href=\"https://wikipedia.org/wiki/Kassy<PERSON>-<PERSON><PERSON>_<PERSON>\" title=\"Kassym-<PERSON><PERSON>\">Kassym-<PERSON><PERSON></a> is inaugurated as the second <a href=\"https://wikipedia.org/wiki/President_of_Kazakhstan\" title=\"President of Kazakhstan\">president of Kazakhstan</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Kassy<PERSON>-<PERSON><PERSON>_<PERSON>\" title=\"Kassym-<PERSON><PERSON>\">Kassym-<PERSON><PERSON></a> is inaugurated as the second <a href=\"https://wikipedia.org/wiki/President_of_Kazakhstan\" title=\"President of Kazakhstan\">president of Kazakhstan</a>.", "links": [{"title": "Kassym-<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>-<PERSON><PERSON>_<PERSON>"}, {"title": "President of Kazakhstan", "link": "https://wikipedia.org/wiki/President_of_Kazakhstan"}]}, {"year": "2024", "text": "A fire in a residential building in Mangaf, Kuwait City kills at least 50 people.", "html": "2024 - <a href=\"https://wikipedia.org/wiki/2024_Mangaf_building_fire\" title=\"2024 Mangaf building fire\">A fire in a residential building</a> in <a href=\"https://wikipedia.org/wiki/Mangaf\" title=\"Mangaf\">Mangaf</a>, <a href=\"https://wikipedia.org/wiki/Kuwait_City\" title=\"Kuwait City\">Kuwait City</a> kills at least 50 people.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/2024_Mangaf_building_fire\" title=\"2024 Mangaf building fire\">A fire in a residential building</a> in <a href=\"https://wikipedia.org/wiki/Mangaf\" title=\"Mangaf\">Mangaf</a>, <a href=\"https://wikipedia.org/wiki/Kuwait_City\" title=\"Kuwait City\">Kuwait City</a> kills at least 50 people.", "links": [{"title": "2024 Mangaf building fire", "link": "https://wikipedia.org/wiki/2024_Mangaf_building_fire"}, {"title": "Mangaf", "link": "https://wikipedia.org/wiki/Mangaf"}, {"title": "Kuwait City", "link": "https://wikipedia.org/wiki/Kuwait_City"}]}], "Births": [{"year": "950", "text": "<PERSON><PERSON><PERSON>, Japanese emperor (d. 1011)", "html": "950 - <a href=\"https://wikipedia.org/wiki/Emperor_<PERSON><PERSON><PERSON>\" title=\"Emperor <PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese emperor (d. 1011)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Emperor_<PERSON><PERSON><PERSON>\" title=\"Emperor <PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese emperor (d. 1011)", "links": [{"title": "Emperor <PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>i"}]}, {"year": "1107", "text": "<PERSON>, Chinese emperor (d. 1187)", "html": "1107 - <a href=\"https://wikipedia.org/wiki/Emperor_<PERSON><PERSON>_of_Song\" title=\"Emperor <PERSON><PERSON> of Song\"><PERSON></a>, Chinese emperor (d. 1187)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Emperor_<PERSON><PERSON>_of_Song\" title=\"Emperor <PERSON> of Song\"><PERSON></a>, Chinese emperor (d. 1187)", "links": [{"title": "Emperor <PERSON><PERSON> of Song", "link": "https://wikipedia.org/wiki/Emperor_<PERSON><PERSON>_of_Song"}]}, {"year": "1161", "text": "<PERSON>, Duchess of Brittany (d. 1201)", "html": "1161 - <a href=\"https://wikipedia.org/wiki/<PERSON>,_Duchess_of_Brittany\" title=\"<PERSON>, Duchess of Brittany\"><PERSON>, Duchess of Brittany</a> (d. 1201)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>,_Duchess_of_Brittany\" title=\"<PERSON>, Duchess of Brittany\"><PERSON>, Duchess of Brittany</a> (d. 1201)", "links": [{"title": "<PERSON>, Duchess of Brittany", "link": "https://wikipedia.org/wiki/<PERSON>,_Duchess_of_Brittany"}]}, {"year": "1519", "text": "<PERSON><PERSON><PERSON>, Grand Duke of Tuscany (d. 1574)", "html": "1519 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>%27_<PERSON>,_Grand_Duke_of_Tuscany\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON>, Grand Duke of Tuscany\"><PERSON><PERSON><PERSON>, Grand Duke of Tuscany</a> (d. 1574)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>%27_<PERSON>,_Grand_Duke_of_Tuscany\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON>, Grand Duke of Tuscany\"><PERSON><PERSON><PERSON>, Grand Duke of Tuscany</a> (d. 1574)", "links": [{"title": "<PERSON><PERSON><PERSON>, Grand Duke of Tuscany", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>%27_<PERSON>,_Grand_Duke_of_Tuscany"}]}, {"year": "1561", "text": "<PERSON> of Württemberg, German princess (d. 1616)", "html": "1561 - <a href=\"https://wikipedia.org/wiki/<PERSON>_of_W%C3%BCrttemberg\" title=\"<PERSON> of Württemberg\"><PERSON> of Württemberg</a>, German princess (d. 1616)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Anna_of_W%C3%BCrttemberg\" title=\"<PERSON> of Württemberg\"><PERSON> of Württemberg</a>, German princess (d. 1616)", "links": [{"title": "Anna of Württemberg", "link": "https://wikipedia.org/wiki/Anna_of_W%C3%BCrttemberg"}]}, {"year": "1564", "text": "<PERSON>, Duke of Saxe-Coburg (d. 1633)", "html": "1564 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Duke_of_Saxe-Coburg\" title=\"<PERSON>, Duke of Saxe-Coburg\"><PERSON>, Duke of Saxe-Coburg</a> (d. 1633)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Duke_of_Saxe-Coburg\" title=\"<PERSON>, Duke of Saxe-Coburg\"><PERSON>, Duke of Saxe-Coburg</a> (d. 1633)", "links": [{"title": "<PERSON>, Duke of Saxe-Coburg", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Duke_of_Saxe-Coburg"}]}, {"year": "1573", "text": "<PERSON>, 5th Earl of Sussex, soldier (d. 1629)", "html": "1573 - <a href=\"https://wikipedia.org/wiki/<PERSON>,_5th_Earl_of_Sussex\" title=\"<PERSON>, 5th Earl of Sussex\"><PERSON>, 5th Earl of Sussex</a>, soldier (d. 1629)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>,_5th_Earl_of_Sussex\" title=\"<PERSON>, 5th Earl of Sussex\"><PERSON>, 5th Earl <PERSON> Sussex</a>, soldier (d. 1629)", "links": [{"title": "<PERSON>, 5th Earl of Sussex", "link": "https://wikipedia.org/wiki/<PERSON>,_5th_Earl_of_Sussex"}]}, {"year": "1577", "text": "<PERSON>, Swiss astronomer and mathematician (d. 1643)", "html": "1577 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swiss astronomer and mathematician (d. 1643)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swiss astronomer and mathematician (d. 1643)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1580", "text": "<PERSON><PERSON><PERSON>, Flemish painter (d. 1662)", "html": "1580 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Flemish painter (d. 1662)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Flemish painter (d. 1662)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>t"}]}, {"year": "1653", "text": "<PERSON> of Courland, Landgravine of Hesse-Kassel (d. 1711)", "html": "1653 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_of_Courland\" title=\"<PERSON> of Courland\"><PERSON> of Courland</a>, Landgravine of Hesse-Kassel (d. 1711)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Courland\" title=\"<PERSON> of Courland\"><PERSON> of Courland</a>, Landgravine of Hesse-Kassel (d. 1711)", "links": [{"title": "<PERSON> of Courland", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_of_Courland"}]}, {"year": "1686", "text": "<PERSON><PERSON><PERSON>, French writer (d. 1764)", "html": "1686 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, French writer (d. 1764)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, French writer (d. 1764)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>-<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1711", "text": "<PERSON>, French priest and theologian (d. 1780)", "html": "1711 - <a href=\"https://wikipedia.org/wiki/<PERSON>_(theologian)\" title=\"<PERSON> (theologian)\"><PERSON></a>, French priest and theologian (d. 1780)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(theologian)\" title=\"<PERSON> (theologian)\"><PERSON></a>, French priest and theologian (d. 1780)", "links": [{"title": "<PERSON> (theologian)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(theologian)"}]}, {"year": "1760", "text": "<PERSON><PERSON><PERSON>, French author, playwright, journalist, and politician (d. 1797)", "html": "1760 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>_Couvrai\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON> Couvrai\"><PERSON><PERSON><PERSON></a>, French author, playwright, journalist, and politician (d. 1797)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>_Couvrai\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON> Couvrai\"><PERSON><PERSON><PERSON></a>, French author, playwright, journalist, and politician (d. 1797)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1771", "text": "<PERSON>, American sergeant (Lewis and Clark Expedition) and author (d. 1870)", "html": "1771 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American sergeant (<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_Expedition\" title=\"Lewis and <PERSON> Expedition\"><PERSON> and <PERSON> Expedition</a>) and author (d. 1870)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American sergeant (<a href=\"https://wikipedia.org/wiki/<PERSON>_and_<PERSON>_Expedition\" title=\"Lewis and <PERSON> Expedition\"><PERSON> and <PERSON> Expedition</a>) and author (d. 1870)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Patrick_Gass"}, {"title": "<PERSON> and Clark Expedition", "link": "https://wikipedia.org/wiki/<PERSON>_and_Clark_Expedition"}]}, {"year": "1775", "text": "<PERSON>, Prussian field marshal (d. 1851)", "html": "1775 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>%C3%BCffling\" title=\"<PERSON>\"><PERSON></a>, Prussian field marshal (d. 1851)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>%C3%BCffling\" title=\"<PERSON>\"><PERSON></a>, Prussian field marshal (d. 1851)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>%C3%BCffling"}]}, {"year": "1777", "text": "<PERSON>, American physician and politician (d. 1837)", "html": "1777 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(U.S._politician)\" class=\"mw-redirect\" title=\"<PERSON> (U.S. politician)\"><PERSON></a>, American physician and politician (d. 1837)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(U.S._politician)\" class=\"mw-redirect\" title=\"<PERSON> (U.S. politician)\"><PERSON></a>, American physician and politician (d. 1837)", "links": [{"title": "<PERSON> (U.S. politician)", "link": "https://wikipedia.org/wiki/<PERSON>_(U.S._politician)"}]}, {"year": "1798", "text": "<PERSON>, American general (d. 1876)", "html": "1798 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(general)\" title=\"<PERSON> (general)\"><PERSON></a>, American general (d. 1876)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(general)\" title=\"<PERSON> (general)\"><PERSON></a>, American general (d. 1876)", "links": [{"title": "<PERSON> (general)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(general)"}]}, {"year": "1800", "text": "<PERSON>, American politician (d. 1836)", "html": "1800 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, American politician (d. 1836)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, American politician (d. 1836)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1802", "text": "<PERSON>, English sociologist and author (d. 1876)", "html": "1802 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English sociologist and author (d. 1876)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English sociologist and author (d. 1876)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1806", "text": "<PERSON>, German-American engineer, designed the Brooklyn Bridge (d. 1869)", "html": "1806 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German-American engineer, designed the <a href=\"https://wikipedia.org/wiki/Brooklyn_Bridge\" title=\"Brooklyn Bridge\">Brooklyn Bridge</a> (d. 1869)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German-American engineer, designed the <a href=\"https://wikipedia.org/wiki/Brooklyn_Bridge\" title=\"Brooklyn Bridge\">Brooklyn Bridge</a> (d. 1869)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>"}, {"title": "Brooklyn Bridge", "link": "https://wikipedia.org/wiki/Brooklyn_Bridge"}]}, {"year": "1807", "text": "<PERSON><PERSON>, Croatian physician and journalist (d. 1879)", "html": "1807 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>%C4%87\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Croatian physician and journalist (d. 1879)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>%C4%87\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Croatian physician and journalist (d. 1879)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Ante_Kuzmani%C4%87"}]}, {"year": "1812", "text": "<PERSON>, French geologist and academic (d. 1890)", "html": "1812 - <a href=\"https://wikipedia.org/wiki/<PERSON>_H%C3%A9bert\" title=\"<PERSON>\"><PERSON></a>, French geologist and academic (d. 1890)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A9bert\" title=\"<PERSON>\"><PERSON></a>, French geologist and academic (d. 1890)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Edmond_H%C3%A9bert"}]}, {"year": "1819", "text": "<PERSON>, English priest, historian, and author (d. 1875)", "html": "1819 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English priest, historian, and author (d. 1875)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English priest, historian, and author (d. 1875)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1827", "text": "<PERSON>, Swiss author, best known for <PERSON> (d. 1901)", "html": "1827 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swiss author, best known for <i><a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a></i> (d. 1901)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swiss author, best known for <i><a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a></i> (d. 1901)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1831", "text": "<PERSON>, English-Australian politician, 1st Premier of Queensland (d. 1905)", "html": "1831 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English-Australian politician, 1st <a href=\"https://wikipedia.org/wiki/Premier_of_Queensland\" title=\"Premier of Queensland\">Premier of Queensland</a> (d. 1905)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English-Australian politician, 1st <a href=\"https://wikipedia.org/wiki/Premier_of_Queensland\" title=\"Premier of Queensland\">Premier of Queensland</a> (d. 1905)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Premier of Queensland", "link": "https://wikipedia.org/wiki/Premier_of_Queensland"}]}, {"year": "1841", "text": "<PERSON>, English architect, designed the Woodborough Road Baptist Church (d. 1928)", "html": "1841 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English architect, designed the <a href=\"https://wikipedia.org/wiki/Woodborough_Road_Baptist_Church\" title=\"Woodborough Road Baptist Church\">Woodborough Road Baptist Church</a> (d. 1928)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>gill\"><PERSON></a>, English architect, designed the <a href=\"https://wikipedia.org/wiki/Woodborough_Road_Baptist_Church\" title=\"Woodborough Road Baptist Church\">Woodborough Road Baptist Church</a> (d. 1928)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>oth<PERSON>gill"}, {"title": "Woodborough Road Baptist Church", "link": "https://wikipedia.org/wiki/Woodborough_Road_Baptist_Church"}]}, {"year": "1843", "text": "<PERSON>, Scottish-English astronomer and author (d. 1914)", "html": "1843 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>(astronomer)\" title=\"<PERSON> (astronomer)\"><PERSON></a>, Scottish-English astronomer and author (d. 1914)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>(astronomer)\" title=\"<PERSON> (astronomer)\"><PERSON></a>, Scottish-English astronomer and author (d. 1914)", "links": [{"title": "<PERSON> (astronomer)", "link": "https://wikipedia.org/wiki/<PERSON>(astronomer)"}]}, {"year": "1851", "text": "<PERSON>, English physicist and academic (d. 1940)", "html": "1851 - <a href=\"https://wikipedia.org/wiki/Oliver_Lodge\" title=\"Oliver Lodge\">Oliver Lodge</a>, English physicist and academic (d. 1940)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Oliver_Lodge\" title=\"Oliver Lodge\">Oliver Lodge</a>, English physicist and academic (d. 1940)", "links": [{"title": "Oliver Lodge", "link": "https://wikipedia.org/wiki/Oliver_<PERSON>"}]}, {"year": "1857", "text": "<PERSON>, Canadian architect, engineer, and politician, 15th Mayor of Longueuil (d. 1909)", "html": "1857 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian architect, engineer, and politician, 15th <a href=\"https://wikipedia.org/wiki/List_of_mayors_of_Longueuil\" title=\"List of mayors of Longueuil\">Mayor of Longueuil</a> (d. 1909)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian architect, engineer, and politician, 15th <a href=\"https://wikipedia.org/wiki/List_of_mayors_of_Longueuil\" title=\"List of mayors of Longueuil\">Mayor of Longueuil</a> (d. 1909)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}, {"title": "List of mayors of Longueuil", "link": "https://wikipedia.org/wiki/List_of_mayors_of_Longueuil"}]}, {"year": "1858", "text": "<PERSON>, English botanist and explorer (d. 1927)", "html": "1858 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English botanist and explorer (d. 1927)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English botanist and explorer (d. 1927)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1858", "text": "<PERSON>, English painter and photographer (d. 1929)", "html": "1858 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English painter and photographer (d. 1929)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English painter and photographer (d. 1929)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1861", "text": "<PERSON>, English cricketer and umpire (d. 1927)", "html": "1861 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English cricketer and umpire (d. 1927)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English cricketer and umpire (d. 1927)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1864", "text": "<PERSON>, American ornithologist, photographer, and author (d. 1945)", "html": "1864 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(ornithologist)\" title=\"<PERSON> (ornithologist)\"><PERSON></a>, American ornithologist, photographer, and author (d. 1945)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(ornithologist)\" title=\"<PERSON> (ornithologist)\"><PERSON></a>, American ornithologist, photographer, and author (d. 1945)", "links": [{"title": "<PERSON> (ornithologist)", "link": "https://wikipedia.org/wiki/<PERSON>(ornithologist)"}]}, {"year": "1873", "text": "<PERSON>, French zoologist (d. 1944)", "html": "1873 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French <a href=\"https://wikipedia.org/wiki/Zoology\" title=\"Zoology\">zoologist</a> (d. 1944)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French <a href=\"https://wikipedia.org/wiki/Zoology\" title=\"Zoology\">zoologist</a> (d. 1944)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}, {"title": "Zoology", "link": "https://wikipedia.org/wiki/Zoology"}]}, {"year": "1877", "text": "<PERSON>, American admiral and politician (d. 1971)", "html": "1877 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American admiral and politician (d. 1971)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American admiral and politician (d. 1971)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1883", "text": "<PERSON><PERSON><PERSON>, French pole vaulter (d. 1969)", "html": "1883 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON> Gonder\"><PERSON><PERSON><PERSON></a>, French pole vaulter (d. 1969)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON> Gonder\"><PERSON><PERSON><PERSON></a>, French pole vaulter (d. 1969)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>nder"}]}, {"year": "1883", "text": "<PERSON>, Austrian-American anthropologist and academic (d. 1957)", "html": "1883 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Austrian-American anthropologist and academic (d. 1957)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Austrian-American anthropologist and academic (d. 1957)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1888", "text": "<PERSON><PERSON><PERSON><PERSON><PERSON>, Polish mathematician and academic (d. 1920)", "html": "1888 - <a href=\"https://wikipedia.org/wiki/Z<PERSON>g<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>g<PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON><PERSON></a>, Polish mathematician and academic (d. 1920)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>g<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>g<PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON><PERSON></a>, Polish mathematician and academic (d. 1920)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>g<PERSON><PERSON>_<PERSON>"}]}, {"year": "1890", "text": "<PERSON><PERSON>, Austrian soldier and painter (d. 1918)", "html": "1890 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Austrian soldier and painter (d. 1918)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Austrian soldier and painter (d. 1918)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/E<PERSON>_<PERSON>"}]}, {"year": "1892", "text": "<PERSON><PERSON><PERSON>, American novelist, journalist, and playwright (d. 1982)", "html": "1892 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American novelist, journalist, and playwright (d. 1982)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American novelist, journalist, and playwright (d. 1982)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1895", "text": "<PERSON><PERSON><PERSON><PERSON>, French chef (d. 1977)", "html": "1895 - <a href=\"https://wikipedia.org/wiki/Eug%C3%A9nie_Brazier\" title=\"<PERSON>ug<PERSON><PERSON> Brazier\"><PERSON><PERSON><PERSON><PERSON></a>, French chef (d. 1977)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Eug%C3%A9nie_Brazier\" title=\"<PERSON>ug<PERSON><PERSON> Brazier\"><PERSON><PERSON><PERSON><PERSON></a>, French chef (d. 1977)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Eug%C3%A9nie_<PERSON>razier"}]}, {"year": "1897", "text": "<PERSON>, English soldier and politician, Prime Minister of the United Kingdom (d. 1977)", "html": "1897 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English soldier and politician, <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_the_United_Kingdom\" title=\"Prime Minister of the United Kingdom\">Prime Minister of the United Kingdom</a> (d. 1977)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English soldier and politician, <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_the_United_Kingdom\" title=\"Prime Minister of the United Kingdom\">Prime Minister of the United Kingdom</a> (d. 1977)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Prime Minister of the United Kingdom", "link": "https://wikipedia.org/wiki/Prime_Minister_of_the_United_Kingdom"}]}, {"year": "1899", "text": "<PERSON>, German-American biochemist and academic, Nobel Prize laureate (d. 1986)", "html": "1899 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German-American biochemist and academic, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Physiology_or_Medicine\" title=\"Nobel Prize in Physiology or Medicine\">Nobel Prize</a> laureate (d. 1986)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German-American biochemist and academic, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Physiology_or_Medicine\" title=\"Nobel Prize in Physiology or Medicine\">Nobel Prize</a> laureate (d. 1986)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Nobel Prize in Physiology or Medicine", "link": "https://wikipedia.org/wiki/Nobel_Prize_in_Physiology_or_Medicine"}]}, {"year": "1899", "text": "<PERSON><PERSON><PERSON>, Ukrainian-American photographer and journalist (d. 1968)", "html": "1899 - <a href=\"https://wikipedia.org/wiki/Weegee\" title=\"Weegee\"><PERSON><PERSON><PERSON></a>, Ukrainian-American photographer and journalist (d. 1968)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Weegee\" title=\"Weegee\"><PERSON><PERSON><PERSON></a>, Ukrainian-American photographer and journalist (d. 1968)", "links": [{"title": "Weegee", "link": "https://wikipedia.org/wiki/Weegee"}]}, {"year": "1902", "text": "<PERSON><PERSON><PERSON>, Belgian lawyer and politician, Mayor of Ghent (d. 1973)", "html": "1902 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Belgian lawyer and politician, <a href=\"https://wikipedia.org/wiki/List_of_mayors_of_Ghent\" title=\"List of mayors of Ghent\">Mayor of Ghent</a> (d. 1973)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Belgian lawyer and politician, <a href=\"https://wikipedia.org/wiki/List_of_mayors_of_Ghent\" title=\"List of mayors of Ghent\">Mayor of Ghent</a> (d. 1973)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}, {"title": "List of mayors of Ghent", "link": "https://wikipedia.org/wiki/List_of_mayors_of_Ghent"}]}, {"year": "1905", "text": "<PERSON>, American sprinter and football player (d. 1988)", "html": "1905 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American sprinter and football player (d. 1988)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American sprinter and football player (d. 1988)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Ray_Barbuti"}]}, {"year": "1906", "text": "<PERSON><PERSON>, Italian poet (d. 1977)", "html": "1906 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Italian poet (d. 1977)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Italian poet (d. 1977)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>a"}]}, {"year": "1908", "text": "<PERSON>, Russian ballerina and educator (d. 2010)", "html": "1908 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian ballerina and educator (d. 2010)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian ballerina and educator (d. 2010)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>va"}]}, {"year": "1908", "text": "<PERSON>, German SS officer (d. 1975)", "html": "1908 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German <a href=\"https://wikipedia.org/wiki/SS\" class=\"mw-redirect\" title=\"SS\">SS</a> officer (d. 1975)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German <a href=\"https://wikipedia.org/wiki/SS\" class=\"mw-redirect\" title=\"SS\">SS</a> officer (d. 1975)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>zeny"}, {"title": "SS", "link": "https://wikipedia.org/wiki/SS"}]}, {"year": "1912", "text": "<PERSON>, Canadian ice hockey player and coach (d. 1993)", "html": "1912 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player and coach (d. 1993)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player and coach (d. 1993)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1912", "text": "<PERSON>, American psychologist and academic (d. 1961)", "html": "1912 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American psychologist and academic (d. 1961)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American psychologist and academic (d. 1961)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1913", "text": "<PERSON>, Canadian general (d. 1996)", "html": "1913 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian general (d. 1996)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian general (d. 1996)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1913", "text": "<PERSON>, Canadian admiral (d. 2005)", "html": "1913 - <a href=\"https://wikipedia.org/wiki/Desmond_<PERSON>\" title=\"Desmond <PERSON>\"><PERSON></a>, Canadian admiral (d. 2005)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Desmond_<PERSON>\" title=\"Desmond Piers\"><PERSON></a>, Canadian admiral (d. 2005)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Desmond_<PERSON>"}]}, {"year": "1914", "text": "<PERSON>, American actor (d. 1975)", "html": "1914 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor (d. 1975)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor (d. 1975)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1914", "text": "<PERSON>, Chinese-Japanese Go player (d. 2014)", "html": "1914 - <a href=\"https://wikipedia.org/wiki/Go_Seigen\" title=\"Go Seigen\"><PERSON> Seigen</a>, Chinese-Japanese <a href=\"https://wikipedia.org/wiki/Go_(game)\" title=\"Go (game)\">Go</a> player (d. 2014)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Go_Seigen\" title=\"Go Seigen\">Go Seigen</a>, Chinese-Japanese <a href=\"https://wikipedia.org/wiki/Go_(game)\" title=\"Go (game)\">Go</a> player (d. 2014)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Go_Seigen"}, {"title": "Go (game)", "link": "https://wikipedia.org/wiki/Go_(game)"}]}, {"year": "1915", "text": "<PERSON><PERSON><PERSON>, American actress (d. 1995)", "html": "1915 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American actress (d. 1995)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American actress (d. 1995)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1915", "text": "<PERSON>, English soldier and politician (d. 1997)", "html": "1915 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English soldier and politician (d. 1997)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English soldier and politician (d. 1997)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1915", "text": "<PERSON>, American banker and businessman (d. 2017)", "html": "1915 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American banker and businessman (d. 2017)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American banker and businessman (d. 2017)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1916", "text": "<PERSON>, American director and producer (d. 1991)", "html": "1916 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American director and producer (d. 1991)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON> Allen\"><PERSON></a>, American director and producer (d. 1991)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1916", "text": "<PERSON><PERSON>, Mexican-American politician and diplomat, 14th Governor of Arizona (d. 2015)", "html": "1916 - <a href=\"https://wikipedia.org/wiki/Ra%C3%BAl_H%C3%A9<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Mexican-American politician and diplomat, 14th <a href=\"https://wikipedia.org/wiki/Governor_of_Arizona\" class=\"mw-redirect\" title=\"Governor of Arizona\">Governor of Arizona</a> (d. 2015)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Ra%C3%BAl_H%C3%A9<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Mexican-American politician and diplomat, 14th <a href=\"https://wikipedia.org/wiki/Governor_of_Arizona\" class=\"mw-redirect\" title=\"Governor of Arizona\">Governor of Arizona</a> (d. 2015)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Ra%C3%BAl_H%C3%A9ctor_Castro"}, {"title": "Governor of Arizona", "link": "https://wikipedia.org/wiki/Governor_of_Arizona"}]}, {"year": "1918", "text": "<PERSON>, American architect (d. 1999)", "html": "1918 - <a href=\"https://wikipedia.org/wiki/Georgia_<PERSON>_<PERSON>_<PERSON>\" title=\"Georgia <PERSON>\"><PERSON></a>, American architect (d. 1999)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Georgia_<PERSON>_<PERSON>_<PERSON>\" title=\"Georgia <PERSON>\"><PERSON> <PERSON></a>, American architect (d. 1999)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1918", "text": "<PERSON>, Sri Lankan-Australian mathematician and academic (d. 2001)", "html": "1918 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>ezer\" class=\"mw-redirect\" title=\"Christie <PERSON> Eliezer\"><PERSON></a>, Sri Lankan-Australian mathematician and academic (d. 2001)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"Christie Jaya<PERSON>nam Eliezer\"><PERSON></a>, Sri Lankan-Australian mathematician and academic (d. 2001)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_Eliezer"}]}, {"year": "1919", "text": "<PERSON><PERSON>, German-American actress and educator (d. 2004)", "html": "1919 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, German-American actress and educator (d. 2004)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, German-American actress and educator (d. 2004)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Uta_Hagen"}]}, {"year": "1920", "text": "<PERSON>, American soldier and cartoonist (d. 2002)", "html": "1920 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(cartoonist)\" title=\"<PERSON> (cartoonist)\"><PERSON></a>, American soldier and cartoonist (d. 2002)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>(cartoonist)\" title=\"<PERSON> (cartoonist)\"><PERSON></a>, American soldier and cartoonist (d. 2002)", "links": [{"title": "<PERSON> (cartoonist)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>(cartoonist)"}]}, {"year": "1920", "text": "<PERSON>, English actor and screenwriter (d. 2000)", "html": "1920 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(actor)\" title=\"<PERSON> (actor)\"><PERSON></a>, English actor and screenwriter (d. 2000)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(actor)\" title=\"<PERSON> (actor)\"><PERSON></a>, English actor and screenwriter (d. 2000)", "links": [{"title": "<PERSON> (actor)", "link": "https://wikipedia.org/wiki/<PERSON>_(actor)"}]}, {"year": "1921", "text": "<PERSON>, Spanish director and screenwriter (d. 2010)", "html": "1921 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%ADa_Berlanga\" title=\"<PERSON>\"><PERSON></a>, Spanish director and screenwriter (d. 2010)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%ADa_Berlanga\" title=\"<PERSON>\"><PERSON></a>, Spanish director and screenwriter (d. 2010)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Luis_Garc%C3%ADa_Berlanga"}]}, {"year": "1921", "text": "<PERSON>, Canadian author and illustrator (d. 2005)", "html": "1921 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian author and illustrator (d. 2005)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian author and illustrator (d. 2005)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1922", "text": "<PERSON><PERSON><PERSON><PERSON>, Italian astrophysicist and author (d. 2013)", "html": "1922 - <a href=\"https://wikipedia.org/wiki/Margh<PERSON><PERSON>_<PERSON>ck\" title=\"Margh<PERSON><PERSON> Hack\"><PERSON><PERSON><PERSON><PERSON></a>, Italian astrophysicist and author (d. 2013)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Mar<PERSON><PERSON><PERSON>_<PERSON>ck\" title=\"Margh<PERSON><PERSON> Hack\"><PERSON><PERSON><PERSON><PERSON></a>, Italian astrophysicist and author (d. 2013)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Mar<PERSON><PERSON><PERSON>_<PERSON>ck"}]}, {"year": "1924", "text": "<PERSON>, American lieutenant and politician, 41st President of the United States (d. 2018)", "html": "1924 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lieutenant and politician, 41st <a href=\"https://wikipedia.org/wiki/President_of_the_United_States\" title=\"President of the United States\">President of the United States</a> (d. 2018)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lieutenant and politician, 41st <a href=\"https://wikipedia.org/wiki/President_of_the_United_States\" title=\"President of the United States\">President of the United States</a> (d. 2018)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>"}, {"title": "President of the United States", "link": "https://wikipedia.org/wiki/President_of_the_United_States"}]}, {"year": "1924", "text": "<PERSON><PERSON><PERSON>, German-American guitarist and radio host (d. 2013)", "html": "1924 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, German-American guitarist and radio host (d. 2013)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, German-American guitarist and radio host (d. 2013)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1928", "text": "<PERSON>, American singer-songwriter and actor (d. 2018)", "html": "1928 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and actor (d. 2018)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and actor (d. 2018)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Vic_Damone"}]}, {"year": "1928", "text": "<PERSON><PERSON>, Greek politician and diplomat, Greek Minister for Foreign Affairs", "html": "1928 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Greek politician and diplomat, <a href=\"https://wikipedia.org/wiki/Minister_for_Foreign_Affairs_(Greece)\" class=\"mw-redirect\" title=\"Minister for Foreign Affairs (Greece)\">Greek Minister for Foreign Affairs</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Greek politician and diplomat, <a href=\"https://wikipedia.org/wiki/Minister_for_Foreign_Affairs_(Greece)\" class=\"mw-redirect\" title=\"Minister for Foreign Affairs (Greece)\">Greek Minister for Foreign Affairs</a>", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}, {"title": "Minister for Foreign Affairs (Greece)", "link": "https://wikipedia.org/wiki/Minister_for_Foreign_Affairs_(Greece)"}]}, {"year": "1928", "text": "<PERSON>, American composer and director (d. 2024)", "html": "1928 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American composer and director (d. 2024)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American composer and director (d. 2024)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1929", "text": "<PERSON><PERSON><PERSON>, English author and critic (d. 1995)", "html": "1929 - <a href=\"https://wikipedia.org/wiki/Brigid_Brophy\" title=\"<PERSON>rig<PERSON> Brophy\"><PERSON><PERSON><PERSON></a>, English author and critic (d. 1995)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>rigid_Brophy\" title=\"Brig<PERSON> Brophy\"><PERSON><PERSON><PERSON></a>, English author and critic (d. 1995)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/B<PERSON><PERSON>_<PERSON>rophy"}]}, {"year": "1929", "text": "<PERSON>, Australian rugby league player (d. 2004)", "html": "1929 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian rugby league player (d. 2004)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Roy <PERSON>\"><PERSON></a>, Australian rugby league player (d. 2004)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1929", "text": "<PERSON>, German-Dutch diarist; victim of the Holocaust (d. 1945)", "html": "1929 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German-Dutch diarist; victim of the <a href=\"https://wikipedia.org/wiki/Holocaust\" class=\"mw-redirect\" title=\"Holocaust\">Holocaust</a> (d. 1945)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German-Dutch diarist; victim of the <a href=\"https://wikipedia.org/wiki/Holocaust\" class=\"mw-redirect\" title=\"Holocaust\">Holocaust</a> (d. 1945)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Holocaust", "link": "https://wikipedia.org/wiki/Holocaust"}]}, {"year": "1929", "text": "<PERSON><PERSON>, Pakistani linguist and academic (d. 2019)", "html": "1929 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Pakistani linguist and academic (d. 2019)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Pakistani linguist and academic (d. 2019)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1929", "text": "<PERSON>, Baron <PERSON>, Scottish lawyer, judge, and politician, Solicitor General for Scotland (d. 2017)", "html": "1929 - <a href=\"https://wikipedia.org/wiki/<PERSON>,_Baron_<PERSON>\" title=\"<PERSON>, Baron <PERSON>\"><PERSON>, Baron <PERSON></a>, Scottish lawyer, judge, and politician, <a href=\"https://wikipedia.org/wiki/Solicitor_General_for_Scotland\" title=\"Solicitor General for Scotland\">Solicitor General for Scotland</a> (d. 2017)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>,_Baron_<PERSON>\" title=\"<PERSON>, Baron <PERSON>\"><PERSON>, Baron <PERSON></a>, Scottish lawyer, judge, and politician, <a href=\"https://wikipedia.org/wiki/Solicitor_General_for_Scotland\" title=\"Solicitor General for Scotland\">Solicitor General for Scotland</a> (d. 2017)", "links": [{"title": "<PERSON>, Baron <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_<PERSON>_<PERSON>"}, {"title": "Solicitor General for Scotland", "link": "https://wikipedia.org/wiki/Solicitor_General_for_Scotland"}]}, {"year": "1930", "text": "<PERSON>, Australian cricketer (d. 1979)", "html": "1930 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>(cricketer)\" title=\"<PERSON> (cricketer)\"><PERSON></a>, Australian cricketer (d. 1979)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>(cricketer)\" title=\"<PERSON> (cricketer)\"><PERSON></a>, Australian cricketer (d. 1979)", "links": [{"title": "<PERSON> (cricketer)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>(cricketer)"}]}, {"year": "1930", "text": "<PERSON>, American chess player (d. 1976)", "html": "1930 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American chess player (d. 1976)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American chess player (d. 1976)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1930", "text": "<PERSON><PERSON>, Scottish racing driver and engineer (d. 1993)", "html": "1930 - <a href=\"https://wikipedia.org/wiki/Innes_Ireland\" title=\"Innes Ireland\"><PERSON>es Ireland</a>, Scottish racing driver and engineer (d. 1993)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Innes_Ireland\" title=\"Innes Ireland\"><PERSON>es Ireland</a>, Scottish racing driver and engineer (d. 1993)", "links": [{"title": "Innes Ireland", "link": "https://wikipedia.org/wiki/Innes_Ireland"}]}, {"year": "1930", "text": "<PERSON>, American actor and singer (d. 2017)", "html": "1930 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor and singer (d. 2017)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor and singer (d. 2017)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1931", "text": "<PERSON><PERSON><PERSON><PERSON>, American author and scholar (d. 2005)", "html": "1931 - <a href=\"https://wikipedia.org/wiki/Trevanian\" title=\"Trevanian\"><PERSON><PERSON><PERSON><PERSON></a>, American author and scholar (d. 2005)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Trevanian\" title=\"Trevanian\"><PERSON><PERSON><PERSON><PERSON></a>, American author and scholar (d. 2005)", "links": [{"title": "Trevanian", "link": "https://wikipedia.org/wiki/Trevanian"}]}, {"year": "1931", "text": "<PERSON><PERSON>, American novelist (d. 2005)", "html": "1931 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American novelist (d. 2005)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American novelist (d. 2005)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1932", "text": "<PERSON>, South African soprano and producer", "html": "1932 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, South African soprano and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, South African soprano and producer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1932", "text": "<PERSON><PERSON>, Ethiopian runner (d. 2002)", "html": "1932 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>old<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Ethiopian runner (d. 2002)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>old<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Ethiopian runner (d. 2002)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>olde"}]}, {"year": "1933", "text": "<PERSON>, American photographer and journalist (d. 2004)", "html": "1933 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(photographer)\" title=\"<PERSON> (photographer)\"><PERSON></a>, American photographer and journalist (d. 2004)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(photographer)\" title=\"<PERSON> (photographer)\"><PERSON></a>, American photographer and journalist (d. 2004)", "links": [{"title": "<PERSON> (photographer)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(photographer)"}]}, {"year": "1934", "text": "<PERSON>, American actor and cinematographer (d. 2001)", "html": "1934 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON><PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor and cinematographer (d. 2001)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON><PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor and cinematographer (d. 2001)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "1934", "text": "<PERSON>, English director and producer (d. 2021)", "html": "1934 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English director and producer (d. 2021)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English director and producer (d. 2021)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1935", "text": "<PERSON>, Australian cricketer (d. 2014)", "html": "1935 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian cricketer (d. 2014)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian cricketer (d. 2014)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1935", "text": "<PERSON>, English lawyer and judge", "html": "1935 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(English_judge)\" title=\"<PERSON> (English judge)\"><PERSON></a>, English lawyer and judge", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(English_judge)\" title=\"<PERSON> (English judge)\"><PERSON></a>, English lawyer and judge", "links": [{"title": "<PERSON> (English judge)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(English_judge)"}]}, {"year": "1937", "text": "<PERSON>, Russian-French mathematician and academic (d. 2010)", "html": "1937 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian-French mathematician and academic (d. 2010)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian-French mathematician and academic (d. 2010)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1937", "text": "<PERSON>, German footballer and manager (d. 2015)", "html": "1937 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German footballer and manager (d. 2015)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German footballer and manager (d. 2015)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1937", "text": "<PERSON><PERSON>, Hungarian-Austrian biologist and zoologist", "html": "1937 - <a href=\"https://wikipedia.org/wiki/Antal_Festetics\" title=\"Antal Festetics\"><PERSON><PERSON> Fest<PERSON></a>, Hungarian-Austrian biologist and zoologist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Antal_Festetics\" title=\"Antal Festetics\"><PERSON>tal Festetics</a>, Hungarian-Austrian biologist and zoologist", "links": [{"title": "Antal Festetics", "link": "https://wikipedia.org/wiki/Antal_Festetics"}]}, {"year": "1937", "text": "<PERSON><PERSON>, American record producer, guitarist, and songwriter (d. 2016)", "html": "1937 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> Moman\"><PERSON><PERSON></a>, American record producer, guitarist, and songwriter (d. 2016)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_Mom<PERSON>\" title=\"<PERSON><PERSON> Moman\"><PERSON><PERSON></a>, American record producer, guitarist, and songwriter (d. 2016)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>s_<PERSON>an"}]}, {"year": "1938", "text": "<PERSON><PERSON><PERSON>, Guinean lawyer and politician, 11th Prime Minister of Guinea (d. 2016)", "html": "1938 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>%C3%A9\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Guinean lawyer and politician, 11th <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Guinea\" class=\"mw-redirect\" title=\"Prime Minister of Guinea\">Prime Minister of Guinea</a> (d. 2016)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>%C3%A9\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Guinean lawyer and politician, 11th <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Guinea\" class=\"mw-redirect\" title=\"Prime Minister of Guinea\">Prime Minister of Guinea</a> (d. 2016)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>%C3%A9"}, {"title": "Prime Minister of Guinea", "link": "https://wikipedia.org/wiki/Prime_Minister_of_Guinea"}]}, {"year": "1938", "text": "<PERSON>, English-Australian actor", "html": "1938 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English-Australian actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English-Australian actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1939", "text": "<PERSON>, Australian rugby league player and coach (d. 2024)", "html": "1939 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(rugby_league)\" title=\"<PERSON> (rugby league)\"><PERSON></a>, Australian rugby league player and coach (d. 2024)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(rugby_league)\" title=\"<PERSON> (rugby league)\"><PERSON></a>, Australian rugby league player and coach (d. 2024)", "links": [{"title": "<PERSON> (rugby league)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(rugby_league)"}]}, {"year": "1939", "text": "<PERSON>, American sergeant and politician (d. 2003)", "html": "1939 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American sergeant and politician (d. 2003)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American sergeant and politician (d. 2003)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1940", "text": "<PERSON>, Canadian educator and politician", "html": "1940 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian educator and politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian educator and politician", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1941", "text": "<PERSON><PERSON>, American sportscaster", "html": "1941 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> <PERSON>\"><PERSON><PERSON></a>, American sportscaster", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> <PERSON>\"><PERSON><PERSON></a>, American sportscaster", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Marv_<PERSON>"}]}, {"year": "1941", "text": "<PERSON><PERSON>, American pianist and composer (d. 2021)", "html": "1941 - <a href=\"https://wikipedia.org/wiki/Chi<PERSON>_<PERSON>\" title=\"Chick Corea\"><PERSON><PERSON></a>, American pianist and composer (d. 2021)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Chi<PERSON>_<PERSON>\" title=\"Chick Corea\"><PERSON><PERSON></a>, American pianist and composer (d. 2021)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Chi<PERSON>_<PERSON>a"}]}, {"year": "1941", "text": "<PERSON>, English singer-songwriter, guitarist, and actor", "html": "1941 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(singer)\" title=\"<PERSON> (singer)\"><PERSON></a>, English singer-songwriter, guitarist, and actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(singer)\" title=\"<PERSON> (singer)\"><PERSON></a>, English singer-songwriter, guitarist, and actor", "links": [{"title": "<PERSON> (singer)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(singer)"}]}, {"year": "1941", "text": "<PERSON>, English singer-songwriter (d. 2013)", "html": "1941 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English singer-songwriter (d. 2013)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Presley\"><PERSON></a>, English singer-songwriter (d. 2013)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1941", "text": "<PERSON><PERSON><PERSON><PERSON>, American politician", "html": "1941 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>-<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>-<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American politician", "links": [{"title": "<PERSON><PERSON>-<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>-<PERSON>ard"}]}, {"year": "1942", "text": "<PERSON>, American singer-songwriter and producer (d. 2020)", "html": "1942 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and producer (d. 2020)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and producer (d. 2020)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1942", "text": "<PERSON>, German physiologist and biologist, Nobel Prize laureate", "html": "1942 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German physiologist and biologist, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Physiology_or_Medicine\" title=\"Nobel Prize in Physiology or Medicine\">Nobel Prize</a> laureate", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German physiologist and biologist, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Physiology_or_Medicine\" title=\"Nobel Prize in Physiology or Medicine\">Nobel Prize</a> laureate", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Nobel Prize in Physiology or Medicine", "link": "https://wikipedia.org/wiki/Nobel_Prize_in_Physiology_or_Medicine"}]}, {"year": "1945", "text": "<PERSON>, Northern Irish footballer and coach", "html": "1945 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Northern Irish footballer and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Northern Irish footballer and coach", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1946", "text": "<PERSON>, Canadian ice hockey player and coach", "html": "1946 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(hockey_coach)\" class=\"mw-redirect\" title=\"<PERSON> (hockey coach)\"><PERSON></a>, Canadian ice hockey player and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(hockey_coach)\" class=\"mw-redirect\" title=\"<PERSON> (hockey coach)\"><PERSON></a>, Canadian ice hockey player and coach", "links": [{"title": "<PERSON> (hockey coach)", "link": "https://wikipedia.org/wiki/<PERSON>_(hockey_coach)"}]}, {"year": "1946", "text": "<PERSON>, English footballer and manager", "html": "1946 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English footballer and manager", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English footballer and manager", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1946", "text": "<PERSON>, French physicist and academic", "html": "1946 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>r%C3%A9chignac\" title=\"<PERSON>\"><PERSON></a>, French physicist and academic", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>r%C3%A9chignac\" title=\"<PERSON>\"><PERSON></a>, French physicist and academic", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Catherine_Br%C3%A9chignac"}]}, {"year": "1948", "text": "<PERSON>, Austrian racing driver", "html": "1948 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Austrian racing driver", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Austrian racing driver", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1948", "text": "<PERSON>, German footballer", "html": "1948 - <a href=\"https://wikipedia.org/wiki/<PERSON>(footballer)\" title=\"<PERSON> (footballer)\"><PERSON></a>, German footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>(footballer)\" title=\"<PERSON> (footballer)\"><PERSON></a>, German footballer", "links": [{"title": "<PERSON> (footballer)", "link": "https://wikipedia.org/wiki/<PERSON>(footballer)"}]}, {"year": "1948", "text": "<PERSON>, American comic book writer and editor (d. 2017)", "html": "1948 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American comic book writer and editor (d. 2017)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American comic book writer and editor (d. 2017)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1949", "text": "<PERSON><PERSON>, German judge and politician", "html": "1949 - <a href=\"https://wikipedia.org/wiki/Jen<PERSON>_B%C3%B6hrnsen\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, German judge and politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Jen<PERSON>_B%C3%B6hrnsen\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, German judge and politician", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Jens_B%C3%B6hrnsen"}]}, {"year": "1949", "text": "<PERSON>, Canadian ice hockey player", "html": "1949 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1949", "text": "<PERSON>, English singer-songwriter, bass player, and producer (d. 2017)", "html": "1949 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English singer-songwriter, bass player, and producer (d. 2017)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English singer-songwriter, bass player, and producer (d. 2017)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1950", "text": "<PERSON><PERSON><PERSON>, Turkish singer-songwriter and guitarist", "html": "1950 - <a href=\"https://wikipedia.org/wiki/O%C4%9Fuz_Abadan\" title=\"Oğuz Abadan\"><PERSON><PERSON><PERSON></a>, Turkish singer-songwriter and guitarist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/O%C4%9Fuz_Abadan\" title=\"Oğuz Abadan\"><PERSON><PERSON><PERSON></a>, Turkish singer-songwriter and guitarist", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/O%C4%9Fuz_Abadan"}]}, {"year": "1950", "text": "<PERSON>, English politician", "html": "1950 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> F<PERSON>\"><PERSON></a>, English politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Fabricant\"><PERSON></a>, English politician", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>ab<PERSON>nt"}]}, {"year": "1950", "text": "<PERSON>, American actress", "html": "1950 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1951", "text": "<PERSON>, American musician and singer (d. 2007)", "html": "1951 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American musician and singer (d. 2007)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American musician and singer (d. 2007)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1951", "text": "<PERSON><PERSON><PERSON>, Armenian engineer and politician, 10th Prime Minister of Armenia (d. 2007)", "html": "1951 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Armenian engineer and politician, 10th <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Armenia\" title=\"Prime Minister of Armenia\">Prime Minister of Armenia</a> (d. 2007)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Armenian engineer and politician, 10th <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Armenia\" title=\"Prime Minister of Armenia\">Prime Minister of Armenia</a> (d. 2007)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Andranik_<PERSON>"}, {"title": "Prime Minister of Armenia", "link": "https://wikipedia.org/wiki/Prime_Minister_of_Armenia"}]}, {"year": "1952", "text": "<PERSON>, American academic and politician, 10th United States Secretary of Energy", "html": "1952 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American academic and politician, 10th <a href=\"https://wikipedia.org/wiki/United_States_Secretary_of_Energy\" title=\"United States Secretary of Energy\">United States Secretary of Energy</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American academic and politician, 10th <a href=\"https://wikipedia.org/wiki/United_States_Secretary_of_Energy\" title=\"United States Secretary of Energy\">United States Secretary of Energy</a>", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "United States Secretary of Energy", "link": "https://wikipedia.org/wiki/United_States_Secretary_of_Energy"}]}, {"year": "1952", "text": "<PERSON>, American country music singer-songwriter and guitarist", "html": "1952 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American country music singer-songwriter and guitarist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Brown\"><PERSON></a>, American country music singer-songwriter and guitarist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1953", "text": "<PERSON>, American singer-songwriter and guitarist", "html": "1953 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and guitarist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and guitarist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1953", "text": "<PERSON><PERSON><PERSON>, Icelandic politician (d. 2015)", "html": "1953 - <a href=\"https://wikipedia.org/wiki/%C3%81rni_<PERSON><PERSON>_J%C3%B<PERSON><PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Icelandic politician (d. 2015)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/%C3%81rni_<PERSON><PERSON>_J%C3%<PERSON><PERSON><PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Icelandic politician (d. 2015)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/%C3%81rni_Steinar_J%C3%B3<PERSON><PERSON><PERSON>"}]}, {"year": "1956", "text": "<PERSON>, Australian cricketer and sportscaster", "html": "1956 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian cricketer and sportscaster", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian cricketer and sportscaster", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1957", "text": "<PERSON>, American actor, director, and producer", "html": "1957 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor, director, and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor, director, and producer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1957", "text": "<PERSON><PERSON><PERSON>, Pakistani cricketer and coach", "html": "1957 - <a href=\"https://wikipedia.org/wiki/Javed_<PERSON>ndad\" title=\"<PERSON>ave<PERSON>ndad\"><PERSON><PERSON><PERSON></a>, Pakistani cricketer and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Javed_<PERSON>ndad\" title=\"Javed <PERSON>ndad\"><PERSON><PERSON><PERSON></a>, Pakistani cricketer and coach", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Javed_Miandad"}]}, {"year": "1958", "text": "<PERSON>, American singer-songwriter and guitarist", "html": "1958 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and guitarist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and guitarist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1958", "text": "<PERSON>, American writer, producer and director (d. 2025)", "html": "1958 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American writer, producer and director (d. 2025)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American writer, producer and director (d. 2025)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1959", "text": "<PERSON>, American singer-songwriter and musician", "html": "1959 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and musician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and musician", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1959", "text": "<PERSON>, Canadian actor and comedian", "html": "1959 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(comedian)\" class=\"mw-redirect\" title=\"<PERSON> (comedian)\"><PERSON></a>, Canadian actor and comedian", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(comedian)\" class=\"mw-redirect\" title=\"<PERSON> (comedian)\"><PERSON></a>, Canadian actor and comedian", "links": [{"title": "<PERSON> (comedian)", "link": "https://wikipedia.org/wiki/<PERSON>_(comedian)"}]}, {"year": "1960", "text": "<PERSON>, American basketball player and coach", "html": "1960 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player and coach", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1962", "text": "<PERSON>, Canadian psychologist, professor and cultural critic", "html": "1962 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian psychologist, professor and cultural critic", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian psychologist, professor and cultural critic", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1963", "text": "<PERSON>, French racing driver (d. 2012)", "html": "1963 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French racing driver (d. 2012)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French racing driver (d. 2012)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1963", "text": "<PERSON>, Australian footballer, coach, and actor", "html": "1963 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian footballer, coach, and actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Capper\"><PERSON></a>, Australian footballer, coach, and actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_Capper"}]}, {"year": "1964", "text": "<PERSON>, Irish racing driver", "html": "1964 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Irish racing driver", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Irish racing driver", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1964", "text": "<PERSON><PERSON><PERSON>, Japanese filmmaker", "html": "1964 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese filmmaker", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese filmmaker", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1965", "text": "<PERSON>, Australian rugby league player", "html": "1965 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian rugby league player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian rugby league player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1965", "text": "<PERSON>, American sprinter", "html": "1965 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American sprinter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American sprinter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>nce"}]}, {"year": "1965", "text": "<PERSON>, English actress", "html": "1965 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actress", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1966", "text": "<PERSON>, Australian rugby league player", "html": "1966 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian rugby league player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian rugby league player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Marc_<PERSON>"}]}, {"year": "1966", "text": "<PERSON>, Swiss cell biologist", "html": "1966 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swiss cell biologist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swiss cell biologist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1967", "text": "<PERSON><PERSON>, Estonian basketball player and coach", "html": "1967 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Estonian basketball player and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Estonian basketball player and coach", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1967", "text": "<PERSON>, English-Australian actress", "html": "1967 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%27Connor\" title=\"<PERSON>\"><PERSON></a>, English-Australian actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%27Connor\" title=\"<PERSON>\"><PERSON></a>, English-Australian actress", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Frances_O%27Connor"}]}, {"year": "1968", "text": "<PERSON>, American baseball player and coach", "html": "1968 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player and coach", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1968", "text": "<PERSON>, American bass player and songwriter (d. 1999)", "html": "1968 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(musician)\" class=\"mw-redirect\" title=\"<PERSON> (musician)\"><PERSON></a>, American bass player and songwriter (d. 1999)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(musician)\" class=\"mw-redirect\" title=\"<PERSON> (musician)\"><PERSON></a>, American bass player and songwriter (d. 1999)", "links": [{"title": "<PERSON> (musician)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(musician)"}]}, {"year": "1969", "text": "<PERSON><PERSON><PERSON>, Hungarian guitarist (d. 2007)", "html": "1969 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Hungarian guitarist (d. 2007)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Hungarian guitarist (d. 2007)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>i"}]}, {"year": "1969", "text": "<PERSON><PERSON><PERSON>, Mexican wrestler (d. 2013)", "html": "1969 - <a href=\"https://wikipedia.org/wiki/H%C3%A9ctor_Garza\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Mexican wrestler (d. 2013)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/H%C3%A9ctor_Garza\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Mexican wrestler (d. 2013)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/H%C3%A9ctor_Garza"}]}, {"year": "1969", "text": "<PERSON><PERSON>, American ice hockey player", "html": "1969 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American ice hockey player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American ice hockey player", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1969", "text": "<PERSON><PERSON><PERSON>, Austrian politician", "html": "1969 - <a href=\"https://wikipedia.org/wiki/<PERSON>-<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Austrian politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Austrian politician", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>-<PERSON>_<PERSON>"}]}, {"year": "1971", "text": "<PERSON>, American weightlifter and wrestler", "html": "1971 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American weightlifter and wrestler", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American weightlifter and wrestler", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1971", "text": "<PERSON>, American baseball player", "html": "1971 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1971", "text": "<PERSON><PERSON><PERSON><PERSON>, Caribbean-Dominican triple jumper and coach", "html": "1971 - <a href=\"https://wikipedia.org/wiki/J%C3%A9r%C3%B4me_<PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Caribbean-Dominican triple jumper and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/J%C3%A9r%C3%B4me_<PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Caribbean-Dominican triple jumper and coach", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/J%C3%A9r%C3%B4me_Romain"}]}, {"year": "1973", "text": "<PERSON>, American basketball player and coach", "html": "1973 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player and coach", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1973", "text": "<PERSON>, Australian footballer", "html": "1973 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1974", "text": "<PERSON><PERSON><PERSON><PERSON>, Brazilian footballer", "html": "1974 - <a href=\"https://wikipedia.org/wiki/Fl%C3%A1vio_Concei%C3%A7%C3%A3o\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Brazilian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Fl%C3%A1vio_Concei%C3%A7%C3%A3o\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Brazilian footballer", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Fl%C3%A1vio_Concei%C3%A7%C3%A3o"}]}, {"year": "1974", "text": "<PERSON><PERSON><PERSON>, Japanese baseball player", "html": "1974 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese baseball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese baseball player", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1975", "text": "<PERSON>, American wrestler and journalist", "html": "1975 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American wrestler and journalist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American wrestler and journalist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1975", "text": "<PERSON><PERSON><PERSON><PERSON>, French-American actress", "html": "1975 - <a href=\"https://wikipedia.org/wiki/St%C3%A9<PERSON><PERSON>_Szostak\" title=\"<PERSON><PERSON><PERSON><PERSON> Szostak\"><PERSON><PERSON><PERSON><PERSON></a>, French-American actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/St%C3%A9<PERSON><PERSON>_Szostak\" title=\"<PERSON><PERSON><PERSON><PERSON> Szostak\"><PERSON><PERSON><PERSON><PERSON></a>, French-American actress", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/St%C3%A9phanie_Szostak"}]}, {"year": "1976", "text": "<PERSON><PERSON><PERSON>, American basketball player and sportscaster", "html": "1976 - <a href=\"https://wikipedia.org/wiki/Antawn_Jamison\" title=\"Antawn Jamison\"><PERSON><PERSON><PERSON></a>, American basketball player and sportscaster", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Antawn_Jamison\" title=\"Antawn Jamison\"><PERSON><PERSON><PERSON></a>, American basketball player and sportscaster", "links": [{"title": "Antawn Jamison", "link": "https://wikipedia.org/wiki/Antawn_Jamison"}]}, {"year": "1976", "text": "<PERSON>, Zimbabwean cricketer", "html": "1976 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(cricketer)\" title=\"<PERSON> (cricketer)\"><PERSON></a>, Zimbabwean cricketer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(cricketer)\" title=\"<PERSON> (cricketer)\"><PERSON></a>, Zimbabwean cricketer", "links": [{"title": "<PERSON> (cricketer)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(cricketer)"}]}, {"year": "1976", "text": "<PERSON>, Danish footballer", "html": "1976 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%B8<PERSON><PERSON>\" title=\"<PERSON>\"><PERSON></a>, Danish footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Thomas_S%C3%B<PERSON><PERSON><PERSON>\" title=\"<PERSON>\"><PERSON></a>, Danish footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Thomas_S%C3%B8<PERSON><PERSON>"}]}, {"year": "1976", "text": "<PERSON>, English author", "html": "1976 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English author", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English author", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1977", "text": "<PERSON>, Canadian ice hockey player", "html": "1977 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1978", "text": "<PERSON>, English rugby player", "html": "1978 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English rugby player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English rugby player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1979", "text": "<PERSON>, American football player", "html": "1979 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_Clark"}]}, {"year": "1979", "text": "<PERSON><PERSON>, Canadian wrestler", "html": "1979 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Canadian wrestler", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Canadian wrestler", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1979", "text": "<PERSON>, Argentine footballer", "html": "1979 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Argentine footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Diego Milit<PERSON>\"><PERSON></a>, Argentine footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1979", "text": "<PERSON><PERSON>, Swedish singer-songwriter, musician, and record producer", "html": "1979 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Swedish singer-songwriter, musician, and record producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Swedish singer-songwriter, musician, and record producer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>yn"}]}, {"year": "1979", "text": "<PERSON>, American basketball player and coach", "html": "1979 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player and coach", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1980", "text": "<PERSON>, Italian rugby player", "html": "1980 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian rugby player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian rugby player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1981", "text": "<PERSON><PERSON>, Latvian basketball player", "html": "1981 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Latvian basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Latvian basketball player", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>s"}]}, {"year": "1981", "text": "<PERSON><PERSON>, Brazilian model and actress", "html": "1981 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Brazilian model and actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Brazilian model and actress", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Adrian<PERSON>_<PERSON>"}]}, {"year": "1982", "text": "<PERSON><PERSON><PERSON><PERSON>, Indian weightlifter", "html": "1982 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Indian weightlifter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Indian weightlifter", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>i"}]}, {"year": "1982", "text": "<PERSON>, English cricketer", "html": "1982 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English cricketer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English cricketer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1983", "text": "<PERSON>, South African rugby player", "html": "1983 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, South African rugby player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Bryan <PERSON>\"><PERSON></a>, South African rugby player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Bryan_Habana"}]}, {"year": "1983", "text": "<PERSON>, Canadian soccer player", "html": "1983 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian soccer player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian soccer player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1984", "text": "<PERSON>, Kenyan-Qatari runner", "html": "1984 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Kenyan-Qatari runner", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Kenyan-Qatari runner", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/James_<PERSON>"}]}, {"year": "1984", "text": "<PERSON>, Spanish footballer", "html": "1984 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Spanish footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Spanish footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1985", "text": "<PERSON>, American actor", "html": "1985 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1985", "text": "<PERSON>, American computer programmer, co-created Mozilla Firefox", "html": "1985 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American computer programmer, co-created <a href=\"https://wikipedia.org/wiki/Mozilla_Firefox\" class=\"mw-redirect\" title=\"Mozilla Firefox\">Mozilla Firefox</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American computer programmer, co-created <a href=\"https://wikipedia.org/wiki/Mozilla_Firefox\" class=\"mw-redirect\" title=\"Mozilla Firefox\">Mozilla Firefox</a>", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Mozilla Firefox", "link": "https://wikipedia.org/wiki/Mozilla_Firefox"}]}, {"year": "1985", "text": "<PERSON>, Australian rugby league player and sportscaster", "html": "1985 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian rugby league player and sportscaster", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Sam <PERSON>\"><PERSON></a>, Australian rugby league player and sportscaster", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>day"}]}, {"year": "1985", "text": "<PERSON>, American model, actress, and author", "html": "1985 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American model, actress, and author", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American model, actress, and author", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1986", "text": "<PERSON><PERSON>, Australian politician", "html": "1986 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Australian politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Australian politician", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1988", "text": "<PERSON><PERSON>, Swiss footballer", "html": "1988 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Swiss footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Swiss footballer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1988", "text": "<PERSON><PERSON><PERSON>, Chilean footballer", "html": "1988 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Chilean footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Chilean footballer", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1989", "text": "<PERSON>, Swedish ice hockey player", "html": "1989 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swedish ice hockey player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swedish ice hockey player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1989", "text": "<PERSON>, Ethiopian runner", "html": "1989 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Ethiopian runner", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Ethiopian runner", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1990", "text": "<PERSON><PERSON>, American basketball player", "html": "1990 - <a href=\"https://wikipedia.org/wiki/Jrue_Holiday\" title=\"Jrue Holiday\"><PERSON><PERSON> <PERSON></a>, American basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Jrue_Holiday\" title=\"Jrue Holiday\"><PERSON><PERSON> <PERSON></a>, American basketball player", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Jr<PERSON>_Holiday"}]}, {"year": "1990", "text": "<PERSON>, English footballer", "html": "1990 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1991", "text": "<PERSON><PERSON><PERSON><PERSON><PERSON>, Venezuelan baseball player", "html": "1991 - <a href=\"https://wikipedia.org/wiki/Avisa%C3%ADl_Garc%C3%ADa\" title=\"Avisa<PERSON><PERSON> García\"><PERSON><PERSON><PERSON><PERSON><PERSON></a>, Venezuelan baseball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Avisa%C3%ADl_Garc%C3%ADa\" title=\"Avisa<PERSON><PERSON> García\"><PERSON><PERSON><PERSON><PERSON><PERSON></a>, Venezuelan baseball player", "links": [{"title": "Avis<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Avisa%C3%ADl_Garc%C3%ADa"}]}, {"year": "1992", "text": "<PERSON>, Brazilian footballer", "html": "1992 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Brazilian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Brazilian footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1992", "text": "<PERSON>, American actress and musician", "html": "1992 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress and musician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress and musician", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Allie_DiMeco"}]}, {"year": "1994", "text": "<PERSON>, American rapper and singer-songwriter", "html": "1994 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Toliver\"><PERSON></a>, American rapper and singer-songwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Don Toliver\"><PERSON></a>, American rapper and singer-songwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_Toliver"}]}, {"year": "1996", "text": "<PERSON>, Swedish ice hockey player", "html": "1996 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swedish ice hockey player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swedish ice hockey player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1996", "text": "<PERSON><PERSON><PERSON>, Barbadian netball player", "html": "1996 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Barbadian netball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Barbadian netball player", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1999", "text": "<PERSON><PERSON><PERSON><PERSON><PERSON>, Indian YouTuber", "html": "1999 - <a href=\"https://wikipedia.org/wiki/CarryMinati\" title=\"CarryMinati\"><PERSON><PERSON><PERSON><PERSON><PERSON></a>, Indian YouTuber", "no_year_html": "<a href=\"https://wikipedia.org/wiki/CarryMinati\" title=\"CarryMinati\"><PERSON><PERSON><PERSON><PERSON><PERSON></a>, Indian YouTuber", "links": [{"title": "Carry<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/CarryMinati"}]}], "Deaths": [{"year": "796", "text": "<PERSON><PERSON>, Muslim emir (b. 757)", "html": "796 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_I_of_C%C3%B3rdoba\" title=\"<PERSON>ham I of Córdoba\"><PERSON>ham I</a>, Muslim <a href=\"https://wikipedia.org/wiki/Emir\" title=\"Emir\">emir</a> (<abbr title=\"born\">b.</abbr> 757)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_I_of_C%C3%B3rdoba\" title=\"<PERSON><PERSON> I of Córdoba\"><PERSON>ham I</a>, Muslim <a href=\"https://wikipedia.org/wiki/Emir\" title=\"Emir\">emir</a> (<abbr title=\"born\">b.</abbr> 757)", "links": [{"title": "<PERSON><PERSON> of Córdoba", "link": "https://wikipedia.org/wiki/<PERSON>ham_I_of_C%C3%B3rdoba"}, {"title": "<PERSON>ir", "link": "https://wikipedia.org/wiki/Emir"}]}, {"year": "816", "text": "<PERSON> (b. 750)", "html": "816 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"Pope <PERSON>\">Pope <PERSON></a> (b. 750)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\">Pope <PERSON></a> (b. 750)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "918", "text": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, Mercian daughter of <PERSON> the <PERSON> (b. 870)", "html": "918 - <a href=\"https://wikipedia.org/wiki/%C3%86thelfl%C3%A6d\" title=\"<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON></a>, Mercian daughter of <a href=\"https://wikipedia.org/wiki/<PERSON>_the_Great\" title=\"<PERSON> the Great\"><PERSON> the Great</a> (b. 870)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/%C3%86thelfl%C3%A6d\" title=\"<PERSON><PERSON><PERSON><PERSON>æ<PERSON>\"><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON></a>, Mercian daughter of <a href=\"https://wikipedia.org/wiki/<PERSON>_the_Great\" title=\"<PERSON> the Great\"><PERSON> the Great</a> (b. 870)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/%C3%86thelfl%C3%A6d"}, {"title": "<PERSON> the Great", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_Great"}]}, {"year": "1020", "text": "<PERSON><PERSON><PERSON><PERSON>, English archbishop (b. 999)", "html": "1020 - <a href=\"https://wikipedia.org/wiki/Lyfing_(Archbishop_of_Canterbury)\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON><PERSON> (Archbishop of Canterbury)\"><PERSON><PERSON>fing</a>, English archbishop (b. 999)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Lyfing_(Archbishop_of_Canterbury)\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON><PERSON> (Archbishop of Canterbury)\"><PERSON><PERSON>fing</a>, English archbishop (b. 999)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON> (Archbishop of Canterbury)", "link": "https://wikipedia.org/wiki/Lyfing_(Archbishop_of_Canterbury)"}]}, {"year": "1036", "text": "<PERSON><PERSON>, Italian bishop (b. 990)", "html": "1036 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_(bishop_of_Arezzo)\" title=\"<PERSON><PERSON> (bishop of Arezzo)\"><PERSON><PERSON></a>, Italian bishop (b. 990)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_(bishop_of_Arezzo)\" title=\"<PERSON><PERSON> (bishop of Arezzo)\"><PERSON><PERSON></a>, Italian bishop (b. 990)", "links": [{"title": "<PERSON><PERSON> (bishop of Arezzo)", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_(bishop_of_Arezzo)"}]}, {"year": "1144", "text": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, Persian theologian (b. 1075)", "html": "1144 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>\" title=\"Al-Zamakhshari\"><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON></a>, Persian theologian (b. 1075)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Al<PERSON>\" title=\"Al-Zamakhshari\"><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON></a>, Persian theologian (b. 1075)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>-<PERSON>"}]}, {"year": "1152", "text": "<PERSON> of Scotland, 3rd Earl of Huntingdon (b. 1114)", "html": "1152 - <a href=\"https://wikipedia.org/wiki/<PERSON>_of_Scotland\" title=\"<PERSON> of Scotland\"><PERSON> of Scotland</a>, 3rd Earl of Huntingdon (b. 1114)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_of_Scotland\" title=\"<PERSON> of Scotland\"><PERSON> of Scotland</a>, 3rd Earl of Huntingdon (b. 1114)", "links": [{"title": "Henry of Scotland", "link": "https://wikipedia.org/wiki/Henry_of_Scotland"}]}, {"year": "1266", "text": "<PERSON>, Prince of Anhalt-Aschersleben (b. 1215)", "html": "1266 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Prince_of_Anhalt-Aschersleben\" title=\"<PERSON>, Prince of Anhalt-Aschersleben\"><PERSON>, Prince of Anhalt-Aschersleben</a> (b. 1215)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Prince_of_Anhalt-Aschersleben\" title=\"<PERSON>, Prince of Anhalt-Aschersleben\"><PERSON>, Prince of Anhalt-Aschersleben</a> (b. 1215)", "links": [{"title": "<PERSON>, Prince of Anhalt-Aschersleben", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Prince_of_Anhalt-Aschersleben"}]}, {"year": "1294", "text": "<PERSON> of Brienne, Count of Eu", "html": "1294 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Brienne,_Count_of_Eu\" title=\"<PERSON> of Brienne, Count of Eu\"><PERSON> of Brienne, Count of Eu</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Brienne,_Count_of_Eu\" title=\"<PERSON> of Brienne, Count of Eu\"><PERSON> of Brienne, Count of Eu</a>", "links": [{"title": "<PERSON> of Brienne, Count of Eu", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>,_Count_<PERSON>_<PERSON>u"}]}, {"year": "1418", "text": "<PERSON>, Count of Armagnac (b. 1360)", "html": "1418 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Count_of_Armagnac\" title=\"<PERSON>, Count of Armagnac\"><PERSON>, Count of Armagnac</a> (b. 1360)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Count_of_Armagnac\" title=\"<PERSON>, Count of Armagnac\"><PERSON>, Count of Armagnac</a> (b. 1360)", "links": [{"title": "<PERSON>, Count of Armagnac", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Count_<PERSON>_<PERSON>"}]}, {"year": "1420", "text": "<PERSON>, Count of Nassau-Siegen (b. 1362)", "html": "1420 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Count_of_Nassau-Siegen\" title=\"<PERSON>, Count of Nassau-Siegen\"><PERSON>, Count of Nassau-Siegen</a> (b. 1362)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Count_of_Nassau-Siegen\" title=\"<PERSON>, Count of Nassau-Siegen\"><PERSON>, Count of Nassau-Siegen</a> (b. 1362)", "links": [{"title": "<PERSON>, Count of Nassau-Siegen", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Count_of_Nassau-Siegen"}]}, {"year": "1435", "text": "<PERSON>, 14th Earl of Arundel, English commander (b. 1408)", "html": "1435 - <a href=\"https://wikipedia.org/wiki/<PERSON>,_14th_Earl_of_Arundel\" class=\"mw-redirect\" title=\"<PERSON>, 14th Earl of Arundel\"><PERSON>, 14th Earl of Arundel</a>, English commander (b. 1408)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>,_14th_Earl_of_Arundel\" class=\"mw-redirect\" title=\"<PERSON>, 14th Earl of Arundel\"><PERSON>, 14th Earl of Arundel</a>, English commander (b. 1408)", "links": [{"title": "<PERSON>, 14th Earl of Arundel", "link": "https://wikipedia.org/wiki/<PERSON>,_14th_Earl_of_Arundel"}]}, {"year": "1478", "text": "<PERSON><PERSON><PERSON><PERSON>, Marquis of Mantua (b. 1412)", "html": "1478 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>_<PERSON>,_Marquis_of_Mantua\" title=\"<PERSON><PERSON><PERSON><PERSON>, Marquis of Mantua\"><PERSON><PERSON><PERSON><PERSON>, Marquis of Mantua</a> (b. 1412)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>_<PERSON>,_Marquis_of_Mantua\" title=\"<PERSON><PERSON><PERSON><PERSON>, Marquis of Mantua\"><PERSON><PERSON><PERSON><PERSON>, Marquis of Mantua</a> (b. 1412)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>, Marquis of Mantua", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>_<PERSON>,_Marquis_of_Mantua"}]}, {"year": "1524", "text": "<PERSON>, Spanish conquistador (b. 1465)", "html": "1524 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>el%C3%<PERSON><PERSON><PERSON>_de_Cu%C3%A9llar\" title=\"<PERSON> C<PERSON>llar\"><PERSON>llar</a>, Spanish conquistador (b. 1465)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Diego_<PERSON>%C3%<PERSON><PERSON>quez_de_Cu%C3%A9llar\" title=\"<PERSON> C<PERSON>llar\"><PERSON>llar</a>, Spanish conquistador (b. 1465)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Diego_Vel%C3%<PERSON><PERSON><PERSON>_de_Cu%C3%A9llar"}]}, {"year": "1560", "text": "<PERSON><PERSON>, Japanese warrior (b. 1506)", "html": "1560 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Japanese warrior (b. 1506)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Japanese warrior (b. 1506)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>ri"}]}, {"year": "1560", "text": "<PERSON><PERSON><PERSON>, Japanese daimyō (b. 1519)", "html": "1560 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese daimyō (b. 1519)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese daimyō (b. 1519)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON><PERSON>"}]}, {"year": "1565", "text": "<PERSON><PERSON>, French philologist and scholar (b. 1512)", "html": "1565 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, French philologist and scholar (b. 1512)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, French philologist and scholar (b. 1512)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1567", "text": "<PERSON>, 1st Baron <PERSON>, English politician, Lord Chancellor of England (b. 1490)", "html": "1567 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_1st_Baron_<PERSON>\" title=\"<PERSON>, 1st Baron <PERSON>\"><PERSON>, 1st Baron <PERSON></a>, English politician, <a href=\"https://wikipedia.org/wiki/Lord_Chancellor\" title=\"Lord Chancellor\">Lord Chancellor of England</a> (b. 1490)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_1st_Baron_<PERSON>\" title=\"<PERSON>, 1st Baron <PERSON>\"><PERSON>, 1st Baron <PERSON></a>, English politician, <a href=\"https://wikipedia.org/wiki/Lord_Chancellor\" title=\"Lord Chancellor\">Lord Chancellor of England</a> (b. 1490)", "links": [{"title": "<PERSON>, 1st Baron <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_1st_Baron_<PERSON>"}, {"title": "Lord Chancellor", "link": "https://wikipedia.org/wiki/Lord_Chancellor"}]}, {"year": "1574", "text": "<PERSON><PERSON> of France, Duchess of Ferrara (b.1510)", "html": "1574 - <a href=\"https://wikipedia.org/wiki/Ren%C3%A9e_of_France\" title=\"<PERSON><PERSON> of France\"><PERSON><PERSON> of France</a>, Duchess of Ferrara (b.1510)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Ren%C3%A9e_of_France\" title=\"<PERSON><PERSON> of France\"><PERSON><PERSON> of France</a>, Duchess of Ferrara (b.1510)", "links": [{"title": "<PERSON><PERSON> of France", "link": "https://wikipedia.org/wiki/Ren%C3%A9e_of_France"}]}, {"year": "1647", "text": "<PERSON>, English scholar and educator (b. 1575)", "html": "1647 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English scholar and educator (b. 1575)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English scholar and educator (b. 1575)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1668", "text": "<PERSON>, 2nd Viscount <PERSON>, English politician (b. 1599)", "html": "1668 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_2nd_Viscount_<PERSON><PERSON>\" title=\"<PERSON>, 2nd Viscount <PERSON>\"><PERSON>, 2nd Viscount <PERSON></a>, English politician (b. 1599)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>,_2nd_Viscount_<PERSON><PERSON>\" title=\"<PERSON>, 2nd Viscount <PERSON>\"><PERSON>, 2nd Viscount <PERSON></a>, English politician (b. 1599)", "links": [{"title": "<PERSON>, 2nd Viscount <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_2nd_Viscount_<PERSON>"}]}, {"year": "1675", "text": "<PERSON>, Duke of Savoy (b. 1634)", "html": "1675 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>,_Duke_of_Savoy\" title=\"<PERSON>, Duke of Savoy\"><PERSON>, Duke of Savoy</a> (b. 1634)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>,_Duke_of_Savoy\" title=\"<PERSON>, Duke of Savoy\"><PERSON>, Duke of Savoy</a> (b. 1634)", "links": [{"title": "<PERSON>, Duke of Savoy", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>,_Duke_of_Savoy"}]}, {"year": "1734", "text": "<PERSON>, 1st Duke of Berwick, French-English general and politician, Lord Lieutenant of Hampshire (b. 1670)", "html": "1734 - <a href=\"https://wikipedia.org/wiki/<PERSON>,_1st_Duke_of_Berwick\" title=\"<PERSON>, 1st Duke of Berwick\"><PERSON>, 1st Duke of Berwick</a>, French-English general and politician, <a href=\"https://wikipedia.org/wiki/Lord_Lieutenant_of_Hampshire\" title=\"Lord Lieutenant of Hampshire\">Lord Lieutenant of Hampshire</a> (b. 1670)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>,_1st_Duke_of_Berwick\" title=\"<PERSON>, 1st Duke of Berwick\"><PERSON>, 1st Duke of Berwick</a>, French-English general and politician, <a href=\"https://wikipedia.org/wiki/Lord_Lieutenant_of_Hampshire\" title=\"Lord Lieutenant of Hampshire\">Lord Lieutenant of Hampshire</a> (b. 1670)", "links": [{"title": "<PERSON>, 1st Duke of Berwick", "link": "https://wikipedia.org/wiki/<PERSON>,_1st_Duke_of_Berwick"}, {"title": "Lord Lieutenant of Hampshire", "link": "https://wikipedia.org/wiki/Lord_Lieutenant_of_Hampshire"}]}, {"year": "1758", "text": "<PERSON> <PERSON> of Prussia (b. 1722)", "html": "1758 - <a href=\"https://wikipedia.org/wiki/Prince_<PERSON>_<PERSON>_of_Prussia\" title=\"Prince <PERSON> of Prussia\">Prince <PERSON> of Prussia</a> (b. 1722)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Prince_<PERSON>_<PERSON>_of_Prussia\" title=\"Prince <PERSON> of Prussia\">Prince <PERSON> of Prussia</a> (b. 1722)", "links": [{"title": "Prince <PERSON> of Prussia", "link": "https://wikipedia.org/wiki/Prince_Augustus_<PERSON>_of_Prussia"}]}, {"year": "1772", "text": "<PERSON><PERSON><PERSON>, French explorer (b. 1724)", "html": "1772 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, French explorer (b. 1724)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, French explorer (b. 1724)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1778", "text": "<PERSON>, American merchant and politician (b. 1716)", "html": "1778 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American merchant and politician (b. 1716)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American merchant and politician (b. 1716)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1816", "text": "<PERSON>, French general (b. 1757)", "html": "1816 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, French general (b. 1757)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, French general (b. 1757)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1818", "text": "<PERSON><PERSON><PERSON><PERSON>, Ethiopian emperor", "html": "1818 - <a href=\"https://wikipedia.org/wiki/Egwale_<PERSON>\" title=\"<PERSON>gwale <PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Ethiopian emperor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Egwale_<PERSON>\" title=\"Egwale <PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Ethiopian emperor", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Egwale_<PERSON>yon"}]}, {"year": "1841", "text": "<PERSON><PERSON>, Greek composer, archaeologist, and philologist (b. 1786)", "html": "1841 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_(composer)\" title=\"<PERSON><PERSON> (composer)\"><PERSON><PERSON></a>, Greek composer, archaeologist, and philologist (b. 1786)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_(composer)\" title=\"<PERSON><PERSON> (composer)\"><PERSON><PERSON></a>, Greek composer, archaeologist, and philologist (b. 1786)", "links": [{"title": "<PERSON><PERSON> (composer)", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_(composer)"}]}, {"year": "1900", "text": "<PERSON><PERSON><PERSON>, American journalist and author (b. 1820)", "html": "1900 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"Lucret<PERSON> Hale\"><PERSON><PERSON><PERSON></a>, American journalist and author (b. 1820)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"Lucretia <PERSON> Hale\"><PERSON><PERSON><PERSON></a>, American journalist and author (b. 1820)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Lucretia_Peabody_Hale"}]}, {"year": "1904", "text": "<PERSON> Renesse-Bre<PERSON>bach (b. 1836)", "html": "1904 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_Renesse-Breidbach\" title=\"<PERSON> of Renesse-Breidbach\"><PERSON> of Renesse-Breidbach</a> (b. 1836)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_Renesse-Breidbach\" title=\"<PERSON> of Renesse-Breidbach\"><PERSON> of Renesse-Breidbach</a> (b. 1836)", "links": [{"title": "Camille of Renesse-Breidbach", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_Rene<PERSON>-Breidbach"}]}, {"year": "1912", "text": "<PERSON><PERSON><PERSON><PERSON><PERSON>, French economist and academic, Nobel Prize laureate (b. 1822)", "html": "1912 - <a href=\"https://wikipedia.org/wiki/Fr%C3%A9d%C3%A9ric_Passy\" title=\"Fréd<PERSON><PERSON> Passy\"><PERSON><PERSON><PERSON><PERSON><PERSON></a>, French economist and academic, <a href=\"https://wikipedia.org/wiki/Nobel_Peace_Prize\" title=\"Nobel Peace Prize\">Nobel Prize</a> laureate (b. 1822)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Fr%C3%A9d%C3%A9ric_Passy\" title=\"Fréd<PERSON><PERSON> Passy\"><PERSON><PERSON><PERSON><PERSON><PERSON></a>, French economist and academic, <a href=\"https://wikipedia.org/wiki/Nobel_Peace_Prize\" title=\"Nobel Peace Prize\">Nobel Prize</a> laureate (b. 1822)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Fr%C3%A9d%C3%A9ric_Passy"}, {"title": "Nobel Peace Prize", "link": "https://wikipedia.org/wiki/Nobel_Peace_Prize"}]}, {"year": "1917", "text": "<PERSON>, Venezuelan-American singer-songwriter, pianist, and conductor (b. 1853)", "html": "1917 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%B1o\" title=\"<PERSON>\"><PERSON></a>, Venezuelan-American singer-songwriter, pianist, and conductor (b. 1853)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%B1o\" title=\"<PERSON>\"><PERSON></a>, Venezuelan-American singer-songwriter, pianist, and conductor (b. 1853)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%B1o"}]}, {"year": "1932", "text": "<PERSON>, Dutch lawyer and politician, Prime Minister of the Netherlands (b. 1852)", "html": "1932 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch lawyer and politician, <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_the_Netherlands\" title=\"Prime Minister of the Netherlands\">Prime Minister of the Netherlands</a> (b. 1852)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch lawyer and politician, <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_the_Netherlands\" title=\"Prime Minister of the Netherlands\">Prime Minister of the Netherlands</a> (b. 1852)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Prime Minister of the Netherlands", "link": "https://wikipedia.org/wiki/Prime_Minister_of_the_Netherlands"}]}, {"year": "1937", "text": "<PERSON>, Russian general (b. 1893)", "html": "1937 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian general (b. 1893)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian general (b. 1893)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1944", "text": "<PERSON>, German general (b. 1891)", "html": "1944 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German general (b. 1891)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German general (b. 1891)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1946", "text": "<PERSON><PERSON><PERSON><PERSON><PERSON>, Canadian politician, mayor of Montreal (b. 1869)", "html": "1946 - <a href=\"https://wikipedia.org/wiki/M%C3%A9d%C3%<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON><PERSON></a>, Canadian politician, mayor of Montreal (b. 1869)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/M%C3%A9d%C3%<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON><PERSON></a>, Canadian politician, mayor of Montreal (b. 1869)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/M%C3%A9d%C3%A9<PERSON>_<PERSON>"}]}, {"year": "1952", "text": "<PERSON>, Australian politician, 27th Premier of Victoria (b. 1875)", "html": "1952 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>(politician)\" title=\"<PERSON> (politician)\"><PERSON></a>, Australian politician, 27th <a href=\"https://wikipedia.org/wiki/Premier_of_Victoria\" title=\"Premier of Victoria\">Premier of Victoria</a> (b. 1875)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>(politician)\" title=\"<PERSON> (politician)\"><PERSON></a>, Australian politician, 27th <a href=\"https://wikipedia.org/wiki/Premier_of_Victoria\" title=\"Premier of Victoria\">Premier of Victoria</a> (b. 1875)", "links": [{"title": "<PERSON> (politician)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(politician)"}, {"title": "Premier of Victoria", "link": "https://wikipedia.org/wiki/Premier_of_Victoria"}]}, {"year": "1957", "text": "<PERSON>, American saxophonist, composer, and bandleader (The Dorsey Brothers and The California Ramblers) (b. 1904)", "html": "1957 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American saxophonist, composer, and bandleader (<a href=\"https://wikipedia.org/wiki/The_Dorsey_Brothers\" title=\"The Dorsey Brothers\">The Dorsey Brothers</a> and <a href=\"https://wikipedia.org/wiki/The_California_Ramblers\" title=\"The California Ramblers\">The California Ramblers</a>) (b. 1904)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American saxophonist, composer, and bandleader (<a href=\"https://wikipedia.org/wiki/The_Dorsey_Brothers\" title=\"The Dorsey Brothers\">The Dorsey Brothers</a> and <a href=\"https://wikipedia.org/wiki/The_California_Ramblers\" title=\"The California Ramblers\">The California Ramblers</a>) (b. 1904)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "The Dorsey Brothers", "link": "https://wikipedia.org/wiki/The_Dorsey_Brothers"}, {"title": "The California Ramblers", "link": "https://wikipedia.org/wiki/The_California_Ramblers"}]}, {"year": "1962", "text": "<PERSON>, English composer and educator (b. 1879)", "html": "1962 - <a href=\"https://wikipedia.org/wiki/John_Ireland_(composer)\" title=\"<PERSON> (composer)\"><PERSON></a>, English composer and educator (b. 1879)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/John_Ireland_(composer)\" title=\"<PERSON> (composer)\"><PERSON></a>, English composer and educator (b. 1879)", "links": [{"title": "<PERSON> (composer)", "link": "https://wikipedia.org/wiki/John_<PERSON>_(composer)"}]}, {"year": "1963", "text": "<PERSON><PERSON><PERSON>, American soldier and activist (b. 1925)", "html": "1963 - <a href=\"https://wikipedia.org/wiki/Me<PERSON><PERSON>_<PERSON><PERSON>\" title=\"Me<PERSON>gar <PERSON><PERSON>\"><PERSON><PERSON><PERSON> <PERSON></a>, American soldier and activist (b. 1925)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Me<PERSON><PERSON>_<PERSON><PERSON>\" title=\"Medgar <PERSON><PERSON>\"><PERSON><PERSON><PERSON> <PERSON></a>, American soldier and activist (b. 1925)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Medgar_<PERSON>s"}]}, {"year": "1966", "text": "<PERSON>, German viola player and conductor (b. 1891)", "html": "1966 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German viola player and conductor (b. 1891)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German viola player and conductor (b. 1891)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1968", "text": "<PERSON>, English poet and critic (b. 1893)", "html": "1968 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English poet and critic (b. 1893)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Read\"><PERSON></a>, English poet and critic (b. 1893)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1969", "text": "<PERSON>, Ukrainian-Russian painter and sculptor (b. 1899)", "html": "1969 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Ukrainian-Russian painter and sculptor (b. 1899)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Ukrainian-Russian painter and sculptor (b. 1899)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1972", "text": "<PERSON>, American critic, essayist, and editor (b. 1895)", "html": "1972 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American critic, essayist, and editor (b. 1895)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American critic, essayist, and editor (b. 1895)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1972", "text": "<PERSON><PERSON><PERSON>, Indian writer and documentary filmmaker (b. 1909)", "html": "1972 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Indian writer and documentary filmmaker (b. 1909)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Indian writer and documentary filmmaker (b. 1909)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "1976", "text": "<PERSON><PERSON><PERSON>, Indian philosopher and scholar (b. 1887)", "html": "1976 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Indian philosopher and scholar (b. 1887)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Indian philosopher and scholar (b. 1887)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1978", "text": "<PERSON>, Chinese historian, author, and poet (b. 1892)", "html": "1978 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Chinese historian, author, and poet (b. 1892)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Chinese historian, author, and poet (b. 1892)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1978", "text": "<PERSON>, Estonian footballer (b. 1912)", "html": "1978 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Estonian footballer (b. 1912)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Estonian footballer (b. 1912)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1980", "text": "<PERSON>, South African-English businessman, founded the Butlins Company (b. 1899)", "html": "1980 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, South African-English businessman, founded the <a href=\"https://wikipedia.org/wiki/<PERSON>lins\" class=\"mw-redirect\" title=\"Butlins\">Butlins Company</a> (b. 1899)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, South African-English businessman, founded the <a href=\"https://wikipedia.org/wiki/<PERSON>lins\" class=\"mw-redirect\" title=\"Butlins\">Butlins Company</a> (b. 1899)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>"}]}, {"year": "1980", "text": "<PERSON><PERSON><PERSON>, Japanese politician, 68th Prime minister of Japan (b. 1910)", "html": "1980 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_%C5%8Chira\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese politician, 68th <a href=\"https://wikipedia.org/wiki/Prime_minister_of_Japan\" class=\"mw-redirect\" title=\"Prime minister of Japan\">Prime minister of Japan</a> (b. 1910)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_%C5%8Chira\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese politician, 68th <a href=\"https://wikipedia.org/wiki/Prime_minister_of_Japan\" class=\"mw-redirect\" title=\"Prime minister of Japan\">Prime minister of Japan</a> (b. 1910)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Ma<PERSON><PERSON>_%C5%8Chira"}, {"title": "Prime minister of Japan", "link": "https://wikipedia.org/wiki/Prime_minister_of_Japan"}]}, {"year": "1980", "text": "<PERSON><PERSON><PERSON>, American actor (b. 1904)", "html": "1980 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON> Stone\"><PERSON><PERSON><PERSON></a>, American actor (b. 1904)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON> Stone\"><PERSON><PERSON><PERSON></a>, American actor (b. 1904)", "links": [{"title": "<PERSON><PERSON><PERSON> Stone", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1982", "text": "<PERSON>, English sergeant, Victoria Cross recipient (b. 1953)", "html": "1982 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English sergeant, <a href=\"https://wikipedia.org/wiki/Victoria_Cross\" title=\"Victoria Cross\">Victoria Cross</a> recipient (b. 1953)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English sergeant, <a href=\"https://wikipedia.org/wiki/Victoria_Cross\" title=\"Victoria Cross\">Victoria Cross</a> recipient (b. 1953)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Victoria Cross", "link": "https://wikipedia.org/wiki/Victoria_Cross"}]}, {"year": "1982", "text": "<PERSON>, Austrian-German ethologist and academic, Nobel Prize laureate (b. 1886)", "html": "1982 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Austrian-German ethologist and academic, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Physiology_or_Medicine\" title=\"Nobel Prize in Physiology or Medicine\">Nobel Prize</a> laureate (b. 1886)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Austrian-German ethologist and academic, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Physiology_or_Medicine\" title=\"Nobel Prize in Physiology or Medicine\">Nobel Prize</a> laureate (b. 1886)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "Nobel Prize in Physiology or Medicine", "link": "https://wikipedia.org/wiki/Nobel_Prize_in_Physiology_or_Medicine"}]}, {"year": "1983", "text": "<PERSON>, Canadian-American actress (b. 1902)", "html": "1983 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian-American actress (b. 1902)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian-American actress (b. 1902)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1989", "text": "<PERSON>, Australian public servant (b. 1911)", "html": "1989 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(public_servant)\" title=\"<PERSON> (public servant)\"><PERSON></a>, Australian public servant (b. 1911)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(public_servant)\" title=\"<PERSON> (public servant)\"><PERSON></a>, Australian public servant (b. 1911)", "links": [{"title": "<PERSON> (public servant)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(public_servant)"}]}, {"year": "1990", "text": "<PERSON>, Baron <PERSON> of the Maine, English captain and politician, 4th Prime Minister of Northern Ireland (b. 1914)", "html": "1990 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%27<PERSON><PERSON><PERSON>,_Baron_<PERSON>%27N<PERSON><PERSON>_of_the_Maine\" class=\"mw-redirect\" title=\"<PERSON>, Baron <PERSON> of the Maine\"><PERSON>, Baron <PERSON> of the Maine</a>, English captain and politician, 4th <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Northern_Ireland\" title=\"Prime Minister of Northern Ireland\">Prime Minister of Northern Ireland</a> (b. 1914)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%27<PERSON><PERSON><PERSON>,_Baron_<PERSON>%27N<PERSON><PERSON>_of_the_Maine\" class=\"mw-redirect\" title=\"<PERSON>, Baron <PERSON> of the Maine\"><PERSON>, Baron <PERSON> of the Maine</a>, English captain and politician, 4th <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Northern_Ireland\" title=\"Prime Minister of Northern Ireland\">Prime Minister of Northern Ireland</a> (b. 1914)", "links": [{"title": "<PERSON>, Baron <PERSON> of the Maine", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>%27N<PERSON><PERSON>,_<PERSON>_<PERSON>%27Neil<PERSON>_of_the_Maine"}, {"title": "Prime Minister of Northern Ireland", "link": "https://wikipedia.org/wiki/Prime_Minister_of_Northern_Ireland"}]}, {"year": "1994", "text": "<PERSON>, ex-wife of <PERSON><PERSON> <PERSON><PERSON> (b. 1959) and <PERSON>, restaurant employee (b. 1968)", "html": "1994 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, ex-wife of <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> <PERSON><PERSON>\"><PERSON><PERSON> <PERSON><PERSON></a> (b. 1959) and <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, restaurant employee (b. 1968)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, ex-wife of <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> <PERSON><PERSON>\"><PERSON><PERSON> <PERSON><PERSON></a> (b. 1959) and <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, restaurant employee (b. 1968)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "<PERSON><PERSON> <PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1994", "text": "<PERSON><PERSON><PERSON>, Russian-American rabbi and author (b. 1902)", "html": "1994 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>del <PERSON>eerson\"><PERSON><PERSON><PERSON></a>, Russian-American rabbi and author (b. 1902)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>del <PERSON>on\"><PERSON><PERSON><PERSON></a>, Russian-American rabbi and author (b. 1902)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1994", "text": "<PERSON>, Filipino-American labor leader and farmworker (b. 1904)", "html": "1994 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Filipino-American labor leader and farmworker (b. 1904)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Filipino-American labor leader and farmworker (b. 1904)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1995", "text": "<PERSON>, Italian pianist (b. 1920)", "html": "1995 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian pianist (b. 1920)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian pianist (b. 1920)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1995", "text": "<PERSON>, American basketball player (b. 1949)", "html": "1995 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player (b. 1949)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player (b. 1949)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1997", "text": "<PERSON><PERSON><PERSON>, Russian singer-songwriter and guitarist (b. 1924)", "html": "1997 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Russian singer-songwriter and guitarist (b. 1924)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Russian singer-songwriter and guitarist (b. 1924)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1998", "text": "<PERSON>, American author and educator (b. 1924)", "html": "1998 - <a href=\"https://wikipedia.org/wiki/Leo_<PERSON>\" title=\"<PERSON> Bus<PERSON>glia\"><PERSON></a>, American author and educator (b. 1924)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Leo_<PERSON>glia\" title=\"Leo Bus<PERSON>glia\"><PERSON></a>, American author and educator (b. 1924)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Leo_Buscaglia"}]}, {"year": "1998", "text": "<PERSON>, American actress and singer (b. 1922)", "html": "1998 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress and singer (b. 1922)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress and singer (b. 1922)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1999", "text": "<PERSON><PERSON><PERSON>, Iranian numismatist (b. 1910)", "html": "1999 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Iranian numismatist (b. 1910)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Iranian numismatist (b. 1910)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON><PERSON>eh_<PERSON>ani"}]}, {"year": "1999", "text": "<PERSON><PERSON> <PERSON><PERSON>, American novelist and short story writer (b. 1917)", "html": "1999 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> <PERSON><PERSON>\"><PERSON><PERSON> <PERSON><PERSON></a>, American novelist and short story writer (b. 1917)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON>. <PERSON><PERSON>\"><PERSON><PERSON> <PERSON><PERSON></a>, American novelist and short story writer (b. 1917)", "links": [{"title": "<PERSON><PERSON> <PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "2000", "text": "<PERSON><PERSON><PERSON><PERSON>, Indian actor, director, and producer (b. 1919)", "html": "2000 - <a href=\"https://wikipedia.org/wiki/<PERSON>urushottam_Laxman_Deshpande\" title=\"Purushottam Laxman Deshpande\"><PERSON><PERSON><PERSON><PERSON></a>, Indian actor, director, and producer (b. 1919)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_La<PERSON>man_<PERSON>hpande\" title=\"Purushottam Laxman Deshpande\"><PERSON><PERSON><PERSON><PERSON></a>, Indian actor, director, and producer (b. 1919)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON><PERSON>_<PERSON>hpande"}]}, {"year": "2002", "text": "<PERSON>, American fashion designer, founded Bill Blass Limited (b. 1922)", "html": "2002 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American fashion designer, founded <a href=\"https://wikipedia.org/wiki/Bill_B<PERSON>_Limited\" class=\"mw-redirect\" title=\"Bill Blass Limited\">Bill Blass Limited</a> (b. 1922)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American fashion designer, founded <a href=\"https://wikipedia.org/wiki/Bill_B<PERSON>_Limited\" class=\"mw-redirect\" title=\"Bill Blass Limited\">Bill Blass Limited</a> (b. 1922)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "2002", "text": "<PERSON><PERSON>, American reviewer of children's literature (b. 1915)", "html": "2002 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American reviewer of children's literature (b. 1915)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American reviewer of children's literature (b. 1915)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "2003", "text": "<PERSON>, American actor and political activist (b. 1916)", "html": "2003 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor and political activist (b. 1916)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor and political activist (b. 1916)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2005", "text": "<PERSON>, Canadian journalist and author (b. 1918)", "html": "2005 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(writer)\" title=\"<PERSON> (writer)\"><PERSON></a>, Canadian journalist and author (b. 1918)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(writer)\" title=\"<PERSON> (writer)\"><PERSON></a>, Canadian journalist and author (b. 1918)", "links": [{"title": "<PERSON> (writer)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(writer)"}]}, {"year": "2006", "text": "<PERSON>, Australian rugby player and fighter pilot (b. 1915)", "html": "2006 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian rugby player and fighter pilot (b. 1915)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian rugby player and fighter pilot (b. 1915)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2006", "text": "<PERSON><PERSON><PERSON><PERSON>, Romanian-Hungarian composer and educator (b. 1923)", "html": "2006 - <a href=\"https://wikipedia.org/wiki/Gy%C3%B6rgy_<PERSON>\" title=\"<PERSON>yörgy Ligeti\"><PERSON><PERSON><PERSON><PERSON></a>, Romanian-Hungarian composer and educator (b. 1923)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Gy%C3%B6<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Romanian-Hungarian composer and educator (b. 1923)", "links": [{"title": "György <PERSON>", "link": "https://wikipedia.org/wiki/Gy%C3%B6rgy_Ligeti"}]}, {"year": "2006", "text": "<PERSON>, 2nd Baron <PERSON> of Fleet, Canadian businessman and art collector (b. 1923)", "html": "2006 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_2nd_Baron_<PERSON>_of_Fleet\" title=\"<PERSON>, 2nd Baron <PERSON> of Fleet\"><PERSON>, 2nd Baron <PERSON> of Fleet</a>, Canadian businessman and art collector (b. 1923)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>,_2nd_Baron_<PERSON>_of_Fleet\" title=\"<PERSON>, 2nd Baron <PERSON> of Fleet\"><PERSON>, 2nd Baron <PERSON> of Fleet</a>, Canadian businessman and art collector (b. 1923)", "links": [{"title": "<PERSON>, 2nd Baron <PERSON> of Fleet", "link": "https://wikipedia.org/wiki/<PERSON>,_2nd_Baron_<PERSON>_of_Fleet"}]}, {"year": "2008", "text": "<PERSON><PERSON><PERSON>, Czech ice hockey player (b. 1951)", "html": "2008 - <a href=\"https://wikipedia.org/wiki/Miroslav_Dvo%C5%99%C3%A1k_(ice_hockey)\" title=\"<PERSON><PERSON><PERSON> (ice hockey)\"><PERSON><PERSON><PERSON></a>, Czech ice hockey player (b. 1951)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Miroslav_Dvo%C5%99%C3%A1k_(ice_hockey)\" title=\"<PERSON><PERSON><PERSON> (ice hockey)\"><PERSON><PERSON><PERSON></a>, Czech ice hockey player (b. 1951)", "links": [{"title": "<PERSON><PERSON><PERSON> (ice hockey)", "link": "https://wikipedia.org/wiki/Miroslav_Dvo%C5%99%C3%A1k_(ice_hockey)"}]}, {"year": "2008", "text": "<PERSON>, Welsh footballer and manager (b. 1932)", "html": "2008 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Welsh footballer and manager (b. 1932)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Welsh footballer and manager (b. 1932)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2010", "text": "<PERSON>, American illustrator (b. 1931)", "html": "2010 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>\" title=\"<PERSON>\"><PERSON></a>, American illustrator (b. 1931)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>\" title=\"<PERSON>\"><PERSON></a>, American illustrator (b. 1931)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_Williamson"}]}, {"year": "2011", "text": "<PERSON>, Canadian bishop (b. 1920)", "html": "2011 - <a href=\"https://wikipedia.org/wiki/Ren%C3%A9_Au<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian bishop (b. 1920)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Ren%C3%A9_Au<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian bishop (b. 1920)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Ren%C3%A9_Audet"}]}, {"year": "2012", "text": "<PERSON>, Argentinian-French journalist and author (b. 1930)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Argentinian-French journalist and author (b. 1930)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Argentinian-French journalist and author (b. 1930)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2012", "text": "<PERSON><PERSON>, Danish-German psychoanalyst and author (b. 1917)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>-<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Danish-German psychoanalyst and author (b. 1917)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>-<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Danish-German psychoanalyst and author (b. 1917)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>-<PERSON>"}]}, {"year": "2012", "text": "<PERSON><PERSON>, Albanian footballer and manager (b. 1946)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/Me<PERSON>_<PERSON>\" title=\"Medin <PERSON>\"><PERSON><PERSON></a>, Albanian footballer and manager (b. 1946)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Me<PERSON>_<PERSON>\" title=\"Me<PERSON>\"><PERSON><PERSON></a>, Albanian footballer and manager (b. 1946)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Medin_<PERSON>ga"}]}, {"year": "2012", "text": "<PERSON><PERSON>, American  political scientist and economist, Nobel Prize laureate (b. 1933)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American political scientist and economist, <a href=\"https://wikipedia.org/wiki/Nobel_Memorial_Prize_in_Economic_Sciences\" title=\"Nobel Memorial Prize in Economic Sciences\">Nobel Prize</a> laureate (b. 1933)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American political scientist and economist, <a href=\"https://wikipedia.org/wiki/Nobel_Memorial_Prize_in_Economic_Sciences\" title=\"Nobel Memorial Prize in Economic Sciences\">Nobel Prize</a> laureate (b. 1933)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}, {"title": "Nobel Memorial Prize in Economic Sciences", "link": "https://wikipedia.org/wiki/Nobel_Memorial_Prize_in_Economic_Sciences"}]}, {"year": "2012", "text": "<PERSON><PERSON><PERSON>, Spanish footballer (b. 1923)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/Pahi%C3%B1o\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Spanish footballer (b. 1923)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Pahi%C3%B1o\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Spanish footballer (b. 1923)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Pahi%C3%B1o"}]}, {"year": "2012", "text": "<PERSON>, Australian judge and politician, 41st Attorney General of New South Wales (b. 1942)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/<PERSON>(Australian_politician)\" title=\"<PERSON> (Australian politician)\"><PERSON></a>, Australian judge and politician, 41st <a href=\"https://wikipedia.org/wiki/Attorney_General_of_New_South_Wales\" title=\"Attorney General of New South Wales\">Attorney General of New South Wales</a> (b. 1942)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>(Australian_politician)\" title=\"<PERSON> (Australian politician)\"><PERSON></a>, Australian judge and politician, 41st <a href=\"https://wikipedia.org/wiki/Attorney_General_of_New_South_Wales\" title=\"Attorney General of New South Wales\">Attorney General of New South Wales</a> (b. 1942)", "links": [{"title": "<PERSON> (Australian politician)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(Australian_politician)"}, {"title": "Attorney General of New South Wales", "link": "https://wikipedia.org/wiki/Attorney_General_of_New_South_Wales"}]}, {"year": "2013", "text": "<PERSON><PERSON><PERSON>, Spanish nun (b. 1908)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/Te<PERSON>ita_Barajuen\" title=\"Teresita Barajuen\"><PERSON><PERSON><PERSON></a>, Spanish nun (b. 1908)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Te<PERSON>ita_Barajuen\" title=\"Teresita Barajuen\"><PERSON><PERSON><PERSON></a>, Spanish nun (b. 1908)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>ajuen"}]}, {"year": "2013", "text": "<PERSON>, American racing driver (b. 1975)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American racing driver (b. 1975)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American racing driver (b. 1975)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2015", "text": "<PERSON>, Brazilian journalist, poet, and composer (b. 1946)", "html": "2015 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Brazilian journalist, poet, and composer (b. 1946)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Brazilian journalist, poet, and composer (b. 1946)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2018", "text": "<PERSON>, English drummer (b. 1944)", "html": "2018 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English drummer (b. 1944)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English drummer (b. 1944)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "2019", "text": "<PERSON>, American actress (b. 1924)", "html": "2019 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress (b. 1924)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress (b. 1924)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2022", "text": "<PERSON>, American actor (b. 1931)", "html": "2022 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"Philip <PERSON> Hall\"><PERSON></a>, American actor (b. 1931)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"Philip <PERSON> Hall\"><PERSON></a>, American actor (b. 1931)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "2022", "text": "<PERSON>, Welsh rugby union player (b. 1948) ", "html": "2022 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Welsh rugby union player (b. 1948) ", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Welsh rugby union player (b. 1948) ", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2023", "text": "<PERSON><PERSON><PERSON>, Italian businessman and politician, Prime Minister of Italy (b. 1936)", "html": "2023 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Italian businessman and politician, <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Italy\" title=\"Prime Minister of Italy\">Prime Minister of Italy</a> (b. 1936)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Italian businessman and politician, <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Italy\" title=\"Prime Minister of Italy\">Prime Minister of Italy</a> (b. 1936)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}, {"title": "Prime Minister of Italy", "link": "https://wikipedia.org/wiki/Prime_Minister_of_Italy"}]}, {"year": "2023", "text": "<PERSON>, Italian actor and director, (b. 1955)", "html": "2023 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian actor and director, (b. 1955)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian actor and director, (b. 1955)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2023", "text": "<PERSON>, comic book artist and author (b. 1930)", "html": "2023 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON>.</a>, comic book artist and author (b. 1930)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON>.</a>, comic book artist and author (b. 1930)", "links": [{"title": "<PERSON>.", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>."}]}, {"year": "2023", "text": "<PERSON><PERSON><PERSON>, American actor (b. 1951)", "html": "2023 - <a href=\"https://wikipedia.org/wiki/<PERSON>rea<PERSON>_<PERSON>\" title=\"Trea<PERSON>\"><PERSON><PERSON><PERSON></a>, American actor (b. 1951)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>rea<PERSON>_<PERSON>\" title=\"Treat <PERSON>\"><PERSON><PERSON><PERSON></a>, American actor (b. 1951)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Treat_<PERSON>"}]}, {"year": "2024", "text": "<PERSON>, American businessman (b. 1931)", "html": "2024 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American businessman (b. 1931)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American businessman (b. 1931)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "2024", "text": "<PERSON>, American lawyer and politician, 33rd Governor of Oregon (b. 1940)", "html": "2024 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and politician, 33rd <a href=\"https://wikipedia.org/wiki/Governor_of_Oregon\" title=\"Governor of Oregon\">Governor of Oregon</a> (b. 1940)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and politician, 33rd <a href=\"https://wikipedia.org/wiki/Governor_of_Oregon\" title=\"Governor of Oregon\">Governor of Oregon</a> (b. 1940)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}, {"title": "Governor of Oregon", "link": "https://wikipedia.org/wiki/Governor_of_Oregon"}]}, {"year": "2024", "text": "<PERSON>, American basketball player and executive (b. 1938)", "html": "2024 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player and executive (b. 1938)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Jerry West\"><PERSON></a>, American basketball player and executive (b. 1938)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}]}}