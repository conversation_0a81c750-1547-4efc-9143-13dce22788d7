{"date": "November 15", "url": "https://wikipedia.org/wiki/November_15", "data": {"Events": [{"year": "655", "text": "Battle of the Winwaed: <PERSON><PERSON> of Mercia is defeated by <PERSON><PERSON><PERSON><PERSON> of Northumbria.", "html": "655 - <a href=\"https://wikipedia.org/wiki/Battle_of_the_Winwaed\" title=\"Battle of the Winwaed\">Battle of the Winwaed</a>: <a href=\"https://wikipedia.org/wiki/Penda_of_Mercia\" title=\"Penda of Mercia\"><PERSON><PERSON> of Mercia</a> is defeated by <a href=\"https://wikipedia.org/wiki/Oswiu_of_Northumbria\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON><PERSON> of Northumbria\"><PERSON><PERSON><PERSON><PERSON> of Northumbria</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Battle_of_the_Winwaed\" title=\"Battle of the Winwaed\">Battle of the Winwaed</a>: <a href=\"https://wikipedia.org/wiki/Penda_of_Mercia\" title=\"Pen<PERSON> of Mercia\"><PERSON><PERSON> of Mercia</a> is defeated by <a href=\"https://wikipedia.org/wiki/Oswiu_of_Northumbria\" class=\"mw-redirect\" title=\"<PERSON>s<PERSON><PERSON> of Northumbria\"><PERSON><PERSON><PERSON><PERSON> of Northumbria</a>.", "links": [{"title": "Battle of the Winwaed", "link": "https://wikipedia.org/wiki/Battle_of_the_Winwaed"}, {"title": "Penda of Mercia", "link": "https://wikipedia.org/wiki/Penda_of_Mercia"}, {"title": "<PERSON><PERSON><PERSON><PERSON> of Northumbria", "link": "https://wikipedia.org/wiki/Oswiu_of_Northumbria"}]}, {"year": "1315", "text": "Growth of the Old Swiss Confederacy: The Schweizer Eidgenossenschaft ambushes the army of Leopold I in the Battle of Morgarten.", "html": "1315 - <a href=\"https://wikipedia.org/wiki/Growth_of_the_Old_Swiss_Confederacy\" title=\"Growth of the Old Swiss Confederacy\">Growth of the Old Swiss Confederacy</a>: The Schweizer <a href=\"https://wikipedia.org/wiki/Eidgenossenschaft\" title=\"Eidgenossenschaft\">Eidgenossenschaft</a> ambushes the army of <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Duke_of_Austria\" title=\"<PERSON>, Duke of Austria\"><PERSON></a> in the <a href=\"https://wikipedia.org/wiki/Battle_of_Morgarten\" title=\"Battle of Morgarten\">Battle of Morgarten</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Growth_of_the_Old_Swiss_Confederacy\" title=\"Growth of the Old Swiss Confederacy\">Growth of the Old Swiss Confederacy</a>: The Schweizer <a href=\"https://wikipedia.org/wiki/Eidgenossenschaft\" title=\"Eidgenossenschaft\">Eidgenossenschaft</a> ambushes the army of <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Duke_of_Austria\" title=\"<PERSON>, Duke of Austria\"><PERSON></a> in the <a href=\"https://wikipedia.org/wiki/Battle_of_Morgarten\" title=\"Battle of Morgarten\">Battle of Morgarten</a>.", "links": [{"title": "Growth of the Old Swiss Confederacy", "link": "https://wikipedia.org/wiki/Growth_of_the_Old_Swiss_Confederacy"}, {"title": "Eidgenossenschaft", "link": "https://wikipedia.org/wiki/Eidgenossenschaft"}, {"title": "<PERSON>, Duke of Austria", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Duke_of_Austria"}, {"title": "Battle of Morgarten", "link": "https://wikipedia.org/wiki/Battle_of_Morgarten"}]}, {"year": "1532", "text": "Spanish conquest of the Inca Empire: Commanded by <PERSON>, Spanish conquistadors under <PERSON><PERSON><PERSON> meet Incan Emperor <PERSON><PERSON><PERSON><PERSON> for the first time outside Cajamarca, arranging for a meeting in the city plaza the following day.", "html": "1532 - <a href=\"https://wikipedia.org/wiki/Spanish_conquest_of_the_Inca_Empire\" title=\"Spanish conquest of the Inca Empire\">Spanish conquest of the Inca Empire</a>: Commanded by <a href=\"https://wikipedia.org/wiki/Francisco_Pizarro\" title=\"Francisco Pizarro\"><PERSON></a>, Spanish <a href=\"https://wikipedia.org/wiki/Conquistador\" title=\"Conquistador\">conquistadors</a> under <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a> meet <a href=\"https://wikipedia.org/wiki/Sapa_Inca\" title=\"Sapa Inca\">Incan Emperor</a> <a href=\"https://wikipedia.org/wiki/Atahualpa\" title=\"Atahualpa\">Atahualpa</a> for the first time outside <a href=\"https://wikipedia.org/wiki/Cajamarca\" title=\"Cajamarca\">Cajamarca</a>, arranging for a meeting in the <a href=\"https://wikipedia.org/wiki/Battle_of_Cajamarca\" title=\"Battle of Cajamarca\">city plaza the following day</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Spanish_conquest_of_the_Inca_Empire\" title=\"Spanish conquest of the Inca Empire\">Spanish conquest of the Inca Empire</a>: Commanded by <a href=\"https://wikipedia.org/wiki/Francisco_Pi<PERSON>ro\" title=\"Francisco <PERSON>\"><PERSON></a>, Spanish <a href=\"https://wikipedia.org/wiki/Conquistador\" title=\"Conquistador\">conquistadors</a> under <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a> meet <a href=\"https://wikipedia.org/wiki/Sapa_Inca\" title=\"Sapa Inca\">Incan Emperor</a> <a href=\"https://wikipedia.org/wiki/Atahualpa\" title=\"Atahualpa\">Atahualpa</a> for the first time outside <a href=\"https://wikipedia.org/wiki/Cajamarca\" title=\"Cajamarca\">Cajamarca</a>, arranging for a meeting in the <a href=\"https://wikipedia.org/wiki/Battle_of_Cajamarca\" title=\"Battle of Cajamarca\">city plaza the following day</a>.", "links": [{"title": "Spanish conquest of the Inca Empire", "link": "https://wikipedia.org/wiki/Spanish_conquest_of_the_Inca_Empire"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Francisco_<PERSON>"}, {"title": "Conquistador", "link": "https://wikipedia.org/wiki/Conquistador"}, {"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>"}, {"title": "Sapa Inca", "link": "https://wikipedia.org/wiki/Sapa_Inca"}, {"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Atahualpa"}, {"title": "Cajamarca", "link": "https://wikipedia.org/wiki/Cajamarca"}, {"title": "Battle of Cajamarca", "link": "https://wikipedia.org/wiki/Battle_of_Cajamarca"}]}, {"year": "1533", "text": "<PERSON> arrives in Cuzco, the capital of the Inca Empire.", "html": "1533 - <a href=\"https://wikipedia.org/wiki/Francisco_Pizarro\" title=\"Francisco Pizarro\"><PERSON></a> arrives in <a href=\"https://wikipedia.org/wiki/Cusco\" title=\"Cusco\">Cuzco</a>, the capital of the <a href=\"https://wikipedia.org/wiki/Inca_Empire\" title=\"Inca Empire\">Inca Empire</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Francisco_Pi<PERSON>ro\" title=\"Francisco Pizarro\"><PERSON></a> arrives in <a href=\"https://wikipedia.org/wiki/Cusco\" title=\"Cusco\">Cuzco</a>, the capital of the <a href=\"https://wikipedia.org/wiki/Inca_Empire\" title=\"Inca Empire\">Inca Empire</a>.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Francisco_<PERSON>"}, {"title": "Cusco", "link": "https://wikipedia.org/wiki/Cusco"}, {"title": "Inca Empire", "link": "https://wikipedia.org/wiki/Inca_Empire"}]}, {"year": "1705", "text": "<PERSON><PERSON><PERSON><PERSON><PERSON>'s War of Independence: The Habsburg Empire and Denmark win a military victory over the Kurucs from Hungary in the Battle of Zsibó.", "html": "1705 - <a href=\"https://wikipedia.org/wiki/R%C3%A1k%C3%B3czi%27s_War_of_Independence\" title=\"<PERSON><PERSON><PERSON><PERSON><PERSON>'s War of Independence\"><PERSON><PERSON><PERSON><PERSON><PERSON>'s War of Independence</a>: The Habsburg Empire and Denmark win a military victory over the <a href=\"https://wikipedia.org/wiki/Kuruc\" title=\"Kuruc\"><PERSON><PERSON><PERSON></a> from <a href=\"https://wikipedia.org/wiki/Hungary\" title=\"Hungary\">Hungary</a> in the <a href=\"https://wikipedia.org/wiki/Battle_of_Zsib%C3%B3\" title=\"Battle of Zsibó\">Battle of Zsibó</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/R%C3%A1k%C3%B3czi%27s_War_of_Independence\" title=\"<PERSON><PERSON><PERSON><PERSON><PERSON>'s War of Independence\"><PERSON><PERSON><PERSON><PERSON><PERSON>'s War of Independence</a>: The Habsburg Empire and Denmark win a military victory over the <a href=\"https://wikipedia.org/wiki/Kuruc\" title=\"Kuruc\"><PERSON><PERSON><PERSON></a> from <a href=\"https://wikipedia.org/wiki/Hungary\" title=\"Hungary\">Hungary</a> in the <a href=\"https://wikipedia.org/wiki/Battle_of_Zsib%C3%B3\" title=\"Battle of Zsibó\">Battle of Zsibó</a>.", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON><PERSON>'s War of Independence", "link": "https://wikipedia.org/wiki/R%C3%A1k%C3%B3czi%27s_War_of_Independence"}, {"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>ruc"}, {"title": "Hungary", "link": "https://wikipedia.org/wiki/Hungary"}, {"title": "Battle of Zsibó", "link": "https://wikipedia.org/wiki/Battle_of_Zsib%C3%B3"}]}, {"year": "1760", "text": "The secondly-built Castellania in Valletta is officially inaugurated with the blessing of the interior Chapel of Sorrows.", "html": "1760 - The secondly-built <a href=\"https://wikipedia.org/wiki/Castellania_(Valletta)\" title=\"Castellania (Valletta)\">Castellania</a> in <a href=\"https://wikipedia.org/wiki/Valletta\" title=\"Valletta\">Valletta</a> is officially inaugurated with the blessing of the interior Chapel of Sorrows.", "no_year_html": "The secondly-built <a href=\"https://wikipedia.org/wiki/Castellania_(Valletta)\" title=\"Castellania (Valletta)\">Castellania</a> in <a href=\"https://wikipedia.org/wiki/Valletta\" title=\"Valletta\">Valletta</a> is officially inaugurated with the blessing of the interior Chapel of Sorrows.", "links": [{"title": "Castellania (Valletta)", "link": "https://wikipedia.org/wiki/Castellania_(Valletta)"}, {"title": "Valletta", "link": "https://wikipedia.org/wiki/Valletta"}]}, {"year": "1777", "text": "American Revolutionary War: After 16 months of debate the Continental Congress approves the Articles of Confederation.", "html": "1777 - <a href=\"https://wikipedia.org/wiki/American_Revolutionary_War\" title=\"American Revolutionary War\">American Revolutionary War</a>: After 16 months of debate the <a href=\"https://wikipedia.org/wiki/Continental_Congress\" title=\"Continental Congress\">Continental Congress</a> approves the <a href=\"https://wikipedia.org/wiki/Articles_of_Confederation\" title=\"Articles of Confederation\">Articles of Confederation</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/American_Revolutionary_War\" title=\"American Revolutionary War\">American Revolutionary War</a>: After 16 months of debate the <a href=\"https://wikipedia.org/wiki/Continental_Congress\" title=\"Continental Congress\">Continental Congress</a> approves the <a href=\"https://wikipedia.org/wiki/Articles_of_Confederation\" title=\"Articles of Confederation\">Articles of Confederation</a>.", "links": [{"title": "American Revolutionary War", "link": "https://wikipedia.org/wiki/American_Revolutionary_War"}, {"title": "Continental Congress", "link": "https://wikipedia.org/wiki/Continental_Congress"}, {"title": "Articles of Confederation", "link": "https://wikipedia.org/wiki/Articles_of_Confederation"}]}, {"year": "1806", "text": "Pike Expedition: Lieutenant <PERSON><PERSON><PERSON> spots a mountain peak while near the Colorado foothills of the Rocky Mountains. It is later named <PERSON><PERSON> Peak in his honor.", "html": "1806 - <a href=\"https://wikipedia.org/wiki/Pike_Expedition\" title=\"Pike Expedition\">Pike Expedition</a>: Lieutenant <a href=\"https://wikipedia.org/wiki/Zebulon_Pike\" title=\"Zebulon Pike\">Zebulon Pike</a> spots a mountain peak while near the <a href=\"https://wikipedia.org/wiki/Colorado\" title=\"Colorado\">Colorado</a> foothills of the <a href=\"https://wikipedia.org/wiki/Rocky_Mountains\" title=\"Rocky Mountains\">Rocky Mountains</a>. It is later named <a href=\"https://wikipedia.org/wiki/Pikes_Peak\" title=\"Pikes Peak\">Pikes Peak</a> in his honor.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Pike_Expedition\" title=\"Pike Expedition\">Pike Expedition</a>: Lieutenant <a href=\"https://wikipedia.org/wiki/Zebulon_Pike\" title=\"Zebulon Pike\">Zebulon Pike</a> spots a mountain peak while near the <a href=\"https://wikipedia.org/wiki/Colorado\" title=\"Colorado\">Colorado</a> foothills of the <a href=\"https://wikipedia.org/wiki/Rocky_Mountains\" title=\"Rocky Mountains\">Rocky Mountains</a>. It is later named <a href=\"https://wikipedia.org/wiki/Pikes_Peak\" title=\"Pikes Peak\">Pikes Peak</a> in his honor.", "links": [{"title": "Pike Expedition", "link": "https://wikipedia.org/wiki/Pike_Expedition"}, {"title": "Zebulon Pike", "link": "https://wikipedia.org/wiki/Zebulon_Pike"}, {"title": "Colorado", "link": "https://wikipedia.org/wiki/Colorado"}, {"title": "Rocky Mountains", "link": "https://wikipedia.org/wiki/Rocky_Mountains"}, {"title": "Pikes Peak", "link": "https://wikipedia.org/wiki/Pikes_Peak"}]}, {"year": "1842", "text": "A slave revolt in the Cherokee Nation commences.", "html": "1842 - A <a href=\"https://wikipedia.org/wiki/1842_Slave_Revolt_in_the_Cherokee_Nation\" title=\"1842 Slave Revolt in the Cherokee Nation\">slave revolt in the Cherokee Nation</a> commences.", "no_year_html": "A <a href=\"https://wikipedia.org/wiki/1842_Slave_Revolt_in_the_Cherokee_Nation\" title=\"1842 Slave Revolt in the Cherokee Nation\">slave revolt in the Cherokee Nation</a> commences.", "links": [{"title": "1842 Slave Revolt in the Cherokee Nation", "link": "https://wikipedia.org/wiki/1842_Slave_Revolt_in_the_Cherokee_Nation"}]}, {"year": "1849", "text": "Boilers of the steamboat Louisiana explode as she pulls back from the dock in New Orleans, killing more than 150 people.", "html": "1849 - <a href=\"https://wikipedia.org/wiki/Boiler_explosion\" title=\"Boiler explosion\">Boilers</a> of the steamboat <a href=\"https://wikipedia.org/wiki/Louisiana_(steamboat)\" title=\"Louisiana (steamboat)\"><i>Louisiana</i></a> explode as she pulls back from the dock in <a href=\"https://wikipedia.org/wiki/New_Orleans\" title=\"New Orleans\">New Orleans</a>, killing more than 150 people.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Boiler_explosion\" title=\"Boiler explosion\">Boilers</a> of the steamboat <a href=\"https://wikipedia.org/wiki/Louisiana_(steamboat)\" title=\"Louisiana (steamboat)\"><i>Louisiana</i></a> explode as she pulls back from the dock in <a href=\"https://wikipedia.org/wiki/New_Orleans\" title=\"New Orleans\">New Orleans</a>, killing more than 150 people.", "links": [{"title": "Boiler explosion", "link": "https://wikipedia.org/wiki/Boiler_explosion"}, {"title": "Louisiana (steamboat)", "link": "https://wikipedia.org/wiki/Louisiana_(steamboat)"}, {"title": "New Orleans", "link": "https://wikipedia.org/wiki/New_Orleans"}]}, {"year": "1864", "text": "American Civil War: Union General <PERSON> begins his March to the Sea.", "html": "1864 - <a href=\"https://wikipedia.org/wiki/American_Civil_War\" title=\"American Civil War\">American Civil War</a>: Union General <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> begins his <a href=\"https://wikipedia.org/wiki/Sherman%27s_March_to_the_Sea\" title=\"<PERSON>'s March to the Sea\">March to the Sea</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/American_Civil_War\" title=\"American Civil War\">American Civil War</a>: Union General <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> begins his <a href=\"https://wikipedia.org/wiki/Sherman%27s_March_to_the_Sea\" title=\"<PERSON>'s March to the Sea\">March to the Sea</a>.", "links": [{"title": "American Civil War", "link": "https://wikipedia.org/wiki/American_Civil_War"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "<PERSON>'s March to the Sea", "link": "https://wikipedia.org/wiki/Sherman%27s_March_to_the_Sea"}]}, {"year": "1884", "text": "The Berlin Conference of 1884-1885 met on 15 November 1884, and after an adjournment concluded on 26 February 1885, with the signature of a General Act, regulating the European colonisation and trade in Africa during the New Imperialism period. ", "html": "1884 - The <a href=\"https://wikipedia.org/wiki/Berlin_Conference\" title=\"Berlin Conference\">Berlin Conference</a> of 1884-1885 met on 15 November 1884, and after an adjournment concluded on 26 February 1885, with the signature of a General Act, regulating the European colonisation and trade in Africa during the New Imperialism period. ", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Berlin_Conference\" title=\"Berlin Conference\">Berlin Conference</a> of 1884-1885 met on 15 November 1884, and after an adjournment concluded on 26 February 1885, with the signature of a General Act, regulating the European colonisation and trade in Africa during the New Imperialism period. ", "links": [{"title": "Berlin Conference", "link": "https://wikipedia.org/wiki/Berlin_Conference"}]}, {"year": "1889", "text": "Brazil is declared a republic by Marshal <PERSON><PERSON><PERSON> as Emperor <PERSON> is deposed in a military coup.", "html": "1889 - <a href=\"https://wikipedia.org/wiki/Brazil\" title=\"Brazil\">Brazil</a> is declared a republic by <a href=\"https://wikipedia.org/wiki/Marshal_(Brazil)\" title=\"Marshal (Brazil)\">Marshal</a> <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_F<PERSON>\" title=\"<PERSON><PERSON><PERSON> da Fonseca\"><PERSON><PERSON><PERSON></a> as <a href=\"https://wikipedia.org/wiki/Emperor_of_Brazil\" class=\"mw-redirect\" title=\"Emperor of Brazil\">Emperor</a> <a href=\"https://wikipedia.org/wiki/Pedro_II_of_Brazil\" title=\"Pedro II of Brazil\">Pedro II</a> is <a href=\"https://wikipedia.org/wiki/Decline_and_fall_of_Pedro_II_of_Brazil\" title=\"Decline and fall of <PERSON> II of Brazil\">deposed in a military coup</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Brazil\" title=\"Brazil\">Brazil</a> is declared a republic by <a href=\"https://wikipedia.org/wiki/Marshal_(Brazil)\" title=\"Marshal (Brazil)\">Marshal</a> <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON> da Fonseca\"><PERSON><PERSON><PERSON></a> as <a href=\"https://wikipedia.org/wiki/Emperor_of_Brazil\" class=\"mw-redirect\" title=\"Emperor of Brazil\">Emperor</a> <a href=\"https://wikipedia.org/wiki/Pedro_II_of_Brazil\" title=\"Pedro II of Brazil\"><PERSON> II</a> is <a href=\"https://wikipedia.org/wiki/Decline_and_fall_of_Pedro_II_of_Brazil\" title=\"Decline and fall of <PERSON> II of Brazil\">deposed in a military coup</a>.", "links": [{"title": "Brazil", "link": "https://wikipedia.org/wiki/Brazil"}, {"title": "Marshal (Brazil)", "link": "https://wikipedia.org/wiki/Marshal_(Brazil)"}, {"title": "<PERSON><PERSON><PERSON>eca", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>eca"}, {"title": "Emperor of Brazil", "link": "https://wikipedia.org/wiki/Emperor_of_Brazil"}, {"title": "Pedro II of Brazil", "link": "https://wikipedia.org/wiki/Pedro_II_of_Brazil"}, {"title": "Decline and fall of Pedro II of Brazil", "link": "https://wikipedia.org/wiki/Decline_and_fall_of_Pedro_II_of_Brazil"}]}, {"year": "1899", "text": "Second Boer War: Battle of Chieveley, a British armored train is ambushed and partially derailed. British lose the battle, with 80 soldiers captured, along with war correspondent <PERSON>. ", "html": "1899 - <a href=\"https://wikipedia.org/wiki/Second_Boer_War\" title=\"Second Boer War\">Second Boer War</a>: <a href=\"https://wikipedia.org/wiki/Battle_of_Chieveley\" title=\"Battle of Chieveley\">Battle of Chieveley</a>, a British armored train is ambushed and partially derailed. British lose the battle, with 80 soldiers captured, along with war correspondent <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>. ", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Second_Boer_War\" title=\"Second Boer War\">Second Boer War</a>: <a href=\"https://wikipedia.org/wiki/Battle_of_Chieveley\" title=\"Battle of Chieveley\">Battle of Chieveley</a>, a British armored train is ambushed and partially derailed. British lose the battle, with 80 soldiers captured, along with war correspondent <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>. ", "links": [{"title": "Second Boer War", "link": "https://wikipedia.org/wiki/Second_Boer_War"}, {"title": "Battle of Chieveley", "link": "https://wikipedia.org/wiki/Battle_of_Chieveley"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>"}]}, {"year": "1917", "text": "Eduskunta declares itself the supreme state power of Finland, prompting its declaration of independence and secession from Russia.", "html": "1917 - <i><a href=\"https://wikipedia.org/wiki/Parliament_of_Finland\" title=\"Parliament of Finland\">Eduskunta</a></i> declares itself the supreme state power of <a href=\"https://wikipedia.org/wiki/Finland\" title=\"Finland\">Finland</a>, prompting its <a href=\"https://wikipedia.org/wiki/Independence_of_Finland\" title=\"Independence of Finland\">declaration of independence</a> and secession from <a href=\"https://wikipedia.org/wiki/Soviet_Russia\" class=\"mw-redirect\" title=\"Soviet Russia\">Russia</a>.", "no_year_html": "<i><a href=\"https://wikipedia.org/wiki/Parliament_of_Finland\" title=\"Parliament of Finland\">Eduskunta</a></i> declares itself the supreme state power of <a href=\"https://wikipedia.org/wiki/Finland\" title=\"Finland\">Finland</a>, prompting its <a href=\"https://wikipedia.org/wiki/Independence_of_Finland\" title=\"Independence of Finland\">declaration of independence</a> and secession from <a href=\"https://wikipedia.org/wiki/Soviet_Russia\" class=\"mw-redirect\" title=\"Soviet Russia\">Russia</a>.", "links": [{"title": "Parliament of Finland", "link": "https://wikipedia.org/wiki/Parliament_of_Finland"}, {"title": "Finland", "link": "https://wikipedia.org/wiki/Finland"}, {"title": "Independence of Finland", "link": "https://wikipedia.org/wiki/Independence_of_Finland"}, {"title": "Soviet Russia", "link": "https://wikipedia.org/wiki/Soviet_Russia"}]}, {"year": "1920", "text": "The first assembly of the League of Nations is held in Geneva, Switzerland.", "html": "1920 - The first assembly of the <a href=\"https://wikipedia.org/wiki/League_of_Nations\" title=\"League of Nations\">League of Nations</a> is held in <a href=\"https://wikipedia.org/wiki/Geneva\" title=\"Geneva\">Geneva</a>, <a href=\"https://wikipedia.org/wiki/Switzerland\" title=\"Switzerland\">Switzerland</a>.", "no_year_html": "The first assembly of the <a href=\"https://wikipedia.org/wiki/League_of_Nations\" title=\"League of Nations\">League of Nations</a> is held in <a href=\"https://wikipedia.org/wiki/Geneva\" title=\"Geneva\">Geneva</a>, <a href=\"https://wikipedia.org/wiki/Switzerland\" title=\"Switzerland\">Switzerland</a>.", "links": [{"title": "League of Nations", "link": "https://wikipedia.org/wiki/League_of_Nations"}, {"title": "Geneva", "link": "https://wikipedia.org/wiki/Geneva"}, {"title": "Switzerland", "link": "https://wikipedia.org/wiki/Switzerland"}]}, {"year": "1920", "text": "The Free City of Danzig is established.", "html": "1920 - The <a href=\"https://wikipedia.org/wiki/Free_City_of_Danzig\" title=\"Free City of Danzig\">Free City of Danzig</a> is established.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Free_City_of_Danzig\" title=\"Free City of Danzig\">Free City of Danzig</a> is established.", "links": [{"title": "Free City of Danzig", "link": "https://wikipedia.org/wiki/Free_City_of_Danzig"}]}, {"year": "1922", "text": "At least 300 are massacred during a general strike in Guayaquil, Ecuador.", "html": "1922 - At least 300 are <a href=\"https://wikipedia.org/wiki/1922_Guayaquil_general_strike\" title=\"1922 Guayaquil general strike\">massacred</a> during a general strike in <a href=\"https://wikipedia.org/wiki/Guayaquil\" title=\"Guayaquil\">Guayaquil</a>, <a href=\"https://wikipedia.org/wiki/Ecuador\" title=\"Ecuador\">Ecuador</a>.", "no_year_html": "At least 300 are <a href=\"https://wikipedia.org/wiki/1922_Guayaquil_general_strike\" title=\"1922 Guayaquil general strike\">massacred</a> during a general strike in <a href=\"https://wikipedia.org/wiki/Guayaquil\" title=\"Guayaquil\">Guayaquil</a>, <a href=\"https://wikipedia.org/wiki/Ecuador\" title=\"Ecuador\">Ecuador</a>.", "links": [{"title": "1922 Guayaquil general strike", "link": "https://wikipedia.org/wiki/1922_Guayaquil_general_strike"}, {"title": "Guayaquil", "link": "https://wikipedia.org/wiki/Guayaquil"}, {"title": "Ecuador", "link": "https://wikipedia.org/wiki/Ecuador"}]}, {"year": "1928", "text": "The RNLI lifeboat <PERSON> capsizes in Rye Harbour with the loss of the entire 17-man crew.", "html": "1928 - The <a href=\"https://wikipedia.org/wiki/Royal_National_Lifeboat_Institution\" title=\"Royal National Lifeboat Institution\">RNLI</a> lifeboat <a href=\"https://wikipedia.org/wiki/R<PERSON><PERSON>_<PERSON>_<PERSON>_(ON_661)\" title=\"RNLB <PERSON> (ON 661)\"><i><PERSON></i></a> capsizes in <a href=\"https://wikipedia.org/wiki/Rye_Harbour\" title=\"Rye Harbour\">Rye Harbour</a> with the loss of the entire 17-man crew.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Royal_National_Lifeboat_Institution\" title=\"Royal National Lifeboat Institution\">RNLI</a> lifeboat <a href=\"https://wikipedia.org/wiki/R<PERSON><PERSON>_<PERSON>_<PERSON>_(ON_661)\" title=\"RNL<PERSON> (ON 661)\"><i><PERSON></i></a> capsizes in <a href=\"https://wikipedia.org/wiki/Rye_Harbour\" title=\"Rye Harbour\">Rye Harbour</a> with the loss of the entire 17-man crew.", "links": [{"title": "Royal National Lifeboat Institution", "link": "https://wikipedia.org/wiki/Royal_National_Lifeboat_Institution"}, {"title": "RNLB <PERSON> (ON 661)", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>_(ON_661)"}, {"title": "Rye Harbour", "link": "https://wikipedia.org/wiki/Rye_Harbour"}]}, {"year": "1933", "text": "Thailand holds its first election.", "html": "1933 - <a href=\"https://wikipedia.org/wiki/Thailand\" title=\"Thailand\">Thailand</a> holds <a href=\"https://wikipedia.org/wiki/1933_Siamese_general_election\" title=\"1933 Siamese general election\">its first election</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Thailand\" title=\"Thailand\">Thailand</a> holds <a href=\"https://wikipedia.org/wiki/1933_Siamese_general_election\" title=\"1933 Siamese general election\">its first election</a>.", "links": [{"title": "Thailand", "link": "https://wikipedia.org/wiki/Thailand"}, {"title": "1933 Siamese general election", "link": "https://wikipedia.org/wiki/1933_Siamese_general_election"}]}, {"year": "1938", "text": "Nazi Germany bans Jewish children from public schools in the aftermath of Kristallnacht.", "html": "1938 - <a href=\"https://wikipedia.org/wiki/Nazi_Germany\" title=\"Nazi Germany\">Nazi Germany</a> bans Jewish children from public schools in the aftermath of <a href=\"https://wikipedia.org/wiki/Kristallnacht\" title=\"Kristallnacht\">Kristallnacht</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Nazi_Germany\" title=\"Nazi Germany\">Nazi Germany</a> bans Jewish children from public schools in the aftermath of <a href=\"https://wikipedia.org/wiki/Kristallnacht\" title=\"Kristallnacht\">Kristallnacht</a>.", "links": [{"title": "Nazi Germany", "link": "https://wikipedia.org/wiki/Nazi_Germany"}, {"title": "Kristallnacht", "link": "https://wikipedia.org/wiki/Kristallnacht"}]}, {"year": "1942", "text": "World War II: The Battle of Guadalcanal ends in a decisive Allied victory.", "html": "1942 - <a href=\"https://wikipedia.org/wiki/World_War_II\" title=\"World War II\">World War II</a>: The <a href=\"https://wikipedia.org/wiki/Naval_Battle_of_Guadalcanal\" title=\"Naval Battle of Guadalcanal\">Battle of Guadalcanal</a> ends in a decisive <a href=\"https://wikipedia.org/wiki/Allies_of_World_War_II\" title=\"Allies of World War II\">Allied</a> victory.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/World_War_II\" title=\"World War II\">World War II</a>: The <a href=\"https://wikipedia.org/wiki/Naval_Battle_of_Guadalcanal\" title=\"Naval Battle of Guadalcanal\">Battle of Guadalcanal</a> ends in a decisive <a href=\"https://wikipedia.org/wiki/Allies_of_World_War_II\" title=\"Allies of World War II\">Allied</a> victory.", "links": [{"title": "World War II", "link": "https://wikipedia.org/wiki/World_War_II"}, {"title": "Naval Battle of Guadalcanal", "link": "https://wikipedia.org/wiki/Naval_Battle_of_Guadalcanal"}, {"title": "Allies of World War II", "link": "https://wikipedia.org/wiki/Allies_of_World_War_II"}]}, {"year": "1943", "text": "The Holocaust: German SS leader <PERSON> orders that Gypsies are to be put \"on the same level as Jews and placed in concentration camps\".", "html": "1943 - <a href=\"https://wikipedia.org/wiki/The_Holocaust\" title=\"The Holocaust\">The Holocaust</a>: German <a href=\"https://wikipedia.org/wiki/Schutzstaffel\" title=\"Schutzstaffel\">SS</a> leader <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> orders that <a href=\"https://wikipedia.org/wiki/Romani_people\" title=\"Romani people\">Gypsies</a> are to be put \"on the same level as <a href=\"https://wikipedia.org/wiki/Jews\" title=\"Jews\">Jews</a> and placed in <a href=\"https://wikipedia.org/wiki/Internment#Concentration_camp\" title=\"Internment\">concentration camps</a>\".", "no_year_html": "<a href=\"https://wikipedia.org/wiki/The_Holocaust\" title=\"The Holocaust\">The Holocaust</a>: German <a href=\"https://wikipedia.org/wiki/Schutzstaffel\" title=\"Schutzstaffel\">SS</a> leader <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> orders that <a href=\"https://wikipedia.org/wiki/Romani_people\" title=\"Romani people\">Gypsies</a> are to be put \"on the same level as <a href=\"https://wikipedia.org/wiki/Jews\" title=\"Jews\">Jews</a> and placed in <a href=\"https://wikipedia.org/wiki/Internment#Concentration_camp\" title=\"Internment\">concentration camps</a>\".", "links": [{"title": "The Holocaust", "link": "https://wikipedia.org/wiki/The_Holocaust"}, {"title": "<PERSON><PERSON><PERSON>sta<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>tzstaffel"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Romani people", "link": "https://wikipedia.org/wiki/Romani_people"}, {"title": "Jews", "link": "https://wikipedia.org/wiki/Jews"}, {"title": "Internment", "link": "https://wikipedia.org/wiki/Internment#Concentration_camp"}]}, {"year": "1951", "text": "<PERSON><PERSON>, along with 11 comrades, is sentenced to death for attempting to reestablish the Communist Party of Greece.", "html": "1951 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, along with 11 comrades, is sentenced to death for attempting to reestablish the <a href=\"https://wikipedia.org/wiki/Communist_Party_of_Greece\" title=\"Communist Party of Greece\">Communist Party of Greece</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, along with 11 comrades, is sentenced to death for attempting to reestablish the <a href=\"https://wikipedia.org/wiki/Communist_Party_of_Greece\" title=\"Communist Party of Greece\">Communist Party of Greece</a>.", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Nikos_Beloyannis"}, {"title": "Communist Party of Greece", "link": "https://wikipedia.org/wiki/Communist_Party_of_Greece"}]}, {"year": "1955", "text": "The first part of the Saint Petersburg Metro is opened.", "html": "1955 - The first part of the <a href=\"https://wikipedia.org/wiki/Saint_Petersburg_Metro\" title=\"Saint Petersburg Metro\">Saint Petersburg Metro</a> is opened.", "no_year_html": "The first part of the <a href=\"https://wikipedia.org/wiki/Saint_Petersburg_Metro\" title=\"Saint Petersburg Metro\">Saint Petersburg Metro</a> is opened.", "links": [{"title": "Saint Petersburg Metro", "link": "https://wikipedia.org/wiki/Saint_Petersburg_Metro"}]}, {"year": "1957", "text": "<PERSON>ent 3 crashes near Chessell.", "html": "1957 - Short Solent 3 <a href=\"https://wikipedia.org/wiki/1957_Aquila_Airways_Solent_crash\" title=\"1957 Aquila Airways Solent crash\">crashes</a> near <a href=\"https://wikipedia.org/wiki/<PERSON>ell\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>.", "no_year_html": "Short Solent 3 <a href=\"https://wikipedia.org/wiki/1957_Aquila_Airways_Solent_crash\" title=\"1957 Aquila Airways Solent crash\">crashes</a> near <a href=\"https://wikipedia.org/wiki/<PERSON>ell\" title=\"Chessell\"><PERSON><PERSON></a>.", "links": [{"title": "1957 Aquila Airways Solent crash", "link": "https://wikipedia.org/wiki/1957_Aquila_Airways_Solent_crash"}, {"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>"}]}, {"year": "1959", "text": "Four members of the <PERSON><PERSON><PERSON> family are murdered near Holcomb, Kansas, by <PERSON> and <PERSON>, a crime later detailed by <PERSON> in his 1966 non-fiction novel In Cold Blood.", "html": "1959 - <a href=\"https://wikipedia.org/wiki/Clutter_family_murders\" title=\"Clutter family murders\">Four members of the <PERSON><PERSON><PERSON> family are murdered</a> near <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>,_Kansas\" title=\"Holcomb, Kansas\">Holcomb, Kansas</a>, by <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> and <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, a crime later detailed by <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> in his 1966 <a href=\"https://wikipedia.org/wiki/Non-fiction_novel\" title=\"Non-fiction novel\">non-fiction novel</a> <i><a href=\"https://wikipedia.org/wiki/In_Cold_Blood\" title=\"In Cold Blood\">In Cold Blood</a></i>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Clutter_family_murders\" title=\"Clutter family murders\">Four members of the <PERSON><PERSON><PERSON> family are murdered</a> near <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>,_Kansas\" title=\"Holcomb, Kansas\">Holcomb, Kansas</a>, by <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> and <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, a crime later detailed by <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> in his 1966 <a href=\"https://wikipedia.org/wiki/Non-fiction_novel\" title=\"Non-fiction novel\">non-fiction novel</a> <i><a href=\"https://wikipedia.org/wiki/In_Cold_Blood\" title=\"In Cold Blood\">In Cold Blood</a></i>.", "links": [{"title": "Clutter family murders", "link": "https://wikipedia.org/wiki/Clutter_family_murders"}, {"title": "Holcomb, Kansas", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>comb,_Kansas"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Non-fiction novel", "link": "https://wikipedia.org/wiki/Non-fiction_novel"}, {"title": "In Cold Blood", "link": "https://wikipedia.org/wiki/In_Cold_Blood"}]}, {"year": "1965", "text": "<PERSON> sets a land speed record of 600.601 mph (966.574 km/h) in his car, the Spirit of America, at the Bonneville Salt Flats in Utah.", "html": "1965 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> sets a land speed record of 600.601 mph (966.574 km/h) in his car, the <a href=\"https://wikipedia.org/wiki/Spirit_of_America_(automobile)\" title=\"Spirit of America (automobile)\">Spirit of America</a>, at the <a href=\"https://wikipedia.org/wiki/Bonneville_Salt_Flats\" title=\"Bonneville Salt Flats\">Bonneville Salt Flats</a> in <a href=\"https://wikipedia.org/wiki/Utah\" title=\"Utah\">Utah</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> sets a land speed record of 600.601 mph (966.574 km/h) in his car, the <a href=\"https://wikipedia.org/wiki/Spirit_of_America_(automobile)\" title=\"Spirit of America (automobile)\">Spirit of America</a>, at the <a href=\"https://wikipedia.org/wiki/Bonneville_Salt_Flats\" title=\"Bonneville Salt Flats\">Bonneville Salt Flats</a> in <a href=\"https://wikipedia.org/wiki/Utah\" title=\"Utah\">Utah</a>.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Spirit of America (automobile)", "link": "https://wikipedia.org/wiki/Spirit_of_America_(automobile)"}, {"title": "Bonneville Salt Flats", "link": "https://wikipedia.org/wiki/Bonneville_Salt_Flats"}, {"title": "Utah", "link": "https://wikipedia.org/wiki/Utah"}]}, {"year": "1966", "text": "Project Gemini: Gemini 12 completes the program's final mission, when it splashes down safely in the Atlantic Ocean.", "html": "1966 - <a href=\"https://wikipedia.org/wiki/Project_Gemini\" title=\"Project Gemini\">Project Gemini</a>: <i><a href=\"https://wikipedia.org/wiki/Gemini_12\" title=\"Gemini 12\">Gemini 12</a></i> completes the program's final mission, when it splashes down safely in the <a href=\"https://wikipedia.org/wiki/Atlantic_Ocean\" title=\"Atlantic Ocean\">Atlantic Ocean</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Project_Gemini\" title=\"Project Gemini\">Project Gemini</a>: <i><a href=\"https://wikipedia.org/wiki/Gemini_12\" title=\"Gemini 12\">Gemini 12</a></i> completes the program's final mission, when it splashes down safely in the <a href=\"https://wikipedia.org/wiki/Atlantic_Ocean\" title=\"Atlantic Ocean\">Atlantic Ocean</a>.", "links": [{"title": "Project Gemini", "link": "https://wikipedia.org/wiki/Project_Gemini"}, {"title": "Gemini 12", "link": "https://wikipedia.org/wiki/Gemini_12"}, {"title": "Atlantic Ocean", "link": "https://wikipedia.org/wiki/Atlantic_Ocean"}]}, {"year": "1967", "text": "The only fatality of the North American X-15 program occurs during the 191st flight when Air Force test pilot <PERSON> loses control of his aircraft which is destroyed mid-air over the Mojave Desert.", "html": "1967 - The only fatality of the <a href=\"https://wikipedia.org/wiki/North_American_X-15\" title=\"North American X-15\">North American X-15</a> program occurs during the <a href=\"https://wikipedia.org/wiki/X-15_Flight_3-65-97\" title=\"X-15 Flight 3-65-97\">191st flight</a> when Air Force test pilot <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> loses control of his aircraft which is destroyed mid-air over the <a href=\"https://wikipedia.org/wiki/Mojave_Desert\" title=\"Mojave Desert\">Mojave Desert</a>.", "no_year_html": "The only fatality of the <a href=\"https://wikipedia.org/wiki/North_American_X-15\" title=\"North American X-15\">North American X-15</a> program occurs during the <a href=\"https://wikipedia.org/wiki/X-15_Flight_3-65-97\" title=\"X-15 Flight 3-65-97\">191st flight</a> when Air Force test pilot <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> loses control of his aircraft which is destroyed mid-air over the <a href=\"https://wikipedia.org/wiki/Mojave_Desert\" title=\"Mojave Desert\">Mojave Desert</a>.", "links": [{"title": "North American X-15", "link": "https://wikipedia.org/wiki/North_American_X-15"}, {"title": "X-15 Flight 3-65-97", "link": "https://wikipedia.org/wiki/X-15_Flight_3-65-97"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "Mojave Desert", "link": "https://wikipedia.org/wiki/Mojave_Desert"}]}, {"year": "1968", "text": "The Cleveland Transit System becomes the first transit system in the western hemisphere to provide direct rapid transit service from a city's downtown to its major airport.", "html": "1968 - The <a href=\"https://wikipedia.org/wiki/Cleveland_Transit_System\" class=\"mw-redirect\" title=\"Cleveland Transit System\">Cleveland Transit System</a> becomes the first transit system in the <a href=\"https://wikipedia.org/wiki/Western_Hemisphere\" title=\"Western Hemisphere\">western hemisphere</a> to provide direct rapid transit service from <a href=\"https://wikipedia.org/wiki/Cleveland\" title=\"Cleveland\">a city's downtown</a> to <a href=\"https://wikipedia.org/wiki/Cleveland_Hopkins_International_Airport\" title=\"Cleveland Hopkins International Airport\">its major airport</a>.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Cleveland_Transit_System\" class=\"mw-redirect\" title=\"Cleveland Transit System\">Cleveland Transit System</a> becomes the first transit system in the <a href=\"https://wikipedia.org/wiki/Western_Hemisphere\" title=\"Western Hemisphere\">western hemisphere</a> to provide direct rapid transit service from <a href=\"https://wikipedia.org/wiki/Cleveland\" title=\"Cleveland\">a city's downtown</a> to <a href=\"https://wikipedia.org/wiki/Cleveland_Hopkins_International_Airport\" title=\"Cleveland Hopkins International Airport\">its major airport</a>.", "links": [{"title": "Cleveland Transit System", "link": "https://wikipedia.org/wiki/Cleveland_Transit_System"}, {"title": "Western Hemisphere", "link": "https://wikipedia.org/wiki/Western_Hemisphere"}, {"title": "Cleveland", "link": "https://wikipedia.org/wiki/Cleveland"}, {"title": "Cleveland Hopkins International Airport", "link": "https://wikipedia.org/wiki/Cleveland_Hopkins_International_Airport"}]}, {"year": "1969", "text": "Cold War: The Soviet submarine K-19 collides with the American submarine USS Gato in the Barents Sea.", "html": "1969 - <a href=\"https://wikipedia.org/wiki/Cold_War\" title=\"Cold War\">Cold War</a>: The <a href=\"https://wikipedia.org/wiki/Soviet_submarine_K-19\" title=\"Soviet submarine K-19\">Soviet submarine K-19</a> collides with the <a href=\"https://wikipedia.org/wiki/United_States_Navy\" title=\"United States Navy\">American submarine</a> <a href=\"https://wikipedia.org/wiki/USS_Gato_(SSN-615)\" title=\"USS Gato (SSN-615)\">USS <i>Gato</i></a> in the <a href=\"https://wikipedia.org/wiki/Barents_Sea\" title=\"Barents Sea\">Barents Sea</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Cold_War\" title=\"Cold War\">Cold War</a>: The <a href=\"https://wikipedia.org/wiki/Soviet_submarine_K-19\" title=\"Soviet submarine K-19\">Soviet submarine K-19</a> collides with the <a href=\"https://wikipedia.org/wiki/United_States_Navy\" title=\"United States Navy\">American submarine</a> <a href=\"https://wikipedia.org/wiki/USS_Gato_(SSN-615)\" title=\"USS Gato (SSN-615)\">USS <i>Gato</i></a> in the <a href=\"https://wikipedia.org/wiki/Barents_Sea\" title=\"Barents Sea\">Barents Sea</a>.", "links": [{"title": "Cold War", "link": "https://wikipedia.org/wiki/Cold_War"}, {"title": "Soviet submarine K-19", "link": "https://wikipedia.org/wiki/Soviet_submarine_K-19"}, {"title": "United States Navy", "link": "https://wikipedia.org/wiki/United_States_Navy"}, {"title": "USS Gato (SSN-615)", "link": "https://wikipedia.org/wiki/USS_Gato_(SSN-615)"}, {"title": "Barents Sea", "link": "https://wikipedia.org/wiki/Barents_Sea"}]}, {"year": "1969", "text": "Vietnam War: In Washington, D.C., 250,000-500,000 protesters staged a peaceful demonstration against the war, including a symbolic \"March Against Death\".", "html": "1969 - <a href=\"https://wikipedia.org/wiki/Vietnam_War\" title=\"Vietnam War\">Vietnam War</a>: In <a href=\"https://wikipedia.org/wiki/Washington,_D.C.\" title=\"Washington, D.C.\">Washington, D.C.</a>, 250,000-500,000 protesters staged a <a href=\"https://wikipedia.org/wiki/Moratorium_to_End_the_War_in_Vietnam#November_15,_1969,_Moratorium_March_on_Washington\" title=\"Moratorium to End the War in Vietnam\">peaceful demonstration against the war</a>, including a symbolic \"March Against Death\".", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Vietnam_War\" title=\"Vietnam War\">Vietnam War</a>: In <a href=\"https://wikipedia.org/wiki/Washington,_D.C.\" title=\"Washington, D.C.\">Washington, D.C.</a>, 250,000-500,000 protesters staged a <a href=\"https://wikipedia.org/wiki/Moratorium_to_End_the_War_in_Vietnam#November_15,_1969,_Moratorium_March_on_Washington\" title=\"Moratorium to End the War in Vietnam\">peaceful demonstration against the war</a>, including a symbolic \"March Against Death\".", "links": [{"title": "Vietnam War", "link": "https://wikipedia.org/wiki/Vietnam_War"}, {"title": "Washington, D.C.", "link": "https://wikipedia.org/wiki/Washington,_D.C."}, {"title": "Moratorium to End the War in Vietnam", "link": "https://wikipedia.org/wiki/Moratorium_to_End_the_War_in_Vietnam#November_15,_1969,_Moratorium_March_on_Washington"}]}, {"year": "1971", "text": "Intel releases the world's first commercial single-chip microprocessor, the 4004.", "html": "1971 - <a href=\"https://wikipedia.org/wiki/Intel\" title=\"Intel\">Intel</a> releases the world's first commercial single-chip <a href=\"https://wikipedia.org/wiki/Microprocessor\" title=\"Microprocessor\">microprocessor</a>, the <a href=\"https://wikipedia.org/wiki/Intel_4004\" title=\"Intel 4004\">4004</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Intel\" title=\"Intel\">Intel</a> releases the world's first commercial single-chip <a href=\"https://wikipedia.org/wiki/Microprocessor\" title=\"Microprocessor\">microprocessor</a>, the <a href=\"https://wikipedia.org/wiki/Intel_4004\" title=\"Intel 4004\">4004</a>.", "links": [{"title": "Intel", "link": "https://wikipedia.org/wiki/Intel"}, {"title": "Microprocessor", "link": "https://wikipedia.org/wiki/Microprocessor"}, {"title": "Intel 4004", "link": "https://wikipedia.org/wiki/Intel_4004"}]}, {"year": "1976", "text": "<PERSON> and the Parti Québécois take power to become the first Quebec government of the 20th century clearly in favor of independence.", "html": "1976 - <a href=\"https://wikipedia.org/wiki/Ren%C3%A9_L%C3%A9vesque\" title=\"<PERSON>\"><PERSON></a> and the <i><a href=\"https://wikipedia.org/wiki/Parti_Qu%C3%A9b%C3%A9cois\" title=\"Parti Québécois\">Parti Québécois</a></i> <a href=\"https://wikipedia.org/wiki/1976_Quebec_general_election\" title=\"1976 Quebec general election\">take power</a> to become the first <a href=\"https://wikipedia.org/wiki/Quebec\" title=\"Quebec\">Quebec</a> government of the 20th century clearly in favor of <a href=\"https://wikipedia.org/wiki/Quebec_sovereignty_movement\" title=\"Quebec sovereignty movement\">independence</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Ren%C3%A9_L%C3%A9vesque\" title=\"<PERSON>\"><PERSON></a> and the <i><a href=\"https://wikipedia.org/wiki/Parti_Qu%C3%A9b%C3%A9cois\" title=\"Parti Québécois\">Parti Québécois</a></i> <a href=\"https://wikipedia.org/wiki/1976_Quebec_general_election\" title=\"1976 Quebec general election\">take power</a> to become the first <a href=\"https://wikipedia.org/wiki/Quebec\" title=\"Quebec\">Quebec</a> government of the 20th century clearly in favor of <a href=\"https://wikipedia.org/wiki/Quebec_sovereignty_movement\" title=\"Quebec sovereignty movement\">independence</a>.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Ren%C3%A9_L%C3%A9vesque"}, {"title": "Parti Québécois", "link": "https://wikipedia.org/wiki/Parti_Qu%C3%A9b%C3%A9cois"}, {"title": "1976 Quebec general election", "link": "https://wikipedia.org/wiki/1976_Quebec_general_election"}, {"title": "Quebec", "link": "https://wikipedia.org/wiki/Quebec"}, {"title": "Quebec sovereignty movement", "link": "https://wikipedia.org/wiki/Quebec_sovereignty_movement"}]}, {"year": "1978", "text": "A chartered Douglas DC-8 crashes near Colombo, Sri Lanka, killing 183.", "html": "1978 - A chartered <a href=\"https://wikipedia.org/wiki/Douglas_DC-8\" title=\"Douglas DC-8\">Douglas DC-8</a> <a href=\"https://wikipedia.org/wiki/Loftlei%C3%B0ir_Flight_001\" title=\"Loftleiðir Flight 001\">crashes</a> near <a href=\"https://wikipedia.org/wiki/Colombo\" title=\"Colombo\">Colombo</a>, <a href=\"https://wikipedia.org/wiki/Sri_Lanka\" title=\"Sri Lanka\">Sri Lanka</a>, killing 183.", "no_year_html": "A chartered <a href=\"https://wikipedia.org/wiki/Douglas_DC-8\" title=\"Douglas DC-8\">Douglas DC-8</a> <a href=\"https://wikipedia.org/wiki/Loftlei%C3%B0ir_Flight_001\" title=\"Loftleiðir Flight 001\">crashes</a> near <a href=\"https://wikipedia.org/wiki/Colombo\" title=\"Colombo\">Colombo</a>, <a href=\"https://wikipedia.org/wiki/Sri_Lanka\" title=\"Sri Lanka\">Sri Lanka</a>, killing 183.", "links": [{"title": "Douglas DC-8", "link": "https://wikipedia.org/wiki/Douglas_DC-8"}, {"title": "Loftleiðir Flight 001", "link": "https://wikipedia.org/wiki/Loftlei%C3%B0ir_Flight_001"}, {"title": "Colombo", "link": "https://wikipedia.org/wiki/Colombo"}, {"title": "Sri Lanka", "link": "https://wikipedia.org/wiki/Sri_Lanka"}]}, {"year": "1979", "text": "A package from Unabomber <PERSON> begins smoking in the cargo hold of a flight from Chicago to Washington, D.C., forcing the plane to make an emergency landing.", "html": "1979 - A package from Unabomber <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> begins smoking in the cargo hold of <a href=\"https://wikipedia.org/wiki/American_Airlines_Flight_444\" title=\"American Airlines Flight 444\">a flight</a> from <a href=\"https://wikipedia.org/wiki/Chicago\" title=\"Chicago\">Chicago</a> to <a href=\"https://wikipedia.org/wiki/Washington,_D.C.\" title=\"Washington, D.C.\">Washington, D.C.</a>, forcing the plane to make an emergency landing.", "no_year_html": "A package from Unabomber <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> begins smoking in the cargo hold of <a href=\"https://wikipedia.org/wiki/American_Airlines_Flight_444\" title=\"American Airlines Flight 444\">a flight</a> from <a href=\"https://wikipedia.org/wiki/Chicago\" title=\"Chicago\">Chicago</a> to <a href=\"https://wikipedia.org/wiki/Washington,_D.C.\" title=\"Washington, D.C.\">Washington, D.C.</a>, forcing the plane to make an emergency landing.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "American Airlines Flight 444", "link": "https://wikipedia.org/wiki/American_Airlines_Flight_444"}, {"title": "Chicago", "link": "https://wikipedia.org/wiki/Chicago"}, {"title": "Washington, D.C.", "link": "https://wikipedia.org/wiki/Washington,_D.C."}]}, {"year": "1983", "text": "Turkish Republic of Northern Cyprus declares independence; it is only recognized by Turkey.", "html": "1983 - <a href=\"https://wikipedia.org/wiki/Northern_Cyprus\" title=\"Northern Cyprus\">Turkish Republic of Northern Cyprus</a> declares independence; it is <a href=\"https://wikipedia.org/wiki/Foreign_relations_of_Northern_Cyprus\" title=\"Foreign relations of Northern Cyprus\">only recognized by Turkey</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Northern_Cyprus\" title=\"Northern Cyprus\">Turkish Republic of Northern Cyprus</a> declares independence; it is <a href=\"https://wikipedia.org/wiki/Foreign_relations_of_Northern_Cyprus\" title=\"Foreign relations of Northern Cyprus\">only recognized by Turkey</a>.", "links": [{"title": "Northern Cyprus", "link": "https://wikipedia.org/wiki/Northern_Cyprus"}, {"title": "Foreign relations of Northern Cyprus", "link": "https://wikipedia.org/wiki/Foreign_relations_of_Northern_Cyprus"}]}, {"year": "1985", "text": "A research assistant is injured when a package from the Unabomber addressed to a University of Michigan professor explodes.", "html": "1985 - A research assistant is injured when a package from the <a href=\"https://wikipedia.org/wiki/Unabomber\" class=\"mw-redirect\" title=\"Unabomber\">Unabomber</a> addressed to a <a href=\"https://wikipedia.org/wiki/University_of_Michigan\" title=\"University of Michigan\">University of Michigan</a> professor explodes.", "no_year_html": "A research assistant is injured when a package from the <a href=\"https://wikipedia.org/wiki/Unabomber\" class=\"mw-redirect\" title=\"Unabomber\">Unabomber</a> addressed to a <a href=\"https://wikipedia.org/wiki/University_of_Michigan\" title=\"University of Michigan\">University of Michigan</a> professor explodes.", "links": [{"title": "Unabomber", "link": "https://wikipedia.org/wiki/Unabomber"}, {"title": "University of Michigan", "link": "https://wikipedia.org/wiki/University_of_Michigan"}]}, {"year": "1985", "text": "The Anglo-Irish Agreement is signed at Hillsborough Castle by British Prime Minister <PERSON> and Irish Taoiseach <PERSON><PERSON><PERSON>.", "html": "1985 - The <a href=\"https://wikipedia.org/wiki/Anglo-Irish_Agreement\" title=\"Anglo-Irish Agreement\">Anglo-Irish Agreement</a> is signed at <a href=\"https://wikipedia.org/wiki/Hillsborough,_County_Down\" class=\"mw-redirect\" title=\"Hillsborough, County Down\">Hillsborough Castle</a> by <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_the_United_Kingdom\" title=\"Prime Minister of the United Kingdom\">British Prime Minister</a> <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> and Irish <a href=\"https://wikipedia.org/wiki/Taoiseach\" title=\"Taoiseach\">Taoiseach</a> <a href=\"https://wikipedia.org/wiki/G<PERSON><PERSON>_<PERSON>tz<PERSON>erald\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Anglo-Irish_Agreement\" title=\"Anglo-Irish Agreement\">Anglo-Irish Agreement</a> is signed at <a href=\"https://wikipedia.org/wiki/Hillsborough,_County_Down\" class=\"mw-redirect\" title=\"Hillsborough, County Down\">Hillsborough Castle</a> by <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_the_United_Kingdom\" title=\"Prime Minister of the United Kingdom\">British Prime Minister</a> <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> and Irish <a href=\"https://wikipedia.org/wiki/Taoiseach\" title=\"Taoiseach\">Taoiseach</a> <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>erald\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>.", "links": [{"title": "Anglo-Irish Agreement", "link": "https://wikipedia.org/wiki/Anglo-Irish_Agreement"}, {"title": "Hillsborough, County Down", "link": "https://wikipedia.org/wiki/Hillsborough,_County_Down"}, {"title": "Prime Minister of the United Kingdom", "link": "https://wikipedia.org/wiki/Prime_Minister_of_the_United_Kingdom"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>iseach"}, {"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON><PERSON>"}]}, {"year": "1987", "text": "In Brașov, Romania, workers rebel against the communist regime of <PERSON><PERSON>.", "html": "1987 - In <a href=\"https://wikipedia.org/wiki/Bra%C8%99ov\" title=\"Brașov\"><PERSON><PERSON><PERSON><PERSON></a>, <a href=\"https://wikipedia.org/wiki/Socialist_Republic_of_Romania\" title=\"Socialist Republic of Romania\">Romania</a>, <a href=\"https://wikipedia.org/wiki/Bra%C8%99ov_Rebellion\" class=\"mw-redirect\" title=\"Brașov Rebellion\">workers rebel</a> against the communist regime of <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>%C8%99<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>.", "no_year_html": "In <a href=\"https://wikipedia.org/wiki/Bra%C8%99ov\" title=\"Brașov\"><PERSON><PERSON><PERSON><PERSON></a>, <a href=\"https://wikipedia.org/wiki/Socialist_Republic_of_Romania\" title=\"Socialist Republic of Romania\">Romania</a>, <a href=\"https://wikipedia.org/wiki/Bra%C8%99ov_Rebellion\" class=\"mw-redirect\" title=\"Brașov Rebellion\">workers rebel</a> against the communist regime of <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>%C8%99<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>.", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Bra%C8%99ov"}, {"title": "Socialist Republic of Romania", "link": "https://wikipedia.org/wiki/Socialist_Republic_of_Romania"}, {"title": "Brașov Rebellion", "link": "https://wikipedia.org/wiki/Bra%C8%99ov_Rebellion"}, {"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>%C8%99escu"}]}, {"year": "1987", "text": "Continental Airlines Flight 1713 crashes during takeoff from Stapleton International Airport in Denver, Colorado, killing 25.", "html": "1987 - <a href=\"https://wikipedia.org/wiki/Continental_Airlines_Flight_1713\" title=\"Continental Airlines Flight 1713\">Continental Airlines Flight 1713</a> crashes during takeoff from <a href=\"https://wikipedia.org/wiki/Stapleton_International_Airport\" title=\"Stapleton International Airport\">Stapleton International Airport</a> in <a href=\"https://wikipedia.org/wiki/Denver\" title=\"Denver\">Denver</a>, Colorado, killing 25.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Continental_Airlines_Flight_1713\" title=\"Continental Airlines Flight 1713\">Continental Airlines Flight 1713</a> crashes during takeoff from <a href=\"https://wikipedia.org/wiki/Stapleton_International_Airport\" title=\"Stapleton International Airport\">Stapleton International Airport</a> in <a href=\"https://wikipedia.org/wiki/Denver\" title=\"Denver\">Denver</a>, Colorado, killing 25.", "links": [{"title": "Continental Airlines Flight 1713", "link": "https://wikipedia.org/wiki/Continental_Airlines_Flight_1713"}, {"title": "Stapleton International Airport", "link": "https://wikipedia.org/wiki/Stapleton_International_Airport"}, {"title": "Denver", "link": "https://wikipedia.org/wiki/Denver"}]}, {"year": "1988", "text": "In the Soviet Union, the uncrewed Shuttle Buran makes its only space flight.", "html": "1988 - In the <a href=\"https://wikipedia.org/wiki/Soviet_Union\" title=\"Soviet Union\">Soviet Union</a>, the uncrewed <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_(spacecraft)\" title=\"<PERSON><PERSON><PERSON> (spacecraft)\">Shuttle <i>Buran</i></a> makes its only space flight.", "no_year_html": "In the <a href=\"https://wikipedia.org/wiki/Soviet_Union\" title=\"Soviet Union\">Soviet Union</a>, the uncrewed <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_(spacecraft)\" title=\"<PERSON><PERSON><PERSON> (spacecraft)\">Shuttle <i>Buran</i></a> makes its only space flight.", "links": [{"title": "Soviet Union", "link": "https://wikipedia.org/wiki/Soviet_Union"}, {"title": "<PERSON><PERSON><PERSON> (spacecraft)", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_(spacecraft)"}]}, {"year": "1988", "text": "Israeli-Palestinian conflict: An independent State of Palestine is proclaimed by the Palestinian National Council.", "html": "1988 - <a href=\"https://wikipedia.org/wiki/Israeli%E2%80%93Palestinian_conflict\" title=\"Israeli-Palestinian conflict\">Israeli-Palestinian conflict</a>: An independent <a href=\"https://wikipedia.org/wiki/State_of_Palestine\" class=\"mw-redirect\" title=\"State of Palestine\">State of Palestine</a> is proclaimed by the <a href=\"https://wikipedia.org/wiki/Palestinian_National_Council\" title=\"Palestinian National Council\">Palestinian National Council</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Israeli%E2%80%93Palestinian_conflict\" title=\"Israeli-Palestinian conflict\">Israeli-Palestinian conflict</a>: An independent <a href=\"https://wikipedia.org/wiki/State_of_Palestine\" class=\"mw-redirect\" title=\"State of Palestine\">State of Palestine</a> is proclaimed by the <a href=\"https://wikipedia.org/wiki/Palestinian_National_Council\" title=\"Palestinian National Council\">Palestinian National Council</a>.", "links": [{"title": "Israeli-Palestinian conflict", "link": "https://wikipedia.org/wiki/Israeli%E2%80%93Palestinian_conflict"}, {"title": "State of Palestine", "link": "https://wikipedia.org/wiki/State_of_Palestine"}, {"title": "Palestinian National Council", "link": "https://wikipedia.org/wiki/Palestinian_National_Council"}]}, {"year": "1988", "text": "The first Fairtrade label, Max Havelaar, is launched in the Netherlands.", "html": "1988 - The first <a href=\"https://wikipedia.org/wiki/Fairtrade_certification\" class=\"mw-redirect\" title=\"Fairtrade certification\">Fairtrade</a> label, <a href=\"https://wikipedia.org/wiki/Stichting_Max_Have<PERSON>ar\" title=\"Stichting Max Havelaar\"><PERSON></a>, is launched in the <a href=\"https://wikipedia.org/wiki/Netherlands\" title=\"Netherlands\">Netherlands</a>.", "no_year_html": "The first <a href=\"https://wikipedia.org/wiki/Fairtrade_certification\" class=\"mw-redirect\" title=\"Fairtrade certification\">Fairtrade</a> label, <a href=\"https://wikipedia.org/wiki/Stichting_Max_Have<PERSON>ar\" title=\"Stichting Max Havelaar\"><PERSON></a>, is launched in the <a href=\"https://wikipedia.org/wiki/Netherlands\" title=\"Netherlands\">Netherlands</a>.", "links": [{"title": "Fairtrade certification", "link": "https://wikipedia.org/wiki/Fairtrade_certification"}, {"title": "Stichting Max <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>"}, {"title": "Netherlands", "link": "https://wikipedia.org/wiki/Netherlands"}]}, {"year": "1990", "text": "The Communist People's Republic of Bulgaria is disestablished and a new republican government is instituted.", "html": "1990 - The Communist <a href=\"https://wikipedia.org/wiki/People%27s_Republic_of_Bulgaria\" title=\"People's Republic of Bulgaria\">People's Republic of Bulgaria</a> is disestablished and a new <a href=\"https://wikipedia.org/wiki/Bulgaria\" title=\"Bulgaria\">republican government</a> is instituted.", "no_year_html": "The Communist <a href=\"https://wikipedia.org/wiki/People%27s_Republic_of_Bulgaria\" title=\"People's Republic of Bulgaria\">People's Republic of Bulgaria</a> is disestablished and a new <a href=\"https://wikipedia.org/wiki/Bulgaria\" title=\"Bulgaria\">republican government</a> is instituted.", "links": [{"title": "People's Republic of Bulgaria", "link": "https://wikipedia.org/wiki/People%27s_Republic_of_Bulgaria"}, {"title": "Bulgaria", "link": "https://wikipedia.org/wiki/Bulgaria"}]}, {"year": "1990", "text": "Space Shuttle Atlantis is launched on STS-38, a classified mission for the Department of Defense.", "html": "1990 - <a href=\"https://wikipedia.org/wiki/Space_Shuttle_Atlantis\" title=\"Space Shuttle Atlantis\">Space Shuttle <i>Atlantis</i></a> is launched on <a href=\"https://wikipedia.org/wiki/STS-38\" title=\"STS-38\">STS-38</a>, a <a href=\"https://wikipedia.org/wiki/Classified_information_in_the_United_States\" title=\"Classified information in the United States\">classified</a> mission for the <a href=\"https://wikipedia.org/wiki/United_States_Department_of_Defense\" title=\"United States Department of Defense\">Department of Defense</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Space_Shuttle_Atlantis\" title=\"Space Shuttle Atlantis\">Space Shuttle <i>Atlantis</i></a> is launched on <a href=\"https://wikipedia.org/wiki/STS-38\" title=\"STS-38\">STS-38</a>, a <a href=\"https://wikipedia.org/wiki/Classified_information_in_the_United_States\" title=\"Classified information in the United States\">classified</a> mission for the <a href=\"https://wikipedia.org/wiki/United_States_Department_of_Defense\" title=\"United States Department of Defense\">Department of Defense</a>.", "links": [{"title": "Space Shuttle Atlantis", "link": "https://wikipedia.org/wiki/Space_Shuttle_Atlantis"}, {"title": "STS-38", "link": "https://wikipedia.org/wiki/STS-38"}, {"title": "Classified information in the United States", "link": "https://wikipedia.org/wiki/Classified_information_in_the_United_States"}, {"title": "United States Department of Defense", "link": "https://wikipedia.org/wiki/United_States_Department_of_Defense"}]}, {"year": "1994", "text": "A magnitude 7.1 earthquake hits the central Philippine island of Mindoro, killing 78 people, injuring 430 and triggering a tsunami up to 8.5 m (28 ft) high.", "html": "1994 - A <a href=\"https://wikipedia.org/wiki/1994_Mindoro_earthquake\" title=\"1994 Mindoro earthquake\">magnitude 7.1 earthquake</a> hits the central Philippine island of <a href=\"https://wikipedia.org/wiki/Mindoro\" title=\"Mindoro\">Mindoro</a>, killing 78 people, injuring 430 and triggering a tsunami up to 8.5 m (28 ft) high.", "no_year_html": "A <a href=\"https://wikipedia.org/wiki/1994_Mindoro_earthquake\" title=\"1994 Mindoro earthquake\">magnitude 7.1 earthquake</a> hits the central Philippine island of <a href=\"https://wikipedia.org/wiki/Mindoro\" title=\"Mindoro\">Mindoro</a>, killing 78 people, injuring 430 and triggering a tsunami up to 8.5 m (28 ft) high.", "links": [{"title": "1994 Mindoro earthquake", "link": "https://wikipedia.org/wiki/1994_Mindoro_earthquake"}, {"title": "Mindoro", "link": "https://wikipedia.org/wiki/Mindoro"}]}, {"year": "2000", "text": "A chartered Antonov An-24 crashes after takeoff from Luanda, Angola, killing more than 40 people.", "html": "2000 - A chartered <a href=\"https://wikipedia.org/wiki/Antonov_An-24\" title=\"Antonov An-24\"><PERSON><PERSON> An-24</a> <a href=\"https://wikipedia.org/wiki/2000_ASA_Pesada_Antonov_An-24_crash\" title=\"2000 ASA Pesada Antonov An-24 crash\">crashes</a> after takeoff from <a href=\"https://wikipedia.org/wiki/Luanda\" title=\"Luanda\">Lu<PERSON></a>, <a href=\"https://wikipedia.org/wiki/Angola\" title=\"Angola\">Angola</a>, killing more than 40 people.", "no_year_html": "A chartered <a href=\"https://wikipedia.org/wiki/Antonov_An-24\" title=\"Antonov An-24\"><PERSON><PERSON> An-24</a> <a href=\"https://wikipedia.org/wiki/2000_ASA_Pesada_Antonov_An-24_crash\" title=\"2000 ASA Pesada Antonov An-24 crash\">crashes</a> after takeoff from <a href=\"https://wikipedia.org/wiki/Luanda\" title=\"Luanda\">Lu<PERSON></a>, <a href=\"https://wikipedia.org/wiki/Angola\" title=\"Angola\">Angola</a>, killing more than 40 people.", "links": [{"title": "Antonov An-24", "link": "https://wikipedia.org/wiki/Antonov_An-24"}, {"title": "2000 ASA Pesada <PERSON> An-24 crash", "link": "https://wikipedia.org/wiki/2000_ASA_P<PERSON><PERSON>_<PERSON>_An-24_crash"}, {"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Luanda"}, {"title": "Angola", "link": "https://wikipedia.org/wiki/Angola"}]}, {"year": "2000", "text": "Jharkhand officially becomes the 28th state of India, formed from eighteen districts of southern Bihar.", "html": "2000 - <a href=\"https://wikipedia.org/wiki/Jharkhand\" title=\"Jharkhand\">Jharkhand</a> officially <a href=\"https://wikipedia.org/wiki/Bihar_Reorganisation_Act,_2000\" title=\"Bihar Reorganisation Act, 2000\">becomes</a> the 28th <a href=\"https://wikipedia.org/wiki/States_of_India\" class=\"mw-redirect\" title=\"States of India\">state of India</a>, formed from <a href=\"https://wikipedia.org/wiki/List_of_districts_of_Bihar\" title=\"List of districts of Bihar\">eighteen districts</a> of southern <a href=\"https://wikipedia.org/wiki/Bihar\" title=\"Bihar\">Bihar</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Jharkhand\" title=\"Jharkhand\">Jharkhand</a> officially <a href=\"https://wikipedia.org/wiki/Bihar_Reorganisation_Act,_2000\" title=\"Bihar Reorganisation Act, 2000\">becomes</a> the 28th <a href=\"https://wikipedia.org/wiki/States_of_India\" class=\"mw-redirect\" title=\"States of India\">state of India</a>, formed from <a href=\"https://wikipedia.org/wiki/List_of_districts_of_Bihar\" title=\"List of districts of Bihar\">eighteen districts</a> of southern <a href=\"https://wikipedia.org/wiki/Bihar\" title=\"Bihar\">Bihar</a>.", "links": [{"title": "Jharkhand", "link": "https://wikipedia.org/wiki/Jharkhand"}, {"title": "Bihar Reorganisation Act, 2000", "link": "https://wikipedia.org/wiki/Bihar_Reorganisation_Act,_2000"}, {"title": "States of India", "link": "https://wikipedia.org/wiki/States_of_India"}, {"title": "List of districts of Bihar", "link": "https://wikipedia.org/wiki/List_of_districts_of_Bihar"}, {"title": "Bihar", "link": "https://wikipedia.org/wiki/Bihar"}]}, {"year": "2001", "text": "Microsoft launches the Xbox game console in North America.", "html": "2001 - <a href=\"https://wikipedia.org/wiki/Microsoft\" title=\"Microsoft\">Microsoft</a> launches the <a href=\"https://wikipedia.org/wiki/Xbox_(console)\" title=\"Xbox (console)\">Xbox</a> game console in <a href=\"https://wikipedia.org/wiki/North_America\" title=\"North America\">North America</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Microsoft\" title=\"Microsoft\">Microsoft</a> launches the <a href=\"https://wikipedia.org/wiki/Xbox_(console)\" title=\"Xbox (console)\">Xbox</a> game console in <a href=\"https://wikipedia.org/wiki/North_America\" title=\"North America\">North America</a>.", "links": [{"title": "Microsoft", "link": "https://wikipedia.org/wiki/Microsoft"}, {"title": "Xbox (console)", "link": "https://wikipedia.org/wiki/Xbox_(console)"}, {"title": "North America", "link": "https://wikipedia.org/wiki/North_America"}]}, {"year": "2002", "text": "<PERSON> becomes General Secretary of the Chinese Communist Party and a new nine-member Politburo Standing Committee is inaugurated.", "html": "2002 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> becomes <a href=\"https://wikipedia.org/wiki/General_Secretary_of_the_Chinese_Communist_Party\" title=\"General Secretary of the Chinese Communist Party\">General Secretary of the Chinese Communist Party</a> and a new nine-member <a href=\"https://wikipedia.org/wiki/Politburo_Standing_Committee\" class=\"mw-redirect\" title=\"Politburo Standing Committee\">Politburo Standing Committee</a> is inaugurated.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> becomes <a href=\"https://wikipedia.org/wiki/General_Secretary_of_the_Chinese_Communist_Party\" title=\"General Secretary of the Chinese Communist Party\">General Secretary of the Chinese Communist Party</a> and a new nine-member <a href=\"https://wikipedia.org/wiki/Politburo_Standing_Committee\" class=\"mw-redirect\" title=\"Politburo Standing Committee\">Politburo Standing Committee</a> is inaugurated.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "General Secretary of the Chinese Communist Party", "link": "https://wikipedia.org/wiki/General_Secretary_of_the_Chinese_Communist_Party"}, {"title": "Politburo Standing Committee", "link": "https://wikipedia.org/wiki/Politburo_Standing_Committee"}]}, {"year": "2003", "text": "The first day of the 2003 Istanbul bombings, in which two car bombs, targeting two synagogues, explode, kill 25 people and wound 300 more.", "html": "2003 - The first day of the <a href=\"https://wikipedia.org/wiki/2003_Istanbul_bombings\" title=\"2003 Istanbul bombings\">2003 Istanbul bombings</a>, in which two car bombs, targeting two synagogues, explode, kill 25 people and wound 300 more.", "no_year_html": "The first day of the <a href=\"https://wikipedia.org/wiki/2003_Istanbul_bombings\" title=\"2003 Istanbul bombings\">2003 Istanbul bombings</a>, in which two car bombs, targeting two synagogues, explode, kill 25 people and wound 300 more.", "links": [{"title": "2003 Istanbul bombings", "link": "https://wikipedia.org/wiki/2003_Istanbul_bombings"}]}, {"year": "2006", "text": "Al Jazeera English launches worldwide.", "html": "2006 - <i><a href=\"https://wikipedia.org/wiki/Al_Jazeera_English\" title=\"Al Jazeera English\">Al Jazeera English</a></i> launches worldwide.", "no_year_html": "<i><a href=\"https://wikipedia.org/wiki/Al_Jazeera_English\" title=\"Al Jazeera English\">Al Jazeera English</a></i> launches worldwide.", "links": [{"title": "Al Jazeera English", "link": "https://wikipedia.org/wiki/Al_Jazeera_English"}]}, {"year": "2007", "text": "Cyclone <PERSON><PERSON> hits Bangladesh, killing an estimated 5,000 people and destroying parts of the world's largest mangrove forest, the Sundarbans.", "html": "2007 - <a href=\"https://wikipedia.org/wiki/Cyclone_Sidr\" title=\"Cyclone Sidr\">Cyclone Sidr</a> hits <a href=\"https://wikipedia.org/wiki/Bangladesh\" title=\"Bangladesh\">Bangladesh</a>, killing an estimated 5,000 people and destroying parts of the world's largest <a href=\"https://wikipedia.org/wiki/Mangrove\" title=\"Mangrove\">mangrove</a> forest, the <a href=\"https://wikipedia.org/wiki/Sundarbans\" title=\"Sundarbans\">Sundarbans</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Cyclone_Sidr\" title=\"Cyclone Sidr\">Cyclone Sidr</a> hits <a href=\"https://wikipedia.org/wiki/Bangladesh\" title=\"Bangladesh\">Bangladesh</a>, killing an estimated 5,000 people and destroying parts of the world's largest <a href=\"https://wikipedia.org/wiki/Mangrove\" title=\"Mangrove\">mangrove</a> forest, the <a href=\"https://wikipedia.org/wiki/Sundarbans\" title=\"Sundarbans\">Sundarbans</a>.", "links": [{"title": "Cyclone Sidr", "link": "https://wikipedia.org/wiki/<PERSON>_Sidr"}, {"title": "Bangladesh", "link": "https://wikipedia.org/wiki/Bangladesh"}, {"title": "Mangrove", "link": "https://wikipedia.org/wiki/Mangrove"}, {"title": "Sundarbans", "link": "https://wikipedia.org/wiki/Sundarbans"}]}, {"year": "2012", "text": "<PERSON> becomes General Secretary of the Chinese Communist Party and a new seven-member Politburo Standing Committee is inaugurated.", "html": "2012 - <a href=\"https://wikipedia.org/wiki/Xi_Jinping\" title=\"Xi Jinping\"><PERSON></a> becomes <a href=\"https://wikipedia.org/wiki/General_Secretary_of_the_Chinese_Communist_Party\" title=\"General Secretary of the Chinese Communist Party\">General Secretary of the Chinese Communist Party</a> and a new seven-member <a href=\"https://wikipedia.org/wiki/Politburo_Standing_Committee\" class=\"mw-redirect\" title=\"Politburo Standing Committee\">Politburo Standing Committee</a> is inaugurated.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Xi_<PERSON>ping\" title=\"Xi Jinping\"><PERSON></a> becomes <a href=\"https://wikipedia.org/wiki/General_Secretary_of_the_Chinese_Communist_Party\" title=\"General Secretary of the Chinese Communist Party\">General Secretary of the Chinese Communist Party</a> and a new seven-member <a href=\"https://wikipedia.org/wiki/Politburo_Standing_Committee\" class=\"mw-redirect\" title=\"Politburo Standing Committee\">Politburo Standing Committee</a> is inaugurated.", "links": [{"title": "Xi <PERSON>ping", "link": "https://wikipedia.org/wiki/Xi_<PERSON>"}, {"title": "General Secretary of the Chinese Communist Party", "link": "https://wikipedia.org/wiki/General_Secretary_of_the_Chinese_Communist_Party"}, {"title": "Politburo Standing Committee", "link": "https://wikipedia.org/wiki/Politburo_Standing_Committee"}]}, {"year": "2016", "text": "Hong Kong's High Court bans elected politicians <PERSON><PERSON> and <PERSON><PERSON> from the city's Parliament.", "html": "2016 - <a href=\"https://wikipedia.org/wiki/Hong_Kong_High_Court\" class=\"mw-redirect\" title=\"Hong Kong High Court\">Hong Kong's High Court</a> bans elected politicians <a href=\"https://wikipedia.org/wiki/Yau_Wai-ching\" title=\"Yau Wai-ching\"><PERSON><PERSON> Wai-ching</a> and <a href=\"https://wikipedia.org/wiki/Baggio_Leung\" title=\"Baggio Leung\">Baggio Leung</a> from the city's <a href=\"https://wikipedia.org/wiki/Legislative_Council_of_Hong_Kong\" title=\"Legislative Council of Hong Kong\">Parliament</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Hong_Kong_High_Court\" class=\"mw-redirect\" title=\"Hong Kong High Court\">Hong Kong's High Court</a> bans elected politicians <a href=\"https://wikipedia.org/wiki/Yau_Wai-ching\" title=\"Yau Wai-ching\"><PERSON><PERSON> Wai-ching</a> and <a href=\"https://wikipedia.org/wiki/Baggio_Leung\" title=\"Baggio Leung\">Baggio Leung</a> from the city's <a href=\"https://wikipedia.org/wiki/Legislative_Council_of_Hong_Kong\" title=\"Legislative Council of Hong Kong\">Parliament</a>.", "links": [{"title": "Hong Kong High Court", "link": "https://wikipedia.org/wiki/Hong_Kong_High_Court"}, {"title": "<PERSON><PERSON>-ching", "link": "https://wikipedia.org/wiki/Yau_<PERSON><PERSON>-ching"}, {"title": "Baggio Leung", "link": "https://wikipedia.org/wiki/Ba<PERSON>_<PERSON>"}, {"title": "Legislative Council of Hong Kong", "link": "https://wikipedia.org/wiki/Legislative_Council_of_Hong_Kong"}]}, {"year": "2017", "text": "A flood a few miles outside of Athens results in the death of 25 people. ", "html": "2017 - A <a href=\"https://wikipedia.org/wiki/2017_West_Attica_floods\" title=\"2017 West Attica floods\">flood</a> a few miles outside of <a href=\"https://wikipedia.org/wiki/Athens\" title=\"Athens\">Athens</a> results in the death of 25 people. ", "no_year_html": "A <a href=\"https://wikipedia.org/wiki/2017_West_Attica_floods\" title=\"2017 West Attica floods\">flood</a> a few miles outside of <a href=\"https://wikipedia.org/wiki/Athens\" title=\"Athens\">Athens</a> results in the death of 25 people. ", "links": [{"title": "2017 West Attica floods", "link": "https://wikipedia.org/wiki/2017_West_Attica_floods"}, {"title": "Athens", "link": "https://wikipedia.org/wiki/Athens"}]}, {"year": "2020", "text": "<PERSON> wins the Turkish Grand Prix and secures his seventh drivers' title, equalling the all-time record held by <PERSON>.", "html": "2020 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> wins the <a href=\"https://wikipedia.org/wiki/2020_Turkish_Grand_Prix\" title=\"2020 Turkish Grand Prix\">Turkish Grand Prix</a> and secures his <a href=\"https://wikipedia.org/wiki/List_of_Formula_One_World_Drivers%27_Champions\" title=\"List of Formula One World Drivers' Champions\">seventh drivers' title</a>, equalling the all-time record held by <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> wins the <a href=\"https://wikipedia.org/wiki/2020_Turkish_Grand_Prix\" title=\"2020 Turkish Grand Prix\">Turkish Grand Prix</a> and secures his <a href=\"https://wikipedia.org/wiki/List_of_Formula_One_World_Drivers%27_Champions\" title=\"List of Formula One World Drivers' Champions\">seventh drivers' title</a>, equalling the all-time record held by <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "2020 Turkish Grand Prix", "link": "https://wikipedia.org/wiki/2020_Turkish_Grand_Prix"}, {"title": "List of Formula One World Drivers' Champions", "link": "https://wikipedia.org/wiki/List_of_Formula_One_World_Drivers%27_Champions"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "2022", "text": "The world population reached eight billion.", "html": "2022 - The <a href=\"https://wikipedia.org/wiki/World_population\" title=\"World population\">world population</a> reached <a href=\"https://wikipedia.org/wiki/Day_of_Eight_Billion\" title=\"Day of Eight Billion\">eight billion</a>.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/World_population\" title=\"World population\">world population</a> reached <a href=\"https://wikipedia.org/wiki/Day_of_Eight_Billion\" title=\"Day of Eight Billion\">eight billion</a>.", "links": [{"title": "World population", "link": "https://wikipedia.org/wiki/World_population"}, {"title": "Day of Eight Billion", "link": "https://wikipedia.org/wiki/Day_of_Eight_Billion"}]}], "Births": [{"year": "459", "text": "<PERSON><PERSON><PERSON>, Mayan king (d. 501)", "html": "459 - <a href=\"https://wikipedia.org/wiki/B%CA%BCutz_Aj_Sak_Chiik\" title=\"Bʼutz Aj Sak Chiik\"><PERSON>ʼ<PERSON> <PERSON><PERSON></a>, Mayan king (d. 501)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/B%CA%BCutz_Aj_Sak_Chiik\" title=\"Bʼutz Aj Sa<PERSON> Chiik\"><PERSON>ʼ<PERSON> <PERSON><PERSON></a>, Mayan king (d. 501)", "links": [{"title": "Bʼutz Aj Sa<PERSON>", "link": "https://wikipedia.org/wiki/B%CA%BCutz_Aj_Sa<PERSON>_<PERSON>ik"}]}, {"year": "1316", "text": "<PERSON>, king of France and Navarre (d. 1316)", "html": "1316 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_France\" title=\"<PERSON> I of France\"><PERSON></a>, king of France and Navarre (d. 1316)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_France\" title=\"<PERSON> of France\"><PERSON></a>, king of France and Navarre (d. 1316)", "links": [{"title": "<PERSON> of France", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_France"}]}, {"year": "1397", "text": "<PERSON>, pope of the Catholic Church (d. 1455)", "html": "1397 - <a href=\"https://wikipedia.org/wiki/Pope_<PERSON>_<PERSON>\" title=\"Pope Nicholas <PERSON>\"><PERSON></a>, pope of the Catholic Church (d. 1455)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"Pope Nicholas V\"><PERSON></a>, pope of the Catholic Church (d. 1455)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1498", "text": "<PERSON> of Austria, queen of Portugal and France (d. 1558)", "html": "1498 - <a href=\"https://wikipedia.org/wiki/Eleanor_of_Austria\" title=\"<PERSON> of Austria\"><PERSON> of Austria</a>, queen of Portugal and France (d. 1558)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Eleanor_<PERSON>_Austria\" title=\"<PERSON> of Austria\"><PERSON> of Austria</a>, queen of Portugal and France (d. 1558)", "links": [{"title": "<PERSON> of Austria", "link": "https://wikipedia.org/wiki/Eleanor_of_Austria"}]}, {"year": "1511", "text": "<PERSON>, Dutch poet and author (d. 1536)", "html": "1511 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch poet and author (d. 1536)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch poet and author (d. 1536)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1556", "text": "<PERSON>, French cardinal (d. 1618)", "html": "1556 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French cardinal (d. 1618)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French cardinal (d. 1618)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1607", "text": "<PERSON>, French author (d. 1701)", "html": "1607 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>%C3%A9ry\" title=\"<PERSON>\"><PERSON></a>, French author (d. 1701)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>%C3%A9ry\" title=\"<PERSON>\"><PERSON></a>, French author (d. 1701)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>ud%C3%A9ry"}]}, {"year": "1660", "text": "<PERSON>, German historian and orientalist (d. 1746)", "html": "1660 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German historian and orientalist (d. 1746)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German historian and orientalist (d. 1746)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1661", "text": "<PERSON>, Swiss-American settler and author (d. 1743)", "html": "1661 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, Swiss-American settler and author (d. 1743)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, Swiss-American settler and author (d. 1743)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1692", "text": "<PERSON><PERSON><PERSON>, German poet and theologian (d. 1775)", "html": "1692 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, German poet and theologian (d. 1775)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, German poet and theologian (d. 1775)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Eusebius_Amort"}]}, {"year": "1708", "text": "<PERSON>, 1st Earl of Chatham, English politician, Prime Minister of Great Britain (d. 1778)", "html": "1708 - <a href=\"https://wikipedia.org/wiki/<PERSON>,_1st_Earl_of_Chatham\" title=\"<PERSON>, 1st Earl of Chatham\"><PERSON>, 1st Earl of Chatham</a>, English politician, <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Great_Britain\" class=\"mw-redirect\" title=\"Prime Minister of Great Britain\">Prime Minister of Great Britain</a> (d. 1778)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>,_1st_Earl_of_Chatham\" title=\"<PERSON>, 1st Earl of Chatham\"><PERSON>, 1st Earl of Chatham</a>, English politician, <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Great_Britain\" class=\"mw-redirect\" title=\"Prime Minister of Great Britain\">Prime Minister of Great Britain</a> (d. 1778)", "links": [{"title": "<PERSON>, 1st Earl of Chatham", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_1st_Earl_of_Chatham"}, {"title": "Prime Minister of Great Britain", "link": "https://wikipedia.org/wiki/Prime_Minister_of_Great_Britain"}]}, {"year": "1738", "text": "<PERSON>, German-English astronomer and composer (d. 1822)", "html": "1738 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German-English astronomer and composer (d. 1822)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German-English astronomer and composer (d. 1822)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1741", "text": "<PERSON>, Swiss poet and physiognomist (d. 1801)", "html": "1741 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swiss poet and physiognomist (d. 1801)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swiss poet and physiognomist (d. 1801)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1746", "text": "<PERSON>, French-Canadian poet, playwright, and composer (d. 1809)", "html": "1746 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French-Canadian poet, playwright, and composer (d. 1809)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French-Canadian poet, playwright, and composer (d. 1809)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1757", "text": "<PERSON>, Danish surgeon, botanist, and academic (d. 1830)", "html": "1757 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Danish surgeon, botanist, and academic (d. 1830)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Danish surgeon, botanist, and academic (d. 1830)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1776", "text": "<PERSON>, Mexican journalist and author (d. 1827)", "html": "1776 - <a href=\"https://wikipedia.org/wiki/Jos%C3%A9_Joaqu%C3%ADn_Fern%C3%A1<PERSON><PERSON>_<PERSON>_<PERSON><PERSON>\" title=\"<PERSON><PERSON>\"><PERSON></a>, Mexican journalist and author (d. 1827)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Jos%C3%A9_Joaqu%C3%ADn_Fern%C3%A1ndez_<PERSON>_<PERSON><PERSON>\" title=\"<PERSON>\"><PERSON></a>, Mexican journalist and author (d. 1827)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Jos%C3%A9_Joaqu%C3%ADn_Fern%C3%<PERSON><PERSON><PERSON>_<PERSON>_<PERSON><PERSON>"}]}, {"year": "1784", "text": "<PERSON><PERSON><PERSON><PERSON>, French husband of <PERSON><PERSON><PERSON> of Württemberg (d. 1860)", "html": "1784 - <a href=\"https://wikipedia.org/wiki/J%C3%A9r%C3%B4<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, French husband of <a href=\"https://wikipedia.org/wiki/Catharina_of_W%C3%BCrttemberg\" title=\"Cat<PERSON>na of Württemberg\"><PERSON><PERSON><PERSON> of Württemberg</a> (d. 1860)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/J%C3%A9r%C3%B4<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, French husband of <a href=\"https://wikipedia.org/wiki/Catharina_of_W%C3%BCrttemberg\" title=\"<PERSON><PERSON>na of Württemberg\"><PERSON><PERSON><PERSON> of Württemberg</a> (d. 1860)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/J%C3%A9r%C3%B4me_Bonaparte"}, {"title": "Cat<PERSON>na of Württemberg", "link": "https://wikipedia.org/wiki/Catharina_of_W%C3%BCrttemberg"}]}, {"year": "1791", "text": "<PERSON>, German lawyer, jurist, and politician (d. 1869)", "html": "1791 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German lawyer, jurist, and politician (d. 1869)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German lawyer, jurist, and politician (d. 1869)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1793", "text": "<PERSON>, French mathematician and academic (d. 1880)", "html": "1793 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French mathematician and academic (d. 1880)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French mathematician and academic (d. 1880)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1849", "text": "<PERSON>, American astronomer and educator (d. 1934)", "html": "1849 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American astronomer and educator (d. 1934)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American astronomer and educator (d. 1934)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "1852", "text": "<PERSON><PERSON><PERSON><PERSON>, Egyptian ruler (d. 1892)", "html": "1852 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON> <PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Egyptian ruler (d. 1892)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Egyptian ruler (d. 1892)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>w<PERSON><PERSON>_<PERSON>"}]}, {"year": "1859", "text": "<PERSON>, Norwegian businessman and politician, 11th Prime Minister of Norway (d. 1960)", "html": "1859 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Norwegian businessman and politician, 11th <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Norway\" title=\"Prime Minister of Norway\">Prime Minister of Norway</a> (d. 1960)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Norwegian businessman and politician, 11th <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Norway\" title=\"Prime Minister of Norway\">Prime Minister of Norway</a> (d. 1960)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}, {"title": "Prime Minister of Norway", "link": "https://wikipedia.org/wiki/Prime_Minister_of_Norway"}]}, {"year": "1862", "text": "<PERSON><PERSON><PERSON>, German novelist, poet, and playwright, Nobel Prize laureate (d. 1946)", "html": "1862 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, German novelist, poet, and playwright, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Literature\" title=\"Nobel Prize in Literature\">Nobel Prize</a> laureate (d. 1946)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, German novelist, poet, and playwright, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Literature\" title=\"Nobel Prize in Literature\">Nobel Prize</a> laureate (d. 1946)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>n"}, {"title": "Nobel Prize in Literature", "link": "https://wikipedia.org/wiki/Nobel_Prize_in_Literature"}]}, {"year": "1865", "text": "<PERSON>, Australian politician, 22nd Premier of Tasmania (d. 1932)", "html": "1865 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(Australian_politician)\" title=\"<PERSON> (Australian politician)\"><PERSON></a>, Australian politician, 22nd <a href=\"https://wikipedia.org/wiki/Premier_of_Tasmania\" title=\"Premier of Tasmania\">Premier of Tasmania</a> (d. 1932)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(Australian_politician)\" title=\"<PERSON> (Australian politician)\"><PERSON></a>, Australian politician, 22nd <a href=\"https://wikipedia.org/wiki/Premier_of_Tasmania\" title=\"Premier of Tasmania\">Premier of Tasmania</a> (d. 1932)", "links": [{"title": "<PERSON> (Australian politician)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(Australian_politician)"}, {"title": "Premier of Tasmania", "link": "https://wikipedia.org/wiki/Premier_of_Tasmania"}]}, {"year": "1866", "text": "<PERSON><PERSON><PERSON>, Indian lawyer, social reformer and writer (d. 1954)", "html": "1866 - <a href=\"https://wikipedia.org/wiki/Cornelia_Sorabji\" title=\"Cornelia Sorabji\">Corne<PERSON> Sorabji</a>, Indian lawyer, social reformer and writer (d. 1954)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Cornelia_Sorabji\" title=\"Cornelia Sorabji\">Corne<PERSON>ji</a>, Indian lawyer, social reformer and writer (d. 1954)", "links": [{"title": "Cornelia <PERSON>rabji", "link": "https://wikipedia.org/wiki/Cornelia_Sorabji"}]}, {"year": "1867", "text": "<PERSON>, German polyglot (d. 1930)", "html": "1867 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German <a href=\"https://wikipedia.org/wiki/Polyglotism\" class=\"mw-redirect\" title=\"Polyglotism\">polyglot</a> (d. 1930)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German <a href=\"https://wikipedia.org/wiki/Polyglotism\" class=\"mw-redirect\" title=\"Polyglotism\">polyglot</a> (d. 1930)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Polyglotism", "link": "https://wikipedia.org/wiki/Polyglotism"}]}, {"year": "1868", "text": "<PERSON>, Romanian biologist, zoologist, and explorer (d. 1947)", "html": "1868 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C8%9B%C4%83\" title=\"<PERSON>\"><PERSON></a>, Romanian biologist, zoologist, and explorer (d. 1947)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C8%9B%C4%83\" title=\"<PERSON>\"><PERSON></a>, Romanian biologist, zoologist, and explorer (d. 1947)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>%C8%9B%C4%83"}]}, {"year": "1873", "text": "<PERSON>, American physician and academic (d. 1945)", "html": "1873 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American physician and academic (d. 1945)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American physician and academic (d. 1945)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1874", "text": "<PERSON><PERSON>, Greek runner (d. 1941)", "html": "1874 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Greek runner (d. 1941)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Greek runner (d. 1941)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1874", "text": "<PERSON>, Danish zoologist and physiologist, Nobel Prize laureate (d. 1949)", "html": "1874 - <a href=\"https://wikipedia.org/wiki/August_<PERSON>\" title=\"August <PERSON>\">August <PERSON></a>, Danish zoologist and physiologist, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Physiology_or_Medicine\" title=\"Nobel Prize in Physiology or Medicine\">Nobel Prize</a> laureate (d. 1949)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/August_<PERSON>\" title=\"August <PERSON>\">August <PERSON></a>, Danish zoologist and physiologist, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Physiology_or_Medicine\" title=\"Nobel Prize in Physiology or Medicine\">Nobel Prize</a> laureate (d. 1949)", "links": [{"title": "August <PERSON>", "link": "https://wikipedia.org/wiki/August_K<PERSON>h"}, {"title": "Nobel Prize in Physiology or Medicine", "link": "https://wikipedia.org/wiki/Nobel_Prize_in_Physiology_or_Medicine"}]}, {"year": "1879", "text": "<PERSON>, American actor (d. 1953)", "html": "1879 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor (d. 1953)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor (d. 1953)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1881", "text": "<PERSON>, American journalist and author (d. 1960)", "html": "1881 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, American journalist and author (d. 1960)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, American journalist and author (d. 1960)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1882", "text": "<PERSON>, Austrian-American lawyer and jurist (d. 1965)", "html": "1882 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Austrian-American lawyer and jurist (d. 1965)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Austrian-American lawyer and jurist (d. 1965)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1886", "text": "<PERSON>, French-Egyptian philosopher and author (d. 1951)", "html": "1886 - <a href=\"https://wikipedia.org/wiki/Ren%C3%A9_Gu%C3%A9non\" title=\"<PERSON>\"><PERSON></a>, French-Egyptian philosopher and author (d. 1951)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Ren%C3%A9_Gu%C3%A9non\" title=\"<PERSON>\"><PERSON></a>, French-Egyptian philosopher and author (d. 1951)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Ren%C3%A9_Gu%C3%A9non"}]}, {"year": "1887", "text": "<PERSON>, American poet, critic, and translator (d. 1972)", "html": "1887 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American poet, critic, and translator (d. 1972)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American poet, critic, and translator (d. 1972)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1887", "text": "<PERSON>, American painter and educator (d. 1986)", "html": "1887 - <a href=\"https://wikipedia.org/wiki/Georgia_O%27Keeffe\" title=\"Georgia O'Keeffe\">Georgia O<PERSON></a>, American painter and educator (d. 1986)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Georgia_O%27Keeffe\" title=\"Georgia O'<PERSON>effe\">Georgia O<PERSON></a>, American painter and educator (d. 1986)", "links": [{"title": "Georgia O'Keeffe", "link": "https://wikipedia.org/wiki/Georgia_O%27Keeffe"}]}, {"year": "1888", "text": "<PERSON><PERSON>, American pianist and composer (d. 1958)", "html": "1888 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American pianist and composer (d. 1958)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American pianist and composer (d. 1958)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1890", "text": "<PERSON><PERSON>, English author and educator (d. 1969)", "html": "1890 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> C<PERSON>\"><PERSON><PERSON></a>, English author and educator (d. 1969)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> Crompton\"><PERSON><PERSON></a>, English author and educator (d. 1969)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1891", "text": "<PERSON><PERSON>, American businessman and politician, 11th United States Secretary of Commerce (d. 1986)", "html": "1891 - <a href=\"https://wikipedia.org/wiki/W<PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> <PERSON><PERSON>\"><PERSON><PERSON> <PERSON></a>, American businessman and politician, 11th <a href=\"https://wikipedia.org/wiki/United_States_Secretary_of_Commerce\" title=\"United States Secretary of Commerce\">United States Secretary of Commerce</a> (d. 1986)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/W<PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> <PERSON>\"><PERSON><PERSON> <PERSON></a>, American businessman and politician, 11th <a href=\"https://wikipedia.org/wiki/United_States_Secretary_of_Commerce\" title=\"United States Secretary of Commerce\">United States Secretary of Commerce</a> (d. 1986)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/W<PERSON>_<PERSON><PERSON>_<PERSON>"}, {"title": "United States Secretary of Commerce", "link": "https://wikipedia.org/wiki/United_States_Secretary_of_Commerce"}]}, {"year": "1891", "text": "<PERSON>, German field marshal (d. 1944)", "html": "1891 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German field marshal (d. 1944)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German field marshal (d. 1944)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1892", "text": "<PERSON>, American actress (d. 1964)", "html": "1892 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress (d. 1964)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress (d. 1964)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1895", "text": "Grand Duchess <PERSON> of Russia (d. 1918)", "html": "1895 - <a href=\"https://wikipedia.org/wiki/Grand_Duchess_<PERSON>_<PERSON>_of_Russia\" title=\"Grand Duchess <PERSON> of Russia\">Grand Duchess <PERSON> of Russia</a> (d. 1918)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Grand_Duchess_<PERSON>_<PERSON>_of_Russia\" title=\"Grand Duchess <PERSON> of Russia\">Grand Duchess <PERSON> of Russia</a> (d. 1918)", "links": [{"title": "Grand Duchess <PERSON> of Russia", "link": "https://wikipedia.org/wiki/Grand_Duchess_<PERSON>_<PERSON>_of_Russia"}]}, {"year": "1895", "text": "<PERSON><PERSON>, Polish journalist, poet, and playwright (d. 1976)", "html": "1895 - <a href=\"https://wikipedia.org/wiki/Antoni_S%C5%82onimski\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Polish journalist, poet, and playwright (d. 1976)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Antoni_S%C5%82onimski\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Polish journalist, poet, and playwright (d. 1976)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Antoni_S%C5%82onimski"}]}, {"year": "1896", "text": "<PERSON>, English businessman (d. 1967)", "html": "1896 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English businessman (d. 1967)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English businessman (d. 1967)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1897", "text": "<PERSON><PERSON><PERSON>, Welsh journalist and politician, Secretary of State for Health (d. 1960)", "html": "1897 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON> <PERSON>\"><PERSON><PERSON><PERSON></a>, Welsh journalist and politician, <a href=\"https://wikipedia.org/wiki/Secretary_of_State_for_Health\" class=\"mw-redirect\" title=\"Secretary of State for Health\">Secretary of State for Health</a> (d. 1960)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Welsh journalist and politician, <a href=\"https://wikipedia.org/wiki/Secretary_of_State_for_Health\" class=\"mw-redirect\" title=\"Secretary of State for Health\">Secretary of State for Health</a> (d. 1960)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON><PERSON>"}, {"title": "Secretary of State for Health", "link": "https://wikipedia.org/wiki/Secretary_of_State_for_Health"}]}, {"year": "1897", "text": "<PERSON><PERSON><PERSON><PERSON>, English author and critic (d. 1988)", "html": "1897 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, English author and critic (d. 1988)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON> Sitwell\"><PERSON><PERSON><PERSON><PERSON></a>, English author and critic (d. 1988)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1899", "text": "<PERSON><PERSON><PERSON>, Estonian-American soldier and diplomat, Estonian Minister of War (d. 1990)", "html": "1899 - <a href=\"https://wikipedia.org/wiki/A<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Estonian-American soldier and diplomat, <a href=\"https://wikipedia.org/wiki/Minister_of_Defence_(Estonia)\" title=\"Minister of Defence (Estonia)\">Estonian Minister of War</a> (d. 1990)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Estonian-American soldier and diplomat, <a href=\"https://wikipedia.org/wiki/Minister_of_Defence_(Estonia)\" title=\"Minister of Defence (Estonia)\">Estonian Minister of War</a> (d. 1990)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Av<PERSON>_<PERSON>"}, {"title": "Minister of Defence (Estonia)", "link": "https://wikipedia.org/wiki/Minister_of_Defence_(Estonia)"}]}, {"year": "1903", "text": "<PERSON><PERSON><PERSON>, New Zealand cricketer and coach (d. 1974)", "html": "1903 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, New Zealand cricketer and coach (d. 1974)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, New Zealand cricketer and coach (d. 1974)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>ie_<PERSON>mpster"}]}, {"year": "1905", "text": "<PERSON><PERSON><PERSON>, Italian conductor and composer (d. 1980)", "html": "1905 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Italian conductor and composer (d. 1980)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Italian conductor and composer (d. 1980)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>"}]}, {"year": "1906", "text": "<PERSON>, American general and politician (d. 1990)", "html": "1906 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American general and politician (d. 1990)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American general and politician (d. 1990)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1907", "text": "<PERSON>, German colonel (d. 1944)", "html": "1907 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German colonel (d. 1944)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German colonel (d. 1944)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1908", "text": "<PERSON>, Italian engineer and businessman, founded <PERSON><PERSON><PERSON> (d. 1979)", "html": "1908 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian engineer and businessman, founded <a href=\"https://wikipedia.org/wiki/Abarth\" title=\"Abarth\"><PERSON><PERSON><PERSON></a> (d. 1979)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian engineer and businessman, founded <a href=\"https://wikipedia.org/wiki/Abarth\" title=\"Abar<PERSON>\"><PERSON><PERSON><PERSON></a> (d. 1979)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/A<PERSON>th"}]}, {"year": "1912", "text": "<PERSON>, Estonian physicist and academic (d. 2010)", "html": "1912 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Estonian physicist and academic (d. 2010)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Estonian physicist and academic (d. 2010)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1912", "text": "<PERSON>, Japanese-Korean colonel (d. 1945)", "html": "1912 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, Japanese-Korean colonel (d. 1945)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, Japanese-Korean colonel (d. 1945)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1913", "text": "<PERSON>, Australian footballer and coach (d. 2003)", "html": "1913 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian footballer and coach (d. 2003)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian footballer and coach (d. 2003)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1913", "text": "<PERSON>, Belgian journalist and poet (d. 2005)", "html": "1913 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Belgian journalist and poet (d. 2005)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Belgian journalist and poet (d. 2005)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1914", "text": "<PERSON><PERSON> <PERSON><PERSON>, Indian lawyer and judge (d. 2014)", "html": "1914 - <a href=\"https://wikipedia.org/wiki/V<PERSON>_<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON> <PERSON><PERSON>\">V<PERSON> <PERSON><PERSON></a>, Indian lawyer and judge (d. 2014)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/V<PERSON>_<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON> <PERSON><PERSON>\"><PERSON><PERSON> <PERSON><PERSON></a>, Indian lawyer and judge (d. 2014)", "links": [{"title": "<PERSON><PERSON> <PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1916", "text": "<PERSON><PERSON>, Barbadian nurse and politician, 7th Governor-General of Barbados (d. 1995)", "html": "1916 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Barbadian nurse and politician, 7th <a href=\"https://wikipedia.org/wiki/Governor-General_of_Barbados\" title=\"Governor-General of Barbados\">Governor-General of Barbados</a> (d. 1995)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Barbadian nurse and politician, 7th <a href=\"https://wikipedia.org/wiki/Governor-General_of_Barbados\" title=\"Governor-General of Barbados\">Governor-General of Barbados</a> (d. 1995)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}, {"title": "Governor-General of Barbados", "link": "https://wikipedia.org/wiki/Governor-General_of_Barbados"}]}, {"year": "1916", "text": "<PERSON>, Mexican-American voice actor, animator, director, and producer (d. 2008)", "html": "1916 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Mexican-American voice actor, animator, director, and producer (d. 2008)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Mexican-American voice actor, animator, director, and producer (d. 2008)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1919", "text": "<PERSON>, American singer and actress (d. 2007)", "html": "1919 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer and actress (d. 2007)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer and actress (d. 2007)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1919", "text": "<PERSON>, American judge and television personality (d. 2017)", "html": "1919 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American judge and television personality (d. 2017)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American judge and television personality (d. 2017)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1920", "text": "<PERSON><PERSON><PERSON>, Greek actor, director, and screenwriter (d. 1999)", "html": "1920 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Greek actor, director, and screenwriter (d. 1999)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Greek actor, director, and screenwriter (d. 1999)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1922", "text": "<PERSON>, German juggler (d. 2004)", "html": "1922 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German juggler (d. 2004)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German juggler (d. 2004)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1922", "text": "<PERSON>, American biochemist and academic (d. 2019)", "html": "1922 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American biochemist and academic (d. 2019)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American biochemist and academic (d. 2019)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1922", "text": "<PERSON>, Italian director and screenwriter (d. 2015)", "html": "1922 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> R<PERSON>\"><PERSON></a>, Italian director and screenwriter (d. 2015)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> R<PERSON>i\"><PERSON></a>, Italian director and screenwriter (d. 2015)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Francesco_<PERSON>"}]}, {"year": "1923", "text": "<PERSON><PERSON><PERSON>, Vietnamese composer, poet, and painter (d. 1995)", "html": "1923 - <a href=\"https://wikipedia.org/wiki/V%C4%83n_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Vietnamese composer, poet, and painter (d. 1995)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/V%C4%83n_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Vietnamese composer, poet, and painter (d. 1995)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/V%C4%83n_Cao"}]}, {"year": "1923", "text": "<PERSON>, Polish-Brazilian businessman and philanthropist, founded Casas Bahia (d. 2014)", "html": "1923 - <a href=\"https://wikipedia.org/wiki/<PERSON>(businessman)\" title=\"<PERSON> (businessman)\"><PERSON></a>, Polish-Brazilian businessman and philanthropist, founded <a href=\"https://wikipedia.org/wiki/Casas_Bahia\" title=\"Casas Bahia\">Casas Bahia</a> (d. 2014)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>(businessman)\" title=\"<PERSON> (businessman)\"><PERSON></a>, Polish-Brazilian businessman and philanthropist, founded <a href=\"https://wikipedia.org/wiki/Casas_Bahia\" title=\"Casas Bahia\">Casas Bahia</a> (d. 2014)", "links": [{"title": "<PERSON> (businessman)", "link": "https://wikipedia.org/wiki/<PERSON>(businessman)"}, {"title": "Casas Bahia", "link": "https://wikipedia.org/wiki/Casas_Bahia"}]}, {"year": "1924", "text": "<PERSON><PERSON><PERSON>, Italian composer and conductor (d. 2013)", "html": "1924 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Italian composer and conductor (d. 2013)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Italian composer and conductor (d. 2013)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1925", "text": "<PERSON>, American lawyer, politician, and diplomat, 12th White House Chief of Staff (d. 2014)", "html": "1925 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer, politician, and diplomat, 12th <a href=\"https://wikipedia.org/wiki/White_House_Chief_of_Staff\" title=\"White House Chief of Staff\">White House Chief of Staff</a> (d. 2014)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer, politician, and diplomat, 12th <a href=\"https://wikipedia.org/wiki/White_House_Chief_of_Staff\" title=\"White House Chief of Staff\">White House Chief of Staff</a> (d. 2014)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "White House Chief of Staff", "link": "https://wikipedia.org/wiki/White_House_Chief_of_Staff"}]}, {"year": "1926", "text": "<PERSON>, American author and academic (d. 1990)", "html": "1926 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(writer)\" title=\"<PERSON> (writer)\"><PERSON></a>, American author and academic (d. 1990)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(writer)\" title=\"<PERSON> (writer)\"><PERSON></a>, American author and academic (d. 1990)", "links": [{"title": "<PERSON> (writer)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(writer)"}]}, {"year": "1927", "text": "<PERSON>, New Zealand politician, 30th Prime Minister of New Zealand (d. 1995)", "html": "1927 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, New Zealand politician, 30th <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_New_Zealand\" title=\"Prime Minister of New Zealand\">Prime Minister of New Zealand</a> (d. 1995)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, New Zealand politician, 30th <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_New_Zealand\" title=\"Prime Minister of New Zealand\">Prime Minister of New Zealand</a> (d. 1995)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Prime Minister of New Zealand", "link": "https://wikipedia.org/wiki/Prime_Minister_of_New_Zealand"}]}, {"year": "1929", "text": "<PERSON>, American actor, singer, and producer (d. 2021)", "html": "1929 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor, singer, and producer (d. 2021)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor, singer, and producer (d. 2021)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1929", "text": "<PERSON>, American singer (d. 1968)", "html": "1929 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer (d. 1968)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer (d. 1968)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1930", "text": "<PERSON><PERSON> <PERSON><PERSON>,  English novelist, short story writer, and essayist (d. 2009)", "html": "1930 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"J<PERSON> <PERSON><PERSON>\"><PERSON><PERSON> <PERSON><PERSON></a>, English novelist, short story writer, and essayist (d. 2009)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> <PERSON><PERSON>\"><PERSON><PERSON> <PERSON><PERSON></a>, English novelist, short story writer, and essayist (d. 2009)", "links": [{"title": "<PERSON><PERSON> <PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "1930", "text": "<PERSON><PERSON>, American lawyer and politician, 15th Governor of Utah (d. 2015)", "html": "1930 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American lawyer and politician, 15th <a href=\"https://wikipedia.org/wiki/Governor_of_Utah\" class=\"mw-redirect\" title=\"Governor of Utah\">Governor of Utah</a> (d. 2015)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American lawyer and politician, 15th <a href=\"https://wikipedia.org/wiki/Governor_of_Utah\" class=\"mw-redirect\" title=\"Governor of Utah\">Governor of Utah</a> (d. 2015)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}, {"title": "Governor of Utah", "link": "https://wikipedia.org/wiki/Governor_of_Utah"}]}, {"year": "1931", "text": "<PERSON>, American actor, singer, and lawyer (d. 2013)", "html": "1931 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>(actor)\" title=\"<PERSON> (actor)\"><PERSON></a>, American actor, singer, and lawyer (d. 2013)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>(actor)\" title=\"<PERSON> (actor)\"><PERSON></a>, American actor, singer, and lawyer (d. 2013)", "links": [{"title": "<PERSON> (actor)", "link": "https://wikipedia.org/wiki/<PERSON>(actor)"}]}, {"year": "1931", "text": "<PERSON><PERSON>, Kenyan economist and politician, 3rd President of Kenya (d. 2022)", "html": "1931 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> Ki<PERSON>\"><PERSON><PERSON></a>, Kenyan economist and politician, 3rd <a href=\"https://wikipedia.org/wiki/President_of_Kenya\" title=\"President of Kenya\">President of Kenya</a> (d. 2022)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> Ki<PERSON>\"><PERSON><PERSON></a>, Kenyan economist and politician, 3rd <a href=\"https://wikipedia.org/wiki/President_of_Kenya\" title=\"President of Kenya\">President of Kenya</a> (d. 2022)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Mwai_Kibaki"}, {"title": "President of Kenya", "link": "https://wikipedia.org/wiki/President_of_Kenya"}]}, {"year": "1931", "text": "<PERSON>, Congolese politician, President of the Republic of the Congo (d. 2020)", "html": "1931 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Congolese politician, <a href=\"https://wikipedia.org/wiki/President_of_the_Republic_of_the_Congo\" class=\"mw-redirect\" title=\"President of the Republic of the Congo\">President of the Republic of the Congo</a> (d. 2020)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Congolese politician, <a href=\"https://wikipedia.org/wiki/President_of_the_Republic_of_the_Congo\" class=\"mw-redirect\" title=\"President of the Republic of the Congo\">President of the Republic of the Congo</a> (d. 2020)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "President of the Republic of the Congo", "link": "https://wikipedia.org/wiki/President_of_the_Republic_of_the_Congo"}]}, {"year": "1932", "text": "<PERSON><PERSON>, English singer-songwriter and actress", "html": "1932 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, English singer-songwriter and actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, English singer-songwriter and actress", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1932", "text": "<PERSON>, American singer (d. 1972)", "html": "1932 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer (d. 1972)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer (d. 1972)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1932", "text": "<PERSON>, American philosopher, author, and academic", "html": "1932 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American philosopher, author, and academic", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American philosopher, author, and academic", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Alvin_Plantinga"}]}, {"year": "1933", "text": "<PERSON>, American actress (d. 2001)", "html": "1933 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress (d. 2001)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress (d. 2001)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1933", "text": "<PERSON>, American scholar and author (d. 2011)", "html": "1933 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(scholar)\" title=\"<PERSON> (scholar)\"><PERSON></a>, American scholar and author (d. 2011)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(scholar)\" title=\"<PERSON> (scholar)\"><PERSON></a>, American scholar and author (d. 2011)", "links": [{"title": "<PERSON> (scholar)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(scholar)"}]}, {"year": "1934", "text": "<PERSON>, American actress and author (d. 2022)", "html": "1934 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress and author (d. 2022)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress and author (d. 2022)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1934", "text": "<PERSON>, English pianist and composer (d. 2023)", "html": "1934 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(musician)\" title=\"<PERSON> (musician)\"><PERSON></a>, English pianist and composer (d. 2023)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(musician)\" title=\"<PERSON> (musician)\"><PERSON></a>, English pianist and composer (d. 2023)", "links": [{"title": "<PERSON> (musician)", "link": "https://wikipedia.org/wiki/<PERSON>_(musician)"}]}, {"year": "1935", "text": "<PERSON><PERSON>, American basketball player (d. 2016)", "html": "1935 - <a href=\"https://wikipedia.org/wiki/Nera_White\" title=\"Nera White\"><PERSON><PERSON></a>, American basketball player (d. 2016)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Nera_White\" title=\"Nera White\"><PERSON><PERSON></a>, American basketball player (d. 2016)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Nera_White"}]}, {"year": "1936", "text": "<PERSON><PERSON> <PERSON><PERSON>, American race car driver (d. 2003)", "html": "1936 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> <PERSON><PERSON>\"><PERSON><PERSON> <PERSON><PERSON></a>, American race car driver (d. 2003)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> <PERSON><PERSON>\"><PERSON><PERSON> <PERSON><PERSON></a>, American race car driver (d. 2003)", "links": [{"title": "<PERSON><PERSON> <PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "1936", "text": "<PERSON>, German singer-songwriter and guitarist", "html": "1936 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German singer-songwriter and guitarist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German singer-songwriter and guitarist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1936", "text": "<PERSON>, Indian-Canadian journalist and publisher (d. 1998)", "html": "1936 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Indian-Canadian journalist and publisher (d. 1998)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Indian-Canadian journalist and publisher (d. 1998)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1937", "text": "<PERSON>, American singer-songwriter (d. 1968)", "html": "1937 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON> Willie <PERSON>\"><PERSON> <PERSON></a>, American singer-songwriter (d. 1968)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON> Willie <PERSON>\"><PERSON> <PERSON></a>, American singer-songwriter (d. 1968)", "links": [{"title": "<PERSON> <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1939", "text": "<PERSON>, English footballer and manager", "html": "1939 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English footballer and manager", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English footballer and manager", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1939", "text": "<PERSON><PERSON><PERSON>, American actor and screenwriter (d. 2021)", "html": "1939 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American actor and screenwriter (d. 2021)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American actor and screenwriter (d. 2021)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1939", "text": "<PERSON><PERSON><PERSON><PERSON><PERSON>-<PERSON><PERSON>, Finnish physician and parapsychologist (d. 2015)", "html": "1939 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>-<PERSON><PERSON>_<PERSON>-<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON><PERSON></a>, Finnish physician and parapsychologist (d. 2015)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>-<PERSON><PERSON>_<PERSON>-<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON><PERSON></a>, Finnish physician and parapsychologist (d. 2015)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON><PERSON>-<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>-<PERSON><PERSON>_<PERSON>-<PERSON>"}]}, {"year": "1940", "text": "<PERSON>, Italian fashion designer (d. 2024)", "html": "1940 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian fashion designer (d. 2024)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian fashion designer (d. 2024)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1940", "text": "<PERSON>, American CIA technical operations officer (d. 2019)", "html": "1940 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American CIA technical operations officer (d. 2019)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American CIA technical operations officer (d. 2019)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1940", "text": "<PERSON><PERSON>, Danish actor and screenwriter", "html": "1940 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Danish actor and screenwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Danish actor and screenwriter", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1940", "text": "<PERSON>, English singer-songwriter, guitarist, and physician", "html": "1940 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English singer-songwriter, guitarist, and physician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English singer-songwriter, guitarist, and physician", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1940", "text": "<PERSON>, American actor", "html": "1940 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1941", "text": "<PERSON>, English singer-songwriter, bass player, and producer", "html": "1941 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English singer-songwriter, bass player, and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English singer-songwriter, bass player, and producer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1941", "text": "<PERSON>, American author and illustrator", "html": "1941 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author and illustrator", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author and illustrator", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1942", "text": "<PERSON>, Argentinian-Israeli pianist and conductor", "html": "1942 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Argentinian-Israeli pianist and conductor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Argentinian-Israeli pianist and conductor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1945", "text": "<PERSON>, Australian director, producer, and screenwriter", "html": "1945 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian director, producer, and screenwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian director, producer, and screenwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1945", "text": "<PERSON>, American actor and singer", "html": "1945 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor and singer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor and singer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1945", "text": "<PERSON><PERSON><PERSON><PERSON><PERSON>, Norwegian-Swedish singer", "html": "1945 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>-<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON><PERSON></a>, Norwegian-Swedish singer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>-<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON>-<PERSON><PERSON></a>, Norwegian-Swedish singer", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Anni-<PERSON><PERSON>_<PERSON>"}]}, {"year": "1946", "text": "<PERSON><PERSON><PERSON>, Greek basketball player", "html": "1946 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Greek basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Greek basketball player", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1947", "text": "<PERSON>, American basketball player", "html": "1947 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1947", "text": "<PERSON>, Sri Lankan cardinal", "html": "1947 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Sri Lankan cardinal", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Sri Lankan cardinal", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1947", "text": "<PERSON>, American politician and diplomat, 21st United States Ambassador to the United Nations (d. 2023)", "html": "1947 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American politician and diplomat, 21st <a href=\"https://wikipedia.org/wiki/United_States_Ambassador_to_the_United_Nations\" class=\"mw-redirect\" title=\"United States Ambassador to the United Nations\">United States Ambassador to the United Nations</a> (d. 2023)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American politician and diplomat, 21st <a href=\"https://wikipedia.org/wiki/United_States_Ambassador_to_the_United_Nations\" class=\"mw-redirect\" title=\"United States Ambassador to the United Nations\">United States Ambassador to the United Nations</a> (d. 2023)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "United States Ambassador to the United Nations", "link": "https://wikipedia.org/wiki/United_States_Ambassador_to_the_United_Nations"}]}, {"year": "1947", "text": "<PERSON>, Australian journalist and sportscaster", "html": "1947 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian journalist and sportscaster", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian journalist and sportscaster", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1948", "text": "<PERSON>, Malaysian fashion designer[page needed]", "html": "1948 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Malaysian fashion designer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Malaysian fashion designer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1948", "text": "<PERSON><PERSON><PERSON>, Jr., Filipino journalist, lawyer, politician and diplomat", "html": "1948 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>,_Jr.\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON>, Jr.\"><PERSON><PERSON><PERSON>, Jr.</a>, Filipino journalist, lawyer, politician and diplomat", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>,_Jr.\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON>, Jr.\"><PERSON><PERSON><PERSON>, Jr.</a>, Filipino journalist, lawyer, politician and diplomat", "links": [{"title": "<PERSON><PERSON><PERSON>, Jr.", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>,_Jr."}]}, {"year": "1950", "text": "<PERSON><PERSON>, German lawyer and politician, 16th Mayor of Marburg", "html": "1950 - <a href=\"https://wikipedia.org/wiki/E<PERSON>_Vaupel\" title=\"Egon Vaupel\"><PERSON><PERSON></a>, German lawyer and politician, 16th <a href=\"https://wikipedia.org/wiki/Mayor_of_Marburg\" class=\"mw-redirect\" title=\"Mayor of Marburg\">Mayor of Marburg</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/E<PERSON>_Vaupel\" title=\"E<PERSON> Vaupel\"><PERSON><PERSON></a>, German lawyer and politician, 16th <a href=\"https://wikipedia.org/wiki/Mayor_of_Marburg\" class=\"mw-redirect\" title=\"Mayor of Marburg\">Mayor of Marburg</a>", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Egon_Vaupel"}, {"title": "Mayor of Marburg", "link": "https://wikipedia.org/wiki/Mayor_of_Marburg"}]}, {"year": "1951", "text": "<PERSON>, American actress, singer, and producer", "html": "1951 - <a href=\"https://wikipedia.org/wiki/Beverly_D%27Angelo\" title=\"<PERSON>Angelo\"><PERSON></a>, American actress, singer, and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Beverly_D%27Angelo\" title=\"Beverly D'Angelo\"><PERSON></a>, American actress, singer, and producer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Beverly_D%27Angelo"}]}, {"year": "1952", "text": "<PERSON>, American journalist, historian, and author", "html": "1952 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American journalist, historian, and author", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American journalist, historian, and author", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1952", "text": "<PERSON>, American wrestler (d. 2011)", "html": "1952 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American wrestler (d. 2011)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American wrestler (d. 2011)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1953", "text": "<PERSON>, American R&B singer-songwriter and arranger", "html": "1953 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%27Neal\" title=\"<PERSON>\"><PERSON></a>, American R&amp;B singer-songwriter and arranger", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%27Neal\" title=\"<PERSON>\"><PERSON></a>, American R&amp;B singer-songwriter and arranger", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Alexander_O%27Neal"}]}, {"year": "1953", "text": "<PERSON>, American actor, director, and producer", "html": "1953 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor, director, and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor, director, and producer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1954", "text": "<PERSON>, American director and producer", "html": "1954 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American director and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American director and producer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1954", "text": "<PERSON>, British politician", "html": "1954 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, British politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, British politician", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_Dent_Coad"}]}, {"year": "1954", "text": "<PERSON><PERSON><PERSON><PERSON>, Polish journalist and politician, 3rd President of Poland", "html": "1954 - <a href=\"https://wikipedia.org/wiki/Aleksander_Kwa%C5%9Bniewski\" title=\"Aleksander <PERSON>śniewski\"><PERSON><PERSON><PERSON><PERSON></a>, Polish journalist and politician, 3rd <a href=\"https://wikipedia.org/wiki/President_of_Poland\" title=\"President of Poland\">President of Poland</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Aleksander_Kwa%C5%9Bnie<PERSON>\" title=\"Aleksan<PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Polish journalist and politician, 3rd <a href=\"https://wikipedia.org/wiki/President_of_Poland\" title=\"President of Poland\">President of Poland</a>", "links": [{"title": "Aleksan<PERSON>", "link": "https://wikipedia.org/wiki/Aleksander_Kwa%C5%9Bniewski"}, {"title": "President of Poland", "link": "https://wikipedia.org/wiki/President_of_Poland"}]}, {"year": "1954", "text": "<PERSON>, American singer-songwriter, guitarist, and producer", "html": "1954 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(musician)\" title=\"<PERSON> (musician)\"><PERSON></a>, American singer-songwriter, guitarist, and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(musician)\" title=\"<PERSON> (musician)\"><PERSON></a>, American singer-songwriter, guitarist, and producer", "links": [{"title": "<PERSON> (musician)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(musician)"}]}, {"year": "1954", "text": "<PERSON>, American R&B, disco, and rock drummer (d. 2003)", "html": "1954 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(drummer)\" title=\"<PERSON> (drummer)\"><PERSON></a>, American R&amp;B, disco, and rock drummer (d. 2003)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(drummer)\" title=\"<PERSON> (drummer)\"><PERSON></a>, American R&amp;B, disco, and rock drummer (d. 2003)", "links": [{"title": "<PERSON> (drummer)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(drummer)"}]}, {"year": "1955", "text": "<PERSON>, English pop singer-songwriter and percussionist", "html": "1955 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English pop singer-songwriter and percussionist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English pop singer-songwriter and percussionist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1956", "text": "<PERSON>, American guitarist and producer", "html": "1956 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American guitarist and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American guitarist and producer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1957", "text": "<PERSON>, Australian comedian and actor", "html": "1957 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(comedian)\" title=\"<PERSON> (comedian)\"><PERSON></a>, Australian comedian and actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>(comedian)\" title=\"<PERSON> (comedian)\"><PERSON></a>, Australian comedian and actor", "links": [{"title": "<PERSON> (comedian)", "link": "https://wikipedia.org/wiki/<PERSON>_(comedian)"}]}, {"year": "1957", "text": "<PERSON>, American guitarist and composer", "html": "1957 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American guitarist and composer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American guitarist and composer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1957", "text": "<PERSON>, American historian and educator", "html": "1957 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American historian and educator", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American historian and educator", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1957", "text": "<PERSON>, German footballer and manager", "html": "1957 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German footballer and manager", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German footballer and manager", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1958", "text": "<PERSON>-<PERSON>, Australian actor and director", "html": "1958 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>-<PERSON>\" title=\"<PERSON>\"><PERSON>-<PERSON></a>, Australian actor and director", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>-<PERSON>\" title=\"<PERSON>-<PERSON>\"><PERSON>-<PERSON></a>, Australian actor and director", "links": [{"title": "<PERSON>-<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>-<PERSON>"}]}, {"year": "1958", "text": "<PERSON><PERSON>, Chinese lawyer and businesswoman", "html": "1958 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Chinese lawyer and businesswoman", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Chinese lawyer and businesswoman", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>"}]}, {"year": "1958", "text": "<PERSON>, British politician", "html": "1958 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, British politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, British politician", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1959", "text": "<PERSON><PERSON><PERSON>, English author", "html": "1959 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, English author", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, English author", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/T<PERSON><PERSON>_<PERSON>"}]}, {"year": "1960", "text": "<PERSON>, English broadcaster", "html": "1960 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English broadcaster", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English broadcaster", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_Airey"}]}, {"year": "1961", "text": "<PERSON>, New Zealand rugby league player", "html": "1961 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, New Zealand rugby league player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, New Zealand rugby league player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1962", "text": "<PERSON>, American basketball player and educator", "html": "1962 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player and educator", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player and educator", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1962", "text": "<PERSON>, American comedian, actress, and producer", "html": "1962 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Judy Gold\"><PERSON></a>, American comedian, actress, and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Judy Gold\"><PERSON></a>, American comedian, actress, and producer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1963", "text": "<PERSON>, English tennis player and television host", "html": "1963 - <a href=\"https://wikipedia.org/wiki/Andrew_<PERSON>\" title=\"Andrew Castle\"><PERSON></a>, English tennis player and television host", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Andrew_Castle\" title=\"Andrew Castle\"><PERSON></a>, English tennis player and television host", "links": [{"title": "Andrew Castle", "link": "https://wikipedia.org/wiki/Andrew_<PERSON>"}]}, {"year": "1963", "text": "<PERSON>, Lebanese-Australian rugby league player and sportscaster", "html": "1963 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Lebanese-Australian rugby league player and sportscaster", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Lebanese-Australian rugby league player and sportscaster", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1963", "text": "<PERSON>, American actor", "html": "1963 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>%27<PERSON><PERSON><PERSON>_(actor)\" title=\"<PERSON> (actor)\"><PERSON></a>, American actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>%27<PERSON><PERSON><PERSON>_(actor)\" title=\"<PERSON> (actor)\"><PERSON></a>, American actor", "links": [{"title": "<PERSON> (actor)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>%27<PERSON><PERSON><PERSON>_(actor)"}]}, {"year": "1964", "text": "<PERSON><PERSON><PERSON>, Greek footballer and manager", "html": "1964 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>posporis\" title=\"<PERSON><PERSON><PERSON> Aposporis\"><PERSON><PERSON><PERSON></a>, Greek footballer and manager", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>oris\" title=\"<PERSON><PERSON><PERSON> Aposporis\"><PERSON><PERSON><PERSON></a>, Greek footballer and manager", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>eli<PERSON>_Aposporis"}]}, {"year": "1964", "text": "<PERSON>, Russian footballer, coach, and manager (d. 2011)", "html": "1964 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian footballer, coach, and manager (d. 2011)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian footballer, coach, and manager (d. 2011)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1964", "text": "<PERSON><PERSON><PERSON>, Estonian basketball player and coach", "html": "1964 - <a href=\"https://wikipedia.org/wiki/Tiit_Sokk\" title=\"Tiit Sokk\">T<PERSON><PERSON></a>, Estonian basketball player and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Tiit_Sokk\" title=\"Tiit Sokk\">Tii<PERSON></a>, Estonian basketball player and coach", "links": [{"title": "Tiit Sokk", "link": "https://wikipedia.org/wiki/Tiit_Sokk"}]}, {"year": "1965", "text": "<PERSON>, English snooker player", "html": "1965 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English snooker player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English snooker player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1965", "text": "<PERSON>, German swimmer", "html": "1965 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German swimmer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German swimmer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1966", "text": "<PERSON>, American actress", "html": "1966 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1967", "text": "<PERSON>, American basketball player and sportscaster", "html": "1967 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player and sportscaster", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player and sportscaster", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1967", "text": "<PERSON>, American computer scientist, roboticist, and academic", "html": "1967 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American computer scientist, roboticist, and academic", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American computer scientist, roboticist, and academic", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1967", "text": "<PERSON>-<PERSON>, American rapper and actor", "html": "1967 - <a href=\"https://wikipedia.org/wiki/E-40\" title=\"E-40\">E-40</a>, American rapper and actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/E-40\" title=\"E-40\">E-40</a>, American rapper and actor", "links": [{"title": "E-40", "link": "https://wikipedia.org/wiki/E-40"}]}, {"year": "1967", "text": "<PERSON>, English footballer (d. 2013)", "html": "1967 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(footballer,_born_1967)\" title=\"<PERSON> (footballer, born 1967)\"><PERSON></a>, English footballer (d. 2013)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(footballer,_born_1967)\" title=\"<PERSON> (footballer, born 1967)\"><PERSON></a>, English footballer (d. 2013)", "links": [{"title": "<PERSON> (footballer, born 1967)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>(footballer,_born_1967)"}]}, {"year": "1967", "text": "<PERSON>, French director, producer, and screenwriter", "html": "1967 - <a href=\"https://wikipedia.org/wiki/Fran%C3%A7ois_Oz<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French director, producer, and screenwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Fran%C3%A7ois_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French director, producer, and screenwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Fran%C3%A7ois_<PERSON>on"}]}, {"year": "1967", "text": "<PERSON>, Uruguayan footballer and manager", "html": "1967 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Po<PERSON>\"><PERSON></a>, Uruguayan footballer and manager", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Poye<PERSON>\"><PERSON></a>, Uruguayan footballer and manager", "links": [{"title": "Gus <PERSON>", "link": "https://wikipedia.org/wiki/Gus_Poyet"}]}, {"year": "1967", "text": "<PERSON>, New Zealand rugby player", "html": "1967 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, New Zealand rugby player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, New Zealand rugby player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1968", "text": "<PERSON><PERSON><PERSON> <PERSON>stard, American rapper and producer (d. 2004)", "html": "1968 - <a href=\"https://wikipedia.org/wiki/Ol%27_Dirty_Bastard\" title=\"Ol' Dirty Bastard\">O<PERSON>' Dirty Bastard</a>, American rapper and producer (d. 2004)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Ol%27_Dirty_Bastard\" title=\"Ol' Dirty Bastard\">Ol' Dirty Bastard</a>, American rapper and producer (d. 2004)", "links": [{"title": "Ol' <PERSON> Bastard", "link": "https://wikipedia.org/wiki/Ol%27_Dirty_Bastard"}]}, {"year": "1968", "text": "<PERSON><PERSON>, Italian director, producer, and screenwriter", "html": "1968 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Italian director, producer, and screenwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Italian director, producer, and screenwriter", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1968", "text": "<PERSON><PERSON><PERSON>, Filipino journalist and politician", "html": "1968 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>asi%C3%B1o\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Filipino journalist and politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>%C3%B1o\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Filipino journalist and politician", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Teodoro_Casi%C3%B1o"}]}, {"year": "1968", "text": "<PERSON>, American singer-songwriter, guitarist, and producer", "html": "1968 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter, guitarist, and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter, guitarist, and producer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1968", "text": "<PERSON><PERSON>, German footballer and manager", "html": "1968 - <a href=\"https://wikipedia.org/wiki/Uwe_R%C3%B6sler\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, German footballer and manager", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Uwe_R%C3%B6sler\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, German footballer and manager", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Uwe_R%C3%B6sler"}]}, {"year": "1970", "text": "<PERSON><PERSON>, Croatian footballer and coach", "html": "1970 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>%C4%8Di%C4%87\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Croatian footballer and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>%C4%8Di%C4%87\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Croatian footballer and coach", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Ilija_Ara%C4%8Di%C4%87"}]}, {"year": "1970", "text": "<PERSON>, American singer-songwriter and guitarist", "html": "1970 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and guitarist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and guitarist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1970", "text": "<PERSON>, Georgian-Ukrainian academic and politician, 19th Ukrainian Minister of Healthcare", "html": "1970 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Georgian-Ukrainian academic and politician, 19th <a href=\"https://wikipedia.org/wiki/Minister_of_Healthcare_(Ukraine)\" title=\"Minister of Healthcare (Ukraine)\">Ukrainian Minister of Healthcare</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Georgian-Ukrainian academic and politician, 19th <a href=\"https://wikipedia.org/wiki/Minister_of_Healthcare_(Ukraine)\" title=\"Minister of Healthcare (Ukraine)\">Ukrainian Minister of Healthcare</a>", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}, {"title": "Minister of Healthcare (Ukraine)", "link": "https://wikipedia.org/wiki/Minister_of_Healthcare_(Ukraine)"}]}, {"year": "1970", "text": "<PERSON>, Cameroonian footballer", "html": "1970 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%27Boma\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, Cameroonian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_M%27Boma\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, Cameroonian footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Patrick_M%27Boma"}]}, {"year": "1971", "text": "<PERSON>, American actor", "html": "1971 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1971", "text": "<PERSON>, German footballer", "html": "1971 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1972", "text": "<PERSON><PERSON>, English-American actor", "html": "1972 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, English-American actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, English-American actor", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1973", "text": "<PERSON>, American actress", "html": "1973 - <a href=\"https://wikipedia.org/wiki/Sydney_Tamii<PERSON>_<PERSON>\" title=\"Sydney Tamiia Poitier\"><PERSON> <PERSON><PERSON><PERSON></a>, American actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Sydney_Tamii<PERSON>_<PERSON>\" title=\"Sydney Tamiia Poitier\"><PERSON> <PERSON><PERSON><PERSON></a>, American actress", "links": [{"title": "Sydney Tamiia <PERSON>", "link": "https://wikipedia.org/wiki/Sydney_<PERSON>ii<PERSON>_<PERSON>er"}]}, {"year": "1973", "text": "<PERSON><PERSON><PERSON><PERSON>, English cricketer", "html": "1973 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, English cricketer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, English cricketer", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1974", "text": "<PERSON>, Canadian singer-songwriter, guitarist, and producer", "html": "1974 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian singer-songwriter, guitarist, and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian singer-songwriter, guitarist, and producer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Chad_<PERSON>"}]}, {"year": "1975", "text": "<PERSON>, English fashion designer", "html": "1975 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English fashion designer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English fashion designer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1975", "text": "<PERSON><PERSON>, Canadian ice hockey player", "html": "1975 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_Tremblay\" title=\"Yannick Tremblay\"><PERSON><PERSON></a>, Canadian ice hockey player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_Tremblay\" title=\"Yannick Tremblay\"><PERSON><PERSON></a>, Canadian ice hockey player", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>remblay"}]}, {"year": "1975", "text": "<PERSON>, Croatian footballer", "html": "1975 - <a href=\"https://wikipedia.org/wiki/<PERSON>_%C5%BDivkovi%C4%87\" title=\"<PERSON>\"><PERSON></a>, Croatian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_%C5%BDivkovi%C4%87\" title=\"<PERSON>\"><PERSON></a>, Croatian footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_%C5%BDivkovi%C4%87"}]}, {"year": "1976", "text": "<PERSON>, American comedian, actor, and stuntman", "html": "1976 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American comedian, actor, and stuntman", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American comedian, actor, and stuntman", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1976", "text": "<PERSON><PERSON>, French actress", "html": "1976 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, French actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, French actress", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1976", "text": "<PERSON><PERSON>, Indonesian comedian and actor", "html": "1976 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_(comedian)\" title=\"<PERSON><PERSON> (comedian)\"><PERSON><PERSON></a>, Indonesian comedian and actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_(comedian)\" title=\"<PERSON><PERSON> (comedian)\"><PERSON><PERSON></a>, Indonesian comedian and actor", "links": [{"title": "<PERSON><PERSON> (comedian)", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_(comedian)"}]}, {"year": "1977", "text": "<PERSON>, Slovak ice hockey player", "html": "1977 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Slovak ice hockey player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Slovak ice hockey player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1977", "text": "<PERSON>, Australian politician, 40th Premier of Queensland", "html": "1977 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian politician, 40th <a href=\"https://wikipedia.org/wiki/Premier_of_Queensland\" title=\"Premier of Queensland\">Premier of Queensland</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian politician, 40th <a href=\"https://wikipedia.org/wiki/Premier_of_Queensland\" title=\"Premier of Queensland\">Premier of Queensland</a>", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Premier of Queensland", "link": "https://wikipedia.org/wiki/Premier_of_Queensland"}]}, {"year": "1977", "text": "<PERSON>, American actor", "html": "1977 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(actor)\" title=\"<PERSON> (actor)\"><PERSON></a>, American actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>(actor)\" title=\"<PERSON> (actor)\"><PERSON></a>, American actor", "links": [{"title": "<PERSON> (actor)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>(actor)"}]}, {"year": "1977", "text": "<PERSON>, English businessman", "html": "1977 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English businessman", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English businessman", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1977", "text": "<PERSON><PERSON>, American football player", "html": "1977 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American football player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American football player", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1978", "text": "<PERSON>, American football player", "html": "1978 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> W<PERSON>\"><PERSON></a>, American football player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> W<PERSON>\"><PERSON></a>, American football player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1979", "text": "<PERSON>, American football player and coach", "html": "1979 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player and coach", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1979", "text": "<PERSON><PERSON>, Spanish footballer", "html": "1979 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Spanish footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Spanish footballer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>"}]}, {"year": "1979", "text": "<PERSON>, Australian cyclist", "html": "1979 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian cyclist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian cyclist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1980", "text": "<PERSON>, American singer-songwriter and actor", "html": "1980 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Young\"><PERSON></a>, American singer-songwriter and actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Young\"><PERSON></a>, American singer-songwriter and actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_Young"}]}, {"year": "1981", "text": "<PERSON>, American football player", "html": "1981 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1981", "text": "<PERSON><PERSON>, Mexican golfer", "html": "1981 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Mexican golfer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Mexican golfer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1982", "text": "<PERSON><PERSON> <PERSON><PERSON>, American football player", "html": "1982 - <a href=\"https://wikipedia.org/wiki/D<PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> <PERSON><PERSON>\"><PERSON><PERSON> <PERSON><PERSON></a>, American football player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> <PERSON><PERSON>\"><PERSON><PERSON> <PERSON><PERSON></a>, American football player", "links": [{"title": "<PERSON><PERSON> <PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "1982", "text": "<PERSON>, Japanese actress", "html": "1982 - <a href=\"https://wikipedia.org/wiki/Rio_Hirai\" title=\"Rio Hirai\"><PERSON></a>, Japanese actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Rio_Hirai\" title=\"Rio Hirai\"><PERSON></a>, Japanese actress", "links": [{"title": "Rio Hirai", "link": "https://wikipedia.org/wiki/Rio_Hirai"}]}, {"year": "1982", "text": "<PERSON>, American football player", "html": "1982 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1982", "text": "<PERSON>, German rugby player", "html": "1982 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German rugby player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German rugby player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1982", "text": "<PERSON><PERSON><PERSON>, Australian swimmer", "html": "1982 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Australian swimmer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Australian swimmer", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1982", "text": "<PERSON><PERSON>, American football player", "html": "1982 - <a href=\"https://wikipedia.org/wiki/Lo<PERSON>_<PERSON>\" title=\"<PERSON><PERSON> Tatupu\"><PERSON><PERSON></a>, American football player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> Tatupu\"><PERSON><PERSON></a>, American football player", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>pu"}]}, {"year": "1982", "text": "<PERSON><PERSON>, Nigerian footballer", "html": "1982 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Nigerian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Nigerian footballer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1983", "text": "<PERSON>, Gibraltarian runner", "html": "1983 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Gibraltarian runner", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Gibraltarian runner", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1983", "text": "<PERSON>, English actress", "html": "1983 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actress", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1983", "text": "<PERSON><PERSON><PERSON><PERSON>, Serbian basketball player", "html": "1983 - <a href=\"https://wikipedia.org/wiki/Aleksandar_Pavlovi%C4%87_(basketball)\" title=\"<PERSON><PERSON><PERSON><PERSON> (basketball)\"><PERSON><PERSON><PERSON><PERSON></a>, Serbian basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Aleksandar_Pavlovi%C4%87_(basketball)\" title=\"<PERSON><PERSON><PERSON><PERSON> (basketball)\"><PERSON><PERSON><PERSON><PERSON></a>, Serbian basketball player", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON> (basketball)", "link": "https://wikipedia.org/wiki/Aleksandar_Pavlovi%C4%87_(basketball)"}]}, {"year": "1983", "text": "<PERSON>, Spanish tennis player", "html": "1983 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Spanish tennis player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Spanish tennis player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1983", "text": "<PERSON>, Dutch footballer and coach", "html": "1983 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch footballer and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch footballer and coach", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1984", "text": "<PERSON> <PERSON>, American actor and producer", "html": "1984 - <a href=\"https://wikipedia.org/wiki/Asia_<PERSON>_<PERSON>\" title=\"Asia Kate Dillon\"><PERSON></a>, American actor and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Asia_<PERSON>_<PERSON>\" title=\"Asia Kate Dillon\"><PERSON></a>, American actor and producer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Asia_<PERSON>_<PERSON>"}]}, {"year": "1985", "text": "<PERSON>, American model", "html": "1985 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American model", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American model", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1985", "text": "<PERSON><PERSON><PERSON>, American basketball player", "html": "1985 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American basketball player", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1985", "text": "<PERSON>, Welsh footballer", "html": "1985 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Welsh footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Welsh footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1986", "text": "<PERSON>, Tobagonian-American actor", "html": "1986 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Tobagonian-American actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Tobagonian-American actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1986", "text": "<PERSON><PERSON>, American football player", "html": "1986 - <a href=\"https://wikipedia.org/wiki/Coye_Francies\" title=\"Coye Francies\"><PERSON><PERSON>an<PERSON></a>, American football player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Coye_Francies\" title=\"Coye Francies\"><PERSON><PERSON>an<PERSON></a>, American football player", "links": [{"title": "Coye Francies", "link": "https://wikipedia.org/wiki/Coye_Francies"}]}, {"year": "1986", "text": "<PERSON><PERSON>, Indian tennis player", "html": "1986 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Indian tennis player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Indian tennis player", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1986", "text": "<PERSON>, American singer-songwriter", "html": "1986 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1987", "text": "<PERSON>, Spanish basketball player", "html": "1987 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Spanish basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Spanish basketball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1988", "text": "<PERSON><PERSON><PERSON><PERSON><PERSON>, American rapper, songwriter, and producer", "html": "1988 - <a href=\"https://wikipedia.org/wiki/B.o.B\" title=\"B.o.B\">B.o.B</a>, American rapper, songwriter, and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/B.o.B\" title=\"B.o.B\">B.o.B</a>, American rapper, songwriter, and producer", "links": [{"title": "B.o.B", "link": "https://wikipedia.org/wiki/B.o.B"}]}, {"year": "1988", "text": "<PERSON><PERSON><PERSON><PERSON>, American-Turkish basketball player", "html": "1988 - <a href=\"https://wikipedia.org/wiki/Quanit<PERSON>_<PERSON>\" title=\"<PERSON>uan<PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, American-Turkish basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Quanit<PERSON>_<PERSON>\" title=\"<PERSON>uanit<PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, American-Turkish basketball player", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Quanit<PERSON>_<PERSON>"}]}, {"year": "1988", "text": "<PERSON>, French rugby player", "html": "1988 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French rugby player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French rugby player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Morgan_<PERSON>"}]}, {"year": "1988", "text": "<PERSON>, English rugby player", "html": "1988 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English rugby player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English rugby player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>s"}]}, {"year": "1989", "text": "<PERSON><PERSON>, Filipino singer", "html": "1989 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Filipino singer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Filipino singer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>y"}]}, {"year": "1991", "text": "<PERSON><PERSON>, French footballer", "html": "1991 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, French footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, French footballer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1991", "text": "<PERSON><PERSON><PERSON>, American actress", "html": "1991 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American actress", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1992", "text": "<PERSON>, American baseball player", "html": "1992 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1992", "text": "<PERSON>, Italian skier", "html": "1992 - <a href=\"https://wikipedia.org/wiki/Sofia_Goggia\" title=\"Sofia Goggia\"><PERSON></a>, Italian skier", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Sofia_Goggia\" title=\"Sofia Goggia\"><PERSON></a>, Italian skier", "links": [{"title": "Sofia Goggia", "link": "https://wikipedia.org/wiki/Sofia_Goggia"}]}, {"year": "1992", "text": "<PERSON><PERSON>, Japanese singer", "html": "1992 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Japanese singer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Japanese singer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1992", "text": "<PERSON><PERSON>, Chilean tennis player", "html": "1992 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Chilean tennis player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> Se<PERSON>l\"><PERSON><PERSON></a>, Chilean tennis player", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>l"}]}, {"year": "1992", "text": "<PERSON>, American baseball player", "html": "1992 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Trevor Story\"><PERSON></a>, American baseball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Trevor Story\"><PERSON></a>, American baseball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1992", "text": "<PERSON>, Austrian footballer", "html": "1992 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Austrian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Austrian footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1993", "text": "<PERSON><PERSON>, American football player", "html": "1993 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American football player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American football player", "links": [{"title": "<PERSON>k <PERSON>tead", "link": "https://wikipedia.org/wiki/Arik_Armstead"}]}, {"year": "1993", "text": "<PERSON>, Argentine footballer", "html": "1993 - <a href=\"https://wikipedia.org/wiki/Paulo_Dybal<PERSON>\" title=\"<PERSON> D<PERSON>\"><PERSON></a>, Argentine footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Paulo_Dybala\" title=\"<PERSON> Dybala\"><PERSON></a>, Argentine footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Paulo_Dybala"}]}, {"year": "1993", "text": "<PERSON><PERSON>, Japanese actress and singer", "html": "1993 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> I<PERSON>\"><PERSON><PERSON></a>, Japanese actress and singer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> I<PERSON>\"><PERSON><PERSON></a>, Japanese actress and singer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>rie"}]}, {"year": "1993", "text": "<PERSON><PERSON><PERSON>, Belarusian rhythmic gymnast", "html": "1993 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_Stan<PERSON>\" title=\"<PERSON><PERSON><PERSON> Stan<PERSON>\"><PERSON><PERSON><PERSON></a>, Belarusian rhythmic gymnast", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Belarusian rhythmic gymnast", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Melitina_Staniouta"}]}, {"year": "1994", "text": "<PERSON><PERSON><PERSON>, Russian tennis player", "html": "1994 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Russian tennis player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Russian tennis player", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON><PERSON><PERSON>"}]}, {"year": "1994", "text": "<PERSON>, American actress and model", "html": "1994 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress and model", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress and model", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1994", "text": "<PERSON>, Australian rugby league player", "html": "1994 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian rugby league player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian rugby league player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1995", "text": "<PERSON>, American swimmer", "html": "1995 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American swimmer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American swimmer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1995", "text": "<PERSON><PERSON><PERSON>, Dominican-American basketball player", "html": "1995 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Dominican-American basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Dominican-American basketball player", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>"}]}, {"year": "1996", "text": "<PERSON>, South Korean footballer", "html": "1996 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(footballer)\" title=\"<PERSON> (footballer)\"><PERSON></a>, South Korean footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(footballer)\" title=\"<PERSON> (footballer)\"><PERSON></a>, South Korean footballer", "links": [{"title": "<PERSON> (footballer)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>-<PERSON>_(footballer)"}]}, {"year": "1997", "text": "<PERSON>, Spanish tennis player", "html": "1997 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Spanish tennis player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Spanish tennis player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2001", "text": "<PERSON><PERSON><PERSON>, American basketball player", "html": "2001 - <a href=\"https://wikipedia.org/wiki/TyT<PERSON>_<PERSON>_Jr.\" title=\"Ty<PERSON><PERSON> Jr.\"><PERSON><PERSON><PERSON> Jr.</a>, American basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/TyT<PERSON>_<PERSON>_Jr.\" title=\"Ty<PERSON><PERSON> Jr.\"><PERSON><PERSON><PERSON> Jr.</a>, American basketball player", "links": [{"title": "<PERSON><PERSON><PERSON> Jr.", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>y_Washington_Jr."}]}], "Deaths": [{"year": "165 BCE", "text": "<PERSON><PERSON><PERSON>, Jewish resistance leader", "html": "165 BCE - 165 BCE - <a href=\"https://wikipedia.org/wiki/Mattathias\" title=\"Matt<PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Jewish resistance leader", "no_year_html": "165 BCE - <a href=\"https://wikipedia.org/wiki/Matt<PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Jewish resistance leader", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Mattathias"}]}, {"year": "621", "text": "<PERSON><PERSON>, Breton bishop and saint", "html": "621 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_(saint)\" title=\"<PERSON><PERSON> (saint)\"><PERSON><PERSON></a>, Breton bishop and saint", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_(saint)\" title=\"<PERSON><PERSON> (saint)\"><PERSON><PERSON></a>, Breton bishop and saint", "links": [{"title": "<PERSON><PERSON> (saint)", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_(saint)"}]}, {"year": "655", "text": "<PERSON><PERSON><PERSON><PERSON>, king of East Anglia", "html": "655 - <a href=\"https://wikipedia.org/wiki/%C3%86thelhere_of_East_Anglia\" title=\"<PERSON><PERSON><PERSON><PERSON> of East Anglia\"><PERSON><PERSON><PERSON><PERSON></a>, king of East Anglia", "no_year_html": "<a href=\"https://wikipedia.org/wiki/%C3%86thelhere_of_East_Anglia\" title=\"<PERSON><PERSON><PERSON><PERSON> of East Anglia\"><PERSON><PERSON><PERSON><PERSON></a>, king of East Anglia", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON> of East Anglia", "link": "https://wikipedia.org/wiki/%C3%86thelhere_of_East_Anglia"}]}, {"year": "655", "text": "<PERSON><PERSON> of Mercia, king of Mercia", "html": "655 - <a href=\"https://wikipedia.org/wiki/Penda_of_Mercia\" title=\"<PERSON><PERSON> of Mercia\"><PERSON><PERSON> of Mercia</a>, king of Mercia", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Penda_of_Mercia\" title=\"<PERSON><PERSON> of Mercia\"><PERSON><PERSON> of Mercia</a>, king of Mercia", "links": [{"title": "Penda of Mercia", "link": "https://wikipedia.org/wiki/Penda_of_Mercia"}]}, {"year": "1037", "text": "<PERSON><PERSON> <PERSON>, French nobleman (b. 983)", "html": "1037 - <a href=\"https://wikipedia.org/wiki/Odo_II,_Count_of_Blois\" title=\"<PERSON><PERSON> <PERSON>, Count of Blois\"><PERSON><PERSON> <PERSON></a>, French nobleman (b. 983)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Odo_II,_Count_of_Blois\" title=\"<PERSON><PERSON> <PERSON>, Count of Blois\"><PERSON><PERSON> <PERSON></a>, French nobleman (b. 983)", "links": [{"title": "<PERSON><PERSON> <PERSON>, Count of Blois", "link": "https://wikipedia.org/wiki/Odo_II,_Count_<PERSON>_<PERSON><PERSON><PERSON>"}]}, {"year": "1136", "text": "<PERSON>, margrave of Austria (b. 1073)", "html": "1136 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Mar<PERSON>_of_Austria\" title=\"<PERSON>, Margrave of Austria\"><PERSON></a>, margrave of Austria (b. 1073)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_<PERSON><PERSON>_of_Austria\" title=\"<PERSON>, Margrave of Austria\"><PERSON></a>, margrave of Austria (b. 1073)", "links": [{"title": "<PERSON>, Margrave of Austria", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_<PERSON><PERSON>_of_Austria"}]}, {"year": "1194", "text": "<PERSON>, countess of Flanders", "html": "1194 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Countess_of_Flanders\" title=\"<PERSON>, Countess of Flanders\"><PERSON></a>, countess of Flanders", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Countess_of_Flanders\" title=\"<PERSON>, Countess of Flanders\"><PERSON></a>, countess of Flanders", "links": [{"title": "<PERSON>, Countess of Flanders", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Countess_of_Flanders"}]}, {"year": "1226", "text": "<PERSON> of Isenberg, German nobleman (b. 1193)", "html": "1226 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_Isenberg\" title=\"<PERSON> of Isenberg\"><PERSON> of Isenberg</a>, German nobleman (b. 1193)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_Isenberg\" title=\"<PERSON> of Isenberg\"><PERSON> of Isenberg</a>, German nobleman (b. 1193)", "links": [{"title": "<PERSON> of Isenberg", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1280", "text": "<PERSON><PERSON>, German bishop, theologian, and philosopher (b. 1193)", "html": "1280 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, German bishop, theologian, and philosopher (b. 1193)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, German bishop, theologian, and philosopher (b. 1193)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1347", "text": "<PERSON> of Urgell, Spanish nobleman (b. 1321)", "html": "1347 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Urgell\" class=\"mw-redirect\" title=\"<PERSON> of Urgell\"><PERSON> of Urgell</a>, Spanish nobleman (b. 1321)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Urgell\" class=\"mw-redirect\" title=\"<PERSON> of Urgell\"><PERSON> of Urgell</a>, Spanish nobleman (b. 1321)", "links": [{"title": "<PERSON> Urgell", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1351", "text": "<PERSON> of P<PERSON>rt, duchess of Austria", "html": "1351 - <a href=\"https://wikipedia.org/wiki/<PERSON>_of_P<PERSON>\" title=\"<PERSON> of Pfirt\"><PERSON> of P<PERSON>rt</a>, duchess of Austria", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_of_<PERSON>\" title=\"<PERSON> of Pfirt\"><PERSON> of P<PERSON>rt</a>, duchess of Austria", "links": [{"title": "<PERSON> of Pfirt", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1379", "text": "<PERSON>, duke of Bavaria", "html": "1379 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Duke_of_Bavaria\" title=\"<PERSON>, Duke of Bavaria\"><PERSON></a>, duke of Bavaria", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Duke_of_Bavaria\" title=\"<PERSON>, Duke of Bavaria\"><PERSON></a>, duke of Bavaria", "links": [{"title": "<PERSON>, Duke of Bavaria", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Duke_of_Bavaria"}]}, {"year": "1463", "text": "<PERSON>, Italian nobleman", "html": "1463 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, Italian nobleman", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, Italian nobleman", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1527", "text": "<PERSON> York, English princess (b. 1479)", "html": "1527 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_York\" title=\"<PERSON> of York\"><PERSON> York</a>, English princess (b. 1479)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Catherine_<PERSON>_York\" title=\"<PERSON> York\"><PERSON> York</a>, English princess (b. 1479)", "links": [{"title": "Catherine of York", "link": "https://wikipedia.org/wiki/Catherine_<PERSON>_York"}]}, {"year": "1579", "text": "<PERSON><PERSON><PERSON>, Hungarian preacher, founder of the Unitarian Church of Transylvania (b. 1510)", "html": "1579 - <a href=\"https://wikipedia.org/wiki/Ferenc_D%C3%A1vid\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Hungarian preacher, founder of the <a href=\"https://wikipedia.org/wiki/Unitarian_Church_of_Transylvania\" title=\"Unitarian Church of Transylvania\">Unitarian Church of Transylvania</a> (b. 1510)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Ferenc_D%C3%A1vid\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Hungarian preacher, founder of the <a href=\"https://wikipedia.org/wiki/Unitarian_Church_of_Transylvania\" title=\"Unitarian Church of Transylvania\">Unitarian Church of Transylvania</a> (b. 1510)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Ferenc_D%C3%A1vid"}, {"title": "Unitarian Church of Transylvania", "link": "https://wikipedia.org/wiki/Unitarian_Church_of_Transylvania"}]}, {"year": "1628", "text": "<PERSON><PERSON><PERSON>, Paraguayan missionary and martyr (b. 1576)", "html": "1628 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>%C3%A1lez_de_Santa_Cruz\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON> Santa Cruz\"><PERSON><PERSON><PERSON> Cruz</a>, Paraguayan missionary and martyr (b. 1576)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>%C3%A1lez_de_Santa_Cruz\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON> Cruz\"><PERSON><PERSON><PERSON> Cruz</a>, Paraguayan missionary and martyr (b. 1576)", "links": [{"title": "<PERSON><PERSON><PERSON> Cruz", "link": "https://wikipedia.org/wiki/Roque_Gonz%C3%A1lez_de_Santa_Cruz"}]}, {"year": "1630", "text": "<PERSON>, German astronomer and mathematician (b. 1571)", "html": "1630 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German astronomer and mathematician (b. 1571)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German astronomer and mathematician (b. 1571)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1670", "text": "<PERSON>, Czech bishop, philosopher, and educator (b. 1592)", "html": "1670 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Czech bishop, philosopher, and educator (b. 1592)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Czech bishop, philosopher, and educator (b. 1592)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1691", "text": "<PERSON><PERSON><PERSON>, Dutch painter (b. 1620)", "html": "1691 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Dutch painter (b. 1620)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Dutch painter (b. 1620)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1706", "text": "<PERSON><PERSON><PERSON>, Tibetan dalai lama (b. 1683)", "html": "1706 - <a href=\"https://wikipedia.org/wiki/6th_Dalai_Lama\" title=\"6th Dalai Lama\"><PERSON><PERSON><PERSON></a>, Tibetan dalai lama (b. 1683)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/6th_Dalai_Lama\" title=\"6th Dalai Lama\"><PERSON><PERSON><PERSON></a>, Tibetan dalai lama (b. 1683)", "links": [{"title": "6th <PERSON><PERSON> Lama", "link": "https://wikipedia.org/wiki/6th_<PERSON><PERSON>_<PERSON>"}]}, {"year": "1712", "text": "<PERSON>, 4th Duke of Hamilton, Scottish general and politician, Lord Lieutenant of Lancashire (b. 1658)", "html": "1712 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_4th_Duke_<PERSON>_Hamilton\" title=\"<PERSON>, 4th Duke of Hamilton\"><PERSON>, 4th Duke of Hamilton</a>, Scottish general and politician, <a href=\"https://wikipedia.org/wiki/Lord_Lieutenant_of_Lancashire\" title=\"Lord Lieutenant of Lancashire\">Lord Lieutenant of Lancashire</a> (b. 1658)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>,_4th_Duke_<PERSON>_<PERSON>\" title=\"<PERSON>, 4th Duke of Hamilton\"><PERSON>, 4th Duke of Hamilton</a>, Scottish general and politician, <a href=\"https://wikipedia.org/wiki/Lord_Lieutenant_of_Lancashire\" title=\"Lord Lieutenant of Lancashire\">Lord Lieutenant of Lancashire</a> (b. 1658)", "links": [{"title": "<PERSON>, 4th Duke of Hamilton", "link": "https://wikipedia.org/wiki/<PERSON>,_4th_<PERSON>_<PERSON>_<PERSON>"}, {"title": "Lord Lieutenant of Lancashire", "link": "https://wikipedia.org/wiki/Lord_Lieutenant_of_Lancashire"}]}, {"year": "1712", "text": "<PERSON>, 4th Baron <PERSON>, English politician (b. 1675)", "html": "1712 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_4th_Baron_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>, 4th Baron <PERSON>\"><PERSON>, 4th Baron <PERSON></a>, English politician (b. 1675)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_4th_Baron_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>, 4th Baron <PERSON>\"><PERSON>, 4th Baron <PERSON></a>, English politician (b. 1675)", "links": [{"title": "<PERSON>, 4th Baron <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>,_4th_Baron_<PERSON>"}]}, {"year": "1787", "text": "<PERSON>, German composer (b. 1714)", "html": "1787 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German composer (b. 1714)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German composer (b. 1714)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1794", "text": "<PERSON>, Scottish-American minister and academic (b. 1723)", "html": "1794 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish-American minister and academic (b. 1723)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish-American minister and academic (b. 1723)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1795", "text": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, French painter (b. 1719)", "html": "1795 - <a href=\"https://wikipedia.org/wiki/<PERSON>-<PERSON>%C3%A9d%C3%A9<PERSON>-<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON></a>, French painter (b. 1719)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>-<PERSON>%C3%A9d%C3%A9<PERSON>-<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON></a>, French painter (b. 1719)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Charles-Am%C3%A9d%C3%A9e-<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1832", "text": "<PERSON><PERSON><PERSON>, French economist and businessman (b. 1767)", "html": "1832 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, French economist and businessman (b. 1767)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, French economist and businessman (b. 1767)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1836", "text": "<PERSON> of Alaska, Russian missionary and saint (b. 1750s)", "html": "1836 - <a href=\"https://wikipedia.org/wiki/<PERSON>_of_Alaska\" title=\"<PERSON> of Alaska\"><PERSON> of Alaska</a>, Russian missionary and saint (b. 1750s)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_of_Alaska\" title=\"<PERSON> of Alaska\"><PERSON> of Alaska</a>, Russian missionary and saint (b. 1750s)", "links": [{"title": "Herman of Alaska", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_Alaska"}]}, {"year": "1845", "text": "<PERSON>, English Baptist minister and Jamaican missionary (b. 1803)", "html": "1845 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English Baptist minister and Jamaican missionary (b. 1803)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English Baptist minister and Jamaican missionary (b. 1803)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1853", "text": "<PERSON>, Portuguese queen and regent (b. 1819)", "html": "1853 - <a href=\"https://wikipedia.org/wiki/Maria_II_of_Portugal\" title=\"Maria II of Portugal\"><PERSON> II</a>, Portuguese queen and regent (b. 1819)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Maria_II_of_Portugal\" title=\"Maria II of Portugal\"><PERSON> II</a>, Portuguese queen and regent (b. 1819)", "links": [{"title": "Maria II of Portugal", "link": "https://wikipedia.org/wiki/Maria_II_of_Portugal"}]}, {"year": "1892", "text": "<PERSON>, Scottish-Canadian serial killer (b. 1850)", "html": "1892 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish-Canadian serial killer (b. 1850)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish-Canadian serial killer (b. 1850)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1897", "text": "<PERSON>, English-Australian politician, 10th Premier of Tasmania (b. 1810)", "html": "1897 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English-Australian politician, 10th <a href=\"https://wikipedia.org/wiki/Premier_of_Tasmania\" title=\"Premier of Tasmania\">Premier of Tasmania</a> (b. 1810)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English-Australian politician, 10th <a href=\"https://wikipedia.org/wiki/Premier_of_Tasmania\" title=\"Premier of Tasmania\">Premier of Tasmania</a> (b. 1810)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Premier of Tasmania", "link": "https://wikipedia.org/wiki/Premier_of_Tasmania"}]}, {"year": "1908", "text": "<PERSON><PERSON><PERSON>, China empress dowager and regent (b. 1835)", "html": "1908 - <a href=\"https://wikipedia.org/wiki/Empress_<PERSON><PERSON>_Cix<PERSON>\" title=\"Empress Dowager Cixi\"><PERSON><PERSON><PERSON></a>, China empress dowager and regent (b. 1835)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Empress_<PERSON><PERSON>_<PERSON><PERSON><PERSON>\" title=\"Empress Dowager Cixi\"><PERSON><PERSON><PERSON></a>, China empress dowager and regent (b. 1835)", "links": [{"title": "Empress <PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON><PERSON>i"}]}, {"year": "1910", "text": "<PERSON>, German author (b. 1831)", "html": "1910 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German author (b. 1831)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German author (b. 1831)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1916", "text": "<PERSON><PERSON>, Polish journalist and author, Nobel Prize laureate (b. 1846)", "html": "1916 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Polish journalist and author, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Literature\" title=\"Nobel Prize in Literature\">Nobel Prize</a> laureate (b. 1846)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Polish journalist and author, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Literature\" title=\"Nobel Prize in Literature\">Nobel Prize</a> laureate (b. 1846)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}, {"title": "Nobel Prize in Literature", "link": "https://wikipedia.org/wiki/Nobel_Prize_in_Literature"}]}, {"year": "1917", "text": "<PERSON><PERSON>, French sociologist, psychologist, and philosopher (b. 1858)", "html": "1917 - <a href=\"https://wikipedia.org/wiki/%C3%89mile_Du<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, French sociologist, psychologist, and philosopher (b. 1858)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/%C3%89mile_Du<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, French sociologist, psychologist, and philosopher (b. 1858)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/%C3%89mile_Durkheim"}]}, {"year": "1919", "text": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, Polish-Russian engineer, electrician, and inventor (b. 1862)", "html": "1919 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Polish-Russian engineer, electrician, and inventor (b. 1862)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Polish-Russian engineer, electrician, and inventor (b. 1862)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Mikhail_<PERSON>ky"}]}, {"year": "1919", "text": "<PERSON>, Egyptian lawyer and politician (b. 1868)", "html": "1919 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Egyptian lawyer and politician (b. 1868)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Egyptian lawyer and politician (b. 1868)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1919", "text": "<PERSON>, French-Swiss chemist and academic, Nobel Prize laureate (b. 1866)", "html": "1919 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French-Swiss chemist and academic, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Chemistry\" title=\"Nobel Prize in Chemistry\">Nobel Prize</a> laureate (b. 1866)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French-Swiss chemist and academic, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Chemistry\" title=\"Nobel Prize in Chemistry\">Nobel Prize</a> laureate (b. 1866)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Nobel Prize in Chemistry", "link": "https://wikipedia.org/wiki/Nobel_Prize_in_Chemistry"}]}, {"year": "1921", "text": "<PERSON><PERSON><PERSON>, veteran Irish republican and leading trade unionist (b. 1880)", "html": "1921 - <a href=\"https://wikipedia.org/wiki/<PERSON>h<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON> <PERSON>\"><PERSON><PERSON><PERSON></a>, veteran <a href=\"https://wikipedia.org/wiki/Irish_republicanism\" title=\"Irish republicanism\">Irish republican</a> and leading trade unionist (b. 1880)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>h<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON> <PERSON>\"><PERSON><PERSON><PERSON></a>, veteran <a href=\"https://wikipedia.org/wiki/Irish_republicanism\" title=\"Irish republicanism\">Irish republican</a> and leading trade unionist (b. 1880)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Tadhg_<PERSON>"}, {"title": "Irish republicanism", "link": "https://wikipedia.org/wiki/Irish_republicanism"}]}, {"year": "1922", "text": "<PERSON><PERSON>, Greek lawyer and politician, 94th Prime Minister of Greece (b. 1866)", "html": "1922 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Greek lawyer and politician, 94th <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Greece\" title=\"Prime Minister of Greece\">Prime Minister of Greece</a> (b. 1866)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Greek lawyer and politician, 94th <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Greece\" title=\"Prime Minister of Greece\">Prime Minister of Greece</a> (b. 1866)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}, {"title": "Prime Minister of Greece", "link": "https://wikipedia.org/wiki/Prime_Minister_of_Greece"}]}, {"year": "1922", "text": "<PERSON><PERSON>, Greek mathematician and politician, 107th Prime Minister of Greece (b. 1854)", "html": "1922 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Greek mathematician and politician, 107th <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Greece\" title=\"Prime Minister of Greece\">Prime Minister of Greece</a> (b. 1854)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Greek mathematician and politician, 107th <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Greece\" title=\"Prime Minister of Greece\">Prime Minister of Greece</a> (b. 1854)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>padakis"}, {"title": "Prime Minister of Greece", "link": "https://wikipedia.org/wiki/Prime_Minister_of_Greece"}]}, {"year": "1922", "text": "<PERSON><PERSON>, Greek lawyer and politician, 106th Prime Minister of Greece (b. 1872)", "html": "1922 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Greek lawyer and politician, 106th <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Greece\" title=\"Prime Minister of Greece\">Prime Minister of Greece</a> (b. 1872)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Greek lawyer and politician, 106th <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Greece\" title=\"Prime Minister of Greece\">Prime Minister of Greece</a> (b. 1872)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Nikolaos_Stratos"}, {"title": "Prime Minister of Greece", "link": "https://wikipedia.org/wiki/Prime_Minister_of_Greece"}]}, {"year": "1941", "text": "<PERSON><PERSON>, English motorcycle racer (b. 1902)", "html": "1941 - <a href=\"https://wikipedia.org/wiki/W<PERSON>_<PERSON><PERSON>\" title=\"Wal <PERSON>ley\"><PERSON><PERSON></a>, English motorcycle racer (b. 1902)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/W<PERSON>_<PERSON><PERSON>\" title=\"Wal <PERSON>ley\"><PERSON><PERSON></a>, English motorcycle racer (b. 1902)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Wal_<PERSON>ley"}]}, {"year": "1942", "text": "<PERSON><PERSON><PERSON>, Swiss author and photographer (b. 1908)", "html": "1942 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Swiss author and photographer (b. 1908)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Swiss author and photographer (b. 1908)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1945", "text": "<PERSON>, American ornithologist and photographer (b. 1864)", "html": "1945 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(ornithologist)\" title=\"<PERSON> (ornithologist)\"><PERSON></a>, American ornithologist and photographer (b. 1864)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(ornithologist)\" title=\"<PERSON> (ornithologist)\"><PERSON></a>, American ornithologist and photographer (b. 1864)", "links": [{"title": "<PERSON> (ornithologist)", "link": "https://wikipedia.org/wiki/<PERSON>(ornithologist)"}]}, {"year": "1949", "text": "<PERSON>, Indian activist, assassin of <PERSON><PERSON><PERSON> (b. 1911)", "html": "1949 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Indian activist, assassin of <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a> (b. 1911)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Indian activist, assassin of <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a> (b. 1911)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>e"}, {"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1949", "text": "<PERSON><PERSON><PERSON>, Indian assassin of <PERSON><PERSON><PERSON> (b. 1910)", "html": "1949 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Indian assassin of <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a> (b. 1910)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Indian assassin of <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a> (b. 1910)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>e"}, {"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1951", "text": "<PERSON>, American painter and educator (b. 1862)", "html": "1951 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American painter and educator (b. 1862)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American painter and educator (b. 1862)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1954", "text": "<PERSON>, American actor, singer, director, and screenwriter (b. 1878)", "html": "1954 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor, singer, director, and screenwriter (b. 1878)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor, singer, director, and screenwriter (b. 1878)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1956", "text": "<PERSON>, German paleontologist (b. 1888)", "html": "1956 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German paleontologist (b. 1888)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German paleontologist (b. 1888)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1958", "text": "<PERSON>, American actor, singer, and producer (b. 1914)", "html": "1958 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Tyrone Power\"><PERSON></a>, American actor, singer, and producer (b. 1914)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_Power\" title=\"Tyrone Power\"><PERSON></a>, American actor, singer, and producer (b. 1914)", "links": [{"title": "Tyrone Power", "link": "https://wikipedia.org/wiki/Tyrone_Power"}]}, {"year": "1959", "text": "<PERSON>, Scottish physicist and meteorologist, Nobel Prize laureate (b. 1869)", "html": "1959 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, Scottish physicist and meteorologist, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Physics\" title=\"Nobel Prize in Physics\">Nobel Prize</a> laureate (b. 1869)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, Scottish physicist and meteorologist, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Physics\" title=\"Nobel Prize in Physics\">Nobel Prize</a> laureate (b. 1869)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>"}, {"title": "Nobel Prize in Physics", "link": "https://wikipedia.org/wiki/Nobel_Prize_in_Physics"}]}, {"year": "1960", "text": "<PERSON>, Canadian murderer (b. 1937)", "html": "1960 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian murderer (b. 1937)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian murderer (b. 1937)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1961", "text": "<PERSON>, American actress (b. 1883)", "html": "1961 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress (b. 1883)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress (b. 1883)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1961", "text": "<PERSON>, Dutch pathologist and academic (b. 1883)", "html": "1961 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch pathologist and academic (b. 1883)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch pathologist and academic (b. 1883)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>i<PERSON>"}]}, {"year": "1963", "text": "<PERSON>, Hungarian-American conductor (b. 1888)", "html": "1963 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Hungarian-American conductor (b. 1888)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Hungarian-American conductor (b. 1888)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1966", "text": "<PERSON><PERSON>, Greek weightlifter and wrestler (b. 1877)", "html": "1966 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Greek weightlifter and wrestler (b. 1877)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Greek weightlifter and wrestler (b. 1877)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1966", "text": "<PERSON>, Lithuanian-American sculptor and painter (b. 1887)", "html": "1966 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Lithuanian-American sculptor and painter (b. 1887)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Lithuanian-American sculptor and painter (b. 1887)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1967", "text": "<PERSON>, American soldier, pilot, and astronaut (b. 1930)", "html": "1967 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American soldier, pilot, and astronaut (b. 1930)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American soldier, pilot, and astronaut (b. 1930)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1970", "text": "<PERSON><PERSON>, Egyptian-Greek politician (b. 1884)", "html": "1970 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Egyptian-Greek politician (b. 1884)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Egyptian-Greek politician (b. 1884)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1971", "text": "<PERSON>, English-Russian colonel (b. 1903)", "html": "1971 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English-Russian colonel (b. 1903)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English-Russian colonel (b. 1903)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1976", "text": "<PERSON>, French actor, singer, and producer (b. 1904)", "html": "1976 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French actor, singer, and producer (b. 1904)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French actor, singer, and producer (b. 1904)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1978", "text": "<PERSON>, American anthropologist and author (b. 1901)", "html": "1978 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American anthropologist and author (b. 1901)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American anthropologist and author (b. 1901)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1980", "text": "<PERSON>, American actor and singer (b. 1916)", "html": "1980 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(singer)\" title=\"<PERSON> (singer)\"><PERSON></a>, American actor and singer (b. 1916)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(singer)\" title=\"<PERSON> (singer)\"><PERSON></a>, American actor and singer (b. 1916)", "links": [{"title": "<PERSON> (singer)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>(singer)"}]}, {"year": "1981", "text": "<PERSON>, American baseball player and coach (b. 1954)", "html": "1981 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player and coach (b. 1954)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player and coach (b. 1954)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1981", "text": "<PERSON><PERSON>, American actress (b. 1894)", "html": "1981 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American actress (b. 1894)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American actress (b. 1894)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Enid_<PERSON>"}]}, {"year": "1981", "text": "<PERSON><PERSON><PERSON>, Pakistani poet and scholar (b. 1938)", "html": "1981 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Pakistani poet and scholar (b. 1938)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Pakistani poet and scholar (b. 1938)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON><PERSON><PERSON>"}]}, {"year": "1982", "text": "<PERSON><PERSON><PERSON>, Indian philosopher and Gandhian, <PERSON><PERSON><PERSON> (b. 1895)", "html": "1982 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_Bhave\" title=\"Vinoba Bhave\"><PERSON><PERSON><PERSON></a>, Indian philosopher and Gandhian, <a href=\"https://wikipedia.org/wiki/B<PERSON>t_Ratna\" title=\"Bhara<PERSON> Ratna\"><PERSON><PERSON><PERSON></a> Awardee (b. 1895)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_Bhave\" title=\"Vinoba Bhave\"><PERSON><PERSON><PERSON></a>, Indian philosopher and Gandhian, <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>t_Ratna\" title=\"Bhara<PERSON> Ratna\"><PERSON><PERSON><PERSON></a> Awardee (b. 1895)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>ob<PERSON>_Bhave"}, {"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>t_<PERSON>na"}]}, {"year": "1982", "text": "<PERSON>, Argentinian race car driver (b. 1901)", "html": "1982 - <a href=\"https://wikipedia.org/wiki/Mart%C3%ADn_de_%C3%81<PERSON><PERSON><PERSON>_(racing_driver)\" title=\"<PERSON> (racing driver)\"><PERSON></a>, Argentinian race car driver (b. 1901)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Mart%C3%ADn_de_%C3%81<PERSON><PERSON><PERSON>_(racing_driver)\" title=\"<PERSON> (racing driver)\"><PERSON></a>, Argentinian race car driver (b. 1901)", "links": [{"title": "<PERSON> (racing driver)", "link": "https://wikipedia.org/wiki/Mart%C3%ADn_de_%C3%81lza<PERSON>_(racing_driver)"}]}, {"year": "1983", "text": "<PERSON>, English keyboard player and songwriter (b. 1955)", "html": "1983 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English keyboard player and songwriter (b. 1955)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English keyboard player and songwriter (b. 1955)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1983", "text": "<PERSON>, American baseball player and manager (b. 1898)", "html": "1983 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player and manager (b. 1898)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player and manager (b. 1898)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1983", "text": "<PERSON>, English actor (b. 1912)", "html": "1983 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actor (b. 1912)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actor (b. 1912)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1984", "text": "<PERSON>, American infant, who received baboon heart (b. 1984)", "html": "1984 - <a href=\"https://wikipedia.org/wiki/Baby_<PERSON><PERSON>\" title=\"Baby <PERSON><PERSON>\">Baby <PERSON><PERSON></a>, American infant, who received <a href=\"https://wikipedia.org/wiki/Baboon\" title=\"Baboon\">baboon</a> heart (b. 1984)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Baby_<PERSON><PERSON>\" title=\"Baby <PERSON><PERSON>\">Baby <PERSON><PERSON></a>, American infant, who received <a href=\"https://wikipedia.org/wiki/Baboon\" title=\"Baboon\">baboon</a> heart (b. 1984)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>"}, {"title": "Baboon", "link": "https://wikipedia.org/wiki/Baboon"}]}, {"year": "1985", "text": "<PERSON><PERSON><PERSON>, German-Swiss painter, photographer, and poet (b. 1913)", "html": "1985 - <a href=\"https://wikipedia.org/wiki/M%C3%A9<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, German-Swiss painter, photographer, and poet (b. 1913)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/M%C3%A9ret_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, German-Swiss painter, photographer, and poet (b. 1913)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/M%C3%A9ret_Oppenheim"}]}, {"year": "1988", "text": "<PERSON><PERSON>, Dominican conductor and composer (b. 1915)", "html": "1988 - <a href=\"https://wikipedia.org/wiki/Bill<PERSON>_<PERSON>%C3%B3meta\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Dominican conductor and composer (b. 1915)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>%C3%B3meta\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Dominican conductor and composer (b. 1915)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Billo_Fr%C3%B3meta"}]}, {"year": "1988", "text": "<PERSON><PERSON><PERSON><PERSON> of Athens, Greek archbishop and theologian (b. 1905)", "html": "1988 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>_of_Athens\" title=\"<PERSON><PERSON><PERSON><PERSON> I of Athens\"><PERSON><PERSON><PERSON><PERSON> of Athens</a>, Greek archbishop and theologian (b. 1905)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>_of_Athens\" title=\"<PERSON><PERSON><PERSON><PERSON> <PERSON> of Athens\"><PERSON><PERSON><PERSON><PERSON> <PERSON> of Athens</a>, Greek archbishop and theologian (b. 1905)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON> of Athens", "link": "https://wikipedia.org/wiki/<PERSON>eronymos_I_of_Athens"}]}, {"year": "1994", "text": "<PERSON>, American author (b. 1908)", "html": "1994 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author (b. 1908)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author (b. 1908)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1996", "text": "<PERSON><PERSON>, American lawyer and diplomat (b. 1904)", "html": "1996 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> Hiss\"><PERSON><PERSON></a>, American lawyer and diplomat (b. 1904)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> Hiss\"><PERSON><PERSON></a>, American lawyer and diplomat (b. 1904)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_Hiss"}]}, {"year": "1997", "text": "<PERSON>, American director and composer (b. 1912)", "html": "1997 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American director and composer (b. 1912)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American director and composer (b. 1912)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1998", "text": "<PERSON><PERSON>, Trinidadian-American activist (b. 1941)", "html": "1998 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Trinidadian-American activist (b. 1941)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Trinidadian-American activist (b. 1941)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1998", "text": "<PERSON><PERSON><PERSON><PERSON>, Czech discus thrower (b. 1937)", "html": "1998 - <a href=\"https://wikipedia.org/wiki/Ludv%C3%ADk_Dan%C4%9Bk\" title=\"Ludvík Daněk\"><PERSON><PERSON><PERSON><PERSON></a>, Czech discus thrower (b. 1937)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Ludv%C3%ADk_Dan%C4%9Bk\" title=\"Ludvík Daněk\"><PERSON>d<PERSON><PERSON></a>, Czech discus thrower (b. 1937)", "links": [{"title": "Ludvík Daněk", "link": "https://wikipedia.org/wiki/Ludv%C3%ADk_Dan%C4%9Bk"}]}, {"year": "2000", "text": "<PERSON><PERSON><PERSON>, son of industrialist <PERSON><PERSON><PERSON>, converted to Shia Islam (b. 1954)", "html": "2000 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, son of industrialist <PERSON><PERSON><PERSON>, converted to Shia Islam (b. 1954)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, son of industrialist <PERSON><PERSON><PERSON>, converted to Shia Islam (b. 1954)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "2003", "text": "<PERSON>, Canadian runner (b. 1910)", "html": "2003 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(track_and_field_athlete)\" class=\"mw-redirect\" title=\"<PERSON> (track and field athlete)\"><PERSON></a>, Canadian runner (b. 1910)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(track_and_field_athlete)\" class=\"mw-redirect\" title=\"<PERSON> (track and field athlete)\"><PERSON></a>, Canadian runner (b. 1910)", "links": [{"title": "<PERSON> (track and field athlete)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(track_and_field_athlete)"}]}, {"year": "2003", "text": "<PERSON>, American actress and singer (b. 1925)", "html": "2003 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress and singer (b. 1925)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress and singer (b. 1925)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2003", "text": "<PERSON>, American businessman, co-founded the Loews Corporation (b. 1923)", "html": "2003 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American businessman, co-founded the <a href=\"https://wikipedia.org/wiki/Loews_Corporation\" title=\"Loews Corporation\">Loews Corporation</a> (b. 1923)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American businessman, co-founded the <a href=\"https://wikipedia.org/wiki/Loews_Corporation\" title=\"Loews Corporation\">Loews Corporation</a> (b. 1923)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Loews Corporation", "link": "https://wikipedia.org/wiki/Loews_Corporation"}]}, {"year": "2003", "text": "<PERSON><PERSON>, American guitarist and producer (b. 1924)", "html": "2003 - <a href=\"https://wikipedia.org/wiki/Speedy_West\" title=\"Speedy West\"><PERSON><PERSON></a>, American guitarist and producer (b. 1924)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Speedy_West\" title=\"Speedy West\"><PERSON><PERSON></a>, American guitarist and producer (b. 1924)", "links": [{"title": "Speedy West", "link": "https://wikipedia.org/wiki/Speedy_West"}]}, {"year": "2004", "text": "<PERSON>, American businessman and politician, 30th Governor of Minnesota (b. 1909)", "html": "2004 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American businessman and politician, 30th <a href=\"https://wikipedia.org/wiki/Governor_of_Minnesota\" title=\"Governor of Minnesota\">Governor of Minnesota</a> (b. 1909)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American businessman and politician, 30th <a href=\"https://wikipedia.org/wiki/Governor_of_Minnesota\" title=\"Governor of Minnesota\">Governor of Minnesota</a> (b. 1909)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>"}, {"title": "Governor of Minnesota", "link": "https://wikipedia.org/wiki/Governor_of_Minnesota"}]}, {"year": "2004", "text": "<PERSON>, Welsh-Canadian actor and screenwriter (b. 1930)", "html": "2004 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(comedian)\" title=\"<PERSON> (comedian)\"><PERSON></a>, Welsh-Canadian actor and screenwriter (b. 1930)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(comedian)\" title=\"<PERSON> (comedian)\"><PERSON></a>, Welsh-Canadian actor and screenwriter (b. 1930)", "links": [{"title": "<PERSON> (comedian)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(comedian)"}]}, {"year": "2005", "text": "<PERSON>, American pastor and author (b. 1931)", "html": "2005 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American pastor and author (b. 1931)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American pastor and author (b. 1931)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2005", "text": "<PERSON><PERSON>, Finnish journalist and author (b. 1959)", "html": "2005 - <a href=\"https://wikipedia.org/wiki/Arto_<PERSON>n\" title=\"Art<PERSON>\"><PERSON><PERSON></a>, Finnish journalist and author (b. 1959)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Arto_<PERSON>n\" title=\"Art<PERSON>\"><PERSON><PERSON></a>, Finnish journalist and author (b. 1959)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Arto_Salminen"}]}, {"year": "2006", "text": "<PERSON>, American historian and author (b. 1937)", "html": "2006 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American historian and author (b. 1937)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American historian and author (b. 1937)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "2007", "text": "<PERSON>, American baseball player and sportscaster (b. 1928)", "html": "2007 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player and sportscaster (b. 1928)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player and sportscaster (b. 1928)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2008", "text": "<PERSON>, American painter (b. 1922)", "html": "2008 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American painter (b. 1922)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American painter (b. 1922)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2009", "text": "Serbian Patriarch <PERSON><PERSON><PERSON> (b. 1914)", "html": "2009 - <a href=\"https://wikipedia.org/wiki/Serbian_Patriarch_Pavle_II\" class=\"mw-redirect\" title=\"Serbian Patriarch Pavle II\">Serbian Patriarch Pa<PERSON><PERSON> II</a> (b. 1914)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Serbian_Patriarch_Pavle_II\" class=\"mw-redirect\" title=\"Serbian Patriarch Pavle II\">Serbian Patriarch <PERSON><PERSON><PERSON> II</a> (b. 1914)", "links": [{"title": "Serbian Patriarch <PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Serbian_Patriarch_<PERSON><PERSON><PERSON>_II"}]}, {"year": "2010", "text": "<PERSON>, American chess player and journalist (b. 1932)", "html": "2010 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(chess_grandmaster)\" class=\"mw-redirect\" title=\"<PERSON> (chess grandmaster)\"><PERSON></a>, American chess player and journalist (b. 1932)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(chess_grandmaster)\" class=\"mw-redirect\" title=\"<PERSON> (chess grandmaster)\"><PERSON></a>, American chess player and journalist (b. 1932)", "links": [{"title": "<PERSON> (chess grandmaster)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(chess_grandmaster)"}]}, {"year": "2010", "text": "<PERSON>, American baseball player (b. 1944)", "html": "2010 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player (b. 1944)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player (b. 1944)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2010", "text": "<PERSON>, American actor, director, and producer (b. 1921)", "html": "2010 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor, director, and producer (b. 1921)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor, director, and producer (b. 1921)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "2011", "text": "<PERSON><PERSON>, American murderer (b. 1946)", "html": "2011 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> Chandler\"><PERSON><PERSON></a>, American murderer (b. 1946)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> Chandler\"><PERSON><PERSON></a>, American murderer (b. 1946)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_Chandler"}]}, {"year": "2012", "text": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, Cameroonian footballer and politician (b. 1954)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/Th%C3%A9ophile_<PERSON>\" title=\"Théophile <PERSON>\">Th<PERSON><PERSON><PERSON><PERSON></a>, Cameroonian footballer and politician (b. 1954)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Th%C3%A9ophile_<PERSON>\" title=\"Théophile <PERSON>\">Thé<PERSON><PERSON><PERSON></a>, Cameroonian footballer and politician (b. 1954)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Th%C3%A9ophile_Abega"}]}, {"year": "2012", "text": "<PERSON><PERSON>, Portuguese motorcycle racer (b. 1976)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/Lu%C3%ADs_Carreira\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Portuguese motorcycle racer (b. 1976)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Lu%C3%AD<PERSON>_Carreira\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Portuguese motorcycle racer (b. 1976)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Lu%C3%ADs_Carreira"}]}, {"year": "2012", "text": "<PERSON><PERSON>, Fijian rugby player (b. 1983)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_Ku<PERSON>vore\" title=\"Maleli Kunavore\"><PERSON><PERSON></a>, Fijian rugby player (b. 1983)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>vore\" title=\"Maleli Kunavore\"><PERSON><PERSON></a>, Fijian rugby player (b. 1983)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_Kunavore"}]}, {"year": "2012", "text": "<PERSON><PERSON> <PERSON><PERSON>, Indian politician, 18th Indian Minister of Defence (b. 1931)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/<PERSON>._<PERSON><PERSON>_<PERSON>\" title=\"<PERSON>. <PERSON><PERSON>\"><PERSON><PERSON> <PERSON><PERSON></a>, Indian politician, 18th <a href=\"https://wikipedia.org/wiki/Minister_of_Defence_(India)\" title=\"Minister of Defence (India)\">Indian Minister of Defence</a> (b. 1931)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>._<PERSON><PERSON>_<PERSON>\" title=\"K. C<PERSON>\"><PERSON><PERSON> <PERSON><PERSON></a>, Indian politician, 18th <a href=\"https://wikipedia.org/wiki/Minister_of_Defence_(India)\" title=\"Minister of Defence (India)\">Indian Minister of Defence</a> (b. 1931)", "links": [{"title": "<PERSON><PERSON> <PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>._<PERSON><PERSON>_<PERSON>t"}, {"title": "Minister of Defence (India)", "link": "https://wikipedia.org/wiki/Minister_of_Defence_(India)"}]}, {"year": "2012", "text": "<PERSON><PERSON>, Norwegian trombonist, composer, and conductor (b. 1940)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/Frode_Thingn%C3%A6s\" title=\"Frode Thingnæ<PERSON>\"><PERSON><PERSON></a>, Norwegian trombonist, composer, and conductor (b. 1940)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Frode_Thingn%C3%A6s\" title=\"<PERSON>ode Thingnæ<PERSON>\"><PERSON><PERSON></a>, Norwegian trombonist, composer, and conductor (b. 1940)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Frode_Thingn%C3%A6s"}]}, {"year": "2013", "text": "<PERSON>, American actress and producer (b. 1929)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress and producer (b. 1929)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress and producer (b. 1929)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "2013", "text": "<PERSON><PERSON><PERSON><PERSON>, Cypriot lawyer and politician, 4th President of Cyprus (b. 1919)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_Clerides\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Cypriot lawyer and politician, 4th <a href=\"https://wikipedia.org/wiki/President_of_Cyprus\" title=\"President of Cyprus\">President of Cyprus</a> (b. 1919)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_C<PERSON>ides\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Cypriot lawyer and politician, 4th <a href=\"https://wikipedia.org/wiki/President_of_Cyprus\" title=\"President of Cyprus\">President of Cyprus</a> (b. 1919)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Glafcos_Clerides"}, {"title": "President of Cyprus", "link": "https://wikipedia.org/wiki/President_of_Cyprus"}]}, {"year": "2013", "text": "<PERSON>, American football player and coach (b. 1930)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(American_football)\" title=\"<PERSON> (American football)\"><PERSON></a>, American football player and coach (b. 1930)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(American_football)\" title=\"<PERSON> (American football)\"><PERSON></a>, American football player and coach (b. 1930)", "links": [{"title": "<PERSON> (American football)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(American_football)"}]}, {"year": "2014", "text": "<PERSON>, English painter and academic (b. 1918)", "html": "2014 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON> Chalker\"><PERSON></a>, English painter and academic (b. 1918)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON> Chalker\"><PERSON></a>, English painter and academic (b. 1918)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "2014", "text": "<PERSON>, French photographer and educator (b. 1934)", "html": "2014 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French photographer and educator (b. 1934)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French photographer and educator (b. 1934)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2014", "text": "<PERSON><PERSON><PERSON>, Cameroonian footballer (b. 1983)", "html": "2014 - <a href=\"https://wikipedia.org/wiki/Val%C3%A9ry_M%C3%A9zague\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Cameroonian footballer (b. 1983)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Val%C3%A9ry_M%C3%A9zague\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Cameroonian footballer (b. 1983)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Val%C3%A9ry_M%C3%A9zague"}]}, {"year": "2014", "text": "<PERSON>, Australian soldier and politician, Australian Minister for the Capital Territory (b. 1924)", "html": "2014 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian soldier and politician, <a href=\"https://wikipedia.org/wiki/Minister_for_Home_Affairs_(Australia)\" title=\"Minister for Home Affairs (Australia)\">Australian Minister for the Capital Territory</a> (b. 1924)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian soldier and politician, <a href=\"https://wikipedia.org/wiki/Minister_for_Home_Affairs_(Australia)\" title=\"Minister for Home Affairs (Australia)\">Australian Minister for the Capital Territory</a> (b. 1924)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>ers"}, {"title": "Minister for Home Affairs (Australia)", "link": "https://wikipedia.org/wiki/Minister_for_Home_Affairs_(Australia)"}]}, {"year": "2015", "text": "<PERSON><PERSON><PERSON><PERSON>, French author (b. 1920)", "html": "2015 - <a href=\"https://wikipedia.org/wiki/Gis%C3%A8<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, French author (b. 1920)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Gis%C3%A8le_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, French author (b. 1920)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Gis%C3%A8le_<PERSON><PERSON><PERSON>"}]}, {"year": "2015", "text": "<PERSON>, American economist and academic (b. 1930)", "html": "2015 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American economist and academic (b. 1930)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American economist and academic (b. 1930)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>f"}]}, {"year": "2015", "text": "<PERSON><PERSON>, Indian-British actor (b. 1929)", "html": "2015 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Indian-British actor (b. 1929)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Indian-British actor (b. 1929)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "2016", "text": "<PERSON><PERSON>, American pianist and songwriter (b. 1927)", "html": "2016 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American pianist and songwriter (b. 1927)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American pianist and songwriter (b. 1927)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "2017", "text": "<PERSON>, American singer and rapper (b. 1996)", "html": "2017 - <a href=\"https://wikipedia.org/wiki/<PERSON>_Peep\" title=\"Lil Peep\"><PERSON></a>, American singer and rapper (b. 1996)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_Peep\" title=\"Lil Peep\"><PERSON></a>, American singer and rapper (b. 1996)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>"}]}, {"year": "2023", "text": "<PERSON><PERSON><PERSON>, Serbian actor (b. 1960)", "html": "2023 - <a href=\"https://wikipedia.org/wiki/%C5%BDarko_Lau%C5%A1evi%C4%87\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Serbian actor (b. 1960)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/%C5%BDarko_Lau%C5%A1evi%C4%87\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Serbian actor (b. 1960)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/%C5%BDarko_Lau%C5%A1evi%C4%87"}]}, {"year": "2024", "text": "<PERSON>, Portuguese pacifist (b. 1933)", "html": "2024 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Portuguese pacifist (b. 1933)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Caeiro\"><PERSON></a>, Portuguese pacifist (b. 1933)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Celeste_<PERSON>iro"}]}, {"year": "2024", "text": "<PERSON><PERSON><PERSON>, Romanian-American gymnastics coach (b. 1942)", "html": "2024 - <a href=\"https://wikipedia.org/wiki/B%C3%A9la_K%C3%<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Romanian-American gymnastics coach (b. 1942)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/B%C3%A9la_K%C3%<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Romanian-American gymnastics coach (b. 1942)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/B%C3%A9la_K%C3%A1rolyi"}]}, {"year": "2024", "text": "<PERSON>, Irish comedian and actor (b. 1957)", "html": "2024 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Irish comedian and actor (b. 1957)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Irish comedian and actor (b. 1957)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2024", "text": "<PERSON><PERSON>, Princess <PERSON><PERSON><PERSON>, Japanese princess (b. 1923)", "html": "2024 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>,_Princess_<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON>, Princess <PERSON><PERSON><PERSON>\"><PERSON><PERSON>, Princess <PERSON><PERSON><PERSON></a>, Japanese princess (b. 1923)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>,_Princess_<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON>, Princess <PERSON><PERSON><PERSON>\"><PERSON><PERSON>, Princess <PERSON><PERSON><PERSON></a>, Japanese princess (b. 1923)", "links": [{"title": "<PERSON><PERSON>, Princess <PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>,_Princess_<PERSON><PERSON><PERSON>"}]}]}}