{"date": "September 5", "url": "https://wikipedia.org/wiki/September_5", "data": {"Events": [{"year": "917", "text": "<PERSON> declares himself emperor, establishing the Southern Han state in southern China, at his capital of Panyu.", "html": "917 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(emperor)\" title=\"<PERSON> (emperor)\"><PERSON></a> declares himself emperor, establishing the <a href=\"https://wikipedia.org/wiki/Southern_Han\" title=\"Southern Han\">Southern Han</a> state in southern China, at his capital of Panyu.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(emperor)\" title=\"<PERSON> (emperor)\"><PERSON></a> declares himself emperor, establishing the <a href=\"https://wikipedia.org/wiki/Southern_Han\" title=\"Southern Han\">Southern Han</a> state in southern China, at his capital of Panyu.", "links": [{"title": "<PERSON> (emperor)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(emperor)"}, {"title": "Southern Han", "link": "https://wikipedia.org/wiki/Southern_Han"}]}, {"year": "1367", "text": "<PERSON><PERSON> <PERSON> becomes king of Ava", "html": "1367 - <a href=\"https://wikipedia.org/wiki/<PERSON>wa_Saw_Ke\" title=\"Swa Saw Ke\"><PERSON><PERSON> <PERSON></a> becomes king of <a href=\"https://wikipedia.org/wiki/Ava_Kingdom\" class=\"mw-redirect\" title=\"Ava Kingdom\">Ava</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_Ke\" title=\"Swa Saw Ke\"><PERSON><PERSON> <PERSON></a> becomes king of <a href=\"https://wikipedia.org/wiki/Ava_Kingdom\" class=\"mw-redirect\" title=\"Ava Kingdom\">Ava</a>", "links": [{"title": "<PERSON><PERSON> <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>"}, {"title": "Ava Kingdom", "link": "https://wikipedia.org/wiki/Ava_Kingdom"}]}, {"year": "1590", "text": "<PERSON>'s army forces <PERSON> of France to lift the siege of Paris.", "html": "1590 - <a href=\"https://wikipedia.org/wiki/<PERSON>,_Duke_of_Parma\" title=\"<PERSON>, Duke of Parma\"><PERSON></a>'s army forces <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_France\" title=\"<PERSON> IV of France\"><PERSON> of France</a> to lift the <a href=\"https://wikipedia.org/wiki/Siege_of_Paris_(1590)\" title=\"Siege of Paris (1590)\">siege of Paris</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>,_Duke_of_Parma\" title=\"<PERSON>, Duke of Parma\"><PERSON></a>'s army forces <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_France\" title=\"<PERSON> IV of France\"><PERSON> of France</a> to lift the <a href=\"https://wikipedia.org/wiki/Siege_of_Paris_(1590)\" title=\"Siege of Paris (1590)\">siege of Paris</a>.", "links": [{"title": "<PERSON>, Duke of Parma", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Duke_of_Parma"}, {"title": "<PERSON> of France", "link": "https://wikipedia.org/wiki/<PERSON>_IV_of_France"}, {"title": "Siege of Paris (1590)", "link": "https://wikipedia.org/wiki/Siege_of_Paris_(1590)"}]}, {"year": "1622", "text": "A hurricane overruns a Spanish fleet bound from Havana to Cadiz and sinks the galleon Atocha. Only five men are rescued, but 260 passengers and 200 million pesos are buried with the Atocha under 50 feet of water.", "html": "1622 - A hurricane overruns a Spanish fleet bound from Havana to Cadiz and sinks the <a href=\"https://wikipedia.org/wiki/Nuestra_Se%C3%B1ora_de_Atocha\" title=\"Nuestra Señora de Atocha\">galleon Atocha</a>. Only five men are rescued, but 260 passengers and 200 million pesos are buried with the Atocha under 50 feet of water.", "no_year_html": "A hurricane overruns a Spanish fleet bound from Havana to Cadiz and sinks the <a href=\"https://wikipedia.org/wiki/Nuestra_Se%C3%B1ora_de_Atocha\" title=\"Nuestra Señora de Atocha\">galleon Atocha</a>. Only five men are rescued, but 260 passengers and 200 million pesos are buried with the Atocha under 50 feet of water.", "links": [{"title": "Nuestra Señora de Atocha", "link": "https://wikipedia.org/wiki/Nuestra_Se%C3%B1ora_de_Atocha"}]}, {"year": "1661", "text": "Fall of <PERSON>: <PERSON>'s Superintendent of Finances is arrested in Nantes by <PERSON><PERSON><PERSON><PERSON>, captain of the king's musketeers.", "html": "1661 - Fall of <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>: <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_France\" class=\"mw-redirect\" title=\"Louis XIV of France\"><PERSON></a>'s <a href=\"https://wikipedia.org/wiki/Superintendent_of_Finances\" title=\"Superintendent of Finances\">Superintendent of Finances</a> is arrested in <a href=\"https://wikipedia.org/wiki/Nantes\" title=\"Nan<PERSON>\">Nantes</a> by <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>Castelmore_d%27Artagnan\" class=\"mw-redirect\" title=\"<PERSON> d'Artagnan\"><PERSON><PERSON><PERSON><PERSON></a>, captain of the king's <a href=\"https://wikipedia.org/wiki/Musketeer\" title=\"Musketeer\">musketeers</a>.", "no_year_html": "Fall of <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>: <a href=\"https://wikipedia.org/wiki/<PERSON>_XIV_of_France\" class=\"mw-redirect\" title=\"Louis XIV of France\"><PERSON></a>'s <a href=\"https://wikipedia.org/wiki/Superintendent_of_Finances\" title=\"Superintendent of Finances\">Superintendent of Finances</a> is arrested in <a href=\"https://wikipedia.org/wiki/Nantes\" title=\"Nantes\">Nantes</a> by <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>Castelmore_d%27Artagnan\" class=\"mw-redirect\" title=\"<PERSON> d'Artagnan\"><PERSON><PERSON><PERSON><PERSON></a>, captain of the king's <a href=\"https://wikipedia.org/wiki/Musketeer\" title=\"Musketeer\">musketeers</a>.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "<PERSON> of France", "link": "https://wikipedia.org/wiki/Louis_XIV_of_France"}, {"title": "Superintendent of Finances", "link": "https://wikipedia.org/wiki/Superintendent_of_Finances"}, {"title": "Nantes", "link": "https://wikipedia.org/wiki/Nantes"}, {"title": "<PERSON>Castelmore d'Artagnan", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>-Castelmore_d%27Artagnan"}, {"title": "Musketeer", "link": "https://wikipedia.org/wiki/Musketeer"}]}, {"year": "1666", "text": "Great Fire of London ends: Ten thousand buildings, including Old St Paul's Cathedral, are destroyed, but only six people are known to have died.", "html": "1666 - <a href=\"https://wikipedia.org/wiki/Great_Fire_of_London\" title=\"Great Fire of London\">Great Fire of London</a> ends: Ten thousand buildings, including <a href=\"https://wikipedia.org/wiki/Old_St_Paul%27s_Cathedral\" title=\"Old St Paul's Cathedral\">Old St Paul's Cathedral</a>, are destroyed, but only six people are known to have died.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Great_Fire_of_London\" title=\"Great Fire of London\">Great Fire of London</a> ends: Ten thousand buildings, including <a href=\"https://wikipedia.org/wiki/Old_St_Paul%27s_Cathedral\" title=\"Old St Paul's Cathedral\">Old St Paul's Cathedral</a>, are destroyed, but only six people are known to have died.", "links": [{"title": "Great Fire of London", "link": "https://wikipedia.org/wiki/Great_Fire_of_London"}, {"title": "Old St Paul's Cathedral", "link": "https://wikipedia.org/wiki/Old_St_Paul%27s_Cathedral"}]}, {"year": "1697", "text": "War of the Grand Alliance : A French warship commanded by Captain <PERSON> defeated an English squadron at the Battle of Hudson's Bay.", "html": "1697 - <a href=\"https://wikipedia.org/wiki/War_of_the_Grand_Alliance\" class=\"mw-redirect\" title=\"War of the Grand Alliance\">War of the Grand Alliance</a> : A French warship commanded by Captain <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_d%27Iberville\" title=\"<PERSON> d'Iberville\"><PERSON> d<PERSON>Iber<PERSON></a> defeated an English squadron at the <a href=\"https://wikipedia.org/wiki/Battle_of_Hudson%27s_Bay\" title=\"Battle of Hudson's Bay\">Battle of Hudson's Bay</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/War_of_the_Grand_Alliance\" class=\"mw-redirect\" title=\"War of the Grand Alliance\">War of the Grand Alliance</a> : A French warship commanded by Captain <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_d%27Iberville\" title=\"<PERSON> d'Iberville\"><PERSON> d<PERSON>Iber<PERSON></a> defeated an English squadron at the <a href=\"https://wikipedia.org/wiki/Battle_of_Hudson%27s_Bay\" title=\"Battle of Hudson's Bay\">Battle of Hudson's Bay</a>.", "links": [{"title": "War of the Grand Alliance", "link": "https://wikipedia.org/wiki/War_of_the_Grand_Alliance"}, {"title": "<PERSON>Iber<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_d%27Iberville"}, {"title": "Battle of Hudson's Bay", "link": "https://wikipedia.org/wiki/Battle_of_Hudson%27s_Bay"}]}, {"year": "1698", "text": "In an effort to Westernize his nobility, Tsar <PERSON> of Russia imposes a tax on beards for all men except the clergy and peasantry.", "html": "1698 - In an effort to Westernize his nobility, <a href=\"https://wikipedia.org/wiki/Tsar\" title=\"Tsar\">Tsar</a> <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Russia\" class=\"mw-redirect\" title=\"<PERSON> of Russia\"><PERSON> of Russia</a> imposes a tax on beards for all men except the clergy and peasantry.", "no_year_html": "In an effort to Westernize his nobility, <a href=\"https://wikipedia.org/wiki/Tsar\" title=\"Tsar\">Tsar</a> <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Russia\" class=\"mw-redirect\" title=\"<PERSON> of Russia\"><PERSON> of Russia</a> imposes a tax on beards for all men except the clergy and peasantry.", "links": [{"title": "Tsar", "link": "https://wikipedia.org/wiki/Tsar"}, {"title": "<PERSON> of Russia", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Russia"}]}, {"year": "1725", "text": "Wedding of Louis <PERSON> and <PERSON>.", "html": "1725 - Wedding of <a href=\"https://wikipedia.org/wiki/Louis_XV_of_France\" class=\"mw-redirect\" title=\"Louis XV of France\"><PERSON></a> and <a href=\"https://wikipedia.org/wiki/<PERSON>_Leszczy%C5%84ska\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>.", "no_year_html": "Wedding of <a href=\"https://wikipedia.org/wiki/Louis_XV_of_France\" class=\"mw-redirect\" title=\"Louis XV of France\"><PERSON></a> and <a href=\"https://wikipedia.org/wiki/<PERSON>_Leszczy%C5%84ska\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>.", "links": [{"title": "<PERSON> XV of France", "link": "https://wikipedia.org/wiki/Louis_XV_of_France"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Maria_Leszczy%C5%84ska"}]}, {"year": "1774", "text": "First Continental Congress assembles in Philadelphia.", "html": "1774 - <a href=\"https://wikipedia.org/wiki/First_Continental_Congress\" title=\"First Continental Congress\">First</a> <a href=\"https://wikipedia.org/wiki/Continental_Congress\" title=\"Continental Congress\">Continental Congress</a> assembles in <a href=\"https://wikipedia.org/wiki/Philadelphia\" title=\"Philadelphia\">Philadelphia</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/First_Continental_Congress\" title=\"First Continental Congress\">First</a> <a href=\"https://wikipedia.org/wiki/Continental_Congress\" title=\"Continental Congress\">Continental Congress</a> assembles in <a href=\"https://wikipedia.org/wiki/Philadelphia\" title=\"Philadelphia\">Philadelphia</a>.", "links": [{"title": "First Continental Congress", "link": "https://wikipedia.org/wiki/First_Continental_Congress"}, {"title": "Continental Congress", "link": "https://wikipedia.org/wiki/Continental_Congress"}, {"title": "Philadelphia", "link": "https://wikipedia.org/wiki/Philadelphia"}]}, {"year": "1781", "text": "Battle of the Chesapeake in the American Revolutionary War: The British Navy is repelled by the French Navy, contributing to the British surrender at Yorktown.", "html": "1781 - <a href=\"https://wikipedia.org/wiki/Battle_of_the_Chesapeake\" title=\"Battle of the Chesapeake\">Battle of the Chesapeake</a> in the <a href=\"https://wikipedia.org/wiki/American_Revolutionary_War\" title=\"American Revolutionary War\">American Revolutionary War</a>: The British Navy is repelled by the French Navy, contributing to the <a href=\"https://wikipedia.org/wiki/Siege_of_Yorktown\" title=\"Siege of Yorktown\">British surrender at Yorktown</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Battle_of_the_Chesapeake\" title=\"Battle of the Chesapeake\">Battle of the Chesapeake</a> in the <a href=\"https://wikipedia.org/wiki/American_Revolutionary_War\" title=\"American Revolutionary War\">American Revolutionary War</a>: The British Navy is repelled by the French Navy, contributing to the <a href=\"https://wikipedia.org/wiki/Siege_of_Yorktown\" title=\"Siege of Yorktown\">British surrender at Yorktown</a>.", "links": [{"title": "Battle of the Chesapeake", "link": "https://wikipedia.org/wiki/Battle_of_the_Chesapeake"}, {"title": "American Revolutionary War", "link": "https://wikipedia.org/wiki/American_Revolutionary_War"}, {"title": "Siege of Yorktown", "link": "https://wikipedia.org/wiki/Siege_of_Yorktown"}]}, {"year": "1791", "text": "<PERSON><PERSON><PERSON><PERSON> writes the Declaration of the Rights of Woman and of the Female Citizen.", "html": "1791 - <a href=\"https://wikipedia.org/wiki/Olympe_de_Gouges\" title=\"Olympe de Gouges\">Olympe de Gouges</a> writes the <a href=\"https://wikipedia.org/wiki/Declaration_of_the_Rights_of_Woman_and_of_the_Female_Citizen\" title=\"Declaration of the Rights of Woman and of the Female Citizen\">Declaration of the Rights of Woman and of the Female Citizen</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Olympe_de_Gouges\" title=\"Olympe de Gouges\">Olympe de Gouges</a> writes the <a href=\"https://wikipedia.org/wiki/Declaration_of_the_Rights_of_Woman_and_of_the_Female_Citizen\" title=\"Declaration of the Rights of Woman and of the Female Citizen\">Declaration of the Rights of Woman and of the Female Citizen</a>.", "links": [{"title": "Olympe de Gouges", "link": "https://wikipedia.org/wiki/Olympe_de_Gouges"}, {"title": "Declaration of the Rights of Woman and of the Female Citizen", "link": "https://wikipedia.org/wiki/Declaration_of_the_Rights_of_Woman_and_of_the_Female_Citizen"}]}, {"year": "1793", "text": "French Revolution: The French National Convention initiates the Reign of Terror.", "html": "1793 - <a href=\"https://wikipedia.org/wiki/French_Revolution\" title=\"French Revolution\">French Revolution</a>: The <a href=\"https://wikipedia.org/wiki/French_National_Convention\" class=\"mw-redirect\" title=\"French National Convention\">French National Convention</a> initiates the <a href=\"https://wikipedia.org/wiki/Reign_of_Terror\" title=\"Reign of Terror\">Reign of Terror</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/French_Revolution\" title=\"French Revolution\">French Revolution</a>: The <a href=\"https://wikipedia.org/wiki/French_National_Convention\" class=\"mw-redirect\" title=\"French National Convention\">French National Convention</a> initiates the <a href=\"https://wikipedia.org/wiki/Reign_of_Terror\" title=\"Reign of Terror\">Reign of Terror</a>.", "links": [{"title": "French Revolution", "link": "https://wikipedia.org/wiki/French_Revolution"}, {"title": "French National Convention", "link": "https://wikipedia.org/wiki/French_National_Convention"}, {"title": "Reign of Terror", "link": "https://wikipedia.org/wiki/Reign_of_Terror"}]}, {"year": "1798", "text": "Conscription is made mandatory in France by the Jourdan law.", "html": "1798 - <a href=\"https://wikipedia.org/wiki/Conscription\" title=\"Conscription\">Conscription</a> is made mandatory in France by the <a href=\"https://wikipedia.org/wiki/Jour<PERSON>_law\" title=\"Jourdan law\">Jourdan law</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Conscription\" title=\"Conscription\">Conscription</a> is made mandatory in France by the <a href=\"https://wikipedia.org/wiki/Jourdan_law\" title=\"Jourdan law\">Jourdan law</a>.", "links": [{"title": "Conscription", "link": "https://wikipedia.org/wiki/Conscription"}, {"title": "<PERSON><PERSON><PERSON> law", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_law"}]}, {"year": "1812", "text": "War of 1812: The Siege of Fort Wayne begins when Chief <PERSON><PERSON><PERSON>'s forces attack two soldiers returning from the fort's outhouses.", "html": "1812 - <a href=\"https://wikipedia.org/wiki/War_of_1812\" title=\"War of 1812\">War of 1812</a>: The <a href=\"https://wikipedia.org/wiki/Siege_of_Fort_Wayne\" title=\"Siege of Fort Wayne\">Siege of Fort Wayne</a> begins when Chief <a href=\"https://wikipedia.org/wiki/Winamac\" title=\"Winamac\">Winamac</a>'s forces attack two soldiers returning from the fort's outhouses.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/War_of_1812\" title=\"War of 1812\">War of 1812</a>: The <a href=\"https://wikipedia.org/wiki/Siege_of_Fort_Wayne\" title=\"Siege of Fort Wayne\">Siege of Fort Wayne</a> begins when Chief <a href=\"https://wikipedia.org/wiki/Winamac\" title=\"Winamac\">Winamac</a>'s forces attack two soldiers returning from the fort's outhouses.", "links": [{"title": "War of 1812", "link": "https://wikipedia.org/wiki/War_of_1812"}, {"title": "Siege of Fort Wayne", "link": "https://wikipedia.org/wiki/Siege_of_Fort_Wayne"}, {"title": "Winamac", "link": "https://wikipedia.org/wiki/Winamac"}]}, {"year": "1816", "text": "Louis XVIII has to dissolve the Chambre introuvable (\"Unobtainable Chamber\").", "html": "1816 - <a href=\"https://wikipedia.org/wiki/Louis_XVIII_of_France\" class=\"mw-redirect\" title=\"Louis XVIII of France\">Louis XVIII</a> has to dissolve the <a href=\"https://wikipedia.org/wiki/Chambre_introuvable\" title=\"Chambre introuvable\">Chambre introuvable</a> (\"Unobtainable Chamber\").", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Louis_XVIII_of_France\" class=\"mw-redirect\" title=\"Louis XVIII of France\">Louis XVIII</a> has to dissolve the <a href=\"https://wikipedia.org/wiki/Chambre_introuvable\" title=\"Chambre introuvable\">Chambre introuvable</a> (\"Unobtainable Chamber\").", "links": [{"title": "<PERSON> of France", "link": "https://wikipedia.org/wiki/Louis_XVIII_of_France"}, {"title": "Chambre introuvable", "link": "https://wikipedia.org/wiki/Chambre_introuvable"}]}, {"year": "1836", "text": "<PERSON> is elected as the first president of the Republic of Texas.", "html": "1836 - <a href=\"https://wikipedia.org/wiki/Sam_Houston\" title=\"Sam Houston\"><PERSON></a> is elected as the first president of the <a href=\"https://wikipedia.org/wiki/Republic_of_Texas\" title=\"Republic of Texas\">Republic of Texas</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Sam_Houston\" title=\"Sam Houston\"><PERSON></a> is elected as the first president of the <a href=\"https://wikipedia.org/wiki/Republic_of_Texas\" title=\"Republic of Texas\">Republic of Texas</a>.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Sam_Houston"}, {"title": "Republic of Texas", "link": "https://wikipedia.org/wiki/Republic_of_Texas"}]}, {"year": "1839", "text": "The United Kingdom declares war on the Qing dynasty of China.", "html": "1839 - The United Kingdom declares <a href=\"https://wikipedia.org/wiki/First_Opium_War\" title=\"First Opium War\">war</a> on the <a href=\"https://wikipedia.org/wiki/Qing_dynasty\" title=\"Qing dynasty\">Qing dynasty</a> of China.", "no_year_html": "The United Kingdom declares <a href=\"https://wikipedia.org/wiki/First_Opium_War\" title=\"First Opium War\">war</a> on the <a href=\"https://wikipedia.org/wiki/Qing_dynasty\" title=\"Qing dynasty\">Qing dynasty</a> of China.", "links": [{"title": "First Opium War", "link": "https://wikipedia.org/wiki/First_Opium_War"}, {"title": "Qing dynasty", "link": "https://wikipedia.org/wiki/Qing_dynasty"}]}, {"year": "1862", "text": "American Civil War: The Army of Northern Virginia crosses the Potomac River at White's Ford in the Maryland Campaign.", "html": "1862 - <a href=\"https://wikipedia.org/wiki/American_Civil_War\" title=\"American Civil War\">American Civil War</a>: The <a href=\"https://wikipedia.org/wiki/Army_of_Northern_Virginia\" title=\"Army of Northern Virginia\">Army of Northern Virginia</a> crosses the <a href=\"https://wikipedia.org/wiki/Potomac_River\" title=\"Potomac River\">Potomac River</a> at <a href=\"https://wikipedia.org/wiki/White%27s_Ford\" title=\"White's Ford\">White's Ford</a> in the <a href=\"https://wikipedia.org/wiki/Maryland_Campaign\" class=\"mw-redirect\" title=\"Maryland Campaign\">Maryland Campaign</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/American_Civil_War\" title=\"American Civil War\">American Civil War</a>: The <a href=\"https://wikipedia.org/wiki/Army_of_Northern_Virginia\" title=\"Army of Northern Virginia\">Army of Northern Virginia</a> crosses the <a href=\"https://wikipedia.org/wiki/Potomac_River\" title=\"Potomac River\">Potomac River</a> at <a href=\"https://wikipedia.org/wiki/White%27s_Ford\" title=\"White's Ford\">White's Ford</a> in the <a href=\"https://wikipedia.org/wiki/Maryland_Campaign\" class=\"mw-redirect\" title=\"Maryland Campaign\">Maryland Campaign</a>.", "links": [{"title": "American Civil War", "link": "https://wikipedia.org/wiki/American_Civil_War"}, {"title": "Army of Northern Virginia", "link": "https://wikipedia.org/wiki/Army_of_Northern_Virginia"}, {"title": "Potomac River", "link": "https://wikipedia.org/wiki/Potomac_River"}, {"title": "<PERSON>'s Ford", "link": "https://wikipedia.org/wiki/White%27s_<PERSON>"}, {"title": "Maryland Campaign", "link": "https://wikipedia.org/wiki/Maryland_Campaign"}]}, {"year": "1877", "text": "American Indian Wars: Oglala Sioux chief <PERSON> is bayoneted by a United States soldier after resisting confinement in a guardhouse at Fort Robinson in Nebraska.", "html": "1877 - <a href=\"https://wikipedia.org/wiki/American_Indian_Wars\" title=\"American Indian Wars\">American Indian Wars</a>: <a href=\"https://wikipedia.org/wiki/Oglala_Lakota\" class=\"mw-redirect\" title=\"Oglala Lakota\">Oglala Sioux</a> <a href=\"https://wikipedia.org/wiki/Tribal_chief\" title=\"Tribal chief\">chief</a> <a href=\"https://wikipedia.org/wiki/Crazy_Horse\" title=\"Crazy Horse\">Crazy Horse</a> is <a href=\"https://wikipedia.org/wiki/Bayonet\" title=\"Bayonet\">bayoneted</a> by a United States soldier after resisting confinement in a guardhouse at <a href=\"https://wikipedia.org/wiki/Fort_Robinson\" title=\"Fort Robinson\">Fort Robinson</a> in <a href=\"https://wikipedia.org/wiki/Nebraska\" title=\"Nebraska\">Nebraska</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/American_Indian_Wars\" title=\"American Indian Wars\">American Indian Wars</a>: <a href=\"https://wikipedia.org/wiki/Oglala_Lakota\" class=\"mw-redirect\" title=\"Oglala Lakota\">Oglala Sioux</a> <a href=\"https://wikipedia.org/wiki/Tribal_chief\" title=\"Tribal chief\">chief</a> <a href=\"https://wikipedia.org/wiki/Crazy_Horse\" title=\"Crazy Horse\">Crazy Horse</a> is <a href=\"https://wikipedia.org/wiki/Bayonet\" title=\"Bayonet\">bayoneted</a> by a United States soldier after resisting confinement in a guardhouse at <a href=\"https://wikipedia.org/wiki/Fort_Robinson\" title=\"Fort Robinson\">Fort Robinson</a> in <a href=\"https://wikipedia.org/wiki/Nebraska\" title=\"Nebraska\">Nebraska</a>.", "links": [{"title": "American Indian Wars", "link": "https://wikipedia.org/wiki/American_Indian_Wars"}, {"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Oglala_Lakota"}, {"title": "Tribal chief", "link": "https://wikipedia.org/wiki/Tribal_chief"}, {"title": "Crazy Horse", "link": "https://wikipedia.org/wiki/Crazy_Horse"}, {"title": "Bayonet", "link": "https://wikipedia.org/wiki/Bayonet"}, {"title": "Fort Robinson", "link": "https://wikipedia.org/wiki/Fort_Robinson"}, {"title": "Nebraska", "link": "https://wikipedia.org/wiki/Nebraska"}]}, {"year": "1882", "text": "The first United States Labor Day parade is held in New York City.", "html": "1882 - The first United States <a href=\"https://wikipedia.org/wiki/Labor_Day\" title=\"Labor Day\">Labor Day</a> parade is held in New York City.", "no_year_html": "The first United States <a href=\"https://wikipedia.org/wiki/Labor_Day\" title=\"Labor Day\">Labor Day</a> parade is held in New York City.", "links": [{"title": "Labor Day", "link": "https://wikipedia.org/wiki/Labor_Day"}]}, {"year": "1887", "text": "A fire at the Theatre Royal, Exeter kills 186, making it the UK's deadliest ever building fire.", "html": "1887 - A <a href=\"https://wikipedia.org/wiki/Exeter_Theatre_Royal_fire\" title=\"Exeter Theatre Royal fire\">fire at the Theatre Royal, Exeter</a> kills 186, making it the UK's deadliest ever building fire.", "no_year_html": "A <a href=\"https://wikipedia.org/wiki/Exeter_Theatre_Royal_fire\" title=\"Exeter Theatre Royal fire\">fire at the Theatre Royal, Exeter</a> kills 186, making it the UK's deadliest ever building fire.", "links": [{"title": "Exeter Theatre Royal fire", "link": "https://wikipedia.org/wiki/Exeter_Theatre_Royal_fire"}]}, {"year": "1905", "text": "Russo-Japanese War: In New Hampshire, United States, the Treaty of Portsmouth, mediated by U.S. President <PERSON>, ends the war.", "html": "1905 - <a href=\"https://wikipedia.org/wiki/Russo-Japanese_War\" title=\"Russo-Japanese War\">Russo-Japanese War</a>: In <a href=\"https://wikipedia.org/wiki/New_Hampshire\" title=\"New Hampshire\">New Hampshire</a>, United States, the <a href=\"https://wikipedia.org/wiki/Treaty_of_Portsmouth\" title=\"Treaty of Portsmouth\">Treaty of Portsmouth</a>, mediated by U.S. President <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, ends the war.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Russo-Japanese_War\" title=\"Russo-Japanese War\">Russo-Japanese War</a>: In <a href=\"https://wikipedia.org/wiki/New_Hampshire\" title=\"New Hampshire\">New Hampshire</a>, United States, the <a href=\"https://wikipedia.org/wiki/Treaty_of_Portsmouth\" title=\"Treaty of Portsmouth\">Treaty of Portsmouth</a>, mediated by U.S. President <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, ends the war.", "links": [{"title": "Russo-Japanese War", "link": "https://wikipedia.org/wiki/Russo-Japanese_War"}, {"title": "New Hampshire", "link": "https://wikipedia.org/wiki/New_Hampshire"}, {"title": "Treaty of Portsmouth", "link": "https://wikipedia.org/wiki/Treaty_of_Portsmouth"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1914", "text": "World War I: First Battle of the Marne begins. Northeast of Paris, the French attack and defeat German forces who are advancing on the capital.", "html": "1914 - <a href=\"https://wikipedia.org/wiki/World_War_I\" title=\"World War I\">World War I</a>: <a href=\"https://wikipedia.org/wiki/First_Battle_of_the_Marne\" title=\"First Battle of the Marne\">First Battle of the Marne</a> begins. Northeast of Paris, the French attack and defeat German forces who are advancing on the capital.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/World_War_I\" title=\"World War I\">World War I</a>: <a href=\"https://wikipedia.org/wiki/First_Battle_of_the_Marne\" title=\"First Battle of the Marne\">First Battle of the Marne</a> begins. Northeast of Paris, the French attack and defeat German forces who are advancing on the capital.", "links": [{"title": "World War I", "link": "https://wikipedia.org/wiki/World_War_I"}, {"title": "First Battle of the Marne", "link": "https://wikipedia.org/wiki/First_Battle_of_the_Marne"}]}, {"year": "1915", "text": "The pacifist Zimmerwald Conference begins.", "html": "1915 - The pacifist <a href=\"https://wikipedia.org/wiki/Zimmerwald_Conference\" title=\"Zimmerwald Conference\">Zimmerwald Conference</a> begins.", "no_year_html": "The pacifist <a href=\"https://wikipedia.org/wiki/Zimmerwald_Conference\" title=\"Zimmerwald Conference\">Zimmerwald Conference</a> begins.", "links": [{"title": "Zimmerwald Conference", "link": "https://wikipedia.org/wiki/Zimmerwald_Conference"}]}, {"year": "1932", "text": "The French Upper Volta is broken apart between Ivory Coast, French Sudan, and Niger.", "html": "1932 - The <a href=\"https://wikipedia.org/wiki/French_Upper_Volta\" title=\"French Upper Volta\">French Upper Volta</a> is broken apart between <a href=\"https://wikipedia.org/wiki/Ivory_Coast\" title=\"Ivory Coast\">Ivory Coast</a>, <a href=\"https://wikipedia.org/wiki/French_Sudan\" title=\"French Sudan\">French Sudan</a>, and <a href=\"https://wikipedia.org/wiki/Niger\" title=\"Niger\">Niger</a>.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/French_Upper_Volta\" title=\"French Upper Volta\">French Upper Volta</a> is broken apart between <a href=\"https://wikipedia.org/wiki/Ivory_Coast\" title=\"Ivory Coast\">Ivory Coast</a>, <a href=\"https://wikipedia.org/wiki/French_Sudan\" title=\"French Sudan\">French Sudan</a>, and <a href=\"https://wikipedia.org/wiki/Niger\" title=\"Niger\">Niger</a>.", "links": [{"title": "French Upper Volta", "link": "https://wikipedia.org/wiki/French_Upper_Volta"}, {"title": "Ivory Coast", "link": "https://wikipedia.org/wiki/Ivory_Coast"}, {"title": "French Sudan", "link": "https://wikipedia.org/wiki/French_Sudan"}, {"title": "Niger", "link": "https://wikipedia.org/wiki/Niger"}]}, {"year": "1937", "text": "Spanish Civil War: Llanes falls to the Nationalists following a one-day siege.", "html": "1937 - <a href=\"https://wikipedia.org/wiki/Spanish_Civil_War\" title=\"Spanish Civil War\">Spanish Civil War</a>: <a href=\"https://wikipedia.org/wiki/Llanes\" title=\"Llanes\">Llanes</a> falls to the <a href=\"https://wikipedia.org/wiki/Nationalist_faction_(Spanish_Civil_War)\" title=\"Nationalist faction (Spanish Civil War)\">Nationalists</a> following a one-day siege.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Spanish_Civil_War\" title=\"Spanish Civil War\">Spanish Civil War</a>: <a href=\"https://wikipedia.org/wiki/Llanes\" title=\"Llanes\">Llanes</a> falls to the <a href=\"https://wikipedia.org/wiki/Nationalist_faction_(Spanish_Civil_War)\" title=\"Nationalist faction (Spanish Civil War)\">Nationalists</a> following a one-day siege.", "links": [{"title": "Spanish Civil War", "link": "https://wikipedia.org/wiki/Spanish_Civil_War"}, {"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/L<PERSON>s"}, {"title": "Nationalist faction (Spanish Civil War)", "link": "https://wikipedia.org/wiki/Nationalist_faction_(Spanish_Civil_War)"}]}, {"year": "1938", "text": "Chile: A group of youths affiliated with the fascist National Socialist Movement of Chile are executed after surrendering during a failed coup.", "html": "1938 - <a href=\"https://wikipedia.org/wiki/Chile\" title=\"Chile\">Chile</a>: A group of youths affiliated with the fascist <a href=\"https://wikipedia.org/wiki/National_Socialist_Movement_of_Chile\" title=\"National Socialist Movement of Chile\">National Socialist Movement of Chile</a> are <a href=\"https://wikipedia.org/wiki/Seguro_Obrero_massacre\" title=\"Seguro <PERSON> massacre\">executed after surrendering</a> during a failed coup.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Chile\" title=\"Chile\">Chile</a>: A group of youths affiliated with the fascist <a href=\"https://wikipedia.org/wiki/National_Socialist_Movement_of_Chile\" title=\"National Socialist Movement of Chile\">National Socialist Movement of Chile</a> are <a href=\"https://wikipedia.org/wiki/Seguro_O<PERSON>_massacre\" title=\"Seguro O<PERSON> massacre\">executed after surrendering</a> during a failed coup.", "links": [{"title": "Chile", "link": "https://wikipedia.org/wiki/Chile"}, {"title": "National Socialist Movement of Chile", "link": "https://wikipedia.org/wiki/National_Socialist_Movement_of_Chile"}, {"title": "<PERSON><PERSON><PERSON> massacre", "link": "https://wikipedia.org/wiki/Se<PERSON><PERSON>_Obrero_massacre"}]}, {"year": "1941", "text": "Whole territory of Estonia is occupied by Nazi Germany.", "html": "1941 - Whole territory of <a href=\"https://wikipedia.org/wiki/German_occupation_of_the_Baltic_states_during_World_War_II\" title=\"German occupation of the Baltic states during World War II\">Estonia is occupied</a> by Nazi Germany.", "no_year_html": "Whole territory of <a href=\"https://wikipedia.org/wiki/German_occupation_of_the_Baltic_states_during_World_War_II\" title=\"German occupation of the Baltic states during World War II\">Estonia is occupied</a> by Nazi Germany.", "links": [{"title": "German occupation of the Baltic states during World War II", "link": "https://wikipedia.org/wiki/German_occupation_of_the_Baltic_states_during_World_War_II"}]}, {"year": "1942", "text": "World War II: Japanese high command orders withdrawal at Milne Bay, the first major Japanese defeat in land warfare during the Pacific War.", "html": "1942 - <a href=\"https://wikipedia.org/wiki/World_War_II\" title=\"World War II\">World War II</a>: Japanese high command orders withdrawal at <a href=\"https://wikipedia.org/wiki/Battle_of_Milne_Bay\" title=\"Battle of Milne Bay\">Milne Bay</a>, the first major Japanese defeat in land warfare during the <a href=\"https://wikipedia.org/wiki/Pacific_War\" title=\"Pacific War\">Pacific War</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/World_War_II\" title=\"World War II\">World War II</a>: Japanese high command orders withdrawal at <a href=\"https://wikipedia.org/wiki/Battle_of_Milne_Bay\" title=\"Battle of Milne Bay\">Milne Bay</a>, the first major Japanese defeat in land warfare during the <a href=\"https://wikipedia.org/wiki/Pacific_War\" title=\"Pacific War\">Pacific War</a>.", "links": [{"title": "World War II", "link": "https://wikipedia.org/wiki/World_War_II"}, {"title": "Battle of Milne Bay", "link": "https://wikipedia.org/wiki/Battle_of_Milne_Bay"}, {"title": "Pacific War", "link": "https://wikipedia.org/wiki/Pacific_War"}]}, {"year": "1943", "text": "World War II: The 503rd Parachute Infantry Regiment lands and occupies Lae Nadzab Airport, near Lae in the Salamaua-Lae campaign.", "html": "1943 - World War II: The <a href=\"https://wikipedia.org/wiki/503rd_Infantry_Regiment\" class=\"mw-redirect\" title=\"503rd Infantry Regiment\">503rd Parachute Infantry Regiment</a> lands and occupies <a href=\"https://wikipedia.org/wiki/Lae_Nadzab_Airport\" title=\"Lae Nadzab Airport\">Lae Nadzab Airport</a>, near <a href=\"https://wikipedia.org/wiki/Lae\" title=\"Lae\">Lae</a> in the <a href=\"https://wikipedia.org/wiki/Salamaua%E2%80%93Lae_campaign\" title=\"Salamaua-Lae campaign\">Salamaua-Lae campaign</a>.", "no_year_html": "World War II: The <a href=\"https://wikipedia.org/wiki/503rd_Infantry_Regiment\" class=\"mw-redirect\" title=\"503rd Infantry Regiment\">503rd Parachute Infantry Regiment</a> lands and occupies <a href=\"https://wikipedia.org/wiki/Lae_Nadzab_Airport\" title=\"Lae Nadzab Airport\">Lae Nadzab Airport</a>, near <a href=\"https://wikipedia.org/wiki/Lae\" title=\"Lae\">Lae</a> in the <a href=\"https://wikipedia.org/wiki/Salamaua%E2%80%93Lae_campaign\" title=\"Salamaua-Lae campaign\">Salamaua-Lae campaign</a>.", "links": [{"title": "503rd Infantry Regiment", "link": "https://wikipedia.org/wiki/503rd_Infantry_Regiment"}, {"title": "Lae Nadzab Airport", "link": "https://wikipedia.org/wiki/Lae_Nadzab_Airport"}, {"title": "Lae", "link": "https://wikipedia.org/wiki/Lae"}, {"title": "Salamaua-Lae campaign", "link": "https://wikipedia.org/wiki/Salamaua%E2%80%93Lae_campaign"}]}, {"year": "1944", "text": "Belgium, Netherlands and Luxembourg constitute Benelux.", "html": "1944 - Belgium, Netherlands and <a href=\"https://wikipedia.org/wiki/Luxembourg\" title=\"Luxembourg\">Luxembourg</a> constitute <a href=\"https://wikipedia.org/wiki/Benelux\" title=\"Benelux\">Benelux</a>.", "no_year_html": "Belgium, Netherlands and <a href=\"https://wikipedia.org/wiki/Luxembourg\" title=\"Luxembourg\">Luxembourg</a> constitute <a href=\"https://wikipedia.org/wiki/Benelux\" title=\"Benelux\">Benelux</a>.", "links": [{"title": "Luxembourg", "link": "https://wikipedia.org/wiki/Luxembourg"}, {"title": "Benelux", "link": "https://wikipedia.org/wiki/Benelux"}]}, {"year": "1945", "text": "Cold War: <PERSON>, a Soviet Union embassy clerk, defects to Canada, exposing Soviet espionage in North America, signalling the beginning of the Cold War.", "html": "1945 - <a href=\"https://wikipedia.org/wiki/Cold_War\" title=\"Cold War\">Cold War</a>: <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, a <a href=\"https://wikipedia.org/wiki/Soviet_Union\" title=\"Soviet Union\">Soviet Union</a> embassy clerk, defects to Canada, exposing Soviet espionage in North America, signalling the beginning of the Cold War.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Cold_War\" title=\"Cold War\">Cold War</a>: <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, a <a href=\"https://wikipedia.org/wiki/Soviet_Union\" title=\"Soviet Union\">Soviet Union</a> embassy clerk, defects to Canada, exposing Soviet espionage in North America, signalling the beginning of the Cold War.", "links": [{"title": "Cold War", "link": "https://wikipedia.org/wiki/Cold_War"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Soviet Union", "link": "https://wikipedia.org/wiki/Soviet_Union"}]}, {"year": "1945", "text": "<PERSON><PERSON>, a Japanese American suspected of being wartime radio propagandist <PERSON> Rose, is arrested in Yokohama.", "html": "1945 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_D%27Aquino\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, a <a href=\"https://wikipedia.org/wiki/Japanese_American\" class=\"mw-redirect\" title=\"Japanese American\">Japanese American</a> suspected of being wartime radio propagandist <a href=\"https://wikipedia.org/wiki/Tokyo_Rose\" title=\"Tokyo Rose\">Tokyo Rose</a>, is arrested in <a href=\"https://wikipedia.org/wiki/Yokohama\" title=\"Yokohama\">Yokohama</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_D%27Aquino\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, a <a href=\"https://wikipedia.org/wiki/Japanese_American\" class=\"mw-redirect\" title=\"Japanese American\">Japanese American</a> suspected of being wartime radio propagandist <a href=\"https://wikipedia.org/wiki/Tokyo_Rose\" title=\"Tokyo Rose\">Tokyo Rose</a>, is arrested in <a href=\"https://wikipedia.org/wiki/Yokohama\" title=\"Yokohama\">Yokohama</a>.", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Iva_Toguri_D%27Aquino"}, {"title": "Japanese American", "link": "https://wikipedia.org/wiki/Japanese_American"}, {"title": "Tokyo Rose", "link": "https://wikipedia.org/wiki/Tokyo_Rose"}, {"title": "Yokohama", "link": "https://wikipedia.org/wiki/Yokohama"}]}, {"year": "1948", "text": "In France, <PERSON> becomes President of the Council while being Foreign minister; as such, he is the negotiator of the major treaties of the end of World War II.", "html": "1948 - In France, <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> becomes <a href=\"https://wikipedia.org/wiki/President_of_the_Council_of_Ministers\" title=\"President of the Council of Ministers\">President of the Council</a> while being <a href=\"https://wikipedia.org/wiki/Foreign_minister\" class=\"mw-redirect\" title=\"Foreign minister\">Foreign minister</a>; as such, he is the negotiator of the major treaties of the end of World War II.", "no_year_html": "In France, <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> becomes <a href=\"https://wikipedia.org/wiki/President_of_the_Council_of_Ministers\" title=\"President of the Council of Ministers\">President of the Council</a> while being <a href=\"https://wikipedia.org/wiki/Foreign_minister\" class=\"mw-redirect\" title=\"Foreign minister\">Foreign minister</a>; as such, he is the negotiator of the major treaties of the end of World War II.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "President of the Council of Ministers", "link": "https://wikipedia.org/wiki/President_of_the_Council_of_Ministers"}, {"title": "Foreign minister", "link": "https://wikipedia.org/wiki/Foreign_minister"}]}, {"year": "1954", "text": "KLM Flight 633 crashes into the River Shannon in Shannon, County Clare, Ireland, killing 28.", "html": "1954 - <a href=\"https://wikipedia.org/wiki/KLM_Flight_633\" title=\"KLM Flight 633\">KLM Flight 633</a> crashes into the <a href=\"https://wikipedia.org/wiki/River_Shannon\" title=\"River Shannon\">River Shannon</a> in <a href=\"https://wikipedia.org/wiki/Shannon,_County_Clare\" title=\"Shannon, County Clare\">Shannon, County Clare</a>, <a href=\"https://wikipedia.org/wiki/Ireland\" title=\"Ireland\">Ireland</a>, killing 28.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/KLM_Flight_633\" title=\"KLM Flight 633\">KLM Flight 633</a> crashes into the <a href=\"https://wikipedia.org/wiki/River_Shannon\" title=\"River Shannon\">River Shannon</a> in <a href=\"https://wikipedia.org/wiki/Shannon,_County_Clare\" title=\"Shannon, County Clare\">Shannon, County Clare</a>, <a href=\"https://wikipedia.org/wiki/Ireland\" title=\"Ireland\">Ireland</a>, killing 28.", "links": [{"title": "KLM Flight 633", "link": "https://wikipedia.org/wiki/KLM_Flight_633"}, {"title": "River Shannon", "link": "https://wikipedia.org/wiki/River_Shannon"}, {"title": "Shannon, County Clare", "link": "https://wikipedia.org/wiki/Shannon,_County_Clare"}, {"title": "Ireland", "link": "https://wikipedia.org/wiki/Ireland"}]}, {"year": "1957", "text": "Cuban Revolution: <PERSON><PERSON><PERSON><PERSON> bombs the revolt in Cienfuegos.", "html": "1957 - <a href=\"https://wikipedia.org/wiki/Cuban_Revolution\" title=\"Cuban Revolution\">Cuban Revolution</a>: <a href=\"https://wikipedia.org/wiki/Fulgencio_Batista\" title=\"Fulgencio Batista\">Fulgencio Batista</a> bombs the revolt in <a href=\"https://wikipedia.org/wiki/Cienfuegos\" title=\"Cienfuegos\">Cienfuegos</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Cuban_Revolution\" title=\"Cuban Revolution\">Cuban Revolution</a>: <a href=\"https://wikipedia.org/wiki/Fulgencio_Batista\" title=\"Fulgencio Batista\">Fulgencio Batista</a> bombs the revolt in <a href=\"https://wikipedia.org/wiki/Cienfuegos\" title=\"Cienfuegos\">Cienfuegos</a>.", "links": [{"title": "Cuban Revolution", "link": "https://wikipedia.org/wiki/Cuban_Revolution"}, {"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Fulgencio_Batista"}, {"title": "Cienfuegos", "link": "https://wikipedia.org/wiki/Cienfuegos"}]}, {"year": "1960", "text": "Poet <PERSON><PERSON><PERSON><PERSON> is the first elected President of Senegal.", "html": "1960 - Poet <a href=\"https://wikipedia.org/wiki/L%C3%A9opold_S%C3%A9<PERSON>_<PERSON>or\" title=\"Léopold <PERSON>\"><PERSON><PERSON><PERSON><PERSON> <PERSON></a> is the first elected <a href=\"https://wikipedia.org/wiki/President_of_Senegal\" title=\"President of Senegal\">President of Senegal</a>.", "no_year_html": "Poet <a href=\"https://wikipedia.org/wiki/L%C3%A9opold_S%C3%A9<PERSON>_<PERSON>\" title=\"Léopold <PERSON>\"><PERSON><PERSON><PERSON><PERSON> <PERSON><PERSON></a> is the first elected <a href=\"https://wikipedia.org/wiki/President_of_Senegal\" title=\"President of Senegal\">President of Senegal</a>.", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/L%C3%A9opold_S%C3%A9dar_Senghor"}, {"title": "President of Senegal", "link": "https://wikipedia.org/wiki/President_of_Senegal"}]}, {"year": "1960", "text": "<PERSON> (then known as <PERSON><PERSON>) wins the gold medal in the light heavyweight boxing competition at the Olympic Games in Rome.", "html": "1960 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> (then known as <PERSON><PERSON>) wins the gold medal in the <a href=\"https://wikipedia.org/wiki/Boxing_at_the_1960_Summer_Olympics\" title=\"Boxing at the 1960 Summer Olympics\">light heavyweight boxing competition at the Olympic Games</a> in Rome.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> (then known as <PERSON><PERSON>) wins the gold medal in the <a href=\"https://wikipedia.org/wiki/Boxing_at_the_1960_Summer_Olympics\" title=\"Boxing at the 1960 Summer Olympics\">light heavyweight boxing competition at the Olympic Games</a> in Rome.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Boxing at the 1960 Summer Olympics", "link": "https://wikipedia.org/wiki/Boxing_at_the_1960_Summer_Olympics"}]}, {"year": "1969", "text": "Mỹ Lai Massacre: U.S. Army Lieutenant <PERSON> is charged with six specifications of premeditated murder for the death of 109 Vietnamese civilians in My Lai.", "html": "1969 - <a href=\"https://wikipedia.org/wiki/M%E1%BB%B9_Lai_massacre\" class=\"mw-redirect\" title=\"Mỹ Lai massacre\">Mỹ Lai Massacre</a>: U.S. Army <a href=\"https://wikipedia.org/wiki/Lieutenant\" title=\"Lieutenant\">Lieutenant</a> <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> is charged with six specifications of premeditated murder for the death of 109 <a href=\"https://wikipedia.org/wiki/Vietnam\" title=\"Vietnam\">Vietnamese</a> civilians in <a href=\"https://wikipedia.org/wiki/My_<PERSON>\" class=\"mw-redirect\" title=\"My Lai\">My Lai</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/M%E1%BB%B9_Lai_massacre\" class=\"mw-redirect\" title=\"Mỹ Lai massacre\">Mỹ Lai Massacre</a>: U.S. Army <a href=\"https://wikipedia.org/wiki/Lieutenant\" title=\"Lieutenant\">Lieutenant</a> <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> is charged with six specifications of premeditated murder for the death of 109 <a href=\"https://wikipedia.org/wiki/Vietnam\" title=\"Vietnam\">Vietnamese</a> civilians in <a href=\"https://wikipedia.org/wiki/My_<PERSON>\" class=\"mw-redirect\" title=\"My Lai\">My Lai</a>.", "links": [{"title": "Mỹ Lai massacre", "link": "https://wikipedia.org/wiki/M%E1%BB%B9_<PERSON>_massacre"}, {"title": "Lieutenant", "link": "https://wikipedia.org/wiki/Lieutenant"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}, {"title": "Vietnam", "link": "https://wikipedia.org/wiki/Vietnam"}, {"title": "My Lai", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1970", "text": "Vietnam War: Operation <PERSON> Glenn begins: The United States 101st Airborne Division and the South Vietnamese 1st Infantry Division initiate a new operation in Thừa Thiên-Huế Province.", "html": "1970 - <a href=\"https://wikipedia.org/wiki/Vietnam_War\" title=\"Vietnam War\">Vietnam War</a>: <a href=\"https://wikipedia.org/wiki/Operation_<PERSON>_Glenn\" title=\"Operation Jefferson Glenn\">Operation <PERSON></a> begins: The <a href=\"https://wikipedia.org/wiki/101st_Airborne_Division\" title=\"101st Airborne Division\">United States 101st Airborne Division</a> and the <a href=\"https://wikipedia.org/wiki/South_Vietnam\" title=\"South Vietnam\">South Vietnamese</a> <a href=\"https://wikipedia.org/wiki/1st_Division_(South_Vietnam)\" title=\"1st Division (South Vietnam)\">1st Infantry Division</a> initiate a new operation in <a href=\"https://wikipedia.org/wiki/Hu%E1%BA%BF\" title=\"Huế\">Thừa Thiên-Huế Province</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Vietnam_War\" title=\"Vietnam War\">Vietnam War</a>: <a href=\"https://wikipedia.org/wiki/Operation_<PERSON>_Glenn\" title=\"<PERSON> Jefferson Glenn\">Operation <PERSON></a> begins: The <a href=\"https://wikipedia.org/wiki/101st_Airborne_Division\" title=\"101st Airborne Division\">United States 101st Airborne Division</a> and the <a href=\"https://wikipedia.org/wiki/South_Vietnam\" title=\"South Vietnam\">South Vietnamese</a> <a href=\"https://wikipedia.org/wiki/1st_Division_(South_Vietnam)\" title=\"1st Division (South Vietnam)\">1st Infantry Division</a> initiate a new operation in <a href=\"https://wikipedia.org/wiki/Hu%E1%BA%BF\" title=\"Huế\">Thừa Thiên-Huế Province</a>.", "links": [{"title": "Vietnam War", "link": "https://wikipedia.org/wiki/Vietnam_War"}, {"title": "Operation <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "101st Airborne Division", "link": "https://wikipedia.org/wiki/101st_Airborne_Division"}, {"title": "South Vietnam", "link": "https://wikipedia.org/wiki/South_Vietnam"}, {"title": "1st Division (South Vietnam)", "link": "https://wikipedia.org/wiki/1st_Division_(South_Vietnam)"}, {"title": "Huế", "link": "https://wikipedia.org/wiki/Hu%E1%BA%BF"}]}, {"year": "1972", "text": "Munich massacre: A Palestinian terrorist group called \"Black September\" attacks and takes hostage 11 Israeli athletes at the Munich Olympic Games. Two die in the attack and nine are murdered the following day.", "html": "1972 - <a href=\"https://wikipedia.org/wiki/Munich_massacre\" title=\"Munich massacre\">Munich massacre</a>: A <a href=\"https://wikipedia.org/wiki/Palestinian_terrorist\" class=\"mw-redirect\" title=\"Palestinian terrorist\">Palestinian terrorist</a> group called \"<a href=\"https://wikipedia.org/wiki/Black_September_Organization\" title=\"Black September Organization\">Black September</a>\" attacks and takes hostage 11 <a href=\"https://wikipedia.org/wiki/Israel\" title=\"Israel\">Israeli</a> athletes at the <a href=\"https://wikipedia.org/wiki/1972_Summer_Olympics\" title=\"1972 Summer Olympics\">Munich Olympic Games</a>. Two die in the attack and nine are murdered the following day.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Munich_massacre\" title=\"Munich massacre\">Munich massacre</a>: A <a href=\"https://wikipedia.org/wiki/Palestinian_terrorist\" class=\"mw-redirect\" title=\"Palestinian terrorist\">Palestinian terrorist</a> group called \"<a href=\"https://wikipedia.org/wiki/Black_September_Organization\" title=\"Black September Organization\">Black September</a>\" attacks and takes hostage 11 <a href=\"https://wikipedia.org/wiki/Israel\" title=\"Israel\">Israeli</a> athletes at the <a href=\"https://wikipedia.org/wiki/1972_Summer_Olympics\" title=\"1972 Summer Olympics\">Munich Olympic Games</a>. Two die in the attack and nine are murdered the following day.", "links": [{"title": "Munich massacre", "link": "https://wikipedia.org/wiki/Munich_massacre"}, {"title": "Palestinian terrorist", "link": "https://wikipedia.org/wiki/Palestinian_terrorist"}, {"title": "Black September Organization", "link": "https://wikipedia.org/wiki/Black_September_Organization"}, {"title": "Israel", "link": "https://wikipedia.org/wiki/Israel"}, {"title": "1972 Summer Olympics", "link": "https://wikipedia.org/wiki/1972_Summer_Olympics"}]}, {"year": "1975", "text": "Sacramento, California: <PERSON><PERSON><PERSON> attempts to assassinate U.S. President <PERSON>.", "html": "1975 - <a href=\"https://wikipedia.org/wiki/Sacramento,_California\" title=\"Sacramento, California\">Sacramento, California</a>: <a href=\"https://wikipedia.org/wiki/Lynette_Fromme\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON> Fromme\"><PERSON><PERSON><PERSON></a> <a href=\"https://wikipedia.org/wiki/Attempted_assassination_of_<PERSON>_<PERSON>_in_Sacramento\" title=\"Attempted assassination of <PERSON> in Sacramento\">attempts to assassinate</a> U.S. President <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Sacramento,_California\" title=\"Sacramento, California\">Sacramento, California</a>: <a href=\"https://wikipedia.org/wiki/Lynette_Fromme\" class=\"mw-redirect\" title=\"<PERSON>ynette Fromme\"><PERSON><PERSON><PERSON></a> <a href=\"https://wikipedia.org/wiki/Attempted_assassination_of_<PERSON>_<PERSON>_in_Sacramento\" title=\"Attempted assassination of <PERSON> in Sacramento\">attempts to assassinate</a> U.S. President <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>.", "links": [{"title": "Sacramento, California", "link": "https://wikipedia.org/wiki/Sacramento,_California"}, {"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>ynette_Fromme"}, {"title": "Attempted assassination of <PERSON> in Sacramento", "link": "https://wikipedia.org/wiki/Attempted_assassination_of_<PERSON>_<PERSON>_in_Sacramento"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1977", "text": "Voyager Program: NASA launches the Voyager 1 spacecraft.", "html": "1977 - <a href=\"https://wikipedia.org/wiki/Voyager_Program\" class=\"mw-redirect\" title=\"Voyager Program\">Voyager Program</a>: <a href=\"https://wikipedia.org/wiki/NASA\" title=\"NASA\">NASA</a> launches the <i><a href=\"https://wikipedia.org/wiki/Voyager_1\" title=\"Voyager 1\">Voyager 1</a></i> spacecraft.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Voyager_Program\" class=\"mw-redirect\" title=\"Voyager Program\">Voyager Program</a>: <a href=\"https://wikipedia.org/wiki/NASA\" title=\"NASA\">NASA</a> launches the <i><a href=\"https://wikipedia.org/wiki/Voyager_1\" title=\"Voyager 1\">Voyager 1</a></i> spacecraft.", "links": [{"title": "Voyager Program", "link": "https://wikipedia.org/wiki/Voyager_Program"}, {"title": "NASA", "link": "https://wikipedia.org/wiki/NASA"}, {"title": "Voyager 1", "link": "https://wikipedia.org/wiki/Voyager_1"}]}, {"year": "1978", "text": "Camp David Accords: <PERSON><PERSON><PERSON> and <PERSON><PERSON> begin peace discussions at Camp David, Maryland.", "html": "1978 - <a href=\"https://wikipedia.org/wiki/Camp_<PERSON>_Accords\" title=\"Camp David Accords\">Camp David Accords</a>: <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_Begin\" title=\"<PERSON><PERSON><PERSON> Begin\"><PERSON><PERSON><PERSON></a> and <a href=\"https://wikipedia.org/wiki/Anwar_Sadat\" title=\"Anwar Sadat\">Anwar Sadat</a> begin peace discussions at <a href=\"https://wikipedia.org/wiki/Camp_David\" title=\"Camp David\">Camp David, Maryland</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Camp_<PERSON>_Accords\" title=\"Camp David Accords\">Camp David Accords</a>: <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_Begin\" title=\"<PERSON><PERSON><PERSON> Begin\"><PERSON><PERSON><PERSON></a> and <a href=\"https://wikipedia.org/wiki/Anwar_Sadat\" title=\"Anwar Sadat\">Anwar Sadat</a> begin peace discussions at <a href=\"https://wikipedia.org/wiki/Camp_David\" title=\"Camp David\">Camp David, Maryland</a>.", "links": [{"title": "Camp David Accords", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>gin"}, {"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>at"}, {"title": "Camp David", "link": "https://wikipedia.org/wiki/Camp_David"}]}, {"year": "1980", "text": "The Gotthard Road Tunnel opens in Switzerland as the world's longest highway tunnel at 10.14 miles (16.32 km) stretching from Göschenen to Airolo.", "html": "1980 - The <a href=\"https://wikipedia.org/wiki/Gotthard_Road_Tunnel\" title=\"Gotthard Road Tunnel\">Gotthard Road Tunnel</a> opens in Switzerland as the world's longest highway tunnel at 10.14 miles (16.32 km) stretching from <a href=\"https://wikipedia.org/wiki/G%C3%B6schenen\" title=\"Göschenen\">Göschenen</a> to <a href=\"https://wikipedia.org/wiki/Airolo\" title=\"Airolo\">Airolo</a>.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Gotthard_Road_Tunnel\" title=\"Gotthard Road Tunnel\">Gotthard Road Tunnel</a> opens in Switzerland as the world's longest highway tunnel at 10.14 miles (16.32 km) stretching from <a href=\"https://wikipedia.org/wiki/G%C3%B6schenen\" title=\"Göschenen\">Göschenen</a> to <a href=\"https://wikipedia.org/wiki/Airolo\" title=\"Airolo\">Airolo</a>.", "links": [{"title": "Gotthard Road Tunnel", "link": "https://wikipedia.org/wiki/Gotthard_Road_Tunnel"}, {"title": "Göschenen", "link": "https://wikipedia.org/wiki/G%C3%B6schenen"}, {"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Airolo"}]}, {"year": "1981", "text": "The first women arrive at what becomes Greenham Common Women's Peace Camp in the UK.", "html": "1981 - The first women arrive at what becomes <a href=\"https://wikipedia.org/wiki/Greenham_Common_Women%27s_Peace_Camp\" title=\"Greenham Common Women's Peace Camp\">Greenham Common Women's Peace Camp</a> in the UK.", "no_year_html": "The first women arrive at what becomes <a href=\"https://wikipedia.org/wiki/Greenham_Common_Women%27s_Peace_Camp\" title=\"Greenham Common Women's Peace Camp\">Greenham Common Women's Peace Camp</a> in the UK.", "links": [{"title": "Greenham Common Women's Peace Camp", "link": "https://wikipedia.org/wiki/Greenham_Common_Women%27s_Peace_Camp"}]}, {"year": "1984", "text": "STS-41-D: The Space Shuttle Discovery lands after its maiden voyage.", "html": "1984 - <a href=\"https://wikipedia.org/wiki/STS-41-D\" title=\"STS-41-D\">STS-41-D</a>: The <a href=\"https://wikipedia.org/wiki/Space_Shuttle_Discovery\" title=\"Space Shuttle Discovery\">Space Shuttle <i>Discovery</i></a> lands after its maiden voyage.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/STS-41-D\" title=\"STS-41-D\">STS-41-D</a>: The <a href=\"https://wikipedia.org/wiki/Space_Shuttle_Discovery\" title=\"Space Shuttle Discovery\">Space Shuttle <i>Discovery</i></a> lands after its maiden voyage.", "links": [{"title": "STS-41-D", "link": "https://wikipedia.org/wiki/STS-41-D"}, {"title": "Space Shuttle Discovery", "link": "https://wikipedia.org/wiki/Space_Shuttle_Discovery"}]}, {"year": "1984", "text": "Western Australia becomes the last Australian state to abolish capital punishment.", "html": "1984 - <a href=\"https://wikipedia.org/wiki/Western_Australia\" title=\"Western Australia\">Western Australia</a> becomes the last Australian state to abolish <a href=\"https://wikipedia.org/wiki/Capital_punishment\" title=\"Capital punishment\">capital punishment</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Western_Australia\" title=\"Western Australia\">Western Australia</a> becomes the last Australian state to abolish <a href=\"https://wikipedia.org/wiki/Capital_punishment\" title=\"Capital punishment\">capital punishment</a>.", "links": [{"title": "Western Australia", "link": "https://wikipedia.org/wiki/Western_Australia"}, {"title": "Capital punishment", "link": "https://wikipedia.org/wiki/Capital_punishment"}]}, {"year": "1986", "text": "Pan Am Flight 73 from Mumbai, India with 358 people on board is hijacked at Karachi International Airport.", "html": "1986 - <a href=\"https://wikipedia.org/wiki/Pan_Am_Flight_73\" title=\"Pan Am Flight 73\">Pan Am Flight 73</a> from <a href=\"https://wikipedia.org/wiki/Mumbai\" title=\"Mumbai\">Mumbai</a>, <a href=\"https://wikipedia.org/wiki/India\" title=\"India\">India</a> with 358 people on board is <a href=\"https://wikipedia.org/wiki/Aircraft_hijacking\" title=\"Aircraft hijacking\">hijacked</a> at <a href=\"https://wikipedia.org/wiki/Karachi_International_Airport\" class=\"mw-redirect\" title=\"Karachi International Airport\">Karachi International Airport</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Pan_Am_Flight_73\" title=\"Pan Am Flight 73\">Pan Am Flight 73</a> from <a href=\"https://wikipedia.org/wiki/Mumbai\" title=\"Mumbai\">Mumbai</a>, <a href=\"https://wikipedia.org/wiki/India\" title=\"India\">India</a> with 358 people on board is <a href=\"https://wikipedia.org/wiki/Aircraft_hijacking\" title=\"Aircraft hijacking\">hijacked</a> at <a href=\"https://wikipedia.org/wiki/Karachi_International_Airport\" class=\"mw-redirect\" title=\"Karachi International Airport\">Karachi International Airport</a>.", "links": [{"title": "Pan Am Flight 73", "link": "https://wikipedia.org/wiki/Pan_Am_Flight_73"}, {"title": "Mumbai", "link": "https://wikipedia.org/wiki/Mumbai"}, {"title": "India", "link": "https://wikipedia.org/wiki/India"}, {"title": "Aircraft hijacking", "link": "https://wikipedia.org/wiki/Aircraft_hijacking"}, {"title": "Karachi International Airport", "link": "https://wikipedia.org/wiki/Karachi_International_Airport"}]}, {"year": "1990", "text": "Sri Lankan Civil War: Sri Lankan Army soldiers slaughter 158 civilians.", "html": "1990 - <a href=\"https://wikipedia.org/wiki/Sri_Lankan_civil_war\" title=\"Sri Lankan civil war\">Sri Lankan Civil War</a>: Sri Lankan Army soldiers <a href=\"https://wikipedia.org/wiki/Eastern_University_massacre\" title=\"Eastern University massacre\">slaughter</a> 158 civilians.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Sri_Lankan_civil_war\" title=\"Sri Lankan civil war\">Sri Lankan Civil War</a>: Sri Lankan Army soldiers <a href=\"https://wikipedia.org/wiki/Eastern_University_massacre\" title=\"Eastern University massacre\">slaughter</a> 158 civilians.", "links": [{"title": "Sri Lankan civil war", "link": "https://wikipedia.org/wiki/Sri_Lankan_civil_war"}, {"title": "Eastern University massacre", "link": "https://wikipedia.org/wiki/Eastern_University_massacre"}]}, {"year": "1991", "text": "The current international treaty defending indigenous peoples, Indigenous and Tribal Peoples Convention, 1989, comes into force.", "html": "1991 - The current international treaty defending indigenous peoples, <a href=\"https://wikipedia.org/wiki/Indigenous_and_Tribal_Peoples_Convention,_1989\" title=\"Indigenous and Tribal Peoples Convention, 1989\">Indigenous and Tribal Peoples Convention, 1989</a>, comes into force.", "no_year_html": "The current international treaty defending indigenous peoples, <a href=\"https://wikipedia.org/wiki/Indigenous_and_Tribal_Peoples_Convention,_1989\" title=\"Indigenous and Tribal Peoples Convention, 1989\">Indigenous and Tribal Peoples Convention, 1989</a>, comes into force.", "links": [{"title": "Indigenous and Tribal Peoples Convention, 1989", "link": "https://wikipedia.org/wiki/Indigenous_and_Tribal_Peoples_Convention,_1989"}]}, {"year": "1996", "text": "Hurricane <PERSON><PERSON> makes landfall near Cape Fear, North Carolina as a Category 3 storm with 115 mph sustained winds. <PERSON><PERSON> caused over $3 billion in damage and killed 27 people.", "html": "1996 - <a href=\"https://wikipedia.org/wiki/Hurricane_Fran\" title=\"Hurricane Fran\">Hurricane <PERSON><PERSON></a> makes landfall near <a href=\"https://wikipedia.org/wiki/Cape_Fear_(headland)\" title=\"Cape Fear (headland)\">Cape Fear</a>, <a href=\"https://wikipedia.org/wiki/North_Carolina\" title=\"North Carolina\">North Carolina</a> as a <a href=\"https://wikipedia.org/wiki/Tropical_cyclone\" title=\"Tropical cyclone\">Category 3</a> storm with 115 mph sustained winds. <PERSON><PERSON> caused over $3 billion in damage and killed 27 people.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Hurricane_Fran\" title=\"Hurricane Fran\">Hurricane <PERSON><PERSON></a> makes landfall near <a href=\"https://wikipedia.org/wiki/Cape_Fear_(headland)\" title=\"Cape Fear (headland)\">Cape Fear</a>, <a href=\"https://wikipedia.org/wiki/North_Carolina\" title=\"North Carolina\">North Carolina</a> as a <a href=\"https://wikipedia.org/wiki/Tropical_cyclone\" title=\"Tropical cyclone\">Category 3</a> storm with 115 mph sustained winds. <PERSON><PERSON> caused over $3 billion in damage and killed 27 people.", "links": [{"title": "Hurricane <PERSON>an", "link": "https://wikipedia.org/wiki/Hurricane_Fran"}, {"title": "Cape Fear (headland)", "link": "https://wikipedia.org/wiki/Cape_Fear_(headland)"}, {"title": "North Carolina", "link": "https://wikipedia.org/wiki/North_Carolina"}, {"title": "Tropical cyclone", "link": "https://wikipedia.org/wiki/Tropical_cyclone"}]}, {"year": "2005", "text": "Mandala Airlines Flight 091 crashes after takeoff from Polonia International Airport in Medan, Indonesia, killing 149.", "html": "2005 - <a href=\"https://wikipedia.org/wiki/Mandala_Airlines_Flight_091\" title=\"Mandala Airlines Flight 091\">Mandala Airlines Flight 091</a> crashes after takeoff from <a href=\"https://wikipedia.org/wiki/Soewondo_Air_Force_Base\" title=\"Soewondo Air Force Base\">Polonia International Airport</a> in <a href=\"https://wikipedia.org/wiki/Medan\" title=\"Medan\">Medan</a>, Indonesia, killing 149.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Mandala_Airlines_Flight_091\" title=\"Mandala Airlines Flight 091\">Mandala Airlines Flight 091</a> crashes after takeoff from <a href=\"https://wikipedia.org/wiki/Soewondo_Air_Force_Base\" title=\"Soewondo Air Force Base\">Polonia International Airport</a> in <a href=\"https://wikipedia.org/wiki/Medan\" title=\"Medan\">Medan</a>, Indonesia, killing 149.", "links": [{"title": "Mandala Airlines Flight 091", "link": "https://wikipedia.org/wiki/Mandala_Airlines_Flight_091"}, {"title": "Soewondo Air Force Base", "link": "https://wikipedia.org/wiki/Soewondo_Air_Force_Base"}, {"title": "Medan", "link": "https://wikipedia.org/wiki/Medan"}]}, {"year": "2012", "text": "An accidental explosion at a Turkish Army ammunition store in Afyon, western Turkey kills 25 soldiers and wounds four others.", "html": "2012 - An <a href=\"https://wikipedia.org/wiki/2012_Afyonkarahisar_arsenal_explosion\" title=\"2012 Afyonkarahisar arsenal explosion\">accidental explosion</a> at a Turkish Army ammunition store in <a href=\"https://wikipedia.org/wiki/Afyonkarahisar\" title=\"Afyonkarahisar\">Afyon</a>, western Turkey kills 25 soldiers and wounds four others.", "no_year_html": "An <a href=\"https://wikipedia.org/wiki/2012_Afyonkarahisar_arsenal_explosion\" title=\"2012 Afyonkarahisar arsenal explosion\">accidental explosion</a> at a Turkish Army ammunition store in <a href=\"https://wikipedia.org/wiki/Afyonkarahisar\" title=\"Afyonkarahisar\">Afyon</a>, western Turkey kills 25 soldiers and wounds four others.", "links": [{"title": "2012 Afyonkarahisar arsenal explosion", "link": "https://wikipedia.org/wiki/2012_Afyonkarahisar_arsenal_explosion"}, {"title": "Afyonkarahisar", "link": "https://wikipedia.org/wiki/Afyonkarahisar"}]}, {"year": "2021", "text": "The President of Guinea, <PERSON> Condé is captured by armed forces during a coup d'état.", "html": "2021 - The President of Guinea, <a href=\"https://wikipedia.org/wiki/Alpha_Cond%C3%A9\" title=\"Alpha Condé\">Alpha Condé</a> is captured by armed forces during a <a href=\"https://wikipedia.org/wiki/2021_Guinean_coup_d%27%C3%A9tat\" title=\"2021 Guinean coup d'état\">coup d'état</a>.", "no_year_html": "The President of Guinea, <a href=\"https://wikipedia.org/wiki/Alpha_Cond%C3%A9\" title=\"Alpha Condé\">Alpha Condé</a> is captured by armed forces during a <a href=\"https://wikipedia.org/wiki/2021_Guinean_coup_d%27%C3%A9tat\" title=\"2021 Guinean coup d'état\">coup d'état</a>.", "links": [{"title": "Alpha Condé", "link": "https://wikipedia.org/wiki/Alpha_Cond%C3%A9"}, {"title": "2021 Guinean coup d'état", "link": "https://wikipedia.org/wiki/2021_Guinean_coup_d%27%C3%A9tat"}]}, {"year": "2022", "text": "<PERSON> is declared the winner of the UK Conservative Party leadership election, beating <PERSON><PERSON>", "html": "2022 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> is declared the winner of the <a href=\"https://wikipedia.org/wiki/July%E2%80%93September_2022_Conservative_Party_leadership_election\" title=\"July-September 2022 Conservative Party leadership election\">UK Conservative Party leadership election</a>, beating <a href=\"https://wikipedia.org/wiki/Rishi_<PERSON>ak\" title=\"Rishi Sunak\"><PERSON><PERSON></a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> is declared the winner of the <a href=\"https://wikipedia.org/wiki/July%E2%80%93September_2022_Conservative_Party_leadership_election\" title=\"July-September 2022 Conservative Party leadership election\">UK Conservative Party leadership election</a>, beating <a href=\"https://wikipedia.org/wiki/Rishi_<PERSON>ak\" title=\"Rishi Sunak\"><PERSON><PERSON></a>", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "July-September 2022 Conservative Party leadership election", "link": "https://wikipedia.org/wiki/July%E2%80%93September_2022_Conservative_Party_leadership_election"}, {"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>ak"}]}, {"year": "2022", "text": "At least 93 people die and 25 are missing after a magnitude 6.8 earthquake strikes Sichuan, China.", "html": "2022 - At least 93 people die and 25 are missing after a <a href=\"https://wikipedia.org/wiki/2022_Luding_earthquake\" title=\"2022 Luding earthquake\">magnitude 6.8 earthquake</a> strikes <a href=\"https://wikipedia.org/wiki/Sichuan\" title=\"Sichuan\">Sichuan</a>, China.", "no_year_html": "At least 93 people die and 25 are missing after a <a href=\"https://wikipedia.org/wiki/2022_Luding_earthquake\" title=\"2022 Luding earthquake\">magnitude 6.8 earthquake</a> strikes <a href=\"https://wikipedia.org/wiki/Sichuan\" title=\"Sichuan\">Sichuan</a>, China.", "links": [{"title": "2022 Luding earthquake", "link": "https://wikipedia.org/wiki/2022_Luding_earthquake"}, {"title": "Sichuan", "link": "https://wikipedia.org/wiki/Sichuan"}]}], "Births": [{"year": "989", "text": "<PERSON>, Chinese chancellor (d. 1052)", "html": "989 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Chinese chancellor (d. 1052)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Chinese chancellor (d. 1052)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1187", "text": "<PERSON>, king of France (d. 1226)", "html": "1187 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_France\" title=\"<PERSON> VIII of France\"><PERSON></a>, king of France (d. 1226)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_France\" title=\"<PERSON> VIII of France\"><PERSON></a>, king of France (d. 1226)", "links": [{"title": "<PERSON> of France", "link": "https://wikipedia.org/wiki/Louis_VIII_of_France"}]}, {"year": "1201", "text": "<PERSON><PERSON> <PERSON> Thouars, duchess of Brittany (d. 1221)", "html": "1201 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>,_Duchess_of_Brittany\" title=\"<PERSON><PERSON>, Duchess of Brittany\"><PERSON><PERSON> of Thouars</a>, duchess of Brittany (d. 1221)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>,_Duchess_of_Brittany\" title=\"<PERSON><PERSON>, Duchess of Brittany\"><PERSON><PERSON> of Thouars</a>, duchess of Brittany (d. 1221)", "links": [{"title": "<PERSON><PERSON>, Duchess of Brittany", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>,_Duchess_of_Brittany"}]}, {"year": "1319", "text": "<PERSON>, king of Aragon (d. 1387)", "html": "1319 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Aragon\" title=\"<PERSON> of Aragon\"><PERSON></a>, king of Aragon (d. 1387)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_Aragon\" title=\"<PERSON> of Aragon\"><PERSON></a>, king of Aragon (d. 1387)", "links": [{"title": "<PERSON> of Aragon", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_Aragon"}]}, {"year": "1451", "text": "<PERSON>, daughter of <PERSON> (d. 1476)", "html": "1451 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Duchess_of_<PERSON>\" title=\"<PERSON>, Duchess of Clarence\"><PERSON></a>, daughter of <PERSON> (d. 1476)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Duchess_of_<PERSON>\" title=\"<PERSON>, Duchess of Clarence\"><PERSON></a>, daughter of <PERSON> (d. 1476)", "links": [{"title": "<PERSON>, Duchess of Clarence", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Duchess_of_<PERSON>"}]}, {"year": "1500", "text": "<PERSON> Jever, ruler of the Lordship of Jever (d. 1575)", "html": "1500 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_Je<PERSON>\" title=\"<PERSON> of Jever\"><PERSON> of Jever</a>, ruler of the Lordship of Jever (d. 1575)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_Je<PERSON>\" title=\"<PERSON> of Jever\"><PERSON> of Jever</a>, ruler of the Lordship of Jever (d. 1575)", "links": [{"title": "<PERSON> of Jever", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_Je<PERSON>"}]}, {"year": "1533", "text": "<PERSON><PERSON><PERSON>, Italian philosopher and logician (d. 1589)", "html": "1533 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Italian philosopher and logician (d. 1589)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Italian philosopher and logician (d. 1589)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1540", "text": "<PERSON> of Holstein, prince of Denmark  (d. 1583)", "html": "1540 - <a href=\"https://wikipedia.org/wiki/<PERSON>,_Duke_of_Holstein\" title=\"<PERSON>, Duke of Holstein\"><PERSON> of Holstein</a>, prince of Denmark (d. 1583)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>,_Duke_of_Holstein\" title=\"<PERSON>, Duke of Holstein\"><PERSON> of Holstein</a>, prince of Denmark (d. 1583)", "links": [{"title": "<PERSON>, Duke of Holstein", "link": "https://wikipedia.org/wiki/<PERSON>,_Duke_of_Holstein"}]}, {"year": "1567", "text": "<PERSON> <PERSON><PERSON><PERSON><PERSON>, Japanese daimyō (d. 1636)", "html": "1567 - <a href=\"https://wikipedia.org/wiki/Date_Masamune\" title=\"Date Masamune\">Date <PERSON><PERSON><PERSON><PERSON></a>, Japanese daimyō (d. 1636)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Date_Masamune\" title=\"Date Masamune\">Date <PERSON><PERSON><PERSON><PERSON></a>, Japanese daimyō (d. 1636)", "links": [{"title": "Date Ma<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Date_<PERSON><PERSON><PERSON><PERSON>"}]}, {"year": "1568", "text": "<PERSON><PERSON><PERSON>, Italian poet, philosopher, and theologian (d. 1639)", "html": "1568 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_Camp<PERSON>lla\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Italian poet, philosopher, and theologian (d. 1639)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>lla\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Italian poet, philosopher, and theologian (d. 1639)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>lla"}]}, {"year": "1638", "text": "<PERSON>, king of France (d. 1715)", "html": "1638 - <a href=\"https://wikipedia.org/wiki/<PERSON>_XIV_of_France\" class=\"mw-redirect\" title=\"<PERSON> XIV of France\"><PERSON></a>, king of France (d. 1715)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Louis_XIV_of_France\" class=\"mw-redirect\" title=\"<PERSON> XIV of France\"><PERSON></a>, king of France (d. 1715)", "links": [{"title": "<PERSON> of France", "link": "https://wikipedia.org/wiki/Louis_XIV_of_France"}]}, {"year": "1641", "text": "<PERSON>, 2nd Earl of Sunderland, English diplomat (d. 1702)", "html": "1641 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_2nd_Earl_of_Sunderland\" title=\"<PERSON>, 2nd Earl of Sunderland\"><PERSON>, 2nd Earl of Sunderland</a>, English diplomat (d. 1702)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_2nd_Earl_of_Sunderland\" title=\"<PERSON>, 2nd Earl of Sunderland\"><PERSON>, 2nd Earl of Sunderland</a>, English diplomat (d. 1702)", "links": [{"title": "<PERSON>, 2nd Earl of Sunderland", "link": "https://wikipedia.org/wiki/<PERSON>,_2nd_Earl_of_Sunderland"}]}, {"year": "1642", "text": "<PERSON> of Orange-Nassau, Dutch princess (d. 1688)", "html": "1642 - <a href=\"https://wikipedia.org/wiki/Maria_of_Orange-Nassau_(1642%E2%80%931688)\" class=\"mw-redirect\" title=\"<PERSON> of Orange-Nassau (1642-1688)\"><PERSON> of Orange-Nassau</a>, Dutch princess (d. 1688)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Maria_of_Orange-Nassau_(1642%E2%80%931688)\" class=\"mw-redirect\" title=\"<PERSON> of Orange-Nassau (1642-1688)\"><PERSON> of Orange-Nassau</a>, Dutch princess (d. 1688)", "links": [{"title": "<PERSON> of Orange-Nassau (1642-1688)", "link": "https://wikipedia.org/wiki/Maria_of_Orange-Nassau_(1642%E2%80%931688)"}]}, {"year": "1651", "text": "<PERSON>, English explorer (d. 1715)", "html": "1651 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English explorer (d. 1715)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English explorer (d. 1715)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1666", "text": "<PERSON><PERSON><PERSON>, German historian and theologian (d. 1714)", "html": "1666 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, German historian and theologian (d. 1714)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, German historian and theologian (d. 1714)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1667", "text": "<PERSON>, Italian priest, mathematician, and philosopher (d. 1733)", "html": "1667 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian priest, mathematician, and philosopher (d. 1733)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian priest, mathematician, and philosopher (d. 1733)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>i"}]}, {"year": "1694", "text": "<PERSON><PERSON><PERSON><PERSON>, Czech conductor and composer (d. 1744)", "html": "1694 - <a href=\"https://wikipedia.org/wiki/Franti%C5%A1ek_V%C3%A1clav_M%C3%AD%C4%8Da\" class=\"mw-redirect\" title=\"Fr<PERSON>š<PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Czech conductor and composer (d. 1744)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Franti%C5%A1ek_V%C3%A1clav_M%C3%AD%C4%8Da\" class=\"mw-redirect\" title=\"Fr<PERSON>šek <PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Czech conductor and composer (d. 1744)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Franti%C5%A1ek_V%C3%A1clav_M%C3%AD%C4%8Da"}]}, {"year": "1695", "text": "<PERSON>, Swedish politician and diplomat (d. 1770)", "html": "1695 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swedish politician and diplomat (d. 1770)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swedish politician and diplomat (d. 1770)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1722", "text": "<PERSON>, Prince-Elector of Saxony (d. 1763)", "html": "1722 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Elector_of_Saxony\" title=\"<PERSON>, Elector of Saxony\"><PERSON></a>, Prince-Elector of Saxony (d. 1763)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Elector_of_Saxony\" title=\"<PERSON>, Elector of Saxony\"><PERSON></a>, Prince<PERSON>Elector of Saxony (d. 1763)", "links": [{"title": "<PERSON>, Elector of Saxony", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Elector_of_Saxony"}]}, {"year": "1725", "text": "<PERSON><PERSON><PERSON>, French mathematician and theorist (d. 1799)", "html": "1725 - <a href=\"https://wikipedia.org/wiki/<PERSON>-%C3%89tien<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, French mathematician and theorist (d. 1799)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>%C3%89tien<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, French mathematician and theorist (d. 1799)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>-%C3%89tien<PERSON>_<PERSON>"}]}, {"year": "1735", "text": "<PERSON>, German-English viol player and composer (d. 1782)", "html": "1735 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German-English <a href=\"https://wikipedia.org/wiki/Viol\" title=\"Viol\">viol</a> player and composer (d. 1782)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German-English <a href=\"https://wikipedia.org/wiki/Viol\" title=\"Viol\">viol</a> player and composer (d. 1782)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "Viol", "link": "https://wikipedia.org/wiki/Viol"}]}, {"year": "1750", "text": "<PERSON>, Scottish poet and author (d. 1774)", "html": "1750 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish poet and author (d. 1774)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish poet and author (d. 1774)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1769", "text": "<PERSON>, English commander (d. 1810)", "html": "1769 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English commander (d. 1810)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English commander (d. 1810)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1771", "text": "<PERSON><PERSON><PERSON>, Duke of Teschen (d. 1847)", "html": "1771 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>,_Duke_of_Teschen\" title=\"<PERSON><PERSON><PERSON>, Duke of Teschen\"><PERSON><PERSON><PERSON>, Duke of Teschen</a> (d. 1847)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>,_Duke_of_Teschen\" title=\"<PERSON><PERSON><PERSON>, Duke of Teschen\"><PERSON><PERSON><PERSON>, Duke of Teschen</a> (d. 1847)", "links": [{"title": "<PERSON><PERSON><PERSON>, Duke of Teschen", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>,_Duke_of_Teschen"}]}, {"year": "1772", "text": "<PERSON><PERSON><PERSON><PERSON>, Iranian king (d. 1834)", "html": "1772 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>-<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Iranian king (d. 1834)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>-<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Iranian king (d. 1834)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>-<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1774", "text": "<PERSON><PERSON><PERSON> <PERSON>, German painter and etcher (d. 1840)", "html": "1774 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON> <PERSON>\"><PERSON><PERSON><PERSON> <PERSON></a>, German painter and etcher (d. 1840)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON> <PERSON></a>, German painter and etcher (d. 1840)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1775", "text": "<PERSON>, Spanish general (d. 1825)", "html": "1775 - <a href=\"https://wikipedia.org/wiki/Juan_Mart%C3%ADn_D%C3%ADez\" title=\"<PERSON>\"><PERSON></a>, Spanish general (d. 1825)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Juan_Mart%C3%ADn_D%C3%ADez\" title=\"<PERSON>\"><PERSON></a>, Spanish general (d. 1825)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Juan_Mart%C3%ADn_D%C3%ADez"}]}, {"year": "1781", "text": "<PERSON>, Austrian composer and publisher (d. 1858)", "html": "1781 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Austrian composer and publisher (d. 1858)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Austrian composer and publisher (d. 1858)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1787", "text": "<PERSON>, French mineralogist and geologist (d. 1850)", "html": "1787 - <a href=\"https://wikipedia.org/wiki/Fran%C3%A7ois_Sulpice_Beudant\" title=\"François Sulpice Beudant\"><PERSON></a>, French mineralogist and geologist (d. 1850)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Fran%C3%A7ois_Sulpice_Beudant\" title=\"François Sulpice Beudant\"><PERSON></a>, French mineralogist and geologist (d. 1850)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Fran%C3%A7ois_Sulpice_Beudant"}]}, {"year": "1791", "text": "<PERSON>, German pianist and composer (d. 1864)", "html": "1791 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German pianist and composer (d. 1864)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German pianist and composer (d. 1864)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1792", "text": "Ours<PERSON><PERSON><PERSON><PERSON>, French geologist and mineralogist (d. 1857)", "html": "1792 - <a href=\"https://wikipedia.org/wiki/Our<PERSON>-<PERSON>-<PERSON>%C3%A9noy\" title=\"Our<PERSON>-<PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON><PERSON></a>, French geologist and mineralogist (d. 1857)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Our<PERSON>-<PERSON>-<PERSON>_<PERSON>-<PERSON>%C3%A9noy\" title=\"Our<PERSON>-<PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON><PERSON></a>, French geologist and mineralogist (d. 1857)", "links": [{"title": "Ours-<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Ours-<PERSON>-<PERSON>_<PERSON>-Dufr%C3%A9noy"}]}, {"year": "1806", "text": "<PERSON>, French general and politician, French Minister of War (d. 1865)", "html": "1806 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A<PERSON><PERSON>_<PERSON>_<PERSON>_<PERSON>_<PERSON>%C3%A8re\" class=\"mw-redirect\" title=\"<PERSON> Lamori<PERSON>\"><PERSON></a>, French general and politician, <a href=\"https://wikipedia.org/wiki/Minister_of_Defence_(France)\" class=\"mw-redirect\" title=\"Minister of Defence (France)\">French Minister of War</a> (d. 1865)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A<PERSON><PERSON>_<PERSON>_<PERSON>_<PERSON>%C3%A8re\" class=\"mw-redirect\" title=\"<PERSON> Lamorici<PERSON>\"><PERSON></a>, French general and politician, <a href=\"https://wikipedia.org/wiki/Minister_of_Defence_(France)\" class=\"mw-redirect\" title=\"Minister of Defence (France)\">French Minister of War</a> (d. 1865)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A<PERSON><PERSON>_<PERSON>_<PERSON>_<PERSON>_<PERSON>%C3%A8re"}, {"title": "Minister of Defence (France)", "link": "https://wikipedia.org/wiki/Minister_of_Defence_(France)"}]}, {"year": "1817", "text": "<PERSON><PERSON><PERSON>, Russian poet, author, and playwright (d. 1875)", "html": "1817 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Russian poet, author, and playwright (d. 1875)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Russian poet, author, and playwright (d. 1875)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1818", "text": "<PERSON>, Australian explorer and surveyor (d. 1848)", "html": "1818 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian explorer and surveyor (d. 1848)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian explorer and surveyor (d. 1848)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1826", "text": "<PERSON>, English cricketer and businessman (d. 1884)", "html": "1826 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English cricketer and businessman (d. 1884)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English cricketer and businessman (d. 1884)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1827", "text": "<PERSON><PERSON><PERSON>, Italian poet and songwriter (d. 1849)", "html": "1827 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Italian poet and songwriter (d. 1849)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Italian poet and songwriter (d. 1849)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1829", "text": "<PERSON>, American inventor (d. 1908)", "html": "1829 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American inventor (d. 1908)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American inventor (d. 1908)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1831", "text": "<PERSON><PERSON>, French author and playwright (d. 1908)", "html": "1831 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, French author and playwright (d. 1908)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, French author and playwright (d. 1908)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1833", "text": "<PERSON>, American businessman (d. 1917)", "html": "1833 - <a href=\"https://wikipedia.org/wiki/<PERSON>_Huntington_Hartford\" title=\"George Huntington Hartford\"><PERSON></a>, American businessman (d. 1917)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/George_Huntington_Hartford\" title=\"George Huntington Hartford\"><PERSON></a>, American businessman (d. 1917)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_Huntington_Hartford"}]}, {"year": "1836", "text": "<PERSON><PERSON>, Peruvian soldier and politician, 57th President of Peru (d. 1921)", "html": "1836 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>%C3%B1o\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Peruvian soldier and politician, 57th <a href=\"https://wikipedia.org/wiki/President_of_Peru\" title=\"President of Peru\">President of Peru</a> (d. 1921)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>%C3%B1o\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Peruvian soldier and politician, 57th <a href=\"https://wikipedia.org/wiki/President_of_Peru\" title=\"President of Peru\">President of Peru</a> (d. 1921)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_Borgo%C3%B1o"}, {"title": "President of Peru", "link": "https://wikipedia.org/wiki/President_of_Peru"}]}, {"year": "1847", "text": "<PERSON>, American outlaw (d. 1882)", "html": "1847 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American outlaw (d. 1882)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American outlaw (d. 1882)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1850", "text": "<PERSON><PERSON>, German physicist (d. 1930)", "html": "1850 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, German physicist (d. 1930)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, German physicist (d. 1930)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1856", "text": "<PERSON>, American lawyer, publisher, and politician (d. 1922)", "html": "1856 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer, publisher, and politician (d. 1922)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer, publisher, and politician (d. 1922)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "1867", "text": "<PERSON>, American pianist and composer (d. 1944)", "html": "1867 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Amy Beach\"><PERSON></a>, American pianist and composer (d. 1944)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Amy Beach\"><PERSON></a>, American pianist and composer (d. 1944)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1871", "text": "<PERSON>, Estonian physician and politician, Head of State of Estonia (d 1941)", "html": "1871 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Estonian physician and politician, <a href=\"https://wikipedia.org/wiki/Head_of_State_of_Estonia\" class=\"mw-redirect\" title=\"Head of State of Estonia\">Head of State of Estonia</a> (d 1941)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Estonian physician and politician, <a href=\"https://wikipedia.org/wiki/Head_of_State_of_Estonia\" class=\"mw-redirect\" title=\"Head of State of Estonia\">Head of State of Estonia</a> (d 1941)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Head of State of Estonia", "link": "https://wikipedia.org/wiki/Head_of_State_of_Estonia"}]}, {"year": "1872", "text": "<PERSON><PERSON> <PERSON><PERSON>, Indian lawyer and politician (d. 1936)", "html": "1872 - <a href=\"https://wikipedia.org/wiki/V._<PERSON>._Chidambaram_Pillai\" title=\"V. O. Chidambaram Pillai\"><PERSON><PERSON> <PERSON><PERSON></a>, Indian lawyer and politician (d. 1936)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/V._<PERSON>._Chidambaram_Pillai\" title=\"V. O. Chidambaram Pillai\"><PERSON><PERSON> <PERSON><PERSON></a>, Indian lawyer and politician (d. 1936)", "links": [{"title": "<PERSON><PERSON> <PERSON><PERSON>", "link": "https://wikipedia.org/wiki/V._<PERSON>._Chidambaram_Pillai"}]}, {"year": "1872", "text": "<PERSON>, Australian tennis player (d. 1950)", "html": "1872 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian tennis player (d. 1950)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian tennis player (d. 1950)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1873", "text": "<PERSON>, American general and engineer (d. 1942)", "html": "1873 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American general and engineer (d. 1942)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American general and engineer (d. 1942)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1874", "text": "<PERSON><PERSON>, American baseball player and manager (d. 1959)", "html": "1874 - <a href=\"https://wikipedia.org/wiki/Nap_<PERSON><PERSON><PERSON>\" title=\"Nap <PERSON><PERSON><PERSON>\"><PERSON><PERSON></a>, American baseball player and manager (d. 1959)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Nap_<PERSON><PERSON><PERSON>\" title=\"Nap <PERSON>jo<PERSON>\"><PERSON><PERSON></a>, American baseball player and manager (d. 1959)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Nap_<PERSON><PERSON>ie"}]}, {"year": "1876", "text": "<PERSON>, German field marshal (d. 1956)", "html": "1876 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German field marshal (d. 1956)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German field marshal (d. 1956)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1880", "text": "<PERSON> of Manila, Spanish-Filipino priest and martyr (d. 1936)", "html": "1880 - <a href=\"https://wikipedia.org/wiki/Jos%C3%A9_Mar%C3%ADa_of_Manila\" title=\"<PERSON> of Manila\"><PERSON> of Manila</a>, Spanish-Filipino priest and martyr (d. 1936)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Jos%C3%A9_Mar%C3%ADa_of_Manila\" title=\"<PERSON> of Manila\"><PERSON> of Manila</a>, Spanish-Filipino priest and martyr (d. 1936)", "links": [{"title": "<PERSON> of Manila", "link": "https://wikipedia.org/wiki/Jos%C3%A9_Mar%C3%ADa_of_Manila"}]}, {"year": "1881", "text": "<PERSON>, Austrian philosopher and politician, Foreign Minister of Austria (d. 1938)", "html": "1881 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Austrian philosopher and politician, <a href=\"https://wikipedia.org/wiki/Foreign_Minister_of_Austria\" class=\"mw-redirect\" title=\"Foreign Minister of Austria\">Foreign Minister of Austria</a> (d. 1938)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Austrian philosopher and politician, <a href=\"https://wikipedia.org/wiki/Foreign_Minister_of_Austria\" class=\"mw-redirect\" title=\"Foreign Minister of Austria\">Foreign Minister of Austria</a> (d. 1938)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Foreign Minister of Austria", "link": "https://wikipedia.org/wiki/Foreign_Minister_of_Austria"}]}, {"year": "1881", "text": "<PERSON>, 1st Baron <PERSON>, English field marshal (d. 1964)", "html": "1881 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>,_1st_Baron_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>, 1st Baron <PERSON>\"><PERSON>, 1st Baron <PERSON></a>, English field marshal (d. 1964)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>,_1st_Baron_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>, 1st Baron <PERSON>\"><PERSON>, 1st Baron <PERSON></a>, English field marshal (d. 1964)", "links": [{"title": "<PERSON>, 1st Baron <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>,_1st_Baron_<PERSON>"}]}, {"year": "1883", "text": "<PERSON>, Austrian musicologist and scholar (d. 1967)", "html": "1883 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Austrian musicologist and scholar (d. 1967)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Austrian musicologist and scholar (d. 1967)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1888", "text": "<PERSON><PERSON><PERSON><PERSON>, Indian philosopher and politician, 2nd President of India (d. 1975)", "html": "1888 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Indian philosopher and politician, 2nd <a href=\"https://wikipedia.org/wiki/President_of_India\" title=\"President of India\">President of India</a> (d. 1975)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Indian philosopher and politician, 2nd <a href=\"https://wikipedia.org/wiki/President_of_India\" title=\"President of India\">President of India</a> (d. 1975)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON><PERSON><PERSON><PERSON>"}, {"title": "President of India", "link": "https://wikipedia.org/wiki/President_of_India"}]}, {"year": "1892", "text": "<PERSON>, Hungarian violinist and educator (d. 1973)", "html": "1892 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Hungarian violinist and educator (d. 1973)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Hungarian violinist and educator (d. 1973)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1897", "text": "<PERSON>, American actor (d. 1992)", "html": "1897 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor (d. 1992)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor (d. 1992)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1897", "text": "<PERSON>, American market analyst, founded <PERSON><PERSON><PERSON><PERSON> (d. 1980)", "html": "1897 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American market analyst, founded <a href=\"https://wikipedia.org/wiki/<PERSON>N<PERSON>sen\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a> (d. 1980)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American market analyst, founded <a href=\"https://wikipedia.org/wiki/ACNielsen\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON>sen\"><PERSON><PERSON><PERSON><PERSON></a> (d. 1980)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>"}]}, {"year": "1899", "text": "<PERSON>, American author and screenwriter (d. 1944)", "html": "1899 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author and screenwriter (d. 1944)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author and screenwriter (d. 1944)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1899", "text": "<PERSON>, Canadian author and educator (d. 1989)", "html": "1899 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian author and educator (d. 1989)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian author and educator (d. 1989)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1901", "text": "<PERSON>, American actress (d. 1988)", "html": "1901 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress (d. 1988)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress (d. 1988)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Florence_Eldridge"}]}, {"year": "1901", "text": "<PERSON>, Italian politician, 33rd Prime Minister of Italy (d. 1991)", "html": "1901 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian politician, 33rd <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Italy\" title=\"Prime Minister of Italy\">Prime Minister of Italy</a> (d. 1991)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian politician, 33rd <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Italy\" title=\"Prime Minister of Italy\">Prime Minister of Italy</a> (d. 1991)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Prime Minister of Italy", "link": "https://wikipedia.org/wiki/Prime_Minister_of_Italy"}]}, {"year": "1902", "text": "<PERSON>, American playwright, producer, manager, and publicist (d. 1998)", "html": "1902 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American playwright, producer, manager, and publicist (d. 1998)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American playwright, producer, manager, and publicist (d. 1998)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1902", "text": "<PERSON>, American actor, director, producer, and screenwriter (d. 1979)", "html": "1902 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor, director, producer, and screenwriter (d. 1979)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor, director, producer, and screenwriter (d. 1979)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1904", "text": "<PERSON>, Australian pianist and educator (d. 2004)", "html": "1904 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian pianist and educator (d. 2004)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian pianist and educator (d. 2004)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Vera_Bradford"}]}, {"year": "1905", "text": "<PERSON>, French general (d. 1979)", "html": "1905 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French general (d. 1979)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French general (d. 1979)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1905", "text": "<PERSON>, Hungarian-English journalist and author (d. 1983)", "html": "1905 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Hungarian-English journalist and author (d. 1983)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Hungarian-English journalist and author (d. 1983)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1905", "text": "<PERSON><PERSON>, Filipino lawyer and politician (d. 2005)", "html": "1905 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Filipino lawyer and politician (d. 2005)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Filipino lawyer and politician (d. 2005)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1906", "text": "<PERSON><PERSON>, American painter, lithographer, and photographer (d. 1978)", "html": "1906 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American painter, lithographer, and photographer (d. 1978)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American painter, lithographer, and photographer (d. 1978)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1906", "text": "<PERSON><PERSON>, American singer-songwriter and pianist (d. 1995)", "html": "1906 - <a href=\"https://wikipedia.org/wiki/Sunnyland_Slim\" title=\"Sunnyland Slim\"><PERSON><PERSON></a>, American singer-songwriter and pianist (d. 1995)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Sunnyland_Slim\" title=\"Sunnyland Slim\"><PERSON><PERSON></a>, American singer-songwriter and pianist (d. 1995)", "links": [{"title": "Sunnyland Slim", "link": "https://wikipedia.org/wiki/Sunnyland_Slim"}]}, {"year": "1908", "text": "<PERSON><PERSON><PERSON>, Brazilian physician, geographer, and activist (d. 1973)", "html": "1908 - <a href=\"https://wikipedia.org/wiki/Josu%C3%A<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Brazilian physician, geographer, and activist (d. 1973)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Josu%C3%A<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Brazilian physician, geographer, and activist (d. 1973)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Josu%C3%A9_de_<PERSON>"}]}, {"year": "1908", "text": "<PERSON><PERSON><PERSON><PERSON>, German-American pianist and composer (d. 2004)", "html": "1908 - <a href=\"https://wikipedia.org/wiki/Joaqu%C3%ADn_<PERSON><PERSON>-<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, German-American pianist and composer (d. 2004)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Joaqu%C3%ADn_<PERSON><PERSON>-<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, German-American pianist and composer (d. 2004)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Joaqu%C3%ADn_<PERSON><PERSON>-<PERSON><PERSON>"}]}, {"year": "1908", "text": "<PERSON>, Italian composer and painter (d. 2019)", "html": "1908 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian composer and painter (d. 2019)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian composer and painter (d. 2019)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1908", "text": "<PERSON><PERSON>, Italian engineer (d. 1966)", "html": "1908 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_R<PERSON>lta\" title=\"Ren<PERSON> Rivolta\"><PERSON><PERSON></a>, Italian engineer (d. 1966)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_R<PERSON>lta\" title=\"Ren<PERSON> Rivolta\"><PERSON><PERSON></a>, Italian engineer (d. 1966)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Ren<PERSON>_Rivolta"}]}, {"year": "1909", "text": "<PERSON>, German pianist and conductor (d. 1971)", "html": "1909 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German pianist and conductor (d. 1971)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German pianist and conductor (d. 1971)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1909", "text": "<PERSON>, Russian-English talent manager (d. 1994)", "html": "1909 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian-English talent manager (d. 1994)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian-English talent manager (d. 1994)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1909", "text": "<PERSON>, Scottish-Australian cricketer (d. 1933)", "html": "1909 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish-Australian cricketer (d. 1933)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish-Australian cricketer (d. 1933)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1910", "text": "<PERSON><PERSON>, English author (d. 1996)", "html": "1910 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, English author (d. 1996)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, English author (d. 1996)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1910", "text": "<PERSON><PERSON><PERSON>, Indian cricketer (d. 1981)", "html": "1910 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>lia\" title=\"Phiroze Palia\"><PERSON><PERSON><PERSON></a>, Indian cricketer (d. 1981)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"Phi<PERSON>ze Palia\"><PERSON><PERSON><PERSON></a>, Indian cricketer (d. 1981)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>lia"}]}, {"year": "1912", "text": "<PERSON>, American composer and theorist (d. 1992)", "html": "1912 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American composer and theorist (d. 1992)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Cage\"><PERSON></a>, American composer and theorist (d. 1992)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1912", "text": "<PERSON><PERSON>, Swedish-German actress and photographer (d. 2001)", "html": "1912 - <a href=\"https://wikipedia.org/wiki/Kris<PERSON>_S%C3%B6<PERSON><PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Swedish-German actress and photographer (d. 2001)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_S%C3%B6<PERSON><PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Swedish-German actress and photographer (d. 2001)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Kristina_S%C3%B6<PERSON><PERSON>"}]}, {"year": "1912", "text": "<PERSON>, American voice actor, animator, and screenwriter (d. 2004)", "html": "1912 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(animator)\" title=\"<PERSON> (animator)\"><PERSON></a>, American voice actor, animator, and screenwriter (d. 2004)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(animator)\" title=\"<PERSON> (animator)\"><PERSON></a>, American voice actor, animator, and screenwriter (d. 2004)", "links": [{"title": "<PERSON> (animator)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>(animator)"}]}, {"year": "1914", "text": "<PERSON>, English make up artist (d. 2013)", "html": "1914 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English make up artist (d. 2013)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English make up artist (d. 2013)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1914", "text": "<PERSON>, American violinist, composer, and educator (d. 1984)", "html": "1914 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American violinist, composer, and educator (d. 1984)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American violinist, composer, and educator (d. 1984)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1914", "text": "<PERSON><PERSON><PERSON>, Chilean physicist, mathematician, and poet (d. 2018)", "html": "1914 - <a href=\"https://wikipedia.org/wiki/Nicanor_<PERSON>rra\" title=\"<PERSON><PERSON><PERSON> Parra\"><PERSON><PERSON><PERSON></a>, Chilean physicist, mathematician, and poet (d. 2018)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>rra\" title=\"<PERSON><PERSON><PERSON>rra\"><PERSON><PERSON><PERSON></a>, Chilean physicist, mathematician, and poet (d. 2018)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>r_Parra"}]}, {"year": "1916", "text": "<PERSON>, Canadian comedian, actor, and screenwriter (d. 2002)", "html": "1916 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian comedian, actor, and screenwriter (d. 2002)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian comedian, actor, and screenwriter (d. 2002)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1916", "text": "<PERSON>, American novelist (d. 1991)", "html": "1916 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American novelist (d. 1991)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American novelist (d. 1991)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1917", "text": "<PERSON>, American photographer (d. 2012)", "html": "1917 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American photographer (d. 2012)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American photographer (d. 2012)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "1917", "text": "<PERSON><PERSON><PERSON>, Swedish harness racer and trainer (d. 2008)", "html": "1917 - <a href=\"https://wikipedia.org/wiki/S%C3%B6ren_Nordin\" title=\"Sören Nordin\"><PERSON><PERSON><PERSON></a>, Swedish <a href=\"https://wikipedia.org/wiki/Harness_racing\" title=\"Harness racing\">harness</a> racer and trainer (d. 2008)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/S%C3%B6ren_Nordin\" title=\"Sören Nordin\"><PERSON><PERSON><PERSON></a>, Swedish <a href=\"https://wikipedia.org/wiki/Harness_racing\" title=\"Harness racing\">harness</a> racer and trainer (d. 2008)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/S%C3%B6ren_<PERSON>in"}, {"title": "Harness racing", "link": "https://wikipedia.org/wiki/Harness_racing"}]}, {"year": "1918", "text": "<PERSON>, Mexican actor, director, and screenwriter (d. 1992)", "html": "1918 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Mexican actor, director, and screenwriter (d. 1992)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Mexican actor, director, and screenwriter (d. 1992)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1918", "text": "<PERSON>, Sr., Australian captain and politician (d. 1990)", "html": "1918 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Sr.\" class=\"mw-redirect\" title=\"<PERSON>, Sr.\"><PERSON>, Sr.</a>, Australian captain and politician (d. 1990)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Sr.\" class=\"mw-redirect\" title=\"<PERSON>, Sr.\"><PERSON>, Sr.</a>, Australian captain and politician (d. 1990)", "links": [{"title": "<PERSON>, Sr.", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_<PERSON>."}]}, {"year": "1918", "text": "<PERSON>, American cartoonist and monk (d. 2009)", "html": "1918 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(cartoonist)\" title=\"<PERSON> (cartoonist)\"><PERSON></a>, American cartoonist and monk (d. 2009)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(cartoonist)\" title=\"<PERSON> (cartoonist)\"><PERSON></a>, American cartoonist and monk (d. 2009)", "links": [{"title": "<PERSON> (cartoonist)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(cartoonist)"}]}, {"year": "1919", "text": "<PERSON>, German SS officer (d. 1945)", "html": "1919 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German <a href=\"https://wikipedia.org/wiki/SS\" class=\"mw-redirect\" title=\"SS\">SS</a> officer (d. 1945)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German <a href=\"https://wikipedia.org/wiki/SS\" class=\"mw-redirect\" title=\"SS\">SS</a> officer (d. 1945)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "SS", "link": "https://wikipedia.org/wiki/SS"}]}, {"year": "1920", "text": "<PERSON>, English-American composer and educator (d. 1990)", "html": "1920 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English-American composer and educator (d. 1990)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English-American composer and educator (d. 1990)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1920", "text": "<PERSON><PERSON>, Dutch-Swiss actor, director, producer, and screenwriter (d. 2007)", "html": "1920 - <a href=\"https://wikipedia.org/wiki/Fons_Rademakers\" title=\"Fons Rademakers\"><PERSON><PERSON> Rade<PERSON></a>, Dutch-Swiss actor, director, producer, and screenwriter (d. 2007)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Fons_Rademakers\" title=\"Fons Rademakers\"><PERSON><PERSON> Rade<PERSON></a>, Dutch-Swiss actor, director, producer, and screenwriter (d. 2007)", "links": [{"title": "Fons Rademakers", "link": "https://wikipedia.org/wiki/Fons_Rademakers"}]}, {"year": "1921", "text": "<PERSON>, Canadian ice hockey player and coach (d. 2013)", "html": "1921 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(ice_hockey)\" title=\"<PERSON> (ice hockey)\"><PERSON></a>, Canadian ice hockey player and coach (d. 2013)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(ice_hockey)\" title=\"<PERSON> (ice hockey)\"><PERSON></a>, Canadian ice hockey player and coach (d. 2013)", "links": [{"title": "<PERSON> (ice hockey)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(ice_hockey)"}]}, {"year": "1921", "text": "<PERSON>, American businessman, created the MPAA film rating system (d. 2007)", "html": "1921 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American businessman, created the <a href=\"https://wikipedia.org/wiki/Motion_Picture_Association_of_America_film_rating_system\" class=\"mw-redirect\" title=\"Motion Picture Association of America film rating system\">MPAA film rating system</a> (d. 2007)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American businessman, created the <a href=\"https://wikipedia.org/wiki/Motion_Picture_Association_of_America_film_rating_system\" class=\"mw-redirect\" title=\"Motion Picture Association of America film rating system\">MPAA film rating system</a> (d. 2007)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Motion Picture Association of America film rating system", "link": "https://wikipedia.org/wiki/Motion_Picture_Association_of_America_film_rating_system"}]}, {"year": "1922", "text": "<PERSON><PERSON>, English physicist and academic (d. 2016)", "html": "1922 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, English physicist and academic (d. 2016)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, English physicist and academic (d. 2016)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1923", "text": "<PERSON>, Australian captain and politician (d. 2002)", "html": "1923 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian captain and politician (d. 2002)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian captain and politician (d. 2002)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1923", "text": "<PERSON>, Australian cricketer (d. 2004)", "html": "1923 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian cricketer (d. 2004)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian cricketer (d. 2004)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1924", "text": "<PERSON>, American football player and coach (d. 2013)", "html": "1924 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player and coach (d. 2013)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player and coach (d. 2013)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1924", "text": "<PERSON>, Australian-American artist (d. 2016)", "html": "1924 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian-American artist (d. 2016)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian-American artist (d. 2016)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Frank_Armitage"}]}, {"year": "1925", "text": "<PERSON>, American author (d. 2014)", "html": "1925 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author (d. 2014)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author (d. 2014)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1927", "text": "<PERSON>, American economist and academic (d. 2019)", "html": "1927 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American economist and academic (d. 2019)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American economist and academic (d. 2019)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1928", "text": "<PERSON>, English pianist and educator (d. 2006)", "html": "1928 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English pianist and educator (d. 2006)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English pianist and educator (d. 2006)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1928", "text": "<PERSON>, German trombonist and educator (d. 2005)", "html": "1928 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German trombonist and educator (d. 2005)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German trombonist and educator (d. 2005)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1929", "text": "<PERSON>, American comedian and actor (d. 2024)", "html": "1929 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American comedian and actor (d. 2024)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American comedian and actor (d. 2024)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1929", "text": "<PERSON><PERSON><PERSON>, Russian general, pilot, and cosmonaut (d. 2004)", "html": "1929 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Russian general, pilot, and cosmonaut (d. 2004)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Russian general, pilot, and cosmonaut (d. 2004)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1932", "text": "<PERSON>, American actress and singer", "html": "1932 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress and singer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress and singer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1932", "text": "<PERSON>, American electrical engineer and inventor (d. 2024)", "html": "1932 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American electrical engineer and inventor (d. 2024)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American electrical engineer and inventor (d. 2024)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1933", "text": "<PERSON>, Chilean cardinal", "html": "1933 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>rr%C3%<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Chilean cardinal", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>%C3%<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Chilean cardinal", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>%C3%<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1934", "text": "<PERSON>, German cardinal (d. 2024)", "html": "1934 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German cardinal (d. 2024)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German cardinal (d. 2024)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1934", "text": "<PERSON>, American actor and educator (d. 2008)", "html": "1934 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor and educator (d. 2008)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor and educator (d. 2008)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1934", "text": "<PERSON>, English politician, Shadow Secretary of State for Northern Ireland (d. 2017)", "html": "1934 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(politician)\" title=\"<PERSON> (politician)\"><PERSON></a>, English politician, <a href=\"https://wikipedia.org/wiki/Shadow_Secretary_of_State_for_Northern_Ireland\" title=\"Shadow Secretary of State for Northern Ireland\">Shadow Secretary of State for Northern Ireland</a> (d. 2017)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(politician)\" title=\"<PERSON> (politician)\"><PERSON></a>, English politician, <a href=\"https://wikipedia.org/wiki/Shadow_Secretary_of_State_for_Northern_Ireland\" title=\"Shadow Secretary of State for Northern Ireland\">Shadow Secretary of State for Northern Ireland</a> (d. 2017)", "links": [{"title": "<PERSON> (politician)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(politician)"}, {"title": "Shadow Secretary of State for Northern Ireland", "link": "https://wikipedia.org/wiki/Shadow_Secretary_of_State_for_Northern_Ireland"}]}, {"year": "1935", "text": "<PERSON>, American author and philanthropist, founded Werner <PERSON>hard and Associates and The Hunger Project", "html": "1935 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author and philanthropist, founded <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_and_<PERSON>\" title=\"<PERSON> and <PERSON>\"><PERSON> and <PERSON></a> and <a href=\"https://wikipedia.org/wiki/The_Hunger_Project\" title=\"The Hunger Project\">The Hunger Project</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author and philanthropist, founded <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_and_<PERSON>\" title=\"<PERSON> and <PERSON>\"><PERSON> and <PERSON></a> and <a href=\"https://wikipedia.org/wiki/The_Hunger_Project\" title=\"The Hunger Project\">The Hunger Project</a>", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "<PERSON> and Associates", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON><PERSON>_and_Associates"}, {"title": "The Hunger Project", "link": "https://wikipedia.org/wiki/The_Hunger_Project"}]}, {"year": "1935", "text": "<PERSON>, Australian composer and educator", "html": "1935 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian composer and educator", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian composer and educator", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1935", "text": "<PERSON><PERSON>, Chinese-American actress", "html": "1935 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON>ille Soong\"><PERSON><PERSON></a>, Chinese-American actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON>ille Soong\"><PERSON><PERSON></a>, Chinese-American actress", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>ille_<PERSON>g"}]}, {"year": "1936", "text": "<PERSON>, Canadian lawyer and politician (d. 2014)", "html": "1936 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(Quebec_politician)\" title=\"<PERSON> (Quebec politician)\"><PERSON></a>, Canadian lawyer and politician (d. 2014)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(Quebec_politician)\" title=\"<PERSON> (Quebec politician)\"><PERSON></a>, Canadian lawyer and politician (d. 2014)", "links": [{"title": "<PERSON> (Quebec politician)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(Quebec_politician)"}]}, {"year": "1936", "text": "<PERSON>, American politician and diplomat, 24th United States Ambassador to the United Nations", "html": "1936 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American politician and diplomat, 24th <a href=\"https://wikipedia.org/wiki/United_States_Ambassador_to_the_United_Nations\" class=\"mw-redirect\" title=\"United States Ambassador to the United Nations\">United States Ambassador to the United Nations</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American politician and diplomat, 24th <a href=\"https://wikipedia.org/wiki/United_States_Ambassador_to_the_United_Nations\" class=\"mw-redirect\" title=\"United States Ambassador to the United Nations\">United States Ambassador to the United Nations</a>", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "United States Ambassador to the United Nations", "link": "https://wikipedia.org/wiki/United_States_Ambassador_to_the_United_Nations"}]}, {"year": "1936", "text": "<PERSON>, American sociologist, author, and educator", "html": "1936 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American sociologist, author, and educator", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American sociologist, author, and educator", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1936", "text": "<PERSON>, American baseball player and coach", "html": "1936 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player and coach", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1936", "text": "<PERSON><PERSON><PERSON>, Latvian poet, journalist, and translator (d. 2022)", "html": "1936 - <a href=\"https://wikipedia.org/wiki/Knuts_Skujenieks\" title=\"Knuts Skujenie<PERSON>\"><PERSON><PERSON><PERSON></a>, Latvian poet, journalist, and translator (d. 2022)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Knuts_Skujenieks\" title=\"K<PERSON><PERSON> Skujenie<PERSON>\"><PERSON><PERSON><PERSON></a>, Latvian poet, journalist, and translator (d. 2022)", "links": [{"title": "Knuts <PERSON>", "link": "https://wikipedia.org/wiki/Knuts_Skujenieks"}]}, {"year": "1937", "text": "<PERSON>, Argentine footballer and manager (d. 2018)", "html": "1937 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%ADn_Angelillo\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, Argentine footballer and manager (d. 2018)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%ADn_Angelillo\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, Argentine footballer and manager (d. 2018)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Antonio_Valent%C3%ADn_Angelillo"}]}, {"year": "1937", "text": "<PERSON>, English director, producer, and screenwriter", "html": "1937 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English director, producer, and screenwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English director, producer, and screenwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1938", "text": "<PERSON>, Sr., Canadian ice hockey player, coach, and manager (d. 2007)", "html": "1938 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Sr.\" class=\"mw-redirect\" title=\"<PERSON>, Sr.\"><PERSON>, Sr.</a>, Canadian ice hockey player, coach, and manager (d. 2007)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>,_Sr.\" class=\"mw-redirect\" title=\"<PERSON>, Sr.\"><PERSON>, Sr.</a>, Canadian ice hockey player, coach, and manager (d. 2007)", "links": [{"title": "<PERSON>, Sr.", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Sr."}]}, {"year": "1938", "text": "<PERSON><PERSON>, Baroness <PERSON> of Darwen, English politician (d. 2024)", "html": "1938 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>,_Baroness_<PERSON>_of_Darwen\" title=\"<PERSON><PERSON>, Baroness <PERSON> of Darwen\"><PERSON><PERSON>, Baroness <PERSON> of Darwen</a>, English politician (d. 2024)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>,_Baroness_<PERSON>_of_Darwen\" title=\"<PERSON><PERSON>, Baroness <PERSON> of Darwen\"><PERSON><PERSON>, Baroness <PERSON> of Darwen</a>, English politician (d. 2024)", "links": [{"title": "<PERSON><PERSON>, <PERSON> of Darwen", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>,_<PERSON>_<PERSON>_of_Darwen"}]}, {"year": "1939", "text": "<PERSON><PERSON>, American nurse and activist", "html": "1939 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American nurse and activist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American nurse and activist", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1939", "text": "<PERSON>, American actor, director, and screenwriter", "html": "1939 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor, director, and screenwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor, director, and screenwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1939", "text": "<PERSON>, Australian actor", "html": "1939 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1939", "text": "<PERSON>, American singer-songwriter and guitarist (d. 2008)", "html": "1939 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(musician)\" title=\"<PERSON> (musician)\"><PERSON></a>, American singer-songwriter and guitarist (d. 2008)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>(musician)\" title=\"<PERSON> (musician)\"><PERSON></a>, American singer-songwriter and guitarist (d. 2008)", "links": [{"title": "<PERSON> (musician)", "link": "https://wikipedia.org/wiki/<PERSON>(musician)"}]}, {"year": "1939", "text": "<PERSON>, English journalist, author, and politician (d. 2021)", "html": "1939 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English journalist, author, and politician (d. 2021)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English journalist, author, and politician (d. 2021)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1940", "text": "<PERSON>, Baroness <PERSON> of Breckland, English politician", "html": "1940 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Baroness_<PERSON><PERSON><PERSON>_of_Breckland\" title=\"<PERSON>, Baroness <PERSON> of Breckland\"><PERSON>, Baroness <PERSON> of Breckland</a>, English politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>,_Baroness_<PERSON><PERSON>_of_Breckland\" title=\"<PERSON>, Baroness <PERSON> of Breckland\"><PERSON>, Baroness <PERSON> of Breckland</a>, English politician", "links": [{"title": "<PERSON>, Baroness <PERSON> of Breckland", "link": "https://wikipedia.org/wiki/<PERSON>,_Baroness_<PERSON><PERSON>_of_Breckland"}]}, {"year": "1940", "text": "<PERSON><PERSON>, American actress and singer (d. 2023)", "html": "1940 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American actress and singer (d. 2023)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American actress and singer (d. 2023)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1941", "text": "<PERSON>, Canadian ice hockey player and coach (d. 2022)", "html": "1941 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player and coach (d. 2022)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player and coach (d. 2022)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1942", "text": "<PERSON>, German actor, director, producer, and screenwriter", "html": "1942 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German actor, director, producer, and screenwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German actor, director, producer, and screenwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1942", "text": "<PERSON>, Mexican conductor and composer (d. 1995)", "html": "1942 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Mexican conductor and composer (d. 1995)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Mexican conductor and composer (d. 1995)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1943", "text": "<PERSON><PERSON><PERSON>, Filipino social worker and politician, 10th Filipino Secretary of Social Welfare and Development (d. 2007)", "html": "1943 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Filipino social worker and politician, 10th <a href=\"https://wikipedia.org/wiki/Secretary_of_Social_Welfare_and_Development_(Philippines)\" class=\"mw-redirect\" title=\"Secretary of Social Welfare and Development (Philippines)\">Filipino Secretary of Social Welfare and Development</a> (d. 2007)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Filipino social worker and politician, 10th <a href=\"https://wikipedia.org/wiki/Secretary_of_Social_Welfare_and_Development_(Philippines)\" class=\"mw-redirect\" title=\"Secretary of Social Welfare and Development (Philippines)\">Filipino Secretary of Social Welfare and Development</a> (d. 2007)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>gu<PERSON>g"}, {"title": "Secretary of Social Welfare and Development (Philippines)", "link": "https://wikipedia.org/wiki/Secretary_of_Social_Welfare_and_Development_(Philippines)"}]}, {"year": "1944", "text": "<PERSON><PERSON>, Italian poet, author, and playwright (d. 1996)", "html": "1944 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Italian poet, author, and playwright (d. 1996)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Italian poet, author, and playwright (d. 1996)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1944", "text": "<PERSON>, Australian lawyer and politician, 33rd Australian Minister of Foreign Affairs", "html": "1944 - <a href=\"https://wikipedia.org/wiki/<PERSON>(politician)\" title=\"<PERSON> (politician)\"><PERSON></a>, Australian lawyer and politician, 33rd <a href=\"https://wikipedia.org/wiki/Minister_for_Foreign_Affairs_(Australia)\" title=\"Minister for Foreign Affairs (Australia)\">Australian Minister of Foreign Affairs</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>(politician)\" title=\"<PERSON> (politician)\"><PERSON></a>, Australian lawyer and politician, 33rd <a href=\"https://wikipedia.org/wiki/Minister_for_Foreign_Affairs_(Australia)\" title=\"Minister for Foreign Affairs (Australia)\">Australian Minister of Foreign Affairs</a>", "links": [{"title": "<PERSON> (politician)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(politician)"}, {"title": "Minister for Foreign Affairs (Australia)", "link": "https://wikipedia.org/wiki/Minister_for_Foreign_Affairs_(Australia)"}]}, {"year": "1945", "text": "<PERSON>, Swedish director and screenwriter", "html": "1945 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swedish director and screenwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swedish director and screenwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1945", "text": "<PERSON>, Scottish singer-songwriter and guitarist", "html": "1945 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish singer-songwriter and guitarist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish singer-songwriter and guitarist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1946", "text": "<PERSON><PERSON><PERSON><PERSON>, South Korean astrophysicist and academic", "html": "1946 - <a href=\"https://wikipedia.org/wiki/Kyon<PERSON><PERSON>_<PERSON>\" title=\"Kyon<PERSON><PERSON> Chang\"><PERSON><PERSON><PERSON><PERSON></a>, South Korean astrophysicist and academic", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Kyon<PERSON><PERSON>_<PERSON>\" title=\"Kyon<PERSON><PERSON> Chang\"><PERSON><PERSON><PERSON><PERSON></a>, South Korean astrophysicist and academic", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Kyon<PERSON><PERSON>_Chang"}]}, {"year": "1946", "text": "<PERSON>, American actor and director", "html": "1946 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor and director", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor and director", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1946", "text": "<PERSON>, Scottish singer-songwriter and guitarist (d. 2018)", "html": "1946 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish singer-songwriter and guitarist (d. 2018)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish singer-songwriter and guitarist (d. 2018)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1946", "text": "<PERSON>, British singer and songwriter (d. 1991)", "html": "1946 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Freddie Mercury\"><PERSON></a>, British singer and songwriter (d. 1991)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Freddie Mercury\"><PERSON></a>, British singer and songwriter (d. 1991)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1946", "text": "<PERSON><PERSON>, American singer-songwriter, guitarist, and actor", "html": "1946 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_Wainwright_III\" title=\"<PERSON><PERSON> Wainwright III\"><PERSON><PERSON> III</a>, American singer-songwriter, guitarist, and actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>ainwright_III\" title=\"<PERSON><PERSON> Wainwright III\"><PERSON><PERSON> III</a>, American singer-songwriter, guitarist, and actor", "links": [{"title": "<PERSON><PERSON> III", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1947", "text": "<PERSON>, Manx saxophonist and flute player", "html": "1947 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Manx saxophonist and flute player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Manx saxophonist and flute player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1947", "text": "<PERSON>, American pianist, songwriter, and producer", "html": "1947 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American pianist, songwriter, and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American pianist, songwriter, and producer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1947", "text": "<PERSON>, American singer-songwriter and drummer (d. 2008)", "html": "1947 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Miles\"><PERSON></a>, American singer-songwriter and drummer (d. 2008)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Miles\"><PERSON></a>, American singer-songwriter and drummer (d. 2008)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1947", "text": "<PERSON>, Australian cricketer and sportscaster (d. 2019)", "html": "1947 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian cricketer and sportscaster (d. 2019)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian cricketer and sportscaster (d. 2019)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1948", "text": "<PERSON><PERSON>, Austrian lawyer, politician, and diplomat, Foreign Minister of Austria", "html": "1948 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Austrian lawyer, politician, and diplomat, <a href=\"https://wikipedia.org/wiki/Foreign_Minister_of_Austria\" class=\"mw-redirect\" title=\"Foreign Minister of Austria\">Foreign Minister of Austria</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Austrian lawyer, politician, and diplomat, <a href=\"https://wikipedia.org/wiki/Foreign_Minister_of_Austria\" class=\"mw-redirect\" title=\"Foreign Minister of Austria\">Foreign Minister of Austria</a>", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>o-W<PERSON>ner"}, {"title": "Foreign Minister of Austria", "link": "https://wikipedia.org/wiki/Foreign_Minister_of_Austria"}]}, {"year": "1949", "text": "<PERSON><PERSON>, English guitarist and songwriter", "html": "1949 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> Clem<PERSON>on\"><PERSON><PERSON></a>, English guitarist and songwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> Clempson\"><PERSON><PERSON></a>, English guitarist and songwriter", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>lem<PERSON>"}]}, {"year": "1950", "text": "<PERSON>, English businesswoman and politician", "html": "1950 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English businesswoman and politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English businesswoman and politician", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1950", "text": "<PERSON>, American cartoonist, created <PERSON>", "html": "1950 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American cartoonist, created <i><a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"Cathy\">Cathy</a></i>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American cartoonist, created <i><a href=\"https://wikipedia.org/wiki/Cathy\" title=\"Cathy\">Cathy</a></i>", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1951", "text": "<PERSON>, German footballer", "html": "1951 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1951", "text": "<PERSON>, American actor and producer", "html": "1951 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor and producer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1951", "text": "<PERSON>, American drummer and percussionist (d. 2020)", "html": "1951 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American drummer and percussionist (d. 2020)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American drummer and percussionist (d. 2020)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1952", "text": "<PERSON>, American rock singer-songwriter and actor", "html": "1952 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American rock singer-songwriter and actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American rock singer-songwriter and actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1953", "text": "<PERSON>, American historian and journalist", "html": "1953 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American historian and journalist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American historian and journalist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1953", "text": "<PERSON>, New Zealand rugby player and sportscaster", "html": "1953 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, New Zealand rugby player and sportscaster", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Me<PERSON>ted\"><PERSON></a>, New Zealand rugby player and sportscaster", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Murray_<PERSON>xted"}]}, {"year": "1953", "text": "<PERSON><PERSON>, Estonian engineer and politician, Estonian Minister of Social Affairs", "html": "1953 - <a href=\"https://wikipedia.org/wiki/Eiki_Nestor\" title=\"Eiki Nestor\"><PERSON><PERSON></a>, Estonian engineer and politician, <a href=\"https://wikipedia.org/wiki/Minister_of_Social_Affairs_(Estonia)\" class=\"mw-redirect\" title=\"Minister of Social Affairs (Estonia)\">Estonian Minister of Social Affairs</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Eiki_Nestor\" title=\"Eiki Nestor\"><PERSON><PERSON></a>, Estonian engineer and politician, <a href=\"https://wikipedia.org/wiki/Minister_of_Social_Affairs_(Estonia)\" class=\"mw-redirect\" title=\"Minister of Social Affairs (Estonia)\">Estonian Minister of Social Affairs</a>", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Eiki_Nestor"}, {"title": "Minister of Social Affairs (Estonia)", "link": "https://wikipedia.org/wiki/Minister_of_Social_Affairs_(Estonia)"}]}, {"year": "1953", "text": "<PERSON>, Canadian singer-songwriter", "html": "1953 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A9\" title=\"<PERSON>\"><PERSON></a>, Canadian singer-songwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A9\" title=\"<PERSON>\"><PERSON></a>, Canadian singer-songwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A9"}]}, {"year": "1954", "text": "<PERSON>, Jamaican footballer and cricketer (d. 2015)", "html": "1954 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(cricketer)\" title=\"<PERSON> (cricketer)\"><PERSON></a>, Jamaican footballer and cricketer (d. 2015)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(cricketer)\" title=\"<PERSON> (cricketer)\"><PERSON></a>, Jamaican footballer and cricketer (d. 2015)", "links": [{"title": "<PERSON> (cricketer)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(cricketer)"}]}, {"year": "1954", "text": "<PERSON>, American journalist and author", "html": "1954 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American journalist and author", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American journalist and author", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1956", "text": "<PERSON>, Singaporean businessman and politician", "html": "1956 - <a href=\"https://wikipedia.org/wiki/Low_Thia_Khiang\" title=\"Low Thia Khiang\">Low Thia Khiang</a>, Singaporean businessman and politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Low_Thia_Khiang\" title=\"Low Thia Khiang\">Low Thia Khiang</a>, Singaporean businessman and politician", "links": [{"title": "Low Thia Khiang", "link": "https://wikipedia.org/wiki/Low_Thia_Khiang"}]}, {"year": "1956", "text": "<PERSON><PERSON><PERSON>, Swedish singer-songwriter, guitarist, and producer", "html": "1956 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Swedish singer-songwriter, guitarist, and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Swedish singer-songwriter, guitarist, and producer", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1956", "text": "<PERSON>, American actress", "html": "1956 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1957", "text": "<PERSON><PERSON>, German footballer and manager", "html": "1957 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, German footballer and manager", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, German footballer and manager", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>s"}]}, {"year": "1957", "text": "<PERSON>, Dutch cyclist", "html": "1957 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch cyclist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch cyclist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1958", "text": "<PERSON>, Swedish bassist, composer, and producer", "html": "1958 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swedish bassist, composer, and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swedish bassist, composer, and producer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1959", "text": "<PERSON>, German journalist and publisher (d. 2014)", "html": "1959 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German journalist and publisher (d. 2014)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German journalist and publisher (d. 2014)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1960", "text": "<PERSON>, American football player, athlete, and actor", "html": "1960 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player, athlete, and actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player, athlete, and actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1960", "text": "<PERSON>, Swedish anthropologist and academic", "html": "1960 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swedish anthropologist and academic", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swedish anthropologist and academic", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1961", "text": "<PERSON><PERSON><PERSON>, Canadian pianist and composer", "html": "1961 - <a href=\"https://wikipedia.org/wiki/<PERSON>-<PERSON><PERSON>%C3%A<PERSON>_<PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Canadian pianist and composer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>-<PERSON>%C3%A<PERSON>_<PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Canadian pianist and composer", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>-Andr%C3%A9_<PERSON><PERSON>"}]}, {"year": "1962", "text": "<PERSON>, English sailor and coach", "html": "1962 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English sailor and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English sailor and coach", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1962", "text": "<PERSON>, Welsh businessman", "html": "1962 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(artistic_director)\" title=\"<PERSON> (artistic director)\"><PERSON></a>, Welsh businessman", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(artistic_director)\" title=\"<PERSON> (artistic director)\"><PERSON></a>, Welsh businessman", "links": [{"title": "<PERSON> (artistic director)", "link": "https://wikipedia.org/wiki/<PERSON>_(artistic_director)"}]}, {"year": "1963", "text": "<PERSON>, American bass player and songwriter", "html": "1963 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American bass player and songwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American bass player and songwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1963", "text": "<PERSON><PERSON>, American actress and model", "html": "1963 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American actress and model", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American actress and model", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1963", "text": "<PERSON>, American baseball player and sportscaster", "html": "1963 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player and sportscaster", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player and sportscaster", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1963", "text": "<PERSON>, American R&B singer-songwriter and actress", "html": "1963 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American R&amp;B singer-songwriter and actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American R&amp;B singer-songwriter and actress", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1963", "text": "<PERSON><PERSON>, Japanese race car driver and manager", "html": "1963 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Japanese race car driver and manager", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Japanese race car driver and manager", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>e"}]}, {"year": "1964", "text": "<PERSON>, Australian footballer and manager", "html": "1964 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian footballer and manager", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian footballer and manager", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1964", "text": "<PERSON>, Belarusian-Ukrainian director and screenwriter", "html": "1964 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Belarusian-Ukrainian director and screenwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Belarusian-Ukrainian director and screenwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1964", "text": "<PERSON>, American basketball player", "html": "1964 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1964", "text": "<PERSON>, American actor (d. 2016)", "html": "1964 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor (d. 2016)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor (d. 2016)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1965", "text": "<PERSON>, Australian race car driver", "html": "1965 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian race car driver", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian race car driver", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1965", "text": "<PERSON><PERSON><PERSON><PERSON>, Japanese wrestler", "html": "1965 - <a href=\"https://wikipedia.org/wiki/Hoshitango_Imachi\" title=\"Hoshitango Imachi\"><PERSON><PERSON><PERSON><PERSON></a>, Japanese wrestler", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Hoshitan<PERSON>_I<PERSON>chi\" title=\"Hoshitango Imachi\"><PERSON><PERSON><PERSON><PERSON></a>, Japanese wrestler", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Hoshitango_<PERSON>chi"}]}, {"year": "1965", "text": "<PERSON>, English geneticist and academic", "html": "1965 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English geneticist and academic", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English geneticist and academic", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1966", "text": "<PERSON><PERSON><PERSON>, Spanish actor, director, and screenwriter", "html": "1966 - <a href=\"https://wikipedia.org/wiki/Achero_Ma%C3%B1as\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Spanish actor, director, and screenwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Achero_Ma%C3%B1as\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Spanish actor, director, and screenwriter", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Achero_Ma%C3%B1as"}]}, {"year": "1966", "text": "<PERSON><PERSON><PERSON>, Serbian footballer and manager", "html": "1966 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>%C4%87\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Serbian footballer and manager", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>%C4%87\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Serbian footballer and manager", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Milinko_Panti%C4%87"}]}, {"year": "1967", "text": "<PERSON>, German footballer and manager", "html": "1967 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German footballer and manager", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German footballer and manager", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1967", "text": "<PERSON>, English field hockey player", "html": "1967 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English field hockey player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English field hockey player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1968", "text": "<PERSON><PERSON><PERSON>, Ukrainian footballer and manager", "html": "1968 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Ukrainian footballer and manager", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Ukrainian footballer and manager", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1968", "text": "<PERSON>, American basketball player and sportscaster", "html": "1968 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(basketball)\" title=\"<PERSON> (basketball)\"><PERSON></a>, American basketball player and sportscaster", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(basketball)\" title=\"<PERSON> (basketball)\"><PERSON></a>, American basketball player and sportscaster", "links": [{"title": "<PERSON> (basketball)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(basketball)"}]}, {"year": "1968", "text": "<PERSON>, Dutch footballer and coach", "html": "1968 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch footballer and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch footballer and coach", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1968", "text": "<PERSON>, American singer-songwriter and drummer", "html": "1968 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and drummer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and drummer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>k"}]}, {"year": "1969", "text": "<PERSON>, Brazilian footballer and manager", "html": "1969 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%BAjo\" title=\"<PERSON>\"><PERSON></a>, Brazilian footballer and manager", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%BAjo\" title=\"<PERSON>\"><PERSON></a>, Brazilian footballer and manager", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Leonardo_Ara%C3%BAjo"}]}, {"year": "1969", "text": "<PERSON><PERSON>, Japanese voice actress, singer, and radio host", "html": "1969 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Japanese voice actress, singer, and radio host", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Japanese voice actress, singer, and radio host", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1969", "text": "<PERSON>, English cricketer and coach", "html": "1969 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English cricketer and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English cricketer and coach", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1969", "text": "<PERSON><PERSON><PERSON><PERSON>, American actor and musician", "html": "1969 - <a href=\"https://wikipedia.org/wiki/Dweezil_Zappa\" title=\"Dweezil Zappa\"><PERSON><PERSON><PERSON><PERSON></a>, American actor and musician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Dweezil_Zappa\" title=\"Dweezil Zappa\"><PERSON><PERSON><PERSON><PERSON></a>, American actor and musician", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Dweezil_Zappa"}]}, {"year": "1970", "text": "<PERSON>, American singer-songwriter, guitarist, puppeteer, and director", "html": "1970 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(musician)\" title=\"<PERSON> (musician)\"><PERSON></a>, American singer-songwriter, guitarist, puppeteer, and director", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(musician)\" title=\"<PERSON> (musician)\"><PERSON></a>, American singer-songwriter, guitarist, puppeteer, and director", "links": [{"title": "<PERSON> (musician)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(musician)"}]}, {"year": "1970", "text": "<PERSON>, Bangladeshi cricketer", "html": "1970 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(cricketer)\" title=\"<PERSON> (cricketer)\"><PERSON></a>, Bangladeshi cricketer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(cricketer)\" title=\"<PERSON> (cricketer)\"><PERSON></a>, Bangladeshi cricketer", "links": [{"title": "<PERSON> (cricketer)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_(cricketer)"}]}, {"year": "1970", "text": "<PERSON>, Filipino journalist and politician", "html": "1970 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Filipino journalist and politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Filipino journalist and politician", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1970", "text": "<PERSON>, English actor, director, producer, and screenwriter", "html": "1970 - <a href=\"https://wikipedia.org/wiki/Johnny_Vegas\" title=\"Johnny Vegas\"><PERSON></a>, English actor, director, producer, and screenwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Johnny_Vegas\" title=\"Johnny Vegas\"><PERSON></a>, English actor, director, producer, and screenwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1971", "text": "<PERSON>, Australian cricketer and mixed martial artist", "html": "1971 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian cricketer and mixed martial artist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian cricketer and mixed martial artist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1972", "text": "<PERSON>, Canadian-American wrestler and referee", "html": "1972 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian-American wrestler and referee", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian-American wrestler and referee", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1972", "text": "<PERSON>, Zimbabwean cricketer", "html": "1972 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Zimbabwean cricketer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Zimbabwean cricketer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1973", "text": "<PERSON>, English actor, director, and screenwriter", "html": "1973 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actor, director, and screenwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actor, director, and screenwriter", "links": [{"title": "Paddy Considine", "link": "https://wikipedia.org/wiki/Paddy_Considine"}]}, {"year": "1973", "text": "<PERSON>, American actress", "html": "1973 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1974", "text": "<PERSON>, British fell runner convicted of the attempted murder of <PERSON>", "html": "1974 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, British fell runner convicted of the attempted murder of <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, British fell runner convicted of the attempted murder of <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1974", "text": "<PERSON><PERSON>, Grenadian cricketer", "html": "1974 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Grenadian cricketer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Grenadian cricketer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1974", "text": "<PERSON><PERSON><PERSON><PERSON>, Estonian politician, Estonian Minister of the Interior", "html": "1974 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Estonian politician, <a href=\"https://wikipedia.org/wiki/Minister_of_the_Interior_(Estonia)\" class=\"mw-redirect\" title=\"Minister of the Interior (Estonia)\">Estonian Minister of the Interior</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Estonian politician, <a href=\"https://wikipedia.org/wiki/Minister_of_the_Interior_(Estonia)\" class=\"mw-redirect\" title=\"Minister of the Interior (Estonia)\">Estonian Minister of the Interior</a>", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>-<PERSON>_<PERSON>"}, {"title": "Minister of the Interior (Estonia)", "link": "https://wikipedia.org/wiki/Minister_of_the_Interior_(Estonia)"}]}, {"year": "1975", "text": "<PERSON>, American baseball player and manager", "html": "1975 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player and manager", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player and manager", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Rod_<PERSON>ajas"}]}, {"year": "1975", "text": "<PERSON>, Dutch footballer and manager", "html": "1975 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch footballer and manager", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch footballer and manager", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1975", "text": "<PERSON>, American baseball player", "html": "1975 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1975", "text": "<PERSON>, Australian rugby league player and coach", "html": "1975 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian rugby league player and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian rugby league player and coach", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1976", "text": "<PERSON>, Ukrainian gymnast", "html": "1976 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Ukrainian gymnast", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Ukrainian gymnast", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Tatiana_<PERSON>"}]}, {"year": "1976", "text": "<PERSON><PERSON>, Dutch actress and singer", "html": "1976 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Dutch actress and singer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Dutch actress and singer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1977", "text": "<PERSON><PERSON><PERSON>, American football player and sportscaster", "html": "1977 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON> Colvin\"><PERSON><PERSON><PERSON></a>, American football player and sportscaster", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON> Colvin\"><PERSON><PERSON><PERSON></a>, American football player and sportscaster", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Rosevelt_Colvin"}]}, {"year": "1977", "text": "<PERSON><PERSON>, Spanish footballer", "html": "1977 - <a href=\"https://wikipedia.org/wiki/Jose<PERSON>_Etxeberria\" title=\"Joseba Etxeberria\"><PERSON><PERSON></a>, Spanish footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Jose<PERSON>_Etxeberria\" title=\"Joseba Etxeberria\"><PERSON><PERSON></a>, Spanish footballer", "links": [{"title": "Joseba Etxeberria", "link": "https://wikipedia.org/wiki/Joseba_Etxeberria"}]}, {"year": "1977", "text": "<PERSON><PERSON>, Japanese wrestler", "html": "1977 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Japanese wrestler", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Japanese wrestler", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1977", "text": "<PERSON><PERSON><PERSON>, American basketball player", "html": "1977 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American basketball player", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1978", "text": "<PERSON>, Canadian actress", "html": "1978 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian actress", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1978", "text": "<PERSON>, New Zealand politician, 41st Prime Minister of New Zealand", "html": "1978 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, New Zealand politician, 41st <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_New_Zealand\" title=\"Prime Minister of New Zealand\">Prime Minister of New Zealand</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, New Zealand politician, 41st <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_New_Zealand\" title=\"Prime Minister of New Zealand\">Prime Minister of New Zealand</a>", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}, {"title": "Prime Minister of New Zealand", "link": "https://wikipedia.org/wiki/Prime_Minister_of_New_Zealand"}]}, {"year": "1978", "text": "<PERSON>, New Zealand rugby player", "html": "1978 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, New Zealand rugby player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, New Zealand rugby player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1978", "text": "<PERSON>, Antiguan cricketer", "html": "1978 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Antiguan cricketer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Antiguan cricketer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1978", "text": "<PERSON>, Chinese chess player", "html": "1978 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Chinese chess player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Chinese chess player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1979", "text": "<PERSON>, Norwegian footballer", "html": "1979 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Norwegian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Norwegian footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1979", "text": "<PERSON>, Canadian basketball player and sportscaster", "html": "1979 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian basketball player and sportscaster", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian basketball player and sportscaster", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1979", "text": "<PERSON>, French skier", "html": "1979 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French skier", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French skier", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1979", "text": "<PERSON>, Italian footballer", "html": "1979 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>nz<PERSON>"}]}, {"year": "1979", "text": "<PERSON>, Irish footballer", "html": "1979 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%27<PERSON><PERSON><PERSON><PERSON>\" title=\"<PERSON>\"><PERSON></a>, Irish footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%27<PERSON><PERSON><PERSON><PERSON>\" title=\"<PERSON>\"><PERSON></a>, Irish footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>%27<PERSON><PERSON><PERSON><PERSON>"}]}, {"year": "1980", "text": "<PERSON>, Argentine footballer", "html": "1980 - <a href=\"https://wikipedia.org/wiki/Franco_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Argentine footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Franco_Costanzo\" title=\"<PERSON>\"><PERSON></a>, Argentine footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Franco_Costanzo"}]}, {"year": "1980", "text": "<PERSON>, British singer", "html": "1980 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, British singer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, British singer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1981", "text": "<PERSON>, Spanish cyclist", "html": "1981 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Spanish cyclist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Spanish cyclist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1981", "text": "<PERSON>, Estonian opera singer", "html": "1981 - <a href=\"https://wikipedia.org/wiki/Kai_R%C3%BC%C3%BCtel\" title=\"<PERSON>\"><PERSON></a>, Estonian opera singer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Kai_R%C3%BC%C3%BCtel\" title=\"<PERSON>\"><PERSON></a>, Estonian opera singer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Kai_R%C3%BC%C3%BCtel"}]}, {"year": "1981", "text": "<PERSON><PERSON><PERSON>, Italian tennis player", "html": "1981 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Italian tennis player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Italian tennis player", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1982", "text": "<PERSON>, Spanish-Swiss footballer", "html": "1982 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Spanish-Swiss footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Spanish-Swiss footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1983", "text": "<PERSON><PERSON>, Ukrainian-German footballer", "html": "1983 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Ukrainian-German footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Ukrainian-German footballer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1983", "text": "<PERSON>, Uruguayan footballer", "html": "1983 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Uruguayan footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Uruguayan footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1983", "text": "<PERSON>, American football coach", "html": "1983 - <a href=\"https://wikipedia.org/wiki/<PERSON>_Riley\" title=\"<PERSON> Riley\"><PERSON></a>, American football coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_Riley\" title=\"<PERSON> Riley\"><PERSON></a>, American football coach", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1983", "text": "<PERSON>, English footballer", "html": "1983 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1983", "text": "<PERSON>, American baseball player", "html": "1983 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(outfielder)\" title=\"<PERSON> (outfielder)\"><PERSON></a>, American baseball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>(outfielder)\" title=\"<PERSON> (outfielder)\"><PERSON></a>, American baseball player", "links": [{"title": "<PERSON> (outfielder)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>(outfielder)"}]}, {"year": "1984", "text": "<PERSON>, Scottish field hockey player", "html": "1984 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(field_hockey)\" title=\"<PERSON> (field hockey)\"><PERSON></a>, Scottish field hockey player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(field_hockey)\" title=\"<PERSON> (field hockey)\"><PERSON></a>, Scottish field hockey player", "links": [{"title": "<PERSON> (field hockey)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(field_hockey)"}]}, {"year": "1984", "text": "<PERSON>, Danish cyclist (d. 2021)", "html": "1984 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>%C3%B<PERSON><PERSON><PERSON>\" title=\"<PERSON>\"><PERSON></a>, Danish cyclist (d. 2021)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>%C3%B<PERSON><PERSON><PERSON>\" title=\"<PERSON>\"><PERSON></a>, Danish cyclist (d. 2021)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>%C3%B8<PERSON><PERSON>"}]}, {"year": "1985", "text": "<PERSON>, American basketball player", "html": "1985 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1985", "text": "<PERSON>, American soccer player", "html": "1985 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Guy\"><PERSON></a>, American soccer player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Guy\"><PERSON></a>, American soccer player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1986", "text": "<PERSON>, American football player", "html": "1986 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1986", "text": "<PERSON><PERSON><PERSON>, Indian cricketer", "html": "1986 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>yan_<PERSON>jha\" title=\"<PERSON><PERSON>yan Ojha\"><PERSON><PERSON><PERSON></a>, Indian cricketer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>yan_<PERSON><PERSON>\" title=\"<PERSON><PERSON>yan Ojha\"><PERSON><PERSON><PERSON></a>, Indian cricketer", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON><PERSON>ha"}]}, {"year": "1988", "text": "<PERSON><PERSON>, Swedish footballer", "html": "1988 - <a href=\"https://wikipedia.org/wiki/Den<PERSON>_Avdi%C4%87\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Swedish footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Den<PERSON>_Avdi%C4%87\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Swedish footballer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Denni_Avdi%C4%87"}]}, {"year": "1988", "text": "<PERSON>, Ecuadorian footballer", "html": "1988 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Ecuadorian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Ecuadorian footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Felipe_<PERSON>edo"}]}, {"year": "1988", "text": "<PERSON>-<PERSON><PERSON><PERSON>, American actress and singer", "html": "1988 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>-<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress and singer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress and singer", "links": [{"title": "<PERSON>-<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>-<PERSON>"}]}, {"year": "1989", "text": "<PERSON>, American basketball player", "html": "1989 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1989", "text": "<PERSON>, American actress and singer", "html": "1989 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress and singer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress and singer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1989", "text": "<PERSON>, American ice hockey player", "html": "1989 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(ice_hockey)\" title=\"<PERSON> (ice hockey)\"><PERSON></a>, American ice hockey player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(ice_hockey)\" title=\"<PERSON> (ice hockey)\"><PERSON></a>, American ice hockey player", "links": [{"title": "<PERSON> (ice hockey)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(ice_hockey)"}]}, {"year": "1989", "text": "<PERSON>, Spanish footballer", "html": "1989 - <a href=\"https://wikipedia.org/wiki/Jos%C3%A9_%C3%81ngel_Vald%C3%A9s\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, Spanish footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Jos%C3%A9_%C3%81ngel_Vald%C3%A9s\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, Spanish footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Jos%C3%A9_%C3%81ngel_Vald%C3%A9s"}]}, {"year": "1989", "text": "<PERSON>, English rugby player", "html": "1989 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English rugby player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English rugby player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1990", "text": "<PERSON>, Italian footballer", "html": "1990 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(footballer_born_1990)\" class=\"mw-redirect\" title=\"<PERSON> (footballer born 1990)\"><PERSON></a>, Italian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(footballer_born_1990)\" class=\"mw-redirect\" title=\"<PERSON> (footballer born 1990)\"><PERSON></a>, Italian footballer", "links": [{"title": "<PERSON> (footballer born 1990)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(footballer_born_1990)"}]}, {"year": "1990", "text": "<PERSON>, Dominican tennis player", "html": "1990 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dominican tennis player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dominican tennis player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1990", "text": "<PERSON>, American basketball player", "html": "1990 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1990", "text": "<PERSON><PERSON>, South Korean figure skater", "html": "1990 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, South Korean figure skater", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, South Korean figure skater", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1990", "text": "<PERSON>, Argentine footballer", "html": "1990 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Argentine footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Argentine footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1991", "text": "<PERSON><PERSON><PERSON>, English actor and political adviser", "html": "1991 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, English actor and political adviser", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, English actor and political adviser", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1991", "text": "<PERSON><PERSON><PERSON>, Turkish footballer", "html": "1991 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_Yavru\" title=\"<PERSON><PERSON><PERSON>v<PERSON>\"><PERSON><PERSON><PERSON></a>, Turkish footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_Ya<PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON> Yav<PERSON>\"><PERSON><PERSON><PERSON></a>, Turkish footballer", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>ek<PERSON>_Ya<PERSON>ru"}]}, {"year": "1993", "text": "<PERSON>, Dominican baseball player", "html": "1993 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dominican baseball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dominican baseball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1993", "text": "<PERSON><PERSON> <PERSON><PERSON>, American basketball player", "html": "1993 - <a href=\"https://wikipedia.org/wiki/T<PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> <PERSON><PERSON>\"><PERSON><PERSON> <PERSON><PERSON></a>, American basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/T._<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> <PERSON><PERSON>\"><PERSON><PERSON> <PERSON><PERSON></a>, American basketball player", "links": [{"title": "<PERSON><PERSON> <PERSON><PERSON>", "link": "https://wikipedia.org/wiki/T._<PERSON><PERSON>_<PERSON>"}]}, {"year": "1994", "text": "<PERSON><PERSON>, Italian swimmer", "html": "1994 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Italian swimmer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Italian swimmer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1995", "text": "<PERSON><PERSON><PERSON>, Hungarian tennis player", "html": "1995 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Hungarian tennis player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Hungarian tennis player", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON><PERSON>"}]}, {"year": "1995", "text": "<PERSON>, Swedish ice hockey player", "html": "1995 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swedish ice hockey player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swedish ice hockey player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>mark"}]}, {"year": "1996", "text": "<PERSON><PERSON><PERSON>, American baseball player", "html": "1996 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American baseball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American baseball player", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1996", "text": "<PERSON><PERSON><PERSON>, Norwegian singer", "html": "1996 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_(singer)\" title=\"<PERSON><PERSON><PERSON> (singer)\"><PERSON><PERSON><PERSON></a>, Norwegian singer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_(singer)\" title=\"<PERSON><PERSON><PERSON> (singer)\"><PERSON><PERSON><PERSON></a>, Norwegian singer", "links": [{"title": "<PERSON><PERSON><PERSON> (singer)", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_(singer)"}]}, {"year": "1996", "text": "<PERSON><PERSON><PERSON>, Dutch footballer", "html": "1996 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Dutch footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Dutch footballer", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1997", "text": "<PERSON>, American baseball player", "html": "1997 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1997", "text": "<PERSON><PERSON><PERSON>, Japanese idol", "html": "1997 - <a href=\"https://wikipedia.org/wiki/Ky%C5%8Dko_Sait%C5%8D\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese idol", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Ky%C5%8Dko_Sait%C5%8D\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese idol", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Ky%C5%8Dko_Sait%C5%8D"}]}, {"year": "1998", "text": "<PERSON>, American tennis player", "html": "1998 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American tennis player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American tennis player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1998", "text": "<PERSON>, American football player", "html": "1998 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1998", "text": "<PERSON><PERSON><PERSON>, American basketball player", "html": "1998 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American basketball player", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1999", "text": "<PERSON><PERSON>, Czech ice hockey player", "html": "1999 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>il\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Czech ice hockey player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>il\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Czech ice hockey player", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Filip_<PERSON>ytil"}]}, {"year": "2001", "text": "<PERSON><PERSON><PERSON>, English footballer", "html": "2001 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, English footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, English footballer", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_Saka"}]}], "Deaths": [{"year": "590", "text": "<PERSON><PERSON><PERSON>, Lombard king (b. 540)", "html": "590 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Lombard king (b. 540)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Lombard king (b. 540)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>thari"}]}, {"year": "714", "text": "<PERSON><PERSON>, emperor of the Tang Dynasty", "html": "714 - <a href=\"https://wikipedia.org/wiki/Emperor_<PERSON><PERSON>_of_Tang\" title=\"Emperor Shang of Tang\"><PERSON><PERSON></a>, emperor of the Tang Dynasty", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Emperor_<PERSON><PERSON>_of_Tang\" title=\"Emperor Shang of Tang\"><PERSON><PERSON></a>, emperor of the Tang Dynasty", "links": [{"title": "Emperor <PERSON><PERSON> of Tang", "link": "https://wikipedia.org/wiki/Emperor_<PERSON><PERSON>_of_Tang"}]}, {"year": "1165", "text": "<PERSON><PERSON>, emperor of Japan (b. 1143)", "html": "1165 - <a href=\"https://wikipedia.org/wiki/Emperor_Nij%C5%8D\" title=\"Emperor <PERSON><PERSON>\"><PERSON><PERSON></a>, emperor of Japan (b. 1143)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Emperor_Nij%C5%8D\" title=\"Emperor <PERSON><PERSON>\"><PERSON><PERSON></a>, emperor of Japan (b. 1143)", "links": [{"title": "Emperor <PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Emperor_Nij%C5%8D"}]}, {"year": "1235", "text": "<PERSON>, duke of Brabant (b. 1165)", "html": "1235 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Duke_of_Brabant\" title=\"<PERSON>, Duke of Brabant\"><PERSON></a>, duke of Brabant (b. 1165)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Duke_of_Brabant\" title=\"<PERSON>, Duke of Brabant\"><PERSON></a>, duke of Brabant (b. 1165)", "links": [{"title": "<PERSON>, Duke of Brabant", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1311", "text": "<PERSON><PERSON><PERSON>, Hungarian oligarch", "html": "1311 - <a href=\"https://wikipedia.org/wiki/Amadeus_Aba\" title=\"Amadeus Aba\"><PERSON><PERSON><PERSON></a>, Hungarian oligarch", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Amade<PERSON>_<PERSON>\" title=\"Amadeus Aba\"><PERSON><PERSON><PERSON></a>, Hungarian oligarch", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Amadeus_<PERSON>"}]}, {"year": "1336", "text": "<PERSON>, count of Étampes (b. 1305)", "html": "1336 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%27%C3%89vreux\" title=\"<PERSON>\"><PERSON></a>, count of Étampes (b. 1305)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%27%C3%89vreux\" title=\"<PERSON>\"><PERSON></a>, count of Étampes (b. 1305)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_d%27%C3%89vreux"}]}, {"year": "1526", "text": "<PERSON>, Spanish explorer", "html": "1526 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Spanish explorer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Spanish explorer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1548", "text": "<PERSON>, Sixth and last Queen of Henry VIII of England (b. c. 1512)", "html": "1548 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Sixth and last Queen of <a href=\"https://wikipedia.org/wiki/<PERSON>_VIII_of_England\" class=\"mw-redirect\" title=\"Henry VIII of England\"><PERSON> VIII of England</a> (b. c. 1512)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Sixth and last Queen of <a href=\"https://wikipedia.org/wiki/<PERSON>_VIII_of_England\" class=\"mw-redirect\" title=\"Henry VIII of England\"><PERSON> VIII of England</a> (b. c. 1512)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "<PERSON> of England", "link": "https://wikipedia.org/wiki/Henry_VIII_of_England"}]}, {"year": "1562", "text": "<PERSON><PERSON><PERSON>, German Protestant reformer (b. 1497)", "html": "1562 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, German Protestant reformer (b. 1497)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, German Protestant reformer (b. 1497)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1569", "text": "<PERSON>, Bishop of London (b. c. 1500)", "html": "1569 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Bishop of London (b. c. 1500)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Bishop of London (b. c. 1500)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1607", "text": "<PERSON><PERSON><PERSON>, French politician, Chancellor of France (b. 1529)", "html": "1607 - <a href=\"https://wikipedia.org/wiki/Pomponne_de_Belli%C3%A8vre\" title=\"Pomponne de Bellièvre\"><PERSON><PERSON><PERSON> Bellièvre</a>, French politician, <a href=\"https://wikipedia.org/wiki/Chancellor_of_France\" title=\"Chancellor of France\">Chancellor of France</a> (b. 1529)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Pomponne_de_Belli%C3%A8vre\" title=\"Pomponne de Bellièvre\"><PERSON><PERSON><PERSON> Bellièvre</a>, French politician, <a href=\"https://wikipedia.org/wiki/Chancellor_of_France\" title=\"Chancellor of France\">Chancellor of France</a> (b. 1529)", "links": [{"title": "Pomponne de Bellièvre", "link": "https://wikipedia.org/wiki/Pomponne_de_Belli%C3%A8vre"}, {"title": "Chancellor of France", "link": "https://wikipedia.org/wiki/Chancellor_of_France"}]}, {"year": "1629", "text": "<PERSON>, Italian singer-songwriter (b. 1585)", "html": "1629 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian singer-songwriter (b. 1585)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian singer-songwriter (b. 1585)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1734", "text": "<PERSON>, French composer (b. 1664)", "html": "1734 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French composer (b. 1664)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French composer (b. 1664)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1786", "text": "<PERSON>, English merchant and philanthropist (b. 1712)", "html": "1786 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English merchant and philanthropist (b. 1712)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English merchant and philanthropist (b. 1712)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1803", "text": "<PERSON>, French general and author (b. 1741)", "html": "1803 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French general and author (b. 1741)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French general and author (b. 1741)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1803", "text": "<PERSON>, French flute player and composer (b. 1759)", "html": "1803 - <a href=\"https://wikipedia.org/wiki/Fran%C3%A<PERSON><PERSON>_<PERSON><PERSON>\" title=\"<PERSON>\"><PERSON></a>, French flute player and composer (b. 1759)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Fran%C3%A<PERSON><PERSON>_<PERSON><PERSON>\" title=\"<PERSON>\"><PERSON></a>, French flute player and composer (b. 1759)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Fran%C3%A7<PERSON>_<PERSON>enne"}]}, {"year": "1836", "text": "<PERSON>, Austrian actor and playwright (b. 1790)", "html": "1836 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Austrian actor and playwright (b. 1790)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Austrian actor and playwright (b. 1790)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1838", "text": "<PERSON>, French architect and interior decorator (b. 1764)", "html": "1838 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French architect and interior decorator (b. 1764)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French architect and interior decorator (b. 1764)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1857", "text": "<PERSON>, French sociologist and philosopher (b. 1798)", "html": "1857 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French sociologist and philosopher (b. 1798)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French sociologist and philosopher (b. 1798)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Auguste_<PERSON>te"}]}, {"year": "1876", "text": "<PERSON>, Chilean admiral and politician, 1st President of Chile (b. 1790)", "html": "1876 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Chilean admiral and politician, 1st <a href=\"https://wikipedia.org/wiki/President_of_Chile\" title=\"President of Chile\">President of Chile</a> (b. 1790)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Chilean admiral and politician, 1st <a href=\"https://wikipedia.org/wiki/President_of_Chile\" title=\"President of Chile\">President of Chile</a> (b. 1790)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "President of Chile", "link": "https://wikipedia.org/wiki/President_of_Chile"}]}, {"year": "1877", "text": "<PERSON>, American tribal leader (b. 1849)", "html": "1877 - <a href=\"https://wikipedia.org/wiki/Crazy_Horse\" title=\"Crazy Horse\">Crazy Horse</a>, American tribal leader (b. 1849)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Crazy_Horse\" title=\"Crazy Horse\">Crazy Horse</a>, American tribal leader (b. 1849)", "links": [{"title": "Crazy Horse", "link": "https://wikipedia.org/wiki/Crazy_Horse"}]}, {"year": "1894", "text": "<PERSON>, Jr., United States Army cavalry officer (b. 1822)", "html": "1894 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Jr.\" class=\"mw-redirect\" title=\"<PERSON>, Jr.\"><PERSON>, Jr.</a>, United States Army cavalry officer (b. 1822)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>,_Jr.\" class=\"mw-redirect\" title=\"<PERSON>, Jr.\"><PERSON>, Jr.</a>, United States Army cavalry officer (b. 1822)", "links": [{"title": "<PERSON>, Jr.", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Jr."}]}, {"year": "1898", "text": "<PERSON>, Canadian-American nurse, soldier, and spy (b. 1841)", "html": "1898 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian-American nurse, soldier, and spy (b. 1841)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian-American nurse, soldier, and spy (b. 1841)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1901", "text": "<PERSON><PERSON><PERSON><PERSON>, Slovenian physicist and academic (b. 1853)", "html": "1901 - <a href=\"https://wikipedia.org/wiki/Igna<PERSON><PERSON>_Klemen%C4%8Di%C4%8D\" title=\"<PERSON>gna<PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Slovenian physicist and academic (b. 1853)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Igna<PERSON><PERSON>_<PERSON>lemen%C4%8Di%C4%8D\" title=\"Igna<PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Slovenian physicist and academic (b. 1853)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Igna<PERSON><PERSON>_<PERSON><PERSON>%C4%8Di%C4%8D"}]}, {"year": "1902", "text": "<PERSON>, German anthropologist, pathologist, and biologist (b. 1821)", "html": "1902 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German anthropologist, pathologist, and biologist (b. 1821)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German anthropologist, pathologist, and biologist (b. 1821)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1906", "text": "<PERSON>, Austrian physicist and philosopher (b. 1844)", "html": "1906 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Austrian physicist and philosopher (b. 1844)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Austrian physicist and philosopher (b. 1844)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1909", "text": "<PERSON>, French chemist (b. 1864)", "html": "1909 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French chemist (b. 1864)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French chemist (b. 1864)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Louis_Bouveault"}]}, {"year": "1912", "text": "<PERSON>, Jr., American LTG (Army), Medal of Honor recipient (b. 1845)", "html": "1912 - <a href=\"https://wikipedia.org/wiki/<PERSON>,_Jr.\" class=\"mw-redirect\" title=\"<PERSON>, Jr.\"><PERSON>, Jr.</a>, <a href=\"https://wikipedia.org/wiki/United_States\" title=\"United States\">American</a> <a href=\"https://wikipedia.org/wiki/Lieutenant_general_(United_States)\" title=\"Lieutenant general (United States)\"><PERSON>T<PERSON> (Army)</a>, <a href=\"https://wikipedia.org/wiki/Medal_of_Honor\" title=\"Medal of Honor\">Medal of Honor</a> recipient (b. 1845)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>,_Jr.\" class=\"mw-redirect\" title=\"<PERSON>, Jr.\"><PERSON>, Jr.</a>, <a href=\"https://wikipedia.org/wiki/United_States\" title=\"United States\">American</a> <a href=\"https://wikipedia.org/wiki/Lieutenant_general_(United_States)\" title=\"Lieutenant general (United States)\">LT<PERSON> (Army)</a>, <a href=\"https://wikipedia.org/wiki/Medal_of_Honor\" title=\"Medal of Honor\">Medal of Honor</a> recipient (b. 1845)", "links": [{"title": "<PERSON>, Jr.", "link": "https://wikipedia.org/wiki/<PERSON>,_Jr."}, {"title": "United States", "link": "https://wikipedia.org/wiki/United_States"}, {"title": "Lieutenant general (United States)", "link": "https://wikipedia.org/wiki/Lieutenant_general_(United_States)"}, {"title": "Medal of Honor", "link": "https://wikipedia.org/wiki/Medal_of_Honor"}]}, {"year": "1917", "text": "<PERSON>, Austrian-Polish physicist and mountaineer (b. 1872)", "html": "1917 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Austrian-Polish physicist and mountaineer (b. 1872)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Austrian-Polish physicist and mountaineer (b. 1872)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1920", "text": "<PERSON>, American actor (b. 1893)", "html": "1920 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor (b. 1893)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor (b. 1893)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1922", "text": "<PERSON><PERSON>, French painter (b. 1867)", "html": "1922 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, French painter (b. 1867)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, French painter (b. 1867)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1926", "text": "<PERSON>, German journalist and politician (b. 1890)", "html": "1926 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German journalist and politician (b. 1890)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German journalist and politician (b. 1890)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1930", "text": "<PERSON>, American soldier, businessman, and philanthropist (b. 1849)", "html": "1930 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American soldier, businessman, and philanthropist (b. 1849)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American soldier, businessman, and philanthropist (b. 1849)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1931", "text": "<PERSON>, Scottish footballer (b. 1909)", "html": "1931 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(footballer,_born_1909)\" title=\"<PERSON> (footballer, born 1909)\"><PERSON></a>, Scottish footballer (b. 1909)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(footballer,_born_1909)\" title=\"<PERSON> (footballer, born 1909)\"><PERSON></a>, Scottish footballer (b. 1909)", "links": [{"title": "<PERSON> (footballer, born 1909)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(footballer,_born_1909)"}]}, {"year": "1932", "text": "<PERSON>, Spanish journalist, author, and playwright (b. 1866)", "html": "1932 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Spanish journalist, author, and playwright (b. 1866)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Spanish journalist, author, and playwright (b. 1866)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1932", "text": "<PERSON>, German-American director, producer, and screenwriter (b. 1889)", "html": "1932 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German-American director, producer, and screenwriter (b. 1889)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German-American director, producer, and screenwriter (b. 1889)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1934", "text": "<PERSON>, Russian-Australian businessman, founded Myer Stores (b. 1878)", "html": "1934 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian-Australian businessman, founded <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>\" title=\"<PERSON><PERSON>\">Myer Stores</a> (b. 1878)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian-Australian businessman, founded <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>\" title=\"<PERSON><PERSON>\">Myer Stores</a> (b. 1878)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>er"}]}, {"year": "1936", "text": "<PERSON><PERSON>, French poet and critic (b. 1859)", "html": "1936 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, French poet and critic (b. 1859)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, French poet and critic (b. 1859)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1942", "text": "<PERSON>, French soldier and pilot (b. 1917)", "html": "1942 - <a href=\"https://wikipedia.org/wiki/Fran%C3%A7ois_de_Labouch%C3%A8re\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, French soldier and pilot (b. 1917)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Fran%C3%A7ois_de_Labouch%C3%A8re\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, French soldier and pilot (b. 1917)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Fran%C3%A7<PERSON>_de_Labouch%C3%A8re"}]}, {"year": "1945", "text": "<PERSON><PERSON>, Australian cricketer and footballer (b. 1877)", "html": "1945 - <a href=\"https://wikipedia.org/wiki/Clem_Hill\" title=\"Clem Hill\"><PERSON><PERSON></a>, Australian cricketer and footballer (b. 1877)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Clem_Hill\" title=\"Clem Hill\"><PERSON><PERSON></a>, Australian cricketer and footballer (b. 1877)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Clem_Hill"}]}, {"year": "1948", "text": "<PERSON>, American physicist and chemist (b. 1881)", "html": "1948 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American physicist and chemist (b. 1881)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American physicist and chemist (b. 1881)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1953", "text": "<PERSON>, Argentine-German agronomist and politician (b. 1895)", "html": "1953 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>%C3%A9\" title=\"<PERSON>\"><PERSON></a>, Argentine-German agronomist and politician (b. 1895)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>%C3%A9\" title=\"<PERSON>\"><PERSON></a>, Argentine-German agronomist and politician (b. 1895)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>%C3%A9"}]}, {"year": "1954", "text": "<PERSON><PERSON>, German lawyer and politician, Vice-Chancellor of Germany (b. 1860)", "html": "1954 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, German lawyer and politician, <a href=\"https://wikipedia.org/wiki/Vice-Chancellor_of_Germany\" title=\"Vice-Chancellor of Germany\">Vice-Chancellor of Germany</a> (b. 1860)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, German lawyer and politician, <a href=\"https://wikipedia.org/wiki/Vice-Chancellor_of_Germany\" title=\"Vice-Chancellor of Germany\">Vice-Chancellor of Germany</a> (b. 1860)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}, {"title": "Vice-Chancellor of Germany", "link": "https://wikipedia.org/wiki/Vice-Chancellor_of_Germany"}]}, {"year": "1955", "text": "<PERSON><PERSON>, Sr., Australian footballer and coach (b. 1911)", "html": "1955 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>,_Sr.\" class=\"mw-redirect\" title=\"<PERSON><PERSON>, Sr.\"><PERSON><PERSON>, Sr.</a>, Australian footballer and coach (b. 1911)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>,_Sr.\" class=\"mw-redirect\" title=\"<PERSON><PERSON>, Sr.\"><PERSON><PERSON>, Sr.</a>, Australian footballer and coach (b. 1911)", "links": [{"title": "<PERSON><PERSON>, Sr.", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>,_<PERSON>."}]}, {"year": "1961", "text": "<PERSON>, American academic (b. 1861)", "html": "1961 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American academic (b. 1861)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American academic (b. 1861)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1965", "text": "<PERSON>, Scottish journalist and politician, Secretary of State for Scotland (b. 1882)", "html": "1965 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(Scottish_politician)\" class=\"mw-redirect\" title=\"<PERSON> (Scottish politician)\"><PERSON></a>, Scottish journalist and politician, <a href=\"https://wikipedia.org/wiki/Secretary_of_State_for_Scotland\" title=\"Secretary of State for Scotland\">Secretary of State for Scotland</a> (b. 1882)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(Scottish_politician)\" class=\"mw-redirect\" title=\"<PERSON> (Scottish politician)\"><PERSON></a>, Scottish journalist and politician, <a href=\"https://wikipedia.org/wiki/Secretary_of_State_for_Scotland\" title=\"Secretary of State for Scotland\">Secretary of State for Scotland</a> (b. 1882)", "links": [{"title": "<PERSON> (Scottish politician)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(Scottish_politician)"}, {"title": "Secretary of State for Scotland", "link": "https://wikipedia.org/wiki/Secretary_of_State_for_Scotland"}]}, {"year": "1966", "text": "<PERSON><PERSON><PERSON><PERSON>, Hungarian golfer, tennis player, and architect (b. 1879)", "html": "1966 - <a href=\"https://wikipedia.org/wiki/Dezs%C5%91_Lauber\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Hungarian golfer, tennis player, and architect (b. 1879)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Dezs%C5%91_Lauber\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Hungarian golfer, tennis player, and architect (b. 1879)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Dezs%C5%91_Lauber"}]}, {"year": "1970", "text": "<PERSON><PERSON>, German-Austrian race car driver (b. 1942)", "html": "1970 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> Rindt\"><PERSON><PERSON></a>, German-Austrian race car driver (b. 1942)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> Rindt\"><PERSON><PERSON></a>, German-Austrian race car driver (b. 1942)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>t"}]}, {"year": "1972", "text": "<PERSON>, Australian cricketer and businessman (b. 1897)", "html": "1972 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian cricketer and businessman (b. 1897)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian cricketer and businessman (b. 1897)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1973", "text": "<PERSON>, American baseball player and coach (b. 1889)", "html": "1973 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player and coach (b. 1889)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player and coach (b. 1889)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1975", "text": "<PERSON>, American microbiologist (b. 1881)", "html": "1975 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American microbiologist (b. 1881)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American microbiologist (b. 1881)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1977", "text": "<PERSON>, Belgian poet and activist (b. 1897)", "html": "1977 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Belgian poet and activist (b. 1897)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Belgian poet and activist (b. 1897)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1979", "text": "<PERSON>, Italian cardinal (b. 1884)", "html": "1979 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian cardinal (b. 1884)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian cardinal (b. 1884)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1980", "text": "<PERSON>, Australian composer and educator (b. 1923)", "html": "1980 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian composer and educator (b. 1923)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian composer and educator (b. 1923)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Don_Banks"}]}, {"year": "1982", "text": "<PERSON>, English captain and pilot (b. 1910)", "html": "1982 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English captain and pilot (b. 1910)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English captain and pilot (b. 1910)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1984", "text": "<PERSON>, Indonesian politician and diplomat, 3rd Vice President of Indonesia (b. 1917)", "html": "1984 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Indonesian politician and diplomat, 3rd <a href=\"https://wikipedia.org/wiki/Vice_President_of_Indonesia\" title=\"Vice President of Indonesia\">Vice President of Indonesia</a> (b. 1917)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Indonesian politician and diplomat, 3rd <a href=\"https://wikipedia.org/wiki/Vice_President_of_Indonesia\" title=\"Vice President of Indonesia\">Vice President of Indonesia</a> (b. 1917)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Vice President of Indonesia", "link": "https://wikipedia.org/wiki/Vice_President_of_Indonesia"}]}, {"year": "1984", "text": "<PERSON>, American psychic and author (b. 1929)", "html": "1984 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American psychic and author (b. 1929)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American psychic and author (b. 1929)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1985", "text": "<PERSON>, Estonian engineer (b. 1914)", "html": "1985 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Estonian engineer (b. 1914)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Estonian engineer (b. 1914)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1986", "text": "<PERSON><PERSON><PERSON>, Indian model and youngest recipient of country's highest peacetime military award <PERSON><PERSON> (b. 1963)", "html": "1986 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON> Bhanot\"><PERSON><PERSON><PERSON></a>, Indian model and youngest recipient of country's highest peacetime military award <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>kra_(military_decoration)\" title=\"<PERSON><PERSON> Chakra (military decoration)\"><PERSON><PERSON></a> (b. 1963)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON> Bhanot\"><PERSON><PERSON><PERSON></a>, Indian model and youngest recipient of country's highest peacetime military award <a href=\"https://wikipedia.org/wiki/Ash<PERSON>_Chakra_(military_decoration)\" title=\"<PERSON>oka Chakra (military decoration)\"><PERSON><PERSON></a> (b. 1963)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON><PERSON>"}, {"title": "<PERSON><PERSON> Chakra (military decoration)", "link": "https://wikipedia.org/wiki/Ash<PERSON>_<PERSON>kra_(military_decoration)"}]}, {"year": "1988", "text": "<PERSON><PERSON>, German actor and singer (b. 1913)", "html": "1988 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>%C3%B6be\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, German actor and singer (b. 1913)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>%C3%B6be\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, German actor and singer (b. 1913)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Gert_Fr%C3%B6be"}]}, {"year": "1989", "text": "<PERSON>, Welsh-Australian chemical engineer (b. 1905)", "html": "1989 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Welsh-Australian chemical engineer (b. 1905)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Welsh-Australian chemical engineer (b. 1905)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1990", "text": "<PERSON>, Baron <PERSON>, English academic and diplomat (b. 1907)", "html": "1990 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Baron_<PERSON>\" title=\"<PERSON>, Baron <PERSON>\"><PERSON>, Baron <PERSON></a>, English academic and diplomat (b. 1907)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Baron_<PERSON>\" title=\"<PERSON>, Baron <PERSON>\"><PERSON>, Baron <PERSON></a>, English academic and diplomat (b. 1907)", "links": [{"title": "<PERSON>, Baron <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Baron_<PERSON>"}]}, {"year": "1990", "text": "<PERSON>, American cartoonist and publisher, co-founded Eisner & Iger (b. 1903)", "html": "1990 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American cartoonist and publisher, co-founded <a href=\"https://wikipedia.org/wiki/E<PERSON>ner_%26_Iger\" title=\"<PERSON><PERSON><PERSON> &amp; Iger\">E<PERSON>ner &amp; Iger</a> (b. 1903)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American cartoonist and publisher, co-founded <a href=\"https://wikipedia.org/wiki/E<PERSON>ner_%26_Iger\" title=\"<PERSON><PERSON><PERSON> &amp; Iger\">E<PERSON>ner &amp; Iger</a> (b. 1903)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Eisner & Iger", "link": "https://wikipedia.org/wiki/Eisner_%26_Iger"}]}, {"year": "1990", "text": "<PERSON>, Bulgarian politician (b. 1896)", "html": "1990 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Bulgarian politician (b. 1896)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Bulgarian politician (b. 1896)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1991", "text": "<PERSON><PERSON><PERSON>, Indian author and poet (b. 1931)", "html": "1991 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON> Josh<PERSON>\"><PERSON><PERSON><PERSON></a>, Indian author and poet (b. 1931)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON> Josh<PERSON>\"><PERSON><PERSON><PERSON></a>, Indian author and poet (b. 1931)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>i"}]}, {"year": "1992", "text": "<PERSON>, American author and poet (b. 1910)", "html": "1992 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author and poet (b. 1910)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author and poet (b. 1910)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1993", "text": "<PERSON>, French cinematographer (b. 1914)", "html": "1993 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French cinematographer (b. 1914)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French cinematographer (b. 1914)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1994", "text": "<PERSON><PERSON><PERSON>, Israeli mathematician and scholar (b. 1921)", "html": "1994 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_Amitsur\" title=\"Shimshon Amitsur\"><PERSON><PERSON><PERSON></a>, Israeli mathematician and scholar (b. 1921)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>itsur\" title=\"Shi<PERSON>hon Amitsur\"><PERSON><PERSON><PERSON></a>, Israeli mathematician and scholar (b. 1921)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_Amitsur"}]}, {"year": "1994", "text": "<PERSON>, Australian politician (b. 1946)", "html": "1994 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(Australian_politician)\" title=\"<PERSON> (Australian politician)\"><PERSON></a>, Australian politician (b. 1946)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(Australian_politician)\" title=\"<PERSON> (Australian politician)\"><PERSON></a>, Australian politician (b. 1946)", "links": [{"title": "<PERSON> (Australian politician)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(Australian_politician)"}]}, {"year": "1995", "text": "<PERSON><PERSON><PERSON>, Indonesian comedian, actor, and singer (b. 1939)", "html": "1995 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Indonesian comedian, actor, and singer (b. 1939)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Indonesian comedian, actor, and singer (b. 1939)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1995", "text": "<PERSON><PERSON>, Indian music composer, who mainly composed for Bengali, Hindi, Malayalam film and other films. (b. 1922)", "html": "1995 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Indian music composer, who mainly composed for Bengali, Hindi, Malayalam film and other films. (b. 1922)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Indian music composer, who mainly composed for Bengali, Hindi, Malayalam film and other films. (b. 1922)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1996", "text": "<PERSON>, Indian bishop (b. 1926)", "html": "1996 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>%27Souza\" title=\"<PERSON>Souza\"><PERSON></a>, Indian bishop (b. 1926)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>%27Souza\" title=\"<PERSON>Souza\"><PERSON></a>, Indian bishop (b. 1926)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Basil_Salvadore_D%27Souza"}]}, {"year": "1997", "text": "<PERSON>, American author and critic (b. 1907)", "html": "1997 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author and critic (b. 1907)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author and critic (b. 1907)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1997", "text": "<PERSON>, American actor (b. 1926)", "html": "1997 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON> Little <PERSON>\"><PERSON></a>, American actor (b. 1926)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON> Little Sky\"><PERSON></a>, American actor (b. 1926)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1997", "text": "<PERSON>, Hungarian conductor and director (b. 1912)", "html": "1997 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Hungarian conductor and director (b. 1912)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Hungarian conductor and director (b. 1912)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1997", "text": "<PERSON>, Albanian-Indian nun, missionary, and saint, Nobel Prize laureate (b. 1910)", "html": "1997 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Albanian-Indian nun, missionary, and saint, <a href=\"https://wikipedia.org/wiki/Nobel_Peace_Prize\" title=\"Nobel Peace Prize\">Nobel Prize</a> laureate (b. 1910)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Albanian-Indian nun, missionary, and saint, <a href=\"https://wikipedia.org/wiki/Nobel_Peace_Prize\" title=\"Nobel Peace Prize\">Nobel Prize</a> laureate (b. 1910)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Nobel Peace Prize", "link": "https://wikipedia.org/wiki/Nobel_Peace_Prize"}]}, {"year": "1998", "text": "<PERSON>, Canadian radio host (b. 1909)", "html": "1998 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian radio host (b. 1909)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian radio host (b. 1909)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1998", "text": "<PERSON>, Jr., Dutch economist and politician, Dutch Minister of Transport (b. 1922)", "html": "1998 - <a href=\"https://wikipedia.org/wiki/<PERSON>,_Jr.\" class=\"mw-redirect\" title=\"<PERSON>, Jr.\"><PERSON>, Jr.</a>, Dutch economist and politician, <a href=\"https://wikipedia.org/wiki/Ministry_of_Transport,_Public_Works_and_Water_Management_(Netherlands)\" class=\"mw-redirect\" title=\"Ministry of Transport, Public Works and Water Management (Netherlands)\">Dutch Minister of Transport</a> (b. 1922)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>,_Jr.\" class=\"mw-redirect\" title=\"<PERSON>, Jr.\"><PERSON>, Jr.</a>, Dutch economist and politician, <a href=\"https://wikipedia.org/wiki/Ministry_of_Transport,_Public_Works_and_Water_Management_(Netherlands)\" class=\"mw-redirect\" title=\"Ministry of Transport, Public Works and Water Management (Netherlands)\">Dutch Minister of Transport</a> (b. 1922)", "links": [{"title": "<PERSON>, Jr.", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Jr."}, {"title": "Ministry of Transport, Public Works and Water Management (Netherlands)", "link": "https://wikipedia.org/wiki/Ministry_of_Transport,_Public_Works_and_Water_Management_(Netherlands)"}]}, {"year": "1998", "text": "<PERSON><PERSON><PERSON>, Danish interior designer (b. 1926)", "html": "1998 - <a href=\"https://wikipedia.org/wiki/Verne<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Danish interior designer (b. 1926)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Danish interior designer (b. 1926)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Verner_<PERSON>ton"}]}, {"year": "1998", "text": "<PERSON>, American actor and director (b. 1921)", "html": "1998 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Leo Penn\"><PERSON></a>, American actor and director (b. 1921)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Leo_Penn\" title=\"Leo Penn\"><PERSON></a>, American actor and director (b. 1921)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Leo_Penn"}]}, {"year": "1999", "text": "<PERSON>, English historian and politician, Minister for Defence Procurement (b. 1928)", "html": "1999 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English historian and politician, <a href=\"https://wikipedia.org/wiki/Minister_for_Defence_Procurement\" class=\"mw-redirect\" title=\"Minister for Defence Procurement\">Minister for Defence Procurement</a> (b. 1928)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English historian and politician, <a href=\"https://wikipedia.org/wiki/Minister_for_Defence_Procurement\" class=\"mw-redirect\" title=\"Minister for Defence Procurement\">Minister for Defence Procurement</a> (b. 1928)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Minister for Defence Procurement", "link": "https://wikipedia.org/wiki/Minister_for_Defence_Procurement"}]}, {"year": "1999", "text": "<PERSON>, American director, producer, and screenwriter (b. 1914)", "html": "1999 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American director, producer, and screenwriter (b. 1914)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American director, producer, and screenwriter (b. 1914)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1999", "text": "<PERSON>, Canadian businessman and politician, Postmaster General of Canada (b. 1921)", "html": "1999 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian businessman and politician, <a href=\"https://wikipedia.org/wiki/Postmaster_General_of_Canada\" title=\"Postmaster General of Canada\">Postmaster General of Canada</a> (b. 1921)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian businessman and politician, <a href=\"https://wikipedia.org/wiki/Postmaster_General_of_Canada\" title=\"Postmaster General of Canada\">Postmaster General of Canada</a> (b. 1921)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Postmaster General of Canada", "link": "https://wikipedia.org/wiki/Postmaster_General_of_Canada"}]}, {"year": "2000", "text": "<PERSON>, Guyanese cricketer and politician (b. 1942)", "html": "2000 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Guyanese cricketer and politician (b. 1942)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Guyanese cricketer and politician (b. 1942)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2001", "text": "<PERSON>, American chef and author (b. 1914)", "html": "2001 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(chef)\" title=\"<PERSON> (chef)\"><PERSON></a>, American chef and author (b. 1914)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(chef)\" title=\"<PERSON> (chef)\"><PERSON></a>, American chef and author (b. 1914)", "links": [{"title": "<PERSON> (chef)", "link": "https://wikipedia.org/wiki/<PERSON>_(chef)"}]}, {"year": "2001", "text": "<PERSON>, Croatian economist and academic (b. 1912)", "html": "2001 - <a href=\"https://wikipedia.org/wiki/Vladimir_%C5%BDerjavi%C4%87\" title=\"<PERSON>\"><PERSON></a>, Croatian economist and academic (b. 1912)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_%C5%BDerjavi%C4%87\" title=\"<PERSON>\"><PERSON></a>, Croatian economist and academic (b. 1912)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Vladimir_%C5%BDerjavi%C4%87"}]}, {"year": "2002", "text": "<PERSON>, American cosmologist and astronomer (b. 1935)", "html": "2002 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American cosmologist and astronomer (b. 1935)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American cosmologist and astronomer (b. 1935)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2003", "text": "<PERSON><PERSON><PERSON>, Canadian-American singer and actress (b. 1927)", "html": "2003 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Canadian-American singer and actress (b. 1927)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Canadian-American singer and actress (b. 1927)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "2005", "text": "<PERSON>, Chilean general (b. 1917)", "html": "2005 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Chilean general (b. 1917)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Chilean general (b. 1917)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2007", "text": "<PERSON>, American engineer and politician (b. 1941)", "html": "2007 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(politician)\" class=\"mw-redirect\" title=\"<PERSON> (politician)\"><PERSON></a>, American engineer and politician (b. 1941)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>(politician)\" class=\"mw-redirect\" title=\"<PERSON> (politician)\"><PERSON></a>, American engineer and politician (b. 1941)", "links": [{"title": "<PERSON> (politician)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>(politician)"}]}, {"year": "2007", "text": "<PERSON>, American lawyer and politician (b. 1939)", "html": "2007 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and politician (b. 1939)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and politician (b. 1939)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "2007", "text": "<PERSON>, Norwegian singer-songwriter and guitarist (b. 1976)", "html": "2007 - <a href=\"https://wikipedia.org/wiki/<PERSON>_(musician)\" title=\"<PERSON> (musician)\"><PERSON></a>, Norwegian singer-songwriter and guitarist (b. 1976)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(musician)\" title=\"<PERSON> (musician)\"><PERSON></a>, Norwegian singer-songwriter and guitarist (b. 1976)", "links": [{"title": "<PERSON> (musician)", "link": "https://wikipedia.org/wiki/<PERSON>_(musician)"}]}, {"year": "2007", "text": "<PERSON><PERSON> <PERSON>, American pastor and author (b. 1930)", "html": "2007 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON> <PERSON></a>, American pastor and author (b. 1930)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON> <PERSON></a>, American pastor and author (b. 1930)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>"}]}, {"year": "2007", "text": "<PERSON><PERSON>, Greek director and screenwriter (b. 1939)", "html": "2007 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Greek director and screenwriter (b. 1939)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Greek director and screenwriter (b. 1939)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "2009", "text": "<PERSON><PERSON>, Nigerian lawyer and activist (b. 1938)", "html": "2009 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Nigerian lawyer and activist (b. 1938)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Nigerian lawyer and activist (b. 1938)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "2010", "text": "<PERSON><PERSON>, Australian author and academic (b. 1932)", "html": "2010 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"Hedley Beare\"><PERSON><PERSON></a>, Australian author and academic (b. 1932)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> Bear<PERSON>\"><PERSON><PERSON></a>, Australian author and academic (b. 1932)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "2010", "text": "<PERSON>, Belgian-Dutch poet and painter (b. 1922)", "html": "2010 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_Be<PERSON>loo\" class=\"mw-redirect\" title=\"<PERSON> Beverloo\"><PERSON>lo<PERSON></a>, Belgian-Dutch poet and painter (b. 1922)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_Be<PERSON>loo\" class=\"mw-redirect\" title=\"<PERSON> Beverloo\"><PERSON>lo<PERSON></a>, Belgian-Dutch poet and painter (b. 1922)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>_<PERSON>"}]}, {"year": "2012", "text": "<PERSON><PERSON>, Turkish-Bosnian footballer (b. 1986)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/Ediz_Bahtiyaro%C4%9Flu\" title=\"Ediz Bahtiyaroğlu\"><PERSON><PERSON></a>, Turkish-Bosnian footballer (b. 1986)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Ediz_Bahtiyaro%C4%9Flu\" title=\"Ediz Bahtiyaroğlu\"><PERSON><PERSON>roğlu</a>, Turkish-Bosnian footballer (b. 1986)", "links": [{"title": "Ediz <PERSON>", "link": "https://wikipedia.org/wiki/Ediz_Bahtiyaro%C4%9Flu"}]}, {"year": "2012", "text": "<PERSON>, Australian cricketer and field hockey player (b. 1926)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian cricketer and field hockey player (b. 1926)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian cricketer and field hockey player (b. 1926)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2012", "text": "<PERSON>, Russian-American actress and author (b. 1946)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/Victoria_Fyodorova\" title=\"<PERSON>\"><PERSON></a>, Russian-American actress and author (b. 1946)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Victoria_Fyodorova\" title=\"<PERSON> F<PERSON>\"><PERSON></a>, Russian-American actress and author (b. 1946)", "links": [{"title": "<PERSON>dorova", "link": "https://wikipedia.org/wiki/Victoria_Fyo<PERSON>ova"}]}, {"year": "2012", "text": "<PERSON>, English jockey and journalist (b. 1929)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English jockey and journalist (b. 1929)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English jockey and journalist (b. 1929)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2013", "text": "<PERSON>, American lawyer and politician (b. 1950)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and politician (b. 1950)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and politician (b. 1950)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2013", "text": "<PERSON>, English pilot, journalist, and author (b. 1922)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English pilot, journalist, and author (b. 1922)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English pilot, journalist, and author (b. 1922)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2013", "text": "<PERSON><PERSON>, American journalist and academic (b. 1975)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> Jordan\"><PERSON><PERSON></a>, American journalist and academic (b. 1975)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> Jordan\"><PERSON><PERSON></a>, American journalist and academic (b. 1975)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Isamu_Jordan"}]}, {"year": "2014", "text": "<PERSON>, American journalist (b. 1930)", "html": "2014 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(journalist)\" title=\"<PERSON> (journalist)\"><PERSON></a>, American journalist (b. 1930)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(journalist)\" title=\"<PERSON> (journalist)\"><PERSON></a>, American journalist (b. 1930)", "links": [{"title": "<PERSON> (journalist)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(journalist)"}]}, {"year": "2014", "text": "<PERSON>, German mathematician, author, and academic (b. 1964)", "html": "2014 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German mathematician, author, and academic (b. 1964)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German mathematician, author, and academic (b. 1964)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>eus<PERSON>"}]}, {"year": "2015", "text": "<PERSON><PERSON>, Malaysian-Singaporean businessman, founded Eng Wah Global (b. 1923)", "html": "2015 - <a href=\"https://wikipedia.org/wiki/Goh_Eng_Wah\" title=\"Goh Eng Wah\"><PERSON><PERSON></a>, Malaysian-Singaporean businessman, founded <a href=\"https://wikipedia.org/wiki/Eng_Wah_Global\" title=\"Eng Wah Global\">Eng Wah Global</a> (b. 1923)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Goh_Eng_Wah\" title=\"Goh Eng Wah\"><PERSON><PERSON></a>, Malaysian-Singaporean businessman, founded <a href=\"https://wikipedia.org/wiki/Eng_Wah_Global\" title=\"Eng Wah Global\">Eng Wah Global</a> (b. 1923)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Goh_Eng_Wah"}, {"title": "<PERSON>g <PERSON>", "link": "https://wikipedia.org/wiki/Eng_Wah_Global"}]}, {"year": "2015", "text": "<PERSON><PERSON><PERSON>, Indian singer-songwriter (b. 1964)", "html": "2015 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Indian singer-songwriter (b. 1964)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Indian singer-songwriter (b. 1964)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "2015", "text": "<PERSON>, American baseball player and businessman (b. 1929)", "html": "2015 - <a href=\"https://wikipedia.org/wiki/Chester_Stranczek\" title=\"<PERSON>\"><PERSON></a>, American baseball player and businessman (b. 1929)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Chester_Stranczek\" title=\"<PERSON>ek\"><PERSON></a>, American baseball player and businessman (b. 1929)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Chester_Stranczek"}]}, {"year": "2016", "text": "<PERSON>, American actor (b. 1925)", "html": "2016 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%27Brian\" title=\"<PERSON>\"><PERSON></a>, American actor (b. 1925)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%27Brian\" title=\"<PERSON>\"><PERSON></a>, American actor (b. 1925)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_O%27Brian"}]}, {"year": "2016", "text": "<PERSON>, American lawyer, writer, and political activist (b. 1924)", "html": "2016 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer, writer, and political activist (b. 1924)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer, writer, and political activist (b. 1924)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2017", "text": "<PERSON><PERSON>, Dutch-American physicist and Nobel laureate (b. 1920)", "html": "2017 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Dutch-American physicist and Nobel laureate (b. 1920)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Dutch-American physicist and Nobel laureate (b. 1920)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Nicolaas_B<PERSON>bergen"}]}, {"year": "2018", "text": "<PERSON><PERSON><PERSON><PERSON><PERSON>, Indian Gujarati writer and journalist (b. 1934)", "html": "2018 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON><PERSON></a>, Indian Gujarati writer and journalist (b. 1934)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON><PERSON></a>, Indian Gujarati writer and journalist (b. 1934)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "2018", "text": "<PERSON><PERSON>, Brazilian actress (b. 1926)", "html": "2018 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Brazilian actress (b. 1926)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Brazilian actress (b. 1926)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "2019", "text": "<PERSON>, Mexican painter, sculptor, and graphic artist (b. 1940)", "html": "2019 - <a href=\"https://wikipedia.org/wiki/Francisco_Toledo\" title=\"Francisco Toledo\">Francisco Toledo</a>, Mexican painter, sculptor, and graphic artist (b. 1940)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Francisco_Toledo\" title=\"Francisco Toledo\">Francisco Toledo</a>, Mexican painter, sculptor, and graphic artist (b. 1940)", "links": [{"title": "Francisco Toledo", "link": "https://wikipedia.org/wiki/Francisco_Toledo"}]}, {"year": "2021", "text": "<PERSON>, English singer, member of Girls Aloud (b. 1981)", "html": "2021 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English singer, member of <a href=\"https://wikipedia.org/wiki/Girls_Aloud\" title=\"Girls Aloud\">Girls Aloud</a> (b. 1981)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English singer, member of <a href=\"https://wikipedia.org/wiki/Girls_Aloud\" title=\"Girls Aloud\">Girls Aloud</a> (b. 1981)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Girls Aloud", "link": "https://wikipedia.org/wiki/<PERSON>_Al<PERSON>"}]}, {"year": "2024", "text": "<PERSON>, Ugandan athlete (b. 1991)", "html": "2024 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Ugandan athlete (b. 1991)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Ugandan athlete (b. 1991)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2024", "text": "<PERSON><PERSON>, English musician (b. 1938)", "html": "2024 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, English musician (b. 1938)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, English musician (b. 1938)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "2024", "text": "<PERSON><PERSON>, Indian historian of mathematics (b. 1935)", "html": "2024 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Indian historian of mathematics (b. 1935)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Indian historian of mathematics (b. 1935)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "2024", "text": "<PERSON><PERSON><PERSON><PERSON>, Brazilian pianist and composer (b. 1941)", "html": "2024 - <a href=\"https://wikipedia.org/wiki/S%C3%A9<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Brazilian pianist and composer (b. 1941)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/S%C3%A9<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Brazilian pianist and composer (b. 1941)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/S%C3%A9rg<PERSON>_<PERSON>"}]}, {"year": "2024", "text": "<PERSON>, American rapper (b. 1990)", "html": "2024 - <a href=\"https://wikipedia.org/wiki/<PERSON>_Ho<PERSON>_<PERSON>\" title=\"Rich Homie <PERSON>\"><PERSON></a>, American rapper (b. 1990)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"Rich Homie <PERSON>\"><PERSON></a>, American rapper (b. 1990)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "2024", "text": "<PERSON>, French film director and screenwriter (b. 1967)", "html": "2024 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French film director and screenwriter (b. 1967)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French film director and screenwriter (b. 1967)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}]}}