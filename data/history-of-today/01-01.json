{"date": "January 1", "url": "https://wikipedia.org/wiki/January_1", "data": {"Events": [{"year": "153 BC", "text": "For the first time, Roman consuls begin their year in office on January 1.", "html": "153 BC - 153 BC - For the first time, <a href=\"https://wikipedia.org/wiki/Roman_consul\" title=\"Roman consul\">Roman consuls</a> begin their year in office on January 1.", "no_year_html": "153 BC - For the first time, <a href=\"https://wikipedia.org/wiki/Roman_consul\" title=\"Roman consul\">Roman consuls</a> begin their year in office on January 1.", "links": [{"title": "Roman consul", "link": "https://wikipedia.org/wiki/Roman_consul"}]}, {"year": "45 BC", "text": "The Julian calendar takes effect as the civil calendar of the Roman Republic, establishing January 1 as the new date of the new year.", "html": "45 BC - 45 BC - The <a href=\"https://wikipedia.org/wiki/Julian_calendar\" title=\"Julian calendar\">Julian calendar</a> takes effect as the civil calendar of the Roman Republic, establishing January 1 as the new date of the new year.", "no_year_html": "45 BC - The <a href=\"https://wikipedia.org/wiki/Julian_calendar\" title=\"Julian calendar\">Julian calendar</a> takes effect as the civil calendar of the Roman Republic, establishing January 1 as the new date of the new year.", "links": [{"title": "Julian calendar", "link": "https://wikipedia.org/wiki/Julian_calendar"}]}, {"year": "42 BC", "text": "The Roman Senate posthumously deifies <PERSON>.", "html": "42 BC - 42 BC - The <a href=\"https://wikipedia.org/wiki/Roman_Senate\" title=\"Roman Senate\">Roman Senate</a> posthumously <a href=\"https://wikipedia.org/wiki/Apotheosis\" title=\"Apotheosis\">deifies</a> <a href=\"https://wikipedia.org/wiki/Julius_Caesar\" title=\"Julius Caesar\"><PERSON> Caesar</a>.", "no_year_html": "42 BC - The <a href=\"https://wikipedia.org/wiki/Roman_Senate\" title=\"Roman Senate\">Roman Senate</a> posthumously <a href=\"https://wikipedia.org/wiki/Apotheosis\" title=\"Apotheosis\">deifies</a> <a href=\"https://wikipedia.org/wiki/Julius_Caesar\" title=\"Julius Caesar\">Julius Caesar</a>.", "links": [{"title": "Roman Senate", "link": "https://wikipedia.org/wiki/Roman_Senate"}, {"title": "Apotheosis", "link": "https://wikipedia.org/wiki/Apotheosis"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "193", "text": "The Senate chooses <PERSON><PERSON><PERSON> against his will to succeed <PERSON><PERSON><PERSON> as Roman emperor.", "html": "193 - The Senate chooses <a href=\"https://wikipedia.org/wiki/Pertinax\" title=\"Pertinax\"><PERSON><PERSON><PERSON></a> against his will to succeed <a href=\"https://wikipedia.org/wiki/Commodus\" title=\"Commodus\"><PERSON>mm<PERSON></a> as <a href=\"https://wikipedia.org/wiki/Roman_emperor\" title=\"Roman emperor\">Roman emperor</a>.", "no_year_html": "The Senate chooses <a href=\"https://wikipedia.org/wiki/Pertinax\" title=\"Pertinax\"><PERSON><PERSON><PERSON></a> against his will to succeed <a href=\"https://wikipedia.org/wiki/Commodus\" title=\"Commodus\">Comm<PERSON></a> as <a href=\"https://wikipedia.org/wiki/Roman_emperor\" title=\"Roman emperor\">Roman emperor</a>.", "links": [{"title": "Pertinax", "link": "https://wikipedia.org/wiki/Pertinax"}, {"title": "Commodus", "link": "https://wikipedia.org/wiki/Commodus"}, {"title": "Roman emperor", "link": "https://wikipedia.org/wiki/Roman_emperor"}]}, {"year": "404", "text": "Saint <PERSON><PERSON><PERSON> tries to stop a gladiatorial fight in a Roman amphitheatre, and is stoned to death by the crowd. This act impresses the Christian Emperor <PERSON><PERSON>, who issues a historic ban on gladiatorial fights.", "html": "404 - <a href=\"https://wikipedia.org/wiki/Saint_Telemachus\" title=\"Saint Telemachus\">Saint Telemachus</a> tries to stop a <a href=\"https://wikipedia.org/wiki/Gladiator\" title=\"Gladiator\">gladiatorial</a> fight in a Roman <a href=\"https://wikipedia.org/wiki/Amphitheatre\" title=\"Amphitheatre\">amphitheatre</a>, and is <a href=\"https://wikipedia.org/wiki/Stoning\" title=\"Stoning\">stoned to death</a> by the crowd. This act impresses the Christian Emperor <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_(emperor)\" class=\"mw-redirect\" title=\"<PERSON><PERSON> (emperor)\"><PERSON><PERSON></a>, who issues a historic ban on gladiatorial fights.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Saint_Telemachus\" title=\"Saint Telemachus\">Saint Telemachus</a> tries to stop a <a href=\"https://wikipedia.org/wiki/Gladiator\" title=\"Gladiator\">gladiatorial</a> fight in a Roman <a href=\"https://wikipedia.org/wiki/Amphitheatre\" title=\"Amphitheatre\">amphitheatre</a>, and is <a href=\"https://wikipedia.org/wiki/Stoning\" title=\"Stoning\">stoned to death</a> by the crowd. This act impresses the Christian Emperor <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_(emperor)\" class=\"mw-redirect\" title=\"<PERSON><PERSON> (emperor)\"><PERSON><PERSON></a>, who issues a historic ban on gladiatorial fights.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Saint_Telemachus"}, {"title": "Gladiator", "link": "https://wikipedia.org/wiki/Gladiator"}, {"title": "Amphitheatre", "link": "https://wikipedia.org/wiki/Amphitheatre"}, {"title": "Stoning", "link": "https://wikipedia.org/wiki/Stoning"}, {"title": "<PERSON><PERSON> (emperor)", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_(emperor)"}]}, {"year": "417", "text": "Emperor <PERSON><PERSON> forces <PERSON><PERSON><PERSON> into marriage to <PERSON><PERSON><PERSON>, his famous general (magister militum) (probable).", "html": "417 - <a href=\"https://wikipedia.org/wiki/Emperor_Honorius\" class=\"mw-redirect\" title=\"Emperor <PERSON>ius\">Emperor <PERSON><PERSON></a> forces <a href=\"https://wikipedia.org/wiki/Galla_Placidia\" title=\"Galla Placidia\"><PERSON><PERSON><PERSON> Placidia</a> into marriage to <a href=\"https://wikipedia.org/wiki/Constantius_III\" title=\"<PERSON><PERSON>ius III\"><PERSON><PERSON><PERSON></a>, his famous general (<i><a href=\"https://wikipedia.org/wiki/Magister_militum\" title=\"Magister militum\">magister militum</a></i>) (probable).", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Emperor_Honorius\" class=\"mw-redirect\" title=\"Emperor <PERSON>ius\">Emperor <PERSON><PERSON></a> forces <a href=\"https://wikipedia.org/wiki/Galla_Placidia\" title=\"Galla Placidia\"><PERSON><PERSON><PERSON> Placidia</a> into marriage to <a href=\"https://wikipedia.org/wiki/Con<PERSON>ius_III\" title=\"<PERSON><PERSON>ius III\"><PERSON><PERSON><PERSON></a>, his famous general (<i><a href=\"https://wikipedia.org/wiki/Magister_militum\" title=\"Magister militum\">magister militum</a></i>) (probable).", "links": [{"title": "Emperor <PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>"}, {"title": "Galla Placid<PERSON>", "link": "https://wikipedia.org/wiki/Galla_Placidia"}, {"title": "<PERSON><PERSON><PERSON> III", "link": "https://wikipedia.org/wiki/Constantius_III"}, {"title": "Magister militum", "link": "https://wikipedia.org/wiki/Magister_militum"}]}, {"year": "947", "text": "Emperor <PERSON><PERSON> of the Khitan-led Liao dynasty captures Daliang, ending the dynasty and empire of the Later Jin.", "html": "947 - Emperor <a href=\"https://wikipedia.org/wiki/Emperor_<PERSON><PERSON>_of_Liao\" title=\"Emperor <PERSON>zong of Liao\"><PERSON><PERSON></a> of the <a href=\"https://wikipedia.org/wiki/Khitan_people\" title=\"Khitan people\">Khitan</a>-led <a href=\"https://wikipedia.org/wiki/Liao_dynasty\" title=\"Liao dynasty\">Liao dynasty</a> captures <a href=\"https://wikipedia.org/wiki/Kaifeng\" title=\"Kaifeng\">Daliang</a>, ending the dynasty and empire of the <a href=\"https://wikipedia.org/wiki/Later_Jin_(Five_Dynasties)\" title=\"Later Jin (Five Dynasties)\">Later Jin</a>.", "no_year_html": "Emperor <a href=\"https://wikipedia.org/wiki/Emperor_<PERSON><PERSON>_of_Liao\" title=\"Emperor <PERSON><PERSON> of Liao\"><PERSON><PERSON></a> of the <a href=\"https://wikipedia.org/wiki/Khitan_people\" title=\"Khitan people\">Khitan</a>-led <a href=\"https://wikipedia.org/wiki/Liao_dynasty\" title=\"Liao dynasty\">Liao dynasty</a> captures <a href=\"https://wikipedia.org/wiki/Kaifeng\" title=\"Kaifeng\">Daliang</a>, ending the dynasty and empire of the <a href=\"https://wikipedia.org/wiki/Later_Jin_(Five_Dynasties)\" title=\"Later Jin (Five Dynasties)\">Later Jin</a>.", "links": [{"title": "Emperor <PERSON><PERSON> of Liao", "link": "https://wikipedia.org/wiki/Emperor_<PERSON><PERSON>_of_Liao"}, {"title": "Khitan people", "link": "https://wikipedia.org/wiki/Khitan_people"}, {"title": "Liao dynasty", "link": "https://wikipedia.org/wiki/Liao_dynasty"}, {"title": "Kaifeng", "link": "https://wikipedia.org/wiki/Kai<PERSON>"}, {"title": "Later Jin (Five Dynasties)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(Five_Dynasties)"}]}, {"year": "1001", "text": "Grand Prince <PERSON> of Hungary is named the first King of Hungary by <PERSON> <PERSON> (probable).", "html": "1001 - Grand Prince <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Hungary\" title=\"<PERSON> I of Hungary\"><PERSON> of Hungary</a> is named the first <a href=\"https://wikipedia.org/wiki/King_of_Hungary\" title=\"King of Hungary\">King of Hungary</a> by <a href=\"https://wikipedia.org/wiki/<PERSON>_Sylvester_II\" title=\"Pope Sylvester II\"><PERSON> II</a> (probable).", "no_year_html": "Grand Prince <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Hungary\" title=\"<PERSON> of Hungary\"><PERSON> of Hungary</a> is named the first <a href=\"https://wikipedia.org/wiki/King_of_Hungary\" title=\"King of Hungary\">King of Hungary</a> by <a href=\"https://wikipedia.org/wiki/<PERSON>_Sylvester_II\" title=\"Pope Sylvester II\"><PERSON> II</a> (probable).", "links": [{"title": "<PERSON> of Hungary", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_Hungary"}, {"title": "King of Hungary", "link": "https://wikipedia.org/wiki/King_of_Hungary"}, {"title": "<PERSON> II", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1068", "text": "<PERSON><PERSON> marries <PERSON><PERSON><PERSON><PERSON> and is crowned Byzantine Emperor.", "html": "1068 - <a href=\"https://wikipedia.org/wiki/Romanos_IV_Diogenes\" title=\"Romanos IV Diogenes\">Romano<PERSON> IV Diogenes</a> marries <a href=\"https://wikipedia.org/wiki/Eudoki<PERSON>_Makrembolitissa\" title=\"<PERSON><PERSON><PERSON><PERSON> Ma<PERSON>rembolitis<PERSON>\"><PERSON><PERSON><PERSON><PERSON></a> and is crowned <a href=\"https://wikipedia.org/wiki/Byzantine_Emperor\" class=\"mw-redirect\" title=\"Byzantine Emperor\">Byzantine Emperor</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>s_IV_Diogenes\" title=\"Romanos IV Diogenes\">Romanos IV Diogenes</a> marries <a href=\"https://wikipedia.org/wiki/Eudo<PERSON><PERSON>_Makrembolitissa\" title=\"<PERSON><PERSON><PERSON><PERSON>bol<PERSON>\"><PERSON><PERSON><PERSON><PERSON></a> and is crowned <a href=\"https://wikipedia.org/wiki/Byzantine_Emperor\" class=\"mw-redirect\" title=\"Byzantine Emperor\">Byzantine Emperor</a>.", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_IV_Di<PERSON>es"}, {"title": "<PERSON><PERSON><PERSON>a <PERSON>", "link": "https://wikipedia.org/wiki/Eudokia_Ma<PERSON>rembolitissa"}, {"title": "Byzantine Emperor", "link": "https://wikipedia.org/wiki/Byzantine_Emperor"}]}, {"year": "1259", "text": "<PERSON> is proclaimed co-emperor of the Empire of Nicaea with his ward <PERSON>.", "html": "1259 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> is proclaimed co-emperor of the <a href=\"https://wikipedia.org/wiki/Empire_of_Nicaea\" title=\"Empire of Nicaea\">Empire of Nicaea</a> with his ward <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> VIII Palaiologos\"><PERSON></a> is proclaimed co-emperor of the <a href=\"https://wikipedia.org/wiki/Empire_of_Nicaea\" title=\"Empire of Nicaea\">Empire of Nicaea</a> with his ward <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "Empire of Nicaea", "link": "https://wikipedia.org/wiki/Empire_of_Nicaea"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1438", "text": "<PERSON> of Habsburg is crowned King of Hungary.", "html": "1438 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Germany\" title=\"<PERSON> II of Germany\"><PERSON> of Habsburg</a> is crowned <a href=\"https://wikipedia.org/wiki/King_of_Hungary\" title=\"King of Hungary\">King of Hungary</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Germany\" title=\"<PERSON> II of Germany\"><PERSON> of Habsburg</a> is crowned <a href=\"https://wikipedia.org/wiki/King_of_Hungary\" title=\"King of Hungary\">King of Hungary</a>.", "links": [{"title": "<PERSON> of Germany", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Germany"}, {"title": "King of Hungary", "link": "https://wikipedia.org/wiki/King_of_Hungary"}]}, {"year": "1502", "text": "The present-day location of Rio de Janeiro, Brazil, is first explored by the Portuguese.", "html": "1502 - The present-day location of <a href=\"https://wikipedia.org/wiki/Rio_de_Janeiro\" title=\"Rio de Janeiro\">Rio de Janeiro</a>, <a href=\"https://wikipedia.org/wiki/Brazil\" title=\"Brazil\">Brazil</a>, is first explored by the <a href=\"https://wikipedia.org/wiki/Portuguese_Empire\" title=\"Portuguese Empire\">Portuguese</a>.", "no_year_html": "The present-day location of <a href=\"https://wikipedia.org/wiki/Rio_de_Janeiro\" title=\"Rio de Janeiro\">Rio de Janeiro</a>, <a href=\"https://wikipedia.org/wiki/Brazil\" title=\"Brazil\">Brazil</a>, is first explored by the <a href=\"https://wikipedia.org/wiki/Portuguese_Empire\" title=\"Portuguese Empire\">Portuguese</a>.", "links": [{"title": "Rio de Janeiro", "link": "https://wikipedia.org/wiki/Rio_de_Janeiro"}, {"title": "Brazil", "link": "https://wikipedia.org/wiki/Brazil"}, {"title": "Portuguese Empire", "link": "https://wikipedia.org/wiki/Portuguese_Empire"}]}, {"year": "1515", "text": "Twenty-year-old <PERSON>, Duke of Brittany, succeeds to the French throne following the death of his father-in-law, <PERSON>.", "html": "1515 - Twenty-year-old <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_France\" title=\"<PERSON> of France\"><PERSON>, Duke of Brittany</a>, succeeds to the <a href=\"https://wikipedia.org/wiki/List_of_French_monarchs\" title=\"List of French monarchs\">French throne</a> following the death of his father-in-law, <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_France\" class=\"mw-redirect\" title=\"<PERSON> of France\"><PERSON></a>.", "no_year_html": "Twenty-year-old <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_France\" title=\"<PERSON> of France\"><PERSON>, Duke of Brittany</a>, succeeds to the <a href=\"https://wikipedia.org/wiki/List_of_French_monarchs\" title=\"List of French monarchs\">French throne</a> following the death of his father-in-law, <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_France\" class=\"mw-redirect\" title=\"<PERSON> of France\"><PERSON></a>.", "links": [{"title": "<PERSON> of France", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_France"}, {"title": "List of French monarchs", "link": "https://wikipedia.org/wiki/List_of_French_monarchs"}, {"title": "<PERSON> of France", "link": "https://wikipedia.org/wiki/Louis_<PERSON>_of_France"}]}, {"year": "1527", "text": "Croatian nobles elect <PERSON>, Archduke of Austria as King of Croatia in the 1527 election in Cetin.", "html": "1527 - Croatian nobles elect <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Archduke_of_Austria\" class=\"mw-redirect\" title=\"<PERSON>, Archduke of Austria\"><PERSON>, Archduke of Austria</a> as <a href=\"https://wikipedia.org/wiki/King_of_Croatia\" class=\"mw-redirect\" title=\"King of Croatia\">King of Croatia</a> in the <a href=\"https://wikipedia.org/wiki/1527_election_in_Cetin\" title=\"1527 election in Cetin\">1527 election in Cetin</a>.", "no_year_html": "Croatian nobles elect <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Archduke_of_Austria\" class=\"mw-redirect\" title=\"<PERSON>, Archduke of Austria\"><PERSON>, Archduke of Austria</a> as <a href=\"https://wikipedia.org/wiki/King_of_Croatia\" class=\"mw-redirect\" title=\"King of Croatia\">King of Croatia</a> in the <a href=\"https://wikipedia.org/wiki/1527_election_in_Cetin\" title=\"1527 election in Cetin\">1527 election in Cetin</a>.", "links": [{"title": "<PERSON>, Archduke of Austria", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_<PERSON><PERSON>ke_of_Austria"}, {"title": "King of Croatia", "link": "https://wikipedia.org/wiki/King_of_Croatia"}, {"title": "1527 election in Cetin", "link": "https://wikipedia.org/wiki/1527_election_in_Cetin"}]}, {"year": "1600", "text": "Scotland recognises January 1 as the start of the year, instead of March 25.", "html": "1600 - <a href=\"https://wikipedia.org/wiki/Kingdom_of_Scotland\" title=\"Kingdom of Scotland\">Scotland</a> recognises January 1 as the start of the year, instead of March 25.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Kingdom_of_Scotland\" title=\"Kingdom of Scotland\">Scotland</a> recognises January 1 as the start of the year, instead of March 25.", "links": [{"title": "Kingdom of Scotland", "link": "https://wikipedia.org/wiki/Kingdom_of_Scotland"}]}, {"year": "1604", "text": "The Masque of Indian and China Knights is performed by courtiers of James VI and I at Hampton Court.", "html": "1604 - <i><a href=\"https://wikipedia.org/wiki/The_Masque_of_Indian_and_China_Knights\" title=\"The Masque of Indian and China Knights\">The Masque of Indian and China Knights</a></i> is performed by courtiers of <a href=\"https://wikipedia.org/wiki/<PERSON>_VI_and_I\" title=\"<PERSON> VI and I\"><PERSON> VI and I</a> at <a href=\"https://wikipedia.org/wiki/Hampton_Court\" class=\"mw-redirect\" title=\"Hampton Court\">Hampton Court</a>.", "no_year_html": "<i><a href=\"https://wikipedia.org/wiki/The_Masque_of_Indian_and_China_Knights\" title=\"The Masque of Indian and China Knights\">The Masque of Indian and China Knights</a></i> is performed by courtiers of <a href=\"https://wikipedia.org/wiki/<PERSON>_VI_and_I\" title=\"<PERSON> VI and I\"><PERSON> VI and I</a> at <a href=\"https://wikipedia.org/wiki/Hampton_Court\" class=\"mw-redirect\" title=\"Hampton Court\">Hampton Court</a>.", "links": [{"title": "The Masque of Indian and China Knights", "link": "https://wikipedia.org/wiki/The_Masque_of_Indian_and_China_Knights"}, {"title": "James <PERSON> and I", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_and_I"}, {"title": "Hampton Court", "link": "https://wikipedia.org/wiki/Hampton_Court"}]}, {"year": "1651", "text": "<PERSON> is crowned King of Scotland at Scone Palace.", "html": "1651 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_England\" title=\"<PERSON> II of England\"><PERSON> II</a> is <a href=\"https://wikipedia.org/wiki/Scottish_coronation_of_<PERSON>_II\" title=\"Scottish coronation of <PERSON> II\">crowned</a> <a href=\"https://wikipedia.org/wiki/King_of_Scotland\" class=\"mw-redirect\" title=\"King of Scotland\">King of Scotland</a> at <a href=\"https://wikipedia.org/wiki/Scone_Palace\" title=\"Scone Palace\">Scone Palace</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_England\" title=\"<PERSON> II of England\"><PERSON> II</a> is <a href=\"https://wikipedia.org/wiki/Scottish_coronation_of_<PERSON>_II\" title=\"Scottish coronation of <PERSON> II\">crowned</a> <a href=\"https://wikipedia.org/wiki/King_of_Scotland\" class=\"mw-redirect\" title=\"King of Scotland\">King of Scotland</a> at <a href=\"https://wikipedia.org/wiki/Scone_Palace\" title=\"Scone Palace\">Scone Palace</a>.", "links": [{"title": "<PERSON> of England", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_England"}, {"title": "Scottish coronation of <PERSON>", "link": "https://wikipedia.org/wiki/Scottish_coronation_of_<PERSON>_<PERSON>"}, {"title": "King of Scotland", "link": "https://wikipedia.org/wiki/King_of_Scotland"}, {"title": "Scone Palace", "link": "https://wikipedia.org/wiki/Scone_Palace"}]}, {"year": "1700", "text": "Russia begins using the Anno Domini era instead of the Anno Mundi era of the Byzantine Empire.", "html": "1700 - Russia begins using the <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a> era instead of the <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_Mu<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a> era of the <a href=\"https://wikipedia.org/wiki/Byzantine_Empire\" title=\"Byzantine Empire\">Byzantine Empire</a>.", "no_year_html": "Russia begins using the <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a> era instead of the <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a> era of the <a href=\"https://wikipedia.org/wiki/Byzantine_Empire\" title=\"Byzantine Empire\">Byzantine Empire</a>.", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>"}, {"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Ann<PERSON>_<PERSON>"}, {"title": "Byzantine Empire", "link": "https://wikipedia.org/wiki/Byzantine_Empire"}]}, {"year": "1707", "text": "<PERSON> is proclaimed King of Portugal and the Algarves in Lisbon.", "html": "1707 - <a href=\"https://wikipedia.org/wiki/John_V_of_Portugal\" title=\"John V of Portugal\"><PERSON></a> is proclaimed King of Portugal and the <a href=\"https://wikipedia.org/wiki/Algarves\" class=\"mw-redirect\" title=\"Algarves\">Algarves</a> in Lisbon.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/John_V_of_Portugal\" title=\"John V of Portugal\"><PERSON></a> is proclaimed King of Portugal and the <a href=\"https://wikipedia.org/wiki/Algarves\" class=\"mw-redirect\" title=\"Algarves\">Algarves</a> in Lisbon.", "links": [{"title": "<PERSON> of Portugal", "link": "https://wikipedia.org/wiki/John_V_of_Portugal"}, {"title": "Algarves", "link": "https://wikipedia.org/wiki/Algarves"}]}, {"year": "1725", "text": "<PERSON><PERSON> <PERSON><PERSON> leads the first performance of his chorale cantata <PERSON><PERSON>, nun sei gepreiset, BWV 41, which features the trumpet fanfares from the beginning also in the end.", "html": "1725 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON><PERSON> <PERSON><PERSON></a> leads the first performance of his <a href=\"https://wikipedia.org/wiki/Chorale_cantata_cycle\" title=\"Chorale cantata cycle\">chorale cantata</a> <a href=\"https://wikipedia.org/wiki/Je<PERSON>,_nun_sei_gepreiset,_BWV_41\" title=\"<PERSON><PERSON>, nun sei gepreiset, BWV 41\"><i><PERSON><PERSON>, nun sei gepreiset</i>, BWV 41</a>, which features the trumpet fanfares from the beginning also in the end.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON><PERSON> <PERSON><PERSON></a> leads the first performance of his <a href=\"https://wikipedia.org/wiki/Chorale_cantata_cycle\" title=\"Chorale cantata cycle\">chorale cantata</a> <a href=\"https://wikipedia.org/wiki/Je<PERSON>,_nun_sei_gepreiset,_BWV_41\" title=\"<PERSON><PERSON>, nun sei gepreiset, BWV 41\"><i><PERSON><PERSON>, nun sei gepreiset</i>, BWV 41</a>, which features the trumpet fanfares from the beginning also in the end.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "Chorale cantata cycle", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_cantata_cycle"}, {"title": "<PERSON><PERSON>, nun sei gepreiset, BWV 41", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>,_nun_sei_g<PERSON><PERSON>,_<PERSON>_41"}]}, {"year": "1739", "text": "Bouvet Island, the world's remotest island, is discovered by French explorer <PERSON><PERSON><PERSON>.", "html": "1739 - <a href=\"https://wikipedia.org/wiki/Bouvet_Island\" title=\"Bouvet Island\">Bouvet Island</a>, the world's remotest island, is discovered by French explorer <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Bouvet_Island\" title=\"Bouvet Island\">Bouvet Island</a>, the world's remotest island, is discovered by French explorer <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>.", "links": [{"title": "Bouvet Island", "link": "https://wikipedia.org/wiki/Bouvet_Island"}, {"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1772", "text": "The first traveler's cheques, which could be used in 90 European cities, are issued by the London Credit Exchange Company.", "html": "1772 - The first <a href=\"https://wikipedia.org/wiki/Traveler%27s_cheque\" class=\"mw-redirect\" title=\"Traveler's cheque\">traveler's cheques</a>, which could be used in 90 European cities, are issued by the London Credit Exchange Company.", "no_year_html": "The first <a href=\"https://wikipedia.org/wiki/Traveler%27s_cheque\" class=\"mw-redirect\" title=\"Traveler's cheque\">traveler's cheques</a>, which could be used in 90 European cities, are issued by the London Credit Exchange Company.", "links": [{"title": "Traveler's cheque", "link": "https://wikipedia.org/wiki/Traveler%27s_cheque"}]}, {"year": "1773", "text": "The hymn that becomes known as \"Amazing Grace\", previously titled \"1 Chronicles 17:16-17, <PERSON>'s Review and Expectation\", is first used to accompany a sermon led by <PERSON> in the town of Olney, Buckinghamshire, England.", "html": "1773 - The hymn that becomes known as \"<a href=\"https://wikipedia.org/wiki/Amazing_Grace\" title=\"Amazing Grace\">Amazing Grace</a>\", previously titled \"1 Chronicles 17:16-17, <PERSON>'s Review and Expectation\", is first used to accompany a sermon led by <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"John Newton\"><PERSON></a> in the town of <a href=\"https://wikipedia.org/wiki/Olney,_Buckinghamshire\" title=\"Olney, Buckinghamshire\">Olney, Buckinghamshire</a>, England.", "no_year_html": "The hymn that becomes known as \"<a href=\"https://wikipedia.org/wiki/<PERSON>_Grace\" title=\"Amazing Grace\">Amazing Grace</a>\", previously titled \"1 Chronicles 17:16-17, <PERSON>'s Review and Expectation\", is first used to accompany a sermon led by <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"John Newton\"><PERSON></a> in the town of <a href=\"https://wikipedia.org/wiki/Olney,_Buckinghamshire\" title=\"Olney, Buckinghamshire\">Olney, Buckinghamshire</a>, England.", "links": [{"title": "Amazing Grace", "link": "https://wikipedia.org/wiki/<PERSON>_Grace"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}, {"title": "Olney, Buckinghamshire", "link": "https://wikipedia.org/wiki/Olney,_Buckinghamshire"}]}, {"year": "1776", "text": "American Revolutionary War: Burning of Norfolk - Norfolk, Virginia, is burned to the ground by combined Royal Navy and Continental Army action.", "html": "1776 - <a href=\"https://wikipedia.org/wiki/American_Revolutionary_War\" title=\"American Revolutionary War\">American Revolutionary War</a>: <a href=\"https://wikipedia.org/wiki/Burning_of_Norfolk\" title=\"Burning of Norfolk\">Burning of Norfolk</a> - <a href=\"https://wikipedia.org/wiki/Norfolk,_Virginia\" title=\"Norfolk, Virginia\">Norfolk, Virginia</a>, is burned to the ground by combined <a href=\"https://wikipedia.org/wiki/Royal_Navy\" title=\"Royal Navy\">Royal Navy</a> and <a href=\"https://wikipedia.org/wiki/Continental_Army\" title=\"Continental Army\">Continental Army</a> action.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/American_Revolutionary_War\" title=\"American Revolutionary War\">American Revolutionary War</a>: <a href=\"https://wikipedia.org/wiki/Burning_of_Norfolk\" title=\"Burning of Norfolk\">Burning of Norfolk</a> - <a href=\"https://wikipedia.org/wiki/Norfolk,_Virginia\" title=\"Norfolk, Virginia\">Norfolk, Virginia</a>, is burned to the ground by combined <a href=\"https://wikipedia.org/wiki/Royal_Navy\" title=\"Royal Navy\">Royal Navy</a> and <a href=\"https://wikipedia.org/wiki/Continental_Army\" title=\"Continental Army\">Continental Army</a> action.", "links": [{"title": "American Revolutionary War", "link": "https://wikipedia.org/wiki/American_Revolutionary_War"}, {"title": "Burning of Norfolk", "link": "https://wikipedia.org/wiki/Burning_of_Norfolk"}, {"title": "Norfolk, Virginia", "link": "https://wikipedia.org/wiki/Norfolk,_Virginia"}, {"title": "Royal Navy", "link": "https://wikipedia.org/wiki/Royal_Navy"}, {"title": "Continental Army", "link": "https://wikipedia.org/wiki/Continental_Army"}]}, {"year": "1776", "text": "General <PERSON> hoists the first United States flag, the Grand Union Flag, at Prospect Hill.", "html": "1776 - General <a href=\"https://wikipedia.org/wiki/George_Washington\" title=\"George Washington\"><PERSON></a> hoists the first <a href=\"https://wikipedia.org/wiki/United_States\" title=\"United States\">United States</a> flag, the <a href=\"https://wikipedia.org/wiki/Grand_Union_Flag\" title=\"Grand Union Flag\">Grand Union Flag</a>, at <a href=\"https://wikipedia.org/wiki/Prospect_Hill_Monument\" title=\"Prospect Hill Monument\">Prospect Hill</a>.", "no_year_html": "General <a href=\"https://wikipedia.org/wiki/George_<PERSON>\" title=\"George Washington\"><PERSON></a> hoists the first <a href=\"https://wikipedia.org/wiki/United_States\" title=\"United States\">United States</a> flag, the <a href=\"https://wikipedia.org/wiki/Grand_Union_Flag\" title=\"Grand Union Flag\">Grand Union Flag</a>, at <a href=\"https://wikipedia.org/wiki/Prospect_Hill_Monument\" title=\"Prospect Hill Monument\">Prospect Hill</a>.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/George_Washington"}, {"title": "United States", "link": "https://wikipedia.org/wiki/United_States"}, {"title": "Grand Union Flag", "link": "https://wikipedia.org/wiki/Grand_Union_Flag"}, {"title": "Prospect Hill Monument", "link": "https://wikipedia.org/wiki/Prospect_Hill_Monument"}]}, {"year": "1781", "text": "American Revolutionary War: One thousand five hundred soldiers of the 6th Pennsylvania Regiment under General <PERSON>'s command rebel against the Continental Army's winter camp in Morristown, New Jersey in the Pennsylvania Line Mutiny of 1781.", "html": "1781 - American Revolutionary War: One thousand five hundred soldiers of the <a href=\"https://wikipedia.org/wiki/6th_Pennsylvania_Regiment\" title=\"6th Pennsylvania Regiment\">6th Pennsylvania Regiment</a> under General <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>'s command rebel against the <a href=\"https://wikipedia.org/wiki/Continental_Army\" title=\"Continental Army\">Continental Army</a>'s winter camp in <a href=\"https://wikipedia.org/wiki/Morristown,_New_Jersey\" title=\"Morristown, New Jersey\">Morristown, New Jersey</a> in the <a href=\"https://wikipedia.org/wiki/Pennsylvania_Line_Mutiny\" title=\"Pennsylvania Line Mutiny\">Pennsylvania Line Mutiny</a> of 1781.", "no_year_html": "American Revolutionary War: One thousand five hundred soldiers of the <a href=\"https://wikipedia.org/wiki/6th_Pennsylvania_Regiment\" title=\"6th Pennsylvania Regiment\">6th Pennsylvania Regiment</a> under General <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>'s command rebel against the <a href=\"https://wikipedia.org/wiki/Continental_Army\" title=\"Continental Army\">Continental Army</a>'s winter camp in <a href=\"https://wikipedia.org/wiki/Morristown,_New_Jersey\" title=\"Morristown, New Jersey\">Morristown, New Jersey</a> in the <a href=\"https://wikipedia.org/wiki/Pennsylvania_Line_Mutiny\" title=\"Pennsylvania Line Mutiny\">Pennsylvania Line Mutiny</a> of 1781.", "links": [{"title": "6th Pennsylvania Regiment", "link": "https://wikipedia.org/wiki/6th_Pennsylvania_Regiment"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Continental Army", "link": "https://wikipedia.org/wiki/Continental_Army"}, {"title": "Morristown, New Jersey", "link": "https://wikipedia.org/wiki/Morristown,_New_Jersey"}, {"title": "Pennsylvania Line Mutiny", "link": "https://wikipedia.org/wiki/Pennsylvania_Line_Mutiny"}]}, {"year": "1788", "text": "The first edition of The Times of London, previously The Daily Universal Register, is published.", "html": "1788 - The first edition of <i><a href=\"https://wikipedia.org/wiki/The_Times\" title=\"The Times\">The Times</a></i> of London, previously <i>The Daily Universal Register</i>, is published.", "no_year_html": "The first edition of <i><a href=\"https://wikipedia.org/wiki/The_Times\" title=\"The Times\">The Times</a></i> of London, previously <i>The Daily Universal Register</i>, is published.", "links": [{"title": "The Times", "link": "https://wikipedia.org/wiki/The_Times"}]}, {"year": "1801", "text": "The legislative union of Kingdom of Great Britain and Kingdom of Ireland is completed, and the United Kingdom of Great Britain and Ireland is proclaimed.", "html": "1801 - The legislative union of <a href=\"https://wikipedia.org/wiki/Kingdom_of_Great_Britain\" title=\"Kingdom of Great Britain\">Kingdom of Great Britain</a> and <a href=\"https://wikipedia.org/wiki/Kingdom_of_Ireland\" title=\"Kingdom of Ireland\">Kingdom of Ireland</a> is completed, and the <a href=\"https://wikipedia.org/wiki/United_Kingdom_of_Great_Britain_and_Ireland\" title=\"United Kingdom of Great Britain and Ireland\">United Kingdom of Great Britain and Ireland</a> is proclaimed.", "no_year_html": "The legislative union of <a href=\"https://wikipedia.org/wiki/Kingdom_of_Great_Britain\" title=\"Kingdom of Great Britain\">Kingdom of Great Britain</a> and <a href=\"https://wikipedia.org/wiki/Kingdom_of_Ireland\" title=\"Kingdom of Ireland\">Kingdom of Ireland</a> is completed, and the <a href=\"https://wikipedia.org/wiki/United_Kingdom_of_Great_Britain_and_Ireland\" title=\"United Kingdom of Great Britain and Ireland\">United Kingdom of Great Britain and Ireland</a> is proclaimed.", "links": [{"title": "Kingdom of Great Britain", "link": "https://wikipedia.org/wiki/Kingdom_of_Great_Britain"}, {"title": "Kingdom of Ireland", "link": "https://wikipedia.org/wiki/Kingdom_of_Ireland"}, {"title": "United Kingdom of Great Britain and Ireland", "link": "https://wikipedia.org/wiki/United_Kingdom_of_Great_Britain_and_Ireland"}]}, {"year": "1801", "text": "<PERSON><PERSON>, the largest and first known object in the Asteroid belt, is discovered by <PERSON>.", "html": "1801 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_(dwarf_planet)\" title=\"Ceres (dwarf planet)\"><PERSON><PERSON></a>, the largest and first known object in the <a href=\"https://wikipedia.org/wiki/Asteroid_belt\" title=\"Asteroid belt\">Asteroid belt</a>, is discovered by <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_(dwarf_planet)\" title=\"Ceres (dwarf planet)\"><PERSON><PERSON></a>, the largest and first known object in the <a href=\"https://wikipedia.org/wiki/Asteroid_belt\" title=\"Asteroid belt\">Asteroid belt</a>, is discovered by <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>.", "links": [{"title": "<PERSON><PERSON> (dwarf planet)", "link": "https://wikipedia.org/wiki/Ceres_(dwarf_planet)"}, {"title": "Asteroid belt", "link": "https://wikipedia.org/wiki/Asteroid_belt"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1804", "text": "French rule ends in Haiti. Haiti becomes the first black-majority republic and second independent country in North America after the United States.", "html": "1804 - <a href=\"https://wikipedia.org/wiki/Saint-Domingue\" title=\"Saint-Domingue\">French rule</a> ends in <a href=\"https://wikipedia.org/wiki/Haiti\" title=\"Haiti\">Haiti</a>. Haiti becomes the first black-majority republic and second independent country in <a href=\"https://wikipedia.org/wiki/North_America\" title=\"North America\">North America</a> after the <a href=\"https://wikipedia.org/wiki/United_States\" title=\"United States\">United States</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Saint-Domingue\" title=\"Saint-Domingue\">French rule</a> ends in <a href=\"https://wikipedia.org/wiki/Haiti\" title=\"Haiti\">Haiti</a>. Haiti becomes the first black-majority republic and second independent country in <a href=\"https://wikipedia.org/wiki/North_America\" title=\"North America\">North America</a> after the <a href=\"https://wikipedia.org/wiki/United_States\" title=\"United States\">United States</a>.", "links": [{"title": "Saint-Domingue", "link": "https://wikipedia.org/wiki/Saint-Domingue"}, {"title": "Haiti", "link": "https://wikipedia.org/wiki/Haiti"}, {"title": "North America", "link": "https://wikipedia.org/wiki/North_America"}, {"title": "United States", "link": "https://wikipedia.org/wiki/United_States"}]}, {"year": "1806", "text": "The French Republican Calendar is abolished.", "html": "1806 - The <a href=\"https://wikipedia.org/wiki/French_Republican_Calendar\" class=\"mw-redirect\" title=\"French Republican Calendar\">French Republican Calendar</a> is abolished.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/French_Republican_Calendar\" class=\"mw-redirect\" title=\"French Republican Calendar\">French Republican Calendar</a> is abolished.", "links": [{"title": "French Republican Calendar", "link": "https://wikipedia.org/wiki/French_Republican_Calendar"}]}, {"year": "1808", "text": "The United States bans the importation of slaves.", "html": "1808 - The United States <a href=\"https://wikipedia.org/wiki/Act_Prohibiting_Importation_of_Slaves\" title=\"Act Prohibiting Importation of Slaves\">bans the importation of slaves</a>.", "no_year_html": "The United States <a href=\"https://wikipedia.org/wiki/Act_Prohibiting_Importation_of_Slaves\" title=\"Act Prohibiting Importation of Slaves\">bans the importation of slaves</a>.", "links": [{"title": "Act Prohibiting Importation of Slaves", "link": "https://wikipedia.org/wiki/Act_Prohibiting_Importation_of_Slaves"}]}, {"year": "1810", "text": "Major-General <PERSON><PERSON><PERSON> officially becomes Governor of New South Wales.", "html": "1810 - Major-General <a href=\"https://wikipedia.org/wiki/Lachlan_Macquarie\" title=\"Lachlan Macquarie\"><PERSON><PERSON>an Macquarie</a> officially becomes <a href=\"https://wikipedia.org/wiki/Governor_of_New_South_Wales\" title=\"Governor of New South Wales\">Governor of New South Wales</a>.", "no_year_html": "Major-General <a href=\"https://wikipedia.org/wiki/Lachlan_Macquarie\" title=\"Lachlan Macquarie\">Lachlan Macquarie</a> officially becomes <a href=\"https://wikipedia.org/wiki/Governor_of_New_South_Wales\" title=\"Governor of New South Wales\">Governor of New South Wales</a>.", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>an_<PERSON>"}, {"title": "Governor of New South Wales", "link": "https://wikipedia.org/wiki/Governor_of_New_South_Wales"}]}, {"year": "1818", "text": "<PERSON> (anonymously) publishes the pioneering work of science fiction, Frankenstein; or, The Modern Prometheus, in London.", "html": "1818 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> (anonymously) publishes the pioneering work of science fiction, <i><a href=\"https://wikipedia.org/wiki/Frankenstein\" title=\"<PERSON>\"><PERSON>; or, The Modern Prometheus</a></i>, in London.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> (anonymously) publishes the pioneering work of science fiction, <i><a href=\"https://wikipedia.org/wiki/Frankenstein\" title=\"<PERSON>\"><PERSON>; or, The Modern Prometheus</a></i>, in London.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Frankenstein", "link": "https://wikipedia.org/wiki/Frankenstein"}]}, {"year": "1822", "text": "The Greek Constitution of 1822 is adopted by the First National Assembly at Epidaurus.", "html": "1822 - The <a href=\"https://wikipedia.org/wiki/Greek_Constitution_of_1822\" title=\"Greek Constitution of 1822\">Greek Constitution of 1822</a> is adopted by the <a href=\"https://wikipedia.org/wiki/First_National_Assembly_at_Epidaurus\" title=\"First National Assembly at Epidaurus\">First National Assembly at Epidaurus</a>.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Greek_Constitution_of_1822\" title=\"Greek Constitution of 1822\">Greek Constitution of 1822</a> is adopted by the <a href=\"https://wikipedia.org/wiki/First_National_Assembly_at_Epidaurus\" title=\"First National Assembly at Epidaurus\">First National Assembly at Epidaurus</a>.", "links": [{"title": "Greek Constitution of 1822", "link": "https://wikipedia.org/wiki/Greek_Constitution_of_1822"}, {"title": "First National Assembly at Epidaurus", "link": "https://wikipedia.org/wiki/First_National_Assembly_at_Epidaurus"}]}, {"year": "1834", "text": "Most of Germany forms the Zollverein customs union, the first such union between sovereign states.", "html": "1834 - Most of Germany forms the <i><a href=\"https://wikipedia.org/wiki/Zollverein\" title=\"Zollverein\">Zollverein</a></i> customs union, the first such union between sovereign states.", "no_year_html": "Most of Germany forms the <i><a href=\"https://wikipedia.org/wiki/Zollverein\" title=\"Zollverein\">Zollverein</a></i> customs union, the first such union between sovereign states.", "links": [{"title": "Zollverein", "link": "https://wikipedia.org/wiki/Zollverein"}]}, {"year": "1845", "text": "The Philippines moves its national calendar to align with other Asian countries' calendars by skipping Tuesday, December 31, 1844. The change has been ordered by Governor-General <PERSON><PERSON><PERSON><PERSON> to reform the country's calendar so that it aligns with the rest of Asia. Its territory has been one day behind the rest of Asia for 323 years since the arrival of <PERSON> in the Philippines on March 16, 1521.", "html": "1845 - The Philippines moves its national calendar to align with other Asian countries' calendars by skipping Tuesday, December 31, 1844. The change has been ordered by Governor-General <a href=\"https://wikipedia.org/wiki/Narciso_Claver%C3%ADa_y_Zald%C3%BAa\" title=\"Narciso Clavería y Zaldúa\">Narciso Claveria</a> to reform the country's calendar so that it aligns with the rest of Asia. Its territory has been one day behind the rest of Asia for 323 years since the arrival of <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> in the Philippines on March 16, 1521.", "no_year_html": "The Philippines moves its national calendar to align with other Asian countries' calendars by skipping Tuesday, December 31, 1844. The change has been ordered by Governor-General <a href=\"https://wikipedia.org/wiki/Narciso_Claver%C3%ADa_y_Zald%C3%BAa\" title=\"Narciso Clavería y Zaldúa\">Narciso Claveria</a> to reform the country's calendar so that it aligns with the rest of Asia. Its territory has been one day behind the rest of Asia for 323 years since the arrival of <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> in the Philippines on March 16, 1521.", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON> y Zaldúa", "link": "https://wikipedia.org/wiki/Narciso_Claver%C3%ADa_y_Zald%C3%BAa"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1847", "text": "The world's first \"Mercy\" Hospital is founded in Pittsburgh, United States, by a group of Sisters of Mercy from Ireland; the name will go on to grace over 30 major hospitals throughout the world.", "html": "1847 - The world's first <a href=\"https://wikipedia.org/wiki/UPMC_Mercy\" title=\"UPMC Mercy\">\"Mercy\" Hospital</a> is founded in <a href=\"https://wikipedia.org/wiki/Pittsburgh\" title=\"Pittsburgh\">Pittsburgh</a>, United States, by a group of <a href=\"https://wikipedia.org/wiki/Sisters_of_Mercy\" title=\"Sisters of Mercy\">Sisters of Mercy</a> from Ireland; the name will go on to grace over 30 major hospitals throughout the world.", "no_year_html": "The world's first <a href=\"https://wikipedia.org/wiki/UPMC_Mercy\" title=\"UPMC Mercy\">\"Mercy\" Hospital</a> is founded in <a href=\"https://wikipedia.org/wiki/Pittsburgh\" title=\"Pittsburgh\">Pittsburgh</a>, United States, by a group of <a href=\"https://wikipedia.org/wiki/Sisters_of_Mercy\" title=\"Sisters of Mercy\">Sisters of Mercy</a> from Ireland; the name will go on to grace over 30 major hospitals throughout the world.", "links": [{"title": "UPMC Mercy", "link": "https://wikipedia.org/wiki/UPMC_Mercy"}, {"title": "Pittsburgh", "link": "https://wikipedia.org/wiki/Pittsburgh"}, {"title": "Sisters of Mercy", "link": "https://wikipedia.org/wiki/<PERSON>_of_Mercy"}]}, {"year": "1860", "text": "The first Polish postage stamp is issued, replacing the Russian stamps previously in use.", "html": "1860 - <a href=\"https://wikipedia.org/wiki/Postage_stamps_and_postal_history_of_Poland#First_Polish_stamp\" title=\"Postage stamps and postal history of Poland\">The first Polish postage stamp</a> is issued, replacing the <a href=\"https://wikipedia.org/wiki/Postage_stamps_and_postal_history_of_Russia\" title=\"Postage stamps and postal history of Russia\">Russian stamps</a> previously in use.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Postage_stamps_and_postal_history_of_Poland#First_Polish_stamp\" title=\"Postage stamps and postal history of Poland\">The first Polish postage stamp</a> is issued, replacing the <a href=\"https://wikipedia.org/wiki/Postage_stamps_and_postal_history_of_Russia\" title=\"Postage stamps and postal history of Russia\">Russian stamps</a> previously in use.", "links": [{"title": "Postage stamps and postal history of Poland", "link": "https://wikipedia.org/wiki/Postage_stamps_and_postal_history_of_Poland#First_Polish_stamp"}, {"title": "Postage stamps and postal history of Russia", "link": "https://wikipedia.org/wiki/Postage_stamps_and_postal_history_of_Russia"}]}, {"year": "1861", "text": "Liberal forces supporting <PERSON> enter Mexico City.", "html": "1861 - <a href=\"https://wikipedia.org/wiki/Liberal_Party_(Mexico)\" title=\"Liberal Party (Mexico)\">Liberal</a> forces supporting <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A1rez\" title=\"<PERSON>\"><PERSON></a> enter <a href=\"https://wikipedia.org/wiki/Mexico_City\" title=\"Mexico City\">Mexico City</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Liberal_Party_(Mexico)\" title=\"Liberal Party (Mexico)\">Liberal</a> forces supporting <a href=\"https://wikipedia.org/wiki/Benito_<PERSON>%C3%A1rez\" title=\"<PERSON>\"><PERSON></a> enter <a href=\"https://wikipedia.org/wiki/Mexico_City\" title=\"Mexico City\">Mexico City</a>.", "links": [{"title": "Liberal Party (Mexico)", "link": "https://wikipedia.org/wiki/Liberal_Party_(Mexico)"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Benito_Ju%C3%A1rez"}, {"title": "Mexico City", "link": "https://wikipedia.org/wiki/Mexico_City"}]}, {"year": "1863", "text": "American Civil War: The Emancipation Proclamation takes effect in Confederate territory.", "html": "1863 - <a href=\"https://wikipedia.org/wiki/American_Civil_War\" title=\"American Civil War\">American Civil War</a>: The <a href=\"https://wikipedia.org/wiki/Emancipation_Proclamation\" title=\"Emancipation Proclamation\">Emancipation Proclamation</a> takes effect in <a href=\"https://wikipedia.org/wiki/Confederate_States_of_America\" title=\"Confederate States of America\">Confederate</a> territory.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/American_Civil_War\" title=\"American Civil War\">American Civil War</a>: The <a href=\"https://wikipedia.org/wiki/Emancipation_Proclamation\" title=\"Emancipation Proclamation\">Emancipation Proclamation</a> takes effect in <a href=\"https://wikipedia.org/wiki/Confederate_States_of_America\" title=\"Confederate States of America\">Confederate</a> territory.", "links": [{"title": "American Civil War", "link": "https://wikipedia.org/wiki/American_Civil_War"}, {"title": "Emancipation Proclamation", "link": "https://wikipedia.org/wiki/Emancipation_Proclamation"}, {"title": "Confederate States of America", "link": "https://wikipedia.org/wiki/Confederate_States_of_America"}]}, {"year": "1877", "text": "Queen <PERSON> of the United Kingdom is proclaimed Empress of India.", "html": "1877 - <a href=\"https://wikipedia.org/wiki/Queen_<PERSON>\" title=\"Queen <PERSON>\">Queen <PERSON></a> of the United Kingdom is proclaimed <a href=\"https://wikipedia.org/wiki/Empress_of_India\" class=\"mw-redirect\" title=\"Empress of India\">Empress of India</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Queen_<PERSON>\" title=\"Queen <PERSON>\">Queen <PERSON></a> of the United Kingdom is proclaimed <a href=\"https://wikipedia.org/wiki/Empress_of_India\" class=\"mw-redirect\" title=\"Empress of India\">Empress of India</a>.", "links": [{"title": "Queen <PERSON>", "link": "https://wikipedia.org/wiki/Queen_<PERSON>"}, {"title": "Empress of India", "link": "https://wikipedia.org/wiki/Empress_of_India"}]}, {"year": "1885", "text": "Twenty-five nations adopt <PERSON><PERSON>'s proposal for standard time (and also, time zones).", "html": "1885 - Twenty-five nations adopt <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_Fleming\" title=\"<PERSON><PERSON> Fleming\"><PERSON><PERSON> Fleming</a>'s proposal for <a href=\"https://wikipedia.org/wiki/Standard_time\" title=\"Standard time\">standard time</a> (and also, <a href=\"https://wikipedia.org/wiki/Time_zone\" title=\"Time zone\">time zones</a>).", "no_year_html": "Twenty-five nations adopt <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_Fleming\" title=\"<PERSON><PERSON> Fleming\"><PERSON><PERSON> Fleming</a>'s proposal for <a href=\"https://wikipedia.org/wiki/Standard_time\" title=\"Standard time\">standard time</a> (and also, <a href=\"https://wikipedia.org/wiki/Time_zone\" title=\"Time zone\">time zones</a>).", "links": [{"title": "Sandford Fleming", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_Fleming"}, {"title": "Standard time", "link": "https://wikipedia.org/wiki/Standard_time"}, {"title": "Time zone", "link": "https://wikipedia.org/wiki/Time_zone"}]}, {"year": "1890", "text": "Eritrea is consolidated into a colony by the Italian government.", "html": "1890 - <a href=\"https://wikipedia.org/wiki/Eritrea\" title=\"Eritrea\">Eritrea</a> is consolidated into a <a href=\"https://wikipedia.org/wiki/Colony\" title=\"Colony\">colony</a> by the <a href=\"https://wikipedia.org/wiki/Government_of_Italy\" title=\"Government of Italy\">Italian government</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Eritrea\" title=\"Eritrea\">Eritrea</a> is consolidated into a <a href=\"https://wikipedia.org/wiki/Colony\" title=\"Colony\">colony</a> by the <a href=\"https://wikipedia.org/wiki/Government_of_Italy\" title=\"Government of Italy\">Italian government</a>.", "links": [{"title": "Eritrea", "link": "https://wikipedia.org/wiki/Eritrea"}, {"title": "Colony", "link": "https://wikipedia.org/wiki/Colony"}, {"title": "Government of Italy", "link": "https://wikipedia.org/wiki/Government_of_Italy"}]}, {"year": "1892", "text": "Ellis Island begins processing immigrants into the United States.", "html": "1892 - <a href=\"https://wikipedia.org/wiki/Ellis_Island\" title=\"Ellis Island\">Ellis Island</a> begins processing <a href=\"https://wikipedia.org/wiki/Immigration_to_the_United_States\" title=\"Immigration to the United States\">immigrants into the United States</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Ellis_Island\" title=\"Ellis Island\">Ellis Island</a> begins processing <a href=\"https://wikipedia.org/wiki/Immigration_to_the_United_States\" title=\"Immigration to the United States\">immigrants into the United States</a>.", "links": [{"title": "Ellis Island", "link": "https://wikipedia.org/wiki/Ellis_Island"}, {"title": "Immigration to the United States", "link": "https://wikipedia.org/wiki/Immigration_to_the_United_States"}]}, {"year": "1898", "text": "New York, New York annexes land from surrounding counties, creating the City of Greater New York. The four initial boroughs, Manhattan, Brooklyn, Queens, and The Bronx, are joined on January 25 by Staten Island to create the modern city of five boroughs.", "html": "1898 - <a href=\"https://wikipedia.org/wiki/New_York_City\" title=\"New York City\">New York, New York</a> annexes land from surrounding counties, creating the <a href=\"https://wikipedia.org/wiki/City_of_Greater_New_York\" title=\"City of Greater New York\">City of Greater New York</a>. The four initial <a href=\"https://wikipedia.org/wiki/Boroughs_of_New_York_City\" title=\"Boroughs of New York City\">boroughs</a>, <a href=\"https://wikipedia.org/wiki/Manhattan\" title=\"Manhattan\">Manhattan</a>, <a href=\"https://wikipedia.org/wiki/Brooklyn\" title=\"Brooklyn\">Brooklyn</a>, <a href=\"https://wikipedia.org/wiki/Queens\" title=\"Queens\">Queens</a>, and <a href=\"https://wikipedia.org/wiki/The_Bronx\" title=\"The Bronx\">The Bronx</a>, are joined on January 25 by <a href=\"https://wikipedia.org/wiki/Staten_Island\" title=\"Staten Island\">Staten Island</a> to create the modern city of five boroughs.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/New_York_City\" title=\"New York City\">New York, New York</a> annexes land from surrounding counties, creating the <a href=\"https://wikipedia.org/wiki/City_of_Greater_New_York\" title=\"City of Greater New York\">City of Greater New York</a>. The four initial <a href=\"https://wikipedia.org/wiki/Boroughs_of_New_York_City\" title=\"Boroughs of New York City\">boroughs</a>, <a href=\"https://wikipedia.org/wiki/Manhattan\" title=\"Manhattan\">Manhattan</a>, <a href=\"https://wikipedia.org/wiki/Brooklyn\" title=\"Brooklyn\">Brooklyn</a>, <a href=\"https://wikipedia.org/wiki/Queens\" title=\"Queens\">Queens</a>, and <a href=\"https://wikipedia.org/wiki/The_Bronx\" title=\"The Bronx\">The Bronx</a>, are joined on January 25 by <a href=\"https://wikipedia.org/wiki/Staten_Island\" title=\"Staten Island\">Staten Island</a> to create the modern city of five boroughs.", "links": [{"title": "New York City", "link": "https://wikipedia.org/wiki/New_York_City"}, {"title": "City of Greater New York", "link": "https://wikipedia.org/wiki/City_of_Greater_New_York"}, {"title": "Boroughs of New York City", "link": "https://wikipedia.org/wiki/Boroughs_of_New_York_City"}, {"title": "Manhattan", "link": "https://wikipedia.org/wiki/Manhattan"}, {"title": "Brooklyn", "link": "https://wikipedia.org/wiki/Brooklyn"}, {"title": "Queens", "link": "https://wikipedia.org/wiki/Queens"}, {"title": "The Bronx", "link": "https://wikipedia.org/wiki/The_Bronx"}, {"title": "Staten Island", "link": "https://wikipedia.org/wiki/Staten_Island"}]}, {"year": "1899", "text": "Spanish rule ends in Cuba.", "html": "1899 - <a href=\"https://wikipedia.org/wiki/Captaincy_General_of_Cuba\" title=\"Captaincy General of Cuba\">Spanish rule</a> ends in <a href=\"https://wikipedia.org/wiki/Cuba\" title=\"Cuba\">Cuba</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Captaincy_General_of_Cuba\" title=\"Captaincy General of Cuba\">Spanish rule</a> ends in <a href=\"https://wikipedia.org/wiki/Cuba\" title=\"Cuba\">Cuba</a>.", "links": [{"title": "Captaincy General of Cuba", "link": "https://wikipedia.org/wiki/Captaincy_General_of_Cuba"}, {"title": "Cuba", "link": "https://wikipedia.org/wiki/Cuba"}]}, {"year": "1900", "text": "Nigeria becomes a British protectorate with <PERSON> as high commissioner.", "html": "1900 - <a href=\"https://wikipedia.org/wiki/Nigeria\" title=\"Nigeria\">Nigeria</a> becomes a British protectorate with <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a> as high commissioner.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Nigeria\" title=\"Nigeria\">Nigeria</a> becomes a British protectorate with <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a> as high commissioner.", "links": [{"title": "Nigeria", "link": "https://wikipedia.org/wiki/Nigeria"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1901", "text": "The Southern Nigeria Protectorate is established within the British Empire.", "html": "1901 - The <a href=\"https://wikipedia.org/wiki/Southern_Nigeria_Protectorate\" title=\"Southern Nigeria Protectorate\">Southern Nigeria Protectorate</a> is established within the British Empire.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Southern_Nigeria_Protectorate\" title=\"Southern Nigeria Protectorate\">Southern Nigeria Protectorate</a> is established within the British Empire.", "links": [{"title": "Southern Nigeria Protectorate", "link": "https://wikipedia.org/wiki/Southern_Nigeria_Protectorate"}]}, {"year": "1901", "text": "The British colonies of New South Wales, Queensland, Victoria, South Australia, Tasmania, and Western Australia federate as the Commonwealth of Australia; <PERSON> is appointed the first Prime Minister.", "html": "1901 - The British colonies of <a href=\"https://wikipedia.org/wiki/New_South_Wales\" title=\"New South Wales\">New South Wales</a>, <a href=\"https://wikipedia.org/wiki/Queensland\" title=\"Queensland\">Queensland</a>, <a href=\"https://wikipedia.org/wiki/Victoria_(Australia)\" class=\"mw-redirect\" title=\"Victoria (Australia)\">Victoria</a>, <a href=\"https://wikipedia.org/wiki/South_Australia\" title=\"South Australia\">South Australia</a>, <a href=\"https://wikipedia.org/wiki/Tasmania\" title=\"Tasmania\">Tasmania</a>, and <a href=\"https://wikipedia.org/wiki/Western_Australia\" title=\"Western Australia\">Western Australia</a> <a href=\"https://wikipedia.org/wiki/Federation_of_Australia\" title=\"Federation of Australia\">federate</a> as the <a href=\"https://wikipedia.org/wiki/Australia\" title=\"Australia\">Commonwealth of Australia</a>; <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> is appointed the first <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Australia\" title=\"Prime Minister of Australia\">Prime Minister</a>.", "no_year_html": "The British colonies of <a href=\"https://wikipedia.org/wiki/New_South_Wales\" title=\"New South Wales\">New South Wales</a>, <a href=\"https://wikipedia.org/wiki/Queensland\" title=\"Queensland\">Queensland</a>, <a href=\"https://wikipedia.org/wiki/Victoria_(Australia)\" class=\"mw-redirect\" title=\"Victoria (Australia)\">Victoria</a>, <a href=\"https://wikipedia.org/wiki/South_Australia\" title=\"South Australia\">South Australia</a>, <a href=\"https://wikipedia.org/wiki/Tasmania\" title=\"Tasmania\">Tasmania</a>, and <a href=\"https://wikipedia.org/wiki/Western_Australia\" title=\"Western Australia\">Western Australia</a> <a href=\"https://wikipedia.org/wiki/Federation_of_Australia\" title=\"Federation of Australia\">federate</a> as the <a href=\"https://wikipedia.org/wiki/Australia\" title=\"Australia\">Commonwealth of Australia</a>; <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> is appointed the first <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Australia\" title=\"Prime Minister of Australia\">Prime Minister</a>.", "links": [{"title": "New South Wales", "link": "https://wikipedia.org/wiki/New_South_Wales"}, {"title": "Queensland", "link": "https://wikipedia.org/wiki/Queensland"}, {"title": "Victoria (Australia)", "link": "https://wikipedia.org/wiki/Victoria_(Australia)"}, {"title": "South Australia", "link": "https://wikipedia.org/wiki/South_Australia"}, {"title": "Tasmania", "link": "https://wikipedia.org/wiki/Tasmania"}, {"title": "Western Australia", "link": "https://wikipedia.org/wiki/Western_Australia"}, {"title": "Federation of Australia", "link": "https://wikipedia.org/wiki/Federation_of_Australia"}, {"title": "Australia", "link": "https://wikipedia.org/wiki/Australia"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Prime Minister of Australia", "link": "https://wikipedia.org/wiki/Prime_Minister_of_Australia"}]}, {"year": "1902", "text": "The first American college football bowl game, the Rose Bowl between Michigan and Stanford, is held in Pasadena, California.", "html": "1902 - The first American <a href=\"https://wikipedia.org/wiki/College_football\" title=\"College football\">college football</a> <a href=\"https://wikipedia.org/wiki/Bowl_game\" title=\"Bowl game\">bowl game</a>, the <a href=\"https://wikipedia.org/wiki/Rose_Bowl_Game\" title=\"Rose Bowl Game\">Rose Bowl</a> between <a href=\"https://wikipedia.org/wiki/University_of_Michigan\" title=\"University of Michigan\">Michigan</a> and <a href=\"https://wikipedia.org/wiki/Stanford_University\" title=\"Stanford University\">Stanford</a>, is held in <a href=\"https://wikipedia.org/wiki/Pasadena,_California\" title=\"Pasadena, California\">Pasadena, California</a>.", "no_year_html": "The first American <a href=\"https://wikipedia.org/wiki/College_football\" title=\"College football\">college football</a> <a href=\"https://wikipedia.org/wiki/Bowl_game\" title=\"Bowl game\">bowl game</a>, the <a href=\"https://wikipedia.org/wiki/Rose_Bowl_Game\" title=\"Rose Bowl Game\">Rose Bowl</a> between <a href=\"https://wikipedia.org/wiki/University_of_Michigan\" title=\"University of Michigan\">Michigan</a> and <a href=\"https://wikipedia.org/wiki/Stanford_University\" title=\"Stanford University\">Stanford</a>, is held in <a href=\"https://wikipedia.org/wiki/Pasadena,_California\" title=\"Pasadena, California\">Pasadena, California</a>.", "links": [{"title": "College football", "link": "https://wikipedia.org/wiki/College_football"}, {"title": "Bowl game", "link": "https://wikipedia.org/wiki/Bowl_game"}, {"title": "Rose Bowl Game", "link": "https://wikipedia.org/wiki/Rose_Bowl_Game"}, {"title": "University of Michigan", "link": "https://wikipedia.org/wiki/University_of_Michigan"}, {"title": "Stanford University", "link": "https://wikipedia.org/wiki/Stanford_University"}, {"title": "Pasadena, California", "link": "https://wikipedia.org/wiki/Pasadena,_California"}]}, {"year": "1910", "text": "Captain <PERSON> is promoted to rear admiral, and becomes the youngest admiral in the Royal Navy (except for royal family members) since <PERSON><PERSON><PERSON>.", "html": "1910 - Captain <a href=\"https://wikipedia.org/wiki/<PERSON>,_1st_Earl_<PERSON>\" title=\"<PERSON>, 1st <PERSON>\"><PERSON></a> is promoted to <a href=\"https://wikipedia.org/wiki/Rear_admiral\" title=\"Rear admiral\">rear admiral</a>, and becomes the youngest admiral in the Royal Navy (except for <a href=\"https://wikipedia.org/wiki/British_royal_family\" title=\"British royal family\">royal family</a> members) since <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>,_1st_Viscount_<PERSON>\" title=\"<PERSON><PERSON><PERSON>, 1st Viscount <PERSON>\"><PERSON><PERSON><PERSON></a>.", "no_year_html": "Captain <a href=\"https://wikipedia.org/wiki/<PERSON>,_1st_<PERSON>_<PERSON>\" title=\"<PERSON>, 1st <PERSON>\"><PERSON></a> is promoted to <a href=\"https://wikipedia.org/wiki/Rear_admiral\" title=\"Rear admiral\">rear admiral</a>, and becomes the youngest admiral in the Royal Navy (except for <a href=\"https://wikipedia.org/wiki/British_royal_family\" title=\"British royal family\">royal family</a> members) since <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>,_1st_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>, 1st Viscount <PERSON>\"><PERSON><PERSON><PERSON></a>.", "links": [{"title": "<PERSON>, 1st <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>,_1st_<PERSON>_<PERSON>"}, {"title": "Rear admiral", "link": "https://wikipedia.org/wiki/Rear_admiral"}, {"title": "British royal family", "link": "https://wikipedia.org/wiki/British_royal_family"}, {"title": "<PERSON><PERSON><PERSON>, 1st Viscount <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>rat<PERSON>_<PERSON>,_1st_Viscount_<PERSON>"}]}, {"year": "1912", "text": "The Republic of China is established.", "html": "1912 - The <a href=\"https://wikipedia.org/wiki/Republic_of_China_(1912%E2%80%9349)\" class=\"mw-redirect\" title=\"Republic of China (1912-49)\">Republic of China</a> is established.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Republic_of_China_(1912%E2%80%9349)\" class=\"mw-redirect\" title=\"Republic of China (1912-49)\">Republic of China</a> is established.", "links": [{"title": "Republic of China (1912-49)", "link": "https://wikipedia.org/wiki/Republic_of_China_(1912%E2%80%9349)"}]}, {"year": "1914", "text": "The SPT Airboat Line becomes the world's first scheduled airline to use a winged aircraft.", "html": "1914 - The <a href=\"https://wikipedia.org/wiki/St._Petersburg%E2%80%93Tampa_Airboat_Line\" title=\"St. Petersburg-Tampa Airboat Line\">SPT Airboat Line</a> becomes the world's first scheduled airline to use a <a href=\"https://wikipedia.org/wiki/Fixed-wing_aircraft\" title=\"Fixed-wing aircraft\">winged aircraft</a>.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/St._Petersburg%E2%80%93Tampa_Airboat_Line\" title=\"St. Petersburg-Tampa Airboat Line\">SPT Airboat Line</a> becomes the world's first scheduled airline to use a <a href=\"https://wikipedia.org/wiki/Fixed-wing_aircraft\" title=\"Fixed-wing aircraft\">winged aircraft</a>.", "links": [{"title": "St. Petersburg-Tampa Airboat Line", "link": "https://wikipedia.org/wiki/St._Petersburg%E2%80%93Tampa_Airboat_Line"}, {"title": "Fixed-wing aircraft", "link": "https://wikipedia.org/wiki/Fixed-wing_aircraft"}]}, {"year": "1923", "text": "Britain's Railways are grouped into the Big Four: LNER, GWR, SR, and LMS.", "html": "1923 - Britain's Railways are grouped into the <a href=\"https://wikipedia.org/wiki/List_of_railway_companies_involved_in_the_1923_grouping#The_Big_Four\" title=\"List of railway companies involved in the 1923 grouping\">Big Four</a>: <a href=\"https://wikipedia.org/wiki/London_and_North_Eastern_Railway\" title=\"London and North Eastern Railway\">LNER</a>, <a href=\"https://wikipedia.org/wiki/Great_Western_Railway\" title=\"Great Western Railway\">GWR</a>, <a href=\"https://wikipedia.org/wiki/Southern_Railway_(UK)\" title=\"Southern Railway (UK)\">SR</a>, and <a href=\"https://wikipedia.org/wiki/London,_Midland_and_Scottish_Railway\" title=\"London, Midland and Scottish Railway\">LMS</a>.", "no_year_html": "Britain's Railways are grouped into the <a href=\"https://wikipedia.org/wiki/List_of_railway_companies_involved_in_the_1923_grouping#The_Big_Four\" title=\"List of railway companies involved in the 1923 grouping\">Big Four</a>: <a href=\"https://wikipedia.org/wiki/London_and_North_Eastern_Railway\" title=\"London and North Eastern Railway\">LNER</a>, <a href=\"https://wikipedia.org/wiki/Great_Western_Railway\" title=\"Great Western Railway\">GWR</a>, <a href=\"https://wikipedia.org/wiki/Southern_Railway_(UK)\" title=\"Southern Railway (UK)\">SR</a>, and <a href=\"https://wikipedia.org/wiki/London,_Midland_and_Scottish_Railway\" title=\"London, Midland and Scottish Railway\">LMS</a>.", "links": [{"title": "List of railway companies involved in the 1923 grouping", "link": "https://wikipedia.org/wiki/List_of_railway_companies_involved_in_the_1923_grouping#The_Big_Four"}, {"title": "London and North Eastern Railway", "link": "https://wikipedia.org/wiki/London_and_North_Eastern_Railway"}, {"title": "Great Western Railway", "link": "https://wikipedia.org/wiki/Great_Western_Railway"}, {"title": "Southern Railway (UK)", "link": "https://wikipedia.org/wiki/Southern_Railway_(UK)"}, {"title": "London, Midland and Scottish Railway", "link": "https://wikipedia.org/wiki/London,_Midland_and_Scottish_Railway"}]}, {"year": "1927", "text": "New Mexican oil legislation goes into effect, leading to the formal outbreak of the Cristero War.", "html": "1927 - New <a href=\"https://wikipedia.org/wiki/Mexico\" title=\"Mexico\">Mexican</a> oil legislation goes into effect, leading to the formal outbreak of the <a href=\"https://wikipedia.org/wiki/Cristero_War\" title=\"Cristero War\">Cristero War</a>.", "no_year_html": "New <a href=\"https://wikipedia.org/wiki/Mexico\" title=\"Mexico\">Mexican</a> oil legislation goes into effect, leading to the formal outbreak of the <a href=\"https://wikipedia.org/wiki/Cristero_War\" title=\"Cristero War\">Cristero War</a>.", "links": [{"title": "Mexico", "link": "https://wikipedia.org/wiki/Mexico"}, {"title": "Cristero War", "link": "https://wikipedia.org/wiki/Cristero_War"}]}, {"year": "1928", "text": "<PERSON> defects through Iran to seek asylum in France. He is the only member of <PERSON>'s secretariat to have defected from the Soviet Union.", "html": "1928 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> defects through <a href=\"https://wikipedia.org/wiki/Iran\" title=\"Iran\">Iran</a> to seek asylum in <a href=\"https://wikipedia.org/wiki/France\" title=\"France\">France</a>. He is the only member of <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>'s secretariat to have defected from the <a href=\"https://wikipedia.org/wiki/Soviet_Union\" title=\"Soviet Union\">Soviet Union</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> defects through <a href=\"https://wikipedia.org/wiki/Iran\" title=\"Iran\">Iran</a> to seek asylum in <a href=\"https://wikipedia.org/wiki/France\" title=\"France\">France</a>. He is the only member of <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>'s secretariat to have defected from the <a href=\"https://wikipedia.org/wiki/Soviet_Union\" title=\"Soviet Union\">Soviet Union</a>.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}, {"title": "Iran", "link": "https://wikipedia.org/wiki/Iran"}, {"title": "France", "link": "https://wikipedia.org/wiki/France"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}, {"title": "Soviet Union", "link": "https://wikipedia.org/wiki/Soviet_Union"}]}, {"year": "1929", "text": "The former municipalities of Point Grey, British Columbia and South Vancouver, British Columbia are amalgamated into Vancouver.", "html": "1929 - The former municipalities of <a href=\"https://wikipedia.org/wiki/Point_Grey,_British_Columbia\" class=\"mw-redirect\" title=\"Point Grey, British Columbia\">Point Grey, British Columbia</a> and <a href=\"https://wikipedia.org/wiki/South_Vancouver,_British_Columbia\" class=\"mw-redirect\" title=\"South Vancouver, British Columbia\">South Vancouver, British Columbia</a> are <a href=\"https://wikipedia.org/wiki/Merger_(politics)\" title=\"Merger (politics)\">amalgamated</a> into <a href=\"https://wikipedia.org/wiki/Vancouver\" title=\"Vancouver\">Vancouver</a>.", "no_year_html": "The former municipalities of <a href=\"https://wikipedia.org/wiki/Point_Grey,_British_Columbia\" class=\"mw-redirect\" title=\"Point Grey, British Columbia\">Point Grey, British Columbia</a> and <a href=\"https://wikipedia.org/wiki/South_Vancouver,_British_Columbia\" class=\"mw-redirect\" title=\"South Vancouver, British Columbia\">South Vancouver, British Columbia</a> are <a href=\"https://wikipedia.org/wiki/Merger_(politics)\" title=\"Merger (politics)\">amalgamated</a> into <a href=\"https://wikipedia.org/wiki/Vancouver\" title=\"Vancouver\">Vancouver</a>.", "links": [{"title": "Point Grey, British Columbia", "link": "https://wikipedia.org/wiki/Point_Grey,_British_Columbia"}, {"title": "South Vancouver, British Columbia", "link": "https://wikipedia.org/wiki/South_Vancouver,_British_Columbia"}, {"title": "<PERSON><PERSON> (politics)", "link": "https://wikipedia.org/wiki/Merger_(politics)"}, {"title": "Vancouver", "link": "https://wikipedia.org/wiki/Vancouver"}]}, {"year": "1932", "text": "The United States Post Office Department issues a set of 12 stamps commemorating the 200th anniversary of <PERSON>'s birth.", "html": "1932 - The <a href=\"https://wikipedia.org/wiki/United_States_Post_Office_Department\" title=\"United States Post Office Department\">United States Post Office Department</a> issues <a href=\"https://wikipedia.org/wiki/1932_Washington_Bicentennial\" class=\"mw-redirect\" title=\"1932 Washington Bicentennial\">a set of 12 stamps</a> commemorating the 200th anniversary of <a href=\"https://wikipedia.org/wiki/Washington%27s_Birthday\" class=\"mw-redirect\" title=\"Washington's Birthday\"><PERSON>'s birth</a>.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/United_States_Post_Office_Department\" title=\"United States Post Office Department\">United States Post Office Department</a> issues <a href=\"https://wikipedia.org/wiki/1932_Washington_Bicentennial\" class=\"mw-redirect\" title=\"1932 Washington Bicentennial\">a set of 12 stamps</a> commemorating the 200th anniversary of <a href=\"https://wikipedia.org/wiki/Washington%27s_Birthday\" class=\"mw-redirect\" title=\"Washington's Birthday\"><PERSON>'s birth</a>.", "links": [{"title": "United States Post Office Department", "link": "https://wikipedia.org/wiki/United_States_Post_Office_Department"}, {"title": "1932 Washington Bicentennial", "link": "https://wikipedia.org/wiki/1932_Washington_Bicentennial"}, {"title": "Washington's Birthday", "link": "https://wikipedia.org/wiki/Washington%27s_Birthday"}]}, {"year": "1934", "text": "Alcatraz Island in San Francisco Bay becomes a United States federal prison.", "html": "1934 - <a href=\"https://wikipedia.org/wiki/Alcatraz_Island\" title=\"Alcatraz Island\">Alcatraz Island</a> in <a href=\"https://wikipedia.org/wiki/San_Francisco_Bay\" title=\"San Francisco Bay\">San Francisco Bay</a> becomes a <a href=\"https://wikipedia.org/wiki/United_States_Federal_Prison\" class=\"mw-redirect\" title=\"United States Federal Prison\">United States federal prison</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Alcatraz_Island\" title=\"Alcatraz Island\">Alcatraz Island</a> in <a href=\"https://wikipedia.org/wiki/San_Francisco_Bay\" title=\"San Francisco Bay\">San Francisco Bay</a> becomes a <a href=\"https://wikipedia.org/wiki/United_States_Federal_Prison\" class=\"mw-redirect\" title=\"United States Federal Prison\">United States federal prison</a>.", "links": [{"title": "Alcatraz Island", "link": "https://wikipedia.org/wiki/Alcatraz_Island"}, {"title": "San Francisco Bay", "link": "https://wikipedia.org/wiki/San_Francisco_Bay"}, {"title": "United States Federal Prison", "link": "https://wikipedia.org/wiki/United_States_Federal_Prison"}]}, {"year": "1934", "text": "A \"Law for the Prevention of Genetically Diseased Offspring\" comes into effect in Nazi Germany.", "html": "1934 - A \"<a href=\"https://wikipedia.org/wiki/Nazi_eugenics\" title=\"Nazi eugenics\">Law for the Prevention of Genetically Diseased Offspring</a>\" comes into effect in <a href=\"https://wikipedia.org/wiki/Nazi_Germany\" title=\"Nazi Germany\">Nazi Germany</a>.", "no_year_html": "A \"<a href=\"https://wikipedia.org/wiki/Nazi_eugenics\" title=\"Nazi eugenics\">Law for the Prevention of Genetically Diseased Offspring</a>\" comes into effect in <a href=\"https://wikipedia.org/wiki/Nazi_Germany\" title=\"Nazi Germany\">Nazi Germany</a>.", "links": [{"title": "Nazi eugenics", "link": "https://wikipedia.org/wiki/Nazi_eugenics"}, {"title": "Nazi Germany", "link": "https://wikipedia.org/wiki/Nazi_Germany"}]}, {"year": "1942", "text": "The Declaration by United Nations is signed by twenty-six nations.", "html": "1942 - The <a href=\"https://wikipedia.org/wiki/Declaration_by_United_Nations\" title=\"Declaration by United Nations\">Declaration by United Nations</a> is signed by twenty-six nations.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Declaration_by_United_Nations\" title=\"Declaration by United Nations\">Declaration by United Nations</a> is signed by twenty-six nations.", "links": [{"title": "Declaration by United Nations", "link": "https://wikipedia.org/wiki/Declaration_by_United_Nations"}]}, {"year": "1945", "text": "World War II: The German Luftwaffe launches Operation Bodenplatte, a massive, but failed, attempt to knock out Allied air power in northern Europe in a single blow.", "html": "1945 - <a href=\"https://wikipedia.org/wiki/World_War_II\" title=\"World War II\">World War II</a>: The German <i><a href=\"https://wikipedia.org/wiki/Luftwaffe\" title=\"Luftwaffe\">Luftwaffe</a></i> launches <i><a href=\"https://wikipedia.org/wiki/Operation_Bodenplatte\" title=\"Operation Bodenplatte\">Operation Bodenplatte</a></i>, a massive, but failed, attempt to knock out <a href=\"https://wikipedia.org/wiki/Allies_of_World_War_II\" title=\"Allies of World War II\">Allied</a> air power in northern Europe in a single blow.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/World_War_II\" title=\"World War II\">World War II</a>: The German <i><a href=\"https://wikipedia.org/wiki/Luftwaffe\" title=\"Luftwaffe\">Luftwaffe</a></i> launches <i><a href=\"https://wikipedia.org/wiki/Operation_Bodenplatte\" title=\"Operation Bodenplatte\">Operation Bodenplatte</a></i>, a massive, but failed, attempt to knock out <a href=\"https://wikipedia.org/wiki/Allies_of_World_War_II\" title=\"Allies of World War II\">Allied</a> air power in northern Europe in a single blow.", "links": [{"title": "World War II", "link": "https://wikipedia.org/wiki/World_War_II"}, {"title": "Luftwaffe", "link": "https://wikipedia.org/wiki/Luftwaffe"}, {"title": "Operation Bodenplatte", "link": "https://wikipedia.org/wiki/Operation_Bodenplatte"}, {"title": "Allies of World War II", "link": "https://wikipedia.org/wiki/Allies_of_World_War_II"}]}, {"year": "1947", "text": "Cold War: The American and British occupation zones in Allied-occupied Germany, after World War II, merge to form the Bizone, which later (with the French zone) became part of West Germany.", "html": "1947 - <a href=\"https://wikipedia.org/wiki/Cold_War\" title=\"Cold War\">Cold War</a>: The American and British occupation zones in <a href=\"https://wikipedia.org/wiki/Allied-occupied_Germany\" title=\"Allied-occupied Germany\">Allied-occupied Germany</a>, after World War II, merge to form the <a href=\"https://wikipedia.org/wiki/Bizone\" title=\"Bizone\">Bizone</a>, which later (with the French zone) became part of <a href=\"https://wikipedia.org/wiki/West_Germany\" title=\"West Germany\">West Germany</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Cold_War\" title=\"Cold War\">Cold War</a>: The American and British occupation zones in <a href=\"https://wikipedia.org/wiki/Allied-occupied_Germany\" title=\"Allied-occupied Germany\">Allied-occupied Germany</a>, after World War II, merge to form the <a href=\"https://wikipedia.org/wiki/Bizone\" title=\"Bizone\">Bizone</a>, which later (with the French zone) became part of <a href=\"https://wikipedia.org/wiki/West_Germany\" title=\"West Germany\">West Germany</a>.", "links": [{"title": "Cold War", "link": "https://wikipedia.org/wiki/Cold_War"}, {"title": "Allied-occupied Germany", "link": "https://wikipedia.org/wiki/Allied-occupied_Germany"}, {"title": "Bizone", "link": "https://wikipedia.org/wiki/Bizone"}, {"title": "West Germany", "link": "https://wikipedia.org/wiki/West_Germany"}]}, {"year": "1947", "text": "The Canadian Citizenship Act 1946 comes into effect, converting British subjects into Canadian citizens. Prime Minister <PERSON> becomes the first Canadian citizen.", "html": "1947 - The <a href=\"https://wikipedia.org/wiki/Canadian_Citizenship_Act_1946\" class=\"mw-redirect\" title=\"Canadian Citizenship Act 1946\">Canadian Citizenship Act 1946</a> comes into effect, converting <a href=\"https://wikipedia.org/wiki/British_subject\" title=\"British subject\">British subjects</a> into <a href=\"https://wikipedia.org/wiki/Canadian_nationality_law\" title=\"Canadian nationality law\">Canadian citizens</a>. Prime Minister <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> becomes the first Canadian citizen.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Canadian_Citizenship_Act_1946\" class=\"mw-redirect\" title=\"Canadian Citizenship Act 1946\">Canadian Citizenship Act 1946</a> comes into effect, converting <a href=\"https://wikipedia.org/wiki/British_subject\" title=\"British subject\">British subjects</a> into <a href=\"https://wikipedia.org/wiki/Canadian_nationality_law\" title=\"Canadian nationality law\">Canadian citizens</a>. Prime Minister <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> becomes the first Canadian citizen.", "links": [{"title": "Canadian Citizenship Act 1946", "link": "https://wikipedia.org/wiki/Canadian_Citizenship_Act_1946"}, {"title": "British subject", "link": "https://wikipedia.org/wiki/British_subject"}, {"title": "Canadian nationality law", "link": "https://wikipedia.org/wiki/Canadian_nationality_law"}, {"title": "<PERSON> King", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1948", "text": "The British railway network is nationalized to form British Railways.", "html": "1948 - The <a href=\"https://wikipedia.org/wiki/Transport_in_England#Rail\" title=\"Transport in England\">British railway network</a> is <a href=\"https://wikipedia.org/wiki/Nationalization\" title=\"Nationalization\">nationalized</a> to form <a href=\"https://wikipedia.org/wiki/British_Rail\" title=\"British Rail\">British Railways</a>.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Transport_in_England#Rail\" title=\"Transport in England\">British railway network</a> is <a href=\"https://wikipedia.org/wiki/Nationalization\" title=\"Nationalization\">nationalized</a> to form <a href=\"https://wikipedia.org/wiki/British_Rail\" title=\"British Rail\">British Railways</a>.", "links": [{"title": "Transport in England", "link": "https://wikipedia.org/wiki/Transport_in_England#Rail"}, {"title": "Nationalization", "link": "https://wikipedia.org/wiki/Nationalization"}, {"title": "British Rail", "link": "https://wikipedia.org/wiki/British_Rail"}]}, {"year": "1949", "text": "United Nations cease-fire takes effect in Kashmir from one minute before midnight. War between India and Pakistan stops accordingly.", "html": "1949 - <a href=\"https://wikipedia.org/wiki/United_Nations\" title=\"United Nations\">United Nations</a> <a href=\"https://wikipedia.org/wiki/Ceasefire\" title=\"Ceasefire\">cease-fire</a> takes effect in <a href=\"https://wikipedia.org/wiki/Kashmir\" title=\"Kashmir\">Kashmir</a> from one minute before midnight. <a href=\"https://wikipedia.org/wiki/Indo-Pakistani_War_of_1947%E2%80%931948\" class=\"mw-redirect\" title=\"Indo-Pakistani War of 1947-1948\">War between India and Pakistan</a> stops accordingly.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/United_Nations\" title=\"United Nations\">United Nations</a> <a href=\"https://wikipedia.org/wiki/Ceasefire\" title=\"Ceasefire\">cease-fire</a> takes effect in <a href=\"https://wikipedia.org/wiki/Kashmir\" title=\"Kashmir\">Kashmir</a> from one minute before midnight. <a href=\"https://wikipedia.org/wiki/Indo-Pakistani_War_of_1947%E2%80%931948\" class=\"mw-redirect\" title=\"Indo-Pakistani War of 1947-1948\">War between India and Pakistan</a> stops accordingly.", "links": [{"title": "United Nations", "link": "https://wikipedia.org/wiki/United_Nations"}, {"title": "Ceasefire", "link": "https://wikipedia.org/wiki/Ceasefire"}, {"title": "Kashmir", "link": "https://wikipedia.org/wiki/Kashmir"}, {"title": "Indo-Pakistani War of 1947-1948", "link": "https://wikipedia.org/wiki/Indo-Pakistani_War_of_1947%E2%80%931948"}]}, {"year": "1956", "text": "Sudan achieves independence from Egypt and the United Kingdom.", "html": "1956 - <a href=\"https://wikipedia.org/wiki/Sudan\" title=\"Sudan\">Sudan</a> achieves independence from <a href=\"https://wikipedia.org/wiki/Egypt\" title=\"Egypt\">Egypt</a> and the United Kingdom.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Sudan\" title=\"Sudan\">Sudan</a> achieves independence from <a href=\"https://wikipedia.org/wiki/Egypt\" title=\"Egypt\">Egypt</a> and the United Kingdom.", "links": [{"title": "Sudan", "link": "https://wikipedia.org/wiki/Sudan"}, {"title": "Egypt", "link": "https://wikipedia.org/wiki/Egypt"}]}, {"year": "1957", "text": "George Town, Penang, is made a city by a royal charter of Queen <PERSON> of the United Kingdom.", "html": "1957 - <a href=\"https://wikipedia.org/wiki/George_Town,_Penang\" title=\"George Town, Penang\">George Town, Penang</a>, is made a city by a <a href=\"https://wikipedia.org/wiki/Royal_charter\" title=\"Royal charter\">royal charter</a> of Queen <a href=\"https://wikipedia.org/wiki/Elizabeth_II\" title=\"Elizabeth II\"><PERSON> II</a> of the United Kingdom.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/George_Town,_Penang\" title=\"George Town, Penang\">George Town, Penang</a>, is made a city by a <a href=\"https://wikipedia.org/wiki/Royal_charter\" title=\"Royal charter\">royal charter</a> of Queen <a href=\"https://wikipedia.org/wiki/Elizabeth_II\" title=\"Elizabeth II\"><PERSON> II</a> of the United Kingdom.", "links": [{"title": "George Town, Penang", "link": "https://wikipedia.org/wiki/George_Town,_Penang"}, {"title": "Royal charter", "link": "https://wikipedia.org/wiki/Royal_charter"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_II"}]}, {"year": "1957", "text": "Lèse majesté in Thailand is strengthened to include \"insult\" and changed to a crime against national security, after the Thai criminal code of 1956 went into effect.: 6, 18 ", "html": "1957 - <a href=\"https://wikipedia.org/wiki/L%C3%A8se_majest%C3%A9_in_Thailand\" class=\"mw-redirect\" title=\"Lèse majesté in Thailand\">Lèse majesté in Thailand</a> is strengthened to include \"<a href=\"https://wikipedia.org/wiki/Insult_(legal)\" title=\"Insult (legal)\">insult</a>\" and changed to a crime against <a href=\"https://wikipedia.org/wiki/National_security\" title=\"National security\">national security</a>, after the Thai criminal code of 1956 went into effect.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/L%C3%A8se_majest%C3%A9_in_Thailand\" class=\"mw-redirect\" title=\"Lèse majesté in Thailand\">Lèse majesté in Thailand</a> is strengthened to include \"<a href=\"https://wikipedia.org/wiki/Insult_(legal)\" title=\"Insult (legal)\">insult</a>\" and changed to a crime against <a href=\"https://wikipedia.org/wiki/National_security\" title=\"National security\">national security</a>, after the Thai criminal code of 1956 went into effect.", "links": [{"title": "Lèse majesté in Thailand", "link": "https://wikipedia.org/wiki/L%C3%A8se_majest%C3%A9_in_Thailand"}, {"title": "Insult (legal)", "link": "https://wikipedia.org/wiki/Insult_(legal)"}, {"title": "National security", "link": "https://wikipedia.org/wiki/National_security"}]}, {"year": "1958", "text": "The European Economic Community is established.", "html": "1958 - The <a href=\"https://wikipedia.org/wiki/European_Economic_Community\" title=\"European Economic Community\">European Economic Community</a> is established.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/European_Economic_Community\" title=\"European Economic Community\">European Economic Community</a> is established.", "links": [{"title": "European Economic Community", "link": "https://wikipedia.org/wiki/European_Economic_Community"}]}, {"year": "1959", "text": "Cuban Revolution: <PERSON><PERSON><PERSON><PERSON>, dictator of Cuba, is overthrown by <PERSON><PERSON>'s forces.", "html": "1959 - <a href=\"https://wikipedia.org/wiki/Cuban_Revolution\" title=\"Cuban Revolution\">Cuban Revolution</a>: <a href=\"https://wikipedia.org/wiki/Fulgencio_Batista\" title=\"Fulgencio Batista\"><PERSON><PERSON><PERSON><PERSON></a>, dictator of <a href=\"https://wikipedia.org/wiki/Cuba\" title=\"Cuba\">Cuba</a>, is overthrown by <a href=\"https://wikipedia.org/wiki/Fidel_Castro\" title=\"Fidel Castro\"><PERSON><PERSON></a>'s forces.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Cuban_Revolution\" title=\"Cuban Revolution\">Cuban Revolution</a>: <a href=\"https://wikipedia.org/wiki/Fulgencio_Batista\" title=\"Fulgencio Batista\"><PERSON><PERSON><PERSON><PERSON></a>, dictator of <a href=\"https://wikipedia.org/wiki/Cuba\" title=\"Cuba\">Cuba</a>, is overthrown by <a href=\"https://wikipedia.org/wiki/Fidel_<PERSON>\" title=\"Fidel Castro\"><PERSON><PERSON></a>'s forces.", "links": [{"title": "Cuban Revolution", "link": "https://wikipedia.org/wiki/Cuban_Revolution"}, {"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Fulgencio_Batista"}, {"title": "Cuba", "link": "https://wikipedia.org/wiki/Cuba"}, {"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1960", "text": "Cameroon achieves independence from France and the United Kingdom.", "html": "1960 - <a href=\"https://wikipedia.org/wiki/Cameroon\" title=\"Cameroon\">Cameroon</a> achieves independence from France and the United Kingdom.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Cameroon\" title=\"Cameroon\">Cameroon</a> achieves independence from France and the United Kingdom.", "links": [{"title": "Cameroon", "link": "https://wikipedia.org/wiki/Cameroon"}]}, {"year": "1962", "text": "Western Samoa achieves independence from New Zealand; its name is changed to the Independent State of Western Samoa.", "html": "1962 - <a href=\"https://wikipedia.org/wiki/Western_Samoa_Trust_Territory\" class=\"mw-redirect\" title=\"Western Samoa Trust Territory\">Western Samoa</a> achieves independence from <a href=\"https://wikipedia.org/wiki/New_Zealand\" title=\"New Zealand\">New Zealand</a>; its name is changed to the <a href=\"https://wikipedia.org/wiki/Independent_State_of_Western_Samoa\" class=\"mw-redirect\" title=\"Independent State of Western Samoa\">Independent State of Western Samoa</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Western_Samoa_Trust_Territory\" class=\"mw-redirect\" title=\"Western Samoa Trust Territory\">Western Samoa</a> achieves independence from <a href=\"https://wikipedia.org/wiki/New_Zealand\" title=\"New Zealand\">New Zealand</a>; its name is changed to the <a href=\"https://wikipedia.org/wiki/Independent_State_of_Western_Samoa\" class=\"mw-redirect\" title=\"Independent State of Western Samoa\">Independent State of Western Samoa</a>.", "links": [{"title": "Western Samoa Trust Territory", "link": "https://wikipedia.org/wiki/Western_Samoa_Trust_Territory"}, {"title": "New Zealand", "link": "https://wikipedia.org/wiki/New_Zealand"}, {"title": "Independent State of Western Samoa", "link": "https://wikipedia.org/wiki/Independent_State_of_Western_Samoa"}]}, {"year": "1964", "text": "The Federation of Rhodesia and Nyasaland is divided into the independent republics of Zambia and Malawi, and the British-controlled Rhodesia.", "html": "1964 - The <a href=\"https://wikipedia.org/wiki/Federation_of_Rhodesia_and_Nyasaland\" title=\"Federation of Rhodesia and Nyasaland\">Federation of Rhodesia and Nyasaland</a> is divided into the independent republics of <a href=\"https://wikipedia.org/wiki/Zambia\" title=\"Zambia\">Zambia</a> and <a href=\"https://wikipedia.org/wiki/Malawi\" title=\"Malawi\">Malawi</a>, and the British-controlled <a href=\"https://wikipedia.org/wiki/Rhodesia\" title=\"Rhodesia\">Rhodesia</a>.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Federation_of_Rhodesia_and_Nyasaland\" title=\"Federation of Rhodesia and Nyasaland\">Federation of Rhodesia and Nyasaland</a> is divided into the independent republics of <a href=\"https://wikipedia.org/wiki/Zambia\" title=\"Zambia\">Zambia</a> and <a href=\"https://wikipedia.org/wiki/Malawi\" title=\"Malawi\">Malawi</a>, and the British-controlled <a href=\"https://wikipedia.org/wiki/Rhodesia\" title=\"Rhodesia\">Rhodesia</a>.", "links": [{"title": "Federation of Rhodesia and Nyasaland", "link": "https://wikipedia.org/wiki/Federation_of_Rhodesia_and_Nyasaland"}, {"title": "Zambia", "link": "https://wikipedia.org/wiki/Zambia"}, {"title": "Malawi", "link": "https://wikipedia.org/wiki/Malawi"}, {"title": "Rhodesia", "link": "https://wikipedia.org/wiki/Rhodesia"}]}, {"year": "1965", "text": "The People's Democratic Party of Afghanistan is founded in Kabul, Afghanistan.", "html": "1965 - The <a href=\"https://wikipedia.org/wiki/People%27s_Democratic_Party_of_Afghanistan\" title=\"People's Democratic Party of Afghanistan\">People's Democratic Party of Afghanistan</a> is founded in <a href=\"https://wikipedia.org/wiki/Kabul\" title=\"Kabul\">Kabul, Afghanistan</a>.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/People%27s_Democratic_Party_of_Afghanistan\" title=\"People's Democratic Party of Afghanistan\">People's Democratic Party of Afghanistan</a> is founded in <a href=\"https://wikipedia.org/wiki/Kabul\" title=\"Kabul\">Kabul, Afghanistan</a>.", "links": [{"title": "People's Democratic Party of Afghanistan", "link": "https://wikipedia.org/wiki/People%27s_Democratic_Party_of_Afghanistan"}, {"title": "Kabul", "link": "https://wikipedia.org/wiki/Kabul"}]}, {"year": "1970", "text": "The defined beginning of Unix time, at 00:00:00.", "html": "1970 - The defined beginning of <a href=\"https://wikipedia.org/wiki/Unix_time\" title=\"Unix time\">Unix time</a>, at 00:00:00.", "no_year_html": "The defined beginning of <a href=\"https://wikipedia.org/wiki/Unix_time\" title=\"Unix time\">Unix time</a>, at 00:00:00.", "links": [{"title": "Unix time", "link": "https://wikipedia.org/wiki/Unix_time"}]}, {"year": "1971", "text": "Cigarette advertisements are banned on American television.", "html": "1971 - <a href=\"https://wikipedia.org/wiki/Cigarette\" title=\"Cigarette\">Cigarette</a> <a href=\"https://wikipedia.org/wiki/Nicotine_marketing\" title=\"Nicotine marketing\">advertisements</a> are banned on <a href=\"https://wikipedia.org/wiki/Television_in_the_United_States\" title=\"Television in the United States\">American television</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Cigarette\" title=\"Cigarette\">Cigarette</a> <a href=\"https://wikipedia.org/wiki/Nicotine_marketing\" title=\"Nicotine marketing\">advertisements</a> are banned on <a href=\"https://wikipedia.org/wiki/Television_in_the_United_States\" title=\"Television in the United States\">American television</a>.", "links": [{"title": "Cigarette", "link": "https://wikipedia.org/wiki/C<PERSON>rette"}, {"title": "Nicotine marketing", "link": "https://wikipedia.org/wiki/Nicotine_marketing"}, {"title": "Television in the United States", "link": "https://wikipedia.org/wiki/Television_in_the_United_States"}]}, {"year": "1973", "text": "Denmark, Ireland and the United Kingdom are admitted into the European Economic Community.", "html": "1973 - <a href=\"https://wikipedia.org/wiki/Denmark\" title=\"Denmark\">Denmark</a>, <a href=\"https://wikipedia.org/wiki/Republic_of_Ireland\" title=\"Republic of Ireland\">Ireland</a> and the <a href=\"https://wikipedia.org/wiki/United_Kingdom\" title=\"United Kingdom\">United Kingdom</a> are admitted into the <a href=\"https://wikipedia.org/wiki/European_Economic_Community\" title=\"European Economic Community\">European Economic Community</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Denmark\" title=\"Denmark\">Denmark</a>, <a href=\"https://wikipedia.org/wiki/Republic_of_Ireland\" title=\"Republic of Ireland\">Ireland</a> and the <a href=\"https://wikipedia.org/wiki/United_Kingdom\" title=\"United Kingdom\">United Kingdom</a> are admitted into the <a href=\"https://wikipedia.org/wiki/European_Economic_Community\" title=\"European Economic Community\">European Economic Community</a>.", "links": [{"title": "Denmark", "link": "https://wikipedia.org/wiki/Denmark"}, {"title": "Republic of Ireland", "link": "https://wikipedia.org/wiki/Republic_of_Ireland"}, {"title": "United Kingdom", "link": "https://wikipedia.org/wiki/United_Kingdom"}, {"title": "European Economic Community", "link": "https://wikipedia.org/wiki/European_Economic_Community"}]}, {"year": "1976", "text": "A bomb explodes on board Middle East Airlines Flight 438 over Qaisumah, Saudi Arabia, killing all 81 people on board.", "html": "1976 - A bomb explodes on board <a href=\"https://wikipedia.org/wiki/Middle_East_Airlines_Flight_438\" title=\"Middle East Airlines Flight 438\">Middle East Airlines Flight 438</a> over <a href=\"https://wikipedia.org/wiki/Qaisumah\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, <a href=\"https://wikipedia.org/wiki/Saudi_Arabia\" title=\"Saudi Arabia\">Saudi Arabia</a>, killing all 81 people on board.", "no_year_html": "A bomb explodes on board <a href=\"https://wikipedia.org/wiki/Middle_East_Airlines_Flight_438\" title=\"Middle East Airlines Flight 438\">Middle East Airlines Flight 438</a> over <a href=\"https://wikipedia.org/wiki/Qaisumah\" title=\"<PERSON><PERSON>uma<PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, <a href=\"https://wikipedia.org/wiki/Saudi_Arabia\" title=\"Saudi Arabia\">Saudi Arabia</a>, killing all 81 people on board.", "links": [{"title": "Middle East Airlines Flight 438", "link": "https://wikipedia.org/wiki/Middle_East_Airlines_Flight_438"}, {"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>ais<PERSON>h"}, {"title": "Saudi Arabia", "link": "https://wikipedia.org/wiki/Saudi_Arabia"}]}, {"year": "1978", "text": "Air India Flight 855, a Boeing 747, crashes into the Arabian Sea off the coast of Bombay, India, due to instrument failure, spatial disorientation, and pilot error, killing all 213 people on board.", "html": "1978 - <a href=\"https://wikipedia.org/wiki/Air_India_Flight_855\" title=\"Air India Flight 855\">Air India Flight 855</a>, a <a href=\"https://wikipedia.org/wiki/Boeing_747\" title=\"Boeing 747\">Boeing 747</a>, crashes into the <a href=\"https://wikipedia.org/wiki/Arabian_Sea\" title=\"Arabian Sea\">Arabian Sea</a> off the coast of <a href=\"https://wikipedia.org/wiki/Mumbai\" title=\"Mumbai\">Bombay, India</a>, due to instrument failure, <a href=\"https://wikipedia.org/wiki/Spatial_disorientation\" title=\"Spatial disorientation\">spatial disorientation</a>, and <a href=\"https://wikipedia.org/wiki/Pilot_error\" title=\"Pilot error\">pilot error</a>, killing all 213 people on board.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Air_India_Flight_855\" title=\"Air India Flight 855\">Air India Flight 855</a>, a <a href=\"https://wikipedia.org/wiki/Boeing_747\" title=\"Boeing 747\">Boeing 747</a>, crashes into the <a href=\"https://wikipedia.org/wiki/Arabian_Sea\" title=\"Arabian Sea\">Arabian Sea</a> off the coast of <a href=\"https://wikipedia.org/wiki/Mumbai\" title=\"Mumbai\">Bombay, India</a>, due to instrument failure, <a href=\"https://wikipedia.org/wiki/Spatial_disorientation\" title=\"Spatial disorientation\">spatial disorientation</a>, and <a href=\"https://wikipedia.org/wiki/Pilot_error\" title=\"Pilot error\">pilot error</a>, killing all 213 people on board.", "links": [{"title": "Air India Flight 855", "link": "https://wikipedia.org/wiki/Air_India_Flight_855"}, {"title": "Boeing 747", "link": "https://wikipedia.org/wiki/Boeing_747"}, {"title": "Arabian Sea", "link": "https://wikipedia.org/wiki/Arabian_Sea"}, {"title": "Mumbai", "link": "https://wikipedia.org/wiki/Mumbai"}, {"title": "Spatial disorientation", "link": "https://wikipedia.org/wiki/Spatial_disorientation"}, {"title": "Pilot error", "link": "https://wikipedia.org/wiki/Pilot_error"}]}, {"year": "1979", "text": "the Joint Communiqué on the Establishment of Diplomatic Relations and Taiwan Relations Act enter into force. Through the Communiqué, the United States establishes normal diplomatic relations with China. Through the Act, the United States guarantees military support for Taiwan.", "html": "1979 - the <a href=\"https://wikipedia.org/wiki/Joint_Communiqu%C3%A9_on_the_Establishment_of_Diplomatic_Relations\" title=\"Joint Communiqué on the Establishment of Diplomatic Relations\">Joint Communiqué on the Establishment of Diplomatic Relations</a> and <a href=\"https://wikipedia.org/wiki/Taiwan_Relations_Act\" title=\"Taiwan Relations Act\">Taiwan Relations Act</a> enter into force. Through the Communiqué, the United States establishes normal diplomatic relations with China. Through the Act, the United States guarantees military support for Taiwan.", "no_year_html": "the <a href=\"https://wikipedia.org/wiki/Joint_Communiqu%C3%A9_on_the_Establishment_of_Diplomatic_Relations\" title=\"Joint Communiqué on the Establishment of Diplomatic Relations\">Joint Communiqué on the Establishment of Diplomatic Relations</a> and <a href=\"https://wikipedia.org/wiki/Taiwan_Relations_Act\" title=\"Taiwan Relations Act\">Taiwan Relations Act</a> enter into force. Through the Communiqué, the United States establishes normal diplomatic relations with China. Through the Act, the United States guarantees military support for Taiwan.", "links": [{"title": "Joint Communiqué on the Establishment of Diplomatic Relations", "link": "https://wikipedia.org/wiki/Joint_Communiqu%C3%A9_on_the_Establishment_of_Diplomatic_Relations"}, {"title": "Taiwan Relations Act", "link": "https://wikipedia.org/wiki/Taiwan_Relations_Act"}]}, {"year": "1981", "text": "Greece is admitted into the European Community.", "html": "1981 - <a href=\"https://wikipedia.org/wiki/Greece\" title=\"Greece\">Greece</a> is admitted into the European Community.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Greece\" title=\"Greece\">Greece</a> is admitted into the European Community.", "links": [{"title": "Greece", "link": "https://wikipedia.org/wiki/Greece"}]}, {"year": "1982", "text": "Peruvian <PERSON> becomes the first Latin American to hold the title of Secretary-General of the United Nations.", "html": "1982 - <a href=\"https://wikipedia.org/wiki/Peru\" title=\"Peru\">Peruvian</a> <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A9rez_de_Cu%C3%A9llar\" title=\"<PERSON>\"><PERSON></a> becomes the first <a href=\"https://wikipedia.org/wiki/Latin_America\" title=\"Latin America\">Latin American</a> to hold the title of <a href=\"https://wikipedia.org/wiki/Secretary-General_of_the_United_Nations\" title=\"Secretary-General of the United Nations\">Secretary-General of the United Nations</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Peru\" title=\"Peru\">Peruvian</a> <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A9rez_de_Cu%C3%A9llar\" title=\"<PERSON>\"><PERSON></a> becomes the first <a href=\"https://wikipedia.org/wiki/Latin_America\" title=\"Latin America\">Latin American</a> to hold the title of <a href=\"https://wikipedia.org/wiki/Secretary-General_of_the_United_Nations\" title=\"Secretary-General of the United Nations\">Secretary-General of the United Nations</a>.", "links": [{"title": "Peru", "link": "https://wikipedia.org/wiki/Peru"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Javier_P%C3%A9rez_de_Cu%C3%A9llar"}, {"title": "Latin America", "link": "https://wikipedia.org/wiki/Latin_America"}, {"title": "Secretary-General of the United Nations", "link": "https://wikipedia.org/wiki/Secretary-General_of_the_United_Nations"}]}, {"year": "1983", "text": "The ARPANET officially changes to using TCP/IP, the Internet Protocol, effectively creating the Internet.", "html": "1983 - The <a href=\"https://wikipedia.org/wiki/ARPANET\" title=\"ARPANET\">ARPANET</a> officially changes to using TCP/IP, the <a href=\"https://wikipedia.org/wiki/Internet_Protocol\" title=\"Internet Protocol\">Internet Protocol</a>, effectively creating the <a href=\"https://wikipedia.org/wiki/Internet\" title=\"Internet\">Internet</a>.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/ARPANET\" title=\"ARPANET\">ARPANET</a> officially changes to using TCP/IP, the <a href=\"https://wikipedia.org/wiki/Internet_Protocol\" title=\"Internet Protocol\">Internet Protocol</a>, effectively creating the <a href=\"https://wikipedia.org/wiki/Internet\" title=\"Internet\">Internet</a>.", "links": [{"title": "ARPANET", "link": "https://wikipedia.org/wiki/ARPANET"}, {"title": "Internet Protocol", "link": "https://wikipedia.org/wiki/Internet_Protocol"}, {"title": "Internet", "link": "https://wikipedia.org/wiki/Internet"}]}, {"year": "1984", "text": "The original American Telephone & Telegraph Company is divested of its 22 Bell System companies as a result of the settlement of the 1974 United States Department of Justice antitrust suit against AT&T.", "html": "1984 - The original <a href=\"https://wikipedia.org/wiki/AT%26T_Corporation\" title=\"AT&amp;T Corporation\">American Telephone &amp; Telegraph Company</a> is divested of its 22 <a href=\"https://wikipedia.org/wiki/Bell_System\" title=\"Bell System\">Bell System</a> companies as a result of the settlement of the 1974 <a href=\"https://wikipedia.org/wiki/United_States_Department_of_Justice\" title=\"United States Department of Justice\">United States Department of Justice</a> <a href=\"https://wikipedia.org/wiki/Competition_law\" title=\"Competition law\">antitrust</a> <a href=\"https://wikipedia.org/wiki/Breakup_of_the_Bell_System\" title=\"Breakup of the Bell System\">suit against AT&amp;T</a>.", "no_year_html": "The original <a href=\"https://wikipedia.org/wiki/AT%26T_Corporation\" title=\"AT&amp;T Corporation\">American Telephone &amp; Telegraph Company</a> is divested of its 22 <a href=\"https://wikipedia.org/wiki/Bell_System\" title=\"Bell System\">Bell System</a> companies as a result of the settlement of the 1974 <a href=\"https://wikipedia.org/wiki/United_States_Department_of_Justice\" title=\"United States Department of Justice\">United States Department of Justice</a> <a href=\"https://wikipedia.org/wiki/Competition_law\" title=\"Competition law\">antitrust</a> <a href=\"https://wikipedia.org/wiki/Breakup_of_the_Bell_System\" title=\"Breakup of the Bell System\">suit against AT&amp;T</a>.", "links": [{"title": "AT&T Corporation", "link": "https://wikipedia.org/wiki/AT%26T_Corporation"}, {"title": "Bell System", "link": "https://wikipedia.org/wiki/Bell_System"}, {"title": "United States Department of Justice", "link": "https://wikipedia.org/wiki/United_States_Department_of_Justice"}, {"title": "Competition law", "link": "https://wikipedia.org/wiki/Competition_law"}, {"title": "Breakup of the Bell System", "link": "https://wikipedia.org/wiki/Breakup_of_the_Bell_System"}]}, {"year": "1984", "text": "Brunei becomes independent of the United Kingdom.", "html": "1984 - <a href=\"https://wikipedia.org/wiki/Brunei\" title=\"Brunei\">Brunei</a> becomes independent of the United Kingdom.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Brunei\" title=\"Brunei\">Brunei</a> becomes independent of the United Kingdom.", "links": [{"title": "Brunei", "link": "https://wikipedia.org/wiki/Brunei"}]}, {"year": "1985", "text": "The first British mobile phone call is made by <PERSON> to his father Sir <PERSON>, chairman of Vodafone.", "html": "1985 - The first British <a href=\"https://wikipedia.org/wiki/Mobile_phone\" title=\"Mobile phone\">mobile phone</a> call is made by <PERSON> to his father <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\">Sir <PERSON></a>, chairman of <a href=\"https://wikipedia.org/wiki/Vodafone\" title=\"Vodafone\">Vodafone</a>.", "no_year_html": "The first British <a href=\"https://wikipedia.org/wiki/Mobile_phone\" title=\"Mobile phone\">mobile phone</a> call is made by <PERSON> to his father <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\">Sir <PERSON></a>, chairman of <a href=\"https://wikipedia.org/wiki/Vodafone\" title=\"Vodafone\">Vodafone</a>.", "links": [{"title": "Mobile phone", "link": "https://wikipedia.org/wiki/Mobile_phone"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}, {"title": "Vodafone", "link": "https://wikipedia.org/wiki/Vodafone"}]}, {"year": "1985", "text": "Eastern Air Lines Flight 980 crashes into Mount Illimani in Bolivia, killing all 29 aboard.", "html": "1985 - <a href=\"https://wikipedia.org/wiki/Eastern_Air_Lines_Flight_980\" title=\"Eastern Air Lines Flight 980\">Eastern Air Lines Flight 980</a> crashes into <a href=\"https://wikipedia.org/wiki/Illimani\" title=\"Illimani\">Mount Illimani</a> in Bolivia, killing all 29 aboard.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Eastern_Air_Lines_Flight_980\" title=\"Eastern Air Lines Flight 980\">Eastern Air Lines Flight 980</a> crashes into <a href=\"https://wikipedia.org/wiki/Illimani\" title=\"Illimani\">Mount Illimani</a> in Bolivia, killing all 29 aboard.", "links": [{"title": "Eastern Air Lines Flight 980", "link": "https://wikipedia.org/wiki/Eastern_Air_Lines_Flight_980"}, {"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Illimani"}]}, {"year": "1987", "text": "The Isleta Pueblo tribe elect <PERSON><PERSON><PERSON> to be their first female governor.", "html": "1987 - The <a href=\"https://wikipedia.org/wiki/Pueblo_of_Isleta\" title=\"Pueblo of Isleta\">Isleta Pueblo</a> tribe elect <PERSON><PERSON><PERSON> to be their first female governor.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Pueblo_of_Isleta\" title=\"Pueblo of Isleta\">Isleta Pueblo</a> tribe elect <PERSON><PERSON><PERSON> to be their first female governor.", "links": [{"title": "Pueblo of Isleta", "link": "https://wikipedia.org/wiki/Pueblo_of_Isleta"}]}, {"year": "1988", "text": "The Evangelical Lutheran Church in America comes into existence, creating the largest Lutheran denomination in the United States.", "html": "1988 - The <a href=\"https://wikipedia.org/wiki/Evangelical_Lutheran_Church_in_America\" title=\"Evangelical Lutheran Church in America\">Evangelical Lutheran Church in America</a> comes into existence, creating the largest <a href=\"https://wikipedia.org/wiki/Lutheranism\" title=\"Lutheranism\">Lutheran</a> <a href=\"https://wikipedia.org/wiki/Religious_denomination\" title=\"Religious denomination\">denomination</a> in the United States.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Evangelical_Lutheran_Church_in_America\" title=\"Evangelical Lutheran Church in America\">Evangelical Lutheran Church in America</a> comes into existence, creating the largest <a href=\"https://wikipedia.org/wiki/Lutheranism\" title=\"Lutheranism\">Lutheran</a> <a href=\"https://wikipedia.org/wiki/Religious_denomination\" title=\"Religious denomination\">denomination</a> in the United States.", "links": [{"title": "Evangelical Lutheran Church in America", "link": "https://wikipedia.org/wiki/Evangelical_Lutheran_Church_in_America"}, {"title": "Lutheranism", "link": "https://wikipedia.org/wiki/Lutheranism"}, {"title": "Religious denomination", "link": "https://wikipedia.org/wiki/Religious_denomination"}]}, {"year": "1989", "text": "The Montreal Protocol comes into force, stopping the use of chemicals contributing to ozone depletion.", "html": "1989 - The <a href=\"https://wikipedia.org/wiki/Montreal_Protocol\" title=\"Montreal Protocol\">Montreal Protocol</a> comes into force, stopping the use of chemicals contributing to <a href=\"https://wikipedia.org/wiki/Ozone_depletion\" title=\"Ozone depletion\">ozone depletion</a>.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Montreal_Protocol\" title=\"Montreal Protocol\">Montreal Protocol</a> comes into force, stopping the use of chemicals contributing to <a href=\"https://wikipedia.org/wiki/Ozone_depletion\" title=\"Ozone depletion\">ozone depletion</a>.", "links": [{"title": "Montreal Protocol", "link": "https://wikipedia.org/wiki/Montreal_Protocol"}, {"title": "Ozone depletion", "link": "https://wikipedia.org/wiki/Ozone_depletion"}]}, {"year": "1990", "text": "<PERSON> is sworn in as New York City's first black mayor.", "html": "1990 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> is sworn in as <a href=\"https://wikipedia.org/wiki/New_York_City\" title=\"New York City\">New York City</a>'s first black <a href=\"https://wikipedia.org/wiki/Mayor_of_New_York_City\" title=\"Mayor of New York City\">mayor</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> is sworn in as <a href=\"https://wikipedia.org/wiki/New_York_City\" title=\"New York City\">New York City</a>'s first black <a href=\"https://wikipedia.org/wiki/Mayor_of_New_York_City\" title=\"Mayor of New York City\">mayor</a>.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}, {"title": "New York City", "link": "https://wikipedia.org/wiki/New_York_City"}, {"title": "Mayor of New York City", "link": "https://wikipedia.org/wiki/Mayor_of_New_York_City"}]}, {"year": "1993", "text": "Dissolution of Czechoslovakia: Czechoslovakia is divided into the Czech Republic and Slovak Republic.", "html": "1993 - <a href=\"https://wikipedia.org/wiki/Dissolution_of_Czechoslovakia\" title=\"Dissolution of Czechoslovakia\">Dissolution of Czechoslovakia</a>: <a href=\"https://wikipedia.org/wiki/Czechoslovakia\" title=\"Czechoslovakia\">Czechoslovakia</a> is divided into the <a href=\"https://wikipedia.org/wiki/Czech_Republic\" title=\"Czech Republic\">Czech Republic</a> and <a href=\"https://wikipedia.org/wiki/Slovakia\" title=\"Slovakia\">Slovak Republic</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Dissolution_of_Czechoslovakia\" title=\"Dissolution of Czechoslovakia\">Dissolution of Czechoslovakia</a>: <a href=\"https://wikipedia.org/wiki/Czechoslovakia\" title=\"Czechoslovakia\">Czechoslovakia</a> is divided into the <a href=\"https://wikipedia.org/wiki/Czech_Republic\" title=\"Czech Republic\">Czech Republic</a> and <a href=\"https://wikipedia.org/wiki/Slovakia\" title=\"Slovakia\">Slovak Republic</a>.", "links": [{"title": "Dissolution of Czechoslovakia", "link": "https://wikipedia.org/wiki/Dissolution_of_Czechoslovakia"}, {"title": "Czechoslovakia", "link": "https://wikipedia.org/wiki/Czechoslovakia"}, {"title": "Czech Republic", "link": "https://wikipedia.org/wiki/Czech_Republic"}, {"title": "Slovakia", "link": "https://wikipedia.org/wiki/Slovakia"}]}, {"year": "1994", "text": "The Zapatista Army of National Liberation initiates twelve days of armed conflict in the Mexican state of Chiapas.", "html": "1994 - The <a href=\"https://wikipedia.org/wiki/Zapatista_Army_of_National_Liberation\" title=\"Zapatista Army of National Liberation\">Zapatista Army of National Liberation</a> initiates twelve days of armed conflict in the Mexican state of <a href=\"https://wikipedia.org/wiki/Chiapas\" title=\"Chiapas\">Chiapas</a>.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Zapatista_Army_of_National_Liberation\" title=\"Zapatista Army of National Liberation\">Zapatista Army of National Liberation</a> initiates twelve days of armed conflict in the Mexican state of <a href=\"https://wikipedia.org/wiki/Chiapas\" title=\"Chiapas\">Chiapas</a>.", "links": [{"title": "Zapatista Army of National Liberation", "link": "https://wikipedia.org/wiki/Zapatista_Army_of_National_Liberation"}, {"title": "Chiapas", "link": "https://wikipedia.org/wiki/Chiapas"}]}, {"year": "1994", "text": "The North American Free Trade Agreement (NAFTA) comes into effect.", "html": "1994 - The <a href=\"https://wikipedia.org/wiki/North_American_Free_Trade_Agreement\" title=\"North American Free Trade Agreement\">North American Free Trade Agreement</a> (NAFTA) comes into effect.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/North_American_Free_Trade_Agreement\" title=\"North American Free Trade Agreement\">North American Free Trade Agreement</a> (NAFTA) comes into effect.", "links": [{"title": "North American Free Trade Agreement", "link": "https://wikipedia.org/wiki/North_American_Free_Trade_Agreement"}]}, {"year": "1995", "text": "The World Trade Organization comes into being.", "html": "1995 - The <a href=\"https://wikipedia.org/wiki/World_Trade_Organization\" title=\"World Trade Organization\">World Trade Organization</a> comes into being.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/World_Trade_Organization\" title=\"World Trade Organization\">World Trade Organization</a> comes into being.", "links": [{"title": "World Trade Organization", "link": "https://wikipedia.org/wiki/World_Trade_Organization"}]}, {"year": "1995", "text": "The Draupner wave in the North Sea in Norway is detected, confirming the existence of freak waves.", "html": "1995 - The <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>ner_wave\" title=\"Draupner wave\">Dr<PERSON><PERSON>ner wave</a> in the <a href=\"https://wikipedia.org/wiki/North_Sea\" title=\"North Sea\">North Sea</a> in <a href=\"https://wikipedia.org/wiki/Norway\" title=\"Norway\">Norway</a> is detected, confirming the existence of <a href=\"https://wikipedia.org/wiki/Rogue_wave\" title=\"Rogue wave\">freak waves</a>.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_wave\" title=\"Dr<PERSON><PERSON>ner wave\">Dr<PERSON><PERSON>ner wave</a> in the <a href=\"https://wikipedia.org/wiki/North_Sea\" title=\"North Sea\">North Sea</a> in <a href=\"https://wikipedia.org/wiki/Norway\" title=\"Norway\">Norway</a> is detected, confirming the existence of <a href=\"https://wikipedia.org/wiki/Rogue_wave\" title=\"Rogue wave\">freak waves</a>.", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON> wave", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_wave"}, {"title": "North Sea", "link": "https://wikipedia.org/wiki/North_Sea"}, {"title": "Norway", "link": "https://wikipedia.org/wiki/Norway"}, {"title": "Rogue wave", "link": "https://wikipedia.org/wiki/Rogue_wave"}]}, {"year": "1995", "text": "Austria, Finland and Sweden join the EU.", "html": "1995 - <a href=\"https://wikipedia.org/wiki/Austria\" title=\"Austria\">Austria</a>, <a href=\"https://wikipedia.org/wiki/Finland\" title=\"Finland\">Finland</a> and <a href=\"https://wikipedia.org/wiki/Sweden\" title=\"Sweden\">Sweden</a> <a href=\"https://wikipedia.org/wiki/1995_enlargement_of_the_European_Union\" title=\"1995 enlargement of the European Union\">join the EU</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Austria\" title=\"Austria\">Austria</a>, <a href=\"https://wikipedia.org/wiki/Finland\" title=\"Finland\">Finland</a> and <a href=\"https://wikipedia.org/wiki/Sweden\" title=\"Sweden\">Sweden</a> <a href=\"https://wikipedia.org/wiki/1995_enlargement_of_the_European_Union\" title=\"1995 enlargement of the European Union\">join the EU</a>.", "links": [{"title": "Austria", "link": "https://wikipedia.org/wiki/Austria"}, {"title": "Finland", "link": "https://wikipedia.org/wiki/Finland"}, {"title": "Sweden", "link": "https://wikipedia.org/wiki/Sweden"}, {"title": "1995 enlargement of the European Union", "link": "https://wikipedia.org/wiki/1995_enlargement_of_the_European_Union"}]}, {"year": "1998", "text": "Following a currency reform, Russia begins to circulate new rubles to stem inflation and promote confidence.", "html": "1998 - Following a <a href=\"https://wikipedia.org/wiki/Monetary_reform_in_Russia,_1998\" title=\"Monetary reform in Russia, 1998\">currency reform</a>, Russia begins to circulate new <a href=\"https://wikipedia.org/wiki/Russian_ruble\" title=\"Russian ruble\">rubles</a> to stem <a href=\"https://wikipedia.org/wiki/Inflation\" title=\"Inflation\">inflation</a> and promote confidence.", "no_year_html": "Following a <a href=\"https://wikipedia.org/wiki/Monetary_reform_in_Russia,_1998\" title=\"Monetary reform in Russia, 1998\">currency reform</a>, Russia begins to circulate new <a href=\"https://wikipedia.org/wiki/Russian_ruble\" title=\"Russian ruble\">rubles</a> to stem <a href=\"https://wikipedia.org/wiki/Inflation\" title=\"Inflation\">inflation</a> and promote confidence.", "links": [{"title": "Monetary reform in Russia, 1998", "link": "https://wikipedia.org/wiki/Monetary_reform_in_Russia,_1998"}, {"title": "Russian ruble", "link": "https://wikipedia.org/wiki/Russian_ruble"}, {"title": "Inflation", "link": "https://wikipedia.org/wiki/Inflation"}]}, {"year": "1998", "text": "Argentinian physicist <PERSON> publishes a landmark paper initiating the study of AdS/CFT correspondence, which links string theory and quantum gravity.", "html": "1998 - Argentinian physicist <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> publishes a landmark paper initiating the study of <a href=\"https://wikipedia.org/wiki/AdS/CFT_correspondence\" title=\"AdS/CFT correspondence\">AdS/CFT correspondence</a>, which links <a href=\"https://wikipedia.org/wiki/String_theory\" title=\"String theory\">string theory</a> and <a href=\"https://wikipedia.org/wiki/Quantum_gravity\" title=\"Quantum gravity\">quantum gravity</a>.", "no_year_html": "Argentinian physicist <a href=\"https://wikipedia.org/wiki/Juan<PERSON>\" title=\"Juan <PERSON>\"><PERSON></a> publishes a landmark paper initiating the study of <a href=\"https://wikipedia.org/wiki/AdS/CFT_correspondence\" title=\"AdS/CFT correspondence\">AdS/CFT correspondence</a>, which links <a href=\"https://wikipedia.org/wiki/String_theory\" title=\"String theory\">string theory</a> and <a href=\"https://wikipedia.org/wiki/Quantum_gravity\" title=\"Quantum gravity\">quantum gravity</a>.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Juan_<PERSON>"}, {"title": "AdS/CFT correspondence", "link": "https://wikipedia.org/wiki/AdS/CFT_correspondence"}, {"title": "String theory", "link": "https://wikipedia.org/wiki/String_theory"}, {"title": "Quantum gravity", "link": "https://wikipedia.org/wiki/Quantum_gravity"}]}, {"year": "1999", "text": "The Euro currency is introduced in 11 member nations of the European Union (with the exception of the United Kingdom, Denmark, Greece and Sweden).", "html": "1999 - The <a href=\"https://wikipedia.org/wiki/Euro\" title=\"Euro\">Euro</a> currency is introduced in 11 member nations of the <a href=\"https://wikipedia.org/wiki/European_Union\" title=\"European Union\">European Union</a> (with the exception of the United Kingdom, Denmark, Greece and Sweden).", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Euro\" title=\"Euro\">Euro</a> currency is introduced in 11 member nations of the <a href=\"https://wikipedia.org/wiki/European_Union\" title=\"European Union\">European Union</a> (with the exception of the United Kingdom, Denmark, Greece and Sweden).", "links": [{"title": "Euro", "link": "https://wikipedia.org/wiki/Euro"}, {"title": "European Union", "link": "https://wikipedia.org/wiki/European_Union"}]}, {"year": "2001", "text": "Greece adopts the Euro, becoming the 12th Eurozone country.", "html": "2001 - <a href=\"https://wikipedia.org/wiki/Greece\" title=\"Greece\">Greece</a> adopts the <a href=\"https://wikipedia.org/wiki/Euro\" title=\"Euro\">Euro</a>, becoming the 12th <a href=\"https://wikipedia.org/wiki/Eurozone\" title=\"Eurozone\">Eurozone</a> country.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Greece\" title=\"Greece\">Greece</a> adopts the <a href=\"https://wikipedia.org/wiki/Euro\" title=\"Euro\">Euro</a>, becoming the 12th <a href=\"https://wikipedia.org/wiki/Eurozone\" title=\"Eurozone\">Eurozone</a> country.", "links": [{"title": "Greece", "link": "https://wikipedia.org/wiki/Greece"}, {"title": "Euro", "link": "https://wikipedia.org/wiki/Euro"}, {"title": "Eurozone", "link": "https://wikipedia.org/wiki/Eurozone"}]}, {"year": "2004", "text": "In a vote of confidence, General <PERSON><PERSON> wins 658 out of 1,170 votes in the Electoral College of Pakistan, and according to Article 41(8) of the Constitution of Pakistan, is \"deemed to be elected\" to the office of President until October 2007.", "html": "2004 - In a <a href=\"https://wikipedia.org/wiki/Motion_of_no_confidence\" title=\"Motion of no confidence\">vote of confidence</a>, General <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a> wins 658 out of 1,170 votes in the <a href=\"https://wikipedia.org/wiki/Electoral_College_(Pakistan)\" title=\"Electoral College (Pakistan)\">Electoral College of Pakistan</a>, and according to Article 41(8) of the <a href=\"https://wikipedia.org/wiki/Constitution_of_Pakistan\" title=\"Constitution of Pakistan\">Constitution of Pakistan</a>, is \"deemed to be elected\" to the office of <a href=\"https://wikipedia.org/wiki/President_of_Pakistan\" title=\"President of Pakistan\">President</a> until October 2007.", "no_year_html": "In a <a href=\"https://wikipedia.org/wiki/Motion_of_no_confidence\" title=\"Motion of no confidence\">vote of confidence</a>, General <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a> wins 658 out of 1,170 votes in the <a href=\"https://wikipedia.org/wiki/Electoral_College_(Pakistan)\" title=\"Electoral College (Pakistan)\">Electoral College of Pakistan</a>, and according to Article 41(8) of the <a href=\"https://wikipedia.org/wiki/Constitution_of_Pakistan\" title=\"Constitution of Pakistan\">Constitution of Pakistan</a>, is \"deemed to be elected\" to the office of <a href=\"https://wikipedia.org/wiki/President_of_Pakistan\" title=\"President of Pakistan\">President</a> until October 2007.", "links": [{"title": "Motion of no confidence", "link": "https://wikipedia.org/wiki/Motion_of_no_confidence"}, {"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>f"}, {"title": "Electoral College (Pakistan)", "link": "https://wikipedia.org/wiki/Electoral_College_(Pakistan)"}, {"title": "Constitution of Pakistan", "link": "https://wikipedia.org/wiki/Constitution_of_Pakistan"}, {"title": "President of Pakistan", "link": "https://wikipedia.org/wiki/President_of_Pakistan"}]}, {"year": "2007", "text": "Bulgaria and Romania join the EU.", "html": "2007 - <a href=\"https://wikipedia.org/wiki/Bulgaria\" title=\"Bulgaria\">Bulgaria</a> and <a href=\"https://wikipedia.org/wiki/Romania\" title=\"Romania\">Romania</a> <a href=\"https://wikipedia.org/wiki/2007_enlargement_of_the_European_Union\" title=\"2007 enlargement of the European Union\">join the EU</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Bulgaria\" title=\"Bulgaria\">Bulgaria</a> and <a href=\"https://wikipedia.org/wiki/Romania\" title=\"Romania\">Romania</a> <a href=\"https://wikipedia.org/wiki/2007_enlargement_of_the_European_Union\" title=\"2007 enlargement of the European Union\">join the EU</a>.", "links": [{"title": "Bulgaria", "link": "https://wikipedia.org/wiki/Bulgaria"}, {"title": "Romania", "link": "https://wikipedia.org/wiki/Romania"}, {"title": "2007 enlargement of the European Union", "link": "https://wikipedia.org/wiki/2007_enlargement_of_the_European_Union"}]}, {"year": "2007", "text": "Adam Air Flight 574 breaks apart in mid-air and crashes near the Makassar Strait, Indonesia, killing all 102 people on board.", "html": "2007 - <a href=\"https://wikipedia.org/wiki/Adam_Air_Flight_574\" title=\"Adam Air Flight 574\">Adam Air Flight 574</a> breaks apart in mid-air and crashes near the <a href=\"https://wikipedia.org/wiki/Makassar_Strait\" title=\"Makassar Strait\">Makassar Strait</a>, <a href=\"https://wikipedia.org/wiki/Indonesia\" title=\"Indonesia\">Indonesia</a>, killing all 102 people on board.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Adam_Air_Flight_574\" title=\"Adam Air Flight 574\">Adam Air Flight 574</a> breaks apart in mid-air and crashes near the <a href=\"https://wikipedia.org/wiki/Makassar_Strait\" title=\"Makassar Strait\">Makassar Strait</a>, <a href=\"https://wikipedia.org/wiki/Indonesia\" title=\"Indonesia\">Indonesia</a>, killing all 102 people on board.", "links": [{"title": "Adam Air Flight 574", "link": "https://wikipedia.org/wiki/Adam_Air_Flight_574"}, {"title": "Makassar Strait", "link": "https://wikipedia.org/wiki/Makassar_Strait"}, {"title": "Indonesia", "link": "https://wikipedia.org/wiki/Indonesia"}]}, {"year": "2009", "text": "Sixty-six people die in a nightclub fire in Bangkok, Thailand.", "html": "2009 - Sixty-six people die in a <a href=\"https://wikipedia.org/wiki/Santika_Club_fire\" title=\"Santika Club fire\">nightclub fire</a> in <a href=\"https://wikipedia.org/wiki/Bangkok\" title=\"Bangkok\">Bangkok, Thailand</a>.", "no_year_html": "Sixty-six people die in a <a href=\"https://wikipedia.org/wiki/Santika_Club_fire\" title=\"Santika Club fire\">nightclub fire</a> in <a href=\"https://wikipedia.org/wiki/Bangkok\" title=\"Bangkok\">Bangkok, Thailand</a>.", "links": [{"title": "Santika Club fire", "link": "https://wikipedia.org/wiki/Santika_Club_fire"}, {"title": "Bangkok", "link": "https://wikipedia.org/wiki/Bangkok"}]}, {"year": "2010", "text": "A suicide car bomber detonates at a volleyball tournament in Lakki Marwat, Pakistan, killing 105 and injuring 100 more.", "html": "2010 - A suicide car bomber <a href=\"https://wikipedia.org/wiki/2010_La<PERSON><PERSON>_Mar<PERSON>_suicide_bombing\" title=\"2010 Lak<PERSON> Mar<PERSON> suicide bombing\">detonates at a volleyball tournament</a> in Lakki Marwat, Pakistan, killing 105 and injuring 100 more.", "no_year_html": "A suicide car bomber <a href=\"https://wikipedia.org/wiki/2010_Lak<PERSON>_Mar<PERSON>_suicide_bombing\" title=\"2010 Lak<PERSON> suicide bombing\">detonates at a volleyball tournament</a> in Lakki Marwat, Pakistan, killing 105 and injuring 100 more.", "links": [{"title": "2010 <PERSON><PERSON><PERSON> suicide bombing", "link": "https://wikipedia.org/wiki/2010_<PERSON><PERSON><PERSON>_<PERSON><PERSON>_suicide_bombing"}]}, {"year": "2011", "text": "A bomb explodes as Coptic Christians in Alexandria, Egypt, leave a new year service, killing 23 people.", "html": "2011 - A bomb <a href=\"https://wikipedia.org/wiki/2011_Alexandria_bombing\" title=\"2011 Alexandria bombing\">explodes</a> as <a href=\"https://wikipedia.org/wiki/Copts\" title=\"Copts\">Coptic Christians</a> in <a href=\"https://wikipedia.org/wiki/Alexandria\" title=\"Alexandria\">Alexandria</a>, <a href=\"https://wikipedia.org/wiki/Egypt\" title=\"Egypt\">Egypt</a>, leave a new year service, killing 23 people.", "no_year_html": "A bomb <a href=\"https://wikipedia.org/wiki/2011_Alexandria_bombing\" title=\"2011 Alexandria bombing\">explodes</a> as <a href=\"https://wikipedia.org/wiki/Copts\" title=\"Copts\">Coptic Christians</a> in <a href=\"https://wikipedia.org/wiki/Alexandria\" title=\"Alexandria\">Alexandria</a>, <a href=\"https://wikipedia.org/wiki/Egypt\" title=\"Egypt\">Egypt</a>, leave a new year service, killing 23 people.", "links": [{"title": "2011 Alexandria bombing", "link": "https://wikipedia.org/wiki/2011_Alexandria_bombing"}, {"title": "Copts", "link": "https://wikipedia.org/wiki/Copts"}, {"title": "Alexandria", "link": "https://wikipedia.org/wiki/Alexandria"}, {"title": "Egypt", "link": "https://wikipedia.org/wiki/Egypt"}]}, {"year": "2011", "text": "Estonia officially adopts the Euro currency and becomes the 17th Eurozone country.", "html": "2011 - <a href=\"https://wikipedia.org/wiki/Estonia\" title=\"Estonia\">Estonia</a> officially adopts the Euro currency and becomes the 17th <a href=\"https://wikipedia.org/wiki/Eurozone\" title=\"Eurozone\">Eurozone</a> country.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Estonia\" title=\"Estonia\">Estonia</a> officially adopts the Euro currency and becomes the 17th <a href=\"https://wikipedia.org/wiki/Eurozone\" title=\"Eurozone\">Eurozone</a> country.", "links": [{"title": "Estonia", "link": "https://wikipedia.org/wiki/Estonia"}, {"title": "Eurozone", "link": "https://wikipedia.org/wiki/Eurozone"}]}, {"year": "2013", "text": "At least 60 people are killed and 200 injured in a stampede after celebrations at Félix Houphouët-Boigny Stadium in Abidjan, Ivory Coast.", "html": "2013 - At least 60 people are killed and 200 injured in a <a href=\"https://wikipedia.org/wiki/2013_Houphou%C3%ABt-Boigny_stampede\" title=\"2013 Houphouët-Boigny stampede\">stampede</a> after celebrations at <a href=\"https://wikipedia.org/wiki/Stade_F%C3%A9lix_Houphou%C3%ABt-Boigny\" class=\"mw-redirect\" title=\"Stade Félix <PERSON>-Boigny\"><PERSON>igny Stadium</a> in <a href=\"https://wikipedia.org/wiki/Abidjan\" title=\"Abidjan\">Abidjan</a>, <a href=\"https://wikipedia.org/wiki/Ivory_Coast\" title=\"Ivory Coast\">Ivory Coast</a>.", "no_year_html": "At least 60 people are killed and 200 injured in a <a href=\"https://wikipedia.org/wiki/2013_Houphou%C3%ABt-Boigny_stampede\" title=\"2013 Houphouët-Boigny stampede\">stampede</a> after celebrations at <a href=\"https://wikipedia.org/wiki/Stade_F%C3%A9lix_Houphou%C3%ABt-Boigny\" class=\"mw-redirect\" title=\"Stade Félix Ho<PERSON>hou<PERSON>-Boigny\"><PERSON>-<PERSON>igny Stadium</a> in <a href=\"https://wikipedia.org/wiki/Abidjan\" title=\"Abidjan\">Abidjan</a>, <a href=\"https://wikipedia.org/wiki/Ivory_Coast\" title=\"Ivory Coast\">Ivory Coast</a>.", "links": [{"title": "2013 Houphouët-<PERSON><PERSON><PERSON> stampede", "link": "https://wikipedia.org/wiki/2013_Houphou%C3%ABt-Boigny_stampede"}, {"title": "Stade Félix <PERSON>-Boigny", "link": "https://wikipedia.org/wiki/Stade_F%C3%A9lix_Ho<PERSON>hou%C3%ABt-Boigny"}, {"title": "Abidjan", "link": "https://wikipedia.org/wiki/Abidjan"}, {"title": "Ivory Coast", "link": "https://wikipedia.org/wiki/Ivory_Coast"}]}, {"year": "2015", "text": "The Eurasian Economic Union comes into effect, creating a political and economic union between Russia, Belarus, Armenia, Kazakhstan and Kyrgyzstan.", "html": "2015 - The <a href=\"https://wikipedia.org/wiki/Eurasian_Economic_Union\" title=\"Eurasian Economic Union\">Eurasian Economic Union</a> comes into effect, creating a political and economic union between Russia, Belarus, Armenia, Kazakhstan and Kyrgyzstan.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Eurasian_Economic_Union\" title=\"Eurasian Economic Union\">Eurasian Economic Union</a> comes into effect, creating a political and economic union between Russia, Belarus, Armenia, Kazakhstan and Kyrgyzstan.", "links": [{"title": "Eurasian Economic Union", "link": "https://wikipedia.org/wiki/Eurasian_Economic_Union"}]}, {"year": "2017", "text": "An attack on a nightclub in Istanbul, Turkey, during New Year's celebrations, kills 39 people and injures 79 others.", "html": "2017 - An <a href=\"https://wikipedia.org/wiki/Istanbul_nightclub_shooting\" title=\"Istanbul nightclub shooting\">attack on a nightclub</a> in <a href=\"https://wikipedia.org/wiki/Istanbul\" title=\"Istanbul\">Istanbul</a>, <a href=\"https://wikipedia.org/wiki/Turkey\" title=\"Turkey\">Turkey</a>, during New Year's celebrations, kills 39 people and injures 79 others.", "no_year_html": "An <a href=\"https://wikipedia.org/wiki/Istanbul_nightclub_shooting\" title=\"Istanbul nightclub shooting\">attack on a nightclub</a> in <a href=\"https://wikipedia.org/wiki/Istanbul\" title=\"Istanbul\">Istanbul</a>, <a href=\"https://wikipedia.org/wiki/Turkey\" title=\"Turkey\">Turkey</a>, during New Year's celebrations, kills 39 people and injures 79 others.", "links": [{"title": "Istanbul nightclub shooting", "link": "https://wikipedia.org/wiki/Istanbul_nightclub_shooting"}, {"title": "Istanbul", "link": "https://wikipedia.org/wiki/Istanbul"}, {"title": "Turkey", "link": "https://wikipedia.org/wiki/Turkey"}]}, {"year": "2023", "text": "Croatia officially adopts the Euro, becoming the 20th Eurozone country, and becomes the 27th member of the Schengen Area.", "html": "2023 - <a href=\"https://wikipedia.org/wiki/Croatia\" title=\"Croatia\">Croatia</a> officially adopts the Euro, becoming the 20th Eurozone country, and becomes the 27th member of the <a href=\"https://wikipedia.org/wiki/Schengen_Area\" title=\"Schengen Area\">Schengen Area</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Croatia\" title=\"Croatia\">Croatia</a> officially adopts the Euro, becoming the 20th Eurozone country, and becomes the 27th member of the <a href=\"https://wikipedia.org/wiki/Schengen_Area\" title=\"Schengen Area\">Schengen Area</a>.", "links": [{"title": "Croatia", "link": "https://wikipedia.org/wiki/Croatia"}, {"title": "Schengen Area", "link": "https://wikipedia.org/wiki/Schengen_Area"}]}, {"year": "2024", "text": "A 7.5 Mww  earthquake strikes the western coast of Japan, killing more than 500 people and injuring over 1,000 others. A majority of direct deaths were due to collapsed homes.", "html": "2024 - A 7.5 M<sub>ww</sub>  <a href=\"https://wikipedia.org/wiki/2024_Noto_earthquake\" title=\"2024 Noto earthquake\">earthquake strikes</a> the western coast of <a href=\"https://wikipedia.org/wiki/Japan\" title=\"Japan\">Japan</a>, killing more than 500 people and injuring over 1,000 others. A majority of direct deaths were due to collapsed homes.", "no_year_html": "A 7.5 M<sub>ww</sub>  <a href=\"https://wikipedia.org/wiki/2024_Noto_earthquake\" title=\"2024 Noto earthquake\">earthquake strikes</a> the western coast of <a href=\"https://wikipedia.org/wiki/Japan\" title=\"Japan\">Japan</a>, killing more than 500 people and injuring over 1,000 others. A majority of direct deaths were due to collapsed homes.", "links": [{"title": "2024 Noto earthquake", "link": "https://wikipedia.org/wiki/2024_Noto_earthquake"}, {"title": "Japan", "link": "https://wikipedia.org/wiki/Japan"}]}, {"year": "2024", "text": "Disney's copyright protection on Steam<PERSON> Willie and the original Mickey Mouse expires as they enter the public domain.", "html": "2024 - Disney's copyright protection on <i><a href=\"https://wikipedia.org/wiki/Steamboat_Willie\" title=\"Steamboat Willie\">Steamboat Willie</a></i> and the original <a href=\"https://wikipedia.org/wiki/<PERSON>_Mouse\" title=\"<PERSON> Mouse\"><PERSON></a> expires as they <a href=\"https://wikipedia.org/wiki/2024_in_public_domain\" title=\"2024 in public domain\">enter the public domain</a>.", "no_year_html": "Disney's copyright protection on <i><a href=\"https://wikipedia.org/wiki/Steamboat_Willie\" title=\"Steamboat Willie\">Steamboat Willie</a></i> and the original <a href=\"https://wikipedia.org/wiki/<PERSON>_Mouse\" title=\"<PERSON> Mouse\"><PERSON></a> expires as they <a href=\"https://wikipedia.org/wiki/2024_in_public_domain\" title=\"2024 in public domain\">enter the public domain</a>.", "links": [{"title": "Steamboat Willie", "link": "https://wikipedia.org/wiki/Steamboat_Willie"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "2024 in public domain", "link": "https://wikipedia.org/wiki/2024_in_public_domain"}]}, {"year": "2024", "text": "Artsakh ceases to exist.", "html": "2024 - <a href=\"https://wikipedia.org/wiki/Republic_of_Artsakh\" title=\"Republic of Artsakh\">Artsakh</a> ceases to exist.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Republic_of_Artsakh\" title=\"Republic of Artsakh\">Artsakh</a> ceases to exist.", "links": [{"title": "Republic of Artsakh", "link": "https://wikipedia.org/wiki/Republic_of_Artsakh"}]}, {"year": "2025", "text": "14 people are killed and 35 others injured during a vehicle-ramming and shooting attack in New Orleans, Louisiana.", "html": "2025 - 14 people are killed and 35 others injured during a <a href=\"https://wikipedia.org/wiki/2025_New_Orleans_truck_attack\" title=\"2025 New Orleans truck attack\">vehicle-ramming and shooting attack</a> in <a href=\"https://wikipedia.org/wiki/New_Orleans\" title=\"New Orleans\">New Orleans</a>, <a href=\"https://wikipedia.org/wiki/Louisiana\" title=\"Louisiana\">Louisiana</a>.", "no_year_html": "14 people are killed and 35 others injured during a <a href=\"https://wikipedia.org/wiki/2025_New_Orleans_truck_attack\" title=\"2025 New Orleans truck attack\">vehicle-ramming and shooting attack</a> in <a href=\"https://wikipedia.org/wiki/New_Orleans\" title=\"New Orleans\">New Orleans</a>, <a href=\"https://wikipedia.org/wiki/Louisiana\" title=\"Louisiana\">Louisiana</a>.", "links": [{"title": "2025 New Orleans truck attack", "link": "https://wikipedia.org/wiki/2025_New_Orleans_truck_attack"}, {"title": "New Orleans", "link": "https://wikipedia.org/wiki/New_Orleans"}, {"title": "Louisiana", "link": "https://wikipedia.org/wiki/Louisiana"}]}], "Births": [{"year": "766", "text": "<PERSON>, 8th Imam of Twelver Shia Islam (d. 818)", "html": "766 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>R<PERSON>ha\"><PERSON></a>, 8th <a href=\"https://wikipedia.org/wiki/Imamate_(Twelver_doctrine)\" class=\"mw-redirect\" title=\"Imamate (Twelver doctrine)\">Imam</a> of <a href=\"https://wikipedia.org/wiki/Twelver\" class=\"mw-redirect\" title=\"Twelver\">Twelver</a> <a href=\"https://wikipedia.org/wiki/Shia_Islam\" title=\"Shia Islam\">Shia Islam</a> (d. 818)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" class=\"mw-redirect\" title=\"<PERSON> al<PERSON>Ridha\"><PERSON></a>, 8th <a href=\"https://wikipedia.org/wiki/Imamate_(Twelver_doctrine)\" class=\"mw-redirect\" title=\"Imamate (Twelver doctrine)\">Imam</a> of <a href=\"https://wikipedia.org/wiki/Twelver\" class=\"mw-redirect\" title=\"Twelver\">Twelver</a> <a href=\"https://wikipedia.org/wiki/Shia_Islam\" title=\"Shia Islam\">Shia Islam</a> (d. 818)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Imamate (Twelver doctrine)", "link": "https://wikipedia.org/wiki/Imamate_(Twelver_doctrine)"}, {"title": "Twelver", "link": "https://wikipedia.org/wiki/Twelver"}, {"title": "Shia Islam", "link": "https://wikipedia.org/wiki/Shia_Islam"}]}, {"year": "1431", "text": "<PERSON> (d. 1503)", "html": "1431 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_VI\" title=\"Pope Alexander VI\">Pope <PERSON> VI</a> (d. 1503)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_VI\" title=\"<PERSON> Alexander VI\">Pope <PERSON> VI</a> (d. 1503)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1449", "text": "<PERSON>, Italian politician (d. 1492)", "html": "1449 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%27_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian politician (d. 1492)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%27_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian politician (d. 1492)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>%27_Medici"}]}, {"year": "1467", "text": "<PERSON><PERSON><PERSON> the <PERSON>, Polish king (d. 1548)", "html": "1467 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_I_the_Old\" title=\"<PERSON><PERSON><PERSON> I the Old\"><PERSON><PERSON><PERSON> <PERSON> the Old</a>, Polish king (d. 1548)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_I_the_Old\" title=\"<PERSON><PERSON><PERSON> I the Old\"><PERSON><PERSON><PERSON> <PERSON> the Old</a>, Polish king (d. 1548)", "links": [{"title": "<PERSON><PERSON><PERSON> the <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_the_Old"}]}, {"year": "1484", "text": "<PERSON><PERSON><PERSON><PERSON>, Swiss pastor and theologian (d. 1531)", "html": "1484 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Swiss pastor and theologian (d. 1531)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Swiss pastor and theologian (d. 1531)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1511", "text": "<PERSON>, Duke of Cornwall, first-born child of <PERSON> of England (d. 1511)", "html": "1511 - <a href=\"https://wikipedia.org/wiki/<PERSON>,_Duke_of_Cornwall\" title=\"<PERSON>, Duke of Cornwall\"><PERSON>, Duke of Cornwall</a>, first-born child of <a href=\"https://wikipedia.org/wiki/<PERSON>_VIII_of_England\" class=\"mw-redirect\" title=\"<PERSON> VIII of England\"><PERSON> VIII of England</a> (d. 1511)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>,_Duke_of_Cornwall\" title=\"<PERSON>, Duke of Cornwall\"><PERSON>, Duke of Cornwall</a>, first-born child of <a href=\"https://wikipedia.org/wiki/<PERSON>_VIII_of_England\" class=\"mw-redirect\" title=\"<PERSON> VIII of England\"><PERSON> VIII of England</a> (d. 1511)", "links": [{"title": "<PERSON>, Duke of Cornwall", "link": "https://wikipedia.org/wiki/<PERSON>,_Duke_of_Cornwall"}, {"title": "<PERSON> of England", "link": "https://wikipedia.org/wiki/Henry_VIII_of_England"}]}, {"year": "1557", "text": "<PERSON>, Prince of Transylvania (d. 1606)", "html": "1557 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, Prince of Transylvania (d. 1606)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, Prince of Transylvania (d. 1606)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1600", "text": "<PERSON>, Dutch theologian and academic (d. 1649)", "html": "1600 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Friedrich <PERSON>\"><PERSON></a>, Dutch theologian and academic (d. 1649)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Friedrich <PERSON>\"><PERSON></a>, Dutch theologian and academic (d. 1649)", "links": [{"title": "Friedrich Spanheim", "link": "https://wikipedia.org/wiki/Friedrich_Spanheim"}]}, {"year": "1628", "text": "<PERSON>, German composer and theorist (d. 1692)", "html": "1628 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German composer and theorist (d. 1692)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German composer and theorist (d. 1692)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1655", "text": "<PERSON>, German jurist and philosopher (d. 1728)", "html": "1655 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German jurist and philosopher (d. 1728)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German jurist and philosopher (d. 1728)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1684", "text": "<PERSON>, Dutch scholar and author (d. 1748)", "html": "1684 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch scholar and author (d. 1748)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch scholar and author (d. 1748)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1704", "text": "<PERSON><PERSON>, English author, poet, and politician (d. 1787)", "html": "1704 - <a href=\"https://wikipedia.org/wiki/Soame_<PERSON>\" title=\"Soame Jenyn<PERSON>\"><PERSON><PERSON></a>, English author, poet, and politician (d. 1787)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/So<PERSON>_<PERSON>\" title=\"Soame <PERSON>\"><PERSON><PERSON></a>, English author, poet, and politician (d. 1787)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/So<PERSON>_<PERSON>s"}]}, {"year": "1711", "text": "Baron <PERSON>, Austrian soldier (d. 1749)", "html": "1711 - <a href=\"https://wikipedia.org/wiki/Baron_<PERSON>_<PERSON>_<PERSON>_<PERSON>\" title=\"Baron <PERSON>\">Baron <PERSON></a>, Austrian soldier (d. 1749)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Baron_<PERSON>_<PERSON>_<PERSON>_<PERSON>\" title=\"Baron <PERSON>\">Baron <PERSON></a>, Austrian soldier (d. 1749)", "links": [{"title": "<PERSON> <PERSON>", "link": "https://wikipedia.org/wiki/Baron_<PERSON>_<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1714", "text": "<PERSON>, Italian soprano and author (d. 1800)", "html": "1714 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian soprano and author (d. 1800)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian soprano and author (d. 1800)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1714", "text": "<PERSON><PERSON><PERSON><PERSON>, Lithuanian pastor and poet (d. 1780)", "html": "1714 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Lithuanian pastor and poet (d. 1780)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Lithuanian pastor and poet (d. 1780)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Kristijon<PERSON>_<PERSON>"}]}, {"year": "1735", "text": "<PERSON>, American silversmith and engraver (d. 1818)", "html": "1735 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American silversmith and engraver (d. 1818)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American silversmith and engraver (d. 1818)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1745", "text": "<PERSON>, American general and politician (d. 1796)", "html": "1745 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American general and politician (d. 1796)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American general and politician (d. 1796)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1752", "text": "<PERSON>, American seamstress, sewed flags for the Pennsylvania Navy during the Revolutionary War (d. 1836)", "html": "1752 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American seamstress, sewed flags for the Pennsylvania Navy during the Revolutionary War (d. 1836)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American seamstress, sewed flags for the Pennsylvania Navy during the Revolutionary War (d. 1836)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1768", "text": "<PERSON>, Anglo-Irish author (d. 1849)", "html": "1768 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Anglo-Irish author (d. 1849)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Anglo-Irish author (d. 1849)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Maria_<PERSON>"}]}, {"year": "1769", "text": "<PERSON><PERSON><PERSON>, French obstetrician (d. 1821)", "html": "1769 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, French <a href=\"https://wikipedia.org/wiki/Obstetrician\" class=\"mw-redirect\" title=\"Obstetrician\">obstetrician</a> (d. 1821)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, French <a href=\"https://wikipedia.org/wiki/Obstetrician\" class=\"mw-redirect\" title=\"Obstetrician\">obstetrician</a> (d. 1821)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>"}, {"title": "Obstetrician", "link": "https://wikipedia.org/wiki/Obstetrician"}]}, {"year": "1774", "text": "<PERSON>, French zoologist and academic (d. 1860)", "html": "1774 - <a href=\"https://wikipedia.org/wiki/Andr%C3%A9_<PERSON>_<PERSON>_Du<PERSON>%C3%A9ril\" title=\"<PERSON>\"><PERSON></a>, French zoologist and academic (d. 1860)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Andr%C3%A9_<PERSON>_<PERSON>_Du<PERSON>%C3%A9ril\" title=\"<PERSON>\"><PERSON></a>, French zoologist and academic (d. 1860)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Andr%C3%A9_<PERSON>_<PERSON>_Dum%C3%A9ril"}]}, {"year": "1779", "text": "<PERSON>, English publisher (d. 1847)", "html": "1779 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(printer)\" title=\"<PERSON> (printer)\"><PERSON></a>, English publisher (d. 1847)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(printer)\" title=\"<PERSON> (printer)\"><PERSON></a>, English publisher (d. 1847)", "links": [{"title": "<PERSON> (printer)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(printer)"}]}, {"year": "1803", "text": "<PERSON>, American politician and father of poet <PERSON> (d. 1874)", "html": "1803 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American politician and father of poet <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> (d. 1874)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American politician and father of poet <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> (d. 1874)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1806", "text": "<PERSON>, Estonian-French chess player (d. 1853)", "html": "1806 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Estonian-French chess player (d. 1853)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Estonian-French chess player (d. 1853)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1809", "text": "<PERSON><PERSON><PERSON>, French lawyer and entomologist (d. 1880)", "html": "1809 - <a href=\"https://wikipedia.org/wiki/A<PERSON><PERSON>_<PERSON>%C3%A9e\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, French lawyer and entomologist (d. 1880)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>%C3%A9e\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, French lawyer and entomologist (d. 1880)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/A<PERSON><PERSON>_<PERSON>uen%C3%A9e"}]}, {"year": "1813", "text": "<PERSON>, American politician (d. 1868)", "html": "1813 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(Congressman)\" title=\"<PERSON> (Congressman)\"><PERSON></a>, American politician (d. 1868)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(Congressman)\" title=\"<PERSON> (Congressman)\"><PERSON></a>, American politician (d. 1868)", "links": [{"title": "<PERSON> (Congressman)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(Congressman)"}]}, {"year": "1814", "text": "<PERSON>, Chinese rebellion leader and king (d. 1864)", "html": "1814 - <a href=\"https://wikipedia.org/wiki/Hong_Xi<PERSON>n\" title=\"<PERSON>\"><PERSON></a>, Chinese rebellion leader and king (d. 1864)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Hong_Xi<PERSON>n\" title=\"<PERSON>\"><PERSON></a>, Chinese rebellion leader and king (d. 1864)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Hong_<PERSON>"}]}, {"year": "1818", "text": "<PERSON>, Irish-born American general (d. 1866)", "html": "1818 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(general)\" title=\"<PERSON> (general)\"><PERSON></a>, Irish-born American general (d. 1866)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(general)\" title=\"<PERSON> (general)\"><PERSON></a>, Irish-born American general (d. 1866)", "links": [{"title": "<PERSON> (general)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(general)"}]}, {"year": "1819", "text": "<PERSON>, English-Italian poet and academic (d. 1861)", "html": "1819 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English-Italian poet and academic (d. 1861)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English-Italian poet and academic (d. 1861)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1819", "text": "<PERSON>, American general (d. 1878)", "html": "1819 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_(judge)\" title=\"<PERSON> (judge)\"><PERSON></a>, American general (d. 1878)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_(judge)\" title=\"<PERSON> (judge)\"><PERSON></a>, American general (d. 1878)", "links": [{"title": "<PERSON> (judge)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_(judge)"}]}, {"year": "1823", "text": "<PERSON><PERSON><PERSON>, Hungarian poet and activist (d. 1849)", "html": "1823 - <a href=\"https://wikipedia.org/wiki/S%C3%A1ndor_Pet%C5%91fi\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Hungarian poet and activist (d. 1849)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/S%C3%A1ndor_Pet%C5%91fi\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Hungarian poet and activist (d. 1849)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/S%C3%A1ndor_Pet%C5%91fi"}]}, {"year": "1833", "text": "<PERSON>, Scottish-New Zealand architect, designed the Otago Boys' High School and Knox Church (d. 1902)", "html": "1833 - <a href=\"https://wikipedia.org/wiki/<PERSON>(architect)\" title=\"<PERSON> (architect)\"><PERSON></a>, Scottish-New Zealand architect, designed the <a href=\"https://wikipedia.org/wiki/Otago_Boys%27_High_School\" title=\"Otago Boys' High School\">Otago Boys' High School</a> and <a href=\"https://wikipedia.org/wiki/Knox_Church,_Dunedin\" title=\"Knox Church, Dunedin\">Knox Church</a> (d. 1902)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>(architect)\" title=\"<PERSON> (architect)\"><PERSON></a>, Scottish-New Zealand architect, designed the <a href=\"https://wikipedia.org/wiki/Otago_Boys%27_High_School\" title=\"Otago Boys' High School\">Otago Boys' High School</a> and <a href=\"https://wikipedia.org/wiki/Knox_Church,_Dunedin\" title=\"Knox Church, Dunedin\">Knox Church</a> (d. 1902)", "links": [{"title": "<PERSON> (architect)", "link": "https://wikipedia.org/wiki/<PERSON>_(architect)"}, {"title": "Otago Boys' High School", "link": "https://wikipedia.org/wiki/Otago_Boys%27_High_School"}, {"title": "Knox Church, Dunedin", "link": "https://wikipedia.org/wiki/Knox_Church,_Dunedin"}]}, {"year": "1834", "text": "<PERSON><PERSON><PERSON>, French author and playwright (d. 1908)", "html": "1834 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>%C3%A9vy\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, French author and playwright (d. 1908)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>%C3%A9vy\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, French author and playwright (d. 1908)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_Hal%C3%A9vy"}]}, {"year": "1839", "text": "<PERSON><PERSON><PERSON>, English-Italian author and activist (d. 1908)", "html": "1839 - <a href=\"https://wikipedia.org/wiki/<PERSON>uida\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, English-Italian author and activist (d. 1908)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, English-Italian author and activist (d. 1908)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Ouida"}]}, {"year": "1848", "text": "<PERSON>, Irish-American lawyer and politician (d. 1924)", "html": "1848 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Irish-American lawyer and politician (d. 1924)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Irish-American lawyer and politician (d. 1924)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "1852", "text": "<PERSON><PERSON><PERSON><PERSON><PERSON>, French chemist and academic (d. 1904)", "html": "1852 - <a href=\"https://wikipedia.org/wiki/Eug%C3%A8<PERSON>-<PERSON><PERSON><PERSON>_<PERSON>%C3%A7ay\" title=\"<PERSON><PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON><PERSON></a>, French chemist and academic (d. 1904)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Eug%C3%A8ne-<PERSON><PERSON><PERSON>_Demar%C3%A7ay\" title=\"<PERSON><PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON><PERSON></a>, French chemist and academic (d. 1904)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Eug%C3%A8ne-Anatole_Demar%C3%A7ay"}]}, {"year": "1854", "text": "<PERSON>, Scottish anthropologist and academic (d. 1941)", "html": "1854 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish anthropologist and academic (d. 1941)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish anthropologist and academic (d. 1941)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1854", "text": "<PERSON>, Irish-Australian politician, 15th Premier of New South Wales (d. 1940)", "html": "1854 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Irish-Australian politician, 15th <a href=\"https://wikipedia.org/wiki/Premier_of_New_South_Wales\" title=\"Premier of New South Wales\">Premier of New South Wales</a> (d. 1940)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Irish-Australian politician, 15th <a href=\"https://wikipedia.org/wiki/Premier_of_New_South_Wales\" title=\"Premier of New South Wales\">Premier of New South Wales</a> (d. 1940)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Premier of New South Wales", "link": "https://wikipedia.org/wiki/Premier_of_New_South_Wales"}]}, {"year": "1857", "text": "<PERSON>, American baseball player (d. 1933)", "html": "1857 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player (d. 1933)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player (d. 1933)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1858", "text": "<PERSON>, Kraków-born painter (d. 1942)", "html": "1858 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Kraków-born painter (d. 1942)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Kraków-born painter (d. 1942)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1859", "text": "<PERSON>, American inventor (d. 1923)", "html": "1859 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American inventor (d. 1923)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American inventor (d. 1923)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1859", "text": "<PERSON><PERSON><PERSON><PERSON>, Burmese king (d. 1916)", "html": "1859 - <a href=\"https://wikipedia.org/wiki/Thi<PERSON><PERSON>_<PERSON>\" title=\"Thibaw Min\"><PERSON><PERSON><PERSON><PERSON></a>, Burmese king (d. 1916)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Thiba<PERSON>_<PERSON>\" title=\"Thibaw Min\"><PERSON><PERSON><PERSON><PERSON> <PERSON></a>, Burmese king (d. 1916)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Thibaw_<PERSON>"}]}, {"year": "1860", "text": "<PERSON>, Italian cardinal (d. 1935)", "html": "1860 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian cardinal (d. 1935)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian cardinal (d. 1935)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1863", "text": "<PERSON>, French historian and educator, founded the International Olympic Committee (d. 1937)", "html": "1863 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French historian and educator, founded the <a href=\"https://wikipedia.org/wiki/International_Olympic_Committee\" title=\"International Olympic Committee\">International Olympic Committee</a> (d. 1937)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French historian and educator, founded the <a href=\"https://wikipedia.org/wiki/International_Olympic_Committee\" title=\"International Olympic Committee\">International Olympic Committee</a> (d. 1937)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "International Olympic Committee", "link": "https://wikipedia.org/wiki/International_Olympic_Committee"}]}, {"year": "1864", "text": "<PERSON>, American photographer and curator (d. 1946)", "html": "1864 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American photographer and curator (d. 1946)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American photographer and curator (d. 1946)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1864", "text": "<PERSON>, Chinese painter (d. 1957)", "html": "1864 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Chinese painter (d. 1957)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Chinese painter (d. 1957)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1867", "text": "<PERSON>, English astronomer and scholar (d. 1949)", "html": "1867 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English astronomer and scholar (d. 1949)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English astronomer and scholar (d. 1949)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1874", "text": "<PERSON>, American publisher and politician, 46th United States Secretary of the Navy (d. 1944)", "html": "1874 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American publisher and politician, 46th <a href=\"https://wikipedia.org/wiki/United_States_Secretary_of_the_Navy\" title=\"United States Secretary of the Navy\">United States Secretary of the Navy</a> (d. 1944)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American publisher and politician, 46th <a href=\"https://wikipedia.org/wiki/United_States_Secretary_of_the_Navy\" title=\"United States Secretary of the Navy\">United States Secretary of the Navy</a> (d. 1944)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "United States Secretary of the Navy", "link": "https://wikipedia.org/wiki/United_States_Secretary_of_the_Navy"}]}, {"year": "1874", "text": "<PERSON><PERSON>, German-American pilot and engineer (d. 1927)", "html": "1874 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, German-American pilot and engineer (d. 1927)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, German-American pilot and engineer (d. 1927)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1877", "text": "<PERSON>, German sinologist and orientalist (d. 1937)", "html": "1877 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>%C3%ABl-Holstein\" title=\"<PERSON>\"><PERSON></a>, German sinologist and orientalist (d. 1937)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>%C3%ABl-Holstein\" title=\"<PERSON>\"><PERSON></a>, German sinologist and orientalist (d. 1937)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>%C3%ABl-Holstein"}]}, {"year": "1878", "text": "<PERSON><PERSON><PERSON>, Danish mathematician, statistician, and engineer (d. 1929)", "html": "1878 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Danish mathematician, statistician, and engineer (d. 1929)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Danish mathematician, statistician, and engineer (d. 1929)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/A<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "1879", "text": "<PERSON><PERSON> <PERSON><PERSON>, English author and playwright (d. 1970)", "html": "1879 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> <PERSON><PERSON>\"><PERSON><PERSON> <PERSON><PERSON></a>, English author and playwright (d. 1970)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> <PERSON><PERSON>\"><PERSON><PERSON> <PERSON><PERSON></a>, English author and playwright (d. 1970)", "links": [{"title": "<PERSON><PERSON> <PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "1879", "text": "<PERSON>, Hungarian-American screenwriter and producer, founded the Fox Film Corporation and Fox Theatres (d. 1952)", "html": "1879 - <a href=\"https://wikipedia.org/wiki/<PERSON>(producer)\" title=\"<PERSON> (producer)\"><PERSON></a>, Hungarian-American screenwriter and producer, founded the <a href=\"https://wikipedia.org/wiki/Fox_Film_Corporation\" class=\"mw-redirect\" title=\"Fox Film Corporation\">Fox Film Corporation</a> and <a href=\"https://wikipedia.org/wiki/Fox_Theatres\" title=\"Fox Theatres\">Fox Theatres</a> (d. 1952)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>(producer)\" title=\"<PERSON> (producer)\"><PERSON></a>, Hungarian-American screenwriter and producer, founded the <a href=\"https://wikipedia.org/wiki/Fox_Film_Corporation\" class=\"mw-redirect\" title=\"Fox Film Corporation\">Fox Film Corporation</a> and <a href=\"https://wikipedia.org/wiki/Fox_Theatres\" title=\"Fox Theatres\">Fox Theatres</a> (d. 1952)", "links": [{"title": "<PERSON> (producer)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(producer)"}, {"title": "Fox Film Corporation", "link": "https://wikipedia.org/wiki/Fox_Film_Corporation"}, {"title": "Fox Theatres", "link": "https://wikipedia.org/wiki/Fox_Theatres"}]}, {"year": "1883", "text": "<PERSON>, American general, lawyer, and politician (d. 1959)", "html": "1883 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American general, lawyer, and politician (d. 1959)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American general, lawyer, and politician (d. 1959)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1883", "text": "<PERSON><PERSON>, Georgian Social Democrat politician (d. 1924)", "html": "1883 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>hom<PERSON>\"><PERSON><PERSON></a>, Georgian Social Democrat politician (d. 1924)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>hom<PERSON>\"><PERSON><PERSON></a>, Georgian Social Democrat politician (d. 1924)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>ki"}]}, {"year": "1884", "text": "<PERSON><PERSON><PERSON><PERSON>, Japanese lieutenant, engineer, and politician, founded Nakajima Aircraft Company (d. 1949)", "html": "1884 - <a href=\"https://wikipedia.org/wiki/Chikuhei_Nakajima\" title=\"Chi<PERSON>he<PERSON> Na<PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Japanese lieutenant, engineer, and politician, founded <a href=\"https://wikipedia.org/wiki/Nakajima_Aircraft_Company\" title=\"Nakajima Aircraft Company\">Nakajima Aircraft Company</a> (d. 1949)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Chikuhei_Nakajima\" title=\"<PERSON><PERSON>he<PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Japanese lieutenant, engineer, and politician, founded <a href=\"https://wikipedia.org/wiki/Nakajima_Aircraft_Company\" title=\"Nakajima Aircraft Company\">Nakajima Aircraft Company</a> (d. 1949)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>"}, {"title": "Nakajima Aircraft Company", "link": "https://wikipedia.org/wiki/Nakajima_Aircraft_Company"}]}, {"year": "1887", "text": "<PERSON>, German admiral (d. 1945)", "html": "1887 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German admiral (d. 1945)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German admiral (d. 1945)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1888", "text": "<PERSON><PERSON><PERSON>, American painter, designer, and illustrator", "html": "1888 - <a href=\"https://wikipedia.org/wiki/Ches<PERSON>_Bonestell\" title=\"Ch<PERSON><PERSON> Bonestell\"><PERSON><PERSON><PERSON></a>, American painter, designer, and illustrator", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>tell\" title=\"Ches<PERSON> Bonestell\"><PERSON><PERSON><PERSON></a>, American painter, designer, and illustrator", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_Bonestell"}]}, {"year": "1888", "text": "<PERSON>, Canadian-American engineer, designed the M1 Garand rifle (d. 1974)", "html": "1888 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian-American engineer, designed the <a href=\"https://wikipedia.org/wiki/M1_Garand\" title=\"M1 Garand\">M1 Garand rifle</a> (d. 1974)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian-American engineer, designed the <a href=\"https://wikipedia.org/wiki/M1_Garand\" title=\"M1 Garand\">M1 Garand rifle</a> (d. 1974)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "M1 Garand", "link": "https://wikipedia.org/wiki/M1_Garand"}]}, {"year": "1888", "text": "<PERSON><PERSON>, Greek general (d. 1965)", "html": "1888 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Greek general (d. 1965)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Greek general (d. 1965)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Georgios_Stanotas"}]}, {"year": "1889", "text": "<PERSON>, American actor (d. 1967)", "html": "1889 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor (d. 1967)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor (d. 1967)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1889", "text": "<PERSON><PERSON>, American author (d. 1969)", "html": "1889 - <a href=\"https://wikipedia.org/wiki/Seabury_Quinn\" title=\"Seabury Quinn\"><PERSON><PERSON></a>, American author (d. 1969)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Seabury_Quinn\" title=\"Seabury Quinn\"><PERSON><PERSON></a>, American author (d. 1969)", "links": [{"title": "Seabury Quinn", "link": "https://wikipedia.org/wiki/Seabury_Quinn"}]}, {"year": "1890", "text": "<PERSON>, Slovenian geographer and academic (d. 1966)", "html": "1890 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Slovenian geographer and academic (d. 1966)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Slovenian geographer and academic (d. 1966)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1891", "text": "<PERSON><PERSON><PERSON><PERSON>, Indian educator and politician, 3rd Governor of Rajasthan (d. 1969)", "html": "1891 - <a href=\"https://wikipedia.org/wiki/Sampurnanand\" title=\"Sampurnanand\">Sampurnan<PERSON></a>, Indian educator and politician, 3rd <a href=\"https://wikipedia.org/wiki/List_of_Governors_of_Rajasthan\" class=\"mw-redirect\" title=\"List of Governors of Rajasthan\">Governor of Rajasthan</a> (d. 1969)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Sampurnanand\" title=\"Sampurnanand\">Sampurnanand</a>, Indian educator and politician, 3rd <a href=\"https://wikipedia.org/wiki/List_of_Governors_of_Rajasthan\" class=\"mw-redirect\" title=\"List of Governors of Rajasthan\">Governor of Rajasthan</a> (d. 1969)", "links": [{"title": "Sampurnanand", "link": "https://wikipedia.org/wiki/Sampurnanand"}, {"title": "List of Governors of Rajasthan", "link": "https://wikipedia.org/wiki/List_of_Governors_of_Rajasthan"}]}, {"year": "1892", "text": "<PERSON><PERSON><PERSON>, Indian author and activist (d. 1942)", "html": "1892 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Indian author and activist (d. 1942)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Indian author and activist (d. 1942)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1892", "text": "<PERSON><PERSON>, Polish-American conductor (d. 1958)", "html": "1892 - <a href=\"https://wikipedia.org/wiki/Artur_<PERSON>zi%C5%84ski\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Polish-American conductor (d. 1958)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Artur_<PERSON>%C5%84ski\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Polish-American conductor (d. 1958)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Artur_Rodzi%C5%84ski"}]}, {"year": "1892", "text": "<PERSON>, Filipino lawyer and politician, 5th President of the Philippines (d. 1948)", "html": "1892 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Filipino lawyer and politician, 5th <a href=\"https://wikipedia.org/wiki/President_of_the_Philippines\" title=\"President of the Philippines\">President of the Philippines</a> (d. 1948)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Filipino lawyer and politician, 5th <a href=\"https://wikipedia.org/wiki/President_of_the_Philippines\" title=\"President of the Philippines\">President of the Philippines</a> (d. 1948)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "President of the Philippines", "link": "https://wikipedia.org/wiki/President_of_the_Philippines"}]}, {"year": "1893", "text": "<PERSON><PERSON><PERSON><PERSON>, Greek colonel (d. 1940)", "html": "1893 - <a href=\"https://wikipedia.org/wiki/Mordechai_Frizis\" title=\"Mordechai Frizis\"><PERSON><PERSON><PERSON><PERSON></a>, Greek colonel (d. 1940)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Morde<PERSON>i_Frizis\" title=\"Mordechai Frizis\"><PERSON><PERSON><PERSON><PERSON></a>, Greek colonel (d. 1940)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Mordechai_Frizis"}]}, {"year": "1893", "text": "<PERSON><PERSON><PERSON>, American football player and coach (d. 1964)", "html": "1893 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American football player and coach (d. 1964)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American football player and coach (d. 1964)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1894", "text": "<PERSON><PERSON><PERSON>, Indian physicist and mathematician (d. 1974)", "html": "1894 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Indian physicist and mathematician (d. 1974)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Indian physicist and mathematician (d. 1974)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1894", "text": "<PERSON>, American clergyman (d. 1970)", "html": "1894 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American clergyman (d. 1970)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American clergyman (d. 1970)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1895", "text": "<PERSON><PERSON> <PERSON>, American law enforcement official; 1st Director of the Federal Bureau of Investigation (d. 1972)", "html": "1895 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American law enforcement official; 1st <a href=\"https://wikipedia.org/wiki/Director_of_the_Federal_Bureau_of_Investigation\" title=\"Director of the Federal Bureau of Investigation\">Director of the Federal Bureau of Investigation</a> (d. 1972)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American law enforcement official; 1st <a href=\"https://wikipedia.org/wiki/Director_of_the_Federal_Bureau_of_Investigation\" title=\"Director of the Federal Bureau of Investigation\">Director of the Federal Bureau of Investigation</a> (d. 1972)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>"}, {"title": "Director of the Federal Bureau of Investigation", "link": "https://wikipedia.org/wiki/Director_of_the_Federal_Bureau_of_Investigation"}]}, {"year": "1899", "text": "<PERSON><PERSON><PERSON>, centre-left Italian politician (d. 1991) ", "html": "1899 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, centre-left Italian politician (d. 1991) ", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, centre-left Italian politician (d. 1991) ", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1900", "text": "<PERSON><PERSON>, Japanese soldier and diplomat (d. 1986)", "html": "1900 - <a href=\"https://wikipedia.org/wiki/Chiune_Sugihara\" title=\"Chiune Sugihara\"><PERSON><PERSON></a>, Japanese soldier and diplomat (d. 1986)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Chiune_Sugihara\" title=\"<PERSON><PERSON> Sugihara\"><PERSON><PERSON></a>, Japanese soldier and diplomat (d. 1986)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Chiune_Sugihara"}]}, {"year": "1900", "text": "<PERSON>, Spanish-American singer-songwriter and actor (d. 1990)", "html": "1900 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Spanish-American singer-songwriter and actor (d. 1990)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Spanish-American singer-songwriter and actor (d. 1990)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Xavier_<PERSON>t"}]}, {"year": "1902", "text": "<PERSON>, Norwegian-South African cricketer and lawyer (d. 1977)", "html": "1902 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Norwegian-South African cricketer and lawyer (d. 1977)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Norwegian-South African cricketer and lawyer (d. 1977)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>n"}]}, {"year": "1902", "text": "<PERSON>, German jurist and political dissident (d. 1945)", "html": "1902 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>%C3%A1nyi\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, German jurist and political dissident (d. 1945)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>%C3%A1nyi\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, German jurist and political dissident (d. 1945)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>%C3%A1nyi"}]}, {"year": "1904", "text": "<PERSON><PERSON><PERSON>, Pakistani lawyer and politician, 5th President of Pakistan (d. 1982)", "html": "1904 - <a href=\"https://wikipedia.org/wiki/Fazal_Ilahi_Chaudhry\" title=\"Fazal Ilahi Chaudhry\"><PERSON><PERSON><PERSON></a>, Pakistani lawyer and politician, 5th <a href=\"https://wikipedia.org/wiki/President_of_Pakistan\" title=\"President of Pakistan\">President of Pakistan</a> (d. 1982)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Fazal_Ilahi_Chaudhry\" title=\"Fazal Ilahi Chaudhry\"><PERSON><PERSON><PERSON></a>, Pakistani lawyer and politician, 5th <a href=\"https://wikipedia.org/wiki/President_of_Pakistan\" title=\"President of Pakistan\">President of Pakistan</a> (d. 1982)", "links": [{"title": "Fazal Ilahi Chaudhry", "link": "https://wikipedia.org/wiki/Fazal_Ilahi_Chaudhry"}, {"title": "President of Pakistan", "link": "https://wikipedia.org/wiki/President_of_Pakistan"}]}, {"year": "1905", "text": "<PERSON><PERSON>, Ukrainian-Polish mathematician and theorist (d. 1981)", "html": "1905 - <a href=\"https://wikipedia.org/wiki/Stanis%C5%82aw_Mazur\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Ukrainian-Polish mathematician and theorist (d. 1981)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Stanis%C5%82aw_Mazur\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Ukrainian-Polish mathematician and theorist (d. 1981)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Stanis%C5%82aw_Mazur"}]}, {"year": "1905", "text": "<PERSON><PERSON>, Norwegian journalist and war correspondent (d. 1961)", "html": "1905 - <a href=\"https://wikipedia.org/wiki/Li<PERSON>_Lindb%C3%A6k\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Norwegian journalist and war correspondent (d. 1961)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Li<PERSON>_Lindb%C3%A6k\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Norwegian journalist and war correspondent (d. 1961)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Lise_Lindb%C3%A6k"}]}, {"year": "1906", "text": "<PERSON>, Filipino filmmaker and actor (d. 1988)", "html": "1906 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Filipino filmmaker and actor (d. 1988)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Filipino filmmaker and actor (d. 1988)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1907", "text": "<PERSON><PERSON><PERSON>, Japanese sprinter and long jumper (d. 1931)", "html": "1907 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese sprinter and long jumper (d. 1931)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese sprinter and long jumper (d. 1931)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON><PERSON>"}]}, {"year": "1909", "text": "<PERSON>, American actor (d. 1992)", "html": "1909 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor (d. 1992)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor (d. 1992)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1909", "text": "<PERSON><PERSON>, Ukrainian soldier and politician (d. 1959)", "html": "1909 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Ukrainian soldier and politician (d. 1959)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Ukrainian soldier and politician (d. 1959)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>era"}]}, {"year": "1909", "text": "<PERSON>, American-Russian journalist, author, and activist (d. 1993)", "html": "1909 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American-Russian journalist, author, and activist (d. 1993)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American-Russian journalist, author, and activist (d. 1993)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1911", "text": "<PERSON>, English director, producer, and screenwriter (d. 1971)", "html": "1911 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English director, producer, and screenwriter (d. 1971)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English director, producer, and screenwriter (d. 1971)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1911", "text": "<PERSON>, American baseball player (d. 1986)", "html": "1911 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player (d. 1986)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player (d. 1986)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1911", "text": "<PERSON>, Polish-American violinist and educator (d. 2012)", "html": "1911 - <a href=\"https://wikipedia.org/wiki/Roman_Totenberg\" title=\"Roman To<PERSON>berg\"><PERSON></a>, Polish-American violinist and educator (d. 2012)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Roman_Totenberg\" title=\"Roman To<PERSON>\"><PERSON></a>, Polish-American violinist and educator (d. 2012)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Roman_Totenberg"}]}, {"year": "1911", "text": "<PERSON>, American poet and author (d. 1960)", "html": "1911 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American poet and author (d. 1960)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American poet and author (d. 1960)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1912", "text": "<PERSON>, Russian mathematician and historian (d. 1995)", "html": "1912 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian mathematician and historian (d. 1995)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian mathematician and historian (d. 1995)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1912", "text": "<PERSON>, British spy (d. 1988)", "html": "1912 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, British spy (d. 1988)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, British spy (d. 1988)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1912", "text": "<PERSON><PERSON><PERSON><PERSON>, Greek poet and academic (d. 1991)", "html": "1912 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Greek poet and academic (d. 1991)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Greek poet and academic (d. 1991)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>if<PERSON>s_<PERSON>"}]}, {"year": "1914", "text": "<PERSON><PERSON>, British SOE agent (d. 1944)", "html": "1914 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, British <a href=\"https://wikipedia.org/wiki/Special_Operations_Executive\" title=\"Special Operations Executive\">SOE</a> agent (d. 1944)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, British <a href=\"https://wikipedia.org/wiki/Special_Operations_Executive\" title=\"Special Operations Executive\">SOE</a> agent (d. 1944)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON><PERSON>_<PERSON>"}, {"title": "Special Operations Executive", "link": "https://wikipedia.org/wiki/Special_Operations_Executive"}]}, {"year": "1917", "text": "<PERSON>, American actress and singer (d. 2016)", "html": "1917 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress and singer (d. 2016)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress and singer (d. 2016)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1918", "text": "<PERSON>, Scottish colonel, Victoria Cross recipient (d. 2000)", "html": "1918 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish colonel, <a href=\"https://wikipedia.org/wiki/Victoria_Cross\" title=\"Victoria Cross\"><PERSON> Cross</a> recipient (d. 2000)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish colonel, <a href=\"https://wikipedia.org/wiki/Victoria_Cross\" title=\"Victoria Cross\">Victoria Cross</a> recipient (d. 2000)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "Victoria Cross", "link": "https://wikipedia.org/wiki/Victoria_Cross"}]}, {"year": "1918", "text": "<PERSON>, Dutch swimmer (d. 1997)", "html": "1918 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch swimmer (d. 1997)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch swimmer (d. 1997)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1919", "text": "<PERSON>, American boxer and actor (d. 1990)", "html": "1919 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American boxer and actor (d. 1990)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American boxer and actor (d. 1990)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Rocky_Graziano"}]}, {"year": "1919", "text": "<PERSON>, American actress (d. 1948)", "html": "1919 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress (d. 1948)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress (d. 1948)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1919", "text": "<PERSON>, British actress, Emmerdale Farm (d. 2019)", "html": "1919 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, British actress, <i><a href=\"https://wikipedia.org/wiki/Emmerdale_Farm\" class=\"mw-redirect\" title=\"Emmerdale Farm\">Emmerdale Farm</a></i> (d. 2019)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, British actress, <i><a href=\"https://wikipedia.org/wiki/Emmerdale_Farm\" class=\"mw-redirect\" title=\"Emmerdale Farm\">Emmerdale Farm</a></i> (d. 2019)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Emmerdale Farm", "link": "https://wikipedia.org/wiki/Emmerdale_Farm"}]}, {"year": "1919", "text": "<PERSON>, American basketball player (d. 1997)", "html": "1919 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player (d. 1997)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player (d. 1997)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1919", "text": "<PERSON><PERSON> <PERSON><PERSON>, American soldier and author (d. 2010)", "html": "1919 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> <PERSON><PERSON>\"><PERSON><PERSON> <PERSON><PERSON></a>, American soldier and author (d. 2010)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> <PERSON><PERSON>\"><PERSON><PERSON> <PERSON><PERSON></a>, American soldier and author (d. 2010)", "links": [{"title": "<PERSON><PERSON> <PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "1919", "text": "<PERSON>, Pakistani folk singer[citation needed]", "html": "1919 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Pakistani folk singer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Pakistani folk singer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1920", "text": "<PERSON><PERSON><PERSON><PERSON>, Italian cartoonist (d. 2007)", "html": "1920 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Italian cartoonist (d. 2007)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Italian cartoonist (d. 2007)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1921", "text": "<PERSON>, Palestinian-American philosopher and academic (d. 1986)", "html": "1921 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Palestinian-American philosopher and academic (d. 1986)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Palestinian-American philosopher and academic (d. 1986)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1921", "text": "<PERSON>, French sculptor and academic (d. 1998)", "html": "1921 - <a href=\"https://wikipedia.org/wiki/C%C3%A9<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French sculptor and academic (d. 1998)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/C%C3%A9<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French sculptor and academic (d. 1998)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/C%C3%A9sar_<PERSON>ni"}]}, {"year": "1921", "text": "<PERSON>, Italian actress (d. 2013)", "html": "1921 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian actress (d. 2013)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian actress (d. 2013)", "links": [{"title": "Regina <PERSON>chi", "link": "https://wikipedia.org/wiki/Regina_Bianchi"}]}, {"year": "1921", "text": "<PERSON>, American basketball player (d. 1977)", "html": "1921 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(basketball)\" title=\"<PERSON> (basketball)\"><PERSON></a>, American basketball player (d. 1977)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(basketball)\" title=\"<PERSON> (basketball)\"><PERSON></a>, American basketball player (d. 1977)", "links": [{"title": "<PERSON> (basketball)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(basketball)"}]}, {"year": "1922", "text": "<PERSON>, American soldier and politician, 106th Governor of South Carolina (d. 2019)", "html": "1922 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, American soldier and politician, 106th <a href=\"https://wikipedia.org/wiki/Governor_of_South_Carolina\" title=\"Governor of South Carolina\">Governor of South Carolina</a> (d. 2019)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, American soldier and politician, 106th <a href=\"https://wikipedia.org/wiki/Governor_of_South_Carolina\" title=\"Governor of South Carolina\">Governor of South Carolina</a> (d. 2019)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}, {"title": "Governor of South Carolina", "link": "https://wikipedia.org/wiki/Governor_of_South_Carolina"}]}, {"year": "1923", "text": "<PERSON>, American actress (d. 1990)", "html": "1923 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress (d. 1990)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress (d. 1990)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1923", "text": "<PERSON><PERSON>, Italian actress (d. 2019)", "html": "1923 - <a href=\"https://wikipedia.org/wiki/Valentina_Cortese\" title=\"<PERSON><PERSON> Corte<PERSON>\"><PERSON><PERSON></a>, Italian actress (d. 2019)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Valentina_Cortese\" title=\"<PERSON>ntina <PERSON>rte<PERSON>\"><PERSON><PERSON></a>, Italian actress (d. 2019)", "links": [{"title": "Valentina Cortese", "link": "https://wikipedia.org/wiki/Valentina_Cortese"}]}, {"year": "1923", "text": "<PERSON><PERSON>, American jazz vibraphonist and composer (d. 1999)", "html": "1923 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American jazz vibraphonist and composer (d. 1999)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American jazz vibraphonist and composer (d. 1999)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Mi<PERSON>_<PERSON>"}]}, {"year": "1924", "text": "<PERSON>, Equatorial Guinean politician, 1st President of the Republic of Equatorial Guinea (d. 1979)", "html": "1924 - <a href=\"https://wikipedia.org/wiki/Francisco_Mac%C3%ADas_Ng<PERSON>ma\" title=\"<PERSON>\"><PERSON></a>, Equatorial Guinean politician, 1st <a href=\"https://wikipedia.org/wiki/List_of_heads_of_state_of_Equatorial_Guinea\" class=\"mw-redirect\" title=\"List of heads of state of Equatorial Guinea\">President of the Republic of Equatorial Guinea</a> (d. 1979)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Francisco_Mac%C3%ADas_Ng<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Equatorial Guinean politician, 1st <a href=\"https://wikipedia.org/wiki/List_of_heads_of_state_of_Equatorial_Guinea\" class=\"mw-redirect\" title=\"List of heads of state of Equatorial Guinea\">President of the Republic of Equatorial Guinea</a> (d. 1979)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Francisco_Mac%C3%AD<PERSON>_<PERSON><PERSON><PERSON>"}, {"title": "List of heads of state of Equatorial Guinea", "link": "https://wikipedia.org/wiki/List_of_heads_of_state_of_Equatorial_Guinea"}]}, {"year": "1925", "text": "<PERSON>, American child actor (d. 1981)", "html": "1925 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(American_actor)\" class=\"mw-redirect\" title=\"<PERSON> (American actor)\"><PERSON></a>, American child actor (d. 1981)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(American_actor)\" class=\"mw-redirect\" title=\"<PERSON> (American actor)\"><PERSON></a>, American child actor (d. 1981)", "links": [{"title": "<PERSON> (American actor)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(American_actor)"}]}, {"year": "1925", "text": "<PERSON>, Tanzanian politician and diplomat, 1st Tanzanian Minister of Finance (d. 2005)", "html": "1925 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Tanzanian politician and diplomat, 1st <a href=\"https://wikipedia.org/wiki/Minister_of_Finance_(Tanzania)\" title=\"Minister of Finance (Tanzania)\">Tanzanian Minister of Finance</a> (d. 2005)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Tanzanian politician and diplomat, 1st <a href=\"https://wikipedia.org/wiki/Minister_of_Finance_(Tanzania)\" title=\"Minister of Finance (Tanzania)\">Tanzanian Minister of Finance</a> (d. 2005)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}, {"title": "Minister of Finance (Tanzania)", "link": "https://wikipedia.org/wiki/Minister_of_Finance_(Tanzania)"}]}, {"year": "1926", "text": "<PERSON><PERSON><PERSON>, Lithuanian basketball player and coach (d. 2008)", "html": "1926 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>%C4%8Dius\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Lithuanian basketball player and coach (d. 2008)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>%C4%8Dius\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Lithuanian basketball player and coach (d. 2008)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_Petkevi%C4%8Dius"}]}, {"year": "1927", "text": "<PERSON>, French-Swiss dancer, choreographer, and director (d. 2007)", "html": "1927 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A9jart\" title=\"<PERSON>\"><PERSON></a>, French-Swiss dancer, choreographer, and director (d. 2007)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A9jart\" title=\"<PERSON>\"><PERSON></a>, French-Swiss dancer, choreographer, and director (d. 2007)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Maurice_B%C3%A9jart"}]}, {"year": "1927", "text": "<PERSON>, American clergyman and political activist (d. 1965)", "html": "1927 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American clergyman and political activist (d. 1965)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American clergyman and political activist (d. 1965)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1927", "text": "<PERSON>, American economist and academic, Nobel Prize laureate", "html": "1927 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American economist and academic, <a href=\"https://wikipedia.org/wiki/Nobel_Memorial_Prize_in_Economic_Sciences\" title=\"Nobel Memorial Prize in Economic Sciences\">Nobel Prize</a> laureate", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American economist and academic, <a href=\"https://wikipedia.org/wiki/Nobel_Memorial_Prize_in_Economic_Sciences\" title=\"Nobel Memorial Prize in Economic Sciences\">Nobel Prize</a> laureate", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "Nobel Memorial Prize in Economic Sciences", "link": "https://wikipedia.org/wiki/Nobel_Memorial_Prize_in_Economic_Sciences"}]}, {"year": "1927", "text": "<PERSON><PERSON>, American football player and businessman (d. 1998)", "html": "1927 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American football player and businessman (d. 1998)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American football player and businessman (d. 1998)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1928", "text": "<PERSON>, American author and screenwriter (d. 1984)", "html": "1928 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author and screenwriter (d. 1984)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author and screenwriter (d. 1984)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1928", "text": "<PERSON>, German-American historian, author, and academic", "html": "1928 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German-American historian, author, and academic", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German-American historian, author, and academic", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1929", "text": "<PERSON>, American journalist, author, and playwright (d. 2012)", "html": "1929 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American journalist, author, and playwright (d. 2012)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American journalist, author, and playwright (d. 2012)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1929", "text": "<PERSON><PERSON><PERSON>, Japanese actor and stuntman, portrayed <PERSON><PERSON> from 1954 to 1972 (d. 2017)", "html": "1929 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese actor and stuntman, portrayed <a href=\"https://wikipedia.org/wiki/Godzilla\" title=\"Godzilla\">God<PERSON></a> from 1954 to 1972 (d. 2017)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese actor and stuntman, portrayed <a href=\"https://wikipedia.org/wiki/Godzilla\" title=\"Godzilla\">God<PERSON></a> from 1954 to 1972 (d. 2017)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}, {"title": "Godzilla", "link": "https://wikipedia.org/wiki/Godzilla"}]}, {"year": "1930", "text": "<PERSON>, American actor (d. 2017)", "html": "1930 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor (d. 2017)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Hard<PERSON>\"><PERSON></a>, American actor (d. 2017)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>in"}]}, {"year": "1930", "text": "<PERSON>, American director and producer", "html": "1930 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American director and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American director and producer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1932", "text": "<PERSON>, Italian conductor (d. 1989)", "html": "1932 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A8\" title=\"<PERSON>\"><PERSON></a>, Italian conductor (d. 1989)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A8\" title=\"<PERSON>\"><PERSON></a>, Italian conductor (d. 1989)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Giuseppe_<PERSON>%C3%A8"}]}, {"year": "1933", "text": "<PERSON>, American philanthropist and diplomat (d. 2021)", "html": "1933 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, American philanthropist and diplomat (d. 2021)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, American philanthropist and diplomat (d. 2021)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1933", "text": "<PERSON>, English dramatist (d. 1967)", "html": "1933 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English dramatist (d. 1967)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English dramatist (d. 1967)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1934", "text": "<PERSON>, American lawyer and radio host (d. 1984)", "html": "1934 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and radio host (d. 1984)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and radio host (d. 1984)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1934", "text": "<PERSON><PERSON><PERSON>, Algerian politician, Algerian Minister of Foreign Affairs", "html": "1934 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Algerian politician, <a href=\"https://wikipedia.org/wiki/Ministry_of_Foreign_Affairs_(Algeria)\" title=\"Ministry of Foreign Affairs (Algeria)\">Algerian Minister of Foreign Affairs</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Algerian politician, <a href=\"https://wikipedia.org/wiki/Ministry_of_Foreign_Affairs_(Algeria)\" title=\"Ministry of Foreign Affairs (Algeria)\">Algerian Minister of Foreign Affairs</a>", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON><PERSON>i"}, {"title": "Ministry of Foreign Affairs (Algeria)", "link": "https://wikipedia.org/wiki/Ministry_of_Foreign_Affairs_(Algeria)"}]}, {"year": "1935", "text": "<PERSON><PERSON>, Indian politician (d. 2024)", "html": "1935 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON> <PERSON>\"><PERSON><PERSON> <PERSON></a>, Indian politician (d. 2024)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON> <PERSON></a>, Indian politician (d. 2024)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1936", "text": "<PERSON>, American businessman, co-founded Costco", "html": "1936 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American businessman, co-founded <a href=\"https://wikipedia.org/wiki/Costco\" title=\"Costco\">Costco</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American businessman, co-founded <a href=\"https://wikipedia.org/wiki/Costco\" title=\"Costco\">Costco</a>", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Costco", "link": "https://wikipedia.org/wiki/Costco"}]}, {"year": "1938", "text": "<PERSON>, American actor", "html": "1938 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1939", "text": "<PERSON><PERSON><PERSON><PERSON>, French actress", "html": "1939 - <a href=\"https://wikipedia.org/wiki/Mich%C3%A8<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, French actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Mich%C3%A8<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, French actress", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Mich%C3%A8le_Me<PERSON>ier"}]}, {"year": "1939", "text": "<PERSON>, English motorcycle racer and businessman (d. 2022)", "html": "1939 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Read\"><PERSON></a>, English motorcycle racer and businessman (d. 2022)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Read\"><PERSON></a>, English motorcycle racer and businessman (d. 2022)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1939", "text": "<PERSON><PERSON><PERSON><PERSON>, American politician", "html": "1939 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, American politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, American politician", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_Thompson"}]}, {"year": "1939", "text": "<PERSON><PERSON><PERSON>, Malian politician, Prime Minister of Mali (d. 2022)", "html": "1939 - <a href=\"https://wikipedia.org/wiki/Younoussi_Tour%C3%A9\" title=\"Younoussi Touré\"><PERSON><PERSON><PERSON>é</a>, Malian politician, <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Mali\" class=\"mw-redirect\" title=\"Prime Minister of Mali\">Prime Minister of Mali</a> (d. 2022)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Younoussi_Tour%C3%A9\" title=\"Younoussi Touré\"><PERSON><PERSON><PERSON> Touré</a>, Malian politician, <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Mali\" class=\"mw-redirect\" title=\"Prime Minister of Mali\">Prime Minister of Mali</a> (d. 2022)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Younoussi_Tour%C3%A9"}, {"title": "Prime Minister of Mali", "link": "https://wikipedia.org/wiki/Prime_Minister_of_Mali"}]}, {"year": "1942", "text": "<PERSON>, American lawyer and politician, 67th Mayor of Detroit", "html": "1942 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and politician, 67th <a href=\"https://wikipedia.org/wiki/Mayor_of_Detroit\" class=\"mw-redirect\" title=\"Mayor of Detroit\">Mayor of Detroit</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and politician, 67th <a href=\"https://wikipedia.org/wiki/Mayor_of_Detroit\" class=\"mw-redirect\" title=\"Mayor of Detroit\">Mayor of Detroit</a>", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Mayor of Detroit", "link": "https://wikipedia.org/wiki/Mayor_of_Detroit"}]}, {"year": "1942", "text": "<PERSON>, 3rd Baron <PERSON>, English dentist and politician", "html": "1942 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_3rd_Baron_<PERSON><PERSON>\" title=\"<PERSON>, 3rd Baron <PERSON>\"><PERSON>, 3rd Baron <PERSON></a>, English dentist and politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_3rd_Baron_<PERSON><PERSON>\" title=\"<PERSON>, 3rd <PERSON>\"><PERSON>, 3rd <PERSON></a>, English dentist and politician", "links": [{"title": "<PERSON>, 3rd <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_3rd_Baron_<PERSON>"}]}, {"year": "1942", "text": "<PERSON> <PERSON>, American singer-songwriter and guitarist", "html": "1942 - <a href=\"https://wikipedia.org/wiki/Country_Joe_McDonald\" title=\"Country Joe McDonald\">Country Joe <PERSON></a>, American singer-songwriter and guitarist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Country_Joe_McDonald\" title=\"Country Joe McDonald\">Country Joe <PERSON></a>, American singer-songwriter and guitarist", "links": [{"title": "Country <PERSON>", "link": "https://wikipedia.org/wiki/Country_<PERSON>_<PERSON>"}]}, {"year": "1942", "text": "<PERSON><PERSON><PERSON>, Ivorian economist and politician, President of the Ivory Coast", "html": "1942 - <a href=\"https://wikipedia.org/wiki/Alassane_O<PERSON>\" title=\"Alassane <PERSON>\"><PERSON><PERSON><PERSON></a>, Ivorian economist and politician, <a href=\"https://wikipedia.org/wiki/List_of_heads_of_state_of_Ivory_Coast\" title=\"List of heads of state of Ivory Coast\">President of the Ivory Coast</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Alassane_<PERSON>\" title=\"Alass<PERSON>\"><PERSON><PERSON><PERSON></a>, Ivorian economist and politician, <a href=\"https://wikipedia.org/wiki/List_of_heads_of_state_of_Ivory_Coast\" title=\"List of heads of state of Ivory Coast\">President of the Ivory Coast</a>", "links": [{"title": "Alassane <PERSON>", "link": "https://wikipedia.org/wiki/Alassane_<PERSON>"}, {"title": "List of heads of state of Ivory Coast", "link": "https://wikipedia.org/wiki/List_of_heads_of_state_of_Ivory_Coast"}]}, {"year": "1942", "text": "<PERSON><PERSON><PERSON>, Russian pilot and cosmonaut (d. 2005)", "html": "1942 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Russian pilot and cosmonaut (d. 2005)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Russian pilot and cosmonaut (d. 2005)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1943", "text": "<PERSON>, American soldier and politician, 7th Governor of Alaska", "html": "1943 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>(politician)\" title=\"<PERSON> (politician)\"><PERSON></a>, American soldier and politician, 7th <a href=\"https://wikipedia.org/wiki/Governor_of_Alaska\" class=\"mw-redirect\" title=\"Governor of Alaska\">Governor of Alaska</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>(politician)\" title=\"<PERSON> (politician)\"><PERSON></a>, American soldier and politician, 7th <a href=\"https://wikipedia.org/wiki/Governor_of_Alaska\" class=\"mw-redirect\" title=\"Governor of Alaska\">Governor of Alaska</a>", "links": [{"title": "<PERSON> (politician)", "link": "https://wikipedia.org/wiki/<PERSON>_(politician)"}, {"title": "Governor of Alaska", "link": "https://wikipedia.org/wiki/Governor_of_Alaska"}]}, {"year": "1943", "text": "<PERSON>, American comedian, screenwriter and producer", "html": "1943 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American comedian, screenwriter and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American comedian, screenwriter and producer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1943", "text": "<PERSON>, Croatian lawyer and politician, 16th Speaker of the Croatian Parliament", "html": "1943 - <a href=\"https://wikipedia.org/wiki/<PERSON>_%C5%A0eks\" title=\"<PERSON>\"><PERSON></a>, Croatian lawyer and politician, 16th <a href=\"https://wikipedia.org/wiki/Speaker_of_the_Croatian_Parliament\" title=\"Speaker of the Croatian Parliament\">Speaker of the Croatian Parliament</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_%C5%A0eks\" title=\"<PERSON>\"><PERSON></a>, Croatian lawyer and politician, 16th <a href=\"https://wikipedia.org/wiki/Speaker_of_the_Croatian_Parliament\" title=\"Speaker of the Croatian Parliament\">Speaker of the Croatian Parliament</a>", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Vladimir_%C5%A0eks"}, {"title": "Speaker of the Croatian Parliament", "link": "https://wikipedia.org/wiki/Speaker_of_the_Croatian_Parliament"}]}, {"year": "1944", "text": "<PERSON>, Australian rugby league player", "html": "1944 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian rugby league player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian rugby league player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1944", "text": "<PERSON>, American professional wrestling manager", "html": "1944 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American professional wrestling manager", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American professional wrestling manager", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1944", "text": "<PERSON><PERSON><PERSON><PERSON>, Pakistani field hockey player and politician, 13th Prime Minister of Pakistan (d. 2020)", "html": "1944 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Pakistani field hockey player and politician, 13th <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Pakistan\" title=\"Prime Minister of Pakistan\">Prime Minister of Pakistan</a> (d. 2020)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Pakistani field hockey player and politician, 13th <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Pakistan\" title=\"Prime Minister of Pakistan\">Prime Minister of Pakistan</a> (d. 2020)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>_<PERSON>"}, {"title": "Prime Minister of Pakistan", "link": "https://wikipedia.org/wiki/Prime_Minister_of_Pakistan"}]}, {"year": "1944", "text": "<PERSON>, Polish journalist and author (d. 2013)", "html": "1944 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C5%84ska\" title=\"<PERSON>\"><PERSON></a>, Polish journalist and author (d. 2013)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C5%84ska\" title=\"<PERSON>\"><PERSON></a>, Polish journalist and author (d. 2013)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Teresa_<PERSON>a%C5%84ska"}]}, {"year": "1944", "text": "<PERSON><PERSON>, Estonian author, playwright, and director (d. 2005)", "html": "1944 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Estonian author, playwright, and director (d. 2005)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Estonian author, playwright, and director (d. 2005)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>i_Unt"}]}, {"year": "1945", "text": "<PERSON>, American politician and former United States Ambassador to Poland", "html": "1945 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American politician and former United States Ambassador to Poland", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American politician and former United States Ambassador to Poland", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1945", "text": "<PERSON><PERSON>, Belgian racing driver", "html": "1945 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>ck<PERSON>\" title=\"<PERSON><PERSON>ckx\"><PERSON><PERSON></a>, Belgian racing driver", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>ck<PERSON>\" title=\"<PERSON><PERSON>ckx\"><PERSON><PERSON></a>, Belgian racing driver", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>x"}]}, {"year": "1945", "text": "<PERSON>, American basketball player", "html": "1945 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(basketball)\" title=\"<PERSON> (basketball)\"><PERSON></a>, American basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(basketball)\" title=\"<PERSON> (basketball)\"><PERSON></a>, American basketball player", "links": [{"title": "<PERSON> (basketball)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(basketball)"}]}, {"year": "1946", "text": "<PERSON><PERSON><PERSON><PERSON>, Brazilian footballer and manager", "html": "1946 - <a href=\"https://wikipedia.org/wiki/Rivellin<PERSON>\" title=\"Rivellin<PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Brazilian footballer and manager", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Rive<PERSON><PERSON>\" title=\"Rivellin<PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Brazilian footballer and manager", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Rivellino"}]}, {"year": "1946", "text": "<PERSON>, American social psychologist and academic", "html": "1946 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American social psychologist and academic", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American social psychologist and academic", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1947", "text": "<PERSON>, American sergeant and politician, 54th Governor of New Jersey", "html": "1947 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American sergeant and politician, 54th <a href=\"https://wikipedia.org/wiki/Governor_of_New_Jersey\" title=\"Governor of New Jersey\">Governor of New Jersey</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American sergeant and politician, 54th <a href=\"https://wikipedia.org/wiki/Governor_of_New_Jersey\" title=\"Governor of New Jersey\">Governor of New Jersey</a>", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Governor of New Jersey", "link": "https://wikipedia.org/wiki/Governor_of_New_Jersey"}]}, {"year": "1948", "text": "<PERSON><PERSON>, Turkish economist, academic, and politician, 57th Deputy Prime Minister of Turkey", "html": "1948 - <a href=\"https://wikipedia.org/wiki/Devlet_Bah%C3%A7eli\" title=\"<PERSON><PERSON> Bahçeli\"><PERSON><PERSON></a>, Turkish economist, academic, and politician, 57th <a href=\"https://wikipedia.org/wiki/Deputy_Prime_Minister_of_Turkey\" title=\"Deputy Prime Minister of Turkey\">Deputy Prime Minister of Turkey</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Devlet_Bah%C3%A7eli\" title=\"<PERSON>let Bahçeli\"><PERSON><PERSON></a>, Turkish economist, academic, and politician, 57th <a href=\"https://wikipedia.org/wiki/Deputy_Prime_Minister_of_Turkey\" title=\"Deputy Prime Minister of Turkey\">Deputy Prime Minister of Turkey</a>", "links": [{"title": "Devlet Bahçeli", "link": "https://wikipedia.org/wiki/Devlet_Bah%C3%A7eli"}, {"title": "Deputy Prime Minister of Turkey", "link": "https://wikipedia.org/wiki/Deputy_Prime_Minister_of_Turkey"}]}, {"year": "1948", "text": "<PERSON>, Russian general and politician, 1st Russian Minister of Defence (d. 2012)", "html": "1948 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian general and politician, 1st <a href=\"https://wikipedia.org/wiki/Ministry_of_Defence_(Russia)\" title=\"Ministry of Defence (Russia)\">Russian Minister of Defence</a> (d. 2012)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian general and politician, 1st <a href=\"https://wikipedia.org/wiki/Ministry_of_Defence_(Russia)\" title=\"Ministry of Defence (Russia)\">Russian Minister of Defence</a> (d. 2012)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Ministry of Defence (Russia)", "link": "https://wikipedia.org/wiki/Ministry_of_Defence_(Russia)"}]}, {"year": "1948", "text": "<PERSON>, New Zealand runner and politician (d. 2018)", "html": "1948 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, New Zealand runner and politician (d. 2018)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, New Zealand runner and politician (d. 2018)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1949", "text": "<PERSON><PERSON><PERSON>, Ukrainian politician and diplomat", "html": "1949 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Ukrainian politician and diplomat", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Ukrainian politician and diplomat", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1950", "text": "<PERSON>, Australian rugby league player and coach", "html": "1950 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(rugby_league)\" title=\"<PERSON> (rugby league)\"><PERSON></a>, Australian rugby league player and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(rugby_league)\" title=\"<PERSON> (rugby league)\"><PERSON></a>, Australian rugby league player and coach", "links": [{"title": "<PERSON> (rugby league)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(rugby_league)"}]}, {"year": "1950", "text": "<PERSON>, English footballer", "html": "1950 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>(footballer)\" title=\"<PERSON> (footballer)\"><PERSON></a>, English footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>(footballer)\" title=\"<PERSON> (footballer)\"><PERSON></a>, English footballer", "links": [{"title": "<PERSON> (footballer)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>(footballer)"}]}, {"year": "1952", "text": "<PERSON><PERSON><PERSON>, Indian director and cinematographer", "html": "1952 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON> <PERSON>\"><PERSON><PERSON><PERSON> <PERSON></a>, Indian director and cinematographer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON> <PERSON>\"><PERSON><PERSON><PERSON> <PERSON></a>, Indian director and cinematographer", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "1953", "text": "<PERSON>, American businessman and politician, 29th Governor of New Mexico", "html": "1953 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American businessman and politician, 29th <a href=\"https://wikipedia.org/wiki/Governor_of_New_Mexico\" title=\"Governor of New Mexico\">Governor of New Mexico</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American businessman and politician, 29th <a href=\"https://wikipedia.org/wiki/Governor_of_New_Mexico\" title=\"Governor of New Mexico\">Governor of New Mexico</a>", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Governor of New Mexico", "link": "https://wikipedia.org/wiki/Governor_of_New_Mexico"}]}, {"year": "1954", "text": "<PERSON>, American actor", "html": "1954 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1954", "text": "<PERSON>, American lawyer and politician", "html": "1954 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and politician", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1954", "text": "<PERSON>, Irish poet and critic (d. 2012)", "html": "1954 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%27Driscoll\" title=\"<PERSON>\"><PERSON></a>, Irish poet and critic (d. 2012)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%27Driscoll\" title=\"<PERSON>\"><PERSON></a>, Irish poet and critic (d. 2012)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_O%27Driscoll"}]}, {"year": "1954", "text": "<PERSON><PERSON>, Greek engineer and politician, Greek Minister of Finance", "html": "1954 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Greek engineer and politician, <a href=\"https://wikipedia.org/wiki/Ministry_of_Finance_(Greece)\" class=\"mw-redirect\" title=\"Ministry of Finance (Greece)\">Greek Minister of Finance</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Greek engineer and politician, <a href=\"https://wikipedia.org/wiki/Ministry_of_Finance_(Greece)\" class=\"mw-redirect\" title=\"Ministry of Finance (Greece)\">Greek Minister of Finance</a>", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}, {"title": "Ministry of Finance (Greece)", "link": "https://wikipedia.org/wiki/Ministry_of_Finance_(Greece)"}]}, {"year": "1955", "text": "<PERSON>, English classicist, academic and presenter", "html": "1955 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(classicist)\" title=\"<PERSON> (classicist)\"><PERSON></a>, English classicist, academic and presenter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(classicist)\" title=\"<PERSON> (classicist)\"><PERSON></a>, English classicist, academic and presenter", "links": [{"title": "<PERSON> (classicist)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(classicist)"}]}, {"year": "1955", "text": "<PERSON><PERSON><PERSON><PERSON>, American baseball player (d. 2021)", "html": "1955 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, American baseball player (d. 2021)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, American baseball player (d. 2021)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1956", "text": "<PERSON>, Russian engineer and astronaut", "html": "1956 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, Russian engineer and astronaut", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, Russian engineer and astronaut", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1956", "text": "<PERSON>, Australian rugby league player", "html": "1956 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian rugby league player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian rugby league player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1956", "text": "<PERSON>, French lawyer and politician; Managing Director, International Monetary Fund", "html": "1956 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French lawyer and politician; Managing Director, <a href=\"https://wikipedia.org/wiki/International_Monetary_Fund\" title=\"International Monetary Fund\">International Monetary Fund</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French lawyer and politician; Managing Director, <a href=\"https://wikipedia.org/wiki/International_Monetary_Fund\" title=\"International Monetary Fund\">International Monetary Fund</a>", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "International Monetary Fund", "link": "https://wikipedia.org/wiki/International_Monetary_Fund"}]}, {"year": "1956", "text": "<PERSON>, American basketball player (d. 2011)", "html": "1956 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(basketball,_born_1956)\" title=\"<PERSON> (basketball, born 1956)\"><PERSON></a>, American basketball player (d. 2011)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(basketball,_born_1956)\" title=\"<PERSON> (basketball, born 1956)\"><PERSON></a>, American basketball player (d. 2011)", "links": [{"title": "<PERSON> (basketball, born 1956)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(basketball,_born_1956)"}]}, {"year": "1956", "text": "<PERSON>, Australian singer-songwriter and guitarist", "html": "1956 - <a href=\"https://wikipedia.org/wiki/Martin_Plaza\" title=\"Martin Plaza\"><PERSON></a>, Australian singer-songwriter and guitarist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Martin_Plaza\" title=\"Martin Plaza\"><PERSON></a>, Australian singer-songwriter and guitarist", "links": [{"title": "Martin Plaza", "link": "https://wikipedia.org/wiki/Martin_<PERSON>"}]}, {"year": "1957", "text": "<PERSON><PERSON><PERSON>, Greek lawyer and politician, Deputy Prime Minister of Greece", "html": "1957 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>enizel<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Greek lawyer and politician, <a href=\"https://wikipedia.org/wiki/Deputy_Prime_Minister_of_Greece\" title=\"Deputy Prime Minister of Greece\">Deputy Prime Minister of Greece</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Greek lawyer and politician, <a href=\"https://wikipedia.org/wiki/Deputy_Prime_Minister_of_Greece\" title=\"Deputy Prime Minister of Greece\">Deputy Prime Minister of Greece</a>", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}, {"title": "Deputy Prime Minister of Greece", "link": "https://wikipedia.org/wiki/Deputy_Prime_Minister_of_Greece"}]}, {"year": "1958", "text": "<PERSON><PERSON>, Barbadian rapper and DJ ", "html": "1958 - <a href=\"https://wikipedia.org/wiki/Grandmaster_Flash\" title=\"Grandmaster Flash\"><PERSON><PERSON> Flash</a>, Barbadian rapper and DJ ", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Grandmaster_Flash\" title=\"Grandmaster Flash\"><PERSON><PERSON> Flash</a>, Barbadian rapper and DJ ", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1958", "text": "<PERSON>, American ice hockey player", "html": "1958 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American ice hockey player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American ice hockey player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1959", "text": "<PERSON>, Afghan colonel, pilot, and astronaut", "html": "1959 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, Afghan colonel, pilot, and astronaut", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, Afghan colonel, pilot, and astronaut", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "1959", "text": "<PERSON><PERSON><PERSON>, Comorian colonel and politician, President of the Comoros", "html": "1959 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Comorian colonel and politician, <a href=\"https://wikipedia.org/wiki/List_of_heads_of_state_of_Comoros\" class=\"mw-redirect\" title=\"List of heads of state of Comoros\">President of the Comoros</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Comorian colonel and politician, <a href=\"https://wikipedia.org/wiki/List_of_heads_of_state_of_Comoros\" class=\"mw-redirect\" title=\"List of heads of state of Comoros\">President of the Comoros</a>", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON><PERSON><PERSON>"}, {"title": "List of heads of state of Comoros", "link": "https://wikipedia.org/wiki/List_of_heads_of_state_of_Comoros"}]}, {"year": "1959", "text": "<PERSON><PERSON><PERSON><PERSON>, Greek basketball player and coach", "html": "1959 - <a href=\"https://wikipedia.org/wiki/<PERSON>agi<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Greek basketball player and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>agi<PERSON><PERSON>_<PERSON>\" title=\"<PERSON>agi<PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Greek basketball player and coach", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Panagiot<PERSON>_<PERSON>"}]}, {"year": "1959", "text": "<PERSON>, English director and former actor", "html": "1959 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(actor)\" title=\"<PERSON> (actor)\"><PERSON></a>, English director and former actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(actor)\" title=\"<PERSON> (actor)\"><PERSON></a>, English director and former actor", "links": [{"title": "<PERSON> (actor)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(actor)"}]}, {"year": "1961", "text": "<PERSON>, Australian rugby league player", "html": "1961 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian rugby league player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian rugby league player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>o"}]}, {"year": "1962", "text": "<PERSON>, Italian-Scottish economist and academic", "html": "1962 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian-Scottish economist and academic", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian-Scottish economist and academic", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1963", "text": "<PERSON><PERSON><PERSON>, French racing driver", "html": "1963 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, French racing driver", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, French racing driver", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>"}]}, {"year": "1964", "text": "<PERSON><PERSON><PERSON>, American actress", "html": "1964 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American actress", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1966", "text": "<PERSON>, Australian businesswoman and politician, 28th Speaker of the Australian House of Representatives", "html": "1966 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian businesswoman and politician, 28th <a href=\"https://wikipedia.org/wiki/Speaker_of_the_Australian_House_of_Representatives\" title=\"Speaker of the Australian House of Representatives\">Speaker of the Australian House of Representatives</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian businesswoman and politician, 28th <a href=\"https://wikipedia.org/wiki/Speaker_of_the_Australian_House_of_Representatives\" title=\"Speaker of the Australian House of Representatives\">Speaker of the Australian House of Representatives</a>", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Speaker of the Australian House of Representatives", "link": "https://wikipedia.org/wiki/Speaker_of_the_Australian_House_of_Representatives"}]}, {"year": "1966", "text": "<PERSON><PERSON><PERSON>, Serbian journalist and politician, 95th Prime Minister of Serbia", "html": "1966 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>%C4%8Di%C4%87\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Serbian journalist and politician, 95th <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Serbia\" title=\"Prime Minister of Serbia\">Prime Minister of Serbia</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>%C4%8Di%C4%87\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Serbian journalist and politician, 95th <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Serbia\" title=\"Prime Minister of Serbia\">Prime Minister of Serbia</a>", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Ivica_Da%C4%8Di%C4%87"}, {"title": "Prime Minister of Serbia", "link": "https://wikipedia.org/wiki/Prime_Minister_of_Serbia"}]}, {"year": "1966", "text": "<PERSON><PERSON><PERSON><PERSON>, Croatian-Canadian businessman, 11th Prime Minister of Croatia", "html": "1966 - <a href=\"https://wikipedia.org/wiki/Tihomir_Ore%C5%A1kovi%C4%87\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Croatian-Canadian businessman, 11th <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Croatia\" title=\"Prime Minister of Croatia\">Prime Minister of Croatia</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Tihomir_Ore%C5%A1kovi%C4%87\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Croatian-Canadian businessman, 11th <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Croatia\" title=\"Prime Minister of Croatia\">Prime Minister of Croatia</a>", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Tihomir_Ore%C5%A1kovi%C4%87"}, {"title": "Prime Minister of Croatia", "link": "https://wikipedia.org/wiki/Prime_Minister_of_Croatia"}]}, {"year": "1967", "text": "<PERSON><PERSON><PERSON>, New Zealand rugby league player", "html": "1967 - <a href=\"https://wikipedia.org/wiki/Tawer<PERSON>_<PERSON>au\" title=\"Tawera Nikau\"><PERSON><PERSON><PERSON></a>, New Zealand rugby league player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>wer<PERSON>_<PERSON>au\" title=\"Tawera Nikau\"><PERSON><PERSON><PERSON></a>, New Zealand rugby league player", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON><PERSON>"}]}, {"year": "1968", "text": "<PERSON><PERSON>, Croatian footballer", "html": "1968 - <a href=\"https://wikipedia.org/wiki/Davor_%C5%A0uker\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Croatian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Da<PERSON>_%C5%A0uker\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Croatian footballer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Davor_%C5%A0uker"}]}, {"year": "1969", "text": "<PERSON>, American actor", "html": "1969 - <a href=\"https://wikipedia.org/wiki/Morris_Chestnut\" title=\"Morris Chestnut\"><PERSON> Chestnut</a>, American actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Morris_Chestnut\" title=\"Morris Chestnut\"><PERSON> Chestnut</a>, American actor", "links": [{"title": "Morris Chestnut", "link": "https://wikipedia.org/wiki/Morris_Chestnut"}]}, {"year": "1969", "text": "<PERSON><PERSON>, American actor (d. 2018)", "html": "1969 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American actor (d. 2018)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American actor (d. 2018)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Verne_Troyer"}]}, {"year": "1970", "text": "<PERSON>, Russian footballer and coach", "html": "1970 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian footballer and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian footballer and coach", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1971", "text": "<PERSON><PERSON>, American wrestler and coach", "html": "1971 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American wrestler and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American wrestler and coach", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1971", "text": "<PERSON>, Czech-American ice hockey player and coach", "html": "1971 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%ADk\" title=\"<PERSON>\"><PERSON></a>, Czech-American ice hockey player and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%ADk\" title=\"<PERSON>\"><PERSON></a>, Czech-American ice hockey player and coach", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%ADk"}]}, {"year": "1971", "text": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, Indian politician", "html": "1971 - <a href=\"https://wikipedia.org/wiki/J<PERSON><PERSON>radity<PERSON>_Madhavrao_Scindia\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON>rad<PERSON>a Madhavrao Scindia\"><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON></a>, Indian politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>rad<PERSON><PERSON>_Madhavrao_Scindia\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON>rad<PERSON>a Madhavrao Scindia\"><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON></a>, Indian politician", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> Madhavrao Scindia", "link": "https://wikipedia.org/wiki/Jyo<PERSON>rad<PERSON>a_Madhavra<PERSON>_Scindia"}]}, {"year": "1972", "text": "<PERSON><PERSON>, French footballer", "html": "1972 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, French footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, French footballer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON><PERSON>"}]}, {"year": "1974", "text": "<PERSON>, Canadian lawyer and politician, 9th Canadian Minister of Industry", "html": "1974 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian lawyer and politician, 9th <a href=\"https://wikipedia.org/wiki/Minister_of_Industry_(Canada)\" class=\"mw-redirect\" title=\"Minister of Industry (Canada)\">Canadian Minister of Industry</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian lawyer and politician, 9th <a href=\"https://wikipedia.org/wiki/Minister_of_Industry_(Canada)\" class=\"mw-redirect\" title=\"Minister of Industry (Canada)\">Canadian Minister of Industry</a>", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Minister of Industry (Canada)", "link": "https://wikipedia.org/wiki/Minister_of_Industry_(Canada)"}]}, {"year": "1975", "text": "<PERSON>, Australian basketball player and coach", "html": "1975 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian basketball player and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian basketball player and coach", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1975", "text": "<PERSON>, American soccer player and sportscaster", "html": "1975 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(soccer)\" title=\"<PERSON> (soccer)\"><PERSON></a>, American soccer player and sportscaster", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(soccer)\" title=\"<PERSON> (soccer)\"><PERSON></a>, American soccer player and sportscaster", "links": [{"title": "<PERSON> (soccer)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(soccer)"}]}, {"year": "1975", "text": "<PERSON>-<PERSON>, Canadian ice hockey player", "html": "1975 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>-<PERSON>\" title=\"<PERSON>-<PERSON>\"><PERSON></a>, Canadian ice hockey player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>-<PERSON>\" title=\"<PERSON>-<PERSON>\"><PERSON>-<PERSON></a>, Canadian ice hockey player", "links": [{"title": "<PERSON>-<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>-<PERSON>"}]}, {"year": "1975", "text": "<PERSON>, Dominican baseball player", "html": "1975 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%ADs\" title=\"<PERSON>\"><PERSON></a>, Dominican baseball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%ADs\" title=\"<PERSON>\"><PERSON></a>, Dominican baseball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Fernando_Tat%C3%ADs"}]}, {"year": "1976", "text": "<PERSON>, American singer, songwriter, producer, and actor", "html": "1976 - <a href=\"https://wikipedia.org/wiki/<PERSON>_(American_singer)\" title=\"<PERSON> (American singer)\"><PERSON></a>, American singer, songwriter, producer, and actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(American_singer)\" title=\"<PERSON> (American singer)\"><PERSON></a>, American singer, songwriter, producer, and actor", "links": [{"title": "<PERSON> (American singer)", "link": "https://wikipedia.org/wiki/<PERSON>_(American_singer)"}]}, {"year": "1979", "text": "<PERSON><PERSON><PERSON>, Indian actress ", "html": "1979 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Indian actress ", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Indian actress ", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON><PERSON>"}]}, {"year": "1980", "text": "<PERSON><PERSON>, Swedish-American model", "html": "1980 - <a href=\"https://wikipedia.org/wiki/Eli<PERSON>_<PERSON>\" title=\"<PERSON>n <PERSON>\"><PERSON><PERSON></a>, Swedish-American model", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Eli<PERSON>_<PERSON>\" title=\"<PERSON>n <PERSON>\"><PERSON><PERSON></a>, Swedish-American model", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Elin_Nordegren"}]}, {"year": "1981", "text": "<PERSON>, Irish-English actor", "html": "1981 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Irish-English actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Irish-English actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1981", "text": "<PERSON><PERSON><PERSON>, Hungarian racing driver", "html": "1981 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>ner\" title=\"<PERSON>sol<PERSON> Baumgartner\"><PERSON><PERSON><PERSON></a>, Hungarian racing driver", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>um<PERSON>ner\" title=\"<PERSON>sol<PERSON> Baumgartner\"><PERSON><PERSON><PERSON></a>, Hungarian racing driver", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_Baumgartner"}]}, {"year": "1981", "text": "<PERSON><PERSON><PERSON>, Croatian footballer", "html": "1981 - <a href=\"https://wikipedia.org/wiki/M<PERSON><PERSON>_Petri%C4%87\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Croatian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_Petri%C4%87\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Croatian footballer", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Mladen_Petri%C4%87"}]}, {"year": "1981", "text": "<PERSON>, American actress", "html": "1981 - <a href=\"https://wikipedia.org/wiki/Eden_Riegel\" title=\"<PERSON> Riegel\"><PERSON></a>, American actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Eden_Riegel\" title=\"<PERSON> Riegel\"><PERSON></a>, American actress", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Eden_Riegel"}]}, {"year": "1982", "text": "<PERSON><PERSON><PERSON>, Uruguayan footballer", "html": "1982 - <a href=\"https://wikipedia.org/wiki/Egidio_Ar%C3%A9valo\" title=\"Egidio Arévalo\"><PERSON><PERSON><PERSON> Aréval<PERSON></a>, Uruguayan footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Egidio_Ar%C3%A9valo\" title=\"Egidio Arévalo\"><PERSON><PERSON><PERSON> Aréval<PERSON></a>, Uruguayan footballer", "links": [{"title": "Egi<PERSON>", "link": "https://wikipedia.org/wiki/Egidio_Ar%C3%A9valo"}]}, {"year": "1982", "text": "<PERSON>, Argentine tennis player", "html": "1982 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Argentine tennis player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Argentine tennis player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1983", "text": "<PERSON><PERSON>, English footballer", "html": "1983 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, English footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> Davenport\"><PERSON><PERSON></a>, English footballer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1983", "text": "<PERSON>, South Korean archer", "html": "1983 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>-<PERSON>yun_(archer)\" title=\"<PERSON> (archer)\"><PERSON></a>, South Korean archer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>-<PERSON>yun_(archer)\" title=\"<PERSON> (archer)\"><PERSON></a>, South Korean archer", "links": [{"title": "<PERSON> (archer)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>-<PERSON>yun_(archer)"}]}, {"year": "1984", "text": "<PERSON>, Peruvian footballer", "html": "1984 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Peruvian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Peruvian footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1984", "text": "<PERSON>, Spanish basketball player", "html": "1984 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_Emeter<PERSON>\" title=\"Fernando San Emeterio\"><PERSON></a>, Spanish basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_Emeter<PERSON>\" title=\"Fernando San Emeterio\"><PERSON></a>, Spanish basketball player", "links": [{"title": "Fernando San Emeterio", "link": "https://wikipedia.org/wiki/Fernando_San_Emeterio"}]}, {"year": "1984", "text": "<PERSON>, Australian rugby league player", "html": "1984 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian rugby league player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian rugby league player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1985", "text": "<PERSON>, Canadian ice hockey player", "html": "1985 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1985", "text": "<PERSON>, Northern Irish footballer", "html": "1985 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Northern Irish footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Northern Irish footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1985", "text": "<PERSON><PERSON>, Japanese professional wrestler", "html": "1985 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Japanese professional wrestler", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Japanese professional wrestler", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>"}]}, {"year": "1985", "text": "<PERSON><PERSON><PERSON>, Brazilian basketball player", "html": "1985 - <a href=\"https://wikipedia.org/wiki/Tiago_Splitter\" title=\"Tiago Splitter\"><PERSON><PERSON><PERSON></a>, Brazilian basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Tia<PERSON>_Splitter\" title=\"Tiago Splitter\"><PERSON><PERSON><PERSON></a>, Brazilian basketball player", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Tiago_Splitter"}]}, {"year": "1986", "text": "<PERSON>, Uruguayan tennis player", "html": "1986 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Uruguayan tennis player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Uruguayan tennis player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1986", "text": "<PERSON>, American basketball player", "html": "1986 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(basketball)\" title=\"<PERSON> (basketball)\"><PERSON></a>, American basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(basketball)\" title=\"<PERSON> (basketball)\"><PERSON></a>, American basketball player", "links": [{"title": "<PERSON> (basketball)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(basketball)"}]}, {"year": "1986", "text": "<PERSON>, Northern Irish actor", "html": "1986 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Northern Irish actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Northern Irish actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1987", "text": "<PERSON>, Canadian ice hockey player", "html": "1987 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A9\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A9\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Gilbert_Brul%C3%A9"}]}, {"year": "1987", "text": "<PERSON><PERSON>, American ice dancer", "html": "1987 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American ice dancer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American ice dancer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1987", "text": "<PERSON><PERSON>, Swedish ice hockey player", "html": "1987 - <a href=\"https://wikipedia.org/wiki/Patric_H%C3%B6<PERSON>q<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Swedish ice hockey player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Patric_H%C3%B6<PERSON>q<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Swedish ice hockey player", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Patric_H%C3%B6<PERSON>q<PERSON>"}]}, {"year": "1988", "text": "<PERSON>, Czech footballer", "html": "1988 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Czech footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Czech footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1988", "text": "<PERSON>, American baseball player", "html": "1988 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Dallas_Ke<PERSON>l"}]}, {"year": "1989", "text": "<PERSON>, American football player", "html": "1989 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1990", "text": "<PERSON>, Israeli tennis player", "html": "1990 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Israeli tennis player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Israeli tennis player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1990", "text": "<PERSON>, Tunisian football player", "html": "1990 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A2loul\" title=\"<PERSON>\"><PERSON></a>, Tunisian football player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A2loul\" title=\"<PERSON>\"><PERSON></a>, Tunisian football player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Ali_Ma%C3%A2loul"}]}, {"year": "1991", "text": "<PERSON>, American basketball player", "html": "1991 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>.\" title=\"<PERSON> Jr.\"><PERSON> Jr.</a>, American basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>.\" title=\"<PERSON> Jr.\"><PERSON> Jr.</a>, American basketball player", "links": [{"title": "<PERSON> Jr.", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>."}]}, {"year": "1991", "text": "<PERSON>, American football player", "html": "1991 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> S<PERSON>\"><PERSON></a>, American football player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1991", "text": "<PERSON>, American football player", "html": "1991 - <a href=\"https://wikipedia.org/wiki/<PERSON>_Su%27a-Filo\" class=\"mw-redirect\" title=\"<PERSON>a-<PERSON>\"><PERSON></a>, American football player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Xavier_Su%27a-Filo\" class=\"mw-redirect\" title=\"<PERSON>a-Filo\"><PERSON></a>, American football player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Xavier_Su%27a-<PERSON><PERSON>"}]}, {"year": "1992", "text": "<PERSON>, New Zealand rugby league player", "html": "1992 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, New Zealand rugby league player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, New Zealand rugby league player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1992", "text": "<PERSON>, Irish footballer", "html": "1992 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Irish footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Irish footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1993", "text": "<PERSON>, American basketball player", "html": "1993 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>.\" title=\"<PERSON>.\"><PERSON>.</a>, American basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>.\" title=\"<PERSON>.\"><PERSON>.</a>, American basketball player", "links": [{"title": "<PERSON>.", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>."}]}, {"year": "1993", "text": "<PERSON><PERSON><PERSON><PERSON>, Malian footballer", "html": "1993 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_Doucour%C3%A9\" title=\"<PERSON><PERSON><PERSON><PERSON> Doucouré\"><PERSON><PERSON><PERSON><PERSON></a>, Malian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_Doucour%C3%A9\" title=\"<PERSON><PERSON><PERSON><PERSON> Doucouré\"><PERSON><PERSON><PERSON><PERSON></a>, Malian footballer", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>e_Doucour%C3%A9"}]}, {"year": "1994", "text": "<PERSON>, Australian rugby league player", "html": "1994 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian rugby league player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian rugby league player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1994", "text": "<PERSON><PERSON><PERSON>, American baseball player", "html": "1994 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_Jr.\" title=\"<PERSON><PERSON><PERSON> Wade Jr.\"><PERSON><PERSON><PERSON>.</a>, American baseball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_Jr.\" title=\"<PERSON><PERSON><PERSON> Wade Jr.\"><PERSON><PERSON><PERSON>.</a>, American baseball player", "links": [{"title": "<PERSON><PERSON><PERSON>.", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>."}]}, {"year": "1995", "text": "<PERSON><PERSON><PERSON>, Iranian footballer", "html": "1995 - <a href=\"https://wikipedia.org/wiki/Sa<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Iranian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Iranian footballer", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON><PERSON><PERSON><PERSON>"}]}, {"year": "1995", "text": "<PERSON><PERSON>, American singer and YouTube personality", "html": "1995 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_(entertainer)\" class=\"mw-redirect\" title=\"<PERSON><PERSON> (entertainer)\"><PERSON><PERSON></a>, American singer and YouTube personality", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_(entertainer)\" class=\"mw-redirect\" title=\"<PERSON><PERSON> (entertainer)\"><PERSON><PERSON></a>, American singer and YouTube personality", "links": [{"title": "<PERSON><PERSON> (entertainer)", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_(entertainer)"}]}, {"year": "1996", "text": "<PERSON>, Brazilian footballer", "html": "1996 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Brazilian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Brazilian footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1996", "text": "<PERSON><PERSON><PERSON>, German footballer", "html": "1996 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, German footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, German footballer", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1996", "text": "<PERSON>, Danish footballer", "html": "1996 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Danish footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Danish footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1997", "text": "<PERSON>, American singer-songwriter", "html": "1997 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1997", "text": "<PERSON><PERSON>, Australian rugby league player", "html": "1997 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Australian rugby league player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Australian rugby league player", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1997", "text": "<PERSON><PERSON><PERSON>, Argentine footballer", "html": "1997 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Argentine footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Argentine footballer", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1998", "text": "<PERSON>, Moldovan-Spanish tennis player", "html": "1998 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>%C8%99a\" title=\"<PERSON>\"><PERSON></a>, Moldovan-Spanish tennis player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C8%99a\" title=\"<PERSON>\"><PERSON></a>, Moldovan-Spanish tennis player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Cristina_Buc%C8%99a"}]}, {"year": "1998", "text": "<PERSON><PERSON><PERSON>, Colombian footballer", "html": "1998 - <a href=\"https://wikipedia.org/wiki/Ed<PERSON><PERSON>_Cetr%C3%A9\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Colombian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Ed<PERSON><PERSON>_<PERSON>tr%C3%A9\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Colombian footballer", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Edwuin_Cetr%C3%A9"}]}, {"year": "1998", "text": "<PERSON><PERSON>, Zambian footballer", "html": "1998 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_M<PERSON>\" title=\"<PERSON><PERSON> M<PERSON>\"><PERSON><PERSON></a>, Zambian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_M<PERSON>\" title=\"En<PERSON> M<PERSON>pu\"><PERSON><PERSON></a>, Zambian footballer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Enock_Mwepu"}]}, {"year": "1998", "text": "<PERSON>, Nigerian footballer", "html": "1998 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Nigerian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Nigerian footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1999", "text": "<PERSON><PERSON>, Argentine footballer", "html": "1999 - <a href=\"https://wikipedia.org/wiki/Tom%C3%A1s_Chancalay\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Argentine footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Tom%C3%A1s_Chancalay\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Argentine footballer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Tom%C3%A1s_Chancalay"}]}, {"year": "1999", "text": "<PERSON><PERSON><PERSON>, Indonesian-Azerbaijani badminton player", "html": "1999 - <a href=\"https://wikipedia.org/wiki/A<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Indonesian-Azerbaijani badminton player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/A<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Indonesian-Azerbaijani badminton player", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Az<PERSON>_Qowim<PERSON>honi"}]}, {"year": "2000", "text": "<PERSON>, German footballer", "html": "2000 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%<PERSON>hn\" title=\"<PERSON>\"><PERSON></a>, German footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%<PERSON><PERSON>\" title=\"<PERSON>\"><PERSON></a>, German footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%BChn"}]}, {"year": "2000", "text": "<PERSON>, American rapper", "html": "2000 - <a href=\"https://wikipedia.org/wiki/Ice_Spice\" title=\"Ice Spice\"><PERSON></a>, American rapper", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Ice_Spice\" title=\"Ice Spice\"><PERSON> Spice</a>, American rapper", "links": [{"title": "Ice Spice", "link": "https://wikipedia.org/wiki/Ice_Spice"}]}, {"year": "2001", "text": "<PERSON><PERSON><PERSON>, Australian actress", "html": "2001 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Australian actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Australian actress", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "2001", "text": "<PERSON>, South Korean singer", "html": "2001 - <a href=\"https://wikipedia.org/wiki/Winter_(singer)\" title=\"<PERSON> (singer)\">Winter</a>, South Korean singer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Winter_(singer)\" title=\"<PERSON> (singer)\">Winter</a>, South Korean singer", "links": [{"title": "<PERSON> (singer)", "link": "https://wikipedia.org/wiki/<PERSON>_(singer)"}]}, {"year": "2002", "text": "<PERSON>, Ivorian footballer", "html": "2002 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Ivorian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Ivorian footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2003", "text": "<PERSON><PERSON>, Russian rhythmic gymnast", "html": "2003 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Russian rhythmic gymnast", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Russian rhythmic gymnast", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON><PERSON><PERSON>a"}]}, {"year": "2004", "text": "<PERSON><PERSON>, Senegalese footballer", "html": "2004 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Senegalese footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Senegalese footballer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>ine_<PERSON>ara"}]}, {"year": "2007", "text": "<PERSON>, Argentine footballer", "html": "2007 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Argentine footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Argentine footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}], "Deaths": [{"year": "138", "text": "<PERSON>, adopted son and intended successor of <PERSON><PERSON> (b. 101)", "html": "138 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>elius\" class=\"mw-redirect\" title=\"<PERSON> Aelius\"><PERSON></a>, adopted son and intended successor of <a href=\"https://wikipedia.org/wiki/Hadrian\" title=\"Had<PERSON>\"><PERSON><PERSON></a> (b. 101)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>elius\" class=\"mw-redirect\" title=\"<PERSON> Aelius\"><PERSON></a>, adopted son and intended successor of <a href=\"https://wikipedia.org/wiki/Hadrian\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a> (b. 101)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>"}, {"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>"}]}, {"year": "404", "text": "<PERSON><PERSON><PERSON>, Christian monk and martyr", "html": "404 - <a href=\"https://wikipedia.org/wiki/<PERSON>_Telemachus\" title=\"Saint Telemachus\"><PERSON><PERSON><PERSON></a>, Christian monk and martyr", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Saint_Telemachus\" title=\"Saint Telemachus\"><PERSON><PERSON><PERSON></a>, Christian monk and martyr", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Saint_Telemachus"}]}, {"year": "898", "text": "<PERSON><PERSON>, Frankish king (b. 860)", "html": "898 - <a href=\"https://wikipedia.org/wiki/Odo_of_France\" title=\"Odo of France\"><PERSON><PERSON> <PERSON></a>, Frankish king (b. 860)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Odo_of_France\" title=\"Odo of France\"><PERSON><PERSON> <PERSON></a>, Frankish king (b. 860)", "links": [{"title": "<PERSON><PERSON> of France", "link": "https://wikipedia.org/wiki/Odo_of_France"}]}, {"year": "951", "text": "<PERSON><PERSON>, king of León and Galicia", "html": "951 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_II_of_Le%C3%B3n\" title=\"<PERSON><PERSON> II of León\"><PERSON><PERSON> II</a>, king of León and Galicia", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_II_of_Le%C3%B3n\" title=\"<PERSON><PERSON> II of León\"><PERSON><PERSON> II</a>, king of León and Galicia", "links": [{"title": "Ramiro II of León", "link": "https://wikipedia.org/wiki/Ramiro_II_of_Le%C3%B3n"}]}, {"year": "1031", "text": "<PERSON> of Volpiano, Italian abbot (b. 962)", "html": "1031 - <a href=\"https://wikipedia.org/wiki/<PERSON>_of_Volpiano\" title=\"<PERSON> of Volpiano\"><PERSON> of Volpiano</a>, Italian abbot (b. 962)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_Volpiano\" title=\"<PERSON> of Volpiano\"><PERSON> of Volpiano</a>, Italian abbot (b. 962)", "links": [{"title": "<PERSON> of Volpiano", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_Vol<PERSON>"}]}, {"year": "1189", "text": "<PERSON> of Marcy, Cistercian abbot (b. c. 1136)", "html": "1189 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_Marcy\" title=\"<PERSON> of Marcy\"><PERSON> of Marcy</a>, Cistercian abbot (b. <abbr title=\"circa\">c.</abbr><span style=\"white-space:nowrap;\"> 1136</span>)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON> of Marcy\"><PERSON> of Marcy</a>, Cistercian abbot (b. <abbr title=\"circa\">c.</abbr><span style=\"white-space:nowrap;\"> 1136</span>)", "links": [{"title": "<PERSON> of Marcy", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_Marc<PERSON>"}]}, {"year": "1204", "text": "<PERSON><PERSON><PERSON> <PERSON>, king of Norway (b. 1182)", "html": "1204 - <a href=\"https://wikipedia.org/wiki/Haakon_III_of_Norway\" class=\"mw-redirect\" title=\"Haakon III of Norway\"><PERSON><PERSON><PERSON> <PERSON></a>, king of Norway (b. 1182)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Haakon_III_of_Norway\" class=\"mw-redirect\" title=\"Haakon III of Norway\"><PERSON><PERSON><PERSON> III</a>, king of Norway (b. 1182)", "links": [{"title": "Haakon III of Norway", "link": "https://wikipedia.org/wiki/Haakon_III_of_Norway"}]}, {"year": "1387", "text": "<PERSON>, king of Navarre (b. 1332)", "html": "1387 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Navarre\" title=\"<PERSON> of Navarre\"><PERSON> II</a>, king of Navarre (b. 1332)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Navarre\" title=\"<PERSON> of Navarre\"><PERSON> II</a>, king of Navarre (b. 1332)", "links": [{"title": "<PERSON> of Navarre", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Navarre"}]}, {"year": "1496", "text": "<PERSON>, count of Angoulême (b. 1459)", "html": "1496 - <a href=\"https://wikipedia.org/wiki/<PERSON>,_Count_of_Angoul%C3%AAme\" title=\"<PERSON>, Count of Angoulême\"><PERSON></a>, count of Angoulême (b. 1459)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>,_Count_of_Angoul%C3%AAme\" title=\"<PERSON>, Count of Angoulême\"><PERSON></a>, count of Angoulême (b. 1459)", "links": [{"title": "<PERSON>, Count of Angoulême", "link": "https://wikipedia.org/wiki/<PERSON>,_Count_of_Ang<PERSON><PERSON>%C3%AAme"}]}, {"year": "1515", "text": "<PERSON>, king of France (b. 1462)", "html": "1515 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_France\" class=\"mw-redirect\" title=\"<PERSON> of France\"><PERSON></a>, king of France (b. 1462)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_France\" class=\"mw-redirect\" title=\"<PERSON> of France\"><PERSON></a>, king of France (b. 1462)", "links": [{"title": "<PERSON> of France", "link": "https://wikipedia.org/wiki/Louis_<PERSON>_of_France"}]}, {"year": "1559", "text": "<PERSON>, king of Denmark (b. 1503)", "html": "1559 - <a href=\"https://wikipedia.org/wiki/Christian_III_of_Denmark\" title=\"Christian III of Denmark\"><PERSON></a>, king of Denmark (b. 1503)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Christian_III_of_Denmark\" title=\"Christian III of Denmark\"><PERSON> III</a>, king of Denmark (b. 1503)", "links": [{"title": "<PERSON> of Denmark", "link": "https://wikipedia.org/wiki/Christian_III_of_Denmark"}]}, {"year": "1560", "text": "<PERSON>, French poet and critic (b. 1522)", "html": "1560 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French poet and critic (b. 1522)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French poet and critic (b. 1522)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1617", "text": "<PERSON><PERSON><PERSON>, Dutch painter and illustrator (b. 1558)", "html": "1617 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Dutch painter and illustrator (b. 1558)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Dutch painter and illustrator (b. 1558)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1697", "text": "<PERSON><PERSON><PERSON>, Florentine historian and author (b. 1625)", "html": "1697 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Florentine historian and author (b. 1625)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Florentine historian and author (b. 1625)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1716", "text": "<PERSON>, English playwright and poet (b. 1641)", "html": "1716 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English playwright and poet (b. 1641)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English playwright and poet (b. 1641)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1748", "text": "<PERSON>, Swiss mathematician and academic (b. 1667)", "html": "1748 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swiss mathematician and academic (b. 1667)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swiss mathematician and academic (b. 1667)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1766", "text": "<PERSON>, Jacobite pretender (b. 1688)", "html": "1766 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Jacobite pretender (b. 1688)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Jacobite pretender (b. 1688)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1780", "text": "<PERSON>, German organist and composer (b. 1713)", "html": "1780 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German organist and composer (b. 1713)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German organist and composer (b. 1713)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1782", "text": "<PERSON>, German composer (b. 1735)", "html": "1782 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"Johann <PERSON>\"><PERSON></a>, German composer (b. 1735)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"Johann <PERSON>\"><PERSON></a>, German composer (b. 1735)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1789", "text": "<PERSON>, 1st Baron <PERSON>, English lawyer and politician, British Speaker of the House of Commons (b. 1716)", "html": "1789 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_1st_Baron_<PERSON>\" title=\"<PERSON>, 1st Baron <PERSON>\"><PERSON>, 1st Baron <PERSON></a>, English lawyer and politician, <a href=\"https://wikipedia.org/wiki/Speaker_of_the_House_of_Commons_(United_Kingdom)\" title=\"Speaker of the House of Commons (United Kingdom)\">British Speaker of the House of Commons</a> (b. 1716)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>,_1st_Baron_<PERSON>\" title=\"<PERSON>, 1st Baron <PERSON>\"><PERSON>, 1st Baron <PERSON></a>, English lawyer and politician, <a href=\"https://wikipedia.org/wiki/Speaker_of_the_House_of_Commons_(United_Kingdom)\" title=\"Speaker of the House of Commons (United Kingdom)\">British Speaker of the House of Commons</a> (b. 1716)", "links": [{"title": "<PERSON>, 1st Baron <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_1st_Baron_<PERSON>"}, {"title": "Speaker of the House of Commons (United Kingdom)", "link": "https://wikipedia.org/wiki/Speaker_of_the_House_of_Commons_(United_Kingdom)"}]}, {"year": "1793", "text": "<PERSON>, Italian painter and educator (b. 1712)", "html": "1793 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian painter and educator (b. 1712)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian painter and educator (b. 1712)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Francesco_Guardi"}]}, {"year": "1817", "text": "<PERSON>, German chemist and academic (b. 1743)", "html": "1817 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German chemist and academic (b. 1743)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German chemist and academic (b. 1743)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1846", "text": "<PERSON>, English sailor and explorer (b. 1825)", "html": "1846 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English sailor and explorer (b. 1825)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English sailor and explorer (b. 1825)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1853", "text": "<PERSON>, Australian farmer and explorer (b. 1778)", "html": "1853 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian farmer and explorer (b. 1778)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian farmer and explorer (b. 1778)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1862", "text": "<PERSON>, Ukrainian mathematician and physicist (b. 1801)", "html": "1862 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Ukrainian mathematician and physicist (b. 1801)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Ukrainian mathematician and physicist (b. 1801)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1881", "text": "<PERSON>, French activist (b. 1805)", "html": "1881 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French activist (b. 1805)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French activist (b. 1805)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1892", "text": "<PERSON><PERSON><PERSON> <PERSON><PERSON>, American lawyer and politician, 25th Mayor of Chicago (b. 1805)", "html": "1892 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON> Mason\"><PERSON><PERSON><PERSON> <PERSON><PERSON></a>, American lawyer and politician, 25th <a href=\"https://wikipedia.org/wiki/Mayor_of_Chicago\" title=\"Mayor of Chicago\">Mayor of Chicago</a> (b. 1805)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON> Mason\"><PERSON><PERSON><PERSON> <PERSON><PERSON></a>, American lawyer and politician, 25th <a href=\"https://wikipedia.org/wiki/Mayor_of_Chicago\" title=\"Mayor of Chicago\">Mayor of Chicago</a> (b. 1805)", "links": [{"title": "<PERSON><PERSON><PERSON> B. Mason", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON><PERSON>_<PERSON>"}, {"title": "Mayor of Chicago", "link": "https://wikipedia.org/wiki/Mayor_of_Chicago"}]}, {"year": "1894", "text": "<PERSON>, German physicist and academic (b. 1857)", "html": "1894 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German physicist and academic (b. 1857)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German physicist and academic (b. 1857)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1896", "text": "<PERSON>, American publisher and lawyer, created the Beach Pneumatic Transit (b. 1826)", "html": "1896 - <a href=\"https://wikipedia.org/wiki/Alfred_Ely_Beach\" title=\"Alfred Ely Beach\"><PERSON> Beach</a>, American publisher and lawyer, created the <a href=\"https://wikipedia.org/wiki/Beach_Pneumatic_Transit\" title=\"Beach Pneumatic Transit\">Beach Pneumatic Transit</a> (b. 1826)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Alfred_Ely_Beach\" title=\"Alfred Ely Beach\"><PERSON></a>, American publisher and lawyer, created the <a href=\"https://wikipedia.org/wiki/Beach_Pneumatic_Transit\" title=\"Beach Pneumatic Transit\">Beach Pneumatic Transit</a> (b. 1826)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Alfred_Ely_Beach"}, {"title": "Beach Pneumatic Transit", "link": "https://wikipedia.org/wiki/Beach_Pneumatic_Transit"}]}, {"year": "1901", "text": "<PERSON><PERSON><PERSON>, American politician, writer, and fringe scientist (b. 1831)", "html": "1901 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON> <PERSON>\"><PERSON><PERSON><PERSON> <PERSON></a>, American politician, writer, and fringe scientist (b. 1831)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON> <PERSON></a>, American politician, writer, and fringe scientist (b. 1831)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "1906", "text": "<PERSON>, Scottish-Australian farmer and politician, 11th Premier of Queensland (b. 1833)", "html": "1906 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>(Australian_politician)\" title=\"<PERSON> (Australian politician)\"><PERSON></a>, Scottish-Australian farmer and politician, 11th <a href=\"https://wikipedia.org/wiki/Premier_of_Queensland\" title=\"Premier of Queensland\">Premier of Queensland</a> (b. 1833)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>(Australian_politician)\" title=\"<PERSON> (Australian politician)\"><PERSON></a>, Scottish-Australian farmer and politician, 11th <a href=\"https://wikipedia.org/wiki/Premier_of_Queensland\" title=\"Premier of Queensland\">Premier of Queensland</a> (b. 1833)", "links": [{"title": "<PERSON> (Australian politician)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>(Australian_politician)"}, {"title": "Premier of Queensland", "link": "https://wikipedia.org/wiki/Premier_of_Queensland"}]}, {"year": "1918", "text": "<PERSON>, Canadian poet and author (b. 1858)", "html": "1918 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian poet and author (b. 1858)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian poet and author (b. 1858)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1921", "text": "<PERSON><PERSON><PERSON>, German lawyer and politician, 5th Chancellor of Germany (b. 1856)", "html": "1921 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>_<PERSON>weg\" title=\"<PERSON><PERSON><PERSON> von <PERSON>llweg\"><PERSON><PERSON><PERSON> <PERSON></a>, German lawyer and politician, 5th <a href=\"https://wikipedia.org/wiki/Chancellor_of_Germany\" title=\"Chancellor of Germany\">Chancellor of Germany</a> (b. 1856)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>_<PERSON>we<PERSON>\" title=\"<PERSON><PERSON><PERSON> von <PERSON>\"><PERSON><PERSON><PERSON> <PERSON></a>, German lawyer and politician, 5th <a href=\"https://wikipedia.org/wiki/Chancellor_of_Germany\" title=\"Chancellor of Germany\">Chancellor of Germany</a> (b. 1856)", "links": [{"title": "<PERSON><PERSON><PERSON> <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>_<PERSON>g"}, {"title": "Chancellor of Germany", "link": "https://wikipedia.org/wiki/Chancellor_of_Germany"}]}, {"year": "1923", "text": "<PERSON>, American baseball player (b. 1872)", "html": "1923 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player (b. 1872)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player (b. 1872)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1928", "text": "<PERSON><PERSON>, American dancer (b. 1862)", "html": "1928 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American dancer (b. 1862)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American dancer (b. 1862)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1929", "text": "<PERSON>, Turkish civil servant and politician, Turkish Minister of Environment and Urban Planning (b. 1894)", "html": "1929 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Turkish civil servant and politician, <a href=\"https://wikipedia.org/wiki/Ministry_of_Environment_and_Urban_Planning_(Turkey)\" class=\"mw-redirect\" title=\"Ministry of Environment and Urban Planning (Turkey)\">Turkish Minister of Environment and Urban Planning</a> (b. 1894)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Turkish civil servant and politician, <a href=\"https://wikipedia.org/wiki/Ministry_of_Environment_and_Urban_Planning_(Turkey)\" class=\"mw-redirect\" title=\"Ministry of Environment and Urban Planning (Turkey)\">Turkish Minister of Environment and Urban Planning</a> (b. 1894)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Ministry of Environment and Urban Planning (Turkey)", "link": "https://wikipedia.org/wiki/Ministry_of_Environment_and_Urban_Planning_(Turkey)"}]}, {"year": "1931", "text": "<PERSON><PERSON>, Dutch microbiologist and botanist (b. 1851)", "html": "1931 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Dutch microbiologist and botanist (b. 1851)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Dutch microbiologist and botanist (b. 1851)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1937", "text": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, Indian religious leader, founded the Gaudiya Math (b. 1874)", "html": "1937 - <a href=\"https://wikipedia.org/wiki/B<PERSON><PERSON><PERSON><PERSON><PERSON>_Sarasvati\" title=\"Bhaktisi<PERSON><PERSON><PERSON> Sarasvati\"><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON></a>, Indian religious leader, founded the <a href=\"https://wikipedia.org/wiki/Gaudiya_Math\" title=\"Gaudiya Math\">Gaudiya Math</a> (b. 1874)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>_Sarasvati\" title=\"B<PERSON><PERSON><PERSON><PERSON><PERSON> Sarasvati\"><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON></a>, Indian religious leader, founded the <a href=\"https://wikipedia.org/wiki/Gaudiya_Math\" title=\"Gaudiya Math\">Gaudiya Math</a> (b. 1874)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>sidd<PERSON><PERSON>_Sarasvati"}, {"title": "Gaudiya Math", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_Math"}]}, {"year": "1940", "text": "<PERSON><PERSON><PERSON>, Indian author and educator (b. 1865)", "html": "1940 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Indian author and educator (b. 1865)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Indian author and educator (b. 1865)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1943", "text": "<PERSON><PERSON>, Hungarian journalist (b. 1905)", "html": "1943 - <a href=\"https://wikipedia.org/wiki/Jen%C5%91_Rejt%C5%91\" title=\"<PERSON><PERSON> Rejtő\"><PERSON><PERSON></a>, Hungarian journalist (b. 1905)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Jen%C5%91_Rejt%C5%91\" title=\"<PERSON><PERSON> Rejtő\"><PERSON><PERSON></a>, Hungarian journalist (b. 1905)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Jen%C5%91_Rejt%C5%91"}]}, {"year": "1944", "text": "<PERSON>, English architect, designed the Castle Drogo and Thiepval Memorial (b. 1869)", "html": "1944 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English architect, designed the <a href=\"https://wikipedia.org/wiki/Castle_Drogo\" title=\"Castle Drogo\">Castle Drogo</a> and <a href=\"https://wikipedia.org/wiki/Thiepval_Memorial\" title=\"Thiep<PERSON> Memorial\">Thiepval Memorial</a> (b. 1869)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English architect, designed the <a href=\"https://wikipedia.org/wiki/Castle_Drogo\" title=\"Castle Drogo\">Castle Drogo</a> and <a href=\"https://wikipedia.org/wiki/Thiep<PERSON>_Memorial\" title=\"Thiep<PERSON> Memorial\">Thi<PERSON><PERSON> Memorial</a> (b. 1869)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Castle Drogo", "link": "https://wikipedia.org/wiki/Castle_Drogo"}, {"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Thiep<PERSON>_Memorial"}]}, {"year": "1944", "text": "<PERSON>, Australian cricketer (b. 1862)", "html": "1944 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(Australian_cricketer)\" title=\"<PERSON> (Australian cricketer)\"><PERSON></a>, Australian cricketer (b. 1862)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(Australian_cricketer)\" title=\"<PERSON> (Australian cricketer)\"><PERSON></a>, Australian cricketer (b. 1862)", "links": [{"title": "<PERSON> (Australian cricketer)", "link": "https://wikipedia.org/wiki/<PERSON>(Australian_cricketer)"}]}, {"year": "1953", "text": "<PERSON>, American singer-songwriter and guitarist (b. 1923)", "html": "1953 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and guitarist (b. 1923)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and guitarist (b. 1923)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1955", "text": "<PERSON><PERSON>, Indian colloid chemist, academic, and scientific administrator (b. 1894)", "html": "1955 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_Swarup_Bhatnagar\" title=\"<PERSON><PERSON> Bhatnagar\"><PERSON><PERSON></a>, Indian colloid chemist, academic, and scientific administrator (b. 1894)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_Swarup_Bhatnagar\" title=\"<PERSON><PERSON> Swarup Bhatnagar\"><PERSON><PERSON></a>, Indian colloid chemist, academic, and scientific administrator (b. 1894)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>up_Bhatnagar"}]}, {"year": "1954", "text": "<PERSON>, English politician and diplomat, Chancellor of the Duchy of Lancaster (b. 1890)", "html": "1954 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English politician and diplomat, <a href=\"https://wikipedia.org/wiki/Chancellor_of_the_Duchy_of_Lancaster\" title=\"Chancellor of the Duchy of Lancaster\">Chancellor of the Duchy of Lancaster</a> (b. 1890)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English politician and diplomat, <a href=\"https://wikipedia.org/wiki/Chancellor_of_the_Duchy_of_Lancaster\" title=\"Chancellor of the Duchy of Lancaster\">Chancellor of the Duchy of Lancaster</a> (b. 1890)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Chancellor of the Duchy of Lancaster", "link": "https://wikipedia.org/wiki/Chancellor_of_the_Duchy_of_Lancaster"}]}, {"year": "1954", "text": "<PERSON>, American poet and critic (b. 1887)", "html": "1954 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(poet)\" title=\"<PERSON> (poet)\"><PERSON></a>, American poet and critic (b. 1887)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(poet)\" title=\"<PERSON> (poet)\"><PERSON></a>, American poet and critic (b. 1887)", "links": [{"title": "<PERSON> (poet)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(poet)"}]}, {"year": "1955", "text": "<PERSON>, American archaeologist and historian (b. 1881)", "html": "1955 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American archaeologist and historian (b. 1881)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American archaeologist and historian (b. 1881)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "1958", "text": "<PERSON>, American photographer (b. 1886)", "html": "1958 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American photographer (b. 1886)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American photographer (b. 1886)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1960", "text": "<PERSON>, American actress (b. 1909)", "html": "1960 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress (b. 1909)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress (b. 1909)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1961", "text": "<PERSON><PERSON><PERSON>, Scottish cryptologist (b. 1881)", "html": "1961 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Scottish cryptologist (b. 1881)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Scottish cryptologist (b. 1881)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1966", "text": "<PERSON>, French journalist and politician, 16th President of the French Republic (b. 1884)", "html": "1966 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French journalist and politician, 16th <a href=\"https://wikipedia.org/wiki/President_of_the_French_Republic\" class=\"mw-redirect\" title=\"President of the French Republic\">President of the French Republic</a> (b. 1884)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French journalist and politician, 16th <a href=\"https://wikipedia.org/wiki/President_of_the_French_Republic\" class=\"mw-redirect\" title=\"President of the French Republic\">President of the French Republic</a> (b. 1884)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "President of the French Republic", "link": "https://wikipedia.org/wiki/President_of_the_French_Republic"}]}, {"year": "1969", "text": "<PERSON>, American actor, playwright and screenwriter (b. 1902)", "html": "1969 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor, playwright and screenwriter (b. 1902)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor, playwright and screenwriter (b. 1902)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1971", "text": "<PERSON><PERSON><PERSON><PERSON><PERSON> of Pochayiv, Ukrainian saint (b. 1894)", "html": "1971 - <a href=\"https://wikipedia.org/wiki/Amphi<PERSON><PERSON><PERSON>_of_Pochayiv\" title=\"<PERSON>phi<PERSON><PERSON><PERSON> of Pochayiv\"><PERSON><PERSON><PERSON><PERSON><PERSON> of Pochayiv</a>, Ukrainian saint (b. 1894)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON><PERSON>_of_Pochayiv\" title=\"<PERSON><PERSON><PERSON><PERSON><PERSON> of Pochayiv\"><PERSON><PERSON><PERSON><PERSON><PERSON> of Pochayiv</a>, Ukrainian saint (b. 1894)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON><PERSON> of Pochayiv", "link": "https://wikipedia.org/wiki/<PERSON>phi<PERSON><PERSON><PERSON>_of_Pochayiv"}]}, {"year": "1972", "text": "<PERSON>, French actor and singer (b. 1888)", "html": "1972 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French actor and singer (b. 1888)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French actor and singer (b. 1888)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1977", "text": "<PERSON>, American lyric tenor and composer (b. 1887)", "html": "1977 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lyric tenor and composer (b. 1887)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lyric tenor and composer (b. 1887)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1978", "text": "<PERSON><PERSON>, German-Canadian painter (b. 1911)", "html": "1978 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, German-Canadian painter (b. 1911)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, German-Canadian painter (b. 1911)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1980", "text": "<PERSON>, Italian journalist and politician, Italian Minister of Foreign Affairs (b. 1891)", "html": "1980 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian journalist and politician, <a href=\"https://wikipedia.org/wiki/Italian_Minister_of_Foreign_Affairs\" class=\"mw-redirect\" title=\"Italian Minister of Foreign Affairs\">Italian Minister of Foreign Affairs</a> (b. 1891)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian journalist and politician, <a href=\"https://wikipedia.org/wiki/Italian_Minister_of_Foreign_Affairs\" class=\"mw-redirect\" title=\"Italian Minister of Foreign Affairs\">Italian Minister of Foreign Affairs</a> (b. 1891)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Italian Minister of Foreign Affairs", "link": "https://wikipedia.org/wiki/Italian_Minister_of_Foreign_Affairs"}]}, {"year": "1981", "text": "<PERSON><PERSON><PERSON><PERSON>, American-Australian pianist (b. 1920)", "html": "1981 - <a href=\"https://wikipedia.org/wiki/Hephzibah_Menuhin\" title=\"Hephzibah Menuhin\"><PERSON><PERSON><PERSON><PERSON></a>, American-Australian pianist (b. 1920)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Heph<PERSON>bah_Menuhin\" title=\"Hephzibah Menuhin\"><PERSON><PERSON><PERSON><PERSON></a>, American-Australian pianist (b. 1920)", "links": [{"title": "Hephzi<PERSON> Menuhin", "link": "https://wikipedia.org/wiki/Heph<PERSON><PERSON>_Menuhin"}]}, {"year": "1982", "text": "<PERSON>, American actor (b. 1938)", "html": "1982 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor (b. 1938)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor (b. 1938)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1984", "text": "<PERSON>, French-English singer-songwriter and guitarist (b. 1928)", "html": "1984 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French-English singer-songwriter and guitarist (b. 1928)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French-English singer-songwriter and guitarist (b. 1928)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1984", "text": "<PERSON><PERSON><PERSON><PERSON>, known as \"<PERSON><PERSON><PERSON>\", Spanish bullfighter (b. 1903)", "html": "1984 - <a href=\"https://wikipedia.org/wiki/Joaqu%C3%ADn_Rodr%C3%ADguez_Ortega\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, known as \"<PERSON><PERSON><PERSON>\", Spanish bullfighter (b. 1903)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Joaqu%C3%ADn_Rodr%C3%ADguez_Ortega\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, known as \"<PERSON><PERSON><PERSON>\", Spanish bullfighter (b. 1903)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Joaqu%C3%ADn_Rodr%C3%ADguez_Ortega"}]}, {"year": "1988", "text": "<PERSON><PERSON>, American folk artist (b. 1886 or 1887)", "html": "1988 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> Hunter\"><PERSON><PERSON></a>, American folk artist (b. 1886 or 1887)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> Hunter\"><PERSON><PERSON></a>, American folk artist (b. 1886 or 1887)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1992", "text": "<PERSON>, American computer scientist and admiral, co-developed CO<PERSON><PERSON> (b. 1906)", "html": "1992 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American computer scientist and admiral, co-developed <a href=\"https://wikipedia.org/wiki/COBOL\" title=\"COBOL\">COBOL</a> (b. 1906)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American computer scientist and admiral, co-developed <a href=\"https://wikipedia.org/wiki/COBOL\" title=\"COBOL\">COBOL</a> (b. 1906)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "COBOL", "link": "https://wikipedia.org/wiki/COBOL"}]}, {"year": "1994", "text": "<PERSON>, <PERSON>, New Zealand physician and politician, 11th Governor-General of New Zealand (b. 1900)", "html": "1994 - <a href=\"https://wikipedia.org/wiki/<PERSON>,_Baron_<PERSON>\" title=\"<PERSON>, Baron <PERSON>\"><PERSON>, Baron <PERSON></a>, New Zealand physician and politician, 11th <a href=\"https://wikipedia.org/wiki/Governor-General_of_New_Zealand\" title=\"Governor-General of New Zealand\">Governor-General of New Zealand</a> (b. 1900)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>,_Baron_<PERSON>\" title=\"<PERSON>, Baron <PERSON>\"><PERSON>, Baron <PERSON></a>, New Zealand physician and politician, 11th <a href=\"https://wikipedia.org/wiki/Governor-General_of_New_Zealand\" title=\"Governor-General of New Zealand\">Governor-General of New Zealand</a> (b. 1900)", "links": [{"title": "<PERSON>, Baron <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>,_Baron_<PERSON>"}, {"title": "Governor-General of New Zealand", "link": "https://wikipedia.org/wiki/Governor-General_of_New_Zealand"}]}, {"year": "1994", "text": "<PERSON><PERSON>, American actor (b. 1907)", "html": "1994 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American actor (b. 1907)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American actor (b. 1907)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1994", "text": "<PERSON>, Irish historian and academic (b. 1914)", "html": "1994 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, Irish historian and academic (b. 1914)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, Irish historian and academic (b. 1914)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1995", "text": "<PERSON>, Hungarian-American physicist and mathematician, Nobel Prize laureate (b. 1902)", "html": "1995 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Hungarian-American physicist and mathematician, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Physics\" title=\"Nobel Prize in Physics\">Nobel Prize</a> laureate (b. 1902)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Hungarian-American physicist and mathematician, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Physics\" title=\"Nobel Prize in Physics\">Nobel Prize</a> laureate (b. 1902)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Nobel Prize in Physics", "link": "https://wikipedia.org/wiki/Nobel_Prize_in_Physics"}]}, {"year": "1996", "text": "<PERSON><PERSON><PERSON>, American admiral (b. 1901)", "html": "1996 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American admiral (b. 1901)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American admiral (b. 1901)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1996", "text": "<PERSON>, German-American engineer (b. 1906)", "html": "1996 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German-American engineer (b. 1906)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German-American engineer (b. 1906)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1997", "text": "<PERSON><PERSON>, American singer-songwriter, guitarist, and producer (b. 1944)", "html": "1997 - <a href=\"https://wikipedia.org/wiki/Townes_Van_Zandt\" title=\"Townes Van Zandt\"><PERSON><PERSON> Van Z<PERSON></a>, American singer-songwriter, guitarist, and producer (b. 1944)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Townes_Van_Zandt\" title=\"Townes Van Zandt\"><PERSON><PERSON> Z<PERSON></a>, American singer-songwriter, guitarist, and producer (b. 1944)", "links": [{"title": "Townes Van <PERSON>t", "link": "https://wikipedia.org/wiki/Townes_Van_Z<PERSON>t"}]}, {"year": "1998", "text": "<PERSON>, American tennis player and coach (b. 1905)", "html": "1998 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American tennis player and coach (b. 1905)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American tennis player and coach (b. 1905)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2001", "text": "<PERSON>, American actor (b. 1914)", "html": "2001 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor (b. 1914)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor (b. 1914)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2002", "text": "<PERSON>, American film producer and author (b. 1944)", "html": "2002 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American film producer and author (b. 1944)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American film producer and author (b. 1944)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2003", "text": "<PERSON>, American soldier, pilot, and politician, 20th Governor of South Dakota (b. 1915)", "html": "2003 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American soldier, pilot, and politician, 20th <a href=\"https://wikipedia.org/wiki/Governor_of_South_Dakota\" title=\"Governor of South Dakota\">Governor of South Dakota</a> (b. 1915)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American soldier, pilot, and politician, 20th <a href=\"https://wikipedia.org/wiki/Governor_of_South_Dakota\" title=\"Governor of South Dakota\">Governor of South Dakota</a> (b. 1915)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Governor of South Dakota", "link": "https://wikipedia.org/wiki/Governor_of_South_Dakota"}]}, {"year": "2005", "text": "<PERSON>, American educator and politician (b. 1924)", "html": "2005 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American educator and politician (b. 1924)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American educator and politician (b. 1924)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2005", "text": "<PERSON><PERSON>, Vietnamese revolutionary (b. 1913)", "html": "2005 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Vietnamese revolutionary (b. 1913)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> <PERSON>\"><PERSON><PERSON></a>, Vietnamese revolutionary (b. 1913)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "2006", "text": "<PERSON>, American economist and journalist (b. 1913)", "html": "2006 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American economist and journalist (b. 1913)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American economist and journalist (b. 1913)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2007", "text": "<PERSON>, South African-English biochemist and academic (b. 1943)", "html": "2007 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, South African-English biochemist and academic (b. 1943)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, South African-English biochemist and academic (b. 1943)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "2007", "text": "<PERSON><PERSON>, American short story writer (b. 1912)", "html": "2007 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American short story writer (b. 1912)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American short story writer (b. 1912)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "2007", "text": "<PERSON><PERSON>, American football player (b. 1982)", "html": "2007 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American football player (b. 1982)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American football player (b. 1982)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "2008", "text": "<PERSON><PERSON><PERSON>, Indian educator and politician (b. 1919)", "html": "2008 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Indian educator and politician (b. 1919)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON> <PERSON></a>, Indian educator and politician (b. 1919)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>"}]}, {"year": "2009", "text": "<PERSON><PERSON><PERSON>, American politician (b. 1918)", "html": "2009 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American politician (b. 1918)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American politician (b. 1918)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON><PERSON>"}]}, {"year": "2009", "text": "<PERSON>, South African anti-apartheid activist and politician (b. 1917)", "html": "2009 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, South African anti-apartheid activist and politician (b. 1917)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, South African anti-apartheid activist and politician (b. 1917)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2010", "text": "<PERSON><PERSON>, American-Mexican singer-songwriter (b. 1972)", "html": "2010 - <a href=\"https://wikipedia.org/wiki/Lhasa_de_Sela\" title=\"Lhasa de Sela\"><PERSON><PERSON> <PERSON></a>, American-Mexican singer-songwriter (b. 1972)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Lhasa_de_Sela\" title=\"Lhasa de Sela\"><PERSON><PERSON> <PERSON></a>, American-Mexican singer-songwriter (b. 1972)", "links": [{"title": "Lhasa de Sela", "link": "https://wikipedia.org/wiki/Lhasa_de_Sela"}]}, {"year": "2012", "text": "<PERSON>, English fencer (b. 1922)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(fencer)\" title=\"<PERSON> (fencer)\"><PERSON></a>, English fencer (b. 1922)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(fencer)\" title=\"<PERSON> (fencer)\"><PERSON></a>, English fencer (b. 1922)", "links": [{"title": "<PERSON> (fencer)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(fencer)"}]}, {"year": "2012", "text": "<PERSON><PERSON>, Macedonian lawyer and politician, 1st President of the Republic of Macedonia (b. 1917)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Macedonian lawyer and politician, 1st <a href=\"https://wikipedia.org/wiki/President_of_the_Republic_of_Macedonia\" class=\"mw-redirect\" title=\"President of the Republic of Macedonia\">President of the Republic of Macedonia</a> (b. 1917)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Macedonian lawyer and politician, 1st <a href=\"https://wikipedia.org/wiki/President_of_the_Republic_of_Macedonia\" class=\"mw-redirect\" title=\"President of the Republic of Macedonia\">President of the Republic of Macedonia</a> (b. 1917)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}, {"title": "President of the Republic of Macedonia", "link": "https://wikipedia.org/wiki/President_of_the_Republic_of_Macedonia"}]}, {"year": "2012", "text": "<PERSON><PERSON>, Burmese physician, businessman, and activist (b. 1962)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Burmese physician, businessman, and activist (b. 1962)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Burmese physician, businessman, and activist (b. 1962)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>"}]}, {"year": "2012", "text": "<PERSON>, American football player and coach (b. 1922)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player and coach (b. 1922)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Mont\"><PERSON></a>, American football player and coach (b. 1922)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2013", "text": "<PERSON>, English journalist (b. 1945)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English journalist (b. 1945)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English journalist (b. 1945)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2013", "text": "<PERSON>, American singer and actress (b. 1927)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer and actress (b. 1927)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer and actress (b. 1927)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_Page"}]}, {"year": "2014", "text": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, Japanese monk and educator (b. 1910)", "html": "2014 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON></a>, Japanese monk and educator (b. 1910)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON></a>, Japanese monk and educator (b. 1910)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>_<PERSON>de"}]}, {"year": "2014", "text": "<PERSON>, Tanzanian banker and politician, 13th Tanzanian Minister of Finance (b. 1950)", "html": "2014 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Tanzanian banker and politician, 13th <a href=\"https://wikipedia.org/wiki/Minister_of_Finance_(Tanzania)\" title=\"Minister of Finance (Tanzania)\">Tanzanian Minister of Finance</a> (b. 1950)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Tanzanian banker and politician, 13th <a href=\"https://wikipedia.org/wiki/Minister_of_Finance_(Tanzania)\" title=\"Minister of Finance (Tanzania)\">Tanzanian Minister of Finance</a> (b. 1950)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Minister of Finance (Tanzania)", "link": "https://wikipedia.org/wiki/Minister_of_Finance_(Tanzania)"}]}, {"year": "2014", "text": "<PERSON><PERSON>, American actress (b. 1914)", "html": "2014 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American actress (b. 1914)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American actress (b. 1914)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "2015", "text": "<PERSON>, German sociologist (b. 1944)", "html": "2015 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German sociologist (b. 1944)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German sociologist (b. 1944)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "2015", "text": "<PERSON>, American lawyer and politician, 52nd Governor of New York (b. 1932)", "html": "2015 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and politician, 52nd <a href=\"https://wikipedia.org/wiki/Governor_of_New_York\" title=\"Governor of New York\">Governor of New York</a> (b. 1932)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and politician, 52nd <a href=\"https://wikipedia.org/wiki/Governor_of_New_York\" title=\"Governor of New York\">Governor of New York</a> (b. 1932)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Governor of New York", "link": "https://wikipedia.org/wiki/Governor_of_New_York"}]}, {"year": "2015", "text": "<PERSON>, American actress (b. 1932)", "html": "2015 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress (b. 1932)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress (b. 1932)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2015", "text": "<PERSON>, Lebanese lawyer and politician, 58th Prime Minister of Lebanon (b. 1934)", "html": "2015 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Lebanese lawyer and politician, 58th <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Lebanon\" title=\"Prime Minister of Lebanon\">Prime Minister of Lebanon</a> (b. 1934)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Lebanese lawyer and politician, 58th <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Lebanon\" title=\"Prime Minister of Lebanon\">Prime Minister of Lebanon</a> (b. 1934)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Prime Minister of Lebanon", "link": "https://wikipedia.org/wiki/Prime_Minister_of_Lebanon"}]}, {"year": "2015", "text": "<PERSON>, Russian physician and astronaut (b. 1950)", "html": "2015 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian physician and astronaut (b. 1950)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian physician and astronaut (b. 1950)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "2015", "text": "<PERSON>, United States District Judge (b. 1930)", "html": "2015 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, <a href=\"https://wikipedia.org/wiki/United_States_District_Judge\" class=\"mw-redirect\" title=\"United States District Judge\">United States District Judge</a> (b. 1930)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, <a href=\"https://wikipedia.org/wiki/United_States_District_Judge\" class=\"mw-redirect\" title=\"United States District Judge\">United States District Judge</a> (b. 1930)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "United States District Judge", "link": "https://wikipedia.org/wiki/United_States_District_Judge"}]}, {"year": "2016", "text": "<PERSON><PERSON><PERSON>, Russian poet and journalist (b. 1932)", "html": "2016 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Russian poet and journalist (b. 1932)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Russian poet and journalist (b. 1932)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>a"}]}, {"year": "2016", "text": "<PERSON>, American soldier, lawyer, and politician, 38th Governor of Arkansas (b. 1925)", "html": "2016 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American soldier, lawyer, and politician, 38th <a href=\"https://wikipedia.org/wiki/Governor_of_Arkansas\" class=\"mw-redirect\" title=\"Governor of Arkansas\">Governor of Arkansas</a> (b. 1925)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American soldier, lawyer, and politician, 38th <a href=\"https://wikipedia.org/wiki/Governor_of_Arkansas\" class=\"mw-redirect\" title=\"Governor of Arkansas\">Governor of Arkansas</a> (b. 1925)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Governor of Arkansas", "link": "https://wikipedia.org/wiki/Governor_of_Arkansas"}]}, {"year": "2016", "text": "<PERSON><PERSON><PERSON>, Hungarian-American cinematographer and producer (b. 1930)", "html": "2016 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Hungarian-American cinematographer and producer (b. 1930)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Hungarian-American cinematographer and producer (b. 1930)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Vil<PERSON>_<PERSON>"}]}, {"year": "2017", "text": "<PERSON>, British economist (b. 1944)", "html": "2017 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, British economist (b. 1944)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, British economist (b. 1944)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2017", "text": "<PERSON><PERSON>, Canadian politician (b. 1926)", "html": "2017 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Canadian politician (b. 1926)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Canadian politician (b. 1926)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "2018", "text": "<PERSON>, American violinist (b. 1920)", "html": "2018 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American violinist (b. 1920)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American violinist (b. 1920)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2018", "text": "<PERSON>, American actor (b. 1984)", "html": "2018 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor (b. 1984)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor (b. 1984)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2019", "text": "<PERSON>, Australian politician (b. 1940)", "html": "2019 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>(politician)\" title=\"<PERSON> (politician)\"><PERSON></a>, Australian politician (b. 1940)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>(politician)\" title=\"<PERSON> (politician)\"><PERSON></a>, Australian politician (b. 1940)", "links": [{"title": "<PERSON> (politician)", "link": "https://wikipedia.org/wiki/<PERSON>(politician)"}]}, {"year": "2019", "text": "<PERSON><PERSON><PERSON>, American singer, songwriter, environmentalist, educator and philanthropist (b. 1952) ", "html": "2019 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American singer, songwriter, environmentalist, educator and philanthropist (b. <a href=\"https://wikipedia.org/wiki/1952\" title=\"1952\">1952</a>) ", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American singer, songwriter, environmentalist, educator and philanthropist (b. <a href=\"https://wikipedia.org/wiki/1952\" title=\"1952\">1952</a>) ", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Pegi_Young"}, {"title": "1952", "link": "https://wikipedia.org/wiki/1952"}]}, {"year": "2019", "text": "<PERSON>, last known <PERSON><PERSON><PERSON><PERSON> (b. c. 2004)", "html": "2019 - <a href=\"https://wikipedia.org/wiki/<PERSON>_(snail)\" title=\"<PERSON> (snail)\"><PERSON></a>, <a href=\"https://wikipedia.org/wiki/Endling\" title=\"Endling\">last known</a> <i><a href=\"https://wikipedia.org/wiki/Achatinella_apexfulva\" title=\"Achatinella apexfulva\">Achatinella apexfulva</a></i> (b. <abbr title=\"circa\">c.</abbr><span style=\"white-space:nowrap;\"> 2004</span>)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(snail)\" title=\"<PERSON> (snail)\"><PERSON></a>, <a href=\"https://wikipedia.org/wiki/Endling\" title=\"Endling\">last known</a> <i><a href=\"https://wikipedia.org/wiki/Achatinella_apexfulva\" title=\"Achatinella apexfulva\">Achatinella apexfulva</a></i> (b. <abbr title=\"circa\">c.</abbr><span style=\"white-space:nowrap;\"> 2004</span>)", "links": [{"title": "<PERSON> (snail)", "link": "https://wikipedia.org/wiki/<PERSON>_(snail)"}, {"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Endling"}, {"title": "<PERSON><PERSON><PERSON><PERSON>va", "link": "https://wikipedia.org/wiki/Achatinella_apexfulva"}]}, {"year": "2020", "text": "<PERSON>, American rapper (b. 1998)", "html": "2020 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON></a>, American rapper (b. 1998)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON></a>, American rapper (b. 1998)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>i_<PERSON>i"}]}, {"year": "2020", "text": "<PERSON>, British travel writer and journalist (b. 1937)", "html": "2020 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, British travel writer and journalist (b. 1937)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, British travel writer and journalist (b. 1937)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2020", "text": "<PERSON>, American baseball player (b. 1929)", "html": "2020 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player (b. 1929)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player (b. 1929)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2020", "text": "<PERSON>, Australian rugby union player (b. 1940)", "html": "2020 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(rugby_union)\" title=\"<PERSON> (rugby union)\"><PERSON></a>, Australian rugby union player (b. 1940)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(rugby_union)\" title=\"<PERSON> (rugby union)\"><PERSON></a>, Australian rugby union player (b. 1940)", "links": [{"title": "<PERSON> (rugby union)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(rugby_union)"}]}, {"year": "2020", "text": "<PERSON>, American lawyer and businessman (b. 1942)", "html": "2020 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and businessman (b. 1942)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and businessman (b. 1942)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2021", "text": "<PERSON>, Portuguese fado singer (b. 1939)", "html": "2021 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Portuguese <a href=\"https://wikipedia.org/wiki/Fado\" title=\"Fado\">fado</a> singer (b. 1939)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Portuguese <a href=\"https://wikipedia.org/wiki/Fado\" title=\"Fado\">fado</a> singer (b. 1939)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "Fado", "link": "https://wikipedia.org/wiki/Fado"}]}, {"year": "2021", "text": "<PERSON>, English actor (b. 1928)", "html": "2021 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Mark <PERSON>\"><PERSON></a>, English actor (b. 1928)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Mark Eden\"><PERSON></a>, English actor (b. 1928)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2021", "text": "<PERSON><PERSON>, Belizean educator and psychologist (b. 1930)", "html": "2021 - <a href=\"https://wikipedia.org/wiki/Elm<PERSON>_Minita_Gordon\" title=\"Elmira Minita Gordon\"><PERSON><PERSON> Mini<PERSON></a>, Belizean educator and psychologist (b. 1930)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Elmira_Minita_Gordon\" title=\"Elmira Minita Gordon\"><PERSON><PERSON></a>, Belizean educator and psychologist (b. 1930)", "links": [{"title": "Elmira Minita Gordon", "link": "https://wikipedia.org/wiki/Elmira_Minita_Gordon"}]}, {"year": "2021", "text": "<PERSON>, American football player (b. 1942)", "html": "2021 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Little\"><PERSON></a>, American football player (b. 1942)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Little\"><PERSON></a>, American football player (b. 1942)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2022", "text": "<PERSON>, British broadcaster and journalist (b. 1975)", "html": "2022 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, British broadcaster and journalist (b. 1975)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, British broadcaster and journalist (b. 1975)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2022", "text": "<PERSON>, American football player and coach (b. 1944)", "html": "2022 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player and coach (b. 1944)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player and coach (b. 1944)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2023", "text": "<PERSON>, American musician and songwriter (b. 1955)", "html": "2023 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(musician)\" title=\"<PERSON> (musician)\"><PERSON></a>, American musician and songwriter (b. 1955)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(musician)\" title=\"<PERSON> (musician)\"><PERSON></a>, American musician and songwriter (b. 1955)", "links": [{"title": "<PERSON> (musician)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>(musician)"}]}, {"year": "2024", "text": "<PERSON><PERSON><PERSON>, American celebrity chef and YouTuber  (b. 1956)", "html": "2024 - <a href=\"https://wikipedia.org/wiki/Lynja\" title=\"Lynja\"><PERSON><PERSON><PERSON></a>, American celebrity chef and YouTuber (b. 1956)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Lynja\" title=\"Lynja\"><PERSON><PERSON><PERSON></a>, American celebrity chef and YouTuber (b. 1956)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Lynja"}]}, {"year": "2025", "text": "<PERSON>, English author and critic (b. 1935)", "html": "2025 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(author)\" title=\"<PERSON> (author)\"><PERSON></a>, English author and critic (b. 1935)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(author)\" title=\"<PERSON> (author)\"><PERSON></a>, English author and critic (b. 1935)", "links": [{"title": "<PERSON> (author)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(author)"}]}, {"year": "2025", "text": "<PERSON>, Australian musician (b. 1933)", "html": "2025 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian musician (b. 1933)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian musician (b. 1933)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2025", "text": "<PERSON>, American singer-songwriter and actor (b. 1951)", "html": "2025 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and actor (b. 1951)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and actor (b. 1951)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}]}}