{"date": "March 18", "url": "https://wikipedia.org/wiki/March_18", "data": {"Events": [{"year": "37", "text": "Roman Senate annuls <PERSON><PERSON><PERSON>' will and proclaims <PERSON> Julius <PERSON> (aka Caligula = Little Boots) emperor.", "html": "37 - Roman Senate annuls <a href=\"https://wikipedia.org/wiki/Tiberius\" title=\"Tiber<PERSON>\"><PERSON><PERSON><PERSON></a>' will and proclaims <a href=\"https://wikipedia.org/wiki/Caligula\" title=\"Caligula\">Gaius Julius <PERSON></a> <i>(aka <a href=\"https://wikipedia.org/wiki/Caligula\" title=\"Caligula\">Caligula</a> = <PERSON>)</i> emperor.", "no_year_html": "Roman Senate annuls <a href=\"https://wikipedia.org/wiki/Tiberius\" title=\"Tiber<PERSON>\"><PERSON><PERSON><PERSON></a>' will and proclaims <a href=\"https://wikipedia.org/wiki/Caligula\" title=\"Caligula\">Gaius <PERSON></a> <i>(aka <a href=\"https://wikipedia.org/wiki/Caligula\" title=\"Caligula\">Caligula</a> = <PERSON> Boots)</i> emperor.", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Tiberius"}, {"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Caligula"}, {"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Caligula"}]}, {"year": "1068", "text": "An earthquake in the Levant and the Arabian Peninsula leaves up to 20,000 dead.", "html": "1068 - An <a href=\"https://wikipedia.org/wiki/1068_Near_East_earthquake\" title=\"1068 Near East earthquake\">earthquake</a> in the <a href=\"https://wikipedia.org/wiki/Levant\" title=\"Levant\">Levant</a> and the <a href=\"https://wikipedia.org/wiki/Arabian_Peninsula\" title=\"Arabian Peninsula\">Arabian Peninsula</a> leaves up to 20,000 dead.", "no_year_html": "An <a href=\"https://wikipedia.org/wiki/1068_Near_East_earthquake\" title=\"1068 Near East earthquake\">earthquake</a> in the <a href=\"https://wikipedia.org/wiki/Levant\" title=\"Levant\">Levant</a> and the <a href=\"https://wikipedia.org/wiki/Arabian_Peninsula\" title=\"Arabian Peninsula\">Arabian Peninsula</a> leaves up to 20,000 dead.", "links": [{"title": "1068 Near East earthquake", "link": "https://wikipedia.org/wiki/1068_Near_East_earthquake"}, {"title": "Levant", "link": "https://wikipedia.org/wiki/Levant"}, {"title": "Arabian Peninsula", "link": "https://wikipedia.org/wiki/Arabian_Peninsula"}]}, {"year": "1229", "text": "<PERSON>, Holy Roman Emperor, declares himself King of Jerusalem in the Sixth Crusade.", "html": "1229 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Holy_Roman_Emperor\" title=\"<PERSON> II, Holy Roman Emperor\"><PERSON> II, Holy Roman Emperor</a>, declares himself <a href=\"https://wikipedia.org/wiki/King_of_Jerusalem\" title=\"King of Jerusalem\">King of Jerusalem</a> in the <a href=\"https://wikipedia.org/wiki/Sixth_Crusade\" title=\"Sixth Crusade\">Sixth Crusade</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Holy_Roman_Emperor\" title=\"<PERSON> II, Holy Roman Emperor\"><PERSON> II, Holy Roman Emperor</a>, declares himself <a href=\"https://wikipedia.org/wiki/King_of_Jerusalem\" title=\"King of Jerusalem\">King of Jerusalem</a> in the <a href=\"https://wikipedia.org/wiki/Sixth_Crusade\" title=\"Sixth Crusade\">Sixth Crusade</a>.", "links": [{"title": "<PERSON>, Holy Roman Emperor", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Holy_Roman_Emperor"}, {"title": "King of Jerusalem", "link": "https://wikipedia.org/wiki/King_of_Jerusalem"}, {"title": "Sixth Crusade", "link": "https://wikipedia.org/wiki/Sixth_Crusade"}]}, {"year": "1241", "text": "First Mongol invasion of Poland: Mongols overwhelm Polish armies in Kraków in the Battle of Chmielnik and plunder the city.", "html": "1241 - <a href=\"https://wikipedia.org/wiki/First_Mongol_invasion_of_Poland\" title=\"First Mongol invasion of Poland\">First Mongol invasion of Poland</a>: <a href=\"https://wikipedia.org/wiki/Mongol_Empire\" title=\"Mongol Empire\">Mongols</a> overwhelm <a href=\"https://wikipedia.org/wiki/History_of_Poland_during_the_Piast_dynasty\" title=\"History of Poland during the Piast dynasty\">Polish</a> armies in <a href=\"https://wikipedia.org/wiki/Krak%C3%B3w\" title=\"Kraków\">Kraków</a> in the <a href=\"https://wikipedia.org/wiki/Battle_of_Chmielnik\" title=\"Battle of Chmielnik\">Battle of Chmielnik</a> and plunder the city.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/First_Mongol_invasion_of_Poland\" title=\"First Mongol invasion of Poland\">First Mongol invasion of Poland</a>: <a href=\"https://wikipedia.org/wiki/Mongol_Empire\" title=\"Mongol Empire\">Mongols</a> overwhelm <a href=\"https://wikipedia.org/wiki/History_of_Poland_during_the_Piast_dynasty\" title=\"History of Poland during the Piast dynasty\">Polish</a> armies in <a href=\"https://wikipedia.org/wiki/Krak%C3%B3w\" title=\"Kraków\">Kraków</a> in the <a href=\"https://wikipedia.org/wiki/Battle_of_Chmielnik\" title=\"Battle of Chmielnik\">Battle of Chmielnik</a> and plunder the city.", "links": [{"title": "First Mongol invasion of Poland", "link": "https://wikipedia.org/wiki/First_Mongol_invasion_of_Poland"}, {"title": "Mongol Empire", "link": "https://wikipedia.org/wiki/Mongol_Empire"}, {"title": "History of Poland during the Piast dynasty", "link": "https://wikipedia.org/wiki/History_of_Poland_during_the_Piast_dynasty"}, {"title": "Kraków", "link": "https://wikipedia.org/wiki/Krak%C3%B3w"}, {"title": "Battle of Chmielnik", "link": "https://wikipedia.org/wiki/Battle_of_Chmielnik"}]}, {"year": "1314", "text": "<PERSON>, the 23rd and final Grand Master of the Knights Templar, is burned at the stake.", "html": "1314 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, the 23rd and final Grand Master of the <a href=\"https://wikipedia.org/wiki/Knights_Templar\" title=\"Knights Templar\">Knights Templar</a>, is burned at the stake.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, the 23rd and final Grand Master of the <a href=\"https://wikipedia.org/wiki/Knights_Templar\" title=\"Knights Templar\">Knights Templar</a>, is burned at the stake.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "Knights Templar", "link": "https://wikipedia.org/wiki/Knights_Templar"}]}, {"year": "1438", "text": "<PERSON> of Habsburg becomes King of the Romans.", "html": "1438 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_Habsburg\" class=\"mw-redirect\" title=\"<PERSON> II of Habsburg\"><PERSON> of Habsburg</a> becomes <a href=\"https://wikipedia.org/wiki/King_of_the_Romans\" title=\"King of the Romans\">King of the Romans</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON> II of Habsburg\"><PERSON> of Habsburg</a> becomes <a href=\"https://wikipedia.org/wiki/King_of_the_Romans\" title=\"King of the Romans\">King of the Romans</a>.", "links": [{"title": "<PERSON> Habsburg", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_Habsburg"}, {"title": "King of the Romans", "link": "https://wikipedia.org/wiki/King_of_the_Romans"}]}, {"year": "1571", "text": "Valletta is made the capital city of Malta.", "html": "1571 - <a href=\"https://wikipedia.org/wiki/Valletta\" title=\"Valletta\">Valletta</a> is made the capital city of <a href=\"https://wikipedia.org/wiki/Malta\" title=\"Malta\">Malta</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Valletta\" title=\"Valletta\">Valletta</a> is made the capital city of <a href=\"https://wikipedia.org/wiki/Malta\" title=\"Malta\">Malta</a>.", "links": [{"title": "Valletta", "link": "https://wikipedia.org/wiki/Valletta"}, {"title": "Malta", "link": "https://wikipedia.org/wiki/Malta"}]}, {"year": "1608", "text": "<PERSON><PERSON><PERSON><PERSON> is formally crowned Emperor of Ethiopia.", "html": "1608 - <a href=\"https://wikipedia.org/wiki/Susenyo<PERSON>_I\" title=\"Susenyos I\"><PERSON><PERSON><PERSON><PERSON></a> is formally crowned Emperor of <a href=\"https://wikipedia.org/wiki/Ethiopia\" title=\"Ethiopia\">Ethiopia</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Susenyo<PERSON>_I\" title=\"Susenyos I\"><PERSON><PERSON><PERSON><PERSON></a> is formally crowned Emperor of <a href=\"https://wikipedia.org/wiki/Ethiopia\" title=\"Ethiopia\">Ethiopia</a>.", "links": [{"title": "Susenyos I", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>s_I"}, {"title": "Ethiopia", "link": "https://wikipedia.org/wiki/Ethiopia"}]}, {"year": "1644", "text": "The Third Anglo-Powhatan War begins in the Colony of Virginia.", "html": "1644 - The <a href=\"https://wikipedia.org/wiki/Anglo-Powhatan_Wars\" title=\"Anglo-Powhatan Wars\">Third Anglo-Powhatan War</a> begins in the <a href=\"https://wikipedia.org/wiki/Colony_of_Virginia\" title=\"Colony of Virginia\">Colony of Virginia</a>.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Anglo-Powhatan_Wars\" title=\"Anglo-Powhatan Wars\">Third Anglo-Powhatan War</a> begins in the <a href=\"https://wikipedia.org/wiki/Colony_of_Virginia\" title=\"Colony of Virginia\">Colony of Virginia</a>.", "links": [{"title": "Anglo-Powhatan Wars", "link": "https://wikipedia.org/wiki/Anglo-Powhatan_Wars"}, {"title": "Colony of Virginia", "link": "https://wikipedia.org/wiki/Colony_of_Virginia"}]}, {"year": "1673", "text": "English lord <PERSON> sold his half of New Jersey to the Quakers", "html": "1673 - English lord <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_1st_Baron_<PERSON>_of_Stratton\" title=\"<PERSON>, 1st Baron <PERSON> of Stratton\"><PERSON></a> sold his half of <a href=\"https://wikipedia.org/wiki/New_Jersey\" title=\"New Jersey\">New Jersey</a> to the <a href=\"https://wikipedia.org/wiki/Quakers\" title=\"Quakers\">Quakers</a>", "no_year_html": "English lord <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_1st_Baron_<PERSON>_of_Stratton\" title=\"<PERSON>, 1st Baron <PERSON> of Stratton\"><PERSON></a> sold his half of <a href=\"https://wikipedia.org/wiki/New_Jersey\" title=\"New Jersey\">New Jersey</a> to the <a href=\"https://wikipedia.org/wiki/Quakers\" title=\"Quakers\">Quakers</a>", "links": [{"title": "<PERSON>, 1st Baron <PERSON> of Stratton", "link": "https://wikipedia.org/wiki/<PERSON>,_1st_Baron_<PERSON>_of_Stratton"}, {"title": "New Jersey", "link": "https://wikipedia.org/wiki/New_Jersey"}, {"title": "Quakers", "link": "https://wikipedia.org/wiki/Quakers"}]}, {"year": "1741", "text": "New York governor <PERSON>'s complex at Fort George is burned in an arson attack, starting the New York Conspiracy of 1741.", "html": "1741 - New York governor <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>(governor)\" title=\"<PERSON> (governor)\"><PERSON></a>'s complex at <a href=\"https://wikipedia.org/wiki/Fort_Amsterdam\" title=\"Fort Amsterdam\">Fort George</a> is burned in an arson attack, starting the <a href=\"https://wikipedia.org/wiki/New_York_Conspiracy_of_1741\" title=\"New York Conspiracy of 1741\">New York Conspiracy of 1741</a>.", "no_year_html": "New York governor <a href=\"https://wikipedia.org/wiki/<PERSON>(governor)\" title=\"<PERSON> (governor)\"><PERSON></a>'s complex at <a href=\"https://wikipedia.org/wiki/Fort_Amsterdam\" title=\"Fort Amsterdam\">Fort George</a> is burned in an arson attack, starting the <a href=\"https://wikipedia.org/wiki/New_York_Conspiracy_of_1741\" title=\"New York Conspiracy of 1741\">New York Conspiracy of 1741</a>.", "links": [{"title": "<PERSON> (governor)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(governor)"}, {"title": "Fort Amsterdam", "link": "https://wikipedia.org/wiki/Fort_Amsterdam"}, {"title": "New York Conspiracy of 1741", "link": "https://wikipedia.org/wiki/New_York_Conspiracy_of_1741"}]}, {"year": "1766", "text": "American Revolution: The British Parliament repeals the Stamp Act.", "html": "1766 - <a href=\"https://wikipedia.org/wiki/American_Revolution\" title=\"American Revolution\">American Revolution</a>: The <a href=\"https://wikipedia.org/wiki/British_Parliament\" class=\"mw-redirect\" title=\"British Parliament\">British Parliament</a> repeals the <a href=\"https://wikipedia.org/wiki/Stamp_Act_1765\" title=\"Stamp Act 1765\">Stamp Act</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/American_Revolution\" title=\"American Revolution\">American Revolution</a>: The <a href=\"https://wikipedia.org/wiki/British_Parliament\" class=\"mw-redirect\" title=\"British Parliament\">British Parliament</a> repeals the <a href=\"https://wikipedia.org/wiki/Stamp_Act_1765\" title=\"Stamp Act 1765\">Stamp Act</a>.", "links": [{"title": "American Revolution", "link": "https://wikipedia.org/wiki/American_Revolution"}, {"title": "British Parliament", "link": "https://wikipedia.org/wiki/British_Parliament"}, {"title": "Stamp Act 1765", "link": "https://wikipedia.org/wiki/Stamp_Act_1765"}]}, {"year": "1793", "text": "The first modern republic in Germany, the Republic of Mainz, is declared by <PERSON>.", "html": "1793 - The first modern <a href=\"https://wikipedia.org/wiki/Republic\" title=\"Republic\">republic</a> in Germany, the <a href=\"https://wikipedia.org/wiki/Republic_of_Mainz\" title=\"Republic of Mainz\">Republic of Mainz</a>, is declared by <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>.", "no_year_html": "The first modern <a href=\"https://wikipedia.org/wiki/Republic\" title=\"Republic\">republic</a> in Germany, the <a href=\"https://wikipedia.org/wiki/Republic_of_Mainz\" title=\"Republic of Mainz\">Republic of Mainz</a>, is declared by <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>.", "links": [{"title": "Republic", "link": "https://wikipedia.org/wiki/Republic"}, {"title": "Republic of Mainz", "link": "https://wikipedia.org/wiki/Republic_of_Mainz"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1793", "text": "Flanders Campaign of the French Revolution, Battle of Neerwinden.", "html": "1793 - <a href=\"https://wikipedia.org/wiki/Low_Countries_theatre_of_the_War_of_the_First_Coalition\" title=\"Low Countries theatre of the War of the First Coalition\">Flanders Campaign</a> of the <a href=\"https://wikipedia.org/wiki/French_Revolution\" title=\"French Revolution\">French Revolution</a>, <a href=\"https://wikipedia.org/wiki/Battle_of_Neerwinden_(1793)\" title=\"Battle of Neerwinden (1793)\">Battle of Neerwinden</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Low_Countries_theatre_of_the_War_of_the_First_Coalition\" title=\"Low Countries theatre of the War of the First Coalition\">Flanders Campaign</a> of the <a href=\"https://wikipedia.org/wiki/French_Revolution\" title=\"French Revolution\">French Revolution</a>, <a href=\"https://wikipedia.org/wiki/Battle_of_Neerwinden_(1793)\" title=\"Battle of Neerwinden (1793)\">Battle of Neerwinden</a>.", "links": [{"title": "Low Countries theatre of the War of the First Coalition", "link": "https://wikipedia.org/wiki/Low_Countries_theatre_of_the_War_of_the_First_Coalition"}, {"title": "French Revolution", "link": "https://wikipedia.org/wiki/French_Revolution"}, {"title": "Battle of Neerwinden (1793)", "link": "https://wikipedia.org/wiki/Battle_of_Neerwinden_(1793)"}]}, {"year": "1834", "text": "Six farm labourers from Tolpuddle, Dorset, England are sentenced to be transported to Australia for forming a trade union.", "html": "1834 - <a href=\"https://wikipedia.org/wiki/Tolpuddle_Martyrs\" title=\"Tolpuddle Martyrs\">Six farm labourers</a> from <a href=\"https://wikipedia.org/wiki/Tolpuddle\" title=\"Tolpuddle\">Tolpuddle</a>, <a href=\"https://wikipedia.org/wiki/Dorset\" title=\"Dorset\">Dorset</a>, England are sentenced to be transported to Australia for forming a <a href=\"https://wikipedia.org/wiki/Trade_union\" title=\"Trade union\">trade union</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Tolpuddle_Martyrs\" title=\"Tolpuddle Martyrs\">Six farm labourers</a> from <a href=\"https://wikipedia.org/wiki/Tolpuddle\" title=\"Tolpuddle\">Tolpuddle</a>, <a href=\"https://wikipedia.org/wiki/Dorset\" title=\"Dorset\">Dorset</a>, England are sentenced to be transported to Australia for forming a <a href=\"https://wikipedia.org/wiki/Trade_union\" title=\"Trade union\">trade union</a>.", "links": [{"title": "Tolpuddle Martyrs", "link": "https://wikipedia.org/wiki/Tolpuddle_Martyrs"}, {"title": "To<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Tolpuddle"}, {"title": "Dorset", "link": "https://wikipedia.org/wiki/Dorset"}, {"title": "Trade union", "link": "https://wikipedia.org/wiki/Trade_union"}]}, {"year": "1848", "text": "The premiere of <PERSON>'s <PERSON><PERSON> in Philadelphia is the first known performance of a grand opera by an American composer.", "html": "1848 - The premiere of <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>'s <i><PERSON><PERSON></i> in <a href=\"https://wikipedia.org/wiki/Philadelphia\" title=\"Philadelphia\">Philadelphia</a> is the first known performance of a <a href=\"https://wikipedia.org/wiki/Grand_opera\" title=\"Grand opera\">grand opera</a> by an American composer.", "no_year_html": "The premiere of <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>'s <i><PERSON><PERSON></i> in <a href=\"https://wikipedia.org/wiki/Philadelphia\" title=\"Philadelphia\">Philadelphia</a> is the first known performance of a <a href=\"https://wikipedia.org/wiki/Grand_opera\" title=\"Grand opera\">grand opera</a> by an American composer.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Philadelphia", "link": "https://wikipedia.org/wiki/Philadelphia"}, {"title": "Grand opera", "link": "https://wikipedia.org/wiki/Grand_opera"}]}, {"year": "1848", "text": "Revolutions of 1848: A rebellion arose in Milan which in five days of street fighting drove Marshal <PERSON><PERSON><PERSON><PERSON> and his Austrian soldiers from the city.", "html": "1848 - <a href=\"https://wikipedia.org/wiki/Revolutions_of_1848\" title=\"Revolutions of 1848\">Revolutions of 1848</a>: A <a href=\"https://wikipedia.org/wiki/Five_Days_of_Milan\" title=\"Five Days of Milan\">rebellion</a> arose in <a href=\"https://wikipedia.org/wiki/Milan\" title=\"Milan\">Milan</a> which in five days of street fighting drove <a href=\"https://wikipedia.org/wiki/Marshal_<PERSON>\" class=\"mw-redirect\" title=\"Marshal <PERSON>\">Marshal <PERSON></a> and his Austrian soldiers from the city.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Revolutions_of_1848\" title=\"Revolutions of 1848\">Revolutions of 1848</a>: A <a href=\"https://wikipedia.org/wiki/Five_Days_of_Milan\" title=\"Five Days of Milan\">rebellion</a> arose in <a href=\"https://wikipedia.org/wiki/Milan\" title=\"Milan\">Milan</a> which in five days of street fighting drove <a href=\"https://wikipedia.org/wiki/Marshal_<PERSON>\" class=\"mw-redirect\" title=\"Marshal <PERSON>\">Marshal <PERSON></a> and his Austrian soldiers from the city.", "links": [{"title": "Revolutions of 1848", "link": "https://wikipedia.org/wiki/Revolutions_of_1848"}, {"title": "Five Days of Milan", "link": "https://wikipedia.org/wiki/Five_Days_of_Milan"}, {"title": "Milan", "link": "https://wikipedia.org/wiki/Milan"}, {"title": "Marshal <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1865", "text": "American Civil War: The Congress of the Confederate States adjourns for the last time.", "html": "1865 - <a href=\"https://wikipedia.org/wiki/American_Civil_War\" title=\"American Civil War\">American Civil War</a>: The <a href=\"https://wikipedia.org/wiki/Congress_of_the_Confederate_States\" class=\"mw-redirect\" title=\"Congress of the Confederate States\">Congress of the Confederate States</a> adjourns for the last time.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/American_Civil_War\" title=\"American Civil War\">American Civil War</a>: The <a href=\"https://wikipedia.org/wiki/Congress_of_the_Confederate_States\" class=\"mw-redirect\" title=\"Congress of the Confederate States\">Congress of the Confederate States</a> adjourns for the last time.", "links": [{"title": "American Civil War", "link": "https://wikipedia.org/wiki/American_Civil_War"}, {"title": "Congress of the Confederate States", "link": "https://wikipedia.org/wiki/Congress_of_the_Confederate_States"}]}, {"year": "1871", "text": "Declaration of the Paris Commune; President of the French Republic, <PERSON><PERSON><PERSON>, orders the evacuation of Paris.", "html": "1871 - Declaration of the <a href=\"https://wikipedia.org/wiki/Paris_Commune\" title=\"Paris Commune\">Paris Commune</a>; President of the French Republic, <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, orders the evacuation of Paris.", "no_year_html": "Declaration of the <a href=\"https://wikipedia.org/wiki/Paris_Commune\" title=\"Paris Commune\">Paris Commune</a>; President of the French Republic, <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, orders the evacuation of Paris.", "links": [{"title": "Paris Commune", "link": "https://wikipedia.org/wiki/Paris_Commune"}, {"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1874", "text": "The Hawaiian Kingdom signs a treaty with the United States granting exclusive trade rights.", "html": "1874 - The <a href=\"https://wikipedia.org/wiki/Hawaiian_Kingdom\" title=\"Hawaiian Kingdom\">Hawaiian Kingdom</a> signs a treaty with the United States granting exclusive <a href=\"https://wikipedia.org/wiki/Trade\" title=\"Trade\">trade</a> rights.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Hawaiian_Kingdom\" title=\"Hawaiian Kingdom\">Hawaiian Kingdom</a> signs a treaty with the United States granting exclusive <a href=\"https://wikipedia.org/wiki/Trade\" title=\"Trade\">trade</a> rights.", "links": [{"title": "Hawaiian Kingdom", "link": "https://wikipedia.org/wiki/Hawaiian_Kingdom"}, {"title": "Trade", "link": "https://wikipedia.org/wiki/Trade"}]}, {"year": "1899", "text": "<PERSON>, a satellite of Saturn, becomes the first to be discovered with photographs, taken in August 1898, by <PERSON>.", "html": "1899 - <a href=\"https://wikipedia.org/wiki/Phoebe_(moon)\" title=\"<PERSON> (moon)\">Phoebe</a>, a <a href=\"https://wikipedia.org/wiki/Moons_of_Saturn\" title=\"Moons of Saturn\">satellite of Saturn</a>, becomes the first to be discovered with photographs, taken in August 1898, by <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(moon)\" title=\"<PERSON> (moon)\">Phoebe</a>, a <a href=\"https://wikipedia.org/wiki/Moons_of_Saturn\" title=\"Moons of Saturn\">satellite of Saturn</a>, becomes the first to be discovered with photographs, taken in August 1898, by <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>.", "links": [{"title": "<PERSON> (moon)", "link": "https://wikipedia.org/wiki/<PERSON>_(moon)"}, {"title": "Moons of Saturn", "link": "https://wikipedia.org/wiki/Moons_of_Saturn"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1901", "text": "The Kumasi Mutiny of 1901 begins.", "html": "1901 - The <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_Mutiny_of_1901\" title=\"<PERSON><PERSON><PERSON> Mutiny of 1901\"><PERSON><PERSON><PERSON>y of 1901</a> begins.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_Mutiny_of_1901\" title=\"<PERSON><PERSON><PERSON> Mutiny of 1901\"><PERSON><PERSON><PERSON> Mutiny of 1901</a> begins.", "links": [{"title": "<PERSON><PERSON><PERSON> of 1901", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_Mutiny_of_1901"}]}, {"year": "1902", "text": "Mac<PERSON> issues Presidential Order No. 1 of his Tagalog Republic.", "html": "1902 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"Mac<PERSON>\"><PERSON><PERSON></a> issues Presidential Order No. 1 of his <a href=\"https://wikipedia.org/wiki/Tagalog_Republic\" title=\"Tagalog Republic\">Tagalog Republic</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"Macario <PERSON>\"><PERSON><PERSON></a> issues Presidential Order No. 1 of his <a href=\"https://wikipedia.org/wiki/Tagalog_Republic\" title=\"Tagalog Republic\">Tagalog Republic</a>.", "links": [{"title": "Macario Sakay", "link": "https://wikipedia.org/wiki/<PERSON>ario_<PERSON>y"}, {"title": "Tagalog Republic", "link": "https://wikipedia.org/wiki/Tagalog_Republic"}]}, {"year": "1913", "text": "King <PERSON> of Greece is assassinated in the recently liberated city of Thessaloniki.", "html": "1913 - King <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Greece\" title=\"<PERSON> of Greece\"><PERSON> of Greece</a> is assassinated in the recently liberated city of <a href=\"https://wikipedia.org/wiki/Thessaloniki\" title=\"Thessaloniki\">Thessaloniki</a>.", "no_year_html": "King <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Greece\" title=\"<PERSON> of Greece\"><PERSON> of Greece</a> is assassinated in the recently liberated city of <a href=\"https://wikipedia.org/wiki/Thessaloniki\" title=\"Thessaloniki\">Thessaloniki</a>.", "links": [{"title": "<PERSON> of Greece", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Greece"}, {"title": "Thessaloniki", "link": "https://wikipedia.org/wiki/Thessaloniki"}]}, {"year": "1915", "text": "World War I: During the Battle of Gallipoli, three battleships are sunk during a failed British and French naval attack on the Dardanelles.", "html": "1915 - <a href=\"https://wikipedia.org/wiki/World_War_I\" title=\"World War I\">World War I</a>: During the <a href=\"https://wikipedia.org/wiki/Battle_of_Gallipoli\" class=\"mw-redirect\" title=\"Battle of Gallipoli\">Battle of Gallipoli</a>, three <a href=\"https://wikipedia.org/wiki/Battleship\" title=\"Battleship\">battleships</a> are sunk during a failed British and French <a href=\"https://wikipedia.org/wiki/Naval_operations_in_the_Dardanelles_Campaign\" class=\"mw-redirect\" title=\"Naval operations in the Dardanelles Campaign\">naval attack on the Dardanelles</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/World_War_I\" title=\"World War I\">World War I</a>: During the <a href=\"https://wikipedia.org/wiki/Battle_of_Gallipoli\" class=\"mw-redirect\" title=\"Battle of Gallipoli\">Battle of Gallipoli</a>, three <a href=\"https://wikipedia.org/wiki/Battleship\" title=\"Battleship\">battleships</a> are sunk during a failed British and French <a href=\"https://wikipedia.org/wiki/Naval_operations_in_the_Dardanelles_Campaign\" class=\"mw-redirect\" title=\"Naval operations in the Dardanelles Campaign\">naval attack on the Dardanelles</a>.", "links": [{"title": "World War I", "link": "https://wikipedia.org/wiki/World_War_I"}, {"title": "Battle of Gallipoli", "link": "https://wikipedia.org/wiki/Battle_of_Gallipoli"}, {"title": "Battleship", "link": "https://wikipedia.org/wiki/Battleship"}, {"title": "Naval operations in the Dardanelles Campaign", "link": "https://wikipedia.org/wiki/Naval_operations_in_the_Dardanelles_Campaign"}]}, {"year": "1921", "text": "The second Peace of Riga is signed between Poland and the Soviet Union.", "html": "1921 - The second <a href=\"https://wikipedia.org/wiki/Peace_of_Riga\" class=\"mw-redirect\" title=\"Peace of Riga\">Peace of Riga</a> is signed between Poland and the <a href=\"https://wikipedia.org/wiki/Soviet_Union\" title=\"Soviet Union\">Soviet Union</a>.", "no_year_html": "The second <a href=\"https://wikipedia.org/wiki/Peace_of_Riga\" class=\"mw-redirect\" title=\"Peace of Riga\">Peace of Riga</a> is signed between Poland and the <a href=\"https://wikipedia.org/wiki/Soviet_Union\" title=\"Soviet Union\">Soviet Union</a>.", "links": [{"title": "Peace of Riga", "link": "https://wikipedia.org/wiki/Peace_of_Riga"}, {"title": "Soviet Union", "link": "https://wikipedia.org/wiki/Soviet_Union"}]}, {"year": "1921", "text": "The Kronstadt rebellion is suppressed by the Red Army.", "html": "1921 - The <a href=\"https://wikipedia.org/wiki/Kronstadt_rebellion\" title=\"Kronstadt rebellion\">Kronstadt rebellion</a> is suppressed by the <a href=\"https://wikipedia.org/wiki/Red_Army\" title=\"Red Army\">Red Army</a>.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Kronstadt_rebellion\" title=\"Kronstadt rebellion\">Kronstadt rebellion</a> is suppressed by the <a href=\"https://wikipedia.org/wiki/Red_Army\" title=\"Red Army\">Red Army</a>.", "links": [{"title": "Kronstadt rebellion", "link": "https://wikipedia.org/wiki/Kronstadt_rebellion"}, {"title": "Red Army", "link": "https://wikipedia.org/wiki/Red_Army"}]}, {"year": "1921", "text": "Mongolian Revolution of 1921: The Mongolian People's Army defeated local Chinese forces at Altanbulag, Selenge (then known as Maimachen). This battle was seen as the birthday of the People's Army and completed the expulsion of Chinese militants in Mongolia.", "html": "1921 - <a href=\"https://wikipedia.org/wiki/Mongolian_Revolution_of_1921\" title=\"Mongolian Revolution of 1921\">Mongolian Revolution of 1921</a>: The Mongolian People's Army defeated local Chinese forces at <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>bulag,_Selenge\" title=\"Altanbula<PERSON>, Selenge\">Altanbulag, Selenge</a> (then known as Maimachen). This battle was seen as the birthday of the People's Army and completed the expulsion of Chinese militants in Mongolia.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Mongolian_Revolution_of_1921\" title=\"Mongolian Revolution of 1921\">Mongolian Revolution of 1921</a>: The Mongolian People's Army defeated local Chinese forces at <a href=\"https://wikipedia.org/wiki/Alt<PERSON>bulag,_Selenge\" title=\"Altanbula<PERSON>, Selenge\">Altanbulag, Selenge</a> (then known as Maimachen). This battle was seen as the birthday of the People's Army and completed the expulsion of Chinese militants in Mongolia.", "links": [{"title": "Mongolian Revolution of 1921", "link": "https://wikipedia.org/wiki/Mongolian_Revolution_of_1921"}, {"title": "Altanbulag, Selenge", "link": "https://wikipedia.org/wiki/<PERSON>anbula<PERSON>,_<PERSON><PERSON>ge"}]}, {"year": "1922", "text": "In India, <PERSON><PERSON> is sentenced to six years in prison for civil disobedience, of which he serves only two.", "html": "1922 - In India, <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a> is sentenced to six years in <a href=\"https://wikipedia.org/wiki/Prison\" title=\"Prison\">prison</a> for <a href=\"https://wikipedia.org/wiki/Civil_disobedience\" title=\"Civil disobedience\">civil disobedience</a>, of which he serves only two.", "no_year_html": "In India, <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a> is sentenced to six years in <a href=\"https://wikipedia.org/wiki/Prison\" title=\"Prison\">prison</a> for <a href=\"https://wikipedia.org/wiki/Civil_disobedience\" title=\"Civil disobedience\">civil disobedience</a>, of which he serves only two.", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}, {"title": "Prison", "link": "https://wikipedia.org/wiki/Prison"}, {"title": "Civil disobedience", "link": "https://wikipedia.org/wiki/Civil_disobedience"}]}, {"year": "1925", "text": "The 1925 Tri-State tornado hits the Midwestern states of Missouri, Illinois, and Indiana, killing 695 people.", "html": "1925 - The <a href=\"https://wikipedia.org/wiki/1925_Tri-State_tornado\" class=\"mw-redirect\" title=\"1925 Tri-State tornado\">1925 Tri-State tornado</a> hits the Midwestern states of <a href=\"https://wikipedia.org/wiki/Missouri\" title=\"Missouri\">Missouri</a>, <a href=\"https://wikipedia.org/wiki/Illinois\" title=\"Illinois\">Illinois</a>, and <a href=\"https://wikipedia.org/wiki/Indiana\" title=\"Indiana\">Indiana</a>, killing 695 people.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/1925_Tri-State_tornado\" class=\"mw-redirect\" title=\"1925 Tri-State tornado\">1925 Tri-State tornado</a> hits the Midwestern states of <a href=\"https://wikipedia.org/wiki/Missouri\" title=\"Missouri\">Missouri</a>, <a href=\"https://wikipedia.org/wiki/Illinois\" title=\"Illinois\">Illinois</a>, and <a href=\"https://wikipedia.org/wiki/Indiana\" title=\"Indiana\">Indiana</a>, killing 695 people.", "links": [{"title": "1925 Tri-State tornado", "link": "https://wikipedia.org/wiki/1925_Tri-State_tornado"}, {"title": "Missouri", "link": "https://wikipedia.org/wiki/Missouri"}, {"title": "Illinois", "link": "https://wikipedia.org/wiki/Illinois"}, {"title": "Indiana", "link": "https://wikipedia.org/wiki/Indiana"}]}, {"year": "1937", "text": "The New London School explosion in New London, Texas, kills 300 people, mostly children.", "html": "1937 - The <a href=\"https://wikipedia.org/wiki/New_London_School_explosion\" title=\"New London School explosion\">New London School explosion</a> in <a href=\"https://wikipedia.org/wiki/New_London,_Texas\" title=\"New London, Texas\">New London, Texas</a>, kills 300 people, mostly children.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/New_London_School_explosion\" title=\"New London School explosion\">New London School explosion</a> in <a href=\"https://wikipedia.org/wiki/New_London,_Texas\" title=\"New London, Texas\">New London, Texas</a>, kills 300 people, mostly children.", "links": [{"title": "New London School explosion", "link": "https://wikipedia.org/wiki/New_London_School_explosion"}, {"title": "New London, Texas", "link": "https://wikipedia.org/wiki/New_London,_Texas"}]}, {"year": "1937", "text": "Spanish Civil War: Spanish Republican forces defeat the Italians at the Battle of Guadalajara.", "html": "1937 - <a href=\"https://wikipedia.org/wiki/Spanish_Civil_War\" title=\"Spanish Civil War\">Spanish Civil War</a>: Spanish Republican forces defeat the <a href=\"https://wikipedia.org/wiki/Italy\" title=\"Italy\">Italians</a> at the <a href=\"https://wikipedia.org/wiki/Battle_of_Guadalajara\" title=\"Battle of Guadalajara\">Battle of Guadalajara</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Spanish_Civil_War\" title=\"Spanish Civil War\">Spanish Civil War</a>: Spanish Republican forces defeat the <a href=\"https://wikipedia.org/wiki/Italy\" title=\"Italy\">Italians</a> at the <a href=\"https://wikipedia.org/wiki/Battle_of_Guadalajara\" title=\"Battle of Guadalajara\">Battle of Guadalajara</a>.", "links": [{"title": "Spanish Civil War", "link": "https://wikipedia.org/wiki/Spanish_Civil_War"}, {"title": "Italy", "link": "https://wikipedia.org/wiki/Italy"}, {"title": "Battle of Guadalajara", "link": "https://wikipedia.org/wiki/Battle_of_Guadalajara"}]}, {"year": "1938", "text": "Mexico creates Pemex by expropriating all foreign-owned oil reserves and facilities.", "html": "1938 - Mexico creates <a href=\"https://wikipedia.org/wiki/Pemex\" title=\"Pemex\">Pemex</a> by <a href=\"https://wikipedia.org/wiki/Mexican_oil_expropriation\" title=\"Mexican oil expropriation\">expropriating</a> all foreign-owned oil reserves and facilities.", "no_year_html": "Mexico creates <a href=\"https://wikipedia.org/wiki/Pemex\" title=\"Pemex\">Pemex</a> by <a href=\"https://wikipedia.org/wiki/Mexican_oil_expropriation\" title=\"Mexican oil expropriation\">expropriating</a> all foreign-owned oil reserves and facilities.", "links": [{"title": "Pemex", "link": "https://wikipedia.org/wiki/Pemex"}, {"title": "Mexican oil expropriation", "link": "https://wikipedia.org/wiki/Mexican_oil_expropriation"}]}, {"year": "1940", "text": "World War II: <PERSON> and <PERSON> meet at the Brenner Pass in the Alps and agree to form an alliance against France and the United Kingdom.", "html": "1940 - <a href=\"https://wikipedia.org/wiki/World_War_II\" title=\"World War II\">World War II</a>: <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Hitler\"><PERSON></a> and <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> meet at the <a href=\"https://wikipedia.org/wiki/Brenner_Pass\" title=\"Brenner Pass\">Brenner Pass</a> in the <a href=\"https://wikipedia.org/wiki/Alps\" title=\"Alps\">Alps</a> and agree to form an alliance against France and the United Kingdom.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/World_War_II\" title=\"World War II\">World War II</a>: <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON> Hitler\"><PERSON></a> and <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> meet at the <a href=\"https://wikipedia.org/wiki/Brenner_Pass\" title=\"Brenner Pass\">Brenner Pass</a> in the <a href=\"https://wikipedia.org/wiki/Alps\" title=\"Alps\">Alps</a> and agree to form an alliance against France and the United Kingdom.", "links": [{"title": "World War II", "link": "https://wikipedia.org/wiki/World_War_II"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Brenner Pass", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}, {"title": "Alps", "link": "https://wikipedia.org/wiki/Alps"}]}, {"year": "1942", "text": "The War Relocation Authority is established in the United States to take Japanese Americans into custody.", "html": "1942 - The <a href=\"https://wikipedia.org/wiki/War_Relocation_Authority\" title=\"War Relocation Authority\">War Relocation Authority</a> is established in the United States to take Japanese Americans into custody.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/War_Relocation_Authority\" title=\"War Relocation Authority\">War Relocation Authority</a> is established in the United States to take Japanese Americans into custody.", "links": [{"title": "War Relocation Authority", "link": "https://wikipedia.org/wiki/War_Relocation_Authority"}]}, {"year": "1944", "text": "Mount Vesuvius in Italy erupts, killing 26 people, causing thousands to flee their homes, and destroying dozens of Allied bombers.", "html": "1944 - <a href=\"https://wikipedia.org/wiki/Mount_Vesuvius\" title=\"Mount Vesuvius\">Mount Vesuvius</a> in Italy erupts, killing 26 people, causing thousands to flee their homes, and destroying dozens of Allied bombers.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Mount_Vesuvius\" title=\"Mount Vesuvius\">Mount Vesuvius</a> in Italy erupts, killing 26 people, causing thousands to flee their homes, and destroying dozens of Allied bombers.", "links": [{"title": "Mount Vesuvius", "link": "https://wikipedia.org/wiki/Mount_Vesuvius"}]}, {"year": "1945", "text": "40th Infantry Division, spearheaded by the 185th US Infantry Regiment, landed unopposed in Tigbauan forcing the Japanese forces to surrender and General <PERSON><PERSON> and Gen. Gen<PERSON> to declare the Liberation of Panay, Romblon and Guimaras.", "html": "1945 - 40th Infantry Division, spearheaded by the 185th US Infantry Regiment, landed unopposed in <a href=\"https://wikipedia.org/wiki/Tigbauan\" title=\"Tigbauan\">Tigbauan</a> forcing the Japanese forces to surrender and General <a href=\"https://wikipedia.org/wiki/<PERSON>ario_Peralta\" class=\"mw-redirect\" title=\"Macario Peralta\"><PERSON><PERSON></a> and Gen<PERSON> <PERSON><PERSON> to declare the <a href=\"https://wikipedia.org/wiki/Panay_Liberation_Day\" title=\"Panay Liberation Day\">Liberation of Panay, Romblon and Guimaras</a>.", "no_year_html": "40th Infantry Division, spearheaded by the 185th US Infantry Regiment, landed unopposed in <a href=\"https://wikipedia.org/wiki/Tigbauan\" title=\"Tigbauan\">Tigbauan</a> forcing the Japanese forces to surrender and General <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_Peralta\" class=\"mw-redirect\" title=\"Macario Peralta\"><PERSON><PERSON></a> and Gen<PERSON> Gen<PERSON> to declare the <a href=\"https://wikipedia.org/wiki/Panay_Liberation_Day\" title=\"Panay Liberation Day\">Liberation of Panay, Romblon and Guimaras</a>.", "links": [{"title": "Tigbauan", "link": "https://wikipedia.org/wiki/Tigbauan"}, {"title": "Macario Peralta", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}, {"title": "Panay Liberation Day", "link": "https://wikipedia.org/wiki/Panay_Liberation_Day"}]}, {"year": "1948", "text": "Soviet consultants leave Yugoslavia in the first sign of the Tito-Stalin split.", "html": "1948 - Soviet consultants leave Yugoslavia in the first sign of the <a href=\"https://wikipedia.org/wiki/Tito%E2%80%93S<PERSON><PERSON>_split\" title=\"Tito-Stalin split\">Tito<PERSON>Stalin split</a>.", "no_year_html": "Soviet consultants leave Yugoslavia in the first sign of the <a href=\"https://wikipedia.org/wiki/Tito%E2%80%93S<PERSON><PERSON>_split\" title=\"Tito-Stalin split\">Tito<PERSON>Stalin split</a>.", "links": [{"title": "Tito<PERSON><PERSON> split", "link": "https://wikipedia.org/wiki/Tito%E2%80%93Stalin_split"}]}, {"year": "1953", "text": "An earthquake hits western Turkey, killing at least 1,070 people.", "html": "1953 - An <a href=\"https://wikipedia.org/wiki/1953_Yenice%E2%80%93G%C3%B6nen_earthquake\" title=\"1953 Yenice-Gönen earthquake\">earthquake</a> hits western <a href=\"https://wikipedia.org/wiki/Turkey\" title=\"Turkey\">Turkey</a>, killing at least 1,070 people.", "no_year_html": "An <a href=\"https://wikipedia.org/wiki/1953_Yenice%E2%80%93G%C3%B6nen_earthquake\" title=\"1953 Yenice-Gönen earthquake\">earthquake</a> hits western <a href=\"https://wikipedia.org/wiki/Turkey\" title=\"Turkey\">Turkey</a>, killing at least 1,070 people.", "links": [{"title": "1953 Yenice-Gönen earthquake", "link": "https://wikipedia.org/wiki/1953_Yenice%E2%80%93G%C3%B6nen_earthquake"}, {"title": "Turkey", "link": "https://wikipedia.org/wiki/Turkey"}]}, {"year": "1959", "text": "The Hawaii Admission Act is signed into law.", "html": "1959 - The <a href=\"https://wikipedia.org/wiki/Hawaii_Admission_Act\" title=\"Hawaii Admission Act\">Hawaii Admission Act</a> is signed into law.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Hawaii_Admission_Act\" title=\"Hawaii Admission Act\">Hawaii Admission Act</a> is signed into law.", "links": [{"title": "Hawaii Admission Act", "link": "https://wikipedia.org/wiki/Hawaii_Admission_Act"}]}, {"year": "1962", "text": "The Évian Accords end the Algerian War of Independence, which had begun in 1954.", "html": "1962 - The <a href=\"https://wikipedia.org/wiki/%C3%89vian_Accords\" title=\"Évian Accords\">Évian Accords</a> end the <a href=\"https://wikipedia.org/wiki/Algerian_War_of_Independence\" class=\"mw-redirect\" title=\"Algerian War of Independence\">Algerian War of Independence</a>, which had begun in 1954.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/%C3%89vian_Accords\" title=\"Évian Accords\">Évian Accords</a> end the <a href=\"https://wikipedia.org/wiki/Algerian_War_of_Independence\" class=\"mw-redirect\" title=\"Algerian War of Independence\">Algerian War of Independence</a>, which had begun in 1954.", "links": [{"title": "Évian Accords", "link": "https://wikipedia.org/wiki/%C3%89vian_Accords"}, {"title": "Algerian War of Independence", "link": "https://wikipedia.org/wiki/Algerian_War_of_Independence"}]}, {"year": "1965", "text": "Cosmonaut <PERSON>, leaving his spacecraft Voskhod 2 for 12 minutes, becomes the first person to walk in space.", "html": "1965 - <a href=\"https://wikipedia.org/wiki/Cosmonaut\" class=\"mw-redirect\" title=\"Cosmonaut\">Cosmonaut</a> <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, leaving his spacecraft <a href=\"https://wikipedia.org/wiki/Voskhod_2\" title=\"Voskhod 2\">Voskhod 2</a> for 12 minutes, becomes the first person to <a href=\"https://wikipedia.org/wiki/Extravehicular_activity\" title=\"Extravehicular activity\">walk in space</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Cosmonaut\" class=\"mw-redirect\" title=\"Cosmonaut\">Cosmonaut</a> <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, leaving his spacecraft <a href=\"https://wikipedia.org/wiki/Voskhod_2\" title=\"Voskhod 2\">Voskhod 2</a> for 12 minutes, becomes the first person to <a href=\"https://wikipedia.org/wiki/Extravehicular_activity\" title=\"Extravehicular activity\">walk in space</a>.", "links": [{"title": "Cosmonaut", "link": "https://wikipedia.org/wiki/Cosmonaut"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}, {"title": "Voskhod 2", "link": "https://wikipedia.org/wiki/Voskhod_2"}, {"title": "Extravehicular activity", "link": "https://wikipedia.org/wiki/Extravehicular_activity"}]}, {"year": "1966", "text": "United Arab Airlines Flight 749 crashes on approach to Cairo International Airport in Cairo, Egypt, killing 30 people.", "html": "1966 - <a href=\"https://wikipedia.org/wiki/United_Arab_Airlines_Flight_749\" title=\"United Arab Airlines Flight 749\">United Arab Airlines Flight 749</a> crashes on approach to <a href=\"https://wikipedia.org/wiki/Cairo_International_Airport\" title=\"Cairo International Airport\">Cairo International Airport</a> in <a href=\"https://wikipedia.org/wiki/Cairo\" title=\"Cairo\">Cairo</a>, Egypt, killing 30 people.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/United_Arab_Airlines_Flight_749\" title=\"United Arab Airlines Flight 749\">United Arab Airlines Flight 749</a> crashes on approach to <a href=\"https://wikipedia.org/wiki/Cairo_International_Airport\" title=\"Cairo International Airport\">Cairo International Airport</a> in <a href=\"https://wikipedia.org/wiki/Cairo\" title=\"Cairo\">Cairo</a>, Egypt, killing 30 people.", "links": [{"title": "United Arab Airlines Flight 749", "link": "https://wikipedia.org/wiki/United_Arab_Airlines_Flight_749"}, {"title": "Cairo International Airport", "link": "https://wikipedia.org/wiki/Cairo_International_Airport"}, {"title": "Cairo", "link": "https://wikipedia.org/wiki/Cairo"}]}, {"year": "1967", "text": "The supertanker Torrey Canyon runs aground off the Cornish coast.", "html": "1967 - The <a href=\"https://wikipedia.org/wiki/Supertanker\" class=\"mw-redirect\" title=\"Supertanker\">supertanker</a> <a href=\"https://wikipedia.org/wiki/SS_Torrey_Canyon\" title=\"SS Torrey Canyon\"><i>Torrey Canyon</i></a> runs aground off the Cornish coast.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Supertanker\" class=\"mw-redirect\" title=\"Supertanker\">supertanker</a> <a href=\"https://wikipedia.org/wiki/SS_Torrey_Canyon\" title=\"SS Torrey Canyon\"><i>Torrey Canyon</i></a> runs aground off the Cornish coast.", "links": [{"title": "Supertanker", "link": "https://wikipedia.org/wiki/Supertanker"}, {"title": "SS Torrey Canyon", "link": "https://wikipedia.org/wiki/SS_Torrey_Canyon"}]}, {"year": "1968", "text": "Gold standard: The U.S. Congress repeals the requirement for a gold reserve to back US currency.", "html": "1968 - <a href=\"https://wikipedia.org/wiki/Gold_standard\" title=\"Gold standard\">Gold standard</a>: The <a href=\"https://wikipedia.org/wiki/United_States_Congress\" title=\"United States Congress\">U.S. Congress</a> repeals the requirement for a gold reserve to back <a href=\"https://wikipedia.org/wiki/United_States_dollar\" title=\"United States dollar\">US currency</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Gold_standard\" title=\"Gold standard\">Gold standard</a>: The <a href=\"https://wikipedia.org/wiki/United_States_Congress\" title=\"United States Congress\">U.S. Congress</a> repeals the requirement for a gold reserve to back <a href=\"https://wikipedia.org/wiki/United_States_dollar\" title=\"United States dollar\">US currency</a>.", "links": [{"title": "Gold standard", "link": "https://wikipedia.org/wiki/Gold_standard"}, {"title": "United States Congress", "link": "https://wikipedia.org/wiki/United_States_Congress"}, {"title": "United States dollar", "link": "https://wikipedia.org/wiki/United_States_dollar"}]}, {"year": "1969", "text": "The United States begins secretly bombing the Sihanouk Trail in Cambodia, used by communist forces to infiltrate South Vietnam.", "html": "1969 - The United States begins <a href=\"https://wikipedia.org/wiki/Operation_Menu\" title=\"Operation Menu\">secretly bombing</a> the <a href=\"https://wikipedia.org/wiki/Sihanouk_Trail\" title=\"Sihanouk Trail\">Sihanouk Trail</a> in <a href=\"https://wikipedia.org/wiki/Cambodia\" title=\"Cambodia\">Cambodia</a>, used by <a href=\"https://wikipedia.org/wiki/People%27s_Army_of_Vietnam\" title=\"People's Army of Vietnam\">communist forces</a> to infiltrate <a href=\"https://wikipedia.org/wiki/South_Vietnam\" title=\"South Vietnam\">South Vietnam</a>.", "no_year_html": "The United States begins <a href=\"https://wikipedia.org/wiki/Operation_Menu\" title=\"Operation Menu\">secretly bombing</a> the <a href=\"https://wikipedia.org/wiki/Sihanouk_Trail\" title=\"Sihanouk Trail\">Sihanouk Trail</a> in <a href=\"https://wikipedia.org/wiki/Cambodia\" title=\"Cambodia\">Cambodia</a>, used by <a href=\"https://wikipedia.org/wiki/People%27s_Army_of_Vietnam\" title=\"People's Army of Vietnam\">communist forces</a> to infiltrate <a href=\"https://wikipedia.org/wiki/South_Vietnam\" title=\"South Vietnam\">South Vietnam</a>.", "links": [{"title": "Operation Menu", "link": "https://wikipedia.org/wiki/Operation_Menu"}, {"title": "Sihanouk Trail", "link": "https://wikipedia.org/wiki/Sihanouk_Trail"}, {"title": "Cambodia", "link": "https://wikipedia.org/wiki/Cambodia"}, {"title": "People's Army of Vietnam", "link": "https://wikipedia.org/wiki/People%27s_Army_of_Vietnam"}, {"title": "South Vietnam", "link": "https://wikipedia.org/wiki/South_Vietnam"}]}, {"year": "1970", "text": "<PERSON><PERSON> ousts Prince <PERSON><PERSON> of Cambodia.", "html": "1970 - <a href=\"https://wikipedia.org/wiki/Lon_<PERSON>\" title=\"Lon <PERSON>\"><PERSON><PERSON></a> <a href=\"https://wikipedia.org/wiki/1970_Cambodian_coup_d%27%C3%A9tat\" title=\"1970 Cambodian coup d'état\">ousts</a> Prince <a href=\"https://wikipedia.org/wiki/Norod<PERSON>_Si<PERSON>k\" title=\"Norodom <PERSON>han<PERSON>\"><PERSON><PERSON><PERSON></a> of <a href=\"https://wikipedia.org/wiki/Cambodia\" title=\"Cambodia\">Cambodia</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Lon_<PERSON>\" title=\"Lon <PERSON>\"><PERSON><PERSON></a> <a href=\"https://wikipedia.org/wiki/1970_Cambodian_coup_d%27%C3%A9tat\" title=\"1970 Cambodian coup d'état\">ousts</a> Prince <a href=\"https://wikipedia.org/wiki/Norod<PERSON>_Sihan<PERSON>k\" title=\"Norod<PERSON>\"><PERSON><PERSON><PERSON></a> of <a href=\"https://wikipedia.org/wiki/Cambodia\" title=\"Cambodia\">Cambodia</a>.", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Lon_<PERSON>l"}, {"title": "1970 Cambodian coup d'état", "link": "https://wikipedia.org/wiki/1970_Cambodian_coup_d%27%C3%A9tat"}, {"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Norodom_<PERSON>"}, {"title": "Cambodia", "link": "https://wikipedia.org/wiki/Cambodia"}]}, {"year": "1971", "text": "Peru: A landslide crashes into Yanawayin Lake, killing 200 people at the mining camp of Chungar.", "html": "1971 - <a href=\"https://wikipedia.org/wiki/Peru\" title=\"Peru\">Peru</a>: A <a href=\"https://wikipedia.org/wiki/Landslide\" title=\"Landslide\">landslide</a> crashes into <a href=\"https://wikipedia.org/wiki/Yanawayin_Lake\" title=\"Yanawayin Lake\">Yanawayin Lake</a>, killing 200 people at the mining camp of Chungar.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Peru\" title=\"Peru\">Peru</a>: A <a href=\"https://wikipedia.org/wiki/Landslide\" title=\"Landslide\">landslide</a> crashes into <a href=\"https://wikipedia.org/wiki/Yanawayin_Lake\" title=\"Yanawayin Lake\">Yanawayin Lake</a>, killing 200 people at the mining camp of Chungar.", "links": [{"title": "Peru", "link": "https://wikipedia.org/wiki/Peru"}, {"title": "Landslide", "link": "https://wikipedia.org/wiki/Landslide"}, {"title": "Yanawayin Lake", "link": "https://wikipedia.org/wiki/Yanawayin_Lake"}]}, {"year": "1974", "text": "<PERSON><PERSON><PERSON>, a nude sculpture by <PERSON><PERSON><PERSON><PERSON> in Istanbul is torn down in the middle of the night.", "html": "1974 - <a href=\"https://wikipedia.org/wiki/G%C3%BCzel_%C4%B0stanbul\" title=\"G<PERSON>zel İstanbul\"><PERSON><PERSON><PERSON></a>, a nude sculpture by <a href=\"https://wikipedia.org/wiki/G%C3%<PERSON><PERSON><PERSON>_Duyar\" title=\"<PERSON><PERSON>rda<PERSON>yar\"><PERSON><PERSON><PERSON><PERSON></a> in Istanbul is torn down in the middle of the night.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/G%C3%BCzel_%C4%B0stanbul\" title=\"G<PERSON>zel İstanbul\"><PERSON><PERSON><PERSON></a>, a nude sculpture by <a href=\"https://wikipedia.org/wiki/G%C3%<PERSON><PERSON><PERSON>_Du<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a> in Istanbul is torn down in the middle of the night.", "links": [{"title": "Güzel <PERSON>", "link": "https://wikipedia.org/wiki/G%C3%BCzel_%C4%B0stanbul"}, {"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/G%C3%BCrdal_Duyar"}]}, {"year": "1980", "text": "A Vostok-2M rocket at Plesetsk Cosmodrome Site 43 explodes during a fueling operation, killing 48 people.", "html": "1980 - A <a href=\"https://wikipedia.org/wiki/Vostok-2M\" title=\"Vostok-2M\">Vostok-2M</a> rocket at <a href=\"https://wikipedia.org/wiki/Plesetsk_Cosmodrome_Site_43\" title=\"Plesetsk Cosmodrome Site 43\">Plesetsk Cosmodrome Site 43</a> <a href=\"https://wikipedia.org/wiki/1980_Plesetsk_launch_pad_disaster\" title=\"1980 Plesetsk launch pad disaster\">explodes during a fueling operation</a>, killing 48 people.", "no_year_html": "A <a href=\"https://wikipedia.org/wiki/Vostok-2M\" title=\"Vostok-2M\">Vostok-2M</a> rocket at <a href=\"https://wikipedia.org/wiki/Plesetsk_Cosmodrome_Site_43\" title=\"Plesetsk Cosmodrome Site 43\">Plesetsk Cosmodrome Site 43</a> <a href=\"https://wikipedia.org/wiki/1980_Plesetsk_launch_pad_disaster\" title=\"1980 Plesetsk launch pad disaster\">explodes during a fueling operation</a>, killing 48 people.", "links": [{"title": "Vostok-2M", "link": "https://wikipedia.org/wiki/Vostok-2M"}, {"title": "Plesetsk Cosmodrome Site 43", "link": "https://wikipedia.org/wiki/Plesetsk_Cosmodrome_Site_43"}, {"title": "1980 Plesetsk launch pad disaster", "link": "https://wikipedia.org/wiki/1980_Plesetsk_launch_pad_disaster"}]}, {"year": "1990", "text": "Germans in the German Democratic Republic vote in the first democratic elections in the former communist dictatorship.", "html": "1990 - Germans in the <a href=\"https://wikipedia.org/wiki/German_Democratic_Republic\" class=\"mw-redirect\" title=\"German Democratic Republic\">German Democratic Republic</a> vote in the <a href=\"https://wikipedia.org/wiki/1990_East_German_general_election\" title=\"1990 East German general election\">first democratic elections</a> in the former communist dictatorship.", "no_year_html": "Germans in the <a href=\"https://wikipedia.org/wiki/German_Democratic_Republic\" class=\"mw-redirect\" title=\"German Democratic Republic\">German Democratic Republic</a> vote in the <a href=\"https://wikipedia.org/wiki/1990_East_German_general_election\" title=\"1990 East German general election\">first democratic elections</a> in the former communist dictatorship.", "links": [{"title": "German Democratic Republic", "link": "https://wikipedia.org/wiki/German_Democratic_Republic"}, {"title": "1990 East German general election", "link": "https://wikipedia.org/wiki/1990_East_German_general_election"}]}, {"year": "1990", "text": "In the largest art theft in US history, 12 paintings, collectively worth around $500 million, are stolen from the Isabella Stewart Gardner Museum in Boston.", "html": "1990 - In the largest <a href=\"https://wikipedia.org/wiki/Art_theft\" title=\"Art theft\">art theft</a> in US history, 12 paintings, collectively worth around $500 million, <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_Museum_theft\" title=\"Isabella Stewart Gardner Museum theft\">are stolen</a> from the <a href=\"https://wikipedia.org/wiki/Isabella_Stewart_Gardner_Museum\" title=\"Isabella Stewart Gardner Museum\">Isabella Stewart Gardner Museum</a> in <a href=\"https://wikipedia.org/wiki/Boston\" title=\"Boston\">Boston</a>.", "no_year_html": "In the largest <a href=\"https://wikipedia.org/wiki/Art_theft\" title=\"Art theft\">art theft</a> in US history, 12 paintings, collectively worth around $500 million, <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_Museum_theft\" title=\"Isabella Stewart Gardner Museum theft\">are stolen</a> from the <a href=\"https://wikipedia.org/wiki/Isabella_<PERSON>_Gardner_Museum\" title=\"Isabella Stewart Gardner Museum\">Isabella Stewart Gardner Museum</a> in <a href=\"https://wikipedia.org/wiki/Boston\" title=\"Boston\">Boston</a>.", "links": [{"title": "Art theft", "link": "https://wikipedia.org/wiki/Art_theft"}, {"title": "<PERSON> Gardner Museum theft", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_Gardner_Museum_theft"}, {"title": "<PERSON> Gardner Museum", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_Museum"}, {"title": "Boston", "link": "https://wikipedia.org/wiki/Boston"}]}, {"year": "1994", "text": "Bosnia's Bosniaks and Croats sign the Washington Agreement, ending war between the Croatian Republic of Herzeg-Bosnia and the Republic of Bosnia and Herzegovina, and establishing the Federation of Bosnia and Herzegovina.", "html": "1994 - <a href=\"https://wikipedia.org/wiki/Bosnia_and_Herzegovina\" title=\"Bosnia and Herzegovina\">Bosnia</a>'s <a href=\"https://wikipedia.org/wiki/Bosniaks\" title=\"Bosniaks\">Bosniaks</a> and <a href=\"https://wikipedia.org/wiki/Croats\" title=\"Croats\">Croats</a> sign the <a href=\"https://wikipedia.org/wiki/Washington_Agreement\" title=\"Washington Agreement\">Washington Agreement</a>, ending war between the <a href=\"https://wikipedia.org/wiki/Croatian_Republic_of_Herzeg-Bosnia\" title=\"Croatian Republic of Herzeg-Bosnia\">Croatian Republic of Herzeg-Bosnia</a> and the <a href=\"https://wikipedia.org/wiki/Republic_of_Bosnia_and_Herzegovina\" title=\"Republic of Bosnia and Herzegovina\">Republic of Bosnia and Herzegovina</a>, and establishing the <a href=\"https://wikipedia.org/wiki/Federation_of_Bosnia_and_Herzegovina\" title=\"Federation of Bosnia and Herzegovina\">Federation of Bosnia and Herzegovina</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Bosnia_and_Herzegovina\" title=\"Bosnia and Herzegovina\">Bosnia</a>'s <a href=\"https://wikipedia.org/wiki/Bosniaks\" title=\"Bosniaks\">Bosniaks</a> and <a href=\"https://wikipedia.org/wiki/Croats\" title=\"Croats\">Croats</a> sign the <a href=\"https://wikipedia.org/wiki/Washington_Agreement\" title=\"Washington Agreement\">Washington Agreement</a>, ending war between the <a href=\"https://wikipedia.org/wiki/Croatian_Republic_of_Herzeg-Bosnia\" title=\"Croatian Republic of Herzeg-Bosnia\">Croatian Republic of Herzeg-Bosnia</a> and the <a href=\"https://wikipedia.org/wiki/Republic_of_Bosnia_and_Herzegovina\" title=\"Republic of Bosnia and Herzegovina\">Republic of Bosnia and Herzegovina</a>, and establishing the <a href=\"https://wikipedia.org/wiki/Federation_of_Bosnia_and_Herzegovina\" title=\"Federation of Bosnia and Herzegovina\">Federation of Bosnia and Herzegovina</a>.", "links": [{"title": "Bosnia and Herzegovina", "link": "https://wikipedia.org/wiki/Bosnia_and_Herzegovina"}, {"title": "Bosniaks", "link": "https://wikipedia.org/wiki/Bosniaks"}, {"title": "Croats", "link": "https://wikipedia.org/wiki/Croats"}, {"title": "Washington Agreement", "link": "https://wikipedia.org/wiki/Washington_Agreement"}, {"title": "Croatian Republic of Herzeg-Bosnia", "link": "https://wikipedia.org/wiki/Croatian_Republic_of_Herzeg-Bosnia"}, {"title": "Republic of Bosnia and Herzegovina", "link": "https://wikipedia.org/wiki/Republic_of_Bosnia_and_Herzegovina"}, {"title": "Federation of Bosnia and Herzegovina", "link": "https://wikipedia.org/wiki/Federation_of_Bosnia_and_Herzegovina"}]}, {"year": "1996", "text": "A nightclub fire in Quezon City, Philippines kills 162 people.", "html": "1996 - A <a href=\"https://wikipedia.org/wiki/Ozone_Disco_fire\" title=\"Ozone Disco fire\">nightclub fire</a> in <a href=\"https://wikipedia.org/wiki/Quezon_City\" title=\"Quezon City\">Quezon City</a>, Philippines kills 162 people.", "no_year_html": "A <a href=\"https://wikipedia.org/wiki/Ozone_Disco_fire\" title=\"Ozone Disco fire\">nightclub fire</a> in <a href=\"https://wikipedia.org/wiki/Quezon_City\" title=\"Quezon City\">Quezon City</a>, Philippines kills 162 people.", "links": [{"title": "Ozone Disco fire", "link": "https://wikipedia.org/wiki/Ozone_Disco_fire"}, {"title": "Quezon City", "link": "https://wikipedia.org/wiki/Quezon_City"}]}, {"year": "1997", "text": "The tail of a Russian Antonov An-24 charter plane breaks off while en route to Turkey, causing the plane to crash and killing all 50 people on board.", "html": "1997 - The tail of a Russian <a href=\"https://wikipedia.org/wiki/Antonov_An-24\" title=\"Antonov An-24\">Antonov An-24</a> charter plane <a href=\"https://wikipedia.org/wiki/Stavropolskaya_Aktsionernaya_Avia_Flight_1023\" title=\"Stavropolskaya Aktsionernaya Avia Flight 1023\">breaks off</a> while en route to Turkey, causing the plane to crash and killing all 50 people on board.", "no_year_html": "The tail of a Russian <a href=\"https://wikipedia.org/wiki/Antonov_An-24\" title=\"Antonov An-24\">Antonov An-24</a> charter plane <a href=\"https://wikipedia.org/wiki/Stavropolskaya_Aktsionernaya_Avia_Flight_1023\" title=\"Stavropolskaya Aktsionernaya Avia Flight 1023\">breaks off</a> while en route to Turkey, causing the plane to crash and killing all 50 people on board.", "links": [{"title": "Antonov An-24", "link": "https://wikipedia.org/wiki/Antonov_An-24"}, {"title": "Stavropolskaya Aktsionernaya Avia Flight 1023", "link": "https://wikipedia.org/wiki/Stavropolskaya_Aktsionernaya_Avia_Flight_1023"}]}, {"year": "2014", "text": "The parliaments of Russia and Crimea sign an accession treaty.", "html": "2014 - The parliaments of Russia and <a href=\"https://wikipedia.org/wiki/Crimea\" title=\"Crimea\">Crimea</a> sign an <a href=\"https://wikipedia.org/wiki/Annexation_of_Crimea_by_the_Russian_Federation\" title=\"Annexation of Crimea by the Russian Federation\">accession treaty</a>.", "no_year_html": "The parliaments of Russia and <a href=\"https://wikipedia.org/wiki/Crimea\" title=\"Crimea\">Crimea</a> sign an <a href=\"https://wikipedia.org/wiki/Annexation_of_Crimea_by_the_Russian_Federation\" title=\"Annexation of Crimea by the Russian Federation\">accession treaty</a>.", "links": [{"title": "Crimea", "link": "https://wikipedia.org/wiki/Crimea"}, {"title": "Annexation of Crimea by the Russian Federation", "link": "https://wikipedia.org/wiki/Annexation_of_Crimea_by_the_Russian_Federation"}]}, {"year": "2015", "text": "The Bardo National Museum in Tunisia is attacked by gunmen. Twenty-four people, almost all tourists, are killed, and at least 50 other people are wounded.", "html": "2015 - The <a href=\"https://wikipedia.org/wiki/Bardo_National_Museum_(Tunis)\" title=\"Bardo National Museum (Tunis)\">Bardo National Museum</a> in <a href=\"https://wikipedia.org/wiki/Tunisia\" title=\"Tunisia\">Tunisia</a> is <a href=\"https://wikipedia.org/wiki/Bardo_National_Museum_attack\" title=\"Bardo National Museum attack\">attacked</a> by gunmen. Twenty-four people, almost all tourists, are killed, and at least 50 other people are wounded.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Bardo_National_Museum_(Tunis)\" title=\"Bardo National Museum (Tunis)\">Bardo National Museum</a> in <a href=\"https://wikipedia.org/wiki/Tunisia\" title=\"Tunisia\">Tunisia</a> is <a href=\"https://wikipedia.org/wiki/Bardo_National_Museum_attack\" title=\"Bardo National Museum attack\">attacked</a> by gunmen. Twenty-four people, almost all tourists, are killed, and at least 50 other people are wounded.", "links": [{"title": "Bardo National Museum (Tunis)", "link": "https://wikipedia.org/wiki/Bardo_National_Museum_(Tunis)"}, {"title": "Tunisia", "link": "https://wikipedia.org/wiki/Tunisia"}, {"title": "Bardo National Museum attack", "link": "https://wikipedia.org/wiki/Bardo_National_Museum_attack"}]}], "Births": [{"year": "1075", "text": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, Persian scholar and theologian (d. 1144)", "html": "1075 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>\" title=\"Al-Zamakhshari\"><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON></a>, Persian scholar and theologian (d. 1144)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Al<PERSON>\" title=\"Al-Zamakhshari\"><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON></a>, Persian scholar and theologian (d. 1144)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>-<PERSON>"}]}, {"year": "1395", "text": "<PERSON>, 2nd Duke of Exeter, English military commander (d. 1447)", "html": "1395 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_2nd_Duke_of_Exeter\" title=\"<PERSON>, 2nd Duke of Exeter\"><PERSON>, 2nd Duke of Exeter</a>, English military commander (d. 1447)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_2nd_Duke_of_Exeter\" title=\"<PERSON>, 2nd Duke of Exeter\"><PERSON>, 2nd Duke of Exeter</a>, English military commander (d. 1447)", "links": [{"title": "<PERSON>, 2nd Duke of Exeter", "link": "https://wikipedia.org/wiki/<PERSON>,_2nd_Duke_of_Exeter"}]}, {"year": "1495", "text": "<PERSON>, Queen of France (d. 1533)", "html": "1495 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Queen_of_France\" title=\"<PERSON>, Queen of France\"><PERSON>, Queen of France</a> (d. 1533)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Queen_of_France\" title=\"<PERSON>, Queen of France\"><PERSON>, Queen of France</a> (d. 1533)", "links": [{"title": "<PERSON>, Queen of France", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Queen_of_France"}]}, {"year": "1548", "text": "<PERSON><PERSON><PERSON>, Dutch painter (d. 1616)", "html": "1548 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"Cornelis Ketel\"><PERSON><PERSON><PERSON></a>, Dutch painter (d. 1616)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"Cornelis <PERSON>tel\"><PERSON><PERSON><PERSON></a>, Dutch painter (d. 1616)", "links": [{"title": "Cornelis <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>rnelis_Ketel"}]}, {"year": "1552", "text": "<PERSON><PERSON><PERSON><PERSON> the Elder, German theologian (d. 1610)", "html": "1552 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>_the_Elder\" title=\"<PERSON><PERSON><PERSON><PERSON> the Elder\"><PERSON><PERSON><PERSON><PERSON> the Elder</a>, German theologian (d. 1610)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>_the_Elder\" title=\"<PERSON><PERSON><PERSON><PERSON> the Elder\"><PERSON><PERSON><PERSON><PERSON> the Elder</a>, German theologian (d. 1610)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON> the Elder", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON><PERSON><PERSON>_the_Elder"}]}, {"year": "1554", "text": "<PERSON><PERSON><PERSON>, Count of Waldeck-Eisenberg, Count of Waldeck-Eisenberg (d. 1588)", "html": "1554 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>,_Count_of_Waldeck-Eisenberg\" title=\"<PERSON><PERSON><PERSON>, Count of Waldeck-Eisenberg\"><PERSON><PERSON><PERSON> <PERSON>, Count of Waldeck-Eisenberg</a>, Count of Waldeck-Eisenberg (d. 1588)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>,_Count_of_Waldeck-Eisenberg\" title=\"<PERSON><PERSON><PERSON> <PERSON>, Count of Waldeck-Eisenberg\"><PERSON><PERSON><PERSON> <PERSON>, Count of Waldeck-Eisenberg</a>, Count of Waldeck-Eisenberg (d. 1588)", "links": [{"title": "<PERSON><PERSON><PERSON>, Count of Waldeck-Eisenberg", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>,_Count_<PERSON>_<PERSON><PERSON><PERSON>-<PERSON>"}]}, {"year": "1555", "text": "<PERSON>, Duke of Anjou (d. 1584)", "html": "1555 - <a href=\"https://wikipedia.org/wiki/<PERSON>,_Duke_of_Anjou\" title=\"<PERSON>, Duke of Anjou\"><PERSON>, Duke of Anjou</a> (d. 1584)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>,_Duke_of_Anjou\" title=\"<PERSON>, Duke of Anjou\"><PERSON>, Duke of Anjou</a> (d. 1584)", "links": [{"title": "<PERSON>, Duke of Anjou", "link": "https://wikipedia.org/wiki/<PERSON>,_Duke_of_Anjou"}]}, {"year": "1578", "text": "<PERSON>, German painter (d. 1610)", "html": "1578 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German painter (d. 1610)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German painter (d. 1610)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1590", "text": "<PERSON>, Portuguese historian and poet (d. 1649)", "html": "1590 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_e_Sousa\" title=\"<PERSON> e Sousa\"><PERSON> Sousa</a>, Portuguese historian and poet (d. 1649)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_e_Sousa\" title=\"<PERSON> Faria e Sousa\"><PERSON> Sousa</a>, Portuguese historian and poet (d. 1649)", "links": [{"title": "<PERSON> Faria e Sousa", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1597", "text": "<PERSON><PERSON><PERSON><PERSON>, French religious leader, founded the Société Notre-Dame de Montréal (d. 1659)", "html": "1597 - <a href=\"https://wikipedia.org/wiki/J%C3%A9r%C3%B4<PERSON>_<PERSON>_<PERSON>_de_la_Dauversi%C3%A8re\" title=\"<PERSON><PERSON><PERSON><PERSON> de la Dauversière\"><PERSON><PERSON><PERSON><PERSON> la Dauversière</a>, French religious leader, founded the <a href=\"https://wikipedia.org/wiki/Soci%C3%A9t%C3%A9_Notre-Dame_de_Montr%C3%A9al\" title=\"Société Notre-Dame de Montréal\">Société Notre-Dame de Montréal</a> (d. 1659)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/J%C3%A9r%C3%B4<PERSON>_<PERSON>_<PERSON><PERSON>_de_la_Dauversi%C3%A8re\" title=\"<PERSON><PERSON><PERSON><PERSON> Royer de la Dauversière\"><PERSON><PERSON><PERSON><PERSON> <PERSON> la Dauversière</a>, French religious leader, founded the <a href=\"https://wikipedia.org/wiki/Soci%C3%A9t%C3%A9_Notre-Dame_de_Montr%C3%A9al\" title=\"Société Notre-Dame de Montréal\">Société Notre-Dame de Montréal</a> (d. 1659)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON> la Dauversière", "link": "https://wikipedia.org/wiki/J%C3%A9r%C3%B4<PERSON>_<PERSON>_<PERSON><PERSON>_<PERSON>_la_Dauversi%C3%A8re"}, {"title": "Société Notre-Dame de Montréal", "link": "https://wikipedia.org/wiki/Soci%C3%A9t%C3%A9_Notre-Dame_de_Montr%C3%A9al"}]}, {"year": "1603", "text": "<PERSON>, English colonial magistrate (d. 1697)", "html": "1603 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English colonial magistrate (d. 1697)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English colonial magistrate (d. 1697)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1609", "text": "<PERSON> of Denmark (d. 1670)", "html": "1609 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Denmark\" title=\"<PERSON> of Denmark\"><PERSON> of Denmark</a> (d. 1670)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Denmark\" title=\"<PERSON> of Denmark\"><PERSON> of Denmark</a> (d. 1670)", "links": [{"title": "<PERSON> of Denmark", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Denmark"}]}, {"year": "1634", "text": "<PERSON>, French author (d. 1693)", "html": "1634 - <a href=\"https://wikipedia.org/wiki/Madame_<PERSON>_<PERSON>_<PERSON>\" title=\"Madame <PERSON> La Faye<PERSON>\">Madame <PERSON></a>, French author (d. 1693)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Madame_<PERSON>_<PERSON>_<PERSON>\" title=\"Madame <PERSON> La Fayette\">Madame <PERSON></a>, French author (d. 1693)", "links": [{"title": "<PERSON> de La Fayette", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1640", "text": "<PERSON>, French mathematician and astronomer (d. 1719)", "html": "1640 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French mathematician and astronomer (d. 1719)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French mathematician and astronomer (d. 1719)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1657", "text": "<PERSON>, Italian organist and composer (d. 1743)", "html": "1657 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian organist and composer (d. 1743)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian organist and composer (d. 1743)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1690", "text": "<PERSON>, Prussian-German mathematician and academic (d. 1764)", "html": "1690 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Prussian-German mathematician and academic (d. 1764)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Prussian-German mathematician and academic (d. 1764)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1701", "text": "<PERSON><PERSON><PERSON>, Swedish businessman and philanthropist, co-founded the Swedish East India Company (d. 1776)", "html": "1701 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Swedish businessman and philanthropist, co-founded the <a href=\"https://wikipedia.org/wiki/Swedish_East_India_Company\" title=\"Swedish East India Company\">Swedish East India Company</a> (d. 1776)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Swedish businessman and philanthropist, co-founded the <a href=\"https://wikipedia.org/wiki/Swedish_East_India_Company\" title=\"Swedish East India Company\">Swedish East India Company</a> (d. 1776)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}, {"title": "Swedish East India Company", "link": "https://wikipedia.org/wiki/Swedish_East_India_Company"}]}, {"year": "1733", "text": "<PERSON>, German author and bookseller (d. 1811)", "html": "1733 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German author and bookseller (d. 1811)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German author and bookseller (d. 1811)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1780", "text": "<PERSON><PERSON>, Serbian prince (d. 1860)", "html": "1780 - <a href=\"https://wikipedia.org/wiki/Milo%C5%A1_Obrenovi%C4%87\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Serbian prince (d. 1860)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Milo%C5%A1_Obrenovi%C4%87\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Serbian prince (d. 1860)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Milo%C5%A1_Obrenovi%C4%87"}]}, {"year": "1782", "text": "<PERSON>, American lawyer and politician, 7th Vice President of the United States (d. 1850)", "html": "1782 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and politician, 7th <a href=\"https://wikipedia.org/wiki/Vice_President_of_the_United_States\" title=\"Vice President of the United States\">Vice President of the United States</a> (d. 1850)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and politician, 7th <a href=\"https://wikipedia.org/wiki/Vice_President_of_the_United_States\" title=\"Vice President of the United States\">Vice President of the United States</a> (d. 1850)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "Vice President of the United States", "link": "https://wikipedia.org/wiki/Vice_President_of_the_United_States"}]}, {"year": "1789", "text": "<PERSON>, English poet, hymn writer, editor (d. 1871)", "html": "1789 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English poet, hymn writer, editor (d. 1871)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English poet, hymn writer, editor (d. 1871)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1798", "text": "<PERSON>, German-American jurist and philosopher (d. 1872)", "html": "1798 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German-American jurist and philosopher (d. 1872)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German-American jurist and philosopher (d. 1872)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1800", "text": "<PERSON>, Irish actress, the first wife and muse of <PERSON> (d. 1854)", "html": "1800 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Irish actress, the first wife and muse of <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> (d. 1854)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Irish actress, the first wife and muse of <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> (d. 1854)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1813", "text": "<PERSON>, German poet and playwright (d. 1864)", "html": "1813 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, German poet and playwright (d. 1864)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"Christian <PERSON>\"><PERSON></a>, German poet and playwright (d. 1864)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1814", "text": "<PERSON>, American businessman (d. 1897)", "html": "1814 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, American businessman (d. 1897)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, American businessman (d. 1897)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1819", "text": "<PERSON>, Scottish-Australian politician, 5th Premier of Victoria (d. 1893)", "html": "1819 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish-Australian politician, 5th <a href=\"https://wikipedia.org/wiki/Premier_of_Victoria\" title=\"Premier of Victoria\">Premier of Victoria</a> (d. 1893)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish-Australian politician, 5th <a href=\"https://wikipedia.org/wiki/Premier_of_Victoria\" title=\"Premier of Victoria\">Premier of Victoria</a> (d. 1893)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Premier of Victoria", "link": "https://wikipedia.org/wiki/Premier_of_Victoria"}]}, {"year": "1820", "text": "<PERSON>, American businessman, industrialist, and philanthropist (d. 1891)", "html": "1820 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American businessman, industrialist, and philanthropist (d. 1891)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American businessman, industrialist, and philanthropist (d. 1891)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1823", "text": "<PERSON>, French general (d. 1883)", "html": "1823 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French general (d. 1883)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French general (d. 1883)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1828", "text": "<PERSON><PERSON>, English activist and politician, Nobel Prize laureate (d. 1908)", "html": "1828 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, English activist and politician, <a href=\"https://wikipedia.org/wiki/Nobel_Peace_Prize\" title=\"Nobel Peace Prize\">Nobel Prize</a> laureate (d. 1908)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, English activist and politician, <a href=\"https://wikipedia.org/wiki/Nobel_Peace_Prize\" title=\"Nobel Peace Prize\">Nobel Prize</a> laureate (d. 1908)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}, {"title": "Nobel Peace Prize", "link": "https://wikipedia.org/wiki/Nobel_Peace_Prize"}]}, {"year": "1837", "text": "<PERSON><PERSON>, American lawyer and politician, 22nd and 24th President of the United States (d. 1908)", "html": "1837 - <a href=\"https://wikipedia.org/wiki/Grover_Cleveland\" title=\"Grover Cleveland\"><PERSON><PERSON></a>, American lawyer and politician, 22nd and 24th <a href=\"https://wikipedia.org/wiki/President_of_the_United_States\" title=\"President of the United States\">President of the United States</a> (d. 1908)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Grover_Cleveland\" title=\"Grover Cleveland\"><PERSON><PERSON></a>, American lawyer and politician, 22nd and 24th <a href=\"https://wikipedia.org/wiki/President_of_the_United_States\" title=\"President of the United States\">President of the United States</a> (d. 1908)", "links": [{"title": "Grover <PERSON>", "link": "https://wikipedia.org/wiki/Grover_Cleveland"}, {"title": "President of the United States", "link": "https://wikipedia.org/wiki/President_of_the_United_States"}]}, {"year": "1840", "text": "<PERSON>, English poet and critic (d. 1901)", "html": "1840 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_Monkhouse\" title=\"William <PERSON> Monkhouse\"><PERSON></a>, English poet and critic (d. 1901)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_Monk<PERSON>\" title=\"William <PERSON> Monkhouse\"><PERSON></a>, English poet and critic (d. 1901)", "links": [{"title": "William <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_Monkhouse"}]}, {"year": "1842", "text": "<PERSON><PERSON><PERSON><PERSON>, French poet and critic (d. 1898)", "html": "1842 - <a href=\"https://wikipedia.org/wiki/St%C3%A9phane_Mallarm%C3%A9\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, French poet and critic (d. 1898)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/St%C3%A9phane_Mallarm%C3%A9\" title=\"<PERSON><PERSON><PERSON><PERSON>arm<PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, French poet and critic (d. 1898)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/St%C3%A9phane_Mallarm%C3%A9"}]}, {"year": "1844", "text": "<PERSON><PERSON><PERSON><PERSON>, Russian composer and academic (d. 1908)", "html": "1844 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian composer and academic (d. 1908)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian composer and academic (d. 1908)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1845", "text": "<PERSON><PERSON> <PERSON>, Native American tribal leader (d. 1904)", "html": "1845 - <a href=\"https://wikipedia.org/wiki/Kicking_Bear\" title=\"Kicking Bear\">Kicking Bear</a>, Native American tribal leader (d. 1904)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Kicking_Bear\" title=\"Kicking Bear\">Kicking Bear</a>, Native American tribal leader (d. 1904)", "links": [{"title": "Kicking Bear", "link": "https://wikipedia.org/wiki/Kicking_<PERSON>"}]}, {"year": "1848", "text": "<PERSON><PERSON>, American architect and engineer (d. 1938)", "html": "1848 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American architect and engineer (d. 1938)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American architect and engineer (d. 1938)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1858", "text": "<PERSON>, German engineer, invented the Diesel engine (d. 1913)", "html": "1858 - <a href=\"https://wikipedia.org/wiki/<PERSON>_Diesel\" title=\"Rudolf Diesel\"><PERSON></a>, German engineer, invented the <a href=\"https://wikipedia.org/wiki/Diesel_engine\" title=\"Diesel engine\">Diesel engine</a> (d. 1913)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_Diesel\" title=\"Rudolf Diesel\"><PERSON></a>, German engineer, invented the <a href=\"https://wikipedia.org/wiki/Diesel_engine\" title=\"Diesel engine\">Diesel engine</a> (d. 1913)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Diesel engine", "link": "https://wikipedia.org/wiki/Diesel_engine"}]}, {"year": "1862", "text": "<PERSON>, Swedish painter (d. 1915)", "html": "1862 - <a href=\"https://wikipedia.org/wiki/Eug%C3%A<PERSON><PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swedish painter (d. 1915)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Eug%C3%A<PERSON><PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swedish painter (d. 1915)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Eug%C3%A8ne_<PERSON><PERSON>"}]}, {"year": "1863", "text": "<PERSON>, American lawyer and politician, 39th Governor of New York (d. 1941)", "html": "1863 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and politician, 39th <a href=\"https://wikipedia.org/wiki/Governor_of_New_York\" title=\"Governor of New York\">Governor of New York</a> (d. 1941)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and politician, 39th <a href=\"https://wikipedia.org/wiki/Governor_of_New_York\" title=\"Governor of New York\">Governor of New York</a> (d. 1941)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Governor of New York", "link": "https://wikipedia.org/wiki/Governor_of_New_York"}]}, {"year": "1869", "text": "<PERSON>, English businessman and politician, Prime Minister of the United Kingdom (d. 1940)", "html": "1869 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English businessman and politician, <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_the_United_Kingdom\" title=\"Prime Minister of the United Kingdom\">Prime Minister of the United Kingdom</a> (d. 1940)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English businessman and politician, <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_the_United_Kingdom\" title=\"Prime Minister of the United Kingdom\">Prime Minister of the United Kingdom</a> (d. 1940)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Prime Minister of the United Kingdom", "link": "https://wikipedia.org/wiki/Prime_Minister_of_the_United_Kingdom"}]}, {"year": "1870", "text": "<PERSON>, Canadian mathematician (d. 1917)", "html": "1870 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian mathematician (d. 1917)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian mathematician (d. 1917)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "1874", "text": "<PERSON>, Russian-French philosopher and theologian (d. 1948)", "html": "1874 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian-French philosopher and theologian (d. 1948)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian-French philosopher and theologian (d. 1948)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1877", "text": "<PERSON>, American mystic and psychic (d. 1945)", "html": "1877 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American mystic and psychic (d. 1945)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American mystic and psychic (d. 1945)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1877", "text": "<PERSON><PERSON>, Australian cricketer and engineer (d. 1945)", "html": "1877 - <a href=\"https://wikipedia.org/wiki/Clem_Hill\" title=\"Clem Hill\"><PERSON><PERSON></a>, Australian cricketer and engineer (d. 1945)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Clem_Hill\" title=\"Clem Hill\"><PERSON><PERSON></a>, Australian cricketer and engineer (d. 1945)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Clem_Hill"}]}, {"year": "1878", "text": "<PERSON><PERSON><PERSON>, 1st Baron <PERSON>, English businessman (d. 1956)", "html": "1878 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>,_1st_Baron_<PERSON>\" title=\"<PERSON><PERSON><PERSON>, 1st Baron <PERSON>\"><PERSON><PERSON><PERSON>, 1st Baron <PERSON></a>, English businessman (d. 1956)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>,_1st_Baron_<PERSON>\" title=\"<PERSON><PERSON><PERSON>, 1st Baron <PERSON>\"><PERSON><PERSON><PERSON>, 1st Baron <PERSON></a>, English businessman (d. 1956)", "links": [{"title": "<PERSON><PERSON><PERSON>, 1st Baron <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>,_1st_<PERSON>_<PERSON>"}]}, {"year": "1880", "text": "<PERSON><PERSON>, Finnish politician (d. 1947)", "html": "1880 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Finnish politician (d. 1947)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Finnish politician (d. 1947)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1882", "text": "<PERSON><PERSON>, Italian composer and educator (d. 1973)", "html": "1882 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Italian composer and educator (d. 1973)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Italian composer and educator (d. 1973)", "links": [{"title": "<PERSON><PERSON> <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1884", "text": "<PERSON>, English-Australian journalist and author (d. 1968)", "html": "1884 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English-Australian journalist and author (d. 1968)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English-Australian journalist and author (d. 1968)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1886", "text": "<PERSON>, American actor, singer, and dancer (d. 1970)", "html": "1886 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor, singer, and dancer (d. 1970)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor, singer, and dancer (d. 1970)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1890", "text": "<PERSON>, French director and screenwriter (d. 1969)", "html": "1890 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French director and screenwriter (d. 1969)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French director and screenwriter (d. 1969)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1893", "text": "<PERSON><PERSON>, Italian cyclist (d. 1978)", "html": "1893 - <a href=\"https://wikipedia.org/wiki/Costante_Girardengo\" title=\"Costante Girardengo\"><PERSON><PERSON></a>, Italian cyclist (d. 1978)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Costante_Girardengo\" title=\"Costante Girardengo\"><PERSON><PERSON></a>, Italian cyclist (d. 1978)", "links": [{"title": "Costante Girardengo", "link": "https://wikipedia.org/wiki/Costante_Girardengo"}]}, {"year": "1893", "text": "<PERSON><PERSON><PERSON>, English soldier and poet (d. 1918)", "html": "1893 - <a href=\"https://wikipedia.org/wiki/W<PERSON><PERSON>_<PERSON>\" title=\"W<PERSON><PERSON>\">W<PERSON><PERSON></a>, English soldier and poet (d. 1918)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/W<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\">W<PERSON><PERSON></a>, English soldier and poet (d. 1918)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/W<PERSON><PERSON>_<PERSON>"}]}, {"year": "1899", "text": "<PERSON>, English toy-maker and businesswoman (d. 1991)", "html": "1899 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English toy-maker and businesswoman (d. 1991)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English toy-maker and businesswoman (d. 1991)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1901", "text": "<PERSON><PERSON>, Canadian mystic, author and philosopher (d. 1990)", "html": "1901 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_Hall\" class=\"mw-redirect\" title=\"<PERSON>ly Palmer Hall\"><PERSON><PERSON></a>, Canadian mystic, author and philosopher (d. 1990)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_Hall\" class=\"mw-redirect\" title=\"<PERSON>ly Palmer Hall\"><PERSON><PERSON></a>, Canadian mystic, author and philosopher (d. 1990)", "links": [{"title": "<PERSON><PERSON> Palmer Hall", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_Hall"}]}, {"year": "1901", "text": "<PERSON>, American painter (d. 1970)", "html": "1901 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(artist)\" class=\"mw-redirect\" title=\"<PERSON> (artist)\"><PERSON></a>, American painter (d. 1970)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(artist)\" class=\"mw-redirect\" title=\"<PERSON> (artist)\"><PERSON></a>, American painter (d. 1970)", "links": [{"title": "<PERSON> (artist)", "link": "https://wikipedia.org/wiki/<PERSON>_(artist)"}]}, {"year": "1903", "text": "<PERSON><PERSON><PERSON>, Italian journalist and politician, Italian Minister of Foreign Affairs (d. 1944)", "html": "1903 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Italian journalist and politician, <a href=\"https://wikipedia.org/wiki/Minister_of_Foreign_Affairs_(Italy)\" title=\"Minister of Foreign Affairs (Italy)\">Italian Minister of Foreign Affairs</a> (d. 1944)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Italian journalist and politician, <a href=\"https://wikipedia.org/wiki/Minister_of_Foreign_Affairs_(Italy)\" title=\"Minister of Foreign Affairs (Italy)\">Italian Minister of Foreign Affairs</a> (d. 1944)", "links": [{"title": "Galeazzo <PERSON>iano", "link": "https://wikipedia.org/wiki/Galeazzo_Ciano"}, {"title": "Minister of Foreign Affairs (Italy)", "link": "https://wikipedia.org/wiki/Minister_of_Foreign_Affairs_(Italy)"}]}, {"year": "1903", "text": "<PERSON><PERSON> <PERSON><PERSON>, German cartoonist (d. 1944)", "html": "1903 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"E. O. <PERSON>en\"><PERSON><PERSON> <PERSON><PERSON></a>, German cartoonist (d. 1944)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"E. O. <PERSON>en\"><PERSON><PERSON> <PERSON><PERSON></a>, German cartoonist (d. 1944)", "links": [{"title": "<PERSON><PERSON> <PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "1904", "text": "<PERSON><PERSON><PERSON><PERSON>, Slovenian poet and author (d. 1926)", "html": "1904 - <a href=\"https://wikipedia.org/wiki/Sre%C4%8D<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Slovenian poet and author (d. 1926)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Sre%C4%8D<PERSON>_<PERSON>vel\" title=\"<PERSON><PERSON><PERSON><PERSON> Kosovel\"><PERSON><PERSON><PERSON><PERSON></a>, Slovenian poet and author (d. 1926)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Sre%C4%8Dko_<PERSON>vel"}]}, {"year": "1905", "text": "<PERSON>, American physicist and engineer (d. 1985)", "html": "1905 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American physicist and engineer (d. 1985)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American physicist and engineer (d. 1985)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1905", "text": "<PERSON>, English actor (d. 1958)", "html": "1905 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actor (d. 1958)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actor (d. 1958)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1907", "text": "<PERSON>, English zoologist and neurophysiologist (d. 1997)", "html": "1907 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English zoologist and neurophysiologist (d. 1997)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English zoologist and neurophysiologist (d. 1997)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1908", "text": "<PERSON><PERSON>, French composer (d. 1995)", "html": "1908 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>%C3%A9\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, French composer (d. 1995)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>%C3%A9\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, French composer (d. 1995)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Loulou_Gast%C3%A9"}]}, {"year": "1909", "text": "<PERSON>, American businessman, co-founded the E & J Gallo Winery (d. 2007)", "html": "1909 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American businessman, co-founded the <a href=\"https://wikipedia.org/wiki/E_%26_<PERSON>_<PERSON>allo_Winery\" title=\"E &amp; <PERSON>allo Winery\">E &amp; J <PERSON> Winery</a> (d. 2007)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American businessman, co-founded the <a href=\"https://wikipedia.org/wiki/E_%26_<PERSON>_<PERSON>_Winery\" title=\"E &amp; <PERSON> Winery\">E &amp; J <PERSON> Winery</a> (d. 2007)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}, {"title": "E & J Gallo Winery", "link": "https://wikipedia.org/wiki/E_%26_<PERSON>_<PERSON>_Winery"}]}, {"year": "1909", "text": "<PERSON><PERSON>, English author and illustrator (d. 2004)", "html": "1909 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON> <PERSON></a>, English author and illustrator (d. 2004)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON> <PERSON></a>, English author and illustrator (d. 2004)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1911", "text": "<PERSON><PERSON>, American singer-songwriter and actor (d. 1967)", "html": "1911 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American singer-songwriter and actor (d. 1967)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>e\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American singer-songwriter and actor (d. 1967)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Smiley_Burnette"}]}, {"year": "1912", "text": "<PERSON>, American voice actor and announcer (d. 2010)", "html": "1912 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American voice actor and announcer (d. 2010)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American voice actor and announcer (d. 2010)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1913", "text": "<PERSON>, French director and screenwriter (d. 1996)", "html": "1913 - <a href=\"https://wikipedia.org/wiki/Ren%C3%A9_Cl%C3%A9ment\" title=\"<PERSON>\"><PERSON></a>, French director and screenwriter (d. 1996)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Ren%C3%A9_Cl%C3%A9ment\" title=\"<PERSON>\"><PERSON></a>, French director and screenwriter (d. 1996)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Ren%C3%A9_Cl%C3%A9ment"}]}, {"year": "1913", "text": "<PERSON>, German colonel and pilot (d. 1941)", "html": "1913 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%B6lders\" title=\"<PERSON>\"><PERSON></a>, German colonel and pilot (d. 1941)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%B6lder<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German colonel and pilot (d. 1941)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Werner_M%C3%B6lders"}]}, {"year": "1915", "text": "<PERSON>, American author and screenwriter (d. 1996)", "html": "1915 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author and screenwriter (d. 1996)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author and screenwriter (d. 1996)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1922", "text": "<PERSON><PERSON>, German journalist and politician, Federal Minister for Special Affairs of Germany (d. 2015)", "html": "1922 - <a href=\"https://wikipedia.org/wiki/Egon_Bahr\" title=\"Egon Bahr\"><PERSON><PERSON></a>, German journalist and politician, <a href=\"https://wikipedia.org/wiki/Federal_Minister_for_Special_Affairs_of_Germany\" title=\"Federal Minister for Special Affairs of Germany\">Federal Minister for Special Affairs of Germany</a> (d. 2015)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Egon_Bahr\" title=\"Egon Bahr\"><PERSON><PERSON></a>, German journalist and politician, <a href=\"https://wikipedia.org/wiki/Federal_Minister_for_Special_Affairs_of_Germany\" title=\"Federal Minister for Special Affairs of Germany\">Federal Minister for Special Affairs of Germany</a> (d. 2015)", "links": [{"title": "Egon Bahr", "link": "https://wikipedia.org/wiki/Egon_Bahr"}, {"title": "Federal Minister for Special Affairs of Germany", "link": "https://wikipedia.org/wiki/Federal_Minister_for_Special_Affairs_of_Germany"}]}, {"year": "1922", "text": "<PERSON>, American sociologist and academic (d. 2006)", "html": "1922 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American sociologist and academic (d. 2006)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American sociologist and academic (d. 2006)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1922", "text": "<PERSON>, Hungarian-Dutch visual artist (d. 2020)", "html": "1922 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Hungarian-Dutch visual artist (d. 2020)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Hungarian-Dutch visual artist (d. 2020)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1922", "text": "<PERSON>, American activist, co-founded the Southern Christian Leadership Conference (d. 2011)", "html": "1922 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American activist, co-founded the <a href=\"https://wikipedia.org/wiki/Southern_Christian_Leadership_Conference\" title=\"Southern Christian Leadership Conference\">Southern Christian Leadership Conference</a> (d. 2011)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American activist, co-founded the <a href=\"https://wikipedia.org/wiki/Southern_Christian_Leadership_Conference\" title=\"Southern Christian Leadership Conference\">Southern Christian Leadership Conference</a> (d. 2011)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Southern Christian Leadership Conference", "link": "https://wikipedia.org/wiki/Southern_Christian_Leadership_Conference"}]}, {"year": "1923", "text": "<PERSON>, American race car driver and businessman (d. 2013)", "html": "1923 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American race car driver and businessman (d. 2013)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American race car driver and businessman (d. 2013)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1925", "text": "<PERSON>, Italian musician (d. 2017)", "html": "1925 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian musician (d. 2017)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian musician (d. 2017)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1925", "text": "<PERSON>, English journalist, lawyer, and judge (d. 2010)", "html": "1925 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English journalist, lawyer, and judge (d. 2010)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English journalist, lawyer, and judge (d. 2010)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1926", "text": "<PERSON>, American actor and director (d. 2010)", "html": "1926 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor and director (d. 2010)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor and director (d. 2010)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1927", "text": "<PERSON>, American pianist and composer", "html": "1927 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American pianist and composer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American pianist and composer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1927", "text": "<PERSON>, American journalist and actor (d. 2003)", "html": "1927 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American journalist and actor (d. 2003)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American journalist and actor (d. 2003)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1927", "text": "<PERSON>, German-American businesswoman and philanthropist, founded the Lillian Vernon Company (d. 2015)", "html": "1927 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(businesswoman)\" title=\"<PERSON> (businesswoman)\"><PERSON></a>, German-American businesswoman and philanthropist, founded the <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>(company)\" title=\"<PERSON> (company)\">Lillian Vernon Company</a> (d. 2015)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(businesswoman)\" title=\"<PERSON> (businesswoman)\"><PERSON></a>, German-American businesswoman and philanthropist, founded the <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>(company)\" title=\"<PERSON> (company)\">Lillian Vernon Company</a> (d. 2015)", "links": [{"title": "<PERSON> (businesswoman)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>(businesswoman)"}, {"title": "<PERSON> (company)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(company)"}]}, {"year": "1928", "text": "<PERSON>, Spanish cyclist (d. 2013)", "html": "1928 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Spanish cyclist (d. 2013)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Spanish cyclist (d. 2013)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1928", "text": "<PERSON><PERSON>, Filipino general and politician, 12th President of the Philippines (d. 2022)", "html": "1928 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Filipino general and politician, 12th <a href=\"https://wikipedia.org/wiki/President_of_the_Philippines\" title=\"President of the Philippines\">President of the Philippines</a> (d. 2022)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Filipino general and politician, 12th <a href=\"https://wikipedia.org/wiki/President_of_the_Philippines\" title=\"President of the Philippines\">President of the Philippines</a> (d. 2022)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Fi<PERSON>_<PERSON><PERSON>_<PERSON>"}, {"title": "President of the Philippines", "link": "https://wikipedia.org/wiki/President_of_the_Philippines"}]}, {"year": "1929", "text": "<PERSON>, Polish-American lawyer and author (d. 2015)", "html": "1929 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Polish-American lawyer and author (d. 2015)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Polish-American lawyer and author (d. 2015)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1930", "text": "<PERSON>, American mathematician and academic (d. 1998)", "html": "1930 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>(mathematician)\" title=\"<PERSON> (mathematician)\"><PERSON></a>, American mathematician and academic (d. 1998)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>(mathematician)\" title=\"<PERSON> (mathematician)\"><PERSON></a>, American mathematician and academic (d. 1998)", "links": [{"title": "<PERSON> (mathematician)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_(mathematician)"}]}, {"year": "1931", "text": "<PERSON>, Scottish actor (d. 2020)", "html": "1931 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(actor)\" title=\"<PERSON> (actor)\"><PERSON></a>, Scottish actor (d. 2020)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(actor)\" title=\"<PERSON> (actor)\"><PERSON></a>, Scottish actor (d. 2020)", "links": [{"title": "<PERSON> (actor)", "link": "https://wikipedia.org/wiki/<PERSON>(actor)"}]}, {"year": "1932", "text": "<PERSON>, American novelist, short story writer, and critic (d. 2009)", "html": "1932 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American novelist, short story writer, and critic (d. 2009)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American novelist, short story writer, and critic (d. 2009)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1933", "text": "<PERSON><PERSON>, American civil rights activist and politician (d. 2019)", "html": "1933 - <a href=\"https://wikipedia.org/wiki/Unita_Blackwell\" title=\"Unita Blackwell\"><PERSON><PERSON></a>, American civil rights activist and politician (d. 2019)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Unita_Blackwell\" title=\"Unita Blackwell\"><PERSON><PERSON></a>, American civil rights activist and politician (d. 2019)", "links": [{"title": "Unita Blackwell", "link": "https://wikipedia.org/wiki/Unita_Blackwell"}]}, {"year": "1934", "text": "<PERSON>, English footballer and manager (d. 1983)", "html": "1934 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English footballer and manager (d. 1983)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English footballer and manager (d. 1983)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1934", "text": "<PERSON>, American country music singer and musician (d. 2020)", "html": "1934 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Charley Pride\"><PERSON></a>, American country music singer and musician (d. 2020)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Charley Pride\"><PERSON></a>, American country music singer and musician (d. 2020)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1935", "text": "<PERSON>-<PERSON>, Danish mathematician and statistician (d. 2022)", "html": "1935 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>\" title=\"<PERSON>\"><PERSON></a>, Danish mathematician and statistician (d. 2022)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>\" title=\"<PERSON>\"><PERSON></a>, Danish mathematician and statistician (d. 2022)", "links": [{"title": "<PERSON>-<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>-<PERSON>"}]}, {"year": "1935", "text": "<PERSON>, American psychiatrist and author (d. 2016)", "html": "1935 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American psychiatrist and author (d. 2016)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American psychiatrist and author (d. 2016)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1936", "text": "<PERSON><PERSON> <PERSON><PERSON>, South African lawyer and politician, former State President of South Africa, Nobel Prize laureate (d. 2021)", "html": "1936 - <a href=\"https://wikipedia.org/wiki/F<PERSON>_<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON> <PERSON><PERSON>\"><PERSON><PERSON> <PERSON><PERSON></a>, South African lawyer and politician, former <a href=\"https://wikipedia.org/wiki/State_President_of_South_Africa\" title=\"State President of South Africa\">State President of South Africa</a>, <a href=\"https://wikipedia.org/wiki/Nobel_Peace_Prize\" title=\"Nobel Peace Prize\">Nobel Prize</a> laureate (d. 2021)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/F<PERSON>_<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON> <PERSON><PERSON>\"><PERSON><PERSON> <PERSON><PERSON></a>, South African lawyer and politician, former <a href=\"https://wikipedia.org/wiki/State_President_of_South_Africa\" title=\"State President of South Africa\">State President of South Africa</a>, <a href=\"https://wikipedia.org/wiki/Nobel_Peace_Prize\" title=\"Nobel Peace Prize\">Nobel Prize</a> laureate (d. 2021)", "links": [{"title": "<PERSON><PERSON> <PERSON><PERSON>", "link": "https://wikipedia.org/wiki/F<PERSON>_<PERSON><PERSON>_<PERSON>_<PERSON>"}, {"title": "State President of South Africa", "link": "https://wikipedia.org/wiki/State_President_of_South_Africa"}, {"title": "Nobel Peace Prize", "link": "https://wikipedia.org/wiki/Nobel_Peace_Prize"}]}, {"year": "1937", "text": "<PERSON><PERSON>, German cyclist and sportscaster (d. 2016)", "html": "1937 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, German cyclist and sportscaster (d. 2016)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, German cyclist and sportscaster (d. 2016)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/R<PERSON>_<PERSON>ig"}]}, {"year": "1937", "text": "<PERSON>, American race car driver (d. 1975)", "html": "1937 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American race car driver (d. 1975)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American race car driver (d. 1975)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1938", "text": "<PERSON>, American actor and screenwriter", "html": "1938 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor and screenwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor and screenwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1938", "text": "<PERSON><PERSON>, Indian actor and producer (d. 2017)", "html": "1938 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Indian actor and producer (d. 2017)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Indian actor and producer (d. 2017)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1938", "text": "<PERSON>, English singer-songwriter and actor (d. 2019)", "html": "1938 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English singer-songwriter and actor (d. 2019)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English singer-songwriter and actor (d. 2019)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1938", "text": "<PERSON><PERSON>, Finnish race car driver (d. 2017)", "html": "1938 - <a href=\"https://wikipedia.org/wiki/Timo_M%C3%A4kinen\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Finnish race car driver (d. 2017)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Timo_M%C3%A4kinen\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Finnish race car driver (d. 2017)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Timo_M%C3%A4kinen"}]}, {"year": "1938", "text": "<PERSON><PERSON>, Japanese actress (d. 2006)", "html": "1938 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Japanese actress (d. 2006)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Japanese actress (d. 2006)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>ga"}]}, {"year": "1939", "text": "<PERSON>, English footballer and manager", "html": "1939 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English footballer and manager", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English footballer and manager", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1939", "text": "<PERSON><PERSON><PERSON>, French violinist and conductor", "html": "1939 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, French violinist and conductor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, French violinist and conductor", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>"}]}, {"year": "1941", "text": "<PERSON>, American singer-songwriter (d. 2006)", "html": "1941 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter (d. 2006)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter (d. 2006)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1942", "text": "<PERSON>, American filmmaker and playwright (d. 1988)", "html": "1942 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American filmmaker and playwright (d. 1988)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American filmmaker and playwright (d. 1988)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1942", "text": "<PERSON>, American basketball player and coach", "html": "1942 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(basketball)\" title=\"<PERSON> (basketball)\"><PERSON></a>, American basketball player and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(basketball)\" title=\"<PERSON> (basketball)\"><PERSON></a>, American basketball player and coach", "links": [{"title": "<PERSON> (basketball)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(basketball)"}]}, {"year": "1943", "text": "<PERSON>, American singer-songwriter (d. 2006)", "html": "1943 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter (d. 2006)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter (d. 2006)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1944", "text": "<PERSON><PERSON>, Israeli general and politician, 22nd Transportation Minister of Israel (d. 2012)", "html": "1944 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>-<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Israeli general and politician, 22nd <a href=\"https://wikipedia.org/wiki/Ministry_of_Transport_and_Road_Safety\" title=\"Ministry of Transport and Road Safety\">Transportation Minister of Israel</a> (d. 2012)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Israeli general and politician, 22nd <a href=\"https://wikipedia.org/wiki/Ministry_of_Transport_and_Road_Safety\" title=\"Ministry of Transport and Road Safety\">Transportation Minister of Israel</a> (d. 2012)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON><PERSON>-<PERSON>"}, {"title": "Ministry of Transport and Road Safety", "link": "https://wikipedia.org/wiki/Ministry_of_Transport_and_Road_Safety"}]}, {"year": "1944", "text": "<PERSON>, American football player and actor (d. 2021)", "html": "1944 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player and actor (d. 2021)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player and actor (d. 2021)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1944", "text": "<PERSON>, Australian publisher and businessman, founded Dick Smith Electronics and Australian Geographic", "html": "1944 - <a href=\"https://wikipedia.org/wiki/<PERSON>_(entrepreneur)\" class=\"mw-redirect\" title=\"<PERSON> (entrepreneur)\"><PERSON></a>, Australian publisher and businessman, founded <a href=\"https://wikipedia.org/wiki/<PERSON>(retailer)\" title=\"<PERSON> (retailer)\"><PERSON> Electronics</a> and <i><a href=\"https://wikipedia.org/wiki/Australian_Geographic\" title=\"Australian Geographic\">Australian Geographic</a></i>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>(entrepreneur)\" class=\"mw-redirect\" title=\"<PERSON> (entrepreneur)\"><PERSON></a>, Australian publisher and businessman, founded <a href=\"https://wikipedia.org/wiki/<PERSON>(retailer)\" title=\"<PERSON> (retailer)\"><PERSON> Electronics</a> and <i><a href=\"https://wikipedia.org/wiki/Australian_Geographic\" title=\"Australian Geographic\">Australian Geographic</a></i>", "links": [{"title": "<PERSON> (entrepreneur)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(entrepreneur)"}, {"title": "<PERSON> (retailer)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(retailer)"}, {"title": "Australian Geographic", "link": "https://wikipedia.org/wiki/Australian_Geographic"}]}, {"year": "1945", "text": "<PERSON><PERSON><PERSON>, Japanese photographer (d. 2020)", "html": "1945 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese photographer (d. 2020)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese photographer (d. 2020)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_Ki<PERSON>"}]}, {"year": "1945", "text": "<PERSON>, American journalist and radio host", "html": "1945 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American journalist and radio host", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American journalist and radio host", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1945", "text": "<PERSON>, American actress (d. 2012)", "html": "1945 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress (d. 2012)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress (d. 2012)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1945", "text": "<PERSON>, Scottish singer-songwriter, pianist, and producer (d. 2009)", "html": "1945 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish singer-songwriter, pianist, and producer (d. 2009)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish singer-songwriter, pianist, and producer (d. 2009)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1946", "text": "<PERSON>, French race car driver", "html": "1946 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A8re\" title=\"<PERSON>\"><PERSON></a>, French race car driver", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A8re\" title=\"<PERSON>\"><PERSON></a>, French race car driver", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A8re"}]}, {"year": "1947", "text": "<PERSON>, English actor and playwright", "html": "1947 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actor and playwright", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actor and playwright", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1947", "text": "<PERSON>, French actor, director, and screenwriter", "html": "1947 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French actor, director, and screenwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French actor, director, and screenwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Patrick_<PERSON>"}]}, {"year": "1947", "text": "<PERSON>, English cricketer, journalist, and sportscaster", "html": "1947 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>(cricketer)\" title=\"<PERSON> (cricketer)\"><PERSON></a>, English cricketer, journalist, and sportscaster", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>(cricketer)\" title=\"<PERSON> (cricketer)\"><PERSON></a>, English cricketer, journalist, and sportscaster", "links": [{"title": "<PERSON> (cricketer)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>(cricketer)"}]}, {"year": "1947", "text": "<PERSON><PERSON> <PERSON><PERSON>, English rock drummer (d. 1990)", "html": "1947 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> <PERSON><PERSON>\"><PERSON><PERSON> <PERSON><PERSON></a>, English rock drummer (d. 1990)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> <PERSON><PERSON>\"><PERSON><PERSON> <PERSON><PERSON></a>, English rock drummer (d. 1990)", "links": [{"title": "<PERSON><PERSON> <PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "1948", "text": "<PERSON>, Canadian ice hockey player and coach", "html": "1948 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player and coach", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1948", "text": "<PERSON>, Welsh footballer", "html": "1948 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Welsh footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Welsh footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1948", "text": "<PERSON><PERSON><PERSON>, Indian cricketer (d. 2005)", "html": "1948 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Indian cricketer (d. 2005)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Indian cricketer (d. 2005)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1949", "text": "<PERSON><PERSON>, Norwegian singer and politician, Norwegian Minister of Culture", "html": "1949 - <a href=\"https://wikipedia.org/wiki/%C3%85se_Kleveland\" title=\"<PERSON><PERSON> Klevela<PERSON>\"><PERSON><PERSON></a>, Norwegian singer and politician, <a href=\"https://wikipedia.org/wiki/Minister_of_Culture_(Norway)\" class=\"mw-redirect\" title=\"Minister of Culture (Norway)\">Norwegian Minister of Culture</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/%C3%85se_Kleveland\" title=\"<PERSON><PERSON> K<PERSON>ela<PERSON>\"><PERSON><PERSON></a>, Norwegian singer and politician, <a href=\"https://wikipedia.org/wiki/Minister_of_Culture_(Norway)\" class=\"mw-redirect\" title=\"Minister of Culture (Norway)\">Norwegian Minister of Culture</a>", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/%C3%85se_Kleveland"}, {"title": "Minister of Culture (Norway)", "link": "https://wikipedia.org/wiki/Minister_of_Culture_(Norway)"}]}, {"year": "1950", "text": "<PERSON>, American conductor and educator", "html": "1950 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American conductor and educator", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American conductor and educator", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1950", "text": "<PERSON>, American actor", "html": "1950 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1950", "text": "<PERSON>, English geneticist and academic", "html": "1950 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English geneticist and academic", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English geneticist and academic", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1950", "text": "<PERSON>, Australian race car driver", "html": "1950 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian race car driver", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian race car driver", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1951", "text": "<PERSON>, English actor", "html": "1951 - <a href=\"https://wikipedia.org/wiki/<PERSON>_(actor)\" title=\"<PERSON> (actor)\"><PERSON></a>, English actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>(actor)\" title=\"<PERSON> (actor)\"><PERSON></a>, English actor", "links": [{"title": "<PERSON> (actor)", "link": "https://wikipedia.org/wiki/<PERSON>_(actor)"}]}, {"year": "1951", "text": "<PERSON>, American businessman and philanthropist, co-founded Ben and Jerry's", "html": "1951 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>(businessman)\" title=\"<PERSON> (businessman)\"><PERSON></a>, American businessman and philanthropist, co-founded <a href=\"https://wikipedia.org/wiki/<PERSON>_and_<PERSON>%27s\" class=\"mw-redirect\" title=\"<PERSON> and <PERSON>'s\"><PERSON> and <PERSON>'s</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>(businessman)\" title=\"<PERSON> (businessman)\"><PERSON></a>, American businessman and philanthropist, co-founded <a href=\"https://wikipedia.org/wiki/<PERSON>_and_<PERSON>%27s\" class=\"mw-redirect\" title=\"<PERSON> and <PERSON>'s\"><PERSON> and <PERSON>'s</a>", "links": [{"title": "<PERSON> (businessman)", "link": "https://wikipedia.org/wiki/<PERSON>(businessman)"}, {"title": "<PERSON> and <PERSON>'s", "link": "https://wikipedia.org/wiki/<PERSON>_and_<PERSON>%27s"}]}, {"year": "1951", "text": "<PERSON>, American guitarist and composer", "html": "1951 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American guitarist and composer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American guitarist and composer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1951", "text": "<PERSON>, American lawyer, author, and judge", "html": "1951 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer, author, and judge", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer, author, and judge", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1952", "text": "<PERSON>, American journalist and actor", "html": "1952 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American journalist and actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American journalist and actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1952", "text": "<PERSON>, Irish jockey and trainer (d. 2015)", "html": "1952 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Irish jockey and trainer (d. 2015)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Irish jockey and trainer (d. 2015)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>y"}]}, {"year": "1952", "text": "<PERSON>, Irish singer-songwriter and guitarist (d. 2019)", "html": "1952 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A9\" title=\"<PERSON>\"><PERSON></a>, Irish singer-songwriter and guitarist (d. 2019)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A9\" title=\"<PERSON>\"><PERSON></a>, Irish singer-songwriter and guitarist (d. 2019)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Bernie_Torm%C3%A9"}]}, {"year": "1952", "text": "<PERSON>, American football player (d. 2002)", "html": "1952 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player (d. 2002)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player (d. 2002)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1953", "text": "<PERSON>, Austrian-American poet and translator (d. 2015)", "html": "1953 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Austrian-American poet and translator (d. 2015)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Austrian-American poet and translator (d. 2015)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1953", "text": "<PERSON><PERSON><PERSON>, Japanese composer", "html": "1953 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese composer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese composer", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1955", "text": "<PERSON>, American lawyer and politician, 45th Mayor of St. Louis", "html": "1955 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, American lawyer and politician, 45th <a href=\"https://wikipedia.org/wiki/Mayor_of_St._Louis\" title=\"Mayor of St. Louis\">Mayor of St. Louis</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, American lawyer and politician, 45th <a href=\"https://wikipedia.org/wiki/Mayor_of_St._Louis\" title=\"Mayor of St. Louis\">Mayor of St. Louis</a>", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "Mayor of St. Louis", "link": "https://wikipedia.org/wiki/Mayor_of_St._Louis"}]}, {"year": "1955", "text": "<PERSON>, English journalist and game show host", "html": "1955 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English journalist and game show host", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English journalist and game show host", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1956", "text": "<PERSON>, Canadian wrestler", "html": "1956 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian wrestler", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian wrestler", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1956", "text": "<PERSON>, American madam (d. 2008)", "html": "1956 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American madam (d. 2008)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American madam (d. 2008)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1956", "text": "<PERSON><PERSON><PERSON>, Swedish skier", "html": "1956 - <a href=\"https://wikipedia.org/wiki/Ingemar_Stenmark\" title=\"Ingemar Stenmark\">Ingemar Stenmark</a>, Swedish skier", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Ingemar_Stenmark\" title=\"Ingemar Stenmark\">Ingemar Stenmark</a>, Swedish skier", "links": [{"title": "Ingemar Stenmark", "link": "https://wikipedia.org/wiki/Ingemar_Stenmark"}]}, {"year": "1957", "text": "<PERSON><PERSON>, Swedish physicist and astronaut", "html": "1957 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Swedish physicist and astronaut", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Swedish physicist and astronaut", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1958", "text": "<PERSON>, Sri Lankan journalist and author (d. 1990)", "html": "1958 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Sri Lankan journalist and author (d. 1990)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Sri Lankan journalist and author (d. 1990)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1959", "text": "<PERSON>, French director, producer, and screenwriter, founded EuropaCorp", "html": "1959 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French director, producer, and screenwriter, founded <a href=\"https://wikipedia.org/wiki/EuropaCorp\" title=\"EuropaCorp\">EuropaCorp</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French director, producer, and screenwriter, founded <a href=\"https://wikipedia.org/wiki/EuropaCorp\" title=\"EuropaCorp\">EuropaCorp</a>", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "EuropaCorp", "link": "https://wikipedia.org/wiki/EuropaCorp"}]}, {"year": "1959", "text": "<PERSON>, American singer-songwriter and actress (d. 2022)", "html": "1959 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and actress (d. 2022)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Cara\"><PERSON></a>, American singer-songwriter and actress (d. 2022)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1960", "text": "<PERSON>, American actor (d. 2004)", "html": "1960 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor (d. 2004)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor (d. 2004)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1960", "text": "<PERSON>, Canadian ice hockey player and coach", "html": "1960 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player and coach", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1960", "text": "<PERSON>, Cypriot-English chess player", "html": "1960 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Cypriot-English chess player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Cypriot-English chess player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1961", "text": "<PERSON>, American singer-songwriter and guitarist (d. 2017)", "html": "1961 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and guitarist (d. 2017)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and guitarist (d. 2017)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1962", "text": "<PERSON>, American actor, producer, screenwriter, musician and martial artist", "html": "1962 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor, producer, screenwriter, musician and martial artist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor, producer, screenwriter, musician and martial artist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1962", "text": "<PERSON>, American singer-songwriter, guitarist, and actor", "html": "1962 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter, guitarist, and actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter, guitarist, and actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1962", "text": "<PERSON>, American television personality", "html": "1962 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American television personality", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American television personality", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1962", "text": "<PERSON><PERSON><PERSON>, Japanese actor and director", "html": "1962 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese actor and director", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese actor and director", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1962", "text": "<PERSON><PERSON>, German race car driver and engineer", "html": "1962 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>dler\"><PERSON><PERSON></a>, German race car driver and engineer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>dler\"><PERSON><PERSON></a>, German race car driver and engineer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>dler"}]}, {"year": "1963", "text": "<PERSON>, American guitarist (d. 2021)", "html": "1963 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American guitarist (d. 2021)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American guitarist (d. 2021)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1963", "text": "<PERSON>, American model, actress, and singer", "html": "1963 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, American model, actress, and singer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, American model, actress, and singer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1964", "text": "<PERSON>, American speed skater", "html": "1964 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American speed skater", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American speed skater", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1964", "text": "<PERSON>, Italian race car driver", "html": "1964 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian race car driver", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian race car driver", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1964", "text": "<PERSON>, British politician", "html": "1964 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>\" title=\"<PERSON>\"><PERSON></a>, British politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>\" title=\"<PERSON>\"><PERSON></a>, British politician", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>"}]}, {"year": "1964", "text": "<PERSON>, Mozambican film director", "html": "1964 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Mozambican film director", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Mozambican film director", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1964", "text": "<PERSON>, English saxophonist and clarinet player", "html": "1964 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Pine\"><PERSON></a>, English saxophonist and clarinet player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Courtney Pine\"><PERSON></a>, English saxophonist and clarinet player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1965", "text": "<PERSON>, English-Canadian actor", "html": "1965 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English-Canadian actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English-Canadian actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1966", "text": "<PERSON>, American singer-songwriter and guitarist", "html": "1966 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and guitarist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and guitarist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1966", "text": "<PERSON>, English businessman", "html": "1966 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(entrepreneur)\" title=\"<PERSON> (entrepreneur)\"><PERSON></a>, English businessman", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(entrepreneur)\" title=\"<PERSON> (entrepreneur)\"><PERSON></a>, English businessman", "links": [{"title": "<PERSON> (entrepreneur)", "link": "https://wikipedia.org/wiki/<PERSON>_(entrepreneur)"}]}, {"year": "1966", "text": "<PERSON>, Canadian golfer", "html": "1966 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian golfer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian golfer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1967", "text": "<PERSON><PERSON>, English singer-songwriter and guitarist", "html": "1967 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, English singer-songwriter and guitarist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, English singer-songwriter and guitarist", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1968", "text": "<PERSON>, Mexican footballer and manager", "html": "1968 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Mexican footballer and manager", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Mexican footballer and manager", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1968", "text": "<PERSON><PERSON><PERSON>, Georgian footballer and manager", "html": "1968 - <a href=\"https://wikipedia.org/wiki/Temur_Ketsbaia\" title=\"Temur Ketsbaia\"><PERSON><PERSON><PERSON></a>, Georgian footballer and manager", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Temur_Ketsbaia\" title=\"Temur Ketsbaia\"><PERSON><PERSON><PERSON></a>, Georgian footballer and manager", "links": [{"title": "Temur <PERSON>", "link": "https://wikipedia.org/wiki/Temur_Ketsbaia"}]}, {"year": "1968", "text": "<PERSON>, English businessman and politician", "html": "1968 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English businessman and politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English businessman and politician", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1969", "text": "<PERSON>, American actor", "html": "1969 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1969", "text": "<PERSON>, English accordion player and composer", "html": "1969 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English accordion player and composer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English accordion player and composer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1969", "text": "<PERSON><PERSON><PERSON>, Ukrainian chess player", "html": "1969 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Ukrainian chess player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Ukrainian chess player", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1969", "text": "<PERSON>, English cricketer", "html": "1969 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English cricketer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English cricketer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1970", "text": "<PERSON>, Australian politician", "html": "1970 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian politician", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1970", "text": "<PERSON> <PERSON><PERSON><PERSON>, American rapper, producer, and actress", "html": "1970 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON><PERSON><PERSON>\" title=\"<PERSON> Lati<PERSON><PERSON>\"><PERSON> <PERSON><PERSON><PERSON><PERSON></a>, American rapper, producer, and actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON><PERSON>\" title=\"<PERSON> Lati<PERSON><PERSON>\"><PERSON> <PERSON><PERSON><PERSON><PERSON></a>, American rapper, producer, and actress", "links": [{"title": "Queen <PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON><PERSON><PERSON>"}]}, {"year": "1971", "text": "<PERSON>, Australian tennis player", "html": "1971 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(tennis)\" title=\"<PERSON> (tennis)\"><PERSON></a>, Australian tennis player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(tennis)\" title=\"<PERSON> (tennis)\"><PERSON></a>, Australian tennis player", "links": [{"title": "<PERSON> (tennis)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(tennis)"}]}, {"year": "1971", "text": "<PERSON>, American wrestler (d. 2008)", "html": "1971 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(wrestler)\" title=\"<PERSON> (wrestler)\"><PERSON></a>, American wrestler (d. 2008)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>(wrestler)\" title=\"<PERSON> (wrestler)\"><PERSON></a>, American wrestler (d. 2008)", "links": [{"title": "<PERSON> (wrestler)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>(wrestler)"}]}, {"year": "1971", "text": "<PERSON><PERSON>, South African-American tennis player, coach, and sportscaster", "html": "1971 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, South African-American tennis player, coach, and sportscaster", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, South African-American tennis player, coach, and sportscaster", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>t"}]}, {"year": "1971", "text": "<PERSON>, English economist and politician", "html": "1971 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English economist and politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Kitty Ussher\"><PERSON></a>, English economist and politician", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>sher"}]}, {"year": "1972", "text": "<PERSON>, American comedian, actor, director, and producer", "html": "1972 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American comedian, actor, director, and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American comedian, actor, director, and producer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1972", "text": "<PERSON><PERSON><PERSON>, American lawyer and politician", "html": "1972 - <a href=\"https://wikipedia.org/wiki/<PERSON>in<PERSON>_<PERSON>riebus\" title=\"<PERSON>in<PERSON> Priebus\"><PERSON><PERSON><PERSON></a>, American lawyer and politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>bus\" title=\"<PERSON>in<PERSON> Priebus\"><PERSON><PERSON><PERSON></a>, American lawyer and politician", "links": [{"title": "<PERSON><PERSON>ce <PERSON>", "link": "https://wikipedia.org/wiki/Reince_Priebus"}]}, {"year": "1973", "text": "<PERSON><PERSON>, American voice actress and screenwriter", "html": "1973 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American voice actress and screenwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American voice actress and screenwriter", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1974", "text": "<PERSON><PERSON>, French basketball player, coach, and sportscaster", "html": "1974 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"Lau<PERSON>\"><PERSON><PERSON></a>, French basketball player, coach, and sportscaster", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, French basketball player, coach, and sportscaster", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1974", "text": "<PERSON>, English bass player, songwriter, and producer", "html": "1974 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English bass player, songwriter, and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English bass player, songwriter, and producer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1975", "text": "<PERSON>, American actress, singer, and dancer", "html": "1975 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress, singer, and dancer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Foster\"><PERSON></a>, American actress, singer, and dancer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1975", "text": "<PERSON>, American football player and sportscaster", "html": "1975 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player and sportscaster", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player and sportscaster", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1975", "text": "<PERSON><PERSON>, Finnish ice hockey player", "html": "1975 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Finnish ice hockey player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Finnish ice hockey player", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>n"}]}, {"year": "1975", "text": "<PERSON>, Lithuanian footballer", "html": "1975 - <a href=\"https://wikipedia.org/wiki/<PERSON>_%C5%BDvirg%C5%BE<PERSON><PERSON><PERSON>\" title=\"<PERSON>\"><PERSON></a>, Lithuanian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_%C5%BDvirg%C5%BE<PERSON><PERSON><PERSON>\" title=\"<PERSON>\"><PERSON></a>, Lithuanian footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Tomas_%C5%BDvirg%C5%BEdauskas"}]}, {"year": "1976", "text": "<PERSON><PERSON><PERSON>, Brazilian actress and producer", "html": "1976 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Brazilian actress and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Brazilian actress and producer", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1976", "text": "<PERSON><PERSON>, Japanese baseball player", "html": "1976 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Japanese baseball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Japanese baseball player", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1976", "text": "<PERSON>, American baseball player", "html": "1976 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Scott_Podsednik"}]}, {"year": "1976", "text": "<PERSON>, American wrestler, trainer, and author, founded Chikara wrestling promotion", "html": "1976 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American wrestler, trainer, and author, founded <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_(professional_wrestling)\" title=\"<PERSON><PERSON> (professional wrestling)\">Chikara wrestling promotion</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American wrestler, trainer, and author, founded <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_(professional_wrestling)\" title=\"<PERSON><PERSON> (professional wrestling)\">Chikara wrestling promotion</a>", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "<PERSON><PERSON> (professional wrestling)", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_(professional_wrestling)"}]}, {"year": "1977", "text": "<PERSON><PERSON><PERSON>, Slovak ice hockey player", "html": "1977 - <a href=\"https://wikipedia.org/wiki/Zdeno_Ch%C3%A1ra\" title=\"Zdeno Chára\"><PERSON><PERSON><PERSON></a>, Slovak ice hockey player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Zdeno_Ch%C3%A1ra\" title=\"Zdeno Chára\"><PERSON><PERSON><PERSON></a>, Slovak ice hockey player", "links": [{"title": "Zdeno <PERSON>", "link": "https://wikipedia.org/wiki/Zdeno_Ch%C3%A1ra"}]}, {"year": "1977", "text": "<PERSON>, English footballer and sportscaster", "html": "1977 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(footballer,_born_1977)\" title=\"<PERSON> (footballer, born 1977)\"><PERSON></a>, English footballer and sportscaster", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(footballer,_born_1977)\" title=\"<PERSON> (footballer, born 1977)\"><PERSON></a>, English footballer and sportscaster", "links": [{"title": "<PERSON> (footballer, born 1977)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(footballer,_born_1977)"}]}, {"year": "1977", "text": "<PERSON>, Dominican-American baseball player", "html": "1977 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dominican-American baseball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dominican-American baseball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1977", "text": "<PERSON>, French footballer and manager", "html": "1977 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French footballer and manager", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French footballer and manager", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1977", "text": "<PERSON><PERSON><PERSON>, American baseball player and coach", "html": "1977 - <a href=\"https://wikipedia.org/wiki/Terr<PERSON>_Sledge\" title=\"Terrmel Sledge\"><PERSON><PERSON><PERSON>ledge</a>, American baseball player and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Terr<PERSON>_Sledge\" title=\"Terrmel Sledge\"><PERSON><PERSON><PERSON>ledge</a>, American baseball player and coach", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Te<PERSON><PERSON>_<PERSON>ledge"}]}, {"year": "1978", "text": "<PERSON>, Czech ice hockey player", "html": "1978 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Czech ice hockey player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Czech ice hockey player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1978", "text": "<PERSON><PERSON><PERSON><PERSON>, Brazilian footballer and manager (d. 2014)", "html": "1978 - <a href=\"https://wikipedia.org/wiki/Fernand%C3%A3<PERSON>_(footballer,_born_1978)\" title=\"<PERSON><PERSON><PERSON><PERSON> (footballer, born 1978)\"><PERSON><PERSON><PERSON><PERSON></a>, Brazilian footballer and manager (d. 2014)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Fernand%C3%A3<PERSON>_(footballer,_born_1978)\" title=\"<PERSON><PERSON><PERSON><PERSON> (footballer, born 1978)\"><PERSON><PERSON><PERSON><PERSON></a>, Brazilian footballer and manager (d. 2014)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON> (footballer, born 1978)", "link": "https://wikipedia.org/wiki/Fernand%C3%A3o_(footballer,_born_1978)"}]}, {"year": "1978", "text": "<PERSON>, Australian swimmer", "html": "1978 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian swimmer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian swimmer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1978", "text": "<PERSON>, Chinese actor", "html": "1978 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Chinese actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Chinese actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1978", "text": "<PERSON>, American basketball player, coach, and sportscaster", "html": "1978 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player, coach, and sportscaster", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player, coach, and sportscaster", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1978", "text": "<PERSON>, Swedish footballer, coach, and manager", "html": "1978 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swedish footballer, coach, and manager", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swedish footballer, coach, and manager", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1979", "text": "<PERSON>, American singer-songwriter, guitarist, and television personality", "html": "1979 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter, guitarist, and television personality", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter, guitarist, and television personality", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1980", "text": "<PERSON><PERSON><PERSON><PERSON>, French footballer", "html": "1980 - <a href=\"https://wikipedia.org/wiki/S%C3%A<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, French footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/S%C3%<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, French footballer", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/S%C3%<PERSON><PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1980", "text": "<PERSON>, English actress", "html": "1980 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actress", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1980", "text": "<PERSON><PERSON>, Russian ice hockey player", "html": "1980 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Russian ice hockey player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Russian ice hockey player", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>v<PERSON>"}]}, {"year": "1980", "text": "<PERSON>, Russian figure skater", "html": "1980 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian figure skater", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian figure skater", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1981", "text": "<PERSON><PERSON>, Norwegian biathlete", "html": "1981 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Norwegian biathlete", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Norwegian biathlete", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1981", "text": "<PERSON>, Swiss cyclist", "html": "1981 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swiss cyclist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swiss cyclist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1981", "text": "<PERSON>, French sprinter", "html": "1981 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French sprinter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French sprinter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1981", "text": "<PERSON>, South Korean singer and actress", "html": "1981 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>-ra\" title=\"<PERSON>-ra\"><PERSON></a>, South Korean singer and actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>-<PERSON>a\" title=\"<PERSON>ra\"><PERSON></a>, South Korean singer and actress", "links": [{"title": "<PERSON>a", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>-ra"}]}, {"year": "1981", "text": "<PERSON><PERSON><PERSON>, American basketball player", "html": "1981 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American basketball player", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1981", "text": "<PERSON>, German footballer", "html": "1981 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1981", "text": "<PERSON>, American soccer player", "html": "1981 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American soccer player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American soccer player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1981", "text": "<PERSON><PERSON><PERSON>, Croatian tennis player", "html": "1981 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Croatian tennis player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Croatian tennis player", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1982", "text": "<PERSON>, American baseball player", "html": "1982 - <a href=\"https://wikipedia.org/wiki/Chad_Cordero\" title=\"<PERSON>\"><PERSON></a>, American baseball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Chad_Cordero\" title=\"<PERSON>\"><PERSON></a>, American baseball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Chad_Cordero"}]}, {"year": "1982", "text": "<PERSON><PERSON>, German race car driver", "html": "1982 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, German race car driver", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, German race car driver", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>lock"}]}, {"year": "1982", "text": "<PERSON>, Canadian ice hockey player", "html": "1982 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1982", "text": "<PERSON><PERSON><PERSON>, Angolan footballer", "html": "1982 - <a href=\"https://wikipedia.org/wiki/Mantorras\" title=\"Mantorras\"><PERSON><PERSON><PERSON></a>, Angolan footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Mantorras\" title=\"Mantorras\"><PERSON><PERSON><PERSON></a>, Angolan footballer", "links": [{"title": "Mantorras", "link": "https://wikipedia.org/wiki/Mantorras"}]}, {"year": "1982", "text": "<PERSON>, American actor, director, producer, and screenwriter", "html": "1982 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>lly\"><PERSON></a>, American actor, director, producer, and screenwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Pally\"><PERSON></a>, American actor, director, producer, and screenwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1983", "text": "<PERSON>, American wrestler", "html": "1983 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON> III\"><PERSON></a>, American wrestler", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American wrestler", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1983", "text": "<PERSON><PERSON><PERSON><PERSON>, French tennis player", "html": "1983 - <a href=\"https://wikipedia.org/wiki/St%C3%A<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, French tennis player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/St%C3%A<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, French tennis player", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/St%C3%A<PERSON><PERSON><PERSON>_<PERSON>-<PERSON>"}]}, {"year": "1983", "text": "<PERSON>, American baseball player", "html": "1983 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1983", "text": "<PERSON><PERSON>, Polish footballer", "html": "1983 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Polish footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Polish footballer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1984", "text": "<PERSON>, Italian footballer", "html": "1984 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1984", "text": "<PERSON><PERSON><PERSON>, American tennis player", "html": "1984 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American tennis player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American tennis player", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1984", "text": "<PERSON><PERSON><PERSON>, American singer and actress", "html": "1984 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American singer and actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American singer and actress", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1985", "text": "<PERSON>, Brazilian race car driver", "html": "1985 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Beatriz\"><PERSON></a>, Brazilian race car driver", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_Beat<PERSON>\" title=\"<PERSON> Beatriz\"><PERSON></a>, Brazilian race car driver", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>riz"}]}, {"year": "1985", "text": "<PERSON><PERSON>, English actor", "html": "1985 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, English actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, English actor", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1985", "text": "<PERSON>, English singer", "html": "1985 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English singer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English singer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1985", "text": "<PERSON>, Australian footballer", "html": "1985 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1986", "text": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, Algerian footballer", "html": "1986 - <a href=\"https://wikipedia.org/wiki/Abdennour_Ch%C3%A9rif_<PERSON>-<PERSON>ni\" title=\"Abdennour Chéri<PERSON>\">Abd<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON></a>, Algerian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Abdennour_Ch%C3%A9rif_<PERSON>-Ouazzani\" title=\"Abdennour Chérif <PERSON>\">Abd<PERSON><PERSON>ur Ché<PERSON><PERSON></a>, Algerian footballer", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Abdennour_Ch%C3%A9rif_<PERSON>"}]}, {"year": "1986", "text": "<PERSON><PERSON><PERSON>, Swedish singer-songwriter", "html": "1986 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Swedish singer-songwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Swedish singer-songwriter", "links": [{"title": "Lykke Li", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>ke_Li"}]}, {"year": "1986", "text": "<PERSON>, American ice hockey player", "html": "1986 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American ice hockey player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American ice hockey player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1986", "text": "<PERSON>, American football player and sportscaster", "html": "1986 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player and sportscaster", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player and sportscaster", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1987", "text": "<PERSON><PERSON> <PERSON><PERSON>, American basketball player", "html": "1987 - <a href=\"https://wikipedia.org/wiki/C._<PERSON><PERSON>_<PERSON>\" title=\"C. J. Miles\"><PERSON><PERSON> <PERSON><PERSON></a>, American basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/C._<PERSON><PERSON>_<PERSON>\" title=\"C. J. Miles\"><PERSON><PERSON> <PERSON><PERSON></a>, American basketball player", "links": [{"title": "<PERSON><PERSON> <PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "1987", "text": "<PERSON>, American swimmer", "html": "1987 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American swimmer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American swimmer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>i"}]}, {"year": "1989", "text": "<PERSON>, Canadian ice hockey player", "html": "1989 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1989", "text": "<PERSON>, Italian footballer", "html": "1989 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1989", "text": "<PERSON>, English-American actress", "html": "1989 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English-American actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English-American actress", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1989", "text": "<PERSON><PERSON><PERSON><PERSON><PERSON>, Indian cricketer", "html": "1989 - <a href=\"https://wikipedia.org/wiki/Shree<PERSON><PERSON>_Goswami\" title=\"<PERSON><PERSON>ee<PERSON><PERSON> Goswami\"><PERSON><PERSON><PERSON><PERSON><PERSON></a>, Indian cricketer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Shree<PERSON><PERSON>_<PERSON>swami\" title=\"<PERSON><PERSON>ee<PERSON><PERSON> Goswami\"><PERSON><PERSON><PERSON><PERSON><PERSON></a>, Indian cricketer", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Shree<PERSON><PERSON>_<PERSON><PERSON>wami"}]}, {"year": "1989", "text": "<PERSON><PERSON>, Japanese singer-songwriter", "html": "1989 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Japanese singer-songwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Japanese singer-songwriter", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>o"}]}, {"year": "1989", "text": "<PERSON>, Canadian guitarist and producer", "html": "1989 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian guitarist and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian guitarist and producer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1989", "text": "<PERSON>, Chinese model", "html": "1989 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Ming Xi\"><PERSON></a>, Chinese model", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Ming_Xi\" title=\"Ming Xi\"><PERSON></a>, Chinese model", "links": [{"title": "Ming <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1991", "text": "<PERSON>, American football player", "html": "1991 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1991", "text": "<PERSON><PERSON>, Dominican baseball player", "html": "1991 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>arc%C3%ADa\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Dominican baseball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>arc%C3%ADa\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Dominican baseball player", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Leury_Garc%C3%ADa"}]}, {"year": "1991", "text": "<PERSON>, American basketball player", "html": "1991 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(basketball)\" title=\"<PERSON> (basketball)\"><PERSON></a>, American basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Solomon_Hill_(basketball)\" title=\"<PERSON> Hill (basketball)\"><PERSON></a>, American basketball player", "links": [{"title": "<PERSON> (basketball)", "link": "https://wikipedia.org/wiki/Solomon_Hill_(basketball)"}]}, {"year": "1991", "text": "<PERSON>, American singer-songwriter and guitarist", "html": "1991 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and guitarist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and guitarist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1991", "text": "<PERSON><PERSON> <PERSON><PERSON>, American baseball player", "html": "1991 - <a href=\"https://wikipedia.org/wiki/J._<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> <PERSON><PERSON>\"><PERSON><PERSON> <PERSON><PERSON></a>, American baseball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/J._<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> <PERSON><PERSON>\"><PERSON><PERSON> <PERSON><PERSON></a>, American baseball player", "links": [{"title": "<PERSON><PERSON> <PERSON><PERSON>", "link": "https://wikipedia.org/wiki/J._T._<PERSON>uto"}]}, {"year": "1992", "text": "<PERSON>, American football player", "html": "1992 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(American_football)\" title=\"<PERSON> (American football)\"><PERSON></a>, American football player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(American_football)\" title=\"<PERSON> (American football)\"><PERSON></a>, American football player", "links": [{"title": "<PERSON> (American football)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(American_football)"}]}, {"year": "1992", "text": "<PERSON>, American baseball player", "html": "1992 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1992", "text": "<PERSON>, American race car driver", "html": "1992 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American race car driver", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American race car driver", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1992", "text": "<PERSON><PERSON><PERSON>, Japanese singer, actor, and model", "html": "1992 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese singer, actor, and model", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese singer, actor, and model", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON><PERSON>"}]}, {"year": "1993", "text": "<PERSON>, American wrestler", "html": "1993 - <a href=\"https://wikipedia.org/wiki/Solo_Sikoa\" title=\"Solo Sikoa\"><PERSON></a>, American wrestler", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Solo_Sikoa\" title=\"Solo Sikoa\"><PERSON></a>, American wrestler", "links": [{"title": "Solo Sikoa", "link": "https://wikipedia.org/wiki/Solo_Sikoa"}]}, {"year": "1994", "text": "<PERSON>, American basketball player", "html": "1994 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1994", "text": "<PERSON>, American football player", "html": "1994 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1995", "text": "<PERSON><PERSON>, Romanian tennis player", "html": "1995 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Romanian tennis player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Romanian tennis player", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>a"}]}, {"year": "1995", "text": "<PERSON>, American actress and dancer", "html": "1995 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress and dancer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress and dancer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1996", "text": "<PERSON><PERSON>, Haitian basketball player", "html": "1996 - <a href=\"https://wikipedia.org/wiki/Skal_Labissi%C3%A8re\" title=\"<PERSON><PERSON> Labissière\"><PERSON><PERSON></a>, Haitian basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Skal_Labissi%C3%A8re\" title=\"<PERSON><PERSON> Labissière\"><PERSON><PERSON></a>, Haitian basketball player", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Skal_Labissi%C3%A8re"}]}, {"year": "1997", "text": "<PERSON><PERSON>, American actress", "html": "1997 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_Bravo\" title=\"Ciara Bravo\"><PERSON><PERSON></a>, American actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_Bravo\" title=\"Ciara Bravo\"><PERSON><PERSON></a>, American actress", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1997", "text": "<PERSON><PERSON><PERSON>, New Zealand rugby union player", "html": "1997 - <a href=\"https://wikipedia.org/wiki/Riek<PERSON>_<PERSON><PERSON>\" title=\"<PERSON>iek<PERSON> Ioane\"><PERSON><PERSON><PERSON></a>, New Zealand rugby union player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Riek<PERSON>_<PERSON><PERSON>\" title=\"Riek<PERSON> Ioan<PERSON>\"><PERSON><PERSON><PERSON></a>, New Zealand rugby union player", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Riek<PERSON>_<PERSON><PERSON>e"}]}, {"year": "1997", "text": "<PERSON>, American football player", "html": "1997 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_Whitehead"}]}, {"year": "1997", "text": "<PERSON><PERSON><PERSON>, Croatian basketball player", "html": "1997 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Croatian basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Croatian basketball player", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1998", "text": "<PERSON>, Dominican baseball player", "html": "1998 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dominican baseball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dominican baseball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2002", "text": "<PERSON><PERSON><PERSON>, American football player", "html": "2002 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"B<PERSON><PERSON> Rice\"><PERSON><PERSON><PERSON></a>, American football player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"B<PERSON><PERSON> Rice\"><PERSON><PERSON><PERSON></a>, American football player", "links": [{"title": "Brenden Rice", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>den_Rice"}]}], "Deaths": [{"year": "978", "text": "<PERSON> the Martyr, English king (b. 962)", "html": "978 - <a href=\"https://wikipedia.org/wiki/<PERSON>_the_Martyr\" title=\"<PERSON> the Martyr\"><PERSON> the Martyr</a>, English king (b. 962)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_the_Martyr\" title=\"<PERSON> the Martyr\"><PERSON> the Martyr</a>, English king (b. 962)", "links": [{"title": "<PERSON> the Martyr", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1076", "text": "<PERSON><PERSON><PERSON><PERSON><PERSON> of Anjou, Duchess of Burgundy (b. 1018)", "html": "1076 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON><PERSON>_of_Anjou,_Duchess_of_Burgundy\" title=\"<PERSON><PERSON><PERSON><PERSON><PERSON> of Anjou, Duchess of Burgundy\"><PERSON><PERSON><PERSON><PERSON><PERSON> of Anjou, Duchess of Burgundy</a> (b. 1018)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON><PERSON>_of_Anjou,_Duchess_of_Burgundy\" title=\"<PERSON><PERSON><PERSON><PERSON><PERSON> of Anjou, Duchess of Burgundy\"><PERSON><PERSON><PERSON><PERSON><PERSON> of Anjou, Duchess of Burgundy</a> (b. 1018)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON><PERSON> of Anjou, Duchess of Burgundy", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON><PERSON>_of_Anjou,_Duchess_of_Burgundy"}]}, {"year": "1086", "text": "<PERSON><PERSON><PERSON> of Lucca, Italian bishop (b. 1036)", "html": "1086 - <a href=\"https://wikipedia.org/wiki/Ansel<PERSON>_of_Lucca\" title=\"<PERSON><PERSON><PERSON> of Lucca\"><PERSON><PERSON><PERSON> of Lucca</a>, Italian bishop (b. 1036)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>sel<PERSON>_of_Lucca\" title=\"<PERSON><PERSON><PERSON> of Lucca\"><PERSON><PERSON><PERSON> of Lucca</a>, Italian bishop (b. 1036)", "links": [{"title": "Anselm of Lucca", "link": "https://wikipedia.org/wiki/Anselm_of_Lucca"}]}, {"year": "1227", "text": "<PERSON> <PERSON><PERSON> (b. 1148)", "html": "1227 - <a href=\"https://wikipedia.org/wiki/<PERSON>_Honorius_III\" title=\"<PERSON> Honorius III\">Pope <PERSON><PERSON></a> (b. 1148)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_Honorius_III\" title=\"<PERSON> Honorius III\">Pope <PERSON><PERSON></a> (b. 1148)", "links": [{"title": "<PERSON> <PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "1272", "text": "<PERSON>, 7th Earl of Arundel (b. 1246)", "html": "1272 - <a href=\"https://wikipedia.org/wiki/<PERSON>,_7th_Earl_of_Arundel\" class=\"mw-redirect\" title=\"<PERSON>, 7th Earl of Arundel\"><PERSON>, 7th Earl of Arundel</a> (b. 1246)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>,_7th_Earl_of_Arundel\" class=\"mw-redirect\" title=\"<PERSON>, 7th Earl of Arundel\"><PERSON>, 7th Earl of Arundel</a> (b. 1246)", "links": [{"title": "<PERSON>, 7th Earl of Arundel", "link": "https://wikipedia.org/wiki/<PERSON>,_7th_Earl_of_Arundel"}]}, {"year": "1308", "text": "<PERSON> of Galicia", "html": "1308 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Galicia\" title=\"<PERSON> of Galicia\"><PERSON> of Galicia</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Galicia\" title=\"<PERSON> of Galicia\"><PERSON> of Galicia</a>", "links": [{"title": "<PERSON> of Galicia", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Galicia"}]}, {"year": "1314", "text": "<PERSON>, Frankish knight (b. 1244)", "html": "1314 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Frankish knight (b. 1244)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Frankish knight (b. 1244)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1314", "text": "<PERSON><PERSON>, Preceptor of Normandy for the Knights Templar", "html": "1314 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Preceptor of Normandy for the Knights Templar", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Preceptor of Normandy for the Knights Templar", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1321", "text": "<PERSON>, Hungarian oligarch (b. c. 1260/5)", "html": "1321 - <a href=\"https://wikipedia.org/wiki/Matthew_III_Cs%C3%A1k\" title=\"Matthew III Csák\"><PERSON></a>, Hungarian oligarch (b. c. 1260/5)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Matthew_III_Cs%C3%A1k\" title=\"Matthew III Csák\"><PERSON></a>, Hungarian oligarch (b. c. 1260/5)", "links": [{"title": "<PERSON>ák", "link": "https://wikipedia.org/wiki/Matthew_III_Cs%C3%A1k"}]}, {"year": "1582", "text": "<PERSON>, attempted assassin of <PERSON> Orange (b. 1562)", "html": "1582 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, attempted assassin of <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_Orange\" class=\"mw-redirect\" title=\"William I of Orange\"><PERSON> Orange</a> (b. 1562)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, attempted assassin of <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Orange\" class=\"mw-redirect\" title=\"William I of Orange\"><PERSON> Orange</a> (b. 1562)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "<PERSON> Orange", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_Orange"}]}, {"year": "1675", "text": "<PERSON>, 1st Earl of Donegall, Irish soldier (b. 1606)", "html": "1675 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_1st_Earl_of_Donegall\" title=\"<PERSON>, 1st Earl of Donegall\"><PERSON>, 1st Earl of Donegall</a>, Irish soldier (b. 1606)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_1st_Earl_of_Donegall\" title=\"<PERSON>, 1st Earl of Donegall\"><PERSON>, 1st Earl of Donegall</a>, Irish soldier (b. 1606)", "links": [{"title": "<PERSON>, 1st Earl of Donegall", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_1st_Earl_of_Donegall"}]}, {"year": "1689", "text": "<PERSON>, English soldier and politician (b. 1607)", "html": "1689 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English soldier and politician (b. 1607)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English soldier and politician (b. 1607)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1703", "text": "<PERSON>, Maltese sculptor and painter (b. 1645)", "html": "1703 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Maltese sculptor and painter (b. 1645)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Maltese sculptor and painter (b. 1645)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1745", "text": "<PERSON>, English politician, Prime Minister of the United Kingdom (b. 1676)", "html": "1745 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English politician, <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_the_United_Kingdom\" title=\"Prime Minister of the United Kingdom\">Prime Minister of the United Kingdom</a> (b. 1676)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English politician, <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_the_United_Kingdom\" title=\"Prime Minister of the United Kingdom\">Prime Minister of the United Kingdom</a> (b. 1676)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Prime Minister of the United Kingdom", "link": "https://wikipedia.org/wiki/Prime_Minister_of_the_United_Kingdom"}]}, {"year": "1768", "text": "<PERSON>, Irish novelist and clergyman (b. 1713)", "html": "1768 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Irish novelist and clergyman (b. 1713)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Irish novelist and clergyman (b. 1713)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1781", "text": "<PERSON>, French economist and politician, Controller-General of Finances (b. 1727)", "html": "1781 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French economist and politician, <a href=\"https://wikipedia.org/wiki/List_of_Finance_Ministers_of_France\" class=\"mw-redirect\" title=\"List of Finance Ministers of France\">Controller-General of Finances</a> (b. 1727)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French economist and politician, <a href=\"https://wikipedia.org/wiki/List_of_Finance_Ministers_of_France\" class=\"mw-redirect\" title=\"List of Finance Ministers of France\">Controller-General of Finances</a> (b. 1727)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "List of Finance Ministers of France", "link": "https://wikipedia.org/wiki/List_of_Finance_Ministers_of_France"}]}, {"year": "1793", "text": "<PERSON>, Prussian minister of education (b. 1731)", "html": "1793 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Prussian minister of education (b. 1731)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Prussian minister of education (b. 1731)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1823", "text": "<PERSON><PERSON><PERSON>, French cellist and composer (b. 1753)", "html": "1823 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_Br%C3%A9val\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, French cellist and composer (b. 1753)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_Br%C3%A9val\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, French cellist and composer (b. 1753)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_Br%C3%A9val"}]}, {"year": "1835", "text": "<PERSON>, Danish-Prussian politician and diplomat (b. 1769)", "html": "1835 - <a href=\"https://wikipedia.org/wiki/Christian_G%C3%<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Danish-Prussian politician and diplomat (b. 1769)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Christian_G%C3%<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Danish-Prussian politician and diplomat (b. 1769)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Christian_G%C3%<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1845", "text": "<PERSON>, American gardener and missionary (b. 1774)", "html": "1845 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American gardener and missionary (b. 1774)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American gardener and missionary (b. 1774)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>seed"}]}, {"year": "1871", "text": "<PERSON>, Indian-English mathematician and academic (b. 1806)", "html": "1871 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Indian-English mathematician and academic (b. 1806)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Indian-English mathematician and academic (b. 1806)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1898", "text": "<PERSON>, American author and activist (b. 1826)", "html": "1898 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American author and activist (b. 1826)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON><PERSON></a>, American author and activist (b. 1826)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1900", "text": "<PERSON><PERSON><PERSON><PERSON>, Danish botanist (b. 1835)", "html": "1900 - <a href=\"https://wikipedia.org/wiki/Hjalmar_Ki%C3%A6rsk<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Danish botanist (b. 1835)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Hjalmar_Ki%C3%A6rskou\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Danish botanist (b. 1835)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Hjalmar_Ki%C3%A6rskou"}]}, {"year": "1907", "text": "<PERSON><PERSON>, French chemist and politician, French Minister of Foreign Affairs (b. 1827)", "html": "1907 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, French chemist and politician, <a href=\"https://wikipedia.org/wiki/Ministry_of_Foreign_Affairs_and_International_Development_(France)\" class=\"mw-redirect\" title=\"Ministry of Foreign Affairs and International Development (France)\">French Minister of Foreign Affairs</a> (b. 1827)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, French chemist and politician, <a href=\"https://wikipedia.org/wiki/Ministry_of_Foreign_Affairs_and_International_Development_(France)\" class=\"mw-redirect\" title=\"Ministry of Foreign Affairs and International Development (France)\">French Minister of Foreign Affairs</a> (b. 1827)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}, {"title": "Ministry of Foreign Affairs and International Development (France)", "link": "https://wikipedia.org/wiki/Ministry_of_Foreign_Affairs_and_International_Development_(France)"}]}, {"year": "1913", "text": "<PERSON> of Greece (b. 1845)", "html": "1913 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Greece\" title=\"<PERSON> of Greece\"><PERSON> of Greece</a> (b. 1845)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Greece\" title=\"<PERSON> of Greece\"><PERSON> of Greece</a> (b. 1845)", "links": [{"title": "<PERSON> of Greece", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Greece"}]}, {"year": "1918", "text": "<PERSON>, American architect, designed the Plaza Hotel (b. 1847)", "html": "1918 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American architect, designed the <a href=\"https://wikipedia.org/wiki/Plaza_Hotel\" title=\"Plaza Hotel\">Plaza Hotel</a> (b. 1847)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American architect, designed the <a href=\"https://wikipedia.org/wiki/Plaza_Hotel\" title=\"Plaza Hotel\">Plaza Hotel</a> (b. 1847)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "Plaza Hotel", "link": "https://wikipedia.org/wiki/Plaza_Hotel"}]}, {"year": "1930", "text": "<PERSON>, American painter (b. 1863)", "html": "1930 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American painter (b. 1863)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American painter (b. 1863)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1936", "text": "<PERSON><PERSON><PERSON><PERSON>, Greek journalist, lawyer, and politician, 93rd Prime Minister of Greece (b. 1864)", "html": "1936 - <a href=\"https://wikipedia.org/wiki/Eleftherios_Venizelos\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Greek journalist, lawyer, and politician, 93rd <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Greece\" title=\"Prime Minister of Greece\">Prime Minister of Greece</a> (b. 1864)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Eleftherios_<PERSON>enizel<PERSON>\" title=\"<PERSON><PERSON>ther<PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Greek journalist, lawyer, and politician, 93rd <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Greece\" title=\"Prime Minister of Greece\">Prime Minister of Greece</a> (b. 1864)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Eleftherios_Venizelos"}, {"title": "Prime Minister of Greece", "link": "https://wikipedia.org/wiki/Prime_Minister_of_Greece"}]}, {"year": "1939", "text": "<PERSON>, English businessman, founded Lunn Poly (b. 1859)", "html": "1939 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, English businessman, founded <a href=\"https://wikipedia.org/wiki/Lunn_Poly\" title=\"Lunn Poly\"><PERSON>nn Poly</a> (b. 1859)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, English businessman, founded <a href=\"https://wikipedia.org/wiki/Lunn_Poly\" title=\"Lunn Poly\">Lunn Poly</a> (b. 1859)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "<PERSON>nn Poly", "link": "https://wikipedia.org/wiki/Lunn_Poly"}]}, {"year": "1941", "text": "<PERSON>, French cyclist (b. 1884)", "html": "1941 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French cyclist (b. 1884)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French cyclist (b. 1884)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1947", "text": "<PERSON>, American businessman, co-founded General Motors and Chevrolet (b. 1861)", "html": "1947 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American businessman, co-founded <a href=\"https://wikipedia.org/wiki/General_Motors\" title=\"General Motors\">General Motors</a> and <a href=\"https://wikipedia.org/wiki/Chevrolet\" title=\"Chevrolet\">Chevrolet</a> (b. 1861)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American businessman, co-founded <a href=\"https://wikipedia.org/wiki/General_Motors\" title=\"General Motors\">General Motors</a> and <a href=\"https://wikipedia.org/wiki/Chevrolet\" title=\"Chevrolet\">Chevrolet</a> (b. 1861)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "General Motors", "link": "https://wikipedia.org/wiki/General_Motors"}, {"title": "Chevrolet", "link": "https://wikipedia.org/wiki/Chevrolet"}]}, {"year": "1954", "text": "<PERSON>, English cricketer (b. 1868)", "html": "1954 - <a href=\"https://wikipedia.org/wiki/1954\" title=\"1954\">1954</a> - <a href=\"https://wikipedia.org/wiki/<PERSON>(cricketer)\" title=\"<PERSON> (cricketer)\"><PERSON></a>, English cricketer (b. 1868)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/1954\" title=\"1954\">1954</a> - <a href=\"https://wikipedia.org/wiki/<PERSON>(cricketer)\" title=\"<PERSON> (cricketer)\"><PERSON></a>, English cricketer (b. 1868)", "links": [{"title": "1954", "link": "https://wikipedia.org/wiki/1954"}, {"title": "<PERSON> (cricketer)", "link": "https://wikipedia.org/wiki/<PERSON>(cricketer)"}]}, {"year": "1956", "text": "<PERSON>, American environmentalist and author (b. 1896)", "html": "1956 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American environmentalist and author (b. 1896)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American environmentalist and author (b. 1896)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1962", "text": "<PERSON>, American accountant and politician, 60th Governor of Delaware (b. 1880)", "html": "1962 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American accountant and politician, 60th <a href=\"https://wikipedia.org/wiki/Governor_of_Delaware\" class=\"mw-redirect\" title=\"Governor of Delaware\">Governor of Delaware</a> (b. 1880)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American accountant and politician, 60th <a href=\"https://wikipedia.org/wiki/Governor_of_Delaware\" class=\"mw-redirect\" title=\"Governor of Delaware\">Governor of Delaware</a> (b. 1880)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>"}, {"title": "Governor of Delaware", "link": "https://wikipedia.org/wiki/Governor_of_Delaware"}]}, {"year": "1963", "text": "<PERSON><PERSON> <PERSON><PERSON>, English Jesuit priest (b. 1879)", "html": "1963 - <a href=\"https://wikipedia.org/wiki/C._C._<PERSON>\" title=\"C. C. <PERSON>\"><PERSON><PERSON> <PERSON><PERSON></a>, English Jesuit priest (b. 1879)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/C._C._<PERSON>\" title=\"C. C. <PERSON>\"><PERSON><PERSON> <PERSON><PERSON></a>, English Jesuit priest (b. 1879)", "links": [{"title": "C. C. <PERSON>", "link": "https://wikipedia.org/wiki/C._<PERSON><PERSON>_<PERSON>"}]}, {"year": "1964", "text": "<PERSON><PERSON><PERSON><PERSON>, Swedish businessman, 4th President of the International Olympic Committee (b. 1870)", "html": "1964 - <a href=\"https://wikipedia.org/wiki/Sigfrid_Edstr%C3%B6m\" class=\"mw-redirect\" title=\"<PERSON>g<PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Swedish businessman, 4th <a href=\"https://wikipedia.org/wiki/President_of_the_International_Olympic_Committee\" title=\"President of the International Olympic Committee\">President of the International Olympic Committee</a> (b. 1870)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Sigfrid_Edstr%C3%B6m\" class=\"mw-redirect\" title=\"<PERSON>g<PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Swedish businessman, 4th <a href=\"https://wikipedia.org/wiki/President_of_the_International_Olympic_Committee\" title=\"President of the International Olympic Committee\">President of the International Olympic Committee</a> (b. 1870)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Sigfrid_Edstr%C3%B6m"}, {"title": "President of the International Olympic Committee", "link": "https://wikipedia.org/wiki/President_of_the_International_Olympic_Committee"}]}, {"year": "1965", "text": "<PERSON><PERSON><PERSON> of Egypt (b. 1920)", "html": "1965 - <a href=\"https://wikipedia.org/wiki/Farouk_of_Egypt\" title=\"Farouk of Egypt\"><PERSON><PERSON><PERSON> of Egypt</a> (b. 1920)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Farouk_of_Egypt\" title=\"Farouk of Egypt\"><PERSON><PERSON><PERSON> of Egypt</a> (b. 1920)", "links": [{"title": "Far<PERSON>k of Egypt", "link": "https://wikipedia.org/wiki/Farouk_of_Egypt"}]}, {"year": "1973", "text": "<PERSON>, Estonian philologist and poet (b. 1880)", "html": "1973 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Estonian philologist and poet (b. 1880)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Estonian philologist and poet (b. 1880)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1977", "text": "<PERSON><PERSON>, Congolese politician, President of the Republic of the Congo (b. 1938)", "html": "1977 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Congolese politician, <a href=\"https://wikipedia.org/wiki/President_of_the_Republic_of_the_Congo\" class=\"mw-redirect\" title=\"President of the Republic of the Congo\">President of the Republic of the Congo</a> (b. 1938)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Congolese politician, <a href=\"https://wikipedia.org/wiki/President_of_the_Republic_of_the_Congo\" class=\"mw-redirect\" title=\"President of the Republic of the Congo\">President of the Republic of the Congo</a> (b. 1938)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}, {"title": "President of the Republic of the Congo", "link": "https://wikipedia.org/wiki/President_of_the_Republic_of_the_Congo"}]}, {"year": "1977", "text": "<PERSON>, Brazilian race car driver (b. 1944)", "html": "1977 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Brazilian race car driver (b. 1944)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Brazilian race car driver (b. 1944)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1978", "text": "<PERSON>, American author and screenwriter (b. 1915)", "html": "1978 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author and screenwriter (b. 1915)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author and screenwriter (b. 1915)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1978", "text": "<PERSON>, American actress (b. 1892)", "html": "1978 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress (b. 1892)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress (b. 1892)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1980", "text": "<PERSON>, German psychologist and philosopher (b. 1900)", "html": "1980 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German psychologist and philosopher (b. 1900)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German psychologist and philosopher (b. 1900)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1982", "text": "<PERSON>, Irish farmer and politician, Minister for Agriculture, Food and the Marine (b. 1901)", "html": "1982 - <a href=\"https://wikipedia.org/wiki/<PERSON>(politician)\" title=\"<PERSON> (politician)\"><PERSON></a>, Irish farmer and politician, <a href=\"https://wikipedia.org/wiki/Minister_for_Agriculture,_Food_and_the_Marine\" title=\"Minister for Agriculture, Food and the Marine\">Minister for Agriculture, Food and the Marine</a> (b. 1901)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>(politician)\" title=\"<PERSON> (politician)\"><PERSON></a>, Irish farmer and politician, <a href=\"https://wikipedia.org/wiki/Minister_for_Agriculture,_Food_and_the_Marine\" title=\"Minister for Agriculture, Food and the Marine\">Minister for Agriculture, Food and the Marine</a> (b. 1901)", "links": [{"title": "<PERSON> (politician)", "link": "https://wikipedia.org/wiki/<PERSON>_(politician)"}, {"title": "Minister for Agriculture, Food and the Marine", "link": "https://wikipedia.org/wiki/Minister_for_Agriculture,_Food_and_the_Marine"}]}, {"year": "1983", "text": "<PERSON><PERSON> of Italy (b. 1904)", "html": "1983 - <a href=\"https://wikipedia.org/wiki/Umberto_II_of_Italy\" title=\"Umberto II of Italy\">Umberto II of Italy</a> (b. 1904)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Umberto_II_of_Italy\" title=\"Umberto II of Italy\">Umberto II of Italy</a> (b. 1904)", "links": [{"title": "<PERSON>berto II of Italy", "link": "https://wikipedia.org/wiki/Umberto_II_of_Italy"}]}, {"year": "1984", "text": "<PERSON>, American baseball player and coach (b. 1933)", "html": "1984 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player and coach (b. 1933)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player and coach (b. 1933)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1986", "text": "<PERSON>, American novelist and short story writer (b. 1914)", "html": "1986 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American novelist and short story writer (b. 1914)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American novelist and short story writer (b. 1914)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1987", "text": "<PERSON><PERSON>, Norwegian singer and revue actress (b. 1914)", "html": "1987 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Norwegian singer and revue actress (b. 1914)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Norwegian singer and revue actress (b. 1914)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1988", "text": "<PERSON>, American trumpet player and cornet player (b. 1917)", "html": "1988 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American trumpet player and cornet player (b. 1917)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American trumpet player and cornet player (b. 1917)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1990", "text": "<PERSON>, American comedian (b. 1953)", "html": "1990 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(comedian)\" title=\"<PERSON> (comedian)\"><PERSON></a>, American comedian (b. 1953)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(comedian)\" title=\"<PERSON> (comedian)\"><PERSON></a>, American comedian (b. 1953)", "links": [{"title": "<PERSON> (comedian)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(comedian)"}]}, {"year": "1993", "text": "<PERSON>, English-American economist and activist (b. 1910)", "html": "1993 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English-American economist and activist (b. 1910)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English-American economist and activist (b. 1910)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1996", "text": "<PERSON><PERSON><PERSON><PERSON>, Greek poet and critic, Nobel Prize laureate (b. 1911)", "html": "1996 - <a href=\"https://wikipedia.org/wiki/O<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Greek poet and critic, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Literature\" title=\"Nobel Prize in Literature\">Nobel Prize</a> laureate (b. 1911)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Greek poet and critic, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Literature\" title=\"Nobel Prize in Literature\">Nobel Prize</a> laureate (b. 1911)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Odysseas_<PERSON>tis"}, {"title": "Nobel Prize in Literature", "link": "https://wikipedia.org/wiki/Nobel_Prize_in_Literature"}]}, {"year": "2000", "text": "<PERSON><PERSON><PERSON>, German theologian and academic (b. 1909)", "html": "2000 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, German theologian and academic (b. 1909)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, German theologian and academic (b. 1909)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>ge"}]}, {"year": "2001", "text": "<PERSON>, American singer-songwriter and guitarist (b. 1935)", "html": "2001 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(musician)\" title=\"<PERSON> (musician)\"><PERSON></a>, American singer-songwriter and guitarist (b. 1935)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(musician)\" title=\"<PERSON> (musician)\"><PERSON></a>, American singer-songwriter and guitarist (b. 1935)", "links": [{"title": "<PERSON> (musician)", "link": "https://wikipedia.org/wiki/<PERSON>_(musician)"}]}, {"year": "2002", "text": "<PERSON><PERSON> <PERSON><PERSON>, American soldier and author (b. 1914)", "html": "2002 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> <PERSON><PERSON>\"><PERSON><PERSON> <PERSON><PERSON></a>, American soldier and author (b. 1914)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> <PERSON><PERSON>\"><PERSON><PERSON> <PERSON><PERSON></a>, American soldier and author (b. 1914)", "links": [{"title": "<PERSON><PERSON> <PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "2003", "text": "<PERSON>, German race car driver (b. 1910)", "html": "2003 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German race car driver (b. 1910)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German race car driver (b. 1910)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2003", "text": "<PERSON>, Thai-English engineer and businessman, founded the Osborne Computer Corporation (b. 1939)", "html": "2003 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Thai-English engineer and businessman, founded the <a href=\"https://wikipedia.org/wiki/Osborne_Computer_Corporation\" title=\"Osborne Computer Corporation\">Osborne Computer Corporation</a> (b. 1939)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Thai-English engineer and businessman, founded the <a href=\"https://wikipedia.org/wiki/Osborne_Computer_Corporation\" title=\"Osborne Computer Corporation\">Osborne Computer Corporation</a> (b. 1939)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Osborne Computer Corporation", "link": "https://wikipedia.org/wiki/Osborne_Computer_Corporation"}]}, {"year": "2004", "text": "<PERSON>, Canadian businessman, co-founded McCain Foods (b. 1927)", "html": "2004 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian businessman, co-founded <a href=\"https://wikipedia.org/wiki/McCain_Foods\" title=\"McCain Foods\">McCain Foods</a> (b. 1927)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian businessman, co-founded <a href=\"https://wikipedia.org/wiki/McCain_Foods\" title=\"McCain Foods\">McCain Foods</a> (b. 1927)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "McCain Foods", "link": "https://wikipedia.org/wiki/McCain_Foods"}]}, {"year": "2006", "text": "<PERSON>, Canadian photographer and cinematographer (b. 1922)", "html": "2006 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian photographer and cinematographer (b. 1922)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian photographer and cinematographer (b. 1922)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2007", "text": "<PERSON>, Indian-English cricketer, coach, and sportscaster (b. 1948)", "html": "2007 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Indian-English cricketer, coach, and sportscaster (b. 1948)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Indian-English cricketer, coach, and sportscaster (b. 1948)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2008", "text": "<PERSON>, English director and screenwriter (b. 1954)", "html": "2008 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English director and screenwriter (b. 1954)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English director and screenwriter (b. 1954)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2009", "text": "<PERSON><PERSON><PERSON>, Iranian journalist and blogger (b. 1980)", "html": "2009 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Iranian journalist and blogger (b. 1980)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Iranian journalist and blogger (b. 1980)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON><PERSON>_<PERSON>_<PERSON>"}]}, {"year": "2009", "text": "<PERSON>, English-American actress (b. 1963)", "html": "2009 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English-American actress (b. 1963)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English-American actress (b. 1963)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2010", "text": "<PERSON><PERSON>, American actor and businessman (b. 1924)", "html": "2010 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> <PERSON>\"><PERSON><PERSON></a>, American actor and businessman (b. 1924)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> <PERSON>\"><PERSON><PERSON></a>, American actor and businessman (b. 1924)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "2011", "text": "<PERSON>, American lawyer and politician, 63rd United States Secretary of State (b. 1925)", "html": "2011 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and politician, 63rd <a href=\"https://wikipedia.org/wiki/United_States_Secretary_of_State\" title=\"United States Secretary of State\">United States Secretary of State</a> (b. 1925)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and politician, 63rd <a href=\"https://wikipedia.org/wiki/United_States_Secretary_of_State\" title=\"United States Secretary of State\">United States Secretary of State</a> (b. 1925)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "United States Secretary of State", "link": "https://wikipedia.org/wiki/United_States_Secretary_of_State"}]}, {"year": "2012", "text": "<PERSON><PERSON>, American journalist and author (b. 1918)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_B<PERSON>er\" title=\"Furman Bisher\"><PERSON><PERSON></a>, American journalist and author (b. 1918)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_B<PERSON>er\" title=\"Furman Bisher\"><PERSON><PERSON></a>, American journalist and author (b. 1918)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>er"}]}, {"year": "2012", "text": "<PERSON>, American soldier, Medal of Honor recipient (b. 1932)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American soldier, <a href=\"https://wikipedia.org/wiki/Medal_of_Honor\" title=\"Medal of Honor\">Medal of Honor</a> recipient (b. 1932)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American soldier, <a href=\"https://wikipedia.org/wiki/Medal_of_Honor\" title=\"Medal of Honor\">Medal of Honor</a> recipient (b. 1932)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "Medal of Honor", "link": "https://wikipedia.org/wiki/Medal_of_Honor"}]}, {"year": "2012", "text": "<PERSON>, American general (b. 1920)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>.\" title=\"<PERSON> Jr.\"><PERSON>.</a>, American general (b. 1920)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>.\" title=\"<PERSON>.\"><PERSON>.</a>, American general (b. 1920)", "links": [{"title": "<PERSON>.", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>_<PERSON>."}]}, {"year": "2012", "text": "<PERSON> Tonga (b. 1948)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"George <PERSON>po<PERSON> V\"><PERSON></a> of Tonga (b. 1948)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"George <PERSON> V\"><PERSON></a> of Tonga (b. 1948)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "2013", "text": "<PERSON>, Pakistani general and pilot (b. 1935)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Pakistani general and pilot (b. 1935)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Pakistani general and pilot (b. 1935)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "2013", "text": "<PERSON>, American novelist, screenwriter, and director (b. 1947)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American novelist, screenwriter, and director (b. 1947)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American novelist, screenwriter, and director (b. 1947)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2013", "text": "<PERSON>, American lawyer and politician (b. 1938)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/<PERSON>_Ford\" title=\"Clay Ford\"><PERSON></a>, American lawyer and politician (b. 1938)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Clay_Ford\" title=\"Clay Ford\"><PERSON></a>, American lawyer and politician (b. 1938)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_Ford"}]}, {"year": "2014", "text": "<PERSON>, Nigerian author, playwright, and academic (b. 1951)", "html": "2014 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Nigerian author, playwright, and academic (b. 1951)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Nigerian author, playwright, and academic (b. 1951)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Catherine_<PERSON>_<PERSON>u"}]}, {"year": "2014", "text": "<PERSON>, Zambian footballer, coach, and manager (b. 1953)", "html": "2014 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Kaiser <PERSON>\"><PERSON></a>, Zambian footballer, coach, and manager (b. 1953)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Kaiser <PERSON>\"><PERSON></a>, Zambian footballer, coach, and manager (b. 1953)", "links": [{"title": "Kaiser Kalambo", "link": "https://wikipedia.org/wiki/Kaiser_<PERSON>"}]}, {"year": "2014", "text": "<PERSON>, American author and critic (b. 1943)", "html": "2014 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author and critic (b. 1943)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author and critic (b. 1943)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2015", "text": "<PERSON>, Chinese footballer and manager (b. 1961)", "html": "2015 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Chinese footballer and manager (b. 1961)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Chinese footballer and manager (b. 1961)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2015", "text": "<PERSON>, American priest and theologian (b. 1939)", "html": "2015 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American priest and theologian (b. 1939)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American priest and theologian (b. 1939)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2015", "text": "<PERSON>, Kenyan nurse, journalist, and politician (b. 1930)", "html": "2015 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Ogot\"><PERSON></a>, Kenyan nurse, journalist, and politician (b. 1930)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Ogot\"><PERSON></a>, Kenyan nurse, journalist, and politician (b. 1930)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2016", "text": "<PERSON>, English author and screenwriter (b. 1939)", "html": "2016 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English author and screenwriter (b. 1939)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English author and screenwriter (b. 1939)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2016", "text": "<PERSON>, Czech director and screenwriter (b. 1936)", "html": "2016 - <a href=\"https://wikipedia.org/wiki/Jan_N%C4%9Bmec\" title=\"<PERSON>\"><PERSON></a>, Czech director and screenwriter (b. 1936)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Jan_<PERSON>%C4%9Bmec\" title=\"<PERSON>\"><PERSON></a>, Czech director and screenwriter (b. 1936)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Jan_N%C4%9Bmec"}]}, {"year": "2016", "text": "<PERSON><PERSON>, American football player (b. 1992)", "html": "2016 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American football player (b. 1992)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> <PERSON>\"><PERSON><PERSON></a>, American football player (b. 1992)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/T<PERSON>_<PERSON>"}]}, {"year": "2016", "text": "<PERSON>, German lawyer and politician, 15th Vice-Chancellor of Germany (b. 1961)", "html": "2016 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German lawyer and politician, 15th <a href=\"https://wikipedia.org/wiki/Vice-Chancellor_of_Germany\" title=\"Vice-Chancellor of Germany\">Vice-Chancellor of Germany</a> (b. 1961)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German lawyer and politician, 15th <a href=\"https://wikipedia.org/wiki/Vice-Chancellor_of_Germany\" title=\"Vice-Chancellor of Germany\">Vice-Chancellor of Germany</a> (b. 1961)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Vice-Chancellor of Germany", "link": "https://wikipedia.org/wiki/Vice-Chancellor_of_Germany"}]}, {"year": "2017", "text": "<PERSON>, American guitarist, singer and songwriter (b. 1926)", "html": "2017 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American guitarist, singer and songwriter (b. 1926)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American guitarist, singer and songwriter (b. 1926)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2020", "text": "<PERSON>, American test pilot, engineer and astronaut (b. 1932)", "html": "2020 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American test pilot, engineer and astronaut (b. 1932)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American test pilot, engineer and astronaut (b. 1932)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2024", "text": "<PERSON>, American Air Force officer, test pilot, and NASA astronaut (b. 1930)", "html": "2024 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American Air Force officer, test pilot, and NASA astronaut (b. 1930)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American Air Force officer, test pilot, and NASA astronaut (b. 1930)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}]}}