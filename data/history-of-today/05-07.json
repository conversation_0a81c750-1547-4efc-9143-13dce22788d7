{"date": "May 7", "url": "https://wikipedia.org/wiki/May_7", "data": {"Events": [{"year": "351", "text": "The Jewish revolt against <PERSON><PERSON><PERSON> breaks out after his arrival at Antioch.", "html": "351 - The <a href=\"https://wikipedia.org/wiki/Jewish_revolt_against_<PERSON><PERSON><PERSON>_Gallus\" title=\"Jewish revolt against <PERSON><PERSON><PERSON> Gallus\">Jewish revolt against <PERSON><PERSON><PERSON></a> breaks out after his arrival at <a href=\"https://wikipedia.org/wiki/Antioch\" title=\"Antioch\">Antioch</a>.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Jewish_revolt_against_<PERSON><PERSON><PERSON>_Gallus\" title=\"Jewish revolt against <PERSON><PERSON><PERSON> Gallus\">Jewish revolt against <PERSON><PERSON><PERSON></a> breaks out after his arrival at <a href=\"https://wikipedia.org/wiki/Antioch\" title=\"Antioch\">Antioch</a>.", "links": [{"title": "Jewish revolt against <PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Jewish_revolt_against_<PERSON><PERSON><PERSON>_<PERSON>"}, {"title": "Antioch", "link": "https://wikipedia.org/wiki/Antioch"}]}, {"year": "558", "text": "In Constantinople, the dome of the Hagia Sophia collapses, twenty years after its construction. <PERSON><PERSON> immediately orders that the dome be rebuilt.", "html": "558 - In <a href=\"https://wikipedia.org/wiki/Constantinople\" title=\"Constantinople\">Constantinople</a>, the <a href=\"https://wikipedia.org/wiki/Dome\" title=\"Dome\">dome</a> of the <a href=\"https://wikipedia.org/wiki/Hagia_Sophia\" title=\"Hagia Sophia\">Hagia Sophia</a> collapses, twenty years after its construction. <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_I\" title=\"<PERSON><PERSON> I\"><PERSON><PERSON> I</a> immediately orders that the dome be rebuilt.", "no_year_html": "In <a href=\"https://wikipedia.org/wiki/Constantinople\" title=\"Constantinople\">Constantinople</a>, the <a href=\"https://wikipedia.org/wiki/Dome\" title=\"Dome\">dome</a> of the <a href=\"https://wikipedia.org/wiki/Hagia_Sophia\" title=\"Hagia Sophia\">Hagia Sophia</a> collapses, twenty years after its construction. <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> I\"><PERSON><PERSON> I</a> immediately orders that the dome be rebuilt.", "links": [{"title": "Constantinople", "link": "https://wikipedia.org/wiki/Constantinople"}, {"title": "Dome", "link": "https://wikipedia.org/wiki/Dome"}, {"title": "Hagia Sophia", "link": "https://wikipedia.org/wiki/Hagia_Sophia"}, {"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1274", "text": "In France, the Second Council of Lyon opens; it ratified a decree to regulate the election of the Pope.", "html": "1274 - In France, the <a href=\"https://wikipedia.org/wiki/Second_Council_of_Lyon\" title=\"Second Council of Lyon\">Second Council of Lyon</a> opens; it ratified a decree to regulate the election of the <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"Pope\">Pope</a>.", "no_year_html": "In France, the <a href=\"https://wikipedia.org/wiki/Second_Council_of_Lyon\" title=\"Second Council of Lyon\">Second Council of Lyon</a> opens; it ratified a decree to regulate the election of the <a href=\"https://wikipedia.org/wiki/Pope\" title=\"Pope\">Pope</a>.", "links": [{"title": "Second Council of Lyon", "link": "https://wikipedia.org/wiki/Second_Council_of_Lyon"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Pope"}]}, {"year": "1541", "text": "<PERSON>, Countess of Salisbury, executed at the Tower of London for having 'committed and perpetrated diverse and sundry other detestable and abominable treasons' against her kinsman King <PERSON> of England", "html": "1541 - <a href=\"https://wikipedia.org/wiki/<PERSON>,_Countess_of_Salisbury\" title=\"<PERSON>, Countess of Salisbury\"><PERSON>, Countess of Salisbury</a>, executed at the <a href=\"https://wikipedia.org/wiki/Tower_of_London\" title=\"Tower of London\">Tower of London</a> for having 'committed and perpetrated diverse and sundry other detestable and abominable treasons' against her kinsman King <a href=\"https://wikipedia.org/wiki/<PERSON>_VIII\" title=\"<PERSON> VIII\"><PERSON> of England</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>,_Countess_of_Salisbury\" title=\"<PERSON>, Countess of Salisbury\"><PERSON>, Countess of Salisbury</a>, executed at the <a href=\"https://wikipedia.org/wiki/Tower_of_London\" title=\"Tower of London\">Tower of London</a> for having 'committed and perpetrated diverse and sundry other detestable and abominable treasons' against her kinsman King <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> VIII\"><PERSON> VIII of England</a>", "links": [{"title": "<PERSON>, Countess of Salisbury", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Countess_of_Salisbury"}, {"title": "Tower of London", "link": "https://wikipedia.org/wiki/Tower_of_London"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1487", "text": "The Siege of Málaga commences during the Spanish Reconquista.", "html": "1487 - The <a href=\"https://wikipedia.org/wiki/Siege_of_M%C3%A1laga_(1487)\" title=\"Siege of Málaga (1487)\">Siege of Málaga</a> commences during the Spanish <a href=\"https://wikipedia.org/wiki/Reconquista\" title=\"Reconquista\">Reconquista</a>.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Siege_of_M%C3%A1laga_(1487)\" title=\"Siege of Málaga (1487)\">Siege of Málaga</a> commences during the Spanish <a href=\"https://wikipedia.org/wiki/Reconquista\" title=\"Reconquista\">Reconquista</a>.", "links": [{"title": "Siege of Málaga (1487)", "link": "https://wikipedia.org/wiki/Siege_of_M%C3%A1laga_(1487)"}, {"title": "Reconquista", "link": "https://wikipedia.org/wiki/Reconquista"}]}, {"year": "1544", "text": "The Burning of Edinburgh by an English army is the first action of the Rough Wooing.", "html": "1544 - The <a href=\"https://wikipedia.org/wiki/Burning_of_Edinburgh\" title=\"Burning of Edinburgh\">Burning of Edinburgh</a> by an English army is the first action of the <a href=\"https://wikipedia.org/wiki/Rough_Wooing\" title=\"Rough Wooing\">Rough Wooing</a>.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Burning_of_Edinburgh\" title=\"Burning of Edinburgh\">Burning of Edinburgh</a> by an English army is the first action of the <a href=\"https://wikipedia.org/wiki/Rough_Wooing\" title=\"Rough Wooing\">Rough Wooing</a>.", "links": [{"title": "Burning of Edinburgh", "link": "https://wikipedia.org/wiki/Burning_of_Edinburgh"}, {"title": "Rough Wooing", "link": "https://wikipedia.org/wiki/Rough_Wooing"}]}, {"year": "1625", "text": "State funeral of <PERSON> and <PERSON> (1566-1625) is held at Westminster Abbey.", "html": "1625 - <a href=\"https://wikipedia.org/wiki/Death_and_funeral_of_<PERSON>_VI_and_I\" title=\"Death and funeral of <PERSON> VI and I\">State funeral</a> of <a href=\"https://wikipedia.org/wiki/<PERSON>_VI_and_I\" title=\"<PERSON> VI and I\"><PERSON> VI and I</a> (1566-1625) is held at <a href=\"https://wikipedia.org/wiki/Westminster_Abbey\" title=\"Westminster Abbey\">Westminster Abbey</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Death_and_funeral_of_<PERSON>_VI_and_I\" title=\"Death and funeral of <PERSON> VI and I\">State funeral</a> of <a href=\"https://wikipedia.org/wiki/<PERSON>_VI_and_I\" title=\"<PERSON> VI and I\"><PERSON> VI and I</a> (1566-1625) is held at <a href=\"https://wikipedia.org/wiki/Westminster_Abbey\" title=\"Westminster Abbey\">Westminster Abbey</a>.", "links": [{"title": "Death and funeral of <PERSON> and I", "link": "https://wikipedia.org/wiki/Death_and_funeral_of_<PERSON>_VI_and_I"}, {"title": "James <PERSON> and I", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_and_I"}, {"title": "Westminster Abbey", "link": "https://wikipedia.org/wiki/Westminster_Abbey"}]}, {"year": "1664", "text": "Inaugural celebrations begin at Louis XIV of France's new Palace of Versailles.", "html": "1664 - Inaugural celebrations begin at <a href=\"https://wikipedia.org/wiki/Louis_XIV_of_France\" class=\"mw-redirect\" title=\"Louis XIV of France\"><PERSON> of France</a>'s new <a href=\"https://wikipedia.org/wiki/Palace_of_Versailles\" title=\"Palace of Versailles\">Palace of Versailles</a>.", "no_year_html": "Inaugural celebrations begin at <a href=\"https://wikipedia.org/wiki/Louis_XIV_of_France\" class=\"mw-redirect\" title=\"Louis XIV of France\"><PERSON> of France</a>'s new <a href=\"https://wikipedia.org/wiki/Palace_of_Versailles\" title=\"Palace of Versailles\">Palace of Versailles</a>.", "links": [{"title": "<PERSON> of France", "link": "https://wikipedia.org/wiki/Louis_XIV_of_France"}, {"title": "Palace of Versailles", "link": "https://wikipedia.org/wiki/Palace_of_Versailles"}]}, {"year": "1685", "text": "Battle of Vrtijeljka between rebels and Ottoman forces.", "html": "1685 - <a href=\"https://wikipedia.org/wiki/Battle_of_Vrtijeljka\" class=\"mw-redirect\" title=\"Battle of Vrtijeljka\">Battle of Vrtijeljka</a> between rebels and Ottoman forces.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Battle_of_Vrtijeljka\" class=\"mw-redirect\" title=\"Battle of Vrtijeljka\">Battle of Vrtijeljka</a> between rebels and Ottoman forces.", "links": [{"title": "Battle of Vrtijeljka", "link": "https://wikipedia.org/wiki/Battle_of_V<PERSON><PERSON><PERSON><PERSON><PERSON>a"}]}, {"year": "1697", "text": "Stockholm's royal castle (dating back to medieval times) is destroyed by fire. It is replaced in the 18th century by the current Royal Palace.", "html": "1697 - <a href=\"https://wikipedia.org/wiki/Stockholm\" title=\"Stockholm\">Stockholm</a>'s royal castle (dating back to <a href=\"https://wikipedia.org/wiki/Middle_Ages\" title=\"Middle Ages\">medieval</a> times) is destroyed by fire. It is replaced in the 18th century by the current <a href=\"https://wikipedia.org/wiki/Stockholm_Palace\" title=\"Stockholm Palace\">Royal Palace</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Stockholm\" title=\"Stockholm\">Stockholm</a>'s royal castle (dating back to <a href=\"https://wikipedia.org/wiki/Middle_Ages\" title=\"Middle Ages\">medieval</a> times) is destroyed by fire. It is replaced in the 18th century by the current <a href=\"https://wikipedia.org/wiki/Stockholm_Palace\" title=\"Stockholm Palace\">Royal Palace</a>.", "links": [{"title": "Stockholm", "link": "https://wikipedia.org/wiki/Stockholm"}, {"title": "Middle Ages", "link": "https://wikipedia.org/wiki/Middle_Ages"}, {"title": "Stockholm Palace", "link": "https://wikipedia.org/wiki/Stockholm_Palace"}]}, {"year": "1718", "text": "The city of New Orleans is founded by <PERSON><PERSON><PERSON>, <PERSON><PERSON>.", "html": "1718 - The city of <a href=\"https://wikipedia.org/wiki/New_Orleans\" title=\"New Orleans\">New Orleans</a> is founded by <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>,_<PERSON><PERSON>_<PERSON>_Bienville\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON>, <PERSON><PERSON>\"><PERSON><PERSON><PERSON>, <PERSON><PERSON></a>.", "no_year_html": "The city of <a href=\"https://wikipedia.org/wiki/New_Orleans\" title=\"New Orleans\">New Orleans</a> is founded by <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>,_<PERSON><PERSON>_<PERSON>_Bienville\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON>, <PERSON><PERSON>\"><PERSON><PERSON><PERSON>, <PERSON><PERSON></a>.", "links": [{"title": "New Orleans", "link": "https://wikipedia.org/wiki/New_Orleans"}, {"title": "<PERSON><PERSON><PERSON>, <PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>,_<PERSON><PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1763", "text": "Pontiac's War begins with Pontiac's attempt to seize Fort Detroit from the British.", "html": "1763 - <a href=\"https://wikipedia.org/wiki/Pontiac%27s_War\" title=\"Pontiac's War\">Pontiac's War</a> begins with <a href=\"https://wikipedia.org/wiki/Pontiac_(Ottawa_leader)\" class=\"mw-redirect\" title=\"Pontiac (Ottawa leader)\">Pontiac</a>'s attempt to <a href=\"https://wikipedia.org/wiki/Siege_of_Fort_Detroit\" title=\"Siege of Fort Detroit\">seize</a> <a href=\"https://wikipedia.org/wiki/Fort_Detroit\" class=\"mw-redirect\" title=\"Fort Detroit\">Fort Detroit</a> from the British.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Pontiac%27s_War\" title=\"Pontiac's War\">Pontiac's War</a> begins with <a href=\"https://wikipedia.org/wiki/Pontiac_(Ottawa_leader)\" class=\"mw-redirect\" title=\"<PERSON> (Ottawa leader)\">Pontiac</a>'s attempt to <a href=\"https://wikipedia.org/wiki/Siege_of_Fort_Detroit\" title=\"Siege of Fort Detroit\">seize</a> <a href=\"https://wikipedia.org/wiki/Fort_Detroit\" class=\"mw-redirect\" title=\"Fort Detroit\">Fort Detroit</a> from the British.", "links": [{"title": "Pontiac's War", "link": "https://wikipedia.org/wiki/Pontiac%27s_War"}, {"title": "<PERSON> (Ottawa leader)", "link": "https://wikipedia.org/wiki/Pontiac_(Ottawa_leader)"}, {"title": "Siege of Fort Detroit", "link": "https://wikipedia.org/wiki/Siege_of_Fort_Detroit"}, {"title": "Fort Detroit", "link": "https://wikipedia.org/wiki/Fort_Detroit"}]}, {"year": "1765", "text": "HMS Victory is launched at Chatham Dockyard, Kent. She is not commissioned until 1778.", "html": "1765 - <a href=\"https://wikipedia.org/wiki/HMS_Victory\" title=\"HMS Victory\">HMS Victory</a> is launched at <a href=\"https://wikipedia.org/wiki/Chatham_Dockyard\" title=\"Chatham Dockyard\">Chatham Dockyard</a>, Kent. She is not commissioned until 1778.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/HMS_Victory\" title=\"HMS Victory\">HMS Victory</a> is launched at <a href=\"https://wikipedia.org/wiki/Chatham_Dockyard\" title=\"Chatham Dockyard\">Chatham Dockyard</a>, Kent. She is not commissioned until 1778.", "links": [{"title": "HMS Victory", "link": "https://wikipedia.org/wiki/HMS_Victory"}, {"title": "Chatham Dockyard", "link": "https://wikipedia.org/wiki/Chatham_Dockyard"}]}, {"year": "1794", "text": "French Revolution: <PERSON><PERSON><PERSON><PERSON> introduces the Cult of the Supreme Being in the National Convention as the new state religion of the French First Republic.", "html": "1794 - <a href=\"https://wikipedia.org/wiki/French_Revolution\" title=\"French Revolution\">French Revolution</a>: <a href=\"https://wikipedia.org/wiki/Maximilien_Robespierre\" title=\"Maximilien Robespierre\"><PERSON><PERSON><PERSON><PERSON></a> introduces the <a href=\"https://wikipedia.org/wiki/Cult_of_the_Supreme_Being\" title=\"Cult of the Supreme Being\">Cult of the Supreme Being</a> in the <a href=\"https://wikipedia.org/wiki/National_Convention\" title=\"National Convention\">National Convention</a> as the new <a href=\"https://wikipedia.org/wiki/State_religion\" title=\"State religion\">state religion</a> of the <a href=\"https://wikipedia.org/wiki/French_First_Republic\" title=\"French First Republic\">French First Republic</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/French_Revolution\" title=\"French Revolution\">French Revolution</a>: <a href=\"https://wikipedia.org/wiki/Maximilien_Robespierre\" title=\"Maximilien Robespierre\"><PERSON><PERSON>pierre</a> introduces the <a href=\"https://wikipedia.org/wiki/Cult_of_the_Supreme_Being\" title=\"Cult of the Supreme Being\">Cult of the Supreme Being</a> in the <a href=\"https://wikipedia.org/wiki/National_Convention\" title=\"National Convention\">National Convention</a> as the new <a href=\"https://wikipedia.org/wiki/State_religion\" title=\"State religion\">state religion</a> of the <a href=\"https://wikipedia.org/wiki/French_First_Republic\" title=\"French First Republic\">French First Republic</a>.", "links": [{"title": "French Revolution", "link": "https://wikipedia.org/wiki/French_Revolution"}, {"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Maximilien_Robespierre"}, {"title": "Cult of the Supreme Being", "link": "https://wikipedia.org/wiki/Cult_of_the_Supreme_Being"}, {"title": "National Convention", "link": "https://wikipedia.org/wiki/National_Convention"}, {"title": "State religion", "link": "https://wikipedia.org/wiki/State_religion"}, {"title": "French First Republic", "link": "https://wikipedia.org/wiki/French_First_Republic"}]}, {"year": "1798", "text": "French Revolutionary Wars: A French force attempting to dislodge a small British garrison on the Îles Saint-Marcouf is  repulsed with heavy losses.", "html": "1798 - <a href=\"https://wikipedia.org/wiki/French_Revolutionary_Wars\" title=\"French Revolutionary Wars\">French Revolutionary Wars</a>: A French force attempting to dislodge a small British garrison on the <a href=\"https://wikipedia.org/wiki/%C3%8Eles_Saint-Marcouf\" title=\"Îles Saint-Marcouf\">Îles Saint-Marcouf</a> is <a href=\"https://wikipedia.org/wiki/Battle_of_the_%C3%8Eles_Saint-Marcouf\" title=\"Battle of the Îles Saint-Marcouf\">repulsed with heavy losses</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/French_Revolutionary_Wars\" title=\"French Revolutionary Wars\">French Revolutionary Wars</a>: A French force attempting to dislodge a small British garrison on the <a href=\"https://wikipedia.org/wiki/%C3%8Eles_Saint-Marcouf\" title=\"Îles Saint-Marcouf\">Îles Saint-Marcouf</a> is <a href=\"https://wikipedia.org/wiki/Battle_of_the_%C3%8Eles_Saint-Marcouf\" title=\"Battle of the Îles Saint-Marcouf\">repulsed with heavy losses</a>.", "links": [{"title": "French Revolutionary Wars", "link": "https://wikipedia.org/wiki/French_Revolutionary_Wars"}, {"title": "Îles Saint-Marcouf", "link": "https://wikipedia.org/wiki/%C3%8Eles_Saint-Marco<PERSON>"}, {"title": "Battle of the Îles Saint-Marcouf", "link": "https://wikipedia.org/wiki/Battle_of_the_%C3%8Eles_<PERSON>-<PERSON>"}]}, {"year": "1824", "text": "World premiere of <PERSON>'s Ninth Symphony in Vienna, Austria. The performance is conducted by <PERSON> under the composer's supervision.", "html": "1824 - World premiere of <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> van Beethoven\"><PERSON> van <PERSON></a>'s <a href=\"https://wikipedia.org/wiki/Symphony_No._9_(<PERSON>)\" title=\"Symphony No. 9 (Beethoven)\">Ninth Symphony</a> in <a href=\"https://wikipedia.org/wiki/Vienna\" title=\"Vienna\">Vienna</a>, Austria. The performance is conducted by <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> under the composer's supervision.", "no_year_html": "World premiere of <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> van Beethoven\"><PERSON></a>'s <a href=\"https://wikipedia.org/wiki/Symphony_No._9_(<PERSON>)\" title=\"Symphony No. 9 (Beethoven)\">Ninth Symphony</a> in <a href=\"https://wikipedia.org/wiki/Vienna\" title=\"Vienna\">Vienna</a>, Austria. The performance is conducted by <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> under the composer's supervision.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "Symphony No. 9 (<PERSON>)", "link": "https://wikipedia.org/wiki/Symphony_No._9_(<PERSON>)"}, {"title": "Vienna", "link": "https://wikipedia.org/wiki/Vienna"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1832", "text": "Greece's independence is recognized by the Treaty of London.", "html": "1832 - Greece's independence is recognized by the <a href=\"https://wikipedia.org/wiki/Treaty_of_London,_1832\" class=\"mw-redirect\" title=\"Treaty of London, 1832\">Treaty of London</a>.", "no_year_html": "Greece's independence is recognized by the <a href=\"https://wikipedia.org/wiki/Treaty_of_London,_1832\" class=\"mw-redirect\" title=\"Treaty of London, 1832\">Treaty of London</a>.", "links": [{"title": "Treaty of London, 1832", "link": "https://wikipedia.org/wiki/Treaty_of_London,_1832"}]}, {"year": "1840", "text": "The Great Natchez Tornado strikes Natchez, Mississippi killing 317 people. It is the second deadliest tornado in United States history.", "html": "1840 - The <a href=\"https://wikipedia.org/wiki/Great_Natchez_Tornado\" class=\"mw-redirect\" title=\"Great Natchez Tornado\">Great <PERSON><PERSON>z Tornado</a> strikes <a href=\"https://wikipedia.org/wiki/Natchez,_Mississippi\" title=\"Natchez, Mississippi\">Natchez, Mississippi</a> killing 317 people. It is the second deadliest <a href=\"https://wikipedia.org/wiki/Tornado\" title=\"Tornado\">tornado</a> in United States history.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Great_Natchez_Tornado\" class=\"mw-redirect\" title=\"Great Natchez Tornado\">Great Natchez Tornado</a> strikes <a href=\"https://wikipedia.org/wiki/Natchez,_Mississippi\" title=\"Natchez, Mississippi\">Natchez, Mississippi</a> killing 317 people. It is the second deadliest <a href=\"https://wikipedia.org/wiki/Tornado\" title=\"Tornado\">tornado</a> in United States history.", "links": [{"title": "Great Natchez Tornado", "link": "https://wikipedia.org/wiki/Great_Nat<PERSON>z_Tornado"}, {"title": "Natchez, Mississippi", "link": "https://wikipedia.org/wiki/Natchez,_Mississippi"}, {"title": "Tornado", "link": "https://wikipedia.org/wiki/Tornado"}]}, {"year": "1846", "text": "The Cambridge Chronicle, America's oldest surviving weekly newspaper, is published for the first time in Cambridge, Massachusetts.", "html": "1846 - The <i><a href=\"https://wikipedia.org/wiki/Cambridge_Chronicle\" title=\"Cambridge Chronicle\">Cambridge Chronicle</a></i>, America's oldest surviving weekly newspaper, is published for the first time in <a href=\"https://wikipedia.org/wiki/Cambridge,_Massachusetts\" title=\"Cambridge, Massachusetts\">Cambridge, Massachusetts</a>.", "no_year_html": "The <i><a href=\"https://wikipedia.org/wiki/Cambridge_Chronicle\" title=\"Cambridge Chronicle\">Cambridge Chronicle</a></i>, America's oldest surviving weekly newspaper, is published for the first time in <a href=\"https://wikipedia.org/wiki/Cambridge,_Massachusetts\" title=\"Cambridge, Massachusetts\">Cambridge, Massachusetts</a>.", "links": [{"title": "Cambridge Chronicle", "link": "https://wikipedia.org/wiki/Cambridge_Chronicle"}, {"title": "Cambridge, Massachusetts", "link": "https://wikipedia.org/wiki/Cambridge,_Massachusetts"}]}, {"year": "1864", "text": "American Civil War: The Army of the Potomac, under General <PERSON>, breaks off from the Battle of the Wilderness and moves southwards.", "html": "1864 - <a href=\"https://wikipedia.org/wiki/American_Civil_War\" title=\"American Civil War\">American Civil War</a>: The <a href=\"https://wikipedia.org/wiki/Army_of_the_Potomac\" title=\"Army of the Potomac\">Army of the Potomac</a>, under General <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_Grant\" title=\"Ulysses <PERSON> Grant\"><PERSON></a>, breaks off from the <a href=\"https://wikipedia.org/wiki/Battle_of_the_Wilderness\" title=\"Battle of the Wilderness\">Battle of the Wilderness</a> and moves southwards.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/American_Civil_War\" title=\"American Civil War\">American Civil War</a>: The <a href=\"https://wikipedia.org/wiki/Army_of_the_Potomac\" title=\"Army of the Potomac\">Army of the Potomac</a>, under General <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_Grant\" title=\"Ulysses <PERSON> Grant\"><PERSON></a>, breaks off from the <a href=\"https://wikipedia.org/wiki/Battle_of_the_Wilderness\" title=\"Battle of the Wilderness\">Battle of the Wilderness</a> and moves southwards.", "links": [{"title": "American Civil War", "link": "https://wikipedia.org/wiki/American_Civil_War"}, {"title": "Army of the Potomac", "link": "https://wikipedia.org/wiki/Army_of_the_Potomac"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>"}, {"title": "Battle of the Wilderness", "link": "https://wikipedia.org/wiki/Battle_of_the_Wilderness"}]}, {"year": "1864", "text": "The world's oldest surviving clipper ship, the City of Adelaide is launched by <PERSON>, Hay and Co. in Sunderland, England, for transporting passengers and goods between Britain and Australia.", "html": "1864 - The world's oldest surviving <a href=\"https://wikipedia.org/wiki/Clipper\" title=\"Clipper\">clipper</a> ship, the <i><a href=\"https://wikipedia.org/wiki/City_of_Adelaide_(1864)\" title=\"City of Adelaide (1864)\">City of Adelaide</a></i> is launched by <a href=\"https://wikipedia.org/wiki/<PERSON>_(shipbuilder)\" title=\"<PERSON> (shipbuilder)\"><PERSON>, Hay and Co.</a> in Sunderland, England, for transporting passengers and goods between Britain and Australia.", "no_year_html": "The world's oldest surviving <a href=\"https://wikipedia.org/wiki/Clipper\" title=\"Clipper\">clipper</a> ship, the <i><a href=\"https://wikipedia.org/wiki/City_of_Adelaide_(1864)\" title=\"City of Adelaide (1864)\">City of Adelaide</a></i> is launched by <a href=\"https://wikipedia.org/wiki/<PERSON>_(shipbuilder)\" title=\"<PERSON> (shipbuilder)\"><PERSON>, Hay and Co.</a> in Sunderland, England, for transporting passengers and goods between Britain and Australia.", "links": [{"title": "Clipper", "link": "https://wikipedia.org/wiki/Clipper"}, {"title": "City of Adelaide (1864)", "link": "https://wikipedia.org/wiki/City_of_Adelaide_(1864)"}, {"title": "<PERSON> (shipbuilder)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(shipbuilder)"}]}, {"year": "1895", "text": "In Saint Petersburg, Russian scientist <PERSON> demonstrates to the Russian Physical and Chemical Society his invention, the <PERSON><PERSON> lightning detector—a primitive radio receiver. In some parts of the former Soviet Union the anniversary of this day is celebrated as Radio Day.", "html": "1895 - In <a href=\"https://wikipedia.org/wiki/Saint_Petersburg\" title=\"Saint Petersburg\">Saint Petersburg</a>, Russian scientist <a href=\"https://wikipedia.org/wiki/<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a> demonstrates to the Russian Physical and Chemical Society his invention, the <PERSON>ov <a href=\"https://wikipedia.org/wiki/Lightning\" title=\"Lightning\">lightning</a> detector—a primitive <a href=\"https://wikipedia.org/wiki/Radio_receiver\" title=\"Radio receiver\">radio receiver</a>. In some parts of the former <a href=\"https://wikipedia.org/wiki/Soviet_Union\" title=\"Soviet Union\">Soviet Union</a> the anniversary of this day is celebrated as <a href=\"https://wikipedia.org/wiki/Radio_Day\" title=\"Radio Day\">Radio Day</a>.", "no_year_html": "In <a href=\"https://wikipedia.org/wiki/Saint_Petersburg\" title=\"Saint Petersburg\">Saint Petersburg</a>, Russian scientist <a href=\"https://wikipedia.org/wiki/<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a> demonstrates to the Russian Physical and Chemical Society his invention, the <PERSON>ov <a href=\"https://wikipedia.org/wiki/Lightning\" title=\"Lightning\">lightning</a> detector—a primitive <a href=\"https://wikipedia.org/wiki/Radio_receiver\" title=\"Radio receiver\">radio receiver</a>. In some parts of the former <a href=\"https://wikipedia.org/wiki/Soviet_Union\" title=\"Soviet Union\">Soviet Union</a> the anniversary of this day is celebrated as <a href=\"https://wikipedia.org/wiki/Radio_Day\" title=\"Radio Day\">Radio Day</a>.", "links": [{"title": "Saint Petersburg", "link": "https://wikipedia.org/wiki/Saint_Petersburg"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "Lightning", "link": "https://wikipedia.org/wiki/Lightning"}, {"title": "Radio receiver", "link": "https://wikipedia.org/wiki/Radio_receiver"}, {"title": "Soviet Union", "link": "https://wikipedia.org/wiki/Soviet_Union"}, {"title": "Radio Day", "link": "https://wikipedia.org/wiki/Radio_Day"}]}, {"year": "1915", "text": "World War I: German submarine U-20 sinks RMS Lusitania, killing 1,199 people, including 128 Americans. Public reaction to the sinking turns many former pro-Germans in the United States against the German Empire.", "html": "1915 - <a href=\"https://wikipedia.org/wiki/World_War_I\" title=\"World War I\">World War I</a>: German <a href=\"https://wikipedia.org/wiki/Submarine\" title=\"Submarine\">submarine</a> <a href=\"https://wikipedia.org/wiki/SM_U-20_(Germany)\" title=\"SM U-20 (Germany)\"><i>U-20</i></a> <a href=\"https://wikipedia.org/wiki/Sinking_of_the_RMS_Lusitania\" title=\"Sinking of the RMS Lusitania\">sinks</a> <a href=\"https://wikipedia.org/wiki/RMS_Lusitania\" title=\"RMS Lusitania\">RMS <i>Lusitania</i></a>, killing 1,199 people, including 128 Americans. Public reaction to the sinking turns many former pro-Germans in the United States against the <a href=\"https://wikipedia.org/wiki/German_Empire\" title=\"German Empire\">German Empire</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/World_War_I\" title=\"World War I\">World War I</a>: German <a href=\"https://wikipedia.org/wiki/Submarine\" title=\"Submarine\">submarine</a> <a href=\"https://wikipedia.org/wiki/SM_U-20_(Germany)\" title=\"SM U-20 (Germany)\"><i>U-20</i></a> <a href=\"https://wikipedia.org/wiki/Sinking_of_the_RMS_Lusitania\" title=\"Sinking of the RMS Lusitania\">sinks</a> <a href=\"https://wikipedia.org/wiki/RMS_Lusitania\" title=\"RMS Lusitania\">RMS <i>Lusitania</i></a>, killing 1,199 people, including 128 Americans. Public reaction to the sinking turns many former pro-Germans in the United States against the <a href=\"https://wikipedia.org/wiki/German_Empire\" title=\"German Empire\">German Empire</a>.", "links": [{"title": "World War I", "link": "https://wikipedia.org/wiki/World_War_I"}, {"title": "Submarine", "link": "https://wikipedia.org/wiki/Submarine"}, {"title": "SM U-20 (Germany)", "link": "https://wikipedia.org/wiki/SM_U-20_(Germany)"}, {"title": "Sinking of the RMS Lusitania", "link": "https://wikipedia.org/wiki/Sinking_of_the_RMS_Lusitania"}, {"title": "RMS Lusitania", "link": "https://wikipedia.org/wiki/RMS_Lusitania"}, {"title": "German Empire", "link": "https://wikipedia.org/wiki/German_Empire"}]}, {"year": "1915", "text": "The Republic of China accedes to 13 of the 21 Demands, extending the Empire of Japan's control over Manchuria and the Chinese economy.", "html": "1915 - The <a href=\"https://wikipedia.org/wiki/Republic_of_China_(1912%E2%80%931949)\" title=\"Republic of China (1912-1949)\">Republic of China</a> accedes to 13 of the <a href=\"https://wikipedia.org/wiki/Twenty-One_Demands#Japanese_ultimatum\" title=\"Twenty-One Demands\">21 Demands</a>, extending the <a href=\"https://wikipedia.org/wiki/Empire_of_Japan\" title=\"Empire of Japan\">Empire of Japan</a><span class=\"nowrap\" style=\"padding-left:0.1em;\">'s</span> control over <a href=\"https://wikipedia.org/wiki/Manchuria\" title=\"Manchuria\">Manchuria</a> and the Chinese economy.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Republic_of_China_(1912%E2%80%931949)\" title=\"Republic of China (1912-1949)\">Republic of China</a> accedes to 13 of the <a href=\"https://wikipedia.org/wiki/Twenty-One_Demands#Japanese_ultimatum\" title=\"Twenty-One Demands\">21 Demands</a>, extending the <a href=\"https://wikipedia.org/wiki/Empire_of_Japan\" title=\"Empire of Japan\">Empire of Japan</a><span class=\"nowrap\" style=\"padding-left:0.1em;\">'s</span> control over <a href=\"https://wikipedia.org/wiki/Manchuria\" title=\"Manchuria\">Manchuria</a> and the Chinese economy.", "links": [{"title": "Republic of China (1912-1949)", "link": "https://wikipedia.org/wiki/Republic_of_China_(1912%E2%80%931949)"}, {"title": "Twenty-One Demands", "link": "https://wikipedia.org/wiki/Twenty-One_Demands#Japanese_ultimatum"}, {"title": "Empire of Japan", "link": "https://wikipedia.org/wiki/Empire_of_Japan"}, {"title": "Manchuria", "link": "https://wikipedia.org/wiki/Manchuria"}]}, {"year": "1920", "text": "Polish-Soviet War: Kyiv offensive: Polish troops led by <PERSON><PERSON><PERSON> and <PERSON> and assisted by a symbolic Ukrainian force capture Kyiv only to be driven out by the Red Army counter-offensive a month later.", "html": "1920 - <a href=\"https://wikipedia.org/wiki/Polish%E2%80%93Soviet_War\" title=\"Polish-Soviet War\">Polish-Soviet War</a>: <a href=\"https://wikipedia.org/wiki/Kiev_offensive_(1920)\" title=\"Kiev offensive (1920)\">Kyiv offensive</a>: <a href=\"https://wikipedia.org/wiki/Second_Polish_Republic\" title=\"Second Polish Republic\">Polish</a> troops led by <a href=\"https://wikipedia.org/wiki/J%C3%B3zef_Pi%C5%82sudski\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a> and <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>-%C5%9Amig%C5%82y\" title=\"<PERSON>\"><PERSON></a> and assisted by a symbolic <a href=\"https://wikipedia.org/wiki/Ukrainian_People%27s_Republic\" title=\"Ukrainian People's Republic\">Ukrainian</a> force capture <a href=\"https://wikipedia.org/wiki/Kyiv\" title=\"Kyiv\">Kyiv</a> only to be driven out by the <a href=\"https://wikipedia.org/wiki/Red_Army\" title=\"Red Army\">Red Army</a> counter-offensive a month later.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Polish%E2%80%93Soviet_War\" title=\"Polish-Soviet War\">Polish-Soviet War</a>: <a href=\"https://wikipedia.org/wiki/Kiev_offensive_(1920)\" title=\"Kiev offensive (1920)\">Kyiv offensive</a>: <a href=\"https://wikipedia.org/wiki/Second_Polish_Republic\" title=\"Second Polish Republic\">Polish</a> troops led by <a href=\"https://wikipedia.org/wiki/J%C3%B3zef_Pi%C5%82sudski\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a> and <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>-%C5%9Amig%C5%82y\" title=\"<PERSON>\"><PERSON></a> and assisted by a symbolic <a href=\"https://wikipedia.org/wiki/Ukrainian_People%27s_Republic\" title=\"Ukrainian People's Republic\">Ukrainian</a> force capture <a href=\"https://wikipedia.org/wiki/Kyiv\" title=\"Kyiv\">Kyiv</a> only to be driven out by the <a href=\"https://wikipedia.org/wiki/Red_Army\" title=\"Red Army\">Red Army</a> counter-offensive a month later.", "links": [{"title": "Polish-Soviet War", "link": "https://wikipedia.org/wiki/Polish%E2%80%93Soviet_War"}, {"title": "Kiev offensive (1920)", "link": "https://wikipedia.org/wiki/Kiev_offensive_(1920)"}, {"title": "Second Polish Republic", "link": "https://wikipedia.org/wiki/Second_Polish_Republic"}, {"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/J%C3%B3zef_Pi%C5%82sudski"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON><PERSON>-%C5%9Amig%C5%82y"}, {"title": "Ukrainian People's Republic", "link": "https://wikipedia.org/wiki/Ukrainian_People%27s_Republic"}, {"title": "Kyiv", "link": "https://wikipedia.org/wiki/Kyiv"}, {"title": "Red Army", "link": "https://wikipedia.org/wiki/Red_Army"}]}, {"year": "1920", "text": "Treaty of Moscow: Soviet Russia recognizes the independence of the Democratic Republic of Georgia only to invade the country six months later.", "html": "1920 - <a href=\"https://wikipedia.org/wiki/Treaty_of_Moscow_(1920)\" title=\"Treaty of Moscow (1920)\">Treaty of Moscow</a>: <a href=\"https://wikipedia.org/wiki/Russian_SFSR\" class=\"mw-redirect\" title=\"Russian SFSR\">Soviet Russia</a> recognizes the independence of the <a href=\"https://wikipedia.org/wiki/Democratic_Republic_of_Georgia\" title=\"Democratic Republic of Georgia\">Democratic Republic of Georgia</a> only to invade the country six months later.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Treaty_of_Moscow_(1920)\" title=\"Treaty of Moscow (1920)\">Treaty of Moscow</a>: <a href=\"https://wikipedia.org/wiki/Russian_SFSR\" class=\"mw-redirect\" title=\"Russian SFSR\">Soviet Russia</a> recognizes the independence of the <a href=\"https://wikipedia.org/wiki/Democratic_Republic_of_Georgia\" title=\"Democratic Republic of Georgia\">Democratic Republic of Georgia</a> only to invade the country six months later.", "links": [{"title": "Treaty of Moscow (1920)", "link": "https://wikipedia.org/wiki/Treaty_of_Moscow_(1920)"}, {"title": "Russian SFSR", "link": "https://wikipedia.org/wiki/Russian_SFSR"}, {"title": "Democratic Republic of Georgia", "link": "https://wikipedia.org/wiki/Democratic_Republic_of_Georgia"}]}, {"year": "1930", "text": "The 7.1 Mw  Salmas earthquake shakes northwestern Iran and southeastern Turkey with a maximum Mercalli intensity of IX (Violent). Up to three-thousand people were killed.", "html": "1930 - The 7.1 M<sub>w</sub>  <a href=\"https://wikipedia.org/wiki/1930_Salmas_earthquake\" title=\"1930 Salmas earthquake\">Salmas earthquake</a> shakes northwestern Iran and southeastern Turkey with a maximum <a href=\"https://wikipedia.org/wiki/Mercalli_intensity_scale\" class=\"mw-redirect\" title=\"Mercalli intensity scale\">Mercalli intensity</a> of IX (<i>Violent</i>). Up to three-thousand people were killed.", "no_year_html": "The 7.1 M<sub>w</sub>  <a href=\"https://wikipedia.org/wiki/1930_Salmas_earthquake\" title=\"1930 Salmas earthquake\">Salmas earthquake</a> shakes northwestern Iran and southeastern Turkey with a maximum <a href=\"https://wikipedia.org/wiki/Mercalli_intensity_scale\" class=\"mw-redirect\" title=\"Mercalli intensity scale\">Mercalli intensity</a> of IX (<i>Violent</i>). Up to three-thousand people were killed.", "links": [{"title": "1930 Salmas earthquake", "link": "https://wikipedia.org/wiki/1930_Salmas_earthquake"}, {"title": "Mercalli intensity scale", "link": "https://wikipedia.org/wiki/Mercalli_intensity_scale"}]}, {"year": "1931", "text": "The stand-off between criminal <PERSON> and 300 members of the New York Police Department takes place in his fifth-floor apartment on West 91st Street, New York City.", "html": "1931 - The stand-off between criminal <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> and 300 members of the <a href=\"https://wikipedia.org/wiki/New_York_Police_Department\" class=\"mw-redirect\" title=\"New York Police Department\">New York Police Department</a> takes place in his fifth-floor apartment on West 91st Street, <a href=\"https://wikipedia.org/wiki/New_York_City\" title=\"New York City\">New York City</a>.", "no_year_html": "The stand-off between criminal <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> and 300 members of the <a href=\"https://wikipedia.org/wiki/New_York_Police_Department\" class=\"mw-redirect\" title=\"New York Police Department\">New York Police Department</a> takes place in his fifth-floor apartment on West 91st Street, <a href=\"https://wikipedia.org/wiki/New_York_City\" title=\"New York City\">New York City</a>.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "New York Police Department", "link": "https://wikipedia.org/wiki/New_York_Police_Department"}, {"title": "New York City", "link": "https://wikipedia.org/wiki/New_York_City"}]}, {"year": "1937", "text": "Spanish Civil War: The German Condor Legion, equipped with Heinkel He 51 biplanes, arrives in Spain to assist <PERSON>'s forces.", "html": "1937 - <a href=\"https://wikipedia.org/wiki/Spanish_Civil_War\" title=\"Spanish Civil War\">Spanish Civil War</a>: The German <a href=\"https://wikipedia.org/wiki/Condor_Legion\" title=\"Condor Legion\">Condor Legion</a>, equipped with <a href=\"https://wikipedia.org/wiki/Heinkel_He_51\" title=\"Heinkel He 51\">Heinkel He 51</a> biplanes, arrives in Spain to assist <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>'s forces.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Spanish_Civil_War\" title=\"Spanish Civil War\">Spanish Civil War</a>: The German <a href=\"https://wikipedia.org/wiki/Condor_Legion\" title=\"Condor Legion\">Condor Legion</a>, equipped with <a href=\"https://wikipedia.org/wiki/Heinkel_He_51\" title=\"Heinkel He 51\">Heinkel He 51</a> biplanes, arrives in Spain to assist <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>'s forces.", "links": [{"title": "Spanish Civil War", "link": "https://wikipedia.org/wiki/Spanish_Civil_War"}, {"title": "Condor Legion", "link": "https://wikipedia.org/wiki/Condor_Legion"}, {"title": "<PERSON><PERSON><PERSON> 51", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_51"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Francisco_Franco"}]}, {"year": "1940", "text": "World War II: The Norway Debate in the British House of Commons begins, and leads to the replacement of Prime Minister <PERSON> with <PERSON> three days later.", "html": "1940 - <a href=\"https://wikipedia.org/wiki/World_War_II\" title=\"World War II\">World War II</a>: The <a href=\"https://wikipedia.org/wiki/Norway_Debate\" title=\"Norway Debate\">Norway Debate</a> in the <a href=\"https://wikipedia.org/wiki/British_House_of_Commons\" class=\"mw-redirect\" title=\"British House of Commons\">British House of Commons</a> begins, and leads to the replacement of Prime Minister <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> with <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> three days later.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/World_War_II\" title=\"World War II\">World War II</a>: The <a href=\"https://wikipedia.org/wiki/Norway_Debate\" title=\"Norway Debate\">Norway Debate</a> in the <a href=\"https://wikipedia.org/wiki/British_House_of_Commons\" class=\"mw-redirect\" title=\"British House of Commons\">British House of Commons</a> begins, and leads to the replacement of Prime Minister <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> with <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> three days later.", "links": [{"title": "World War II", "link": "https://wikipedia.org/wiki/World_War_II"}, {"title": "Norway Debate", "link": "https://wikipedia.org/wiki/Norway_Debate"}, {"title": "British House of Commons", "link": "https://wikipedia.org/wiki/British_House_of_Commons"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>"}]}, {"year": "1942", "text": "World War II: During the Battle of the Coral Sea, United States Navy aircraft carrier aircraft attack and sink the Imperial Japanese Navy light aircraft carrier Shōhō; the battle marks the first time in naval history that two enemy fleets fight without visual contact between warring ships.", "html": "1942 - World War II: During the <a href=\"https://wikipedia.org/wiki/Battle_of_the_Coral_Sea\" title=\"Battle of the Coral Sea\">Battle of the Coral Sea</a>, United States Navy <a href=\"https://wikipedia.org/wiki/Aircraft_carrier\" title=\"Aircraft carrier\">aircraft carrier</a> aircraft attack and sink the <a href=\"https://wikipedia.org/wiki/Imperial_Japanese_Navy\" title=\"Imperial Japanese Navy\">Imperial Japanese Navy</a> <a href=\"https://wikipedia.org/wiki/Light_aircraft_carrier\" title=\"Light aircraft carrier\">light aircraft carrier</a> <a href=\"https://wikipedia.org/wiki/Japanese_aircraft_carrier_Sh%C5%8Dh%C5%8D\" title=\"Japanese aircraft carrier Shōhō\">Shōhō</a>; the battle marks the first time in naval history that two enemy fleets fight without visual contact between warring ships.", "no_year_html": "World War II: During the <a href=\"https://wikipedia.org/wiki/Battle_of_the_Coral_Sea\" title=\"Battle of the Coral Sea\">Battle of the Coral Sea</a>, United States Navy <a href=\"https://wikipedia.org/wiki/Aircraft_carrier\" title=\"Aircraft carrier\">aircraft carrier</a> aircraft attack and sink the <a href=\"https://wikipedia.org/wiki/Imperial_Japanese_Navy\" title=\"Imperial Japanese Navy\">Imperial Japanese Navy</a> <a href=\"https://wikipedia.org/wiki/Light_aircraft_carrier\" title=\"Light aircraft carrier\">light aircraft carrier</a> <a href=\"https://wikipedia.org/wiki/Japanese_aircraft_carrier_Sh%C5%8Dh%C5%8D\" title=\"Japanese aircraft carrier Shōhō\">Shōhō</a>; the battle marks the first time in naval history that two enemy fleets fight without visual contact between warring ships.", "links": [{"title": "Battle of the Coral Sea", "link": "https://wikipedia.org/wiki/Battle_of_the_Coral_Sea"}, {"title": "Aircraft carrier", "link": "https://wikipedia.org/wiki/Aircraft_carrier"}, {"title": "Imperial Japanese Navy", "link": "https://wikipedia.org/wiki/Imperial_Japanese_Navy"}, {"title": "Light aircraft carrier", "link": "https://wikipedia.org/wiki/Light_aircraft_carrier"}, {"title": "Japanese aircraft carrier Shōhō", "link": "https://wikipedia.org/wiki/Japanese_aircraft_carrier_Sh%C5%8Dh%C5%8D"}]}, {"year": "1945", "text": "World War II: Last German U-boat attack of the war, two freighters are sunk off the Firth of Forth, Scotland.", "html": "1945 - World War II: Last German <a href=\"https://wikipedia.org/wiki/U-boat\" title=\"U-boat\">U-boat</a> attack of the war, two freighters are sunk off the <a href=\"https://wikipedia.org/wiki/Firth_of_Forth\" title=\"Firth of Forth\">Firth of Forth</a>, Scotland.", "no_year_html": "World War II: Last German <a href=\"https://wikipedia.org/wiki/U-boat\" title=\"U-boat\">U-boat</a> attack of the war, two freighters are sunk off the <a href=\"https://wikipedia.org/wiki/Firth_of_Forth\" title=\"Firth of Forth\">Firth of Forth</a>, Scotland.", "links": [{"title": "U-boat", "link": "https://wikipedia.org/wiki/U-boat"}, {"title": "Firth of Forth", "link": "https://wikipedia.org/wiki/Firth_of_Forth"}]}, {"year": "1945", "text": "World War II: <PERSON><PERSON><PERSON><PERSON><PERSON> signs unconditional surrender terms at Reims, France, ending Germany's participation in the war. The document takes effect the next day.", "html": "1945 - World War II: <i><a href=\"https://wikipedia.org/wiki/Generalfeldmarschall\" title=\"Generalfeldmarschall\">Generalfeldmarschall</a></i> <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> signs <a href=\"https://wikipedia.org/wiki/German_Instrument_of_Surrender\" title=\"German Instrument of Surrender\">unconditional surrender terms</a> at <a href=\"https://wikipedia.org/wiki/Reims\" title=\"Reims\">Reims</a>, France, ending Germany's participation in the war. The document takes effect the next day.", "no_year_html": "World War II: <i><a href=\"https://wikipedia.org/wiki/Generalfeldmarschall\" title=\"Generalfeldmarschall\">Generalfeldmarschall</a></i> <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> signs <a href=\"https://wikipedia.org/wiki/German_Instrument_of_Surrender\" title=\"German Instrument of Surrender\">unconditional surrender terms</a> at <a href=\"https://wikipedia.org/wiki/Reims\" title=\"Reims\">Reims</a>, France, ending Germany's participation in the war. The document takes effect the next day.", "links": [{"title": "Generalfeldmarschall", "link": "https://wikipedia.org/wiki/Generalfeldmarschall"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>l"}, {"title": "German Instrument of Surrender", "link": "https://wikipedia.org/wiki/German_Instrument_of_Surrender"}, {"title": "Reims", "link": "https://wikipedia.org/wiki/Reims"}]}, {"year": "1946", "text": "Tokyo Telecommunications Engineering (later renamed Sony) is founded.", "html": "1946 - Tokyo Telecommunications Engineering (later renamed <a href=\"https://wikipedia.org/wiki/Sony\" title=\"Sony\">Sony</a>) is founded.", "no_year_html": "Tokyo Telecommunications Engineering (later renamed <a href=\"https://wikipedia.org/wiki/Sony\" title=\"Sony\">Sony</a>) is founded.", "links": [{"title": "Sony", "link": "https://wikipedia.org/wiki/Sony"}]}, {"year": "1948", "text": "The Council of Europe is founded during the Hague Congress.", "html": "1948 - The <a href=\"https://wikipedia.org/wiki/Council_of_Europe\" title=\"Council of Europe\">Council of Europe</a> is founded during the <a href=\"https://wikipedia.org/wiki/Hague_Congress_(1948)\" class=\"mw-redirect\" title=\"Hague Congress (1948)\">Hague Congress</a>.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Council_of_Europe\" title=\"Council of Europe\">Council of Europe</a> is founded during the <a href=\"https://wikipedia.org/wiki/Hague_Congress_(1948)\" class=\"mw-redirect\" title=\"Hague Congress (1948)\">Hague Congress</a>.", "links": [{"title": "Council of Europe", "link": "https://wikipedia.org/wiki/Council_of_Europe"}, {"title": "Hague Congress (1948)", "link": "https://wikipedia.org/wiki/Hague_Congress_(1948)"}]}, {"year": "1952", "text": "The concept of the integrated circuit, the basis for all modern computers, is first published by <PERSON>.", "html": "1952 - The concept of the <a href=\"https://wikipedia.org/wiki/Integrated_circuit\" title=\"Integrated circuit\">integrated circuit</a>, the basis for all modern computers, is first published by <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>.", "no_year_html": "The concept of the <a href=\"https://wikipedia.org/wiki/Integrated_circuit\" title=\"Integrated circuit\">integrated circuit</a>, the basis for all modern computers, is first published by <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>.", "links": [{"title": "Integrated circuit", "link": "https://wikipedia.org/wiki/Integrated_circuit"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1954", "text": "Indochina War: The Battle of Dien Bien Phu ends in a French defeat and a Viet Minh victory (the battle began on March 13).", "html": "1954 - <a href=\"https://wikipedia.org/wiki/Indochina_War\" class=\"mw-redirect\" title=\"Indochina War\">Indochina War</a>: The <a href=\"https://wikipedia.org/wiki/Battle_of_Dien_Bien_Phu\" title=\"Battle of Dien Bien Phu\">Battle of Dien Bien Phu</a> ends in a French defeat and a Viet Minh victory (the battle began on March 13).", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Indochina_War\" class=\"mw-redirect\" title=\"Indochina War\">Indochina War</a>: The <a href=\"https://wikipedia.org/wiki/Battle_of_Dien_Bien_Phu\" title=\"Battle of Dien Bien Phu\">Battle of Dien Bien Phu</a> ends in a French defeat and a Viet Minh victory (the battle began on March 13).", "links": [{"title": "Indochina War", "link": "https://wikipedia.org/wiki/Indochina_War"}, {"title": "Battle of Dien Bien Phu", "link": "https://wikipedia.org/wiki/Battle_of_Dien_Bien_Phu"}]}, {"year": "1960", "text": "Cold War: U-2 Crisis of 1960: Soviet leader <PERSON><PERSON> announces that his nation is holding American U-2 pilot <PERSON>.", "html": "1960 - <a href=\"https://wikipedia.org/wiki/Cold_War\" title=\"Cold War\">Cold War</a>: <a href=\"https://wikipedia.org/wiki/U-2_Crisis_of_1960\" class=\"mw-redirect\" title=\"U-2 Crisis of 1960\">U-2 Crisis of 1960</a>: Soviet leader <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a> announces that his nation is holding American <a href=\"https://wikipedia.org/wiki/Lockheed_U-2\" title=\"Lockheed U-2\">U-2</a> pilot <a href=\"https://wikipedia.org/wiki/<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Cold_War\" title=\"Cold War\">Cold War</a>: <a href=\"https://wikipedia.org/wiki/U-2_Crisis_of_1960\" class=\"mw-redirect\" title=\"U-2 Crisis of 1960\">U-2 Crisis of 1960</a>: Soviet leader <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a> announces that his nation is holding American <a href=\"https://wikipedia.org/wiki/Lockheed_U-2\" title=\"Lockheed U-2\">U-2</a> pilot <a href=\"https://wikipedia.org/wiki/<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>.", "links": [{"title": "Cold War", "link": "https://wikipedia.org/wiki/Cold_War"}, {"title": "U-2 Crisis of 1960", "link": "https://wikipedia.org/wiki/U-2_Crisis_of_1960"}, {"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}, {"title": "Lockheed U-2", "link": "https://wikipedia.org/wiki/Lockheed_U-2"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1964", "text": "Pacific Airlines Flight 773 is hijacked by <PERSON> and crashes in Contra Costa County, California, killing 44.", "html": "1964 - <a href=\"https://wikipedia.org/wiki/Pacific_Air_Lines_Flight_773\" title=\"Pacific Air Lines Flight 773\">Pacific Airlines Flight 773</a> is hijacked by <a href=\"https://wikipedia.org/wiki/Francisco_Gonzales\" title=\"Francisco Gonzales\"><PERSON></a> and crashes in <a href=\"https://wikipedia.org/wiki/Contra_Costa_County,_California\" title=\"Contra Costa County, California\">Contra Costa County, California</a>, killing 44.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Pacific_Air_Lines_Flight_773\" title=\"Pacific Air Lines Flight 773\">Pacific Airlines Flight 773</a> is hijacked by <a href=\"https://wikipedia.org/wiki/Francisco_Gonzales\" title=\"Francisco Gonzales\"><PERSON></a> and crashes in <a href=\"https://wikipedia.org/wiki/Contra_Costa_County,_California\" title=\"Contra Costa County, California\">Contra Costa County, California</a>, killing 44.", "links": [{"title": "Pacific Air Lines Flight 773", "link": "https://wikipedia.org/wiki/Pacific_Air_Lines_Flight_773"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Francisco_<PERSON>les"}, {"title": "Contra Costa County, California", "link": "https://wikipedia.org/wiki/Contra_Costa_County,_California"}]}, {"year": "1986", "text": "Canadian <PERSON> becomes the first person to climb each of the Seven Summits.", "html": "1986 - Canadian <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> becomes the first person to climb each of the <a href=\"https://wikipedia.org/wiki/Seven_Summits\" title=\"Seven Summits\">Seven Summits</a>.", "no_year_html": "Canadian <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> becomes the first person to climb each of the <a href=\"https://wikipedia.org/wiki/Seven_Summits\" title=\"Seven Summits\">Seven Summits</a>.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Seven Summits", "link": "https://wikipedia.org/wiki/Seven_Summits"}]}, {"year": "1991", "text": "A fire and explosion occurs at a fireworks factory at Sungai Buloh, Malaysia, killing 26.", "html": "1991 - A <a href=\"https://wikipedia.org/wiki/Bright_Sparklers_Fireworks_disaster\" class=\"mw-redirect\" title=\"Bright Sparklers Fireworks disaster\">fire and explosion occurs at a fireworks factory</a> at <a href=\"https://wikipedia.org/wiki/Sungai_Buloh\" title=\"Sungai Buloh\">Sungai Buloh</a>, Malaysia, killing 26.", "no_year_html": "A <a href=\"https://wikipedia.org/wiki/Bright_Sparklers_Fireworks_disaster\" class=\"mw-redirect\" title=\"Bright Sparklers Fireworks disaster\">fire and explosion occurs at a fireworks factory</a> at <a href=\"https://wikipedia.org/wiki/Sungai_Buloh\" title=\"Sungai Buloh\">Sungai Buloh</a>, Malaysia, killing 26.", "links": [{"title": "Bright Sparklers Fireworks disaster", "link": "https://wikipedia.org/wiki/Bright_Sparklers_Fireworks_disaster"}, {"title": "Sungai Buloh", "link": "https://wikipedia.org/wiki/Sungai_Buloh"}]}, {"year": "1992", "text": "Michigan ratifies a 203-year-old proposed amendment to the United States Constitution making the 27th Amendment law. This amendment bars the U.S. Congress from giving itself a mid-term pay raise.", "html": "1992 - <a href=\"https://wikipedia.org/wiki/Michigan\" title=\"Michigan\">Michigan</a> ratifies a 203-year-old proposed amendment to the <a href=\"https://wikipedia.org/wiki/United_States_Constitution\" class=\"mw-redirect\" title=\"United States Constitution\">United States Constitution</a> making the <a href=\"https://wikipedia.org/wiki/Twenty-seventh_Amendment_to_the_United_States_Constitution\" title=\"Twenty-seventh Amendment to the United States Constitution\">27th Amendment</a> law. This amendment bars the <a href=\"https://wikipedia.org/wiki/United_States_Congress\" title=\"United States Congress\">U.S. Congress</a> from giving itself a mid-term pay raise.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Michigan\" title=\"Michigan\">Michigan</a> ratifies a 203-year-old proposed amendment to the <a href=\"https://wikipedia.org/wiki/United_States_Constitution\" class=\"mw-redirect\" title=\"United States Constitution\">United States Constitution</a> making the <a href=\"https://wikipedia.org/wiki/Twenty-seventh_Amendment_to_the_United_States_Constitution\" title=\"Twenty-seventh Amendment to the United States Constitution\">27th Amendment</a> law. This amendment bars the <a href=\"https://wikipedia.org/wiki/United_States_Congress\" title=\"United States Congress\">U.S. Congress</a> from giving itself a mid-term pay raise.", "links": [{"title": "Michigan", "link": "https://wikipedia.org/wiki/Michigan"}, {"title": "United States Constitution", "link": "https://wikipedia.org/wiki/United_States_Constitution"}, {"title": "Twenty-seventh Amendment to the United States Constitution", "link": "https://wikipedia.org/wiki/Twenty-seventh_Amendment_to_the_United_States_Constitution"}, {"title": "United States Congress", "link": "https://wikipedia.org/wiki/United_States_Congress"}]}, {"year": "1992", "text": "Space Shuttle program: The Space Shuttle Endeavour is launched on its first mission, STS-49.", "html": "1992 - <a href=\"https://wikipedia.org/wiki/Space_Shuttle_program\" title=\"Space Shuttle program\">Space Shuttle program</a>: The <a href=\"https://wikipedia.org/wiki/Space_Shuttle\" title=\"Space Shuttle\">Space Shuttle</a> <i><a href=\"https://wikipedia.org/wiki/Space_Shuttle_Endeavour\" title=\"Space Shuttle Endeavour\">Endeavour</a></i> is launched on its first mission, <a href=\"https://wikipedia.org/wiki/STS-49\" title=\"STS-49\">STS-49</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Space_Shuttle_program\" title=\"Space Shuttle program\">Space Shuttle program</a>: The <a href=\"https://wikipedia.org/wiki/Space_Shuttle\" title=\"Space Shuttle\">Space Shuttle</a> <i><a href=\"https://wikipedia.org/wiki/Space_Shuttle_Endeavour\" title=\"Space Shuttle Endeavour\">Endeavour</a></i> is launched on its first mission, <a href=\"https://wikipedia.org/wiki/STS-49\" title=\"STS-49\">STS-49</a>.", "links": [{"title": "Space Shuttle program", "link": "https://wikipedia.org/wiki/Space_Shuttle_program"}, {"title": "Space Shuttle", "link": "https://wikipedia.org/wiki/Space_Shuttle"}, {"title": "Space Shuttle Endeavour", "link": "https://wikipedia.org/wiki/Space_Shuttle_Endeavour"}, {"title": "STS-49", "link": "https://wikipedia.org/wiki/STS-49"}]}, {"year": "1992", "text": "Three employees at a McDonald's Restaurant in Sydney, Nova Scotia, Canada, are brutally murdered and a fourth permanently disabled after a botched robbery. It is the first \"fast-food murder\" in Canada.", "html": "1992 - Three employees at a <a href=\"https://wikipedia.org/wiki/McDonald%27s\" title=\"McDonald's\"><PERSON>'s</a> Restaurant in <a href=\"https://wikipedia.org/wiki/Sydney,_Nova_Scotia\" title=\"Sydney, Nova Scotia\">Sydney, Nova Scotia</a>, Canada, are <a href=\"https://wikipedia.org/wiki/Sydney_River_McDonald%27s_murders\" title=\"Sydney River McDonald's murders\">brutally murdered</a> and a fourth permanently disabled after a botched robbery. It is the first \"fast-food murder\" in Canada.", "no_year_html": "Three employees at a <a href=\"https://wikipedia.org/wiki/<PERSON>%27s\" title=\"McDonald's\">McDonald's</a> Restaurant in <a href=\"https://wikipedia.org/wiki/Sydney,_Nova_Scotia\" title=\"Sydney, Nova Scotia\">Sydney, Nova Scotia</a>, Canada, are <a href=\"https://wikipedia.org/wiki/Sydney_River_McDonald%27s_murders\" title=\"Sydney River McDonald's murders\">brutally murdered</a> and a fourth permanently disabled after a botched robbery. It is the first \"fast-food murder\" in Canada.", "links": [{"title": "McDonald's", "link": "https://wikipedia.org/wiki/<PERSON>%27s"}, {"title": "Sydney, Nova Scotia", "link": "https://wikipedia.org/wiki/Sydney,_Nova_Scotia"}, {"title": "Sydney River McDonald's murders", "link": "https://wikipedia.org/wiki/Sydney_River_McDonald%27s_murders"}]}, {"year": "1994", "text": "<PERSON><PERSON>'s painting The Scream is recovered undamaged after being stolen from the National Gallery of Norway in February.", "html": "1994 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>'s painting <i><a href=\"https://wikipedia.org/wiki/The_Scream\" title=\"The Scream\">The Scream</a></i> is recovered undamaged after being stolen from the <a href=\"https://wikipedia.org/wiki/National_Gallery_of_Norway\" class=\"mw-redirect\" title=\"National Gallery of Norway\">National Gallery of Norway</a> in February.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>'s painting <i><a href=\"https://wikipedia.org/wiki/The_Scream\" title=\"The Scream\">The Scream</a></i> is recovered undamaged after being stolen from the <a href=\"https://wikipedia.org/wiki/National_Gallery_of_Norway\" class=\"mw-redirect\" title=\"National Gallery of Norway\">National Gallery of Norway</a> in February.", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}, {"title": "The Scream", "link": "https://wikipedia.org/wiki/The_Scream"}, {"title": "National Gallery of Norway", "link": "https://wikipedia.org/wiki/National_Gallery_of_Norway"}]}, {"year": "1998", "text": "Mercedes-Benz buys Chrysler for US$40 billion and forms DaimlerChrysler in the largest industrial merger in history.", "html": "1998 - <a href=\"https://wikipedia.org/wiki/Mercedes-Benz\" title=\"Mercedes-Benz\">Mercedes-Benz</a> buys <a href=\"https://wikipedia.org/wiki/Chrysler\" title=\"Chrysler\">Chrysler</a> for US$40 billion and forms <a href=\"https://wikipedia.org/wiki/DaimlerChrysler\" class=\"mw-redirect\" title=\"DaimlerChrysler\">DaimlerChrysler</a> in the largest industrial merger in history.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Mercedes-Benz\" title=\"Mercedes-Benz\">Mercedes-Benz</a> buys <a href=\"https://wikipedia.org/wiki/Chrysler\" title=\"Chrysler\">Chrysler</a> for US$40 billion and forms <a href=\"https://wikipedia.org/wiki/DaimlerChrysler\" class=\"mw-redirect\" title=\"DaimlerChrysler\">DaimlerChrysler</a> in the largest industrial merger in history.", "links": [{"title": "Mercedes-Benz", "link": "https://wikipedia.org/wiki/Mercedes-Benz"}, {"title": "Chrysler", "link": "https://wikipedia.org/wiki/Chrysler"}, {"title": "DaimlerChrysler", "link": "https://wikipedia.org/wiki/DaimlerChrysler"}]}, {"year": "1999", "text": "Pope <PERSON> travels to Romania, becoming the first pope to visit a predominantly Eastern Orthodox country since the Great Schism in 1054.", "html": "1999 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>\" title=\"Pope <PERSON>\">Pope <PERSON> II</a> travels to <a href=\"https://wikipedia.org/wiki/Romania\" title=\"Romania\">Romania</a>, becoming the first pope to visit a predominantly <a href=\"https://wikipedia.org/wiki/Eastern_Orthodox_Church\" title=\"Eastern Orthodox Church\">Eastern Orthodox</a> country since the <a href=\"https://wikipedia.org/wiki/East%E2%80%93West_Schism\" title=\"East-West Schism\">Great Schism</a> in 1054.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>\" title=\"Pope <PERSON>\">Pope <PERSON> II</a> travels to <a href=\"https://wikipedia.org/wiki/Romania\" title=\"Romania\">Romania</a>, becoming the first pope to visit a predominantly <a href=\"https://wikipedia.org/wiki/Eastern_Orthodox_Church\" title=\"Eastern Orthodox Church\">Eastern Orthodox</a> country since the <a href=\"https://wikipedia.org/wiki/East%E2%80%93West_Schism\" title=\"East-West Schism\">Great Schism</a> in 1054.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>"}, {"title": "Romania", "link": "https://wikipedia.org/wiki/Romania"}, {"title": "Eastern Orthodox Church", "link": "https://wikipedia.org/wiki/Eastern_Orthodox_Church"}, {"title": "East-West Schism", "link": "https://wikipedia.org/wiki/East%E2%80%93West_Schism"}]}, {"year": "1999", "text": "Kosovo War: Three Chinese citizens are killed and 20 wounded when a NATO aircraft inadvertently bombs the Chinese embassy in Belgrade, Serbia.", "html": "1999 - <a href=\"https://wikipedia.org/wiki/Kosovo_War\" title=\"Kosovo War\">Kosovo War</a>: Three Chinese citizens are killed and 20 wounded when a <a href=\"https://wikipedia.org/wiki/NATO\" title=\"NATO\">NATO</a> aircraft inadvertently <a href=\"https://wikipedia.org/wiki/United_States_bombing_of_the_Chinese_embassy_in_Belgrade\" title=\"United States bombing of the Chinese embassy in Belgrade\">bombs the Chinese embassy</a> in <a href=\"https://wikipedia.org/wiki/Belgrade\" title=\"Belgrade\">Belgrade, Serbia</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Kosovo_War\" title=\"Kosovo War\">Kosovo War</a>: Three Chinese citizens are killed and 20 wounded when a <a href=\"https://wikipedia.org/wiki/NATO\" title=\"NATO\">NATO</a> aircraft inadvertently <a href=\"https://wikipedia.org/wiki/United_States_bombing_of_the_Chinese_embassy_in_Belgrade\" title=\"United States bombing of the Chinese embassy in Belgrade\">bombs the Chinese embassy</a> in <a href=\"https://wikipedia.org/wiki/Belgrade\" title=\"Belgrade\">Belgrade, Serbia</a>.", "links": [{"title": "Kosovo War", "link": "https://wikipedia.org/wiki/Kosovo_War"}, {"title": "NATO", "link": "https://wikipedia.org/wiki/NATO"}, {"title": "United States bombing of the Chinese embassy in Belgrade", "link": "https://wikipedia.org/wiki/United_States_bombing_of_the_Chinese_embassy_in_Belgrade"}, {"title": "Belgrade", "link": "https://wikipedia.org/wiki/Belgrade"}]}, {"year": "1999", "text": "In Guinea-Bissau, President <PERSON> is ousted in a military coup.", "html": "1999 - In <a href=\"https://wikipedia.org/wiki/Guinea-Bissau\" title=\"Guinea-Bissau\">Guinea-Bissau</a>, President <a href=\"https://wikipedia.org/wiki/Jo%C3%A3<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> is ousted in a <a href=\"https://wikipedia.org/wiki/Military_coup\" class=\"mw-redirect\" title=\"Military coup\">military coup</a>.", "no_year_html": "In <a href=\"https://wikipedia.org/wiki/Guinea-Bissau\" title=\"Guinea-Bissau\">Guinea-Bissau</a>, President <a href=\"https://wikipedia.org/wiki/Jo%C3%A3<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> is ousted in a <a href=\"https://wikipedia.org/wiki/Military_coup\" class=\"mw-redirect\" title=\"Military coup\">military coup</a>.", "links": [{"title": "Guinea-Bissau", "link": "https://wikipedia.org/wiki/Guinea-Bissau"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Jo%C3%A3<PERSON>_<PERSON>_<PERSON>"}, {"title": "Military coup", "link": "https://wikipedia.org/wiki/Military_coup"}]}, {"year": "2000", "text": "<PERSON> is inaugurated as president of Russia.", "html": "2000 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> is inaugurated as president of Russia.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> is inaugurated as president of Russia.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2002", "text": "An EgyptAir Boeing 737-500 crashes on approach to Tunis-Carthage International Airport, killing 14 people.", "html": "2002 - An <a href=\"https://wikipedia.org/wiki/EgyptAir_Flight_843\" title=\"EgyptAir Flight 843\">EgyptAir Boeing 737-500</a> crashes on approach to <a href=\"https://wikipedia.org/wiki/Tunis%E2%80%93Carthage_International_Airport\" title=\"Tunis-Carthage International Airport\">Tunis-Carthage International Airport</a>, killing 14 people.", "no_year_html": "An <a href=\"https://wikipedia.org/wiki/EgyptAir_Flight_843\" title=\"EgyptAir Flight 843\">EgyptAir Boeing 737-500</a> crashes on approach to <a href=\"https://wikipedia.org/wiki/Tunis%E2%80%93Carthage_International_Airport\" title=\"Tunis-Carthage International Airport\">Tunis-Carthage International Airport</a>, killing 14 people.", "links": [{"title": "EgyptAir Flight 843", "link": "https://wikipedia.org/wiki/EgyptAir_Flight_843"}, {"title": "Tunis-Carthage International Airport", "link": "https://wikipedia.org/wiki/Tunis%E2%80%93Carthage_International_Airport"}]}, {"year": "2002", "text": "A China Northern Airlines MD-82 plunges into the Yellow Sea, killing 112 people.", "html": "2002 - A <a href=\"https://wikipedia.org/wiki/China_Northern_Airlines_Flight_6136\" title=\"China Northern Airlines Flight 6136\">China Northern Airlines MD-82</a> plunges into the <a href=\"https://wikipedia.org/wiki/Yellow_Sea\" title=\"Yellow Sea\">Yellow Sea</a>, killing 112 people.", "no_year_html": "A <a href=\"https://wikipedia.org/wiki/China_Northern_Airlines_Flight_6136\" title=\"China Northern Airlines Flight 6136\">China Northern Airlines MD-82</a> plunges into the <a href=\"https://wikipedia.org/wiki/Yellow_Sea\" title=\"Yellow Sea\">Yellow Sea</a>, killing 112 people.", "links": [{"title": "China Northern Airlines Flight 6136", "link": "https://wikipedia.org/wiki/China_Northern_Airlines_Flight_6136"}, {"title": "Yellow Sea", "link": "https://wikipedia.org/wiki/Yellow_Sea"}]}, {"year": "2004", "text": "American businessman <PERSON> is beheaded by Islamic militants. The act is recorded on videotape and released on the Internet.", "html": "2004 - American businessman <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a> is <a href=\"https://wikipedia.org/wiki/Decapitation\" title=\"Decapitation\">beheaded</a> by <a href=\"https://wikipedia.org/wiki/Islamic_militants\" class=\"mw-redirect\" title=\"Islamic militants\">Islamic militants</a>. The act is recorded on videotape and released on the Internet.", "no_year_html": "American businessman <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a> is <a href=\"https://wikipedia.org/wiki/Decapitation\" title=\"Decapitation\">beheaded</a> by <a href=\"https://wikipedia.org/wiki/Islamic_militants\" class=\"mw-redirect\" title=\"Islamic militants\">Islamic militants</a>. The act is recorded on videotape and released on the Internet.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Decapitation", "link": "https://wikipedia.org/wiki/Decapitation"}, {"title": "Islamic militants", "link": "https://wikipedia.org/wiki/Islamic_militants"}]}], "Births": [{"year": "Before 160", "text": "<PERSON>, Roman noblewoman (d. 224)", "html": "Before 160 - Before <a href=\"https://wikipedia.org/wiki/160\" title=\"160\">160</a> - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Roman noblewoman (d. 224)", "no_year_html": "Before <a href=\"https://wikipedia.org/wiki/160\" title=\"160\">160</a> - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Roman noblewoman (d. 224)", "links": [{"title": "160", "link": "https://wikipedia.org/wiki/160"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1488", "text": "<PERSON> of the Palatinate, archbishop of Regensburg (d. 1538)", "html": "1488 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_the_Palatinate\" title=\"<PERSON> of the Palatinate\"><PERSON> of the Palatinate</a>, archbishop of Regensburg (d. 1538)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_the_Palatinate\" title=\"<PERSON> of the Palatinate\"><PERSON> of the Palatinate</a>, archbishop of Regensburg (d. 1538)", "links": [{"title": "<PERSON> of the Palatinate", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_the_Palatinate"}]}, {"year": "1530", "text": "<PERSON>, Prince of Condé (d. 1569)", "html": "1530 - <a href=\"https://wikipedia.org/wiki/<PERSON>,_Prince_of_Cond%C3%A9_(1530%E2%80%931569)\" class=\"mw-redirect\" title=\"<PERSON>, Prince of Condé (1530-1569)\"><PERSON>, Prince of Condé</a> (d. 1569)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>,_Prince_of_Cond%C3%A9_(1530%E2%80%931569)\" class=\"mw-redirect\" title=\"<PERSON>, Prince of Condé (1530-1569)\"><PERSON>, Prince of Condé</a> (d. 1569)", "links": [{"title": "<PERSON>, Prince of Condé (1530-1569)", "link": "https://wikipedia.org/wiki/<PERSON>,_Prince_of_Cond%C3%A9_(1530%E2%80%931569)"}]}, {"year": "1553", "text": "<PERSON>, Duke of Prussia (d. 1618)", "html": "1553 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Duke_of_Prussia\" title=\"<PERSON>, Duke of Prussia\"><PERSON>, Duke of Prussia</a> (d. 1618)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Duke_of_Prussia\" title=\"<PERSON>, Duke of Prussia\"><PERSON>, Duke of Prussia</a> (d. 1618)", "links": [{"title": "<PERSON>, Duke of Prussia", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Duke_of_Prussia"}]}, {"year": "1605", "text": "Patriarch <PERSON><PERSON> of Moscow (d. 1681)", "html": "1605 - <a href=\"https://wikipedia.org/wiki/Patriarch_<PERSON><PERSON>_of_Moscow\" title=\"Patriarch Nik<PERSON> of Moscow\">Patriarch <PERSON><PERSON> of Moscow</a> (d. 1681)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Patriarch_<PERSON><PERSON>_of_Moscow\" title=\"Patriarch Nikon of Moscow\">Patriarch <PERSON><PERSON> of Moscow</a> (d. 1681)", "links": [{"title": "Patriarch <PERSON><PERSON> of Moscow", "link": "https://wikipedia.org/wiki/<PERSON>_Nikon_of_Moscow"}]}, {"year": "1643", "text": "<PERSON><PERSON>, American politician, 10th Mayor of New York City (d. 1700)", "html": "1643 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American politician, 10th <a href=\"https://wikipedia.org/wiki/Mayor_of_New_York_City\" title=\"Mayor of New York City\">Mayor of New York City</a> (d. 1700)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American politician, 10th <a href=\"https://wikipedia.org/wiki/Mayor_of_New_York_City\" title=\"Mayor of New York City\">Mayor of New York City</a> (d. 1700)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>"}, {"title": "Mayor of New York City", "link": "https://wikipedia.org/wiki/Mayor_of_New_York_City"}]}, {"year": "1700", "text": "<PERSON>, Dutch-Austrian physician (d. 1772)", "html": "1700 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch-Austrian physician (d. 1772)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch-Austrian physician (d. 1772)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1701", "text": "<PERSON>, German tenor and composer (d. 1759)", "html": "1701 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German tenor and composer (d. 1759)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German tenor and composer (d. 1759)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1711", "text": "<PERSON>, Scottish economist, historian, and philosopher (d. 1776)", "html": "1711 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish economist, historian, and philosopher (d. 1776)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish economist, historian, and philosopher (d. 1776)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1724", "text": "<PERSON><PERSON><PERSON>, French-Austrian field marshal (d. 1797)", "html": "1724 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, French-Austrian field marshal (d. 1797)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, French-Austrian field marshal (d. 1797)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1740", "text": "<PERSON>, Russian police officer and general (d. 1814)", "html": "1740 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian police officer and general (d. 1814)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian police officer and general (d. 1814)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1748", "text": "<PERSON><PERSON><PERSON><PERSON>, French playwright and philosopher (d. 1793)", "html": "1748 - <a href=\"https://wikipedia.org/wiki/Olympe_de_Gouges\" title=\"Olympe de Gouges\"><PERSON><PERSON><PERSON><PERSON> <PERSON> Go<PERSON></a>, French playwright and philosopher (d. 1793)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Olympe_de_Gouges\" title=\"Olympe de Gouges\"><PERSON><PERSON><PERSON><PERSON> <PERSON> Gouges</a>, French playwright and philosopher (d. 1793)", "links": [{"title": "Olympe de Gouges", "link": "https://wikipedia.org/wiki/Olympe_de_Gouges"}]}, {"year": "1751", "text": "<PERSON>, American artisan and military officer (d. 1815)", "html": "1751 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American artisan and military officer (d. 1815)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American artisan and military officer (d. 1815)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1763", "text": "<PERSON><PERSON><PERSON>, Polish general (d. 1813)", "html": "1763 - <a href=\"https://wikipedia.org/wiki/J%C3%B3<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Polish general (d. 1813)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/J%C3%B<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Polish general (d. 1813)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/J%C3%B3<PERSON>_<PERSON>"}]}, {"year": "1767", "text": "Princess <PERSON><PERSON> of Prussia (d. 1820)", "html": "1767 - <a href=\"https://wikipedia.org/wiki/Princess_<PERSON><PERSON>_<PERSON>_of_Prussia\" title=\"Princess <PERSON><PERSON> of Prussia\">Princess <PERSON><PERSON> of Prussia</a> (d. 1820)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Princess_<PERSON><PERSON>_<PERSON>_of_Prussia\" title=\"Princess <PERSON><PERSON> of Prussia\">Princess <PERSON><PERSON> of Prussia</a> (d. 1820)", "links": [{"title": "Princess <PERSON><PERSON> of Prussia", "link": "https://wikipedia.org/wiki/Princess_<PERSON><PERSON>_<PERSON>_<PERSON>_Prussia"}]}, {"year": "1774", "text": "<PERSON>, American commodore (d. 1833)", "html": "1774 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American commodore (d. 1833)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American commodore (d. 1833)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1787", "text": "<PERSON>, Canadian archaeologist and politician, 1st mayor of Montreal (d. 1858)", "html": "1787 - <a href=\"https://wikipedia.org/wiki/<PERSON>_(1787%E2%80%931858)\" class=\"mw-redirect\" title=\"<PERSON> (1787-1858)\"><PERSON></a>, Canadian archaeologist and politician, 1st <a href=\"https://wikipedia.org/wiki/Mayor_of_Montreal\" title=\"Mayor of Montreal\">mayor of Montreal</a> (d. 1858)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(1787%E2%80%931858)\" class=\"mw-redirect\" title=\"<PERSON> (1787-1858)\"><PERSON></a>, Canadian archaeologist and politician, 1st <a href=\"https://wikipedia.org/wiki/Mayor_of_Montreal\" title=\"Mayor of Montreal\">mayor of Montreal</a> (d. 1858)", "links": [{"title": "<PERSON> (1787-1858)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(1787%E2%80%931858)"}, {"title": "Mayor of Montreal", "link": "https://wikipedia.org/wiki/Mayor_of_Montreal"}]}, {"year": "1812", "text": "<PERSON>, English poet and playwright (d. 1889)", "html": "1812 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English poet and playwright (d. 1889)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English poet and playwright (d. 1889)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1833", "text": "<PERSON>, German pianist and composer (d. 1897)", "html": "1833 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German pianist and composer (d. 1897)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German pianist and composer (d. 1897)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1836", "text": "<PERSON>, American lawyer and politician, 40th Speaker of the United States House of Representatives (d. 1926)", "html": "1836 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and politician, 40th <a href=\"https://wikipedia.org/wiki/Speaker_of_the_United_States_House_of_Representatives\" title=\"Speaker of the United States House of Representatives\">Speaker of the United States House of Representatives</a> (d. 1926)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and politician, 40th <a href=\"https://wikipedia.org/wiki/Speaker_of_the_United_States_House_of_Representatives\" title=\"Speaker of the United States House of Representatives\">Speaker of the United States House of Representatives</a> (d. 1926)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "Speaker of the United States House of Representatives", "link": "https://wikipedia.org/wiki/Speaker_of_the_United_States_House_of_Representatives"}]}, {"year": "1837", "text": "<PERSON>, German geographer and explorer (d. 1875)", "html": "1837 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German geographer and explorer (d. 1875)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German geographer and explorer (d. 1875)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1840", "text": "<PERSON><PERSON><PERSON>, Russian composer and educator (d. 1893)", "html": "1840 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Russian composer and educator (d. 1893)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Russian composer and educator (d. 1893)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON><PERSON><PERSON>_<PERSON><PERSON>"}]}, {"year": "1845", "text": "<PERSON>, American nurse and activist (d. 1926)", "html": "1845 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American nurse and activist (d. 1926)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American nurse and activist (d. 1926)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1847", "text": "<PERSON>, 5th Earl of Rosebery, English politician, Prime Minister of the United Kingdom (d. 1929)", "html": "1847 - <a href=\"https://wikipedia.org/wiki/<PERSON>,_5th_Earl_<PERSON>_Rose<PERSON>\" title=\"<PERSON>, 5th Earl of Rosebery\"><PERSON>, 5th Earl of Rosebery</a>, English politician, <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_the_United_Kingdom\" title=\"Prime Minister of the United Kingdom\">Prime Minister of the United Kingdom</a> (d. 1929)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>,_5th_Earl_<PERSON>_Rose<PERSON>\" title=\"<PERSON>, 5th Earl of Rosebery\"><PERSON>, 5th Earl of Rosebery</a>, English politician, <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_the_United_Kingdom\" title=\"Prime Minister of the United Kingdom\">Prime Minister of the United Kingdom</a> (d. 1929)", "links": [{"title": "<PERSON>, 5th Earl of Rosebery", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_5th_Earl_of_<PERSON>"}, {"title": "Prime Minister of the United Kingdom", "link": "https://wikipedia.org/wiki/Prime_Minister_of_the_United_Kingdom"}]}, {"year": "1857", "text": "<PERSON>, American lawyer and politician, 9th Governor of West Virginia (d. 1930)", "html": "1857 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and politician, 9th <a href=\"https://wikipedia.org/wiki/Governor_of_West_Virginia\" class=\"mw-redirect\" title=\"Governor of West Virginia\">Governor of West Virginia</a> (d. 1930)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and politician, 9th <a href=\"https://wikipedia.org/wiki/Governor_of_West_Virginia\" class=\"mw-redirect\" title=\"Governor of West Virginia\">Governor of West Virginia</a> (d. 1930)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>"}, {"title": "Governor of West Virginia", "link": "https://wikipedia.org/wiki/Governor_of_West_Virginia"}]}, {"year": "1860", "text": "<PERSON>, English businessman (d. 1930)", "html": "1860 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English businessman (d. 1930)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English businessman (d. 1930)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1861", "text": "<PERSON><PERSON><PERSON><PERSON>, Indian author and poet, Nobel Prize laureate (d. 1941)", "html": "1861 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_Tagore\" title=\"<PERSON><PERSON><PERSON>nath Tagore\"><PERSON><PERSON><PERSON><PERSON></a>, Indian author and poet, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Literature\" title=\"Nobel Prize in Literature\">Nobel Prize</a> laureate (d. 1941)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>nath Tagore\"><PERSON><PERSON><PERSON><PERSON></a>, Indian author and poet, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Literature\" title=\"Nobel Prize in Literature\">Nobel Prize</a> laureate (d. 1941)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>"}, {"title": "Nobel Prize in Literature", "link": "https://wikipedia.org/wiki/Nobel_Prize_in_Literature"}]}, {"year": "1867", "text": "<PERSON><PERSON><PERSON><PERSON>, Polish novelist, Nobel Prize laureate (d. 1925)", "html": "1867 - <a href=\"https://wikipedia.org/wiki/W%C5%82adys%C5%82aw_Reymont\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Polish novelist, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Literature\" title=\"Nobel Prize in Literature\">Nobel Prize</a> laureate (d. 1925)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/W%C5%82adys%C5%82aw_Reymont\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Polish novelist, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Literature\" title=\"Nobel Prize in Literature\">Nobel Prize</a> laureate (d. 1925)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/W%C5%82adys%C5%82aw_Reymont"}, {"title": "Nobel Prize in Literature", "link": "https://wikipedia.org/wiki/Nobel_Prize_in_Literature"}]}, {"year": "1875", "text": "<PERSON>, American pole vaulter (d. 1951)", "html": "1875 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(athlete)\" title=\"<PERSON> (athlete)\"><PERSON></a>, American pole vaulter (d. 1951)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(athlete)\" title=\"<PERSON> (athlete)\"><PERSON></a>, American pole vaulter (d. 1951)", "links": [{"title": "<PERSON> (athlete)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(athlete)"}]}, {"year": "1880", "text": "<PERSON><PERSON><PERSON>, Indologist and Sanskrit scholar, Bhara<PERSON> awardee (d. 1972)", "html": "1880 - <a href=\"https://wikipedia.org/wiki/<PERSON>du<PERSON>_<PERSON>aman_Kane\" title=\"Pandurang Vaman Kane\"><PERSON><PERSON><PERSON></a>, Indologist and Sanskrit scholar, <a href=\"https://wikipedia.org/wiki/B<PERSON><PERSON>_Ratna\" title=\"Bhara<PERSON> Ratna\"><PERSON><PERSON><PERSON></a> awardee (d. 1972)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>aman_<PERSON>\" title=\"Pandurang Vaman Kane\"><PERSON><PERSON><PERSON></a>, Indologist and Sanskrit scholar, <a href=\"https://wikipedia.org/wiki/Bharat_Ratna\" title=\"Bhara<PERSON> Ratna\"><PERSON><PERSON><PERSON></a> awardee (d. 1972)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>"}, {"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>t_<PERSON>na"}]}, {"year": "1881", "text": "<PERSON>, American cyclist (d. 1954)", "html": "1881 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American cyclist (d. 1954)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American cyclist (d. 1954)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "1882", "text": "<PERSON>, Belgian author and poet (d. 1960)", "html": "1882 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Belgian author and poet (d. 1960)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Belgian author and poet (d. 1960)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Willem_Elsschot"}]}, {"year": "1885", "text": "<PERSON> \"<PERSON><PERSON>\" <PERSON>, American actor (d. 1969)", "html": "1885 - <a href=\"https://wikipedia.org/wiki/<PERSON>_%22Gabby%22_<PERSON>\" title='<PERSON> \"<PERSON><PERSON>\" <PERSON>'><PERSON> \"<PERSON><PERSON>\" <PERSON></a>, American actor (d. 1969)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_%22G<PERSON>%22_<PERSON>\" title='<PERSON> \"<PERSON><PERSON>\" <PERSON>'><PERSON> \"<PERSON><PERSON>\" <PERSON></a>, American actor (d. 1969)", "links": [{"title": "<PERSON> \"<PERSON><PERSON>\" <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_%22Gabby%22_<PERSON>"}]}, {"year": "1889", "text": "<PERSON>, Estonian colonel (d. 1943)", "html": "1889 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Estonian colonel (d. 1943)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Estonian colonel (d. 1943)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1891", "text": "<PERSON>, Scottish engineer and activist (d. 1988)", "html": "1891 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish engineer and activist (d. 1988)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish engineer and activist (d. 1988)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1892", "text": "<PERSON>, American poet, playwright, and lawyer (d. 1982)", "html": "1892 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American poet, playwright, and lawyer (d. 1982)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American poet, playwright, and lawyer (d. 1982)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1892", "text": "<PERSON><PERSON><PERSON>, Yugoslav field marshal and politician, 1st President of Yugoslavia (d. 1980)", "html": "1892 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Yugoslav field marshal and politician, 1st <a href=\"https://wikipedia.org/wiki/President_of_Yugoslavia\" title=\"President of Yugoslavia\">President of Yugoslavia</a> (d. 1980)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Yugoslav field marshal and politician, 1st <a href=\"https://wikipedia.org/wiki/President_of_Yugoslavia\" title=\"President of Yugoslavia\">President of Yugoslavia</a> (d. 1980)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON><PERSON><PERSON>_<PERSON>"}, {"title": "President of Yugoslavia", "link": "https://wikipedia.org/wiki/President_of_Yugoslavia"}]}, {"year": "1893", "text": "<PERSON>, Canadian ice hockey coach and manager (d. 1985)", "html": "1893 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey coach and manager (d. 1985)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey coach and manager (d. 1985)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1896", "text": "<PERSON>, English tennis and badminton player (d. 1992)", "html": "1896 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English tennis and badminton player (d. 1992)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON> Godfree\"><PERSON></a>, English tennis and badminton player (d. 1992)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "1899", "text": "<PERSON>, English sculptor and academic (d. 1998)", "html": "1899 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, English sculptor and academic (d. 1998)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, English sculptor and academic (d. 1998)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1901", "text": "<PERSON>, American actor (d. 1961)", "html": "1901 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor (d. 1961)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor (d. 1961)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1903", "text": "<PERSON><PERSON>, Russian-Soviet poet and translator (d. 1958)", "html": "1903 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Russian-Soviet poet and translator (d. 1958)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Russian-Soviet poet and translator (d. 1958)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1905", "text": "<PERSON>, Welsh-Australian chemical engineer (d. 1989)", "html": "1905 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Welsh-Australian chemical engineer (d. 1989)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Welsh-Australian chemical engineer (d. 1989)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1909", "text": "<PERSON>, American scientist and inventor, co-founded the Polaroid Corporation (d. 1991)", "html": "1909 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"Edwin <PERSON>\"><PERSON></a>, American scientist and inventor, co-founded the <a href=\"https://wikipedia.org/wiki/Polaroid_Corporation\" title=\"Polaroid Corporation\">Polaroid Corporation</a> (d. 1991)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"Edwin <PERSON>\"><PERSON></a>, American scientist and inventor, co-founded the <a href=\"https://wikipedia.org/wiki/Polaroid_Corporation\" title=\"Polaroid Corporation\">Polaroid Corporation</a> (d. 1991)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "Polaroid Corporation", "link": "https://wikipedia.org/wiki/Polaroid_Corporation"}]}, {"year": "1909", "text": "<PERSON>, Native American teacher (d. 2005)", "html": "1909 - <a href=\"https://wikipedia.org/wiki/<PERSON>_Sunrise_Lorentino\" title=\"Dorothy Sunrise Lorentino\"><PERSON></a>, Native American teacher (d. 2005)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_Sunrise_Lorentino\" title=\"Dorothy Sunrise Lorentino\"><PERSON></a>, Native American teacher (d. 2005)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Dorothy_Sunrise_Lorentino"}]}, {"year": "1911", "text": "<PERSON><PERSON><PERSON>, Japanese director, producer, and screenwriter (d. 1993)", "html": "1911 - <a href=\"https://wikipedia.org/wiki/Ishir%C5%8D_Honda\" title=\"Ishir<PERSON> Honda\"><PERSON><PERSON><PERSON></a>, Japanese director, producer, and screenwriter (d. 1993)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Ishir%C5%8D_Honda\" title=\"Ishirō Honda\"><PERSON><PERSON><PERSON></a>, Japanese director, producer, and screenwriter (d. 1993)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Ishir%C5%8D_Honda"}]}, {"year": "1913", "text": "<PERSON>, American physicist and engineer (d. 2016)", "html": "1913 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American physicist and engineer (d. 2016)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American physicist and engineer (d. 2016)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1917", "text": "<PERSON>, Italian cardinal and composer (d. 2013)", "html": "1917 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian cardinal and composer (d. 2013)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian cardinal and composer (d. 2013)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1917", "text": "<PERSON><PERSON>, Australian public servant (d. 2020)", "html": "1917 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Australian public servant (d. 2020)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Australian public servant (d. 2020)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1917", "text": "<PERSON>, English actor (d. 2000)", "html": "1917 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(actor)\" class=\"mw-redirect\" title=\"<PERSON> (actor)\"><PERSON></a>, English actor (d. 2000)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(actor)\" class=\"mw-redirect\" title=\"<PERSON> (actor)\"><PERSON></a>, English actor (d. 2000)", "links": [{"title": "<PERSON> (actor)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(actor)"}]}, {"year": "1919", "text": "<PERSON>, Argentinian actress, 25th First Lady of Argentina (d. 1952)", "html": "1919 - <a href=\"https://wikipedia.org/wiki/Eva_Per%C3%B3n\" title=\"<PERSON>\"><PERSON></a>, Argentinian actress, 25th <a href=\"https://wikipedia.org/wiki/First_Lady_of_Argentina\" class=\"mw-redirect\" title=\"First Lady of Argentina\">First Lady of Argentina</a> (d. 1952)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_Per%C3%B3n\" title=\"<PERSON>\"><PERSON></a>, Argentinian actress, 25th <a href=\"https://wikipedia.org/wiki/First_Lady_of_Argentina\" class=\"mw-redirect\" title=\"First Lady of Argentina\">First Lady of Argentina</a> (d. 1952)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Eva_Per%C3%B3n"}, {"title": "First Lady of Argentina", "link": "https://wikipedia.org/wiki/First_Lady_of_Argentina"}]}, {"year": "1920", "text": "<PERSON><PERSON>, Indonesian actor (d. 1985)", "html": "1920 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Indonesian actor (d. 1985)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Indonesian actor (d. 1985)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Ren<PERSON>_<PERSON>"}]}, {"year": "1922", "text": "<PERSON>, American actor and director (d. 2006)", "html": "1922 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor and director (d. 2006)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor and director (d. 2006)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1923", "text": "<PERSON>, American actress (d. 1985)", "html": "1923 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress (d. 1985)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress (d. 1985)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1927", "text": "<PERSON>, German-American author and screenwriter (d. 2013)", "html": "1927 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German-American author and screenwriter (d. 2013)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German-American author and screenwriter (d. 2013)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1929", "text": "<PERSON>, American baseball player, coach, and manager (d. 2011)", "html": "1929 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player, coach, and manager (d. 2011)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player, coach, and manager (d. 2011)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1930", "text": "<PERSON>, American football player and coach (d. 2017)", "html": "1930 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player and coach (d. 2017)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player and coach (d. 2017)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1931", "text": "<PERSON>, American singer (d. 2007)", "html": "1931 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer (d. 2007)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer (d. 2007)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1931", "text": "<PERSON>, American author (d. 2019)", "html": "1931 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author (d. 2019)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author (d. 2019)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1932", "text": "<PERSON>, American lawyer and politician, 37th Mayor of Albuquerque (d. 2017)", "html": "1932 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and politician, 37th <a href=\"https://wikipedia.org/wiki/Mayor_of_Albuquerque\" class=\"mw-redirect\" title=\"Mayor of Albuquerque\">Mayor of Albuquerque</a> (d. 2017)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and politician, 37th <a href=\"https://wikipedia.org/wiki/Mayor_of_Albuquerque\" class=\"mw-redirect\" title=\"Mayor of Albuquerque\">Mayor of Albuquerque</a> (d. 2017)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Pete_Dome<PERSON>i"}, {"title": "Mayor of Albuquerque", "link": "https://wikipedia.org/wiki/Mayor_of_Albuquerque"}]}, {"year": "1932", "text": "<PERSON>, English journalist and author  (d. 1997)", "html": "1932 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English journalist and author (d. 1997)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English journalist and author (d. 1997)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1933", "text": "<PERSON>, American football player and sportscaster (d. 2002)", "html": "1933 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player and sportscaster (d. 2002)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player and sportscaster (d. 2002)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1935", "text": "<PERSON>, English architect (d. 2023)", "html": "1935 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(architect)\" title=\"<PERSON> (architect)\"><PERSON></a>, English architect (d. 2023)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(architect)\" title=\"<PERSON> (architect)\"><PERSON></a>, English architect (d. 2023)", "links": [{"title": "<PERSON> (architect)", "link": "https://wikipedia.org/wiki/<PERSON>_(architect)"}]}, {"year": "1936", "text": "<PERSON>, Irish rugby player and businessman (d. 2024)", "html": "1936 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%27Reilly\" title=\"<PERSON>\"><PERSON></a>, Irish rugby player and businessman (d. 2024)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%27Reilly\" title=\"<PERSON>\"><PERSON></a>, Irish rugby player and businessman (d. 2024)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>%27Reilly"}]}, {"year": "1939", "text": "<PERSON>, Canadian-American biologist and academic, Nobel Prize laureate (d. 2022)", "html": "1939 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian-American biologist and academic, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Chemistry\" title=\"Nobel Prize in Chemistry\">Nobel Prize</a> laureate (d. 2022)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian-American biologist and academic, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Chemistry\" title=\"Nobel Prize in Chemistry\">Nobel Prize</a> laureate (d. 2022)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Nobel Prize in Chemistry", "link": "https://wikipedia.org/wiki/Nobel_Prize_in_Chemistry"}]}, {"year": "1939", "text": "<PERSON><PERSON><PERSON><PERSON>, Italian actor, director, and screenwriter (d. 2022)", "html": "1939 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Italian actor, director, and screenwriter (d. 2022)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Italian actor, director, and screenwriter (d. 2022)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Rugger<PERSON>_<PERSON>to"}]}, {"year": "1939", "text": "<PERSON><PERSON><PERSON>, Dutch economist and politician, Prime Minister of the Netherlands (d. 2018)", "html": "1939 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Dutch economist and politician, <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_the_Netherlands\" title=\"Prime Minister of the Netherlands\">Prime Minister of the Netherlands</a> (d. 2018)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Dutch economist and politician, <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_the_Netherlands\" title=\"Prime Minister of the Netherlands\">Prime Minister of the Netherlands</a> (d. 2018)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Ru<PERSON>_Lubbers"}, {"title": "Prime Minister of the Netherlands", "link": "https://wikipedia.org/wiki/Prime_Minister_of_the_Netherlands"}]}, {"year": "1939", "text": "<PERSON>, American pop/doo-wop singer (d. 2010)", "html": "1939 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Mae<PERSON>\"><PERSON></a>, American pop/doo-wop singer (d. 2010)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Maestro\"><PERSON></a>, American pop/doo-wop singer (d. 2010)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1940", "text": "<PERSON>, English novelist and short story writer  (d. 1992)", "html": "1940 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English novelist and short story writer (d. 1992)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English novelist and short story writer (d. 1992)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1943", "text": "<PERSON>, American singer and painter", "html": "1943 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(artist)\" title=\"<PERSON> (artist)\"><PERSON></a>, American singer and painter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(artist)\" title=\"<PERSON> (artist)\"><PERSON></a>, American singer and painter", "links": [{"title": "<PERSON> (artist)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(artist)"}]}, {"year": "1943", "text": "<PERSON>, Australian academic and politician, 39th Premier of South Australia (d. 2015)", "html": "1943 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian academic and politician, 39th <a href=\"https://wikipedia.org/wiki/Premier_of_South_Australia\" title=\"Premier of South Australia\">Premier of South Australia</a> (d. 2015)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian academic and politician, 39th <a href=\"https://wikipedia.org/wiki/Premier_of_South_Australia\" title=\"Premier of South Australia\">Premier of South Australia</a> (d. 2015)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Premier of South Australia", "link": "https://wikipedia.org/wiki/Premier_of_South_Australia"}]}, {"year": "1943", "text": "<PERSON>, Australian novelist and short story writer", "html": "1943 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(novelist)\" title=\"<PERSON> (novelist)\"><PERSON></a>, Australian novelist and short story writer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(novelist)\" title=\"<PERSON> (novelist)\"><PERSON></a>, Australian novelist and short story writer", "links": [{"title": "<PERSON> (novelist)", "link": "https://wikipedia.org/wiki/<PERSON>_(novelist)"}]}, {"year": "1945", "text": "<PERSON>, Irish singer-songwriter and guitarist", "html": "1945 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Irish singer-songwriter and guitarist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Irish singer-songwriter and guitarist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1945", "text": "<PERSON>, American actress", "html": "1945 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>er"}]}, {"year": "1946", "text": "<PERSON><PERSON>, American R&B/disco singer and actress", "html": "1946 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"Thelma Houston\"><PERSON><PERSON></a>, American R&amp;B/disco singer and actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"Thelma Houston\"><PERSON><PERSON></a>, American R&amp;B/disco singer and actress", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>lma_Houston"}]}, {"year": "1946", "text": "<PERSON><PERSON>, American football player (d. 2015)", "html": "1946 - <a href=\"https://wikipedia.org/wiki/<PERSON>v_<PERSON>\" title=\"<PERSON><PERSON> <PERSON>\"><PERSON><PERSON></a>, American football player (d. 2015)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> <PERSON>\"><PERSON><PERSON></a>, American football player (d. 2015)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Marv_<PERSON>"}]}, {"year": "1946", "text": "<PERSON>, American drummer", "html": "1946 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American drummer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American drummer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1946", "text": "<PERSON>, English author and poet", "html": "1946 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English author and poet", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English author and poet", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1950", "text": "<PERSON>, Australian lawyer, sports administrator and businessman", "html": "1950 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(sports_administrator)\" title=\"<PERSON> (sports administrator)\"><PERSON></a>, Australian lawyer, sports administrator and businessman", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(sports_administrator)\" title=\"<PERSON> (sports administrator)\"><PERSON></a>, Australian lawyer, sports administrator and businessman", "links": [{"title": "<PERSON> (sports administrator)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(sports_administrator)"}]}, {"year": "1950", "text": "<PERSON>, American television journalist and lawyer (d. 2008)", "html": "1950 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American television journalist and lawyer (d. 2008)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American television journalist and lawyer (d. 2008)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1954", "text": "<PERSON>, American director, producer, and screenwriter", "html": "1954 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American director, producer, and screenwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American director, producer, and screenwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1956", "text": "<PERSON>, Dutch jurist and politician, Prime Minister of the Netherlands", "html": "1956 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\">Jan <PERSON></a>, Dutch jurist and politician, <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_the_Netherlands\" title=\"Prime Minister of the Netherlands\">Prime Minister of the Netherlands</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch jurist and politician, <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_the_Netherlands\" title=\"Prime Minister of the Netherlands\">Prime Minister of the Netherlands</a>", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Prime Minister of the Netherlands", "link": "https://wikipedia.org/wiki/Prime_Minister_of_the_Netherlands"}]}, {"year": "1956", "text": "<PERSON>, English pianist and composer", "html": "1956 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English pianist and composer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English pianist and composer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1956", "text": "<PERSON>, English director and producer", "html": "1956 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English director and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English director and producer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1956", "text": "<PERSON>, Canadian talk show host and politician (d. 2016)", "html": "1956 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian talk show host and politician (d. 2016)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian talk show host and politician (d. 2016)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1958", "text": "<PERSON>, English nurse and academic", "html": "1958 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English nurse and academic", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English nurse and academic", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1958", "text": "<PERSON>, American politician", "html": "1958 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American politician", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1960", "text": "<PERSON>, Baron <PERSON> of Denham, Iraqi-English surgeon and academic", "html": "1960 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Baron_<PERSON>_of_Denham\" title=\"<PERSON>, Baron <PERSON> of Denham\"><PERSON>, Baron <PERSON> of Denham</a>, Iraqi-English surgeon and academic", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Baron_<PERSON>_of_Denham\" title=\"<PERSON>, Baron <PERSON> of Denham\"><PERSON>, Baron <PERSON> of Denham</a>, Iraqi-English surgeon and academic", "links": [{"title": "<PERSON>, Baron <PERSON> of Denham", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Baron_<PERSON>_of_Denham"}]}, {"year": "1960", "text": "<PERSON><PERSON><PERSON><PERSON>, Spanish author (d. 2021)", "html": "1960 - <a href=\"https://wikipedia.org/wiki/Almudena_Grandes\" title=\"Almudena Grandes\">Almudena Grandes</a>, Spanish author (d. 2021)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Almudena_Grandes\" title=\"Almudena Grandes\">Almuden<PERSON> Grandes</a>, Spanish author (d. 2021)", "links": [{"title": "Almudena Grandes", "link": "https://wikipedia.org/wiki/Almudena_Grandes"}]}, {"year": "1961", "text": "<PERSON>, Scottish anthropologist and academic", "html": "1961 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(forensic_anthropologist)\" class=\"mw-redirect\" title=\"<PERSON> (forensic anthropologist)\"><PERSON></a>, Scottish anthropologist and academic", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(forensic_anthropologist)\" class=\"mw-redirect\" title=\"<PERSON> (forensic anthropologist)\"><PERSON></a>, Scottish anthropologist and academic", "links": [{"title": "<PERSON> (forensic anthropologist)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(forensic_anthropologist)"}]}, {"year": "1965", "text": "<PERSON>, Canadian wrestler (d. 1999)", "html": "1965 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Hart\"><PERSON></a>, Canadian wrestler (d. 1999)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Owen Hart\"><PERSON></a>, Canadian wrestler (d. 1999)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1965", "text": "<PERSON>, Northern Irish footballer and manager", "html": "1965 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Norman <PERSON>\"><PERSON></a>, Northern Irish footballer and manager", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Norman Whiteside\"><PERSON></a>, Northern Irish footballer and manager", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1967", "text": "<PERSON>, Belgian politician", "html": "1967 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%27<PERSON><PERSON><PERSON>_(politician)\" title=\"<PERSON> (politician)\"><PERSON></a>, Belgian politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%27<PERSON><PERSON><PERSON>_(politician)\" title=\"<PERSON> (politician)\"><PERSON></a>, Belgian politician", "links": [{"title": "<PERSON> (politician)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>%27<PERSON><PERSON><PERSON>_(politician)"}]}, {"year": "1967", "text": "<PERSON>, Australian mass murderer", "html": "1967 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian mass murderer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian mass murderer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1967", "text": "<PERSON>, American colonel and politician", "html": "1967 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American colonel and politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American colonel and politician", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1968", "text": "<PERSON><PERSON><PERSON>, American actress and singer", "html": "1968 - <a href=\"https://wikipedia.org/wiki/Trac<PERSON>_<PERSON>\" title=\"T<PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American actress and singer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Trac<PERSON>_<PERSON>\" title=\"T<PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American actress and singer", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/T<PERSON><PERSON>_<PERSON>"}]}, {"year": "1968", "text": "<PERSON>, Canadian lawyer and politician, 30th Canadian Minister of Transport", "html": "1968 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian lawyer and politician, 30th <a href=\"https://wikipedia.org/wiki/Minister_of_Transport_(Canada)\" title=\"Minister of Transport (Canada)\">Canadian Minister of Transport</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian lawyer and politician, 30th <a href=\"https://wikipedia.org/wiki/Minister_of_Transport_(Canada)\" title=\"Minister of Transport (Canada)\">Canadian Minister of Transport</a>", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Minister of Transport (Canada)", "link": "https://wikipedia.org/wiki/Minister_of_Transport_(Canada)"}]}, {"year": "1969", "text": "<PERSON><PERSON><PERSON>, Swedish singer-songwriter", "html": "1969 - <a href=\"https://wikipedia.org/wiki/<PERSON>-Eye_Cherry\" title=\"Eagle-Eye Cherry\"><PERSON><PERSON><PERSON></a>, Swedish singer-songwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Eagle-Eye_Cherry\" title=\"Eagle-Eye Cherry\"><PERSON>-<PERSON> Cherry</a>, Swedish singer-songwriter", "links": [{"title": "Eagle-Eye Cherry", "link": "https://wikipedia.org/wiki/Eagle-Eye_Cherry"}]}, {"year": "1971", "text": "<PERSON>, French economist", "html": "1971 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French economist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French economist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1972", "text": "<PERSON>, American mixed martial artist and wrestler", "html": "1972 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American mixed martial artist and wrestler", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American mixed martial artist and wrestler", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1974", "text": "<PERSON><PERSON><PERSON>, American actor, writer, and producer", "html": "1974 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American actor, writer, and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American actor, writer, and producer", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1976", "text": "<PERSON>, American basketball player and executive", "html": "1976 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player and executive", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player and executive", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1976", "text": "<PERSON>, New Zealand rugby league player", "html": "1976 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, New Zealand rugby league player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, New Zealand rugby league player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1976", "text": "<PERSON>, American lieutenant, Medal of Honor recipient (d. 2005)", "html": "1976 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lieutenant, <a href=\"https://wikipedia.org/wiki/Medal_of_Honor\" title=\"Medal of Honor\">Medal of Honor</a> recipient (d. 2005)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lieutenant, <a href=\"https://wikipedia.org/wiki/Medal_of_Honor\" title=\"Medal of Honor\">Medal of Honor</a> recipient (d. 2005)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "Medal of Honor", "link": "https://wikipedia.org/wiki/Medal_of_Honor"}]}, {"year": "1976", "text": "<PERSON><PERSON>, former Israeli Minister of Justice", "html": "1976 - <a href=\"https://wikipedia.org/wiki/Ayelet_Shaked\" title=\"Ayelet Shaked\"><PERSON><PERSON> Shaked</a>, former Israeli Minister of Justice", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Aye<PERSON>_Shaked\" title=\"Ayelet Shaked\"><PERSON><PERSON> Shaked</a>, former Israeli Minister of Justice", "links": [{"title": "<PERSON><PERSON> Shaked", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_Shaked"}]}, {"year": "1978", "text": "<PERSON>, American basketball player", "html": "1978 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1979", "text": "<PERSON>, American basketball player", "html": "1979 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(basketball)\" title=\"<PERSON> (basketball)\"><PERSON></a>, American basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(basketball)\" title=\"<PERSON> (basketball)\"><PERSON></a>, American basketball player", "links": [{"title": "<PERSON> (basketball)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(basketball)"}]}, {"year": "1984", "text": "<PERSON>, American baseball player", "html": "1984 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1984", "text": "<PERSON>, Canadian wrestler", "html": "1984 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian wrestler", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian wrestler", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1984", "text": "<PERSON>, American football player", "html": "1984 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1985", "text": "<PERSON>, Colombian singer-songwriter and producer", "html": "1985 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>\" title=\"<PERSON>lvin\"><PERSON></a>, Colombian singer-songwriter and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>\" title=\"<PERSON> <PERSON><PERSON>\"><PERSON></a>, Colombian singer-songwriter and producer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1986", "text": "<PERSON>, English drummer", "html": "1986 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English drummer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English drummer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1987", "text": "<PERSON><PERSON>, American actress and comedian", "html": "1987 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American actress and comedian", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American actress and comedian", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1987", "text": "<PERSON>, Scottish footballer", "html": "1987 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(footballer,_born_1987)\" title=\"<PERSON> (footballer, born 1987)\"><PERSON></a>, Scottish footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(footballer,_born_1987)\" title=\"<PERSON> (footballer, born 1987)\"><PERSON></a>, Scottish footballer", "links": [{"title": "<PERSON> (footballer, born 1987)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(footballer,_born_1987)"}]}, {"year": "1989", "text": "<PERSON>, American football player", "html": "1989 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1990", "text": "<PERSON>, Canadian-American footballer", "html": "1990 - <a href=\"https://wikipedia.org/wiki/Sydney_Leroux\" title=\"Sydney Leroux\"><PERSON></a>, Canadian-American footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Sydney_Leroux\" title=\"Sydney Leroux\"><PERSON></a>, Canadian-American footballer", "links": [{"title": "<PERSON>rou<PERSON>", "link": "https://wikipedia.org/wiki/Sydney_Leroux"}]}, {"year": "1992", "text": "<PERSON>, Canadian actor and musician", "html": "1992 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian actor and musician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian actor and musician", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1993", "text": "<PERSON>, English wrestler", "html": "1993 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English wrestler", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English wrestler", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Will_<PERSON>spreay"}]}, {"year": "1993", "text": "<PERSON><PERSON><PERSON>, Australian tennis player", "html": "1993 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Australian tennis player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Australian tennis player", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1995", "text": "<PERSON><PERSON>, Ivorian international footballer", "html": "1995 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Ivorian international footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Ivorian international footballer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>ana"}]}, {"year": "1996", "text": "<PERSON> \"<PERSON><PERSON>\" <PERSON>, South Korean League of Legends gamer", "html": "1996 - <a href=\"https://wikipedia.org/wiki/Faker_(gamer)\" title=\"Faker (gamer)\"><PERSON> \"Faker\" <PERSON>-hyeok</a>, South Korean <i><a href=\"https://wikipedia.org/wiki/League_of_Legends\" title=\"League of Legends\">League of Legends</a></i> gamer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Faker_(gamer)\" title=\"Faker (gamer)\"><PERSON> \"Faker\" <PERSON>-hyeok</a>, South Korean <i><a href=\"https://wikipedia.org/wiki/League_of_Legends\" title=\"League of Legends\">League of Legends</a></i> gamer", "links": [{"title": "<PERSON><PERSON> (gamer)", "link": "https://wikipedia.org/wiki/F<PERSON>_(gamer)"}, {"title": "League of Legends", "link": "https://wikipedia.org/wiki/League_of_Legends"}]}, {"year": "1997", "text": "<PERSON><PERSON>, Russian tennis player", "html": "1997 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Russian tennis player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Russian tennis player", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Dar<PERSON>_<PERSON>a"}]}, {"year": "1997", "text": " <PERSON><PERSON>, Belgian footballer", "html": "1997 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Belgian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Belgian footballer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON><PERSON><PERSON>"}]}, {"year": "1997", "text": "<PERSON>, American golfer", "html": "1997 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Young\"><PERSON></a>, American golfer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Young\"><PERSON></a>, American golfer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1998", "text": "<PERSON>, American YouTuber", "html": "1998 - <a href=\"https://wikipedia.org/wiki/1998\" title=\"1998\">1998</a> - <a href=\"https://wikipedia.org/wiki/MrBeast\" title=\"MrBeas<PERSON>\"><PERSON></a>, American YouTuber", "no_year_html": "<a href=\"https://wikipedia.org/wiki/1998\" title=\"1998\">1998</a> - <a href=\"https://wikipedia.org/wiki/MrBeast\" title=\"MrBeas<PERSON>\"><PERSON></a>, American YouTuber", "links": [{"title": "1998", "link": "https://wikipedia.org/wiki/1998"}, {"title": "Mr<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/MrBeast"}]}, {"year": "1998", "text": "<PERSON>, Spanish footballer", "html": "1998 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Spanish footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Spanish footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1998", "text": "<PERSON>, Finnish ice hockey player", "html": "1998 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A4rvi\" title=\"<PERSON>\"><PERSON></a>, Finnish ice hockey player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A4rvi\" title=\"<PERSON>\"><PERSON></a>, Finnish ice hockey player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>juj%C3%A4rvi"}]}, {"year": "1999", "text": "<PERSON>, Dutch footballer", "html": "1999 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2004", "text": "<PERSON><PERSON>, American tennis player", "html": "2004 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American tennis player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> Krueger\"><PERSON><PERSON></a>, American tennis player", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "2004", "text": "<PERSON><PERSON>, South Korean singer", "html": "2004 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_(singer)\" title=\"<PERSON><PERSON> (singer)\"><PERSON><PERSON></a>, South Korean singer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_(singer)\" title=\"<PERSON><PERSON> (singer)\"><PERSON><PERSON></a>, South Korean singer", "links": [{"title": "<PERSON><PERSON> (singer)", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_(singer)"}]}], "Deaths": [{"year": "721", "text": "<PERSON> of Beverley, bishop of York", "html": "721 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_Be<PERSON>ley\" title=\"<PERSON> of Beverley\"><PERSON> of Beverley</a>, bishop of <a href=\"https://wikipedia.org/wiki/Diocese_of_York\" title=\"Diocese of York\">York</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON> of Beverley\"><PERSON> of Beverley</a>, bishop of <a href=\"https://wikipedia.org/wiki/Diocese_of_York\" title=\"Diocese of York\">York</a>", "links": [{"title": "<PERSON> of Beverley", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "Diocese of York", "link": "https://wikipedia.org/wiki/Diocese_of_York"}]}, {"year": "833", "text": "<PERSON>, Egyptian Muslim historian", "html": "833 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Egyptian Muslim historian", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Egyptian Muslim historian", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "973", "text": "<PERSON>, Holy Roman Emperor (b. 912)", "html": "973 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Holy_Roman_Emperor\" class=\"mw-redirect\" title=\"<PERSON>, Holy Roman Emperor\"><PERSON>, Holy Roman Emperor</a> (b. 912)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Holy_Roman_Emperor\" class=\"mw-redirect\" title=\"<PERSON>, Holy Roman Emperor\"><PERSON>, Holy Roman Emperor</a> (b. 912)", "links": [{"title": "<PERSON>, Holy Roman Emperor", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Holy_Roman_Emperor"}]}, {"year": "1014", "text": "<PERSON><PERSON><PERSON> <PERSON>, 1st King of Georgia (b. 960)", "html": "1014 - <a href=\"https://wikipedia.org/wiki/Bagrat_III_of_Georgia\" title=\"Bagrat III of Georgia\"><PERSON><PERSON><PERSON> <PERSON></a>, 1st King of Georgia (b. 960)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Bagrat_III_of_Georgia\" title=\"Bagrat III of Georgia\"><PERSON><PERSON><PERSON> <PERSON></a>, 1st King of Georgia (b. 960)", "links": [{"title": "Bagrat III of Georgia", "link": "https://wikipedia.org/wiki/Bagrat_III_of_Georgia"}]}, {"year": "1092", "text": "<PERSON><PERSON><PERSON><PERSON>, English monk and bishop", "html": "1092 - <a href=\"https://wikipedia.org/wiki/Remigius_de_F%C3%A9camp\" title=\"Remigi<PERSON> de Fécamp\"><PERSON><PERSON><PERSON><PERSON></a>, English monk and bishop", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Remigius_de_F%C3%A9camp\" title=\"Remigi<PERSON> de Fécamp\"><PERSON><PERSON><PERSON><PERSON></a>, English monk and bishop", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Remigius_de_F%C3%A9camp"}]}, {"year": "1166", "text": "<PERSON> of Sicily", "html": "1166 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Sicily\" title=\"<PERSON> of Sicily\"><PERSON> of Sicily</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Sicily\" title=\"<PERSON> of Sicily\"><PERSON> of Sicily</a>", "links": [{"title": "<PERSON> of Sicily", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Sicily"}]}, {"year": "1202", "text": "<PERSON><PERSON>, Earl of Surrey", "html": "1202 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>,_Earl_of_Surrey\" title=\"<PERSON><PERSON>, Earl of Surrey\"><PERSON><PERSON>, Earl of Surrey</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>,_Earl_of_Surrey\" title=\"<PERSON><PERSON>, Earl of Surrey\"><PERSON><PERSON>, Earl of Surrey</a>", "links": [{"title": "<PERSON><PERSON>, Earl of Surrey", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>,_Earl_of_Surrey"}]}, {"year": "1205", "text": "<PERSON><PERSON><PERSON> of Hungary (b. 1201)", "html": "1205 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_III_of_Hungary\" title=\"<PERSON><PERSON><PERSON> III of Hungary\"><PERSON><PERSON><PERSON> III of Hungary</a> (b. 1201)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_III_of_Hungary\" title=\"La<PERSON>laus III of Hungary\"><PERSON><PERSON><PERSON> III of Hungary</a> (b. 1201)", "links": [{"title": "<PERSON><PERSON><PERSON> of Hungary", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_III_of_Hungary"}]}, {"year": "1234", "text": "<PERSON>, Duke of Merania (b. c. 1180)", "html": "1234 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Duke_of_Merania\" title=\"<PERSON>, Duke of Merania\"><PERSON>, Duke of Merania</a> (b. c. 1180)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Duke_of_Merania\" title=\"<PERSON>, Duke of Merania\"><PERSON>, Duke of Merania</a> (b. c. 1180)", "links": [{"title": "<PERSON>, Duke of Merania", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Duke_of_Merania"}]}, {"year": "1243", "text": "<PERSON>, 5th Earl of Arundel", "html": "1243 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%27<PERSON><PERSON><PERSON>,_5th_Earl_of_Arundel\" title=\"<PERSON>, 5th Earl of Arundel\"><PERSON>, 5th Earl of Arundel</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%27<PERSON><PERSON><PERSON>,_5th_Earl_of_Arundel\" title=\"<PERSON>, 5th Earl of Arundel\"><PERSON>, 5th Earl of Arundel</a>", "links": [{"title": "<PERSON>, 5th Earl of Arundel", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>%27<PERSON><PERSON><PERSON>,_5th_Earl_of_Arundel"}]}, {"year": "1427", "text": "<PERSON>, 5th Baron <PERSON>, English priest (b. 1352)", "html": "1427 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_5th_Baron_<PERSON>_<PERSON>\" title=\"<PERSON>, 5th Baron <PERSON>\"><PERSON>, 5th Baron <PERSON></a>, English priest (b. 1352)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_5th_Baron_<PERSON>_<PERSON>\" title=\"<PERSON>, 5th Baron <PERSON>\"><PERSON>, 5th Baron <PERSON></a>, English priest (b. 1352)", "links": [{"title": "<PERSON>, 5th Baron <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>,_5th_Baron_<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1494", "text": "<PERSON><PERSON><PERSON>, Emperor of Ethiopia (b. 1471)", "html": "1494 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, <a href=\"https://wikipedia.org/wiki/Emperor_of_Ethiopia\" title=\"Emperor of Ethiopia\">Emperor of Ethiopia</a> (b. 1471)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, <a href=\"https://wikipedia.org/wiki/Emperor_of_Ethiopia\" title=\"Emperor of Ethiopia\">Emperor of Ethiopia</a> (b. 1471)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/E<PERSON>nder"}, {"title": "Emperor of Ethiopia", "link": "https://wikipedia.org/wiki/Emperor_of_Ethiopia"}]}, {"year": "1523", "text": "<PERSON>, German knight (b. 1481)", "html": "1523 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON> Sickingen\"><PERSON></a>, German knight (b. 1481)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON> Sickingen\"><PERSON></a>, German knight (b. 1481)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1539", "text": "<PERSON><PERSON><PERSON><PERSON>, Italian printer (b. 1466)", "html": "1539 - <a href=\"https://wikipedia.org/wiki/<PERSON>tta<PERSON><PERSON>_Petrucci\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Italian printer (b. 1466)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>tta<PERSON><PERSON>_<PERSON>rucci\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Italian printer (b. 1466)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Ottaviano_Petrucci"}]}, {"year": "1617", "text": "<PERSON>, German astronomer and theologian (b. 1564)", "html": "1617 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German astronomer and theologian (b. 1564)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German astronomer and theologian (b. 1564)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1667", "text": "<PERSON>, German organist and composer (b. 1616)", "html": "1667 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German organist and composer (b. 1616)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German organist and composer (b. 1616)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1682", "text": "<PERSON><PERSON><PERSON> of Russia (b. 1661)", "html": "1682 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_III_of_Russia\" title=\"<PERSON><PERSON><PERSON> III of Russia\"><PERSON><PERSON><PERSON> of Russia</a> (b. 1661)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_III_of_Russia\" title=\"<PERSON><PERSON><PERSON> III of Russia\"><PERSON><PERSON><PERSON> of Russia</a> (b. 1661)", "links": [{"title": "<PERSON><PERSON><PERSON> of Russia", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_III_of_Russia"}]}, {"year": "1685", "text": "<PERSON><PERSON> (b. 1630)", "html": "1685 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a> (b. 1630)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a> (b. 1630)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1718", "text": "<PERSON> Modena (b. 1658)", "html": "1718 - <a href=\"https://wikipedia.org/wiki/<PERSON>_of_Modena\" title=\"<PERSON> of Modena\"><PERSON> of Modena</a> (b. 1658)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_Modena\" title=\"<PERSON> of Modena\"><PERSON> of Modena</a> (b. 1658)", "links": [{"title": "<PERSON> of Modena", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_Mode<PERSON>"}]}, {"year": "1793", "text": "<PERSON>, Italian violinist and composer (b. 1722)", "html": "1793 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian violinist and composer (b. 1722)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian violinist and composer (b. 1722)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1800", "text": "<PERSON><PERSON><PERSON><PERSON>, Italian composer (b. 1728)", "html": "1800 - <a href=\"https://wikipedia.org/wiki/Niccol%C3%B2_Piccinni\" title=\"<PERSON><PERSON><PERSON><PERSON>cci<PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Italian composer (b. 1728)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Niccol%C3%B2_Piccinni\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Italian composer (b. 1728)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Niccol%C3%B2_Piccinni"}]}, {"year": "1805", "text": "<PERSON>, 2nd Earl of Shelburne, Irish-English politician, Prime Minister of Great Britain (b. 1737)", "html": "1805 - <a href=\"https://wikipedia.org/wiki/<PERSON>,_2nd_Earl_of_Shelburne\" title=\"<PERSON>, 2nd Earl of Shelburne\"><PERSON>, 2nd Earl of Shelburne</a>, Irish-English politician, <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Great_Britain\" class=\"mw-redirect\" title=\"Prime Minister of Great Britain\">Prime Minister of Great Britain</a> (b. 1737)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>,_2nd_Earl_of_Shelburne\" title=\"<PERSON>, 2nd Earl of Shelburne\"><PERSON>, 2nd Earl of Shelburne</a>, Irish-English politician, <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Great_Britain\" class=\"mw-redirect\" title=\"Prime Minister of Great Britain\">Prime Minister of Great Britain</a> (b. 1737)", "links": [{"title": "<PERSON>, 2nd Earl of Shelburne", "link": "https://wikipedia.org/wiki/<PERSON>,_2nd_Earl_of_Shelburne"}, {"title": "Prime Minister of Great Britain", "link": "https://wikipedia.org/wiki/Prime_Minister_of_Great_Britain"}]}, {"year": "1815", "text": "<PERSON><PERSON><PERSON>, American colonel and politician, 45th Deputy Governor of Rhode Island (b. 1739)", "html": "1815 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American colonel and politician, 45th <a href=\"https://wikipedia.org/wiki/Deputy_Governor_of_Rhode_Island\" class=\"mw-redirect\" title=\"Deputy Governor of Rhode Island\">Deputy Governor of Rhode Island</a> (b. 1739)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American colonel and politician, 45th <a href=\"https://wikipedia.org/wiki/Deputy_Governor_of_Rhode_Island\" class=\"mw-redirect\" title=\"Deputy Governor of Rhode Island\">Deputy Governor of Rhode Island</a> (b. 1739)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}, {"title": "Deputy Governor of Rhode Island", "link": "https://wikipedia.org/wiki/Deputy_Governor_of_Rhode_Island"}]}, {"year": "1825", "text": "<PERSON>, Italian composer and conductor (b. 1750)", "html": "1825 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian composer and conductor (b. 1750)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian composer and conductor (b. 1750)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1840", "text": "<PERSON><PERSON><PERSON> <PERSON>, German painter and educator (b. 1774)", "html": "1840 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON> <PERSON></a>, German painter and educator (b. 1774)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON> <PERSON></a>, German painter and educator (b. 1774)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1868", "text": "<PERSON>, 1st Baron <PERSON> and <PERSON>, Scottish lawyer and politician, Lord High Chancellor of Great Britain (b. 1778)", "html": "1868 - <a href=\"https://wikipedia.org/wiki/<PERSON>,_1st_Baron_<PERSON>_and_<PERSON><PERSON>\" title=\"<PERSON>, 1st Baron <PERSON> and <PERSON><PERSON>\"><PERSON>, 1st Baron <PERSON> and <PERSON><PERSON></a>, Scottish lawyer and politician, <a href=\"https://wikipedia.org/wiki/Lord_Chancellor\" title=\"Lord Chancellor\">Lord High Chancellor of Great Britain</a> (b. 1778)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>,_1st_Baron_<PERSON>_and_<PERSON><PERSON>\" title=\"<PERSON>, 1st Baron <PERSON> and <PERSON><PERSON>\"><PERSON>, 1st Baron <PERSON> and <PERSON><PERSON></a>, Scottish lawyer and politician, <a href=\"https://wikipedia.org/wiki/Lord_Chancellor\" title=\"Lord Chancellor\">Lord High Chancellor of Great Britain</a> (b. 1778)", "links": [{"title": "<PERSON>, 1st Baron <PERSON> and <PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>,_1st_Baron_<PERSON>_and_<PERSON><PERSON>"}, {"title": "Lord Chancellor", "link": "https://wikipedia.org/wiki/Lord_Chancellor"}]}, {"year": "1872", "text": "<PERSON>, American carpenter and politician, 4th Mayor of Chicago (b. 1805)", "html": "1872 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American carpenter and politician, 4th <a href=\"https://wikipedia.org/wiki/Mayor_of_Chicago\" title=\"Mayor of Chicago\">Mayor of Chicago</a> (b. 1805)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American carpenter and politician, 4th <a href=\"https://wikipedia.org/wiki/Mayor_of_Chicago\" title=\"Mayor of Chicago\">Mayor of Chicago</a> (b. 1805)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Mayor of Chicago", "link": "https://wikipedia.org/wiki/Mayor_of_Chicago"}]}, {"year": "1876", "text": "<PERSON>, American clergyman, historian, and author (b. 1795)", "html": "1876 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>pra<PERSON>\" title=\"<PERSON> Sprague\"><PERSON></a>, American clergyman, historian, and author (b. 1795)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON> Sprague\"><PERSON></a>, American clergyman, historian, and author (b. 1795)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>gue"}]}, {"year": "1887", "text": "<PERSON><PERSON> <PERSON><PERSON> <PERSON><PERSON>, German-American religious leader and theologian (b. 1811)", "html": "1887 - <a href=\"https://wikipedia.org/wiki/C._F<PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"C<PERSON> <PERSON><PERSON> <PERSON><PERSON>\"><PERSON><PERSON> <PERSON><PERSON> <PERSON><PERSON></a>, German-American religious leader and theologian (b. 1811)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/C._F<PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"C<PERSON> <PERSON><PERSON> <PERSON><PERSON>\"><PERSON><PERSON> <PERSON><PERSON> <PERSON><PERSON></a>, German-American religious leader and theologian (b. 1811)", "links": [{"title": "<PERSON>. <PERSON><PERSON> <PERSON><PERSON>", "link": "https://wikipedia.org/wiki/C._<PERSON><PERSON>_<PERSON><PERSON>_<PERSON><PERSON>"}]}, {"year": "1896", "text": "<PERSON><PERSON> <PERSON><PERSON>, American serial killer (b. 1861)", "html": "1896 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> <PERSON><PERSON>\"><PERSON><PERSON> <PERSON><PERSON></a>, American serial killer (b. 1861)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> <PERSON><PERSON>\"><PERSON><PERSON> <PERSON><PERSON></a>, American serial killer (b. 1861)", "links": [{"title": "<PERSON><PERSON> <PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "1902", "text": "<PERSON><PERSON><PERSON>, Italian priest and saint (b. 1818)", "html": "1902 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Italian priest and saint (b. 1818)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Italian priest and saint (b. 1818)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/A<PERSON><PERSON>_<PERSON>i"}]}, {"year": "1917", "text": "<PERSON>, English fighter pilot (b. 1896)", "html": "1917 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Albert Ball\"><PERSON></a>, English fighter pilot (b. 1896)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Albert Ball\"><PERSON></a>, English fighter pilot (b. 1896)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1922", "text": "<PERSON>, German pianist and composer (b. 1857)", "html": "1922 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German pianist and composer (b. 1857)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German pianist and composer (b. 1857)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Max_Wagenknecht"}]}, {"year": "1924", "text": "<PERSON><PERSON>, Indian activist (b. 1897/1898)", "html": "1924 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Indian activist (b. 1897/1898)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Indian activist (b. 1897/1898)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>uri_Sitara<PERSON>_Raju"}]}, {"year": "1925", "text": "<PERSON>, 1st Viscount <PERSON>, English businessman and politician (b. 1851)", "html": "1925 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_1st_Viscount_<PERSON>\" title=\"<PERSON>, 1st Viscount <PERSON>\"><PERSON>, 1st Viscount <PERSON></a>, English businessman and politician (b. 1851)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>,_1st_Viscount_<PERSON>\" title=\"<PERSON>, 1st Viscount <PERSON>\"><PERSON>, 1st Viscount <PERSON></a>, English businessman and politician (b. 1851)", "links": [{"title": "<PERSON>, 1st Viscount <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>,_1st_Viscount_<PERSON>"}]}, {"year": "1937", "text": "<PERSON>, German captain and author (b. 1886)", "html": "1937 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German captain and author (b. 1886)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German captain and author (b. 1886)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>"}]}, {"year": "1938", "text": "<PERSON><PERSON><PERSON>, Romanian politician, former Prime Minister (b. 1881)", "html": "1938 - <a href=\"https://wikipedia.org/wiki/Octavian_Goga\" title=\"Octavian Goga\"><PERSON><PERSON><PERSON></a>, Romanian politician, former Prime Minister (b. 1881)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Octavian_Goga\" title=\"Octavian Goga\"><PERSON><PERSON><PERSON></a>, Romanian politician, former Prime Minister (b. 1881)", "links": [{"title": "Octavian <PERSON>", "link": "https://wikipedia.org/wiki/Octavian_Goga"}]}, {"year": "1940", "text": "<PERSON>, English journalist and politician (b. 1859)", "html": "1940 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English journalist and politician (b. 1859)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English journalist and politician (b. 1859)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1941", "text": "<PERSON>, Scottish-English anthropologist and academic (b. 1854)", "html": "1941 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish-English anthropologist and academic (b. 1854)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish-English anthropologist and academic (b. 1854)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1942", "text": "<PERSON>, Croatian pianist, composer, and conductor (b. 1863)", "html": "1942 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Croatian pianist, composer, and conductor (b. 1863)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Croatian pianist, composer, and conductor (b. 1863)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1943", "text": "<PERSON><PERSON>, Turkish colonel and politician, 2nd Prime Minister of Turkey (b. 1880)", "html": "1943 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Turkish colonel and politician, 2nd <a href=\"https://wikipedia.org/wiki/List_of_Prime_Ministers_of_Turkey\" class=\"mw-redirect\" title=\"List of Prime Ministers of Turkey\">Prime Minister of Turkey</a> (b. 1880)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Turkish colonel and politician, 2nd <a href=\"https://wikipedia.org/wiki/List_of_Prime_Ministers_of_Turkey\" class=\"mw-redirect\" title=\"List of Prime Ministers of Turkey\">Prime Minister of Turkey</a> (b. 1880)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}, {"title": "List of Prime Ministers of Turkey", "link": "https://wikipedia.org/wiki/List_of_Prime_Ministers_of_Turkey"}]}, {"year": "1946", "text": "<PERSON>, Nigerian journalist and politician (b. 1864)", "html": "1946 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Nigerian journalist and politician (b. 1864)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Nigerian journalist and politician (b. 1864)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1951", "text": "<PERSON>, American actor (b. 1889)", "html": "1951 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor (b. 1889)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Baxter\"><PERSON></a>, American actor (b. 1889)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_Baxter"}]}, {"year": "1967", "text": "<PERSON>, American writer and poet (b. 1899)", "html": "1967 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American writer and poet (b. 1899)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American writer and poet (b. 1899)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1958", "text": "<PERSON><PERSON><PERSON>, Estonian organist, composer, and conductor (b. 1880)", "html": "1958 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>%C3%BCdig\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Estonian organist, composer, and conductor (b. 1880)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>%C3%BCdig\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Estonian organist, composer, and conductor (b. 1880)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Mihkel_L%C3%BCdig"}]}, {"year": "1976", "text": "<PERSON>, English children's book writer (b. 1884)", "html": "1976 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English children's book writer (b. 1884)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English children's book writer (b. 1884)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1978", "text": "<PERSON><PERSON>, American journalist and author (b. 1915)", "html": "1978 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American journalist and author (b. 1915)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American journalist and author (b. 1915)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1986", "text": "<PERSON><PERSON><PERSON>, Turkish playwright and author (b. 1915)", "html": "1986 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Turkish playwright and author (b. 1915)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Turkish playwright and author (b. 1915)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1987", "text": "<PERSON>, Northern Irish actor (b. 1930)", "html": "1987 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Northern Irish actor (b. 1930)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Northern Irish actor (b. 1930)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1987", "text": "<PERSON>, American soldier and activist, co-founded Gay Men's Health Crisis (b. 1941)", "html": "1987 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American soldier and activist, co-founded <a href=\"https://wikipedia.org/wiki/Gay_Men%27s_Health_Crisis\" class=\"mw-redirect\" title=\"Gay Men's Health Crisis\">Gay Men's Health Crisis</a> (b. 1941)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American soldier and activist, co-founded <a href=\"https://wikipedia.org/wiki/Gay_Men%27s_Health_Crisis\" class=\"mw-redirect\" title=\"Gay Men's Health Crisis\">Gay Men's Health Crisis</a> (b. 1941)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Gay Men's Health Crisis", "link": "https://wikipedia.org/wiki/Gay_Men%27s_Health_Crisis"}]}, {"year": "1990", "text": "<PERSON>, Sri Lankan lawyer and politician (b. 1932)", "html": "1990 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Sri Lankan lawyer and politician (b. 1932)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Sri Lankan lawyer and politician (b. 1932)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Sam_<PERSON>u"}]}, {"year": "1994", "text": "<PERSON>, American art critic (b. 1909)", "html": "1994 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American art critic (b. 1909)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American art critic (b. 1909)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1995", "text": "<PERSON>, American drummer, singer, and bandleader (Glenn Miller Orchestra) (b. 1910)", "html": "1995 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American drummer, singer, and bandleader (<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"Glenn <PERSON> Orchestra\"><PERSON></a>) (b. 1910)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American drummer, singer, and bandleader (<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Glenn <PERSON> Orchestra\">Glenn <PERSON> Orchestra</a>) (b. 1910)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1998", "text": "<PERSON>, South African-English physicist and academic, Nobel Prize laureate (b. 1924)", "html": "1998 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, South African-English physicist and academic, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Physiology_or_Medicine\" title=\"Nobel Prize in Physiology or Medicine\">Nobel Prize</a> laureate (b. 1924)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, South African-English physicist and academic, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Physiology_or_Medicine\" title=\"Nobel Prize in Physiology or Medicine\">Nobel Prize</a> laureate (b. 1924)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "Nobel Prize in Physiology or Medicine", "link": "https://wikipedia.org/wiki/Nobel_Prize_in_Physiology_or_Medicine"}]}, {"year": "1998", "text": "<PERSON>, American singer-songwriter and guitarist (b. 1941)", "html": "1998 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and guitarist (b. 1941)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and guitarist (b. 1941)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2000", "text": "<PERSON>, Jr., American captain, actor, and producer (b. 1909)", "html": "2000 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Jr.\" class=\"mw-redirect\" title=\"<PERSON>, Jr.\"><PERSON>, Jr.</a>, American captain, actor, and producer (b. 1909)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Jr.\" class=\"mw-redirect\" title=\"<PERSON>, Jr.\"><PERSON>, Jr.</a>, American captain, actor, and producer (b. 1909)", "links": [{"title": "<PERSON>, Jr.", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Jr."}]}, {"year": "2001", "text": "<PERSON>, French author and politician (b. 1912)", "html": "2001 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French author and politician (b. 1912)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French author and politician (b. 1912)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>-Bus<PERSON>"}]}, {"year": "2004", "text": "<PERSON><PERSON><PERSON><PERSON>, Polish journalist (b. 1956)", "html": "2004 - <a href=\"https://wikipedia.org/wiki/W<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Polish journalist (b. 1956)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/W<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Polish journalist (b. 1956)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Wald<PERSON><PERSON>_<PERSON>"}]}, {"year": "2005", "text": "<PERSON>, American author and activist (b. 1971)", "html": "2005 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author and activist (b. 1971)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author and activist (b. 1971)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Tristan_<PERSON>f"}]}, {"year": "2005", "text": "<PERSON>, American captain and politician (b. 1909)", "html": "2005 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American captain and politician (b. 1909)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American captain and politician (b. 1909)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "2005", "text": "<PERSON><PERSON><PERSON>, Ecuadorian footballer (b. 1980)", "html": "2005 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Ecuadorian footballer (b. 1980)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Ecuadorian footballer (b. 1980)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "2006", "text": "<PERSON>, Australian journalist (b. 1943)", "html": "2006 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian journalist (b. 1943)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian journalist (b. 1943)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2006", "text": "<PERSON>, American singer and philanthropist (b. 1918)", "html": "2006 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer and philanthropist (b. 1918)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer and philanthropist (b. 1918)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "2007", "text": "<PERSON>, English magazine editor (b. 1958)", "html": "2007 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Blow\"><PERSON></a>, English magazine editor (b. 1958)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Blow\"><PERSON></a>, English magazine editor (b. 1958)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2007", "text": "<PERSON>, American boxer (b. 1977)", "html": "2007 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American boxer (b. 1977)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American boxer (b. 1977)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Diego_<PERSON>rrales"}]}, {"year": "2007", "text": "<PERSON><PERSON><PERSON>, Romanian journalist and politician (b. 1926)", "html": "2007 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON><PERSON>\" title=\"Octavian Pa<PERSON>\"><PERSON><PERSON><PERSON></a>, Romanian journalist and politician (b. 1926)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON><PERSON>\" title=\"Octavia<PERSON> Pa<PERSON>\"><PERSON><PERSON><PERSON></a>, Romanian journalist and politician (b. 1926)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>avia<PERSON>_<PERSON><PERSON>"}]}, {"year": "2007", "text": "<PERSON><PERSON><PERSON><PERSON>, American cult leader, founded the Nation of Yahweh (b. 1935)", "html": "2007 - <a href=\"https://wikipedia.org/wiki/Ya<PERSON><PERSON><PERSON>_ben_<PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON> ben <PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON> ben <PERSON></a>, American cult leader, founded the <a href=\"https://wikipedia.org/wiki/Nation_of_Yahweh\" title=\"Nation of Yahweh\">Nation of Yahweh</a> (b. 1935)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Ya<PERSON><PERSON><PERSON>_ben_<PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON> ben <PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON> ben <PERSON></a>, American cult leader, founded the <a href=\"https://wikipedia.org/wiki/Nation_of_Yahweh\" title=\"Nation of Yahweh\">Nation of Yahweh</a> (b. 1935)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON> ben <PERSON>", "link": "https://wikipedia.org/wiki/Ya<PERSON><PERSON><PERSON>_<PERSON>_<PERSON><PERSON><PERSON>h"}, {"title": "Nation of Yahweh", "link": "https://wikipedia.org/wiki/Nation_of_Yahweh"}]}, {"year": "2009", "text": "<PERSON>, English designer (b. 1930)", "html": "2009 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(designer)\" title=\"<PERSON> (designer)\"><PERSON></a>, English designer (b. 1930)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(designer)\" title=\"<PERSON> (designer)\"><PERSON></a>, English designer (b. 1930)", "links": [{"title": "<PERSON> (designer)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(designer)"}]}, {"year": "2009", "text": "<PERSON>, American baseball player, coach, and manager (b. 1923)", "html": "2009 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player, coach, and manager (b. 1923)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player, coach, and manager (b. 1923)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2011", "text": "<PERSON><PERSON>, Spanish golfer (b. 1957)", "html": "2011 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Spanish golfer (b. 1957)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Spanish golfer (b. 1957)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Se<PERSON>_<PERSON>"}]}, {"year": "2011", "text": "<PERSON>, Canadian physicist and academic, Nobel Prize laureate (b. 1924)", "html": "2011 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian physicist and academic, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Physics\" title=\"Nobel Prize in Physics\">Nobel Prize</a> laureate (b. 1924)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian physicist and academic, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Physics\" title=\"Nobel Prize in Physics\">Nobel Prize</a> laureate (b. 1924)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Nobel Prize in Physics", "link": "https://wikipedia.org/wiki/Nobel_Prize_in_Physics"}]}, {"year": "2011", "text": "<PERSON>, English songwriter, producer, and radio host (b. 1957)", "html": "2011 - <a href=\"https://wikipedia.org/wiki/<PERSON>_George\" title=\"<PERSON> George\"><PERSON></a>, English songwriter, producer, and radio host (b. 1957)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_George\" title=\"<PERSON> George\"><PERSON></a>, English songwriter, producer, and radio host (b. 1957)", "links": [{"title": "<PERSON> George", "link": "https://wikipedia.org/wiki/<PERSON>_George"}]}, {"year": "2011", "text": "<PERSON>, Soviet historian (b. 1929)", "html": "2011 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Soviet historian (b. 1929)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Soviet historian (b. 1929)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2012", "text": "<PERSON>, Scottish trade union leader (b. 1931)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish trade union leader (b. 1931)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish trade union leader (b. 1931)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2012", "text": "<PERSON><PERSON><PERSON>, Hungarian economist and politician (b. 1943)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Hungarian economist and politician (b. 1943)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Hungarian economist and politician (b. 1943)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>ren<PERSON>_<PERSON>"}]}, {"year": "2012", "text": "<PERSON>, American captain and pilot (b. 1942)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>tch\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, American captain and pilot (b. 1942)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>tch\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, American captain and pilot (b. 1942)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>tch"}]}, {"year": "2013", "text": "<PERSON><PERSON><PERSON><PERSON>, Italian footballer and manager (b. 1948)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON> Ma<PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Italian footballer and manager (b. 1948)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Italian footballer and manager (b. 1948)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "2013", "text": "<PERSON>, Jr., American football player (b. 1943)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Jr.\" class=\"mw-redirect\" title=\"<PERSON>, Jr.\"><PERSON>, Jr.</a>, American football player (b. 1943)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Jr.\" class=\"mw-redirect\" title=\"<PERSON>, Jr.\"><PERSON>, Jr.</a>, American football player (b. 1943)", "links": [{"title": "<PERSON>, Jr.", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Jr."}]}, {"year": "2014", "text": "<PERSON>, Australian air marshal (b. 1923)", "html": "2014 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian air marshal (b. 1923)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian air marshal (b. 1923)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2014", "text": "<PERSON>, English astronomer, chemist, and academic (b. 1943)", "html": "2014 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English astronomer, chemist, and academic (b. 1943)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English astronomer, chemist, and academic (b. 1943)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2014", "text": "<PERSON>, American baseball player (b. 1927)", "html": "2014 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player (b. 1927)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player (b. 1927)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2015", "text": "<PERSON>, American businessman (b. 1956)", "html": "2015 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American businessman (b. 1956)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American businessman (b. 1956)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2015", "text": "<PERSON>, Australian-American author and illustrator (b. 1929)", "html": "2015 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(cartoonist)\" title=\"<PERSON> (cartoonist)\"><PERSON></a>, Australian-American author and illustrator (b. 1929)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>(cartoonist)\" title=\"<PERSON> (cartoonist)\"><PERSON></a>, Australian-American author and illustrator (b. 1929)", "links": [{"title": "<PERSON> (cartoonist)", "link": "https://wikipedia.org/wiki/<PERSON>(cartoonist)"}]}, {"year": "2023", "text": "<PERSON><PERSON>, Norwegian writer (b. 1930)", "html": "2023 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Norwegian writer (b. 1930)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Norwegian writer (b. 1930)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "2024", "text": "<PERSON>, American musician, record producer, audio engineer, and music journalist (b. 1962)", "html": "2024 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American musician, record producer, audio engineer, and music journalist (b. 1962)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American musician, record producer, audio engineer, and music journalist (b. 1962)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}]}}