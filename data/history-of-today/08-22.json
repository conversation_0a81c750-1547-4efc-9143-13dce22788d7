{"date": "August 22", "url": "https://wikipedia.org/wiki/August_22", "data": {"Events": [{"year": "392", "text": "<PERSON><PERSON><PERSON><PERSON> has <PERSON><PERSON><PERSON> elected Western Roman Emperor.", "html": "392 - <a href=\"https://wikipedia.org/wiki/Arbogast_(magister_militum)\" title=\"Arbogast (magister militum)\"><PERSON><PERSON><PERSON><PERSON></a> has <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a> elected <a href=\"https://wikipedia.org/wiki/Roman_emperor\" title=\"Roman emperor\">Western Roman Emperor</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Arbogast_(magister_militum)\" title=\"Arbogast (magister militum)\"><PERSON><PERSON><PERSON><PERSON></a> has <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a> elected <a href=\"https://wikipedia.org/wiki/Roman_emperor\" title=\"Roman emperor\">Western Roman Emperor</a>.", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON> (magister militum)", "link": "https://wikipedia.org/wiki/Arbogast_(magister_militum)"}, {"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/E<PERSON><PERSON>"}, {"title": "Roman emperor", "link": "https://wikipedia.org/wiki/Roman_emperor"}]}, {"year": "851", "text": "Battle of Jengland: <PERSON><PERSON><PERSON><PERSON> defeats <PERSON> the Bald near the Breton town of Jengland.", "html": "851 - <a href=\"https://wikipedia.org/wiki/Battle_of_Jengland\" title=\"Battle of Jengland\">Battle of Jengland</a>: <a href=\"https://wikipedia.org/wiki/E<PERSON>poe\" title=\"<PERSON>rispoe\"><PERSON><PERSON><PERSON><PERSON></a> defeats <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_Ba<PERSON>\" title=\"<PERSON> the Bald\"><PERSON> the Bald</a> near the Breton town of Jengland.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Battle_of_Jengland\" title=\"Battle of Jengland\">Battle of Jengland</a>: <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>poe\" title=\"<PERSON><PERSON>poe\"><PERSON><PERSON><PERSON><PERSON></a> defeats <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_Ba<PERSON>\" title=\"<PERSON> the Bald\"><PERSON> the Bald</a> near the Breton town of Jengland.", "links": [{"title": "Battle of Jengland", "link": "https://wikipedia.org/wiki/Battle_of_Jengland"}, {"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>e"}, {"title": "<PERSON> the <PERSON>ld", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1138", "text": "Battle of the Standard between Scotland and England.", "html": "1138 - <a href=\"https://wikipedia.org/wiki/Battle_of_the_Standard\" title=\"Battle of the Standard\">Battle of the Standard</a> between Scotland and England.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Battle_of_the_Standard\" title=\"Battle of the Standard\">Battle of the Standard</a> between Scotland and England.", "links": [{"title": "Battle of the Standard", "link": "https://wikipedia.org/wiki/Battle_of_the_Standard"}]}, {"year": "1153", "text": "Crusader-Fatimid wars: The fortress of Ascalon was surrendered by Fatimid Egypt to an army of crusaders, Templars, and Hospitallers led by King <PERSON> of Jerusalem.", "html": "1153 - <a href=\"https://wikipedia.org/wiki/Crusader%E2%80%93Fatimid_wars\" class=\"mw-redirect\" title=\"Crusader-Fatimid wars\">Crusader-Fatimid wars</a>: The fortress of <a href=\"https://wikipedia.org/wiki/Ascalon\" title=\"Ascalon\">Ascalon</a> was <a href=\"https://wikipedia.org/wiki/Siege_of_Ascalon\" title=\"Siege of Ascalon\">surrendered</a> by <a href=\"https://wikipedia.org/wiki/Fatimid_Caliphate\" title=\"Fatimid Caliphate\">Fatimid Egypt</a> to an army of <a href=\"https://wikipedia.org/wiki/Kingdom_of_Jerusalem\" title=\"Kingdom of Jerusalem\">crusaders</a>, <a href=\"https://wikipedia.org/wiki/Knights_Templar\" title=\"Knights Templar\">Templars</a>, and <a href=\"https://wikipedia.org/wiki/Knights_Hospitaller\" title=\"Knights Hospitaller\">Hospitallers</a> led by King <a href=\"https://wikipedia.org/wiki/<PERSON>_III_of_Jerusalem\" title=\"<PERSON> III of Jerusalem\"><PERSON> III of Jerusalem</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Crusader%E2%80%93Fatimid_wars\" class=\"mw-redirect\" title=\"Crusader-Fatimid wars\">Crusader-Fatimid wars</a>: The fortress of <a href=\"https://wikipedia.org/wiki/Ascalon\" title=\"Ascalon\">Ascalon</a> was <a href=\"https://wikipedia.org/wiki/Siege_of_Ascalon\" title=\"Siege of Ascalon\">surrendered</a> by <a href=\"https://wikipedia.org/wiki/Fatimid_Caliphate\" title=\"Fatimid Caliphate\">Fatimid Egypt</a> to an army of <a href=\"https://wikipedia.org/wiki/Kingdom_of_Jerusalem\" title=\"Kingdom of Jerusalem\">crusaders</a>, <a href=\"https://wikipedia.org/wiki/Knights_Templar\" title=\"Knights Templar\">Templars</a>, and <a href=\"https://wikipedia.org/wiki/Knights_Hospitaller\" title=\"Knights Hospitaller\">Hospitallers</a> led by King <a href=\"https://wikipedia.org/wiki/<PERSON>_III_of_Jerusalem\" title=\"<PERSON> III of Jerusalem\"><PERSON> III of Jerusalem</a>.", "links": [{"title": "Crusader-Fatimid wars", "link": "https://wikipedia.org/wiki/Crusader%E2%80%93Fatimid_wars"}, {"title": "Ascalon", "link": "https://wikipedia.org/wiki/Ascalon"}, {"title": "Siege of Ascalon", "link": "https://wikipedia.org/wiki/Siege_of_Ascalon"}, {"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Fatimid_Caliphate"}, {"title": "Kingdom of Jerusalem", "link": "https://wikipedia.org/wiki/Kingdom_of_Jerusalem"}, {"title": "Knights Templar", "link": "https://wikipedia.org/wiki/Knights_Templar"}, {"title": "Knights Hospitaller", "link": "https://wikipedia.org/wiki/Knights_Hospitaller"}, {"title": "<PERSON> III of Jerusalem", "link": "https://wikipedia.org/wiki/Baldwin_III_of_Jerusalem"}]}, {"year": "1485", "text": "The Battle of Bosworth Field occurs; King <PERSON> III of England's death in battle marks the end of the reigning Plantagenet dynasty and the beginning of the Tudors under <PERSON>.", "html": "1485 - The <a href=\"https://wikipedia.org/wiki/Battle_of_Bosworth_Field\" title=\"Battle of Bosworth Field\">Battle of Bosworth Field</a> occurs; King <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_England\" title=\"<PERSON> of England\"><PERSON> of England</a>'s death in battle marks the end of the reigning <a href=\"https://wikipedia.org/wiki/House_of_Plantagenet\" title=\"House of Plantagenet\">Plantagenet dynasty</a> and the beginning of the Tudors under <a href=\"https://wikipedia.org/wiki/<PERSON>_VII_of_England\" title=\"<PERSON> VII of England\"><PERSON> VII</a>.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Battle_of_Bosworth_Field\" title=\"Battle of Bosworth Field\">Battle of Bosworth Field</a> occurs; King <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_England\" title=\"<PERSON> of England\"><PERSON> of England</a>'s death in battle marks the end of the reigning <a href=\"https://wikipedia.org/wiki/House_of_Plantagenet\" title=\"House of Plantagenet\">Plantagenet dynasty</a> and the beginning of the Tudors under <a href=\"https://wikipedia.org/wiki/<PERSON>_VII_of_England\" title=\"<PERSON> VII of England\"><PERSON> VII</a>.", "links": [{"title": "Battle of Bosworth Field", "link": "https://wikipedia.org/wiki/Battle_of_Bosworth_Field"}, {"title": "<PERSON> of England", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_England"}, {"title": "House of Plantagenet", "link": "https://wikipedia.org/wiki/House_of_Plantagenet"}, {"title": "<PERSON> of England", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_England"}]}, {"year": "1559", "text": "Spanish archbishop <PERSON><PERSON><PERSON> is arrested for heresy.", "html": "1559 - Spanish <a href=\"https://wikipedia.org/wiki/Archbishop\" title=\"Archbishop\">archbishop</a> <a href=\"https://wikipedia.org/wiki/Bartolom%C3%A9_Carranza\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a> is arrested for <a href=\"https://wikipedia.org/wiki/Heresy_in_Christianity\" title=\"Heresy in Christianity\">heresy</a>.", "no_year_html": "Spanish <a href=\"https://wikipedia.org/wiki/Archbishop\" title=\"Archbishop\">archbishop</a> <a href=\"https://wikipedia.org/wiki/Bartolom%C3%A9_Carranza\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a> is arrested for <a href=\"https://wikipedia.org/wiki/Heresy_in_Christianity\" title=\"Heresy in Christianity\">heresy</a>.", "links": [{"title": "Archbishop", "link": "https://wikipedia.org/wiki/Archbishop"}, {"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Bartolom%C3%A9_Carranza"}, {"title": "Heresy in Christianity", "link": "https://wikipedia.org/wiki/Heresy_in_Christianity"}]}, {"year": "1614", "text": "Fettmilch Uprising: Jews are expelled from Frankfurt, Holy Roman Empire, following the plundering of the Judengasse.", "html": "1614 - <a href=\"https://wikipedia.org/wiki/Fettmilch_Uprising\" class=\"mw-redirect\" title=\"Fettmilch Uprising\">Fettmilch Uprising</a>: Jews are expelled from <a href=\"https://wikipedia.org/wiki/Frankfurt\" title=\"Frankfurt\">Frankfurt</a>, <a href=\"https://wikipedia.org/wiki/Holy_Roman_Empire\" title=\"Holy Roman Empire\">Holy Roman Empire</a>, following the plundering of the <a href=\"https://wikipedia.org/wiki/Frankfurter_Judengasse\" title=\"Frankfurter Judengasse\">Judengasse</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Fettmilch_Uprising\" class=\"mw-redirect\" title=\"Fettmilch Uprising\">Fettmilch Uprising</a>: Jews are expelled from <a href=\"https://wikipedia.org/wiki/Frankfurt\" title=\"Frankfurt\">Frankfurt</a>, <a href=\"https://wikipedia.org/wiki/Holy_Roman_Empire\" title=\"Holy Roman Empire\">Holy Roman Empire</a>, following the plundering of the <a href=\"https://wikipedia.org/wiki/Frankfurter_Judengasse\" title=\"Frankfurter Judengasse\">Judengasse</a>.", "links": [{"title": "Fettmilch Uprising", "link": "https://wikipedia.org/wiki/Fettmilch_Uprising"}, {"title": "Frankfurt", "link": "https://wikipedia.org/wiki/Frankfurt"}, {"title": "Holy Roman Empire", "link": "https://wikipedia.org/wiki/Holy_Roman_Empire"}, {"title": "Frankfurter Judengasse", "link": "https://wikipedia.org/wiki/Frankfurter_Judengasse"}]}, {"year": "1639", "text": "Madras (now Chennai), India, is founded by the British East India Company on a sliver of land bought from local Nayak rulers.", "html": "1639 - Madras (now <a href=\"https://wikipedia.org/wiki/Chennai\" title=\"Chennai\">Chennai</a>), India, is founded by the <a href=\"https://wikipedia.org/wiki/East_India_Company\" title=\"East India Company\">British East India Company</a> on a sliver of land bought from local Nayak rulers.", "no_year_html": "Madras (now <a href=\"https://wikipedia.org/wiki/Chennai\" title=\"Chennai\">Chennai</a>), India, is founded by the <a href=\"https://wikipedia.org/wiki/East_India_Company\" title=\"East India Company\">British East India Company</a> on a sliver of land bought from local Nayak rulers.", "links": [{"title": "Chennai", "link": "https://wikipedia.org/wiki/Chennai"}, {"title": "East India Company", "link": "https://wikipedia.org/wiki/East_India_Company"}]}, {"year": "1642", "text": "<PERSON> raises his standard in Nottingham, which marks the beginning of the English Civil War.", "html": "1642 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_England\" title=\"<PERSON> I of England\"><PERSON> I</a> raises his standard in <a href=\"https://wikipedia.org/wiki/Nottingham\" title=\"Nottingham\">Nottingham</a>, which marks the beginning of the <a href=\"https://wikipedia.org/wiki/English_Civil_War\" title=\"English Civil War\">English Civil War</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_England\" title=\"<PERSON> I of England\"><PERSON> I</a> raises his standard in <a href=\"https://wikipedia.org/wiki/Nottingham\" title=\"Nottingham\">Nottingham</a>, which marks the beginning of the <a href=\"https://wikipedia.org/wiki/English_Civil_War\" title=\"English Civil War\">English Civil War</a>.", "links": [{"title": "<PERSON> of England", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_England"}, {"title": "Nottingham", "link": "https://wikipedia.org/wiki/Nottingham"}, {"title": "English Civil War", "link": "https://wikipedia.org/wiki/English_Civil_War"}]}, {"year": "1654", "text": "<PERSON> arrives in New Amsterdam. He is the first known Jewish immigrant to America.", "html": "1654 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> arrives in <a href=\"https://wikipedia.org/wiki/New_Amsterdam\" title=\"New Amsterdam\">New Amsterdam</a>. He is the first known Jewish immigrant to America.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> arrives in <a href=\"https://wikipedia.org/wiki/New_Amsterdam\" title=\"New Amsterdam\">New Amsterdam</a>. He is the first known Jewish immigrant to America.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}, {"title": "New Amsterdam", "link": "https://wikipedia.org/wiki/New_Amsterdam"}]}, {"year": "1711", "text": "Britain's Quebec Expedition loses eight ships and almost nine hundred soldiers, sailors and women to rocks at Pointe-aux-Anglais.", "html": "1711 - Britain's <a href=\"https://wikipedia.org/wiki/Quebec_expedition_(1711)\" title=\"Quebec expedition (1711)\">Quebec Expedition</a> loses eight ships and almost nine hundred soldiers, sailors and women to rocks at <a href=\"https://wikipedia.org/wiki/Pointe-aux-Anglais\" title=\"Pointe-aux-Anglais\">Pointe-aux-Anglais</a>.", "no_year_html": "Britain's <a href=\"https://wikipedia.org/wiki/Quebec_expedition_(1711)\" title=\"Quebec expedition (1711)\">Quebec Expedition</a> loses eight ships and almost nine hundred soldiers, sailors and women to rocks at <a href=\"https://wikipedia.org/wiki/Pointe-aux-Anglais\" title=\"Pointe-aux-Anglais\">Pointe-aux-Anglais</a>.", "links": [{"title": "Quebec expedition (1711)", "link": "https://wikipedia.org/wiki/Quebec_expedition_(1711)"}, {"title": "Pointe-aux-Anglais", "link": "https://wikipedia.org/wiki/Pointe-aux-Anglais"}]}, {"year": "1717", "text": "Spanish troops land on Sardinia.", "html": "1717 - Spanish troops land on <a href=\"https://wikipedia.org/wiki/Sardinia\" title=\"Sardinia\">Sardinia</a>.", "no_year_html": "Spanish troops land on <a href=\"https://wikipedia.org/wiki/Sardinia\" title=\"Sardinia\">Sardinia</a>.", "links": [{"title": "Sardinia", "link": "https://wikipedia.org/wiki/Sardinia"}]}, {"year": "1770", "text": "<PERSON> names and lands on Possession Island, and claims the east coast of Australia for Britain as New South Wales.", "html": "1770 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> names and lands on <a href=\"https://wikipedia.org/wiki/Possession_Island_(Queensland)\" title=\"Possession Island (Queensland)\">Possession Island</a>, and claims the east coast of Australia for Britain as <a href=\"https://wikipedia.org/wiki/New_South_Wales\" title=\"New South Wales\">New South Wales</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> names and lands on <a href=\"https://wikipedia.org/wiki/Possession_Island_(Queensland)\" title=\"Possession Island (Queensland)\">Possession Island</a>, and claims the east coast of Australia for Britain as <a href=\"https://wikipedia.org/wiki/New_South_Wales\" title=\"New South Wales\">New South Wales</a>.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Possession Island (Queensland)", "link": "https://wikipedia.org/wiki/Possession_Island_(Queensland)"}, {"title": "New South Wales", "link": "https://wikipedia.org/wiki/New_South_Wales"}]}, {"year": "1777", "text": "British forces abandon the Siege of Fort Stanwix after hearing rumors of Continental Army reinforcements.", "html": "1777 - British forces abandon the <a href=\"https://wikipedia.org/wiki/Siege_of_Fort_Stanwix\" title=\"Siege of Fort Stanwix\">Siege of Fort Stanwix</a> after hearing rumors of <a href=\"https://wikipedia.org/wiki/Continental_Army\" title=\"Continental Army\">Continental Army</a> reinforcements.", "no_year_html": "British forces abandon the <a href=\"https://wikipedia.org/wiki/Siege_of_Fort_Stanwix\" title=\"Siege of Fort Stanwix\">Siege of Fort Stanwix</a> after hearing rumors of <a href=\"https://wikipedia.org/wiki/Continental_Army\" title=\"Continental Army\">Continental Army</a> reinforcements.", "links": [{"title": "Siege of Fort Stanwix", "link": "https://wikipedia.org/wiki/Siege_of_Fort_Stanwix"}, {"title": "Continental Army", "link": "https://wikipedia.org/wiki/Continental_Army"}]}, {"year": "1780", "text": "<PERSON>'s ship HMS Resolution returns to England (<PERSON> having been killed on Hawaii during the voyage).", "html": "1780 - <PERSON>'s ship <a href=\"https://wikipedia.org/wiki/HMS_Resolution_(1771)\" title=\"HMS Resolution (1771)\">HMS <i>Resolution</i></a> returns to England (<PERSON> having been killed on Hawaii during the voyage).", "no_year_html": "<PERSON>'s ship <a href=\"https://wikipedia.org/wiki/HMS_Resolution_(1771)\" title=\"HMS Resolution (1771)\">HMS <i>Resolution</i></a> returns to England (<PERSON> having been killed on Hawaii during the voyage).", "links": [{"title": "HMS Resolution (1771)", "link": "https://wikipedia.org/wiki/HMS_Resolution_(1771)"}]}, {"year": "1791", "text": "The Haitian slave revolution begins in Saint-Domingue, Haiti.", "html": "1791 - The <a href=\"https://wikipedia.org/wiki/Haiti\" title=\"Haiti\">Haitian</a> <a href=\"https://wikipedia.org/wiki/Haitian_Revolution\" title=\"Haitian Revolution\">slave revolution</a> begins in <a href=\"https://wikipedia.org/wiki/Saint-Domingue\" title=\"Saint-Domingue\">Saint-Domingue, Haiti</a>.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Haiti\" title=\"Haiti\">Haitian</a> <a href=\"https://wikipedia.org/wiki/Haitian_Revolution\" title=\"Haitian Revolution\">slave revolution</a> begins in <a href=\"https://wikipedia.org/wiki/Saint-Domingue\" title=\"Saint-Domingue\">Saint-Domingue, Haiti</a>.", "links": [{"title": "Haiti", "link": "https://wikipedia.org/wiki/Haiti"}, {"title": "Haitian Revolution", "link": "https://wikipedia.org/wiki/Haitian_Revolution"}, {"title": "Saint-Domingue", "link": "https://wikipedia.org/wiki/Saint-Domingue"}]}, {"year": "1798", "text": "French troops land at Kilcummin, County Mayo, Ireland to aid the rebellion.", "html": "1798 - French troops land at <a href=\"https://wikipedia.org/wiki/Kilcummin,_County_Mayo\" title=\"Kilcummin, County Mayo\">Kilcummin, County Mayo</a>, Ireland to aid the <a href=\"https://wikipedia.org/wiki/Irish_Rebellion_of_1798\" title=\"Irish Rebellion of 1798\">rebellion</a>.", "no_year_html": "French troops land at <a href=\"https://wikipedia.org/wiki/Kilcummin,_County_Mayo\" title=\"Kilcummin, County Mayo\">Kilcummin, County Mayo</a>, Ireland to aid the <a href=\"https://wikipedia.org/wiki/Irish_Rebellion_of_1798\" title=\"Irish Rebellion of 1798\">rebellion</a>.", "links": [{"title": "Kilcummin, County Mayo", "link": "https://wikipedia.org/wiki/Kil<PERSON><PERSON>,_County_Mayo"}, {"title": "Irish Rebellion of 1798", "link": "https://wikipedia.org/wiki/Irish_Rebellion_of_1798"}]}, {"year": "1827", "text": "<PERSON> becomes President of Peru.", "html": "1827 - <a href=\"https://wikipedia.org/wiki/Jos%C3%A9_de_La_Mar\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a> becomes <a href=\"https://wikipedia.org/wiki/List_of_Presidents_of_Peru\" class=\"mw-redirect\" title=\"List of Presidents of Peru\">President of Peru</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Jos%C3%A9_de_La_Mar\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a> becomes <a href=\"https://wikipedia.org/wiki/List_of_Presidents_of_Peru\" class=\"mw-redirect\" title=\"List of Presidents of Peru\">President of Peru</a>.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Jos%C3%A9_de_La_Mar"}, {"title": "List of Presidents of Peru", "link": "https://wikipedia.org/wiki/List_of_Presidents_of_Peru"}]}, {"year": "1846", "text": "The Second Federal Republic of Mexico is established.", "html": "1846 - The <a href=\"https://wikipedia.org/wiki/Second_Federal_Republic_of_Mexico\" title=\"Second Federal Republic of Mexico\">Second Federal Republic of Mexico</a> is established.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Second_Federal_Republic_of_Mexico\" title=\"Second Federal Republic of Mexico\">Second Federal Republic of Mexico</a> is established.", "links": [{"title": "Second Federal Republic of Mexico", "link": "https://wikipedia.org/wiki/Second_Federal_Republic_of_Mexico"}]}, {"year": "1849", "text": "Passaleão incident: <PERSON>, the governor of Portuguese Macau, is assassinated by a group of Chinese locals, triggering a military confrontation between China and Portugal at the Battle of Passaleão three days after.", "html": "1849 - <a href=\"https://wikipedia.org/wiki/Passale%C3%A3o_incident\" title=\"Passaleão incident\">Passaleão incident</a>: <a href=\"https://wikipedia.org/wiki/Jo%C3%A3o_<PERSON>_Ferreira_do_Amaral\" title=\"<PERSON>\"><PERSON></a>, the <a href=\"https://wikipedia.org/wiki/Governor_of_Macau\" title=\"Governor of Macau\">governor</a> of <a href=\"https://wikipedia.org/wiki/Portuguese_Macau\" title=\"Portuguese Macau\">Portuguese Macau</a>, is <a href=\"https://wikipedia.org/wiki/Passale%C3%A3o_incident#Assassination_of_Amaral\" title=\"Passaleão incident\">assassinated</a> by a group of Chinese locals, triggering a military confrontation between <a href=\"https://wikipedia.org/wiki/Qing_dynasty\" title=\"Qing dynasty\">China</a> and <a href=\"https://wikipedia.org/wiki/History_of_Portugal_(1834%E2%80%931910)\" title=\"History of Portugal (1834-1910)\">Portugal</a> at the <a href=\"https://wikipedia.org/wiki/Passale%C3%A3o_incident\" title=\"Passaleão incident\">Battle of Passaleão</a> three days after.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Passale%C3%A3o_incident\" title=\"Passaleão incident\">Passaleão incident</a>: <a href=\"https://wikipedia.org/wiki/Jo%C3%A3o_<PERSON>_Ferreira_do_Amaral\" title=\"<PERSON>\"><PERSON></a>, the <a href=\"https://wikipedia.org/wiki/Governor_of_Macau\" title=\"Governor of Macau\">governor</a> of <a href=\"https://wikipedia.org/wiki/Portuguese_Macau\" title=\"Portuguese Macau\">Portuguese Macau</a>, is <a href=\"https://wikipedia.org/wiki/Passale%C3%A3o_incident#Assassination_of_Amaral\" title=\"Passaleão incident\">assassinated</a> by a group of Chinese locals, triggering a military confrontation between <a href=\"https://wikipedia.org/wiki/Qing_dynasty\" title=\"Qing dynasty\">China</a> and <a href=\"https://wikipedia.org/wiki/History_of_Portugal_(1834%E2%80%931910)\" title=\"History of Portugal (1834-1910)\">Portugal</a> at the <a href=\"https://wikipedia.org/wiki/Passale%C3%A3o_incident\" title=\"Passaleão incident\">Battle of Passaleão</a> three days after.", "links": [{"title": "Passaleão incident", "link": "https://wikipedia.org/wiki/Passale%C3%A3o_incident"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Jo%C3%A3<PERSON>_<PERSON>_<PERSON>_do_Amaral"}, {"title": "Governor of Macau", "link": "https://wikipedia.org/wiki/Governor_of_Macau"}, {"title": "Portuguese Macau", "link": "https://wikipedia.org/wiki/Portuguese_Macau"}, {"title": "Passaleão incident", "link": "https://wikipedia.org/wiki/Passale%C3%A3o_incident#Assassination_of_Amaral"}, {"title": "Qing dynasty", "link": "https://wikipedia.org/wiki/Qing_dynasty"}, {"title": "History of Portugal (1834-1910)", "link": "https://wikipedia.org/wiki/History_of_Portugal_(1834%E2%80%931910)"}, {"title": "Passaleão incident", "link": "https://wikipedia.org/wiki/Passale%C3%A3o_incident"}]}, {"year": "1851", "text": "The first America's Cup is won by the yacht America.", "html": "1851 - The first <a href=\"https://wikipedia.org/wiki/America%27s_Cup\" title=\"America's Cup\">America's Cup</a> is won by the <a href=\"https://wikipedia.org/wiki/Yacht\" title=\"Yacht\">yacht</a> <i><a href=\"https://wikipedia.org/wiki/America_(yacht)\" title=\"America (yacht)\">America</a></i>.", "no_year_html": "The first <a href=\"https://wikipedia.org/wiki/America%27s_Cup\" title=\"America's Cup\">America's Cup</a> is won by the <a href=\"https://wikipedia.org/wiki/Yacht\" title=\"Yacht\">yacht</a> <i><a href=\"https://wikipedia.org/wiki/America_(yacht)\" title=\"America (yacht)\">America</a></i>.", "links": [{"title": "America's Cup", "link": "https://wikipedia.org/wiki/America%27s_Cup"}, {"title": "Yacht", "link": "https://wikipedia.org/wiki/Yacht"}, {"title": "America (yacht)", "link": "https://wikipedia.org/wiki/America_(yacht)"}]}, {"year": "1864", "text": "Twelve nations sign the First Geneva Convention, establishing the rules of protection of the victims of armed conflicts.", "html": "1864 - Twelve nations sign the <a href=\"https://wikipedia.org/wiki/First_Geneva_Convention\" title=\"First Geneva Convention\">First Geneva Convention</a>, establishing the rules of protection of the victims of armed conflicts.", "no_year_html": "Twelve nations sign the <a href=\"https://wikipedia.org/wiki/First_Geneva_Convention\" title=\"First Geneva Convention\">First Geneva Convention</a>, establishing the rules of protection of the victims of armed conflicts.", "links": [{"title": "First Geneva Convention", "link": "https://wikipedia.org/wiki/First_Geneva_Convention"}]}, {"year": "1875", "text": "The Treaty of Saint Petersburg between Japan and Russia is ratified, providing for the exchange of Sakhalin for the Kuril Islands.", "html": "1875 - The <a href=\"https://wikipedia.org/wiki/Treaty_of_Saint_Petersburg_(1875)\" title=\"Treaty of Saint Petersburg (1875)\">Treaty of Saint Petersburg</a> between Japan and Russia is ratified, providing for the exchange of <a href=\"https://wikipedia.org/wiki/Sakhalin\" title=\"Sakhalin\">Sakhalin</a> for the <a href=\"https://wikipedia.org/wiki/Kuril_Islands\" title=\"Kuril Islands\">Kuril Islands</a>.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Treaty_of_Saint_Petersburg_(1875)\" title=\"Treaty of Saint Petersburg (1875)\">Treaty of Saint Petersburg</a> between Japan and Russia is ratified, providing for the exchange of <a href=\"https://wikipedia.org/wiki/Sakhalin\" title=\"Sakhalin\">Sakhalin</a> for the <a href=\"https://wikipedia.org/wiki/Kuril_Islands\" title=\"Kuril Islands\">Kuril Islands</a>.", "links": [{"title": "Treaty of Saint Petersburg (1875)", "link": "https://wikipedia.org/wiki/Treaty_of_Saint_Petersburg_(1875)"}, {"title": "Sakhalin", "link": "https://wikipedia.org/wiki/Sakhalin"}, {"title": "Kuril Islands", "link": "https://wikipedia.org/wiki/Kuril_Islands"}]}, {"year": "1894", "text": "<PERSON><PERSON><PERSON> forms the Natal Indian Congress (NIC) in order to fight discrimination against Indian traders in Natal.", "html": "1894 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a> forms the <a href=\"https://wikipedia.org/wiki/Natal_Indian_Congress\" title=\"Natal Indian Congress\">Natal Indian Congress</a> (NIC) in order to fight discrimination against Indian traders in Natal.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a> forms the <a href=\"https://wikipedia.org/wiki/Natal_Indian_Congress\" title=\"Natal Indian Congress\">Natal Indian Congress</a> (NIC) in order to fight discrimination against Indian traders in Natal.", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}, {"title": "Natal Indian Congress", "link": "https://wikipedia.org/wiki/Natal_Indian_Congress"}]}, {"year": "1902", "text": "The Cadillac Motor Company is founded.", "html": "1902 - The <a href=\"https://wikipedia.org/wiki/Cadillac\" title=\"Cadillac\">Cadillac</a> Motor Company is founded.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Cadillac\" title=\"Cadillac\">Cadillac</a> Motor Company is founded.", "links": [{"title": "Cadillac", "link": "https://wikipedia.org/wiki/Cadillac"}]}, {"year": "1902", "text": "<PERSON> becomes the first President of the United States to make a public appearance in an automobile.", "html": "1902 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> becomes the first President of the United States to make a public appearance in an automobile.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> becomes the first President of the United States to make a public appearance in an automobile.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1902", "text": "At least 6,000 people are killed by the magnitude 7.7 Kashgar earthquake in the Tien Shan mountains.", "html": "1902 - At least 6,000 people are killed by the magnitude 7.7 <a href=\"https://wikipedia.org/wiki/1902_Turkestan_earthquake\" title=\"1902 Turkestan earthquake\">Kashgar earthquake</a> in the Tien Shan mountains.", "no_year_html": "At least 6,000 people are killed by the magnitude 7.7 <a href=\"https://wikipedia.org/wiki/1902_Turkestan_earthquake\" title=\"1902 Turkestan earthquake\">Kashgar earthquake</a> in the Tien Shan mountains.", "links": [{"title": "1902 Turkestan earthquake", "link": "https://wikipedia.org/wiki/1902_Turkestan_earthquake"}]}, {"year": "1922", "text": "<PERSON>, Commander-in-chief of the Irish Free State Army, is shot dead in an ambush during the Irish Civil War.", "html": "1922 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(Irish_leader)\" title=\"<PERSON> (Irish leader)\"><PERSON></a>, <a href=\"https://wikipedia.org/wiki/Commander-in-chief\" title=\"Commander-in-chief\">Commander-in-chief</a> of the <a href=\"https://wikipedia.org/wiki/Irish_Free_State\" title=\"Irish Free State\">Irish Free State</a> Army, is shot dead in an ambush during the <a href=\"https://wikipedia.org/wiki/Irish_Civil_War\" title=\"Irish Civil War\">Irish Civil War</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(Irish_leader)\" title=\"<PERSON> (Irish leader)\"><PERSON></a>, <a href=\"https://wikipedia.org/wiki/Commander-in-chief\" title=\"Commander-in-chief\">Commander-in-chief</a> of the <a href=\"https://wikipedia.org/wiki/Irish_Free_State\" title=\"Irish Free State\">Irish Free State</a> Army, is shot dead in an ambush during the <a href=\"https://wikipedia.org/wiki/Irish_Civil_War\" title=\"Irish Civil War\">Irish Civil War</a>.", "links": [{"title": "<PERSON> (Irish leader)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(Irish_leader)"}, {"title": "Commander-in-chief", "link": "https://wikipedia.org/wiki/Commander-in-chief"}, {"title": "Irish Free State", "link": "https://wikipedia.org/wiki/Irish_Free_State"}, {"title": "Irish Civil War", "link": "https://wikipedia.org/wiki/Irish_Civil_War"}]}, {"year": "1934", "text": "<PERSON> of Australia becomes the only test cricket captain to twice regain The Ashes.", "html": "1934 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> of <a href=\"https://wikipedia.org/wiki/Australia_national_cricket_team\" title=\"Australia national cricket team\">Australia</a> becomes the only <a href=\"https://wikipedia.org/wiki/Test_cricket\" title=\"Test cricket\">test cricket</a> captain to twice regain <a href=\"https://wikipedia.org/wiki/The_Ashes\" title=\"The Ashes\">The Ashes</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> of <a href=\"https://wikipedia.org/wiki/Australia_national_cricket_team\" title=\"Australia national cricket team\">Australia</a> becomes the only <a href=\"https://wikipedia.org/wiki/Test_cricket\" title=\"Test cricket\">test cricket</a> captain to twice regain <a href=\"https://wikipedia.org/wiki/The_Ashes\" title=\"The Ashes\">The Ashes</a>.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Australia national cricket team", "link": "https://wikipedia.org/wiki/Australia_national_cricket_team"}, {"title": "Test cricket", "link": "https://wikipedia.org/wiki/Test_cricket"}, {"title": "The Ashes", "link": "https://wikipedia.org/wiki/The_Ashes"}]}, {"year": "1941", "text": "World War II: German troops begin the Siege of Leningrad.", "html": "1941 - <a href=\"https://wikipedia.org/wiki/World_War_II\" title=\"World War II\">World War II</a>: German troops begin the <a href=\"https://wikipedia.org/wiki/Siege_of_Leningrad\" title=\"Siege of Leningrad\">Siege of Leningrad</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/World_War_II\" title=\"World War II\">World War II</a>: German troops begin the <a href=\"https://wikipedia.org/wiki/Siege_of_Leningrad\" title=\"Siege of Leningrad\">Siege of Leningrad</a>.", "links": [{"title": "World War II", "link": "https://wikipedia.org/wiki/World_War_II"}, {"title": "Siege of Leningrad", "link": "https://wikipedia.org/wiki/Siege_of_Leningrad"}]}, {"year": "1942", "text": "Brazil declares war on Germany, Japan and Italy.", "html": "1942 - <a href=\"https://wikipedia.org/wiki/Brazil\" title=\"Brazil\">Brazil</a> declares war on <a href=\"https://wikipedia.org/wiki/Germany\" title=\"Germany\">Germany</a>, Japan and <a href=\"https://wikipedia.org/wiki/Italy\" title=\"Italy\">Italy</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Brazil\" title=\"Brazil\">Brazil</a> declares war on <a href=\"https://wikipedia.org/wiki/Germany\" title=\"Germany\">Germany</a>, Japan and <a href=\"https://wikipedia.org/wiki/Italy\" title=\"Italy\">Italy</a>.", "links": [{"title": "Brazil", "link": "https://wikipedia.org/wiki/Brazil"}, {"title": "Germany", "link": "https://wikipedia.org/wiki/Germany"}, {"title": "Italy", "link": "https://wikipedia.org/wiki/Italy"}]}, {"year": "1944", "text": "World War II: Holocaust of Kedros in Crete by German forces.", "html": "1944 - World War II: <a href=\"https://wikipedia.org/wiki/Holocaust_of_Kedros\" title=\"Holocaust of Kedros\">Holocaust of Kedros</a> in <a href=\"https://wikipedia.org/wiki/Crete\" title=\"Crete\">Crete</a> by German forces.", "no_year_html": "World War II: <a href=\"https://wikipedia.org/wiki/Holocaust_of_Kedros\" title=\"Holocaust of Kedros\">Holocaust of Kedros</a> in <a href=\"https://wikipedia.org/wiki/Crete\" title=\"Crete\">Crete</a> by German forces.", "links": [{"title": "Holocaust of Kedros", "link": "https://wikipedia.org/wiki/Holocaust_of_<PERSON><PERSON><PERSON>"}, {"title": "Crete", "link": "https://wikipedia.org/wiki/Crete"}]}, {"year": "1949", "text": "The Queen Charlotte earthquake is Canada's strongest since the 1700 Cascadia earthquake.", "html": "1949 - The <a href=\"https://wikipedia.org/wiki/1949_Queen_Charlotte_Islands_earthquake\" title=\"1949 Queen Charlotte Islands earthquake\">Queen Charlotte earthquake</a> is Canada's strongest since the <a href=\"https://wikipedia.org/wiki/1700_Cascadia_earthquake\" title=\"1700 Cascadia earthquake\">1700 Cascadia earthquake</a>.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/1949_Queen_Charlotte_Islands_earthquake\" title=\"1949 Queen Charlotte Islands earthquake\">Queen Charlotte earthquake</a> is Canada's strongest since the <a href=\"https://wikipedia.org/wiki/1700_Cascadia_earthquake\" title=\"1700 Cascadia earthquake\">1700 Cascadia earthquake</a>.", "links": [{"title": "1949 Queen Charlotte Islands earthquake", "link": "https://wikipedia.org/wiki/1949_Queen_Charlotte_Islands_earthquake"}, {"title": "1700 Cascadia earthquake", "link": "https://wikipedia.org/wiki/1700_Cascadia_earthquake"}]}, {"year": "1953", "text": "The penal colony on Devil's Island is permanently closed.", "html": "1953 - The <a href=\"https://wikipedia.org/wiki/Penal_colony\" title=\"Penal colony\">penal colony</a> on <a href=\"https://wikipedia.org/wiki/Devil%27s_Island\" title=\"Devil's Island\">Devil's Island</a> is permanently closed.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Penal_colony\" title=\"Penal colony\">penal colony</a> on <a href=\"https://wikipedia.org/wiki/Devil%27s_Island\" title=\"Devil's Island\">Devil's Island</a> is permanently closed.", "links": [{"title": "Penal colony", "link": "https://wikipedia.org/wiki/Penal_colony"}, {"title": "Devil's Island", "link": "https://wikipedia.org/wiki/Devil%27s_Island"}]}, {"year": "1962", "text": "The OAS attempts to assassinate French president <PERSON>.", "html": "1962 - The <a href=\"https://wikipedia.org/wiki/Organisation_de_l%27arm%C3%A9e_secr%C3%A8te\" class=\"mw-redirect\" title=\"Organisation de l'armée secrète\">OAS</a> <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_attack\" title=\"<PERSON><PERSON><PERSON> attack\">attempts to assassinate</a> French president <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Organisation_de_l%27arm%C3%A9e_secr%C3%A8te\" class=\"mw-redirect\" title=\"Organisation de l'armée secrète\">OAS</a> <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_attack\" title=\"<PERSON><PERSON><PERSON><PERSON><PERSON> attack\">attempts to assassinate</a> French president <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>.", "links": [{"title": "Organisation de l'armée secrète", "link": "https://wikipedia.org/wiki/Organisation_de_l%27arm%C3%A9e_secr%C3%A8te"}, {"title": "<PERSON><PERSON><PERSON><PERSON><PERSON> attack", "link": "https://wikipedia.org/wiki/<PERSON>-<PERSON><PERSON><PERSON>_attack"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1963", "text": "X-15 Flight 91 reaches the highest altitude of the X-15 program (107.96 km (67.08 mi) (354,200 feet)).", "html": "1963 - <a href=\"https://wikipedia.org/wiki/X-15_Flight_91\" title=\"X-15 Flight 91\">X-15 Flight 91</a> reaches the highest altitude of the X-15 program (107.96 km (67.08 mi) (354,200 feet)).", "no_year_html": "<a href=\"https://wikipedia.org/wiki/X-15_Flight_91\" title=\"X-15 Flight 91\">X-15 Flight 91</a> reaches the highest altitude of the X-15 program (107.96 km (67.08 mi) (354,200 feet)).", "links": [{"title": "X-15 Flight 91", "link": "https://wikipedia.org/wiki/X-15_Flight_91"}]}, {"year": "1965", "text": "<PERSON>, pitcher for the San Francisco Giants, strikes <PERSON>, catcher for the Los Angeles Dodgers, on the head with a bat, sparking a 14-minute brawl, one of the most violent on-field incidents in sports history.", "html": "1965 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, pitcher for the <a href=\"https://wikipedia.org/wiki/1965_San_Francisco_Giants_season\" title=\"1965 San Francisco Giants season\">San Francisco Giants</a>, strikes <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"John <PERSON>\"><PERSON></a>, catcher for the <a href=\"https://wikipedia.org/wiki/1965_Los_Angeles_Dodgers_season\" title=\"1965 Los Angeles Dodgers season\">Los Angeles Dodgers</a>, on the head with a <a href=\"https://wikipedia.org/wiki/Baseball_bat\" title=\"Baseball bat\">bat</a>, sparking a <a href=\"https://wikipedia.org/wiki/Battle_of_Candlestick_Park\" class=\"mw-redirect\" title=\"Battle of Candlestick Park\">14-minute brawl</a>, one of the most violent on-field incidents in sports history.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, pitcher for the <a href=\"https://wikipedia.org/wiki/1965_San_Francisco_Giants_season\" title=\"1965 San Francisco Giants season\">San Francisco Giants</a>, strikes <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"John <PERSON>\"><PERSON></a>, catcher for the <a href=\"https://wikipedia.org/wiki/1965_Los_Angeles_Dodgers_season\" title=\"1965 Los Angeles Dodgers season\">Los Angeles Dodgers</a>, on the head with a <a href=\"https://wikipedia.org/wiki/Baseball_bat\" title=\"Baseball bat\">bat</a>, sparking a <a href=\"https://wikipedia.org/wiki/Battle_of_Candlestick_Park\" class=\"mw-redirect\" title=\"Battle of Candlestick Park\">14-minute brawl</a>, one of the most violent on-field incidents in sports history.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "1965 San Francisco Giants season", "link": "https://wikipedia.org/wiki/1965_San_Francisco_Giants_season"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "1965 Los Angeles Dodgers season", "link": "https://wikipedia.org/wiki/1965_Los_Angeles_Dodgers_season"}, {"title": "Baseball bat", "link": "https://wikipedia.org/wiki/Baseball_bat"}, {"title": "Battle of Candlestick Park", "link": "https://wikipedia.org/wiki/Battle_of_Candlestick_Park"}]}, {"year": "1966", "text": "Labor movements NFWA and AWOC merge to become the United Farm Workers Organizing Committee (UFWOC), the predecessor of the United Farm Workers.", "html": "1966 - Labor movements <a href=\"https://wikipedia.org/wiki/United_Farm_Workers\" title=\"United Farm Workers\">NFWA</a> and AWOC merge to become the United Farm Workers Organizing Committee (UFWOC), the predecessor of the <a href=\"https://wikipedia.org/wiki/United_Farm_Workers\" title=\"United Farm Workers\">United Farm Workers</a>.", "no_year_html": "Labor movements <a href=\"https://wikipedia.org/wiki/United_Farm_Workers\" title=\"United Farm Workers\">NFWA</a> and AWOC merge to become the United Farm Workers Organizing Committee (UFWOC), the predecessor of the <a href=\"https://wikipedia.org/wiki/United_Farm_Workers\" title=\"United Farm Workers\">United Farm Workers</a>.", "links": [{"title": "United Farm Workers", "link": "https://wikipedia.org/wiki/United_Farm_Workers"}, {"title": "United Farm Workers", "link": "https://wikipedia.org/wiki/United_Farm_Workers"}]}, {"year": "1968", "text": "Pope <PERSON> arrives in Bogotá, Colombia. It is the first visit of a pope to Latin America.", "html": "1968 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"Pope Paul VI\">Pope <PERSON></a> arrives in <a href=\"https://wikipedia.org/wiki/Bogot%C3%A1\" title=\"Bogotá\">Bogotá</a>, <a href=\"https://wikipedia.org/wiki/Colombia\" title=\"Colombia\">Colombia</a>. It is the first visit of a pope to Latin America.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"Pope Paul <PERSON>\">Pope <PERSON> VI</a> arrives in <a href=\"https://wikipedia.org/wiki/Bogot%C3%A1\" title=\"Bogotá\">Bogotá</a>, <a href=\"https://wikipedia.org/wiki/Colombia\" title=\"Colombia\">Colombia</a>. It is the first visit of a pope to Latin America.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "Bogotá", "link": "https://wikipedia.org/wiki/Bogot%C3%A1"}, {"title": "Colombia", "link": "https://wikipedia.org/wiki/Colombia"}]}, {"year": "1971", "text": "<PERSON><PERSON> and <PERSON> announce the arrest of 20 of the Camden 28.", "html": "1971 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a> and <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> announce the arrest of 20 of <a href=\"https://wikipedia.org/wiki/The_Camden_28\" title=\"The Camden 28\">the Camden 28</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a> and <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> announce the arrest of 20 of <a href=\"https://wikipedia.org/wiki/The_Camden_28\" title=\"The Camden 28\">the Camden 28</a>.", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "The Camden 28", "link": "https://wikipedia.org/wiki/The_Camden_28"}]}, {"year": "1972", "text": "Rhodesia is expelled by the IOC for its racist policies.", "html": "1972 - <a href=\"https://wikipedia.org/wiki/Rhodesia\" title=\"Rhodesia\">Rhodesia</a> is expelled by the <a href=\"https://wikipedia.org/wiki/International_Olympic_Committee\" title=\"International Olympic Committee\">IOC</a> for its racist policies.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Rhodesia\" title=\"Rhodesia\">Rhodesia</a> is expelled by the <a href=\"https://wikipedia.org/wiki/International_Olympic_Committee\" title=\"International Olympic Committee\">IOC</a> for its racist policies.", "links": [{"title": "Rhodesia", "link": "https://wikipedia.org/wiki/Rhodesia"}, {"title": "International Olympic Committee", "link": "https://wikipedia.org/wiki/International_Olympic_Committee"}]}, {"year": "1973", "text": "The Congress of Chile votes in favour of a resolution condemning President <PERSON>'s government and demands that he resign or else be unseated through force and new elections.", "html": "1973 - The <a href=\"https://wikipedia.org/wiki/Congress_of_Chile\" class=\"mw-redirect\" title=\"Congress of Chile\">Congress of Chile</a> votes in favour of a resolution condemning President <a href=\"https://wikipedia.org/wiki/Salvador_Allende\" title=\"<PERSON>\"><PERSON></a>'s government and demands that he resign or else be unseated through force and new elections.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Congress_of_Chile\" class=\"mw-redirect\" title=\"Congress of Chile\">Congress of Chile</a> votes in favour of a resolution condemning President <a href=\"https://wikipedia.org/wiki/Salvador_Allende\" title=\"<PERSON>\"><PERSON></a>'s government and demands that he resign or else be unseated through force and new elections.", "links": [{"title": "Congress of Chile", "link": "https://wikipedia.org/wiki/Congress_of_Chile"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Salvador_Allende"}]}, {"year": "1978", "text": "Nicaraguan Revolution: The FSLN seizes the National Congress of Nicaragua, along with over a thousand hostages.", "html": "1978 - <a href=\"https://wikipedia.org/wiki/Nicaraguan_Revolution\" title=\"Nicaraguan Revolution\">Nicaraguan Revolution</a>: The <a href=\"https://wikipedia.org/wiki/Sandinista_National_Liberation_Front\" title=\"Sandinista National Liberation Front\">FSLN</a> seizes the <a href=\"https://wikipedia.org/wiki/National_Congress_of_Nicaragua\" title=\"National Congress of Nicaragua\">National Congress of Nicaragua</a>, along with over a thousand hostages.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Nicaraguan_Revolution\" title=\"Nicaraguan Revolution\">Nicaraguan Revolution</a>: The <a href=\"https://wikipedia.org/wiki/Sandinista_National_Liberation_Front\" title=\"Sandinista National Liberation Front\">FSLN</a> seizes the <a href=\"https://wikipedia.org/wiki/National_Congress_of_Nicaragua\" title=\"National Congress of Nicaragua\">National Congress of Nicaragua</a>, along with over a thousand hostages.", "links": [{"title": "Nicaraguan Revolution", "link": "https://wikipedia.org/wiki/Nicaraguan_Revolution"}, {"title": "Sandinista National Liberation Front", "link": "https://wikipedia.org/wiki/Sandinista_National_Liberation_Front"}, {"title": "National Congress of Nicaragua", "link": "https://wikipedia.org/wiki/National_Congress_of_Nicaragua"}]}, {"year": "1978", "text": "The District of Columbia Voting Rights Amendment is passed by the U.S. Congress, although it is never ratified by a sufficient number of states.", "html": "1978 - The <a href=\"https://wikipedia.org/wiki/District_of_Columbia_Voting_Rights_Amendment\" title=\"District of Columbia Voting Rights Amendment\">District of Columbia Voting Rights Amendment</a> is passed by the U.S. Congress, although it is never ratified by a sufficient number of states.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/District_of_Columbia_Voting_Rights_Amendment\" title=\"District of Columbia Voting Rights Amendment\">District of Columbia Voting Rights Amendment</a> is passed by the U.S. Congress, although it is never ratified by a sufficient number of states.", "links": [{"title": "District of Columbia Voting Rights Amendment", "link": "https://wikipedia.org/wiki/District_of_Columbia_Voting_Rights_Amendment"}]}, {"year": "1981", "text": "Far Eastern Air Transport Flight 103 disintegrates in mid-air and crashes in Sanyi Township, Miaoli County, Taiwan. All 110 people on board are killed.", "html": "1981 - <a href=\"https://wikipedia.org/wiki/Far_Eastern_Air_Transport_Flight_103\" title=\"Far Eastern Air Transport Flight 103\">Far Eastern Air Transport Flight 103</a> disintegrates in mid-air and crashes in <a href=\"https://wikipedia.org/wiki/Sanyi,_Miaoli\" title=\"Sanyi, Miaoli\">Sanyi Township</a>, <a href=\"https://wikipedia.org/wiki/Miaoli_County\" title=\"Miaoli County\">Miaoli County</a>, <a href=\"https://wikipedia.org/wiki/Taiwan\" title=\"Taiwan\">Taiwan</a>. All 110 people on board are killed.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Far_Eastern_Air_Transport_Flight_103\" title=\"Far Eastern Air Transport Flight 103\">Far Eastern Air Transport Flight 103</a> disintegrates in mid-air and crashes in <a href=\"https://wikipedia.org/wiki/Sanyi,_Miaoli\" title=\"Sanyi, Miaoli\">Sanyi Township</a>, <a href=\"https://wikipedia.org/wiki/Miaoli_County\" title=\"Miaoli County\">Miaoli County</a>, <a href=\"https://wikipedia.org/wiki/Taiwan\" title=\"Taiwan\">Taiwan</a>. All 110 people on board are killed.", "links": [{"title": "Far Eastern Air Transport Flight 103", "link": "https://wikipedia.org/wiki/Far_Eastern_Air_Transport_Flight_103"}, {"title": "Sanyi, Miaoli", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>,_<PERSON><PERSON>"}, {"title": "Miaoli County", "link": "https://wikipedia.org/wiki/Miaoli_County"}, {"title": "Taiwan", "link": "https://wikipedia.org/wiki/Taiwan"}]}, {"year": "1985", "text": "British Airtours Flight 28M suffers an engine fire during takeoff at Manchester Airport. The pilots abort but due to inefficient evacuation procedures 55 people are killed, mostly from smoke inhalation.", "html": "1985 - <a href=\"https://wikipedia.org/wiki/1985_Manchester_Airport_disaster\" title=\"1985 Manchester Airport disaster\">British Airtours Flight 28M</a> suffers an engine fire during takeoff at <a href=\"https://wikipedia.org/wiki/Manchester_Airport\" title=\"Manchester Airport\">Manchester Airport</a>. The pilots abort but due to inefficient evacuation procedures 55 people are killed, mostly from smoke inhalation.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/1985_Manchester_Airport_disaster\" title=\"1985 Manchester Airport disaster\">British Airtours Flight 28M</a> suffers an engine fire during takeoff at <a href=\"https://wikipedia.org/wiki/Manchester_Airport\" title=\"Manchester Airport\">Manchester Airport</a>. The pilots abort but due to inefficient evacuation procedures 55 people are killed, mostly from smoke inhalation.", "links": [{"title": "1985 Manchester Airport disaster", "link": "https://wikipedia.org/wiki/1985_Manchester_Airport_disaster"}, {"title": "Manchester Airport", "link": "https://wikipedia.org/wiki/Manchester_Airport"}]}, {"year": "1989", "text": "<PERSON> strikes out <PERSON><PERSON> to become the first Major League Baseball pitcher to record 5,000 strikeouts.", "html": "1989 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> strikes out <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a> to become the first <a href=\"https://wikipedia.org/wiki/Major_League_Baseball\" title=\"Major League Baseball\">Major League Baseball</a> <a href=\"https://wikipedia.org/wiki/Pitcher\" title=\"Pitcher\">pitcher</a> to record 5,000 <a href=\"https://wikipedia.org/wiki/Strikeout\" title=\"Strikeout\">strikeouts</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> strikes out <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a> to become the first <a href=\"https://wikipedia.org/wiki/Major_League_Baseball\" title=\"Major League Baseball\">Major League Baseball</a> <a href=\"https://wikipedia.org/wiki/Pitcher\" title=\"Pitcher\">pitcher</a> to record 5,000 <a href=\"https://wikipedia.org/wiki/Strikeout\" title=\"Strikeout\">strikeouts</a>.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}, {"title": "Major League Baseball", "link": "https://wikipedia.org/wiki/Major_League_Baseball"}, {"title": "Pitcher", "link": "https://wikipedia.org/wiki/Pitcher"}, {"title": "Strikeout", "link": "https://wikipedia.org/wiki/Strikeout"}]}, {"year": "1991", "text": "Iceland is the first nation in the world to recognize the independence of the Baltic states.", "html": "1991 - <a href=\"https://wikipedia.org/wiki/Iceland\" title=\"Iceland\">Iceland</a> is the first nation in the world to recognize the independence of the <a href=\"https://wikipedia.org/wiki/Baltic_states\" title=\"Baltic states\">Baltic states</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Iceland\" title=\"Iceland\">Iceland</a> is the first nation in the world to recognize the independence of the <a href=\"https://wikipedia.org/wiki/Baltic_states\" title=\"Baltic states\">Baltic states</a>.", "links": [{"title": "Iceland", "link": "https://wikipedia.org/wiki/Iceland"}, {"title": "Baltic states", "link": "https://wikipedia.org/wiki/Baltic_states"}]}, {"year": "1992", "text": "FBI sniper <PERSON><PERSON> shoots and kills <PERSON><PERSON> during an 11-day siege at her home at Ruby Ridge, Idaho.", "html": "1992 - <a href=\"https://wikipedia.org/wiki/Federal_Bureau_of_Investigation\" title=\"Federal Bureau of Investigation\">FBI</a> <a href=\"https://wikipedia.org/wiki/Sniper\" title=\"Sniper\">sniper</a> <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a> shoots and kills <PERSON><PERSON> during an 11-day siege at her home at <a href=\"https://wikipedia.org/wiki/Ruby_Ridge,_Idaho\" class=\"mw-redirect\" title=\"Ruby Ridge, Idaho\">Ruby Ridge, Idaho</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Federal_Bureau_of_Investigation\" title=\"Federal Bureau of Investigation\">FBI</a> <a href=\"https://wikipedia.org/wiki/Sniper\" title=\"Sniper\">sniper</a> <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a> shoots and kills <PERSON><PERSON> during an 11-day siege at her home at <a href=\"https://wikipedia.org/wiki/Ruby_Ridge,_Idaho\" class=\"mw-redirect\" title=\"Ruby Ridge, Idaho\">Ruby Ridge, Idaho</a>.", "links": [{"title": "Federal Bureau of Investigation", "link": "https://wikipedia.org/wiki/Federal_Bureau_of_Investigation"}, {"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Sniper"}, {"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}, {"title": "Ruby Ridge, Idaho", "link": "https://wikipedia.org/wiki/Ruby_Ridge,_Idaho"}]}, {"year": "1999", "text": "China Airlines Flight 642 crashes at Hong Kong International Airport, killing three people and injuring 208 more.", "html": "1999 - <a href=\"https://wikipedia.org/wiki/China_Airlines_Flight_642\" title=\"China Airlines Flight 642\">China Airlines Flight 642</a> crashes at <a href=\"https://wikipedia.org/wiki/Hong_Kong_International_Airport\" title=\"Hong Kong International Airport\">Hong Kong International Airport</a>, killing three people and injuring 208 more.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/China_Airlines_Flight_642\" title=\"China Airlines Flight 642\">China Airlines Flight 642</a> crashes at <a href=\"https://wikipedia.org/wiki/Hong_Kong_International_Airport\" title=\"Hong Kong International Airport\">Hong Kong International Airport</a>, killing three people and injuring 208 more.", "links": [{"title": "China Airlines Flight 642", "link": "https://wikipedia.org/wiki/China_Airlines_Flight_642"}, {"title": "Hong Kong International Airport", "link": "https://wikipedia.org/wiki/Hong_Kong_International_Airport"}]}, {"year": "2003", "text": "Alabama Chief Justice <PERSON> is suspended after refusing to comply with a federal court order to remove a rock inscribed with the Ten Commandments from the lobby of the Alabama Supreme Court building.", "html": "2003 - <a href=\"https://wikipedia.org/wiki/Alabama\" title=\"Alabama\">Alabama</a> <a href=\"https://wikipedia.org/wiki/Chief_Justice\" class=\"mw-redirect\" title=\"Chief Justice\">Chief Justice</a> <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> is suspended after refusing to comply with a federal court order to remove a rock inscribed with the <a href=\"https://wikipedia.org/wiki/Ten_Commandments\" title=\"Ten Commandments\">Ten Commandments</a> from the lobby of the <a href=\"https://wikipedia.org/wiki/Alabama_Supreme_Court\" class=\"mw-redirect\" title=\"Alabama Supreme Court\">Alabama Supreme Court</a> building.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Alabama\" title=\"Alabama\">Alabama</a> <a href=\"https://wikipedia.org/wiki/Chief_Justice\" class=\"mw-redirect\" title=\"Chief Justice\">Chief Justice</a> <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> is suspended after refusing to comply with a federal court order to remove a rock inscribed with the <a href=\"https://wikipedia.org/wiki/Ten_Commandments\" title=\"Ten Commandments\">Ten Commandments</a> from the lobby of the <a href=\"https://wikipedia.org/wiki/Alabama_Supreme_Court\" class=\"mw-redirect\" title=\"Alabama Supreme Court\">Alabama Supreme Court</a> building.", "links": [{"title": "Alabama", "link": "https://wikipedia.org/wiki/Alabama"}, {"title": "Chief Justice", "link": "https://wikipedia.org/wiki/Chief_Justice"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Ten Commandments", "link": "https://wikipedia.org/wiki/Ten_Commandments"}, {"title": "Alabama Supreme Court", "link": "https://wikipedia.org/wiki/Alabama_Supreme_Court"}]}, {"year": "2004", "text": "Versions of <PERSON> Scream and <PERSON>, two paintings by <PERSON><PERSON>, are stolen at gunpoint from a museum in Oslo, Norway.", "html": "2004 - Versions of <i><a href=\"https://wikipedia.org/wiki/The_Scream\" title=\"The Scream\">The Scream</a></i> and <i><a href=\"https://wikipedia.org/wiki/<PERSON>_(Munch)\" title=\"<PERSON> (Munch)\">Madonna</a></i>, two paintings by <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>nch\"><PERSON><PERSON></a>, are stolen at gunpoint from a museum in <a href=\"https://wikipedia.org/wiki/Oslo\" title=\"Oslo\">Oslo</a>, Norway.", "no_year_html": "Versions of <i><a href=\"https://wikipedia.org/wiki/The_Scream\" title=\"The Scream\">The Scream</a></i> and <i><a href=\"https://wikipedia.org/wiki/<PERSON>_(Munch)\" title=\"<PERSON> (Munch)\">Madonna</a></i>, two paintings by <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>nch\"><PERSON><PERSON></a>, are stolen at gunpoint from a museum in <a href=\"https://wikipedia.org/wiki/Oslo\" title=\"Oslo\">Oslo</a>, Norway.", "links": [{"title": "The Scream", "link": "https://wikipedia.org/wiki/The_Scream"}, {"title": "<PERSON> (Munch)", "link": "https://wikipedia.org/wiki/<PERSON>_(Munch)"}, {"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}, {"title": "Oslo", "link": "https://wikipedia.org/wiki/Oslo"}]}, {"year": "2006", "text": "Pulkovo Aviation Enterprise Flight 612 crashes near the Russian border over eastern Ukraine, killing all 170 people on board.", "html": "2006 - <a href=\"https://wikipedia.org/wiki/Pulkovo_Aviation_Enterprise_Flight_612\" title=\"Pulkovo Aviation Enterprise Flight 612\">Pulkovo Aviation Enterprise Flight 612</a> crashes near the Russian border over eastern <a href=\"https://wikipedia.org/wiki/Ukraine\" title=\"Ukraine\">Ukraine</a>, killing all 170 people on board.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Pulkovo_Aviation_Enterprise_Flight_612\" title=\"Pulkovo Aviation Enterprise Flight 612\">Pulkovo Aviation Enterprise Flight 612</a> crashes near the Russian border over eastern <a href=\"https://wikipedia.org/wiki/Ukraine\" title=\"Ukraine\">Ukraine</a>, killing all 170 people on board.", "links": [{"title": "Pulkovo Aviation Enterprise Flight 612", "link": "https://wikipedia.org/wiki/Pulkovo_Aviation_Enterprise_Flight_612"}, {"title": "Ukraine", "link": "https://wikipedia.org/wiki/Ukraine"}]}, {"year": "2006", "text": "<PERSON><PERSON><PERSON> is awarded the Fields Medal for his proof of the <PERSON><PERSON><PERSON><PERSON> conjecture in mathematics but refuses to accept the medal.", "html": "2006 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a> is awarded the <a href=\"https://wikipedia.org/wiki/Fields_Medal\" title=\"Fields Medal\">Fields Medal</a> for his proof of the <a href=\"https://wikipedia.org/wiki/Poincar%C3%A9_conjecture\" title=\"Poincaré conjecture\">Poincaré conjecture</a> in mathematics but refuses to accept the medal.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a> is awarded the <a href=\"https://wikipedia.org/wiki/Fields_Medal\" title=\"Fields Medal\">Fields Medal</a> for his proof of the <a href=\"https://wikipedia.org/wiki/Poincar%C3%A9_conjecture\" title=\"Poincaré conjecture\">Poincaré conjecture</a> in mathematics but refuses to accept the medal.", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}, {"title": "Fields Medal", "link": "https://wikipedia.org/wiki/Fields_Medal"}, {"title": "<PERSON><PERSON><PERSON><PERSON> conjecture", "link": "https://wikipedia.org/wiki/Poincar%C3%A9_conjecture"}]}, {"year": "2007", "text": "The Texas Rangers defeat the Baltimore Orioles 30-3, the most runs scored by a team in modern Major League Baseball history.", "html": "2007 - The <a href=\"https://wikipedia.org/wiki/Texas_Rangers_(baseball)\" title=\"Texas Rangers (baseball)\">Texas Rangers</a> defeat the <a href=\"https://wikipedia.org/wiki/Baltimore_Orioles\" title=\"Baltimore Orioles\">Baltimore Orioles</a> 30-3, the most runs scored by a team in modern <a href=\"https://wikipedia.org/wiki/Major_League_Baseball\" title=\"Major League Baseball\">Major League Baseball</a> history.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Texas_Rangers_(baseball)\" title=\"Texas Rangers (baseball)\">Texas Rangers</a> defeat the <a href=\"https://wikipedia.org/wiki/Baltimore_Orioles\" title=\"Baltimore Orioles\">Baltimore Orioles</a> 30-3, the most runs scored by a team in modern <a href=\"https://wikipedia.org/wiki/Major_League_Baseball\" title=\"Major League Baseball\">Major League Baseball</a> history.", "links": [{"title": "Texas Rangers (baseball)", "link": "https://wikipedia.org/wiki/Texas_Rangers_(baseball)"}, {"title": "Baltimore Orioles", "link": "https://wikipedia.org/wiki/Baltimore_Orioles"}, {"title": "Major League Baseball", "link": "https://wikipedia.org/wiki/Major_League_Baseball"}]}, {"year": "2012", "text": "Ethnic clashes over grazing rights for cattle in Kenya's Tana River District result in more than 52 deaths.", "html": "2012 - <a href=\"https://wikipedia.org/wiki/2012%E2%80%9313_Tana_River_District_clashes\" class=\"mw-redirect\" title=\"2012-13 Tana River District clashes\">Ethnic clashes</a> over <a href=\"https://wikipedia.org/wiki/Grazing_rights\" title=\"Grazing rights\">grazing rights</a> for cattle in Kenya's <a href=\"https://wikipedia.org/wiki/Tana_River_District\" title=\"Tana River District\">Tana River District</a> result in more than 52 deaths.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/2012%E2%80%9313_Tana_River_District_clashes\" class=\"mw-redirect\" title=\"2012-13 Tana River District clashes\">Ethnic clashes</a> over <a href=\"https://wikipedia.org/wiki/Grazing_rights\" title=\"Grazing rights\">grazing rights</a> for cattle in Kenya's <a href=\"https://wikipedia.org/wiki/Tana_River_District\" title=\"Tana River District\">Tana River District</a> result in more than 52 deaths.", "links": [{"title": "2012-13 Tana River District clashes", "link": "https://wikipedia.org/wiki/2012%E2%80%9313_Tana_River_District_clashes"}, {"title": "Grazing rights", "link": "https://wikipedia.org/wiki/Grazing_rights"}, {"title": "Tana River District", "link": "https://wikipedia.org/wiki/Tana_River_District"}]}], "Births": [{"year": "1412", "text": "<PERSON>, Elector of Saxony (d. 1464)", "html": "1412 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Elector_of_Saxony\" title=\"<PERSON>, Elector of Saxony\"><PERSON>, Elector of Saxony</a> (d. 1464)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Elector_of_Saxony\" title=\"<PERSON>, Elector of Saxony\"><PERSON>, Elector of Saxony</a> (d. 1464)", "links": [{"title": "<PERSON>, Elector of Saxony", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Elector_of_Saxony"}]}, {"year": "1570", "text": "<PERSON>, Roman Catholic archbishop and cardinal (d. 1636)", "html": "1570 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>\" title=\"<PERSON>\"><PERSON></a>, Roman Catholic archbishop and cardinal (d. 1636)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>\" title=\"<PERSON>\"><PERSON></a>, Roman Catholic archbishop and cardinal (d. 1636)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1599", "text": "<PERSON> of Hanau, German noblewoman (d. 1636)", "html": "1599 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_Han<PERSON>\" title=\"<PERSON> of Hanau\"><PERSON> of Hanau</a>, German noblewoman (d. 1636)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON> of Hanau\"><PERSON> of Hanau</a>, German noblewoman (d. 1636)", "links": [{"title": "<PERSON> Hanau", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1601", "text": "<PERSON>, French author, poet, and playwright (d. 1667)", "html": "1601 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>%C3%A9ry\" title=\"<PERSON>\"><PERSON></a>, French author, poet, and playwright (d. 1667)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>%C3%A9ry\" title=\"<PERSON>\"><PERSON></a>, French author, poet, and playwright (d. 1667)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON><PERSON>%C3%A9ry"}]}, {"year": "1624", "text": "<PERSON>, French author and poet (d. 1701)", "html": "1624 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French author and poet (d. 1701)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French author and poet (d. 1701)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1647", "text": "<PERSON>, French physicist and mathematician, developed pressure cooking (d. 1712)", "html": "1647 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French physicist and mathematician, developed <a href=\"https://wikipedia.org/wiki/Pressure_cooking\" class=\"mw-redirect\" title=\"Pressure cooking\">pressure cooking</a> (d. 1712)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French physicist and mathematician, developed <a href=\"https://wikipedia.org/wiki/Pressure_cooking\" class=\"mw-redirect\" title=\"Pressure cooking\">pressure cooking</a> (d. 1712)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Pressure cooking", "link": "https://wikipedia.org/wiki/Pressure_cooking"}]}, {"year": "1679", "text": "<PERSON>, French cardinal (d. 1758)", "html": "1679 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A9<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French cardinal (d. 1758)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French cardinal (d. 1758)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Pierre_<PERSON>u%C3%A9<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1760", "text": "<PERSON> (d. 1829)", "html": "1760 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\">Pope <PERSON></a> (d. 1829)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\">Pope <PERSON></a> (d. 1829)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1764", "text": "<PERSON>, French architect and interior designer (d. 1838)", "html": "1764 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French architect and interior designer (d. 1838)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French architect and interior designer (d. 1838)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1771", "text": "<PERSON>, English engineer (d. 1831)", "html": "1771 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English engineer (d. 1831)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English engineer (d. 1831)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1773", "text": "<PERSON><PERSON>, French botanist and explorer (d. 1858)", "html": "1773 - <a href=\"https://wikipedia.org/wiki/Aim%C3%A9_Bonpland\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, French botanist and explorer (d. 1858)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Aim%C3%A9_Bonpland\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, French botanist and explorer (d. 1858)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Aim%C3%A9_<PERSON><PERSON>land"}]}, {"year": "1778", "text": "<PERSON>, American poet, playwright, and politician, 11th United States Secretary of the Navy (d. 1860)", "html": "1778 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American poet, playwright, and politician, 11th <a href=\"https://wikipedia.org/wiki/United_States_Secretary_of_the_Navy\" title=\"United States Secretary of the Navy\">United States Secretary of the Navy</a> (d. 1860)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American poet, playwright, and politician, 11th <a href=\"https://wikipedia.org/wiki/United_States_Secretary_of_the_Navy\" title=\"United States Secretary of the Navy\">United States Secretary of the Navy</a> (d. 1860)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "United States Secretary of the Navy", "link": "https://wikipedia.org/wiki/United_States_Secretary_of_the_Navy"}]}, {"year": "1800", "text": "<PERSON>, Italian poet and scholar (d. 1865)", "html": "1800 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian poet and scholar (d. 1865)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian poet and scholar (d. 1865)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1818", "text": "<PERSON>, German jurist (d. 1892)", "html": "1818 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German jurist (d. 1892)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German jurist (d. 1892)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1827", "text": "<PERSON>, Canadian businessman and politician (d. 1906)", "html": "1827 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian businessman and politician (d. 1906)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian businessman and politician (d. 1906)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1834", "text": "<PERSON>, American physicist and astronomer (d. 1906)", "html": "1834 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, American physicist and astronomer (d. 1906)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, American physicist and astronomer (d. 1906)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1836", "text": "<PERSON>, American soldier and painter (d. 1918)", "html": "1836 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American soldier and painter (d. 1918)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American soldier and painter (d. 1918)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1844", "text": "<PERSON>, American Naval officer and explorer (d. 1881)", "html": "1844 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American Naval officer and explorer (d. 1881)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American Naval officer and explorer (d. 1881)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1845", "text": "<PERSON>, American businessman and politician, 42nd Governor of Massachusetts (d. 1924)", "html": "1845 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American businessman and politician, 42nd <a href=\"https://wikipedia.org/wiki/Governor_of_Massachusetts\" title=\"Governor of Massachusetts\">Governor of Massachusetts</a> (d. 1924)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American businessman and politician, 42nd <a href=\"https://wikipedia.org/wiki/Governor_of_Massachusetts\" title=\"Governor of Massachusetts\">Governor of Massachusetts</a> (d. 1924)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "Governor of Massachusetts", "link": "https://wikipedia.org/wiki/Governor_of_Massachusetts"}]}, {"year": "1847", "text": "<PERSON>, Australian politician, 1st Premier of Western Australia (d. 1918)", "html": "1847 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian politician, 1st <a href=\"https://wikipedia.org/wiki/Premier_of_Western_Australia\" title=\"Premier of Western Australia\">Premier of Western Australia</a> (d. 1918)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian politician, 1st <a href=\"https://wikipedia.org/wiki/Premier_of_Western_Australia\" title=\"Premier of Western Australia\">Premier of Western Australia</a> (d. 1918)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Premier of Western Australia", "link": "https://wikipedia.org/wiki/Premier_of_Western_Australia"}]}, {"year": "1848", "text": "<PERSON>, American publisher, founded the Chicago Daily News (d. 1929)", "html": "1848 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American publisher, founded the <i><a href=\"https://wikipedia.org/wiki/Chicago_Daily_News\" title=\"Chicago Daily News\">Chicago Daily News</a></i> (d. 1929)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American publisher, founded the <i><a href=\"https://wikipedia.org/wiki/Chicago_Daily_News\" title=\"Chicago Daily News\">Chicago Daily News</a></i> (d. 1929)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "Chicago Daily News", "link": "https://wikipedia.org/wiki/Chicago_Daily_News"}]}, {"year": "1854", "text": "Milan I of Serbia (d. 1901)", "html": "1854 - <a href=\"https://wikipedia.org/wiki/Milan_I_of_Serbia\" title=\"Milan I of Serbia\">Milan I of Serbia</a> (d. 1901)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Milan_I_of_Serbia\" title=\"Milan I of Serbia\">Milan I of Serbia</a> (d. 1901)", "links": [{"title": "Milan I of Serbia", "link": "https://wikipedia.org/wiki/Milan_I_of_Serbia"}]}, {"year": "1857", "text": "<PERSON>, American baseball player and manager (d. 1937)", "html": "1857 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(baseball)\" title=\"<PERSON> (baseball)\"><PERSON></a>, American baseball player and manager (d. 1937)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(baseball)\" title=\"<PERSON> (baseball)\"><PERSON></a>, American baseball player and manager (d. 1937)", "links": [{"title": "<PERSON> (baseball)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(baseball)"}]}, {"year": "1860", "text": "<PERSON>, Polish-German technician and inventor, created the <PERSON><PERSON><PERSON><PERSON> disk (d. 1940)", "html": "1860 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Polish-German technician and inventor, created the <a href=\"https://wikipedia.org/wiki/Nipkow_disk\" title=\"Ni<PERSON><PERSON><PERSON> disk\">Ni<PERSON><PERSON>w disk</a> (d. 1940)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Polish-German technician and inventor, created the <a href=\"https://wikipedia.org/wiki/Nipkow_disk\" title=\"<PERSON><PERSON><PERSON><PERSON> disk\"><PERSON><PERSON><PERSON>w disk</a> (d. 1940)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "Nipkow disk", "link": "https://wikipedia.org/wiki/Nipkow_disk"}]}, {"year": "1860", "text": "<PERSON>, German physician, biologist, and eugenicist (d. 1940)", "html": "1860 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German physician, biologist, and eugenicist (d. 1940)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German physician, biologist, and eugenicist (d. 1940)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1862", "text": "<PERSON>, French pianist and composer (d. 1918)", "html": "1862 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French pianist and composer (d. 1918)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French pianist and composer (d. 1918)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1867", "text": "<PERSON>, Swiss physician and nutritionist (d. 1939)", "html": "1867 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swiss physician and nutritionist (d. 1939)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swiss physician and nutritionist (d. 1939)", "links": [{"title": "<PERSON>-<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>-<PERSON>"}]}, {"year": "1867", "text": "<PERSON>, American inventor (d. 1934)", "html": "1867 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American inventor (d. 1934)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American inventor (d. 1934)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1868", "text": "<PERSON>, American chemist (d. 1958)", "html": "1868 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American chemist (d. 1958)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American chemist (d. 1958)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "1873", "text": "<PERSON>, Russian physician and philosopher (d. 1928)", "html": "1873 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian physician and philosopher (d. 1928)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian physician and philosopher (d. 1928)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1874", "text": "<PERSON>, German philosopher and author (d. 1928)", "html": "1874 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German philosopher and author (d. 1928)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German philosopher and author (d. 1928)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1880", "text": "<PERSON><PERSON>, German author and poet (d. 1916)", "html": "1880 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_(author)\" title=\"<PERSON><PERSON> (author)\"><PERSON><PERSON></a>, German author and poet (d. 1916)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_(author)\" title=\"<PERSON><PERSON> (author)\"><PERSON><PERSON></a>, German author and poet (d. 1916)", "links": [{"title": "<PERSON><PERSON> (author)", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_(author)"}]}, {"year": "1880", "text": "<PERSON>, American cartoonist (d. 1944)", "html": "1880 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American cartoonist (d. 1944)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American cartoonist (d. 1944)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1881", "text": "<PERSON><PERSON>, English Dominican priest (d. 1934)", "html": "1881 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, English Dominican priest (d. 1934)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, English Dominican priest (d. 1934)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1881", "text": "<PERSON>, Australian soldier and policeman (d. 1949)", "html": "1881 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"James <PERSON>\"><PERSON></a>, Australian soldier and policeman (d. 1949)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"James <PERSON>\"><PERSON></a>, Australian soldier and policeman (d. 1949)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1882", "text": "<PERSON><PERSON>, French pilot (d. 1919)", "html": "1882 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, French pilot (d. 1919)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, French pilot (d. 1919)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1887", "text": "<PERSON><PERSON>, German jurist and politician, German Minister of Foreign Affairs (d. 1977)", "html": "1887 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, German jurist and politician, <a href=\"https://wikipedia.org/wiki/Minister_for_Foreign_Affairs_(Germany)\" title=\"Minister for Foreign Affairs (Germany)\">German Minister of Foreign Affairs</a> (d. 1977)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON> <PERSON></a>, German jurist and politician, <a href=\"https://wikipedia.org/wiki/Minister_for_Foreign_Affairs_(Germany)\" title=\"Minister for Foreign Affairs (Germany)\">German Minister of Foreign Affairs</a> (d. 1977)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>"}, {"title": "Minister for Foreign Affairs (Germany)", "link": "https://wikipedia.org/wiki/Minister_for_Foreign_Affairs_(Germany)"}]}, {"year": "1890", "text": "<PERSON>, South African actor (d. 1973)", "html": "1890 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, South African actor (d. 1973)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, South African actor (d. 1973)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1891", "text": "<PERSON>, Australian soldier and railway engineer (d. 1983)", "html": "1891 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian soldier and railway engineer (d. 1983)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian soldier and railway engineer (d. 1983)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1891", "text": "<PERSON>, Lithuanian-Italian sculptor (d. 1973)", "html": "1891 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Lithuanian-Italian sculptor (d. 1973)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Lithuanian-Italian sculptor (d. 1973)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1893", "text": "<PERSON><PERSON><PERSON>, English 7th General of The Salvation Army (d. 1977)", "html": "1893 - <a href=\"https://wikipedia.org/wiki/W<PERSON><PERSON>_<PERSON>\" title=\"W<PERSON><PERSON>\">W<PERSON><PERSON></a>, English 7th <a href=\"https://wikipedia.org/wiki/General_of_The_Salvation_Army\" title=\"General of The Salvation Army\">General of The Salvation Army</a> (d. 1977)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/W<PERSON><PERSON>_<PERSON>\" title=\"W<PERSON><PERSON>\">W<PERSON><PERSON></a>, English 7th <a href=\"https://wikipedia.org/wiki/General_of_The_Salvation_Army\" title=\"General of The Salvation Army\">General of The Salvation Army</a> (d. 1977)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/W<PERSON><PERSON>_<PERSON>"}, {"title": "General of The Salvation Army", "link": "https://wikipedia.org/wiki/General_of_The_Salvation_Army"}]}, {"year": "1893", "text": "<PERSON>, American poet, short story writer, critic, and satirist (d. 1967)", "html": "1893 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American poet, short story writer, critic, and satirist (d. 1967)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American poet, short story writer, critic, and satirist (d. 1967)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1893", "text": "<PERSON>, American chemist (d. 1992)", "html": "1893 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American chemist (d. 1992)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American chemist (d. 1992)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1895", "text": "<PERSON><PERSON><PERSON><PERSON><PERSON>, Hungarian captain, pilot, and explorer (d. 1951)", "html": "1895 - <a href=\"https://wikipedia.org/wiki/L%C3%A1szl%C3%B3_Alm%C3%A1sy\" title=\"<PERSON><PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON><PERSON></a>, Hungarian captain, pilot, and explorer (d. 1951)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/L%C3%A1szl%C3%B3_Alm%C3%A1sy\" title=\"<PERSON><PERSON><PERSON><PERSON>ó <PERSON>\"><PERSON><PERSON><PERSON><PERSON><PERSON></a>, Hungarian captain, pilot, and explorer (d. 1951)", "links": [{"title": "László <PERSON>", "link": "https://wikipedia.org/wiki/L%C3%A1szl%C3%B3_Alm%C3%A1sy"}]}, {"year": "1895", "text": "<PERSON>, Canadian lawyer and politician, 21st Lieutenant Governor of Quebec (d. 1966)", "html": "1895 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian lawyer and politician, 21st <a href=\"https://wikipedia.org/wiki/Lieutenant_Governor_of_Quebec\" title=\"Lieutenant Governor of Quebec\">Lieutenant Governor of Quebec</a> (d. 1966)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian lawyer and politician, 21st <a href=\"https://wikipedia.org/wiki/Lieutenant_Governor_of_Quebec\" title=\"Lieutenant Governor of Quebec\">Lieutenant Governor of Quebec</a> (d. 1966)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}, {"title": "Lieutenant Governor of Quebec", "link": "https://wikipedia.org/wiki/Lieutenant_Governor_of_Quebec"}]}, {"year": "1896", "text": "<PERSON>, American geologist, educator, and polar explorer (d. 1995)", "html": "1896 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American geologist, educator, and polar explorer (d. 1995)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American geologist, educator, and polar explorer (d. 1995)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1897", "text": "<PERSON>, Australian cricketer and educator (d. 1965)", "html": "1897 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian cricketer and educator (d. 1965)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian cricketer and educator (d. 1965)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1900", "text": "<PERSON><PERSON>, Swiss-born pianist and child prodigy (d. 1999)", "html": "1900 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Swiss-born pianist and child prodigy (d. 1999)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Swiss-born pianist and child prodigy (d. 1999)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_Fischer"}]}, {"year": "1902", "text": "<PERSON>, American lawyer and politician (d. 1973)", "html": "1902 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and politician (d. 1973)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and politician (d. 1973)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1902", "text": "<PERSON><PERSON>, German actress, film director and propagandist (d. 2003)", "html": "1902 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, German actress, film director and propagandist (d. 2003)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, German actress, film director and propagandist (d. 2003)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>i_R<PERSON>tahl"}]}, {"year": "1902", "text": "<PERSON>, American historian and author (d. 1982)", "html": "1902 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American historian and author (d. 1982)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American historian and author (d. 1982)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1903", "text": "<PERSON>, American cartoonist, co-founded Eisner & Iger (d. 1990)", "html": "1903 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American cartoonist, co-founded <a href=\"https://wikipedia.org/wiki/E<PERSON>ner_%26_Iger\" title=\"<PERSON><PERSON><PERSON> &amp; Iger\">E<PERSON>ner &amp; Iger</a> (d. 1990)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American cartoonist, co-founded <a href=\"https://wikipedia.org/wiki/E<PERSON>ner_%26_Iger\" title=\"<PERSON><PERSON><PERSON> &amp; Iger\">E<PERSON>ner &amp; Iger</a> (d. 1990)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Eisner & Iger", "link": "https://wikipedia.org/wiki/Eisner_%26_Iger"}]}, {"year": "1904", "text": "<PERSON><PERSON>, Chinese soldier and politician, 1st Vice Premier of the People's Republic of China (d. 1997)", "html": "1904 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Chinese soldier and politician, 1st <a href=\"https://wikipedia.org/wiki/Vice_Premier_of_the_People%27s_Republic_of_China\" class=\"mw-redirect\" title=\"Vice Premier of the People's Republic of China\">Vice Premier of the People's Republic of China</a> (d. 1997)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Chinese soldier and politician, 1st <a href=\"https://wikipedia.org/wiki/Vice_Premier_of_the_People%27s_Republic_of_China\" class=\"mw-redirect\" title=\"Vice Premier of the People's Republic of China\">Vice Premier of the People's Republic of China</a> (d. 1997)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Den<PERSON>_<PERSON>"}, {"title": "Vice Premier of the People's Republic of China", "link": "https://wikipedia.org/wiki/Vice_Premier_of_the_People%27s_Republic_of_China"}]}, {"year": "1908", "text": "<PERSON><PERSON>, French photographer and painter (d. 2004)", "html": "1908 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French photographer and painter (d. 2004)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French photographer and painter (d. 2004)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1908", "text": "<PERSON>, German rugby player and coach (d. 1993)", "html": "1908 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German rugby player and coach (d. 1993)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German rugby player and coach (d. 1993)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>hi<PERSON>es"}]}, {"year": "1909", "text": "<PERSON>, American screenwriter and producer (d. 2000)", "html": "1909 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American screenwriter and producer (d. 2000)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American screenwriter and producer (d. 2000)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1909", "text": "<PERSON>, American football player and coach (d. 1992)", "html": "1909 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player and coach (d. 1992)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player and coach (d. 1992)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>in"}]}, {"year": "1913", "text": "<PERSON>, English businessman and pilot (d. 2008)", "html": "1913 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English businessman and pilot (d. 2008)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English businessman and pilot (d. 2008)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1913", "text": "<PERSON>, Italian physicist and academic (d. 1993)", "html": "1913 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian physicist and academic (d. 1993)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian physicist and academic (d. 1993)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1914", "text": "<PERSON>, American author and playwright (d. 1992)", "html": "1914 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author and playwright (d. 1992)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author and playwright (d. 1992)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1914", "text": "<PERSON>, American businessman, co-founded the Country Music Hall of Fame and Museum (d. 1989)", "html": "1914 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American businessman, co-founded the <a href=\"https://wikipedia.org/wiki/Country_Music_Hall_of_Fame_and_Museum\" title=\"Country Music Hall of Fame and Museum\">Country Music Hall of Fame and Museum</a> (d. 1989)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American businessman, co-founded the <a href=\"https://wikipedia.org/wiki/Country_Music_Hall_of_Fame_and_Museum\" title=\"Country Music Hall of Fame and Museum\">Country Music Hall of Fame and Museum</a> (d. 1989)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>"}, {"title": "Country Music Hall of Fame and Museum", "link": "https://wikipedia.org/wiki/Country_Music_Hall_of_Fame_and_Museum"}]}, {"year": "1915", "text": "<PERSON>, American activist (d. 2004)", "html": "1915 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American activist (d. 2004)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American activist (d. 2004)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1915", "text": "<PERSON>, Canadian-American scientist, co-designed the electron microscope (d. 2007)", "html": "1915 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian-American scientist, co-designed the <a href=\"https://wikipedia.org/wiki/Electron_microscope\" title=\"Electron microscope\">electron microscope</a> (d. 2007)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian-American scientist, co-designed the <a href=\"https://wikipedia.org/wiki/Electron_microscope\" title=\"Electron microscope\">electron microscope</a> (d. 2007)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Electron microscope", "link": "https://wikipedia.org/wiki/Electron_microscope"}]}, {"year": "1915", "text": "<PERSON>, Polish economist and politician, 15th Prime Minister of the Polish Republic in Exile (d. 2005)", "html": "1915 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Polish economist and politician, 15th <a href=\"https://wikipedia.org/wiki/Polish_government-in-exile\" title=\"Polish government-in-exile\">Prime Minister of the Polish Republic in Exile</a> (d. 2005)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Polish economist and politician, 15th <a href=\"https://wikipedia.org/wiki/Polish_government-in-exile\" title=\"Polish government-in-exile\">Prime Minister of the Polish Republic in Exile</a> (d. 2005)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Polish government-in-exile", "link": "https://wikipedia.org/wiki/Polish_government-in-exile"}]}, {"year": "1917", "text": "<PERSON>, American singer-songwriter and guitarist (d. 2001)", "html": "1917 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and guitarist (d. 2001)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and guitarist (d. 2001)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1918", "text": "<PERSON>, American journalist and author (d. 2004)", "html": "1918 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American journalist and author (d. 2004)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American journalist and author (d. 2004)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1920", "text": "<PERSON>, American science fiction writer and screenwriter (d. 2012)", "html": "1920 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American science fiction writer and screenwriter (d. 2012)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American science fiction writer and screenwriter (d. 2012)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Ray_<PERSON>"}]}, {"year": "1920", "text": "<PERSON>, American surgeon and scientist (d. 2016)", "html": "1920 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American surgeon and scientist (d. 2016)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American surgeon and scientist (d. 2016)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1921", "text": "<PERSON><PERSON>, Greek director and screenwriter (d. 2003)", "html": "1921 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Greek director and screenwriter (d. 2003)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Greek director and screenwriter (d. 2003)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1921", "text": "<PERSON>, English cricketer, footballer, and journalist (d. 2012)", "html": "1921 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(cricketer)\" title=\"<PERSON> (cricketer)\"><PERSON></a>, English cricketer, footballer, and journalist (d. 2012)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(cricketer)\" title=\"<PERSON> (cricketer)\"><PERSON></a>, English cricketer, footballer, and journalist (d. 2012)", "links": [{"title": "<PERSON> (cricketer)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(cricketer)"}]}, {"year": "1922", "text": "<PERSON>, Argentine painter and sculptor (d. 1996)", "html": "1922 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Argentine painter and sculptor (d. 1996)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Argentine painter and sculptor (d. 1996)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1922", "text": "<PERSON><PERSON>, Greek-American costume designer (d. 2011)", "html": "1922 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>dge\"><PERSON><PERSON></a>, Greek-American costume designer (d. 2011)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>dge\" title=\"<PERSON><PERSON>dge\"><PERSON><PERSON></a>, Greek-American costume designer (d. 2011)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>dge"}]}, {"year": "1922", "text": "<PERSON>, American science fiction and fantasy artist (d. 2005)", "html": "1922 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American science fiction and fantasy artist (d. 2005)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American science fiction and fantasy artist (d. 2005)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1924", "text": "<PERSON>, Jr., American playwright and author (d. 1989)", "html": "1924 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Jr.\" class=\"mw-redirect\" title=\"<PERSON>, Jr.\"><PERSON>, Jr.</a>, American playwright and author (d. 1989)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Jr.\" class=\"mw-redirect\" title=\"<PERSON>, Jr.\"><PERSON>, Jr.</a>, American playwright and author (d. 1989)", "links": [{"title": "<PERSON>, Jr.", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Jr."}]}, {"year": "1924", "text": "<PERSON><PERSON><PERSON>, Indian writer, satirist and humorist (d. 1995)", "html": "1924 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Indian writer, satirist and humorist (d. 1995)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Indian writer, satirist and humorist (d. 1995)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>i"}]}, {"year": "1925", "text": "<PERSON>, English actress and republican (d. 2020)", "html": "1925 - <a href=\"https://wikipedia.org/wiki/Honor_Blackman\" title=\"Honor Blackman\"><PERSON></a>, English actress and republican (d. 2020)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Honor_Blackman\" title=\"Honor Blackman\"><PERSON> Blackman</a>, English actress and republican (d. 2020)", "links": [{"title": "<PERSON> Blackman", "link": "https://wikipedia.org/wiki/Honor_Blackman"}]}, {"year": "1926", "text": "<PERSON>, French fashion designer (d. 2023)", "html": "1926 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French fashion designer (d. 2023)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French fashion designer (d. 2023)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1926", "text": "<PERSON>, American pop singer (d. 2011)", "html": "1926 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(singer)\" title=\"<PERSON> (singer)\"><PERSON></a>, American pop singer (d. 2011)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(singer)\" title=\"<PERSON> (singer)\"><PERSON></a>, American pop singer (d. 2011)", "links": [{"title": "<PERSON> (singer)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(singer)"}]}, {"year": "1928", "text": "<PERSON><PERSON>, Sierra Leonean academic and diplomat (d. 2015)", "html": "1928 - <a href=\"https://wikipedia.org/wiki/Tinga_Seisay\" title=\"Tinga Seisay\"><PERSON><PERSON></a>, Sierra Leonean academic and diplomat (d. 2015)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Tinga_Seisay\" title=\"Tinga Seisay\"><PERSON><PERSON></a>, Sierra Leonean academic and diplomat (d. 2015)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Tinga_Seisay"}]}, {"year": "1928", "text": "<PERSON><PERSON><PERSON>, German composer and academic (d. 2007)", "html": "1928 - <a href=\"https://wikipedia.org/wiki/Karlheinz_Stockhausen\" title=\"Karlheinz Stockhausen\"><PERSON><PERSON><PERSON>hausen</a>, German composer and academic (d. 2007)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Karlheinz_Stockhausen\" title=\"Karlheinz Stockhausen\"><PERSON><PERSON><PERSON></a>, German composer and academic (d. 2007)", "links": [{"title": "Karlheinz Stockhausen", "link": "https://wikipedia.org/wiki/Karlheinz_Stockhausen"}]}, {"year": "1929", "text": "<PERSON><PERSON>, Russian anthropologist and author (d. 1991)", "html": "1929 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_(anthropologist)\" title=\"<PERSON><PERSON> (anthropologist)\"><PERSON><PERSON></a>, Russian anthropologist and author (d. 1991)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_(anthropologist)\" title=\"<PERSON><PERSON> (anthropologist)\"><PERSON><PERSON></a>, Russian anthropologist and author (d. 1991)", "links": [{"title": "<PERSON><PERSON> (anthropologist)", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON><PERSON>_(anthropologist)"}]}, {"year": "1929", "text": "<PERSON>, American computer scientist (d. 2024)", "html": "1929 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American computer scientist (d. 2024)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American computer scientist (d. 2024)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1929", "text": "<PERSON>, German police officer and general (d. 2017)", "html": "1929 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German police officer and general (d. 2017)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German police officer and general (d. 2017)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1930", "text": "<PERSON><PERSON><PERSON>, Brazilian footballer (d. 2013)", "html": "1930 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>_Neves\" class=\"mw-redirect\" title=\"<PERSON>yl<PERSON> dos Santos Neves\"><PERSON><PERSON><PERSON> <PERSON></a>, Brazilian footballer (d. 2013)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>_<PERSON>eves\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON> dos Santos Neves\"><PERSON><PERSON><PERSON> <PERSON></a>, Brazilian footballer (d. 2013)", "links": [{"title": "<PERSON><PERSON><PERSON> Santos Neves", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1932", "text": "<PERSON>, American engineer, colonel, and astronaut (d. 2020)", "html": "1932 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, American engineer, colonel, and astronaut (d. 2020)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, American engineer, colonel, and astronaut (d. 2020)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1933", "text": "<PERSON><PERSON><PERSON>, Italian actress (d. 1994)", "html": "1933 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Italian actress (d. 1994)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Italian actress (d. 1994)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>cina"}]}, {"year": "1934", "text": "<PERSON>, Jr., American general and engineer (d. 2012)", "html": "1934 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Jr.\" class=\"mw-redirect\" title=\"<PERSON>, Jr.\"><PERSON>, Jr.</a>, American general and engineer (d. 2012)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Jr.\" class=\"mw-redirect\" title=\"<PERSON>, Jr.\"><PERSON>, Jr.</a>, American general and engineer (d. 2012)", "links": [{"title": "<PERSON>, Jr.", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Jr."}]}, {"year": "1935", "text": "<PERSON>, American novelist, short story writer, and journalist", "html": "1935 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American novelist, short story writer, and journalist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American novelist, short story writer, and journalist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1936", "text": "<PERSON>, American singer-songwriter, guitarist, and producer (d. 2012)", "html": "1936 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter, guitarist, and producer (d. 2012)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter, guitarist, and producer (d. 2012)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1936", "text": "<PERSON>, American journalist and producer (d. 2009)", "html": "1936 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(journalist)\" class=\"mw-redirect\" title=\"<PERSON> (journalist)\"><PERSON></a>, American journalist and producer (d. 2009)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(journalist)\" class=\"mw-redirect\" title=\"<PERSON> (journalist)\"><PERSON></a>, American journalist and producer (d. 2009)", "links": [{"title": "<PERSON> (journalist)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(journalist)"}]}, {"year": "1936", "text": "<PERSON>, American singer-songwriter and guitarist (d. 2010)", "html": "1936 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and guitarist (d. 2010)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and guitarist (d. 2010)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1936", "text": "<PERSON>, German roller coaster designer and engineer, designed the Maverick roller coaster", "html": "1936 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German roller coaster designer and engineer, designed the <a href=\"https://wikipedia.org/wiki/Maverick_(roller_coaster)\" title=\"Maverick (roller coaster)\">Maverick roller coaster</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German roller coaster designer and engineer, designed the <a href=\"https://wikipedia.org/wiki/Maverick_(roller_coaster)\" title=\"Maverick (roller coaster)\">Maverick roller coaster</a>", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Maverick (roller coaster)", "link": "https://wikipedia.org/wiki/Maverick_(roller_coaster)"}]}, {"year": "1938", "text": "<PERSON>, American businesswoman and politician (d. 2013)", "html": "1938 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American businesswoman and politician (d. 2013)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American businesswoman and politician (d. 2013)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1939", "text": "<PERSON>, American actress (d. 2019)", "html": "1939 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress (d. 2019)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress (d. 2019)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1939", "text": "<PERSON>, American baseball player", "html": "1939 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1940", "text": "<PERSON>, American football player and coach (d. 2025)", "html": "1940 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player and coach (d. 2025)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player and coach (d. 2025)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1941", "text": "<PERSON>, American football player and coach", "html": "1941 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player and coach", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1943", "text": "<PERSON><PERSON>, Welsh police commissioner and politician, inaugural First Minister of Wales", "html": "1943 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Welsh police commissioner and politician, inaugural <a href=\"https://wikipedia.org/wiki/First_Minister_of_Wales\" title=\"First Minister of Wales\">First Minister of Wales</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Welsh police commissioner and politician, inaugural <a href=\"https://wikipedia.org/wiki/First_Minister_of_Wales\" title=\"First Minister of Wales\">First Minister of Wales</a>", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}, {"title": "First Minister of Wales", "link": "https://wikipedia.org/wiki/First_Minister_of_Wales"}]}, {"year": "1943", "text": "<PERSON><PERSON><PERSON>, Japanese computer scientist and engineer, co-designed the Intel 4004", "html": "1943 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese computer scientist and engineer, co-designed the <a href=\"https://wikipedia.org/wiki/Intel_4004\" title=\"Intel 4004\">Intel 4004</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese computer scientist and engineer, co-designed the <a href=\"https://wikipedia.org/wiki/Intel_4004\" title=\"Intel 4004\">Intel 4004</a>", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}, {"title": "Intel 4004", "link": "https://wikipedia.org/wiki/Intel_4004"}]}, {"year": "1944", "text": "<PERSON>, English physicist and academic", "html": "1944 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English physicist and academic", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English physicist and academic", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1945", "text": "<PERSON>, American screenwriter and producer", "html": "1945 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American screenwriter and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American screenwriter and producer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1945", "text": "<PERSON>, American singer-songwriter and producer", "html": "1945 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and producer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1947", "text": "<PERSON>, American actress and producer (d. 2023)", "html": "1947 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress and producer (d. 2023)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress and producer (d. 2023)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1948", "text": "<PERSON>, American singer-songwriter and guitarist", "html": "1948 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(musician)\" class=\"mw-redirect\" title=\"<PERSON> (musician)\"><PERSON></a>, American singer-songwriter and guitarist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(musician)\" class=\"mw-redirect\" title=\"<PERSON> (musician)\"><PERSON></a>, American singer-songwriter and guitarist", "links": [{"title": "<PERSON> (musician)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(musician)"}]}, {"year": "1949", "text": "<PERSON>, American swimmer and author", "html": "1949 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American swimmer and author", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American swimmer and author", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1949", "text": "<PERSON><PERSON>, Dutch businessman", "html": "1949 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Dutch businessman", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Dutch businessman", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>voort"}]}, {"year": "1950", "text": "<PERSON>, American baseball player and coach", "html": "1950 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player and coach", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1950", "text": "<PERSON><PERSON><PERSON>, American lawyer and politician, Chief of Staff to the Vice President of the United States", "html": "1950 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"Scooter <PERSON>\"><PERSON><PERSON><PERSON></a>, American lawyer and politician, <a href=\"https://wikipedia.org/wiki/Chief_of_Staff_to_the_Vice_President_of_the_United_States\" title=\"Chief of Staff to the Vice President of the United States\">Chief of Staff to the Vice President of the United States</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"Scooter <PERSON>\"><PERSON><PERSON><PERSON> <PERSON></a>, American lawyer and politician, <a href=\"https://wikipedia.org/wiki/Chief_of_Staff_to_the_Vice_President_of_the_United_States\" title=\"Chief of Staff to the Vice President of the United States\">Chief of Staff to the Vice President of the United States</a>", "links": [{"title": "<PERSON>ooter <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>oot<PERSON>_<PERSON>"}, {"title": "Chief of Staff to the Vice President of the United States", "link": "https://wikipedia.org/wiki/Chief_of_Staff_to_the_Vice_President_of_the_United_States"}]}, {"year": "1952", "text": "<PERSON>, American singer-songwriter and guitarist (d. 1977)", "html": "1952 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and guitarist (d. 1977)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and guitarist (d. 1977)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1953", "text": "<PERSON>, American weightlifter, wrestler, and manager", "html": "1953 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American weightlifter, wrestler, and manager", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American weightlifter, wrestler, and manager", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1955", "text": "<PERSON><PERSON><PERSON><PERSON>, Indian film actor, producer and politician", "html": "1955 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Indian film actor, producer and politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Indian film actor, producer and politician", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>"}]}, {"year": "1956", "text": "<PERSON>, American baseball player and coach", "html": "1956 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player and coach", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1956", "text": "<PERSON>, Australian cricketer", "html": "1956 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(Australian_cricketer)\" title=\"<PERSON> (Australian cricketer)\"><PERSON></a>, Australian cricketer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(Australian_cricketer)\" title=\"<PERSON> (Australian cricketer)\"><PERSON></a>, Australian cricketer", "links": [{"title": "<PERSON> (Australian cricketer)", "link": "https://wikipedia.org/wiki/<PERSON>_(Australian_cricketer)"}]}, {"year": "1957", "text": "<PERSON>, English snooker player, sportscaster, and author", "html": "1957 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English snooker player, sportscaster, and author", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English snooker player, sportscaster, and author", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1957", "text": "<PERSON>, American country music singer-songwriter (d. 2016)", "html": "1957 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American country music singer-songwriter (d. 2016)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American country music singer-songwriter (d. 2016)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1958", "text": "<PERSON><PERSON>, American-Canadian actor", "html": "1958 - <a href=\"https://wikipedia.org/wiki/Col<PERSON>_Fe<PERSON>\" title=\"Colm Feore\"><PERSON><PERSON></a>, American-Canadian actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"Colm Feore\"><PERSON><PERSON></a>, American-Canadian actor", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Colm_Feore"}]}, {"year": "1958", "text": "<PERSON>, American wrestler", "html": "1958 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Ray\"><PERSON></a>, American wrestler", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Ray\"><PERSON></a>, American wrestler", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1958", "text": "<PERSON>, English-born American guitarist and songwriter", "html": "1958 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English-born American guitarist and songwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English-born American guitarist and songwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1959", "text": "<PERSON>, Cuban-American singer-songwriter, bass player, and producer", "html": "1959 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Cuban-American singer-songwriter, bass player, and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Cuban-American singer-songwriter, bass player, and producer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1959", "text": "<PERSON><PERSON>, Danish lawyer and politician, Danish Minister of Finance", "html": "1959 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Danish lawyer and politician, <a href=\"https://wikipedia.org/wiki/List_of_Finance_Ministers_of_Denmark\" class=\"mw-redirect\" title=\"List of Finance Ministers of Denmark\">Danish Minister of Finance</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Danish lawyer and politician, <a href=\"https://wikipedia.org/wiki/List_of_Finance_Ministers_of_Denmark\" class=\"mw-redirect\" title=\"List of Finance Ministers of Denmark\">Danish Minister of Finance</a>", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}, {"title": "List of Finance Ministers of Denmark", "link": "https://wikipedia.org/wiki/List_of_Finance_Ministers_of_Denmark"}]}, {"year": "1959", "text": "<PERSON>, English actor", "html": "1959 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(actor)\" title=\"<PERSON> (actor)\"><PERSON></a>, English actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(actor)\" title=\"<PERSON> (actor)\"><PERSON></a>, English actor", "links": [{"title": "<PERSON> (actor)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(actor)"}]}, {"year": "1960", "text": "<PERSON><PERSON><PERSON>, German footballer and manager", "html": "1960 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>e\" title=\"<PERSON><PERSON><PERSON> Gehrke\"><PERSON><PERSON><PERSON></a>, German footballer and manager", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON> Gehrke\"><PERSON><PERSON><PERSON></a>, German footballer and manager", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>e"}]}, {"year": "1960", "text": "<PERSON>, American country music singer", "html": "1960 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American country music singer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American country music singer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1960", "text": "<PERSON>, American actress and playwright", "html": "1960 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress and playwright", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress and playwright", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1961", "text": "<PERSON>, Argentine singer-songwriter, guitarist, and producer", "html": "1961 - <a href=\"https://wikipedia.org/wiki/Andr%C3%A9s_Calamaro\" title=\"<PERSON>\"><PERSON></a>, Argentine singer-songwriter, guitarist, and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Andr%C3%A9s_Calamaro\" title=\"<PERSON>\"><PERSON></a>, Argentine singer-songwriter, guitarist, and producer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Andr%C3%A9s_Calamaro"}]}, {"year": "1961", "text": "<PERSON>, English singer and musician", "html": "1961 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English singer and musician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English singer and musician", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1961", "text": "<PERSON><PERSON><PERSON>, American singer-songwriter and drummer", "html": "1961 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American singer-songwriter and drummer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American singer-songwriter and drummer", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1962", "text": "<PERSON>, Italian sprinter", "html": "1962 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian sprinter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian sprinter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1963", "text": "<PERSON>, American singer-songwriter, pianist, and producer", "html": "1963 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Amos\"><PERSON></a>, American singer-songwriter, pianist, and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_Amos\" title=\"<PERSON> Amos\"><PERSON></a>, American singer-songwriter, pianist, and producer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1963", "text": "<PERSON>, American R&B/soul singer", "html": "1963 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American R&amp;B/soul singer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American R&amp;B/soul singer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1964", "text": "<PERSON><PERSON>, Swedish-American tennis player and coach", "html": "1964 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Swedish-American tennis player and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Swedish-American tennis player and coach", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>"}]}, {"year": "1965", "text": "<PERSON>, South African-Australian surfer", "html": "1965 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, South African-Australian surfer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, South African-Australian surfer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1965", "text": "<PERSON>, Canadian man, born male but reassigned female and raised as a girl after a botched circumcision (d. 2004)", "html": "1965 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian man, born male but reassigned female and raised as a girl after a botched circumcision (d. 2004)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian man, born male but reassigned female and raised as a girl after a botched circumcision (d. 2004)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1966", "text": "<PERSON><PERSON><PERSON>, American rapper and producer", "html": "1966 - <a href=\"https://wikipedia.org/wiki/GZA\" title=\"GZA\"><PERSON><PERSON><PERSON></a>, American rapper and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/GZA\" title=\"GZA\"><PERSON><PERSON><PERSON></a>, American rapper and producer", "links": [{"title": "GZA", "link": "https://wikipedia.org/wiki/GZA"}]}, {"year": "1966", "text": "<PERSON>, Dutch footballer and manager", "html": "1966 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch footballer and manager", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch footballer and manager", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>ge"}]}, {"year": "1967", "text": "<PERSON><PERSON><PERSON><PERSON>, English actor", "html": "1967 - <a href=\"https://wikipedia.org/wiki/Adewale_A<PERSON>nuoye-Agbaje\" title=\"Adewale Akinnuoye-Agbaje\"><PERSON><PERSON><PERSON> Akinnuoye-Ag<PERSON></a>, English actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Adewale_A<PERSON>nuoye-Agbaje\" title=\"Adewale Akinnuoye-Agbaje\"><PERSON><PERSON><PERSON> A<PERSON>nuoy<PERSON>-A<PERSON></a>, English actor", "links": [{"title": "Adewale Akinnuoye-Agbaje", "link": "https://wikipedia.org/wiki/Adewale_Akinnuoye-Ag<PERSON>je"}]}, {"year": "1967", "text": "<PERSON>, American actor and comedian", "html": "1967 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor and comedian", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor and comedian", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1967", "text": "<PERSON>, Australian singer-songwriter and guitarist", "html": "1967 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian singer-songwriter and guitarist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian singer-songwriter and guitarist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1967", "text": "<PERSON>, American singer-songwriter (d. 2002)", "html": "1967 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter (d. 2002)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter (d. 2002)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>aley"}]}, {"year": "1968", "text": "<PERSON>, Danish comedian, actor, and screenwriter", "html": "1968 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Danish comedian, actor, and screenwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Danish comedian, actor, and screenwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1968", "text": "<PERSON>, Russian footballer", "html": "1968 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1968", "text": "<PERSON>, Australian businesswoman", "html": "1968 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(businesswoman)\" title=\"<PERSON> (businesswoman)\"><PERSON></a>, Australian businesswoman", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(businesswoman)\" title=\"<PERSON> (businesswoman)\"><PERSON></a>, Australian businesswoman", "links": [{"title": "<PERSON> (businesswoman)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>(businesswoman)"}]}, {"year": "1968", "text": "<PERSON><PERSON>, Austrian tennis player (d. 2008)", "html": "1968 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Austrian tennis player (d. 2008)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Austrian tennis player (d. 2008)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1970", "text": "<PERSON>, English author and broadcaster", "html": "1970 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English author and broadcaster", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English author and broadcaster", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1970", "text": "<PERSON><PERSON><PERSON>, Italian-American chef and author", "html": "1970 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"Giada <PERSON>\"><PERSON><PERSON><PERSON></a>, Italian-American chef and author", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"Gia<PERSON>\"><PERSON><PERSON><PERSON></a>, Italian-American chef and author", "links": [{"title": "Giada <PERSON>", "link": "https://wikipedia.org/wiki/Gia<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1970", "text": "<PERSON><PERSON><PERSON><PERSON>, Hungarian fencer", "html": "1970 - <a href=\"https://wikipedia.org/wiki/T%C3%ADmea_Nagy\" title=\"Tímea Nagy\">T<PERSON><PERSON><PERSON></a>, Hungarian fencer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/T%C3%ADmea_Nagy\" title=\"Tímea Nagy\"><PERSON><PERSON><PERSON><PERSON></a>, Hungarian fencer", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/T%C3%ADmea_Nagy"}]}, {"year": "1971", "text": "<PERSON>, English actor", "html": "1971 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(actor)\" title=\"<PERSON> (actor)\"><PERSON></a>, English actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(actor)\" title=\"<PERSON> (actor)\"><PERSON></a>, English actor", "links": [{"title": "<PERSON> (actor)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(actor)"}]}, {"year": "1971", "text": "<PERSON>, American singer-songwriter and guitarist", "html": "1971 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and guitarist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and guitarist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1971", "text": "<PERSON>, American actor", "html": "1971 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1972", "text": "<PERSON><PERSON><PERSON>, South African pole vaulter", "html": "1972 - <a href=\"https://wikipedia.org/wiki/Okkert_Brits\" title=\"Okkert Brits\"><PERSON><PERSON><PERSON> B<PERSON></a>, South African pole vaulter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Okkert_Brits\" title=\"Okkert Brits\"><PERSON><PERSON><PERSON> Brits</a>, South African pole vaulter", "links": [{"title": "Okkert Brits", "link": "https://wikipedia.org/wiki/Okkert_Brits"}]}, {"year": "1972", "text": "<PERSON>, American singer-songwriter, guitarist, and drummer", "html": "1972 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter, guitarist, and drummer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter, guitarist, and drummer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1972", "text": "<PERSON>, German-Brazilian race car driver", "html": "1972 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German-Brazilian race car driver", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German-Brazilian race car driver", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1973", "text": "<PERSON><PERSON><PERSON>, Malaysian sport shooter", "html": "1973 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Malaysian sport shooter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Malaysian sport shooter", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1973", "text": "<PERSON><PERSON>, American singer-songwriter and dancer", "html": "1973 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American singer-songwriter and dancer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American singer-songwriter and dancer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1973", "text": "<PERSON>, American actress, comedian, and screenwriter", "html": "1973 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress, comedian, and screenwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress, comedian, and screenwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1973", "text": "<PERSON><PERSON><PERSON><PERSON><PERSON>, Lithuanian basketball player", "html": "1973 - <a href=\"https://wikipedia.org/wiki/Eurelijus_%C5%BDukauskas\" title=\"<PERSON><PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON><PERSON></a>, Lithuanian basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Eurelijus_%C5%BDukauskas\" title=\"<PERSON><PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON><PERSON></a>, Lithuanian basketball player", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Eurelijus_%C5%BDukauskas"}]}, {"year": "1974", "text": "<PERSON>, American politician", "html": "1974 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American politician", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1974", "text": "<PERSON>, American actress and singer", "html": "1974 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress and singer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress and singer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1974", "text": "<PERSON><PERSON><PERSON><PERSON>, Argentinian rugby player", "html": "1974 - <a href=\"https://wikipedia.org/wiki/Agust%C3%ADn_Pichot\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Argentinian rugby player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Agust%C3%ADn_Pichot\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Argentinian rugby player", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Agust%C3%ADn_Pichot"}]}, {"year": "1975", "text": "<PERSON>, Australian footballer", "html": "1975 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1975", "text": "<PERSON>, Brazilian actor", "html": "1975 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Brazilian actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Brazilian actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1976", "text": "<PERSON>, Lithuanian footballer", "html": "1976 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Lithuanian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Lithuanian footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1976", "text": "<PERSON>, American bassist, cellist, and pianist", "html": "1976 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(musician)\" title=\"<PERSON> (musician)\"><PERSON></a>, American bassist, cellist, and pianist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(musician)\" title=\"<PERSON> (musician)\"><PERSON></a>, American bassist, cellist, and pianist", "links": [{"title": "<PERSON> (musician)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(musician)"}]}, {"year": "1976", "text": "<PERSON>, French decathlete", "html": "1976 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French decathlete", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French decathlete", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1976", "text": "<PERSON>, American baseball player", "html": "1976 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1976", "text": "<PERSON>, American baseball player", "html": "1976 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1977", "text": "<PERSON><PERSON><PERSON><PERSON>, Icelandic footballer", "html": "1977 - <a href=\"https://wikipedia.org/wiki/Hei%C3%B0ar_Hel<PERSON>on\" title=\"<PERSON>i<PERSON><PERSON> He<PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Icelandic footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Hei%C3%B0ar_Helguson\" title=\"<PERSON>i<PERSON><PERSON> Hel<PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Icelandic footballer", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Hei%C3%B0ar_Helguson"}]}, {"year": "1977", "text": "<PERSON><PERSON>, Israeli visual artist and writer", "html": "1977 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Israeli visual artist and writer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Israeli visual artist and writer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1978", "text": "<PERSON>, English actor, comedian, writer, and television presenter", "html": "1978 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actor, comedian, writer, and television presenter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actor, comedian, writer, and television presenter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1978", "text": "<PERSON><PERSON><PERSON>, Greek basketball player", "html": "1978 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Greek basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Greek basketball player", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1979", "text": "<PERSON>, American actor", "html": "1979 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(actor)\" title=\"<PERSON> (actor)\"><PERSON></a>, American actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(actor)\" title=\"<PERSON> (actor)\"><PERSON></a>, American actor", "links": [{"title": "<PERSON> (actor)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(actor)"}]}, {"year": "1979", "text": "<PERSON>, American football player", "html": "1979 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1980", "text": "<PERSON>, German footballer", "html": "1980 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1980", "text": "<PERSON>, Canadian sprinter", "html": "1980 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian sprinter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian sprinter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1980", "text": "<PERSON><PERSON>, Japanese wrestler", "html": "1980 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Japanese wrestler", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Japanese wrestler", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1981", "text": "<PERSON>, American football player", "html": "1981 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1981", "text": "<PERSON>, South Korean footballer (d. 2012)", "html": "1981 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>-kyu\" title=\"<PERSON>-kyu\"><PERSON></a>, South Korean footballer (d. 2012)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>-kyu\" title=\"<PERSON>-kyu\"><PERSON></a>, South Korean footballer (d. 2012)", "links": [{"title": "<PERSON>yu", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>-kyu"}]}, {"year": "1981", "text": "<PERSON>, German athlete", "html": "1981 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%B6ll\" title=\"<PERSON>\"><PERSON></a>, German athlete", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%B6ll\" title=\"<PERSON>\"><PERSON></a>, German athlete", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Christina_Obergf%C3%B6ll"}]}, {"year": "1983", "text": "<PERSON>, Dutch cyclist", "html": "1983 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch cyclist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch cyclist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1983", "text": "<PERSON><PERSON><PERSON>, American football player", "html": "1983 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American football player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American football player", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1984", "text": "<PERSON>, English footballer", "html": "1984 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(footballer)\" title=\"<PERSON> (footballer)\"><PERSON></a>, English footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(footballer)\" title=\"<PERSON> (footballer)\"><PERSON></a>, English footballer", "links": [{"title": "<PERSON> (footballer)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(footballer)"}]}, {"year": "1984", "text": "<PERSON>, Ghanaian-Qatari footballer", "html": "1984 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Ghanaian-Qatari footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Ghanaian-Qatari footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1985", "text": "<PERSON>, American journalist", "html": "1985 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American journalist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American journalist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1985", "text": "<PERSON><PERSON>, Samoan-American wrestler", "html": "1985 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Samoan-American wrestler", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Samoan-American wrestler", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>o"}]}, {"year": "1985", "text": "<PERSON>, Samoan-American wrestler", "html": "1985 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Samoan-American wrestler", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Samoan-American wrestler", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1985", "text": "<PERSON><PERSON>, Turkish race car drivr", "html": "1985 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_Yolu%C3%A7\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Turkish race car drivr", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_Yolu%C3%A7\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Turkish race car drivr", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Salih_Yolu%C3%A7"}]}, {"year": "1986", "text": "<PERSON>, Irish footballer", "html": "1986 - <a href=\"https://wikipedia.org/wiki/Stephen_Ireland\" title=\"Stephen Ireland\"><PERSON></a>, Irish footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Stephen_Ireland\" title=\"Stephen Ireland\"><PERSON></a>, Irish footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Stephen_<PERSON>"}]}, {"year": "1986", "text": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, Japanese sumo wrestler", "html": "1986 - <a href=\"https://wikipedia.org/wiki/Tokush%C5%8Dry%C5%<PERSON>_<PERSON><PERSON>\" title=\"Tokus<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON></a>, Japanese sumo wrestler", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Tokush%C5%8Dry%C5%<PERSON>_<PERSON><PERSON>\" title=\"Tokus<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON></a>, Japanese sumo wrestler", "links": [{"title": "To<PERSON><PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Tokush%C5%8Dry%C5%AB_Makoto"}]}, {"year": "1987", "text": "<PERSON>, Italian footballer", "html": "1987 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1987", "text": "<PERSON>s, American wrestler", "html": "1987 - <a href=\"https://wikipedia.org/wiki/Apollo_Crews\" title=\"Apollo Crews\">Apollo Crews</a>, American wrestler", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Apollo_Crews\" title=\"Apollo Crews\">Apollo Crews</a>, American wrestler", "links": [{"title": "Apollo Crews", "link": "https://wikipedia.org/wiki/Apollo_Crews"}]}, {"year": "1989", "text": "<PERSON>, Italian footballer", "html": "1989 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Giacomo_Bonaventura"}]}, {"year": "1990", "text": "<PERSON>, American football player", "html": "1990 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(American_football)\" title=\"<PERSON> (American football)\"><PERSON></a>, American football player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(American_football)\" title=\"<PERSON> (American football)\"><PERSON></a>, American football player", "links": [{"title": "<PERSON> (American football)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(American_football)"}]}, {"year": "1990", "text": "<PERSON>, American baseball player", "html": "1990 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(baseball)\" title=\"<PERSON> (baseball)\"><PERSON></a>, American baseball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(baseball)\" title=\"<PERSON> (baseball)\"><PERSON></a>, American baseball player", "links": [{"title": "<PERSON> (baseball)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(baseball)"}]}, {"year": "1990", "text": "<PERSON>, Australian rugby league player", "html": "1990 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian rugby league player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian rugby league player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1990", "text": "<PERSON>, American football player", "html": "1990 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1991", "text": "<PERSON>, Italian footballer", "html": "1991 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Federico_<PERSON>"}]}, {"year": "1991", "text": "<PERSON><PERSON>, Canadian ice hockey player", "html": "1991 - <a href=\"https://wikipedia.org/wiki/Brayden_Schenn\" title=\"Brayden Schenn\"><PERSON><PERSON></a>, Canadian ice hockey player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Brayden_Schenn\" title=\"Brayden Schenn\"><PERSON><PERSON></a>, Canadian ice hockey player", "links": [{"title": "<PERSON><PERSON>n", "link": "https://wikipedia.org/wiki/Brayden_<PERSON>n"}]}, {"year": "1992", "text": "<PERSON><PERSON>, Bosnian tennis player", "html": "1992 - <a href=\"https://wikipedia.org/wiki/Ema_Burgi%C4%87_Buck<PERSON>\" class=\"mw-redirect\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Bosnian tennis player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Ema_Burgi%C4%87_Bucko\" class=\"mw-redirect\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Bosnian tennis player", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Ema_Burgi%C4%87_Bucko"}]}, {"year": "1993", "text": "<PERSON>, American mixed martial artist", "html": "1993 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Dani<PERSON>\"><PERSON></a>, American mixed martial artist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Dani<PERSON>\"><PERSON></a>, American mixed martial artist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1994", "text": "Israel <PERSON>, American actor", "html": "1994 - <a href=\"https://wikipedia.org/wiki/Israel_Broussard\" title=\"Israel Broussard\">Israel B<PERSON>sar<PERSON></a>, American actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Israel_Broussard\" title=\"Israel Broussard\">Israel Broussard</a>, American actor", "links": [{"title": "Israel Broussard", "link": "https://wikipedia.org/wiki/Israel_Broussard"}]}, {"year": "1994", "text": "<PERSON><PERSON>, Finnish ice hockey player", "html": "1994 - <a href=\"https://wikipedia.org/wiki/Olli_M%C3%A4%C3%A4tt%C3%A4\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Finnish ice hockey player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Olli_M%C3%A4%C3%A4tt%C3%A4\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Finnish ice hockey player", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Olli_M%C3%A4%C3%A4tt%C3%A4"}]}, {"year": "1995", "text": "<PERSON><PERSON>, English singer-songwriter", "html": "1995 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, English singer-songwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, English singer-songwriter", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>"}]}, {"year": "1996", "text": "Jessica<PERSON><PERSON>, British Paralympic swimmer", "html": "1996 - <a href=\"https://wikipedia.org/wiki/<PERSON>-<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, British Paralympic swimmer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>-<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, British Paralympic swimmer", "links": [{"title": "Jessica-<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>-<PERSON>_<PERSON>"}]}, {"year": "1996", "text": "<PERSON><PERSON>, South Korean singer-songwriter", "html": "1996 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_(singer)\" title=\"<PERSON><PERSON> (singer)\"><PERSON><PERSON>-<PERSON></a>, South Korean singer-songwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_(singer)\" title=\"<PERSON><PERSON> (singer)\"><PERSON><PERSON>-<PERSON></a>, South Korean singer-songwriter", "links": [{"title": "<PERSON><PERSON> (singer)", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_(singer)"}]}, {"year": "1997", "text": "<PERSON><PERSON>, American football player", "html": "1997 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American football player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"Max<PERSON> Crosby\"><PERSON><PERSON></a>, American football player", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>x_<PERSON>"}]}, {"year": "1997", "text": "<PERSON><PERSON>, American streamer", "html": "1997 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_(streamer)\" title=\"<PERSON><PERSON> (streamer)\"><PERSON><PERSON></a>, American streamer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_(streamer)\" title=\"<PERSON><PERSON> (streamer)\"><PERSON><PERSON></a>, American streamer", "links": [{"title": "<PERSON><PERSON> (streamer)", "link": "https://wikipedia.org/wiki/Fan<PERSON>_(streamer)"}]}, {"year": "1997", "text": "<PERSON><PERSON><PERSON>, Argentine footballer", "html": "1997 - <a href=\"https://wikipedia.org/wiki/Lautaro_Mart%C3%ADnez\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Argentine footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Lautaro_Mart%C3%ADnez\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Argentine footballer", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Lautaro_Mart%C3%ADnez"}]}, {"year": "2001", "text": "<PERSON><PERSON><PERSON>, American basketball player", "html": "2001 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_Ball\" title=\"LaMelo Ball\"><PERSON><PERSON><PERSON></a>, American basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"LaMelo Ball\"><PERSON><PERSON><PERSON></a>, American basketball player", "links": [{"title": "La<PERSON>elo <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_Ball"}]}, {"year": "2003", "text": "<PERSON>, Australian cricketer", "html": "2003 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian cricketer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian cricketer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2005", "text": "<PERSON><PERSON><PERSON>, Bulgarian rhythmic gymnast", "html": "2005 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Bulgarian rhythmic gymnast", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Bulgarian rhythmic gymnast", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}], "Deaths": [{"year": "408", "text": "<PERSON><PERSON><PERSON>, Roman general (b. 359)", "html": "408 - <a href=\"https://wikipedia.org/wiki/St<PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Roman general (b. 359)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/St<PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Roman general (b. 359)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>"}]}, {"year": "1155", "text": "Emperor <PERSON><PERSON><PERSON> of Japan (b. 1139)", "html": "1155 - <a href=\"https://wikipedia.org/wiki/Emperor_<PERSON><PERSON><PERSON>\" title=\"Emperor <PERSON><PERSON><PERSON>\">Emperor <PERSON><PERSON><PERSON></a> of Japan (b. 1139)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Emperor_<PERSON><PERSON><PERSON>\" title=\"Emperor <PERSON><PERSON><PERSON>\">Emperor <PERSON><PERSON><PERSON></a> of Japan (b. 1139)", "links": [{"title": "Emperor <PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON><PERSON>"}]}, {"year": "1241", "text": "<PERSON>, (b. 1143)", "html": "1241 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON> Gregory IX\"><PERSON> <PERSON> IX</a>, (b. 1143)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON> Gregory <PERSON>\"><PERSON> IX</a>, (b. 1143)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1280", "text": "<PERSON> (b. 1225)", "html": "1280 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"Pope Nicholas III\">Pope <PERSON> III</a> (b. 1225)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"Pope Nicholas <PERSON>\">Pope <PERSON> III</a> (b. 1225)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1304", "text": "<PERSON>, Count of Holland (b. 1247)", "html": "1304 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Count_of_Holland\" title=\"<PERSON>, Count of Holland\"><PERSON>, Count of Holland</a> (b. 1247)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Count_of_Holland\" title=\"<PERSON>, Count of Holland\"><PERSON>, Count of Holland</a> (b. 1247)", "links": [{"title": "<PERSON>, Count of Holland", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Count_of_Holland"}]}, {"year": "1338", "text": "<PERSON>, Duke of Athens (b. 1312)", "html": "1338 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Duke_of_Athens\" title=\"<PERSON>, Duke of Athens\"><PERSON>, Duke of Athens</a> (b. 1312)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Duke_of_Athens\" title=\"<PERSON>, Duke of Athens\"><PERSON>, Duke of Athens</a> (b. 1312)", "links": [{"title": "<PERSON>, Duke of Athens", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Duke_of_Athens"}]}, {"year": "1350", "text": "<PERSON> of France (b. 1293)", "html": "1350 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_France\" title=\"<PERSON> of France\"><PERSON> of France</a> (b. 1293)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_France\" title=\"<PERSON> of France\"><PERSON> of France</a> (b. 1293)", "links": [{"title": "<PERSON> of France", "link": "https://wikipedia.org/wiki/Philip_<PERSON>_of_France"}]}, {"year": "1358", "text": "<PERSON> of France (b. 1295)", "html": "1358 - <a href=\"https://wikipedia.org/wiki/Isabella_of_France\" title=\"<PERSON> of France\"><PERSON> of France</a> (b. 1295)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Isabella_of_France\" title=\"<PERSON> of France\"><PERSON> of France</a> (b. 1295)", "links": [{"title": "<PERSON> of France", "link": "https://wikipedia.org/wiki/Isabella_of_France"}]}, {"year": "1425", "text": "<PERSON>, Princess of Asturias (b. 1423)", "html": "1425 - <a href=\"https://wikipedia.org/wiki/<PERSON>,_Princess_of_Asturias\" title=\"<PERSON>, Princess of Asturias\"><PERSON>, Princess of Asturias</a> (b. 1423)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>,_Princess_of_Asturias\" title=\"<PERSON>, Princess of Asturias\"><PERSON>, Princess of Asturias</a> (b. 1423)", "links": [{"title": "<PERSON>, Princess of Asturias", "link": "https://wikipedia.org/wiki/<PERSON>,_Princess_of_Asturias"}]}, {"year": "1456", "text": "<PERSON><PERSON> of Wallachia", "html": "1456 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_II_of_Wallachia\" title=\"<PERSON><PERSON> II of Wallachia\"><PERSON><PERSON> of Wallachia</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_II_of_Wallachia\" title=\"<PERSON><PERSON> of Wallachia\"><PERSON><PERSON> of Wallachia</a>", "links": [{"title": "<PERSON><PERSON> of Wallachia", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_of_Wallachia"}]}, {"year": "1485", "text": "<PERSON> of England (b. 1452)", "html": "1485 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_England\" title=\"<PERSON> of England\"><PERSON> of England</a> (b. 1452)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_England\" title=\"<PERSON> of England\"><PERSON> of England</a> (b. 1452)", "links": [{"title": "<PERSON> of England", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_England"}]}, {"year": "1485", "text": "<PERSON>, Yorkist knight", "html": "1485 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(Yorkist_knight)\" title=\"<PERSON> (Yorkist knight)\"><PERSON></a>, Yorkist knight", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(Yorkist_knight)\" title=\"<PERSON> (Yorkist knight)\"><PERSON></a>, Yorkist knight", "links": [{"title": "<PERSON> (Yorkist knight)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(Yorkist_knight)"}]}, {"year": "1485", "text": "<PERSON>, 1st Duke of Norfolk (b. 1430)", "html": "1485 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_1st_Duke_of_Norfolk\" title=\"<PERSON>, 1st Duke of Norfolk\"><PERSON>, 1st Duke of Norfolk</a> (b. 1430)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_1st_Duke_of_Norfolk\" title=\"<PERSON>, 1st Duke of Norfolk\"><PERSON>, 1st Duke of Norfolk</a> (b. 1430)", "links": [{"title": "<PERSON>, 1st Duke of Norfolk", "link": "https://wikipedia.org/wiki/<PERSON>,_1st_Duke_of_Norfolk"}]}, {"year": "1485", "text": "<PERSON>, supporter of <PERSON>", "html": "1485 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, supporter of <PERSON>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, supporter of <PERSON>", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1485", "text": "<PERSON>, supporter of <PERSON> (b. 1426)", "html": "1485 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(standard-bearer)\" title=\"<PERSON> (standard-bearer)\"><PERSON></a>, supporter of <PERSON> (b. 1426)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(standard-bearer)\" title=\"<PERSON> (standard-bearer)\"><PERSON></a>, supporter of <PERSON> (b. 1426)", "links": [{"title": "<PERSON> (standard-bearer)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(standard-bearer)"}]}, {"year": "1532", "text": "<PERSON>, Archbishop of Canterbury (b. 1450)", "html": "1532 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, <a href=\"https://wikipedia.org/wiki/Archbishop_of_Canterbury\" title=\"Archbishop of Canterbury\">Archbishop of Canterbury</a> (b. 1450)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, <a href=\"https://wikipedia.org/wiki/Archbishop_of_Canterbury\" title=\"Archbishop of Canterbury\">Archbishop of Canterbury</a> (b. 1450)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Archbishop of Canterbury", "link": "https://wikipedia.org/wiki/Archbishop_of_Canterbury"}]}, {"year": "1545", "text": "<PERSON>, 1st Duke of Suffolk, English politician and husband of <PERSON> (b. c. 1484)", "html": "1545 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_1st_Duke_of_Suffolk\" title=\"<PERSON>, 1st Duke of Suffolk\"><PERSON>, 1st Duke of Suffolk</a>, English politician and husband of <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Queen_of_France\" title=\"<PERSON>, Queen of France\"><PERSON></a> (b. c. 1484)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_1st_Duke_of_Suffolk\" title=\"<PERSON>, 1st Duke of Suffolk\"><PERSON>, 1st Duke of Suffolk</a>, English politician and husband of <a href=\"https://wikipedia.org/wiki/<PERSON>,_Queen_of_France\" title=\"<PERSON>, Queen of France\"><PERSON></a> (b. c. 1484)", "links": [{"title": "<PERSON>, 1st Duke of Suffolk", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_1st_Duke_of_Suffolk"}, {"title": "<PERSON>, Queen of France", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Queen_of_France"}]}, {"year": "1553", "text": "<PERSON>, 1st Duke of Northumberland, English admiral and politician, Lord President of the Council (b. 1504)", "html": "1553 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_1st_Duke_of_Northumberland\" title=\"<PERSON>, 1st Duke of Northumberland\"><PERSON>, 1st Duke of Northumberland</a>, English admiral and politician, <a href=\"https://wikipedia.org/wiki/Lord_President_of_the_Council\" title=\"Lord President of the Council\">Lord President of the Council</a> (b. 1504)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>,_1st_Duke_of_Northumberland\" title=\"<PERSON>, 1st Duke of Northumberland\"><PERSON>, 1st Duke of Northumberland</a>, English admiral and politician, <a href=\"https://wikipedia.org/wiki/Lord_President_of_the_Council\" title=\"Lord President of the Council\">Lord President of the Council</a> (b. 1504)", "links": [{"title": "<PERSON>, 1st Duke of Northumberland", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_1st_Duke_of_Northumberland"}, {"title": "Lord President of the Council", "link": "https://wikipedia.org/wiki/Lord_President_of_the_Council"}]}, {"year": "1572", "text": "<PERSON>, 7th Earl of Northumberland, English leader of the Rising of the North (b. 1528)", "html": "1572 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_7th_Earl_of_Northumberland\" title=\"<PERSON>, 7th Earl of Northumberland\"><PERSON>, 7th Earl of Northumberland</a>, English leader of the <a href=\"https://wikipedia.org/wiki/Rising_of_the_North\" title=\"Rising of the North\">Rising of the North</a> (b. 1528)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_7th_Earl_of_Northumberland\" title=\"<PERSON>, 7th Earl of Northumberland\"><PERSON>, 7th Earl of Northumberland</a>, English leader of the <a href=\"https://wikipedia.org/wiki/Rising_of_the_North\" title=\"Rising of the North\">Rising of the North</a> (b. 1528)", "links": [{"title": "<PERSON>, 7th Earl of Northumberland", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_7th_Earl_of_Northumberland"}, {"title": "Rising of the North", "link": "https://wikipedia.org/wiki/Rising_of_the_North"}]}, {"year": "1584", "text": "<PERSON>, Polish poet and playwright (b. 1530)", "html": "1584 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Polish poet and playwright (b. 1530)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Polish poet and playwright (b. 1530)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1599", "text": "<PERSON>, Italian singer-songwriter (b. 1553)", "html": "1599 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian singer-songwriter (b. 1553)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian singer-songwriter (b. 1553)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1607", "text": "<PERSON>, English lawyer and explorer, founded the London Company (b. 1572)", "html": "1607 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English lawyer and explorer, founded the <a href=\"https://wikipedia.org/wiki/London_Company\" class=\"mw-redirect\" title=\"London Company\">London Company</a> (b. 1572)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English lawyer and explorer, founded the <a href=\"https://wikipedia.org/wiki/London_Company\" class=\"mw-redirect\" title=\"London Company\">London Company</a> (b. 1572)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>ld"}, {"title": "London Company", "link": "https://wikipedia.org/wiki/London_Company"}]}, {"year": "1652", "text": "<PERSON>, Estonian-Swedish soldier and politician, Lord High Constable of Sweden (b. 1583)", "html": "1652 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Estonian-Swedish soldier and politician, <a href=\"https://wikipedia.org/wiki/Lord_High_Constable_of_Sweden\" title=\"Lord High Constable of Sweden\">Lord High Constable of Sweden</a> (b. 1583)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Estonian-Swedish soldier and politician, <a href=\"https://wikipedia.org/wiki/Lord_High_Constable_of_Sweden\" title=\"Lord High Constable of Sweden\">Lord High Constable of Sweden</a> (b. 1583)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>"}, {"title": "Lord High Constable of Sweden", "link": "https://wikipedia.org/wiki/Lord_High_Constable_of_Sweden"}]}, {"year": "1664", "text": "<PERSON>, Polish astronomer and author (b. 1610)", "html": "1664 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Polish astronomer and author (b. 1610)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Polish astronomer and author (b. 1610)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1680", "text": "<PERSON>, Elector of Saxony (b. 1613)", "html": "1680 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>,_Elector_of_Saxony\" title=\"<PERSON>, Elector of Saxony\"><PERSON>, Elector of Saxony</a> (b. 1613)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>,_Elector_of_Saxony\" title=\"<PERSON>, Elector of Saxony\"><PERSON>, Elector of Saxony</a> (b. 1613)", "links": [{"title": "<PERSON>, Elector of Saxony", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>,_Elector_of_Saxony"}]}, {"year": "1681", "text": "<PERSON>, Dutch Plymouth Colony settler (b. 1602)", "html": "1681 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, Dutch Plymouth Colony settler (b. 1602)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, Dutch Plymouth Colony settler (b. 1602)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1701", "text": "<PERSON>, 1st Earl of Bath, English soldier and politician, Lord Lieutenant of Ireland (b. 1628)", "html": "1701 - <a href=\"https://wikipedia.org/wiki/<PERSON>,_1st_Earl_<PERSON>_Bath\" title=\"<PERSON>, 1st Earl of Bath\"><PERSON>, 1st Earl of Bath</a>, English soldier and politician, <a href=\"https://wikipedia.org/wiki/Lord_Lieutenant_of_Ireland\" title=\"Lord Lieutenant of Ireland\">Lord Lieutenant of Ireland</a> (b. 1628)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>,_1st_Earl_<PERSON>_Bath\" title=\"<PERSON>, 1st Earl <PERSON> Bath\"><PERSON>, 1st Earl of Bath</a>, English soldier and politician, <a href=\"https://wikipedia.org/wiki/Lord_Lieutenant_of_Ireland\" title=\"Lord Lieutenant of Ireland\">Lord Lieutenant of Ireland</a> (b. 1628)", "links": [{"title": "<PERSON>, 1st Earl <PERSON> Bath", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_1st_Earl_of_Bath"}, {"title": "Lord Lieutenant of Ireland", "link": "https://wikipedia.org/wiki/Lord_Lieutenant_of_Ireland"}]}, {"year": "1711", "text": "<PERSON>, duc <PERSON>, French general (b. 1644)", "html": "1711 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%<PERSON><PERSON><PERSON>,_duc_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>, duc de Boufflers\"><PERSON>, duc <PERSON></a>, French general (b. 1644)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%<PERSON><PERSON><PERSON>,_duc_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>, duc de Boufflers\"><PERSON>, duc <PERSON></a>, French general (b. 1644)", "links": [{"title": "<PERSON>, duc de Bo<PERSON>lers", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A<PERSON><PERSON>,_duc_<PERSON>_<PERSON>"}]}, {"year": "1752", "text": "<PERSON>, English mathematician, historian, and theologian (b. 1667)", "html": "1752 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English mathematician, historian, and theologian (b. 1667)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English mathematician, historian, and theologian (b. 1667)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1773", "text": "<PERSON>, 1st Baron <PERSON>, English poet and politician, Chancellor of the Exchequer (b. 1709)", "html": "1773 - <a href=\"https://wikipedia.org/wiki/<PERSON>,_1st_Baron_<PERSON>\" title=\"<PERSON>, 1st Baron <PERSON>\"><PERSON>, 1st Baron <PERSON></a>, English poet and politician, <a href=\"https://wikipedia.org/wiki/Chancellor_of_the_Exchequer\" title=\"Chancellor of the Exchequer\">Chancellor of the Exchequer</a> (b. 1709)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>,_1st_Baron_<PERSON>\" title=\"<PERSON>, 1st Baron <PERSON>\"><PERSON>, 1st Baron <PERSON></a>, English poet and politician, <a href=\"https://wikipedia.org/wiki/Chancellor_of_the_Exchequer\" title=\"Chancellor of the Exchequer\">Chancellor of the Exchequer</a> (b. 1709)", "links": [{"title": "<PERSON>, 1st Baron <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>,_1st_Baron_<PERSON>"}, {"title": "Chancellor of the Exchequer", "link": "https://wikipedia.org/wiki/Chancellor_of_the_Exchequer"}]}, {"year": "1793", "text": "<PERSON>, French general (b. 1713)", "html": "1793 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, French general (b. 1713)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, French general (b. 1713)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1797", "text": "<PERSON><PERSON><PERSON>, French-Austrian field marshal (b. 1724)", "html": "1797 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, French-Austrian field marshal (b. 1724)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, French-Austrian field marshal (b. 1724)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1806", "text": "<PERSON><PERSON><PERSON><PERSON>, French painter and illustrator (b. 1732)", "html": "1806 - <a href=\"https://wikipedia.org/wiki/<PERSON>-<PERSON>%C3%A9_<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, French painter and illustrator (b. 1732)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>%C3%A9_<PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, French painter and illustrator (b. 1732)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>-<PERSON>%C3%A9_<PERSON><PERSON><PERSON>"}]}, {"year": "1818", "text": "<PERSON>, English lawyer and politician, 1st Governor-General of Bengal (b. 1732)", "html": "1818 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English lawyer and politician, 1st <a href=\"https://wikipedia.org/wiki/Governor-General_of_Bengal\" class=\"mw-redirect\" title=\"Governor-General of Bengal\">Governor-General of Bengal</a> (b. 1732)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English lawyer and politician, 1st <a href=\"https://wikipedia.org/wiki/Governor-General_of_Bengal\" class=\"mw-redirect\" title=\"Governor-General of Bengal\">Governor-General of Bengal</a> (b. 1732)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Governor-General of Bengal", "link": "https://wikipedia.org/wiki/Governor-General_of_Bengal"}]}, {"year": "1828", "text": "<PERSON>, Austrian neuroanatomist and physiologist (b. 1758)", "html": "1828 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Austrian neuroanatomist and physiologist (b. 1758)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Austrian neuroanatomist and physiologist (b. 1758)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1850", "text": "<PERSON><PERSON>, Romanian-Austrian poet and author (b. 1802)", "html": "1850 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Romanian-Austrian poet and author (b. 1802)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Romanian-Austrian poet and author (b. 1802)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1861", "text": "<PERSON><PERSON><PERSON>, Emperor of China (b. 1831)", "html": "1861 - <a href=\"https://wikipedia.org/wiki/Xi<PERSON><PERSON>_Emperor\" title=\"Xianfeng Emperor\"><PERSON><PERSON><PERSON></a>, Emperor of China (b. 1831)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Xi<PERSON><PERSON>_Emperor\" title=\"Xianfeng Emperor\"><PERSON><PERSON><PERSON></a>, Emperor of China (b. 1831)", "links": [{"title": "Xi<PERSON><PERSON> Emperor", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_Emperor"}]}, {"year": "1888", "text": "<PERSON><PERSON><PERSON>, Hungarian jurist and politician, Hungarian Minister of Education (b. 1817)", "html": "1888 - <a href=\"https://wikipedia.org/wiki/%C3%81goston_Trefort\" title=\"Ágoston Trefort\"><PERSON><PERSON><PERSON></a>, Hungarian jurist and politician, <a href=\"https://wikipedia.org/wiki/Minister_of_Education_(Hungary)\" title=\"Minister of Education (Hungary)\">Hungarian Minister of Education</a> (b. 1817)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/%C3%81goston_Trefort\" title=\"Ágoston Trefort\"><PERSON><PERSON><PERSON></a>, Hungarian jurist and politician, <a href=\"https://wikipedia.org/wiki/Minister_of_Education_(Hungary)\" title=\"Minister of Education (Hungary)\">Hungarian Minister of Education</a> (b. 1817)", "links": [{"title": "Ágoston Trefort", "link": "https://wikipedia.org/wiki/%C3%81goston_Trefort"}, {"title": "Minister of Education (Hungary)", "link": "https://wikipedia.org/wiki/Minister_of_Education_(Hungary)"}]}, {"year": "1891", "text": "<PERSON>, Czech journalist, author, and poet (b. 1834)", "html": "1891 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Czech journalist, author, and poet (b. 1834)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Czech journalist, author, and poet (b. 1834)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1903", "text": "<PERSON>-<PERSON>, 3rd Marquess of Salisbury, English academic and politician, Prime Minister of the United Kingdom (b. 1830)", "html": "1903 - <a href=\"https://wikipedia.org/wiki/<PERSON>-<PERSON>,_3rd_Marquess_of_Salisbury\" title=\"<PERSON>, 3rd Marquess of Salisbury\"><PERSON>, 3rd Marquess of Salisbury</a>, English academic and politician, <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_the_United_Kingdom\" title=\"Prime Minister of the United Kingdom\">Prime Minister of the United Kingdom</a> (b. 1830)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>-<PERSON>,_3rd_Marquess_of_Salisbury\" title=\"<PERSON>, 3rd Marquess of Salisbury\"><PERSON>, 3rd Marquess of Salisbury</a>, English academic and politician, <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_the_United_Kingdom\" title=\"Prime Minister of the United Kingdom\">Prime Minister of the United Kingdom</a> (b. 1830)", "links": [{"title": "<PERSON><PERSON>, 3rd Marquess of Salisbury", "link": "https://wikipedia.org/wiki/<PERSON>-<PERSON>,_3rd_Marquess_of_Salisbury"}, {"title": "Prime Minister of the United Kingdom", "link": "https://wikipedia.org/wiki/Prime_Minister_of_the_United_Kingdom"}]}, {"year": "1904", "text": "<PERSON>, American novelist and poet (b. 1850)", "html": "1904 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American novelist and poet (b. 1850)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American novelist and poet (b. 1850)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1909", "text": "<PERSON>, English dermatologist and author (b. 1846)", "html": "1909 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English dermatologist and author (b. 1846)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English dermatologist and author (b. 1846)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1914", "text": "<PERSON><PERSON><PERSON><PERSON>, Italian bishop and academic (b. 1859)", "html": "1914 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian bishop and academic (b. 1859)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian bishop and academic (b. 1859)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON><PERSON>-<PERSON>"}]}, {"year": "1918", "text": "<PERSON><PERSON><PERSON>, German neurologist and academic (b. 1868)", "html": "1918 - <a href=\"https://wikipedia.org/wiki/Korb<PERSON>_B<PERSON>mann\" title=\"Korbinian Brodmann\"><PERSON><PERSON><PERSON><PERSON></a>, German neurologist and academic (b. 1868)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Korb<PERSON>_B<PERSON>mann\" title=\"Korb<PERSON> Brodmann\"><PERSON><PERSON><PERSON><PERSON></a>, German neurologist and academic (b. 1868)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON><PERSON><PERSON>"}]}, {"year": "1920", "text": "<PERSON>, Swedish artist (b. 1860)", "html": "1920 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swedish artist (b. 1860)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swedish artist (b. 1860)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1922", "text": "<PERSON>, Irish rebel, counter-intelligence and military tactician, and politician; 2nd Irish Minister of Finance (b. 1890)", "html": "1922 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(Irish_leader)\" title=\"<PERSON> (Irish leader)\"><PERSON></a>, Irish rebel, counter-intelligence and military tactician, and politician; 2nd <a href=\"https://wikipedia.org/wiki/Minister_for_Finance_(Ireland)\" title=\"Minister for Finance (Ireland)\">Irish Minister of Finance</a> (b. 1890)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(Irish_leader)\" title=\"<PERSON> (Irish leader)\"><PERSON></a>, Irish rebel, counter-intelligence and military tactician, and politician; 2nd <a href=\"https://wikipedia.org/wiki/Minister_for_Finance_(Ireland)\" title=\"Minister for Finance (Ireland)\">Irish Minister of Finance</a> (b. 1890)", "links": [{"title": "<PERSON> (Irish leader)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(Irish_leader)"}, {"title": "Minister for Finance (Ireland)", "link": "https://wikipedia.org/wiki/Minister_for_Finance_(Ireland)"}]}, {"year": "1926", "text": "<PERSON>, American academic (b. 1834)", "html": "1926 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American academic (b. 1834)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American academic (b. 1834)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1933", "text": "<PERSON><PERSON><PERSON>, Greek general and diplomat (b. 1858)", "html": "1933 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Greek general and diplomat (b. 1858)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Greek general and diplomat (b. 1858)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1940", "text": "<PERSON>, English physicist and academic (b. 1851)", "html": "1940 - <a href=\"https://wikipedia.org/wiki/Oliver_Lodge\" title=\"Oliver Lodge\">Oliver Lodge</a>, English physicist and academic (b. 1851)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Oliver_Lodge\" title=\"Oliver Lodge\">Oliver Lodge</a>, English physicist and academic (b. 1851)", "links": [{"title": "Oliver Lodge", "link": "https://wikipedia.org/wiki/Oliver_<PERSON>"}]}, {"year": "1940", "text": "<PERSON>, 1st Baron <PERSON>, Maltese lawyer and politician, 4th Prime Minister of Malta (b. 1861)", "html": "1940 - <a href=\"https://wikipedia.org/wiki/<PERSON>,_1st_Baron_<PERSON>\" title=\"<PERSON>, 1st Baron <PERSON>\"><PERSON>, 1st Baron <PERSON></a>, Maltese lawyer and politician, 4th <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Malta\" title=\"Prime Minister of Malta\">Prime Minister of Malta</a> (b. 1861)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>,_1st_Baron_<PERSON>\" title=\"<PERSON>, 1st Baron <PERSON>\"><PERSON>, 1st Baron <PERSON></a>, Maltese lawyer and politician, 4th <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Malta\" title=\"Prime Minister of Malta\">Prime Minister of Malta</a> (b. 1861)", "links": [{"title": "<PERSON>, 1st Baron <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>,_1st_Baron_<PERSON>"}, {"title": "Prime Minister of Malta", "link": "https://wikipedia.org/wiki/Prime_Minister_of_Malta"}]}, {"year": "1942", "text": "<PERSON>, Russian dancer and choreographer (b. 1880)", "html": "1942 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian dancer and choreographer (b. 1880)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian dancer and choreographer (b. 1880)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1946", "text": "<PERSON><PERSON><PERSON>, Hungarian general and politician, 35th Prime Minister of Hungary (b. 1883)", "html": "1946 - <a href=\"https://wikipedia.org/wiki/D%C3%B6me_Szt%C3%B3jay\" title=\"<PERSON><PERSON><PERSON> Sztójay\"><PERSON><PERSON><PERSON></a>, Hungarian general and politician, 35th <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Hungary\" title=\"Prime Minister of Hungary\">Prime Minister of Hungary</a> (b. 1883)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/D%C3%B6me_Szt%C3%B3jay\" title=\"<PERSON><PERSON><PERSON> Sztójay\"><PERSON><PERSON><PERSON></a>, Hungarian general and politician, 35th <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Hungary\" title=\"Prime Minister of Hungary\">Prime Minister of Hungary</a> (b. 1883)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/D%C3%B6me_Szt%C3%B3jay"}, {"title": "Prime Minister of Hungary", "link": "https://wikipedia.org/wiki/Prime_Minister_of_Hungary"}]}, {"year": "1950", "text": "<PERSON>, American geologist and academic (b. 1888)", "html": "1950 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(geologist)\" title=\"<PERSON> (geologist)\"><PERSON></a>, American geologist and academic (b. 1888)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>(geologist)\" title=\"<PERSON> (geologist)\"><PERSON></a>, American geologist and academic (b. 1888)", "links": [{"title": "<PERSON> (geologist)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>(geologist)"}]}, {"year": "1951", "text": "<PERSON>, Canadian businessman and philanthropist (b. 1884)", "html": "1951 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian businessman and philanthropist (b. 1884)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian businessman and philanthropist (b. 1884)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1953", "text": "<PERSON>, American baseball player (b. 1916)", "html": "1953 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player (b. 1916)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Jim <PERSON>\"><PERSON></a>, American baseball player (b. 1916)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1958", "text": "<PERSON>, French novelist and paleographer, Nobel Prize laureate (b. 1881)", "html": "1958 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French novelist and paleographer, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_for_Literature\" class=\"mw-redirect\" title=\"Nobel Prize for Literature\">Nobel Prize</a> laureate (b. 1881)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French novelist and paleographer, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_for_Literature\" class=\"mw-redirect\" title=\"Nobel Prize for Literature\">Nobel Prize</a> laureate (b. 1881)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>"}, {"title": "Nobel Prize for Literature", "link": "https://wikipedia.org/wiki/Nobel_Prize_for_Literature"}]}, {"year": "1960", "text": "<PERSON>, Estonian soldier and politician, Prime Minister of Estonia in exile (b. 1897)", "html": "1960 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Estonian soldier and politician, <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Estonia_in_exile\" class=\"mw-redirect\" title=\"Prime Minister of Estonia in exile\">Prime Minister of Estonia in exile</a> (b. 1897)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Estonian soldier and politician, <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Estonia_in_exile\" class=\"mw-redirect\" title=\"Prime Minister of Estonia in exile\">Prime Minister of Estonia in exile</a> (b. 1897)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Prime Minister of Estonia in exile", "link": "https://wikipedia.org/wiki/Prime_Minister_of_Estonia_in_exile"}]}, {"year": "1963", "text": "<PERSON>, 1st Viscount <PERSON>, English businessman and philanthropist, founded Morris Motors (b. 1877)", "html": "1963 - <a href=\"https://wikipedia.org/wiki/<PERSON>,_1st_Viscount_<PERSON>\" title=\"<PERSON>, 1st Viscount <PERSON>\"><PERSON>, 1st Viscount <PERSON></a>, English businessman and philanthropist, founded <a href=\"https://wikipedia.org/wiki/Morris_Motors\" title=\"Morris Motors\">Morris Motors</a> (b. 1877)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>,_1st_Viscount_<PERSON>\" title=\"<PERSON>, 1st Viscount <PERSON>\"><PERSON>, 1st Viscount <PERSON></a>, English businessman and philanthropist, founded <a href=\"https://wikipedia.org/wiki/Morris_Motors\" title=\"Morris Motors\">Morris Motors</a> (b. 1877)", "links": [{"title": "<PERSON>, 1st Viscount <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_1st_Viscount_<PERSON>"}, {"title": "Morris Motors", "link": "https://wikipedia.org/wiki/Morris_Motors"}]}, {"year": "1967", "text": "<PERSON>, American biologist and academic, co-created the birth-control pill (b. 1903)", "html": "1967 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, American biologist and academic, co-created the <a href=\"https://wikipedia.org/wiki/Combined_oral_contraceptive_pill\" title=\"Combined oral contraceptive pill\">birth-control pill</a> (b. 1903)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, American biologist and academic, co-created the <a href=\"https://wikipedia.org/wiki/Combined_oral_contraceptive_pill\" title=\"Combined oral contraceptive pill\">birth-control pill</a> (b. 1903)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "Combined oral contraceptive pill", "link": "https://wikipedia.org/wiki/Combined_oral_contraceptive_pill"}]}, {"year": "1970", "text": "<PERSON>, Russian philologist and scholar (b. 1895)", "html": "1970 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian philologist and scholar (b. 1895)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian philologist and scholar (b. 1895)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>pp"}]}, {"year": "1971", "text": "<PERSON><PERSON><PERSON>, Swedish archaeologist (b. 1888)", "html": "1971 - <a href=\"https://wikipedia.org/wiki/<PERSON>ir<PERSON>_<PERSON>\" title=\"<PERSON>ir<PERSON>\"><PERSON><PERSON><PERSON></a>, Swedish archaeologist (b. 1888)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>ir<PERSON>_<PERSON>\" title=\"<PERSON>ir<PERSON>\"><PERSON><PERSON><PERSON></a>, Swedish archaeologist (b. 1888)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Birger_<PERSON>erman"}]}, {"year": "1974", "text": "<PERSON>, Polish-English mathematician, biologist, and author (b. 1908)", "html": "1974 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Polish-English mathematician, biologist, and author (b. 1908)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Polish-English mathematician, biologist, and author (b. 1908)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1976", "text": "<PERSON>, Greek pianist and composer (b. 1913)", "html": "1976 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Greek pianist and composer (b. 1913)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Greek pianist and composer (b. 1913)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1976", "text": "<PERSON><PERSON><PERSON><PERSON>, Brazilian physician and politician, 21st President of Brazil (b. 1902)", "html": "1976 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Brazilian physician and politician, 21st <a href=\"https://wikipedia.org/wiki/President_of_Brazil\" title=\"President of Brazil\">President of Brazil</a> (b. 1902)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Brazilian physician and politician, 21st <a href=\"https://wikipedia.org/wiki/President_of_Brazil\" title=\"President of Brazil\">President of Brazil</a> (b. 1902)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Juscelino_<PERSON>"}, {"title": "President of Brazil", "link": "https://wikipedia.org/wiki/President_of_Brazil"}]}, {"year": "1977", "text": "<PERSON>, English actor (b. 1918)", "html": "1977 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(actor)\" title=\"<PERSON> (actor)\"><PERSON></a>, English actor (b. 1918)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(actor)\" title=\"<PERSON> (actor)\"><PERSON></a>, English actor (b. 1918)", "links": [{"title": "<PERSON> (actor)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(actor)"}]}, {"year": "1977", "text": "<PERSON><PERSON><PERSON>, Korean monk, philosopher and writer (b. 1891)", "html": "1977 - <a href=\"https://wikipedia.org/wiki/Chun<PERSON>ong\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Korean monk, philosopher and writer (b. 1891)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Chunseong\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Korean monk, philosopher and writer (b. 1891)", "links": [{"title": "<PERSON>se<PERSON>", "link": "https://wikipedia.org/wiki/Chunseong"}]}, {"year": "1977", "text": "<PERSON>, Australian politician (b. 1907)", "html": "1977 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian politician (b. 1907)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian politician (b. 1907)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1978", "text": "<PERSON><PERSON>, Kenyan politician, 1st President of Kenya (b. 1894)", "html": "1978 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Kenyan politician, 1st <a href=\"https://wikipedia.org/wiki/President_of_Kenya\" title=\"President of Kenya\">President of Kenya</a> (b. 1894)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Kenyan politician, 1st <a href=\"https://wikipedia.org/wiki/President_of_Kenya\" title=\"President of Kenya\">President of Kenya</a> (b. 1894)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Jomo_Kenyatta"}, {"title": "President of Kenya", "link": "https://wikipedia.org/wiki/President_of_Kenya"}]}, {"year": "1979", "text": "<PERSON>, American novelist, short-story writer, and poet (b. 1904)", "html": "1979 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American novelist, short-story writer, and poet (b. 1904)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American novelist, short-story writer, and poet (b. 1904)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "1980", "text": "<PERSON>, American pilot, engineer, and businessman, founded McDonnell Aircraft (b. 1899)", "html": "1980 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American pilot, engineer, and businessman, founded <a href=\"https://wikipedia.org/wiki/McDonnell_Aircraft\" class=\"mw-redirect\" title=\"McDonnell Aircraft\">McDonnell Aircraft</a> (b. 1899)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American pilot, engineer, and businessman, founded <a href=\"https://wikipedia.org/wiki/McDonnell_Aircraft\" class=\"mw-redirect\" title=\"McDonnell Aircraft\">McDonnell Aircraft</a> (b. 1899)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "McDonnell Aircraft", "link": "https://wikipedia.org/wiki/McDonnell_Aircraft"}]}, {"year": "1981", "text": "<PERSON>, Filipino painter (b. 1910)", "html": "1981 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Filipino painter (b. 1910)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Filipino painter (b. 1910)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1985", "text": "<PERSON> (historian), Historian of Mexico and its Indians, President of the American Historical Association (b. 1920)", "html": "1985 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(historian)\" title=\"<PERSON> (historian)\"><PERSON> (historian)</a>, Historian of Mexico and its Indians, President of the <a href=\"https://wikipedia.org/wiki/American_Historical_Association\" title=\"American Historical Association\">American Historical Association</a> (b. 1920)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>(historian)\" title=\"<PERSON> (historian)\"><PERSON> (historian)</a>, Historian of Mexico and its Indians, President of the <a href=\"https://wikipedia.org/wiki/American_Historical_Association\" title=\"American Historical Association\">American Historical Association</a> (b. 1920)", "links": [{"title": "<PERSON> (historian)", "link": "https://wikipedia.org/wiki/<PERSON>_(historian)"}, {"title": "American Historical Association", "link": "https://wikipedia.org/wiki/American_Historical_Association"}]}, {"year": "1986", "text": "<PERSON><PERSON><PERSON><PERSON>, Turkish lawyer and politician, 3rd President of Turkey (b. 1883)", "html": "1986 - <a href=\"https://wikipedia.org/wiki/Cel%C3%A2l_Bayar\" title=\"Cel<PERSON><PERSON> Bayar\"><PERSON><PERSON><PERSON><PERSON></a>, Turkish lawyer and politician, 3rd <a href=\"https://wikipedia.org/wiki/President_of_Turkey\" title=\"President of Turkey\">President of Turkey</a> (b. 1883)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Cel%C3%A2l_Bayar\" title=\"Celâl Bayar\"><PERSON><PERSON><PERSON><PERSON></a>, Turkish lawyer and politician, 3rd <a href=\"https://wikipedia.org/wiki/President_of_Turkey\" title=\"President of Turkey\">President of Turkey</a> (b. 1883)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Cel%C3%A2l_Bayar"}, {"title": "President of Turkey", "link": "https://wikipedia.org/wiki/President_of_Turkey"}]}, {"year": "1987", "text": "<PERSON>, American author and journalist (b. 1909)", "html": "1987 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author and journalist (b. 1909)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author and journalist (b. 1909)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1989", "text": "<PERSON>, Belgian cyclist (b. 1933)", "html": "1989 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Belgian cyclist (b. 1933)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Belgian cyclist (b. 1933)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1989", "text": "<PERSON><PERSON>, American activist, co-founded the Black Panther Party (b. 1942)", "html": "1989 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American activist, co-founded the <a href=\"https://wikipedia.org/wiki/Black_Panther_Party\" title=\"Black Panther Party\">Black Panther Party</a> (b. 1942)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American activist, co-founded the <a href=\"https://wikipedia.org/wiki/Black_Panther_Party\" title=\"Black Panther Party\">Black Panther Party</a> (b. 1942)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>"}, {"title": "Black Panther Party", "link": "https://wikipedia.org/wiki/Black_Panther_Party"}]}, {"year": "1991", "text": "<PERSON><PERSON>, Canadian-American actress (b. 1924)", "html": "1991 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Canadian-American actress (b. 1924)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Canadian-American actress (b. 1924)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1991", "text": "<PERSON>, Russian soldier and politician, Soviet Minister of Interior (b. 1937)", "html": "1991 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian soldier and politician, <a href=\"https://wikipedia.org/wiki/Ministry_of_Internal_Affairs_(Russia)\" title=\"Ministry of Internal Affairs (Russia)\">Soviet Minister of Interior</a> (b. 1937)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian soldier and politician, <a href=\"https://wikipedia.org/wiki/Ministry_of_Internal_Affairs_(Russia)\" title=\"Ministry of Internal Affairs (Russia)\">Soviet Minister of Interior</a> (b. 1937)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Ministry of Internal Affairs (Russia)", "link": "https://wikipedia.org/wiki/Ministry_of_Internal_Affairs_(Russia)"}]}, {"year": "1994", "text": "<PERSON>, Canadian director and screenwriter (b. 1931)", "html": "1994 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian director and screenwriter (b. 1931)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian director and screenwriter (b. 1931)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1994", "text": "<PERSON>, American sculptor and painter (b. 1914)", "html": "1994 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American sculptor and painter (b. 1914)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American sculptor and painter (b. 1914)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1995", "text": "<PERSON>, Irish footballer and manager (b. 1919)", "html": "1995 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Irish footballer and manager (b. 1919)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Irish footballer and manager (b. 1919)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1996", "text": "<PERSON>, Austrian car designer and engineer (b. 1904)", "html": "1996 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Austrian car designer and engineer (b. 1904)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Austrian car designer and engineer (b. 1904)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Erwin_<PERSON>"}]}, {"year": "2000", "text": "<PERSON><PERSON><PERSON>, 2nd President of Azerbaijan (b. 1938)", "html": "2000 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_Elchibey\" title=\"<PERSON><PERSON><PERSON> El<PERSON>\"><PERSON><PERSON><PERSON></a>, 2nd <a href=\"https://wikipedia.org/wiki/President_of_Azerbaijan\" title=\"President of Azerbaijan\">President of Azerbaijan</a> (b. 1938)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_El<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, 2nd <a href=\"https://wikipedia.org/wiki/President_of_Azerbaijan\" title=\"President of Azerbaijan\">President of Azerbaijan</a> (b. 1938)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>lfaz_Elchibey"}, {"title": "President of Azerbaijan", "link": "https://wikipedia.org/wiki/President_of_Azerbaijan"}]}, {"year": "2003", "text": "<PERSON>, Swiss figure skater and coach (b. 1914)", "html": "2003 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swiss figure skater and coach (b. 1914)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swiss figure skater and coach (b. 1914)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2004", "text": "<PERSON>, Russian chess player and trainer (b. 1960)", "html": "2004 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian chess player and trainer (b. 1960)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian chess player and trainer (b. 1960)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2004", "text": "<PERSON>, Australian soldier and politician, 33rd Premier of Tasmania (b. 1908)", "html": "2004 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(politician)\" title=\"<PERSON> (politician)\"><PERSON></a>, Australian soldier and politician, 33rd <a href=\"https://wikipedia.org/wiki/Premier_of_Tasmania\" title=\"Premier of Tasmania\">Premier of Tasmania</a> (b. 1908)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(politician)\" title=\"<PERSON> (politician)\"><PERSON></a>, Australian soldier and politician, 33rd <a href=\"https://wikipedia.org/wiki/Premier_of_Tasmania\" title=\"Premier of Tasmania\">Premier of Tasmania</a> (b. 1908)", "links": [{"title": "<PERSON> (politician)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(politician)"}, {"title": "Premier of Tasmania", "link": "https://wikipedia.org/wiki/Premier_of_Tasmania"}]}, {"year": "2004", "text": "<PERSON>, Canadian director and producer (b. 1920)", "html": "2004 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian director and producer (b. 1920)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian director and producer (b. 1920)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2005", "text": "<PERSON>, French-Italian director and composer (b. 1929)", "html": "2005 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Luc Ferrari\"><PERSON></a>, French-Italian director and composer (b. 1929)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Luc Ferrari\"><PERSON></a>, French-Italian director and composer (b. 1929)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2005", "text": "<PERSON>, American chemist and metallurgist (b. 1914)", "html": "2005 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American chemist and metallurgist (b. 1914)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American chemist and metallurgist (b. 1914)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "2007", "text": "<PERSON>, American short story writer and poet (b. 1922)", "html": "2007 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American short story writer and poet (b. 1922)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American short story writer and poet (b. 1922)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2008", "text": "<PERSON>, English-Canadian soldier (b. 1899)", "html": "2008 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English-Canadian soldier (b. 1899)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English-Canadian soldier (b. 1899)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2009", "text": "<PERSON><PERSON>, Canadian pacifist, feminist, and activist (b. 1908)", "html": "2009 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Canadian <a href=\"https://wikipedia.org/wiki/Pacifism\" title=\"Pacifism\">pacifist</a>, <a href=\"https://wikipedia.org/wiki/Feminism\" title=\"Feminism\">feminist</a>, and <a href=\"https://wikipedia.org/wiki/Activist\" class=\"mw-redirect\" title=\"Activist\">activist</a> (b. 1908)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Canadian <a href=\"https://wikipedia.org/wiki/Pacifism\" title=\"Pacifism\">pacifist</a>, <a href=\"https://wikipedia.org/wiki/Feminism\" title=\"Feminism\">feminist</a>, and <a href=\"https://wikipedia.org/wiki/Activist\" class=\"mw-redirect\" title=\"Activist\">activist</a> (b. 1908)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}, {"title": "Pacifism", "link": "https://wikipedia.org/wiki/Pacifism"}, {"title": "Feminism", "link": "https://wikipedia.org/wiki/Feminism"}, {"title": "Activist", "link": "https://wikipedia.org/wiki/Activist"}]}, {"year": "2009", "text": "<PERSON>, American journalist and author (b. 1926)", "html": "2009 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American journalist and author (b. 1926)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American journalist and author (b. 1926)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2010", "text": "<PERSON><PERSON><PERSON>, Croatian footballer and manager (b. 1923)", "html": "2010 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Croatian footballer and manager (b. 1923)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Croatian footballer and manager (b. 1923)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "2011", "text": "<PERSON>, American singer-songwriter and producer (b. 1942)", "html": "2011 - <a href=\"https://wikipedia.org/wiki/Ash<PERSON>_%26_<PERSON>\" title=\"<PERSON><PERSON> &amp; Simpson\"><PERSON></a>, American singer-songwriter and producer (b. 1942)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Ashford_%26_<PERSON>\" title=\"<PERSON><PERSON> &amp; Simpson\"><PERSON></a>, American singer-songwriter and producer (b. 1942)", "links": [{"title": "Ashford & Simpson", "link": "https://wikipedia.org/wiki/Ashford_%26_Simpson"}]}, {"year": "2011", "text": "<PERSON>, Canadian academic and politician (b. 1950)", "html": "2011 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian academic and politician (b. 1950)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian academic and politician (b. 1950)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2011", "text": "<PERSON>, American philanthropist (b. 1922)", "html": "2011 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American philanthropist (b. 1922)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American philanthropist (b. 1922)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2012", "text": "<PERSON>, English author (b. 1925)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English author (b. 1925)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English author (b. 1925)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>den"}]}, {"year": "2012", "text": "<PERSON>, Chinese cardinal (b. 1923)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>-<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Chinese cardinal (b. 1923)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>-<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Chinese cardinal (b. 1923)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>-<PERSON>si"}]}, {"year": "2012", "text": "<PERSON>, American actor and screenwriter (b. 1926)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor and screenwriter (b. 1926)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor and screenwriter (b. 1926)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2013", "text": "<PERSON>, American pilot and businessman, founded the Experimental Aircraft Association (b. 1921)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American pilot and businessman, founded the <a href=\"https://wikipedia.org/wiki/Experimental_Aircraft_Association\" title=\"Experimental Aircraft Association\">Experimental Aircraft Association</a> (b. 1921)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American pilot and businessman, founded the <a href=\"https://wikipedia.org/wiki/Experimental_Aircraft_Association\" title=\"Experimental Aircraft Association\">Experimental Aircraft Association</a> (b. 1921)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Experimental Aircraft Association", "link": "https://wikipedia.org/wiki/Experimental_Aircraft_Association"}]}, {"year": "2013", "text": "<PERSON>, Italian footballer (b. 1984)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian footballer (b. 1984)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian footballer (b. 1984)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2014", "text": "<PERSON><PERSON> <PERSON><PERSON>, Indian author, poet, and playwright (b. 1932)", "html": "2014 - <a href=\"https://wikipedia.org/wiki/U._R._Ananthamurthy\" title=\"U. R<PERSON> Anantham<PERSON>hy\"><PERSON><PERSON> <PERSON><PERSON></a>, Indian author, poet, and playwright (b. 1932)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/U._R<PERSON>_<PERSON>nt<PERSON>urt<PERSON>\" title=\"U. R. Ananthamurthy\"><PERSON><PERSON> <PERSON><PERSON></a>, Indian author, poet, and playwright (b. 1932)", "links": [{"title": "<PERSON><PERSON> <PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON><PERSON><PERSON>"}]}, {"year": "2014", "text": "<PERSON>, Greek lexicographer and philologist (b. 1906)", "html": "2014 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Greek lexicographer and philologist (b. 1906)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Greek lexicographer and philologist (b. 1906)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2014", "text": "<PERSON>, American football player and coach (b. 1928)", "html": "2014 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player and coach (b. 1928)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player and coach (b. 1928)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2014", "text": "<PERSON><PERSON>, American baseball player (b. 1933)", "html": "2014 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American baseball player (b. 1933)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American baseball player (b. 1933)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "2014", "text": "<PERSON>, American businessman, founded the University of Phoenix (b. 1921)", "html": "2014 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American businessman, founded the <a href=\"https://wikipedia.org/wiki/University_of_Phoenix\" title=\"University of Phoenix\">University of Phoenix</a> (b. 1921)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American businessman, founded the <a href=\"https://wikipedia.org/wiki/University_of_Phoenix\" title=\"University of Phoenix\">University of Phoenix</a> (b. 1921)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "University of Phoenix", "link": "https://wikipedia.org/wiki/University_of_Phoenix"}]}, {"year": "2014", "text": "<PERSON>, American chemist and academic (b. 1929)", "html": "2014 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American chemist and academic (b. 1929)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American chemist and academic (b. 1929)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "2015", "text": "<PERSON>, Australian cricketer and journalist (b. 1922)", "html": "2015 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian cricketer and journalist (b. 1922)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian cricketer and journalist (b. 1922)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2015", "text": "<PERSON><PERSON>, Cambodian academic and politician (b. 1932)", "html": "2015 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Cambodian academic and politician (b. 1932)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Cambodian academic and politician (b. 1932)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>hirith"}]}, {"year": "2015", "text": "<PERSON>, English race car driver and book dealer (b. 1919)", "html": "2015 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(racing_driver)\" title=\"<PERSON> (racing driver)\"><PERSON></a>, English race car driver and book dealer (b. 1919)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(racing_driver)\" title=\"<PERSON> (racing driver)\"><PERSON></a>, English race car driver and book dealer (b. 1919)", "links": [{"title": "<PERSON> (racing driver)", "link": "https://wikipedia.org/wiki/<PERSON>_(racing_driver)"}]}, {"year": "2016", "text": "<PERSON><PERSON> <PERSON><PERSON>, 6th President of Singapore (b. 1924)", "html": "2016 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> <PERSON><PERSON>\"><PERSON><PERSON> <PERSON><PERSON></a>, 6th <a href=\"https://wikipedia.org/wiki/President_of_Singapore\" title=\"President of Singapore\">President of Singapore</a> (b. 1924)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> <PERSON><PERSON>\"><PERSON><PERSON> <PERSON><PERSON></a>, 6th <a href=\"https://wikipedia.org/wiki/President_of_Singapore\" title=\"President of Singapore\">President of Singapore</a> (b. 1924)", "links": [{"title": "<PERSON><PERSON> <PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>"}, {"title": "President of Singapore", "link": "https://wikipedia.org/wiki/President_of_Singapore"}]}, {"year": "2016", "text": "<PERSON><PERSON>, Belgian and American jazz musician (b. 1922)", "html": "2016 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_Thi<PERSON>mans\" title=\"Too<PERSON> Thielemans\"><PERSON><PERSON></a>, Belgian and American <a href=\"https://wikipedia.org/wiki/Jazz_in_Belgium\" class=\"mw-redirect\" title=\"Jazz in Belgium\">jazz</a> musician (b. 1922)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_Thi<PERSON>\" title=\"Too<PERSON> Thielemans\"><PERSON><PERSON></a>, Belgian and American <a href=\"https://wikipedia.org/wiki/Jazz_in_Belgium\" class=\"mw-redirect\" title=\"Jazz in Belgium\">jazz</a> musician (b. 1922)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON><PERSON><PERSON>"}, {"title": "Jazz in Belgium", "link": "https://wikipedia.org/wiki/Jazz_in_Belgium"}]}, {"year": "2017", "text": "<PERSON>, British Computer scientist (b. 1948)", "html": "2017 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, British <a href=\"https://wikipedia.org/wiki/Computer_scientist\" title=\"Computer scientist\">Computer scientist</a> (b. 1948)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, British <a href=\"https://wikipedia.org/wiki/Computer_scientist\" title=\"Computer scientist\">Computer scientist</a> (b. 1948)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON><PERSON>_<PERSON>"}, {"title": "Computer scientist", "link": "https://wikipedia.org/wiki/Computer_scientist"}]}, {"year": "2018", "text": "<PERSON>, American musician (b. 1949)", "html": "2018 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American musician (b. 1949)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American musician (b. 1949)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2018", "text": "<PERSON>, Indian printmaker, sculptor and teacher (b. 1925)", "html": "2018 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(artist)\" title=\"<PERSON> (artist)\"><PERSON></a>, Indian printmaker, sculptor and teacher (b. 1925)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(artist)\" title=\"<PERSON> (artist)\"><PERSON></a>, Indian printmaker, sculptor and teacher (b. 1925)", "links": [{"title": "<PERSON> (artist)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(artist)"}]}, {"year": "2021", "text": "<PERSON>, Canadian ice hockey player (b. 1941)", "html": "2021 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player (b. 1941)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player (b. 1941)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2024", "text": "<PERSON>, American military officer (b. 1928)", "html": "2024 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American military officer (b. 1928)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American military officer (b. 1928)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}]}}