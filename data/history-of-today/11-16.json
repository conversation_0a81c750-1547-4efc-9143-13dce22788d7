{"date": "November 16", "url": "https://wikipedia.org/wiki/November_16", "data": {"Events": [{"year": "951", "text": "Emperor <PERSON> sends a Southern Tang expeditionary force of 10,000 men under <PERSON><PERSON> to conquer Chu. <PERSON> removes the ruling family to his own capital in Nanjing, ending the Chu Kingdom.", "html": "951 - Emperor <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(Southern_Tang)\" title=\"Li Jing (Southern Tang)\"><PERSON></a> sends a <a href=\"https://wikipedia.org/wiki/Southern_Tang\" title=\"Southern Tang\">Southern Tang</a> expeditionary force of 10,000 men under <a href=\"https://wikipedia.org/wiki/Bian_Hao\" title=\"Bian Hao\"><PERSON><PERSON></a> to conquer <a href=\"https://wikipedia.org/wiki/Chu_(Ten_Kingdoms)\" class=\"mw-redirect\" title=\"Chu (Ten Kingdoms)\">Chu</a>. <PERSON> removes the ruling family to his own capital in <a href=\"https://wikipedia.org/wiki/Nanjing\" title=\"Nanjing\">Nanjing</a>, ending the Chu Kingdom.", "no_year_html": "Emperor <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(Southern_Tang)\" title=\"Li Jing (Southern Tang)\"><PERSON></a> sends a <a href=\"https://wikipedia.org/wiki/Southern_Tang\" title=\"Southern Tang\">Southern Tang</a> expeditionary force of 10,000 men under <a href=\"https://wikipedia.org/wiki/Bian_Hao\" title=\"Bian Hao\"><PERSON><PERSON></a> to conquer <a href=\"https://wikipedia.org/wiki/Chu_(Ten_Kingdoms)\" class=\"mw-redirect\" title=\"Chu (Ten Kingdoms)\">Chu</a>. <PERSON> removes the ruling family to his own capital in <a href=\"https://wikipedia.org/wiki/Nanjing\" title=\"Nanjing\">Nanjing</a>, ending the Chu Kingdom.", "links": [{"title": "<PERSON> (Southern Tang)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(Southern_Tang)"}, {"title": "Southern Tang", "link": "https://wikipedia.org/wiki/Southern_Tang"}, {"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>o"}, {"title": "Chu (Ten Kingdoms)", "link": "https://wikipedia.org/wiki/Chu_(Ten_Kingdoms)"}, {"title": "Nanjing", "link": "https://wikipedia.org/wiki/Nanjing"}]}, {"year": "1272", "text": "While travelling during the Ninth Crusade, Prince <PERSON> becomes King of England upon <PERSON> of England's death, but he will not return to England for nearly two years to assume the throne.", "html": "1272 - While travelling during the <a href=\"https://wikipedia.org/wiki/Ninth_Crusade\" class=\"mw-redirect\" title=\"Ninth Crusade\">Ninth Crusade</a>, <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_England\" title=\"<PERSON> of England\">Prince <PERSON></a> becomes King of England upon <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_England\" title=\"<PERSON> of England\"><PERSON> III of England</a>'s death, but he will not return to England for nearly two years to assume the throne.", "no_year_html": "While travelling during the <a href=\"https://wikipedia.org/wiki/Ninth_Crusade\" class=\"mw-redirect\" title=\"Ninth Crusade\">Ninth Crusade</a>, <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_England\" title=\"<PERSON> of England\">Prince <PERSON></a> becomes King of England upon <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_England\" title=\"<PERSON> of England\"><PERSON> of England</a>'s death, but he will not return to England for nearly two years to assume the throne.", "links": [{"title": "Ninth Crusade", "link": "https://wikipedia.org/wiki/Ninth_Crusade"}, {"title": "<PERSON> of England", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_England"}, {"title": "<PERSON> of England", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_England"}]}, {"year": "1491", "text": "An auto-da-fé, held in the Brasero de la Dehesa outside of Ávila, concludes the case of the Holy Child of La Guardia with the public execution of several Jewish and converso suspects.", "html": "1491 - An <a href=\"https://wikipedia.org/wiki/Auto-da-f%C3%A9\" title=\"Auto-da-fé\">auto-da-fé</a>, held in the Brasero de la Dehesa outside of <a href=\"https://wikipedia.org/wiki/%C3%81vila,_Spain\" class=\"mw-redirect\" title=\"Ávila, Spain\">Á<PERSON></a>, concludes the case of the <a href=\"https://wikipedia.org/wiki/Holy_Child_of_La_Guardia\" title=\"Holy Child of La Guardia\">Holy Child of La Guardia</a> with the public execution of several Jewish and <a href=\"https://wikipedia.org/wiki/Converso\" title=\"Converso\">converso</a> suspects.", "no_year_html": "An <a href=\"https://wikipedia.org/wiki/Auto-da-f%C3%A9\" title=\"Auto-da-fé\">auto-da-fé</a>, held in the Brasero de la Dehesa outside of <a href=\"https://wikipedia.org/wiki/%C3%81vila,_Spain\" class=\"mw-redirect\" title=\"Ávila, Spain\">Ávila</a>, concludes the case of the <a href=\"https://wikipedia.org/wiki/Holy_Child_of_La_Guardia\" title=\"Holy Child of La Guardia\">Holy Child of La Guardia</a> with the public execution of several Jewish and <a href=\"https://wikipedia.org/wiki/Converso\" title=\"Converso\">converso</a> suspects.", "links": [{"title": "Auto-da-fé", "link": "https://wikipedia.org/wiki/Auto-da-f%C3%A9"}, {"title": "Ávila, Spain", "link": "https://wikipedia.org/wiki/%C3%81vila,_Spain"}, {"title": "Holy Child of La Guardia", "link": "https://wikipedia.org/wiki/Holy_Child_of_La_Guardia"}, {"title": "Converso", "link": "https://wikipedia.org/wiki/Converso"}]}, {"year": "1532", "text": "<PERSON> and his men capture Inca Emperor <PERSON><PERSON><PERSON><PERSON> at the Battle of Cajamarca.", "html": "1532 - <a href=\"https://wikipedia.org/wiki/Francisco<PERSON>\" title=\"<PERSON>\"><PERSON></a> and his men capture <a href=\"https://wikipedia.org/wiki/Inca_Empire\" title=\"Inca Empire\">Inca</a> Emperor <a href=\"https://wikipedia.org/wiki/Atahualpa\" title=\"Atahualpa\">Atahualpa</a> at the <a href=\"https://wikipedia.org/wiki/Battle_of_Cajamarca\" title=\"Battle of Cajamarca\">Battle of Cajamarca</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Francisco<PERSON>\" title=\"Francisco <PERSON>\"><PERSON></a> and his men capture <a href=\"https://wikipedia.org/wiki/Inca_Empire\" title=\"Inca Empire\">Inca</a> Emperor <a href=\"https://wikipedia.org/wiki/Atahualpa\" title=\"Atahualpa\">Atahualpa</a> at the <a href=\"https://wikipedia.org/wiki/Battle_of_Cajamarca\" title=\"Battle of Cajamarca\">Battle of Cajamarca</a>.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Francisco_<PERSON>"}, {"title": "Inca Empire", "link": "https://wikipedia.org/wiki/Inca_Empire"}, {"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Atahualpa"}, {"title": "Battle of Cajamarca", "link": "https://wikipedia.org/wiki/Battle_of_Cajamarca"}]}, {"year": "1632", "text": "King <PERSON><PERSON> of Sweden was killed at the Battle of Lützen during the Thirty Years' War.", "html": "1632 - <a href=\"https://wikipedia.org/wiki/King_<PERSON><PERSON>_<PERSON>_of_Sweden\" class=\"mw-redirect\" title=\"King <PERSON><PERSON> of Sweden\">King <PERSON><PERSON> of Sweden</a> was killed at the <a href=\"https://wikipedia.org/wiki/Battle_of_L%C3%BCtzen_(1632)\" title=\"Battle of Lützen (1632)\">Battle of Lützen</a> during the <a href=\"https://wikipedia.org/wiki/Thirty_Years%27_War\" title=\"Thirty Years' War\">Thirty Years' War</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/King_<PERSON><PERSON>_<PERSON>_of_Sweden\" class=\"mw-redirect\" title=\"King <PERSON><PERSON> of Sweden\">King <PERSON><PERSON> of Sweden</a> was killed at the <a href=\"https://wikipedia.org/wiki/Battle_of_L%C3%BCtzen_(1632)\" title=\"Battle of Lützen (1632)\">Battle of Lützen</a> during the <a href=\"https://wikipedia.org/wiki/Thirty_Years%27_War\" title=\"Thirty Years' War\">Thirty Years' War</a>.", "links": [{"title": "King <PERSON><PERSON> of Sweden", "link": "https://wikipedia.org/wiki/King_<PERSON><PERSON>_<PERSON><PERSON>_of_Sweden"}, {"title": "Battle of Lützen (1632)", "link": "https://wikipedia.org/wiki/Battle_of_L%C3%BCtzen_(1632)"}, {"title": "Thirty Years' War", "link": "https://wikipedia.org/wiki/Thirty_Years%27_War"}]}, {"year": "1776", "text": "American Revolutionary War: British and Hessian units capture Fort Washington from the Patriots.", "html": "1776 - <a href=\"https://wikipedia.org/wiki/American_Revolutionary_War\" title=\"American Revolutionary War\">American Revolutionary War</a>: British and <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_(soldier)\" title=\"<PERSON><PERSON> (soldier)\">Hessian</a> units <a href=\"https://wikipedia.org/wiki/Battle_of_Fort_Washington\" title=\"Battle of Fort Washington\">capture</a> <a href=\"https://wikipedia.org/wiki/Fort_Washington_(Manhattan)\" title=\"Fort Washington (Manhattan)\">Fort Washington</a> from the Patriots.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/American_Revolutionary_War\" title=\"American Revolutionary War\">American Revolutionary War</a>: British and <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_(soldier)\" title=\"<PERSON><PERSON> (soldier)\">Hessian</a> units <a href=\"https://wikipedia.org/wiki/Battle_of_Fort_Washington\" title=\"Battle of Fort Washington\">capture</a> <a href=\"https://wikipedia.org/wiki/Fort_Washington_(Manhattan)\" title=\"Fort Washington (Manhattan)\">Fort Washington</a> from the Patriots.", "links": [{"title": "American Revolutionary War", "link": "https://wikipedia.org/wiki/American_Revolutionary_War"}, {"title": "<PERSON><PERSON> (soldier)", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_(soldier)"}, {"title": "Battle of Fort Washington", "link": "https://wikipedia.org/wiki/Battle_of_Fort_Washington"}, {"title": "Fort Washington (Manhattan)", "link": "https://wikipedia.org/wiki/Fort_Washington_(Manhattan)"}]}, {"year": "1793", "text": "French Revolution: Ninety dissident Roman Catholic priests are executed by drowning at Nantes.", "html": "1793 - <a href=\"https://wikipedia.org/wiki/French_Revolution\" title=\"French Revolution\">French Revolution</a>: Ninety dissident Roman Catholic priests are <a href=\"https://wikipedia.org/wiki/Drownings_at_Nantes\" title=\"Drownings at Nantes\">executed by drowning</a> at <a href=\"https://wikipedia.org/wiki/Nantes\" title=\"Nantes\">Nantes</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/French_Revolution\" title=\"French Revolution\">French Revolution</a>: Ninety dissident Roman Catholic priests are <a href=\"https://wikipedia.org/wiki/Drownings_at_Nantes\" title=\"Drownings at Nantes\">executed by drowning</a> at <a href=\"https://wikipedia.org/wiki/Nantes\" title=\"Nantes\">Nantes</a>.", "links": [{"title": "French Revolution", "link": "https://wikipedia.org/wiki/French_Revolution"}, {"title": "Drownings at Nantes", "link": "https://wikipedia.org/wiki/Drownings_at_Nantes"}, {"title": "Nantes", "link": "https://wikipedia.org/wiki/Nantes"}]}, {"year": "1797", "text": "The Prussian heir apparent, <PERSON>, becomes King of Prussia as <PERSON>.", "html": "1797 - The Prussian heir apparent, <PERSON>, becomes King of Prussia as <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_of_Prussia\" title=\"<PERSON> of Prussia\"><PERSON> III</a>.", "no_year_html": "The Prussian heir apparent, <PERSON>, becomes King of Prussia as <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_of_Prussia\" title=\"<PERSON> of Prussia\"><PERSON> III</a>.", "links": [{"title": "<PERSON> of Prussia", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>_Prussia"}]}, {"year": "1805", "text": "Napoleonic Wars: Battle of Schöngrabern: Russian forces under <PERSON><PERSON><PERSON> delay the pursuit by French troops under <PERSON>.", "html": "1805 - <a href=\"https://wikipedia.org/wiki/Napoleonic_Wars\" title=\"Napoleonic Wars\">Napoleonic Wars</a>: <a href=\"https://wikipedia.org/wiki/Battle_of_Sch%C3%B6ngrabern\" title=\"Battle of Schöngrabern\">Battle of Schöngrabern</a>: Russian forces under <a href=\"https://wikipedia.org/wiki/Pyotr_Bagration\" title=\"Pyotr Ba<PERSON>\"><PERSON><PERSON><PERSON></a> delay the pursuit by French troops under <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Napoleonic_Wars\" title=\"Napoleonic Wars\">Napoleonic Wars</a>: <a href=\"https://wikipedia.org/wiki/Battle_of_Sch%C3%B6ngrabern\" title=\"Battle of Schöngrabern\">Battle of Schöngrabern</a>: Russian forces under <a href=\"https://wikipedia.org/wiki/Pyotr_Bagration\" title=\"Pyotr Ba<PERSON>\"><PERSON><PERSON><PERSON></a> delay the pursuit by French troops under <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>.", "links": [{"title": "Napoleonic Wars", "link": "https://wikipedia.org/wiki/Napoleonic_Wars"}, {"title": "Battle of Schöngrabern", "link": "https://wikipedia.org/wiki/Battle_of_Sch%C3%B6ngrabern"}, {"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1822", "text": "American Old West: Missouri trader <PERSON> arrives in Santa Fe, New Mexico, over a route that became known as the Santa Fe Trail.", "html": "1822 - <a href=\"https://wikipedia.org/wiki/American_Old_West\" class=\"mw-redirect\" title=\"American Old West\">American Old West</a>: <a href=\"https://wikipedia.org/wiki/Missouri\" title=\"Missouri\">Missouri</a> trader <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> arrives in <a href=\"https://wikipedia.org/wiki/Santa_Fe,_New_Mexico\" title=\"Santa Fe, New Mexico\">Santa Fe, New Mexico</a>, over a route that became known as the <a href=\"https://wikipedia.org/wiki/Santa_Fe_Trail\" title=\"Santa Fe Trail\">Santa Fe Trail</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/American_Old_West\" class=\"mw-redirect\" title=\"American Old West\">American Old West</a>: <a href=\"https://wikipedia.org/wiki/Missouri\" title=\"Missouri\">Missouri</a> trader <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> arrives in <a href=\"https://wikipedia.org/wiki/Santa_Fe,_New_Mexico\" title=\"Santa Fe, New Mexico\">Santa Fe, New Mexico</a>, over a route that became known as the <a href=\"https://wikipedia.org/wiki/Santa_Fe_Trail\" title=\"Santa Fe Trail\">Santa Fe Trail</a>.", "links": [{"title": "American Old West", "link": "https://wikipedia.org/wiki/American_Old_West"}, {"title": "Missouri", "link": "https://wikipedia.org/wiki/Missouri"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}, {"title": "Santa Fe, New Mexico", "link": "https://wikipedia.org/wiki/Santa_Fe,_New_Mexico"}, {"title": "Santa Fe Trail", "link": "https://wikipedia.org/wiki/Santa_Fe_Trail"}]}, {"year": "1828", "text": "Greek War of Independence: The London Protocol entails the creation of an autonomous Greek state under Ottoman suzerainty, encompassing the Morea and the Cyclades.", "html": "1828 - <a href=\"https://wikipedia.org/wiki/Greek_War_of_Independence\" title=\"Greek War of Independence\">Greek War of Independence</a>: The <a href=\"https://wikipedia.org/wiki/London_Protocol_(1828)\" title=\"London Protocol (1828)\">London Protocol</a> entails the creation of an autonomous Greek state under Ottoman suzerainty, encompassing the <a href=\"https://wikipedia.org/wiki/Morea\" title=\"Morea\">Morea</a> and the <a href=\"https://wikipedia.org/wiki/Cyclades\" title=\"Cyclades\">Cyclades</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Greek_War_of_Independence\" title=\"Greek War of Independence\">Greek War of Independence</a>: The <a href=\"https://wikipedia.org/wiki/London_Protocol_(1828)\" title=\"London Protocol (1828)\">London Protocol</a> entails the creation of an autonomous Greek state under Ottoman suzerainty, encompassing the <a href=\"https://wikipedia.org/wiki/Morea\" title=\"Morea\">Morea</a> and the <a href=\"https://wikipedia.org/wiki/Cyclades\" title=\"Cyclades\">Cyclades</a>.", "links": [{"title": "Greek War of Independence", "link": "https://wikipedia.org/wiki/Greek_War_of_Independence"}, {"title": "London Protocol (1828)", "link": "https://wikipedia.org/wiki/London_Protocol_(1828)"}, {"title": "Morea", "link": "https://wikipedia.org/wiki/Morea"}, {"title": "Cyclades", "link": "https://wikipedia.org/wiki/Cyclades"}]}, {"year": "1849", "text": "A Russian court sentences writer <PERSON><PERSON><PERSON> to death for anti-government activities linked to a radical intellectual group; his sentence is later commuted to hard labor.", "html": "1849 - A Russian court sentences writer <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a> to death for anti-government activities linked to a radical intellectual group; his sentence is later commuted to hard labor.", "no_year_html": "A Russian court sentences writer <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a> to death for anti-government activities linked to a radical intellectual group; his sentence is later commuted to hard labor.", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1855", "text": "<PERSON> becomes the first European to see the Victoria Falls in what is now Zambia-Zimbabwe.", "html": "1855 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> becomes the first European to see the <a href=\"https://wikipedia.org/wiki/Victoria_Falls,_Zambia\" class=\"mw-redirect\" title=\"Victoria Falls, Zambia\">Victoria Falls</a> in what is now <a href=\"https://wikipedia.org/wiki/Zambia\" title=\"Zambia\">Zambia</a>-<a href=\"https://wikipedia.org/wiki/Zimbabwe\" title=\"Zimbabwe\">Zimbabwe</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> becomes the first European to see the <a href=\"https://wikipedia.org/wiki/Victoria_Falls,_Zambia\" class=\"mw-redirect\" title=\"Victoria Falls, Zambia\">Victoria Falls</a> in what is now <a href=\"https://wikipedia.org/wiki/Zambia\" title=\"Zambia\">Zambia</a>-<a href=\"https://wikipedia.org/wiki/Zimbabwe\" title=\"Zimbabwe\">Zimbabwe</a>.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Victoria Falls, Zambia", "link": "https://wikipedia.org/wiki/Victoria_Falls,_Zambia"}, {"title": "Zambia", "link": "https://wikipedia.org/wiki/Zambia"}, {"title": "Zimbabwe", "link": "https://wikipedia.org/wiki/Zimbabwe"}]}, {"year": "1857", "text": "Second relief of Lucknow: Twenty-four Victoria Crosses are awarded, the most in a single day.", "html": "1857 - <a href=\"https://wikipedia.org/wiki/Siege_of_Lucknow\" title=\"Siege of Lucknow\">Second relief of Lucknow</a>: Twenty-four <a href=\"https://wikipedia.org/wiki/Victoria_Cross\" title=\"Victoria Cross\">Victoria Crosses</a> are awarded, the most in a single day.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Siege_of_Lucknow\" title=\"Siege of Lucknow\">Second relief of Lucknow</a>: Twenty-four <a href=\"https://wikipedia.org/wiki/Victoria_Cross\" title=\"Victoria Cross\">Victoria Crosses</a> are awarded, the most in a single day.", "links": [{"title": "Siege of Lucknow", "link": "https://wikipedia.org/wiki/Siege_of_Lucknow"}, {"title": "Victoria Cross", "link": "https://wikipedia.org/wiki/Victoria_Cross"}]}, {"year": "1863", "text": "American Civil War: In the Battle of Campbell's Station, Confederate troops unsuccessfully attack Union forces which allows General <PERSON> to secure Knoxville, Tennessee.", "html": "1863 - <a href=\"https://wikipedia.org/wiki/American_Civil_War\" title=\"American Civil War\">American Civil War</a>: In the <a href=\"https://wikipedia.org/wiki/Battle_of_Campbell%27s_Station\" title=\"Battle of Campbell's Station\">Battle of Campbell's Station</a>, <a href=\"https://wikipedia.org/wiki/Confederate_States_of_America\" title=\"Confederate States of America\">Confederate</a> troops unsuccessfully attack <a href=\"https://wikipedia.org/wiki/Union_(American_Civil_War)\" title=\"Union (American Civil War)\">Union</a> forces which allows General <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"Ambrose Burns<PERSON>\"><PERSON></a> to secure <a href=\"https://wikipedia.org/wiki/Knoxville,_Tennessee\" title=\"Knoxville, Tennessee\">Knoxville, Tennessee</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/American_Civil_War\" title=\"American Civil War\">American Civil War</a>: In the <a href=\"https://wikipedia.org/wiki/Battle_of_Campbell%27s_Station\" title=\"Battle of Campbell's Station\">Battle of Campbell's Station</a>, <a href=\"https://wikipedia.org/wiki/Confederate_States_of_America\" title=\"Confederate States of America\">Confederate</a> troops unsuccessfully attack <a href=\"https://wikipedia.org/wiki/Union_(American_Civil_War)\" title=\"Union (American Civil War)\">Union</a> forces which allows General <a href=\"https://wikipedia.org/wiki/Ambrose_<PERSON>\" title=\"Ambrose Burnside\"><PERSON></a> to secure <a href=\"https://wikipedia.org/wiki/Knoxville,_Tennessee\" title=\"Knoxville, Tennessee\">Knoxville, Tennessee</a>.", "links": [{"title": "American Civil War", "link": "https://wikipedia.org/wiki/American_Civil_War"}, {"title": "Battle of Campbell's Station", "link": "https://wikipedia.org/wiki/Battle_of_Campbell%27s_Station"}, {"title": "Confederate States of America", "link": "https://wikipedia.org/wiki/Confederate_States_of_America"}, {"title": "Union (American Civil War)", "link": "https://wikipedia.org/wiki/Union_(American_Civil_War)"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Knoxville, Tennessee", "link": "https://wikipedia.org/wiki/Knoxville,_Tennessee"}]}, {"year": "1871", "text": "The National Rifle Association of America receives its charter from New York State.", "html": "1871 - The <a href=\"https://wikipedia.org/wiki/National_Rifle_Association_of_America\" class=\"mw-redirect\" title=\"National Rifle Association of America\">National Rifle Association of America</a> receives its charter from New York State.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/National_Rifle_Association_of_America\" class=\"mw-redirect\" title=\"National Rifle Association of America\">National Rifle Association of America</a> receives its charter from New York State.", "links": [{"title": "National Rifle Association of America", "link": "https://wikipedia.org/wiki/National_Rifle_Association_of_America"}]}, {"year": "1885", "text": "Canadian rebel leader of the Métis and \"Father of Manitoba\" <PERSON> is executed for treason.", "html": "1885 - Canadian rebel leader of the <a href=\"https://wikipedia.org/wiki/M%C3%A9<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a> and \"Father of <a href=\"https://wikipedia.org/wiki/Manitoba\" title=\"Manitoba\">Manitoba</a>\" <a href=\"https://wikipedia.org/wiki/Louis_R<PERSON>\" title=\"Louis <PERSON>\"><PERSON></a> is executed for <a href=\"https://wikipedia.org/wiki/Treason\" title=\"Treason\">treason</a>.", "no_year_html": "Canadian rebel leader of the <a href=\"https://wikipedia.org/wiki/M%C3%A9tis\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a> and \"Father of <a href=\"https://wikipedia.org/wiki/Manitoba\" title=\"Manitoba\">Manitoba</a>\" <a href=\"https://wikipedia.org/wiki/Louis_R<PERSON>\" title=\"Louis Riel\"><PERSON></a> is executed for <a href=\"https://wikipedia.org/wiki/Treason\" title=\"Treason\">treason</a>.", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/M%C3%A9tis"}, {"title": "Manitoba", "link": "https://wikipedia.org/wiki/Manitoba"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Louis_<PERSON>"}, {"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Treason"}]}, {"year": "1904", "text": "English engineer <PERSON> receives a patent for the thermionic valve (vacuum tube).", "html": "1904 - English engineer <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> receives a patent for the <a href=\"https://wikipedia.org/wiki/Vacuum_tube\" title=\"Vacuum tube\">thermionic valve</a> (vacuum tube).", "no_year_html": "English engineer <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> receives a patent for the <a href=\"https://wikipedia.org/wiki/Vacuum_tube\" title=\"Vacuum tube\">thermionic valve</a> (vacuum tube).", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "Vacuum tube", "link": "https://wikipedia.org/wiki/Vacuum_tube"}]}, {"year": "1907", "text": "Indian Territory and Oklahoma Territory join to form Oklahoma, which is admitted as the 46th U.S. state.", "html": "1907 - <a href=\"https://wikipedia.org/wiki/Indian_Territory\" title=\"Indian Territory\">Indian Territory</a> and <a href=\"https://wikipedia.org/wiki/Oklahoma_Territory\" title=\"Oklahoma Territory\">Oklahoma Territory</a> join to form <a href=\"https://wikipedia.org/wiki/Oklahoma\" title=\"Oklahoma\">Oklahoma</a>, which is admitted as the 46th U.S. state.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Indian_Territory\" title=\"Indian Territory\">Indian Territory</a> and <a href=\"https://wikipedia.org/wiki/Oklahoma_Territory\" title=\"Oklahoma Territory\">Oklahoma Territory</a> join to form <a href=\"https://wikipedia.org/wiki/Oklahoma\" title=\"Oklahoma\">Oklahoma</a>, which is admitted as the 46th U.S. state.", "links": [{"title": "Indian Territory", "link": "https://wikipedia.org/wiki/Indian_Territory"}, {"title": "Oklahoma Territory", "link": "https://wikipedia.org/wiki/Oklahoma_Territory"}, {"title": "Oklahoma", "link": "https://wikipedia.org/wiki/Oklahoma"}]}, {"year": "1914", "text": "The Federal Reserve Bank of the United States officially opens.", "html": "1914 - The <a href=\"https://wikipedia.org/wiki/Federal_Reserve_Bank\" title=\"Federal Reserve Bank\">Federal Reserve Bank</a> of the <a href=\"https://wikipedia.org/wiki/United_States\" title=\"United States\">United States</a> officially opens.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Federal_Reserve_Bank\" title=\"Federal Reserve Bank\">Federal Reserve Bank</a> of the <a href=\"https://wikipedia.org/wiki/United_States\" title=\"United States\">United States</a> officially opens.", "links": [{"title": "Federal Reserve Bank", "link": "https://wikipedia.org/wiki/Federal_Reserve_Bank"}, {"title": "United States", "link": "https://wikipedia.org/wiki/United_States"}]}, {"year": "1920", "text": "Qantas, Australia's national airline, is founded as Queensland and Northern Territory Aerial Services Limited.", "html": "1920 - <a href=\"https://wikipedia.org/wiki/Qantas\" title=\"Qantas\">Qantas</a>, Australia's national airline, <a href=\"https://wikipedia.org/wiki/History_of_Qantas\" title=\"History of Qantas\">is founded</a> as Queensland and Northern Territory Aerial Services Limited.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Qantas\" title=\"Qantas\">Qantas</a>, Australia's national airline, <a href=\"https://wikipedia.org/wiki/History_of_Qantas\" title=\"History of Qantas\">is founded</a> as Queensland and Northern Territory Aerial Services Limited.", "links": [{"title": "Qantas", "link": "https://wikipedia.org/wiki/Qantas"}, {"title": "History of Qantas", "link": "https://wikipedia.org/wiki/History_of_Qantas"}]}, {"year": "1933", "text": "The United States and the Soviet Union establish formal diplomatic relations.", "html": "1933 - The United States and the <a href=\"https://wikipedia.org/wiki/Soviet_Union\" title=\"Soviet Union\">Soviet Union</a> establish formal <a href=\"https://wikipedia.org/wiki/Soviet_Union%E2%80%93United_States_relations\" title=\"Soviet Union-United States relations\">diplomatic relations</a>.", "no_year_html": "The United States and the <a href=\"https://wikipedia.org/wiki/Soviet_Union\" title=\"Soviet Union\">Soviet Union</a> establish formal <a href=\"https://wikipedia.org/wiki/Soviet_Union%E2%80%93United_States_relations\" title=\"Soviet Union-United States relations\">diplomatic relations</a>.", "links": [{"title": "Soviet Union", "link": "https://wikipedia.org/wiki/Soviet_Union"}, {"title": "Soviet Union-United States relations", "link": "https://wikipedia.org/wiki/Soviet_Union%E2%80%93United_States_relations"}]}, {"year": "1938", "text": "LSD is first synthesized by <PERSON> from ergotamine at the Sandoz Laboratories in Basel.", "html": "1938 - <a href=\"https://wikipedia.org/wiki/Lysergic_acid_diethylamide\" class=\"mw-redirect\" title=\"Lysergic acid diethylamide\">LSD</a> is first synthesized by <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> from ergotamine at the Sandoz Laboratories in Basel.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Lysergic_acid_diethylamide\" class=\"mw-redirect\" title=\"Lysergic acid diethylamide\">LSD</a> is first synthesized by <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> from ergotamine at the Sandoz Laboratories in Basel.", "links": [{"title": "Lysergic acid diethylamide", "link": "https://wikipedia.org/wiki/Lysergic_acid_diethylamide"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1940", "text": "World War II: In response to the leveling of Coventry by the German Luftwaffe two days before, the Royal Air Force bombs Hamburg.", "html": "1940 - <a href=\"https://wikipedia.org/wiki/World_War_II\" title=\"World War II\">World War II</a>: In response to the <a href=\"https://wikipedia.org/wiki/Coventry_Blitz\" title=\"Coventry Blitz\">leveling of Coventry</a> by the German Luftwaffe two days before, the Royal Air Force <a href=\"https://wikipedia.org/wiki/Bombing_of_Hamburg_in_World_War_II\" title=\"Bombing of Hamburg in World War II\">bombs Hamburg</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/World_War_II\" title=\"World War II\">World War II</a>: In response to the <a href=\"https://wikipedia.org/wiki/Coventry_Blitz\" title=\"Coventry Blitz\">leveling of Coventry</a> by the German Luftwaffe two days before, the Royal Air Force <a href=\"https://wikipedia.org/wiki/Bombing_of_Hamburg_in_World_War_II\" title=\"Bombing of Hamburg in World War II\">bombs Hamburg</a>.", "links": [{"title": "World War II", "link": "https://wikipedia.org/wiki/World_War_II"}, {"title": "Coventry Blitz", "link": "https://wikipedia.org/wiki/Coventry_Blitz"}, {"title": "Bombing of Hamburg in World War II", "link": "https://wikipedia.org/wiki/Bombing_of_Hamburg_in_World_War_II"}]}, {"year": "1940", "text": "The Holocaust: In occupied Poland, the Nazis close off the Warsaw Ghetto from the outside world.", "html": "1940 - <a href=\"https://wikipedia.org/wiki/The_Holocaust\" title=\"The Holocaust\">The Holocaust</a>: In occupied Poland, the <a href=\"https://wikipedia.org/wiki/Nazism\" title=\"Nazism\">Nazis</a> close off the <a href=\"https://wikipedia.org/wiki/Warsaw_Ghetto\" title=\"Warsaw Ghetto\">Warsaw Ghetto</a> from the outside world.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/The_Holocaust\" title=\"The Holocaust\">The Holocaust</a>: In occupied Poland, the <a href=\"https://wikipedia.org/wiki/Nazism\" title=\"Nazism\">Nazis</a> close off the <a href=\"https://wikipedia.org/wiki/Warsaw_Ghetto\" title=\"Warsaw Ghetto\">Warsaw Ghetto</a> from the outside world.", "links": [{"title": "The Holocaust", "link": "https://wikipedia.org/wiki/The_Holocaust"}, {"title": "Nazism", "link": "https://wikipedia.org/wiki/Nazism"}, {"title": "Warsaw Ghetto", "link": "https://wikipedia.org/wiki/Warsaw_Ghetto"}]}, {"year": "1940", "text": "New York City's \"Mad Bomber\" <PERSON> places his first bomb at a Manhattan office building used by Consolidated Edison.", "html": "1940 - New York City's \"Mad Bomber\" <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> places his first bomb at a <a href=\"https://wikipedia.org/wiki/Manhattan\" title=\"Manhattan\">Manhattan</a> office building used by <a href=\"https://wikipedia.org/wiki/Consolidated_Edison\" title=\"Consolidated Edison\">Consolidated Edison</a>.", "no_year_html": "New York City's \"Mad Bomber\" <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> places his first bomb at a <a href=\"https://wikipedia.org/wiki/Manhattan\" title=\"Manhattan\">Manhattan</a> office building used by <a href=\"https://wikipedia.org/wiki/Consolidated_Edison\" title=\"Consolidated Edison\">Consolidated Edison</a>.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Manhattan", "link": "https://wikipedia.org/wiki/Manhattan"}, {"title": "Consolidated Edison", "link": "https://wikipedia.org/wiki/Consolidated_Edison"}]}, {"year": "1944", "text": "World War II: In support of the Battle of Hürtgen Forest, the town of Düren is destroyed by Allied aircraft.", "html": "1944 - World War II: In support of the <a href=\"https://wikipedia.org/wiki/Battle_of_H%C3%BCrtgen_Forest\" title=\"Battle of Hürtgen Forest\">Battle of Hürtgen Forest</a>, the town of <a href=\"https://wikipedia.org/wiki/D%C3%BCren\" title=\"Düren\">D<PERSON>ren</a> is destroyed by Allied aircraft.", "no_year_html": "World War II: In support of the <a href=\"https://wikipedia.org/wiki/Battle_of_H%C3%BCrtgen_Forest\" title=\"Battle of Hürtgen Forest\">Battle of Hürtgen Forest</a>, the town of <a href=\"https://wikipedia.org/wiki/D%C3%BCren\" title=\"Düren\">D<PERSON>ren</a> is destroyed by Allied aircraft.", "links": [{"title": "Battle of Hürtgen Forest", "link": "https://wikipedia.org/wiki/Battle_of_H%C3%BCrtgen_Forest"}, {"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/D%C3%BCren"}]}, {"year": "1944", "text": "The Jussi Awards, the Finnish film award ceremony, is held for the first time at Restaurant Adlon in Helsinki.", "html": "1944 - The <a href=\"https://wikipedia.org/wiki/Jussi_Awards\" title=\"Jussi Awards\">Jussi Awards</a>, the Finnish film award ceremony, is held for the <a href=\"https://wikipedia.org/wiki/1st_Jussi_Awards\" title=\"1st Jussi Awards\">first time</a> at Restaurant Adlon in <a href=\"https://wikipedia.org/wiki/Helsinki\" title=\"Helsinki\">Helsinki</a>.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Jussi_Awards\" title=\"Jussi Awards\">Jussi Awards</a>, the Finnish film award ceremony, is held for the <a href=\"https://wikipedia.org/wiki/1st_Jussi_Awards\" title=\"1st Jussi Awards\">first time</a> at Restaurant Adlon in <a href=\"https://wikipedia.org/wiki/Helsinki\" title=\"Helsinki\">Helsinki</a>.", "links": [{"title": "Jussi Awards", "link": "https://wikipedia.org/wiki/Jussi_Awards"}, {"title": "1st Jussi Awards", "link": "https://wikipedia.org/wiki/1st_Ju<PERSON>_Awards"}, {"title": "Helsinki", "link": "https://wikipedia.org/wiki/Helsinki"}]}, {"year": "1945", "text": "UNESCO is founded.", "html": "1945 - <a href=\"https://wikipedia.org/wiki/UNESCO\" title=\"UNESCO\">UNESCO</a> is founded.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/UNESCO\" title=\"UNESCO\">UNESCO</a> is founded.", "links": [{"title": "UNESCO", "link": "https://wikipedia.org/wiki/UNESCO"}]}, {"year": "1958", "text": "National Airlines Flight 967 explodes in mid-air over the Gulf of Mexico, killing all 42 aboard.", "html": "1958 - <a href=\"https://wikipedia.org/wiki/National_Airlines_Flight_967\" title=\"National Airlines Flight 967\">National Airlines Flight 967</a> explodes in mid-air over the Gulf of Mexico, killing all 42 aboard.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/National_Airlines_Flight_967\" title=\"National Airlines Flight 967\">National Airlines Flight 967</a> explodes in mid-air over the Gulf of Mexico, killing all 42 aboard.", "links": [{"title": "National Airlines Flight 967", "link": "https://wikipedia.org/wiki/National_Airlines_Flight_967"}]}, {"year": "1959", "text": "Aeroflot Flight 315 crashes on approach to Lviv Airport, killing all 40 people on board.", "html": "1959 - <a href=\"https://wikipedia.org/wiki/Aeroflot_Flight_315_(1959)\" title=\"Aeroflot Flight 315 (1959)\">Aeroflot Flight 315</a> crashes on approach to Lviv Airport, killing all 40 people on board.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Aeroflot_Flight_315_(1959)\" title=\"Aeroflot Flight 315 (1959)\">Aeroflot Flight 315</a> crashes on approach to Lviv Airport, killing all 40 people on board.", "links": [{"title": "Aeroflot Flight 315 (1959)", "link": "https://wikipedia.org/wiki/Aeroflot_Flight_315_(1959)"}]}, {"year": "1965", "text": "Venera program: The Soviet Union launches the Venera 3 space probe toward Venus, which will be the first spacecraft to reach the surface of another planet.", "html": "1965 - <a href=\"https://wikipedia.org/wiki/Venera_program\" class=\"mw-redirect\" title=\"Venera program\">Venera program</a>: The Soviet Union launches the <i><a href=\"https://wikipedia.org/wiki/Venera_3\" title=\"Venera 3\">Venera 3</a></i> space probe toward <a href=\"https://wikipedia.org/wiki/Venus\" title=\"Venus\">Venus</a>, which will be the first <a href=\"https://wikipedia.org/wiki/Spacecraft\" title=\"Spacecraft\">spacecraft</a> to reach the surface of another <a href=\"https://wikipedia.org/wiki/Planet\" title=\"Planet\">planet</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Venera_program\" class=\"mw-redirect\" title=\"Venera program\">Venera program</a>: The Soviet Union launches the <i><a href=\"https://wikipedia.org/wiki/Venera_3\" title=\"Venera 3\">Venera 3</a></i> space probe toward <a href=\"https://wikipedia.org/wiki/Venus\" title=\"Venus\">Venus</a>, which will be the first <a href=\"https://wikipedia.org/wiki/Spacecraft\" title=\"Spacecraft\">spacecraft</a> to reach the surface of another <a href=\"https://wikipedia.org/wiki/Planet\" title=\"Planet\">planet</a>.", "links": [{"title": "Venera program", "link": "https://wikipedia.org/wiki/Venera_program"}, {"title": "Venera 3", "link": "https://wikipedia.org/wiki/Venera_3"}, {"title": "Venus", "link": "https://wikipedia.org/wiki/Venus"}, {"title": "Spacecraft", "link": "https://wikipedia.org/wiki/Spacecraft"}, {"title": "Planet", "link": "https://wikipedia.org/wiki/Planet"}]}, {"year": "1967", "text": "Aeroflot Flight 2230 crashes near Koltsovo Airport, killing 107.", "html": "1967 - <a href=\"https://wikipedia.org/wiki/Aeroflot_Flight_2230\" title=\"Aeroflot Flight 2230\">Aeroflot Flight 2230</a> crashes near <a href=\"https://wikipedia.org/wiki/Koltsovo_International_Airport\" title=\"Koltsovo International Airport\">Koltsovo Airport</a>, killing 107.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Aeroflot_Flight_2230\" title=\"Aeroflot Flight 2230\">Aeroflot Flight 2230</a> crashes near <a href=\"https://wikipedia.org/wiki/Koltsovo_International_Airport\" title=\"Koltsovo International Airport\">Koltsovo Airport</a>, killing 107.", "links": [{"title": "Aeroflot Flight 2230", "link": "https://wikipedia.org/wiki/Aeroflot_Flight_2230"}, {"title": "Koltsovo International Airport", "link": "https://wikipedia.org/wiki/Koltsovo_International_Airport"}]}, {"year": "1973", "text": "Skylab program: NASA launches Skylab 4 with a crew of three astronauts from Cape Canaveral, Florida for an 84-day mission.", "html": "1973 - <a href=\"https://wikipedia.org/wiki/Skylab\" title=\"Skylab\">Skylab</a> program: <a href=\"https://wikipedia.org/wiki/NASA\" title=\"NASA\">NASA</a> launches <i><a href=\"https://wikipedia.org/wiki/Skylab_4\" title=\"Skylab 4\">Skylab 4</a></i> with a crew of three <a href=\"https://wikipedia.org/wiki/Astronaut\" title=\"Astronaut\">astronauts</a> from <a href=\"https://wikipedia.org/wiki/Cape_Canaveral,_Florida\" title=\"Cape Canaveral, Florida\">Cape Canaveral, Florida</a> for an 84-day mission.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Skylab\" title=\"Skylab\">Skylab</a> program: <a href=\"https://wikipedia.org/wiki/NASA\" title=\"NASA\">NASA</a> launches <i><a href=\"https://wikipedia.org/wiki/Skylab_4\" title=\"Skylab 4\">Skylab 4</a></i> with a crew of three <a href=\"https://wikipedia.org/wiki/Astronaut\" title=\"Astronaut\">astronauts</a> from <a href=\"https://wikipedia.org/wiki/Cape_Canaveral,_Florida\" title=\"Cape Canaveral, Florida\">Cape Canaveral, Florida</a> for an 84-day mission.", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Skylab"}, {"title": "NASA", "link": "https://wikipedia.org/wiki/NASA"}, {"title": "Skylab 4", "link": "https://wikipedia.org/wiki/Skylab_4"}, {"title": "Astronaut", "link": "https://wikipedia.org/wiki/Astronaut"}, {"title": "Cape Canaveral, Florida", "link": "https://wikipedia.org/wiki/Cape_Canaveral,_Florida"}]}, {"year": "1973", "text": "U.S. President <PERSON> signs the Trans-Alaska Pipeline Authorization Act into law, authorizing the construction of the Alaska Pipeline.", "html": "1973 - U.S. President <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> signs the <a href=\"https://wikipedia.org/wiki/Trans-Alaska_Pipeline_Authorization_Act\" title=\"Trans-Alaska Pipeline Authorization Act\">Trans-Alaska Pipeline Authorization Act</a> into law, authorizing the construction of the <a href=\"https://wikipedia.org/wiki/Alaska_Pipeline\" class=\"mw-redirect\" title=\"Alaska Pipeline\">Alaska Pipeline</a>.", "no_year_html": "U.S. President <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> signs the <a href=\"https://wikipedia.org/wiki/Trans-Alaska_Pipeline_Authorization_Act\" title=\"Trans-Alaska Pipeline Authorization Act\">Trans-Alaska Pipeline Authorization Act</a> into law, authorizing the construction of the <a href=\"https://wikipedia.org/wiki/Alaska_Pipeline\" class=\"mw-redirect\" title=\"Alaska Pipeline\">Alaska Pipeline</a>.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Trans-Alaska Pipeline Authorization Act", "link": "https://wikipedia.org/wiki/Trans-Alaska_Pipeline_Authorization_Act"}, {"title": "Alaska Pipeline", "link": "https://wikipedia.org/wiki/Alaska_Pipeline"}]}, {"year": "1974", "text": "The Arecibo message is broadcast from Puerto Rico.", "html": "1974 - The <a href=\"https://wikipedia.org/wiki/Arecibo_message\" title=\"Arecibo message\"><PERSON><PERSON><PERSON> message</a> is broadcast from Puerto Rico.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Arecibo_message\" title=\"Arecibo message\"><PERSON><PERSON><PERSON> message</a> is broadcast from Puerto Rico.", "links": [{"title": "Arecibo message", "link": "https://wikipedia.org/wiki/Arecibo_message"}]}, {"year": "1979", "text": "The first line of Bucharest Metro (Line M1) is opened from Timpuri Noi to Semănătoarea in Bucharest, Romania.", "html": "1979 - The first line of <a href=\"https://wikipedia.org/wiki/Bucharest_Metro\" title=\"Bucharest Metro\">Bucharest Metro</a> (Line M1) is opened from <a href=\"https://wikipedia.org/wiki/Timpuri_Noi_metro_station\" title=\"Timpuri Noi metro station\">Timpuri Noi</a> to <a href=\"https://wikipedia.org/wiki/Petrache_Poenaru_metro_station\" title=\"Petrache Poenaru metro station\">Semănătoarea</a> in <a href=\"https://wikipedia.org/wiki/Bucharest\" title=\"Bucharest\">Bucharest</a>, Romania.", "no_year_html": "The first line of <a href=\"https://wikipedia.org/wiki/Bucharest_Metro\" title=\"Bucharest Metro\">Bucharest Metro</a> (Line M1) is opened from <a href=\"https://wikipedia.org/wiki/Timpuri_Noi_metro_station\" title=\"Timpuri Noi metro station\">Timpuri Noi</a> to <a href=\"https://wikipedia.org/wiki/Petrache_Poenaru_metro_station\" title=\"Petrache Poenaru metro station\">Semănătoarea</a> in <a href=\"https://wikipedia.org/wiki/Bucharest\" title=\"Bucharest\">Bucharest</a>, Romania.", "links": [{"title": "Bucharest Metro", "link": "https://wikipedia.org/wiki/Bucharest_Metro"}, {"title": "Timpuri Noi metro station", "link": "https://wikipedia.org/wiki/Timpuri_Noi_metro_station"}, {"title": "Petrache Poenaru metro station", "link": "https://wikipedia.org/wiki/Petrache_Poenaru_metro_station"}, {"title": "Bucharest", "link": "https://wikipedia.org/wiki/Bucharest"}]}, {"year": "1981", "text": "Aeroflot Flight 3603 crashes during landing at Norilsk Airport, killing 99.", "html": "1981 - <a href=\"https://wikipedia.org/wiki/Aeroflot_Flight_3603\" title=\"Aeroflot Flight 3603\">Aeroflot Flight 3603</a> crashes during landing at <a href=\"https://wikipedia.org/wiki/Alykel_International_Airport\" title=\"Alykel International Airport\">Norilsk Airport</a>, killing 99.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Aeroflot_Flight_3603\" title=\"Aeroflot Flight 3603\">Aeroflot Flight 3603</a> crashes during landing at <a href=\"https://wikipedia.org/wiki/Alykel_International_Airport\" title=\"Alykel International Airport\">Norilsk Airport</a>, killing 99.", "links": [{"title": "Aeroflot Flight 3603", "link": "https://wikipedia.org/wiki/Aeroflot_Flight_3603"}, {"title": "Alykel International Airport", "link": "https://wikipedia.org/wiki/Alykel_International_Airport"}]}, {"year": "1988", "text": "The Supreme Soviet of the Estonian Soviet Socialist Republic declares that Estonia is \"sovereign\" but stops short of declaring independence.", "html": "1988 - The <a href=\"https://wikipedia.org/wiki/Supreme_Soviet\" title=\"Supreme Soviet\">Supreme Soviet</a> of the <a href=\"https://wikipedia.org/wiki/Estonian_Soviet_Socialist_Republic\" title=\"Estonian Soviet Socialist Republic\">Estonian Soviet Socialist Republic</a> <a href=\"https://wikipedia.org/wiki/Estonian_Sovereignty_Declaration\" title=\"Estonian Sovereignty Declaration\">declares</a> that <a href=\"https://wikipedia.org/wiki/Estonia\" title=\"Estonia\">Estonia</a> is \"sovereign\" but stops short of declaring independence.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Supreme_Soviet\" title=\"Supreme Soviet\">Supreme Soviet</a> of the <a href=\"https://wikipedia.org/wiki/Estonian_Soviet_Socialist_Republic\" title=\"Estonian Soviet Socialist Republic\">Estonian Soviet Socialist Republic</a> <a href=\"https://wikipedia.org/wiki/Estonian_Sovereignty_Declaration\" title=\"Estonian Sovereignty Declaration\">declares</a> that <a href=\"https://wikipedia.org/wiki/Estonia\" title=\"Estonia\">Estonia</a> is \"sovereign\" but stops short of declaring independence.", "links": [{"title": "Supreme Soviet", "link": "https://wikipedia.org/wiki/Supreme_Soviet"}, {"title": "Estonian Soviet Socialist Republic", "link": "https://wikipedia.org/wiki/Estonian_Soviet_Socialist_Republic"}, {"title": "Estonian Sovereignty Declaration", "link": "https://wikipedia.org/wiki/Estonian_Sovereignty_Declaration"}, {"title": "Estonia", "link": "https://wikipedia.org/wiki/Estonia"}]}, {"year": "1988", "text": "In the first open election in more than a decade, voters in Pakistan elect populist candidate <PERSON><PERSON><PERSON> to be Prime Minister of Pakistan.", "html": "1988 - In the first open election in more than a decade, voters in <a href=\"https://wikipedia.org/wiki/Pakistan\" title=\"Pakistan\">Pakistan</a> elect populist candidate <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a> to be <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Pakistan\" title=\"Prime Minister of Pakistan\">Prime Minister of Pakistan</a>.", "no_year_html": "In the first open election in more than a decade, voters in <a href=\"https://wikipedia.org/wiki/Pakistan\" title=\"Pakistan\">Pakistan</a> elect populist candidate <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a> to be <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Pakistan\" title=\"Prime Minister of Pakistan\">Prime Minister of Pakistan</a>.", "links": [{"title": "Pakistan", "link": "https://wikipedia.org/wiki/Pakistan"}, {"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON><PERSON><PERSON>"}, {"title": "Prime Minister of Pakistan", "link": "https://wikipedia.org/wiki/Prime_Minister_of_Pakistan"}]}, {"year": "1989", "text": "El Salvadoran army troops kill six Jesuit priests and two others at Jose Simeon Canas University.", "html": "1989 - El Salvadoran army troops <a href=\"https://wikipedia.org/wiki/1989_murders_of_Jesuits_in_El_Salvador\" title=\"1989 murders of Jesuits in El Salvador\">kill six Jesuit priests</a> and two others at <a href=\"https://wikipedia.org/wiki/Central_American_University_(San_Salvador)\" class=\"mw-redirect\" title=\"Central American University (San Salvador)\">Jose <PERSON> University</a>.", "no_year_html": "El Salvadoran army troops <a href=\"https://wikipedia.org/wiki/1989_murders_of_Jesuits_in_El_Salvador\" title=\"1989 murders of Jesuits in El Salvador\">kill six Jesuit priests</a> and two others at <a href=\"https://wikipedia.org/wiki/Central_American_University_(San_Salvador)\" class=\"mw-redirect\" title=\"Central American University (San Salvador)\">Jose <PERSON> University</a>.", "links": [{"title": "1989 murders of Jesuits in El Salvador", "link": "https://wikipedia.org/wiki/1989_murders_of_Jesuits_in_El_Salvador"}, {"title": "Central American University (San Salvador)", "link": "https://wikipedia.org/wiki/Central_American_University_(San_Salvador)"}]}, {"year": "1990", "text": "Pop group <PERSON><PERSON> are stripped of their Grammy Award because the duo did not sing at all on the Girl You Know It's True album. Session musicians had provided all the vocals.", "html": "1990 - <a href=\"https://wikipedia.org/wiki/Pop_music\" title=\"Pop music\">Pop group</a> <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a> are stripped of their <a href=\"https://wikipedia.org/wiki/Grammy_Award\" class=\"mw-redirect\" title=\"Grammy Award\">Grammy Award</a> because the duo did not sing at all on the <i><a href=\"https://wikipedia.org/wiki/All_or_Nothing_(<PERSON><PERSON>_<PERSON>_album)\" title=\"All or Nothing (<PERSON><PERSON> album)\">Girl You Know It's True</a></i> album. <a href=\"https://wikipedia.org/wiki/Session_musician\" title=\"Session musician\">Session musicians</a> had provided all the vocals.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Pop_music\" title=\"Pop music\">Pop group</a> <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a> are stripped of their <a href=\"https://wikipedia.org/wiki/Grammy_Award\" class=\"mw-redirect\" title=\"Grammy Award\">Grammy Award</a> because the duo did not sing at all on the <i><a href=\"https://wikipedia.org/wiki/All_or_Nothing_(<PERSON><PERSON>_<PERSON>_album)\" title=\"All or Nothing (<PERSON><PERSON> album)\">Girl You Know It's True</a></i> album. <a href=\"https://wikipedia.org/wiki/Session_musician\" title=\"Session musician\">Session musicians</a> had provided all the vocals.", "links": [{"title": "Pop music", "link": "https://wikipedia.org/wiki/Pop_music"}, {"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>i"}, {"title": "Grammy Award", "link": "https://wikipedia.org/wiki/Grammy_Award"}, {"title": "All or Nothing (<PERSON><PERSON> album)", "link": "https://wikipedia.org/wiki/All_or_Nothing_(<PERSON><PERSON>_<PERSON><PERSON><PERSON>_album)"}, {"title": "Session musician", "link": "https://wikipedia.org/wiki/Session_musician"}]}, {"year": "1992", "text": "The Hoxne Hoard is discovered by metal detectorist <PERSON> in Hoxne, Suffolk.", "html": "1992 - The <a href=\"https://wikipedia.org/wiki/Hoxne_Hoard\" title=\"Hoxne Hoard\"><PERSON><PERSON><PERSON> Hoard</a> is discovered by <a href=\"https://wikipedia.org/wiki/Metal_detector\" title=\"Metal detector\">metal detectorist</a> <PERSON> in <a href=\"https://wikipedia.org/wiki/Hoxne\" title=\"Hoxne\">Hoxne</a>, Suffolk.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Hoxne_Hoard\" title=\"Hoxne Hoard\"><PERSON><PERSON><PERSON> Hoard</a> is discovered by <a href=\"https://wikipedia.org/wiki/Metal_detector\" title=\"Metal detector\">metal detectorist</a> <PERSON> in <a href=\"https://wikipedia.org/wiki/Hoxne\" title=\"Hoxne\">Ho<PERSON><PERSON></a>, Suffolk.", "links": [{"title": "<PERSON><PERSON><PERSON> Hoard", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_Hoard"}, {"title": "Metal detector", "link": "https://wikipedia.org/wiki/Metal_detector"}, {"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Hoxne"}]}, {"year": "1997", "text": "After nearly 18 years of incarceration, China releases <PERSON>, a pro-democracy dissident, from jail for medical reasons.", "html": "1997 - After nearly 18 years of incarceration, <a href=\"https://wikipedia.org/wiki/China\" title=\"China\">China</a> releases <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, a pro-democracy dissident, from jail for medical reasons.", "no_year_html": "After nearly 18 years of incarceration, <a href=\"https://wikipedia.org/wiki/China\" title=\"China\">China</a> releases <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, a pro-democracy dissident, from jail for medical reasons.", "links": [{"title": "China", "link": "https://wikipedia.org/wiki/China"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2002", "text": "The first cases of the 2002-2004 SARS outbreak are traced to Foshan, Guangdong Province, China.", "html": "2002 - The first cases of the <a href=\"https://wikipedia.org/wiki/2002%E2%80%932004_SARS_outbreak\" title=\"2002-2004 SARS outbreak\">2002-2004 SARS outbreak</a> are traced to <a href=\"https://wikipedia.org/wiki/Foshan\" title=\"Foshan\">Foshan</a>, Guangdong Province, China.", "no_year_html": "The first cases of the <a href=\"https://wikipedia.org/wiki/2002%E2%80%932004_SARS_outbreak\" title=\"2002-2004 SARS outbreak\">2002-2004 SARS outbreak</a> are traced to <a href=\"https://wikipedia.org/wiki/Foshan\" title=\"Foshan\">F<PERSON>an</a>, Guangdong Province, China.", "links": [{"title": "2002-2004 SARS outbreak", "link": "https://wikipedia.org/wiki/2002%E2%80%932004_SARS_outbreak"}, {"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>an"}]}, {"year": "2004", "text": "Half-Life 2 is released, a game winning 39 Game of the Year awards and being cited as one of the best games ever made.", "html": "2004 - <a href=\"https://wikipedia.org/wiki/Half-Life_2\" title=\"Half-Life 2\">Half-Life 2</a> is released, a game winning 39 Game of the Year awards and being cited as one of the best games ever made.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Half-Life_2\" title=\"Half-Life 2\">Half-Life 2</a> is released, a game winning 39 Game of the Year awards and being cited as one of the best games ever made.", "links": [{"title": "Half-Life 2", "link": "https://wikipedia.org/wiki/Half-Life_2"}]}, {"year": "2005", "text": "Following a 31-year wait, Australia defeats Uruguay in a penalty shootout to qualify for the 2006 FIFA World Cup.", "html": "2005 - Following a 31-year wait, <a href=\"https://wikipedia.org/wiki/Australia_men%27s_national_soccer_team\" title=\"Australia men's national soccer team\">Australia</a> defeats <a href=\"https://wikipedia.org/wiki/Uruguay_national_football_team\" title=\"Uruguay national football team\">Uruguay</a> in a <a href=\"https://wikipedia.org/wiki/Penalty_shootout\" title=\"Penalty shootout\">penalty shootout</a> to qualify for the <a href=\"https://wikipedia.org/wiki/2006_FIFA_World_Cup\" title=\"2006 FIFA World Cup\">2006 FIFA World Cup</a>.", "no_year_html": "Following a 31-year wait, <a href=\"https://wikipedia.org/wiki/Australia_men%27s_national_soccer_team\" title=\"Australia men's national soccer team\">Australia</a> defeats <a href=\"https://wikipedia.org/wiki/Uruguay_national_football_team\" title=\"Uruguay national football team\">Uruguay</a> in a <a href=\"https://wikipedia.org/wiki/Penalty_shootout\" title=\"Penalty shootout\">penalty shootout</a> to qualify for the <a href=\"https://wikipedia.org/wiki/2006_FIFA_World_Cup\" title=\"2006 FIFA World Cup\">2006 FIFA World Cup</a>.", "links": [{"title": "Australia men's national soccer team", "link": "https://wikipedia.org/wiki/Australia_men%27s_national_soccer_team"}, {"title": "Uruguay national football team", "link": "https://wikipedia.org/wiki/Uruguay_national_football_team"}, {"title": "Penalty shootout", "link": "https://wikipedia.org/wiki/Penalty_shootout"}, {"title": "2006 FIFA World Cup", "link": "https://wikipedia.org/wiki/2006_FIFA_World_Cup"}]}, {"year": "2009", "text": "Space Shuttle program: Space Shuttle Atlantis is launched on mission STS-129 to the International Space Station.", "html": "2009 - <a href=\"https://wikipedia.org/wiki/Space_Shuttle_program\" title=\"Space Shuttle program\">Space Shuttle program</a>: <a href=\"https://wikipedia.org/wiki/Space_Shuttle_Atlantis\" title=\"Space Shuttle Atlantis\">Space Shuttle <i>Atlantis</i></a> is launched on mission <a href=\"https://wikipedia.org/wiki/STS-129\" title=\"STS-129\">STS-129</a> to the <a href=\"https://wikipedia.org/wiki/International_Space_Station\" title=\"International Space Station\">International Space Station</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Space_Shuttle_program\" title=\"Space Shuttle program\">Space Shuttle program</a>: <a href=\"https://wikipedia.org/wiki/Space_Shuttle_Atlantis\" title=\"Space Shuttle Atlantis\">Space Shuttle <i>Atlantis</i></a> is launched on mission <a href=\"https://wikipedia.org/wiki/STS-129\" title=\"STS-129\">STS-129</a> to the <a href=\"https://wikipedia.org/wiki/International_Space_Station\" title=\"International Space Station\">International Space Station</a>.", "links": [{"title": "Space Shuttle program", "link": "https://wikipedia.org/wiki/Space_Shuttle_program"}, {"title": "Space Shuttle Atlantis", "link": "https://wikipedia.org/wiki/Space_Shuttle_Atlantis"}, {"title": "STS-129", "link": "https://wikipedia.org/wiki/STS-129"}, {"title": "International Space Station", "link": "https://wikipedia.org/wiki/International_Space_Station"}]}, {"year": "2022", "text": "Artemis Program: NASA launches Artemis 1 on the first flight of the Space Launch System, the start of the program's future missions to the moon.", "html": "2022 - <a href=\"https://wikipedia.org/wiki/Artemis_Program\" class=\"mw-redirect\" title=\"Artemis Program\">Artemis Program</a>: <a href=\"https://wikipedia.org/wiki/NASA\" title=\"NASA\">NASA</a> launches <i><a href=\"https://wikipedia.org/wiki/Artemis_1\" class=\"mw-redirect\" title=\"Artemis 1\">Artemis 1</a></i> on the first flight of the <a href=\"https://wikipedia.org/wiki/Space_Launch_System\" title=\"Space Launch System\">Space Launch System</a>, the start of the program's future missions to the moon.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Artemis_Program\" class=\"mw-redirect\" title=\"Artemis Program\">Artemis Program</a>: <a href=\"https://wikipedia.org/wiki/NASA\" title=\"NASA\">NASA</a> launches <i><a href=\"https://wikipedia.org/wiki/Artemis_1\" class=\"mw-redirect\" title=\"Artemis 1\">Artemis 1</a></i> on the first flight of the <a href=\"https://wikipedia.org/wiki/Space_Launch_System\" title=\"Space Launch System\">Space Launch System</a>, the start of the program's future missions to the moon.", "links": [{"title": "Artemis Program", "link": "https://wikipedia.org/wiki/Artemis_Program"}, {"title": "NASA", "link": "https://wikipedia.org/wiki/NASA"}, {"title": "Artemis 1", "link": "https://wikipedia.org/wiki/Artemis_1"}, {"title": "Space Launch System", "link": "https://wikipedia.org/wiki/Space_Launch_System"}]}], "Births": [{"year": "42 BC", "text": "<PERSON><PERSON><PERSON>, Roman emperor (d. 37 AD)", "html": "42 BC - 42 BC - <a href=\"https://wikipedia.org/wiki/Tiberius\" title=\"Tiber<PERSON>\"><PERSON><PERSON><PERSON></a>, Roman emperor (d. 37 AD)", "no_year_html": "42 BC - <a href=\"https://wikipedia.org/wiki/Tiber<PERSON>\" title=\"<PERSON>iber<PERSON>\"><PERSON><PERSON><PERSON></a>, Roman emperor (d. 37 AD)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Tiberius"}]}, {"year": "1436", "text": "<PERSON>, Italian ruler (d. 1521)", "html": "1436 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian ruler (d. 1521)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian ruler (d. 1521)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1457", "text": "<PERSON> of Naples, Hungarian queen (d. 1508)", "html": "1457 - <a href=\"https://wikipedia.org/wiki/<PERSON>_of_Naples\" title=\"Beatrice of Naples\"><PERSON> of Naples</a>, Hungarian queen (d. 1508)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_of_Naples\" title=\"Beatrice of Naples\"><PERSON> of Naples</a>, Hungarian queen (d. 1508)", "links": [{"title": "<PERSON> of Naples", "link": "https://wikipedia.org/wiki/<PERSON>_of_Naples"}]}, {"year": "1466", "text": "<PERSON>, Florentine philosopher (d. 1522)", "html": "1466 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>\" title=\"Francesco Cattani da Diacceto\"><PERSON>to</a>, Florentine philosopher (d. 1522)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>\" title=\"Francesco Cattani da Diacceto\"><PERSON>to</a>, Florentine philosopher (d. 1522)", "links": [{"title": "<PERSON> Diacceto", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1483", "text": "<PERSON> of the Palatinate, <PERSON><PERSON><PERSON><PERSON> of Hesse, German noble (d. 1522)", "html": "1483 - <a href=\"https://wikipedia.org/wiki/<PERSON>_of_the_Palatinate,_Landgravine_of_Hesse\" title=\"Elisabeth of the Palatinate, Landgravine of Hesse\"><PERSON> of the Palatinate, Landgravin<PERSON> of Hesse</a>, German noble (d. 1522)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_of_the_Palatinate,_Landgravine_of_Hesse\" title=\"Elisabeth of the Palatinate, Landgravine of Hesse\"><PERSON> of the Palatinate, Landgravine of Hesse</a>, German noble (d. 1522)", "links": [{"title": "<PERSON> of the Palatinate, Landgravine of Hesse", "link": "https://wikipedia.org/wiki/<PERSON>_of_the_Palatinate,_Landgravine_of_Hesse"}]}, {"year": "1528", "text": "<PERSON>, Queen of Navarre (d. 1572)", "html": "1528 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%27Albret\" title=\"<PERSON>\"><PERSON></a>, Queen of Navarre (d. 1572)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%27Albret\" title=\"<PERSON>\"><PERSON></a>, Queen of Navarre (d. 1572)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>%27Al<PERSON>t"}]}, {"year": "1531", "text": "<PERSON>, Duchess consort of Nemours (d. 1607)", "html": "1531 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%27Este\" title=\"<PERSON>\"><PERSON></a>, Duchess consort of Nemours (d. 1607)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%27Este\" title=\"<PERSON>\"><PERSON></a>, Duchess consort of Nemours (d. 1607)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Anna_d%27Este"}]}, {"year": "1538", "text": "<PERSON> <PERSON><PERSON><PERSON> of Mongrovejo, Spanish Grand Inquisitioner, Archbishop of Lima (d. 1606)", "html": "1538 - <PERSON> <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_of_Mongrovejo\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON> of Mongrovejo\"><PERSON><PERSON><PERSON> of Mongrovejo</a>, Spanish Grand Inquisitioner, Archbishop of Lima (d. 1606)", "no_year_html": "<PERSON> <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_of_Mongrovejo\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON> of Mongrovejo\"><PERSON><PERSON><PERSON> of Mongrovejo</a>, Spanish Grand Inquisitioner, Archbishop of Lima (d. 1606)", "links": [{"title": "<PERSON><PERSON><PERSON> of Mongrovejo", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_of_Mongrovejo"}]}, {"year": "1540", "text": "Princess <PERSON> of Sweden (d. 1627)", "html": "1540 - <a href=\"https://wikipedia.org/wiki/Princess_<PERSON>_of_Sweden\" title=\"Princess <PERSON> of Sweden\">Princess <PERSON> of Sweden</a> (d. 1627)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Princess_<PERSON>_of_Sweden\" title=\"Princess <PERSON> of Sweden\">Princess <PERSON> of Sweden</a> (d. 1627)", "links": [{"title": "Princess <PERSON> of Sweden", "link": "https://wikipedia.org/wiki/Princess_<PERSON>_of_Sweden"}]}, {"year": "1566", "text": "<PERSON>, Archduchess of Austria and nun (d. 1621)", "html": "1566 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Archduchess of Austria and nun (d. 1621)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Archduchess of Austria and nun (d. 1621)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1569", "text": "<PERSON>, German organist and composer (d. 1609)", "html": "1569 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(composer)\" title=\"<PERSON> (composer)\"><PERSON></a>, German organist and composer (d. 1609)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(composer)\" title=\"<PERSON> (composer)\"><PERSON></a>, German organist and composer (d. 1609)", "links": [{"title": "<PERSON> (composer)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(composer)"}]}, {"year": "1603", "text": "<PERSON><PERSON>, Polish monk (d. 1673)", "html": "1603 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Polish monk (d. 1673)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Polish monk (d. 1673)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1643", "text": "<PERSON>, French-English jeweler and explorer (d. 1703)", "html": "1643 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French-English jeweler and explorer (d. 1703)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French-English jeweler and explorer (d. 1703)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1648", "text": "<PERSON>, English banker and politician (d. 1711)", "html": "1648 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(English_banker)\" title=\"<PERSON> (English banker)\"><PERSON></a>, English banker and politician (d. 1711)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(English_banker)\" title=\"<PERSON> (English banker)\"><PERSON></a>, English banker and politician (d. 1711)", "links": [{"title": "<PERSON> (English banker)", "link": "https://wikipedia.org/wiki/<PERSON>_(English_banker)"}]}, {"year": "1715", "text": "<PERSON><PERSON><PERSON>, Maltese-Italian composer and educator (d. 1760)", "html": "1715 - <a href=\"https://wikipedia.org/wiki/Girolamo_Abos\" title=\"Girolamo Abos\"><PERSON><PERSON><PERSON></a>, Maltese-Italian composer and educator (d. 1760)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Girolamo_Abos\" title=\"Girolamo Abos\"><PERSON><PERSON><PERSON></a>, Maltese-Italian composer and educator (d. 1760)", "links": [{"title": "Girolamo <PERSON>", "link": "https://wikipedia.org/wiki/Girolamo_Abos"}]}, {"year": "1717", "text": "<PERSON>, French mathematician, physicist, and philosopher (d. 1793)", "html": "1717 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_d%27A<PERSON><PERSON>\" title=\"<PERSON>\"><PERSON></a>, French mathematician, physicist, and philosopher (d. 1793)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_d%27<PERSON><PERSON><PERSON>\" title=\"<PERSON>\"><PERSON></a>, French mathematician, physicist, and philosopher (d. 1793)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_d%27A<PERSON><PERSON>"}]}, {"year": "1720", "text": "<PERSON>, French-Italian composer (d. 1788)", "html": "1720 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, French-Italian composer (d. 1788)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, French-Italian composer (d. 1788)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1750", "text": "<PERSON>, 1st Baron <PERSON>, English lawyer, judge, and politician (d. 1818)", "html": "1750 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_1st_Baron_<PERSON>\" title=\"<PERSON>, 1st Baron <PERSON>\"><PERSON>, 1st Baron <PERSON></a>, English lawyer, judge, and politician (d. 1818)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_1st_Baron_<PERSON>\" title=\"<PERSON>, 1st Baron <PERSON>\"><PERSON>, 1st Baron <PERSON></a>, English lawyer, judge, and politician (d. 1818)", "links": [{"title": "<PERSON>, 1st Baron <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_1st_Baron_<PERSON>"}]}, {"year": "1753", "text": "<PERSON>, Irish-American surgeon and politician (d. 1816)", "html": "1753 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Irish-American surgeon and politician (d. 1816)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Irish-American surgeon and politician (d. 1816)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1758", "text": "<PERSON>, Danish philologist and author (d. 1841)", "html": "1758 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Danish philologist and author (d. 1841)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Danish philologist and author (d. 1841)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1774", "text": "<PERSON>, German-Russian Minister of Finance (d. 1845)", "html": "1774 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, German-Russian Minister of Finance (d. 1845)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, German-Russian Minister of Finance (d. 1845)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1793", "text": "<PERSON>, Irish painter of the Romantic era (d. 1861)", "html": "1793 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Irish painter of the Romantic era (d. 1861)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Irish painter of the Romantic era (d. 1861)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1806", "text": "<PERSON>, American author and educator (d. 1887)", "html": "1806 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author and educator (d. 1887)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author and educator (d. 1887)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1807", "text": "<PERSON><PERSON><PERSON>, Icelandic poet, author and naturalist (d. 1845)", "html": "1807 - <a href=\"https://wikipedia.org/wiki/J%C3%B3nas_Hallgr%C3%<PERSON><PERSON>son\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Icelandic poet, author and naturalist (d. 1845)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/J%C3%B3nas_Hallgr%C3%<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Icelandic poet, author and naturalist (d. 1845)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/J%C3%B3nas_Hallgr%C3%AD<PERSON>son"}]}, {"year": "1811", "text": "<PERSON>, English academic and politician (d. 1889)", "html": "1811 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English academic and politician (d. 1889)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English academic and politician (d. 1889)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1836", "text": "<PERSON><PERSON><PERSON><PERSON><PERSON> of Hawaii (d. 1891)", "html": "1836 - <a href=\"https://wikipedia.org/wiki/Kal%C4%81kaua\" title=\"Kalākaua\">Kalākaua</a> of Hawaii (d. 1891)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Kal%C4%81kaua\" title=\"Kalākaua\">Kalākaua</a> of Hawaii (d. 1891)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Kal%C4%81kaua"}]}, {"year": "1839", "text": "<PERSON><PERSON><PERSON><PERSON>, Canadian poet, author, and politician (d. 1908)", "html": "1839 - <a href=\"https://wikipedia.org/wiki/<PERSON>-<PERSON>%C3%A9_Fr%C3%A9chette\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Canadian poet, author, and politician (d. 1908)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>-<PERSON>%C3%A9_Fr%C3%A9chette\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Canadian poet, author, and politician (d. 1908)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Louis-Honor%C3%A9_Fr%C3%A9chette"}]}, {"year": "1841", "text": "<PERSON>, French physicist and academic (d. 1923)", "html": "1841 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French physicist and academic (d. 1923)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French physicist and academic (d. 1923)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1847", "text": "<PERSON>, Canadian lawyer and politician (d. 1927)", "html": "1847 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian lawyer and politician (d. 1927)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian lawyer and politician (d. 1927)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1851", "text": "<PERSON><PERSON>, American-Swiss soprano and actress (d. 1929)", "html": "1851 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American-Swiss soprano and actress (d. 1929)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American-Swiss soprano and actress (d. 1929)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>"}]}, {"year": "1856", "text": "<PERSON><PERSON><PERSON>, German architect (d. 1928)", "html": "1856 - <a href=\"https://wikipedia.org/wiki/J%C3%BCrgen_Kr%C3%B6ger\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, German architect (d. 1928)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/J%C3%BCrgen_Kr%C3%B6ger\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, German architect (d. 1928)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/J%C3%BCrgen_Kr%C3%B6ger"}]}, {"year": "1861", "text": "<PERSON>, Italian politician and journalist (d. 1930)", "html": "1861 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian politician and journalist (d. 1930)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian politician and journalist (d. 1930)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1861", "text": "<PERSON><PERSON><PERSON><PERSON><PERSON>, Venezuelan nun (d. 1925)", "html": "1861 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>-<PERSON>\" title=\"<PERSON><PERSON>-<PERSON>\"><PERSON><PERSON></a>, Venezuelan nun (d. 1925)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>-<PERSON>\" title=\"<PERSON><PERSON>-<PERSON>\"><PERSON><PERSON></a>, Venezuelan nun (d. 1925)", "links": [{"title": "<PERSON><PERSON>-Cordero", "link": "https://wikipedia.org/wiki/Georgina_Febres-Cordero"}]}, {"year": "1862", "text": "<PERSON>, Australian cricketer (d. 1944)", "html": "1862 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(Australian_cricketer)\" title=\"<PERSON> (Australian cricketer)\"><PERSON></a>, Australian cricketer (d. 1944)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(Australian_cricketer)\" title=\"<PERSON> (Australian cricketer)\"><PERSON></a>, Australian cricketer (d. 1944)", "links": [{"title": "<PERSON> (Australian cricketer)", "link": "https://wikipedia.org/wiki/<PERSON>(Australian_cricketer)"}]}, {"year": "1873", "text": "<PERSON><PERSON> <PERSON><PERSON>, American trumpet player and composer (d. 1958)", "html": "1873 - <a href=\"https://wikipedia.org/wiki/W._<PERSON>._Handy\" title=\"<PERSON><PERSON> <PERSON><PERSON> Handy\"><PERSON><PERSON> <PERSON><PERSON></a>, American trumpet player and composer (d. 1958)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/W._C._Handy\" title=\"W. C. Handy\"><PERSON><PERSON> <PERSON><PERSON></a>, American trumpet player and composer (d. 1958)", "links": [{"title": "<PERSON><PERSON> <PERSON><PERSON>", "link": "https://wikipedia.org/wiki/W._C._Handy"}]}, {"year": "1874", "text": "<PERSON>, Russian admiral and explorer (d. 1920)", "html": "1874 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian admiral and explorer (d. 1920)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian admiral and explorer (d. 1920)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1883", "text": "<PERSON>, American runner and coach (d. 1972)", "html": "1883 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American runner and coach (d. 1972)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American runner and coach (d. 1972)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1888", "text": "<PERSON>, Uruguayan pianist and composer (d. 1957)", "html": "1888 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Uruguayan pianist and composer (d. 1957)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Uruguayan pianist and composer (d. 1957)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1889", "text": "<PERSON>, American director, producer, and playwright (d. 1961)", "html": "1889 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American director, producer, and playwright (d. 1961)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American director, producer, and playwright (d. 1961)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1889", "text": "<PERSON>, German general (d. 1944)", "html": "1889 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%9F\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, German general (d. 1944)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%9F\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, German general (d. 1944)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Dietrich_Krai%C3%9F"}]}, {"year": "1890", "text": "<PERSON><PERSON><PERSON>, 6th President of the Philippines (d. 1956)", "html": "1890 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, 6th President of the Philippines (d. 1956)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, 6th President of the Philippines (d. 1956)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1892", "text": "<PERSON>, Chinese historian, author, and poet (d. 1978)", "html": "1892 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Chinese historian, author, and poet (d. 1978)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Chinese historian, author, and poet (d. 1978)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1892", "text": "<PERSON><PERSON>, Italian race car driver and motorcycle racer (d. 1953)", "html": "1892 - <a href=\"https://wikipedia.org/wiki/Tazio_Nuvolari\" title=\"Tazio Nuvolari\"><PERSON><PERSON></a>, Italian race car driver and motorcycle racer (d. 1953)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Tazio_Nuvolari\" title=\"Tazio Nuvolari\"><PERSON><PERSON></a>, Italian race car driver and motorcycle racer (d. 1953)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Tazio_Nuvolari"}]}, {"year": "1894", "text": "<PERSON>, American golfer (d. 1975)", "html": "1894 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American golfer (d. 1975)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American golfer (d. 1975)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1894", "text": "<PERSON><PERSON><PERSON><PERSON>, Austrian philosopher and politician (d. 1972)", "html": "1894 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>-<PERSON>\" title=\"<PERSON>Kalergi\"><PERSON></a>, Austrian philosopher and politician (d. 1972)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>-<PERSON>\" title=\"<PERSON>Ka<PERSON>\"><PERSON></a>, Austrian philosopher and politician (d. 1972)", "links": [{"title": "<PERSON>Ka<PERSON>gi", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>-<PERSON>"}]}, {"year": "1895", "text": "<PERSON>, German composer, violist and conductor (d. 1963)", "html": "1895 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German composer, violist and conductor (d. 1963)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German composer, violist and conductor (d. 1963)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1896", "text": "<PERSON>, Australian author and critic (d. 1984)", "html": "1896 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian author and critic (d. 1984)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian author and critic (d. 1984)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1896", "text": "<PERSON>, English fascist leader and politician (d. 1980)", "html": "1896 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English fascist leader and politician (d. 1980)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English fascist leader and politician (d. 1980)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1896", "text": "<PERSON>, American actor and singer (d. 1960)", "html": "1896 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor and singer (d. 1960)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor and singer (d. 1960)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1897", "text": "<PERSON><PERSON><PERSON><PERSON>, Indian-Pakistani academic (d. 1951)", "html": "1897 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON><PERSON></a>, Indian-Pakistani academic (d. 1951)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON><PERSON></a>, Indian-Pakistani academic (d. 1951)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1899", "text": "<PERSON>, American radio host (d. 1976)", "html": "1899 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American radio host (d. 1976)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American radio host (d. 1976)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1900", "text": "<PERSON><PERSON><PERSON>, Czechoslovakian race car driver (d. 1994)", "html": "1900 - <a href=\"https://wikipedia.org/wiki/Eli%C5%A1ka_Junkov%C3%A1\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Czechoslovakian race car driver (d. 1994)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Eli%C5%A1ka_Junkov%C3%A1\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Czechoslovakian race car driver (d. 1994)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Eli%C5%A1ka_<PERSON>kov%C3%A1"}]}, {"year": "1904", "text": "<PERSON><PERSON><PERSON>, Nigerian statesman, 1st President of Nigeria (d. 1996)", "html": "1904 - <a href=\"https://wikipedia.org/wiki/Nnamdi_Azikiwe\" title=\"Nnamdi Azikiwe\"><PERSON><PERSON><PERSON></a>, Nigerian statesman, 1st <a href=\"https://wikipedia.org/wiki/President_of_Nigeria\" title=\"President of Nigeria\">President of Nigeria</a> (d. 1996)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Nnamdi_Azikiwe\" title=\"Nnamdi Azikiwe\"><PERSON><PERSON><PERSON></a>, Nigerian statesman, 1st <a href=\"https://wikipedia.org/wiki/President_of_Nigeria\" title=\"President of Nigeria\">President of Nigeria</a> (d. 1996)", "links": [{"title": "N<PERSON>di <PERSON>", "link": "https://wikipedia.org/wiki/Nnamdi_Azikiwe"}, {"title": "President of Nigeria", "link": "https://wikipedia.org/wiki/President_of_Nigeria"}]}, {"year": "1905", "text": "<PERSON>, American guitarist and banjo player (d. 1973)", "html": "1905 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American guitarist and banjo player (d. 1973)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American guitarist and banjo player (d. 1973)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1907", "text": "<PERSON>, American actor, singer, director, producer, and screenwriter (d. 1997)", "html": "1907 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor, singer, director, producer, and screenwriter (d. 1997)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor, singer, director, producer, and screenwriter (d. 1997)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1909", "text": "<PERSON>, Indian-Pakistani religious leader (d. 1982)", "html": "1909 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Indian-Pakistani religious leader (d. 1982)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Indian-Pakistani religious leader (d. 1982)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "1912", "text": "<PERSON>, American actor and director (d. 1997)", "html": "1912 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, American actor and director (d. 1997)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, American actor and director (d. 1997)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "1912", "text": "<PERSON><PERSON> <PERSON><PERSON> <PERSON><PERSON>, Canadian actor, playwright, and author (d. 1995)", "html": "1912 - <a href=\"https://wikipedia.org/wiki/W._<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"W. E<PERSON> <PERSON><PERSON>\"><PERSON><PERSON> <PERSON><PERSON> <PERSON><PERSON></a>, Canadian actor, playwright, and author (d. 1995)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/W._<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"W. E. D<PERSON> Ross\"><PERSON><PERSON> <PERSON><PERSON> <PERSON><PERSON></a>, Canadian actor, playwright, and author (d. 1995)", "links": [{"title": "<PERSON><PERSON> <PERSON><PERSON> <PERSON><PERSON>", "link": "https://wikipedia.org/wiki/W._<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "1913", "text": "<PERSON>, American actress (d. 2015)", "html": "1913 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress (d. 2015)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress (d. 2015)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1914", "text": "<PERSON>, English spy (d. 1997)", "html": "1914 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English spy (d. 1997)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English spy (d. 1997)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1915", "text": "<PERSON>, Chinese-American author (d. 2017)", "html": "1915 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Chinese-American author (d. 2017)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Chinese-American author (d. 2017)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1916", "text": "<PERSON>, New Zealand actor and director (d. 1996)", "html": "1916 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, New Zealand actor and director (d. 1996)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, New Zealand actor and director (d. 1996)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1916", "text": "<PERSON><PERSON>, American voice actor and singer (d. 1988)", "html": "1916 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American voice actor and singer (d. 1988)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American voice actor and singer (d. 1988)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1916", "text": "<PERSON>, Canadian-American bassist (d. 1983)", "html": "1916 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(musician)\" title=\"<PERSON> (musician)\"><PERSON></a>, Canadian-American bassist (d. 1983)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(musician)\" title=\"<PERSON> (musician)\"><PERSON></a>, Canadian-American bassist (d. 1983)", "links": [{"title": "<PERSON> (musician)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(musician)"}]}, {"year": "1922", "text": "<PERSON>, American computer scientist, physicist, and engineer (d. 2015)", "html": "1922 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American computer scientist, physicist, and engineer (d. 2015)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American computer scientist, physicist, and engineer (d. 2015)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1922", "text": "<PERSON>, Portuguese novelist and Nobel laureate in Literature (d. 2010)", "html": "1922 - <a href=\"https://wikipedia.org/wiki/Jos%C3%A9_<PERSON>go\" title=\"<PERSON>\"><PERSON></a>, Portuguese novelist and Nobel laureate in Literature (d. 2010)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Jos%C3%A9_<PERSON>go\" title=\"<PERSON>\"><PERSON></a>, Portuguese novelist and Nobel laureate in Literature (d. 2010)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Jos%C3%A9_Saramago"}]}, {"year": "1924", "text": "<PERSON>, American businessman (d. 2013)", "html": "1924 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American businessman (d. 2013)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American businessman (d. 2013)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1924", "text": "<PERSON>, American sprinter and coach (d. 2014)", "html": "1924 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American sprinter and coach (d. 2014)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American sprinter and coach (d. 2014)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1927", "text": "<PERSON><PERSON>, American pianist and composer (d. 1983)", "html": "1927 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_Coke<PERSON>\" title=\"<PERSON><PERSON> Coker\"><PERSON><PERSON></a>, American pianist and composer (d. 1983)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_Coke<PERSON>\" title=\"<PERSON><PERSON> Coker\"><PERSON><PERSON></a>, American pianist and composer (d. 1983)", "links": [{"title": "<PERSON><PERSON>r", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_Coker"}]}, {"year": "1928", "text": "<PERSON><PERSON>, American actor and director (d. 2022)", "html": "1928 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_Gulager\" title=\"<PERSON><PERSON> Gulager\"><PERSON><PERSON></a>, American actor and director (d. 2022)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_Gulager\" title=\"<PERSON><PERSON> Gulager\"><PERSON><PERSON></a>, American actor and director (d. 2022)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_Gulager"}]}, {"year": "1929", "text": "<PERSON>, English businessman (d. 2018)", "html": "1929 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English businessman (d. 2018)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English businessman (d. 2018)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1930", "text": "<PERSON><PERSON>, Nigerian novelist, poet, and critic (d. 2013)", "html": "1930 - <a href=\"https://wikipedia.org/wiki/Chinua_Achebe\" title=\"Chinua Achebe\"><PERSON><PERSON></a>, Nigerian novelist, poet, and critic (d. 2013)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Chinua_Achebe\" title=\"Chinua Achebe\"><PERSON><PERSON></a>, Nigerian novelist, poet, and critic (d. 2013)", "links": [{"title": "Chinua Achebe", "link": "https://wikipedia.org/wiki/Chinua_Achebe"}]}, {"year": "1930", "text": "<PERSON>, American baseball player (d. 2021)", "html": "1930 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player (d. 2021)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player (d. 2021)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1930", "text": "<PERSON>, Italian mob boss (d. 2017)", "html": "1930 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> R<PERSON>\"><PERSON></a>, Italian mob boss (d. 2017)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Salvatore Riina\"><PERSON></a>, Italian mob boss (d. 2017)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Salvatore_<PERSON>na"}]}, {"year": "1931", "text": "<PERSON>, Italian author and illustrator (d. 2006)", "html": "1931 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian author and illustrator (d. 2006)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian author and illustrator (d. 2006)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1931", "text": "<PERSON>, American singer and guitarist (d. 2011)", "html": "1931 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer and guitarist (d. 2011)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer and guitarist (d. 2011)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1933", "text": "<PERSON><PERSON><PERSON>, American R&B singer", "html": "1933 - <a href=\"https://wikipedia.org/wiki/Garnet_Mimms\" title=\"Garnet Mimms\">Gar<PERSON>mm<PERSON></a>, American R&amp;B singer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Garnet_Mimms\" title=\"Garnet Mimms\">Gar<PERSON>mm<PERSON></a>, American R&amp;B singer", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Garnet_<PERSON><PERSON>s"}]}, {"year": "1935", "text": "<PERSON>, American journalist and author", "html": "1935 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American journalist and author", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American journalist and author", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1935", "text": "<PERSON>, Iraqi-Lebanese cleric, educator, and author (d. 2010)", "html": "1935 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Iraqi-Lebanese cleric, educator, and author (d. 2010)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Iraqi-Lebanese cleric, educator, and author (d. 2010)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1935", "text": "<PERSON><PERSON><PERSON>, Egyptian-English surgeon and academic", "html": "1935 - <a href=\"https://wikipedia.org/wiki/Mag<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Egyptian-English surgeon and academic", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Mag<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Egyptian-English surgeon and academic", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Mag<PERSON>_<PERSON>"}]}, {"year": "1936", "text": "<PERSON>, Australian businessman and politician", "html": "1936 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(Australian_politician)\" title=\"<PERSON> (Australian politician)\"><PERSON></a>, Australian businessman and politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(Australian_politician)\" title=\"<PERSON> (Australian politician)\"><PERSON></a>, Australian businessman and politician", "links": [{"title": "<PERSON> (Australian politician)", "link": "https://wikipedia.org/wiki/<PERSON>_(Australian_politician)"}]}, {"year": "1937", "text": "<PERSON>, English economist and academic (d. 2023)", "html": "1937 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English economist and academic (d. 2023)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English economist and academic (d. 2023)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1938", "text": "<PERSON>, Moroccan filmmaker (d. 2011)", "html": "1938 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Moroccan filmmaker (d. 2011)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Moroccan filmmaker (d. 2011)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1938", "text": "<PERSON>, Taiwanese politician", "html": "1938 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>-hsiang\" title=\"<PERSON>sian<PERSON>\"><PERSON></a>, Taiwanese politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>-hsiang\" title=\"<PERSON>hsian<PERSON>\"><PERSON></a>, Taiwanese politician", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>-hsiang"}]}, {"year": "1938", "text": "<PERSON>, Canadian actor (d. 2020)", "html": "1938 - <a href=\"https://wikipedia.org/wiki/Walter_Learning\" title=\"Walter Learning\"><PERSON></a>, Canadian actor (d. 2020)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Walter_Learning\" title=\"Walter Learning\"><PERSON></a>, Canadian actor (d. 2020)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Walter_Learning"}]}, {"year": "1938", "text": "<PERSON>, American philosopher, author, and academic (d. 2002)", "html": "1938 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American philosopher, author, and academic (d. 2002)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American philosopher, author, and academic (d. 2002)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1938", "text": "<PERSON>, American singer-songwriter and guitarist", "html": "1938 - <a href=\"https://wikipedia.org/wiki/Troy_Seals\" title=\"Troy Seals\"><PERSON></a>, American singer-songwriter and guitarist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Troy_Seals\" title=\"Troy Seals\"><PERSON></a>, American singer-songwriter and guitarist", "links": [{"title": "Troy Seals", "link": "https://wikipedia.org/wiki/Troy_Seals"}]}, {"year": "1939", "text": "<PERSON>, English author and critic", "html": "1939 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(critic)\" title=\"<PERSON> (critic)\"><PERSON></a>, English author and critic", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(critic)\" title=\"<PERSON> (critic)\"><PERSON></a>, English author and critic", "links": [{"title": "<PERSON> (critic)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(critic)"}]}, {"year": "1940", "text": "<PERSON>, American actress, singer, and dancer", "html": "1940 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress, singer, and dancer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress, singer, and dancer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1941", "text": "<PERSON>, Italian guitarist, composer, and musicologist (d. 2022)", "html": "1941 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian guitarist, composer, and musicologist (d. 2022)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian guitarist, composer, and musicologist (d. 2022)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1941", "text": "<PERSON>, English race car driver (d. 2005)", "html": "1941 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English race car driver (d. 2005)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English race car driver (d. 2005)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1941", "text": "<PERSON>, American singer-songwriter and producer", "html": "1941 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and producer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1942", "text": "<PERSON>, Scottish jockey and sportscaster", "html": "1942 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish jockey and sportscaster", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish jockey and sportscaster", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1942", "text": "<PERSON>, English-Canadian actress", "html": "1942 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English-Canadian actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English-Canadian actress", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1944", "text": "<PERSON>, English psychologist and academic (d. 2022)", "html": "1944 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English psychologist and academic (d. 2022)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English psychologist and academic (d. 2022)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1945", "text": "<PERSON><PERSON>, American guitarist and songwriter (d. 2014)", "html": "1945 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American guitarist and songwriter (d. 2014)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American guitarist and songwriter (d. 2014)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1945", "text": "<PERSON>, American historian, author, and academic", "html": "1945 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Lynn Hunt\"><PERSON></a>, American historian, author, and academic", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Lynn Hunt\"><PERSON></a>, American historian, author, and academic", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1945", "text": "<PERSON>, American actor", "html": "1945 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>back"}]}, {"year": "1946", "text": "<PERSON>, Australian drummer and songwriter (d. 2023)", "html": "1946 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(musician)\" title=\"<PERSON> (musician)\"><PERSON></a>, Australian drummer and songwriter (d. 2023)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(musician)\" title=\"<PERSON> (musician)\"><PERSON></a>, Australian drummer and songwriter (d. 2023)", "links": [{"title": "<PERSON> (musician)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(musician)"}]}, {"year": "1946", "text": "<PERSON>, American botanist, philosopher, and author (d. 2000)", "html": "1946 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American botanist, philosopher, and author (d. 2000)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American botanist, philosopher, and author (d. 2000)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1946", "text": "<PERSON>, American writer", "html": "1946 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American writer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American writer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1946", "text": "<PERSON>, American writer", "html": "1946 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American writer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American writer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1946", "text": "<PERSON>, American basketball player and coach (d. 2018)", "html": "1946 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player and coach (d. 2018)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player and coach (d. 2018)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1947", "text": "<PERSON>, Cuban journalist and activist", "html": "1947 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_Hern%C3%A1ndez\" title=\"<PERSON>\"><PERSON></a>, Cuban journalist and activist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>n%C3%A1ndez\" title=\"<PERSON>\"><PERSON></a>, Cuban journalist and activist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>%C3%A1ndez"}]}, {"year": "1948", "text": "<PERSON><PERSON>, German footballer and manager (d. 2023)", "html": "1948 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, German footballer and manager (d. 2023)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, German footballer and manager (d. 2023)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1948", "text": "<PERSON>, American singer-songwriter and pianist", "html": "1948 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and pianist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and pianist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1948", "text": "<PERSON>, American-English playwright and critic", "html": "1948 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American-English playwright and critic", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American-English playwright and critic", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1948", "text": "<PERSON>, Australian actor", "html": "1948 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(Australian_actor)\" class=\"mw-redirect\" title=\"<PERSON> (Australian actor)\"><PERSON></a>, Australian actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(Australian_actor)\" class=\"mw-redirect\" title=\"<PERSON> (Australian actor)\"><PERSON></a>, Australian actor", "links": [{"title": "<PERSON> (Australian actor)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(Australian_actor)"}]}, {"year": "1950", "text": "<PERSON>, American football player (d. 2001)", "html": "1950 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player (d. 2001)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player (d. 2001)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1950", "text": "<PERSON>, Filipino farmer and politician", "html": "1950 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Filipino farmer and politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Filipino farmer and politician", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1951", "text": "<PERSON>, New Zealand rugby player", "html": "1951 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(rugby_union)\" title=\"<PERSON> (rugby union)\"><PERSON></a>, New Zealand rugby player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(rugby_union)\" title=\"<PERSON> (rugby union)\"><PERSON></a>, New Zealand rugby player", "links": [{"title": "<PERSON> (rugby union)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(rugby_union)"}]}, {"year": "1951", "text": "<PERSON>, American actor", "html": "1951 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1952", "text": "<PERSON><PERSON><PERSON>, Japanese video game designer", "html": "1952 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese video game designer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese video game designer", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1953", "text": "<PERSON><PERSON>, Welsh comedian, actor, and author", "html": "1953 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON> <PERSON></a>, Welsh comedian, actor, and author", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Welsh comedian, actor, and author", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1954", "text": "<PERSON>, American novelist and short story writer", "html": "1954 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American novelist and short story writer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American novelist and short story writer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1954", "text": "<PERSON>, Australian lawyer and politician", "html": "1954 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian lawyer and politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian lawyer and politician", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1955", "text": "<PERSON><PERSON><PERSON>, Argentine footballer, coach, and manager", "html": "1955 - <a href=\"https://wikipedia.org/wiki/H%C3%A9ctor_C%C3%BAper\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Argentine footballer, coach, and manager", "no_year_html": "<a href=\"https://wikipedia.org/wiki/H%C3%A9ctor_C%C3%BAper\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Argentine footballer, coach, and manager", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/H%C3%A9ctor_C%C3%BAper"}]}, {"year": "1955", "text": "<PERSON>, Canadian ice hockey player and coach", "html": "1955 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player and coach", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1955", "text": "<PERSON>, Ecuadoran businessman, 47th President of Ecuador", "html": "1955 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Ecuadoran businessman, 47th <a href=\"https://wikipedia.org/wiki/President_of_Ecuador\" title=\"President of Ecuador\">President of Ecuador</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Ecuadoran businessman, 47th <a href=\"https://wikipedia.org/wiki/President_of_Ecuador\" title=\"President of Ecuador\">President of Ecuador</a>", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "President of Ecuador", "link": "https://wikipedia.org/wiki/President_of_Ecuador"}]}, {"year": "1955", "text": "<PERSON>, Japanese actor", "html": "1955 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Japanese actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Japanese actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1955", "text": "<PERSON><PERSON><PERSON>, Venezuelan businessman", "html": "1955 - <a href=\"https://wikipedia.org/wiki/Esteban_Trapiello\" title=\"Esteban Trapiello\"><PERSON><PERSON><PERSON> Trap<PERSON>lo</a>, Venezuelan businessman", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Esteban_Trapiello\" title=\"Esteban Trapiello\"><PERSON><PERSON><PERSON></a>, Venezuelan businessman", "links": [{"title": "Este<PERSON>", "link": "https://wikipedia.org/wiki/Esteban_Trapiello"}]}, {"year": "1956", "text": "<PERSON>, American race car driver and businessman", "html": "1956 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American race car driver and businessman", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American race car driver and businessman", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1957", "text": "<PERSON>, French actor", "html": "1957 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1958", "text": "<PERSON><PERSON>, American actress", "html": "1958 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American actress", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Mar<PERSON>_<PERSON>"}]}, {"year": "1958", "text": "<PERSON>, Serbian author and academic", "html": "1958 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C4%87\" title=\"<PERSON>\"><PERSON></a>, Serbian author and academic", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C4%87\" title=\"<PERSON>\"><PERSON></a>, Serbian author and academic", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>pi%C4%87"}]}, {"year": "1959", "text": "<PERSON><PERSON>, English journalist", "html": "1959 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, English journalist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, English journalist", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1959", "text": "<PERSON>, American cardiologist and physician (d. 2014)", "html": "1959 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American cardiologist and physician (d. 2014)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American cardiologist and physician (d. 2014)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_Fesmire"}]}, {"year": "1961", "text": "<PERSON>, English boxer", "html": "1961 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English boxer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English boxer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1962", "text": "<PERSON><PERSON>, Canadian writer and artist (d. 2016)", "html": "1962 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Canadian writer and artist (d. 2016)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Canadian writer and artist (d. 2016)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1962", "text": "<PERSON><PERSON>, English bass player", "html": "1962 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_(musician)\" title=\"<PERSON><PERSON> (musician)\"><PERSON><PERSON></a>, English bass player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_(musician)\" title=\"<PERSON><PERSON> (musician)\"><PERSON><PERSON></a>, English bass player", "links": [{"title": "<PERSON><PERSON> (musician)", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_(musician)"}]}, {"year": "1963", "text": "<PERSON>, English drummer and producer", "html": "1963 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%BCelles\" title=\"<PERSON>\"><PERSON></a>, English drummer and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%BCelles\" title=\"<PERSON>\"><PERSON></a>, English drummer and producer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Steve_Arg%C3%BCelles"}]}, {"year": "1963", "text": "<PERSON>, Brazilian newscaster, publicist and journalist", "html": "1963 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(newscaster)\" title=\"<PERSON> (newscaster)\"><PERSON></a>, Brazilian newscaster, publicist and journalist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(newscaster)\" title=\"<PERSON> (newscaster)\"><PERSON></a>, Brazilian newscaster, publicist and journalist", "links": [{"title": "<PERSON> (newscaster)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(newscaster)"}]}, {"year": "1963", "text": "<PERSON><PERSON>, American tennis player", "html": "1963 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American tennis player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American tennis player", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1964", "text": "<PERSON><PERSON><PERSON>, <PERSON>, English businessman and politician", "html": "1964 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>,_Baron_<PERSON>\" title=\"<PERSON><PERSON><PERSON>, <PERSON>\"><PERSON><PERSON><PERSON>, Baron <PERSON></a>, English businessman and politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>,_Baron_<PERSON>\" title=\"<PERSON><PERSON><PERSON>, <PERSON>\"><PERSON><PERSON><PERSON>, Baron <PERSON></a>, English businessman and politician", "links": [{"title": "<PERSON><PERSON><PERSON>, <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>,_<PERSON>_<PERSON>"}]}, {"year": "1964", "text": "<PERSON><PERSON>, Italian-French actress, director, and screenwriter", "html": "1964 - <a href=\"https://wikipedia.org/wiki/Vale<PERSON>_<PERSON>runi_<PERSON>\" title=\"Valeria Bruni <PERSON>chi\"><PERSON><PERSON></a>, Italian-French actress, director, and screenwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Vale<PERSON>_<PERSON>runi_<PERSON>\" title=\"Valeria Bruni <PERSON>chi\"><PERSON><PERSON></a>, Italian-French actress, director, and screenwriter", "links": [{"title": "Vale<PERSON> B<PERSON>", "link": "https://wikipedia.org/wiki/Valeria_<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1964", "text": "<PERSON>, American baseball player", "html": "1964 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1964", "text": "<PERSON>, Canadian singer-songwriter and pianist", "html": "1964 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian singer-songwriter and pianist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian singer-songwriter and pianist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1964", "text": "<PERSON>, American actor", "html": "1964 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1964", "text": "<PERSON><PERSON>, American actress", "html": "1964 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American actress", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1965", "text": "<PERSON><PERSON>, Finnish footballer", "html": "1965 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Finnish footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Finnish footballer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>n"}]}, {"year": "1966", "text": "<PERSON>, American singer-songwriter, guitarist, and producer", "html": "1966 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Joey Cape\"><PERSON></a>, American singer-songwriter, guitarist, and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Joey Cape\"><PERSON></a>, American singer-songwriter, guitarist, and producer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1966", "text": "<PERSON>, American guitarist", "html": "1966 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American guitarist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American guitarist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1966", "text": "<PERSON>, German keyboard player", "html": "1966 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German keyboard player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German keyboard player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1966", "text": "<PERSON>, Canadian-American actor and producer", "html": "1966 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian-American actor and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian-American actor and producer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1966", "text": "<PERSON><PERSON>, English journalist, author, and explorer", "html": "1966 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, English journalist, author, and explorer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, English journalist, author, and explorer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1967", "text": "<PERSON>, American poet and academic (d. 2009)", "html": "1967 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American poet and academic (d. 2009)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American poet and academic (d. 2009)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1967", "text": "<PERSON>, American actress and director", "html": "1967 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress and director", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress and director", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1968", "text": "<PERSON>, American actress", "html": "1968 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1968", "text": "<PERSON><PERSON><PERSON>, Indian politician (d. 2014)", "html": "1968 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Indian politician (d. 2014)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Indian politician (d. 2014)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "1968", "text": "<PERSON>, American swimmer", "html": "1968 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American swimmer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American swimmer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1970", "text": "<PERSON>, Canadian-American guitarist and producer", "html": "1970 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Made<PERSON>\"><PERSON></a>, Canadian-American guitarist and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Mader\"><PERSON></a>, Canadian-American guitarist and producer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1970", "text": "<PERSON>, American actress", "html": "1970 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1971", "text": "<PERSON><PERSON>, German javelin thrower and shot putter", "html": "1971 - <a href=\"https://wikipedia.org/wiki/Tan<PERSON>_Dam<PERSON>e\" title=\"<PERSON><PERSON> Dam<PERSON>\"><PERSON><PERSON></a>, German javelin thrower and shot putter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Tan<PERSON>_Damaske\" title=\"<PERSON><PERSON> Damask<PERSON>\"><PERSON><PERSON></a>, German javelin thrower and shot putter", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Tanja_Damaske"}]}, {"year": "1971", "text": "<PERSON><PERSON><PERSON>, Moroccan footballer and manager", "html": "1971 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Moroccan footballer and manager", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Moroccan footballer and manager", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON><PERSON>"}]}, {"year": "1971", "text": "<PERSON><PERSON>, Estonian soprano and actress", "html": "1971 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Estonian soprano and actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Estonian soprano and actress", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1971", "text": "<PERSON>, Russian swimmer and coach", "html": "1971 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(swimmer)\" class=\"mw-redirect\" title=\"<PERSON> (swimmer)\"><PERSON></a>, Russian swimmer and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(swimmer)\" class=\"mw-redirect\" title=\"<PERSON> (swimmer)\"><PERSON></a>, Russian swimmer and coach", "links": [{"title": "<PERSON> (swimmer)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(swimmer)"}]}, {"year": "1971", "text": "<PERSON><PERSON><PERSON>, Pakistani cricketer and coach", "html": "1971 - <a href=\"https://wikipedia.org/wiki/Waq<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Pakistani cricketer and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Waq<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Pakistani cricketer and coach", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/W<PERSON><PERSON>_<PERSON>"}]}, {"year": "1972", "text": "<PERSON><PERSON>, American actress and singer", "html": "1972 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American actress and singer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American actress and singer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>"}]}, {"year": "1973", "text": "<PERSON>, English race car driver and manager", "html": "1973 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English race car driver and manager", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English race car driver and manager", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1973", "text": "<PERSON>, New Zealand-Scottish rugby player and sportscaster", "html": "1973 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, New Zealand-Scottish rugby player and sportscaster", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, New Zealand-Scottish rugby player and sportscaster", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1974", "text": "<PERSON>, American actress and singer", "html": "1974 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress and singer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress and singer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1974", "text": "<PERSON><PERSON><PERSON>, Italian ice dancer and coach", "html": "1974 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Italian ice dancer and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Italian ice dancer and coach", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1974", "text": "<PERSON>, English footballer and sportscaster", "html": "1974 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English footballer and sportscaster", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English footballer and sportscaster", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1975", "text": "<PERSON>, Dominican baseball player (d. 2021)", "html": "1975 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dominican baseball player (d. 2021)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dominican baseball player (d. 2021)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1975", "text": "<PERSON><PERSON>, Japanese actress, model, and singer", "html": "1975 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Japanese actress, model, and singer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Japanese actress, model, and singer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1976", "text": "<PERSON>, English singer-songwriter", "html": "1976 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English singer-songwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Black\"><PERSON></a>, English singer-songwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1976", "text": "<PERSON><PERSON>, Finnish footballer", "html": "1976 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Finnish footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Finnish footballer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>ja"}]}, {"year": "1976", "text": "<PERSON><PERSON><PERSON>, Dutch swimmer", "html": "1976 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>we<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Dutch swimmer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Dutch swimmer", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>g"}]}, {"year": "1977", "text": "<PERSON><PERSON><PERSON>, Ukrainian-American figure skater", "html": "1977 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Ukrainian-American figure skater", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Ukrainian-American figure skater", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1977", "text": "<PERSON><PERSON>, Australian singer-songwriter and actress", "html": "1977 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Australian singer-songwriter and actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Australian singer-songwriter and actress", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1977", "text": "<PERSON>, American actress and singer", "html": "1977 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress and singer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress and singer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1977", "text": "<PERSON><PERSON><PERSON>, Mexican actor and producer", "html": "1977 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Mexican actor and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Mexican actor and producer", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1978", "text": "<PERSON><PERSON>, American baseball player", "html": "1978 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>night\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American baseball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> Bouknight\"><PERSON><PERSON></a>, American baseball player", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1978", "text": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, Turkish runner", "html": "1978 - <a href=\"https://wikipedia.org/wiki/Mehtap_Do%C4%9Fan-S%C4%B1zmaz\" title=\"<PERSON><PERSON><PERSON>an-Sızmaz\"><PERSON><PERSON><PERSON>-<PERSON></a>, Turkish runner", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Mehtap_Do%C4%9Fan-S%C4%B1zmaz\" title=\"<PERSON>hta<PERSON> Doğan-Sızmaz\"><PERSON><PERSON><PERSON>-Sızmaz</a>, Turkish runner", "links": [{"title": "Mehtap <PERSON>ğan-Sızmaz", "link": "https://wikipedia.org/wiki/Mehtap_Do%C4%9Fan-S%C4%B1zmaz"}]}, {"year": "1978", "text": "<PERSON><PERSON><PERSON>, Japanese actor", "html": "1978 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese actor", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1978", "text": "<PERSON>, Scottish footballer and manager", "html": "1978 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish footballer and manager", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish footballer and manager", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1978", "text": "<PERSON>, Brazilian guitarist and drummer", "html": "1978 - <a href=\"https://wikipedia.org/wiki/Carolina_Parra\" title=\"Carolina Parra\"><PERSON></a>, Brazilian guitarist and drummer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Carolina_Parra\" title=\"Carolina Parra\"><PERSON></a>, Brazilian guitarist and drummer", "links": [{"title": "Carolina Parra", "link": "https://wikipedia.org/wiki/Carolina_Parra"}]}, {"year": "1979", "text": "<PERSON>, American surfer", "html": "1979 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(surfer)\" title=\"<PERSON> (surfer)\"><PERSON></a>, American surfer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(surfer)\" title=\"<PERSON> (surfer)\"><PERSON></a>, American surfer", "links": [{"title": "<PERSON> (surfer)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(surfer)"}]}, {"year": "1980", "text": "<PERSON><PERSON>, Italian footballer", "html": "1980 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Italian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Italian footballer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1980", "text": "<PERSON><PERSON>, American basketball player", "html": "1980 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American basketball player", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1980", "text": "<PERSON>, Italian skier", "html": "1980 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian skier", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian skier", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1980", "text": "<PERSON>, Canadian wrestler", "html": "1980 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian wrestler", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian wrestler", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1980", "text": "<PERSON>, Turkish footballer", "html": "1980 - <a href=\"https://wikipedia.org/wiki/<PERSON>_%C3%9C%C3%A7%C3%BCnc%C3%BC\" title=\"<PERSON>\"><PERSON></a>, Turkish footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_%C3%9C%C3%A7%C3%BCnc%C3%BC\" title=\"<PERSON>\"><PERSON></a>, Turkish footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Hasan_%C3%9C%C3%A7%C3%BCnc%C3%BC"}]}, {"year": "1981", "text": "<PERSON>, Puerto Rican baseball player", "html": "1981 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(baseball)\" title=\"<PERSON> (baseball)\"><PERSON></a>, Puerto Rican baseball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(baseball)\" title=\"<PERSON> (baseball)\"><PERSON></a>, Puerto Rican baseball player", "links": [{"title": "<PERSON> (baseball)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(baseball)"}]}, {"year": "1981", "text": "<PERSON>, Canadian singer-songwriter", "html": "1981 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian singer-songwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian singer-songwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1981", "text": "<PERSON><PERSON><PERSON>, American voice actress, singer, and director", "html": "1981 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"Cait<PERSON> Glass\"><PERSON><PERSON><PERSON></a>, American voice actress, singer, and director", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"Cait<PERSON> Glass\"><PERSON><PERSON><PERSON></a>, American voice actress, singer, and director", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_Glass"}]}, {"year": "1981", "text": "<PERSON>-<PERSON><PERSON><PERSON>, Australian singer-songwriter", "html": "1981 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian singer-songwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian singer-songwriter", "links": [{"title": "<PERSON>-<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1981", "text": "<PERSON><PERSON>, English-American football player", "html": "1981 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_Umenyiora\" title=\"<PERSON>si Umenyiora\"><PERSON><PERSON></a>, English-American football player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_Umenyiora\" title=\"<PERSON><PERSON> Umenyiora\"><PERSON><PERSON></a>, English-American football player", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>enyiora"}]}, {"year": "1982", "text": "<PERSON><PERSON>, Filipino-American boxer", "html": "1982 - <a href=\"https://wikipedia.org/wiki/Nonito_Don<PERSON>\" title=\"Nonito Donaire\"><PERSON><PERSON></a>, Filipino-American boxer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Non<PERSON>_<PERSON>\" title=\"Nonito Donaire\"><PERSON><PERSON></a>, Filipino-American boxer", "links": [{"title": "Nonito <PERSON>", "link": "https://wikipedia.org/wiki/Nonito_Donaire"}]}, {"year": "1982", "text": "<PERSON><PERSON>, South African rugby player", "html": "1982 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, South African rugby player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, South African rugby player", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1982", "text": "<PERSON>, French sprinter", "html": "1982 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French sprinter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French sprinter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1982", "text": "<PERSON><PERSON><PERSON>, American-Israeli basketball player", "html": "1982 - <a href=\"https://wikipedia.org/wiki/Amar%27e_Stoudem<PERSON>\" title=\"<PERSON>'e Stoudemire\"><PERSON><PERSON><PERSON></a>, American-Israeli basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Amar%27e_St<PERSON>em<PERSON>\" title=\"<PERSON>'e Stoudemire\"><PERSON><PERSON><PERSON></a>, American-Israeli basketball player", "links": [{"title": "Amar'<PERSON> Stoudemire", "link": "https://wikipedia.org/wiki/Amar%27e_<PERSON><PERSON><PERSON><PERSON>"}]}, {"year": "1983", "text": "<PERSON><PERSON>, American rapper", "html": "1983 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_A.D.\" title=\"Kool A.D.\"><PERSON><PERSON> A.D.</a>, American rapper", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_A.D.\" title=\"Kool A.D.\"><PERSON><PERSON>D.</a>, American rapper", "links": [{"title": "Kool A.D.", "link": "https://wikipedia.org/wiki/Kool_A.D."}]}, {"year": "1983", "text": "<PERSON><PERSON>, Finnish ice hockey player", "html": "1983 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Finnish ice hockey player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Finnish ice hockey player", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1983", "text": "<PERSON><PERSON><PERSON>, German swimmer", "html": "1983 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_Steffen\" title=\"<PERSON>rit<PERSON> Steffen\"><PERSON><PERSON><PERSON></a>, German swimmer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_Steffen\" title=\"<PERSON>rit<PERSON> Steffen\"><PERSON><PERSON><PERSON></a>, German swimmer", "links": [{"title": "<PERSON><PERSON><PERSON> Steffen", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_Steffen"}]}, {"year": "1984", "text": "<PERSON>, English model and actress", "html": "1984 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English model and actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English model and actress", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1984", "text": "<PERSON>, American actress", "html": "1984 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1984", "text": "<PERSON>, English footballer", "html": "1984 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(English_footballer)\" title=\"<PERSON> (English footballer)\"><PERSON></a>, English footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(English_footballer)\" title=\"<PERSON> (English footballer)\"><PERSON></a>, English footballer", "links": [{"title": "<PERSON> (English footballer)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(English_footballer)"}]}, {"year": "1984", "text": "<PERSON><PERSON><PERSON>, Mongolian sumo wrestler", "html": "1984 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Mongolian sumo wrestler", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Mongolian sumo wrestler", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON><PERSON>ro"}]}, {"year": "1985", "text": "<PERSON><PERSON><PERSON>, Indian actor", "html": "1985 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Indian actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Indian actor", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1985", "text": "<PERSON><PERSON>, Finnish politician, former Prime Minister of Finland", "html": "1985 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Finnish politician, former <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Finland\" title=\"Prime Minister of Finland\">Prime Minister of Finland</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Finnish politician, former <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Finland\" title=\"Prime Minister of Finland\">Prime Minister of Finland</a>", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_Marin"}, {"title": "Prime Minister of Finland", "link": "https://wikipedia.org/wiki/Prime_Minister_of_Finland"}]}, {"year": "1986", "text": "<PERSON>, Islamic terrorist, perpetrator of the Orlando nightclub shooting (d. 2016)", "html": "1986 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Islamic terrorist, perpetrator of the <a href=\"https://wikipedia.org/wiki/Orlando_nightclub_shooting\" class=\"mw-redirect\" title=\"Orlando nightclub shooting\">Orlando nightclub shooting</a> (d. 2016)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Islamic terrorist, perpetrator of the <a href=\"https://wikipedia.org/wiki/Orlando_nightclub_shooting\" class=\"mw-redirect\" title=\"Orlando nightclub shooting\">Orlando nightclub shooting</a> (d. 2016)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Orlando nightclub shooting", "link": "https://wikipedia.org/wiki/Orlando_nightclub_shooting"}]}, {"year": "1986", "text": "<PERSON><PERSON>, French rugby player", "html": "1986 - <a href=\"https://wikipedia.org/wiki/Maxime_M%C3%A9dard\" title=\"<PERSON><PERSON>dard\"><PERSON><PERSON></a>, French rugby player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Maxime_M%C3%A9dard\" title=\"<PERSON><PERSON>dard\"><PERSON><PERSON></a>, French rugby player", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Maxime_M%C3%A9dard"}]}, {"year": "1987", "text": "<PERSON><PERSON><PERSON>, Israeli footballer", "html": "1987 - <a href=\"https://wikipedia.org/wiki/Eitan_Tibi\" title=\"Eitan Tibi\"><PERSON><PERSON><PERSON></a>, Israeli footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Eitan_Tibi\" title=\"Eitan Tibi\"><PERSON><PERSON><PERSON></a>, Israeli footballer", "links": [{"title": "Eitan Tibi", "link": "https://wikipedia.org/wiki/Eitan_Tibi"}]}, {"year": "1987", "text": "<PERSON>, American baseball player", "html": "1987 - <a href=\"https://wikipedia.org/wiki/Jordan_Walden\" title=\"<PERSON>\"><PERSON></a>, American baseball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Jordan_Walden\" title=\"Jordan Walden\"><PERSON></a>, American baseball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Jordan_Walden"}]}, {"year": "1989", "text": "<PERSON><PERSON><PERSON><PERSON>, American rapper and producer", "html": "1989 - <a href=\"https://wikipedia.org/wiki/Iamsu!\" title=\"Iamsu!\"><PERSON><PERSON><PERSON>!</a>, American rapper and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Iamsu!\" title=\"Iamsu!\"><PERSON><PERSON><PERSON>!</a>, American rapper and producer", "links": [{"title": "I<PERSON>u!", "link": "https://wikipedia.org/wiki/Iamsu!"}]}, {"year": "1990", "text": "<PERSON><PERSON><PERSON>, Filipino actor", "html": "1990 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Filipino actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Filipino actor", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1990", "text": "<PERSON><PERSON><PERSON>, Hungarian football player", "html": "1990 - <a href=\"https://wikipedia.org/wiki/D%C3%A9nes_Di<PERSON>z\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Hungarian football player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/D%C3%A9nes_Di<PERSON>z\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Hungarian football player", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/D%C3%A9nes_Di<PERSON>z"}]}, {"year": "1991", "text": "<PERSON><PERSON><PERSON>, Serbian footballer", "html": "1991 - <a href=\"https://wikipedia.org/wiki/Nemanja_Gudelj\" title=\"Nemanja Gudelj\"><PERSON><PERSON><PERSON></a>, Serbian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Nemanja_Gudelj\" title=\"Nemanja Gudelj\"><PERSON><PERSON><PERSON></a>, Serbian footballer", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>emanja_<PERSON>j"}]}, {"year": "1991", "text": "<PERSON><PERSON>, Japanese actress and singer", "html": "1991 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Japanese actress and singer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Japanese actress and singer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1992", "text": "<PERSON>, Nigerian footballer", "html": "1992 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Nigerian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Nigerian footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1992", "text": "<PERSON>, Australian rugby league player", "html": "1992 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian rugby league player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian rugby league player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1992", "text": "<PERSON><PERSON>, Croatian footballer", "html": "1992 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>%C4%87\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Croatian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>%C4%87\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Croatian footballer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Marcel<PERSON>_Brozovi%C4%87"}]}, {"year": "1992", "text": "<PERSON>, American-Belarusian ice hockey player", "html": "1992 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American-Belarusian ice hockey player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American-Belarusian ice hockey player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1993", "text": "<PERSON><PERSON> <PERSON><PERSON>, American football player", "html": "1993 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> <PERSON><PERSON>\"><PERSON><PERSON> <PERSON><PERSON></a>, American football player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> <PERSON><PERSON>\"><PERSON><PERSON> <PERSON><PERSON></a>, American football player", "links": [{"title": "<PERSON><PERSON> <PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "1993", "text": "<PERSON>, American comedian and actor", "html": "1993 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American comedian and actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American comedian and actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1993", "text": "<PERSON><PERSON><PERSON>, Portuguese footballer", "html": "1993 - <a href=\"https://wikipedia.org/wiki/N%C3%A<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Portuguese footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/N%C3%A<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Portuguese footballer", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/N%C3%A9<PERSON>_Se<PERSON>o"}]}, {"year": "1993", "text": "<PERSON><PERSON>, American basketball player", "html": "1993 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> Valentine\"><PERSON><PERSON></a>, American basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> Valentine\"><PERSON><PERSON></a>, American basketball player", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1994", "text": "<PERSON>, American actor", "html": "1994 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1994", "text": "<PERSON><PERSON><PERSON>, Japanese footballer", "html": "1994 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese footballer", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1995", "text": "<PERSON>-<PERSON><PERSON><PERSON>, American actor and pianist", "html": "1995 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor and pianist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor and pianist", "links": [{"title": "<PERSON>-<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1995", "text": "<PERSON><PERSON><PERSON>, Cameroonian footballer", "html": "1995 - <a href=\"https://wikipedia.org/wiki/Andr%C3%A9-<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Cameroonian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Andr%C3%A9-<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Cameroonian footballer", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Andr%C3%A9-<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1996", "text": "<PERSON><PERSON><PERSON>, Senegalese footballer", "html": "1996 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_Dia\" title=\"Boulaye Dia\"><PERSON><PERSON><PERSON></a>, Senegalese footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_Dia\" title=\"Boulaye Dia\"><PERSON><PERSON><PERSON></a>, Senegalese footballer", "links": [{"title": "Boulaye Dia", "link": "https://wikipedia.org/wiki/<PERSON>ula<PERSON>_Dia"}]}, {"year": "1997", "text": "<PERSON>, Brazilian footballer", "html": "1997 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A3es\" title=\"<PERSON>\"><PERSON></a>, Brazilian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A3es\" title=\"<PERSON>\"><PERSON></a>, Brazilian footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_G<PERSON>%C3%A3es"}]}, {"year": "1999", "text": "<PERSON><PERSON>, South Sudanese-American basketball player", "html": "1999 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_Bol\" title=\"Bo<PERSON> Bol\"><PERSON><PERSON></a>, South Sudanese-American basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_Bol\" title=\"Bo<PERSON> Bol\"><PERSON><PERSON></a>, South Sudanese-American basketball player", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>l"}]}, {"year": "1999", "text": "<PERSON><PERSON>, Dutch footballer", "html": "1999 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Dutch footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Dutch footballer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>"}]}, {"year": "2000", "text": "<PERSON>, Australian basketball player", "html": "2000 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(basketball)\" title=\"<PERSON> (basketball)\"><PERSON></a>, Australian basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(basketball)\" title=\"<PERSON> (basketball)\"><PERSON></a>, Australian basketball player", "links": [{"title": "<PERSON> (basketball)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(basketball)"}]}, {"year": "2006", "text": "<PERSON>, American singer", "html": "2006 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}], "Deaths": [{"year": "897", "text": "<PERSON><PERSON>, Chinese warlord", "html": "897 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>hui\" title=\"Gu Yanhui\"><PERSON><PERSON></a>, Chinese <a href=\"https://wikipedia.org/wiki/Warlord\" title=\"Warlord\">warlord</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>hui\" title=\"Gu Yanhui\"><PERSON><PERSON></a>, Chinese <a href=\"https://wikipedia.org/wiki/Warlord\" title=\"Warlord\">warlord</a>", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}, {"title": "Warlord", "link": "https://wikipedia.org/wiki/Warlord"}]}, {"year": "987", "text": "<PERSON>, Chinese scholar-official", "html": "987 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Chinese <a href=\"https://wikipedia.org/wiki/Scholar-official\" title=\"Scholar-official\">scholar-official</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Chinese <a href=\"https://wikipedia.org/wiki/Scholar-official\" title=\"Scholar-official\">scholar-official</a>", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>n"}, {"title": "Scholar-official", "link": "https://wikipedia.org/wiki/Scholar-official"}]}, {"year": "1005", "text": "<PERSON><PERSON><PERSON> of Abingdon, Archbishop of Canterbury", "html": "1005 - <a href=\"https://wikipedia.org/wiki/%C3%86lf<PERSON>_of_Abingdon\" title=\"<PERSON><PERSON><PERSON> of Abingdon\"><PERSON><PERSON><PERSON> of Abingdon</a>, <a href=\"https://wikipedia.org/wiki/Archbishop_of_Canterbury\" title=\"Archbishop of Canterbury\">Archbishop of Canterbury</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/%C3%86lf<PERSON>_of_Abingdon\" title=\"<PERSON><PERSON><PERSON> of Abingdon\"><PERSON><PERSON><PERSON> of Abingdon</a>, <a href=\"https://wikipedia.org/wiki/Archbishop_of_Canterbury\" title=\"Archbishop of Canterbury\">Archbishop of Canterbury</a>", "links": [{"title": "<PERSON><PERSON><PERSON> of Abingdon", "link": "https://wikipedia.org/wiki/%C3%86lfric_of_Abingdon"}, {"title": "Archbishop of Canterbury", "link": "https://wikipedia.org/wiki/Archbishop_of_Canterbury"}]}, {"year": "1093", "text": "<PERSON> of Scotland (b. 1045)", "html": "1093 - <a href=\"https://wikipedia.org/wiki/<PERSON>_Margaret_of_Scotland\" title=\"<PERSON> of Scotland\"><PERSON> of Scotland</a> (b. 1045)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Scotland\" title=\"<PERSON> of Scotland\"><PERSON> of Scotland</a> (b. 1045)", "links": [{"title": "<PERSON> of Scotland", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_Scotland"}]}, {"year": "1131", "text": "<PERSON><PERSON><PERSON><PERSON> of Kiev, Rus princess and author of medical books", "html": "1131 - <a href=\"https://wikipedia.org/wiki/Dobrodeia_of_Kiev\" title=\"Dobrodeia of Kiev\"><PERSON><PERSON><PERSON><PERSON> of Kiev</a>, Rus princess and author of medical books", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Dobrodeia_of_Kiev\" title=\"Dobrodeia of Kiev\">Dobrodeia of Kiev</a>, Rus princess and author of medical books", "links": [{"title": "Dobrodeia of Kiev", "link": "https://wikipedia.org/wiki/Dobrodeia_of_Kiev"}]}, {"year": "1240", "text": "<PERSON>, English archbishop and saint (b. 1175)", "html": "1240 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, English archbishop and saint (b. 1175)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, English archbishop and saint (b. 1175)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1240", "text": "<PERSON>, Andalusian Arab philosopher (b. 1165)", "html": "1240 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Andalusian Arab philosopher (b. 1165)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Andalusian Arab philosopher (b. 1165)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1264", "text": "Emperor <PERSON><PERSON> of Song China (b. 1205)", "html": "1264 - <a href=\"https://wikipedia.org/wiki/Emperor_<PERSON><PERSON>\" title=\"Emperor <PERSON><PERSON>\">Emperor <PERSON><PERSON></a> of <a href=\"https://wikipedia.org/wiki/Song_dynasty\" title=\"Song dynasty\">Song China</a> (b. 1205)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Emperor_<PERSON><PERSON>\" title=\"Emperor <PERSON><PERSON>\">Emperor <PERSON><PERSON></a> of <a href=\"https://wikipedia.org/wiki/Song_dynasty\" title=\"Song dynasty\">Song China</a> (b. 1205)", "links": [{"title": "Emperor <PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>"}, {"title": "Song dynasty", "link": "https://wikipedia.org/wiki/Song_dynasty"}]}, {"year": "1272", "text": "<PERSON> of England (b. 1207)", "html": "1272 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_England\" title=\"<PERSON> III of England\"><PERSON> of England</a> (b. 1207)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_England\" title=\"<PERSON> III of England\"><PERSON> of England</a> (b. 1207)", "links": [{"title": "<PERSON> of England", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_England"}]}, {"year": "1322", "text": "<PERSON><PERSON><PERSON>, Sultan of Granada (b. 1287)", "html": "1322 - <a href=\"https://wikipedia.org/wiki/Nasr_of_Granada\" title=\"Nasr of Granada\"><PERSON><PERSON><PERSON></a>, Sultan of <a href=\"https://wikipedia.org/wiki/Emirate_of_Granada\" title=\"Emirate of Granada\">Granada</a> (b. 1287)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Nasr_of_Granada\" title=\"Nasr of Granada\"><PERSON><PERSON><PERSON></a>, Sultan of <a href=\"https://wikipedia.org/wiki/Emirate_of_Granada\" title=\"Emirate of Granada\">Granada</a> (b. 1287)", "links": [{"title": "Nasr of Granada", "link": "https://wikipedia.org/wiki/Nasr_of_Granada"}, {"title": "Emirate of Granada", "link": "https://wikipedia.org/wiki/Emirate_of_Granada"}]}, {"year": "1328", "text": "<PERSON>, Japanese shōgun (b. 1276)", "html": "1328 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Prince <PERSON>\">Prince <PERSON></a>, Japanese shōgun (b. 1276)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Prince <PERSON>\">Prince <PERSON></a>, Japanese shōgun (b. 1276)", "links": [{"title": "Prince <PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON><PERSON>"}]}, {"year": "1464", "text": "<PERSON>, Margrave of Brandenburg-Kulmbach (b. 1406)", "html": "1464 - <a href=\"https://wikipedia.org/wiki/<PERSON>,_Margrave_of_Brandenburg-Kulmbach\" title=\"<PERSON>, Margrave of Brandenburg-Kulmbach\"><PERSON>, Margrave of Brandenburg-Kulmbach</a> (b. 1406)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>,_<PERSON><PERSON>_of_Brandenburg-Kulmbach\" title=\"<PERSON>, Margrave of Brandenburg-Kulmbach\"><PERSON>, Margrave of Brandenburg-Kulmbach</a> (b. 1406)", "links": [{"title": "<PERSON>, Margrave of Brandenburg-Kulmbach", "link": "https://wikipedia.org/wiki/<PERSON>,_<PERSON><PERSON>_of_Brandenburg-Kulmbach"}]}, {"year": "1494", "text": "<PERSON><PERSON>, German noble (b. 1432)", "html": "1494 - <a href=\"https://wikipedia.org/wiki/Theda_Ukena\" title=\"Theda Ukena\"><PERSON><PERSON></a>, German noble (b. 1432)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Theda_Ukena\" title=\"Theda Ukena\"><PERSON><PERSON></a>, German noble (b. 1432)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Theda_Ukena"}]}, {"year": "1580", "text": "<PERSON> of Baden-Sponheim, German Noblewoman (b. 1507)", "html": "1580 - <a href=\"https://wikipedia.org/wiki/<PERSON>_of_Baden-Sponheim\" title=\"<PERSON> of Baden-Sponheim\"><PERSON> of Baden-Sponheim</a>, German Noblewoman (b. 1507)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_of_Baden-Sponheim\" title=\"<PERSON> of Baden-Sponheim\"><PERSON> of Baden-Sponheim</a>, German Noblewoman (b. 1507)", "links": [{"title": "Marie of Baden-Sponheim", "link": "https://wikipedia.org/wiki/Marie_of_Baden-Sponheim"}]}, {"year": "1601", "text": "<PERSON>, 6th Earl of Westmorland (b. 1542)", "html": "1601 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_6th_Earl_of_Westmorland\" title=\"<PERSON>, 6th Earl of Westmorland\"><PERSON>, 6th Earl of Westmorland</a> (b. 1542)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_6th_Earl_of_Westmorland\" title=\"<PERSON>, 6th Earl of Westmorland\"><PERSON>, 6th Earl of Westmorland</a> (b. 1542)", "links": [{"title": "<PERSON>, 6th Earl of Westmorland", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_6th_Earl_of_Westmorland"}]}, {"year": "1603", "text": "<PERSON>, French Catholic theologian and philosopher (b. 1541)", "html": "1603 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French Catholic theologian and philosopher (b. 1541)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French Catholic theologian and philosopher (b. 1541)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1613", "text": "<PERSON><PERSON><PERSON>, Italian author and educator (b. 1556)", "html": "1613 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Italian author and educator (b. 1556)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Italian author and educator (b. 1556)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1625", "text": "<PERSON><PERSON><PERSON><PERSON>, Italian painter (b. c. 1532)", "html": "1625 - <a href=\"https://wikipedia.org/wiki/Sofonisba_Anguissola\" title=\"Sofonisba Anguissola\"><PERSON>fonis<PERSON> Anguisso<PERSON></a>, Italian painter (b. c. 1532)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Sofonisba_Anguissola\" title=\"Sofonisba Anguissola\">Sofonis<PERSON> Anguissola</a>, Italian painter (b. c. 1532)", "links": [{"title": "Sofonisba Anguissola", "link": "https://wikipedia.org/wiki/Sofonisba_Ang<PERSON>ssola"}]}, {"year": "1628", "text": "<PERSON>, Italian organist and composer (b. 1555)", "html": "1628 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian organist and composer (b. 1555)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian organist and composer (b. 1555)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1688", "text": "<PERSON><PERSON>, Swedish-Estonian scholar and author (b. 1660)", "html": "1688 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Swedish-Estonian scholar and author (b. 1660)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Swedish-Estonian scholar and author (b. 1660)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1695", "text": "<PERSON>, French philosopher and author (b. 1625)", "html": "1695 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French philosopher and author (b. 1625)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French philosopher and author (b. 1625)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1724", "text": "<PERSON>, English criminal (b. 1702)", "html": "1724 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English criminal (b. 1702)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English criminal (b. 1702)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1745", "text": "<PERSON>, 2nd Duke of Ormonde, Irish general and politician, Lord Lieutenant of Ireland (b. 1665)", "html": "1745 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_2nd_Duke_of_Ormonde\" title=\"<PERSON>, 2nd Duke of Ormonde\"><PERSON>, 2nd Duke of Ormonde</a>, Irish general and politician, <a href=\"https://wikipedia.org/wiki/Lord_Lieutenant_of_Ireland\" title=\"Lord Lieutenant of Ireland\">Lord Lieutenant of Ireland</a> (b. 1665)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>,_2nd_Duke_of_Ormonde\" title=\"<PERSON>, 2nd Duke of Ormonde\"><PERSON>, 2nd Duke of Ormonde</a>, Irish general and politician, <a href=\"https://wikipedia.org/wiki/Lord_Lieutenant_of_Ireland\" title=\"Lord Lieutenant of Ireland\">Lord Lieutenant of Ireland</a> (b. 1665)", "links": [{"title": "<PERSON>, 2nd Duke of Ormonde", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_2nd_Duke_of_Ormonde"}, {"title": "Lord Lieutenant of Ireland", "link": "https://wikipedia.org/wiki/Lord_Lieutenant_of_Ireland"}]}, {"year": "1773", "text": "<PERSON>, English journalist and author (b. 1715)", "html": "1773 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(book_editor)\" title=\"<PERSON> (book editor)\"><PERSON></a>, English journalist and author (b. 1715)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(book_editor)\" title=\"<PERSON> (book editor)\"><PERSON></a>, English journalist and author (b. 1715)", "links": [{"title": "<PERSON> (book editor)", "link": "https://wikipedia.org/wiki/<PERSON>_(book_editor)"}]}, {"year": "1779", "text": "<PERSON><PERSON><PERSON>, Finnish botanist and explorer (b. 1716)", "html": "1779 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Finnish botanist and explorer (b. 1716)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Finnish botanist and explorer (b. 1716)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON><PERSON>"}]}, {"year": "1790", "text": "<PERSON> of <PERSON><PERSON>, American politician (b. 1723)", "html": "1790 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_St<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON> of St. Thomas <PERSON>\"><PERSON> of St. Thomas <PERSON></a>, American politician (b. 1723)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON> of St. Thomas <PERSON>ifer\"><PERSON> of St. Thomas <PERSON></a>, American politician (b. 1723)", "links": [{"title": "<PERSON> of <PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1797", "text": "<PERSON> of Prussia (b. 1744)", "html": "1797 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_of_Prussia\" title=\"<PERSON> of Prussia\"><PERSON> of Prussia</a> (b. 1744)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_of_Prussia\" title=\"<PERSON> of Prussia\"><PERSON> of Prussia</a> (b. 1744)", "links": [{"title": "<PERSON> of Prussia", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_of_Prussia"}]}, {"year": "1802", "text": "<PERSON>, French botanist and explorer (b. 1746)", "html": "1802 - <a href=\"https://wikipedia.org/wiki/Andr%C3%A9_Mi<PERSON><PERSON>\" title=\"<PERSON>\"><PERSON></a>, French botanist and explorer (b. 1746)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Andr%C3%A9_<PERSON><PERSON><PERSON>\" title=\"<PERSON>\"><PERSON></a>, French botanist and explorer (b. 1746)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Andr%C3%A9_<PERSON><PERSON><PERSON>"}]}, {"year": "1806", "text": "<PERSON>, American general, lawyer, and politician, founded Cleveland, Ohio (b. 1754)", "html": "1806 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American general, lawyer, and politician, founded <a href=\"https://wikipedia.org/wiki/Cleveland,_Ohio\" class=\"mw-redirect\" title=\"Cleveland, Ohio\">Cleveland, Ohio</a> (b. 1754)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American general, lawyer, and politician, founded <a href=\"https://wikipedia.org/wiki/Cleveland,_Ohio\" class=\"mw-redirect\" title=\"Cleveland, Ohio\">Cleveland, Ohio</a> (b. 1754)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>and"}, {"title": "Cleveland, Ohio", "link": "https://wikipedia.org/wiki/Cleveland,_Ohio"}]}, {"year": "1808", "text": "<PERSON>, Ottoman sultan (b. 1779)", "html": "1808 - <a href=\"https://wikipedia.org/wiki/<PERSON>_IV\" title=\"Mustafa IV\"><PERSON></a>, Ottoman sultan (b. 1779)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_IV\" title=\"Mustafa IV\"><PERSON></a>, Ottoman sultan (b. 1779)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_IV"}]}, {"year": "1836", "text": "<PERSON><PERSON>, South African-French mycologist and academic (b. 1761)", "html": "1836 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON> He<PERSON>\"><PERSON><PERSON></a>, South African-French mycologist and academic (b. 1761)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON> He<PERSON>\"><PERSON><PERSON></a>, South African-French mycologist and academic (b. 1761)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1878", "text": "Princess <PERSON> of Hesse and by Rhine (b. 1874)", "html": "1878 - <a href=\"https://wikipedia.org/wiki/Princess_<PERSON>_of_Hesse_and_by_Rhine_(1874%E2%80%931878)\" title=\"Princess <PERSON> of Hesse and by Rhine (1874-1878)\">Princess <PERSON> of Hesse and by Rhine</a> (b. 1874)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Princess_<PERSON>_of_Hesse_and_by_Rhine_(1874%E2%80%931878)\" title=\"Princess <PERSON> of Hesse and by Rhine (1874-1878)\">Princess <PERSON> of Hesse and by Rhine</a> (b. 1874)", "links": [{"title": "Princess <PERSON> of Hesse and by Rhine (1874-1878)", "link": "https://wikipedia.org/wiki/Princess_<PERSON>_of_Hesse_and_by_Rhine_(1874%E2%80%931878)"}]}, {"year": "1884", "text": "<PERSON><PERSON><PERSON><PERSON>, Czech-Austrian soldier and physician (b. 1835)", "html": "1884 - <a href=\"https://wikipedia.org/wiki/Franti%C5%A1ek_Chvostek\" title=\"František Chvostek\"><PERSON><PERSON><PERSON><PERSON></a>, Czech-Austrian soldier and physician (b. 1835)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Franti%C5%A1ek_Chvostek\" title=\"František Chvostek\"><PERSON><PERSON><PERSON><PERSON></a>, Czech-Austrian soldier and physician (b. 1835)", "links": [{"title": "František Chvostek", "link": "https://wikipedia.org/wiki/Franti%C5%A1ek_Chvostek"}]}, {"year": "1885", "text": "<PERSON>, Canadian lawyer and politician (b. 1844)", "html": "1885 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian lawyer and politician (b. 1844)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian lawyer and politician (b. 1844)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Louis_<PERSON>"}]}, {"year": "1903", "text": "Princess <PERSON> of Hesse and by Rhine (b. 1895)", "html": "1903 - <a href=\"https://wikipedia.org/wiki/Princess_Elisabeth_of_Hesse_and_by_Rhine_(1895%E2%80%931903)\" title=\"Princess Elisabeth of Hesse and by Rhine (1895-1903)\">Princess <PERSON> of Hesse and by Rhine</a> (b. 1895)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Princess_Elisabeth_of_Hesse_and_by_Rhine_(1895%E2%80%931903)\" title=\"Princess Elisabeth of Hesse and by Rhine (1895-1903)\">Princess <PERSON> of Hesse and by Rhine</a> (b. 1895)", "links": [{"title": "Princess <PERSON> of Hesse and by Rhine (1895-1903)", "link": "https://wikipedia.org/wiki/Princess_Elisabeth_of_Hesse_and_by_Rhine_(1895%E2%80%931903)"}]}, {"year": "1907", "text": "<PERSON>, Duke of Parma (b. 1848)", "html": "1907 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Duke_of_Parma\" title=\"<PERSON>, Duke of Parma\"><PERSON>, Duke of Parma</a> (b. 1848)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Duke_of_Parma\" title=\"<PERSON>, Duke of Parma\"><PERSON>, Duke of Parma</a> (b. 1848)", "links": [{"title": "<PERSON>, Duke of Parma", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Duke_of_Parma"}]}, {"year": "1908", "text": "<PERSON><PERSON><PERSON><PERSON>, French-Canadian lawyer and politician, 4th Premier of Quebec (b. 1829)", "html": "1908 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>_<PERSON>bini%C3%A8re\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, French-Canadian lawyer and politician, 4th <a href=\"https://wikipedia.org/wiki/Premier_of_Quebec\" title=\"Premier of Quebec\">Premier of Quebec</a> (b. 1829)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>_<PERSON>bini%C3%A8re\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, French-Canadian lawyer and politician, 4th <a href=\"https://wikipedia.org/wiki/Premier_of_Quebec\" title=\"Premier of Quebec\">Premier of Quebec</a> (b. 1829)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON><PERSON>_<PERSON>_<PERSON>i%C3%A8re"}, {"title": "Premier of Quebec", "link": "https://wikipedia.org/wiki/Premier_of_Quebec"}]}, {"year": "1911", "text": "<PERSON><PERSON> <PERSON><PERSON>, American physician and politician, 9th Mayor of Minneapolis (b. 1842)", "html": "1911 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"A. A<PERSON> Ames\"><PERSON><PERSON> <PERSON><PERSON></a>, American physician and politician, 9th <a href=\"https://wikipedia.org/wiki/Mayor_of_Minneapolis\" class=\"mw-redirect\" title=\"Mayor of Minneapolis\">Mayor of Minneapolis</a> (b. 1842)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"A. A<PERSON> Ames\"><PERSON><PERSON> <PERSON><PERSON></a>, American physician and politician, 9th <a href=\"https://wikipedia.org/wiki/Mayor_of_Minneapolis\" class=\"mw-redirect\" title=\"Mayor of Minneapolis\">Mayor of Minneapolis</a> (b. 1842)", "links": [{"title": "<PERSON><PERSON> <PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>"}, {"title": "Mayor of Minneapolis", "link": "https://wikipedia.org/wiki/Mayor_of_Minneapolis"}]}, {"year": "1911", "text": "<PERSON>, American shot putter (b. 1879)", "html": "1911 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American shot putter (b. 1879)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American shot putter (b. 1879)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1913", "text": "<PERSON>, English businessman, founded Express County Milk Supply Company (b. 1836)", "html": "1913 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English businessman, founded <a href=\"https://wikipedia.org/wiki/Express_County_Milk_Supply_Company\" class=\"mw-redirect\" title=\"Express County Milk Supply Company\">Express County Milk Supply Company</a> (b. 1836)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English businessman, founded <a href=\"https://wikipedia.org/wiki/Express_County_Milk_Supply_Company\" class=\"mw-redirect\" title=\"Express County Milk Supply Company\">Express County Milk Supply Company</a> (b. 1836)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Express County Milk Supply Company", "link": "https://wikipedia.org/wiki/Express_County_Milk_Supply_Company"}]}, {"year": "1922", "text": "<PERSON>, Polish-German physicist and academic (b. 1875)", "html": "1922 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Polish-German physicist and academic (b. 1875)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Polish-German physicist and academic (b. 1875)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1939", "text": "<PERSON>, American lawyer and jurist (b. 1866)", "html": "1939 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(justice)\" class=\"mw-redirect\" title=\"<PERSON> (justice)\"><PERSON></a>, American lawyer and jurist (b. 1866)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(justice)\" class=\"mw-redirect\" title=\"<PERSON> (justice)\"><PERSON></a>, American lawyer and jurist (b. 1866)", "links": [{"title": "<PERSON> (justice)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(justice)"}]}, {"year": "1941", "text": "<PERSON>, Estonian footballer (b. 1902)", "html": "1941 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Estonian footballer (b. 1902)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Estonian footballer (b. 1902)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1941", "text": "<PERSON><PERSON>, Estonian organist, composer, and conductor (b. 1864)", "html": "1941 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_H%C3%A4rma\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Estonian organist, composer, and conductor (b. 1864)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_H%C3%A4rma\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Estonian organist, composer, and conductor (b. 1864)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Miina_H%C3%A4rma"}]}, {"year": "1947", "text": "<PERSON>, Italian businessman and politician, founded the Venice Film Festival (b. 1877)", "html": "1947 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian businessman and politician, founded the <a href=\"https://wikipedia.org/wiki/Venice_Film_Festival\" title=\"Venice Film Festival\">Venice Film Festival</a> (b. 1877)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian businessman and politician, founded the <a href=\"https://wikipedia.org/wiki/Venice_Film_Festival\" title=\"Venice Film Festival\">Venice Film Festival</a> (b. 1877)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Venice Film Festival", "link": "https://wikipedia.org/wiki/Venice_Film_Festival"}]}, {"year": "1950", "text": "<PERSON>, American physician and surgeon, co-founded Alcoholics Anonymous (b. 1879)", "html": "1950 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(doctor)\" title=\"<PERSON> (doctor)\"><PERSON></a>, American physician and surgeon, co-founded <a href=\"https://wikipedia.org/wiki/Alcoholics_Anonymous\" title=\"Alcoholics Anonymous\">Alcoholics Anonymous</a> (b. 1879)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>(doctor)\" title=\"<PERSON> (doctor)\"><PERSON></a>, American physician and surgeon, co-founded <a href=\"https://wikipedia.org/wiki/Alcoholics_Anonymous\" title=\"Alcoholics Anonymous\">Alcoholics Anonymous</a> (b. 1879)", "links": [{"title": "<PERSON> (doctor)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(doctor)"}, {"title": "Alcoholics Anonymous", "link": "https://wikipedia.org/wiki/Alcoholics_Anonymous"}]}, {"year": "1956", "text": "<PERSON><PERSON><PERSON>, Japanese sumo wrestler, the 24th <PERSON><PERSON><PERSON><PERSON> (b. 1887)", "html": "1956 - <a href=\"https://wikipedia.org/wiki/%C5%8Ctori_Tanigor%C5%8D\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese sumo wrestler, the 24th <a href=\"https://wikipedia.org/wiki/Ma<PERSON>uchi#Yokozuna\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a> (b. 1887)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/%C5%8Ctori_Tanigor%C5%8D\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese sumo wrestler, the 24th <a href=\"https://wikipedia.org/wiki/Ma<PERSON>uchi#Yo<PERSON>zuna\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a> (b. 1887)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/%C5%8Ctori_Tanigor%C5%8D"}, {"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Makuuchi#Yokozuna"}]}, {"year": "1960", "text": "<PERSON>, American actor (b. 1901)", "html": "1960 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> G<PERSON>\"><PERSON></a>, American actor (b. 1901)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Gable\"><PERSON></a>, American actor (b. 1901)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>able"}]}, {"year": "1961", "text": "<PERSON>, American lawyer and politician, 48th Speaker of the United States House of Representatives (b. 1882)", "html": "1961 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and politician, 48th <a href=\"https://wikipedia.org/wiki/Speaker_of_the_United_States_House_of_Representatives\" title=\"Speaker of the United States House of Representatives\">Speaker of the United States House of Representatives</a> (b. 1882)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and politician, 48th <a href=\"https://wikipedia.org/wiki/Speaker_of_the_United_States_House_of_Representatives\" title=\"Speaker of the United States House of Representatives\">Speaker of the United States House of Representatives</a> (b. 1882)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Speaker of the United States House of Representatives", "link": "https://wikipedia.org/wiki/Speaker_of_the_United_States_House_of_Representatives"}]}, {"year": "1964", "text": "<PERSON>, American botanist and author (b. 1898)", "html": "1964 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American botanist and author (b. 1898)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American botanist and author (b. 1898)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1971", "text": "<PERSON><PERSON>, American model and actress (b. 1943)", "html": "1971 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American model and actress (b. 1943)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American model and actress (b. 1943)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>wick"}]}, {"year": "1972", "text": "<PERSON>, Russian ballerina and actress (b. 1889)", "html": "1972 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian ballerina and actress (b. 1889)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian ballerina and actress (b. 1889)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1973", "text": "<PERSON>, English-American philosopher, author, and educator (b. 1915)", "html": "1973 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English-American philosopher, author, and educator (b. 1915)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English-American philosopher, author, and educator (b. 1915)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1974", "text": "<PERSON><PERSON>, German physicist and engineer (b. 1882)", "html": "1974 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, German physicist and engineer (b. 1882)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, German physicist and engineer (b. 1882)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1976", "text": "<PERSON>, English cricketer (b. 1905)", "html": "1976 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(cricketer)\" title=\"<PERSON> (cricketer)\"><PERSON></a>, English cricketer (b. 1905)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(cricketer)\" title=\"<PERSON> (cricketer)\"><PERSON></a>, English cricketer (b. 1905)", "links": [{"title": "<PERSON> (cricketer)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>(cricketer)"}]}, {"year": "1982", "text": "<PERSON>, Russian mathematician and academic (b. 1896)", "html": "1982 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian mathematician and academic (b. 1896)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian mathematician and academic (b. 1896)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1984", "text": "<PERSON>, American trombonist (b. 1906)", "html": "1984 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American trombonist (b. 1906)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American trombonist (b. 1906)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1986", "text": "<PERSON><PERSON><PERSON><PERSON>, Irish actress (b. 1923)", "html": "1986 - <a href=\"https://wikipedia.org/wiki/Siobh%C3%<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Irish actress (b. 1923)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Siobh%C3%<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Irish actress (b. 1923)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Siobh%C3%A1n_<PERSON>"}]}, {"year": "1986", "text": "<PERSON><PERSON><PERSON><PERSON>, Indian businessman (b. 1936)", "html": "1986 - <a href=\"https://wikipedia.org/wiki/Pandit<PERSON><PERSON>_<PERSON>\" title=\"Pan<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Indian businessman (b. 1936)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>dit<PERSON><PERSON>_<PERSON>\" title=\"Pan<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Indian businessman (b. 1936)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Panditra<PERSON>_<PERSON>"}]}, {"year": "1987", "text": "<PERSON>, American baseball player and coach (b. 1937)", "html": "1987 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(baseball)\" title=\"<PERSON> (baseball)\"><PERSON></a>, American baseball player and coach (b. 1937)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(baseball)\" title=\"<PERSON> (baseball)\"><PERSON></a>, American baseball player and coach (b. 1937)", "links": [{"title": "<PERSON> (baseball)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(baseball)"}]}, {"year": "1989", "text": "<PERSON><PERSON><PERSON>, Canadian lawyer and politician (b. 1938)", "html": "1989 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>%C3%A9part\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Canadian lawyer and politician (b. 1938)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>%C3%A9part\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Canadian lawyer and politician (b. 1938)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>%C3%A9part"}]}, {"year": "1990", "text": "<PERSON><PERSON>, Turkish politician (b. 1937)", "html": "1990 - <a href=\"https://wikipedia.org/wiki/Ege_Bagatur\" title=\"Ege Bagatur\"><PERSON><PERSON></a>, Turkish politician (b. 1937)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Ege_Bagatur\" title=\"Ege Bagatur\"><PERSON><PERSON></a>, Turkish politician (b. 1937)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Ege_Bagatur"}]}, {"year": "1993", "text": "<PERSON>, Slovak-German soprano (b. 1939)", "html": "1993 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Slovak-German soprano (b. 1939)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Slovak-German soprano (b. 1939)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1993", "text": "<PERSON><PERSON><PERSON>, Tunisia-born French clown (b. 1915)", "html": "1993 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Tunisia-born French clown (b. 1915)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Tunisia-born French clown (b. 1915)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/A<PERSON><PERSON>_<PERSON>"}]}, {"year": "1994", "text": "<PERSON><PERSON>, American singer-songwriter and guitarist (b. 1943)", "html": "1994 - <a href=\"https://wikipedia.org/wiki/<PERSON>et_<PERSON>\" title=\"Chet Powers\"><PERSON><PERSON></a>, American singer-songwriter and guitarist (b. 1943)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>et_<PERSON>\" title=\"Chet Powers\"><PERSON><PERSON></a>, American singer-songwriter and guitarist (b. 1943)", "links": [{"title": "Chet <PERSON>", "link": "https://wikipedia.org/wiki/Chet_<PERSON>"}]}, {"year": "1999", "text": "<PERSON>, American microbiologist and academic, Nobel Prize laureate (b. 1928)", "html": "1999 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American microbiologist and academic, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Physiology_or_Medicine\" title=\"Nobel Prize in Physiology or Medicine\">Nobel Prize</a> laureate (b. 1928)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American microbiologist and academic, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Physiology_or_Medicine\" title=\"Nobel Prize in Physiology or Medicine\">Nobel Prize</a> laureate (b. 1928)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Nobel Prize in Physiology or Medicine", "link": "https://wikipedia.org/wiki/Nobel_Prize_in_Physiology_or_Medicine"}]}, {"year": "2000", "text": "<PERSON>, American hip-hop artist (b. 1971)", "html": "2000 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, American hip-hop artist (b. 1971)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, American hip-hop artist (b. 1971)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "2000", "text": "<PERSON><PERSON>, Turkish-French singer-songwriter (b. 1957)", "html": "2000 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> Kay<PERSON>\"><PERSON><PERSON></a>, Turkish-French singer-songwriter (b. 1957)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Turkish-French singer-songwriter (b. 1957)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>a"}]}, {"year": "2001", "text": "<PERSON>, American pianist and composer (b. 1930)", "html": "2001 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(musician)\" title=\"<PERSON> (musician)\"><PERSON></a>, American pianist and composer (b. 1930)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(musician)\" title=\"<PERSON> (musician)\"><PERSON></a>, American pianist and composer (b. 1930)", "links": [{"title": "<PERSON> (musician)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(musician)"}]}, {"year": "2005", "text": "<PERSON>, American radio and television host and producer (b. 1913)", "html": "2005 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American radio and television host and producer (b. 1913)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American radio and television host and producer (b. 1913)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "2005", "text": "<PERSON>, Canadian-American chemist and academic, Nobel Prize laureate (b. 1915)", "html": "2005 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian-American chemist and academic, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Chemistry\" title=\"Nobel Prize in Chemistry\">Nobel Prize</a> laureate (b. 1915)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian-American chemist and academic, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Chemistry\" title=\"Nobel Prize in Chemistry\">Nobel Prize</a> laureate (b. 1915)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Nobel Prize in Chemistry", "link": "https://wikipedia.org/wiki/Nobel_Prize_in_Chemistry"}]}, {"year": "2005", "text": "<PERSON>, English activist, founded the Vegan Society (b. 1910)", "html": "2005 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English activist, founded the <a href=\"https://wikipedia.org/wiki/Vegan_Society\" class=\"mw-redirect\" title=\"Vegan Society\">Vegan Society</a> (b. 1910)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English activist, founded the <a href=\"https://wikipedia.org/wiki/Vegan_Society\" class=\"mw-redirect\" title=\"Vegan Society\">Vegan Society</a> (b. 1910)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}, {"title": "Vegan Society", "link": "https://wikipedia.org/wiki/Vegan_Society"}]}, {"year": "2006", "text": "<PERSON>, American economist and academic, Nobel Prize laureate (b. 1912)", "html": "2006 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American economist and academic, <a href=\"https://wikipedia.org/wiki/Nobel_Memorial_Prize_in_Economic_Sciences\" title=\"Nobel Memorial Prize in Economic Sciences\">Nobel Prize</a> laureate (b. 1912)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American economist and academic, <a href=\"https://wikipedia.org/wiki/Nobel_Memorial_Prize_in_Economic_Sciences\" title=\"Nobel Memorial Prize in Economic Sciences\">Nobel Prize</a> laureate (b. 1912)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Nobel Memorial Prize in Economic Sciences", "link": "https://wikipedia.org/wiki/Nobel_Memorial_Prize_in_Economic_Sciences"}]}, {"year": "2006", "text": "<PERSON>, Russian sociologist and political scientist (b. 1930)", "html": "2006 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian sociologist and political scientist (b. 1930)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian sociologist and political scientist (b. 1930)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2007", "text": "<PERSON>, American businessman (b. 1914)", "html": "2007 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American businessman (b. 1914)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American businessman (b. 1914)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2007", "text": "<PERSON><PERSON><PERSON>, Norwegian actress and singer (b. 1947)", "html": "2007 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Norwegian actress and singer (b. 1947)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Norwegian actress and singer (b. 1947)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "2007", "text": "<PERSON><PERSON><PERSON>, Norwegian actor and screenwriter (b. 1946)", "html": "2007 - <a href=\"https://wikipedia.org/wiki/Trond_<PERSON>ag\" title=\"Trond Kirk<PERSON>ag\"><PERSON><PERSON><PERSON></a>, Norwegian actor and screenwriter (b. 1946)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Trond_<PERSON>\" title=\"Trond <PERSON>\"><PERSON><PERSON><PERSON></a>, Norwegian actor and screenwriter (b. 1946)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Trond_<PERSON>ag"}]}, {"year": "2007", "text": "<PERSON>, English boxer, poet, and author (b. 1922)", "html": "2007 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English boxer, poet, and author (b. 1922)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English boxer, poet, and author (b. 1922)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2008", "text": "<PERSON>; Polish-Swiss art dealer (b. 1928)", "html": "2008 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>; Polish-Swiss art dealer (b. 1928)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>; Polish-Swiss art dealer (b. 1928)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2008", "text": "<PERSON>, English actor and screenwriter (b. 1916)", "html": "2008 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actor and screenwriter (b. 1916)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actor and screenwriter (b. 1916)", "links": [{"title": "Reg <PERSON>", "link": "https://wikipedia.org/wiki/Reg_Varney"}]}, {"year": "2009", "text": "<PERSON>, Mexican footballer (b. 1978)", "html": "2009 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Mexican footballer (b. 1978)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Mexican footballer (b. 1978)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "2009", "text": "<PERSON>, Ukrainian-Russian accountant and lawyer (b. 1972)", "html": "2009 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Ukrainian-Russian accountant and lawyer (b. 1972)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Ukrainian-Russian accountant and lawyer (b. 1972)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2009", "text": "<PERSON>, English actor (b. 1930)", "html": "2009 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actor (b. 1930)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actor (b. 1930)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2010", "text": "<PERSON><PERSON><PERSON>, American biologist and sailor (b. 1913)", "html": "2010 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_Chance\" title=\"Britton Chance\"><PERSON><PERSON><PERSON></a>, American biologist and sailor (b. 1913)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"Britton Chance\"><PERSON><PERSON><PERSON></a>, American biologist and sailor (b. 1913)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "2010", "text": "<PERSON><PERSON>, American publicist (b. 1946)", "html": "2010 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American publicist (b. 1946)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American publicist (b. 1946)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>n"}]}, {"year": "2010", "text": "<PERSON><PERSON><PERSON>, Filipino DJ and talent manager (b. 1952)", "html": "2010 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"Wyn<PERSON>\"><PERSON><PERSON><PERSON></a>, Filipino DJ and talent manager (b. 1952)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON>yn<PERSON>\"><PERSON><PERSON><PERSON></a>, Filipino DJ and talent manager (b. 1952)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Wyn<PERSON>_<PERSON>"}]}, {"year": "2012", "text": "<PERSON>, Australian evangelist and academic (b. 1930)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(evangelist)\" title=\"<PERSON> (evangelist)\"><PERSON></a>, Australian evangelist and academic (b. 1930)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(evangelist)\" title=\"<PERSON> (evangelist)\"><PERSON></a>, Australian evangelist and academic (b. 1930)", "links": [{"title": "<PERSON> (evangelist)", "link": "https://wikipedia.org/wiki/<PERSON>_(evangelist)"}]}, {"year": "2012", "text": "<PERSON><PERSON><PERSON>, Bangladeshi actor and director (b. 1930)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Bangladeshi actor and director (b. 1930)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Bangladeshi actor and director (b. 1930)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>has<PERSON>_<PERSON><PERSON>"}]}, {"year": "2012", "text": "<PERSON>, French mountaineer (b. 1960)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French mountaineer (b. 1960)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French mountaineer (b. 1960)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2012", "text": "<PERSON><PERSON>, Ghanaian engineer and politician, 3rd Vice President of Ghana (b. 1946)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Ghanaian engineer and politician, 3rd <a href=\"https://wikipedia.org/wiki/Vice_President_of_Ghana\" title=\"Vice President of Ghana\">Vice President of Ghana</a> (b. 1946)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Ghanaian engineer and politician, 3rd <a href=\"https://wikipedia.org/wiki/Vice_President_of_Ghana\" title=\"Vice President of Ghana\">Vice President of Ghana</a> (b. 1946)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>a"}, {"title": "Vice President of Ghana", "link": "https://wikipedia.org/wiki/Vice_President_of_Ghana"}]}, {"year": "2012", "text": "<PERSON><PERSON><PERSON>, Iraqi-Israeli lawyer, judge, and politician (b. 1920)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Iraqi-Israeli lawyer, judge, and politician (b. 1920)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Iraqi-Israeli lawyer, judge, and politician (b. 1920)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>wi"}]}, {"year": "2012", "text": "<PERSON>, New Zealand rugby player (b. 1921)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(rugby)\" title=\"<PERSON> (rugby)\"><PERSON></a>, New Zealand rugby player (b. 1921)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(rugby)\" title=\"<PERSON> (rugby)\"><PERSON></a>, New Zealand rugby player (b. 1921)", "links": [{"title": "<PERSON> (rugby)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>(rugby)"}]}, {"year": "2013", "text": "<PERSON>, American journalist (b. 1928)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(reporter)\" title=\"<PERSON> (reporter)\"><PERSON></a>, American journalist (b. 1928)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(reporter)\" title=\"<PERSON> (reporter)\"><PERSON></a>, American journalist (b. 1928)", "links": [{"title": "<PERSON> (reporter)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(reporter)"}]}, {"year": "2013", "text": "<PERSON>, American bowler (b. 1941)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American bowler (b. 1941)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American bowler (b. 1941)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "2013", "text": "<PERSON>, Canadian lieutenant and politician (b. 1925)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian lieutenant and politician (b. 1925)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian lieutenant and politician (b. 1925)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "2013", "text": "<PERSON><PERSON>, Indian-Pakistani diplomat, 19th Foreign Secretary of Pakistan (b. 1932)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Indian-Pakistani diplomat, 19th <a href=\"https://wikipedia.org/wiki/Foreign_Secretary_of_Pakistan\" class=\"mw-redirect\" title=\"Foreign Secretary of Pakistan\">Foreign Secretary of Pakistan</a> (b. 1932)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Indian-Pakistani diplomat, 19th <a href=\"https://wikipedia.org/wiki/Foreign_Secretary_of_Pakistan\" class=\"mw-redirect\" title=\"Foreign Secretary of Pakistan\">Foreign Secretary of Pakistan</a> (b. 1932)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>"}, {"title": "Foreign Secretary of Pakistan", "link": "https://wikipedia.org/wiki/Foreign_Secretary_of_Pakistan"}]}, {"year": "2013", "text": "<PERSON>, American mathematician and academic (b. 1940)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American mathematician and academic (b. 1940)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American mathematician and academic (b. 1940)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2013", "text": "<PERSON><PERSON>, Norwegian footballer and manager (b. 1931)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Norwegian footballer and manager (b. 1931)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Norwegian footballer and manager (b. 1931)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "2013", "text": "<PERSON>, Jr., American author, critic, and academic (b. 1923)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>,_Jr.\" class=\"mw-redirect\" title=\"<PERSON>, Jr.\"><PERSON>, Jr.</a>, American author, critic, and academic (b. 1923)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>,_Jr.\" class=\"mw-redirect\" title=\"<PERSON>, Jr.\"><PERSON>, Jr.</a>, American author, critic, and academic (b. 1923)", "links": [{"title": "<PERSON>, Jr.", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>,_Jr."}]}, {"year": "2013", "text": "<PERSON>, American painter, sculptor, and illustrator (b. 1924)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(artist)\" title=\"<PERSON> (artist)\"><PERSON></a>, American painter, sculptor, and illustrator (b. 1924)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(artist)\" title=\"<PERSON> (artist)\"><PERSON></a>, American painter, sculptor, and illustrator (b. 1924)", "links": [{"title": "<PERSON> (artist)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(artist)"}]}, {"year": "2014", "text": "<PERSON>, American historian, author, and critic (b. 1926)", "html": "2014 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American historian, author, and critic (b. 1926)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American historian, author, and critic (b. 1926)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2014", "text": "<PERSON><PERSON>, Serbian poet and playwright (b. 1931)", "html": "2014 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_%C4%86irilov\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Serbian poet and playwright (b. 1931)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_%C4%86irilov\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Serbian poet and playwright (b. 1931)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_%C4%86irilov"}]}, {"year": "2014", "text": "<PERSON>, Australian cricketer (b. 1935)", "html": "2014 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian cricketer (b. 1935)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian cricketer (b. 1935)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2014", "text": "<PERSON>, American football player and coach (b. 1987)", "html": "2014 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player and coach (b. 1987)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player and coach (b. 1987)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2014", "text": "<PERSON><PERSON><PERSON><PERSON>, Polish soldier, pilot, and architect (b. 1920)", "html": "2014 - <a href=\"https://wikipedia.org/wiki/Jadwiga_Pi%C5%82sudska\" title=\"<PERSON><PERSON><PERSON><PERSON> Piłsudska\"><PERSON><PERSON><PERSON><PERSON></a>, Polish soldier, pilot, and architect (b. 1920)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Jadwiga_Pi%C5%82sudska\" title=\"<PERSON><PERSON><PERSON><PERSON> Pił<PERSON>ds<PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Polish soldier, pilot, and architect (b. 1920)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Jadwiga_Pi%C5%82sudska"}]}, {"year": "2014", "text": "<PERSON>, American soldier, pilot, and politician, 74th Governor of Georgia (b. 1925)", "html": "2014 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American soldier, pilot, and politician, 74th <a href=\"https://wikipedia.org/wiki/Governor_of_Georgia\" title=\"Governor of Georgia\">Governor of Georgia</a> (b. 1925)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American soldier, pilot, and politician, 74th <a href=\"https://wikipedia.org/wiki/Governor_of_Georgia\" title=\"Governor of Georgia\">Governor of Georgia</a> (b. 1925)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}, {"title": "Governor of Georgia", "link": "https://wikipedia.org/wiki/Governor_of_Georgia"}]}, {"year": "2015", "text": "<PERSON>, American actor (b. 1938)", "html": "2015 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor (b. 1938)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor (b. 1938)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2015", "text": "<PERSON>, American graphic designer and producer (b. 1945)", "html": "2015 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American graphic designer and producer (b. 1945)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American graphic designer and producer (b. 1945)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "2015", "text": "<PERSON>, Canadian ice hockey player and coach (b. 1926)", "html": "2015 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player and coach (b. 1926)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player and coach (b. 1926)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2015", "text": "<PERSON><PERSON>, American general (b. 1924)", "html": "2015 - <a href=\"https://wikipedia.org/wiki/Alton_D._Slay\" title=\"Alton D. Slay\"><PERSON><PERSON></a>, American general (b. 1924)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Alton_D._Slay\" title=\"Alton D. Slay\"><PERSON><PERSON><PERSON></a>, American general (b. 1924)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Alton_D._Slay"}]}, {"year": "2016", "text": "<PERSON>, American computer engineer (b. 1918)", "html": "2016 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American computer engineer (b. 1918)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American computer engineer (b. 1918)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "2016", "text": "<PERSON>, American politician and writer (b. 1922)", "html": "2016 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American politician and writer (b. 1922)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American politician and writer (b. 1922)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2016", "text": "<PERSON>, Romanian football player (b. 1972)", "html": "2016 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Romanian football player (b. 1972)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Romanian football player (b. 1972)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2017", "text": "<PERSON><PERSON><PERSON>, Japanese actress (b. 1960)", "html": "2017 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese actress (b. 1960)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese actress (b. 1960)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON><PERSON><PERSON>"}]}, {"year": "2017", "text": "<PERSON>, American actress (b. 1934)", "html": "2017 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress (b. 1934)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress (b. 1934)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2018", "text": "<PERSON>, American novelist, playwright, and screenwriter (b. 1931)", "html": "2018 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American novelist, playwright, and screenwriter (b. 1931)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American novelist, playwright, and screenwriter (b. 1931)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "2019", "text": "<PERSON>, Scottish astronomer (b. 1947)", "html": "2019 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish astronomer (b. 1947)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish astronomer (b. 1947)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "2019", "text": "<PERSON>, British photographer (b. 1938)", "html": "2019 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%27N<PERSON><PERSON>_(photographer)\" title=\"<PERSON> (photographer)\"><PERSON></a>, British photographer (b. 1938)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%27N<PERSON><PERSON>_(photographer)\" title=\"<PERSON> (photographer)\"><PERSON></a>, British photographer (b. 1938)", "links": [{"title": "<PERSON> (photographer)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>%27N<PERSON><PERSON>_(photographer)"}]}, {"year": "2020", "text": "<PERSON>, English string teacher (b. 1936)", "html": "2020 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English string teacher (b. 1936)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English string teacher (b. 1936)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2021", "text": "<PERSON><PERSON><PERSON>, Finnish journalist and politician (b. 1964)", "html": "2021 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Finnish journalist and politician (b. 1964)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Finnish journalist and politician (b. 1964)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "2022", "text": "<PERSON>, French-American actor and author (b. 1926)", "html": "2022 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French-American actor and author (b. 1926)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French-American actor and author (b. 1926)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2024", "text": "<PERSON>, Russian ballet dancer (b. 1985)", "html": "2024 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian ballet dancer (b. 1985)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian ballet dancer (b. 1985)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Vladimir_<PERSON>"}]}]}}