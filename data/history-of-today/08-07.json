{"date": "August 7", "url": "https://wikipedia.org/wiki/August_7", "data": {"Events": [{"year": "461", "text": "Roman Emperor <PERSON><PERSON> is beheaded near the river Iria in north-west Italy following his arrest and deposition by the magister militum Ricimer.", "html": "461 - Roman Emperor <a href=\"https://wikipedia.org/wiki/Major<PERSON>\" title=\"Majorian\"><PERSON><PERSON></a> is beheaded near the river <a href=\"https://wikipedia.org/wiki/Staffora\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a> in north-west Italy following his arrest and deposition by the <i>magister militum</i> <a href=\"https://wikipedia.org/wiki/Ricimer\" title=\"Ricimer\">Ricimer</a>.", "no_year_html": "Roman Emperor <a href=\"https://wikipedia.org/wiki/Major<PERSON>\" title=\"Majorian\"><PERSON><PERSON></a> is beheaded near the river <a href=\"https://wikipedia.org/wiki/Staffora\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a> in north-west Italy following his arrest and deposition by the <i>magister militum</i> <a href=\"https://wikipedia.org/wiki/Ricimer\" title=\"Ricimer\">Ricimer</a>.", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>ian"}, {"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Staffora"}, {"title": "Ricimer", "link": "https://wikipedia.org/wiki/Ricimer"}]}, {"year": "626", "text": "The Avar and Slav armies leave the siege of Constantinople.", "html": "626 - The <a href=\"https://wikipedia.org/wiki/Avars_(Carpathians)\" class=\"mw-redirect\" title=\"Avars (Carpathians)\">Avar</a> and <a href=\"https://wikipedia.org/wiki/Slavs\" title=\"Slavs\">Slav</a> armies leave the <a href=\"https://wikipedia.org/wiki/Siege_of_Constantinople_(626)\" title=\"Siege of Constantinople (626)\">siege of Constantinople</a>.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Avars_(Carpathians)\" class=\"mw-redirect\" title=\"Avars (Carpathians)\">Avar</a> and <a href=\"https://wikipedia.org/wiki/Slavs\" title=\"Slavs\">Slav</a> armies leave the <a href=\"https://wikipedia.org/wiki/Siege_of_Constantinople_(626)\" title=\"Siege of Constantinople (626)\">siege of Constantinople</a>.", "links": [{"title": "Avars (Carpathians)", "link": "https://wikipedia.org/wiki/Avars_(Carpathians)"}, {"title": "Slavs", "link": "https://wikipedia.org/wiki/Slavs"}, {"title": "Siege of Constantinople (626)", "link": "https://wikipedia.org/wiki/Siege_of_Constantinople_(626)"}]}, {"year": "768", "text": "<PERSON> <PERSON> is elected to office, and quickly seeks Frankish protection against the Lombard threat, since the Byzantine Empire is no longer able to help.", "html": "768 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\">Pope <PERSON> III</a> is elected to office, and quickly seeks Frankish protection against the Lombard threat, since the Byzantine Empire is no longer able to help.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\">Pope <PERSON> III</a> is elected to office, and quickly seeks Frankish protection against the Lombard threat, since the Byzantine Empire is no longer able to help.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "936", "text": "Coronation of King <PERSON> of Germany.", "html": "936 - Coronation of King <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Holy_Roman_Emperor\" class=\"mw-redirect\" title=\"<PERSON>, Holy Roman Emperor\"><PERSON> of Germany</a>.", "no_year_html": "Coronation of King <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Holy_Roman_Emperor\" class=\"mw-redirect\" title=\"<PERSON>, Holy Roman Emperor\"><PERSON> of Germany</a>.", "links": [{"title": "<PERSON>, Holy Roman Emperor", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Holy_Roman_Emperor"}]}, {"year": "1461", "text": "The Ming dynasty Chinese military general <PERSON> stages a coup against the Tianshun Emperor.", "html": "1461 - The <a href=\"https://wikipedia.org/wiki/Ming_dynasty\" title=\"Ming dynasty\">Ming dynasty</a> <a href=\"https://wikipedia.org/wiki/Military_history_of_China_before_1912\" title=\"Military history of China before 1912\">Chinese military general</a> <PERSON> <a href=\"https://wikipedia.org/wiki/Rebellion_of_<PERSON>_<PERSON>\" title=\"Rebellion of <PERSON> Qin\">stages a coup</a> against the <a href=\"https://wikipedia.org/wiki/Zhengtong_Emperor\" class=\"mw-redirect\" title=\"Zhengtong Emperor\">T<PERSON>hun Emperor</a>.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Ming_dynasty\" title=\"Ming dynasty\">Ming dynasty</a> <a href=\"https://wikipedia.org/wiki/Military_history_of_China_before_1912\" title=\"Military history of China before 1912\">Chinese military general</a> <PERSON> <a href=\"https://wikipedia.org/wiki/Rebellion_of_<PERSON>_<PERSON>\" title=\"Rebellion of <PERSON> Qin\">stages a coup</a> against the <a href=\"https://wikipedia.org/wiki/Zhengtong_Emperor\" class=\"mw-redirect\" title=\"Zhengtong Emperor\"><PERSON><PERSON>hun Emperor</a>.", "links": [{"title": "Ming dynasty", "link": "https://wikipedia.org/wiki/Ming_dynasty"}, {"title": "Military history of China before 1912", "link": "https://wikipedia.org/wiki/Military_history_of_China_before_1912"}, {"title": "Rebellion of Cao Qin", "link": "https://wikipedia.org/wiki/Rebellion_of_<PERSON>_<PERSON>"}, {"title": "<PERSON><PERSON>g Emperor", "link": "https://wikipedia.org/wiki/<PERSON>tong_Emperor"}]}, {"year": "1479", "text": "Battle of Guinegate: French troops of King <PERSON> were defeated by the Burgundians led by <PERSON><PERSON><PERSON> of Habsburg.", "html": "1479 - <a href=\"https://wikipedia.org/wiki/Battle_of_Guinegate_(1479)\" title=\"Battle of Guinegate (1479)\">Battle of Guinegate</a>: French troops of King <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> were defeated by the Burgundians led by <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Holy_Roman_Emperor\" title=\"<PERSON> I, Holy Roman Emperor\">Archduke <PERSON> of Habsburg</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Battle_of_Guinegate_(1479)\" title=\"Battle of Guinegate (1479)\">Battle of Guinegate</a>: French troops of King <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> were defeated by the Burgundians led by <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Holy_Roman_Emperor\" title=\"Maximilian I, Holy Roman Emperor\">Archduke <PERSON> of Habsburg</a>.", "links": [{"title": "Battle of Guinegate (1479)", "link": "https://wikipedia.org/wiki/Battle_of_Guinegate_(1479)"}, {"title": "Louis XI", "link": "https://wikipedia.org/wiki/Louis_XI"}, {"title": "<PERSON>, Holy Roman Emperor", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Holy_Roman_Emperor"}]}, {"year": "1679", "text": "The brigantine Le Griffon becomes the first ship to sail the upper Great Lakes of North America.", "html": "1679 - The <a href=\"https://wikipedia.org/wiki/Brigantine\" title=\"Brigantine\">brigantine</a> <i><a href=\"https://wikipedia.org/wiki/Le_Griffon\" title=\"Le Griffon\"><PERSON> G<PERSON></a></i> becomes the first ship to sail the upper <a href=\"https://wikipedia.org/wiki/Great_Lakes\" title=\"Great Lakes\">Great Lakes</a> of North America.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Brigantine\" title=\"Brigantine\">brigantine</a> <i><a href=\"https://wikipedia.org/wiki/Le_Griffon\" title=\"Le Griffon\">Le Griff<PERSON></a></i> becomes the first ship to sail the upper <a href=\"https://wikipedia.org/wiki/Great_Lakes\" title=\"Great Lakes\">Great Lakes</a> of North America.", "links": [{"title": "Brigantine", "link": "https://wikipedia.org/wiki/B<PERSON>antine"}, {"title": "Le Griffon", "link": "https://wikipedia.org/wiki/Le_Griffon"}, {"title": "Great Lakes", "link": "https://wikipedia.org/wiki/Great_Lakes"}]}, {"year": "1714", "text": "The Battle of Gangut: The first important victory of the Russian Navy.", "html": "1714 - The <a href=\"https://wikipedia.org/wiki/Battle_of_Gangut\" title=\"Battle of Gangut\">Battle of Gangut</a>: The first important victory of the <a href=\"https://wikipedia.org/wiki/Russian_Navy\" title=\"Russian Navy\">Russian Navy</a>.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Battle_of_Gangut\" title=\"Battle of Gangut\">Battle of Gangut</a>: The first important victory of the <a href=\"https://wikipedia.org/wiki/Russian_Navy\" title=\"Russian Navy\">Russian Navy</a>.", "links": [{"title": "Battle of Gangut", "link": "https://wikipedia.org/wiki/Battle_of_Gangut"}, {"title": "Russian Navy", "link": "https://wikipedia.org/wiki/Russian_Navy"}]}, {"year": "1743", "text": "The Treaty of Åbo ended the 1741-1743 Russo-Swedish War.", "html": "1743 - The <a href=\"https://wikipedia.org/wiki/Treaty_of_%C3%85bo\" title=\"Treaty of Åbo\">Treaty of Åbo</a> ended the <a href=\"https://wikipedia.org/wiki/Russo-Swedish_War_(1741%E2%80%931743)\" title=\"Russo-Swedish War (1741-1743)\">1741-1743 Russo-Swedish War</a>.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Treaty_of_%C3%85bo\" title=\"Treaty of Åbo\">Treaty of Åbo</a> ended the <a href=\"https://wikipedia.org/wiki/Russo-Swedish_War_(1741%E2%80%931743)\" title=\"Russo-Swedish War (1741-1743)\">1741-1743 Russo-Swedish War</a>.", "links": [{"title": "Treaty of Åbo", "link": "https://wikipedia.org/wiki/Treaty_of_%C3%85bo"}, {"title": "Russo-Swedish War (1741-1743)", "link": "https://wikipedia.org/wiki/Russo-Swedish_War_(1741%E2%80%931743)"}]}, {"year": "1782", "text": "<PERSON> orders the creation of the Badge of Military Merit to honor soldiers wounded in battle. It is later renamed to the more poetic Purple Heart.", "html": "1782 - <a href=\"https://wikipedia.org/wiki/George_<PERSON>\" title=\"George Washington\"><PERSON></a> orders the creation of the <a href=\"https://wikipedia.org/wiki/Badge_of_Military_Merit\" title=\"Badge of Military Merit\">Badge of Military Merit</a> to honor soldiers wounded in battle. It is later renamed to the more poetic <a href=\"https://wikipedia.org/wiki/Purple_Heart\" title=\"Purple Heart\">Purple Heart</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/George_<PERSON>\" title=\"George Washington\"><PERSON></a> orders the creation of the <a href=\"https://wikipedia.org/wiki/Badge_of_Military_Merit\" title=\"Badge of Military Merit\">Badge of Military Merit</a> to honor soldiers wounded in battle. It is later renamed to the more poetic <a href=\"https://wikipedia.org/wiki/Purple_Heart\" title=\"Purple Heart\">Purple Heart</a>.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/George_Washington"}, {"title": "Badge of Military Merit", "link": "https://wikipedia.org/wiki/Badge_of_Military_Merit"}, {"title": "Purple Heart", "link": "https://wikipedia.org/wiki/<PERSON>_Heart"}]}, {"year": "1786", "text": "The first federal Indian Reservation is created by the United States.", "html": "1786 - The first federal <a href=\"https://wikipedia.org/wiki/Indian_reservation\" title=\"Indian reservation\">Indian Reservation</a> is created by the <a href=\"https://wikipedia.org/wiki/United_States\" title=\"United States\">United States</a>.", "no_year_html": "The first federal <a href=\"https://wikipedia.org/wiki/Indian_reservation\" title=\"Indian reservation\">Indian Reservation</a> is created by the <a href=\"https://wikipedia.org/wiki/United_States\" title=\"United States\">United States</a>.", "links": [{"title": "Indian reservation", "link": "https://wikipedia.org/wiki/Indian_reservation"}, {"title": "United States", "link": "https://wikipedia.org/wiki/United_States"}]}, {"year": "1789", "text": "The United States Department of War is established.", "html": "1789 - The <a href=\"https://wikipedia.org/wiki/United_States_Department_of_War\" title=\"United States Department of War\">United States Department of War</a> is established.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/United_States_Department_of_War\" title=\"United States Department of War\">United States Department of War</a> is established.", "links": [{"title": "United States Department of War", "link": "https://wikipedia.org/wiki/United_States_Department_of_War"}]}, {"year": "1791", "text": "American troops destroy the Miami town of Kenapacomaqua near the site of present-day Logansport, Indiana in the Northwest Indian War.", "html": "1791 - American troops destroy the <a href=\"https://wikipedia.org/wiki/Miami_people\" title=\"Miami people\">Miami</a> town of <a href=\"https://wikipedia.org/wiki/Battle_of_Kenapacomaqua\" title=\"Battle of Kenapacomaqua\">Kenapacomaqua</a> near the site of present-day <a href=\"https://wikipedia.org/wiki/Logansport,_Indiana\" title=\"Logansport, Indiana\">Logansport, Indiana</a> in the <a href=\"https://wikipedia.org/wiki/Northwest_Indian_War\" title=\"Northwest Indian War\">Northwest Indian War</a>.", "no_year_html": "American troops destroy the <a href=\"https://wikipedia.org/wiki/Miami_people\" title=\"Miami people\">Miami</a> town of <a href=\"https://wikipedia.org/wiki/Battle_of_Kenapacomaqua\" title=\"Battle of Kenapacomaqua\">Kenapacomaqua</a> near the site of present-day <a href=\"https://wikipedia.org/wiki/Logansport,_Indiana\" title=\"Logansport, Indiana\">Logansport, Indiana</a> in the <a href=\"https://wikipedia.org/wiki/Northwest_Indian_War\" title=\"Northwest Indian War\">Northwest Indian War</a>.", "links": [{"title": "Miami people", "link": "https://wikipedia.org/wiki/Miami_people"}, {"title": "Battle of Kenapacomaqua", "link": "https://wikipedia.org/wiki/Battle_of_Kenapacomaqua"}, {"title": "Logansport, Indiana", "link": "https://wikipedia.org/wiki/Logansport,_Indiana"}, {"title": "Northwest Indian War", "link": "https://wikipedia.org/wiki/Northwest_Indian_War"}]}, {"year": "1794", "text": "U.S. President <PERSON> invokes the Militia Acts of 1792 to suppress the Whiskey Rebellion in western Pennsylvania.", "html": "1794 - <a href=\"https://wikipedia.org/wiki/President_of_the_United_States\" title=\"President of the United States\">U.S. President</a> <PERSON> invokes the <a href=\"https://wikipedia.org/wiki/Militia_Acts_of_1792\" title=\"Militia Acts of 1792\">Militia Acts of 1792</a> to suppress the <a href=\"https://wikipedia.org/wiki/Whiskey_Rebellion\" title=\"Whiskey Rebellion\">Whiskey Rebellion</a> in <a href=\"https://wikipedia.org/wiki/Western_Pennsylvania\" title=\"Western Pennsylvania\">western Pennsylvania</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/President_of_the_United_States\" title=\"President of the United States\">U.S. President</a> <PERSON> invokes the <a href=\"https://wikipedia.org/wiki/Militia_Acts_of_1792\" title=\"Militia Acts of 1792\">Militia Acts of 1792</a> to suppress the <a href=\"https://wikipedia.org/wiki/Whiskey_Rebellion\" title=\"Whiskey Rebellion\">Whiskey Rebellion</a> in <a href=\"https://wikipedia.org/wiki/Western_Pennsylvania\" title=\"Western Pennsylvania\">western Pennsylvania</a>.", "links": [{"title": "President of the United States", "link": "https://wikipedia.org/wiki/President_of_the_United_States"}, {"title": "Militia Acts of 1792", "link": "https://wikipedia.org/wiki/Militia_Acts_of_1792"}, {"title": "Whiskey Rebellion", "link": "https://wikipedia.org/wiki/Whiskey_Rebellion"}, {"title": "Western Pennsylvania", "link": "https://wikipedia.org/wiki/Western_Pennsylvania"}]}, {"year": "1819", "text": "<PERSON><PERSON><PERSON> Bolívar triumphs over Spain in the Battle of Boyacá.", "html": "1819 - <a href=\"https://wikipedia.org/wiki/Sim%C3%B3n_Bol%C3%ADvar\" title=\"Sim<PERSON> Bolívar\"><PERSON><PERSON><PERSON></a> triumphs over Spain in the <a href=\"https://wikipedia.org/wiki/Battle_of_Boyac%C3%A1\" title=\"Battle of Boyacá\">Battle of Boyacá</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Sim%C3%B3n_Bol%C3%ADvar\" title=\"Simón Bolívar\"><PERSON><PERSON><PERSON></a> triumphs over Spain in the <a href=\"https://wikipedia.org/wiki/Battle_of_Boyac%C3%A1\" title=\"Battle of Boyacá\">Battle of Boyacá</a>.", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Sim%C3%B3n_Bol%C3%ADvar"}, {"title": "Battle of Boyacá", "link": "https://wikipedia.org/wiki/Battle_of_Boyac%C3%A1"}]}, {"year": "1858", "text": "The first Australian rules football match is played between Melbourne Grammar and Scotch College.", "html": "1858 - The first <a href=\"https://wikipedia.org/wiki/Australian_rules_football\" title=\"Australian rules football\">Australian rules football</a> match is played between <a href=\"https://wikipedia.org/wiki/Melbourne_Grammar\" class=\"mw-redirect\" title=\"Melbourne Grammar\">Melbourne Grammar</a> and Scotch College.", "no_year_html": "The first <a href=\"https://wikipedia.org/wiki/Australian_rules_football\" title=\"Australian rules football\">Australian rules football</a> match is played between <a href=\"https://wikipedia.org/wiki/Melbourne_Grammar\" class=\"mw-redirect\" title=\"Melbourne Grammar\">Melbourne Grammar</a> and Scotch College.", "links": [{"title": "Australian rules football", "link": "https://wikipedia.org/wiki/Australian_rules_football"}, {"title": "Melbourne Grammar", "link": "https://wikipedia.org/wiki/Melbourne_Grammar"}]}, {"year": "1890", "text": "<PERSON>, found guilty of the 1889 <PERSON><PERSON><PERSON><PERSON><PERSON> murder, became the last woman to be executed in Sweden.[better source needed]", "html": "1890 - <a href=\"https://wikipedia.org/wiki/<PERSON>_M%C3%A5nsdotter\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, found guilty of the 1889 <a href=\"https://wikipedia.org/wiki/Yngsj%C3%B6_murder\" title=\"Yngsjö murder\">Yng<PERSON><PERSON><PERSON> murder</a>, became the last woman to be executed in Sweden.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_M%C3%A5nsdotter\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, found guilty of the 1889 <a href=\"https://wikipedia.org/wiki/Yngsj%C3%B6_murder\" title=\"Yngsjö murder\">Yngsj<PERSON> murder</a>, became the last woman to be executed in Sweden.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Anna_M%C3%A5nsdotter"}, {"title": "<PERSON><PERSON><PERSON><PERSON><PERSON> murder", "link": "https://wikipedia.org/wiki/Yngsj%C3%B6_murder"}]}, {"year": "1909", "text": "<PERSON> and three friends become the first women to complete a transcontinental auto trip, taking 59 days to travel from New York, New York to San Francisco, California.", "html": "1909 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> and three friends become the first women to complete a transcontinental auto trip, taking 59 days to travel from New York, <a href=\"https://wikipedia.org/wiki/New_York_(state)\" title=\"New York (state)\">New York</a> to <a href=\"https://wikipedia.org/wiki/San_Francisco\" title=\"San Francisco\">San Francisco, California</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> and three friends become the first women to complete a transcontinental auto trip, taking 59 days to travel from New York, <a href=\"https://wikipedia.org/wiki/New_York_(state)\" title=\"New York (state)\">New York</a> to <a href=\"https://wikipedia.org/wiki/San_Francisco\" title=\"San Francisco\">San Francisco, California</a>.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>"}, {"title": "New York (state)", "link": "https://wikipedia.org/wiki/New_York_(state)"}, {"title": "San Francisco", "link": "https://wikipedia.org/wiki/San_Francisco"}]}, {"year": "1927", "text": "The Peace Bridge opens between Fort Erie, Ontario and Buffalo, New York.", "html": "1927 - The <a href=\"https://wikipedia.org/wiki/Peace_Bridge\" title=\"Peace Bridge\">Peace Bridge</a> opens between <a href=\"https://wikipedia.org/wiki/Fort_Erie,_Ontario\" title=\"Fort Erie, Ontario\">Fort Erie, Ontario</a> and <a href=\"https://wikipedia.org/wiki/Buffalo,_New_York\" title=\"Buffalo, New York\">Buffalo, New York</a>.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Peace_Bridge\" title=\"Peace Bridge\">Peace Bridge</a> opens between <a href=\"https://wikipedia.org/wiki/Fort_Erie,_Ontario\" title=\"Fort Erie, Ontario\">Fort Erie, Ontario</a> and <a href=\"https://wikipedia.org/wiki/Buffalo,_New_York\" title=\"Buffalo, New York\">Buffalo, New York</a>.", "links": [{"title": "Peace Bridge", "link": "https://wikipedia.org/wiki/Peace_Bridge"}, {"title": "Fort Erie, Ontario", "link": "https://wikipedia.org/wiki/Fort_Erie,_Ontario"}, {"title": "Buffalo, New York", "link": "https://wikipedia.org/wiki/Buffalo,_New_York"}]}, {"year": "1930", "text": "The last confirmed lynching of black people in the Northern United States occurs in Marion, Indiana; two men, <PERSON> and <PERSON><PERSON><PERSON>, are killed.", "html": "1930 - The last confirmed <a href=\"https://wikipedia.org/wiki/Lynching\" title=\"Lynching\">lynching</a> of black people in the Northern United States occurs in <a href=\"https://wikipedia.org/wiki/Marion,_Indiana\" title=\"Marion, Indiana\">Marion, Indiana</a>; two men, <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_and_<PERSON><PERSON><PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON> and <PERSON><PERSON><PERSON>\"><PERSON> and <PERSON><PERSON><PERSON></a>, are killed.", "no_year_html": "The last confirmed <a href=\"https://wikipedia.org/wiki/Lynching\" title=\"Lynching\">lynching</a> of black people in the Northern United States occurs in <a href=\"https://wikipedia.org/wiki/Marion,_Indiana\" title=\"Marion, Indiana\">Marion, Indiana</a>; two men, <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_and_<PERSON><PERSON><PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON> and <PERSON><PERSON><PERSON>\"><PERSON> and <PERSON><PERSON><PERSON></a>, are killed.", "links": [{"title": "Lynching", "link": "https://wikipedia.org/wiki/Lynching"}, {"title": "Marion, Indiana", "link": "https://wikipedia.org/wiki/Marion,_Indiana"}, {"title": "<PERSON> and <PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_and_<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1933", "text": "The Kingdom of Iraq slaughters over 3,000 Assyrians in the village of Simele. This date is recognized as Martyrs Day or National Day of Mourning by the Assyrian community in memory of the Simele massacre.", "html": "1933 - The <a href=\"https://wikipedia.org/wiki/Kingdom_of_Iraq\" title=\"Kingdom of Iraq\">Kingdom of Iraq</a> <a href=\"https://wikipedia.org/wiki/Simele_massacre\" title=\"Simele massacre\">slaughters</a> over 3,000 Assyrians in the village of Simele. This date is recognized as <i>Martyrs Day</i> or <i>National Day of Mourning</i> by the Assyrian community in memory of the Simele massacre.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Kingdom_of_Iraq\" title=\"Kingdom of Iraq\">Kingdom of Iraq</a> <a href=\"https://wikipedia.org/wiki/Simele_massacre\" title=\"Simele massacre\">slaughters</a> over 3,000 Assyrians in the village of Simele. This date is recognized as <i>Martyrs Day</i> or <i>National Day of Mourning</i> by the Assyrian community in memory of the Simele massacre.", "links": [{"title": "Kingdom of Iraq", "link": "https://wikipedia.org/wiki/Kingdom_of_Iraq"}, {"title": "Simele massacre", "link": "https://wikipedia.org/wiki/Si<PERSON>e_massacre"}]}, {"year": "1942", "text": "World War II: The Battle of Guadalcanal begins as the United States Marines initiate the first American offensive of the war with landings on Guadalcanal and Tulagi in the Solomon Islands.", "html": "1942 - <a href=\"https://wikipedia.org/wiki/World_War_II\" title=\"World War II\">World War II</a>: The <a href=\"https://wikipedia.org/wiki/Guadalcanal_Campaign\" class=\"mw-redirect\" title=\"Guadalcanal Campaign\">Battle of Guadalcanal</a> begins as the <a href=\"https://wikipedia.org/wiki/United_States_Marine_Corps\" title=\"United States Marine Corps\">United States Marines</a> initiate the first American offensive of the war with landings on <a href=\"https://wikipedia.org/wiki/Guadalcanal\" title=\"Guadalcanal\">Guadalcanal</a> and <a href=\"https://wikipedia.org/wiki/Battle_of_Tulagi_and_Gavutu%E2%80%93Tanambogo\" title=\"Battle of Tulagi and Gavutu-Tanambogo\">Tulagi</a> in the <a href=\"https://wikipedia.org/wiki/Solomon_Islands\" title=\"Solomon Islands\">Solomon Islands</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/World_War_II\" title=\"World War II\">World War II</a>: The <a href=\"https://wikipedia.org/wiki/Guadalcanal_Campaign\" class=\"mw-redirect\" title=\"Guadalcanal Campaign\">Battle of Guadalcanal</a> begins as the <a href=\"https://wikipedia.org/wiki/United_States_Marine_Corps\" title=\"United States Marine Corps\">United States Marines</a> initiate the first American offensive of the war with landings on <a href=\"https://wikipedia.org/wiki/Guadalcanal\" title=\"Guadalcanal\">Guadalcanal</a> and <a href=\"https://wikipedia.org/wiki/Battle_of_Tulagi_and_Gavutu%E2%80%93Tanambogo\" title=\"Battle of Tulagi and Gavutu-Tanambogo\">Tulagi</a> in the <a href=\"https://wikipedia.org/wiki/Solomon_Islands\" title=\"Solomon Islands\">Solomon Islands</a>.", "links": [{"title": "World War II", "link": "https://wikipedia.org/wiki/World_War_II"}, {"title": "Guadalcanal Campaign", "link": "https://wikipedia.org/wiki/Guadalcanal_Campaign"}, {"title": "United States Marine Corps", "link": "https://wikipedia.org/wiki/United_States_Marine_Corps"}, {"title": "Guadalcanal", "link": "https://wikipedia.org/wiki/Guadalcanal"}, {"title": "Battle of Tulagi and Gavutu-Tanambogo", "link": "https://wikipedia.org/wiki/Battle_of_Tulagi_and_Gavutu%E2%80%93Tanambogo"}, {"title": "Solomon Islands", "link": "https://wikipedia.org/wiki/Solomon_Islands"}]}, {"year": "1944", "text": "IBM dedicates the first program-controlled calculator, the Automatic Sequence Controlled Calculator (known best as the Harvard Mark I).", "html": "1944 - <a href=\"https://wikipedia.org/wiki/IBM\" title=\"IBM\">IBM</a> dedicates the first program-controlled <a href=\"https://wikipedia.org/wiki/Calculator\" title=\"Calculator\">calculator</a>, the Automatic Sequence Controlled Calculator (known best as the <a href=\"https://wikipedia.org/wiki/Harvard_Mark_I\" title=\"Harvard Mark I\">Harvard Mark I</a>).", "no_year_html": "<a href=\"https://wikipedia.org/wiki/IBM\" title=\"IBM\">IBM</a> dedicates the first program-controlled <a href=\"https://wikipedia.org/wiki/Calculator\" title=\"Calculator\">calculator</a>, the Automatic Sequence Controlled Calculator (known best as the <a href=\"https://wikipedia.org/wiki/Harvard_Mark_I\" title=\"Harvard Mark I\">Harvard Mark I</a>).", "links": [{"title": "IBM", "link": "https://wikipedia.org/wiki/IBM"}, {"title": "Calculator", "link": "https://wikipedia.org/wiki/Calculator"}, {"title": "Harvard Mark I", "link": "https://wikipedia.org/wiki/Harvard_Mark_I"}]}, {"year": "1946", "text": "The government of the Soviet Union presented a note to its Turkish counterparts which refuted the latter's sovereignty over the Turkish Straits, thus beginning the Turkish Straits crisis.", "html": "1946 - The government of the <a href=\"https://wikipedia.org/wiki/Soviet_Union\" title=\"Soviet Union\">Soviet Union</a> presented a note to its <a href=\"https://wikipedia.org/wiki/Turkey\" title=\"Turkey\">Turkish counterparts</a> which refuted the latter's sovereignty over the <a href=\"https://wikipedia.org/wiki/Turkish_Straits\" class=\"mw-redirect\" title=\"Turkish Straits\">Turkish Straits</a>, thus beginning the <a href=\"https://wikipedia.org/wiki/Turkish_Straits_crisis\" title=\"Turkish Straits crisis\">Turkish Straits crisis</a>.", "no_year_html": "The government of the <a href=\"https://wikipedia.org/wiki/Soviet_Union\" title=\"Soviet Union\">Soviet Union</a> presented a note to its <a href=\"https://wikipedia.org/wiki/Turkey\" title=\"Turkey\">Turkish counterparts</a> which refuted the latter's sovereignty over the <a href=\"https://wikipedia.org/wiki/Turkish_Straits\" class=\"mw-redirect\" title=\"Turkish Straits\">Turkish Straits</a>, thus beginning the <a href=\"https://wikipedia.org/wiki/Turkish_Straits_crisis\" title=\"Turkish Straits crisis\">Turkish Straits crisis</a>.", "links": [{"title": "Soviet Union", "link": "https://wikipedia.org/wiki/Soviet_Union"}, {"title": "Turkey", "link": "https://wikipedia.org/wiki/Turkey"}, {"title": "Turkish Straits", "link": "https://wikipedia.org/wiki/Turkish_Straits"}, {"title": "Turkish Straits crisis", "link": "https://wikipedia.org/wiki/Turkish_Straits_crisis"}]}, {"year": "1947", "text": "<PERSON>'s balsa wood raft, the Kon-Tiki, smashes into the reef at Raroia in the Tuamotu Islands after a 101-day, 7,000 kilometres (4,300 mi) journey across the Pacific Ocean in an attempt to prove that pre-historic peoples could have traveled from South America.", "html": "1947 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>'s <a href=\"https://wikipedia.org/wiki/Ochroma_pyramidale\" class=\"mw-redirect\" title=\"Ochroma pyramidale\">balsa wood</a> raft, the <i><a href=\"https://wikipedia.org/wiki/Kon-Tiki\" class=\"mw-redirect\" title=\"Kon-Tiki\">Kon-Tiki</a></i>, smashes into the <a href=\"https://wikipedia.org/wiki/Reef\" title=\"Reef\">reef</a> at <a href=\"https://wikipedia.org/wiki/Raroia\" title=\"Raroia\">Raroia</a> in the <a href=\"https://wikipedia.org/wiki/Tuamotus\" title=\"Tuamotus\">Tuamotu Islands</a> after a 101-day, 7,000 kilometres (4,300 mi) journey across the Pacific Ocean in an attempt to prove that pre-historic peoples could have traveled from South America.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>'s <a href=\"https://wikipedia.org/wiki/Ochroma_pyramidale\" class=\"mw-redirect\" title=\"Ochroma pyramidale\">balsa wood</a> raft, the <i><a href=\"https://wikipedia.org/wiki/Kon-Tiki\" class=\"mw-redirect\" title=\"Kon-Tiki\">Kon-Tiki</a></i>, smashes into the <a href=\"https://wikipedia.org/wiki/Reef\" title=\"Reef\">reef</a> at <a href=\"https://wikipedia.org/wiki/Raroia\" title=\"Raroia\">Raroia</a> in the <a href=\"https://wikipedia.org/wiki/Tuamotus\" title=\"Tuamotus\">Tuamotu Islands</a> after a 101-day, 7,000 kilometres (4,300 mi) journey across the Pacific Ocean in an attempt to prove that pre-historic peoples could have traveled from South America.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Ochroma pyramidale", "link": "https://wikipedia.org/wiki/Ochroma_pyramidale"}, {"title": "Kon-Tiki", "link": "https://wikipedia.org/wiki/Kon-Tiki"}, {"title": "Reef", "link": "https://wikipedia.org/wiki/Reef"}, {"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Raroia"}, {"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>tus"}]}, {"year": "1947", "text": "The Bombay Municipal Corporation formally takes over the Bombay Electric Supply and Transport (BEST).", "html": "1947 - The <a href=\"https://wikipedia.org/wiki/Municipal_Corporation_of_Greater_Mumbai\" class=\"mw-redirect\" title=\"Municipal Corporation of Greater Mumbai\">Bombay Municipal Corporation</a> formally takes over the <a href=\"https://wikipedia.org/wiki/Brihanmumbai_Electric_Supply_and_Transport\" title=\"Brihanmumbai Electric Supply and Transport\">Bombay Electric Supply and Transport</a> (BEST).", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Municipal_Corporation_of_Greater_Mumbai\" class=\"mw-redirect\" title=\"Municipal Corporation of Greater Mumbai\">Bombay Municipal Corporation</a> formally takes over the <a href=\"https://wikipedia.org/wiki/Brihanmumbai_Electric_Supply_and_Transport\" title=\"Brihanmumbai Electric Supply and Transport\">Bombay Electric Supply and Transport</a> (BEST).", "links": [{"title": "Municipal Corporation of Greater Mumbai", "link": "https://wikipedia.org/wiki/Municipal_Corporation_of_Greater_Mumbai"}, {"title": "Brihanmumbai Electric Supply and Transport", "link": "https://wikipedia.org/wiki/Brihanmumbai_Electric_Supply_and_Transport"}]}, {"year": "1959", "text": "Explorer program: Explorer 6 launches from the Atlantic Missile Range in Cape Canaveral, Florida.", "html": "1959 - <a href=\"https://wikipedia.org/wiki/Explorer_program\" class=\"mw-redirect\" title=\"Explorer program\">Explorer program</a>: <i><a href=\"https://wikipedia.org/wiki/Explorer_6\" title=\"Explorer 6\">Explorer 6</a></i> launches from the <a href=\"https://wikipedia.org/wiki/Eastern_Range\" title=\"Eastern Range\">Atlantic Missile Range</a> in <a href=\"https://wikipedia.org/wiki/Cape_Canaveral,_Florida\" title=\"Cape Canaveral, Florida\">Cape Canaveral, Florida</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Explorer_program\" class=\"mw-redirect\" title=\"Explorer program\">Explorer program</a>: <i><a href=\"https://wikipedia.org/wiki/Explorer_6\" title=\"Explorer 6\">Explorer 6</a></i> launches from the <a href=\"https://wikipedia.org/wiki/Eastern_Range\" title=\"Eastern Range\">Atlantic Missile Range</a> in <a href=\"https://wikipedia.org/wiki/Cape_Canaveral,_Florida\" title=\"Cape Canaveral, Florida\">Cape Canaveral, Florida</a>.", "links": [{"title": "Explorer program", "link": "https://wikipedia.org/wiki/Explorer_program"}, {"title": "Explorer 6", "link": "https://wikipedia.org/wiki/Explorer_6"}, {"title": "Eastern Range", "link": "https://wikipedia.org/wiki/Eastern_Range"}, {"title": "Cape Canaveral, Florida", "link": "https://wikipedia.org/wiki/Cape_Canaveral,_Florida"}]}, {"year": "1960", "text": "Ivory Coast becomes independent from France.", "html": "1960 - <a href=\"https://wikipedia.org/wiki/Ivory_Coast\" title=\"Ivory Coast\">Ivory Coast</a> becomes independent from France.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Ivory_Coast\" title=\"Ivory Coast\">Ivory Coast</a> becomes independent from France.", "links": [{"title": "Ivory Coast", "link": "https://wikipedia.org/wiki/Ivory_Coast"}]}, {"year": "1962", "text": "Canadian-born American pharmacologist <PERSON> is awarded the U.S. President's Award for Distinguished Federal Civilian Service for her refusal to authorize thalidomide.", "html": "1962 - Canadian-born American pharmacologist <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> is awarded the U.S. President's Award for Distinguished Federal Civilian Service for her refusal to authorize <a href=\"https://wikipedia.org/wiki/Thalidomide\" title=\"Thalidomide\">thalidomide</a>.", "no_year_html": "Canadian-born American pharmacologist <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> is awarded the U.S. President's Award for Distinguished Federal Civilian Service for her refusal to authorize <a href=\"https://wikipedia.org/wiki/Thalidomide\" title=\"Thalidomide\">thalidomide</a>.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "Thalidomide", "link": "https://wikipedia.org/wiki/Thalidomide"}]}, {"year": "1964", "text": "Vietnam War: The U.S. Congress passes the Gulf of Tonkin Resolution giving U.S. President <PERSON> broad war powers to deal with North Vietnamese attacks on American forces.", "html": "1964 - <a href=\"https://wikipedia.org/wiki/Vietnam_War\" title=\"Vietnam War\">Vietnam War</a>: The <a href=\"https://wikipedia.org/wiki/United_States_Congress\" title=\"United States Congress\">U.S. Congress</a> passes the <a href=\"https://wikipedia.org/wiki/Gulf_of_Tonkin_Resolution\" title=\"Gulf of Tonkin Resolution\">Gulf of Tonkin Resolution</a> giving U.S. President <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> broad war powers to deal with <a href=\"https://wikipedia.org/wiki/North_Vietnam\" title=\"North Vietnam\">North Vietnamese</a> attacks on American forces.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Vietnam_War\" title=\"Vietnam War\">Vietnam War</a>: The <a href=\"https://wikipedia.org/wiki/United_States_Congress\" title=\"United States Congress\">U.S. Congress</a> passes the <a href=\"https://wikipedia.org/wiki/Gulf_of_Tonkin_Resolution\" title=\"Gulf of Tonkin Resolution\">Gulf of Tonkin Resolution</a> giving U.S. President <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> broad war powers to deal with <a href=\"https://wikipedia.org/wiki/North_Vietnam\" title=\"North Vietnam\">North Vietnamese</a> attacks on American forces.", "links": [{"title": "Vietnam War", "link": "https://wikipedia.org/wiki/Vietnam_War"}, {"title": "United States Congress", "link": "https://wikipedia.org/wiki/United_States_Congress"}, {"title": "Gulf of Tonkin Resolution", "link": "https://wikipedia.org/wiki/Gulf_of_Tonkin_Resolution"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>"}, {"title": "North Vietnam", "link": "https://wikipedia.org/wiki/North_Vietnam"}]}, {"year": "1969", "text": "<PERSON> appoints <PERSON>, a Mohawk-Oglala Sioux and co-founder of the National Congress of American Indians, as the new commissioner of the Bureau of Indian Affairs.", "html": "1969 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> appoints <PERSON>, a <a href=\"https://wikipedia.org/wiki/Mohawk_people\" title=\"Mohawk people\">Mohawk</a>-<a href=\"https://wikipedia.org/wiki/Oglala_Lakota\" class=\"mw-redirect\" title=\"Oglala Lakota\">Oglala</a> Sioux and co-founder of the <a href=\"https://wikipedia.org/wiki/National_Congress_of_American_Indians\" title=\"National Congress of American Indians\">National Congress of American Indians</a>, as the new commissioner of the <a href=\"https://wikipedia.org/wiki/Bureau_of_Indian_Affairs\" title=\"Bureau of Indian Affairs\">Bureau of Indian Affairs</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> appoints <PERSON>, a <a href=\"https://wikipedia.org/wiki/Mohawk_people\" title=\"Mohawk people\">Mohawk</a>-<a href=\"https://wikipedia.org/wiki/Oglala_Lakota\" class=\"mw-redirect\" title=\"Oglala Lakota\">Oglala</a> Sioux and co-founder of the <a href=\"https://wikipedia.org/wiki/National_Congress_of_American_Indians\" title=\"National Congress of American Indians\">National Congress of American Indians</a>, as the new commissioner of the <a href=\"https://wikipedia.org/wiki/Bureau_of_Indian_Affairs\" title=\"Bureau of Indian Affairs\">Bureau of Indian Affairs</a>.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Mohawk people", "link": "https://wikipedia.org/wiki/Mohawk_people"}, {"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Oglala_Lakota"}, {"title": "National Congress of American Indians", "link": "https://wikipedia.org/wiki/National_Congress_of_American_Indians"}, {"title": "Bureau of Indian Affairs", "link": "https://wikipedia.org/wiki/Bureau_of_Indian_Affairs"}]}, {"year": "1970", "text": "California judge <PERSON> is taken hostage in his courtroom and killed during an effort to free <PERSON> from police custody.", "html": "1970 - California judge <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> is taken hostage in his courtroom and killed during an effort to free <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(Black_Panther)\" class=\"mw-redirect\" title=\"<PERSON> (Black Panther)\"><PERSON></a> from police custody.", "no_year_html": "California judge <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> is taken hostage in his courtroom and killed during an effort to free <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(Black_Panther)\" class=\"mw-redirect\" title=\"<PERSON> (Black Panther)\"><PERSON></a> from police custody.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "<PERSON> (Black Panther)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(Black_Panther)"}]}, {"year": "1974", "text": "<PERSON> performs a high wire act between the twin towers of the World Trade Center 1,368 feet (417 m) in the air.", "html": "1974 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> performs a high wire act between the twin towers of the <a href=\"https://wikipedia.org/wiki/World_Trade_Center_(1973-2001)\" class=\"mw-redirect\" title=\"World Trade Center (1973-2001)\">World Trade Center</a> 1,368 feet (417 m) in the air.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> performs a high wire act between the twin towers of the <a href=\"https://wikipedia.org/wiki/World_Trade_Center_(1973-2001)\" class=\"mw-redirect\" title=\"World Trade Center (1973-2001)\">World Trade Center</a> 1,368 feet (417 m) in the air.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}, {"title": "World Trade Center (1973-2001)", "link": "https://wikipedia.org/wiki/World_Trade_Center_(1973-2001)"}]}, {"year": "1976", "text": "Viking program: Viking 2 enters orbit around Mars.", "html": "1976 - <a href=\"https://wikipedia.org/wiki/Viking_program\" title=\"Viking program\">Viking program</a>: <i><a href=\"https://wikipedia.org/wiki/Viking_2\" title=\"Viking 2\">Viking 2</a></i> enters orbit around <a href=\"https://wikipedia.org/wiki/Mars\" title=\"Mars\">Mars</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Viking_program\" title=\"Viking program\">Viking program</a>: <i><a href=\"https://wikipedia.org/wiki/Viking_2\" title=\"Viking 2\">Viking 2</a></i> enters orbit around <a href=\"https://wikipedia.org/wiki/Mars\" title=\"Mars\">Mars</a>.", "links": [{"title": "Viking program", "link": "https://wikipedia.org/wiki/Viking_program"}, {"title": "Viking 2", "link": "https://wikipedia.org/wiki/Viking_2"}, {"title": "Mars", "link": "https://wikipedia.org/wiki/Mars"}]}, {"year": "1978", "text": "U.S. President <PERSON> declares a federal emergency at Love Canal due to toxic waste that had been disposed of negligently.", "html": "1978 - U.S. President <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> declares a federal emergency at <a href=\"https://wikipedia.org/wiki/Love_Canal\" title=\"Love Canal\">Love Canal</a> due to <a href=\"https://wikipedia.org/wiki/Toxic_waste\" title=\"Toxic waste\">toxic waste</a> that had been disposed of negligently.", "no_year_html": "U.S. President <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> declares a federal emergency at <a href=\"https://wikipedia.org/wiki/Love_Canal\" title=\"Love Canal\">Love Canal</a> due to <a href=\"https://wikipedia.org/wiki/Toxic_waste\" title=\"Toxic waste\">toxic waste</a> that had been disposed of negligently.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Love Canal", "link": "https://wikipedia.org/wiki/Love_Canal"}, {"title": "Toxic waste", "link": "https://wikipedia.org/wiki/Toxic_waste"}]}, {"year": "1981", "text": "The Washington Star ceases all operations after 128 years of publication.", "html": "1981 - <i><a href=\"https://wikipedia.org/wiki/The_Washington_Star\" title=\"The Washington Star\">The Washington Star</a></i> ceases all operations after 128 years of publication.", "no_year_html": "<i><a href=\"https://wikipedia.org/wiki/The_Washington_Star\" title=\"The Washington Star\">The Washington Star</a></i> ceases all operations after 128 years of publication.", "links": [{"title": "The Washington Star", "link": "https://wikipedia.org/wiki/The_Washington_Star"}]}, {"year": "1985", "text": "<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON> and <PERSON><PERSON> are chosen to be Japan's first astronauts.", "html": "1985 - <a href=\"https://wikipedia.org/wiki/Takao_Do<PERSON>\" title=\"Takao Doi\"><PERSON><PERSON><PERSON></a>, <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a> and <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a> are chosen to be Japan's first <a href=\"https://wikipedia.org/wiki/Astronaut\" title=\"Astronaut\">astronauts</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>ka<PERSON>_<PERSON><PERSON>\" title=\"Takao Doi\"><PERSON><PERSON><PERSON></a>, <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a> and <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a> are chosen to be Japan's first <a href=\"https://wikipedia.org/wiki/Astronaut\" title=\"Astronaut\">astronauts</a>.", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Takao_Doi"}, {"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>i"}, {"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}, {"title": "Astronaut", "link": "https://wikipedia.org/wiki/Astronaut"}]}, {"year": "1987", "text": "Cold War: <PERSON><PERSON> becomes the first person to swim from the United States to the Soviet Union, crossing the Bering Strait from Little Diomede Island in Alaska to Big Diomede in the Soviet Union.", "html": "1987 - <a href=\"https://wikipedia.org/wiki/Cold_War\" title=\"Cold War\">Cold War</a>: <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a> becomes the first person to swim from the United States to the <a href=\"https://wikipedia.org/wiki/Soviet_Union\" title=\"Soviet Union\">Soviet Union</a>, crossing the <a href=\"https://wikipedia.org/wiki/Bering_Strait\" title=\"Bering Strait\">Bering Strait</a> from <a href=\"https://wikipedia.org/wiki/Little_Diomede_Island\" title=\"Little Diomede Island\">Little Diomede Island</a> in Alaska to <a href=\"https://wikipedia.org/wiki/Big_Diomede\" class=\"mw-redirect\" title=\"Big Diomede\">Big Diomede</a> in the Soviet Union.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Cold_War\" title=\"Cold War\">Cold War</a>: <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a> becomes the first person to swim from the United States to the <a href=\"https://wikipedia.org/wiki/Soviet_Union\" title=\"Soviet Union\">Soviet Union</a>, crossing the <a href=\"https://wikipedia.org/wiki/Bering_Strait\" title=\"Bering Strait\">Bering Strait</a> from <a href=\"https://wikipedia.org/wiki/Little_Diomede_Island\" title=\"Little Diomede Island\">Little Diomede Island</a> in Alaska to <a href=\"https://wikipedia.org/wiki/Big_Diomede\" class=\"mw-redirect\" title=\"Big Diomede\">Big Diomede</a> in the Soviet Union.", "links": [{"title": "Cold War", "link": "https://wikipedia.org/wiki/Cold_War"}, {"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}, {"title": "Soviet Union", "link": "https://wikipedia.org/wiki/Soviet_Union"}, {"title": "Bering Strait", "link": "https://wikipedia.org/wiki/Bering_Strait"}, {"title": "Little Diomede Island", "link": "https://wikipedia.org/wiki/Little_Diomede_Island"}, {"title": "Big Diomede", "link": "https://wikipedia.org/wiki/Big_Diomede"}]}, {"year": "1989", "text": "U.S. Congressman <PERSON> (D-TX) and 15 others die in a plane crash in Ethiopia.", "html": "1989 - U.S. Congressman <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> (D-<a href=\"https://wikipedia.org/wiki/Texas\" title=\"Texas\">TX</a>) and 15 others die in a <a href=\"https://wikipedia.org/wiki/Aviation_accidents_and_incidents\" title=\"Aviation accidents and incidents\">plane crash</a> in <a href=\"https://wikipedia.org/wiki/Ethiopia\" title=\"Ethiopia\">Ethiopia</a>.", "no_year_html": "U.S. Congressman <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> (D-<a href=\"https://wikipedia.org/wiki/Texas\" title=\"Texas\">TX</a>) and 15 others die in a <a href=\"https://wikipedia.org/wiki/Aviation_accidents_and_incidents\" title=\"Aviation accidents and incidents\">plane crash</a> in <a href=\"https://wikipedia.org/wiki/Ethiopia\" title=\"Ethiopia\">Ethiopia</a>.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Texas", "link": "https://wikipedia.org/wiki/Texas"}, {"title": "Aviation accidents and incidents", "link": "https://wikipedia.org/wiki/Aviation_accidents_and_incidents"}, {"title": "Ethiopia", "link": "https://wikipedia.org/wiki/Ethiopia"}]}, {"year": "1990", "text": "First American soldiers arrive in Saudi Arabia as part of the Gulf War.", "html": "1990 - First American soldiers arrive in Saudi Arabia as part of the <a href=\"https://wikipedia.org/wiki/Gulf_War\" title=\"Gulf War\">Gulf War</a>.", "no_year_html": "First American soldiers arrive in Saudi Arabia as part of the <a href=\"https://wikipedia.org/wiki/Gulf_War\" title=\"Gulf War\">Gulf War</a>.", "links": [{"title": "Gulf War", "link": "https://wikipedia.org/wiki/Gulf_War"}]}, {"year": "1993", "text": "<PERSON>, a Menominee activist, is sworn in as the head of the Bureau of Indian Affairs.", "html": "1993 - <a href=\"https://wikipedia.org/wiki/<PERSON>_Deer\" title=\"Ada Deer\"><PERSON> Deer</a>, a <a href=\"https://wikipedia.org/wiki/Menominee\" title=\"Menominee\">Menominee</a> activist, is sworn in as the head of the <a href=\"https://wikipedia.org/wiki/Bureau_of_Indian_Affairs\" title=\"Bureau of Indian Affairs\">Bureau of Indian Affairs</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Ada_Deer\" title=\"Ada Deer\"><PERSON></a>, a <a href=\"https://wikipedia.org/wiki/Menominee\" title=\"Menominee\">Menominee</a> activist, is sworn in as the head of the <a href=\"https://wikipedia.org/wiki/Bureau_of_Indian_Affairs\" title=\"Bureau of Indian Affairs\">Bureau of Indian Affairs</a>.", "links": [{"title": "Ada Deer", "link": "https://wikipedia.org/wiki/<PERSON>_Deer"}, {"title": "Menominee", "link": "https://wikipedia.org/wiki/Menominee"}, {"title": "Bureau of Indian Affairs", "link": "https://wikipedia.org/wiki/Bureau_of_Indian_Affairs"}]}, {"year": "1995", "text": "The Chilean government declares state of emergency in the southern half of the country in response to an event of intense, cold, wind, rain and snowfall known as the White Earthquake.", "html": "1995 - The Chilean government declares <a href=\"https://wikipedia.org/wiki/State_of_emergency\" title=\"State of emergency\">state of emergency</a> in the southern half of the country in response to an event of intense, cold, wind, rain and snowfall known as the <a href=\"https://wikipedia.org/wiki/White_Earthquake\" title=\"White Earthquake\">White Earthquake</a>.", "no_year_html": "The Chilean government declares <a href=\"https://wikipedia.org/wiki/State_of_emergency\" title=\"State of emergency\">state of emergency</a> in the southern half of the country in response to an event of intense, cold, wind, rain and snowfall known as the <a href=\"https://wikipedia.org/wiki/White_Earthquake\" title=\"White Earthquake\">White Earthquake</a>.", "links": [{"title": "State of emergency", "link": "https://wikipedia.org/wiki/State_of_emergency"}, {"title": "White Earthquake", "link": "https://wikipedia.org/wiki/White_Earthquake"}]}, {"year": "1997", "text": "Space Shuttle Program: The Space Shuttle Discovery launches on STS-85 from the Kennedy Space Center in Cape Canaveral, Florida.", "html": "1997 - <a href=\"https://wikipedia.org/wiki/Space_Shuttle_program\" title=\"Space Shuttle program\">Space Shuttle Program</a>: The <a href=\"https://wikipedia.org/wiki/Space_Shuttle\" title=\"Space Shuttle\">Space Shuttle</a> <i><a href=\"https://wikipedia.org/wiki/Space_Shuttle_Discovery\" title=\"Space Shuttle Discovery\">Discovery</a></i> launches on <a href=\"https://wikipedia.org/wiki/STS-85\" title=\"STS-85\">STS-85</a> from the <a href=\"https://wikipedia.org/wiki/Kennedy_Space_Center\" title=\"Kennedy Space Center\">Kennedy Space Center</a> in Cape Canaveral, Florida.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Space_Shuttle_program\" title=\"Space Shuttle program\">Space Shuttle Program</a>: The <a href=\"https://wikipedia.org/wiki/Space_Shuttle\" title=\"Space Shuttle\">Space Shuttle</a> <i><a href=\"https://wikipedia.org/wiki/Space_Shuttle_Discovery\" title=\"Space Shuttle Discovery\">Discovery</a></i> launches on <a href=\"https://wikipedia.org/wiki/STS-85\" title=\"STS-85\">STS-85</a> from the <a href=\"https://wikipedia.org/wiki/Kennedy_Space_Center\" title=\"Kennedy Space Center\">Kennedy Space Center</a> in Cape Canaveral, Florida.", "links": [{"title": "Space Shuttle program", "link": "https://wikipedia.org/wiki/Space_Shuttle_program"}, {"title": "Space Shuttle", "link": "https://wikipedia.org/wiki/Space_Shuttle"}, {"title": "Space Shuttle Discovery", "link": "https://wikipedia.org/wiki/Space_Shuttle_Discovery"}, {"title": "STS-85", "link": "https://wikipedia.org/wiki/STS-85"}, {"title": "Kennedy Space Center", "link": "https://wikipedia.org/wiki/Kennedy_Space_Center"}]}, {"year": "1997", "text": "Fine Air Flight 101 crashes after takeoff from Miami International Airport, killing five people.", "html": "1997 - <a href=\"https://wikipedia.org/wiki/Fine_Air_Flight_101\" title=\"Fine Air Flight 101\">Fine Air Flight 101</a> crashes after takeoff from <a href=\"https://wikipedia.org/wiki/Miami_International_Airport\" title=\"Miami International Airport\">Miami International Airport</a>, killing five people.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Fine_Air_Flight_101\" title=\"Fine Air Flight 101\">Fine Air Flight 101</a> crashes after takeoff from <a href=\"https://wikipedia.org/wiki/Miami_International_Airport\" title=\"Miami International Airport\">Miami International Airport</a>, killing five people.", "links": [{"title": "Fine Air Flight 101", "link": "https://wikipedia.org/wiki/Fine_Air_Flight_101"}, {"title": "Miami International Airport", "link": "https://wikipedia.org/wiki/Miami_International_Airport"}]}, {"year": "1998", "text": "Bombings at United States embassies in Dar es Salaam, Tanzania and Nairobi, Kenya kill approximately 212 people.", "html": "1998 - <a href=\"https://wikipedia.org/wiki/1998_United_States_embassy_bombings\" title=\"1998 United States embassy bombings\">Bombings at United States embassies</a> in <a href=\"https://wikipedia.org/wiki/Dar_es_Salaam\" title=\"Dar es Salaam\">Dar es Salaam, Tanzania</a> and <a href=\"https://wikipedia.org/wiki/Nairobi\" title=\"Nairobi\">Nairobi, Kenya</a> kill approximately 212 people.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/1998_United_States_embassy_bombings\" title=\"1998 United States embassy bombings\">Bombings at United States embassies</a> in <a href=\"https://wikipedia.org/wiki/Dar_es_Salaam\" title=\"Dar es Salaam\">Dar es Salaam, Tanzania</a> and <a href=\"https://wikipedia.org/wiki/Nairobi\" title=\"Nairobi\">Nairobi, Kenya</a> kill approximately 212 people.", "links": [{"title": "1998 United States embassy bombings", "link": "https://wikipedia.org/wiki/1998_United_States_embassy_bombings"}, {"title": "Dar es Salaam", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "Nairobi", "link": "https://wikipedia.org/wiki/Nairobi"}]}, {"year": "1999", "text": "The Chechnya-based Islamic International Brigade invades neighboring Dagestan.", "html": "1999 - The <a href=\"https://wikipedia.org/wiki/Chechnya\" title=\"Chechnya\">Chechnya</a>-based <a href=\"https://wikipedia.org/wiki/Islamic_International_Brigade\" class=\"mw-redirect\" title=\"Islamic International Brigade\">Islamic International Brigade</a> <a href=\"https://wikipedia.org/wiki/War_of_Dagestan\" class=\"mw-redirect\" title=\"War of Dagestan\">invades</a> neighboring <a href=\"https://wikipedia.org/wiki/Dagestan\" title=\"Dagestan\">Dagestan</a>.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Chechnya\" title=\"Chechnya\">Chechnya</a>-based <a href=\"https://wikipedia.org/wiki/Islamic_International_Brigade\" class=\"mw-redirect\" title=\"Islamic International Brigade\">Islamic International Brigade</a> <a href=\"https://wikipedia.org/wiki/War_of_Dagestan\" class=\"mw-redirect\" title=\"War of Dagestan\">invades</a> neighboring <a href=\"https://wikipedia.org/wiki/Dagestan\" title=\"Dagestan\">Dagestan</a>.", "links": [{"title": "Chechnya", "link": "https://wikipedia.org/wiki/Chechnya"}, {"title": "Islamic International Brigade", "link": "https://wikipedia.org/wiki/Islamic_International_Brigade"}, {"title": "War of Dagestan", "link": "https://wikipedia.org/wiki/War_of_Dagestan"}, {"title": "Dagestan", "link": "https://wikipedia.org/wiki/Dagestan"}]}, {"year": "2007", "text": "At AT&T Park, <PERSON> hits his 756th career home run to surpass <PERSON>'s 33-year-old record.", "html": "2007 - At <a href=\"https://wikipedia.org/wiki/AT%26T_Park\" class=\"mw-redirect\" title=\"AT&amp;T Park\">AT&amp;T Park</a>, <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> hits his 756th career <a href=\"https://wikipedia.org/wiki/Home_run\" title=\"Home run\">home run</a> to surpass <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>'s 33-year-old record.", "no_year_html": "At <a href=\"https://wikipedia.org/wiki/AT%26T_Park\" class=\"mw-redirect\" title=\"AT&amp;T Park\">AT&amp;T Park</a>, <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> hits his 756th career <a href=\"https://wikipedia.org/wiki/Home_run\" title=\"Home run\">home run</a> to surpass <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>'s 33-year-old record.", "links": [{"title": "AT&T Park", "link": "https://wikipedia.org/wiki/AT%26T_Park"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Home run", "link": "https://wikipedia.org/wiki/Home_run"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2008", "text": "The start of the Russo-Georgian War over the territory of South Ossetia.", "html": "2008 - The start of the <a href=\"https://wikipedia.org/wiki/Russo-Georgian_War\" title=\"Russo-Georgian War\">Russo-Georgian War</a> over the territory of <a href=\"https://wikipedia.org/wiki/South_Ossetia\" title=\"South Ossetia\">South Ossetia</a>.", "no_year_html": "The start of the <a href=\"https://wikipedia.org/wiki/Russo-Georgian_War\" title=\"Russo-Georgian War\">Russo-Georgian War</a> over the territory of <a href=\"https://wikipedia.org/wiki/South_Ossetia\" title=\"South Ossetia\">South Ossetia</a>.", "links": [{"title": "Russo-Georgian War", "link": "https://wikipedia.org/wiki/Russo-Georgian_War"}, {"title": "South Ossetia", "link": "https://wikipedia.org/wiki/South_Ossetia"}]}, {"year": "2020", "text": "Air India Express Flight 1344 overshoots the runway at Calicut International Airport in the Malappuram district of Kerala, India, and crashes, killing 21 of the 190 people on board.", "html": "2020 - <a href=\"https://wikipedia.org/wiki/Air_India_Express_Flight_1344\" title=\"Air India Express Flight 1344\">Air India Express Flight 1344</a> overshoots the runway at <a href=\"https://wikipedia.org/wiki/Calicut_International_Airport\" class=\"mw-redirect\" title=\"Calicut International Airport\">Calicut International Airport</a> in the <a href=\"https://wikipedia.org/wiki/Malappuram_district\" title=\"Malappuram district\">Malappuram district</a> of <a href=\"https://wikipedia.org/wiki/Kerala\" title=\"Kerala\">Kerala</a>, <a href=\"https://wikipedia.org/wiki/India\" title=\"India\">India</a>, and crashes, killing 21 of the 190 people on board.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Air_India_Express_Flight_1344\" title=\"Air India Express Flight 1344\">Air India Express Flight 1344</a> overshoots the runway at <a href=\"https://wikipedia.org/wiki/Calicut_International_Airport\" class=\"mw-redirect\" title=\"Calicut International Airport\">Calicut International Airport</a> in the <a href=\"https://wikipedia.org/wiki/Malappuram_district\" title=\"Malappuram district\">Malappuram district</a> of <a href=\"https://wikipedia.org/wiki/Kerala\" title=\"Kerala\">Kerala</a>, <a href=\"https://wikipedia.org/wiki/India\" title=\"India\">India</a>, and crashes, killing 21 of the 190 people on board.", "links": [{"title": "Air India Express Flight 1344", "link": "https://wikipedia.org/wiki/Air_India_Express_Flight_1344"}, {"title": "Calicut International Airport", "link": "https://wikipedia.org/wiki/Calicut_International_Airport"}, {"title": "Malappuram district", "link": "https://wikipedia.org/wiki/Malappuram_district"}, {"title": "Kerala", "link": "https://wikipedia.org/wiki/Kerala"}, {"title": "India", "link": "https://wikipedia.org/wiki/India"}]}], "Births": [{"year": "317", "text": "<PERSON><PERSON><PERSON>, Roman emperor (d. 361)", "html": "317 - <a href=\"https://wikipedia.org/wiki/Constantius_II\" title=\"Constantius II\"><PERSON><PERSON><PERSON> II</a>, Roman emperor (d. 361)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Con<PERSON>ius_II\" title=\"Constantius II\"><PERSON><PERSON><PERSON> II</a>, Roman emperor (d. 361)", "links": [{"title": "<PERSON><PERSON><PERSON> II", "link": "https://wikipedia.org/wiki/Constantius_II"}]}, {"year": "1282", "text": "<PERSON> Rhuddlan (d. 1316)", "html": "1282 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_Rhuddlan\" title=\"<PERSON> of Rhuddlan\"><PERSON> of Rhuddlan</a> (d. 1316)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_Rhuddlan\" title=\"<PERSON> of Rhuddlan\"><PERSON> of Rhuddlan</a> (d. 1316)", "links": [{"title": "<PERSON> of Rhuddlan", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON><PERSON><PERSON>"}]}, {"year": "1533", "text": "<PERSON>, Spanish soldier and poet (d. 1595)", "html": "1533 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Spanish soldier and poet (d. 1595)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Spanish soldier and poet (d. 1595)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1560", "text": "<PERSON>, Hungarian aristocrat and purported serial killer (d. 1614)", "html": "1560 - <a href=\"https://wikipedia.org/wiki/Elizabeth_B%C3%A1thory\" title=\"<PERSON>\"><PERSON></a>, Hungarian aristocrat and purported serial killer (d. 1614)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_B%C3%A1thory\" title=\"<PERSON>\"><PERSON></a>, Hungarian aristocrat and purported serial killer (d. 1614)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Elizabeth_B%C3%A1thory"}]}, {"year": "1571", "text": "<PERSON>, English viol player and composer (d. 1627)", "html": "1571 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_the_elder\" title=\"<PERSON> the elder\"><PERSON></a>, English <a href=\"https://wikipedia.org/wiki/Viol\" title=\"Viol\">viol</a> player and composer (d. 1627)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_the_elder\" title=\"<PERSON> the elder\"><PERSON></a>, English <a href=\"https://wikipedia.org/wiki/Viol\" title=\"Viol\">viol</a> player and composer (d. 1627)", "links": [{"title": "<PERSON> the elder", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_the_elder"}, {"title": "Viol", "link": "https://wikipedia.org/wiki/Viol"}]}, {"year": "1574", "text": "<PERSON>, English explorer and cartographer (d. 1649)", "html": "1574 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(explorer)\" title=\"<PERSON> (explorer)\"><PERSON></a>, English explorer and cartographer (d. 1649)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(explorer)\" title=\"<PERSON> (explorer)\"><PERSON></a>, English explorer and cartographer (d. 1649)", "links": [{"title": "<PERSON> (explorer)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>(explorer)"}]}, {"year": "1598", "text": "<PERSON>, Swedish poet and linguist (d. 1672)", "html": "1598 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swedish poet and linguist (d. 1672)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swedish poet and linguist (d. 1672)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>nhielm"}]}, {"year": "1613", "text": "<PERSON>, Prince of Nassau-Dietz, Dutch stadtholder (d. 1664)", "html": "1613 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Prince_of_Nassau-Dietz\" title=\"<PERSON>, Prince of Nassau-Dietz\"><PERSON>, Prince of Nassau-Dietz</a>, Dutch stadtholder (d. 1664)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Prince_of_Nassau-Dietz\" title=\"<PERSON>, Prince of Nassau-Dietz\"><PERSON>, Prince of Nassau-Dietz</a>, Dutch stadtholder (d. 1664)", "links": [{"title": "<PERSON>, Prince of Nassau-Dietz", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Prince_of_Nassau-Dietz"}]}, {"year": "1702", "text": "<PERSON>, Mughal emperor of India (d. 1748)", "html": "1702 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Mughal emperor of India (d. 1748)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Mughal emperor of India (d. 1748)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1726", "text": "<PERSON>, American banker and politician, 2nd Governor of Massachusetts (d. 1790)", "html": "1726 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American banker and politician, 2nd <a href=\"https://wikipedia.org/wiki/Governor_of_Massachusetts\" title=\"Governor of Massachusetts\">Governor of Massachusetts</a> (d. 1790)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American banker and politician, 2nd <a href=\"https://wikipedia.org/wiki/Governor_of_Massachusetts\" title=\"Governor of Massachusetts\">Governor of Massachusetts</a> (d. 1790)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Governor of Massachusetts", "link": "https://wikipedia.org/wiki/Governor_of_Massachusetts"}]}, {"year": "1742", "text": "<PERSON><PERSON>, American general (d. 1786)", "html": "1742 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American general (d. 1786)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American general (d. 1786)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1751", "text": "<PERSON><PERSON> of Prussia, Princess of Orange (d. 1820)", "html": "1751 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_of_Prussia,_Princess_of_Orange\" title=\"<PERSON><PERSON> of Prussia, Princess of Orange\"><PERSON><PERSON> of Prussia, Princess of Orange</a> (d. 1820)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_of_Prussia,_Princess_of_Orange\" title=\"<PERSON><PERSON> of Prussia, Princess of Orange\"><PERSON><PERSON> of Prussia, Princess of Orange</a> (d. 1820)", "links": [{"title": "<PERSON><PERSON> of Prussia, Princess of Orange", "link": "https://wikipedia.org/wiki/<PERSON>ina_of_Prussia,_Princess_of_Orange"}]}, {"year": "1779", "text": "<PERSON>, German geographer and academic (d. 1859)", "html": "1779 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German geographer and academic (d. 1859)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German geographer and academic (d. 1859)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1826", "text": "<PERSON>, Finnish professor, poet, scholar of the Finno-Ugric languages, author, and literary critic (d. 1889)", "html": "1826 - <a href=\"https://wikipedia.org/wiki/August_<PERSON>\" title=\"August <PERSON>\">August <PERSON></a>, Finnish professor, poet, scholar of the <a href=\"https://wikipedia.org/wiki/Finno-Ugric_languages\" title=\"Finno-Ugric languages\">Finno-Ugric languages</a>, author, and literary critic (d. 1889)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/August_<PERSON>\" title=\"August <PERSON>\">August <PERSON></a>, Finnish professor, poet, scholar of the <a href=\"https://wikipedia.org/wiki/Finno-Ugric_languages\" title=\"Finno-Ugric languages\">Finno-Ugric languages</a>, author, and literary critic (d. 1889)", "links": [{"title": "August <PERSON>", "link": "https://wikipedia.org/wiki/August_<PERSON><PERSON><PERSON><PERSON>"}, {"title": "Finno-Ugric languages", "link": "https://wikipedia.org/wiki/Finno-Ugric_languages"}]}, {"year": "1844", "text": "<PERSON>, French geologist and author (d. 1911)", "html": "1844 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>-<PERSON>%C3%A9vy\" title=\"<PERSON>\"><PERSON></a>, French geologist and author (d. 1911)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>-<PERSON>%C3%A9vy\" title=\"<PERSON>\"><PERSON></a>, French geologist and author (d. 1911)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>-<PERSON>%C3%A9vy"}]}, {"year": "1862", "text": "<PERSON>, French painter (d. 1939)", "html": "1862 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French painter (d. 1939)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French painter (d. 1939)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1862", "text": "<PERSON> of Baden (d. 1931)", "html": "1862 - <a href=\"https://wikipedia.org/wiki/Victoria_of_Baden\" title=\"Victoria of Baden\">Victoria of Baden</a> (d. 1931)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Victoria_of_Baden\" title=\"Victoria of Baden\">Victoria of Baden</a> (d. 1931)", "links": [{"title": "Victoria of Baden", "link": "https://wikipedia.org/wiki/Victoria_of_Baden"}]}, {"year": "1867", "text": "<PERSON>, Danish-German painter and illustrator (d. 1956)", "html": "1867 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Danish-German painter and illustrator (d. 1956)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Danish-German painter and illustrator (d. 1956)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1868", "text": "<PERSON><PERSON><PERSON>, Russian-German economist and statistician (d. 1931)", "html": "1868 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Russian-German economist and statistician (d. 1931)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Russian-German economist and statistician (d. 1931)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1868", "text": "<PERSON><PERSON>, English actor (d. 1941)", "html": "1868 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, English actor (d. 1941)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, English actor (d. 1941)", "links": [{"title": "<PERSON><PERSON> Wright", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1869", "text": "<PERSON>,  American mathematician (d. 1959)", "html": "1869 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American mathematician (d. 1959)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American mathematician (d. 1959)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1876", "text": "<PERSON>, Dutch dancer and spy (d. 1917)", "html": "1876 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Mata Hari\"><PERSON></a>, Dutch dancer and spy (d. 1917)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Mata Hari\"><PERSON></a>, Dutch dancer and spy (d. 1917)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Mata_Hari"}]}, {"year": "1879", "text": "<PERSON>, South African cricketer (d. 1931)", "html": "1879 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, South African cricketer (d. 1931)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, South African cricketer (d. 1931)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1884", "text": "<PERSON>, American actress and singer (d. 1970)", "html": "1884 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress and singer (d. 1970)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress and singer (d. 1970)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1884", "text": "<PERSON>, Estonian painter and illustrator (d. 1940)", "html": "1884 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Estonian painter and illustrator (d. 1940)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Estonian painter and illustrator (d. 1940)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1887", "text": "<PERSON>, German author and playwright (d. 1970)", "html": "1887 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>\" title=\"<PERSON>\"><PERSON></a>, German author and playwright (d. 1970)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>\" title=\"<PERSON>\"><PERSON></a>, German author and playwright (d. 1970)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1890", "text": "<PERSON>, American author and activist (d. 1964)", "html": "1890 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author and activist (d. 1964)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author and activist (d. 1964)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1901", "text": "<PERSON>, American actress and singer (d. 1981)", "html": "1901 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress and singer (d. 1981)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress and singer (d. 1981)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1903", "text": "<PERSON>, Kenyan-English palaeontologist and archaeologist (d. 1972)", "html": "1903 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Kenyan-English palaeontologist and archaeologist (d. 1972)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Kenyan-English palaeontologist and archaeologist (d. 1972)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1904", "text": "<PERSON>, American political scientist, academic, and diplomat, Nobel Prize laureate (d. 1971)", "html": "1904 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American political scientist, academic, and diplomat, <a href=\"https://wikipedia.org/wiki/Nobel_Peace_Prize\" title=\"Nobel Peace Prize\">Nobel Prize</a> laureate (d. 1971)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American political scientist, academic, and diplomat, <a href=\"https://wikipedia.org/wiki/Nobel_Peace_Prize\" title=\"Nobel Peace Prize\">Nobel Prize</a> laureate (d. 1971)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Nobel Peace Prize", "link": "https://wikipedia.org/wiki/Nobel_Peace_Prize"}]}, {"year": "1907", "text": "<PERSON>, Belarusian-American soldier and painter (d. 1980)", "html": "1907 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Belarusian-American soldier and painter (d. 1980)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Belarusian-American soldier and painter (d. 1980)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1911", "text": "<PERSON><PERSON><PERSON>, Hungarian lawyer and politician (d. 1979)", "html": "1911 - <a href=\"https://wikipedia.org/wiki/Istv%C3%A1n_Bib%C3%B3\" title=\"Ist<PERSON> Bibó\"><PERSON><PERSON><PERSON></a>, Hungarian lawyer and politician (d. 1979)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Istv%C3%A1n_Bib%C3%B3\" title=\"<PERSON>t<PERSON> Bibó\"><PERSON><PERSON><PERSON></a>, Hungarian lawyer and politician (d. 1979)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Istv%C3%A1n_Bib%C3%B3"}]}, {"year": "1911", "text": "<PERSON>, American director and screenwriter (d. 1979)", "html": "1911 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American director and screenwriter (d. 1979)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American director and screenwriter (d. 1979)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1916", "text": "<PERSON><PERSON><PERSON>,  American actor, puppeteer, and costume designer (d. 2008)", "html": "1916 - <a href=\"https://wikipedia.org/wiki/Kermit_Love\" title=\"Kermit Love\"><PERSON><PERSON><PERSON></a>, American actor, puppeteer, and costume designer (d. 2008)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Kermit_Love\" title=\"Kermit Love\"><PERSON><PERSON><PERSON></a>, American actor, puppeteer, and costume designer (d. 2008)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Kermit_Love"}]}, {"year": "1918", "text": "<PERSON><PERSON>, Dutch poet and translator (d. 1985)", "html": "1918 - <a href=\"https://wikipedia.org/wiki/C._Buddingh%27\" title=\"C<PERSON>'\"><PERSON><PERSON>'</a>, Dutch poet and translator (d. 1985)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/C._Buddingh%27\" title=\"C. <PERSON>'\"><PERSON><PERSON>'</a>, Dutch poet and translator (d. 1985)", "links": [{"title": "<PERSON><PERSON>'", "link": "https://wikipedia.org/wiki/C._Buddingh%27"}]}, {"year": "1918", "text": "<PERSON>, American sociologist and author (d. 2007)", "html": "1918 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American sociologist and author (d. 2007)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American sociologist and author (d. 2007)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1921", "text": "<PERSON><PERSON>, French guitarist (d. 2014)", "html": "1921 - <a href=\"https://wikipedia.org/wiki/Manitas_de_Plata\" title=\"Manitas de Plata\"><PERSON><PERSON> de Plata</a>, French guitarist (d. 2014)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Manitas_de_Plata\" title=\"Manitas de Plata\">Manitas de Plata</a>, French guitarist (d. 2014)", "links": [{"title": "Manitas de Plata", "link": "https://wikipedia.org/wiki/Manitas_de_Plata"}]}, {"year": "1921", "text": "<PERSON><PERSON>, Czech-American composer and conductor (d. 2016)", "html": "1921 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Czech-American composer and conductor (d. 2016)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Czech-American composer and conductor (d. 2016)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1925", "text": "<PERSON><PERSON>, American songwriter (d. 2003)", "html": "1925 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_and_<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> and <PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON></a>, American songwriter (d. 2003)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_and_<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> and <PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON></a>, American songwriter (d. 2003)", "links": [{"title": "<PERSON><PERSON> and <PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_and_<PERSON><PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1926", "text": "<PERSON>, American puppeteer, voice actor, and singer (d. 2015)", "html": "1926 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American puppeteer, voice actor, and singer (d. 2015)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American puppeteer, voice actor, and singer (d. 2015)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>g"}]}, {"year": "1927", "text": "<PERSON>, American soldier, lawyer, and politician, 50th Governor of Louisiana (d. 2021)", "html": "1927 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American soldier, lawyer, and politician, 50th <a href=\"https://wikipedia.org/wiki/Governor_of_Louisiana\" title=\"Governor of Louisiana\">Governor of Louisiana</a> (d. 2021)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American soldier, lawyer, and politician, 50th <a href=\"https://wikipedia.org/wiki/Governor_of_Louisiana\" title=\"Governor of Louisiana\">Governor of Louisiana</a> (d. 2021)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Governor of Louisiana", "link": "https://wikipedia.org/wiki/Governor_of_Louisiana"}]}, {"year": "1928", "text": "<PERSON>, American author and academic (d. 2020)", "html": "1928 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author and academic (d. 2020)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author and academic (d. 2020)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1928", "text": "<PERSON>, English architect, designed Tricorn Centre and Trinity Square (d. 2021)", "html": "1928 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English architect, designed <a href=\"https://wikipedia.org/wiki/Tricorn_Centre\" title=\"Tricorn Centre\">Tricorn Centre</a> and <a href=\"https://wikipedia.org/wiki/Trinity_Square,_Gateshead\" title=\"Trinity Square, Gateshead\">Trinity Square</a> (d. 2021)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English architect, designed <a href=\"https://wikipedia.org/wiki/Tricorn_Centre\" title=\"Tricorn Centre\">Tricorn Centre</a> and <a href=\"https://wikipedia.org/wiki/Trinity_Square,_Gateshead\" title=\"Trinity Square, Gateshead\">Trinity Square</a> (d. 2021)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Tricorn Centre", "link": "https://wikipedia.org/wiki/Tricorn_Centre"}, {"title": "Trinity Square, Gateshead", "link": "https://wikipedia.org/wiki/Trinity_Square,_Gateshead"}]}, {"year": "1928", "text": "<PERSON>, Canadian-American stage magician and author (d. 2020)", "html": "1928 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian-American stage magician and author (d. 2020)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian-American stage magician and author (d. 2020)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1929", "text": "<PERSON>, American baseball player (d. 2020)", "html": "1929 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player (d. 2020)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player (d. 2020)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1930", "text": "<PERSON><PERSON><PERSON><PERSON>, Azerbaijani-French painter and academic (d. 2013)", "html": "1930 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Azerbaijani-French painter and academic (d. 2013)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Azerbaijani-French painter and academic (d. 2013)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON><PERSON><PERSON><PERSON>"}]}, {"year": "1930", "text": "<PERSON><PERSON><PERSON>, Estonian composer and educator (d. 2017)", "html": "1930 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Estonian composer and educator (d. 2017)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Estonian composer and educator (d. 2017)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1932", "text": "<PERSON><PERSON>, Ethiopian runner (d. 1973)", "html": "1932 - <a href=\"https://wikipedia.org/wiki/Abe<PERSON>_Bikila\" title=\"Abebe Bikila\"><PERSON><PERSON></a>, Ethiopian runner (d. 1973)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Abebe_Bikila\" title=\"Abebe Bikila\"><PERSON><PERSON></a>, Ethiopian runner (d. 1973)", "links": [{"title": "Abebe B<PERSON>", "link": "https://wikipedia.org/wiki/Abebe_Bikila"}]}, {"year": "1932", "text": "<PERSON>, English actor (d. 2011)", "html": "1932 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actor (d. 2011)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actor (d. 2011)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1932", "text": "<PERSON><PERSON>, Dutch painter and illustrator (d. 1995)", "html": "1932 - <a href=\"https://wikipedia.org/wiki/Rien_Poortvliet\" title=\"Rien Poortvliet\"><PERSON><PERSON></a>, Dutch painter and illustrator (d. 1995)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>tvlie<PERSON>\" title=\"Rien Poortvlie<PERSON>\"><PERSON><PERSON></a>, Dutch painter and illustrator (d. 1995)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Rien_Poortvliet"}]}, {"year": "1932", "text": "<PERSON>, Jr., American ophthalmologist and academic (d. 2005)", "html": "1932 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Jr.\" class=\"mw-redirect\" title=\"<PERSON>, Jr.\"><PERSON>, Jr.</a>, American ophthalmologist and academic (d. 2005)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Jr.\" class=\"mw-redirect\" title=\"<PERSON>, Jr.\"><PERSON>, Jr.</a>, American ophthalmologist and academic (d. 2005)", "links": [{"title": "<PERSON>, Jr.", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Jr."}]}, {"year": "1933", "text": "<PERSON>, South African footballer and manager", "html": "1933 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, South African footballer and manager", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, South African footballer and manager", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1933", "text": "<PERSON><PERSON>, American economist and academic, Nobel Prize laureate (d. 2012)", "html": "1933 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American economist and academic, <a href=\"https://wikipedia.org/wiki/Nobel_Memorial_Prize_in_Economic_Sciences\" title=\"Nobel Memorial Prize in Economic Sciences\">Nobel Prize</a> laureate (d. 2012)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American economist and academic, <a href=\"https://wikipedia.org/wiki/Nobel_Memorial_Prize_in_Economic_Sciences\" title=\"Nobel Memorial Prize in Economic Sciences\">Nobel Prize</a> laureate (d. 2012)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}, {"title": "Nobel Memorial Prize in Economic Sciences", "link": "https://wikipedia.org/wiki/Nobel_Memorial_Prize_in_Economic_Sciences"}]}, {"year": "1933", "text": "<PERSON>, American journalist and author (d. 2017)", "html": "1933 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American journalist and author (d. 2017)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American journalist and author (d. 2017)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1933", "text": "<PERSON>, Filipino politician and diplomat", "html": "1933 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Filipino politician and diplomat", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Filipino politician and diplomat", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1934", "text": "<PERSON><PERSON><PERSON>, Hungarian director, producer, and screenwriter (d. 2001)", "html": "1934 - <a href=\"https://wikipedia.org/wiki/S%C3%A1ndor_Sim%C3%B3\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Hungarian director, producer, and screenwriter (d. 2001)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/S%C3%A1ndor_Sim%C3%B3\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Hungarian director, producer, and screenwriter (d. 2001)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/S%C3%A1ndor_Sim%C3%B3"}]}, {"year": "1935", "text": "<PERSON>, American college football coach and broadcaster", "html": "1935 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American college football coach and broadcaster", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American college football coach and broadcaster", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1935", "text": "<PERSON><PERSON><PERSON>, American saxophonist and composer (d. 1977)", "html": "1935 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American saxophonist and composer (d. 1977)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American saxophonist and composer (d. 1977)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1937", "text": "<PERSON><PERSON><PERSON>, Hungarian table tennis player and coach (d. 2011)", "html": "1937 - <a href=\"https://wikipedia.org/wiki/Zolt%C3%A1n_Berczik\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Hungarian table tennis player and coach (d. 2011)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Zolt%C3%A1n_Be<PERSON>zik\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Hungarian table tennis player and coach (d. 2011)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Zolt%C3%A1n_Berc<PERSON>k"}]}, {"year": "1937", "text": "<PERSON>, English cricketer and coach (d. 2012)", "html": "1937 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(cricketer)\" title=\"<PERSON> (cricketer)\"><PERSON></a>, English cricketer and coach (d. 2012)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(cricketer)\" title=\"<PERSON> (cricketer)\"><PERSON></a>, English cricketer and coach (d. 2012)", "links": [{"title": "<PERSON> (cricketer)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(cricketer)"}]}, {"year": "1938", "text": "<PERSON><PERSON><PERSON><PERSON>, Italian automotive designer", "html": "1938 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>ro\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Italian automotive designer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>ro\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Italian automotive designer", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Gior<PERSON><PERSON>_<PERSON>"}]}, {"year": "1940", "text": "<PERSON><PERSON><PERSON>, French-Belgian lawyer and politician, 63rd Prime Minister of Belgium (d. 2014)", "html": "1940 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, French-Belgian lawyer and politician, 63rd <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Belgium\" title=\"Prime Minister of Belgium\">Prime Minister of Belgium</a> (d. 2014)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, French-Belgian lawyer and politician, 63rd <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Belgium\" title=\"Prime Minister of Belgium\">Prime Minister of Belgium</a> (d. 2014)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>"}, {"title": "Prime Minister of Belgium", "link": "https://wikipedia.org/wiki/Prime_Minister_of_Belgium"}]}, {"year": "1940", "text": "<PERSON><PERSON>, German record producer, journalist and film critic (d. 2007)", "html": "1940 - <a href=\"https://wikipedia.org/wiki/<PERSON>we_Nettelbeck\" title=\"Uwe Nettelbeck\"><PERSON><PERSON></a>, German record producer, journalist and film critic (d. 2007)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Uwe_Nettelbeck\" title=\"Uwe Nettelbeck\"><PERSON><PERSON></a>, German record producer, journalist and film critic (d. 2007)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Uwe_Nettelbeck"}]}, {"year": "1941", "text": "<PERSON>, <PERSON> of Temple Guiting, English publisher and politician (d. 2016)", "html": "1941 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Baron_<PERSON>_of_Temple_Guiting\" title=\"<PERSON>, Baron <PERSON> of Temple Guiting\"><PERSON>, Baron <PERSON> of Temple Guiting</a>, English publisher and politician (d. 2016)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>,_Baron_<PERSON>_of_Temple_Guiting\" title=\"<PERSON>, Baron <PERSON> of Temple Guiting\"><PERSON>, Baron <PERSON> of Temple Guiting</a>, English publisher and politician (d. 2016)", "links": [{"title": "<PERSON>, <PERSON> of Temple Guiting", "link": "https://wikipedia.org/wiki/<PERSON>,_<PERSON>_<PERSON>_of_Temple_Guiting"}]}, {"year": "1942", "text": "<PERSON>, American humorist, novelist, short story writer, and radio host", "html": "1942 - <a href=\"https://wikipedia.org/wiki/Garrison_Ke<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American humorist, novelist, short story writer, and radio host", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American humorist, novelist, short story writer, and radio host", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Garrison_<PERSON>"}]}, {"year": "1942", "text": "<PERSON>, Argentinian boxer and actor (d. 1995)", "html": "1942 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%B3n\" title=\"<PERSON>\"><PERSON></a>, Argentinian boxer and actor (d. 1995)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%B3n\" title=\"<PERSON>\"><PERSON></a>, Argentinian boxer and actor (d. 1995)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Carlos_Monz%C3%B3n"}]}, {"year": "1942", "text": "<PERSON><PERSON><PERSON>, Brazilian singer-songwriter, writer and producer", "html": "1942 - <a href=\"https://wikipedia.org/wiki/Cae<PERSON>_Veloso\" title=\"Cae<PERSON> Veloso\"><PERSON><PERSON><PERSON></a>, Brazilian singer-songwriter, writer and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Cae<PERSON>_Veloso\" title=\"Caetano Veloso\"><PERSON><PERSON><PERSON></a>, Brazilian singer-songwriter, writer and producer", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Cae<PERSON>_Veloso"}]}, {"year": "1942", "text": "<PERSON>, English biochemist and academic", "html": "1942 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(microbiologist)\" title=\"<PERSON> (microbiologist)\"><PERSON></a>, English biochemist and academic", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(microbiologist)\" title=\"<PERSON> (microbiologist)\"><PERSON></a>, English biochemist and academic", "links": [{"title": "<PERSON> (microbiologist)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(microbiologist)"}]}, {"year": "1942", "text": "<PERSON><PERSON> <PERSON><PERSON>, American singer (d. 2021)", "html": "1942 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> <PERSON><PERSON>\"><PERSON><PERSON> <PERSON><PERSON></a>, American singer (d. 2021)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> <PERSON><PERSON>\"><PERSON><PERSON> <PERSON><PERSON></a>, American singer (d. 2021)", "links": [{"title": "<PERSON><PERSON> <PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "1943", "text": "<PERSON>, Egyptian religious leader", "html": "1943 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Egyptian religious leader", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Mohammed Bad<PERSON>\"><PERSON></a>, Egyptian religious leader", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1943", "text": "<PERSON>, Australian singer-songwriter and lawyer", "html": "1943 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian singer-songwriter and lawyer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian singer-songwriter and lawyer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1943", "text": "<PERSON>, French director and screenwriter (d. 2010)", "html": "1943 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French director and screenwriter (d. 2010)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French director and screenwriter (d. 2010)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1944", "text": "<PERSON>, American actor", "html": "1944 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(actor)\" title=\"<PERSON> (actor)\"><PERSON></a>, American actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>(actor)\" title=\"<PERSON> (actor)\"><PERSON></a>, American actor", "links": [{"title": "<PERSON> (actor)", "link": "https://wikipedia.org/wiki/<PERSON>_(actor)"}]}, {"year": "1944", "text": "<PERSON>, American soldier and lawyer, 6th Director of the Federal Bureau of Investigation", "html": "1944 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American soldier and lawyer, 6th <a href=\"https://wikipedia.org/wiki/Director_of_the_Federal_Bureau_of_Investigation\" title=\"Director of the Federal Bureau of Investigation\">Director of the Federal Bureau of Investigation</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American soldier and lawyer, 6th <a href=\"https://wikipedia.org/wiki/Director_of_the_Federal_Bureau_of_Investigation\" title=\"Director of the Federal Bureau of Investigation\">Director of the Federal Bureau of Investigation</a>", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}, {"title": "Director of the Federal Bureau of Investigation", "link": "https://wikipedia.org/wiki/Director_of_the_Federal_Bureau_of_Investigation"}]}, {"year": "1945", "text": "<PERSON>, Scottish actor and director (d. 2014)", "html": "1945 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Kenny <PERSON>\"><PERSON></a>, Scottish actor and director (d. 2014)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Kenny_<PERSON>\" title=\"Kenny <PERSON>\"><PERSON></a>, Scottish actor and director (d. 2014)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Kenny_<PERSON>"}]}, {"year": "1945", "text": "<PERSON>, American football player and jurist", "html": "1945 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player and jurist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player and jurist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1947", "text": "<PERSON><PERSON>, Dutch-Australian singer-songwriter", "html": "1947 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Dutch-Australian singer-songwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Dutch-Australian singer-songwriter", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_Henri"}]}, {"year": "1947", "text": "<PERSON>, Ukrainian singer-songwriter, producer, and actress", "html": "1947 - <a href=\"https://wikipedia.org/wiki/Sofia_Rotaru\" title=\"Sofia Rotaru\"><PERSON></a>, Ukrainian singer-songwriter, producer, and actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Sofia_Rotaru\" title=\"Sofia Rotaru\"><PERSON></a>, Ukrainian singer-songwriter, producer, and actress", "links": [{"title": "Sofia Rotaru", "link": "https://wikipedia.org/wiki/Sofia_Rotaru"}]}, {"year": "1948", "text": "<PERSON>, American businessman and author", "html": "1948 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American businessman and author", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American businessman and author", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1948", "text": "<PERSON>, Australian cricketer and coach", "html": "1948 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian cricketer and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian cricketer and coach", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1949", "text": "<PERSON><PERSON><PERSON>, Lebanese journalist and politician", "html": "1949 - <a href=\"https://wikipedia.org/wiki/W<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Lebanese journalist and politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Lebanese journalist and politician", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/W<PERSON><PERSON>_<PERSON>"}]}, {"year": "1949", "text": "<PERSON>, South African-English journalist and politician", "html": "1949 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, South African-English journalist and politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, South African-English journalist and politician", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1950", "text": "<PERSON>, American singer-songwriter and guitarist", "html": "1950 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and guitarist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and guitarist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1950", "text": "<PERSON>, American politician and diplomat, 16th Assistant Secretary of State for International Organization Affairs", "html": "1950 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American politician and diplomat, 16th <a href=\"https://wikipedia.org/wiki/Assistant_Secretary_of_State_for_International_Organization_Affairs\" title=\"Assistant Secretary of State for International Organization Affairs\">Assistant Secretary of State for International Organization Affairs</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American politician and diplomat, 16th <a href=\"https://wikipedia.org/wiki/Assistant_Secretary_of_State_for_International_Organization_Affairs\" title=\"Assistant Secretary of State for International Organization Affairs\">Assistant Secretary of State for International Organization Affairs</a>", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Assistant Secretary of State for International Organization Affairs", "link": "https://wikipedia.org/wiki/Assistant_Secretary_of_State_for_International_Organization_Affairs"}]}, {"year": "1950", "text": "<PERSON><PERSON>, Sri Lankan educator and politician", "html": "1950 - <a href=\"https://wikipedia.org/wiki/S._<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Sri Lankan educator and politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/S._<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Sri Lankan educator and politician", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/S<PERSON>_<PERSON><PERSON>uth<PERSON>ni"}]}, {"year": "1952", "text": "<PERSON>, American actress and producer", "html": "1952 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress and producer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1952", "text": "<PERSON><PERSON><PERSON>, Irish golfer", "html": "1952 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Irish golfer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Irish golfer", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1952", "text": "<PERSON><PERSON>, Dutch footballer", "html": "1952 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Dutch footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Dutch footballer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>st"}]}, {"year": "1952", "text": "<PERSON>, English comedian, actor, and author", "html": "1952 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English comedian, actor, and author", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English comedian, actor, and author", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1953", "text": "<PERSON>, American journalist and author", "html": "1953 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American journalist and author", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American journalist and author", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1954", "text": "<PERSON><PERSON>, Russian footballer, manager and politician", "html": "1954 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Russian footballer, manager and politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Russian footballer, manager and politician", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1954", "text": "<PERSON>, Israeli spy", "html": "1954 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Israeli spy", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Israeli spy", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1954", "text": "<PERSON>, Scottish politician", "html": "1954 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>(politician)\" title=\"<PERSON> (politician)\"><PERSON></a>, Scottish politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>(politician)\" title=\"<PERSON> (politician)\"><PERSON></a>, Scottish politician", "links": [{"title": "<PERSON> (politician)", "link": "https://wikipedia.org/wiki/<PERSON>(politician)"}]}, {"year": "1955", "text": "<PERSON>, American actor, comedian and voice actor", "html": "1955 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor, comedian and voice actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor, comedian and voice actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1955", "text": "<PERSON>, American lawyer and politician, 51st Mayor of Seattle", "html": "1955 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and politician, 51st <a href=\"https://wikipedia.org/wiki/Mayor_of_Seattle\" title=\"Mayor of Seattle\">Mayor of Seattle</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and politician, 51st <a href=\"https://wikipedia.org/wiki/Mayor_of_Seattle\" title=\"Mayor of Seattle\">Mayor of Seattle</a>", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Mayor of Seattle", "link": "https://wikipedia.org/wiki/Mayor_of_Seattle"}]}, {"year": "1955", "text": "<PERSON>, Russian author and playwright", "html": "1955 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian author and playwright", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian author and playwright", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1957", "text": "<PERSON><PERSON>, Irish journalist, lawyer, and actress (d. 2012)", "html": "1957 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Irish journalist, lawyer, and actress (d. 2012)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Irish journalist, lawyer, and actress (d. 2012)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>han"}]}, {"year": "1957", "text": "<PERSON>, Russian gymnast and colonel", "html": "1957 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian gymnast and colonel", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian gymnast and colonel", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1958", "text": "<PERSON>, Canadian-American jockey", "html": "1958 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian-American jockey", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian-American jockey", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1958", "text": "<PERSON>, English singer-songwriter and guitarist", "html": "1958 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English singer-songwriter and guitarist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English singer-songwriter and guitarist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1958", "text": "<PERSON>, Cuban-American runner and coach", "html": "1958 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Cuban-American runner and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Cuban-American runner and coach", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1959", "text": "<PERSON><PERSON><PERSON><PERSON>, Belgian orientalist and author", "html": "1959 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>ra<PERSON>_<PERSON>\" title=\"Koen<PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Belgian orientalist and author", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>ra<PERSON>_<PERSON>\" title=\"Ko<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Belgian orientalist and author", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>st"}]}, {"year": "1959", "text": "<PERSON>, Zimbabwean cricketer and coach", "html": "1959 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Zimbabwean cricketer and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Zimbabwean cricketer and coach", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1960", "text": "<PERSON>, American actor, director, producer, and screenwriter", "html": "1960 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor, director, producer, and screenwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor, director, producer, and screenwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1961", "text": "<PERSON>, English actor and singer", "html": "1961 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actor and singer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actor and singer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1961", "text": "<PERSON><PERSON>, Russian gymnast", "html": "1961 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Russian gymnast", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Russian gymnast", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1961", "text": "<PERSON>, English jockey and trainer (d. 2016)", "html": "1961 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English jockey and trainer (d. 2016)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English jockey and trainer (d. 2016)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1961", "text": "<PERSON>, Colombian singer, songwriter, and actor", "html": "1961 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Colombian singer, songwriter, and actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Colombian singer, songwriter, and actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1962", "text": "<PERSON>, American banjo player, songwriter, and producer", "html": "1962 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American banjo player, songwriter, and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American banjo player, songwriter, and producer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1963", "text": "<PERSON>, Australian rugby league player", "html": "1963 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(rugby_league)\" title=\"<PERSON> (rugby league)\"><PERSON></a>, Australian rugby league player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>(rugby_league)\" title=\"<PERSON> (rugby league)\"><PERSON></a>, Australian rugby league player", "links": [{"title": "<PERSON> (rugby league)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>(rugby_league)"}]}, {"year": "1963", "text": "<PERSON>, American journalist and author", "html": "1963 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American journalist and author", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American journalist and author", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1963", "text": "<PERSON>, American pianist and educator", "html": "1963 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American pianist and educator", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American pianist and educator", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1964", "text": "<PERSON>, English-Australian journalist and author", "html": "1964 - <a href=\"https://wikipedia.org/wiki/John_<PERSON>\" title=\"John <PERSON>\"><PERSON></a>, English-Australian journalist and author", "no_year_html": "<a href=\"https://wikipedia.org/wiki/John_Birmingham\" title=\"John Birmingham\"><PERSON></a>, English-Australian journalist and author", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/John_<PERSON>"}]}, {"year": "1964", "text": "<PERSON>, English guitarist and songwriter", "html": "1964 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English guitarist and songwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English guitarist and songwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1964", "text": "<PERSON>, Scottish jockey", "html": "1964 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish jockey", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish jockey", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1965", "text": "<PERSON><PERSON>, American singer-songwriter, guitarist, and producer", "html": "1965 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American singer-songwriter, guitarist, and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American singer-songwriter, guitarist, and producer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1965", "text": "<PERSON>, Canadian figure skater", "html": "1965 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian figure skater", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian figure skater", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1966", "text": "<PERSON>, Scottish laicised priest and politician, Minister of State for Scotland (d. 2011)", "html": "1966 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>(politician)\" class=\"mw-redirect\" title=\"<PERSON> (politician)\"><PERSON></a>, Scottish laicised priest and politician, <a href=\"https://wikipedia.org/wiki/Minister_of_State_for_Scotland\" class=\"mw-redirect\" title=\"Minister of State for Scotland\">Minister of State for Scotland</a> (d. 2011)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>(politician)\" class=\"mw-redirect\" title=\"<PERSON> (politician)\"><PERSON></a>, Scottish laicised priest and politician, <a href=\"https://wikipedia.org/wiki/Minister_of_State_for_Scotland\" class=\"mw-redirect\" title=\"Minister of State for Scotland\">Minister of State for Scotland</a> (d. 2011)", "links": [{"title": "<PERSON> (politician)", "link": "https://wikipedia.org/wiki/<PERSON>(politician)"}, {"title": "Minister of State for Scotland", "link": "https://wikipedia.org/wiki/Minister_of_State_for_Scotland"}]}, {"year": "1966", "text": "<PERSON><PERSON><PERSON><PERSON>, British actress", "html": "1966 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, British actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, British actress", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON><PERSON><PERSON>"}]}, {"year": "1966", "text": "<PERSON><PERSON>, American singer-songwriter and guitarist", "html": "1966 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American singer-songwriter and guitarist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American singer-songwriter and guitarist", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>"}]}, {"year": "1966", "text": "<PERSON>, American-British entrepreneur, co-founder of Wikipedia", "html": "1966 - <a href=\"https://wikipedia.org/wiki/Jimmy_<PERSON>\" title=\"Jimmy <PERSON>\"><PERSON></a>, American-British entrepreneur, co-founder of <a href=\"https://wikipedia.org/wiki/Wikipedia\" title=\"Wikipedia\">Wikipedia</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Jimmy_<PERSON>\" title=\"Jimmy Wales\"><PERSON></a>, American-British entrepreneur, co-founder of <a href=\"https://wikipedia.org/wiki/Wikipedia\" title=\"Wikipedia\">Wikipedia</a>", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Jimmy_<PERSON>"}, {"title": "Wikipedia", "link": "https://wikipedia.org/wiki/Wikipedia"}]}, {"year": "1967", "text": "<PERSON>, American baseball player", "html": "1967 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1968", "text": "<PERSON>, Italian-American director and screenwriter", "html": "1968 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian-American director and screenwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian-American director and screenwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1968", "text": "<PERSON>, Australian surfer and coach", "html": "1968 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian surfer and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian surfer and coach", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1968", "text": "<PERSON>, Australian actress and author", "html": "1968 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian actress and author", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian actress and author", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1969", "text": "<PERSON>, Swiss writer", "html": "1969 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swiss writer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swiss writer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1969", "text": "<PERSON>, Scottish footballer and manager", "html": "1969 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish footballer and manager", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish footballer and manager", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1969", "text": "<PERSON>, Israeli writer and LGBT activist", "html": "1969 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Israeli writer and LGBT activist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>._<PERSON>eleg\" title=\"<PERSON> G. Peleg\"><PERSON></a>, Israeli writer and LGBT activist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>._<PERSON>"}]}, {"year": "1970", "text": "<PERSON>, American swimmer (d. 2006)", "html": "1970 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American swimmer (d. 2006)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American swimmer (d. 2006)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1971", "text": "<PERSON>, England cricketer and sportscaster", "html": "1971 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, England cricketer and sportscaster", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, England cricketer and sportscaster", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Dominic_<PERSON>"}]}, {"year": "1971", "text": "<PERSON>, American actress and singer", "html": "1971 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Rachel <PERSON>\"><PERSON></a>, American actress and singer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Rachel <PERSON>\"><PERSON></a>, American actress and singer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Rachel_<PERSON>"}]}, {"year": "1972", "text": "<PERSON>, Filipino boxer and promoter", "html": "1972 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%B1alosa\" title=\"<PERSON>\"><PERSON></a>, Filipino boxer and promoter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%B1alo<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Filipino boxer and promoter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Gerry_Pe%C3%B1alosa"}]}, {"year": "1973", "text": "<PERSON>, Russian singer-songwriter (d. 2013)", "html": "1973 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian singer-songwriter (d. 2013)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian singer-songwriter (d. 2013)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1973", "text": "<PERSON>, Vietnamese-American baseball player", "html": "1973 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Vietnamese-American baseball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Vietnamese-American baseball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1973", "text": "<PERSON>, English-Australian footballer, coach, and manager", "html": "1973 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English-Australian footballer, coach, and manager", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English-Australian footballer, coach, and manager", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1974", "text": "<PERSON>, American actor", "html": "1974 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_Benymon"}]}, {"year": "1974", "text": "<PERSON>, American actor", "html": "1974 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1975", "text": "<PERSON><PERSON>, Turkish singer-songwriter", "html": "1975 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Turkish singer-songwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Turkish singer-songwriter", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>demir"}]}, {"year": "1975", "text": "<PERSON>, Australian cricketer", "html": "1975 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian cricketer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian cricketer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1975", "text": "<PERSON>, Australian model and actress", "html": "1975 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian model and actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian model and actress", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1975", "text": "<PERSON>, American football player (d. 2015)", "html": "1975 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(American_football)\" title=\"<PERSON> (American football)\"><PERSON></a>, American football player (d. 2015)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(American_football)\" title=\"<PERSON> (American football)\"><PERSON></a>, American football player (d. 2015)", "links": [{"title": "<PERSON> (American football)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(American_football)"}]}, {"year": "1975", "text": "<PERSON>, American journalist and politician, 44th Lieutenant Governor of Wisconsin", "html": "1975 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American journalist and politician, 44th <a href=\"https://wikipedia.org/wiki/Lieutenant_Governor_of_Wisconsin\" title=\"Lieutenant Governor of Wisconsin\">Lieutenant Governor of Wisconsin</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American journalist and politician, 44th <a href=\"https://wikipedia.org/wiki/Lieutenant_Governor_of_Wisconsin\" title=\"Lieutenant Governor of Wisconsin\">Lieutenant Governor of Wisconsin</a>", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Lieutenant Governor of Wisconsin", "link": "https://wikipedia.org/wiki/Lieutenant_Governor_of_Wisconsin"}]}, {"year": "1975", "text": "<PERSON><PERSON><PERSON>, Colombian baseball player", "html": "1975 - <a href=\"https://wikipedia.org/wiki/%C3%89dgar_Renter%C3%ADa\" title=\"Édgar Rentería\"><PERSON><PERSON><PERSON></a>, Colombian baseball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/%C3%89dgar_Renter%C3%ADa\" title=\"Édgar Rentería\"><PERSON><PERSON><PERSON></a>, Colombian baseball player", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/%C3%89dgar_Renter%C3%ADa"}]}, {"year": "1975", "text": "<PERSON><PERSON><PERSON>, South African actress", "html": "1975 - <a href=\"https://wikipedia.org/wiki/Charlize_<PERSON>\" title=\"Charl<PERSON>\"><PERSON><PERSON><PERSON></a>, South African actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Charlize_<PERSON>\" title=\"Charl<PERSON>\"><PERSON><PERSON><PERSON></a>, South African actress", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Charlize_Theron"}]}, {"year": "1976", "text": "<PERSON><PERSON>, Greek footballer and manager", "html": "1976 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Greek footballer and manager", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Greek footballer and manager", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_Eleftherop<PERSON>los"}]}, {"year": "1976", "text": "<PERSON>, American football player", "html": "1976 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1977", "text": "<PERSON>, English fashion designer", "html": "1977 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English fashion designer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English fashion designer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1977", "text": "<PERSON>, English singer-songwriter and DJ", "html": "1977 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English singer-songwriter and DJ", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English singer-songwriter and DJ", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1977", "text": "<PERSON>, Rugby League Player", "html": "1977 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Rugby League Player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Rugby League Player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1978", "text": "<PERSON>, French director, producer, and screenwriter", "html": "1978 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French director, producer, and screenwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French director, producer, and screenwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1978", "text": "<PERSON><PERSON>, American singer-songwriter", "html": "1978 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American singer-songwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American singer-songwriter", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>a"}]}, {"year": "1978", "text": "<PERSON>, English-Barbadian footballer", "html": "1978 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English-Barbadian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English-Barbadian footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1978", "text": "<PERSON><PERSON><PERSON><PERSON>, American actor", "html": "1978 - <a href=\"https://wikipedia.org/wiki/Cirroc_Lofton\" title=\"Cirroc Lofton\"><PERSON><PERSON><PERSON><PERSON></a>, American actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Cirroc_Lofton\" title=\"Cirroc Lofton\"><PERSON><PERSON><PERSON><PERSON></a>, American actor", "links": [{"title": "Cirroc <PERSON>on", "link": "https://wikipedia.org/wiki/Cirroc_Lofton"}]}, {"year": "1979", "text": "<PERSON>, American actor, director, and screenwriter", "html": "1979 - <a href=\"https://wikipedia.org/wiki/<PERSON>_(actor)\" title=\"<PERSON> (actor)\"><PERSON></a>, American actor, director, and screenwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>(actor)\" title=\"<PERSON> (actor)\"><PERSON></a>, American actor, director, and screenwriter", "links": [{"title": "<PERSON> (actor)", "link": "https://wikipedia.org/wiki/<PERSON>_(actor)"}]}, {"year": "1979", "text": "<PERSON>, Spanish footballer", "html": "1979 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Spanish footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Spanish footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1979", "text": "<PERSON><PERSON><PERSON>, Austrian anthropologist and author", "html": "1979 - <a href=\"https://wikipedia.org/wiki/Birgit_Zotz\" title=\"Birgit Zotz\"><PERSON><PERSON><PERSON></a>, Austrian anthropologist and author", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Birgit_Zotz\" title=\"Birgit Zotz\"><PERSON><PERSON><PERSON></a>, Austrian anthropologist and author", "links": [{"title": "Birgit Zotz", "link": "https://wikipedia.org/wiki/Birgit_<PERSON>z"}]}, {"year": "1980", "text": "<PERSON><PERSON>, German footballer", "html": "1980 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, German footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, German footballer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1980", "text": "<PERSON><PERSON><PERSON>, French model and actress", "html": "1980 - <a href=\"https://wikipedia.org/wiki/Aur%C3%A<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, French model and actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Aur%C3%A<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, French model and actress", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Aur%C3%A<PERSON><PERSON>_<PERSON>l"}]}, {"year": "1980", "text": "<PERSON><PERSON><PERSON>, Brazilian footballer", "html": "1980 - <a href=\"https://wikipedia.org/wiki/T%C3%A1cio_Caetano_Cruz_Queiroz\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Brazilian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/T%C3%A1cio_Caetano_Cruz_Queiroz\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Brazilian footballer", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/T%C3%A1cio_Caetano_Cruz_Queiroz"}]}, {"year": "1980", "text": "<PERSON><PERSON><PERSON><PERSON>, Japanese footballer", "html": "1980 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Japanese footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Japanese footballer", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>ki"}]}, {"year": "1981", "text": "<PERSON>, American soccer player", "html": "1981 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American soccer player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American soccer player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1981", "text": "<PERSON>, American actor and producer", "html": "1981 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor and producer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1982", "text": "<PERSON><PERSON>, Argentine actress and singer", "html": "1982 - <a href=\"https://wikipedia.org/wiki/%C3%81ngeles_Balbiani\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Argentine actress and singer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/%C3%81ngeles_Ba<PERSON>i\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Argentine actress and singer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/%C3%81nge<PERSON>_Balbiani"}]}, {"year": "1982", "text": "<PERSON><PERSON><PERSON>, Australian actress", "html": "1982 - <a href=\"https://wikipedia.org/wiki/A<PERSON>ie_<PERSON>\" title=\"A<PERSON><PERSON>\">A<PERSON><PERSON></a>, Australian actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/A<PERSON><PERSON>_<PERSON>\" title=\"A<PERSON><PERSON>\">A<PERSON><PERSON></a>, Australian actress", "links": [{"title": "A<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Abbie_<PERSON>"}]}, {"year": "1982", "text": "<PERSON>, Argentine rugby player", "html": "1982 - <a href=\"https://wikipedia.org/wiki/Juan_<PERSON>%C3%ADn_Hern%C3%A1ndez\" title=\"<PERSON>\"><PERSON></a>, Argentine rugby player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Juan_Mart%C3%ADn_Hern%C3%A1ndez\" title=\"<PERSON>\"><PERSON></a>, Argentine rugby player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Juan_Mart%C3%ADn_Hern%C3%A1ndez"}]}, {"year": "1982", "text": "<PERSON><PERSON>, American football player (d. 2007)", "html": "1982 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"Marquise Hill\"><PERSON><PERSON></a>, American football player (d. 2007)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"Marquise Hill\"><PERSON><PERSON></a>, American football player (d. 2007)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1982", "text": "<PERSON><PERSON><PERSON>, Greek basketball player", "html": "1982 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Greek basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Greek basketball player", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1982", "text": "<PERSON>, Macedonian singer and drummer", "html": "1982 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C4%8Di%C4%87\" title=\"<PERSON>\"><PERSON></a>, Macedonian singer and drummer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C4%8Di%C4%87\" title=\"<PERSON>\"><PERSON></a>, Macedonian singer and drummer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Martin_Vu%C4%8Di%C4%87"}]}, {"year": "1983", "text": "<PERSON>, Mexican singer-songwriter and actor", "html": "1983 - <a href=\"https://wikipedia.org/wiki/Christian_Ch%C3%A1vez\" title=\"<PERSON>\"><PERSON></a>, Mexican singer-songwriter and actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Christian_Ch%C3%A1vez\" title=\"<PERSON>\"><PERSON></a>, Mexican singer-songwriter and actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Christian_Ch%C3%A1vez"}]}, {"year": "1983", "text": "<PERSON><PERSON>, Turkish singer-songwriter", "html": "1983 - <a href=\"https://wikipedia.org/wiki/Murat_Dalk%C4%B1l%C4%B1%C3%A7\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Turkish singer-songwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>rat_Dalk%C4%B1l%C4%B1%C3%A7\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Turkish singer-songwriter", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Murat_Dalk%C4%B1l%C4%B1%C3%A7"}]}, {"year": "1983", "text": "<PERSON>, Portuguese footballer", "html": "1983 - <a href=\"https://wikipedia.org/wiki/<PERSON>_(footballer)\" title=\"<PERSON> (footballer)\"><PERSON></a>, Portuguese footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>(footballer)\" title=\"<PERSON> (footballer)\"><PERSON></a>, Portuguese footballer", "links": [{"title": "<PERSON> (footballer)", "link": "https://wikipedia.org/wiki/<PERSON>(footballer)"}]}, {"year": "1983", "text": "<PERSON><PERSON><PERSON>, Ukrainian cyclist", "html": "1983 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Ukrainian cyclist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Ukrainian cyclist", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON><PERSON>"}]}, {"year": "1983", "text": "<PERSON>, English cricketer and journalist", "html": "1983 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English cricketer and journalist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English cricketer and journalist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1984", "text": "<PERSON><PERSON><PERSON>, Greek basketball player", "html": "1984 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Greek basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Greek basketball player", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1984", "text": "<PERSON><PERSON>, Pakistani model and actress", "html": "1984 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> Siddiqui\"><PERSON><PERSON></a>, Pakistani model and actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_Sid<PERSON>\" title=\"<PERSON><PERSON> Siddiqui\"><PERSON><PERSON></a>, Pakistani model and actress", "links": [{"title": "Tooba Siddiqui", "link": "https://wikipedia.org/wiki/Tooba_Siddiqui"}]}, {"year": "1984", "text": "<PERSON>, South Korean poet and author (d. 2003)", "html": "1984 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>-<PERSON>\" title=\"<PERSON>\"><PERSON></a>, South Korean poet and author (d. 2003)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>-<PERSON>\" title=\"<PERSON>\"><PERSON></a>, South Korean poet and author (d. 2003)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>-seok"}]}, {"year": "1986", "text": "<PERSON>, German swimmer", "html": "1986 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German swimmer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German swimmer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1986", "text": "<PERSON><PERSON>, Slovenian footballer", "html": "1986 - <a href=\"https://wikipedia.org/wiki/Valter_Birsa\" title=\"Valter Birsa\"><PERSON><PERSON></a>, Slovenian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Val<PERSON>_Birsa\" title=\"Valter Birsa\"><PERSON><PERSON></a>, Slovenian footballer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Valter_Birsa"}]}, {"year": "1986", "text": "<PERSON><PERSON><PERSON><PERSON>, Mexican model and actress", "html": "1986 - <a href=\"https://wikipedia.org/wiki/Alta%C3%ADr_Jarabo\" title=\"Altaír Jarabo\"><PERSON><PERSON><PERSON><PERSON></a>, Mexican model and actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Alta%C3%ADr_Jarabo\" title=\"Altaír Jarabo\"><PERSON><PERSON><PERSON><PERSON></a>, Mexican model and actress", "links": [{"title": "Altaír <PERSON>", "link": "https://wikipedia.org/wiki/Alta%C3%ADr_Jarabo"}]}, {"year": "1986", "text": "<PERSON>, Mexican boxer", "html": "1986 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON> Rosa\"><PERSON></a>, Mexican boxer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON> Rosa\"><PERSON></a>, Mexican boxer", "links": [{"title": "<PERSON> Rosa", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1987", "text": "<PERSON>, Canadian ice hockey player", "html": "1987 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1987", "text": "<PERSON><PERSON><PERSON>, Sierra Leonean footballer", "html": "1987 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Sierra Leonean footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Sierra Leonean footballer", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON><PERSON>"}]}, {"year": "1987", "text": "<PERSON>, American baseball player", "html": "1987 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1987", "text": "<PERSON><PERSON><PERSON>, German footballer", "html": "1987 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, German footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, German footballer", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1988", "text": "<PERSON>, Canadian ice hockey player", "html": "1988 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1988", "text": "<PERSON>, Senegalese footballer", "html": "1988 - <a href=\"https://wikipedia.org/wiki/<PERSON>_(footballer,_born_1988)\" title=\"<PERSON> (footballer, born 1988)\"><PERSON></a>, Senegalese footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(footballer,_born_1988)\" title=\"<PERSON> (footballer, born 1988)\"><PERSON></a>, Senegalese footballer", "links": [{"title": "<PERSON> (footballer, born 1988)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(footballer,_born_1988)"}]}, {"year": "1988", "text": "<PERSON><PERSON>, West Indian cricketer", "html": "1988 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, West Indian cricketer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, West Indian cricketer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1988", "text": "<PERSON>, American blogger", "html": "1988 - <a href=\"https://wikipedia.org/wiki/Melody_Oliveria\" title=\"Melody Oliveria\"><PERSON></a>, American blogger", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Melody_Oliveria\" title=\"Melody Oliveria\"><PERSON></a>, American blogger", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Melody_Oliveria"}]}, {"year": "1988", "text": "<PERSON>, Dutch footballer", "html": "1988 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1988", "text": "<PERSON><PERSON>, American football player", "html": "1988 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American football player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American football player", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1989", "text": "<PERSON><PERSON><PERSON>, American basketball player", "html": "1989 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American basketball player", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1990", "text": "<PERSON>, Canadian ice hockey player", "html": "1990 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(ice_hockey)\" title=\"<PERSON> (ice hockey)\"><PERSON></a>, Canadian ice hockey player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(ice_hockey)\" title=\"<PERSON> (ice hockey)\"><PERSON></a>, Canadian ice hockey player", "links": [{"title": "<PERSON> (ice hockey)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(ice_hockey)"}]}, {"year": "1990", "text": "<PERSON>, English singer-songwriter", "html": "1990 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English singer-songwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English singer-songwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1991", "text": "<PERSON>, Spanish motorcycle racer (d. 2016)", "html": "1991 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Spanish motorcycle racer (d. 2016)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Spanish motorcycle racer (d. 2016)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1991", "text": "<PERSON>, Dutch footballer", "html": "1991 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON> te V<PERSON>e\"><PERSON> te <PERSON></a>, Dutch footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON> te V<PERSON>e\"><PERSON></a>, Dutch footballer", "links": [{"title": "<PERSON> te Vrede", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_V<PERSON>e"}]}, {"year": "1992", "text": "<PERSON>, English cyclist", "html": "1992 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English cyclist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English cyclist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1992", "text": "<PERSON>, English cyclist", "html": "1992 - <a href=\"https://wikipedia.org/wiki/<PERSON>(cyclist)\" title=\"<PERSON> (cyclist)\"><PERSON></a>, English cyclist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>(cyclist)\" title=\"<PERSON> (cyclist)\"><PERSON></a>, English cyclist", "links": [{"title": "<PERSON> (cyclist)", "link": "https://wikipedia.org/wiki/<PERSON>(cyclist)"}]}, {"year": "1992", "text": "<PERSON><PERSON>, Dutch footballer", "html": "1992 - <a href=\"https://wikipedia.org/wiki/Wout_Wegh<PERSON>t\" title=\"Wout Weghorst\">W<PERSON></a>, Dutch footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Wout_Wegh<PERSON>t\" title=\"Wout Weghorst\">W<PERSON></a>, Dutch footballer", "links": [{"title": "Wout <PERSON>", "link": "https://wikipedia.org/wiki/Wout_<PERSON>t"}]}, {"year": "1993", "text": "<PERSON>, American actress and television personality", "html": "1993 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress and television personality", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress and television personality", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1993", "text": "<PERSON><PERSON>, Estonian ski jumper", "html": "1993 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_N%C3%B5mme\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Estonian ski jumper", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_N%C3%B5mme\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Estonian ski jumper", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Martti_N%C3%B5mme"}]}, {"year": "1993", "text": "<PERSON><PERSON>, Polish sprinter", "html": "1993 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Polish sprinter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Polish sprinter", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1996", "text": "<PERSON>, Spanish footballer", "html": "1996 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Spanish footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Spanish footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1997", "text": "<PERSON><PERSON>, Polish footballer", "html": "1997 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Polish footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Polish footballer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1997", "text": "<PERSON><PERSON>, American football player", "html": "1997 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American football player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American football player", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1998", "text": "<PERSON>, Italian diver", "html": "1998 - <a href=\"https://wikipedia.org/wiki/Vladimir_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian diver", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Vladimir_Barbu\" title=\"Vladimir <PERSON>bu\"><PERSON></a>, Italian diver", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Vladimir_Barbu"}]}, {"year": "1998", "text": "<PERSON>, Peruvian windsurfer", "html": "1998 - <a href=\"https://wikipedia.org/wiki/Mar%C3%ADa_Bel%C3%A9n_Ba<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Peruvian windsurfer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Mar%C3%ADa_Bel%C3%A9n_Bazo\" title=\"María <PERSON>\"><PERSON></a>, Peruvian windsurfer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Mar%C3%ADa_Bel%C3%A9n_Bazo"}]}, {"year": "1998", "text": "<PERSON><PERSON><PERSON>, American football player", "html": "1998 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American football player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American football player", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1999", "text": "<PERSON>-<PERSON><PERSON>, American hurdler and sprinter", "html": "1999 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON>-<PERSON></a>, American hurdler and sprinter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>-<PERSON>\" title=\"<PERSON>\"><PERSON>-<PERSON></a>, American hurdler and sprinter", "links": [{"title": "<PERSON> McLaughlin-<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>-<PERSON>"}]}, {"year": "2000", "text": "<PERSON>, English footballer", "html": "2000 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}], "Deaths": [{"year": "461", "text": "<PERSON><PERSON>, Roman emperor (b. 420)", "html": "461 - <a href=\"https://wikipedia.org/wiki/Major<PERSON>\" title=\"Major<PERSON>\"><PERSON><PERSON></a>, Roman emperor (b. 420)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Majorian\" title=\"Majorian\"><PERSON><PERSON></a>, Roman emperor (b. 420)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>ian"}]}, {"year": "707", "text": "<PERSON>, Chinese prince", "html": "707 - <a href=\"https://wikipedia.org/wiki/Li_Cho<PERSON>jun\" title=\"Li Chongjun\"><PERSON></a>, Chinese prince", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Li_Chongjun\" title=\"Li Chongjun\"><PERSON></a>, Chinese prince", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Li_<PERSON>jun"}]}, {"year": "1028", "text": "<PERSON>, king of León (b. 994)", "html": "1028 - <a href=\"https://wikipedia.org/wiki/Alfonso_V_of_Le%C3%B3n\" title=\"<PERSON> V of León\"><PERSON></a>, king of León (b. 994)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Alfonso_V_of_Le%C3%B3n\" title=\"<PERSON> V of León\"><PERSON> V</a>, king of León (b. 994)", "links": [{"title": "Alfonso V of León", "link": "https://wikipedia.org/wiki/Alfonso_V_of_Le%C3%B3n"}]}, {"year": "1106", "text": "<PERSON>, Holy Roman Emperor (b. 1050)", "html": "1106 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Holy_Roman_Emperor\" title=\"<PERSON> IV, Holy Roman Emperor\"><PERSON> IV</a>, Holy Roman Emperor (b. 1050)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Holy_Roman_Emperor\" title=\"<PERSON> IV, Holy Roman Emperor\"><PERSON></a>, Holy Roman Emperor (b. 1050)", "links": [{"title": "<PERSON>, Holy Roman Emperor", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Holy_Roman_Emperor"}]}, {"year": "1234", "text": "<PERSON>, bishop of Hereford (b. c. 1155)", "html": "1234 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, bishop of Hereford (b. c. 1155)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, bishop of Hereford (b. c. 1155)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1272", "text": "<PERSON>, English Lord Chancellor", "html": "1272 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(Lord_Chancellor)\" title=\"<PERSON> (Lord Chancellor)\"><PERSON></a>, English <a href=\"https://wikipedia.org/wiki/Lord_Chancellor\" title=\"Lord Chancellor\">Lord Chancellor</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(Lord_Chancellor)\" title=\"<PERSON> (Lord Chancellor)\"><PERSON></a>, English <a href=\"https://wikipedia.org/wiki/Lord_Chancellor\" title=\"Lord Chancellor\">Lord Chancellor</a>", "links": [{"title": "<PERSON> (Lord Chancellor)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(Lord_Chancellor)"}, {"title": "Lord Chancellor", "link": "https://wikipedia.org/wiki/Lord_Chancellor"}]}, {"year": "1296", "text": "<PERSON>, prince-bishop of Regensburg", "html": "1296 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, prince-bishop of Regensburg", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, prince-bishop of Regensburg", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1385", "text": "<PERSON>, mother of <PERSON> (b. 1328)", "html": "1385 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON> of Kent\"><PERSON></a>, mother of <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_England\" title=\"<PERSON> of England\"><PERSON></a> (b. 1328)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Kent\"><PERSON> Kent</a>, mother of <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_England\" title=\"<PERSON> II of England\"><PERSON></a> (b. 1328)", "links": [{"title": "<PERSON> of Kent", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "<PERSON> of England", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_England"}]}, {"year": "1485", "text": "<PERSON>, duke of Albany (b. 1454)", "html": "1485 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Duke_of_Albany\" title=\"<PERSON>, Duke of Albany\"><PERSON></a>, duke of Albany (b. 1454)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Duke_of_Albany\" title=\"<PERSON>, Duke of Albany\"><PERSON></a>, duke of Albany (b. 1454)", "links": [{"title": "<PERSON>, Duke of Albany", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_<PERSON>_of_Albany"}]}, {"year": "1547", "text": "<PERSON><PERSON><PERSON><PERSON>, Italian priest and saint (b. 1480)", "html": "1547 - <a href=\"https://wikipedia.org/wiki/Saint_Cajetan\" title=\"Saint Cajetan\"><PERSON><PERSON><PERSON><PERSON></a>, Italian priest and saint (b. 1480)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Saint_Cajetan\" title=\"Saint Cajetan\"><PERSON><PERSON><PERSON><PERSON></a>, Italian priest and saint (b. 1480)", "links": [{"title": "Saint Cajetan", "link": "https://wikipedia.org/wiki/Saint_Cajetan"}]}, {"year": "1613", "text": "<PERSON>, English judge and politician, Lord Chief Justice of England (b. 1544)", "html": "1613 - <a href=\"https://wikipedia.org/wiki/<PERSON>(judge)\" title=\"<PERSON> (judge)\"><PERSON></a>, English judge and politician, <a href=\"https://wikipedia.org/wiki/Lord_Chief_Justice_of_England\" class=\"mw-redirect\" title=\"Lord Chief Justice of England\">Lord Chief Justice of England</a> (b. 1544)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>(judge)\" title=\"<PERSON> (judge)\"><PERSON></a>, English judge and politician, <a href=\"https://wikipedia.org/wiki/Lord_Chief_Justice_of_England\" class=\"mw-redirect\" title=\"Lord Chief Justice of England\">Lord Chief Justice of England</a> (b. 1544)", "links": [{"title": "<PERSON> (judge)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(judge)"}, {"title": "Lord Chief Justice of England", "link": "https://wikipedia.org/wiki/Lord_Chief_Justice_of_England"}]}, {"year": "1616", "text": "<PERSON>, Italian architect, designed Teatro Olimpico (b. 1548)", "html": "1616 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian architect, designed <a href=\"https://wikipedia.org/wiki/Teatro_Olimpico\" title=\"Teatro Olimpico\">Teatro Olimpico</a> (b. 1548)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian architect, designed <a href=\"https://wikipedia.org/wiki/Teatro_Olimpico\" title=\"Teatro Olimpico\">Teatro Olimpico</a> (b. 1548)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}, {"title": "Teatro Olimpico", "link": "https://wikipedia.org/wiki/Teatro_Olimpico"}]}, {"year": "1632", "text": "<PERSON>, 19th Earl of Oxford, English soldier (b. 1575)", "html": "1632 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_19th_Earl_of_Oxford\" title=\"<PERSON>, 19th Earl of Oxford\"><PERSON>, 19th Earl of Oxford</a>, English soldier (b. 1575)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_19th_Earl_of_Oxford\" title=\"<PERSON>, 19th Earl of Oxford\"><PERSON>, 19th Earl of Oxford</a>, English soldier (b. 1575)", "links": [{"title": "<PERSON>, 19th Earl of Oxford", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_19th_Earl_of_Oxford"}]}, {"year": "1635", "text": "<PERSON>, German poet and academic (b. 1591)", "html": "1635 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German poet and academic (b. 1591)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German poet and academic (b. 1591)", "links": [{"title": "Friedrich <PERSON>", "link": "https://wikipedia.org/wiki/Friedrich_<PERSON>"}]}, {"year": "1639", "text": "<PERSON>, Dutch astronomer and mathematician (b. 1605)", "html": "1639 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch astronomer and mathematician (b. 1605)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch astronomer and mathematician (b. 1605)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1661", "text": "<PERSON>, Chinese journalist and critic (b. 1608)", "html": "1661 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Chinese journalist and critic (b. 1608)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Chinese journalist and critic (b. 1608)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1787", "text": "<PERSON>, English Anglican churchman and activist (b. 1705)", "html": "1787 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(priest)\" title=\"<PERSON> (priest)\"><PERSON></a>, English Anglican churchman and activist (b. 1705)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(priest)\" title=\"<PERSON> (priest)\"><PERSON></a>, English Anglican churchman and activist (b. 1705)", "links": [{"title": "<PERSON> (priest)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(priest)"}]}, {"year": "1817", "text": "<PERSON>, French economist and politician (b. 1739)", "html": "1817 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>_<PERSON>_Nemours\" title=\"<PERSON>emours\"><PERSON></a>, French economist and politician (b. 1739)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>_<PERSON>_Nemours\" title=\"<PERSON> Nemours\"><PERSON>ours</a>, French economist and politician (b. 1739)", "links": [{"title": "<PERSON>ours", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1834", "text": "<PERSON>, French weaver and inventor, invented the <PERSON><PERSON><PERSON><PERSON> loom (b. 1752)", "html": "1834 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French weaver and inventor, invented the <a href=\"https://wikipedia.org/wiki/J<PERSON><PERSON><PERSON>_loom\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON><PERSON> loom\">J<PERSON><PERSON><PERSON> loom</a> (b. 1752)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French weaver and inventor, invented the <a href=\"https://wikipedia.org/wiki/J<PERSON><PERSON><PERSON>_loom\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON><PERSON> loom\"><PERSON><PERSON><PERSON><PERSON> loom</a> (b. 1752)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "<PERSON><PERSON><PERSON><PERSON> loom", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_loom"}]}, {"year": "1848", "text": "<PERSON><PERSON><PERSON>, Swedish chemist and academic (b. 1779)", "html": "1848 - <a href=\"https://wikipedia.org/wiki/J%C3%B6<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Swedish chemist and academic (b. 1779)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/J%C3%B6<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Swedish chemist and academic (b. 1779)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/J%C3%B6<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1855", "text": "<PERSON>, Mexican general and politician, 19th President of Mexico (b. 1802)", "html": "1855 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Mexican general and politician, 19th <a href=\"https://wikipedia.org/wiki/President_of_Mexico\" title=\"President of Mexico\">President of Mexico</a> (b. 1802)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Mexican general and politician, 19th <a href=\"https://wikipedia.org/wiki/President_of_Mexico\" title=\"President of Mexico\">President of Mexico</a> (b. 1802)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "President of Mexico", "link": "https://wikipedia.org/wiki/President_of_Mexico"}]}, {"year": "1864", "text": "<PERSON>, Chinese field marshal (b. 1823)", "html": "1864 - <a href=\"https://wikipedia.org/wiki/Li_<PERSON>\" title=\"Li Xiucheng\"><PERSON></a>, Chinese field marshal (b. 1823)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Li_<PERSON>\" title=\"Li Xiucheng\"><PERSON></a>, Chinese field marshal (b. 1823)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Li_<PERSON>ng"}]}, {"year": "1893", "text": "<PERSON>, Italian composer and academic (b. 1854)", "html": "1893 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian composer and academic (b. 1854)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian composer and academic (b. 1854)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1899", "text": "<PERSON>, Dutch painter and educator (b. 1837)", "html": "1899 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch painter and educator (b. 1837)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch painter and educator (b. 1837)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1900", "text": "<PERSON>, German lawyer and politician (b. 1826)", "html": "1900 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German lawyer and politician (b. 1826)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German lawyer and politician (b. 1826)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1912", "text": "<PERSON><PERSON><PERSON><PERSON><PERSON>, Swiss limnologist and academic (b. 1841)", "html": "1912 - <a href=\"https://wikipedia.org/wiki/Fran%C3%A<PERSON><PERSON>-<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON><PERSON></a>, Swiss limnologist and academic (b. 1841)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Fran%C3%A<PERSON><PERSON>-<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON><PERSON></a>, Swiss limnologist and academic (b. 1841)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Fran%C3%A<PERSON><PERSON>-<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1917", "text": "<PERSON>, South African-English commander and pilot (b. 1891)", "html": "1917 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, South African-English commander and pilot (b. 1891)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, South African-English commander and pilot (b. 1891)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1938", "text": "<PERSON>, Russian actor and director (b. 1863)", "html": "1938 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian actor and director (b. 1863)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian actor and director (b. 1863)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1941", "text": "<PERSON><PERSON><PERSON><PERSON>, Indian author, poet, and playwright, Nobel Prize laureate (b. 1861)", "html": "1941 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_Tagore\" title=\"<PERSON><PERSON><PERSON>nath Tagore\"><PERSON><PERSON><PERSON><PERSON></a>, Indian author, poet, and playwright, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Literature\" title=\"Nobel Prize in Literature\">Nobel Prize</a> laureate (b. 1861)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>nath Tagore\"><PERSON><PERSON><PERSON><PERSON></a>, Indian author, poet, and playwright, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Literature\" title=\"Nobel Prize in Literature\">Nobel Prize</a> laureate (b. 1861)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>"}, {"title": "Nobel Prize in Literature", "link": "https://wikipedia.org/wiki/Nobel_Prize_in_Literature"}]}, {"year": "1948", "text": "<PERSON>, English-American actor and director (b. 1879)", "html": "1948 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(actor)\" title=\"<PERSON> (actor)\"><PERSON></a>, English-American actor and director (b. 1879)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>(actor)\" title=\"<PERSON> (actor)\"><PERSON></a>, English-American actor and director (b. 1879)", "links": [{"title": "<PERSON> (actor)", "link": "https://wikipedia.org/wiki/<PERSON>(actor)"}]}, {"year": "1953", "text": "<PERSON><PERSON>, American baseball player and manager (b. 1860)", "html": "1953 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American baseball player and manager (b. 1860)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American baseball player and manager (b. 1860)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1957", "text": "<PERSON>, American actor, singer, and director (b. 1892)", "html": "1957 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor, singer, and director (b. 1892)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor, singer, and director (b. 1892)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1958", "text": "<PERSON>, American author and educator (b. 1892)", "html": "1958 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author and educator (b. 1892)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author and educator (b. 1892)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1960", "text": "<PERSON>, Argentine boxer (b. 1894)", "html": "1960 - <a href=\"https://wikipedia.org/wiki/<PERSON>_%C3%81ngel_Firpo\" title=\"<PERSON>\"><PERSON></a>, Argentine boxer (b. 1894)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_%C3%81ngel_Firpo\" title=\"<PERSON>\"><PERSON></a>, Argentine boxer (b. 1894)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_%C3%81ngel_Firpo"}]}, {"year": "1963", "text": "<PERSON>, last of the Spanish Maquis, holding out after the end of the Spanish Civil War (b. 1908)", "html": "1963 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, last of the <a href=\"https://wikipedia.org/wiki/Spanish_Maquis\" title=\"Spanish Maquis\">Spanish Maquis</a>, holding out after the end of the <a href=\"https://wikipedia.org/wiki/Spanish_Civil_War\" title=\"Spanish Civil War\">Spanish Civil War</a> (b. 1908)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, last of the <a href=\"https://wikipedia.org/wiki/Spanish_Maquis\" title=\"Spanish Maquis\">Spanish Maquis</a>, holding out after the end of the <a href=\"https://wikipedia.org/wiki/Spanish_Civil_War\" title=\"Spanish Civil War\">Spanish Civil War</a> (b. 1908)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "Spanish Maquis", "link": "https://wikipedia.org/wiki/Spanish_Maquis"}, {"title": "Spanish Civil War", "link": "https://wikipedia.org/wiki/Spanish_Civil_War"}]}, {"year": "1968", "text": "<PERSON>, Italian race car driver (b. 1908)", "html": "1968 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian race car driver (b. 1908)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian race car driver (b. 1908)", "links": [{"title": "Giovanni <PERSON>", "link": "https://wikipedia.org/wiki/Giovanni_Bracco"}]}, {"year": "1969", "text": "<PERSON>, French professional footballer (b. 1915)", "html": "1969 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French professional footballer (b. 1915)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French professional footballer (b. 1915)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1969", "text": "<PERSON>, Hungarian-French composer (b. 1905)", "html": "1969 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Hungarian-French composer (b. 1905)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Hungarian-French composer (b. 1905)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1970", "text": "<PERSON>, American lawyer and judge (b. 1904)", "html": "1970 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and judge (b. 1904)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and judge (b. 1904)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1970", "text": "<PERSON>, American bodyguard and kidnapper (b. 1953)", "html": "1970 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American bodyguard and kidnapper (b. 1953)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American bodyguard and kidnapper (b. 1953)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1972", "text": "<PERSON><PERSON>, American model, actress, and singer (b. 1929)", "html": "1972 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American model, actress, and singer (b. 1929)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American model, actress, and singer (b. 1929)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1973", "text": "<PERSON>, Australian cricketer (b. 1895)", "html": "1973 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(cricketer)\" title=\"<PERSON> (cricketer)\"><PERSON></a>, Australian cricketer (b. 1895)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(cricketer)\" title=\"<PERSON> (cricketer)\"><PERSON></a>, Australian cricketer (b. 1895)", "links": [{"title": "<PERSON> (cricketer)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>(cricketer)"}]}, {"year": "1974", "text": "<PERSON>, Mexican poet and author (b. 1925)", "html": "1974 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>nos\" title=\"<PERSON>\"><PERSON></a>, Mexican poet and author (b. 1925)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>nos\" title=\"<PERSON>\"><PERSON></a>, Mexican poet and author (b. 1925)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Rosario_Castellanos"}]}, {"year": "1974", "text": "<PERSON><PERSON><PERSON>, Canadian ice hockey player and coach (b. 1902)", "html": "1974 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Canadian ice hockey player and coach (b. 1902)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Canadian ice hockey player and coach (b. 1902)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1978", "text": "<PERSON>, English trumpeter (b. 1922)", "html": "1978 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English trumpeter (b. 1922)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English trumpeter (b. 1922)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1981", "text": "<PERSON><PERSON>, Estonian chess player (b. 1931)", "html": "1981 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Estonian chess player (b. 1931)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Estonian chess player (b. 1931)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>i"}]}, {"year": "1985", "text": "<PERSON>, American actress (b. 1922)", "html": "1985 - <a href=\"https://wikipedia.org/wiki/Grayson_Hall\" title=\"Grayson Hall\"><PERSON> Hall</a>, American actress (b. 1922)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Grayson_Hall\" title=\"Grayson Hall\"><PERSON> Hall</a>, American actress (b. 1922)", "links": [{"title": "Grayson Hall", "link": "https://wikipedia.org/wiki/Grayson_Hall"}]}, {"year": "1987", "text": "<PERSON>, Lebanese lawyer and politician, 7th President of Lebanon (b. 1900)", "html": "1987 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Lebanese lawyer and politician, 7th <a href=\"https://wikipedia.org/wiki/President_of_Lebanon\" title=\"President of Lebanon\">President of Lebanon</a> (b. 1900)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Lebanese lawyer and politician, 7th <a href=\"https://wikipedia.org/wiki/President_of_Lebanon\" title=\"President of Lebanon\">President of Lebanon</a> (b. 1900)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}, {"title": "President of Lebanon", "link": "https://wikipedia.org/wiki/President_of_Lebanon"}]}, {"year": "1989", "text": "<PERSON>, American lawyer and politician (b. 1944)", "html": "1989 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and politician (b. 1944)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and politician (b. 1944)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1994", "text": "<PERSON>, English actor (b. 1934)", "html": "1994 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actor (b. 1934)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actor (b. 1934)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1995", "text": "<PERSON><PERSON><PERSON>, English author and critic (b. 1929)", "html": "1995 - <a href=\"https://wikipedia.org/wiki/Brigid_Brophy\" title=\"Brig<PERSON> Brophy\"><PERSON><PERSON><PERSON></a>, English author and critic (b. 1929)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>rigid_Brophy\" title=\"Brig<PERSON> Brophy\"><PERSON><PERSON><PERSON></a>, English author and critic (b. 1929)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/B<PERSON><PERSON>_<PERSON>rophy"}]}, {"year": "2001", "text": "<PERSON><PERSON><PERSON><PERSON>, Lithuanian basketball player (b. 1932)", "html": "2001 - <a href=\"https://wikipedia.org/wiki/Al<PERSON><PERSON><PERSON>_<PERSON>%C4%97nas\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Lithuanian basketball player (b. 1932)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>%C4%97nas\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Lithuanian basketball player (b. 1932)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Algirdas_Laurit%C4%97nas"}]}, {"year": "2003", "text": "<PERSON><PERSON> <PERSON><PERSON>, Sri Lankan zoologist and academic (b. 1931)", "html": "2003 - <a href=\"https://wikipedia.org/wiki/K._<PERSON>._A<PERSON><PERSON>pragasam\" title=\"K. D. Arulpragasam\"><PERSON><PERSON> <PERSON><PERSON></a>, Sri Lankan zoologist and academic (b. 1931)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/K._<PERSON><PERSON>_<PERSON><PERSON><PERSON>gas<PERSON>\" title=\"K. D. A<PERSON>pragasam\"><PERSON><PERSON> <PERSON><PERSON></a>, Sri Lankan zoologist and academic (b. 1931)", "links": [{"title": "K. D<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON><PERSON><PERSON>am"}]}, {"year": "2003", "text": "<PERSON>, American baseball player and coach (b. 1929)", "html": "2003 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player and coach (b. 1929)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player and coach (b. 1929)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2004", "text": "<PERSON>, American firefighter (b. 1915)", "html": "2004 - <a href=\"https://wikipedia.org/wiki/Red_Adair\" title=\"Red Adair\"><PERSON></a>, American firefighter (b. 1915)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Red_Adair\" title=\"Red Adair\"><PERSON> Adair</a>, American firefighter (b. 1915)", "links": [{"title": "Red Adair", "link": "https://wikipedia.org/wiki/Red_Adair"}]}, {"year": "2004", "text": "<PERSON>, English ornithologist and academic (b. 1948)", "html": "2004 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English ornithologist and academic (b. 1948)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English ornithologist and academic (b. 1948)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2005", "text": "<PERSON>, Canadian-American journalist and author (b. 1938)", "html": "2005 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian-American journalist and author (b. 1938)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian-American journalist and author (b. 1938)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2005", "text": "<PERSON><PERSON>-<PERSON><PERSON><PERSON><PERSON>, Slovak painter (b. 1909)", "html": "2005 - <a href=\"https://wikipedia.org/wiki/Ester_%C5%A0imerov%C3%A1-Martin%C4%8Dekov%C3%A1\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Slovak painter (b. 1909)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Ester_%C5%A0imerov%C3%A1-Martin%C4%8Dekov%C3%A1\" title=\"<PERSON><PERSON>\"><PERSON><PERSON>-<PERSON></a>, Slovak painter (b. 1909)", "links": [{"title": "<PERSON><PERSON>-<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Ester_%C5%A0imerov%C3%A1-Martin%C4%8Dekov%C3%A1"}]}, {"year": "2006", "text": "<PERSON>, American lawyer and politician (b. 1911)", "html": "2006 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and politician (b. 1911)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and politician (b. 1911)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "2007", "text": "<PERSON>, Mexican actor, director, and producer (b. 1917)", "html": "2007 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Mexican actor, director, and producer (b. 1917)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Mexican actor, director, and producer (b. 1917)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2007", "text": "<PERSON>, New Zealand businessman, founded Tait Communications (b. 1919)", "html": "2007 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Tai<PERSON>\"><PERSON></a>, New Zealand businessman, founded <a href=\"https://wikipedia.org/wiki/Tait_Communications\" title=\"Tait Communications\">Tait Communications</a> (b. 1919)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Angus Tai<PERSON>\"><PERSON></a>, New Zealand businessman, founded <a href=\"https://wikipedia.org/wiki/Tait_Communications\" title=\"Tait Communications\">Tait Communications</a> (b. 1919)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Angus_Tait"}, {"title": "Tait Communications", "link": "https://wikipedia.org/wiki/Tait_Communications"}]}, {"year": "2008", "text": "<PERSON>, American talent agent and producer (b. 1931)", "html": "2008 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American talent agent and producer (b. 1931)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American talent agent and producer (b. 1931)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2008", "text": "<PERSON>, Italian engineer and businessman (b. 1957)", "html": "2008 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian engineer and businessman (b. 1957)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian engineer and businessman (b. 1957)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2009", "text": "<PERSON>, American educator and politician, 48th Mayor of Albuquerque (b. 1933)", "html": "2009 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American educator and politician, 48th <a href=\"https://wikipedia.org/wiki/Mayor_of_Albuquerque\" class=\"mw-redirect\" title=\"Mayor of Albuquerque\">Mayor of Albuquerque</a> (b. 1933)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American educator and politician, 48th <a href=\"https://wikipedia.org/wiki/Mayor_of_Albuquerque\" class=\"mw-redirect\" title=\"Mayor of Albuquerque\">Mayor of Albuquerque</a> (b. 1933)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "Mayor of Albuquerque", "link": "https://wikipedia.org/wiki/Mayor_of_Albuquerque"}]}, {"year": "2009", "text": "<PERSON>, American singer-songwriter (b. 1933)", "html": "2009 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter (b. 1933)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter (b. 1933)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2010", "text": "<PERSON>, English mathematician and statistician (b. 1924)", "html": "2010 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>\" title=\"<PERSON>\"><PERSON></a>, English mathematician and statistician (b. 1924)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English mathematician and statistician (b. 1924)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2011", "text": "<PERSON>, American soldier, academic, and politician, 29th Governor of Oregon (b. 1922)", "html": "2011 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American soldier, academic, and politician, 29th <a href=\"https://wikipedia.org/wiki/Governor_of_Oregon\" title=\"Governor of Oregon\">Governor of Oregon</a> (b. 1922)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American soldier, academic, and politician, 29th <a href=\"https://wikipedia.org/wiki/Governor_of_Oregon\" title=\"Governor of Oregon\">Governor of Oregon</a> (b. 1922)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Governor of Oregon", "link": "https://wikipedia.org/wiki/Governor_of_Oregon"}]}, {"year": "2011", "text": "<PERSON>, New Zealand-English captain and spy (b. 1912)", "html": "2011 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Nancy <PERSON>\"><PERSON></a>, New Zealand-English captain and spy (b. 1912)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Nancy Wake\"><PERSON></a>, New Zealand-English captain and spy (b. 1912)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2012", "text": "<PERSON><PERSON><PERSON>, Azerbaijani academic and politician, Speaker of the National Assembly of Azerbaijan (b. 1928)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Azerbaijani academic and politician, <a href=\"https://wikipedia.org/wiki/Speaker_of_the_National_Assembly_of_Azerbaijan\" title=\"Speaker of the National Assembly of Azerbaijan\">Speaker of the National Assembly of Azerbaijan</a> (b. 1928)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Azerbaijani academic and politician, <a href=\"https://wikipedia.org/wiki/Speaker_of_the_National_Assembly_of_Azerbaijan\" title=\"Speaker of the National Assembly of Azerbaijan\">Speaker of the National Assembly of Azerbaijan</a> (b. 1928)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}, {"title": "Speaker of the National Assembly of Azerbaijan", "link": "https://wikipedia.org/wiki/Speaker_of_the_National_Assembly_of_Azerbaijan"}]}, {"year": "2012", "text": "<PERSON>, American critic and academic (b. 1922)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American critic and academic (b. 1922)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American critic and academic (b. 1922)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2012", "text": "<PERSON>, Russian footballer and coach (b. 1959)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian footballer and coach (b. 1959)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian footballer and coach (b. 1959)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Vladimir_<PERSON>"}]}, {"year": "2012", "text": "<PERSON>, Italian journalist and author (b. 1931)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian journalist and author (b. 1931)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian journalist and author (b. 1931)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2012", "text": "<PERSON>, American sociologist and academic (b. 1931)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American sociologist and academic (b. 1931)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American sociologist and academic (b. 1931)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2012", "text": "<PERSON><PERSON><PERSON>, Czech indologist and author (b. 1925)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/Du%C5%A1an_Zbavitel\" title=\"<PERSON><PERSON><PERSON> Zba<PERSON>\"><PERSON><PERSON><PERSON></a>, Czech indologist and author (b. 1925)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Du%C5%A1an_Zbavitel\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Czech indologist and author (b. 1925)", "links": [{"title": "Du<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Du%C5%A1an_Zbavitel"}]}, {"year": "2013", "text": "<PERSON>, American linguist, historian, and academic (b. 1927)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American linguist, historian, and academic (b. 1927)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American linguist, historian, and academic (b. 1927)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_Armistead"}]}, {"year": "2013", "text": "<PERSON><PERSON>, Russian footballer (b. 1964)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Russian footballer (b. 1964)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Russian footballer (b. 1964)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "2013", "text": "<PERSON>, English-Canadian biologist, chemist, and academic (b. 1952)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, English-Canadian biologist, chemist, and academic (b. 1952)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, English-Canadian biologist, chemist, and academic (b. 1952)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2013", "text": "<PERSON>, American actress and dancer (b. 1923)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress and dancer (b. 1923)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress and dancer (b. 1923)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "2013", "text": "<PERSON><PERSON>, Estonian architect (b. 1946)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> T<PERSON>u\"><PERSON><PERSON></a>, Estonian architect (b. 1946)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Estonian architect (b. 1946)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>u"}]}, {"year": "2013", "text": "<PERSON>, Russian boxer (b. 1961)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian boxer (b. 1961)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian boxer (b. 1961)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2014", "text": "<PERSON><PERSON><PERSON>, Argentine lawyer and politician (b. 1955)", "html": "2014 - <a href=\"https://wikipedia.org/wiki/V%C3%<PERSON>ctor_Fayad\" title=\"<PERSON><PERSON><PERSON> Fayad\"><PERSON><PERSON><PERSON></a>, Argentine lawyer and politician (b. 1955)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/V%C3%<PERSON><PERSON>_Fayad\" title=\"<PERSON><PERSON><PERSON> Fayad\"><PERSON><PERSON><PERSON></a>, Argentine lawyer and politician (b. 1955)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/V%C3%ADctor_Fayad"}]}, {"year": "2014", "text": "<PERSON>, American football player and coach (b. 1926)", "html": "2014 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player and coach (b. 1926)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player and coach (b. 1926)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2014", "text": "<PERSON>, American record producer (b. 1921)", "html": "2014 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Stone\"><PERSON></a>, American record producer (b. 1921)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Stone\"><PERSON></a>, American record producer (b. 1921)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2015", "text": "<PERSON>, Chilean general (b. 1929)", "html": "2015 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Chilean general (b. 1929)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Chilean general (b. 1929)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2015", "text": "<PERSON>, Canadian pharmacologist and physician (b. 1914)", "html": "2015 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian pharmacologist and physician (b. 1914)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian pharmacologist and physician (b. 1914)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "2015", "text": "<PERSON>, American golfer, co-founded LPGA (b. 1923)", "html": "2015 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American golfer, co-founded <a href=\"https://wikipedia.org/wiki/LPGA\" title=\"LPGA\">LPGA</a> (b. 1923)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American golfer, co-founded <a href=\"https://wikipedia.org/wiki/LPGA\" title=\"LPGA\">LPGA</a> (b. 1923)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "LPGA", "link": "https://wikipedia.org/wiki/LPGA"}]}, {"year": "2016", "text": "<PERSON>, American racing driver (b. 1989)", "html": "2016 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American racing driver (b. 1989)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American racing driver (b. 1989)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2017", "text": "<PERSON>, American baseball player (b. 1949)", "html": "2017 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player (b. 1949)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player (b. 1949)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2017", "text": "<PERSON>, American composer (b. 1943)", "html": "2017 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American composer (b. 1943)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American composer (b. 1943)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2018", "text": "<PERSON><PERSON>, Indian politician, former Tamil Nadu Chief Minister and prominent leader of Tamils (b. 1924)", "html": "2018 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Indian politician, former Tamil Nadu Chief Minister and prominent leader of Tamils (b. 1924)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Indian politician, former Tamil Nadu Chief Minister and prominent leader of Tamils (b. 1924)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>"}]}, {"year": "2018", "text": "<PERSON>, Slovak hockey player (b. 1940)", "html": "2018 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Slovak hockey player (b. 1940)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Slovak hockey player (b. 1940)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2019", "text": "<PERSON>, American musician, singer, poet and cartoonist (b. 1967)", "html": "2019 - <a href=\"https://wikipedia.org/wiki/<PERSON>_(musician)\" title=\"<PERSON> (musician)\"><PERSON></a>, American musician, singer, poet and <a href=\"https://wikipedia.org/wiki/Cartoonist\" title=\"Cartoonist\">cartoonist</a> (b. 1967)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(musician)\" title=\"<PERSON> (musician)\"><PERSON></a>, American musician, singer, poet and <a href=\"https://wikipedia.org/wiki/Cartoonist\" title=\"Cartoonist\">cartoonist</a> (b. 1967)", "links": [{"title": "<PERSON> (musician)", "link": "https://wikipedia.org/wiki/<PERSON>_(musician)"}, {"title": "Cartoonist", "link": "https://wikipedia.org/wiki/Cartoonist"}]}, {"year": "2020", "text": "<PERSON><PERSON>, Vietnamese politician (b. 1931)", "html": "2020 - <a href=\"https://wikipedia.org/wiki/L%C3%AA_Kh%E1%BA%A3_Phi%C3%AAu\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Vietnamese politician (b. 1931)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/L%C3%AA_Kh%E1%BA%A3_Phi%C3%AAu\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Vietnamese politician (b. 1931)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/L%C3%AA_Kh%E1%BA%A3_Phi%C3%AAu"}]}, {"year": "2021", "text": "<PERSON><PERSON>, American actress (b. 1950)", "html": "2021 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American actress (b. 1950)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American actress (b. 1950)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>ie_<PERSON>"}]}, {"year": "2021", "text": "<PERSON>, American comedian (b. 1980)", "html": "2021 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(comedian)\" title=\"<PERSON> (comedian)\"><PERSON></a>, American comedian (b. 1980)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(comedian)\" title=\"<PERSON> (comedian)\"><PERSON></a>, American comedian (b. 1980)", "links": [{"title": "<PERSON> (comedian)", "link": "https://wikipedia.org/wiki/<PERSON>(comedian)"}]}, {"year": "2022", "text": "<PERSON>, American historian and author (b. 1933)", "html": "2022 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American historian and author (b. 1933)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American historian and author (b. 1933)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "2023", "text": "<PERSON>, American film director (b. 1935)", "html": "2023 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American film director (b. 1935)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American film director (b. 1935)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2024", "text": "<PERSON>, American astronaut (b. 1943)", "html": "2024 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American astronaut (b. 1943)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American astronaut (b. 1943)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}]}}