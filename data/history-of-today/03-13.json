{"date": "March 13", "url": "https://wikipedia.org/wiki/March_13", "data": {"Events": [{"year": "222", "text": "Roman emperor <PERSON><PERSON><PERSON><PERSON> is murdered alongside his mother, <PERSON>. He is replaced by his 14-year old cousin, <PERSON><PERSON><PERSON>.", "html": "222 - Roman emperor <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a> is murdered alongside his mother, <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>. He is replaced by his 14-year old cousin, <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_Alexander\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>.", "no_year_html": "Roman emperor <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a> is murdered alongside his mother, <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>. He is replaced by his 14-year old cousin, <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>.", "links": [{"title": "<PERSON>aga<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Elagabalus"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_Soaemias"}, {"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "624", "text": "The Battle of Badr, the first major battle between the Muslims and Quraysh.", "html": "624 - The <a href=\"https://wikipedia.org/wiki/Battle_of_Badr\" title=\"Battle of Badr\">Battle of Badr</a>, the first major battle between the Muslims and Quraysh.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Battle_of_Badr\" title=\"Battle of Badr\">Battle of Badr</a>, the first major battle between the Muslims and Quraysh.", "links": [{"title": "Battle of Badr", "link": "https://wikipedia.org/wiki/Battle_of_Badr"}]}, {"year": "1323", "text": "Siege of Warangal: Sultan <PERSON><PERSON><PERSON><PERSON> sends an expeditionary army led by his son, <PERSON>, to the Kakatiya capital Warangal - after ruler <PERSON><PERSON><PERSON><PERSON><PERSON> has refused to make tribute payments. He besieges the city and finally, after a campaign of 8 months, Prataparudra surrenders on November 9.", "html": "1323 - <a href=\"https://wikipedia.org/wiki/Siege_of_Warangal_(1323)\" title=\"Siege of Warangal (1323)\">Siege of Warangal</a>: Sultan <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>-<PERSON>_<PERSON>lu<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON> <PERSON></a> sends an expeditionary army led by his son, <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>luq\" title=\"<PERSON> bin Tughluq\"><PERSON> bin <PERSON>luq</a>, to the Kakatiya capital <a href=\"https://wikipedia.org/wiki/Warangal\" title=\"Warangal\">Warangal</a> - after ruler <a href=\"https://wikipedia.org/wiki/Prataparudra\" title=\"Prataparudra\">P<PERSON><PERSON><PERSON><PERSON></a> has refused to make <a href=\"https://wikipedia.org/wiki/Tribute\" title=\"Tribute\">tribute</a> payments. He besieges the city and finally, after a campaign of 8 months, Prataparudra surrenders on <a href=\"https://wikipedia.org/wiki/November_9\" title=\"November 9\">November 9</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Siege_of_Warangal_(1323)\" title=\"Siege of Warangal (1323)\">Siege of Warangal</a>: Sultan <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>-<PERSON>_<PERSON>lu<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON> <PERSON>\"><PERSON><PERSON><PERSON><PERSON> <PERSON></a> sends an expeditionary army led by his son, <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON> bin Tu<PERSON>luq\"><PERSON> bin <PERSON>lu<PERSON></a>, to the Kakatiya capital <a href=\"https://wikipedia.org/wiki/Warangal\" title=\"Warangal\">Warangal</a> - after ruler <a href=\"https://wikipedia.org/wiki/Prataparudra\" title=\"Prataparudra\">Prata<PERSON>ud<PERSON></a> has refused to make <a href=\"https://wikipedia.org/wiki/Tribute\" title=\"Tribute\">tribute</a> payments. He besieges the city and finally, after a campaign of 8 months, Prataparudra surrenders on <a href=\"https://wikipedia.org/wiki/November_9\" title=\"November 9\">November 9</a>.", "links": [{"title": "Siege of Warangal (1323)", "link": "https://wikipedia.org/wiki/Siege_of_Warangal_(1323)"}, {"title": "<PERSON><PERSON><PERSON><PERSON> <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>-<PERSON>_<PERSON>"}, {"title": "<PERSON> bin <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "Warangal", "link": "https://wikipedia.org/wiki/Warangal"}, {"title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Prataparudra"}, {"title": "Tribute", "link": "https://wikipedia.org/wiki/Tribute"}, {"title": "November 9", "link": "https://wikipedia.org/wiki/November_9"}]}, {"year": "1567", "text": "The Battle of Oosterweel, traditionally regarded as the start of the Eighty Years' War.", "html": "1567 - The <a href=\"https://wikipedia.org/wiki/Battle_of_Oosterweel\" title=\"Battle of Oosterweel\">Battle of Oosterweel</a>, traditionally regarded as the start of the <a href=\"https://wikipedia.org/wiki/Eighty_Years%27_War\" title=\"Eighty Years' War\">Eighty Years' War</a>.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Battle_of_Oosterweel\" title=\"Battle of Oosterweel\">Battle of Oosterweel</a>, traditionally regarded as the start of the <a href=\"https://wikipedia.org/wiki/Eighty_Years%27_War\" title=\"Eighty Years' War\">Eighty Years' War</a>.", "links": [{"title": "Battle of Oosterweel", "link": "https://wikipedia.org/wiki/Battle_of_Oosterweel"}, {"title": "Eighty Years' War", "link": "https://wikipedia.org/wiki/Eighty_Years%27_War"}]}, {"year": "1591", "text": "At the Battle of Tondibi in Mali, Moroccan forces of the Saadi dynasty, led by <PERSON><PERSON>, defeat the Songhai Empire, despite being outnumbered by at least five to one.", "html": "1591 - At the <a href=\"https://wikipedia.org/wiki/Battle_of_Tondibi\" title=\"Battle of Tondibi\">Battle of Tondibi</a> in <a href=\"https://wikipedia.org/wiki/Mali\" title=\"Mali\">Mali</a>, <a href=\"https://wikipedia.org/wiki/Morocco\" title=\"Morocco\">Moroccan</a> forces of the <a href=\"https://wikipedia.org/wiki/Saadi_dynasty\" class=\"mw-redirect\" title=\"Saadi dynasty\">Saadi dynasty</a>, led by <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, defeat the <a href=\"https://wikipedia.org/wiki/Songhai_Empire\" title=\"Songhai Empire\">Songhai Empire</a>, despite being outnumbered by at least five to one.", "no_year_html": "At the <a href=\"https://wikipedia.org/wiki/Battle_of_Tondibi\" title=\"Battle of Tondibi\">Battle of Tondibi</a> in <a href=\"https://wikipedia.org/wiki/Mali\" title=\"Mali\">Mali</a>, <a href=\"https://wikipedia.org/wiki/Morocco\" title=\"Morocco\">Moroccan</a> forces of the <a href=\"https://wikipedia.org/wiki/Saadi_dynasty\" class=\"mw-redirect\" title=\"Saadi dynasty\">Saadi dynasty</a>, led by <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, defeat the <a href=\"https://wikipedia.org/wiki/Songhai_Empire\" title=\"Songhai Empire\">Songhai Empire</a>, despite being outnumbered by at least five to one.", "links": [{"title": "Battle of Tondibi", "link": "https://wikipedia.org/wiki/Battle_of_Tondibi"}, {"title": "Mali", "link": "https://wikipedia.org/wiki/Mali"}, {"title": "Morocco", "link": "https://wikipedia.org/wiki/Morocco"}, {"title": "Saadi dynasty", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_dynasty"}, {"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}, {"title": "Songhai Empire", "link": "https://wikipedia.org/wiki/Songhai_Empire"}]}, {"year": "1639", "text": "Harvard College is named after clergyman <PERSON>.", "html": "1639 - <a href=\"https://wikipedia.org/wiki/Harvard_College\" title=\"Harvard College\">Harvard College</a> is named after clergyman <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(clergyman)\" title=\"<PERSON> (clergyman)\"><PERSON></a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Harvard_College\" title=\"Harvard College\">Harvard College</a> is named after clergyman <a href=\"https://wikipedia.org/wiki/<PERSON>_Harvard_(clergyman)\" title=\"<PERSON> (clergyman)\"><PERSON></a>.", "links": [{"title": "Harvard College", "link": "https://wikipedia.org/wiki/Harvard_College"}, {"title": "<PERSON> (clergyman)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(clergyman)"}]}, {"year": "1697", "text": "Nojpetén, capital of the last independent Maya kingdom, falls to Spanish conquistadors, the final step in the Spanish conquest of Guatemala.", "html": "1697 - <a href=\"https://wikipedia.org/wiki/Nojpet%C3%A9n\" title=\"Nojpetén\">Nojpetén</a>, capital of the last independent <a href=\"https://wikipedia.org/wiki/Itza_people\" title=\"Itza people\">Maya</a> kingdom, falls to Spanish <a href=\"https://wikipedia.org/wiki/Conquistador\" title=\"Conquistador\">conquistadors</a>, the final step in the <a href=\"https://wikipedia.org/wiki/Spanish_conquest_of_Guatemala\" title=\"Spanish conquest of Guatemala\">Spanish conquest of Guatemala</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Nojpet%C3%A9n\" title=\"Nojpetén\">Nojpetén</a>, capital of the last independent <a href=\"https://wikipedia.org/wiki/Itza_people\" title=\"Itza people\">Maya</a> kingdom, falls to Spanish <a href=\"https://wikipedia.org/wiki/Conquistador\" title=\"Conquistador\">conquistadors</a>, the final step in the <a href=\"https://wikipedia.org/wiki/Spanish_conquest_of_Guatemala\" title=\"Spanish conquest of Guatemala\">Spanish conquest of Guatemala</a>.", "links": [{"title": "<PERSON>j<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Nojpet%C3%A9n"}, {"title": "Itza people", "link": "https://wikipedia.org/wiki/Itza_people"}, {"title": "Conquistador", "link": "https://wikipedia.org/wiki/Conquistador"}, {"title": "Spanish conquest of Guatemala", "link": "https://wikipedia.org/wiki/Spanish_conquest_of_Guatemala"}]}, {"year": "1741", "text": "The Battle of Cartagena de Indias (part of the War of Jenkins' Ear) begins.", "html": "1741 - The <a href=\"https://wikipedia.org/wiki/Battle_of_Cartagena_de_Indias\" title=\"Battle of Cartagena de Indias\">Battle of Cartagena de Indias</a> (part of the <a href=\"https://wikipedia.org/wiki/War_of_Jenkins%27_Ear\" title=\"War of Jenkins' Ear\">War of Jenkins' Ear</a>) begins.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Battle_of_Cartagena_de_Indias\" title=\"Battle of Cartagena de Indias\">Battle of Cartagena de Indias</a> (part of the <a href=\"https://wikipedia.org/wiki/War_of_Jenkins%27_Ear\" title=\"War of Jenkins' Ear\">War of Jenkins' Ear</a>) begins.", "links": [{"title": "Battle of Cartagena de Indias", "link": "https://wikipedia.org/wiki/Battle_of_Cartagena_de_Indias"}, {"title": "War of Jenkins' Ear", "link": "https://wikipedia.org/wiki/War_of_Jenkins%27_Ear"}]}, {"year": "1781", "text": "<PERSON> discovers Uranus.", "html": "1781 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> discovers <a href=\"https://wikipedia.org/wiki/Uranus\" title=\"Uranus\">Uranus</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> discovers <a href=\"https://wikipedia.org/wiki/Uranus\" title=\"Uranus\">Uranus</a>.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}, {"title": "Uranus", "link": "https://wikipedia.org/wiki/Uranus"}]}, {"year": "1809", "text": "<PERSON> of Sweden is deposed in the Coup of 1809.", "html": "1809 - <a href=\"https://wikipedia.org/wiki/Gustav_IV_Adolf_of_Sweden\" class=\"mw-redirect\" title=\"Gustav IV Adolf of Sweden\"><PERSON> IV Adolf of Sweden</a> is deposed in the <a href=\"https://wikipedia.org/wiki/Coup_of_1809\" title=\"Coup of 1809\">Coup of 1809</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Gustav_IV_Adolf_of_Sweden\" class=\"mw-redirect\" title=\"Gustav IV Adolf of Sweden\"><PERSON> IV Adolf of Sweden</a> is deposed in the <a href=\"https://wikipedia.org/wiki/Coup_of_1809\" title=\"Coup of 1809\">Coup of 1809</a>.", "links": [{"title": "<PERSON> of Sweden", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_of_Sweden"}, {"title": "Coup of 1809", "link": "https://wikipedia.org/wiki/Coup_of_1809"}]}, {"year": "1811", "text": "A French and Italian fleet is defeated by a British squadron off the island of Vis in the Adriatic during the Napoleonic Wars.", "html": "1811 - A French and Italian fleet <a href=\"https://wikipedia.org/wiki/Battle_of_Lissa_(1811)\" title=\"Battle of Lissa (1811)\">is defeated</a> by a British squadron off the island of <a href=\"https://wikipedia.org/wiki/Vis_(island)\" title=\"Vis (island)\">Vis</a> in the <a href=\"https://wikipedia.org/wiki/Adriatic\" class=\"mw-redirect\" title=\"Adriatic\">Adriatic</a> during the <a href=\"https://wikipedia.org/wiki/Napoleonic_Wars\" title=\"Napoleonic Wars\">Napoleonic Wars</a>.", "no_year_html": "A French and Italian fleet <a href=\"https://wikipedia.org/wiki/Battle_of_Lissa_(1811)\" title=\"Battle of Lissa (1811)\">is defeated</a> by a British squadron off the island of <a href=\"https://wikipedia.org/wiki/Vis_(island)\" title=\"Vis (island)\">Vis</a> in the <a href=\"https://wikipedia.org/wiki/Adriatic\" class=\"mw-redirect\" title=\"Adriatic\">Adriatic</a> during the <a href=\"https://wikipedia.org/wiki/Napoleonic_Wars\" title=\"Napoleonic Wars\">Napoleonic Wars</a>.", "links": [{"title": "Battle of Lissa (1811)", "link": "https://wikipedia.org/wiki/Battle_of_Lissa_(1811)"}, {"title": "Vis (island)", "link": "https://wikipedia.org/wiki/Vis_(island)"}, {"title": "Adriatic", "link": "https://wikipedia.org/wiki/Adriatic"}, {"title": "Napoleonic Wars", "link": "https://wikipedia.org/wiki/Napoleonic_Wars"}]}, {"year": "1815", "text": "Participants at the Congress of Vienna declare <PERSON> an outlaw following his escape from Elba", "html": "1815 - Participants at the <a href=\"https://wikipedia.org/wiki/Congress_of_Vienna\" title=\"Congress of Vienna\">Congress of Vienna</a> declare <a href=\"https://wikipedia.org/wiki/Napoleon\" title=\"<PERSON>\"><PERSON></a> an <a href=\"https://wikipedia.org/wiki/Outlaw\" title=\"Outlaw\">outlaw</a> following his escape from <a href=\"https://wikipedia.org/wiki/Elba\" title=\"Elba\">Elba</a>", "no_year_html": "Participants at the <a href=\"https://wikipedia.org/wiki/Congress_of_Vienna\" title=\"Congress of Vienna\">Congress of Vienna</a> declare <a href=\"https://wikipedia.org/wiki/Napoleon\" title=\"<PERSON>\"><PERSON></a> an <a href=\"https://wikipedia.org/wiki/Outlaw\" title=\"Outlaw\">outlaw</a> following his escape from <a href=\"https://wikipedia.org/wiki/Elba\" title=\"Elba\">Elba</a>", "links": [{"title": "Congress of Vienna", "link": "https://wikipedia.org/wiki/Congress_of_Vienna"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Napoleon"}, {"title": "Outlaw", "link": "https://wikipedia.org/wiki/Outlaw"}, {"title": "Elba", "link": "https://wikipedia.org/wiki/Elba"}]}, {"year": "1826", "text": "<PERSON> <PERSON> publishes the apostolic constitution Quo Graviora in which he renewed the prohibition on Catholics joining freemasonry.", "html": "1826 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"Pope <PERSON>\">Pope <PERSON></a> publishes the <a href=\"https://wikipedia.org/wiki/Apostolic_constitution\" title=\"Apostolic constitution\">apostolic constitution</a> <i><a href=\"https://wikipedia.org/wiki/Quo_graviora_(1826)\" class=\"mw-redirect\" title=\"Quo graviora (1826)\">Quo Graviora</a></i> in which he renewed the prohibition on Catholics joining <a href=\"https://wikipedia.org/wiki/Freemasonry\" title=\"Freemasonry\">freemasonry</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_Leo_<PERSON>\" title=\"Pope <PERSON>\">Pope <PERSON></a> publishes the <a href=\"https://wikipedia.org/wiki/Apostolic_constitution\" title=\"Apostolic constitution\">apostolic constitution</a> <i><a href=\"https://wikipedia.org/wiki/Quo_graviora_(1826)\" class=\"mw-redirect\" title=\"Quo graviora (1826)\">Quo Graviora</a></i> in which he renewed the prohibition on Catholics joining <a href=\"https://wikipedia.org/wiki/Freemasonry\" title=\"Freemasonry\">freemasonry</a>.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "Apostolic constitution", "link": "https://wikipedia.org/wiki/Apostolic_constitution"}, {"title": "Quo graviora (1826)", "link": "https://wikipedia.org/wiki/Quo_graviora_(1826)"}, {"title": "Freemasonry", "link": "https://wikipedia.org/wiki/Freemasonry"}]}, {"year": "1845", "text": "<PERSON>'s Violin Concerto receives its première performance in Leipzig with <PERSON> as soloist.", "html": "1845 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>'s <a href=\"https://wikipedia.org/wiki/Violin_Concerto_(<PERSON><PERSON><PERSON>)\" title=\"Violin Concerto (<PERSON><PERSON><PERSON><PERSON>)\">Violin Concerto</a> receives its première performance in <a href=\"https://wikipedia.org/wiki/Leipzig\" title=\"Leipzig\">Leipzig</a> with <a href=\"https://wikipedia.org/wiki/<PERSON>(musician)\" title=\"<PERSON> (musician)\"><PERSON></a> as soloist.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>'s <a href=\"https://wikipedia.org/wiki/Violin_Concerto_(<PERSON><PERSON><PERSON>)\" title=\"Violin Concerto (<PERSON><PERSON><PERSON><PERSON>)\">Violin Concerto</a> receives its première performance in <a href=\"https://wikipedia.org/wiki/Leipzig\" title=\"Leipzig\">Leipzig</a> with <a href=\"https://wikipedia.org/wiki/<PERSON>(musician)\" title=\"<PERSON> (musician)\"><PERSON></a> as soloist.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Violin Concerto (<PERSON><PERSON><PERSON><PERSON>)", "link": "https://wikipedia.org/wiki/Violin_Concerto_(<PERSON><PERSON><PERSON><PERSON>)"}, {"title": "Leipzig", "link": "https://wikipedia.org/wiki/Leipzig"}, {"title": "<PERSON> (musician)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>(musician)"}]}, {"year": "1848", "text": "The German revolutions of 1848-1849 begin in Vienna.", "html": "1848 - The <a href=\"https://wikipedia.org/wiki/German_revolutions_of_1848%E2%80%931849\" title=\"German revolutions of 1848-1849\">German revolutions of 1848-1849</a> begin in Vienna.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/German_revolutions_of_1848%E2%80%931849\" title=\"German revolutions of 1848-1849\">German revolutions of 1848-1849</a> begin in Vienna.", "links": [{"title": "German revolutions of 1848-1849", "link": "https://wikipedia.org/wiki/German_revolutions_of_1848%E2%80%931849"}]}, {"year": "1862", "text": "The Act Prohibiting the Return of Slaves is passed by the United States Congress, effectively annulling the Fugitive Slave Act of 1850 and setting the stage for the Emancipation Proclamation.", "html": "1862 - The <a href=\"https://wikipedia.org/wiki/Act_Prohibiting_the_Return_of_Slaves\" title=\"Act Prohibiting the Return of Slaves\">Act Prohibiting the Return of Slaves</a> is passed by the <a href=\"https://wikipedia.org/wiki/United_States_Congress\" title=\"United States Congress\">United States Congress</a>, effectively annulling the <a href=\"https://wikipedia.org/wiki/Fugitive_Slave_Act_of_1850\" title=\"Fugitive Slave Act of 1850\">Fugitive Slave Act of 1850</a> and setting the stage for the <a href=\"https://wikipedia.org/wiki/Emancipation_Proclamation\" title=\"Emancipation Proclamation\">Emancipation Proclamation</a>.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Act_Prohibiting_the_Return_of_Slaves\" title=\"Act Prohibiting the Return of Slaves\">Act Prohibiting the Return of Slaves</a> is passed by the <a href=\"https://wikipedia.org/wiki/United_States_Congress\" title=\"United States Congress\">United States Congress</a>, effectively annulling the <a href=\"https://wikipedia.org/wiki/Fugitive_Slave_Act_of_1850\" title=\"Fugitive Slave Act of 1850\">Fugitive Slave Act of 1850</a> and setting the stage for the <a href=\"https://wikipedia.org/wiki/Emancipation_Proclamation\" title=\"Emancipation Proclamation\">Emancipation Proclamation</a>.", "links": [{"title": "Act Prohibiting the Return of Slaves", "link": "https://wikipedia.org/wiki/Act_Prohibiting_the_Return_of_Slaves"}, {"title": "United States Congress", "link": "https://wikipedia.org/wiki/United_States_Congress"}, {"title": "Fugitive Slave Act of 1850", "link": "https://wikipedia.org/wiki/Fugitive_Slave_Act_of_1850"}, {"title": "Emancipation Proclamation", "link": "https://wikipedia.org/wiki/Emancipation_Proclamation"}]}, {"year": "1884", "text": "The Siege of Khartoum begins. It lasts until January 26, 1885.", "html": "1884 - The <a href=\"https://wikipedia.org/wiki/Siege_of_Khartoum\" title=\"Siege of Khartoum\">Siege of Khartoum</a> begins. It lasts until January 26, 1885.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Siege_of_Khartoum\" title=\"Siege of Khartoum\">Siege of Khartoum</a> begins. It lasts until January 26, 1885.", "links": [{"title": "Siege of Khartoum", "link": "https://wikipedia.org/wiki/Siege_of_Khartoum"}]}, {"year": "1888", "text": "The eruption of Ritter Island triggers tsunamis that kill up to 3,000 people on nearby islands.", "html": "1888 - The <a href=\"https://wikipedia.org/wiki/1888_Ritter_Island_eruption_and_tsunami\" title=\"1888 Ritter Island eruption and tsunami\">eruption of Ritter Island</a> triggers <a href=\"https://wikipedia.org/wiki/Tsunami\" title=\"Tsunami\">tsunamis</a> that kill up to 3,000 people on nearby islands.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/1888_Ritter_Island_eruption_and_tsunami\" title=\"1888 Ritter Island eruption and tsunami\">eruption of Ritter Island</a> triggers <a href=\"https://wikipedia.org/wiki/Tsunami\" title=\"Tsunami\">tsunamis</a> that kill up to 3,000 people on nearby islands.", "links": [{"title": "1888 Ritter Island eruption and tsunami", "link": "https://wikipedia.org/wiki/1888_Ritter_Island_eruption_and_tsunami"}, {"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Tsunami"}]}, {"year": "1900", "text": "British forces occupy Bloemfontein, Orange Free State, during the Second Boer War.", "html": "1900 - British forces occupy <a href=\"https://wikipedia.org/wiki/Bloemfontein\" title=\"Bloemfontein\">Bloemfontein</a>, <a href=\"https://wikipedia.org/wiki/Orange_Free_State\" title=\"Orange Free State\">Orange Free State</a>, during the <a href=\"https://wikipedia.org/wiki/Second_Boer_War\" title=\"Second Boer War\">Second Boer War</a>.", "no_year_html": "British forces occupy <a href=\"https://wikipedia.org/wiki/Bloemfontein\" title=\"Bloemfontein\">Bloemfontein</a>, <a href=\"https://wikipedia.org/wiki/Orange_Free_State\" title=\"Orange Free State\">Orange Free State</a>, during the <a href=\"https://wikipedia.org/wiki/Second_Boer_War\" title=\"Second Boer War\">Second Boer War</a>.", "links": [{"title": "Bloemfontein", "link": "https://wikipedia.org/wiki/Bloemfontein"}, {"title": "Orange Free State", "link": "https://wikipedia.org/wiki/Orange_Free_State"}, {"title": "Second Boer War", "link": "https://wikipedia.org/wiki/Second_Boer_War"}]}, {"year": "1920", "text": "The <PERSON><PERSON>sch briefly ousts the Weimar Republic government from Berlin.", "html": "1920 - The <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a> briefly ousts the <a href=\"https://wikipedia.org/wiki/Weimar_Republic\" title=\"Weimar Republic\">Weimar Republic</a> government from <a href=\"https://wikipedia.org/wiki/Berlin\" title=\"Berlin\">Berlin</a>.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a> briefly ousts the <a href=\"https://wikipedia.org/wiki/Weimar_Republic\" title=\"Weimar Republic\">Weimar Republic</a> government from <a href=\"https://wikipedia.org/wiki/Berlin\" title=\"Berlin\">Berlin</a>.", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}, {"title": "Weimar Republic", "link": "https://wikipedia.org/wiki/Weimar_Republic"}, {"title": "Berlin", "link": "https://wikipedia.org/wiki/Berlin"}]}, {"year": "1930", "text": "The news of the discovery of Pluto is announced by Lowell Observatory.", "html": "1930 - The news of the discovery of <a href=\"https://wikipedia.org/wiki/Pluto\" title=\"Pluto\">Pluto</a> is announced by <a href=\"https://wikipedia.org/wiki/Lowell_Observatory\" title=\"Lowell Observatory\">Lowell Observatory</a>.", "no_year_html": "The news of the discovery of <a href=\"https://wikipedia.org/wiki/Pluto\" title=\"Pluto\">Pluto</a> is announced by <a href=\"https://wikipedia.org/wiki/Lowell_Observatory\" title=\"Lowell Observatory\">Lowell Observatory</a>.", "links": [{"title": "Pluto", "link": "https://wikipedia.org/wiki/Pluto"}, {"title": "Lowell Observatory", "link": "https://wikipedia.org/wiki/Lowell_Observatory"}]}, {"year": "1940", "text": "The Winter War between Finland and the Soviet Union officially ends after the signing of the Moscow Peace Treaty.", "html": "1940 - The <a href=\"https://wikipedia.org/wiki/Winter_War\" title=\"Winter War\">Winter War</a> between <a href=\"https://wikipedia.org/wiki/Finland\" title=\"Finland\">Finland</a> and the <a href=\"https://wikipedia.org/wiki/Soviet_Union\" title=\"Soviet Union\">Soviet Union</a> officially ends after the signing of the <a href=\"https://wikipedia.org/wiki/Moscow_Peace_Treaty\" title=\"Moscow Peace Treaty\">Moscow Peace Treaty</a>.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Winter_War\" title=\"Winter War\">Winter War</a> between <a href=\"https://wikipedia.org/wiki/Finland\" title=\"Finland\">Finland</a> and the <a href=\"https://wikipedia.org/wiki/Soviet_Union\" title=\"Soviet Union\">Soviet Union</a> officially ends after the signing of the <a href=\"https://wikipedia.org/wiki/Moscow_Peace_Treaty\" title=\"Moscow Peace Treaty\">Moscow Peace Treaty</a>.", "links": [{"title": "Winter War", "link": "https://wikipedia.org/wiki/Winter_War"}, {"title": "Finland", "link": "https://wikipedia.org/wiki/Finland"}, {"title": "Soviet Union", "link": "https://wikipedia.org/wiki/Soviet_Union"}, {"title": "Moscow Peace Treaty", "link": "https://wikipedia.org/wiki/Moscow_Peace_Treaty"}]}, {"year": "1943", "text": "The Holocaust: German forces liquidate the Jewish ghetto in Kraków.", "html": "1943 - <a href=\"https://wikipedia.org/wiki/The_Holocaust\" title=\"The Holocaust\">The Holocaust</a>: <a href=\"https://wikipedia.org/wiki/Nazi_Germany\" title=\"Nazi Germany\">German</a> forces liquidate the <a href=\"https://wikipedia.org/wiki/Krak%C3%B3w_Ghetto\" title=\"Kraków Ghetto\">Jewish ghetto in Kraków</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/The_Holocaust\" title=\"The Holocaust\">The Holocaust</a>: <a href=\"https://wikipedia.org/wiki/Nazi_Germany\" title=\"Nazi Germany\">German</a> forces liquidate the <a href=\"https://wikipedia.org/wiki/Krak%C3%B3w_Ghetto\" title=\"Kraków Ghetto\">Jewish ghetto in Kraków</a>.", "links": [{"title": "The Holocaust", "link": "https://wikipedia.org/wiki/The_Holocaust"}, {"title": "Nazi Germany", "link": "https://wikipedia.org/wiki/Nazi_Germany"}, {"title": "Kraków Ghetto", "link": "https://wikipedia.org/wiki/Krak%C3%B3w_Ghetto"}]}, {"year": "1954", "text": "The Battle of Điện Biên Phủ begins with an artillery barrage by Viet Minh forces under <PERSON><PERSON>; Viet Minh victory led to the end of the First Indochina War and French withdrawal from Vietnam.", "html": "1954 - The <a href=\"https://wikipedia.org/wiki/Battle_of_Dien_Bien_Phu\" title=\"Battle of Dien Bien Phu\">Battle of Điện Biên Phủ</a> begins with an artillery barrage by <a href=\"https://wikipedia.org/wiki/Viet_Minh\" title=\"Viet Minh\">V<PERSON> Minh</a> forces under <a href=\"https://wikipedia.org/wiki/V%C3%B5_Nguy%C3%AAn_Gi%C3%A1p\" title=\"Võ Nguyên Giáp\"><PERSON><PERSON></a>; Viet Minh victory led to the end of the <a href=\"https://wikipedia.org/wiki/First_Indochina_War\" title=\"First Indochina War\">First Indochina War</a> and French withdrawal from Vietnam.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Battle_of_Dien_Bien_Phu\" title=\"Battle of Dien Bien Phu\">Battle of Điện Biên Phủ</a> begins with an artillery barrage by <a href=\"https://wikipedia.org/wiki/Viet_Minh\" title=\"Viet Minh\"><PERSON><PERSON> Minh</a> forces under <a href=\"https://wikipedia.org/wiki/V%C3%B5_Nguy%C3%AAn_Gi%C3%A1p\" title=\"Võ Nguyên Giáp\">V<PERSON></a>; Viet Minh victory led to the end of the <a href=\"https://wikipedia.org/wiki/First_Indochina_War\" title=\"First Indochina War\">First Indochina War</a> and French withdrawal from Vietnam.", "links": [{"title": "Battle of Dien Bien Phu", "link": "https://wikipedia.org/wiki/Battle_of_Dien_Bien_Phu"}, {"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Viet_Minh"}, {"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/V%C3%B5_Nguy%C3%AAn_Gi%C3%A1p"}, {"title": "First Indochina War", "link": "https://wikipedia.org/wiki/First_Indochina_War"}]}, {"year": "1957", "text": "Cuban student revolutionaries storm the presidential palace in Havana in a failed attempt on the life of President <PERSON><PERSON><PERSON><PERSON>.", "html": "1957 - <a href=\"https://wikipedia.org/wiki/Cuba\" title=\"Cuba\">Cuban</a> student revolutionaries storm the <a href=\"https://wikipedia.org/wiki/Museum_of_the_Revolution_(Cuba)\" title=\"Museum of the Revolution (Cuba)\">presidential palace</a> in <a href=\"https://wikipedia.org/wiki/Havana\" title=\"Havana\">Havana</a> in a failed attempt on the life of President <a href=\"https://wikipedia.org/wiki/Fulgencio_Batista\" title=\"Fulgencio Batista\"><PERSON><PERSON><PERSON>io Batista</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Cuba\" title=\"Cuba\">Cuban</a> student revolutionaries storm the <a href=\"https://wikipedia.org/wiki/Museum_of_the_Revolution_(Cuba)\" title=\"Museum of the Revolution (Cuba)\">presidential palace</a> in <a href=\"https://wikipedia.org/wiki/Havana\" title=\"Havana\">Havana</a> in a failed attempt on the life of President <a href=\"https://wikipedia.org/wiki/Fulgenc<PERSON>_Batista\" title=\"Fulgencio Batista\"><PERSON><PERSON><PERSON><PERSON>ista</a>.", "links": [{"title": "Cuba", "link": "https://wikipedia.org/wiki/Cuba"}, {"title": "Museum of the Revolution (Cuba)", "link": "https://wikipedia.org/wiki/Museum_of_the_Revolution_(Cuba)"}, {"title": "Havana", "link": "https://wikipedia.org/wiki/Havana"}, {"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Fulgencio_Batista"}]}, {"year": "1969", "text": "Apollo 9 returns safely to Earth after testing the Lunar Module.", "html": "1969 - <a href=\"https://wikipedia.org/wiki/Apollo_9\" title=\"Apollo 9\">Apollo 9</a> returns safely to <a href=\"https://wikipedia.org/wiki/Earth\" title=\"Earth\">Earth</a> after testing the <a href=\"https://wikipedia.org/wiki/Apollo_Lunar_Module\" title=\"Apollo Lunar Module\">Lunar Module</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Apollo_9\" title=\"Apollo 9\">Apollo 9</a> returns safely to <a href=\"https://wikipedia.org/wiki/Earth\" title=\"Earth\">Earth</a> after testing the <a href=\"https://wikipedia.org/wiki/Apollo_Lunar_Module\" title=\"Apollo Lunar Module\">Lunar Module</a>.", "links": [{"title": "Apollo 9", "link": "https://wikipedia.org/wiki/Apollo_9"}, {"title": "Earth", "link": "https://wikipedia.org/wiki/Earth"}, {"title": "Apollo Lunar Module", "link": "https://wikipedia.org/wiki/Apollo_Lunar_Module"}]}, {"year": "1974", "text": "Sierra Pacific Airlines Flight 802 crashes into the White Mountains near Bishop, California, killing 36.", "html": "1974 - <a href=\"https://wikipedia.org/wiki/Sierra_Pacific_Airlines_Flight_802\" title=\"Sierra Pacific Airlines Flight 802\">Sierra Pacific Airlines Flight 802</a> crashes into the <a href=\"https://wikipedia.org/wiki/White_Mountains_(California)\" title=\"White Mountains (California)\">White Mountains</a> near <a href=\"https://wikipedia.org/wiki/<PERSON>,_California\" title=\"Bishop, California\">Bishop, California</a>, killing 36.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Sierra_Pacific_Airlines_Flight_802\" title=\"Sierra Pacific Airlines Flight 802\">Sierra Pacific Airlines Flight 802</a> crashes into the <a href=\"https://wikipedia.org/wiki/White_Mountains_(California)\" title=\"White Mountains (California)\">White Mountains</a> near <a href=\"https://wikipedia.org/wiki/<PERSON>,_California\" title=\"Bishop, California\">Bishop, California</a>, killing 36.", "links": [{"title": "Sierra Pacific Airlines Flight 802", "link": "https://wikipedia.org/wiki/Sierra_Pacific_Airlines_Flight_802"}, {"title": "White Mountains (California)", "link": "https://wikipedia.org/wiki/White_Mountains_(California)"}, {"title": "<PERSON>, California", "link": "https://wikipedia.org/wiki/<PERSON>,_California"}]}, {"year": "1979", "text": "The New Jewel Movement, headed by <PERSON>, ousts the Prime Minister of Grenada, <PERSON>, in a coup d'état.", "html": "1979 - The <a href=\"https://wikipedia.org/wiki/New_Jewel_Movement\" title=\"New Jewel Movement\">New Jewel Movement</a>, headed by <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, ousts the <a href=\"https://wikipedia.org/wiki/List_of_heads_of_government_of_Grenada\" title=\"List of heads of government of Grenada\">Prime Minister of Grenada</a>, <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, in a <a href=\"https://wikipedia.org/wiki/Coup_d%27%C3%A9tat\" title=\"Coup d'état\">coup d'état</a>.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/New_Jewel_Movement\" title=\"New Jewel Movement\">New Jewel Movement</a>, headed by <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, ousts the <a href=\"https://wikipedia.org/wiki/List_of_heads_of_government_of_Grenada\" title=\"List of heads of government of Grenada\">Prime Minister of Grenada</a>, <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, in a <a href=\"https://wikipedia.org/wiki/Coup_d%27%C3%A9tat\" title=\"Coup d'état\">coup d'état</a>.", "links": [{"title": "New Jewel Movement", "link": "https://wikipedia.org/wiki/New_Jewel_Movement"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "List of heads of government of Grenada", "link": "https://wikipedia.org/wiki/List_of_heads_of_government_of_Grenada"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}, {"title": "Coup d'état", "link": "https://wikipedia.org/wiki/Coup_d%27%C3%A9tat"}]}, {"year": "1988", "text": "The Seikan Tunnel, the longest tunnel in the world with an undersea segment, opens between Aomori and Hakodate, Japan.", "html": "1988 - The <a href=\"https://wikipedia.org/wiki/Seikan_Tunnel\" title=\"Seikan Tunnel\">Seikan Tunnel</a>, the longest tunnel in the world with an undersea segment, opens between <a href=\"https://wikipedia.org/wiki/Aomori\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a> and <a href=\"https://wikipedia.org/wiki/Hakodate\" title=\"Hakodate\">Hakodate</a>, Japan.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Seikan_Tunnel\" title=\"Seikan Tunnel\">Seikan Tunnel</a>, the longest tunnel in the world with an undersea segment, opens between <a href=\"https://wikipedia.org/wiki/Aomori\" title=\"Aomo<PERSON>\">A<PERSON><PERSON></a> and <a href=\"https://wikipedia.org/wiki/Hakodate\" title=\"Hakodate\">Hakodate</a>, Japan.", "links": [{"title": "Seikan Tunnel", "link": "https://wikipedia.org/wiki/Seikan_Tunnel"}, {"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Aomori"}, {"title": "Hakodate", "link": "https://wikipedia.org/wiki/Hakodate"}]}, {"year": "1989", "text": "Space Shuttle Discovery launches on STS-29 carrying the TDRS-4 satellite.", "html": "1989 - <a href=\"https://wikipedia.org/wiki/Space_Shuttle_Discovery\" title=\"Space Shuttle Discovery\">Space Shuttle <i>Discovery</i></a> launches on <a href=\"https://wikipedia.org/wiki/STS-29\" title=\"STS-29\">STS-29</a> carrying the <a href=\"https://wikipedia.org/wiki/TDRS-4\" title=\"TDRS-4\">TDRS-4</a> satellite.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Space_Shuttle_Discovery\" title=\"Space Shuttle Discovery\">Space Shuttle <i>Discovery</i></a> launches on <a href=\"https://wikipedia.org/wiki/STS-29\" title=\"STS-29\">STS-29</a> carrying the <a href=\"https://wikipedia.org/wiki/TDRS-4\" title=\"TDRS-4\">TDRS-4</a> satellite.", "links": [{"title": "Space Shuttle Discovery", "link": "https://wikipedia.org/wiki/Space_Shuttle_Discovery"}, {"title": "STS-29", "link": "https://wikipedia.org/wiki/STS-29"}, {"title": "TDRS-4", "link": "https://wikipedia.org/wiki/TDRS-4"}]}, {"year": "1992", "text": "The Mw  6.6 Erzincan earthquake strikes eastern Turkey with a maximum Mercalli intensity of VIII (Severe).", "html": "1992 - The M<sub>w</sub>  6.6 <a href=\"https://wikipedia.org/wiki/1992_Erzincan_earthquake\" title=\"1992 Erzincan earthquake\">Erzincan earthquake</a> strikes eastern <a href=\"https://wikipedia.org/wiki/Turkey\" title=\"Turkey\">Turkey</a> with a maximum <a href=\"https://wikipedia.org/wiki/Modified_Mercalli_intensity_scale\" title=\"Modified Mercalli intensity scale\">Mercalli intensity</a> of VIII (<i>Severe</i>).", "no_year_html": "The M<sub>w</sub>  6.6 <a href=\"https://wikipedia.org/wiki/1992_Erzincan_earthquake\" title=\"1992 Erzincan earthquake\">Erzincan earthquake</a> strikes eastern <a href=\"https://wikipedia.org/wiki/Turkey\" title=\"Turkey\">Turkey</a> with a maximum <a href=\"https://wikipedia.org/wiki/Modified_Mercalli_intensity_scale\" title=\"Modified Mercalli intensity scale\">Mercalli intensity</a> of VIII (<i>Severe</i>).", "links": [{"title": "1992 Erzincan earthquake", "link": "https://wikipedia.org/wiki/1992_Erzincan_earthquake"}, {"title": "Turkey", "link": "https://wikipedia.org/wiki/Turkey"}, {"title": "Modified Mercalli intensity scale", "link": "https://wikipedia.org/wiki/Modified_Mercalli_intensity_scale"}]}, {"year": "1993", "text": "The 1993 Storm of the Century affects the eastern United States, dropping feet of snow in many areas.", "html": "1993 - The <a href=\"https://wikipedia.org/wiki/1993_Storm_of_the_Century\" title=\"1993 Storm of the Century\">1993 Storm of the Century</a> affects the <a href=\"https://wikipedia.org/wiki/Eastern_United_States\" title=\"Eastern United States\">eastern United States</a>, dropping feet of snow in many areas.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/1993_Storm_of_the_Century\" title=\"1993 Storm of the Century\">1993 Storm of the Century</a> affects the <a href=\"https://wikipedia.org/wiki/Eastern_United_States\" title=\"Eastern United States\">eastern United States</a>, dropping feet of snow in many areas.", "links": [{"title": "1993 Storm of the Century", "link": "https://wikipedia.org/wiki/1993_Storm_of_the_Century"}, {"title": "Eastern United States", "link": "https://wikipedia.org/wiki/Eastern_United_States"}]}, {"year": "1996", "text": "The Dunblane massacre leads to the death of sixteen primary school children and one teacher in Dunblane, Scotland.", "html": "1996 - The <a href=\"https://wikipedia.org/wiki/Dunblane_massacre\" title=\"Dunblane massacre\">Dunblane massacre</a> leads to the death of sixteen primary school children and one teacher in <a href=\"https://wikipedia.org/wiki/Dunblane\" title=\"Dunblane\">Dunblane</a>, Scotland.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Dunblane_massacre\" title=\"Dunblane massacre\">Dunblane massacre</a> leads to the death of sixteen primary school children and one teacher in <a href=\"https://wikipedia.org/wiki/Dunblane\" title=\"Dunblane\">Dunblane</a>, Scotland.", "links": [{"title": "Dunblane massacre", "link": "https://wikipedia.org/wiki/Dun<PERSON>ne_massacre"}, {"title": "Dunblane", "link": "https://wikipedia.org/wiki/Dunblane"}]}, {"year": "1997", "text": "The Missionaries of Charity choose <PERSON> to succeed <PERSON> as their leader.", "html": "1997 - The <a href=\"https://wikipedia.org/wiki/Missionaries_of_Charity\" title=\"Missionaries of Charity\">Missionaries of Charity</a> choose <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON> Josh<PERSON>\">Sister <PERSON></a> to succeed <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> as their leader.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Missionaries_of_Charity\" title=\"Missionaries of Charity\">Missionaries of Charity</a> choose <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON> Joshi\"><PERSON></a> to succeed <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> as their leader.", "links": [{"title": "Missionaries of Charity", "link": "https://wikipedia.org/wiki/Missionaries_of_Charity"}, {"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>i"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2003", "text": "An article in Nature identifies the Ciampate del Diavolo as 350,000-year-old hominid footprints.", "html": "2003 - An article in <i><a href=\"https://wikipedia.org/wiki/Nature_(journal)\" title=\"Nature (journal)\">Nature</a></i> identifies the <a href=\"https://wikipedia.org/wiki/Ciampate_del_Diavolo\" title=\"Ciampate del Diavolo\">Ciampate del Diavolo</a> as 350,000-year-old hominid footprints.", "no_year_html": "An article in <i><a href=\"https://wikipedia.org/wiki/Nature_(journal)\" title=\"Nature (journal)\">Nature</a></i> identifies the <a href=\"https://wikipedia.org/wiki/Ciampate_del_Diavolo\" title=\"Ciampate del Diavolo\">Ciampate del Diavolo</a> as 350,000-year-old hominid footprints.", "links": [{"title": "Nature (journal)", "link": "https://wikipedia.org/wiki/Nature_(journal)"}, {"title": "Ciampate del Diavolo", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>"}]}, {"year": "2012", "text": "The Sierre coach crash kills 28 people, including 22 children.", "html": "2012 - The <a href=\"https://wikipedia.org/wiki/Sierre_coach_crash\" title=\"Sierre coach crash\">Sierre coach crash</a> kills 28 people, including 22 children.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Sierre_coach_crash\" title=\"Sierre coach crash\">Sierre coach crash</a> kills 28 people, including 22 children.", "links": [{"title": "Sierre coach crash", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_coach_crash"}]}, {"year": "2013", "text": "The 2013 papal conclave elects Cardinal <PERSON> taking the name <PERSON> as the 266th Pope of the Catholic Church.", "html": "2013 - The <a href=\"https://wikipedia.org/wiki/2013_papal_conclave\" title=\"2013 papal conclave\">2013 papal conclave</a> elects Cardinal <PERSON> taking the name <a href=\"https://wikipedia.org/wiki/Pope_<PERSON>\" title=\"Pope <PERSON>\">Pope <PERSON></a> as the <a href=\"https://wikipedia.org/wiki/List_of_popes\" title=\"List of popes\">266th</a> <a href=\"https://wikipedia.org/wiki/Pope\" title=\"Pope\">Pope</a> of the <a href=\"https://wikipedia.org/wiki/Catholic_Church\" title=\"Catholic Church\">Catholic Church</a>.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/2013_papal_conclave\" title=\"2013 papal conclave\">2013 papal conclave</a> elects Cardinal <PERSON> taking the name <a href=\"https://wikipedia.org/wiki/Pope_<PERSON>\" title=\"Pope <PERSON>\">Pope <PERSON></a> as the <a href=\"https://wikipedia.org/wiki/List_of_popes\" title=\"List of popes\">266th</a> <a href=\"https://wikipedia.org/wiki/Pope\" title=\"Pope\">Pope</a> of the <a href=\"https://wikipedia.org/wiki/Catholic_Church\" title=\"Catholic Church\">Catholic Church</a>.", "links": [{"title": "2013 papal conclave", "link": "https://wikipedia.org/wiki/2013_papal_conclave"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "List of popes", "link": "https://wikipedia.org/wiki/List_of_popes"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Pope"}, {"title": "Catholic Church", "link": "https://wikipedia.org/wiki/Catholic_Church"}]}, {"year": "2016", "text": "The Ankara bombing kills at least 37 people.", "html": "2016 - The <a href=\"https://wikipedia.org/wiki/March_2016_Ankara_bombing\" title=\"March 2016 Ankara bombing\">Ankara bombing</a> kills at least 37 people.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/March_2016_Ankara_bombing\" title=\"March 2016 Ankara bombing\">Ankara bombing</a> kills at least 37 people.", "links": [{"title": "March 2016 Ankara bombing", "link": "https://wikipedia.org/wiki/March_2016_Ankara_bombing"}]}, {"year": "2016", "text": "Three gunmen attack two hotels in the Ivory Coast town of Grand-Bassam, killing at least 19 people.", "html": "2016 - Three gunmen <a href=\"https://wikipedia.org/wiki/Grand-Bassam_shootings\" title=\"Grand-Bassam shootings\">attack</a> two hotels in the <a href=\"https://wikipedia.org/wiki/Ivory_Coast\" title=\"Ivory Coast\">Ivory Coast</a> town of <a href=\"https://wikipedia.org/wiki/Grand-Bassam\" title=\"Grand-Bassam\">Grand-Bassam</a>, killing at least 19 people.", "no_year_html": "Three gunmen <a href=\"https://wikipedia.org/wiki/Grand-Bassam_shootings\" title=\"Grand-Bassam shootings\">attack</a> two hotels in the <a href=\"https://wikipedia.org/wiki/Ivory_Coast\" title=\"Ivory Coast\">Ivory Coast</a> town of <a href=\"https://wikipedia.org/wiki/Grand-Bassam\" title=\"Grand-Bassam\">Grand-Bassam</a>, killing at least 19 people.", "links": [{"title": "Grand-Bassam shootings", "link": "https://wikipedia.org/wiki/Grand-<PERSON><PERSON>_shootings"}, {"title": "Ivory Coast", "link": "https://wikipedia.org/wiki/Ivory_Coast"}, {"title": "<PERSON>-<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>"}]}, {"year": "2020", "text": "President <PERSON> declares the COVID-19 pandemic to be a national emergency in the United States.", "html": "2020 - President <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> declares the <a href=\"https://wikipedia.org/wiki/COVID-19_pandemic_in_the_United_States\" title=\"COVID-19 pandemic in the United States\">COVID-19 pandemic</a> to be a <a href=\"https://wikipedia.org/wiki/State_of_emergency\" title=\"State of emergency\">national emergency</a> in the United States.", "no_year_html": "President <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> declares the <a href=\"https://wikipedia.org/wiki/COVID-19_pandemic_in_the_United_States\" title=\"COVID-19 pandemic in the United States\">COVID-19 pandemic</a> to be a <a href=\"https://wikipedia.org/wiki/State_of_emergency\" title=\"State of emergency\">national emergency</a> in the United States.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}, {"title": "COVID-19 pandemic in the United States", "link": "https://wikipedia.org/wiki/COVID-19_pandemic_in_the_United_States"}, {"title": "State of emergency", "link": "https://wikipedia.org/wiki/State_of_emergency"}]}, {"year": "2020", "text": "<PERSON><PERSON><PERSON><PERSON> is killed by police officers who were forcibly entering her home in Louisville, Kentucky; her death sparked extensive protests against racism and police brutality.", "html": "2020 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a> is <a href=\"https://wikipedia.org/wiki/Killing_of_<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"Killing of <PERSON><PERSON><PERSON><PERSON>\">killed</a> by police officers who were forcibly entering her home in <a href=\"https://wikipedia.org/wiki/Louisville,_Kentucky\" title=\"Louisville, Kentucky\">Louisville, Kentucky</a>; her death sparked <a href=\"https://wikipedia.org/wiki/B<PERSON><PERSON><PERSON>_<PERSON>_protests\" title=\"B<PERSON><PERSON><PERSON> protests\">extensive protests</a> against racism and police brutality.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a> is <a href=\"https://wikipedia.org/wiki/Killing_of_<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"Killing of <PERSON><PERSON><PERSON><PERSON>\">killed</a> by police officers who were forcibly entering her home in <a href=\"https://wikipedia.org/wiki/Louisville,_Kentucky\" title=\"Louisville, Kentucky\">Louisville, Kentucky</a>; her death sparked <a href=\"https://wikipedia.org/wiki/B<PERSON><PERSON><PERSON>_<PERSON>_protests\" title=\"B<PERSON><PERSON><PERSON> protests\">extensive protests</a> against racism and police brutality.", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>"}, {"title": "Killing of <PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Killing_of_<PERSON><PERSON><PERSON><PERSON>_<PERSON>"}, {"title": "Louisville, Kentucky", "link": "https://wikipedia.org/wiki/Louisville,_Kentucky"}, {"title": "<PERSON><PERSON><PERSON><PERSON> protests", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>_protests"}]}, {"year": "2020", "text": "<PERSON><PERSON> is sworn in as the first female President of Greece amid strict COVID-19 measures.", "html": "2020 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a> is sworn in as the first female <a href=\"https://wikipedia.org/wiki/President_of_Greece\" title=\"President of Greece\">President of Greece</a> amid strict <a href=\"https://wikipedia.org/wiki/COVID-19_in_Greece\" class=\"mw-redirect\" title=\"COVID-19 in Greece\">COVID-19 measures</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a> is sworn in as the first female <a href=\"https://wikipedia.org/wiki/President_of_Greece\" title=\"President of Greece\">President of Greece</a> amid strict <a href=\"https://wikipedia.org/wiki/COVID-19_in_Greece\" class=\"mw-redirect\" title=\"COVID-19 in Greece\">COVID-19 measures</a>.", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}, {"title": "President of Greece", "link": "https://wikipedia.org/wiki/President_of_Greece"}, {"title": "COVID-19 in Greece", "link": "https://wikipedia.org/wiki/COVID-19_in_Greece"}]}], "Births": [{"year": "1372", "text": "<PERSON>, Duke of Orléans (d. 1407)", "html": "1372 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Duke_of_Orl%C3%A9ans\" title=\"<PERSON>, Duke of Orléans\"><PERSON>, Duke of Orléans</a> (d. 1407)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Duke_of_Orl%C3%A9ans\" title=\"<PERSON>, Duke of Orléans\"><PERSON>, Duke of Orléans</a> (d. 1407)", "links": [{"title": "<PERSON>, Duke of Orléans", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_<PERSON>_<PERSON>_Orl%C3%A9ans"}]}, {"year": "1479", "text": "<PERSON>, German hymnwriter (d. 1534)", "html": "1479 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>gler\"><PERSON></a>, German hymnwriter (d. 1534)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German hymnwriter (d. 1534)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1560", "text": "<PERSON>, Count of Nassau-Dillenburg, Dutch count (d. 1620)", "html": "1560 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Count_of_Nassau-Dillenburg\" title=\"<PERSON>, Count of Nassau-Dillenburg\"><PERSON>, Count of Nassau-Dillenburg</a>, Dutch count (d. 1620)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Count_of_Nassau-Dillenburg\" title=\"<PERSON>, Count of Nassau-Dillenburg\"><PERSON>, Count of Nassau-Dillenburg</a>, Dutch count (d. 1620)", "links": [{"title": "<PERSON>, Count of Nassau-Dillenburg", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Count_of_Nassau-Dillenburg"}]}, {"year": "1593", "text": "<PERSON>, French painter (probable; d. 1652)", "html": "1593 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_Tour\" title=\"Georges de La Tour\"><PERSON></a>, French painter (probable; d. 1652)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>\" title=\"Georges de La Tour\"><PERSON></a>, French painter (probable; d. 1652)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1599", "text": "<PERSON>, Belgian Jesuit scholastic and saint (d. 1621)", "html": "1599 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Belgian Jesuit scholastic and saint (d. 1621)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Belgian Jesuit scholastic and saint (d. 1621)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1615", "text": "<PERSON>, pope of the Catholic Church (d. 1700)", "html": "1615 - <a href=\"https://wikipedia.org/wiki/Pope_Innocent_XII\" title=\"Pope Innocent XII\"><PERSON></a>, pope of the Catholic Church (d. 1700)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_XII\" title=\"Pope Innocent XII\"><PERSON></a>, pope of the Catholic Church (d. 1700)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_Innocent_XII"}]}, {"year": "1683", "text": "<PERSON>, German botanist (d. 1741)", "html": "1683 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German botanist (d. 1741)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German botanist (d. 1741)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1700", "text": "<PERSON>, French flute player and composer (d. 1768)", "html": "1700 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French flute player and composer (d. 1768)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French flute player and composer (d. 1768)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1719", "text": "<PERSON>, 4th Baron <PERSON>, English field marshal and politician, Lord Lieutenant of Essex (d. 1797)", "html": "1719 - <a href=\"https://wikipedia.org/wiki/<PERSON>,_4th_Baron_<PERSON>_<PERSON>\" title=\"<PERSON>, 4th Baron <PERSON>\"><PERSON>, 4th Baron <PERSON></a>, English field marshal and politician, <a href=\"https://wikipedia.org/wiki/Lord_Lieutenant_of_Essex\" title=\"Lord Lieutenant of Essex\">Lord Lieutenant of Essex</a> (d. 1797)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>,_4th_Baron_<PERSON>_<PERSON>\" title=\"<PERSON>, 4th Baron <PERSON>\"><PERSON>, 4th Baron <PERSON></a>, English field marshal and politician, <a href=\"https://wikipedia.org/wiki/Lord_Lieutenant_of_Essex\" title=\"Lord Lieutenant of Essex\">Lord Lieutenant of Essex</a> (d. 1797)", "links": [{"title": "<PERSON>, 4th Baron <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>,_4th_Baron_<PERSON>_<PERSON>_<PERSON>"}, {"title": "Lord Lieutenant of Essex", "link": "https://wikipedia.org/wiki/Lord_Lieutenant_of_Essex"}]}, {"year": "1720", "text": "<PERSON>, Swiss historian and author (d. 1793)", "html": "1720 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swiss historian and author (d. 1793)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swiss historian and author (d. 1793)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1741", "text": "<PERSON>, Holy Roman Emperor (d. 1790)", "html": "1741 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Holy_Roman_Emperor\" title=\"<PERSON> II, Holy Roman Emperor\"><PERSON>, Holy Roman Emperor</a> (d. 1790)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Holy_Roman_Emperor\" title=\"<PERSON> II, Holy Roman Emperor\"><PERSON>, Holy Roman Emperor</a> (d. 1790)", "links": [{"title": "<PERSON>, Holy Roman Emperor", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Holy_Roman_Emperor"}]}, {"year": "1763", "text": "<PERSON>, French general and diplomat (d. 1815)", "html": "1763 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French general and diplomat (d. 1815)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French general and diplomat (d. 1815)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1764", "text": "<PERSON>, 2nd Earl <PERSON>, English politician, Prime Minister of the United Kingdom (d. 1845)", "html": "1764 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_2nd_Earl_<PERSON>\" title=\"<PERSON>, 2nd Earl <PERSON>\"><PERSON>, 2nd Earl <PERSON></a>, English politician, <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_the_United_Kingdom\" title=\"Prime Minister of the United Kingdom\">Prime Minister of the United Kingdom</a> (d. 1845)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>,_2nd_Earl_<PERSON>\" title=\"<PERSON>, 2nd Earl <PERSON>\"><PERSON>, 2nd Earl <PERSON></a>, English politician, <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_the_United_Kingdom\" title=\"Prime Minister of the United Kingdom\">Prime Minister of the United Kingdom</a> (d. 1845)", "links": [{"title": "<PERSON>, 2nd Earl <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>,_2nd_Earl_<PERSON>"}, {"title": "Prime Minister of the United Kingdom", "link": "https://wikipedia.org/wiki/Prime_Minister_of_the_United_Kingdom"}]}, {"year": "1770", "text": "<PERSON>, English animal breeder (d. 1809)", "html": "1770 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English animal breeder (d. 1809)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English animal breeder (d. 1809)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1781", "text": "<PERSON>, German painter and architect, designed the Konzerthaus Berlin (d. 1841)", "html": "1781 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German painter and architect, designed the <a href=\"https://wikipedia.org/wiki/Konzerthaus_Berlin\" title=\"Konzerthaus Berlin\">Konzerthaus Berlin</a> (d. 1841)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German painter and architect, designed the <a href=\"https://wikipedia.org/wiki/Konzerthaus_Berlin\" title=\"Konzerthaus Berlin\">Konzerthaus Berlin</a> (d. 1841)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Konzerthaus Berlin", "link": "https://wikipedia.org/wiki/Konzerthaus_Berlin"}]}, {"year": "1798", "text": "<PERSON>, American wife of <PERSON><PERSON>, 14th First Lady of the United States (d. 1853)", "html": "1798 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American wife of <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>llmore\" title=\"<PERSON><PERSON>ll<PERSON>\"><PERSON><PERSON></a>, 14th <a href=\"https://wikipedia.org/wiki/First_Lady_of_the_United_States\" title=\"First Lady of the United States\">First Lady of the United States</a> (d. 1853)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American wife of <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>llmore\" title=\"<PERSON><PERSON> Fillmore\"><PERSON><PERSON></a>, 14th <a href=\"https://wikipedia.org/wiki/First_Lady_of_the_United_States\" title=\"First Lady of the United States\">First Lady of the United States</a> (d. 1853)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Millard_Fillmore"}, {"title": "First Lady of the United States", "link": "https://wikipedia.org/wiki/First_Lady_of_the_United_States"}]}, {"year": "1800", "text": "<PERSON>, Ottoman politician, 212th Grand Vizier of the Ottoman Empire (d. 1858)", "html": "1800 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C5%9<PERSON><PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Ottoman politician, 212th <a href=\"https://wikipedia.org/wiki/List_of_Ottoman_Grand_Viziers\" class=\"mw-redirect\" title=\"List of Ottoman Grand Viziers\">Grand Vizier of the Ottoman Empire</a> (d. 1858)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C5%9F<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Ottoman politician, 212th <a href=\"https://wikipedia.org/wiki/List_of_Ottoman_Grand_Viziers\" class=\"mw-redirect\" title=\"List of Ottoman Grand Viziers\">Grand Vizier of the Ottoman Empire</a> (d. 1858)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Mustafa_Re%C5%9Fid_<PERSON>"}, {"title": "List of Ottoman Grand Viziers", "link": "https://wikipedia.org/wiki/List_of_Ottoman_Grand_Viziers"}]}, {"year": "1815", "text": "<PERSON>, American physician, linguist, and missionary (d. 1911)", "html": "1815 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American physician, linguist, and missionary (d. 1911)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American physician, linguist, and missionary (d. 1911)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1825", "text": "<PERSON>, Norwegian-German painter and academic (d. 1903)", "html": "1825 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Norwegian-German painter and academic (d. 1903)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Norwegian-German painter and academic (d. 1903)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1855", "text": "<PERSON><PERSON><PERSON>, American astronomer and mathematician (d. 1916)", "html": "1855 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"Percival <PERSON>\"><PERSON><PERSON><PERSON></a>, American astronomer and mathematician (d. 1916)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"Per<PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American astronomer and mathematician (d. 1916)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Percival_<PERSON>"}]}, {"year": "1857", "text": "<PERSON><PERSON> <PERSON><PERSON>, English-American historian and politician (d. 1933)", "html": "1857 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> <PERSON><PERSON>\"><PERSON><PERSON> <PERSON><PERSON></a>, English-American historian and politician (d. 1933)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> <PERSON><PERSON>\"><PERSON><PERSON> <PERSON><PERSON></a>, English-American historian and politician (d. 1933)", "links": [{"title": "<PERSON><PERSON> <PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "1860", "text": "<PERSON>, Slovene-Austrian composer (d. 1903)", "html": "1860 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Slovene-Austrian composer (d. 1903)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Wolf\"><PERSON></a>, Slovene-Austrian composer (d. 1903)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1862", "text": "<PERSON>, French general (d. 1943)", "html": "1862 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French general (d. 1943)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French general (d. 1943)", "links": [{"title": "<PERSON> Pro<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "1864", "text": "<PERSON><PERSON>, Russian-German painter (d. 1941)", "html": "1864 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Russian-German painter (d. 1941)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Russian-German painter (d. 1941)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1870", "text": "<PERSON>, American painter and illustrator (d. 1938)", "html": "1870 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American painter and illustrator (d. 1938)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American painter and illustrator (d. 1938)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1874", "text": "<PERSON><PERSON>, American jumper, coach, and lawyer (d. 1949)", "html": "1874 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American jumper, coach, and lawyer (d. 1949)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American jumper, coach, and lawyer (d. 1949)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1880", "text": "<PERSON>, Czech architect (d. 1945)", "html": "1880 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C4%8D%C3%A1r\" title=\"<PERSON>\"><PERSON></a>, Czech architect (d. 1945)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C4%8D%C3%A1r\" title=\"<PERSON>\"><PERSON></a>, Czech architect (d. 1945)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_Go%C4%8D%C3%A1r"}]}, {"year": "1883", "text": "<PERSON>, Italian pianist and composer (d. 1926)", "html": "1883 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian pianist and composer (d. 1926)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian pianist and composer (d. 1926)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1884", "text": "<PERSON>, New Zealand-English author and educator (d. 1941)", "html": "1884 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, New Zealand-English author and educator (d. 1941)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, New Zealand-English author and educator (d. 1941)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1886", "text": "<PERSON> <PERSON>, American baseball player and manager (d. 1963)", "html": "1886 - <a href=\"https://wikipedia.org/wiki/Home_Run_Baker\" title=\"Home Run Baker\"><PERSON> <PERSON></a>, American baseball player and manager (d. 1963)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Home_Run_Baker\" title=\"Home Run Baker\"><PERSON> <PERSON></a>, American baseball player and manager (d. 1963)", "links": [{"title": "Home Run Baker", "link": "https://wikipedia.org/wiki/Home_Run_Baker"}]}, {"year": "1886", "text": "<PERSON>, American captain and photographer (d. 1949)", "html": "1886 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American captain and photographer (d. 1949)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American captain and photographer (d. 1949)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1888", "text": "<PERSON>, French author and diplomat (d. 1976)", "html": "1888 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French author and diplomat (d. 1976)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French author and diplomat (d. 1976)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1890", "text": "<PERSON>, German conductor and director (d. 1951)", "html": "1890 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German conductor and director (d. 1951)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German conductor and director (d. 1951)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1892", "text": "<PERSON>, American journalist and author (d. 1978)", "html": "1892 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American journalist and author (d. 1978)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American journalist and author (d. 1978)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1897", "text": "<PERSON><PERSON><PERSON><PERSON>, Armenian poet and activist (d. 1937)", "html": "1897 - <a href=\"https://wikipedia.org/wiki/Yegh<PERSON>e_Charents\" title=\"Yegh<PERSON><PERSON> Charents\"><PERSON><PERSON><PERSON><PERSON></a>, Armenian poet and activist (d. 1937)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Yegh<PERSON><PERSON>_Charents\" title=\"Yegh<PERSON><PERSON> Charents\"><PERSON><PERSON><PERSON><PERSON></a>, Armenian poet and activist (d. 1937)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Yegh<PERSON><PERSON>_<PERSON>rents"}]}, {"year": "1898", "text": "<PERSON>, American director and producer (d. 1985)", "html": "1898 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American director and producer (d. 1985)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American director and producer (d. 1985)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1899", "text": "<PERSON>, American physicist and mathematician, Nobel Prize laureate (d. 1980)", "html": "1899 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American physicist and mathematician, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Physics\" title=\"Nobel Prize in Physics\">Nobel Prize</a> laureate (d. 1980)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American physicist and mathematician, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Physics\" title=\"Nobel Prize in Physics\">Nobel Prize</a> laureate (d. 1980)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>"}, {"title": "Nobel Prize in Physics", "link": "https://wikipedia.org/wiki/Nobel_Prize_in_Physics"}]}, {"year": "1899", "text": "<PERSON><PERSON>, Bulgarian pianist and composer (d. 1978)", "html": "1899 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Bulgarian pianist and composer (d. 1978)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Bulgarian pianist and composer (d. 1978)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1900", "text": "<PERSON><PERSON>, Belgian painter (d. 1980)", "html": "1900 - <a href=\"https://wikipedia.org/wiki/Andr%C3%A9e_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Belgian painter (d. 1980)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Andr%C3%A9e_<PERSON>t\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Belgian painter (d. 1980)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Andr%C3%A9e_<PERSON>squet"}]}, {"year": "1900", "text": "<PERSON><PERSON><PERSON>, Greek poet and diplomat, Nobel Prize laureate (d. 1971)", "html": "1900 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Greek poet and diplomat, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Literature\" title=\"Nobel Prize in Literature\">Nobel Prize</a> laureate (d. 1971)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Greek poet and diplomat, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Literature\" title=\"Nobel Prize in Literature\">Nobel Prize</a> laureate (d. 1971)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>is"}, {"title": "Nobel Prize in Literature", "link": "https://wikipedia.org/wiki/Nobel_Prize_in_Literature"}]}, {"year": "1902", "text": "<PERSON>, German-French painter and sculptor (d. 1975)", "html": "1902 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German-French painter and sculptor (d. 1975)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German-French painter and sculptor (d. 1975)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1904", "text": "<PERSON>, Trinidadian cricketer and footballer (d. 1988)", "html": "1904 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Trinidadian cricketer and footballer (d. 1988)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Trinidadian cricketer and footballer (d. 1988)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1907", "text": "<PERSON>, Australian politician (d. 1985)", "html": "1907 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian politician (d. 1985)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian politician (d. 1985)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1908", "text": "<PERSON>, American publisher, philanthropist, and diplomat, United States Ambassador to the United Kingdom (d. 2002)", "html": "1908 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American publisher, philanthropist, and diplomat, <a href=\"https://wikipedia.org/wiki/United_States_Ambassador_to_the_United_Kingdom\" class=\"mw-redirect\" title=\"United States Ambassador to the United Kingdom\">United States Ambassador to the United Kingdom</a> (d. 2002)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American publisher, philanthropist, and diplomat, <a href=\"https://wikipedia.org/wiki/United_States_Ambassador_to_the_United_Kingdom\" class=\"mw-redirect\" title=\"United States Ambassador to the United Kingdom\">United States Ambassador to the United Kingdom</a> (d. 2002)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "United States Ambassador to the United Kingdom", "link": "https://wikipedia.org/wiki/United_States_Ambassador_to_the_United_Kingdom"}]}, {"year": "1908", "text": "<PERSON>, American chemist and Women's Army Corps officer (d. 1997)", "html": "1908 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American chemist and <a href=\"https://wikipedia.org/wiki/Women%27s_Army_Corps\" title=\"Women's Army Corps\">Women's Army Corps</a> officer (d. 1997)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American chemist and <a href=\"https://wikipedia.org/wiki/Women%27s_Army_Corps\" title=\"Women's Army Corps\">Women's Army Corps</a> officer (d. 1997)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Women's Army Corps", "link": "https://wikipedia.org/wiki/Women%27s_Army_Corps"}]}, {"year": "1910", "text": "<PERSON>, American saxophonist, songwriter, and bandleader (d. 1987)", "html": "1910 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American saxophonist, songwriter, and bandleader (d. 1987)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American saxophonist, songwriter, and bandleader (d. 1987)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1910", "text": "<PERSON><PERSON>, Turkish journalist and author (d. 1973)", "html": "1910 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Turkish journalist and author (d. 1973)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Turkish journalist and author (d. 1973)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1911", "text": "<PERSON>, Cuban composer and conductor (d. 1981)", "html": "1911 - <a href=\"https://wikipedia.org/wiki/Jos%C3%A9_Ard%C3%A9vol\" title=\"<PERSON>\"><PERSON></a>, Cuban composer and conductor (d. 1981)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Jos%C3%A9_Ard%C3%A9vol\" title=\"<PERSON>\"><PERSON></a>, Cuban composer and conductor (d. 1981)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Jos%C3%A9_Ard%C3%A9vol"}]}, {"year": "1911", "text": "<PERSON><PERSON>, American author, founder of Scientology (d. 1986)", "html": "1911 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON> <PERSON></a>, American author, founder of <a href=\"https://wikipedia.org/wiki/Scientology\" title=\"Scientology\">Scientology</a> (d. 1986)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON> <PERSON></a>, American author, founder of <a href=\"https://wikipedia.org/wiki/Scientology\" title=\"Scientology\">Scientology</a> (d. 1986)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>"}, {"title": "Scientology", "link": "https://wikipedia.org/wiki/Scientology"}]}, {"year": "1913", "text": "<PERSON>, American politician, 13th Director of Central Intelligence (d. 1987)", "html": "1913 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American politician, 13th <a href=\"https://wikipedia.org/wiki/Director_of_Central_Intelligence\" title=\"Director of Central Intelligence\">Director of Central Intelligence</a> (d. 1987)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American politician, 13th <a href=\"https://wikipedia.org/wiki/Director_of_Central_Intelligence\" title=\"Director of Central Intelligence\">Director of Central Intelligence</a> (d. 1987)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "Director of Central Intelligence", "link": "https://wikipedia.org/wiki/Director_of_Central_Intelligence"}]}, {"year": "1913", "text": "<PERSON>, Russian author and playwright (d. 2009)", "html": "1913 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian author and playwright (d. 2009)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian author and playwright (d. 2009)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1914", "text": "<PERSON><PERSON> <PERSON><PERSON>, Canadian author and playwright (d. 1998)", "html": "1914 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> <PERSON><PERSON>\"><PERSON><PERSON> <PERSON><PERSON></a>, Canadian author and playwright (d. 1998)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/W<PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> <PERSON><PERSON>\"><PERSON><PERSON> <PERSON><PERSON></a>, Canadian author and playwright (d. 1998)", "links": [{"title": "<PERSON><PERSON> <PERSON><PERSON>", "link": "https://wikipedia.org/wiki/W<PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "1914", "text": "<PERSON>, American lieutenant and pilot, Medal of Honor recipient (d. 1943)", "html": "1914 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%27Hare\" title=\"<PERSON>\"><PERSON></a>, American lieutenant and pilot, <a href=\"https://wikipedia.org/wiki/Medal_of_Honor\" title=\"Medal of Honor\">Medal of Honor</a> recipient (d. 1943)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%27Hare\" title=\"<PERSON>\"><PERSON></a>, American lieutenant and pilot, <a href=\"https://wikipedia.org/wiki/Medal_of_Honor\" title=\"Medal of Honor\">Medal of Honor</a> recipient (d. 1943)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Edward_O%27Hare"}, {"title": "Medal of Honor", "link": "https://wikipedia.org/wiki/Medal_of_Honor"}]}, {"year": "1916", "text": "<PERSON><PERSON>, American educator and politician, 5th United States Ambassador to the Holy See (d. 2013)", "html": "1916 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American educator and politician, 5th <a href=\"https://wikipedia.org/wiki/United_States_Ambassador_to_the_Holy_See\" class=\"mw-redirect\" title=\"United States Ambassador to the Holy See\">United States Ambassador to the Holy See</a> (d. 2013)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American educator and politician, 5th <a href=\"https://wikipedia.org/wiki/United_States_Ambassador_to_the_Holy_See\" class=\"mw-redirect\" title=\"United States Ambassador to the Holy See\">United States Ambassador to the Holy See</a> (d. 2013)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}, {"title": "United States Ambassador to the Holy See", "link": "https://wikipedia.org/wiki/United_States_Ambassador_to_the_Holy_See"}]}, {"year": "1916", "text": "<PERSON><PERSON><PERSON>, American engineer and academic (d. 2017)", "html": "1916 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American engineer and academic (d. 2017)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American engineer and academic (d. 2017)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1920", "text": "<PERSON>, American businessman, co-founded Comcast (d. 2015)", "html": "1920 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American businessman, co-founded <a href=\"https://wikipedia.org/wiki/Comcast\" title=\"Comcast\">Comcast</a> (d. 2015)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American businessman, co-founded <a href=\"https://wikipedia.org/wiki/Comcast\" title=\"Comcast\">Comcast</a> (d. 2015)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "Comcast", "link": "https://wikipedia.org/wiki/Comcast"}]}, {"year": "1921", "text": "<PERSON>, American cartoonist (d. 2023)", "html": "1921 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American cartoonist (d. 2023)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American cartoonist (d. 2023)", "links": [{"title": "Al Jaffee", "link": "https://wikipedia.org/wiki/Al_Jaffee"}]}, {"year": "1923", "text": "<PERSON><PERSON>, Greek general (d. 2010)", "html": "1923 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Greek general (d. 2010)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Greek general (d. 2010)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1925", "text": "<PERSON>, American drummer and composer (d. 2024)", "html": "1925 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American drummer and composer (d. 2024)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American drummer and composer (d. 2024)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1926", "text": "<PERSON>, Honduran lawyer and politician, President of Honduras (d. 2003)", "html": "1926 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Honduran lawyer and politician, <a href=\"https://wikipedia.org/wiki/President_of_Honduras\" title=\"President of Honduras\">President of Honduras</a> (d. 2003)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Honduran lawyer and politician, <a href=\"https://wikipedia.org/wiki/President_of_Honduras\" title=\"President of Honduras\">President of Honduras</a> (d. 2003)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "President of Honduras", "link": "https://wikipedia.org/wiki/President_of_Honduras"}]}, {"year": "1929", "text": "<PERSON><PERSON><PERSON><PERSON>, Polish economist and politician, 9th Prime Minister of the Republic of Poland (d. 2014)", "html": "1929 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Polish economist and politician, 9th <a href=\"https://wikipedia.org/wiki/List_of_Prime_Ministers_of_Poland\" class=\"mw-redirect\" title=\"List of Prime Ministers of Poland\">Prime Minister of the Republic of Poland</a> (d. 2014)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Polish economist and politician, 9th <a href=\"https://wikipedia.org/wiki/List_of_Prime_Ministers_of_Poland\" class=\"mw-redirect\" title=\"List of Prime Ministers of Poland\">Prime Minister of the Republic of Poland</a> (d. 2014)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>"}, {"title": "List of Prime Ministers of Poland", "link": "https://wikipedia.org/wiki/List_of_Prime_Ministers_of_Poland"}]}, {"year": "1930", "text": "<PERSON>, American Reform rabbi (d. 2024)", "html": "1930 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American Reform rabbi (d. 2024)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American Reform rabbi (d. 2024)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1933", "text": "<PERSON>, American illustrator", "html": "1933 - <a href=\"https://wikipedia.org/wiki/<PERSON>_and_<PERSON>_<PERSON>\" title=\"<PERSON> and <PERSON>\"><PERSON></a>, American illustrator", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_and_<PERSON>_<PERSON>\" title=\"<PERSON> and <PERSON>\"><PERSON></a>, American illustrator", "links": [{"title": "<PERSON> and <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_and_<PERSON>_<PERSON>"}]}, {"year": "1933", "text": "<PERSON><PERSON>, Moroccan economist and sociologist (d. 2014)", "html": "1933 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Moroccan economist and sociologist (d. 2014)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Moroccan economist and sociologist (d. 2014)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1933", "text": "<PERSON><PERSON>, German author and academic (d. 2009)", "html": "1933 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON> <PERSON>\"><PERSON><PERSON> <PERSON></a>, German author and academic (d. 2009)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON> <PERSON></a>, German author and academic (d. 2009)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1935", "text": "<PERSON>, English author and screenwriter (d. 2015)", "html": "1935 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English author and screenwriter (d. 2015)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English author and screenwriter (d. 2015)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1938", "text": "<PERSON>, American captain and politician (d. 2012)", "html": "1938 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American captain and politician (d. 2012)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American captain and politician (d. 2012)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1939", "text": "<PERSON>, American singer-songwriter and pianist", "html": "1939 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and pianist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and pianist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1941", "text": "<PERSON><PERSON>, American environmentalist, author, and academic (d. 2001)", "html": "1941 - <a href=\"https://wikipedia.org/wiki/<PERSON>lla_Meadows\" title=\"Donella Meadows\"><PERSON><PERSON></a>, American environmentalist, author, and academic (d. 2001)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Donella_Meadows\" title=\"Donella Meadows\"><PERSON><PERSON></a>, American environmentalist, author, and academic (d. 2001)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1941", "text": "<PERSON>, American R&B Soul Singer and Guitarist (d. 1998)", "html": "1941 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American R&amp;B Soul Singer and Guitarist (d. 1998)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American R&amp;B Soul Singer and Guitarist (d. 1998)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1942", "text": "<PERSON>, American computer scientist and engineer", "html": "1942 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American computer scientist and engineer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American computer scientist and engineer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1942", "text": "<PERSON><PERSON><PERSON>, Palestinian poet and author (d. 2008)", "html": "1942 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Palestinian poet and author (d. 2008)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Palestinian poet and author (d. 2008)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1942", "text": "<PERSON><PERSON><PERSON>, American singer-songwriter (d. 1999)", "html": "1942 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_John\" title=\"Scatman John\"><PERSON><PERSON><PERSON></a>, American singer-songwriter (d. 1999)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_John\" title=\"Scatman John\"><PERSON><PERSON><PERSON></a>, American singer-songwriter (d. 1999)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1942", "text": "<PERSON>, Australian journalist and television host (d. 2024)", "html": "1942 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian journalist and television host (d. 2024)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian journalist and television host (d. 2024)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1944", "text": "<PERSON>, <PERSON>, English economist and academic", "html": "1944 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_<PERSON>_<PERSON>\" title=\"<PERSON>, <PERSON>\"><PERSON>, Baron <PERSON></a>, English economist and academic", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_<PERSON>_<PERSON>\" title=\"<PERSON>, <PERSON>\"><PERSON>, <PERSON></a>, English economist and academic", "links": [{"title": "<PERSON>, <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_<PERSON>_<PERSON>"}]}, {"year": "1945", "text": "<PERSON><PERSON><PERSON>, Russian mathematician and academic", "html": "1945 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Russian mathematician and academic", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Russian mathematician and academic", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1946", "text": "<PERSON><PERSON><PERSON>, American-Israeli colonel (d. 1976)", "html": "1946 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American-Israeli colonel (d. 1976)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American-Israeli colonel (d. 1976)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1947", "text": "<PERSON>, English ballerina and educator", "html": "1947 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English ballerina and educator", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English ballerina and educator", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1947", "text": "<PERSON>, Swiss pediatrician and cellist (d. 2018)", "html": "1947 - <a href=\"https://wikipedia.org/wiki/<PERSON>_Rich<PERSON>\" title=\"<PERSON> Richner\"><PERSON></a>, Swiss pediatrician and cellist (d. 2018)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_Rich<PERSON>\" title=\"<PERSON> Richner\"><PERSON></a>, Swiss pediatrician and cellist (d. 2018)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1947", "text": "<PERSON><PERSON>, American race car driver", "html": "1947 - <a href=\"https://wikipedia.org/wiki/L<PERSON>_<PERSON>._James\" title=\"Lyn St. James\"><PERSON><PERSON></a>, American race car driver", "no_year_html": "<a href=\"https://wikipedia.org/wiki/L<PERSON>_<PERSON>._James\" title=\"Lyn St. James\"><PERSON><PERSON></a>, American race car driver", "links": [{"title": "Lyn St. James", "link": "https://wikipedia.org/wiki/Lyn_St._James"}]}, {"year": "1949", "text": "<PERSON><PERSON><PERSON><PERSON><PERSON>, Israeli politician", "html": "1949 - <a href=\"https://wikipedia.org/wiki/Ze%27<PERSON>_<PERSON>\" title=\"Ze'ev <PERSON>\"><PERSON><PERSON>'e<PERSON></a>, Israeli politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Ze%27<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>'ev <PERSON>\"><PERSON><PERSON>'e<PERSON></a>, Israeli politician", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Ze%27<PERSON>_<PERSON><PERSON>"}]}, {"year": "1949", "text": "<PERSON><PERSON>, New Zealand lawyer and politician, 12th Chief Justice of New Zealand", "html": "1949 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, New Zealand lawyer and politician, 12th <a href=\"https://wikipedia.org/wiki/Chief_Justice_of_New_Zealand\" title=\"Chief Justice of New Zealand\">Chief Justice of New Zealand</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, New Zealand lawyer and politician, 12th <a href=\"https://wikipedia.org/wiki/Chief_Justice_of_New_Zealand\" title=\"Chief Justice of New Zealand\">Chief Justice of New Zealand</a>", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}, {"title": "Chief Justice of New Zealand", "link": "https://wikipedia.org/wiki/Chief_Justice_of_New_Zealand"}]}, {"year": "1949", "text": "<PERSON>, Scottish hairdresser (d. 2024)", "html": "1949 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish hairdresser (d. 2024)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish hairdresser (d. 2024)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1950", "text": "<PERSON>, Hungarian-British boxer and actor", "html": "1950 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Hungarian-British boxer and actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Hungarian-British boxer and actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>ner"}]}, {"year": "1950", "text": "<PERSON>, Trinidadian cricketer", "html": "1950 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Trinidadian cricketer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Trinidadian cricketer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1950", "text": "<PERSON>, American physician, journalist, and author (d. 2018)", "html": "1950 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American physician, journalist, and author (d. 2018)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American physician, journalist, and author (d. 2018)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1950", "text": "<PERSON>, American actor, director, and screenwriter", "html": "1950 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor, director, and screenwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor, director, and screenwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1951", "text": "<PERSON><PERSON>, Spanish-American singer, guitarist, and actress", "html": "1951 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Spanish-American singer, guitarist, and actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Spanish-American singer, guitarist, and actress", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>ro"}]}, {"year": "1952", "text": "<PERSON>, German composer and educator", "html": "1952 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German composer and educator", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German composer and educator", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1952", "text": "<PERSON>, English journalist and author", "html": "1952 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English journalist and author", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English journalist and author", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1953", "text": "<PERSON>, American golfer (d. 2023)", "html": "1953 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(golfer)\" title=\"<PERSON> (golfer)\"><PERSON></a>, American golfer (d. 2023)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(golfer)\" title=\"<PERSON> (golfer)\"><PERSON></a>, American golfer (d. 2023)", "links": [{"title": "<PERSON> (golfer)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(golfer)"}]}, {"year": "1953", "text": "<PERSON>, American bishop, 27th Presiding Bishop of the Episcopal Church", "html": "1953 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>(bishop)\" title=\"<PERSON> (bishop)\"><PERSON></a>, American bishop, 27th <a href=\"https://wikipedia.org/wiki/Presiding_Bishop_of_the_Episcopal_Church\" class=\"mw-redirect\" title=\"Presiding Bishop of the Episcopal Church\">Presiding Bishop of the Episcopal Church</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>(bishop)\" title=\"<PERSON> (bishop)\"><PERSON></a>, American bishop, 27th <a href=\"https://wikipedia.org/wiki/Presiding_Bishop_of_the_Episcopal_Church\" class=\"mw-redirect\" title=\"Presiding Bishop of the Episcopal Church\">Presiding Bishop of the Episcopal Church</a>", "links": [{"title": "<PERSON> (bishop)", "link": "https://wikipedia.org/wiki/<PERSON>(bishop)"}, {"title": "Presiding Bishop of the Episcopal Church", "link": "https://wikipedia.org/wiki/Presiding_Bishop_of_the_Episcopal_Church"}]}, {"year": "1953", "text": "<PERSON>, American actress (d. 2012)", "html": "1953 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress (d. 2012)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress (d. 2012)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1954", "text": "<PERSON>, <PERSON>, Guyanese-English politician and diplomat", "html": "1954 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Baroness_<PERSON>\" title=\"<PERSON>, <PERSON>\"><PERSON>, Baroness <PERSON></a>, Guyanese-English politician and diplomat", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Baroness_<PERSON>\" title=\"<PERSON>, <PERSON>\"><PERSON>, Baroness <PERSON></a>, Guyanese-English politician and diplomat", "links": [{"title": "<PERSON>, <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_<PERSON>_<PERSON>"}]}, {"year": "1954", "text": "<PERSON>, Canadian actress and screenwriter", "html": "1954 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian actress and screenwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian actress and screenwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1955", "text": "<PERSON>, Italian footballer and manager", "html": "1955 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian footballer and manager", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian footballer and manager", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1955", "text": "<PERSON><PERSON>, American actress (d. 2017)", "html": "1955 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American actress (d. 2017)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American actress (d. 2017)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1955", "text": "<PERSON>, Russian pentathlete", "html": "1955 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian pentathlete", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian pentathlete", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1956", "text": "<PERSON>, American actress and producer", "html": "1956 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress and producer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1956", "text": "<PERSON>, North-American businessman and banker", "html": "1956 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, North-American businessman and banker", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, North-American businessman and banker", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1957", "text": "<PERSON>, American banker and politician, 31st Governor of North Dakota", "html": "1957 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American banker and politician, 31st <a href=\"https://wikipedia.org/wiki/Governor_of_North_Dakota\" title=\"Governor of North Dakota\">Governor of North Dakota</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American banker and politician, 31st <a href=\"https://wikipedia.org/wiki/Governor_of_North_Dakota\" title=\"Governor of North Dakota\">Governor of North Dakota</a>", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Governor of North Dakota", "link": "https://wikipedia.org/wiki/Governor_of_North_Dakota"}]}, {"year": "1957", "text": "<PERSON>, American composer and conductor (d. 2003)", "html": "1957 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American composer and conductor (d. 2003)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American composer and conductor (d. 2003)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1958", "text": "<PERSON><PERSON><PERSON><PERSON>, Salvadoran footballer", "html": "1958 - <a href=\"https://wikipedia.org/wiki/M%C3%A1gico_Gonz%C3%A1lez\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Salvadoran footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/M%C3%A1gico_Gonz%C3%A1lez\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Salvadoran footballer", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/M%C3%A1gico_Gonz%C3%A1lez"}]}, {"year": "1958", "text": "<PERSON>, American lawyer and politician", "html": "1958 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and politician", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1958", "text": "<PERSON><PERSON>, Caribbean-English author and playwright", "html": "1958 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Caribbean-English author and playwright", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Caribbean-English author and playwright", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1959", "text": "<PERSON>, Australian cricketer", "html": "1959 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian cricketer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian cricketer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1960", "text": "<PERSON>, English-Irish musician and songwriter", "html": "1960 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English-Irish musician and songwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English-Irish musician and songwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1960", "text": "<PERSON>, American animator, screenwriter, and voice actor (d. 2005)", "html": "1960 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American animator, screenwriter, and voice actor (d. 2005)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American animator, screenwriter, and voice actor (d. 2005)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>nft"}]}, {"year": "1962", "text": "<PERSON>, Portuguese politician", "html": "1962 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Portuguese politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Portuguese politician", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1963", "text": "<PERSON>, Dominican baseball player and manager", "html": "1963 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dominican baseball player and manager", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dominican baseball player and manager", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1963", "text": "<PERSON>, American football player", "html": "1963 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1963", "text": "<PERSON><PERSON>, Argentine musician, songwriter and filmmaker", "html": "1963 - <a href=\"https://wikipedia.org/wiki/Fito_P%C3%A1ez\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Argentine musician, songwriter and filmmaker", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Fito_P%C3%A1ez\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Argentine musician, songwriter and filmmaker", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Fito_P%C3%A1ez"}]}, {"year": "1964", "text": "<PERSON>, American baseball player", "html": "1964 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1964", "text": "<PERSON>, Australian rugby league player", "html": "1964 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian rugby league player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian rugby league player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1964", "text": "<PERSON>, Australian rugby league player and coach", "html": "1964 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian rugby league player and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian rugby league player and coach", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1966", "text": "<PERSON>, Brazilian singer-songwriter (d. 1997)", "html": "1966 - <a href=\"https://wikipedia.org/wiki/Chico_Science\" title=\"Chico Science\"><PERSON></a>, Brazilian singer-songwriter (d. 1997)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Chico_Science\" title=\"Chico Science\"><PERSON></a>, Brazilian singer-songwriter (d. 1997)", "links": [{"title": "Chico Science", "link": "https://wikipedia.org/wiki/Chico_Science"}]}, {"year": "1967", "text": "<PERSON>, Colombian footballer (d. 1994)", "html": "1967 - <a href=\"https://wikipedia.org/wiki/Andr%C3%A9s_Escobar\" title=\"<PERSON>\"><PERSON></a>, Colombian footballer (d. 1994)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Andr%C3%A9s_Escobar\" title=\"<PERSON>\"><PERSON></a>, Colombian footballer (d. 1994)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Andr%C3%A9s_Escobar"}]}, {"year": "1967", "text": "<PERSON>, Dutch footballer and referee", "html": "1967 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch footballer and referee", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch footballer and referee", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1969", "text": "<PERSON>, Australian rugby league player", "html": "1969 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian rugby league player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian rugby league player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1970", "text": "<PERSON>, American director and producer", "html": "1970 - <a href=\"https://wikipedia.org/wiki/<PERSON>_Story\" title=\"Tim Story\"><PERSON></a>, American director and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_Story\" title=\"Tim Story\"><PERSON></a>, American director and producer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1971", "text": "<PERSON><PERSON>, American actress", "html": "1971 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American actress", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1971", "text": "<PERSON>, Danish footballer and manager", "html": "1971 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Danish footballer and manager", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Danish footballer and manager", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1971", "text": "<PERSON><PERSON>, American actress", "html": "1971 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American actress", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1972", "text": "Common, American rapper and actor", "html": "1972 - <a href=\"https://wikipedia.org/wiki/Common_(rapper)\" title=\"<PERSON> (rapper)\"><PERSON></a>, American rapper and actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Common_(rapper)\" title=\"<PERSON> (rapper)\">Common</a>, American rapper and actor", "links": [{"title": "<PERSON> (rapper)", "link": "https://wikipedia.org/wiki/<PERSON>_(rapper)"}]}, {"year": "1972", "text": "<PERSON>, American football player, coach, and analyst", "html": "1972 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player, coach, and analyst", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player, coach, and analyst", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1973", "text": "<PERSON>, Surinamese-Dutch footballer and manager", "html": "1973 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Surinamese-Dutch footballer and manager", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Surinamese-Dutch footballer and manager", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1973", "text": "<PERSON>, American singer-songwriter", "html": "1973 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1973", "text": "<PERSON>, American basketball player and coach", "html": "1973 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(basketball)\" class=\"mw-redirect\" title=\"<PERSON> (basketball)\"><PERSON></a>, American basketball player and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(basketball)\" class=\"mw-redirect\" title=\"<PERSON> (basketball)\"><PERSON></a>, American basketball player and coach", "links": [{"title": "<PERSON> (basketball)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(basketball)"}]}, {"year": "1974", "text": "<PERSON>, Scottish cricketer", "html": "1974 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish cricketer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish cricketer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1974", "text": "<PERSON>, Swedish tennis player and sportscaster", "html": "1974 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swedish tennis player and sportscaster", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swedish tennis player and sportscaster", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1975", "text": "<PERSON>, English football referee", "html": "1975 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English football referee", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English football referee", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1976", "text": "<PERSON>, American basketball player and rapper", "html": "1976 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player and rapper", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player and rapper", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Troy_Hudson"}]}, {"year": "1976", "text": "<PERSON>, American actor and producer", "html": "1976 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor and producer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1978", "text": "<PERSON>, American cyclist", "html": "1978 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American cyclist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American cyclist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1979", "text": "<PERSON>, Venezuelan baseball player", "html": "1979 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Venezuelan baseball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Venezuelan baseball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1979", "text": "<PERSON><PERSON><PERSON>, Belgian sprinter", "html": "1979 - <a href=\"https://wikipedia.org/wiki/C%C3%A<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Belgian sprinter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/C%C3%A<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Belgian sprinter", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/C%C3%A9<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1980", "text": "<PERSON><PERSON>, American basketball player", "html": "1980 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American basketball player", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1980", "text": "<PERSON>, Australian rugby league player", "html": "1980 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian rugby league player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian rugby league player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1982", "text": "<PERSON><PERSON>, Brazilian basketball player", "html": "1982 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Brazilian basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Brazilian basketball player", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1982", "text": "<PERSON>, American basketball player", "html": "1982 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1983", "text": "<PERSON><PERSON><PERSON>, American swimmer", "html": "1983 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American swimmer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American swimmer", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1984", "text": "<PERSON><PERSON>, Indian actress", "html": "1984 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Indian actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Indian actress", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Gee<PERSON>_<PERSON>ra"}]}, {"year": "1985", "text": "<PERSON><PERSON><PERSON>, Brazilian footballer", "html": "1985 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_(footballer)\" title=\"<PERSON><PERSON><PERSON> (footballer)\"><PERSON><PERSON><PERSON></a>, Brazilian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_(footballer)\" title=\"<PERSON><PERSON><PERSON> (footballer)\"><PERSON><PERSON><PERSON></a>, Brazilian footballer", "links": [{"title": "<PERSON><PERSON><PERSON> (footballer)", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_(footballer)"}]}, {"year": "1985", "text": "<PERSON><PERSON>, American actor", "html": "1985 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American actor", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1986", "text": "<PERSON>, South African-New Zealand cricketer", "html": "1986 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, South African-New Zealand cricketer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, South African-New Zealand cricketer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1987", "text": "<PERSON>, American race car driver", "html": "1987 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American race car driver", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American race car driver", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1987", "text": "<PERSON>, German footballer", "html": "1987 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(footballer)\" title=\"<PERSON> (footballer)\"><PERSON></a>, German footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>(footballer)\" title=\"<PERSON> (footballer)\"><PERSON></a>, German footballer", "links": [{"title": "<PERSON> (footballer)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>(footballer)"}]}, {"year": "1988", "text": "<PERSON><PERSON><PERSON><PERSON>, Dutch footballer", "html": "1988 - <a href=\"https://wikipedia.org/wiki/Furd<PERSON><PERSON>_<PERSON>\" title=\"<PERSON>rd<PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Dutch footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>rd<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Dutch footballer", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>h"}]}, {"year": "1989", "text": "<PERSON><PERSON><PERSON>, German footballer", "html": "1989 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_Badstuber\" title=\"<PERSON><PERSON><PERSON> Badstuber\"><PERSON><PERSON><PERSON></a>, German footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>stuber\" title=\"<PERSON><PERSON><PERSON> Badstuber\"><PERSON><PERSON><PERSON></a>, German footballer", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>ube<PERSON>"}]}, {"year": "1989", "text": "<PERSON><PERSON><PERSON>, English columnist, television personality, and model (d. 2014)", "html": "1989 - <a href=\"https://wikipedia.org/wiki/<PERSON>each<PERSON>_<PERSON>\" title=\"Peach<PERSON> Geldof\"><PERSON><PERSON><PERSON></a>, English columnist, television personality, and model (d. 2014)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"Peach<PERSON>of\"><PERSON><PERSON><PERSON></a>, English columnist, television personality, and model (d. 2014)", "links": [{"title": "Peach<PERSON>", "link": "https://wikipedia.org/wiki/Peach<PERSON>_<PERSON>of"}]}, {"year": "1989", "text": "<PERSON>, Venezuelan baseball player", "html": "1989 - <a href=\"https://wikipedia.org/wiki/<PERSON>_Le%C3%B3n\" title=\"Sandy León\"><PERSON></a>, Venezuelan baseball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_Le%C3%B3n\" title=\"Sandy León\"><PERSON></a>, Venezuelan baseball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Sandy_Le%C3%B3n"}]}, {"year": "1989", "text": "<PERSON><PERSON>, German footballer", "html": "1989 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, German footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, German footballer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1989", "text": "<PERSON>, Canadian racing driver", "html": "1989 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian racing driver", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian racing driver", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1990", "text": "<PERSON><PERSON><PERSON>, Malagasy footballer", "html": "1990 - <a href=\"https://wikipedia.org/wiki/An<PERSON><PERSON>_<PERSON>\" title=\"Anicet Abel\"><PERSON><PERSON><PERSON></a>, Malagasy footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"Anice<PERSON> Abel\"><PERSON><PERSON><PERSON></a>, Malagasy footballer", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Anice<PERSON>_<PERSON>"}]}, {"year": "1990", "text": "<PERSON><PERSON>, American football player", "html": "1990 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American football player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American football player", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1991", "text": "<PERSON>, Australian speed skater", "html": "1991 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian speed skater", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian speed skater", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1991", "text": "<PERSON>, American basketball player", "html": "1991 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1992", "text": "<PERSON>, Australian actress", "html": "1992 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian actress", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1992", "text": "<PERSON>, English actor", "html": "1992 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(actor)\" title=\"<PERSON> (actor)\"><PERSON></a>, English actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>(actor)\" title=\"<PERSON> (actor)\"><PERSON></a>, English actor", "links": [{"title": "<PERSON> (actor)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(actor)"}]}, {"year": "1992", "text": "<PERSON><PERSON>, Puerto Rican singer-songwriter and rapper", "html": "1992 - <a href=\"https://wikipedia.org/wiki/Ozuna\" title=\"Oz<PERSON>\"><PERSON><PERSON></a>, Puerto Rican singer-songwriter and rapper", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Ozuna\" title=\"Oz<PERSON>\"><PERSON><PERSON></a>, Puerto Rican singer-songwriter and rapper", "links": [{"title": "Ozuna", "link": "https://wikipedia.org/wiki/Ozuna"}]}, {"year": "1992", "text": "<PERSON>, South Korean actor and singer", "html": "1992 - <a href=\"https://wikipedia.org/wiki/L_(entertainer)\" title=\"L (entertainer)\">L</a>, South Korean actor and singer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/L_(entertainer)\" title=\"L (entertainer)\">L</a>, South Korean actor and singer", "links": [{"title": "<PERSON> (entertainer)", "link": "https://wikipedia.org/wiki/L_(entertainer)"}]}, {"year": "1993", "text": "<PERSON>, English footballer", "html": "1993 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>s"}]}, {"year": "1994", "text": "<PERSON>, Spanish footballer", "html": "1994 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Spanish footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Spanish footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1994", "text": "<PERSON>, Indian cricketer", "html": "1994 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Indian cricketer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Indian cricketer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1995", "text": "<PERSON><PERSON><PERSON>, American skier", "html": "1995 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American skier", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American skier", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON><PERSON><PERSON>"}]}, {"year": "1995", "text": "<PERSON>, South Korean tennis player", "html": "1995 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>-jeong\" title=\"<PERSON>jeong\"><PERSON>eon<PERSON></a>, South Korean tennis player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>-jeong\" title=\"<PERSON>jeon<PERSON>\"><PERSON>eon<PERSON></a>, South Korean tennis player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>-jeong"}]}, {"year": "1996", "text": "<PERSON><PERSON>, Canadian ice hockey player", "html": "1996 - <a href=\"https://wikipedia.org/wiki/Brayden_Point\" title=\"Brayden Point\">Brayden Point</a>, Canadian ice hockey player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Brayden_Point\" title=\"Brayden Point\">Brayden Point</a>, Canadian ice hockey player", "links": [{"title": "Brayden Point", "link": "https://wikipedia.org/wiki/Brayden_Point"}]}, {"year": "1997", "text": "<PERSON><PERSON>, American model, actress, and musician", "html": "1997 - <a href=\"https://wikipedia.org/wiki/Pyper_America\" title=\"Pyper America\">Pyper America</a>, American model, actress, and musician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Pyper_America\" title=\"Pyper America\">Pyper America</a>, American model, actress, and musician", "links": [{"title": "Pyper America", "link": "https://wikipedia.org/wiki/Pyper_America"}]}, {"year": "1997", "text": "<PERSON><PERSON>, American basketball player", "html": "1997 - <a href=\"https://wikipedia.org/wiki/Land<PERSON>_<PERSON>t\" title=\"<PERSON><PERSON> S<PERSON>t\"><PERSON><PERSON></a>, American basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>t\" title=\"<PERSON><PERSON> S<PERSON>t\"><PERSON><PERSON></a>, American basketball player", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>t"}]}, {"year": "1998", "text": "<PERSON><PERSON><PERSON>, Dutch footballer", "html": "1998 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Dutch footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Dutch footballer", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1998", "text": "<PERSON>, American rapper, singer-songwriter, and actor", "html": "1998 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American rapper, singer-songwriter, and actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American rapper, singer-songwriter, and actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2001", "text": "<PERSON>, Australian rugby league player", "html": "2001 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, Australian rugby league player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, Australian rugby league player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "2002", "text": "<PERSON>, American football player", "html": "2002 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>.\" title=\"<PERSON>.\"><PERSON>.</a>, American football player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>.\" title=\"<PERSON> Jr.\"><PERSON>.</a>, American football player", "links": [{"title": "<PERSON>.", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>."}]}, {"year": "2004", "text": "<PERSON><PERSON>, American tennis player", "html": "2004 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>ff\"><PERSON><PERSON></a>, American tennis player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>ff\"><PERSON><PERSON></a>, American tennis player", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>ff"}]}], "Deaths": [{"year": "1202", "text": "<PERSON><PERSON><PERSON><PERSON> the Old, king of Poland (b. c. 1121)", "html": "1202 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_III_the_Old\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON><PERSON> III the Old\"><PERSON><PERSON><PERSON><PERSON> III the Old</a>, king of Poland (b. c. 1121)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_III_the_Old\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON><PERSON> III the Old\"><PERSON><PERSON><PERSON><PERSON> III the Old</a>, king of Poland (b. c. 1121)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON> III the Old", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_III_the_Old"}]}, {"year": "1271", "text": "<PERSON> of Almain, English knight (b. 1235)", "html": "1271 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_Alma<PERSON>\" title=\"<PERSON> of Almain\"><PERSON> of Almain</a>, English knight (b. 1235)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON> of Almain\"><PERSON> of <PERSON></a>, English knight (b. 1235)", "links": [{"title": "<PERSON> of Almain", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1415", "text": "<PERSON><PERSON>, Crown Prince of Ava (b. 1391)", "html": "1415 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>wa\" title=\"<PERSON><PERSON> Kyawswa\"><PERSON><PERSON></a>, Crown Prince of <a href=\"https://wikipedia.org/wiki/Ava_Kingdom\" class=\"mw-redirect\" title=\"Ava Kingdom\">Ava</a> (b. 1391)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> Kyawswa\"><PERSON><PERSON></a>, Crown Prince of <a href=\"https://wikipedia.org/wiki/Ava_Kingdom\" class=\"mw-redirect\" title=\"Ava Kingdom\">Ava</a> (b. 1391)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>wswa"}, {"title": "Ava Kingdom", "link": "https://wikipedia.org/wiki/Ava_Kingdom"}]}, {"year": "1447", "text": "<PERSON>, Timurid ruler of Persia and Transoxania (b. 1377)", "html": "1447 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Ruk<PERSON>\"><PERSON></a>, <a href=\"https://wikipedia.org/wiki/Timurid_Empire\" title=\"Timurid Empire\"><PERSON><PERSON><PERSON></a> ruler of Persia and Transoxania (b. 1377)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Shah Ruk<PERSON>\"><PERSON></a>, <a href=\"https://wikipedia.org/wiki/Timurid_Empire\" title=\"Timurid Empire\"><PERSON><PERSON><PERSON></a> ruler of Persia and Transoxania (b. 1377)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Timurid Empire", "link": "https://wikipedia.org/wiki/Timurid_Empire"}]}, {"year": "1573", "text": "<PERSON>, French politician (b. 1507)", "html": "1573 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>%27H%C3%B4pital\" title=\"<PERSON>Hôpital\"><PERSON></a>, French politician (b. 1507)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>%27H%C3%B4pital\" title=\"<PERSON>Hôpital\"><PERSON></a>, French politician (b. 1507)", "links": [{"title": "<PERSON> l'Hôpital", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>%27H%C3%B4pital"}]}, {"year": "1601", "text": "<PERSON>, Pol<PERSON>ian (b. 1563)", "html": "1601 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, <PERSON><PERSON><PERSON> (b. 1563)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, <PERSON><PERSON><PERSON> (b. 1563)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1619", "text": "<PERSON>, English actor (b. 1567)", "html": "1619 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> B<PERSON>\"><PERSON></a>, English actor (b. 1567)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> B<PERSON>\"><PERSON></a>, English actor (b. 1567)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1711", "text": "<PERSON><PERSON><PERSON><PERSON><PERSON>, French poet and critic (b. 1636)", "html": "1711 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>-Despr%C3%A9aux\" title=\"<PERSON>\"><PERSON></a>, French poet and critic (b. 1636)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>-Despr%C3%A9aux\" title=\"<PERSON>\"><PERSON></a>, French poet and critic (b. 1636)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>-Despr%C3%A9aux"}]}, {"year": "1719", "text": "<PERSON>, German chemist and potter (b. 1682)", "html": "1719 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>%C3%B6ttger\" title=\"<PERSON>\"><PERSON></a>, German chemist and potter (b. 1682)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>%C3%B6ttger\" title=\"<PERSON>\"><PERSON></a>, German chemist and potter (b. 1682)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>%C3%B6ttger"}]}, {"year": "1800", "text": "<PERSON>, Indian minister and politician (b. 1742)", "html": "1800 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Indian minister and politician (b. 1742)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Indian minister and politician (b. 1742)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1808", "text": "<PERSON> Denmark (b. 1749)", "html": "1808 - <a href=\"https://wikipedia.org/wiki/Christian_VII_of_Denmark\" title=\"Christian VII of Denmark\">Christian VII of Denmark</a> (b. 1749)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Christian_VII_of_Denmark\" title=\"Christian VII of Denmark\">Christian VII of Denmark</a> (b. 1749)", "links": [{"title": "Christian VII of Denmark", "link": "https://wikipedia.org/wiki/Christian_VII_of_Denmark"}]}, {"year": "1823", "text": "<PERSON>, 1st Earl of St Vincent, English admiral and politician (b. 1735)", "html": "1823 - <a href=\"https://wikipedia.org/wiki/<PERSON>,_1st_Earl_of_St_Vincent\" title=\"<PERSON>, 1st Earl of St Vincent\"><PERSON>, 1st Earl of St Vincent</a>, English admiral and politician (b. 1735)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>,_1st_Earl_of_St_Vincent\" title=\"<PERSON>, 1st Earl of St Vincent\"><PERSON>, 1st Earl of St Vincent</a>, English admiral and politician (b. 1735)", "links": [{"title": "<PERSON>, 1st Earl of St Vincent", "link": "https://wikipedia.org/wiki/<PERSON>,_1st_Earl_of_St_Vincent"}]}, {"year": "1833", "text": "<PERSON>, English lieutenant and cartographer (b. 1757)", "html": "1833 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(Royal_Navy_officer)\" title=\"<PERSON> (Royal Navy officer)\"><PERSON></a>, English lieutenant and cartographer (b. 1757)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(Royal_Navy_officer)\" title=\"<PERSON> (Royal Navy officer)\"><PERSON></a>, English lieutenant and cartographer (b. 1757)", "links": [{"title": "<PERSON> (Royal Navy officer)", "link": "https://wikipedia.org/wiki/<PERSON>_(Royal_Navy_officer)"}]}, {"year": "1842", "text": "<PERSON>, English general (b. 1761)", "html": "1842 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English general (b. 1761)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English general (b. 1761)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1854", "text": "<PERSON><PERSON><PERSON>, French politician, 6th Prime Minister of France (b. 1773)", "html": "1854 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>%C3%A8le\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, French politician, 6th <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_France\" title=\"Prime Minister of France\">Prime Minister of France</a> (b. 1773)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>ill%C3%A8le\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, French politician, 6th <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_France\" title=\"Prime Minister of France\">Prime Minister of France</a> (b. 1773)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>%C3%A8le"}, {"title": "Prime Minister of France", "link": "https://wikipedia.org/wiki/Prime_Minister_of_France"}]}, {"year": "1873", "text": "<PERSON>, American physician, lawyer, and businessman (b. 1808)", "html": "1873 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, American physician, lawyer, and businessman (b. 1808)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, American physician, lawyer, and businessman (b. 1808)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1879", "text": "<PERSON>, German mathematician and chess player (b. 1818)", "html": "1879 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German mathematician and chess player (b. 1818)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German mathematician and chess player (b. 1818)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1881", "text": "<PERSON> of Russia (b. 1818)", "html": "1881 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Russia\" title=\"<PERSON> II of Russia\"><PERSON> of Russia</a> (b. 1818)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Russia\" title=\"<PERSON> II of Russia\"><PERSON> of Russia</a> (b. 1818)", "links": [{"title": "<PERSON> of Russia", "link": "https://wikipedia.org/wiki/<PERSON>_II_of_Russia"}]}, {"year": "1884", "text": "<PERSON><PERSON>., American son of <PERSON><PERSON> (b. 1868)", "html": "1884 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>.\" title=\"<PERSON><PERSON> Jr.\"><PERSON><PERSON> Jr.</a>, American son of <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a> (b. 1868)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>.\" title=\"<PERSON><PERSON> Jr.\"><PERSON><PERSON> Jr.</a>, American son of <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a> (b. 1868)", "links": [{"title": "<PERSON><PERSON> Jr.", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_Stanford_Jr."}, {"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Le<PERSON>_Stanford"}]}, {"year": "1885", "text": "<PERSON>, Maltese politician (b. 1795)", "html": "1885 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Maltese politician (b. 1795)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Maltese politician (b. 1795)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1901", "text": "<PERSON>, American general and politician, 23rd President of the United States (b. 1833)", "html": "1901 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American general and politician, 23rd <a href=\"https://wikipedia.org/wiki/President_of_the_United_States\" title=\"President of the United States\">President of the United States</a> (b. 1833)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American general and politician, 23rd <a href=\"https://wikipedia.org/wiki/President_of_the_United_States\" title=\"President of the United States\">President of the United States</a> (b. 1833)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "President of the United States", "link": "https://wikipedia.org/wiki/President_of_the_United_States"}]}, {"year": "1906", "text": "<PERSON>, American activist (b. 1820)", "html": "1906 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American activist (b. 1820)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American activist (b. 1820)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "1912", "text": "<PERSON><PERSON><PERSON>, Canadian engineer and architect, designed the Parliament Building (b. 1836)", "html": "1912 - <a href=\"https://wikipedia.org/wiki/Eug%C3%A8ne-%C3%89tienne_Tach%C3%A9\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Canadian engineer and architect, designed the <a href=\"https://wikipedia.org/wiki/Parliament_Building_(Quebec)\" title=\"Parliament Building (Quebec)\">Parliament Building</a> (b. 1836)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Eug%C3%A8ne-%C3%89tienne_Tach%C3%A9\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Canadian engineer and architect, designed the <a href=\"https://wikipedia.org/wiki/Parliament_Building_(Quebec)\" title=\"Parliament Building (Quebec)\">Parliament Building</a> (b. 1836)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Eug%C3%A8ne-%C3%89tienne_Tach%C3%A9"}, {"title": "Parliament Building (Quebec)", "link": "https://wikipedia.org/wiki/Parliament_Building_(Quebec)"}]}, {"year": "1921", "text": "<PERSON>, American opera singer and educator (b. 1835)", "html": "1921 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American opera singer and educator (b. 1835)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American opera singer and educator (b. 1835)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "1923", "text": "<PERSON>, American real estate entrepreneur (b. 1856)", "html": "1923 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American real estate entrepreneur (b. 1856)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American real estate entrepreneur (b. 1856)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1936", "text": "<PERSON>, New Zealand lawyer and politician, 20th Prime Minister of New Zealand (b. 1851)", "html": "1936 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(New_Zealand_politician)\" title=\"<PERSON> (New Zealand politician)\"><PERSON></a>, New Zealand lawyer and politician, 20th <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_New_Zealand\" title=\"Prime Minister of New Zealand\">Prime Minister of New Zealand</a> (b. 1851)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(New_Zealand_politician)\" title=\"<PERSON> (New Zealand politician)\"><PERSON></a>, New Zealand lawyer and politician, 20th <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_New_Zealand\" title=\"Prime Minister of New Zealand\">Prime Minister of New Zealand</a> (b. 1851)", "links": [{"title": "<PERSON> (New Zealand politician)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(New_Zealand_politician)"}, {"title": "Prime Minister of New Zealand", "link": "https://wikipedia.org/wiki/Prime_Minister_of_New_Zealand"}]}, {"year": "1938", "text": "<PERSON>, American lawyer and author (b. 1857)", "html": "1938 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and author (b. 1857)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and author (b. 1857)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1943", "text": "<PERSON>, American poet, short story writer, and novelist (b. 1898)", "html": "1943 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>%C3%A9t\" title=\"<PERSON>\"><PERSON></a>, American poet, short story writer, and novelist (b. 1898)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>%C3%A9t\" title=\"<PERSON>\"><PERSON></a>, American poet, short story writer, and novelist (b. 1898)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>%C3%A9t"}]}, {"year": "1946", "text": "<PERSON>, German field marshal (b. 1878)", "html": "1946 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German field marshal (b. 1878)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German field marshal (b. 1878)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1951", "text": "<PERSON><PERSON> \"the Terrible\" <PERSON>, Estonian anti-communist, freedom fighter and forest brother (b. 1917)", "html": "1951 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> Ka<PERSON>\"><PERSON><PERSON> \"the Terrible\" <PERSON><PERSON><PERSON><PERSON></a>, Estonian anti-communist, freedom fighter and <a href=\"https://wikipedia.org/wiki/Forest_brother\" class=\"mw-redirect\" title=\"Forest brother\">forest brother</a> (b. 1917)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON> \"the Terrible\" <PERSON><PERSON><PERSON><PERSON></a>, Estonian anti-communist, freedom fighter and <a href=\"https://wikipedia.org/wiki/Forest_brother\" class=\"mw-redirect\" title=\"Forest brother\">forest brother</a> (b. 1917)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}, {"title": "Forest brother", "link": "https://wikipedia.org/wiki/<PERSON>_brother"}]}, {"year": "1953", "text": "<PERSON>, Estonian general and statesman (b. 1884)", "html": "1953 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Estonian general and statesman (b. 1884)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Estonian general and statesman (b. 1884)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1961", "text": "<PERSON><PERSON>, Norwegian journalist and war correspondent (b. 1905)", "html": "1961 - <a href=\"https://wikipedia.org/wiki/Li<PERSON>_Lindb%C3%A6k\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Norwegian journalist and war correspondent (b. 1905)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Li<PERSON>_Lindb%C3%A6k\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Norwegian journalist and war correspondent (b. 1905)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Lise_Lindb%C3%A6k"}]}, {"year": "1962", "text": "<PERSON>, Irish sculptor (b. 1882)", "html": "1962 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Irish sculptor (b. 1882)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Irish sculptor (b. 1882)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1965", "text": "<PERSON><PERSON><PERSON>, Italian engineer (b. 1891)", "html": "1965 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Italian engineer (b. 1891)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Italian engineer (b. 1891)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1965", "text": "<PERSON>, Albanian-American bishop and politician, 14th Prime Minister of Albania (b. 1882)", "html": "1965 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Albanian-American bishop and politician, 14th <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Albania\" title=\"Prime Minister of Albania\">Prime Minister of Albania</a> (b. 1882)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Albanian-American bishop and politician, 14th <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Albania\" title=\"Prime Minister of Albania\">Prime Minister of Albania</a> (b. 1882)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>"}, {"title": "Prime Minister of Albania", "link": "https://wikipedia.org/wiki/Prime_Minister_of_Albania"}]}, {"year": "1971", "text": "<PERSON><PERSON>, American painter and illustrator (b. 1882)", "html": "1971 - <a href=\"https://wikipedia.org/wiki/Rockwell_Kent\" title=\"Rockwell Kent\">Rockwell Kent</a>, American painter and illustrator (b. 1882)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Rockwell_Kent\" title=\"Rockwell Kent\">Rockwell Kent</a>, American painter and illustrator (b. 1882)", "links": [{"title": "Rockwell Kent", "link": "https://wikipedia.org/wiki/Rockwell_Kent"}]}, {"year": "1972", "text": "<PERSON>, English photographer (b. 1941)", "html": "1972 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English photographer (b. 1941)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English photographer (b. 1941)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1975", "text": "<PERSON><PERSON>, Yugoslav novelist, poet, and short story writer, Nobel Prize laureate (b. 1892)", "html": "1975 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_Andri%C4%87\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Yugoslav novelist, poet, and short story writer, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Literature\" title=\"Nobel Prize in Literature\">Nobel Prize</a> laureate (b. 1892)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>%C4%87\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Yugoslav novelist, poet, and short story writer, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Literature\" title=\"Nobel Prize in Literature\">Nobel Prize</a> laureate (b. 1892)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Ivo_Andri%C4%87"}, {"title": "Nobel Prize in Literature", "link": "https://wikipedia.org/wiki/Nobel_Prize_in_Literature"}]}, {"year": "1976", "text": "<PERSON>, American sports executive (b. 1900)", "html": "1976 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American sports executive (b. 1900)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>\" title=\"<PERSON>\"><PERSON></a>, American sports executive (b. 1900)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1983", "text": "<PERSON>, German-Dutch illustrator and educator (b. 1896)", "html": "1983 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German-Dutch illustrator and educator (b. 1896)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German-Dutch illustrator and educator (b. 1896)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1990", "text": "<PERSON>, Austrian-American psychologist and author (b. 1903)", "html": "1990 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Austrian-American psychologist and author (b. 1903)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Austrian-American psychologist and author (b. 1903)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1995", "text": "<PERSON><PERSON><PERSON>, French nurse and spy (b. 1912)", "html": "1995 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, French nurse and spy (b. 1912)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, French nurse and spy (b. 1912)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>det<PERSON>_<PERSON>"}]}, {"year": "1996", "text": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, Polish director and screenwriter (b. 1941)", "html": "1996 - <a href=\"https://wikipedia.org/wiki/K<PERSON><PERSON>ztof_Kie%C5%9B<PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON></a>, Polish director and screenwriter (b. 1941)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/K<PERSON><PERSON>ztof_Kie%C5%9B<PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON></a>, Polish director and screenwriter (b. 1941)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>z<PERSON>f_Kie%C5%9B<PERSON><PERSON>"}]}, {"year": "1998", "text": "<PERSON>, English singer-songwriter (b. 1945)", "html": "1998 - <a href=\"https://wikipedia.org/wiki/Judge_Dread\" title=\"Judge Dread\">Judge <PERSON><PERSON></a>, English singer-songwriter (b. 1945)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Judge_Dread\" title=\"Judge Dread\">Judge <PERSON><PERSON></a>, English singer-songwriter (b. 1945)", "links": [{"title": "Judge <PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Judge_Dread"}]}, {"year": "1998", "text": "<PERSON>, German-American physicist and engineer (b. 1911)", "html": "1998 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German-American physicist and engineer (b. 1911)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German-American physicist and engineer (b. 1911)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1999", "text": "<PERSON>, American cartoonist, director, and producer (b. 1911)", "html": "1999 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American cartoonist, director, and producer (b. 1911)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American cartoonist, director, and producer (b. 1911)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1999", "text": "<PERSON><PERSON><PERSON>, American director and screenwriter (b. 1912)", "html": "1999 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American director and screenwriter (b. 1912)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American director and screenwriter (b. 1912)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON><PERSON>"}]}, {"year": "2001", "text": "<PERSON>, American actor and cinematographer (b. 1934)", "html": "2001 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON><PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor and cinematographer (b. 1934)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON><PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor and cinematographer (b. 1934)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "2001", "text": "<PERSON><PERSON><PERSON><PERSON>, Filipino historian and educator (b. 1895)", "html": "2001 - <a href=\"https://wikipedia.org/wiki/Encarnacion_Alzona\" class=\"mw-redirect\" title=\"Encarnacion Alzona\">Encarnacion Alzona</a>, Filipino historian and educator (b. 1895)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Encarnacion_Alzona\" class=\"mw-redirect\" title=\"Encarnacion Alzona\">Encarnacion Alzona</a>, Filipino historian and educator (b. 1895)", "links": [{"title": "Encarnacion Alzona", "link": "https://wikipedia.org/wiki/Encarnacion_Alzona"}]}, {"year": "2002", "text": "<PERSON><PERSON><PERSON>, German philosopher and scholar (b. 1900)", "html": "2002 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, German philosopher and scholar (b. 1900)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, German philosopher and scholar (b. 1900)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>"}]}, {"year": "2004", "text": "<PERSON>, Austrian cardinal (b. 1905)", "html": "2004 - <a href=\"https://wikipedia.org/wiki/Franz_K%C3%B6nig\" title=\"<PERSON>\"><PERSON></a>, Austrian cardinal (b. 1905)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Franz_K%C3%B6nig\" title=\"<PERSON>\"><PERSON></a>, Austrian cardinal (b. 1905)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Franz_K%C3%B6nig"}]}, {"year": "2006", "text": "<PERSON>, American businessman, invented the chicken nugget (b. 1921)", "html": "2006 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American businessman, invented the <a href=\"https://wikipedia.org/wiki/Chicken_nugget\" title=\"Chicken nugget\">chicken nugget</a> (b. 1921)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American businessman, invented the <a href=\"https://wikipedia.org/wiki/Chicken_nugget\" title=\"Chicken nugget\">chicken nugget</a> (b. 1921)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>"}, {"title": "Chicken nugget", "link": "https://wikipedia.org/wiki/Chicken_nugget"}]}, {"year": "2006", "text": "<PERSON>, Scottish footballer (b. 1944)", "html": "2006 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish footballer (b. 1944)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish footballer (b. 1944)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>"}]}, {"year": "2006", "text": "<PERSON>, American actress (b. 1925)", "html": "2006 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress (b. 1925)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress (b. 1925)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2006", "text": "<PERSON>, American television personality, game show host (b. 1942)", "html": "2006 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American television personality, game show host (b. 1942)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American television personality, game show host (b. 1942)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2007", "text": "<PERSON>, American wrestler and manager (b. 1925)", "html": "2007 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American wrestler and manager (b. 1925)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American wrestler and manager (b. 1925)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2009", "text": "<PERSON>, American actress (b. 1923)", "html": "2009 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress (b. 1923)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress (b. 1923)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2009", "text": "<PERSON>, American businessman (b. 1917)", "html": "2009 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American businessman (b. 1917)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American businessman (b. 1917)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "2010", "text": "<PERSON>, French singer-songwriter (b. 1930)", "html": "2010 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French singer-songwriter (b. 1930)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French singer-songwriter (b. 1930)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "2011", "text": "<PERSON>, Canadian-American ice hockey player (b. 1951)", "html": "2011 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian-American ice hockey player (b. 1951)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian-American ice hockey player (b. 1951)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2013", "text": "<PERSON>, English drummer and songwriter (b. 1957)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English drummer and songwriter (b. 1957)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English drummer and songwriter (b. 1957)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2014", "text": "<PERSON><PERSON><PERSON>, American sergeant, lawyer, and politician, 37th Governor of Florida (b. 1928)", "html": "2014 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American sergeant, lawyer, and politician, 37th <a href=\"https://wikipedia.org/wiki/Governor_of_Florida\" title=\"Governor of Florida\">Governor of Florida</a> (b. 1928)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American sergeant, lawyer, and politician, 37th <a href=\"https://wikipedia.org/wiki/Governor_of_Florida\" title=\"Governor of Florida\">Governor of Florida</a> (b. 1928)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}, {"title": "Governor of Florida", "link": "https://wikipedia.org/wiki/Governor_of_Florida"}]}, {"year": "2014", "text": "<PERSON>, Baron <PERSON>, Irish businessman and politician (b. 1944)", "html": "2014 - <a href=\"https://wikipedia.org/wiki/<PERSON>,_Baron_<PERSON>\" title=\"<PERSON>, Baron <PERSON>\"><PERSON>, Baron <PERSON></a>, Irish businessman and politician (b. 1944)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>,_Baron_<PERSON>\" title=\"<PERSON>, Baron Ball<PERSON>mond\"><PERSON>, Baron <PERSON></a>, Irish businessman and politician (b. 1944)", "links": [{"title": "<PERSON>, Baron <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>,_Baron_<PERSON>"}]}, {"year": "2014", "text": "<PERSON>, Sierra Leonean economist, lawyer, and politician, 3rd President of Sierra Leone (b. 1932)", "html": "2014 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Sierra Leonean economist, lawyer, and politician, 3rd <a href=\"https://wikipedia.org/wiki/President_of_Sierra_Leone\" title=\"President of Sierra Leone\">President of Sierra Leone</a> (b. 1932)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Sierra Leonean economist, lawyer, and politician, 3rd <a href=\"https://wikipedia.org/wiki/President_of_Sierra_Leone\" title=\"President of Sierra Leone\">President of Sierra Leone</a> (b. 1932)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>"}, {"title": "President of Sierra Leone", "link": "https://wikipedia.org/wiki/President_of_Sierra_Leone"}]}, {"year": "2014", "text": "<PERSON><PERSON><PERSON><PERSON>, Lithuanian-Israeli author and screenwriter (b. 1934)", "html": "2014 - <a href=\"https://wikipedia.org/wiki/Icc<PERSON>kas_<PERSON>\" title=\"Icc<PERSON>kas Meras\"><PERSON><PERSON><PERSON><PERSON></a>, Lithuanian-Israeli author and screenwriter (b. 1934)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Icc<PERSON>kas_<PERSON>\" title=\"Icc<PERSON><PERSON> Me<PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Lithuanian-Israeli author and screenwriter (b. 1934)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Icchokas_Meras"}]}, {"year": "2015", "text": "<PERSON>, American baseball player and manager (b. 1924)", "html": "2015 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player and manager (b. 1924)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player and manager (b. 1924)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Al_Rosen"}]}, {"year": "2016", "text": "<PERSON>, American philosopher, mathematician, and computer scientist (b. 1926)", "html": "2016 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American philosopher, mathematician, and computer scientist (b. 1926)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American philosopher, mathematician, and computer scientist (b. 1926)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "2017", "text": "<PERSON>, American author  (b. 1965)", "html": "2017 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author (b. 1965)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author (b. 1965)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "2018", "text": "<PERSON>, Lebanese writer and women's rights activist. (b. 1931)", "html": "2018 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Lebanese writer and women's rights activist. (b. 1931)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Lebanese writer and women's rights activist. (b. 1931)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2021", "text": "Marvelous <PERSON>,  American professional boxer (b. 1954)", "html": "2021 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>ous <PERSON>\"><PERSON><PERSON> <PERSON></a>, American professional boxer (b. 1954)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>ous <PERSON>\"><PERSON><PERSON> <PERSON></a>, American professional boxer (b. 1954)", "links": [{"title": "Marvelous <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>"}]}, {"year": "2021", "text": "<PERSON>, English motorsport commentator and journalist (b. 1923)", "html": "2021 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English motorsport commentator and journalist (b. 1923)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English motorsport commentator and journalist (b. 1923)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2022", "text": "<PERSON>, American actor (b. 1950)", "html": "2022 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Hurt\"><PERSON></a>, American actor (b. 1950)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"William Hurt\"><PERSON></a>, American actor (b. 1950)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2024", "text": "<PERSON>, French admiral (b.1921)", "html": "2024 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French admiral (b.1921)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French admiral (b.1921)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}]}}