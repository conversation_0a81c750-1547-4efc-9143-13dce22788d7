{"date": "January 17", "url": "https://wikipedia.org/wiki/January_17", "data": {"Events": [{"year": "38 BC", "text": "<PERSON><PERSON><PERSON> divorces his wife <PERSON><PERSON><PERSON><PERSON> and marries <PERSON><PERSON>, ending the fragile peace between the Second Triumvirate and <PERSON><PERSON>.", "html": "38 BC - 38 BC - <a href=\"https://wikipedia.org/wiki/Octavian\" class=\"mw-redirect\" title=\"Octavian\"><PERSON><PERSON><PERSON></a> divorces his wife <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_(wife_of_<PERSON>)\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON><PERSON> (wife of <PERSON>)\"><PERSON><PERSON><PERSON><PERSON></a> and marries <a href=\"https://wikipedia.org/wiki/Livia_Drusilla\" class=\"mw-redirect\" title=\"<PERSON>ia Drusilla\"><PERSON><PERSON> Dr<PERSON></a>, ending the fragile peace between the <a href=\"https://wikipedia.org/wiki/Second_Triumvirate\" title=\"Second Triumvirate\">Second Triumvirate</a> and <a href=\"https://wikipedia.org/wiki/Sextus_Pompey\" title=\"Sextus Pompey\"><PERSON><PERSON></a>.", "no_year_html": "38 BC - <a href=\"https://wikipedia.org/wiki/Octavian\" class=\"mw-redirect\" title=\"Octavian\"><PERSON><PERSON><PERSON></a> divorces his wife <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_(wife_of_<PERSON>)\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON><PERSON> (wife of <PERSON>)\"><PERSON><PERSON><PERSON><PERSON></a> and marries <a href=\"https://wikipedia.org/wiki/Livia_Drusilla\" class=\"mw-redirect\" title=\"<PERSON><PERSON> Dr<PERSON>\"><PERSON><PERSON> Dr<PERSON></a>, ending the fragile peace between the <a href=\"https://wikipedia.org/wiki/Second_Triumvirate\" title=\"Second Triumvirate\">Second Triumvirate</a> and <a href=\"https://wikipedia.org/wiki/Sextus_Pompey\" title=\"Sextus Pompey\"><PERSON><PERSON></a>.", "links": [{"title": "Octavian", "link": "https://wikipedia.org/wiki/Octavian"}, {"title": "<PERSON><PERSON><PERSON><PERSON> (wife of <PERSON>)", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_(wife_of_<PERSON>)"}, {"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Livia_Drusilla"}, {"title": "Second Triumvirate", "link": "https://wikipedia.org/wiki/Second_Triumvirate"}, {"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Sextus_Pompey"}]}, {"year": "1362", "text": "Saint <PERSON>' flood kills at least 25,000 people on the shores of the North Sea.", "html": "1362 - <a href=\"https://wikipedia.org/wiki/Saint_Marcellus%27_flood\" class=\"mw-redirect\" title=\"Saint Marcellus' flood\"><PERSON> Marcellus' flood</a> kills at least 25,000 people on the shores of the North Sea.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Saint_Marcellus%27_flood\" class=\"mw-redirect\" title=\"Saint Marcellus' flood\"><PERSON> Marcellus' flood</a> kills at least 25,000 people on the shores of the North Sea.", "links": [{"title": "<PERSON>' flood", "link": "https://wikipedia.org/wiki/Saint_Marcellus%27_flood"}]}, {"year": "1377", "text": "<PERSON> reaches Rome, after deciding to move the Papacy back to Rome from Avignon.", "html": "1377 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"Pope Gregory <PERSON>\">Pope <PERSON></a> reaches Rome, after deciding to move the Papacy back to Rome from <a href=\"https://wikipedia.org/wiki/Avignon\" title=\"Avignon\">Avignon</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"Pope Gregory <PERSON>\">Pope <PERSON></a> reaches Rome, after deciding to move the Papacy back to Rome from <a href=\"https://wikipedia.org/wiki/Avignon\" title=\"Avignon\">Avignon</a>.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "Avignon", "link": "https://wikipedia.org/wiki/Avignon"}]}, {"year": "1524", "text": "<PERSON> sets sail westward from Madeira to find a sea route to the Pacific Ocean.", "html": "1524 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>er<PERSON>\" title=\"<PERSON> Verrazzano\"><PERSON> Verrazzano</a> sets sail westward from <a href=\"https://wikipedia.org/wiki/Madeira\" title=\"Madeira\">Madeira</a> to find a sea route to the Pacific Ocean.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON> Verrazzano\"><PERSON>errazzano</a> sets sail westward from <a href=\"https://wikipedia.org/wiki/Madeira\" title=\"Madeira\">Madeira</a> to find a sea route to the Pacific Ocean.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "Madeira", "link": "https://wikipedia.org/wiki/Madeira"}]}, {"year": "1562", "text": "France grants religious toleration to the Huguenots in the Edict of Saint-Germain.", "html": "1562 - France grants religious toleration to the <a href=\"https://wikipedia.org/wiki/Huguenot\" class=\"mw-redirect\" title=\"Huguenot\">Huguenots</a> in the <a href=\"https://wikipedia.org/wiki/Edict_of_Saint-Germain\" title=\"Edict of Saint-Germain\">Edict of Saint-Germain</a>.", "no_year_html": "France grants religious toleration to the <a href=\"https://wikipedia.org/wiki/Huguenot\" class=\"mw-redirect\" title=\"Huguenot\">Huguenots</a> in the <a href=\"https://wikipedia.org/wiki/Edict_of_Saint-Germain\" title=\"Edict of Saint-Germain\">Edict of Saint-Germain</a>.", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>not"}, {"title": "Edict of Saint-Germain", "link": "https://wikipedia.org/wiki/Edict_of_Saint-Germain"}]}, {"year": "1595", "text": "During the French Wars of Religion, <PERSON> of France declares war on Spain.", "html": "1595 - During the <a href=\"https://wikipedia.org/wiki/French_Wars_of_Religion\" title=\"French Wars of Religion\">French Wars of Religion</a>, <a href=\"https://wikipedia.org/wiki/<PERSON>_IV_of_France\" title=\"Henry IV of France\"><PERSON> of France</a> declares war on Spain.", "no_year_html": "During the <a href=\"https://wikipedia.org/wiki/French_Wars_of_Religion\" title=\"French Wars of Religion\">French Wars of Religion</a>, <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_France\" title=\"Henry IV of France\"><PERSON> of France</a> declares war on Spain.", "links": [{"title": "French Wars of Religion", "link": "https://wikipedia.org/wiki/French_Wars_of_Religion"}, {"title": "<PERSON> of France", "link": "https://wikipedia.org/wiki/<PERSON>_IV_of_France"}]}, {"year": "1608", "text": "Emperor <PERSON><PERSON><PERSON><PERSON> of Ethiopia surprises an Oromo army at Ebenat; his army reportedly kills 12,000 Oromo at the cost of 400 of his men.", "html": "1608 - Emperor <a href=\"https://wikipedia.org/wiki/Susenyos_I\" title=\"Susenyos I\"><PERSON><PERSON>yos I</a> of Ethiopia surprises an <a href=\"https://wikipedia.org/wiki/Oromo_people\" title=\"Oromo people\">Oromo</a> army at Ebenat; his army reportedly kills 12,000 Oromo at the cost of 400 of his men.", "no_year_html": "Emperor <a href=\"https://wikipedia.org/wiki/Susenyo<PERSON>_I\" title=\"Susenyos I\"><PERSON><PERSON>yos I</a> of Ethiopia surprises an <a href=\"https://wikipedia.org/wiki/Oromo_people\" title=\"Oromo people\">Oromo</a> army at Ebenat; his army reportedly kills 12,000 Oromo at the cost of 400 of his men.", "links": [{"title": "Susenyos I", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>s_I"}, {"title": "Oromo people", "link": "https://wikipedia.org/wiki/Oromo_people"}]}, {"year": "1641", "text": "Reapers' War: The Junta de Braços (parliamentary assembly) of the Principality of Catalonia accepts the proposal of establishment of the Catalan Republic under French protection.", "html": "1641 - <a href=\"https://wikipedia.org/wiki/Reapers%27_War\" title=\"Reapers' War\">Reapers' War</a>: The <a href=\"https://wikipedia.org/wiki/Junta_de_Bra%C3%A7os\" title=\"Junta de Braços\">Junta de Braços</a> (parliamentary assembly) of the <a href=\"https://wikipedia.org/wiki/Principality_of_Catalonia\" title=\"Principality of Catalonia\">Principality of Catalonia</a> accepts the proposal of establishment of the <a href=\"https://wikipedia.org/wiki/Catalan_Republic_(1640%E2%80%931641)\" title=\"Catalan Republic (1640-1641)\">Catalan Republic</a> under French protection.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Reapers%27_War\" title=\"Reapers' War\">Reapers' War</a>: The <a href=\"https://wikipedia.org/wiki/Junta_de_Bra%C3%A7os\" title=\"Junta de Braços\">Junta de Braços</a> (parliamentary assembly) of the <a href=\"https://wikipedia.org/wiki/Principality_of_Catalonia\" title=\"Principality of Catalonia\">Principality of Catalonia</a> accepts the proposal of establishment of the <a href=\"https://wikipedia.org/wiki/Catalan_Republic_(1640%E2%80%931641)\" title=\"Catalan Republic (1640-1641)\">Catalan Republic</a> under French protection.", "links": [{"title": "Reapers' War", "link": "https://wikipedia.org/wiki/Reapers%27_War"}, {"title": "Junta de Braços", "link": "https://wikipedia.org/wiki/Junta_de_Bra%C3%A7os"}, {"title": "Principality of Catalonia", "link": "https://wikipedia.org/wiki/Principality_of_Catalonia"}, {"title": "Catalan Republic (1640-1641)", "link": "https://wikipedia.org/wiki/Catalan_Republic_(1640%E2%80%931641)"}]}, {"year": "1648", "text": "England's Long Parliament passes the \"Vote of No Addresses\", breaking off negotiations with King <PERSON> and thereby setting the scene for the second phase of the English Civil War.", "html": "1648 - England's <a href=\"https://wikipedia.org/wiki/Long_Parliament\" title=\"Long Parliament\">Long Parliament</a> passes the \"<a href=\"https://wikipedia.org/wiki/Vote_of_No_Addresses\" title=\"Vote of No Addresses\">Vote of No Addresses</a>\", breaking off negotiations with <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_England\" title=\"<PERSON> of England\">King <PERSON> I</a> and thereby setting the scene for the second phase of the <a href=\"https://wikipedia.org/wiki/English_Civil_War\" title=\"English Civil War\">English Civil War</a>.", "no_year_html": "England's <a href=\"https://wikipedia.org/wiki/Long_Parliament\" title=\"Long Parliament\">Long Parliament</a> passes the \"<a href=\"https://wikipedia.org/wiki/Vote_of_No_Addresses\" title=\"Vote of No Addresses\">Vote of No Addresses</a>\", breaking off negotiations with <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_England\" title=\"<PERSON> I of England\">King <PERSON> I</a> and thereby setting the scene for the second phase of the <a href=\"https://wikipedia.org/wiki/English_Civil_War\" title=\"English Civil War\">English Civil War</a>.", "links": [{"title": "Long Parliament", "link": "https://wikipedia.org/wiki/Long_Parliament"}, {"title": "Vote of No Addresses", "link": "https://wikipedia.org/wiki/Vote_of_No_Addresses"}, {"title": "<PERSON> of England", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_England"}, {"title": "English Civil War", "link": "https://wikipedia.org/wiki/English_Civil_War"}]}, {"year": "1649", "text": "The Second Ormonde Peace creates an alliance between the Irish Royalists and Confederates during the War of the Three Kingdoms. The coalition was then decisively defeated during the Cromwellian conquest of Ireland.", "html": "1649 - The <a href=\"https://wikipedia.org/wiki/Second_Ormonde_Peace\" title=\"Second Ormonde Peace\">Second Ormonde Peace</a> creates an alliance between the <a href=\"https://wikipedia.org/wiki/Cavaliers\" class=\"mw-redirect\" title=\"Cavaliers\">Irish Royalists</a> and <a href=\"https://wikipedia.org/wiki/Irish_Confederates\" class=\"mw-redirect\" title=\"Irish Confederates\">Confederates</a> during the <a href=\"https://wikipedia.org/wiki/War_of_the_Three_Kingdoms\" class=\"mw-redirect\" title=\"War of the Three Kingdoms\">War of the Three Kingdoms</a>. The coalition was then decisively defeated during the <a href=\"https://wikipedia.org/wiki/Cromwellian_conquest_of_Ireland\" title=\"Cromwellian conquest of Ireland\">Cromwellian conquest of Ireland</a>.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Second_Ormonde_Peace\" title=\"Second Ormonde Peace\">Second Ormonde Peace</a> creates an alliance between the <a href=\"https://wikipedia.org/wiki/Cavaliers\" class=\"mw-redirect\" title=\"Cavaliers\">Irish Royalists</a> and <a href=\"https://wikipedia.org/wiki/Irish_Confederates\" class=\"mw-redirect\" title=\"Irish Confederates\">Confederates</a> during the <a href=\"https://wikipedia.org/wiki/War_of_the_Three_Kingdoms\" class=\"mw-redirect\" title=\"War of the Three Kingdoms\">War of the Three Kingdoms</a>. The coalition was then decisively defeated during the <a href=\"https://wikipedia.org/wiki/Cromwellian_conquest_of_Ireland\" title=\"Cromwellian conquest of Ireland\">Cromwellian conquest of Ireland</a>.", "links": [{"title": "Second Ormonde Peace", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON><PERSON>_<PERSON>"}, {"title": "Cavaliers", "link": "https://wikipedia.org/wiki/Cavaliers"}, {"title": "Irish Confederates", "link": "https://wikipedia.org/wiki/Irish_Confederates"}, {"title": "War of the Three Kingdoms", "link": "https://wikipedia.org/wiki/War_of_the_Three_Kingdoms"}, {"title": "Cromwellian conquest of Ireland", "link": "https://wikipedia.org/wiki/Cromwellian_conquest_of_Ireland"}]}, {"year": "1773", "text": "Captain <PERSON> leads the first expedition to sail south of the Antarctic Circle.", "html": "1773 - Captain <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> leads the <a href=\"https://wikipedia.org/wiki/Second_voyage_of_<PERSON>_<PERSON>\" title=\"Second voyage of <PERSON>\">first expedition</a> to sail south of the <a href=\"https://wikipedia.org/wiki/Antarctic_Circle\" title=\"Antarctic Circle\">Antarctic Circle</a>.", "no_year_html": "Captain <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> leads the <a href=\"https://wikipedia.org/wiki/Second_voyage_of_<PERSON>_<PERSON>\" title=\"Second voyage of <PERSON>\">first expedition</a> to sail south of the <a href=\"https://wikipedia.org/wiki/Antarctic_Circle\" title=\"Antarctic Circle\">Antarctic Circle</a>.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Second voyage of <PERSON>", "link": "https://wikipedia.org/wiki/Second_voyage_of_<PERSON>_<PERSON>"}, {"title": "Antarctic Circle", "link": "https://wikipedia.org/wiki/Antarctic_Circle"}]}, {"year": "1781", "text": "American Revolutionary War: Battle of Cowpens: Continental troops under Brigadier General <PERSON> defeat British forces under Lieutenant Colonel <PERSON><PERSON><PERSON> at the battle in South Carolina.", "html": "1781 - <a href=\"https://wikipedia.org/wiki/American_Revolutionary_War\" title=\"American Revolutionary War\">American Revolutionary War</a>: <a href=\"https://wikipedia.org/wiki/Battle_of_Cowpens\" title=\"Battle of Cowpens\">Battle of Cowpens</a>: Continental troops under Brigadier General <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> defeat British forces under Lieutenant Colonel <a href=\"https://wikipedia.org/wiki/Banastre_Tarleton\" title=\"Banastre Tarleton\">Banastre Tarleton</a> at the battle in <a href=\"https://wikipedia.org/wiki/South_Carolina\" title=\"South Carolina\">South Carolina</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/American_Revolutionary_War\" title=\"American Revolutionary War\">American Revolutionary War</a>: <a href=\"https://wikipedia.org/wiki/Battle_of_Cowpens\" title=\"Battle of Cowpens\">Battle of Cowpens</a>: Continental troops under Brigadier General <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> defeat British forces under Lieutenant Colonel <a href=\"https://wikipedia.org/wiki/Banastre_Tarleton\" title=\"Banastre Tarleton\">Banastre Tarleton</a> at the battle in <a href=\"https://wikipedia.org/wiki/South_Carolina\" title=\"South Carolina\">South Carolina</a>.", "links": [{"title": "American Revolutionary War", "link": "https://wikipedia.org/wiki/American_Revolutionary_War"}, {"title": "Battle of Cowpens", "link": "https://wikipedia.org/wiki/Battle_of_Cowpens"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Ban<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Banastre_Tarleton"}, {"title": "South Carolina", "link": "https://wikipedia.org/wiki/South_Carolina"}]}, {"year": "1799", "text": "Maltese patriot <PERSON><PERSON>, along with a number of other patriots, is executed.", "html": "1799 - <a href=\"https://wikipedia.org/wiki/Malta\" title=\"Malta\">Maltese</a> patriot <a href=\"https://wikipedia.org/wiki/Dun_Mi<PERSON>_<PERSON>ri\" title=\"Dun <PERSON>\">Du<PERSON></a>, along with a number of other patriots, is executed.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Malta\" title=\"Malta\">Maltese</a> patriot <a href=\"https://wikipedia.org/wiki/Dun_Mi<PERSON>_<PERSON>ri\" title=\"Dun <PERSON>\">Dun <PERSON></a>, along with a number of other patriots, is executed.", "links": [{"title": "Malta", "link": "https://wikipedia.org/wiki/Malta"}, {"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON><PERSON>_<PERSON>ri"}]}, {"year": "1811", "text": "Mexican War of Independence: In the Battle of Calderón Bridge, a heavily outnumbered Spanish force of 6,000 troops defeats nearly 100,000 Mexican revolutionaries.", "html": "1811 - <a href=\"https://wikipedia.org/wiki/Mexican_War_of_Independence\" title=\"Mexican War of Independence\">Mexican War of Independence</a>: In the <a href=\"https://wikipedia.org/wiki/Battle_of_Calder%C3%B3n_Bridge\" title=\"Battle of Calderón Bridge\">Battle of Calderón Bridge</a>, a heavily outnumbered <a href=\"https://wikipedia.org/wiki/History_of_Spain_(1810%E2%80%9373)\" class=\"mw-redirect\" title=\"History of Spain (1810-73)\">Spanish</a> force of 6,000 troops defeats nearly 100,000 Mexican revolutionaries.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Mexican_War_of_Independence\" title=\"Mexican War of Independence\">Mexican War of Independence</a>: In the <a href=\"https://wikipedia.org/wiki/Battle_of_Calder%C3%B3n_Bridge\" title=\"Battle of Calderón Bridge\">Battle of Calderón Bridge</a>, a heavily outnumbered <a href=\"https://wikipedia.org/wiki/History_of_Spain_(1810%E2%80%9373)\" class=\"mw-redirect\" title=\"History of Spain (1810-73)\">Spanish</a> force of 6,000 troops defeats nearly 100,000 Mexican revolutionaries.", "links": [{"title": "Mexican War of Independence", "link": "https://wikipedia.org/wiki/Mexican_War_of_Independence"}, {"title": "Battle of Calderón Bridge", "link": "https://wikipedia.org/wiki/Battle_of_Calder%C3%B3n_Bridge"}, {"title": "History of Spain (1810-73)", "link": "https://wikipedia.org/wiki/History_of_Spain_(1810%E2%80%9373)"}]}, {"year": "1852", "text": "The United Kingdom signs the Sand River Convention with the South African Republic.", "html": "1852 - The United Kingdom signs the <a href=\"https://wikipedia.org/wiki/Sand_River_Convention\" title=\"Sand River Convention\">Sand River Convention</a> with the <a href=\"https://wikipedia.org/wiki/South_African_Republic\" title=\"South African Republic\">South African Republic</a>.", "no_year_html": "The United Kingdom signs the <a href=\"https://wikipedia.org/wiki/Sand_River_Convention\" title=\"Sand River Convention\">Sand River Convention</a> with the <a href=\"https://wikipedia.org/wiki/South_African_Republic\" title=\"South African Republic\">South African Republic</a>.", "links": [{"title": "Sand River Convention", "link": "https://wikipedia.org/wiki/Sand_River_Convention"}, {"title": "South African Republic", "link": "https://wikipedia.org/wiki/South_African_Republic"}]}, {"year": "1873", "text": "A group of Modoc warriors defeats the United States Army in the First Battle of the Stronghold, part of the Modoc War.", "html": "1873 - A group of <a href=\"https://wikipedia.org/wiki/Modoc_people\" title=\"Modoc people\">Modoc</a> warriors defeats the <a href=\"https://wikipedia.org/wiki/United_States_Army\" title=\"United States Army\">United States Army</a> in the <a href=\"https://wikipedia.org/wiki/First_Battle_of_the_Stronghold\" title=\"First Battle of the Stronghold\">First Battle of the Stronghold</a>, part of the <a href=\"https://wikipedia.org/wiki/Modoc_War\" title=\"Modoc War\">Modoc War</a>.", "no_year_html": "A group of <a href=\"https://wikipedia.org/wiki/Modoc_people\" title=\"Modoc people\">Modoc</a> warriors defeats the <a href=\"https://wikipedia.org/wiki/United_States_Army\" title=\"United States Army\">United States Army</a> in the <a href=\"https://wikipedia.org/wiki/First_Battle_of_the_Stronghold\" title=\"First Battle of the Stronghold\">First Battle of the Stronghold</a>, part of the <a href=\"https://wikipedia.org/wiki/Modoc_War\" title=\"Modoc War\">Modoc War</a>.", "links": [{"title": "Modoc people", "link": "https://wikipedia.org/wiki/Modoc_people"}, {"title": "United States Army", "link": "https://wikipedia.org/wiki/United_States_Army"}, {"title": "First Battle of the Stronghold", "link": "https://wikipedia.org/wiki/First_Battle_of_the_Stronghold"}, {"title": "Modoc War", "link": "https://wikipedia.org/wiki/Modoc_War"}]}, {"year": "1885", "text": "A British force defeats a large Dervish army at the Battle of Abu Klea in the Sudan.", "html": "1885 - A British force defeats a large <a href=\"https://wikipedia.org/wiki/Dervish\" title=\"Dervish\">Dervish</a> army at the <a href=\"https://wikipedia.org/wiki/Battle_of_Abu_Klea\" title=\"Battle of Abu Klea\">Battle of Abu Klea</a> in the <a href=\"https://wikipedia.org/wiki/Sudan\" title=\"Sudan\">Sudan</a>.", "no_year_html": "A British force defeats a large <a href=\"https://wikipedia.org/wiki/Dervish\" title=\"Dervish\">Dervish</a> army at the <a href=\"https://wikipedia.org/wiki/Battle_of_Abu_Klea\" title=\"Battle of Abu Klea\">Battle of Abu Klea</a> in the <a href=\"https://wikipedia.org/wiki/Sudan\" title=\"Sudan\">Sudan</a>.", "links": [{"title": "Dervish", "link": "https://wikipedia.org/wiki/Dervish"}, {"title": "Battle of Abu Klea", "link": "https://wikipedia.org/wiki/Battle_of_Abu_Klea"}, {"title": "Sudan", "link": "https://wikipedia.org/wiki/Sudan"}]}, {"year": "1893", "text": "<PERSON><PERSON><PERSON>, along with the Citizens' Committee of Public Safety, led the Overthrow of the Kingdom of Hawaii and the government of Queen <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>.", "html": "1893 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_Thurston\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, along with the <a href=\"https://wikipedia.org/wiki/Committee_of_Safety_(Hawaii)\" title=\"Committee of Safety (Hawaii)\">Citizens' Committee of Public Safety</a>, led the <a href=\"https://wikipedia.org/wiki/Overthrow_of_the_Kingdom_of_Hawaii\" class=\"mw-redirect\" title=\"Overthrow of the Kingdom of Hawaii\">Overthrow of the Kingdom of Hawaii</a> and the government of <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>oka<PERSON>\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON><PERSON>\">Queen <PERSON><PERSON></a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_Thurston\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, along with the <a href=\"https://wikipedia.org/wiki/Committee_of_Safety_(Hawaii)\" title=\"Committee of Safety (Hawaii)\">Citizens' Committee of Public Safety</a>, led the <a href=\"https://wikipedia.org/wiki/Overthrow_of_the_Kingdom_of_Hawaii\" class=\"mw-redirect\" title=\"Overthrow of the Kingdom of Hawaii\">Overthrow of the Kingdom of Hawaii</a> and the government of <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON><PERSON>\">Queen <PERSON><PERSON></a>.", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON><PERSON>_<PERSON><PERSON>"}, {"title": "Committee of Safety (Hawaii)", "link": "https://wikipedia.org/wiki/Committee_of_Safety_(Hawaii)"}, {"title": "Overthrow of the Kingdom of Hawaii", "link": "https://wikipedia.org/wiki/Overthrow_of_the_Kingdom_of_Hawaii"}, {"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>"}]}, {"year": "1899", "text": "The United States takes possession of Wake Island in the Pacific Ocean.", "html": "1899 - The United States takes possession of <a href=\"https://wikipedia.org/wiki/Wake_Island\" title=\"Wake Island\">Wake Island</a> in the Pacific Ocean.", "no_year_html": "The United States takes possession of <a href=\"https://wikipedia.org/wiki/Wake_Island\" title=\"Wake Island\">Wake Island</a> in the Pacific Ocean.", "links": [{"title": "Wake Island", "link": "https://wikipedia.org/wiki/Wake_Island"}]}, {"year": "1903", "text": "El Yunque National Forest in Puerto Rico becomes part of the United States National Forest System as the Luquillo Forest Reserve.", "html": "1903 - <a href=\"https://wikipedia.org/wiki/El_Yunque_National_Forest\" title=\"El Yunque National Forest\">El Yunque National Forest</a> in <a href=\"https://wikipedia.org/wiki/Puerto_Rico\" title=\"Puerto Rico\">Puerto Rico</a> becomes part of the United States <a href=\"https://wikipedia.org/wiki/National_Forest_System\" class=\"mw-redirect\" title=\"National Forest System\">National Forest System</a> as the Luquillo Forest Reserve.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/El_Yunque_National_Forest\" title=\"El Yunque National Forest\">El Yunque National Forest</a> in <a href=\"https://wikipedia.org/wiki/Puerto_Rico\" title=\"Puerto Rico\">Puerto Rico</a> becomes part of the United States <a href=\"https://wikipedia.org/wiki/National_Forest_System\" class=\"mw-redirect\" title=\"National Forest System\">National Forest System</a> as the Luquillo Forest Reserve.", "links": [{"title": "El Yunque National Forest", "link": "https://wikipedia.org/wiki/El_Yunque_National_Forest"}, {"title": "Puerto Rico", "link": "https://wikipedia.org/wiki/Puerto_Rico"}, {"title": "National Forest System", "link": "https://wikipedia.org/wiki/National_Forest_System"}]}, {"year": "1904", "text": "<PERSON>'s The Cherry Orchard receives its premiere performance at the Moscow Art Theatre.", "html": "1904 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>'s <i><a href=\"https://wikipedia.org/wiki/The_Cherry_Orchard\" title=\"The Cherry Orchard\">The Cherry Orchard</a></i> receives its premiere performance at the <a href=\"https://wikipedia.org/wiki/Moscow_Art_Theatre\" title=\"Moscow Art Theatre\">Moscow Art Theatre</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>'s <i><a href=\"https://wikipedia.org/wiki/The_Cherry_Orchard\" title=\"The Cherry Orchard\">The Cherry Orchard</a></i> receives its premiere performance at the <a href=\"https://wikipedia.org/wiki/Moscow_Art_Theatre\" title=\"Moscow Art Theatre\">Moscow Art Theatre</a>.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}, {"title": "The Cherry Orchard", "link": "https://wikipedia.org/wiki/The_Cherry_Orchard"}, {"title": "Moscow Art Theatre", "link": "https://wikipedia.org/wiki/Moscow_Art_Theatre"}]}, {"year": "1912", "text": "British polar explorer Captain <PERSON> reaches the South Pole, one month after <PERSON><PERSON><PERSON>.", "html": "1912 - British polar explorer Captain <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> <a href=\"https://wikipedia.org/wiki/Terra_Nova_Expedition\" title=\"Terra Nova Expedition\">reaches the South Pole</a>, one month after <a href=\"https://wikipedia.org/wiki/Roald_Amundsen\" title=\"Roald Amundsen\">Roald Amundsen</a>.", "no_year_html": "British polar explorer Captain <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> <a href=\"https://wikipedia.org/wiki/Terra_Nova_Expedition\" title=\"Terra Nova Expedition\">reaches the South Pole</a>, one month after <a href=\"https://wikipedia.org/wiki/Roald_Amundsen\" title=\"Roald Amundsen\">Roa<PERSON> Am<PERSON></a>.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "Terra Nova Expedition", "link": "https://wikipedia.org/wiki/Terra_Nova_Expedition"}, {"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Roald_Am<PERSON>en"}]}, {"year": "1915", "text": "Russia defeats Ottoman Turkey in the Battle of Sarikamish during the Caucasus Campaign of World War I.", "html": "1915 - <a href=\"https://wikipedia.org/wiki/Russia\" title=\"Russia\">Russia</a> defeats <a href=\"https://wikipedia.org/wiki/Ottoman_Empire\" title=\"Ottoman Empire\">Ottoman Turkey</a> in the <a href=\"https://wikipedia.org/wiki/Battle_of_Sarikamish\" title=\"Battle of Sarikamish\">Battle of Sarikamish</a> during the <a href=\"https://wikipedia.org/wiki/Caucasus_Campaign\" class=\"mw-redirect\" title=\"Caucasus Campaign\">Caucasus Campaign</a> of <a href=\"https://wikipedia.org/wiki/World_War_I\" title=\"World War I\">World War I</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Russia\" title=\"Russia\">Russia</a> defeats <a href=\"https://wikipedia.org/wiki/Ottoman_Empire\" title=\"Ottoman Empire\">Ottoman Turkey</a> in the <a href=\"https://wikipedia.org/wiki/Battle_of_Sarikamish\" title=\"Battle of Sarikamish\">Battle of Sarikamish</a> during the <a href=\"https://wikipedia.org/wiki/Caucasus_Campaign\" class=\"mw-redirect\" title=\"Caucasus Campaign\">Caucasus Campaign</a> of <a href=\"https://wikipedia.org/wiki/World_War_I\" title=\"World War I\">World War I</a>.", "links": [{"title": "Russia", "link": "https://wikipedia.org/wiki/Russia"}, {"title": "Ottoman Empire", "link": "https://wikipedia.org/wiki/Ottoman_Empire"}, {"title": "Battle of Sarikamish", "link": "https://wikipedia.org/wiki/Battle_of_Sarikamish"}, {"title": "Caucasus Campaign", "link": "https://wikipedia.org/wiki/Caucasus_Campaign"}, {"title": "World War I", "link": "https://wikipedia.org/wiki/World_War_I"}]}, {"year": "1917", "text": "The United States pays Denmark $25 million for the Virgin Islands.", "html": "1917 - The United States pays Denmark $25 million for the <a href=\"https://wikipedia.org/wiki/United_States_Virgin_Islands\" title=\"United States Virgin Islands\">Virgin Islands</a>.", "no_year_html": "The United States pays Denmark $25 million for the <a href=\"https://wikipedia.org/wiki/United_States_Virgin_Islands\" title=\"United States Virgin Islands\">Virgin Islands</a>.", "links": [{"title": "United States Virgin Islands", "link": "https://wikipedia.org/wiki/United_States_Virgin_Islands"}]}, {"year": "1918", "text": "Finnish Civil War: The first serious battles take place between the Red Guards and the White Guard.", "html": "1918 - <a href=\"https://wikipedia.org/wiki/Finnish_Civil_War\" title=\"Finnish Civil War\">Finnish Civil War</a>: The first serious battles take place between the <a href=\"https://wikipedia.org/wiki/Red_Guards_(Finland)\" title=\"Red Guards (Finland)\">Red Guards</a> and the <a href=\"https://wikipedia.org/wiki/White_Guard_(Finland)\" title=\"White Guard (Finland)\">White Guard</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Finnish_Civil_War\" title=\"Finnish Civil War\">Finnish Civil War</a>: The first serious battles take place between the <a href=\"https://wikipedia.org/wiki/Red_Guards_(Finland)\" title=\"Red Guards (Finland)\">Red Guards</a> and the <a href=\"https://wikipedia.org/wiki/White_Guard_(Finland)\" title=\"White Guard (Finland)\">White Guard</a>.", "links": [{"title": "Finnish Civil War", "link": "https://wikipedia.org/wiki/Finnish_Civil_War"}, {"title": "Red Guards (Finland)", "link": "https://wikipedia.org/wiki/Red_Guards_(Finland)"}, {"title": "White Guard (Finland)", "link": "https://wikipedia.org/wiki/White_Guard_(Finland)"}]}, {"year": "1920", "text": "Alcohol Prohibition begins in the United States as the Volstead Act goes into effect.", "html": "1920 - <a href=\"https://wikipedia.org/wiki/Prohibition_in_the_United_States\" title=\"Prohibition in the United States\">Alcohol Prohibition</a> begins in the United States as the <a href=\"https://wikipedia.org/wiki/Volstead_Act\" title=\"Volstead Act\">Volstead Act</a> goes into effect.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Prohibition_in_the_United_States\" title=\"Prohibition in the United States\">Alcohol Prohibition</a> begins in the United States as the <a href=\"https://wikipedia.org/wiki/Volstead_Act\" title=\"Volstead Act\">Volstead Act</a> goes into effect.", "links": [{"title": "Prohibition in the United States", "link": "https://wikipedia.org/wiki/Prohibition_in_the_United_States"}, {"title": "Volstead Act", "link": "https://wikipedia.org/wiki/Volstead_Act"}]}, {"year": "1941", "text": "Franco-Thai War: Vichy French forces inflict a decisive defeat over the Royal Thai Navy.", "html": "1941 - <a href=\"https://wikipedia.org/wiki/Franco-Thai_War\" title=\"Franco-Thai War\">Franco-Thai War</a>: <a href=\"https://wikipedia.org/wiki/Vichy_France\" title=\"Vichy France\">Vichy French</a> forces <a href=\"https://wikipedia.org/wiki/Battle_of_Ko_Chang\" title=\"Battle of Ko Chang\">inflict a decisive defeat</a> over the <a href=\"https://wikipedia.org/wiki/Royal_Thai_Navy\" title=\"Royal Thai Navy\">Royal Thai Navy</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Franco-Thai_War\" title=\"Franco-Thai War\">Franco-Thai War</a>: <a href=\"https://wikipedia.org/wiki/Vichy_France\" title=\"Vichy France\">Vichy French</a> forces <a href=\"https://wikipedia.org/wiki/Battle_of_Ko_Chang\" title=\"Battle of Ko Chang\">inflict a decisive defeat</a> over the <a href=\"https://wikipedia.org/wiki/Royal_Thai_Navy\" title=\"Royal Thai Navy\">Royal Thai Navy</a>.", "links": [{"title": "Franco-Thai War", "link": "https://wikipedia.org/wiki/Franco-Thai_War"}, {"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Vichy_France"}, {"title": "Battle of Ko Chang", "link": "https://wikipedia.org/wiki/Battle_of_Ko_Chang"}, {"title": "Royal Thai Navy", "link": "https://wikipedia.org/wiki/Royal_Thai_Navy"}]}, {"year": "1943", "text": "World War II: Greek submarine <PERSON>nik<PERSON> captures the 200-ton sailing vessel <PERSON><PERSON><PERSON> and mans her with part of her crew.", "html": "1943 - <a href=\"https://wikipedia.org/wiki/World_War_II\" title=\"World War II\">World War II</a>: <a href=\"https://wikipedia.org/wiki/Greek_submarine_Papanikolis_(Y-2)\" title=\"Greek submarine <PERSON>nik<PERSON> (Y-2)\">Greek submarine <PERSON><PERSON><PERSON></a> captures the 200-ton sailing vessel <i><PERSON><PERSON><PERSON></i> and mans her with part of her crew.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/World_War_II\" title=\"World War II\">World War II</a>: <a href=\"https://wikipedia.org/wiki/Greek_submarine_Papanikolis_(Y-2)\" title=\"Greek submarine <PERSON>nik<PERSON> (Y-2)\">Greek submarine <PERSON><PERSON><PERSON></a> captures the 200-ton sailing vessel <i><PERSON><PERSON><PERSON></i> and mans her with part of her crew.", "links": [{"title": "World War II", "link": "https://wikipedia.org/wiki/World_War_II"}, {"title": "Greek submarine Papanik<PERSON> (Y-2)", "link": "https://wikipedia.org/wiki/Greek_submarine_Papanikolis_(Y-2)"}]}, {"year": "1944", "text": "World War II: Allied forces launch the first of four assaults on Monte Cassino with the intention of breaking through the Winter Line and seizing Rome, an effort that would ultimately take four months and cost 105,000 Allied casualties.", "html": "1944 - World War II: <a href=\"https://wikipedia.org/wiki/Allies_of_World_War_II\" title=\"Allies of World War II\">Allied</a> forces launch the first of <a href=\"https://wikipedia.org/wiki/Battle_of_Monte_Cassino\" title=\"Battle of Monte Cassino\">four assaults</a> on <a href=\"https://wikipedia.org/wiki/Monte_Cassino\" title=\"Monte Cassino\">Monte Cassino</a> with the intention of breaking through the <a href=\"https://wikipedia.org/wiki/Winter_Line\" title=\"Winter Line\">Winter Line</a> and seizing Rome, an effort that would ultimately take four months and cost 105,000 Allied casualties.", "no_year_html": "World War II: <a href=\"https://wikipedia.org/wiki/Allies_of_World_War_II\" title=\"Allies of World War II\">Allied</a> forces launch the first of <a href=\"https://wikipedia.org/wiki/Battle_of_Monte_Cassino\" title=\"Battle of Monte Cassino\">four assaults</a> on <a href=\"https://wikipedia.org/wiki/Monte_Cassino\" title=\"Monte Cassino\">Monte Cassino</a> with the intention of breaking through the <a href=\"https://wikipedia.org/wiki/Winter_Line\" title=\"Winter Line\">Winter Line</a> and seizing Rome, an effort that would ultimately take four months and cost 105,000 Allied casualties.", "links": [{"title": "Allies of World War II", "link": "https://wikipedia.org/wiki/Allies_of_World_War_II"}, {"title": "Battle of Monte Cassino", "link": "https://wikipedia.org/wiki/Battle_of_Monte_Cassino"}, {"title": "Monte Cassino", "link": "https://wikipedia.org/wiki/Monte_Cassino"}, {"title": "Winter Line", "link": "https://wikipedia.org/wiki/Winter_Line"}]}, {"year": "1945", "text": "World War II: The Vistula-Oder Offensive forces German troops out of Warsaw.", "html": "1945 - World War II: The <a href=\"https://wikipedia.org/wiki/Vistula%E2%80%93Oder_Offensive\" class=\"mw-redirect\" title=\"Vistula-Oder Offensive\">Vistula-Oder Offensive</a> forces German troops out of <a href=\"https://wikipedia.org/wiki/Warsaw\" title=\"Warsaw\">Warsaw</a>.", "no_year_html": "World War II: The <a href=\"https://wikipedia.org/wiki/Vistula%E2%80%93Oder_Offensive\" class=\"mw-redirect\" title=\"Vistula-Oder Offensive\">Vistula-Oder Offensive</a> forces German troops out of <a href=\"https://wikipedia.org/wiki/Warsaw\" title=\"Warsaw\">Warsaw</a>.", "links": [{"title": "Vistula-Oder Offensive", "link": "https://wikipedia.org/wiki/Vistula%E2%80%93Oder_Offensive"}, {"title": "Warsaw", "link": "https://wikipedia.org/wiki/Warsaw"}]}, {"year": "1945", "text": "The SS-Totenkopfverbände begin the evacuation of the Auschwitz concentration camp as the Red Army closes in.", "html": "1945 - The <i><a href=\"https://wikipedia.org/wiki/SS-Totenkopfverb%C3%A4nde\" title=\"SS-Totenkopfverbände\">SS-Totenkopfverbände</a></i> begin the evacuation of the <a href=\"https://wikipedia.org/wiki/Auschwitz_concentration_camp\" title=\"Auschwitz concentration camp\">Auschwitz concentration camp</a> as the <a href=\"https://wikipedia.org/wiki/Red_Army\" title=\"Red Army\">Red Army</a> closes in.", "no_year_html": "The <i><a href=\"https://wikipedia.org/wiki/SS-Totenkopfverb%C3%A4nde\" title=\"SS-Totenkopfverbände\">SS-Totenkopfverbände</a></i> begin the evacuation of the <a href=\"https://wikipedia.org/wiki/Auschwitz_concentration_camp\" title=\"Auschwitz concentration camp\">Auschwitz concentration camp</a> as the <a href=\"https://wikipedia.org/wiki/Red_Army\" title=\"Red Army\">Red Army</a> closes in.", "links": [{"title": "SS-Totenkopfverbände", "link": "https://wikipedia.org/wiki/SS-Totenkopfverb%C3%A4nde"}, {"title": "Auschwitz concentration camp", "link": "https://wikipedia.org/wiki/Auschwitz_concentration_camp"}, {"title": "Red Army", "link": "https://wikipedia.org/wiki/Red_Army"}]}, {"year": "1945", "text": "Swedish diplomat <PERSON> is taken into Soviet custody while in Hungary; he is never publicly seen again.", "html": "1945 - Swedish diplomat <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> is taken into <a href=\"https://wikipedia.org/wiki/Soviet_Union\" title=\"Soviet Union\">Soviet</a> custody while in Hungary; he is never publicly seen again.", "no_year_html": "Swedish diplomat <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> is taken into <a href=\"https://wikipedia.org/wiki/Soviet_Union\" title=\"Soviet Union\">Soviet</a> custody while in Hungary; he is never publicly seen again.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}, {"title": "Soviet Union", "link": "https://wikipedia.org/wiki/Soviet_Union"}]}, {"year": "1946", "text": "The UN Security Council holds its first session.", "html": "1946 - The <a href=\"https://wikipedia.org/wiki/UN_Security_Council\" class=\"mw-redirect\" title=\"UN Security Council\">UN Security Council</a> holds its first session.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/UN_Security_Council\" class=\"mw-redirect\" title=\"UN Security Council\">UN Security Council</a> holds its first session.", "links": [{"title": "UN Security Council", "link": "https://wikipedia.org/wiki/UN_Security_Council"}]}, {"year": "1948", "text": "The Renville Agreement between the Netherlands and Indonesia is ratified.", "html": "1948 - The <a href=\"https://wikipedia.org/wiki/Renville_Agreement\" title=\"Renville Agreement\">Renville Agreement</a> between the <a href=\"https://wikipedia.org/wiki/Netherlands\" title=\"Netherlands\">Netherlands</a> and <a href=\"https://wikipedia.org/wiki/Indonesia\" title=\"Indonesia\">Indonesia</a> is ratified.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Renville_Agreement\" title=\"Renville Agreement\">Renville Agreement</a> between the <a href=\"https://wikipedia.org/wiki/Netherlands\" title=\"Netherlands\">Netherlands</a> and <a href=\"https://wikipedia.org/wiki/Indonesia\" title=\"Indonesia\">Indonesia</a> is ratified.", "links": [{"title": "Renville Agreement", "link": "https://wikipedia.org/wiki/Renville_Agreement"}, {"title": "Netherlands", "link": "https://wikipedia.org/wiki/Netherlands"}, {"title": "Indonesia", "link": "https://wikipedia.org/wiki/Indonesia"}]}, {"year": "1950", "text": "The Great Brink's Robbery: Eleven thieves steal more than $2 million from an armored car company's offices in Boston.", "html": "1950 - The <a href=\"https://wikipedia.org/wiki/Great_Brink%27s_Rob<PERSON>\" title=\"Great Brink's Robbery\">Great Brink's <PERSON><PERSON></a>: Eleven thieves steal more than $2 million from an armored car company's offices in <a href=\"https://wikipedia.org/wiki/Boston\" title=\"Boston\">Boston</a>.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Great_Brink%27s_<PERSON><PERSON>\" title=\"Great Brink's Robbery\">Great Brink's <PERSON><PERSON></a>: Eleven thieves steal more than $2 million from an armored car company's offices in <a href=\"https://wikipedia.org/wiki/Boston\" title=\"Boston\">Boston</a>.", "links": [{"title": "Great Brink's Robbery", "link": "https://wikipedia.org/wiki/Great_Brink%27s_<PERSON><PERSON>"}, {"title": "Boston", "link": "https://wikipedia.org/wiki/Boston"}]}, {"year": "1950", "text": "United Nations Security Council Resolution 79 relating to arms control is adopted.", "html": "1950 - <a href=\"https://wikipedia.org/wiki/United_Nations_Security_Council_Resolution_79\" title=\"United Nations Security Council Resolution 79\">United Nations Security Council Resolution 79</a> relating to <a href=\"https://wikipedia.org/wiki/Arms_control\" title=\"Arms control\">arms control</a> is adopted.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/United_Nations_Security_Council_Resolution_79\" title=\"United Nations Security Council Resolution 79\">United Nations Security Council Resolution 79</a> relating to <a href=\"https://wikipedia.org/wiki/Arms_control\" title=\"Arms control\">arms control</a> is adopted.", "links": [{"title": "United Nations Security Council Resolution 79", "link": "https://wikipedia.org/wiki/United_Nations_Security_Council_Resolution_79"}, {"title": "Arms control", "link": "https://wikipedia.org/wiki/Arms_control"}]}, {"year": "1961", "text": "U.S. President <PERSON> delivers a televised farewell address to the nation three days before leaving office, in which he warns against the accumulation of power by the \"military-industrial complex\" as well as the dangers of massive spending, especially deficit spending.", "html": "1961 - U.S. President <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> delivers a televised <a href=\"https://wikipedia.org/wiki/<PERSON>%27s_farewell_address\" class=\"mw-redirect\" title=\"<PERSON>'s farewell address\">farewell address to the nation</a> three days before leaving office, in which he warns against the accumulation of power by the \"<a href=\"https://wikipedia.org/wiki/Military%E2%80%93industrial_complex\" title=\"Military-industrial complex\">military-industrial complex</a>\" as well as the dangers of massive spending, especially deficit spending.", "no_year_html": "U.S. President <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> delivers a televised <a href=\"https://wikipedia.org/wiki/<PERSON>%27s_farewell_address\" class=\"mw-redirect\" title=\"<PERSON>'s farewell address\">farewell address to the nation</a> three days before leaving office, in which he warns against the accumulation of power by the \"<a href=\"https://wikipedia.org/wiki/Military%E2%80%93industrial_complex\" title=\"Military-industrial complex\">military-industrial complex</a>\" as well as the dangers of massive spending, especially deficit spending.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "<PERSON>'s farewell address", "link": "https://wikipedia.org/wiki/Eisenhower%27s_farewell_address"}, {"title": "Military-industrial complex", "link": "https://wikipedia.org/wiki/Military%E2%80%93industrial_complex"}]}, {"year": "1961", "text": "Former Congolese Prime Minister <PERSON><PERSON> is murdered in circumstances suggesting the support and complicity of the governments of Belgium and the United States.", "html": "1961 - Former <a href=\"https://wikipedia.org/wiki/Republic_of_the_Congo_(L%C3%A9opoldville)\" title=\"Republic of the Congo (Léopoldville)\">Congolese</a> Prime Minister <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a> is murdered in circumstances suggesting the support and complicity of the governments of Belgium and the United States.", "no_year_html": "Former <a href=\"https://wikipedia.org/wiki/Republic_of_the_Congo_(L%C3%A9opoldville)\" title=\"Republic of the Congo (Léopoldville)\">Congolese</a> Prime Minister <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a> is murdered in circumstances suggesting the support and complicity of the governments of Belgium and the United States.", "links": [{"title": "Republic of the Congo (Léopoldville)", "link": "https://wikipedia.org/wiki/Republic_of_the_Congo_(L%C3%A9opoldville)"}, {"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1966", "text": "Palomares incident: A B-52 bomber collides with a KC-135 Stratotanker over Spain, killing seven airmen, and dropping three 70-kiloton nuclear bombs near the town of Palomares and another one into the sea.", "html": "1966 - <a href=\"https://wikipedia.org/wiki/1966_Palomares_B-52_crash\" class=\"mw-redirect\" title=\"1966 Palomares B-52 crash\">Palomares incident</a>: A <a href=\"https://wikipedia.org/wiki/Boeing_B-52_Stratofortress\" title=\"Boeing B-52 Stratofortress\">B-52</a> bomber collides with a <a href=\"https://wikipedia.org/wiki/KC-135_Stratotanker\" class=\"mw-redirect\" title=\"KC-135 Stratotanker\">KC-135 Stratotanker</a> over Spain, killing seven airmen, and dropping three 70-kiloton <a href=\"https://wikipedia.org/wiki/B28_nuclear_bomb\" title=\"B28 nuclear bomb\">nuclear bombs</a> near the town of <a href=\"https://wikipedia.org/wiki/Palomares,_Almer%C3%ADa\" title=\"Palomares, Almería\">Palomares</a> and another one into the sea.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/1966_Palomares_B-52_crash\" class=\"mw-redirect\" title=\"1966 Palomares B-52 crash\">Palomares incident</a>: A <a href=\"https://wikipedia.org/wiki/Boeing_B-52_Stratofortress\" title=\"Boeing B-52 Stratofortress\">B-52</a> bomber collides with a <a href=\"https://wikipedia.org/wiki/KC-135_Stratotanker\" class=\"mw-redirect\" title=\"KC-135 Stratotanker\">KC-135 Stratotanker</a> over Spain, killing seven airmen, and dropping three 70-kiloton <a href=\"https://wikipedia.org/wiki/B28_nuclear_bomb\" title=\"B28 nuclear bomb\">nuclear bombs</a> near the town of <a href=\"https://wikipedia.org/wiki/Palomares,_Almer%C3%ADa\" title=\"Palomares, Almería\">Palomares</a> and another one into the sea.", "links": [{"title": "1966 Palomares B-52 crash", "link": "https://wikipedia.org/wiki/1966_Palomares_B-52_crash"}, {"title": "Boeing B-52 Stratofortress", "link": "https://wikipedia.org/wiki/Boeing_B-52_Stratofortress"}, {"title": "KC-135 Stratotanker", "link": "https://wikipedia.org/wiki/KC-135_Stratotanker"}, {"title": "B28 nuclear bomb", "link": "https://wikipedia.org/wiki/B28_nuclear_bomb"}, {"title": "Palomares, Almería", "link": "https://wikipedia.org/wiki/Palomares,_Almer%C3%ADa"}]}, {"year": "1969", "text": "Black Panther Party members <PERSON><PERSON><PERSON> and <PERSON> are killed during a meeting in Campbell Hall on the campus of UCLA.", "html": "1969 - <a href=\"https://wikipedia.org/wiki/Black_Panther_Party\" title=\"Black Panther Party\">Black Panther Party</a> members <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON> <PERSON>\"><PERSON><PERSON><PERSON></a> and <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> are killed during a meeting in Campbell Hall on the campus of <a href=\"https://wikipedia.org/wiki/UCLA\" class=\"mw-redirect\" title=\"UCLA\">UCLA</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Black_Panther_Party\" title=\"Black Panther Party\">Black Panther Party</a> members <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON> <PERSON>\"><PERSON><PERSON><PERSON></a> and <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> are killed during a meeting in Campbell Hall on the campus of <a href=\"https://wikipedia.org/wiki/UCLA\" class=\"mw-redirect\" title=\"UCLA\">UCLA</a>.", "links": [{"title": "Black Panther Party", "link": "https://wikipedia.org/wiki/Black_Panther_Party"}, {"title": "Bunchy <PERSON>", "link": "https://wikipedia.org/wiki/Bunch<PERSON>_<PERSON>"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}, {"title": "UCLA", "link": "https://wikipedia.org/wiki/UCLA"}]}, {"year": "1977", "text": "Capital punishment in the United States resumes after a ten-year hiatus, as convicted murderer <PERSON> is executed by firing squad in Utah.", "html": "1977 - <a href=\"https://wikipedia.org/wiki/Capital_punishment_in_the_United_States\" title=\"Capital punishment in the United States\">Capital punishment in the United States</a> resumes after a ten-year hiatus, as convicted murderer <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> is <a href=\"https://wikipedia.org/wiki/Execution_by_firing_squad\" title=\"Execution by firing squad\">executed by firing squad</a> in Utah.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Capital_punishment_in_the_United_States\" title=\"Capital punishment in the United States\">Capital punishment in the United States</a> resumes after a ten-year hiatus, as convicted murderer <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> is <a href=\"https://wikipedia.org/wiki/Execution_by_firing_squad\" title=\"Execution by firing squad\">executed by firing squad</a> in Utah.", "links": [{"title": "Capital punishment in the United States", "link": "https://wikipedia.org/wiki/Capital_punishment_in_the_United_States"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Execution by firing squad", "link": "https://wikipedia.org/wiki/Execution_by_firing_squad"}]}, {"year": "1981", "text": "President of the Philippines <PERSON> lifts martial law eight years and five months after declaring it.", "html": "1981 - <a href=\"https://wikipedia.org/wiki/President_of_the_Philippines\" title=\"President of the Philippines\">President of the Philippines</a> <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> lifts <a href=\"https://wikipedia.org/wiki/Martial_law_in_the_Philippines\" title=\"Martial law in the Philippines\">martial law</a> eight years and five months after declaring it.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/President_of_the_Philippines\" title=\"President of the Philippines\">President of the Philippines</a> <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> lifts <a href=\"https://wikipedia.org/wiki/Martial_law_in_the_Philippines\" title=\"Martial law in the Philippines\">martial law</a> eight years and five months after declaring it.", "links": [{"title": "President of the Philippines", "link": "https://wikipedia.org/wiki/President_of_the_Philippines"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Martial law in the Philippines", "link": "https://wikipedia.org/wiki/Martial_law_in_the_Philippines"}]}, {"year": "1991", "text": "Gulf War: Operation Desert Storm begins early in the morning as aircraft strike positions across Iraq, it is also the first major combat sortie for the F-117. LCDR <PERSON>'s F/A-18C Hornet from VFA-81 is shot down by a Mig-25 and is the first American casualty of the War. Iraq fires eight Scud missiles into Israel in an unsuccessful bid to provoke Israeli retaliation.", "html": "1991 - <a href=\"https://wikipedia.org/wiki/Gulf_War\" title=\"Gulf War\">Gulf War</a>: <a href=\"https://wikipedia.org/wiki/Operation_Desert_Storm\" class=\"mw-redirect\" title=\"Operation Desert Storm\">Operation Desert Storm</a> begins early in the morning as aircraft strike positions across Iraq, it is also the first major combat sortie for the <a href=\"https://wikipedia.org/wiki/Lockheed_F-117_Nighthawk\" title=\"Lockheed F-117 Nighthawk\">F-117</a>. <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\">LCDR <PERSON>'s</a> <a href=\"https://wikipedia.org/wiki/McDonnell_Douglas_F/A-18_Hornet\" title=\"McDonnell Douglas F/A-18 Hornet\">F/A-18C Hornet</a> from <a href=\"https://wikipedia.org/wiki/VFA-81\" title=\"VFA-81\">VFA-81</a> is shot down by a <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>-<PERSON><PERSON>_MiG-25\" title=\"<PERSON><PERSON><PERSON>-<PERSON><PERSON>vich MiG-25\">Mig-25</a> and is the first American casualty of the War. <a href=\"https://wikipedia.org/wiki/Iraq\" title=\"Iraq\">Iraq</a> fires eight <a href=\"https://wikipedia.org/wiki/Scud\" class=\"mw-redirect\" title=\"Scud\">Scud</a> missiles into <a href=\"https://wikipedia.org/wiki/Israel\" title=\"Israel\">Israel</a> in an unsuccessful bid to provoke Israeli retaliation.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Gulf_War\" title=\"Gulf War\">Gulf War</a>: <a href=\"https://wikipedia.org/wiki/Operation_Desert_Storm\" class=\"mw-redirect\" title=\"Operation Desert Storm\">Operation Desert Storm</a> begins early in the morning as aircraft strike positions across Iraq, it is also the first major combat sortie for the <a href=\"https://wikipedia.org/wiki/Lockheed_F-117_Nighthawk\" title=\"Lockheed F-117 Nighthawk\">F-117</a>. <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\">LCD<PERSON> <PERSON>'s</a> <a href=\"https://wikipedia.org/wiki/McDonnell_Douglas_F/A-18_Hornet\" title=\"McDonnell Douglas F/A-18 Hornet\">F/A-18C <PERSON>t</a> from <a href=\"https://wikipedia.org/wiki/VFA-81\" title=\"VFA-81\">VFA-81</a> is shot down by a <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>-<PERSON><PERSON>_MiG-25\" title=\"<PERSON><PERSON><PERSON>-<PERSON><PERSON>vich MiG-25\">Mig-25</a> and is the first American casualty of the War. <a href=\"https://wikipedia.org/wiki/Iraq\" title=\"Iraq\">Iraq</a> fires eight <a href=\"https://wikipedia.org/wiki/Scud\" class=\"mw-redirect\" title=\"Scud\">Scud</a> missiles into <a href=\"https://wikipedia.org/wiki/Israel\" title=\"Israel\">Israel</a> in an unsuccessful bid to provoke Israeli retaliation.", "links": [{"title": "Gulf War", "link": "https://wikipedia.org/wiki/Gulf_War"}, {"title": "Operation Desert Storm", "link": "https://wikipedia.org/wiki/Operation_Desert_Storm"}, {"title": "Lockheed F-117 Nighthawk", "link": "https://wikipedia.org/wiki/Lockheed_F-117_Nighthawk"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "McDonnell Douglas F/A-18 Hornet", "link": "https://wikipedia.org/wiki/<PERSON>_Douglas_F/A-18_Hornet"}, {"title": "VFA-81", "link": "https://wikipedia.org/wiki/VFA-81"}, {"title": "Miko<PERSON><PERSON><PERSON><PERSON><PERSON> MiG-25", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>-<PERSON><PERSON><PERSON>_MiG-25"}, {"title": "Iraq", "link": "https://wikipedia.org/wiki/Iraq"}, {"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Scud"}, {"title": "Israel", "link": "https://wikipedia.org/wiki/Israel"}]}, {"year": "1991", "text": "Crown Prince <PERSON> of Norway becomes King <PERSON>, following the death of his father, King <PERSON><PERSON>.", "html": "1991 - Crown Prince <PERSON> of Norway becomes <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Norway\" class=\"mw-redirect\" title=\"<PERSON> of Norway\">King <PERSON> V</a>, following the death of his father, <a href=\"https://wikipedia.org/wiki/<PERSON>lav_V_of_Norway\" class=\"mw-redirect\" title=\"<PERSON><PERSON> V of Norway\">King <PERSON><PERSON> V</a>.", "no_year_html": "Crown Prince <PERSON> of Norway becomes <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Norway\" class=\"mw-redirect\" title=\"<PERSON> of Norway\">King <PERSON> V</a>, following the death of his father, <a href=\"https://wikipedia.org/wiki/<PERSON>lav_V_of_Norway\" class=\"mw-redirect\" title=\"<PERSON><PERSON> V of Norway\">King <PERSON><PERSON> V</a>.", "links": [{"title": "<PERSON> of Norway", "link": "https://wikipedia.org/wiki/Harald_V_of_Norway"}, {"title": "<PERSON><PERSON> of <PERSON>", "link": "https://wikipedia.org/wiki/Olav_V_of_Norway"}]}, {"year": "1992", "text": "During a visit to South Korea, Japanese Prime Minister <PERSON><PERSON> apologizes for forcing Korean women into sexual slavery during World War II.", "html": "1992 - During a visit to South Korea, <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Japan\" title=\"Prime Minister of Japan\">Japanese Prime Minister</a> <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a> apologizes for forcing Korean women into <a href=\"https://wikipedia.org/wiki/Comfort_women\" title=\"Comfort women\">sexual slavery</a> during World War II.", "no_year_html": "During a visit to South Korea, <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Japan\" title=\"Prime Minister of Japan\">Japanese Prime Minister</a> <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a> apologizes for forcing Korean women into <a href=\"https://wikipedia.org/wiki/Comfort_women\" title=\"Comfort women\">sexual slavery</a> during World War II.", "links": [{"title": "Prime Minister of Japan", "link": "https://wikipedia.org/wiki/Prime_Minister_of_Japan"}, {"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}, {"title": "Comfort women", "link": "https://wikipedia.org/wiki/Comfort_women"}]}, {"year": "1994", "text": "The 6.7 Mw  Northridge earthquake shakes the Greater Los Angeles Area with a maximum Mercalli intensity of IX (Violent), leaving 57 people dead and more than 8,700 injured.", "html": "1994 - The 6.7 M<sub>w</sub>  <a href=\"https://wikipedia.org/wiki/1994_Northridge_earthquake\" title=\"1994 Northridge earthquake\">Northridge earthquake</a> shakes the <a href=\"https://wikipedia.org/wiki/Greater_Los_Angeles_Area\" class=\"mw-redirect\" title=\"Greater Los Angeles Area\">Greater Los Angeles Area</a> with a maximum <a href=\"https://wikipedia.org/wiki/Mercalli_intensity_scale\" class=\"mw-redirect\" title=\"Mercalli intensity scale\">Mercalli intensity</a> of IX (<i>Violent</i>), leaving 57 people dead and more than 8,700 injured.", "no_year_html": "The 6.7 M<sub>w</sub>  <a href=\"https://wikipedia.org/wiki/1994_Northridge_earthquake\" title=\"1994 Northridge earthquake\">Northridge earthquake</a> shakes the <a href=\"https://wikipedia.org/wiki/Greater_Los_Angeles_Area\" class=\"mw-redirect\" title=\"Greater Los Angeles Area\">Greater Los Angeles Area</a> with a maximum <a href=\"https://wikipedia.org/wiki/Mercalli_intensity_scale\" class=\"mw-redirect\" title=\"Mercalli intensity scale\">Mercalli intensity</a> of IX (<i>Violent</i>), leaving 57 people dead and more than 8,700 injured.", "links": [{"title": "1994 Northridge earthquake", "link": "https://wikipedia.org/wiki/1994_Northridge_earthquake"}, {"title": "Greater Los Angeles Area", "link": "https://wikipedia.org/wiki/Greater_Los_Angeles_Area"}, {"title": "Mercalli intensity scale", "link": "https://wikipedia.org/wiki/Mercalli_intensity_scale"}]}, {"year": "1995", "text": "The 6.9 Mw  Great Hanshin earthquake shakes the southern Hyōgo Prefecture with a maximum Shindo of 7, leaving 5,502-6,434 people dead, and 251,301-310,000 displaced.", "html": "1995 - The 6.9 M<sub>w</sub>  <a href=\"https://wikipedia.org/wiki/Great_Hanshin_earthquake\" title=\"Great Hanshin earthquake\">Great Hanshin earthquake</a> shakes the southern <a href=\"https://wikipedia.org/wiki/Hy%C5%8Dgo_Prefecture\" title=\"Hyōgo Prefecture\">Hyōgo Prefecture</a> with a maximum <a href=\"https://wikipedia.org/wiki/Japan_Meteorological_Agency_seismic_intensity_scale\" title=\"Japan Meteorological Agency seismic intensity scale\">Shindo</a> of 7, leaving 5,502-6,434 people dead, and 251,301-310,000 displaced.", "no_year_html": "The 6.9 M<sub>w</sub>  <a href=\"https://wikipedia.org/wiki/Great_Hanshin_earthquake\" title=\"Great Hanshin earthquake\">Great Hanshin earthquake</a> shakes the southern <a href=\"https://wikipedia.org/wiki/Hy%C5%8Dgo_Prefecture\" title=\"Hyōgo Prefecture\">Hyōgo Prefecture</a> with a maximum <a href=\"https://wikipedia.org/wiki/Japan_Meteorological_Agency_seismic_intensity_scale\" title=\"Japan Meteorological Agency seismic intensity scale\">Shindo</a> of 7, leaving 5,502-6,434 people dead, and 251,301-310,000 displaced.", "links": [{"title": "Great Hanshin earthquake", "link": "https://wikipedia.org/wiki/Great_Hanshin_earthquake"}, {"title": "Hyōgo Prefecture", "link": "https://wikipedia.org/wiki/Hy%C5%8Dgo_Prefecture"}, {"title": "Japan Meteorological Agency seismic intensity scale", "link": "https://wikipedia.org/wiki/Japan_Meteorological_Agency_seismic_intensity_scale"}]}, {"year": "1996", "text": "The Czech Republic applies for membership in the European Union.", "html": "1996 - The <a href=\"https://wikipedia.org/wiki/Czech_Republic\" title=\"Czech Republic\">Czech Republic</a> applies for membership in the <a href=\"https://wikipedia.org/wiki/European_Union\" title=\"European Union\">European Union</a>.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Czech_Republic\" title=\"Czech Republic\">Czech Republic</a> applies for membership in the <a href=\"https://wikipedia.org/wiki/European_Union\" title=\"European Union\">European Union</a>.", "links": [{"title": "Czech Republic", "link": "https://wikipedia.org/wiki/Czech_Republic"}, {"title": "European Union", "link": "https://wikipedia.org/wiki/European_Union"}]}, {"year": "1997", "text": "Cape Canaveral Air Force Station: A Delta II carrying the GPS IIR-1 satellite explodes 13 seconds after launch, dropping 250 tons of burning rocket remains around the launch pad.", "html": "1997 - <a href=\"https://wikipedia.org/wiki/Cape_Canaveral_Air_Force_Station\" class=\"mw-redirect\" title=\"Cape Canaveral Air Force Station\">Cape Canaveral Air Force Station</a>: A <a href=\"https://wikipedia.org/wiki/Delta_II\" title=\"Delta II\">Delta II</a> carrying the <a href=\"https://wikipedia.org/wiki/GPS_IIR-1\" title=\"GPS IIR-1\">GPS IIR-1</a> satellite explodes 13 seconds after launch, dropping 250 tons of burning rocket remains around the launch pad.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Cape_Canaveral_Air_Force_Station\" class=\"mw-redirect\" title=\"Cape Canaveral Air Force Station\">Cape Canaveral Air Force Station</a>: A <a href=\"https://wikipedia.org/wiki/Delta_II\" title=\"Delta II\">Delta II</a> carrying the <a href=\"https://wikipedia.org/wiki/GPS_IIR-1\" title=\"GPS IIR-1\">GPS IIR-1</a> satellite explodes 13 seconds after launch, dropping 250 tons of burning rocket remains around the launch pad.", "links": [{"title": "Cape Canaveral Air Force Station", "link": "https://wikipedia.org/wiki/Cape_Canaveral_Air_Force_Station"}, {"title": "Delta II", "link": "https://wikipedia.org/wiki/Delta_II"}, {"title": "GPS IIR-1", "link": "https://wikipedia.org/wiki/GPS_IIR-1"}]}, {"year": "1998", "text": "<PERSON><PERSON><PERSON><PERSON><PERSON> scandal: <PERSON> breaks the story of the <PERSON><PERSON> affair on his Drudge Report website.", "html": "1998 - <a href=\"https://wikipedia.org/wiki/<PERSON>%E2%80%93L<PERSON><PERSON><PERSON>_scandal\" title=\"<PERSON><PERSON><PERSON> scandal\"><PERSON>-<PERSON> scandal</a>: <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> breaks the story of the <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>-<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> affair on his <i><a href=\"https://wikipedia.org/wiki/Drudge_Report\" title=\"Drudge Report\">Drudge Report</a></i> website.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>%E2%80%93L<PERSON><PERSON><PERSON>_scandal\" title=\"<PERSON><PERSON><PERSON> scandal\"><PERSON>-<PERSON> scandal</a>: <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>udge\" title=\"<PERSON>\"><PERSON></a> breaks the story of the <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>-<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> affair on his <i><a href=\"https://wikipedia.org/wiki/Drudge_Report\" title=\"Drudge Report\">Drudge Report</a></i> website.", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON><PERSON> scandal", "link": "https://wikipedia.org/wiki/Clinton%E2%80%93L<PERSON><PERSON><PERSON>_scandal"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Drudge Report", "link": "https://wikipedia.org/wiki/Drudge_Report"}]}, {"year": "2002", "text": "Mount Nyiragongo erupts in the Democratic Republic of the Congo, displacing an estimated 400,000 people.", "html": "2002 - <a href=\"https://wikipedia.org/wiki/Mount_Nyiragongo\" title=\"Mount Nyiragongo\">Mount Nyiragongo</a> erupts in the <a href=\"https://wikipedia.org/wiki/Democratic_Republic_of_the_Congo\" title=\"Democratic Republic of the Congo\">Democratic Republic of the Congo</a>, displacing an estimated 400,000 people.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Mount_Nyiragongo\" title=\"Mount Nyiragongo\">Mount Nyiragongo</a> erupts in the <a href=\"https://wikipedia.org/wiki/Democratic_Republic_of_the_Congo\" title=\"Democratic Republic of the Congo\">Democratic Republic of the Congo</a>, displacing an estimated 400,000 people.", "links": [{"title": "Mount Nyiragongo", "link": "https://wikipedia.org/wiki/Mount_Nyiragongo"}, {"title": "Democratic Republic of the Congo", "link": "https://wikipedia.org/wiki/Democratic_Republic_of_the_Congo"}]}, {"year": "2007", "text": "The Doomsday Clock is set to five minutes to midnight in response to North Korea's nuclear testing.", "html": "2007 - The <a href=\"https://wikipedia.org/wiki/Doomsday_Clock\" title=\"Doomsday Clock\">Doomsday Clock</a> is set to five minutes to midnight in response to <a href=\"https://wikipedia.org/wiki/North_Korea\" title=\"North Korea\">North Korea</a>'s nuclear testing.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Doomsday_Clock\" title=\"Doomsday Clock\">Doomsday Clock</a> is set to five minutes to midnight in response to <a href=\"https://wikipedia.org/wiki/North_Korea\" title=\"North Korea\">North Korea</a>'s nuclear testing.", "links": [{"title": "Doomsday Clock", "link": "https://wikipedia.org/wiki/Doomsday_Clock"}, {"title": "North Korea", "link": "https://wikipedia.org/wiki/North_Korea"}]}, {"year": "2008", "text": "British Airways Flight 38 crashes short of the runway at Heathrow Airport, injuring 47.", "html": "2008 - <a href=\"https://wikipedia.org/wiki/British_Airways_Flight_38\" title=\"British Airways Flight 38\">British Airways Flight 38</a> crashes short of the runway at <a href=\"https://wikipedia.org/wiki/Heathrow_Airport\" title=\"Heathrow Airport\">Heathrow Airport</a>, injuring 47.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/British_Airways_Flight_38\" title=\"British Airways Flight 38\">British Airways Flight 38</a> crashes short of the runway at <a href=\"https://wikipedia.org/wiki/Heathrow_Airport\" title=\"Heathrow Airport\">Heathrow Airport</a>, injuring 47.", "links": [{"title": "British Airways Flight 38", "link": "https://wikipedia.org/wiki/British_Airways_Flight_38"}, {"title": "Heathrow Airport", "link": "https://wikipedia.org/wiki/Heathrow_Airport"}]}, {"year": "2010", "text": "Rioting begins between Muslim and Christian groups in Jos, Nigeria, results in at least 200 deaths.", "html": "2010 - <a href=\"https://wikipedia.org/wiki/2010_Jo<PERSON>_riots\" title=\"2010 Jos riots\">Rioting</a> begins between Muslim and Christian groups in <a href=\"https://wikipedia.org/wiki/Jo<PERSON>\" title=\"Jo<PERSON>\">Jos, Nigeria</a>, results in at least 200 deaths.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/2010_Jo<PERSON>_riots\" title=\"2010 Jos riots\">Rioting</a> begins between Muslim and Christian groups in <a href=\"https://wikipedia.org/wiki/Jo<PERSON>\" title=\"Jo<PERSON>\">Jos, Nigeria</a>, results in at least 200 deaths.", "links": [{"title": "2010 Jos riots", "link": "https://wikipedia.org/wiki/2010_<PERSON><PERSON>_riots"}, {"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>s"}]}, {"year": "2013", "text": "Former cyclist <PERSON> confesses to his doping in an airing of Oprah's Next Chapter.", "html": "2013 - Former cyclist <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> confesses to <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_doping_case\" title=\"<PERSON> doping case\">his doping</a> in an airing of <i><a href=\"https://wikipedia.org/wiki/Oprah%27s_Next_Chapter\" class=\"mw-redirect\" title=\"Oprah's Next Chapter\">Oprah's Next Chapter</a></i>.", "no_year_html": "Former cyclist <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> confesses to <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_doping_case\" title=\"<PERSON> doping case\">his doping</a> in an airing of <i><a href=\"https://wikipedia.org/wiki/Oprah%27s_Next_Chapter\" class=\"mw-redirect\" title=\"Oprah's Next Chapter\">Oprah's Next Chapter</a></i>.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "<PERSON> doping case", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_doping_case"}, {"title": "Oprah's Next Chapter", "link": "https://wikipedia.org/wiki/Oprah%27s_Next_Chapter"}]}, {"year": "2013", "text": "<PERSON><PERSON> is murdered by members of Golden Dawn in Petralona, Athens, leading the creation of new measures to combat race-based attacks in Greece.", "html": "2013 - <PERSON><PERSON> is <a href=\"https://wikipedia.org/wiki/Murder_of_<PERSON><PERSON>\" title=\"Murder of <PERSON><PERSON>\">murdered</a> by members of <a href=\"https://wikipedia.org/wiki/Golden_Dawn_(Greece)\" title=\"Golden Dawn (Greece)\">Golden Dawn</a> in <a href=\"https://wikipedia.org/wiki/Petralona\" title=\"Petralona\">Petralona</a>, <a href=\"https://wikipedia.org/wiki/Athens\" title=\"Athens\">Athens</a>, leading the creation of new measures to combat race-based attacks in <a href=\"https://wikipedia.org/wiki/Greece\" title=\"Greece\">Greece</a>.", "no_year_html": "<PERSON><PERSON> is <a href=\"https://wikipedia.org/wiki/Murder_of_<PERSON><PERSON>\" title=\"Murder of <PERSON><PERSON>\">murdered</a> by members of <a href=\"https://wikipedia.org/wiki/Golden_Dawn_(Greece)\" title=\"Golden Dawn (Greece)\">Golden Dawn</a> in <a href=\"https://wikipedia.org/wiki/Petralona\" title=\"Petralona\">Petralona</a>, <a href=\"https://wikipedia.org/wiki/Athens\" title=\"Athens\">Athens</a>, leading the creation of new measures to combat race-based attacks in <a href=\"https://wikipedia.org/wiki/Greece\" title=\"Greece\">Greece</a>.", "links": [{"title": "Murder of <PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Murder_of_<PERSON><PERSON>_<PERSON>"}, {"title": "Golden Dawn (Greece)", "link": "https://wikipedia.org/wiki/Golden_Dawn_(Greece)"}, {"title": "Petralona", "link": "https://wikipedia.org/wiki/Petralona"}, {"title": "Athens", "link": "https://wikipedia.org/wiki/Athens"}, {"title": "Greece", "link": "https://wikipedia.org/wiki/Greece"}]}, {"year": "2016", "text": "President <PERSON> announces the Joint Comprehensive Plan of Action, an agreement intended to limit Iran's nuclear program. ", "html": "2016 - President <a href=\"https://wikipedia.org/wiki/Barack_Obama\" title=\"Barack Obama\"><PERSON></a> announces the <a href=\"https://wikipedia.org/wiki/Joint_Comprehensive_Plan_of_Action\" title=\"Joint Comprehensive Plan of Action\">Joint Comprehensive Plan of Action</a>, an agreement intended to limit Iran's nuclear program. ", "no_year_html": "President <a href=\"https://wikipedia.org/wiki/Barack_Obama\" title=\"Barack Obama\"><PERSON></a> announces the <a href=\"https://wikipedia.org/wiki/Joint_Comprehensive_Plan_of_Action\" title=\"Joint Comprehensive Plan of Action\">Joint Comprehensive Plan of Action</a>, an agreement intended to limit Iran's nuclear program. ", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Barack<PERSON>Obama"}, {"title": "Joint Comprehensive Plan of Action", "link": "https://wikipedia.org/wiki/Joint_Comprehensive_Plan_of_Action"}]}, {"year": "2017", "text": "The search for Malaysia Airlines Flight 370 is announced to be suspended.", "html": "2017 - <a href=\"https://wikipedia.org/wiki/Search_for_Malaysia_Airlines_Flight_370\" title=\"Search for Malaysia Airlines Flight 370\">The search for</a> <a href=\"https://wikipedia.org/wiki/Malaysia_Airlines_Flight_370\" title=\"Malaysia Airlines Flight 370\">Malaysia Airlines Flight 370</a> is announced to be suspended.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Search_for_Malaysia_Airlines_Flight_370\" title=\"Search for Malaysia Airlines Flight 370\">The search for</a> <a href=\"https://wikipedia.org/wiki/Malaysia_Airlines_Flight_370\" title=\"Malaysia Airlines Flight 370\">Malaysia Airlines Flight 370</a> is announced to be suspended.", "links": [{"title": "Search for Malaysia Airlines Flight 370", "link": "https://wikipedia.org/wiki/Search_for_Malaysia_Airlines_Flight_370"}, {"title": "Malaysia Airlines Flight 370", "link": "https://wikipedia.org/wiki/Malaysia_Airlines_Flight_370"}]}, {"year": "2023", "text": "An avalanche strikes Nyingchi, Tibet, killing 28 people.", "html": "2023 - <a href=\"https://wikipedia.org/wiki/2023_Nyingchi_avalanche\" title=\"2023 Nyingchi avalanche\">An avalanche strikes Nyingchi, Tibet</a>, killing 28 people.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/2023_Nyingchi_avalanche\" title=\"2023 Nyingchi avalanche\">An avalanche strikes Nyingchi, Tibet</a>, killing 28 people.", "links": [{"title": "2023 Nyingchi avalanche", "link": "https://wikipedia.org/wiki/2023_Nyingchi_avalanche"}]}], "Births": [{"year": "1342", "text": "<PERSON>, Duke of Burgundy (d. 1404)", "html": "1342 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Duke_of_Burgundy\" class=\"mw-redirect\" title=\"<PERSON>, Duke of Burgundy\"><PERSON>, Duke of Burgundy</a> (d. 1404)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Duke_of_Burgundy\" class=\"mw-redirect\" title=\"<PERSON>, Duke of Burgundy\"><PERSON>, Duke of Burgundy</a> (d. 1404)", "links": [{"title": "<PERSON>, Duke of Burgundy", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Duke_of_Burgundy"}]}, {"year": "1429", "text": "<PERSON>, Italian artist (d.c. 1498)", "html": "1429 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian artist (d.c. <a href=\"https://wikipedia.org/wiki/1498\" title=\"1498\">1498</a>)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian artist (d.c. <a href=\"https://wikipedia.org/wiki/1498\" title=\"1498\">1498</a>)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "1498", "link": "https://wikipedia.org/wiki/1498"}]}, {"year": "1463", "text": "<PERSON>, Elector of Saxony (d. 1525)", "html": "1463 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Elector_of_Saxony\" title=\"<PERSON>, Elector of Saxony\"><PERSON>, Elector of Saxony</a> (d. 1525)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Elector_of_Saxony\" title=\"<PERSON>, Elector of Saxony\"><PERSON>, Elector of Saxony</a> (d. 1525)", "links": [{"title": "<PERSON>, Elector of Saxony", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Elector_of_Saxony"}]}, {"year": "1463", "text": "<PERSON>, French cardinal (d. 1535)", "html": "1463 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French cardinal (d. 1535)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French cardinal (d. 1535)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1472", "text": "<PERSON><PERSON><PERSON>, Italian captain (d. 1508)", "html": "1472 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Italian captain (d. 1508)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>ro\"><PERSON><PERSON><PERSON></a>, Italian captain (d. 1508)", "links": [{"title": "<PERSON><PERSON><PERSON> Montefeltro", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1484", "text": "<PERSON>, German priest and reformer (d. 1545)", "html": "1484 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German priest and reformer (d. 1545)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German priest and reformer (d. 1545)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1501", "text": "<PERSON><PERSON>, German physician and botanist (d. 1566)", "html": "1501 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, German physician and botanist (d. 1566)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, German physician and botanist (d. 1566)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1504", "text": "<PERSON> (d. 1572)", "html": "1504 - <a href=\"https://wikipedia.org/wiki/Pope_<PERSON>_V\" title=\"Pope Pius V\">Pope <PERSON> V</a> (d. 1572)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Pope_<PERSON>_V\" title=\"Pope Pius V\">Pope <PERSON> V</a> (d. 1572)", "links": [{"title": "Pope <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1517", "text": "<PERSON>, 1st Duke of Suffolk, English Duke (d. 1554)", "html": "1517 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_1st_Duke_of_Suffolk\" class=\"mw-redirect\" title=\"<PERSON>, 1st Duke of Suffolk\"><PERSON>, 1st Duke of Suffolk</a>, English Duke (d. 1554)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_1st_Duke_of_Suffolk\" class=\"mw-redirect\" title=\"<PERSON>, 1st Duke of Suffolk\"><PERSON>, 1st Duke of Suffolk</a>, English Duke (d. 1554)", "links": [{"title": "<PERSON>, 1st Duke of Suffolk", "link": "https://wikipedia.org/wiki/<PERSON>,_1st_Duke_of_Suffolk"}]}, {"year": "1560", "text": "<PERSON><PERSON>, Swiss botanist, physician, and academic (d. 1624)", "html": "1560 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> Bauhin\"><PERSON><PERSON></a>, Swiss botanist, physician, and academic (d. 1624)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> Bauhin\"><PERSON><PERSON></a>, Swiss botanist, physician, and academic (d. 1624)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1574", "text": "<PERSON>, English physician, astrologer, and mathematician (d. 1637)", "html": "1574 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English physician, astrologer, and mathematician (d. 1637)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English physician, astrologer, and mathematician (d. 1637)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1593", "text": "<PERSON>, English alchemist and astrologer (d. 1662)", "html": "1593 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English alchemist and astrologer (d. 1662)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English alchemist and astrologer (d. 1662)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1600", "text": "<PERSON>, Spanish playwright and poet (d. 1681)", "html": "1600 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%B3n_de_la_Barca\" title=\"<PERSON> Barca\"><PERSON></a>, Spanish playwright and poet (d. 1681)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%B3n_de_la_Barca\" title=\"<PERSON> Barca\"><PERSON></a>, Spanish playwright and poet (d. 1681)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Pedro_Calder%C3%B3n_de_la_Barca"}]}, {"year": "1612", "text": "<PERSON>, English general and politician (d. 1671)", "html": "1612 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English general and politician (d. 1671)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English general and politician (d. 1671)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1640", "text": "<PERSON>, American settler (d. 1724)", "html": "1640 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American settler (d. 1724)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American settler (d. 1724)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1659", "text": "<PERSON>, Italian violinist and composer (d. 1745)", "html": "1659 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian violinist and composer (d. 1745)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian violinist and composer (d. 1745)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1666", "text": "<PERSON>, Italian anatomist and physician (d. 1723)", "html": "1666 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian anatomist and physician (d. 1723)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian anatomist and physician (d. 1723)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1686", "text": "<PERSON>, Scottish historian and author (d. 1766)", "html": "1686 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish historian and author (d. 1766)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish historian and author (d. 1766)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1693", "text": "<PERSON><PERSON>, Spanish colonial governor of Cartagena de Indias (Colombia, 1739 - 1742); of Spanish Florida (1749 - 1752); and of Yucatán (Mexico, 1754 - 1758) (d. 1761)", "html": "1693 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Spanish colonial governor of <a href=\"https://wikipedia.org/wiki/Cartagena_de_Indias\" class=\"mw-redirect\" title=\"Cartagena de Indias\">Cartagena de Indias</a> (Colombia, 1739 - 1742); of Spanish Florida (1749 - 1752); and of Yucatán (Mexico, 1754 - 1758) (d. 1761)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Spanish colonial governor of <a href=\"https://wikipedia.org/wiki/Cartagena_de_Indias\" class=\"mw-redirect\" title=\"Cartagena de Indias\">Cartagena de Indias</a> (Colombia, 1739 - 1742); of Spanish Florida (1749 - 1752); and of Yucatán (Mexico, 1754 - 1758) (d. 1761)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>"}, {"title": "Cartagena de Indias", "link": "https://wikipedia.org/wiki/Cartagena_de_Indias"}]}, {"year": "1706", "text": "<PERSON>, American publisher, inventor, and politician, 6th President of Pennsylvania (d. 1790)", "html": "1706 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American publisher, inventor, and politician, 6th <a href=\"https://wikipedia.org/wiki/List_of_Governors_of_Pennsylvania\" class=\"mw-redirect\" title=\"List of Governors of Pennsylvania\">President of Pennsylvania</a> (d. 1790)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American publisher, inventor, and politician, 6th <a href=\"https://wikipedia.org/wiki/List_of_Governors_of_Pennsylvania\" class=\"mw-redirect\" title=\"List of Governors of Pennsylvania\">President of Pennsylvania</a> (d. 1790)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "List of Governors of Pennsylvania", "link": "https://wikipedia.org/wiki/List_of_Governors_of_Pennsylvania"}]}, {"year": "1712", "text": "<PERSON>, English organist and composer (d. 1786)", "html": "1712 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>(composer)\" title=\"<PERSON> (composer)\"><PERSON></a>, English organist and composer (d. 1786)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>(composer)\" title=\"<PERSON> (composer)\"><PERSON></a>, English organist and composer (d. 1786)", "links": [{"title": "<PERSON> (composer)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>(composer)"}]}, {"year": "1719", "text": "<PERSON>, American businessman (d. 1806)", "html": "1719 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American businessman (d. 1806)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American businessman (d. 1806)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1728", "text": "<PERSON>, German pianist and composer (d. 1788)", "html": "1728 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>%C3%BCthel\" title=\"<PERSON>\"><PERSON></a>, German pianist and composer (d. 1788)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>%C3%BCthel\" title=\"<PERSON>\"><PERSON></a>, German pianist and composer (d. 1788)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>%C3%BCthel"}]}, {"year": "1732", "text": "<PERSON><PERSON>, Polish-Lithuanian king (d. 1798)", "html": "1732 - <a href=\"https://wikipedia.org/wiki/Stanis%C5%82aw_August_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Polish-Lithuanian king (d. 1798)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Stanis%C5%82aw_August_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Polish-Lithuanian king (d. 1798)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Stanis%C5%82aw_August_<PERSON><PERSON><PERSON><PERSON>"}]}, {"year": "1734", "text": "<PERSON><PERSON><PERSON>, French composer and conductor (d. 1829)", "html": "1734 - <a href=\"https://wikipedia.org/wiki/Fran%C3%A<PERSON><PERSON>-<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, French composer and conductor (d. 1829)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Fran%C3%A<PERSON><PERSON>-<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, French composer and conductor (d. 1829)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Fran%C3%A<PERSON><PERSON>-<PERSON>_<PERSON>"}]}, {"year": "1761", "text": "Sir <PERSON>, 4th Baronet, Scottish geologist and geophysicist (d. 1832)", "html": "1761 - <a href=\"https://wikipedia.org/wiki/Sir_<PERSON>_<PERSON>,_4th_Baronet\" title=\"Sir <PERSON>, 4th Baronet\">Sir <PERSON>, 4th Baronet</a>, Scottish geologist and geophysicist (d. 1832)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Sir_<PERSON>_<PERSON>,_4th_Baronet\" title=\"Sir <PERSON>, 4th Baronet\">Sir <PERSON>, 4th Baronet</a>, Scottish geologist and geophysicist (d. 1832)", "links": [{"title": "Sir <PERSON>, 4th Baronet", "link": "https://wikipedia.org/wiki/Sir_<PERSON>_<PERSON>,_4th_Baronet"}]}, {"year": "1789", "text": "<PERSON>, German historian and theologian (d. 1850)", "html": "1789 - <a href=\"https://wikipedia.org/wiki/August_Neander\" title=\"August Neander\">August <PERSON></a>, German historian and theologian (d. 1850)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/August_<PERSON><PERSON><PERSON>\" title=\"August Neander\">August <PERSON></a>, German historian and theologian (d. 1850)", "links": [{"title": "August <PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/August_<PERSON><PERSON><PERSON>"}]}, {"year": "1793", "text": "<PERSON>, Spanish-American priest, rancher and politician (d. 1867)", "html": "1793 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A9_Mart%C3%ADnez\" title=\"<PERSON>\"><PERSON></a>, Spanish-American priest, rancher and politician (d. 1867)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A9_Mart%C3%ADnez\" title=\"<PERSON>\"><PERSON></a>, Spanish-American priest, rancher and politician (d. 1867)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_Jo<PERSON>%C3%A9_Mart%C3%ADnez"}]}, {"year": "1814", "text": "<PERSON>, English author (d. 1887)", "html": "1814 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(author)\" title=\"<PERSON> (author)\"><PERSON></a>, English author (d. 1887)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(author)\" title=\"<PERSON> (author)\"><PERSON></a>, English author (d. 1887)", "links": [{"title": "<PERSON> (author)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(author)"}]}, {"year": "1820", "text": "<PERSON>, English author and poet (d. 1849)", "html": "1820 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%AB\" title=\"<PERSON>\"><PERSON></a>, English author and poet (d. 1849)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%AB\" title=\"<PERSON>\"><PERSON></a>, English author and poet (d. 1849)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Anne_Bront%C3%AB"}]}, {"year": "1828", "text": "<PERSON>, American lawyer and general, Medal of Honor recipient (d. 1918)", "html": "1828 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and general, <a href=\"https://wikipedia.org/wiki/Medal_of_Honor\" title=\"Medal of Honor\">Medal of Honor</a> recipient (d. 1918)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and general, <a href=\"https://wikipedia.org/wiki/Medal_of_Honor\" title=\"Medal of Honor\">Medal of Honor</a> recipient (d. 1918)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>"}, {"title": "Medal of Honor", "link": "https://wikipedia.org/wiki/Medal_of_Honor"}]}, {"year": "1828", "text": "<PERSON><PERSON>, Hungarian violinist and composer (d. 1898)", "html": "1828 - <a href=\"https://wikipedia.org/wiki/Ede_Rem%C3%A9nyi\" title=\"Ede Reményi\"><PERSON><PERSON></a>, Hungarian violinist and composer (d. 1898)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Ede_Rem%C3%A9nyi\" title=\"Ede Reményi\"><PERSON><PERSON></a>, Hungarian violinist and composer (d. 1898)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Ede_Rem%C3%A9nyi"}]}, {"year": "1832", "text": "<PERSON>, American historian and academic (d. 1906)", "html": "1832 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American historian and academic (d. 1906)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American historian and academic (d. 1906)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1834", "text": "<PERSON>, German biologist, zoologist, and geneticist (d. 1914)", "html": "1834 - <a href=\"https://wikipedia.org/wiki/August_<PERSON>\" title=\"August <PERSON>\">August <PERSON></a>, German biologist, zoologist, and geneticist (d. 1914)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/August_<PERSON>\" title=\"August <PERSON>\">August <PERSON></a>, German biologist, zoologist, and geneticist (d. 1914)", "links": [{"title": "August <PERSON><PERSON>", "link": "https://wikipedia.org/wiki/August_<PERSON><PERSON>"}]}, {"year": "1850", "text": "<PERSON><PERSON><PERSON><PERSON>, Brazilian cardinal (d. 1930)", "html": "1850 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>over<PERSON>_<PERSON>_Albuquerque_Cavalcanti\" title=\"<PERSON><PERSON><PERSON><PERSON>over<PERSON> de <PERSON> Cavalcanti\"><PERSON><PERSON><PERSON><PERSON> C<PERSON></a>, Brazilian cardinal (d. 1930)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>_<PERSON>_Albuquerque_Cavalcanti\" title=\"<PERSON><PERSON><PERSON><PERSON> Arcoverde de Albuquerque Cavalcanti\"><PERSON><PERSON><PERSON><PERSON> Caval<PERSON></a>, Brazilian cardinal (d. 1930)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>l<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON><PERSON>_<PERSON>_<PERSON>_C<PERSON>l<PERSON>ti"}]}, {"year": "1850", "text": "<PERSON>, Russian pianist and composer (d. 1918)", "html": "1850 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian pianist and composer (d. 1918)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian pianist and composer (d. 1918)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1851", "text": "<PERSON><PERSON> <PERSON><PERSON>, American author and illustrator (d. 1928)", "html": "1851 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> <PERSON><PERSON> Frost\"><PERSON><PERSON> <PERSON><PERSON></a>, American author and illustrator (d. 1928)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> <PERSON><PERSON> Frost\"><PERSON><PERSON> <PERSON><PERSON></a>, American author and illustrator (d. 1928)", "links": [{"title": "A<PERSON> B<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "1853", "text": "<PERSON><PERSON>, American suffragist (d. 1933)", "html": "1853 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_Belmont\" title=\"Alva Belmont\"><PERSON><PERSON></a>, American suffragist (d. 1933)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>va_Belmont\" title=\"Alva Belmont\"><PERSON><PERSON></a>, American suffragist (d. 1933)", "links": [{"title": "Alva <PERSON>", "link": "https://wikipedia.org/wiki/Alva_Belmont"}]}, {"year": "1853", "text": "<PERSON><PERSON> <PERSON>, American painter and academic (d. 1930)", "html": "1853 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON> <PERSON>\"><PERSON><PERSON> <PERSON></a>, American painter and academic (d. 1930)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON> <PERSON>\"><PERSON><PERSON> <PERSON></a>, American painter and academic (d. 1930)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1857", "text": "<PERSON>, Austrian pianist, composer, and conductor (d. 1941)", "html": "1857 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Austrian pianist, composer, and conductor (d. 1941)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Austrian pianist, composer, and conductor (d. 1941)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1857", "text": "<PERSON>, French-American engineer (d. 1935)", "html": "1857 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French-American engineer (d. 1935)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_August<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French-American engineer (d. 1935)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "1858", "text": "<PERSON><PERSON>, Colombian author (d. 1940)", "html": "1858 - <a href=\"https://wikipedia.org/wiki/Tom%C3%A1s_Carrasquilla\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Colombian author (d. 1940)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Tom%C3%A1s_Carrasquilla\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Colombian author (d. 1940)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Tom%C3%A1s_Carrasquilla"}]}, {"year": "1860", "text": "<PERSON>, Irish academic and politician, 1st President of Ireland (d. 1949)", "html": "1860 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Irish academic and politician, 1st <a href=\"https://wikipedia.org/wiki/President_of_Ireland\" title=\"President of Ireland\">President of Ireland</a> (d. 1949)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Irish academic and politician, 1st <a href=\"https://wikipedia.org/wiki/President_of_Ireland\" title=\"President of Ireland\">President of Ireland</a> (d. 1949)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "President of Ireland", "link": "https://wikipedia.org/wiki/President_of_Ireland"}]}, {"year": "1863", "text": "<PERSON>, Welsh lawyer and politician, Prime Minister of the United Kingdom (d. 1945)", "html": "1863 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Welsh lawyer and politician, <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_the_United_Kingdom\" title=\"Prime Minister of the United Kingdom\">Prime Minister of the United Kingdom</a> (d. 1945)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Welsh lawyer and politician, <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_the_United_Kingdom\" title=\"Prime Minister of the United Kingdom\">Prime Minister of the United Kingdom</a> (d. 1945)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "Prime Minister of the United Kingdom", "link": "https://wikipedia.org/wiki/Prime_Minister_of_the_United_Kingdom"}]}, {"year": "1863", "text": "<PERSON>, Russian actor and director (d. 1938)", "html": "1863 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian actor and director (d. 1938)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian actor and director (d. 1938)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1865", "text": "Sir <PERSON>, 7th Baronet, English general and politician, 3rd Governor-General of New Zealand (d. 1951)", "html": "1865 - <a href=\"https://wikipedia.org/wiki/Sir_<PERSON>,_7th_Baronet\" title=\"Sir <PERSON>, 7th Baronet\">Sir <PERSON>, 7th Baronet</a>, English general and politician, 3rd <a href=\"https://wikipedia.org/wiki/Governor-General_of_New_Zealand\" title=\"Governor-General of New Zealand\">Governor-General of New Zealand</a> (d. 1951)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Sir_<PERSON>,_7th_Baronet\" title=\"Sir <PERSON>, 7th Baronet\">Sir <PERSON>, 7th Baronet</a>, English general and politician, 3rd <a href=\"https://wikipedia.org/wiki/Governor-General_of_New_Zealand\" title=\"Governor-General of New Zealand\">Governor-General of New Zealand</a> (d. 1951)", "links": [{"title": "Sir <PERSON>, 7th Baronet", "link": "https://wikipedia.org/wiki/Sir_<PERSON>,_7th_Baronet"}, {"title": "Governor-General of New Zealand", "link": "https://wikipedia.org/wiki/Governor-General_of_New_Zealand"}]}, {"year": "1867", "text": "<PERSON>, German-born American film producer, co-founded Universal Studios (d. 1939)", "html": "1867 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German-born American film producer, co-founded <a href=\"https://wikipedia.org/wiki/Universal_Pictures\" title=\"Universal Pictures\">Universal Studios</a> (d. 1939)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German-born American film producer, co-founded <a href=\"https://wikipedia.org/wiki/Universal_Pictures\" title=\"Universal Pictures\">Universal Studios</a> (d. 1939)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Universal Pictures", "link": "https://wikipedia.org/wiki/Universal_Pictures"}]}, {"year": "1867", "text": "Sir <PERSON>, 3rd Baronet, English colonel, pilot, and polo player (d. 1934)", "html": "1867 - <a href=\"https://wikipedia.org/wiki/Sir_<PERSON>,_3rd_Baronet\" title=\"Sir <PERSON>, 3rd Baronet\">Sir <PERSON>, 3rd Baronet</a>, English colonel, pilot, and polo player (d. 1934)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Sir_<PERSON>_<PERSON>,_3rd_Baronet\" title=\"Sir <PERSON>, 3rd Baronet\">Sir <PERSON>, 3rd Baronet</a>, English colonel, pilot, and polo player (d. 1934)", "links": [{"title": "Sir <PERSON>, 3rd Baronet", "link": "https://wikipedia.org/wiki/Sir_<PERSON>,_3rd_Baronet"}]}, {"year": "1871", "text": "<PERSON>, 1st <PERSON>, English admiral (d. 1936)", "html": "1871 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_1st_Earl_<PERSON>\" title=\"<PERSON>, 1st <PERSON>\"><PERSON>, 1st <PERSON></a>, English admiral (d. 1936)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>,_1st_<PERSON>_<PERSON>\" title=\"<PERSON>, 1st <PERSON>\"><PERSON>, 1st <PERSON></a>, English admiral (d. 1936)", "links": [{"title": "<PERSON>, 1st <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>,_1st_<PERSON>_<PERSON>"}]}, {"year": "1871", "text": "<PERSON><PERSON>, Romanian historian and politician, 34th Prime Minister of Romania (d. 1940)", "html": "1871 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Romanian historian and politician, 34th <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Romania\" title=\"Prime Minister of Romania\">Prime Minister of Romania</a> (d. 1940)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Romanian historian and politician, 34th <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Romania\" title=\"Prime Minister of Romania\">Prime Minister of Romania</a> (d. 1940)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}, {"title": "Prime Minister of Romania", "link": "https://wikipedia.org/wiki/Prime_Minister_of_Romania"}]}, {"year": "1875", "text": "<PERSON><PERSON><PERSON><PERSON>, Uruguayan journalist and playwright (d. 1910)", "html": "1875 - <a href=\"https://wikipedia.org/wiki/Florencio_S%C3%A1nchez\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Uruguayan journalist and playwright (d. 1910)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Florencio_S%C3%A1nchez\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Uruguayan journalist and playwright (d. 1910)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Florencio_S%C3%A1nchez"}]}, {"year": "1876", "text": "<PERSON>, American lawyer and politician, 30th Mayor of Jersey City (d. 1956)", "html": "1876 - <a href=\"https://wikipedia.org/wiki/Frank_Hague\" title=\"Frank Hague\"><PERSON></a>, American lawyer and politician, 30th <a href=\"https://wikipedia.org/wiki/Mayor_of_Jersey_City\" class=\"mw-redirect\" title=\"Mayor of Jersey City\">Mayor of Jersey City</a> (d. 1956)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Frank_Hague\" title=\"Frank Hague\"><PERSON></a>, American lawyer and politician, 30th <a href=\"https://wikipedia.org/wiki/Mayor_of_Jersey_City\" class=\"mw-redirect\" title=\"Mayor of Jersey City\">Mayor of Jersey City</a> (d. 1956)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Frank_<PERSON>"}, {"title": "Mayor of Jersey City", "link": "https://wikipedia.org/wiki/Mayor_of_Jersey_City"}]}, {"year": "1877", "text": "<PERSON><PERSON><PERSON><PERSON><PERSON>, Czech botanist and zoologist (d. 1937)", "html": "1877 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C5%88ka_Baborov%C3%A1-%C4%8Cih%C3%A1kov%C3%A1\" title=\"<PERSON> Baborová<PERSON><PERSON><PERSON><PERSON>\"><PERSON>-<PERSON><PERSON><PERSON></a>, Czech botanist and zoologist (d. 1937)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C5%88ka_Baborov%C3%A1-%C4%8Cih%C3%A1kov%C3%A1\" title=\"<PERSON>ň<PERSON> Baborová-<PERSON><PERSON><PERSON>\"><PERSON>-<PERSON><PERSON><PERSON></a>, Czech botanist and zoologist (d. 1937)", "links": [{"title": "<PERSON>-<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Marie_Zde%C5%88ka_Baborov%C3%A1-%C4%8Cih%C3%A1kov%C3%A1"}]}, {"year": "1877", "text": "<PERSON>, English-Australian author and illustrator (d. 1969)", "html": "1877 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English-Australian author and illustrator (d. 1969)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English-Australian author and illustrator (d. 1969)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/May_<PERSON>"}]}, {"year": "1880", "text": "<PERSON>, Canadian-American actor, director, and producer (d. 1960)", "html": "1880 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian-American actor, director, and producer (d. 1960)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian-American actor, director, and producer (d. 1960)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1881", "text": "<PERSON><PERSON>, Polish mathematician and academic (d. 1941)", "html": "1881 - <a href=\"https://wikipedia.org/wiki/Anton<PERSON>_%C5%81omnicki\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Polish mathematician and academic (d. 1941)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_%C5%81omnicki\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Polish mathematician and academic (d. 1941)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Antoni_%C5%81omnicki"}]}, {"year": "1881", "text": "<PERSON>, English psychologist and author (d. 1948)", "html": "1881 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English psychologist and author (d. 1948)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English psychologist and author (d. 1948)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1882", "text": "<PERSON>, Sr., American actor (d. 1946)", "html": "1882 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Sr.\" class=\"mw-redirect\" title=\"<PERSON>, Sr.\"><PERSON>, Sr.</a>, American actor (d. 1946)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Sr.\" class=\"mw-redirect\" title=\"<PERSON>, Sr.\"><PERSON>, Sr.</a>, American actor (d. 1946)", "links": [{"title": "<PERSON>, Sr.", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_<PERSON>."}]}, {"year": "1883", "text": "<PERSON>, English-Scottish author, poet, and playwright (d. 1972)", "html": "1883 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English-Scottish author, poet, and playwright (d. 1972)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English-Scottish author, poet, and playwright (d. 1972)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1886", "text": "<PERSON>, American pilot and businessman, founded the Glenn L. Martin Company (d. 1955)", "html": "1886 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American pilot and businessman, founded the <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_Martin_Company\" title=\"Glenn L. Martin Company\">Glenn L. Martin Company</a> (d. 1955)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American pilot and businessman, founded the <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_Company\" title=\"Glenn L. Martin Company\">Glenn L. Martin Company</a> (d. 1955)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "Glenn L. Martin Company", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_Martin_Company"}]}, {"year": "1887", "text": "<PERSON><PERSON>, Norwegian psychoanalyst and philologist (d. 1975)", "html": "1887 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Norwegian psychoanalyst and philologist (d. 1975)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Norwegian psychoanalyst and philologist (d. 1975)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1888", "text": "<PERSON>, Indian philosopher and author (d. 1963)", "html": "1888 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Indian philosopher and author (d. 1963)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Indian philosopher and author (d. 1963)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1897", "text": "<PERSON>, French physician and serial killer (d. 1946)", "html": "1897 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French physician and serial killer (d. 1946)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French physician and serial killer (d. 1946)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1898", "text": "<PERSON><PERSON>, Serbian librarian (d. 1972)", "html": "1898 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Serbian librarian (d. 1972)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Serbian librarian (d. 1972)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1899", "text": "<PERSON>, American mob boss (d. 1947)", "html": "1899 - <a href=\"https://wikipedia.org/wiki/Al_<PERSON>one\" title=\"Al <PERSON>\"><PERSON></a>, American mob boss (d. 1947)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Al_Capone\" title=\"Al Cap<PERSON>\"><PERSON></a>, American mob boss (d. 1947)", "links": [{"title": "Al Capone", "link": "https://wikipedia.org/wiki/Al_Capone"}]}, {"year": "1899", "text": "<PERSON>, American philosopher and academic (d. 1977)", "html": "1899 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American philosopher and academic (d. 1977)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American philosopher and academic (d. 1977)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1899", "text": "<PERSON><PERSON><PERSON>, English engineer and author (d. 1960)", "html": "1899 - <a href=\"https://wikipedia.org/wiki/Nevil_<PERSON>e\" title=\"Nevil Shute\"><PERSON><PERSON><PERSON></a>, English engineer and author (d. 1960)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Nevil_<PERSON>e\" title=\"Nevil Shute\"><PERSON><PERSON><PERSON></a>, English engineer and author (d. 1960)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Nevil_<PERSON>e"}]}, {"year": "1901", "text": "<PERSON><PERSON>, Lithuanian-American philosopher and author (d. 1973)", "html": "1901 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Lithuanian-American philosopher and author (d. 1973)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Lithuanian-American philosopher and author (d. 1973)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1904", "text": "<PERSON><PERSON>, Thai painter and illustrator (d. 1969)", "html": "1904 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Thai painter and illustrator (d. 1969)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Thai painter and illustrator (d. 1969)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1905", "text": "<PERSON>, American baseball player (d. 2005)", "html": "1905 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player (d. 2005)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player (d. 2005)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1905", "text": "<PERSON>, American saxophonist and bandleader (d. 2007)", "html": "1905 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American saxophonist and bandleader (d. 2007)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American saxophonist and bandleader (d. 2007)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1905", "text": "<PERSON>, Estonian composer, conductor, educator, and critic (d. 1950)", "html": "1905 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Estonian composer, conductor, educator, and critic (d. 1950)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Estonian composer, conductor, educator, and critic (d. 1950)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1905", "text": "<PERSON>, Argentinian footballer and manager (d. 1966)", "html": "1905 - <a href=\"https://wikipedia.org/wiki/Guillermo_<PERSON>%C3%A1bile\" title=\"<PERSON>\"><PERSON></a>, Argentinian footballer and manager (d. 1966)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Guillermo_<PERSON>%C3%A1bile\" title=\"<PERSON>\"><PERSON></a>, Argentinian footballer and manager (d. 1966)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Guillermo_St%C3%A1bile"}]}, {"year": "1905", "text": "<PERSON>, Czech poet and translator (d. 1960)", "html": "1905 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%AD%C4%8Dek\" title=\"<PERSON>\"><PERSON></a>, Czech poet and translator (d. 1960)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%AD%C4%8Dek\" title=\"<PERSON>\"><PERSON></a>, Czech poet and translator (d. 1960)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%AD%C4%8Dek"}]}, {"year": "1907", "text": "<PERSON><PERSON>, Indonesian-Dutch composer and engineer (d. 1987)", "html": "1907 - <a href=\"https://wikipedia.org/wiki/He<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Indonesian-Dutch composer and engineer (d. 1987)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Indonesian-Dutch composer and engineer (d. 1987)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/He<PERSON>_<PERSON>"}]}, {"year": "1907", "text": "<PERSON>, British fellwalker, guidebook author and illustrator (d. 1991)", "html": "1907 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, British fellwalker, guidebook author and illustrator (d. 1991)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, British fellwalker, guidebook author and illustrator (d. 1991)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1908", "text": "<PERSON><PERSON>, American boxing manager and trainer (d. 1985)", "html": "1908 - <a href=\"https://wikipedia.org/wiki/Cus_D%27Amato\" title=\"Cus D'Amato\"><PERSON><PERSON></a>, American boxing manager and trainer (d. 1985)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Cus_D%27Amato\" title=\"Cus D'Amato\"><PERSON><PERSON></a>, American boxing manager and trainer (d. 1985)", "links": [{"title": "<PERSON>us D'Amato", "link": "https://wikipedia.org/wiki/Cus_D%27Amato"}]}, {"year": "1911", "text": "<PERSON><PERSON>, Canadian ice hockey player (d. 1966)", "html": "1911 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Canadian ice hockey player (d. 1966)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Canadian ice hockey player (d. 1966)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1911", "text": "<PERSON>, American admiral (d. 1981)", "html": "1911 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>_<PERSON>.\" title=\"<PERSON> Jr.\"><PERSON> Jr.</a>, American admiral (d. 1981)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>_<PERSON>.\" title=\"<PERSON> Jr.\"><PERSON> Jr.</a>, American admiral (d. 1981)", "links": [{"title": "<PERSON> Jr.", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>_<PERSON>."}]}, {"year": "1911", "text": "<PERSON>, American economist and academic, Nobel Prize laureate (d. 1991)", "html": "1911 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American economist and academic, <a href=\"https://wikipedia.org/wiki/Nobel_Memorial_Prize_in_Economic_Sciences\" title=\"Nobel Memorial Prize in Economic Sciences\">Nobel Prize</a> laureate (d. 1991)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American economist and academic, <a href=\"https://wikipedia.org/wiki/Nobel_Memorial_Prize_in_Economic_Sciences\" title=\"Nobel Memorial Prize in Economic Sciences\">Nobel Prize</a> laureate (d. 1991)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Nobel Memorial Prize in Economic Sciences", "link": "https://wikipedia.org/wiki/Nobel_Memorial_Prize_in_Economic_Sciences"}]}, {"year": "1914", "text": "<PERSON><PERSON><PERSON>, Italian-Chilean businessman (d. 2007)", "html": "1914 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Italian-Chilean businessman (d. 2007)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Italian-Chilean businessman (d. 2007)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Anacleto_Angelini"}]}, {"year": "1914", "text": "<PERSON>, American director, producer, and screenwriter (d. 2008)", "html": "1914 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American director, producer, and screenwriter (d. 2008)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American director, producer, and screenwriter (d. 2008)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1914", "text": "<PERSON><PERSON>, English actor (d. 1969)", "html": "1914 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actor (d. 1969)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actor (d. 1969)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1914", "text": "<PERSON>, Australian lieutenant and pilot (d. 2015)", "html": "1914 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian lieutenant and pilot (d. 2015)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian lieutenant and pilot (d. 2015)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1914", "text": "<PERSON>, American poet and author (d. 1993)", "html": "1914 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(poet)\" title=\"<PERSON> (poet)\"><PERSON></a>, American poet and author (d. 1993)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>(poet)\" title=\"<PERSON> (poet)\"><PERSON></a>, American poet and author (d. 1993)", "links": [{"title": "<PERSON> (poet)", "link": "https://wikipedia.org/wiki/<PERSON>_(poet)"}]}, {"year": "1916", "text": "<PERSON>., American lieutenant and politician (d. 2011)", "html": "1916 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>.\" title=\"<PERSON>.\"><PERSON> Jr.</a>, American lieutenant and politician (d. 2011)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>.\" title=\"<PERSON> Jr.\"><PERSON> Jr.</a>, American lieutenant and politician (d. 2011)", "links": [{"title": "<PERSON> Jr.", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>."}]}, {"year": "1917", "text": "<PERSON><PERSON> <PERSON><PERSON>, Indian actor, director, and politician, 3rd Chief Minister of Tamil Nadu (d. 1987)", "html": "1917 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> <PERSON><PERSON>\"><PERSON><PERSON> <PERSON><PERSON></a>, Indian actor, director, and politician, 3rd <a href=\"https://wikipedia.org/wiki/Chief_Minister_of_Tamil_Nadu\" class=\"mw-redirect\" title=\"Chief Minister of Tamil Nadu\">Chief Minister of Tamil Nadu</a> (d. 1987)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> <PERSON><PERSON>\"><PERSON><PERSON> <PERSON><PERSON></a>, Indian actor, director, and politician, 3rd <a href=\"https://wikipedia.org/wiki/Chief_Minister_of_Tamil_Nadu\" class=\"mw-redirect\" title=\"Chief Minister of Tamil Nadu\">Chief Minister of Tamil Nadu</a> (d. 1987)", "links": [{"title": "<PERSON><PERSON> <PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>"}, {"title": "Chief Minister of Tamil Nadu", "link": "https://wikipedia.org/wiki/Chief_Minister_of_Tamil_Nadu"}]}, {"year": "1918", "text": "<PERSON>, English lawyer and politician, Secretary of State for Education (d. 1994)", "html": "1918 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English lawyer and politician, <a href=\"https://wikipedia.org/wiki/Secretary_of_State_for_Education\" title=\"Secretary of State for Education\">Secretary of State for Education</a> (d. 1994)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English lawyer and politician, <a href=\"https://wikipedia.org/wiki/Secretary_of_State_for_Education\" title=\"Secretary of State for Education\">Secretary of State for Education</a> (d. 1994)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Secretary of State for Education", "link": "https://wikipedia.org/wiki/Secretary_of_State_for_Education"}]}, {"year": "1918", "text": "<PERSON>, American soldier and politician, 36th Governor of Pennsylvania (d. 2013)", "html": "1918 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_Leader\" title=\"<PERSON>\"><PERSON></a>, American soldier and politician, 36th <a href=\"https://wikipedia.org/wiki/Governor_of_Pennsylvania\" class=\"mw-redirect\" title=\"Governor of Pennsylvania\">Governor of Pennsylvania</a> (d. 2013)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_Leader\" title=\"<PERSON>\"><PERSON></a>, American soldier and politician, 36th <a href=\"https://wikipedia.org/wiki/Governor_of_Pennsylvania\" class=\"mw-redirect\" title=\"Governor of Pennsylvania\">Governor of Pennsylvania</a> (d. 2013)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_Leader"}, {"title": "Governor of Pennsylvania", "link": "https://wikipedia.org/wiki/Governor_of_Pennsylvania"}]}, {"year": "1920", "text": "<PERSON>, French author and illustrator (d. 2003)", "html": "1920 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French author and illustrator (d. 2003)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French author and illustrator (d. 2003)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1921", "text": "<PERSON>, Scottish footballer (d. 2005)", "html": "1921 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish footballer (d. 2005)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish footballer (d. 2005)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1921", "text": "<PERSON><PERSON><PERSON>, Pakistani general and politician (d. 2018)", "html": "1921 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Pakistani general and politician (d. 2018)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Pakistani general and politician (d. 2018)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1921", "text": "<PERSON>, English footballer and manager (d. 2002)", "html": "1921 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English footballer and manager (d. 2002)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English footballer and manager (d. 2002)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1921", "text": "<PERSON>, Cuban cartoonist (d. 1998)", "html": "1921 - <a href=\"https://wikipedia.org/wiki/Antonio_<PERSON>%C3%ADas\" title=\"<PERSON>\"><PERSON></a>, Cuban cartoonist (d. 1998)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%ADas\" title=\"<PERSON>\"><PERSON></a>, Cuban cartoonist (d. 1998)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Antonio_Proh%C3%ADas"}]}, {"year": "1922", "text": "<PERSON>, Mexican academic and politician, 50th President of Mexico (d. 2022)", "html": "1922 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%ADa\" title=\"<PERSON>\"><PERSON></a>, Mexican academic and politician, 50th <a href=\"https://wikipedia.org/wiki/President_of_Mexico\" title=\"President of Mexico\">President of Mexico</a> (d. 2022)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%ADa\" title=\"<PERSON>\"><PERSON></a>, Mexican academic and politician, 50th <a href=\"https://wikipedia.org/wiki/President_of_Mexico\" title=\"President of Mexico\">President of Mexico</a> (d. 2022)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>cheverr%C3%ADa"}, {"title": "President of Mexico", "link": "https://wikipedia.org/wiki/President_of_Mexico"}]}, {"year": "1922", "text": "<PERSON>, American soldier, lawyer, and politician, 65th United States Attorney General (d. 2012)", "html": "1922 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American soldier, lawyer, and politician, 65th <a href=\"https://wikipedia.org/wiki/United_States_Attorney_General\" title=\"United States Attorney General\">United States Attorney General</a> (d. 2012)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American soldier, lawyer, and politician, 65th <a href=\"https://wikipedia.org/wiki/United_States_Attorney_General\" title=\"United States Attorney General\">United States Attorney General</a> (d. 2012)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "United States Attorney General", "link": "https://wikipedia.org/wiki/United_States_Attorney_General"}]}, {"year": "1922", "text": "<PERSON>, American actress, game show panelist, television personality, and animal rights activist (d. 2021)", "html": "1922 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress, game show panelist, television personality, and animal rights activist (d. 2021)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_White\" title=\"<PERSON> White\"><PERSON></a>, American actress, game show panelist, television personality, and animal rights activist (d. 2021)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1923", "text": "<PERSON><PERSON>, Indian author and playwright (d. 1962)", "html": "1923 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_Raghav\" title=\"<PERSON>ya Raghav\"><PERSON><PERSON></a>, Indian author and playwright (d. 1962)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_Raghav\" title=\"<PERSON>ya Raghav\"><PERSON><PERSON></a>, Indian author and playwright (d. 1962)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>"}]}, {"year": "1924", "text": "<PERSON><PERSON>, Belgian footballer and journalist (d. 2013)", "html": "1924 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Belgian footballer and journalist (d. 2013)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Belgian footballer and journalist (d. 2013)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/R<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1924", "text": "<PERSON>, American biologist, cancer researcher, and academic (d. 2017)", "html": "1924 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>lum<PERSON>_<PERSON>\" title=\"<PERSON> Plummer <PERSON>\"><PERSON></a>, American biologist, cancer researcher, and academic (d. 2017)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON> Plummer Cobb\"><PERSON></a>, American biologist, cancer researcher, and academic (d. 2017)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>lum<PERSON>_<PERSON>"}]}, {"year": "1925", "text": "<PERSON><PERSON>, Latvian-American architect (d. 2017)", "html": "1925 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Latvian-American architect (d. 2017)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Latvian-American architect (d. 2017)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1925", "text": "<PERSON>, American author and journalist (d. 2000)", "html": "1925 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author and journalist (d. 2000)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author and journalist (d. 2000)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1925", "text": "<PERSON>, Pakistani cricketer and author (d. 1996)", "html": "1925 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Pakistani cricketer and author (d. 1996)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Pakistani cricketer and author (d. 1996)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "1926", "text": "<PERSON>, American lawyer and politician (d. 2023)", "html": "1926 - <a href=\"https://wikipedia.org/wiki/Newton_N._<PERSON>ow\" title=\"Newton N. Minow\"><PERSON> <PERSON></a>, American lawyer and politician (d. 2023)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Newton_N._<PERSON>ow\" title=\"Newton N. Minow\"><PERSON></a>, American lawyer and politician (d. 2023)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_N<PERSON>_<PERSON>ow"}]}, {"year": "1926", "text": "<PERSON><PERSON>, Scottish-English ballerina and actress (d. 2006)", "html": "1926 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Scottish-English ballerina and actress (d. 2006)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Scottish-English ballerina and actress (d. 2006)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1926", "text": "<PERSON>, Barbadian cricketer (d. 2006)", "html": "1926 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Barbadian cricketer (d. 2006)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Barbadian cricketer (d. 2006)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1927", "text": "<PERSON>, American physician and humanitarian (d. 1961)", "html": "1927 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American physician and humanitarian (d. 1961)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American physician and humanitarian (d. 1961)", "links": [{"title": "<PERSON> III", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1927", "text": "<PERSON><PERSON>, American actress and singer (d. 2008)", "html": "1927 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American actress and singer (d. 2008)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American actress and singer (d. 2008)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1927", "text": "<PERSON>, American lawyer and politician (d. 2014)", "html": "1927 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and politician (d. 2014)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and politician (d. 2014)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1927", "text": "<PERSON><PERSON> <PERSON><PERSON>, American director and producer (d. 1994)", "html": "1927 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_Swackhamer\" title=\"<PERSON><PERSON> <PERSON><PERSON>wackhamer\"><PERSON><PERSON> <PERSON><PERSON></a>, American director and producer (d. 1994)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_Swackhamer\" title=\"<PERSON><PERSON> <PERSON><PERSON> Swackhamer\"><PERSON><PERSON> <PERSON><PERSON></a>, American director and producer (d. 1994)", "links": [{"title": "<PERSON><PERSON> <PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON><PERSON><PERSON><PERSON>r"}]}, {"year": "1928", "text": "<PERSON>, French composer (d. 1973)", "html": "1928 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A9\" title=\"<PERSON>\"><PERSON></a>, French composer (d. 1973)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A9\" title=\"<PERSON>\"><PERSON></a>, French composer (d. 1973)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A9"}]}, {"year": "1928", "text": "<PERSON><PERSON>, English-American hairdresser and businessman (d. 2012)", "html": "1928 - <a href=\"https://wikipedia.org/wiki/Vidal_Sassoon\" title=\"Vidal Sassoon\"><PERSON><PERSON></a>, English-American hairdresser and businessman (d. 2012)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Vidal_Sassoon\" title=\"Vidal Sassoon\"><PERSON><PERSON> Sasso<PERSON></a>, English-American hairdresser and businessman (d. 2012)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Vidal_Sassoon"}]}, {"year": "1929", "text": "<PERSON>, British actor (d. 2020)", "html": "1929 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, British actor (d. 2020)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, British actor (d. 2020)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1929", "text": "<PERSON>, Canadian-Swiss ice hockey player, coach, and sportscaster (d. 1986)", "html": "1929 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian-Swiss ice hockey player, coach, and sportscaster (d. 1986)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian-Swiss ice hockey player, coach, and sportscaster (d. 1986)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1929", "text": "<PERSON>, Malaysian-Singaporean lawyer and politician, Attorney-General of Singapore (d. 2012)", "html": "1929 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Malaysian-Singaporean lawyer and politician, <a href=\"https://wikipedia.org/wiki/Attorney-General_of_Singapore\" title=\"Attorney-General of Singapore\">Attorney-General of Singapore</a> (d. 2012)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Malaysian-Singaporean lawyer and politician, <a href=\"https://wikipedia.org/wiki/Attorney-General_of_Singapore\" title=\"Attorney-General of Singapore\">Attorney-General of Singapore</a> (d. 2012)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>ik"}, {"title": "Attorney-General of Singapore", "link": "https://wikipedia.org/wiki/Attorney-General_of_Singapore"}]}, {"year": "1931", "text": "<PERSON>, American actor (d. 2024)", "html": "1931 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor (d. 2024)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor (d. 2024)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1931", "text": "<PERSON>, American sergeant and politician, 66th Governor of Virginia", "html": "1931 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American sergeant and politician, 66th <a href=\"https://wikipedia.org/wiki/Governor_of_Virginia\" title=\"Governor of Virginia\">Governor of Virginia</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American sergeant and politician, 66th <a href=\"https://wikipedia.org/wiki/Governor_of_Virginia\" title=\"Governor of Virginia\">Governor of Virginia</a>", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Governor of Virginia", "link": "https://wikipedia.org/wiki/Governor_of_Virginia"}]}, {"year": "1931", "text": "<PERSON>, American baseball player, coach, and manager (d. 2014)", "html": "1931 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player, coach, and manager (d. 2014)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player, coach, and manager (d. 2014)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1932", "text": "<PERSON>, English actor (d. 2009)", "html": "1932 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actor (d. 2009)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actor (d. 2009)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1932", "text": "<PERSON><PERSON>, American actress and dancer (d. 2005)", "html": "1932 - <a href=\"https://wikipedia.org/wiki/Sheree_North\" title=\"Sheree North\"><PERSON><PERSON></a>, American actress and dancer (d. 2005)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Sheree_North\" title=\"Sheree North\"><PERSON><PERSON></a>, American actress and dancer (d. 2005)", "links": [{"title": "Sheree North", "link": "https://wikipedia.org/wiki/Sheree_North"}]}, {"year": "1933", "text": "<PERSON><PERSON>, Egyptian-French singer and actress (d. 1987)", "html": "1933 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Egyptian-French singer and actress (d. 1987)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Egyptian-French singer and actress (d. 1987)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Dalida"}]}, {"year": "1933", "text": "Prince <PERSON><PERSON><PERSON>, French-Pakistani diplomat, United Nations High Commissioner for Refugees (d. 2003)", "html": "1933 - <a href=\"https://wikipedia.org/wiki/Prince_<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"Prince <PERSON><PERSON><PERSON>\">Prince <PERSON><PERSON><PERSON></a>, French-Pakistani diplomat, <a href=\"https://wikipedia.org/wiki/United_Nations_High_Commissioner_for_Refugees\" title=\"United Nations High Commissioner for Refugees\">United Nations High Commissioner for Refugees</a> (d. 2003)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Prince_<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"Prince <PERSON><PERSON><PERSON>\">Prince <PERSON><PERSON><PERSON></a>, French-Pakistani diplomat, <a href=\"https://wikipedia.org/wiki/United_Nations_High_Commissioner_for_Refugees\" title=\"United Nations High Commissioner for Refugees\">United Nations High Commissioner for Refugees</a> (d. 2003)", "links": [{"title": "Prince <PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Prince_<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>"}, {"title": "United Nations High Commissioner for Refugees", "link": "https://wikipedia.org/wiki/United_Nations_High_Commissioner_for_Refugees"}]}, {"year": "1933", "text": "<PERSON><PERSON>, American actress, puppeteer/ventriloquist, and television host (d. 1998)", "html": "1933 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> <PERSON>\"><PERSON><PERSON></a>, American actress, puppeteer/ventriloquist, and television host (d. 1998)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> <PERSON>\"><PERSON><PERSON></a>, American actress, puppeteer/ventriloquist, and television host (d. 1998)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1934", "text": "<PERSON>, Scottish-American director and screenwriter (d. 1996)", "html": "1934 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish-American director and screenwriter (d. 1996)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish-American director and screenwriter (d. 1996)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1935", "text": "<PERSON>, American businesswoman and politician, 72nd Governor of Delaware (d. 2021)", "html": "1935 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American businesswoman and politician, 72nd <a href=\"https://wikipedia.org/wiki/Governor_of_Delaware\" class=\"mw-redirect\" title=\"Governor of Delaware\">Governor of Delaware</a> (d. 2021)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American businesswoman and politician, 72nd <a href=\"https://wikipedia.org/wiki/Governor_of_Delaware\" class=\"mw-redirect\" title=\"Governor of Delaware\">Governor of Delaware</a> (d. 2021)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "Governor of Delaware", "link": "https://wikipedia.org/wiki/Governor_of_Delaware"}]}, {"year": "1936", "text": "<PERSON>, English academic and diplomat, British ambassador to Japan (d. 2019)", "html": "1936 - <a href=\"https://wikipedia.org/wiki/<PERSON>(diplomat)\" title=\"<PERSON> (diplomat)\"><PERSON></a>, English academic and diplomat, <a href=\"https://wikipedia.org/wiki/List_of_Ambassadors_of_the_United_Kingdom_to_Japan\" class=\"mw-redirect\" title=\"List of Ambassadors of the United Kingdom to Japan\">British ambassador to Japan</a> (d. 2019)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>(diplomat)\" title=\"<PERSON> (diplomat)\"><PERSON></a>, English academic and diplomat, <a href=\"https://wikipedia.org/wiki/List_of_Ambassadors_of_the_United_Kingdom_to_Japan\" class=\"mw-redirect\" title=\"List of Ambassadors of the United Kingdom to Japan\">British ambassador to Japan</a> (d. 2019)", "links": [{"title": "<PERSON> (diplomat)", "link": "https://wikipedia.org/wiki/<PERSON>_(diplomat)"}, {"title": "List of Ambassadors of the United Kingdom to Japan", "link": "https://wikipedia.org/wiki/List_of_Ambassadors_of_the_United_Kingdom_to_Japan"}]}, {"year": "1936", "text": "<PERSON><PERSON>, Sri Lankan lawyer and politician (d. 1997)", "html": "1936 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Sri Lankan lawyer and politician (d. 1997)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_Thangat<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Sri Lankan lawyer and politician (d. 1997)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_Thangathurai"}]}, {"year": "1937", "text": "<PERSON>, French philosopher and academic", "html": "1937 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French philosopher and academic", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French philosopher and academic", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1938", "text": "<PERSON>, American author and academic (d. 1991)", "html": "1938 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author and academic (d. 1991)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author and academic (d. 1991)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1938", "text": "<PERSON><PERSON>, Swedish cross country skier", "html": "1938 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Swedish cross country skier", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Swedish cross country skier", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1939", "text": "<PERSON><PERSON><PERSON><PERSON> of Athens, Greek archbishop (d. 2008)", "html": "1939 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_of_Athens\" title=\"<PERSON><PERSON><PERSON><PERSON> of Athens\"><PERSON><PERSON><PERSON><PERSON> of Athens</a>, Greek archbishop (d. 2008)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_of_Athens\" title=\"<PERSON><PERSON><PERSON><PERSON> of Athens\"><PERSON><PERSON><PERSON><PERSON> of Athens</a>, Greek archbishop (d. 2008)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON> of Athens", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_of_Athens"}]}, {"year": "1939", "text": "<PERSON><PERSON>, American talk show host and producer", "html": "1939 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American talk show host and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American talk show host and producer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1940", "text": "<PERSON><PERSON><PERSON>, Egyptian-Armenian patriarch (d. 2015)", "html": "1940 - <a href=\"https://wikipedia.org/wiki/N<PERSON><PERSON>_Bedros_XIX_Tarmouni\" title=\"Nerses Bedros XIX Tarmouni\"><PERSON><PERSON><PERSON> XIX <PERSON></a>, Egyptian-Armenian patriarch (d. 2015)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/N<PERSON><PERSON>_Bedros_XIX_Tarmouni\" title=\"<PERSON>erses Bedros XIX Tarmouni\"><PERSON><PERSON><PERSON> XI<PERSON></a>, Egyptian-Armenian patriarch (d. 2015)", "links": [{"title": "<PERSON><PERSON><PERSON> XIX Tarmouni", "link": "https://wikipedia.org/wiki/N<PERSON><PERSON>_Bedros_XIX_<PERSON>ni"}]}, {"year": "1940", "text": "<PERSON><PERSON><PERSON><PERSON>, Kenyan athlete", "html": "1940 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Kenyan athlete", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Kenyan athlete", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1940", "text": "<PERSON><PERSON><PERSON>, Uruguayan physician and politician, 39th President of Uruguay (d. 2020)", "html": "1940 - <a href=\"https://wikipedia.org/wiki/Tabar%C3%A9_V%C3%<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Uruguayan physician and politician, 39th <a href=\"https://wikipedia.org/wiki/President_of_Uruguay\" title=\"President of Uruguay\">President of Uruguay</a> (d. 2020)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Tabar%C3%A9_V%C3%<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Uruguayan physician and politician, 39th <a href=\"https://wikipedia.org/wiki/President_of_Uruguay\" title=\"President of Uruguay\">President of Uruguay</a> (d. 2020)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Tabar%C3%A9_V%C3%<PERSON><PERSON><PERSON>"}, {"title": "President of Uruguay", "link": "https://wikipedia.org/wiki/President_of_Uruguay"}]}, {"year": "1941", "text": "<PERSON><PERSON><PERSON>, Jr., Hungarian physicist and architect", "html": "1941 - <a href=\"https://wikipedia.org/wiki/Istv%C3%<PERSON><PERSON>_<PERSON><PERSON>,_Jr.\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON>, Jr.\"><PERSON><PERSON><PERSON>, Jr.</a>, Hungarian physicist and architect", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Istv%C3%<PERSON><PERSON>_<PERSON>,_Jr.\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON>, Jr.\"><PERSON><PERSON><PERSON>, Jr.</a>, Hungarian physicist and architect", "links": [{"title": "<PERSON><PERSON><PERSON>, Jr.", "link": "https://wikipedia.org/wiki/Istv%C3%<PERSON><PERSON>_<PERSON><PERSON><PERSON>,_<PERSON>."}]}, {"year": "1942", "text": "<PERSON>, American boxer and activist (d. 2016)", "html": "1942 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American boxer and activist (d. 2016)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Ali\"><PERSON></a>, American boxer and activist (d. 2016)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1942", "text": "<PERSON><PERSON>, Australian journalist and author", "html": "1942 - <a href=\"https://wikipedia.org/wiki/Ita_<PERSON><PERSON><PERSON>\" title=\"Ita Buttrose\"><PERSON><PERSON> <PERSON></a>, Australian journalist and author", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Ita_<PERSON><PERSON><PERSON>\" title=\"Ita Buttrose\"><PERSON><PERSON></a>, Australian journalist and author", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Ita_But<PERSON>se"}]}, {"year": "1942", "text": "<PERSON><PERSON>, German violinist and educator", "html": "1942 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, German violinist and educator", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, German violinist and educator", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1942", "text": "<PERSON>, English bishop", "html": "1942 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English bishop", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English bishop", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1943", "text": "<PERSON>, American singer-songwriter and guitarist", "html": "1943 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and guitarist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and guitarist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1943", "text": "<PERSON>, Haitian agronomist and politician, 52nd President of Haiti (d. 2017)", "html": "1943 - <a href=\"https://wikipedia.org/wiki/Ren%C3%A9_Pr%C3%A9val\" title=\"<PERSON>\"><PERSON></a>, Haitian agronomist and politician, 52nd <a href=\"https://wikipedia.org/wiki/President_of_Haiti\" title=\"President of Haiti\">President of Haiti</a> (d. 2017)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Ren%C3%A9_Pr%C3%A9val\" title=\"<PERSON>\"><PERSON></a>, Haitian agronomist and politician, 52nd <a href=\"https://wikipedia.org/wiki/President_of_Haiti\" title=\"President of Haiti\">President of Haiti</a> (d. 2017)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Ren%C3%A9_Pr%C3%A9val"}, {"title": "President of Haiti", "link": "https://wikipedia.org/wiki/President_of_Haiti"}]}, {"year": "1944", "text": "<PERSON>, English sociologist, author, and academic", "html": "1944 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English sociologist, author, and academic", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English sociologist, author, and academic", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1945", "text": "<PERSON><PERSON><PERSON>, Indian poet, playwright, and composer", "html": "1945 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Indian poet, playwright, and composer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Indian poet, playwright, and composer", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/J<PERSON><PERSON>_<PERSON><PERSON><PERSON>"}]}, {"year": "1945", "text": "<PERSON>, Australian psychologist and academic (d. 2022)", "html": "1945 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian psychologist and academic (d. 2022)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian psychologist and academic (d. 2022)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1947", "text": "<PERSON>, English actress", "html": "1947 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actress", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1947", "text": "<PERSON>, American actress", "html": "1947 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1948", "text": "<PERSON><PERSON><PERSON><PERSON>, Icelandic politician, 21st Prime Minister of Iceland", "html": "1948 - <a href=\"https://wikipedia.org/wiki/Dav%C3%AD%C3%B<PERSON>_<PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Icelandic politician, 21st <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Iceland\" title=\"Prime Minister of Iceland\">Prime Minister of Iceland</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Dav%C3%AD%C3%B<PERSON>_<PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Icelandic politician, 21st <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Iceland\" title=\"Prime Minister of Iceland\">Prime Minister of Iceland</a>", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Dav%C3%AD%C3%B0_<PERSON><PERSON>"}, {"title": "Prime Minister of Iceland", "link": "https://wikipedia.org/wiki/Prime_Minister_of_Iceland"}]}, {"year": "1949", "text": "<PERSON>, American computer scientist and academic (d. 2003)", "html": "1949 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American computer scientist and academic (d. 2003)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American computer scientist and academic (d. 2003)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1949", "text": "<PERSON><PERSON><PERSON>, Liberian businessman and politician (d. 2014)", "html": "1949 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Liberian businessman and politician (d. 2014)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Liberian businessman and politician (d. 2014)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1949", "text": "<PERSON><PERSON>, French violinist and conductor", "html": "1949 - <a href=\"https://wikipedia.org/wiki/August<PERSON>_<PERSON>\" title=\"August<PERSON>\">August<PERSON></a>, French violinist and conductor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/August<PERSON>_<PERSON>\" title=\"August<PERSON>\"><PERSON><PERSON></a>, French violinist and conductor", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/August<PERSON>_<PERSON>"}]}, {"year": "1949", "text": "<PERSON>, American actor and comedian (d. 1984)", "html": "1949 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor and comedian (d. 1984)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor and comedian (d. 1984)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1949", "text": "<PERSON>, English singer-songwriter and guitarist", "html": "1949 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English singer-songwriter and guitarist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English singer-songwriter and guitarist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1950", "text": "<PERSON>, Puerto Rican-American author and academic", "html": "1950 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%B3pe<PERSON>_<PERSON>eves\" title=\"<PERSON>\"><PERSON></a>, Puerto Rican-American author and academic", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%B3pez_<PERSON>eves\" title=\"<PERSON>\"><PERSON></a>, Puerto Rican-American author and academic", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Luis_L%C3%B3<PERSON><PERSON>_<PERSON>eves"}]}, {"year": "1952", "text": "<PERSON>, American author (d. 2009)", "html": "1952 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author (d. 2009)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author (d. 2009)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1952", "text": "<PERSON>, American baseball player and sportscaster (d. 2002)", "html": "1952 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player and sportscaster (d. 2002)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player and sportscaster (d. 2002)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1952", "text": "<PERSON><PERSON><PERSON>, Japanese pianist, composer, and producer (d. 2023)", "html": "1952 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese pianist, composer, and producer (d. 2023)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese pianist, composer, and producer (d. 2023)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1953", "text": "<PERSON>, American bass player and educator", "html": "1953 - <a href=\"https://wikipedia.org/wiki/Jeff_<PERSON>\" title=\"Jeff Berlin\"><PERSON></a>, American bass player and educator", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Jeff_<PERSON>\" title=\"Jeff Berlin\"><PERSON></a>, American bass player and educator", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Jeff_<PERSON>"}]}, {"year": "1953", "text": "<PERSON>, American singer and guitarist", "html": "1953 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(blues_musician)\" title=\"<PERSON> (blues musician)\"><PERSON></a>, American singer and guitarist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(blues_musician)\" title=\"<PERSON> (blues musician)\"><PERSON></a>, American singer and guitarist", "links": [{"title": "<PERSON> (blues musician)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(blues_musician)"}]}, {"year": "1954", "text": "<PERSON>, Jr., American environmental lawyer, writer, and conspiracy theorist", "html": "1954 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>,_Jr.\" class=\"mw-redirect\" title=\"<PERSON>, Jr.\"><PERSON>, Jr.</a>, American environmental lawyer, writer, and conspiracy theorist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>,_Jr.\" class=\"mw-redirect\" title=\"<PERSON>, Jr.\"><PERSON>, Jr.</a>, American environmental lawyer, writer, and conspiracy theorist", "links": [{"title": "<PERSON>, Jr.", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>,_Jr."}]}, {"year": "1955", "text": "<PERSON>, American singer-songwriter, musician, record producer, author and actor", "html": "1955 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter, musician, record producer, author and actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter, musician, record producer, author and actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1955", "text": "<PERSON>, Italian cardinal", "html": "1955 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian cardinal", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian cardinal", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1955", "text": "<PERSON>, American basketball player and referee", "html": "1955 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player and referee", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player and referee", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1956", "text": "<PERSON>, English journalist and politician", "html": "1956 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English journalist and politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English journalist and politician", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1956", "text": "<PERSON>, English singer-songwriter and guitarist", "html": "1956 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English singer-songwriter and guitarist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English singer-songwriter and guitarist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1957", "text": "<PERSON>, American actor, comedian, television personality and game show host", "html": "1957 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor, comedian, television personality and game show host", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor, comedian, television personality and game show host", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1957", "text": "<PERSON>, American journalist and author", "html": "1957 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American journalist and author", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American journalist and author", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1958", "text": "<PERSON>, English biologist, cancer researcher", "html": "1958 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English biologist, cancer researcher", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English biologist, cancer researcher", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1959", "text": "<PERSON><PERSON>, American singer-songwriter, guitarist, and actress", "html": "1959 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American singer-songwriter, guitarist, and actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American singer-songwriter, guitarist, and actress", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1960", "text": "<PERSON>, American singer-songwriter and guitarist", "html": "1960 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(musician)\" title=\"<PERSON> (musician)\"><PERSON></a>, American singer-songwriter and guitarist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>(musician)\" title=\"<PERSON> (musician)\"><PERSON></a>, American singer-songwriter and guitarist", "links": [{"title": "<PERSON> (musician)", "link": "https://wikipedia.org/wiki/<PERSON>(musician)"}]}, {"year": "1960", "text": "<PERSON><PERSON>, Jamaican-American baseball player and coach", "html": "1960 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Jamaican-American baseball player and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Jamaican-American baseball player and coach", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1961", "text": "<PERSON>, American director, producer, and screenwriter", "html": "1961 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American director, producer, and screenwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American director, producer, and screenwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1962", "text": "<PERSON>, Japanese broadcaster and politician, 46th Japanese Minister of Finance", "html": "1962 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Japanese broadcaster and politician, 46th <a href=\"https://wikipedia.org/wiki/Minister_of_Finance_(Japan)\" title=\"Minister of Finance (Japan)\">Japanese Minister of Finance</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Japanese broadcaster and politician, 46th <a href=\"https://wikipedia.org/wiki/Minister_of_Finance_(Japan)\" title=\"Minister of Finance (Japan)\">Japanese Minister of Finance</a>", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>"}, {"title": "Minister of Finance (Japan)", "link": "https://wikipedia.org/wiki/Minister_of_Finance_(Japan)"}]}, {"year": "1962", "text": "<PERSON>, Canadian-American actor, comedian, and producer", "html": "1962 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian-American actor, comedian, and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian-American actor, comedian, and producer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1962", "text": "<PERSON>, American journalist and author", "html": "1962 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American journalist and author", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American journalist and author", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1962", "text": "<PERSON>, American actor and singer", "html": "1962 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%27Hare\" title=\"<PERSON>\"><PERSON></a>, American actor and singer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%27Hare\" title=\"<PERSON>\"><PERSON></a>, American actor and singer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Denis_O%27Hare"}]}, {"year": "1963", "text": "<PERSON>, English footballer, agent, manager and chief executive", "html": "1963 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>(footballer)\" title=\"<PERSON> (footballer)\"><PERSON></a>, English footballer, agent, manager and chief executive", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>(footballer)\" title=\"<PERSON> (footballer)\"><PERSON></a>, English footballer, agent, manager and chief executive", "links": [{"title": "<PERSON> (footballer)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>(footballer)"}]}, {"year": "1963", "text": "<PERSON>, German singer-songwriter, guitarist, and producer", "html": "1963 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German singer-songwriter, guitarist, and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German singer-songwriter, guitarist, and producer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1964", "text": "<PERSON>, American lawyer and activist, 44th First Lady of the United States", "html": "1964 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and activist, 44th <a href=\"https://wikipedia.org/wiki/List_of_First_Ladies_of_the_United_States\" class=\"mw-redirect\" title=\"List of First Ladies of the United States\">First Lady of the United States</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and activist, 44th <a href=\"https://wikipedia.org/wiki/List_of_First_Ladies_of_the_United_States\" class=\"mw-redirect\" title=\"List of First Ladies of the United States\">First Lady of the United States</a>", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>"}, {"title": "List of First Ladies of the United States", "link": "https://wikipedia.org/wiki/List_of_First_Ladies_of_the_United_States"}]}, {"year": "1964", "text": "<PERSON>, Samoan-New Zealand rugby player", "html": "1964 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Samoan-New Zealand rugby player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Samoan-New Zealand rugby player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>"}]}, {"year": "1965", "text": "<PERSON><PERSON><PERSON><PERSON>, Canadian ice hockey player", "html": "1965 - <a href=\"https://wikipedia.org/wiki/Syl<PERSON><PERSON>_Tu<PERSON>on\" title=\"Sylvain Turgeon\"><PERSON><PERSON><PERSON><PERSON></a>, Canadian ice hockey player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>yl<PERSON><PERSON>_Tu<PERSON>\" title=\"Sylvain Turgeon\"><PERSON><PERSON><PERSON><PERSON></a>, Canadian ice hockey player", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>yl<PERSON><PERSON>_<PERSON>"}]}, {"year": "1966", "text": "<PERSON><PERSON>, English golfer", "html": "1966 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, English golfer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> <PERSON>\"><PERSON><PERSON></a>, English golfer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1966", "text": "<PERSON>, American actor", "html": "1966 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1966", "text": "<PERSON><PERSON><PERSON>, Jamaican rapper, musician, and songwriter", "html": "1966 - <a href=\"https://wikipedia.org/wiki/Shabba_Ranks\" title=\"Shabba Ranks\">Shabba Ranks</a>, Jamaican rapper, musician, and songwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Shabba_Ranks\" title=\"Shabba Ranks\">Shabba Ranks</a>, Jamaican rapper, musician, and songwriter", "links": [{"title": "Shabba Ranks", "link": "https://wikipedia.org/wiki/Shabba_Ranks"}]}, {"year": "1967", "text": "<PERSON>, English singer-songwriter, guitarist, and producer", "html": "1967 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English singer-songwriter, guitarist, and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English singer-songwriter, guitarist, and producer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1968", "text": "<PERSON>, English journalist and author", "html": "1968 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> P<PERSON>\"><PERSON></a>, English journalist and author", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Pelling\"><PERSON></a>, English journalist and author", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Rowan_Pelling"}]}, {"year": "1968", "text": "<PERSON><PERSON>, Dutch author, poet, and scholar", "html": "1968 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Dutch author, poet, and scholar", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Dutch author, poet, and scholar", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1969", "text": "<PERSON><PERSON><PERSON>, English actor", "html": "1969 - <a href=\"https://wikipedia.org/wiki/Na<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, English actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Na<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, English actor", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Na<PERSON><PERSON>_<PERSON>"}]}, {"year": "1969", "text": "<PERSON><PERSON>, Swedish director, screenwriter, and author", "html": "1969 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Swedish director, screenwriter, and author", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Swedish director, screenwriter, and author", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1969", "text": "<PERSON><PERSON><PERSON><PERSON>, Dutch DJ and producer", "html": "1969 - <a href=\"https://wikipedia.org/wiki/Ti%C3%ABsto\" title=\"Tië<PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Dutch DJ and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Ti%C3%ABsto\" title=\"Tië<PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Dutch DJ and producer", "links": [{"title": "Tiësto", "link": "https://wikipedia.org/wiki/Ti%C3%ABsto"}]}, {"year": "1970", "text": "<PERSON><PERSON><PERSON>, Brazilian footballer", "html": "1970 - <a href=\"https://wikipedia.org/wiki/C%C3%A1ssio_Alves_de_Barros\" class=\"mw-redirect\" title=\"<PERSON><PERSON>sio Alves de Barros\"><PERSON><PERSON><PERSON> Barr<PERSON></a>, Brazilian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/C%C3%A1ssio_Alves_de_Barros\" class=\"mw-redirect\" title=\"Cássio Alves de Barros\"><PERSON><PERSON><PERSON> Alves <PERSON> Barr<PERSON></a>, Brazilian footballer", "links": [{"title": "Cássio Al<PERSON> de Barros", "link": "https://wikipedia.org/wiki/C%C3%<PERSON><PERSON><PERSON>_<PERSON><PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1970", "text": "<PERSON>, American ice hockey player and actor", "html": "1970 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American ice hockey player and actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American ice hockey player and actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1970", "text": "<PERSON><PERSON>, Russian-American animator, director, and producer", "html": "1970 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Russian-American animator, director, and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Russian-American animator, director, and producer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1971", "text": "<PERSON><PERSON><PERSON>, Greek basketball player", "html": "1971 - <a href=\"https://wikipedia.org/wiki/Gior<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Greek basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Greek basketball player", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>nis"}]}, {"year": "1971", "text": "<PERSON>, English race car driver (d. 2005)", "html": "1971 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English race car driver (d. 2005)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English race car driver (d. 2005)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1971", "text": "<PERSON>, American singer-songwriter, producer, and actor", "html": "1971 - <a href=\"https://wikipedia.org/wiki/Kid_Rock\" title=\"Kid Rock\">Kid Rock</a>, American singer-songwriter, producer, and actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Kid_Rock\" title=\"Kid Rock\">Kid Rock</a>, American singer-songwriter, producer, and actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Kid_Rock"}]}, {"year": "1971", "text": "<PERSON><PERSON><PERSON>, French actress, director, and screenwriter", "html": "1971 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, French actress, director, and screenwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, French actress, director, and screenwriter", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>ud"}]}, {"year": "1973", "text": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, Mexican footballer and actor", "html": "1973 - <a href=\"https://wikipedia.org/wiki/Cuauht%C3%A9mo<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON></a>, Mexican footballer and actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Cuauht%C3%A9mo<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON></a>, Mexican footballer and actor", "links": [{"title": "Cuauhté<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Cuauht%C3%A9moc_Blanco"}]}, {"year": "1973", "text": "<PERSON>, Australian politician, 37th Treasurer of Australia", "html": "1973 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian politician, 37th <a href=\"https://wikipedia.org/wiki/Treasurer_of_Australia\" title=\"Treasurer of Australia\">Treasurer of Australia</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian politician, 37th <a href=\"https://wikipedia.org/wiki/Treasurer_of_Australia\" title=\"Treasurer of Australia\">Treasurer of Australia</a>", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}, {"title": "Treasurer of Australia", "link": "https://wikipedia.org/wiki/Treasurer_of_Australia"}]}, {"year": "1973", "text": "<PERSON>, Australian netball player and sportscaster", "html": "1973 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian netball player and sportscaster", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian netball player and sportscaster", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1973", "text": "<PERSON>, Canadian ice hockey player and sportscaster", "html": "1973 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(ice_hockey)\" title=\"<PERSON> (ice hockey)\"><PERSON></a>, Canadian ice hockey player and sportscaster", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(ice_hockey)\" title=\"<PERSON> (ice hockey)\"><PERSON></a>, Canadian ice hockey player and sportscaster", "links": [{"title": "<PERSON> (ice hockey)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(ice_hockey)"}]}, {"year": "1974", "text": "<PERSON>, Chinese footballer and manager", "html": "1974 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(footballer,_born_1974)\" title=\"<PERSON> (footballer, born 1974)\"><PERSON></a>, Chinese footballer and manager", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(footballer,_born_1974)\" title=\"<PERSON> (footballer, born 1974)\"><PERSON></a>, Chinese footballer and manager", "links": [{"title": "<PERSON> (footballer, born 1974)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(footballer,_born_1974)"}]}, {"year": "1974", "text": "<PERSON><PERSON><PERSON>, Bulgarian viola player, composer, and producer", "html": "1974 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Bulgarian viola player, composer, and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Bulgarian viola player, composer, and producer", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1974", "text": "<PERSON>, American football player", "html": "1974 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1975", "text": "<PERSON>, American actor", "html": "1975 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(actor)\" title=\"<PERSON> (actor)\"><PERSON></a>, American actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(actor)\" title=\"<PERSON> (actor)\"><PERSON></a>, American actor", "links": [{"title": "<PERSON> (actor)", "link": "https://wikipedia.org/wiki/<PERSON>_(actor)"}]}, {"year": "1977", "text": "<PERSON>, Australian actor, director, screenwriter, and producer", "html": "1977 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian actor, director, screenwriter, and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian actor, director, screenwriter, and producer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1978", "text": "<PERSON>, Australian Paralympian", "html": "1978 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian <a href=\"https://wikipedia.org/wiki/Paralympic_athletics\" class=\"mw-redirect\" title=\"Paralympic athletics\">Paralympian</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian <a href=\"https://wikipedia.org/wiki/Paralympic_athletics\" class=\"mw-redirect\" title=\"Paralympic athletics\">Paralympian</a>", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Paralympic athletics", "link": "https://wikipedia.org/wiki/Paralympic_athletics"}]}, {"year": "1978", "text": "<PERSON>, English singer-songwriter", "html": "1978 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(English_singer)\" class=\"mw-redirect\" title=\"<PERSON> (English singer)\"><PERSON></a>, English singer-songwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(English_singer)\" class=\"mw-redirect\" title=\"<PERSON> (English singer)\"><PERSON></a>, English singer-songwriter", "links": [{"title": "<PERSON> (English singer)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(English_singer)"}]}, {"year": "1980", "text": "<PERSON><PERSON><PERSON>, Ukrainian-American dancer and choreographer", "html": "1980 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Ukrainian-American dancer and choreographer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Ukrainian-American dancer and choreographer", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1980", "text": "<PERSON><PERSON>, American singer-songwriter and actress", "html": "1980 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>el\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American singer-songwriter and actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>el\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American singer-songwriter and actress", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>ey_Deschanel"}]}, {"year": "1980", "text": "<PERSON><PERSON><PERSON>, Lithuanian footballer", "html": "1980 - <a href=\"https://wikipedia.org/wiki/Modestas_Stonys\" title=\"Modestas Stonys\"><PERSON><PERSON><PERSON> St<PERSON></a>, Lithuanian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Modestas_Stonys\" title=\"Modestas Stonys\"><PERSON><PERSON><PERSON> St<PERSON></a>, Lithuanian footballer", "links": [{"title": "Modestas Stonys", "link": "https://wikipedia.org/wiki/Modestas_Stonys"}]}, {"year": "1981", "text": "<PERSON>, Northern Irish footballer and manager", "html": "1981 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Northern Irish footballer and manager", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Northern Irish footballer and manager", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1981", "text": "<PERSON>, American singer, actor, and television personality", "html": "1981 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer, actor, and television personality", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer, actor, and television personality", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1981", "text": "<PERSON>, Canadian ice hockey player", "html": "1981 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1982", "text": "<PERSON><PERSON><PERSON>, American basketball player", "html": "1982 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American basketball player", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1982", "text": "<PERSON>, Australian rugby league player and coach", "html": "1982 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(rugby_league)\" title=\"<PERSON> (rugby league)\"><PERSON></a>, Australian rugby league player and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(rugby_league)\" title=\"<PERSON> (rugby league)\"><PERSON></a>, Australian rugby league player and coach", "links": [{"title": "<PERSON> (rugby league)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(rugby_league)"}]}, {"year": "1982", "text": "<PERSON>, Canadian singer", "html": "1982 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian singer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian singer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1983", "text": "<PERSON><PERSON><PERSON>, Spanish footballer", "html": "1983 - <a href=\"https://wikipedia.org/wiki/%C3%81l<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Spanish footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/%C3%<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Spanish footballer", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/%C3%81l<PERSON>_<PERSON>"}]}, {"year": "1983", "text": "<PERSON>, English actor", "html": "1983 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1983", "text": "<PERSON>, German basketball player", "html": "1983 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German basketball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1983", "text": "<PERSON>, Australian race car driver", "html": "1983 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian race car driver", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian race car driver", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1983", "text": "<PERSON><PERSON>, Brazilian martial artist", "html": "1983 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_(grappler)\" title=\"<PERSON><PERSON> (grappler)\"><PERSON><PERSON></a>, Brazilian martial artist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_(grappler)\" title=\"<PERSON><PERSON> (grappler)\"><PERSON><PERSON></a>, Brazilian martial artist", "links": [{"title": "<PERSON><PERSON> (grappler)", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_(grappler)"}]}, {"year": "1984", "text": "<PERSON>, Scottish singer-songwriter, DJ, and producer", "html": "1984 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish singer-songwriter, DJ, and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish singer-songwriter, DJ, and producer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1984", "text": "<PERSON>, American wrestler", "html": "1984 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American wrestler", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American wrestler", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1985", "text": "<PERSON>, Argentinian footballer", "html": "1985 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Argentinian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Argentinian footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1985", "text": "<PERSON>, Dutch singer-songwriter", "html": "1985 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch singer-songwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch singer-songwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1986", "text": "<PERSON>, Swedish ice hockey player", "html": "1986 - <a href=\"https://wikipedia.org/wiki/Viktor_St%C3%A5lberg\" title=\"<PERSON>\"><PERSON></a>, Swedish ice hockey player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Viktor_St%C3%A5lberg\" title=\"<PERSON>\"><PERSON></a>, Swedish ice hockey player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Viktor_St%C3%A5lberg"}]}, {"year": "1987", "text": "<PERSON>, American baseball player", "html": "1987 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1987", "text": "<PERSON><PERSON><PERSON><PERSON>, Ukrainian boxer", "html": "1987 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Ukrainian boxer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Ukrainian boxer", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1988", "text": "<PERSON>, Italian motorcycle racer (d. 2013)", "html": "1988 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian motorcycle racer (d. 2013)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian motorcycle racer (d. 2013)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1988", "text": "<PERSON>, American basketball player", "html": "1988 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1988", "text": "<PERSON>, Australian rugby player", "html": "1988 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian rugby player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian rugby player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Will_Genia"}]}, {"year": "1988", "text": "<PERSON>, American actor", "html": "1988 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1988", "text": "<PERSON><PERSON><PERSON>, Mexican footballer", "html": "1988 - <a href=\"https://wikipedia.org/wiki/H%C3%A<PERSON><PERSON>_<PERSON>_(footballer)\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON> (footballer)\"><PERSON><PERSON><PERSON></a>, Mexican footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/H%C3%A<PERSON><PERSON>_<PERSON>_(footballer)\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON> (footballer)\"><PERSON><PERSON><PERSON></a>, Mexican footballer", "links": [{"title": "<PERSON><PERSON><PERSON> (footballer)", "link": "https://wikipedia.org/wiki/H%C3%A<PERSON><PERSON>_<PERSON>_(footballer)"}]}, {"year": "1989", "text": "<PERSON>, American baseball player", "html": "1989 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Jordan\"><PERSON></a>, American baseball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Taylor Jordan\"><PERSON></a>, American baseball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1989", "text": "<PERSON>, American actress", "html": "1989 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1990", "text": "<PERSON>, Colombian footballer", "html": "1990 - <a href=\"https://wikipedia.org/wiki/Santiago_Tr%C3%A9llez\" title=\"Santiago Tréllez\"><PERSON>ré<PERSON></a>, Colombian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Santiago_Tr%C3%A9llez\" title=\"Santiago Tréllez\"><PERSON>ré<PERSON></a>, Colombian footballer", "links": [{"title": "Santiago Tréllez", "link": "https://wikipedia.org/wiki/Santiago_Tr%C3%A9llez"}]}, {"year": "1990", "text": "<PERSON>, American basketball player", "html": "1990 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1991", "text": "<PERSON>, American baseball player", "html": "1991 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1991", "text": "<PERSON><PERSON>, American actress", "html": "1991 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American actress", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1991", "text": "<PERSON><PERSON><PERSON><PERSON>, Finnish rally driver", "html": "1991 - <a href=\"https://wikipedia.org/wiki/<PERSON>sa<PERSON><PERSON>_<PERSON>\" title=\"<PERSON>sa<PERSON><PERSON> La<PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Finnish rally driver", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON>sa<PERSON><PERSON> La<PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Finnish rally driver", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Esa<PERSON><PERSON>_<PERSON>i"}]}, {"year": "1991", "text": "<PERSON><PERSON>, American BMX rider", "html": "1991 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American BMX rider", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American BMX rider", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1992", "text": "<PERSON><PERSON>, Russian ice hockey player", "html": "1992 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Russian ice hockey player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Russian ice hockey player", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>"}]}, {"year": "1994", "text": "<PERSON>, American-English actress", "html": "1994 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American-English actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American-English actress", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1994", "text": "<PERSON>, Australian cricketer", "html": "1994 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian cricketer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian cricketer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1995", "text": "<PERSON><PERSON>, American actor and model", "html": "1995 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American actor and model", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American actor and model", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1996", "text": "<PERSON><PERSON><PERSON>, American basketball player", "html": "1996 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>er\" title=\"<PERSON><PERSON><PERSON> Trier\"><PERSON><PERSON><PERSON></a>, American basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>er\" title=\"<PERSON><PERSON><PERSON> Trier\"><PERSON><PERSON><PERSON></a>, American basketball player", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>er"}]}, {"year": "1997", "text": "<PERSON>, American boxer, actor, rapper, and social media personality", "html": "1997 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American boxer, actor, rapper, and social media personality", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American boxer, actor, rapper, and social media personality", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1997", "text": "<PERSON>, American baseball player", "html": "1997 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1998", "text": "<PERSON>, Australian cricketer", "html": "1998 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian cricketer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian cricketer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1998", "text": "<PERSON><PERSON><PERSON><PERSON>, French footballer", "html": "1998 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>-<PERSON>%C3%A9la%C3%AFde\" title=\"<PERSON>\"><PERSON></a>, French footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>-<PERSON>%C3%A9la%C3%AFde\" title=\"<PERSON>\"><PERSON></a>, French footballer", "links": [{"title": "<PERSON>-<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>-Ad%C3%A9la%C3%AFde"}]}, {"year": "1999", "text": "<PERSON>, American actor and singer", "html": "1999 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor and singer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor and singer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>"}]}, {"year": "2000", "text": "<PERSON>, South Korean singer and actor", "html": "2000 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>-hee\" title=\"<PERSON>\"><PERSON></a>, South Korean singer and actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>-hee\" title=\"<PERSON>\"><PERSON></a>, South Korean singer and actor", "links": [{"title": "<PERSON>e", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>-hee"}]}, {"year": "2000", "text": "<PERSON>, Canadian race car driver", "html": "2000 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian race car driver", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian race car driver", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2000", "text": "<PERSON><PERSON>, American basketball player", "html": "2000 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American basketball player", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "2001", "text": "<PERSON><PERSON>, Argentinian footballer", "html": "2001 - <a href=\"https://wikipedia.org/wiki/Enzo_Fern%C3%A1ndez\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Argentinian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/En<PERSON>_Fern%C3%A1ndez\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Argentinian footballer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Enzo_Fern%C3%A1ndez"}]}], "Deaths": [{"year": "395", "text": "<PERSON><PERSON><PERSON>, Roman emperor (b. 347)", "html": "395 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_I\" title=\"<PERSON><PERSON><PERSON> I\"><PERSON><PERSON><PERSON></a>, Roman emperor (b. 347)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_I\" title=\"<PERSON><PERSON>ius I\"><PERSON><PERSON><PERSON> I</a>, Roman emperor (b. 347)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_I"}]}, {"year": "644", "text": "<PERSON><PERSON><PERSON> the <PERSON>, French bishop and saint", "html": "644 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_the_Pious\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON> the Pious\"><PERSON><PERSON><PERSON> the Pious</a>, French bishop and saint", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_the_Pious\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON> the Pious\"><PERSON><PERSON><PERSON> the Pious</a>, French bishop and saint", "links": [{"title": "<PERSON><PERSON><PERSON> the <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_the_Pious"}]}, {"year": "764", "text": "<PERSON> of Freising, German bishop", "html": "764 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON> of Fr<PERSON>sing\"><PERSON> of Fr<PERSON>sing</a>, German bishop", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON> of Freising\"><PERSON> of <PERSON></a>, German bishop", "links": [{"title": "<PERSON> of Freising", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1040", "text": "<PERSON><PERSON><PERSON><PERSON><PERSON> <PERSON> of Ghazni, Sultan of the Ghaznavid Empire (b. 998)", "html": "1040 - <a href=\"https://wikipedia.org/wiki/Mas%27ud_I_of_Ghazni\" class=\"mw-redirect\" title=\"Mas'ud I of Ghazni\"><PERSON><PERSON><PERSON>ud <PERSON> of Ghazni</a>, Sultan of the <a href=\"https://wikipedia.org/wiki/Ghaznavids\" title=\"Ghaznavids\">Ghaznavid Empire</a> (b. 998)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Mas%27ud_I_of_Ghazni\" class=\"mw-redirect\" title=\"Mas'ud I of Ghazni\"><PERSON><PERSON><PERSON>ud <PERSON> of Ghazni</a>, Sultan of the <a href=\"https://wikipedia.org/wiki/Ghaznavids\" title=\"Ghaznavids\">Ghaznavid Empire</a> (b. 998)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON><PERSON> <PERSON> of Ghazni", "link": "https://wikipedia.org/wiki/Mas%27ud_I_of_Ghaz<PERSON>"}, {"title": "Ghaz<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Gha<PERSON><PERSON><PERSON>s"}]}, {"year": "1156", "text": "<PERSON>, fifth Grand Master of the Knights Templar", "html": "1156 - <a href=\"https://wikipedia.org/wiki/Andr%C3%A<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, fifth <a href=\"https://wikipedia.org/wiki/Grand_Masters_of_the_Knights_Templar\" class=\"mw-redirect\" title=\"Grand Masters of the Knights Templar\">Grand Master of the Knights Templar</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Andr%C3%A<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, fifth <a href=\"https://wikipedia.org/wiki/Grand_Masters_of_the_Knights_Templar\" class=\"mw-redirect\" title=\"Grand Masters of the Knights Templar\">Grand Master of the Knights Templar</a>", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Andr%C3%A9_<PERSON>_<PERSON>"}, {"title": "Grand Masters of the Knights Templar", "link": "https://wikipedia.org/wiki/Grand_Masters_of_the_Knights_Templar"}]}, {"year": "1168", "text": "<PERSON><PERSON><PERSON>, Count of Flanders (b. 1099)", "html": "1168 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>,_Count_of_Flanders\" title=\"<PERSON><PERSON><PERSON>, Count of Flanders\"><PERSON><PERSON><PERSON>, Count of Flanders</a> (b. 1099)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>,_Count_of_Flanders\" title=\"<PERSON><PERSON><PERSON>, Count of Flanders\"><PERSON><PERSON><PERSON>, Count of Flanders</a> (b. 1099)", "links": [{"title": "<PERSON><PERSON><PERSON>, Count of Flanders", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>,_Count_of_Flanders"}]}, {"year": "1229", "text": "<PERSON> of Riga, German bishop (b. 1165)", "html": "1229 - <a href=\"https://wikipedia.org/wiki/Albert_of_Riga\" title=\"<PERSON> of Riga\"><PERSON> of Riga</a>, German bishop (b. 1165)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Albert_of_Riga\" title=\"Albert of Riga\"><PERSON> of Riga</a>, German bishop (b. 1165)", "links": [{"title": "Albert of Riga", "link": "https://wikipedia.org/wiki/Albert_of_Riga"}]}, {"year": "1329", "text": "<PERSON>, Carthusian nun (b. 1263)", "html": "1329 - <a href=\"https://wikipedia.org/wiki/Saint_Roseline\" class=\"mw-redirect\" title=\"Saint Roseline\"><PERSON></a>, Carthusian nun (b. 1263)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Saint_Roseline\" class=\"mw-redirect\" title=\"Saint Roseline\"><PERSON></a>, Carthusian nun (b. 1263)", "links": [{"title": "Saint Roseline", "link": "https://wikipedia.org/wiki/Saint_Roseline"}]}, {"year": "1334", "text": "<PERSON> of Brittany, Earl of Richmond (b. 1266)", "html": "1334 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_Brittany,_Earl_of_Richmond\" title=\"<PERSON> of Brittany, Earl of Richmond\"><PERSON> of Brittany, Earl of Richmond</a> (b. 1266)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_Brittany,_Earl_of_Richmond\" title=\"<PERSON> of Brittany, Earl of Richmond\"><PERSON> of Brittany, Earl of Richmond</a> (b. 1266)", "links": [{"title": "<PERSON> of Brittany, Earl of Richmond", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_Brittany,_Earl_of_Richmond"}]}, {"year": "1345", "text": "<PERSON> of Asti, Greek patriarch", "html": "1345 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON> of Asti\"><PERSON> of Asti</a>, Greek patriarch", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON> of Asti\"><PERSON> of Asti</a>, Greek patriarch", "links": [{"title": "<PERSON> of Asti", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1345", "text": "<PERSON><PERSON>, Genoese Lord of Chios", "html": "1345 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Genoese <a href=\"https://wikipedia.org/wiki/Lord_of_Chios\" class=\"mw-redirect\" title=\"Lord of Chios\">Lord of Chios</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Genoese <a href=\"https://wikipedia.org/wiki/Lord_of_Chios\" class=\"mw-redirect\" title=\"Lord of Chios\">Lord of Chios</a>", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Martino_Z<PERSON>car<PERSON>"}, {"title": "Lord of Chios", "link": "https://wikipedia.org/wiki/Lord_of_Chios"}]}, {"year": "1369", "text": "<PERSON> of Cyprus (b. 1328)", "html": "1369 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Cyprus\" title=\"<PERSON> of Cyprus\"><PERSON> of Cyprus</a> (b. 1328)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Cyprus\" title=\"<PERSON> of Cyprus\"><PERSON> of Cyprus</a> (b. 1328)", "links": [{"title": "<PERSON> of Cyprus", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Cyprus"}]}, {"year": "1456", "text": "<PERSON> of Lorraine<PERSON><PERSON>, French translator (b. 1395)", "html": "1456 - <a href=\"https://wikipedia.org/wiki/<PERSON>_of_Lorraine-Vaud%C3%A9mont\" title=\"<PERSON> of Lorraine-Vaudémont\"><PERSON> of Lorraine-Vaudémont</a>, French translator (b. 1395)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_of_Lorraine-Vaud%C3%A9<PERSON>\" title=\"<PERSON> of Lorraine-Vaudémont\"><PERSON> of Lorraine-Vaudémont</a>, French translator (b. 1395)", "links": [{"title": "Elisabeth of Lorraine-Vaudémont", "link": "https://wikipedia.org/wiki/Elisabeth_of_Lorraine-Vaud%C3%A9mont"}]}, {"year": "1468", "text": "<PERSON><PERSON><PERSON><PERSON><PERSON>, Albanian soldier and politician (b. 1405)", "html": "1468 - <a href=\"https://wikipedia.org/wiki/Skanderbeg\" title=\"Skanderbeg\"><PERSON><PERSON><PERSON><PERSON><PERSON></a>, Albanian soldier and politician (b. 1405)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Skanderbeg\" title=\"Skanderbeg\"><PERSON><PERSON><PERSON><PERSON><PERSON></a>, Albanian soldier and politician (b. 1405)", "links": [{"title": "Skanderbeg", "link": "https://wikipedia.org/wiki/Skanderbeg"}]}, {"year": "1523", "text": "<PERSON> of Hesse-Marburg, German landgravine (b. 1466)", "html": "1523 - <a href=\"https://wikipedia.org/wiki/<PERSON>_of_Hesse-Marburg\" title=\"Elisabeth of Hesse-Marburg\"><PERSON> of Hesse-Marburg</a>, German landgravine (b. 1466)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_of_Hesse-Marburg\" title=\"Elisabeth of Hesse-Marburg\"><PERSON> of Hesse-Marburg</a>, German landgravine (b. 1466)", "links": [{"title": "Elisabeth of Hesse-Marburg", "link": "https://wikipedia.org/wiki/Elisabeth_<PERSON>_Hesse-Marburg"}]}, {"year": "1588", "text": "<PERSON>, Chinese general (b. 1528)", "html": "1588 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Chinese general (b. 1528)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Chinese general (b. 1528)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Qi_<PERSON>ng"}]}, {"year": "1598", "text": "<PERSON><PERSON><PERSON> of Russia (b. 1557)", "html": "1598 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_of_Russia\" title=\"<PERSON><PERSON><PERSON> <PERSON> of Russia\"><PERSON><PERSON><PERSON> of Russia</a> (b. 1557)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_of_Russia\" title=\"<PERSON><PERSON><PERSON> of Russia\"><PERSON><PERSON><PERSON> of Russia</a> (b. 1557)", "links": [{"title": "<PERSON><PERSON><PERSON> of Russia", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_of_Russia"}]}, {"year": "1617", "text": "<PERSON><PERSON>, Croatian bishop and lexicographer (b. 1551)", "html": "1617 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Croatian bishop and lexicographer (b. 1551)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Croatian bishop and lexicographer (b. 1551)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1705", "text": "<PERSON>, English botanist and historian (b. 1627)", "html": "1705 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English botanist and historian (b. 1627)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English botanist and historian (b. 1627)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1718", "text": "<PERSON>, American colonel (b. 1639)", "html": "1718 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(ranger)\" title=\"<PERSON> (ranger)\"><PERSON></a>, American colonel (b. 1639)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(ranger)\" title=\"<PERSON> (ranger)\"><PERSON></a>, American colonel (b. 1639)", "links": [{"title": "<PERSON> (ranger)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(ranger)"}]}, {"year": "1737", "text": "<PERSON><PERSON><PERSON><PERSON>, German architect (b. 1662)", "html": "1737 - <a href=\"https://wikipedia.org/wiki/Matth%C3%A<PERSON><PERSON>_<PERSON>_P%C3%B<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON> <PERSON></a>, German architect (b. 1662)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Matth%C3%A<PERSON><PERSON>_<PERSON>_<PERSON>%C3%B<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON> <PERSON></a>, German architect (b. 1662)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Matth%C3%A4<PERSON>_<PERSON>_P%C3%B6<PERSON><PERSON>"}]}, {"year": "1738", "text": "<PERSON><PERSON><PERSON>, French organist and composer (b. 1682)", "html": "1738 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>%C3%A7<PERSON>_Dan<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, French organist and composer (b. 1682)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>%C3%A7<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, French organist and composer (b. 1682)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>-<PERSON><PERSON>%C3%A<PERSON><PERSON>_<PERSON>"}]}, {"year": "1751", "text": "<PERSON><PERSON>, Italian violinist and composer (b. 1671)", "html": "1751 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Italian violinist and composer (b. 1671)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Italian violinist and composer (b. 1671)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1826", "text": "<PERSON>, Spanish-French composer (b. 1806)", "html": "1826 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%B3stomo_A<PERSON>aga\" title=\"<PERSON>\"><PERSON></a>, Spanish-French composer (b. 1806)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%B3stomo_Arriaga\" title=\"<PERSON>\"><PERSON></a>, Spanish-French composer (b. 1806)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>%C3%B3stomo_A<PERSON>aga"}]}, {"year": "1834", "text": "<PERSON>, Italian physicist and academic (b. 1762)", "html": "1834 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian physicist and academic (b. 1762)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian physicist and academic (b. 1762)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1850", "text": "<PERSON>, English-Canadian painter and author (b. 1762)", "html": "1850 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English-Canadian painter and author (b. 1762)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English-Canadian painter and author (b. 1762)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1861", "text": "<PERSON>, Irish actress and dancer (b. 1821)", "html": "1861 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Irish actress and dancer (b. 1821)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Irish actress and dancer (b. 1821)", "links": [{"title": "Lola Montez", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1863", "text": "<PERSON>, French painter (b. 1789)", "html": "1863 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French painter (b. 1789)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French painter (b. 1789)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1869", "text": "<PERSON>, Russian composer (b. 1813)", "html": "1869 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian composer (b. 1813)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian composer (b. 1813)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1878", "text": "<PERSON>, English historian and jurist (b. 1812)", "html": "1878 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English historian and jurist (b. 1812)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English historian and jurist (b. 1812)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1884", "text": "<PERSON>, German ornithologist and herpetologist (b. 1804)", "html": "1884 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German ornithologist and herpetologist (b. 1804)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German ornithologist and herpetologist (b. 1804)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1887", "text": "<PERSON>, Australian lawyer and politician, 13th Premier of Tasmania (b. 1840)", "html": "1887 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian lawyer and politician, 13th <a href=\"https://wikipedia.org/wiki/Premier_of_Tasmania\" title=\"Premier of Tasmania\">Premier of Tasmania</a> (b. 1840)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian lawyer and politician, 13th <a href=\"https://wikipedia.org/wiki/Premier_of_Tasmania\" title=\"Premier of Tasmania\">Premier of Tasmania</a> (b. 1840)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Premier of Tasmania", "link": "https://wikipedia.org/wiki/Premier_of_Tasmania"}]}, {"year": "1888", "text": "<PERSON> <PERSON>, Canadian tribal chief (b. 1825)", "html": "1888 - <a href=\"https://wikipedia.org/wiki/Big_Bear\" title=\"Big Bear\">Big Bear</a>, Canadian tribal chief (b. 1825)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Big_Bear\" title=\"Big Bear\">Big Bear</a>, Canadian tribal chief (b. 1825)", "links": [{"title": "Big Bear", "link": "https://wikipedia.org/wiki/<PERSON>_Bear"}]}, {"year": "1891", "text": "<PERSON>, American historian and politician, 17th United States Secretary of the Navy (b. 1800)", "html": "1891 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American historian and politician, 17th <a href=\"https://wikipedia.org/wiki/United_States_Secretary_of_the_Navy\" title=\"United States Secretary of the Navy\">United States Secretary of the Navy</a> (b. 1800)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American historian and politician, 17th <a href=\"https://wikipedia.org/wiki/United_States_Secretary_of_the_Navy\" title=\"United States Secretary of the Navy\">United States Secretary of the Navy</a> (b. 1800)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "United States Secretary of the Navy", "link": "https://wikipedia.org/wiki/United_States_Secretary_of_the_Navy"}]}, {"year": "1893", "text": "<PERSON>, American general, lawyer, and politician, 19th President of the United States (b. 1822)", "html": "1893 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American general, lawyer, and politician, 19th <a href=\"https://wikipedia.org/wiki/President_of_the_United_States\" title=\"President of the United States\">President of the United States</a> (b. 1822)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American general, lawyer, and politician, 19th <a href=\"https://wikipedia.org/wiki/President_of_the_United_States\" title=\"President of the United States\">President of the United States</a> (b. 1822)", "links": [{"title": "<PERSON> B<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>"}, {"title": "President of the United States", "link": "https://wikipedia.org/wiki/President_of_the_United_States"}]}, {"year": "1896", "text": "<PERSON>, Baroness <PERSON>, Welsh writer and patron of the arts (b. 1802)", "html": "1896 - <a href=\"https://wikipedia.org/wiki/Augusta_Hall,_Baroness_<PERSON>\" title=\"Augusta Hall, Baroness <PERSON>\"><PERSON>, Baroness <PERSON></a>, Welsh writer and patron of the arts (b. 1802)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Augusta_Hall,_Baroness_<PERSON>\" title=\"Augusta Hall, Baroness <PERSON>\"><PERSON>, Baroness <PERSON></a>, Welsh writer and patron of the arts (b. 1802)", "links": [{"title": "<PERSON>, <PERSON>", "link": "https://wikipedia.org/wiki/Augusta_Hall,_<PERSON>_<PERSON>"}]}, {"year": "1903", "text": "<PERSON><PERSON><PERSON>, Hungarian architect and philanthropist (b. 1828)", "html": "1903 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Hungarian architect and philanthropist (b. 1828)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Hungarian architect and philanthropist (b. 1828)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON><PERSON>"}]}, {"year": "1908", "text": "<PERSON>, Grand Duke of Tuscany (b. 1835)", "html": "1908 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Grand_Duke_of_Tuscany\" title=\"<PERSON>, Grand Duke of Tuscany\"><PERSON>, Grand Duke of Tuscany</a> (b. 1835)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Grand_Duke_of_Tuscany\" title=\"<PERSON>, Grand Duke of Tuscany\"><PERSON>, Grand Duke of Tuscany</a> (b. 1835)", "links": [{"title": "<PERSON>, Grand Duke of Tuscany", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Grand_Duke_of_Tuscany"}]}, {"year": "1909", "text": "<PERSON><PERSON><PERSON>, Finnish politician and journalist (b. 1826)", "html": "1909 - <a href=\"https://wikipedia.org/wiki/Agat<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON> Me<PERSON>man\"><PERSON><PERSON><PERSON></a>, Finnish politician and journalist (b. 1826)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Agat<PERSON>_<PERSON>\" title=\"A<PERSON><PERSON> Me<PERSON>man\"><PERSON><PERSON><PERSON></a>, Finnish politician and journalist (b. 1826)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Agat<PERSON>_<PERSON><PERSON>"}]}, {"year": "1909", "text": "<PERSON>, Australian lawyer, judge, and politician, 4th Premier of Tasmania (b. 1819)", "html": "1909 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(Australian_politician)\" title=\"<PERSON> (Australian politician)\"><PERSON></a>, Australian lawyer, judge, and politician, 4th <a href=\"https://wikipedia.org/wiki/Premier_of_Tasmania\" title=\"Premier of Tasmania\">Premier of Tasmania</a> (b. 1819)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(Australian_politician)\" title=\"<PERSON> (Australian politician)\"><PERSON></a>, Australian lawyer, judge, and politician, 4th <a href=\"https://wikipedia.org/wiki/Premier_of_Tasmania\" title=\"Premier of Tasmania\">Premier of Tasmania</a> (b. 1819)", "links": [{"title": "<PERSON> (Australian politician)", "link": "https://wikipedia.org/wiki/<PERSON>_(Australian_politician)"}, {"title": "Premier of Tasmania", "link": "https://wikipedia.org/wiki/Premier_of_Tasmania"}]}, {"year": "1911", "text": "<PERSON>, English polymath, anthropologist, and geographer (b. 1822)", "html": "1911 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English polymath, anthropologist, and geographer (b. 1822)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English polymath, anthropologist, and geographer (b. 1822)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1927", "text": "<PERSON>, American founder of the Girl Scouts of the USA (b. 1860)", "html": "1927 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American founder of the <a href=\"https://wikipedia.org/wiki/Girl_Scouts_of_the_USA\" title=\"Girl Scouts of the USA\">Girl Scouts of the USA</a> (b. 1860)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American founder of the <a href=\"https://wikipedia.org/wiki/Girl_Scouts_of_the_USA\" title=\"Girl Scouts of the USA\">Girl Scouts of the USA</a> (b. 1860)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "Girl Scouts of the USA", "link": "https://wikipedia.org/wiki/Girl_Scouts_of_the_USA"}]}, {"year": "1930", "text": "<PERSON><PERSON><PERSON>, One of the first performers to record music on 78 rpm records in India. (b. 1873)", "html": "1930 - <a href=\"https://wikipedia.org/wiki/Gau<PERSON>_<PERSON>\" title=\"Gauhar J<PERSON>\"><PERSON><PERSON><PERSON></a>, One of the first performers to record music on 78 rpm records in India. (b. 1873)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Gau<PERSON>_<PERSON>\" title=\"Gauhar Jaan\"><PERSON><PERSON><PERSON></a>, One of the first performers to record music on 78 rpm records in India. (b. 1873)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Gau<PERSON>_<PERSON>aan"}]}, {"year": "1931", "text": "Grand Duke <PERSON> of Russia (b. 1864)", "html": "1931 - <a href=\"https://wikipedia.org/wiki/Grand_Duke_<PERSON>_<PERSON>_of_Russia\" title=\"Grand Duke <PERSON> of Russia\">Grand Duke <PERSON> of Russia</a> (b. 1864)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Grand_Duke_<PERSON>_<PERSON>_of_Russia\" title=\"Grand Duke <PERSON> of Russia\">Grand Duke <PERSON> of Russia</a> (b. 1864)", "links": [{"title": "Grand Duke <PERSON> of Russia", "link": "https://wikipedia.org/wiki/Grand_Duke_<PERSON>_<PERSON>_of_Russia"}]}, {"year": "1932", "text": "<PERSON><PERSON>, Turkish general (b. 1881)", "html": "1932 - <a href=\"https://wikipedia.org/wiki/Ah<PERSON>_Dervi%C5%9F\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Turkish general (b. 1881)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Ahmet_Dervi%C5%9F\" title=\"<PERSON><PERSON> Derviş\"><PERSON><PERSON></a>, Turkish general (b. 1881)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Ahmet_Dervi%C5%9F"}]}, {"year": "1932", "text": "<PERSON>, Australian captain, Victoria Cross recipient (b. 1893)", "html": "1932 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian captain, <a href=\"https://wikipedia.org/wiki/Victoria_Cross\" title=\"Victoria Cross\">Victoria Cross</a> recipient (b. 1893)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian captain, <a href=\"https://wikipedia.org/wiki/Victoria_Cross\" title=\"Victoria Cross\">Victoria Cross</a> recipient (b. 1893)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Victoria Cross", "link": "https://wikipedia.org/wiki/Victoria_Cross"}]}, {"year": "1933", "text": "<PERSON>, American stained glass artist (b. 1848)", "html": "1933 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>fort_Tiffany\" title=\"Louis Comfort Tiffany\"><PERSON></a>, American <a href=\"https://wikipedia.org/wiki/Stained_glass\" title=\"Stained glass\">stained glass</a> artist (b. 1848)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"Louis Comfort Tiffany\"><PERSON></a>, American <a href=\"https://wikipedia.org/wiki/Stained_glass\" title=\"Stained glass\">stained glass</a> artist (b. 1848)", "links": [{"title": "Louis <PERSON>", "link": "https://wikipedia.org/wiki/Louis_Comfort_Tiffany"}, {"title": "Stained glass", "link": "https://wikipedia.org/wiki/Stained_glass"}]}, {"year": "1936", "text": "<PERSON><PERSON><PERSON>, Romanian journalist, author, and poet (b. 1885)", "html": "1936 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Romanian journalist, author, and poet (b. 1885)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Romanian journalist, author, and poet (b. 1885)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>giale"}]}, {"year": "1942", "text": "<PERSON><PERSON>, German field marshal (b. 1884)", "html": "1942 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, German field marshal (b. 1884)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, German field marshal (b. 1884)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1947", "text": "<PERSON><PERSON><PERSON>, Russian historian and general (b. 1869)", "html": "1947 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Russian historian and general (b. 1869)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Russian historian and general (b. 1869)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1947", "text": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, Canadian cardinal (b. 1883)", "html": "1947 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Canadian cardinal (b. 1883)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Canadian cardinal (b. 1883)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>-<PERSON>_<PERSON>"}]}, {"year": "1951", "text": "<PERSON><PERSON><PERSON>, Indian poet, playwright, and director (b. 1903)", "html": "1951 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Indian poet, playwright, and director (b. 1903)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Indian poet, playwright, and director (b. 1903)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1952", "text": "<PERSON>, American businessman (b. 1877)", "html": "1952 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON>.</a>, American businessman (b. 1877)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>.\" title=\"<PERSON>\"><PERSON>.</a>, American businessman (b. 1877)", "links": [{"title": "<PERSON>.", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>."}]}, {"year": "1961", "text": "<PERSON><PERSON>, Congolese politician, 1st Prime Minister of the Democratic Republic of the Congo (b. 1925)", "html": "1961 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Congolese politician, 1st <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_the_Democratic_Republic_of_the_Congo\" title=\"Prime Minister of the Democratic Republic of the Congo\">Prime Minister of the Democratic Republic of the Congo</a> (b. 1925)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Congolese politician, 1st <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_the_Democratic_Republic_of_the_Congo\" title=\"Prime Minister of the Democratic Republic of the Congo\">Prime Minister of the Democratic Republic of the Congo</a> (b. 1925)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}, {"title": "Prime Minister of the Democratic Republic of the Congo", "link": "https://wikipedia.org/wiki/Prime_Minister_of_the_Democratic_Republic_of_the_Congo"}]}, {"year": "1970", "text": "<PERSON>, Russian-American bassoon player and educator (b. 1890)", "html": "1970 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian-American <a href=\"https://wikipedia.org/wiki/Bassoon\" title=\"Bassoon\">bassoon</a> player and educator (b. 1890)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian-American <a href=\"https://wikipedia.org/wiki/Bassoon\" title=\"Bassoon\">bassoon</a> player and educator (b. 1890)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}, {"title": "Bassoon", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>"}]}, {"year": "1970", "text": "<PERSON>, American rhythm and blues singer and pianist (b. 1937)", "html": "1970 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American rhythm and blues singer and pianist (b. 1937)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American rhythm and blues singer and pianist (b. 1937)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1972", "text": "<PERSON>, American author and playwright (b. 1896)", "html": "1972 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author and playwright (b. 1896)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author and playwright (b. 1896)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1977", "text": "<PERSON><PERSON>, Scottish mountaineer (b. 1940)", "html": "1977 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Scottish mountaineer (b. 1940)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Scottish mountaineer (b. 1940)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1977", "text": "<PERSON>, American murderer (b. 1940)", "html": "1977 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American murderer (b. 1940)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American murderer (b. 1940)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1981", "text": "<PERSON><PERSON>, Greek footballer and lawyer (b. 1899)", "html": "1981 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_Panourgias\" title=\"<PERSON>kas Panourgias\"><PERSON><PERSON></a>, Greek footballer and lawyer (b. 1899)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_Panourgias\" title=\"Loukas Panourgias\"><PERSON><PERSON></a>, Greek footballer and lawyer (b. 1899)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Loukas_Panourgias"}]}, {"year": "1984", "text": "<PERSON><PERSON><PERSON>, Greek pianist, composer, and conductor (b. 1903)", "html": "1984 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Greek pianist, composer, and conductor (b. 1903)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Greek pianist, composer, and conductor (b. 1903)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1987", "text": "<PERSON>, Argentinian director and screenwriter (b. 1908)", "html": "1987 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Argentinian director and screenwriter (b. 1908)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Argentinian director and screenwriter (b. 1908)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1987", "text": "<PERSON>, American psychologist and author (b. 1927)", "html": "1987 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American psychologist and author (b. 1927)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American psychologist and author (b. 1927)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1988", "text": "<PERSON>, South African journalist and author (b. 1938)", "html": "1988 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, South African journalist and author (b. 1938)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, South African journalist and author (b. 1938)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1991", "text": "<PERSON><PERSON> (b. 1903)", "html": "1991 - <a href=\"https://wikipedia.org/wiki/Olav_V_of_Norway\" class=\"mw-redirect\" title=\"<PERSON>lav V of Norway\"><PERSON><PERSON> of Norway</a> (b. 1903)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Olav_V_of_Norway\" class=\"mw-redirect\" title=\"<PERSON>lav V of Norway\"><PERSON><PERSON> of Norway</a> (b. 1903)", "links": [{"title": "<PERSON><PERSON> of <PERSON>", "link": "https://wikipedia.org/wiki/Olav_V_of_Norway"}]}, {"year": "1992", "text": "<PERSON>, English soldier and businessman (b. 1915)", "html": "1992 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English soldier and businessman (b. 1915)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English soldier and businessman (b. 1915)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1993", "text": "<PERSON>, English-Lebanese historian and academic (b. 1915)", "html": "1993 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English-Lebanese historian and academic (b. 1915)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English-Lebanese historian and academic (b. 1915)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1994", "text": "<PERSON><PERSON><PERSON><PERSON>, Russian spy (b. 1926)", "html": "1994 - <a href=\"https://wikipedia.org/wiki/Ye<PERSON><PERSON>_<PERSON>_(spy)\" title=\"<PERSON><PERSON><PERSON> (spy)\"><PERSON><PERSON><PERSON><PERSON></a>, Russian spy (b. 1926)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Ye<PERSON><PERSON>_<PERSON>_(spy)\" title=\"<PERSON><PERSON><PERSON> (spy)\"><PERSON><PERSON><PERSON><PERSON></a>, Russian spy (b. 1926)", "links": [{"title": "<PERSON><PERSON><PERSON> (spy)", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_(spy)"}]}, {"year": "1994", "text": "<PERSON>, American runner, shot putter, and discus thrower (b. 1918)", "html": "1994 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American runner, shot putter, and discus thrower (b. 1918)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American runner, shot putter, and discus thrower (b. 1918)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1996", "text": "<PERSON>, American lawyer and politician (b. 1936)", "html": "1996 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and politician (b. 1936)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and politician (b. 1936)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Barbara_Jordan"}]}, {"year": "1996", "text": "<PERSON>, English geneticist (b. 1922)", "html": "1996 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English geneticist (b. 1922)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English geneticist (b. 1922)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1997", "text": "<PERSON>, Australian farmer and politician, 20th Australian Minister for the Navy (b. 1912)", "html": "1997 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian farmer and politician, 20th <a href=\"https://wikipedia.org/wiki/Minister_for_Defence_(Australia)\" title=\"Minister for Defence (Australia)\">Australian Minister for the Navy</a> (b. 1912)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian farmer and politician, 20th <a href=\"https://wikipedia.org/wiki/Minister_for_Defence_(Australia)\" title=\"Minister for Defence (Australia)\">Australian Minister for the Navy</a> (b. 1912)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Minister for Defence (Australia)", "link": "https://wikipedia.org/wiki/Minister_for_Defence_(Australia)"}]}, {"year": "1997", "text": "<PERSON>, American astronomer and academic, discovered <PERSON><PERSON><PERSON> (b. 1906)", "html": "1997 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American astronomer and academic, discovered <a href=\"https://wikipedia.org/wiki/Pluto\" title=\"Pluto\">Pluto</a> (b. 1906)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American astronomer and academic, discovered <a href=\"https://wikipedia.org/wiki/Pluto\" title=\"Pluto\">Pluto</a> (b. 1906)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Pluto", "link": "https://wikipedia.org/wiki/Pluto"}]}, {"year": "2000", "text": "<PERSON>, English trumpet player and educator (b. 1928)", "html": "2000 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(musician)\" title=\"<PERSON> (musician)\"><PERSON></a>, English trumpet player and educator (b. 1928)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(musician)\" title=\"<PERSON> (musician)\"><PERSON></a>, English trumpet player and educator (b. 1928)", "links": [{"title": "<PERSON> (musician)", "link": "https://wikipedia.org/wiki/<PERSON>_(musician)"}]}, {"year": "2000", "text": "<PERSON>, Romanian journalist and politician (b. 1917)", "html": "2000 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C8%9Biu\" title=\"<PERSON>\"><PERSON></a>, Romanian journalist and politician (b. 1917)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C8%9Biu\" title=\"<PERSON>\"><PERSON></a>, Romanian journalist and politician (b. 1917)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Ion_Ra%C8%9Biu"}]}, {"year": "2002", "text": "<PERSON><PERSON>, Spanish author and politician, Nobel Prize laureate (b. 1916)", "html": "2002 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>%C3%A9_<PERSON><PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Spanish author and politician, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Literature\" title=\"Nobel Prize in Literature\">Nobel Prize</a> laureate (b. 1916)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>%C3%A9_<PERSON><PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Spanish author and politician, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Literature\" title=\"Nobel Prize in Literature\">Nobel Prize</a> laureate (b. 1916)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Camilo_Jos%C3%A9_Cela"}, {"title": "Nobel Prize in Literature", "link": "https://wikipedia.org/wiki/Nobel_Prize_in_Literature"}]}, {"year": "2002", "text": "<PERSON>, Russian physicist and academic (b. 1932)", "html": "2002 - <a href=\"https://wikipedia.org/wiki/<PERSON>_Personov\" title=\"Roman Personov\"><PERSON></a>, Russian physicist and academic (b. 1932)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_Personov\" title=\"Roman Personov\"><PERSON></a>, Russian physicist and academic (b. 1932)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>ov"}]}, {"year": "2003", "text": "<PERSON>, American actor and director (b. 1926)", "html": "2003 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor and director (b. 1926)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor and director (b. 1926)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2004", "text": "<PERSON>, English banker (b. 1929)", "html": "2004 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English banker (b. 1929)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English banker (b. 1929)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "2004", "text": "<PERSON>, American baseball player and coach (b. 1914)", "html": "2004 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player and coach (b. 1914)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player and coach (b. 1914)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2004", "text": "<PERSON>, American film producer (b. 1915)", "html": "2004 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American film producer (b. 1915)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American film producer (b. 1915)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2004", "text": "<PERSON>, American actor (b. 1931)", "html": "2004 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor (b. 1931)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor (b. 1931)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2005", "text": "<PERSON>, Australian businessman (b. 1960)", "html": "2005 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(businessman)\" title=\"<PERSON> (businessman)\"><PERSON></a>, Australian businessman (b. 1960)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(businessman)\" title=\"<PERSON> (businessman)\"><PERSON></a>, Australian businessman (b. 1960)", "links": [{"title": "<PERSON> (businessman)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(businessman)"}]}, {"year": "2005", "text": "<PERSON>, American actress, singer, and dancer (b. 1920)", "html": "2005 - <a href=\"https://wikipedia.org/wiki/Virginia_Mayo\" title=\"Virginia Mayo\"><PERSON> Mayo</a>, American actress, singer, and dancer (b. 1920)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Virginia_Mayo\" title=\"Virginia Mayo\">Virginia Mayo</a>, American actress, singer, and dancer (b. 1920)", "links": [{"title": "Virginia Mayo", "link": "https://wikipedia.org/wiki/Virginia_Mayo"}]}, {"year": "2005", "text": "<PERSON>, American microbiologist and academic (b. 1920)", "html": "2005 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(scientist)\" title=\"<PERSON> (scientist)\"><PERSON></a>, American microbiologist and academic (b. 1920)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(scientist)\" title=\"<PERSON> (scientist)\"><PERSON></a>, American microbiologist and academic (b. 1920)", "links": [{"title": "<PERSON> (scientist)", "link": "https://wikipedia.org/wiki/<PERSON>_(scientist)"}]}, {"year": "2005", "text": "<PERSON>, Chinese politician, 3rd Premier of the People's Republic of China (b. 1919)", "html": "2005 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Chinese politician, 3rd <a href=\"https://wikipedia.org/wiki/Premier_of_the_People%27s_Republic_of_China\" class=\"mw-redirect\" title=\"Premier of the People's Republic of China\">Premier of the People's Republic of China</a> (b. 1919)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Chinese politician, 3rd <a href=\"https://wikipedia.org/wiki/Premier_of_the_People%27s_Republic_of_China\" class=\"mw-redirect\" title=\"Premier of the People's Republic of China\">Premier of the People's Republic of China</a> (b. 1919)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Premier of the People's Republic of China", "link": "https://wikipedia.org/wiki/Premier_of_the_People%27s_Republic_of_China"}]}, {"year": "2006", "text": "<PERSON>, Canadian surgeon (b. 1925)", "html": "2006 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian surgeon (b. 1925)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian surgeon (b. 1925)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2007", "text": "<PERSON>, American journalist and author (b. 1925)", "html": "2007 - <a href=\"https://wikipedia.org/wiki/Art_B<PERSON>wald\" title=\"<PERSON> Buchwald\"><PERSON></a>, American journalist and author (b. 1925)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Art_Buchwald\" title=\"<PERSON> Buchwald\"><PERSON></a>, American journalist and author (b. 1925)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Art_Buchwald"}]}, {"year": "2007", "text": "<PERSON><PERSON><PERSON>, Ukrainian engineer and politician (b. 1951)", "html": "2007 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Ukrainian engineer and politician (b. 1951)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Ukrainian engineer and politician (b. 1951)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "2007", "text": "<PERSON><PERSON>, German record producer, journalist and film critic (b. 1940)", "html": "2007 - <a href=\"https://wikipedia.org/wiki/<PERSON>we_Nettelbeck\" title=\"Uwe Nettelbeck\"><PERSON><PERSON></a>, German record producer, journalist and film critic (b. 1940)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>we_Nettelbeck\" title=\"<PERSON>we Nettelbeck\"><PERSON><PERSON></a>, German record producer, journalist and film critic (b. 1940)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Uwe_Nettelbeck"}]}, {"year": "2008", "text": "<PERSON>, American chess player and author (b. 1943)", "html": "2008 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American chess player and author (b. 1943)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American chess player and author (b. 1943)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2008", "text": "<PERSON>, American football player, wrestler, and actor (b. 1948)", "html": "2008 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player, wrestler, and actor (b. 1948)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player, wrestler, and actor (b. 1948)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2009", "text": "<PERSON>, Swedish journalist and historian (b. 1943)", "html": "2009 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swedish journalist and historian (b. 1943)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swedish journalist and historian (b. 1943)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2010", "text": "<PERSON><PERSON>, American football player (b. 1983)", "html": "2010 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American football player (b. 1983)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American football player (b. 1983)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "2010", "text": "<PERSON><PERSON><PERSON>, Indian politician and 9th Chief Minister of West Bengal (b. 1914)", "html": "2010 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Indian politician and 9th <a href=\"https://wikipedia.org/wiki/Chief_Minister_of_West_Bengal\" class=\"mw-redirect\" title=\"Chief Minister of West Bengal\">Chief Minister of West Bengal</a> (b. 1914)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Indian politician and 9th <a href=\"https://wikipedia.org/wiki/Chief_Minister_of_West_Bengal\" class=\"mw-redirect\" title=\"Chief Minister of West Bengal\">Chief Minister of West Bengal</a> (b. 1914)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON><PERSON>"}, {"title": "Chief Minister of West Bengal", "link": "https://wikipedia.org/wiki/Chief_Minister_of_West_Bengal"}]}, {"year": "2010", "text": "<PERSON><PERSON><PERSON>, Greek journalist and politician, Foreign Minister of Greece (b. 1919)", "html": "2010 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Greek journalist and politician, <a href=\"https://wikipedia.org/wiki/Minister_for_Foreign_Affairs_(Greece)\" class=\"mw-redirect\" title=\"Minister for Foreign Affairs (Greece)\">Foreign Minister of Greece</a> (b. 1919)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Greek journalist and politician, <a href=\"https://wikipedia.org/wiki/Minister_for_Foreign_Affairs_(Greece)\" class=\"mw-redirect\" title=\"Minister for Foreign Affairs (Greece)\">Foreign Minister of Greece</a> (b. 1919)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>stantino<PERSON>"}, {"title": "Minister for Foreign Affairs (Greece)", "link": "https://wikipedia.org/wiki/Minister_for_Foreign_Affairs_(Greece)"}]}, {"year": "2010", "text": "<PERSON>, American author and screenwriter (b. 1937)", "html": "2010 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author and screenwriter (b. 1937)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author and screenwriter (b. 1937)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2011", "text": "<PERSON>, American songwriter and producer (b. 1934)", "html": "2011 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American songwriter and producer (b. 1934)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American songwriter and producer (b. 1934)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2012", "text": "<PERSON>, German soldier and pilot (b. 1917)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German soldier and pilot (b. 1917)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German soldier and pilot (b. 1917)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2012", "text": "<PERSON>, American singer-songwriter and producer (b. 1921)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and producer (b. 1921)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Otis\"><PERSON></a>, American singer-songwriter and producer (b. 1921)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2012", "text": "<PERSON>, American baseball player and umpire (b. 1937)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player and umpire (b. 1937)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player and umpire (b. 1937)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2013", "text": "<PERSON><PERSON><PERSON>, Turkish journalist and author (b. 1941)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Turkish journalist and author (b. 1941)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Turkish journalist and author (b. 1941)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>"}]}, {"year": "2013", "text": "<PERSON>, German author (b. 1964)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German author (b. 1964)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German author (b. 1964)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2013", "text": "<PERSON>, Belgian journalist (b. 1954)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Belgian journalist (b. 1954)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Belgian journalist (b. 1954)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2013", "text": "<PERSON>, Zimbabwean politician, Vice President of Zimbabwe (b. 1934)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Zimbabwean politician, <a href=\"https://wikipedia.org/wiki/Vice_President_of_Zimbabwe\" class=\"mw-redirect\" title=\"Vice President of Zimbabwe\">Vice President of Zimbabwe</a> (b. 1934)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Zimbabwean politician, <a href=\"https://wikipedia.org/wiki/Vice_President_of_Zimbabwe\" class=\"mw-redirect\" title=\"Vice President of Zimbabwe\">Vice President of Zimbabwe</a> (b. 1934)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Vice President of Zimbabwe", "link": "https://wikipedia.org/wiki/Vice_President_of_Zimbabwe"}]}, {"year": "2013", "text": "<PERSON><PERSON>, English soprano and actress (b. 1926)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, English soprano and actress (b. 1926)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, English soprano and actress (b. 1926)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "2014", "text": "<PERSON><PERSON>, Indian spiritual leader, 52nd <PERSON><PERSON><PERSON> (b. 1915)", "html": "2014 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Indian spiritual leader, 52nd <a href=\"https://wikipedia.org/wiki/Da%27i_al-Mutl<PERSON>\" title=\"<PERSON><PERSON><PERSON> al-Mutl<PERSON>\"><PERSON><PERSON><PERSON> <PERSON><PERSON>tl<PERSON></a> (b. 1915)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Indian spiritual leader, 52nd <a href=\"https://wikipedia.org/wiki/Da%27i_al-<PERSON>tl<PERSON>\" title=\"<PERSON><PERSON><PERSON> al-Mutl<PERSON>\"><PERSON><PERSON><PERSON> <PERSON><PERSON></a> (b. 1915)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>"}, {"title": "<PERSON><PERSON><PERSON> al-<PERSON>", "link": "https://wikipedia.org/wiki/Da%27i_<PERSON>-<PERSON>"}]}, {"year": "2014", "text": "<PERSON><PERSON><PERSON>, Canadian educator and politician (b. 1940)", "html": "2014 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Canadian educator and politician (b. 1940)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Canadian educator and politician (b. 1940)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>an<PERSON>_<PERSON>"}]}, {"year": "2014", "text": "<PERSON>, Baron <PERSON> of West Green, English businessman and politician (b. 1942)", "html": "2014 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Baron_<PERSON>_of_West_Green\" title=\"<PERSON>, Baron <PERSON> of West Green\"><PERSON>, Baron <PERSON> of West Green</a>, English businessman and politician (b. 1942)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>,_Baron_<PERSON>_of_West_Green\" title=\"<PERSON>, Baron <PERSON> of West Green\"><PERSON>, Baron <PERSON> of West Green</a>, English businessman and politician (b. 1942)", "links": [{"title": "<PERSON>, Baron <PERSON> of West Green", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_<PERSON>_<PERSON>_of_West_Green"}]}, {"year": "2014", "text": "<PERSON>, American captain, Medal of Honor recipient (b. 1940)", "html": "2014 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>inty_III\" title=\"<PERSON> III\"><PERSON></a>, American captain, <a href=\"https://wikipedia.org/wiki/Medal_of_Honor\" title=\"Medal of Honor\">Medal of Honor</a> recipient (b. 1940)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>inty_III\" title=\"<PERSON> III\"><PERSON></a>, American captain, <a href=\"https://wikipedia.org/wiki/Medal_of_Honor\" title=\"Medal of Honor\">Medal of Honor</a> recipient (b. 1940)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON><PERSON><PERSON>_<PERSON>"}, {"title": "Medal of Honor", "link": "https://wikipedia.org/wiki/Medal_of_Honor"}]}, {"year": "2014", "text": "<PERSON><PERSON>, Indian-Canadian businesswoman (b. 1962)", "html": "2014 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Indian-Canadian businesswoman (b. 1962)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Indian-Canadian businesswoman (b. 1962)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "2014", "text": "<PERSON><PERSON><PERSON>, Indian film actress (b. 1931)", "html": "2014 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Indian film actress (b. 1931)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Indian film actress (b. 1931)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Suchitra_Sen"}]}, {"year": "2015", "text": "<PERSON>, English footballer and manager (b. 1931)", "html": "2015 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English footballer and manager (b. 1931)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English footballer and manager (b. 1931)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2015", "text": "<PERSON><PERSON>, Egyptian actress and producer (b. 1931)", "html": "2015 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Egyptian actress and producer (b. 1931)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Egyptian actress and producer (b. 1931)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "2015", "text": "<PERSON>, Canadian actor and screenwriter (b. 1924)", "html": "2015 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian actor and screenwriter (b. 1924)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian actor and screenwriter (b. 1924)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2016", "text": "<PERSON><PERSON><PERSON>, American singer-songwriter and producer (b. 1939)", "html": "2016 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_(musician)\" title=\"<PERSON><PERSON><PERSON> (musician)\"><PERSON><PERSON><PERSON></a>, American singer-songwriter and producer (b. 1939)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_(musician)\" title=\"<PERSON><PERSON><PERSON> (musician)\"><PERSON><PERSON><PERSON></a>, American singer-songwriter and producer (b. 1939)", "links": [{"title": "<PERSON><PERSON><PERSON> (musician)", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_(musician)"}]}, {"year": "2016", "text": "<PERSON>, New Zealand painter and historian (b. 1923)", "html": "2016 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Melvin Day\"><PERSON></a>, New Zealand painter and historian (b. 1923)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Melvin Day\"><PERSON></a>, New Zealand painter and historian (b. 1923)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2016", "text": "<PERSON><PERSON>, Indian lawyer and politician, 12th Governor of Sikkim (b. 1935)", "html": "2016 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Indian lawyer and politician, 12th <a href=\"https://wikipedia.org/wiki/Governor_of_Sikkim\" class=\"mw-redirect\" title=\"Governor of Sikkim\">Governor of Sikkim</a> (b. 1935)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON> <PERSON></a>, Indian lawyer and politician, 12th <a href=\"https://wikipedia.org/wiki/Governor_of_Sikkim\" class=\"mw-redirect\" title=\"Governor of Sikkim\">Governor of Sikkim</a> (b. 1935)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>"}, {"title": "Governor of Sikkim", "link": "https://wikipedia.org/wiki/Governor_of_Sikkim"}]}, {"year": "2016", "text": "<PERSON><PERSON><PERSON>, Indian religious leader (b. 1926)", "html": "2016 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_Tirtha_(Kashi_Math)\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON> (Kashi Math)\"><PERSON><PERSON><PERSON></a>, Indian religious leader (b. 1926)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_Tirtha_(Kashi_Math)\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON> (Kashi Math)\"><PERSON><PERSON><PERSON></a>, Indian religious leader (b. 1926)", "links": [{"title": "<PERSON><PERSON><PERSON> (Kashi Math)", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_Tirtha_(Kashi_Math)"}]}, {"year": "2017", "text": "<PERSON><PERSON><PERSON>, American football player and coach (b. 1929)", "html": "2017 - <a href=\"https://wikipedia.org/wiki/Tir<PERSON>_<PERSON>\" title=\"Tirrel <PERSON>\"><PERSON><PERSON><PERSON></a>, American football player and coach (b. 1929)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Tir<PERSON>_<PERSON>\" title=\"Tir<PERSON> <PERSON>\"><PERSON><PERSON><PERSON></a>, American football player and coach (b. 1929)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Tir<PERSON>_<PERSON>"}]}, {"year": "2017", "text": "<PERSON><PERSON>, American western lowland gorilla, first gorilla born in captivity and oldest recorded (b. 1956)", "html": "2017 - <a href=\"https://wikipedia.org/wiki/Colo_(gorilla)\" title=\"Colo (gorilla)\"><PERSON><PERSON></a>, American <a href=\"https://wikipedia.org/wiki/Western_lowland_gorilla\" title=\"Western lowland gorilla\">western lowland gorilla</a>, first gorilla born in captivity and oldest recorded (b. 1956)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Colo_(gorilla)\" title=\"Colo (gorilla)\"><PERSON><PERSON></a>, American <a href=\"https://wikipedia.org/wiki/Western_lowland_gorilla\" title=\"Western lowland gorilla\">western lowland gorilla</a>, first gorilla born in captivity and oldest recorded (b. 1956)", "links": [{"title": "<PERSON><PERSON> (gorilla)", "link": "https://wikipedia.org/wiki/Col<PERSON>_(gorilla)"}, {"title": "Western lowland gorilla", "link": "https://wikipedia.org/wiki/Western_lowland_gorilla"}]}, {"year": "2019", "text": "<PERSON><PERSON>, Malayalam movie composer (b. 1948)", "html": "2019 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_(composer)\" title=\"<PERSON><PERSON> (composer)\"><PERSON><PERSON></a>, Malayalam movie composer (b. 1948)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_(composer)\" title=\"<PERSON><PERSON> (composer)\"><PERSON><PERSON></a>, Malayalam movie composer (b. 1948)", "links": [{"title": "<PERSON><PERSON> (composer)", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON><PERSON><PERSON><PERSON>_(composer)"}]}, {"year": "2020", "text": "<PERSON>, British actor (b.1937)", "html": "2020 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, British actor (b.1937)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, British actor (b.1937)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>s"}]}, {"year": "2021", "text": "<PERSON><PERSON><PERSON>, Pakistani film and television actor (b. 1948)", "html": "2021 - <a href=\"https://wikipedia.org/wiki/Rasheed_Naz\" title=\"Rasheed Naz\"><PERSON><PERSON><PERSON></a>, Pakistani film and television actor (b. 1948)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Rasheed_Naz\" title=\"Rasheed Naz\"><PERSON><PERSON><PERSON> <PERSON><PERSON></a>, Pakistani film and television actor (b. 1948)", "links": [{"title": "Rasheed Naz", "link": "https://wikipedia.org/wiki/Rasheed_Naz"}]}, {"year": "2022", "text": "<PERSON><PERSON><PERSON>, Indian dancer (b. 1937)", "html": "2022 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>j\" title=\"<PERSON><PERSON><PERSON>haraj\"><PERSON><PERSON><PERSON></a>, Indian dancer (b. 1937)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>j\" title=\"<PERSON><PERSON><PERSON>haraj\"><PERSON><PERSON><PERSON></a>, Indian dancer (b. 1937)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>j"}]}, {"year": "2023", "text": "<PERSON><PERSON>, French supercentenarian (b. 1904)", "html": "2023 - <a href=\"https://wikipedia.org/wiki/Lucile_<PERSON>\" title=\"Lucile <PERSON>\"><PERSON><PERSON></a>, French supercentenarian (b. 1904)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"Lucile <PERSON>\"><PERSON><PERSON></a>, French supercentenarian (b. 1904)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Lucile_<PERSON>on"}]}, {"year": "2025", "text": "<PERSON><PERSON>, French politician, 25th Minister of State of Monaco (b. 1959)", "html": "2025 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, French politician, 25th <a href=\"https://wikipedia.org/wiki/Minister_of_State_(Monaco)\" title=\"Minister of State (Monaco)\">Minister of State of Monaco</a> (b. 1959)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, French politician, 25th <a href=\"https://wikipedia.org/wiki/Minister_of_State_(Monaco)\" title=\"Minister of State (Monaco)\">Minister of State of Monaco</a> (b. 1959)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}, {"title": "Minister of State (Monaco)", "link": "https://wikipedia.org/wiki/Minister_of_State_(Monaco)"}]}, {"year": "2025", "text": "<PERSON>, American cartoonist, playwright, screenwriter, and educator (b. 1929)", "html": "2025 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American cartoonist, playwright, screenwriter, and educator (b. 1929)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American cartoonist, playwright, screenwriter, and educator (b. 1929)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2025", "text": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, Mongolian politician, 1st President of Mongolia (b. 1942)", "html": "2025 - <a href=\"https://wikipedia.org/wiki/P<PERSON>al<PERSON><PERSON><PERSON>_<PERSON>chirbat\" title=\"P<PERSON>al<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON></a>, Mongolian politician, 1st <a href=\"https://wikipedia.org/wiki/President_of_Mongolia\" title=\"President of Mongolia\">President of Mongolia</a> (b. 1942)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"Punsal<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON></a>, Mongolian politician, 1st <a href=\"https://wikipedia.org/wiki/President_of_Mongolia\" title=\"President of Mongolia\">President of Mongolia</a> (b. 1942)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Punsalmaagiin_<PERSON>at"}, {"title": "President of Mongolia", "link": "https://wikipedia.org/wiki/President_of_Mongolia"}]}, {"year": "2025", "text": "<PERSON>, Scottish footballer (b. 1940)", "html": "2025 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish footballer (b. 1940)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish footballer (b. 1940)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}]}}