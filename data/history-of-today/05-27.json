{"date": "May 27", "url": "https://wikipedia.org/wiki/May_27", "data": {"Events": [{"year": "1096", "text": "Count <PERSON><PERSON><PERSON> enters Mainz, where his followers massacre Jewish citizens. At least 600 Jews are killed.", "html": "1096 - <a href=\"https://wikipedia.org/wiki/Count_<PERSON><PERSON><PERSON>\" class=\"mw-redirect\" title=\"Count <PERSON><PERSON><PERSON>\">Count <PERSON><PERSON><PERSON></a> enters <a href=\"https://wikipedia.org/wiki/Mainz\" title=\"Mainz\">Mainz</a>, where his followers <a href=\"https://wikipedia.org/wiki/Rhineland_massacres#Emicho\" title=\"Rhineland massacres\">massacre Jewish citizens</a>. At least 600 Jews are killed.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Count_<PERSON><PERSON><PERSON>\" class=\"mw-redirect\" title=\"Count <PERSON><PERSON><PERSON>\">Count <PERSON><PERSON><PERSON></a> enters <a href=\"https://wikipedia.org/wiki/Mainz\" title=\"Mainz\">Mainz</a>, where his followers <a href=\"https://wikipedia.org/wiki/Rhineland_massacres#Emicho\" title=\"Rhineland massacres\">massacre Jewish citizens</a>. At least 600 Jews are killed.", "links": [{"title": "Count <PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Count_<PERSON><PERSON><PERSON>"}, {"title": "Mainz", "link": "https://wikipedia.org/wiki/Mainz"}, {"title": "Rhineland massacres", "link": "https://wikipedia.org/wiki/Rhineland_massacres#Emicho"}]}, {"year": "1120", "text": "<PERSON> of Capua is anointed as <PERSON> two weeks before his untimely death.", "html": "1120 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_Capua\" title=\"<PERSON> of Capua\"><PERSON> of Capua</a> is anointed as <a href=\"https://wikipedia.org/wiki/Prince_of_Capua\" class=\"mw-redirect\" title=\"Prince of Capua\">Prince</a> two weeks before his untimely death.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_Capua\" title=\"<PERSON> of Capua\"><PERSON> of Capua</a> is anointed as <a href=\"https://wikipedia.org/wiki/Prince_of_Capua\" class=\"mw-redirect\" title=\"Prince of Capua\">Prince</a> two weeks before his untimely death.", "links": [{"title": "<PERSON> of Capua", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_Cap<PERSON>"}, {"title": "Prince of Capua", "link": "https://wikipedia.org/wiki/Prince_<PERSON>_Capua"}]}, {"year": "1153", "text": "<PERSON> becomes King of Scotland.", "html": "1153 - <a href=\"https://wikipedia.org/wiki/<PERSON>_IV_of_Scotland\" title=\"<PERSON> IV of Scotland\"><PERSON> IV</a> becomes King of Scotland.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Scotland\" title=\"<PERSON> IV of Scotland\"><PERSON> IV</a> becomes King of Scotland.", "links": [{"title": "<PERSON> of Scotland", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Scotland"}]}, {"year": "1199", "text": "<PERSON> is crowned King of England.", "html": "1199 - <a href=\"https://wikipedia.org/wiki/<PERSON>,_King_of_England\" title=\"<PERSON>, King of England\"><PERSON></a> is crowned King of England.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>,_King_of_England\" title=\"<PERSON>, King of England\"><PERSON></a> is crowned King of England.", "links": [{"title": "<PERSON>, King of England", "link": "https://wikipedia.org/wiki/<PERSON>,_King_of_England"}]}, {"year": "1257", "text": "<PERSON> of Cornwall, and his wife, <PERSON><PERSON> of Provence, are crowned King and Queen of the Germans at Aachen Cathedral.", "html": "1257 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_Cornwall\" title=\"<PERSON> of Cornwall\"><PERSON> of Cornwall</a>, and his wife, <a href=\"https://wikipedia.org/wiki/Sanchia_of_Provence\" title=\"Sanchia of Provence\">Sanchia of Provence</a>, are crowned King and Queen of the Germans at <a href=\"https://wikipedia.org/wiki/Aachen_Cathedral\" title=\"Aachen Cathedral\">Aachen Cathedral</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_Cornwall\" title=\"<PERSON> of Cornwall\"><PERSON> of Cornwall</a>, and his wife, <a href=\"https://wikipedia.org/wiki/Sanchia_of_Provence\" title=\"Sanchia of Provence\">Sanchia of Provence</a>, are crowned King and Queen of the Germans at <a href=\"https://wikipedia.org/wiki/Aachen_Cathedral\" title=\"Aachen Cathedral\">Aachen Cathedral</a>.", "links": [{"title": "<PERSON> of Cornwall", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_Cornwall"}, {"title": "Sanchia of Provence", "link": "https://wikipedia.org/wiki/Sanchia_of_Provence"}, {"title": "Aachen Cathedral", "link": "https://wikipedia.org/wiki/Aachen_Cathedral"}]}, {"year": "1644", "text": "Manchu regent <PERSON><PERSON> defeats rebel leader <PERSON> of the Shun dynasty at the Battle of Shanhai Pass, allowing the Manchus to enter and conquer the capital city of Beijing.", "html": "1644 - <a href=\"https://wikipedia.org/wiki/Manchu\" class=\"mw-redirect\" title=\"Manchu\">Manchu</a> regent <a href=\"https://wikipedia.org/wiki/Dorgon\" title=\"Dorgon\"><PERSON><PERSON></a> defeats rebel leader <a href=\"https://wikipedia.org/wiki/<PERSON>_Z<PERSON>ng\" title=\"Li Zicheng\"><PERSON></a> of the <a href=\"https://wikipedia.org/wiki/Shun_dynasty\" title=\"Shun dynasty\">Shun dynasty</a> at the <a href=\"https://wikipedia.org/wiki/Battle_of_Shanhai_Pass\" title=\"Battle of Shanhai Pass\">Battle of Shanhai Pass</a>, allowing the Manchus to enter and conquer the capital city of Beijing.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Manchu\" class=\"mw-redirect\" title=\"Manchu\">Manchu</a> regent <a href=\"https://wikipedia.org/wiki/Dorgon\" title=\"Dorgon\"><PERSON>rgon</a> defeats rebel leader <a href=\"https://wikipedia.org/wiki/<PERSON>_Z<PERSON>ng\" title=\"Li Zicheng\"><PERSON></a> of the <a href=\"https://wikipedia.org/wiki/Shun_dynasty\" title=\"Shun dynasty\">Shun dynasty</a> at the <a href=\"https://wikipedia.org/wiki/Battle_of_Shanhai_Pass\" title=\"Battle of Shanhai Pass\">Battle of Shanhai Pass</a>, allowing the Manchus to enter and conquer the capital city of Beijing.", "links": [{"title": "Man<PERSON>", "link": "https://wikipedia.org/wiki/Manchu"}, {"title": "Dorgon", "link": "https://wikipedia.org/wiki/Do<PERSON>"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Li_<PERSON>icheng"}, {"title": "Shun dynasty", "link": "https://wikipedia.org/wiki/Shun_dynasty"}, {"title": "Battle of Shanhai Pass", "link": "https://wikipedia.org/wiki/Battle_of_Shanhai_Pass"}]}, {"year": "1703", "text": "Tsar <PERSON> the Great founds the city of Saint Petersburg.", "html": "1703 - Tsar <a href=\"https://wikipedia.org/wiki/<PERSON>_the_Great\" title=\"<PERSON> the Great\"><PERSON> the Great</a> founds the city of <a href=\"https://wikipedia.org/wiki/Saint_Petersburg\" title=\"Saint Petersburg\">Saint Petersburg</a>.", "no_year_html": "Tsar <a href=\"https://wikipedia.org/wiki/<PERSON>_the_Great\" title=\"<PERSON> the Great\"><PERSON> the Great</a> founds the city of <a href=\"https://wikipedia.org/wiki/Saint_Petersburg\" title=\"Saint Petersburg\">Saint Petersburg</a>.", "links": [{"title": "<PERSON> the <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_Great"}, {"title": "Saint Petersburg", "link": "https://wikipedia.org/wiki/Saint_Petersburg"}]}, {"year": "1798", "text": "The <PERSON><PERSON><PERSON><PERSON> duel takes place on Putney Heath outside London. A bloodless duel between the Prime Minister of Great Britain <PERSON> the <PERSON> and his political opponent <PERSON>.", "html": "1798 - The <a href=\"https://wikipedia.org/wiki/Pitt%E2%80%93Tierney_duel\" title=\"Pitt<PERSON>Tierney duel\">Pitt-Tierney duel</a> takes place on <a href=\"https://wikipedia.org/wiki/Put<PERSON>_Heath\" class=\"mw-redirect\" title=\"Putney Heath\"><PERSON><PERSON></a> outside <a href=\"https://wikipedia.org/wiki/London\" title=\"London\">London</a>. A bloodless <a href=\"https://wikipedia.org/wiki/Duel\" title=\"Duel\">duel</a> between the <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Great_Britain\" class=\"mw-redirect\" title=\"Prime Minister of Great Britain\">Prime Minister of Great Britain</a> <a href=\"https://wikipedia.org/wiki/<PERSON>_the_<PERSON>\" title=\"<PERSON> the <PERSON>\"><PERSON> the <PERSON></a> and his political opponent <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Pitt%E2%80%93Tierney_duel\" title=\"Pitt-Tierney duel\">Pitt-Tierney duel</a> takes place on <a href=\"https://wikipedia.org/wiki/Put<PERSON>_Heath\" class=\"mw-redirect\" title=\"Putney Heath\"><PERSON><PERSON></a> outside <a href=\"https://wikipedia.org/wiki/London\" title=\"London\">London</a>. A bloodless <a href=\"https://wikipedia.org/wiki/Duel\" title=\"Duel\">duel</a> between the <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Great_Britain\" class=\"mw-redirect\" title=\"Prime Minister of Great Britain\">Prime Minister of Great Britain</a> <a href=\"https://wikipedia.org/wiki/<PERSON>_the_<PERSON>\" title=\"<PERSON> the <PERSON>\"><PERSON> the <PERSON></a> and his political opponent <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>.", "links": [{"title": "<PERSON>-<PERSON><PERSON> duel", "link": "https://wikipedia.org/wiki/Pitt%E2%80%93Tierney_duel"}, {"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_Heath"}, {"title": "London", "link": "https://wikipedia.org/wiki/London"}, {"title": "Duel", "link": "https://wikipedia.org/wiki/Duel"}, {"title": "Prime Minister of Great Britain", "link": "https://wikipedia.org/wiki/Prime_Minister_of_Great_Britain"}, {"title": "<PERSON> the Younger", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1798", "text": "The Battle of Oulart Hill takes place in Wexford, Ireland; Irish rebel leaders defeat and kill a detachment of militia.", "html": "1798 - The <a href=\"https://wikipedia.org/wiki/Battle_of_Oulart_Hill\" title=\"Battle of Oulart Hill\">Battle of Oulart Hill</a> takes place in <a href=\"https://wikipedia.org/wiki/Wexford\" title=\"Wexford\">Wexford</a>, Ireland; Irish rebel leaders defeat and kill a detachment of militia.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Battle_of_Oulart_Hill\" title=\"Battle of Oulart Hill\">Battle of Oulart Hill</a> takes place in <a href=\"https://wikipedia.org/wiki/Wexford\" title=\"Wexford\">Wexford</a>, Ireland; Irish rebel leaders defeat and kill a detachment of militia.", "links": [{"title": "Battle of Oulart Hill", "link": "https://wikipedia.org/wiki/Battle_of_Oulart_Hill"}, {"title": "Wexford", "link": "https://wikipedia.org/wiki/Wexford"}]}, {"year": "1799", "text": "War of the Second Coalition: Austrian forces defeat the French at Winterthur, Switzerland.", "html": "1799 - <a href=\"https://wikipedia.org/wiki/War_of_the_Second_Coalition\" title=\"War of the Second Coalition\">War of the Second Coalition</a>: <a href=\"https://wikipedia.org/wiki/Habsburg_monarchy\" title=\"Habsburg monarchy\">Austrian</a> forces defeat the <a href=\"https://wikipedia.org/wiki/French_First_Republic\" title=\"French First Republic\">French</a> at <a href=\"https://wikipedia.org/wiki/Battle_of_Winterthur_(1799)\" class=\"mw-redirect\" title=\"Battle of Winterthur (1799)\">Winterthur</a>, Switzerland.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/War_of_the_Second_Coalition\" title=\"War of the Second Coalition\">War of the Second Coalition</a>: <a href=\"https://wikipedia.org/wiki/Habsburg_monarchy\" title=\"Habsburg monarchy\">Austrian</a> forces defeat the <a href=\"https://wikipedia.org/wiki/French_First_Republic\" title=\"French First Republic\">French</a> at <a href=\"https://wikipedia.org/wiki/Battle_of_Winterthur_(1799)\" class=\"mw-redirect\" title=\"Battle of Winterthur (1799)\">Winterthur</a>, Switzerland.", "links": [{"title": "War of the Second Coalition", "link": "https://wikipedia.org/wiki/War_of_the_Second_Coalition"}, {"title": "Habsburg monarchy", "link": "https://wikipedia.org/wiki/Habsburg_monarchy"}, {"title": "French First Republic", "link": "https://wikipedia.org/wiki/French_First_Republic"}, {"title": "Battle of Winterthur (1799)", "link": "https://wikipedia.org/wiki/Battle_of_Winterthur_(1799)"}]}, {"year": "1813", "text": "War of 1812: In Canada, American forces capture Fort George.", "html": "1813 - <a href=\"https://wikipedia.org/wiki/War_of_1812\" title=\"War of 1812\">War of 1812</a>: In Canada, American forces capture <a href=\"https://wikipedia.org/wiki/Battle_of_Fort_George\" title=\"Battle of Fort George\">Fort George</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/War_of_1812\" title=\"War of 1812\">War of 1812</a>: In Canada, American forces capture <a href=\"https://wikipedia.org/wiki/Battle_of_Fort_George\" title=\"Battle of Fort George\">Fort George</a>.", "links": [{"title": "War of 1812", "link": "https://wikipedia.org/wiki/War_of_1812"}, {"title": "Battle of Fort George", "link": "https://wikipedia.org/wiki/Battle_of_Fort_George"}]}, {"year": "1860", "text": "<PERSON> begins the Siege of Palermo, part of the wars of Italian unification.", "html": "1860 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> begins the <a href=\"https://wikipedia.org/wiki/Siege_of_Palermo\" title=\"Siege of Palermo\">Siege of Palermo</a>, part of the wars of <a href=\"https://wikipedia.org/wiki/Italian_unification\" class=\"mw-redirect\" title=\"Italian unification\">Italian unification</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> begins the <a href=\"https://wikipedia.org/wiki/Siege_of_Palermo\" title=\"Siege of Palermo\">Siege of Palermo</a>, part of the wars of <a href=\"https://wikipedia.org/wiki/Italian_unification\" class=\"mw-redirect\" title=\"Italian unification\">Italian unification</a>.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Siege of Palermo", "link": "https://wikipedia.org/wiki/Siege_of_Palermo"}, {"title": "Italian unification", "link": "https://wikipedia.org/wiki/Italian_unification"}]}, {"year": "1863", "text": "American Civil War: The first Union infantry assault of the Siege of Port Hudson occurs.", "html": "1863 - <a href=\"https://wikipedia.org/wiki/American_Civil_War\" title=\"American Civil War\">American Civil War</a>: The first Union infantry assault of the <a href=\"https://wikipedia.org/wiki/Siege_of_Port_Hudson\" title=\"Siege of Port Hudson\">Siege of Port Hudson</a> occurs.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/American_Civil_War\" title=\"American Civil War\">American Civil War</a>: The first Union infantry assault of the <a href=\"https://wikipedia.org/wiki/Siege_of_Port_Hudson\" title=\"Siege of Port Hudson\">Siege of Port Hudson</a> occurs.", "links": [{"title": "American Civil War", "link": "https://wikipedia.org/wiki/American_Civil_War"}, {"title": "Siege of Port Hudson", "link": "https://wikipedia.org/wiki/Siege_of_Port_Hudson"}]}, {"year": "1874", "text": "The first group of Dorsland trekkers under the leadership of <PERSON><PERSON> leaves Pretoria.", "html": "1874 - The first group of <a href=\"https://wikipedia.org/wiki/Dorsland_Trek\" title=\"Dorsland Trek\">Dorsland trekkers</a> under the leadership of <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a> leaves <a href=\"https://wikipedia.org/wiki/Pretoria\" title=\"Pretoria\">Pretoria</a>.", "no_year_html": "The first group of <a href=\"https://wikipedia.org/wiki/Dorsland_Trek\" title=\"Dorsland Trek\">Dorsland trekkers</a> under the leadership of <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a> leaves <a href=\"https://wikipedia.org/wiki/Pretoria\" title=\"Pretoria\">Pretoria</a>.", "links": [{"title": "Dorsland Trek", "link": "https://wikipedia.org/wiki/Dorsland_Trek"}, {"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}, {"title": "Pretoria", "link": "https://wikipedia.org/wiki/Pretoria"}]}, {"year": "1883", "text": "<PERSON> is crowned Tsar of Russia.", "html": "1883 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Russia\" title=\"<PERSON> III of Russia\"><PERSON> III</a> is crowned Tsar of Russia.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Russia\" title=\"<PERSON> III of Russia\"><PERSON> III</a> is crowned Tsar of Russia.", "links": [{"title": "<PERSON> of Russia", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Russia"}]}, {"year": "1896", "text": "The F4-strength St. Louis-East St. Louis tornado hits in St. Louis, Missouri, and East St. Louis, Illinois, killing at least 255 people and causing over $10 million in damage.", "html": "1896 - The <a href=\"https://wikipedia.org/wiki/Fujita_scale\" title=\"Fujita scale\">F4</a>-strength <a href=\"https://wikipedia.org/wiki/1896_St._Louis%E2%80%93East_St._Louis_tornado\" title=\"1896 St. Louis-East St. Louis tornado\">St. Louis-East St. Louis tornado</a> hits in <a href=\"https://wikipedia.org/wiki/St._Louis,_Missouri\" class=\"mw-redirect\" title=\"St. Louis, Missouri\">St. Louis, Missouri</a>, and <a href=\"https://wikipedia.org/wiki/East_St._Louis,_Illinois\" title=\"East St. Louis, Illinois\">East St. Louis, Illinois</a>, killing at least 255 people and causing over $10 million in damage.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Fujita_scale\" title=\"Fujita scale\">F4</a>-strength <a href=\"https://wikipedia.org/wiki/1896_St._Louis%E2%80%93East_St._Louis_tornado\" title=\"1896 St. Louis-East St. Louis tornado\">St. Louis-East St. Louis tornado</a> hits in <a href=\"https://wikipedia.org/wiki/St._Louis,_Missouri\" class=\"mw-redirect\" title=\"St. Louis, Missouri\">St. Louis, Missouri</a>, and <a href=\"https://wikipedia.org/wiki/East_St._Louis,_Illinois\" title=\"East St. Louis, Illinois\">East St. Louis, Illinois</a>, killing at least 255 people and causing over $10 million in damage.", "links": [{"title": "Fujita scale", "link": "https://wikipedia.org/wiki/Fujita_scale"}, {"title": "1896 St. Louis-East St. Louis tornado", "link": "https://wikipedia.org/wiki/1896_St._Louis%E2%80%93East_St._Louis_tornado"}, {"title": "St. Louis, Missouri", "link": "https://wikipedia.org/wiki/St._Louis,_Missouri"}, {"title": "East St. Louis, Illinois", "link": "https://wikipedia.org/wiki/East_St._Louis,_Illinois"}]}, {"year": "1905", "text": "Russo-Japanese War: The Battle of Tsushima begins.", "html": "1905 - <a href=\"https://wikipedia.org/wiki/Russo-Japanese_War\" title=\"Russo-Japanese War\">Russo-Japanese War</a>: The <a href=\"https://wikipedia.org/wiki/Battle_of_Tsushima\" title=\"Battle of Tsushima\">Battle of Tsushima</a> begins.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Russo-Japanese_War\" title=\"Russo-Japanese War\">Russo-Japanese War</a>: The <a href=\"https://wikipedia.org/wiki/Battle_of_Tsushima\" title=\"Battle of Tsushima\">Battle of Tsushima</a> begins.", "links": [{"title": "Russo-Japanese War", "link": "https://wikipedia.org/wiki/Russo-Japanese_War"}, {"title": "Battle of Tsushima", "link": "https://wikipedia.org/wiki/Battle_of_Tsushima"}]}, {"year": "1915", "text": "HMS Princess <PERSON> explodes and sinks off Sheerness, Kent, with the loss of 352 lives.", "html": "1915 - <a href=\"https://wikipedia.org/wiki/HMS_Princess_<PERSON>\" title=\"HMS Princess <PERSON>\">HMS <i>Princess <PERSON></i></a> explodes and sinks off <a href=\"https://wikipedia.org/wiki/Sheerness\" title=\"Sheerness\">Sheerness</a>, <a href=\"https://wikipedia.org/wiki/Kent\" title=\"Kent\">Kent</a>, with the loss of 352 lives.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/HMS_Princess_<PERSON>\" title=\"HMS Princess <PERSON>\">HMS <i>Princess <PERSON></i></a> explodes and sinks off <a href=\"https://wikipedia.org/wiki/Sheerness\" title=\"Sheerness\">Sheerness</a>, <a href=\"https://wikipedia.org/wiki/Kent\" title=\"Kent\">Kent</a>, with the loss of 352 lives.", "links": [{"title": "HMS Princess <PERSON>", "link": "https://wikipedia.org/wiki/HMS_Princess_<PERSON>"}, {"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Sheerness"}, {"title": "Kent", "link": "https://wikipedia.org/wiki/Kent"}]}, {"year": "1917", "text": "<PERSON> <PERSON> promulgates the 1917 Code of Canon Law, the first comprehensive codification of Catholic canon law in the legal history of the Catholic Church.", "html": "1917 - <a href=\"https://wikipedia.org/wiki/<PERSON>_Benedict_XV\" title=\"Pope Benedict XV\">Pope <PERSON> XV</a> promulgates the <a href=\"https://wikipedia.org/wiki/1917_Code_of_Canon_Law\" title=\"1917 Code of Canon Law\">1917 <i>Code of Canon Law</i></a>, the first comprehensive <a href=\"https://wikipedia.org/wiki/Codification_(law)\" title=\"Codification (law)\">codification</a> of <a href=\"https://wikipedia.org/wiki/Catholic_canon_law\" class=\"mw-redirect\" title=\"Catholic canon law\">Catholic canon law</a> in the <a href=\"https://wikipedia.org/wiki/Legal_history_of_the_Catholic_Church\" title=\"Legal history of the Catholic Church\">legal history of the Catholic Church</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_XV\" title=\"Pope Benedict XV\">Pope <PERSON> XV</a> promulgates the <a href=\"https://wikipedia.org/wiki/1917_Code_of_Canon_Law\" title=\"1917 Code of Canon Law\">1917 <i>Code of Canon Law</i></a>, the first comprehensive <a href=\"https://wikipedia.org/wiki/Codification_(law)\" title=\"Codification (law)\">codification</a> of <a href=\"https://wikipedia.org/wiki/Catholic_canon_law\" class=\"mw-redirect\" title=\"Catholic canon law\">Catholic canon law</a> in the <a href=\"https://wikipedia.org/wiki/Legal_history_of_the_Catholic_Church\" title=\"Legal history of the Catholic Church\">legal history of the Catholic Church</a>.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "1917 Code of Canon Law", "link": "https://wikipedia.org/wiki/1917_Code_of_Canon_Law"}, {"title": "Codification (law)", "link": "https://wikipedia.org/wiki/Codification_(law)"}, {"title": "Catholic canon law", "link": "https://wikipedia.org/wiki/Catholic_canon_law"}, {"title": "Legal history of the Catholic Church", "link": "https://wikipedia.org/wiki/Legal_history_of_the_Catholic_Church"}]}, {"year": "1919", "text": "The NC-4 aircraft arrives in Lisbon after completing the first transatlantic flight.", "html": "1919 - The <a href=\"https://wikipedia.org/wiki/NC-4\" class=\"mw-redirect\" title=\"NC-4\">NC-4</a> aircraft arrives in <a href=\"https://wikipedia.org/wiki/Lisbon\" title=\"Lisbon\">Lisbon</a> after completing the first <a href=\"https://wikipedia.org/wiki/Transatlantic_flight\" title=\"Transatlantic flight\">transatlantic flight</a>.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/NC-4\" class=\"mw-redirect\" title=\"NC-4\">NC-4</a> aircraft arrives in <a href=\"https://wikipedia.org/wiki/Lisbon\" title=\"Lisbon\">Lisbon</a> after completing the first <a href=\"https://wikipedia.org/wiki/Transatlantic_flight\" title=\"Transatlantic flight\">transatlantic flight</a>.", "links": [{"title": "NC-4", "link": "https://wikipedia.org/wiki/NC-4"}, {"title": "Lisbon", "link": "https://wikipedia.org/wiki/Lisbon"}, {"title": "Transatlantic flight", "link": "https://wikipedia.org/wiki/Transatlantic_flight"}]}, {"year": "1927", "text": "The Ford Motor Company ceases manufacture of the Ford Model T and begins to retool plants to make the Ford Model A.", "html": "1927 - The <a href=\"https://wikipedia.org/wiki/Ford_Motor_Company\" title=\"Ford Motor Company\">Ford Motor Company</a> ceases manufacture of the <a href=\"https://wikipedia.org/wiki/Ford_Model_T\" title=\"Ford Model T\">Ford Model T</a> and begins to retool plants to make the <a href=\"https://wikipedia.org/wiki/Ford_Model_A_(1927%E2%80%931931)\" title=\"Ford Model A (1927-1931)\">Ford Model A</a>.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Ford_Motor_Company\" title=\"Ford Motor Company\">Ford Motor Company</a> ceases manufacture of the <a href=\"https://wikipedia.org/wiki/Ford_Model_T\" title=\"Ford Model T\">Ford Model T</a> and begins to retool plants to make the <a href=\"https://wikipedia.org/wiki/Ford_Model_A_(1927%E2%80%931931)\" title=\"Ford Model A (1927-1931)\">Ford Model A</a>.", "links": [{"title": "Ford Motor Company", "link": "https://wikipedia.org/wiki/Ford_Motor_Company"}, {"title": "Ford Model T", "link": "https://wikipedia.org/wiki/Ford_Model_T"}, {"title": "Ford Model A (1927-1931)", "link": "https://wikipedia.org/wiki/Ford_Model_A_(1927%E2%80%931931)"}]}, {"year": "1930", "text": "The 1,046 feet (319 m) Chrysler Building in New York City, the tallest man-made structure at the time, opens to the public.", "html": "1930 - The 1,046 feet (319 m) <a href=\"https://wikipedia.org/wiki/Chrysler_Building\" title=\"Chrysler Building\">Chrysler Building</a> in <a href=\"https://wikipedia.org/wiki/New_York_City\" title=\"New York City\">New York City</a>, the <a href=\"https://wikipedia.org/wiki/List_of_tallest_buildings_in_the_world\" class=\"mw-redirect\" title=\"List of tallest buildings in the world\">tallest man-made structure</a> at the time, opens to the public.", "no_year_html": "The 1,046 feet (319 m) <a href=\"https://wikipedia.org/wiki/Chrysler_Building\" title=\"Chrysler Building\">Chrysler Building</a> in <a href=\"https://wikipedia.org/wiki/New_York_City\" title=\"New York City\">New York City</a>, the <a href=\"https://wikipedia.org/wiki/List_of_tallest_buildings_in_the_world\" class=\"mw-redirect\" title=\"List of tallest buildings in the world\">tallest man-made structure</a> at the time, opens to the public.", "links": [{"title": "Chrysler Building", "link": "https://wikipedia.org/wiki/Chrysler_Building"}, {"title": "New York City", "link": "https://wikipedia.org/wiki/New_York_City"}, {"title": "List of tallest buildings in the world", "link": "https://wikipedia.org/wiki/List_of_tallest_buildings_in_the_world"}]}, {"year": "1933", "text": "New Deal: The U.S. Federal Securities Act is signed into law requiring the registration of securities with the Federal Trade Commission.", "html": "1933 - <a href=\"https://wikipedia.org/wiki/New_Deal\" title=\"New Deal\">New Deal</a>: The U.S. <a href=\"https://wikipedia.org/wiki/Federal_Securities_Act\" class=\"mw-redirect\" title=\"Federal Securities Act\">Federal Securities Act</a> is signed into law requiring the registration of securities with the <a href=\"https://wikipedia.org/wiki/Federal_Trade_Commission\" title=\"Federal Trade Commission\">Federal Trade Commission</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/New_Deal\" title=\"New Deal\">New Deal</a>: The U.S. <a href=\"https://wikipedia.org/wiki/Federal_Securities_Act\" class=\"mw-redirect\" title=\"Federal Securities Act\">Federal Securities Act</a> is signed into law requiring the registration of securities with the <a href=\"https://wikipedia.org/wiki/Federal_Trade_Commission\" title=\"Federal Trade Commission\">Federal Trade Commission</a>.", "links": [{"title": "New Deal", "link": "https://wikipedia.org/wiki/New_Deal"}, {"title": "Federal Securities Act", "link": "https://wikipedia.org/wiki/Federal_Securities_Act"}, {"title": "Federal Trade Commission", "link": "https://wikipedia.org/wiki/Federal_Trade_Commission"}]}, {"year": "1935", "text": "New Deal: The Supreme Court of the United States declares the National Industrial Recovery Act to be unconstitutional in A.L.A. Schechter Poultry Corp. v. United States, (295 U.S. 495).", "html": "1935 - New Deal: The <a href=\"https://wikipedia.org/wiki/Supreme_Court_of_the_United_States\" title=\"Supreme Court of the United States\">Supreme Court of the United States</a> declares the <a href=\"https://wikipedia.org/wiki/National_Industrial_Recovery_Act\" class=\"mw-redirect\" title=\"National Industrial Recovery Act\">National Industrial Recovery Act</a> to be unconstitutional in <i><a href=\"https://wikipedia.org/wiki/A.L.A._Schechter_Poultry_Corp._v._United_States\" title=\"A.L.A. Schechter Poultry Corp. v. United States\">A.L.A. Schechter Poultry Corp. v. United States</a></i>, (295 U.S. 495).", "no_year_html": "New Deal: The <a href=\"https://wikipedia.org/wiki/Supreme_Court_of_the_United_States\" title=\"Supreme Court of the United States\">Supreme Court of the United States</a> declares the <a href=\"https://wikipedia.org/wiki/National_Industrial_Recovery_Act\" class=\"mw-redirect\" title=\"National Industrial Recovery Act\">National Industrial Recovery Act</a> to be unconstitutional in <i><a href=\"https://wikipedia.org/wiki/A.L.A._Schechter_Poultry_Corp._v._United_States\" title=\"A.L.A. Schechter Poultry Corp. v. United States\">A.L.A. Schechter Poultry Corp. v. United States</a></i>, (295 U.S. 495).", "links": [{"title": "Supreme Court of the United States", "link": "https://wikipedia.org/wiki/Supreme_Court_of_the_United_States"}, {"title": "National Industrial Recovery Act", "link": "https://wikipedia.org/wiki/National_Industrial_Recovery_Act"}, {"title": "A.L.A. Schechter Poultry Corp. v. United States", "link": "https://wikipedia.org/wiki/A.L.A._Sc<PERSON><PERSON>er_Poultry_Corp._v._United_States"}]}, {"year": "1937", "text": "In California, the Golden Gate Bridge opens to pedestrian traffic, creating a vital link between San Francisco and Marin County, California.", "html": "1937 - In <a href=\"https://wikipedia.org/wiki/California\" title=\"California\">California</a>, the <a href=\"https://wikipedia.org/wiki/Golden_Gate_Bridge\" title=\"Golden Gate Bridge\">Golden Gate Bridge</a> opens to pedestrian traffic, creating a vital link between <a href=\"https://wikipedia.org/wiki/San_Francisco\" title=\"San Francisco\">San Francisco</a> and <a href=\"https://wikipedia.org/wiki/Marin_County,_California\" title=\"Marin County, California\">Marin County, California</a>.", "no_year_html": "In <a href=\"https://wikipedia.org/wiki/California\" title=\"California\">California</a>, the <a href=\"https://wikipedia.org/wiki/Golden_Gate_Bridge\" title=\"Golden Gate Bridge\">Golden Gate Bridge</a> opens to pedestrian traffic, creating a vital link between <a href=\"https://wikipedia.org/wiki/San_Francisco\" title=\"San Francisco\">San Francisco</a> and <a href=\"https://wikipedia.org/wiki/Marin_County,_California\" title=\"Marin County, California\">Marin County, California</a>.", "links": [{"title": "California", "link": "https://wikipedia.org/wiki/California"}, {"title": "Golden Gate Bridge", "link": "https://wikipedia.org/wiki/Golden_Gate_Bridge"}, {"title": "San Francisco", "link": "https://wikipedia.org/wiki/San_Francisco"}, {"title": "Marin County, California", "link": "https://wikipedia.org/wiki/Marin_County,_California"}]}, {"year": "1940", "text": "World War II: In the Le Paradis massacre, 99 soldiers from a Royal Norfolk Regiment unit are shot after surrendering to German troops; two survive.", "html": "1940 - <a href=\"https://wikipedia.org/wiki/World_War_II\" title=\"World War II\">World War II</a>: In the <a href=\"https://wikipedia.org/wiki/Le_Paradis_massacre\" title=\"Le Paradis massacre\">Le Paradis massacre</a>, 99 soldiers from a <a href=\"https://wikipedia.org/wiki/Royal_Norfolk_Regiment\" title=\"Royal Norfolk Regiment\">Royal Norfolk Regiment</a> unit are shot after surrendering to <a href=\"https://wikipedia.org/wiki/Nazi_Germany\" title=\"Nazi Germany\">German</a> troops; two survive.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/World_War_II\" title=\"World War II\">World War II</a>: In the <a href=\"https://wikipedia.org/wiki/Le_Paradis_massacre\" title=\"Le Paradis massacre\">Le Paradis massacre</a>, 99 soldiers from a <a href=\"https://wikipedia.org/wiki/Royal_Norfolk_Regiment\" title=\"Royal Norfolk Regiment\">Royal Norfolk Regiment</a> unit are shot after surrendering to <a href=\"https://wikipedia.org/wiki/Nazi_Germany\" title=\"Nazi Germany\">German</a> troops; two survive.", "links": [{"title": "World War II", "link": "https://wikipedia.org/wiki/World_War_II"}, {"title": "Le Paradis massacre", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_massacre"}, {"title": "Royal Norfolk Regiment", "link": "https://wikipedia.org/wiki/Royal_Norfolk_Regiment"}, {"title": "Nazi Germany", "link": "https://wikipedia.org/wiki/Nazi_Germany"}]}, {"year": "1941", "text": "World War II: U.S. President <PERSON> proclaims an \"unlimited national emergency\".", "html": "1941 - World War II: U.S. President <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> proclaims an \"unlimited national emergency\".", "no_year_html": "World War II: U.S. President <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> proclaims an \"unlimited national emergency\".", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1941", "text": "World War II: The German battleship Bismarck is sunk in the North Atlantic, killing almost 2,100 men.", "html": "1941 - World War II: The <a href=\"https://wikipedia.org/wiki/German_battleship_<PERSON>\" title=\"German battleship <PERSON>\">German battleship <i>Bismarck</i></a> is sunk in the North Atlantic, killing almost 2,100 men.", "no_year_html": "World War II: The <a href=\"https://wikipedia.org/wiki/German_battleship_<PERSON>\" title=\"German battleship <PERSON>\">German battleship <i>Bismarck</i></a> is sunk in the North Atlantic, killing almost 2,100 men.", "links": [{"title": "German battleship Bismarck", "link": "https://wikipedia.org/wiki/German_battleship_Bismarck"}]}, {"year": "1942", "text": "World War II: In Operation Anthropoid, <PERSON><PERSON><PERSON> is fatally wounded in Prague; he dies of his injuries eight days later.", "html": "1942 - World War II: In <a href=\"https://wikipedia.org/wiki/Operation_Anthropoid\" class=\"mw-redirect\" title=\"Operation Anthropoid\">Operation Anthropoid</a>, <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a> is fatally wounded in <a href=\"https://wikipedia.org/wiki/Prague\" title=\"Prague\">Prague</a>; he dies of his injuries eight days later.", "no_year_html": "World War II: In <a href=\"https://wikipedia.org/wiki/Operation_Anthropoid\" class=\"mw-redirect\" title=\"Operation Anthropoid\">Operation Anthropoid</a>, <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a> is fatally wounded in <a href=\"https://wikipedia.org/wiki/Prague\" title=\"Prague\">Prague</a>; he dies of his injuries eight days later.", "links": [{"title": "Operation Anthropoid", "link": "https://wikipedia.org/wiki/Operation_Anthropoid"}, {"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}, {"title": "Prague", "link": "https://wikipedia.org/wiki/Prague"}]}, {"year": "1950", "text": "The Linnanmäki amusement park is opened for the first time in Helsinki.", "html": "1950 - The <a href=\"https://wikipedia.org/wiki/Linnanm%C3%A4ki\" title=\"Linnanmäki\">Linnanmäki</a> amusement park is opened for the first time in <a href=\"https://wikipedia.org/wiki/Helsinki\" title=\"Helsinki\">Helsinki</a>.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Linnanm%C3%A4ki\" title=\"Linnanmäki\">Linnanmäki</a> amusement park is opened for the first time in <a href=\"https://wikipedia.org/wiki/Helsinki\" title=\"Helsinki\">Helsinki</a>.", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Linnanm%C3%A4ki"}, {"title": "Helsinki", "link": "https://wikipedia.org/wiki/Helsinki"}]}, {"year": "1958", "text": "First flight of the McDonnell Douglas F-4 Phantom II.", "html": "1958 - First flight of the <a href=\"https://wikipedia.org/wiki/McDonnell_Douglas_F-4_Phantom_II\" title=\"McDonnell Douglas F-4 Phantom II\">McDonnell Douglas F-4 Phantom II</a>.", "no_year_html": "First flight of the <a href=\"https://wikipedia.org/wiki/McDonnell_Douglas_F-4_Phantom_II\" title=\"McDonnell Douglas F-4 Phantom II\">McDonnell Douglas F-4 Phantom II</a>.", "links": [{"title": "McDonnell Douglas F-4 Phantom II", "link": "https://wikipedia.org/wiki/<PERSON>_Douglas_F-4_Phantom_II"}]}, {"year": "1960", "text": "In Turkey, a military coup removes President <PERSON><PERSON><PERSON><PERSON> and the rest of the democratic government from office.", "html": "1960 - In <a href=\"https://wikipedia.org/wiki/Turkey\" title=\"Turkey\">Turkey</a>, a <a href=\"https://wikipedia.org/wiki/1960_Turkish_coup_d%27%C3%A9tat\" title=\"1960 Turkish coup d'état\">military coup</a> removes President <a href=\"https://wikipedia.org/wiki/Cel%C3%A2l_Bayar\" title=\"Celâ<PERSON> Bayar\"><PERSON><PERSON><PERSON><PERSON></a> and the rest of the democratic government from office.", "no_year_html": "In <a href=\"https://wikipedia.org/wiki/Turkey\" title=\"Turkey\">Turkey</a>, a <a href=\"https://wikipedia.org/wiki/1960_Turkish_coup_d%27%C3%A9tat\" title=\"1960 Turkish coup d'état\">military coup</a> removes President <a href=\"https://wikipedia.org/wiki/Cel%C3%A2l_Bayar\" title=\"Celâ<PERSON> Bayar\"><PERSON><PERSON><PERSON><PERSON></a> and the rest of the democratic government from office.", "links": [{"title": "Turkey", "link": "https://wikipedia.org/wiki/Turkey"}, {"title": "1960 Turkish coup d'état", "link": "https://wikipedia.org/wiki/1960_Turkish_coup_d%27%C3%A9tat"}, {"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Cel%C3%A2l_Bayar"}]}, {"year": "1962", "text": "The Centralia mine fire is ignited in the town's landfill above a coal mine.", "html": "1962 - The <a href=\"https://wikipedia.org/wiki/Centralia_mine_fire\" title=\"Centralia mine fire\">Centralia mine fire</a> is ignited in the town's landfill above a coal mine.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Centralia_mine_fire\" title=\"Centralia mine fire\">Centralia mine fire</a> is ignited in the town's landfill above a coal mine.", "links": [{"title": "Centralia mine fire", "link": "https://wikipedia.org/wiki/Centralia_mine_fire"}]}, {"year": "1965", "text": "Vietnam War: American warships begin the first bombardment of National Liberation Front targets within South Vietnam.", "html": "1965 - <a href=\"https://wikipedia.org/wiki/Vietnam_War\" title=\"Vietnam War\">Vietnam War</a>: American warships begin the first bombardment of <a href=\"https://wikipedia.org/wiki/National_Front_for_the_Liberation_of_Vietnam\" class=\"mw-redirect\" title=\"National Front for the Liberation of Vietnam\">National Liberation Front</a> targets within <a href=\"https://wikipedia.org/wiki/South_Vietnam\" title=\"South Vietnam\">South Vietnam</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Vietnam_War\" title=\"Vietnam War\">Vietnam War</a>: American warships begin the first bombardment of <a href=\"https://wikipedia.org/wiki/National_Front_for_the_Liberation_of_Vietnam\" class=\"mw-redirect\" title=\"National Front for the Liberation of Vietnam\">National Liberation Front</a> targets within <a href=\"https://wikipedia.org/wiki/South_Vietnam\" title=\"South Vietnam\">South Vietnam</a>.", "links": [{"title": "Vietnam War", "link": "https://wikipedia.org/wiki/Vietnam_War"}, {"title": "National Front for the Liberation of Vietnam", "link": "https://wikipedia.org/wiki/National_Front_for_the_Liberation_of_Vietnam"}, {"title": "South Vietnam", "link": "https://wikipedia.org/wiki/South_Vietnam"}]}, {"year": "1967", "text": "Australians vote in favor of a constitutional referendum granting the Australian government the power to make laws to benefit Indigenous Australians and to count them in the national census.", "html": "1967 - Australians vote in favor of a <a href=\"https://wikipedia.org/wiki/Australian_referendum,_1967_(Aboriginals)\" class=\"mw-redirect\" title=\"Australian referendum, 1967 (Aboriginals)\">constitutional referendum</a> granting the Australian government the power to make laws to benefit <a href=\"https://wikipedia.org/wiki/Indigenous_Australians\" title=\"Indigenous Australians\">Indigenous Australians</a> and to count them in the national census.", "no_year_html": "Australians vote in favor of a <a href=\"https://wikipedia.org/wiki/Australian_referendum,_1967_(Aboriginals)\" class=\"mw-redirect\" title=\"Australian referendum, 1967 (Aboriginals)\">constitutional referendum</a> granting the Australian government the power to make laws to benefit <a href=\"https://wikipedia.org/wiki/Indigenous_Australians\" title=\"Indigenous Australians\">Indigenous Australians</a> and to count them in the national census.", "links": [{"title": "Australian referendum, 1967 (Aboriginals)", "link": "https://wikipedia.org/wiki/Australian_referendum,_1967_(Aboriginals)"}, {"title": "Indigenous Australians", "link": "https://wikipedia.org/wiki/Indigenous_Australians"}]}, {"year": "1967", "text": "The U.S. Navy aircraft carrier USS John F<PERSON> Kennedy is launched by <PERSON> and her daughter <PERSON>.", "html": "1967 - The U.S. Navy aircraft carrier <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>_(CV-67)\" title=\"USS <PERSON> (CV-67)\">USS <i><PERSON></i></a> is <a href=\"https://wikipedia.org/wiki/Ceremonial_ship_launching\" title=\"Ceremonial ship launching\">launched</a> by <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a> and her daughter <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>.", "no_year_html": "The U.S. Navy aircraft carrier <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>_(CV-67)\" title=\"USS <PERSON> (CV-67)\">USS <i><PERSON></i></a> is <a href=\"https://wikipedia.org/wiki/Ceremonial_ship_launching\" title=\"Ceremonial ship launching\">launched</a> by <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a> and her daughter <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>.", "links": [{"title": "USS <PERSON> (CV-67)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>_(CV-67)"}, {"title": "Ceremonial ship launching", "link": "https://wikipedia.org/wiki/Ceremonial_ship_launching"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1971", "text": "The Dahlerau train disaster, the worst railway accident in West Germany, kills 46 people and injures 25 near Wuppertal.", "html": "1971 - The <a href=\"https://wikipedia.org/wiki/Dahlerau_train_disaster\" title=\"Dahlerau train disaster\">Dahlerau train disaster</a>, the worst railway accident in West Germany, kills 46 people and injures 25 near <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Dahlerau_train_disaster\" title=\"Dahlerau train disaster\">Dahlerau train disaster</a>, the worst railway accident in West Germany, kills 46 people and injures 25 near <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>.", "links": [{"title": "Dahlerau train disaster", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_train_disaster"}, {"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>"}]}, {"year": "1971", "text": "Pakistani forces massacre over 200 civilians, mostly Bengali Hindus, in the Bagbati massacre.", "html": "1971 - Pakistani forces massacre over 200 civilians, mostly <a href=\"https://wikipedia.org/wiki/Bengali_Hindus\" title=\"Bengali Hindus\">Bengali Hindus</a>, in the <a href=\"https://wikipedia.org/wiki/Bagbati_massacre\" title=\"Bagbati massacre\">Bagbati massacre</a>.", "no_year_html": "Pakistani forces massacre over 200 civilians, mostly <a href=\"https://wikipedia.org/wiki/Bengali_Hindus\" title=\"Bengali Hindus\">Bengali Hindus</a>, in the <a href=\"https://wikipedia.org/wiki/Bagbati_massacre\" title=\"Bagbati massacre\">Bagbati massacre</a>.", "links": [{"title": "Bengali Hindus", "link": "https://wikipedia.org/wiki/Bengali_Hindus"}, {"title": "Bagbati massacre", "link": "https://wikipedia.org/wiki/Bagbati_massacre"}]}, {"year": "1975", "text": "Dibbles Bridge coach crash near Grassington, in North Yorkshire, England, kills 33 - the highest ever death toll in a road accident in the United Kingdom.", "html": "1975 - <a href=\"https://wikipedia.org/wiki/1975_Dibbles_Bridge_coach_crash\" title=\"1975 Dibbles Bridge coach crash\">Dibbles Bridge coach crash</a> near <a href=\"https://wikipedia.org/wiki/Grassington\" title=\"Grassington\">Grassington</a>, in <a href=\"https://wikipedia.org/wiki/North_Yorkshire\" title=\"North Yorkshire\">North Yorkshire</a>, England, kills 33 - the highest ever death toll in a road accident in the United Kingdom.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/1975_Di<PERSON>s_Bridge_coach_crash\" title=\"1975 Dibbles Bridge coach crash\">Dibbles Bridge coach crash</a> near <a href=\"https://wikipedia.org/wiki/Grassington\" title=\"Grassington\">Grassington</a>, in <a href=\"https://wikipedia.org/wiki/North_Yorkshire\" title=\"North Yorkshire\">North Yorkshire</a>, England, kills 33 - the highest ever death toll in a road accident in the United Kingdom.", "links": [{"title": "1975 Dibbles Bridge coach crash", "link": "https://wikipedia.org/wiki/1975_Dibbles_Bridge_coach_crash"}, {"title": "Grassington", "link": "https://wikipedia.org/wiki/Grassington"}, {"title": "North Yorkshire", "link": "https://wikipedia.org/wiki/North_Yorkshire"}]}, {"year": "1977", "text": "A plane crash at José Martí International Airport in Havana, Cuba, kills 67.", "html": "1977 - A <a href=\"https://wikipedia.org/wiki/Aeroflot_Flight_331\" title=\"Aeroflot Flight 331\">plane crash</a> at <a href=\"https://wikipedia.org/wiki/Jos%C3%A9_Mart%C3%AD_International_Airport\" title=\"José Martí International Airport\">José Martí International Airport</a> in <a href=\"https://wikipedia.org/wiki/Havana\" title=\"Havana\">Havana</a>, Cuba, kills 67.", "no_year_html": "A <a href=\"https://wikipedia.org/wiki/Aeroflot_Flight_331\" title=\"Aeroflot Flight 331\">plane crash</a> at <a href=\"https://wikipedia.org/wiki/Jos%C3%A9_Mart%C3%AD_International_Airport\" title=\"José Martí International Airport\">José Martí International Airport</a> in <a href=\"https://wikipedia.org/wiki/Havana\" title=\"Havana\">Havana</a>, Cuba, kills 67.", "links": [{"title": "Aeroflot Flight 331", "link": "https://wikipedia.org/wiki/Aeroflot_Flight_331"}, {"title": "José Martí International Airport", "link": "https://wikipedia.org/wiki/Jos%C3%A9_Mart%C3%AD_International_Airport"}, {"title": "Havana", "link": "https://wikipedia.org/wiki/Havana"}]}, {"year": "1980", "text": "The Gwangju Massacre: Airborne and army troops of South Korea retake the city of Gwangju from civil militias, killing at least 207 and possibly many more.", "html": "1980 - The <a href=\"https://wikipedia.org/wiki/Gwangju_Massacre\" class=\"mw-redirect\" title=\"Gwangju Massacre\">Gwangju Massacre</a>: Airborne and army troops of South Korea retake the city of <a href=\"https://wikipedia.org/wiki/Gwangju\" title=\"Gwangju\">Gwangju</a> from civil militias, killing at least 207 and possibly many more.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Gwangju_Massacre\" class=\"mw-redirect\" title=\"Gwangju Massacre\">Gwangju Massacre</a>: Airborne and army troops of South Korea retake the city of <a href=\"https://wikipedia.org/wiki/Gwangju\" title=\"Gwangju\">Gwangju</a> from civil militias, killing at least 207 and possibly many more.", "links": [{"title": "Gwangju Massacre", "link": "https://wikipedia.org/wiki/Gwangju_Massacre"}, {"title": "Gwangju", "link": "https://wikipedia.org/wiki/Gwangju"}]}, {"year": "1984", "text": "The Danube-Black Sea Canal is opened, in a ceremony attended by the Ceauș<PERSON><PERSON>. It had been under construction since the 1950s.", "html": "1984 - The <a href=\"https://wikipedia.org/wiki/Danube%E2%80%93Black_Sea_Canal\" title=\"Danube-Black Sea Canal\">Danube-Black Sea Canal</a> is opened, in a ceremony attended by the <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>%C8%99<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>. It had been under construction since the 1950s.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Danube%E2%80%93Black_Sea_Canal\" title=\"Danube-Black Sea Canal\">Danube-Black Sea Canal</a> is opened, in a ceremony attended by the <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>%C8%99<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>. It had been under construction since the 1950s.", "links": [{"title": "Danube-Black Sea Canal", "link": "https://wikipedia.org/wiki/Danube%E2%80%93Black_Sea_Canal"}, {"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>%C8%99escu"}]}, {"year": "1988", "text": "Somaliland War of Independence: The Somali National Movement launches a major offensive against Somali government forces in Hargeisa and Burao, then the second- and third-largest cities of Somalia.", "html": "1988 - <a href=\"https://wikipedia.org/wiki/Somaliland_War_of_Independence\" title=\"Somaliland War of Independence\">Somaliland War of Independence</a>: The <a href=\"https://wikipedia.org/wiki/Somali_National_Movement\" title=\"Somali National Movement\">Somali National Movement</a> launches a <a href=\"https://wikipedia.org/wiki/1988_Hargeisa-Burao_offensive\" title=\"1988 Hargeisa-Burao offensive\">major offensive</a> against Somali government forces in <a href=\"https://wikipedia.org/wiki/Hargeisa\" title=\"Hargeisa\">Hargeisa</a> and <a href=\"https://wikipedia.org/wiki/Burao\" title=\"Burao\">Burao</a>, then the second- and third-largest cities of <a href=\"https://wikipedia.org/wiki/Somalia\" title=\"Somalia\">Somalia</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Somaliland_War_of_Independence\" title=\"Somaliland War of Independence\">Somaliland War of Independence</a>: The <a href=\"https://wikipedia.org/wiki/Somali_National_Movement\" title=\"Somali National Movement\">Somali National Movement</a> launches a <a href=\"https://wikipedia.org/wiki/1988_Hargeisa-Burao_offensive\" title=\"1988 Hargeisa-Burao offensive\">major offensive</a> against Somali government forces in <a href=\"https://wikipedia.org/wiki/Hargeisa\" title=\"Hargeisa\">Hargeisa</a> and <a href=\"https://wikipedia.org/wiki/Burao\" title=\"Burao\">Burao</a>, then the second- and third-largest cities of <a href=\"https://wikipedia.org/wiki/Somalia\" title=\"Somalia\">Somalia</a>.", "links": [{"title": "Somaliland War of Independence", "link": "https://wikipedia.org/wiki/Somaliland_War_of_Independence"}, {"title": "Somali National Movement", "link": "https://wikipedia.org/wiki/Somali_National_Movement"}, {"title": "1988 Hargeisa-Burao offensive", "link": "https://wikipedia.org/wiki/1988_<PERSON><PERSON><PERSON>-<PERSON><PERSON><PERSON>_offensive"}, {"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Harge<PERSON>"}, {"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>o"}, {"title": "Somalia", "link": "https://wikipedia.org/wiki/Somalia"}]}, {"year": "1996", "text": "First Chechen War: Russian President <PERSON> meets with Chechnyan rebels for the first time and negotiates a cease-fire.", "html": "1996 - <a href=\"https://wikipedia.org/wiki/First_Chechen_War\" title=\"First Chechen War\">First Chechen War</a>: Russian President <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> meets with <a href=\"https://wikipedia.org/wiki/Chechnya\" title=\"Chechnya\">Chechnyan</a> rebels for the first time and negotiates a cease-fire.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/First_Chechen_War\" title=\"First Chechen War\">First Chechen War</a>: Russian President <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> meets with <a href=\"https://wikipedia.org/wiki/Chechnya\" title=\"Chechnya\">Chechnyan</a> rebels for the first time and negotiates a cease-fire.", "links": [{"title": "First Chechen War", "link": "https://wikipedia.org/wiki/First_Chechen_War"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}, {"title": "Chechnya", "link": "https://wikipedia.org/wiki/Chechnya"}]}, {"year": "1997", "text": "The 1997 Central Texas tornado outbreak occurs, spawning multiple tornadoes in Central Texas, including the F5 that killed 27 in Jarrell.", "html": "1997 - The <a href=\"https://wikipedia.org/wiki/1997_Central_Texas_tornado_outbreak\" title=\"1997 Central Texas tornado outbreak\">1997 Central Texas tornado outbreak</a> occurs, spawning multiple tornadoes in Central Texas, including <a href=\"https://wikipedia.org/wiki/1997_Prairie_Dell-Jarrell_tornado\" class=\"mw-redirect\" title=\"1997 Prairie Dell-Jarrell tornado\">the F5 that killed 27</a> in <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>,_Texas\" title=\"Jarrell, Texas\"><PERSON><PERSON><PERSON></a>.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/1997_Central_Texas_tornado_outbreak\" title=\"1997 Central Texas tornado outbreak\">1997 Central Texas tornado outbreak</a> occurs, spawning multiple tornadoes in Central Texas, including <a href=\"https://wikipedia.org/wiki/1997_Prairie_Dell-Jarrell_tornado\" class=\"mw-redirect\" title=\"1997 Prairie Dell-Jarrell tornado\">the F5 that killed 27</a> in <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>,_Texas\" title=\"Jarrell, Texas\"><PERSON><PERSON><PERSON></a>.", "links": [{"title": "1997 Central Texas tornado outbreak", "link": "https://wikipedia.org/wiki/1997_Central_Texas_tornado_outbreak"}, {"title": "1997 Prairie Dell-Jarrell tornado", "link": "https://wikipedia.org/wiki/1997_Prairie_Dell-J<PERSON><PERSON>_tornado"}, {"title": "Jarrell, Texas", "link": "https://wikipedia.org/wiki/<PERSON>arrell,_Texas"}]}, {"year": "1998", "text": "Oklahoma City bombing: <PERSON> is sentenced to 12 years in prison and fined $200,000 for failing to warn authorities about the terrorist plot.", "html": "1998 - <a href=\"https://wikipedia.org/wiki/Oklahoma_City_bombing\" title=\"Oklahoma City bombing\">Oklahoma City bombing</a>: <a href=\"https://wikipedia.org/wiki/<PERSON>_(American)\" class=\"mw-redirect\" title=\"<PERSON> (American)\"><PERSON></a> is sentenced to 12 years in prison and fined $200,000 for failing to warn authorities about the <a href=\"https://wikipedia.org/wiki/Terrorism\" title=\"Terrorism\">terrorist</a> plot.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Oklahoma_City_bombing\" title=\"Oklahoma City bombing\">Oklahoma City bombing</a>: <a href=\"https://wikipedia.org/wiki/<PERSON>_(American)\" class=\"mw-redirect\" title=\"<PERSON> (American)\"><PERSON></a> is sentenced to 12 years in prison and fined $200,000 for failing to warn authorities about the <a href=\"https://wikipedia.org/wiki/Terrorism\" title=\"Terrorism\">terrorist</a> plot.", "links": [{"title": "Oklahoma City bombing", "link": "https://wikipedia.org/wiki/Oklahoma_City_bombing"}, {"title": "<PERSON> (American)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(American)"}, {"title": "Terrorism", "link": "https://wikipedia.org/wiki/Terrorism"}]}, {"year": "1999", "text": "Space Shuttle Discovery is launched on STS-96, the first shuttle mission to dock with the International Space Station.", "html": "1999 - <a href=\"https://wikipedia.org/wiki/Space_Shuttle_Discovery\" title=\"Space Shuttle Discovery\">Space Shuttle <i>Discovery</i></a> is launched on <a href=\"https://wikipedia.org/wiki/STS-96\" title=\"STS-96\">STS-96</a>, the first shuttle mission to dock with the <a href=\"https://wikipedia.org/wiki/International_Space_Station\" title=\"International Space Station\">International Space Station</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Space_Shuttle_Discovery\" title=\"Space Shuttle Discovery\">Space Shuttle <i>Discovery</i></a> is launched on <a href=\"https://wikipedia.org/wiki/STS-96\" title=\"STS-96\">STS-96</a>, the first shuttle mission to dock with the <a href=\"https://wikipedia.org/wiki/International_Space_Station\" title=\"International Space Station\">International Space Station</a>.", "links": [{"title": "Space Shuttle Discovery", "link": "https://wikipedia.org/wiki/Space_Shuttle_Discovery"}, {"title": "STS-96", "link": "https://wikipedia.org/wiki/STS-96"}, {"title": "International Space Station", "link": "https://wikipedia.org/wiki/International_Space_Station"}]}, {"year": "2001", "text": "Members of Abu <PERSON>, an Islamist separatist group, seize twenty hostages from an affluent island resort on Palawan in the Philippines; the hostage crisis would not be resolved until June 2002.", "html": "2001 - Members of <a href=\"https://wikipedia.org/wiki/<PERSON>Say<PERSON>\" title=\"Abu Sayyaf\"><PERSON> Say<PERSON></a>, an <a href=\"https://wikipedia.org/wiki/Islamism\" title=\"Islamism\">Islamist</a> separatist group, <a href=\"https://wikipedia.org/wiki/Dos_Palmas_kidnappings\" title=\"Dos Palmas kidnappings\">seize twenty hostages</a> from an affluent island resort on <a href=\"https://wikipedia.org/wiki/Palawan\" title=\"Palawan\">Palawan</a> in the <a href=\"https://wikipedia.org/wiki/Philippines\" title=\"Philippines\">Philippines</a>; the hostage crisis would not be resolved until June 2002.", "no_year_html": "Members of <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"Abu Sayyaf\"><PERSON> Say<PERSON></a>, an <a href=\"https://wikipedia.org/wiki/Islamism\" title=\"Islamism\">Islamist</a> separatist group, <a href=\"https://wikipedia.org/wiki/Dos_Palmas_kidnappings\" title=\"Dos Palmas kidnappings\">seize twenty hostages</a> from an affluent island resort on <a href=\"https://wikipedia.org/wiki/Palawan\" title=\"Palawan\">Palawan</a> in the <a href=\"https://wikipedia.org/wiki/Philippines\" title=\"Philippines\">Philippines</a>; the hostage crisis would not be resolved until June 2002.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}, {"title": "Islamism", "link": "https://wikipedia.org/wiki/Islamism"}, {"title": "<PERSON><PERSON>s kidnappings", "link": "https://wikipedia.org/wiki/Dos_Palmas_kidnappings"}, {"title": "Palawan", "link": "https://wikipedia.org/wiki/Palawan"}, {"title": "Philippines", "link": "https://wikipedia.org/wiki/Philippines"}]}, {"year": "2006", "text": "The 6.4 Mw  Yogyakarta earthquake shakes central Java with an MSK intensity of VIII (Damaging), leaving more than 5,700 dead and 37,000 injured.", "html": "2006 - The 6.4 M<sub>w</sub>  <a href=\"https://wikipedia.org/wiki/2006_Yogyakarta_earthquake\" title=\"2006 Yogyakarta earthquake\">Yogyakarta earthquake</a> shakes central <a href=\"https://wikipedia.org/wiki/Java\" title=\"Java\">Java</a> with an <a href=\"https://wikipedia.org/wiki/Medvedev%E2%80%93Sponheuer%E2%80%93Karnik_scale\" title=\"Medvedev-Sponheuer-Karnik scale\">MSK</a> intensity of VIII (<i>Damaging</i>), leaving more than 5,700 dead and 37,000 injured.", "no_year_html": "The 6.4 M<sub>w</sub>  <a href=\"https://wikipedia.org/wiki/2006_Yogyakarta_earthquake\" title=\"2006 Yogyakarta earthquake\">Yogyakarta earthquake</a> shakes central <a href=\"https://wikipedia.org/wiki/Java\" title=\"Java\">Java</a> with an <a href=\"https://wikipedia.org/wiki/Medvedev%E2%80%93Sponheuer%E2%80%93Karnik_scale\" title=\"Medvedev-Sponheuer-Karnik scale\">MSK</a> intensity of VIII (<i>Damaging</i>), leaving more than 5,700 dead and 37,000 injured.", "links": [{"title": "2006 Yogyakarta earthquake", "link": "https://wikipedia.org/wiki/2006_Yogyakarta_earthquake"}, {"title": "Java", "link": "https://wikipedia.org/wiki/Java"}, {"title": "<PERSON>d<PERSON><PERSON>-Sponheuer-Karnik scale", "link": "https://wikipedia.org/wiki/Medvedev%E2%80%93Sponheuer%E2%80%93Karnik_scale"}]}, {"year": "2016", "text": "<PERSON> is the first president of the United States to visit Hiroshima Peace Memorial Park and meet <PERSON><PERSON><PERSON><PERSON>.", "html": "2016 - <a href=\"https://wikipedia.org/wiki/Barack_Obama\" title=\"Barack Obama\"><PERSON></a> is the first president of the United States to visit <a href=\"https://wikipedia.org/wiki/Hiroshima_Peace_Memorial_Park\" title=\"Hiroshima Peace Memorial Park\">Hiroshima Peace Memorial Park</a> and meet <i><a href=\"https://wikipedia.org/wiki/Hibakusha\" title=\"Hibakusha\">Hibakusha</a></i>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Barack_Obama\" title=\"Barack Obama\"><PERSON></a> is the first president of the United States to visit <a href=\"https://wikipedia.org/wiki/Hiroshima_Peace_Memorial_Park\" title=\"Hiroshima Peace Memorial Park\">Hiroshima Peace Memorial Park</a> and meet <i><a href=\"https://wikipedia.org/wiki/Hibakusha\" title=\"Hibakus<PERSON>\">Hibakus<PERSON></a></i>.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Barack<PERSON>Obama"}, {"title": "Hiroshima Peace Memorial Park", "link": "https://wikipedia.org/wiki/Hiroshima_Peace_Memorial_Park"}, {"title": "Hibakus<PERSON>", "link": "https://wikipedia.org/wiki/Hi<PERSON>kusha"}]}, {"year": "2017", "text": "<PERSON> takes over after <PERSON><PERSON> as the leader of the Conservative Party of Canada.", "html": "2017 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> takes over after <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a> as the leader of the <a href=\"https://wikipedia.org/wiki/Conservative_Party_of_Canada\" title=\"Conservative Party of Canada\">Conservative Party of Canada</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> takes over after <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a> as the leader of the <a href=\"https://wikipedia.org/wiki/Conservative_Party_of_Canada\" title=\"Conservative Party of Canada\">Conservative Party of Canada</a>.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}, {"title": "Conservative Party of Canada", "link": "https://wikipedia.org/wiki/Conservative_Party_of_Canada"}]}, {"year": "2018", "text": "Maryland Flood Event: A flood occurs throughout the Patapsco Valley, causing one death, destroying the entire first floors of buildings on Main Street in Ellicott City, and causing cars to overturn.", "html": "2018 - <a href=\"https://wikipedia.org/wiki/2018_Maryland_flood\" title=\"2018 Maryland flood\">Maryland Flood Event</a>: A flood occurs throughout the <a href=\"https://wikipedia.org/wiki/Patapsco_Valley\" title=\"Patapsco Valley\">Patapsco Valley</a>, causing one death, destroying the entire first floors of buildings on Main Street in <a href=\"https://wikipedia.org/wiki/Ellicott_City\" class=\"mw-redirect\" title=\"Ellicott City\">Ellicott City</a>, and causing cars to overturn.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/2018_Maryland_flood\" title=\"2018 Maryland flood\">Maryland Flood Event</a>: A flood occurs throughout the <a href=\"https://wikipedia.org/wiki/Patapsco_Valley\" title=\"Patapsco Valley\">Patapsco Valley</a>, causing one death, destroying the entire first floors of buildings on Main Street in <a href=\"https://wikipedia.org/wiki/Ellicott_City\" class=\"mw-redirect\" title=\"Ellicott City\">Ellicott City</a>, and causing cars to overturn.", "links": [{"title": "2018 Maryland flood", "link": "https://wikipedia.org/wiki/2018_Maryland_flood"}, {"title": "Patapsco Valley", "link": "https://wikipedia.org/wiki/Patapsco_Valley"}, {"title": "Ellicott City", "link": "https://wikipedia.org/wiki/Ellicott_City"}]}], "Births": [{"year": "742", "text": "Emperor <PERSON><PERSON> of Tang (d. 805)", "html": "742 - <a href=\"https://wikipedia.org/wiki/Emperor_<PERSON><PERSON>_of_Tang\" title=\"Emperor <PERSON><PERSON> of Tang\">Emperor <PERSON><PERSON> of Tang</a> (d. 805)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Emperor_<PERSON><PERSON>_of_Tang\" title=\"Emperor <PERSON>zong of Tang\">Emperor <PERSON><PERSON> of Tang</a> (d. 805)", "links": [{"title": "Emperor <PERSON><PERSON> of Tang", "link": "https://wikipedia.org/wiki/Emperor_<PERSON><PERSON>_of_Tang"}]}, {"year": "1332", "text": "<PERSON>, Tunisian historian and theologian (d. 1406)", "html": "1332 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Tunisian historian and theologian (d. 1406)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Tunisian historian and theologian (d. 1406)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1378", "text": "<PERSON>, Chinese military commander, historian and playwright (d. 1448)[citation needed]", "html": "1378 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Chinese military commander, historian and playwright (d. 1448)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Chinese military commander, historian and playwright (d. 1448)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1519", "text": "<PERSON><PERSON><PERSON>, Italian historian and theorist (d. 1594)", "html": "1519 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_Mei\" title=\"Girolamo Mei\"><PERSON><PERSON><PERSON></a>, Italian historian and theorist (d. 1594)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_Mei\" title=\"Girolamo Mei\"><PERSON><PERSON><PERSON></a>, Italian historian and theorist (d. 1594)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1537", "text": "<PERSON>, Landgrave of Hesse-Marburg (d. 1604)", "html": "1537 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Landgrave_of_Hesse-Marburg\" title=\"<PERSON>, Landgrave of Hesse-Marburg\"><PERSON>, Landgrave of Hesse-Marburg</a> (d. 1604)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Landgrave_of_Hesse-Marburg\" title=\"<PERSON>, Landgrave of Hesse-Marburg\"><PERSON>, Landgrave of Hesse-Marburg</a> (d. 1604)", "links": [{"title": "<PERSON>, Landgrave of Hesse-Marburg", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_<PERSON><PERSON>_of_Hesse-Marburg"}]}, {"year": "1576", "text": "<PERSON><PERSON><PERSON>, German author and scholar (d. 1649)", "html": "1576 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, German author and scholar (d. 1649)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, German author and scholar (d. 1649)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>aspar_<PERSON><PERSON>"}]}, {"year": "1584", "text": "<PERSON>, German theologian and composer (d. 1640)", "html": "1584 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German theologian and composer (d. 1640)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German theologian and composer (d. 1640)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1601", "text": "<PERSON>, French-Canadian missionary and saint (d. 1648)", "html": "1601 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French-Canadian missionary and saint (d. 1648)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French-Canadian missionary and saint (d. 1648)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1626", "text": "<PERSON>, Prince of Orange (d. 1650)", "html": "1626 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Prince_of_Orange\" title=\"<PERSON>, Prince of Orange\"><PERSON>, Prince of Orange</a> (d. 1650)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Prince_of_Orange\" title=\"<PERSON>, Prince of Orange\"><PERSON>, Prince of Orange</a> (d. 1650)", "links": [{"title": "<PERSON>, Prince of Orange", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Prince_of_Orange"}]}, {"year": "1651", "text": "<PERSON>, French cardinal (d. 1729)", "html": "1651 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French cardinal (d. 1729)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French cardinal (d. 1729)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1652", "text": "<PERSON>, Princess <PERSON><PERSON> of Germany (d. 1722)", "html": "1652 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Princess_<PERSON><PERSON>\" class=\"mw-redirect\" title=\"<PERSON>, Princess <PERSON><PERSON>\"><PERSON>, Princess <PERSON><PERSON></a> of Germany (d. 1722)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Princess_<PERSON><PERSON>\" class=\"mw-redirect\" title=\"<PERSON>, Princess <PERSON><PERSON>\"><PERSON>, Princess <PERSON><PERSON></a> of Germany (d. 1722)", "links": [{"title": "<PERSON>, Princess <PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Princess_<PERSON><PERSON>"}]}, {"year": "1738", "text": "<PERSON>, American merchant and politician, 14th President of the Continental Congress (d. 1796)", "html": "1738 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American merchant and politician, 14th <a href=\"https://wikipedia.org/wiki/President_of_the_Continental_Congress\" title=\"President of the Continental Congress\">President of the Continental Congress</a> (d. 1796)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American merchant and politician, 14th <a href=\"https://wikipedia.org/wiki/President_of_the_Continental_Congress\" title=\"President of the Continental Congress\">President of the Continental Congress</a> (d. 1796)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "President of the Continental Congress", "link": "https://wikipedia.org/wiki/President_of_the_Continental_Congress"}]}, {"year": "1756", "text": "<PERSON> of Bavaria (d. 1825)", "html": "1756 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_of_Bavaria\" title=\"<PERSON> of Bavaria\"><PERSON> of Bavaria</a> (d. 1825)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_of_Bavaria\" title=\"<PERSON> of Bavaria\"><PERSON> of Bavaria</a> (d. 1825)", "links": [{"title": "<PERSON> of Bavaria", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>_Bavaria"}]}, {"year": "1774", "text": "<PERSON>, Irish hydrographer and officer in the Royal Navy (d. 1857)", "html": "1774 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Irish hydrographer and officer in the Royal Navy (d. 1857)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Irish hydrographer and officer in the Royal Navy (d. 1857)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1794", "text": "<PERSON>, American businessman and philanthropist (d. 1877)", "html": "1794 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American businessman and philanthropist (d. 1877)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American businessman and philanthropist (d. 1877)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1814", "text": "<PERSON>, Viennese-born American architect (d. 1885)", "html": "1814 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Viennese-born American architect (d. 1885)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Viennese-born American architect (d. 1885)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1815", "text": "<PERSON>, English-Australian politician, 7th Premier of New South Wales (d. 1896)", "html": "1815 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English-Australian politician, 7th <a href=\"https://wikipedia.org/wiki/Premier_of_New_South_Wales\" title=\"Premier of New South Wales\">Premier of New South Wales</a> (d. 1896)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English-Australian politician, 7th <a href=\"https://wikipedia.org/wiki/Premier_of_New_South_Wales\" title=\"Premier of New South Wales\">Premier of New South Wales</a> (d. 1896)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Henry_<PERSON>"}, {"title": "Premier of New South Wales", "link": "https://wikipedia.org/wiki/Premier_of_New_South_Wales"}]}, {"year": "1818", "text": "<PERSON>, American journalist and activist (d. 1894)", "html": "1818 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American journalist and activist (d. 1894)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American journalist and activist (d. 1894)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1819", "text": "<PERSON>, American poet and songwriter (d. 1910)", "html": "1819 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American poet and songwriter (d. 1910)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American poet and songwriter (d. 1910)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1827", "text": "<PERSON>, American lawyer and politician (d. 1892)", "html": "1827 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_(U.S._politician)\" title=\"<PERSON> (U.S. politician)\"><PERSON></a>, American lawyer and politician (d. 1892)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_(U.S._politician)\" title=\"<PERSON> (U.S. politician)\"><PERSON></a>, American lawyer and politician (d. 1892)", "links": [{"title": "<PERSON> (U.S. politician)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_(U.S._politician)"}]}, {"year": "1832", "text": "<PERSON><PERSON>, American surveyor and politician, 7th Governor of Oregon (d. 1917)", "html": "1832 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"Zenas Ferry Moody\"><PERSON><PERSON></a>, American surveyor and politician, 7th <a href=\"https://wikipedia.org/wiki/Governor_of_Oregon\" title=\"Governor of Oregon\">Governor of Oregon</a> (d. 1917)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"Zenas Ferry Moody\"><PERSON><PERSON></a>, American surveyor and politician, 7th <a href=\"https://wikipedia.org/wiki/Governor_of_Oregon\" title=\"Governor of Oregon\">Governor of Oregon</a> (d. 1917)", "links": [{"title": "Zenas Ferry Moody", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_Moody"}, {"title": "Governor of Oregon", "link": "https://wikipedia.org/wiki/Governor_of_Oregon"}]}, {"year": "1836", "text": "<PERSON>, American businessman and financier (d. 1892)", "html": "1836 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American businessman and financier (d. 1892)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American businessman and financier (d. 1892)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1837", "text": "<PERSON>, American police officer (d. 1876)", "html": "1837 - <a href=\"https://wikipedia.org/wiki/<PERSON>_Bill_<PERSON>\" title=\"Wild Bill Hi<PERSON>ok\"><PERSON></a>, American police officer (d. 1876)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"Wild Bill <PERSON>\"><PERSON> <PERSON></a>, American police officer (d. 1876)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1852", "text": "<PERSON>, English cricketer (d. 1899)", "html": "1852 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(cricketer)\" title=\"<PERSON> (cricketer)\"><PERSON></a>, English cricketer (d. 1899)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>(cricketer)\" title=\"<PERSON> (cricketer)\"><PERSON></a>, English cricketer (d. 1899)", "links": [{"title": "<PERSON> (cricketer)", "link": "https://wikipedia.org/wiki/<PERSON>_(cricketer)"}]}, {"year": "1857", "text": "<PERSON>, German chemist (d. 1928)", "html": "1857 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German chemist (d. 1928)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German chemist (d. 1928)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1860", "text": "<PERSON>, Portuguese politician, 7th President of Portugal (d. 1941)", "html": "1860 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Portuguese politician, 7th <a href=\"https://wikipedia.org/wiki/President_of_Portugal\" title=\"President of Portugal\">President of Portugal</a> (d. 1941)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Portuguese politician, 7th <a href=\"https://wikipedia.org/wiki/President_of_Portugal\" title=\"President of Portugal\">President of Portugal</a> (d. 1941)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "President of Portugal", "link": "https://wikipedia.org/wiki/President_of_Portugal"}]}, {"year": "1860", "text": "<PERSON><PERSON><PERSON><PERSON>, Norwegian songwriter (d. 1931)", "html": "1860 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Norwegian songwriter (d. 1931)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Norwegian songwriter (d. 1931)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1863", "text": "<PERSON>, English cricketer (d. 1921)", "html": "1863 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English cricketer (d. 1921)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English cricketer (d. 1921)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1867", "text": "<PERSON>, English author and playwright (d. 1931)", "html": "1867 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English author and playwright (d. 1931)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English author and playwright (d. 1931)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1868", "text": "<PERSON><PERSON><PERSON>, Bosnian poet and author (d. 1924)", "html": "1868 - <a href=\"https://wikipedia.org/wiki/Aleksa_%C5%A0anti%C4%87\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Bosnian poet and author (d. 1924)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Aleksa_%C5%A0anti%C4%87\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Bosnian poet and author (d. 1924)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Aleksa_%C5%A0anti%C4%87"}]}, {"year": "1871", "text": "<PERSON>, French painter and illustrator (d. 1958)", "html": "1871 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French painter and illustrator (d. 1958)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French painter and illustrator (d. 1958)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1875", "text": "<PERSON>, English cricketer (d. 1942)", "html": "1875 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(cricketer)\" class=\"mw-redirect\" title=\"<PERSON> (cricketer)\"><PERSON></a>, English cricketer (d. 1942)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(cricketer)\" class=\"mw-redirect\" title=\"<PERSON> (cricketer)\"><PERSON></a>, English cricketer (d. 1942)", "links": [{"title": "<PERSON> (cricketer)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(cricketer)"}]}, {"year": "1876", "text": "<PERSON><PERSON><PERSON><PERSON>, Polish journalist and author (d. 1945)", "html": "1876 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON>rdy<PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Polish journalist and author (d. 1945)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Polish journalist and author (d. 1945)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "1876", "text": "<PERSON>, English engineer (d. 1965)", "html": "1876 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English engineer (d. 1965)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English engineer (d. 1965)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1878", "text": "<PERSON>, Swedish artist (d. 1972)", "html": "1878 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swedish artist (d. 1972)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swedish artist (d. 1972)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1879", "text": "<PERSON>, German-American linguist and psychologist (d. 1963)", "html": "1879 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%<PERSON><PERSON>\" title=\"<PERSON>\"><PERSON></a>, German-American linguist and psychologist (d. 1963)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%<PERSON><PERSON>\" title=\"<PERSON>\"><PERSON></a>, German-American linguist and psychologist (d. 1963)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Karl_B%C3%BChler"}]}, {"year": "1879", "text": "<PERSON>, German judge and politician (d. 1962)", "html": "1879 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German judge and politician (d. 1962)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German judge and politician (d. 1962)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1883", "text": "<PERSON>, American painter (d. 1971)", "html": "1883 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American painter (d. 1971)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American painter (d. 1971)", "links": [{"title": "<PERSON>ke", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1884", "text": "<PERSON>, Czech journalist, author, and composer (d. 1968)", "html": "1884 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Max Brod\"><PERSON></a>, Czech journalist, author, and composer (d. 1968)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_Brod\" title=\"Max Brod\"><PERSON></a>, Czech journalist, author, and composer (d. 1968)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>rod"}]}, {"year": "1887", "text": "<PERSON>, English cricketer (d. 1978)", "html": "1887 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English cricketer (d. 1978)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English cricketer (d. 1978)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1888", "text": "<PERSON>, French composer (d. 1979)", "html": "1888 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French composer (d. 1979)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French composer (d. 1979)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1891", "text": "<PERSON>, Canadian violinist, pianist, and composer (d. 1965)", "html": "1891 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian violinist, pianist, and composer (d. 1965)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian violinist, pianist, and composer (d. 1965)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1891", "text": "<PERSON><PERSON>, Estonian poet and author (d. 1958)", "html": "1891 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>%C3%A4rner\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Estonian poet and author (d. 1958)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>aan_<PERSON>%C3%A4rner\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Estonian poet and author (d. 1958)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Jaan_K%C3%A4rner"}]}, {"year": "1894", "text": "<PERSON><PERSON><PERSON>, French physician and author (d. 1961)", "html": "1894 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>%C3%A9line\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, French physician and author (d. 1961)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A9line\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, French physician and author (d. 1961)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>-<PERSON>_<PERSON>%C3%A9line"}]}, {"year": "1894", "text": "<PERSON><PERSON><PERSON>, American detective novelist and screenwriter (d. 1961)", "html": "1894 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American detective novelist and screenwriter (d. 1961)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American detective novelist and screenwriter (d. 1961)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1895", "text": "<PERSON>, Canadian educator and politician, 13th Premier of Manitoba (d. 1995)", "html": "1895 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian educator and politician, 13th <a href=\"https://wikipedia.org/wiki/Premier_of_Manitoba\" title=\"Premier of Manitoba\">Premier of Manitoba</a> (d. 1995)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian educator and politician, 13th <a href=\"https://wikipedia.org/wiki/Premier_of_Manitoba\" title=\"Premier of Manitoba\">Premier of Manitoba</a> (d. 1995)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "Premier of Manitoba", "link": "https://wikipedia.org/wiki/Premier_of_Manitoba"}]}, {"year": "1897", "text": "<PERSON>, English physicist and academic, Nobel Prize laureate (d. 1967)", "html": "1897 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English physicist and academic, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Physics\" title=\"Nobel Prize in Physics\">Nobel Prize</a> laureate (d. 1967)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English physicist and academic, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Physics\" title=\"Nobel Prize in Physics\">Nobel Prize</a> laureate (d. 1967)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}, {"title": "Nobel Prize in Physics", "link": "https://wikipedia.org/wiki/Nobel_Prize_in_Physics"}]}, {"year": "1897", "text": "<PERSON><PERSON>, American rugby player and coach (d. 1962)", "html": "1897 - <a href=\"https://wikipedia.org/wiki/Din<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American rugby player and coach (d. 1962)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American rugby player and coach (d. 1962)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Dink_<PERSON>"}]}, {"year": "1898", "text": "<PERSON>, American engineer, inventor and writer (d. 1976)", "html": "1898 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American engineer, inventor and writer (d. 1976)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American engineer, inventor and writer (d. 1976)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1899", "text": "<PERSON>, Estonian chess and draughts player (d. 1993)", "html": "1899 - <a href=\"https://wikipedia.org/wiki/<PERSON>_T%C3%BCrn\" title=\"<PERSON>\"><PERSON></a>, Estonian chess and <a href=\"https://wikipedia.org/wiki/Draughts\" class=\"mw-redirect\" title=\"Draughts\">draughts</a> player (d. 1993)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_T%C3%BCrn\" title=\"<PERSON>\"><PERSON></a>, Estonian chess and <a href=\"https://wikipedia.org/wiki/Draughts\" class=\"mw-redirect\" title=\"Draughts\">draughts</a> player (d. 1993)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Johannes_T%C3%BCrn"}, {"title": "Draughts", "link": "https://wikipedia.org/wiki/Draughts"}]}, {"year": "1900", "text": "<PERSON><PERSON>, German overseer of the Nazi Uckermark concentration camp (d. 1964)", "html": "1900 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>z\" title=\"<PERSON><PERSON> Toberentz\"><PERSON><PERSON></a>, German overseer of the Nazi <a href=\"https://wikipedia.org/wiki/Uckermark_concentration_camp\" title=\"Uckermark concentration camp\">Uckermark concentration camp</a> (d. 1964)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>z\" title=\"<PERSON><PERSON>z\"><PERSON><PERSON></a>, German overseer of the Nazi <a href=\"https://wikipedia.org/wiki/Uckermark_concentration_camp\" title=\"Uckermark concentration camp\">Uckermark concentration camp</a> (d. 1964)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>z"}, {"title": "Uckermark concentration camp", "link": "https://wikipedia.org/wiki/Uckermark_concentration_camp"}]}, {"year": "1900", "text": "<PERSON><PERSON><PERSON><PERSON><PERSON>, Belarusian poet and translator (d. 1933)", "html": "1900 - <a href=\"https://wikipedia.org/wiki/U%C5%82adzimir_%C5%BDy%C5%82ka\" title=\"<PERSON><PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON><PERSON></a>, Belarusian poet and translator (d. 1933)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/U%C5%82adzimir_%C5%BDy%C5%82ka\" title=\"<PERSON><PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON><PERSON></a>, Belarusian poet and translator (d. 1933)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/U%C5%82adzimir_%C5%BDy%C5%82ka"}]}, {"year": "1906", "text": "<PERSON><PERSON><PERSON>, Thai monk and philosopher (d. 1993)", "html": "1906 - <a href=\"https://wikipedia.org/wiki/Buddhadas<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Thai monk and philosopher (d. 1993)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Buddhadas<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Thai monk and philosopher (d. 1993)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>a"}]}, {"year": "1906", "text": "<PERSON>, English footballer (d. 1984)", "html": "1906 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(footballer)\" title=\"<PERSON> (footballer)\"><PERSON></a>, English footballer (d. 1984)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(footballer)\" title=\"<PERSON> (footballer)\"><PERSON></a>, English footballer (d. 1984)", "links": [{"title": "<PERSON> (footballer)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(footballer)"}]}, {"year": "1906", "text": "<PERSON>, Italian bishop (d. 2009)", "html": "1906 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian bishop (d. 2009)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian bishop (d. 2009)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1907", "text": "<PERSON>, Greek-American poet and critic (d. 1988)", "html": "1907 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Greek-American poet and critic (d. 1988)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Greek-American poet and critic (d. 1988)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1907", "text": "<PERSON>, American biologist, environmentalist, and author (d. 1964)", "html": "1907 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American biologist, environmentalist, and author (d. 1964)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American biologist, environmentalist, and author (d. 1964)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1909", "text": "<PERSON>, American singer and philanthropist (d. 2011)", "html": "1909 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer and philanthropist (d. 2011)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Hope\"><PERSON></a>, American singer and philanthropist (d. 2011)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1909", "text": "<PERSON>, Venezuelan supercentenarian, oldest living man, last man born in 1900s decade (d. 2024)", "html": "1909 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>%C3%A9rez\" title=\"<PERSON>\"><PERSON></a>, Venezuelan supercentenarian, <a href=\"https://wikipedia.org/wiki/Oldest_living_man\" class=\"mw-redirect\" title=\"Oldest living man\">oldest living man</a>, last man born in 1900s decade (d. 2024)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_P%C3%A9rez\" title=\"<PERSON>\"><PERSON></a>, Venezuelan supercentenarian, <a href=\"https://wikipedia.org/wiki/Oldest_living_man\" class=\"mw-redirect\" title=\"Oldest living man\">oldest living man</a>, last man born in 1900s decade (d. 2024)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_P%C3%A9rez"}, {"title": "Oldest living man", "link": "https://wikipedia.org/wiki/Oldest_living_man"}]}, {"year": "1911", "text": "<PERSON>, American journalist and politician, 38th Vice President of the United States (d. 1978)", "html": "1911 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American journalist and politician, 38th <a href=\"https://wikipedia.org/wiki/Vice_President_of_the_United_States\" title=\"Vice President of the United States\">Vice President of the United States</a> (d. 1978)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American journalist and politician, 38th <a href=\"https://wikipedia.org/wiki/Vice_President_of_the_United_States\" title=\"Vice President of the United States\">Vice President of the United States</a> (d. 1978)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Vice President of the United States", "link": "https://wikipedia.org/wiki/Vice_President_of_the_United_States"}]}, {"year": "1911", "text": "<PERSON>, Hungarian-Israeli politician, Mayor of Jerusalem (d. 2007)", "html": "1911 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Hungarian-Israeli politician, <a href=\"https://wikipedia.org/wiki/Mayor_of_Jerusalem\" title=\"Mayor of Jerusalem\">Mayor of Jerusalem</a> (d. 2007)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Hungarian-Israeli politician, <a href=\"https://wikipedia.org/wiki/Mayor_of_Jerusalem\" title=\"Mayor of Jerusalem\">Mayor of Jerusalem</a> (d. 2007)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Mayor of Jerusalem", "link": "https://wikipedia.org/wiki/Mayor_of_Jerusalem"}]}, {"year": "1911", "text": "<PERSON>, American actor (d. 1993)", "html": "1911 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor (d. 1993)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Vincent Price\"><PERSON></a>, American actor (d. 1993)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1912", "text": "<PERSON>, American novelist and short story writer (d. 1982)", "html": "1912 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American novelist and short story writer (d. 1982)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American novelist and short story writer (d. 1982)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1912", "text": "<PERSON>, American golfer and sportscaster (d. 2002)", "html": "1912 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American golfer and sportscaster (d. 2002)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American golfer and sportscaster (d. 2002)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>d"}]}, {"year": "1912", "text": "<PERSON>, American baseball player, coach, and manager (d. 1995)", "html": "1912 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(baseball)\" title=\"<PERSON> (baseball)\"><PERSON></a>, American baseball player, coach, and manager (d. 1995)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(baseball)\" title=\"<PERSON> (baseball)\"><PERSON></a>, American baseball player, coach, and manager (d. 1995)", "links": [{"title": "<PERSON> (baseball)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(baseball)"}]}, {"year": "1915", "text": "<PERSON><PERSON>, Chilean singer-songwriter (d. 1996)", "html": "1915 - <a href=\"https://wikipedia.org/wiki/Ester_Sor%C3%A9\" title=\"Ester Soré\"><PERSON><PERSON></a>, Chilean singer-songwriter (d. 1996)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Ester_Sor%C3%A9\" title=\"Ester Soré\"><PERSON><PERSON></a>, Chilean singer-songwriter (d. 1996)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Ester_Sor%C3%A9"}]}, {"year": "1915", "text": "<PERSON>, American novelist (d. 2019)", "html": "1915 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American novelist (d. 2019)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American novelist (d. 2019)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1917", "text": "<PERSON>, English engineer (d. 2007)", "html": "1917 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English engineer (d. 2007)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English engineer (d. 2007)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1918", "text": "<PERSON><PERSON><PERSON>, Japanese commander and politician, 45th Prime Minister of Japan (d. 2019)", "html": "1918 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese commander and politician, 45th <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Japan\" title=\"Prime Minister of Japan\">Prime Minister of Japan</a> (d. 2019)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese commander and politician, 45th <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Japan\" title=\"Prime Minister of Japan\">Prime Minister of Japan</a> (d. 2019)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}, {"title": "Prime Minister of Japan", "link": "https://wikipedia.org/wiki/Prime_Minister_of_Japan"}]}, {"year": "1921", "text": "<PERSON>, Australian-English animator, director, and voice actor (d. 2013)", "html": "1921 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian-English animator, director, and voice actor (d. 2013)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian-English animator, director, and voice actor (d. 2013)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1922", "text": "<PERSON>, German lieutenant and pharmacist (d. 2015)", "html": "1922 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German lieutenant and pharmacist (d. 2015)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German lieutenant and pharmacist (d. 2015)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1922", "text": "<PERSON>, English actor (d. 2015)", "html": "1922 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actor (d. 2015)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actor (d. 2015)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1922", "text": "<PERSON>, American banker and politician, 37th Governor of Colorado (d. 2013)", "html": "1922 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American banker and politician, 37th <a href=\"https://wikipedia.org/wiki/Governor_of_Colorado\" title=\"Governor of Colorado\">Governor of Colorado</a> (d. 2013)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American banker and politician, 37th <a href=\"https://wikipedia.org/wiki/Governor_of_Colorado\" title=\"Governor of Colorado\">Governor of Colorado</a> (d. 2013)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>"}, {"title": "Governor of Colorado", "link": "https://wikipedia.org/wiki/Governor_of_Colorado"}]}, {"year": "1923", "text": "<PERSON>, German-American political scientist and politician, 56th United States Secretary of State, Nobel Prize laureate (d. 2023)", "html": "1923 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German-American political scientist and politician, 56th <a href=\"https://wikipedia.org/wiki/United_States_Secretary_of_State\" title=\"United States Secretary of State\">United States Secretary of State</a>, <a href=\"https://wikipedia.org/wiki/Nobel_Peace_Prize\" title=\"Nobel Peace Prize\">Nobel Prize</a> laureate (d. 2023)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German-American political scientist and politician, 56th <a href=\"https://wikipedia.org/wiki/United_States_Secretary_of_State\" title=\"United States Secretary of State\">United States Secretary of State</a>, <a href=\"https://wikipedia.org/wiki/Nobel_Peace_Prize\" title=\"Nobel Peace Prize\">Nobel Prize</a> laureate (d. 2023)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "United States Secretary of State", "link": "https://wikipedia.org/wiki/United_States_Secretary_of_State"}, {"title": "Nobel Peace Prize", "link": "https://wikipedia.org/wiki/Nobel_Peace_Prize"}]}, {"year": "1923", "text": "<PERSON>, American businessman and philanthropist (d. 2020)", "html": "1923 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>tone\"><PERSON></a>, American businessman and philanthropist (d. 2020)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Redstone\"><PERSON></a>, American businessman and philanthropist (d. 2020)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_Redstone"}]}, {"year": "1924", "text": "<PERSON>, Venezuelan physician and politician, President of Venezuela (d. 2014)", "html": "1924 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Venezuelan physician and politician, <a href=\"https://wikipedia.org/wiki/President_of_Venezuela\" title=\"President of Venezuela\">President of Venezuela</a> (d. 2014)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Venezuelan physician and politician, <a href=\"https://wikipedia.org/wiki/President_of_Venezuela\" title=\"President of Venezuela\">President of Venezuela</a> (d. 2014)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "President of Venezuela", "link": "https://wikipedia.org/wiki/President_of_Venezuela"}]}, {"year": "1924", "text": "<PERSON>, English-Australian director, founded the Melbourne Theatre Company (d. 2013)", "html": "1924 - <a href=\"https://wikipedia.org/wiki/<PERSON>(director)\" title=\"<PERSON> (director)\"><PERSON></a>, English-Australian director, founded the <a href=\"https://wikipedia.org/wiki/Melbourne_Theatre_Company\" title=\"Melbourne Theatre Company\">Melbourne Theatre Company</a> (d. 2013)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>(director)\" title=\"<PERSON> (director)\"><PERSON></a>, English-Australian director, founded the <a href=\"https://wikipedia.org/wiki/Melbourne_Theatre_Company\" title=\"Melbourne Theatre Company\">Melbourne Theatre Company</a> (d. 2013)", "links": [{"title": "<PERSON> (director)", "link": "https://wikipedia.org/wiki/<PERSON>(director)"}, {"title": "Melbourne Theatre Company", "link": "https://wikipedia.org/wiki/Melbourne_Theatre_Company"}]}, {"year": "1925", "text": "<PERSON>, American journalist and author (d. 2008)", "html": "1925 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American journalist and author (d. 2008)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American journalist and author (d. 2008)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1927", "text": "<PERSON><PERSON><PERSON>, Estonian chess player and journalist (d. 1996)", "html": "1927 - <a href=\"https://wikipedia.org/wiki/J%C3%<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Estonian chess player and journalist (d. 1996)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/J%C3%<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Estonian chess player and journalist (d. 1996)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/J%C3%<PERSON><PERSON>_<PERSON>viir"}]}, {"year": "1928", "text": "<PERSON><PERSON>, Scottish-American composer and educator", "html": "1928 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Scottish-American composer and educator", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Scottish-American composer and educator", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1930", "text": "<PERSON>, American novelist and short story writer (d. 2024)", "html": "1930 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American novelist and short story writer (d. 2024)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American novelist and short story writer (d. 2024)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1930", "text": "<PERSON>, American civil servant and judge, 8th Director of the Federal Bureau of Investigation (d. 2020)", "html": "1930 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American civil servant and judge, 8th <a href=\"https://wikipedia.org/wiki/Director_of_the_Federal_Bureau_of_Investigation\" title=\"Director of the Federal Bureau of Investigation\">Director of the Federal Bureau of Investigation</a> (d. 2020)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/William_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American civil servant and judge, 8th <a href=\"https://wikipedia.org/wiki/Director_of_the_Federal_Bureau_of_Investigation\" title=\"Director of the Federal Bureau of Investigation\">Director of the Federal Bureau of Investigation</a> (d. 2020)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/William_<PERSON>_<PERSON>"}, {"title": "Director of the Federal Bureau of Investigation", "link": "https://wikipedia.org/wiki/Director_of_the_Federal_Bureau_of_Investigation"}]}, {"year": "1930", "text": "<PERSON><PERSON>, Estonian composer and educator (d. 2010)", "html": "1930 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Estonian composer and educator (d. 2010)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Estonian composer and educator (d. 2010)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1931", "text": "<PERSON>, French-Canadian neurologist (d. 1986)", "html": "1931 - <a href=\"https://wikipedia.org/wiki/Andr%C3%A9_<PERSON><PERSON>\" title=\"<PERSON>\"><PERSON></a>, French-Canadian neurologist (d. 1986)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Andr%C3%A9_<PERSON>au\" title=\"<PERSON>\"><PERSON></a>, French-Canadian neurologist (d. 1986)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Andr%C3%A9_<PERSON><PERSON><PERSON>"}]}, {"year": "1931", "text": "<PERSON>, English field marshal and politician, Governor of Gibraltar (d. 2022)", "html": "1931 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(British_Army_officer)\" title=\"<PERSON> (British Army officer)\"><PERSON></a>, English field marshal and politician, <a href=\"https://wikipedia.org/wiki/Governor_of_Gibraltar\" title=\"Governor of Gibraltar\">Governor of Gibraltar</a> (d. 2022)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(British_Army_officer)\" title=\"<PERSON> (British Army officer)\"><PERSON></a>, English field marshal and politician, <a href=\"https://wikipedia.org/wiki/Governor_of_Gibraltar\" title=\"Governor of Gibraltar\">Governor of Gibraltar</a> (d. 2022)", "links": [{"title": "<PERSON> (British Army officer)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(British_Army_officer)"}, {"title": "Governor of Gibraltar", "link": "https://wikipedia.org/wiki/Governor_of_Gibraltar"}]}, {"year": "1931", "text": "<PERSON>, French actor (d. 2002)", "html": "1931 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French actor (d. 2002)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French actor (d. 2002)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1931", "text": "<PERSON><PERSON>, Egyptian actress and producer (d. 2015)", "html": "1931 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Egyptian actress and producer (d. 2015)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Egyptian actress and producer (d. 2015)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1931", "text": "<PERSON>, American author and professor", "html": "1931 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author and professor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author and professor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1933", "text": "<PERSON>, Canadian businessman (d. 2008)", "html": "1933 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, Canadian businessman (d. 2008)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, Canadian businessman (d. 2008)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1933", "text": "<PERSON>, Spanish author and illustrator (d. 2007)", "html": "1933 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Spanish author and illustrator (d. 2007)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Spanish author and illustrator (d. 2007)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1934", "text": "<PERSON>, Canadian-American baseball player (d. 2020)", "html": "1934 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian-American baseball player (d. 2020)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian-American baseball player (d. 2020)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1934", "text": "<PERSON>, American author and screenwriter (d. 2018)", "html": "1934 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author and screenwriter (d. 2018)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author and screenwriter (d. 2018)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1935", "text": "<PERSON>, American football player and coach (d. 2014)", "html": "1935 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, American football player and coach (d. 2014)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, American football player and coach (d. 2014)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1935", "text": "<PERSON>, British road manager of The Beatles (d. 1976)", "html": "1935 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, British road manager of <a href=\"https://wikipedia.org/wiki/The_Beatles\" title=\"The Beatles\">The Beatles</a> (d. 1976)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, British road manager of <a href=\"https://wikipedia.org/wiki/The_Beatles\" title=\"The Beatles\">The Beatles</a> (d. 1976)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "The Beatles", "link": "https://wikipedia.org/wiki/The_Beatles"}]}, {"year": "1935", "text": "<PERSON>, American baseball player and coach (d. 2017)", "html": "1935 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player and coach (d. 2017)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player and coach (d. 2017)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1935", "text": "<PERSON>, American jazz pianist and composer (d. 2022)", "html": "1935 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American jazz pianist and composer (d. 2022)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American jazz pianist and composer (d. 2022)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1935", "text": "<PERSON>, American model and actress, Miss America 1955", "html": "1935 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American model and actress, <a href=\"https://wikipedia.org/wiki/Miss_America_1955\" title=\"Miss America 1955\">Miss America 1955</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American model and actress, <a href=\"https://wikipedia.org/wiki/Miss_America_1955\" title=\"Miss America 1955\">Miss America 1955</a>", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Miss America 1955", "link": "https://wikipedia.org/wiki/Miss_America_1955"}]}, {"year": "1936", "text": "<PERSON>, English admiral", "html": "1936 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(Royal_Navy_officer)\" title=\"<PERSON> (Royal Navy officer)\"><PERSON></a>, English admiral", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(Royal_Navy_officer)\" title=\"<PERSON> (Royal Navy officer)\"><PERSON></a>, English admiral", "links": [{"title": "<PERSON> (Royal Navy officer)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(Royal_Navy_officer)"}]}, {"year": "1936", "text": "<PERSON>, American actor and producer (d. 2024)", "html": "1936 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>.\" title=\"<PERSON>\"><PERSON>.</a>, American actor and producer (d. 2024)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>.\" title=\"<PERSON>\"><PERSON>.</a>, American actor and producer (d. 2024)", "links": [{"title": "<PERSON>.", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>."}]}, {"year": "1936", "text": "<PERSON>, Canadian educator and politician, 29th Canadian Minister of National Defence (d. 2014)", "html": "1936 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian educator and politician, 29th <a href=\"https://wikipedia.org/wiki/Minister_of_National_Defence_(Canada)\" title=\"Minister of National Defence (Canada)\">Canadian Minister of National Defence</a> (d. 2014)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian educator and politician, 29th <a href=\"https://wikipedia.org/wiki/Minister_of_National_Defence_(Canada)\" title=\"Minister of National Defence (Canada)\">Canadian Minister of National Defence</a> (d. 2014)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Minister of National Defence (Canada)", "link": "https://wikipedia.org/wiki/Minister_of_National_Defence_(Canada)"}]}, {"year": "1937", "text": "<PERSON>, American playwright and producer (d. 1999)", "html": "1937 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American playwright and producer (d. 1999)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American playwright and producer (d. 1999)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1939", "text": "<PERSON>, 6th <PERSON>, English courtier and businessman", "html": "1939 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_6th_<PERSON>_<PERSON>\" title=\"<PERSON>, 6th <PERSON>\"><PERSON>, 6th Earl <PERSON></a>, English courtier and businessman", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>,_6th_<PERSON>_<PERSON>\" title=\"<PERSON>, 6th <PERSON>\"><PERSON>, 6th <PERSON></a>, English courtier and businessman", "links": [{"title": "<PERSON>, 6th <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_6th_<PERSON>_<PERSON>"}]}, {"year": "1939", "text": "<PERSON>, Canadian captain and politician", "html": "1939 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian captain and politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian captain and politician", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1939", "text": "<PERSON><PERSON><PERSON>, Greek businessman", "html": "1939 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Greek businessman", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Greek businessman", "links": [{"title": "Sokra<PERSON>", "link": "https://wikipedia.org/wiki/Sokratis_<PERSON>"}]}, {"year": "1939", "text": "<PERSON>, English businessman and philanthropist", "html": "1939 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English businessman and philanthropist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English businessman and philanthropist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1939", "text": "<PERSON>, Mexican-American advertising and marketing executive", "html": "1939 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Mexican-American advertising and marketing executive", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Mexican-American advertising and marketing executive", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1939", "text": "<PERSON>, American singer-songwriter and guitarist (d. 2017)", "html": "1939 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and guitarist (d. 2017)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and guitarist (d. 2017)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1940", "text": "<PERSON>, Australian journalist and sportscaster (d. 2015)", "html": "1940 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(sports_journalist)\" title=\"<PERSON> (sports journalist)\"><PERSON></a>, Australian journalist and sportscaster (d. 2015)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(sports_journalist)\" title=\"<PERSON> (sports journalist)\"><PERSON></a>, Australian journalist and sportscaster (d. 2015)", "links": [{"title": "<PERSON> (sports journalist)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(sports_journalist)"}]}, {"year": "1942", "text": "<PERSON>, American police officer", "html": "1942 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American police officer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American police officer", "links": [{"title": "Lee Ba<PERSON>", "link": "https://wikipedia.org/wiki/Lee_<PERSON>"}]}, {"year": "1942", "text": "<PERSON><PERSON>, English racing driver (d. 1970)", "html": "1942 - <a href=\"https://wikipedia.org/wiki/Piers_Courage\" title=\"Piers Courage\"><PERSON><PERSON></a>, English racing driver (d. 1970)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>s_Courage\" title=\"Piers Courage\"><PERSON><PERSON></a>, English racing driver (d. 1970)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>s_Courage"}]}, {"year": "1942", "text": "<PERSON>, Baron <PERSON>, English accountant and politician, Chancellor of the Duchy of Lancaster", "html": "1942 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Baron_<PERSON>\" title=\"<PERSON>, Baron <PERSON>\"><PERSON>, Baron <PERSON></a>, English accountant and politician, <a href=\"https://wikipedia.org/wiki/Chancellor_of_the_Duchy_of_Lancaster\" title=\"Chancellor of the Duchy of Lancaster\">Chancellor of the Duchy of Lancaster</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>,_Baron_<PERSON>\" title=\"<PERSON>, Baron <PERSON>\"><PERSON>, Baron <PERSON></a>, English accountant and politician, <a href=\"https://wikipedia.org/wiki/Chancellor_of_the_Duchy_of_Lancaster\" title=\"Chancellor of the Duchy of Lancaster\">Chancellor of the Duchy of Lancaster</a>", "links": [{"title": "<PERSON>, Baron <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_<PERSON>_<PERSON>"}, {"title": "Chancellor of the Duchy of Lancaster", "link": "https://wikipedia.org/wiki/Chancellor_of_the_Duchy_of_Lancaster"}]}, {"year": "1942", "text": "<PERSON>, English racing driver", "html": "1942 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English racing driver", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English racing driver", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>do<PERSON>"}]}, {"year": "1943", "text": "<PERSON><PERSON>, English singer and actress (d. 2015)", "html": "1943 - <a href=\"https://wikipedia.org/wiki/Cilla_Black\" title=\"Cilla Black\"><PERSON><PERSON></a>, English singer and actress (d. 2015)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Cilla_Black\" title=\"Cilla Black\"><PERSON><PERSON></a>, English singer and actress (d. 2015)", "links": [{"title": "Cilla Black", "link": "https://wikipedia.org/wiki/<PERSON>illa_Black"}]}, {"year": "1943", "text": "<PERSON>, American actor", "html": "1943 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1944", "text": "<PERSON>, American lawyer and politician", "html": "1944 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, American lawyer and politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, American lawyer and politician", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1944", "text": "<PERSON>, Norwegian handball player", "html": "1944 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Norwegian handball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Norwegian handball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1944", "text": "<PERSON>, English historian and politician, Lord Lieutenant of West Yorkshire (d. 2020)", "html": "1944 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English historian and politician, <a href=\"https://wikipedia.org/wiki/Lord_Lieutenant_of_West_Yorkshire\" title=\"Lord Lieutenant of West Yorkshire\">Lord Lieutenant of West Yorkshire</a> (d. 2020)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English historian and politician, <a href=\"https://wikipedia.org/wiki/Lord_Lieutenant_of_West_Yorkshire\" title=\"Lord Lieutenant of West Yorkshire\">Lord Lieutenant of West Yorkshire</a> (d. 2020)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Lord Lieutenant of West Yorkshire", "link": "https://wikipedia.org/wiki/Lord_Lieutenant_of_West_Yorkshire"}]}, {"year": "1944", "text": "<PERSON>, French singer-songwriter, guitarist, and actor", "html": "1944 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French singer-songwriter, guitarist, and actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French singer-songwriter, guitarist, and actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1945", "text": "<PERSON>, Canadian singer-songwriter and guitarist", "html": "1945 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian singer-songwriter and guitarist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian singer-songwriter and guitarist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1946", "text": "<PERSON><PERSON><PERSON><PERSON><PERSON>, Danish bassist and composer (d. 2005)", "html": "1946 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>-<PERSON><PERSON>_%C3%98<PERSON><PERSON>_<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON>-<PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON></a>, Danish bassist and composer (d. 2005)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>-<PERSON><PERSON>_%C3%98<PERSON><PERSON>_<PERSON><PERSON>\" title=\"<PERSON><PERSON>-<PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON></a>, Danish bassist and composer (d. 2005)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>-<PERSON><PERSON>_%C3%98rst<PERSON>_<PERSON><PERSON><PERSON>"}]}, {"year": "1946", "text": "<PERSON>, English motorcycle racer (d. 1978)", "html": "1946 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(motorcyclist)\" title=\"<PERSON> (motorcyclist)\"><PERSON></a>, English motorcycle racer (d. 1978)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(motorcyclist)\" title=\"<PERSON> (motorcyclist)\"><PERSON></a>, English motorcycle racer (d. 1978)", "links": [{"title": "<PERSON> (motorcyclist)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(motorcyclist)"}]}, {"year": "1947", "text": "<PERSON>, American politician", "html": "1947 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American politician", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1947", "text": "<PERSON>, German-Australian singer-songwriter, guitarist, and actor", "html": "1947 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German-Australian singer-songwriter, guitarist, and actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German-Australian singer-songwriter, guitarist, and actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1947", "text": "<PERSON><PERSON><PERSON>, Slovenian footballer and coach", "html": "1947 - <a href=\"https://wikipedia.org/wiki/Branko_O<PERSON>k\" title=\"Brank<PERSON> Oblak\"><PERSON><PERSON><PERSON></a>, Slovenian footballer and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Brank<PERSON>_O<PERSON>k\" title=\"Brank<PERSON> Oblak\"><PERSON><PERSON><PERSON></a>, Slovenian footballer and coach", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>ranko_O<PERSON>k"}]}, {"year": "1947", "text": "<PERSON><PERSON><PERSON>, Estonian politician, 19th Estonian Minister of Foreign Affairs", "html": "1947 - <a href=\"https://wikipedia.org/wiki/Riivo_Sinij%C3%A4rv\" title=\"Riivo Sinijärv\">Rii<PERSON></a>, Estonian politician, 19th <a href=\"https://wikipedia.org/wiki/Minister_of_Foreign_Affairs_(Estonia)\" title=\"Minister of Foreign Affairs (Estonia)\">Estonian Minister of Foreign Affairs</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Riivo_Sinij%C3%A4rv\" title=\"Riivo Sinijärv\"><PERSON>ii<PERSON></a>, Estonian politician, 19th <a href=\"https://wikipedia.org/wiki/Minister_of_Foreign_Affairs_(Estonia)\" title=\"Minister of Foreign Affairs (Estonia)\">Estonian Minister of Foreign Affairs</a>", "links": [{"title": "Riivo Sinijärv", "link": "https://wikipedia.org/wiki/Riivo_Sinij%C3%A4rv"}, {"title": "Minister of Foreign Affairs (Estonia)", "link": "https://wikipedia.org/wiki/Minister_of_Foreign_Affairs_(Estonia)"}]}, {"year": "1948", "text": "<PERSON><PERSON><PERSON>, Dutch civil servant (d. 2017)", "html": "1948 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Dutch civil servant (d. 2017)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Dutch civil servant (d. 2017)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1948", "text": "<PERSON>, English bass player", "html": "1948 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English bass player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English bass player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1948", "text": "<PERSON> <PERSON>-<PERSON><PERSON>, American occultist and author (d. 2014)", "html": "1948 - <a href=\"https://wikipedia.org/wiki/Morning_Glory_Zell-Ravenheart\" title=\"Morning Glory Zell-Ravenheart\">Morning Glory Zell-Ravenheart</a>, American occultist and author (d. 2014)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Morning_Glory_Zell-Ravenheart\" title=\"Morning Glory Zell-Ravenheart\">Morning Glory Zell-Ravenheart</a>, American occultist and author (d. 2014)", "links": [{"title": "Morning Glory Zell-<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Morning_Glory_<PERSON><PERSON>-<PERSON>heart"}]}, {"year": "1949", "text": "<PERSON>, 8th Earl of Lonsdale, English politician (d. 2021)", "html": "1949 - <a href=\"https://wikipedia.org/wiki/<PERSON>,_8th_Earl_of_Lonsdale\" title=\"<PERSON>, 8th Earl of Lonsdale\"><PERSON>, 8th Earl of Lonsdale</a>, English politician (d. 2021)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>,_8th_Earl_of_Lonsdale\" title=\"<PERSON>, 8th Earl of Lonsdale\"><PERSON>, 8th Earl of Lonsdale</a>, English politician (d. 2021)", "links": [{"title": "<PERSON>, 8th Earl of Lonsdale", "link": "https://wikipedia.org/wiki/<PERSON>,_8th_Earl_of_Lonsdale"}]}, {"year": "1949", "text": "<PERSON><PERSON>, German runner", "html": "1949 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, German runner", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, German runner", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1950", "text": "<PERSON>, American singer-songwriter and actress", "html": "1950 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and actress", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1950", "text": "<PERSON><PERSON>, Greek basketball player and coach (d. 2015)", "html": "1950 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Greek basketball player and coach (d. 2015)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Greek basketball player and coach (d. 2015)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1951", "text": "<PERSON>, English boxer", "html": "1951 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English boxer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English boxer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1954", "text": "<PERSON>, Australian businesswoman, activist, and politician", "html": "1954 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian businesswoman, activist, and politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian businesswoman, activist, and politician", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1954", "text": "<PERSON>, American football player and coach", "html": "1954 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player and coach", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1955", "text": "<PERSON>, American wrestler, manager, and producer", "html": "1955 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American wrestler, manager, and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American wrestler, manager, and producer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1955", "text": "<PERSON>, American actor, director, and producer", "html": "1955 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor, director, and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor, director, and producer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1955", "text": "<PERSON>, English organist and conductor", "html": "1955 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(organist)\" title=\"<PERSON> (organist)\"><PERSON></a>, English organist and conductor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(organist)\" title=\"<PERSON> (organist)\"><PERSON></a>, English organist and conductor", "links": [{"title": "<PERSON> (organist)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(organist)"}]}, {"year": "1956", "text": "<PERSON>, American journalist", "html": "1956 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American journalist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American journalist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1956", "text": "<PERSON>, English producer and manager, co-founded Ambassador Theatre Group", "html": "1956 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> S<PERSON>\"><PERSON></a>, English producer and manager, co-founded <a href=\"https://wikipedia.org/wiki/Ambassador_Theatre_Group\" class=\"mw-redirect\" title=\"Ambassador Theatre Group\">Ambassador Theatre Group</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> S<PERSON>\"><PERSON></a>, English producer and manager, co-founded <a href=\"https://wikipedia.org/wiki/Ambassador_Theatre_Group\" class=\"mw-redirect\" title=\"Ambassador Theatre Group\">Ambassador Theatre Group</a>", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Ambassador Theatre Group", "link": "https://wikipedia.org/wiki/Ambassador_Theatre_Group"}]}, {"year": "1956", "text": "<PERSON>, Italian director and screenwriter", "html": "1956 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian director and screenwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian director and screenwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Giuseppe_<PERSON>"}]}, {"year": "1957", "text": "<PERSON><PERSON>, Norwegian politician, Norwegian Minister of Labour", "html": "1957 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Norwegian politician, <a href=\"https://wikipedia.org/wiki/Minister_of_Labour_and_Social_Inclusion\" title=\"Minister of Labour and Social Inclusion\">Norwegian Minister of Labour</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON> <PERSON><PERSON></a>, Norwegian politician, <a href=\"https://wikipedia.org/wiki/Minister_of_Labour_and_Social_Inclusion\" title=\"Minister of Labour and Social Inclusion\">Norwegian Minister of Labour</a>", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON><PERSON>_<PERSON>"}, {"title": "Minister of Labour and Social Inclusion", "link": "https://wikipedia.org/wiki/Minister_of_Labour_and_Social_Inclusion"}]}, {"year": "1957", "text": "<PERSON><PERSON>, Indian lawyer and politician, Indian Minister of Transport", "html": "1957 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Indian lawyer and politician, <a href=\"https://wikipedia.org/wiki/Ministry_of_Road_Transport_and_Highways\" title=\"Ministry of Road Transport and Highways\">Indian Minister of Transport</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Indian lawyer and politician, <a href=\"https://wikipedia.org/wiki/Ministry_of_Road_Transport_and_Highways\" title=\"Ministry of Road Transport and Highways\">Indian Minister of Transport</a>", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}, {"title": "Ministry of Road Transport and Highways", "link": "https://wikipedia.org/wiki/Ministry_of_Road_Transport_and_Highways"}]}, {"year": "1957", "text": "<PERSON>, Canadian-American keyboard player and bass player (d. 2016)", "html": "1957 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian-American keyboard player and bass player (d. 2016)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian-American keyboard player and bass player (d. 2016)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1957", "text": "<PERSON><PERSON>, English singer-songwriter, musician, and producer", "html": "1957 - <a href=\"https://wikipedia.org/wiki/Siouxsie_Sioux\" title=\"Siouxsie Sioux\">Siouxsie Sioux</a>, English singer-songwriter, musician, and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Siouxsie_Sioux\" title=\"Siouxsie Sioux\">Siouxsie Sioux</a>, English singer-songwriter, musician, and producer", "links": [{"title": "Siouxsie Sioux", "link": "https://wikipedia.org/wiki/Siouxsie_Sioux"}]}, {"year": "1958", "text": "<PERSON>, English accountant and politician, 682nd Lord Mayor of London", "html": "1958 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English accountant and politician, 682nd <a href=\"https://wikipedia.org/wiki/Lord_Mayor_of_London\" title=\"Lord Mayor of London\">Lord Mayor of London</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English accountant and politician, 682nd <a href=\"https://wikipedia.org/wiki/Lord_Mayor_of_London\" title=\"Lord Mayor of London\">Lord Mayor of London</a>", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Lord Mayor of London", "link": "https://wikipedia.org/wiki/Lord_Mayor_of_London"}]}, {"year": "1958", "text": "<PERSON>, New Zealand singer-songwriter and musician", "html": "1958 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, New Zealand singer-songwriter and musician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Finn\"><PERSON></a>, New Zealand singer-songwriter and musician", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1958", "text": "<PERSON>, Filipino politician, 23rd Secretary of the Interior and Local Government (d. 2012)", "html": "1958 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Filipino politician, 23rd <a href=\"https://wikipedia.org/wiki/Secretary_of_the_Interior_and_Local_Government\" title=\"Secretary of the Interior and Local Government\">Secretary of the Interior and Local Government</a> (d. 2012)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Filipino politician, 23rd <a href=\"https://wikipedia.org/wiki/Secretary_of_the_Interior_and_Local_Government\" title=\"Secretary of the Interior and Local Government\">Secretary of the Interior and Local Government</a> (d. 2012)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Secretary of the Interior and Local Government", "link": "https://wikipedia.org/wiki/Secretary_of_the_Interior_and_Local_Government"}]}, {"year": "1960", "text": "<PERSON>, Canadian ice hockey player and sportscaster", "html": "1960 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player and sportscaster", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player and sportscaster", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>rrien"}]}, {"year": "1961", "text": "<PERSON>, Brazilian runner and coach", "html": "1961 - <a href=\"https://wikipedia.org/wiki/Jos%C3%A9_Lu%C3%ADz_Barbosa\" title=\"<PERSON>\"><PERSON></a>, Brazilian runner and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Jos%C3%A9_Lu%C3%ADz_Barbosa\" title=\"<PERSON>\"><PERSON></a>, Brazilian runner and coach", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Jos%C3%A9_Lu%C3%ADz_Barbosa"}]}, {"year": "1961", "text": "<PERSON><PERSON>, American actress", "html": "1961 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American actress", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Peri_<PERSON><PERSON>"}]}, {"year": "1962", "text": "<PERSON><PERSON>, Mexican footballer", "html": "1962 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Mexican footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Mexican footballer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_Bernal"}]}, {"year": "1962", "text": "<PERSON>, Australian basketball player", "html": "1962 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian basketball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1962", "text": "<PERSON>, American actor, director, producer, and screenwriter", "html": "1962 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(scriptwriter)\" class=\"mw-redirect\" title=\"<PERSON> (scriptwriter)\"><PERSON></a>, American actor, director, producer, and screenwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(scriptwriter)\" class=\"mw-redirect\" title=\"<PERSON> (scriptwriter)\"><PERSON></a>, American actor, director, producer, and screenwriter", "links": [{"title": "<PERSON> (scriptwriter)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(scriptwriter)"}]}, {"year": "1962", "text": "<PERSON>, Israeli-English biologist and academic", "html": "1962 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Israeli-English biologist and academic", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Israeli-English biologist and academic", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "1962", "text": "<PERSON>, Scottish lawyer and politician, Secretary of State for Scotland", "html": "1962 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish lawyer and politician, <a href=\"https://wikipedia.org/wiki/Secretary_of_State_for_Scotland\" title=\"Secretary of State for Scotland\">Secretary of State for Scotland</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish lawyer and politician, <a href=\"https://wikipedia.org/wiki/Secretary_of_State_for_Scotland\" title=\"Secretary of State for Scotland\">Secretary of State for Scotland</a>", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}, {"title": "Secretary of State for Scotland", "link": "https://wikipedia.org/wiki/Secretary_of_State_for_Scotland"}]}, {"year": "1962", "text": "<PERSON>, Indian cricketer, coach and sportscaster", "html": "1962 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Indian cricketer, coach and sportscaster", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Indian cricketer, coach and sportscaster", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1963", "text": "<PERSON><PERSON><PERSON>, Cuban pianist and composer", "html": "1963 - <a href=\"https://wikipedia.org/wiki/Go<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Cuban pianist and composer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Cuban pianist and composer", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Go<PERSON>lo_Rubalcaba"}]}, {"year": "1963", "text": "<PERSON>, Swiss skier", "html": "1963 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swiss skier", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swiss skier", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1964", "text": "<PERSON>, American actor, producer, and screenwriter", "html": "1964 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor, producer, and screenwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor, producer, and screenwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1965", "text": "<PERSON>, American actor", "html": "1965 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1965", "text": "<PERSON>, Australian-English tennis player and sportscaster", "html": "1965 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Pat Cash\"><PERSON></a>, Australian-English tennis player and sportscaster", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Pat Cash\"><PERSON></a>, Australian-English tennis player and sportscaster", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1966", "text": "<PERSON><PERSON>, English chef and author", "html": "1966 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> Blumenthal\"><PERSON><PERSON></a>, English chef and author", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> Blumenthal\"><PERSON><PERSON></a>, English chef and author", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1967", "text": "<PERSON>, English international footballer, coach, and manager", "html": "1967 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English international footballer, coach, and manager", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English international footballer, coach, and manager", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1967", "text": "<PERSON>, American actor", "html": "1967 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1968", "text": "<PERSON>, American baseball player and coach", "html": "1968 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player and coach", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1968", "text": "<PERSON><PERSON><PERSON>, English journalist", "html": "1968 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, English journalist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, English journalist", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1968", "text": "<PERSON><PERSON>, Turkish basketball player and coach", "html": "1968 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Turkish basketball player and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Turkish basketball player and coach", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1968", "text": "<PERSON>, American baseball player and sportscaster", "html": "1968 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player and sportscaster", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player and sportscaster", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1969", "text": "<PERSON>, American baseball player", "html": "1969 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1969", "text": "<PERSON>, American race car driver", "html": "1969 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American race car driver", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American race car driver", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1969", "text": "<PERSON>, American computer scientist and engineer", "html": "1969 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American computer scientist and engineer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American computer scientist and engineer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1970", "text": "<PERSON>, Italian cyclist", "html": "1970 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian cyclist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian cyclist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1970", "text": "<PERSON>, English educator and politician", "html": "1970 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English educator and politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English educator and politician", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1970", "text": "<PERSON>, English actor", "html": "1970 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1970", "text": "<PERSON>, American-born Australian musician", "html": "1970 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(musician)\" title=\"<PERSON> (musician)\"><PERSON></a>, American-born Australian musician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(musician)\" title=\"<PERSON> (musician)\"><PERSON></a>, American-born Australian musician", "links": [{"title": "<PERSON> (musician)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(musician)"}]}, {"year": "1971", "text": "<PERSON><PERSON>, Nauruan politician", "html": "1971 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Nauruan politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Nauruan politician", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1971", "text": "<PERSON>, English actor", "html": "1971 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1971", "text": "<PERSON>, Australian footballer and coach", "html": "1971 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian footballer and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian footballer and coach", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1971", "text": "<PERSON><PERSON>, Estonian author", "html": "1971 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Estonian author", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Estonian author", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1971", "text": "<PERSON>, American rapper and dancer (d. 2002)", "html": "1971 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American rapper and dancer (d. 2002)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American rapper and dancer (d. 2002)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1971", "text": "<PERSON>, English footballer", "html": "1971 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1971", "text": "<PERSON>, South African tennis player", "html": "1971 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, South African tennis player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, South African tennis player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1971", "text": "<PERSON>, British politician, leader of the Women's Equality Party", "html": "1971 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, British politician, leader of the <a href=\"https://wikipedia.org/wiki/Women%27s_Equality_Party\" title=\"Women's Equality Party\">Women's Equality Party</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, British politician, leader of the <a href=\"https://wikipedia.org/wiki/Women%27s_Equality_Party\" title=\"Women's Equality Party\">Women's Equality Party</a>", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Women's Equality Party", "link": "https://wikipedia.org/wiki/Women%27s_Equality_Party"}]}, {"year": "1971", "text": "<PERSON><PERSON><PERSON>, British radio and television broadcaster", "html": "1971 - <a href=\"https://wikipedia.org/wiki/Petroc_Trelawny\" title=\"Petroc Trelawny\">Petroc Trelawny</a>, British radio and television broadcaster", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Petroc_Trelawny\" title=\"Petroc Trelawny\">Petroc Trelawny</a>, British radio and television broadcaster", "links": [{"title": "Petroc Trelawny", "link": "https://wikipedia.org/wiki/Petroc_Trelawny"}]}, {"year": "1972", "text": "<PERSON>, American golfer", "html": "1972 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American golfer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American golfer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1972", "text": "<PERSON>, American football player", "html": "1972 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1972", "text": "<PERSON>, Russian ice hockey player", "html": "1972 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>lov\" title=\"<PERSON>lov\"><PERSON></a>, Russian ice hockey player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>lov\" title=\"<PERSON> So<PERSON>lov\"><PERSON></a>, Russian ice hockey player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Maxim_Sokolov"}]}, {"year": "1973", "text": "<PERSON>, American actor and comedian", "html": "1973 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor and comedian", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor and comedian", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1973", "text": "<PERSON><PERSON>, New Zealand rugby player and coach", "html": "1973 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, New Zealand rugby player and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, New Zealand rugby player and coach", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>"}]}, {"year": "1973", "text": "<PERSON><PERSON><PERSON>, Greek film video, and theatre director, producer and screenwriter", "html": "1973 - <a href=\"https://wikipedia.org/wiki/Yo<PERSON><PERSON>_Lanthimos\" title=\"Yo<PERSON>s Lanthimos\"><PERSON><PERSON><PERSON></a>, Greek film video, and theatre director, producer and screenwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Yo<PERSON><PERSON>_Lanthimos\" title=\"Yo<PERSON><PERSON> Lanthimos\"><PERSON><PERSON><PERSON></a>, Greek film video, and theatre director, producer and screenwriter", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Yo<PERSON>s_<PERSON>nthimos"}]}, {"year": "1974", "text": "<PERSON>, British singer-songwriter", "html": "1974 - <a href=\"https://wikipedia.org/wiki/<PERSON>_Edwards\" title=\"<PERSON> Edwards\"><PERSON></a>, British singer-songwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_Edwards\" title=\"<PERSON> Edwards\"><PERSON></a>, British singer-songwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1974", "text": "<PERSON>, English actress, singer, and television host", "html": "1974 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actress, singer, and television host", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actress, singer, and television host", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1974", "text": "<PERSON>, American singer-songwriter and guitarist", "html": "1974 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and guitarist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and guitarist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1974", "text": "<PERSON>, American football player", "html": "1974 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1975", "text": "<PERSON>, American rapper", "html": "1975 - <a href=\"https://wikipedia.org/wiki/Andr%C3%A9_3000\" title=\"André 3000\"><PERSON></a>, American rapper", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Andr%C3%A9_3000\" title=\"André 3000\"><PERSON></a>, American rapper", "links": [{"title": "<PERSON> 3000", "link": "https://wikipedia.org/wiki/Andr%C3%A9_3000"}]}, {"year": "1975", "text": "<PERSON>, Australian cricketer", "html": "1975 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian cricketer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian cricketer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1975", "text": "<PERSON><PERSON><PERSON><PERSON>, American rapper", "html": "1975 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>s\" title=\"J<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, American rapper", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, American rapper", "links": [{"title": "Jadakiss", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>s"}]}, {"year": "1975", "text": "<PERSON>, English chef and author", "html": "1975 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English chef and author", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English chef and author", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1975", "text": "<PERSON><PERSON><PERSON>, Turkish astrophysicist, astronomer, and academic", "html": "1975 - <a href=\"https://wikipedia.org/wiki/Feryal_%C3%96zel\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Turkish astrophysicist, astronomer, and academic", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Feryal_%C3%96zel\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Turkish astrophysicist, astronomer, and academic", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Feryal_%C3%96zel"}]}, {"year": "1976", "text": "<PERSON>, Swiss racing driver", "html": "1976 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%<PERSON><PERSON><PERSON>_(racing_driver)\" title=\"<PERSON> (racing driver)\"><PERSON></a>, Swiss racing driver", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%<PERSON><PERSON><PERSON>_(racing_driver)\" title=\"<PERSON> (racing driver)\"><PERSON></a>, Swiss racing driver", "links": [{"title": "<PERSON> (racing driver)", "link": "https://wikipedia.org/wiki/Marcel_F%C3%A<PERSON><PERSON>_(racing_driver)"}]}, {"year": "1977", "text": "<PERSON><PERSON><PERSON><PERSON><PERSON>, Algerian high jumper", "html": "1977 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON><PERSON></a>, Algerian high jumper", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON><PERSON></a>, Algerian high jumper", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1977", "text": "<PERSON><PERSON><PERSON>, Sri Lankan cricketer", "html": "1977 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Sri Lankan cricketer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Sri Lankan cricketer", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1978", "text": "<PERSON><PERSON>, American soccer player", "html": "1978 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American soccer player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American soccer player", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1979", "text": "<PERSON>, American author and illustrator (d. 2004)", "html": "1979 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author and illustrator (d. 2004)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author and illustrator (d. 2004)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1979", "text": "<PERSON>, Australian footballer", "html": "1979 - <a href=\"https://wikipedia.org/wiki/Mile_Sterjovski\" title=\"Mile Sterjovski\"><PERSON></a>, Australian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Mile_Sterjovski\" title=\"Mile Sterjovski\"><PERSON></a>, Australian footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Mile_Sterjovski"}]}, {"year": "1980", "text": "<PERSON>, Canadian figure skater", "html": "1980 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian figure skater", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian figure skater", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1981", "text": "<PERSON><PERSON>, Romanian ballerina", "html": "1981 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Romanian ballerina", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Romanian ballerina", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1981", "text": "<PERSON>, Swedish footballer", "html": "1981 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swedish footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swedish footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1982", "text": "<PERSON><PERSON>, Canadian professional wrestler", "html": "1982 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON><PERSON></a>, Canadian professional wrestler", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON><PERSON></a>, Canadian professional wrestler", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1984", "text": "<PERSON>, American basketball player", "html": "1984 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1984", "text": "<PERSON>, Mexican baseball pitcher", "html": "1984 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%<PERSON><PERSON><PERSON>_(pitcher)\" title=\"<PERSON> (pitcher)\"><PERSON></a>, Mexican baseball pitcher", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%<PERSON><PERSON><PERSON>_(pitcher)\" title=\"<PERSON> (pitcher)\"><PERSON></a>, Mexican baseball pitcher", "links": [{"title": "<PERSON> (pitcher)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%<PERSON><PERSON><PERSON>_(pitcher)"}]}, {"year": "1985", "text": "<PERSON>, Taiwanese baseball player", "html": "1985 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>-ming\" title=\"<PERSON>-ming\"><PERSON>-min<PERSON></a>, Taiwanese baseball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>-ming\" title=\"<PERSON>-ming\"><PERSON>-min<PERSON></a>, Taiwanese baseball player", "links": [{"title": "<PERSON>g", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>-ming"}]}, {"year": "1985", "text": "<PERSON>, Spanish footballer", "html": "1985 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Spanish footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Spanish footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1986", "text": "<PERSON>, Manx motorcycle racer", "html": "1986 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Manx motorcycle racer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Manx motorcycle racer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1986", "text": "<PERSON><PERSON>, Senegalese basketball player", "html": "1986 - <a href=\"https://wikipedia.org/wiki/Bamba_Fall\" title=\"Bamba Fall\"><PERSON><PERSON> Fall</a>, Senegalese basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Bamba_Fall\" title=\"Bamba Fall\"><PERSON><PERSON> Fall</a>, Senegalese basketball player", "links": [{"title": "Bamba Fall", "link": "https://wikipedia.org/wiki/Bamba_Fall"}]}, {"year": "1986", "text": "<PERSON><PERSON>, Danish footballer", "html": "1986 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>h%C3%B6ne\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Danish footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>h%C3%B6ne\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Danish footballer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Lasse_Sch%C3%B6ne"}]}, {"year": "1987", "text": "<PERSON><PERSON><PERSON><PERSON>, Ivorian footballer", "html": "1987 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Ivorian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Ivorian footballer", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>"}]}, {"year": "1987", "text": "<PERSON>, Australian actress", "html": "1987 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian actress", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Bella_<PERSON>cote"}]}, {"year": "1987", "text": "<PERSON><PERSON>, Turkish basketball player", "html": "1987 - <a href=\"https://wikipedia.org/wiki/Bora_Pa%C3%A7un\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Turkish basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Bora_Pa%C3%A7un\" title=\"<PERSON><PERSON>çun\"><PERSON><PERSON></a>, Turkish basketball player", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Bora_Pa%C3%A7un"}]}, {"year": "1987", "text": "<PERSON>, Australian rugby league player", "html": "1987 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(rugby_league)\" title=\"<PERSON> (rugby league)\"><PERSON></a>, Australian rugby league player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(rugby_league)\" title=\"<PERSON> (rugby league)\"><PERSON></a>, Australian rugby league player", "links": [{"title": "<PERSON> (rugby league)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(rugby_league)"}]}, {"year": "1987", "text": "<PERSON>, Czech speed skater and cyclist", "html": "1987 - <a href=\"https://wikipedia.org/wiki/Martina_S%C3%A1bl%C3%ADkov%C3%A1\" title=\"<PERSON>\"><PERSON></a>, Czech speed skater and cyclist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Martina_S%C3%A1bl%C3%ADkov%C3%A1\" title=\"<PERSON>\"><PERSON></a>, Czech speed skater and cyclist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Martina_S%C3%A1bl%C3%ADkov%C3%A1"}]}, {"year": "1988", "text": "<PERSON><PERSON><PERSON>, American football player (d. 2024)", "html": "1988 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American football player (d. 2024)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American football player (d. 2024)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1988", "text": "<PERSON><PERSON>, Russian hurdler", "html": "1988 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Russian hurdler", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Russian hurdler", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1988", "text": "<PERSON>, American baseball pitcher", "html": "1988 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball pitcher", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball pitcher", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1988", "text": "<PERSON>, American football player (d. 2015)", "html": "1988 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player (d. 2015)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player (d. 2015)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1989", "text": "<PERSON>, Estonian footballer", "html": "1989 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(footballer)\" title=\"<PERSON> (footballer)\"><PERSON></a>, Estonian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>(footballer)\" title=\"<PERSON> (footballer)\"><PERSON></a>, Estonian footballer", "links": [{"title": "<PERSON> (footballer)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(footballer)"}]}, {"year": "1989", "text": "<PERSON><PERSON>, South Korean rapper, record producer, and singer-songwriter", "html": "1989 - <a href=\"https://wikipedia.org/wiki/Peakboy\" title=\"Peakboy\"><PERSON><PERSON></a>, South Korean rapper, record producer, and singer-songwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Peakboy\" title=\"Peakboy\"><PERSON><PERSON></a>, South Korean rapper, record producer, and singer-songwriter", "links": [{"title": "Peakboy", "link": "https://wikipedia.org/wiki/Peakboy"}]}, {"year": "1990", "text": "<PERSON><PERSON><PERSON>, Ethiopian runner", "html": "1990 - <a href=\"https://wikipedia.org/wiki/Yenew_Alamirew\" title=\"Yenew Alamirew\">Yenew <PERSON>amirew</a>, Ethiopian runner", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Yenew_Alamirew\" title=\"Yenew Alamirew\">Yene<PERSON>amirew</a>, Ethiopian runner", "links": [{"title": "Yenew Alamirew", "link": "https://wikipedia.org/wiki/Yenew_Alamirew"}]}, {"year": "1990", "text": "<PERSON>, American actor and singer", "html": "1990 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor and singer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor and singer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1990", "text": "<PERSON>, Swedish ice hockey player", "html": "1990 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, Swedish ice hockey player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, Swedish ice hockey player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1991", "text": "<PERSON><PERSON><PERSON>, Belgian footballer", "html": "1991 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Belgian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Belgian footballer", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1991", "text": "<PERSON>, Samoan rugby league player", "html": "1991 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Samoan rugby league player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Samoan rugby league player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1991", "text": "<PERSON><PERSON><PERSON>, Russian tennis player", "html": "1991 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Russian tennis player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Russian tennis player", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Ksenia_<PERSON>k"}]}, {"year": "1991", "text": "<PERSON><PERSON>, Estonian footballer", "html": "1991 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Estonian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Estonian footballer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>s"}]}, {"year": "1992", "text": "<PERSON>, Canadian sprinter", "html": "1992 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>(sprinter)\" title=\"<PERSON> (sprinter)\"><PERSON></a>, Canadian sprinter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>(sprinter)\" title=\"<PERSON> (sprinter)\"><PERSON></a>, Canadian sprinter", "links": [{"title": "<PERSON> (sprinter)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(sprinter)"}]}, {"year": "1992", "text": "<PERSON>, Canadian canoer", "html": "1992 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, Canadian canoer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, Canadian canoer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1995", "text": "<PERSON><PERSON>, Cuban baseball player", "html": "1995 - <a href=\"https://wikipedia.org/wiki/Yo%C3%A1n_Moncada\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Cuban baseball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Yo%C3%A1n_Moncada\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Cuban baseball player", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Yo%C3%A1n_Moncada"}]}, {"year": "1996", "text": "<PERSON>, South Korean singer", "html": "1996 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>wan_(singer)\" title=\"<PERSON> (singer)\"><PERSON></a>, South Korean singer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(singer)\" title=\"<PERSON> (singer)\"><PERSON></a>, South Korean singer", "links": [{"title": "<PERSON> (singer)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>-hwan_(singer)"}]}, {"year": "1997", "text": "<PERSON>, Hungarian tennis player", "html": "1997 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, Hungarian tennis player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, Hungarian tennis player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1997", "text": "<PERSON>, American football player", "html": "1997 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(American_football)\" title=\"<PERSON> (American football)\"><PERSON></a>, American football player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(American_football)\" title=\"<PERSON> (American football)\"><PERSON></a>, American football player", "links": [{"title": "<PERSON> (American football)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(American_football)"}]}, {"year": "1999", "text": "<PERSON><PERSON><PERSON>, French-American actress and model", "html": "1999 - <a href=\"https://wikipedia.org/wiki/<PERSON>-<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, French-American actress and model", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>-<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, French-American actress and model", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>-<PERSON>_<PERSON>"}]}], "Deaths": [{"year": "366", "text": "<PERSON><PERSON><PERSON><PERSON>, Roman usurper (b. 325)", "html": "366 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_(usurper)\" title=\"<PERSON><PERSON><PERSON><PERSON> (usurper)\"><PERSON><PERSON><PERSON><PERSON></a>, Roman usurper (b. 325)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_(usurper)\" title=\"<PERSON><PERSON><PERSON><PERSON> (usurper)\"><PERSON><PERSON><PERSON><PERSON></a>, Roman usurper (b. 325)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON> (usurper)", "link": "https://wikipedia.org/wiki/Procopi<PERSON>_(usurper)"}]}, {"year": "398", "text": "<PERSON><PERSON>, emperor of the Xianbei state Later <PERSON> (b. 355)", "html": "398 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, emperor of the <a href=\"https://wikipedia.org/wiki/Xianbei\" title=\"Xianbei\">Xianbei</a> state <a href=\"https://wikipedia.org/wiki/Later_Yan\" title=\"Later Yan\">Later Yan</a> (b. 355)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, emperor of the <a href=\"https://wikipedia.org/wiki/Xianbei\" title=\"Xianbei\">Xianbei</a> state <a href=\"https://wikipedia.org/wiki/Later_Yan\" title=\"Later Yan\">Later Yan</a> (b. 355)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>o"}, {"title": "Xianbei", "link": "https://wikipedia.org/wiki/Xianbei"}, {"title": "Later Yan", "link": "https://wikipedia.org/wiki/Later_Yan"}]}, {"year": "475", "text": "<PERSON><PERSON><PERSON><PERSON>, bishop of Orange", "html": "475 - <a href=\"https://wikipedia.org/wiki/Eutropius_of_Orange\" title=\"Eutropius of Orange\">Eut<PERSON>ius</a>, <a href=\"https://wikipedia.org/wiki/Ancient_Diocese_of_Orange\" title=\"Ancient Diocese of Orange\">bishop of Orange</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Eutropius_of_Orange\" title=\"Eutropius of Orange\">Eut<PERSON><PERSON></a>, <a href=\"https://wikipedia.org/wiki/Ancient_Diocese_of_Orange\" title=\"Ancient Diocese of Orange\">bishop of Orange</a>", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON> of Orange", "link": "https://wikipedia.org/wiki/Eutropius_of_Orange"}, {"title": "Ancient Diocese of Orange", "link": "https://wikipedia.org/wiki/Ancient_Diocese_of_Orange"}]}, {"year": "866", "text": "<PERSON><PERSON><PERSON> of Asturias (b. 831)", "html": "866 - <a href=\"https://wikipedia.org/wiki/Ordo%C3%B1o_I_of_Asturias\" title=\"Ordoño I of Asturias\"><PERSON><PERSON><PERSON> of Asturias</a> (b. 831)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Ordo%C3%B1o_I_of_Asturias\" title=\"Ordoño I of Asturias\">Ordo<PERSON> I of Asturias</a> (b. 831)", "links": [{"title": "Ordoño I of Asturias", "link": "https://wikipedia.org/wiki/Ordo%C3%B1o_I_of_Asturias"}]}, {"year": "927", "text": "<PERSON><PERSON><PERSON> of Bulgaria first Bulgarian Emperor (b. 864)", "html": "927 - <a href=\"https://wikipedia.org/wiki/Simeon_I_of_Bulgaria\" title=\"Simeon I of Bulgaria\">Sime<PERSON> <PERSON> of Bulgaria</a> first Bulgarian Emperor (b. 864)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Simeon_I_of_Bulgaria\" title=\"Simeon I of Bulgaria\"><PERSON><PERSON><PERSON> <PERSON> of Bulgaria</a> first Bulgarian Emperor (b. 864)", "links": [{"title": "Simeon I of Bulgaria", "link": "https://wikipedia.org/wiki/Simeon_I_of_Bulgaria"}]}, {"year": "1039", "text": "<PERSON>, Count of Holland (b. 981)", "html": "1039 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Count_of_Holland\" title=\"<PERSON>, Count of Holland\"><PERSON>, Count of Holland</a> (b. 981)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Count_of_Holland\" title=\"<PERSON>, Count of Holland\"><PERSON>, Count of Holland</a> (b. 981)", "links": [{"title": "<PERSON>, Count of Holland", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Count_of_Holland"}]}, {"year": "1045", "text": "<PERSON> of Würzburg, imperial chancellor of Italy (b. c. 1005)", "html": "1045 - <a href=\"https://wikipedia.org/wiki/<PERSON>_of_W%C3%BCrzburg\" class=\"mw-redirect\" title=\"<PERSON> of Würzburg\"><PERSON> of Würzburg</a>, imperial chancellor of Italy (b. c. 1005)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_of_W%C3%BCrzburg\" class=\"mw-redirect\" title=\"<PERSON> of Würzburg\"><PERSON> of Würzburg</a>, imperial chancellor of Italy (b. c. 1005)", "links": [{"title": "<PERSON> of Würzburg", "link": "https://wikipedia.org/wiki/Bruno_of_W%C3%BCrzburg"}]}, {"year": "1178", "text": "<PERSON>, bishop of Utrecht", "html": "1178 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, bishop of Utrecht", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, bishop of Utrecht", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1240", "text": "<PERSON>, 5th Earl of Surrey (b. 1166)", "html": "1240 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_5th_Earl_of_Surrey\" title=\"<PERSON>, 5th Earl of Surrey\"><PERSON>, 5th Earl of Surrey</a> (b. 1166)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_5th_Earl_of_Surrey\" title=\"<PERSON>, 5th Earl of Surrey\"><PERSON>, 5th Earl of Surrey</a> (b. 1166)", "links": [{"title": "<PERSON>, 5th Earl of Surrey", "link": "https://wikipedia.org/wiki/<PERSON>,_5th_Earl_of_Surrey"}]}, {"year": "1444", "text": "<PERSON>, 1st Duke of Somerset, English commander (b. 1404)", "html": "1444 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_1st_Duke_of_Somerset\" title=\"<PERSON>, 1st Duke of Somerset\"><PERSON>, 1st Duke of Somerset</a>, English commander (b. 1404)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>,_1st_Duke_of_Somerset\" title=\"<PERSON>, 1st Duke of Somerset\"><PERSON>, 1st Duke of Somerset</a>, English commander (b. 1404)", "links": [{"title": "<PERSON>, 1st Duke of Somerset", "link": "https://wikipedia.org/wiki/<PERSON>,_1st_Duke_of_Somerset"}]}, {"year": "1508", "text": "<PERSON><PERSON><PERSON><PERSON>, Duke of Milan (b. 1452)", "html": "1508 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Duke of Milan (b. 1452)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Duke of Milan (b. 1452)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>a"}]}, {"year": "1525", "text": "<PERSON>, German mystic and theologian (b. 1488)", "html": "1525 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%BCntzer\" title=\"<PERSON>\"><PERSON></a>, German mystic and theologian (b. 1488)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%BC<PERSON>zer\" title=\"<PERSON>\"><PERSON></a>, German mystic and theologian (b. 1488)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Thomas_M%C3%BCntzer"}]}, {"year": "1541", "text": "<PERSON>, Countess of Salisbury (b. 1473)", "html": "1541 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Countess_of_Salisbury\" title=\"<PERSON>, Countess of Salisbury\"><PERSON>, Countess of Salisbury</a> (b. 1473)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Countess_of_Salisbury\" title=\"<PERSON>, Countess of Salisbury\"><PERSON>, Countess of Salisbury</a> (b. 1473)", "links": [{"title": "<PERSON>, Countess of Salisbury", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Countess_of_Salisbury"}]}, {"year": "1564", "text": "<PERSON>, French pastor and theologian (b. 1509)", "html": "1564 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French pastor and theologian (b. 1509)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French pastor and theologian (b. 1509)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1610", "text": "<PERSON>, French assassin of <PERSON> of France (b. 1578)", "html": "1610 - <a href=\"https://wikipedia.org/wiki/Fran%C3%A7ois_Ra<PERSON>c\" title=\"<PERSON>\"><PERSON></a>, French assassin of <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_France\" title=\"Henry IV of France\"><PERSON> of France</a> (b. 1578)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Fran%C3%A7ois_Ra<PERSON>c\" title=\"<PERSON>\"><PERSON></a>, French assassin of <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_France\" title=\"Henry IV of France\"><PERSON> of France</a> (b. 1578)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Fran%C3%A7ois_Ravaillac"}, {"title": "<PERSON> of France", "link": "https://wikipedia.org/wiki/<PERSON>_IV_of_France"}]}, {"year": "1624", "text": "<PERSON>, Spanish sailor and cosmographer (b. c. 1580)", "html": "1624 - <a href=\"https://wikipedia.org/wiki/Diego_Ram%C3%<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Spanish sailor and cosmographer (b. c. 1580)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Diego_Ram%C3%<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Spanish sailor and cosmographer (b. c. 1580)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Diego_Ram%C3%<PERSON><PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1637", "text": "<PERSON>, 1st Baron <PERSON> of Brantfield, English politician (b. c. 1566)", "html": "1637 - <a href=\"https://wikipedia.org/wiki/<PERSON>,_1st_Baron_<PERSON>_of_Brantfield\" title=\"<PERSON>, 1st Baron <PERSON> of Brantfield\"><PERSON>, 1st Baron <PERSON> of Brantfield</a>, English politician (b. c. 1566)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>,_1st_Baron_<PERSON>_of_Brantfield\" title=\"<PERSON>, 1st Baron <PERSON> of Brantfield\"><PERSON>, 1st Baron <PERSON> of Brantfield</a>, English politician (b. c. 1566)", "links": [{"title": "<PERSON>, 1st Baron <PERSON> of Brantfield", "link": "https://wikipedia.org/wiki/<PERSON>,_1st_Baron_<PERSON>_of_Brantfield"}]}, {"year": "1661", "text": "<PERSON>, 1st Marquess of Argyll, Scottish general and politician (b. 1607)", "html": "1661 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_1st_Marquess_of_Argyll\" title=\"<PERSON>, 1st Marquess of Argyll\"><PERSON>, 1st Marquess of Argyll</a>, Scottish general and politician (b. 1607)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_1st_Marquess_of_Argyll\" title=\"<PERSON>, 1st Marquess of Argyll\"><PERSON>, 1st Marquess of Argyll</a>, Scottish general and politician (b. 1607)", "links": [{"title": "<PERSON>, 1st Marquess of Argyll", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_1st_Marquess_of_Argyll"}]}, {"year": "1675", "text": "<PERSON><PERSON>, Italian-French painter (b. 1613)", "html": "1675 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_Dughet\" title=\"<PERSON>pard Dughet\"><PERSON><PERSON></a>, Italian-French painter (b. 1613)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_Dughet\" title=\"<PERSON>pard Dughet\"><PERSON><PERSON></a>, Italian-French painter (b. 1613)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>ghet"}]}, {"year": "1690", "text": "<PERSON>, Italian organist and composer (b. 1626)", "html": "1690 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian organist and composer (b. 1626)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian organist and composer (b. 1626)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1702", "text": "<PERSON>, French priest and critic (b. 1628)", "html": "1702 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French priest and critic (b. 1628)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French priest and critic (b. 1628)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1707", "text": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, marquise <PERSON>, French mistress of <PERSON> of France (b. 1640)", "html": "1707 - <a href=\"https://wikipedia.org/wiki/Fran%C3%A7oise-Ath%C3%A9na%C3%A<PERSON>s,_marquise_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, marquise <PERSON>\"><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, marquise <PERSON></a>, French mistress of <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON> XIV\"><PERSON></a> of France (b. 1640)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Fran%C3%A7oise-Ath%C3%A9na%C3%AFs,_marquise_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, marquise de <PERSON>\"><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, marquise <PERSON></a>, French mistress of <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> of France (b. 1640)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, marquise <PERSON>", "link": "https://wikipedia.org/wiki/Fran%C3%A7oise-Ath%C3%A9na%C3%AFs,_marquise_<PERSON>_<PERSON>"}, {"title": "Louis XIV", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1781", "text": "<PERSON>, Italian physicist and academic (b. 1716)", "html": "1781 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian physicist and academic (b. 1716)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian physicist and academic (b. 1716)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1797", "text": "<PERSON><PERSON><PERSON><PERSON>, French journalist (b. 1760)", "html": "1797 - <a href=\"https://wikipedia.org/wiki/Fran%C3%A7ois-No%C3%AB<PERSON>_<PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, French journalist (b. 1760)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Fran%C3%A7ois-No%C3%AB<PERSON>_<PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, French journalist (b. 1760)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Fran%C3%A7ois-No%C3%ABl_<PERSON>uf"}]}, {"year": "1831", "text": "<PERSON><PERSON><PERSON>, American hunter, explorer, and author (b. 1799)", "html": "1831 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American hunter, explorer, and author (b. 1799)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American hunter, explorer, and author (b. 1799)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1840", "text": "<PERSON><PERSON><PERSON><PERSON>, Italian violinist and composer (b. 1782)", "html": "1840 - <a href=\"https://wikipedia.org/wiki/Niccol%C3%B2_<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Italian violinist and composer (b. 1782)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Niccol%C3%B2_<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Italian violinist and composer (b. 1782)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Niccol%C3%B2_<PERSON><PERSON><PERSON>"}]}, {"year": "1867", "text": "<PERSON> American mythologist (b. 1796)", "html": "1867 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> American mythologist (b. 1796)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> American mythologist (b. 1796)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1896", "text": "<PERSON>, Russian physicist, engineer, and academic (b. 1839)", "html": "1896 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian physicist, engineer, and academic (b. 1839)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian physicist, engineer, and academic (b. 1839)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1910", "text": "<PERSON>, German physician and microbiologist, Nobel Prize laureate (b. 1843)", "html": "1910 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German physician and microbiologist, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Physiology_or_Medicine\" title=\"Nobel Prize in Physiology or Medicine\">Nobel Prize</a> laureate (b. 1843)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German physician and microbiologist, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Physiology_or_Medicine\" title=\"Nobel Prize in Physiology or Medicine\">Nobel Prize</a> laureate (b. 1843)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Nobel Prize in Physiology or Medicine", "link": "https://wikipedia.org/wiki/Nobel_Prize_in_Physiology_or_Medicine"}]}, {"year": "1918", "text": "<PERSON><PERSON><PERSON>, Japanese sumo wrestler, the 18th <PERSON><PERSON><PERSON><PERSON> (b. 1869)", "html": "1918 - <a href=\"https://wikipedia.org/wiki/%C5%8Czutsu_Man%27emon\" title=\"Ōzutsu Man'emon\"><PERSON><PERSON><PERSON> Man'emon</a>, Japanese sumo wrestler, the 18th <a href=\"https://wikipedia.org/wiki/Ma<PERSON>uchi#Yokozuna\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a> (b. 1869)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/%C5%8Czutsu_Man%27emon\" title=\"Ōzutsu Man'emon\"><PERSON><PERSON><PERSON> Man'emon</a>, Japanese sumo wrestler, the 18th <a href=\"https://wikipedia.org/wiki/Ma<PERSON>uchi#Yokozuna\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a> (b. 1869)", "links": [{"title": "Ōzutsu Man'emon", "link": "https://wikipedia.org/wiki/%C5%8Czutsu_Man%27emon"}, {"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Makuuchi#Yokozuna"}]}, {"year": "1919", "text": "<PERSON><PERSON><PERSON><PERSON>, Indian author and activist (b. 1848)", "html": "1919 - <a href=\"https://wikipedia.org/wiki/Kand<PERSON><PERSON>_<PERSON>\" title=\"Kand<PERSON><PERSON> Veeresaling<PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Indian author and activist (b. 1848)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Kand<PERSON><PERSON>_<PERSON>\" title=\"Kand<PERSON><PERSON> Veeresaling<PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Indian author and activist (b. 1848)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1933", "text": "<PERSON><PERSON><PERSON>, French target shooter (b. 1868)", "html": "1933 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, French target shooter (b. 1868)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, French target shooter (b. 1868)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/A<PERSON><PERSON>_<PERSON>"}]}, {"year": "1939", "text": "<PERSON>, Austrian-French journalist and author (b. 1894)", "html": "1939 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Austrian-French journalist and author (b. 1894)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Austrian-French journalist and author (b. 1894)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1941", "text": "<PERSON>, German captain (b. 1894)", "html": "1941 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German captain (b. 1894)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German captain (b. 1894)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1941", "text": "<PERSON><PERSON><PERSON><PERSON>, German admiral (b. 1889)", "html": "1941 - <a href=\"https://wikipedia.org/wiki/G%C3%BCnther_L%C3%BCtjens\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, German admiral (b. 1889)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/G%C3%BCnther_L%C3%BCtjens\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, German admiral (b. 1889)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/G%C3%BCnther_L%C3%BCtjens"}]}, {"year": "1942", "text": "<PERSON><PERSON><PERSON>, Turkish theologian, logician, and translator (b. 1878)", "html": "1942 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>%C4%B1r\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Turkish theologian, logician, and translator (b. 1878)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>%C4%B1r\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Turkish theologian, logician, and translator (b. 1878)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>%C4%B1r"}]}, {"year": "1943", "text": "<PERSON>, New Zealand soldier and politician, 21st Prime Minister of New Zealand (b. 1878)", "html": "1943 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, New Zealand soldier and politician, 21st <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_New_Zealand\" title=\"Prime Minister of New Zealand\">Prime Minister of New Zealand</a> (b. 1878)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, New Zealand soldier and politician, 21st <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_New_Zealand\" title=\"Prime Minister of New Zealand\">Prime Minister of New Zealand</a> (b. 1878)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Gordon_<PERSON>"}, {"title": "Prime Minister of New Zealand", "link": "https://wikipedia.org/wiki/Prime_Minister_of_New_Zealand"}]}, {"year": "1945", "text": "<PERSON><PERSON>, German physician (b. 1888)", "html": "1945 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, German physician (b. 1888)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, German physician (b. 1888)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Enno_Lolling"}]}, {"year": "1947", "text": "<PERSON>, American baseball player and manager (b. 1885)", "html": "1947 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player and manager (b. 1885)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player and manager (b. 1885)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1949", "text": "<PERSON>, American cartoonist, publisher, and businessman, founded R<PERSON>ley's Believe It or Not! (b. 1890)", "html": "1949 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American cartoonist, publisher, and businessman, founded <i><a href=\"https://wikipedia.org/wiki/Ripley%27s_Believe_It_or_Not!\" title=\"<PERSON><PERSON><PERSON>'s Believe It or Not!\"><PERSON><PERSON><PERSON>'s Believe It or Not!</a></i> (b. 1890)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American cartoonist, publisher, and businessman, founded <i><a href=\"https://wikipedia.org/wiki/Rip<PERSON>%27s_Believe_It_or_Not!\" title=\"<PERSON><PERSON><PERSON>'s Believe It or Not!\"><PERSON><PERSON><PERSON>'s Believe It or Not!</a></i> (b. 1890)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "<PERSON><PERSON><PERSON>'s Believe It or Not!", "link": "https://wikipedia.org/wiki/Ripley%27s_Believe_It_or_Not!"}]}, {"year": "1953", "text": "<PERSON>, American baseball player and manager (b. 1868)", "html": "1953 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player and manager (b. 1868)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player and manager (b. 1868)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1960", "text": "<PERSON>, American painter and illustrator (b. 1877)", "html": "1960 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American painter and illustrator (b. 1877)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American painter and illustrator (b. 1877)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1963", "text": "<PERSON><PERSON><PERSON>, Greek physician and politician (b. 1912)", "html": "1963 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Greek physician and politician (b. 1912)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Greek physician and politician (b. 1912)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1964", "text": "<PERSON><PERSON><PERSON><PERSON>, Indian lawyer and politician, 1st Prime Minister of India (b. 1889)", "html": "1964 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Indian lawyer and politician, 1st <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_India\" title=\"Prime Minister of India\">Prime Minister of India</a> (b. 1889)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Indian lawyer and politician, 1st <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_India\" title=\"Prime Minister of India\">Prime Minister of India</a> (b. 1889)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>"}, {"title": "Prime Minister of India", "link": "https://wikipedia.org/wiki/Prime_Minister_of_India"}]}, {"year": "1965", "text": "<PERSON>, American military officer, educator, businessperson, and politician (b. 1905)", "html": "1965 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American military officer, educator, businessperson, and politician (b. 1905)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American military officer, educator, businessperson, and politician (b. 1905)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "1967", "text": "<PERSON><PERSON>, American composer and educator (b. 1880)", "html": "1967 - <a href=\"https://wikipedia.org/wiki/W<PERSON>_<PERSON>\" title=\"<PERSON><PERSON> <PERSON>\"><PERSON><PERSON></a>, American composer and educator (b. 1880)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/W<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American composer and educator (b. 1880)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/W<PERSON>_<PERSON>"}]}, {"year": "1967", "text": "<PERSON>, German academic and politician (b. 1889)", "html": "1967 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German academic and politician (b. 1889)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German academic and politician (b. 1889)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1969", "text": "<PERSON>, American actor and producer (b. 1926)", "html": "1969 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor and producer (b. 1926)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor and producer (b. 1926)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1971", "text": "<PERSON><PERSON><PERSON>, Hungarian-Austrian philosopher from the Vienna Circle (b. 1901)", "html": "1971 - <a href=\"https://wikipedia.org/wiki/B%C3%A9<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Hungarian-Austrian philosopher from the Vienna Circle (b. 1901)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/B%C3%A9<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Hungarian-Austrian philosopher from the Vienna Circle (b. 1901)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/B%C3%A9la_Juhos"}]}, {"year": "1971", "text": "<PERSON><PERSON>, Italian footballer and coach (b. 1935)", "html": "1971 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Italian footballer and coach (b. 1935)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Italian footballer and coach (b. 1935)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1980", "text": "<PERSON><PERSON><PERSON>, Turkish agronomist and politician (b. 1932)", "html": "1980 - <a href=\"https://wikipedia.org/wiki/G%C3%BCn_Sazak\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Turkish agronomist and politician (b. 1932)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/G%C3%BCn_Sazak\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Turkish agronomist and politician (b. 1932)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/G%C3%BCn_Sazak"}]}, {"year": "1984", "text": "<PERSON><PERSON><PERSON><PERSON>, Serbian composer (b. 1923)", "html": "1984 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Serbian composer (b. 1923)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Serbian composer (b. 1923)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>li<PERSON>_<PERSON>nja<PERSON>"}]}, {"year": "1986", "text": "Murder of the Faruqis:\n<PERSON>, Palestinian-American Muslim philosopher and scholar (b. 1921)\n<PERSON>, American scholar of ethnomusicology, wife of <PERSON> (b. 1926)", "html": "1986 - <a href=\"https://wikipedia.org/wiki/Murder_of_the_Faruqis\" title=\"Murder of the Faruqis\">Murder of the Faruqis</a>:\n<ul>\n<li>\n<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Palestinian-American Muslim philosopher and scholar (b. 1921)</li>\n<li>\n<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American scholar of ethnomusicology, wife of <PERSON> (b. 1926)</li>\n</ul>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Murder_of_the_Faruqis\" title=\"Murder of the Faruqis\">Murder of the Faruqis</a>:\n<ul>\n<li>\n<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Palestinian-American Muslim philosopher and scholar (b. 1921)</li>\n<li>\n<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American scholar of ethnomusicology, wife of <PERSON> (b. 1926)</li>\n</ul>", "links": [{"title": "Murder of the Faruqis", "link": "https://wikipedia.org/wiki/Murder_of_the_Faruqis"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>-<PERSON>"}]}, {"year": "<PERSON>, Palestinian-American Muslim philosopher and scholar (b. 1921)", "text": null, "html": "<PERSON>, Palestinian-American Muslim philosopher and scholar (b. 1921) - <a href=\"https://wikipedia.orghttps://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Palestinian-American Muslim philosopher and scholar (b. 1921)", "no_year_html": "<a href=\"https://wikipedia.orghttps://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Palestinian-American Muslim philosopher and scholar (b. 1921)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.orghttps://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "<PERSON>, American scholar of ethnomusicology, wife of <PERSON> (b. 1926)", "text": null, "html": "<PERSON>, American scholar of ethnomusicology, wife of <PERSON> (b. 1926) - <a href=\"https://wikipedia.orghttps://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American scholar of ethnomusicology, wife of <PERSON> (b. 1926)", "no_year_html": "<a href=\"https://wikipedia.orghttps://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American scholar of ethnomusicology, wife of <PERSON> (b. 1926)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.orghttps://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1986", "text": "<PERSON><PERSON>, Indian politician, Chief Minister of West Bengal (b. 1901)", "html": "1986 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Indian politician, <a href=\"https://wikipedia.org/wiki/Chief_Minister_of_West_Bengal\" class=\"mw-redirect\" title=\"Chief Minister of West Bengal\">Chief Minister of West Bengal</a> (b. 1901)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Indian politician, <a href=\"https://wikipedia.org/wiki/Chief_Minister_of_West_Bengal\" class=\"mw-redirect\" title=\"Chief Minister of West Bengal\">Chief Minister of West Bengal</a> (b. 1901)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}, {"title": "Chief Minister of West Bengal", "link": "https://wikipedia.org/wiki/Chief_Minister_of_West_Bengal"}]}, {"year": "1986", "text": "<PERSON><PERSON><PERSON>, Greek actor and cinematographer (b. 1918)", "html": "1986 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Greek actor and cinematographer (b. 1918)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Greek actor and cinematographer (b. 1918)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1987", "text": "<PERSON>, American biochemist and academic, Nobel Prize laureate (b. 1891)", "html": "1987 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American biochemist and academic, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Chemistry\" title=\"Nobel Prize in Chemistry\">Nobel Prize</a> laureate (b. 1891)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American biochemist and academic, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Chemistry\" title=\"Nobel Prize in Chemistry\">Nobel Prize</a> laureate (b. 1891)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Nobel Prize in Chemistry", "link": "https://wikipedia.org/wiki/Nobel_Prize_in_Chemistry"}]}, {"year": "1988", "text": "<PERSON><PERSON><PERSON><PERSON><PERSON>, Swedish actress (b. 1908)", "html": "1988 - <a href=\"https://wikipedia.org/wiki/Hj%C3%B6<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON><PERSON></a>, Swedish actress (b. 1908)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Hj%C3%B6<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON><PERSON></a>, Swedish actress (b. 1908)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Hj%C3%B6<PERSON><PERSON>_<PERSON><PERSON>"}]}, {"year": "1988", "text": "<PERSON>, German physicist and academic, Nobel Prize laureate (b. 1906)", "html": "1988 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German physicist and academic, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Physics\" title=\"Nobel Prize in Physics\">Nobel Prize</a> laureate (b. 1906)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German physicist and academic, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Physics\" title=\"Nobel Prize in Physics\">Nobel Prize</a> laureate (b. 1906)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Nobel Prize in Physics", "link": "https://wikipedia.org/wiki/Nobel_Prize_in_Physics"}]}, {"year": "1989", "text": "<PERSON><PERSON><PERSON>, Russian poet and translator (b. 1907)", "html": "1989 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Russian poet and translator (b. 1907)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Russian poet and translator (b. 1907)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/A<PERSON><PERSON>_<PERSON>"}]}, {"year": "1990", "text": "<PERSON>, American lawyer and politician, 44th Governor of New Jersey (b. 1908)", "html": "1990 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and politician, 44th <a href=\"https://wikipedia.org/wiki/Governor_of_New_Jersey\" title=\"Governor of New Jersey\">Governor of New Jersey</a> (b. 1908)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and politician, 44th <a href=\"https://wikipedia.org/wiki/Governor_of_New_Jersey\" title=\"Governor of New Jersey\">Governor of New Jersey</a> (b. 1908)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>"}, {"title": "Governor of New Jersey", "link": "https://wikipedia.org/wiki/Governor_of_New_Jersey"}]}, {"year": "1991", "text": "<PERSON>, Austrian musicologist and theorist (b. 1904)", "html": "1991 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Austrian musicologist and theorist (b. 1904)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Austrian musicologist and theorist (b. 1904)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1992", "text": "Uncle <PERSON>, American fiddler (b. 1890)", "html": "1992 - <a href=\"https://wikipedia.org/wiki/Uncle_<PERSON>_<PERSON>\" title=\"Uncle <PERSON>\">Uncle <PERSON></a>, American fiddler (b. 1890)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Uncle_<PERSON>_<PERSON>\" title=\"Uncle <PERSON>\">Uncle <PERSON></a>, American fiddler (b. 1890)", "links": [{"title": "Uncle <PERSON>", "link": "https://wikipedia.org/wiki/Uncle_<PERSON>_<PERSON>"}]}, {"year": "1998", "text": "<PERSON><PERSON>, Indian lawyer and politician (b. 1905)", "html": "1998 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Indian lawyer and politician (b. 1905)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Indian lawyer and politician (b. 1905)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>i"}]}, {"year": "2000", "text": "<PERSON><PERSON><PERSON><PERSON>, Polish engineer and pilot (b. 1912)", "html": "2000 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Polish engineer and pilot (b. 1912)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Polish engineer and pilot (b. 1912)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "2000", "text": "<PERSON>, Baron <PERSON> of Beoch, Scottish politician and diplomat, 25th Governor of Hong Kong (b. 1917)", "html": "2000 - <a href=\"https://wikipedia.org/wiki/<PERSON>,_Baron_<PERSON>_of_Beoch\" title=\"<PERSON>, Baron <PERSON> of Beoch\"><PERSON>, Baron <PERSON> of Beoch</a>, Scottish politician and diplomat, 25th <a href=\"https://wikipedia.org/wiki/Governor_of_Hong_Kong\" title=\"Governor of Hong Kong\">Governor of Hong Kong</a> (b. 1917)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>,_Baron_<PERSON>_of_Beoch\" title=\"<PERSON>, Baron <PERSON> of Beoch\"><PERSON>, Baron <PERSON> of Beoch</a>, Scottish politician and diplomat, 25th <a href=\"https://wikipedia.org/wiki/Governor_of_Hong_Kong\" title=\"Governor of Hong Kong\">Governor of Hong Kong</a> (b. 1917)", "links": [{"title": "<PERSON>, Baron <PERSON> of Beoch", "link": "https://wikipedia.org/wiki/<PERSON>,_Baron_<PERSON>_of_Beoch"}, {"title": "Governor of Hong Kong", "link": "https://wikipedia.org/wiki/Governor_of_Hong_Kong"}]}, {"year": "2000", "text": "<PERSON>, Canadian ice hockey player and coach (b. 1921)", "html": "2000 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player and coach (b. 1921)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player and coach (b. 1921)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2002", "text": "<PERSON>, Scottish historian (b. 1909)", "html": "2002 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish historian (b. 1909)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish historian (b. 1909)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "2003", "text": "<PERSON>, Italian composer and educator (b. 1925)", "html": "2003 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian composer and educator (b. 1925)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian composer and educator (b. 1925)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2006", "text": "<PERSON>, American journalist (b. 1949)", "html": "2006 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American journalist (b. 1949)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American journalist (b. 1949)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2006", "text": "<PERSON>, American actor (b. 1939)", "html": "2006 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor (b. 1939)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor (b. 1939)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "2006", "text": "<PERSON>, American football player (b. 1966)", "html": "2006 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player (b. 1966)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player (b. 1966)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2007", "text": "<PERSON><PERSON><PERSON>, Japanese singer-songwriter (b. 1967)", "html": "2007 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese singer-songwriter (b. 1967)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese singer-songwriter (b. 1967)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "2007", "text": "<PERSON>, American actress and dancer (b. 1932)", "html": "2007 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress and dancer (b. 1932)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress and dancer (b. 1932)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>yler"}]}, {"year": "2007", "text": "<PERSON>, American inventor, created the modern hot air balloon (b. 1919)", "html": "2007 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American inventor, created the modern <a href=\"https://wikipedia.org/wiki/Hot_air_balloon\" title=\"Hot air balloon\">hot air balloon</a> (b. 1919)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American inventor, created the modern <a href=\"https://wikipedia.org/wiki/Hot_air_balloon\" title=\"Hot air balloon\">hot air balloon</a> (b. 1919)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Hot air balloon", "link": "https://wikipedia.org/wiki/Hot_air_balloon"}]}, {"year": "2008", "text": "<PERSON>, Hungarian soldier (b. 1900)", "html": "2008 - <a href=\"https://wikipedia.org/wiki/Franz_K%C3%BCnstler\" title=\"<PERSON>\"><PERSON></a>, Hungarian soldier (b. 1900)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Franz_K%C3%BC<PERSON><PERSON>\" title=\"<PERSON>\"><PERSON></a>, Hungarian soldier (b. 1900)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Franz_K%C3%BCnstler"}]}, {"year": "2009", "text": "<PERSON>, American lawyer and academic (b. 1931)", "html": "2009 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and academic (b. 1931)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and academic (b. 1931)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "2009", "text": "<PERSON>, Welsh-American economist and academic, Nobel Prize laureate (b. 1934)", "html": "2009 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Welsh-American economist and academic, <a href=\"https://wikipedia.org/wiki/Nobel_Memorial_Prize_in_Economic_Sciences\" title=\"Nobel Memorial Prize in Economic Sciences\">Nobel Prize</a> laureate (b. 1934)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Welsh-American economist and academic, <a href=\"https://wikipedia.org/wiki/Nobel_Memorial_Prize_in_Economic_Sciences\" title=\"Nobel Memorial Prize in Economic Sciences\">Nobel Prize</a> laureate (b. 1934)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Nobel Memorial Prize in Economic Sciences", "link": "https://wikipedia.org/wiki/Nobel_Memorial_Prize_in_Economic_Sciences"}]}, {"year": "2009", "text": "<PERSON>, British nursing administrator; Northern Ireland's first Chief Nursing Officer (b. 1910)", "html": "2009 - <a href=\"https://wikipedia.org/wiki/Mona_Grey\" title=\"Mona <PERSON>\"><PERSON></a>, British nursing administrator; Northern Ireland's first Chief Nursing Officer (b. 1910)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Mona_Grey\" title=\"Mona Grey\"><PERSON></a>, British nursing administrator; Northern Ireland's first Chief Nursing Officer (b. 1910)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Mona_<PERSON>"}]}, {"year": "2009", "text": "<PERSON><PERSON><PERSON>, Canadian biochemist, physician, and psychiatrist (b. 1917)", "html": "2009 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Canadian biochemist, physician, and psychiatrist (b. 1917)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Canadian biochemist, physician, and psychiatrist (b. 1917)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "2009", "text": "<PERSON><PERSON>, Haitian-American priest and theologian (b. 1946)", "html": "2009 - <a href=\"https://wikipedia.org/wiki/G%C3%A9<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Haitian-American priest and theologian (b. 1946)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/G%C3%A9<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Haitian-American priest and theologian (b. 1946)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/G%C3%A9<PERSON><PERSON>_<PERSON>"}]}, {"year": "2009", "text": "<PERSON>, American nun and author (b. 1933)", "html": "2009 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>%27Marie\" title=\"<PERSON>\"><PERSON></a>, American nun and author (b. 1933)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>%27Marie\" title=\"<PERSON>\"><PERSON></a>, American nun and author (b. 1933)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>%27Marie"}]}, {"year": "2009", "text": "<PERSON>, Australian soldier and physician (b. 1913)", "html": "2009 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian soldier and physician (b. 1913)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian soldier and physician (b. 1913)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2009", "text": "<PERSON>, English-American television host (b. 1933)", "html": "2009 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English-American television host (b. 1933)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English-American television host (b. 1933)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "2010", "text": "<PERSON><PERSON>, Thai animator and director (b. 1929)", "html": "2010 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Thai animator and director (b. 1929)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Thai animator and director (b. 1929)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Payut_Ngaokrachang"}]}, {"year": "2011", "text": "<PERSON>, American actor and singer (b. 1950)", "html": "2011 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor and singer (b. 1950)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor and singer (b. 1950)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2011", "text": "<PERSON><PERSON>, Polish-American basketball player (b. 1974)", "html": "2011 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Polish-American basketball player (b. 1974)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Polish-American basketball player (b. 1974)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "2011", "text": "<PERSON>-<PERSON><PERSON>, American singer-songwriter and poet (b. 1949)", "html": "2011 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and poet (b. 1949)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and poet (b. 1949)", "links": [{"title": "<PERSON>-Heron", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2012", "text": "<PERSON><PERSON><PERSON>, Nevisian educator and politician, 1st Premier of Nevis (b. 1934)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON> Daniel\"><PERSON><PERSON><PERSON></a>, Nevisian educator and politician, 1st <a href=\"https://wikipedia.org/wiki/Premier_of_Nevis\" title=\"Premier of Nevis\">Premier of Nevis</a> (b. 1934)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON> Daniel\"><PERSON><PERSON><PERSON></a>, Nevisian educator and politician, 1st <a href=\"https://wikipedia.org/wiki/Premier_of_Nevis\" title=\"Premier of Nevis\">Premier of Nevis</a> (b. 1934)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}, {"title": "Premier of Nevis", "link": "https://wikipedia.org/wiki/Premier_of_Nevis"}]}, {"year": "2012", "text": "<PERSON>, German mathematician and academic (b. 1927)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German mathematician and academic (b. 1927)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German mathematician and academic (b. 1927)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2012", "text": "<PERSON><PERSON>, Russian-born Armenian Iranologist (b. 1928)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Russian-born Armenian Iranologist (b. 1928)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Russian-born Armenian Iranologist (b. 1928)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON><PERSON>"}]}, {"year": "2012", "text": "<PERSON>, Canadian-American geneticist and academic (b. 1936)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian-American geneticist and academic (b. 1936)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian-American geneticist and academic (b. 1936)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2013", "text": "<PERSON><PERSON><PERSON>, Indian politician (b. 1917)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Indian politician (b. 1917)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Indian politician (b. 1917)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>"}]}, {"year": "2013", "text": "<PERSON>, English actor (b. 1926)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actor (b. 1926)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actor (b. 1926)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2013", "text": "<PERSON><PERSON><PERSON><PERSON>, Malian politician, Prime Minister of Mali (b. 1931)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/Abdoulaye_S%C3%A9kou_Sow\" title=\"Abd<PERSON><PERSON>e Sékou Sow\"><PERSON><PERSON><PERSON><PERSON></a>, Malian politician, <a href=\"https://wikipedia.org/wiki/List_of_heads_of_government_of_Mali\" class=\"mw-redirect\" title=\"List of heads of government of Mali\">Prime Minister of Mali</a> (b. 1931)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Abdoulaye_S%C3%A9kou_Sow\" title=\"Abd<PERSON>laye Sékou Sow\"><PERSON><PERSON><PERSON><PERSON></a>, Malian politician, <a href=\"https://wikipedia.org/wiki/List_of_heads_of_government_of_Mali\" class=\"mw-redirect\" title=\"List of heads of government of Mali\">Prime Minister of Mali</a> (b. 1931)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Abdoulaye_S%C3%A9kou_Sow"}, {"title": "List of heads of government of Mali", "link": "https://wikipedia.org/wiki/List_of_heads_of_government_of_Mali"}]}, {"year": "2014", "text": "<PERSON>, Canadian painter and author (b. 1936)", "html": "2014 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian painter and author (b. 1936)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian painter and author (b. 1936)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2014", "text": "<PERSON><PERSON>-<PERSON><PERSON><PERSON>, German director, producer, and screenwriter (b. 1940)", "html": "2014 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>-<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, German director, producer, and screenwriter (b. 1940)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>-<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, German director, producer, and screenwriter (b. 1940)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>-<PERSON>"}]}, {"year": "2014", "text": "<PERSON>, Puerto Rican-American baseball player, coach, and manager (b. 1929)", "html": "2014 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Puerto Rican-American baseball player, coach, and manager (b. 1929)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Puerto Rican-American baseball player, coach, and manager (b. 1929)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2014", "text": "<PERSON><PERSON>, Italian-American graphic designer (b. 1931)", "html": "2014 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Italian-American graphic designer (b. 1931)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Italian-American graphic designer (b. 1931)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "2015", "text": "<PERSON>, Swedish rally driver (b. 1929)", "html": "2015 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swedish rally driver (b. 1929)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swedish rally driver (b. 1929)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "2015", "text": "<PERSON><PERSON>, Norwegian sociologist, criminologist, and author (b. 1928)", "html": "2015 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Norwegian sociologist, criminologist, and author (b. 1928)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Norwegian sociologist, criminologist, and author (b. 1928)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "2015", "text": "<PERSON>, English footballer and manager (b. 1956)", "html": "2015 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(footballer,_born_1956)\" title=\"<PERSON> (footballer, born 1956)\"><PERSON></a>, English footballer and manager (b. 1956)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(footballer,_born_1956)\" title=\"<PERSON> (footballer, born 1956)\"><PERSON></a>, English footballer and manager (b. 1956)", "links": [{"title": "<PERSON> (footballer, born 1956)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(footballer,_born_1956)"}]}, {"year": "2015", "text": "<PERSON>, American philosopher and academic (b. 1932)", "html": "2015 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(philosopher)\" class=\"mw-redirect\" title=\"<PERSON> (philosopher)\"><PERSON></a>, American philosopher and academic (b. 1932)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(philosopher)\" class=\"mw-redirect\" title=\"<PERSON> (philosopher)\"><PERSON></a>, American philosopher and academic (b. 1932)", "links": [{"title": "<PERSON> (philosopher)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>(philosopher)"}]}, {"year": "2017", "text": "<PERSON>, American musician, singer and songwriter (b. 1947)", "html": "2017 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American musician, singer and songwriter (b. 1947)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American musician, singer and songwriter (b. 1947)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2018", "text": "<PERSON>, American science fiction author and editor (b. 1947)", "html": "2018 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American science fiction author and editor (b. 1947)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American science fiction author and editor (b. 1947)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Gardner_Dozo<PERSON>"}]}, {"year": "2020", "text": "<PERSON>, American playwright, public health advocate and LGBT rights activist (b. 1935)", "html": "2020 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American playwright, public health advocate and LGBT rights activist (b. 1935)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American playwright, public health advocate and LGBT rights activist (b. 1935)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "2021", "text": "<PERSON><PERSON>, former Prime Minister of Denmark (b. 1929)", "html": "2021 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>hl%C3%BCter\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, former Prime Minister of Denmark (b. 1929)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>hl%C3%BCter\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, former Prime Minister of Denmark (b. 1929)", "links": [{"title": "Poul Schlüter", "link": "https://wikipedia.org/wiki/Poul_Schl%C3%BCter"}]}, {"year": "2024", "text": "<PERSON>, American actress (b. 1936)", "html": "2024 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress (b. 1936)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress (b. 1936)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2024", "text": "<PERSON>, American basketball player and sportscaster (b. 1952)", "html": "2024 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player and sportscaster (b. 1952)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player and sportscaster (b. 1952)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}]}}