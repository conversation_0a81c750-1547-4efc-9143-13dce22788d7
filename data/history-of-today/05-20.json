{"date": "May 20", "url": "https://wikipedia.org/wiki/May_20", "data": {"Events": [{"year": "325", "text": "The First Council of Nicaea is formally opened, starting the first ecumenical council of the Christian Church.", "html": "325 - The <a href=\"https://wikipedia.org/wiki/First_Council_of_Nicaea\" title=\"First Council of Nicaea\">First Council of Nicaea</a> is formally opened, starting the first <a href=\"https://wikipedia.org/wiki/Ecumenical_council\" title=\"Ecumenical council\">ecumenical council</a> of the Christian Church.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/First_Council_of_Nicaea\" title=\"First Council of Nicaea\">First Council of Nicaea</a> is formally opened, starting the first <a href=\"https://wikipedia.org/wiki/Ecumenical_council\" title=\"Ecumenical council\">ecumenical council</a> of the Christian Church.", "links": [{"title": "First Council of Nicaea", "link": "https://wikipedia.org/wiki/First_Council_of_Nicaea"}, {"title": "Ecumenical council", "link": "https://wikipedia.org/wiki/Ecumenical_council"}]}, {"year": "491", "text": "Empress <PERSON><PERSON><PERSON> marries <PERSON><PERSON><PERSON>. The widowed <PERSON> is able to choose her successor for the Byzantine throne, after <PERSON><PERSON> (late emperor) dies of dysentery.", "html": "491 - Empress <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_(empress)\" title=\"<PERSON><PERSON><PERSON> (empress)\"><PERSON><PERSON><PERSON></a> marries <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_I_Dicorus\" title=\"<PERSON><PERSON><PERSON> I Dicorus\"><PERSON><PERSON><PERSON> I</a>. The widowed <i><a href=\"https://wikipedia.org/wiki/Augusta_(honorific)\" class=\"mw-redirect\" title=\"<PERSON> (honorific)\"><PERSON></a></i> is able to choose her successor for the <a href=\"https://wikipedia.org/wiki/Byzantine_Empire\" title=\"Byzantine Empire\">Byzantine</a> throne, after <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_(emperor)\" title=\"<PERSON><PERSON> (emperor)\"><PERSON><PERSON></a> (late emperor) dies of <a href=\"https://wikipedia.org/wiki/Dysentery\" title=\"Dysentery\">dysentery</a>.", "no_year_html": "Empress <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_(empress)\" title=\"<PERSON><PERSON><PERSON> (empress)\"><PERSON><PERSON><PERSON></a> marries <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_I_Dicorus\" title=\"<PERSON><PERSON><PERSON> I Dicorus\"><PERSON><PERSON><PERSON> I</a>. The widowed <i><a href=\"https://wikipedia.org/wiki/Augusta_(honorific)\" class=\"mw-redirect\" title=\"<PERSON> (honorific)\"><PERSON></a></i> is able to choose her successor for the <a href=\"https://wikipedia.org/wiki/Byzantine_Empire\" title=\"Byzantine Empire\">Byzantine</a> throne, after <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_(emperor)\" title=\"<PERSON><PERSON> (emperor)\"><PERSON><PERSON></a> (late emperor) dies of <a href=\"https://wikipedia.org/wiki/Dysentery\" title=\"Dysentery\">dysentery</a>.", "links": [{"title": "<PERSON><PERSON><PERSON> (empress)", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_(empress)"}, {"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>sius_I_Dicorus"}, {"title": "<PERSON> (honorific)", "link": "https://wikipedia.org/wiki/Augusta_(honorific)"}, {"title": "Byzantine Empire", "link": "https://wikipedia.org/wiki/Byzantine_Empire"}, {"title": "<PERSON><PERSON> (emperor)", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_(emperor)"}, {"title": "Dysentery", "link": "https://wikipedia.org/wiki/Dysentery"}]}, {"year": "685", "text": "The Battle of Dun Nechtain is fought between a Pictish army under King <PERSON><PERSON> and the invading Northumbrians under King <PERSON><PERSON><PERSON><PERSON><PERSON>, who are decisively defeated.", "html": "685 - The <a href=\"https://wikipedia.org/wiki/Battle_of_Dun_Nechtain\" title=\"Battle of Dun Nechtain\">Battle of Dun Nechtain</a> is fought between a <a href=\"https://wikipedia.org/wiki/Picts\" title=\"Picts\">Pictish</a> army under King <a href=\"https://wikipedia.org/wiki/Bridei_III\" class=\"mw-redirect\" title=\"Bridei III\">Bridei III</a> and the invading <a href=\"https://wikipedia.org/wiki/Northumbrians\" class=\"mw-redirect\" title=\"Northumbrians\">Northumbrians</a> under King <a href=\"https://wikipedia.org/wiki/Ecgfrith_of_Northumbria\" title=\"Ecg<PERSON><PERSON> of Northumbria\">Ecg<PERSON><PERSON></a>, who are decisively defeated.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Battle_of_Dun_Nechtain\" title=\"Battle of Dun Nechtain\">Battle of Dun Nechtain</a> is fought between a <a href=\"https://wikipedia.org/wiki/Picts\" title=\"Picts\">Pictish</a> army under King <a href=\"https://wikipedia.org/wiki/Bridei_III\" class=\"mw-redirect\" title=\"Bridei III\">Bridei III</a> and the invading <a href=\"https://wikipedia.org/wiki/Northumbrians\" class=\"mw-redirect\" title=\"Northumbrians\">Northumbrians</a> under King <a href=\"https://wikipedia.org/wiki/Ecgfrith_of_Northumbria\" title=\"Ec<PERSON><PERSON><PERSON> of Northumbria\">Ecg<PERSON><PERSON></a>, who are decisively defeated.", "links": [{"title": "Battle of Dun Nechtain", "link": "https://wikipedia.org/wiki/Battle_of_Dun_Nechtain"}, {"title": "Picts", "link": "https://wikipedia.org/wiki/Picts"}, {"title": "<PERSON><PERSON> III", "link": "https://wikipedia.org/wiki/Bridei_III"}, {"title": "Northumbrians", "link": "https://wikipedia.org/wiki/Northumbrians"}, {"title": "<PERSON><PERSON><PERSON><PERSON><PERSON> of Northumbria", "link": "https://wikipedia.org/wiki/Ecgfrith_of_Northumbria"}]}, {"year": "794", "text": "While visiting the royal Mercian court at Sutton Walls with a view to marrying princess <PERSON><PERSON><PERSON><PERSON><PERSON>, King <PERSON><PERSON><PERSON><PERSON><PERSON> II of East Anglia is taken captive and beheaded.", "html": "794 - While visiting the royal <a href=\"https://wikipedia.org/wiki/Mercia\" title=\"Mercia\">Mercian</a> court at <a href=\"https://wikipedia.org/wiki/Sutton_Walls_Hill_Fort\" title=\"Sutton Walls Hill Fort\">Sutton Walls</a> with a view to marrying princess <a href=\"https://wikipedia.org/wiki/%C3%86lfthryth_of_Crowland\" title=\"Ælfthryth of Crowland\"><PERSON><PERSON><PERSON><PERSON><PERSON></a>, King <a href=\"https://wikipedia.org/wiki/%C3%86thelberht_II_of_East_Anglia\" title=\"Æthelberht II of East Anglia\">Æthelberht II of East Anglia</a> is taken captive and <a href=\"https://wikipedia.org/wiki/Decapitation\" title=\"Decapitation\">beheaded</a>.", "no_year_html": "While visiting the royal <a href=\"https://wikipedia.org/wiki/Mercia\" title=\"Mercia\">Mercian</a> court at <a href=\"https://wikipedia.org/wiki/Sutton_Walls_Hill_Fort\" title=\"Sutton Walls Hill Fort\">Sutton Walls</a> with a view to marrying princess <a href=\"https://wikipedia.org/wiki/%C3%86lfthryth_of_Crowland\" title=\"Ælfthryth of Crowland\"><PERSON><PERSON><PERSON><PERSON><PERSON></a>, King <a href=\"https://wikipedia.org/wiki/%C3%86thelberht_II_of_East_Anglia\" title=\"Æthelberht II of East Anglia\">Æthelberht II of East Anglia</a> is taken captive and <a href=\"https://wikipedia.org/wiki/Decapitation\" title=\"Decapitation\">beheaded</a>.", "links": [{"title": "Mercia", "link": "https://wikipedia.org/wiki/Mercia"}, {"title": "Sutton Walls Hill Fort", "link": "https://wikipedia.org/wiki/Sutton_Walls_Hill_Fort"}, {"title": "<PERSON><PERSON><PERSON><PERSON><PERSON> of Crowland", "link": "https://wikipedia.org/wiki/%C3%86lfthryth_of_Crowland"}, {"title": "Æthelberht II of East Anglia", "link": "https://wikipedia.org/wiki/%C3%86thelberht_II_of_East_Anglia"}, {"title": "Decapitation", "link": "https://wikipedia.org/wiki/Decapitation"}]}, {"year": "1217", "text": "The Second Battle of Lincoln is fought near Lincoln, England, resulting in the defeat of <PERSON> of France by <PERSON>, 1st Earl of Pembroke.", "html": "1217 - The <a href=\"https://wikipedia.org/wiki/Second_Battle_of_Lincoln\" class=\"mw-redirect\" title=\"Second Battle of Lincoln\">Second Battle of Lincoln</a> is fought near <a href=\"https://wikipedia.org/wiki/Lincoln,_England\" title=\"Lincoln, England\">Lincoln</a>, England, resulting in the defeat of <a href=\"https://wikipedia.org/wiki/Louis_VIII_of_France\" title=\"<PERSON> of France\">Prince <PERSON> of France</a> by <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_1st_Earl_of_Pembroke\" title=\"<PERSON>, 1st Earl of Pembroke\"><PERSON>, 1st Earl of Pembroke</a>.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Second_Battle_of_Lincoln\" class=\"mw-redirect\" title=\"Second Battle of Lincoln\">Second Battle of Lincoln</a> is fought near <a href=\"https://wikipedia.org/wiki/Lincoln,_England\" title=\"Lincoln, England\">Lincoln</a>, England, resulting in the defeat of <a href=\"https://wikipedia.org/wiki/Louis_VIII_of_France\" title=\"<PERSON> of France\">Prince <PERSON> of France</a> by <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_1st_Earl_of_Pembroke\" title=\"<PERSON>, 1st Earl of Pembroke\"><PERSON>, 1st Earl of Pembroke</a>.", "links": [{"title": "Second Battle of Lincoln", "link": "https://wikipedia.org/wiki/Second_Battle_of_Lincoln"}, {"title": "Lincoln, England", "link": "https://wikipedia.org/wiki/Lincoln,_England"}, {"title": "<PERSON> of France", "link": "https://wikipedia.org/wiki/Louis_VIII_of_France"}, {"title": "<PERSON>, 1st Earl of Pembroke", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_1st_Earl_of_Pembroke"}]}, {"year": "1293", "text": "King <PERSON><PERSON> of Castile creates the Estudio de Escuelas de Generales in Alcalá de Henares.", "html": "1293 - King <a href=\"https://wikipedia.org/wiki/Sancho_IV_of_Castile\" title=\"Sancho IV of Castile\">Sancho IV of Castile</a> creates the <a href=\"https://wikipedia.org/wiki/Estudio_de_Escuelas_de_Generales\" class=\"mw-redirect\" title=\"Estudio de Escuelas de Generales\">Estudio de Escuelas de Generales</a> in <a href=\"https://wikipedia.org/wiki/Alcal%C3%A1_de_Henares\" title=\"Alcalá de Henares\">Alcalá de Henares</a>.", "no_year_html": "King <a href=\"https://wikipedia.org/wiki/Sancho_IV_of_Castile\" title=\"Sancho IV of Castile\"><PERSON>cho IV of Castile</a> creates the <a href=\"https://wikipedia.org/wiki/Estudio_de_Escuelas_de_Generales\" class=\"mw-redirect\" title=\"Estudio de Escuelas de Generales\">Estudio de Escuelas de Generales</a> in <a href=\"https://wikipedia.org/wiki/Alcal%C3%A1_de_Henares\" title=\"Alcalá de Henares\">Alcalá de Henares</a>.", "links": [{"title": "<PERSON><PERSON> IV of Castile", "link": "https://wikipedia.org/wiki/Sancho_IV_of_Castile"}, {"title": "Estudio de Escuelas de Generales", "link": "https://wikipedia.org/wiki/Estudio_de_Escuelas_de_Generales"}, {"title": "Alcalá de Henares", "link": "https://wikipedia.org/wiki/Alcal%C3%A1_de_Henares"}]}, {"year": "1426", "text": "King <PERSON><PERSON><PERSON><PERSON> formally ascends to the throne of Ava.[note 1]", "html": "1426 - King <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_Thad<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON> Thado\"><PERSON><PERSON><PERSON><PERSON></a> formally ascends to the <a href=\"https://wikipedia.org/wiki/Kingdom_of_Ava\" title=\"Kingdom of Ava\">throne of Ava</a>.", "no_year_html": "King <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_Thado\" title=\"<PERSON><PERSON><PERSON><PERSON> Thado\"><PERSON><PERSON><PERSON><PERSON></a> formally ascends to the <a href=\"https://wikipedia.org/wiki/Kingdom_of_Ava\" title=\"Kingdom of Ava\">throne of Ava</a>.", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON><PERSON>o"}, {"title": "Kingdom of Ava", "link": "https://wikipedia.org/wiki/Kingdom_of_Ava"}]}, {"year": "1449", "text": "The Battle of Alfarrobeira is fought, establishing the House of Braganza as a principal royal family of Portugal.", "html": "1449 - The <a href=\"https://wikipedia.org/wiki/Battle_of_Alfarrobeira\" title=\"Battle of Alfarrobeira\">Battle of Alfarrobeira</a> is fought, establishing the <a href=\"https://wikipedia.org/wiki/House_of_Braganza\" title=\"House of Braganza\">House of Braganza</a> as a principal royal family of Portugal.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Battle_of_Alfarrobeira\" title=\"Battle of Alfarrobeira\">Battle of Alfarrobeira</a> is fought, establishing the <a href=\"https://wikipedia.org/wiki/House_of_Braganza\" title=\"House of Braganza\">House of Braganza</a> as a principal royal family of Portugal.", "links": [{"title": "Battle of Alfarrobeira", "link": "https://wikipedia.org/wiki/Battle_of_Alfarrobeira"}, {"title": "House of Braganza", "link": "https://wikipedia.org/wiki/House_of_Braganza"}]}, {"year": "1497", "text": "<PERSON> sets sail from Bristol, England, on his ship Matthew looking for a route to the west (other documents give a May 2 date).", "html": "1497 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> sets sail from <a href=\"https://wikipedia.org/wiki/Bristol\" title=\"Bristol\">Bristol</a>, England, on his ship <a href=\"https://wikipedia.org/wiki/<PERSON>_(1497_ship)\" title=\"<PERSON> (1497 ship)\"><i>Matthew</i></a> looking for a route to the west (other documents give a <a href=\"https://wikipedia.org/wiki/May_2\" title=\"May 2\">May 2</a> date).", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> sets sail from <a href=\"https://wikipedia.org/wiki/Bristol\" title=\"Bristol\">Bristol</a>, England, on his ship <a href=\"https://wikipedia.org/wiki/<PERSON>_(1497_ship)\" title=\"<PERSON> (1497 ship)\"><i>Matthew</i></a> looking for a route to the west (other documents give a <a href=\"https://wikipedia.org/wiki/May_2\" title=\"May 2\">May 2</a> date).", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Bristol", "link": "https://wikipedia.org/wiki/Bristol"}, {"title": "<PERSON> (1497 ship)", "link": "https://wikipedia.org/wiki/<PERSON>_(1497_ship)"}, {"title": "May 2", "link": "https://wikipedia.org/wiki/May_2"}]}, {"year": "1498", "text": "Portuguese explorer <PERSON><PERSON> discovers the sea route to India when he arrives at Kozhikode (previously known as Calicut), India.", "html": "1498 - Portuguese explorer <a href=\"https://wikipedia.org/wiki/Vasco_da_Gama\" title=\"Vasco da Gama\">Vasco da Gama</a> <a href=\"https://wikipedia.org/wiki/Discovery_of_the_sea_route_to_India\" class=\"mw-redirect\" title=\"Discovery of the sea route to India\">discovers the sea route to India</a> when he arrives at <a href=\"https://wikipedia.org/wiki/Kozhikode\" title=\"Kozhikode\">Kozhikode</a> (previously known as Calicut), India.", "no_year_html": "Portuguese explorer <a href=\"https://wikipedia.org/wiki/Vasco_da_Gama\" title=\"Vasco da Gama\">Vasco da Gama</a> <a href=\"https://wikipedia.org/wiki/Discovery_of_the_sea_route_to_India\" class=\"mw-redirect\" title=\"Discovery of the sea route to India\">discovers the sea route to India</a> when he arrives at <a href=\"https://wikipedia.org/wiki/Kozhikode\" title=\"Kozhikode\">Kozhikode</a> (previously known as Calicut), India.", "links": [{"title": "Vasco da Gama", "link": "https://wikipedia.org/wiki/V<PERSON>_da_Gama"}, {"title": "Discovery of the sea route to India", "link": "https://wikipedia.org/wiki/Discovery_of_the_sea_route_to_India"}, {"title": "Kozhikode", "link": "https://wikipedia.org/wiki/Kozhikode"}]}, {"year": "1520", "text": "<PERSON><PERSON> defeats <PERSON><PERSON><PERSON><PERSON>, sent by Spain to punish him for insubordination.", "html": "1520 - <a href=\"https://wikipedia.org/wiki/Hernan_Cort%C3%A9s\" class=\"mw-redirect\" title=\"Hernan Cortés\"><PERSON><PERSON></a> <a href=\"https://wikipedia.org/wiki/Battle_of_Cempoala\" title=\"Battle of Cempoala\">defeats</a> <a href=\"https://wikipedia.org/wiki/P%C3%A1nfilo_de_Narv%C3%A1ez\" title=\"Pán<PERSON>lo de Narv<PERSON>ez\"><PERSON><PERSON><PERSON><PERSON> <PERSON></a>, sent by Spain to punish him for insubordination.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Her<PERSON>_Cort%C3%A9s\" class=\"mw-redirect\" title=\"Hernan Cortés\"><PERSON><PERSON></a> <a href=\"https://wikipedia.org/wiki/Battle_of_Cempoala\" title=\"Battle of Cempoala\">defeats</a> <a href=\"https://wikipedia.org/wiki/P%C3%A1nfilo_de_Narv%C3%A1ez\" title=\"<PERSON>án<PERSON><PERSON> de Narv<PERSON>ez\"><PERSON><PERSON><PERSON><PERSON> <PERSON></a>, sent by Spain to punish him for insubordination.", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Hernan_Cort%C3%A9s"}, {"title": "Battle of Cempoala", "link": "https://wikipedia.org/wiki/Battle_of_Cempoala"}, {"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/P%C3%A1nfilo_de_Narv%C3%A1ez"}]}, {"year": "1521", "text": "<PERSON><PERSON><PERSON> <PERSON> Loyola is seriously wounded in the Battle of Pampeluna.", "html": "1521 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_of_Loyola\" title=\"<PERSON><PERSON><PERSON> of Loyola\"><PERSON><PERSON><PERSON> of Loyola</a> is seriously wounded in the <a href=\"https://wikipedia.org/wiki/Battle_of_Pampeluna\" title=\"Battle of Pampeluna\">Battle of Pampeluna</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_Loyola\" title=\"<PERSON><PERSON><PERSON> of Loyola\"><PERSON><PERSON><PERSON> of Loyola</a> is seriously wounded in the <a href=\"https://wikipedia.org/wiki/Battle_of_Pampeluna\" title=\"Battle of Pampeluna\">Battle of Pampeluna</a>.", "links": [{"title": "<PERSON><PERSON><PERSON> of Loyola", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_Loyola"}, {"title": "Battle of Pampeluna", "link": "https://wikipedia.org/wiki/Battle_of_Pampeluna"}]}, {"year": "1570", "text": "Cartographer <PERSON> issues Theatrum Orbis Terrarum, the first modern atlas.", "html": "1570 - <a href=\"https://wikipedia.org/wiki/Cartography\" title=\"Cartography\">Cartographer</a> <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> issues <i><a href=\"https://wikipedia.org/wiki/Theatrum_Orbis_Terrarum\" title=\"Theatrum Orbis Terrarum\">Theatrum Orbis Terrarum</a></i>, the first modern <a href=\"https://wikipedia.org/wiki/Atlas\" title=\"Atlas\">atlas</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Cartography\" title=\"Cartography\">Cartographer</a> <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> issues <i><a href=\"https://wikipedia.org/wiki/Theatrum_Orbis_Terrarum\" title=\"Theatrum Orbis Terrarum\">Theatrum Orbis Terrarum</a></i>, the first modern <a href=\"https://wikipedia.org/wiki/Atlas\" title=\"Atlas\">atlas</a>.", "links": [{"title": "Cartography", "link": "https://wikipedia.org/wiki/Cartography"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}, {"title": "Theatrum Orbis Terrarum", "link": "https://wikipedia.org/wiki/Theatrum_Orbis_Terrarum"}, {"title": "Atlas", "link": "https://wikipedia.org/wiki/Atlas"}]}, {"year": "1609", "text": "<PERSON>'s sonnets are first published in London, perhaps illicitly, by the publisher <PERSON>.", "html": "1609 - <a href=\"https://wikipedia.org/wiki/Shakespeare%27s_sonnets\" title=\"Shakespeare's sonnets\"><PERSON>'s sonnets</a> are first published in London, perhaps illicitly, by the publisher <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Shakespeare%27s_sonnets\" title=\"Shakespeare's sonnets\"><PERSON>'s sonnets</a> are first published in London, perhaps illicitly, by the publisher <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>.", "links": [{"title": "<PERSON>'s sonnets", "link": "https://wikipedia.org/wiki/Shakespeare%27s_sonnets"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1631", "text": "The city of Magdeburg in Germany is seized by forces of the Holy Roman Empire and most of its inhabitants massacred, in one of the bloodiest incidents of the Thirty Years' War.", "html": "1631 - The city of <a href=\"https://wikipedia.org/wiki/Magdeburg\" title=\"Magdeburg\">Magdeburg</a> in Germany is seized by forces of the <a href=\"https://wikipedia.org/wiki/Holy_Roman_Empire\" title=\"Holy Roman Empire\">Holy Roman Empire</a> and most of its inhabitants <a href=\"https://wikipedia.org/wiki/Sack_of_Magdeburg\" title=\"Sack of Magdeburg\">massacred</a>, in one of the bloodiest incidents of the <a href=\"https://wikipedia.org/wiki/Thirty_Years%27_War\" title=\"Thirty Years' War\">Thirty Years' War</a>.", "no_year_html": "The city of <a href=\"https://wikipedia.org/wiki/Magdeburg\" title=\"Magdeburg\">Magdeburg</a> in Germany is seized by forces of the <a href=\"https://wikipedia.org/wiki/Holy_Roman_Empire\" title=\"Holy Roman Empire\">Holy Roman Empire</a> and most of its inhabitants <a href=\"https://wikipedia.org/wiki/Sack_of_Magdeburg\" title=\"Sack of Magdeburg\">massacred</a>, in one of the bloodiest incidents of the <a href=\"https://wikipedia.org/wiki/Thirty_Years%27_War\" title=\"Thirty Years' War\">Thirty Years' War</a>.", "links": [{"title": "Magdeburg", "link": "https://wikipedia.org/wiki/Magdeburg"}, {"title": "Holy Roman Empire", "link": "https://wikipedia.org/wiki/Holy_Roman_Empire"}, {"title": "Sack of Magdeburg", "link": "https://wikipedia.org/wiki/Sack_of_Magdeburg"}, {"title": "Thirty Years' War", "link": "https://wikipedia.org/wiki/Thirty_Years%27_War"}]}, {"year": "1645", "text": "Yangzhou massacre: The ten day massacre of 800,000 residents of the city of Yangzhou, part of the Transition from Ming to Qing.", "html": "1645 - <a href=\"https://wikipedia.org/wiki/Yangzhou_massacre\" title=\"Yangzhou massacre\">Yangzhou massacre</a>: The ten day massacre of 800,000 residents of the city of <a href=\"https://wikipedia.org/wiki/Yangzhou\" title=\"Yangzhou\">Yangzhou</a>, part of the <a href=\"https://wikipedia.org/wiki/Transition_from_Ming_to_Qing\" title=\"Transition from Ming to Qing\">Transition from Ming to Qing</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Yangzhou_massacre\" title=\"Yangzhou massacre\">Yangzhou massacre</a>: The ten day massacre of 800,000 residents of the city of <a href=\"https://wikipedia.org/wiki/Yangzhou\" title=\"Yangzhou\">Yangzhou</a>, part of the <a href=\"https://wikipedia.org/wiki/Transition_from_Ming_to_Qing\" title=\"Transition from Ming to Qing\">Transition from Ming to Qing</a>.", "links": [{"title": "Yangzhou massacre", "link": "https://wikipedia.org/wiki/Yangzhou_massacre"}, {"title": "Yangzhou", "link": "https://wikipedia.org/wiki/Yangzhou"}, {"title": "Transition from Ming to Qing", "link": "https://wikipedia.org/wiki/Transition_from_Ming_to_Qing"}]}, {"year": "1714", "text": "<PERSON> leads the first performance of his cantata for <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, ihr Lieder, BWV 172, at the chapel of Schloss Weimar.", "html": "1714 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> leads the first performance of his cantata for Pentecost, <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>,_<PERSON><PERSON>_<PERSON><PERSON>,_er<PERSON><PERSON>,_ihr_<PERSON><PERSON>!_BWV_172\" title=\"<PERSON><PERSON><PERSON><PERSON>, ihr <PERSON><PERSON>, erkling<PERSON>, ihr <PERSON><PERSON>! BWV 172\"><i><PERSON><PERSON><PERSON><PERSON>, ihr <PERSON><PERSON></i>, BWV 172</a>, at the chapel of <a href=\"https://wikipedia.org/wiki/Schloss_Weimar\" title=\"Schloss Weimar\">Schloss Weimar</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> leads the first performance of his cantata for Pentecost, <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>,_<PERSON><PERSON>_<PERSON><PERSON>,_er<PERSON><PERSON>,_ihr_<PERSON><PERSON>!_BWV_172\" title=\"<PERSON><PERSON><PERSON><PERSON>, ihr <PERSON><PERSON>, erkling<PERSON>, ihr <PERSON>ten! BWV 172\"><i><PERSON><PERSON><PERSON><PERSON>, ihr <PERSON><PERSON></i>, BWV 172</a>, at the chapel of <a href=\"https://wikipedia.org/wiki/Schloss_Weimar\" title=\"Schloss Weimar\">Schloss Weimar</a>.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "<PERSON><PERSON><PERSON><PERSON>, ihr <PERSON><PERSON>, er<PERSON><PERSON>, ihr <PERSON><PERSON>! BWV 172", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>,_<PERSON>hr_<PERSON><PERSON>,_er<PERSON><PERSON>,_ihr_<PERSON><PERSON>!_BWV_172"}, {"title": "Schloss Weimar", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>oss_<PERSON>"}]}, {"year": "1741", "text": "The Battle of Cartagena de Indias ends in a Spanish victory and the British begin withdrawal towards Jamaica with substantial losses.", "html": "1741 - The <a href=\"https://wikipedia.org/wiki/Battle_of_Cartagena_de_Indias\" title=\"Battle of Cartagena de Indias\">Battle of Cartagena de Indias</a> ends in a <a href=\"https://wikipedia.org/wiki/Spanish_Navy\" title=\"Spanish Navy\">Spanish</a> victory and the <a href=\"https://wikipedia.org/wiki/Royal_Navy\" title=\"Royal Navy\">British</a> begin <a href=\"https://wikipedia.org/wiki/Withdrawal_(military)\" title=\"Withdrawal (military)\">withdrawal</a> towards <a href=\"https://wikipedia.org/wiki/Jamaica\" title=\"Jamaica\">Jamaica</a> with substantial <a href=\"https://wikipedia.org/wiki/Casualty_(person)\" title=\"Casualty (person)\">losses</a>.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Battle_of_Cartagena_de_Indias\" title=\"Battle of Cartagena de Indias\">Battle of Cartagena de Indias</a> ends in a <a href=\"https://wikipedia.org/wiki/Spanish_Navy\" title=\"Spanish Navy\">Spanish</a> victory and the <a href=\"https://wikipedia.org/wiki/Royal_Navy\" title=\"Royal Navy\">British</a> begin <a href=\"https://wikipedia.org/wiki/Withdrawal_(military)\" title=\"Withdrawal (military)\">withdrawal</a> towards <a href=\"https://wikipedia.org/wiki/Jamaica\" title=\"Jamaica\">Jamaica</a> with substantial <a href=\"https://wikipedia.org/wiki/Casualty_(person)\" title=\"Casualty (person)\">losses</a>.", "links": [{"title": "Battle of Cartagena de Indias", "link": "https://wikipedia.org/wiki/Battle_of_Cartagena_de_Indias"}, {"title": "Spanish Navy", "link": "https://wikipedia.org/wiki/Spanish_Navy"}, {"title": "Royal Navy", "link": "https://wikipedia.org/wiki/Royal_Navy"}, {"title": "<PERSON><PERSON><PERSON> (military)", "link": "https://wikipedia.org/wiki/Withdrawal_(military)"}, {"title": "Jamaica", "link": "https://wikipedia.org/wiki/Jamaica"}, {"title": "Casualty (person)", "link": "https://wikipedia.org/wiki/Casualty_(person)"}]}, {"year": "1775", "text": "The Mecklenburg Declaration of Independence is allegedly signed in Charlotte, North Carolina.", "html": "1775 - The <a href=\"https://wikipedia.org/wiki/Mecklenburg_Declaration_of_Independence\" title=\"Mecklenburg Declaration of Independence\">Mecklenburg Declaration of Independence</a> is allegedly signed in <a href=\"https://wikipedia.org/wiki/Charlotte,_North_Carolina\" title=\"Charlotte, North Carolina\">Charlotte, North Carolina</a>.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Mecklenburg_Declaration_of_Independence\" title=\"Mecklenburg Declaration of Independence\">Mecklenburg Declaration of Independence</a> is allegedly signed in <a href=\"https://wikipedia.org/wiki/Charlotte,_North_Carolina\" title=\"Charlotte, North Carolina\">Charlotte, North Carolina</a>.", "links": [{"title": "Mecklenburg Declaration of Independence", "link": "https://wikipedia.org/wiki/Mecklenburg_Declaration_of_Independence"}, {"title": "Charlotte, North Carolina", "link": "https://wikipedia.org/wiki/Charlotte,_North_Carolina"}]}, {"year": "1802", "text": "By the Law of 20 May 1802, <PERSON> reinstates slavery in the French colonies, revoking its abolition in the French Revolution.", "html": "1802 - By the <a href=\"https://wikipedia.org/wiki/Law_of_20_May_1802\" title=\"Law of 20 May 1802\">Law of 20 May 1802</a>, <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"Napoleon Bonaparte\"><PERSON></a> reinstates <a href=\"https://wikipedia.org/wiki/Slavery\" title=\"Slavery\">slavery</a> in the <a href=\"https://wikipedia.org/wiki/French_colonial_empire\" title=\"French colonial empire\">French colonies</a>, revoking its abolition in the <a href=\"https://wikipedia.org/wiki/French_Revolution\" title=\"French Revolution\">French Revolution</a>.", "no_year_html": "By the <a href=\"https://wikipedia.org/wiki/Law_of_20_May_1802\" title=\"Law of 20 May 1802\">Law of 20 May 1802</a>, <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"Napoleon Bonaparte\"><PERSON></a> reinstates <a href=\"https://wikipedia.org/wiki/Slavery\" title=\"Slavery\">slavery</a> in the <a href=\"https://wikipedia.org/wiki/French_colonial_empire\" title=\"French colonial empire\">French colonies</a>, revoking its abolition in the <a href=\"https://wikipedia.org/wiki/French_Revolution\" title=\"French Revolution\">French Revolution</a>.", "links": [{"title": "Law of 20 May 1802", "link": "https://wikipedia.org/wiki/Law_of_20_May_1802"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Slavery", "link": "https://wikipedia.org/wiki/Slavery"}, {"title": "French colonial empire", "link": "https://wikipedia.org/wiki/French_colonial_empire"}, {"title": "French Revolution", "link": "https://wikipedia.org/wiki/French_Revolution"}]}, {"year": "1813", "text": "<PERSON> leads his French troops into the Battle of Bautzen in Saxony, Germany, against the combined armies of Russia and Prussia. The battle ends the next day with a French victory.", "html": "1813 - <PERSON> leads his French troops into the <a href=\"https://wikipedia.org/wiki/Battle_of_Bautzen_(1813)\" title=\"Battle of Bautzen (1813)\">Battle of Bautzen</a> in <a href=\"https://wikipedia.org/wiki/Saxony\" title=\"Saxony\">Saxony</a>, Germany, against the combined armies of Russia and <a href=\"https://wikipedia.org/wiki/Prussia\" title=\"Prussia\">Prussia</a>. The battle ends the next day with a French victory.", "no_year_html": "<PERSON> leads his French troops into the <a href=\"https://wikipedia.org/wiki/Battle_of_Bautzen_(1813)\" title=\"Battle of Bautzen (1813)\">Battle of Bautzen</a> in <a href=\"https://wikipedia.org/wiki/Saxony\" title=\"Saxony\">Saxony</a>, Germany, against the combined armies of Russia and <a href=\"https://wikipedia.org/wiki/Prussia\" title=\"Prussia\">Prussia</a>. The battle ends the next day with a French victory.", "links": [{"title": "Battle of Bautzen (1813)", "link": "https://wikipedia.org/wiki/Battle_of_Bautzen_(1813)"}, {"title": "Saxony", "link": "https://wikipedia.org/wiki/Saxony"}, {"title": "Prussia", "link": "https://wikipedia.org/wiki/Prussia"}]}, {"year": "1861", "text": "American Civil War: The state of Kentucky proclaims its neutrality, which will last until September 3 when Confederate forces enter the state. Meanwhile, the State of North Carolina secedes from the Union.", "html": "1861 - <a href=\"https://wikipedia.org/wiki/American_Civil_War\" title=\"American Civil War\">American Civil War</a>: The state of <a href=\"https://wikipedia.org/wiki/Kentucky\" title=\"Kentucky\">Kentucky</a> proclaims its <a href=\"https://wikipedia.org/wiki/Country_neutrality_(international_relations)\" class=\"mw-redirect\" title=\"Country neutrality (international relations)\">neutrality</a>, which will last until <a href=\"https://wikipedia.org/wiki/September_3\" title=\"September 3\">September 3</a> when <a href=\"https://wikipedia.org/wiki/Confederate_States_of_America\" title=\"Confederate States of America\">Confederate</a> forces enter the state. Meanwhile, the State of North Carolina secedes from the Union.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/American_Civil_War\" title=\"American Civil War\">American Civil War</a>: The state of <a href=\"https://wikipedia.org/wiki/Kentucky\" title=\"Kentucky\">Kentucky</a> proclaims its <a href=\"https://wikipedia.org/wiki/Country_neutrality_(international_relations)\" class=\"mw-redirect\" title=\"Country neutrality (international relations)\">neutrality</a>, which will last until <a href=\"https://wikipedia.org/wiki/September_3\" title=\"September 3\">September 3</a> when <a href=\"https://wikipedia.org/wiki/Confederate_States_of_America\" title=\"Confederate States of America\">Confederate</a> forces enter the state. Meanwhile, the State of North Carolina secedes from the Union.", "links": [{"title": "American Civil War", "link": "https://wikipedia.org/wiki/American_Civil_War"}, {"title": "Kentucky", "link": "https://wikipedia.org/wiki/Kentucky"}, {"title": "Country neutrality (international relations)", "link": "https://wikipedia.org/wiki/Country_neutrality_(international_relations)"}, {"title": "September 3", "link": "https://wikipedia.org/wiki/September_3"}, {"title": "Confederate States of America", "link": "https://wikipedia.org/wiki/Confederate_States_of_America"}]}, {"year": "1862", "text": "U.S. President <PERSON> signs the Homestead Act into law, opening eighty-four million acres (340,000 km2) of public land to settlers.", "html": "1862 - U.S. President <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Abraham <PERSON>\"><PERSON></a> signs the <a href=\"https://wikipedia.org/wiki/Homestead_Act_of_1862\" class=\"mw-redirect\" title=\"Homestead Act of 1862\">Homestead Act</a> into law, opening eighty-four million acres (340,000 km) of public land to settlers.", "no_year_html": "U.S. President <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Abraham Lincoln\"><PERSON></a> signs the <a href=\"https://wikipedia.org/wiki/Homestead_Act_of_1862\" class=\"mw-redirect\" title=\"Homestead Act of 1862\">Homestead Act</a> into law, opening eighty-four million acres (340,000 km) of public land to settlers.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Homestead Act of 1862", "link": "https://wikipedia.org/wiki/Homestead_Act_of_1862"}]}, {"year": "1864", "text": "American Civil War: Battle of Ware Bottom Church: In the Virginia Bermuda Hundred campaign, 10,000 troops fight in this Confederate victory.", "html": "1864 - American Civil War: <a href=\"https://wikipedia.org/wiki/Battle_of_Ware_Bottom_Church\" title=\"Battle of Ware Bottom Church\">Battle of Ware Bottom Church</a>: In the <a href=\"https://wikipedia.org/wiki/Virginia\" title=\"Virginia\">Virginia</a> <a href=\"https://wikipedia.org/wiki/Bermuda_Hundred_campaign\" title=\"Bermuda Hundred campaign\">Bermuda Hundred campaign</a>, 10,000 troops fight in this Confederate victory.", "no_year_html": "American Civil War: <a href=\"https://wikipedia.org/wiki/Battle_of_Ware_Bottom_Church\" title=\"Battle of Ware Bottom Church\">Battle of Ware Bottom Church</a>: In the <a href=\"https://wikipedia.org/wiki/Virginia\" title=\"Virginia\">Virginia</a> <a href=\"https://wikipedia.org/wiki/Bermuda_Hundred_campaign\" title=\"Bermuda Hundred campaign\">Bermuda Hundred campaign</a>, 10,000 troops fight in this Confederate victory.", "links": [{"title": "Battle of Ware Bottom Church", "link": "https://wikipedia.org/wiki/Battle_of_Ware_Bottom_Church"}, {"title": "Virginia", "link": "https://wikipedia.org/wiki/Virginia"}, {"title": "Bermuda Hundred campaign", "link": "https://wikipedia.org/wiki/Bermuda_Hundred_campaign"}]}, {"year": "1873", "text": "<PERSON> and <PERSON> receive a U.S. patent for blue jeans with copper rivets.", "html": "1873 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> and <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> receive a U.S. <a href=\"https://wikipedia.org/wiki/Patent\" title=\"Patent\">patent</a> for blue <a href=\"https://wikipedia.org/wiki/Jeans\" title=\"<PERSON><PERSON>\">jeans</a> with <a href=\"https://wikipedia.org/wiki/Copper\" title=\"Copper\">copper</a> rivets.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> and <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> receive a U.S. <a href=\"https://wikipedia.org/wiki/Patent\" title=\"Patent\">patent</a> for blue <a href=\"https://wikipedia.org/wiki/Jeans\" title=\"<PERSON><PERSON>\">jeans</a> with <a href=\"https://wikipedia.org/wiki/Copper\" title=\"Copper\">copper</a> rivets.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>"}, {"title": "Patent", "link": "https://wikipedia.org/wiki/Patent"}, {"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>s"}, {"title": "Copper", "link": "https://wikipedia.org/wiki/Copper"}]}, {"year": "1875", "text": "Signing of the Metre Convention by 17 nations leading to the establishment of the International System of Units.", "html": "1875 - Signing of the <a href=\"https://wikipedia.org/wiki/Metre_Convention\" title=\"Metre Convention\">Metre Convention</a> by 17 nations leading to the establishment of the <a href=\"https://wikipedia.org/wiki/International_System_of_Units\" title=\"International System of Units\">International System of Units</a>.", "no_year_html": "Signing of the <a href=\"https://wikipedia.org/wiki/Metre_Convention\" title=\"Metre Convention\">Metre Convention</a> by 17 nations leading to the establishment of the <a href=\"https://wikipedia.org/wiki/International_System_of_Units\" title=\"International System of Units\">International System of Units</a>.", "links": [{"title": "Metre Convention", "link": "https://wikipedia.org/wiki/Metre_Convention"}, {"title": "International System of Units", "link": "https://wikipedia.org/wiki/International_System_of_Units"}]}, {"year": "1882", "text": "The Triple Alliance between the German Empire, Austria-Hungary and the Kingdom of Italy is formed.", "html": "1882 - The <a href=\"https://wikipedia.org/wiki/Triple_Alliance_(1882)\" title=\"Triple Alliance (1882)\">Triple Alliance</a> between the German Empire, <a href=\"https://wikipedia.org/wiki/Austria-Hungary\" title=\"Austria-Hungary\">Austria-Hungary</a> and the <a href=\"https://wikipedia.org/wiki/Kingdom_of_Italy\" title=\"Kingdom of Italy\">Kingdom of Italy</a> is formed.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Triple_Alliance_(1882)\" title=\"Triple Alliance (1882)\">Triple Alliance</a> between the German Empire, <a href=\"https://wikipedia.org/wiki/Austria-Hungary\" title=\"Austria-Hungary\">Austria-Hungary</a> and the <a href=\"https://wikipedia.org/wiki/Kingdom_of_Italy\" title=\"Kingdom of Italy\">Kingdom of Italy</a> is formed.", "links": [{"title": "Triple Alliance (1882)", "link": "https://wikipedia.org/wiki/Triple_Alliance_(1882)"}, {"title": "Austria-Hungary", "link": "https://wikipedia.org/wiki/Austria-Hungary"}, {"title": "Kingdom of Italy", "link": "https://wikipedia.org/wiki/Kingdom_of_Italy"}]}, {"year": "1883", "text": "Krakatoa begins to erupt; the volcano explodes three months later, killing more than 36,000 people.", "html": "1883 - <a href=\"https://wikipedia.org/wiki/Krakatoa\" title=\"Krakatoa\">Krakatoa</a> <a href=\"https://wikipedia.org/wiki/1883_eruption_of_Krakatoa\" title=\"1883 eruption of Krakatoa\">begins to erupt</a>; the volcano explodes three months later, killing more than 36,000 people.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Krakatoa\" title=\"Krakatoa\">Krakatoa</a> <a href=\"https://wikipedia.org/wiki/1883_eruption_of_Krakatoa\" title=\"1883 eruption of Krakatoa\">begins to erupt</a>; the volcano explodes three months later, killing more than 36,000 people.", "links": [{"title": "Krakatoa", "link": "https://wikipedia.org/wiki/Krakatoa"}, {"title": "1883 eruption of Krakatoa", "link": "https://wikipedia.org/wiki/1883_eruption_of_Krakatoa"}]}, {"year": "1891", "text": "History of cinema: The first public display of Thomas <PERSON>'s prototype kinetoscope.", "html": "1891 - <a href=\"https://wikipedia.org/wiki/History_of_film\" title=\"History of film\">History of cinema</a>: The first public display of <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Thomas Edison\"><PERSON></a>'s prototype <a href=\"https://wikipedia.org/wiki/Kinetoscope\" title=\"Kinetoscope\">kinetoscope</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/History_of_film\" title=\"History of film\">History of cinema</a>: The first public display of <a href=\"https://wikipedia.org/wiki/Thomas_<PERSON>\" title=\"Thomas Edison\"><PERSON></a>'s prototype <a href=\"https://wikipedia.org/wiki/Kinetoscope\" title=\"Kinetoscope\">kinetoscope</a>.", "links": [{"title": "History of film", "link": "https://wikipedia.org/wiki/History_of_film"}, {"title": "Thomas <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Kinetoscope", "link": "https://wikipedia.org/wiki/Kinetoscope"}]}, {"year": "1902", "text": "Cuba gains independence from the United States. <PERSON><PERSON> becomes the country's first President.", "html": "1902 - <a href=\"https://wikipedia.org/wiki/Cuba\" title=\"Cuba\">Cuba</a> gains independence from the United States. <a href=\"https://wikipedia.org/wiki/Tom%C3%A1s_Estrada_Palma\" title=\"Tomás Estrada Palma\"><PERSON><PERSON></a> becomes the country's first President.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Cuba\" title=\"Cuba\">Cuba</a> gains independence from the United States. <a href=\"https://wikipedia.org/wiki/Tom%C3%A1s_Estrada_Palma\" title=\"Tomás Estrada Palma\"><PERSON><PERSON></a> becomes the country's first President.", "links": [{"title": "Cuba", "link": "https://wikipedia.org/wiki/Cuba"}, {"title": "Tomás Estrada Palma", "link": "https://wikipedia.org/wiki/Tom%C3%A1s_Estrada_Palma"}]}, {"year": "1927", "text": "Treaty of Jeddah: The United Kingdom recognizes the sovereignty of King <PERSON> in the Kingdoms of Hejaz and Nejd, which later merge to become the Kingdom of Saudi Arabia.", "html": "1927 - <a href=\"https://wikipedia.org/wiki/Treaty_of_Jeddah_(1927)\" title=\"Treaty of Jeddah (1927)\">Treaty of Jeddah</a>: The United Kingdom recognizes the <a href=\"https://wikipedia.org/wiki/Sovereignty\" title=\"Sovereignty\">sovereignty</a> of King <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> in the Kingdoms of <a href=\"https://wikipedia.org/wiki/Hejaz\" title=\"Hejaz\">Hejaz</a> and <a href=\"https://wikipedia.org/wiki/Najd\" title=\"Najd\">Nejd</a>, which later merge to become the Kingdom of <a href=\"https://wikipedia.org/wiki/Saudi_Arabia\" title=\"Saudi Arabia\">Saudi Arabia</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Treaty_of_Jeddah_(1927)\" title=\"Treaty of Jeddah (1927)\">Treaty of Jeddah</a>: The United Kingdom recognizes the <a href=\"https://wikipedia.org/wiki/Sovereignty\" title=\"Sovereignty\">sovereignty</a> of King <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> in the Kingdoms of <a href=\"https://wikipedia.org/wiki/Hejaz\" title=\"Hejaz\">Hejaz</a> and <a href=\"https://wikipedia.org/wiki/Najd\" title=\"Najd\">Nejd</a>, which later merge to become the Kingdom of <a href=\"https://wikipedia.org/wiki/Saudi_Arabia\" title=\"Saudi Arabia\">Saudi Arabia</a>.", "links": [{"title": "Treaty of Jeddah (1927)", "link": "https://wikipedia.org/wiki/Treaty_of_Jeddah_(1927)"}, {"title": "Sovereignty", "link": "https://wikipedia.org/wiki/Sovereignty"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>z"}, {"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Najd"}, {"title": "Saudi Arabia", "link": "https://wikipedia.org/wiki/Saudi_Arabia"}]}, {"year": "1927", "text": "<PERSON> takes off for Paris from Roosevelt Field in Long Island, N.Y., aboard the Spirit of St. Louis on the first nonstop solo flight across the Atlantic Ocean, landing .mw-parser-output .frac{white-space:nowrap}.mw-parser-output .frac .num,.mw-parser-output .frac .den{font-size:80%;line-height:0;vertical-align:super}.mw-parser-output .frac .den{vertical-align:sub}.mw-parser-output .sr-only{border:0;clip:rect(0,0,0,0);clip-path:polygon(0px 0px,0px 0px,0px 0px);height:1px;margin:-1px;overflow:hidden;padding:0;position:absolute;width:1px}33+1⁄2 hours later.", "html": "1927 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> takes off for Paris from Roosevelt Field in Long Island, N.Y., aboard the <a href=\"https://wikipedia.org/wiki/Spirit_of_St._Louis\" title=\"Spirit of St. Louis\">Spirit of St. Louis</a> on the first nonstop solo flight across the Atlantic Ocean, landing <style data-mw-deduplicate=\"TemplateStyles:r1154941027\">.mw-parser-output .frac{white-space:nowrap}.mw-parser-output .frac .num,.mw-parser-output .frac .den{font-size:80%;line-height:0;vertical-align:super}.mw-parser-output .frac .den{vertical-align:sub}.mw-parser-output .sr-only{border:0;clip:rect(0,0,0,0);clip-path:polygon(0px 0px,0px 0px,0px 0px);height:1px;margin:-1px;overflow:hidden;padding:0;position:absolute;width:1px}</style>\n<span class=\"frac\">33<span class=\"sr-only\">+</span><span class=\"num\">1</span>⁄<span class=\"den\">2</span></span> hours later.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> takes off for Paris from Roosevelt Field in Long Island, N.Y., aboard the <a href=\"https://wikipedia.org/wiki/Spirit_of_St._Louis\" title=\"Spirit of St. Louis\">Spirit of St. Louis</a> on the first nonstop solo flight across the Atlantic Ocean, landing <style data-mw-deduplicate=\"TemplateStyles:r1154941027\">.mw-parser-output .frac{white-space:nowrap}.mw-parser-output .frac .num,.mw-parser-output .frac .den{font-size:80%;line-height:0;vertical-align:super}.mw-parser-output .frac .den{vertical-align:sub}.mw-parser-output .sr-only{border:0;clip:rect(0,0,0,0);clip-path:polygon(0px 0px,0px 0px,0px 0px);height:1px;margin:-1px;overflow:hidden;padding:0;position:absolute;width:1px}</style>\n<span class=\"frac\">33<span class=\"sr-only\">+</span><span class=\"num\">1</span>⁄<span class=\"den\">2</span></span> hours later.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}, {"title": "Spirit of St. Louis", "link": "https://wikipedia.org/wiki/<PERSON>_of_St._Louis"}]}, {"year": "1932", "text": "<PERSON> takes off from Newfoundland to begin the world's first solo nonstop flight across the Atlantic Ocean by a female pilot, landing in Ireland the next day.", "html": "1932 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> takes off from <a href=\"https://wikipedia.org/wiki/Newfoundland_(island)\" title=\"Newfoundland (island)\">Newfoundland</a> to begin the world's first solo nonstop flight across the Atlantic Ocean by a female pilot, landing in Ireland the next day.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> takes off from <a href=\"https://wikipedia.org/wiki/Newfoundland_(island)\" title=\"Newfoundland (island)\">Newfoundland</a> to begin the world's first solo nonstop flight across the Atlantic Ocean by a female pilot, landing in Ireland the next day.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Newfoundland (island)", "link": "https://wikipedia.org/wiki/Newfoundland_(island)"}]}, {"year": "1940", "text": "The Holocaust: The first prisoners arrive at a new concentration camp at Auschwitz.", "html": "1940 - <a href=\"https://wikipedia.org/wiki/The_Holocaust\" title=\"The Holocaust\">The Holocaust</a>: The first prisoners arrive at a <a href=\"https://wikipedia.org/wiki/Auschwitz_concentration_camp\" title=\"Auschwitz concentration camp\">new concentration camp at Auschwitz</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/The_Holocaust\" title=\"The Holocaust\">The Holocaust</a>: The first prisoners arrive at a <a href=\"https://wikipedia.org/wiki/Auschwitz_concentration_camp\" title=\"Auschwitz concentration camp\">new concentration camp at Auschwitz</a>.", "links": [{"title": "The Holocaust", "link": "https://wikipedia.org/wiki/The_Holocaust"}, {"title": "Auschwitz concentration camp", "link": "https://wikipedia.org/wiki/Auschwitz_concentration_camp"}]}, {"year": "1941", "text": "World War II: Battle of Crete: German paratroops invade Crete.", "html": "1941 - World War II: <a href=\"https://wikipedia.org/wiki/Battle_of_Crete\" title=\"Battle of Crete\">Battle of Crete</a>: <a href=\"https://wikipedia.org/wiki/Nazi_Germany\" title=\"Nazi Germany\">German</a> <a href=\"https://wikipedia.org/wiki/Fallschirmj%C3%A4ger_(World_War_II)\" class=\"mw-redirect\" title=\"Fallschirmjäger (World War II)\">paratroops</a> invade <a href=\"https://wikipedia.org/wiki/Crete\" title=\"Crete\">Crete</a>.", "no_year_html": "World War II: <a href=\"https://wikipedia.org/wiki/Battle_of_Crete\" title=\"Battle of Crete\">Battle of Crete</a>: <a href=\"https://wikipedia.org/wiki/Nazi_Germany\" title=\"Nazi Germany\">German</a> <a href=\"https://wikipedia.org/wiki/Fallschirmj%C3%A4ger_(World_War_II)\" class=\"mw-redirect\" title=\"Fallschirmjäger (World War II)\">paratroops</a> invade <a href=\"https://wikipedia.org/wiki/Crete\" title=\"Crete\">Crete</a>.", "links": [{"title": "Battle of Crete", "link": "https://wikipedia.org/wiki/Battle_of_Crete"}, {"title": "Nazi Germany", "link": "https://wikipedia.org/wiki/Nazi_Germany"}, {"title": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> (World War II)", "link": "https://wikipedia.org/wiki/Fallschirmj%C3%A4ger_(World_War_II)"}, {"title": "Crete", "link": "https://wikipedia.org/wiki/Crete"}]}, {"year": "1943", "text": "The Luttra Woman, a bog body from the Early Neolithic period (radiocarbon-dated c. 3928-3651 BC), was discovered near Luttra, Sweden.", "html": "1943 - The <a href=\"https://wikipedia.org/wiki/<PERSON>tt<PERSON>_Woman\" title=\"Luttra Woman\">Luttra Woman</a>, a <a href=\"https://wikipedia.org/wiki/Bog_body\" title=\"Bog body\">bog body</a> from the Early <a href=\"https://wikipedia.org/wiki/Neolithic\" title=\"Neolithic\">Neolithic</a> period (<a href=\"https://wikipedia.org/wiki/Radiocarbon_dating\" title=\"Radiocarbon dating\">radiocarbon-dated</a> <abbr title=\"circa\">c.</abbr><span style=\"white-space:nowrap;\"> 3928-3651 BC</span>), was discovered near <a href=\"https://wikipedia.org/wiki/Luttra\" title=\"Luttra\">Lu<PERSON>ra</a>, <a href=\"https://wikipedia.org/wiki/Sweden\" title=\"Sweden\">Sweden</a>.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/<PERSON>tt<PERSON>_Woman\" title=\"Luttra Woman\">Luttra Woman</a>, a <a href=\"https://wikipedia.org/wiki/Bog_body\" title=\"Bog body\">bog body</a> from the Early <a href=\"https://wikipedia.org/wiki/Neolithic\" title=\"Neolithic\">Neolithic</a> period (<a href=\"https://wikipedia.org/wiki/Radiocarbon_dating\" title=\"Radiocarbon dating\">radiocarbon-dated</a> <abbr title=\"circa\">c.</abbr><span style=\"white-space:nowrap;\"> 3928-3651 BC</span>), was discovered near <a href=\"https://wikipedia.org/wiki/Luttra\" title=\"Lu<PERSON>ra\">Lu<PERSON>ra</a>, <a href=\"https://wikipedia.org/wiki/Sweden\" title=\"Sweden\">Sweden</a>.", "links": [{"title": "Luttra Woman", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_Woman"}, {"title": "Bog body", "link": "https://wikipedia.org/wiki/Bog_body"}, {"title": "Neolithic", "link": "https://wikipedia.org/wiki/Neolithic"}, {"title": "Radiocarbon dating", "link": "https://wikipedia.org/wiki/Radiocarbon_dating"}, {"title": "Luttra", "link": "https://wikipedia.org/wiki/Luttra"}, {"title": "Sweden", "link": "https://wikipedia.org/wiki/Sweden"}]}, {"year": "1948", "text": "Generalissimo <PERSON> wins the 1948 Republic of China presidential election and is sworn in as the first President of the Republic of China at Nanjing.", "html": "1948 - General<PERSON><PERSON> <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>\" title=\"<PERSON>\"><PERSON></a> wins the <a href=\"https://wikipedia.org/wiki/1948_Chinese_presidential_election\" title=\"1948 Chinese presidential election\">1948 Republic of China presidential election</a> and is sworn in as the first <a href=\"https://wikipedia.org/wiki/President_of_the_Republic_of_China\" title=\"President of the Republic of China\">President of the Republic of China</a> at <a href=\"https://wikipedia.org/wiki/Nanjing\" title=\"Nanjing\">Nanjing</a>.", "no_year_html": "Generalissimo <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> wins the <a href=\"https://wikipedia.org/wiki/1948_Chinese_presidential_election\" title=\"1948 Chinese presidential election\">1948 Republic of China presidential election</a> and is sworn in as the first <a href=\"https://wikipedia.org/wiki/President_of_the_Republic_of_China\" title=\"President of the Republic of China\">President of the Republic of China</a> at <a href=\"https://wikipedia.org/wiki/Nanjing\" title=\"Nanjing\">Nanjing</a>.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>-<PERSON>"}, {"title": "1948 Chinese presidential election", "link": "https://wikipedia.org/wiki/1948_Chinese_presidential_election"}, {"title": "President of the Republic of China", "link": "https://wikipedia.org/wiki/President_of_the_Republic_of_China"}, {"title": "Nanjing", "link": "https://wikipedia.org/wiki/Nanjing"}]}, {"year": "1949", "text": "In the United States, the Armed Forces Security Agency, the predecessor to the National Security Agency, is established.", "html": "1949 - In the United States, the Armed Forces Security Agency, the predecessor to the <a href=\"https://wikipedia.org/wiki/National_Security_Agency\" title=\"National Security Agency\">National Security Agency</a>, is established.", "no_year_html": "In the United States, the Armed Forces Security Agency, the predecessor to the <a href=\"https://wikipedia.org/wiki/National_Security_Agency\" title=\"National Security Agency\">National Security Agency</a>, is established.", "links": [{"title": "National Security Agency", "link": "https://wikipedia.org/wiki/National_Security_Agency"}]}, {"year": "1956", "text": "In Operation Redwing, the first United States airborne hydrogen bomb is dropped over Bikini Atoll in the Pacific Ocean.", "html": "1956 - In <a href=\"https://wikipedia.org/wiki/Operation_Redwing\" title=\"Operation Redwing\">Operation Redwing</a>, the first United States airborne <a href=\"https://wikipedia.org/wiki/Hydrogen_bomb\" class=\"mw-redirect\" title=\"Hydrogen bomb\">hydrogen bomb</a> is dropped over <a href=\"https://wikipedia.org/wiki/Bikini_Atoll\" title=\"Bikini Atoll\">Bikini Atoll</a> in the Pacific Ocean.", "no_year_html": "In <a href=\"https://wikipedia.org/wiki/Operation_Redwing\" title=\"Operation Redwing\">Operation Redwing</a>, the first United States airborne <a href=\"https://wikipedia.org/wiki/Hydrogen_bomb\" class=\"mw-redirect\" title=\"Hydrogen bomb\">hydrogen bomb</a> is dropped over <a href=\"https://wikipedia.org/wiki/Bikini_Atoll\" title=\"Bikini Atoll\">Bikini Atoll</a> in the Pacific Ocean.", "links": [{"title": "Operation Redwing", "link": "https://wikipedia.org/wiki/Operation_Redwing"}, {"title": "Hydrogen bomb", "link": "https://wikipedia.org/wiki/Hydrogen_bomb"}, {"title": "Bikini Atoll", "link": "https://wikipedia.org/wiki/Bikini_Atoll"}]}, {"year": "1958", "text": "Capital Airlines Flight 300 collides in mid-air with a United States Air Force Lockheed T-33 over Brunswick, Maryland, killing 12.", "html": "1958 - <a href=\"https://wikipedia.org/wiki/Capital_Airlines_Flight_300\" title=\"Capital Airlines Flight 300\">Capital Airlines Flight 300</a> collides in mid-air with a <a href=\"https://wikipedia.org/wiki/United_States_Air_Force\" title=\"United States Air Force\">United States Air Force</a> <a href=\"https://wikipedia.org/wiki/Lockheed_T-33\" title=\"Lockheed T-33\">Lockheed T-33</a> over <a href=\"https://wikipedia.org/wiki/Brunswick,_Maryland\" title=\"Brunswick, Maryland\">Brunswick, Maryland</a>, killing 12.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Capital_Airlines_Flight_300\" title=\"Capital Airlines Flight 300\">Capital Airlines Flight 300</a> collides in mid-air with a <a href=\"https://wikipedia.org/wiki/United_States_Air_Force\" title=\"United States Air Force\">United States Air Force</a> <a href=\"https://wikipedia.org/wiki/Lockheed_T-33\" title=\"Lockheed T-33\">Lockheed T-33</a> over <a href=\"https://wikipedia.org/wiki/Brunswick,_Maryland\" title=\"Brunswick, Maryland\">Brunswick, Maryland</a>, killing 12.", "links": [{"title": "Capital Airlines Flight 300", "link": "https://wikipedia.org/wiki/Capital_Airlines_Flight_300"}, {"title": "United States Air Force", "link": "https://wikipedia.org/wiki/United_States_Air_Force"}, {"title": "Lockheed T-33", "link": "https://wikipedia.org/wiki/Lockheed_T-33"}, {"title": "Brunswick, Maryland", "link": "https://wikipedia.org/wiki/Brunswick,_Maryland"}]}, {"year": "1964", "text": "Discovery of the cosmic microwave background radiation by <PERSON> and <PERSON><PERSON>.", "html": "1964 - <a href=\"https://wikipedia.org/wiki/Discovery_of_cosmic_microwave_background_radiation\" title=\"Discovery of cosmic microwave background radiation\">Discovery</a> of the <a href=\"https://wikipedia.org/wiki/Cosmic_microwave_background_radiation\" class=\"mw-redirect\" title=\"Cosmic microwave background radiation\">cosmic microwave background radiation</a> by <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> and <a href=\"https://wikipedia.org/wiki/Arno_Penzias\" class=\"mw-redirect\" title=\"Arno <PERSON>\"><PERSON><PERSON></a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Discovery_of_cosmic_microwave_background_radiation\" title=\"Discovery of cosmic microwave background radiation\">Discovery</a> of the <a href=\"https://wikipedia.org/wiki/Cosmic_microwave_background_radiation\" class=\"mw-redirect\" title=\"Cosmic microwave background radiation\">cosmic microwave background radiation</a> by <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> and <a href=\"https://wikipedia.org/wiki/Arno_Penzias\" class=\"mw-redirect\" title=\"Arno <PERSON>\"><PERSON><PERSON></a>.", "links": [{"title": "Discovery of cosmic microwave background radiation", "link": "https://wikipedia.org/wiki/Discovery_of_cosmic_microwave_background_radiation"}, {"title": "Cosmic microwave background radiation", "link": "https://wikipedia.org/wiki/Cosmic_microwave_background_radiation"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/A<PERSON>_<PERSON>s"}]}, {"year": "1965", "text": "One hundred twenty-one people are killed when Pakistan International Airlines Flight 705 crashes at Cairo International Airport.", "html": "1965 - One hundred twenty-one people are killed when <a href=\"https://wikipedia.org/wiki/Pakistan_International_Airlines_Flight_705\" title=\"Pakistan International Airlines Flight 705\">Pakistan International Airlines Flight 705</a> crashes at <a href=\"https://wikipedia.org/wiki/Cairo_International_Airport\" title=\"Cairo International Airport\">Cairo International Airport</a>.", "no_year_html": "One hundred twenty-one people are killed when <a href=\"https://wikipedia.org/wiki/Pakistan_International_Airlines_Flight_705\" title=\"Pakistan International Airlines Flight 705\">Pakistan International Airlines Flight 705</a> crashes at <a href=\"https://wikipedia.org/wiki/Cairo_International_Airport\" title=\"Cairo International Airport\">Cairo International Airport</a>.", "links": [{"title": "Pakistan International Airlines Flight 705", "link": "https://wikipedia.org/wiki/Pakistan_International_Airlines_Flight_705"}, {"title": "Cairo International Airport", "link": "https://wikipedia.org/wiki/Cairo_International_Airport"}]}, {"year": "1967", "text": "The Popular Movement of the Revolution political party is established in the Democratic Republic of the Congo.", "html": "1967 - The <a href=\"https://wikipedia.org/wiki/Popular_Movement_of_the_Revolution\" title=\"Popular Movement of the Revolution\">Popular Movement of the Revolution</a> <a href=\"https://wikipedia.org/wiki/Political_party\" title=\"Political party\">political party</a> is established in the <a href=\"https://wikipedia.org/wiki/Democratic_Republic_of_the_Congo\" title=\"Democratic Republic of the Congo\">Democratic Republic of the Congo</a>.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Popular_Movement_of_the_Revolution\" title=\"Popular Movement of the Revolution\">Popular Movement of the Revolution</a> <a href=\"https://wikipedia.org/wiki/Political_party\" title=\"Political party\">political party</a> is established in the <a href=\"https://wikipedia.org/wiki/Democratic_Republic_of_the_Congo\" title=\"Democratic Republic of the Congo\">Democratic Republic of the Congo</a>.", "links": [{"title": "Popular Movement of the Revolution", "link": "https://wikipedia.org/wiki/Popular_Movement_of_the_Revolution"}, {"title": "Political party", "link": "https://wikipedia.org/wiki/Political_party"}, {"title": "Democratic Republic of the Congo", "link": "https://wikipedia.org/wiki/Democratic_Republic_of_the_Congo"}]}, {"year": "1969", "text": "The Battle of Hamburger Hill in Vietnam ends.", "html": "1969 - The <a href=\"https://wikipedia.org/wiki/Battle_of_Hamburger_Hill\" title=\"Battle of Hamburger Hill\">Battle of Hamburger Hill</a> in <a href=\"https://wikipedia.org/wiki/Vietnam\" title=\"Vietnam\">Vietnam</a> ends.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Battle_of_Hamburger_Hill\" title=\"Battle of Hamburger Hill\">Battle of Hamburger Hill</a> in <a href=\"https://wikipedia.org/wiki/Vietnam\" title=\"Vietnam\">Vietnam</a> ends.", "links": [{"title": "Battle of Hamburger Hill", "link": "https://wikipedia.org/wiki/Battle_of_Hamburger_Hill"}, {"title": "Vietnam", "link": "https://wikipedia.org/wiki/Vietnam"}]}, {"year": "1971", "text": "In the Chuknagar massacre, Pakistani forces massacre thousands, mostly Bengali Hindus.", "html": "1971 - In the <a href=\"https://wikipedia.org/wiki/Chuknagar_massacre\" title=\"Chuknagar massacre\">Chuknagar massacre</a>, Pakistani forces massacre thousands, mostly Bengali Hindus.", "no_year_html": "In the <a href=\"https://wikipedia.org/wiki/Chuknagar_massacre\" title=\"Chuknagar massacre\">Chuknagar massacre</a>, Pakistani forces massacre thousands, mostly Bengali Hindus.", "links": [{"title": "Chuknagar massacre", "link": "https://wikipedia.org/wiki/Chuknagar_massacre"}]}, {"year": "1980", "text": "In a referendum in Quebec, the population rejects, with 60% of the vote, a government proposal to move towards independence from Canada.", "html": "1980 - In a <a href=\"https://wikipedia.org/wiki/1980_Quebec_referendum\" title=\"1980 Quebec referendum\">referendum</a> in Quebec, the population rejects, with 60% of the vote, a government proposal to move towards independence from Canada.", "no_year_html": "In a <a href=\"https://wikipedia.org/wiki/1980_Quebec_referendum\" title=\"1980 Quebec referendum\">referendum</a> in Quebec, the population rejects, with 60% of the vote, a government proposal to move towards independence from Canada.", "links": [{"title": "1980 Quebec referendum", "link": "https://wikipedia.org/wiki/1980_Quebec_referendum"}]}, {"year": "1983", "text": "First publications of the discovery of the HIV virus that causes AIDS in the journal Science by a team of French scientists including <PERSON><PERSON>, <PERSON><PERSON><PERSON>, and <PERSON>.", "html": "1983 - First publications of the discovery of the <a href=\"https://wikipedia.org/wiki/HIV\" title=\"HIV\">HIV</a> <a href=\"https://wikipedia.org/wiki/Virus\" title=\"Virus\">virus</a> that causes <a href=\"https://wikipedia.org/wiki/AIDS\" class=\"mw-redirect\" title=\"AIDS\">AIDS</a> in the journal <i><a href=\"https://wikipedia.org/wiki/Science_(journal)\" title=\"Science (journal)\">Science</a></i> by a team of French scientists including <a href=\"https://wikipedia.org/wiki/Fran%C3%A7oise_Barr%C3%A9-Sinoussi\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, and <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>.", "no_year_html": "First publications of the discovery of the <a href=\"https://wikipedia.org/wiki/HIV\" title=\"HIV\">HIV</a> <a href=\"https://wikipedia.org/wiki/Virus\" title=\"Virus\">virus</a> that causes <a href=\"https://wikipedia.org/wiki/AIDS\" class=\"mw-redirect\" title=\"AIDS\">AIDS</a> in the journal <i><a href=\"https://wikipedia.org/wiki/Science_(journal)\" title=\"Science (journal)\">Science</a></i> by a team of French scientists including <a href=\"https://wikipedia.org/wiki/Fran%C3%A7oise_Barr%C3%A9-Sinoussi\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, and <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>.", "links": [{"title": "HIV", "link": "https://wikipedia.org/wiki/HIV"}, {"title": "Virus", "link": "https://wikipedia.org/wiki/Virus"}, {"title": "AIDS", "link": "https://wikipedia.org/wiki/AIDS"}, {"title": "Science (journal)", "link": "https://wikipedia.org/wiki/Science_(journal)"}, {"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Fran%C3%A7oise_Barr%C3%A9-Sinoussi"}, {"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1983", "text": "Church Street bombing: A car bomb planted by UMkhonto we Sizwe explodes on Church Street in South Africa's capital, Pretoria, killing 19 people and injuring 217 others.", "html": "1983 - <a href=\"https://wikipedia.org/wiki/Church_Street,_Pretoria_bombing\" title=\"Church Street, Pretoria bombing\">Church Street bombing</a>: A car bomb planted by <a href=\"https://wikipedia.org/wiki/UMkhonto_we_Sizwe\" class=\"mw-redirect\" title=\"UMkhonto we Sizwe\">UMkhonto we Sizwe</a> explodes on Church Street in South Africa's capital, <a href=\"https://wikipedia.org/wiki/Pretoria\" title=\"Pretoria\">Pretoria</a>, killing 19 people and injuring 217 others.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Church_Street,_Pretoria_bombing\" title=\"Church Street, Pretoria bombing\">Church Street bombing</a>: A car bomb planted by <a href=\"https://wikipedia.org/wiki/UMkhonto_we_Sizwe\" class=\"mw-redirect\" title=\"UMkhonto we Sizwe\">UMkhonto we Sizwe</a> explodes on Church Street in South Africa's capital, <a href=\"https://wikipedia.org/wiki/Pretoria\" title=\"Pretoria\">Pretoria</a>, killing 19 people and injuring 217 others.", "links": [{"title": "Church Street, Pretoria bombing", "link": "https://wikipedia.org/wiki/Church_Street,_Pretoria_bombing"}, {"title": "UMkhonto we Sizwe", "link": "https://wikipedia.org/wiki/UMkhonto_we_Sizwe"}, {"title": "Pretoria", "link": "https://wikipedia.org/wiki/Pretoria"}]}, {"year": "1985", "text": "Radio Martí, part of the Voice of America service, begins broadcasting to Cuba.", "html": "1985 - <a href=\"https://wikipedia.org/wiki/Radio_Mart%C3%AD\" class=\"mw-redirect\" title=\"Radio Martí\">Radio Martí</a>, part of the <a href=\"https://wikipedia.org/wiki/Voice_of_America\" title=\"Voice of America\">Voice of America</a> service, begins broadcasting to <a href=\"https://wikipedia.org/wiki/Cuba\" title=\"Cuba\">Cuba</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Radio_Mart%C3%AD\" class=\"mw-redirect\" title=\"Radio Martí\">Radio Martí</a>, part of the <a href=\"https://wikipedia.org/wiki/Voice_of_America\" title=\"Voice of America\">Voice of America</a> service, begins broadcasting to <a href=\"https://wikipedia.org/wiki/Cuba\" title=\"Cuba\">Cuba</a>.", "links": [{"title": "Radio Martí", "link": "https://wikipedia.org/wiki/Radio_Mart%C3%AD"}, {"title": "Voice of America", "link": "https://wikipedia.org/wiki/Voice_of_America"}, {"title": "Cuba", "link": "https://wikipedia.org/wiki/Cuba"}]}, {"year": "1989", "text": "The Chinese authorities declare martial law in the face of pro-democracy demonstrations, setting the scene for the Tiananmen Square massacre.", "html": "1989 - The Chinese authorities declare martial law in the face of pro-<a href=\"https://wikipedia.org/wiki/Democracy\" title=\"Democracy\">democracy</a> demonstrations, setting the scene for the <a href=\"https://wikipedia.org/wiki/Tiananmen_Square_massacre\" class=\"mw-redirect\" title=\"Tiananmen Square massacre\">Tiananmen Square massacre</a>.", "no_year_html": "The Chinese authorities declare martial law in the face of pro-<a href=\"https://wikipedia.org/wiki/Democracy\" title=\"Democracy\">democracy</a> demonstrations, setting the scene for the <a href=\"https://wikipedia.org/wiki/Tiananmen_Square_massacre\" class=\"mw-redirect\" title=\"Tiananmen Square massacre\">Tiananmen Square massacre</a>.", "links": [{"title": "Democracy", "link": "https://wikipedia.org/wiki/Democracy"}, {"title": "Tiananmen Square massacre", "link": "https://wikipedia.org/wiki/Tiananmen_Square_massacre"}]}, {"year": "1990", "text": "The first post-Communist presidential and parliamentary elections are held in Romania.", "html": "1990 - The first post-<a href=\"https://wikipedia.org/wiki/Communism\" title=\"Communism\">Communist</a> presidential and parliamentary elections are held in <a href=\"https://wikipedia.org/wiki/Romania\" title=\"Romania\">Romania</a>.", "no_year_html": "The first post-<a href=\"https://wikipedia.org/wiki/Communism\" title=\"Communism\">Communist</a> presidential and parliamentary elections are held in <a href=\"https://wikipedia.org/wiki/Romania\" title=\"Romania\">Romania</a>.", "links": [{"title": "Communism", "link": "https://wikipedia.org/wiki/Communism"}, {"title": "Romania", "link": "https://wikipedia.org/wiki/Romania"}]}, {"year": "1996", "text": "Civil rights: The Supreme Court of the United States rules in <PERSON><PERSON> v<PERSON> against a law that would have prevented any city, town or county in the state of Colorado from taking any legislative, executive, or judicial action to protect the rights of gays and lesbians.", "html": "1996 - <a href=\"https://wikipedia.org/wiki/LGBT_rights_in_the_United_States\" class=\"mw-redirect\" title=\"LGBT rights in the United States\">Civil rights</a>: The <a href=\"https://wikipedia.org/wiki/Supreme_Court_of_the_United_States\" title=\"Supreme Court of the United States\">Supreme Court of the United States</a> rules in <i><a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_v<PERSON>_<PERSON>\" title=\"<PERSON><PERSON> v<PERSON>\"><PERSON><PERSON> v<PERSON></a></i> against a law that would have prevented any city, town or county in the state of <a href=\"https://wikipedia.org/wiki/Colorado\" title=\"Colorado\">Colorado</a> from taking any legislative, executive, or judicial action to protect the rights of <a href=\"https://wikipedia.org/wiki/Homosexuality\" title=\"Homosexuality\">gays and lesbians</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/LGBT_rights_in_the_United_States\" class=\"mw-redirect\" title=\"LGBT rights in the United States\">Civil rights</a>: The <a href=\"https://wikipedia.org/wiki/Supreme_Court_of_the_United_States\" title=\"Supreme Court of the United States\">Supreme Court of the United States</a> rules in <i><a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_v<PERSON>_<PERSON>\" title=\"<PERSON><PERSON> v<PERSON>\"><PERSON><PERSON> v<PERSON></a></i> against a law that would have prevented any city, town or county in the state of <a href=\"https://wikipedia.org/wiki/Colorado\" title=\"Colorado\">Colorado</a> from taking any legislative, executive, or judicial action to protect the rights of <a href=\"https://wikipedia.org/wiki/Homosexuality\" title=\"Homosexuality\">gays and lesbians</a>.", "links": [{"title": "LGBT rights in the United States", "link": "https://wikipedia.org/wiki/LGBT_rights_in_the_United_States"}, {"title": "Supreme Court of the United States", "link": "https://wikipedia.org/wiki/Supreme_Court_of_the_United_States"}, {"title": "Romer v. <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_v._<PERSON>"}, {"title": "Colorado", "link": "https://wikipedia.org/wiki/Colorado"}, {"title": "Homosexuality", "link": "https://wikipedia.org/wiki/Homosexuality"}]}, {"year": "2002", "text": "The independence of East Timor is recognized by Portugal, formally ending 23 years of Indonesian rule and three years of provisional UN administration (Portugal itself is the former colonizer of East Timor until 1976).", "html": "2002 - The independence of <a href=\"https://wikipedia.org/wiki/East_Timor\" class=\"mw-redirect\" title=\"East Timor\">East Timor</a> is recognized by Portugal, formally ending 23 years of <a href=\"https://wikipedia.org/wiki/Indonesia\" title=\"Indonesia\">Indonesian</a> rule and three years of provisional UN administration (Portugal itself is the former <a href=\"https://wikipedia.org/wiki/Portuguese_Empire\" title=\"Portuguese Empire\">colonizer</a> of East Timor until 1976).", "no_year_html": "The independence of <a href=\"https://wikipedia.org/wiki/East_Timor\" class=\"mw-redirect\" title=\"East Timor\">East Timor</a> is recognized by Portugal, formally ending 23 years of <a href=\"https://wikipedia.org/wiki/Indonesia\" title=\"Indonesia\">Indonesian</a> rule and three years of provisional UN administration (Portugal itself is the former <a href=\"https://wikipedia.org/wiki/Portuguese_Empire\" title=\"Portuguese Empire\">colonizer</a> of East Timor until 1976).", "links": [{"title": "East Timor", "link": "https://wikipedia.org/wiki/East_Timor"}, {"title": "Indonesia", "link": "https://wikipedia.org/wiki/Indonesia"}, {"title": "Portuguese Empire", "link": "https://wikipedia.org/wiki/Portuguese_Empire"}]}, {"year": "2009", "text": "An Indonesian Air Force Lockheed L-100 Hercules crashes in Magetan Regency, killing 99.", "html": "2009 - An <a href=\"https://wikipedia.org/wiki/Indonesian_Air_Force\" title=\"Indonesian Air Force\">Indonesian Air Force</a> <a href=\"https://wikipedia.org/wiki/Lockheed_L-100_Hercules\" title=\"Lockheed L-100 Hercules\">Lockheed L-100 Hercules</a> <a href=\"https://wikipedia.org/wiki/2009_Indonesian_Air_Force_L-100_crash\" title=\"2009 Indonesian Air Force L-100 crash\">crashes</a> in <a href=\"https://wikipedia.org/wiki/Magetan_Regency\" title=\"Magetan Regency\">Magetan Regency</a>, killing 99.", "no_year_html": "An <a href=\"https://wikipedia.org/wiki/Indonesian_Air_Force\" title=\"Indonesian Air Force\">Indonesian Air Force</a> <a href=\"https://wikipedia.org/wiki/Lockheed_L-100_Hercules\" title=\"Lockheed L-100 Hercules\">Lockheed L-100 Hercules</a> <a href=\"https://wikipedia.org/wiki/2009_Indonesian_Air_Force_L-100_crash\" title=\"2009 Indonesian Air Force L-100 crash\">crashes</a> in <a href=\"https://wikipedia.org/wiki/Magetan_Regency\" title=\"Magetan Regency\">Magetan Regency</a>, killing 99.", "links": [{"title": "Indonesian Air Force", "link": "https://wikipedia.org/wiki/Indonesian_Air_Force"}, {"title": "Lockheed L-100 Hercules", "link": "https://wikipedia.org/wiki/Lockheed_L-100_Hercules"}, {"title": "2009 Indonesian Air Force L-100 crash", "link": "https://wikipedia.org/wiki/2009_Indonesian_Air_Force_L-100_crash"}, {"title": "Magetan Regency", "link": "https://wikipedia.org/wiki/Magetan_Regency"}]}, {"year": "2011", "text": "<PERSON><PERSON> is sworn in as the Chief Minister of West Bengal, the first woman to hold this post.", "html": "2011 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a> is sworn in as the <a href=\"https://wikipedia.org/wiki/Chief_Minister_of_West_Bengal\" class=\"mw-redirect\" title=\"Chief Minister of West Bengal\">Chief Minister of West Bengal</a>, the first woman to hold this post.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a> is sworn in as the <a href=\"https://wikipedia.org/wiki/Chief_Minister_of_West_Bengal\" class=\"mw-redirect\" title=\"Chief Minister of West Bengal\">Chief Minister of West Bengal</a>, the first woman to hold this post.", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}, {"title": "Chief Minister of West Bengal", "link": "https://wikipedia.org/wiki/Chief_Minister_of_West_Bengal"}]}, {"year": "2012", "text": "At least 27 people are killed and 50 others injured when a 6.0-magnitude earthquake strikes northern Italy.", "html": "2012 - At least 27 people are killed and 50 others injured when a <a href=\"https://wikipedia.org/wiki/2012_Northern_Italy_earthquakes\" title=\"2012 Northern Italy earthquakes\">6.0-magnitude earthquake</a> strikes northern Italy.", "no_year_html": "At least 27 people are killed and 50 others injured when a <a href=\"https://wikipedia.org/wiki/2012_Northern_Italy_earthquakes\" title=\"2012 Northern Italy earthquakes\">6.0-magnitude earthquake</a> strikes northern Italy.", "links": [{"title": "2012 Northern Italy earthquakes", "link": "https://wikipedia.org/wiki/2012_Northern_Italy_earthquakes"}]}, {"year": "2013", "text": "An EF5 tornado strikes the Oklahoma City suburb of Moore, killing 24 people and injuring 377 others.", "html": "2013 - An <a href=\"https://wikipedia.org/wiki/Enhanced_Fujita_scale\" title=\"Enhanced Fujita scale\">EF5</a> <a href=\"https://wikipedia.org/wiki/2013_<PERSON>_tornado\" title=\"2013 <PERSON> tornado\">tornado</a> strikes the <a href=\"https://wikipedia.org/wiki/Oklahoma_City\" title=\"Oklahoma City\">Oklahoma City</a> suburb of <a href=\"https://wikipedia.org/wiki/<PERSON>,_Oklahoma\" title=\"Moore, Oklahoma\"><PERSON></a>, killing 24 people and injuring 377 others.", "no_year_html": "An <a href=\"https://wikipedia.org/wiki/Enhanced_Fujita_scale\" title=\"Enhanced Fujita scale\">EF5</a> <a href=\"https://wikipedia.org/wiki/2013_<PERSON>_tornado\" title=\"2013 <PERSON> tornado\">tornado</a> strikes the <a href=\"https://wikipedia.org/wiki/Oklahoma_City\" title=\"Oklahoma City\">Oklahoma City</a> suburb of <a href=\"https://wikipedia.org/wiki/Moore,_Oklahoma\" title=\"Moore, Oklahoma\"><PERSON></a>, killing 24 people and injuring 377 others.", "links": [{"title": "Enhanced Fujita scale", "link": "https://wikipedia.org/wiki/Enhanced_Fujita_scale"}, {"title": "2013 Moore tornado", "link": "https://wikipedia.org/wiki/2013_<PERSON>_tornado"}, {"title": "Oklahoma City", "link": "https://wikipedia.org/wiki/Oklahoma_City"}, {"title": "Moore, Oklahoma", "link": "https://wikipedia.org/wiki/Moore,_Oklahoma"}]}, {"year": "2016", "text": "The government of Singapore authorised the controversial execution of convicted murderer <PERSON><PERSON> for the murder of a Chinese construction worker despite the international pleas for clemency, notably from Amnesty International and the United Nations.", "html": "2016 - The government of <a href=\"https://wikipedia.org/wiki/Singapore\" title=\"Singapore\">Singapore</a> authorised the controversial <a href=\"https://wikipedia.org/wiki/Capital_punishment\" title=\"Capital punishment\">execution</a> of convicted murderer <a href=\"https://wikipedia.org/wiki/Kho_Jabing\" title=\"Kho Jabing\"><PERSON><PERSON></a> for the murder of a Chinese construction worker despite the international pleas for clemency, notably from <a href=\"https://wikipedia.org/wiki/Amnesty_International\" title=\"Amnesty International\">Amnesty International</a> and the <a href=\"https://wikipedia.org/wiki/United_Nations\" title=\"United Nations\">United Nations</a>.", "no_year_html": "The government of <a href=\"https://wikipedia.org/wiki/Singapore\" title=\"Singapore\">Singapore</a> authorised the controversial <a href=\"https://wikipedia.org/wiki/Capital_punishment\" title=\"Capital punishment\">execution</a> of convicted murderer <a href=\"https://wikipedia.org/wiki/Kho_Jabing\" title=\"Kho Jabing\"><PERSON><PERSON></a> for the murder of a Chinese construction worker despite the international pleas for clemency, notably from <a href=\"https://wikipedia.org/wiki/Amnesty_International\" title=\"Amnesty International\">Amnesty International</a> and the <a href=\"https://wikipedia.org/wiki/United_Nations\" title=\"United Nations\">United Nations</a>.", "links": [{"title": "Singapore", "link": "https://wikipedia.org/wiki/Singapore"}, {"title": "Capital punishment", "link": "https://wikipedia.org/wiki/Capital_punishment"}, {"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Kho_Jabing"}, {"title": "Amnesty International", "link": "https://wikipedia.org/wiki/Amnesty_International"}, {"title": "United Nations", "link": "https://wikipedia.org/wiki/United_Nations"}]}, {"year": "2019", "text": "The International System of Units (SI): The base units are redefined, making the international prototype of the kilogram obsolete.", "html": "2019 - The <a href=\"https://wikipedia.org/wiki/International_System_of_Units\" title=\"International System of Units\">International System of Units</a> (SI): The base units are redefined, making the <a href=\"https://wikipedia.org/wiki/International_Prototype_of_the_Kilogram\" title=\"International Prototype of the Kilogram\">international prototype of the kilogram</a> obsolete.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/International_System_of_Units\" title=\"International System of Units\">International System of Units</a> (SI): The base units are redefined, making the <a href=\"https://wikipedia.org/wiki/International_Prototype_of_the_Kilogram\" title=\"International Prototype of the Kilogram\">international prototype of the kilogram</a> obsolete.", "links": [{"title": "International System of Units", "link": "https://wikipedia.org/wiki/International_System_of_Units"}, {"title": "International Prototype of the Kilogram", "link": "https://wikipedia.org/wiki/International_Prototype_of_the_Kilogram"}]}, {"year": "2022", "text": "Russo-Ukrainian War: Russia claims full control of the Ukrainian city of Mariupol after a nearly three-month siege.", "html": "2022 - <a href=\"https://wikipedia.org/wiki/Russo-Ukrainian_War\" title=\"Russo-Ukrainian War\">Russo-Ukrainian War</a>: Russia claims full control of the Ukrainian city of <a href=\"https://wikipedia.org/wiki/Mariupol\" title=\"Mariupol\">Mariupol</a> after a nearly <a href=\"https://wikipedia.org/wiki/Siege_of_Mariupol\" title=\"Siege of Mariupol\">three-month siege</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Russo-Ukrainian_War\" title=\"Russo-Ukrainian War\">Russo-Ukrainian War</a>: Russia claims full control of the Ukrainian city of <a href=\"https://wikipedia.org/wiki/Mariupol\" title=\"Mariupol\">Mariupol</a> after a nearly <a href=\"https://wikipedia.org/wiki/Siege_of_Mariupol\" title=\"Siege of Mariupol\">three-month siege</a>.", "links": [{"title": "Russo-Ukrainian War", "link": "https://wikipedia.org/wiki/Russo-Ukrainian_War"}, {"title": "Mariupol", "link": "https://wikipedia.org/wiki/Mariupol"}, {"title": "Siege of Mariupol", "link": "https://wikipedia.org/wiki/Siege_of_Mariupol"}]}], "Births": [{"year": "1315", "text": "<PERSON><PERSON> of Luxembourg, first wife of <PERSON> of France (d. 1349)", "html": "1315 - <a href=\"https://wikipedia.org/wiki/Bonne_of_Luxembourg\" title=\"Bonne of Luxembourg\"><PERSON><PERSON> of Luxembourg</a>, first wife of <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_France\" title=\"<PERSON> II of France\"><PERSON> of France</a> (d. 1349)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Bonne_of_Luxembourg\" title=\"<PERSON><PERSON> of Luxembourg\"><PERSON><PERSON> of Luxembourg</a>, first wife of <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_France\" title=\"<PERSON> II of France\"><PERSON> of France</a> (d. 1349)", "links": [{"title": "Bonne of Luxembourg", "link": "https://wikipedia.org/wiki/Bonne_of_Luxembourg"}, {"title": "<PERSON> of France", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_France"}]}, {"year": "1470", "text": "<PERSON>, Italian cardinal, poet, and scholar (d. 1547)", "html": "1470 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian cardinal, poet, and scholar (d. 1547)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian cardinal, poet, and scholar (d. 1547)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1505", "text": "<PERSON><PERSON>, Dutch writer (d. 1568)", "html": "1505 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Dutch writer (d. 1568)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Dutch writer (d. 1568)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1531", "text": "<PERSON><PERSON><PERSON> of Ava, Viceroy of Ava (d. 1584)", "html": "1531 - <a href=\"https://wikipedia.org/wiki/Thado_Minsaw_of_Ava\" title=\"Thad<PERSON> Minsaw of Ava\"><PERSON><PERSON><PERSON> of Ava</a>, Viceroy of Ava (d. 1584)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Thad<PERSON>_Minsaw_of_Ava\" title=\"Thad<PERSON> Minsaw of Ava\">T<PERSON><PERSON> of Ava</a>, Viceroy of Ava (d. 1584)", "links": [{"title": "<PERSON><PERSON><PERSON> of Ava", "link": "https://wikipedia.org/wiki/Thado_Minsaw_of_Ava"}]}, {"year": "1537", "text": "<PERSON><PERSON><PERSON><PERSON>, Italian anatomist (d. 1619)", "html": "1537 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Italian anatomist (d. 1619)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Italian anatomist (d. 1619)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1575", "text": "<PERSON>, English judge and politician (d. 1649)", "html": "1575 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English judge and politician (d. 1649)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English judge and politician (d. 1649)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1664", "text": "<PERSON>, German sculptor and architect (d. 1714)", "html": "1664 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%<PERSON>ter\" title=\"<PERSON>\"><PERSON></a>, German sculptor and architect (d. 1714)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%<PERSON>ter\" title=\"<PERSON>\"><PERSON></a>, German sculptor and architect (d. 1714)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>hl%C3%BCter"}]}, {"year": "1726", "text": "<PERSON>, English painter and academic (d. 1770)", "html": "1726 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English painter and academic (d. 1770)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English painter and academic (d. 1770)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Francis_<PERSON>"}]}, {"year": "1759", "text": "<PERSON>, Virgin Islander-American architect, designed the United States Capitol (d. 1828)", "html": "1759 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Virgin Islander-American architect, designed the <a href=\"https://wikipedia.org/wiki/United_States_Capitol\" title=\"United States Capitol\">United States Capitol</a> (d. 1828)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Virgin Islander-American architect, designed the <a href=\"https://wikipedia.org/wiki/United_States_Capitol\" title=\"United States Capitol\">United States Capitol</a> (d. 1828)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "United States Capitol", "link": "https://wikipedia.org/wiki/United_States_Capitol"}]}, {"year": "1769", "text": "<PERSON>, Greek admiral and politician (d. 1835)", "html": "1769 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, Greek admiral and politician (d. 1835)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, Greek admiral and politician (d. 1835)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1772", "text": "Sir <PERSON>, 2nd Baronet, English inventor and politician, developed Congreve rockets (d. 1828)", "html": "1772 - <a href=\"https://wikipedia.org/wiki/Sir_<PERSON>,_2nd_Baronet\" title=\"Sir <PERSON>, 2nd Baronet\">Sir <PERSON>, 2nd Baronet</a>, English inventor and politician, developed <a href=\"https://wikipedia.org/wiki/Congreve_rockets\" class=\"mw-redirect\" title=\"Congreve rockets\">Congreve rockets</a> (d. 1828)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Sir_<PERSON>,_2nd_Baronet\" title=\"Sir <PERSON>, 2nd Baronet\">Sir <PERSON>, 2nd Baronet</a>, English inventor and politician, developed <a href=\"https://wikipedia.org/wiki/Congreve_rockets\" class=\"mw-redirect\" title=\"Congreve rockets\">Congreve rockets</a> (d. 1828)", "links": [{"title": "Sir <PERSON>, 2nd Baronet", "link": "https://wikipedia.org/wiki/Sir_<PERSON>,_2nd_Baronet"}, {"title": "Congreve rockets", "link": "https://wikipedia.org/wiki/Congreve_rockets"}]}, {"year": "1776", "text": "<PERSON>, American-Canadian fur trader and explorer (d. 1862)", "html": "1776 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(explorer)\" title=\"<PERSON> (explorer)\"><PERSON></a>, American-Canadian fur trader and explorer (d. 1862)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>(explorer)\" title=\"<PERSON> (explorer)\"><PERSON></a>, American-Canadian fur trader and explorer (d. 1862)", "links": [{"title": "<PERSON> (explorer)", "link": "https://wikipedia.org/wiki/<PERSON>(explorer)"}]}, {"year": "1795", "text": "<PERSON>, Mexican soldier. President (1847-1848) (d. 1854)", "html": "1795 - <a href=\"https://wikipedia.org/wiki/Pedro_Mar%C3%ADa_de_Anaya\" title=\"<PERSON>\"><PERSON></a>, Mexican soldier. President (1847-1848) (d. 1854)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Pedro_Mar%C3%ADa_de_Anaya\" title=\"<PERSON>\"><PERSON></a>, Mexican soldier. President (1847-1848) (d. 1854)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Pedro_Mar%C3%AD<PERSON>_de_<PERSON>ya"}]}, {"year": "1799", "text": "<PERSON><PERSON>, French novelist and playwright (d. 1850)", "html": "1799 - <a href=\"https://wikipedia.org/wiki/Honor%C3%A9_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, French novelist and playwright (d. 1850)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Honor%C3%A9_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, French novelist and playwright (d. 1850)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Honor%C3%A9_de_<PERSON>"}]}, {"year": "1806", "text": "<PERSON>, English economist, civil servant, and philosopher (d. 1873)", "html": "1806 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"John <PERSON>\"><PERSON></a>, English economist, civil servant, and philosopher (d. 1873)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"John <PERSON>\"><PERSON></a>, English economist, civil servant, and philosopher (d. 1873)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1811", "text": "<PERSON>, English-New Zealand poet and politician, 4th Prime Minister of New Zealand (d. 1887)", "html": "1811 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English-New Zealand poet and politician, 4th <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_New_Zealand\" title=\"Prime Minister of New Zealand\">Prime Minister of New Zealand</a> (d. 1887)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English-New Zealand poet and politician, 4th <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_New_Zealand\" title=\"Prime Minister of New Zealand\">Prime Minister of New Zealand</a> (d. 1887)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Prime Minister of New Zealand", "link": "https://wikipedia.org/wiki/Prime_Minister_of_New_Zealand"}]}, {"year": "1818", "text": "<PERSON>, American businessman and politician, co-founded Wells Fargo and American Express (d. 1881)", "html": "1818 - <a href=\"https://wikipedia.org/wiki/William_<PERSON>\" title=\"William Fargo\"><PERSON></a>, American businessman and politician, co-founded <a href=\"https://wikipedia.org/wiki/Wells_Fargo\" title=\"Wells Fargo\">Wells Fargo</a> and <a href=\"https://wikipedia.org/wiki/American_Express\" title=\"American Express\">American Express</a> (d. 1881)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/William_Fargo\" title=\"William Fargo\"><PERSON></a>, American businessman and politician, co-founded <a href=\"https://wikipedia.org/wiki/Wells_Fargo\" title=\"Wells Fargo\">Wells Fargo</a> and <a href=\"https://wikipedia.org/wiki/American_Express\" title=\"American Express\">American Express</a> (d. 1881)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Wells Fargo", "link": "https://wikipedia.org/wiki/Wells_Fargo"}, {"title": "American Express", "link": "https://wikipedia.org/wiki/American_Express"}]}, {"year": "1822", "text": "<PERSON><PERSON><PERSON><PERSON><PERSON>, French economist and academic, Nobel Prize laureate (d. 1912)", "html": "1822 - <a href=\"https://wikipedia.org/wiki/Fr%C3%A9d%C3%A9ric_Passy\" title=\"Fréd<PERSON><PERSON> Passy\"><PERSON><PERSON><PERSON><PERSON><PERSON></a>, French economist and academic, <a href=\"https://wikipedia.org/wiki/Nobel_Peace_Prize\" title=\"Nobel Peace Prize\">Nobel Prize</a> laureate (d. 1912)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Fr%C3%A9d%C3%A9ric_Passy\" title=\"Fréd<PERSON><PERSON> Passy\"><PERSON><PERSON><PERSON><PERSON><PERSON></a>, French economist and academic, <a href=\"https://wikipedia.org/wiki/Nobel_Peace_Prize\" title=\"Nobel Peace Prize\">Nobel Prize</a> laureate (d. 1912)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Fr%C3%A9d%C3%A9ric_Passy"}, {"title": "Nobel Peace Prize", "link": "https://wikipedia.org/wiki/Nobel_Peace_Prize"}]}, {"year": "1824", "text": "<PERSON><PERSON><PERSON>, Confederate States Army general (d. 1890)", "html": "1824 - <a href=\"https://wikipedia.org/wiki/<PERSON>admus_<PERSON><PERSON>_<PERSON>\" title=\"Cadmus <PERSON>\"><PERSON><PERSON><PERSON></a>, <a href=\"https://wikipedia.org/wiki/Confederate_States_Army\" title=\"Confederate States Army\">Confederate States Army</a> general (d. 1890)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"Cadmus <PERSON>\"><PERSON><PERSON><PERSON></a>, <a href=\"https://wikipedia.org/wiki/Confederate_States_Army\" title=\"Confederate States Army\">Confederate States Army</a> general (d. 1890)", "links": [{"title": "Cadmus <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON><PERSON>_<PERSON>"}, {"title": "Confederate States Army", "link": "https://wikipedia.org/wiki/Confederate_States_Army"}]}, {"year": "1825", "text": "<PERSON><PERSON>, the first woman to be ordained as a mainstream Protestant minister in the U.S. (d. 1921)", "html": "1825 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, the first woman to be ordained as a mainstream Protestant minister in the U.S. (d. 1921)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, the first woman to be ordained as a mainstream Protestant minister in the U.S. (d. 1921)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1830", "text": "<PERSON>, French author (d. 1907)", "html": "1830 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French author (d. 1907)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French author (d. 1907)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1838", "text": "<PERSON>, French lawyer and politician, 65th Prime Minister of France (d. 1925)", "html": "1838 - <a href=\"https://wikipedia.org/wiki/Jules_<PERSON>%C3%A9line\" title=\"<PERSON>\"><PERSON></a>, French lawyer and politician, 65th <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_France\" title=\"Prime Minister of France\">Prime Minister of France</a> (d. 1925)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Jules_<PERSON>%C3%A9line\" title=\"<PERSON>\"><PERSON></a>, French lawyer and politician, 65th <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_France\" title=\"Prime Minister of France\">Prime Minister of France</a> (d. 1925)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Jules_M%C3%A9line"}, {"title": "Prime Minister of France", "link": "https://wikipedia.org/wiki/Prime_Minister_of_France"}]}, {"year": "1851", "text": "<PERSON><PERSON>, German-American inventor, invented the Gramophone record (d. 1929)", "html": "1851 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, German-American inventor, invented the <a href=\"https://wikipedia.org/wiki/Gramophone_record\" class=\"mw-redirect\" title=\"Gramophone record\">Gramophone record</a> (d. 1929)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, German-American inventor, invented the <a href=\"https://wikipedia.org/wiki/Gramophone_record\" class=\"mw-redirect\" title=\"Gramophone record\">Gramophone record</a> (d. 1929)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}, {"title": "Gramophone record", "link": "https://wikipedia.org/wiki/Gramophone_record"}]}, {"year": "1854", "text": "<PERSON>, Australian politician, 28th Premier of Victoria (d. 1937)", "html": "1854 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian politician, 28th <a href=\"https://wikipedia.org/wiki/Premier_of_Victoria\" title=\"Premier of Victoria\">Premier of Victoria</a> (d. 1937)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian politician, 28th <a href=\"https://wikipedia.org/wiki/Premier_of_Victoria\" title=\"Premier of Victoria\">Premier of Victoria</a> (d. 1937)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Premier of Victoria", "link": "https://wikipedia.org/wiki/Premier_of_Victoria"}]}, {"year": "1856", "text": "<PERSON><PERSON><PERSON>, French Neo-Impressionist painter (d. 1910)", "html": "1856 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, French Neo-Impressionist painter (d. 1910)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, French Neo-Impressionist painter (d. 1910)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1860", "text": "<PERSON>, German chemist, zymologist, and academic, Nobel Prize laureate (d. 1917)", "html": "1860 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German chemist, zymologist, and academic, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Chemistry\" title=\"Nobel Prize in Chemistry\">Nobel Prize</a> laureate (d. 1917)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German chemist, zymologist, and academic, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Chemistry\" title=\"Nobel Prize in Chemistry\">Nobel Prize</a> laureate (d. 1917)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Nobel Prize in Chemistry", "link": "https://wikipedia.org/wiki/Nobel_Prize_in_Chemistry"}]}, {"year": "1875", "text": "<PERSON><PERSON><PERSON>, Dutch rower (d. 1953)", "html": "1875 - <a href=\"https://wikipedia.org/wiki/He<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Dutch rower (d. 1953)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Dutch rower (d. 1953)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1877", "text": "<PERSON>, Irish-American jumper (d. 1927)", "html": "1877 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(athlete)\" title=\"<PERSON> (athlete)\"><PERSON></a>, Irish-American jumper (d. 1927)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(athlete)\" title=\"<PERSON> (athlete)\"><PERSON></a>, Irish-American jumper (d. 1927)", "links": [{"title": "<PERSON> (athlete)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(athlete)"}]}, {"year": "1879", "text": "<PERSON>, German chemist (d. 1965)", "html": "1879 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German chemist (d. 1965)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German chemist (d. 1965)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1882", "text": "<PERSON><PERSON><PERSON>, Danish-Norwegian novelist, essayist, and translator, Nobel Prize laureate (d. 1949)", "html": "1882 - <a href=\"https://wikipedia.org/wiki/Sigrid_Undset\" title=\"<PERSON>g<PERSON>\"><PERSON><PERSON><PERSON></a>, Danish-Norwegian novelist, essayist, and translator, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Literature\" title=\"Nobel Prize in Literature\">Nobel Prize</a> laureate (d. 1949)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>g<PERSON>_<PERSON>\" title=\"<PERSON>g<PERSON>\"><PERSON><PERSON><PERSON></a>, Danish-Norwegian novelist, essayist, and translator, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Literature\" title=\"Nobel Prize in Literature\">Nobel Prize</a> laureate (d. 1949)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Sigrid_Undset"}, {"title": "Nobel Prize in Literature", "link": "https://wikipedia.org/wiki/Nobel_Prize_in_Literature"}]}, {"year": "1883", "text": "<PERSON><PERSON><PERSON> of Iraq (d. 1933)", "html": "1883 - <a href=\"https://wikipedia.org/wiki/Faisal_I_of_Iraq\" title=\"Faisal I of Iraq\">Faisal I of Iraq</a> (d. 1933)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Faisal_I_of_Iraq\" title=\"Faisal I of Iraq\"><PERSON>aisal I of Iraq</a> (d. 1933)", "links": [{"title": "<PERSON><PERSON><PERSON> I of Iraq", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_I_of_Iraq"}]}, {"year": "1886", "text": "<PERSON>, Turkish footballer and manager, founded the Galatasaray Sports Club (d. 1951)", "html": "1886 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Turkish footballer and manager, founded the <a href=\"https://wikipedia.org/wiki/Galatasaray_S.K.\" title=\"Galatasaray S.K.\">Galatasaray Sports Club</a> (d. 1951)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Turkish footballer and manager, founded the <a href=\"https://wikipedia.org/wiki/Galatasaray_S.K.\" title=\"Galatasaray S.K.\">Galatasaray Sports Club</a> (d. 1951)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>n"}, {"title": "Galatasaray S.K.", "link": "https://wikipedia.org/wiki/Galatasaray_S.K."}]}, {"year": "1894", "text": "<PERSON><PERSON><PERSON><PERSON>, Indian guru and scholar (d. 1994)", "html": "1894 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Indian guru and scholar (d. 1994)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Indian guru and scholar (d. 1994)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1895", "text": "<PERSON><PERSON> <PERSON><PERSON>, English engineer, designed the Supermarine Spitfire and Supermarine S.6B (d. 1937)", "html": "1895 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> <PERSON><PERSON>\"><PERSON><PERSON> <PERSON><PERSON></a>, English engineer, designed the <a href=\"https://wikipedia.org/wiki/Supermarine_Spitfire\" title=\"Supermarine Spitfire\">Supermarine Spitfire</a> and <a href=\"https://wikipedia.org/wiki/Supermarine_S.6B\" title=\"Supermarine S.6B\">Supermarine S.6B</a> (d. 1937)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> <PERSON><PERSON>\"><PERSON><PERSON> <PERSON><PERSON></a>, English engineer, designed the <a href=\"https://wikipedia.org/wiki/Supermarine_Spitfire\" title=\"Supermarine Spitfire\">Supermarine Spitfire</a> and <a href=\"https://wikipedia.org/wiki/Supermarine_S.6B\" title=\"Supermarine S.6B\">Supermarine S.6B</a> (d. 1937)", "links": [{"title": "<PERSON><PERSON> <PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>"}, {"title": "Supermarine Spitfire", "link": "https://wikipedia.org/wiki/Supermarine_Spitfire"}, {"title": "Supermarine S.6B", "link": "https://wikipedia.org/wiki/Supermarine_S.6B"}]}, {"year": "1897", "text": "<PERSON>, Spanish economist and author (d. 1983)", "html": "1897 - <a href=\"https://wikipedia.org/wiki/<PERSON>_Abad_de_Santill%C3%A1n\" title=\"Diego Abad de Santillán\"><PERSON></a>, Spanish economist and author (d. 1983)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_Abad_de_Santill%C3%A1n\" title=\"Diego Abad de Santillán\"><PERSON>ill<PERSON></a>, Spanish economist and author (d. 1983)", "links": [{"title": "Diego <PERSON>ill<PERSON>", "link": "https://wikipedia.org/wiki/Diego_A<PERSON>_de_Santill%C3%A1n"}]}, {"year": "1897", "text": "<PERSON>, English hammer and discus thrower (d. 1986)", "html": "1897 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English hammer and discus thrower (d. 1986)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English hammer and discus thrower (d. 1986)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1898", "text": "<PERSON>, Estonian painter (d. 1995)", "html": "1898 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Estonian painter (d. 1995)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Estonian painter (d. 1995)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1899", "text": "<PERSON>, Russian painter and sculptor (d. 1969)", "html": "1899 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian painter and sculptor (d. 1969)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian painter and sculptor (d. 1969)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1899", "text": "<PERSON>, American lawyer and jurist (d. 1971)", "html": "1899 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and jurist (d. 1971)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and jurist (d. 1971)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1900", "text": "<PERSON><PERSON><PERSON><PERSON><PERSON>, Indian poet and author (d. 1977)", "html": "1900 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON><PERSON>_<PERSON>t\" title=\"<PERSON><PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON><PERSON></a>, Indian poet and author (d. 1977)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON><PERSON></a>, Indian poet and author (d. 1977)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>rana<PERSON><PERSON>_Pant"}]}, {"year": "1901", "text": "<PERSON>, Dutch chess player, mathematician, and author (d. 1981)", "html": "1901 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch chess player, mathematician, and author (d. 1981)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch chess player, mathematician, and author (d. 1981)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>we"}]}, {"year": "1901", "text": "<PERSON>, American journalist (d. 1970)", "html": "1901 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American journalist (d. 1970)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American journalist (d. 1970)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1904", "text": "<PERSON><PERSON><PERSON>, English author of detective fiction (d. 1966)", "html": "1904 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, English author of detective fiction (d. 1966)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, English author of detective fiction (d. 1966)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>ingham"}]}, {"year": "1906", "text": "<PERSON>, Italian cardinal (d. 1989)", "html": "1906 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian cardinal (d. 1989)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian cardinal (d. 1989)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1907", "text": "<PERSON>, American photographer and journalist (d. 2004)", "html": "1907 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American photographer and journalist (d. 2004)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American photographer and journalist (d. 2004)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1908", "text": "<PERSON>, Australian politician, 38th Premier of Victoria (d. 1990)", "html": "1908 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian politician, 38th <a href=\"https://wikipedia.org/wiki/Premier_of_Victoria\" title=\"Premier of Victoria\">Premier of Victoria</a> (d. 1990)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian politician, 38th <a href=\"https://wikipedia.org/wiki/Premier_of_Victoria\" title=\"Premier of Victoria\">Premier of Victoria</a> (d. 1990)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Premier of Victoria", "link": "https://wikipedia.org/wiki/Premier_of_Victoria"}]}, {"year": "1908", "text": "<PERSON>, French actor and director (d. 1980)", "html": "1908 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French actor and director (d. 1980)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French actor and director (d. 1980)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1908", "text": "<PERSON>, American botanist and author (d. 1993)", "html": "1908 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American botanist and author (d. 1993)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American botanist and author (d. 1993)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1908", "text": "<PERSON>, American actor (d. 1997)", "html": "1908 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor (d. 1997)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor (d. 1997)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1911", "text": "<PERSON>, American author (d. 1986)", "html": "1911 - <a href=\"https://wikipedia.org/wiki/<PERSON>_Fox\" title=\"Gardner Fox\"><PERSON></a>, American author (d. 1986)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Gardner_Fox\" title=\"Gardner Fox\"><PERSON></a>, American author (d. 1986)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_Fox"}]}, {"year": "1911", "text": "<PERSON>, Dutch author and playwright (d. 1995)", "html": "1911 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, Dutch author and playwright (d. 1995)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, Dutch author and playwright (d. 1995)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "1913", "text": "<PERSON><PERSON><PERSON>, Peruvian footballer (d. 1996)", "html": "1913 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>oro_Fern%C3%A1ndez\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Peruvian footballer (d. 1996)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_Fern%C3%A1ndez\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Peruvian footballer (d. 1996)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Teodoro_Fern%C3%A1ndez"}]}, {"year": "1913", "text": "<PERSON>, American engineer, co-founded Hewlett-Packard (d. 2001)", "html": "1913 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON> Hewlett\"><PERSON></a>, American engineer, co-founded <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>-<PERSON>\" title=\"He<PERSON><PERSON>-Packard\"><PERSON><PERSON><PERSON>-<PERSON></a> (d. 2001)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON> Hewlett\"><PERSON></a>, American engineer, co-founded <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>-<PERSON>\" title=\"He<PERSON>ett-Packard\"><PERSON><PERSON><PERSON>-<PERSON></a> (d. 2001)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "Hewlett-Packard", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>ett-<PERSON>"}]}, {"year": "1913", "text": "<PERSON>, Argentine Archaeologist (d. 2002)", "html": "1913 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Argentine Archaeologist (d. 2002)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Argentine Archaeologist (d. 2002)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1915", "text": "<PERSON>, English actor (d. 2008)", "html": "1915 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actor (d. 2008)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actor (d. 2008)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1915", "text": "<PERSON><PERSON>, Israeli general and politician, 5th Israeli Minister of Foreign Affairs (d. 1981)", "html": "1915 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Israeli general and politician, 5th <a href=\"https://wikipedia.org/wiki/Ministry_of_Foreign_Affairs_(Israel)\" title=\"Ministry of Foreign Affairs (Israel)\">Israeli Minister of Foreign Affairs</a> (d. 1981)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Israeli general and politician, 5th <a href=\"https://wikipedia.org/wiki/Ministry_of_Foreign_Affairs_(Israel)\" title=\"Ministry of Foreign Affairs (Israel)\">Israeli Minister of Foreign Affairs</a> (d. 1981)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}, {"title": "Ministry of Foreign Affairs (Israel)", "link": "https://wikipedia.org/wiki/Ministry_of_Foreign_Affairs_(Israel)"}]}, {"year": "1915", "text": "<PERSON><PERSON>, Australian comedian and actor (d. 1999)", "html": "1915 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> <PERSON>\"><PERSON><PERSON></a>, Australian comedian and actor (d. 1999)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> <PERSON>\"><PERSON><PERSON></a>, Australian comedian and actor (d. 1999)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1916", "text": "<PERSON>, English rugby player, historian, and academic (d. 2015)", "html": "1916 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English rugby player, historian, and academic (d. 2015)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English rugby player, historian, and academic (d. 2015)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1916", "text": "<PERSON><PERSON>, Russian soldier and pilot (d. 2001)", "html": "1916 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Russian soldier and pilot (d. 2001)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Russian soldier and pilot (d. 2001)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1916", "text": "<PERSON><PERSON>, Italian sprinter and hurdler (d. 2006)", "html": "1916 - <a href=\"https://wikipedia.org/wiki/Ondina_Valla\" title=\"Ondina Valla\"><PERSON><PERSON></a>, Italian sprinter and hurdler (d. 2006)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Ondina_Valla\" title=\"Ondina Valla\"><PERSON><PERSON>la</a>, Italian sprinter and hurdler (d. 2006)", "links": [{"title": "Ondina Valla", "link": "https://wikipedia.org/wiki/Ondina_Valla"}]}, {"year": "1917", "text": "<PERSON>, Israeli-English author and activist (d. 2000)", "html": "1917 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Cliff\"><PERSON></a>, Israeli-English author and activist (d. 2000)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Tony Cliff\"><PERSON></a>, Israeli-English author and activist (d. 2000)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1917", "text": "<PERSON>, Canadian lawyer, judge, and politician, 28th Canadian Minister of Justice (d. 1967)", "html": "1917 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian lawyer, judge, and politician, 28th <a href=\"https://wikipedia.org/wiki/Minister_of_Justice_(Canada)\" class=\"mw-redirect\" title=\"Minister of Justice (Canada)\">Canadian Minister of Justice</a> (d. 1967)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian lawyer, judge, and politician, 28th <a href=\"https://wikipedia.org/wiki/Minister_of_Justice_(Canada)\" class=\"mw-redirect\" title=\"Minister of Justice (Canada)\">Canadian Minister of Justice</a> (d. 1967)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Minister of Justice (Canada)", "link": "https://wikipedia.org/wiki/Minister_of_Justice_(Canada)"}]}, {"year": "1918", "text": "<PERSON>, Russian tank commander (d. 1996)", "html": "1918 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, Russian tank commander (d. 1996)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, Russian tank commander (d. 1996)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1918", "text": "<PERSON>, American biologist, geneticist, and academic, Nobel Prize laureate (d. 2004)", "html": "1918 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American biologist, geneticist, and academic, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Physiology_or_Medicine\" title=\"Nobel Prize in Physiology or Medicine\">Nobel Prize</a> laureate (d. 2004)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American biologist, geneticist, and academic, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Physiology_or_Medicine\" title=\"Nobel Prize in Physiology or Medicine\">Nobel Prize</a> laureate (d. 2004)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>"}, {"title": "Nobel Prize in Physiology or Medicine", "link": "https://wikipedia.org/wiki/Nobel_Prize_in_Physiology_or_Medicine"}]}, {"year": "1919", "text": "<PERSON>, American comedian (d. 1991)", "html": "1919 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American comedian (d. 1991)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American comedian (d. 1991)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1920", "text": "<PERSON>, Scottish lieutenant and banker, Victoria Cross recipient", "html": "1920 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish lieutenant and banker, <a href=\"https://wikipedia.org/wiki/Victoria_Cross\" title=\"Victoria Cross\">Victoria Cross</a> recipient", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish lieutenant and banker, <a href=\"https://wikipedia.org/wiki/Victoria_Cross\" title=\"Victoria Cross\"><PERSON> Cross</a> recipient", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}, {"title": "Victoria Cross", "link": "https://wikipedia.org/wiki/Victoria_Cross"}]}, {"year": "1921", "text": "<PERSON>, German author and playwright (d. 1947)", "html": "1921 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German author and playwright (d. 1947)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German author and playwright (d. 1947)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1921", "text": "<PERSON>, American baseball player and scout (d. 1998)", "html": "1921 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player and scout (d. 1998)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player and scout (d. 1998)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>r"}]}, {"year": "1921", "text": "<PERSON><PERSON>, Chinese-American logician, philosopher, and mathematician (d. 1995)", "html": "1921 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_(academic)\" title=\"<PERSON><PERSON> (academic)\"><PERSON><PERSON></a>, Chinese-American logician, philosopher, and mathematician (d. 1995)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_(academic)\" title=\"<PERSON><PERSON> (academic)\"><PERSON><PERSON></a>, Chinese-American logician, philosopher, and mathematician (d. 1995)", "links": [{"title": "<PERSON><PERSON> (academic)", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_(academic)"}]}, {"year": "1922", "text": "<PERSON>, Northern Irish international footballer (d. 1988)", "html": "1922 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>(footballer)\" title=\"<PERSON> (footballer)\"><PERSON></a>, Northern Irish international footballer (d. 1988)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(footballer)\" title=\"<PERSON> (footballer)\"><PERSON></a>, Northern Irish international footballer (d. 1988)", "links": [{"title": "<PERSON> (footballer)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(footballer)"}]}, {"year": "1923", "text": "<PERSON>, American actress (d. 2011)", "html": "1923 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress (d. 2011)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress (d. 2011)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1923", "text": "<PERSON>, Trinidad-born writer (d. 1994)", "html": "1923 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Trinidad-born writer (d. 1994)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Trinidad-born writer (d. 1994)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1924", "text": "<PERSON>, English-American CIA officer and author (d. 2014)", "html": "1924 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English-American <a href=\"https://wikipedia.org/wiki/CIA\" class=\"mw-redirect\" title=\"CIA\">CIA</a> officer and author (d. 2014)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English-American <a href=\"https://wikipedia.org/wiki/CIA\" class=\"mw-redirect\" title=\"CIA\">CIA</a> officer and author (d. 2014)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}, {"title": "CIA", "link": "https://wikipedia.org/wiki/CIA"}]}, {"year": "1924", "text": "<PERSON><PERSON><PERSON>, Uruguayan journalist and politician (d. 1976)", "html": "1924 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Uruguayan journalist and politician (d. 1976)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Uruguayan journalist and politician (d. 1976)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1925", "text": "<PERSON>, Russian engineer, designed the Tupolev Tu-144 (d. 2001)", "html": "1925 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, Russian engineer, designed the <a href=\"https://wikipedia.org/wiki/Tupolev_Tu-144\" title=\"Tupolev Tu-144\">Tupolev Tu-144</a> (d. 2001)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, Russian engineer, designed the <a href=\"https://wikipedia.org/wiki/Tupolev_Tu-144\" title=\"Tupolev Tu-144\">Tupolev Tu-144</a> (d. 2001)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Tupolev Tu-144", "link": "https://wikipedia.org/wiki/Tupolev_Tu-144"}]}, {"year": "1926", "text": "<PERSON>, American race car driver (d. 1956)", "html": "1926 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American race car driver (d. 1956)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American race car driver (d. 1956)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1927", "text": "<PERSON>, American football player and coach (d. 2023)", "html": "1927 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player and coach (d. 2023)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player and coach (d. 2023)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1927", "text": "<PERSON>, American actor (d. 2019)", "html": "1927 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor (d. 2019)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor (d. 2019)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1927", "text": "<PERSON><PERSON><PERSON>, Polish cardinal (d. 2016)", "html": "1927 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Polish cardinal (d. 2016)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Polish cardinal (d. 2016)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Franciszek_<PERSON>ski"}]}, {"year": "1929", "text": "<PERSON>, German-born Venezuelan zoologist (d. 2021)", "html": "1929 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German-born Venezuelan zoologist (d. 2021)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German-born Venezuelan zoologist (d. 2021)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1929", "text": "<PERSON>, Canadian politician and diplomat, 33rd Canadian Minister of Finance (d. 2022)", "html": "1929 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian politician and diplomat, 33rd <a href=\"https://wikipedia.org/wiki/Minister_of_Finance_(Canada)\" title=\"Minister of Finance (Canada)\">Canadian Minister of Finance</a> (d. 2022)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian politician and diplomat, 33rd <a href=\"https://wikipedia.org/wiki/Minister_of_Finance_(Canada)\" title=\"Minister of Finance (Canada)\">Canadian Minister of Finance</a> (d. 2022)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}, {"title": "Minister of Finance (Canada)", "link": "https://wikipedia.org/wiki/Minister_of_Finance_(Canada)"}]}, {"year": "1930", "text": "<PERSON>, American football player and coach (d. 2009)", "html": "1930 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player and coach (d. 2009)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player and coach (d. 2009)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1931", "text": "<PERSON>, American baseball player and manager (d. 1982)", "html": "1931 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player and manager (d. 1982)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player and manager (d. 1982)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1931", "text": "<PERSON>, American trumpeter (d. 2016)", "html": "1931 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(musician)\" title=\"<PERSON> (musician)\"><PERSON></a>, American trumpeter (d. 2016)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(musician)\" title=\"<PERSON> (musician)\"><PERSON></a>, American trumpeter (d. 2016)", "links": [{"title": "<PERSON> (musician)", "link": "https://wikipedia.org/wiki/<PERSON>_(musician)"}]}, {"year": "1933", "text": "<PERSON>, American actress and singer", "html": "1933 - <a href=\"https://wikipedia.org/wiki/Constance_Towers\" title=\"Constance Towers\"><PERSON></a>, American actress and singer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Constance_Towers\" title=\"Constance Towers\"><PERSON></a>, American actress and singer", "links": [{"title": "Constance Towers", "link": "https://wikipedia.org/wiki/Constance_Towers"}]}, {"year": "1935", "text": "<PERSON>, Uruguayan guerrilla leader and politician, 40th President of Uruguay", "html": "1935 - <a href=\"https://wikipedia.org/wiki/Jos%C3%A9_Mujica\" title=\"<PERSON>\"><PERSON></a>, Uruguayan <a href=\"https://wikipedia.org/wiki/Guerrilla\" class=\"mw-redirect\" title=\"Guerrilla\">guerrilla</a> leader and politician, 40th <a href=\"https://wikipedia.org/wiki/List_of_Presidents_of_Uruguay\" class=\"mw-redirect\" title=\"List of Presidents of Uruguay\">President of Uruguay</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Jos%C3%A9_Mujica\" title=\"<PERSON>\"><PERSON></a>, Uruguayan <a href=\"https://wikipedia.org/wiki/Guerrilla\" class=\"mw-redirect\" title=\"Guerrilla\">guerrilla</a> leader and politician, 40th <a href=\"https://wikipedia.org/wiki/List_of_Presidents_of_Uruguay\" class=\"mw-redirect\" title=\"List of Presidents of Uruguay\">President of Uruguay</a>", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Jos%C3%A9_Mujica"}, {"title": "Guerrilla", "link": "https://wikipedia.org/wiki/Guerrilla"}, {"title": "List of Presidents of Uruguay", "link": "https://wikipedia.org/wiki/List_of_Presidents_of_Uruguay"}]}, {"year": "1936", "text": "<PERSON>, American actor", "html": "1936 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1937", "text": "<PERSON>, American golfer (d. 2011)", "html": "1937 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(golfer)\" title=\"<PERSON> (golfer)\"><PERSON></a>, American golfer (d. 2011)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(golfer)\" title=\"<PERSON> (golfer)\"><PERSON></a>, American golfer (d. 2011)", "links": [{"title": "<PERSON> (golfer)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(golfer)"}]}, {"year": "1937", "text": "<PERSON>, English footballer", "html": "1937 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1939", "text": "<PERSON><PERSON>, Sri Lankan-Indian director, cinematographer, and screenwriter (d. 2014)", "html": "1939 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Sri Lankan-Indian director, cinematographer, and screenwriter (d. 2014)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Sri Lankan-Indian director, cinematographer, and screenwriter (d. 2014)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON><PERSON>"}]}, {"year": "1940", "text": "<PERSON><PERSON>, American singer-songwriter and producer (d. 1969)", "html": "1940 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American singer-songwriter and producer (d. 1969)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American singer-songwriter and producer (d. 1969)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1940", "text": "<PERSON>, Slovak-Canadian ice hockey player and sportscaster (d. 2018)", "html": "1940 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Slovak-Canadian ice hockey player and sportscaster (d. 2018)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Slovak-Canadian ice hockey player and sportscaster (d. 2018)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1940", "text": "<PERSON><PERSON><PERSON>, Japanese-Taiwanese baseball player and manager", "html": "1940 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese-Taiwanese baseball player and manager", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese-Taiwanese baseball player and manager", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1941", "text": "<PERSON><PERSON>, Singaporean politician, 2nd Prime Minister of Singapore", "html": "1941 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Singaporean politician, 2nd <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Singapore\" title=\"Prime Minister of Singapore\">Prime Minister of Singapore</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Singaporean politician, 2nd <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Singapore\" title=\"Prime Minister of Singapore\">Prime Minister of Singapore</a>", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>"}, {"title": "Prime Minister of Singapore", "link": "https://wikipedia.org/wiki/Prime_Minister_of_Singapore"}]}, {"year": "1941", "text": "<PERSON>, American actor and teacher", "html": "1941 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor and teacher", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor and teacher", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1942", "text": "<PERSON>, Canadian lawyer and diplomat, Canadian Ambassador to the United States", "html": "1942 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A9tien\" title=\"<PERSON>\"><PERSON></a>, Canadian lawyer and diplomat, <a href=\"https://wikipedia.org/wiki/List_of_Canadian_ambassadors_to_the_United_States\" class=\"mw-redirect\" title=\"List of Canadian ambassadors to the United States\">Canadian Ambassador to the United States</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A9tien\" title=\"<PERSON>\"><PERSON></a>, Canadian lawyer and diplomat, <a href=\"https://wikipedia.org/wiki/List_of_Canadian_ambassadors_to_the_United_States\" class=\"mw-redirect\" title=\"List of Canadian ambassadors to the United States\">Canadian Ambassador to the United States</a>", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Raymond_Chr%C3%A9tien"}, {"title": "List of Canadian ambassadors to the United States", "link": "https://wikipedia.org/wiki/List_of_Canadian_ambassadors_to_the_United_States"}]}, {"year": "1942", "text": "<PERSON>, Welsh sprinter and long jumper", "html": "1942 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Welsh sprinter and long jumper", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Welsh sprinter and long jumper", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1942", "text": "<PERSON>, American sergeant and sniper (d. 1999)", "html": "1942 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American sergeant and sniper (d. 1999)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American sergeant and sniper (d. 1999)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1942", "text": "<PERSON><PERSON>, South African tennis player", "html": "1942 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, South African tennis player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>Millan\"><PERSON><PERSON></a>, South African tennis player", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Frew_<PERSON>"}]}, {"year": "1943", "text": "<PERSON><PERSON>, Italian singer, actor, and winemaker", "html": "1943 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Italian singer, actor, and winemaker", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Albano_Carrisi\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Italian singer, actor, and winemaker", "links": [{"title": "Albano <PERSON>", "link": "https://wikipedia.org/wiki/Albano_Carrisi"}]}, {"year": "1943", "text": "<PERSON><PERSON><PERSON>, Trinidadian cricketer", "html": "1943 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Trinidadian cricketer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Trinidadian cricketer", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1944", "text": "<PERSON>, English singer-songwriter (d. 2014)", "html": "1944 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Co<PERSON>\"><PERSON></a>, English singer-songwriter (d. 2014)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Cocker\"><PERSON></a>, English singer-songwriter (d. 2014)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1944", "text": "<PERSON><PERSON><PERSON><PERSON>, Indonesian-Dutch singer-songwriter and guitarist", "html": "1944 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>root\"><PERSON><PERSON><PERSON><PERSON></a>, Indonesian-Dutch singer-songwriter and guitarist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>_<PERSON>root\" title=\"<PERSON><PERSON><PERSON><PERSON> Groot\"><PERSON><PERSON><PERSON><PERSON></a>, Indonesian-Dutch singer-songwriter and guitarist", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_de_<PERSON>root"}]}, {"year": "1944", "text": "<PERSON>, English cricketer and manager", "html": "1944 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English cricketer and manager", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English cricketer and manager", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1944", "text": "<PERSON>, Austrian businessman, co-founder of Red Bull GmbH (d. 2022)", "html": "1944 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Austrian businessman, co-founder of <a href=\"https://wikipedia.org/wiki/Red_Bull_GmbH\" title=\"Red Bull GmbH\">Red Bull GmbH</a> (d. 2022)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Austrian businessman, co-founder of <a href=\"https://wikipedia.org/wiki/Red_Bull_GmbH\" title=\"Red Bull GmbH\">Red Bull GmbH</a> (d. 2022)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Red Bull GmbH", "link": "https://wikipedia.org/wiki/Red_Bull_GmbH"}]}, {"year": "1945", "text": "<PERSON><PERSON>, Peruvian intelligence officer", "html": "1945 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Peruvian intelligence officer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Peruvian intelligence officer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1946", "text": "<PERSON><PERSON>, American singer-songwriter, producer, and actress", "html": "1946 - <a href=\"https://wikipedia.org/wiki/Cher\" title=\"Cher\"><PERSON>er</a>, American singer-songwriter, producer, and actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Cher\" title=\"Cher\"><PERSON>er</a>, American singer-songwriter, producer, and actress", "links": [{"title": "Cher", "link": "https://wikipedia.org/wiki/Cher"}]}, {"year": "1946", "text": "<PERSON>, American baseball player, coach, manager, and sportscaster (d. 2008)", "html": "1946 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player, coach, manager, and sportscaster (d. 2008)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player, coach, manager, and sportscaster (d. 2008)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1947", "text": "<PERSON>, English bass player (d. 1981)", "html": "1947 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, English bass player (d. 1981)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, English bass player (d. 1981)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1947", "text": "<PERSON>, English journalist and academic", "html": "1947 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English journalist and academic", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English journalist and academic", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1949", "text": "<PERSON>, Canadian director, cinematographer, and screenwriter", "html": "1949 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian director, cinematographer, and screenwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian director, cinematographer, and screenwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1949", "text": "<PERSON><PERSON><PERSON><PERSON>, English author and poet", "html": "1949 - <a href=\"https://wikipedia.org/wiki/Mich%C3%A<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, English author and poet", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Mich%C3%A<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, English author and poet", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Mich%C3%A8le_<PERSON>"}]}, {"year": "1949", "text": "<PERSON>, Canadian actor, director, producer, and screenwriter", "html": "1949 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(actor)\" title=\"<PERSON> (actor)\"><PERSON></a>, Canadian actor, director, producer, and screenwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>(actor)\" title=\"<PERSON> (actor)\"><PERSON></a>, Canadian actor, director, producer, and screenwriter", "links": [{"title": "<PERSON> (actor)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>(actor)"}]}, {"year": "1950", "text": "<PERSON>, English-American engineer and producer (d. 2013)", "html": "1950 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English-American engineer and producer (d. 2013)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English-American engineer and producer (d. 2013)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1950", "text": "<PERSON><PERSON><PERSON>, Argentinian footballer and coach", "html": "1950 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_Merlo\" title=\"Re<PERSON>do Merlo\"><PERSON><PERSON><PERSON></a>, Argentinian footballer and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_Merlo\" title=\"<PERSON><PERSON>do Merlo\"><PERSON><PERSON><PERSON></a>, Argentinian footballer and coach", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Reinal<PERSON>_Merlo"}]}, {"year": "1950", "text": "<PERSON>, English organist (d. 2020)", "html": "1950 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English organist (d. 2020)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English organist (d. 2020)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1951", "text": "<PERSON>, American colonel, engineer, and astronaut", "html": "1951 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American colonel, engineer, and astronaut", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American colonel, engineer, and astronaut", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1951", "text": "<PERSON>, Canadian newspaper columnist, journalist and broadcaster (d. 2020)", "html": "1951 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian newspaper columnist, journalist and broadcaster (d. 2020)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian newspaper columnist, journalist and broadcaster (d. 2020)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1951", "text": "<PERSON>, American lawyer and politician", "html": "1951 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and politician", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1952", "text": "<PERSON>, Cameroonian footballer and manager", "html": "1952 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Cameroonian footballer and manager", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Cameroonian footballer and manager", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1952", "text": "<PERSON>, English politician, British Minister of Justice", "html": "1952 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English politician, <a href=\"https://wikipedia.org/wiki/Ministry_of_Justice_(United_Kingdom)\" title=\"Ministry of Justice (United Kingdom)\">British Minister of Justice</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English politician, <a href=\"https://wikipedia.org/wiki/Ministry_of_Justice_(United_Kingdom)\" title=\"Ministry of Justice (United Kingdom)\">British Minister of Justice</a>", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Ministry of Justice (United Kingdom)", "link": "https://wikipedia.org/wiki/Ministry_of_Justice_(United_Kingdom)"}]}, {"year": "1953", "text": "<PERSON>, Australian educator and politician, 103rd Lord Mayor of Melbourne", "html": "1953 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian educator and politician, 103rd <a href=\"https://wikipedia.org/wiki/List_of_mayors_and_lord_mayors_of_Melbourne\" title=\"List of mayors and lord mayors of Melbourne\">Lord Mayor of Melbourne</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian educator and politician, 103rd <a href=\"https://wikipedia.org/wiki/List_of_mayors_and_lord_mayors_of_Melbourne\" title=\"List of mayors and lord mayors of Melbourne\">Lord Mayor of Melbourne</a>", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "List of mayors and lord mayors of Melbourne", "link": "https://wikipedia.org/wiki/List_of_mayors_and_lord_mayors_of_Melbourne"}]}, {"year": "1954", "text": "<PERSON>, American lawyer and politician, 55th Governor of New York", "html": "1954 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and politician, 55th <a href=\"https://wikipedia.org/wiki/Governor_of_New_York\" title=\"Governor of New York\">Governor of New York</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and politician, 55th <a href=\"https://wikipedia.org/wiki/Governor_of_New_York\" title=\"Governor of New York\">Governor of New York</a>", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Governor of New York", "link": "https://wikipedia.org/wiki/Governor_of_New_York"}]}, {"year": "1954", "text": "<PERSON>, Lord <PERSON>, Scottish lawyer and judge", "html": "1954 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Lord_<PERSON>\" title=\"<PERSON>, Lord <PERSON>\"><PERSON>, Lord <PERSON></a>, Scottish lawyer and judge", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Lord_<PERSON>\" title=\"<PERSON>, Lord <PERSON>\"><PERSON>, Lord <PERSON></a>, Scottish lawyer and judge", "links": [{"title": "<PERSON>, <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_<PERSON>_<PERSON>"}]}, {"year": "1955", "text": "<PERSON>, American keyboard player and songwriter", "html": "1955 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(keyboardist)\" title=\"<PERSON> (keyboardist)\"><PERSON></a>, American keyboard player and songwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>(keyboardist)\" title=\"<PERSON> (keyboardist)\"><PERSON></a>, American keyboard player and songwriter", "links": [{"title": "<PERSON> (keyboardist)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>(keyboardist)"}]}, {"year": "1955", "text": "<PERSON><PERSON><PERSON><PERSON>, Polish composer and producer", "html": "1955 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Polish composer and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Polish composer and producer", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1956", "text": "<PERSON><PERSON><PERSON>, Norwegian-German author and critic", "html": "1956 - <a href=\"https://wikipedia.org/wiki/Ingvar_Ambj%C3%B8<PERSON><PERSON>\" title=\"Ingvar <PERSON>jø<PERSON>\"><PERSON><PERSON><PERSON></a>, Norwegian-German author and critic", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Ingvar_Ambj%C3%B8<PERSON><PERSON>\" title=\"Ingvar <PERSON>j<PERSON>\"><PERSON><PERSON><PERSON></a>, Norwegian-German author and critic", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Ingvar_Ambj%C3%B8<PERSON><PERSON>"}]}, {"year": "1956", "text": "<PERSON>, English born Irish international footballer and coach", "html": "1956 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English born Irish international footballer and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English born Irish international footballer and coach", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1956", "text": "<PERSON>, American journalist and author", "html": "1956 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American journalist and author", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American journalist and author", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1957", "text": "<PERSON><PERSON><PERSON>, Japanese lawyer and politician, 62nd Prime Minister of Japan", "html": "1957 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese lawyer and politician, 62nd <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Japan\" title=\"Prime Minister of Japan\">Prime Minister of Japan</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese lawyer and politician, 62nd <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Japan\" title=\"Prime Minister of Japan\">Prime Minister of Japan</a>", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}, {"title": "Prime Minister of Japan", "link": "https://wikipedia.org/wiki/Prime_Minister_of_Japan"}]}, {"year": "1958", "text": "<PERSON>, American journalist and radio host", "html": "1958 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American journalist and radio host", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American journalist and radio host", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1958", "text": "<PERSON>, American singer-songwriter, guitarist, and actress", "html": "1958 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter, guitarist, and actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter, guitarist, and actress", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1959", "text": "<PERSON>, American singer-songwriter", "html": "1959 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1960", "text": "<PERSON>, American actor and director", "html": "1960 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor and director", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor and director", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1961", "text": "<PERSON>, English international footballer and manager", "html": "1961 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English international footballer and manager", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English international footballer and manager", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1961", "text": "<PERSON>, English singer-songwriter and guitarist", "html": "1961 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English singer-songwriter and guitarist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English singer-songwriter and guitarist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1963", "text": "<PERSON>, American baseball player and sportscaster", "html": "1963 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player and sportscaster", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player and sportscaster", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1964", "text": "<PERSON><PERSON><PERSON><PERSON>, Japanese politician, 80th Japanese Minister for Foreign Affairs", "html": "1964 - <a href=\"https://wikipedia.org/wiki/K%C5%8Dichir%C5%8D_Genba\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Japanese politician, 80th <a href=\"https://wikipedia.org/wiki/Minister_for_Foreign_Affairs_(Japan)\" title=\"Minister for Foreign Affairs (Japan)\">Japanese Minister for Foreign Affairs</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/K%C5%8Dichir%C5%8D_Genba\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Japanese politician, 80th <a href=\"https://wikipedia.org/wiki/Minister_for_Foreign_Affairs_(Japan)\" title=\"Minister for Foreign Affairs (Japan)\">Japanese Minister for Foreign Affairs</a>", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/K%C5%8Dichir%C5%8D_Genba"}, {"title": "Minister for Foreign Affairs (Japan)", "link": "https://wikipedia.org/wiki/Minister_for_Foreign_Affairs_(Japan)"}]}, {"year": "1964", "text": "<PERSON><PERSON>, Slovenian footballer, coach, and manager", "html": "1964 - <a href=\"https://wikipedia.org/wiki/Ed<PERSON>_<PERSON>i%C4%87\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Slovenian footballer, coach, and manager", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>i%C4%87\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Slovenian footballer, coach, and manager", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Edin_Osmanovi%C4%87"}]}, {"year": "1964", "text": "<PERSON>, 9th <PERSON>, English journalist and author", "html": "1964 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_9th_Earl_<PERSON>\" title=\"<PERSON>, 9th <PERSON>\"><PERSON>, 9th <PERSON></a>, English journalist and author", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_9th_<PERSON>_<PERSON>\" title=\"<PERSON>, 9th <PERSON>\"><PERSON>, 9th <PERSON></a>, English journalist and author", "links": [{"title": "<PERSON>, 9th <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_9th_Earl_<PERSON>"}]}, {"year": "1965", "text": "<PERSON>, American television host and author", "html": "1965 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American television host and author", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American television host and author", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1965", "text": "<PERSON><PERSON>, Canadian ice hockey player, sportscaster, and lawyer", "html": "1965 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Canadian ice hockey player, sportscaster, and lawyer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Canadian ice hockey player, sportscaster, and lawyer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON><PERSON>"}]}, {"year": "1966", "text": "<PERSON>, American journalist and author", "html": "1966 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American journalist and author", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American journalist and author", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1967", "text": "<PERSON>, English politician", "html": "1967 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English politician", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1967", "text": "<PERSON><PERSON>, Italian director, producer, and screenwriter", "html": "1967 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Italian director, producer, and screenwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Italian director, producer, and screenwriter", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1968", "text": "<PERSON>, American actor and producer", "html": "1968 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>ly<PERSON>\"><PERSON></a>, American actor and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor and producer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1969", "text": "<PERSON>, American wrestler, producer, and soldier", "html": "1969 - <a href=\"https://wikipedia.org/wiki/Road_Dogg\" title=\"Road Dogg\">Road Dogg</a>, American wrestler, producer, and soldier", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Road_Dogg\" title=\"Road Dogg\">Road Dogg</a>, American wrestler, producer, and soldier", "links": [{"title": "Road Dogg", "link": "https://wikipedia.org/wiki/Road_Dogg"}]}, {"year": "1970", "text": "<PERSON><PERSON>, American basketball player", "html": "1970 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American basketball player", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1970", "text": "<PERSON>, Singaporean-English journalist and producer", "html": "1970 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Singaporean-English journalist and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Singaporean-English journalist and producer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>roux"}]}, {"year": "1971", "text": "<PERSON><PERSON><PERSON>, Czech triple jumper and coach", "html": "1971 - <a href=\"https://wikipedia.org/wiki/%C5%A0%C3%A1rka_Ka%C5%A1p%C3%A1rkov%C3%A1\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Czech triple jumper and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/%C5%A0%C3%A1rka_Ka%C5%A1p%C3%A1rkov%C3%A1\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Czech triple jumper and coach", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/%C5%A0%C3%A1rka_Ka%C5%A1p%C3%A1rkov%C3%A1"}]}, {"year": "1971", "text": "<PERSON>, American race car driver", "html": "1971 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American race car driver", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American race car driver", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1972", "text": "<PERSON>, Australian shooter", "html": "1972 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(sport_shooter)\" title=\"<PERSON> (sport shooter)\"><PERSON></a>, Australian shooter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(sport_shooter)\" title=\"<PERSON> (sport shooter)\"><PERSON></a>, Australian shooter", "links": [{"title": "<PERSON> (sport shooter)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(sport_shooter)"}]}, {"year": "1972", "text": "<PERSON>, French rugby player (d. 2020)", "html": "1972 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French rugby player (d. 2020)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French rugby player (d. 2020)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1972", "text": "<PERSON><PERSON>, American rapper, producer, and actor", "html": "1972 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_Rhymes\" title=\"Busta Rhymes\"><PERSON><PERSON>hy<PERSON></a>, American rapper, producer, and actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_Rhymes\" title=\"Busta Rhymes\"><PERSON><PERSON> Rhy<PERSON></a>, American rapper, producer, and actor", "links": [{"title": "Busta Rhymes", "link": "https://wikipedia.org/wiki/Busta_Rhymes"}]}, {"year": "1973", "text": "<PERSON>, Australian rugby league player", "html": "1973 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(rugby_league)\" title=\"<PERSON> (rugby league)\"><PERSON></a>, Australian rugby league player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(rugby_league)\" title=\"<PERSON> (rugby league)\"><PERSON></a>, Australian rugby league player", "links": [{"title": "<PERSON> (rugby league)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(rugby_league)"}]}, {"year": "1974", "text": "<PERSON>, American novelist and short story writer", "html": "1974 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American novelist and short story writer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Am<PERSON>\"><PERSON></a>, American novelist and short story writer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>end"}]}, {"year": "1974", "text": "<PERSON><PERSON><PERSON><PERSON><PERSON>, Indian film director, writer and actor", "html": "1974 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON><PERSON></a>, Indian film director, writer and actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON><PERSON></a>, Indian film director, writer and actor", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON><PERSON>_<PERSON><PERSON>"}]}, {"year": "1975", "text": "<PERSON>, Argentinian actor, director, and screenwriter", "html": "1975 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%ADn\" title=\"<PERSON>\"><PERSON></a>, Argentinian actor, director, and screenwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%ADn\" title=\"<PERSON>\"><PERSON></a>, Argentinian actor, director, and screenwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>uj%C3%ADn"}]}, {"year": "1976", "text": "<PERSON>, Venezuelan-American baseball player", "html": "1976 - <a href=\"https://wikipedia.org/wiki/Ram%C3%B3n_Hern%C3%A1ndez\" title=\"<PERSON>\"><PERSON></a>, Venezuelan-American baseball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Ram%C3%B3n_Hern%C3%A1ndez\" title=\"<PERSON>\"><PERSON></a>, Venezuelan-American baseball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Ram%C3%B3n_Hern%C3%A1ndez"}]}, {"year": "1976", "text": "<PERSON><PERSON>, Japanese baseball player", "html": "1976 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Japanese baseball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Japanese baseball player", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1977", "text": "<PERSON>, American actor", "html": "1977 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1977", "text": "<PERSON>, Argentinian footballer", "html": "1977 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Argentinian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Argentinian footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1977", "text": "<PERSON>, American actress", "html": "1977 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1977", "text": "<PERSON>, Australian rugby player", "html": "1977 - <a href=\"https://wikipedia.org/wiki/<PERSON>_Mo<PERSON>lock\" title=\"<PERSON> Mortlock\"><PERSON></a>, Australian rugby player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_Mo<PERSON>lock\" title=\"<PERSON> Mortlock\"><PERSON></a>, Australian rugby player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>lock"}]}, {"year": "1977", "text": "<PERSON><PERSON><PERSON>, Finnish ice hockey player", "html": "1977 - <a href=\"https://wikipedia.org/wiki/Ves<PERSON>_<PERSON>\" title=\"Vesa <PERSON>\"><PERSON><PERSON><PERSON></a>, Finnish ice hockey player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Ves<PERSON>_<PERSON>\" title=\"Vesa <PERSON>\"><PERSON><PERSON><PERSON></a>, Finnish ice hockey player", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Ves<PERSON>_<PERSON>"}]}, {"year": "1978", "text": "<PERSON><PERSON><PERSON>, Greek chess player", "html": "1978 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Greek chess player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Greek chess player", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1978", "text": "<PERSON><PERSON><PERSON>-<PERSON><PERSON><PERSON><PERSON><PERSON>, Czech pole vaulter", "html": "1978 - <a href=\"https://wikipedia.org/wiki/Pavla_Ham%C3%A1%C4%8Dkov%C3%A1-Rybov%C3%A1\" title=\"<PERSON><PERSON><PERSON>-Rybová\"><PERSON><PERSON><PERSON>-Rybov<PERSON></a>, Czech pole vaulter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Pavla_Ham%C3%A1%C4%8Dkov%C3%A1-Rybov%C3%A1\" title=\"<PERSON><PERSON><PERSON>-Rybová\"><PERSON><PERSON><PERSON>-Rybov<PERSON></a>, Czech pole vaulter", "links": [{"title": "<PERSON><PERSON><PERSON>-Rybová", "link": "https://wikipedia.org/wiki/Pavla_Ham%C3%A1%C4%8Dkov%C3%A1-Rybov%C3%A1"}]}, {"year": "1978", "text": "<PERSON><PERSON>, German runner", "html": "1978 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, German runner", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, German runner", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>n"}]}, {"year": "1979", "text": "<PERSON>, Canadian politician, 28th Leader of the Conservative Party of Canada", "html": "1979 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian politician, 28th <a href=\"https://wikipedia.org/wiki/List_of_Leaders_of_Canada%27s_Conservative_Parties\" class=\"mw-redirect\" title=\"List of Leaders of Canada's Conservative Parties\">Leader of the Conservative Party of Canada</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian politician, 28th <a href=\"https://wikipedia.org/wiki/List_of_Leaders_of_Canada%27s_Conservative_Parties\" class=\"mw-redirect\" title=\"List of Leaders of Canada's Conservative Parties\">Leader of the Conservative Party of Canada</a>", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "List of Leaders of Canada's Conservative Parties", "link": "https://wikipedia.org/wiki/List_of_Leaders_of_Canada%27s_Conservative_Parties"}]}, {"year": "1979", "text": "<PERSON><PERSON>, American baseball player", "html": "1979 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American baseball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American baseball player", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1980", "text": "<PERSON>, American baseball player", "html": "1980 - <a href=\"https://wikipedia.org/wiki/Austin_Ke<PERSON>ns\" title=\"<PERSON> Ke<PERSON>\"><PERSON></a>, American baseball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Austin_Ke<PERSON>ns\" title=\"<PERSON>\"><PERSON></a>, American baseball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Austin_Kearns"}]}, {"year": "1980", "text": "<PERSON><PERSON><PERSON>, American football player", "html": "1980 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American football player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American football player", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1981", "text": "<PERSON><PERSON>, Spanish footballer", "html": "1981 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Spanish footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Spanish footballer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1981", "text": "<PERSON>, American singer and songwriter", "html": "1981 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer and songwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer and songwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1981", "text": "<PERSON>, American basketball player", "html": "1981 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1981", "text": "<PERSON>, Australian race car driver", "html": "1981 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian race car driver", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian race car driver", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1982", "text": "<PERSON><PERSON>, Czech footballer", "html": "1982 - <a href=\"https://wikipedia.org/wiki/Petr_%C4%8Cech\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Czech footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Petr_%C4%8Cech\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Czech footballer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Petr_%C4%8Cech"}]}, {"year": "1982", "text": "<PERSON><PERSON><PERSON>, Pakistani cricketer", "html": "1982 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Pakistani cricketer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Pakistani cricketer", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1982", "text": "<PERSON>, English actress", "html": "1982 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actress", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1982", "text": "<PERSON>, Brazilian director, producer, and screenwriter", "html": "1982 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(director)\" title=\"<PERSON> (director)\"><PERSON></a>, Brazilian director, producer, and screenwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(director)\" title=\"<PERSON> (director)\"><PERSON></a>, Brazilian director, producer, and screenwriter", "links": [{"title": "<PERSON> (director)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(director)"}]}, {"year": "1983", "text": "<PERSON><PERSON><PERSON>, Paraguayan footballer", "html": "1983 - <a href=\"https://wikipedia.org/wiki/%C3%93scar_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Paraguayan footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/%C3%93scar_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Paraguayan footballer", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/%C3%93scar_<PERSON><PERSON>o"}]}, {"year": "1983", "text": "<PERSON>, English rower", "html": "1983 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English rower", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English rower", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1984", "text": "<PERSON><PERSON>, Brazilian footballer", "html": "1984 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Brazilian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Brazilian footballer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1984", "text": "<PERSON>, American basketball player", "html": "1984 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>.\" title=\"<PERSON> Jr.\"><PERSON>.</a>, American basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>.\" title=\"<PERSON> Jr.\"><PERSON>.</a>, American basketball player", "links": [{"title": "<PERSON> Jr.", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>."}]}, {"year": "1984", "text": "<PERSON>, American football player", "html": "1984 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1985", "text": "<PERSON>, Kenyan-English cyclist", "html": "1985 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Kenyan-English cyclist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Kenyan-English cyclist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1985", "text": "<PERSON><PERSON><PERSON>, Australian footballer", "html": "1985 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Australian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Australian footballer", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1986", "text": "<PERSON>, English footballer", "html": "1986 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>tock"}]}, {"year": "1986", "text": "<PERSON><PERSON><PERSON><PERSON>, Cameroonian footballer", "html": "1986 - <a href=\"https://wikipedia.org/wiki/St%C3%A9phane_M<PERSON>\" title=\"St<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Cameroonian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/St%C3%A9phane_<PERSON>\" title=\"St<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Cameroonian footballer", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/St%C3%A9phane_<PERSON>bia"}]}, {"year": "1986", "text": "<PERSON><PERSON><PERSON>, Czech pole vaulter", "html": "1986 - <a href=\"https://wikipedia.org/wiki/Ji%C5%99ina_Svobodov%C3%A1\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON> Svobodová\"><PERSON><PERSON><PERSON></a>, Czech pole vaulter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Ji%C5%99ina_Svobodov%C3%A1\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON>vobodov<PERSON>\"><PERSON><PERSON><PERSON></a>, Czech pole vaulter", "links": [{"title": "<PERSON><PERSON>ina Svobodová", "link": "https://wikipedia.org/wiki/Ji%C5%99ina_Svobodov%C3%A1"}]}, {"year": "1987", "text": "<PERSON>, Japanese footballer", "html": "1987 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Japanese footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Japanese footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1987", "text": "<PERSON>, American basketball player", "html": "1987 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1988", "text": "<PERSON>, Australian rugby league player", "html": "1988 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian rugby league player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian rugby league player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1989", "text": "<PERSON><PERSON><PERSON>, Australian-Tongan rugby league player", "html": "1989 - <a href=\"https://wikipedia.org/wiki/<PERSON>os<PERSON>_Vave\" class=\"mw-redirect\" title=\"Siosia Vave\"><PERSON><PERSON><PERSON></a>, Australian-Tongan rugby league player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>os<PERSON>_Vave\" class=\"mw-redirect\" title=\"Siosia Vave\"><PERSON><PERSON><PERSON></a>, Australian-Tongan rugby league player", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Siosia_Vave"}]}, {"year": "1991", "text": "<PERSON><PERSON><PERSON>, Swiss singer, songwriter, and performer", "html": "1991 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Swiss singer, songwriter, and performer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Swiss singer, songwriter, and performer", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1991", "text": "<PERSON><PERSON>, Turkish footballer", "html": "1991 - <a href=\"https://wikipedia.org/wiki/Emre_%C3%87olak\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Turkish footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Emre_%C3%87olak\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Turkish footballer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Emre_%C3%87olak"}]}, {"year": "1992", "text": "<PERSON><PERSON>, Malawian-Australian swimmer", "html": "1992 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Malawian-Australian swimmer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Malawian-Australian swimmer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1992", "text": "<PERSON>, Irish actor", "html": "1992 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Irish actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Irish actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1992", "text": "<PERSON><PERSON>, Turkish basketball player", "html": "1992 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Turkish basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Turkish basketball player", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1992", "text": "<PERSON>, Swiss freestyle skier", "html": "1992 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swiss freestyle skier", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swiss freestyle skier", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1993", "text": "<PERSON><PERSON>, Egyptian footballer", "html": "1993 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Egyptian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Egyptian footballer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1993", "text": "<PERSON>, American figure skater", "html": "1993 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American figure skater", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American figure skater", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1996", "text": "<PERSON>, Australian rugby league player", "html": "1996 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(rugby_league)\" title=\"<PERSON> (rugby league)\"><PERSON></a>, Australian rugby league player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(rugby_league)\" title=\"<PERSON> (rugby league)\"><PERSON></a>, Australian rugby league player", "links": [{"title": "<PERSON> (rugby league)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(rugby_league)"}]}, {"year": "1997", "text": "<PERSON><PERSON><PERSON>, Japanese footballer", "html": "1997 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese footballer", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1998", "text": "<PERSON>, English race car driver", "html": "1998 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English race car driver", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English race car driver", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1998", "text": "<PERSON>, Canadian figure skater", "html": "1998 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian figure skater", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian figure skater", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}], "Deaths": [{"year": "685", "text": "<PERSON><PERSON><PERSON><PERSON><PERSON> of Northumbria (b. 645)", "html": "685 - <a href=\"https://wikipedia.org/wiki/Ec<PERSON><PERSON><PERSON>_of_Northumbria\" title=\"<PERSON><PERSON><PERSON><PERSON><PERSON> of Northumbria\"><PERSON><PERSON><PERSON><PERSON><PERSON> of Northumbria</a> (b. 645)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Ec<PERSON><PERSON><PERSON>_of_Northumbria\" title=\"<PERSON><PERSON><PERSON><PERSON><PERSON> of Northumbria\"><PERSON><PERSON><PERSON><PERSON><PERSON> of Northumbria</a> (b. 645)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON><PERSON> of Northumbria", "link": "https://wikipedia.org/wiki/Ecgfrith_of_Northumbria"}]}, {"year": "794", "text": "<PERSON><PERSON><PERSON><PERSON><PERSON> II, king of East Anglia", "html": "794 - <a href=\"https://wikipedia.org/wiki/%C3%86thelberht_II_of_East_Anglia\" title=\"Æthelberht II of East Anglia\"><PERSON><PERSON><PERSON><PERSON><PERSON> II</a>, king of <a href=\"https://wikipedia.org/wiki/Kingdom_of_East_Anglia\" title=\"Kingdom of East Anglia\">East Anglia</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/%C3%86thelberht_II_of_East_Anglia\" title=\"Æthelberht II of East Anglia\"><PERSON><PERSON><PERSON><PERSON><PERSON> II</a>, king of <a href=\"https://wikipedia.org/wiki/Kingdom_of_East_Anglia\" title=\"Kingdom of East Anglia\">East Anglia</a>", "links": [{"title": "Æthelberht II of East Anglia", "link": "https://wikipedia.org/wiki/%C3%86thelberht_II_of_East_Anglia"}, {"title": "Kingdom of East Anglia", "link": "https://wikipedia.org/wiki/Kingdom_of_East_Anglia"}]}, {"year": "965", "text": "<PERSON><PERSON> the <PERSON>, Saxon ruler (b.c. 900)", "html": "965 - <a href=\"https://wikipedia.org/wiki/Gero\" title=\"<PERSON><PERSON>\"><PERSON><PERSON> the Great</a>, Saxon ruler (b.c. <a href=\"https://wikipedia.org/wiki/900\" title=\"900\">900</a>)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Gero\" title=\"<PERSON><PERSON>\"><PERSON><PERSON> the Great</a>, Saxon ruler (b.c. <a href=\"https://wikipedia.org/wiki/900\" title=\"900\">900</a>)", "links": [{"title": "Gero", "link": "https://wikipedia.org/wiki/Gero"}, {"title": "900", "link": "https://wikipedia.org/wiki/900"}]}, {"year": "1062", "text": "<PERSON><PERSON>, Chinese magistrate and mayor of Kaifeng (b. 999)", "html": "1062 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Chinese magistrate and mayor of <a href=\"https://wikipedia.org/wiki/Kaifeng\" title=\"Kaifeng\">Kaifeng</a> (b. 999)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Chinese magistrate and mayor of <a href=\"https://wikipedia.org/wiki/Kaifeng\" title=\"Kaifeng\">Kaifeng</a> (b. 999)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}, {"title": "Kaifeng", "link": "https://wikipedia.org/wiki/Kai<PERSON>"}]}, {"year": "1277", "text": "<PERSON> (b. 1215)", "html": "1277 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON> John X<PERSON>\"><PERSON> John XXI</a> (b. 1215)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_XXI\" title=\"Pope John XX<PERSON>\">Pope John XXI</a> (b. 1215)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1285", "text": "<PERSON> of Cyprus (b. 1259)", "html": "1285 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Cyprus\" title=\"<PERSON> of Cyprus\"><PERSON> of Cyprus</a> (b. 1259)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Cyprus\" title=\"<PERSON> of Cyprus\"><PERSON> of Cyprus</a> (b. 1259)", "links": [{"title": "<PERSON> of Cyprus", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Cyprus"}]}, {"year": "1291", "text": "Sufi Saint <PERSON><PERSON><PERSON><PERSON><PERSON>", "html": "1291 - Sufi Saint <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>-<PERSON>_Bukhari\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>", "no_year_html": "Sufi Saint <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>-<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>-<PERSON><PERSON>_<PERSON>"}]}, {"year": "1366", "text": "<PERSON> of Calabria, Empress of Constantinople (b. 1329)", "html": "1366 - <a href=\"https://wikipedia.org/wiki/Maria_of_Calabria\" title=\"<PERSON> of Calabria\"><PERSON> of Calabria</a>, Empress of Constantinople (b. 1329)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Maria_of_Calabria\" title=\"<PERSON> of Calabria\"><PERSON> of Calabria</a>, Empress of Constantinople (b. 1329)", "links": [{"title": "Maria of Calabria", "link": "https://wikipedia.org/wiki/Maria_of_Calabria"}]}, {"year": "1444", "text": "<PERSON> of Siena, Italian-Spanish missionary and saint (b. 1380)", "html": "1444 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_Siena\" title=\"Bernardino of Siena\"><PERSON> of Siena</a>, Italian-Spanish missionary and saint (b. 1380)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_Siena\" title=\"<PERSON> of Siena\"><PERSON> of Siena</a>, Italian-Spanish missionary and saint (b. 1380)", "links": [{"title": "<PERSON> of Siena", "link": "https://wikipedia.org/wiki/Bernardino_of_Siena"}]}, {"year": "1449", "text": "<PERSON><PERSON><PERSON>, 1st Count of Avranches", "html": "1449 - <a href=\"https://wikipedia.org/wiki/%C3%<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>_<PERSON>,_1st_Count_of_Avranches\" title=\"<PERSON><PERSON><PERSON>, 1st Count of Avranches\"><PERSON><PERSON><PERSON>, 1st Count of Avranches</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/%C3%<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>_<PERSON>,_1st_Count_of_Avranches\" title=\"<PERSON><PERSON><PERSON>, 1st Count of Avranches\"><PERSON><PERSON><PERSON>, 1st Count of Avranches</a>", "links": [{"title": "<PERSON><PERSON><PERSON>, 1st Count of Avranches", "link": "https://wikipedia.org/wiki/%C3%81l<PERSON>_<PERSON>_<PERSON>_<PERSON>,_1st_Count_of_Avranches"}]}, {"year": "1449", "text": "<PERSON><PERSON><PERSON>, Duke of Coimbra (b. 1392)", "html": "1449 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>,_Duke_of_Coimbra\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON>, Duke of Coimbra\"><PERSON><PERSON><PERSON>, Duke of Coimbra</a> (b. 1392)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>,_Duke_of_Coimbra\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON>, Duke of Coimbra\"><PERSON><PERSON><PERSON>, Duke of Coimbra</a> (b. 1392)", "links": [{"title": "<PERSON><PERSON><PERSON>, Duke of Coimbra", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>,_Duke_of_Coimbra"}]}, {"year": "1501", "text": "<PERSON><PERSON> of Rieti, Italian Dominican tertiary Religious Sister (b. 1467)", "html": "1501 - <a href=\"https://wikipedia.org/wiki/Columba_of_Rieti\" title=\"Columba of Rieti\"><PERSON><PERSON> of Rieti</a>, Italian Dominican tertiary Religious Sister (b. 1467)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Columba_of_Rieti\" title=\"Columba of Rieti\"><PERSON><PERSON> of Rieti</a>, Italian Dominican tertiary Religious Sister (b. 1467)", "links": [{"title": "Columba of Rieti", "link": "https://wikipedia.org/wiki/Columba_of_Rieti"}]}, {"year": "1503", "text": "<PERSON>, Italian banker and politician (b. 1463)", "html": "1503 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_de%27_<PERSON>\" title=\"<PERSON> Pierfrancesco de<PERSON> Medici\"><PERSON></a>, Italian banker and politician (b. 1463)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_de%27_Medici\" title=\"<PERSON> Pier<PERSON> de<PERSON>\"><PERSON></a>, Italian banker and politician (b. 1463)", "links": [{"title": "Lorenzo di Pierfrancesco de' Medici", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_Pierfrances<PERSON>_de%27_<PERSON>"}]}, {"year": "1506", "text": "<PERSON>, Italian explorer, early European explorer of the Americas (b. 1451)", "html": "1506 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian explorer, early European explorer of the <a href=\"https://wikipedia.org/wiki/Americas\" title=\"Americas\">Americas</a> (b. 1451)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"Christopher <PERSON>\"><PERSON></a>, Italian explorer, early European explorer of the <a href=\"https://wikipedia.org/wiki/Americas\" title=\"Americas\">Americas</a> (b. 1451)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Americas", "link": "https://wikipedia.org/wiki/Americas"}]}, {"year": "1550", "text": "<PERSON><PERSON><PERSON>, Japanese shōgun (b. 1510)", "html": "1550 - <a href=\"https://wikipedia.org/wiki/Ash<PERSON><PERSON>_Yoshiharu\" title=\"Ashikaga Yoshiharu\"><PERSON><PERSON><PERSON></a>, Japanese shōgun (b. 1510)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Ash<PERSON><PERSON>_Yoshiharu\" title=\"Ashikaga Yoshiharu\"><PERSON><PERSON><PERSON></a>, Japanese shōgun (b. 1510)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>ika<PERSON>_Yoshiharu"}]}, {"year": "1579", "text": "<PERSON>, English courtier (b. 1527)", "html": "1579 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English courtier (b. 1527)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English courtier (b. 1527)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1622", "text": "<PERSON><PERSON>, Ottoman sultan (b. 1604)", "html": "1622 - <a href=\"https://wikipedia.org/wiki/<PERSON>sman_II\" title=\"Osman II\"><PERSON><PERSON> <PERSON></a>, Ottoman sultan (b. 1604)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Osman_II\" title=\"Osman II\"><PERSON><PERSON> <PERSON></a>, Ottoman sultan (b. 1604)", "links": [{"title": "Osman II", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_II"}]}, {"year": "1645", "text": "<PERSON>, Chinese general and calligrapher (b. 1601)", "html": "1645 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Chinese general and calligrapher (b. 1601)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Chinese general and calligrapher (b. 1601)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>"}]}, {"year": "1648", "text": "<PERSON><PERSON><PERSON><PERSON>, Polish son of <PERSON><PERSON><PERSON> (b. 1595)", "html": "1648 - <a href=\"https://wikipedia.org/wiki/W%C5%82adys%C5%82aw_IV_Vasa\" title=\"<PERSON><PERSON><PERSON><PERSON> IV Vasa\"><PERSON><PERSON><PERSON><PERSON> IV Vasa</a>, Polish son of <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_III_Vasa\" title=\"<PERSON><PERSON><PERSON> III Vasa\"><PERSON><PERSON><PERSON> III Vasa</a> (b. 1595)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/W%C5%82adys%C5%82aw_IV_Vasa\" title=\"<PERSON><PERSON><PERSON><PERSON> IV Vasa\"><PERSON><PERSON><PERSON><PERSON> IV Vasa</a>, Polish son of <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_III_Vasa\" title=\"<PERSON><PERSON><PERSON> III Vasa\"><PERSON><PERSON><PERSON> III Vasa</a> (b. 1595)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON> IV Vasa", "link": "https://wikipedia.org/wiki/W%C5%82adys%C5%82aw_IV_Vasa"}, {"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1677", "text": "<PERSON>, 2nd Earl of Bristol, Spanish-English politician, English Secretary of State (b. 1612)", "html": "1677 - <a href=\"https://wikipedia.org/wiki/<PERSON>,_2nd_Earl_of_Bristol\" title=\"<PERSON>, 2nd Earl of Bristol\"><PERSON>, 2nd Earl of Bristol</a>, Spanish-English politician, <a href=\"https://wikipedia.org/wiki/Secretary_of_State_(England)\" title=\"Secretary of State (England)\">English Secretary of State</a> (b. 1612)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>,_2nd_Earl_of_Bristol\" title=\"<PERSON>, 2nd Earl of Bristol\"><PERSON>, 2nd Earl of Bristol</a>, Spanish-English politician, <a href=\"https://wikipedia.org/wiki/Secretary_of_State_(England)\" title=\"Secretary of State (England)\">English Secretary of State</a> (b. 1612)", "links": [{"title": "<PERSON>, 2nd Earl of Bristol", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_2nd_Earl_of_Bristol"}, {"title": "Secretary of State (England)", "link": "https://wikipedia.org/wiki/Secretary_of_State_(England)"}]}, {"year": "1713", "text": "<PERSON>, English bishop (b. 1635)", "html": "1713 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English bishop (b. 1635)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English bishop (b. 1635)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1717", "text": "<PERSON>, Welsh lawyer and politician, 102nd Speaker of the House of Commons (b. 1637)", "html": "1717 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>(speaker)\" title=\"<PERSON> (speaker)\"><PERSON></a>, Welsh lawyer and politician, 102nd <a href=\"https://wikipedia.org/wiki/Speaker_of_the_House_of_Commons_(United_Kingdom)\" title=\"Speaker of the House of Commons (United Kingdom)\">Speaker of the House of Commons</a> (b. 1637)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>(speaker)\" title=\"<PERSON> (speaker)\"><PERSON></a>, Welsh lawyer and politician, 102nd <a href=\"https://wikipedia.org/wiki/Speaker_of_the_House_of_Commons_(United_Kingdom)\" title=\"Speaker of the House of Commons (United Kingdom)\">Speaker of the House of Commons</a> (b. 1637)", "links": [{"title": "<PERSON> (speaker)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>(speaker)"}, {"title": "Speaker of the House of Commons (United Kingdom)", "link": "https://wikipedia.org/wiki/Speaker_of_the_House_of_Commons_(United_Kingdom)"}]}, {"year": "1722", "text": "<PERSON><PERSON><PERSON><PERSON>, French botanist and mycologist (b. 1669)", "html": "1722 - <a href=\"https://wikipedia.org/wiki/S%C3%A<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, French botanist and mycologist (b. 1669)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/S%C3%A<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, French botanist and mycologist (b. 1669)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/S%C3%<PERSON><PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1732", "text": "<PERSON>, Scottish author and educator (b. 1676)", "html": "1732 - <a href=\"https://wikipedia.org/wiki/Thomas_<PERSON>\" title=\"Thomas Boston\"><PERSON></a>, Scottish author and educator (b. 1676)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Thomas_Boston\" title=\"Thomas Boston\"><PERSON></a>, Scottish author and educator (b. 1676)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Thomas_<PERSON>"}]}, {"year": "1782", "text": "<PERSON>, English mathematician and academic (b. 1701)", "html": "1782 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>(mathematician)\" title=\"<PERSON> (mathematician)\"><PERSON></a>, English mathematician and academic (b. 1701)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>(mathematician)\" title=\"<PERSON> (mathematician)\"><PERSON></a>, English mathematician and academic (b. 1701)", "links": [{"title": "<PERSON> (mathematician)", "link": "https://wikipedia.org/wiki/<PERSON>(mathematician)"}]}, {"year": "1793", "text": "<PERSON>, Swiss botanist and biologist (b. 1720)", "html": "1793 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swiss botanist and biologist (b. 1720)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swiss botanist and biologist (b. 1720)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1812", "text": "Count <PERSON><PERSON><PERSON><PERSON>, Austrian archbishop (b. 1732)", "html": "1812 - <a href=\"https://wikipedia.org/wiki/Count_<PERSON><PERSON><PERSON><PERSON>_von_<PERSON>\" class=\"mw-redirect\" title=\"Count <PERSON><PERSON><PERSON><PERSON> von <PERSON>\">Count <PERSON><PERSON><PERSON><PERSON> von <PERSON></a>, Austrian archbishop (b. 1732)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Count_<PERSON><PERSON><PERSON><PERSON>_von_<PERSON>\" class=\"mw-redirect\" title=\"Count <PERSON><PERSON><PERSON><PERSON> von <PERSON>do\">Count <PERSON><PERSON><PERSON><PERSON> von <PERSON></a>, Austrian archbishop (b. 1732)", "links": [{"title": "Count <PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Count_<PERSON><PERSON><PERSON><PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1834", "text": "<PERSON>, <PERSON>, French general (b. 1757)", "html": "1834 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Marquis_<PERSON>_<PERSON>\" title=\"<PERSON>, <PERSON>\"><PERSON>, Marquis <PERSON></a>, French general (b. 1757)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Marquis_<PERSON>_<PERSON>\" title=\"<PERSON>, <PERSON>\"><PERSON>, Marquis <PERSON></a>, French general (b. 1757)", "links": [{"title": "<PERSON>, Marquis <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>,_<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1841", "text": "<PERSON>, Spanish poet and theologian (b. 1775)", "html": "1841 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Spanish poet and theologian (b. 1775)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Spanish poet and theologian (b. 1775)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1864", "text": "<PERSON>, English poet (b. 1793)", "html": "1864 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English poet (b. 1793)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English poet (b. 1793)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1873", "text": "<PERSON><PERSON><PERSON>, Canadian soldier, lawyer, and politician, 9th Premier of East Canada (b. 1814)", "html": "1873 - <a href=\"https://wikipedia.org/wiki/<PERSON>-%C3%89tien<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Canadian soldier, lawyer, and politician, 9th <a href=\"https://wikipedia.org/wiki/List_of_Joint_Premiers_of_the_Province_of_Canada\" class=\"mw-redirect\" title=\"List of Joint Premiers of the Province of Canada\">Premier of East Canada</a> (b. 1814)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>-%C3%89tien<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Canadian soldier, lawyer, and politician, 9th <a href=\"https://wikipedia.org/wiki/List_of_Joint_Premiers_of_the_Province_of_Canada\" class=\"mw-redirect\" title=\"List of Joint Premiers of the Province of Canada\">Premier of East Canada</a> (b. 1814)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>-%C3%89tien<PERSON>_<PERSON>"}, {"title": "List of Joint Premiers of the Province of Canada", "link": "https://wikipedia.org/wiki/List_of_Joint_Premiers_of_the_Province_of_Canada"}]}, {"year": "1880", "text": "<PERSON>, Brazilian nurse and philanthropist (b. 1814)", "html": "1880 - <a href=\"https://wikipedia.org/wiki/Ana_N%C3%A9ri\" title=\"<PERSON>\"><PERSON></a>, Brazilian nurse and philanthropist (b. 1814)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Ana_N%C3%A9ri\" title=\"<PERSON>\"><PERSON></a>, Brazilian nurse and philanthropist (b. 1814)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Ana_N%C3%A9ri"}]}, {"year": "1896", "text": "<PERSON>, German pianist and composer (b. 1819)", "html": "1896 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German pianist and composer (b. 1819)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German pianist and composer (b. 1819)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1909", "text": "<PERSON>, American actor and composer (b. 1859)", "html": "1909 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor and composer (b. 1859)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor and composer (b. 1859)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1917", "text": "<PERSON>, Scottish soldier and politician (b. 1887)", "html": "1917 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish soldier and politician (b. 1887)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish soldier and politician (b. 1887)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1917", "text": "<PERSON>, Italian stamp collector (b. 1850)", "html": "1917 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian stamp collector (b. 1850)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian stamp collector (b. 1850)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1924", "text": "<PERSON><PERSON><PERSON>, Mongolian ruler (c. 1869)", "html": "1924 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON>g<PERSON>\"><PERSON><PERSON><PERSON></a>, Mongolian ruler (c. 1869)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Mongolian ruler (c. 1869)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Bog<PERSON>_<PERSON>"}]}, {"year": "1925", "text": "<PERSON>, Maltese politician, 1st Prime Minister of Malta (b. 1862)", "html": "1925 - <a href=\"https://wikipedia.org/wiki/<PERSON>(Prime_Minister)\" title=\"<PERSON> (Prime Minister)\"><PERSON></a>, Maltese politician, 1st <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Malta\" title=\"Prime Minister of Malta\">Prime Minister of Malta</a> (b. 1862)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>(Prime_Minister)\" title=\"<PERSON> (Prime Minister)\"><PERSON></a>, Maltese politician, 1st <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Malta\" title=\"Prime Minister of Malta\">Prime Minister of Malta</a> (b. 1862)", "links": [{"title": "<PERSON> (Prime Minister)", "link": "https://wikipedia.org/wiki/<PERSON>(Prime_Minister)"}, {"title": "Prime Minister of Malta", "link": "https://wikipedia.org/wiki/Prime_Minister_of_Malta"}]}, {"year": "1931", "text": "<PERSON>, Scottish businessman and politician (b. 1831)", "html": "1931 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish businessman and politician (b. 1831)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish businessman and politician (b. 1831)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1940", "text": "<PERSON><PERSON><PERSON>, Swedish author and poet, Nobel Prize laureate (b. 1859)", "html": "1940 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON> von <PERSON>\"><PERSON><PERSON><PERSON> <PERSON></a>, Swedish author and poet, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Literature\" title=\"Nobel Prize in Literature\">Nobel Prize</a> laureate (b. 1859)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON> von <PERSON>\"><PERSON><PERSON><PERSON> <PERSON></a>, Swedish author and poet, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Literature\" title=\"Nobel Prize in Literature\">Nobel Prize</a> laureate (b. 1859)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/V<PERSON><PERSON>_<PERSON>_<PERSON>"}, {"title": "Nobel Prize in Literature", "link": "https://wikipedia.org/wiki/Nobel_Prize_in_Literature"}]}, {"year": "1942", "text": "<PERSON>, French Architect (b. 1867)", "html": "1942 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French Architect (b. 1867)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French Architect (b. 1867)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1946", "text": "<PERSON>, Danish pilot and engineer (b. 1871)", "html": "1946 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Danish pilot and engineer (b. 1871)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Danish pilot and engineer (b. 1871)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1947", "text": "<PERSON>, Slovak-German physicist and academic, Nobel Prize laureate (b. 1862)", "html": "1947 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Slovak-German physicist and academic, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Physics\" title=\"Nobel Prize in Physics\">Nobel Prize</a> laureate (b. 1862)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Slovak-German physicist and academic, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Physics\" title=\"Nobel Prize in Physics\">Nobel Prize</a> laureate (b. 1862)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Nobel Prize in Physics", "link": "https://wikipedia.org/wiki/Nobel_Prize_in_Physics"}]}, {"year": "1947", "text": "<PERSON><PERSON>, Greek sergeant and politician (b. 1890)", "html": "1947 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Greek sergeant and politician (b. 1890)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Greek sergeant and politician (b. 1890)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Georgios_Siantos"}]}, {"year": "1949", "text": "<PERSON><PERSON><PERSON><PERSON> of Athens, Greek archbishop and politician, 137th Prime Minister of Greece (b. 1891)", "html": "1949 - <a href=\"https://wikipedia.org/wiki/Damaskinos_of_Athens\" title=\"Damaskinos of Athens\"><PERSON><PERSON><PERSON><PERSON> of Athens</a>, Greek archbishop and politician, 137th <a href=\"https://wikipedia.org/wiki/List_of_Prime_Ministers_of_Greece\" class=\"mw-redirect\" title=\"List of Prime Ministers of Greece\">Prime Minister of Greece</a> (b. 1891)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Damaskinos_of_Athens\" title=\"Damaskinos of Athens\"><PERSON><PERSON><PERSON><PERSON> of Athens</a>, Greek archbishop and politician, 137th <a href=\"https://wikipedia.org/wiki/List_of_Prime_Ministers_of_Greece\" class=\"mw-redirect\" title=\"List of Prime Ministers of Greece\">Prime Minister of Greece</a> (b. 1891)", "links": [{"title": "Damaskinos of Athens", "link": "https://wikipedia.org/wiki/Damaskinos_of_Athens"}, {"title": "List of Prime Ministers of Greece", "link": "https://wikipedia.org/wiki/List_of_Prime_Ministers_of_Greece"}]}, {"year": "1956", "text": "<PERSON>,  English essayist, parodist, and caricaturist (b. 1872)", "html": "1956 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English essayist, parodist, and caricaturist (b. 1872)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English essayist, parodist, and caricaturist (b. 1872)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1956", "text": "<PERSON><PERSON><PERSON>, Hungarian swimmer and trainer (b. 1881)", "html": "1956 - <a href=\"https://wikipedia.org/wiki/Zolt%C3%A1n_Halmay\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Hungarian swimmer and trainer (b. 1881)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Zolt%C3%A1n_Halmay\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Hungarian swimmer and trainer (b. 1881)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Zolt%C3%A1n_Halmay"}]}, {"year": "1961", "text": "<PERSON>, German colonel and pilot (b. 1915)", "html": "1961 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German colonel and pilot (b. 1915)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German colonel and pilot (b. 1915)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1962", "text": "<PERSON> (<PERSON><PERSON><PERSON><PERSON>), a Polish Orthodox clergyman, the third Metropolitan of Warsaw and all Poland (b. 1901)", "html": "1962 - <a href=\"https://wikipedia.org/wiki/<PERSON>_(<PERSON><PERSON><PERSON><PERSON>)\" class=\"mw-redirect\" title=\"<PERSON> (<PERSON><PERSON><PERSON>)\"><PERSON> (<PERSON><PERSON>)</a>, a Polish <a href=\"https://wikipedia.org/wiki/Eastern_Orthodoxy\" title=\"Eastern Orthodoxy\">Orthodox</a> <a href=\"https://wikipedia.org/wiki/Clergyman\" class=\"mw-redirect\" title=\"Clergyman\">clergyman</a>, the third Metropolitan of Warsaw and all Poland (b. 1901)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(<PERSON><PERSON><PERSON><PERSON>)\" class=\"mw-redirect\" title=\"<PERSON> (<PERSON><PERSON><PERSON><PERSON>)\"><PERSON> (<PERSON><PERSON>)</a>, a Polish <a href=\"https://wikipedia.org/wiki/Eastern_Orthodoxy\" title=\"Eastern Orthodoxy\">Orthodox</a> <a href=\"https://wikipedia.org/wiki/Clergyman\" class=\"mw-redirect\" title=\"Clergyman\">clergyman</a>, the third Metropolitan of Warsaw and all Poland (b. 1901)", "links": [{"title": "<PERSON> (<PERSON><PERSON><PERSON><PERSON>)", "link": "https://wikipedia.org/wiki/<PERSON>_(<PERSON><PERSON><PERSON><PERSON>)"}, {"title": "Eastern Orthodoxy", "link": "https://wikipedia.org/wiki/Eastern_Orthodoxy"}, {"title": "Clergyman", "link": "https://wikipedia.org/wiki/Clergyman"}]}, {"year": "1964", "text": "<PERSON>, American singer (b. 1936)", "html": "1964 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer (b. 1936)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer (b. 1936)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1971", "text": "<PERSON><PERSON>, Welsh poet and academic (b. 1904)", "html": "1971 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Welsh poet and academic (b. 1904)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Welsh poet and academic (b. 1904)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1973", "text": "<PERSON><PERSON>, Italian motorcycle racer (b. 1938)", "html": "1973 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Italian motorcycle racer (b. 1938)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Italian motorcycle racer (b. 1938)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1973", "text": "<PERSON><PERSON><PERSON>, Finnish motorcycle racer (b. 1945)", "html": "1973 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Finnish motorcycle racer (b. 1945)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Finnish motorcycle racer (b. 1945)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1975", "text": "<PERSON>, English sculptor and lithographer (b. 1903)", "html": "1975 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English sculptor and lithographer (b. 1903)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English sculptor and lithographer (b. 1903)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1976", "text": "<PERSON><PERSON>, Canadian ice hockey player (b. 1911)", "html": "1976 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> <PERSON>\"><PERSON><PERSON></a>, Canadian ice hockey player (b. 1911)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> <PERSON>\"><PERSON><PERSON> <PERSON></a>, Canadian ice hockey player (b. 1911)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1976", "text": "<PERSON><PERSON><PERSON>, Uruguayan journalist and politician (b. 1924)", "html": "1976 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Uruguayan journalist and politician (b. 1924)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Uruguayan journalist and politician (b. 1924)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1976", "text": "<PERSON><PERSON><PERSON>, Uruguayan politician (b. 1934)", "html": "1976 - <a href=\"https://wikipedia.org/wiki/H%C3%A9ctor_Guti%C3%A9<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Uruguayan politician (b. 1934)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/H%C3%A9ctor_Guti%C3%A9<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Uruguayan politician (b. 1934)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/H%C3%A9ctor_Guti%C3%A9rrez_Ruiz"}]}, {"year": "1989", "text": "<PERSON>, English economist and academic, Nobel Prize laureate (b. 1904)", "html": "1989 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English economist and academic, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Economics\" class=\"mw-redirect\" title=\"Nobel Prize in Economics\">Nobel Prize</a> laureate (b. 1904)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English economist and academic, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Economics\" class=\"mw-redirect\" title=\"Nobel Prize in Economics\">Nobel Prize</a> laureate (b. 1904)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}, {"title": "Nobel Prize in Economics", "link": "https://wikipedia.org/wiki/Nobel_Prize_in_Economics"}]}, {"year": "1989", "text": "<PERSON><PERSON>, American actress and comedian (b. 1946)", "html": "1989 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American actress and comedian (b. 1946)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American actress and comedian (b. 1946)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1995", "text": "<PERSON>, Australian rugby league player (b. 1925)", "html": "1995 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian rugby league player (b. 1925)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian rugby league player (b. 1925)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1996", "text": "<PERSON>, English actor, portrayed the Third Doctor (b. 1919)", "html": "1996 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actor, portrayed the <a href=\"https://wikipedia.org/wiki/Third_Doctor\" title=\"Third Doctor\">Third Doctor</a> (b. 1919)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actor, portrayed the <a href=\"https://wikipedia.org/wiki/Third_Doctor\" title=\"Third Doctor\">Third Doctor</a> (b. 1919)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Third Doctor", "link": "https://wikipedia.org/wiki/Third_Doctor"}]}, {"year": "1998", "text": "<PERSON>, Norwegian guitarist (b. 1916)", "html": "1998 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Norwegian guitarist (b. 1916)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Norwegian guitarist (b. 1916)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2000", "text": "<PERSON><PERSON><PERSON>, French flute player (b. 1922)", "html": "2000 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, French flute player (b. 1922)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, French flute player (b. 1922)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>"}]}, {"year": "2000", "text": "<PERSON>, American basketball player and actor (b. 1970)", "html": "2000 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player and actor (b. 1970)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player and actor (b. 1970)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2000", "text": "<PERSON><PERSON><PERSON>, Russian colonel, engineer, and astronaut (b. 1933)", "html": "2000 - <a href=\"https://wikipedia.org/wiki/Ye<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Russian colonel, engineer, and astronaut (b. 1933)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Ye<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Russian colonel, engineer, and astronaut (b. 1933)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "2001", "text": "<PERSON><PERSON>, Italian singer-songwriter and pianist (b. 1920)", "html": "2001 - <a href=\"https://wikipedia.org/wiki/Ren<PERSON>_Carosone\" title=\"Renato Carosone\"><PERSON><PERSON></a>, Italian singer-songwriter and pianist (b. 1920)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Ren<PERSON>_<PERSON>osone\" title=\"Renato Carosone\"><PERSON><PERSON></a>, Italian singer-songwriter and pianist (b. 1920)", "links": [{"title": "Renato <PERSON>ne", "link": "https://wikipedia.org/wiki/Renato_<PERSON>ne"}]}, {"year": "2002", "text": "<PERSON>, American paleontologist, biologist, and academic (b. 1941)", "html": "2002 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American paleontologist, biologist, and academic (b. 1941)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American paleontologist, biologist, and academic (b. 1941)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2005", "text": "<PERSON>, French philosopher and academic (b. 1913)", "html": "2005 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C5%93ur\" title=\"<PERSON>\"><PERSON></a>, French philosopher and academic (b. 1913)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C5%93ur\" title=\"<PERSON>\"><PERSON></a>, French philosopher and academic (b. 1913)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>%C5%93ur"}]}, {"year": "2005", "text": "<PERSON>, American general (b. 1918)", "html": "2005 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American general (b. 1918)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American general (b. 1918)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "2007", "text": "<PERSON>, Australian golfer (b. 1914)", "html": "2007 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian golfer (b. 1914)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian golfer (b. 1914)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "2008", "text": "<PERSON>, American politician, 8th White House Chief of Staff (b. 1944)", "html": "2008 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Hamilton Jordan\"><PERSON></a>, American politician, 8th <a href=\"https://wikipedia.org/wiki/White_House_Chief_of_Staff\" title=\"White House Chief of Staff\">White House Chief of Staff</a> (b. 1944)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Hamilton_Jordan\" title=\"Hamilton Jordan\"><PERSON></a>, American politician, 8th <a href=\"https://wikipedia.org/wiki/White_House_Chief_of_Staff\" title=\"White House Chief of Staff\">White House Chief of Staff</a> (b. 1944)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Hamilton_Jordan"}, {"title": "White House Chief of Staff", "link": "https://wikipedia.org/wiki/White_House_Chief_of_Staff"}]}, {"year": "2009", "text": "<PERSON>, Canadian architect and urban planner, designed <PERSON> (b. 1924)", "html": "2009 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian architect and urban planner, designed <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Roy <PERSON> Hall\"><PERSON></a> (b. 1924)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian architect and urban planner, designed <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Roy <PERSON> Hall\"><PERSON></a> (b. 1924)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_Hall"}]}, {"year": "2009", "text": "<PERSON>, American actress and model (b. 1980)", "html": "2009 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(actress)\" title=\"<PERSON> (actress)\"><PERSON></a>, American actress and model (b. 1980)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(actress)\" title=\"<PERSON> (actress)\"><PERSON></a>, American actress and model (b. 1980)", "links": [{"title": "<PERSON> (actress)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(actress)"}]}, {"year": "2009", "text": "<PERSON>, French author, poet, and critic (b. 1919)", "html": "2009 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French author, poet, and critic (b. 1919)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French author, poet, and critic (b. 1919)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2011", "text": "<PERSON>, American wrestler and actor (b. 1952)", "html": "2011 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American wrestler and actor (b. 1952)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American wrestler and actor (b. 1952)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2012", "text": "<PERSON><PERSON>, Indian anthropologist and scholar (b. 1923)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Indian anthropologist and scholar (b. 1923)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Indian anthropologist and scholar (b. 1923)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "2012", "text": "<PERSON>, Manx-English singer-songwriter and producer (b. 1949)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Manx-English singer-songwriter and producer (b. 1949)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Manx-English singer-songwriter and producer (b. 1949)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2012", "text": "<PERSON>, English-Swiss historian, author, and academic (b. 1933)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(activist)\" title=\"<PERSON> (activist)\"><PERSON></a>, English-Swiss historian, author, and academic (b. 1933)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(activist)\" title=\"<PERSON> (activist)\"><PERSON></a>, English-Swiss historian, author, and academic (b. 1933)", "links": [{"title": "<PERSON> (activist)", "link": "https://wikipedia.org/wiki/<PERSON>_(activist)"}]}, {"year": "2012", "text": "<PERSON>, American bass guitarist (b. 1953)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American bass guitarist (b. 1953)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American bass guitarist (b. 1953)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2012", "text": "<PERSON>, American engineer, invented the remote control (b. 1915)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American engineer, invented the <a href=\"https://wikipedia.org/wiki/Remote_control\" title=\"Remote control\">remote control</a> (b. 1915)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American engineer, invented the <a href=\"https://wikipedia.org/wiki/Remote_control\" title=\"Remote control\">remote control</a> (b. 1915)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Remote control", "link": "https://wikipedia.org/wiki/Remote_control"}]}, {"year": "2012", "text": "<PERSON>, American lawyer (b. 1958)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer (b. 1958)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer (b. 1958)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "2013", "text": "<PERSON><PERSON><PERSON>, Italian painter and illustrator (b. 1926)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>i\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Italian painter and illustrator (b. 1926)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>i\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Italian painter and illustrator (b. 1926)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Flavio_Costantini"}]}, {"year": "2013", "text": "<PERSON>, Canadian ice hockey player and manager (b. 1924)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player and manager (b. 1924)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player and manager (b. 1924)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2013", "text": "<PERSON>, Swedish composer (b. 1947)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swedish composer (b. 1947)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swedish composer (b. 1947)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2013", "text": "<PERSON><PERSON>, Czech basketball player and coach (b. 1924)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_K%C5%99%C3%AD%C5%BE\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Czech basketball player and coach (b. 1924)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>%C5%99%C3%AD%C5%BE\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Czech basketball player and coach (b. 1924)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Miloslav_K%C5%99%C3%AD%C5%BE"}]}, {"year": "2013", "text": "<PERSON>, American singer-songwriter, keyboard player, and producer (b. 1939)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter, keyboard player, and producer (b. 1939)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter, keyboard player, and producer (b. 1939)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2013", "text": "<PERSON><PERSON>, English judge and politician (b. 1923)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, English judge and politician (b. 1923)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, English judge and politician (b. 1923)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "2013", "text": "<PERSON>, American singer-songwriter (b. 1995)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter (b. 1995)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter (b. 1995)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2014", "text": "<PERSON>, American psychologist and academic (b. 1944)", "html": "2014 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American psychologist and academic (b. 1944)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American psychologist and academic (b. 1944)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2014", "text": "<PERSON>, New Zealand rugby player (b. 1934)", "html": "2014 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(rugby_union)\" title=\"<PERSON> (rugby union)\"><PERSON></a>, New Zealand rugby player (b. 1934)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(rugby_union)\" title=\"<PERSON> (rugby union)\"><PERSON></a>, New Zealand rugby player (b. 1934)", "links": [{"title": "<PERSON> (rugby union)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(rugby_union)"}]}, {"year": "2014", "text": "<PERSON><PERSON>, English-French painter (b. 1930)", "html": "2014 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, English-French painter (b. 1930)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, English-French painter (b. 1930)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "2014", "text": "<PERSON>, American journalist, author, and critic (b. 1924)", "html": "2014 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American journalist, author, and critic (b. 1924)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American journalist, author, and critic (b. 1924)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2014", "text": "<PERSON> <PERSON>, Spanish-English businessman (b. 1933)", "html": "2014 - <a href=\"https://wikipedia.org/wiki/Prince_<PERSON>_<PERSON>\" title=\"Prince <PERSON>\">Prince <PERSON></a>, Spanish-English businessman (b. 1933)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Prince_<PERSON>_<PERSON>\" title=\"Prince <PERSON>\">Prince <PERSON></a>, Spanish-English businessman (b. 1933)", "links": [{"title": "Prince <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "2014", "text": "<PERSON>, English actress (b. 1929)", "html": "2014 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actress (b. 1929)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actress (b. 1929)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2015", "text": "<PERSON>, American saxophonist, composer, and producer (b. 1956)", "html": "2015 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American saxophonist, composer, and producer (b. 1956)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American saxophonist, composer, and producer (b. 1956)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2015", "text": "<PERSON><PERSON>, Nigerian actor and playwright (b. 1940)", "html": "2015 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Nigerian actor and playwright (b. 1940)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Nigerian actor and playwright (b. 1940)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "2016", "text": "<PERSON><PERSON>, Malaysian convicted murderer who was executed by hanging in Singapore (b. 1984)", "html": "2016 - <a href=\"https://wikipedia.org/wiki/Kho_Jabing\" title=\"Kho Jabing\"><PERSON><PERSON></a>, Malaysian convicted murderer who was executed by hanging in Singapore (b. 1984)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Kho_Jabing\" title=\"Kho Jabing\"><PERSON><PERSON></a>, Malaysian convicted murderer who was executed by hanging in Singapore (b. 1984)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Kho_Jabing"}]}, {"year": "2019", "text": "<PERSON><PERSON>, Austrian race car driver (b. 1949)", "html": "2019 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Austrian race car driver (b. 1949)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Austrian race car driver (b. 1949)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>i_Lauda"}]}, {"year": "2021", "text": "<PERSON>, American anti-pornography activist (b. 1956)", "html": "2021 - <a href=\"https://wikipedia.org/wiki/<PERSON>_(writer)\" title=\"<PERSON> (writer)\"><PERSON></a>, American anti-pornography activist (b. <a href=\"https://wikipedia.org/wiki/1956\" title=\"1956\">1956</a>)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(writer)\" title=\"<PERSON> (writer)\"><PERSON></a>, American anti-pornography activist (b. <a href=\"https://wikipedia.org/wiki/1956\" title=\"1956\">1956</a>)", "links": [{"title": "<PERSON> (writer)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(writer)"}, {"title": "1956", "link": "https://wikipedia.org/wiki/1956"}]}, {"year": "2022", "text": "<PERSON>, American sportswriter and author (b. 1920)", "html": "2022 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American sportswriter and author (b. 1920)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American sportswriter and author (b. 1920)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2022", "text": "<PERSON>, Filipino actress (b. 1941)", "html": "2022 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Filipino actress (b. 1941)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Filipino actress (b. 1941)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2024", "text": "<PERSON>, American stock trader (b. 1937)", "html": "2024 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American stock trader (b. 1937)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American stock trader (b. 1937)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}]}}