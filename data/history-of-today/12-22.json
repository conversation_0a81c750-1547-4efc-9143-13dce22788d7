{"date": "December 22", "url": "https://wikipedia.org/wiki/December_22", "data": {"Events": [{"year": "69", "text": "<PERSON><PERSON><PERSON><PERSON> is proclaimed Emperor of Rome; his predecessor, <PERSON><PERSON><PERSON><PERSON>, attempts to abdicate but is captured and killed at the Gemonian stairs.", "html": "69 - AD 69 - <a href=\"https://wikipedia.org/wiki/Vespasian\" title=\"Vespasian\">V<PERSON>pasian</a> is proclaimed <a href=\"https://wikipedia.org/wiki/Roman_emperor\" title=\"Roman emperor\">Emperor of Rome</a>; his predecessor, <a href=\"https://wikipedia.org/wiki/Vitellius\" title=\"Vite<PERSON><PERSON>\">V<PERSON><PERSON><PERSON></a>, attempts to abdicate but is captured and killed at the <a href=\"https://wikipedia.org/wiki/Gemonian_stairs\" title=\"Gemonian stairs\">Gemonian stairs</a>.", "no_year_html": "AD 69 - <a href=\"https://wikipedia.org/wiki/Vespasian\" title=\"Vespasian\">Vespasian</a> is proclaimed <a href=\"https://wikipedia.org/wiki/Roman_emperor\" title=\"Roman emperor\">Emperor of Rome</a>; his predecessor, <a href=\"https://wikipedia.org/wiki/Vitellius\" title=\"Vite<PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, attempts to abdicate but is captured and killed at the <a href=\"https://wikipedia.org/wiki/Gemonian_stairs\" title=\"Gemonian stairs\">Gemonian stairs</a>.", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Vespasian"}, {"title": "Roman emperor", "link": "https://wikipedia.org/wiki/Roman_emperor"}, {"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Vitellius"}, {"title": "Gemonian stairs", "link": "https://wikipedia.org/wiki/Gemonian_stairs"}]}, {"year": "401", "text": "<PERSON> <PERSON> is elected, the only pope to succeed his father in the office.", "html": "401 - <a href=\"https://wikipedia.org/wiki/Pope_Innocent_I\" title=\"Pope Innocent I\">Pope <PERSON> I</a> is elected, the only pope to succeed his father in the office.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Pope_Innocent_I\" title=\"Pope Innocent I\">Pope <PERSON> I</a> is elected, the only pope to succeed his father in the office.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_I"}]}, {"year": "856", "text": "Damghan earthquake: An earthquake near the Persian city of Damghan kills an estimated 200,000 people, the sixth deadliest earthquake in recorded history.", "html": "856 - <a href=\"https://wikipedia.org/wiki/856_Damghan_earthquake\" title=\"856 Damghan earthquake\">Damghan earthquake</a>: An earthquake near the Persian city of <a href=\"https://wikipedia.org/wiki/Damghan\" title=\"Damghan\">Damghan</a> kills an estimated 200,000 people, the sixth deadliest earthquake in recorded history.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/856_Damghan_earthquake\" title=\"856 Damghan earthquake\">Damghan earthquake</a>: An earthquake near the Persian city of <a href=\"https://wikipedia.org/wiki/Damghan\" title=\"Damghan\">Damghan</a> kills an estimated 200,000 people, the sixth deadliest earthquake in recorded history.", "links": [{"title": "856 Damghan earthquake", "link": "https://wikipedia.org/wiki/856_Damghan_earthquake"}, {"title": "Damghan", "link": "https://wikipedia.org/wiki/Damghan"}]}, {"year": "880", "text": "Luoyang, eastern capital of the Tang dynasty, is captured by rebel leader <PERSON> during the reign of Emperor <PERSON><PERSON>.", "html": "880 - <a href=\"https://wikipedia.org/wiki/Luoyang\" title=\"Luoyang\">Luoyang</a>, eastern capital of the <a href=\"https://wikipedia.org/wiki/Tang_dynasty\" title=\"Tang dynasty\">Tang dynasty</a>, is captured by rebel leader <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> during the reign of <a href=\"https://wikipedia.org/wiki/Emperor_<PERSON><PERSON>_of_Tang\" title=\"Emperor <PERSON><PERSON> of Tang\">Emperor <PERSON><PERSON></a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Luoyang\" title=\"Luoyang\">Luoyang</a>, eastern capital of the <a href=\"https://wikipedia.org/wiki/Tang_dynasty\" title=\"Tang dynasty\">Tang dynasty</a>, is captured by rebel leader <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> during the reign of <a href=\"https://wikipedia.org/wiki/Emperor_<PERSON><PERSON>_of_Tang\" title=\"Emperor <PERSON><PERSON> of Tang\">Emperor <PERSON><PERSON></a>.", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Luoyang"}, {"title": "Tang dynasty", "link": "https://wikipedia.org/wiki/Tang_dynasty"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Emperor <PERSON><PERSON> of Tang", "link": "https://wikipedia.org/wiki/Emperor_<PERSON><PERSON>_of_Tang"}]}, {"year": "1135", "text": "Three weeks after the death of King <PERSON> of England, <PERSON> of Blois claims the throne and is privately crowned King of England, beginning the English Anarchy.", "html": "1135 - Three weeks after the death of King <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_England\" title=\"<PERSON> I of England\"><PERSON> of England</a>, <a href=\"https://wikipedia.org/wiki/<PERSON>,_King_of_England\" title=\"<PERSON>, King of England\"><PERSON> of Blois</a> claims the throne and is privately crowned <a href=\"https://wikipedia.org/wiki/Monarchy_of_the_United_Kingdom\" title=\"Monarchy of the United Kingdom\">King of England</a>, beginning the <a href=\"https://wikipedia.org/wiki/The_Anarchy\" title=\"The Anarchy\">English Anarchy</a>.", "no_year_html": "Three weeks after the death of King <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_England\" title=\"Henry I of England\"><PERSON> of England</a>, <a href=\"https://wikipedia.org/wiki/<PERSON>,_King_of_England\" title=\"<PERSON>, King of England\"><PERSON> of Blois</a> claims the throne and is privately crowned <a href=\"https://wikipedia.org/wiki/Monarchy_of_the_United_Kingdom\" title=\"Monarchy of the United Kingdom\">King of England</a>, beginning the <a href=\"https://wikipedia.org/wiki/The_Anarchy\" title=\"The Anarchy\">English Anarchy</a>.", "links": [{"title": "<PERSON> of England", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_England"}, {"title": "<PERSON>, King of England", "link": "https://wikipedia.org/wiki/<PERSON>,_King_of_England"}, {"title": "Monarchy of the United Kingdom", "link": "https://wikipedia.org/wiki/Monarchy_of_the_United_Kingdom"}, {"title": "The Anarchy", "link": "https://wikipedia.org/wiki/The_Anarchy"}]}, {"year": "1216", "text": "<PERSON> <PERSON><PERSON> approves the Dominican Order through the papal bull of confirmation Religiosam vitam.", "html": "1216 - <a href=\"https://wikipedia.org/wiki/<PERSON>_Honorius_III\" title=\"Pope Honorius III\">Pope <PERSON><PERSON></a> approves the <a href=\"https://wikipedia.org/wiki/Dominican_Order\" title=\"Dominican Order\">Dominican Order</a> through the papal bull of confirmation <a href=\"https://wikipedia.org/wiki/Religiosam_vitam\" title=\"Religiosam vitam\">Religiosam vitam</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_Honorius_III\" title=\"Pope Honorius III\">Pope <PERSON><PERSON></a> approves the <a href=\"https://wikipedia.org/wiki/Dominican_Order\" title=\"Dominican Order\">Dominican Order</a> through the papal bull of confirmation <a href=\"https://wikipedia.org/wiki/Religiosam_vitam\" title=\"Religiosam vitam\">Religiosam vitam</a>.", "links": [{"title": "<PERSON> <PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>"}, {"title": "Dominican Order", "link": "https://wikipedia.org/wiki/Dominican_Order"}, {"title": "Religiosam vitam", "link": "https://wikipedia.org/wiki/Religiosam_vitam"}]}, {"year": "1489", "text": "The forces of the Catholic Monarchs, <PERSON> and <PERSON>, take control of Almería from the Nasrid ruler of Granada, <PERSON>.", "html": "1489 - The forces of the <a href=\"https://wikipedia.org/wiki/Catholic_Monarchs\" class=\"mw-redirect\" title=\"Catholic Monarchs\">Catholic Monarchs</a>, <a href=\"https://wikipedia.org/wiki/<PERSON>_II_of_Aragon\" title=\"<PERSON> Aragon\"><PERSON></a> and <a href=\"https://wikipedia.org/wiki/<PERSON>_I_of_Castile\" title=\"<PERSON> I of Castile\"><PERSON></a>, take control of <a href=\"https://wikipedia.org/wiki/Almer%C3%ADa\" title=\"Almería\">Almería</a> from the <a href=\"https://wikipedia.org/wiki/Nasrid_dynasty\" title=\"Nasrid dynasty\">Nasrid</a> ruler of <a href=\"https://wikipedia.org/wiki/Emirate_of_Granada\" title=\"Emirate of Granada\">Granada</a>, <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Sultan_of_Granada\" class=\"mw-redirect\" title=\"<PERSON>, Sultan of Granada\"><PERSON></a>.", "no_year_html": "The forces of the <a href=\"https://wikipedia.org/wiki/Catholic_Monarchs\" class=\"mw-redirect\" title=\"Catholic Monarchs\">Catholic Monarchs</a>, <a href=\"https://wikipedia.org/wiki/<PERSON>_II_of_Aragon\" title=\"<PERSON> II <PERSON> Aragon\"><PERSON></a> and <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_Castile\" title=\"<PERSON> of Castile\"><PERSON></a>, take control of <a href=\"https://wikipedia.org/wiki/Almer%C3%ADa\" title=\"Almería\">Almería</a> from the <a href=\"https://wikipedia.org/wiki/Nasrid_dynasty\" title=\"Nasrid dynasty\">Nasrid</a> ruler of <a href=\"https://wikipedia.org/wiki/Emirate_of_Granada\" title=\"Emirate of Granada\">Granada</a>, <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Sultan_of_Granada\" class=\"mw-redirect\" title=\"<PERSON>, Sultan of Granada\"><PERSON></a>.", "links": [{"title": "Catholic Monarchs", "link": "https://wikipedia.org/wiki/Catholic_Monarchs"}, {"title": "<PERSON> of Aragon", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Aragon"}, {"title": "<PERSON> of Castile", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Castile"}, {"title": "Almería", "link": "https://wikipedia.org/wiki/Almer%C3%ADa"}, {"title": "Nasrid dynasty", "link": "https://wikipedia.org/wiki/Nasrid_dynasty"}, {"title": "Emirate of Granada", "link": "https://wikipedia.org/wiki/Emirate_of_Granada"}, {"title": "<PERSON>, Sultan of Granada", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Sultan_of_Granada"}]}, {"year": "1769", "text": "Sino-Burmese War: The war ends with the Qing dynasty withdrawing from Burma forever.", "html": "1769 - <a href=\"https://wikipedia.org/wiki/Sino-Burmese_War_(1765%E2%80%9369)\" class=\"mw-redirect\" title=\"Sino-Burmese War (1765-69)\">Sino-Burmese War</a>: The war ends with the <a href=\"https://wikipedia.org/wiki/Qing_dynasty\" title=\"Qing dynasty\">Qing dynasty</a> withdrawing from Burma forever.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Sino-Burmese_War_(1765%E2%80%9369)\" class=\"mw-redirect\" title=\"Sino-Burmese War (1765-69)\">Sino-Burmese War</a>: The war ends with the <a href=\"https://wikipedia.org/wiki/Qing_dynasty\" title=\"Qing dynasty\">Qing dynasty</a> withdrawing from Burma forever.", "links": [{"title": "Sino-Burmese War (1765-69)", "link": "https://wikipedia.org/wiki/Sino-Burmese_War_(1765%E2%80%9369)"}, {"title": "Qing dynasty", "link": "https://wikipedia.org/wiki/Qing_dynasty"}]}, {"year": "1788", "text": "<PERSON><PERSON><PERSON><PERSON> proclaims himself Emperor <PERSON><PERSON>, in effect abolishing on his own the Lê dynasty.", "html": "1788 - <a href=\"https://wikipedia.org/wiki/Nguy%E1%BB%85n_Hu%E1%BB%87\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a> proclaims himself Emperor <PERSON><PERSON>, in effect abolishing on his own the <a href=\"https://wikipedia.org/wiki/L%C3%AA_dynasty\" title=\"Lê dynasty\">Lê dynasty</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Nguy%E1%BB%85n_Hu%E1%BB%87\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a> proclaims himself Emperor <PERSON><PERSON>, in effect abolishing on his own the <a href=\"https://wikipedia.org/wiki/L%C3%AA_dynasty\" title=\"Lê dynasty\">Lê dynasty</a>.", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Nguy%E1%BB%85n_Hu%E1%BB%87"}, {"title": "Lê dynasty", "link": "https://wikipedia.org/wiki/L%C3%AA_dynasty"}]}, {"year": "1790", "text": "The Turkish fortress of Izmail is stormed and captured by <PERSON> and his Russian armies.", "html": "1790 - The Turkish fortress of <a href=\"https://wikipedia.org/wiki/Izmail\" title=\"Izmail\">Izmail</a> is <a href=\"https://wikipedia.org/wiki/Siege_of_Izmail\" title=\"Siege of Izmail\">stormed and captured</a> by <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> and his Russian armies.", "no_year_html": "The Turkish fortress of <a href=\"https://wikipedia.org/wiki/Izmail\" title=\"Izmail\">Izmail</a> is <a href=\"https://wikipedia.org/wiki/Siege_of_Izmail\" title=\"Siege of Izmail\">stormed and captured</a> by <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> and his Russian armies.", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Izmail"}, {"title": "Siege of Izmail", "link": "https://wikipedia.org/wiki/Siege_of_Izmail"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1807", "text": "The Embargo Act, forbidding trade with all foreign countries, is passed by the U.S. Congress at the urging of President <PERSON>.", "html": "1807 - The <a href=\"https://wikipedia.org/wiki/Embargo_Act_of_1807\" title=\"Embargo Act of 1807\">Embargo Act</a>, forbidding trade with all foreign countries, is passed by the <a href=\"https://wikipedia.org/wiki/United_States_Congress\" title=\"United States Congress\">U.S. Congress</a> at the urging of <a href=\"https://wikipedia.org/wiki/President_of_the_United_States\" title=\"President of the United States\">President</a> <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Embargo_Act_of_1807\" title=\"Embargo Act of 1807\">Embargo Act</a>, forbidding trade with all foreign countries, is passed by the <a href=\"https://wikipedia.org/wiki/United_States_Congress\" title=\"United States Congress\">U.S. Congress</a> at the urging of <a href=\"https://wikipedia.org/wiki/President_of_the_United_States\" title=\"President of the United States\">President</a> <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>.", "links": [{"title": "Embargo Act of 1807", "link": "https://wikipedia.org/wiki/Embargo_Act_of_1807"}, {"title": "United States Congress", "link": "https://wikipedia.org/wiki/United_States_Congress"}, {"title": "President of the United States", "link": "https://wikipedia.org/wiki/President_of_the_United_States"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1808", "text": "<PERSON> conducts and performs in concert at the Theater an der Wien, Vienna, with the premiere of his Fifth Symphony, Sixth Symphony, Fourth Piano Concerto and Choral Fantasy.", "html": "1808 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON> van Beethoven\"><PERSON></a> conducts and performs in <a href=\"https://wikipedia.org/wiki/Beethoven_concert_of_22_December_1808\" title=\"Beethoven concert of 22 December 1808\">concert at the Theater an der Wien</a>, Vienna, with the premiere of his <a href=\"https://wikipedia.org/wiki/Symphony_No._5_(Beethoven)\" title=\"Symphony No. 5 (Beethoven)\">Fifth Symphony</a>, <a href=\"https://wikipedia.org/wiki/Symphony_No._6_(Beethoven)\" title=\"Symphony No. 6 (Beethoven)\">Sixth Symphony</a>, <a href=\"https://wikipedia.org/wiki/Piano_Concerto_No._4_(Beethoven)\" title=\"Piano Concerto No. 4 (Beethoven)\">Fourth Piano Concerto</a> and <i><a href=\"https://wikipedia.org/wiki/Choral_Fantasy_(Beethoven)\" title=\"Choral Fantasy (Beethoven)\">Choral Fantasy</a></i>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON> van Beethoven\"><PERSON></a> conducts and performs in <a href=\"https://wikipedia.org/wiki/Beethoven_concert_of_22_December_1808\" title=\"Beethoven concert of 22 December 1808\">concert at the Theater an der Wien</a>, Vienna, with the premiere of his <a href=\"https://wikipedia.org/wiki/Symphony_No._5_(Beethoven)\" title=\"Symphony No. 5 (Beethoven)\">Fifth Symphony</a>, <a href=\"https://wikipedia.org/wiki/Symphony_No._6_(Beethoven)\" title=\"Symphony No. 6 (Beethoven)\">Sixth Symphony</a>, <a href=\"https://wikipedia.org/wiki/Piano_Concerto_No._4_(Beethoven)\" title=\"Piano Concerto No. 4 (Beethoven)\">Fourth Piano Concerto</a> and <i><a href=\"https://wikipedia.org/wiki/Choral_Fantasy_(Beethoven)\" title=\"Choral Fantasy (Beethoven)\">Choral Fantasy</a></i>.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "Beethoven concert of 22 December 1808", "link": "https://wikipedia.org/wiki/Beethoven_concert_of_22_December_1808"}, {"title": "Symphony No. 5 (<PERSON>)", "link": "https://wikipedia.org/wiki/Symphony_No._5_(<PERSON>)"}, {"title": "Symphony No. 6 (<PERSON>)", "link": "https://wikipedia.org/wiki/Symphony_No._6_(<PERSON>)"}, {"title": "Piano Concerto No. 4 (<PERSON>)", "link": "https://wikipedia.org/wiki/Piano_Concerto_No._4_(<PERSON>)"}, {"title": "Choral Fantasy (Beethoven)", "link": "https://wikipedia.org/wiki/Choral_Fantasy_(<PERSON>)"}]}, {"year": "1851", "text": "India's first freight train is operated in Roorkee, to transport material for the construction of the Ganges Canal.", "html": "1851 - India's first freight train is operated in <a href=\"https://wikipedia.org/wiki/Roorkee\" title=\"Roorkee\">Roorkee</a>, to transport material for the construction of the <a href=\"https://wikipedia.org/wiki/Ganges_Canal\" title=\"Ganges Canal\">Ganges Canal</a>.", "no_year_html": "India's first freight train is operated in <a href=\"https://wikipedia.org/wiki/Roorkee\" title=\"Roorkee\">Roorkee</a>, to transport material for the construction of the <a href=\"https://wikipedia.org/wiki/Ganges_Canal\" title=\"Ganges Canal\">Ganges Canal</a>.", "links": [{"title": "Roorkee", "link": "https://wikipedia.org/wiki/Roorkee"}, {"title": "Ganges Canal", "link": "https://wikipedia.org/wiki/Ganges_Canal"}]}, {"year": "1851", "text": "The Library of Congress in Washington, D.C., burns.", "html": "1851 - The <a href=\"https://wikipedia.org/wiki/Library_of_Congress\" title=\"Library of Congress\">Library of Congress</a> in <a href=\"https://wikipedia.org/wiki/Washington,_D.C.\" title=\"Washington, D.C.\">Washington, D.C.</a>, burns.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Library_of_Congress\" title=\"Library of Congress\">Library of Congress</a> in <a href=\"https://wikipedia.org/wiki/Washington,_D.C.\" title=\"Washington, D.C.\">Washington, D.C.</a>, burns.", "links": [{"title": "Library of Congress", "link": "https://wikipedia.org/wiki/Library_of_Congress"}, {"title": "Washington, D.C.", "link": "https://wikipedia.org/wiki/Washington,_D.C."}]}, {"year": "1864", "text": "American Civil War: Savannah, Georgia, falls to the Union's Army of the Tennessee, and General <PERSON> tells President <PERSON>: \"I beg to present you as a Christmas gift the city of Savannah\".", "html": "1864 - <a href=\"https://wikipedia.org/wiki/American_Civil_War\" title=\"American Civil War\">American Civil War</a>: <a href=\"https://wikipedia.org/wiki/Savannah,_Georgia\" title=\"Savannah, Georgia\">Savannah, Georgia</a>, falls to the <a href=\"https://wikipedia.org/wiki/Union_(American_Civil_War)\" title=\"Union (American Civil War)\">Union's</a> <a href=\"https://wikipedia.org/wiki/Army_of_the_Tennessee\" title=\"Army of the Tennessee\">Army of the Tennessee</a>, and General <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> tells President <a href=\"https://wikipedia.org/wiki/Abraham<PERSON>\" title=\"Abraham Lincoln\"><PERSON></a>: \"I beg to present you as a Christmas gift the city of Savannah\".", "no_year_html": "<a href=\"https://wikipedia.org/wiki/American_Civil_War\" title=\"American Civil War\">American Civil War</a>: <a href=\"https://wikipedia.org/wiki/Savannah,_Georgia\" title=\"Savannah, Georgia\">Savannah, Georgia</a>, falls to the <a href=\"https://wikipedia.org/wiki/Union_(American_Civil_War)\" title=\"Union (American Civil War)\">Union's</a> <a href=\"https://wikipedia.org/wiki/Army_of_the_Tennessee\" title=\"Army of the Tennessee\">Army of the Tennessee</a>, and General <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> tells President <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"Abraham Lincoln\"><PERSON></a>: \"I beg to present you as a Christmas gift the city of Savannah\".", "links": [{"title": "American Civil War", "link": "https://wikipedia.org/wiki/American_Civil_War"}, {"title": "Savannah, Georgia", "link": "https://wikipedia.org/wiki/Savannah,_Georgia"}, {"title": "Union (American Civil War)", "link": "https://wikipedia.org/wiki/Union_(American_Civil_War)"}, {"title": "Army of the Tennessee", "link": "https://wikipedia.org/wiki/Army_of_the_Tennessee"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1885", "text": "<PERSON><PERSON>, a samurai, becomes the first Prime Minister of Japan.", "html": "1885 - <a href=\"https://wikipedia.org/wiki/It%C5%8D_<PERSON><PERSON>bu<PERSON>\" title=\"<PERSON><PERSON>bumi\"><PERSON><PERSON></a>, a <a href=\"https://wikipedia.org/wiki/Samurai\" title=\"Samurai\">samurai</a>, becomes the first <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Japan\" title=\"Prime Minister of Japan\">Prime Minister of Japan</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/It%C5%8D_<PERSON><PERSON>bu<PERSON>\" title=\"<PERSON><PERSON>bu<PERSON>\"><PERSON><PERSON></a>, a <a href=\"https://wikipedia.org/wiki/Samurai\" title=\"Samurai\">samurai</a>, becomes the first <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Japan\" title=\"Prime Minister of Japan\">Prime Minister of Japan</a>.", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/It%C5%8D_<PERSON><PERSON><PERSON><PERSON>"}, {"title": "Samurai", "link": "https://wikipedia.org/wiki/Samurai"}, {"title": "Prime Minister of Japan", "link": "https://wikipedia.org/wiki/Prime_Minister_of_Japan"}]}, {"year": "1888", "text": "The Christmas Meeting of 1888, considered to be the official start of the Faroese independence movement.", "html": "1888 - The <a href=\"https://wikipedia.org/wiki/Christmas_Meeting_of_1888\" title=\"Christmas Meeting of 1888\">Christmas Meeting of 1888</a>, considered to be the official start of the <a href=\"https://wikipedia.org/wiki/Faroese_independence_movement\" title=\"Faroese independence movement\">Faroese independence movement</a>.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Christmas_Meeting_of_1888\" title=\"Christmas Meeting of 1888\">Christmas Meeting of 1888</a>, considered to be the official start of the <a href=\"https://wikipedia.org/wiki/Faroese_independence_movement\" title=\"Faroese independence movement\">Faroese independence movement</a>.", "links": [{"title": "Christmas Meeting of 1888", "link": "https://wikipedia.org/wiki/Christmas_Meeting_of_1888"}, {"title": "Faroese independence movement", "link": "https://wikipedia.org/wiki/Faroese_independence_movement"}]}, {"year": "1890", "text": "Cornwallis Valley Railway begins operation between Kentville and Kingsport, Nova Scotia.", "html": "1890 - <a href=\"https://wikipedia.org/wiki/Cornwallis_Valley_Railway\" title=\"Cornwallis Valley Railway\">Cornwallis Valley Railway</a> begins operation between <a href=\"https://wikipedia.org/wiki/Kentville\" title=\"Kentville\">Kentville</a> and <a href=\"https://wikipedia.org/wiki/Kingsport,_Nova_Scotia\" title=\"Kingsport, Nova Scotia\">Kingsport, Nova Scotia</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Cornwallis_Valley_Railway\" title=\"Cornwallis Valley Railway\">Cornwallis Valley Railway</a> begins operation between <a href=\"https://wikipedia.org/wiki/Kentville\" title=\"Kentville\">Kentville</a> and <a href=\"https://wikipedia.org/wiki/Kingsport,_Nova_Scotia\" title=\"Kingsport, Nova Scotia\">Kingsport, Nova Scotia</a>.", "links": [{"title": "Cornwallis Valley Railway", "link": "https://wikipedia.org/wiki/Cornwallis_Valley_Railway"}, {"title": "Kentville", "link": "https://wikipedia.org/wiki/Kentville"}, {"title": "Kingsport, Nova Scotia", "link": "https://wikipedia.org/wiki/Kingsport,_Nova_Scotia"}]}, {"year": "1891", "text": "Asteroid 323 <PERSON><PERSON><PERSON> becomes the first asteroid discovered using photography.", "html": "1891 - Asteroid <a href=\"https://wikipedia.org/wiki/323_Brucia\" title=\"323 Brucia\">323 <PERSON><PERSON><PERSON></a> becomes the first asteroid discovered using photography.", "no_year_html": "Asteroid <a href=\"https://wikipedia.org/wiki/323_Brucia\" title=\"323 Brucia\">323 <PERSON><PERSON><PERSON></a> becomes the first asteroid discovered using photography.", "links": [{"title": "323 Brucia", "link": "https://wikipedia.org/wiki/323_<PERSON><PERSON><PERSON>"}]}, {"year": "1894", "text": "The <PERSON><PERSON><PERSON><PERSON> affair begins in France, when <PERSON> is wrongly convicted of treason.", "html": "1894 - The <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_affair\" title=\"<PERSON><PERSON><PERSON><PERSON> affair\"><PERSON><PERSON><PERSON><PERSON> affair</a> begins in France, when <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> is wrongly convicted of <a href=\"https://wikipedia.org/wiki/Treason\" title=\"Treason\">treason</a>.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_affair\" title=\"<PERSON><PERSON><PERSON><PERSON> affair\"><PERSON><PERSON><PERSON><PERSON> affair</a> begins in France, when <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> is wrongly convicted of <a href=\"https://wikipedia.org/wiki/Treason\" title=\"Treason\">treason</a>.", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON> affair", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_affair"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Treason"}]}, {"year": "1906", "text": "An Mw  7.9 earthquake strikes Xinjiang, China, killing at least 280.", "html": "1906 - <PERSON><sub>w</sub>  <a href=\"https://wikipedia.org/wiki/1906_Manasi_earthquake\" title=\"1906 Manasi earthquake\">7.9 earthquake strikes Xinjiang</a>, China, killing at least 280.", "no_year_html": "An M<sub>w</sub>  <a href=\"https://wikipedia.org/wiki/1906_Manasi_earthquake\" title=\"1906 Manasi earthquake\">7.9 earthquake strikes Xinjiang</a>, China, killing at least 280.", "links": [{"title": "1906 Manasi earthquake", "link": "https://wikipedia.org/wiki/1906_Manasi_earthquake"}]}, {"year": "1920", "text": "The GOELRO economic development plan is adopted by the 8th Congress of Soviets of the Russian SFSR.", "html": "1920 - The <a href=\"https://wikipedia.org/wiki/GOELRO\" title=\"GOELRO\">GOELRO economic development plan</a> is adopted by the 8th <a href=\"https://wikipedia.org/wiki/All-Russian_Congress_of_Soviets\" title=\"All-Russian Congress of Soviets\">Congress of Soviets of the Russian SFSR</a>.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/GOELRO\" title=\"GOELRO\">GOELRO economic development plan</a> is adopted by the 8th <a href=\"https://wikipedia.org/wiki/All-Russian_Congress_of_Soviets\" title=\"All-Russian Congress of Soviets\">Congress of Soviets of the Russian SFSR</a>.", "links": [{"title": "GOELRO", "link": "https://wikipedia.org/wiki/GOELRO"}, {"title": "All-Russian Congress of Soviets", "link": "https://wikipedia.org/wiki/All-Russian_Congress_of_Soviets"}]}, {"year": "1921", "text": "Opening of Visva-Bharati College, also known as Santiniketan College, now Visva Bharati University, India.", "html": "1921 - Opening of <a href=\"https://wikipedia.org/wiki/Visva-Bharati_College\" class=\"mw-redirect\" title=\"Visva-Bharati College\">Visva-Bharati College</a>, also known as <a href=\"https://wikipedia.org/wiki/Sant<PERSON>ketan\" class=\"mw-redirect\" title=\"Sant<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a> College, now <a href=\"https://wikipedia.org/wiki/Visva-Bharati_University\" title=\"Visva-Bharati University\">Visva Bharati University</a>, India.", "no_year_html": "Opening of <a href=\"https://wikipedia.org/wiki/Visva-Bharati_College\" class=\"mw-redirect\" title=\"Visva-Bharati College\">Visva-Bharati College</a>, also known as <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>ketan\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a> College, now <a href=\"https://wikipedia.org/wiki/Visva-Bharati_University\" title=\"Visva-Bharati University\">Visva Bharati University</a>, India.", "links": [{"title": "Visva-Bharati College", "link": "https://wikipedia.org/wiki/Visva-Bharati_College"}, {"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Santiniketan"}, {"title": "Visva-Bharati University", "link": "https://wikipedia.org/wiki/Visva-Bharati_University"}]}, {"year": "1937", "text": "The Lincoln Tunnel opens to traffic in New York City.", "html": "1937 - The <a href=\"https://wikipedia.org/wiki/Lincoln_Tunnel\" title=\"Lincoln Tunnel\">Lincoln Tunnel</a> opens to traffic in New York City.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Lincoln_Tunnel\" title=\"Lincoln Tunnel\">Lincoln Tunnel</a> opens to traffic in New York City.", "links": [{"title": "Lincoln Tunnel", "link": "https://wikipedia.org/wiki/Lincoln_Tunnel"}]}, {"year": "1939", "text": "Indian Muslims observe a \"Day of Deliverance\" to celebrate the resignations of members of the Indian National Congress over their not having been consulted over the decision to enter World War II with the United Kingdom.", "html": "1939 - <a href=\"https://wikipedia.org/wiki/Islam_in_India\" title=\"Islam in India\">Indian Muslims</a> observe a \"<a href=\"https://wikipedia.org/wiki/Day_of_Deliverance_(India)\" class=\"mw-redirect\" title=\"Day of Deliverance (India)\">Day of Deliverance</a>\" to celebrate the resignations of members of the <a href=\"https://wikipedia.org/wiki/Indian_National_Congress\" title=\"Indian National Congress\">Indian National Congress</a> over their not having been consulted over the decision to enter <a href=\"https://wikipedia.org/wiki/World_War_II\" title=\"World War II\">World War II</a> with the United Kingdom.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Islam_in_India\" title=\"Islam in India\">Indian Muslims</a> observe a \"<a href=\"https://wikipedia.org/wiki/Day_of_Deliverance_(India)\" class=\"mw-redirect\" title=\"Day of Deliverance (India)\">Day of Deliverance</a>\" to celebrate the resignations of members of the <a href=\"https://wikipedia.org/wiki/Indian_National_Congress\" title=\"Indian National Congress\">Indian National Congress</a> over their not having been consulted over the decision to enter <a href=\"https://wikipedia.org/wiki/World_War_II\" title=\"World War II\">World War II</a> with the United Kingdom.", "links": [{"title": "Islam in India", "link": "https://wikipedia.org/wiki/Islam_in_India"}, {"title": "Day of Deliverance (India)", "link": "https://wikipedia.org/wiki/Day_of_Deliverance_(India)"}, {"title": "Indian National Congress", "link": "https://wikipedia.org/wiki/Indian_National_Congress"}, {"title": "World War II", "link": "https://wikipedia.org/wiki/World_War_II"}]}, {"year": "1940", "text": "World War II: <PERSON><PERSON> is captured by the Greek army.", "html": "1940 - World War II: <a href=\"https://wikipedia.org/wiki/Himara\" class=\"mw-redirect\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a> is captured by the Greek army.", "no_year_html": "World War II: <a href=\"https://wikipedia.org/wiki/Himara\" class=\"mw-redirect\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a> is captured by the Greek army.", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Himara"}]}, {"year": "1942", "text": "World War II: <PERSON> signs the order to develop the V-2 rocket as a weapon.", "html": "1942 - World War II: <a href=\"https://wikipedia.org/wiki/Adolf_Hitler\" title=\"Adolf Hitler\"><PERSON></a> signs the order to develop the <a href=\"https://wikipedia.org/wiki/V-2_rocket\" title=\"V-2 rocket\">V-2 rocket</a> as a weapon.", "no_year_html": "World War II: <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Adolf <PERSON>\"><PERSON></a> signs the order to develop the <a href=\"https://wikipedia.org/wiki/V-2_rocket\" title=\"V-2 rocket\">V-2 rocket</a> as a weapon.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "V-2 rocket", "link": "https://wikipedia.org/wiki/V-2_rocket"}]}, {"year": "1944", "text": "World War II: Battle of the Bulge: German troops demand the surrender of United States troops at Bastogne, Belgium, prompting the famous one word reply by General <PERSON>: \"Nuts!\"", "html": "1944 - World War II: <a href=\"https://wikipedia.org/wiki/Battle_of_the_Bulge\" title=\"Battle of the Bulge\">Battle of the Bulge</a>: <a href=\"https://wikipedia.org/wiki/Nazi_Germany\" title=\"Nazi Germany\">German</a> troops demand the surrender of United States troops at <a href=\"https://wikipedia.org/wiki/Bastogne\" title=\"Bastogne\">Bastogne</a>, <a href=\"https://wikipedia.org/wiki/Belgium\" title=\"Belgium\">Belgium</a>, prompting the famous one word reply by General <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>: \"Nuts!\"", "no_year_html": "World War II: <a href=\"https://wikipedia.org/wiki/Battle_of_the_Bulge\" title=\"Battle of the Bulge\">Battle of the Bulge</a>: <a href=\"https://wikipedia.org/wiki/Nazi_Germany\" title=\"Nazi Germany\">German</a> troops demand the surrender of United States troops at <a href=\"https://wikipedia.org/wiki/Bastogne\" title=\"Bastogne\">Bastogne</a>, <a href=\"https://wikipedia.org/wiki/Belgium\" title=\"Belgium\">Belgium</a>, prompting the famous one word reply by General <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>: \"Nuts!\"", "links": [{"title": "Battle of the Bulge", "link": "https://wikipedia.org/wiki/Battle_of_the_Bulge"}, {"title": "Nazi Germany", "link": "https://wikipedia.org/wiki/Nazi_Germany"}, {"title": "Bastogne", "link": "https://wikipedia.org/wiki/Bastogne"}, {"title": "Belgium", "link": "https://wikipedia.org/wiki/Belgium"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1944", "text": "World War II: The People's Army of Vietnam is formed to resist Japanese occupation of Indochina, now Vietnam.", "html": "1944 - World War II: The <a href=\"https://wikipedia.org/wiki/People%27s_Army_of_Vietnam\" title=\"People's Army of Vietnam\">People's Army of Vietnam</a> is formed to resist <a href=\"https://wikipedia.org/wiki/Japanese_occupation_of_French_Indochina\" class=\"mw-redirect\" title=\"Japanese occupation of French Indochina\">Japanese occupation of Indochina</a>, now <a href=\"https://wikipedia.org/wiki/Vietnam\" title=\"Vietnam\">Vietnam</a>.", "no_year_html": "World War II: The <a href=\"https://wikipedia.org/wiki/People%27s_Army_of_Vietnam\" title=\"People's Army of Vietnam\">People's Army of Vietnam</a> is formed to resist <a href=\"https://wikipedia.org/wiki/Japanese_occupation_of_French_Indochina\" class=\"mw-redirect\" title=\"Japanese occupation of French Indochina\">Japanese occupation of Indochina</a>, now <a href=\"https://wikipedia.org/wiki/Vietnam\" title=\"Vietnam\">Vietnam</a>.", "links": [{"title": "People's Army of Vietnam", "link": "https://wikipedia.org/wiki/People%27s_Army_of_Vietnam"}, {"title": "Japanese occupation of French Indochina", "link": "https://wikipedia.org/wiki/Japanese_occupation_of_French_Indochina"}, {"title": "Vietnam", "link": "https://wikipedia.org/wiki/Vietnam"}]}, {"year": "1945", "text": "U.S. President <PERSON> issues an executive order giving World War II refugees precedence in visa applications under U.S. immigration quotas.", "html": "1945 - U.S. President <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> issues an <a href=\"https://wikipedia.org/wiki/Executive_order\" title=\"Executive order\">executive order</a> giving <a href=\"https://wikipedia.org/wiki/World_War_II_evacuation_and_expulsion\" title=\"World War II evacuation and expulsion\">World War II refugees</a> precedence in visa applications under U.S. immigration quotas.", "no_year_html": "U.S. President <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> issues an <a href=\"https://wikipedia.org/wiki/Executive_order\" title=\"Executive order\">executive order</a> giving <a href=\"https://wikipedia.org/wiki/World_War_II_evacuation_and_expulsion\" title=\"World War II evacuation and expulsion\">World War II refugees</a> precedence in visa applications under U.S. immigration quotas.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "Executive order", "link": "https://wikipedia.org/wiki/Executive_order"}, {"title": "World War II evacuation and expulsion", "link": "https://wikipedia.org/wiki/World_War_II_evacuation_and_expulsion"}]}, {"year": "1948", "text": "<PERSON><PERSON><PERSON><PERSON><PERSON> established the Emergency Government of the Republic of Indonesia (Pemerintah Darurat Republik Indonesia, PDRI) in West Sumatra.", "html": "1948 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON><PERSON>_Prawiranegara\" title=\"<PERSON><PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON><PERSON></a> established the <a href=\"https://wikipedia.org/wiki/Emergency_Government_of_the_Republic_of_Indonesia\" title=\"Emergency Government of the Republic of Indonesia\">Emergency Government of the Republic of Indonesia</a> (<i>Pemerintah Darurat Republik Indonesia</i>, PDRI) in <a href=\"https://wikipedia.org/wiki/West_Sumatra\" title=\"West Sumatra\">West Sumatra</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON><PERSON>_Prawiranegara\" title=\"<PERSON><PERSON><PERSON><PERSON><PERSON>ne<PERSON>\"><PERSON><PERSON><PERSON><PERSON><PERSON></a> established the <a href=\"https://wikipedia.org/wiki/Emergency_Government_of_the_Republic_of_Indonesia\" title=\"Emergency Government of the Republic of Indonesia\">Emergency Government of the Republic of Indonesia</a> (<i>Pemerintah Darurat Republik Indonesia</i>, PDRI) in <a href=\"https://wikipedia.org/wiki/West_Sumatra\" title=\"West Sumatra\">West Sumatra</a>.", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON><PERSON>_<PERSON>"}, {"title": "Emergency Government of the Republic of Indonesia", "link": "https://wikipedia.org/wiki/Emergency_Government_of_the_Republic_of_Indonesia"}, {"title": "West Sumatra", "link": "https://wikipedia.org/wiki/West_Sumatra"}]}, {"year": "1963", "text": "The cruise ship Lakonia burns 290 kilometres (180 mi) north of Madeira, Portugal with the loss of 128 lives.", "html": "1963 - The <a href=\"https://wikipedia.org/wiki/Cruise_ship\" title=\"Cruise ship\">cruise ship</a> <i><a href=\"https://wikipedia.org/wiki/TSMS_Lakonia\" title=\"TSMS Lakonia\">Lakonia</a></i> burns 290 kilometres (180 mi) north of <a href=\"https://wikipedia.org/wiki/Madeira\" title=\"Madeira\">Madeira</a>, Portugal with the loss of 128 lives.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Cruise_ship\" title=\"Cruise ship\">cruise ship</a> <i><a href=\"https://wikipedia.org/wiki/TSMS_Lakonia\" title=\"TSMS Lakonia\">Lakonia</a></i> burns 290 kilometres (180 mi) north of <a href=\"https://wikipedia.org/wiki/Madeira\" title=\"Madeira\">Madeira</a>, Portugal with the loss of 128 lives.", "links": [{"title": "Cruise ship", "link": "https://wikipedia.org/wiki/Cruise_ship"}, {"title": "TSMS Lakonia", "link": "https://wikipedia.org/wiki/TSMS_Lakonia"}, {"title": "Madeira", "link": "https://wikipedia.org/wiki/Madeira"}]}, {"year": "1964", "text": "The first test flight of the SR-71 (<PERSON>bird) takes place at Air Force Plant 42 in Palmdale, California, United States.", "html": "1964 - The first test flight of the <a href=\"https://wikipedia.org/wiki/Lockheed_SR-71_Blackbird\" title=\"Lockheed SR-71 Blackbird\">SR-71</a> (Blackbird) takes place at <a href=\"https://wikipedia.org/wiki/United_States_Air_Force_Plant_42\" title=\"United States Air Force Plant 42\">Air Force Plant 42</a> in <a href=\"https://wikipedia.org/wiki/Palmdale,_California\" title=\"Palmdale, California\">Palmdale, California</a>, United States.", "no_year_html": "The first test flight of the <a href=\"https://wikipedia.org/wiki/Lockheed_SR-71_Blackbird\" title=\"Lockheed SR-71 Blackbird\">SR-71</a> (Blackbird) takes place at <a href=\"https://wikipedia.org/wiki/United_States_Air_Force_Plant_42\" title=\"United States Air Force Plant 42\">Air Force Plant 42</a> in <a href=\"https://wikipedia.org/wiki/Palmdale,_California\" title=\"Palmdale, California\">Palmdale, California</a>, United States.", "links": [{"title": "Lockheed SR-71 Blackbird", "link": "https://wikipedia.org/wiki/Lockheed_SR-71_Blackbird"}, {"title": "United States Air Force Plant 42", "link": "https://wikipedia.org/wiki/United_States_Air_Force_Plant_42"}, {"title": "Palmdale, California", "link": "https://wikipedia.org/wiki/Palmdale,_California"}]}, {"year": "1965", "text": "In the United Kingdom, a 70 miles per hour (110 km/h) speed limit is applied to all rural roads including motorways for the first time.", "html": "1965 - In the United Kingdom, a 70 miles per hour (110 km/h) <a href=\"https://wikipedia.org/wiki/Speed_limit\" title=\"Speed limit\">speed limit</a> is applied to all rural roads including <a href=\"https://wikipedia.org/wiki/Controlled-access_highway\" title=\"Controlled-access highway\">motorways</a> for the first time.", "no_year_html": "In the United Kingdom, a 70 miles per hour (110 km/h) <a href=\"https://wikipedia.org/wiki/Speed_limit\" title=\"Speed limit\">speed limit</a> is applied to all rural roads including <a href=\"https://wikipedia.org/wiki/Controlled-access_highway\" title=\"Controlled-access highway\">motorways</a> for the first time.", "links": [{"title": "Speed limit", "link": "https://wikipedia.org/wiki/Speed_limit"}, {"title": "Controlled-access highway", "link": "https://wikipedia.org/wiki/Controlled-access_highway"}]}, {"year": "1968", "text": "Cultural Revolution: People's Daily posted the instructions of <PERSON> that \"The intellectual youth must go to the country, and will be educated from living in rural poverty.\"", "html": "1968 - <a href=\"https://wikipedia.org/wiki/Cultural_Revolution\" title=\"Cultural Revolution\">Cultural Revolution</a>: <i><a href=\"https://wikipedia.org/wiki/People%27s_Daily\" title=\"People's Daily\">People's Daily</a></i> posted the instructions of <a href=\"https://wikipedia.org/wiki/Mao_Z<PERSON>ong\" title=\"Mao Zedong\"><PERSON></a> that \"<a href=\"https://wikipedia.org/wiki/Sent-down_youth\" title=\"Sent-down youth\">The intellectual youth</a> must go to the country, and will be educated from living in rural poverty.\"", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Cultural_Revolution\" title=\"Cultural Revolution\">Cultural Revolution</a>: <i><a href=\"https://wikipedia.org/wiki/People%27s_Daily\" title=\"People's Daily\">People's Daily</a></i> posted the instructions of <a href=\"https://wikipedia.org/wiki/Mao_Z<PERSON>ong\" title=\"Mao Zedong\"><PERSON></a> that \"<a href=\"https://wikipedia.org/wiki/Sent-down_youth\" title=\"Sent-down youth\">The intellectual youth</a> must go to the country, and will be educated from living in rural poverty.\"", "links": [{"title": "Cultural Revolution", "link": "https://wikipedia.org/wiki/Cultural_Revolution"}, {"title": "People's Daily", "link": "https://wikipedia.org/wiki/People%27s_Daily"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Sent-down youth", "link": "https://wikipedia.org/wiki/Sent-down_youth"}]}, {"year": "1971", "text": "The international aid organization Doctors Without Borders is founded by <PERSON> and a group of journalists in Paris, France.", "html": "1971 - The international aid organization <a href=\"https://wikipedia.org/wiki/M%C3%A9decins_Sans_Fronti%C3%A8res\" title=\"Médecins Sans Frontières\">Doctors Without Borders</a> is founded by <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> and a group of journalists in <a href=\"https://wikipedia.org/wiki/Paris\" title=\"Paris\">Paris</a>, <a href=\"https://wikipedia.org/wiki/France\" title=\"France\">France</a>.", "no_year_html": "The international aid organization <a href=\"https://wikipedia.org/wiki/M%C3%A9decins_Sans_Fronti%C3%A8res\" title=\"Médecins Sans Frontières\">Doctors Without Borders</a> is founded by <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> and a group of journalists in <a href=\"https://wikipedia.org/wiki/Paris\" title=\"Paris\">Paris</a>, <a href=\"https://wikipedia.org/wiki/France\" title=\"France\">France</a>.", "links": [{"title": "Médecins Sans Frontières", "link": "https://wikipedia.org/wiki/M%C3%A9decins_Sans_Fronti%C3%A8res"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}, {"title": "Paris", "link": "https://wikipedia.org/wiki/Paris"}, {"title": "France", "link": "https://wikipedia.org/wiki/France"}]}, {"year": "1973", "text": "A Royal Air Maroc Sud Aviation Caravelle crashes near Tangier-Boukhalef Airport in Tangier, Morocco, killing 106.", "html": "1973 - A <a href=\"https://wikipedia.org/wiki/Royal_Air_Maroc\" title=\"Royal Air Maroc\">Royal Air Maroc</a> <a href=\"https://wikipedia.org/wiki/Sud_Aviation_Caravelle\" title=\"Sud Aviation Caravelle\">Sud Aviation Caravelle</a> <a href=\"https://wikipedia.org/wiki/1973_Royal_Air_Maroc_Sud_Aviation_Caravelle_crash\" title=\"1973 Royal Air Maroc Sud Aviation Caravelle crash\">crashes</a> near <a href=\"https://wikipedia.org/wiki/Tangier_Ibn_Battouta_Airport\" title=\"Tangier Ibn Battouta Airport\">Tangier-Boukhalef Airport</a> in <a href=\"https://wikipedia.org/wiki/Tangier\" title=\"Tangier\">Tangier</a>, <a href=\"https://wikipedia.org/wiki/Morocco\" title=\"Morocco\">Morocco</a>, killing 106.", "no_year_html": "A <a href=\"https://wikipedia.org/wiki/Royal_Air_Maroc\" title=\"Royal Air Maroc\">Royal Air Maroc</a> <a href=\"https://wikipedia.org/wiki/Sud_Aviation_Caravelle\" title=\"Sud Aviation Caravelle\">Sud Aviation Caravelle</a> <a href=\"https://wikipedia.org/wiki/1973_Royal_Air_Maroc_Sud_Aviation_Caravelle_crash\" title=\"1973 Royal Air Maroc Sud Aviation Caravelle crash\">crashes</a> near <a href=\"https://wikipedia.org/wiki/Tangier_Ibn_Battouta_Airport\" title=\"Tangier Ibn Battouta Airport\">Tangier-Boukhalef Airport</a> in <a href=\"https://wikipedia.org/wiki/Tangier\" title=\"Tangier\">Tangier</a>, <a href=\"https://wikipedia.org/wiki/Morocco\" title=\"Morocco\">Morocco</a>, killing 106.", "links": [{"title": "Royal Air Maroc", "link": "https://wikipedia.org/wiki/Royal_Air_Maroc"}, {"title": "Sud Aviation Caravelle", "link": "https://wikipedia.org/wiki/Sud_Aviation_Caravelle"}, {"title": "1973 Royal Air Maroc Sud Aviation Caravelle crash", "link": "https://wikipedia.org/wiki/1973_Royal_Air_Maroc_Sud_Aviation_Caravelle_crash"}, {"title": "Tangier Ibn Battouta Airport", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_Ibn_Battouta_Airport"}, {"title": "Tangier", "link": "https://wikipedia.org/wiki/<PERSON>ier"}, {"title": "Morocco", "link": "https://wikipedia.org/wiki/Morocco"}]}, {"year": "1974", "text": "Grande Comore, Anjouan and Moh<PERSON>li vote to become the independent nation of Comoros. Mayotte remains under French administration.", "html": "1974 - <a href=\"https://wikipedia.org/wiki/Grande_Comore\" title=\"Grande Comore\">Grande Comore</a>, <a href=\"https://wikipedia.org/wiki/Anjouan\" title=\"Anjouan\">Anjouan</a> and <a href=\"https://wikipedia.org/wiki/Moh%C3%A9li\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a> <a href=\"https://wikipedia.org/wiki/1974_Comorian_independence_referendum\" title=\"1974 Comorian independence referendum\">vote</a> to become the independent nation of <a href=\"https://wikipedia.org/wiki/Comoros\" title=\"Comoros\">Comoros</a>. <a href=\"https://wikipedia.org/wiki/Mayotte\" title=\"Mayotte\">Mayotte</a> remains under French administration.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Grande_Comore\" title=\"Grande Comore\">Grande Comore</a>, <a href=\"https://wikipedia.org/wiki/Anjouan\" title=\"Anjouan\">Anjouan</a> and <a href=\"https://wikipedia.org/wiki/Moh%C3%A9li\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a> <a href=\"https://wikipedia.org/wiki/1974_Comorian_independence_referendum\" title=\"1974 Comorian independence referendum\">vote</a> to become the independent nation of <a href=\"https://wikipedia.org/wiki/Comoros\" title=\"Comoros\">Comoros</a>. <a href=\"https://wikipedia.org/wiki/Mayotte\" title=\"Mayotte\">Mayotte</a> remains under French administration.", "links": [{"title": "Grande Comore", "link": "https://wikipedia.org/wiki/Grande_Comore"}, {"title": "Anjouan", "link": "https://wikipedia.org/wiki/Anjouan"}, {"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Moh%C3%A9li"}, {"title": "1974 Comorian independence referendum", "link": "https://wikipedia.org/wiki/1974_Comorian_independence_referendum"}, {"title": "Comoros", "link": "https://wikipedia.org/wiki/Comoros"}, {"title": "Mayotte", "link": "https://wikipedia.org/wiki/Mayotte"}]}, {"year": "1974", "text": "The house of former British Prime Minister <PERSON> is attacked by members of the Provisional IRA.", "html": "1974 - The house of former British Prime Minister <a href=\"https://wikipedia.org/wiki/Edward_<PERSON>\" title=\"<PERSON>\"><PERSON></a> is attacked by members of the <a href=\"https://wikipedia.org/wiki/Provisional_Irish_Republican_Army\" title=\"Provisional Irish Republican Army\">Provisional IRA</a>.", "no_year_html": "The house of former British Prime Minister <a href=\"https://wikipedia.org/wiki/Edward_<PERSON>\" title=\"<PERSON>\"><PERSON></a> is attacked by members of the <a href=\"https://wikipedia.org/wiki/Provisional_Irish_Republican_Army\" title=\"Provisional Irish Republican Army\">Provisional IRA</a>.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Provisional Irish Republican Army", "link": "https://wikipedia.org/wiki/Provisional_Irish_Republican_Army"}]}, {"year": "1975", "text": "U.S. President <PERSON> creates the Strategic Petroleum Reserve in response to the 1970s energy crisis.", "html": "1975 - U.S. President <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> creates the <a href=\"https://wikipedia.org/wiki/Strategic_Petroleum_Reserve_(United_States)\" title=\"Strategic Petroleum Reserve (United States)\">Strategic Petroleum Reserve</a> in response to the <a href=\"https://wikipedia.org/wiki/1970s_energy_crisis\" title=\"1970s energy crisis\">1970s energy crisis</a>.", "no_year_html": "U.S. President <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> creates the <a href=\"https://wikipedia.org/wiki/Strategic_Petroleum_Reserve_(United_States)\" title=\"Strategic Petroleum Reserve (United States)\">Strategic Petroleum Reserve</a> in response to the <a href=\"https://wikipedia.org/wiki/1970s_energy_crisis\" title=\"1970s energy crisis\">1970s energy crisis</a>.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Strategic Petroleum Reserve (United States)", "link": "https://wikipedia.org/wiki/Strategic_Petroleum_Reserve_(United_States)"}, {"title": "1970s energy crisis", "link": "https://wikipedia.org/wiki/1970s_energy_crisis"}]}, {"year": "1978", "text": "The pivotal Third Plenum of the 11th National Congress of the Chinese Communist Party is held in Beijing, with <PERSON><PERSON> reversing Mao-era policies to pursue a program for Chinese economic reform.", "html": "1978 - The pivotal Third Plenum of the <a href=\"https://wikipedia.org/wiki/11th_National_Congress_of_the_Chinese_Communist_Party\" title=\"11th National Congress of the Chinese Communist Party\">11th National Congress of the Chinese Communist Party</a> is held in Beijing, with <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a> reversing <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Zedong\">Mao</a>-era policies to pursue a program for <a href=\"https://wikipedia.org/wiki/Chinese_economic_reform\" class=\"mw-redirect\" title=\"Chinese economic reform\">Chinese economic reform</a>.", "no_year_html": "The pivotal Third Plenum of the <a href=\"https://wikipedia.org/wiki/11th_National_Congress_of_the_Chinese_Communist_Party\" title=\"11th National Congress of the Chinese Communist Party\">11th National Congress of the Chinese Communist Party</a> is held in Beijing, with <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a> reversing <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Zedong\">Mao</a>-era policies to pursue a program for <a href=\"https://wikipedia.org/wiki/Chinese_economic_reform\" class=\"mw-redirect\" title=\"Chinese economic reform\">Chinese economic reform</a>.", "links": [{"title": "11th National Congress of the Chinese Communist Party", "link": "https://wikipedia.org/wiki/11th_National_Congress_of_the_Chinese_Communist_Party"}, {"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Den<PERSON>_<PERSON>"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Chinese economic reform", "link": "https://wikipedia.org/wiki/Chinese_economic_reform"}]}, {"year": "1984", "text": "\"Subway vigilante\" <PERSON> shoots four would-be muggers on a 2 express train in Manhattan section of New York, United States.", "html": "1984 - \"Subway vigilante\" <a href=\"https://wikipedia.org/wiki/<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a> shoots four would-be muggers on a <a href=\"https://wikipedia.org/wiki/2_(New_York_City_Subway_service)\" title=\"2 (New York City Subway service)\">2</a> express train in <a href=\"https://wikipedia.org/wiki/Manhattan\" title=\"Manhattan\">Manhattan</a> section of New York, United States.", "no_year_html": "\"Subway vigilante\" <a href=\"https://wikipedia.org/wiki/<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a> shoots four would-be muggers on a <a href=\"https://wikipedia.org/wiki/2_(New_York_City_Subway_service)\" title=\"2 (New York City Subway service)\">2</a> express train in <a href=\"https://wikipedia.org/wiki/Manhattan\" title=\"Manhattan\">Manhattan</a> section of New York, United States.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "2 (New York City Subway service)", "link": "https://wikipedia.org/wiki/2_(New_York_City_Subway_service)"}, {"title": "Manhattan", "link": "https://wikipedia.org/wiki/Manhattan"}]}, {"year": "1987", "text": "In Zimbabwe, the political parties ZANU and ZAPU reach an agreement that ends the violence in the Matabeleland region known as the Gukurahundi.", "html": "1987 - In <a href=\"https://wikipedia.org/wiki/Zimbabwe\" title=\"Zimbabwe\">Zimbabwe</a>, the political parties <a href=\"https://wikipedia.org/wiki/Zimbabwe_African_National_Union\" title=\"Zimbabwe African National Union\">ZANU</a> and <a href=\"https://wikipedia.org/wiki/Zimbabwe_African_People%27s_Union\" title=\"Zimbabwe African People's Union\">ZAPU</a> reach an agreement that ends the violence in the <a href=\"https://wikipedia.org/wiki/Matabeleland\" title=\"Matabeleland\">Matabeleland</a> region known as the <a href=\"https://wikipedia.org/wiki/Gukurahundi\" title=\"Gukurahundi\">Gukurahundi</a>.", "no_year_html": "In <a href=\"https://wikipedia.org/wiki/Zimbabwe\" title=\"Zimbabwe\">Zimbabwe</a>, the political parties <a href=\"https://wikipedia.org/wiki/Zimbabwe_African_National_Union\" title=\"Zimbabwe African National Union\">ZANU</a> and <a href=\"https://wikipedia.org/wiki/Zimbabwe_African_People%27s_Union\" title=\"Zimbabwe African People's Union\">ZAPU</a> reach an agreement that ends the violence in the <a href=\"https://wikipedia.org/wiki/Matabeleland\" title=\"Matabeleland\">Matabeleland</a> region known as the <a href=\"https://wikipedia.org/wiki/Gukurahundi\" title=\"Gukurahundi\">Gukurahundi</a>.", "links": [{"title": "Zimbabwe", "link": "https://wikipedia.org/wiki/Zimbabwe"}, {"title": "Zimbabwe African National Union", "link": "https://wikipedia.org/wiki/Zimbabwe_African_National_Union"}, {"title": "Zimbabwe African People's Union", "link": "https://wikipedia.org/wiki/Zimbabwe_African_People%27s_Union"}, {"title": "Matabeleland", "link": "https://wikipedia.org/wiki/Matabeleland"}, {"title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Gukurahundi"}]}, {"year": "1989", "text": "Romanian Revolution: Communist President of Romania <PERSON><PERSON> is overthrown by <PERSON> after days of bloody confrontations. The deposed dictator and his wife <PERSON> flee Bucharest in a helicopter as protesters erupt in cheers.", "html": "1989 - <a href=\"https://wikipedia.org/wiki/Romanian_Revolution\" class=\"mw-redirect\" title=\"Romanian Revolution\">Romanian Revolution</a>: <a href=\"https://wikipedia.org/wiki/Communism\" title=\"Communism\">Communist</a> <a href=\"https://wikipedia.org/wiki/President_of_Romania\" title=\"President of Romania\">President of Romania</a> <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>%C8%99<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a> is overthrown by <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> after days of bloody confrontations. The deposed dictator and his wife <PERSON> flee <a href=\"https://wikipedia.org/wiki/Bucharest\" title=\"Bucharest\">Bucharest</a> in a helicopter as protesters erupt in cheers.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Romanian_Revolution\" class=\"mw-redirect\" title=\"Romanian Revolution\">Romanian Revolution</a>: <a href=\"https://wikipedia.org/wiki/Communism\" title=\"Communism\">Communist</a> <a href=\"https://wikipedia.org/wiki/President_of_Romania\" title=\"President of Romania\">President of Romania</a> <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>%C8%99<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a> is overthrown by <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> after days of bloody confrontations. The deposed dictator and his wife <PERSON> flee <a href=\"https://wikipedia.org/wiki/Bucharest\" title=\"Bucharest\">Bucharest</a> in a helicopter as protesters erupt in cheers.", "links": [{"title": "Romanian Revolution", "link": "https://wikipedia.org/wiki/Romanian_Revolution"}, {"title": "Communism", "link": "https://wikipedia.org/wiki/Communism"}, {"title": "President of Romania", "link": "https://wikipedia.org/wiki/President_of_Romania"}, {"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>%C8%99escu"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Bucharest", "link": "https://wikipedia.org/wiki/Bucharest"}]}, {"year": "1989", "text": "German reunification: Berlin's Brandenburg Gate re-opens after nearly 30 years, effectively ending the division of East and West Germany.", "html": "1989 - <a href=\"https://wikipedia.org/wiki/German_reunification\" title=\"German reunification\">German reunification</a>: Berlin's <a href=\"https://wikipedia.org/wiki/Brandenburg_Gate\" title=\"Brandenburg Gate\">Brandenburg Gate</a> re-opens after nearly 30 years, effectively ending the division of <a href=\"https://wikipedia.org/wiki/East_Germany\" title=\"East Germany\">East</a> and <a href=\"https://wikipedia.org/wiki/West_Germany\" title=\"West Germany\">West Germany</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/German_reunification\" title=\"German reunification\">German reunification</a>: Berlin's <a href=\"https://wikipedia.org/wiki/Brandenburg_Gate\" title=\"Brandenburg Gate\">Brandenburg Gate</a> re-opens after nearly 30 years, effectively ending the division of <a href=\"https://wikipedia.org/wiki/East_Germany\" title=\"East Germany\">East</a> and <a href=\"https://wikipedia.org/wiki/West_Germany\" title=\"West Germany\">West Germany</a>.", "links": [{"title": "German reunification", "link": "https://wikipedia.org/wiki/German_reunification"}, {"title": "Brandenburg Gate", "link": "https://wikipedia.org/wiki/Brandenburg_Gate"}, {"title": "East Germany", "link": "https://wikipedia.org/wiki/East_Germany"}, {"title": "West Germany", "link": "https://wikipedia.org/wiki/West_Germany"}]}, {"year": "1990", "text": "<PERSON><PERSON> is elected President of Poland.", "html": "1990 - <a href=\"https://wikipedia.org/wiki/Lech_Wa%C5%82%C4%99sa\" title=\"Lech Wałęsa\">Lech Wałę<PERSON></a> is <a href=\"https://wikipedia.org/wiki/1990_Polish_presidential_election\" title=\"1990 Polish presidential election\">elected</a> <a href=\"https://wikipedia.org/wiki/President_of_Poland\" title=\"President of Poland\">President of Poland</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Lech_Wa%C5%82%C4%99sa\" title=\"Lech Wałęsa\">Lech Wałę<PERSON></a> is <a href=\"https://wikipedia.org/wiki/1990_Polish_presidential_election\" title=\"1990 Polish presidential election\">elected</a> <a href=\"https://wikipedia.org/wiki/President_of_Poland\" title=\"President of Poland\">President of Poland</a>.", "links": [{"title": "Lech Wałęsa", "link": "https://wikipedia.org/wiki/Lech_Wa%C5%82%C4%99sa"}, {"title": "1990 Polish presidential election", "link": "https://wikipedia.org/wiki/1990_Polish_presidential_election"}, {"title": "President of Poland", "link": "https://wikipedia.org/wiki/President_of_Poland"}]}, {"year": "1990", "text": "Final independence of Marshall Islands and Federated States of Micronesia after termination of trusteeship.", "html": "1990 - Final independence of <a href=\"https://wikipedia.org/wiki/Marshall_Islands\" title=\"Marshall Islands\">Marshall Islands</a> and <a href=\"https://wikipedia.org/wiki/Federated_States_of_Micronesia\" title=\"Federated States of Micronesia\">Federated States of Micronesia</a> after termination of <a href=\"https://wikipedia.org/wiki/Trust_Territory_of_the_Pacific_Islands\" title=\"Trust Territory of the Pacific Islands\">trusteeship</a>.", "no_year_html": "Final independence of <a href=\"https://wikipedia.org/wiki/Marshall_Islands\" title=\"Marshall Islands\">Marshall Islands</a> and <a href=\"https://wikipedia.org/wiki/Federated_States_of_Micronesia\" title=\"Federated States of Micronesia\">Federated States of Micronesia</a> after termination of <a href=\"https://wikipedia.org/wiki/Trust_Territory_of_the_Pacific_Islands\" title=\"Trust Territory of the Pacific Islands\">trusteeship</a>.", "links": [{"title": "Marshall Islands", "link": "https://wikipedia.org/wiki/Marshall_Islands"}, {"title": "Federated States of Micronesia", "link": "https://wikipedia.org/wiki/Federated_States_of_Micronesia"}, {"title": "Trust Territory of the Pacific Islands", "link": "https://wikipedia.org/wiki/Trust_Territory_of_the_Pacific_Islands"}]}, {"year": "1992", "text": "During approach to Tripoli International Airport, a Boeing 727 operating as Libyan Arab Airlines Flight 1103 collides in mid-air with a Libyan Air Force Mikoyan-Gurevich MiG-23, killing 157 people.", "html": "1992 - During approach to <a href=\"https://wikipedia.org/wiki/Tripoli_International_Airport\" title=\"Tripoli International Airport\">Tripoli International Airport</a>, a <a href=\"https://wikipedia.org/wiki/Boeing_727\" title=\"Boeing 727\">Boeing 727</a> operating as <a href=\"https://wikipedia.org/wiki/Libyan_Arab_Airlines_Flight_1103\" title=\"Libyan Arab Airlines Flight 1103\">Libyan Arab Airlines Flight 1103</a> collides in mid-air with a <a href=\"https://wikipedia.org/wiki/Libyan_Air_Force\" title=\"Libyan Air Force\">Libyan Air Force</a> <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>-<PERSON><PERSON>vich_MiG-23\" title=\"<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> MiG-23\"><PERSON><PERSON><PERSON>-<PERSON><PERSON>vich MiG-23</a>, killing 157 people.", "no_year_html": "During approach to <a href=\"https://wikipedia.org/wiki/Tripoli_International_Airport\" title=\"Tripoli International Airport\">Tripoli International Airport</a>, a <a href=\"https://wikipedia.org/wiki/Boeing_727\" title=\"Boeing 727\">Boeing 727</a> operating as <a href=\"https://wikipedia.org/wiki/Libyan_Arab_Airlines_Flight_1103\" title=\"Libyan Arab Airlines Flight 1103\">Libyan Arab Airlines Flight 1103</a> collides in mid-air with a <a href=\"https://wikipedia.org/wiki/Libyan_Air_Force\" title=\"Libyan Air Force\">Libyan Air Force</a> <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>yan-G<PERSON>vich_MiG-23\" title=\"<PERSON><PERSON><PERSON>-<PERSON><PERSON>vich MiG-23\"><PERSON><PERSON><PERSON>-<PERSON><PERSON>vich MiG-23</a>, killing 157 people.", "links": [{"title": "Tripoli International Airport", "link": "https://wikipedia.org/wiki/Tripoli_International_Airport"}, {"title": "Boeing 727", "link": "https://wikipedia.org/wiki/Boeing_727"}, {"title": "Libyan Arab Airlines Flight 1103", "link": "https://wikipedia.org/wiki/Libyan_Arab_Airlines_Flight_1103"}, {"title": "Libyan Air Force", "link": "https://wikipedia.org/wiki/Libyan_Air_Force"}, {"title": "Mikoyan<PERSON><PERSON><PERSON><PERSON> MiG-23", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>-<PERSON><PERSON><PERSON>_MiG-23"}]}, {"year": "1996", "text": "Airborne Express Flight 827 crashes in Narrows, Virginia, killing all six people on board.", "html": "1996 - <a href=\"https://wikipedia.org/wiki/Airborne_Express_Flight_827\" title=\"Airborne Express Flight 827\">Airborne Express Flight 827</a> crashes in <a href=\"https://wikipedia.org/wiki/Narrows,_Virginia\" title=\"Narrows, Virginia\">Narrows, Virginia</a>, killing all six people on board.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Airborne_Express_Flight_827\" title=\"Airborne Express Flight 827\">Airborne Express Flight 827</a> crashes in <a href=\"https://wikipedia.org/wiki/Narrows,_Virginia\" title=\"Narrows, Virginia\">Narrows, Virginia</a>, killing all six people on board.", "links": [{"title": "Airborne Express Flight 827", "link": "https://wikipedia.org/wiki/Airborne_Express_Flight_827"}, {"title": "Narrows, Virginia", "link": "https://wikipedia.org/wiki/Narrows,_Virginia"}]}, {"year": "1997", "text": "Acteal massacre: Attendees at a prayer meeting of Roman Catholic activists for indigenous causes in the small village of Acteal in the Mexican state of Chiapas are massacred by paramilitary forces.", "html": "1997 - <a href=\"https://wikipedia.org/wiki/Acteal_massacre\" title=\"Acteal massacre\">Acteal massacre</a>: Attendees at a prayer meeting of <a href=\"https://wikipedia.org/wiki/Catholic_Church\" title=\"Catholic Church\">Roman Catholic</a> activists for <a href=\"https://wikipedia.org/wiki/Indigenous_peoples_of_the_Americas\" title=\"Indigenous peoples of the Americas\">indigenous</a> causes in the small village of <a href=\"https://wikipedia.org/wiki/Acteal\" title=\"Acteal\">Acteal</a> in the Mexican state of <a href=\"https://wikipedia.org/wiki/Chiapas\" title=\"Chiapas\">Chiapas</a> are <a href=\"https://wikipedia.orghttps://en.wiktionary.org/wiki/massacre\" class=\"extiw\" title=\"wikt:massacre\">massacred</a> by <a href=\"https://wikipedia.org/wiki/Paramilitary\" title=\"Paramilitary\">paramilitary</a> forces.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Acteal_massacre\" title=\"Acteal massacre\">Acteal massacre</a>: Attendees at a prayer meeting of <a href=\"https://wikipedia.org/wiki/Catholic_Church\" title=\"Catholic Church\">Roman Catholic</a> activists for <a href=\"https://wikipedia.org/wiki/Indigenous_peoples_of_the_Americas\" title=\"Indigenous peoples of the Americas\">indigenous</a> causes in the small village of <a href=\"https://wikipedia.org/wiki/Acteal\" title=\"Acteal\">Acteal</a> in the Mexican state of <a href=\"https://wikipedia.org/wiki/Chiapas\" title=\"Chiapas\">Chiapas</a> are <a href=\"https://wikipedia.orghttps://en.wiktionary.org/wiki/massacre\" class=\"extiw\" title=\"wikt:massacre\">massacred</a> by <a href=\"https://wikipedia.org/wiki/Paramilitary\" title=\"Paramilitary\">paramilitary</a> forces.", "links": [{"title": "Acteal massacre", "link": "https://wikipedia.org/wiki/Acteal_massacre"}, {"title": "Catholic Church", "link": "https://wikipedia.org/wiki/Catholic_Church"}, {"title": "Indigenous peoples of the Americas", "link": "https://wikipedia.org/wiki/Indigenous_peoples_of_the_Americas"}, {"title": "Acteal", "link": "https://wikipedia.org/wiki/Acteal"}, {"title": "Chiapas", "link": "https://wikipedia.org/wiki/Chiapas"}, {"title": "wikt:massacre", "link": "https://wikipedia.orghttps://en.wiktionary.org/wiki/massacre"}, {"title": "Paramilitary", "link": "https://wikipedia.org/wiki/Paramilitary"}]}, {"year": "1997", "text": "Somali Civil War: <PERSON> relinquishes the disputed title of President of Somalia by signing the Cairo Declaration, in Cairo, Egypt. It is the first major step towards reconciliation in Somalia since 1991.", "html": "1997 - <a href=\"https://wikipedia.org/wiki/Somali_Civil_War\" title=\"Somali Civil War\">Somali Civil War</a>: <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> relinquishes the disputed title of <a href=\"https://wikipedia.org/wiki/President_of_Somalia\" title=\"President of Somalia\">President of Somalia</a> by signing the <a href=\"https://wikipedia.org/wiki/Attempts_at_reconciliation_in_Somalia_(1991%E2%80%932004)#1997_Cairo_Peace_Conference_/_Cairo_Declaration\" title=\"Attempts at reconciliation in Somalia (1991-2004)\">Cairo Declaration</a>, in <a href=\"https://wikipedia.org/wiki/Cairo\" title=\"Cairo\">Cairo</a>, <a href=\"https://wikipedia.org/wiki/Egypt\" title=\"Egypt\">Egypt</a>. It is the first major step towards <a href=\"https://wikipedia.org/wiki/Attempts_at_reconciliation_in_Somalia_(1991%E2%80%932004)\" title=\"Attempts at reconciliation in Somalia (1991-2004)\">reconciliation in Somalia</a> since 1991.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Somali_Civil_War\" title=\"Somali Civil War\">Somali Civil War</a>: <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_Aidid\" title=\"<PERSON>id\"><PERSON></a> relinquishes the disputed title of <a href=\"https://wikipedia.org/wiki/President_of_Somalia\" title=\"President of Somalia\">President of Somalia</a> by signing the <a href=\"https://wikipedia.org/wiki/Attempts_at_reconciliation_in_Somalia_(1991%E2%80%932004)#1997_Cairo_Peace_Conference_/_Cairo_Declaration\" title=\"Attempts at reconciliation in Somalia (1991-2004)\">Cairo Declaration</a>, in <a href=\"https://wikipedia.org/wiki/Cairo\" title=\"Cairo\">Cairo</a>, <a href=\"https://wikipedia.org/wiki/Egypt\" title=\"Egypt\">Egypt</a>. It is the first major step towards <a href=\"https://wikipedia.org/wiki/Attempts_at_reconciliation_in_Somalia_(1991%E2%80%932004)\" title=\"Attempts at reconciliation in Somalia (1991-2004)\">reconciliation in Somalia</a> since 1991.", "links": [{"title": "Somali Civil War", "link": "https://wikipedia.org/wiki/Somali_Civil_War"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "President of Somalia", "link": "https://wikipedia.org/wiki/President_of_Somalia"}, {"title": "Attempts at reconciliation in Somalia (1991-2004)", "link": "https://wikipedia.org/wiki/Attempts_at_reconciliation_in_Somalia_(1991%E2%80%932004)#1997_Cairo_Peace_Conference_/_Cairo_Declaration"}, {"title": "Cairo", "link": "https://wikipedia.org/wiki/Cairo"}, {"title": "Egypt", "link": "https://wikipedia.org/wiki/Egypt"}, {"title": "Attempts at reconciliation in Somalia (1991-2004)", "link": "https://wikipedia.org/wiki/Attempts_at_reconciliation_in_Somalia_(1991%E2%80%932004)"}]}, {"year": "1999", "text": "Just after taking off from London Stansted Airport, Korean Air Cargo Flight 8509 crashes into Hatfield Forest near Great Hallingbury, killing all four people on board.", "html": "1999 - Just after taking off from <a href=\"https://wikipedia.org/wiki/London_Stansted_Airport\" title=\"London Stansted Airport\">London Stansted Airport</a>, <a href=\"https://wikipedia.org/wiki/Korean_Air_Cargo_Flight_8509\" title=\"Korean Air Cargo Flight 8509\">Korean Air Cargo Flight 8509</a> crashes into <a href=\"https://wikipedia.org/wiki/Hatfield_Forest\" title=\"Hatfield Forest\">Hatfield Forest</a> near <a href=\"https://wikipedia.org/wiki/Great_Hallingbury\" title=\"Great Hallingbury\">Great Hallingbury</a>, killing all four people on board.", "no_year_html": "Just after taking off from <a href=\"https://wikipedia.org/wiki/London_Stansted_Airport\" title=\"London Stansted Airport\">London Stansted Airport</a>, <a href=\"https://wikipedia.org/wiki/Korean_Air_Cargo_Flight_8509\" title=\"Korean Air Cargo Flight 8509\">Korean Air Cargo Flight 8509</a> crashes into <a href=\"https://wikipedia.org/wiki/Hatfield_Forest\" title=\"Hatfield Forest\">Hatfield Forest</a> near <a href=\"https://wikipedia.org/wiki/Great_Hallingbury\" title=\"Great Hallingbury\">Great Hallingbury</a>, killing all four people on board.", "links": [{"title": "London Stansted Airport", "link": "https://wikipedia.org/wiki/London_Stansted_Airport"}, {"title": "Korean Air Cargo Flight 8509", "link": "https://wikipedia.org/wiki/Korean_Air_Cargo_Flight_8509"}, {"title": "Hatfield Forest", "link": "https://wikipedia.org/wiki/Hatfield_Forest"}, {"title": "Great Hallingbury", "link": "https://wikipedia.org/wiki/Great_Hallingbury"}]}, {"year": "2001", "text": "<PERSON><PERSON><PERSON><PERSON>, political leader of the Northern Alliance, hands over power in Islamic State of Afghanistan to the interim government headed by President <PERSON><PERSON>.", "html": "2001 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, political leader of the <a href=\"https://wikipedia.org/wiki/Northern_Alliance\" title=\"Northern Alliance\">Northern Alliance</a>, hands over power in <a href=\"https://wikipedia.org/wiki/Islamic_State_of_Afghanistan\" title=\"Islamic State of Afghanistan\">Islamic State of Afghanistan</a> to the interim government headed by <a href=\"https://wikipedia.org/wiki/President_of_Afghanistan\" title=\"President of Afghanistan\">President</a> <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, political leader of the <a href=\"https://wikipedia.org/wiki/Northern_Alliance\" title=\"Northern Alliance\">Northern Alliance</a>, hands over power in <a href=\"https://wikipedia.org/wiki/Islamic_State_of_Afghanistan\" title=\"Islamic State of Afghanistan\">Islamic State of Afghanistan</a> to the interim government headed by <a href=\"https://wikipedia.org/wiki/President_of_Afghanistan\" title=\"President of Afghanistan\">President</a> <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>.", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>"}, {"title": "Northern Alliance", "link": "https://wikipedia.org/wiki/Northern_Alliance"}, {"title": "Islamic State of Afghanistan", "link": "https://wikipedia.org/wiki/Islamic_State_of_Afghanistan"}, {"title": "President of Afghanistan", "link": "https://wikipedia.org/wiki/President_of_Afghanistan"}, {"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "2001", "text": "<PERSON> attempts to destroy a passenger airliner by igniting explosives hidden in his shoes aboard American Airlines Flight 63.", "html": "2001 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> attempts to destroy a passenger airliner by igniting explosives hidden in his shoes aboard <a href=\"https://wikipedia.org/wiki/2001_failed_shoe_bomb_attempt\" class=\"mw-redirect\" title=\"2001 failed shoe bomb attempt\">American Airlines Flight 63</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> attempts to destroy a passenger airliner by igniting explosives hidden in his shoes aboard <a href=\"https://wikipedia.org/wiki/2001_failed_shoe_bomb_attempt\" class=\"mw-redirect\" title=\"2001 failed shoe bomb attempt\">American Airlines Flight 63</a>.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "2001 failed shoe bomb attempt", "link": "https://wikipedia.org/wiki/2001_failed_shoe_bomb_attempt"}]}, {"year": "2008", "text": "An ash dike ruptured at a solid waste containment area for a Tennessee Valley Authority coal-fired power plant in Roane County, Tennessee, releasing 4.2 million m3 (1.1 billion US gal) of coal fly ash slurry in the largest industrial spill in U.S. history.", "html": "2008 - An ash dike <a href=\"https://wikipedia.org/wiki/Kingston_Fossil_Plant_coal_fly_ash_slurry_spill\" title=\"Kingston Fossil Plant coal fly ash slurry spill\">ruptured</a> at a solid waste containment area for a <a href=\"https://wikipedia.org/wiki/Tennessee_Valley_Authority\" title=\"Tennessee Valley Authority\">Tennessee Valley Authority</a> <a href=\"https://wikipedia.org/wiki/Kingston_Fossil_Plant\" title=\"Kingston Fossil Plant\">coal-fired power plant</a> in <a href=\"https://wikipedia.org/wiki/Roane_County,_Tennessee\" title=\"Roane County, Tennessee\">Roane County, Tennessee</a>, releasing 4.2 million m (1.1 billion US gal) of <a href=\"https://wikipedia.org/wiki/Coal\" title=\"Coal\">coal</a> <a href=\"https://wikipedia.org/wiki/Fly_ash\" class=\"mw-redirect\" title=\"Fly ash\">fly ash</a> <a href=\"https://wikipedia.org/wiki/Slurry\" title=\"Slurry\">slurry</a> in the largest industrial spill in U.S. history.", "no_year_html": "An ash dike <a href=\"https://wikipedia.org/wiki/Kingston_Fossil_Plant_coal_fly_ash_slurry_spill\" title=\"Kingston Fossil Plant coal fly ash slurry spill\">ruptured</a> at a solid waste containment area for a <a href=\"https://wikipedia.org/wiki/Tennessee_Valley_Authority\" title=\"Tennessee Valley Authority\">Tennessee Valley Authority</a> <a href=\"https://wikipedia.org/wiki/Kingston_Fossil_Plant\" title=\"Kingston Fossil Plant\">coal-fired power plant</a> in <a href=\"https://wikipedia.org/wiki/Roane_County,_Tennessee\" title=\"Roane County, Tennessee\">Roane County, Tennessee</a>, releasing 4.2 million m (1.1 billion US gal) of <a href=\"https://wikipedia.org/wiki/Coal\" title=\"Coal\">coal</a> <a href=\"https://wikipedia.org/wiki/Fly_ash\" class=\"mw-redirect\" title=\"Fly ash\">fly ash</a> <a href=\"https://wikipedia.org/wiki/Slurry\" title=\"Slurry\">slurry</a> in the largest industrial spill in U.S. history.", "links": [{"title": "Kingston Fossil Plant coal fly ash slurry spill", "link": "https://wikipedia.org/wiki/Kingston_Fossil_Plant_coal_fly_ash_slurry_spill"}, {"title": "Tennessee Valley Authority", "link": "https://wikipedia.org/wiki/Tennessee_Valley_Authority"}, {"title": "Kingston Fossil Plant", "link": "https://wikipedia.org/wiki/Kingston_Fossil_Plant"}, {"title": "Roane County, Tennessee", "link": "https://wikipedia.org/wiki/Roane_County,_Tennessee"}, {"title": "Coal", "link": "https://wikipedia.org/wiki/Coal"}, {"title": "Fly ash", "link": "https://wikipedia.org/wiki/Fly_ash"}, {"title": "Slurry", "link": "https://wikipedia.org/wiki/Slurry"}]}, {"year": "2010", "text": "The repeal of the Don't ask, don't tell policy, the 17-year-old policy banning homosexuals serving openly in the United States military, is signed into law by President <PERSON>.", "html": "2010 - The <a href=\"https://wikipedia.org/wiki/Don%27t_Ask,_<PERSON>%27t_Tell_Repeal_Act_of_2010\" title=\"Don't Ask, Don't Tell Repeal Act of 2010\">repeal</a> of the <a href=\"https://wikipedia.org/wiki/Don%27t_ask,_don%27t_tell\" title=\"Don't ask, don't tell\">Don't ask, don't tell</a> policy, the 17-year-old policy banning homosexuals serving openly in the United States military, is signed into law by President <a href=\"https://wikipedia.org/wiki/Barack_Obama\" title=\"<PERSON>\"><PERSON></a>.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Don%27t_Ask,_<PERSON>%27t_Tell_Repeal_Act_of_2010\" title=\"Don't Ask, Don't Tell Repeal Act of 2010\">repeal</a> of the <a href=\"https://wikipedia.org/wiki/Don%27t_ask,_don%27t_tell\" title=\"Don't ask, don't tell\">Don't ask, don't tell</a> policy, the 17-year-old policy banning homosexuals serving openly in the United States military, is signed into law by President <a href=\"https://wikipedia.org/wiki/<PERSON>_Obama\" title=\"<PERSON>\"><PERSON></a>.", "links": [{"title": "Don't Ask, Don't Tell Repeal Act of 2010", "link": "https://wikipedia.org/wiki/Don%27t_Ask,_<PERSON>%27t_Tell_Repeal_Act_of_2010"}, {"title": "Don't ask, don't tell", "link": "https://wikipedia.org/wiki/Don%27t_ask,_don%27t_tell"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Barack<PERSON>Obama"}]}, {"year": "2012", "text": "<PERSON><PERSON><PERSON> of Awami National Party and eight others are killed in a Pakistan Taliban bomber suicide attack in Dhaki Nalbandi area near Qissa Khwani Bazaar.", "html": "2012 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a> of <a href=\"https://wikipedia.org/wiki/Awami_National_Party\" title=\"Awami National Party\">Awami National Party</a> and eight others are killed in a <a href=\"https://wikipedia.org/wiki/Pakistan\" title=\"Pakistan\">Pakistan</a> <a href=\"https://wikipedia.org/wiki/Taliban\" title=\"Taliban\">Taliban</a> bomber suicide attack in Dhaki Nalbandi area near Qissa Khwani Bazaar.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a> of <a href=\"https://wikipedia.org/wiki/Awami_National_Party\" title=\"Awami National Party\">Awami National Party</a> and eight others are killed in a <a href=\"https://wikipedia.org/wiki/Pakistan\" title=\"Pakistan\">Pakistan</a> <a href=\"https://wikipedia.org/wiki/Taliban\" title=\"Taliban\">Taliban</a> bomber suicide attack in Dhaki Nalbandi area near Qissa Khwani Bazaar.", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>"}, {"title": "Awami National Party", "link": "https://wikipedia.org/wiki/Awami_National_Party"}, {"title": "Pakistan", "link": "https://wikipedia.org/wiki/Pakistan"}, {"title": "Taliban", "link": "https://wikipedia.org/wiki/Taliban"}]}, {"year": "2016", "text": "A study finds the VSV-EBOV vaccine against the Ebola virus between 70 and 100% effective, thus making it the first proven vaccine against the disease.", "html": "2016 - A study finds the <a href=\"https://wikipedia.org/wiki/VSV-EBOV\" class=\"mw-redirect\" title=\"VSV-EBOV\">VSV-EBOV</a> vaccine against the <a href=\"https://wikipedia.org/wiki/Ebola_virus\" class=\"mw-redirect\" title=\"Ebola virus\">Ebola virus</a> between 70 and 100% effective, thus making it the first proven vaccine against the disease.", "no_year_html": "A study finds the <a href=\"https://wikipedia.org/wiki/VSV-EBOV\" class=\"mw-redirect\" title=\"VSV-EBOV\">VSV-EBOV</a> vaccine against the <a href=\"https://wikipedia.org/wiki/Ebola_virus\" class=\"mw-redirect\" title=\"Ebola virus\">Ebola virus</a> between 70 and 100% effective, thus making it the first proven vaccine against the disease.", "links": [{"title": "VSV-EBOV", "link": "https://wikipedia.org/wiki/VSV-EBOV"}, {"title": "Ebola virus", "link": "https://wikipedia.org/wiki/Ebola_virus"}]}, {"year": "2017", "text": "United Nations Security Council Resolution 2397 against North Korea is unanimously approved.", "html": "2017 - <a href=\"https://wikipedia.org/wiki/United_Nations_Security_Council_Resolution_2397\" title=\"United Nations Security Council Resolution 2397\">United Nations Security Council Resolution 2397</a> against North Korea is unanimously approved.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/United_Nations_Security_Council_Resolution_2397\" title=\"United Nations Security Council Resolution 2397\">United Nations Security Council Resolution 2397</a> against North Korea is unanimously approved.", "links": [{"title": "United Nations Security Council Resolution 2397", "link": "https://wikipedia.org/wiki/United_Nations_Security_Council_Resolution_2397"}]}, {"year": "2017", "text": "President <PERSON> signs the Tax Cuts and Jobs Act of 2017.", "html": "2017 - President <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> signs the <a href=\"https://wikipedia.org/wiki/Tax_Cuts_and_Jobs_Act_of_2017\" class=\"mw-redirect\" title=\"Tax Cuts and Jobs Act of 2017\">Tax Cuts and Jobs Act of 2017</a>.", "no_year_html": "President <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> signs the <a href=\"https://wikipedia.org/wiki/Tax_Cuts_and_Jobs_Act_of_2017\" class=\"mw-redirect\" title=\"Tax Cuts and Jobs Act of 2017\">Tax Cuts and Jobs Act of 2017</a>.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}, {"title": "Tax Cuts and Jobs Act of 2017", "link": "https://wikipedia.org/wiki/Tax_Cuts_and_Jobs_Act_of_2017"}]}, {"year": "2018", "text": "A tsunami caused by an eruption of Anak Krakatau in Indonesia kills at least 430 people and injures almost a thousand more.", "html": "2018 - A <a href=\"https://wikipedia.org/wiki/2018_Sunda_Strait_tsunami\" title=\"2018 Sunda Strait tsunami\">tsunami</a> caused by an eruption of <a href=\"https://wikipedia.org/wiki/Anak_Krakatau\" class=\"mw-redirect\" title=\"Anak Krakata<PERSON>\"><PERSON><PERSON></a> in Indonesia kills at least 430 people and injures almost a thousand more.", "no_year_html": "A <a href=\"https://wikipedia.org/wiki/2018_Sunda_Strait_tsunami\" title=\"2018 Sunda Strait tsunami\">tsunami</a> caused by an eruption of <a href=\"https://wikipedia.org/wiki/Anak_Krakatau\" class=\"mw-redirect\" title=\"Anak Krakatau\"><PERSON><PERSON></a> in Indonesia kills at least 430 people and injures almost a thousand more.", "links": [{"title": "2018 Sunda Strait tsunami", "link": "https://wikipedia.org/wiki/2018_Sunda_Strait_tsunami"}, {"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Anak_K<PERSON>atau"}]}, {"year": "2018", "text": "The 2018-2019 United States federal government shutdown, the longest shutdown of the U.S. federal government in history, begins.", "html": "2018 - The <a href=\"https://wikipedia.org/wiki/2018%E2%80%932019_United_States_federal_government_shutdown\" title=\"2018-2019 United States federal government shutdown\">2018-2019 United States federal government shutdown</a>, the longest <a href=\"https://wikipedia.org/wiki/Government_shutdowns_in_the_United_States\" title=\"Government shutdowns in the United States\">shutdown</a> of the <a href=\"https://wikipedia.org/wiki/Federal_government_of_the_United_States\" title=\"Federal government of the United States\">U.S. federal government</a> in history, begins.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/2018%E2%80%932019_United_States_federal_government_shutdown\" title=\"2018-2019 United States federal government shutdown\">2018-2019 United States federal government shutdown</a>, the longest <a href=\"https://wikipedia.org/wiki/Government_shutdowns_in_the_United_States\" title=\"Government shutdowns in the United States\">shutdown</a> of the <a href=\"https://wikipedia.org/wiki/Federal_government_of_the_United_States\" title=\"Federal government of the United States\">U.S. federal government</a> in history, begins.", "links": [{"title": "2018-2019 United States federal government shutdown", "link": "https://wikipedia.org/wiki/2018%E2%80%932019_United_States_federal_government_shutdown"}, {"title": "Government shutdowns in the United States", "link": "https://wikipedia.org/wiki/Government_shutdowns_in_the_United_States"}, {"title": "Federal government of the United States", "link": "https://wikipedia.org/wiki/Federal_government_of_the_United_States"}]}], "Births": [{"year": "244", "text": "<PERSON><PERSON><PERSON><PERSON>, Roman emperor (d. 311)", "html": "244 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>ian\"><PERSON><PERSON><PERSON><PERSON></a>, Roman emperor (d. 311)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>ian\"><PERSON><PERSON><PERSON><PERSON></a>, Roman emperor (d. 311)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>ian"}]}, {"year": "948", "text": "<PERSON>, Korean official and general (d. 1031)", "html": "948 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>-ch%27an\" title=\"<PERSON>'an\"><PERSON>an</a>, Korean official and general (d. 1031)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>-<PERSON>%27an\" title=\"<PERSON>an\"><PERSON>'an</a>, Korean official and general (d. 1031)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>-ch%27an"}]}, {"year": "1095", "text": "<PERSON> of Sicily (d. 1154)", "html": "1095 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Sicily\" title=\"<PERSON> of Sicily\"><PERSON> of Sicily</a> (d. 1154)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Sicily\" title=\"<PERSON> of Sicily\"><PERSON> of Sicily</a> (d. 1154)", "links": [{"title": "<PERSON> of Sicily", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Sicily"}]}, {"year": "1178", "text": "Emperor <PERSON><PERSON><PERSON> of Japan (d. 1185)", "html": "1178 - <a href=\"https://wikipedia.org/wiki/Emperor_<PERSON><PERSON><PERSON>\" title=\"Emperor <PERSON><PERSON><PERSON>\">Emperor <PERSON><PERSON><PERSON></a> of Japan (d. 1185)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Emperor_<PERSON><PERSON><PERSON>\" title=\"Emperor <PERSON><PERSON><PERSON>\">Emperor <PERSON><PERSON><PERSON></a> of Japan (d. 1185)", "links": [{"title": "Emperor <PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON><PERSON>"}]}, {"year": "1183", "text": "<PERSON><PERSON><PERSON>, Mongol ruler (d. 1242)", "html": "1183 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON> Khan\"><PERSON><PERSON><PERSON></a>, Mongol ruler (d. 1242)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON> Khan\"><PERSON><PERSON><PERSON></a>, Mongol ruler (d. 1242)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1300", "text": "<PERSON><PERSON><PERSON><PERSON><PERSON>, Mongolian emperor (d. 1329)", "html": "1300 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON><PERSON>_<PERSON>_Kusala\" title=\"<PERSON><PERSON><PERSON><PERSON><PERSON> Kusala\"><PERSON><PERSON><PERSON><PERSON><PERSON></a>, Mongolian emperor (d. 1329)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON><PERSON>_<PERSON>_<PERSON>sala\" title=\"<PERSON><PERSON><PERSON><PERSON><PERSON> Kusala\"><PERSON><PERSON><PERSON><PERSON><PERSON></a>, Mongolian emperor (d. 1329)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON><PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1459", "text": "<PERSON>, Ottoman politician (d. 1495)", "html": "1459 - <a href=\"https://wikipedia.org/wiki/Sultan_<PERSON>\" class=\"mw-redirect\" title=\"Sultan <PERSON>\">Sultan <PERSON></a>, Ottoman politician (d. 1495)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Sultan_<PERSON>\" class=\"mw-redirect\" title=\"Sultan <PERSON>\">Sultan <PERSON></a>, Ottoman politician (d. 1495)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Sultan_<PERSON>"}]}, {"year": "1546", "text": "<PERSON><PERSON><PERSON>, Japanese daimyō (d. 1604)", "html": "1546 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese daimyō (d. 1604)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese daimyō (d. 1604)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON><PERSON>"}]}, {"year": "1550", "text": "<PERSON><PERSON><PERSON>, Italian philosopher and author (d. 1631)", "html": "1550 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_(philosopher)\" title=\"<PERSON><PERSON><PERSON> (philosopher)\"><PERSON><PERSON><PERSON></a>, Italian philosopher and author (d. 1631)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_(philosopher)\" title=\"<PERSON><PERSON><PERSON> (philosopher)\"><PERSON><PERSON><PERSON></a>, Italian philosopher and author (d. 1631)", "links": [{"title": "<PERSON><PERSON><PERSON> (philosopher)", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON><PERSON><PERSON>_(philosopher)"}]}, {"year": "1569", "text": "<PERSON>, French architect (d. 1641)", "html": "1569 - <a href=\"https://wikipedia.org/wiki/%C3%89tienne_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French architect (d. 1641)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/%C3%89tien<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French architect (d. 1641)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/%C3%89tienne_Martellange"}]}, {"year": "1591", "text": "<PERSON><PERSON><PERSON>, Maltese architect and sculptor (d. 1666)", "html": "1591 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Maltese architect and sculptor (d. 1666)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Maltese architect and sculptor (d. 1666)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1639", "text": "<PERSON>, French poet and playwright (d. 1699)", "html": "1639 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French poet and playwright (d. 1699)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French poet and playwright (d. 1699)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1666", "text": "<PERSON><PERSON>, Indian guru and poet (d. 1708)", "html": "1666 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON>\">Guru <PERSON><PERSON></a>, Indian guru and poet (d. 1708)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\">Guru <PERSON></a>, Indian guru and poet (d. 1708)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1694", "text": "<PERSON>, German philosopher and academic (d. 1768)", "html": "1694 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German philosopher and academic (d. 1768)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German philosopher and academic (d. 1768)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1696", "text": "<PERSON>, English general and politician, 1st Colonial Governor of Georgia (d. 1785)", "html": "1696 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English general and politician, 1st <a href=\"https://wikipedia.org/wiki/List_of_colonial_governors_of_Georgia\" title=\"List of colonial governors of Georgia\">Colonial Governor of Georgia</a> (d. 1785)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English general and politician, 1st <a href=\"https://wikipedia.org/wiki/List_of_colonial_governors_of_Georgia\" title=\"List of colonial governors of Georgia\">Colonial Governor of Georgia</a> (d. 1785)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "List of colonial governors of Georgia", "link": "https://wikipedia.org/wiki/List_of_colonial_governors_of_Georgia"}]}, {"year": "1723", "text": "<PERSON>, German viol player and composer (d. 1787)", "html": "1723 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German <a href=\"https://wikipedia.org/wiki/Viol\" title=\"Viol\">viol</a> player and composer (d. 1787)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German <a href=\"https://wikipedia.org/wiki/Viol\" title=\"Viol\">viol</a> player and composer (d. 1787)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "Viol", "link": "https://wikipedia.org/wiki/Viol"}]}, {"year": "1765", "text": "<PERSON>, German mathematician and academic (d. 1825)", "html": "1765 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German mathematician and academic (d. 1825)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German mathematician and academic (d. 1825)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1799", "text": "<PERSON>, Irish priest and physicist (d. 1864)", "html": "1799 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Irish priest and physicist (d. 1864)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Irish priest and physicist (d. 1864)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1805", "text": "<PERSON>, English entomologist and archaeologist (d. 1893)", "html": "1805 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>bad<PERSON>\"><PERSON></a>, English entomologist and archaeologist (d. 1893)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"John <PERSON>\"><PERSON></a>, English entomologist and archaeologist (d. 1893)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "1807", "text": "<PERSON>, Norwegian author, poet, and critic (d. 1873)", "html": "1807 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Norwegian author, poet, and critic (d. 1873)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Norwegian author, poet, and critic (d. 1873)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1819", "text": "<PERSON>, German composer and conductor (d. 1870)", "html": "1819 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German composer and conductor (d. 1870)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German composer and conductor (d. 1870)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1819", "text": "<PERSON>, French mathematician and academic (d. 1892)", "html": "1819 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French mathematician and academic (d. 1892)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French mathematician and academic (d. 1892)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1839", "text": "<PERSON>, English magician (d. 1917)", "html": "1839 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English magician (d. 1917)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English magician (d. 1917)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1850", "text": "<PERSON><PERSON>, Mexican general and politician, 35th President of Mexico (d. 1916)", "html": "1850 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Mexican general and politician, 35th <a href=\"https://wikipedia.org/wiki/President_of_Mexico\" title=\"President of Mexico\">President of Mexico</a> (d. 1916)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Mexican general and politician, 35th <a href=\"https://wikipedia.org/wiki/President_of_Mexico\" title=\"President of Mexico\">President of Mexico</a> (d. 1916)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}, {"title": "President of Mexico", "link": "https://wikipedia.org/wiki/President_of_Mexico"}]}, {"year": "1853", "text": "<PERSON>, Venezuelan-American singer-songwriter and pianist (d. 1917)", "html": "1853 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%B1o\" title=\"<PERSON>\"><PERSON></a>, Venezuelan-American singer-songwriter and pianist (d. 1917)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%B1o\" title=\"<PERSON>\"><PERSON></a>, Venezuelan-American singer-songwriter and pianist (d. 1917)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%B1o"}]}, {"year": "1853", "text": "<PERSON><PERSON><PERSON><PERSON>, Russian mathematician, crystallographer, and mineralogist (d. 1919)", "html": "1853 - <a href=\"https://wikipedia.org/wiki/Evgra<PERSON>_<PERSON>\" title=\"Evgra<PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Russian mathematician, crystallographer, and mineralogist (d. 1919)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Evgra<PERSON>_<PERSON>\" title=\"Evgra<PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Russian mathematician, crystallographer, and mineralogist (d. 1919)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Evgra<PERSON>_<PERSON>"}]}, {"year": "1853", "text": "<PERSON><PERSON>, Indian mystic and philosopher (d. 1920)", "html": "1853 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_Devi\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Indian mystic and philosopher (d. 1920)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Sara<PERSON>_Devi\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Indian mystic and philosopher (d. 1920)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Sarada_Devi"}]}, {"year": "1856", "text": "<PERSON>, American lawyer and politician, 45th United States Secretary of State, Nobel Prize laureate (d. 1937)", "html": "1856 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and politician, 45th <a href=\"https://wikipedia.org/wiki/United_States_Secretary_of_State\" title=\"United States Secretary of State\">United States Secretary of State</a>, <a href=\"https://wikipedia.org/wiki/Nobel_Peace_Prize\" title=\"Nobel Peace Prize\">Nobel Prize</a> laureate (d. 1937)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and politician, 45th <a href=\"https://wikipedia.org/wiki/United_States_Secretary_of_State\" title=\"United States Secretary of State\">United States Secretary of State</a>, <a href=\"https://wikipedia.org/wiki/Nobel_Peace_Prize\" title=\"Nobel Peace Prize\">Nobel Prize</a> laureate (d. 1937)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>"}, {"title": "United States Secretary of State", "link": "https://wikipedia.org/wiki/United_States_Secretary_of_State"}, {"title": "Nobel Peace Prize", "link": "https://wikipedia.org/wiki/Nobel_Peace_Prize"}]}, {"year": "1858", "text": "<PERSON>, Italian composer and educator (d. 1924)", "html": "1858 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian composer and educator (d. 1924)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian composer and educator (d. 1924)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1862", "text": "<PERSON>, American baseball player and manager (d. 1956)", "html": "1862 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player and manager (d. 1956)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player and manager (d. 1956)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1865", "text": "<PERSON>, American golfer and tennis player (d. 1945)", "html": "1865 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American golfer and tennis player (d. 1945)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American golfer and tennis player (d. 1945)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1868", "text": "<PERSON><PERSON>, Estonian journalist, lawyer, and politician, 2nd Prime Minister of Estonia (d. 1941?)", "html": "1868 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_T%C3%B5<PERSON><PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Estonian journalist, lawyer, and politician, 2nd <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Estonia\" title=\"Prime Minister of Estonia\">Prime Minister of Estonia</a> (d. 1941?)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_T%C3%B5<PERSON><PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Estonian journalist, lawyer, and politician, 2nd <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Estonia\" title=\"Prime Minister of Estonia\">Prime Minister of Estonia</a> (d. 1941?)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Jaan_T%C3%B5<PERSON>son"}, {"title": "Prime Minister of Estonia", "link": "https://wikipedia.org/wiki/Prime_Minister_of_Estonia"}]}, {"year": "1869", "text": "<PERSON><PERSON><PERSON>, Russian mathematician and academic (d. 1931)", "html": "1869 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Russian mathematician and academic (d. 1931)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Russian mathematician and academic (d. 1931)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1869", "text": "<PERSON>, American poet and playwright (d. 1935)", "html": "1869 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American poet and playwright (d. 1935)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American poet and playwright (d. 1935)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1872", "text": "<PERSON>, French veterinarian and bacteriologist (d. 1961)", "html": "1872 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A9rin\" title=\"<PERSON>\"><PERSON></a>, French veterinarian and bacteriologist (d. 1961)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A9rin\" title=\"<PERSON>\"><PERSON></a>, French veterinarian and bacteriologist (d. 1961)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Camille_Gu%C3%A9rin"}]}, {"year": "1874", "text": "<PERSON>, Austrian cellist, pianist, and composer (d. 1939)", "html": "1874 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(composer)\" title=\"<PERSON> (composer)\"><PERSON></a>, Austrian cellist, pianist, and composer (d. 1939)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(composer)\" title=\"<PERSON> (composer)\"><PERSON></a>, Austrian cellist, pianist, and composer (d. 1939)", "links": [{"title": "<PERSON> (composer)", "link": "https://wikipedia.org/wiki/<PERSON>_(composer)"}]}, {"year": "1876", "text": "<PERSON><PERSON><PERSON>, Egyptian-Italian poet and composer (d. 1944)", "html": "1876 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Egyptian-Italian poet and composer (d. 1944)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Egyptian-Italian poet and composer (d. 1944)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1878", "text": "<PERSON><PERSON>, Polish-American jumper (d. 1925)", "html": "1878 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Polish-American jumper (d. 1925)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Polish-American jumper (d. 1925)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1880", "text": "<PERSON>, Australian rugby league player (d. 1955)", "html": "1880 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian rugby league player (d. 1955)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian rugby league player (d. 1955)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1883", "text": "<PERSON>, American cyclist (d. 1941)", "html": "1883 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American cyclist (d. 1941)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American cyclist (d. 1941)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1883", "text": "<PERSON><PERSON>, French-American composer (d. 1965)", "html": "1883 - <a href=\"https://wikipedia.org/wiki/Edgar<PERSON>_<PERSON>ar%C3%A8se\" title=\"Edgar<PERSON> V<PERSON>\"><PERSON><PERSON></a>, French-American composer (d. 1965)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Edgar<PERSON>_<PERSON>ar%C3%A8se\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, French-American composer (d. 1965)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Edgard_Var%C3%A8se"}]}, {"year": "1884", "text": "<PERSON><PERSON> <PERSON><PERSON>, African American chemist and educator (d. 1966)", "html": "1884 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"St. Elmo Brady\"><PERSON><PERSON> <PERSON><PERSON></a>, African American chemist and educator (d. 1966)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"St. Elmo Brady\"><PERSON><PERSON> <PERSON><PERSON></a>, African American chemist and educator (d. 1966)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "1885", "text": "<PERSON><PERSON>, American conductor and critic (d. 1966)", "html": "1885 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American conductor and critic (d. 1966)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American conductor and critic (d. 1966)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1887", "text": "<PERSON><PERSON><PERSON><PERSON>, Indian mathematician and theorist (d. 1920)", "html": "1887 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Indian mathematician and theorist (d. 1920)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Indian mathematician and theorist (d. 1920)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1888", "text": "<PERSON><PERSON>, 1st Baron <PERSON>, English businessman, founded Rank Organisation (d. 1972)", "html": "1888 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>,_1st_Baron_Rank\" class=\"mw-redirect\" title=\"<PERSON><PERSON>, 1st Baron Rank\"><PERSON><PERSON>, 1st Baron <PERSON></a>, English businessman, founded <a href=\"https://wikipedia.org/wiki/Rank_Organisation\" class=\"mw-redirect\" title=\"Rank Organisation\">Rank Organisation</a> (d. 1972)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>,_1st_Baron_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON><PERSON>, 1st Baron Rank\"><PERSON><PERSON>, 1st Baron <PERSON></a>, English businessman, founded <a href=\"https://wikipedia.org/wiki/Rank_Organisation\" class=\"mw-redirect\" title=\"Rank Organisation\">Rank Organisation</a> (d. 1972)", "links": [{"title": "<PERSON><PERSON>, 1st Baron <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>,_1st_Baron_<PERSON>"}, {"title": "Rank Organisation", "link": "https://wikipedia.org/wiki/Rank_Organisation"}]}, {"year": "1889", "text": "<PERSON>, English runner and soldier (d. 1914)", "html": "1889 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English runner and soldier (d. 1914)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English runner and soldier (d. 1914)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1892", "text": "<PERSON>, Slovenian-Austrian engineer (d. 1929)", "html": "1892 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C4%8Dnik\" title=\"<PERSON>\"><PERSON></a>, Slovenian-Austrian engineer (d. 1929)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C4%8Dnik\" title=\"<PERSON>\"><PERSON></a>, Slovenian-Austrian engineer (d. 1929)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>%C4%8Dnik"}]}, {"year": "1894", "text": "<PERSON>, Finnish academic, professor and the Prime Minister of Finland (d. 1963)", "html": "1894 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Finnish academic, professor and the <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Finland\" title=\"Prime Minister of Finland\">Prime Minister of Finland</a> (d. 1963)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Finnish academic, professor and the <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Finland\" title=\"Prime Minister of Finland\">Prime Minister of Finland</a> (d. 1963)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Prime Minister of Finland", "link": "https://wikipedia.org/wiki/Prime_Minister_of_Finland"}]}, {"year": "1898", "text": "<PERSON>, Russian physicist and mathematician (d. 1974)", "html": "1898 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian physicist and mathematician (d. 1974)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Fock\"><PERSON></a>, Russian physicist and mathematician (d. 1974)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Vladimir_<PERSON>ock"}]}, {"year": "1899", "text": "<PERSON><PERSON><PERSON>, German actor and director (d. 1963)", "html": "1899 - <a href=\"https://wikipedia.org/wiki/Gustaf_Gr%C3%BCndgens\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, German actor and director (d. 1963)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Gustaf_Gr%C3%BCndgens\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, German actor and director (d. 1963)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Gustaf_Gr%C3%BCndgens"}]}, {"year": "1900", "text": "<PERSON>, French director and screenwriter (d. 1973)", "html": "1900 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A9gret\" title=\"<PERSON>\"><PERSON></a>, French director and screenwriter (d. 1973)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A9gret\" title=\"<PERSON>\"><PERSON></a>, French director and screenwriter (d. 1973)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Marc_All%C3%A9gret"}]}, {"year": "1901", "text": "<PERSON>, Russian-American conductor and composer (d. 1980)", "html": "1901 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian-American conductor and composer (d. 1980)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian-American conductor and composer (d. 1980)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1903", "text": "<PERSON><PERSON>, American physiologist and academic, Nobel Prize laureate (d. 1983)", "html": "1903 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON> Hartline\"><PERSON><PERSON></a>, American physiologist and academic, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Physiology_or_Medicine\" title=\"Nobel Prize in Physiology or Medicine\">Nobel Prize</a> laureate (d. 1983)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON> Hartline\"><PERSON><PERSON></a>, American physiologist and academic, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Physiology_or_Medicine\" title=\"Nobel Prize in Physiology or Medicine\">Nobel Prize</a> laureate (d. 1983)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_Hartline"}, {"title": "Nobel Prize in Physiology or Medicine", "link": "https://wikipedia.org/wiki/Nobel_Prize_in_Physiology_or_Medicine"}]}, {"year": "1905", "text": "<PERSON>, French-Italian actor and screenwriter (d. 1972)", "html": "1905 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French-Italian actor and screenwriter (d. 1972)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French-Italian actor and screenwriter (d. 1972)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1905", "text": "<PERSON>, French ice hockey player and racing driver (d. 1955)", "html": "1905 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French ice hockey player and racing driver (d. 1955)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French ice hockey player and racing driver (d. 1955)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1905", "text": "<PERSON>, American poet, translator, and academic (d. 1982)", "html": "1905 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American poet, translator, and academic (d. 1982)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American poet, translator, and academic (d. 1982)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1907", "text": "<PERSON>, English actress (d. 1991)", "html": "1907 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actress (d. 1991)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actress (d. 1991)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1908", "text": "<PERSON>, Italian sculptor and academic (d. 1991)", "html": "1908 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%B9\" title=\"<PERSON>\"><PERSON></a>, Italian sculptor and academic (d. 1991)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%B9\" title=\"<PERSON>\"><PERSON></a>, Italian sculptor and academic (d. 1991)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Giacomo_Manz%C3%B9"}]}, {"year": "1909", "text": "<PERSON>, English actress (d. 1998)", "html": "1909 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actress (d. 1998)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actress (d. 1998)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1911", "text": "<PERSON>, English actor (d. 2003)", "html": "1911 - <a href=\"https://wikipedia.org/wiki/<PERSON>_O%27Dea\" title=\"<PERSON>\"><PERSON></a>, English actor (d. 2003)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_O%27Dea\" title=\"<PERSON>\"><PERSON></a>, English actor (d. 2003)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Danny_O%27Dea"}]}, {"year": "1912", "text": "<PERSON>, Greek commander (d. 1943)", "html": "1912 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, Greek commander (d. 1943)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, Greek commander (d. 1943)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1912", "text": "<PERSON> <PERSON>, American beautification activist;  38th First Lady of the United States (d. 2007)", "html": "1912 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"Lady <PERSON>\">Lady <PERSON></a>, American beautification activist; 38th <a href=\"https://wikipedia.org/wiki/List_of_First_Ladies_of_the_United_States\" class=\"mw-redirect\" title=\"List of First Ladies of the United States\">First Lady of the United States</a> (d. 2007)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"Lady <PERSON>\"><PERSON></a>, American beautification activist; 38th <a href=\"https://wikipedia.org/wiki/List_of_First_Ladies_of_the_United_States\" class=\"mw-redirect\" title=\"List of First Ladies of the United States\">First Lady of the United States</a> (d. 2007)", "links": [{"title": "<PERSON> <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "List of First Ladies of the United States", "link": "https://wikipedia.org/wiki/List_of_First_Ladies_of_the_United_States"}]}, {"year": "1913", "text": "<PERSON>, Italian discus thrower and hurdler (d. 1998)", "html": "1913 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian discus thrower and hurdler (d. 1998)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian discus thrower and hurdler (d. 1998)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1915", "text": "<PERSON>, American actress (d. 2010)", "html": "1915 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress (d. 2010)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress (d. 2010)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1915", "text": "<PERSON>, English author and academic (d. 2000)", "html": "1915 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English author and academic (d. 2000)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English author and academic (d. 2000)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1917", "text": "<PERSON>, American game show host and actor (d. 1999)", "html": "1917 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American game show host and actor (d. 1999)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American game show host and actor (d. 1999)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1921", "text": "<PERSON>, Greek guitarist and composer (d. 1996)", "html": "1921 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Greek guitarist and composer (d. 1996)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Greek guitarist and composer (d. 1996)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1921", "text": "<PERSON><PERSON>, American singer-songwriter and guitarist (d. 1963)", "html": "1921 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American singer-songwriter and guitarist (d. 1963)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American singer-songwriter and guitarist (d. 1963)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1922", "text": "<PERSON>, American actress (d. 1999)", "html": "1922 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress (d. 1999)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Roman\"><PERSON></a>, American actress (d. 1999)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1922", "text": "<PERSON>, American soldier, lawyer, and politician, 56th Speaker of the United States House of Representatives (d. 2015)", "html": "1922 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American soldier, lawyer, and politician, 56th <a href=\"https://wikipedia.org/wiki/Speaker_of_the_United_States_House_of_Representatives\" title=\"Speaker of the United States House of Representatives\">Speaker of the United States House of Representatives</a> (d. 2015)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American soldier, lawyer, and politician, 56th <a href=\"https://wikipedia.org/wiki/Speaker_of_the_United_States_House_of_Representatives\" title=\"Speaker of the United States House of Representatives\">Speaker of the United States House of Representatives</a> (d. 2015)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Speaker of the United States House of Representatives", "link": "https://wikipedia.org/wiki/Speaker_of_the_United_States_House_of_Representatives"}]}, {"year": "1923", "text": "<PERSON><PERSON><PERSON>, English journalist and author (d. 2020)", "html": "1923 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, English journalist and author (d. 2020)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, English journalist and author (d. 2020)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1924", "text": "<PERSON>, American actor and director (d. 2017)", "html": "1924 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor and director (d. 2017)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor and director (d. 2017)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1925", "text": "<PERSON>, American businessman and philanthropist (d. 2006)", "html": "1925 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American businessman and philanthropist (d. 2006)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American businessman and philanthropist (d. 2006)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1925", "text": "Left<PERSON>, Turkish footballer and manager (d. 2012)", "html": "1925 - <a href=\"https://wikipedia.org/wiki/Lefter_K%C3%BC%C3%A7%C3%BCkandonyadis\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Turkish footballer and manager (d. 2012)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Lefter_K%C3%BC%C3%A7%C3%BCkandonyadis\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Turkish footballer and manager (d. 2012)", "links": [{"title": "Lefter <PERSON>", "link": "https://wikipedia.org/wiki/Lefter_K%C3%BC%C3%A7%C3%BCkandonyadis"}]}, {"year": "1926", "text": "<PERSON><PERSON><PERSON>, Italian-Uruguayan footballer and manager (d. 2015)", "html": "1926 - <a href=\"https://wikipedia.org/wiki/Alcides_Ghiggia\" title=\"Alcides Ghiggia\"><PERSON><PERSON><PERSON></a>, Italian-Uruguayan footballer and manager (d. 2015)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Alcides_Ghi<PERSON>\" title=\"Alcides Ghiggia\"><PERSON><PERSON><PERSON></a>, Italian-Uruguayan footballer and manager (d. 2015)", "links": [{"title": "Al<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Alcides_Ghiggia"}]}, {"year": "1926", "text": "<PERSON>, English writer, artist and TV producer (d. 2014)", "html": "1926 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English writer, artist and TV producer (d. 2014)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English writer, artist and TV producer (d. 2014)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1928", "text": "<PERSON><PERSON>, German-Norwegian anthropologist and academic (d. 2016)", "html": "1928 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, German-Norwegian anthropologist and academic (d. 2016)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, German-Norwegian anthropologist and academic (d. 2016)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1929", "text": "<PERSON><PERSON><PERSON>, Indian-Pakistani cricketer", "html": "1929 - <a href=\"https://wikipedia.org/wiki/W<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Indian-Pakistani cricketer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/W<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Indian-Pakistani cricketer", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1930", "text": "<PERSON><PERSON><PERSON>, Russian sprinter and educator (d. 1998)", "html": "1930 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Russian sprinter and educator (d. 1998)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Russian sprinter and educator (d. 1998)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1931", "text": "<PERSON><PERSON><PERSON>, German hurdler and coach (d. 2024)", "html": "1931 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, German hurdler and coach (d. 2024)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, German hurdler and coach (d. 2024)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1931", "text": "<PERSON>, São Toméan lawyer and politician, Prime Minister of São Tomé and Pr<PERSON><PERSON><PERSON> (d. 2013)", "html": "1931 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A7a\" title=\"<PERSON>\"><PERSON></a>, São Toméan lawyer and politician, <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_S%C3%A3o_Tom%C3%A9_and_Pr%C3%ADncipe\" class=\"mw-redirect\" title=\"Prime Minister of São Tomé and Príncipe\">Prime Minister of São Tomé and <PERSON>rín<PERSON><PERSON></a> (d. 2013)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A7a\" title=\"<PERSON>\"><PERSON></a>, São Toméan lawyer and politician, <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_S%C3%A3o_Tom%C3%A9_and_Pr%C3%ADncipe\" class=\"mw-redirect\" title=\"Prime Minister of São Tomé and Príncipe\">Prime Minister of São Tom<PERSON> and <PERSON>rín<PERSON><PERSON></a> (d. 2013)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Carlos_Gra%C3%A7a"}, {"title": "Prime Minister of São Tomé and <PERSON><PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Prime_Minister_of_S%C3%A3o_Tom%C3%A9_and_Pr%C3%ADncipe"}]}, {"year": "1932", "text": "<PERSON>, Welsh soccer player and manager (d. 2013)", "html": "1932 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Welsh soccer player and manager (d. 2013)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Welsh soccer player and manager (d. 2013)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>nam"}]}, {"year": "1933", "text": "<PERSON>, English motorcycle racer (d. 1968)", "html": "1933 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English motorcycle racer (d. 1968)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English motorcycle racer (d. 1968)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1934", "text": "<PERSON>, American race car driver (d. 2018)", "html": "1934 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(racing_driver)\" title=\"<PERSON> (racing driver)\"><PERSON></a>, American race car driver (d. 2018)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(racing_driver)\" title=\"<PERSON> (racing driver)\"><PERSON></a>, American race car driver (d. 2018)", "links": [{"title": "<PERSON> (racing driver)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(racing_driver)"}]}, {"year": "1935", "text": "<PERSON>, Portuguese director and screenwriter (d. 2012)", "html": "1935 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(film_director)\" title=\"<PERSON> (film director)\"><PERSON></a>, Portuguese director and screenwriter (d. 2012)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(film_director)\" title=\"<PERSON> (film director)\"><PERSON></a>, Portuguese director and screenwriter (d. 2012)", "links": [{"title": "<PERSON> (film director)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_(film_director)"}]}, {"year": "1936", "text": "<PERSON>, Irish historian and author", "html": "1936 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(science_historian)\" title=\"<PERSON> (science historian)\"><PERSON></a>, Irish historian and author", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(science_historian)\" title=\"<PERSON> (science historian)\"><PERSON></a>, Irish historian and author", "links": [{"title": "<PERSON> (science historian)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(science_historian)"}]}, {"year": "1936", "text": "<PERSON><PERSON><PERSON>, American actor and director", "html": "1936 - <a href=\"https://wikipedia.org/wiki/H%C3%A9ctor_<PERSON>zondo\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American actor and director", "no_year_html": "<a href=\"https://wikipedia.org/wiki/H%C3%A9ctor_<PERSON>zondo\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American actor and director", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/H%C3%A9ctor_Elizondo"}]}, {"year": "1937", "text": "<PERSON>, English author (d. 2000)", "html": "1937 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English author (d. 2000)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Lamb\"><PERSON></a>, English author (d. 2000)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1937", "text": "<PERSON>, Russian author, poet, and playwright (d. 2018)", "html": "1937 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian author, poet, and playwright (d. 2018)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian author, poet, and playwright (d. 2018)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1937", "text": "<PERSON>, English author and playwright", "html": "1937 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English author and playwright", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English author and playwright", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1938", "text": "<PERSON><PERSON>, Dominican-American baseball player and scout (d. 2011)", "html": "1938 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Dominican-American baseball player and scout (d. 2011)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Dominican-American baseball player and scout (d. 2011)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1938", "text": "<PERSON>, Canadian lawyer and politician, 27th Premier of Quebec", "html": "1938 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian lawyer and politician, 27th <a href=\"https://wikipedia.org/wiki/Premier_of_Quebec\" title=\"Premier of Quebec\">Premier of Quebec</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian lawyer and politician, 27th <a href=\"https://wikipedia.org/wiki/Premier_of_Quebec\" title=\"Premier of Quebec\">Premier of Quebec</a>", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Premier of Quebec", "link": "https://wikipedia.org/wiki/Premier_of_Quebec"}]}, {"year": "1938", "text": "<PERSON>, American singer-songwriter, guitarist, actor, and poet", "html": "1938 - <a href=\"https://wikipedia.org/wiki/Red_Steagall\" title=\"Red Steagall\"><PERSON></a>, American singer-songwriter, guitarist, actor, and poet", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Red_Steagall\" title=\"Red Steagall\"><PERSON></a>, American singer-songwriter, guitarist, actor, and poet", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Red_Steagall"}]}, {"year": "1940", "text": "<PERSON>, Colombian rancher and politician (d. 2009)", "html": "1940 - <a href=\"https://wikipedia.org/wiki/Luis_<PERSON>_<PERSON>u%C3%A9llar\" title=\"<PERSON>\"><PERSON></a>, Colombian rancher and politician (d. 2009)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>%C3%A9llar\" title=\"<PERSON>\"><PERSON></a>, Colombian rancher and politician (d. 2009)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Luis_<PERSON>_Cu%C3%A9llar"}]}, {"year": "1940", "text": "<PERSON>, English journalist, author, and illustrator", "html": "1940 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English journalist, author, and illustrator", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English journalist, author, and illustrator", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1942", "text": "<PERSON>, American baseball player", "html": "1942 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1942", "text": "<PERSON>, English saxophonist", "html": "1942 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English saxophonist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English saxophonist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1943", "text": "<PERSON>, Slovak-Swiss physicist and academic", "html": "1943 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(physicist)\" title=\"<PERSON> (physicist)\"><PERSON></a>, Slovak-Swiss physicist and academic", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(physicist)\" title=\"<PERSON> (physicist)\"><PERSON></a>, Slovak-Swiss physicist and academic", "links": [{"title": "<PERSON> (physicist)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(physicist)"}]}, {"year": "1943", "text": "<PERSON>, American banker and politician, 25th United States Deputy Secretary of Defense", "html": "1943 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American banker and politician, 25th <a href=\"https://wikipedia.org/wiki/United_States_Deputy_Secretary_of_Defense\" title=\"United States Deputy Secretary of Defense\">United States Deputy Secretary of Defense</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American banker and politician, 25th <a href=\"https://wikipedia.org/wiki/United_States_Deputy_Secretary_of_Defense\" title=\"United States Deputy Secretary of Defense\">United States Deputy Secretary of Defense</a>", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}, {"title": "United States Deputy Secretary of Defense", "link": "https://wikipedia.org/wiki/United_States_Deputy_Secretary_of_Defense"}]}, {"year": "1944", "text": "<PERSON>, English chemist and academic", "html": "1944 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English chemist and academic", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English chemist and academic", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1944", "text": "<PERSON>, American baseball player", "html": "1944 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1944", "text": "<PERSON>, English drummer", "html": "1944 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(musician)\" title=\"<PERSON> (musician)\"><PERSON></a>, English drummer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(musician)\" title=\"<PERSON> (musician)\"><PERSON></a>, English drummer", "links": [{"title": "<PERSON> (musician)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(musician)"}]}, {"year": "1945", "text": "<PERSON>, English historian and academic", "html": "1945 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English historian and academic", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English historian and academic", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1945", "text": "<PERSON>, Australian footballer and sportscaster", "html": "1945 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian footballer and sportscaster", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian footballer and sportscaster", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1945", "text": "<PERSON>, American journalist", "html": "1945 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American journalist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American journalist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1946", "text": "<PERSON>, English businessman", "html": "1946 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(businessman)\" title=\"<PERSON> (businessman)\"><PERSON></a>, English businessman", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>(businessman)\" title=\"<PERSON> (businessman)\"><PERSON></a>, English businessman", "links": [{"title": "<PERSON> (businessman)", "link": "https://wikipedia.org/wiki/<PERSON>(businessman)"}]}, {"year": "1946", "text": "<PERSON><PERSON>, American economist and author", "html": "1946 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American economist and author", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American economist and author", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1947", "text": "<PERSON>, American author and screenwriter (d. 1996)", "html": "1947 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author and screenwriter (d. 1996)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author and screenwriter (d. 1996)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1947", "text": "<PERSON><PERSON>, Indian cricketer", "html": "1947 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Indian cricketer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Indian cricketer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1948", "text": "<PERSON>, American baseball player and sportscaster", "html": "1948 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player and sportscaster", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player and sportscaster", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1948", "text": "<PERSON>, American runner, journalist, and author", "html": "1948 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American runner, journalist, and author", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American runner, journalist, and author", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1948", "text": "<PERSON>, American singer-songwriter and guitarist", "html": "1948 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and guitarist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and guitarist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1948", "text": "<PERSON>, English cricketer and coach", "html": "1948 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English cricketer and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English cricketer and coach", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1948", "text": "<PERSON><PERSON>, American actress and singer (d. 2003)", "html": "1948 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>hi<PERSON>\"><PERSON><PERSON></a>, American actress and singer (d. 2003)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American actress and singer (d. 2003)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>hig<PERSON>"}]}, {"year": "1949", "text": "<PERSON>, Manx-English singer-songwriter and producer (d. 2003)", "html": "1949 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Manx-English singer-songwriter and producer (d. 2003)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Manx-English singer-songwriter and producer (d. 2003)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1949", "text": "<PERSON>, Manx-English singer-songwriter and producer (d. 2012)", "html": "1949 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Manx-English singer-songwriter and producer (d. 2012)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>ib<PERSON>\"><PERSON></a>, Manx-English singer-songwriter and producer (d. 2012)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1949", "text": "<PERSON>, American football player (d. 2022)", "html": "1949 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player (d. 2022)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Guy\"><PERSON></a>, American football player (d. 2022)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1950", "text": "<PERSON>, American football player and rugby league player (d. 2020)", "html": "1950 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player and rugby league player (d. 2020)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player and rugby league player (d. 2020)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1951", "text": "<PERSON><PERSON>, Swedish journalist", "html": "1951 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Swedish journalist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Swedish journalist", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1951", "text": "<PERSON>, Dutch-Canadian author and critic", "html": "1951 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch-Canadian author and critic", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch-Canadian author and critic", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1951", "text": "<PERSON>, 6th Duke of Westminster, British landowner, businessman and philanthropist (d. 2016)", "html": "1951 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_6th_Duke_of_Westminster\" title=\"<PERSON>, 6th Duke of Westminster\"><PERSON>, 6th Duke <PERSON> Westminster</a>, British landowner, businessman and philanthropist (d. 2016)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>,_6th_Duke_of_Westminster\" title=\"<PERSON>, 6th Duke of Westminster\"><PERSON>, 6th Duke <PERSON> Westminster</a>, British landowner, businessman and philanthropist (d. 2016)", "links": [{"title": "<PERSON>, 6th Duke of Westminster", "link": "https://wikipedia.org/wiki/<PERSON>,_6th_Duke_of_Westminster"}]}, {"year": "1951", "text": "<PERSON>, American comic book writer, editor, actor, artist and critic", "html": "1951 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, American comic book writer, editor, actor, artist and critic", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON> Isabella\"><PERSON></a>, American comic book writer, editor, actor, artist and critic", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1951", "text": "<PERSON>, Australian golfer", "html": "1951 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian golfer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian golfer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1952", "text": "<PERSON>, Latvian politician and diplomat, former Latvian Minister of Foreign Affairs", "html": "1952 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Latvian politician and diplomat, former <a href=\"https://wikipedia.org/wiki/List_of_Ministers_for_Foreign_Affairs_of_Latvia\" class=\"mw-redirect\" title=\"List of Ministers for Foreign Affairs of Latvia\">Latvian Minister of Foreign Affairs</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Latvian politician and diplomat, former <a href=\"https://wikipedia.org/wiki/List_of_Ministers_for_Foreign_Affairs_of_Latvia\" class=\"mw-redirect\" title=\"List of Ministers for Foreign Affairs of Latvia\">Latvian Minister of Foreign Affairs</a>", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "List of Ministers for Foreign Affairs of Latvia", "link": "https://wikipedia.org/wiki/List_of_Ministers_for_Foreign_Affairs_of_Latvia"}]}, {"year": "1953", "text": "<PERSON>, Canadian ice hockey player", "html": "1953 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(ice_hockey)\" title=\"<PERSON> (ice hockey)\"><PERSON></a>, Canadian ice hockey player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(ice_hockey)\" title=\"<PERSON> (ice hockey)\"><PERSON></a>, Canadian ice hockey player", "links": [{"title": "<PERSON> (ice hockey)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(ice_hockey)"}]}, {"year": "1953", "text": "<PERSON>, American baseball player (d. 2010)", "html": "1953 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player (d. 2010)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player (d. 2010)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1954", "text": "<PERSON><PERSON><PERSON>, Japanese racing driver", "html": "1954 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese racing driver", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese racing driver", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1954", "text": "<PERSON><PERSON>, Nevisian cricketer", "html": "1954 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Nevisian cricketer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Nevisian cricketer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1955", "text": "<PERSON><PERSON><PERSON>, Lithuanian discus thrower", "html": "1955 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>%C5%A1ova\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Lithuanian discus thrower", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>%C5%A1ova\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Lithuanian discus thrower", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>na_Mura%C5%A1ova"}]}, {"year": "1955", "text": "<PERSON><PERSON><PERSON>, American baseball player", "html": "1955 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_(baseball)\" title=\"<PERSON><PERSON><PERSON> (baseball)\"><PERSON><PERSON><PERSON></a>, American baseball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_(baseball)\" title=\"<PERSON><PERSON><PERSON> (baseball)\"><PERSON><PERSON><PERSON></a>, American baseball player", "links": [{"title": "<PERSON><PERSON><PERSON> (baseball)", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_(baseball)"}]}, {"year": "1955", "text": "<PERSON>, German-American biochemist and academic, Nobel Prize laureate", "html": "1955 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>._S%C3%BCdhof\" title=\"<PERSON>\"><PERSON></a>, German-American biochemist and academic, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Physiology_or_Medicine\" title=\"Nobel Prize in Physiology or Medicine\">Nobel Prize</a> laureate", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_S%C3%BCdhof\" title=\"<PERSON>\"><PERSON></a>, German-American biochemist and academic, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Physiology_or_Medicine\" title=\"Nobel Prize in Physiology or Medicine\">Nobel Prize</a> laureate", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Thomas_C._S%C3%BCdhof"}, {"title": "Nobel Prize in Physiology or Medicine", "link": "https://wikipedia.org/wiki/Nobel_Prize_in_Physiology_or_Medicine"}]}, {"year": "1956", "text": "<PERSON>, English businesswoman", "html": "1956 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English businesswoman", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English businesswoman", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_Lighting"}]}, {"year": "1957", "text": "<PERSON>, English bishop", "html": "1957 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English bishop", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English bishop", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1957", "text": "<PERSON>, English-Canadian educator and politician", "html": "1957 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English-Canadian educator and politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English-Canadian educator and politician", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1957", "text": "<PERSON>, Australian rugby league player", "html": "1957 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(rugby_league)\" title=\"<PERSON> (rugby league)\"><PERSON></a>, Australian rugby league player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(rugby_league)\" title=\"<PERSON> (rugby league)\"><PERSON></a>, Australian rugby league player", "links": [{"title": "<PERSON> (rugby league)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(rugby_league)"}]}, {"year": "1958", "text": "<PERSON>, Australian guitarist, songwriter, and producer", "html": "1958 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian guitarist, songwriter, and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian guitarist, songwriter, and producer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1958", "text": "<PERSON>, American singer-songwriter, producer, actor, and director", "html": "1958 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter, producer, actor, and director", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter, producer, actor, and director", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1959", "text": "<PERSON><PERSON>, German footballer and manager", "html": "1959 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, German footballer and manager", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, German footballer and manager", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1960", "text": "<PERSON><PERSON><PERSON>, American painter and poet (d. 1988)", "html": "1960 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American painter and poet (d. 1988)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American painter and poet (d. 1988)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>"}]}, {"year": "1960", "text": "<PERSON>, American rapper and actor", "html": "1960 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, American rapper and actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, American rapper and actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1960", "text": "<PERSON>, Artist", "html": "1960 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, Artist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, Artist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1961", "text": "<PERSON>, Russian colonel, pilot, and astronaut", "html": "1961 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian colonel, pilot, and astronaut", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian colonel, pilot, and astronaut", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1962", "text": "<PERSON>, English actor", "html": "1962 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1963", "text": "<PERSON>, Italian footballer and coach", "html": "1963 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian footballer and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian footballer and coach", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1963", "text": "<PERSON>, South African cricketer and educator", "html": "1963 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, South African cricketer and educator", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, South African cricketer and educator", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1963", "text": "<PERSON>, Japanese-American painter and illustrator", "html": "1963 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Japanese-American painter and illustrator", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON> H<PERSON>\"><PERSON> <PERSON></a>, Japanese-American painter and illustrator", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "1964", "text": "<PERSON>, American baseball player", "html": "1964 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(right-handed_pitcher)\" title=\"<PERSON> (right-handed pitcher)\"><PERSON></a>, American baseball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(right-handed_pitcher)\" title=\"<PERSON> (right-handed pitcher)\"><PERSON></a>, American baseball player", "links": [{"title": "<PERSON> (right-handed pitcher)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(right-handed_pitcher)"}]}, {"year": "1964", "text": "<PERSON>, English businessman and politician", "html": "1964 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English businessman and politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English businessman and politician", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1965", "text": "<PERSON>, American screenwriter", "html": "1965 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American screenwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American screenwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1965", "text": "<PERSON><PERSON><PERSON><PERSON>, Polish heptathlete and triple jumper", "html": "1965 - <a href=\"https://wikipedia.org/wiki/Urszula_W%C5%82odarczyk\" title=\"<PERSON>rs<PERSON><PERSON> Włodarczyk\"><PERSON><PERSON><PERSON><PERSON></a>, Polish heptathlete and triple jumper", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Urszula_W%C5%82odarczyk\" title=\"<PERSON>rs<PERSON><PERSON> Włodarczyk\"><PERSON><PERSON><PERSON><PERSON></a>, Polish heptathlete and triple jumper", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON> Włodarczyk", "link": "https://wikipedia.org/wiki/Urszula_W%C5%82odarczyk"}]}, {"year": "1966", "text": "<PERSON>, Russian gymnast and coach", "html": "1966 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian gymnast and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian gymnast and coach", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1966", "text": "<PERSON>, German singer-songwriter and bass player", "html": "1966 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, German singer-songwriter and bass player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, German singer-songwriter and bass player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1966", "text": "<PERSON>, English lawyer and politician", "html": "1966 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(politician)\" title=\"<PERSON> (politician)\"><PERSON></a>, English lawyer and politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>(politician)\" title=\"<PERSON> (politician)\"><PERSON></a>, English lawyer and politician", "links": [{"title": "<PERSON> (politician)", "link": "https://wikipedia.org/wiki/<PERSON>(politician)"}]}, {"year": "1967", "text": "<PERSON><PERSON>, Welsh singer-songwriter and guitarist (d. 1995)", "html": "1967 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Welsh singer-songwriter and guitarist (d. 1995)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Welsh singer-songwriter and guitarist (d. 1995)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1967", "text": "<PERSON><PERSON><PERSON><PERSON>, Canadian lawyer and politician", "html": "1967 - <a href=\"https://wikipedia.org/wiki/St%C3%A9phane_Gendron\" title=\"<PERSON><PERSON><PERSON><PERSON>dron\"><PERSON><PERSON><PERSON><PERSON></a>, Canadian lawyer and politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/St%C3%A9phane_Gendron\" title=\"St<PERSON><PERSON><PERSON> Gendron\"><PERSON><PERSON><PERSON><PERSON></a>, Canadian lawyer and politician", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/St%C3%A9phane_Gendron"}]}, {"year": "1967", "text": "<PERSON>, English businesswoman and politician", "html": "1967 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English businesswoman and politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English businesswoman and politician", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1967", "text": "<PERSON>, Romanian footballer and manager", "html": "1967 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Romanian footballer and manager", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Romanian footballer and manager", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1968", "text": "<PERSON><PERSON>, Turkish composer, conductor, and historian", "html": "1968 - <a href=\"https://wikipedia.org/wiki/Emre_Arac%C4%B1\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Turkish composer, conductor, and historian", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Emre_Arac%C4%B1\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Turkish composer, conductor, and historian", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Emre_Arac%C4%B1"}]}, {"year": "1968", "text": "<PERSON>, Mexican footballer", "html": "1968 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%<PERSON><PERSON><PERSON>_(footballer,_born_1968)\" title=\"<PERSON> (footballer, born 1968)\"><PERSON></a>, Mexican footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%<PERSON><PERSON><PERSON>_(footballer,_born_1968)\" title=\"<PERSON> (footballer, born 1968)\"><PERSON></a>, Mexican footballer", "links": [{"title": "<PERSON> (footballer, born 1968)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>n%C3%<PERSON><PERSON><PERSON>_(footballer,_born_1968)"}]}, {"year": "1968", "text": "<PERSON>, American singer-songwriter", "html": "1968 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1968", "text": "<PERSON><PERSON>, American actress", "html": "1968 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American actress", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1969", "text": "<PERSON><PERSON><PERSON>, Canadian biathlete", "html": "1969 - <a href=\"https://wikipedia.org/wiki/Myriam_B%C3%A9dard\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Canadian biathlete", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Myriam_B%C3%A9dard\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Canadian biathlete", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Myriam_B%C3%A9dard"}]}, {"year": "1969", "text": "<PERSON>, English footballer and manager", "html": "1969 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English footballer and manager", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English footballer and manager", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1970", "text": "<PERSON>, Scottish darts player", "html": "1970 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(darts_player)\" title=\"<PERSON> (darts player)\"><PERSON></a>, Scottish darts player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(darts_player)\" title=\"<PERSON> (darts player)\"><PERSON></a>, Scottish darts player", "links": [{"title": "<PERSON> (darts player)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(darts_player)"}]}, {"year": "1970", "text": "<PERSON>, Canadian-American lawyer and politician", "html": "1970 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian-American lawyer and politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian-American lawyer and politician", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1971", "text": "<PERSON><PERSON><PERSON><PERSON>, Indian economist and academic", "html": "1971 - <a href=\"https://wikipedia.org/wiki/A<PERSON><PERSON><PERSON>_Patil\" title=\"<PERSON>jeenkya Patil\"><PERSON><PERSON><PERSON><PERSON></a>, Indian economist and academic", "no_year_html": "<a href=\"https://wikipedia.org/wiki/A<PERSON><PERSON><PERSON>_Patil\" title=\"Ajeenkya Patil\"><PERSON><PERSON><PERSON><PERSON></a>, Indian economist and academic", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>il"}]}, {"year": "1972", "text": "<PERSON>, English musician, producer and songwriter", "html": "1972 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(musician)\" title=\"<PERSON> (musician)\"><PERSON></a>, English musician, producer and songwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(musician)\" title=\"<PERSON> (musician)\"><PERSON></a>, English musician, producer and songwriter", "links": [{"title": "<PERSON> (musician)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(musician)"}]}, {"year": "1972", "text": "<PERSON>, Canadian ice hockey player and scout", "html": "1972 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player and scout", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player and scout", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Kirk_<PERSON>by"}]}, {"year": "1972", "text": "<PERSON>, French singer-songwriter and actress", "html": "1972 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French singer-songwriter and actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French singer-songwriter and actress", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1974", "text": "<PERSON><PERSON>, American actress", "html": "1974 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American actress", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>"}]}, {"year": "1975", "text": "<PERSON>, Swiss martial artist", "html": "1975 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swiss martial artist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swiss martial artist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1975", "text": "<PERSON><PERSON><PERSON>, Russian footballer and manager", "html": "1975 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Russian footballer and manager", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Russian footballer and manager", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1975", "text": "<PERSON><PERSON>, Polish footballer", "html": "1975 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>%C4%99ciel\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Polish footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>%C4%99ciel\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Polish footballer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Marcin_Mi%C4%99ciel"}]}, {"year": "1975", "text": "<PERSON><PERSON>, Czech ice hockey player", "html": "1975 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>%C3%A1%C5%99\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Czech ice hockey player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>%C3%A1%C5%99\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Czech ice hockey player", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_Neck%C3%A1%C5%99"}]}, {"year": "1975", "text": "<PERSON><PERSON><PERSON>, Japanese astronaut", "html": "1975 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese astronaut", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese astronaut", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>ishi"}]}, {"year": "1976", "text": "<PERSON><PERSON>, Belgian sprinter", "html": "1976 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>uw%C3%A9\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Belgian sprinter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>uw%C3%A9\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Belgian sprinter", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_De_Caluw%C3%A9"}]}, {"year": "1976", "text": "<PERSON>, American baseball player and coach", "html": "1976 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Lane\"><PERSON></a>, American baseball player and coach", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1976", "text": "<PERSON><PERSON>, Japanese author and illustrator", "html": "1976 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Japanese author and illustrator", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Japanese author and illustrator", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>o"}]}, {"year": "1977", "text": "<PERSON>, Canadian ice hockey player and coach", "html": "1977 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player and coach", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1978", "text": "<PERSON>, South Korean singer", "html": "1978 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, South Korean singer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, South Korean singer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>n"}]}, {"year": "1978", "text": "<PERSON>, Fijian boxer (d. 2015)", "html": "1978 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Fijian boxer (d. 2015)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Ali\"><PERSON></a>, Fijian boxer (d. 2015)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1978", "text": "<PERSON>, Nigerian-Polish footballer", "html": "1978 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Nigerian-Polish footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Nigerian-Polish footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1979", "text": "<PERSON>, Scottish footballer and coach", "html": "1979 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish footballer and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish footballer and coach", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1980", "text": "<PERSON>, American actor, singer, and model", "html": "1980 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor, singer, and model", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor, singer, and model", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1980", "text": "<PERSON>, American basketball player", "html": "1980 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1981", "text": "<PERSON>, Russian high jumper", "html": "1981 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian high jumper", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian high jumper", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>ova"}]}, {"year": "1982", "text": "<PERSON><PERSON><PERSON>, German fencer", "html": "1982 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, German fencer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, German fencer", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1982", "text": "<PERSON><PERSON>, Brazilian actress and model", "html": "1982 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Brazilian actress and model", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Brazilian actress and model", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1983", "text": "<PERSON>, Portuguese footballer", "html": "1983 - <a href=\"https://wikipedia.org/wiki/Jos%C3%A9_<PERSON><PERSON>\" title=\"<PERSON>\"><PERSON></a>, Portuguese footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Jos%C3%A9_<PERSON><PERSON>\" title=\"<PERSON>\"><PERSON></a>, Portuguese footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Jos%C3%A9_<PERSON>onte"}]}, {"year": "1983", "text": "<PERSON>, American wrestler", "html": "1983 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American wrestler", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American wrestler", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_Gallows"}]}, {"year": "1983", "text": "<PERSON>, Kenyan runner", "html": "1983 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Kenyan runner", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Kenyan runner", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Viola_Kibiwot"}]}, {"year": "1984", "text": "<PERSON><PERSON><PERSON>, Swedish singer, record producer and DJ", "html": "1984 - <a href=\"https://wikipedia.org/wiki/Basshunter\" title=\"Basshunter\"><PERSON><PERSON><PERSON></a>, Swedish singer, record producer and DJ", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>hunter\" title=\"Basshunter\"><PERSON><PERSON><PERSON></a>, Swedish singer, record producer and DJ", "links": [{"title": "Basshunter", "link": "https://wikipedia.org/wiki/Basshunter"}]}, {"year": "1984", "text": "<PERSON>, American actor", "html": "1984 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1986", "text": "<PERSON>, Australian footballer", "html": "1986 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1986", "text": "<PERSON><PERSON>, Turkish footballer", "html": "1986 - <a href=\"https://wikipedia.org/wiki/Fatih_%C3%96zt%C3%<PERSON><PERSON>_(footballer,_born_1986)\" title=\"<PERSON><PERSON> (footballer, born 1986)\"><PERSON><PERSON></a>, Turkish footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Fatih_%C3%96zt%C3%<PERSON><PERSON>_(footballer,_born_1986)\" title=\"<PERSON><PERSON> (footballer, born 1986)\"><PERSON><PERSON></a>, Turkish footballer", "links": [{"title": "<PERSON><PERSON> (footballer, born 1986)", "link": "https://wikipedia.org/wiki/Fatih_%C3%96zt%C3%BC<PERSON>_(footballer,_born_1986)"}]}, {"year": "1987", "text": "<PERSON>, American baseball player", "html": "1987 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1987", "text": "<PERSON><PERSON>, Bissau-Portuguese footballer", "html": "1987 - <a href=\"https://wikipedia.org/wiki/%C3%89der_(Portuguese_footballer)\" class=\"mw-redirect\" title=\"<PERSON><PERSON> (Portuguese footballer)\"><PERSON><PERSON></a>, Bissau-Portuguese footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/%C3%89der_(Portuguese_footballer)\" class=\"mw-redirect\" title=\"<PERSON><PERSON> (Portuguese footballer)\"><PERSON><PERSON></a>, Bissau-Portuguese footballer", "links": [{"title": "<PERSON><PERSON> (Portuguese footballer)", "link": "https://wikipedia.org/wiki/%C3%89der_(Portuguese_footballer)"}]}, {"year": "1988", "text": "<PERSON>, American ice hockey player", "html": "1988 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American ice hockey player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American ice hockey player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1988", "text": "<PERSON>, Egyptian footballer", "html": "1988 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Egyptian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Egyptian footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1988", "text": "<PERSON>, Welsh rugby player", "html": "1988 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Halfpenny\"><PERSON></a>, Welsh rugby player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Halfpenny\"><PERSON></a>, Welsh rugby player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1989", "text": "<PERSON><PERSON><PERSON>, American singer-songwriter and actress", "html": "1989 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American singer-songwriter and actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American singer-songwriter and actress", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1989", "text": "<PERSON>, American baseball player", "html": "1989 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1990", "text": "<PERSON><PERSON><PERSON>, French actor and singer", "html": "1990 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, French actor and singer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, French actor and singer", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1991", "text": "<PERSON><PERSON><PERSON>, American rapper", "html": "1991 - <a href=\"https://wikipedia.org/wiki/DaB<PERSON>\" title=\"DaBaby\"><PERSON><PERSON><PERSON></a>, American rapper", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>B<PERSON>\" title=\"DaB<PERSON>\"><PERSON><PERSON><PERSON></a>, American rapper", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/DaBaby"}]}, {"year": "1992", "text": "<PERSON><PERSON>, Slovak tennis player", "html": "1992 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>%C4%8Dov%C3%A1\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Slovak tennis player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>%C4%8Dov%C3%A1\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Slovak tennis player", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Michaela_Hon%C4%8Dov%C3%A1"}]}, {"year": "1992", "text": "<PERSON>, American basketball player", "html": "1992 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(basketball)\" title=\"<PERSON> (basketball)\"><PERSON></a>, American basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(basketball)\" title=\"<PERSON> (basketball)\"><PERSON></a>, American basketball player", "links": [{"title": "<PERSON> (basketball)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(basketball)"}]}, {"year": "1992", "text": "<PERSON><PERSON><PERSON>, South Korean rapper, vocalist and songwriter", "html": "1992 - <a href=\"https://wikipedia.org/wiki/Moonby<PERSON>\" title=\"<PERSON>by<PERSON>\"><PERSON><PERSON><PERSON></a>, South Korean rapper, vocalist and songwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Moonby<PERSON>\" title=\"<PERSON>by<PERSON>\"><PERSON><PERSON><PERSON></a>, South Korean rapper, vocalist and songwriter", "links": [{"title": "Moonbyul", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>"}]}, {"year": "1993", "text": "<PERSON><PERSON>, Spanish footballer", "html": "1993 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Spanish footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Spanish footballer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1993", "text": "<PERSON><PERSON><PERSON>, Portuguese footballer", "html": "1993 - <a href=\"https://wikipedia.org/wiki/Rapha%C3%ABl_Guerreiro\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Portuguese footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Rapha%C3%ABl_Guerreiro\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Portuguese footballer", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Rapha%C3%ABl_Guerreiro"}]}, {"year": "1993", "text": "<PERSON><PERSON>, American singer-songwriter and producer", "html": "1993 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American singer-songwriter and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American singer-songwriter and producer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>or"}]}, {"year": "1994", "text": "<PERSON><PERSON><PERSON>, Portuguese footballer", "html": "1994 - <a href=\"https://wikipedia.org/wiki/R%C3%BAben_Lameiras\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Portuguese footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/R%C3%BAben_Lameiras\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Portuguese footballer", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/R%C3%BAben_Lameiras"}]}, {"year": "1998", "text": "<PERSON>, American actress and singer", "html": "1998 - <a href=\"https://wikipedia.org/wiki/<PERSON>_Han<PERSON>ius\" title=\"<PERSON> Hannelius\"><PERSON></a>, American actress and singer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_Han<PERSON>ius\" title=\"<PERSON> Han<PERSON>\"><PERSON></a>, American actress and singer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_Hannelius"}]}, {"year": "1998", "text": "<PERSON>, Norwegian tennis player", "html": "1998 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Norwegian tennis player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Norwegian tennis player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2000", "text": "<PERSON>, American actor and singer", "html": "2000 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(actor)\" class=\"mw-redirect\" title=\"<PERSON> (actor)\"><PERSON></a>, American actor and singer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(actor)\" class=\"mw-redirect\" title=\"<PERSON> (actor)\"><PERSON></a>, American actor and singer", "links": [{"title": "<PERSON> (actor)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(actor)"}]}, {"year": "2001", "text": "<PERSON><PERSON>, Colombian tennis player", "html": "2001 - <a href=\"https://wikipedia.org/wiki/Cam<PERSON>_Osorio\" title=\"<PERSON><PERSON> Osorio\"><PERSON><PERSON></a>, Colombian tennis player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Cam<PERSON>_Osorio\" title=\"<PERSON><PERSON> Osorio\"><PERSON><PERSON></a>, Colombian tennis player", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Camila_Osorio"}]}, {"year": "2002", "text": "<PERSON>, Ivorian footballer", "html": "2002 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Ivorian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Ivorian footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "2006", "text": "<PERSON><PERSON>, Scottish footballer", "html": "2006 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Scottish footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Scottish footballer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}], "Deaths": [{"year": "69", "text": "<PERSON><PERSON><PERSON><PERSON>, Roman emperor (b. 15)", "html": "69 - AD 69 - <a href=\"https://wikipedia.org/wiki/Vitelli<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Roman emperor (b. 15)", "no_year_html": "AD 69 - <a href=\"https://wikipedia.org/wiki/Vitelli<PERSON>\" title=\"<PERSON>ite<PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Roman emperor (b. 15)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Vitellius"}]}, {"year": "731", "text": "<PERSON>, official of the Chinese Tang dynasty", "html": "731 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, official of the <a href=\"https://wikipedia.org/wiki/History_of_China\" title=\"History of China\">Chinese</a> <a href=\"https://wikipedia.org/wiki/Tang_dynasty\" title=\"Tang dynasty\">Tang dynasty</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, official of the <a href=\"https://wikipedia.org/wiki/History_of_China\" title=\"History of China\">Chinese</a> <a href=\"https://wikipedia.org/wiki/Tang_dynasty\" title=\"Tang dynasty\">Tang dynasty</a>", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "History of China", "link": "https://wikipedia.org/wiki/History_of_China"}, {"title": "Tang dynasty", "link": "https://wikipedia.org/wiki/Tang_dynasty"}]}, {"year": "1012", "text": "<PERSON><PERSON><PERSON> <PERSON><PERSON>, Buyid amir of Iraq", "html": "1012 - <a href=\"https://wikipedia.org/wiki/<PERSON>ha%27_al-Dawla\" class=\"mw-redirect\" title=\"Baha' al-Dawla\"><PERSON><PERSON><PERSON> <PERSON><PERSON></a>, <a href=\"https://wikipedia.org/wiki/Buyid\" class=\"mw-redirect\" title=\"Buyid\">Buyid</a> amir of <a href=\"https://wikipedia.org/wiki/Iraq\" title=\"Iraq\">Iraq</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>ha%27_al-<PERSON>wl<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>ha<PERSON> al-Dawla\"><PERSON><PERSON><PERSON> <PERSON><PERSON></a>, <a href=\"https://wikipedia.org/wiki/Buyid\" class=\"mw-redirect\" title=\"Buyid\">Buyid</a> amir of <a href=\"https://wikipedia.org/wiki/Iraq\" title=\"Iraq\">Iraq</a>", "links": [{"title": "<PERSON><PERSON><PERSON> <PERSON><PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Baha%27_<PERSON>-<PERSON><PERSON><PERSON>"}, {"title": "<PERSON>id", "link": "https://wikipedia.org/wiki/Buyid"}, {"title": "Iraq", "link": "https://wikipedia.org/wiki/Iraq"}]}, {"year": "1060", "text": "<PERSON><PERSON><PERSON>, Archbishop of York", "html": "1060 - <a href=\"https://wikipedia.org/wiki/Cynesige\" title=\"Cynesige\"><PERSON><PERSON><PERSON></a>, Archbishop of York", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Cy<PERSON>ige\" title=\"Cy<PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Archbishop of York", "links": [{"title": "Cynesige", "link": "https://wikipedia.org/wiki/Cynesige"}]}, {"year": "1100", "text": "<PERSON><PERSON><PERSON> of Bohemia (b. 1060)", "html": "1100 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_II\" title=\"Bretislav II\"><PERSON><PERSON><PERSON> <PERSON> of Bohemia</a> (b. 1060)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_II\" title=\"Bretislav II\"><PERSON><PERSON><PERSON> <PERSON> of Bohemia</a> (b. 1060)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1115", "text": "<PERSON>, King of Norway (b. 1099)", "html": "1115 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Norway\" class=\"mw-redirect\" title=\"<PERSON> of Norway\"><PERSON></a>, King of Norway (b. 1099)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Norway\" class=\"mw-redirect\" title=\"<PERSON> of Norway\"><PERSON></a>, King of Norway (b. 1099)", "links": [{"title": "<PERSON> of Norway", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Norway"}]}, {"year": "1419", "text": "Antipope John XXIII", "html": "1419 - <a href=\"https://wikipedia.org/wiki/Antipope_John_XXIII\" title=\"Antipope John XXIII\">Antipope John XXIII</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Antipope_John_XXIII\" title=\"Antipope John XXIII\">Antipope John XXIII</a>", "links": [{"title": "Antipope John XXIII", "link": "https://wikipedia.org/wiki/Antipope_John_XXIII"}]}, {"year": "1530", "text": "<PERSON><PERSON><PERSON>, German lawyer and author (b. 1470)", "html": "1530 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, German lawyer and author (b. 1470)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, German lawyer and author (b. 1470)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1554", "text": "<PERSON>, Italian painter (b. 1498)", "html": "1554 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, Italian painter (b. 1498)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, Italian painter (b. 1498)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1572", "text": "<PERSON>, French miniaturist (b. c. 1510)", "html": "1572 - <a href=\"https://wikipedia.org/wiki/Fran%C3%A7ois_<PERSON>et\" title=\"<PERSON>\"><PERSON></a>, French miniaturist (b. c. 1510)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Fran%C3%A7ois_<PERSON>et\" title=\"<PERSON>\"><PERSON></a>, French miniaturist (b. c. 1510)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Fran%C3%A7ois_Clouet"}]}, {"year": "1603", "text": "<PERSON><PERSON><PERSON>, Ottoman sultan (b. 1566)", "html": "1603 - <a href=\"https://wikipedia.org/wiki/Me<PERSON><PERSON>_III\" title=\"Mehmed III\"><PERSON><PERSON><PERSON> <PERSON></a>, Ottoman sultan (b. 1566)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_III\" title=\"Mehmed III\"><PERSON><PERSON><PERSON> III</a>, Ottoman sultan (b. 1566)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_III"}]}, {"year": "1641", "text": "<PERSON><PERSON><PERSON>, Duke of Sully, 2nd Prime Minister of France (b. 1560)", "html": "1641 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>en_de_B%C3%A<PERSON><PERSON>,_Duke_of_Sully\" title=\"<PERSON><PERSON><PERSON>, Duke of Sully\"><PERSON><PERSON><PERSON>, Duke of Sully</a>, 2nd <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_France\" title=\"Prime Minister of France\">Prime Minister of France</a> (b. 1560)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_de_B%C3%A<PERSON><PERSON>,_Duke_of_Sully\" title=\"<PERSON><PERSON><PERSON>, Duke of Sully\"><PERSON><PERSON><PERSON>, Duke of Sully</a>, 2nd <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_France\" title=\"Prime Minister of France\">Prime Minister of France</a> (b. 1560)", "links": [{"title": "<PERSON><PERSON><PERSON>, Duke of Sully", "link": "https://wikipedia.org/wiki/Maximilien_de_B%C3%A9thune,_Duke_of_Sully"}, {"title": "Prime Minister of France", "link": "https://wikipedia.org/wiki/Prime_Minister_of_France"}]}, {"year": "1646", "text": "<PERSON><PERSON>, Ruthenian metropolitan and saint (b. 1596)", "html": "1646 - <a href=\"https://wikipedia.org/wiki/Petro_Mohyla\" title=\"Petro Mohyla\"><PERSON><PERSON></a>, Ruthenian metropolitan and saint (b. 1596)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Petro_Mohyla\" title=\"Petro Mohyla\"><PERSON><PERSON></a>, Ruthenian metropolitan and saint (b. 1596)", "links": [{"title": "Petro Mohyla", "link": "https://wikipedia.org/wiki/Petro_Mohyla"}]}, {"year": "1660", "text": "<PERSON>, Flemish priest and mathematician (b. 1612)", "html": "1660 - <a href=\"https://wikipedia.org/wiki/Andr%C3%A9_<PERSON><PERSON>\" title=\"<PERSON>\"><PERSON></a>, Flemish priest and mathematician (b. 1612)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Andr%C3%A9_<PERSON><PERSON>\" title=\"<PERSON>\"><PERSON></a>, Flemish priest and mathematician (b. 1612)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Andr%C3%A9_<PERSON><PERSON><PERSON>"}]}, {"year": "1666", "text": "<PERSON><PERSON><PERSON><PERSON>, Italian painter (b. 1591)", "html": "1666 - <a href=\"https://wikipedia.org/wiki/Guercino\" title=\"Guer<PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Italian painter (b. 1591)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Guer<PERSON>o\" title=\"G<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Italian painter (b. 1591)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Guercino"}]}, {"year": "1681", "text": "<PERSON>, English minister and author (b. 1611)", "html": "1681 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English minister and author (b. 1611)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English minister and author (b. 1611)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1767", "text": "<PERSON>, English publisher (b. 1713)", "html": "1767 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English publisher (b. 1713)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English publisher (b. 1713)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1788", "text": "<PERSON><PERSON><PERSON><PERSON>, English physician and surgeon (b. 1714)", "html": "1788 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, English physician and surgeon (b. 1714)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>tt\"><PERSON><PERSON><PERSON><PERSON></a>, English physician and surgeon (b. 1714)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Percival<PERSON>_<PERSON>tt"}]}, {"year": "1806", "text": "<PERSON>, English-American merchant (b. 1719)", "html": "1806 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English-American merchant (b. 1719)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English-American merchant (b. 1719)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1828", "text": "<PERSON>, English chemist and physicist (b. 1766)", "html": "1828 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>oll<PERSON>\"><PERSON></a>, English chemist and physicist (b. 1766)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON> Wollaston\"><PERSON></a>, English chemist and physicist (b. 1766)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1853", "text": "<PERSON>, Mexican general and politician. President (1853) (b. 1802)", "html": "1853 - <a href=\"https://wikipedia.org/wiki/Manuel_<PERSON>%C3%ADa_<PERSON>ini\" title=\"<PERSON>\"><PERSON></a>, Mexican general and politician. President (1853) (b. 1802)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Manuel_<PERSON>%C3%AD<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Mexican general and politician. President (1853) (b. 1802)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Manuel_Mar%C3%ADa_Lombardini"}]}, {"year": "1867", "text": "<PERSON><PERSON><PERSON>, French mathematician and engineer (b. 1788)", "html": "1867 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, French mathematician and engineer (b. 1788)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, French mathematician and engineer (b. 1788)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>-<PERSON>"}]}, {"year": "1870", "text": "<PERSON>, Spanish journalist, poet, and playwright (b. 1836)", "html": "1870 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>%C3%A9cquer\" title=\"<PERSON>\"><PERSON></a>, Spanish journalist, poet, and playwright (b. 1836)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>%C3%A9cquer\" title=\"<PERSON>\"><PERSON></a>, Spanish journalist, poet, and playwright (b. 1836)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>%C3%A9cquer"}]}, {"year": "1880", "text": "<PERSON>, English novelist and poet (b. 1819)", "html": "1880 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English novelist and poet (b. 1819)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English novelist and poet (b. 1819)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1884", "text": "<PERSON>, American cattle baron (b. 1824)", "html": "1884 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American cattle baron (b. 1824)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American cattle baron (b. 1824)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1891", "text": "<PERSON>, German biblical scholar and orientalist (b. 1827)", "html": "1891 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German biblical scholar and orientalist (b. 1827)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German biblical scholar and orientalist (b. 1827)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1899", "text": "<PERSON>, American evangelist and publisher, founded Moody Publishers (b. 1837)", "html": "1899 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American evangelist and publisher, founded <a href=\"https://wikipedia.org/wiki/Moody_Publishers\" class=\"mw-redirect\" title=\"Moody Publishers\">Moody Publishers</a> (b. 1837)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American evangelist and publisher, founded <a href=\"https://wikipedia.org/wiki/Moody_Publishers\" class=\"mw-redirect\" title=\"Moody Publishers\">Moody Publishers</a> (b. 1837)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "Moody Publishers", "link": "https://wikipedia.org/wiki/Moody_Publishers"}]}, {"year": "1902", "text": "<PERSON>, German-Austrian psychiatrist and author (b. 1840)", "html": "1902 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>-<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German-Austrian psychiatrist and author (b. 1840)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>-<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German-Austrian psychiatrist and author (b. 1840)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>-<PERSON>"}]}, {"year": "1915", "text": "<PERSON>, American medical doctor and professor (b. 1864)", "html": "1915 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American medical doctor and professor (b. 1864)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American medical doctor and professor (b. 1864)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>ard"}]}, {"year": "1917", "text": "<PERSON>, Italian-American nun and saint (b. 1850)", "html": "1917 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian-American nun and saint (b. 1850)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian-American nun and saint (b. 1850)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1918", "text": "<PERSON><PERSON><PERSON><PERSON>, Greek lieutenant and pilot (b. 1891)", "html": "1918 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>_(aviator)\" title=\"<PERSON><PERSON><PERSON><PERSON> (aviator)\"><PERSON><PERSON><PERSON><PERSON></a>, Greek lieutenant and pilot (b. 1891)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>_(aviator)\" title=\"<PERSON><PERSON><PERSON><PERSON> (aviator)\"><PERSON><PERSON><PERSON><PERSON></a>, Greek lieutenant and pilot (b. 1891)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON> (aviator)", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>_(aviator)"}]}, {"year": "1919", "text": "<PERSON>, German gymnast (b. 1864)", "html": "1919 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A4rtner\" title=\"<PERSON>\"><PERSON></a>, German gymnast (b. 1864)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A4rt<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German gymnast (b. 1864)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A4rtner"}]}, {"year": "1924", "text": "<PERSON>, German serial killer and cannibal (b. 1860)", "html": "1924 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German serial killer and cannibal (b. 1860)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German serial killer and cannibal (b. 1860)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1925", "text": "<PERSON><PERSON><PERSON>, German pilot and engineer (b. 1886)", "html": "1925 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, German pilot and engineer (b. 1886)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, German pilot and engineer (b. 1886)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1939", "text": "<PERSON>, American singer (b. 1886)", "html": "1939 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer (b. 1886)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer (b. 1886)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>"}]}, {"year": "1940", "text": "<PERSON><PERSON>, American author and screenwriter (b. 1903)", "html": "1940 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American author and screenwriter (b. 1903)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American author and screenwriter (b. 1903)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_West"}]}, {"year": "1941", "text": "<PERSON><PERSON>, Czech actor, director, composer, and screenwriter (b. 1879)", "html": "1941 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>%C5%A1ler\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Czech actor, director, composer, and screenwriter (b. 1879)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>%C5%A1ler\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Czech actor, director, composer, and screenwriter (b. 1879)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>%C5%A1ler"}]}, {"year": "1942", "text": "<PERSON>, German-American anthropologist and linguist (b. 1858)", "html": "1942 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German-American anthropologist and linguist (b. 1858)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German-American anthropologist and linguist (b. 1858)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1943", "text": "<PERSON><PERSON>, English children's book writer and illustrator (b. 1866)", "html": "1943 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, English children's book writer and illustrator (b. 1866)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, English children's book writer and illustrator (b. 1866)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1944", "text": "<PERSON>, American actor, comedian, and vaudevillian (b. 1884)", "html": "1944 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor, comedian, and vaudevillian (b. 1884)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor, comedian, and vaudevillian (b. 1884)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1950", "text": "<PERSON>, English polo player (b. 1876)", "html": "1950 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English polo player (b. 1876)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English polo player (b. 1876)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_Freake"}]}, {"year": "1957", "text": "<PERSON>, English engineer (b. 1883)", "html": "1957 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English engineer (b. 1883)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English engineer (b. 1883)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1959", "text": "<PERSON><PERSON>, Polish-American actress and dancer (b. 1901)", "html": "1959 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Polish-American actress and dancer (b. 1901)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Polish-American actress and dancer (b. 1901)", "links": [{"title": "<PERSON><PERSON> Gray", "link": "https://wikipedia.org/wiki/Gilda_Gray"}]}, {"year": "1960", "text": "<PERSON><PERSON>, Scottish-English architect (b. 1864)", "html": "1960 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>mper\"><PERSON><PERSON></a>, Scottish-English architect (b. 1864)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>mper\"><PERSON><PERSON></a>, Scottish-English architect (b. 1864)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1962", "text": "<PERSON>, Australian politician, 17th Premier of Western Australia (b. 1891)", "html": "1962 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian politician, 17th <a href=\"https://wikipedia.org/wiki/Premier_of_Western_Australia\" title=\"Premier of Western Australia\">Premier of Western Australia</a> (b. 1891)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian politician, 17th <a href=\"https://wikipedia.org/wiki/Premier_of_Western_Australia\" title=\"Premier of Western Australia\">Premier of Western Australia</a> (b. 1891)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Premier of Western Australia", "link": "https://wikipedia.org/wiki/Premier_of_Western_Australia"}]}, {"year": "1965", "text": "<PERSON>, English journalist (b. 1913)", "html": "1965 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English journalist (b. 1913)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English journalist (b. 1913)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1968", "text": "<PERSON>, American journalist (b. 1887)", "html": "1968 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"Raymond <PERSON> Swing\"><PERSON></a>, American journalist (b. 1887)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"Raymond <PERSON> Swing\"><PERSON></a>, American journalist (b. 1887)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1969", "text": "<PERSON>, 45th President of Bolivia (b. 1892)", "html": "1969 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%B1aranda\" title=\"<PERSON>\"><PERSON></a>, 45th <a href=\"https://wikipedia.org/wiki/President_of_Bolivia\" title=\"President of Bolivia\">President of Bolivia</a> (b. 1892)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%B1aranda\" title=\"<PERSON>\"><PERSON></a>, 45th <a href=\"https://wikipedia.org/wiki/President_of_Bolivia\" title=\"President of Bolivia\">President of Bolivia</a> (b. 1892)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Enrique_Pe%C3%B1aranda"}, {"title": "President of Bolivia", "link": "https://wikipedia.org/wiki/President_of_Bolivia"}]}, {"year": "1971", "text": "<PERSON><PERSON>, Dutch journalist and author (b. 1913)", "html": "1971 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Dutch journalist and author (b. 1913)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Dutch journalist and author (b. 1913)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1974", "text": "<PERSON>, American author and critic (b. 1906)", "html": "1974 - <a href=\"https://wikipedia.org/wiki/Sterling_North\" title=\"Sterling North\">Sterling North</a>, American author and critic (b. 1906)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Sterling_North\" title=\"Sterling North\">Sterling North</a>, American author and critic (b. 1906)", "links": [{"title": "Sterling North", "link": "https://wikipedia.org/wiki/Sterling_North"}]}, {"year": "1979", "text": "<PERSON>, American director and producer (b. 1902)", "html": "1979 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American director and producer (b. 1902)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American director and producer (b. 1902)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1985", "text": "<PERSON><PERSON>, American singer and musician (b. 1958)", "html": "1985 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>\" title=\"<PERSON><PERSON> Bo<PERSON>\"><PERSON><PERSON></a>, American singer and musician (b. 1958)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>\" title=\"<PERSON><PERSON> Bo<PERSON>\"><PERSON><PERSON></a>, American singer and musician (b. 1958)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>"}]}, {"year": "1986", "text": "<PERSON>, English author and activist (b. 1904)", "html": "1986 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English author and activist (b. 1904)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English author and activist (b. 1904)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1986", "text": "<PERSON>, Cornish Liberal Politician (b. 1944), Member of Parliament (MP) for Truro (1974-1986)", "html": "1986 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Cornish Liberal Politician (b. 1944), <a href=\"https://wikipedia.org/wiki/Member_of_Parliament_(United_Kingdom)\" title=\"Member of Parliament (United Kingdom)\">Member of Parliament</a> (MP) for <a href=\"https://wikipedia.org/wiki/Truro_(UK_Parliament_constituency)\" title=\"Truro (UK Parliament constituency)\">Truro</a> (1974-1986)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Cornish Liberal Politician (b. 1944), <a href=\"https://wikipedia.org/wiki/Member_of_Parliament_(United_Kingdom)\" title=\"Member of Parliament (United Kingdom)\">Member of Parliament</a> (MP) for <a href=\"https://wikipedia.org/wiki/Truro_(UK_Parliament_constituency)\" title=\"Truro (UK Parliament constituency)\">Truro</a> (1974-1986)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}, {"title": "Member of Parliament (United Kingdom)", "link": "https://wikipedia.org/wiki/Member_of_Parliament_(United_Kingdom)"}, {"title": "Truro (UK Parliament constituency)", "link": "https://wikipedia.org/wiki/Truro_(UK_Parliament_constituency)"}]}, {"year": "1987", "text": "<PERSON>, Italian-Scottish singer-songwriter and guitarist (b. 1953)", "html": "1987 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian-Scottish singer-songwriter and guitarist (b. 1953)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian-Scottish singer-songwriter and guitarist (b. 1953)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1988", "text": "<PERSON>, Brazilian trade union leader and activist (b. 1944)", "html": "1988 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Brazilian trade union leader and activist (b. 1944)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Brazilian trade union leader and activist (b. 1944)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_Mendes"}]}, {"year": "1989", "text": "<PERSON>, Irish author, poet, and playwright, Nobel Prize laureate (b. 1906)", "html": "1989 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Irish author, poet, and playwright, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Literature\" title=\"Nobel Prize in Literature\">Nobel Prize</a> laureate (b. 1906)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Irish author, poet, and playwright, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Literature\" title=\"Nobel Prize in Literature\">Nobel Prize</a> laureate (b. 1906)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}, {"title": "Nobel Prize in Literature", "link": "https://wikipedia.org/wiki/Nobel_Prize_in_Literature"}]}, {"year": "1992", "text": "<PERSON>, English violinist and composer (b. 1907)", "html": "1992 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English violinist and composer (b. 1907)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English violinist and composer (b. 1907)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1992", "text": "<PERSON>, American religious leader (b. 1893)", "html": "1992 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, American religious leader (b. 1893)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, American religious leader (b. 1893)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1993", "text": "<PERSON>, American actor (b. 1913)", "html": "1993 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor (b. 1913)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor (b. 1913)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1995", "text": "<PERSON>, American actress and dancer (b. 1911)", "html": "1995 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>cQue<PERSON>\" title=\"<PERSON> McQueen\"><PERSON></a>, American actress and dancer (b. 1911)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>cQue<PERSON>\" title=\"<PERSON> McQueen\"><PERSON></a>, American actress and dancer (b. 1911)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>"}]}, {"year": "1995", "text": "<PERSON>, English economist and academic, Nobel Prize laureate (b. 1907)", "html": "1995 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English economist and academic, <a href=\"https://wikipedia.org/wiki/Nobel_Memorial_Prize_in_Economic_Sciences\" title=\"Nobel Memorial Prize in Economic Sciences\">Nobel Prize</a> laureate (b. 1907)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English economist and academic, <a href=\"https://wikipedia.org/wiki/Nobel_Memorial_Prize_in_Economic_Sciences\" title=\"Nobel Memorial Prize in Economic Sciences\">Nobel Prize</a> laureate (b. 1907)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Nobel Memorial Prize in Economic Sciences", "link": "https://wikipedia.org/wiki/Nobel_Memorial_Prize_in_Economic_Sciences"}]}, {"year": "1996", "text": "<PERSON>, American cartoonist and television host (b. 1916)", "html": "1996 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American cartoonist and television host (b. 1916)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American cartoonist and television host (b. 1916)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1997", "text": "<PERSON>, Cuban-American dentist and activist (b. 1931)", "html": "1997 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Cuban-American dentist and activist (b. 1931)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Cuban-American dentist and activist (b. 1931)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "2001", "text": "<PERSON><PERSON><PERSON>, Romanian footballer (b. 1981)", "html": "2001 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Romanian footballer (b. 1981)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Romanian footballer (b. 1981)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "2001", "text": "<PERSON>, American lawyer and second chairman of the New Jersey Casino Control Commission (b. 1918)", "html": "2001 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and second chairman of the <a href=\"https://wikipedia.org/wiki/New_Jersey_Casino_Control_Commission\" title=\"New Jersey Casino Control Commission\">New Jersey Casino Control Commission</a> (b. 1918)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and second chairman of the <a href=\"https://wikipedia.org/wiki/New_Jersey_Casino_Control_Commission\" title=\"New Jersey Casino Control Commission\">New Jersey Casino Control Commission</a> (b. 1918)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "New Jersey Casino Control Commission", "link": "https://wikipedia.org/wiki/New_Jersey_Casino_Control_Commission"}]}, {"year": "2002", "text": "<PERSON>, Guyanese lawyer, politician and President of Guyana (b. 1929)", "html": "2002 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Guyanese lawyer, politician and <a href=\"https://wikipedia.org/wiki/President_of_Guyana\" title=\"President of Guyana\">President of Guyana</a> (b. 1929)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Guyanese lawyer, politician and <a href=\"https://wikipedia.org/wiki/President_of_Guyana\" title=\"President of Guyana\">President of Guyana</a> (b. 1929)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "President of Guyana", "link": "https://wikipedia.org/wiki/President_of_Guyana"}]}, {"year": "2002", "text": "<PERSON>, English singer-songwriter (b. 1952)", "html": "2002 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English singer-songwriter (b. 1952)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English singer-songwriter (b. 1952)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2004", "text": "<PERSON>, American baseball player and manager (b. 1950)", "html": "2004 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player and manager (b. 1950)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player and manager (b. 1950)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2006", "text": "<PERSON>, Russian gymnast (b. 1960)", "html": "2006 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian gymnast (b. 1960)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian gymnast (b. 1960)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2006", "text": "<PERSON><PERSON><PERSON>, Russian composer (b. 1919)", "html": "2006 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Russian composer (b. 1919)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Russian composer (b. 1919)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_Ustvolskaya"}]}, {"year": "2007", "text": "<PERSON>, Australian politician, 21st Premier of Western Australia (b. 1911)", "html": "2007 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Charles <PERSON>\"><PERSON></a>, Australian politician, 21st <a href=\"https://wikipedia.org/wiki/Premier_of_Western_Australia\" title=\"Premier of Western Australia\">Premier of Western Australia</a> (b. 1911)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Charles_<PERSON>\" title=\"Charles Court\"><PERSON></a>, Australian politician, 21st <a href=\"https://wikipedia.org/wiki/Premier_of_Western_Australia\" title=\"Premier of Western Australia\">Premier of Western Australia</a> (b. 1911)", "links": [{"title": "Charles <PERSON>", "link": "https://wikipedia.org/wiki/Charles_Court"}, {"title": "Premier of Western Australia", "link": "https://wikipedia.org/wiki/Premier_of_Western_Australia"}]}, {"year": "2007", "text": "<PERSON>, Filipino journalist and playwright (b. 1932)", "html": "2007 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Filipino journalist and playwright (b. 1932)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Filipino journalist and playwright (b. 1932)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2009", "text": "<PERSON>, Colombian rancher and politician (b. 1940)", "html": "2009 - <a href=\"https://wikipedia.org/wiki/Luis_<PERSON>_<PERSON>u%C3%A9llar\" title=\"<PERSON>\"><PERSON></a>, Colombian rancher and politician (b. 1940)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>%C3%A9llar\" title=\"<PERSON>\"><PERSON></a>, Colombian rancher and politician (b. 1940)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Luis_<PERSON>_Cu%C3%A9llar"}]}, {"year": "2009", "text": "<PERSON>, English footballer (b. 1935)", "html": "2009 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English footballer (b. 1935)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English footballer (b. 1935)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2010", "text": "<PERSON>, American soldier and announcer (b. 1921)", "html": "2010 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American soldier and announcer (b. 1921)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American soldier and announcer (b. 1921)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2012", "text": "<PERSON>, American football player and coach (b. 1916)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player and coach (b. 1916)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player and coach (b. 1916)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2012", "text": "<PERSON>, American baseball player (b. 1976)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player (b. 1976)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player (b. 1976)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2012", "text": "<PERSON>, American actor, director, producer, and screenwriter (b. 1937)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor, director, producer, and screenwriter (b. 1937)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor, director, producer, and screenwriter (b. 1937)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2012", "text": "<PERSON>, Malaysian physician and politician (b. 1939)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Malaysian physician and politician (b. 1939)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Malaysian physician and politician (b. 1939)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "2013", "text": "<PERSON><PERSON><PERSON>, Colombian singer-songwriter (b. 1956)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/Diomedes_D%C3%ADaz\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Colombian singer-songwriter (b. 1956)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Diomedes_D%C3%ADaz\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Colombian singer-songwriter (b. 1956)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Diomedes_D%C3%ADaz"}]}, {"year": "2013", "text": "<PERSON>, Danish lawyer and politician (b. 1945)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/Hans_<PERSON>%C3%A6kkerup\" title=\"<PERSON>\"><PERSON></a>, Danish lawyer and politician (b. 1945)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Hans_<PERSON>%C3%A6kkerup\" title=\"<PERSON>\"><PERSON></a>, Danish lawyer and politician (b. 1945)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Hans_H%C3%A6kkerup"}]}, {"year": "2013", "text": "<PERSON>, Swiss author, playwright, and philologist (b. 1928)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Oscar Peer\"><PERSON></a>, Swiss author, playwright, and philologist (b. 1928)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Oscar Peer\"><PERSON></a>, Swiss author, playwright, and philologist (b. 1928)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Oscar_<PERSON>"}]}, {"year": "2014", "text": "<PERSON>, American physicist and academic (b. 1924)", "html": "2014 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American physicist and academic (b. 1924)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American physicist and academic (b. 1924)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2014", "text": "<PERSON>, American actress (b. 1963)", "html": "2014 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress (b. 1963)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress (b. 1963)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2014", "text": "<PERSON>, English singer-songwriter (b. 1944)", "html": "2014 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Co<PERSON>\"><PERSON></a>, English singer-songwriter (b. 1944)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Cocker\"><PERSON></a>, English singer-songwriter (b. 1944)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2014", "text": "<PERSON>, American lawyer and politician (b. 1927)", "html": "2014 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and politician (b. 1927)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and politician (b. 1927)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2015", "text": "<PERSON>, Swedish singer-songwriter (b. 1950)", "html": "2015 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swedish singer-songwriter (b. 1950)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swedish singer-songwriter (b. 1950)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "2015", "text": "<PERSON><PERSON>-<PERSON><PERSON>, Australian activist and politician (b. 1927)", "html": "2015 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>-<PERSON>\" title=\"<PERSON><PERSON>-<PERSON>\"><PERSON><PERSON>-<PERSON></a>, Australian activist and politician (b. 1927)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>-<PERSON>\" title=\"<PERSON><PERSON>-<PERSON>\"><PERSON><PERSON>-<PERSON></a>, Australian activist and politician (b. 1927)", "links": [{"title": "<PERSON><PERSON>-<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>-<PERSON>"}]}, {"year": "2016", "text": "<PERSON>, Australian rugby league player (b. 1980)", "html": "2016 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian rugby league player (b. 1980)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian rugby league player (b. 1980)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2017", "text": "<PERSON><PERSON><PERSON>, Costa Rican painter (b. 1945)", "html": "2017 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>%C3%A1urez\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Costa Rican painter (b. 1945)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>%C3%A1urez\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Costa Rican painter (b. 1945)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_S%C3%A1urez"}]}, {"year": "2018", "text": "<PERSON>, British politician (b. 1941)", "html": "2018 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Paddy Ashdown\"><PERSON></a>, British politician (b. 1941)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Paddy Ashdown\"><PERSON></a>, British politician (b. 1941)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>down"}]}, {"year": "2018", "text": "<PERSON><PERSON><PERSON>, last survivor of the Warsaw Ghetto Uprising (b. 1924)", "html": "2018 - <a href=\"https://wikipedia.org/wiki/Simcha_Rotem\" title=\"Simcha Rotem\"><PERSON><PERSON><PERSON></a>, last survivor of the <a href=\"https://wikipedia.org/wiki/Warsaw_Ghetto_Uprising\" title=\"Warsaw Ghetto Uprising\">Warsaw Ghetto Uprising</a> (b. 1924)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>m<PERSON>_Rotem\" title=\"Simcha Rotem\"><PERSON><PERSON><PERSON></a>, last survivor of the <a href=\"https://wikipedia.org/wiki/Warsaw_Ghetto_Uprising\" title=\"Warsaw Ghetto Uprising\">Warsaw Ghetto Uprising</a> (b. 1924)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>m<PERSON>_Rotem"}, {"title": "Warsaw Ghetto Uprising", "link": "https://wikipedia.org/wiki/Warsaw_Ghetto_Uprising"}]}, {"year": "2018", "text": "<PERSON>, Indonesian guitarist (b. 1982); casualty during 2018 Sunda Strait tsunami", "html": "2018 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, Indonesian guitarist (b. 1982); casualty during <a href=\"https://wikipedia.org/wiki/2018_Sunda_Strait_tsunami\" title=\"2018 Sunda Strait tsunami\">2018 Sunda Strait tsunami</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, Indonesian guitarist (b. 1982); casualty during <a href=\"https://wikipedia.org/wiki/2018_Sunda_Strait_tsunami\" title=\"2018 Sunda Strait tsunami\">2018 Sunda Strait tsunami</a>", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "2018 Sunda Strait tsunami", "link": "https://wikipedia.org/wiki/2018_Sunda_Strait_tsunami"}]}, {"year": "2019", "text": "<PERSON>, American spiritual teacher and author (b. 1931)", "html": "2019 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American spiritual teacher and author (b. 1931)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Das<PERSON>\"><PERSON></a>, American spiritual teacher and author (b. 1931)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_Das<PERSON>"}]}]}}