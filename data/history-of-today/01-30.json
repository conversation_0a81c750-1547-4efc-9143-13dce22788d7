{"date": "January 30", "url": "https://wikipedia.org/wiki/January_30", "data": {"Events": [{"year": "1018", "text": "Poland and the Holy Roman Empire conclude the Peace of Bautzen.", "html": "1018 - <a href=\"https://wikipedia.org/wiki/Poland\" title=\"Poland\">Poland</a> and the <a href=\"https://wikipedia.org/wiki/Holy_Roman_Empire\" title=\"Holy Roman Empire\">Holy Roman Empire</a> conclude the <a href=\"https://wikipedia.org/wiki/Peace_of_Bautzen\" title=\"Peace of Bautzen\">Peace of Bautzen</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Poland\" title=\"Poland\">Poland</a> and the <a href=\"https://wikipedia.org/wiki/Holy_Roman_Empire\" title=\"Holy Roman Empire\">Holy Roman Empire</a> conclude the <a href=\"https://wikipedia.org/wiki/Peace_of_Bautzen\" title=\"Peace of Bautzen\">Peace of Bautzen</a>.", "links": [{"title": "Poland", "link": "https://wikipedia.org/wiki/Poland"}, {"title": "Holy Roman Empire", "link": "https://wikipedia.org/wiki/Holy_Roman_Empire"}, {"title": "Peace of Bautzen", "link": "https://wikipedia.org/wiki/Peace_of_Bautzen"}]}, {"year": "1287", "text": "King <PERSON><PERSON> founds the Hanthawaddy Kingdom, and proclaims independence from the Pagan Kingdom.", "html": "1287 - King <a href=\"https://wikipedia.org/wiki/Wareru\" title=\"Wareru\">Ware<PERSON></a> founds the <a href=\"https://wikipedia.org/wiki/Hanthawaddy_Kingdom\" title=\"Hanthawaddy Kingdom\">Hanthawaddy Kingdom</a>, and proclaims independence from the <a href=\"https://wikipedia.org/wiki/Pagan_Kingdom\" class=\"mw-redirect\" title=\"Pagan Kingdom\">Pagan Kingdom</a>.", "no_year_html": "King <a href=\"https://wikipedia.org/wiki/Wareru\" title=\"Wareru\">Ware<PERSON></a> founds the <a href=\"https://wikipedia.org/wiki/Hanthawaddy_Kingdom\" title=\"Hanthawaddy Kingdom\">Hanthawaddy Kingdom</a>, and proclaims independence from the <a href=\"https://wikipedia.org/wiki/Pagan_Kingdom\" class=\"mw-redirect\" title=\"Pagan Kingdom\">Pagan Kingdom</a>.", "links": [{"title": "Wareru", "link": "https://wikipedia.org/wiki/Wareru"}, {"title": "Hanthawaddy Kingdom", "link": "https://wikipedia.org/wiki/Hanthawaddy_Kingdom"}, {"title": "Pagan Kingdom", "link": "https://wikipedia.org/wiki/Pagan_Kingdom"}]}, {"year": "1607", "text": "An estimated 200 square miles (51,800 ha) along the coasts of the Bristol Channel and Severn Estuary in England are destroyed by massive flooding, resulting in an estimated 2,000 deaths.", "html": "1607 - An estimated 200 square miles (51,800 ha) along the coasts of the <a href=\"https://wikipedia.org/wiki/Bristol_Channel\" title=\"Bristol Channel\">Bristol Channel</a> and <a href=\"https://wikipedia.org/wiki/Severn_Estuary\" title=\"Severn Estuary\">Severn Estuary</a> in England are destroyed by <a href=\"https://wikipedia.org/wiki/1607_Bristol_Channel_floods\" title=\"1607 Bristol Channel floods\">massive flooding</a>, resulting in an estimated 2,000 deaths.", "no_year_html": "An estimated 200 square miles (51,800 ha) along the coasts of the <a href=\"https://wikipedia.org/wiki/Bristol_Channel\" title=\"Bristol Channel\">Bristol Channel</a> and <a href=\"https://wikipedia.org/wiki/Severn_Estuary\" title=\"Severn Estuary\">Severn Estuary</a> in England are destroyed by <a href=\"https://wikipedia.org/wiki/1607_Bristol_Channel_floods\" title=\"1607 Bristol Channel floods\">massive flooding</a>, resulting in an estimated 2,000 deaths.", "links": [{"title": "Bristol Channel", "link": "https://wikipedia.org/wiki/Bristol_Channel"}, {"title": "Severn Estuary", "link": "https://wikipedia.org/wiki/Severn_Estuary"}, {"title": "1607 Bristol Channel floods", "link": "https://wikipedia.org/wiki/1607_Bristol_Channel_floods"}]}, {"year": "1648", "text": "Eighty Years' War: The Treaty of Münster and Osnabrück is signed, ending the conflict between the Netherlands and Spain.", "html": "1648 - <a href=\"https://wikipedia.org/wiki/Eighty_Years%27_War\" title=\"Eighty Years' War\">Eighty Years' War</a>: The <a href=\"https://wikipedia.org/wiki/Peace_of_M%C3%BCnster\" title=\"Peace of Münster\">Treaty of Münster and Osnabrück</a> is signed, ending the conflict between the Netherlands and Spain.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Eighty_Years%27_War\" title=\"Eighty Years' War\">Eighty Years' War</a>: The <a href=\"https://wikipedia.org/wiki/Peace_of_M%C3%BCnster\" title=\"Peace of Münster\">Treaty of Münster and Osnabrück</a> is signed, ending the conflict between the Netherlands and Spain.", "links": [{"title": "Eighty Years' War", "link": "https://wikipedia.org/wiki/Eighty_Years%27_War"}, {"title": "Peace of Münster", "link": "https://wikipedia.org/wiki/Peace_of_M%C3%BCnster"}]}, {"year": "1649", "text": "<PERSON> of England is executed in Whitehall, London.", "html": "1649 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_England\" title=\"<PERSON> I of England\"><PERSON> of England</a> is executed in Whitehall, London.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_England\" title=\"<PERSON> of England\"><PERSON> of England</a> is executed in Whitehall, London.", "links": [{"title": "<PERSON> of England", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_England"}]}, {"year": "1661", "text": "<PERSON>, Lord Protector of the Commonwealth of England, is ritually executed more than two years after his death, on the 12th anniversary of the execution of the monarch he himself deposed.", "html": "1661 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, <a href=\"https://wikipedia.org/wiki/Lord_Protector#Cromwellian_republican_Commonwealth\" title=\"Lord Protector\">Lord Protector</a> of the <a href=\"https://wikipedia.org/wiki/Commonwealth_of_England\" title=\"Commonwealth of England\">Commonwealth of England</a>, is <a href=\"https://wikipedia.org/wiki/Posthumous_execution\" title=\"Posthumous execution\">ritually executed</a> more than two years after his death, on the 12th anniversary of the execution of <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_England\" title=\"<PERSON> of England\">the monarch</a> he himself deposed.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, <a href=\"https://wikipedia.org/wiki/Lord_Protector#Cromwellian_republican_Commonwealth\" title=\"Lord Protector\">Lord Protector</a> of the <a href=\"https://wikipedia.org/wiki/Commonwealth_of_England\" title=\"Commonwealth of England\">Commonwealth of England</a>, is <a href=\"https://wikipedia.org/wiki/Posthumous_execution\" title=\"Posthumous execution\">ritually executed</a> more than two years after his death, on the 12th anniversary of the execution of <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_England\" title=\"<PERSON> of England\">the monarch</a> he himself deposed.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Lord Pro<PERSON>tor", "link": "https://wikipedia.org/wiki/Lord_Protector#Cromwellian_republican_Commonwealth"}, {"title": "Commonwealth of England", "link": "https://wikipedia.org/wiki/Commonwealth_of_England"}, {"title": "Posthumous execution", "link": "https://wikipedia.org/wiki/Posthumous_execution"}, {"title": "<PERSON> of England", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_England"}]}, {"year": "1667", "text": "The Truce of Andrusovo is signed, ending the Russian-Polish War of 1654-1667", "html": "1667 - <a href=\"https://wikipedia.org/wiki/Truce_of_Andrusovo\" title=\"Truce of Andrusovo\">The Truce of Andrusovo</a> is signed, ending the <a href=\"https://wikipedia.org/wiki/Russo-Polish_War_(1654%E2%80%931667)\" title=\"Russo-Polish War (1654-1667)\">Russian-Polish War of 1654-1667</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Truce_of_Andrusovo\" title=\"Truce of Andrusovo\">The Truce of Andrusovo</a> is signed, ending the <a href=\"https://wikipedia.org/wiki/Russo-Polish_War_(1654%E2%80%931667)\" title=\"Russo-Polish War (1654-1667)\">Russian-Polish War of 1654-1667</a>", "links": [{"title": "Truce of Andrusovo", "link": "https://wikipedia.org/wiki/Tru<PERSON>_of_Andrusovo"}, {"title": "Russo-Polish War (1654-1667)", "link": "https://wikipedia.org/wiki/Russo-Polish_War_(1654%E2%80%931667)"}]}, {"year": "1789", "text": "Tây Sơn forces emerge victorious against Qing armies and liberate the capital Thăng Long.", "html": "1789 - <a href=\"https://wikipedia.org/wiki/T%C3%A2y_S%C6%A1n_dynasty\" title=\"Tây Sơn dynasty\">Tây Sơn</a> forces <a href=\"https://wikipedia.org/wiki/Battle_of_Ng%E1%BB%8Dc_H%E1%BB%93i-%C4%90%E1%BB%91ng_%C4%90a\" title=\"Battle of Ngọc Hồi-Đống Đa\">emerge victorious</a> against <a href=\"https://wikipedia.org/wiki/Qing_dynasty\" title=\"Qing dynasty\">Qing</a> armies and liberate the capital <a href=\"https://wikipedia.org/wiki/Th%C4%83ng_Long\" class=\"mw-redirect\" title=\"Thăng Long\">Thăng Long</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/T%C3%A2y_S%C6%A1n_dynasty\" title=\"Tây Sơn dynasty\">Tây Sơn</a> forces <a href=\"https://wikipedia.org/wiki/Battle_of_Ng%E1%BB%8Dc_H%E1%BB%93i-%C4%90%E1%BB%91ng_%C4%90a\" title=\"Battle of Ngọc Hồi-Đống Đa\">emerge victorious</a> against <a href=\"https://wikipedia.org/wiki/Qing_dynasty\" title=\"Qing dynasty\">Qing</a> armies and liberate the capital <a href=\"https://wikipedia.org/wiki/Th%C4%83ng_Long\" class=\"mw-redirect\" title=\"Thăng Long\">Thăng Long</a>.", "links": [{"title": "Tây Sơn dynasty", "link": "https://wikipedia.org/wiki/T%C3%A2y_S%C6%A1n_dynasty"}, {"title": "Battle of Ngọc Hồi-Đống Đa", "link": "https://wikipedia.org/wiki/Battle_of_Ng%E1%BB%8Dc_H%E1%BB%93i-%C4%90%E1%BB%91ng_%C4%90a"}, {"title": "Qing dynasty", "link": "https://wikipedia.org/wiki/Qing_dynasty"}, {"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Th%C4%83ng_Long"}]}, {"year": "1806", "text": "The original Lower Trenton Bridge (also called the Trenton Makes the World Takes Bridge), which spans the Delaware River between Morrisville, Pennsylvania and Trenton, New Jersey, is opened.", "html": "1806 - The original <a href=\"https://wikipedia.org/wiki/Lower_Trenton_Bridge\" title=\"Lower Trenton Bridge\">Lower Trenton Bridge</a> (also called the Trenton Makes the World Takes Bridge), which spans the <a href=\"https://wikipedia.org/wiki/Delaware_River\" title=\"Delaware River\">Delaware River</a> between <a href=\"https://wikipedia.org/wiki/Morrisville,_Bucks_County,_Pennsylvania\" title=\"Morrisville, Bucks County, Pennsylvania\">Morrisville, Pennsylvania</a> and <a href=\"https://wikipedia.org/wiki/Trenton,_New_Jersey\" title=\"Trenton, New Jersey\">Trenton, New Jersey</a>, is opened.", "no_year_html": "The original <a href=\"https://wikipedia.org/wiki/Lower_Trenton_Bridge\" title=\"Lower Trenton Bridge\">Lower Trenton Bridge</a> (also called the Trenton Makes the World Takes Bridge), which spans the <a href=\"https://wikipedia.org/wiki/Delaware_River\" title=\"Delaware River\">Delaware River</a> between <a href=\"https://wikipedia.org/wiki/Morrisville,_Bucks_County,_Pennsylvania\" title=\"Morrisville, Bucks County, Pennsylvania\">Morrisville, Pennsylvania</a> and <a href=\"https://wikipedia.org/wiki/Trenton,_New_Jersey\" title=\"Trenton, New Jersey\">Trenton, New Jersey</a>, is opened.", "links": [{"title": "Lower Trenton Bridge", "link": "https://wikipedia.org/wiki/Lower_Trenton_Bridge"}, {"title": "Delaware River", "link": "https://wikipedia.org/wiki/Delaware_River"}, {"title": "Morrisville, Bucks County, Pennsylvania", "link": "https://wikipedia.org/wiki/Morrisville,_Bucks_County,_Pennsylvania"}, {"title": "Trenton, New Jersey", "link": "https://wikipedia.org/wiki/Trenton,_New_Jersey"}]}, {"year": "1820", "text": "<PERSON> sights the Trinity Peninsula and claims the discovery of Antarctica.", "html": "1820 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> sights the <a href=\"https://wikipedia.org/wiki/Trinity_Peninsula\" title=\"Trinity Peninsula\">Trinity Peninsula</a> and claims the discovery of <a href=\"https://wikipedia.org/wiki/Antarctica\" title=\"Antarctica\">Antarctica</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> sights the <a href=\"https://wikipedia.org/wiki/Trinity_Peninsula\" title=\"Trinity Peninsula\">Trinity Peninsula</a> and claims the discovery of <a href=\"https://wikipedia.org/wiki/Antarctica\" title=\"Antarctica\">Antarctica</a>.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Trinity Peninsula", "link": "https://wikipedia.org/wiki/Trinity_Peninsula"}, {"title": "Antarctica", "link": "https://wikipedia.org/wiki/Antarctica"}]}, {"year": "1826", "text": "The Menai Suspension Bridge, considered the world's first modern suspension bridge, connecting the Isle of Anglesey to the north West coast of Wales, is opened.", "html": "1826 - The <a href=\"https://wikipedia.org/wiki/Menai_Suspension_Bridge\" title=\"Menai Suspension Bridge\">Menai Suspension Bridge</a>, considered the world's first modern suspension bridge, connecting the <a href=\"https://wikipedia.org/wiki/Isle_of_Anglesey\" class=\"mw-redirect\" title=\"Isle of Anglesey\">Isle of Anglesey</a> to the north West coast of Wales, is opened.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Menai_Suspension_Bridge\" title=\"Menai Suspension Bridge\">Menai Suspension Bridge</a>, considered the world's first modern suspension bridge, connecting the <a href=\"https://wikipedia.org/wiki/Isle_of_Anglesey\" class=\"mw-redirect\" title=\"Isle of Anglesey\">Isle of Anglesey</a> to the north West coast of Wales, is opened.", "links": [{"title": "Menai Suspension Bridge", "link": "https://wikipedia.org/wiki/Menai_Suspension_Bridge"}, {"title": "Isle of Anglesey", "link": "https://wikipedia.org/wiki/Isle_of_Anglesey"}]}, {"year": "1835", "text": "In the first assassination attempt against a President of the United States, <PERSON> attempts to shoot president <PERSON>, but fails and is subdued by a crowd, including several congressmen as well as <PERSON> himself.", "html": "1835 - In the first assassination attempt against a President of the United States, <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(failed_assassin)\" title=\"<PERSON> (failed assassin)\"><PERSON></a> attempts to shoot president <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, but fails and is subdued by a crowd, including several congressmen as well as <PERSON> himself.", "no_year_html": "In the first assassination attempt against a President of the United States, <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(failed_assassin)\" title=\"<PERSON> (failed assassin)\"><PERSON></a> attempts to shoot president <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, but fails and is subdued by a crowd, including several congressmen as well as <PERSON> himself.", "links": [{"title": "<PERSON> (failed assassin)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(failed_assassin)"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1847", "text": "Yerba Buena, California is renamed San Francisco, California.", "html": "1847 - <a href=\"https://wikipedia.org/wiki/Yerba_Buena,_California\" title=\"Yerba Buena, California\">Yerba Buena, California</a> is renamed <a href=\"https://wikipedia.org/wiki/San_Francisco,_California\" class=\"mw-redirect\" title=\"San Francisco, California\">San Francisco, California</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Yerba_Buena,_California\" title=\"Yerba Buena, California\">Yerba Buena, California</a> is renamed <a href=\"https://wikipedia.org/wiki/San_Francisco,_California\" class=\"mw-redirect\" title=\"San Francisco, California\">San Francisco, California</a>.", "links": [{"title": "Yerba <PERSON>, California", "link": "https://wikipedia.org/wiki/Yerba_Buena,_California"}, {"title": "San Francisco, California", "link": "https://wikipedia.org/wiki/San_Francisco,_California"}]}, {"year": "1858", "text": "The first Hallé concert is given in Manchester, England, marking the official founding of The Hallé orchestra as a full-time, professional orchestra.", "html": "1858 - The first Hallé concert is given in <a href=\"https://wikipedia.org/wiki/Manchester\" title=\"Manchester\">Manchester</a>, England, marking the official founding of <a href=\"https://wikipedia.org/wiki/The_Hall%C3%A9\" title=\"The Hallé\">The Hallé</a> orchestra as a full-time, professional orchestra.", "no_year_html": "The first Hallé concert is given in <a href=\"https://wikipedia.org/wiki/Manchester\" title=\"Manchester\">Manchester</a>, England, marking the official founding of <a href=\"https://wikipedia.org/wiki/The_Hall%C3%A9\" title=\"The Hallé\">The Hallé</a> orchestra as a full-time, professional orchestra.", "links": [{"title": "Manchester", "link": "https://wikipedia.org/wiki/Manchester"}, {"title": "The Hallé", "link": "https://wikipedia.org/wiki/The_Hall%C3%A9"}]}, {"year": "1862", "text": "American Civil War: The first American ironclad warship, the USS Monitor is launched.", "html": "1862 - <a href=\"https://wikipedia.org/wiki/American_Civil_War\" title=\"American Civil War\">American Civil War</a>: The first American <a href=\"https://wikipedia.org/wiki/Ironclad_warship\" title=\"Ironclad warship\">ironclad warship</a>, the <a href=\"https://wikipedia.org/wiki/USS_Monitor\" title=\"USS Monitor\">USS <i>Monitor</i></a> is launched.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/American_Civil_War\" title=\"American Civil War\">American Civil War</a>: The first American <a href=\"https://wikipedia.org/wiki/Ironclad_warship\" title=\"Ironclad warship\">ironclad warship</a>, the <a href=\"https://wikipedia.org/wiki/USS_Monitor\" title=\"USS Monitor\">USS <i>Monitor</i></a> is launched.", "links": [{"title": "American Civil War", "link": "https://wikipedia.org/wiki/American_Civil_War"}, {"title": "Ironclad warship", "link": "https://wikipedia.org/wiki/Ironclad_warship"}, {"title": "USS Monitor", "link": "https://wikipedia.org/wiki/USS_Monitor"}]}, {"year": "1889", "text": "Archduke Crown Prince <PERSON> of Austria, heir to the Austro-Hungarian crown, is found dead with his mistress Baroness <PERSON> in the Mayerling.", "html": "1889 - <a href=\"https://wikipedia.org/wiki/Arch<PERSON>ke\" title=\"Archduke\">Arch<PERSON>ke</a> <a href=\"https://wikipedia.org/wiki/Crown_Prince_<PERSON>_of_Austria\" class=\"mw-redirect\" title=\"Crown Prince <PERSON> of Austria\">Crown Prince <PERSON> of Austria</a>, heir to the <a href=\"https://wikipedia.org/wiki/Austria-Hungary\" title=\"Austria-Hungary\">Austro-Hungarian</a> crown, is found dead with his mistress <a href=\"https://wikipedia.org/wiki/Baroness_<PERSON>_<PERSON>\" title=\"Baroness <PERSON>\">Baroness <PERSON></a> in the <a href=\"https://wikipedia.org/wiki/Mayerling_Incident\" class=\"mw-redirect\" title=\"Mayerling Incident\">Mayerling</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>ke\" title=\"Archduke\">Arch<PERSON>ke</a> <a href=\"https://wikipedia.org/wiki/Crown_Prince_<PERSON>_of_Austria\" class=\"mw-redirect\" title=\"Crown Prince <PERSON> of Austria\">Crown Prince <PERSON> of Austria</a>, heir to the <a href=\"https://wikipedia.org/wiki/Austria-Hungary\" title=\"Austria-Hungary\">Austro-Hungarian</a> crown, is found dead with his mistress <a href=\"https://wikipedia.org/wiki/Baroness_<PERSON>_<PERSON>\" title=\"Baroness <PERSON>\">Baroness <PERSON></a> in the <a href=\"https://wikipedia.org/wiki/Mayerling_Incident\" class=\"mw-redirect\" title=\"Mayerling Incident\">Mayerling</a>.", "links": [{"title": "Archduke", "link": "https://wikipedia.org/wiki/Archduke"}, {"title": "Crown Prince <PERSON> of Austria", "link": "https://wikipedia.org/wiki/Crown_Prince_<PERSON>_of_Austria"}, {"title": "Austria-Hungary", "link": "https://wikipedia.org/wiki/Austria-Hungary"}, {"title": "Baroness <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "Mayerling Incident", "link": "https://wikipedia.org/wiki/Mayerling_Incident"}]}, {"year": "1902", "text": "The first Anglo-Japanese Alliance is signed in London.", "html": "1902 - The first <a href=\"https://wikipedia.org/wiki/Anglo-Japanese_Alliance\" title=\"Anglo-Japanese Alliance\">Anglo-Japanese Alliance</a> is signed in <a href=\"https://wikipedia.org/wiki/London\" title=\"London\">London</a>.", "no_year_html": "The first <a href=\"https://wikipedia.org/wiki/Anglo-Japanese_Alliance\" title=\"Anglo-Japanese Alliance\">Anglo-Japanese Alliance</a> is signed in <a href=\"https://wikipedia.org/wiki/London\" title=\"London\">London</a>.", "links": [{"title": "Anglo-Japanese Alliance", "link": "https://wikipedia.org/wiki/Anglo-Japanese_Alliance"}, {"title": "London", "link": "https://wikipedia.org/wiki/London"}]}, {"year": "1908", "text": "Indian pacifist and leader <PERSON><PERSON> is released from prison by <PERSON> after being tried and sentenced to two months in jail earlier in the month.", "html": "1908 - Indian pacifist and leader <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON></a> is released from prison by <a href=\"https://wikipedia.org/wiki/Jan_<PERSON>._Smuts\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a> after being tried and sentenced to two months in jail earlier in the month.", "no_year_html": "Indian pacifist and leader <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON></a> is released from prison by <a href=\"https://wikipedia.org/wiki/Jan_<PERSON>._Smuts\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a> after being tried and sentenced to two months in jail earlier in the month.", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_Smuts"}]}, {"year": "1911", "text": "The destroyer <PERSON> Terry makes the first airplane rescue at sea saving the life of <PERSON> 16 kilometres (10 miles) from Havana, Cuba.", "html": "1911 - The destroyer <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(DD-25)\" title=\"<PERSON> Terry (DD-25)\">USS <i><PERSON></i></a> makes the first <a href=\"https://wikipedia.org/wiki/Airplane\" title=\"Airplane\">airplane</a> rescue at sea saving the life of <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>dy\" title=\"<PERSON>dy\"><PERSON></a> 16 kilometres (10 miles) from <a href=\"https://wikipedia.org/wiki/Havana,_Cuba\" class=\"mw-redirect\" title=\"Havana, Cuba\">Havana, Cuba</a>.", "no_year_html": "The destroyer <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(DD-25)\" title=\"<PERSON> Terry (DD-25)\">USS <i><PERSON></i></a> makes the first <a href=\"https://wikipedia.org/wiki/Airplane\" title=\"Airplane\">airplane</a> rescue at sea saving the life of <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>dy\" title=\"<PERSON>dy\"><PERSON></a> 16 kilometres (10 miles) from <a href=\"https://wikipedia.org/wiki/Havana,_Cuba\" class=\"mw-redirect\" title=\"Havana, Cuba\">Havana, Cuba</a>.", "links": [{"title": "<PERSON> (DD-25)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(DD-25)"}, {"title": "Airplane", "link": "https://wikipedia.org/wiki/Airplane"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>"}, {"title": "Havana, Cuba", "link": "https://wikipedia.org/wiki/Havana,_Cuba"}]}, {"year": "1920", "text": "Japanese carmaker Mazda is founded, initially as a cork-producing company.", "html": "1920 - Japanese carmaker <a href=\"https://wikipedia.org/wiki/Mazda\" title=\"Mazda\">Mazda</a> is founded, initially as a <a href=\"https://wikipedia.org/wiki/Cork_(plug)\" class=\"mw-redirect\" title=\"Cork (plug)\">cork</a>-producing company.", "no_year_html": "Japanese carmaker <a href=\"https://wikipedia.org/wiki/Mazda\" title=\"Mazda\">Mazda</a> is founded, initially as a <a href=\"https://wikipedia.org/wiki/Cork_(plug)\" class=\"mw-redirect\" title=\"Cork (plug)\">cork</a>-producing company.", "links": [{"title": "Mazda", "link": "https://wikipedia.org/wiki/Mazda"}, {"title": "Cork (plug)", "link": "https://wikipedia.org/wiki/Cork_(plug)"}]}, {"year": "1925", "text": "The Government of Turkey expels Patriarch <PERSON> from Istanbul.", "html": "1925 - The Government of <a href=\"https://wikipedia.org/wiki/Turkey\" title=\"Turkey\">Turkey</a> expels <a href=\"https://wikipedia.org/wiki/Constantine_VI_of_Constantinople\" title=\"Constantine VI of Constantinople\">Patriarch <PERSON> VI</a> from <a href=\"https://wikipedia.org/wiki/Istanbul\" title=\"Istanbul\">Istanbul</a>.", "no_year_html": "The Government of <a href=\"https://wikipedia.org/wiki/Turkey\" title=\"Turkey\">Turkey</a> expels <a href=\"https://wikipedia.org/wiki/Constantine_VI_of_Constantinople\" title=\"Constantine VI of Constantinople\">Patriarch <PERSON> VI</a> from <a href=\"https://wikipedia.org/wiki/Istanbul\" title=\"Istanbul\">Istanbul</a>.", "links": [{"title": "Turkey", "link": "https://wikipedia.org/wiki/Turkey"}, {"title": "Constantine VI of Constantinople", "link": "https://wikipedia.org/wiki/Constantine_VI_of_Constantinople"}, {"title": "Istanbul", "link": "https://wikipedia.org/wiki/Istanbul"}]}, {"year": "1930", "text": "The Politburo of the Communist Party of the Soviet Union orders the confiscation of lands belonging to the Kulaks in a campaign of Dekulakization, resulting in the executions and forced deportations of millions.", "html": "1930 - The <a href=\"https://wikipedia.org/wiki/Politburo_of_the_Communist_Party_of_the_Soviet_Union\" title=\"Politburo of the Communist Party of the Soviet Union\">Politburo of the Communist Party of the Soviet Union</a> orders the confiscation of lands belonging to the Kulaks in a campaign of <a href=\"https://wikipedia.org/wiki/Dekulakization\" title=\"Dekulakization\">Dekulakization</a>, resulting in the executions and forced deportations of millions.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Politburo_of_the_Communist_Party_of_the_Soviet_Union\" title=\"Politburo of the Communist Party of the Soviet Union\">Politburo of the Communist Party of the Soviet Union</a> orders the confiscation of lands belonging to the Kulaks in a campaign of <a href=\"https://wikipedia.org/wiki/Dekulakization\" title=\"Dekulakization\">Dekulakization</a>, resulting in the executions and forced deportations of millions.", "links": [{"title": "Politburo of the Communist Party of the Soviet Union", "link": "https://wikipedia.org/wiki/Politburo_of_the_Communist_Party_of_the_Soviet_Union"}, {"title": "Dekulakization", "link": "https://wikipedia.org/wiki/Dekulakization"}]}, {"year": "1933", "text": "<PERSON>'s rise to power: <PERSON> takes office as the Chancellor of Germany.", "html": "1933 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%27s_rise_to_power\" title=\"<PERSON>'s rise to power\"><PERSON>'s rise to power</a>: <PERSON> takes office as the Chancellor of Germany.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%27s_rise_to_power\" title=\"<PERSON>'s rise to power\"><PERSON>'s rise to power</a>: <PERSON> takes office as the Chancellor of Germany.", "links": [{"title": "<PERSON>'s rise to power", "link": "https://wikipedia.org/wiki/Adolf_Hitler%27s_rise_to_power"}]}, {"year": "1939", "text": "During a speech in the Reichstag, <PERSON> makes a prediction about the end of the Jewish race in Europe if another world war were to occur.", "html": "1939 - During a <a href=\"https://wikipedia.org/wiki/30_January_1939_Reichstag_speech\" title=\"30 January 1939 Reichstag speech\">speech</a> in the <a href=\"https://wikipedia.org/wiki/Reichstag_(Nazi_Germany)\" title=\"Reichstag (Nazi Germany)\">Reichstag</a>, <a href=\"https://wikipedia.org/wiki/Adolf_Hitler\" title=\"Adolf Hitler\">Adolf <PERSON></a> makes a <a href=\"https://wikipedia.org/wiki/Hitler%27s_prophecy\" title=\"Hitler's prophecy\">prediction</a> about the end of the <a href=\"https://wikipedia.org/wiki/Jews\" title=\"Jews\">Jewish race</a> in Europe if another <a href=\"https://wikipedia.org/wiki/World_war\" title=\"World war\">world war</a> were to occur.", "no_year_html": "During a <a href=\"https://wikipedia.org/wiki/30_January_1939_Reichstag_speech\" title=\"30 January 1939 Reichstag speech\">speech</a> in the <a href=\"https://wikipedia.org/wiki/Reichstag_(Nazi_Germany)\" title=\"Reichstag (Nazi Germany)\">Reichstag</a>, <a href=\"https://wikipedia.org/wiki/Adolf_Hitler\" title=\"Adolf Hitler\">Adolf <PERSON></a> makes a <a href=\"https://wikipedia.org/wiki/Hitler%27s_prophecy\" title=\"Hitler's prophecy\">prediction</a> about the end of the <a href=\"https://wikipedia.org/wiki/Jews\" title=\"Jews\">Jewish race</a> in Europe if another <a href=\"https://wikipedia.org/wiki/World_war\" title=\"World war\">world war</a> were to occur.", "links": [{"title": "30 January 1939 Reichstag speech", "link": "https://wikipedia.org/wiki/30_January_1939_Reichstag_speech"}, {"title": "Reichstag (Nazi Germany)", "link": "https://wikipedia.org/wiki/Reichstag_(Nazi_Germany)"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Hitler's prophecy", "link": "https://wikipedia.org/wiki/Hitler%27s_prophecy"}, {"title": "Jews", "link": "https://wikipedia.org/wiki/Jews"}, {"title": "World war", "link": "https://wikipedia.org/wiki/World_war"}]}, {"year": "1942", "text": "World War II: Japanese forces invade the island of Ambon in the Dutch East Indies. Some 300 captured Allied troops are killed after the surrender. One-quarter of the remaining POWs remain alive at the end of the war.", "html": "1942 - <a href=\"https://wikipedia.org/wiki/World_War_II\" title=\"World War II\">World War II</a>: Japanese forces <a href=\"https://wikipedia.org/wiki/Battle_of_Ambon\" title=\"Battle of Ambon\">invade the island of Ambon</a> in the <a href=\"https://wikipedia.org/wiki/Dutch_East_Indies\" title=\"Dutch East Indies\">Dutch East Indies</a>. Some 300 captured Allied troops are killed after the surrender. One-quarter of the remaining POWs remain alive at the end of the war.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/World_War_II\" title=\"World War II\">World War II</a>: Japanese forces <a href=\"https://wikipedia.org/wiki/Battle_of_Ambon\" title=\"Battle of Ambon\">invade the island of Ambon</a> in the <a href=\"https://wikipedia.org/wiki/Dutch_East_Indies\" title=\"Dutch East Indies\">Dutch East Indies</a>. Some 300 captured Allied troops are killed after the surrender. One-quarter of the remaining POWs remain alive at the end of the war.", "links": [{"title": "World War II", "link": "https://wikipedia.org/wiki/World_War_II"}, {"title": "Battle of Ambon", "link": "https://wikipedia.org/wiki/Battle_of_Ambon"}, {"title": "Dutch East Indies", "link": "https://wikipedia.org/wiki/Dutch_East_Indies"}]}, {"year": "1944", "text": "World War II: The Battle of Cisterna, part of Operation Shingle, begins in central Italy.", "html": "1944 - World War II: The <a href=\"https://wikipedia.org/wiki/Battle_of_Cisterna\" title=\"Battle of Cisterna\">Battle of Cisterna</a>, part of <a href=\"https://wikipedia.org/wiki/Operation_Shingle\" class=\"mw-redirect\" title=\"Operation Shingle\">Operation Shingle</a>, begins in central Italy.", "no_year_html": "World War II: The <a href=\"https://wikipedia.org/wiki/Battle_of_Cisterna\" title=\"Battle of Cisterna\">Battle of Cisterna</a>, part of <a href=\"https://wikipedia.org/wiki/Operation_Shingle\" class=\"mw-redirect\" title=\"Operation Shingle\">Operation Shingle</a>, begins in central Italy.", "links": [{"title": "Battle of Cisterna", "link": "https://wikipedia.org/wiki/Battle_of_Cisterna"}, {"title": "Operation Shingle", "link": "https://wikipedia.org/wiki/Operation_Shingle"}]}, {"year": "1945", "text": "World War II: The <PERSON>, overfilled with German refugees, sinks in the Baltic Sea after being torpedoed by a Soviet submarine, killing approximately 9,500 people.", "html": "1945 - World War II: The <i><a href=\"https://wikipedia.org/wiki/KdF_Ship_Wilhelm_<PERSON>\" class=\"mw-redirect\" title=\"KdF Ship Wilhelm <PERSON>off\"><PERSON></a></i>, overfilled with German <a href=\"https://wikipedia.org/wiki/Refugee\" title=\"Refugee\">refugees</a>, sinks in the <a href=\"https://wikipedia.org/wiki/Baltic_Sea\" title=\"Baltic Sea\">Baltic Sea</a> after being <a href=\"https://wikipedia.org/wiki/Torpedo\" title=\"Torpedo\">torpedoed</a> by a <a href=\"https://wikipedia.org/wiki/Soviet_Union\" title=\"Soviet Union\">Soviet</a> <a href=\"https://wikipedia.org/wiki/Soviet_submarine_S-13\" title=\"Soviet submarine S-13\">submarine</a>, killing approximately 9,500 people.", "no_year_html": "World War II: The <i><a href=\"https://wikipedia.org/wiki/KdF_Ship_Wilhelm_<PERSON>\" class=\"mw-redirect\" title=\"KdF Ship Wilhelm <PERSON>\"><PERSON></a></i>, overfilled with German <a href=\"https://wikipedia.org/wiki/Refugee\" title=\"Refugee\">refugees</a>, sinks in the <a href=\"https://wikipedia.org/wiki/Baltic_Sea\" title=\"Baltic Sea\">Baltic Sea</a> after being <a href=\"https://wikipedia.org/wiki/Torpedo\" title=\"Torpedo\">torpedoed</a> by a <a href=\"https://wikipedia.org/wiki/Soviet_Union\" title=\"Soviet Union\">Soviet</a> <a href=\"https://wikipedia.org/wiki/Soviet_submarine_S-13\" title=\"Soviet submarine S-13\">submarine</a>, killing approximately 9,500 people.", "links": [{"title": "KdF Ship Wilhelm <PERSON>", "link": "https://wikipedia.org/wiki/KdF_<PERSON>_<PERSON>_<PERSON>"}, {"title": "Refugee", "link": "https://wikipedia.org/wiki/Refugee"}, {"title": "Baltic Sea", "link": "https://wikipedia.org/wiki/Baltic_Sea"}, {"title": "Torped<PERSON>", "link": "https://wikipedia.org/wiki/Torpedo"}, {"title": "Soviet Union", "link": "https://wikipedia.org/wiki/Soviet_Union"}, {"title": "Soviet submarine S-13", "link": "https://wikipedia.org/wiki/Soviet_submarine_S-13"}]}, {"year": "1945", "text": "World War II: Raid at Cabanatuan: One hundred and twenty-six American Rangers and Filipino resistance fighters liberate over 500 Allied prisoners from the Japanese-controlled Cabanatuan POW camp.", "html": "1945 - World War II: <a href=\"https://wikipedia.org/wiki/Raid_at_Cabanatuan\" title=\"Raid at Cabanatuan\">Raid at Cabanatuan</a>: One hundred and twenty-six American Rangers and Filipino resistance fighters liberate over 500 Allied prisoners from the Japanese-controlled Cabanatuan POW camp.", "no_year_html": "World War II: <a href=\"https://wikipedia.org/wiki/Raid_at_Cabanatuan\" title=\"Raid at Cabanatuan\">Raid at Cabanatuan</a>: One hundred and twenty-six American Rangers and Filipino resistance fighters liberate over 500 Allied prisoners from the Japanese-controlled Cabanatuan POW camp.", "links": [{"title": "Raid at Cabanatuan", "link": "https://wikipedia.org/wiki/Raid_at_Cabanatuan"}]}, {"year": "1948", "text": "British South American Airways' Tudor IV Star Tiger disappears over the Bermuda Triangle.", "html": "1948 - <a href=\"https://wikipedia.org/wiki/British_South_American_Airways\" title=\"British South American Airways\">British South American Airways</a>' <a href=\"https://wikipedia.org/wiki/BSAA_Star_Tiger_disappearance\" title=\"BSAA Star Tiger disappearance\">Tudor IV Star Tiger disappears</a> over the <a href=\"https://wikipedia.org/wiki/Bermuda_Triangle\" title=\"Bermuda Triangle\">Bermuda Triangle</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/British_South_American_Airways\" title=\"British South American Airways\">British South American Airways</a>' <a href=\"https://wikipedia.org/wiki/BSAA_Star_Tiger_disappearance\" title=\"BSAA Star Tiger disappearance\">Tudor IV Star Tiger disappears</a> over the <a href=\"https://wikipedia.org/wiki/Bermuda_Triangle\" title=\"Bermuda Triangle\">Bermuda Triangle</a>.", "links": [{"title": "British South American Airways", "link": "https://wikipedia.org/wiki/British_South_American_Airways"}, {"title": "BSAA Star Tiger disappearance", "link": "https://wikipedia.org/wiki/BSAA_Star_Tiger_disappearance"}, {"title": "Bermuda Triangle", "link": "https://wikipedia.org/wiki/Bermuda_Triangle"}]}, {"year": "1948", "text": "Following the assassination of <PERSON><PERSON><PERSON> in his home compound, India's prime minister, <PERSON><PERSON><PERSON><PERSON>, broadcasts to the nation, saying \"The light has gone out of our lives\". The date of the assassination becomes observed as \"Martyrs' Day\" in India.", "html": "1948 - Following the <a href=\"https://wikipedia.org/wiki/Assassination_of_<PERSON><PERSON><PERSON>_<PERSON>\" title=\"Assassination of <PERSON><PERSON><PERSON>\">assassination of <PERSON><PERSON><PERSON></a> in his home compound, <a href=\"https://wikipedia.org/wiki/India\" title=\"India\">India</a>'s prime minister, <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_Nehru\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, broadcasts to the nation, saying \"<a href=\"https://wikipedia.org/wiki/The_light_has_gone_out_of_our_lives\" title=\"The light has gone out of our lives\">The light has gone out of our lives</a>\". The date of the assassination becomes observed as \"<a href=\"https://wikipedia.org/wiki/Martyrs%27_Day_(India)\" title=\"Martyrs' Day (India)\">Martyrs' Day</a>\" in India.", "no_year_html": "Following the <a href=\"https://wikipedia.org/wiki/Assassination_of_<PERSON><PERSON><PERSON>_<PERSON>\" title=\"Assassination of <PERSON><PERSON><PERSON>\">assassination of <PERSON><PERSON><PERSON></a> in his home compound, <a href=\"https://wikipedia.org/wiki/India\" title=\"India\">India</a>'s prime minister, <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, broadcasts to the nation, saying \"<a href=\"https://wikipedia.org/wiki/The_light_has_gone_out_of_our_lives\" title=\"The light has gone out of our lives\">The light has gone out of our lives</a>\". The date of the assassination becomes observed as \"<a href=\"https://wikipedia.org/wiki/Martyrs%27_Day_(India)\" title=\"Martyrs' Day (India)\">Martyrs' Day</a>\" in India.", "links": [{"title": "Assassination of <PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Assassination_of_<PERSON><PERSON><PERSON>_<PERSON>"}, {"title": "India", "link": "https://wikipedia.org/wiki/India"}, {"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>"}, {"title": "The light has gone out of our lives", "link": "https://wikipedia.org/wiki/The_light_has_gone_out_of_our_lives"}, {"title": "Martyrs' Day (India)", "link": "https://wikipedia.org/wiki/Martyrs%27_Day_(India)"}]}, {"year": "1956", "text": "In the United States, Civil Rights Movement leader <PERSON>'s home is bombed in retaliation for the Montgomery bus boycott.", "html": "1956 - In the United States, <a href=\"https://wikipedia.org/wiki/Civil_Rights_Movement\" class=\"mw-redirect\" title=\"Civil Rights Movement\">Civil Rights Movement</a> leader <a href=\"https://wikipedia.org/wiki/<PERSON>.\" title=\"<PERSON> Jr.\"><PERSON> Jr.</a>'s home is bombed in retaliation for the <a href=\"https://wikipedia.org/wiki/Montgomery_bus_boycott\" title=\"Montgomery bus boycott\">Montgomery bus boycott</a>.", "no_year_html": "In the United States, <a href=\"https://wikipedia.org/wiki/Civil_Rights_Movement\" class=\"mw-redirect\" title=\"Civil Rights Movement\">Civil Rights Movement</a> leader <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>.\" title=\"<PERSON>.\"><PERSON> Jr.</a>'s home is bombed in retaliation for the <a href=\"https://wikipedia.org/wiki/Montgomery_bus_boycott\" title=\"Montgomery bus boycott\">Montgomery bus boycott</a>.", "links": [{"title": "Civil Rights Movement", "link": "https://wikipedia.org/wiki/Civil_Rights_Movement"}, {"title": "<PERSON> Jr.", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>."}, {"title": "Montgomery bus boycott", "link": "https://wikipedia.org/wiki/Montgomery_bus_boycott"}]}, {"year": "1959", "text": "The forces of the Sultanate of Muscat occupy the last strongholds of the Imamate of Oman, Saiq and Shuraijah, marking the end of Jebel <PERSON> War in Oman.", "html": "1959 - The forces of the Sultanate of Muscat occupy the last strongholds of the Imamate of Oman, <a href=\"https://wikipedia.org/wiki/Saiq\" title=\"Saiq\">Sai<PERSON></a> and Shuraijah, marking the end of <a href=\"https://wikipedia.org/wiki/Jebel_Akhdar_War\" title=\"Jebel Akhdar War\">Jebel <PERSON>khdar War</a> in <a href=\"https://wikipedia.org/wiki/Oman\" title=\"Oman\">Oman</a>.", "no_year_html": "The forces of the Sultanate of Muscat occupy the last strongholds of the Imamate of Oman, <a href=\"https://wikipedia.org/wiki/Saiq\" title=\"Saiq\">Sai<PERSON></a> and Shuraijah, marking the end of <a href=\"https://wikipedia.org/wiki/Jebel_Akhdar_War\" title=\"Jebel Akhdar War\">Jebel <PERSON>dar War</a> in <a href=\"https://wikipedia.org/wiki/Oman\" title=\"Oman\">Oman</a>.", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Saiq"}, {"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_War"}, {"title": "Oman", "link": "https://wikipedia.org/wiki/Oman"}]}, {"year": "1959", "text": "MS <PERSON>, specifically designed to operate in icebound seas, strikes an iceberg on her maiden voyage and sinks, killing all 95 aboard.", "html": "1959 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"MS <PERSON>\">MS <i><PERSON></i></a>, specifically designed to operate in icebound seas, strikes an iceberg on her maiden voyage and sinks, killing all 95 aboard.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"MS <PERSON>\">MS <i><PERSON></i></a>, specifically designed to operate in icebound seas, strikes an iceberg on her maiden voyage and sinks, killing all 95 aboard.", "links": [{"title": "MS <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_Hans_He<PERSON>"}]}, {"year": "1960", "text": "The African National Party is founded in Chad, through the merger of traditionalist parties.", "html": "1960 - The <a href=\"https://wikipedia.org/wiki/African_National_Party\" title=\"African National Party\">African National Party</a> is founded in <a href=\"https://wikipedia.org/wiki/Chad\" title=\"Chad\">Chad</a>, through the merger of traditionalist parties.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/African_National_Party\" title=\"African National Party\">African National Party</a> is founded in <a href=\"https://wikipedia.org/wiki/Chad\" title=\"Chad\">Chad</a>, through the merger of traditionalist parties.", "links": [{"title": "African National Party", "link": "https://wikipedia.org/wiki/African_National_Party"}, {"title": "Chad", "link": "https://wikipedia.org/wiki/Chad"}]}, {"year": "1964", "text": "In a bloodless coup, General <PERSON><PERSON><PERSON><PERSON> overthrows General <PERSON><PERSON><PERSON><PERSON>'s military junta in South Vietnam.", "html": "1964 - In a bloodless coup, General <a href=\"https://wikipedia.org/wiki/Nguy%E1%BB%85n_Kh%C3%A1nh\" title=\"<PERSON>uy<PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a> <a href=\"https://wikipedia.org/wiki/January_1964_South_Vietnamese_coup\" title=\"January 1964 South Vietnamese coup\">overthrows</a> General <a href=\"https://wikipedia.org/wiki/D%C6%B0%C6%A1ng_V%C4%83n_Minh\" title=\"Dương Văn <PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>'s military junta in <a href=\"https://wikipedia.org/wiki/South_Vietnam\" title=\"South Vietnam\">South Vietnam</a>.", "no_year_html": "In a bloodless coup, General <a href=\"https://wikipedia.org/wiki/Nguy%E1%BB%85n_Kh%C3%A1nh\" title=\"<PERSON>uy<PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a> <a href=\"https://wikipedia.org/wiki/January_1964_South_Vietnamese_coup\" title=\"January 1964 South Vietnamese coup\">overthrows</a> General <a href=\"https://wikipedia.org/wiki/D%C6%B0%C6%A1ng_V%C4%83n_Minh\" title=\"Dương Văn Minh\"><PERSON><PERSON><PERSON><PERSON></a>'s military junta in <a href=\"https://wikipedia.org/wiki/South_Vietnam\" title=\"South Vietnam\">South Vietnam</a>.", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Nguy%E1%BB%85n_Kh%C3%A1nh"}, {"title": "January 1964 South Vietnamese coup", "link": "https://wikipedia.org/wiki/January_1964_South_Vietnamese_coup"}, {"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/D%C6%B0%C6%A1ng_V%C4%83n_Minh"}, {"title": "South Vietnam", "link": "https://wikipedia.org/wiki/South_Vietnam"}]}, {"year": "1968", "text": "Vietnam War: Tet Offensive launch by forces of the Viet Cong and North Vietnamese Army against South Vietnam, the United States, and their allies.", "html": "1968 - <a href=\"https://wikipedia.org/wiki/Vietnam_War\" title=\"Vietnam War\">Vietnam War</a>: <a href=\"https://wikipedia.org/wiki/Tet_Offensive\" title=\"Tet Offensive\">Tet Offensive</a> launch by forces of the <a href=\"https://wikipedia.org/wiki/Viet_Cong\" title=\"Viet Cong\">Viet Cong</a> and <a href=\"https://wikipedia.org/wiki/People%27s_Army_of_Vietnam\" title=\"People's Army of Vietnam\">North Vietnamese Army</a> against South Vietnam, the United States, and their allies.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Vietnam_War\" title=\"Vietnam War\">Vietnam War</a>: <a href=\"https://wikipedia.org/wiki/Tet_Offensive\" title=\"Tet Offensive\">Tet Offensive</a> launch by forces of the <a href=\"https://wikipedia.org/wiki/Viet_Cong\" title=\"Viet Cong\">Viet Cong</a> and <a href=\"https://wikipedia.org/wiki/People%27s_Army_of_Vietnam\" title=\"People's Army of Vietnam\">North Vietnamese Army</a> against South Vietnam, the United States, and their allies.", "links": [{"title": "Vietnam War", "link": "https://wikipedia.org/wiki/Vietnam_War"}, {"title": "Tet Offensive", "link": "https://wikipedia.org/wiki/Tet_Offensive"}, {"title": "Viet Cong", "link": "https://wikipedia.org/wiki/Viet_Cong"}, {"title": "People's Army of Vietnam", "link": "https://wikipedia.org/wiki/People%27s_Army_of_Vietnam"}]}, {"year": "1969", "text": "The Beatles' last public performance, on the roof of Apple Records in London. The impromptu concert is broken up by the police.", "html": "1969 - <a href=\"https://wikipedia.org/wiki/The_Beatles\" title=\"The Beatles\">The Beatles</a>' last public performance, on the roof of <a href=\"https://wikipedia.org/wiki/Apple_Records\" title=\"Apple Records\">Apple Records</a> in London. The <a href=\"https://wikipedia.org/wiki/The_Beatles%27_rooftop_concert\" title=\"The Beatles' rooftop concert\">impromptu concert</a> is broken up by the police.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/The_Beatles\" title=\"The Beatles\">The Beatles</a>' last public performance, on the roof of <a href=\"https://wikipedia.org/wiki/Apple_Records\" title=\"Apple Records\">Apple Records</a> in London. The <a href=\"https://wikipedia.org/wiki/The_Beatles%27_rooftop_concert\" title=\"The Beatles' rooftop concert\">impromptu concert</a> is broken up by the police.", "links": [{"title": "The Beatles", "link": "https://wikipedia.org/wiki/The_Beatles"}, {"title": "Apple Records", "link": "https://wikipedia.org/wiki/Apple_Records"}, {"title": "The Beatles' rooftop concert", "link": "https://wikipedia.org/wiki/The_Beatles%27_rooftop_concert"}]}, {"year": "1972", "text": "The Troubles: Bloody Sunday: British paratroopers open fire on anti-internment marchers in Derry, Northern Ireland, killing 13 people; another person later dies of injuries sustained.", "html": "1972 - <a href=\"https://wikipedia.org/wiki/The_Troubles\" title=\"The Troubles\">The Troubles</a>: <a href=\"https://wikipedia.org/wiki/Bloody_Sunday_(1972)\" title=\"Bloody Sunday (1972)\">Bloody Sunday</a>: <a href=\"https://wikipedia.org/wiki/Parachute_Regiment_(United_Kingdom)\" title=\"Parachute Regiment (United Kingdom)\">British paratroopers</a> open fire on anti-internment marchers in <a href=\"https://wikipedia.org/wiki/Derry\" title=\"Derry\">Derry</a>, Northern Ireland, killing 13 people; another person later dies of injuries sustained.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/The_Troubles\" title=\"The Troubles\">The Troubles</a>: <a href=\"https://wikipedia.org/wiki/Bloody_Sunday_(1972)\" title=\"Bloody Sunday (1972)\">Bloody Sunday</a>: <a href=\"https://wikipedia.org/wiki/Parachute_Regiment_(United_Kingdom)\" title=\"Parachute Regiment (United Kingdom)\">British paratroopers</a> open fire on anti-internment marchers in <a href=\"https://wikipedia.org/wiki/Derry\" title=\"Derry\">Derry</a>, Northern Ireland, killing 13 people; another person later dies of injuries sustained.", "links": [{"title": "The Troubles", "link": "https://wikipedia.org/wiki/The_Troubles"}, {"title": "Bloody Sunday (1972)", "link": "https://wikipedia.org/wiki/Bloody_Sunday_(1972)"}, {"title": "Parachute Regiment (United Kingdom)", "link": "https://wikipedia.org/wiki/Parachute_Regiment_(United_Kingdom)"}, {"title": "Derry", "link": "https://wikipedia.org/wiki/Derry"}]}, {"year": "1972", "text": "Pakistan leaves the Commonwealth of Nations in protest of its recognition of breakaway Bangladesh.", "html": "1972 - <a href=\"https://wikipedia.org/wiki/Pakistan\" title=\"Pakistan\">Pakistan</a> leaves the <a href=\"https://wikipedia.org/wiki/Commonwealth_of_Nations\" title=\"Commonwealth of Nations\">Commonwealth of Nations</a> in protest of its recognition of breakaway <a href=\"https://wikipedia.org/wiki/Bangladesh\" title=\"Bangladesh\">Bangladesh</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Pakistan\" title=\"Pakistan\">Pakistan</a> leaves the <a href=\"https://wikipedia.org/wiki/Commonwealth_of_Nations\" title=\"Commonwealth of Nations\">Commonwealth of Nations</a> in protest of its recognition of breakaway <a href=\"https://wikipedia.org/wiki/Bangladesh\" title=\"Bangladesh\">Bangladesh</a>.", "links": [{"title": "Pakistan", "link": "https://wikipedia.org/wiki/Pakistan"}, {"title": "Commonwealth of Nations", "link": "https://wikipedia.org/wiki/Commonwealth_of_Nations"}, {"title": "Bangladesh", "link": "https://wikipedia.org/wiki/Bangladesh"}]}, {"year": "1974", "text": "Pan Am Flight 806 crashes near Pago Pago International Airport in American Samoa, killing 97.", "html": "1974 - <a href=\"https://wikipedia.org/wiki/Pan_Am_Flight_806\" title=\"Pan Am Flight 806\">Pan Am Flight 806</a> crashes near <a href=\"https://wikipedia.org/wiki/Pago_Pago_International_Airport\" title=\"Pago Pago International Airport\">Pago Pago International Airport</a> in <a href=\"https://wikipedia.org/wiki/American_Samoa\" title=\"American Samoa\">American Samoa</a>, killing 97.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Pan_Am_Flight_806\" title=\"Pan Am Flight 806\">Pan Am Flight 806</a> crashes near <a href=\"https://wikipedia.org/wiki/Pago_Pago_International_Airport\" title=\"Pago Pago International Airport\">Pago Pago International Airport</a> in <a href=\"https://wikipedia.org/wiki/American_Samoa\" title=\"American Samoa\">American Samoa</a>, killing 97.", "links": [{"title": "Pan Am Flight 806", "link": "https://wikipedia.org/wiki/Pan_Am_Flight_806"}, {"title": "Pago Pago International Airport", "link": "https://wikipedia.org/wiki/Pago_Pago_International_Airport"}, {"title": "American Samoa", "link": "https://wikipedia.org/wiki/American_Samoa"}]}, {"year": "1975", "text": "The Monitor National Marine Sanctuary is established as the first United States National Marine Sanctuary.", "html": "1975 - The <a href=\"https://wikipedia.org/wiki/Monitor_National_Marine_Sanctuary\" title=\"Monitor National Marine Sanctuary\">Monitor National Marine Sanctuary</a> is established as the first <a href=\"https://wikipedia.org/wiki/United_States_National_Marine_Sanctuary\" class=\"mw-redirect\" title=\"United States National Marine Sanctuary\">United States National Marine Sanctuary</a>.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Monitor_National_Marine_Sanctuary\" title=\"Monitor National Marine Sanctuary\">Monitor National Marine Sanctuary</a> is established as the first <a href=\"https://wikipedia.org/wiki/United_States_National_Marine_Sanctuary\" class=\"mw-redirect\" title=\"United States National Marine Sanctuary\">United States National Marine Sanctuary</a>.", "links": [{"title": "Monitor National Marine Sanctuary", "link": "https://wikipedia.org/wiki/Monitor_National_Marine_Sanctuary"}, {"title": "United States National Marine Sanctuary", "link": "https://wikipedia.org/wiki/United_States_National_Marine_Sanctuary"}]}, {"year": "1975", "text": "Turkish Airlines Flight 345 crashes into the Sea of Marmara near Istanbul Yeşilköy Airport, killing 42.", "html": "1975 - <a href=\"https://wikipedia.org/wiki/Turkish_Airlines_Flight_345\" title=\"Turkish Airlines Flight 345\">Turkish Airlines Flight 345</a> crashes into the <a href=\"https://wikipedia.org/wiki/Sea_of_Marmara\" title=\"Sea of Marmara\">Sea of Marmara</a> near <a href=\"https://wikipedia.org/wiki/Atat%C3%BCrk_Airport\" title=\"Atatürk Airport\">Istanbul Yeşilköy Airport</a>, killing 42.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Turkish_Airlines_Flight_345\" title=\"Turkish Airlines Flight 345\">Turkish Airlines Flight 345</a> crashes into the <a href=\"https://wikipedia.org/wiki/Sea_of_Marmara\" title=\"Sea of Marmara\">Sea of Marmara</a> near <a href=\"https://wikipedia.org/wiki/Atat%C3%BCrk_Airport\" title=\"Atatürk Airport\">Istanbul Yeşilköy Airport</a>, killing 42.", "links": [{"title": "Turkish Airlines Flight 345", "link": "https://wikipedia.org/wiki/Turkish_Airlines_Flight_345"}, {"title": "Sea of Marmara", "link": "https://wikipedia.org/wiki/Sea_of_Marmara"}, {"title": "Atatürk Airport", "link": "https://wikipedia.org/wiki/Atat%C3%BCrk_Airport"}]}, {"year": "1979", "text": "A Varig Boeing 707-323C freighter, flown by the same commander as Flight 820, disappears over the Pacific Ocean 30 minutes after taking off from Tokyo.", "html": "1979 - A <a href=\"https://wikipedia.org/wiki/Varig\" title=\"Varig\">Varig</a> <a href=\"https://wikipedia.org/wiki/Boeing_707-323C\" class=\"mw-redirect\" title=\"Boeing 707-323C\">Boeing 707-323C</a> freighter, flown by the same commander as <a href=\"https://wikipedia.org/wiki/Varig_Flight_820\" title=\"Varig Flight 820\">Flight 820</a>, <a href=\"https://wikipedia.org/wiki/Varig_Flight_967\" title=\"Varig Flight 967\">disappears over the Pacific Ocean</a> 30 minutes after taking off from Tokyo.", "no_year_html": "A <a href=\"https://wikipedia.org/wiki/Varig\" title=\"Varig\">Varig</a> <a href=\"https://wikipedia.org/wiki/Boeing_707-323C\" class=\"mw-redirect\" title=\"Boeing 707-323C\">Boeing 707-323C</a> freighter, flown by the same commander as <a href=\"https://wikipedia.org/wiki/Varig_Flight_820\" title=\"Varig Flight 820\">Flight 820</a>, <a href=\"https://wikipedia.org/wiki/Varig_Flight_967\" title=\"Varig Flight 967\">disappears over the Pacific Ocean</a> 30 minutes after taking off from Tokyo.", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Varig"}, {"title": "Boeing 707-323C", "link": "https://wikipedia.org/wiki/Boeing_707-323C"}, {"title": "Varig Flight 820", "link": "https://wikipedia.org/wiki/Varig_Flight_820"}, {"title": "Varig Flight 967", "link": "https://wikipedia.org/wiki/Varig_Flight_967"}]}, {"year": "1982", "text": "<PERSON> writes the first PC virus code, which is 400 lines long and disguised as an Apple boot program called \"Elk Cloner\".", "html": "1982 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON> S<PERSON>\"><PERSON></a> writes the first PC <a href=\"https://wikipedia.org/wiki/Computer_virus\" title=\"Computer virus\">virus</a> code, which is 400 lines long and disguised as an <a href=\"https://wikipedia.org/wiki/Apple_Computer\" class=\"mw-redirect\" title=\"Apple Computer\">Apple</a> boot program called \"<a href=\"https://wikipedia.org/wiki/Elk_Cloner\" title=\"<PERSON><PERSON> Cloner\"><PERSON><PERSON>loner</a>\".", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON> S<PERSON>\"><PERSON></a> writes the first PC <a href=\"https://wikipedia.org/wiki/Computer_virus\" title=\"Computer virus\">virus</a> code, which is 400 lines long and disguised as an <a href=\"https://wikipedia.org/wiki/Apple_Computer\" class=\"mw-redirect\" title=\"Apple Computer\">Apple</a> boot program called \"<a href=\"https://wikipedia.org/wiki/Elk_Cloner\" title=\"<PERSON>k Cloner\"><PERSON><PERSON></a>\".", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Computer virus", "link": "https://wikipedia.org/wiki/Computer_virus"}, {"title": "Apple Computer", "link": "https://wikipedia.org/wiki/Apple_Computer"}, {"title": "Elk Cloner", "link": "https://wikipedia.org/wiki/El<PERSON>_<PERSON>"}]}, {"year": "1989", "text": "The American embassy in Kabul, Afghanistan is closed.", "html": "1989 - The American <a href=\"https://wikipedia.org/wiki/Embassy_of_the_United_States,_Kabul\" title=\"Embassy of the United States, Kabul\">embassy</a> in <a href=\"https://wikipedia.org/wiki/Kabul\" title=\"Kabul\">Kabul</a>, <a href=\"https://wikipedia.org/wiki/Democratic_Republic_of_Afghanistan\" title=\"Democratic Republic of Afghanistan\">Afghanistan</a> is closed.", "no_year_html": "The American <a href=\"https://wikipedia.org/wiki/Embassy_of_the_United_States,_Kabul\" title=\"Embassy of the United States, Kabul\">embassy</a> in <a href=\"https://wikipedia.org/wiki/Kabul\" title=\"Kabul\">Kabul</a>, <a href=\"https://wikipedia.org/wiki/Democratic_Republic_of_Afghanistan\" title=\"Democratic Republic of Afghanistan\">Afghanistan</a> is closed.", "links": [{"title": "Embassy of the United States, Kabul", "link": "https://wikipedia.org/wiki/Embassy_of_the_United_States,_Kabul"}, {"title": "Kabul", "link": "https://wikipedia.org/wiki/Kabul"}, {"title": "Democratic Republic of Afghanistan", "link": "https://wikipedia.org/wiki/Democratic_Republic_of_Afghanistan"}]}, {"year": "1995", "text": "Hydroxycarbamide becomes the first approved preventive treatment for sickle cell disease.", "html": "1995 - <a href=\"https://wikipedia.org/wiki/Hydroxycarbamide\" title=\"Hydroxycarbamide\">Hydroxycarbamide</a> becomes the first approved preventive treatment for <a href=\"https://wikipedia.org/wiki/Sickle_cell_disease\" title=\"Sickle cell disease\">sickle cell disease</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Hydroxycarbamide\" title=\"Hydroxycarbamide\">Hydroxycarbamide</a> becomes the first approved preventive treatment for <a href=\"https://wikipedia.org/wiki/Sickle_cell_disease\" title=\"Sickle cell disease\">sickle cell disease</a>.", "links": [{"title": "Hydroxycarbamide", "link": "https://wikipedia.org/wiki/Hydroxycarbamide"}, {"title": "Sickle cell disease", "link": "https://wikipedia.org/wiki/Sickle_cell_disease"}]}, {"year": "2000", "text": "Kenya Airways Flight 431 crashes into the Atlantic Ocean off the coast of Ivory Coast, killing 169.", "html": "2000 - <a href=\"https://wikipedia.org/wiki/Kenya_Airways_Flight_431\" title=\"Kenya Airways Flight 431\">Kenya Airways Flight 431</a> crashes into the Atlantic Ocean off the coast of <a href=\"https://wikipedia.org/wiki/Ivory_Coast\" title=\"Ivory Coast\">Ivory Coast</a>, killing 169.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Kenya_Airways_Flight_431\" title=\"Kenya Airways Flight 431\">Kenya Airways Flight 431</a> crashes into the Atlantic Ocean off the coast of <a href=\"https://wikipedia.org/wiki/Ivory_Coast\" title=\"Ivory Coast\">Ivory Coast</a>, killing 169.", "links": [{"title": "Kenya Airways Flight 431", "link": "https://wikipedia.org/wiki/Kenya_Airways_Flight_431"}, {"title": "Ivory Coast", "link": "https://wikipedia.org/wiki/Ivory_Coast"}]}, {"year": "2006", "text": "The Goleta postal facility shootings occur, killing seven people before the perpetrator took her own life.", "html": "2006 - The <a href=\"https://wikipedia.org/wiki/Goleta_postal_facility_shootings\" title=\"Goleta postal facility shootings\">Goleta postal facility shootings</a> occur, killing seven people before the perpetrator took her own life.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Goleta_postal_facility_shootings\" title=\"Goleta postal facility shootings\">Goleta postal facility shootings</a> occur, killing seven people before the perpetrator took her own life.", "links": [{"title": "Goleta postal facility shootings", "link": "https://wikipedia.org/wiki/Goleta_postal_facility_shootings"}]}, {"year": "2007", "text": "Microsoft Corporation releases Windows Vista, a major release of the operating system Microsoft Windows and the NT based kernel.", "html": "2007 - <a href=\"https://wikipedia.org/wiki/Microsoft_Corporation\" class=\"mw-redirect\" title=\"Microsoft Corporation\">Microsoft Corporation</a> releases <a href=\"https://wikipedia.org/wiki/Windows_Vista\" title=\"Windows Vista\">Windows Vista</a>, a major release of the operating system <a href=\"https://wikipedia.org/wiki/Microsoft_Windows\" title=\"Microsoft Windows\">Microsoft Windows</a> and the NT based kernel.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Microsoft_Corporation\" class=\"mw-redirect\" title=\"Microsoft Corporation\">Microsoft Corporation</a> releases <a href=\"https://wikipedia.org/wiki/Windows_Vista\" title=\"Windows Vista\">Windows Vista</a>, a major release of the operating system <a href=\"https://wikipedia.org/wiki/Microsoft_Windows\" title=\"Microsoft Windows\">Microsoft Windows</a> and the NT based kernel.", "links": [{"title": "Microsoft Corporation", "link": "https://wikipedia.org/wiki/Microsoft_Corporation"}, {"title": "Windows Vista", "link": "https://wikipedia.org/wiki/Windows_Vista"}, {"title": "Microsoft Windows", "link": "https://wikipedia.org/wiki/Microsoft_Windows"}]}, {"year": "2013", "text": "Naro-1 becomes the first carrier rocket launched by South Korea.", "html": "2013 - <a href=\"https://wikipedia.org/wiki/Naro-1\" title=\"Naro-1\">Naro-1</a> becomes the first <a href=\"https://wikipedia.org/wiki/Launch_vehicle\" title=\"Launch vehicle\">carrier rocket</a> launched by South Korea.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Naro-1\" title=\"Naro-1\">Naro-1</a> becomes the first <a href=\"https://wikipedia.org/wiki/Launch_vehicle\" title=\"Launch vehicle\">carrier rocket</a> launched by South Korea.", "links": [{"title": "Naro-1", "link": "https://wikipedia.org/wiki/Naro-1"}, {"title": "Launch vehicle", "link": "https://wikipedia.org/wiki/Launch_vehicle"}]}, {"year": "2020", "text": "The World Health Organization declares the COVID-19 pandemic to be a Public Health Emergency of International Concern.", "html": "2020 - The <a href=\"https://wikipedia.org/wiki/World_Health_Organization\" title=\"World Health Organization\">World Health Organization</a> declares the <a href=\"https://wikipedia.org/wiki/COVID-19_pandemic\" title=\"COVID-19 pandemic\">COVID-19 pandemic</a> to be a <a href=\"https://wikipedia.org/wiki/Public_Health_Emergency_of_International_Concern\" class=\"mw-redirect\" title=\"Public Health Emergency of International Concern\">Public Health Emergency of International Concern</a>.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/World_Health_Organization\" title=\"World Health Organization\">World Health Organization</a> declares the <a href=\"https://wikipedia.org/wiki/COVID-19_pandemic\" title=\"COVID-19 pandemic\">COVID-19 pandemic</a> to be a <a href=\"https://wikipedia.org/wiki/Public_Health_Emergency_of_International_Concern\" class=\"mw-redirect\" title=\"Public Health Emergency of International Concern\">Public Health Emergency of International Concern</a>.", "links": [{"title": "World Health Organization", "link": "https://wikipedia.org/wiki/World_Health_Organization"}, {"title": "COVID-19 pandemic", "link": "https://wikipedia.org/wiki/COVID-19_pandemic"}, {"title": "Public Health Emergency of International Concern", "link": "https://wikipedia.org/wiki/Public_Health_Emergency_of_International_Concern"}]}], "Births": [{"year": "58 BC", "text": "<PERSON><PERSON>, Roman wife of <PERSON> (d. 29)", "html": "58 BC - 58 BC - <a href=\"https://wikipedia.org/wiki/Livia\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Roman wife of <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> (d. 29)", "no_year_html": "58 BC - <a href=\"https://wikipedia.org/wiki/Livia\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Roman wife of <a href=\"https://wikipedia.org/wiki/Augustus\" title=\"<PERSON>\"><PERSON></a> (d. 29)", "links": [{"title": "Livia", "link": "https://wikipedia.org/wiki/Livia"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Augustus"}]}, {"year": "1410", "text": "<PERSON>, English knight (d. 1494)", "html": "1410 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English knight (d. 1494)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English knight (d. 1494)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1520", "text": "<PERSON>, English courtier (d. 1600)", "html": "1520 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(died_1600)\" title=\"<PERSON> (died 1600)\"><PERSON></a>, English courtier (d. 1600)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(died_1600)\" title=\"<PERSON> (died 1600)\"><PERSON></a>, English courtier (d. 1600)", "links": [{"title": "<PERSON> (died 1600)", "link": "https://wikipedia.org/wiki/<PERSON>(died_1600)"}]}, {"year": "1563", "text": "<PERSON><PERSON>, Dutch theologian and academic (d. 1641)", "html": "1563 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Dutch theologian and academic (d. 1641)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Dutch theologian and academic (d. 1641)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Francis<PERSON>_Gomarus"}]}, {"year": "1573", "text": "<PERSON>, Margrave of Baden-Durlach (d. 1638)", "html": "1573 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_<PERSON><PERSON>_of_Baden-Durlach\" class=\"mw-redirect\" title=\"<PERSON>, Margrave of Baden-Durlach\"><PERSON>, Margrave of Baden-Durlach</a> (d. 1638)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>,_<PERSON><PERSON>_of_Baden-Durlach\" class=\"mw-redirect\" title=\"<PERSON>, Margrave of Baden-Durlach\"><PERSON>, Margrave of Baden-Durlach</a> (d. 1638)", "links": [{"title": "<PERSON>, Margrave of Baden-Durlach", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_<PERSON><PERSON>_of_Baden-Durlach"}]}, {"year": "1580", "text": "<PERSON><PERSON><PERSON>, Prince of Liechtenstein, court official in Vienna (d. 1658)", "html": "1580 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>,_Prince_of_Liechtenstein\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON>, Prince of Liechtenstein\"><PERSON><PERSON><PERSON>, Prince of Liechtenstein</a>, court official in Vienna (d. 1658)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>,_Prince_of_Liechtenstein\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON>, Prince of Liechtenstein\"><PERSON><PERSON><PERSON>, Prince of Liechtenstein</a>, court official in Vienna (d. 1658)", "links": [{"title": "<PERSON><PERSON><PERSON>, Prince of Liechtenstein", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>,_Prince_of_Liechtenstein"}]}, {"year": "1590", "text": "<PERSON> <PERSON>, 14th Baroness <PERSON> (d. 1676)", "html": "1590 - <a href=\"https://wikipedia.org/wiki/Lady_<PERSON>,_14th_Baroness_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"Lady <PERSON>, 14th Baroness <PERSON>\">Lady <PERSON>, 14th Baroness <PERSON></a> (d. 1676)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Lady_<PERSON>_<PERSON>,_14th_Baroness_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"Lady <PERSON>, 14th Baroness de <PERSON>\">Lady <PERSON>, 14th Baroness <PERSON></a> (d. 1676)", "links": [{"title": "<PERSON> <PERSON>, 14th Baroness <PERSON>", "link": "https://wikipedia.org/wiki/Lady_<PERSON>,_14th_Baroness_<PERSON>_<PERSON>"}]}, {"year": "1628", "text": "<PERSON>, 2nd Duke of Buckingham, English statesman (d. 1687)", "html": "1628 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_2nd_Duke_of_Buckingham\" title=\"<PERSON>, 2nd Duke of Buckingham\"><PERSON>, 2nd Duke of Buckingham</a>, English statesman (d. 1687)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>,_2nd_Duke_of_Buckingham\" title=\"<PERSON>, 2nd Duke of Buckingham\"><PERSON>, 2nd Duke of Buckingham</a>, English statesman (d. 1687)", "links": [{"title": "<PERSON>, 2nd Duke of Buckingham", "link": "https://wikipedia.org/wiki/<PERSON>,_2nd_Duke_of_Buckingham"}]}, {"year": "1661", "text": "<PERSON>, French historian and educator (d. 1741)", "html": "1661 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French historian and educator (d. 1741)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French historian and educator (d. 1741)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1697", "text": "<PERSON>, German flute player and composer (d. 1773)", "html": "1697 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German flute player and composer (d. 1773)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German flute player and composer (d. 1773)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1703", "text": "<PERSON>, French politician (d. 1778)", "html": "1703 - <a href=\"https://wikipedia.org/wiki/Fran%C3%A<PERSON><PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French politician (d. 1778)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Fran%C3%A<PERSON><PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French politician (d. 1778)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Fran%C3%A<PERSON><PERSON>_<PERSON>ot"}]}, {"year": "1720", "text": "<PERSON>, Swedish entomologist and archaeologist (d. 1778)", "html": "1720 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swedish entomologist and archaeologist (d. 1778)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swedish entomologist and archaeologist (d. 1778)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1754", "text": "<PERSON>, American lawyer and politician (d. 1829)", "html": "1754 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>.\" title=\"<PERSON>.\"><PERSON>.</a>, American lawyer and politician (d. 1829)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>.\" title=\"<PERSON>.\"><PERSON>.</a>, American lawyer and politician (d. 1829)", "links": [{"title": "<PERSON>.", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>."}]}, {"year": "1775", "text": "<PERSON>, English poet and author (d. 1864)", "html": "1775 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English poet and author (d. 1864)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English poet and author (d. 1864)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1781", "text": "<PERSON><PERSON><PERSON>, German botanist and poet (d. 1838)", "html": "1781 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, German botanist and poet (d. 1838)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, German botanist and poet (d. 1838)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1816", "text": "<PERSON>, American general and politician, 24th Governor of Massachusetts (d. 1894)", "html": "1816 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American general and politician, 24th <a href=\"https://wikipedia.org/wiki/Governor_of_Massachusetts\" title=\"Governor of Massachusetts\">Governor of Massachusetts</a> (d. 1894)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American general and politician, 24th <a href=\"https://wikipedia.org/wiki/Governor_of_Massachusetts\" title=\"Governor of Massachusetts\">Governor of Massachusetts</a> (d. 1894)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "Governor of Massachusetts", "link": "https://wikipedia.org/wiki/Governor_of_Massachusetts"}]}, {"year": "1822", "text": "<PERSON>, Austrian geologist and curator (d. 1899)", "html": "1822 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Austrian geologist and curator (d. 1899)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Austrian geologist and curator (d. 1899)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1841", "text": "<PERSON>, French politician, 7th President of France (d. 1899)", "html": "1841 - <a href=\"https://wikipedia.org/wiki/F%C3%A9lix_Faure\" title=\"<PERSON>\"><PERSON></a>, French politician, 7th <a href=\"https://wikipedia.org/wiki/President_of_France\" title=\"President of France\">President of France</a> (d. 1899)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/F%C3%A9lix_Faure\" title=\"<PERSON>\"><PERSON></a>, French politician, 7th <a href=\"https://wikipedia.org/wiki/President_of_France\" title=\"President of France\">President of France</a> (d. 1899)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/F%C3%A9lix_Faure"}, {"title": "President of France", "link": "https://wikipedia.org/wiki/President_of_France"}]}, {"year": "1844", "text": "<PERSON>, American lawyer, academic, and diplomat (d. 1922)", "html": "1844 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer, academic, and diplomat (d. 1922)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer, academic, and diplomat (d. 1922)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1846", "text": "<PERSON> of the Cross, Spanish nun and saint (d. 1932)", "html": "1846 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_the_Cross\" title=\"Angela of the Cross\"><PERSON> of the Cross</a>, Spanish nun and saint (d. 1932)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_Cross\" title=\"Angela of the Cross\"><PERSON> of the Cross</a>, Spanish nun and saint (d. 1932)", "links": [{"title": "Angela of the Cross", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1861", "text": "<PERSON>, German-American violinist and composer (d. 1935)", "html": "1861 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German-American violinist and composer (d. 1935)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German-American violinist and composer (d. 1935)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1862", "text": "<PERSON>, German-American conductor and composer (d. 1950)", "html": "1862 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German-American conductor and composer (d. 1950)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German-American conductor and composer (d. 1950)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1866", "text": "<PERSON><PERSON><PERSON>, American author, poet, and critic (d. 1951)", "html": "1866 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American author, poet, and critic (d. 1951)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American author, poet, and critic (d. 1951)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1878", "text": "<PERSON><PERSON> <PERSON><PERSON>, Estonian author (d. 1940)", "html": "1878 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> <PERSON><PERSON>\"><PERSON><PERSON> <PERSON><PERSON></a>, Estonian author (d. 1940)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> <PERSON><PERSON>\"><PERSON><PERSON> <PERSON><PERSON></a>, Estonian author (d. 1940)", "links": [{"title": "<PERSON><PERSON> <PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "1882", "text": "<PERSON>, American lawyer and statesman, 32nd President of the United States (d. 1945)", "html": "1882 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and statesman, 32nd <a href=\"https://wikipedia.org/wiki/President_of_the_United_States\" title=\"President of the United States\">President of the United States</a> (d. 1945)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and statesman, 32nd <a href=\"https://wikipedia.org/wiki/President_of_the_United_States\" title=\"President of the United States\">President of the United States</a> (d. 1945)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "President of the United States", "link": "https://wikipedia.org/wiki/President_of_the_United_States"}]}, {"year": "1889", "text": "<PERSON><PERSON><PERSON>, Indian poet and playwright (d. 1937)", "html": "1889 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Indian poet and playwright (d. 1937)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Indian poet and playwright (d. 1937)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1899", "text": "<PERSON>, South African-American virologist and academic, Nobel Prize laureate (d. 1972)", "html": "1899 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> The<PERSON>\"><PERSON></a>, South African-American virologist and academic, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Physiology_or_Medicine\" title=\"Nobel Prize in Physiology or Medicine\">Nobel Prize</a> laureate (d. 1972)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Theiler\"><PERSON></a>, South African-American virologist and academic, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Physiology_or_Medicine\" title=\"Nobel Prize in Physiology or Medicine\">Nobel Prize</a> laureate (d. 1972)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_Theiler"}, {"title": "Nobel Prize in Physiology or Medicine", "link": "https://wikipedia.org/wiki/Nobel_Prize_in_Physiology_or_Medicine"}]}, {"year": "1900", "text": "<PERSON><PERSON>, Argentine-born British actress (d. 1969)", "html": "1900 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Argentine-born British actress (d. 1969)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Argentine-born British actress (d. 1969)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1901", "text": "<PERSON>, German racing driver (d. 1959)", "html": "1901 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German racing driver (d. 1959)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German racing driver (d. 1959)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1902", "text": "<PERSON><PERSON>, German-English historian and scholar (d. 1983)", "html": "1902 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, German-English historian and scholar (d. 1983)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, German-English historian and scholar (d. 1983)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1910", "text": "<PERSON><PERSON><PERSON><PERSON>, Indian lawyer and politician, Indian Minister of Defence (d. 2000)", "html": "1910 - <a href=\"https://wikipedia.org/wiki/Chidambaram_Subramaniam\" title=\"Chidambaram Subramaniam\">Chidambaram Subramaniam</a>, Indian lawyer and politician, <a href=\"https://wikipedia.org/wiki/Minister_of_Defence_(India)\" title=\"Minister of Defence (India)\">Indian Minister of Defence</a> (d. 2000)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Chidambaram_Subramaniam\" title=\"Chidambaram Subramaniam\">Chidambaram Subramaniam</a>, Indian lawyer and politician, <a href=\"https://wikipedia.org/wiki/Minister_of_Defence_(India)\" title=\"Minister of Defence (India)\">Indian Minister of Defence</a> (d. 2000)", "links": [{"title": "Chidambaram Subramaniam", "link": "https://wikipedia.org/wiki/Chidambaram_Subramaniam"}, {"title": "Minister of Defence (India)", "link": "https://wikipedia.org/wiki/Minister_of_Defence_(India)"}]}, {"year": "1911", "text": "<PERSON>, American jazz trumpet player (d. 1989)", "html": "1911 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American jazz trumpet player (d. 1989)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American jazz trumpet player (d. 1989)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1912", "text": "<PERSON>, German physicist and academic (d. 1988)", "html": "1912 - <a href=\"https://wikipedia.org/wiki/<PERSON>_(physicist)\" title=\"<PERSON> (physicist)\"><PERSON></a>, German physicist and academic (d. 1988)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(physicist)\" title=\"<PERSON> (physicist)\"><PERSON></a>, German physicist and academic (d. 1988)", "links": [{"title": "<PERSON> (physicist)", "link": "https://wikipedia.org/wiki/<PERSON>_(physicist)"}]}, {"year": "1912", "text": "<PERSON>, American pastor and theologian (d. 1984)", "html": "1912 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American pastor and theologian (d. 1984)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American pastor and theologian (d. 1984)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1912", "text": "<PERSON>, American historian and author (d. 1989)", "html": "1912 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American historian and author (d. 1989)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American historian and author (d. 1989)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "1913", "text": "<PERSON><PERSON>, Hungarian-Indian painter (d. 1941)", "html": "1913 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>-<PERSON>\" title=\"<PERSON><PERSON>r-<PERSON>\"><PERSON><PERSON><PERSON></a>, Hungarian-Indian painter (d. 1941)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>-<PERSON>\" title=\"<PERSON><PERSON>r-<PERSON>\"><PERSON><PERSON><PERSON></a>, Hungarian-Indian painter (d. 1941)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>-<PERSON>"}]}, {"year": "1914", "text": "<PERSON><PERSON><PERSON>, French commander and painter (d. 2000)", "html": "1914 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, French commander and painter (d. 2000)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, French commander and painter (d. 2000)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>"}]}, {"year": "1914", "text": "<PERSON>, Canadian-American actor and director (d. 1992)", "html": "1914 - <a href=\"https://wikipedia.org/wiki/John_<PERSON>_(actor)\" class=\"mw-redirect\" title=\"<PERSON> (actor)\"><PERSON></a>, Canadian-American actor and director (d. 1992)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/John_Ireland_(actor)\" class=\"mw-redirect\" title=\"<PERSON> (actor)\"><PERSON></a>, Canadian-American actor and director (d. 1992)", "links": [{"title": "<PERSON> (actor)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(actor)"}]}, {"year": "1914", "text": "<PERSON>, American actor (d. 1995)", "html": "1914 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor (d. 1995)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor (d. 1995)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1915", "text": "<PERSON>, German SS officer (d. 1976)", "html": "1915 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German <a href=\"https://wikipedia.org/wiki/SS\" class=\"mw-redirect\" title=\"SS\">SS</a> officer (d. 1976)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German <a href=\"https://wikipedia.org/wiki/SS\" class=\"mw-redirect\" title=\"SS\">SS</a> officer (d. 1976)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "SS", "link": "https://wikipedia.org/wiki/SS"}]}, {"year": "1915", "text": "<PERSON>, English soldier and politician, Secretary of State for War (d. 2006)", "html": "1915 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English soldier and politician, <a href=\"https://wikipedia.org/wiki/Secretary_of_State_for_War\" title=\"Secretary of State for War\">Secretary of State for War</a> (d. 2006)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English soldier and politician, <a href=\"https://wikipedia.org/wiki/Secretary_of_State_for_War\" title=\"Secretary of State for War\">Secretary of State for War</a> (d. 2006)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Secretary of State for War", "link": "https://wikipedia.org/wiki/Secretary_of_State_for_War"}]}, {"year": "1917", "text": "<PERSON>, Belgian racing driver and journalist (d. 2008)", "html": "1917 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A8re\" title=\"<PERSON>\"><PERSON></a>, Belgian racing driver and journalist (d. 2008)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A8re\" title=\"<PERSON>\"><PERSON></a>, Belgian racing driver and journalist (d. 2008)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A8re"}]}, {"year": "1918", "text": "<PERSON>, American actor and screenwriter (d. 1996)", "html": "1918 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor and screenwriter (d. 1996)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor and screenwriter (d. 1996)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1919", "text": "<PERSON>, American activist (d. 2005)", "html": "1919 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American activist (d. 2005)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American activist (d. 2005)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1920", "text": "<PERSON>, English director and producer (d. 2018)", "html": "1920 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(director)\" title=\"<PERSON> (director)\"><PERSON></a>, English director and producer (d. 2018)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(director)\" title=\"<PERSON> (director)\"><PERSON></a>, English director and producer (d. 2018)", "links": [{"title": "<PERSON> (director)", "link": "https://wikipedia.org/wiki/<PERSON>_(director)"}]}, {"year": "1920", "text": "<PERSON>, British painter (d. 1999)", "html": "1920 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, British painter (d. 1999)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, British painter (d. 1999)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1920", "text": "<PERSON><PERSON>, American director and producer (d. 2007)", "html": "1920 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American director and producer (d. 2007)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American director and producer (d. 2007)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1922", "text": "<PERSON>, American comedian, actor, and director (d. 2008)", "html": "1922 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(comedian)\" class=\"mw-redirect\" title=\"<PERSON> (comedian)\"><PERSON></a>, American comedian, actor, and director (d. 2008)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(comedian)\" class=\"mw-redirect\" title=\"<PERSON> (comedian)\"><PERSON></a>, American comedian, actor, and director (d. 2008)", "links": [{"title": "<PERSON> (comedian)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>(comedian)"}]}, {"year": "1923", "text": "<PERSON>, Czech-American economist and author (d. 2013)", "html": "1923 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Czech-American economist and author (d. 2013)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Czech-American economist and author (d. 2013)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1924", "text": "<PERSON>, American basketball player and coach (d. 2003)", "html": "1924 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player and coach (d. 2003)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player and coach (d. 2003)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1924", "text": "<PERSON><PERSON> <PERSON><PERSON>, Burmese-Indian author and educator (d. 2013)", "html": "1924 - <a href=\"https://wikipedia.org/wiki/S._N._Goenka\" title=\"S. N. Goenka\">S. N<PERSON></a>, Burmese-Indian author and educator (d. 2013)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/S._N._Goenka\" title=\"S. N. Goenka\"><PERSON>. N<PERSON></a>, Burmese-Indian author and educator (d. 2013)", "links": [{"title": "S. N. Goenka", "link": "https://wikipedia.org/wiki/S._N._Goenka"}]}, {"year": "1925", "text": "<PERSON>, American computer scientist, invented the computer mouse (d. 2013)", "html": "1925 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American computer scientist, invented the <a href=\"https://wikipedia.org/wiki/Mouse_(computing)\" class=\"mw-redirect\" title=\"Mouse (computing)\">computer mouse</a> (d. 2013)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American computer scientist, invented the <a href=\"https://wikipedia.org/wiki/Mouse_(computing)\" class=\"mw-redirect\" title=\"Mouse (computing)\">computer mouse</a> (d. 2013)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Mouse (computing)", "link": "https://wikipedia.org/wiki/<PERSON>_(computing)"}]}, {"year": "1927", "text": "<PERSON><PERSON><PERSON>, Swedish statesman, 26th Prime Minister of Sweden (d. 1986)", "html": "1927 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Swedish statesman, 26th <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Sweden\" title=\"Prime Minister of Sweden\">Prime Minister of Sweden</a> (d. 1986)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Swedish statesman, 26th <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Sweden\" title=\"Prime Minister of Sweden\">Prime Minister of Sweden</a> (d. 1986)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}, {"title": "Prime Minister of Sweden", "link": "https://wikipedia.org/wiki/Prime_Minister_of_Sweden"}]}, {"year": "1928", "text": "<PERSON>, American director and producer (d. 2019)", "html": "1928 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Prince\"><PERSON></a>, American director and producer (d. 2019)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Prince\"><PERSON></a>, American director and producer (d. 2019)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1928", "text": "<PERSON>, American basketball player and coach (d. 1998)", "html": "1928 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(basketball)\" title=\"<PERSON> (basketball)\"><PERSON></a>, American basketball player and coach (d. 1998)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>(basketball)\" title=\"<PERSON> (basketball)\"><PERSON></a>, American basketball player and coach (d. 1998)", "links": [{"title": "<PERSON> (basketball)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(basketball)"}]}, {"year": "1929", "text": "<PERSON>, Canadian businesswoman and politician, 15th Lieutenant Governor of Alberta (d. 2005)", "html": "1929 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian businesswoman and politician, 15th <a href=\"https://wikipedia.org/wiki/Lieutenant_Governor_of_Alberta\" title=\"Lieutenant Governor of Alberta\">Lieutenant Governor of Alberta</a> (d. 2005)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian businesswoman and politician, 15th <a href=\"https://wikipedia.org/wiki/Lieutenant_Governor_of_Alberta\" title=\"Lieutenant Governor of Alberta\">Lieutenant Governor of Alberta</a> (d. 2005)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Lieutenant Governor of Alberta", "link": "https://wikipedia.org/wiki/Lieutenant_Governor_of_Alberta"}]}, {"year": "1929", "text": "<PERSON>, South African cricketer (d. 1994)", "html": "1929 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, South African cricketer (d. 1994)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, South African cricketer (d. 1994)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1929", "text": "<PERSON><PERSON><PERSON><PERSON><PERSON>, Canadian-Italian physician and humanitarian (d. 1996)", "html": "1929 - <a href=\"https://wikipedia.org/wiki/Lucille_Teasdale-Corti\" title=\"Lucille Teasdale-Corti\"><PERSON><PERSON>sdale-Corti</a>, Canadian-Italian physician and humanitarian (d. 1996)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Lucille_Teasdale-Corti\" title=\"Lucille Teasdale-Corti\"><PERSON><PERSON>sdale-Corti</a>, Canadian-Italian physician and humanitarian (d. 1996)", "links": [{"title": "Lucille Teasdale-Corti", "link": "https://wikipedia.org/wiki/Lucille_Teasdale-Corti"}]}, {"year": "1930", "text": "<PERSON>, American actor and author (d. 2025)", "html": "1930 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor and author (d. 2025)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor and author (d. 2025)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1930", "text": "<PERSON>, South African general and politician, South African Minister of Defence (d. 2011)", "html": "1930 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, South African general and politician, <a href=\"https://wikipedia.org/wiki/Minister_of_Defence_(South_Africa)\" class=\"mw-redirect\" title=\"Minister of Defence (South Africa)\">South African Minister of Defence</a> (d. 2011)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, South African general and politician, <a href=\"https://wikipedia.org/wiki/Minister_of_Defence_(South_Africa)\" class=\"mw-redirect\" title=\"Minister of Defence (South Africa)\">South African Minister of Defence</a> (d. 2011)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Minister of Defence (South Africa)", "link": "https://wikipedia.org/wiki/Minister_of_Defence_(South_Africa)"}]}, {"year": "1931", "text": "<PERSON>, Canadian lawyer and politician, 34th Canadian Minister of Justice (d. 2020)", "html": "1931 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian lawyer and politician, 34th <a href=\"https://wikipedia.org/wiki/Minister_of_Justice_(Canada)\" class=\"mw-redirect\" title=\"Minister of Justice (Canada)\">Canadian Minister of Justice</a> (d. 2020)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian lawyer and politician, 34th <a href=\"https://wikipedia.org/wiki/Minister_of_Justice_(Canada)\" class=\"mw-redirect\" title=\"Minister of Justice (Canada)\">Canadian Minister of Justice</a> (d. 2020)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}, {"title": "Minister of Justice (Canada)", "link": "https://wikipedia.org/wiki/Minister_of_Justice_(Canada)"}]}, {"year": "1931", "text": "<PERSON>, Australian-American novelist, short story writer, and essayist (d. 2016)", "html": "1931 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian-American novelist, short story writer, and essayist (d. 2016)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian-American novelist, short story writer, and essayist (d. 2016)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1932", "text": "<PERSON><PERSON>, Japanese comedian and politician (d. 2007)", "html": "1932 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"Knock <PERSON>\"><PERSON><PERSON></a>, Japanese comedian and politician (d. 2007)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"Knock <PERSON>\"><PERSON><PERSON></a>, Japanese comedian and politician (d. 2007)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>"}]}, {"year": "1934", "text": "<PERSON>, American actress and singer (d. 2016)", "html": "1934 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress and singer (d. 2016)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress and singer (d. 2016)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1935", "text": "<PERSON>, American novelist, poet, and short story writer (d. 1984)", "html": "1935 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American novelist, poet, and short story writer (d. 1984)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American novelist, poet, and short story writer (d. 1984)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1935", "text": "<PERSON><PERSON>, English saxophonist and composer (d. 1973)", "html": "1935 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, English saxophonist and composer (d. 1973)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, English saxophonist and composer (d. 1973)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1936", "text": "<PERSON><PERSON>, German pianist and composer (d. 1998)", "html": "1936 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, German pianist and composer (d. 1998)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, German pianist and composer (d. 1998)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1937", "text": "<PERSON>, English actress", "html": "1937 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actress", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1937", "text": "<PERSON>, Russian chess grandmaster (d. 2025)", "html": "1937 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian chess grandmaster (d. 2025)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian chess grandmaster (d. 2025)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1938", "text": "<PERSON>, Uzbek politician, 1st President of Uzbekistan (d. 2016)", "html": "1938 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Uzbek politician, 1st <a href=\"https://wikipedia.org/wiki/President_of_Uzbekistan\" title=\"President of Uzbekistan\">President of Uzbekistan</a> (d. 2016)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_Ka<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Uzbek politician, 1st <a href=\"https://wikipedia.org/wiki/President_of_Uzbekistan\" title=\"President of Uzbekistan\">President of Uzbekistan</a> (d. 2016)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>"}, {"title": "President of Uzbekistan", "link": "https://wikipedia.org/wiki/President_of_Uzbekistan"}]}, {"year": "1941", "text": "<PERSON>, American astrophysicist and author", "html": "1941 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American astrophysicist and author", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American astrophysicist and author", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1941", "text": "<PERSON>, American businessman and politician, 46th Vice President of the United States", "html": "1941 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American businessman and politician, 46th <a href=\"https://wikipedia.org/wiki/Vice_President_of_the_United_States\" title=\"Vice President of the United States\">Vice President of the United States</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American businessman and politician, 46th <a href=\"https://wikipedia.org/wiki/Vice_President_of_the_United_States\" title=\"Vice President of the United States\">Vice President of the United States</a>", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Vice President of the United States", "link": "https://wikipedia.org/wiki/Vice_President_of_the_United_States"}]}, {"year": "1941", "text": "<PERSON><PERSON><PERSON>, Dutch swimmer", "html": "1941 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Dutch swimmer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Dutch swimmer", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1942", "text": "<PERSON>, American singer-songwriter and guitarist (d. 2018)", "html": "1942 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and guitarist (d. 2018)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and guitarist (d. 2018)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1943", "text": "<PERSON>, American baseball player and manager", "html": "1943 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player and manager", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player and manager", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1944", "text": "<PERSON>, American cellist and academic (d. 2020)", "html": "1944 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American cellist and academic (d. 2020)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American cellist and academic (d. 2020)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1944", "text": "<PERSON>, English lawyer and judge", "html": "1944 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English lawyer and judge", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English lawyer and judge", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1945", "text": "<PERSON><PERSON>, Israeli military officer and intelligence official, Director of Mossad (2002-11) (d. 2016)", "html": "1945 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Israeli military officer and intelligence official, Director of Mossad (2002-11) (d. 2016)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Israeli military officer and intelligence official, Director of Mossad (2002-11) (d. 2016)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1945", "text": "<PERSON>, American author and scholar (d. 1997)", "html": "1945 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author and scholar (d. 1997)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author and scholar (d. 1997)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1946", "text": "<PERSON>, <PERSON>, English publisher, founded The Big Issue", "html": "1946 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_<PERSON>_<PERSON>\" title=\"<PERSON>, Baron <PERSON>\"><PERSON>, <PERSON></a>, English publisher, founded <i><a href=\"https://wikipedia.org/wiki/The_Big_Issue\" title=\"The Big Issue\">The Big Issue</a></i>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_<PERSON>_<PERSON>\" title=\"<PERSON>, <PERSON>\"><PERSON>, <PERSON></a>, English publisher, founded <i><a href=\"https://wikipedia.org/wiki/The_Big_Issue\" title=\"The Big Issue\">The Big Issue</a></i>", "links": [{"title": "<PERSON>, Baron <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_<PERSON>_<PERSON>"}, {"title": "The Big Issue", "link": "https://wikipedia.org/wiki/The_Big_Issue"}]}, {"year": "1947", "text": "<PERSON>, English poet and author (d. 2023)", "html": "1947 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English poet and author (d. 2023)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English poet and author (d. 2023)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1947", "text": "<PERSON>, English singer-songwriter and guitarist (d. 1991)", "html": "1947 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English singer-songwriter and guitarist (d. 1991)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English singer-songwriter and guitarist (d. 1991)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1949", "text": "<PERSON>, American physician and biologist, Nobel Prize laureate", "html": "1949 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American physician and biologist, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Chemistry\" title=\"Nobel Prize in Chemistry\">Nobel Prize</a> laureate", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American physician and biologist, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Chemistry\" title=\"Nobel Prize in Chemistry\">Nobel Prize</a> laureate", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Nobel Prize in Chemistry", "link": "https://wikipedia.org/wiki/Nobel_Prize_in_Chemistry"}]}, {"year": "1950", "text": "<PERSON>, Australian golfer (d. 2022)", "html": "1950 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian golfer (d. 2022)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian golfer (d. 2022)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1951", "text": "<PERSON>, English drummer, singer-songwriter, producer, and actor", "html": "1951 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English drummer, singer-songwriter, producer, and actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English drummer, singer-songwriter, producer, and actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1951", "text": "<PERSON>, American actor and director", "html": "1951 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor and director", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor and director", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1951", "text": "<PERSON>, English footballer (d. 1995)", "html": "1951 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English footballer (d. 1995)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English footballer (d. 1995)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1952", "text": "<PERSON>, Canadian football player and producer (d. 2021)", "html": "1952 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(Canadian_football)\" title=\"<PERSON> (Canadian football)\"><PERSON></a>, Canadian football player and producer (d. 2021)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(Canadian_football)\" title=\"<PERSON> (Canadian football)\"><PERSON></a>, Canadian football player and producer (d. 2021)", "links": [{"title": "<PERSON> (Canadian football)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(Canadian_football)"}]}, {"year": "1953", "text": "<PERSON>, American author and illustrator", "html": "1953 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author and illustrator", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author and illustrator", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1955", "text": "<PERSON>, American politician, 73rd Governor of Maine", "html": "1955 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American politician, 73rd <a href=\"https://wikipedia.org/wiki/Governor_of_Maine\" title=\"Governor of Maine\">Governor of Maine</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American politician, 73rd <a href=\"https://wikipedia.org/wiki/Governor_of_Maine\" title=\"Governor of Maine\">Governor of Maine</a>", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}, {"title": "Governor of Maine", "link": "https://wikipedia.org/wiki/Governor_of_Maine"}]}, {"year": "1955", "text": "<PERSON>, American golfer", "html": "1955 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American golfer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Strange\"><PERSON></a>, American golfer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1955", "text": "<PERSON><PERSON>, Bahamian-American basketball player and sportscaster", "html": "1955 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Bahamian-American basketball player and sportscaster", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Bahamian-American basketball player and sportscaster", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1956", "text": "<PERSON>, American actress", "html": "1956 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1957", "text": "<PERSON>, American television reporter", "html": "1957 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American television reporter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American television reporter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1957", "text": "<PERSON>, American golfer (d. 1999)", "html": "1957 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American golfer (d. 1999)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American golfer (d. 1999)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1958", "text": "<PERSON>, American actress", "html": "1958 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(actress)\" title=\"<PERSON> (actress)\"><PERSON></a>, American actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>(actress)\" title=\"<PERSON> (actress)\"><PERSON></a>, American actress", "links": [{"title": "<PERSON> (actress)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>(actress)"}]}, {"year": "1959", "text": "<PERSON><PERSON>, American entertainer", "html": "1959 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American entertainer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American entertainer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>"}]}, {"year": "1962", "text": "<PERSON> of Jordan", "html": "1962 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Jordan\" title=\"<PERSON> of Jordan\"><PERSON> of Jordan</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Jordan\" title=\"<PERSON> of Jordan\"><PERSON> of Jordan</a>", "links": [{"title": "<PERSON> of Jordan", "link": "https://wikipedia.org/wiki/<PERSON>_II_of_Jordan"}]}, {"year": "1962", "text": "<PERSON>, American sex offender (d. 2020)", "html": "1962 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American sex offender (d. 2020)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American sex offender (d. 2020)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1964", "text": "<PERSON>, American basketball player, coach, and manager", "html": "1964 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(basketball)\" title=\"<PERSON> (basketball)\"><PERSON></a>, American basketball player, coach, and manager", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(basketball)\" title=\"<PERSON> (basketball)\"><PERSON></a>, American basketball player, coach, and manager", "links": [{"title": "<PERSON> (basketball)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(basketball)"}]}, {"year": "1965", "text": "<PERSON>, Australian rugby league player and coach", "html": "1965 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(rugby_league)\" title=\"<PERSON> (rugby league)\"><PERSON></a>, Australian rugby league player and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(rugby_league)\" title=\"<PERSON> (rugby league)\"><PERSON></a>, Australian rugby league player and coach", "links": [{"title": "<PERSON> (rugby league)", "link": "https://wikipedia.org/wiki/<PERSON>_(rugby_league)"}]}, {"year": "1966", "text": "<PERSON>, Canadian ice hockey player and coach", "html": "1966 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player and coach", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1968", "text": "Felipe VI of Spain", "html": "1968 - <a href=\"https://wikipedia.org/wiki/Felipe_VI_of_Spain\" class=\"mw-redirect\" title=\"Felipe VI of Spain\">Felipe VI of Spain</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Felipe_VI_of_Spain\" class=\"mw-redirect\" title=\"Felipe VI of Spain\"><PERSON> VI of Spain</a>", "links": [{"title": "Felipe VI of Spain", "link": "https://wikipedia.org/wiki/Felipe_VI_of_Spain"}]}, {"year": "1969", "text": "<PERSON>, English footballer and manager", "html": "1969 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(footballer,_born_1969)\" title=\"<PERSON> (footballer, born 1969)\"><PERSON></a>, English footballer and manager", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(footballer,_born_1969)\" title=\"<PERSON> (footballer, born 1969)\"><PERSON></a>, English footballer and manager", "links": [{"title": "<PERSON> (footballer, born 1969)", "link": "https://wikipedia.org/wiki/<PERSON>_(footballer,_born_1969)"}]}, {"year": "1970", "text": "<PERSON><PERSON>, Japanese astronaut", "html": "1970 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Japanese astronaut", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Japanese astronaut", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>i"}]}, {"year": "1972", "text": "<PERSON>, American politician, 56th Speaker of the United States House of Representatives", "html": "1972 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American politician, 56th <a href=\"https://wikipedia.org/wiki/Speaker_of_the_United_States_House_of_Representatives\" title=\"Speaker of the United States House of Representatives\">Speaker of the United States House of Representatives</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American politician, 56th <a href=\"https://wikipedia.org/wiki/Speaker_of_the_United_States_House_of_Representatives\" title=\"Speaker of the United States House of Representatives\">Speaker of the United States House of Representatives</a>", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Speaker of the United States House of Representatives", "link": "https://wikipedia.org/wiki/Speaker_of_the_United_States_House_of_Representatives"}]}, {"year": "1972", "text": "<PERSON>, Canadian ice hockey player", "html": "1972 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1973", "text": "<PERSON><PERSON><PERSON>, American basketball player and sportscaster", "html": "1973 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American basketball player and sportscaster", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American basketball player and sportscaster", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1974", "text": "<PERSON>, British actor", "html": "1974 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, British actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, British actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1974", "text": "<PERSON>, English actress", "html": "1974 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actress", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1975", "text": "<PERSON><PERSON><PERSON>, Brazilian footballer", "html": "1975 - <a href=\"https://wikipedia.org/wiki/Juninho_Pernambucano\" title=\"Juninho Pernambucano\"><PERSON><PERSON><PERSON> Pernambucano</a>, Brazilian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Juninho_Pernambucano\" title=\"Juninho Pernambucano\"><PERSON><PERSON><PERSON> Pernambucano</a>, Brazilian footballer", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Juninho_Pernambucano"}]}, {"year": "1976", "text": "<PERSON>, American entertainer", "html": "1976 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American entertainer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American entertainer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1978", "text": "<PERSON>, Swiss curler", "html": "1978 - <a href=\"https://wikipedia.org/wiki/Carmen_K%C3%BCng\" title=\"<PERSON>\"><PERSON></a>, Swiss curler", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_K%C3%BCng\" title=\"<PERSON>\"><PERSON></a>, Swiss curler", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Carmen_K%C3%BCng"}]}, {"year": "1978", "text": "<PERSON>, American baseball player", "html": "1978 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(pitcher)\" title=\"<PERSON> (pitcher)\"><PERSON></a>, American baseball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>(pitcher)\" title=\"<PERSON> (pitcher)\"><PERSON></a>, American baseball player", "links": [{"title": "<PERSON> (pitcher)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(pitcher)"}]}, {"year": "1979", "text": "<PERSON>, Canadian ice hockey player", "html": "1979 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1980", "text": "<PERSON>, American actress and singer", "html": "1980 - <a href=\"https://wikipedia.org/wiki/Lena_Hall\" title=\"Lena Hall\"><PERSON></a>, American actress and singer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Lena_Hall\" title=\"Lena Hall\"><PERSON></a>, American actress and singer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Lena_Hall"}]}, {"year": "1980", "text": "<PERSON>, American singer-songwriter and musician", "html": "1980 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and musician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and musician", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1980", "text": "<PERSON><PERSON>, Greek footballer", "html": "1980 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Greek footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Greek footballer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1980", "text": "<PERSON><PERSON><PERSON>, American actor and producer", "html": "1980 - <a href=\"https://wikipedia.org/wiki/Wil<PERSON>_<PERSON>derrama\" title=\"Wil<PERSON>\">W<PERSON><PERSON></a>, American actor and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Wil<PERSON>_<PERSON>\" title=\"Wil<PERSON>\">W<PERSON><PERSON></a>, American actor and producer", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Wil<PERSON>_Valderrama"}]}, {"year": "1980", "text": "<PERSON>, American politician and 17th Administrator of the Environmental Protection Agency.", "html": "1980 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American politician and 17th <a href=\"https://wikipedia.org/wiki/Administrator_of_the_Environmental_Protection_Agency\" title=\"Administrator of the Environmental Protection Agency\">Administrator of the Environmental Protection Agency</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>\" title=\"<PERSON>\"><PERSON></a>, American politician and 17th <a href=\"https://wikipedia.org/wiki/Administrator_of_the_Environmental_Protection_Agency\" title=\"Administrator of the Environmental Protection Agency\">Administrator of the Environmental Protection Agency</a>.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Administrator of the Environmental Protection Agency", "link": "https://wikipedia.org/wiki/Administrator_of_the_Environmental_Protection_Agency"}]}, {"year": "1981", "text": "<PERSON>, American basketball player", "html": "1981 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1981", "text": "<PERSON><PERSON><PERSON>, Bulgarian footballer", "html": "1981 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Bulgarian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Bulgarian footballer", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1981", "text": "<PERSON>, English footballer", "html": "1981 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1981", "text": "<PERSON>, Austrian racing driver", "html": "1981 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Austrian racing driver", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Austrian racing driver", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1982", "text": "<PERSON>, American-Mexican baseball player", "html": "1982 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%BA\" title=\"<PERSON>\"><PERSON></a>, American-Mexican baseball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%BA\" title=\"<PERSON>\"><PERSON></a>, American-Mexican baseball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Jorge_Cant%C3%BA"}]}, {"year": "1982", "text": "<PERSON><PERSON><PERSON><PERSON>, Senegalese basketball player and coach", "html": "1982 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON> Diop\"><PERSON><PERSON><PERSON><PERSON></a>, Senegalese basketball player and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"DeSagana Diop\"><PERSON><PERSON><PERSON><PERSON></a>, Senegalese basketball player and coach", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON><PERSON>"}]}, {"year": "1982", "text": "<PERSON>, American football player", "html": "1982 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Cameron Wake\"><PERSON></a>, American football player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Cameron Wake\"><PERSON></a>, American football player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1983", "text": "<PERSON>, English wrestler", "html": "1983 - <a href=\"https://wikipedia.org/wiki/<PERSON>_Ma<PERSON>ick\" title=\"Drake Maverick\"><PERSON></a>, English wrestler", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_Ma<PERSON>ick\" title=\"Drake Maverick\"><PERSON></a>, English wrestler", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_Ma<PERSON>ick"}]}, {"year": "1983", "text": "<PERSON><PERSON><PERSON>, Montenegrin basketball player", "html": "1983 - <a href=\"https://wikipedia.org/wiki/S<PERSON><PERSON>_Vrane%C5%A1\" title=\"<PERSON><PERSON><PERSON> V<PERSON>\"><PERSON><PERSON><PERSON></a>, Montenegrin basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_Vrane%C5%A1\" title=\"<PERSON><PERSON><PERSON> Vraneš\"><PERSON><PERSON><PERSON></a>, Montenegrin basketball player", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Slavko_Vrane%C5%A1"}]}, {"year": "1984", "text": "<PERSON>, American entertainer", "html": "1984 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>\" title=\"<PERSON>udi\"><PERSON></a>, American entertainer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>\" title=\"Kid <PERSON>udi\"><PERSON></a>, American entertainer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>"}]}, {"year": "1984", "text": "<PERSON>, Brazilian mixed martial artist", "html": "1984 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Brazilian mixed martial artist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Brazilian mixed martial artist", "links": [{"title": "<PERSON> Santos", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1984", "text": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, Japanese sumo wrestler", "html": "1984 - <a href=\"https://wikipedia.org/wiki/Kotosh%C5%8D<PERSON><PERSON>_<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON></a>, Japanese sumo wrestler", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Kotosh%C5%8D<PERSON><PERSON>_<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON></a>, Japanese sumo wrestler", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Kotosh%C5%8Dgi<PERSON>_<PERSON><PERSON>"}]}, {"year": "1985", "text": "<PERSON><PERSON><PERSON>, Argentinian tennis player", "html": "1985 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Argentinian tennis player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Argentinian tennis player", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1987", "text": "<PERSON>, Irish wrestler", "html": "1987 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Irish wrestler", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Irish wrestler", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1987", "text": "<PERSON><PERSON>, Brazilian footballer", "html": "1987 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_(footballer,_born_1987)\" title=\"<PERSON><PERSON> (footballer, born 1987)\"><PERSON><PERSON></a>, Brazilian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_(footballer,_born_1987)\" title=\"<PERSON><PERSON> (footballer, born 1987)\"><PERSON><PERSON></a>, Brazilian footballer", "links": [{"title": "<PERSON><PERSON> (footballer, born 1987)", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_(footballer,_born_1987)"}]}, {"year": "1987", "text": "<PERSON><PERSON>, Turkish footballer", "html": "1987 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Turkish footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Turkish footballer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1989", "text": "<PERSON><PERSON><PERSON>, American football player and athlete", "html": "1989 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_Best\" title=\"<PERSON><PERSON><PERSON> Best\"><PERSON><PERSON><PERSON></a>, American football player and athlete", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON> Best\"><PERSON><PERSON><PERSON></a>, American football player and athlete", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1989", "text": "<PERSON><PERSON>, South Korean singer", "html": "1989 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>-ra\" title=\"<PERSON>on Bo-ra\"><PERSON><PERSON>a</a>, South Korean singer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>-ra\" title=\"<PERSON>on Bo-ra\"><PERSON><PERSON>a</a>, South Korean singer", "links": [{"title": "<PERSON><PERSON>-ra", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>-ra"}]}, {"year": "1989", "text": "<PERSON>, Canadian-American actress", "html": "1989 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> B<PERSON>\"><PERSON></a>, Canadian-American actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> B<PERSON>\"><PERSON></a>, Canadian-American actress", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON><PERSON>"}]}, {"year": "1989", "text": "<PERSON><PERSON>, Israeli Olympic badminton player", "html": "1989 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Israeli Olympic badminton player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Israeli Olympic badminton player", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1990", "text": "<PERSON>, Australian track and field athlete", "html": "1990 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian track and field athlete", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian track and field athlete", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1990", "text": "<PERSON><PERSON>, Mexican actress and singer", "html": "1990 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_Gonz%C3%A1lez\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Mexican actress and singer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>nz%C3%A1lez\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Mexican actress and singer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Eiza_Gonz%C3%A1lez"}]}, {"year": "1990", "text": "<PERSON>, Italian-Swiss ice hockey player and coach", "html": "1990 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian-Swiss ice hockey player and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian-Swiss ice hockey player and coach", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1990", "text": "<PERSON>, Australian cricketer", "html": "1990 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian cricketer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian cricketer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1990", "text": "<PERSON>, American actor", "html": "1990 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1991", "text": "<PERSON>, Canadian ice hockey player", "html": "1991 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1991", "text": "<PERSON>, American YouTuber", "html": "1991 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, American YouTuber", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, American YouTuber", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1993", "text": "<PERSON>, English track cyclist", "html": "1993 - <a href=\"https://wikipedia.org/wiki/<PERSON>_Marchant\" title=\"<PERSON> Marchant\"><PERSON></a>, English track cyclist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_Marchant\" title=\"<PERSON> Marchant\"><PERSON></a>, English track cyclist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_Marchant"}]}, {"year": "1993", "text": "<PERSON><PERSON>, Japanese baseball player", "html": "1993 - <a href=\"https://wikipedia.org/wiki/Kodai_Senga\" title=\"Kodai Senga\"><PERSON><PERSON></a>, Japanese baseball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Kodai_Senga\" title=\"Kodai Senga\"><PERSON><PERSON> Senga</a>, Japanese baseball player", "links": [{"title": "Kodai Senga", "link": "https://wikipedia.org/wiki/Kodai_Senga"}]}, {"year": "1993", "text": "<PERSON><PERSON><PERSON><PERSON>, Thai actor", "html": "1993 - <a href=\"https://wikipedia.org/wiki/Thitipoom_<PERSON>\" title=\"Thitipoom <PERSON>hun\"><PERSON><PERSON><PERSON><PERSON></a>, Thai actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Thitipoom_<PERSON>hun\" title=\"Thitipoom <PERSON>pa<PERSON>hun\"><PERSON><PERSON><PERSON><PERSON></a>, Thai actor", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Thitipoom_Techaapa<PERSON>hun"}]}, {"year": "1995", "text": "<PERSON>, American actress", "html": "1995 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1995", "text": "<PERSON>, English diver", "html": "1995 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English diver", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English diver", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1996", "text": "<PERSON><PERSON><PERSON>, Mexican trampoline gymnast", "html": "1996 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Mexican trampoline gymnast", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Mexican trampoline gymnast", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1997", "text": "<PERSON>, Canadian ice hockey player", "html": "1997 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1997", "text": "<PERSON>, American ice hockey player", "html": "1997 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(ice_hockey,_born_1997)\" title=\"<PERSON> (ice hockey, born 1997)\"><PERSON></a>, American ice hockey player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(ice_hockey,_born_1997)\" title=\"<PERSON> (ice hockey, born 1997)\"><PERSON></a>, American ice hockey player", "links": [{"title": "<PERSON> (ice hockey, born 1997)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(ice_hockey,_born_1997)"}]}, {"year": "2000", "text": "<PERSON><PERSON>, Australian actress", "html": "2000 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Australian actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Australian actress", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Markella_<PERSON>"}]}, {"year": "2000", "text": "<PERSON>, American baseball player", "html": "2000 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>oo\" title=\"<PERSON> Woo\"><PERSON></a>, American baseball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_Woo\" title=\"<PERSON> Woo\"><PERSON></a>, American baseball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_Woo"}]}, {"year": "2001", "text": "<PERSON>, English footballer", "html": "2001 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(footballer)\" title=\"<PERSON> (footballer)\"><PERSON></a>, English footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(footballer)\" title=\"<PERSON> (footballer)\"><PERSON></a>, English footballer", "links": [{"title": "<PERSON> (footballer)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(footballer)"}]}, {"year": "2002", "text": "<PERSON><PERSON>, South African singer and songwriter.", "html": "2002 - <a href=\"https://wikipedia.org/wiki/Ty<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, South African singer and songwriter.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Ty<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, South African singer and songwriter.", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Tyla"}]}, {"year": "2002", "text": "<PERSON><PERSON><PERSON>, American football player", "html": "2002 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"B<PERSON><PERSON> Robinson\"><PERSON><PERSON><PERSON></a>, American football player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"B<PERSON><PERSON> Robinson\"><PERSON><PERSON><PERSON></a>, American football player", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "2003", "text": "<PERSON><PERSON>, American basketball player", "html": "2003 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American basketball player", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "2003", "text": "<PERSON><PERSON>, American basketball player", "html": "2003 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American basketball player", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "2005", "text": "Prince <PERSON><PERSON>, second son of King <PERSON> II of Jordan", "html": "2005 - <a href=\"https://wikipedia.org/wiki/Prince_<PERSON><PERSON>_bin_<PERSON>\" title=\"Prince <PERSON><PERSON> bin <PERSON>\">Prince <PERSON><PERSON></a>, second son of <a href=\"https://wikipedia.org/wiki/King_<PERSON>_<PERSON>_of_Jordan\" class=\"mw-redirect\" title=\"King <PERSON> II of Jordan\">King <PERSON> of Jordan</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Prince_<PERSON><PERSON>_bin_<PERSON>\" title=\"Prince <PERSON><PERSON> bin <PERSON>\">Prince <PERSON><PERSON></a>, second son of <a href=\"https://wikipedia.org/wiki/King_<PERSON>_<PERSON>_of_Jordan\" class=\"mw-redirect\" title=\"King <PERSON> of Jordan\">King <PERSON> II of Jordan</a>", "links": [{"title": "Prince <PERSON><PERSON> bin <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_bin_<PERSON>"}, {"title": "King <PERSON> of Jordan", "link": "https://wikipedia.org/wiki/King_<PERSON>_<PERSON>_of_Jordan"}]}], "Deaths": [{"year": "680", "text": "<PERSON><PERSON><PERSON><PERSON>, Frankish queen (b. 626)", "html": "680 - <a href=\"https://wikipedia.org/wiki/Balthild\" class=\"mw-redirect\" title=\"Balthild\"><PERSON><PERSON><PERSON><PERSON></a>, <PERSON><PERSON> queen (b. 626)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Balthild\" class=\"mw-redirect\" title=\"Balthild\"><PERSON><PERSON><PERSON><PERSON></a>, Frankish queen (b. 626)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Balthild"}]}, {"year": "970", "text": "<PERSON> of Bulgaria", "html": "970 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Bulgaria\" title=\"<PERSON> of Bulgaria\"><PERSON> of Bulgaria</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Bulgaria\" title=\"<PERSON> of Bulgaria\"><PERSON> of Bulgaria</a>", "links": [{"title": "<PERSON> of Bulgaria", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Bulgaria"}]}, {"year": "1030", "text": "<PERSON>, Duke of Aquitaine (b. 969)", "html": "1030 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Duke_of_Aquitaine\" title=\"<PERSON>, Duke of Aquitaine\"><PERSON>, Duke of Aquitaine</a> (b. 969)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Duke_of_Aquitaine\" title=\"<PERSON>, Duke of Aquitaine\"><PERSON>, Duke of Aquitaine</a> (b. 969)", "links": [{"title": "<PERSON>, Duke of Aquitaine", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Duke_of_Aquitaine"}]}, {"year": "1181", "text": "Emperor <PERSON><PERSON><PERSON> of Japan (b. 1161)", "html": "1181 - <a href=\"https://wikipedia.org/wiki/Emperor_<PERSON><PERSON><PERSON>\" title=\"Emperor <PERSON><PERSON><PERSON>\">Emperor <PERSON><PERSON><PERSON></a> of Japan (b. 1161)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Emperor_<PERSON><PERSON><PERSON>\" title=\"Emperor <PERSON><PERSON><PERSON>\">Emperor <PERSON><PERSON><PERSON></a> of Japan (b. 1161)", "links": [{"title": "Emperor <PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Emperor_<PERSON><PERSON><PERSON>"}]}, {"year": "1240", "text": "<PERSON><PERSON><PERSON>, Leonese lawyer and cardinal (b. 1165)", "html": "1240 - <a href=\"https://wikipedia.org/wiki/Pelagio_Galvani\" title=\"Pelagio Galvani\"><PERSON><PERSON><PERSON></a>, Leonese lawyer and cardinal (b. 1165)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Pelagio_Galvani\" title=\"Pelagio Galvani\"><PERSON><PERSON><PERSON> Gal<PERSON></a>, Leonese lawyer and cardinal (b. 1165)", "links": [{"title": "Pelagio Galvani", "link": "https://wikipedia.org/wiki/Pelagio_Galvani"}]}, {"year": "1314", "text": "<PERSON> of Saint Omer", "html": "1314 - <a href=\"https://wikipedia.org/wiki/<PERSON>_III_of_Saint_Omer\" class=\"mw-redirect\" title=\"<PERSON> III of Saint Omer\"><PERSON> of Saint Omer</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_III_of_Saint_Omer\" class=\"mw-redirect\" title=\"<PERSON> III of Saint Omer\"><PERSON> of Saint Omer</a>", "links": [{"title": "<PERSON> of Saint Omer", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Saint_<PERSON>"}]}, {"year": "1344", "text": "<PERSON>, 1st Earl of Salisbury (b. 1301)", "html": "1344 - <a href=\"https://wikipedia.org/wiki/<PERSON>,_1st_Earl_of_Salisbury\" class=\"mw-redirect\" title=\"<PERSON>, 1st Earl <PERSON> Salisbury\"><PERSON>, 1st Earl of Salisbury</a> (b. 1301)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>,_1st_Earl_of_Salisbury\" class=\"mw-redirect\" title=\"<PERSON>, 1st Earl of Salisbury\"><PERSON>, 1st Earl of Salisbury</a> (b. 1301)", "links": [{"title": "<PERSON>, 1st Earl of Salisbury", "link": "https://wikipedia.org/wiki/<PERSON>,_1st_Earl_of_Salisbury"}]}, {"year": "1384", "text": "<PERSON>, Count of Flanders (b. 1330)", "html": "1384 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Count_of_Flanders\" title=\"<PERSON>, Count of Flanders\"><PERSON>, Count of Flanders</a> (b. 1330)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Count_of_Flanders\" title=\"<PERSON>, Count of Flanders\"><PERSON>, Count of Flanders</a> (b. 1330)", "links": [{"title": "<PERSON>, Count of Flanders", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Count_of_Flanders"}]}, {"year": "1574", "text": "<PERSON><PERSON><PERSON>, Portuguese historian and philosopher (b. 1502)", "html": "1574 - <a href=\"https://wikipedia.org/wiki/Dami%C3%A3o_de_G%C3%B3is\" title=\"<PERSON><PERSON><PERSON>óis\"><PERSON><PERSON><PERSON></a>, Portuguese historian and philosopher (b. 1502)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Dami%C3%A3o_de_G%C3%B3is\" title=\"<PERSON><PERSON><PERSON> Góis\"><PERSON><PERSON><PERSON></a>, Portuguese historian and philosopher (b. 1502)", "links": [{"title": "Damião de Góis", "link": "https://wikipedia.org/wiki/Dami%C3%A3o_de_G%C3%B3is"}]}, {"year": "1606", "text": "<PERSON><PERSON>, English criminal (b. 1578)", "html": "1606 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, English criminal (b. 1578)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, English criminal (b. 1578)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>"}]}, {"year": "1606", "text": "<PERSON>, English conspirator (b. 1570)", "html": "1606 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(Gunpowder_Plot)\" title=\"<PERSON> (Gunpowder Plot)\"><PERSON></a>, English conspirator (b. 1570)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(Gunpowder_Plot)\" title=\"<PERSON> (Gunpowder Plot)\"><PERSON></a>, English conspirator (b. 1570)", "links": [{"title": "<PERSON> (Gunpowder Plot)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(Gunpowder_Plot)"}]}, {"year": "1606", "text": "<PERSON>, English conspirator (b. 1565)", "html": "1606 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, English conspirator (b. 1565)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, English conspirator (b. 1565)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1649", "text": "<PERSON> of England, Scotland, and Ireland (b. 1600)", "html": "1649 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_England\" title=\"<PERSON> of England\"><PERSON> of England, Scotland, and Ireland</a> (b. 1600)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_England\" title=\"<PERSON> of England\"><PERSON> of England, Scotland, and Ireland</a> (b. 1600)", "links": [{"title": "<PERSON> of England", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_England"}]}, {"year": "1664", "text": "<PERSON><PERSON><PERSON>, Dutch mayor (b. 1599)", "html": "1664 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Dutch mayor (b. 1599)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Dutch mayor (b. 1599)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1730", "text": "<PERSON> of Russia (b. 1715)", "html": "1730 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Russia\" title=\"<PERSON> II of Russia\"><PERSON> of Russia</a> (b. 1715)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Russia\" title=\"<PERSON> II of Russia\"><PERSON> of Russia</a> (b. 1715)", "links": [{"title": "<PERSON> of Russia", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Russia"}]}, {"year": "1770", "text": "<PERSON>, Maltese linguist, historian and cleric (b. 1712)", "html": "1770 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>_<PERSON>_<PERSON>dan<PERSON>\" title=\"Giovanni <PERSON> Soldanis\"><PERSON></a>, Maltese linguist, historian and cleric (b. 1712)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>_<PERSON>_<PERSON>dan<PERSON>\" title=\"<PERSON>danis\"><PERSON></a>, Maltese linguist, historian and cleric (b. 1712)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1836", "text": "<PERSON>, American seamstress, said to have designed the American Flag (b. 1752)", "html": "1836 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American seamstress, said to have designed the <a href=\"https://wikipedia.org/wiki/Flag_of_the_United_States\" title=\"Flag of the United States\">American Flag</a> (b. 1752)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American seamstress, said to have designed the <a href=\"https://wikipedia.org/wiki/Flag_of_the_United_States\" title=\"Flag of the United States\">American Flag</a> (b. 1752)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Flag of the United States", "link": "https://wikipedia.org/wiki/Flag_of_the_United_States"}]}, {"year": "1838", "text": "<PERSON><PERSON><PERSON><PERSON>, American tribal leader (b. 1804)", "html": "1838 - <a href=\"https://wikipedia.org/wiki/Osceola\" title=\"Osceola\"><PERSON><PERSON><PERSON><PERSON></a>, American tribal leader (b. 1804)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Os<PERSON>ola\" title=\"Osceola\"><PERSON><PERSON><PERSON><PERSON></a>, American tribal leader (b. 1804)", "links": [{"title": "Osceola", "link": "https://wikipedia.org/wiki/Osceola"}]}, {"year": "1858", "text": "<PERSON><PERSON><PERSON><PERSON>, Dutch zoologist and ornithologist (b. 1778)", "html": "1858 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Dutch zoologist and ornithologist (b. 1778)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Dutch zoologist and ornithologist (b. 1778)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1867", "text": "Emperor <PERSON><PERSON><PERSON> of Japan (b. 1831)", "html": "1867 - <a href=\"https://wikipedia.org/wiki/Emperor_K%C5%8Dmei\" title=\"Emperor <PERSON><PERSON><PERSON>\">Emperor <PERSON><PERSON><PERSON></a> of Japan (b. 1831)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Emperor_K%C5%8Dmei\" title=\"Emperor <PERSON><PERSON><PERSON>\">Emperor <PERSON><PERSON><PERSON></a> of Japan (b. 1831)", "links": [{"title": "Emperor <PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Emperor_K%C5%8Dmei"}]}, {"year": "1869", "text": "<PERSON>, Irish author (b. 1794)", "html": "1869 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Irish author (b. 1794)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Irish author (b. 1794)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1881", "text": "<PERSON>, English poet and herpetologist (b. 1844)", "html": "1881 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%27Shaughnessy\" title=\"<PERSON>\"><PERSON></a>, English poet and herpetologist (b. 1844)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%27Shaughnessy\" title=\"<PERSON>\"><PERSON></a>, English poet and herpetologist (b. 1844)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Arthur_O%27Shaughnessy"}]}, {"year": "1889", "text": "<PERSON>, Crown Prince of Austria, heir apparent to the throne of Austria-Hungary (b. 1858)", "html": "1889 - <a href=\"https://wikipedia.org/wiki/<PERSON>,_Crown_Prince_of_Austria\" title=\"<PERSON>, Crown Prince of Austria\"><PERSON>, Crown Prince of Austria</a>, <a href=\"https://wikipedia.org/wiki/Heir_apparent\" title=\"Heir apparent\">heir apparent</a> to the throne of Austria-Hungary (b. 1858)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>,_Crown_Prince_of_Austria\" title=\"<PERSON>, Crown Prince of Austria\"><PERSON>, Crown Prince of Austria</a>, <a href=\"https://wikipedia.org/wiki/Heir_apparent\" title=\"Heir apparent\">heir apparent</a> to the throne of Austria-Hungary (b. 1858)", "links": [{"title": "<PERSON>, Crown Prince of Austria", "link": "https://wikipedia.org/wiki/<PERSON>,_Crown_Prince_of_Austria"}, {"title": "Heir apparent", "link": "https://wikipedia.org/wiki/Heir_apparent"}]}, {"year": "1923", "text": "<PERSON><PERSON>, Benedictine abbot (b. 1858)", "html": "1923 - <a href=\"https://wikipedia.org/wiki/Columba_Marmion\" title=\"Columba Marmion\"><PERSON><PERSON></a>, Benedictine abbot (b. 1858)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Columba_Marmion\" title=\"Columba Marmion\"><PERSON><PERSON></a>, Benedictine abbot (b. 1858)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Columba_Marmion"}]}, {"year": "1926", "text": "<PERSON>, American actress (b. 1896)", "html": "1926 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress (b. 1896)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress (b. 1896)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1928", "text": "<PERSON>, Danish physician and academic, Nobel Prize laureate (b. 1867)", "html": "1928 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Danish physician and academic, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Physiology_or_Medicine\" title=\"Nobel Prize in Physiology or Medicine\">Nobel Prize</a> laureate (b. 1867)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Danish physician and academic, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Physiology_or_Medicine\" title=\"Nobel Prize in Physiology or Medicine\">Nobel Prize</a> laureate (b. 1867)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Nobel Prize in Physiology or Medicine", "link": "https://wikipedia.org/wiki/Nobel_Prize_in_Physiology_or_Medicine"}]}, {"year": "1934", "text": "<PERSON>, American publisher, founded the Doubleday Publishing Company (b. 1862)", "html": "1934 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American publisher, founded the <a href=\"https://wikipedia.org/wiki/Doubleday_(publisher)\" title=\"Double<PERSON> (publisher)\">Doubleday Publishing Company</a> (b. 1862)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American publisher, founded the <a href=\"https://wikipedia.org/wiki/Doubleday_(publisher)\" title=\"Double<PERSON> (publisher)\">Doubleday Publishing Company</a> (b. 1862)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "Doubleday (publisher)", "link": "https://wikipedia.org/wiki/Doubleday_(publisher)"}]}, {"year": "1947", "text": "<PERSON>, English botanist and physiologist (b. 1866)", "html": "1947 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English botanist and physiologist (b. 1866)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English botanist and physiologist (b. 1866)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1948", "text": "<PERSON>, Australian air marshal (b. 1895)", "html": "1948 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(RAF_officer)\" title=\"<PERSON> (RAF officer)\"><PERSON></a>, Australian air marshal (b. 1895)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(RAF_officer)\" title=\"<PERSON> (RAF officer)\"><PERSON></a>, Australian air marshal (b. 1895)", "links": [{"title": "<PERSON> (RAF officer)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(RAF_officer)"}]}, {"year": "1948", "text": "<PERSON><PERSON><PERSON>, leader of the Indian independence movement against British rule (b. 1869)", "html": "1948 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, leader of the <a href=\"https://wikipedia.org/wiki/Indian_independence_movement\" title=\"Indian independence movement\">Indian independence movement</a> against <a href=\"https://wikipedia.org/wiki/British_Raj\" title=\"British Raj\">British rule</a> (b. 1869)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, leader of the <a href=\"https://wikipedia.org/wiki/Indian_independence_movement\" title=\"Indian independence movement\">Indian independence movement</a> against <a href=\"https://wikipedia.org/wiki/British_Raj\" title=\"British Raj\">British rule</a> (b. 1869)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}, {"title": "Indian independence movement", "link": "https://wikipedia.org/wiki/Indian_independence_movement"}, {"title": "British Raj", "link": "https://wikipedia.org/wiki/British_Raj"}]}, {"year": "1948", "text": "<PERSON><PERSON>, American pilot and engineer, co-founded the Wright Company (b. 1871)", "html": "1948 - <a href=\"https://wikipedia.org/wiki/<PERSON>_brothers\" title=\"<PERSON> brothers\"><PERSON><PERSON></a>, American pilot and engineer, co-founded the <a href=\"https://wikipedia.org/wiki/Wright_Company\" title=\"Wright Company\">Wright Company</a> (b. 1871)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_brothers\" title=\"<PERSON> brothers\"><PERSON><PERSON></a>, American pilot and engineer, co-founded the <a href=\"https://wikipedia.org/wiki/Wright_Company\" title=\"Wright Company\">Wright Company</a> (b. 1871)", "links": [{"title": "<PERSON> brothers", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Wright Company", "link": "https://wikipedia.org/wiki/Wright_Company"}]}, {"year": "1951", "text": "<PERSON>, Austrian-German engineer and businessman, founded <PERSON> (b. 1875)", "html": "1951 - <a href=\"https://wikipedia.org/wiki/<PERSON>_Porsche\" title=\"Ferdinand Porsche\"><PERSON></a>, Austrian-German engineer and businessman, founded <a href=\"https://wikipedia.org/wiki/Porsche\" title=\"Porsche\">Porsche</a> (b. 1875)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_Porsche\" title=\"Ferdinand Porsche\"><PERSON></a>, Austrian-German engineer and businessman, founded <a href=\"https://wikipedia.org/wiki/Porsche\" title=\"Porsche\">Porsche</a> (b. 1875)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Porsche", "link": "https://wikipedia.org/wiki/Porsche"}]}, {"year": "1958", "text": "<PERSON>, Swiss painter (b. 1878)", "html": "1958 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swiss painter (b. 1878)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swiss painter (b. 1878)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1958", "text": "<PERSON>, German engineer and businessman; founded the Heinkel Aircraft Company (b. 1888)", "html": "1958 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German engineer and businessman; founded the <a href=\"https://wikipedia.org/wiki/Heinkel\" title=\"Heinkel\">Heinkel Aircraft Company</a> (b. 1888)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German engineer and businessman; founded the <a href=\"https://wikipedia.org/wiki/Heinkel\" title=\"Heinkel\">Heinkel Aircraft Company</a> (b. 1888)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>el"}]}, {"year": "1962", "text": "<PERSON>, Brazilian physician and engineer (b. 1894)", "html": "1962 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Brazilian physician and engineer (b. 1894)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Brazilian physician and engineer (b. 1894)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1963", "text": "<PERSON>, French pianist and composer (b. 1899)", "html": "1963 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French pianist and composer (b. 1899)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French pianist and composer (b. 1899)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1966", "text": "<PERSON><PERSON>, Estonian flute player, conductor, and educator (b. 1912)", "html": "1966 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Estonian flute player, conductor, and educator (b. 1912)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Estonian flute player, conductor, and educator (b. 1912)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>l"}]}, {"year": "1968", "text": "<PERSON><PERSON><PERSON><PERSON>, Indian poet, playwright, and journalist (b. 1889)", "html": "1968 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Indian poet, playwright, and journalist (b. 1889)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Indian poet, playwright, and journalist (b. 1889)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>i"}]}, {"year": "1969", "text": "<PERSON>, Belgian friar, Nobel Prize laureate (b. 1910)", "html": "1969 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Belgian friar, <a href=\"https://wikipedia.org/wiki/Nobel_Peace_Prize\" title=\"Nobel Peace Prize\">Nobel Prize</a> laureate (b. 1910)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Belgian friar, <a href=\"https://wikipedia.org/wiki/Nobel_Peace_Prize\" title=\"Nobel Peace Prize\">Nobel Prize</a> laureate (b. 1910)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Nobel Peace Prize", "link": "https://wikipedia.org/wiki/Nobel_Peace_Prize"}]}, {"year": "1973", "text": "<PERSON>, American economist and academic (b. 1885)", "html": "1973 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(economist)\" title=\"<PERSON> (economist)\"><PERSON></a>, American economist and academic (b. 1885)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>(economist)\" title=\"<PERSON> (economist)\"><PERSON></a>, American economist and academic (b. 1885)", "links": [{"title": "<PERSON> (economist)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>(economist)"}]}, {"year": "1973", "text": "<PERSON><PERSON><PERSON>, Bissau-Guinean revolutionary (b. 1943)", "html": "1973 - <a href=\"https://wikipedia.org/wiki/Titina_Sil%C3%A1\" title=\"Titina Silá\"><PERSON><PERSON><PERSON></a>, Bissau-Guinean revolutionary (b. 1943)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Titina_Sil%C3%A1\" title=\"Titina Silá\"><PERSON><PERSON><PERSON></a>, Bissau-Guinean revolutionary (b. 1943)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Titina_Sil%C3%A1"}]}, {"year": "1974", "text": "<PERSON><PERSON>, Estonian pianist and composer (b. 1910)", "html": "1974 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_Roots\" title=\"<PERSON><PERSON> Roots\"><PERSON><PERSON></a>, Estonian pianist and composer (b. 1910)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_Roots\" title=\"<PERSON><PERSON> Roots\"><PERSON><PERSON></a>, Estonian pianist and composer (b. 1910)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1977", "text": "<PERSON>, French zoologist (b. 1883)", "html": "1977 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French zoologist (b. 1883)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French zoologist (b. 1883)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1980", "text": "Professor <PERSON>, American singer-songwriter and pianist (b. 1918)", "html": "1980 - <a href=\"https://wikipedia.org/wiki/Professor_<PERSON><PERSON>\" title=\"Professor <PERSON><PERSON>\">Professor <PERSON><PERSON><PERSON></a>, American singer-songwriter and pianist (b. 1918)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Professor_<PERSON><PERSON>\" title=\"Professor <PERSON>\">Professor <PERSON><PERSON><PERSON></a>, American singer-songwriter and pianist (b. 1918)", "links": [{"title": "Professor <PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Professor_<PERSON><PERSON><PERSON>"}]}, {"year": "1982", "text": "<PERSON><PERSON><PERSON>, American singer-songwriter and guitarist (b. 1912)", "html": "1982 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>%27_<PERSON>\" title=\"Light<PERSON>' <PERSON>\"><PERSON><PERSON><PERSON> <PERSON></a>, American singer-songwriter and guitarist (b. 1912)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>nin%27_<PERSON>\" title=\"Light<PERSON>' <PERSON>\"><PERSON><PERSON><PERSON> <PERSON></a>, American singer-songwriter and guitarist (b. 1912)", "links": [{"title": "Lightnin' <PERSON>", "link": "https://wikipedia.org/wiki/Lightnin%27_<PERSON>"}]}, {"year": "1991", "text": "<PERSON>, American physicist and engineer, Nobel Prize laureate (b. 1908)", "html": "1991 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American physicist and engineer, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Physics\" title=\"Nobel Prize in Physics\">Nobel Prize</a> laureate (b. 1908)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American physicist and engineer, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Physics\" title=\"Nobel Prize in Physics\">Nobel Prize</a> laureate (b. 1908)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Nobel Prize in Physics", "link": "https://wikipedia.org/wiki/Nobel_Prize_in_Physics"}]}, {"year": "1991", "text": "<PERSON>, American photographer and educator (b. 1907)", "html": "1991 - <a href=\"https://wikipedia.org/wiki/Clifton_C._Edom\" title=\"Clifton C. Edom\"><PERSON></a>, American photographer and educator (b. 1907)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Clifton_C._Edom\" title=\"Clifton C. Edom\"><PERSON></a>, American photographer and educator (b. 1907)", "links": [{"title": "Clifton C. Edom", "link": "https://wikipedia.org/wiki/Clifton_C._Edom"}]}, {"year": "1993", "text": "<PERSON> of Yugoslavia, the last Queen of Yugoslavia (b. 1921)", "html": "1993 - <a href=\"https://wikipedia.org/wiki/Alexandra_of_Yugoslavia\" title=\"Alexandra of Yugoslavia\"><PERSON> of Yugoslavia</a>, the last Queen of Yugoslavia (b. 1921)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Alexandra_of_Yugoslavia\" title=\"Alexandra of Yugoslavia\"><PERSON> of Yugoslavia</a>, the last Queen of Yugoslavia (b. 1921)", "links": [{"title": "Alexandra of Yugoslavia", "link": "https://wikipedia.org/wiki/Alexandra_of_Yugoslavia"}]}, {"year": "1994", "text": "<PERSON>, French soldier and author (b. 1912)", "html": "1994 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French soldier and author (b. 1912)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French soldier and author (b. 1912)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1999", "text": "<PERSON><PERSON>, American actor (b. 1919)", "html": "1999 - <a href=\"https://wikipedia.org/wiki/Huntz_Hall\" title=\"Huntz Hall\"><PERSON><PERSON></a>, American actor (b. 1919)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Huntz_Hall\" title=\"Huntz Hall\"><PERSON><PERSON></a>, American actor (b. 1919)", "links": [{"title": "Huntz Hall", "link": "https://wikipedia.org/wiki/Huntz_Hall"}]}, {"year": "1999", "text": "<PERSON>, American journalist (b. 1909)", "html": "1999 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American journalist (b. 1909)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American journalist (b. 1909)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>"}]}, {"year": "2001", "text": "<PERSON><PERSON><PERSON>, French soldier and actor (b. 1911)", "html": "2001 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, French soldier and actor (b. 1911)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, French soldier and actor (b. 1911)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>"}]}, {"year": "2001", "text": "<PERSON><PERSON>, English air marshal and pilot (b. 1915)", "html": "2001 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_(RAF_officer)\" title=\"<PERSON><PERSON> (RAF officer)\"><PERSON><PERSON></a>, English air marshal and pilot (b. 1915)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_(RAF_officer)\" title=\"<PERSON><PERSON> (RAF officer)\"><PERSON><PERSON></a>, English air marshal and pilot (b. 1915)", "links": [{"title": "<PERSON><PERSON> (RAF officer)", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_(RAF_officer)"}]}, {"year": "2001", "text": "<PERSON>, American surgeon and educator (b. 1915)", "html": "2001 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American surgeon and educator (b. 1915)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American surgeon and educator (b. 1915)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "2004", "text": "<PERSON><PERSON>, Swiss-American sociologist (b. 1944)", "html": "2004 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_(sociologist)\" title=\"<PERSON><PERSON> (sociologist)\"><PERSON><PERSON></a>, Swiss-American sociologist (b. 1944)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_(sociologist)\" title=\"<PERSON><PERSON> (sociologist)\"><PERSON><PERSON></a>, Swiss-American sociologist (b. 1944)", "links": [{"title": "<PERSON><PERSON> (sociologist)", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_(sociologist)"}]}, {"year": "2005", "text": "<PERSON><PERSON>, Canadian-Scottish violinist (b. 1971)", "html": "2005 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Canadian-Scottish violinist (b. 1971)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Canadian-Scottish violinist (b. 1971)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "2006", "text": "<PERSON><PERSON>, American author and activist (b. 1927)", "html": "2006 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"Core<PERSON> Scott King\"><PERSON><PERSON></a>, American author and activist (b. 1927)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"Core<PERSON> Scott King\"><PERSON><PERSON></a>, American author and activist (b. 1927)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_Scott_<PERSON>"}]}, {"year": "2006", "text": "<PERSON>, American playwright and academic (b. 1950)", "html": "2006 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American playwright and academic (b. 1950)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American playwright and academic (b. 1950)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2007", "text": "<PERSON>, American author and screenwriter (b. 1917)", "html": "2007 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author and screenwriter (b. 1917)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author and screenwriter (b. 1917)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2008", "text": "<PERSON><PERSON>, Mexican-American priest, founded the Legion of Christ and Regnum Christi (b. 1920)", "html": "2008 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Mexican-American priest, founded the <a href=\"https://wikipedia.org/wiki/Legion_of_Christ\" class=\"mw-redirect\" title=\"Legion of Christ\">Legion of Christ</a> and <a href=\"https://wikipedia.org/wiki/Regnum_Christi\" title=\"Regnum Christi\">Regnum Christi</a> (b. 1920)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Mexican-American priest, founded the <a href=\"https://wikipedia.org/wiki/Legion_of_Christ\" class=\"mw-redirect\" title=\"Legion of Christ\">Legion of Christ</a> and <a href=\"https://wikipedia.org/wiki/Regnum_Christi\" title=\"Regnum Christi\">Regnum Christi</a> (b. 1920)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}, {"title": "Legion of Christ", "link": "https://wikipedia.org/wiki/Legion_of_Christ"}, {"title": "Regnum Christi", "link": "https://wikipedia.org/wiki/Regnum_Christi"}]}, {"year": "2009", "text": "<PERSON><PERSON> <PERSON>, American soldier, pastor, and politician, 49th Governor of Alabama (b. 1933)", "html": "2009 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> <PERSON>\"><PERSON><PERSON> <PERSON></a>, American soldier, pastor, and politician, 49th <a href=\"https://wikipedia.org/wiki/Governor_of_Alabama\" class=\"mw-redirect\" title=\"Governor of Alabama\">Governor of Alabama</a> (b. 1933)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> <PERSON>\"><PERSON><PERSON> <PERSON></a>, American soldier, pastor, and politician, 49th <a href=\"https://wikipedia.org/wiki/Governor_of_Alabama\" class=\"mw-redirect\" title=\"Governor of Alabama\">Governor of Alabama</a> (b. 1933)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>"}, {"title": "Governor of Alabama", "link": "https://wikipedia.org/wiki/Governor_of_Alabama"}]}, {"year": "2010", "text": "<PERSON><PERSON><PERSON>, Kosovar accountant and politician (b. 1960)", "html": "2010 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Kosovar accountant and politician (b. 1960)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Kosovar accountant and politician (b. 1960)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>i"}]}, {"year": "2011", "text": "<PERSON>, English composer and conductor (b. 1933)", "html": "2011 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(composer)\" title=\"<PERSON> (composer)\"><PERSON></a>, English composer and conductor (b. 1933)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>(composer)\" title=\"<PERSON> (composer)\"><PERSON></a>, English composer and conductor (b. 1933)", "links": [{"title": "<PERSON> (composer)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>(composer)"}]}, {"year": "2012", "text": "<PERSON>, American football player and soldier (b. 1925)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player and soldier (b. 1925)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player and soldier (b. 1925)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2012", "text": "<PERSON><PERSON><PERSON>, Dutch author (b. 1947)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Dutch author (b. 1947)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Dutch author (b. 1947)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "2013", "text": "<PERSON><PERSON><PERSON>, Egyptian author and scholar (b. 1920)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/G<PERSON><PERSON>_al-Banna\" title=\"Gamal al-Banna\"><PERSON><PERSON><PERSON></a>, Egyptian author and scholar (b. 1920)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/G<PERSON><PERSON>_al-Banna\" title=\"G<PERSON><PERSON> al-Banna\"><PERSON><PERSON><PERSON></a>, Egyptian author and scholar (b. 1920)", "links": [{"title": "<PERSON><PERSON><PERSON> al<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_al-<PERSON>na"}]}, {"year": "2013", "text": "<PERSON>, American singer (b. 1918)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, American singer (b. 1918)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, American singer (b. 1918)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2013", "text": "<PERSON>, American baseball player and coach (b. 1931)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(baseball)\" title=\"<PERSON> (baseball)\"><PERSON></a>, American baseball player and coach (b. 1931)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(baseball)\" title=\"<PERSON> (baseball)\"><PERSON></a>, American baseball player and coach (b. 1931)", "links": [{"title": "<PERSON> (baseball)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(baseball)"}]}, {"year": "2014", "text": "<PERSON>, Polish general and photographer (b. 1914)", "html": "2014 - <a href=\"https://wikipedia.org/wiki/Stefan_Ba%C5%82uk\" title=\"<PERSON>\"><PERSON></a>, Polish general and photographer (b. 1914)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Stefan_Ba%C5%82uk\" title=\"<PERSON>\"><PERSON></a>, Polish general and photographer (b. 1914)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Stefan_Ba%C5%82uk"}]}, {"year": "2014", "text": "<PERSON> <PERSON> Hannibal, American singer-songwriter and producer (b. 1939)", "html": "2014 - <a href=\"https://wikipedia.org/wiki/The_Mighty_Hannibal\" title=\"The Mighty Hannibal\">The Mighty Hannibal</a>, American singer-songwriter and producer (b. 1939)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/The_Mighty_Hannibal\" title=\"The Mighty Hannibal\">The Mighty Hannibal</a>, American singer-songwriter and producer (b. 1939)", "links": [{"title": "The Mighty Hannibal", "link": "https://wikipedia.org/wiki/The_Mighty_Hannibal"}]}, {"year": "2014", "text": "<PERSON>, American composer and conductor (b. 1937)", "html": "2014 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American composer and conductor (b. 1937)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American composer and conductor (b. 1937)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2014", "text": "<PERSON>, American director, producer, and screenwriter (b. 1924)", "html": "2014 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>.\" title=\"<PERSON>\"><PERSON>.</a>, American director, producer, and screenwriter (b. 1924)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>.\" title=\"<PERSON>\"><PERSON>.</a>, American director, producer, and screenwriter (b. 1924)", "links": [{"title": "<PERSON>.", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>."}]}, {"year": "2014", "text": "Greater, oldest known greater flamingo and Feast Festival 2021 mascot (h. c.1919-1933)", "html": "2014 - <a href=\"https://wikipedia.org/wiki/Greater_(flamingo)\" title=\"Greater (flamingo)\">Greater</a>, oldest known <a href=\"https://wikipedia.org/wiki/Greater_flamingo\" title=\"Greater flamingo\">greater flamingo</a> and <a href=\"https://wikipedia.org/wiki/Feast_Festival\" title=\"Feast Festival\">Feast Festival</a> 2021 mascot (h. c.1919-1933)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Greater_(flamingo)\" title=\"Greater (flamingo)\">Greater</a>, oldest known <a href=\"https://wikipedia.org/wiki/Greater_flamingo\" title=\"Greater flamingo\">greater flamingo</a> and <a href=\"https://wikipedia.org/wiki/Feast_Festival\" title=\"Feast Festival\">Feast Festival</a> 2021 mascot (h. c.1919-1933)", "links": [{"title": "Greater (flamingo)", "link": "https://wikipedia.org/wiki/Greater_(flamingo)"}, {"title": "Greater flamingo", "link": "https://wikipedia.org/wiki/Greater_flamingo"}, {"title": "Feast Festival", "link": "https://wikipedia.org/wiki/Feast_Festival"}]}, {"year": "2015", "text": "<PERSON>, Austrian-American chemist, author, and playwright (b. 1923)", "html": "2015 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Austrian-American chemist, author, and playwright (b. 1923)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Austrian-American chemist, author, and playwright (b. 1923)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2015", "text": "<PERSON><PERSON>, Estonian academic, philosopher, and politician (b. 1947)", "html": "2015 - <a href=\"https://wikipedia.org/wiki/%C3%9C<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Estonian academic, philosopher, and politician (b. 1947)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/%C3%9C<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Estonian academic, philosopher, and politician (b. 1947)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/%C3%9C<PERSON>_<PERSON>ts"}]}, {"year": "2015", "text": "<PERSON><PERSON>, English actress (b. 1932)", "html": "2015 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, English actress (b. 1932)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, English actress (b. 1932)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "2015", "text": "<PERSON><PERSON><PERSON>, Dutch cyclist (b. 1923)", "html": "2015 - <a href=\"https://wikipedia.org/wiki/G<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Dutch cyclist (b. 1923)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/G<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Dutch cyclist (b. 1923)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/G<PERSON><PERSON>_<PERSON>"}]}, {"year": "2015", "text": "<PERSON><PERSON><PERSON>, Bulgarian philosopher and politician, 2nd President of Bulgaria (b. 1935)", "html": "2015 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Bulgarian philosopher and politician, 2nd <a href=\"https://wikipedia.org/wiki/List_of_heads_of_state_of_Bulgaria\" title=\"List of heads of state of Bulgaria\">President of Bulgaria</a> (b. 1935)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Bulgarian philosopher and politician, 2nd <a href=\"https://wikipedia.org/wiki/List_of_heads_of_state_of_Bulgaria\" title=\"List of heads of state of Bulgaria\">President of Bulgaria</a> (b. 1935)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}, {"title": "List of heads of state of Bulgaria", "link": "https://wikipedia.org/wiki/List_of_heads_of_state_of_Bulgaria"}]}, {"year": "2016", "text": "<PERSON>, English actor (b. 1926)", "html": "2016 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actor (b. 1926)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actor (b. 1926)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2016", "text": "<PERSON>, Salvadorian politician, President of El Salvador (b. 1959)", "html": "2016 - <a href=\"https://wikipedia.org/wiki/Francisco_Flores_P%C3%A9rez\" title=\"<PERSON>\"><PERSON></a>, Salvadorian politician, <a href=\"https://wikipedia.org/wiki/President_of_El_Salvador\" title=\"President of El Salvador\">President of El Salvador</a> (b. 1959)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Francisco_Flores_P%C3%A9rez\" title=\"<PERSON>\"><PERSON></a>, Salvadorian politician, <a href=\"https://wikipedia.org/wiki/President_of_El_Salvador\" title=\"President of El Salvador\">President of El Salvador</a> (b. 1959)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Francisco_Flores_P%C3%A9rez"}, {"title": "President of El Salvador", "link": "https://wikipedia.org/wiki/President_of_El_Salvador"}]}, {"year": "2016", "text": "<PERSON>, American activist and politician (b. 1923)", "html": "2016 - <a href=\"https://wikipedia.org/wiki/Georgia_Davis_Powers\" title=\"Georgia Davis Powers\"><PERSON></a>, American activist and politician (b. 1923)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Georgia_Davis_Powers\" title=\"Georgia Davis Powers\"><PERSON></a>, American activist and politician (b. 1923)", "links": [{"title": "Georgia Davis <PERSON>", "link": "https://wikipedia.org/wiki/Georgia_Davis_Powers"}]}, {"year": "2016", "text": "<PERSON>, French pedagogist and professor (b. 1918)", "html": "2016 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French pedagogist and professor (b. <a href=\"https://wikipedia.org/wiki/1918\" title=\"1918\">1918</a>)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French pedagogist and professor (b. <a href=\"https://wikipedia.org/wiki/1918\" title=\"1918\">1918</a>)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "1918", "link": "https://wikipedia.org/wiki/1918"}]}, {"year": "2018", "text": "<PERSON>, American actor and musician (b. 1982)", "html": "2018 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor and musician (b. 1982)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor and musician (b. 1982)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2019", "text": "<PERSON>, American actor (b. 1928)", "html": "2019 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor (b. 1928)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor (b. 1928)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2021", "text": "<PERSON>, Scottish musician (b. 1986)", "html": "2021 - <a href=\"https://wikipedia.org/wiki/<PERSON>_(musician)\" title=\"<PERSON> (musician)\"><PERSON></a>, Scottish musician (b. 1986)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(musician)\" title=\"<PERSON> (musician)\"><PERSON></a>, Scottish musician (b. 1986)", "links": [{"title": "<PERSON> (musician)", "link": "https://wikipedia.org/wiki/<PERSON>_(musician)"}]}, {"year": "2022", "text": "<PERSON><PERSON><PERSON>, American television presenter and model (b. 1991)", "html": "2022 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON> Kryst\"><PERSON><PERSON><PERSON></a>, American television presenter and model (b. 1991)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"Ch<PERSON><PERSON> Kryst\"><PERSON><PERSON><PERSON></a>, American television presenter and model (b. 1991)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "2023", "text": "<PERSON>, American Pro Football Hall of Fame executive (b. 1937)", "html": "2023 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American <a href=\"https://wikipedia.org/wiki/Pro_Football_Hall_of_Fame\" title=\"Pro Football Hall of Fame\">Pro Football Hall of Fame</a> executive (b. 1937)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American <a href=\"https://wikipedia.org/wiki/Pro_Football_Hall_of_Fame\" title=\"Pro Football Hall of Fame\">Pro Football Hall of Fame</a> executive (b. 1937)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Pro Football Hall of Fame", "link": "https://wikipedia.org/wiki/Pro_Football_Hall_of_Fame"}]}, {"year": "2023", "text": "<PERSON>, Canadian ice hockey player (b. 1939)", "html": "2023 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player (b. 1939)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player (b. 1939)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2024", "text": "<PERSON><PERSON>, American actress, singer, and dancer (b. 1933)", "html": "2024 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American actress, singer, and dancer (b. 1933)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American actress, singer, and dancer (b. 1933)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "2025", "text": "<PERSON>, American figure skater and actor (b. 1929)", "html": "2025 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American figure skater and actor (b. 1929)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American figure skater and actor (b. 1929)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2025", "text": "<PERSON>, Papua New Guinean politician, 2nd Prime Minister of Papua New Guinea (b. 1939)", "html": "2025 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Papua New Guinean politician, 2nd <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Papua_New_Guinea\" title=\"Prime Minister of Papua New Guinea\">Prime Minister of Papua New Guinea</a> (b. 1939)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Papua New Guinean politician, 2nd <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Papua_New_Guinea\" title=\"Prime Minister of Papua New Guinea\">Prime Minister of Papua New Guinea</a> (b. 1939)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Prime Minister of Papua New Guinea", "link": "https://wikipedia.org/wiki/Prime_Minister_of_Papua_New_Guinea"}]}, {"year": "2025", "text": "<PERSON>, English singer-songwriter and actress (b. 1946)", "html": "2025 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English singer-songwriter and actress (b. 1946)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English singer-songwriter and actress (b. 1946)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2025", "text": "<PERSON><PERSON>, Filipino politician (b. 1942)", "html": "2025 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Filipino politician (b. 1942)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Filipino politician (b. 1942)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}]}}