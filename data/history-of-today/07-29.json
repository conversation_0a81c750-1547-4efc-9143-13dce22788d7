{"date": "July 29", "url": "https://wikipedia.org/wiki/July_29", "data": {"Events": [{"year": "587 BC", "text": "The Neo-Babylonian Empire sacks Jerusalem and destroys the First Temple.", "html": "587 BC - 587 BC - The <a href=\"https://wikipedia.org/wiki/Neo-Babylonian_Empire\" title=\"Neo-Babylonian Empire\">Neo-Babylonian Empire</a> <a href=\"https://wikipedia.org/wiki/Siege_of_Jerusalem_(587_BC)\" title=\"Siege of Jerusalem (587 BC)\">sacks</a> Jerusalem and destroys the <a href=\"https://wikipedia.org/wiki/First_Temple\" class=\"mw-redirect\" title=\"First Temple\">First Temple</a>.", "no_year_html": "587 BC - The <a href=\"https://wikipedia.org/wiki/Neo-Babylonian_Empire\" title=\"Neo-Babylonian Empire\">Neo-Babylonian Empire</a> <a href=\"https://wikipedia.org/wiki/Siege_of_Jerusalem_(587_BC)\" title=\"Siege of Jerusalem (587 BC)\">sacks</a> Jerusalem and destroys the <a href=\"https://wikipedia.org/wiki/First_Temple\" class=\"mw-redirect\" title=\"First Temple\">First Temple</a>.", "links": [{"title": "Neo-Babylonian Empire", "link": "https://wikipedia.org/wiki/Neo-Babylonian_Empire"}, {"title": "Siege of Jerusalem (587 BC)", "link": "https://wikipedia.org/wiki/Siege_of_Jerusalem_(587_BC)"}, {"title": "First Temple", "link": "https://wikipedia.org/wiki/First_Temple"}]}, {"year": "615", "text": "<PERSON><PERSON> ascends the throne of Palenque at the age of 12.", "html": "615 - <a href=\"https://wikipedia.org/wiki/K%27inich_<PERSON><PERSON>%27_<PERSON><PERSON>\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON></a> ascends the throne of <a href=\"https://wikipedia.org/wiki/Palenque\" title=\"Pa<PERSON><PERSON>\"><PERSON><PERSON><PERSON></a> at the age of 12.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/K%27ini<PERSON>_<PERSON><PERSON>%27_<PERSON><PERSON>\" class=\"mw-redirect\" title=\"<PERSON>'<PERSON><PERSON>\"><PERSON><PERSON></a> ascends the throne of <a href=\"https://wikipedia.org/wiki/Palenque\" title=\"Palen<PERSON>\"><PERSON><PERSON><PERSON></a> at the age of 12.", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/K%27inich_<PERSON>ab%27_Pakal"}, {"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Palenque"}]}, {"year": "904", "text": "Sack of Thessalonica: Saracen raiders under <PERSON> of Tripoli sack Thessaloniki, the Byzantine Empire's second-largest city, after a short siege, and plunder it for a week.", "html": "904 - <a href=\"https://wikipedia.org/wiki/Sack_of_Thessalonica_(904)\" title=\"Sack of Thessalonica (904)\">Sack of Thessalonica</a>: Saracen raiders under <a href=\"https://wikipedia.org/wiki/Leo_of_Tripoli\" title=\"Leo of Tripoli\"><PERSON> of Tripoli</a> sack <a href=\"https://wikipedia.org/wiki/Thessaloniki\" title=\"Thessaloniki\">Thessaloniki</a>, the <a href=\"https://wikipedia.org/wiki/Byzantine_Empire\" title=\"Byzantine Empire\">Byzantine Empire</a>'s second-largest city, after a short siege, and plunder it for a week.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Sack_of_Thessalonica_(904)\" title=\"Sack of Thessalonica (904)\">Sack of Thessalonica</a>: Saracen raiders under <a href=\"https://wikipedia.org/wiki/Leo_of_Tripoli\" title=\"Leo of Tripoli\"><PERSON> of Tripoli</a> sack <a href=\"https://wikipedia.org/wiki/Thessaloniki\" title=\"Thessaloniki\">Thessaloniki</a>, the <a href=\"https://wikipedia.org/wiki/Byzantine_Empire\" title=\"Byzantine Empire\">Byzantine Empire</a>'s second-largest city, after a short siege, and plunder it for a week.", "links": [{"title": "Sack of Thessalonica (904)", "link": "https://wikipedia.org/wiki/Sack_of_Thessalonica_(904)"}, {"title": "Leo of Tripoli", "link": "https://wikipedia.org/wiki/Leo_of_Tripoli"}, {"title": "Thessaloniki", "link": "https://wikipedia.org/wiki/Thessaloniki"}, {"title": "Byzantine Empire", "link": "https://wikipedia.org/wiki/Byzantine_Empire"}]}, {"year": "923", "text": "Battle of Firenzuola: Lombard forces under King <PERSON> and <PERSON><PERSON>, margrave of Ivrea, defeat the dethroned Emperor <PERSON><PERSON><PERSON> of Italy at Firenzuola (Tuscany).", "html": "923 - <a href=\"https://wikipedia.org/wiki/Battle_of_Firenzuola\" title=\"Battle of Firenzuola\">Battle of Firenzuola</a>: Lombard forces under King <a href=\"https://wikipedia.org/wiki/<PERSON>_II_of_Burgundy\" title=\"<PERSON> II of Burgundy\"><PERSON> II</a> and <a href=\"https://wikipedia.org/wiki/Adalbert_I_of_Ivrea\" title=\"Adalbert I of Ivrea\"><PERSON><PERSON></a>, margrave of <a href=\"https://wikipedia.org/wiki/March_of_Ivrea\" title=\"March of Ivrea\">Ivrea</a>, defeat the dethroned Emperor <a href=\"https://wikipedia.org/wiki/Berengar_I_of_Italy\" title=\"Berengar I of Italy\">Berengar I of Italy</a> at <a href=\"https://wikipedia.org/wiki/Firenzuola\" title=\"Firenzuola\">Firenzuola</a> (<a href=\"https://wikipedia.org/wiki/Tuscany\" title=\"Tuscany\">Tuscany</a>).", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Battle_of_Firenzuola\" title=\"Battle of Firenzuola\">Battle of Firenzuola</a>: Lombard forces under <PERSON> <a href=\"https://wikipedia.org/wiki/<PERSON>_II_of_Burgundy\" title=\"<PERSON> II of Burgundy\"><PERSON> II</a> and <a href=\"https://wikipedia.org/wiki/Adalbert_I_of_Ivrea\" title=\"Adalbert I of Ivrea\"><PERSON><PERSON></a>, margrave of <a href=\"https://wikipedia.org/wiki/March_of_Ivrea\" title=\"March of Ivrea\">Ivrea</a>, defeat the dethroned Emperor <a href=\"https://wikipedia.org/wiki/Berengar_I_of_Italy\" title=\"Berengar I of Italy\">Berengar I of Italy</a> at <a href=\"https://wikipedia.org/wiki/Firenzuola\" title=\"Firenzuola\">Firenzuola</a> (<a href=\"https://wikipedia.org/wiki/Tuscany\" title=\"Tuscany\">Tuscany</a>).", "links": [{"title": "Battle of Firenzuola", "link": "https://wikipedia.org/wiki/Battle_of_Firenzuola"}, {"title": "<PERSON> of Burgundy", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Burgundy"}, {"title": "<PERSON><PERSON> of Ivrea", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>_<PERSON>vrea"}, {"title": "March of Ivrea", "link": "https://wikipedia.org/wiki/March_of_Ivrea"}, {"title": "<PERSON><PERSON><PERSON> of Italy", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_I_of_Italy"}, {"title": "Firenzuola", "link": "https://wikipedia.org/wiki/Firenzuola"}, {"title": "Tuscany", "link": "https://wikipedia.org/wiki/Tuscany"}]}, {"year": "1014", "text": "Byzantine-Bulgarian wars: Battle of Kleidion: Byzantine emperor <PERSON> inflicts a decisive defeat on the Bulgarian army, and his subsequent treatment of 15,000 prisoners reportedly causes Tsar <PERSON><PERSON> of Bulgaria to die of a heart attack less than three months later, on October 6.", "html": "1014 - <a href=\"https://wikipedia.org/wiki/Byzantine%E2%80%93Bulgarian_wars\" title=\"Byzantine-Bulgarian wars\">Byzantine-Bulgarian wars</a>: <a href=\"https://wikipedia.org/wiki/Battle_of_Kleidion\" title=\"Battle of Kleidion\">Battle of Kleidion</a>: <a href=\"https://wikipedia.org/wiki/List_of_Byzantine_emperors\" title=\"List of Byzantine emperors\">Byzantine emperor</a> <a href=\"https://wikipedia.org/wiki/Basil_II\" title=\"Basil II\">Basil II</a> inflicts a decisive defeat on the <a href=\"https://wikipedia.org/wiki/Bulgaria\" title=\"Bulgaria\">Bulgarian</a> army, and his subsequent treatment of 15,000 prisoners reportedly causes <a href=\"https://wikipedia.org/wiki/Samuel_of_Bulgaria\" title=\"<PERSON> of Bulgaria\">Tsar <PERSON> of Bulgaria</a> to die of a heart attack less than three months later, on <a href=\"https://wikipedia.org/wiki/October_6\" title=\"October 6\">October 6</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Byzantine%E2%80%93Bulgarian_wars\" title=\"Byzantine-Bulgarian wars\">Byzantine-Bulgarian wars</a>: <a href=\"https://wikipedia.org/wiki/Battle_of_Kleidion\" title=\"Battle of Kleidion\">Battle of Kleidion</a>: <a href=\"https://wikipedia.org/wiki/List_of_Byzantine_emperors\" title=\"List of Byzantine emperors\">Byzantine emperor</a> <a href=\"https://wikipedia.org/wiki/Basil_II\" title=\"Basil II\"><PERSON> II</a> inflicts a decisive defeat on the <a href=\"https://wikipedia.org/wiki/Bulgaria\" title=\"Bulgaria\">Bulgarian</a> army, and his subsequent treatment of 15,000 prisoners reportedly causes <a href=\"https://wikipedia.org/wiki/Samuel_of_Bulgaria\" title=\"<PERSON> of Bulgaria\">Tsar <PERSON> of Bulgaria</a> to die of a heart attack less than three months later, on <a href=\"https://wikipedia.org/wiki/October_6\" title=\"October 6\">October 6</a>.", "links": [{"title": "Byzantine-Bulgarian wars", "link": "https://wikipedia.org/wiki/Byzantine%E2%80%93Bulgarian_wars"}, {"title": "Battle of Kleidion", "link": "https://wikipedia.org/wiki/Battle_of_Kleidion"}, {"title": "List of Byzantine emperors", "link": "https://wikipedia.org/wiki/List_of_Byzantine_emperors"}, {"title": "Basil II", "link": "https://wikipedia.org/wiki/Basil_II"}, {"title": "Bulgaria", "link": "https://wikipedia.org/wiki/Bulgaria"}, {"title": "Samuel of Bulgaria", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_Bulgaria"}, {"title": "October 6", "link": "https://wikipedia.org/wiki/October_6"}]}, {"year": "1018", "text": "Count <PERSON> defeats an army sent by Emperor <PERSON> in the Battle of Vlaardingen.", "html": "1018 - Count <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Count_of_Holland\" title=\"<PERSON>, Count of Holland\"><PERSON></a> defeats an army sent by <a href=\"https://wikipedia.org/wiki/<PERSON>,_Holy_Roman_Emperor\" title=\"<PERSON> II, Holy Roman Emperor\">Emperor <PERSON> II</a> in the <a href=\"https://wikipedia.org/wiki/Battle_of_Vlaardingen\" title=\"Battle of Vlaardingen\">Battle of Vlaardingen</a>.", "no_year_html": "Count <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Count_of_Holland\" title=\"<PERSON>, Count of Holland\"><PERSON></a> defeats an army sent by <a href=\"https://wikipedia.org/wiki/<PERSON>,_Holy_Roman_Emperor\" title=\"<PERSON> II, Holy Roman Emperor\">Emperor <PERSON> II</a> in the <a href=\"https://wikipedia.org/wiki/Battle_of_Vlaardingen\" title=\"Battle of Vlaardingen\">Battle of Vlaardingen</a>.", "links": [{"title": "<PERSON>, Count of Holland", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Count_of_Holland"}, {"title": "<PERSON>, Holy Roman Emperor", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Holy_Roman_Emperor"}, {"title": "Battle of Vlaardingen", "link": "https://wikipedia.org/wiki/Battle_of_Vlaardingen"}]}, {"year": "1030", "text": "Ladejarl-<PERSON><PERSON><PERSON> succession wars: Battle of Stiklestad: King <PERSON> fights and dies trying to regain his Norwegian throne from the Danes.", "html": "1030 - <a href=\"https://wikipedia.org/wiki/Earl<PERSON>_of_Lade\" title=\"Earls of Lade\"><PERSON><PERSON><PERSON><PERSON></a>-<a href=\"https://wikipedia.org/wiki/Hereditary_Kingdom_of_Norway\" class=\"mw-redirect\" title=\"Hereditary Kingdom of Norway\"><PERSON><PERSON><PERSON></a> succession wars: <a href=\"https://wikipedia.org/wiki/Battle_of_Stiklestad\" title=\"Battle of Stiklestad\">Battle of Stiklestad</a>: <a href=\"https://wikipedia.org/wiki/<PERSON>_II_of_Norway\" class=\"mw-redirect\" title=\"<PERSON> II of Norway\">King <PERSON> II</a> fights and dies trying to regain his <a href=\"https://wikipedia.org/wiki/List_of_Norwegian_monarchs\" title=\"List of Norwegian monarchs\">Norwegian throne</a> from the <a href=\"https://wikipedia.org/wiki/Danes\" title=\"Dane<PERSON>\">Dane<PERSON></a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Earls_of_Lade\" title=\"Earls of Lade\"><PERSON><PERSON><PERSON><PERSON></a>-<a href=\"https://wikipedia.org/wiki/Hereditary_Kingdom_of_Norway\" class=\"mw-redirect\" title=\"Hereditary Kingdom of Norway\"><PERSON><PERSON><PERSON></a> succession wars: <a href=\"https://wikipedia.org/wiki/Battle_of_Stiklestad\" title=\"Battle of Stiklestad\">Battle of Stiklestad</a>: <a href=\"https://wikipedia.org/wiki/<PERSON>_II_of_Norway\" class=\"mw-redirect\" title=\"<PERSON> II of Norway\">King <PERSON> II</a> fights and dies trying to regain his <a href=\"https://wikipedia.org/wiki/List_of_Norwegian_monarchs\" title=\"List of Norwegian monarchs\">Norwegian throne</a> from the <a href=\"https://wikipedia.org/wiki/Danes\" title=\"Dane<PERSON>\">Dane<PERSON></a>.", "links": [{"title": "Earls of Lade", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_of_Lade"}, {"title": "Hereditary Kingdom of Norway", "link": "https://wikipedia.org/wiki/Hereditary_Kingdom_of_Norway"}, {"title": "Battle of Stiklestad", "link": "https://wikipedia.org/wiki/Battle_of_Stiklestad"}, {"title": "<PERSON> of Norway", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Norway"}, {"title": "List of Norwegian monarchs", "link": "https://wikipedia.org/wiki/List_of_Norwegian_monarchs"}, {"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Danes"}]}, {"year": "1148", "text": "The Siege of Damascus ends in a decisive crusader defeat and leads to the disintegration of the Second Crusade.", "html": "1148 - The <a href=\"https://wikipedia.org/wiki/Siege_of_Damascus_(1148)\" title=\"Siege of Damascus (1148)\">Siege of Damascus</a> ends in a decisive crusader defeat and leads to the disintegration of the <a href=\"https://wikipedia.org/wiki/Second_Crusade\" title=\"Second Crusade\">Second Crusade</a>.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Siege_of_Damascus_(1148)\" title=\"Siege of Damascus (1148)\">Siege of Damascus</a> ends in a decisive crusader defeat and leads to the disintegration of the <a href=\"https://wikipedia.org/wiki/Second_Crusade\" title=\"Second Crusade\">Second Crusade</a>.", "links": [{"title": "Siege of Damascus (1148)", "link": "https://wikipedia.org/wiki/Siege_of_Damascus_(1148)"}, {"title": "Second Crusade", "link": "https://wikipedia.org/wiki/Second_Crusade"}]}, {"year": "1565", "text": "The widowed <PERSON>, Queen of Scots marries <PERSON>, Lord Dar<PERSON>, Duke of Albany, at Holyrood Palace, Edinburgh, Scotland, in a Catholic ceremony.", "html": "1565 - The widowed <a href=\"https://wikipedia.org/wiki/<PERSON>,_Queen_of_Scots\" title=\"<PERSON>, Queen of Scots\"><PERSON>, Queen of Scots</a> marries <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Lord_<PERSON><PERSON>\" title=\"<PERSON>, Lord Dar<PERSON>ley\"><PERSON>, Lord Darnley</a>, <a href=\"https://wikipedia.org/wiki/Duke_of_Albany\" title=\"Duke of Albany\">Duke of Albany</a>, at <a href=\"https://wikipedia.org/wiki/Holyrood_Palace\" title=\"Holyrood Palace\">Holyrood Palace, Edinburgh</a>, Scotland, in a <a href=\"https://wikipedia.org/wiki/Wedding_of_<PERSON>,_Queen_of_<PERSON>,_and_<PERSON>,_Lord_<PERSON>\" title=\"<PERSON> of <PERSON>, Queen of <PERSON>, and <PERSON>, Lord <PERSON>\">Catholic ceremony</a>.", "no_year_html": "The widowed <a href=\"https://wikipedia.org/wiki/<PERSON>,_Queen_of_Scots\" title=\"<PERSON>, Queen of Scots\"><PERSON>, Queen of Scots</a> marries <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Lord_<PERSON>\" title=\"<PERSON>, Lord Dar<PERSON>ley\"><PERSON>, Lord Darnley</a>, <a href=\"https://wikipedia.org/wiki/Duke_of_Albany\" title=\"Duke of Albany\">Duke of Albany</a>, at <a href=\"https://wikipedia.org/wiki/Holyrood_Palace\" title=\"Holyrood Palace\">Holyrood Palace, Edinburgh</a>, Scotland, in a <a href=\"https://wikipedia.org/wiki/Wedding_of_<PERSON>,_Queen_of_<PERSON>,_and_<PERSON>,_Lord_<PERSON>\" title=\"<PERSON> of <PERSON>, Queen of <PERSON>, and <PERSON>, Lord <PERSON>ley\">Catholic ceremony</a>.", "links": [{"title": "<PERSON>, Queen of Scots", "link": "https://wikipedia.org/wiki/<PERSON>,_Queen_of_Scots"}, {"title": "<PERSON>, Lord Dar<PERSON>ley", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Lord_<PERSON>"}, {"title": "<PERSON> of Albany", "link": "https://wikipedia.org/wiki/Duke_of_Albany"}, {"title": "Holyrood Palace", "link": "https://wikipedia.org/wiki/Holyrood_Palace"}, {"title": "<PERSON> of <PERSON>, Queen of Scots, and <PERSON>, Lord Dar<PERSON>ley", "link": "https://wikipedia.org/wiki/Wedding_of_<PERSON>,_Queen_of_Scots,_and_<PERSON>,_Lord_<PERSON><PERSON>"}]}, {"year": "1567", "text": "The infant <PERSON> is crowned King of Scotland at Stirling.", "html": "1567 - The infant <a href=\"https://wikipedia.org/wiki/<PERSON>_VI_and_<PERSON>\" title=\"<PERSON> VI and I\"><PERSON> VI</a> is <a href=\"https://wikipedia.org/wiki/Coronation_of_<PERSON>_VI_of_Scotland\" class=\"mw-redirect\" title=\"<PERSON> of James VI of Scotland\">crowned King of Scotland</a> at <a href=\"https://wikipedia.org/wiki/Stirling\" title=\"Stirling\">Stirling</a>.", "no_year_html": "The infant <a href=\"https://wikipedia.org/wiki/<PERSON>_VI_and_<PERSON>\" title=\"<PERSON> VI and I\"><PERSON> VI</a> is <a href=\"https://wikipedia.org/wiki/Coronation_of_<PERSON>_VI_of_Scotland\" class=\"mw-redirect\" title=\"<PERSON> of James VI of Scotland\">crowned King of Scotland</a> at <a href=\"https://wikipedia.org/wiki/Stirling\" title=\"Stirling\">Stirling</a>.", "links": [{"title": "James <PERSON> and I", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_and_I"}, {"title": "Coronation of <PERSON> of Scotland", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>_of_Scotland"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Stirling"}]}, {"year": "1588", "text": "Anglo-Spanish War: Battle of Gravelines: English naval forces under the command of Lord <PERSON> and Sir <PERSON> defeat the Spanish Armada off the coast of Gravelines, France.", "html": "1588 - <a href=\"https://wikipedia.org/wiki/Anglo-Spanish_War_(1585%E2%80%931604)\" title=\"Anglo-Spanish War (1585-1604)\">Anglo-Spanish War</a>: <a href=\"https://wikipedia.org/wiki/Spanish_Armada#Battle_of_Gravelines\" title=\"Spanish Armada\">Battle of Gravelines</a>: English <a href=\"https://wikipedia.org/wiki/Royal_Navy\" title=\"Royal Navy\">naval</a> forces under the command of <a href=\"https://wikipedia.org/wiki/<PERSON>,_1st_Earl_of_Nottingham\" title=\"<PERSON>, 1st Earl <PERSON> Nottingham\">Lord <PERSON></a> and Sir <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> defeat the <a href=\"https://wikipedia.org/wiki/Spanish_Armada\" title=\"Spanish Armada\">Spanish Armada</a> off the coast of <a href=\"https://wikipedia.org/wiki/Gravelines\" title=\"Gravelines\">Gravelines</a>, France.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Anglo-Spanish_War_(1585%E2%80%931604)\" title=\"Anglo-Spanish War (1585-1604)\">Anglo-Spanish War</a>: <a href=\"https://wikipedia.org/wiki/Spanish_Armada#Battle_of_Gravelines\" title=\"Spanish Armada\">Battle of Gravelines</a>: English <a href=\"https://wikipedia.org/wiki/Royal_Navy\" title=\"Royal Navy\">naval</a> forces under the command of <a href=\"https://wikipedia.org/wiki/<PERSON>,_1st_Earl_of_Nottingham\" title=\"<PERSON>, 1st Earl of Nottingham\">Lord <PERSON></a> and Sir <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> defeat the <a href=\"https://wikipedia.org/wiki/Spanish_Armada\" title=\"Spanish Armada\">Spanish Armada</a> off the coast of <a href=\"https://wikipedia.org/wiki/Gravelines\" title=\"Gravelines\">Gravelines</a>, France.", "links": [{"title": "Anglo-Spanish War (1585-1604)", "link": "https://wikipedia.org/wiki/Anglo-Spanish_War_(1585%E2%80%931604)"}, {"title": "Spanish Armada", "link": "https://wikipedia.org/wiki/Spanish_Armada#Battle_of_Gravelines"}, {"title": "Royal Navy", "link": "https://wikipedia.org/wiki/Royal_Navy"}, {"title": "<PERSON>, 1st Earl of Nottingham", "link": "https://wikipedia.org/wiki/<PERSON>,_1st_Earl_of_Nottingham"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Spanish Armada", "link": "https://wikipedia.org/wiki/Spanish_Armada"}, {"title": "Gravelines", "link": "https://wikipedia.org/wiki/Gravelines"}]}, {"year": "1693", "text": "War of the Grand Alliance: Battle of Landen: France wins a victory over Allied forces in the Netherlands.", "html": "1693 - <a href=\"https://wikipedia.org/wiki/Nine_Years%27_War\" title=\"Nine Years' War\">War of the Grand Alliance</a>: <a href=\"https://wikipedia.org/wiki/Battle_of_Landen\" title=\"Battle of Landen\">Battle of Landen</a>: France wins a victory over Allied forces in the Netherlands.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Nine_Years%27_War\" title=\"Nine Years' War\">War of the Grand Alliance</a>: <a href=\"https://wikipedia.org/wiki/Battle_of_Landen\" title=\"Battle of Landen\">Battle of Landen</a>: France wins a victory over Allied forces in the Netherlands.", "links": [{"title": "Nine Years' War", "link": "https://wikipedia.org/wiki/Nine_Years%27_War"}, {"title": "Battle of Landen", "link": "https://wikipedia.org/wiki/Battle_of_Landen"}]}, {"year": "1775", "text": "Founding of the U.S. Army Judge Advocate General's Corps: General <PERSON> appoints <PERSON> as Judge Advocate of the Continental Army.", "html": "1775 - Founding of the <a href=\"https://wikipedia.org/wiki/Judge_Advocate_General%27s_Corps,_United_States_Army\" class=\"mw-redirect\" title=\"Judge Advocate General's Corps, United States Army\">U.S. Army Judge Advocate General's Corps</a>: General <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"George <PERSON>\"><PERSON></a> appoints <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> as Judge Advocate of the <a href=\"https://wikipedia.org/wiki/Continental_Army\" title=\"Continental Army\">Continental Army</a>.", "no_year_html": "Founding of the <a href=\"https://wikipedia.org/wiki/Judge_Advocate_General%27s_Corps,_United_States_Army\" class=\"mw-redirect\" title=\"Judge Advocate General's Corps, United States Army\">U.S. Army Judge Advocate General's Corps</a>: General <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"George <PERSON>\"><PERSON></a> appoints <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> as Judge Advocate of the <a href=\"https://wikipedia.org/wiki/Continental_Army\" title=\"Continental Army\">Continental Army</a>.", "links": [{"title": "Judge Advocate General's Corps, United States Army", "link": "https://wikipedia.org/wiki/Judge_Advocate_General%27s_Corps,_United_States_Army"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/George_Washington"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Continental Army", "link": "https://wikipedia.org/wiki/Continental_Army"}]}, {"year": "1818", "text": "French physicist <PERSON><PERSON> submits his prizewinning \"Memoir on the Diffraction of Light\", precisely accounting for the limited extent to which light spreads into shadows, and thereby demolishing the oldest objection to the wave theory of light.", "html": "1818 - French physicist <a href=\"https://wikipedia.org/wiki/August<PERSON>-<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\">August<PERSON></a> submits his prizewinning \"Memoir on the Diffraction of Light\", precisely accounting for the limited extent to which light spreads into shadows, and thereby demolishing the oldest objection to the wave theory of light.", "no_year_html": "French physicist <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>-<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\">August<PERSON></a> submits his prizewinning \"Memoir on the Diffraction of Light\", precisely accounting for the limited extent to which light spreads into shadows, and thereby demolishing the oldest objection to the wave theory of light.", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>-<PERSON>_<PERSON>"}]}, {"year": "1836", "text": "Inauguration of the Arc de Triomphe in Paris, France.", "html": "1836 - Inauguration of the <a href=\"https://wikipedia.org/wiki/Arc_de_Triomphe\" title=\"Arc de Triomphe\">Arc de Triomphe</a> in Paris, France.", "no_year_html": "Inauguration of the <a href=\"https://wikipedia.org/wiki/Arc_de_Triomphe\" title=\"Arc de Triomphe\">Arc de Triomphe</a> in Paris, France.", "links": [{"title": "Arc de Triomphe", "link": "https://wikipedia.org/wiki/Arc_de_Triomphe"}]}, {"year": "1848", "text": "Great Famine of Ireland: Tipperary Revolt: In County Tipperary, Ireland, then in the United Kingdom, an unsuccessful nationalist revolt against British rule is put down by police.", "html": "1848 - <a href=\"https://wikipedia.org/wiki/Great_Famine_(Ireland)\" title=\"Great Famine (Ireland)\">Great Famine of Ireland</a>: <a href=\"https://wikipedia.org/wiki/Young_Irelander_Rebellion_of_1848\" class=\"mw-redirect\" title=\"Young Irelander Rebellion of 1848\">Tipperary Revolt</a>: In <a href=\"https://wikipedia.org/wiki/County_Tipperary\" title=\"County Tipperary\">County Tipperary</a>, Ireland, then in the United Kingdom, an unsuccessful <a href=\"https://wikipedia.org/wiki/Irish_nationalism\" title=\"Irish nationalism\">nationalist</a> revolt against British rule is put down by police.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Great_Famine_(Ireland)\" title=\"Great Famine (Ireland)\">Great Famine of Ireland</a>: <a href=\"https://wikipedia.org/wiki/Young_Irelander_Rebellion_of_1848\" class=\"mw-redirect\" title=\"Young Irelander Rebellion of 1848\">Tipperary Revolt</a>: In <a href=\"https://wikipedia.org/wiki/County_Tipperary\" title=\"County Tipperary\">County Tipperary</a>, Ireland, then in the United Kingdom, an unsuccessful <a href=\"https://wikipedia.org/wiki/Irish_nationalism\" title=\"Irish nationalism\">nationalist</a> revolt against British rule is put down by police.", "links": [{"title": "Great Famine (Ireland)", "link": "https://wikipedia.org/wiki/Great_Famine_(Ireland)"}, {"title": "Young Irelander Rebellion of 1848", "link": "https://wikipedia.org/wiki/Young_Irelander_Rebellion_of_1848"}, {"title": "County Tipperary", "link": "https://wikipedia.org/wiki/County_Tipperary"}, {"title": "Irish nationalism", "link": "https://wikipedia.org/wiki/Irish_nationalism"}]}, {"year": "1851", "text": "<PERSON><PERSON><PERSON> discovers asteroid 15 E<PERSON>mia.", "html": "1851 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a> discovers <a href=\"https://wikipedia.org/wiki/Asteroid\" title=\"Asteroid\">asteroid</a> <a href=\"https://wikipedia.org/wiki/15_Eunomia\" title=\"15 Eunomia\">15 Eunomia</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a> discovers <a href=\"https://wikipedia.org/wiki/Asteroid\" title=\"Asteroid\">asteroid</a> <a href=\"https://wikipedia.org/wiki/15_Eunomia\" title=\"15 Eunomia\">15 Eunomia</a>.", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>"}, {"title": "Asteroid", "link": "https://wikipedia.org/wiki/Asteroid"}, {"title": "15 Eunomia", "link": "https://wikipedia.org/wiki/15_Eunomia"}]}, {"year": "1858", "text": "United States and Japan sign the Harris Treaty.", "html": "1858 - United States and Japan sign the <a href=\"https://wikipedia.org/wiki/Treaty_of_Amity_and_Commerce_(United_States%E2%80%93Japan)\" title=\"Treaty of Amity and Commerce (United States-Japan)\">Harris Treaty</a>.", "no_year_html": "United States and Japan sign the <a href=\"https://wikipedia.org/wiki/Treaty_of_Amity_and_Commerce_(United_States%E2%80%93Japan)\" title=\"Treaty of Amity and Commerce (United States-Japan)\">Harris Treaty</a>.", "links": [{"title": "Treaty of Amity and Commerce (United States-Japan)", "link": "https://wikipedia.org/wiki/Treaty_of_Amity_and_Commerce_(United_States%E2%80%93Japan)"}]}, {"year": "1862", "text": "American Civil War: Confederate spy <PERSON> is arrested by Union troops and detained at the Old Capitol Prison in Washington, D.C.", "html": "1862 - <a href=\"https://wikipedia.org/wiki/American_Civil_War\" title=\"American Civil War\">American Civil War</a>: <a href=\"https://wikipedia.org/wiki/Confederate_States_of_America\" title=\"Confederate States of America\">Confederate</a> spy <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> is arrested by <a href=\"https://wikipedia.org/wiki/Union_(American_Civil_War)\" title=\"Union (American Civil War)\">Union</a> troops and detained at the <a href=\"https://wikipedia.org/wiki/Old_Capitol_Prison\" class=\"mw-redirect\" title=\"Old Capitol Prison\">Old Capitol Prison</a> in Washington, D.C.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/American_Civil_War\" title=\"American Civil War\">American Civil War</a>: <a href=\"https://wikipedia.org/wiki/Confederate_States_of_America\" title=\"Confederate States of America\">Confederate</a> spy <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> is arrested by <a href=\"https://wikipedia.org/wiki/Union_(American_Civil_War)\" title=\"Union (American Civil War)\">Union</a> troops and detained at the <a href=\"https://wikipedia.org/wiki/Old_Capitol_Prison\" class=\"mw-redirect\" title=\"Old Capitol Prison\">Old Capitol Prison</a> in Washington, D.C.", "links": [{"title": "American Civil War", "link": "https://wikipedia.org/wiki/American_Civil_War"}, {"title": "Confederate States of America", "link": "https://wikipedia.org/wiki/Confederate_States_of_America"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Union (American Civil War)", "link": "https://wikipedia.org/wiki/Union_(American_Civil_War)"}, {"title": "Old Capitol Prison", "link": "https://wikipedia.org/wiki/Old_Capitol_Prison"}]}, {"year": "1871", "text": "The Connecticut Valley Railroad opens between Old Saybrook, Connecticut and Hartford, Connecticut in the United States.", "html": "1871 - The <a href=\"https://wikipedia.org/wiki/Valley_Railroad_(Connecticut)\" title=\"Valley Railroad (Connecticut)\">Connecticut Valley Railroad</a> opens between <a href=\"https://wikipedia.org/wiki/Old_Saybrook,_Connecticut\" title=\"Old Saybrook, Connecticut\">Old Saybrook, Connecticut</a> and <a href=\"https://wikipedia.org/wiki/Hartford,_Connecticut\" title=\"Hartford, Connecticut\">Hartford, Connecticut</a> in the United States.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Valley_Railroad_(Connecticut)\" title=\"Valley Railroad (Connecticut)\">Connecticut Valley Railroad</a> opens between <a href=\"https://wikipedia.org/wiki/Old_Saybrook,_Connecticut\" title=\"Old Saybrook, Connecticut\">Old Saybrook, Connecticut</a> and <a href=\"https://wikipedia.org/wiki/Hartford,_Connecticut\" title=\"Hartford, Connecticut\">Hartford, Connecticut</a> in the United States.", "links": [{"title": "Valley Railroad (Connecticut)", "link": "https://wikipedia.org/wiki/Valley_Railroad_(Connecticut)"}, {"title": "Old Saybrook, Connecticut", "link": "https://wikipedia.org/wiki/Old_Saybrook,_Connecticut"}, {"title": "Hartford, Connecticut", "link": "https://wikipedia.org/wiki/Hartford,_Connecticut"}]}, {"year": "1899", "text": "The First Hague Convention is signed.", "html": "1899 - The <a href=\"https://wikipedia.org/wiki/Hague_Conventions_of_1899_and_1907\" title=\"Hague Conventions of 1899 and 1907\">First Hague Convention</a> is signed.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Hague_Conventions_of_1899_and_1907\" title=\"Hague Conventions of 1899 and 1907\">First Hague Convention</a> is signed.", "links": [{"title": "Hague Conventions of 1899 and 1907", "link": "https://wikipedia.org/wiki/Hague_Conventions_of_1899_and_1907"}]}, {"year": "1900", "text": "In Italy, King <PERSON><PERSON> of Italy is assassinated by the anarchist <PERSON><PERSON><PERSON>. His son, <PERSON>, 31 years old, succeeds to the throne.", "html": "1900 - In Italy, King <a href=\"https://wikipedia.org/wiki/Umberto_I_of_Italy\" title=\"<PERSON>berto I of Italy\"><PERSON><PERSON> of Italy</a> is assassinated by the <a href=\"https://wikipedia.org/wiki/Anarchism\" title=\"Anarchism\">anarchist</a> <a href=\"https://wikipedia.org/wiki/Gaetano_Bresci\" title=\"Gae<PERSON>\"><PERSON><PERSON><PERSON></a>. His son, <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_of_Italy\" class=\"mw-redirect\" title=\"<PERSON> of Italy\"><PERSON></a>, 31 years old, succeeds to the throne.", "no_year_html": "In Italy, King <a href=\"https://wikipedia.org/wiki/Umberto_I_of_Italy\" title=\"<PERSON>berto I of Italy\"><PERSON><PERSON> of Italy</a> is assassinated by the <a href=\"https://wikipedia.org/wiki/Anarchism\" title=\"Anarchism\">anarchist</a> <a href=\"https://wikipedia.org/wiki/Gaetano_Bresci\" title=\"Gae<PERSON>ci\"><PERSON><PERSON><PERSON></a>. His son, <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Italy\" class=\"mw-redirect\" title=\"<PERSON> of Italy\"><PERSON></a>, 31 years old, succeeds to the throne.", "links": [{"title": "<PERSON><PERSON> of Italy", "link": "https://wikipedia.org/wiki/Umberto_I_of_Italy"}, {"title": "Anarchism", "link": "https://wikipedia.org/wiki/Anarchism"}, {"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Gae<PERSON>_Bres<PERSON>"}, {"title": "<PERSON> of Italy", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_of_Italy"}]}, {"year": "1901", "text": "Land lottery begins in Oklahoma.", "html": "1901 - Land lottery begins in <a href=\"https://wikipedia.org/wiki/Oklahoma\" title=\"Oklahoma\">Oklahoma</a>.", "no_year_html": "Land lottery begins in <a href=\"https://wikipedia.org/wiki/Oklahoma\" title=\"Oklahoma\">Oklahoma</a>.", "links": [{"title": "Oklahoma", "link": "https://wikipedia.org/wiki/Oklahoma"}]}, {"year": "1907", "text": "Sir <PERSON> sets up the Brownsea Island Scout camp in Poole Harbour on the south coast of England. The camp runs from August 1 to August 9 and is regarded as the foundation of the Scouting movement.", "html": "1907 - Sir <a href=\"https://wikipedia.org/wiki/<PERSON>,_1st_Baron_<PERSON>\" title=\"<PERSON>, 1st Baron <PERSON>\"><PERSON></a> sets up the <a href=\"https://wikipedia.org/wiki/Brownsea_Island_Scout_camp\" title=\"Brownsea Island Scout camp\">Brownsea Island Scout camp</a> in <a href=\"https://wikipedia.org/wiki/Poole_Harbour\" title=\"Poole Harbour\">Poole Harbour</a> on the south coast of England. The camp runs from August 1 to August 9 and is regarded as the foundation of the <a href=\"https://wikipedia.org/wiki/Scouting\" title=\"Scouting\">Scouting</a> movement.", "no_year_html": "Sir <a href=\"https://wikipedia.org/wiki/<PERSON>,_1st_Baron_<PERSON>\" title=\"<PERSON>, 1st Baron <PERSON>\"><PERSON></a> sets up the <a href=\"https://wikipedia.org/wiki/Brownsea_Island_Scout_camp\" title=\"Brownsea Island Scout camp\">Brownsea Island Scout camp</a> in <a href=\"https://wikipedia.org/wiki/Poole_Harbour\" title=\"Poole Harbour\">Poole Harbour</a> on the south coast of England. The camp runs from August 1 to August 9 and is regarded as the foundation of the <a href=\"https://wikipedia.org/wiki/Scouting\" title=\"Scouting\">Scouting</a> movement.", "links": [{"title": "<PERSON>, 1st Baron <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_1st_Baron_<PERSON>"}, {"title": "Brownsea Island Scout camp", "link": "https://wikipedia.org/wiki/Brownsea_Island_Scout_camp"}, {"title": "Poole Harbour", "link": "https://wikipedia.org/wiki/Poole_Harbour"}, {"title": "Scouting", "link": "https://wikipedia.org/wiki/Scouting"}]}, {"year": "1910", "text": "The two-day Slocum massacre commences in Texas, a race riot in which more than 100 African Americans are murdered.", "html": "1910 - The two-day <a href=\"https://wikipedia.org/wiki/Slocum_massacre\" title=\"Slocum massacre\">Slocum massacre</a> commences in Texas, a race riot in which more than 100 African Americans are murdered.", "no_year_html": "The two-day <a href=\"https://wikipedia.org/wiki/Slocum_massacre\" title=\"Slocum massacre\">Slocum massacre</a> commences in Texas, a race riot in which more than 100 African Americans are murdered.", "links": [{"title": "Slocum massacre", "link": "https://wikipedia.org/wiki/Slocum_massacre"}]}, {"year": "1914", "text": "The Cape Cod Canal opened.", "html": "1914 - The <a href=\"https://wikipedia.org/wiki/Cape_Cod_Canal\" title=\"Cape Cod Canal\">Cape Cod Canal</a> opened.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Cape_Cod_Canal\" title=\"Cape Cod Canal\">Cape Cod Canal</a> opened.", "links": [{"title": "Cape Cod Canal", "link": "https://wikipedia.org/wiki/Cape_Cod_Canal"}]}, {"year": "1920", "text": "Construction of the Link River Dam begins as part of the Klamath Reclamation Project.", "html": "1920 - Construction of the <a href=\"https://wikipedia.org/wiki/Link_River_Dam\" title=\"Link River Dam\">Link River Dam</a> begins as part of the <a href=\"https://wikipedia.org/wiki/Klamath_Project\" title=\"Klamath Project\">Klamath Reclamation Project</a>.", "no_year_html": "Construction of the <a href=\"https://wikipedia.org/wiki/Link_River_Dam\" title=\"Link River Dam\">Link River Dam</a> begins as part of the <a href=\"https://wikipedia.org/wiki/Klamath_Project\" title=\"Klamath Project\">Klamath Reclamation Project</a>.", "links": [{"title": "Link River Dam", "link": "https://wikipedia.org/wiki/Link_River_Dam"}, {"title": "Klamath Project", "link": "https://wikipedia.org/wiki/Klamath_Project"}]}, {"year": "1921", "text": "<PERSON> becomes leader of the National Socialist German Workers' Party.", "html": "1921 - <a href=\"https://wikipedia.org/wiki/Adolf_Hitler\" title=\"Adolf Hitler\"><PERSON></a> becomes leader of the <a href=\"https://wikipedia.org/wiki/Nazi_Party\" title=\"Nazi Party\">National Socialist German Workers' Party</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Adolf_Hitler\" title=\"Adolf Hitler\"><PERSON></a> becomes leader of the <a href=\"https://wikipedia.org/wiki/Nazi_Party\" title=\"Nazi Party\">National Socialist German Workers' Party</a>.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Nazi Party", "link": "https://wikipedia.org/wiki/Nazi_Party"}]}, {"year": "1932", "text": "Great Depression: In Washington, D.C., troops disperse the last of the \"Bonus Army\" of World War I veterans using arson, bayonets, sabers, tanks, tear gas, and vomit gas.", "html": "1932 - <a href=\"https://wikipedia.org/wiki/Great_Depression\" title=\"Great Depression\">Great Depression</a>: In Washington, D.C., troops disperse the last of the \"<a href=\"https://wikipedia.org/wiki/Bonus_Army\" title=\"Bonus Army\">Bonus Army</a>\" of World War I veterans using arson, <a href=\"https://wikipedia.org/wiki/Bayonet\" title=\"Bayonet\">bayonets</a>, <a href=\"https://wikipedia.org/wiki/Saber\" class=\"mw-redirect\" title=\"Saber\">sabers</a>, tanks, <a href=\"https://wikipedia.org/wiki/Tear_gas\" title=\"Tear gas\">tear gas</a>, and <a href=\"https://wikipedia.org/wiki/Adamsite\" title=\"Adamsite\">vomit gas</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Great_Depression\" title=\"Great Depression\">Great Depression</a>: In Washington, D.C., troops disperse the last of the \"<a href=\"https://wikipedia.org/wiki/Bonus_Army\" title=\"Bonus Army\">Bonus Army</a>\" of World War I veterans using arson, <a href=\"https://wikipedia.org/wiki/Bayonet\" title=\"Bayonet\">bayonets</a>, <a href=\"https://wikipedia.org/wiki/Saber\" class=\"mw-redirect\" title=\"Saber\">sabers</a>, tanks, <a href=\"https://wikipedia.org/wiki/Tear_gas\" title=\"Tear gas\">tear gas</a>, and <a href=\"https://wikipedia.org/wiki/Adamsite\" title=\"Adamsite\">vomit gas</a>.", "links": [{"title": "Great Depression", "link": "https://wikipedia.org/wiki/Great_Depression"}, {"title": "Bonus Army", "link": "https://wikipedia.org/wiki/Bonus_Army"}, {"title": "Bayonet", "link": "https://wikipedia.org/wiki/Bayonet"}, {"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Saber"}, {"title": "Tear gas", "link": "https://wikipedia.org/wiki/Tear_gas"}, {"title": "Adamsite", "link": "https://wikipedia.org/wiki/Adamsite"}]}, {"year": "1937", "text": "Tongzhou mutiny: In Tongzhou, China, the East Hebei Army attacks Japanese troops and civilians.", "html": "1937 - <a href=\"https://wikipedia.org/wiki/Tongzhou_mutiny\" title=\"Tongzhou mutiny\">Tongzhou mutiny</a>: In <a href=\"https://wikipedia.org/wiki/Tongzhou_District,_Beijing\" class=\"mw-redirect\" title=\"Tongzhou District, Beijing\">Tongzhou, China</a>, the <a href=\"https://wikipedia.org/wiki/East_Hebei_Army\" title=\"East Hebei Army\">East Hebei Army</a> attacks Japanese troops and civilians.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Tongzhou_mutiny\" title=\"Tongzhou mutiny\">Tongzhou mutiny</a>: In <a href=\"https://wikipedia.org/wiki/Tongzhou_District,_Beijing\" class=\"mw-redirect\" title=\"Tongzhou District, Beijing\">Tongzhou, China</a>, the <a href=\"https://wikipedia.org/wiki/East_Hebei_Army\" title=\"East Hebei Army\">East Hebei Army</a> attacks Japanese troops and civilians.", "links": [{"title": "Tongzhou mutiny", "link": "https://wikipedia.org/wiki/Tongzhou_mutiny"}, {"title": "Tongzhou District, Beijing", "link": "https://wikipedia.org/wiki/Tongzhou_District,_Beijing"}, {"title": "East Hebei Army", "link": "https://wikipedia.org/wiki/East_Hebei_Army"}]}, {"year": "1945", "text": "The BBC Light Programme radio station is launched for mainstream light entertainment and music.", "html": "1945 - The <a href=\"https://wikipedia.org/wiki/BBC_Light_Programme\" title=\"BBC Light Programme\">BBC Light Programme</a> radio station is launched for mainstream light entertainment and music.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/BBC_Light_Programme\" title=\"BBC Light Programme\">BBC Light Programme</a> radio station is launched for mainstream light entertainment and music.", "links": [{"title": "BBC Light Programme", "link": "https://wikipedia.org/wiki/BBC_Light_Programme"}]}, {"year": "1948", "text": "Olympic Games: The Games of the XIV Olympiad: After a hiatus of 12 years caused by World War II, the first Summer Olympics to be held since the 1936 Summer Olympics in Berlin, open in London.", "html": "1948 - <a href=\"https://wikipedia.org/wiki/Olympic_Games\" title=\"Olympic Games\">Olympic Games</a>: <a href=\"https://wikipedia.org/wiki/1948_Summer_Olympics\" title=\"1948 Summer Olympics\">The Games of the XIV Olympiad</a>: After a hiatus of 12 years caused by World War II, the first <a href=\"https://wikipedia.org/wiki/Summer_Olympic_Games\" title=\"Summer Olympic Games\">Summer Olympics</a> to be held since the <a href=\"https://wikipedia.org/wiki/1936_Summer_Olympics\" title=\"1936 Summer Olympics\">1936 Summer Olympics</a> in Berlin, open in London.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Olympic_Games\" title=\"Olympic Games\">Olympic Games</a>: <a href=\"https://wikipedia.org/wiki/1948_Summer_Olympics\" title=\"1948 Summer Olympics\">The Games of the XIV Olympiad</a>: After a hiatus of 12 years caused by World War II, the first <a href=\"https://wikipedia.org/wiki/Summer_Olympic_Games\" title=\"Summer Olympic Games\">Summer Olympics</a> to be held since the <a href=\"https://wikipedia.org/wiki/1936_Summer_Olympics\" title=\"1936 Summer Olympics\">1936 Summer Olympics</a> in Berlin, open in London.", "links": [{"title": "Olympic Games", "link": "https://wikipedia.org/wiki/Olympic_Games"}, {"title": "1948 Summer Olympics", "link": "https://wikipedia.org/wiki/1948_Summer_Olympics"}, {"title": "Summer Olympic Games", "link": "https://wikipedia.org/wiki/Summer_Olympic_Games"}, {"title": "1936 Summer Olympics", "link": "https://wikipedia.org/wiki/1936_Summer_Olympics"}]}, {"year": "1950", "text": "Korean War: After four days, the No Gun Ri Massacre ends when the US Army 7th Cavalry Regiment is withdrawn.", "html": "1950 - <a href=\"https://wikipedia.org/wiki/Korean_War\" title=\"Korean War\">Korean War</a>: After four days, the <a href=\"https://wikipedia.org/wiki/No_Gun_Ri_massacre\" title=\"No Gun Ri massacre\">No Gun Ri Massacre</a> ends when the US Army <a href=\"https://wikipedia.org/wiki/7th_Cavalry_Regiment\" title=\"7th Cavalry Regiment\">7th Cavalry Regiment</a> is withdrawn.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Korean_War\" title=\"Korean War\">Korean War</a>: After four days, the <a href=\"https://wikipedia.org/wiki/No_Gun_Ri_massacre\" title=\"No Gun Ri massacre\">No Gun Ri Massacre</a> ends when the US Army <a href=\"https://wikipedia.org/wiki/7th_Cavalry_Regiment\" title=\"7th Cavalry Regiment\">7th Cavalry Regiment</a> is withdrawn.", "links": [{"title": "Korean War", "link": "https://wikipedia.org/wiki/Korean_War"}, {"title": "No Gun Ri massacre", "link": "https://wikipedia.org/wiki/No_Gun_Ri_massacre"}, {"title": "7th Cavalry Regiment", "link": "https://wikipedia.org/wiki/7th_Cavalry_Regiment"}]}, {"year": "1957", "text": "The International Atomic Energy Agency is established.", "html": "1957 - The <a href=\"https://wikipedia.org/wiki/International_Atomic_Energy_Agency\" title=\"International Atomic Energy Agency\">International Atomic Energy Agency</a> is established.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/International_Atomic_Energy_Agency\" title=\"International Atomic Energy Agency\">International Atomic Energy Agency</a> is established.", "links": [{"title": "International Atomic Energy Agency", "link": "https://wikipedia.org/wiki/International_Atomic_Energy_Agency"}]}, {"year": "1957", "text": "Tonight Starring <PERSON> premieres on NBC with <PERSON> beginning the modern day talk show.", "html": "1957 - <a href=\"https://wikipedia.org/wiki/Tonight_Starring_<PERSON>_<PERSON>\" title=\"Tonight Starring Jack <PERSON>\">Tonight Starring <PERSON></a> premieres on NBC with <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> beginning the modern day talk show.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Tonight_Starring_<PERSON>_<PERSON>\" title=\"Tonight Starring Jack <PERSON>\">Tonight Starring <PERSON></a> premieres on NBC with <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> beginning the modern day talk show.", "links": [{"title": "Tonight Starring <PERSON>", "link": "https://wikipedia.org/wiki/Tonight_Starring_<PERSON>_<PERSON>"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1958", "text": "U.S. President <PERSON> signs into law the National Aeronautics and Space Act, which creates the National Aeronautics and Space Administration (NASA).", "html": "1958 - U.S. President <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> signs into law the <a href=\"https://wikipedia.org/wiki/National_Aeronautics_and_Space_Act\" title=\"National Aeronautics and Space Act\">National Aeronautics and Space Act</a>, which creates the <a href=\"https://wikipedia.org/wiki/NASA\" title=\"NASA\">National Aeronautics and Space Administration</a> (NASA).", "no_year_html": "U.S. President <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> signs into law the <a href=\"https://wikipedia.org/wiki/National_Aeronautics_and_Space_Act\" title=\"National Aeronautics and Space Act\">National Aeronautics and Space Act</a>, which creates the <a href=\"https://wikipedia.org/wiki/NASA\" title=\"NASA\">National Aeronautics and Space Administration</a> (NASA).", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "National Aeronautics and Space Act", "link": "https://wikipedia.org/wiki/National_Aeronautics_and_Space_Act"}, {"title": "NASA", "link": "https://wikipedia.org/wiki/NASA"}]}, {"year": "1959", "text": "First United States Congress elections in Hawaii as a state of the Union.", "html": "1959 - First <a href=\"https://wikipedia.org/wiki/United_States_Congress\" title=\"United States Congress\">United States Congress</a> elections in Hawaii as a state of the Union.", "no_year_html": "First <a href=\"https://wikipedia.org/wiki/United_States_Congress\" title=\"United States Congress\">United States Congress</a> elections in Hawaii as a state of the Union.", "links": [{"title": "United States Congress", "link": "https://wikipedia.org/wiki/United_States_Congress"}]}, {"year": "1965", "text": "Vietnam War: The first 4,000 101st Airborne Division paratroopers arrive in Vietnam, landing at Cam Ranh Bay.", "html": "1965 - <a href=\"https://wikipedia.org/wiki/Vietnam_War\" title=\"Vietnam War\">Vietnam War</a>: The first 4,000 <a href=\"https://wikipedia.org/wiki/101st_Airborne_Division\" title=\"101st Airborne Division\">101st Airborne Division</a> paratroopers arrive in <a href=\"https://wikipedia.org/wiki/Vietnam\" title=\"Vietnam\">Vietnam</a>, landing at <a href=\"https://wikipedia.org/wiki/Cam_Ranh_Bay\" title=\"Cam Ranh Bay\">Cam Ranh Bay</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Vietnam_War\" title=\"Vietnam War\">Vietnam War</a>: The first 4,000 <a href=\"https://wikipedia.org/wiki/101st_Airborne_Division\" title=\"101st Airborne Division\">101st Airborne Division</a> paratroopers arrive in <a href=\"https://wikipedia.org/wiki/Vietnam\" title=\"Vietnam\">Vietnam</a>, landing at <a href=\"https://wikipedia.org/wiki/Cam_Ranh_Bay\" title=\"Cam Ranh Bay\">Cam Ranh Bay</a>.", "links": [{"title": "Vietnam War", "link": "https://wikipedia.org/wiki/Vietnam_War"}, {"title": "101st Airborne Division", "link": "https://wikipedia.org/wiki/101st_Airborne_Division"}, {"title": "Vietnam", "link": "https://wikipedia.org/wiki/Vietnam"}, {"title": "Cam Ranh Bay", "link": "https://wikipedia.org/wiki/Cam_Ranh_Bay"}]}, {"year": "1967", "text": "Vietnam War: Off the coast of North Vietnam the USS Forrestal catches on fire in the worst U.S. naval disaster since World War II, killing 134.", "html": "1967 - Vietnam War: Off the coast of <a href=\"https://wikipedia.org/wiki/North_Vietnam\" title=\"North Vietnam\">North Vietnam</a> the <a href=\"https://wikipedia.org/wiki/USS_Forrestal\" title=\"USS Forrestal\">USS <i><PERSON>al</i></a> catches on <a href=\"https://wikipedia.org/wiki/1967_USS_Forrestal_fire\" title=\"1967 USS Forrestal fire\">fire</a> in the worst U.S. naval disaster since World War II, killing 134.", "no_year_html": "Vietnam War: Off the coast of <a href=\"https://wikipedia.org/wiki/North_Vietnam\" title=\"North Vietnam\">North Vietnam</a> the <a href=\"https://wikipedia.org/wiki/USS_Forrestal\" title=\"USS Forrestal\">USS <i><PERSON>al</i></a> catches on <a href=\"https://wikipedia.org/wiki/1967_USS_Forrestal_fire\" title=\"1967 USS Forrestal fire\">fire</a> in the worst U.S. naval disaster since World War II, killing 134.", "links": [{"title": "North Vietnam", "link": "https://wikipedia.org/wiki/North_Vietnam"}, {"title": "USS Forrestal", "link": "https://wikipedia.org/wiki/USS_Forrestal"}, {"title": "1967 USS Forrestal fire", "link": "https://wikipedia.org/wiki/1967_USS_Forrestal_fire"}]}, {"year": "1967", "text": "During the fourth day of celebrating its 400th anniversary, the city of Caracas, Venezuela is shaken by an earthquake, leaving approximately 500 dead.", "html": "1967 - During the fourth day of celebrating its 400th anniversary, the city of <a href=\"https://wikipedia.org/wiki/Caracas\" title=\"Caracas\">Caracas, Venezuela</a> is shaken by <a href=\"https://wikipedia.org/wiki/1967_Caracas_earthquake\" title=\"1967 Caracas earthquake\">an earthquake</a>, leaving approximately 500 dead.", "no_year_html": "During the fourth day of celebrating its 400th anniversary, the city of <a href=\"https://wikipedia.org/wiki/Caracas\" title=\"Caracas\">Caracas, Venezuela</a> is shaken by <a href=\"https://wikipedia.org/wiki/1967_Caracas_earthquake\" title=\"1967 Caracas earthquake\">an earthquake</a>, leaving approximately 500 dead.", "links": [{"title": "Caracas", "link": "https://wikipedia.org/wiki/Caracas"}, {"title": "1967 Caracas earthquake", "link": "https://wikipedia.org/wiki/1967_Caracas_earthquake"}]}, {"year": "1973", "text": "Greeks vote to abolish the monarchy, beginning the first period of the Metapolitefsi.", "html": "1973 - <a href=\"https://wikipedia.org/wiki/1973_Greek_republic_referendum\" title=\"1973 Greek republic referendum\">Greeks vote to abolish the monarchy</a>, beginning the first period of the <a href=\"https://wikipedia.org/wiki/Metapolitefsi\" title=\"Metapolitefsi\">Metapolitefsi</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/1973_Greek_republic_referendum\" title=\"1973 Greek republic referendum\">Greeks vote to abolish the monarchy</a>, beginning the first period of the <a href=\"https://wikipedia.org/wiki/Metapolitefsi\" title=\"Metapolitefsi\">Metapolitefsi</a>.", "links": [{"title": "1973 Greek republic referendum", "link": "https://wikipedia.org/wiki/1973_Greek_republic_referendum"}, {"title": "Metapolitefsi", "link": "https://wikipedia.org/wiki/Metapolitefsi"}]}, {"year": "1973", "text": "Driver <PERSON> is killed during the Dutch Grand Prix, after a suspected tire failure causes his car to pitch into the barriers at high speed.", "html": "1973 - Driver <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> is killed during the <a href=\"https://wikipedia.org/wiki/1973_Dutch_Grand_Prix\" title=\"1973 Dutch Grand Prix\">Dutch Grand Prix</a>, after a suspected tire failure causes his car to pitch into the barriers at high speed.", "no_year_html": "Driver <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> is killed during the <a href=\"https://wikipedia.org/wiki/1973_Dutch_Grand_Prix\" title=\"1973 Dutch Grand Prix\">Dutch Grand Prix</a>, after a suspected tire failure causes his car to pitch into the barriers at high speed.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "1973 Dutch Grand Prix", "link": "https://wikipedia.org/wiki/1973_Dutch_Grand_Prix"}]}, {"year": "1976", "text": "In New York City, <PERSON> (a.k.a. the \"Son of Sam\") kills one person and seriously wounds another in the first of a series of attacks.", "html": "1976 - In New York City, <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> (a.k.a. the \"Son of Sam\") kills one person and seriously wounds another in the first of a series of attacks.", "no_year_html": "In New York City, <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> (a.k.a. the \"Son of Sam\") kills one person and seriously wounds another in the first of a series of attacks.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1980", "text": "Iran adopts a new \"holy\" flag after the Islamic Revolution.", "html": "1980 - Iran adopts a new <a href=\"https://wikipedia.org/wiki/Flag_of_Iran\" title=\"Flag of Iran\">\"holy\" flag</a> after the <a href=\"https://wikipedia.org/wiki/Iranian_Revolution\" class=\"mw-redirect\" title=\"Iranian Revolution\">Islamic Revolution</a>.", "no_year_html": "Iran adopts a new <a href=\"https://wikipedia.org/wiki/Flag_of_Iran\" title=\"Flag of Iran\">\"holy\" flag</a> after the <a href=\"https://wikipedia.org/wiki/Iranian_Revolution\" class=\"mw-redirect\" title=\"Iranian Revolution\">Islamic Revolution</a>.", "links": [{"title": "Flag of Iran", "link": "https://wikipedia.org/wiki/Flag_of_Iran"}, {"title": "Iranian Revolution", "link": "https://wikipedia.org/wiki/Iranian_Revolution"}]}, {"year": "1981", "text": "A worldwide television audience of around 750 million people watch the wedding of <PERSON>, Prince of Wales, and Lady <PERSON> at St Paul's Cathedral in London.", "html": "1981 - A worldwide television audience of around 750 million people watch the <a href=\"https://wikipedia.org/wiki/Wedding_of_Prince_<PERSON>_and_Lady_<PERSON>_<PERSON>\" title=\"Wedding of Prince <PERSON> and Lady <PERSON>\">wedding of <PERSON>, Prince of Wales, and Lady <PERSON></a> at <a href=\"https://wikipedia.org/wiki/St_Paul%27s_Cathedral\" title=\"St Paul's Cathedral\">St Paul's Cathedral</a> in <a href=\"https://wikipedia.org/wiki/London\" title=\"London\">London</a>.", "no_year_html": "A worldwide television audience of around 750 million people watch the <a href=\"https://wikipedia.org/wiki/Wedding_of_Prince_<PERSON>_and_Lady_<PERSON>_<PERSON>\" title=\"Wedding of Prince <PERSON> and Lady <PERSON>\">wedding of <PERSON>, Prince of Wales, and Lady <PERSON></a> at <a href=\"https://wikipedia.org/wiki/St_Paul%27s_Cathedral\" title=\"St Paul's Cathedral\">St Paul's Cathedral</a> in <a href=\"https://wikipedia.org/wiki/London\" title=\"London\">London</a>.", "links": [{"title": "Wedding of Prince <PERSON> and Lady <PERSON>", "link": "https://wikipedia.org/wiki/Wedding_of_Prince_<PERSON>_and_Lady_<PERSON>_<PERSON>"}, {"title": "St Paul's Cathedral", "link": "https://wikipedia.org/wiki/St_Paul%27s_Cathedral"}, {"title": "London", "link": "https://wikipedia.org/wiki/London"}]}, {"year": "1981", "text": "After impeachment on June 21, <PERSON><PERSON><PERSON><PERSON> flees with <PERSON><PERSON> to Paris, in an Iranian Air Force Boeing 707, piloted by Colonel <PERSON><PERSON><PERSON>, to form the National Council of Resistance of Iran.", "html": "1981 - After impeachment on June 21, <a href=\"https://wikipedia.org/wiki/Abol<PERSON><PERSON>_Ban<PERSON>\" title=\"<PERSON>bol<PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a> flees with <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a> to <a href=\"https://wikipedia.org/wiki/Paris\" title=\"Paris\">Paris</a>, in an <a href=\"https://wikipedia.org/wiki/Islamic_Republic_of_Iran_Air_Force\" title=\"Islamic Republic of Iran Air Force\">Iranian Air Force</a> <a href=\"https://wikipedia.org/wiki/Boeing_707\" title=\"Boeing 707\">Boeing 707</a>, piloted by Colonel <PERSON><PERSON><PERSON>, to form the <a href=\"https://wikipedia.org/wiki/National_Council_of_Resistance_of_Iran\" title=\"National Council of Resistance of Iran\">National Council of Resistance of Iran</a>.", "no_year_html": "After impeachment on June 21, <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_Ban<PERSON>\" title=\"<PERSON>bol<PERSON><PERSON> Ban<PERSON>\"><PERSON><PERSON><PERSON><PERSON></a> flees with <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a> to <a href=\"https://wikipedia.org/wiki/Paris\" title=\"Paris\">Paris</a>, in an <a href=\"https://wikipedia.org/wiki/Islamic_Republic_of_Iran_Air_Force\" title=\"Islamic Republic of Iran Air Force\">Iranian Air Force</a> <a href=\"https://wikipedia.org/wiki/Boeing_707\" title=\"Boeing 707\">Boeing 707</a>, piloted by Colonel <PERSON><PERSON><PERSON>, to form the <a href=\"https://wikipedia.org/wiki/National_Council_of_Resistance_of_Iran\" title=\"National Council of Resistance of Iran\">National Council of Resistance of Iran</a>.", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>"}, {"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}, {"title": "Paris", "link": "https://wikipedia.org/wiki/Paris"}, {"title": "Islamic Republic of Iran Air Force", "link": "https://wikipedia.org/wiki/Islamic_Republic_of_Iran_Air_Force"}, {"title": "Boeing 707", "link": "https://wikipedia.org/wiki/Boeing_707"}, {"title": "National Council of Resistance of Iran", "link": "https://wikipedia.org/wiki/National_Council_of_Resistance_of_Iran"}]}, {"year": "1987", "text": "British Prime Minister <PERSON> and President of France <PERSON> sign the agreement to build a tunnel under the English Channel (Channel Tunnel).", "html": "1987 - British Prime Minister <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> and President of France <a href=\"https://wikipedia.org/wiki/Fran%C3%<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> sign the agreement to build a tunnel under the <a href=\"https://wikipedia.org/wiki/English_Channel\" title=\"English Channel\">English Channel</a> (<a href=\"https://wikipedia.org/wiki/Channel_Tunnel\" title=\"Channel Tunnel\">Channel Tunnel</a>).", "no_year_html": "British Prime Minister <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> and President of France <a href=\"https://wikipedia.org/wiki/Fran%C3%<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> sign the agreement to build a tunnel under the <a href=\"https://wikipedia.org/wiki/English_Channel\" title=\"English Channel\">English Channel</a> (<a href=\"https://wikipedia.org/wiki/Channel_Tunnel\" title=\"Channel Tunnel\">Channel Tunnel</a>).", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Fran%C3%A<PERSON><PERSON>_<PERSON>"}, {"title": "English Channel", "link": "https://wikipedia.org/wiki/English_Channel"}, {"title": "Channel Tunnel", "link": "https://wikipedia.org/wiki/Channel_Tunnel"}]}, {"year": "1987", "text": "Prime Minister of India <PERSON><PERSON> and President of Sri Lanka <PERSON><PERSON> <PERSON><PERSON> sign the Indo-Sri Lanka Accord on ethnic issues.", "html": "1987 - Prime Minister of India <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a> and President of Sri Lanka <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> <PERSON><PERSON>\"><PERSON><PERSON> <PERSON><PERSON></a> sign the <a href=\"https://wikipedia.org/wiki/Indo-Sri_Lanka_Accord\" title=\"Indo-Sri Lanka Accord\">Indo-Sri Lanka Accord</a> on ethnic issues.", "no_year_html": "Prime Minister of India <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a> and President of Sri Lanka <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> <PERSON><PERSON>\"><PERSON><PERSON> <PERSON><PERSON></a> sign the <a href=\"https://wikipedia.org/wiki/Indo-Sri_Lanka_Accord\" title=\"Indo-Sri Lanka Accord\">Indo-Sri Lanka Accord</a> on ethnic issues.", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}, {"title": "<PERSON><PERSON> <PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON><PERSON><PERSON>"}, {"title": "Indo-Sri Lanka Accord", "link": "https://wikipedia.org/wiki/Indo-Sri_Lanka_Accord"}]}, {"year": "1993", "text": "The Supreme Court of Israel acquits alleged Nazi death camp guard <PERSON> of all charges and he is set free.", "html": "1993 - The <a href=\"https://wikipedia.org/wiki/Supreme_Court_of_Israel\" title=\"Supreme Court of Israel\">Supreme Court of Israel</a> acquits alleged <a href=\"https://wikipedia.org/wiki/Nazism\" title=\"Nazism\">Nazi</a> death camp guard <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> of all charges and he is set free.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Supreme_Court_of_Israel\" title=\"Supreme Court of Israel\">Supreme Court of Israel</a> acquits alleged <a href=\"https://wikipedia.org/wiki/Nazism\" title=\"Nazism\">Nazi</a> death camp guard <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> of all charges and he is set free.", "links": [{"title": "Supreme Court of Israel", "link": "https://wikipedia.org/wiki/Supreme_Court_of_Israel"}, {"title": "Nazism", "link": "https://wikipedia.org/wiki/Nazism"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1996", "text": "The child protection portion of the Communications Decency Act is struck down by a U.S. federal court as too broad.", "html": "1996 - The child protection portion of the <a href=\"https://wikipedia.org/wiki/Communications_Decency_Act\" title=\"Communications Decency Act\">Communications Decency Act</a> is struck down by a <a href=\"https://wikipedia.org/wiki/United_States_federal_courts\" class=\"mw-redirect\" title=\"United States federal courts\">U.S. federal court</a> as too broad.", "no_year_html": "The child protection portion of the <a href=\"https://wikipedia.org/wiki/Communications_Decency_Act\" title=\"Communications Decency Act\">Communications Decency Act</a> is struck down by a <a href=\"https://wikipedia.org/wiki/United_States_federal_courts\" class=\"mw-redirect\" title=\"United States federal courts\">U.S. federal court</a> as too broad.", "links": [{"title": "Communications Decency Act", "link": "https://wikipedia.org/wiki/Communications_Decency_Act"}, {"title": "United States federal courts", "link": "https://wikipedia.org/wiki/United_States_federal_courts"}]}, {"year": "2005", "text": "Astronomers announce their discovery of the dwarf planet Eris.", "html": "2005 - Astronomers announce their discovery of the dwarf planet <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_(dwarf_planet)\" title=\"Eris (dwarf planet)\"><PERSON><PERSON></a>.", "no_year_html": "Astronomers announce their discovery of the dwarf planet <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_(dwarf_planet)\" title=\"Eris (dwarf planet)\"><PERSON><PERSON></a>.", "links": [{"title": "<PERSON><PERSON> (dwarf planet)", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_(dwarf_planet)"}]}, {"year": "2010", "text": "An overloaded passenger ferry capsizes on the Kasai River in Bandundu Province, Democratic Republic of the Congo, resulting in at least 80 deaths.", "html": "2010 - An overloaded passenger ferry <a href=\"https://wikipedia.org/wiki/2010_Kasai_River_ferry_capsizing\" title=\"2010 Kasai River ferry capsizing\">capsizes</a> on the <a href=\"https://wikipedia.org/wiki/Kasai_River\" title=\"Kasai River\">Kasai River</a> in <a href=\"https://wikipedia.org/wiki/Bandundu_Province\" title=\"Bandundu Province\">Bandundu Province</a>, <a href=\"https://wikipedia.org/wiki/Democratic_Republic_of_the_Congo\" title=\"Democratic Republic of the Congo\">Democratic Republic of the Congo</a>, resulting in at least 80 deaths.", "no_year_html": "An overloaded passenger ferry <a href=\"https://wikipedia.org/wiki/2010_Kasai_River_ferry_capsizing\" title=\"2010 Kasai River ferry capsizing\">capsizes</a> on the <a href=\"https://wikipedia.org/wiki/Kasai_River\" title=\"Kasai River\">Kasai River</a> in <a href=\"https://wikipedia.org/wiki/Bandundu_Province\" title=\"Bandundu Province\">Bandundu Province</a>, <a href=\"https://wikipedia.org/wiki/Democratic_Republic_of_the_Congo\" title=\"Democratic Republic of the Congo\">Democratic Republic of the Congo</a>, resulting in at least 80 deaths.", "links": [{"title": "2010 Kasai River ferry capsizing", "link": "https://wikipedia.org/wiki/2010_Kasai_River_ferry_capsizing"}, {"title": "Kasai River", "link": "https://wikipedia.org/wiki/Kasai_River"}, {"title": "Bandundu Province", "link": "https://wikipedia.org/wiki/Bandundu_Province"}, {"title": "Democratic Republic of the Congo", "link": "https://wikipedia.org/wiki/Democratic_Republic_of_the_Congo"}]}, {"year": "2013", "text": "Two passenger trains collide in the Swiss municipality of Granges-près-Marnand near Lausanne injuring 25 people.", "html": "2013 - Two passenger trains <a href=\"https://wikipedia.org/wiki/Granges-pr%C3%A8s-Marnan<PERSON>_train_crash\" title=\"Granges-près-Marnan<PERSON> train crash\">collide</a> in the Swiss municipality of <a href=\"https://wikipedia.org/wiki/Granges-pr%C3%A8s-Marnand\" title=\"Granges-près-Marnand\"><PERSON><PERSON>-pr<PERSON>-<PERSON>nand</a> near <a href=\"https://wikipedia.org/wiki/Lausanne\" title=\"Lausanne\">Lausanne</a> injuring 25 people.", "no_year_html": "Two passenger trains <a href=\"https://wikipedia.org/wiki/Granges-pr%C3%A8s-<PERSON>nan<PERSON>_train_crash\" title=\"Granges-près-Marnan<PERSON> train crash\">collide</a> in the Swiss municipality of <a href=\"https://wikipedia.org/wiki/Granges-pr%C3%A8s-Marnand\" title=\"Granges-près-Marnand\"><PERSON><PERSON>-pr<PERSON>-<PERSON>nand</a> near <a href=\"https://wikipedia.org/wiki/Lausanne\" title=\"Lausanne\">Lausanne</a> injuring 25 people.", "links": [{"title": "Granges-près-<PERSON><PERSON><PERSON> train crash", "link": "https://wikipedia.org/wiki/Granges-pr%C3%A8s-<PERSON><PERSON><PERSON>_train_crash"}, {"title": "Granges-près-Marnand", "link": "https://wikipedia.org/wiki/Granges-pr%C3%A8s-<PERSON><PERSON>d"}, {"title": "Lausanne", "link": "https://wikipedia.org/wiki/Lausanne"}]}, {"year": "2015", "text": "The first piece of suspected debris from Malaysia Airlines Flight 370 is discovered on Réunion Island.", "html": "2015 - The first piece of suspected debris from <a href=\"https://wikipedia.org/wiki/Malaysia_Airlines_Flight_370\" title=\"Malaysia Airlines Flight 370\">Malaysia Airlines Flight 370</a> <a href=\"https://wikipedia.org/wiki/Search_for_Malaysia_Airlines_Flight_370#Debris_discovered_(July_2015_-_July_2016)\" title=\"Search for Malaysia Airlines Flight 370\">is discovered</a> on <a href=\"https://wikipedia.org/wiki/R%C3%A9union\" title=\"Réunion\">Réunion Island</a>.", "no_year_html": "The first piece of suspected debris from <a href=\"https://wikipedia.org/wiki/Malaysia_Airlines_Flight_370\" title=\"Malaysia Airlines Flight 370\">Malaysia Airlines Flight 370</a> <a href=\"https://wikipedia.org/wiki/Search_for_Malaysia_Airlines_Flight_370#Debris_discovered_(July_2015_-_July_2016)\" title=\"Search for Malaysia Airlines Flight 370\">is discovered</a> on <a href=\"https://wikipedia.org/wiki/R%C3%A9union\" title=\"Réunion\">Réunion Island</a>.", "links": [{"title": "Malaysia Airlines Flight 370", "link": "https://wikipedia.org/wiki/Malaysia_Airlines_Flight_370"}, {"title": "Search for Malaysia Airlines Flight 370", "link": "https://wikipedia.org/wiki/Search_for_Malaysia_Airlines_Flight_370#Debris_discovered_(July_2015_-_July_2016)"}, {"title": "Réunion", "link": "https://wikipedia.org/wiki/R%C3%A9union"}]}, {"year": "2019", "text": "The 2019 Altamira prison riot between rival Brazilian drug gangs leaves 62 dead.", "html": "2019 - The <a href=\"https://wikipedia.org/wiki/2019_Altamira_prison_riot\" title=\"2019 Altamira prison riot\">2019 Altamira prison riot</a> between rival Brazilian drug gangs leaves 62 dead.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/2019_Altamira_prison_riot\" title=\"2019 Altamira prison riot\">2019 Altamira prison riot</a> between rival Brazilian drug gangs leaves 62 dead.", "links": [{"title": "2019 Altamira prison riot", "link": "https://wikipedia.org/wiki/2019_Altamira_prison_riot"}]}, {"year": "2021", "text": "The International Space Station temporarily spins out of control, moving the ISS 45 degrees out of attitude, following an engine malfunction of Russian module <PERSON><PERSON>.", "html": "2021 - The <a href=\"https://wikipedia.org/wiki/International_Space_Station\" title=\"International Space Station\">International Space Station</a> temporarily spins out of control, moving the ISS 45 degrees out of attitude, following an engine malfunction of Russian module <a href=\"https://wikipedia.org/wiki/Nauka_(ISS_module)\" title=\"Nauka (ISS module)\">Nauka</a>.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/International_Space_Station\" title=\"International Space Station\">International Space Station</a> temporarily spins out of control, moving the ISS 45 degrees out of attitude, following an engine malfunction of Russian module <a href=\"https://wikipedia.org/wiki/Nauka_(ISS_module)\" title=\"Nauka (ISS module)\">Nauka</a>.", "links": [{"title": "International Space Station", "link": "https://wikipedia.org/wiki/International_Space_Station"}, {"title": "<PERSON><PERSON> (ISS module)", "link": "https://wikipedia.org/wiki/Nauka_(ISS_module)"}]}, {"year": "2024", "text": "3 children are stabbed to death and 10 other people injured at a dance studio in Southport, England. This incident, coupled with widespread online misinformation, leads to various racially-motivated riots across the UK.", "html": "2024 - 3 children are <a href=\"https://wikipedia.org/wiki/2024_Southport_stabbings\" title=\"2024 Southport stabbings\">stabbed to death</a> and 10 other people injured at a dance studio in <a href=\"https://wikipedia.org/wiki/Southport,_England\" class=\"mw-redirect\" title=\"Southport, England\">Southport, England</a>. This incident, coupled with widespread online misinformation, leads to various <a href=\"https://wikipedia.org/wiki/2024_United_Kingdom_riots\" title=\"2024 United Kingdom riots\">racially-motivated riots</a> across the <a href=\"https://wikipedia.org/wiki/United_Kingdom\" title=\"United Kingdom\">UK</a>.", "no_year_html": "3 children are <a href=\"https://wikipedia.org/wiki/2024_Southport_stabbings\" title=\"2024 Southport stabbings\">stabbed to death</a> and 10 other people injured at a dance studio in <a href=\"https://wikipedia.org/wiki/Southport,_England\" class=\"mw-redirect\" title=\"Southport, England\">Southport, England</a>. This incident, coupled with widespread online misinformation, leads to various <a href=\"https://wikipedia.org/wiki/2024_United_Kingdom_riots\" title=\"2024 United Kingdom riots\">racially-motivated riots</a> across the <a href=\"https://wikipedia.org/wiki/United_Kingdom\" title=\"United Kingdom\">UK</a>.", "links": [{"title": "2024 Southport stabbings", "link": "https://wikipedia.org/wiki/2024_Southport_stabbings"}, {"title": "Southport, England", "link": "https://wikipedia.org/wiki/Southport,_England"}, {"title": "2024 United Kingdom riots", "link": "https://wikipedia.org/wiki/2024_United_Kingdom_riots"}, {"title": "United Kingdom", "link": "https://wikipedia.org/wiki/United_Kingdom"}]}], "Births": [{"year": "869", "text": "<PERSON>, The 12th Imam of  Muslims (Shiites)  (d. 941)", "html": "869 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> al-Mahdi\"><PERSON></a>, The 12th Imam of Muslims (Shiites) (d. 941)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> al-Mahdi\"><PERSON></a>, The 12th Imam of Muslims (Shiites) (d. 941)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "996", "text": "<PERSON><PERSON>, Japanese nobleman (d. 1075)", "html": "996 - <a href=\"https://wikipedia.org/wiki/Fujiwara_no_Norimichi\" title=\"Fujiwara no Norimichi\"><PERSON><PERSON> no <PERSON>ichi</a>, Japanese nobleman (d. 1075)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Fujiwara_no_Norimichi\" title=\"Fujiwara no Norimichi\"><PERSON><PERSON> no <PERSON>ichi</a>, Japanese nobleman (d. 1075)", "links": [{"title": "<PERSON><PERSON> no Norimichi", "link": "https://wikipedia.org/wiki/Fujiwara_<PERSON>_<PERSON><PERSON>ichi"}]}, {"year": "1166", "text": "<PERSON>, French nobleman and king of Jerusalem (d. 1197)", "html": "1166 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Count_of_Champagne\" title=\"<PERSON>, Count of Champagne\"><PERSON> II</a>, French nobleman and king of Jerusalem (d. 1197)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Count_of_Champagne\" title=\"<PERSON>, Count of Champagne\"><PERSON> II</a>, French nobleman and king of Jerusalem (d. 1197)", "links": [{"title": "<PERSON>, Count of Champagne", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Count_of_Champagne"}]}, {"year": "1356", "text": "<PERSON> the Elder, king of Aragon, Valencia and Majorca  (d. 1410)", "html": "1356 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_Aragon\" title=\"<PERSON> of Aragon\"><PERSON> the Elder</a>, king of Aragon, Valencia and Majorca (d. 1410)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_Aragon\" title=\"<PERSON> of Aragon\"><PERSON> the Elder</a>, king of Aragon, Valencia and Majorca (d. 1410)", "links": [{"title": "<PERSON> of Aragon", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1537", "text": "<PERSON><PERSON>, Spanish nobleman (d. 1590)", "html": "1537 - <a href=\"https://wikipedia.org/wiki/Pedro_T%C3%A9llez-Gir%C3%B3n,_1st_Duke_of_Osuna\" class=\"mw-redirect\" title=\"<PERSON>, 1st Duke of Osuna\"><PERSON></a>, Spanish nobleman (d. 1590)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_T%C3%A9llez-Gir%C3%B3n,_1st_Duke_of_Osuna\" class=\"mw-redirect\" title=\"<PERSON>, 1st Duke of Osuna\"><PERSON></a>, Spanish nobleman (d. 1590)", "links": [{"title": "<PERSON>, 1st Duke of Osuna", "link": "https://wikipedia.org/wiki/Pedro_T%C3%A9llez-Gir%C3%B3n,_1st_Duke_of_Osuna"}]}, {"year": "1573", "text": "<PERSON>, duke of Pomerania-Stettin (d. 1618)", "html": "1573 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Duke_of_Pomerania\" title=\"<PERSON>, Duke of Pomerania\"><PERSON></a>, duke of Pomerania-Stettin (d. 1618)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Duke_of_Pomerania\" title=\"<PERSON>, Duke of Pomerania\"><PERSON></a>, duke of Pomerania-Stettin (d. 1618)", "links": [{"title": "<PERSON>, Duke of Pomerania", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Duke_of_Pomerania"}]}, {"year": "1580", "text": "<PERSON>, Italian sculptor (d. 1654)", "html": "1580 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian sculptor (d. 1654)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian sculptor (d. 1654)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1605", "text": "<PERSON>, German poet and hymn-writer (d. 1659)", "html": "1605 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German poet and hymn-writer (d. 1659)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German poet and hymn-writer (d. 1659)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1646", "text": "<PERSON>, German organist and composer (d. 1724)", "html": "1646 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German organist and composer (d. 1724)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German organist and composer (d. 1724)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1744", "text": "<PERSON><PERSON><PERSON>, Italian cardinal (d. 1830)", "html": "1744 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Italian cardinal (d. 1830)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Italian cardinal (d. 1830)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1763", "text": "<PERSON>, Scottish admiral and politician (d. 1845)", "html": "1763 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish admiral and politician (d. 1845)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish admiral and politician (d. 1845)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1797", "text": "<PERSON>, American businessman and financier (d. 1879)", "html": "1797 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American businessman and financier (d. 1879)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American businessman and financier (d. 1879)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1801", "text": "<PERSON>, English cartographer and publisher (d. 1853)", "html": "1801 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English cartographer and publisher (d. 1853)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English cartographer and publisher (d. 1853)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1805", "text": "<PERSON>, French historian and philosopher (d. 1859)", "html": "1805 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French historian and philosopher (d. 1859)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French historian and philosopher (d. 1859)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1806", "text": "<PERSON>, American businessman and banker (d. 1887)", "html": "1806 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American businessman and banker (d. 1887)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American businessman and banker (d. 1887)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1817", "text": "<PERSON>, Armenian-Russian painter and illustrator (d. 1900)", "html": "1817 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Armenian-Russian painter and illustrator (d. 1900)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Armenian-Russian painter and illustrator (d. 1900)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1817", "text": "<PERSON>, German pastor, composer, and conductor (d. 1893)", "html": "1817 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%B6rber\" title=\"<PERSON>\"><PERSON></a>, German pastor, composer, and conductor (d. 1893)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%B6rber\" title=\"<PERSON>\"><PERSON></a>, German pastor, composer, and conductor (d. 1893)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Martin_K%C3%B6rber"}]}, {"year": "1841", "text": "<PERSON>, Norwegian physician (d. 1912)", "html": "1841 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Norwegian physician (d. 1912)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Norwegian physician (d. 1912)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1843", "text": "<PERSON>, German linguist and academic (d. 1901)", "html": "1843 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(linguist)\" title=\"<PERSON> (linguist)\"><PERSON></a>, German linguist and academic (d. 1901)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(linguist)\" title=\"<PERSON> (linguist)\"><PERSON></a>, German linguist and academic (d. 1901)", "links": [{"title": "<PERSON> (linguist)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(linguist)"}]}, {"year": "1846", "text": "<PERSON>, German pianist and composer (d. 1918)", "html": "1846 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German pianist and composer (d. 1918)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German pianist and composer (d. 1918)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1846", "text": "<PERSON>, Brazilian princess  (d. 1921)", "html": "1846 - <a href=\"https://wikipedia.org/wiki/<PERSON>,_Princess_Imperial_of_Brazil\" title=\"<PERSON>, Princess Imperial of Brazil\"><PERSON></a>, Brazilian princess (d. 1921)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>,_Princess_Imperial_of_Brazil\" title=\"<PERSON>, Princess Imperial of Brazil\"><PERSON></a>, Brazilian princess (d. 1921)", "links": [{"title": "<PERSON>, Princess <PERSON> of Brazil", "link": "https://wikipedia.org/wiki/<PERSON>,_Princess_Imperial_of_Brazil"}]}, {"year": "1849", "text": "<PERSON>, Hungarian physician, author, and critic, co-founded the World Zionist Organization (d. 1923)", "html": "1849 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Hungarian physician, author, and critic, co-founded the <a href=\"https://wikipedia.org/wiki/World_Zionist_Organization\" title=\"World Zionist Organization\">World Zionist Organization</a> (d. 1923)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Hungarian physician, author, and critic, co-founded the <a href=\"https://wikipedia.org/wiki/World_Zionist_Organization\" title=\"World Zionist Organization\">World Zionist Organization</a> (d. 1923)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>au"}, {"title": "World Zionist Organization", "link": "https://wikipedia.org/wiki/World_Zionist_Organization"}]}, {"year": "1859", "text": "<PERSON>, Portuguese priest (d. 1948)", "html": "1859 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_Cruz\" title=\"<PERSON>\"><PERSON></a>, Portuguese priest (d. 1948)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_Cruz\" title=\"<PERSON>\"><PERSON></a>, Portuguese priest (d. 1948)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1860", "text": "<PERSON>, 2nd Baron <PERSON>, English politician, 8th Governor of Queensland (d. 1940)", "html": "1860 - <a href=\"https://wikipedia.org/wiki/<PERSON>,_2nd_Baron_<PERSON>\" title=\"<PERSON>, 2nd Baron <PERSON>\"><PERSON>, 2nd Baron <PERSON></a>, English politician, 8th <a href=\"https://wikipedia.org/wiki/Governor_of_Queensland\" title=\"Governor of Queensland\">Governor of Queensland</a> (d. 1940)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>,_2nd_Baron_<PERSON>\" title=\"<PERSON>, 2nd Baron <PERSON>\"><PERSON>, 2nd Baron <PERSON></a>, English politician, 8th <a href=\"https://wikipedia.org/wiki/Governor_of_Queensland\" title=\"Governor of Queensland\">Governor of Queensland</a> (d. 1940)", "links": [{"title": "<PERSON><PERSON><PERSON>, 2nd Baron <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_2nd_Baron_<PERSON>"}, {"title": "Governor of Queensland", "link": "https://wikipedia.org/wiki/Governor_of_Queensland"}]}, {"year": "1867", "text": "<PERSON><PERSON>, Moravian rabbi (d. 1942)", "html": "1867 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Moravian rabbi (d. 1942)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Moravian rabbi (d. 1942)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_Op<PERSON>heim"}]}, {"year": "1869", "text": "<PERSON>,  American novelist and dramatist (d. 1946)", "html": "1869 - <a href=\"https://wikipedia.org/wiki/Booth_Tarkington\" title=\"Booth Tarkington\"><PERSON></a>, American novelist and dramatist (d. 1946)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Booth_Tarkington\" title=\"Booth Tarkington\"><PERSON></a>, American novelist and dramatist (d. 1946)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Booth_Tarkington"}]}, {"year": "1871", "text": "<PERSON>, Estonian writer and journalist (d. 1930)", "html": "1871 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A4ndmets\" title=\"<PERSON>\"><PERSON></a>, Estonian writer and journalist (d. 1930)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A4ndmets\" title=\"<PERSON>\"><PERSON></a>, Estonian writer and journalist (d. 1930)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Jakob_M%C3%A4ndmets"}]}, {"year": "1872", "text": "<PERSON>, American author, lawyer, and politician (d. 1957)", "html": "1872 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author, lawyer, and politician (d. 1957)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author, lawyer, and politician (d. 1957)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1874", "text": "<PERSON><PERSON> <PERSON><PERSON>, Canadian minister and politician (d. 1942)", "html": "1874 - <a href=\"https://wikipedia.org/wiki/J._<PERSON><PERSON>_<PERSON>\" title=\"J. <PERSON><PERSON>\"><PERSON><PERSON> <PERSON><PERSON></a>, Canadian minister and politician (d. 1942)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/J._<PERSON><PERSON>_<PERSON>\" title=\"J. S. <PERSON>worth\"><PERSON><PERSON> <PERSON><PERSON></a>, Canadian minister and politician (d. 1942)", "links": [{"title": "<PERSON><PERSON> <PERSON><PERSON>", "link": "https://wikipedia.org/wiki/J<PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "1876", "text": "<PERSON>, Russian-American actress and acting teacher (d. 1949)", "html": "1876 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian-American actress and acting teacher (d. 1949)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian-American actress and acting teacher (d. 1949)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1878", "text": "<PERSON>, American author, poet, and playwright (d. 1937)", "html": "1878 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author, poet, and playwright (d. 1937)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author, poet, and playwright (d. 1937)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1883", "text": "<PERSON><PERSON><PERSON><PERSON>-<PERSON>, Colombian poet and author (d. 1942)", "html": "1883 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>-<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON>-<PERSON></a>, Colombian poet and author (d. 1942)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>-<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>-<PERSON>\"><PERSON><PERSON><PERSON><PERSON>-<PERSON></a>, Colombian poet and author (d. 1942)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>-<PERSON>", "link": "https://wikipedia.org/wiki/Porfirio_Barba-Jacob"}]}, {"year": "1883", "text": "<PERSON>, Italian fascist revolutionary and politician, 27th Prime Minister of Italy (d. 1945)", "html": "1883 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian fascist revolutionary and politician, 27th <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Italy\" title=\"Prime Minister of Italy\">Prime Minister of Italy</a> (d. 1945)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Benito <PERSON>\"><PERSON></a>, Italian fascist revolutionary and politician, 27th <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Italy\" title=\"Prime Minister of Italy\">Prime Minister of Italy</a> (d. 1945)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Prime Minister of Italy", "link": "https://wikipedia.org/wiki/Prime_Minister_of_Italy"}]}, {"year": "1884", "text": "<PERSON>, American financier and politician, 2nd Under Secretary of the Navy (d. 1975)", "html": "1884 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American financier and politician, 2nd <a href=\"https://wikipedia.org/wiki/Under_Secretary_of_the_Navy\" class=\"mw-redirect\" title=\"Under Secretary of the Navy\">Under Secretary of the Navy</a> (d. 1975)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American financier and politician, 2nd <a href=\"https://wikipedia.org/wiki/Under_Secretary_of_the_Navy\" class=\"mw-redirect\" title=\"Under Secretary of the Navy\">Under Secretary of the Navy</a> (d. 1975)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>d"}, {"title": "Under Secretary of the Navy", "link": "https://wikipedia.org/wiki/Under_Secretary_of_the_Navy"}]}, {"year": "1885", "text": "<PERSON><PERSON>, American actress (d. 1955)", "html": "1885 - <a href=\"https://wikipedia.org/wiki/Theda_Bara\" title=\"Theda Bara\"><PERSON><PERSON></a>, American actress (d. 1955)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Theda_Bara\" title=\"Theda Bara\"><PERSON><PERSON></a>, American actress (d. 1955)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Theda_Bara"}]}, {"year": "1887", "text": "<PERSON><PERSON><PERSON>, Hungarian-American pianist and composer (d. 1951)", "html": "1887 - <a href=\"https://wikipedia.org/wiki/<PERSON>g<PERSON>_<PERSON>\" title=\"<PERSON>g<PERSON>\"><PERSON><PERSON><PERSON></a>, Hungarian-American pianist and composer (d. 1951)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>g<PERSON>_<PERSON>\" title=\"<PERSON>g<PERSON>\"><PERSON><PERSON><PERSON></a>, Hungarian-American pianist and composer (d. 1951)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>g<PERSON>_<PERSON>g"}]}, {"year": "1891", "text": "<PERSON>, German-Israeli gynecologist and academic (d. 1966)", "html": "1891 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German-Israeli gynecologist and academic (d. 1966)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German-Israeli gynecologist and academic (d. 1966)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1892", "text": "<PERSON>, American actor and singer (d. 1984)", "html": "1892 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor and singer (d. 1984)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor and singer (d. 1984)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1896", "text": "<PERSON>, Mexican-American rights activist (d. 1986)", "html": "1896 - <a href=\"https://wikipedia.org/wiki/Maria_L._<PERSON>_Hern%C3%A1ndez\" title=\"<PERSON>\"><PERSON></a>, Mexican-American rights activist (d. 1986)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Maria_L._<PERSON>_Hern%C3%A1ndez\" title=\"<PERSON>\"><PERSON></a>, Mexican-American rights activist (d. 1986)", "links": [{"title": "Maria L<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_de_Hern%C3%A1ndez"}]}, {"year": "1897", "text": "<PERSON>, Guyanese-English general (d. 1983)", "html": "1897 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Guyanese-English general (d. 1983)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Guyanese-English general (d. 1983)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1898", "text": "<PERSON><PERSON><PERSON>, American physicist and academic, Nobel Prize Laureate (d. 1988)", "html": "1898 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American physicist and academic, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Physics\" title=\"Nobel Prize in Physics\">Nobel Prize</a> Laureate (d. 1988)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American physicist and academic, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Physics\" title=\"Nobel Prize in Physics\">Nobel Prize</a> Laureate (d. 1988)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>"}, {"title": "Nobel Prize in Physics", "link": "https://wikipedia.org/wiki/Nobel_Prize_in_Physics"}]}, {"year": "1899", "text": "<PERSON>, American baseball player (d. 1959)", "html": "1899 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player (d. 1959)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player (d. 1959)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1900", "text": "<PERSON>, Australian community worker and political activist (d. 1986)", "html": "1900 - <a href=\"https://wikipedia.org/wiki/Mary_<PERSON>._Austin\" title=\"Mary V. Austin\"><PERSON></a>, Australian community worker and political activist (d. 1986)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Mary_<PERSON>_Austin\" title=\"Mary V. Austin\"><PERSON></a>, Australian community worker and political activist (d. 1986)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Mary_<PERSON>._<PERSON>"}]}, {"year": "1900", "text": "<PERSON><PERSON><PERSON><PERSON>, Swedish novelist and short story writer, Nobel Prize Laureate (d. 1976)", "html": "1900 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Swedish novelist and short story writer, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Literature\" title=\"Nobel Prize in Literature\">Nobel Prize</a> Laureate (d. 1976)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Swedish novelist and short story writer, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Literature\" title=\"Nobel Prize in Literature\">Nobel Prize</a> Laureate (d. 1976)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>"}, {"title": "Nobel Prize in Literature", "link": "https://wikipedia.org/wiki/Nobel_Prize_in_Literature"}]}, {"year": "1900", "text": "<PERSON>, Italian labor leader, activist, and journalist (d. 1980)", "html": "1900 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian labor leader, activist, and journalist (d. 1980)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian labor leader, activist, and journalist (d. 1980)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1900", "text": "<PERSON>, American composer, and bandleader (d. 1964)", "html": "1900 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American composer, and bandleader (d. 1964)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American composer, and bandleader (d. 1964)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1904", "text": "<PERSON><PERSON><PERSON>, Burmese monk and philosopher (d. 1982)", "html": "1904 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Burmese monk and philosopher (d. 1982)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Burmese monk and philosopher (d. 1982)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>adaw"}]}, {"year": "1904", "text": "<PERSON><PERSON> <PERSON><PERSON> <PERSON><PERSON>, French-Indian pilot and businessman, founded Tata Motors and Tata Global Beverages (d. 1993)", "html": "1904 - <a href=\"https://wikipedia.org/wiki/J._R._D._Tata\" title=\"J. R. D. Tata\"><PERSON><PERSON> <PERSON><PERSON> <PERSON><PERSON></a>, French-Indian pilot and businessman, founded <a href=\"https://wikipedia.org/wiki/Tata_Motors\" title=\"Tata Motors\">Tata Motors</a> and <a href=\"https://wikipedia.org/wiki/Tata_Global_Beverages\" class=\"mw-redirect\" title=\"Tata Global Beverages\">Tata Global Beverages</a> (d. 1993)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/J._R._D._Tata\" title=\"J. R. D. Tata\"><PERSON><PERSON> <PERSON><PERSON> <PERSON><PERSON></a>, French-Indian pilot and businessman, founded <a href=\"https://wikipedia.org/wiki/Tata_Motors\" title=\"Tata Motors\">Tata Motors</a> and <a href=\"https://wikipedia.org/wiki/Tata_Global_Beverages\" class=\"mw-redirect\" title=\"Tata Global Beverages\">Tata Global Beverages</a> (d. 1993)", "links": [{"title": "J. R. D. <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>"}, {"title": "Tata Motors", "link": "https://wikipedia.org/wiki/Tata_Motors"}, {"title": "Tata Global Beverages", "link": "https://wikipedia.org/wiki/Tata_Global_Beverages"}]}, {"year": "1905", "text": "<PERSON>, American actress (d. 1965)", "html": "1905 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Bow\"><PERSON></a>, American actress (d. 1965)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Bow\"><PERSON></a>, American actress (d. 1965)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1905", "text": "<PERSON><PERSON>, Swedish economist and diplomat, 2nd Secretary-General of the United Nations, Nobel Prize Laureate (d. 1961)", "html": "1905 - <a href=\"https://wikipedia.org/wiki/Dag_<PERSON>j%C3%B6ld\" title=\"<PERSON><PERSON>j<PERSON>\"><PERSON><PERSON></a>, Swedish economist and diplomat, 2nd <a href=\"https://wikipedia.org/wiki/Secretary-General_of_the_United_Nations\" title=\"Secretary-General of the United Nations\">Secretary-General of the United Nations</a>, <a href=\"https://wikipedia.org/wiki/Nobel_Peace_Prize\" title=\"Nobel Peace Prize\">Nobel Prize</a> Laureate (d. 1961)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Dag_<PERSON>j%C3%B6ld\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Swedish economist and diplomat, 2nd <a href=\"https://wikipedia.org/wiki/Secretary-General_of_the_United_Nations\" title=\"Secretary-General of the United Nations\">Secretary-General of the United Nations</a>, <a href=\"https://wikipedia.org/wiki/Nobel_Peace_Prize\" title=\"Nobel Peace Prize\">Nobel Prize</a> Laureate (d. 1961)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Dag_Hammarskj%C3%B6ld"}, {"title": "Secretary-General of the United Nations", "link": "https://wikipedia.org/wiki/Secretary-General_of_the_United_Nations"}, {"title": "Nobel Peace Prize", "link": "https://wikipedia.org/wiki/Nobel_Peace_Prize"}]}, {"year": "1905", "text": "<PERSON>, American poet and translator (d. 2006)", "html": "1905 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American poet and translator (d. 2006)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American poet and translator (d. 2006)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1906", "text": "<PERSON><PERSON>, American actress and singer (d. 1935)", "html": "1906 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American actress and singer (d. 1935)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American actress and singer (d. 1935)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1907", "text": "<PERSON>, American lawyer (d. 1996)", "html": "1907 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer (d. 1996)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer (d. 1996)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1909", "text": "<PERSON><PERSON>, American author (d. 1997)", "html": "1909 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American author (d. 1997)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American author (d. 1997)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_Baker"}]}, {"year": "1909", "text": "<PERSON>, American-Spanish author (d. 1984)", "html": "1909 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American-Spanish author (d. 1984)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American-Spanish author (d. 1984)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Chester_Himes"}]}, {"year": "1910", "text": "<PERSON>, American actress (d. 1983)", "html": "1910 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress (d. 1983)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_Page\" title=\"<PERSON> Page\"><PERSON></a>, American actress (d. 1983)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Gale_Page"}]}, {"year": "1911", "text": "<PERSON>, American lawyer and politician, 60th Governor of Massachusetts (d. 1995)", "html": "1911 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and politician, 60th <a href=\"https://wikipedia.org/wiki/Governor_of_Massachusetts\" title=\"Governor of Massachusetts\">Governor of Massachusetts</a> (d. 1995)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and politician, 60th <a href=\"https://wikipedia.org/wiki/Governor_of_Massachusetts\" title=\"Governor of Massachusetts\">Governor of Massachusetts</a> (d. 1995)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Governor of Massachusetts", "link": "https://wikipedia.org/wiki/Governor_of_Massachusetts"}]}, {"year": "1911", "text": "Archbishop <PERSON><PERSON><PERSON><PERSON> of America (d. 2005)", "html": "1911 - <a href=\"https://wikipedia.org/wiki/Archbishop_<PERSON><PERSON><PERSON><PERSON>_of_America\" title=\"Archbishop <PERSON><PERSON><PERSON><PERSON> of America\">Archbishop <PERSON><PERSON><PERSON><PERSON> of America</a> (d. 2005)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Archbishop_<PERSON><PERSON><PERSON><PERSON>_of_America\" title=\"Archbishop <PERSON><PERSON><PERSON><PERSON> of America\">Archbishop <PERSON><PERSON><PERSON><PERSON> of America</a> (d. 2005)", "links": [{"title": "Archbishop <PERSON><PERSON><PERSON><PERSON> of America", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON><PERSON><PERSON>_of_America"}]}, {"year": "1913", "text": "<PERSON>, German war criminal, leader of the 1944 Ardeatine massacre (d. 2013)", "html": "1913 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German war criminal, leader of the 1944 <a href=\"https://wikipedia.org/wiki/Ardeatine_massacre\" title=\"Ardeatine massacre\">Ardeatine massacre</a> (d. 2013)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German war criminal, leader of the 1944 <a href=\"https://wikipedia.org/wiki/Ardeatine_massacre\" title=\"Ardeatine massacre\">Ardeatine massacre</a> (d. 2013)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}, {"title": "Ardeatine massacre", "link": "https://wikipedia.org/wiki/Ardeatine_massacre"}]}, {"year": "1914", "text": "<PERSON>, American actor and activist (d. 2017)", "html": "1914 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor and activist (d. 2017)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor and activist (d. 2017)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1915", "text": "<PERSON>, American colonel and religious leader (d. 1985)", "html": "1915 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American colonel and religious leader (d. 1985)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American colonel and religious leader (d. 1985)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "1915", "text": "<PERSON>, American soldier and politician, 64th Governor of Massachusetts (d. 1998)", "html": "1915 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, American soldier and politician, 64th <a href=\"https://wikipedia.org/wiki/Governor_of_Massachusetts\" title=\"Governor of Massachusetts\">Governor of Massachusetts</a> (d. 1998)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, American soldier and politician, 64th <a href=\"https://wikipedia.org/wiki/Governor_of_Massachusetts\" title=\"Governor of Massachusetts\">Governor of Massachusetts</a> (d. 1998)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>"}, {"title": "Governor of Massachusetts", "link": "https://wikipedia.org/wiki/Governor_of_Massachusetts"}]}, {"year": "1916", "text": "<PERSON><PERSON>, American director, producer, and screenwriter (d. 2001)", "html": "1916 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"Bud<PERSON> Boetticher\"><PERSON><PERSON></a>, American director, producer, and screenwriter (d. 2001)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"Bud<PERSON> Boetticher\"><PERSON><PERSON></a>, American director, producer, and screenwriter (d. 2001)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>cher"}]}, {"year": "1916", "text": "<PERSON>, American guitarist (d. 1942)", "html": "1916 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Charlie <PERSON>\"><PERSON></a>, American guitarist (d. 1942)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Charlie Christian\"><PERSON></a>, American guitarist (d. 1942)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1916", "text": "<PERSON>, Australian politician, 39th Premier of Victoria (d. 2004)", "html": "1916 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian politician, 39th <a href=\"https://wikipedia.org/wiki/Premier_of_Victoria\" title=\"Premier of Victoria\">Premier of Victoria</a> (d. 2004)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian politician, 39th <a href=\"https://wikipedia.org/wiki/Premier_of_Victoria\" title=\"Premier of Victoria\">Premier of Victoria</a> (d. 2004)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}, {"title": "Premier of Victoria", "link": "https://wikipedia.org/wiki/Premier_of_Victoria"}]}, {"year": "1917", "text": "<PERSON><PERSON><PERSON>, German SS officer (d. 2013)", "html": "1917 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, German <a href=\"https://wikipedia.org/wiki/SS\" class=\"mw-redirect\" title=\"SS\">SS</a> officer (d. 2013)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, German <a href=\"https://wikipedia.org/wiki/SS\" class=\"mw-redirect\" title=\"SS\">SS</a> officer (d. 2013)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}, {"title": "SS", "link": "https://wikipedia.org/wiki/SS"}]}, {"year": "1918", "text": "<PERSON>, American writer and producer (d. 2014)", "html": "1918 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Don Ingalls\"><PERSON></a>, American writer and producer (d. 2014)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Don Ingalls\"><PERSON></a>, American writer and producer (d. 2014)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Don_Ingalls"}]}, {"year": "1918", "text": "<PERSON>, American journalist and author (d. 1968)", "html": "1918 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%27Connor\" title=\"<PERSON>\"><PERSON></a>, American journalist and author (d. 1968)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%27Connor\" title=\"<PERSON>\"><PERSON></a>, American journalist and author (d. 1968)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Edwin_O%27Connor"}]}, {"year": "1918", "text": "<PERSON>, American novelist, essayist, and memoirist (d. 2005)", "html": "1918 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American novelist, essayist, and memoirist (d. 2005)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American novelist, essayist, and memoirist (d. 2005)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1920", "text": "<PERSON>, Australian businessman (d. 2007)", "html": "1920 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian businessman (d. 2007)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian businessman (d. 2007)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1921", "text": "<PERSON>, American actor (d. 1987)", "html": "1921 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(actor)\" title=\"<PERSON> (actor)\"><PERSON></a>, American actor (d. 1987)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(actor)\" title=\"<PERSON> (actor)\"><PERSON></a>, American actor (d. 1987)", "links": [{"title": "<PERSON> (actor)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(actor)"}]}, {"year": "1921", "text": "<PERSON>, French photographer and journalist (d. 2012)", "html": "1921 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French photographer and journalist (d. 2012)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French photographer and journalist (d. 2012)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1923", "text": "<PERSON>, American screenwriter and producer (d. 2013)", "html": "1923 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(writer)\" title=\"<PERSON> (writer)\"><PERSON></a>, American screenwriter and producer (d. 2013)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(writer)\" title=\"<PERSON> (writer)\"><PERSON></a>, American screenwriter and producer (d. 2013)", "links": [{"title": "<PERSON> (writer)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(writer)"}]}, {"year": "1923", "text": "<PERSON>, American scientist and engineer (d. 2014)", "html": "1923 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American scientist and engineer (d. 2014)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American scientist and engineer (d. 2014)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1923", "text": "<PERSON>, English businessman, founded Marshall Amplification (d. 2012)", "html": "1923 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(businessman)\" title=\"<PERSON> (businessman)\"><PERSON></a>, English businessman, founded <a href=\"https://wikipedia.org/wiki/Marshall_Amplification\" title=\"Marshall Amplification\"><PERSON> Amplification</a> (d. 2012)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>(businessman)\" title=\"<PERSON> (businessman)\"><PERSON></a>, English businessman, founded <a href=\"https://wikipedia.org/wiki/<PERSON>_Amplification\" title=\"Marshall Amplification\"><PERSON> Amplification</a> (d. 2012)", "links": [{"title": "<PERSON> (businessman)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(businessman)"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_Amplification"}]}, {"year": "1923", "text": "<PERSON>, American bodybuilder and actor (d. 2003)", "html": "1923 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American bodybuilder and actor (d. 2003)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American bodybuilder and actor (d. 2003)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1924", "text": "<PERSON>, Canadian-American actor (d. 2005)", "html": "1924 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian-American actor (d. 2005)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian-American actor (d. 2005)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1924", "text": "<PERSON>, American actor (d. 2016)", "html": "1924 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(actor)\" title=\"<PERSON> (actor)\"><PERSON></a>, American actor (d. 2016)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>(actor)\" title=\"<PERSON> (actor)\"><PERSON></a>, American actor (d. 2016)", "links": [{"title": "<PERSON> (actor)", "link": "https://wikipedia.org/wiki/<PERSON>(actor)"}]}, {"year": "1925", "text": "<PERSON>, American mathematician and academic (d. 2014)", "html": "1925 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American mathematician and academic (d. 2014)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American mathematician and academic (d. 2014)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "1925", "text": "<PERSON>, Canadian ice hockey player, manager, and sportscaster (d. 2019)", "html": "1925 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player, manager, and sportscaster (d. 2019)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player, manager, and sportscaster (d. 2019)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1925", "text": "<PERSON>, American sailor (d. 2000)", "html": "1925 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American sailor (d. 2000)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American sailor (d. 2000)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1925", "text": "<PERSON><PERSON>, Greek composer (d. 2021)", "html": "1925 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Greek composer (d. 2021)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Greek composer (d. 2021)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1926", "text": "<PERSON>, Baron <PERSON> of Kincraig, Scottish physician, academic, and politician (d. 2015)", "html": "1926 - <a href=\"https://wikipedia.org/wiki/<PERSON>,_Baron_<PERSON><PERSON><PERSON>_of_Kincraig\" title=\"<PERSON>, Baron <PERSON> of Kincraig\"><PERSON>, Baron <PERSON> of Kincraig</a>, Scottish physician, academic, and politician (d. 2015)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>,_Baron_<PERSON>_of_Kincraig\" title=\"<PERSON>, Baron <PERSON> of Kincraig\"><PERSON>, Baron <PERSON> of Kincraig</a>, Scottish physician, academic, and politician (d. 2015)", "links": [{"title": "<PERSON>, Baron <PERSON> of Kincraig", "link": "https://wikipedia.org/wiki/<PERSON>,_Baron_<PERSON>_of_Kincraig"}]}, {"year": "1927", "text": "<PERSON>, Dutch author, poet, and playwright (d. 2010)", "html": "1927 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch author, poet, and playwright (d. 2010)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch author, poet, and playwright (d. 2010)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1930", "text": "<PERSON>, American dancer and choreographer (d. 2018)", "html": "1930 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(choreographer)\" title=\"<PERSON> (choreographer)\"><PERSON></a>, American dancer and choreographer (d. 2018)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(choreographer)\" title=\"<PERSON> (choreographer)\"><PERSON></a>, American dancer and choreographer (d. 2018)", "links": [{"title": "<PERSON> (choreographer)", "link": "https://wikipedia.org/wiki/<PERSON>_(choreographer)"}]}, {"year": "1931", "text": "<PERSON><PERSON><PERSON>, Norwegian pianist, composer, and bandleader (d. 2020)", "html": "1931 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Norwegian pianist, composer, and bandleader (d. 2020)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Norwegian pianist, composer, and bandleader (d. 2020)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1932", "text": "<PERSON>, English diplomat (d. 2021)", "html": "1932 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English diplomat (d. 2021)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English diplomat (d. 2021)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1932", "text": "<PERSON>, American businesswoman and politician", "html": "1932 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American businesswoman and politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American businesswoman and politician", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1933", "text": "<PERSON>, Italian-American wrestler, manager, and actor (d. 2009)", "html": "1933 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian-American wrestler, manager, and actor (d. 2009)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian-American wrestler, manager, and actor (d. 2009)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1933", "text": "<PERSON>, English race car driver (d. 2012)", "html": "1933 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(racing_driver)\" title=\"<PERSON> (racing driver)\"><PERSON></a>, English race car driver (d. 2012)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(racing_driver)\" title=\"<PERSON> (racing driver)\"><PERSON></a>, English race car driver (d. 2012)", "links": [{"title": "<PERSON> (racing driver)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(racing_driver)"}]}, {"year": "1933", "text": "<PERSON>, American actor and rancher", "html": "1933 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(actor)\" title=\"<PERSON> (actor)\"><PERSON></a>, American actor and rancher", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>(actor)\" title=\"<PERSON> (actor)\"><PERSON></a>, American actor and rancher", "links": [{"title": "<PERSON> (actor)", "link": "https://wikipedia.org/wiki/<PERSON>_(actor)"}]}, {"year": "1933", "text": "<PERSON>, American folk singer-songwriter and musician (d. 2024)", "html": "1933 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American folk singer-songwriter and musician (d. 2024)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American folk singer-songwriter and musician (d. 2024)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1935", "text": "<PERSON>, German tenor and conductor (d. 2019)", "html": "1935 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German tenor and conductor (d. 2019)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German tenor and conductor (d. 2019)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1936", "text": "<PERSON>, American lawyer and politician, 20th United States Secretary of Labor", "html": "1936 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and politician, 20th <a href=\"https://wikipedia.org/wiki/United_States_Secretary_of_Labor\" title=\"United States Secretary of Labor\">United States Secretary of Labor</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and politician, 20th <a href=\"https://wikipedia.org/wiki/United_States_Secretary_of_Labor\" title=\"United States Secretary of Labor\">United States Secretary of Labor</a>", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "United States Secretary of Labor", "link": "https://wikipedia.org/wiki/United_States_Secretary_of_Labor"}]}, {"year": "1937", "text": "<PERSON>, American economist and academic, Nobel Prize Laureate", "html": "1937 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American economist and academic, <a href=\"https://wikipedia.org/wiki/Nobel_Memorial_Prize_in_Economic_Sciences\" title=\"Nobel Memorial Prize in Economic Sciences\">Nobel Prize</a> Laureate", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American economist and academic, <a href=\"https://wikipedia.org/wiki/Nobel_Memorial_Prize_in_Economic_Sciences\" title=\"Nobel Memorial Prize in Economic Sciences\">Nobel Prize</a> Laureate", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Nobel Memorial Prize in Economic Sciences", "link": "https://wikipedia.org/wiki/Nobel_Memorial_Prize_in_Economic_Sciences"}]}, {"year": "1938", "text": "<PERSON>, Canadian-American journalist and author (d. 2005)", "html": "1938 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian-American journalist and author (d. 2005)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian-American journalist and author (d. 2005)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1938", "text": "<PERSON>, Canadian physician and politician (d. 2021)", "html": "1938 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian physician and politician (d. 2021)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian physician and politician (d. 2021)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1940", "text": "<PERSON>, American chemist", "html": "1940 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(scientist)\" title=\"<PERSON> (scientist)\"><PERSON></a>, American chemist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(scientist)\" title=\"<PERSON> (scientist)\"><PERSON></a>, American chemist", "links": [{"title": "<PERSON> (scientist)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(scientist)"}]}, {"year": "1940", "text": "<PERSON><PERSON>, Filipina economist and political commentator", "html": "1940 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Filipina economist and political commentator", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> Mon<PERSON>d\"><PERSON><PERSON></a>, Filipina economist and political commentator", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_Monsod"}]}, {"year": "1941", "text": "<PERSON>, American engineer and politician (d. 2007)", "html": "1941 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(politician)\" class=\"mw-redirect\" title=\"<PERSON> (politician)\"><PERSON></a>, American engineer and politician (d. 2007)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>(politician)\" class=\"mw-redirect\" title=\"<PERSON> (politician)\"><PERSON></a>, American engineer and politician (d. 2007)", "links": [{"title": "<PERSON> (politician)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>(politician)"}]}, {"year": "1941", "text": "<PERSON><PERSON><PERSON>, Indonesian poet and playwright", "html": "1941 - <a href=\"https://wikipedia.org/wiki/Goenawan_<PERSON>\" title=\"Goenawan <PERSON>ham<PERSON>\"><PERSON><PERSON><PERSON></a>, Indonesian poet and playwright", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Go<PERSON><PERSON>_<PERSON>\" title=\"Goenawan Mohamad\"><PERSON><PERSON><PERSON></a>, Indonesian poet and playwright", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>ad"}]}, {"year": "1941", "text": "<PERSON>, English actor (d. 2022)", "html": "1941 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(actor)\" title=\"<PERSON> (actor)\"><PERSON></a>, English actor (d. 2022)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(actor)\" title=\"<PERSON> (actor)\"><PERSON></a>, English actor (d. 2022)", "links": [{"title": "<PERSON> (actor)", "link": "https://wikipedia.org/wiki/<PERSON>_(actor)"}]}, {"year": "1942", "text": "<PERSON>, Australian singer-songwriter", "html": "1942 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian singer-songwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian singer-songwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1942", "text": "<PERSON>, American actor (d. 2022)", "html": "1942 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor (d. 2022)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor (d. 2022)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1943", "text": "<PERSON>, English snooker player and sportscaster", "html": "1943 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(snooker_player)\" title=\"<PERSON> (snooker player)\"><PERSON></a>, English snooker player and sportscaster", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(snooker_player)\" title=\"<PERSON> (snooker player)\"><PERSON></a>, English snooker player and sportscaster", "links": [{"title": "<PERSON> (snooker player)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(snooker_player)"}]}, {"year": "1944", "text": "<PERSON>, American rock climber and mountaineer (d. 2018)", "html": "1944 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American rock climber and mountaineer (d. 2018)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American rock climber and mountaineer (d. 2018)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1945", "text": "<PERSON>, American author and educator", "html": "1945 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author and educator", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author and educator", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1945", "text": "<PERSON><PERSON>, Romanian footballer, coach, and manager", "html": "1945 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Romanian footballer, coach, and manager", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Romanian footballer, coach, and manager", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1946", "text": "<PERSON><PERSON><PERSON>, Chilean painter", "html": "1946 - <a href=\"https://wikipedia.org/wiki/Ximena_Armas\" title=\"Ximena Armas\"><PERSON><PERSON><PERSON></a>, Chilean painter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"Ximena Armas\"><PERSON><PERSON><PERSON></a>, Chilean painter", "links": [{"title": "Xi<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Ximena_Armas"}]}, {"year": "1946", "text": "<PERSON><PERSON>, Swedish race car driver", "html": "1946 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Swedish race car driver", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Swedish race car driver", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1946", "text": "<PERSON>, American keyboard player, songwriter, and producer", "html": "1946 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, American keyboard player, songwriter, and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, American keyboard player, songwriter, and producer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1946", "text": "<PERSON>, Italian mountaineer and adventurer", "html": "1946 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian mountaineer and adventurer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian mountaineer and adventurer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1946", "text": "<PERSON>, English actress", "html": "1946 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actress", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1946", "text": "<PERSON><PERSON><PERSON>, Estonian basketball player", "html": "1946 - <a href=\"https://wikipedia.org/wiki/Alek<PERSON>_Tammiste\" title=\"Aleksei Tam<PERSON>e\"><PERSON><PERSON><PERSON></a>, Estonian basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Alek<PERSON>_Tammiste\" title=\"Aleksei Tammiste\"><PERSON><PERSON><PERSON></a>, Estonian basketball player", "links": [{"title": "Alek<PERSON>", "link": "https://wikipedia.org/wiki/Aleksei_Tammiste"}]}, {"year": "1947", "text": "<PERSON>, American golfer and coach (d. 2006)", "html": "1947 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American golfer and coach (d. 2006)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American golfer and coach (d. 2006)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1948", "text": "<PERSON>, New Zealand-Australian comedian, actor, producer, and screenwriter (d. 2017)", "html": "1948 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(satirist)\" title=\"<PERSON> (satirist)\"><PERSON></a>, New Zealand-Australian comedian, actor, producer, and screenwriter (d. 2017)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(satirist)\" title=\"<PERSON> (satirist)\"><PERSON></a>, New Zealand-Australian comedian, actor, producer, and screenwriter (d. 2017)", "links": [{"title": "<PERSON> (satirist)", "link": "https://wikipedia.org/wiki/<PERSON>_(satirist)"}]}, {"year": "1949", "text": "<PERSON>, American actress", "html": "1949 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1949", "text": "<PERSON><PERSON>, Ecuadorian lawyer and politician, 51st President of Ecuador", "html": "1949 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Ecuadorian lawyer and politician, 51st <a href=\"https://wikipedia.org/wiki/List_of_heads_of_state_of_Ecuador\" class=\"mw-redirect\" title=\"List of heads of state of Ecuador\">President of Ecuador</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Ecuadorian lawyer and politician, 51st <a href=\"https://wikipedia.org/wiki/List_of_heads_of_state_of_Ecuador\" class=\"mw-redirect\" title=\"List of heads of state of Ecuador\">President of Ecuador</a>", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}, {"title": "List of heads of state of Ecuador", "link": "https://wikipedia.org/wiki/List_of_heads_of_state_of_Ecuador"}]}, {"year": "1950", "text": "<PERSON>, American painter, author, and dancer", "html": "1950 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American painter, author, and dancer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American painter, author, and dancer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1951", "text": "<PERSON>, English psychologist and theorist", "html": "1951 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English psychologist and theorist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English psychologist and theorist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1951", "text": "<PERSON>, American baseball player and coach", "html": "1951 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player and coach", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1951", "text": "<PERSON>, American actor, director, screenwriter, and composer", "html": "1951 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor, director, screenwriter, and composer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor, director, screenwriter, and composer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1952", "text": "<PERSON>, <PERSON>, English businessman and politician", "html": "1952 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Baron_<PERSON>\" title=\"<PERSON>, Baron Blackwell\"><PERSON>, Baron <PERSON></a>, English businessman and politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Baron_<PERSON>\" title=\"<PERSON>, Baron Blackwell\"><PERSON>, Baron <PERSON></a>, English businessman and politician", "links": [{"title": "<PERSON>, <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_<PERSON>_<PERSON>"}]}, {"year": "1952", "text": "<PERSON>, English snooker player and sportscaster", "html": "1952 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(snooker_player)\" title=\"<PERSON> (snooker player)\"><PERSON></a>, English snooker player and sportscaster", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(snooker_player)\" title=\"<PERSON> (snooker player)\"><PERSON></a>, English snooker player and sportscaster", "links": [{"title": "<PERSON> (snooker player)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(snooker_player)"}]}, {"year": "1952", "text": "<PERSON><PERSON>, Greek politician", "html": "1952 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>-<PERSON>\" title=\"<PERSON>\"><PERSON>-<PERSON></a>, Greek politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>-<PERSON>\" title=\"<PERSON>\"><PERSON>-<PERSON></a>, Greek politician", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>-<PERSON>"}]}, {"year": "1953", "text": "<PERSON>, American director and producer", "html": "1953 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American director and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American director and producer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1953", "text": "<PERSON>, American television host and actor", "html": "1953 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American television host and actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American television host and actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1953", "text": "<PERSON><PERSON><PERSON>, Canadian musician", "html": "1953 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON> <PERSON>\"><PERSON><PERSON><PERSON></a>, Canadian musician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON> <PERSON>\"><PERSON><PERSON><PERSON></a>, Canadian musician", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1953", "text": "<PERSON>, Irish poet and playwright", "html": "1953 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Irish poet and playwright", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Irish poet and playwright", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1953", "text": "<PERSON>, American musician", "html": "1953 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American musician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American musician", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>fa"}]}, {"year": "1955", "text": "<PERSON><PERSON><PERSON><PERSON>, French actor, director, and screenwriter", "html": "1955 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, French actor, director, and screenwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, French actor, director, and screenwriter", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1955", "text": "<PERSON>, American illustrator (d. 2008)", "html": "1955 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American illustrator (d. 2008)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American illustrator (d. 2008)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1955", "text": "<PERSON>, English politician, Minister of State for Competitiveness", "html": "1955 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English politician, <a href=\"https://wikipedia.org/wiki/Minister_of_State_for_Competitiveness\" title=\"Minister of State for Competitiveness\">Minister of State for Competitiveness</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English politician, <a href=\"https://wikipedia.org/wiki/Minister_of_State_for_Competitiveness\" title=\"Minister of State for Competitiveness\">Minister of State for Competitiveness</a>", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}, {"title": "Minister of State for Competitiveness", "link": "https://wikipedia.org/wiki/Minister_of_State_for_Competitiveness"}]}, {"year": "1956", "text": "<PERSON>, American boxer, trainer, and sportscaster", "html": "1956 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Teddy Atlas\"><PERSON></a>, American boxer, trainer, and sportscaster", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Teddy Atlas\"><PERSON></a>, American boxer, trainer, and sportscaster", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1956", "text": "<PERSON>, American lawyer and politician, 62nd Governor of Mississippi", "html": "1956 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and politician, 62nd <a href=\"https://wikipedia.org/wiki/List_of_Governors_of_Mississippi\" class=\"mw-redirect\" title=\"List of Governors of Mississippi\">Governor of Mississippi</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and politician, 62nd <a href=\"https://wikipedia.org/wiki/List_of_Governors_of_Mississippi\" class=\"mw-redirect\" title=\"List of Governors of Mississippi\">Governor of Mississippi</a>", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "List of Governors of Mississippi", "link": "https://wikipedia.org/wiki/List_of_Governors_of_Mississippi"}]}, {"year": "1956", "text": "<PERSON><PERSON>, Spanish cyclist", "html": "1956 - <a href=\"https://wikipedia.org/wiki/Faust<PERSON>_Rup%C3%A9rez\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Spanish cyclist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_Rup%C3%A9rez\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Spanish cyclist", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Faustino_Rup%C3%A9rez"}]}, {"year": "1957", "text": "<PERSON>, Australian author and educator (d. 2014)", "html": "1957 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian author and educator (d. 2014)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian author and educator (d. 2014)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1957", "text": "<PERSON>, Lithuanian-Swiss chess player (d. 2016)", "html": "1957 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Lithuanian-Swiss chess player (d. 2016)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Lithuanian-Swiss chess player (d. 2016)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1957", "text": "<PERSON><PERSON>, Russian gymnast and coach", "html": "1957 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Russian gymnast and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Russian gymnast and coach", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1958", "text": "<PERSON>, English-American author, activist, and academic", "html": "1958 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English-American author, activist, and academic", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English-American author, activist, and academic", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1958", "text": "<PERSON>, English screenwriter and producer", "html": "1958 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English screenwriter and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English screenwriter and producer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1958", "text": "<PERSON>, American fashion designer", "html": "1958 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American fashion designer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American fashion designer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1959", "text": "<PERSON><PERSON>, Indian actor, singer, and producer", "html": "1959 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Indian actor, singer, and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Indian actor, singer, and producer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1959", "text": "<PERSON><PERSON><PERSON>, Dutch blogger and illustrator", "html": "1959 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Dutch blogger and illustrator", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Dutch blogger and illustrator", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1959", "text": "<PERSON>, American baseball player and manager", "html": "1959 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player and manager", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player and manager", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1959", "text": "<PERSON>, English singer-songwriter and guitarist (d. 2025)", "html": "1959 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English singer-songwriter and guitarist (d. 2025)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English singer-songwriter and guitarist (d. 2025)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>"}]}, {"year": "1960", "text": "<PERSON><PERSON>, French author", "html": "1960 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, French author", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, French author", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1962", "text": "<PERSON>, English DJ and producer", "html": "1962 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English DJ and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English DJ and producer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1962", "text": "<PERSON>, German footballer and manager", "html": "1962 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German footballer and manager", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German footballer and manager", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1962", "text": "<PERSON>, Belgian runner", "html": "1962 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Belgian runner", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Belgian runner", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1962", "text": "<PERSON>, American wrestler", "html": "1962 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American wrestler", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American wrestler", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1963", "text": "<PERSON><PERSON><PERSON><PERSON><PERSON>, Belgian-German businessman", "html": "1963 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON><PERSON></a>, Belgian-German businessman", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON><PERSON></a>, Belgian-German businessman", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>-<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1963", "text": "<PERSON>, Irish footballer and sportscaster", "html": "1963 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Irish footballer and sportscaster", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Irish footballer and sportscaster", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1963", "text": "<PERSON>, English politician", "html": "1963 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English politician", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1963", "text": "<PERSON><PERSON><PERSON>, Pakistani cricketer", "html": "1963 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Pakistani cricketer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Pakistani cricketer", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1963", "text": "<PERSON>, American actress and producer", "html": "1963 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress and producer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1963", "text": "<PERSON>, English footballer, referee, and journalist", "html": "1963 - <a href=\"https://wikipedia.org/wiki/Graham_Poll\" title=\"Graham Poll\"><PERSON></a>, English footballer, referee, and journalist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Graham_Poll\" title=\"Graham Poll\"><PERSON></a>, English footballer, referee, and journalist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Graham_Poll"}]}, {"year": "1964", "text": "<PERSON><PERSON><PERSON>, Estonian footballer", "html": "1964 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Estonian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON> V<PERSON>\"><PERSON><PERSON><PERSON></a>, Estonian footballer", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>u"}]}, {"year": "1965", "text": "<PERSON>, Puerto Rican-American baseball player and coach", "html": "1965 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Puerto Rican-American baseball player and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Puerto Rican-American baseball player and coach", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1965", "text": "<PERSON>, Canadian actor, producer, and screenwriter", "html": "1965 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian actor, producer, and screenwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian actor, producer, and screenwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1965", "text": "<PERSON>, English captain and politician", "html": "1965 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English captain and politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English captain and politician", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1965", "text": "<PERSON>, American soccer player (d. 2014)", "html": "1965 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American soccer player (d. 2014)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American soccer player (d. 2014)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1965", "text": "<PERSON><PERSON><PERSON>, South Korean-American author and academic", "html": "1965 - <a href=\"https://wikipedia.org/wiki/<PERSON>-<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, South Korean-American author and academic", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, South Korean-American author and academic", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>-<PERSON>_<PERSON>"}]}, {"year": "1965", "text": "<PERSON>, Australian author", "html": "1965 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian author", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian author", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>n"}]}, {"year": "1965", "text": "<PERSON>, American guitarist and songwriter", "html": "1965 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON> Weatherman\"><PERSON></a>, American guitarist and songwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>man\"><PERSON></a>, American guitarist and songwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1966", "text": "<PERSON>, English hurdler and sportscaster", "html": "1966 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English hurdler and sportscaster", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English hurdler and sportscaster", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1966", "text": "<PERSON>, English cricketer", "html": "1966 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English cricketer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English cricketer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1966", "text": "<PERSON>, American singer-songwriter and producer", "html": "1966 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and producer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1968", "text": "<PERSON>, English geologist and academic", "html": "1968 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English geologist and academic", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English geologist and academic", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1968", "text": "<PERSON><PERSON><PERSON>, Finnish cellist and educator", "html": "1968 - <a href=\"https://wikipedia.org/wiki/Paavo_L%C3%B6tj%C3%B6nen\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Finnish cellist and educator", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Paavo_L%C3%B6tj%C3%B6nen\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Finnish cellist and educator", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Paavo_L%C3%B6tj%C3%B6nen"}]}, {"year": "1970", "text": "<PERSON>, American author", "html": "1970 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1970", "text": "<PERSON><PERSON>, English journalist, actor, and producer", "html": "1970 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, English journalist, actor, and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, English journalist, actor, and producer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1970", "text": "<PERSON>, Zimbabwean cricketer", "html": "1970 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>(cricketer)\" title=\"<PERSON> (cricketer)\"><PERSON></a>, Zimbabwean cricketer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>(cricketer)\" title=\"<PERSON> (cricketer)\"><PERSON></a>, Zimbabwean cricketer", "links": [{"title": "<PERSON> (cricketer)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(cricketer)"}]}, {"year": "1971", "text": "<PERSON>, German sprinter", "html": "1971 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German sprinter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German sprinter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1972", "text": "<PERSON><PERSON>, Finnish singer and songwriter", "html": "1972 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Finnish singer and songwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Finnish singer and songwriter", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1972", "text": "<PERSON><PERSON>, American actor, producer, and screenwriter", "html": "1972 - <a href=\"https://wikipedia.org/wiki/Wil_Wheaton\" title=\"Wil Wheaton\">W<PERSON></a>, American actor, producer, and screenwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Wil_Wheaton\" title=\"Wil Wheaton\">W<PERSON> <PERSON></a>, American actor, producer, and screenwriter", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Wil_Wheaton"}]}, {"year": "1973", "text": "<PERSON>, American actor and producer", "html": "1973 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor and producer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1973", "text": "<PERSON>, Kazakh mountaineer", "html": "1973 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Kazakh mountaineer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Kazakh mountaineer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1974", "text": "<PERSON>, American actor and musician", "html": "1974 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor and musician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor and musician", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1975", "text": "<PERSON><PERSON><PERSON>, Japanese mixed martial artist", "html": "1975 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese mixed martial artist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese mixed martial artist", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1975", "text": "<PERSON> <PERSON>, Sri Lankan cricketer", "html": "1975 - <a href=\"https://wikipedia.org/wiki/Lanka_<PERSON>_<PERSON>\" title=\"Lanka de Silva\">Lanka de Silva</a>, Sri Lankan cricketer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Lanka_de_<PERSON>\" title=\"Lanka de Silva\">Lanka de Silva</a>, Sri Lankan cricketer", "links": [{"title": "Lanka de Silva", "link": "https://wikipedia.org/wiki/Lanka_<PERSON>_<PERSON>"}]}, {"year": "1975", "text": "<PERSON><PERSON><PERSON>, Italian footballer", "html": "1975 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Italian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Italian footballer", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1975", "text": "<PERSON><PERSON><PERSON>, Estonian footballer", "html": "1975 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Estonian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Estonian footballer", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1978", "text": "<PERSON>, American baseball player", "html": "1978 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(pitcher)\" title=\"<PERSON> (pitcher)\"><PERSON></a>, American baseball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(pitcher)\" title=\"<PERSON> (pitcher)\"><PERSON></a>, American baseball player", "links": [{"title": "<PERSON> (pitcher)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(pitcher)"}]}, {"year": "1978", "text": "<PERSON>, Macedonian tennis player", "html": "1978 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Macedonian tennis player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Macedonian tennis player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Marina_Lazarovska"}]}, {"year": "1979", "text": "<PERSON><PERSON>, Tunisian footballer", "html": "1979 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Tunisian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Tunisian footballer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1979", "text": "<PERSON>, American basketball player", "html": "1979 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1979", "text": "<PERSON><PERSON>, Latvian basketball player", "html": "1979 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>%C5%A1ko\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Latvian basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>%C5%A1ko\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Latvian basketball player", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Juris_Umbra%C5%A1ko"}]}, {"year": "1980", "text": "<PERSON>, Canadian-American baseball player", "html": "1980 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(pitcher)\" title=\"<PERSON> (pitcher)\"><PERSON></a>, Canadian-American baseball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>(pitcher)\" title=\"<PERSON> (pitcher)\"><PERSON></a>, Canadian-American baseball player", "links": [{"title": "<PERSON> (pitcher)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(pitcher)"}]}, {"year": "1980", "text": "<PERSON>, Chilean tennis player", "html": "1980 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A1lez\" title=\"<PERSON>\"><PERSON></a>, Chilean tennis player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A1lez\" title=\"<PERSON>\"><PERSON></a>, Chilean tennis player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Fernando_Gonz%C3%A1lez"}]}, {"year": "1980", "text": "<PERSON>, American drummer", "html": "1980 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American drummer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American drummer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1980", "text": "<PERSON>, Australian rugby league player", "html": "1980 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(rugby_league)\" title=\"<PERSON> (rugby league)\"><PERSON></a>, Australian rugby league player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(rugby_league)\" title=\"<PERSON> (rugby league)\"><PERSON></a>, Australian rugby league player", "links": [{"title": "<PERSON> (rugby league)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(rugby_league)"}]}, {"year": "1981", "text": "<PERSON>, Spanish race car driver", "html": "1981 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Spanish race car driver", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Spanish race car driver", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1981", "text": "<PERSON>, Argentinian footballer", "html": "1981 - <a href=\"https://wikipedia.org/wiki/Andr%C3%A9s_Madrid\" title=\"Andrés Madrid\"><PERSON></a>, Argentinian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Andr%C3%A9s_Madrid\" title=\"Andrés Madrid\"><PERSON></a>, Argentinian footballer", "links": [{"title": "Andrés <PERSON>", "link": "https://wikipedia.org/wiki/Andr%C3%A9s_Madrid"}]}, {"year": "1981", "text": "<PERSON>, American soccer player", "html": "1981 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American soccer player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American soccer player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1982", "text": "<PERSON><PERSON>, Slovenian footballer", "html": "1982 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_Aljan%C4%8Di%C4%8D\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Slovenian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_Aljan%C4%8Di%C4%8D\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Slovenian footballer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>z_Aljan%C4%8Di%C4%8D"}]}, {"year": "1982", "text": "<PERSON><PERSON><PERSON><PERSON>, Brazilian footballer", "html": "1982 - <a href=\"https://wikipedia.org/wiki/J%C3%B4natas_Domingos\" class=\"mw-redirect\" title=\"Jônatas Domingos\"><PERSON><PERSON><PERSON><PERSON></a>, Brazilian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/J%C3%B4natas_Domingos\" class=\"mw-redirect\" title=\"<PERSON>ônat<PERSON> Domingos\"><PERSON><PERSON><PERSON><PERSON></a>, Brazilian footballer", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/J%C3%B4nat<PERSON>_Domingos"}]}, {"year": "1982", "text": "<PERSON>, American actress and criminal", "html": "1982 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress and criminal", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress and criminal", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1983", "text": "<PERSON>, Australian bowler", "html": "1983 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian bowler", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian bowler", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1983", "text": "<PERSON><PERSON>, Mexican journalist and actress", "html": "1983 - <a href=\"https://wikipedia.org/wiki/In%C3%A9s_G%C3%B3mez_Mont\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Mexican journalist and actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/In%C3%A9s_G%C3%B3mez_Mont\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Mexican journalist and actress", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/In%C3%A9s_G%C3%B3mez_Mont"}]}, {"year": "1983", "text": "<PERSON>, Russian ice hockey player", "html": "1983 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian ice hockey player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian ice hockey player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1983", "text": "<PERSON><PERSON>, American football player", "html": "1983 - <a href=\"https://wikipedia.org/wiki/Je<PERSON>_<PERSON>\" title=\"Je<PERSON>\"><PERSON><PERSON></a>, American football player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Je<PERSON>_<PERSON>\" title=\"Je<PERSON>\"><PERSON><PERSON></a>, American football player", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1983", "text": "<PERSON>, American singer-songwriter", "html": "1983 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1984", "text": "<PERSON>, South Korean footballer", "html": "1984 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>-seok\" title=\"Oh <PERSON><PERSON>-seok\"><PERSON> <PERSON><PERSON>-<PERSON></a>, South Korean footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>-seok\" title=\"Oh <PERSON><PERSON>-seok\"><PERSON> <PERSON></a>, South Korean footballer", "links": [{"title": "<PERSON> <PERSON><PERSON><PERSON>seok", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>-seok"}]}, {"year": "1984", "text": "<PERSON>, American baseball player", "html": "1984 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Chad_<PERSON>"}]}, {"year": "1984", "text": "<PERSON>, Honduran footballer", "html": "1984 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Honduran footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Honduran footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1985", "text": "<PERSON><PERSON><PERSON>, Japanese sumo wrestler", "html": "1985 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON> Ayumi\"><PERSON><PERSON><PERSON></a>, Japanese sumo wrestler", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON> Ayumi\"><PERSON><PERSON><PERSON></a>, Japanese sumo wrestler", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON><PERSON>mi"}]}, {"year": "1985", "text": "<PERSON><PERSON><PERSON>, Albanian footballer", "html": "1985 - <a href=\"https://wikipedia.org/wiki/Besart_Berisha\" title=\"<PERSON>sart Berisha\"><PERSON><PERSON><PERSON></a>, Albanian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Besar<PERSON>_Berisha\" title=\"<PERSON>sart Berisha\"><PERSON><PERSON><PERSON></a>, Albanian footballer", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>sar<PERSON>_<PERSON>"}]}, {"year": "1985", "text": "<PERSON>, Indonesian badminton player", "html": "1985 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Indonesian badminton player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Indonesian badminton player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1988", "text": "<PERSON><PERSON><PERSON><PERSON>, Norwegian biathlete", "html": "1988 - <a href=\"https://wikipedia.org/wiki/Tarjei_B%C3%B8\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Norwegian biathlete", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Tarjei_B%C3%B8\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Norwegian biathlete", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Tarjei_B%C3%B8"}]}, {"year": "1989", "text": "<PERSON><PERSON>, Estonian heptathlete", "html": "1989 - <a href=\"https://wikipedia.org/wiki/Grit_%C5%A0adeiko\" title=\"Grit <PERSON>\"><PERSON><PERSON></a>, Estonian heptathlete", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Grit_%C5%A0adeiko\" title=\"Grit <PERSON>\"><PERSON><PERSON></a>, Estonian heptathlete", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Grit_%C5%A0adeiko"}]}, {"year": "1990", "text": "<PERSON>, South Korean actress, singer and model", "html": "1990 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>-<PERSON>yung\" title=\"<PERSON> Se-kyung\"><PERSON></a>, South Korean actress, singer and model", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>-kyung\" title=\"<PERSON> Se-kyung\"><PERSON></a>, South Korean actress, singer and model", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1991", "text": "<PERSON>, Australian rugby league player", "html": "1991 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian rugby league player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian rugby league player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1991", "text": "<PERSON><PERSON><PERSON>, Russian footballer", "html": "1991 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Russian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Russian footballer", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1992", "text": "<PERSON>, Bolivian swimmer", "html": "1992 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Bolivian swimmer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Bolivian swimmer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1993", "text": "<PERSON>, American tennis player", "html": "1993 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, American tennis player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, American tennis player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1993", "text": "<PERSON><PERSON>, American football player", "html": "1993 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American football player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American football player", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1994", "text": "<PERSON>, Canadian ice hockey player", "html": "1994 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%27B<PERSON>_(ice_hockey)\" title=\"<PERSON> (ice hockey)\"><PERSON></a>, Canadian ice hockey player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%27B<PERSON>_(ice_hockey)\" title=\"<PERSON> (ice hockey)\"><PERSON></a>, Canadian ice hockey player", "links": [{"title": "<PERSON> (ice hockey)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>%27B<PERSON>_(ice_hockey)"}]}, {"year": "1998", "text": "<PERSON><PERSON><PERSON>, Swedish tennis player", "html": "1998 - <a href=\"https://wikipedia.org/wiki/Mirjam_Bj%C3%B6<PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Swedish tennis player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Mirjam_Bj%C3%B6<PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Swedish tennis player", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Mirjam_Bj%C3%B6<PERSON><PERSON>"}]}, {"year": "1998", "text": "<PERSON>, American ice hockey player", "html": "1998 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American ice hockey player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American ice hockey player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2003", "text": "<PERSON>, American long jumper and hurdler", "html": "2003 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American long jumper and hurdler", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American long jumper and hurdler", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}], "Deaths": [{"year": "238", "text": "<PERSON><PERSON><PERSON><PERSON>, Roman emperor (b. 165)", "html": "238 - <a href=\"https://wikipedia.org/wiki/Ba<PERSON>binus\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, <a href=\"https://wikipedia.org/wiki/Roman_emperor\" title=\"Roman emperor\">Roman emperor</a> (b. 165)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>us\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, <a href=\"https://wikipedia.org/wiki/Roman_emperor\" title=\"Roman emperor\">Roman emperor</a> (b. 165)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>us"}, {"title": "Roman emperor", "link": "https://wikipedia.org/wiki/Roman_emperor"}]}, {"year": "238", "text": "<PERSON><PERSON><PERSON><PERSON>, Roman emperor (b. 178)", "html": "238 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Roman emperor (b. 178)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Roman emperor (b. 178)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>upienus"}]}, {"year": "451", "text": "<PERSON><PERSON><PERSON>, prince of Northern Wei (b. 428)", "html": "451 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, prince of <a href=\"https://wikipedia.org/wiki/Northern_Wei\" title=\"Northern Wei\">Northern Wei</a> (b. 428)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, prince of <a href=\"https://wikipedia.org/wiki/Northern_Wei\" title=\"Northern Wei\">Northern Wei</a> (b. 428)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}, {"title": "Northern Wei", "link": "https://wikipedia.org/wiki/Northern_Wei"}]}, {"year": "796", "text": "<PERSON><PERSON> of Mercia (b. 730)", "html": "796 - <a href=\"https://wikipedia.org/wiki/Offa_of_Mercia\" title=\"Offa of Mercia\">Offa of Mercia</a> (b. 730)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Offa_of_Mercia\" title=\"Offa of Mercia\">Offa of Mercia</a> (b. 730)", "links": [{"title": "Offa of Mercia", "link": "https://wikipedia.org/wiki/Offa_of_Mercia"}]}, {"year": "846", "text": "<PERSON>, chancellor of the Tang Dynasty", "html": "846 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, <a href=\"https://wikipedia.org/wiki/Chancellor_of_the_Tang_dynasty\" title=\"Chancellor of the Tang dynasty\">chancellor of the Tang Dynasty</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Shen\"><PERSON></a>, <a href=\"https://wikipedia.org/wiki/Chancellor_of_the_Tang_dynasty\" title=\"Chancellor of the Tang dynasty\">chancellor of the Tang Dynasty</a>", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Chancellor of the Tang dynasty", "link": "https://wikipedia.org/wiki/Chancellor_of_the_Tang_dynasty"}]}, {"year": "1030", "text": "<PERSON> of Norway (b. 995)", "html": "1030 - <a href=\"https://wikipedia.org/wiki/<PERSON>_II_of_Norway\" class=\"mw-redirect\" title=\"<PERSON> II of Norway\"><PERSON> of Norway</a> (b. 995)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_II_of_Norway\" class=\"mw-redirect\" title=\"<PERSON> II of Norway\"><PERSON> of Norway</a> (b. 995)", "links": [{"title": "<PERSON> of Norway", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Norway"}]}, {"year": "1095", "text": "<PERSON><PERSON><PERSON> of Hungary (b. 1040)", "html": "1095 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_I_of_Hungary\" title=\"La<PERSON>laus I of Hungary\"><PERSON><PERSON><PERSON> of Hungary</a> (b. 1040)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_I_of_Hungary\" title=\"La<PERSON>laus I of Hungary\"><PERSON><PERSON><PERSON> of Hungary</a> (b. 1040)", "links": [{"title": "<PERSON><PERSON><PERSON> I of Hungary", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_I_of_Hungary"}]}, {"year": "1099", "text": "<PERSON> (b. 1042)", "html": "1099 - <a href=\"https://wikipedia.org/wiki/Pope_Urban_II\" title=\"Pope Urban II\"><PERSON> Urban II</a> (b. 1042)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Pope_Urban_II\" title=\"Pope Urban II\"><PERSON> II</a> (b. 1042)", "links": [{"title": "<PERSON> II", "link": "https://wikipedia.org/wiki/Pope_Urban_II"}]}, {"year": "1108", "text": "<PERSON> of France (b. 1052)", "html": "1108 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_France\" title=\"<PERSON> of France\"><PERSON> of France</a> (b. 1052)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_France\" title=\"<PERSON> of France\"><PERSON> of France</a> (b. 1052)", "links": [{"title": "<PERSON> of France", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_France"}]}, {"year": "1236", "text": "<PERSON><PERSON><PERSON> of Denmark, Queen of France (b. 1175)", "html": "1236 - <a href=\"https://wikipedia.org/wiki/Ingeborg_of_Denmark,_Queen_of_France\" title=\"<PERSON><PERSON><PERSON> of Denmark, Queen of France\"><PERSON><PERSON><PERSON> of Denmark, Queen of France</a> (b. 1175)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Ingeborg_of_Denmark,_Queen_of_France\" title=\"<PERSON><PERSON><PERSON> of Denmark, Queen of France\"><PERSON><PERSON><PERSON> of Denmark, Queen of France</a> (b. 1175)", "links": [{"title": "<PERSON><PERSON><PERSON> of Denmark, Queen of France", "link": "https://wikipedia.org/wiki/Ingeborg_of_Denmark,_Queen_of_France"}]}, {"year": "1326", "text": "<PERSON>, 2nd Earl of Ulster (b. 1259)", "html": "1326 - <a href=\"https://wikipedia.org/wiki/Richard_%C3%93g_<PERSON>_<PERSON>,_2nd_Earl_of_Ulster\" title=\"<PERSON>, 2nd Earl of Ulster\"><PERSON>, 2nd Earl of Ulster</a> (b. 1259)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Richard_%C3%93g_<PERSON>_<PERSON>,_2nd_Earl_of_Ulster\" title=\"<PERSON>, 2nd Earl of Ulster\"><PERSON>, 2nd Earl of Ulster</a> (b. 1259)", "links": [{"title": "<PERSON>, 2nd Earl of Ulster", "link": "https://wikipedia.org/wiki/<PERSON>_%C3%93g_<PERSON>_<PERSON>h,_2nd_Earl_of_Ulster"}]}, {"year": "1504", "text": "<PERSON>, 1st Earl of Derby (b. 1435)", "html": "1504 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_1st_Earl_of_Derby\" title=\"<PERSON>, 1st Earl of Derby\"><PERSON>, 1st Earl of Derby</a> (b. 1435)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_1st_Earl_of_Derby\" title=\"<PERSON>, 1st Earl of Derby\"><PERSON>, 1st Earl of Derby</a> (b. 1435)", "links": [{"title": "<PERSON>, 1st Earl of Derby", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_1st_Earl_of_Derby"}]}, {"year": "1507", "text": "<PERSON>, German-Bohemian geographer and astronomer (b. 1459)", "html": "1507 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German-Bohemian geographer and astronomer (b. 1459)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German-Bohemian geographer and astronomer (b. 1459)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1573", "text": "<PERSON>, English physician and academic (b. 1510)", "html": "1573 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English physician and academic (b. 1510)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English physician and academic (b. 1510)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1612", "text": "<PERSON>, French scholar and diplomat (b. 1554)", "html": "1612 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French scholar and diplomat (b. 1554)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French scholar and diplomat (b. 1554)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1644", "text": "<PERSON> (b. 1568)", "html": "1644 - <a href=\"https://wikipedia.org/wiki/Pope_Urban_VIII\" title=\"Pope Urban VIII\">Pope Urban VIII</a> (b. 1568)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Pope_Urban_VIII\" title=\"Pope Urban VIII\"><PERSON></a> (b. 1568)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_Urban_VIII"}]}, {"year": "1752", "text": "<PERSON>, Irish admiral and politician (b. 1703)", "html": "1752 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(Royal_Navy_officer)\" title=\"<PERSON> (Royal Navy officer)\"><PERSON></a>, Irish admiral and politician (b. 1703)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(Royal_Navy_officer)\" title=\"<PERSON> (Royal Navy officer)\"><PERSON></a>, Irish admiral and politician (b. 1703)", "links": [{"title": "<PERSON> (Royal Navy officer)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(Royal_Navy_officer)"}]}, {"year": "1781", "text": "<PERSON>, German astronomer and mathematician (b. 1713)", "html": "1781 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German astronomer and mathematician (b. 1713)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German astronomer and mathematician (b. 1713)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1792", "text": "<PERSON>, French lawyer and politician, Chancellor of France (b. 1714)", "html": "1792 - <a href=\"https://wikipedia.org/wiki/Ren%C3%A9_<PERSON>_<PERSON>_August<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French lawyer and politician, <a href=\"https://wikipedia.org/wiki/Chancellor_of_France\" title=\"Chancellor of France\">Chancellor of France</a> (b. 1714)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Ren%C3%A9_<PERSON>_<PERSON>_<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French lawyer and politician, <a href=\"https://wikipedia.org/wiki/Chancellor_of_France\" title=\"Chancellor of France\">Chancellor of France</a> (b. 1714)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Ren%C3%A9_<PERSON>_<PERSON>_<PERSON>_<PERSON>_<PERSON>"}, {"title": "Chancellor of France", "link": "https://wikipedia.org/wiki/Chancellor_of_France"}]}, {"year": "1813", "text": "<PERSON><PERSON><PERSON><PERSON><PERSON>, French general (b. 1771)", "html": "1813 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON><PERSON></a>, French general (b. 1771)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON><PERSON></a>, French general (b. 1771)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>-<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1833", "text": "<PERSON>, English philanthropist and politician (b. 1759)", "html": "1833 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English philanthropist and politician (b. 1759)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English philanthropist and politician (b. 1759)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1839", "text": "<PERSON><PERSON>, French mathematician and engineer (b. 1755)", "html": "1839 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_de_Prony\" title=\"<PERSON>pard de Prony\"><PERSON><PERSON></a>, French mathematician and engineer (b. 1755)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_de_<PERSON>ny\" title=\"<PERSON>pard de Prony\"><PERSON><PERSON></a>, French mathematician and engineer (b. 1755)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_de_Prony"}]}, {"year": "1844", "text": "<PERSON>, Austrian pianist, composer, and conductor (b. 1791)", "html": "1844 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_Wolfgang_Mozart\" title=\"Franz Xaver Wolfgang Mozart\"><PERSON><PERSON></a>, Austrian pianist, composer, and conductor (b. 1791)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"Franz Xaver Wolfgang Mozart\"><PERSON></a>, Austrian pianist, composer, and conductor (b. 1791)", "links": [{"title": "Franz <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1856", "text": "<PERSON>, German composer and critic (b. 1810)", "html": "1856 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German composer and critic (b. 1810)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German composer and critic (b. 1810)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1857", "text": "<PERSON>, Scottish minister, astronomer, and author (b. 1774)", "html": "1857 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(scientist)\" title=\"<PERSON> (scientist)\"><PERSON></a>, Scottish minister, astronomer, and author (b. 1774)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(scientist)\" title=\"<PERSON> (scientist)\"><PERSON></a>, Scottish minister, astronomer, and author (b. 1774)", "links": [{"title": "<PERSON> (scientist)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(scientist)"}]}, {"year": "1887", "text": "<PERSON><PERSON><PERSON>, Italian politician, 9th Prime Minister of Italy (b. 1813)", "html": "1887 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_Depretis\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Italian politician, 9th <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Italy\" title=\"Prime Minister of Italy\">Prime Minister of Italy</a> (b. 1813)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_Depret<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Italian politician, 9th <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Italy\" title=\"Prime Minister of Italy\">Prime Minister of Italy</a> (b. 1813)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Agos<PERSON>_Depretis"}, {"title": "Prime Minister of Italy", "link": "https://wikipedia.org/wiki/Prime_Minister_of_Italy"}]}, {"year": "1890", "text": "<PERSON>, Dutch painter and illustrator (b. 1853)", "html": "1890 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch painter and illustrator (b. 1853)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch painter and illustrator (b. 1853)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1895", "text": "<PERSON><PERSON><PERSON>, Brazilian general and politician, 2nd President of Brazil (b. 1839)", "html": "1895 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Brazilian general and politician, 2nd <a href=\"https://wikipedia.org/wiki/President_of_Brazil\" title=\"President of Brazil\">President of Brazil</a> (b. 1839)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Brazilian general and politician, 2nd <a href=\"https://wikipedia.org/wiki/President_of_Brazil\" title=\"President of Brazil\">President of Brazil</a> (b. 1839)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}, {"title": "President of Brazil", "link": "https://wikipedia.org/wiki/President_of_Brazil"}]}, {"year": "1900", "text": "<PERSON><PERSON> of Italy (b. 1844)", "html": "1900 - <a href=\"https://wikipedia.org/wiki/Umberto_I_of_Italy\" title=\"Umberto I of Italy\"><PERSON><PERSON> of Italy</a> (b. 1844)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Umberto_I_of_Italy\" title=\"Umberto I of Italy\"><PERSON><PERSON> of Italy</a> (b. 1844)", "links": [{"title": "<PERSON><PERSON> of Italy", "link": "https://wikipedia.org/wiki/Umberto_I_of_Italy"}]}, {"year": "1908", "text": "<PERSON>, Swiss women's rights activist and unionist (b. 1838)", "html": "1908 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swiss women's rights activist and unionist (b. 1838)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swiss women's rights activist and unionist (b. 1838)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1913", "text": "<PERSON>, Dutch lawyer and jurist, Nobel Prize Laureate (b. 1838)", "html": "1913 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch lawyer and jurist, <a href=\"https://wikipedia.org/wiki/Nobel_Peace_Prize\" title=\"Nobel Peace Prize\">Nobel Prize</a> Lau<PERSON> (b. 1838)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch lawyer and jurist, <a href=\"https://wikipedia.org/wiki/Nobel_Peace_Prize\" title=\"Nobel Peace Prize\">Nobel Prize</a> Laureate (b. 1838)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Nobel Peace Prize", "link": "https://wikipedia.org/wiki/Nobel_Peace_Prize"}]}, {"year": "1918", "text": "<PERSON>, Australian-American painter (b. 1863)", "html": "1918 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian-American painter (b. 1863)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian-American painter (b. 1863)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1924", "text": "<PERSON><PERSON><PERSON><PERSON>, Greek educator and politician, 110th Prime Minister of Greece (b. 1852)", "html": "1924 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Greek educator and politician, 110th <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Greece\" title=\"Prime Minister of Greece\">Prime Minister of Greece</a> (b. 1852)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Greek educator and politician, 110th <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Greece\" title=\"Prime Minister of Greece\">Prime Minister of Greece</a> (b. 1852)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>"}, {"title": "Prime Minister of Greece", "link": "https://wikipedia.org/wiki/Prime_Minister_of_Greece"}]}, {"year": "1934", "text": "<PERSON><PERSON>, Canadian ice hockey player (b. 1883)", "html": "1934 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> Pit<PERSON>\"><PERSON><PERSON></a>, Canadian ice hockey player (b. 1883)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Canadian ice hockey player (b. 1883)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>re"}]}, {"year": "1938", "text": "<PERSON>, Russian lawyer, jurist, and politician, Prosecutor General of the Russian SFSR (b. 1885)", "html": "1938 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian lawyer, jurist, and politician, <a href=\"https://wikipedia.org/wiki/List_of_Prosecutor_Generals_of_Russia_and_the_Soviet_Union\" title=\"List of Prosecutor Generals of Russia and the Soviet Union\">Prosecutor General of the Russian SFSR</a> (b. 1885)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian lawyer, jurist, and politician, <a href=\"https://wikipedia.org/wiki/List_of_Prosecutor_Generals_of_Russia_and_the_Soviet_Union\" title=\"List of Prosecutor Generals of Russia and the Soviet Union\">Prosecutor General of the Russian SFSR</a> (b. 1885)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "List of Prosecutor Generals of Russia and the Soviet Union", "link": "https://wikipedia.org/wiki/List_of_Prosecutor_Generals_of_Russia_and_the_Soviet_Union"}]}, {"year": "1950", "text": "<PERSON>, English race car driver (b. 1915)", "html": "1950 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English race car driver (b. 1915)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English race car driver (b. 1915)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1951", "text": "<PERSON>, Turkish footballer and manager, founded Galatasaray S.K. (b. 1886)", "html": "1951 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Turkish footballer and manager, founded <a href=\"https://wikipedia.org/wiki/Galatasaray_S.K.\" title=\"Galatasaray S.K.\">Galatasaray S.K.</a> (b. 1886)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Turkish footballer and manager, founded <a href=\"https://wikipedia.org/wiki/Galatasaray_S.K.\" title=\"Galatasaray S.K.\">Galatasaray S.K.</a> (b. 1886)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>n"}, {"title": "Galatasaray S.K.", "link": "https://wikipedia.org/wiki/Galatasaray_S.K."}]}, {"year": "1954", "text": "<PERSON><PERSON>, Dutch speed skater (b. 1879)", "html": "1954 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_de_<PERSON>\" title=\"<PERSON><PERSON> de <PERSON>\"><PERSON><PERSON></a>, Dutch speed skater (b. 1879)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_de_<PERSON>\" title=\"<PERSON><PERSON> de <PERSON>\"><PERSON><PERSON></a>, Dutch speed skater (b. 1879)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>ning"}]}, {"year": "1960", "text": "<PERSON>, Turkish politician, 7th Prime Minister of Turkey (b. 1885)", "html": "1960 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Turkish politician, 7th <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Turkey\" title=\"Prime Minister of Turkey\">Prime Minister of Turkey</a> (b. 1885)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Turkish politician, 7th <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Turkey\" title=\"Prime Minister of Turkey\">Prime Minister of Turkey</a> (b. 1885)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Prime Minister of Turkey", "link": "https://wikipedia.org/wiki/Prime_Minister_of_Turkey"}]}, {"year": "1962", "text": "<PERSON>, English biologist and statistician (b. 1890)", "html": "1962 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English biologist and statistician (b. 1890)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English biologist and statistician (b. 1890)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1962", "text": "<PERSON>, Italian-American flute player and educator (b. 1875)", "html": "1962 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian-American flute player and educator (b. 1875)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian-American flute player and educator (b. 1875)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1964", "text": "<PERSON><PERSON>, American baseball player (b. 1885)", "html": "1964 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American baseball player (b. 1885)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American baseball player (b. 1885)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1966", "text": "<PERSON><PERSON><PERSON>, Nigerian general and politician, 2nd Head of State of Nigeria (b. 1924)", "html": "1966 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Nigerian general and politician, 2nd <a href=\"https://wikipedia.org/wiki/List_of_heads_of_state_of_Nigeria\" title=\"List of heads of state of Nigeria\">Head of State of Nigeria</a> (b. 1924)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Nigerian general and politician, 2nd <a href=\"https://wikipedia.org/wiki/List_of_heads_of_state_of_Nigeria\" title=\"List of heads of state of Nigeria\">Head of State of Nigeria</a> (b. 1924)", "links": [{"title": "<PERSON>-<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>-<PERSON>"}, {"title": "List of heads of state of Nigeria", "link": "https://wikipedia.org/wiki/List_of_heads_of_state_of_Nigeria"}]}, {"year": "1966", "text": "<PERSON><PERSON><PERSON><PERSON>, Nigerian colonel (b. 1926)", "html": "1966 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON> Fajuyi\"><PERSON><PERSON><PERSON><PERSON></a>, Nigerian colonel (b. 1926)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON> Fajuyi\"><PERSON><PERSON><PERSON><PERSON></a>, Nigerian colonel (b. 1926)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Adek<PERSON><PERSON>_<PERSON>ajuyi"}]}, {"year": "1970", "text": "<PERSON>, English cellist and conductor (b. 1899)", "html": "1970 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English cellist and conductor (b. 1899)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English cellist and conductor (b. 1899)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1973", "text": "<PERSON><PERSON>, Australian footballer and coach (b. 1915)", "html": "1973 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Australian footballer and coach (b. 1915)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Australian footballer and coach (b. 1915)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1973", "text": "<PERSON>, English race car driver (b. 1948)", "html": "1973 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English race car driver (b. 1948)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English race car driver (b. 1948)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1974", "text": "<PERSON>, American singer (b. 1941)", "html": "1974 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer (b. 1941)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer (b. 1941)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1974", "text": "<PERSON>, German author and poet (b. 1899)", "html": "1974 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A4stner\" title=\"<PERSON>\"><PERSON></a>, German author and poet (b. 1899)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A4stner\" title=\"<PERSON>\"><PERSON></a>, German author and poet (b. 1899)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_K%C3%A4stner"}]}, {"year": "1976", "text": "<PERSON>, American gangster (b. 1913)", "html": "1976 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American gangster (b. 1913)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American gangster (b. 1913)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1978", "text": "<PERSON><PERSON><PERSON>, Polish actor, operetta singer, and songwriter (b. 1904)", "html": "1978 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Polish actor, operetta singer, and songwriter (b. 1904)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Polish actor, operetta singer, and songwriter (b. 1904)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1979", "text": "<PERSON>, German sociologist and philosopher (b. 1898)", "html": "1979 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German sociologist and philosopher (b. 1898)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German sociologist and philosopher (b. 1898)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1979", "text": "<PERSON>, American screenwriter and producer (b. 1916)", "html": "1979 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American screenwriter and producer (b. 1916)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American screenwriter and producer (b. 1916)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1981", "text": "<PERSON>, American urban planner, designed the Northern State Parkway and Southern State Parkway (b. 1888)", "html": "1981 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American urban planner, designed the <a href=\"https://wikipedia.org/wiki/Northern_State_Parkway\" title=\"Northern State Parkway\">Northern State Parkway</a> and <a href=\"https://wikipedia.org/wiki/Southern_State_Parkway\" title=\"Southern State Parkway\">Southern State Parkway</a> (b. 1888)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American urban planner, designed the <a href=\"https://wikipedia.org/wiki/Northern_State_Parkway\" title=\"Northern State Parkway\">Northern State Parkway</a> and <a href=\"https://wikipedia.org/wiki/Southern_State_Parkway\" title=\"Southern State Parkway\">Southern State Parkway</a> (b. 1888)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Northern State Parkway", "link": "https://wikipedia.org/wiki/Northern_State_Parkway"}, {"title": "Southern State Parkway", "link": "https://wikipedia.org/wiki/Southern_State_Parkway"}]}, {"year": "1981", "text": "<PERSON>, British bandleader (b. 1896)", "html": "1981 - <a href=\"https://wikipedia.org/wiki/Sydney_Kyte\" title=\"Sydney Kyte\"><PERSON></a>, British bandleader (b. 1896)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Sydney_Kyte\" title=\"Sydney Kyte\"><PERSON></a>, British bandleader (b. 1896)", "links": [{"title": "Sydney Kyte", "link": "https://wikipedia.org/wiki/Sydney_Kyte"}]}, {"year": "1982", "text": "<PERSON>, American wrestler and actor (b. 1920)", "html": "1982 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American wrestler and actor (b. 1920)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American wrestler and actor (b. 1920)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1982", "text": "<PERSON>, Russian-American engineer, invented the Iconoscope (b. 1889)", "html": "1982 - <a href=\"https://wikipedia.org/wiki/Vladimir_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian-American engineer, invented the <a href=\"https://wikipedia.org/wiki/Iconoscope\" title=\"Iconoscope\">Iconoscope</a> (b. 1889)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Vladimir_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian-American engineer, invented the <a href=\"https://wikipedia.org/wiki/Iconoscope\" title=\"Iconoscope\">Iconoscope</a> (b. 1889)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Vladimir_<PERSON>_<PERSON>"}, {"title": "Iconoscope", "link": "https://wikipedia.org/wiki/Iconoscope"}]}, {"year": "1983", "text": "<PERSON>, Spanish actor, director, and screenwriter (b. 1900)", "html": "1983 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>u%C3%B1uel\" title=\"<PERSON> B<PERSON>\"><PERSON></a>, Spanish actor, director, and screenwriter (b. 1900)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>u%C3%B1uel\" title=\"<PERSON>\"><PERSON></a>, Spanish actor, director, and screenwriter (b. 1900)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Luis_Bu%C3%B1uel"}]}, {"year": "1983", "text": "<PERSON>, Canadian-American actor and screenwriter (b. 1896)", "html": "1983 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian-American actor and screenwriter (b. 1896)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian-American actor and screenwriter (b. 1896)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1983", "text": "<PERSON>, English military officer and actor (b. 1910)", "html": "1983 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English military officer and actor (b. 1910)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English military officer and actor (b. 1910)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1984", "text": "<PERSON>, American television host and bandleader (b. 1900)", "html": "1984 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American television host and bandleader (b. 1900)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American television host and bandleader (b. 1900)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1987", "text": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, Indian author, poet, and playwright (b. 1894)", "html": "1987 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON></a>, Indian author, poet, and playwright (b. 1894)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>adhya<PERSON>\"><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON></a>, Indian author, poet, and playwright (b. 1894)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>_<PERSON><PERSON><PERSON>"}]}, {"year": "1990", "text": "<PERSON>, Austrian academic and politician, 22nd Chancellor of Austria (b. 1911)", "html": "1990 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Austrian academic and politician, 22nd <a href=\"https://wikipedia.org/wiki/Chancellor_of_Austria\" title=\"Chancellor of Austria\">Chancellor of Austria</a> (b. 1911)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Austrian academic and politician, 22nd <a href=\"https://wikipedia.org/wiki/Chancellor_of_Austria\" title=\"Chancellor of Austria\">Chancellor of Austria</a> (b. 1911)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}, {"title": "Chancellor of Austria", "link": "https://wikipedia.org/wiki/Chancellor_of_Austria"}]}, {"year": "1991", "text": "<PERSON>, French general (b. 1902)", "html": "1991 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"Christian de Castries\"><PERSON></a>, French general (b. 1902)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Christian_<PERSON>_<PERSON>\" title=\"<PERSON> Cast<PERSON>\"><PERSON></a>, French general (b. 1902)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_de_<PERSON>"}]}, {"year": "1992", "text": "<PERSON>, Canadian ice hockey player and manager (b. 1952)", "html": "1992 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player and manager (b. 1952)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player and manager (b. 1952)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1994", "text": "<PERSON>, American physician (b. 1925)", "html": "1994 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(doctor)\" title=\"<PERSON> (doctor)\"><PERSON></a>, American physician (b. 1925)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(doctor)\" title=\"<PERSON> (doctor)\"><PERSON></a>, American physician (b. 1925)", "links": [{"title": "<PERSON> (doctor)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(doctor)"}]}, {"year": "1994", "text": "<PERSON>, Egyptian-English biochemist and biophysicist, Nobel Prize laureate (b. 1910)", "html": "1994 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Egyptian-English biochemist and biophysicist, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Chemistry\" title=\"Nobel Prize in Chemistry\">Nobel Prize</a> laureate (b. 1910)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Egyptian-English biochemist and biophysicist, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Chemistry\" title=\"Nobel Prize in Chemistry\">Nobel Prize</a> laureate (b. 1910)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Nobel Prize in Chemistry", "link": "https://wikipedia.org/wiki/Nobel_Prize_in_Chemistry"}]}, {"year": "1995", "text": "<PERSON>, American trumpet player and bandleader (b. 1917)", "html": "1995 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American trumpet player and bandleader (b. 1917)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American trumpet player and bandleader (b. 1917)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1996", "text": "<PERSON><PERSON>, Canadian businessman and politician (b. 1919)", "html": "1996 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Canadian businessman and politician (b. 1919)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Canadian businessman and politician (b. 1919)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1996", "text": "<PERSON><PERSON><PERSON>, French mathematician and theorist (b. 1920)", "html": "1996 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>%C3%<PERSON><PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, French mathematician and theorist (b. 1920)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>%C3%<PERSON><PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, French mathematician and theorist (b. 1920)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>%C3%<PERSON><PERSON><PERSON><PERSON>"}]}, {"year": "1996", "text": "<PERSON>, American singer and bass player (b. 1967)", "html": "1996 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer and bass player (b. 1967)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer and bass player (b. 1967)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1998", "text": "<PERSON>, American director, producer, and choreographer (b. 1918)", "html": "1998 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American director, producer, and choreographer (b. 1918)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American director, producer, and choreographer (b. 1918)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2001", "text": "<PERSON>, Polish soldier and politician (b. 1913)", "html": "2001 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Polish soldier and politician (b. 1913)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Polish soldier and politician (b. 1913)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2001", "text": "<PERSON><PERSON>, German computer scientist, co-founded Chaos Computer Club (b. 1951)", "html": "2001 - <a href=\"https://wikipedia.org/wiki/Wau_Holland\" title=\"Wau Holland\"><PERSON><PERSON> Holland</a>, German computer scientist, co-founded <a href=\"https://wikipedia.org/wiki/Chaos_Computer_Club\" title=\"Chaos Computer Club\">Chaos Computer Club</a> (b. 1951)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Wau_Holland\" title=\"Wau Holland\">Wau Holland</a>, German computer scientist, co-founded <a href=\"https://wikipedia.org/wiki/Chaos_Computer_Club\" title=\"Chaos Computer Club\">Chaos Computer Club</a> (b. 1951)", "links": [{"title": "Wau Holland", "link": "https://wikipedia.org/wiki/Wau_Holland"}, {"title": "Chaos Computer Club", "link": "https://wikipedia.org/wiki/Chaos_Computer_Club"}]}, {"year": "2003", "text": "<PERSON><PERSON><PERSON>, Sierra Leonean soldier, founded the Revolutionary United Front (b. 1937)", "html": "2003 - <a href=\"https://wikipedia.org/wiki/Foday_Sankoh\" title=\"Foday Sankoh\"><PERSON><PERSON><PERSON></a>, Sierra Leonean soldier, founded the <a href=\"https://wikipedia.org/wiki/Revolutionary_United_Front\" title=\"Revolutionary United Front\">Revolutionary United Front</a> (b. 1937)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Foday_Sankoh\" title=\"Foday Sankoh\"><PERSON><PERSON><PERSON></a>, Sierra Leonean soldier, founded the <a href=\"https://wikipedia.org/wiki/Revolutionary_United_Front\" title=\"Revolutionary United Front\">Revolutionary United Front</a> (b. 1937)", "links": [{"title": "Foday Sankoh", "link": "https://wikipedia.org/wiki/Foday_Sankoh"}, {"title": "Revolutionary United Front", "link": "https://wikipedia.org/wiki/Revolutionary_United_Front"}]}, {"year": "2004", "text": "<PERSON><PERSON>, Greek actress and singer (b. 1923)", "html": "2004 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Greek actress and singer (b. 1923)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Greek actress and singer (b. 1923)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "2007", "text": "<PERSON>, English comedian, actor, and author (b. 1940)", "html": "2007 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(actor)\" title=\"<PERSON> (actor)\"><PERSON></a>, English comedian, actor, and author (b. 1940)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>(actor)\" title=\"<PERSON> (actor)\"><PERSON></a>, English comedian, actor, and author (b. 1940)", "links": [{"title": "<PERSON> (actor)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>(actor)"}]}, {"year": "2007", "text": "<PERSON>, French actor (b. 1928)", "html": "2007 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French actor (b. 1928)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French actor (b. 1928)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "2007", "text": "<PERSON>, American journalist and talk show host (b. 1936)", "html": "2007 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American journalist and talk show host (b. 1936)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American journalist and talk show host (b. 1936)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2007", "text": "<PERSON>, American journalist (b. 1921)", "html": "2007 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American journalist (b. 1921)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American journalist (b. 1921)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2008", "text": "<PERSON>, American scientist and bio-defense researcher (b. 1946)", "html": "2008 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, American scientist and bio-defense researcher (b. 1946)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, American scientist and bio-defense researcher (b. 1946)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "2010", "text": "<PERSON>, American chemist and academic (b. 1925)", "html": "2010 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_Wicks\" title=\"<PERSON>\"><PERSON></a>, American chemist and academic (b. 1925)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American chemist and academic (b. 1925)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_Wicks"}]}, {"year": "2012", "text": "<PERSON>, Russian footballer and manager (b. 1970)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian footballer and manager (b. 1970)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian footballer and manager (b. 1970)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2012", "text": "<PERSON>, Polish actor and director (b. 1921)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/August_<PERSON>\" title=\"August <PERSON>\">August <PERSON></a>, Polish actor and director (b. 1921)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/August_<PERSON>\" title=\"August <PERSON>\"><PERSON></a>, Polish actor and director (b. 1921)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/August_<PERSON><PERSON><PERSON>"}]}, {"year": "2012", "text": "<PERSON>, French photographer and journalist (b. 1921)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French photographer and journalist (b. 1921)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French photographer and journalist (b. 1921)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2012", "text": "<PERSON>, English archaeologist and author (b. 1925)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English archaeologist and author (b. 1925)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English archaeologist and author (b. 1925)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2012", "text": "<PERSON>, Danish footballer and coach (b. 1957)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Danish footballer and coach (b. 1957)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Danish footballer and coach (b. 1957)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2013", "text": "<PERSON>, Ecuadorian footballer (b. 1986)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/Christian_Ben%C3%ADtez\" title=\"<PERSON>\"><PERSON></a>, Ecuadorian footballer (b. 1986)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Christian_Ben%C3%ADtez\" title=\"<PERSON>\"><PERSON></a>, Ecuadorian footballer (b. 1986)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Christian_Ben%C3%ADtez"}]}, {"year": "2013", "text": "<PERSON>, American banker and civil servant (b. 1923)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American banker and civil servant (b. 1923)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American banker and civil servant (b. 1923)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2013", "text": "<PERSON>, Australian soldier, pilot, and race car driver (b. 1920)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian soldier, pilot, and race car driver (b. 1920)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian soldier, pilot, and race car driver (b. 1920)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2013", "text": "<PERSON><PERSON>, Indian cricketer and sportscaster (b. 1929)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_(commentator)\" title=\"<PERSON><PERSON> (commentator)\"><PERSON><PERSON></a>, Indian cricketer and sportscaster (b. 1929)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_(commentator)\" title=\"<PERSON><PERSON> (commentator)\"><PERSON><PERSON></a>, Indian cricketer and sportscaster (b. 1929)", "links": [{"title": "<PERSON><PERSON> (commentator)", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_(commentator)"}]}, {"year": "2014", "text": "<PERSON><PERSON>, American soldier, lawyer, and politician (b. 1925)", "html": "2014 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American soldier, lawyer, and politician (b. 1925)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON> <PERSON></a>, American soldier, lawyer, and politician (b. 1925)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>"}]}, {"year": "2014", "text": "<PERSON>, English-American sergeant, Medal of Honor recipient (b. 1943)", "html": "2014 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English-American sergeant, <a href=\"https://wikipedia.org/wiki/Medal_of_Honor\" title=\"Medal of Honor\">Medal of Honor</a> recipient (b. 1943)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English-American sergeant, <a href=\"https://wikipedia.org/wiki/Medal_of_Honor\" title=\"Medal of Honor\">Medal of Honor</a> recipient (b. 1943)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>"}, {"title": "Medal of Honor", "link": "https://wikipedia.org/wiki/Medal_of_Honor"}]}, {"year": "2014", "text": "<PERSON>, Italian pianist and composer (b. 1929)", "html": "2014 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian pianist and composer (b. 1929)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian pianist and composer (b. 1929)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2014", "text": "<PERSON>, Spanish journalist and author (b. 1945)", "html": "2014 - <a href=\"https://wikipedia.org/wiki/Mar%C3%ADa_Antonia_Iglesias\" title=\"<PERSON>\"><PERSON></a>, Spanish journalist and author (b. 1945)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Mar%C3%ADa_Antonia_Iglesias\" title=\"<PERSON>\"><PERSON></a>, Spanish journalist and author (b. 1945)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Mar%C3%ADa_Antonia_Iglesias"}]}, {"year": "2014", "text": "<PERSON><PERSON><PERSON>, Hungarian engineer and politician (b. 1959)", "html": "2014 - <a href=\"https://wikipedia.org/wiki/P%C3%A9ter_Kiss\" title=\"Péter Kiss\"><PERSON><PERSON><PERSON></a>, Hungarian engineer and politician (b. 1959)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/P%C3%A9ter_Kiss\" title=\"Péter Kiss\"><PERSON><PERSON><PERSON></a>, Hungarian engineer and politician (b. 1959)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/P%C3%A9ter_Kiss"}]}, {"year": "2014", "text": "<PERSON><PERSON><PERSON>, American drummer and composer (b. 1939)", "html": "2014 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American drummer and composer (b. 1939)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American drummer and composer (b. 1939)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "2014", "text": "<PERSON>, American soldier and author (b. 1919)", "html": "2014 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>._St._George\" title=\"Thomas R. St. George\">Thomas R. St. George</a>, American soldier and author (b. 1919)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Thomas_<PERSON>._St._George\" title=\"Thomas R. St. George\">Thomas R. St. George</a>, American soldier and author (b. 1919)", "links": [{"title": "Thomas R. St. George", "link": "https://wikipedia.org/wiki/Thomas_<PERSON>._St._George"}]}, {"year": "2015", "text": "<PERSON>, English-Canadian actor, director, and playwright (b. 1920)", "html": "2015 - <a href=\"https://wikipedia.org/wiki/Antony_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English-Canadian actor, director, and playwright (b. 1920)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Antony_Holland\" title=\"<PERSON>\"><PERSON></a>, English-Canadian actor, director, and playwright (b. 1920)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Antony_Holland"}]}, {"year": "2015", "text": "<PERSON>, Anglo-Irish sportscaster (b. 1918)", "html": "2015 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%27Sullevan\" title=\"<PERSON>\"><PERSON></a>, Anglo-Irish sportscaster (b. 1918)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%27Sullevan\" title=\"<PERSON>\"><PERSON></a>, Anglo-Irish sportscaster (b. 1918)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>%27S<PERSON><PERSON><PERSON>"}]}, {"year": "2015", "text": "<PERSON>, American football player and sportscaster (b. 1939)", "html": "2015 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(American_football)\" title=\"<PERSON> (American football)\"><PERSON></a>, American football player and sportscaster (b. 1939)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(American_football)\" title=\"<PERSON> (American football)\"><PERSON></a>, American football player and sportscaster (b. 1939)", "links": [{"title": "<PERSON> (American football)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(American_football)"}]}, {"year": "2015", "text": "<PERSON>, American computer scientist, engineer, and academic (b. 1930)", "html": "2015 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_Westervelt\" title=\"<PERSON>\"><PERSON></a>, American computer scientist, engineer, and academic (b. 1930)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_Westervelt\" title=\"<PERSON> Westervelt\"><PERSON></a>, American computer scientist, engineer, and academic (b. 1930)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Franklin_H._Westervelt"}]}, {"year": "2018", "text": "<PERSON>, Croatian recording artist (b. 1947) ", "html": "2018 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C4%87\" title=\"<PERSON>\"><PERSON></a>, <a href=\"https://wikipedia.org/wiki/Croats\" title=\"Croats\">Croatian</a> recording artist (b. 1947) ", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C4%87\" title=\"<PERSON>\"><PERSON></a>, <a href=\"https://wikipedia.org/wiki/Croats\" title=\"Croats\">Croatian</a> recording artist (b. 1947) ", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Oliver_Dragojevi%C4%87"}, {"title": "Croats", "link": "https://wikipedia.org/wiki/Croats"}]}, {"year": "2018", "text": "<PERSON>, Yugoslav-born American professional wrestler (b. 1947)", "html": "2018 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Yugoslav-born American professional wrestler (b. 1947)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Yugoslav-born American professional wrestler (b. 1947)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}]}}