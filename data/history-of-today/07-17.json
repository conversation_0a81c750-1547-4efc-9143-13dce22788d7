{"date": "July 17", "url": "https://wikipedia.org/wiki/July_17", "data": {"Events": [{"year": "180", "text": "Twelve inhabitants of Scillium (near Kasserine, modern-day Tunisia) in North Africa are executed for being Christians. This is the earliest record of Christianity in that part of the world.", "html": "180 - <a href=\"https://wikipedia.org/wiki/Scillitan_Martyrs\" title=\"Scillitan Martyrs\">Twelve inhabitants</a> of <a href=\"https://wikipedia.org/wiki/Scillium\" title=\"Scillium\">Scillium</a> (near Kasserine, modern-day <a href=\"https://wikipedia.org/wiki/Tunisia\" title=\"Tunisia\">Tunisia</a>) in North Africa are executed for being Christians. This is the earliest record of Christianity in that part of the world.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Scillitan_Martyrs\" title=\"Scillitan Martyrs\">Twelve inhabitants</a> of <a href=\"https://wikipedia.org/wiki/Scillium\" title=\"Scillium\">Scillium</a> (near Kasserine, modern-day <a href=\"https://wikipedia.org/wiki/Tunisia\" title=\"Tunisia\">Tunisia</a>) in North Africa are executed for being Christians. This is the earliest record of Christianity in that part of the world.", "links": [{"title": "Scillitan Martyrs", "link": "https://wikipedia.org/wiki/Scillitan_Martyrs"}, {"title": "Scillium", "link": "https://wikipedia.org/wiki/Scillium"}, {"title": "Tunisia", "link": "https://wikipedia.org/wiki/Tunisia"}]}, {"year": "1048", "text": "<PERSON><PERSON> <PERSON> is elected pope, and dies 23 days later.", "html": "1048 - <a href=\"https://wikipedia.org/wiki/Pope_Damasus_II\" title=\"Pope Damasus II\"><PERSON><PERSON> <PERSON></a> is elected pope, and dies 23 days later.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Pope_Damasus_II\" title=\"Pope Damasus II\"><PERSON><PERSON> <PERSON></a> is elected pope, and dies 23 days later.", "links": [{"title": "Pope Damasus II", "link": "https://wikipedia.org/wiki/<PERSON>_Damasus_II"}]}, {"year": "1203", "text": "The Fourth Crusade assaults Constantinople. The Byzantine emperor <PERSON><PERSON> flees from his capital into exile.", "html": "1203 - The <a href=\"https://wikipedia.org/wiki/Fourth_Crusade\" title=\"Fourth Crusade\">Fourth Crusade</a> assaults <a href=\"https://wikipedia.org/wiki/Constantinople\" title=\"Constantinople\">Constantinople</a>. The <a href=\"https://wikipedia.org/wiki/Byzantine_emperor\" class=\"mw-redirect\" title=\"Byzantine emperor\">Byzantine emperor</a> <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_III_Angelos\" title=\"<PERSON><PERSON> III <PERSON>\"><PERSON><PERSON></a> flees from his capital into exile.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Fourth_Crusade\" title=\"Fourth Crusade\">Fourth Crusade</a> assaults <a href=\"https://wikipedia.org/wiki/Constantinople\" title=\"Constantinople\">Constantinople</a>. The <a href=\"https://wikipedia.org/wiki/Byzantine_emperor\" class=\"mw-redirect\" title=\"Byzantine emperor\">Byzantine emperor</a> <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_III_Angelos\" title=\"<PERSON><PERSON> III Angelo<PERSON>\"><PERSON><PERSON></a> flees from his capital into exile.", "links": [{"title": "Fourth Crusade", "link": "https://wikipedia.org/wiki/Fourth_Crusade"}, {"title": "Constantinople", "link": "https://wikipedia.org/wiki/Constantinople"}, {"title": "Byzantine emperor", "link": "https://wikipedia.org/wiki/Byzantine_emperor"}, {"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Alexios_III_Angelos"}]}, {"year": "1402", "text": "<PERSON>, better known by his era name as the Yongle Emperor, assumes the throne over the Ming dynasty of China.", "html": "1402 - <PERSON>, better known by his <a href=\"https://wikipedia.org/wiki/Regnal_name\" title=\"Regnal name\">era name</a> as the <a href=\"https://wikipedia.org/wiki/<PERSON>le_Emperor\" title=\"Yongle Emperor\">Yongle Emperor</a>, assumes the throne over the <a href=\"https://wikipedia.org/wiki/Ming_dynasty\" title=\"Ming dynasty\">Ming dynasty</a> of China.", "no_year_html": "<PERSON>, better known by his <a href=\"https://wikipedia.org/wiki/Regnal_name\" title=\"Regnal name\">era name</a> as the <a href=\"https://wikipedia.org/wiki/<PERSON>le_Emperor\" title=\"Yongle Emperor\">Yongle Emperor</a>, assumes the throne over the <a href=\"https://wikipedia.org/wiki/Ming_dynasty\" title=\"Ming dynasty\">Ming dynasty</a> of China.", "links": [{"title": "Regnal name", "link": "https://wikipedia.org/wiki/Regnal_name"}, {"title": "Yongle Emperor", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_Emperor"}, {"title": "Ming dynasty", "link": "https://wikipedia.org/wiki/Ming_dynasty"}]}, {"year": "1429", "text": "Hundred Years' War: <PERSON> of France is crowned the King of France in the Reims Cathedral after a successful campaign by <PERSON> Arc.", "html": "1429 - <a href=\"https://wikipedia.org/wiki/Hundred_Years%27_War\" title=\"Hundred Years' War\">Hundred Years' War</a>: <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_France\" title=\"Charles VII of France\"><PERSON> of France</a> is <a href=\"https://wikipedia.org/wiki/Coronation_of_the_French_monarch\" title=\"Coronation of the French monarch\">crowned</a> the King of France in the <a href=\"https://wikipedia.org/wiki/Reims_Cathedral\" title=\"Reims Cathedral\">Reims Cathedral</a> after a successful campaign by <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_Arc\" title=\"Joan of Arc\"><PERSON> Arc</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Hundred_Years%27_War\" title=\"Hundred Years' War\">Hundred Years' War</a>: <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_France\" title=\"Charles VII of France\"><PERSON> of France</a> is <a href=\"https://wikipedia.org/wiki/Coronation_of_the_French_monarch\" title=\"Coronation of the French monarch\">crowned</a> the King of France in the <a href=\"https://wikipedia.org/wiki/Reims_Cathedral\" title=\"Reims Cathedral\">Reims Cathedral</a> after a successful campaign by <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_Arc\" title=\"Joan of Arc\"><PERSON> of Arc</a>.", "links": [{"title": "Hundred Years' War", "link": "https://wikipedia.org/wiki/Hundred_Years%27_War"}, {"title": "<PERSON> of France", "link": "https://wikipedia.org/wiki/Charles_<PERSON>_of_France"}, {"title": "Coronation of the French monarch", "link": "https://wikipedia.org/wiki/Coronation_of_the_French_monarch"}, {"title": "Reims Cathedral", "link": "https://wikipedia.org/wiki/Reims_Cathedral"}, {"title": "<PERSON> of Arc", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1453", "text": "Battle of Castillon: The last battle of the Hundred Years' War, the French under <PERSON> defeat the English under the <PERSON>, who is killed in the battle in Gascony.", "html": "1453 - <a href=\"https://wikipedia.org/wiki/Battle_of_Castillon\" title=\"Battle of Castillon\">Battle of Castillon</a>: The last battle of the <a href=\"https://wikipedia.org/wiki/Hundred_Years%27_War\" title=\"Hundred Years' War\">Hundred Years' War</a>, the French under <a href=\"https://wikipedia.org/wiki/Jean_Bureau\" title=\"Jean Bureau\"><PERSON></a> defeat the English under the <a href=\"https://wikipedia.org/wiki/<PERSON>,_1st_Earl_of_Shrewsbury\" title=\"<PERSON>, 1st Earl of Shrewsbury\">Earl of Shrewsbury</a>, who is killed in the battle in <a href=\"https://wikipedia.org/wiki/Gascony\" title=\"Gascony\">Gascony</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Battle_of_Castillon\" title=\"Battle of Castillon\">Battle of Castillon</a>: The last battle of the <a href=\"https://wikipedia.org/wiki/Hundred_Years%27_War\" title=\"Hundred Years' War\">Hundred Years' War</a>, the French under <a href=\"https://wikipedia.org/wiki/Jean_Bureau\" title=\"Jean Bureau\"><PERSON></a> defeat the English under the <a href=\"https://wikipedia.org/wiki/<PERSON>,_1st_Earl_<PERSON>_Shrewsbury\" title=\"<PERSON>, 1st Earl of Shrewsbury\">Earl <PERSON> Shrewsbury</a>, who is killed in the battle in <a href=\"https://wikipedia.org/wiki/Gascony\" title=\"Gascony\">Gascony</a>.", "links": [{"title": "Battle of Castillon", "link": "https://wikipedia.org/wiki/Battle_of_Castillon"}, {"title": "Hundred Years' War", "link": "https://wikipedia.org/wiki/Hundred_Years%27_War"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "<PERSON>, 1st Earl of Shrewsbury", "link": "https://wikipedia.org/wiki/<PERSON>,_1st_Earl_of_Shrewsbury"}, {"title": "Gascony", "link": "https://wikipedia.org/wiki/Gascony"}]}, {"year": "1489", "text": "<PERSON><PERSON><PERSON> succeeds <PERSON><PERSON><PERSON> as Sultan of Delhi.", "html": "1489 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a> succeeds <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_Lodi\" title=\"<PERSON><PERSON><PERSON> Lodi\"><PERSON><PERSON><PERSON></a> as <a href=\"https://wikipedia.org/wiki/Sultan_of_Delhi\" class=\"mw-redirect\" title=\"Sultan of Delhi\">Sultan of Delhi</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a> succeeds <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_Lodi\" title=\"<PERSON><PERSON><PERSON> Lodi\"><PERSON><PERSON><PERSON></a> as <a href=\"https://wikipedia.org/wiki/Sultan_of_Delhi\" class=\"mw-redirect\" title=\"Sultan of Delhi\">Sultan of Delhi</a>.", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>di"}, {"title": "Ba<PERSON><PERSON> Khan Lodi", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_Lo<PERSON>"}, {"title": "<PERSON> of Delhi", "link": "https://wikipedia.org/wiki/Sultan_of_Delhi"}]}, {"year": "1717", "text": "King <PERSON> of Great Britain sails down the River Thames with a barge of 50 musicians, where <PERSON>'s Water Music is premiered.", "html": "1717 - King <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Great_Britain\" title=\"<PERSON> I of Great Britain\"><PERSON> of Great Britain</a> sails down the <a href=\"https://wikipedia.org/wiki/River_Thames\" title=\"River Thames\">River Thames</a> with a barge of 50 musicians, where <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"George <PERSON> Handel\"><PERSON></a>'s <i><a href=\"https://wikipedia.org/wiki/Water_Music_(<PERSON>)\" class=\"mw-redirect\" title=\"Water Music (Handel)\">Water Music</a></i> is premiered.", "no_year_html": "King <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Great_Britain\" title=\"<PERSON> I of Great Britain\"><PERSON> of Great Britain</a> sails down the <a href=\"https://wikipedia.org/wiki/River_Thames\" title=\"River Thames\">River Thames</a> with a barge of 50 musicians, where <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"George Fr<PERSON> Handel\"><PERSON></a>'s <i><a href=\"https://wikipedia.org/wiki/Water_Music_(Handel)\" class=\"mw-redirect\" title=\"Water Music (Handel)\">Water Music</a></i> is premiered.", "links": [{"title": "<PERSON> of Great Britain", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Great_Britain"}, {"title": "River Thames", "link": "https://wikipedia.org/wiki/River_Thames"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON><PERSON>_<PERSON>"}, {"title": "Water Music (Handel)", "link": "https://wikipedia.org/wiki/Water_Music_(Handel)"}]}, {"year": "1762", "text": "Former emperor <PERSON> of Russia is murdered.", "html": "1762 - Former emperor <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Russia\" title=\"<PERSON> of Russia\"><PERSON> of Russia</a> is murdered.", "no_year_html": "Former emperor <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Russia\" title=\"<PERSON> of Russia\"><PERSON> of Russia</a> is murdered.", "links": [{"title": "<PERSON> of Russia", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Russia"}]}, {"year": "1771", "text": "Bloody Falls massacre: Chipewyan chief <PERSON><PERSON><PERSON><PERSON>, traveling as the guide to <PERSON> on his Arctic overland journey, massacres a group of unsuspecting Inuit.", "html": "1771 - <a href=\"https://wikipedia.org/wiki/Bloody_Falls_massacre\" title=\"Bloody Falls massacre\">Bloody Falls massacre</a>: <a href=\"https://wikipedia.org/wiki/Chipewyan\" title=\"Chipewyan\"><PERSON><PERSON><PERSON></a> chief <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>bbee\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, traveling as the guide to <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> on his Arctic overland journey, massacres a group of unsuspecting <a href=\"https://wikipedia.org/wiki/Inuit\" title=\"Inuit\">Inuit</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Bloody_Falls_massacre\" title=\"Bloody Falls massacre\">Bloody Falls massacre</a>: <a href=\"https://wikipedia.org/wiki/Chipewyan\" title=\"Chipewyan\"><PERSON><PERSON><PERSON></a> chief <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>e\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, traveling as the guide to <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> on his Arctic overland journey, massacres a group of unsuspecting <a href=\"https://wikipedia.org/wiki/Inuit\" title=\"Inuit\">Inuit</a>.", "links": [{"title": "Bloody Falls massacre", "link": "https://wikipedia.org/wiki/Bloody_Falls_massacre"}, {"title": "Chipewyan", "link": "https://wikipedia.org/wiki/<PERSON>ewyan"}, {"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>e"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Inuit", "link": "https://wikipedia.org/wiki/Inuit"}]}, {"year": "1791", "text": "Members of the French National Guard under the command of General <PERSON> open fire on a crowd of radical Jacob<PERSON> at the Champ de Mars, Paris, during the French Revolution, killing scores of people.", "html": "1791 - Members of the <a href=\"https://wikipedia.org/wiki/National_Guard_(France)\" title=\"National Guard (France)\">French National Guard</a> under the command of <a href=\"https://wikipedia.org/wiki/<PERSON>,_<PERSON>_<PERSON>\" title=\"<PERSON>, <PERSON>\">General <PERSON></a> <a href=\"https://wikipedia.org/wiki/Champ_de_Mars_Massacre\" class=\"mw-redirect\" title=\"Champ de Mars Massacre\">open fire</a> on a crowd of radical <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_(politics)\" title=\"<PERSON><PERSON> (politics)\"><PERSON><PERSON></a> at the <a href=\"https://wikipedia.org/wiki/Champ_de_Mars\" title=\"Champ de Mars\">Champ de Mars</a>, Paris, during the <a href=\"https://wikipedia.org/wiki/French_Revolution\" title=\"French Revolution\">French Revolution</a>, killing scores of people.", "no_year_html": "Members of the <a href=\"https://wikipedia.org/wiki/National_Guard_(France)\" title=\"National Guard (France)\">French National Guard</a> under the command of <a href=\"https://wikipedia.org/wiki/<PERSON>,_<PERSON>_<PERSON>\" title=\"<PERSON>, <PERSON>\">General <PERSON></a> <a href=\"https://wikipedia.org/wiki/Champ_de_Mars_Massacre\" class=\"mw-redirect\" title=\"Champ de Mars Massacre\">open fire</a> on a crowd of radical <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_(politics)\" title=\"<PERSON><PERSON> (politics)\"><PERSON><PERSON></a> at the <a href=\"https://wikipedia.org/wiki/Champ_de_Mars\" title=\"Champ de Mars\">Champ de Mars</a>, Paris, during the <a href=\"https://wikipedia.org/wiki/French_Revolution\" title=\"French Revolution\">French Revolution</a>, killing scores of people.", "links": [{"title": "National Guard (France)", "link": "https://wikipedia.org/wiki/National_Guard_(France)"}, {"title": "<PERSON>, Marquis <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>,_<PERSON>_<PERSON>_<PERSON>"}, {"title": "<PERSON><PERSON> Massacre", "link": "https://wikipedia.org/wiki/Champ_de_Mars_Massacre"}, {"title": "<PERSON><PERSON> (politics)", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_(politics)"}, {"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Cha<PERSON>_<PERSON>_<PERSON>"}, {"title": "French Revolution", "link": "https://wikipedia.org/wiki/French_Revolution"}]}, {"year": "1794", "text": "The 16 Carmelite Martyrs of Compiègne are executed ten days prior to the end of the French Revolution's Reign of Terror.", "html": "1794 - The 16 <a href=\"https://wikipedia.org/wiki/Carmelites\" title=\"Carmelites\">Carmelite</a> <a href=\"https://wikipedia.org/wiki/Martyrs_of_Compi%C3%A8gne\" title=\"Martyrs of Compiègne\">Martyrs of Compiègne</a> are executed ten days prior to the end of the French Revolution's <a href=\"https://wikipedia.org/wiki/Reign_of_Terror\" title=\"Reign of Terror\">Reign of Terror</a>.", "no_year_html": "The 16 <a href=\"https://wikipedia.org/wiki/Carmelites\" title=\"Carmelites\">Carmelite</a> <a href=\"https://wikipedia.org/wiki/Martyrs_of_Compi%C3%A8gne\" title=\"Martyrs of Compiègne\">Martyrs of Compiègne</a> are executed ten days prior to the end of the French Revolution's <a href=\"https://wikipedia.org/wiki/Reign_of_Terror\" title=\"Reign of Terror\">Reign of Terror</a>.", "links": [{"title": "Carmelites", "link": "https://wikipedia.org/wiki/Carmelites"}, {"title": "Martyrs of Compiègne", "link": "https://wikipedia.org/wiki/Martyrs_of_Compi%C3%A8gne"}, {"title": "Reign of Terror", "link": "https://wikipedia.org/wiki/Reign_of_Terror"}]}, {"year": "1821", "text": "The Kingdom of Spain cedes the territory of Florida to the United States.", "html": "1821 - The <a href=\"https://wikipedia.org/wiki/Kingdom_of_Spain\" class=\"mw-redirect\" title=\"Kingdom of Spain\">Kingdom of Spain</a> cedes the territory of <a href=\"https://wikipedia.org/wiki/History_of_Florida#End_of_Spanish_control\" title=\"History of Florida\">Florida</a> to the United States.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Kingdom_of_Spain\" class=\"mw-redirect\" title=\"Kingdom of Spain\">Kingdom of Spain</a> cedes the territory of <a href=\"https://wikipedia.org/wiki/History_of_Florida#End_of_Spanish_control\" title=\"History of Florida\">Florida</a> to the United States.", "links": [{"title": "Kingdom of Spain", "link": "https://wikipedia.org/wiki/Kingdom_of_Spain"}, {"title": "History of Florida", "link": "https://wikipedia.org/wiki/History_of_Florida#End_of_Spanish_control"}]}, {"year": "1850", "text": "<PERSON> became the first star (other than the <PERSON>) to be photographed.", "html": "1850 - <a href=\"https://wikipedia.org/wiki/Vega\" title=\"Vega\"><PERSON></a> became the first star (other than the Sun) to be photographed.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Vega\" title=\"Vega\"><PERSON></a> became the first star (other than the Sun) to be photographed.", "links": [{"title": "Vega", "link": "https://wikipedia.org/wiki/Vega"}]}, {"year": "1867", "text": "Harvard School of Dental Medicine is established in Boston, Massachusetts. It is the first dental school in the U.S. that is affiliated with a university.", "html": "1867 - <a href=\"https://wikipedia.org/wiki/Harvard_School_of_Dental_Medicine\" title=\"Harvard School of Dental Medicine\">Harvard School of Dental Medicine</a> is established in <a href=\"https://wikipedia.org/wiki/Boston\" title=\"Boston\">Boston</a>, Massachusetts. It is the first dental school in the U.S. that is affiliated with a university.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Harvard_School_of_Dental_Medicine\" title=\"Harvard School of Dental Medicine\">Harvard School of Dental Medicine</a> is established in <a href=\"https://wikipedia.org/wiki/Boston\" title=\"Boston\">Boston</a>, Massachusetts. It is the first dental school in the U.S. that is affiliated with a university.", "links": [{"title": "Harvard School of Dental Medicine", "link": "https://wikipedia.org/wiki/Harvard_School_of_Dental_Medicine"}, {"title": "Boston", "link": "https://wikipedia.org/wiki/Boston"}]}, {"year": "1899", "text": "NEC Corporation is organized as the first Japanese joint venture with foreign capital.", "html": "1899 - <a href=\"https://wikipedia.org/wiki/NEC\" title=\"NEC\">NEC</a> Corporation is organized as the first Japanese joint venture with foreign capital.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/NEC\" title=\"NEC\">NEC</a> Corporation is organized as the first Japanese joint venture with foreign capital.", "links": [{"title": "NEC", "link": "https://wikipedia.org/wiki/NEC"}]}, {"year": "1901", "text": "Liner Deutschland sets east to west transatlantic record of five days, eleven hours and five minutes.", "html": "1901 - Liner <a href=\"https://wikipedia.org/wiki/SS_Deutschland_(1900)\" title=\"SS Deutschland (1900)\"><i>Deutschland</i></a> sets east to west transatlantic record of five days, eleven hours and five minutes.", "no_year_html": "Liner <a href=\"https://wikipedia.org/wiki/SS_Deutschland_(1900)\" title=\"SS Deutschland (1900)\"><i>Deutschland</i></a> sets east to west transatlantic record of five days, eleven hours and five minutes.", "links": [{"title": "SS Deutschland (1900)", "link": "https://wikipedia.org/wiki/SS_Deutschland_(1900)"}]}, {"year": "1902", "text": "Willis Carrier creates the first air conditioner in Buffalo, New York.", "html": "1902 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Willis Carrier\"><PERSON></a> creates the first <a href=\"https://wikipedia.org/wiki/Air_conditioning\" title=\"Air conditioning\">air conditioner</a> in <a href=\"https://wikipedia.org/wiki/Buffalo,_New_York\" title=\"Buffalo, New York\">Buffalo, New York</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Willis Carrier\"><PERSON> Carrier</a> creates the first <a href=\"https://wikipedia.org/wiki/Air_conditioning\" title=\"Air conditioning\">air conditioner</a> in <a href=\"https://wikipedia.org/wiki/Buffalo,_New_York\" title=\"Buffalo, New York\">Buffalo, New York</a>.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Air conditioning", "link": "https://wikipedia.org/wiki/Air_conditioning"}, {"title": "Buffalo, New York", "link": "https://wikipedia.org/wiki/Buffalo,_New_York"}]}, {"year": "1917", "text": "King <PERSON> issues a proclamation stating that the male line descendants of the British royal family will bear the surname <PERSON>.", "html": "1917 - King <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"George V\"><PERSON> V</a> issues a proclamation stating that the male line descendants of the <a href=\"https://wikipedia.org/wiki/British_royal_family\" title=\"British royal family\">British royal family</a> will bear the surname <a href=\"https://wikipedia.org/wiki/House_of_Windsor\" title=\"House of Windsor\">Windsor</a>.", "no_year_html": "King <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"George V\"><PERSON> V</a> issues a proclamation stating that the male line descendants of the <a href=\"https://wikipedia.org/wiki/British_royal_family\" title=\"British royal family\">British royal family</a> will bear the surname <a href=\"https://wikipedia.org/wiki/House_of_Windsor\" title=\"House of Windsor\">Windsor</a>.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "British royal family", "link": "https://wikipedia.org/wiki/British_royal_family"}, {"title": "House of Windsor", "link": "https://wikipedia.org/wiki/House_of_Windsor"}]}, {"year": "1918", "text": "Tsar <PERSON> of Russia and his immediate family and retainers are executed by Bolshevik Chekists at the Ipatiev House in Yekaterinburg, Russia.", "html": "1918 - <a href=\"https://wikipedia.org/wiki/<PERSON>_II_of_Russia\" class=\"mw-redirect\" title=\"Nicholas II of Russia\">Tsar <PERSON> II</a> of <a href=\"https://wikipedia.org/wiki/Russian_Empire\" title=\"Russian Empire\">Russia</a> and his immediate family and retainers <a href=\"https://wikipedia.org/wiki/Execution_of_the_Romanov_family\" class=\"mw-redirect\" title=\"Execution of the Romanov family\">are executed</a> by <a href=\"https://wikipedia.org/wiki/Bolshevik\" class=\"mw-redirect\" title=\"Bolshevik\">Bolshevik</a> <a href=\"https://wikipedia.org/wiki/Cheka\" title=\"Cheka\">Chekists</a> at the <a href=\"https://wikipedia.org/wiki/Ipatiev_House\" title=\"Ipatiev House\">Ipatiev House</a> in <a href=\"https://wikipedia.org/wiki/Yekaterinburg\" title=\"Yekaterinburg\">Yekaterinburg</a>, <a href=\"https://wikipedia.org/wiki/Russian_Soviet_Federative_Socialist_Republic\" title=\"Russian Soviet Federative Socialist Republic\">Russia</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_II_of_Russia\" class=\"mw-redirect\" title=\"Nicholas II of Russia\">Tsar <PERSON> II</a> of <a href=\"https://wikipedia.org/wiki/Russian_Empire\" title=\"Russian Empire\">Russia</a> and his immediate family and retainers <a href=\"https://wikipedia.org/wiki/Execution_of_the_Romanov_family\" class=\"mw-redirect\" title=\"Execution of the Romanov family\">are executed</a> by <a href=\"https://wikipedia.org/wiki/Bolshevik\" class=\"mw-redirect\" title=\"Bolshevik\">Bolshevik</a> <a href=\"https://wikipedia.org/wiki/Cheka\" title=\"Cheka\">Chekists</a> at the <a href=\"https://wikipedia.org/wiki/Ipatiev_House\" title=\"Ipatiev House\">Ipatiev House</a> in <a href=\"https://wikipedia.org/wiki/Yekaterinburg\" title=\"Yekaterinburg\">Yekaterinburg</a>, <a href=\"https://wikipedia.org/wiki/Russian_Soviet_Federative_Socialist_Republic\" title=\"Russian Soviet Federative Socialist Republic\">Russia</a>.", "links": [{"title": "<PERSON> of Russia", "link": "https://wikipedia.org/wiki/<PERSON>_II_of_Russia"}, {"title": "Russian Empire", "link": "https://wikipedia.org/wiki/Russian_Empire"}, {"title": "Execution of the <PERSON><PERSON> family", "link": "https://wikipedia.org/wiki/Execution_of_the_Romanov_family"}, {"title": "Bolshevik", "link": "https://wikipedia.org/wiki/Bolshevik"}, {"title": "Cheka", "link": "https://wikipedia.org/wiki/Cheka"}, {"title": "Ipatiev House", "link": "https://wikipedia.org/wiki/Ipatiev_House"}, {"title": "Yekaterinburg", "link": "https://wikipedia.org/wiki/Yekaterinburg"}, {"title": "Russian Soviet Federative Socialist Republic", "link": "https://wikipedia.org/wiki/Russian_Soviet_Federative_Socialist_Republic"}]}, {"year": "1918", "text": "The RMS Carpathia, the ship that rescued the 705 survivors from the RMS Titanic, is sunk off Ireland by the German SM U-55; five lives are lost.", "html": "1918 - The <a href=\"https://wikipedia.org/wiki/RMS_Carpathia\" title=\"RMS Carpathia\">RMS <i>Carpathia</i></a>, the ship that rescued the 705 survivors from the <a href=\"https://wikipedia.org/wiki/Titanic\" title=\"Titanic\">RMS <i>Titanic</i></a>, is sunk off Ireland by the German <a href=\"https://wikipedia.org/wiki/SM_U-55\" title=\"SM U-55\">SM <i>U-55</i></a>; five lives are lost.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/RMS_Carpathia\" title=\"RMS Carpathia\">RMS <i>Carpathia</i></a>, the ship that rescued the 705 survivors from the <a href=\"https://wikipedia.org/wiki/Titanic\" title=\"Titanic\">RMS <i>Titanic</i></a>, is sunk off Ireland by the German <a href=\"https://wikipedia.org/wiki/SM_U-55\" title=\"SM U-55\">SM <i>U-55</i></a>; five lives are lost.", "links": [{"title": "RMS Carpathia", "link": "https://wikipedia.org/wiki/RMS_Carpathia"}, {"title": "Titanic", "link": "https://wikipedia.org/wiki/Titanic"}, {"title": "SM U-55", "link": "https://wikipedia.org/wiki/SM_U-55"}]}, {"year": "1919", "text": "The form of government in the Republic of Finland is officially confirmed. For this reason, July 17 is known as the Day of Democracy (Kansanvallan päivä) in Finland.", "html": "1919 - The form of government in the <a href=\"https://wikipedia.org/wiki/Republic_of_Finland\" class=\"mw-redirect\" title=\"Republic of Finland\">Republic of Finland</a> is officially confirmed. For this reason, July 17 is known as the <i>Day of Democracy</i> (<i>Kansanvallan päivä</i>) in Finland.", "no_year_html": "The form of government in the <a href=\"https://wikipedia.org/wiki/Republic_of_Finland\" class=\"mw-redirect\" title=\"Republic of Finland\">Republic of Finland</a> is officially confirmed. For this reason, July 17 is known as the <i>Day of Democracy</i> (<i>Kansanvallan p<PERSON>ivä</i>) in Finland.", "links": [{"title": "Republic of Finland", "link": "https://wikipedia.org/wiki/Republic_of_Finland"}]}, {"year": "1932", "text": "Altona Bloody Sunday: A riot between the Nazi Party paramilitary forces, the SS and SA, and the German Communist Party ensues.", "html": "1932 - <a href=\"https://wikipedia.org/wiki/Altona_Bloody_Sunday\" title=\"Altona Bloody Sunday\">Altona Bloody Sunday</a>: A riot between the <a href=\"https://wikipedia.org/wiki/Nazi_Party\" title=\"Nazi Party\">Nazi Party</a> paramilitary forces, the <a href=\"https://wikipedia.org/wiki/Schutzstaffel\" title=\"Schutzstaffel\">SS</a> and <a href=\"https://wikipedia.org/wiki/Sturmabteilung\" title=\"Sturmabteilung\">SA</a>, and the <a href=\"https://wikipedia.org/wiki/Communist_Party_of_Germany\" title=\"Communist Party of Germany\">German Communist Party</a> ensues.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Altona_Bloody_Sunday\" title=\"Altona Bloody Sunday\">Altona Bloody Sunday</a>: A riot between the <a href=\"https://wikipedia.org/wiki/Nazi_Party\" title=\"Nazi Party\">Nazi Party</a> paramilitary forces, the <a href=\"https://wikipedia.org/wiki/Schutzstaffel\" title=\"Schutzstaffel\">SS</a> and <a href=\"https://wikipedia.org/wiki/Sturmabteilung\" title=\"Sturmabteilung\">SA</a>, and the <a href=\"https://wikipedia.org/wiki/Communist_Party_of_Germany\" title=\"Communist Party of Germany\">German Communist Party</a> ensues.", "links": [{"title": "Altona Bloody Sunday", "link": "https://wikipedia.org/wiki/Altona_Bloody_Sunday"}, {"title": "Nazi Party", "link": "https://wikipedia.org/wiki/Nazi_Party"}, {"title": "<PERSON><PERSON><PERSON>sta<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>tzstaffel"}, {"title": "Sturmabteilung", "link": "https://wikipedia.org/wiki/Sturmabteilung"}, {"title": "Communist Party of Germany", "link": "https://wikipedia.org/wiki/Communist_Party_of_Germany"}]}, {"year": "1936", "text": "Spanish Civil War: An Armed Forces rebellion against the recently elected leftist Popular Front government of Spain starts the civil war.", "html": "1936 - <a href=\"https://wikipedia.org/wiki/Spanish_Civil_War\" title=\"Spanish Civil War\">Spanish Civil War</a>: An <a href=\"https://wikipedia.org/wiki/Spanish_coup_of_July_1936\" title=\"Spanish coup of July 1936\">Armed Forces rebellion</a> against the recently elected leftist <a href=\"https://wikipedia.org/wiki/Popular_Front_(Spain)\" title=\"Popular Front (Spain)\">Popular Front</a> government of Spain starts the civil war.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Spanish_Civil_War\" title=\"Spanish Civil War\">Spanish Civil War</a>: An <a href=\"https://wikipedia.org/wiki/Spanish_coup_of_July_1936\" title=\"Spanish coup of July 1936\">Armed Forces rebellion</a> against the recently elected leftist <a href=\"https://wikipedia.org/wiki/Popular_Front_(Spain)\" title=\"Popular Front (Spain)\">Popular Front</a> government of Spain starts the civil war.", "links": [{"title": "Spanish Civil War", "link": "https://wikipedia.org/wiki/Spanish_Civil_War"}, {"title": "Spanish coup of July 1936", "link": "https://wikipedia.org/wiki/Spanish_coup_of_July_1936"}, {"title": "Popular Front (Spain)", "link": "https://wikipedia.org/wiki/Popular_Front_(Spain)"}]}, {"year": "1938", "text": "<PERSON> takes off from Brooklyn to fly the \"wrong way\" to Ireland and becomes known as \"Wrong Way\" <PERSON><PERSON>.", "html": "1938 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> takes off from <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Floyd <PERSON> Field\">Brooklyn</a> to fly the \"wrong way\" to Ireland and becomes known as \"Wrong Way\" Corrigan.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> takes off from <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"Floyd Bennett Field\">Brooklyn</a> to fly the \"wrong way\" to Ireland and becomes known as \"Wrong Way\" Corrigan.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1944", "text": "Port Chicago disaster: Near the San Francisco Bay, two ships laden with ammunition for the war explode in Port Chicago, California, killing 320.", "html": "1944 - <a href=\"https://wikipedia.org/wiki/Port_Chicago_disaster\" title=\"Port Chicago disaster\">Port Chicago disaster</a>: Near the <a href=\"https://wikipedia.org/wiki/San_Francisco_Bay\" title=\"San Francisco Bay\">San Francisco Bay</a>, two ships laden with <a href=\"https://wikipedia.org/wiki/Ammunition\" title=\"Ammunition\">ammunition</a> for the war explode in <a href=\"https://wikipedia.org/wiki/Port_Chicago,_California\" title=\"Port Chicago, California\">Port Chicago, California</a>, killing 320.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Port_Chicago_disaster\" title=\"Port Chicago disaster\">Port Chicago disaster</a>: Near the <a href=\"https://wikipedia.org/wiki/San_Francisco_Bay\" title=\"San Francisco Bay\">San Francisco Bay</a>, two ships laden with <a href=\"https://wikipedia.org/wiki/Ammunition\" title=\"Ammunition\">ammunition</a> for the war explode in <a href=\"https://wikipedia.org/wiki/Port_Chicago,_California\" title=\"Port Chicago, California\">Port Chicago, California</a>, killing 320.", "links": [{"title": "Port Chicago disaster", "link": "https://wikipedia.org/wiki/Port_Chicago_disaster"}, {"title": "San Francisco Bay", "link": "https://wikipedia.org/wiki/San_Francisco_Bay"}, {"title": "Ammunition", "link": "https://wikipedia.org/wiki/Ammunition"}, {"title": "Port Chicago, California", "link": "https://wikipedia.org/wiki/Port_Chicago,_California"}]}, {"year": "1944", "text": "World War II: At Sainte-Foy-de-Montgommery in Normandy, Field Marshal <PERSON> is seriously injured by Allied aircraft while returning to his headquarters.", "html": "1944 - <a href=\"https://wikipedia.org/wiki/World_War_II\" title=\"World War II\">World War II</a>: At <a href=\"https://wikipedia.org/wiki/<PERSON>-F<PERSON>-<PERSON>Montgommery\" title=\"Sainte-Foy-<PERSON>Montgommery\"><PERSON><PERSON><PERSON><PERSON>gommer<PERSON></a> in Normandy, Field Marshal <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> is seriously injured by <a href=\"https://wikipedia.org/wiki/Allies_of_World_War_II\" title=\"Allies of World War II\">Allied</a> aircraft while returning to his headquarters.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/World_War_II\" title=\"World War II\">World War II</a>: At <a href=\"https://wikipedia.org/wiki/<PERSON>-F<PERSON>-<PERSON>-Montgommery\" title=\"Sainte-Foy-<PERSON>Montgommery\"><PERSON><PERSON><PERSON><PERSON></a> in Normandy, Field Marshal <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> is seriously injured by <a href=\"https://wikipedia.org/wiki/Allies_of_World_War_II\" title=\"Allies of World War II\">Allied</a> aircraft while returning to his headquarters.", "links": [{"title": "World War II", "link": "https://wikipedia.org/wiki/World_War_II"}, {"title": "Sainte-Foy-de-Montgommery", "link": "https://wikipedia.org/wiki/<PERSON>-Foy-de-Montgommery"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Allies of World War II", "link": "https://wikipedia.org/wiki/Allies_of_World_War_II"}]}, {"year": "1945", "text": "World War II: The main three leaders of the Allied nations, <PERSON>, <PERSON> and <PERSON>, meet in the German city of Potsdam to decide the future of a defeated Germany.", "html": "1945 - World War II: The main three leaders of the <a href=\"https://wikipedia.org/wiki/Allies_of_World_War_II\" title=\"Allies of World War II\">Allied</a> nations, <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> and <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, <a href=\"https://wikipedia.org/wiki/Potsdam_Conference\" title=\"Potsdam Conference\">meet</a> in the German city of <a href=\"https://wikipedia.org/wiki/Potsdam\" title=\"Potsdam\">Potsdam</a> to decide the future of a defeated Germany.", "no_year_html": "World War II: The main three leaders of the <a href=\"https://wikipedia.org/wiki/Allies_of_World_War_II\" title=\"Allies of World War II\">Allied</a> nations, <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> and <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, <a href=\"https://wikipedia.org/wiki/Potsdam_Conference\" title=\"Potsdam Conference\">meet</a> in the German city of <a href=\"https://wikipedia.org/wiki/Potsdam\" title=\"Potsdam\">Potsdam</a> to decide the future of a defeated Germany.", "links": [{"title": "Allies of World War II", "link": "https://wikipedia.org/wiki/Allies_of_World_War_II"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}, {"title": "Potsdam Conference", "link": "https://wikipedia.org/wiki/Potsdam_Conference"}, {"title": "Potsdam", "link": "https://wikipedia.org/wiki/Potsdam"}]}, {"year": "1953", "text": "The largest number of United States midshipman casualties in a single event results from an aircraft crash in Florida, killing 44.", "html": "1953 - The largest number of United States <a href=\"https://wikipedia.org/wiki/Midshipman\" title=\"Midshipman\">midshipman</a> casualties in a single event results from an <a href=\"https://wikipedia.org/wiki/USMC_R4Q_NROTC_crash\" title=\"USMC R4Q NROTC crash\">aircraft crash</a> in Florida, killing 44.", "no_year_html": "The largest number of United States <a href=\"https://wikipedia.org/wiki/Midshipman\" title=\"Midshipman\">midshipman</a> casualties in a single event results from an <a href=\"https://wikipedia.org/wiki/USMC_R4Q_NROTC_crash\" title=\"USMC R4Q NROTC crash\">aircraft crash</a> in Florida, killing 44.", "links": [{"title": "Midshipman", "link": "https://wikipedia.org/wiki/Midshipman"}, {"title": "USMC R4Q NROTC crash", "link": "https://wikipedia.org/wiki/USMC_R4Q_NROTC_crash"}]}, {"year": "1954", "text": "First Indochina War: Viet Minh troops successfully ambush the armoured French column 'G.M. 42' in the Battle of Chu Dreh Pass in the Central Highlands. It is the last battle of the war.", "html": "1954 - <a href=\"https://wikipedia.org/wiki/First_Indochina_War\" title=\"First Indochina War\">First Indochina War</a>: <a href=\"https://wikipedia.org/wiki/Viet_Minh\" title=\"Viet Minh\"><PERSON><PERSON> Minh</a> troops successfully ambush the armoured French column 'G.M. 42' in the <a href=\"https://wikipedia.org/wiki/Battle_of_Chu_Dreh_Pass\" title=\"Battle of Chu Dreh Pass\">Battle of Chu Dreh Pass</a> in the <a href=\"https://wikipedia.org/wiki/Central_Highlands_(Vietnam)\" title=\"Central Highlands (Vietnam)\">Central Highlands</a>. It is the last battle of the war.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/First_Indochina_War\" title=\"First Indochina War\">First Indochina War</a>: <a href=\"https://wikipedia.org/wiki/Viet_Minh\" title=\"Viet Minh\">Viet Minh</a> troops successfully ambush the armoured French column 'G.M. 42' in the <a href=\"https://wikipedia.org/wiki/Battle_of_Chu_Dreh_Pass\" title=\"Battle of Chu Dreh Pass\">Battle of Chu Dreh Pass</a> in the <a href=\"https://wikipedia.org/wiki/Central_Highlands_(Vietnam)\" title=\"Central Highlands (Vietnam)\">Central Highlands</a>. It is the last battle of the war.", "links": [{"title": "First Indochina War", "link": "https://wikipedia.org/wiki/First_Indochina_War"}, {"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Viet_Minh"}, {"title": "Battle of Chu Dreh Pass", "link": "https://wikipedia.org/wiki/Battle_of_Chu_Dreh_Pass"}, {"title": "Central Highlands (Vietnam)", "link": "https://wikipedia.org/wiki/Central_Highlands_(Vietnam)"}]}, {"year": "1955", "text": "Disneyland is dedicated and opened by Walt Disney in Anaheim, California.", "html": "1955 - <a href=\"https://wikipedia.org/wiki/Disneyland\" title=\"Disneyland\">Disneyland</a> is dedicated and opened by <a href=\"https://wikipedia.org/wiki/Walt_Disney\" title=\"Walt Disney\"><PERSON></a> in <a href=\"https://wikipedia.org/wiki/Anaheim,_California\" title=\"Anaheim, California\">Anaheim, California</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Disneyland\" title=\"Disneyland\">Disneyland</a> is dedicated and opened by <a href=\"https://wikipedia.org/wiki/Walt_Disney\" title=\"Walt Disney\"><PERSON></a> in <a href=\"https://wikipedia.org/wiki/Anaheim,_California\" title=\"Anaheim, California\">Anaheim, California</a>.", "links": [{"title": "Disneyland", "link": "https://wikipedia.org/wiki/Disneyland"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Anaheim, California", "link": "https://wikipedia.org/wiki/Anaheim,_California"}]}, {"year": "1962", "text": "Nuclear weapons testing: The \"Small Boy\" test shot Little <PERSON><PERSON> I becomes the last atmospheric test detonation at the Nevada National Security Site.", "html": "1962 - <a href=\"https://wikipedia.org/wiki/Nuclear_weapons_testing\" title=\"Nuclear weapons testing\">Nuclear weapons testing</a>: The \"Small Boy\" test shot <a href=\"https://wikipedia.org/wiki/<PERSON>_Feller_(nuclear_tests)\" title=\"<PERSON> Feller (nuclear tests)\">Little Feller I</a> becomes the last atmospheric test detonation at the <a href=\"https://wikipedia.org/wiki/Nevada_National_Security_Site\" class=\"mw-redirect\" title=\"Nevada National Security Site\">Nevada National Security Site</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Nuclear_weapons_testing\" title=\"Nuclear weapons testing\">Nuclear weapons testing</a>: The \"Small Boy\" test shot <a href=\"https://wikipedia.org/wiki/<PERSON>_Feller_(nuclear_tests)\" title=\"<PERSON> Feller (nuclear tests)\">Little Feller I</a> becomes the last atmospheric test detonation at the <a href=\"https://wikipedia.org/wiki/Nevada_National_Security_Site\" class=\"mw-redirect\" title=\"Nevada National Security Site\">Nevada National Security Site</a>.", "links": [{"title": "Nuclear weapons testing", "link": "https://wikipedia.org/wiki/Nuclear_weapons_testing"}, {"title": "<PERSON> (nuclear tests)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(nuclear_tests)"}, {"title": "Nevada National Security Site", "link": "https://wikipedia.org/wiki/Nevada_National_Security_Site"}]}, {"year": "1968", "text": "<PERSON> is overthrown and the Ba'ath Party is installed as the governing power in Iraq with <PERSON> as the new Iraqi President.", "html": "1968 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> is <a href=\"https://wikipedia.org/wiki/17_July_Revolution\" title=\"17 July Revolution\">overthrown</a> and the <a href=\"https://wikipedia.org/wiki/Ba%27ath_Party\" title=\"Ba'ath Party\">Ba'ath Party</a> is installed as the governing power in <a href=\"https://wikipedia.org/wiki/Iraq\" title=\"Iraq\">Iraq</a> with <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> as the new Iraqi President.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> is <a href=\"https://wikipedia.org/wiki/17_July_Revolution\" title=\"17 July Revolution\">overthrown</a> and the <a href=\"https://wikipedia.org/wiki/Ba%27ath_Party\" title=\"Ba'ath Party\">Ba'ath Party</a> is installed as the governing power in <a href=\"https://wikipedia.org/wiki/Iraq\" title=\"Iraq\">Iraq</a> with <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> as the new Iraqi President.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "17 July Revolution", "link": "https://wikipedia.org/wiki/17_July_Revolution"}, {"title": "Ba'ath Party", "link": "https://wikipedia.org/wiki/Ba%27ath_Party"}, {"title": "Iraq", "link": "https://wikipedia.org/wiki/Iraq"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1973", "text": "King <PERSON> of Afghanistan, while having surgery in Italy, is deposed by his cousin <PERSON>.", "html": "1973 - King <a href=\"https://wikipedia.org/wiki/<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a> of <a href=\"https://wikipedia.org/wiki/Afghanistan\" title=\"Afghanistan\">Afghanistan</a>, while having surgery in Italy, is deposed by his cousin <a href=\"https://wikipedia.org/wiki/<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>.", "no_year_html": "King <a href=\"https://wikipedia.org/wiki/<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a> of <a href=\"https://wikipedia.org/wiki/Afghanistan\" title=\"Afghanistan\">Afghanistan</a>, while having surgery in Italy, is deposed by his cousin <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>"}, {"title": "Afghanistan", "link": "https://wikipedia.org/wiki/Afghanistan"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1975", "text": "Apollo-Soyuz Test Project: An American Apollo and a Soviet Soyuz spacecraft dock with each other in orbit marking the first such link-up between spacecraft from the two nations.", "html": "1975 - <a href=\"https://wikipedia.org/wiki/Apollo%E2%80%93Soyuz_Test_Project\" class=\"mw-redirect\" title=\"Apollo-Soyuz Test Project\">Apollo-Soyuz Test Project</a>: An American <a href=\"https://wikipedia.org/wiki/Apollo_program\" title=\"Apollo program\">Apollo</a> and a Soviet <a href=\"https://wikipedia.org/wiki/Soyuz_(spacecraft)\" title=\"Soyuz (spacecraft)\">Soyuz spacecraft</a> dock with each other in <a href=\"https://wikipedia.org/wiki/Orbit\" title=\"Orbit\">orbit</a> marking the first such link-up between spacecraft from the two nations.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Apollo%E2%80%93Soyuz_Test_Project\" class=\"mw-redirect\" title=\"Apollo-Soyuz Test Project\">Apollo-Soyuz Test Project</a>: An American <a href=\"https://wikipedia.org/wiki/Apollo_program\" title=\"Apollo program\">Apollo</a> and a Soviet <a href=\"https://wikipedia.org/wiki/Soyuz_(spacecraft)\" title=\"Soyuz (spacecraft)\">Soyuz spacecraft</a> dock with each other in <a href=\"https://wikipedia.org/wiki/Orbit\" title=\"Orbit\">orbit</a> marking the first such link-up between spacecraft from the two nations.", "links": [{"title": "Apollo-Soyuz Test Project", "link": "https://wikipedia.org/wiki/Apollo%E2%80%93Soyuz_Test_Project"}, {"title": "Apollo program", "link": "https://wikipedia.org/wiki/Apollo_program"}, {"title": "Soyuz (spacecraft)", "link": "https://wikipedia.org/wiki/Soyuz_(spacecraft)"}, {"title": "Orbit", "link": "https://wikipedia.org/wiki/Orbit"}]}, {"year": "1976", "text": "East Timor is annexed and becomes the 27th province of Indonesia.", "html": "1976 - <a href=\"https://wikipedia.org/wiki/East_Timor\" class=\"mw-redirect\" title=\"East Timor\">East Timor</a> is <a href=\"https://wikipedia.org/wiki/Indonesian_invasion_of_East_Timor\" title=\"Indonesian invasion of East Timor\">annexed</a> and becomes the 27th <a href=\"https://wikipedia.org/wiki/Provinces_of_Indonesia\" title=\"Provinces of Indonesia\">province</a> of <a href=\"https://wikipedia.org/wiki/Indonesia\" title=\"Indonesia\">Indonesia</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/East_Timor\" class=\"mw-redirect\" title=\"East Timor\">East Timor</a> is <a href=\"https://wikipedia.org/wiki/Indonesian_invasion_of_East_Timor\" title=\"Indonesian invasion of East Timor\">annexed</a> and becomes the 27th <a href=\"https://wikipedia.org/wiki/Provinces_of_Indonesia\" title=\"Provinces of Indonesia\">province</a> of <a href=\"https://wikipedia.org/wiki/Indonesia\" title=\"Indonesia\">Indonesia</a>.", "links": [{"title": "East Timor", "link": "https://wikipedia.org/wiki/East_Timor"}, {"title": "Indonesian invasion of East Timor", "link": "https://wikipedia.org/wiki/Indonesian_invasion_of_East_Timor"}, {"title": "Provinces of Indonesia", "link": "https://wikipedia.org/wiki/Provinces_of_Indonesia"}, {"title": "Indonesia", "link": "https://wikipedia.org/wiki/Indonesia"}]}, {"year": "1976", "text": "The opening of the Summer Olympics in Montreal is marred by 25 African teams boycotting the games because of New Zealand's participation. Contrary to rulings by other international sports organizations, the IOC had declined to exclude New Zealand because of their participation in South African sporting events during apartheid.", "html": "1976 - The opening of the <a href=\"https://wikipedia.org/wiki/1976_Summer_Olympics\" title=\"1976 Summer Olympics\">Summer Olympics</a> in <a href=\"https://wikipedia.org/wiki/Montreal\" title=\"Montreal\">Montreal</a> is marred by 25 African teams boycotting the games because of <a href=\"https://wikipedia.org/wiki/New_Zealand\" title=\"New Zealand\">New Zealand</a>'s participation. Contrary to rulings by other international sports organizations, the <a href=\"https://wikipedia.org/wiki/IOC\" class=\"mw-redirect\" title=\"IOC\">IOC</a> had declined to exclude New Zealand because of their participation in <a href=\"https://wikipedia.org/wiki/South_Africa\" title=\"South Africa\">South African</a> sporting events during <a href=\"https://wikipedia.org/wiki/Apartheid\" title=\"Apartheid\">apartheid</a>.", "no_year_html": "The opening of the <a href=\"https://wikipedia.org/wiki/1976_Summer_Olympics\" title=\"1976 Summer Olympics\">Summer Olympics</a> in <a href=\"https://wikipedia.org/wiki/Montreal\" title=\"Montreal\">Montreal</a> is marred by 25 African teams boycotting the games because of <a href=\"https://wikipedia.org/wiki/New_Zealand\" title=\"New Zealand\">New Zealand</a>'s participation. Contrary to rulings by other international sports organizations, the <a href=\"https://wikipedia.org/wiki/IOC\" class=\"mw-redirect\" title=\"IOC\">IOC</a> had declined to exclude New Zealand because of their participation in <a href=\"https://wikipedia.org/wiki/South_Africa\" title=\"South Africa\">South African</a> sporting events during <a href=\"https://wikipedia.org/wiki/Apartheid\" title=\"Apartheid\">apartheid</a>.", "links": [{"title": "1976 Summer Olympics", "link": "https://wikipedia.org/wiki/1976_Summer_Olympics"}, {"title": "Montreal", "link": "https://wikipedia.org/wiki/Montreal"}, {"title": "New Zealand", "link": "https://wikipedia.org/wiki/New_Zealand"}, {"title": "IOC", "link": "https://wikipedia.org/wiki/IOC"}, {"title": "South Africa", "link": "https://wikipedia.org/wiki/South_Africa"}, {"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>heid"}]}, {"year": "1979", "text": "Nicaraguan dictator General <PERSON><PERSON><PERSON> resigns and flees to Miami, Florida, United States.", "html": "1979 - <a href=\"https://wikipedia.org/wiki/Nicaragua\" title=\"Nicaragua\">Nicaraguan</a> dictator General <a href=\"https://wikipedia.org/wiki/Anastasio_Somoza_<PERSON>le\" title=\"Anastasio Somo<PERSON>\">Anasta<PERSON></a> resigns and flees to <a href=\"https://wikipedia.org/wiki/Miami,_Florida\" class=\"mw-redirect\" title=\"Miami, Florida\">Miami, Florida</a>, United States.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Nicaragua\" title=\"Nicaragua\">Nicaraguan</a> dictator General <a href=\"https://wikipedia.org/wiki/Anastasio_Somoza_<PERSON>le\" title=\"Anastasio Somo<PERSON>\">Anastasio <PERSON></a> resigns and flees to <a href=\"https://wikipedia.org/wiki/Miami,_Florida\" class=\"mw-redirect\" title=\"Miami, Florida\">Miami, Florida</a>, United States.", "links": [{"title": "Nicaragua", "link": "https://wikipedia.org/wiki/Nicaragua"}, {"title": "Anastasio So<PERSON>", "link": "https://wikipedia.org/wiki/Anastasio_<PERSON><PERSON><PERSON>_<PERSON>"}, {"title": "Miami, Florida", "link": "https://wikipedia.org/wiki/Miami,_Florida"}]}, {"year": "1981", "text": "A structural failure leads to the collapse of a walkway at the Hyatt Regency in Kansas City, Missouri, killing 114 people and injuring more than 200.", "html": "1981 - A structural failure leads to the collapse of <a href=\"https://wikipedia.org/wiki/Hyatt_Regency_walkway_collapse\" title=\"Hyatt Regency walkway collapse\">a walkway at the Hyatt Regency</a> in <a href=\"https://wikipedia.org/wiki/Kansas_City,_Missouri\" title=\"Kansas City, Missouri\">Kansas City, Missouri</a>, killing 114 people and injuring more than 200.", "no_year_html": "A structural failure leads to the collapse of <a href=\"https://wikipedia.org/wiki/Hyatt_Regency_walkway_collapse\" title=\"Hyatt Regency walkway collapse\">a walkway at the Hyatt Regency</a> in <a href=\"https://wikipedia.org/wiki/Kansas_City,_Missouri\" title=\"Kansas City, Missouri\">Kansas City, Missouri</a>, killing 114 people and injuring more than 200.", "links": [{"title": "Hyatt Regency walkway collapse", "link": "https://wikipedia.org/wiki/Hyatt_Regency_walkway_collapse"}, {"title": "Kansas City, Missouri", "link": "https://wikipedia.org/wiki/Kansas_City,_Missouri"}]}, {"year": "1984", "text": "The national drinking age in the United States was changed from 18 to 21.", "html": "1984 - The national drinking age in the United States was <a href=\"https://wikipedia.org/wiki/National_Minimum_Drinking_Age_Act\" title=\"National Minimum Drinking Age Act\">changed</a> from 18 to 21.", "no_year_html": "The national drinking age in the United States was <a href=\"https://wikipedia.org/wiki/National_Minimum_Drinking_Age_Act\" title=\"National Minimum Drinking Age Act\">changed</a> from 18 to 21.", "links": [{"title": "National Minimum Drinking Age Act", "link": "https://wikipedia.org/wiki/National_Minimum_Drinking_Age_Act"}]}, {"year": "1985", "text": "Founding of the EUREKA Network by former head of states <PERSON> (France) and <PERSON> (Germany).", "html": "1985 - Founding of the <a href=\"https://wikipedia.org/wiki/Eureka_(organization)\" class=\"mw-redirect\" title=\"Eureka (organization)\">EUREKA Network</a> by former head of states <a href=\"https://wikipedia.org/wiki/Fran%C3%<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> (France) and <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> (Germany).", "no_year_html": "Founding of the <a href=\"https://wikipedia.org/wiki/Eureka_(organization)\" class=\"mw-redirect\" title=\"Eureka (organization)\">EUREKA Network</a> by former head of states <a href=\"https://wikipedia.org/wiki/Fran%C3%<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> (France) and <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> (Germany).", "links": [{"title": "Eureka (organization)", "link": "https://wikipedia.org/wiki/Eureka_(organization)"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Fran%C3%A<PERSON><PERSON>_<PERSON>"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1989", "text": "First flight of the B-2 Spirit Stealth Bomber.", "html": "1989 - First flight of the <a href=\"https://wikipedia.org/wiki/Northrop_Grumman_B-2_Spirit\" class=\"mw-redirect\" title=\"Northrop Grumman B-2 Spirit\">B-2 Spirit</a> Stealth Bomber.", "no_year_html": "First flight of the <a href=\"https://wikipedia.org/wiki/Northrop_Grumman_B-2_Spirit\" class=\"mw-redirect\" title=\"Northrop Grumman B-2 Spirit\">B-2 Spirit</a> Stealth Bomber.", "links": [{"title": "Northrop Grumman B-2 Spirit", "link": "https://wikipedia.org/wiki/Northrop_Grumman_B-2_Spirit"}]}, {"year": "1989", "text": "Holy See-Poland relations are restored.", "html": "1989 - <a href=\"https://wikipedia.org/wiki/Holy_See%E2%80%93Poland_relations\" title=\"Holy See-Poland relations\">Holy See-Poland relations</a> are restored.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Holy_See%E2%80%93Poland_relations\" title=\"Holy See-Poland relations\">Holy See-Poland relations</a> are restored.", "links": [{"title": "Holy See-Poland relations", "link": "https://wikipedia.org/wiki/Holy_See%E2%80%93Poland_relations"}]}, {"year": "1996", "text": "TWA Flight 800: Off the coast of Long Island, New York, a Paris-bound TWA Boeing 747 explodes, killing all 230 on board.", "html": "1996 - <a href=\"https://wikipedia.org/wiki/TWA_Flight_800\" title=\"TWA Flight 800\">TWA Flight 800</a>: Off the coast of <a href=\"https://wikipedia.org/wiki/Long_Island\" title=\"Long Island\">Long Island</a>, New York, a Paris-bound TWA <a href=\"https://wikipedia.org/wiki/Boeing_747\" title=\"Boeing 747\">Boeing 747</a> explodes, killing all 230 on board.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/TWA_Flight_800\" title=\"TWA Flight 800\">TWA Flight 800</a>: Off the coast of <a href=\"https://wikipedia.org/wiki/Long_Island\" title=\"Long Island\">Long Island</a>, New York, a Paris-bound TWA <a href=\"https://wikipedia.org/wiki/Boeing_747\" title=\"Boeing 747\">Boeing 747</a> explodes, killing all 230 on board.", "links": [{"title": "TWA Flight 800", "link": "https://wikipedia.org/wiki/TWA_Flight_800"}, {"title": "Long Island", "link": "https://wikipedia.org/wiki/Long_Island"}, {"title": "Boeing 747", "link": "https://wikipedia.org/wiki/Boeing_747"}]}, {"year": "1997", "text": "After takeoff from Husein Sastranegara International Airport, Sempati Air Flight 304 crashes into a residential neighborhood in Bandung, killing 28 people.", "html": "1997 - After takeoff from <a href=\"https://wikipedia.org/wiki/Husein_Sastranegara_International_Airport\" class=\"mw-redirect\" title=\"Husein Sastranegara International Airport\">Husein Sastranegara International Airport</a>, <a href=\"https://wikipedia.org/wiki/Sempati_Air_Flight_304\" title=\"Sempati Air Flight 304\">Sempati Air Flight 304</a> crashes into a residential neighborhood in <a href=\"https://wikipedia.org/wiki/Bandung\" title=\"Bandung\">Bandung</a>, killing 28 people.", "no_year_html": "After takeoff from <a href=\"https://wikipedia.org/wiki/Husein_Sastranegara_International_Airport\" class=\"mw-redirect\" title=\"Husein Sastranegara International Airport\">Husein Sastranegara International Airport</a>, <a href=\"https://wikipedia.org/wiki/Sempati_Air_Flight_304\" title=\"Sempati Air Flight 304\">Sempati Air Flight 304</a> crashes into a residential neighborhood in <a href=\"https://wikipedia.org/wiki/Bandung\" title=\"Bandung\">Bandung</a>, killing 28 people.", "links": [{"title": "Husein Sastranegara International Airport", "link": "https://wikipedia.org/wiki/Husein_Sastranegara_International_Airport"}, {"title": "Sempati Air Flight 304", "link": "https://wikipedia.org/wiki/Sempati_Air_Flight_304"}, {"title": "Bandung", "link": "https://wikipedia.org/wiki/Bandung"}]}, {"year": "1998", "text": "The 7.0 Mw  Papua New Guinea earthquake triggers a tsunami that destroys ten villages in Papua New Guinea, killing up to 2,700 people, and leaving several thousand injured.", "html": "1998 - The 7.0 M<sub>w</sub>  <a href=\"https://wikipedia.org/wiki/1998_Papua_New_Guinea_earthquake\" title=\"1998 Papua New Guinea earthquake\">Papua New Guinea earthquake</a> triggers a <a href=\"https://wikipedia.org/wiki/Tsunami\" title=\"Tsunami\">tsunami</a> that destroys ten villages in <a href=\"https://wikipedia.org/wiki/Papua_New_Guinea\" title=\"Papua New Guinea\">Papua New Guinea</a>, killing up to 2,700 people, and leaving several thousand injured.", "no_year_html": "The 7.0 M<sub>w</sub>  <a href=\"https://wikipedia.org/wiki/1998_Papua_New_Guinea_earthquake\" title=\"1998 Papua New Guinea earthquake\">Papua New Guinea earthquake</a> triggers a <a href=\"https://wikipedia.org/wiki/Tsunami\" title=\"Tsunami\">tsunami</a> that destroys ten villages in <a href=\"https://wikipedia.org/wiki/Papua_New_Guinea\" title=\"Papua New Guinea\">Papua New Guinea</a>, killing up to 2,700 people, and leaving several thousand injured.", "links": [{"title": "1998 Papua New Guinea earthquake", "link": "https://wikipedia.org/wiki/1998_Papua_New_Guinea_earthquake"}, {"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Tsunami"}, {"title": "Papua New Guinea", "link": "https://wikipedia.org/wiki/Papua_New_Guinea"}]}, {"year": "1998", "text": "A diplomatic conference adopts the Rome Statute of the International Criminal Court, establishing the permanent international court in The Hague, to prosecute individuals for genocide, crimes against humanity, war crimes, and the crime of aggression.", "html": "1998 - A diplomatic conference adopts the <a href=\"https://wikipedia.org/wiki/Rome_Statute_of_the_International_Criminal_Court\" class=\"mw-redirect\" title=\"Rome Statute of the International Criminal Court\">Rome Statute of the International Criminal Court</a>, establishing the <a href=\"https://wikipedia.org/wiki/International_Criminal_Court\" title=\"International Criminal Court\">permanent international court</a> in <a href=\"https://wikipedia.org/wiki/The_Hague\" title=\"The Hague\">The Hague</a>, to prosecute individuals for <a href=\"https://wikipedia.org/wiki/Genocide\" title=\"Genocide\">genocide</a>, <a href=\"https://wikipedia.org/wiki/Crimes_against_humanity\" title=\"Crimes against humanity\">crimes against humanity</a>, <a href=\"https://wikipedia.org/wiki/War_crime\" title=\"War crime\">war crimes</a>, and the <a href=\"https://wikipedia.org/wiki/Crime_of_aggression\" title=\"Crime of aggression\">crime of aggression</a>.", "no_year_html": "A diplomatic conference adopts the <a href=\"https://wikipedia.org/wiki/Rome_Statute_of_the_International_Criminal_Court\" class=\"mw-redirect\" title=\"Rome Statute of the International Criminal Court\">Rome Statute of the International Criminal Court</a>, establishing the <a href=\"https://wikipedia.org/wiki/International_Criminal_Court\" title=\"International Criminal Court\">permanent international court</a> in <a href=\"https://wikipedia.org/wiki/The_Hague\" title=\"The Hague\">The Hague</a>, to prosecute individuals for <a href=\"https://wikipedia.org/wiki/Genocide\" title=\"Genocide\">genocide</a>, <a href=\"https://wikipedia.org/wiki/Crimes_against_humanity\" title=\"Crimes against humanity\">crimes against humanity</a>, <a href=\"https://wikipedia.org/wiki/War_crime\" title=\"War crime\">war crimes</a>, and the <a href=\"https://wikipedia.org/wiki/Crime_of_aggression\" title=\"Crime of aggression\">crime of aggression</a>.", "links": [{"title": "Rome Statute of the International Criminal Court", "link": "https://wikipedia.org/wiki/Rome_Statute_of_the_International_Criminal_Court"}, {"title": "International Criminal Court", "link": "https://wikipedia.org/wiki/International_Criminal_Court"}, {"title": "The Hague", "link": "https://wikipedia.org/wiki/The_Hague"}, {"title": "Genocide", "link": "https://wikipedia.org/wiki/Genocide"}, {"title": "Crimes against humanity", "link": "https://wikipedia.org/wiki/Crimes_against_humanity"}, {"title": "War crime", "link": "https://wikipedia.org/wiki/War_crime"}, {"title": "Crime of aggression", "link": "https://wikipedia.org/wiki/Crime_of_aggression"}]}, {"year": "2000", "text": "During approach to Lok Nayak Jayaprakash Airport, Alliance Air Flight 7412 suddenly crashes into a residential neighborhood in Patna, India, killing 60 people.", "html": "2000 - During approach to <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_Airport\" title=\"Jay Prakash Narayan Airport\">Lok Nayak Jayaprakash Airport</a>, <a href=\"https://wikipedia.org/wiki/Alliance_Air_Flight_7412\" title=\"Alliance Air Flight 7412\">Alliance Air Flight 7412</a> suddenly crashes into a residential neighborhood in <a href=\"https://wikipedia.org/wiki/Patna\" title=\"Patna\">Patna</a>, <a href=\"https://wikipedia.org/wiki/India\" title=\"India\">India</a>, killing 60 people.", "no_year_html": "During approach to <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_Airport\" title=\"Jay Prakash Narayan Airport\">Lok Nayak Jayaprakash Airport</a>, <a href=\"https://wikipedia.org/wiki/Alliance_Air_Flight_7412\" title=\"Alliance Air Flight 7412\">Alliance Air Flight 7412</a> suddenly crashes into a residential neighborhood in <a href=\"https://wikipedia.org/wiki/Patna\" title=\"Patna\">Patna</a>, <a href=\"https://wikipedia.org/wiki/India\" title=\"India\">India</a>, killing 60 people.", "links": [{"title": "Jay <PERSON> Airport", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_Airport"}, {"title": "Alliance Air Flight 7412", "link": "https://wikipedia.org/wiki/Alliance_Air_Flight_7412"}, {"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Patna"}, {"title": "India", "link": "https://wikipedia.org/wiki/India"}]}, {"year": "2001", "text": "Concorde is brought back into service nearly a year after the July 2000 crash.", "html": "2001 - <a href=\"https://wikipedia.org/wiki/Concorde\" title=\"Concorde\">Concorde</a> is brought back into service nearly a year after the <a href=\"https://wikipedia.org/wiki/Air_France_Flight_4590\" title=\"Air France Flight 4590\">July 2000</a> crash.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Concorde\" title=\"Concorde\">Concorde</a> is brought back into service nearly a year after the <a href=\"https://wikipedia.org/wiki/Air_France_Flight_4590\" title=\"Air France Flight 4590\">July 2000</a> crash.", "links": [{"title": "Concorde", "link": "https://wikipedia.org/wiki/Concorde"}, {"title": "Air France Flight 4590", "link": "https://wikipedia.org/wiki/Air_France_Flight_4590"}]}, {"year": "2006", "text": "The 7.7 Mw  Pangandaran tsunami earthquake severely affects the Indonesian island of Java, killing 668 people, and leaving more than 9,000 injured.", "html": "2006 - The 7.7 M<sub>w</sub>  <a href=\"https://wikipedia.org/wiki/2006_Pangandaran_earthquake_and_tsunami\" title=\"2006 Pangandaran earthquake and tsunami\">Pangandaran tsunami earthquake</a> severely affects the Indonesian island of <a href=\"https://wikipedia.org/wiki/Java\" title=\"Java\">Java</a>, killing 668 people, and leaving more than 9,000 injured.", "no_year_html": "The 7.7 M<sub>w</sub>  <a href=\"https://wikipedia.org/wiki/2006_Pangandaran_earthquake_and_tsunami\" title=\"2006 Pangandaran earthquake and tsunami\">Pangandaran tsunami earthquake</a> severely affects the Indonesian island of <a href=\"https://wikipedia.org/wiki/Java\" title=\"Java\">Java</a>, killing 668 people, and leaving more than 9,000 injured.", "links": [{"title": "2006 Pangandaran earthquake and tsunami", "link": "https://wikipedia.org/wiki/2006_Pangandaran_earthquake_and_tsunami"}, {"title": "Java", "link": "https://wikipedia.org/wiki/Java"}]}, {"year": "2007", "text": "TAM Airlines Flight 3054, an Airbus A320, crashes into a warehouse after landing too fast and missing the end of the São Paulo-Congonhas Airport runway, killing 199 people.", "html": "2007 - <a href=\"https://wikipedia.org/wiki/TAM_Airlines_Flight_3054\" title=\"TAM Airlines Flight 3054\">TAM Airlines Flight 3054</a>, an <a href=\"https://wikipedia.org/wiki/Airbus_A320_family\" title=\"Airbus A320 family\">Airbus A320</a>, crashes into a warehouse after landing too fast and missing the end of the <a href=\"https://wikipedia.org/wiki/S%C3%A3o_Paulo%E2%80%93Congonhas_Airport\" title=\"São Paulo-Congonhas Airport\">São Paulo-Congonhas Airport</a> runway, killing 199 people.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/TAM_Airlines_Flight_3054\" title=\"TAM Airlines Flight 3054\">TAM Airlines Flight 3054</a>, an <a href=\"https://wikipedia.org/wiki/Airbus_A320_family\" title=\"Airbus A320 family\">Airbus A320</a>, crashes into a warehouse after landing too fast and missing the end of the <a href=\"https://wikipedia.org/wiki/S%C3%A3o_Paulo%E2%80%93Congonhas_Airport\" title=\"São Paulo-Congonhas Airport\">São Paulo-Congonhas Airport</a> runway, killing 199 people.", "links": [{"title": "TAM Airlines Flight 3054", "link": "https://wikipedia.org/wiki/TAM_Airlines_Flight_3054"}, {"title": "Airbus A320 family", "link": "https://wikipedia.org/wiki/Airbus_A320_family"}, {"title": "São Paulo-Congonhas Airport", "link": "https://wikipedia.org/wiki/S%C3%A3o_Paulo%E2%80%93Congonhas_Airport"}]}, {"year": "2014", "text": "Malaysia Airlines Flight 17, a Boeing 777, crashes near the border of Ukraine and Russia after being shot down. All 298 people on board are killed.", "html": "2014 - <a href=\"https://wikipedia.org/wiki/Malaysia_Airlines_Flight_17\" title=\"Malaysia Airlines Flight 17\">Malaysia Airlines Flight 17</a>, a <a href=\"https://wikipedia.org/wiki/Boeing_777\" title=\"Boeing 777\">Boeing 777</a>, crashes near the border of <a href=\"https://wikipedia.org/wiki/Ukraine\" title=\"Ukraine\">Ukraine</a> and Russia after being shot down. All 298 people on board are killed.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Malaysia_Airlines_Flight_17\" title=\"Malaysia Airlines Flight 17\">Malaysia Airlines Flight 17</a>, a <a href=\"https://wikipedia.org/wiki/Boeing_777\" title=\"Boeing 777\">Boeing 777</a>, crashes near the border of <a href=\"https://wikipedia.org/wiki/Ukraine\" title=\"Ukraine\">Ukraine</a> and Russia after being shot down. All 298 people on board are killed.", "links": [{"title": "Malaysia Airlines Flight 17", "link": "https://wikipedia.org/wiki/Malaysia_Airlines_Flight_17"}, {"title": "Boeing 777", "link": "https://wikipedia.org/wiki/Boeing_777"}, {"title": "Ukraine", "link": "https://wikipedia.org/wiki/Ukraine"}]}, {"year": "2014", "text": "A French regional train on the Pau-Bayonne line crashes into a high-speed train near the town of Denguin, resulting in at least 25 injuries.", "html": "2014 - A French <a href=\"https://wikipedia.org/wiki/Transport_express_r%C3%A9gional\" title=\"Transport express régional\">regional train</a> on the Pau-Bayonne line <a href=\"https://wikipedia.org/wiki/Denguin_rail_crash\" title=\"Denguin rail crash\">crashes</a> into a <a href=\"https://wikipedia.org/wiki/TGV\" title=\"TGV\">high-speed train</a> near the town of <a href=\"https://wikipedia.org/wiki/Denguin\" title=\"Denguin\">Denguin</a>, resulting in at least 25 injuries.", "no_year_html": "A French <a href=\"https://wikipedia.org/wiki/Transport_express_r%C3%A9gional\" title=\"Transport express régional\">regional train</a> on the Pau-Bayonne line <a href=\"https://wikipedia.org/wiki/Denguin_rail_crash\" title=\"Denguin rail crash\">crashes</a> into a <a href=\"https://wikipedia.org/wiki/TGV\" title=\"TGV\">high-speed train</a> near the town of <a href=\"https://wikipedia.org/wiki/Denguin\" title=\"Denguin\">Denguin</a>, resulting in at least 25 injuries.", "links": [{"title": "Transport express régional", "link": "https://wikipedia.org/wiki/Transport_express_r%C3%A9gional"}, {"title": "Denguin rail crash", "link": "https://wikipedia.org/wiki/Denguin_rail_crash"}, {"title": "TGV", "link": "https://wikipedia.org/wiki/TGV"}, {"title": "Den<PERSON>", "link": "https://wikipedia.org/wiki/Denguin"}]}, {"year": "2014", "text": "<PERSON> is killed by police officer <PERSON> in New York City, after the latter put him in a prohibited chokehold while arresting him.", "html": "2014 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a> is <a href=\"https://wikipedia.org/wiki/Killing_of_<PERSON>\" title=\"Killing of <PERSON>\">killed</a> by police officer <a href=\"https://wikipedia.org/wiki/<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a> in New York City, after the latter put him in a prohibited <a href=\"https://wikipedia.org/wiki/Chokehold\" title=\"Chokehold\">chokehold</a> while arresting him.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a> is <a href=\"https://wikipedia.org/wiki/Killing_of_<PERSON>\" title=\"Killing of <PERSON>\">killed</a> by police officer <a href=\"https://wikipedia.org/wiki/<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a> in New York City, after the latter put him in a prohibited <a href=\"https://wikipedia.org/wiki/Chokehold\" title=\"Chokehold\">chokehold</a> while arresting him.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Killing of <PERSON>", "link": "https://wikipedia.org/wiki/Killing_of_<PERSON>_<PERSON>"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Chokehold", "link": "https://wikipedia.org/wiki/Chokehold"}]}, {"year": "2015", "text": "At least 120 people are killed and 130 injured by a suicide bombing in Diyala Governorate, Iraq.", "html": "2015 - At least 120 people are killed and 130 injured by a <a href=\"https://wikipedia.org/wiki/2015_Khan_Ban<PERSON>_<PERSON>_bombing\" title=\"2015 Khan Bani Saad bombing\">suicide bombing</a> in <a href=\"https://wikipedia.org/wiki/Diyala_Governorate\" title=\"Diyala Governorate\">Diyala Governorate</a>, Iraq.", "no_year_html": "At least 120 people are killed and 130 injured by a <a href=\"https://wikipedia.org/wiki/2015_Khan_Ban<PERSON>_Saad_bombing\" title=\"2015 Khan Bani Saad bombing\">suicide bombing</a> in <a href=\"https://wikipedia.org/wiki/Diyala_Governorate\" title=\"Diyala Governorate\">Diyala Governorate</a>, Iraq.", "links": [{"title": "2015 Khan <PERSON> bombing", "link": "https://wikipedia.org/wiki/2015_<PERSON>_<PERSON>_<PERSON>_bombing"}, {"title": "Diyala Governorate", "link": "https://wikipedia.org/wiki/Diyala_Governorate"}]}, {"year": "2018", "text": "<PERSON> announces that his team has discovered a dozen irregular moons of Jupiter.", "html": "2018 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> announces that his team has discovered a dozen irregular <a href=\"https://wikipedia.org/wiki/Moons_of_Jupiter\" title=\"Moons of Jupiter\">moons of Jupiter</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> announces that his team has discovered a dozen irregular <a href=\"https://wikipedia.org/wiki/Moons_of_Jupiter\" title=\"Moons of Jupiter\">moons of Jupiter</a>.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>"}, {"title": "Moons of Jupiter", "link": "https://wikipedia.org/wiki/Moons_of_Jupiter"}]}], "Births": [{"year": "1487", "text": "<PERSON> of Iran (d. 1524)", "html": "1487 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> I\"><PERSON></a> of Iran (d. 1524)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> I\"><PERSON></a> of Iran (d. 1524)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1499", "text": "<PERSON>, Italian noblewoman (d. 1543)", "html": "1499 - <a href=\"https://wikipedia.org/wiki/Maria_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian noblewoman (d. 1543)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Maria_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian noblewoman (d. 1543)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Maria_Sal<PERSON>ti"}]}, {"year": "1531", "text": "<PERSON>, Roman Catholic cardinal (d. 1574)", "html": "1531 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>%C3%A9<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Roman Catholic cardinal (d. 1574)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>%C3%A9<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Roman Catholic cardinal (d. 1574)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>r%C3%A9<PERSON>_Canaples"}]}, {"year": "1674", "text": "<PERSON>, English hymnwriter and theologian (d. 1748)", "html": "1674 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English hymnwriter and theologian (d. 1748)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English hymnwriter and theologian (d. 1748)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1695", "text": "<PERSON> of Leiningen-Dachsburg-Falkenburg-Heidesheim (d. 1766)", "html": "1695 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_of_Leiningen-Dachsburg-Falkenburg-Heidesheim\" class=\"mw-redirect\" title=\"<PERSON> of Leiningen-Dachsburg-Falkenburg-Heidesheim\"><PERSON> of Leiningen-Dachsburg-Falkenburg-Heidesheim</a> (d. 1766)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_of_Leiningen-Dachsburg-Falkenburg-Heidesheim\" class=\"mw-redirect\" title=\"<PERSON> of Leiningen-Dachsburg-Falkenburg-Heidesheim\"><PERSON> of Leiningen-Dachsburg-Falkenburg-Heidesheim</a> (d. 1766)", "links": [{"title": "<PERSON> of Leiningen-Dachsburg-Falkenburg-Heidesheim", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>_Leiningen-Dachsburg-Falkenburg-Heidesheim"}]}, {"year": "1698", "text": "<PERSON>, French mathematician and philosopher (d. 1759)", "html": "1698 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French mathematician and philosopher (d. 1759)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French mathematician and philosopher (d. 1759)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1708", "text": "<PERSON>, Margrave of Brandenburg-Bayreuth (d. 1769)", "html": "1708 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Margrave_of_Brandenburg-Bayreuth\" title=\"<PERSON>, Margrave of Brandenburg-Bayreuth\"><PERSON>, Margrave of Brandenburg-Bayreuth</a> (d. 1769)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Mar<PERSON>_of_Brandenburg-Bayreuth\" title=\"<PERSON>, Margrave of Brandenburg-Bayreuth\"><PERSON>, Margrave of Brandenburg-Bayreuth</a> (d. 1769)", "links": [{"title": "<PERSON>, Margrave of Brandenburg-Bayreuth", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_<PERSON><PERSON>_of_Brandenburg-Bayreuth"}]}, {"year": "1714", "text": "<PERSON>, German philosopher and academic (d. 1762)", "html": "1714 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German philosopher and academic (d. 1762)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German philosopher and academic (d. 1762)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1744", "text": "<PERSON><PERSON>, American merchant and politician, 5th Vice President of the United States (d. 1814)", "html": "1744 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American merchant and politician, 5th <a href=\"https://wikipedia.org/wiki/Vice_President_of_the_United_States\" title=\"Vice President of the United States\">Vice President of the United States</a> (d. 1814)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American merchant and politician, 5th <a href=\"https://wikipedia.org/wiki/Vice_President_of_the_United_States\" title=\"Vice President of the United States\">Vice President of the United States</a> (d. 1814)", "links": [{"title": "El<PERSON> Gerry", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}, {"title": "Vice President of the United States", "link": "https://wikipedia.org/wiki/Vice_President_of_the_United_States"}]}, {"year": "1763", "text": "<PERSON>, German-American businessman and philanthropist (d. 1848)", "html": "1763 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German-American businessman and philanthropist (d. 1848)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German-American businessman and philanthropist (d. 1848)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1774", "text": "<PERSON>, American minister and theologian (d. 1856)", "html": "1774 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(Quaker_minister)\" title=\"<PERSON> (Quaker minister)\"><PERSON></a>, American minister and theologian (d. 1856)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(Quaker_minister)\" title=\"<PERSON> (Quaker minister)\"><PERSON></a>, American minister and theologian (d. 1856)", "links": [{"title": "<PERSON> (Quaker minister)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON><PERSON>_(Quaker_minister)"}]}, {"year": "1797", "text": "<PERSON>, French painter and academic (d. 1856)", "html": "1797 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French painter and academic (d. 1856)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French painter and academic (d. 1856)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1823", "text": "<PERSON><PERSON>, American businessman, judge, and politician (d. 1910)", "html": "1823 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American businessman, judge, and politician (d. 1910)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American businessman, judge, and politician (d. 1910)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1831", "text": "<PERSON><PERSON><PERSON> Emperor of China (d. 1861)", "html": "1831 - <a href=\"https://wikipedia.org/wiki/Xi<PERSON><PERSON>_Emperor\" title=\"Xianfeng Emperor\"><PERSON><PERSON><PERSON> Emperor</a> of China (d. 1861)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Xi<PERSON><PERSON>_Emperor\" title=\"Xianfeng Emperor\"><PERSON><PERSON><PERSON> Emperor</a> of China (d. 1861)", "links": [{"title": "Xi<PERSON><PERSON> Emperor", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_Emperor"}]}, {"year": "1831", "text": "<PERSON><PERSON> of Qajar Iran (a. 1896)", "html": "1831 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>\" class=\"mw-redirect\" title=\"<PERSON><PERSON>\"><PERSON><PERSON> <PERSON><PERSON></a> of <a href=\"https://wikipedia.org/wiki/Qajar_Iran\" title=\"Qajar Iran\">Qajar Iran</a> (a. 1896)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON><PERSON>\"><PERSON><PERSON> <PERSON><PERSON></a> of <a href=\"https://wikipedia.org/wiki/Qajar_Iran\" title=\"Qajar Iran\">Qajar Iran</a> (a. 1896)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>"}, {"title": "Qajar Iran", "link": "https://wikipedia.org/wiki/Qajar_Iran"}]}, {"year": "1837", "text": "<PERSON><PERSON><PERSON>, Canadian lawyer, judge, and politician, 7th Secretary of State for Canada (d. 1886)", "html": "1837 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>\" title=\"<PERSON>\"><PERSON><PERSON><PERSON></a>, Canadian lawyer, judge, and politician, 7th <a href=\"https://wikipedia.org/wiki/Secretary_of_State_for_Canada\" title=\"Secretary of State for Canada\">Secretary of State for Canada</a> (d. 1886)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON><PERSON><PERSON></a>, Canadian lawyer, judge, and politician, 7th <a href=\"https://wikipedia.org/wiki/Secretary_of_State_for_Canada\" title=\"Secretary of State for Canada\">Secretary of State for Canada</a> (d. 1886)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}, {"title": "Secretary of State for Canada", "link": "https://wikipedia.org/wiki/Secretary_of_State_for_Canada"}]}, {"year": "1839", "text": "<PERSON><PERSON><PERSON><PERSON>, American engineer, invented the Shay locomotive (d. 1916)", "html": "1839 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, American engineer, invented the <a href=\"https://wikipedia.org/wiki/Shay_locomotive\" title=\"Shay locomotive\">Shay locomotive</a> (d. 1916)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, American engineer, invented the <a href=\"https://wikipedia.org/wiki/Shay_locomotive\" title=\"Shay locomotive\">Shay locomotive</a> (d. 1916)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>"}, {"title": "Shay locomotive", "link": "https://wikipedia.org/wiki/Shay_locomotive"}]}, {"year": "1853", "text": "<PERSON><PERSON>, Ukrainian-Austrian philosopher and academic (d. 1920)", "html": "1853 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Ukrainian-Austrian philosopher and academic (d. 1920)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Ukrainian-Austrian philosopher and academic (d. 1920)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>g"}]}, {"year": "1868", "text": "<PERSON>, Danish director and playwright (d. 1944)", "html": "1868 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Danish director and playwright (d. 1944)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Danish director and playwright (d. 1944)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1870", "text": "<PERSON>, Scottish soldier and bagpipe player (d. 1939)", "html": "1870 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish soldier and <a href=\"https://wikipedia.org/wiki/Bagpipe\" class=\"mw-redirect\" title=\"Bagpipe\">bagpipe</a> player (d. 1939)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish soldier and <a href=\"https://wikipedia.org/wiki/Bagpipe\" class=\"mw-redirect\" title=\"Bagpipe\">bagpipe</a> player (d. 1939)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "Bagpipe", "link": "https://wikipedia.org/wiki/Bagpipe"}]}, {"year": "1871", "text": "<PERSON><PERSON>, German-American painter and illustrator (d. 1956)", "html": "1871 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, German-American painter and illustrator (d. 1956)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, German-American painter and illustrator (d. 1956)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>er"}]}, {"year": "1873", "text": "<PERSON>, French painter (d. 1965)", "html": "1873 - <a href=\"https://wikipedia.org/wiki/<PERSON>_Ben<PERSON>\" title=\"Many Benner\"><PERSON></a>, French painter (d. 1965)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_Ben<PERSON>\" title=\"Many Benner\"><PERSON></a>, French painter (d. 1965)", "links": [{"title": "Many Benner", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1879", "text": "<PERSON>, Canadian ice hockey player, coach, and manager (d. 1960)", "html": "1879 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player, coach, and manager (d. 1960)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player, coach, and manager (d. 1960)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1882", "text": "<PERSON>, English admiral and politician, Lord Lieutenant of Somerset (d. 1949)", "html": "1882 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English admiral and politician, <a href=\"https://wikipedia.org/wiki/Lord_Lieutenant_of_Somerset\" title=\"Lord Lieutenant of Somerset\">Lord Lieutenant of Somerset</a> (d. 1949)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English admiral and politician, <a href=\"https://wikipedia.org/wiki/Lord_Lieutenant_of_Somerset\" title=\"Lord Lieutenant of Somerset\">Lord Lieutenant of Somerset</a> (d. 1949)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Lord Lieutenant of Somerset", "link": "https://wikipedia.org/wiki/Lord_Lieutenant_of_Somerset"}]}, {"year": "1888", "text": "<PERSON><PERSON><PERSON>, Ukrainian-Israeli novelist, short story writer and poet, Nobel Prize laureate (d. 1970)", "html": "1888 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Ukrainian-Israeli novelist, short story writer and poet, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Literature\" title=\"Nobel Prize in Literature\">Nobel Prize</a> laureate (d. 1970)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Ukrainian-Israeli novelist, short story writer and poet, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Literature\" title=\"Nobel Prize in Literature\">Nobel Prize</a> laureate (d. 1970)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON><PERSON>_<PERSON>"}, {"title": "Nobel Prize in Literature", "link": "https://wikipedia.org/wiki/Nobel_Prize_in_Literature"}]}, {"year": "1889", "text": "<PERSON><PERSON>, American lawyer and author (d. 1970)", "html": "1889 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American lawyer and author (d. 1970)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American lawyer and author (d. 1970)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1894", "text": "<PERSON>, Belgian priest, astronomer, and cosmologist (d. 1966)", "html": "1894 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%AEtre\" title=\"<PERSON>\"><PERSON></a>, Belgian priest, astronomer, and cosmologist (d. 1966)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%AEtre\" title=\"<PERSON>\"><PERSON></a>, Belgian priest, astronomer, and cosmologist (d. 1966)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%AEtre"}]}, {"year": "1896", "text": "<PERSON>, English RAF officer (d. 1919)", "html": "1896 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(RAF_officer)\" title=\"<PERSON> (RAF officer)\"><PERSON></a>, English RAF officer (d. 1919)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(RAF_officer)\" title=\"<PERSON> (RAF officer)\"><PERSON></a>, English RAF officer (d. 1919)", "links": [{"title": "<PERSON> (RAF officer)", "link": "https://wikipedia.org/wiki/<PERSON>(RAF_officer)"}]}, {"year": "1898", "text": "<PERSON><PERSON><PERSON>, American photographer (d. 1991)", "html": "1898 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American photographer (d. 1991)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American photographer (d. 1991)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>ren<PERSON>_<PERSON>"}]}, {"year": "1898", "text": "<PERSON><PERSON><PERSON>, Canadian soldier and cinematographer (d. 1999)", "html": "1898 - <a href=\"https://wikipedia.org/wiki/Osmond_Borradaile\" title=\"Osmond Borradaile\">Osmo<PERSON></a>, Canadian soldier and cinematographer (d. 1999)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>smo<PERSON>_Bo<PERSON>dai<PERSON>\" title=\"Osmond Borradaile\">Osmo<PERSON></a>, Canadian soldier and cinematographer (d. 1999)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Osmo<PERSON>_<PERSON>daile"}]}, {"year": "1899", "text": "<PERSON>, American actor and dancer (d. 1986)", "html": "1899 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor and dancer (d. 1986)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor and dancer (d. 1986)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1901", "text": "<PERSON>, Italian-American race car driver (d. 1994)", "html": "1901 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian-American race car driver (d. 1994)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian-American race car driver (d. 1994)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1901", "text": "<PERSON>, Polish poet and author (d. 1938)", "html": "1901 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C5%84ski\" title=\"<PERSON>\"><PERSON></a>, Polish poet and author (d. 1938)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C5%84ski\" title=\"<PERSON>\"><PERSON></a>, Polish poet and author (d. 1938)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Bruno_J<PERSON>e%C5%84ski"}]}, {"year": "1901", "text": "<PERSON>, Irish farmer and politician, Minister for Agriculture, Food and the Marine (d. 1982)", "html": "1901 - <a href=\"https://wikipedia.org/wiki/<PERSON>(politician)\" title=\"<PERSON> (politician)\"><PERSON></a>, Irish farmer and politician, <a href=\"https://wikipedia.org/wiki/Minister_for_Agriculture,_Food_and_the_Marine\" title=\"Minister for Agriculture, Food and the Marine\">Minister for Agriculture, Food and the Marine</a> (d. 1982)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>(politician)\" title=\"<PERSON> (politician)\"><PERSON></a>, Irish farmer and politician, <a href=\"https://wikipedia.org/wiki/Minister_for_Agriculture,_Food_and_the_Marine\" title=\"Minister for Agriculture, Food and the Marine\">Minister for Agriculture, Food and the Marine</a> (d. 1982)", "links": [{"title": "<PERSON> (politician)", "link": "https://wikipedia.org/wiki/<PERSON>_(politician)"}, {"title": "Minister for Agriculture, Food and the Marine", "link": "https://wikipedia.org/wiki/Minister_for_Agriculture,_Food_and_the_Marine"}]}, {"year": "1902", "text": "<PERSON>, Australian author and academic (d. 1983)", "html": "1902 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian author and academic (d. 1983)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian author and academic (d. 1983)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1905", "text": "<PERSON>, American actor (d. 1979)", "html": "1905 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor (d. 1979)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor (d. 1979)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1910", "text": "<PERSON>, Canadian lawyer and banker, 2nd Governor of the Bank of Canada (d. 2012)", "html": "1910 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian lawyer and banker, 2nd <a href=\"https://wikipedia.org/wiki/Governor_of_the_Bank_of_Canada\" title=\"Governor of the Bank of Canada\">Governor of the Bank of Canada</a> (d. 2012)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian lawyer and banker, 2nd <a href=\"https://wikipedia.org/wiki/Governor_of_the_Bank_of_Canada\" title=\"Governor of the Bank of Canada\">Governor of the Bank of Canada</a> (d. 2012)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "Governor of the Bank of Canada", "link": "https://wikipedia.org/wiki/Governor_of_the_Bank_of_Canada"}]}, {"year": "1910", "text": "<PERSON>, American chemist and microbiologist (d. 1953)", "html": "1910 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American chemist and microbiologist (d. 1953)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American chemist and microbiologist (d. 1953)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1911", "text": "<PERSON>, American trumpet player (d. 2014)", "html": "1911 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American trumpet player (d. 2014)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American trumpet player (d. 2014)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Lionel_<PERSON>"}]}, {"year": "1911", "text": "<PERSON>, German-Canadian psychiatrist and academic (d. 1999)", "html": "1911 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German-Canadian psychiatrist and academic (d. 1999)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German-Canadian psychiatrist and academic (d. 1999)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1912", "text": "<PERSON>, German race car driver (d. 1958)", "html": "1912 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German race car driver (d. 1958)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German race car driver (d. 1958)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1912", "text": "<PERSON>, Canadian-American radio and television host (d. 2010)", "html": "1912 - <a href=\"https://wikipedia.org/wiki/Art_Linkletter\" title=\"Art Linkletter\"><PERSON></a>, Canadian-American radio and television host (d. 2010)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Art_Linkletter\" title=\"<PERSON> Linkletter\"><PERSON></a>, Canadian-American radio and television host (d. 2010)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Art_Linkletter"}]}, {"year": "1913", "text": "<PERSON>, American architect, designed the Marina City Building (d. 1997)", "html": "1913 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American architect, designed the <a href=\"https://wikipedia.org/wiki/Marina_City\" title=\"Marina City\">Marina City Building</a> (d. 1997)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American architect, designed the <a href=\"https://wikipedia.org/wiki/Marina_City\" title=\"Marina City\">Marina City Building</a> (d. 1997)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Marina City", "link": "https://wikipedia.org/wiki/Marina_City"}]}, {"year": "1914", "text": "<PERSON>, American soprano and educator (d. 1990)", "html": "1914 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American soprano and educator (d. 1990)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American soprano and educator (d. 1990)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1915", "text": "<PERSON><PERSON><PERSON>, Indian actor, singer, and screenwriter (d. 1978)", "html": "1915 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Indian actor, singer, and screenwriter (d. 1978)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Indian actor, singer, and screenwriter (d. 1978)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1915", "text": "<PERSON>, American photographer and educator (d. 1985)", "html": "1915 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American photographer and educator (d. 1985)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American photographer and educator (d. 1985)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1916", "text": "<PERSON>, American economist and policymaker (d. 2007)", "html": "1916 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American economist and policymaker (d. 2007)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American economist and policymaker (d. 2007)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1917", "text": "<PERSON>, American baseball player and manager (d. 2001)", "html": "1917 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player and manager (d. 2001)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player and manager (d. 2001)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1917", "text": "<PERSON>, American actress, comedian, and voice artist (d. 2012)", "html": "1917 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress, comedian, and voice artist (d. 2012)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress, comedian, and voice artist (d. 2012)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1917", "text": "<PERSON><PERSON>, Turkish general and politician, 7th President of Turkey (d. 2015)", "html": "1917 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Turkish general and politician, 7th <a href=\"https://wikipedia.org/wiki/President_of_Turkey\" title=\"President of Turkey\">President of Turkey</a> (d. 2015)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Turkish general and politician, 7th <a href=\"https://wikipedia.org/wiki/President_of_Turkey\" title=\"President of Turkey\">President of Turkey</a> (d. 2015)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}, {"title": "President of Turkey", "link": "https://wikipedia.org/wiki/President_of_Turkey"}]}, {"year": "1917", "text": "<PERSON><PERSON>, French author (d. 1998)", "html": "1917 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, French author (d. 1998)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, French author (d. 1998)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1918", "text": "<PERSON>, Guatemalan soldier and politician, President of Guatemala (d. 2003)", "html": "1918 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Guatemalan soldier and politician, <a href=\"https://wikipedia.org/wiki/President_of_Guatemala\" title=\"President of Guatemala\">President of Guatemala</a> (d. 2003)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Guatemalan soldier and politician, <a href=\"https://wikipedia.org/wiki/President_of_Guatemala\" title=\"President of Guatemala\">President of Guatemala</a> (d. 2003)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>"}, {"title": "President of Guatemala", "link": "https://wikipedia.org/wiki/President_of_Guatemala"}]}, {"year": "1919", "text": "<PERSON>, English footballer (d. 2002)", "html": "1919 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English footballer (d. 2002)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English footballer (d. 2002)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1920", "text": "<PERSON>, American physicist and academic, invented the laser (d. 2005)", "html": "1920 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American physicist and academic, invented the <a href=\"https://wikipedia.org/wiki/Laser\" title=\"Laser\">laser</a> (d. 2005)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American physicist and academic, invented the <a href=\"https://wikipedia.org/wiki/Laser\" title=\"Laser\">laser</a> (d. 2005)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Laser", "link": "https://wikipedia.org/wiki/Laser"}]}, {"year": "1920", "text": "<PERSON>, Spanish businessman, 7th President of the International Olympic Committee (d. 2010)", "html": "1920 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Spanish businessman, 7th <a href=\"https://wikipedia.org/wiki/President_of_the_International_Olympic_Committee\" title=\"President of the International Olympic Committee\">President of the International Olympic Committee</a> (d. 2010)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Spanish businessman, 7th <a href=\"https://wikipedia.org/wiki/President_of_the_International_Olympic_Committee\" title=\"President of the International Olympic Committee\">President of the International Olympic Committee</a> (d. 2010)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "President of the International Olympic Committee", "link": "https://wikipedia.org/wiki/President_of_the_International_Olympic_Committee"}]}, {"year": "1921", "text": "<PERSON>, American guitarist, producer, and songwriter (d. 1977)", "html": "1921 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(musician)\" title=\"<PERSON> (musician)\"><PERSON></a>, American guitarist, producer, and songwriter (d. 1977)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>(musician)\" title=\"<PERSON> (musician)\"><PERSON></a>, American guitarist, producer, and songwriter (d. 1977)", "links": [{"title": "<PERSON> (musician)", "link": "https://wikipedia.org/wiki/<PERSON>_(musician)"}]}, {"year": "1921", "text": "<PERSON>, French mountaineer (d. 1955)", "html": "1921 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French mountaineer (d. 1955)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French mountaineer (d. 1955)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1921", "text": "<PERSON>, American guitarist (d. 1992)", "html": "1921 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American guitarist (d. 1992)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American guitarist (d. 1992)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1921", "text": "<PERSON>, American baseball player (d. 1996)", "html": "1921 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player (d. 1996)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player (d. 1996)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1921", "text": "<PERSON><PERSON><PERSON><PERSON>, Slovak actor (d. 2008)", "html": "1921 - <a href=\"https://wikipedia.org/wiki/Franti%C5%A1ek_Zvar%C3%ADk\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Slovak actor (d. 2008)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Franti%C5%A1ek_Zvar%C3%ADk\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Slovak actor (d. 2008)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Franti%C5%A1ek_Zvar%C3%ADk"}]}, {"year": "1923", "text": "<PERSON>, American psychologist (d. 1981)", "html": "1923 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American psychologist (d. 1981)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Jeanne <PERSON>\"><PERSON></a>, American psychologist (d. 1981)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Jeanne_<PERSON>"}]}, {"year": "1923", "text": "<PERSON>, English car designer, co-founded the Cooper Car Company (d. 2000)", "html": "1923 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(car_maker)\" title=\"<PERSON> (car maker)\"><PERSON></a>, English car designer, co-founded the <a href=\"https://wikipedia.org/wiki/Cooper_Car_Company\" title=\"Cooper Car Company\">Cooper Car Company</a> (d. 2000)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>(car_maker)\" title=\"<PERSON> (car maker)\"><PERSON></a>, English car designer, co-founded the <a href=\"https://wikipedia.org/wiki/Cooper_Car_Company\" title=\"Cooper Car Company\">Cooper Car Company</a> (d. 2000)", "links": [{"title": "<PERSON> (car maker)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(car_maker)"}, {"title": "Cooper Car Company", "link": "https://wikipedia.org/wiki/Cooper_Car_Company"}]}, {"year": "1924", "text": "<PERSON><PERSON><PERSON>, Canadian lawyer and politician, 26th Lieutenant Governor of British Columbia (d. 2013)", "html": "1924 - <a href=\"https://wikipedia.org/wiki/Garde_G<PERSON>om\" title=\"Garde Gardom\"><PERSON><PERSON><PERSON></a>, Canadian lawyer and politician, 26th <a href=\"https://wikipedia.org/wiki/Lieutenant_Governor_of_British_Columbia\" title=\"Lieutenant Governor of British Columbia\">Lieutenant Governor of British Columbia</a> (d. 2013)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Gard<PERSON>_<PERSON>\" title=\"<PERSON>arde Gardom\"><PERSON><PERSON><PERSON></a>, Canadian lawyer and politician, 26th <a href=\"https://wikipedia.org/wiki/Lieutenant_Governor_of_British_Columbia\" title=\"Lieutenant Governor of British Columbia\">Lieutenant Governor of British Columbia</a> (d. 2013)", "links": [{"title": "Garde Gardom", "link": "https://wikipedia.org/wiki/Garde_Gardom"}, {"title": "Lieutenant Governor of British Columbia", "link": "https://wikipedia.org/wiki/Lieutenant_Governor_of_British_Columbia"}]}, {"year": "1925", "text": "<PERSON>, American singer and actor (d. 2014)", "html": "1925 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer and actor (d. 2014)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer and actor (d. 2014)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1925", "text": "<PERSON>, Afghan politician", "html": "1925 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Afghan politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Afghan politician", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1926", "text": "<PERSON><PERSON><PERSON>, French-Canadian wrestler (d. 2010)", "html": "1926 - <a href=\"https://wikipedia.org/wiki/%C3%89<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, French-Canadian wrestler (d. 2010)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/%C3%89<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, French-Canadian wrestler (d. 2010)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/%C3%89<PERSON><PERSON>_<PERSON>pentier"}]}, {"year": "1926", "text": "<PERSON>, American activist and theorist (d. 2015)", "html": "1926 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American activist and theorist (d. 2015)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American activist and theorist (d. 2015)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1928", "text": "<PERSON>, American singer-songwriter and pianist (d. 1976)", "html": "1928 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and pianist (d. 1976)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and pianist (d. 1976)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1929", "text": "<PERSON>, American travel writer (d. 2024)", "html": "1929 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American travel writer (d. 2024)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American travel writer (d. 2024)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1929", "text": "<PERSON>, Russian mathematician and academic (d. 2023)", "html": "1929 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, Russian mathematician and academic (d. 2023)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, Russian mathematician and academic (d. 2023)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1932", "text": "<PERSON><PERSON><PERSON><PERSON>, Italian composer (d. 1996)", "html": "1932 - <a href=\"https://wikipedia.org/wiki/Niccol%C3%B2_Cast<PERSON>lioni\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Italian composer (d. 1996)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Niccol%C3%B2_Cast<PERSON>lioni\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Italian composer (d. 1996)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Niccol%C3%B2_Castiglioni"}]}, {"year": "1932", "text": "<PERSON>, American basketball player and coach (d. 2009)", "html": "1932 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, American basketball player and coach (d. 2009)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, American basketball player and coach (d. 2009)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_Kerr"}]}, {"year": "1932", "text": "<PERSON><PERSON><PERSON><PERSON><PERSON>, Polish pianist and composer (d. 2013)", "html": "1932 - <a href=\"https://wikipedia.org/wiki/Woj<PERSON><PERSON>_<PERSON>\" title=\"Woj<PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON><PERSON></a>, Polish pianist and composer (d. 2013)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Woj<PERSON><PERSON>_<PERSON>\" title=\"Woj<PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON><PERSON></a>, Polish pianist and composer (d. 2013)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Woj<PERSON><PERSON>_<PERSON>"}]}, {"year": "1932", "text": "<PERSON><PERSON>, American author and illustrator (d. 2009)", "html": "1932 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American author and illustrator (d. 2009)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American author and illustrator (d. 2009)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1932", "text": "<PERSON><PERSON>, American basketball player and coach (d. 2021)", "html": "1932 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON><PERSON> <PERSON>\"><PERSON><PERSON></a>, American basketball player and coach (d. 2021)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON><PERSON> <PERSON>\"><PERSON><PERSON></a>, American basketball player and coach (d. 2021)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1932", "text": "<PERSON>, Australian rugby league player (d. 1990)", "html": "1932 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian rugby league player (d. 1990)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian rugby league player (d. 1990)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1932", "text": "<PERSON><PERSON><PERSON>, Spanish-Argentinian cartoonist (d. 2020)", "html": "1932 - <a href=\"https://wikipedia.org/wiki/Quino\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Spanish-Argentinian cartoonist (d. 2020)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>uino\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Spanish-Argentinian cartoonist (d. 2020)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Quino"}]}, {"year": "1932", "text": "<PERSON>, American businessman, founded Publicis & Hal Riney (d. 2008)", "html": "1932 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American businessman, founded <a href=\"https://wikipedia.org/wiki/Publicis_%26_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON> &amp; <PERSON>\">Publicis &amp; <PERSON></a> (d. 2008)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American businessman, founded <a href=\"https://wikipedia.org/wiki/Publicis_%26_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON> &amp; <PERSON>\">Publicis &amp; <PERSON></a> (d. 2008)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Publicis & Hal <PERSON>", "link": "https://wikipedia.org/wiki/Publicis_%26_<PERSON>_<PERSON>"}]}, {"year": "1933", "text": "<PERSON><PERSON>, Japanese actress (d. 2014)", "html": "1933 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Japanese actress (d. 2014)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Japanese actress (d. 2014)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>ji"}]}, {"year": "1933", "text": "<PERSON><PERSON><PERSON><PERSON>, Maltese politician, 9th Prime Minister of Malta (d. 2022)", "html": "1933 - <a href=\"https://wikipedia.org/wiki/Karm<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Maltese politician, 9th <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Malta\" title=\"Prime Minister of Malta\">Prime Minister of Malta</a> (d. 2022)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Karm<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Maltese politician, 9th <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Malta\" title=\"Prime Minister of Malta\">Prime Minister of Malta</a> (d. 2022)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Karm<PERSON><PERSON>_<PERSON><PERSON><PERSON>_<PERSON>"}, {"title": "Prime Minister of Malta", "link": "https://wikipedia.org/wiki/Prime_Minister_of_Malta"}]}, {"year": "1933", "text": "<PERSON>, Canadian singer and comedian (d. 2024)", "html": "1933 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian singer and comedian (d. 2024)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian singer and comedian (d. 2024)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1933", "text": "<PERSON>, Zimbabwean-South African cricketer (d. 2006)", "html": "1933 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Zimbabwean-South African cricketer (d. 2006)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Zimbabwean-South African cricketer (d. 2006)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1934", "text": "<PERSON><PERSON>, Chinese-Filipino billionaire businessman and educator", "html": "1934 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Chinese-Filipino billionaire businessman and educator", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Chinese-Filipino billionaire businessman and educator", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1935", "text": "<PERSON><PERSON><PERSON>, American actress and singer (d. 2019)", "html": "1935 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American actress and singer (d. 2019)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American actress and singer (d. 2019)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1935", "text": "<PERSON>, American composer and educator (d. 2024)", "html": "1935 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American composer and educator (d. 2024)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American composer and educator (d. 2024)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1935", "text": "<PERSON>, Canadian actor and producer (d. 2024)", "html": "1935 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian actor and producer (d. 2024)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian actor and producer (d. 2024)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1938", "text": "<PERSON>, Belgian author and illustrator", "html": "1938 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Belgian author and illustrator", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Belgian author and illustrator", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1939", "text": "<PERSON><PERSON>, Canadian actress and politician (d. 2020)", "html": "1939 - <a href=\"https://wikipedia.org/wiki/Andr%C3%A9e_Champagne\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Canadian actress and politician (d. 2020)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Andr%C3%A9e_Champagne\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Canadian actress and politician (d. 2020)", "links": [{"title": "Andrée Champagne", "link": "https://wikipedia.org/wiki/Andr%C3%A9e_Champagne"}]}, {"year": "1939", "text": "<PERSON>, Welsh singer-songwriter and guitarist (d. 2020)", "html": "1939 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Welsh singer-songwriter and guitarist (d. 2020)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Welsh singer-songwriter and guitarist (d. 2020)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1940", "text": "<PERSON>-<PERSON>, English actor and screenwriter (d. 2020)", "html": "1940 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actor and screenwriter (d. 2020)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actor and screenwriter (d. 2020)", "links": [{"title": "<PERSON>-<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1941", "text": "<PERSON><PERSON>, American football player (d. 2022)", "html": "1941 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American football player (d. 2022)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American football player (d. 2022)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1941", "text": "<PERSON>, English cricketer", "html": "1941 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(cricketer)\" title=\"<PERSON> (cricketer)\"><PERSON></a>, English cricketer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(cricketer)\" title=\"<PERSON> (cricketer)\"><PERSON></a>, English cricketer", "links": [{"title": "<PERSON> (cricketer)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>(cricketer)"}]}, {"year": "1941", "text": "<PERSON><PERSON><PERSON>, German race car driver and manager", "html": "1941 - <a href=\"https://wikipedia.org/wiki/Achi<PERSON>_<PERSON>d\" title=\"<PERSON>chi<PERSON> Warm<PERSON>d\"><PERSON><PERSON><PERSON></a>, German race car driver and manager", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Achi<PERSON>_<PERSON>d\" title=\"<PERSON>chi<PERSON> Warm<PERSON>d\"><PERSON><PERSON><PERSON></a>, German race car driver and manager", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Achim_Warmbold"}]}, {"year": "1942", "text": "<PERSON>, New Zealand-born Canadian singer", "html": "1942 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, New Zealand-born Canadian singer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, New Zealand-born Canadian singer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1942", "text": "<PERSON>, American basketball player (d. 2017)", "html": "1942 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player (d. 2017)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player (d. 2017)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1942", "text": "<PERSON>, American baseball player and manager", "html": "1942 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player and manager", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player and manager", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1942", "text": "<PERSON><PERSON>, English singer-songwriter and keyboard player", "html": "1942 - <a href=\"https://wikipedia.org/wiki/Zoot_Money\" title=\"Zoot Money\"><PERSON><PERSON></a>, English singer-songwriter and keyboard player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Zoot_Money\" title=\"Zoot Money\"><PERSON><PERSON> Money</a>, English singer-songwriter and keyboard player", "links": [{"title": "Zoot Money", "link": "https://wikipedia.org/wiki/Zoot_Money"}]}, {"year": "1943", "text": "<PERSON><PERSON><PERSON><PERSON>, American author and educator", "html": "1943 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, American author and educator", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, American author and educator", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1944", "text": "<PERSON>, New Zealand cricketer and footballer", "html": "1944 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(cricketer)\" title=\"<PERSON> (cricketer)\"><PERSON></a>, New Zealand cricketer and footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>(cricketer)\" title=\"<PERSON> (cricketer)\"><PERSON></a>, New Zealand cricketer and footballer", "links": [{"title": "<PERSON> (cricketer)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(cricketer)"}]}, {"year": "1944", "text": "<PERSON>, Hungarian-English actress", "html": "1944 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Hungarian-English actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Hungarian-English actress", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1944", "text": "<PERSON>, Brazilian footballer and manager (d. 2016)", "html": "1944 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Brazilian footballer and manager (d. 2016)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Brazilian footballer and manager (d. 2016)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1945", "text": "<PERSON>, Crown Prince of Yugoslavia", "html": "1945 - <a href=\"https://wikipedia.org/wiki/<PERSON>,_Crown_Prince_of_Yugoslavia\" title=\"<PERSON>, Crown Prince of Yugoslavia\"><PERSON>, Crown Prince of Yugoslavia</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>,_Crown_Prince_of_Yugoslavia\" title=\"<PERSON>, Crown Prince of Yugoslavia\"><PERSON>, Crown Prince of Yugoslavia</a>", "links": [{"title": "<PERSON>, Crown Prince of Yugoslavia", "link": "https://wikipedia.org/wiki/<PERSON>,_Crown_Prince_of_Yugoslavia"}]}, {"year": "1945", "text": "<PERSON>, <PERSON>, English politician, Secretary of State for Education", "html": "1945 - <a href=\"https://wikipedia.org/wiki/<PERSON>,_Baron_<PERSON>\" title=\"<PERSON>, Baron <PERSON>\"><PERSON>, Baron <PERSON></a>, English politician, <a href=\"https://wikipedia.org/wiki/Secretary_of_State_for_Education\" title=\"Secretary of State for Education\">Secretary of State for Education</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>,_Baron_<PERSON>\" title=\"<PERSON>, Baron <PERSON>\"><PERSON>, Baron <PERSON></a>, English politician, <a href=\"https://wikipedia.org/wiki/Secretary_of_State_for_Education\" title=\"Secretary of State for Education\">Secretary of State for Education</a>", "links": [{"title": "<PERSON>, Baron <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>,_<PERSON>_<PERSON>"}, {"title": "Secretary of State for Education", "link": "https://wikipedia.org/wiki/Secretary_of_State_for_Education"}]}, {"year": "1946", "text": "<PERSON>, American novelist and short story writer", "html": "1946 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American novelist and short story writer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American novelist and short story writer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1946", "text": "<PERSON>, American POW/MIA activist (d. 2009)", "html": "1946 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American POW/MIA activist (d. 2009)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American POW/MIA activist (d. 2009)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1947", "text": "<PERSON>, Baroness <PERSON> of St John's, English educator and politician", "html": "1947 - <a href=\"https://wikipedia.org/wiki/<PERSON>,_Baroness_<PERSON><PERSON><PERSON>_of_St_John%27s\" class=\"mw-redirect\" title=\"<PERSON>, Baroness <PERSON> of St John's\"><PERSON>, Baroness <PERSON> of St John's</a>, English educator and politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>,_Baroness_<PERSON><PERSON>_of_St_John%27s\" class=\"mw-redirect\" title=\"<PERSON>, Baroness <PERSON> of St John's\"><PERSON>, Baroness <PERSON> of St John's</a>, English educator and politician", "links": [{"title": "<PERSON>, Baroness <PERSON> of St John's", "link": "https://wikipedia.org/wiki/<PERSON>,_Baroness_<PERSON><PERSON><PERSON>_of_St_John%27s"}]}, {"year": "1947", "text": "<PERSON>, German footballer and manager", "html": "1947 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German footballer and manager", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German footballer and manager", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1947", "text": "Queen <PERSON><PERSON> of the United Kingdom", "html": "1947 - <a href=\"https://wikipedia.org/wiki/Queen_Cam<PERSON>\" title=\"Queen Camilla\">Queen <PERSON><PERSON> of the United Kingdom</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Queen_Cam<PERSON>\" title=\"Queen Cam<PERSON>\">Queen <PERSON><PERSON> of the United Kingdom</a>", "links": [{"title": "Queen <PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Queen_<PERSON><PERSON>"}]}, {"year": "1947", "text": "<PERSON>, German musician (Kraftwerk)", "html": "1947 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>l%C3%BCr\" title=\"<PERSON>\"><PERSON></a>, German musician (<a href=\"https://wikipedia.org/wiki/Kraftwerk\" title=\"Kraftwerk\">Kraftwerk</a>)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%BCr\" title=\"<PERSON>\"><PERSON></a>, German musician (<a href=\"https://wikipedia.org/wiki/Kraftwerk\" title=\"Kraftwerk\">Kraftwerk</a>)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Wolfgang_Fl%C3%BCr"}, {"title": "Kraftwerk", "link": "https://wikipedia.org/wiki/Kraftwerk"}]}, {"year": "1947", "text": "<PERSON>, English rock drummer (<PERSON>) (d. 2002)", "html": "1947 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English rock drummer (<a href=\"https://wikipedia.org/wiki/The_Sweet\" title=\"The Sweet\">Sweet</a>) (d. 2002)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English rock drummer (<a href=\"https://wikipedia.org/wiki/The_Sweet\" title=\"The Sweet\">Sweet</a>) (d. 2002)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "The Sweet", "link": "https://wikipedia.org/wiki/The_Sweet"}]}, {"year": "1948", "text": "<PERSON>, American guitarist and songwriter (d. 2009)", "html": "1948 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American guitarist and songwriter (d. 2009)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American guitarist and songwriter (d. 2009)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1948", "text": "<PERSON>, Swiss director and producer (d. 2015)", "html": "1948 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swiss director and producer (d. 2015)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swiss director and producer (d. 2015)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1949", "text": "<PERSON><PERSON>, English bass player and songwriter", "html": "1949 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, English bass player and songwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, English bass player and songwriter", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1949", "text": "<PERSON>, American journalist and sportscaster", "html": "1949 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American journalist and sportscaster", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American journalist and sportscaster", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1950", "text": "<PERSON><PERSON>, Bangladeshi politician", "html": "1950 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Bangladeshi politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Bangladeshi politician", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1950", "text": "<PERSON><PERSON><PERSON>, Malaysian corporate figure", "html": "1950 - <a href=\"https://wikipedia.org/wiki/Teng<PERSON>_<PERSON>_<PERSON>\" title=\"Teng<PERSON> Sulai<PERSON>\"><PERSON>g<PERSON> Sulai<PERSON></a>, Malaysian corporate figure", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Teng<PERSON>_<PERSON>_<PERSON>\" title=\"Teng<PERSON> Sulai<PERSON>\"><PERSON><PERSON><PERSON></a>, Malaysian corporate figure", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1950", "text": "<PERSON>, American singer-songwriter and guitarist (d. 2011)", "html": "1950 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Snow\"><PERSON></a>, American singer-songwriter and guitarist (d. 2011)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Snow\"><PERSON></a>, American singer-songwriter and guitarist (d. 2011)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1951", "text": "<PERSON><PERSON>, American actress and singer", "html": "1951 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American actress and singer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American actress and singer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1951", "text": "<PERSON>, American journalist and author", "html": "1951 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American journalist and author", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American journalist and author", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1951", "text": "<PERSON>, English soldier and politician, Minister of State for the Armed Forces", "html": "1951 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English soldier and politician, <a href=\"https://wikipedia.org/wiki/Minister_of_State_for_the_Armed_Forces\" title=\"Minister of State for the Armed Forces\">Minister of State for the Armed Forces</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English soldier and politician, <a href=\"https://wikipedia.org/wiki/Minister_of_State_for_the_Armed_Forces\" title=\"Minister of State for the Armed Forces\">Minister of State for the Armed Forces</a>", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Minister of State for the Armed Forces", "link": "https://wikipedia.org/wiki/Minister_of_State_for_the_Armed_Forces"}]}, {"year": "1952", "text": "<PERSON>, American actor, singer, and producer", "html": "1952 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor, singer, and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor, singer, and producer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1952", "text": "<PERSON><PERSON>, American singer-songwriter (d. 1997)", "html": "1952 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American singer-songwriter (d. 1997)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American singer-songwriter (d. 1997)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1952", "text": "<PERSON><PERSON><PERSON>, Dutch singer-songwriter and guitarist (d. 2015)", "html": "1952 - <a href=\"https://wikipedia.org/wiki/Th%C3%A9_<PERSON>\" title=\"<PERSON><PERSON><PERSON> <PERSON>\"><PERSON><PERSON><PERSON></a>, Dutch singer-songwriter and guitarist (d. 2015)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Th%C3%A9_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Dutch singer-songwriter and guitarist (d. 2015)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Th%C3%A9_Lau"}]}, {"year": "1952", "text": "<PERSON>, American author", "html": "1952 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "1954", "text": "<PERSON>, German chemist and politician, Chancellor of Germany from 2005 to 2021.", "html": "1954 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German chemist and politician, <a href=\"https://wikipedia.org/wiki/Chancellor_of_Germany_(Federal_Republic_of_Germany)\" class=\"mw-redirect\" title=\"Chancellor of Germany (Federal Republic of Germany)\">Chancellor of Germany</a> from 2005 to 2021.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German chemist and politician, <a href=\"https://wikipedia.org/wiki/Chancellor_of_Germany_(Federal_Republic_of_Germany)\" class=\"mw-redirect\" title=\"Chancellor of Germany (Federal Republic of Germany)\">Chancellor of Germany</a> from 2005 to 2021.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Chancellor of Germany (Federal Republic of Germany)", "link": "https://wikipedia.org/wiki/Chancellor_of_Germany_(Federal_Republic_of_Germany)"}]}, {"year": "1954", "text": "<PERSON>, Vanuatuan politician, 6th Prime Minister of Vanuatu (d. 2015)", "html": "1954 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Vanuatuan politician, 6th <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Vanuatu\" title=\"Prime Minister of Vanuatu\">Prime Minister of Vanuatu</a> (d. 2015)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Vanuatuan politician, 6th <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Vanuatu\" title=\"Prime Minister of Vanuatu\">Prime Minister of Vanuatu</a> (d. 2015)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Prime Minister of Vanuatu", "link": "https://wikipedia.org/wiki/Prime_Minister_of_Vanuatu"}]}, {"year": "1954", "text": "<PERSON><PERSON> <PERSON>, American author, screenwriter, and producer", "html": "1954 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American author, screenwriter, and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American author, screenwriter, and producer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1955", "text": "<PERSON><PERSON><PERSON>, Canadian actress and screenwriter", "html": "1955 - <a href=\"https://wikipedia.org/wiki/Sylvie_L%C3%A9onard\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Canadian actress and screenwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>ylvie_L%C3%A9onard\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Canadian actress and screenwriter", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Sylvie_L%C3%A9onard"}]}, {"year": "1955", "text": "<PERSON>, American mycologist and author", "html": "1955 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American mycologist and author", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American mycologist and author", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Paul_<PERSON>"}]}, {"year": "1956", "text": "<PERSON>, Australian lawyer and politician, 38th Australian Minister for Foreign Affairs", "html": "1956 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian lawyer and politician, 38th <a href=\"https://wikipedia.org/wiki/Minister_for_Foreign_Affairs_(Australia)\" title=\"Minister for Foreign Affairs (Australia)\">Australian Minister for Foreign Affairs</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian lawyer and politician, 38th <a href=\"https://wikipedia.org/wiki/Minister_for_Foreign_Affairs_(Australia)\" title=\"Minister for Foreign Affairs (Australia)\">Australian Minister for Foreign Affairs</a>", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Minister for Foreign Affairs (Australia)", "link": "https://wikipedia.org/wiki/Minister_for_Foreign_Affairs_(Australia)"}]}, {"year": "1956", "text": "<PERSON>, Canadian-American ice hockey player and coach", "html": "1956 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian-American ice hockey player and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian-American ice hockey player and coach", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1957", "text": "<PERSON>, American drummer and songwriter (d. 2015)", "html": "1957 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American drummer and songwriter (d. 2015)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American drummer and songwriter (d. 2015)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1957", "text": "<PERSON>, Canadian-American cosmologist and astronomer", "html": "1957 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian-American cosmologist and astronomer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian-American cosmologist and astronomer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1958", "text": "<PERSON>, Chinese director, producer, and screenwriter", "html": "1958 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>-wai\" title=\"<PERSON> Ka<PERSON>-wai\"><PERSON></a>, Chinese director, producer, and screenwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>-wa<PERSON>\" title=\"<PERSON> Kar-wai\"><PERSON>wa<PERSON></a>, Chinese director, producer, and screenwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>-wai"}]}, {"year": "1958", "text": "<PERSON>, English journalist", "html": "1958 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English journalist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English journalist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1958", "text": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, Australian businesswoman, founded Ingeus", "html": "1958 - <a href=\"https://wikipedia.org/wiki/Th%C3%A9r%C3%A8se_Rein\" title=\"Thérèse Rein\"><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON></a>, Australian businesswoman, founded <a href=\"https://wikipedia.org/wiki/Ingeus\" title=\"Ingeus\">Ingeus</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Th%C3%A9r%C3%A8se_Rein\" title=\"Thérèse Rein\"><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON></a>, Australian businesswoman, founded <a href=\"https://wikipedia.org/wiki/Ingeus\" title=\"Ingeus\">Ingeus</a>", "links": [{"title": "Thérèse Rein", "link": "https://wikipedia.org/wiki/Th%C3%A9r%C3%A8se_Rein"}, {"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Ingeus"}]}, {"year": "1958", "text": "<PERSON>, American music manager", "html": "1958 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American music manager", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Silver\"><PERSON></a>, American music manager", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1959", "text": "<PERSON><PERSON>, Baroness <PERSON>, Bangladeshi-English politician", "html": "1959 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>,_Baroness_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON><PERSON>, Baroness <PERSON>\"><PERSON><PERSON>, Baroness <PERSON></a>, Bangladeshi-English politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>,_Baroness_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON><PERSON>, Baroness <PERSON>\"><PERSON><PERSON>, Baroness <PERSON></a>, Bangladeshi-English politician", "links": [{"title": "<PERSON><PERSON>, Baroness <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>,_<PERSON>_<PERSON>"}]}, {"year": "1960", "text": "<PERSON>, English cricketer and coach", "html": "1960 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English cricketer and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English cricketer and coach", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1960", "text": "<PERSON>, English-American screenwriter and producer", "html": "1960 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English-American screenwriter and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English-American screenwriter and producer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1960", "text": "<PERSON>, American journalist and actress", "html": "1960 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American journalist and actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American journalist and actress", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1960", "text": "<PERSON>, Hong Kong martial artist and actor", "html": "1960 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Hong Kong martial artist and actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Hong Kong martial artist and actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1960", "text": "<PERSON>, American soprano", "html": "1960 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American soprano", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American soprano", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_Upshaw"}]}, {"year": "1960", "text": "<PERSON>, Dutch footballer and manager", "html": "1960 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>out<PERSON>\"><PERSON></a>, Dutch footballer and manager", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch footballer and manager", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_Wouters"}]}, {"year": "1961", "text": "<PERSON><PERSON><PERSON><PERSON>, Portuguese politician, 119th Prime Minister of Portugal", "html": "1961 - <a href=\"https://wikipedia.org/wiki/Ant%C3%B3nio_Costa\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Portuguese politician, 119th <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Portugal\" title=\"Prime Minister of Portugal\">Prime Minister of Portugal</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Ant%C3%B3nio_Costa\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Portuguese politician, 119th <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Portugal\" title=\"Prime Minister of Portugal\">Prime Minister of Portugal</a>", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Ant%C3%B3nio_Costa"}, {"title": "Prime Minister of Portugal", "link": "https://wikipedia.org/wiki/Prime_Minister_of_Portugal"}]}, {"year": "1961", "text": "<PERSON>, English comedian and actor (d. 2019)", "html": "1961 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English comedian and actor (d. 2019)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English comedian and actor (d. 2019)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1963", "text": "<PERSON>, American singer-songwriter, producer, and actress", "html": "1963 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Regina Belle\"><PERSON></a>, American singer-songwriter, producer, and actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Regina Belle\"><PERSON></a>, American singer-songwriter, producer, and actress", "links": [{"title": "Regina Belle", "link": "https://wikipedia.org/wiki/Regina_Belle"}]}, {"year": "1963", "text": "<PERSON><PERSON> III of Lesotho", "html": "1963 - <a href=\"https://wikipedia.org/wiki/Letsie_III_of_Lesotho\" class=\"mw-redirect\" title=\"Letsie III of Lesotho\">Letsie III of Lesotho</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Letsie_III_of_Lesotho\" class=\"mw-redirect\" title=\"Letsie III of Lesotho\">Letsie III of Lesotho</a>", "links": [{"title": "<PERSON><PERSON> III of Lesotho", "link": "https://wikipedia.org/wiki/Letsie_III_of_Lesotho"}]}, {"year": "1963", "text": "<PERSON><PERSON>, Finnish ski jumper and singer (d. 2019)", "html": "1963 - <a href=\"https://wikipedia.org/wiki/Matti_Nyk%C3%A4nen\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Finnish ski jumper and singer (d. 2019)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Matti_Nyk%C3%A4nen\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Finnish ski jumper and singer (d. 2019)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Matti_Nyk%C3%A4nen"}]}, {"year": "1964", "text": "<PERSON>, American actress and producer", "html": "1964 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress and producer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1965", "text": "<PERSON>, American singer-songwriter and guitarist", "html": "1965 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and guitarist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and guitarist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1965", "text": "<PERSON>, English-American actor, film director and screenwriter", "html": "1965 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English-American actor, film director and screenwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English-American actor, film director and screenwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1966", "text": "<PERSON>, American guitarist and songwriter", "html": "1966 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American guitarist and songwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American guitarist and songwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1966", "text": "<PERSON><PERSON>, Swedish lawyer and politician, 30th Swedish Minister of Defence", "html": "1966 - <a href=\"https://wikipedia.org/wiki/Sten_Tolgfors\" title=\"Sten Tolgfors\"><PERSON><PERSON></a>, Swedish lawyer and politician, 30th <a href=\"https://wikipedia.org/wiki/Minister_for_Defence_(Sweden)\" title=\"Minister for Defence (Sweden)\">Swedish Minister of Defence</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Sten_Tolgfors\" title=\"Sten Tolgfors\"><PERSON><PERSON></a>, Swedish lawyer and politician, 30th <a href=\"https://wikipedia.org/wiki/Minister_for_Defence_(Sweden)\" title=\"Minister for Defence (Sweden)\">Swedish Minister of Defence</a>", "links": [{"title": "<PERSON><PERSON>fo<PERSON>", "link": "https://wikipedia.org/wiki/Sten_Tolgfors"}, {"title": "Minister for Defence (Sweden)", "link": "https://wikipedia.org/wiki/Minister_for_Defence_(Sweden)"}]}, {"year": "1969", "text": "<PERSON>, Australian actor", "html": "1969 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1969", "text": "<PERSON>, American cartoonist", "html": "1969 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(cartoonist)\" title=\"<PERSON> (cartoonist)\"><PERSON></a>, American cartoonist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(cartoonist)\" title=\"<PERSON> (cartoonist)\"><PERSON></a>, American cartoonist", "links": [{"title": "<PERSON> (cartoonist)", "link": "https://wikipedia.org/wiki/<PERSON>_(cartoonist)"}]}, {"year": "1969", "text": "<PERSON><PERSON>, Estonian cyclist", "html": "1969 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Estonian cyclist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Estonian cyclist", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>u"}]}, {"year": "1971", "text": "<PERSON><PERSON>, American basketball player and coach", "html": "1971 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American basketball player and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American basketball player and coach", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1971", "text": "<PERSON>, Canadian author and activist", "html": "1971 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian author and activist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian author and activist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1971", "text": "<PERSON>, Belgian cyclist", "html": "1971 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Belgian cyclist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Belgian cyclist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1972", "text": "<PERSON>, American singer and guitarist", "html": "1972 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer and guitarist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer and guitarist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1972", "text": "<PERSON><PERSON>, American basketball player and sportscaster", "html": "1972 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American basketball player and sportscaster", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American basketball player and sportscaster", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1972", "text": "<PERSON>, American drummer", "html": "1972 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American drummer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American drummer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1972", "text": "<PERSON><PERSON><PERSON>, Dutch footballer and manager", "html": "1972 - <a href=\"https://wikipedia.org/wiki/Jaap_<PERSON>am\" title=\"<PERSON>aa<PERSON>am\"><PERSON><PERSON><PERSON></a>, Dutch footballer and manager", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Jaap_<PERSON>am\" title=\"<PERSON>aa<PERSON>am\"><PERSON><PERSON><PERSON></a>, Dutch footballer and manager", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Jaap_<PERSON>am"}]}, {"year": "1972", "text": "<PERSON>, American basketball player", "html": "1972 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(basketball,_born_1972)\" title=\"<PERSON> (basketball, born 1972)\"><PERSON></a>, American basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(basketball,_born_1972)\" title=\"<PERSON> (basketball, born 1972)\"><PERSON></a>, American basketball player", "links": [{"title": "<PERSON> (basketball, born 1972)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(basketball,_born_1972)"}]}, {"year": "1973", "text": "<PERSON>, American football player", "html": "1973 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1974", "text": "<PERSON>, Argentine footballer", "html": "1974 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%B3<PERSON><PERSON>_(footballer)\" title=\"<PERSON> (footballer)\"><PERSON></a>, Argentine footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%B3<PERSON><PERSON>_(footballer)\" title=\"<PERSON> (footballer)\"><PERSON></a>, Argentine footballer", "links": [{"title": "<PERSON> (footballer)", "link": "https://wikipedia.org/wiki/Claudio_L%C3%B3<PERSON><PERSON>_(footballer)"}]}, {"year": "1975", "text": "<PERSON>, New Zealand cricketer", "html": "1975 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, New Zealand cricketer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, New Zealand cricketer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1975", "text": "<PERSON>, Spanish actress", "html": "1975 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Spanish actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Spanish actress", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1975", "text": "<PERSON><PERSON>, Finnish DJ and producer", "html": "1975 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Finnish DJ and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Finnish DJ and producer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Darude"}]}, {"year": "1975", "text": "<PERSON><PERSON>, Australian triathlete", "html": "1975 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Australian triathlete", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Australian triathlete", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>p"}]}, {"year": "1975", "text": "<PERSON><PERSON><PERSON>, English television presenter", "html": "1975 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, English television presenter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, English television presenter", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1975", "text": "<PERSON>, Australian-American mathematician", "html": "1975 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Tao\"><PERSON></a>, Australian-American mathematician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_Tao\" title=\"<PERSON> Tao\"><PERSON></a>, Australian-American mathematician", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1976", "text": "<PERSON>, American singer-songwriter and guitarist", "html": "1976 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and guitarist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and guitarist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1976", "text": "<PERSON><PERSON>, Italian chef and author", "html": "1976 - <a href=\"https://wikipedia.org/wiki/Gino_D%27Acampo\" title=\"Gino D'Acampo\"><PERSON><PERSON></a>, Italian chef and author", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Gino_D%27Acampo\" title=\"Gino D'Acampo\"><PERSON><PERSON></a>, Italian chef and author", "links": [{"title": "Gino <PERSON>", "link": "https://wikipedia.org/wiki/Gino_D%27Acampo"}]}, {"year": "1976", "text": "<PERSON><PERSON><PERSON>, Polish-American actress", "html": "1976 - <a href=\"https://wikipedia.org/wiki/Da<PERSON>ra_Domi%C5%84czyk\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Polish-American actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>ra_<PERSON>i%C5%84czyk\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Polish-American actress", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Dagmara_Domi%C5%84czyk"}]}, {"year": "1976", "text": "<PERSON>, Brazilian-Spanish footballer", "html": "1976 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Brazilian-Spanish footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Brazilian-Spanish footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1976", "text": "<PERSON>, Swedish footballer and sportscaster", "html": "1976 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(footballer,_born_1976)\" class=\"mw-redirect\" title=\"<PERSON> (footballer, born 1976)\"><PERSON></a>, Swedish footballer and sportscaster", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(footballer,_born_1976)\" class=\"mw-redirect\" title=\"<PERSON> (footballer, born 1976)\"><PERSON></a>, Swedish footballer and sportscaster", "links": [{"title": "<PERSON> (footballer, born 1976)", "link": "https://wikipedia.org/wiki/<PERSON>_(footballer,_born_1976)"}]}, {"year": "1976", "text": "<PERSON>, American actor", "html": "1976 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1977", "text": "<PERSON>, Australian cricketer", "html": "1977 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian cricketer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian cricketer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1977", "text": "<PERSON><PERSON>, Belgian cyclist", "html": "1977 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>e\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Belgian cyclist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Belgian cyclist", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Leif_Hoste"}]}, {"year": "1977", "text": "<PERSON>, Canadian ice hockey player", "html": "1977 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1978", "text": "<PERSON>, Brazilian mixed martial artist", "html": "1978 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Brazilian mixed martial artist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Brazilian mixed martial artist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Ricardo_<PERSON>"}]}, {"year": "1978", "text": "<PERSON><PERSON>, American musician and songwriter", "html": "1978 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_(musician)\" title=\"<PERSON><PERSON> (musician)\">Pan<PERSON></a>, American musician and songwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_(musician)\" title=\"<PERSON><PERSON> (musician)\"><PERSON><PERSON></a>, American musician and songwriter", "links": [{"title": "<PERSON><PERSON> (musician)", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_Bear_(musician)"}]}, {"year": "1978", "text": "<PERSON>, American baseball player", "html": "1978 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1978", "text": "<PERSON><PERSON>, French film director and screenwriter", "html": "1978 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, French film director and screenwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, French film director and screenwriter", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1979", "text": "<PERSON>, American actor", "html": "1979 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1980", "text": "<PERSON>, Spanish footballer", "html": "1980 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%B1as\" title=\"<PERSON>\"><PERSON></a>, Spanish footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%B1as\" title=\"<PERSON>\"><PERSON></a>, Spanish footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Javier_<PERSON>%C3%B1as"}]}, {"year": "1980", "text": "<PERSON>, British actor, comedian and writer", "html": "1980 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, British actor, comedian and writer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, British actor, comedian and writer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1980", "text": "<PERSON>, American ice hockey player", "html": "1980 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American ice hockey player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American ice hockey player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1981", "text": "<PERSON><PERSON>, Venezuelan runner", "html": "1981 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Venezuelan runner", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Venezuelan runner", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1982", "text": "<PERSON><PERSON>, Anguillan cricketer", "html": "1982 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Ang<PERSON>lan cricketer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, <PERSON><PERSON>lan cricketer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Omari_Banks"}]}, {"year": "1983", "text": "<PERSON>, American baseball player", "html": "1983 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1985", "text": "<PERSON><PERSON>, Swedish ice hockey player", "html": "1985 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Swedish ice hockey player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Swedish ice hockey player", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1985", "text": "<PERSON>, Scottish footballer", "html": "1985 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1986", "text": "<PERSON><PERSON><PERSON><PERSON>, American football player", "html": "1986 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, American football player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, American football player", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1987", "text": "<PERSON>, Australian rugby league player", "html": "1987 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian rugby league player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian rugby league player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1987", "text": "<PERSON><PERSON><PERSON>, American singer, songwriter, and record producer", "html": "1987 - <a href=\"https://wikipedia.org/wiki/Jerem<PERSON>\" title=\"<PERSON>rem<PERSON>\"><PERSON><PERSON><PERSON></a>, American singer, songwriter, and record producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>rem<PERSON>\" title=\"<PERSON>rem<PERSON>\"><PERSON><PERSON><PERSON></a>, American singer, songwriter, and record producer", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>remih"}]}, {"year": "1991", "text": "<PERSON>-<PERSON><PERSON>, Swedish ice hockey player", "html": "1991 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON>-<PERSON></a>, Swedish ice hockey player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON>-<PERSON></a>, Swedish ice hockey player", "links": [{"title": "<PERSON>-<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1992", "text": "<PERSON>, American actress", "html": "1992 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1992", "text": "<PERSON><PERSON>, Finnish actor", "html": "1992 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Finnish actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Finnish actor", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1994", "text": "<PERSON>, American singer-songwriter", "html": "1994 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>s"}]}, {"year": "1997", "text": "<PERSON><PERSON>, British basketball player", "html": "1997 - <a href=\"https://wikipedia.org/wiki/O<PERSON>_An<PERSON>by\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, British basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/O<PERSON>_Anunoby\" title=\"O<PERSON> Anunoby\"><PERSON><PERSON></a>, British basketball player", "links": [{"title": "O<PERSON>", "link": "https://wikipedia.org/wiki/OG_Anunoby"}]}, {"year": "1998", "text": "<PERSON><PERSON>, Cuban rower", "html": "1998 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Cuban rower", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Cuban rower", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Rosa<PERSON>_<PERSON>"}]}, {"year": "2002", "text": "<PERSON>, American baseball player", "html": "2002 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Jordan_Lawlar"}]}, {"year": "2005", "text": "<PERSON>, Canadian ice hockey player", "html": "2005 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}], "Deaths": [{"year": "521", "text": "<PERSON>, Gallo-Roman bishop", "html": "521 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Gallo-Roman bishop", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Gallo-Roman bishop", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "855", "text": "<PERSON>, pope of the Catholic Church (b. 790)", "html": "855 - <a href=\"https://wikipedia.org/wiki/Pope_Leo_IV\" title=\"Pope Leo IV\"><PERSON></a>, pope of the <a href=\"https://wikipedia.org/wiki/Catholic_Church\" title=\"Catholic Church\">Catholic Church</a> (b. 790)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Pope_Leo_IV\" title=\"Pope Leo IV\"><PERSON></a>, pope of the <a href=\"https://wikipedia.org/wiki/Catholic_Church\" title=\"Catholic Church\">Catholic Church</a> (b. 790)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "Catholic Church", "link": "https://wikipedia.org/wiki/Catholic_Church"}]}, {"year": "952", "text": "<PERSON>, Chinese noblewoman (b. 913)", "html": "952 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Chinese noblewoman (b. 913)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Chinese noblewoman (b. 913)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>e"}]}, {"year": "961", "text": "<PERSON>, empress dowager of the Song dynasty", "html": "961 - <a href=\"https://wikipedia.org/wiki/Empress_<PERSON><PERSON>_Du\" title=\"Empress Dowager Du\"><PERSON></a>, empress dowager of the <a href=\"https://wikipedia.org/wiki/Song_dynasty\" title=\"Song dynasty\">Song dynasty</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Empress_<PERSON><PERSON>_Du\" title=\"Empress Dowager Du\">Du</a>, empress dowager of the <a href=\"https://wikipedia.org/wiki/Song_dynasty\" title=\"Song dynasty\">Song dynasty</a>", "links": [{"title": "Empress <PERSON><PERSON> Du", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>"}, {"title": "Song dynasty", "link": "https://wikipedia.org/wiki/Song_dynasty"}]}, {"year": "1070", "text": "<PERSON>, count of Flanders (b. 1030)", "html": "1070 - <a href=\"https://wikipedia.org/wiki/<PERSON>_VI,_Count_of_Flanders\" title=\"<PERSON> VI, Count of Flanders\"><PERSON> VI</a>, count of <a href=\"https://wikipedia.org/wiki/County_of_Flanders\" title=\"County of Flanders\">Flanders</a> (b. 1030)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_VI,_Count_of_Flanders\" title=\"<PERSON> VI, Count of Flanders\"><PERSON> VI</a>, count of <a href=\"https://wikipedia.org/wiki/County_of_Flanders\" title=\"County of Flanders\">Flanders</a> (b. 1030)", "links": [{"title": "<PERSON>, Count of Flanders", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Count_of_Flanders"}, {"title": "County of Flanders", "link": "https://wikipedia.org/wiki/County_of_Flanders"}]}, {"year": "1085", "text": "<PERSON>, Norman adventurer", "html": "1085 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Norman adventurer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Norman adventurer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1119", "text": "<PERSON>, count of Flanders (b. 1093)", "html": "1119 - <a href=\"https://wikipedia.org/wiki/<PERSON>_VII,_Count_of_Flanders\" title=\"<PERSON> VII, Count of Flanders\"><PERSON> VII</a>, count of Flanders (b. 1093)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_VII,_Count_of_Flanders\" title=\"<PERSON> VII, Count of Flanders\"><PERSON> VII</a>, count of Flanders (b. 1093)", "links": [{"title": "<PERSON>, Count of Flanders", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Count_of_Flanders"}]}, {"year": "1210", "text": "<PERSON><PERSON><PERSON> <PERSON>, king of Sweden (b. 1210)", "html": "1210 - <a href=\"https://wikipedia.org/wiki/Sverker_II_of_Sweden\" class=\"mw-redirect\" title=\"Sverker II of Sweden\"><PERSON><PERSON><PERSON> <PERSON></a>, king of <a href=\"https://wikipedia.org/wiki/Sweden\" title=\"Sweden\">Sweden</a> (b. 1210)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Sverker_II_of_Sweden\" class=\"mw-redirect\" title=\"Sverker II of Sweden\"><PERSON>ver<PERSON> <PERSON></a>, king of <a href=\"https://wikipedia.org/wiki/Sweden\" title=\"Sweden\">Sweden</a> (b. 1210)", "links": [{"title": "Sverker II of Sweden", "link": "https://wikipedia.org/wiki/Sverker_II_of_Sweden"}, {"title": "Sweden", "link": "https://wikipedia.org/wiki/Sweden"}]}, {"year": "1304", "text": "<PERSON>, 2nd Baron <PERSON> (b. 1251)", "html": "1304 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_2nd_Baron_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>, 2nd Baron <PERSON>\"><PERSON>, 2nd Baron <PERSON></a> (b. 1251)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_2nd_Baron_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>, 2nd Baron <PERSON>\"><PERSON>, 2nd Baron <PERSON></a> (b. 1251)", "links": [{"title": "<PERSON>, 2nd Baron <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>,_2nd_Baron_<PERSON>"}]}, {"year": "1399", "text": "<PERSON><PERSON><PERSON><PERSON>, queen of Poland (b. 1374)", "html": "1399 - <a href=\"https://wikipedia.org/wiki/Jadwiga_of_Poland\" title=\"<PERSON><PERSON><PERSON><PERSON> of Poland\"><PERSON><PERSON><PERSON><PERSON></a>, queen of <a href=\"https://wikipedia.org/wiki/Poland\" title=\"Poland\">Poland</a> (b. 1374)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Jadwiga_of_Poland\" title=\"<PERSON>ad<PERSON><PERSON> of Poland\"><PERSON><PERSON><PERSON><PERSON></a>, queen of <a href=\"https://wikipedia.org/wiki/Poland\" title=\"Poland\">Poland</a> (b. 1374)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON> of Poland", "link": "https://wikipedia.org/wiki/Jadwiga_of_Poland"}, {"title": "Poland", "link": "https://wikipedia.org/wiki/Poland"}]}, {"year": "1453", "text": "<PERSON>, Grand Prince of Moscow", "html": "1453 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Grand Prince of <a href=\"https://wikipedia.org/wiki/Moscow\" title=\"Moscow\">Moscow</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Grand Prince of <a href=\"https://wikipedia.org/wiki/Moscow\" title=\"Moscow\">Moscow</a>", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Moscow", "link": "https://wikipedia.org/wiki/Moscow"}]}, {"year": "1453", "text": "<PERSON>, 1st Earl of Shrewsbury, English commander and politician (b. 1387)", "html": "1453 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_1st_Earl_of_Shrewsbury\" title=\"<PERSON>, 1st Earl of Shrewsbury\"><PERSON>, 1st Earl of Shrewsbury</a>, English commander and politician (b. 1387)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_1st_Earl_of_Shrewsbury\" title=\"<PERSON>, 1st Earl of Shrewsbury\"><PERSON>, 1st Earl of Shrewsbury</a>, English commander and politician (b. 1387)", "links": [{"title": "<PERSON>, 1st Earl of Shrewsbury", "link": "https://wikipedia.org/wiki/<PERSON>,_1st_Earl_of_Shrewsbury"}]}, {"year": "1531", "text": "<PERSON><PERSON><PERSON>, Japanese commander (b. 1484)", "html": "1531 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON> Takakuni\"><PERSON><PERSON><PERSON></a>, Japanese commander (b. 1484)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON> Takakuni\"><PERSON><PERSON><PERSON></a>, Japanese commander (b. 1484)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Hoso<PERSON>_Ta<PERSON>ni"}]}, {"year": "1571", "text": "<PERSON>, German poet and historian (b. 1516)", "html": "1571 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German poet and historian (b. 1516)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German poet and historian (b. 1516)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1588", "text": "<PERSON><PERSON>, Ottoman architect and engineer, designed the Sokollu Mehmet Pasha Mosque and Süleymaniye Mosque (b. 1489)", "html": "1588 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Ottoman architect and engineer, designed the <a href=\"https://wikipedia.org/wiki/Sokoll<PERSON>_<PERSON><PERSON>_Pasha_Mosque_(Azapkap%C4%B1)\" class=\"mw-redirect\" title=\"Sokollu Mehmed Pasha Mosque (Azapkapı)\">Sokollu Mehmet Pasha Mosque</a> and <a href=\"https://wikipedia.org/wiki/S%C3%BCleymaniye_Mosque\" title=\"Süleymaniye Mosque\">Süleymaniye Mosque</a> (b. 1489)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Ottoman architect and engineer, designed the <a href=\"https://wikipedia.org/wiki/Sokollu_<PERSON>hmed_Pasha_Mosque_(Azapkap%C4%B1)\" class=\"mw-redirect\" title=\"Sokollu Mehmed Pasha Mosque (Azapkapı)\">Sokollu Mehmet Pasha Mosque</a> and <a href=\"https://wikipedia.org/wiki/S%C3%BCleymaniye_Mosque\" title=\"Süleymaniye Mosque\">Süleymaniye Mosque</a> (b. 1489)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}, {"title": "Sokollu <PERSON> Mosque (Azapkapı)", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON><PERSON><PERSON>_<PERSON>_Mosque_(Azapkap%C4%B1)"}, {"title": "Süleymaniye Mosque", "link": "https://wikipedia.org/wiki/S%C3%BCleymaniye_Mosque"}]}, {"year": "1603", "text": "<PERSON><PERSON><PERSON>, Hungarian noble (b. 1553)", "html": "1603 - <a href=\"https://wikipedia.org/wiki/M%C3%B3zes_Sz%C3%A9kely\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON> Székely\"><PERSON><PERSON><PERSON></a>, Hungarian noble (b. 1553)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/M%C3%B3zes_Sz%C3%A9kely\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON> Székely\"><PERSON><PERSON><PERSON></a>, Hungarian noble (b. 1553)", "links": [{"title": "<PERSON><PERSON><PERSON> Székely", "link": "https://wikipedia.org/wiki/M%C3%B3zes_Sz%C3%A9kely"}]}, {"year": "1642", "text": "<PERSON>, Count of Nassau-Siegen, German count, field marshal of the Dutch State Army (b. 1592)", "html": "1642 - <a href=\"https://wikipedia.org/wiki/<PERSON>,_Count_of_Nassau-Siegen\" title=\"<PERSON>, Count of Nassau-Siegen\"><PERSON>, Count of Nassau-Siegen</a>, German count, field marshal of the Dutch State Army (b. 1592)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>,_Count_of_Nassau-Siegen\" title=\"<PERSON>, Count of Nassau-Siegen\"><PERSON>, Count of Nassau-Siegen</a>, German count, field marshal of the Dutch State Army (b. 1592)", "links": [{"title": "<PERSON>, Count of Nassau-Siegen", "link": "https://wikipedia.org/wiki/<PERSON>,_Count_of_Nassau-Siegen"}]}, {"year": "1645", "text": "<PERSON>, 1st Earl of Somerset, English-Scottish politician, Lord Chamberlain of the United Kingdom (b. 1587)", "html": "1645 - <a href=\"https://wikipedia.org/wiki/<PERSON>,_1st_Earl_of_Somerset\" title=\"<PERSON>, 1st Earl of Somerset\"><PERSON>, 1st Earl of Somerset</a>, English-Scottish politician, <a href=\"https://wikipedia.org/wiki/Lord_Chamberlain\" title=\"Lord Chamberlain\">Lord Chamberlain of the United Kingdom</a> (b. 1587)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>,_1st_Earl_of_Somerset\" title=\"<PERSON>, 1st Earl of Somerset\"><PERSON>, 1st Earl of Somerset</a>, English-Scottish politician, <a href=\"https://wikipedia.org/wiki/Lord_Chamberlain\" title=\"Lord Chamberlain\">Lord Chamberlain of the United Kingdom</a> (b. 1587)", "links": [{"title": "<PERSON>, 1st Earl of Somerset", "link": "https://wikipedia.org/wiki/<PERSON>,_1st_Earl_of_Somerset"}, {"title": "Lord <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1704", "text": "<PERSON><PERSON><PERSON>, French fur trader and explorer (b. 1657)", "html": "1704 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, French fur trader and explorer (b. 1657)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, French fur trader and explorer (b. 1657)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1709", "text": "<PERSON>, English planter and merchant (b. 1646)", "html": "1709 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English planter and merchant (b. 1646)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English planter and merchant (b. 1646)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1725", "text": "<PERSON>, English and British soldier, MP for Queenborough, lieutenant-governor of Sheerness (b. before 1660?).", "html": "1725 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(died_1725)\" title=\"<PERSON> (died 1725)\"><PERSON></a>, English and British soldier, MP for Queenborough, lieutenant-governor of Sheerness (b. before 1660?).", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(died_1725)\" title=\"<PERSON> (died 1725)\"><PERSON></a>, English and British soldier, MP for Queenborough, lieutenant-governor of Sheerness (b. before 1660?).", "links": [{"title": "<PERSON> (died 1725)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(died_1725)"}]}, {"year": "1762", "text": "<PERSON> of Russia (b. 1728)", "html": "1762 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Russia\" title=\"<PERSON> of Russia\"><PERSON> of Russia</a> (b. 1728)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Russia\" title=\"<PERSON> of Russia\"><PERSON> of Russia</a> (b. 1728)", "links": [{"title": "<PERSON> of Russia", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Russia"}]}, {"year": "1790", "text": "<PERSON>, Scottish economist and philosopher (b. 1723)", "html": "1790 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish economist and philosopher (b. 1723)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish economist and philosopher (b. 1723)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1791", "text": "<PERSON>, Austrian missionary and author (b. 1717)", "html": "1791 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Austrian missionary and author (b. 1717)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Austrian missionary and author (b. 1717)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1793", "text": "<PERSON>, French murderer (b. 1768)", "html": "1793 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French murderer (b. 1768)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French murderer (b. 1768)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Charlotte_<PERSON>y"}]}, {"year": "1794", "text": "<PERSON>, English chemist and businessman (b. 1718)", "html": "1794 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English chemist and businessman (b. 1718)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English chemist and businessman (b. 1718)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1845", "text": "<PERSON>, 2nd Earl <PERSON>, English politician, Prime Minister of the United Kingdom (b. 1764)", "html": "1845 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_2nd_Earl_<PERSON>\" title=\"<PERSON>, 2nd Earl <PERSON>\"><PERSON>, 2nd Earl <PERSON></a>, English politician, <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_the_United_Kingdom\" title=\"Prime Minister of the United Kingdom\">Prime Minister of the United Kingdom</a> (b. 1764)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>,_2nd_Earl_<PERSON>\" title=\"<PERSON>, 2nd Earl <PERSON>\"><PERSON>, 2nd Earl <PERSON></a>, English politician, <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_the_United_Kingdom\" title=\"Prime Minister of the United Kingdom\">Prime Minister of the United Kingdom</a> (b. 1764)", "links": [{"title": "<PERSON>, 2nd Earl <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>,_2nd_Earl_<PERSON>"}, {"title": "Prime Minister of the United Kingdom", "link": "https://wikipedia.org/wiki/Prime_Minister_of_the_United_Kingdom"}]}, {"year": "1871", "text": "<PERSON>, Polish virtuoso pianist, arranger and composer (b. 1841)", "html": "1871 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Polish virtuoso pianist, arranger and composer (b. 1841)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Polish virtuoso pianist, arranger and composer (b. 1841)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1878", "text": "<PERSON><PERSON><PERSON>, Italian poet and politician (b. 1812)", "html": "1878 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Italian poet and politician (b. 1812)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Italian poet and politician (b. 1812)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1879", "text": "<PERSON><PERSON><PERSON>, Ukrainian-Polish painter (b. 1856)", "html": "1879 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Ukrainian-Polish painter (b. 1856)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Ukrainian-Polish painter (b. 1856)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1881", "text": "<PERSON>, American scout and explorer (b. 1804)", "html": "1881 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American scout and explorer (b. 1804)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American scout and explorer (b. 1804)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1883", "text": "<PERSON><PERSON>, Vietnamese emperor (b. 1829)", "html": "1883 - <a href=\"https://wikipedia.org/wiki/T%E1%BB%B1_%C4%90%E1%BB%A9c\" title=\"Tự Đ<PERSON>\">T<PERSON></a>, Vietnamese emperor (b. 1829)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/T%E1%BB%B1_%C4%90%E1%BB%A9c\" title=\"Tự <PERSON>\"><PERSON><PERSON></a>, Vietnamese emperor (b. 1829)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/T%E1%BB%B1_%C4%90%E1%BB%A9c"}]}, {"year": "1885", "text": "<PERSON><PERSON><PERSON>, Canadian farmer and politician, 1st Canadian Minister of Agriculture (b. 1811)", "html": "1885 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Canadian farmer and politician, 1st <a href=\"https://wikipedia.org/wiki/Minister_of_Agriculture_(Canada)\" class=\"mw-redirect\" title=\"Minister of Agriculture (Canada)\">Canadian Minister of Agriculture</a> (b. 1811)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Canadian farmer and politician, 1st <a href=\"https://wikipedia.org/wiki/Minister_of_Agriculture_(Canada)\" class=\"mw-redirect\" title=\"Minister of Agriculture (Canada)\">Canadian Minister of Agriculture</a> (b. 1811)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>"}, {"title": "Minister of Agriculture (Canada)", "link": "https://wikipedia.org/wiki/Minister_of_Agriculture_(Canada)"}]}, {"year": "1893", "text": "<PERSON>, American banker and politician (b. 1833)", "html": "1893 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American banker and politician (b. 1833)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American banker and politician (b. 1833)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "1894", "text": "<PERSON><PERSON><PERSON>, French poet and translator (b. 1818)", "html": "1894 - <a href=\"https://wikipedia.org/wiki/Le<PERSON><PERSON>_de_<PERSON>\" title=\"Leconte de Lisle\"><PERSON><PERSON><PERSON></a>, French poet and translator (b. 1818)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Le<PERSON><PERSON>_de_<PERSON>\" title=\"Leconte de Lisle\"><PERSON><PERSON><PERSON></a>, French poet and translator (b. 1818)", "links": [{"title": "Leconte de Lisle", "link": "https://wikipedia.org/wiki/Leconte_de_Lisle"}]}, {"year": "1894", "text": "<PERSON>, Austrian anatomist and biologist (b. 1810)", "html": "1894 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Austrian anatomist and biologist (b. 1810)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Austrian anatomist and biologist (b. 1810)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1900", "text": "<PERSON>, Scottish-Australian politician, 8th Premier of Queensland (b. 1835)", "html": "1900 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish-Australian politician, 8th <a href=\"https://wikipedia.org/wiki/Premier_of_Queensland\" title=\"Premier of Queensland\">Premier of Queensland</a> (b. 1835)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish-Australian politician, 8th <a href=\"https://wikipedia.org/wiki/Premier_of_Queensland\" title=\"Premier of Queensland\">Premier of Queensland</a> (b. 1835)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Premier of Queensland", "link": "https://wikipedia.org/wiki/Premier_of_Queensland"}]}, {"year": "1907", "text": "<PERSON>, French author and critic (b. 1830)", "html": "1907 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French author and critic (b. 1830)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French author and critic (b. 1830)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1912", "text": "<PERSON>, French mathematician, physicist, and engineer (b. 1854)", "html": "1912 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A9\" title=\"<PERSON>\"><PERSON></a>, French mathematician, physicist, and engineer (b. 1854)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>%C3%A9\" title=\"<PERSON>\"><PERSON></a>, French mathematician, physicist, and engineer (b. 1854)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Henri_Poincar%C3%A9"}]}, {"year": "1918", "text": "Victims of the Shooting of the <PERSON> family:\n<PERSON> of Russia (b. 1868)\n<PERSON> of Russia (b. 1872)\nGrand Duchess <PERSON> of Russia (b. 1895)\nGrand Duchess <PERSON> of Russia (b. 1897)\nGrand Duchess <PERSON> of Russia (b. 1899)\nGrand Duchess <PERSON> of Russia (b. 1901)\n<PERSON>, <PERSON><PERSON> of Russia (b. 1904)\n<PERSON> (b. 1878)\n<PERSON> (b. 1872)\n<PERSON> (b. 1858)\n<PERSON><PERSON><PERSON> (b. 1865)", "html": "1918 - Victims of the <a href=\"https://wikipedia.org/wiki/Shooting_of_the_Romanov_family\" class=\"mw-redirect\" title=\"Shooting of the Romanov family\">Shooting of the Romanov family</a>:\n<ul>\n<li>\n<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Russia\" class=\"mw-redirect\" title=\"<PERSON> of Russia\"><PERSON> of Russia</a> (b. 1868)</li>\n<li>\n<a href=\"https://wikipedia.org/wiki/<PERSON>_(Alix_of_Hesse)\" title=\"<PERSON> (Ali<PERSON> of Hesse)\"><PERSON> of Russia</a> (b. 1872)</li>\n<li>\n<a href=\"https://wikipedia.org/wiki/Grand_Duchess_<PERSON>_<PERSON>_of_Russia\" title=\"Grand Duchess <PERSON> of Russia\">Grand Duchess <PERSON> of Russia</a> (b. 1895)</li>\n<li>\n<a href=\"https://wikipedia.org/wiki/Grand_Duchess_<PERSON>_<PERSON>_of_Russia\" title=\"Grand Duchess <PERSON> of Russia\">Grand Duchess <PERSON> of Russia</a> (b. 1897)</li>\n<li>\n<a href=\"https://wikipedia.org/wiki/Grand_Duchess_<PERSON>_<PERSON>evna_of_Russia_(1899%E2%80%931918)\" class=\"mw-redirect\" title=\"Grand Duchess Maria <PERSON>evna of Russia (1899-1918)\">Grand Duchess Maria <PERSON>evna of Russia</a> (b. 1899)</li>\n<li>\n<a href=\"https://wikipedia.org/wiki/Grand_Duchess_Anastasia_Nikolaevna_of_Russia\" title=\"Grand Duchess Anastasia Nikolaevna of Russia\">Grand Duchess Anastasia Nikolaevna of Russia</a> (b. 1901)</li>\n<li>\n<a href=\"https://wikipedia.org/wiki/Alexei_Nikolaevich,_Tsarevich_of_Russia\" title=\"Alexei Nikolaevich, Tsarevich of Russia\">Alexei Nikolaevich, Tsarevich of Russia</a> (b. 1904)</li>\n<li>\n<a href=\"https://wikipedia.org/wiki/Anna_Demidova\" title=\"Anna Demidova\">Anna Demidova</a> (b. 1878)</li>\n<li>\n<a href=\"https://wikipedia.org/wiki/Ivan_Kharitonov\" title=\"Ivan Kharitonov\">Ivan Kharitonov</a> (b. 1872)</li>\n<li>\n<a href=\"https://wikipedia.org/wiki/Alexei_Trupp\" title=\"Alexei Trupp\">Alexei Trupp</a> (b. 1858)</li>\n<li>\n<a href=\"https://wikipedia.org/wiki/Yevgeny_Botkin\" class=\"mw-redirect\" title=\"Yevgeny Botkin\">Yevgeny Botkin</a> (b. 1865)</li>\n</ul>", "no_year_html": "Victims of the <a href=\"https://wikipedia.org/wiki/Shooting_of_the_Romanov_family\" class=\"mw-redirect\" title=\"Shooting of the Romanov family\">Shooting of the Romanov family</a>:\n<ul>\n<li>\n<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Russia\" class=\"mw-redirect\" title=\"<PERSON> of Russia\"><PERSON> of Russia</a> (b. 1868)</li>\n<li>\n<a href=\"https://wikipedia.org/wiki/<PERSON>_(Alix_of_Hesse)\" title=\"<PERSON> (Alix of Hesse)\"><PERSON> of Russia</a> (b. 1872)</li>\n<li>\n<a href=\"https://wikipedia.org/wiki/Grand_Duchess_<PERSON>_<PERSON>_of_Russia\" title=\"Grand Duchess <PERSON> of Russia\">Grand Duchess <PERSON> of Russia</a> (b. 1895)</li>\n<li>\n<a href=\"https://wikipedia.org/wiki/Grand_Duchess_<PERSON>_<PERSON>_of_Russia\" title=\"Grand Duchess <PERSON> of Russia\">Grand Duchess <PERSON> of Russia</a> (b. 1897)</li>\n<li>\n<a href=\"https://wikipedia.org/wiki/Grand_Duchess_<PERSON>_<PERSON>evna_of_Russia_(1899%E2%80%931918)\" class=\"mw-redirect\" title=\"Grand Duchess Maria Nikolaevna of Russia (1899-1918)\">Grand Duchess Maria Nikolaevna of Russia</a> (b. 1899)</li>\n<li>\n<a href=\"https://wikipedia.org/wiki/Grand_Duchess_Anastasia_Nikolaevna_of_Russia\" title=\"Grand Duchess Anastasia Nikolaevna of Russia\">Grand Duchess Anastasia Nikolaevna of Russia</a> (b. 1901)</li>\n<li>\n<a href=\"https://wikipedia.org/wiki/Alexei_Nikolaevich,_Tsarevich_of_Russia\" title=\"Alexei Nikolaevich, Tsarevich of Russia\">Alexei Nikolaevich, Tsarevich of Russia</a> (b. 1904)</li>\n<li>\n<a href=\"https://wikipedia.org/wiki/Anna_Demidova\" title=\"Anna Demidova\">Anna Demidova</a> (b. 1878)</li>\n<li>\n<a href=\"https://wikipedia.org/wiki/Ivan_Kharitonov\" title=\"Ivan Kharitonov\">Ivan Kharitonov</a> (b. 1872)</li>\n<li>\n<a href=\"https://wikipedia.org/wiki/Alexei_Trupp\" title=\"Alexei Trupp\">Alexei Trupp</a> (b. 1858)</li>\n<li>\n<a href=\"https://wikipedia.org/wiki/Yevgeny_Botkin\" class=\"mw-redirect\" title=\"Yevgeny Botkin\">Yevgeny Botkin</a> (b. 1865)</li>\n</ul>", "links": [{"title": "Shooting of the <PERSON><PERSON> family", "link": "https://wikipedia.org/wiki/Shooting_of_the_Romanov_family"}, {"title": "<PERSON> of Russia", "link": "https://wikipedia.org/wiki/<PERSON>_II_of_Russia"}, {"title": "<PERSON> (Alix of Hesse)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(Alix_of_Hesse)"}, {"title": "Grand Duchess <PERSON> of Russia", "link": "https://wikipedia.org/wiki/Grand_Duchess_<PERSON>_<PERSON>_of_Russia"}, {"title": "Grand Duchess <PERSON> of Russia", "link": "https://wikipedia.org/wiki/Grand_Duchess_<PERSON>_<PERSON>_of_Russia"}, {"title": "Grand Duchess <PERSON> of Russia (1899-1918)", "link": "https://wikipedia.org/wiki/Grand_Duchess_<PERSON>_<PERSON>_of_Russia_(1899%E2%80%931918)"}, {"title": "Grand Duchess <PERSON> of Russia", "link": "https://wikipedia.org/wiki/Grand_Duchess_<PERSON>_<PERSON>_of_Russia"}, {"title": "<PERSON>, <PERSON><PERSON> of Russia", "link": "https://wikipedia.org/wiki/<PERSON>,_<PERSON>evich_of_Russia"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Yev<PERSON>_<PERSON>"}]}, {"year": "<PERSON> of Russia (b. 1868)", "text": null, "html": "<PERSON> of Russia (b. 1868) - <a href=\"https://wikipedia.orghttps://wikipedia.org/wiki/<PERSON>_II_of_Russia\" class=\"mw-redirect\" title=\"Nicholas II of Russia\"><PERSON> II of Russia</a> (b. 1868)", "no_year_html": "<a href=\"https://wikipedia.orghttps://wikipedia.org/wiki/<PERSON>_II_of_Russia\" class=\"mw-redirect\" title=\"Nicholas II of Russia\"><PERSON> II of Russia</a> (b. 1868)", "links": [{"title": "<PERSON> of Russia", "link": "https://wikipedia.orghttps://wikipedia.org/wiki/<PERSON>_II_of_Russia"}]}, {"year": "<PERSON> of Russia (b. 1872)", "text": null, "html": "<PERSON> of Russia (b. 1872) - <a href=\"https://wikipedia.orghttps://wikipedia.org/wiki/<PERSON>_<PERSON>_(Ali<PERSON>_of_Hesse)\" title=\"<PERSON> (<PERSON>x of Hesse)\"><PERSON> of Russia</a> (b. 1872)", "no_year_html": "<a href=\"https://wikipedia.orghttps://wikipedia.org/wiki/<PERSON>_(Alix_of_Hesse)\" title=\"<PERSON> (Alix of Hesse)\"><PERSON> of Russia</a> (b. 1872)", "links": [{"title": "<PERSON> (Alix of Hesse)", "link": "https://wikipedia.orghttps://wikipedia.org/wiki/<PERSON>_<PERSON>_(Alix_of_Hesse)"}]}, {"year": "Grand Duchess <PERSON> of Russia (b. 1895)", "text": null, "html": "Grand Duchess <PERSON> of Russia (b. 1895) - <a href=\"https://wikipedia.orghttps://wikipedia.org/wiki/Grand_Duchess_<PERSON>_<PERSON>_of_Russia\" title=\"Grand Duchess <PERSON> of Russia\">Grand Duchess <PERSON> of Russia</a> (b. 1895)", "no_year_html": "<a href=\"https://wikipedia.orghttps://wikipedia.org/wiki/Grand_Duchess_<PERSON>_<PERSON>_of_Russia\" title=\"Grand Duchess <PERSON> of Russia\">Grand Duchess <PERSON> of Russia</a> (b. 1895)", "links": [{"title": "Grand Duchess <PERSON> of Russia", "link": "https://wikipedia.orghttps://wikipedia.org/wiki/Grand_Duchess_<PERSON>_<PERSON>_of_Russia"}]}, {"year": "Grand Duchess <PERSON> of Russia (b. 1897)", "text": null, "html": "Grand Duchess <PERSON> of Russia (b. 1897) - <a href=\"https://wikipedia.orghttps://wikipedia.org/wiki/Grand_Duchess_<PERSON>_<PERSON>_of_Russia\" title=\"Grand Duchess <PERSON> of Russia\">Grand Duchess <PERSON> of Russia</a> (b. 1897)", "no_year_html": "<a href=\"https://wikipedia.orghttps://wikipedia.org/wiki/Grand_Duchess_<PERSON>_<PERSON>_of_Russia\" title=\"Grand Duchess <PERSON> of Russia\">Grand Duchess <PERSON> of Russia</a> (b. 1897)", "links": [{"title": "Grand Duchess <PERSON> of Russia", "link": "https://wikipedia.orghttps://wikipedia.org/wiki/Grand_Duchess_<PERSON>_<PERSON>_of_Russia"}]}, {"year": "Grand Duchess <PERSON> of Russia (b. 1899)", "text": null, "html": "Grand Duchess <PERSON> of Russia (b. 1899) - <a href=\"https://wikipedia.orghttps://wikipedia.org/wiki/Grand_Duchess_<PERSON>_<PERSON>_of_Russia_(1899%E2%80%931918)\" class=\"mw-redirect\" title=\"Grand Duchess <PERSON> of Russia (1899-1918)\">Grand Duchess <PERSON> of Russia</a> (b. 1899)", "no_year_html": "<a href=\"https://wikipedia.orghttps://wikipedia.org/wiki/Grand_Duchess_<PERSON>_<PERSON>_of_Russia_(1899%E2%80%931918)\" class=\"mw-redirect\" title=\"Grand Duchess <PERSON> of Russia (1899-1918)\">Grand Duchess <PERSON> of Russia</a> (b. 1899)", "links": [{"title": "Grand Duchess <PERSON> of Russia (1899-1918)", "link": "https://wikipedia.orghttps://wikipedia.org/wiki/Grand_Duchess_<PERSON>_<PERSON>_of_Russia_(1899%E2%80%931918)"}]}, {"year": "Grand Duchess <PERSON> of Russia (b. 1901)", "text": null, "html": "Grand Duchess <PERSON> of Russia (b. 1901) - <a href=\"https://wikipedia.orghttps://wikipedia.org/wiki/Grand_Duchess_<PERSON>_<PERSON>_of_Russia\" title=\"Grand Duchess <PERSON> of Russia\">Grand Duchess <PERSON> of Russia</a> (b. 1901)", "no_year_html": "<a href=\"https://wikipedia.orghttps://wikipedia.org/wiki/Grand_Duchess_<PERSON>_<PERSON>_of_Russia\" title=\"Grand Duchess <PERSON> of Russia\">Grand Duchess <PERSON> of Russia</a> (b. 1901)", "links": [{"title": "Grand Duchess <PERSON> of Russia", "link": "https://wikipedia.orghttps://wikipedia.org/wiki/Grand_Duchess_<PERSON>_<PERSON>_of_Russia"}]}, {"year": "<PERSON>, <PERSON><PERSON> of Russia (b. 1904)", "text": null, "html": "<PERSON>, Tsarevich of Russia (b. 1904) - <a href=\"https://wikipedia.orghttps://wikipedia.org/wiki/<PERSON><PERSON>,_Tsarevich_of_Russia\" title=\"<PERSON>, Tsarevich of Russia\"><PERSON>, Tsarevich of Russia</a> (b. 1904)", "no_year_html": "<a href=\"https://wikipedia.orghttps://wikipedia.org/wiki/<PERSON>,_Tsarevich_of_Russia\" title=\"<PERSON>, Tsarevich of Russia\"><PERSON>, Tsarevich of Russia</a> (b. 1904)", "links": [{"title": "<PERSON>, <PERSON><PERSON> of Russia", "link": "https://wikipedia.orghttps://wikipedia.org/wiki/<PERSON>,_Tsarevich_of_Russia"}]}, {"year": "<PERSON> (b. 1878)", "text": null, "html": "<PERSON> (b. 1878) - <a href=\"https://wikipedia.orghttps://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> (b. 1878)", "no_year_html": "<a href=\"https://wikipedia.orghttps://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> (b. 1878)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.orghttps://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "<PERSON> (b. 1872)", "text": null, "html": "<PERSON> (b. 1872) - <a href=\"https://wikipedia.orghttps://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> (b. 1872)", "no_year_html": "<a href=\"https://wikipedia.orghttps://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> (b. 1872)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.orghttps://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "<PERSON> (b. 1858)", "text": null, "html": "<PERSON> (b. 1858) - <a href=\"https://wikipedia.orghttps://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> (b. 1858)", "no_year_html": "<a href=\"https://wikipedia.orghttps://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> (b. 1858)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.orghttps://wikipedia.org/wiki/<PERSON>_<PERSON>p"}]}, {"year": "<PERSON><PERSON><PERSON> (b. 1865)", "text": null, "html": "<PERSON><PERSON><PERSON> (b. 1865) - <a href=\"https://wikipedia.orghttps://wikipedia.org/wiki/Yev<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON>\">Ye<PERSON><PERSON></a> (b. 1865)", "no_year_html": "<a href=\"https://wikipedia.orghttps://wikipedia.org/wiki/Yev<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a> (b. 1865)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.orghttps://wikipedia.org/wiki/Yev<PERSON>_<PERSON>"}]}, {"year": "1925", "text": "<PERSON><PERSON>, German painter (b. 1858)", "html": "1925 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, German painter (b. 1858)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, German painter (b. 1858)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>th"}]}, {"year": "1928", "text": "<PERSON>, Italian politician, 13th Prime Minister of Italy (b. 1842)", "html": "1928 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian politician, 13th <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Italy\" title=\"Prime Minister of Italy\">Prime Minister of Italy</a> (b. 1842)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian politician, 13th <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Italy\" title=\"Prime Minister of Italy\">Prime Minister of Italy</a> (b. 1842)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>i"}, {"title": "Prime Minister of Italy", "link": "https://wikipedia.org/wiki/Prime_Minister_of_Italy"}]}, {"year": "1928", "text": "<PERSON><PERSON><PERSON>, Mexican general and politician, 39th President of Mexico (b. 1880)", "html": "1928 - <a href=\"https://wikipedia.org/wiki/%C3%81<PERSON><PERSON>_<PERSON>%C3%B3n\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Mexican general and politician, 39th <a href=\"https://wikipedia.org/wiki/President_of_Mexico\" title=\"President of Mexico\">President of Mexico</a> (b. 1880)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/%C3%81<PERSON><PERSON>_<PERSON>%C3%B3n\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Mexican general and politician, 39th <a href=\"https://wikipedia.org/wiki/President_of_Mexico\" title=\"President of Mexico\">President of Mexico</a> (b. 1880)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/%C3%81l<PERSON>_<PERSON>breg%C3%B3n"}, {"title": "President of Mexico", "link": "https://wikipedia.org/wiki/President_of_Mexico"}]}, {"year": "1932", "text": "<PERSON><PERSON>, Norwegian actor, singer, and director (b. 1862)", "html": "1932 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_(actor)\" title=\"<PERSON><PERSON> (actor)\"><PERSON><PERSON></a>, Norwegian actor, singer, and director (b. 1862)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_(actor)\" title=\"<PERSON><PERSON> (actor)\"><PERSON><PERSON></a>, Norwegian actor, singer, and director (b. 1862)", "links": [{"title": "<PERSON><PERSON> (actor)", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_(actor)"}]}, {"year": "1935", "text": "<PERSON>, Irish poet and painter (b. 1867)", "html": "1935 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Irish poet and painter (b. 1867)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Irish poet and painter (b. 1867)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1942", "text": "<PERSON><PERSON>, New Zealand photographer and suffragist (b. 1861)", "html": "1942 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, New Zealand photographer and suffragist (b. 1861)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, New Zealand photographer and suffragist (b. 1861)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>l"}]}, {"year": "1944", "text": "<PERSON>, American mathematician and anthropologist (b. 1898)", "html": "1944 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American mathematician and anthropologist (b. 1898)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American mathematician and anthropologist (b. 1898)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1945", "text": "<PERSON>, German field marshal (b. 1885)", "html": "1945 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(field_marshal)\" title=\"<PERSON> (field marshal)\"><PERSON></a>, German field marshal (b. 1885)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(field_marshal)\" title=\"<PERSON> (field marshal)\"><PERSON></a>, German field marshal (b. 1885)", "links": [{"title": "<PERSON> (field marshal)", "link": "https://wikipedia.org/wiki/<PERSON>(field_marshal)"}]}, {"year": "1946", "text": "<PERSON>, South African-born Australian artist (b. 1867)", "html": "1946 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, South African-born Australian artist (b. 1867)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, South African-born Australian artist (b. 1867)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Florence_Fuller"}]}, {"year": "1946", "text": "<PERSON><PERSON><PERSON><PERSON>, Serbian general (b. 1893)", "html": "1946 - <a href=\"https://wikipedia.org/wiki/Dra%C5%BEa_Mihailovi%C4%87\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Serbian general (b. 1893)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Dra%C5%BEa_Mihailovi%C4%87\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Serbian general (b. 1893)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Dra%C5%BEa_<PERSON><PERSON><PERSON>i%C4%87"}]}, {"year": "1950", "text": "<PERSON><PERSON><PERSON>, English 4th General of The Salvation Army (b. 1865)", "html": "1950 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, English 4th <a href=\"https://wikipedia.org/wiki/General_of_The_Salvation_Army\" title=\"General of The Salvation Army\">General of The Salvation Army</a> (b. 1865)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, English 4th <a href=\"https://wikipedia.org/wiki/General_of_The_Salvation_Army\" title=\"General of The Salvation Army\">General of The Salvation Army</a> (b. 1865)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}, {"title": "General of The Salvation Army", "link": "https://wikipedia.org/wiki/General_of_The_Salvation_Army"}]}, {"year": "1950", "text": "<PERSON><PERSON>, Czech actress (b. 1885)", "html": "1950 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>%C5%A1insk%C3%A1\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Czech actress (b. 1885)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>%C5%A1insk%C3%A1\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Czech actress (b. 1885)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Antonie_Nedo%C5%A1insk%C3%A1"}]}, {"year": "1959", "text": "<PERSON>, American singer (b. 1915)", "html": "1959 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Holiday\"><PERSON></a>, American singer (b. 1915)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Holiday\"><PERSON></a>, American singer (b. 1915)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1959", "text": "<PERSON>, American businessman and publisher (b. 1875)", "html": "1959 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(financier)\" title=\"<PERSON> (financier)\"><PERSON></a>, American businessman and publisher (b. 1875)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(financier)\" title=\"<PERSON> (financier)\"><PERSON></a>, American businessman and publisher (b. 1875)", "links": [{"title": "<PERSON> (financier)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(financier)"}]}, {"year": "1960", "text": "<PERSON>, Canadian physician and biochemist (b. 1879)", "html": "1960 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian physician and biochemist (b. 1879)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian physician and biochemist (b. 1879)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1961", "text": "<PERSON>, American baseball player and manager (b. 1886)", "html": "1961 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player and manager (b. 1886)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player and manager (b. 1886)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1961", "text": "<PERSON><PERSON>, Turkish architect and academic (b. 1908)", "html": "1961 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Turkish architect and academic (b. 1908)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Turkish architect and academic (b. 1908)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Emin_<PERSON><PERSON>_Onat"}]}, {"year": "1967", "text": "<PERSON>, American saxophonist and composer (b. 1926)", "html": "1967 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American saxophonist and composer (b. 1926)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American saxophonist and composer (b. 1926)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1974", "text": "<PERSON><PERSON>, American baseball player and sportscaster (b. 1910)", "html": "1974 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"Dizzy <PERSON>\"><PERSON><PERSON></a>, American baseball player and sportscaster (b. 1910)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"Dizzy <PERSON>\"><PERSON><PERSON></a>, American baseball player and sportscaster (b. 1910)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1975", "text": "<PERSON><PERSON>, Georgian author (b. 1893)", "html": "1975 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Georgian author (b. 1893)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Georgian author (b. 1893)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>dia"}]}, {"year": "1980", "text": "<PERSON> \"<PERSON>\" <PERSON>, American actor and screenwriter (b. 1912)", "html": "1980 - <a href=\"https://wikipedia.org/wiki/Don_%22Red%22_<PERSON>\" title='<PERSON> \"<PERSON>\" <PERSON>'><PERSON> \"<PERSON>\" <PERSON></a>, American actor and screenwriter (b. 1912)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Don_%22Red%22_<PERSON>\" title='<PERSON> \"<PERSON>\" <PERSON>'><PERSON> \"<PERSON>\" <PERSON></a>, American actor and screenwriter (b. 1912)", "links": [{"title": "<PERSON> \"<PERSON>\" <PERSON>", "link": "https://wikipedia.org/wiki/Don_%22Red%22_<PERSON>"}]}, {"year": "1980", "text": "<PERSON>, Russian mathematician and academic (b. 1890)", "html": "1980 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian mathematician and academic (b. 1890)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian mathematician and academic (b. 1890)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Boris_<PERSON>"}]}, {"year": "1988", "text": "<PERSON><PERSON><PERSON>, American football player and wrestler (b. 1946)", "html": "1988 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON> <PERSON>\"><PERSON><PERSON><PERSON> <PERSON></a>, American football player and wrestler (b. 1946)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON> <PERSON>\"><PERSON><PERSON><PERSON> <PERSON></a>, American football player and wrestler (b. 1946)", "links": [{"title": "Bruiser Brody", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1989", "text": "<PERSON><PERSON><PERSON>, Nauruan pastor and politician (b. 1922)", "html": "1989 - <a href=\"https://wikipedia.org/wiki/Itubwa_Amram\" title=\"Itubwa Amram\"><PERSON><PERSON><PERSON></a>, Nauruan pastor and politician (b. 1922)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Itubwa_Amram\" title=\"Itubwa Amram\"><PERSON><PERSON><PERSON></a>, Nauruan pastor and politician (b. 1922)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Itubwa_Amram"}]}, {"year": "1991", "text": "<PERSON>, American psychiatrist and academic (b. 1911)", "html": "1991 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American psychiatrist and academic (b. 1911)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American psychiatrist and academic (b. 1911)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1994", "text": "<PERSON>, French tennis player (b. 1898)", "html": "1994 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French tennis player (b. 1898)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French tennis player (b. 1898)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1995", "text": "<PERSON>, Argentinian race car driver (b. 1911)", "html": "1995 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Argentinian race car driver (b. 1911)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Argentinian race car driver (b. 1911)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1996", "text": "Victims of TWA Flight 800:\n<PERSON>, French ice hockey player (b. 1971)\n<PERSON>, Tunisian-French guitarist (b. 1951)\n<PERSON>, American composer (b. 1949)\n<PERSON>, American interior designer and director (b. 1948)", "html": "1996 - Victims of <a href=\"https://wikipedia.org/wiki/TWA_Flight_800\" title=\"TWA Flight 800\">TWA Flight 800</a>:\n<ul>\n<li>\n<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French ice hockey player (b. 1971)</li>\n<li>\n<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON> Dad<PERSON>\"><PERSON></a>, Tunisian-French guitarist (b. 1951)</li>\n<li>\n<a href=\"https://wikipedia.org/wiki/<PERSON>(composer)\" title=\"<PERSON> (composer)\"><PERSON></a>, American composer (b. 1949)</li>\n<li>\n<a href=\"https://wikipedia.org/wiki/<PERSON>(designer)\" title=\"<PERSON> (designer)\"><PERSON></a>, American interior designer and director (b. 1948)</li>\n</ul>", "no_year_html": "Victims of <a href=\"https://wikipedia.org/wiki/TWA_Flight_800\" title=\"TWA Flight 800\">TWA Flight 800</a>:\n<ul>\n<li>\n<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French ice hockey player (b. 1971)</li>\n<li>\n<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Tunisian-French guitarist (b. 1951)</li>\n<li>\n<a href=\"https://wikipedia.org/wiki/<PERSON>(composer)\" title=\"<PERSON> (composer)\"><PERSON></a>, American composer (b. 1949)</li>\n<li>\n<a href=\"https://wikipedia.org/wiki/<PERSON>(designer)\" title=\"<PERSON> (designer)\"><PERSON></a>, American interior designer and director (b. 1948)</li>\n</ul>", "links": [{"title": "TWA Flight 800", "link": "https://wikipedia.org/wiki/TWA_Flight_800"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "<PERSON> (composer)", "link": "https://wikipedia.org/wiki/<PERSON>(composer)"}, {"title": "<PERSON> (designer)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(designer)"}]}, {"year": "<PERSON>, French ice hockey player (b. 1971)", "text": null, "html": "<PERSON>, French ice hockey player (b. 1971) - <a href=\"https://wikipedia.orghttps://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French ice hockey player (b. 1971)", "no_year_html": "<a href=\"https://wikipedia.orghttps://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French ice hockey player (b. 1971)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.orghttps://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "<PERSON>, Tunisian-French guitarist (b. 1951)", "text": null, "html": "<PERSON>, Tunisian-French guitarist (b. 1951) - <a href=\"https://wikipedia.orghttps://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Tunisian-French guitarist (b. 1951)", "no_year_html": "<a href=\"https://wikipedia.orghttps://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Tunisian-French guitarist (b. 1951)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.orghttps://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "<PERSON>, American composer (b. 1949)", "text": null, "html": "<PERSON>, American composer (b. 1949) - <a href=\"https://wikipedia.orghttps://wikipedia.org/wiki/<PERSON>(composer)\" title=\"<PERSON> (composer)\"><PERSON></a>, American composer (b. 1949)", "no_year_html": "<a href=\"https://wikipedia.orghttps://wikipedia.org/wiki/<PERSON>(composer)\" title=\"<PERSON> (composer)\"><PERSON></a>, American composer (b. 1949)", "links": [{"title": "<PERSON> (composer)", "link": "https://wikipedia.orghttps://wikipedia.org/wiki/<PERSON>_<PERSON>_(composer)"}]}, {"year": "<PERSON>, American interior designer and director (b. 1948)", "text": null, "html": "<PERSON>, American interior designer and director (b. 1948) - <a href=\"https://wikipedia.orghttps://wikipedia.org/wiki/<PERSON>_<PERSON>_(designer)\" title=\"<PERSON> (designer)\"><PERSON></a>, American interior designer and director (b. 1948)", "no_year_html": "<a href=\"https://wikipedia.orghttps://wikipedia.org/wiki/<PERSON>_(designer)\" title=\"<PERSON> (designer)\"><PERSON></a>, American interior designer and director (b. 1948)", "links": [{"title": "<PERSON> (designer)", "link": "https://wikipedia.orghttps://wikipedia.org/wiki/<PERSON>_<PERSON>_(designer)"}]}, {"year": "1996", "text": "<PERSON><PERSON>, English bass player and producer (b. 1938)", "html": "1996 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, English bass player and producer (b. 1938)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, English bass player and producer (b. 1938)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1998", "text": "<PERSON>, American author and illustrator (b. 1925)", "html": "1998 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author and illustrator (b. 1925)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author and illustrator (b. 1925)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2001", "text": "<PERSON><PERSON>, American publisher (b. 1917)", "html": "2001 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American publisher (b. 1917)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American publisher (b. 1917)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "2002", "text": "<PERSON>, Dutch politician and Dutch Minister of Foreign Affairs (b. 1911)", "html": "2002 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch politician and <a href=\"https://wikipedia.org/wiki/Ministry_of_Foreign_Affairs_(Netherlands)\" title=\"Ministry of Foreign Affairs (Netherlands)\">Dutch Minister of Foreign Affairs</a> (b. 1911)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch politician and <a href=\"https://wikipedia.org/wiki/Ministry_of_Foreign_Affairs_(Netherlands)\" title=\"Ministry of Foreign Affairs (Netherlands)\">Dutch Minister of Foreign Affairs</a> (b. 1911)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Ministry of Foreign Affairs (Netherlands)", "link": "https://wikipedia.org/wiki/Ministry_of_Foreign_Affairs_(Netherlands)"}]}, {"year": "2003", "text": "<PERSON>, Welsh weapons inspector (b. 1944)", "html": "2003 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(weapons_expert)\" title=\"<PERSON> (weapons expert)\"><PERSON></a>, Welsh weapons inspector (b. 1944)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(weapons_expert)\" title=\"<PERSON> (weapons expert)\"><PERSON></a>, Welsh weapons inspector (b. 1944)", "links": [{"title": "<PERSON> (weapons expert)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(weapons_expert)"}]}, {"year": "2003", "text": "<PERSON><PERSON>, American pianist and harpsichord player (b. 1914)", "html": "2003 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American pianist and <a href=\"https://wikipedia.org/wiki/Harpsichord\" title=\"Harpsichord\">harpsichord</a> player (b. 1914)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American pianist and <a href=\"https://wikipedia.org/wiki/Harpsichord\" title=\"Harpsichord\">harpsichord</a> player (b. 1914)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}, {"title": "Harpsichord", "link": "https://wikipedia.org/wiki/Harpsichord"}]}, {"year": "2003", "text": "<PERSON>, Latvian-Swiss inventor, invented the Minox (b. 1905)", "html": "2003 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Latvian-Swiss inventor, invented the <a href=\"https://wikipedia.org/wiki/Minox\" title=\"Minox\"><PERSON><PERSON></a> (b. 1905)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Latvian-Swiss inventor, invented the <a href=\"https://wikipedia.org/wiki/Minox\" title=\"<PERSON>ox\"><PERSON><PERSON></a> (b. 1905)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Minox"}]}, {"year": "2005", "text": "<PERSON><PERSON>, Irish-American actress (b. 1913)", "html": "2005 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Irish-American actress (b. 1913)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Irish-American actress (b. 1913)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "2005", "text": "<PERSON>, English colonel and politician, Prime Minister of the United Kingdom (b. 1916)", "html": "2005 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English colonel and politician, <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_the_United_Kingdom\" title=\"Prime Minister of the United Kingdom\">Prime Minister of the United Kingdom</a> (b. 1916)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English colonel and politician, <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_the_United_Kingdom\" title=\"Prime Minister of the United Kingdom\">Prime Minister of the United Kingdom</a> (b. 1916)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Prime Minister of the United Kingdom", "link": "https://wikipedia.org/wiki/Prime_Minister_of_the_United_Kingdom"}]}, {"year": "2005", "text": "<PERSON>, Australian journalist and theorist (b. 1944)", "html": "2005 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian journalist and theorist (b. 1944)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian journalist and theorist (b. 1944)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2006", "text": "<PERSON>, American singer-songwriter (b. 1936)", "html": "2006 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter (b. 1936)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter (b. 1936)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2006", "text": "<PERSON>, American crime novelist (b. 1918)", "html": "2006 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American crime novelist (b. 1918)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American crime novelist (b. 1918)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2007", "text": "<PERSON>, American actor and businessman (b. 1959)", "html": "2007 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor and businessman (b. 1959)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor and businessman (b. 1959)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2007", "text": "<PERSON><PERSON><PERSON>, Brazilian politician (b. 1956)", "html": "2007 - <a href=\"https://wikipedia.org/wiki/J%C3%BAlio_Redecker\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Brazilian politician (b. 1956)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/J%C3%<PERSON>lio_Redecker\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Brazilian politician (b. 1956)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/J%C3%BAlio_Redecker"}]}, {"year": "2007", "text": "<PERSON>, Brazilian lawyer and businessman (b. 1945)", "html": "2007 - <a href=\"https://wikipedia.org/wiki/Paulo_Rog%C3%A<PERSON><PERSON>_Amoretty_Souza\" title=\"Paulo Rogério <PERSON> So<PERSON>\"><PERSON></a>, Brazilian lawyer and businessman (b. 1945)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Paulo_Rog%C3%A<PERSON><PERSON>_<PERSON>etty_Souza\" title=\"Paulo Rogério <PERSON>etty Souza\"><PERSON></a>, Brazilian lawyer and businessman (b. 1945)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Paulo_Rog%C3%A9rio_Amoretty_Souza"}]}, {"year": "2009", "text": "<PERSON>, American journalist and actor (b. 1916)", "html": "2009 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American journalist and actor (b. 1916)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American journalist and actor (b. 1916)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Walter_<PERSON>ronkite"}]}, {"year": "2009", "text": "<PERSON><PERSON><PERSON>, Polish historian and philosopher (b. 1927)", "html": "2009 - <a href=\"https://wikipedia.org/wiki/<PERSON>zek_Ko%C5%82akowski\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Polish historian and philosopher (b. 1927)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>k_Ko%C5%82akowski\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Polish historian and philosopher (b. 1927)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Leszek_Ko%C5%82akowski"}]}, {"year": "2010", "text": "<PERSON>, American actor (b. 1931)", "html": "2010 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor (b. 1931)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor (b. 1931)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2011", "text": "<PERSON>, Australian actor (b. 1967)", "html": "2011 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian actor (b. 1967)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian actor (b. 1967)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "2012", "text": "<PERSON>, English boxer (b. 1973)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English boxer (b. 1973)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English boxer (b. 1973)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2012", "text": "<PERSON>, American general (b. 1931)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American general (b. 1931)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American general (b. 1931)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "2012", "text": "<PERSON><PERSON><PERSON>, Turkish-American composer and producer (b. 1926)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/%C4%B0lhan_Mimaro%C4%9Flu\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Turkish-American composer and producer (b. 1926)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/%C4%B0<PERSON>han_Mimaro%C4%9Flu\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Turkish-American composer and producer (b. 1926)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/%C4%B0lhan_<PERSON>maro%C4%9Flu"}]}, {"year": "2012", "text": "<PERSON>, American journalist and academic (b. 1935)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American journalist and academic (b. 1935)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American journalist and academic (b. 1935)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2012", "text": "<PERSON><PERSON>, Indian-English politician (b. 1954)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Indian-English politician (b. 1954)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Indian-English politician (b. 1954)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "2013", "text": "<PERSON>, English-French journalist and author (b. 1921)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English-French journalist and author (b. 1921)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English-French journalist and author (b. 1921)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2013", "text": "<PERSON>, English-Canadian vibraphone player and composer (b. 1928)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English-Canadian <a href=\"https://wikipedia.org/wiki/Vibraphone\" title=\"Vibraphone\">vibraphone</a> player and composer (b. 1928)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English-Canadian <a href=\"https://wikipedia.org/wiki/Vibraphone\" title=\"Vibraphone\">vibraphone</a> player and composer (b. 1928)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Vibraphone", "link": "https://wikipedia.org/wiki/Vibraphone"}]}, {"year": "2013", "text": "<PERSON>, Italian screenwriter and producer (b. 1940)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian screenwriter and producer (b. 1940)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian screenwriter and producer (b. 1940)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2013", "text": "<PERSON>, American tennis player (b. 1933)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American tennis player (b. 1933)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Flye\"><PERSON></a>, American tennis player (b. 1933)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>e"}]}, {"year": "2013", "text": "<PERSON>, English general (b. 1920)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English general (b. 1920)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English general (b. 1920)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2013", "text": "<PERSON>, Scottish footballer and manager (b. 1933)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(Scottish_footballer)\" title=\"<PERSON> (Scottish footballer)\"><PERSON></a>, Scottish footballer and manager (b. 1933)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(Scottish_footballer)\" title=\"<PERSON> (Scottish footballer)\"><PERSON></a>, Scottish footballer and manager (b. 1933)", "links": [{"title": "<PERSON> (Scottish footballer)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(Scottish_footballer)"}]}, {"year": "2014", "text": "Malaysia Airlines Flight 17 victims:\n<PERSON>, Australian author and critic (b. 1957)\n<PERSON><PERSON>, Malaysian actress (b. 1976)\n<PERSON><PERSON>, Dutch physician and academic (b. 1954)\n<PERSON>, Dutch scholar and politician (b. 1952)", "html": "2014 - <a href=\"https://wikipedia.org/wiki/Malaysia_Airlines_Flight_17\" title=\"Malaysia Airlines Flight 17\">Malaysia Airlines Flight 17</a> victims:\n<ul>\n<li>\n<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian author and critic (b. 1957)</li>\n<li>\n<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Malaysian actress (b. 1976)</li>\n<li>\n<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Dutch physician and academic (b. 1954)</li>\n<li>\n<a href=\"https://wikipedia.org/wiki/Willem_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch scholar and politician (b. 1952)</li>\n</ul>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Malaysia_Airlines_Flight_17\" title=\"Malaysia Airlines Flight 17\">Malaysia Airlines Flight 17</a> victims:\n<ul>\n<li>\n<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian author and critic (b. 1957)</li>\n<li>\n<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Malaysian actress (b. 1976)</li>\n<li>\n<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Dutch physician and academic (b. 1954)</li>\n<li>\n<a href=\"https://wikipedia.org/wiki/Willem_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch scholar and politician (b. 1952)</li>\n</ul>", "links": [{"title": "Malaysia Airlines Flight 17", "link": "https://wikipedia.org/wiki/Malaysia_Airlines_Flight_17"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}, {"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Willem_<PERSON>itte<PERSON>en"}]}, {"year": "<PERSON>, Australian author and critic (b. 1957)", "text": null, "html": "<PERSON>, Australian author and critic (b. 1957) - <a href=\"https://wikipedia.orghttps://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian author and critic (b. 1957)", "no_year_html": "<a href=\"https://wikipedia.orghttps://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian author and critic (b. 1957)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.orghttps://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "<PERSON><PERSON>, Malaysian actress (b. 1976)", "text": null, "html": "<PERSON><PERSON>, Malaysian actress (b. 1976) - <a href=\"https://wikipedia.orghttps://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Malaysian actress (b. 1976)", "no_year_html": "<a href=\"https://wikipedia.orghttps://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> <PERSON>\"><PERSON><PERSON></a>, Malaysian actress (b. 1976)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.orghttps://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "<PERSON><PERSON>, Dutch physician and academic (b. 1954)", "text": null, "html": "<PERSON><PERSON>, Dutch physician and academic (b. 1954) - <a href=\"https://wikipedia.orghttps://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Dutch physician and academic (b. 1954)", "no_year_html": "<a href=\"https://wikipedia.orghttps://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Dutch physician and academic (b. 1954)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.orghttps://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "<PERSON>, Dutch scholar and politician (b. 1952)", "text": null, "html": "<PERSON>, Dutch scholar and politician (b. 1952) - <a href=\"https://wikipedia.orghttps://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch scholar and politician (b. 1952)", "no_year_html": "<a href=\"https://wikipedia.orghttps://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch scholar and politician (b. 1952)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.orghttps://wikipedia.org/wiki/Willem_<PERSON>itte<PERSON>en"}]}, {"year": "2014", "text": "<PERSON>, American colonel, pilot, and astronaut (b. 1933)", "html": "2014 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American colonel, pilot, and astronaut (b. 1933)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American colonel, pilot, and astronaut (b. 1933)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "2014", "text": "<PERSON>, German sculptor and academic (b. 1928)", "html": "2014 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German sculptor and academic (b. 1928)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German sculptor and academic (b. 1928)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2014", "text": "<PERSON>, American actress and singer (b. 1925)", "html": "2014 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress and singer (b. 1925)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress and singer (b. 1925)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2015", "text": "<PERSON>, American football player and coach (b. 1926)", "html": "2015 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player and coach (b. 1926)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player and coach (b. 1926)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2015", "text": "<PERSON>, French race car driver (b. 1989)", "html": "2015 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French race car driver (b. 1989)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French race car driver (b. 1989)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2015", "text": "<PERSON>, English rugby player, historian, and academic (b. 1916)", "html": "2015 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English rugby player, historian, and academic (b. 1916)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English rugby player, historian, and academic (b. 1916)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2015", "text": "<PERSON>, American sportscaster (b. 1927)", "html": "2015 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American sportscaster (b. 1927)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American sportscaster (b. 1927)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2015", "text": "<PERSON>, English pianist and educator (b. 1942)", "html": "2015 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(jazz)\" title=\"<PERSON> (jazz)\"><PERSON></a>, English pianist and educator (b. 1942)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(jazz)\" title=\"<PERSON> (jazz)\"><PERSON></a>, English pianist and educator (b. 1942)", "links": [{"title": "<PERSON> (jazz)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(jazz)"}]}, {"year": "2019", "text": "<PERSON>, German historian and blogger who falsely claimed to be descended from Holocaust survivors", "html": "2019 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German historian and blogger who falsely claimed to be descended from <a href=\"https://wikipedia.org/wiki/Holocaust_survivor\" class=\"mw-redirect\" title=\"Holocaust survivor\">Holocaust survivors</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German historian and blogger who falsely claimed to be descended from <a href=\"https://wikipedia.org/wiki/Holocaust_survivor\" class=\"mw-redirect\" title=\"Holocaust survivor\">Holocaust survivors</a>", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "Holocaust survivor", "link": "https://wikipedia.org/wiki/Holocaust_survivor"}]}, {"year": "2020", "text": "<PERSON>, American Politician and Civil Rights Leader. (b. 1940)", "html": "2020 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American Politician and Civil Rights Leader. (b. 1940)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American Politician and Civil Rights Leader. (b. 1940)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2020", "text": "<PERSON><PERSON><PERSON>, Russian-Australian pair skater (b. 2000)", "html": "2020 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Russian-Australian pair skater (b. 2000)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Russian-Australian pair skater (b. 2000)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON><PERSON>"}]}, {"year": "2024", "text": "<PERSON>, Chinese actress (b. 1946)", "html": "2024 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>-<PERSON>\" title=\"<PERSON>-pei\"><PERSON></a>, Chinese actress (b. 1946)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>-<PERSON>\" title=\"<PERSON>ei-pei\"><PERSON></a>, Chinese actress (b. 1946)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>-<PERSON>ei"}]}, {"year": "2024", "text": "<PERSON>, British botanist and professor (b. 1949)", "html": "2024 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, British botanist and professor (b. 1949)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, British botanist and professor (b. 1949)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>"}]}, {"year": "2024", "text": "<PERSON><PERSON>, American singer, songwriter and scholar (b. 1942)", "html": "2024 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American singer, songwriter and scholar (b. 1942)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American singer, songwriter and scholar (b. 1942)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>"}]}, {"year": "2024", "text": "<PERSON>, American basketball player (b. 1940)", "html": "2024 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(basketball)\" title=\"<PERSON> (basketball)\"><PERSON></a>, American basketball player (b. 1940)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(basketball)\" title=\"<PERSON> (basketball)\"><PERSON></a>, American basketball player (b. 1940)", "links": [{"title": "<PERSON> (basketball)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(basketball)"}]}]}}