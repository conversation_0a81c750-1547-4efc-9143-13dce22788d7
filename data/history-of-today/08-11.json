{"date": "August 11", "url": "https://wikipedia.org/wiki/August_11", "data": {"Events": [{"year": "3114 BC", "text": "The Mesoamerican Long Count calendar, used by several pre-Columbian Mesoamerican civilizations, notably the Maya, begins.", "html": "3114 BC - 3114 BC - The <a href=\"https://wikipedia.org/wiki/Mesoamerican_Long_Count_calendar\" title=\"Mesoamerican Long Count calendar\">Mesoamerican Long Count calendar</a>, used by several <a href=\"https://wikipedia.org/wiki/Pre-Columbian_era\" title=\"Pre-Columbian era\">pre-Columbian</a> <a href=\"https://wikipedia.org/wiki/Mesoamerica\" title=\"Mesoamerica\">Mesoamerican</a> civilizations, notably the <a href=\"https://wikipedia.org/wiki/Maya_civilization\" title=\"Maya civilization\">Maya</a>, begins.", "no_year_html": "3114 BC - The <a href=\"https://wikipedia.org/wiki/Mesoamerican_Long_Count_calendar\" title=\"Mesoamerican Long Count calendar\">Mesoamerican Long Count calendar</a>, used by several <a href=\"https://wikipedia.org/wiki/Pre-Columbian_era\" title=\"Pre-Columbian era\">pre-Columbian</a> <a href=\"https://wikipedia.org/wiki/Mesoamerica\" title=\"Mesoamerica\">Mesoamerican</a> civilizations, notably the <a href=\"https://wikipedia.org/wiki/Maya_civilization\" title=\"Maya civilization\">Maya</a>, begins.", "links": [{"title": "Mesoamerican Long Count calendar", "link": "https://wikipedia.org/wiki/Mesoamerican_Long_Count_calendar"}, {"title": "Pre-Columbian era", "link": "https://wikipedia.org/wiki/Pre-Columbian_era"}, {"title": "Mesoamerica", "link": "https://wikipedia.org/wiki/Mesoamerica"}, {"title": "Maya civilization", "link": "https://wikipedia.org/wiki/Maya_civilization"}]}, {"year": "2492 BC", "text": "Traditional date of the defeat of <PERSON> by <PERSON><PERSON>, progenitor and founder of the Armenian nation.", "html": "2492 BC - 2492 BC - Traditional date of the defeat of <a href=\"https://wikipedia.org/wiki/Bel_(mythology)\" title=\"<PERSON> (mythology)\">Bel</a> by <a href=\"https://wikipedia.org/wiki/Hayk\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, progenitor and founder of the <a href=\"https://wikipedia.org/wiki/Armenians\" title=\"Armenians\">Armenian</a> nation.", "no_year_html": "2492 BC - Traditional date of the defeat of <a href=\"https://wikipedia.org/wiki/Bel_(mythology)\" title=\"<PERSON> (mythology)\">Bel</a> by <a href=\"https://wikipedia.org/wiki/Hayk\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, progenitor and founder of the <a href=\"https://wikipedia.org/wiki/Armenians\" title=\"Armenians\">Armenian</a> nation.", "links": [{"title": "<PERSON> (mythology)", "link": "https://wikipedia.org/wiki/<PERSON>_(mythology)"}, {"title": "Hayk", "link": "https://wikipedia.org/wiki/Hayk"}, {"title": "Armenians", "link": "https://wikipedia.org/wiki/Armenians"}]}, {"year": "106", "text": "The south-western part of Dacia (modern Romania) becomes a Roman province: Roman Dacia.", "html": "106 - The south-western part of <a href=\"https://wikipedia.org/wiki/Dacia\" title=\"Dacia\">Dacia</a> (modern <a href=\"https://wikipedia.org/wiki/Romania\" title=\"Romania\">Romania</a>) becomes a <a href=\"https://wikipedia.org/wiki/Roman_province\" title=\"Roman province\">Roman province</a>: <a href=\"https://wikipedia.org/wiki/Roman_Dacia\" title=\"Roman Dacia\">Roman Dacia</a>.", "no_year_html": "The south-western part of <a href=\"https://wikipedia.org/wiki/Dacia\" title=\"Dacia\">Dacia</a> (modern <a href=\"https://wikipedia.org/wiki/Romania\" title=\"Romania\">Romania</a>) becomes a <a href=\"https://wikipedia.org/wiki/Roman_province\" title=\"Roman province\">Roman province</a>: <a href=\"https://wikipedia.org/wiki/Roman_Dacia\" title=\"Roman Dacia\">Roman Dacia</a>.", "links": [{"title": "Dacia", "link": "https://wikipedia.org/wiki/Dacia"}, {"title": "Romania", "link": "https://wikipedia.org/wiki/Romania"}, {"title": "Roman province", "link": "https://wikipedia.org/wiki/Roman_province"}, {"title": "Roman <PERSON>", "link": "https://wikipedia.org/wiki/Roman_Dacia"}]}, {"year": "117", "text": "<PERSON><PERSON> is proclaimed Roman emperor, two days after <PERSON><PERSON><PERSON>'s death.", "html": "117 - <a href=\"https://wikipedia.org/wiki/Hadrian\" title=\"Hadrian\"><PERSON><PERSON></a> is proclaimed <a href=\"https://wikipedia.org/wiki/Roman_emperor\" title=\"Roman emperor\">Roman emperor</a>, two days after <a href=\"https://wikipedia.org/wiki/T<PERSON>an\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>'s death.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Hadrian\" title=\"Hadrian\"><PERSON><PERSON></a> is proclaimed <a href=\"https://wikipedia.org/wiki/Roman_emperor\" title=\"Roman emperor\">Roman emperor</a>, two days after <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>'s death.", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>"}, {"title": "Roman emperor", "link": "https://wikipedia.org/wiki/Roman_emperor"}, {"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>"}]}, {"year": "355", "text": "<PERSON><PERSON><PERSON>, accused of treason, proclaims himself Roman Emperor against <PERSON><PERSON><PERSON>.", "html": "355 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, accused of treason, proclaims himself <a href=\"https://wikipedia.org/wiki/Roman_emperor\" title=\"Roman emperor\">Roman Emperor</a> against <a href=\"https://wikipedia.org/wiki/Constantius_II\" title=\"Constantius II\">Constantius II</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, accused of treason, proclaims himself <a href=\"https://wikipedia.org/wiki/Roman_emperor\" title=\"Roman emperor\">Roman Emperor</a> against <a href=\"https://wikipedia.org/wiki/Constantius_II\" title=\"Constantius II\">Constantius II</a>.", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}, {"title": "Roman emperor", "link": "https://wikipedia.org/wiki/Roman_emperor"}, {"title": "<PERSON><PERSON><PERSON> II", "link": "https://wikipedia.org/wiki/Constantius_II"}]}, {"year": "490", "text": "Battle of Adda: The Goths under <PERSON><PERSON> the <PERSON> and his ally <PERSON><PERSON><PERSON> defeat the forces of Odoacer on the Adda River, near Milan.", "html": "490 - Battle of Adda: The <a href=\"https://wikipedia.org/wiki/Goths\" title=\"Goths\">Goths</a> under <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_the_Great\" title=\"<PERSON><PERSON> the Great\"><PERSON><PERSON> the Great</a> and his ally <a href=\"https://wikipedia.org/wiki/Alaric_II\" title=\"Alaric II\">Alaric II</a> defeat the forces of <a href=\"https://wikipedia.org/wiki/Odoacer\" title=\"Odoacer\"><PERSON><PERSON><PERSON>r</a> on the <a href=\"https://wikipedia.org/wiki/Adda_River,_Italy\" class=\"mw-redirect\" title=\"Adda River, Italy\">Adda River</a>, near <a href=\"https://wikipedia.org/wiki/Milan\" title=\"Milan\">Milan</a>.", "no_year_html": "Battle of Adda: The <a href=\"https://wikipedia.org/wiki/Goths\" title=\"Goths\">Goths</a> under <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_the_Great\" title=\"<PERSON><PERSON> the Great\"><PERSON><PERSON> the Great</a> and his ally <a href=\"https://wikipedia.org/wiki/Alaric_II\" title=\"Alaric II\">Alaric II</a> defeat the forces of <a href=\"https://wikipedia.org/wiki/Odoacer\" title=\"Odoacer\"><PERSON><PERSON><PERSON>r</a> on the <a href=\"https://wikipedia.org/wiki/Adda_River,_Italy\" class=\"mw-redirect\" title=\"Adda River, Italy\">Adda River</a>, near <a href=\"https://wikipedia.org/wiki/Milan\" title=\"Milan\">Milan</a>.", "links": [{"title": "Goths", "link": "https://wikipedia.org/wiki/Goths"}, {"title": "<PERSON><PERSON> the Great", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_the_Great"}, {"title": "Alaric II", "link": "https://wikipedia.org/wiki/Alaric_II"}, {"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Odoacer"}, {"title": "Adda River, Italy", "link": "https://wikipedia.org/wiki/Adda_River,_Italy"}, {"title": "Milan", "link": "https://wikipedia.org/wiki/Milan"}]}, {"year": "923", "text": "The Qarmatians of Bahrayn capture and pillage the city of Basra.", "html": "923 - The <a href=\"https://wikipedia.org/wiki/Qarmatians\" title=\"Qarmatians\">Qarmatians</a> of <a href=\"https://wikipedia.org/wiki/Bahrayn_(historical_region)\" class=\"mw-redirect\" title=\"Bahrayn (historical region)\">Bahrayn</a> <a href=\"https://wikipedia.org/wiki/Sack_of_Basra_(923)\" title=\"Sack of Basra (923)\">capture and pillage</a> the city of <a href=\"https://wikipedia.org/wiki/Basra\" title=\"Basra\">Basra</a>.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Qarmatians\" title=\"Qarmatians\">Qarmatians</a> of <a href=\"https://wikipedia.org/wiki/Bahrayn_(historical_region)\" class=\"mw-redirect\" title=\"Bahrayn (historical region)\">Bahrayn</a> <a href=\"https://wikipedia.org/wiki/Sack_of_Basra_(923)\" title=\"Sack of Basra (923)\">capture and pillage</a> the city of <a href=\"https://wikipedia.org/wiki/Basra\" title=\"Basra\">Basra</a>.", "links": [{"title": "Qarmatians", "link": "https://wikipedia.org/wiki/Qarmatians"}, {"title": "Bahrayn (historical region)", "link": "https://wikipedia.org/wiki/Bahrayn_(historical_region)"}, {"title": "Sack of Basra (923)", "link": "https://wikipedia.org/wiki/Sack_of_Basra_(923)"}, {"title": "Basra", "link": "https://wikipedia.org/wiki/Basra"}]}, {"year": "1315", "text": "The Great Famine of Europe becomes so dire that even the king of England has difficulties buying bread for himself and his entourage.", "html": "1315 - The <a href=\"https://wikipedia.org/wiki/Great_Famine_of_1315%E2%80%931317\" title=\"Great Famine of 1315-1317\">Great Famine of Europe</a> becomes so dire that even the king of England has difficulties buying bread for himself and his entourage.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Great_Famine_of_1315%E2%80%931317\" title=\"Great Famine of 1315-1317\">Great Famine of Europe</a> becomes so dire that even the king of England has difficulties buying bread for himself and his entourage.", "links": [{"title": "Great Famine of 1315-1317", "link": "https://wikipedia.org/wiki/Great_Famine_of_1315%E2%80%931317"}]}, {"year": "1332", "text": "Wars of Scottish Independence: Battle of Dupplin Moor: Scots under <PERSON><PERSON><PERSON>, Earl of Mar are routed by <PERSON>.", "html": "1332 - <a href=\"https://wikipedia.org/wiki/Wars_of_Scottish_Independence\" title=\"Wars of Scottish Independence\">Wars of Scottish Independence</a>: <a href=\"https://wikipedia.org/wiki/Battle_of_Dupplin_Moor\" title=\"Battle of Dupplin Moor\">Battle of Dupplin Moor</a>: Scots under <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>,_Earl_<PERSON>_Mar\" title=\"<PERSON><PERSON><PERSON>, Earl <PERSON>\"><PERSON><PERSON><PERSON>, Earl <PERSON></a> are routed by <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Wars_of_Scottish_Independence\" title=\"Wars of Scottish Independence\">Wars of Scottish Independence</a>: <a href=\"https://wikipedia.org/wiki/Battle_of_Dupplin_Moor\" title=\"Battle of Dupplin Moor\">Battle of Dupplin Moor</a>: Scots under <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>,_Earl_<PERSON>_Mar\" title=\"<PERSON><PERSON><PERSON>, Earl <PERSON>\"><PERSON><PERSON><PERSON>, Earl <PERSON></a> are routed by <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>.", "links": [{"title": "Wars of Scottish Independence", "link": "https://wikipedia.org/wiki/Wars_of_Scottish_Independence"}, {"title": "Battle of Dupplin Moor", "link": "https://wikipedia.org/wiki/Battle_of_Dupplin_Moor"}, {"title": "<PERSON><PERSON><PERSON> <PERSON>, Earl of Mar", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>,_Earl_of_Mar"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1473", "text": "The Battle of Otlukbeli: <PERSON><PERSON><PERSON> the Conqueror of the Ottoman Empire decisively defeats <PERSON><PERSON><PERSON> of Aq <PERSON>.", "html": "1473 - The <a href=\"https://wikipedia.org/wiki/Battle_of_Otlukbeli\" title=\"Battle of Otlukbeli\">Battle of Otlukbeli</a>: <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_the_Conqueror\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON> the Conqueror\"><PERSON><PERSON><PERSON> the Conqueror</a> of the <a href=\"https://wikipedia.org/wiki/Ottoman_Empire\" title=\"Ottoman Empire\">Ottoman Empire</a> decisively defeats <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a> of <a href=\"https://wikipedia.org/wiki/Aq_Qoy<PERSON>lu\" title=\"Aq Qoyunlu\"><PERSON><PERSON></a>.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Battle_of_Otlukbeli\" title=\"Battle of Otlukbeli\">Battle of Otlukbeli</a>: <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_the_Conqueror\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON> the Conqueror\"><PERSON><PERSON><PERSON> the Conqueror</a> of the <a href=\"https://wikipedia.org/wiki/Ottoman_Empire\" title=\"Ottoman Empire\">Ottoman Empire</a> decisively defeats <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a> of <a href=\"https://wikipedia.org/wiki/Aq_Qoy<PERSON>lu\" title=\"Aq Qoyunlu\"><PERSON><PERSON></a>.", "links": [{"title": "Battle of Otlukbeli", "link": "https://wikipedia.org/wiki/Battle_of_Otlukbeli"}, {"title": "<PERSON><PERSON><PERSON> the Conqueror", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_the_Conqueror"}, {"title": "Ottoman Empire", "link": "https://wikipedia.org/wiki/Ottoman_Empire"}, {"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}, {"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/A<PERSON>_<PERSON>lu"}]}, {"year": "1492", "text": "<PERSON> is elected as Head of the Catholic Church, taking the name <PERSON>.", "html": "1492 - <PERSON> is elected as Head of the Catholic Church, taking the name <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\">Pope <PERSON> VI</a>.", "no_year_html": "<PERSON> is elected as Head of the Catholic Church, taking the name <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\">Pope <PERSON> VI</a>.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1675", "text": "Franco-Dutch War: Forces of the Holy Roman Empire defeat the French in the Battle of Konzer Brücke.", "html": "1675 - <a href=\"https://wikipedia.org/wiki/Franco-Dutch_War\" title=\"Franco-Dutch War\">Franco-Dutch War</a>: Forces of the <a href=\"https://wikipedia.org/wiki/Holy_Roman_Empire\" title=\"Holy Roman Empire\">Holy Roman Empire</a> defeat the <a href=\"https://wikipedia.org/wiki/France\" title=\"France\">French</a> in the <a href=\"https://wikipedia.org/wiki/Battle_of_Konzer_Br%C3%BCcke\" title=\"Battle of Konzer Brücke\">Battle of Konzer Brücke</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Franco-Dutch_War\" title=\"Franco-Dutch War\">Franco-Dutch War</a>: Forces of the <a href=\"https://wikipedia.org/wiki/Holy_Roman_Empire\" title=\"Holy Roman Empire\">Holy Roman Empire</a> defeat the <a href=\"https://wikipedia.org/wiki/France\" title=\"France\">French</a> in the <a href=\"https://wikipedia.org/wiki/Battle_of_Konzer_Br%C3%BCcke\" title=\"Battle of Konzer Brücke\">Battle of Konzer Brücke</a>.", "links": [{"title": "Franco-Dutch War", "link": "https://wikipedia.org/wiki/Franco-Dutch_War"}, {"title": "Holy Roman Empire", "link": "https://wikipedia.org/wiki/Holy_Roman_Empire"}, {"title": "France", "link": "https://wikipedia.org/wiki/France"}, {"title": "Battle of Konzer Brücke", "link": "https://wikipedia.org/wiki/Battle_of_Konzer_Br%C3%BCcke"}]}, {"year": "1685", "text": "Morean War: The 49-day Siege of Coron ends with the surrender and massacre of its garrison by the Venetians.", "html": "1685 - <a href=\"https://wikipedia.org/wiki/Morean_War\" title=\"Morean War\">Morean War</a>: The 49-day <a href=\"https://wikipedia.org/wiki/Siege_of_Coron_(1685)\" title=\"Siege of Coron (1685)\">Siege of Coron</a> ends with the surrender and massacre of its garrison by the Venetians.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Morean_War\" title=\"Morean War\">Morean War</a>: The 49-day <a href=\"https://wikipedia.org/wiki/Siege_of_Coron_(1685)\" title=\"Siege of Coron (1685)\">Siege of Coron</a> ends with the surrender and massacre of its garrison by the Venetians.", "links": [{"title": "Morean War", "link": "https://wikipedia.org/wiki/Morean_War"}, {"title": "Siege of Coron (1685)", "link": "https://wikipedia.org/wiki/Siege_of_Coron_(1685)"}]}, {"year": "1786", "text": "Captain <PERSON> establishes the British colony of Penang in Malaysia.", "html": "1786 - Captain <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Francis Light\"><PERSON></a> establishes the British colony of <a href=\"https://wikipedia.org/wiki/Penang\" title=\"Penang\">Penang</a> in <a href=\"https://wikipedia.org/wiki/Malaysia\" title=\"Malaysia\">Malaysia</a>.", "no_year_html": "Captain <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Francis Light\"><PERSON></a> establishes the British colony of <a href=\"https://wikipedia.org/wiki/Penang\" title=\"Penang\">Penang</a> in <a href=\"https://wikipedia.org/wiki/Malaysia\" title=\"Malaysia\">Malaysia</a>.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Penang", "link": "https://wikipedia.org/wiki/Penang"}, {"title": "Malaysia", "link": "https://wikipedia.org/wiki/Malaysia"}]}, {"year": "1804", "text": "<PERSON> assumes the title of first Emperor of Austria.", "html": "1804 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Holy_Roman_Emperor\" title=\"<PERSON> II, Holy Roman Emperor\"><PERSON> II</a> assumes the title of first <a href=\"https://wikipedia.org/wiki/Emperor_of_Austria\" title=\"Emperor of Austria\">Emperor of Austria</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Holy_Roman_Emperor\" title=\"<PERSON> II, Holy Roman Emperor\"><PERSON> II</a> assumes the title of first <a href=\"https://wikipedia.org/wiki/Emperor_of_Austria\" title=\"Emperor of Austria\">Emperor of Austria</a>.", "links": [{"title": "<PERSON>, Holy Roman Emperor", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Holy_Roman_Emperor"}, {"title": "Emperor of Austria", "link": "https://wikipedia.org/wiki/Emperor_of_Austria"}]}, {"year": "1812", "text": "Peninsular War: French troops engage British-Portuguese forces in the Battle of Majadahonda.", "html": "1812 - <a href=\"https://wikipedia.org/wiki/Peninsular_War\" title=\"Peninsular War\">Peninsular War</a>: French troops engage <a href=\"https://wikipedia.org/wiki/United_Kingdom\" title=\"United Kingdom\">British</a>-<a href=\"https://wikipedia.org/wiki/Portugal\" title=\"Portugal\">Portuguese</a> forces in the <a href=\"https://wikipedia.org/wiki/Battle_of_Majadahonda\" title=\"Battle of Majadahonda\">Battle of Majadahonda</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Peninsular_War\" title=\"Peninsular War\">Peninsular War</a>: French troops engage <a href=\"https://wikipedia.org/wiki/United_Kingdom\" title=\"United Kingdom\">British</a>-<a href=\"https://wikipedia.org/wiki/Portugal\" title=\"Portugal\">Portuguese</a> forces in the <a href=\"https://wikipedia.org/wiki/Battle_of_Majadahonda\" title=\"Battle of Majadahonda\">Battle of Majadahonda</a>.", "links": [{"title": "Peninsular War", "link": "https://wikipedia.org/wiki/Peninsular_War"}, {"title": "United Kingdom", "link": "https://wikipedia.org/wiki/United_Kingdom"}, {"title": "Portugal", "link": "https://wikipedia.org/wiki/Portugal"}, {"title": "Battle of Majadahonda", "link": "https://wikipedia.org/wiki/Battle_of_Majadahonda"}]}, {"year": "1813", "text": "In Colombia, <PERSON> declares the independence of Antioquia.", "html": "1813 - In <a href=\"https://wikipedia.org/wiki/Colombia\" title=\"Colombia\">Colombia</a>, <PERSON> declares the independence of <a href=\"https://wikipedia.org/wiki/Antioquia_Department\" title=\"Antioquia Department\">Antioquia</a>.", "no_year_html": "In <a href=\"https://wikipedia.org/wiki/Colombia\" title=\"Colombia\">Colombia</a>, <PERSON> declares the independence of <a href=\"https://wikipedia.org/wiki/Antioquia_Department\" title=\"Antioquia Department\">Antioquia</a>.", "links": [{"title": "Colombia", "link": "https://wikipedia.org/wiki/Colombia"}, {"title": "Antioquia Department", "link": "https://wikipedia.org/wiki/Antioquia_Department"}]}, {"year": "1858", "text": "The Eiger in the Bernese Alps is ascended for the first time by <PERSON> accompanied by <PERSON> and <PERSON>.", "html": "1858 - The <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a> in the <a href=\"https://wikipedia.org/wiki/Bernese_Alps\" title=\"Bernese Alps\">Bernese Alps</a> is ascended for the first time by <a href=\"https://wikipedia.org/wiki/<PERSON>_(mountaineer)\" title=\"<PERSON> (mountaineer)\"><PERSON></a> accompanied by <a href=\"https://wikipedia.org/wiki/Christian_<PERSON>\" title=\"<PERSON>\"><PERSON></a> and <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a> in the <a href=\"https://wikipedia.org/wiki/Bernese_Alps\" title=\"Bernese Alps\">Bernese Alps</a> is ascended for the first time by <a href=\"https://wikipedia.org/wiki/<PERSON>_(mountaineer)\" title=\"<PERSON> (mountaineer)\"><PERSON></a> accompanied by <a href=\"https://wikipedia.org/wiki/Christian_<PERSON>\" title=\"<PERSON>\"><PERSON></a> and <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>.", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Eiger"}, {"title": "Bernese Alps", "link": "https://wikipedia.org/wiki/Bernese_Alps"}, {"title": "<PERSON> (mountaineer)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(mountaineer)"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1871", "text": "An explosion of guncotton occurs in Stowmarket, England, killing 28.", "html": "1871 - An <a href=\"https://wikipedia.org/wiki/Stowmarket_Guncotton_Explosion\" title=\"Stowmarket Guncotton Explosion\">explosion of guncotton occurs in Stowmarket</a>, England, killing 28.", "no_year_html": "An <a href=\"https://wikipedia.org/wiki/Stowmarket_Guncotton_Explosion\" title=\"Stowmarket Guncotton Explosion\">explosion of guncotton occurs in Stowmarket</a>, England, killing 28.", "links": [{"title": "Stowmarket Guncotton Explosion", "link": "https://wikipedia.org/wiki/Stowmarket_Guncotton_Explosion"}]}, {"year": "1898", "text": "Spanish-American War: American troops enter the city of Mayagüez, Puerto Rico.", "html": "1898 - <a href=\"https://wikipedia.org/wiki/Spanish%E2%80%93American_War\" title=\"Spanish-American War\">Spanish-American War</a>: American troops enter the city of <a href=\"https://wikipedia.org/wiki/Mayag%C3%<PERSON>ez,_Puerto_Rico\" title=\"Mayagüez, Puerto Rico\">Mayagüez, Puerto Rico</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Spanish%E2%80%93American_War\" title=\"Spanish-American War\">Spanish-American War</a>: American troops enter the city of <a href=\"https://wikipedia.org/wiki/Mayag%C3%<PERSON><PERSON>,_Puerto_Rico\" title=\"Mayagüez, Puerto Rico\">Mayagüez, Puerto Rico</a>.", "links": [{"title": "Spanish-American War", "link": "https://wikipedia.org/wiki/Spanish%E2%80%93American_War"}, {"title": "Mayagüez, Puerto Rico", "link": "https://wikipedia.org/wiki/Mayag%C3%<PERSON>ez,_Puerto_Rico"}]}, {"year": "1918", "text": "World War I: The Battle of Amiens ends.", "html": "1918 - <a href=\"https://wikipedia.org/wiki/World_War_I\" title=\"World War I\">World War I</a>: The <a href=\"https://wikipedia.org/wiki/Battle_of_Amiens_(1918)\" title=\"Battle of Amiens (1918)\">Battle of Amiens</a> ends.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/World_War_I\" title=\"World War I\">World War I</a>: The <a href=\"https://wikipedia.org/wiki/Battle_of_Amiens_(1918)\" title=\"Battle of Amiens (1918)\">Battle of Amiens</a> ends.", "links": [{"title": "World War I", "link": "https://wikipedia.org/wiki/World_War_I"}, {"title": "Battle of Amiens (1918)", "link": "https://wikipedia.org/wiki/Battle_of_Amiens_(1918)"}]}, {"year": "1919", "text": "Germany's Weimar Constitution is signed into law.", "html": "1919 - Germany's <a href=\"https://wikipedia.org/wiki/Weimar_Constitution\" title=\"Weimar Constitution\">Weimar Constitution</a> is signed into law.", "no_year_html": "Germany's <a href=\"https://wikipedia.org/wiki/Weimar_Constitution\" title=\"Weimar Constitution\">Weimar Constitution</a> is signed into law.", "links": [{"title": "Weimar Constitution", "link": "https://wikipedia.org/wiki/Weimar_Constitution"}]}, {"year": "1920", "text": "The 1920 Cork hunger strike begins which eventually results in the deaths of three Irish Republicans including the Lord Mayor of Cork <PERSON>.", "html": "1920 - The <a href=\"https://wikipedia.org/wiki/1920_Cork_hunger_strike\" title=\"1920 Cork hunger strike\">1920 Cork hunger strike</a> begins which eventually results in the deaths of three <a href=\"https://wikipedia.org/wiki/Irish_republicanism\" title=\"Irish republicanism\">Irish Republicans</a> including the Lord Mayor of Cork <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/1920_Cork_hunger_strike\" title=\"1920 Cork hunger strike\">1920 Cork hunger strike</a> begins which eventually results in the deaths of three <a href=\"https://wikipedia.org/wiki/Irish_republicanism\" title=\"Irish republicanism\">Irish Republicans</a> including the Lord Mayor of Cork <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>.", "links": [{"title": "1920 Cork hunger strike", "link": "https://wikipedia.org/wiki/1920_Cork_hunger_strike"}, {"title": "Irish republicanism", "link": "https://wikipedia.org/wiki/Irish_republicanism"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1920", "text": "The Latvian-Soviet Peace Treaty, which relinquished Russia's authority and pretenses to Latvia, is signed, ending the Latvian War of Independence.", "html": "1920 - The <a href=\"https://wikipedia.org/wiki/Latvian%E2%80%93Soviet_Peace_Treaty\" title=\"Latvian-Soviet Peace Treaty\">Latvian-Soviet Peace Treaty</a>, which relinquished <a href=\"https://wikipedia.org/wiki/Russia\" title=\"Russia\">Russia</a>'s authority and pretenses to <a href=\"https://wikipedia.org/wiki/Latvia\" title=\"Latvia\">Latvia</a>, is signed, ending the <a href=\"https://wikipedia.org/wiki/Latvian_War_of_Independence\" title=\"Latvian War of Independence\">Latvian War of Independence</a>.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Latvian%E2%80%93Soviet_Peace_Treaty\" title=\"Latvian-Soviet Peace Treaty\">Latvian-Soviet Peace Treaty</a>, which relinquished <a href=\"https://wikipedia.org/wiki/Russia\" title=\"Russia\">Russia</a>'s authority and pretenses to <a href=\"https://wikipedia.org/wiki/Latvia\" title=\"Latvia\">Latvia</a>, is signed, ending the <a href=\"https://wikipedia.org/wiki/Latvian_War_of_Independence\" title=\"Latvian War of Independence\">Latvian War of Independence</a>.", "links": [{"title": "Latvian-Soviet Peace Treaty", "link": "https://wikipedia.org/wiki/Latvian%E2%80%93Soviet_Peace_Treaty"}, {"title": "Russia", "link": "https://wikipedia.org/wiki/Russia"}, {"title": "Latvia", "link": "https://wikipedia.org/wiki/Latvia"}, {"title": "Latvian War of Independence", "link": "https://wikipedia.org/wiki/Latvian_War_of_Independence"}]}, {"year": "1929", "text": "<PERSON> becomes the first baseball player to hit 500 home runs in his career with a home run at League Park in Cleveland, Ohio.", "html": "1929 - <a href=\"https://wikipedia.org/wiki/<PERSON>_Ruth\" title=\"Babe Ruth\"><PERSON></a> becomes the first <a href=\"https://wikipedia.org/wiki/Baseball\" title=\"Baseball\">baseball</a> player to hit <a href=\"https://wikipedia.org/wiki/500_home_run_club\" title=\"500 home run club\">500</a> <a href=\"https://wikipedia.org/wiki/Home_run\" title=\"Home run\">home runs</a> in his career with a home run at <a href=\"https://wikipedia.org/wiki/League_Park\" title=\"League Park\">League Park</a> in <a href=\"https://wikipedia.org/wiki/Cleveland,_Ohio\" class=\"mw-redirect\" title=\"Cleveland, Ohio\">Cleveland, Ohio</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_Ruth\" title=\"Babe Ruth\"><PERSON></a> becomes the first <a href=\"https://wikipedia.org/wiki/Baseball\" title=\"Baseball\">baseball</a> player to hit <a href=\"https://wikipedia.org/wiki/500_home_run_club\" title=\"500 home run club\">500</a> <a href=\"https://wikipedia.org/wiki/Home_run\" title=\"Home run\">home runs</a> in his career with a home run at <a href=\"https://wikipedia.org/wiki/League_Park\" title=\"League Park\">League Park</a> in <a href=\"https://wikipedia.org/wiki/Cleveland,_Ohio\" class=\"mw-redirect\" title=\"Cleveland, Ohio\">Cleveland, Ohio</a>.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Baseball", "link": "https://wikipedia.org/wiki/Baseball"}, {"title": "500 home run club", "link": "https://wikipedia.org/wiki/500_home_run_club"}, {"title": "Home run", "link": "https://wikipedia.org/wiki/Home_run"}, {"title": "League Park", "link": "https://wikipedia.org/wiki/League_Park"}, {"title": "Cleveland, Ohio", "link": "https://wikipedia.org/wiki/Cleveland,_Ohio"}]}, {"year": "1934", "text": "The first civilian prisoners arrive at the Federal prison on Alcatraz Island.", "html": "1934 - The first civilian prisoners arrive at the <a href=\"https://wikipedia.org/wiki/Alcatraz_Federal_Penitentiary\" title=\"Alcatraz Federal Penitentiary\">Federal prison</a> on <a href=\"https://wikipedia.org/wiki/Alcatraz_Island\" title=\"Alcatraz Island\">Alcatraz Island</a>.", "no_year_html": "The first civilian prisoners arrive at the <a href=\"https://wikipedia.org/wiki/Alcatraz_Federal_Penitentiary\" title=\"Alcatraz Federal Penitentiary\">Federal prison</a> on <a href=\"https://wikipedia.org/wiki/Alcatraz_Island\" title=\"Alcatraz Island\">Alcatraz Island</a>.", "links": [{"title": "Alcatraz Federal Penitentiary", "link": "https://wikipedia.org/wiki/Alcatraz_Federal_Penitentiary"}, {"title": "Alcatraz Island", "link": "https://wikipedia.org/wiki/Alcatraz_Island"}]}, {"year": "1942", "text": "Actress <PERSON><PERSON> and composer <PERSON> receive a patent for a Frequency-hopping spread spectrum communication system that later became the basis for modern technologies in wireless telephones, two-way radio communications, and Wi-Fi.", "html": "1942 - Actress <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a> and composer <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> receive a patent for a <a href=\"https://wikipedia.org/wiki/Frequency-hopping_spread_spectrum\" title=\"Frequency-hopping spread spectrum\">Frequency-hopping spread spectrum</a> communication system that later became the basis for modern technologies in wireless telephones, two-way radio communications, and <a href=\"https://wikipedia.org/wiki/Wi-Fi\" title=\"Wi-Fi\">Wi-Fi</a>.", "no_year_html": "Actress <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a> and composer <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> receive a patent for a <a href=\"https://wikipedia.org/wiki/Frequency-hopping_spread_spectrum\" title=\"Frequency-hopping spread spectrum\">Frequency-hopping spread spectrum</a> communication system that later became the basis for modern technologies in wireless telephones, two-way radio communications, and <a href=\"https://wikipedia.org/wiki/Wi-Fi\" title=\"Wi-Fi\">Wi-Fi</a>.", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/He<PERSON>_<PERSON>"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Frequency-hopping spread spectrum", "link": "https://wikipedia.org/wiki/Frequency-hopping_spread_spectrum"}, {"title": "Wi-Fi", "link": "https://wikipedia.org/wiki/Wi-Fi"}]}, {"year": "1945", "text": "Poles in Kraków engage in a pogrom against Jews in the city, killing one and wounding five.", "html": "1945 - Poles in <a href=\"https://wikipedia.org/wiki/Krak%C3%B3w\" title=\"Kraków\">Kraków</a> engage in <a href=\"https://wikipedia.org/wiki/Krak%C3%B3w_pogrom\" title=\"Kraków pogrom\">a pogrom</a> against Jews in the city, killing one and wounding five.", "no_year_html": "Poles in <a href=\"https://wikipedia.org/wiki/Krak%C3%B3w\" title=\"Kraków\">Kraków</a> engage in <a href=\"https://wikipedia.org/wiki/Krak%C3%B3w_pogrom\" title=\"Kraków pogrom\">a pogrom</a> against Jews in the city, killing one and wounding five.", "links": [{"title": "Kraków", "link": "https://wikipedia.org/wiki/Krak%C3%B3w"}, {"title": "Kraków pogrom", "link": "https://wikipedia.org/wiki/Krak%C3%B3w_pogrom"}]}, {"year": "1952", "text": "<PERSON> is proclaimed King of Jordan.", "html": "1952 - <a href=\"https://wikipedia.org/wiki/Hussein_of_Jordan\" title=\"<PERSON> of Jordan\"><PERSON> bin <PERSON></a> is proclaimed King of <a href=\"https://wikipedia.org/wiki/Jordan\" title=\"Jordan\">Jordan</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Hussein_of_Jordan\" title=\"<PERSON> of Jordan\"><PERSON> bin <PERSON></a> is proclaimed King of <a href=\"https://wikipedia.org/wiki/Jordan\" title=\"Jordan\">Jordan</a>.", "links": [{"title": "<PERSON> of Jordan", "link": "https://wikipedia.org/wiki/Hussein_of_Jordan"}, {"title": "Jordan", "link": "https://wikipedia.org/wiki/Jordan"}]}, {"year": "1959", "text": "Sheremetyevo International Airport, the second-largest airport in Russia, opens.", "html": "1959 - <a href=\"https://wikipedia.org/wiki/Sheremetyevo_International_Airport\" title=\"Sheremetyevo International Airport\">Sheremetyevo International Airport</a>, the second-largest airport in Russia, opens.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Sheremetyevo_International_Airport\" title=\"Sheremetyevo International Airport\">Sheremetyevo International Airport</a>, the second-largest airport in Russia, opens.", "links": [{"title": "Sheremetyevo International Airport", "link": "https://wikipedia.org/wiki/Sheremetyevo_International_Airport"}]}, {"year": "1960", "text": "Chad declares independence from France.", "html": "1960 - <a href=\"https://wikipedia.org/wiki/Chad\" title=\"Chad\">Chad</a> declares independence from France.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Chad\" title=\"Chad\">Chad</a> declares independence from France.", "links": [{"title": "Chad", "link": "https://wikipedia.org/wiki/Chad"}]}, {"year": "1961", "text": "The former Portuguese territories in India of Dadra and Nagar Haveli are merged to create the Union Territory Dadra and Nagar Haveli.", "html": "1961 - The former Portuguese territories in <a href=\"https://wikipedia.org/wiki/India\" title=\"India\">India</a> of <a href=\"https://wikipedia.org/wiki/Dadra_DNH\" class=\"mw-redirect\" title=\"Dadra DNH\">Dadra</a> and <a href=\"https://wikipedia.org/wiki/Nagar_Haveli_DNH\" class=\"mw-redirect\" title=\"Nagar Haveli DNH\">Nagar Haveli</a> are merged to create the <a href=\"https://wikipedia.org/wiki/Union_Territory\" class=\"mw-redirect\" title=\"Union Territory\">Union Territory</a> <a href=\"https://wikipedia.org/wiki/Dadra_and_Nagar_Haveli\" class=\"mw-redirect\" title=\"Dadra and Nagar Haveli\">Dadra and Nagar Haveli</a>.", "no_year_html": "The former Portuguese territories in <a href=\"https://wikipedia.org/wiki/India\" title=\"India\">India</a> of <a href=\"https://wikipedia.org/wiki/Dadra_DNH\" class=\"mw-redirect\" title=\"Dadra DNH\">Dadra</a> and <a href=\"https://wikipedia.org/wiki/Nagar_Haveli_DNH\" class=\"mw-redirect\" title=\"Nagar Haveli DNH\">Nagar Haveli</a> are merged to create the <a href=\"https://wikipedia.org/wiki/Union_Territory\" class=\"mw-redirect\" title=\"Union Territory\">Union Territory</a> <a href=\"https://wikipedia.org/wiki/Dadra_and_Nagar_Haveli\" class=\"mw-redirect\" title=\"Dadra and Nagar Haveli\">Dadra and Nagar Haveli</a>.", "links": [{"title": "India", "link": "https://wikipedia.org/wiki/India"}, {"title": "Dadra DNH", "link": "https://wikipedia.org/wiki/Dadra_DNH"}, {"title": "Nagar Haveli DNH", "link": "https://wikipedia.org/wiki/Nagar_Haveli_DNH"}, {"title": "Union Territory", "link": "https://wikipedia.org/wiki/Union_Territory"}, {"title": "Dadra and Nagar Haveli", "link": "https://wikipedia.org/wiki/Dadra_and_Nagar_Haveli"}]}, {"year": "1962", "text": "Vostok 3 launches from the Baikonur Cosmodrome and cosmonaut <PERSON><PERSON> becomes the first person to float in microgravity.", "html": "1962 - <a href=\"https://wikipedia.org/wiki/Vostok_3\" class=\"mw-redirect\" title=\"Vostok 3\">Vostok 3</a> launches from the <a href=\"https://wikipedia.org/wiki/Baikonur_Cosmodrome\" title=\"Baikonur Cosmodrome\">Baikonur Cosmodrome</a> and cosmonaut <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a> becomes the first person to float in <a href=\"https://wikipedia.org/wiki/Micro-g_environment\" class=\"mw-redirect\" title=\"Micro-g environment\">microgravity</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Vostok_3\" class=\"mw-redirect\" title=\"Vostok 3\">Vostok 3</a> launches from the <a href=\"https://wikipedia.org/wiki/Baikonur_Cosmodrome\" title=\"Baikonur Cosmodrome\">Baikonur Cosmodrome</a> and cosmonaut <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a> becomes the first person to float in <a href=\"https://wikipedia.org/wiki/Micro-g_environment\" class=\"mw-redirect\" title=\"Micro-g environment\">microgravity</a>.", "links": [{"title": "Vostok 3", "link": "https://wikipedia.org/wiki/Vostok_3"}, {"title": "Baikonur Cosmodrome", "link": "https://wikipedia.org/wiki/Baikonur_Cosmodrome"}, {"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}, {"title": "Micro-g environment", "link": "https://wikipedia.org/wiki/Micro-g_environment"}]}, {"year": "1965", "text": "Race riots (the Watts Riots) begin in the Watts area of Los Angeles, California.", "html": "1965 - Race riots (the <a href=\"https://wikipedia.org/wiki/Watts_Riots\" class=\"mw-redirect\" title=\"Watts Riots\">Watts Riots</a>) begin in the <a href=\"https://wikipedia.org/wiki/<PERSON>,_Los_Angeles\" title=\"Watts, Los Angeles\">Watts</a> area of <a href=\"https://wikipedia.org/wiki/Los_Angeles\" title=\"Los Angeles\">Los Angeles, California</a>.", "no_year_html": "Race riots (the <a href=\"https://wikipedia.org/wiki/Watts_Riots\" class=\"mw-redirect\" title=\"Watts Riots\">Watts Riots</a>) begin in the <a href=\"https://wikipedia.org/wiki/<PERSON>,_Los_Angeles\" title=\"Watts, Los Angeles\">Watts</a> area of <a href=\"https://wikipedia.org/wiki/Los_Angeles\" title=\"Los Angeles\">Los Angeles, California</a>.", "links": [{"title": "Watts Riots", "link": "https://wikipedia.org/wiki/Watts_Riots"}, {"title": "Watts, Los Angeles", "link": "https://wikipedia.org/wiki/Watts,_Los_Angeles"}, {"title": "Los Angeles", "link": "https://wikipedia.org/wiki/Los_Angeles"}]}, {"year": "1969", "text": "The Apollo 11 astronauts are released from a three-week quarantine following their liftoff from the Moon.", "html": "1969 - The <a href=\"https://wikipedia.org/wiki/Apollo_11\" title=\"Apollo 11\">Apollo 11</a> astronauts are released from a three-week <a href=\"https://wikipedia.org/wiki/Quarantine\" title=\"Quarantine\">quarantine</a> following their liftoff from the Moon.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Apollo_11\" title=\"Apollo 11\">Apollo 11</a> astronauts are released from a three-week <a href=\"https://wikipedia.org/wiki/Quarantine\" title=\"Quarantine\">quarantine</a> following their liftoff from the Moon.", "links": [{"title": "Apollo 11", "link": "https://wikipedia.org/wiki/Apollo_11"}, {"title": "Quarantine", "link": "https://wikipedia.org/wiki/Quarantine"}]}, {"year": "1972", "text": "Vietnam War: The last United States ground combat unit leaves South Vietnam.", "html": "1972 - <a href=\"https://wikipedia.org/wiki/Vietnam_War\" title=\"Vietnam War\">Vietnam War</a>: The last United States ground combat unit leaves <a href=\"https://wikipedia.org/wiki/South_Vietnam\" title=\"South Vietnam\">South Vietnam</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Vietnam_War\" title=\"Vietnam War\">Vietnam War</a>: The last United States ground combat unit leaves <a href=\"https://wikipedia.org/wiki/South_Vietnam\" title=\"South Vietnam\">South Vietnam</a>.", "links": [{"title": "Vietnam War", "link": "https://wikipedia.org/wiki/Vietnam_War"}, {"title": "South Vietnam", "link": "https://wikipedia.org/wiki/South_Vietnam"}]}, {"year": "1973", "text": "At the 1520 Sedgwick Avenue apartment building in The Bronx, New York, DJ <PERSON><PERSON> hosts a house party widely considered to mark the birthplace of hip hop culture and music.  DJ <PERSON><PERSON> demonstrates a new technique of beat juggling and <PERSON> performs a new style of vocal performance called rapping.", "html": "1973 - At the <a href=\"https://wikipedia.org/wiki/1520_Sedgwick_Avenue\" title=\"1520 Sedgwick Avenue\">1520 Sedgwick Avenue</a> apartment building in <a href=\"https://wikipedia.org/wiki/The_Bronx\" title=\"The Bronx\">The Bronx</a>, New York, <a href=\"https://wikipedia.org/wiki/DJ_<PERSON><PERSON>_<PERSON>\" title=\"DJ <PERSON><PERSON>\">DJ <PERSON><PERSON></a> hosts a house party widely considered to mark the birthplace of <a href=\"https://wikipedia.org/wiki/Hip_hop_music\" class=\"mw-redirect\" title=\"Hip hop music\">hip hop</a> culture and music. DJ <PERSON><PERSON> demonstrates a new technique of <a href=\"https://wikipedia.org/wiki/Beat_juggling\" title=\"Beat juggling\">beat juggling</a> and <a href=\"https://wikipedia.org/wiki/Coke_La_Rock\" title=\"Coke La Rock\">Coke La Rock</a> performs a new style of vocal performance called <a href=\"https://wikipedia.org/wiki/Rapping\" title=\"Rapping\">rapping</a>.", "no_year_html": "At the <a href=\"https://wikipedia.org/wiki/1520_Sedgwick_Avenue\" title=\"1520 Sedgwick Avenue\">1520 Sedgwick Avenue</a> apartment building in <a href=\"https://wikipedia.org/wiki/The_Bronx\" title=\"The Bronx\">The Bronx</a>, New York, <a href=\"https://wikipedia.org/wiki/DJ_<PERSON><PERSON>_<PERSON>\" title=\"DJ <PERSON><PERSON>\">DJ <PERSON><PERSON></a> hosts a house party widely considered to mark the birthplace of <a href=\"https://wikipedia.org/wiki/Hip_hop_music\" class=\"mw-redirect\" title=\"Hip hop music\">hip hop</a> culture and music. DJ <PERSON><PERSON> demonstrates a new technique of <a href=\"https://wikipedia.org/wiki/Beat_juggling\" title=\"Beat juggling\">beat juggling</a> and <a href=\"https://wikipedia.org/wiki/Coke_La_Rock\" title=\"Coke La Rock\">Coke La Rock</a> performs a new style of vocal performance called <a href=\"https://wikipedia.org/wiki/Rapping\" title=\"Rapping\">rapping</a>.", "links": [{"title": "1520 Sedgwick Avenue", "link": "https://wikipedia.org/wiki/1520_Sedgwick_Avenue"}, {"title": "The Bronx", "link": "https://wikipedia.org/wiki/The_Bronx"}, {"title": "DJ <PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>"}, {"title": "Hip hop music", "link": "https://wikipedia.org/wiki/Hip_hop_music"}, {"title": "Beat juggling", "link": "https://wikipedia.org/wiki/Beat_juggling"}, {"title": "Coke La Rock", "link": "https://wikipedia.org/wiki/Coke_La_Rock"}, {"title": "Rapping", "link": "https://wikipedia.org/wiki/Rapping"}]}, {"year": "1975", "text": "East Timor: Governor <PERSON><PERSON><PERSON> of Portuguese Timor abandons the capital Dili, following a coup by the Timorese Democratic Union (UDT) and the outbreak of civil war between UDT and Fretilin.", "html": "1975 - <a href=\"https://wikipedia.org/wiki/East_Timor\" class=\"mw-redirect\" title=\"East Timor\">East Timor</a>: Governor <a href=\"https://wikipedia.org/wiki/M%C3%A1rio_Lemos_Pires\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a> of <a href=\"https://wikipedia.org/wiki/Portuguese_Timor\" title=\"Portuguese Timor\">Portuguese Timor</a> abandons the capital <a href=\"https://wikipedia.org/wiki/Dili\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, following a coup by the <a href=\"https://wikipedia.org/wiki/Timorese_Democratic_Union\" title=\"Timorese Democratic Union\">Timorese Democratic Union</a> (UDT) and the outbreak of civil war between UDT and <a href=\"https://wikipedia.org/wiki/Fretilin\" title=\"Fretilin\">Fretilin</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/East_Timor\" class=\"mw-redirect\" title=\"East Timor\">East Timor</a>: Governor <a href=\"https://wikipedia.org/wiki/M%C3%A1rio_Lemos_Pires\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a> of <a href=\"https://wikipedia.org/wiki/Portuguese_Timor\" title=\"Portuguese Timor\">Portuguese Timor</a> abandons the capital <a href=\"https://wikipedia.org/wiki/Dili\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, following a coup by the <a href=\"https://wikipedia.org/wiki/Timorese_Democratic_Union\" title=\"Timorese Democratic Union\">Timorese Democratic Union</a> (UDT) and the outbreak of civil war between UDT and <a href=\"https://wikipedia.org/wiki/Fretilin\" title=\"Fretilin\">Fretilin</a>.", "links": [{"title": "East Timor", "link": "https://wikipedia.org/wiki/East_Timor"}, {"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/M%C3%A1rio_Lemos_Pires"}, {"title": "Portuguese Timor", "link": "https://wikipedia.org/wiki/Portuguese_Timor"}, {"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Dili"}, {"title": "Timorese Democratic Union", "link": "https://wikipedia.org/wiki/Timorese_Democratic_Union"}, {"title": "Fretilin", "link": "https://wikipedia.org/wiki/Fretilin"}]}, {"year": "1979", "text": "Two Aeroflot Tupolev Tu-134s collide over the Ukrainian city of Dniprodzerzhynsk and crash, killing all 178 aboard both airliners.", "html": "1979 - Two <a href=\"https://wikipedia.org/wiki/Aeroflot\" title=\"Aeroflot\">Aeroflot</a> <a href=\"https://wikipedia.org/wiki/Tupolev_Tu-134\" title=\"Tupolev Tu-134\">Tupolev Tu-134s</a> <a href=\"https://wikipedia.org/wiki/1979_Dniprodzerzhynsk_mid-air_collision\" title=\"1979 Dniprodzerzhynsk mid-air collision\">collide</a> over the <a href=\"https://wikipedia.org/wiki/Ukraine\" title=\"Ukraine\">Ukrainian</a> city of <a href=\"https://wikipedia.org/wiki/Kamianske\" title=\"Kamianske\">Dniprodzerzhynsk</a> and crash, killing all 178 aboard both airliners.", "no_year_html": "Two <a href=\"https://wikipedia.org/wiki/Aeroflot\" title=\"Aeroflot\">Aeroflot</a> <a href=\"https://wikipedia.org/wiki/Tupolev_Tu-134\" title=\"Tupolev Tu-134\">Tupolev Tu-134s</a> <a href=\"https://wikipedia.org/wiki/1979_Dniprodzerzhynsk_mid-air_collision\" title=\"1979 Dniprodzerzhynsk mid-air collision\">collide</a> over the <a href=\"https://wikipedia.org/wiki/Ukraine\" title=\"Ukraine\">Ukrainian</a> city of <a href=\"https://wikipedia.org/wiki/Kamianske\" title=\"Kamianske\">Dniprodzerzhynsk</a> and crash, killing all 178 aboard both airliners.", "links": [{"title": "Aeroflot", "link": "https://wikipedia.org/wiki/Aeroflot"}, {"title": "Tupolev Tu-134", "link": "https://wikipedia.org/wiki/Tupolev_Tu-134"}, {"title": "1979 Dniprodzerzhynsk mid-air collision", "link": "https://wikipedia.org/wiki/1979_Dniprodzerzhynsk_mid-air_collision"}, {"title": "Ukraine", "link": "https://wikipedia.org/wiki/Ukraine"}, {"title": "Kamianske", "link": "https://wikipedia.org/wiki/Kamianske"}]}, {"year": "1982", "text": "A bomb explodes on Pan Am Flight 830, en route from Tokyo, Japan to Honolulu, Hawaii, killing one passenger and injuring 15 others.", "html": "1982 - A bomb explodes on <a href=\"https://wikipedia.org/wiki/Pan_Am_Flight_830\" title=\"Pan Am Flight 830\">Pan Am Flight 830</a>, en route from <a href=\"https://wikipedia.org/wiki/Tokyo\" title=\"Tokyo\">Tokyo, Japan</a> to <a href=\"https://wikipedia.org/wiki/Honolulu\" title=\"Honolulu\">Honolulu, Hawaii</a>, killing one passenger and injuring 15 others.", "no_year_html": "A bomb explodes on <a href=\"https://wikipedia.org/wiki/Pan_Am_Flight_830\" title=\"Pan Am Flight 830\">Pan Am Flight 830</a>, en route from <a href=\"https://wikipedia.org/wiki/Tokyo\" title=\"Tokyo\">Tokyo, Japan</a> to <a href=\"https://wikipedia.org/wiki/Honolulu\" title=\"Honolulu\">Honolulu, Hawaii</a>, killing one passenger and injuring 15 others.", "links": [{"title": "Pan Am Flight 830", "link": "https://wikipedia.org/wiki/Pan_Am_Flight_830"}, {"title": "Tokyo", "link": "https://wikipedia.org/wiki/Tokyo"}, {"title": "Honolulu", "link": "https://wikipedia.org/wiki/Honolulu"}]}, {"year": "1984", "text": "\"We begin bombing in five minutes\": United States President <PERSON>, while running for re-election, jokes while preparing to make his weekly Saturday address on National Public Radio.", "html": "1984 - \"<a href=\"https://wikipedia.org/wiki/We_begin_bombing_in_five_minutes\" title=\"We begin bombing in five minutes\">We begin bombing in five minutes</a>\": <a href=\"https://wikipedia.org/wiki/President_of_the_United_States\" title=\"President of the United States\">United States President</a> <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, while running for re-election, jokes while preparing to make his weekly Saturday address on <a href=\"https://wikipedia.org/wiki/NPR\" title=\"NPR\">National Public Radio</a>.", "no_year_html": "\"<a href=\"https://wikipedia.org/wiki/We_begin_bombing_in_five_minutes\" title=\"We begin bombing in five minutes\">We begin bombing in five minutes</a>\": <a href=\"https://wikipedia.org/wiki/President_of_the_United_States\" title=\"President of the United States\">United States President</a> <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, while running for re-election, jokes while preparing to make his weekly Saturday address on <a href=\"https://wikipedia.org/wiki/NPR\" title=\"NPR\">National Public Radio</a>.", "links": [{"title": "We begin bombing in five minutes", "link": "https://wikipedia.org/wiki/We_begin_bombing_in_five_minutes"}, {"title": "President of the United States", "link": "https://wikipedia.org/wiki/President_of_the_United_States"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "NPR", "link": "https://wikipedia.org/wiki/NPR"}]}, {"year": "1988", "text": "A meeting between <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, and leaders of Egyptian Islamic Jihad in Afghanistan culminates in the formation of Al-Qaeda.", "html": "1988 - A meeting between <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_bin_<PERSON>den\" title=\"<PERSON><PERSON><PERSON> bin <PERSON>\"><PERSON><PERSON><PERSON> bin <PERSON></a>, <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, and leaders of <a href=\"https://wikipedia.org/wiki/Egyptian_Islamic_Jihad\" title=\"Egyptian Islamic Jihad\">Egyptian Islamic Jihad</a> in <a href=\"https://wikipedia.org/wiki/Afghanistan\" title=\"Afghanistan\">Afghanistan</a> culminates in the formation of <a href=\"https://wikipedia.org/wiki/Al-Qaeda\" title=\"Al-Qaeda\">Al-Qaeda</a>.", "no_year_html": "A meeting between <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_bin_<PERSON>den\" title=\"<PERSON><PERSON><PERSON> bin <PERSON>\"><PERSON><PERSON><PERSON></a>, <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, and leaders of <a href=\"https://wikipedia.org/wiki/Egyptian_Islamic_Jihad\" title=\"Egyptian Islamic Jihad\">Egyptian Islamic Jihad</a> in <a href=\"https://wikipedia.org/wiki/Afghanistan\" title=\"Afghanistan\">Afghanistan</a> culminates in the formation of <a href=\"https://wikipedia.org/wiki/Al-Qaeda\" title=\"Al-Qaeda\">Al-Qaeda</a>.", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>"}, {"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>den"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "Egyptian Islamic Jihad", "link": "https://wikipedia.org/wiki/Egyptian_Islamic_Jihad"}, {"title": "Afghanistan", "link": "https://wikipedia.org/wiki/Afghanistan"}, {"title": "Al-Qaeda", "link": "https://wikipedia.org/wiki/Al-Qaeda"}]}, {"year": "1991", "text": "Nickelodeon's first line of “Nicktoons” (Doug, Rugrats & Ren & Stimpy) premiere on the channel.", "html": "1991 - <a href=\"https://wikipedia.org/wiki/Nickelodeon\" title=\"Nickelodeon\">Nickelodeon</a>'s first line of “<a href=\"https://wikipedia.org/wiki/Nicktoons\" title=\"Nicktoons\">Nicktoons</a>” (<a href=\"https://wikipedia.org/wiki/<PERSON>_(TV_series)\" title=\"<PERSON> (TV series)\"><PERSON></a>, <a href=\"https://wikipedia.org/wiki/Rugrats\" title=\"Rugrats\">Rugrats</a> &amp; <a href=\"https://wikipedia.org/wiki/The_Ren_%26_Stimpy_Show\" title=\"The Ren &amp; Stimpy Show\">Ren &amp; Stimpy</a>) premiere on the channel.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Nickelodeon\" title=\"Nickelodeon\">Nickelodeon</a>'s first line of “<a href=\"https://wikipedia.org/wiki/Nicktoons\" title=\"Nicktoons\">Nicktoons</a>” (<a href=\"https://wikipedia.org/wiki/<PERSON>_(TV_series)\" title=\"<PERSON> (TV series)\"><PERSON></a>, <a href=\"https://wikipedia.org/wiki/Rugrats\" title=\"Rugrats\">Rugrats</a> &amp; <a href=\"https://wikipedia.org/wiki/The_Ren_%26_Stimpy_Show\" title=\"The Ren &amp; Stimpy Show\">Ren &amp; Stimpy</a>) premiere on the channel.", "links": [{"title": "Nickelodeon", "link": "https://wikipedia.org/wiki/Nickelodeon"}, {"title": "Nicktoons", "link": "https://wikipedia.org/wiki/Nicktoons"}, {"title": "<PERSON> (TV series)", "link": "https://wikipedia.org/wiki/<PERSON>_(TV_series)"}, {"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Rugrats"}, {"title": "The Ren & Stimpy Show", "link": "https://wikipedia.org/wiki/The_Ren_%26_Stimpy_Show"}]}, {"year": "1992", "text": "The Mall of America in Bloomington, Minnesota opens. At the time the largest shopping mall in the United States.", "html": "1992 - The <a href=\"https://wikipedia.org/wiki/Mall_of_America\" title=\"Mall of America\">Mall of America</a> in Bloomington, Minnesota opens. At the time the largest shopping mall in the United States.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Mall_of_America\" title=\"Mall of America\">Mall of America</a> in Bloomington, Minnesota opens. At the time the largest shopping mall in the United States.", "links": [{"title": "Mall of America", "link": "https://wikipedia.org/wiki/Mall_of_America"}]}, {"year": "2000", "text": "An air rage incident occurs on board Southwest Airlines Flight 1763 when 19-year-old <PERSON> attempts to storm the cockpit, but he is subdued by other passengers and dies from his injuries.", "html": "2000 - An <a href=\"https://wikipedia.org/wiki/Air_rage\" title=\"Air rage\">air rage</a> incident occurs on board <a href=\"https://wikipedia.org/wiki/Southwest_Airlines_Flight_1763\" title=\"Southwest Airlines Flight 1763\">Southwest Airlines Flight 1763</a> when 19-year-old <PERSON> attempts to storm the cockpit, but he is subdued by other passengers and dies from his injuries.", "no_year_html": "An <a href=\"https://wikipedia.org/wiki/Air_rage\" title=\"Air rage\">air rage</a> incident occurs on board <a href=\"https://wikipedia.org/wiki/Southwest_Airlines_Flight_1763\" title=\"Southwest Airlines Flight 1763\">Southwest Airlines Flight 1763</a> when 19-year-old <PERSON> attempts to storm the cockpit, but he is subdued by other passengers and dies from his injuries.", "links": [{"title": "Air rage", "link": "https://wikipedia.org/wiki/Air_rage"}, {"title": "Southwest Airlines Flight 1763", "link": "https://wikipedia.org/wiki/Southwest_Airlines_Flight_1763"}]}, {"year": "2003", "text": "NATO takes over command of the peacekeeping force in Afghanistan, marking its first major operation outside Europe in its 54-year-history.", "html": "2003 - <a href=\"https://wikipedia.org/wiki/NATO\" title=\"NATO\">NATO</a> takes over command of the <a href=\"https://wikipedia.org/wiki/International_Security_Assistance_Force\" title=\"International Security Assistance Force\">peacekeeping force</a> in <a href=\"https://wikipedia.org/wiki/Afghanistan\" title=\"Afghanistan\">Afghanistan</a>, marking its first major operation outside <a href=\"https://wikipedia.org/wiki/Europe\" title=\"Europe\">Europe</a> in its 54-year-history.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/NATO\" title=\"NATO\">NATO</a> takes over command of the <a href=\"https://wikipedia.org/wiki/International_Security_Assistance_Force\" title=\"International Security Assistance Force\">peacekeeping force</a> in <a href=\"https://wikipedia.org/wiki/Afghanistan\" title=\"Afghanistan\">Afghanistan</a>, marking its first major operation outside <a href=\"https://wikipedia.org/wiki/Europe\" title=\"Europe\">Europe</a> in its 54-year-history.", "links": [{"title": "NATO", "link": "https://wikipedia.org/wiki/NATO"}, {"title": "International Security Assistance Force", "link": "https://wikipedia.org/wiki/International_Security_Assistance_Force"}, {"title": "Afghanistan", "link": "https://wikipedia.org/wiki/Afghanistan"}, {"title": "Europe", "link": "https://wikipedia.org/wiki/Europe"}]}, {"year": "2003", "text": "<PERSON><PERSON><PERSON> leader <PERSON><PERSON><PERSON>, better known as <PERSON><PERSON><PERSON>, is arrested in Bangkok, Thailand.", "html": "2003 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a> leader <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, better known as <PERSON><PERSON><PERSON>, is arrested in <a href=\"https://wikipedia.org/wiki/Bangkok\" title=\"Bangkok\">Bangkok</a>, <a href=\"https://wikipedia.org/wiki/Thailand\" title=\"Thailand\">Thailand</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a> leader <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, better known as <PERSON><PERSON><PERSON>, is arrested in <a href=\"https://wikipedia.org/wiki/Bangkok\" title=\"Bangkok\">Bangkok</a>, <a href=\"https://wikipedia.org/wiki/Thailand\" title=\"Thailand\">Thailand</a>.", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_Islamiyah"}, {"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}, {"title": "Bangkok", "link": "https://wikipedia.org/wiki/Bangkok"}, {"title": "Thailand", "link": "https://wikipedia.org/wiki/Thailand"}]}, {"year": "2006", "text": "The oil tanker MT Solar 1 sinks off the coast of Guimaras and Negros Islands in the Philippines, causing the country's worst oil spill.", "html": "2006 - The <a href=\"https://wikipedia.org/wiki/Oil_tanker\" title=\"Oil tanker\">oil tanker</a> MT <i>Solar 1</i> sinks off the coast of <a href=\"https://wikipedia.org/wiki/Guimaras\" title=\"Guimaras\">Guimaras</a> and <a href=\"https://wikipedia.org/wiki/Negros_(island)\" class=\"mw-redirect\" title=\"Negros (island)\">Negros</a> Islands in the <a href=\"https://wikipedia.org/wiki/Philippines\" title=\"Philippines\">Philippines</a>, causing the country's <a href=\"https://wikipedia.org/wiki/Guimaras_oil_spill\" title=\"Guimaras oil spill\">worst oil spill</a>.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Oil_tanker\" title=\"Oil tanker\">oil tanker</a> MT <i>Solar 1</i> sinks off the coast of <a href=\"https://wikipedia.org/wiki/Guimaras\" title=\"Guimaras\">Guimaras</a> and <a href=\"https://wikipedia.org/wiki/Negros_(island)\" class=\"mw-redirect\" title=\"Negros (island)\">Negros</a> Islands in the <a href=\"https://wikipedia.org/wiki/Philippines\" title=\"Philippines\">Philippines</a>, causing the country's <a href=\"https://wikipedia.org/wiki/Guimaras_oil_spill\" title=\"Guimaras oil spill\">worst oil spill</a>.", "links": [{"title": "Oil tanker", "link": "https://wikipedia.org/wiki/Oil_tanker"}, {"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>s"}, {"title": "Negros (island)", "link": "https://wikipedia.org/wiki/Negros_(island)"}, {"title": "Philippines", "link": "https://wikipedia.org/wiki/Philippines"}, {"title": "Guimaras oil spill", "link": "https://wikipedia.org/wiki/G<PERSON>maras_oil_spill"}]}, {"year": "2012", "text": "At least 306 people are killed and 3,000 others injured in a pair of earthquakes near Tabriz, Iran.", "html": "2012 - At least 306 people are killed and 3,000 others injured in <a href=\"https://wikipedia.org/wiki/2012_East_Azerbaijan_earthquakes\" title=\"2012 East Azerbaijan earthquakes\">a pair of earthquakes</a> near <a href=\"https://wikipedia.org/wiki/Tabriz\" title=\"Tabriz\">Tabriz, Iran</a>.", "no_year_html": "At least 306 people are killed and 3,000 others injured in <a href=\"https://wikipedia.org/wiki/2012_East_Azerbaijan_earthquakes\" title=\"2012 East Azerbaijan earthquakes\">a pair of earthquakes</a> near <a href=\"https://wikipedia.org/wiki/Tabriz\" title=\"Tabriz\">Tabriz, Iran</a>.", "links": [{"title": "2012 East Azerbaijan earthquakes", "link": "https://wikipedia.org/wiki/2012_East_Azerbaijan_earthquakes"}, {"title": "Tabriz", "link": "https://wikipedia.org/wiki/Tabriz"}]}, {"year": "2017", "text": "At least 41 people are killed and another 179 injured after two passenger trains collide in Alexandria, Egypt.", "html": "2017 - At least 41 people are killed and another 179 injured after two passenger trains <a href=\"https://wikipedia.org/wiki/Alexandria_train_collision\" title=\"Alexandria train collision\">collide</a> in <a href=\"https://wikipedia.org/wiki/Alexandria\" title=\"Alexandria\">Alexandria</a>, <a href=\"https://wikipedia.org/wiki/Egypt\" title=\"Egypt\">Egypt</a>.", "no_year_html": "At least 41 people are killed and another 179 injured after two passenger trains <a href=\"https://wikipedia.org/wiki/Alexandria_train_collision\" title=\"Alexandria train collision\">collide</a> in <a href=\"https://wikipedia.org/wiki/Alexandria\" title=\"Alexandria\">Alexandria</a>, <a href=\"https://wikipedia.org/wiki/Egypt\" title=\"Egypt\">Egypt</a>.", "links": [{"title": "Alexandria train collision", "link": "https://wikipedia.org/wiki/Alexandria_train_collision"}, {"title": "Alexandria", "link": "https://wikipedia.org/wiki/Alexandria"}, {"title": "Egypt", "link": "https://wikipedia.org/wiki/Egypt"}]}, {"year": "2023", "text": "Luna 25 launches from the Vostochny Cosmodrome.", "html": "2023 - <a href=\"https://wikipedia.org/wiki/Luna_25\" title=\"Luna 25\">Luna 25</a> launches from the <a href=\"https://wikipedia.org/wiki/Vostochny_Cosmodrome\" title=\"Vostochny Cosmodrome\">Vostochny Cosmodrome</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Luna_25\" title=\"Luna 25\">Luna 25</a> launches from the <a href=\"https://wikipedia.org/wiki/Vostochny_Cosmodrome\" title=\"Vostochny Cosmodrome\">Vostochny Cosmodrome</a>.", "links": [{"title": "Luna 25", "link": "https://wikipedia.org/wiki/Luna_25"}, {"title": "Vostochny Cosmodrome", "link": "https://wikipedia.org/wiki/Vostochny_Cosmodrome"}]}], "Births": [{"year": "1086", "text": "<PERSON>, Holy Roman Emperor (d. 1125)", "html": "1086 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Holy_Roman_Emperor\" title=\"<PERSON> V, Holy Roman Emperor\"><PERSON>, Holy Roman Emperor</a> (d. 1125)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Holy_Roman_Emperor\" title=\"<PERSON> V, Holy Roman Emperor\"><PERSON>, Holy Roman Emperor</a> (d. 1125)", "links": [{"title": "<PERSON>, Holy Roman Emperor", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Holy_Roman_Emperor"}]}, {"year": "1384", "text": "<PERSON><PERSON><PERSON> of Aragon (d. 1442)", "html": "1384 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_of_Aragon\" title=\"<PERSON><PERSON><PERSON> of Aragon\"><PERSON><PERSON><PERSON> of Aragon</a> (d. 1442)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_of_Aragon\" title=\"<PERSON><PERSON><PERSON> of Aragon\"><PERSON><PERSON><PERSON> of Aragon</a> (d. 1442)", "links": [{"title": "<PERSON><PERSON><PERSON> of Aragon", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_of_Aragon"}]}, {"year": "1472", "text": "<PERSON><PERSON>, Catholic cardinal (d. 1537)", "html": "1472 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>%C3%B6nberg\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Catholic cardinal (d. 1537)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>h%C3%B6nberg\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Catholic cardinal (d. 1537)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>%C3%B6nberg"}]}, {"year": "1510", "text": "<PERSON>, Sovereign Marchioness of Montferrat (d. 1566)", "html": "1510 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Sovereign Marchioness of Montferrat (d. 1566)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Sovereign Marchioness of Montferrat (d. 1566)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1673", "text": "<PERSON>, English physician and astrologer (d. 1754)", "html": "1673 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English physician and astrologer (d. 1754)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English physician and astrologer (d. 1754)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1718", "text": "<PERSON>, Swiss-English general and politician, 22nd Governor of Quebec (d. 1791)", "html": "1718 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swiss-English general and politician, 22nd <a href=\"https://wikipedia.org/wiki/Governor_of_Quebec\" title=\"Governor of Quebec\">Governor of Quebec</a> (d. 1791)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swiss-English general and politician, 22nd <a href=\"https://wikipedia.org/wiki/Governor_of_Quebec\" title=\"Governor of Quebec\">Governor of Quebec</a> (d. 1791)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Governor of Quebec", "link": "https://wikipedia.org/wiki/Governor_of_Quebec"}]}, {"year": "1722", "text": "<PERSON>, English physician (d. 1797)", "html": "1722 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English physician (d. 1797)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English physician (d. 1797)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1748", "text": "<PERSON>, German composer (d. 1812)", "html": "1748 - <a href=\"https://wikipedia.org/wiki/<PERSON>_(composer)\" title=\"<PERSON> (composer)\"><PERSON></a>, German composer (d. 1812)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(composer)\" title=\"<PERSON> (composer)\"><PERSON></a>, German composer (d. 1812)", "links": [{"title": "<PERSON> (composer)", "link": "https://wikipedia.org/wiki/<PERSON>_(composer)"}]}, {"year": "1778", "text": "<PERSON>, Prussian gymnast, educator, and politician (d. 1852)", "html": "1778 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Prussian gymnast, educator, and politician (d. 1852)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Prussian gymnast, educator, and politician (d. 1852)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1794", "text": "<PERSON>, American engraver (d. 1869)", "html": "1794 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American engraver (d. 1869)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American engraver (d. 1869)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "1807", "text": "<PERSON>, American general, lawyer, and politician (d. 1886)", "html": "1807 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American general, lawyer, and politician (d. 1886)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American general, lawyer, and politician (d. 1886)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1808", "text": "<PERSON>, American lawyer and politician (d. 1892)", "html": "1808 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and politician (d. 1892)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and politician (d. 1892)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "1816", "text": "<PERSON>, Scottish-Australian politician, 9th Premier of Tasmania (d. 1882)", "html": "1816 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish-Australian politician, 9th <a href=\"https://wikipedia.org/wiki/Premier_of_Tasmania\" title=\"Premier of Tasmania\">Premier of Tasmania</a> (d. 1882)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish-Australian politician, 9th <a href=\"https://wikipedia.org/wiki/Premier_of_Tasmania\" title=\"Premier of Tasmania\">Premier of Tasmania</a> (d. 1882)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Frederick_Innes"}, {"title": "Premier of Tasmania", "link": "https://wikipedia.org/wiki/Premier_of_Tasmania"}]}, {"year": "1833", "text": "<PERSON>, American soldier, lawyer, and politician (d. 1899)", "html": "1833 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American soldier, lawyer, and politician (d. 1899)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American soldier, lawyer, and politician (d. 1899)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1833", "text": "<PERSON><PERSON>, Japanese samurai and politician (d. 1877)", "html": "1833 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Japanese samurai and politician (d. 1877)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Japanese samurai and politician (d. 1877)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>"}]}, {"year": "1836", "text": "<PERSON>, American historian and politician (d. 1919)", "html": "1836 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(politician)\" title=\"<PERSON> (politician)\"><PERSON></a>, American historian and politician (d. 1919)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>(politician)\" title=\"<PERSON> (politician)\"><PERSON></a>, American historian and politician (d. 1919)", "links": [{"title": "<PERSON> (politician)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>(politician)"}]}, {"year": "1837", "text": "<PERSON>, French engineer and politician, 4th President of the French Republic (d. 1894)", "html": "1837 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, French engineer and politician, 4th <a href=\"https://wikipedia.org/wiki/President_of_the_French_Republic\" class=\"mw-redirect\" title=\"President of the French Republic\">President of the French Republic</a> (d. 1894)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A7ois_<PERSON><PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, French engineer and politician, 4th <a href=\"https://wikipedia.org/wiki/President_of_the_French_Republic\" class=\"mw-redirect\" title=\"President of the French Republic\">President of the French Republic</a> (d. 1894)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>not"}, {"title": "President of the French Republic", "link": "https://wikipedia.org/wiki/President_of_the_French_Republic"}]}, {"year": "1855", "text": "<PERSON>, Australian cricketer (d. 1933)", "html": "1855 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>(cricketer)\" title=\"<PERSON> (cricketer)\"><PERSON></a>, Australian cricketer (d. 1933)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>(cricketer)\" title=\"<PERSON> (cricketer)\"><PERSON></a>, Australian cricketer (d. 1933)", "links": [{"title": "<PERSON> (cricketer)", "link": "https://wikipedia.org/wiki/<PERSON>(cricketer)"}]}, {"year": "1858", "text": "<PERSON><PERSON>, Dutch physician and academic, Nobel Prize laureate (d. 1930)", "html": "1858 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Dutch physician and academic, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_for_Physiology_or_Medicine\" class=\"mw-redirect\" title=\"Nobel Prize for Physiology or Medicine\">Nobel Prize</a> laureate (d. 1930)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Dutch physician and academic, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_for_Physiology_or_Medicine\" class=\"mw-redirect\" title=\"Nobel Prize for Physiology or Medicine\">Nobel Prize</a> laureate (d. 1930)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}, {"title": "Nobel Prize for Physiology or Medicine", "link": "https://wikipedia.org/wiki/Nobel_Prize_for_Physiology_or_Medicine"}]}, {"year": "1860", "text": "<PERSON><PERSON><PERSON>, Hungarian engineer and chess player (d. 1939)", "html": "1860 - <a href=\"https://wikipedia.org/wiki/Ott%C3%B3_Bl%C3%A1thy\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Hungarian engineer and chess player (d. 1939)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Ott%C3%B3_Bl%C3%A1thy\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Hungarian engineer and chess player (d. 1939)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Ott%C3%B3_Bl%C3%A1thy"}]}, {"year": "1870", "text": "<PERSON>, English cricketer (d. 1912)", "html": "1870 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(cricketer)\" title=\"<PERSON> (cricketer)\"><PERSON></a>, English cricketer (d. 1912)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>(cricketer)\" title=\"<PERSON> (cricketer)\"><PERSON></a>, English cricketer (d. 1912)", "links": [{"title": "<PERSON> (cricketer)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(cricketer)"}]}, {"year": "1874", "text": "Princess <PERSON> of Saxe-Altenburg (d. 1953)", "html": "1874 - <a href=\"https://wikipedia.org/wiki/Princess_Louise_<PERSON>_of_Saxe-Altenburg\" title=\"Princess <PERSON> of Saxe-Altenburg\">Princess <PERSON> of Saxe-Altenburg</a> (d. 1953)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Princess_Louise_<PERSON>_of_Saxe-Altenburg\" title=\"Princess <PERSON> of Saxe-Altenburg\">Princess <PERSON> of Saxe-Altenburg</a> (d. 1953)", "links": [{"title": "Princess <PERSON> of Saxe-Altenburg", "link": "https://wikipedia.org/wiki/Princess_Louise_<PERSON>_of_Saxe-Altenburg"}]}, {"year": "1877", "text": "<PERSON><PERSON><PERSON>, American lawyer and judge (d. 1954)", "html": "1877 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American lawyer and judge (d. 1954)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American lawyer and judge (d. 1954)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1878", "text": "<PERSON>, English poet and author (d. 1955)", "html": "1878 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>._F._<PERSON>\" title=\"Oliver <PERSON>. <PERSON>\"><PERSON></a>, English poet and author (d. 1955)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>._F._<PERSON>\" title=\"Oliver W. F. Lodge\"><PERSON>. <PERSON></a>, English poet and author (d. 1955)", "links": [{"title": "Oliver W. <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "1881", "text": "<PERSON><PERSON><PERSON><PERSON>, Estonian wrestler (d. 1920)", "html": "1881 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"Aleksan<PERSON> Abe<PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Estonian wrestler (d. 1920)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON>eksan<PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Estonian wrestler (d. 1920)", "links": [{"title": "Aleksander <PERSON>", "link": "https://wikipedia.org/wiki/Aleksander_<PERSON>"}]}, {"year": "1884", "text": "<PERSON>, Austrian-Swiss actor (d. 1962)", "html": "1884 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Austrian-Swiss actor (d. 1962)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Austrian-Swiss actor (d. 1962)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1885", "text": "<PERSON>, English physicist and engineer (d. 1958)", "html": "1885 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English physicist and engineer (d. 1958)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English physicist and engineer (d. 1958)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1891", "text": "<PERSON><PERSON>, Bulgarian architect and educator (d. 1962)", "html": "1891 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Bulgarian architect and educator (d. 1962)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Bulgarian architect and educator (d. 1962)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1891", "text": "<PERSON>, Austrian historian and philosopher of science, linked to the Vienna Circle (d. 1944)", "html": "1891 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Austrian historian and philosopher of science, linked to the Vienna Circle (d. 1944)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Austrian historian and philosopher of science, linked to the Vienna Circle (d. 1944)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1892", "text": "<PERSON>, Scottish poet and linguist (d. 1978)", "html": "1892 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish poet and linguist (d. 1978)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish poet and linguist (d. 1978)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1892", "text": "<PERSON><PERSON>, Japanese author (d. 1962)", "html": "1892 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Japanese author (d. 1962)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Japanese author (d. 1962)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>"}]}, {"year": "1897", "text": "<PERSON><PERSON>, English author, poet, and educator (d. 1968)", "html": "1897 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, English author, poet, and educator (d. 1968)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, English author, poet, and educator (d. 1968)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/En<PERSON>_<PERSON><PERSON><PERSON>"}]}, {"year": "1897", "text": "<PERSON>, American poet and critic (d. 1970)", "html": "1897 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American poet and critic (d. 1970)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American poet and critic (d. 1970)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1898", "text": "<PERSON>, Faroese educator and politician, 3rd Prime Minister of the Faroe Islands (d. 1968)", "html": "1898 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Faroese educator and politician, 3rd <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_the_Faroe_Islands\" class=\"mw-redirect\" title=\"Prime Minister of the Faroe Islands\">Prime Minister of the Faroe Islands</a> (d. 1968)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Faroese educator and politician, 3rd <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_the_Faroe_Islands\" class=\"mw-redirect\" title=\"Prime Minister of the Faroe Islands\">Prime Minister of the Faroe Islands</a> (d. 1968)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "Prime Minister of the Faroe Islands", "link": "https://wikipedia.org/wiki/Prime_Minister_of_the_Faroe_Islands"}]}, {"year": "1900", "text": "<PERSON>, American sprinter (d. 1943)", "html": "1900 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American sprinter (d. 1943)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American sprinter (d. 1943)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1900", "text": "<PERSON>, American archaeologist and scholar (d. 1994)", "html": "1900 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(archaeologist)\" title=\"<PERSON> (archaeologist)\"><PERSON></a>, American archaeologist and scholar (d. 1994)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(archaeologist)\" title=\"<PERSON> (archaeologist)\"><PERSON></a>, American archaeologist and scholar (d. 1994)", "links": [{"title": "<PERSON> (archaeologist)", "link": "https://wikipedia.org/wiki/<PERSON>_(archaeologist)"}]}, {"year": "1902", "text": "<PERSON>, Italian cyclist (d. 1986)", "html": "1902 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian cyclist (d. 1986)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian cyclist (d. 1986)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1902", "text": "<PERSON>, American actor (d. 1985)", "html": "1902 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor (d. 1985)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor (d. 1985)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1902", "text": "<PERSON>, French general (d. 1991)", "html": "1902 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"Christian de Castries\"><PERSON></a>, French general (d. 1991)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Christian_de_Cast<PERSON>\" title=\"Christian <PERSON> Cast<PERSON>\"><PERSON></a>, French general (d. 1991)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_de_<PERSON>"}]}, {"year": "1905", "text": "<PERSON>, Austrian-American biochemist and academic (d. 2002)", "html": "1905 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Austrian-American biochemist and academic (d. 2002)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Austrian-American biochemist and academic (d. 2002)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1905", "text": "<PERSON>, Estonian diplomat (d. 1998)", "html": "1905 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Estonian diplomat (d. 1998)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Estonian diplomat (d. 1998)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1907", "text": "<PERSON>, Australian cricketer and lawyer (d. 1989)", "html": "1907 - <a href=\"https://wikipedia.org/wiki/<PERSON>_a%27Beckett\" class=\"mw-redirect\" title=\"<PERSON>'<PERSON>\"><PERSON></a>, Australian cricketer and lawyer (d. 1989)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_a%27B<PERSON><PERSON>\" class=\"mw-redirect\" title=\"<PERSON>'<PERSON>\"><PERSON></a>, Australian cricketer and lawyer (d. 1989)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_a%27B<PERSON><PERSON>"}]}, {"year": "1908", "text": "<PERSON>, American author and illustrator (d. 1978)", "html": "1908 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author and illustrator (d. 1978)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author and illustrator (d. 1978)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1908", "text": "Torgny T:son <PERSON><PERSON><PERSON><PERSON>, Swedish sociologist and philosopher (d. 1999)", "html": "1908 - <a href=\"https://wikipedia.org/wiki/Torgny_T:son_<PERSON><PERSON><PERSON><PERSON>\" title=\"Torgny T:son <PERSON><PERSON>\">Torgny T:son <PERSON></a>, Swedish sociologist and philosopher (d. 1999)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Torgny_T:son_<PERSON><PERSON><PERSON><PERSON>\" title=\"Torgny T:son <PERSON><PERSON><PERSON>\">Torgny T:son <PERSON><PERSON><PERSON><PERSON></a>, Swedish sociologist and philosopher (d. 1999)", "links": [{"title": "Torgny T:son <PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Tor<PERSON>_T:son_<PERSON><PERSON><PERSON>t"}]}, {"year": "1909", "text": "<PERSON><PERSON><PERSON>, Japanese composer (d. 1989)", "html": "1909 - <a href=\"https://wikipedia.org/wiki/Y%C5%<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese composer (d. 1989)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Y%C5%<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese composer (d. 1989)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Y%C5%<PERSON><PERSON>_<PERSON>ki"}]}, {"year": "1909", "text": "<PERSON><PERSON>, Estonian philosopher and theologian (d. 1985)", "html": "1909 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Estonian philosopher and theologian (d. 1985)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Estonian philosopher and theologian (d. 1985)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1911", "text": "<PERSON><PERSON>, Thai field marshal and politician, 10th Prime Minister of Thailand (d. 2004)", "html": "1911 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Thai field marshal and politician, 10th <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Thailand\" title=\"Prime Minister of Thailand\">Prime Minister of Thailand</a> (d. 2004)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Thai field marshal and politician, 10th <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Thailand\" title=\"Prime Minister of Thailand\">Prime Minister of Thailand</a> (d. 2004)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_Kit<PERSON>kachorn"}, {"title": "Prime Minister of Thailand", "link": "https://wikipedia.org/wiki/Prime_Minister_of_Thailand"}]}, {"year": "1912", "text": "<PERSON><PERSON><PERSON><PERSON><PERSON>, German astronomer and academic (d. 1954)", "html": "1912 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>-<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German astronomer and academic (d. 1954)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>-<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German astronomer and academic (d. 1954)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON><PERSON>-<PERSON>"}]}, {"year": "1912", "text": "<PERSON>, American screenwriter and producer (d. 1996)", "html": "1912 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American screenwriter and producer (d. 1996)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American screenwriter and producer (d. 1996)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1913", "text": "<PERSON>, Canadian actor (d. 1976)", "html": "1913 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian actor (d. 1976)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian actor (d. 1976)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1913", "text": "<PERSON>, American baseball player and manager (d. 1985)", "html": "1913 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player and manager (d. 1985)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player and manager (d. 1985)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1913", "text": "<PERSON>, English author and academic (d. 1991)", "html": "1913 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English author and academic (d. 1991)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English author and academic (d. 1991)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1915", "text": "<PERSON>, American author and illustrator (d. 2014)", "html": "1915 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author and illustrator (d. 2014)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author and illustrator (d. 2014)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1916", "text": "<PERSON>, English-Belgian race car driver and trumpet player (d. 1956)", "html": "1916 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English-Belgian race car driver and trumpet player (d. 1956)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English-Belgian race car driver and trumpet player (d. 1956)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1919", "text": "<PERSON>, Puerto Rican-American baseball player and manager (d. 2017)", "html": "1919 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Puerto Rican-American baseball player and manager (d. 2017)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Puerto Rican-American baseball player and manager (d. 2017)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1920", "text": "<PERSON>, American singer and talk show host (d. 2006)", "html": "1920 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer and talk show host (d. 2006)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer and talk show host (d. 2006)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1920", "text": "<PERSON>, Canadian ice hockey player (d. 2002)", "html": "1920 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player (d. 2002)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player (d. 2002)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1921", "text": "<PERSON>, American historian and author (d. 1992)", "html": "1921 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American historian and author (d. 1992)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American historian and author (d. 1992)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1922", "text": "<PERSON> \"<PERSON><PERSON>\" <PERSON>, American baseball player (d. 2013)", "html": "1922 - <a href=\"https://wikipedia.org/wiki/<PERSON>_%22Mule%22_Miles\" class=\"mw-redirect\" title='<PERSON> \"<PERSON><PERSON>\" <PERSON>'><PERSON> \"<PERSON>\" <PERSON></a>, American baseball player (d. 2013)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_%22Mule%22_Miles\" class=\"mw-redirect\" title='<PERSON> \"<PERSON><PERSON>\" <PERSON>'><PERSON> \"<PERSON>\" <PERSON></a>, American baseball player (d. 2013)", "links": [{"title": "<PERSON> \"<PERSON><PERSON>\" <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_%22Mule%22_Miles"}]}, {"year": "1923", "text": "<PERSON>, American journalist and actor (d. 2015)", "html": "1923 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American journalist and actor (d. 2015)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American journalist and actor (d. 2015)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1925", "text": "<PERSON>, Canadian ice hockey player and manager (d. 2006)", "html": "1925 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player and manager (d. 2006)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player and manager (d. 2006)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1925", "text": "<PERSON><PERSON>, American actress, businesswoman and writer (d. 2021)", "html": "1925 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American actress, businesswoman and writer (d. 2021)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American actress, businesswoman and writer (d. 2021)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/A<PERSON>_<PERSON>"}]}, {"year": "1926", "text": "<PERSON>, Lithuanian-English chemist and biophysicist, Nobel Prize laureate (d. 2018)", "html": "1926 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Lithuanian-English chemist and biophysicist, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Chemistry\" title=\"Nobel Prize in Chemistry\">Nobel Prize</a> laureate (d. 2018)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Lithuanian-English chemist and biophysicist, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Chemistry\" title=\"Nobel Prize in Chemistry\">Nobel Prize</a> laureate (d. 2018)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Nobel Prize in Chemistry", "link": "https://wikipedia.org/wiki/Nobel_Prize_in_Chemistry"}]}, {"year": "1927", "text": "<PERSON>, English harpsichord player and conductor (d. 2019)", "html": "1927 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English <a href=\"https://wikipedia.org/wiki/Harpsichord\" title=\"Harpsichord\">harpsichord</a> player and conductor (d. 2019)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English <a href=\"https://wikipedia.org/wiki/Harpsichord\" title=\"Harpsichord\">harpsichord</a> player and conductor (d. 2019)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}, {"title": "Harpsichord", "link": "https://wikipedia.org/wiki/Harpsichord"}]}, {"year": "1927", "text": "<PERSON>, American director and producer (d. 2007)", "html": "1927 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American director and producer (d. 2007)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American director and producer (d. 2007)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1932", "text": "<PERSON>, Spanish actor, director, and playwright", "html": "1932 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Spanish actor, director, and playwright", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Spanish actor, director, and playwright", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1932", "text": "<PERSON>, Canadian lawyer, businessman, and politician, founded Canwest (d. 2003)", "html": "1932 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian lawyer, businessman, and politician, founded <a href=\"https://wikipedia.org/wiki/Canwest\" title=\"Canwest\">Canwest</a> (d. 2003)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian lawyer, businessman, and politician, founded <a href=\"https://wikipedia.org/wiki/Canwest\" title=\"Canwest\">Canwest</a> (d. 2003)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Canwest", "link": "https://wikipedia.org/wiki/Canwest"}]}, {"year": "1932", "text": "<PERSON>, English businessman", "html": "1932 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English businessman", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English businessman", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1932", "text": "<PERSON>, American architect, designed the City of Culture of Galicia", "html": "1932 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American architect, designed the <a href=\"https://wikipedia.org/wiki/City_of_Culture_of_Galicia\" title=\"City of Culture of Galicia\">City of Culture of Galicia</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American architect, designed the <a href=\"https://wikipedia.org/wiki/City_of_Culture_of_Galicia\" title=\"City of Culture of Galicia\">City of Culture of Galicia</a>", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "City of Culture of Galicia", "link": "https://wikipedia.org/wiki/City_of_Culture_of_Galicia"}]}, {"year": "1932", "text": "<PERSON>, English director and screenwriter", "html": "1932 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>(director)\" title=\"<PERSON> (director)\"><PERSON></a>, English director and screenwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>(director)\" title=\"<PERSON> (director)\"><PERSON></a>, English director and screenwriter", "links": [{"title": "<PERSON> (director)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(director)"}]}, {"year": "1933", "text": "<PERSON>, American minister and television host (d. 2007)", "html": "1933 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American minister and television host (d. 2007)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American minister and television host (d. 2007)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1933", "text": "<PERSON><PERSON><PERSON>, Polish director and producer (d. 1999)", "html": "1933 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Polish director and producer (d. 1999)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Polish director and producer (d. 1999)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>owski"}]}, {"year": "1933", "text": "<PERSON><PERSON>, Hungarian pianist and conductor", "html": "1933 - <a href=\"https://wikipedia.org/wiki/Tam%C3%A1s_V%C3%A1s%C3%A1ry\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Hungarian pianist and conductor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Tam%C3%A1s_V%C3%A1s%C3%A1ry\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Hungarian pianist and conductor", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Tam%C3%A1s_V%C3%A1s%C3%A1ry"}]}, {"year": "1934", "text": "<PERSON>, South African lawyer and academic (d. 2015)", "html": "1934 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, South African lawyer and academic (d. 2015)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, South African lawyer and academic (d. 2015)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1936", "text": "<PERSON>, American short story writer, essayist, and memoirist (d. 1999)", "html": "1936 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American short story writer, essayist, and memoirist (d. 1999)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American short story writer, essayist, and memoirist (d. 1999)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1936", "text": "<PERSON>, American baseball player and coach (d. 2015)", "html": "1936 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player and coach (d. 2015)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player and coach (d. 2015)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1936", "text": "<PERSON>, English-American historian and academic (d. 2021)", "html": "1936 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, English-American historian and academic (d. 2021)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, English-American historian and academic (d. 2021)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1937", "text": "<PERSON>, English actress (d. 2011)", "html": "1937 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actress (d. 2011)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actress (d. 2011)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1937", "text": "<PERSON>, American businessman, founded International Data Group (d. 2014)", "html": "1937 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American businessman, founded <a href=\"https://wikipedia.org/wiki/International_Data_Group\" title=\"International Data Group\">International Data Group</a> (d. 2014)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American businessman, founded <a href=\"https://wikipedia.org/wiki/International_Data_Group\" title=\"International Data Group\">International Data Group</a> (d. 2014)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "International Data Group", "link": "https://wikipedia.org/wiki/International_Data_Group"}]}, {"year": "1939", "text": "<PERSON>, first President of Seychelles (d. 2017)", "html": "1939 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, first <a href=\"https://wikipedia.org/wiki/President_of_Seychelles\" class=\"mw-redirect\" title=\"President of Seychelles\">President of Seychelles</a> (d. 2017)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, first <a href=\"https://wikipedia.org/wiki/President_of_Seychelles\" class=\"mw-redirect\" title=\"President of Seychelles\">President of Seychelles</a> (d. 2017)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "President of Seychelles", "link": "https://wikipedia.org/wiki/President_of_Seychelles"}]}, {"year": "1939", "text": "<PERSON>, American singer and guitarist (d. 2003)", "html": "1939 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(musician)\" title=\"<PERSON> (musician)\"><PERSON></a>, American singer and guitarist (d. 2003)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(musician)\" title=\"<PERSON> (musician)\"><PERSON></a>, American singer and guitarist (d. 2003)", "links": [{"title": "<PERSON> (musician)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(musician)"}]}, {"year": "1940", "text": "<PERSON><PERSON>, New Zealand cricketer (d. 2012)", "html": "1940 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_Page\" title=\"<PERSON><PERSON> Page\"><PERSON><PERSON></a>, New Zealand cricketer (d. 2012)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_Page\" title=\"<PERSON><PERSON> Page\"><PERSON><PERSON></a>, New Zealand cricketer (d. 2012)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Glenys_Page"}]}, {"year": "1941", "text": "<PERSON>, American-Canadian musician and songwriter", "html": "1941 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American-Canadian musician and songwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American-Canadian musician and songwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1942", "text": "<PERSON>, English drummer and keyboard player", "html": "1942 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English drummer and keyboard player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English drummer and keyboard player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1942", "text": "<PERSON>, American football player (d. 2023)", "html": "1942 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(American_football)\" title=\"<PERSON> (American football)\"><PERSON></a>, American football player (d. 2023)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(American_football)\" title=\"<PERSON> (American football)\"><PERSON></a>, American football player (d. 2023)", "links": [{"title": "<PERSON> (American football)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(American_football)"}]}, {"year": "1943", "text": "<PERSON>, Canadian bass player", "html": "1943 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian bass player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian bass player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1943", "text": "<PERSON><PERSON>, Pakistani general and politician, 10th President of Pakistan (d. 2023)", "html": "1943 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Pakistani general and politician, 10th <a href=\"https://wikipedia.org/wiki/President_of_Pakistan\" title=\"President of Pakistan\">President of Pakistan</a> (d. 2023)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Pakistani general and politician, 10th <a href=\"https://wikipedia.org/wiki/President_of_Pakistan\" title=\"President of Pakistan\">President of Pakistan</a> (d. 2023)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>f"}, {"title": "President of Pakistan", "link": "https://wikipedia.org/wiki/President_of_Pakistan"}]}, {"year": "1943", "text": "<PERSON>, English saxophonist (d. 2006)", "html": "1943 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, English saxophonist (d. 2006)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, English saxophonist (d. 2006)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1944", "text": "<PERSON>, Swedish-English journalist and politician", "html": "1944 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swedish-English journalist and politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swedish-English journalist and politician", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1944", "text": "<PERSON>, American businessman, founded FedEx", "html": "1944 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American businessman, founded <a href=\"https://wikipedia.org/wiki/FedEx\" title=\"FedEx\">FedEx</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American businessman, founded <a href=\"https://wikipedia.org/wiki/FedEx\" title=\"FedEx\">FedEx</a>", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>"}, {"title": "FedEx", "link": "https://wikipedia.org/wiki/FedEx"}]}, {"year": "1944", "text": "<PERSON>, Scottish actor", "html": "1944 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1946", "text": "<PERSON>, American singer-songwriter", "html": "1946 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1946", "text": "<PERSON>, American journalist and author", "html": "1946 - <a href=\"https://wikipedia.org/wiki/<PERSON>_v<PERSON>_<PERSON>\" title=\"<PERSON> v<PERSON>\"><PERSON> v<PERSON></a>, American journalist and author", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_v<PERSON>_<PERSON>\" title=\"<PERSON> v<PERSON>\"><PERSON> v<PERSON></a>, American journalist and author", "links": [{"title": "<PERSON> v<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "1947", "text": "<PERSON>, Dutch footballer, coach, and manager", "html": "1947 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch footballer, coach, and manager", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch footballer, coach, and manager", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1947", "text": "<PERSON><PERSON>, Greek journalist and politician", "html": "1947 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Greek journalist and politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Greek journalist and politician", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1947", "text": "<PERSON><PERSON><PERSON>, Dutch sprinter", "html": "1947 - <a href=\"https://wikipedia.org/wiki/W<PERSON><PERSON>_<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Dutch sprinter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/W<PERSON><PERSON>_<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Dutch sprinter", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/W<PERSON><PERSON>_<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1948", "text": "<PERSON>, Scottish director, producer, and screenwriter", "html": "1948 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish director, producer, and screenwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish director, producer, and screenwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1949", "text": "<PERSON>, American singer-songwriter and guitarist (d. 2024)", "html": "1949 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and guitarist (d. 2024)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and guitarist (d. 2024)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1949", "text": "<PERSON>, American lawyer and politician", "html": "1949 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and politician", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1949", "text": "<PERSON>, Scottish-English actor and singer (d. 1990)", "html": "1949 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish-English actor and singer (d. 1990)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish-English actor and singer (d. 1990)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1950", "text": "<PERSON>, American singer-songwriter and guitarist (d. 2003)", "html": "1950 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and guitarist (d. 2003)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and guitarist (d. 2003)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1950", "text": "<PERSON><PERSON><PERSON><PERSON>, Russian engineer, designed the AN-94 rifle (d. 2003)", "html": "1950 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>v\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Russian engineer, designed the <a href=\"https://wikipedia.org/wiki/AN-94\" title=\"AN-94\">AN-94 rifle</a> (d. 2003)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Russian engineer, designed the <a href=\"https://wikipedia.org/wiki/AN-94\" title=\"AN-94\">AN-94 rifle</a> (d. 2003)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>"}, {"title": "AN-94", "link": "https://wikipedia.org/wiki/AN-94"}]}, {"year": "1950", "text": "<PERSON>, American computer scientist and programmer, co-founded Apple Inc.", "html": "1950 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American computer scientist and programmer, co-founded <a href=\"https://wikipedia.org/wiki/Apple_Inc.\" title=\"Apple Inc.\">Apple Inc.</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American computer scientist and programmer, co-founded <a href=\"https://wikipedia.org/wiki/Apple_Inc.\" title=\"Apple Inc.\">Apple Inc.</a>", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Apple Inc.", "link": "https://wikipedia.org/wiki/Apple_Inc."}]}, {"year": "1952", "text": "<PERSON>, American photographer (d. 1980)", "html": "1952 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American photographer (d. 1980)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American photographer (d. 1980)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1952", "text": "<PERSON>, American singer, guitarist, and producer", "html": "1952 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer, guitarist, and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer, guitarist, and producer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1953", "text": "<PERSON>, American wrestler", "html": "1953 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American wrestler", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Hogan\"><PERSON></a>, American wrestler", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1953", "text": "<PERSON><PERSON><PERSON><PERSON>, Dutch swimmer", "html": "1953 - <a href=\"https://wikipedia.org/wiki/Wijda_Mazereeuw\" title=\"Wijda Mazereeuw\">Wij<PERSON></a>, Dutch swimmer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Wijda_Mazereeuw\" title=\"Wijda Mazereeuw\">Wij<PERSON></a>, Dutch swimmer", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Wijda_Mazereeuw"}]}, {"year": "1954", "text": "<PERSON>, American guitarist", "html": "1954 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American guitarist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American guitarist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1954", "text": "<PERSON>, American golfer and coach (d. 2012)", "html": "1954 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American golfer and coach (d. 2012)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American golfer and coach (d. 2012)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1954", "text": "<PERSON>, English singer-songwriter and musician", "html": "1954 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(musician)\" title=\"<PERSON> (musician)\"><PERSON></a>, English singer-songwriter and musician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(musician)\" title=\"<PERSON> (musician)\"><PERSON></a>, English singer-songwriter and musician", "links": [{"title": "<PERSON> (musician)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(musician)"}]}, {"year": "1954", "text": "<PERSON><PERSON><PERSON>, Estonian footballer, coach, and manager", "html": "1954 - <a href=\"https://wikipedia.org/wiki/Tarmo_R%C3%BC%C3%BCtli\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Estonian footballer, coach, and manager", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Tarmo_R%C3%BC%C3%BCtli\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Estonian footballer, coach, and manager", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Tarmo_R%C3%BC%C3%BCtli"}]}, {"year": "1954", "text": "<PERSON><PERSON><PERSON>, Indian cricketer and umpire (d. 2021)", "html": "1954 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_(cricketer)\" title=\"<PERSON><PERSON><PERSON> (cricketer)\"><PERSON><PERSON><PERSON></a>, Indian cricketer and umpire (d. 2021)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_(cricketer)\" title=\"<PERSON><PERSON><PERSON> (cricketer)\"><PERSON><PERSON><PERSON></a>, Indian cricketer and umpire (d. 2021)", "links": [{"title": "<PERSON><PERSON><PERSON> (cricketer)", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_(cricketer)"}]}, {"year": "1955", "text": "<PERSON>, Canadian politician, 16th Mayor of Gatineau", "html": "1955 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(politician)\" title=\"<PERSON> (politician)\"><PERSON></a>, Canadian politician, 16th <a href=\"https://wikipedia.org/wiki/Mayor_of_Gatineau\" title=\"Mayor of Gatineau\">Mayor of Gatineau</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(politician)\" title=\"<PERSON> (politician)\"><PERSON></a>, Canadian politician, 16th <a href=\"https://wikipedia.org/wiki/Mayor_of_Gatineau\" title=\"Mayor of Gatineau\">Mayor of Gatineau</a>", "links": [{"title": "<PERSON> (politician)", "link": "https://wikipedia.org/wiki/Marc_<PERSON>_(politician)"}, {"title": "Mayor of Gatineau", "link": "https://wikipedia.org/wiki/Mayor_of_Gatineau"}]}, {"year": "1955", "text": "<PERSON>, Northern Irish academic and politician", "html": "1955 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Northern Irish academic and politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Northern Irish academic and politician", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1956", "text": "<PERSON><PERSON><PERSON>, French mathematician and academic", "html": "1956 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"Pierre-Louis Lions\"><PERSON><PERSON><PERSON></a>, French mathematician and academic", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>-<PERSON>_<PERSON>\" title=\"Pierre-<PERSON> Lions\"><PERSON><PERSON><PERSON></a>, French mathematician and academic", "links": [{"title": "Pierre-Louis Lions", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1957", "text": "<PERSON>, English singer-songwriter and guitarist (d. 1993)", "html": "1957 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English singer-songwriter and guitarist (d. 1993)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English singer-songwriter and guitarist (d. 1993)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1957", "text": "<PERSON><PERSON><PERSON>, Japanese technology entrepreneur and investor", "html": "1957 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese technology entrepreneur and investor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese technology entrepreneur and investor", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1958", "text": "<PERSON>, New Zealand rugby player", "html": "1958 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, New Zealand rugby player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, New Zealand rugby player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1958", "text": "<PERSON><PERSON>, English singer-songwriter and bass player", "html": "1958 - <a href=\"https://wikipedia.org/wiki/<PERSON>ah_Wobble\" title=\"<PERSON>ah Wobble\"><PERSON><PERSON></a>, English singer-songwriter and bass player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_Wobble\" title=\"Jah Wobble\"><PERSON><PERSON></a>, English singer-songwriter and bass player", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/J<PERSON>_Wobble"}]}, {"year": "1959", "text": "<PERSON>, Argentinian singer-songwriter, guitarist, and producer (d. 2014)", "html": "1959 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Argentinian singer-songwriter, guitarist, and producer (d. 2014)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Argentinian singer-songwriter, guitarist, and producer (d. 2014)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1959", "text": "<PERSON><PERSON><PERSON>, Japanese businessman", "html": "1959 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese businessman", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese businessman", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1959", "text": "<PERSON><PERSON>, Sri Lankan journalist and author (d. 2005)", "html": "1959 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Sri Lankan journalist and author (d. 2005)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Sri Lankan journalist and author (d. 2005)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>am"}]}, {"year": "1959", "text": "<PERSON>, English businessman", "html": "1959 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English businessman", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English businessman", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1959", "text": "<PERSON><PERSON><PERSON><PERSON><PERSON>, Jr., Hungarian sculptor", "html": "1959 - <a href=\"https://wikipedia.org/wiki/L%C3%A1szl%C3%B3_Szl%C3%<PERSON><PERSON><PERSON>,_Jr.\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON><PERSON><PERSON>, Jr.\"><PERSON><PERSON><PERSON><PERSON><PERSON>, Jr.</a>, Hungarian sculptor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/L%C3%A1szl%C3%B3_Szl%C3%<PERSON><PERSON><PERSON>,_Jr.\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON><PERSON><PERSON>, Jr.\"><PERSON><PERSON><PERSON><PERSON><PERSON>, Jr.</a>, Hungarian sculptor", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON><PERSON>, Jr.", "link": "https://wikipedia.org/wiki/L%C3%A1szl%C3%B3_Szl%C3%<PERSON><PERSON><PERSON>,_<PERSON>."}]}, {"year": "1961", "text": "<PERSON>, American journalist and author", "html": "1961 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(journalist)\" class=\"mw-redirect\" title=\"<PERSON> (journalist)\"><PERSON></a>, American journalist and author", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(journalist)\" class=\"mw-redirect\" title=\"<PERSON> (journalist)\"><PERSON></a>, American journalist and author", "links": [{"title": "<PERSON> (journalist)", "link": "https://wikipedia.org/wiki/<PERSON>_(journalist)"}]}, {"year": "1961", "text": "<PERSON>, American basketball player and coach", "html": "1961 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player and coach", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1961", "text": "<PERSON><PERSON>, Indian actor and film producer", "html": "1961 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Indian actor and film producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Indian actor and film producer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1962", "text": "<PERSON>, American author", "html": "1962 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1962", "text": "<PERSON>, English video game designer and co-founded Revolution Software", "html": "1962 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English video game designer and co-founded <a href=\"https://wikipedia.org/wiki/Revolution_Software\" title=\"Revolution Software\">Revolution Software</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English video game designer and co-founded <a href=\"https://wikipedia.org/wiki/Revolution_Software\" title=\"Revolution Software\">Revolution Software</a>", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Revolution Software", "link": "https://wikipedia.org/wiki/Revolution_Software"}]}, {"year": "1962", "text": "<PERSON>, English journalist and author", "html": "1962 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English journalist and author", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English journalist and author", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1962", "text": "<PERSON>, American director and producer", "html": "1962 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American director and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American director and producer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1963", "text": "<PERSON><PERSON><PERSON>, Japanese baseball player", "html": "1963 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese baseball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese baseball player", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON><PERSON><PERSON>"}]}, {"year": "1964", "text": "<PERSON>, South Korean-American author and illustrator", "html": "1964 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, South Korean-American author and illustrator", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, South Korean-American author and illustrator", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1964", "text": "<PERSON>, New Zealand golfer", "html": "1964 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, New Zealand golfer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, New Zealand golfer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1965", "text": "<PERSON>, Canadian ice hockey player and manager", "html": "1965 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player and manager", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player and manager", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1965", "text": "<PERSON><PERSON>, American actress", "html": "1965 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American actress", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1965", "text": "<PERSON>, American actress", "html": "1965 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_Davis\" title=\"<PERSON> Davis\"><PERSON></a>, American actress", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1966", "text": "<PERSON>, English footballer and coach", "html": "1966 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English footballer and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English footballer and coach", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1966", "text": "<PERSON>, Argentinian pianist and composer", "html": "1966 - <a href=\"https://wikipedia.org/wiki/Juan_Mar%C3%ADa_<PERSON>e\" title=\"<PERSON>\"><PERSON></a>, Argentinian pianist and composer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Juan_Mar%C3%ADa_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Argentinian pianist and composer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Juan_Mar%C3%ADa_Solare"}]}, {"year": "1967", "text": "<PERSON><PERSON><PERSON><PERSON>, Italian footballer and manager", "html": "1967 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Italian footballer and manager", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Italian footballer and manager", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1967", "text": "<PERSON>, Spanish singer-songwriter and guitarist", "html": "1967 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Spanish singer-songwriter and guitarist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Spanish singer-songwriter and guitarist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1967", "text": "<PERSON>, American actor, comedian, and television host", "html": "1967 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor, comedian, and television host", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor, comedian, and television host", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1967", "text": "<PERSON><PERSON>, Norwegian saxophonist and composer", "html": "1967 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Norwegian saxophonist and composer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Norwegian saxophonist and composer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1968", "text": "<PERSON>, American actress", "html": "1968 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1968", "text": "<PERSON>, British actress", "html": "1968 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, British actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, British actress", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1968", "text": "<PERSON>, American singer-songwriter and guitarist", "html": "1968 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and guitarist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and guitarist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1970", "text": "<PERSON>, German footballer and manager", "html": "1970 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German footballer and manager", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German footballer and manager", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1970", "text": "<PERSON><PERSON><PERSON><PERSON>, Italian footballer", "html": "1970 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Italian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Italian footballer", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1971", "text": "<PERSON><PERSON><PERSON>, Mexican actress and screenwriter", "html": "1971 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Mexican actress and screenwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Mexican actress and screenwriter", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1971", "text": "<PERSON>, English footballer", "html": "1971 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1973", "text": "<PERSON><PERSON>, American cyclist", "html": "1973 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American cyclist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American cyclist", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1974", "text": "<PERSON><PERSON><PERSON>, Canadian figure skater", "html": "1974 - <a href=\"https://wikipedia.org/wiki/Marie-France_Dubreuil\" title=\"Marie-France Dubreuil\"><PERSON><PERSON><PERSON></a>, Canadian figure skater", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Marie-France_Dubreuil\" title=\"Marie-France Dubreuil\"><PERSON><PERSON><PERSON></a>, Canadian figure skater", "links": [{"title": "Marie-<PERSON> Dubreuil", "link": "https://wikipedia.org/wiki/Marie-France_Dubreuil"}]}, {"year": "1974", "text": "<PERSON><PERSON><PERSON>, Pakistani singer, songwriter and philanthropist", "html": "1974 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Pakistani singer, songwriter and philanthropist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Pakistani singer, songwriter and philanthropist", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON><PERSON>"}]}, {"year": "1974", "text": "<PERSON>, French biologist and diver (d. 2002)", "html": "1974 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French biologist and diver (d. 2002)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French biologist and diver (d. 2002)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1974", "text": "<PERSON>, American model and actress", "html": "1974 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American model and actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American model and actress", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1975", "text": "<PERSON>, Canadian singer-songwriter", "html": "1975 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian singer-songwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian singer-songwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1976", "text": "<PERSON><PERSON>, Colombian footballer and manager", "html": "1976 - <a href=\"https://wikipedia.org/wiki/Iv%C3%A1n_C%C3%B3rdoba\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Colombian footballer and manager", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Iv%C3%A1n_C%C3%B3rdoba\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Colombian footballer and manager", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Iv%C3%A1n_C%C3%B3rdoba"}]}, {"year": "1976", "text": "<PERSON>, American baseball player", "html": "1976 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1976", "text": "<PERSON>, American actor and screenwriter", "html": "1976 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor and screenwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor and screenwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1976", "text": "<PERSON>, American singer-songwriter and guitarist", "html": "1976 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and guitarist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and guitarist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1976", "text": "<PERSON><PERSON><PERSON><PERSON><PERSON>, Slovak ice hockey player", "html": "1976 - <a href=\"https://wikipedia.org/wiki/%C4%BDubom%C3%ADr_Vi%C5%A1%C5%88ovsk%C3%BD\" title=\"<PERSON>ubom<PERSON><PERSON>\"><PERSON>ub<PERSON><PERSON><PERSON></a>, Slovak ice hockey player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/%C4%BDubom%C3%ADr_Vi%C5%A1%C5%88ovsk%C3%BD\" title=\"<PERSON>ubom<PERSON><PERSON>\"><PERSON>ub<PERSON><PERSON><PERSON></a>, Slovak ice hockey player", "links": [{"title": "Ľubom<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/%C4%BDubom%C3%ADr_Vi%C5%A1%C5%88ovsk%C3%BD"}]}, {"year": "1977", "text": "<PERSON>, Irish singer-songwriter", "html": "1977 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Irish singer-songwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Irish singer-songwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1977", "text": "<PERSON><PERSON><PERSON>, Brazilian footballer", "html": "1977 - <a href=\"https://wikipedia.org/wiki/D%C3%AAnio_Martins\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Brazilian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/D%C3%AAnio_Martins\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Brazilian footballer", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/D%C3%AAnio_Martins"}]}, {"year": "1978", "text": "<PERSON><PERSON>, Greek footballer", "html": "1978 - <a href=\"https://wikipedia.org/wiki/Spy<PERSON>_Gogolos\" title=\"<PERSON><PERSON> Go<PERSON>\"><PERSON><PERSON></a>, Greek footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_Gogolos\" title=\"<PERSON><PERSON> Gogol<PERSON>\"><PERSON><PERSON></a>, Greek footballer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Spyros_Gogolos"}]}, {"year": "1978", "text": "<PERSON>, British politician", "html": "1978 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, British politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, British politician", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1978", "text": "<PERSON>, Ugandan politician", "html": "1978 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Ugandan politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Ugandan politician", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1978", "text": "<PERSON><PERSON>, English comedian, musician, actress, and writer", "html": "1978 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, English comedian, musician, actress, and writer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, English comedian, musician, actress, and writer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>e"}]}, {"year": "1979", "text": "<PERSON>, Ecuadorian footballer", "html": "1979 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>v%C3%AD\" title=\"<PERSON>\"><PERSON></a>, Ecuadorian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>v%C3%AD\" title=\"<PERSON>\"><PERSON></a>, Ecuadorian footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Walter_Ayov%C3%AD"}]}, {"year": "1980", "text": "<PERSON>, English cyclist and sportscaster", "html": "1980 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(cyclist)\" title=\"<PERSON> (cyclist)\"><PERSON></a>, English cyclist and sportscaster", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>(cyclist)\" title=\"<PERSON> (cyclist)\"><PERSON></a>, English cyclist and sportscaster", "links": [{"title": "<PERSON> (cyclist)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>(cyclist)"}]}, {"year": "1980", "text": "<PERSON>, American football player", "html": "1980 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1981", "text": "<PERSON>, Swedish journalist", "html": "1981 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swedish journalist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swedish journalist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1982", "text": "<PERSON>, American football player", "html": "1982 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(American_football)\" title=\"<PERSON> (American football)\"><PERSON></a>, American football player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(American_football)\" title=\"<PERSON> (American football)\"><PERSON></a>, American football player", "links": [{"title": "<PERSON> (American football)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(American_football)"}]}, {"year": "1983", "text": "<PERSON>, Australian actor", "html": "1983 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1983", "text": "<PERSON>, Australian rugby league player", "html": "1983 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian rugby league player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian rugby league player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1983", "text": "<PERSON>, Russian painter (d. 2013)", "html": "1983 - <a href=\"https://wikipedia.org/wiki/<PERSON>_183\" title=\"<PERSON> 183\"><PERSON> 183</a>, Russian painter (d. 2013)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_183\" title=\"<PERSON> 183\"><PERSON> 183</a>, Russian painter (d. 2013)", "links": [{"title": "<PERSON> 183", "link": "https://wikipedia.org/wiki/<PERSON>_183"}]}, {"year": "1984", "text": "<PERSON><PERSON><PERSON><PERSON>, Iranian Olympic fencer", "html": "1984 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Iranian Olympic fencer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Iranian Olympic fencer", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>i"}]}, {"year": "1984", "text": "<PERSON><PERSON>, Dominican baseball player", "html": "1984 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Dominican baseball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Dominican baseball player", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1984", "text": "<PERSON>, Brazilian race car driver", "html": "1984 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Brazilian race car driver", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Brazilian race car driver", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1985", "text": "<PERSON>, Bahraini-Sri Lankan actress", "html": "1985 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Bahraini-Sri Lankan actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Bahraini-Sri Lankan actress", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1985", "text": "<PERSON>, American rapper", "html": "1985 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American rapper", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American rapper", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1986", "text": "<PERSON><PERSON><PERSON>, Algerian footballer", "html": "1986 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Algerian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Algerian footballer", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1986", "text": "<PERSON><PERSON><PERSON><PERSON>, French sailor", "html": "1986 - <a href=\"https://wikipedia.org/wiki/H%C3%A9l%C3%A8ne_Defrance\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, French sailor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/H%C3%A9l%C3%A8ne_Defrance\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, French sailor", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/H%C3%A9l%C3%A8ne_Defrance"}]}, {"year": "1986", "text": "<PERSON>, Venezuelan baseball player", "html": "1986 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Venezuelan baseball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Venezuelan baseball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1987", "text": "<PERSON><PERSON>, French footballer", "html": "1987 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>%27Guessan\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, French footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>%27Guessan\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, French footballer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Dany_N%27Guessan"}]}, {"year": "1987", "text": "<PERSON>, American baseball player", "html": "1987 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1988", "text": "<PERSON><PERSON><PERSON>, Filipino basketball player", "html": "1988 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Filipino basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Filipino basketball player", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1988", "text": "<PERSON>, Australian basketball player", "html": "1988 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian basketball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1988", "text": "<PERSON>, Turkish footballer", "html": "1988 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Turkish footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Turkish footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1989", "text": "<PERSON>, Irish cyclist and triathlete (d. 2013)", "html": "1989 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Junior He<PERSON>nan\">Junior <PERSON></a>, Irish cyclist and triathlete (d. 2013)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Junior He<PERSON>nan\">Junior <PERSON></a>, Irish cyclist and triathlete (d. 2013)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1989", "text": "<PERSON>, German footballer", "html": "1989 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1990", "text": "<PERSON><PERSON>, Slovak tennis player", "html": "1990 - <a href=\"https://wikipedia.org/wiki/Lenka_Jur%C3%ADkov%C3%A1\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Slovak tennis player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Len<PERSON>_Jur%C3%ADkov%C3%A1\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Slovak tennis player", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Lenka_Jur%C3%ADkov%C3%A1"}]}, {"year": "1991", "text": "<PERSON><PERSON><PERSON>, Spanish footballer", "html": "1991 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Spanish footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Spanish footballer", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1992", "text": "<PERSON><PERSON>, American conservative political commentator", "html": "1992 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American conservative political commentator", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American conservative political commentator", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>i_<PERSON>"}]}, {"year": "1993", "text": "<PERSON><PERSON><PERSON>, American actor, singer, and dancer", "html": "1993 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American actor, singer, and dancer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American actor, singer, and dancer", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>r"}]}, {"year": "1994", "text": "<PERSON>, Australian tennis player", "html": "1994 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, Australian tennis player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, Australian tennis player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1994", "text": "<PERSON>, New Zealand cross-country cyclist", "html": "1994 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, New Zealand cross-country cyclist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, New Zealand cross-country cyclist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1994", "text": "<PERSON>, French footballer", "html": "1994 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1994", "text": "<PERSON>, South Korean singer", "html": "1994 - <a href=\"https://wikipedia.org/wiki/Song_I-han\" title=\"Song I-han\"><PERSON>han</a>, South Korean singer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Song_I-han\" title=\"Song I-han\"><PERSON>-han</a>, South Korean singer", "links": [{"title": "Song I-han", "link": "https://wikipedia.org/wiki/Song_I-han"}]}, {"year": "1995", "text": "<PERSON>, South African motorcycle racer", "html": "1995 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, South African motorcycle racer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Bin<PERSON>\"><PERSON></a>, South African motorcycle racer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>der"}]}, {"year": "1997", "text": "<PERSON>, Scottish footballer", "html": "1997 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1999", "text": "<PERSON><PERSON>, Indonesian badminton player", "html": "1999 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Indonesian badminton player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>jun<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Indonesian badminton player", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Gregoria_Maris<PERSON>_Tunjung"}]}, {"year": "1999", "text": "<PERSON><PERSON>, South Korean rapper", "html": "1999 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_(rapper)\" class=\"mw-redirect\" title=\"<PERSON><PERSON> (rapper)\"><PERSON><PERSON></a>, South Korean rapper", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_(rapper)\" class=\"mw-redirect\" title=\"<PERSON><PERSON> (rapper)\"><PERSON><PERSON></a>, South Korean rapper", "links": [{"title": "<PERSON><PERSON> (rapper)", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_(rapper)"}]}, {"year": "2001", "text": "<PERSON><PERSON><PERSON>, Japanese tennis player", "html": "2001 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese tennis player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese tennis player", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Moyu<PERSON>_Uchi<PERSON>"}]}, {"year": "2002", "text": "<PERSON>, American football player", "html": "2002 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>.\" title=\"<PERSON>\"><PERSON>.</a>, American football player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>.\" title=\"<PERSON>.\"><PERSON>.</a>, American football player", "links": [{"title": "<PERSON>.", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>."}]}], "Deaths": [{"year": "223", "text": "<PERSON><PERSON>, Chinese politician and strategist (b. 147)", "html": "223 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Chinese politician and strategist (b. 147)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Chinese politician and strategist (b. 147)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "353", "text": "<PERSON><PERSON><PERSON><PERSON>, Roman usurper (b. 303)", "html": "353 - <a href=\"https://wikipedia.org/wiki/Magnentius\" title=\"Magnentius\"><PERSON><PERSON><PERSON><PERSON></a>, Roman usurper (b. 303)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Magnenti<PERSON>\" title=\"Magnentius\"><PERSON><PERSON><PERSON><PERSON></a>, Roman usurper (b. 303)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Magnentius"}]}, {"year": "449", "text": "Archbishop <PERSON><PERSON><PERSON> of Constantinople", "html": "449 - <a href=\"https://wikipedia.org/wiki/Archbishop_<PERSON><PERSON><PERSON>_of_Constantinople\" class=\"mw-redirect\" title=\"Archbishop <PERSON><PERSON><PERSON> of Constantinople\">Archbishop <PERSON><PERSON><PERSON> of Constantinople</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Archbishop_<PERSON><PERSON><PERSON>_of_Constantinople\" class=\"mw-redirect\" title=\"Archbishop <PERSON><PERSON><PERSON> of Constantinople\">Archbishop <PERSON><PERSON><PERSON> of Constantinople</a>", "links": [{"title": "Archbishop <PERSON><PERSON><PERSON> of Constantinople", "link": "https://wikipedia.org/wiki/Archbishop_<PERSON><PERSON><PERSON>_of_Constantinople"}]}, {"year": "632", "text": "<PERSON><PERSON><PERSON>, abbess of Arles", "html": "632 - <a href=\"https://wikipedia.org/wiki/Rusticula\" title=\"Rusticula\"><PERSON><PERSON><PERSON></a>, abbess of Arles", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Rusticula\" title=\"Rusticula\"><PERSON><PERSON><PERSON></a>, abbess of Arles", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Rusticula"}]}, {"year": "919", "text": "<PERSON><PERSON><PERSON>, Abbasid governor of Egypt", "html": "919 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_al-Rumi\" title=\"<PERSON><PERSON><PERSON> al-Rumi\"><PERSON><PERSON><PERSON></a>, Abbasid governor of Egypt", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_al-Rumi\" title=\"<PERSON><PERSON><PERSON> al-Rumi\"><PERSON><PERSON><PERSON></a>, Abbasid governor of Egypt", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_al-<PERSON>"}]}, {"year": "979", "text": "<PERSON><PERSON>, Count of Alsleben", "html": "979 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>,_Count_of_Alsleben\" title=\"<PERSON><PERSON>, Count of Alsleben\"><PERSON><PERSON>, Count of Alsleben</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>,_Count_of_Alsleben\" title=\"<PERSON><PERSON>, Count of Alsleben\"><PERSON><PERSON>, Count of Alsleben</a>", "links": [{"title": "<PERSON><PERSON>, Count of Alsleben", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>,_Count_of_Alsleben"}]}, {"year": "991", "text": "<PERSON><PERSON><PERSON><PERSON><PERSON>, English soldier (b. 956)", "html": "991 - <a href=\"https://wikipedia.org/wiki/Byrhtnoth\" title=\"Byrhtnoth\"><PERSON><PERSON><PERSON><PERSON><PERSON></a>, English soldier (b. 956)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Byrhtnoth\" title=\"Byrhtnoth\"><PERSON><PERSON><PERSON><PERSON><PERSON></a>, English soldier (b. 956)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Byrhtnoth"}]}, {"year": "1044", "text": "<PERSON><PERSON><PERSON>, king of the Pagan dynasty of Burma (b. 1001)", "html": "1044 - <a href=\"https://wikipedia.org/wiki/Sokkate\" title=\"Sokkate\"><PERSON><PERSON><PERSON></a>, king of the <a href=\"https://wikipedia.org/wiki/Pagan_dynasty\" class=\"mw-redirect\" title=\"Pagan dynasty\">Pagan dynasty</a> of Burma (b. 1001)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Sokkate\" title=\"Sokkate\"><PERSON><PERSON><PERSON></a>, king of the <a href=\"https://wikipedia.org/wiki/Pagan_dynasty\" class=\"mw-redirect\" title=\"Pagan dynasty\">Pagan dynasty</a> of Burma (b. 1001)", "links": [{"title": "Sokkate", "link": "https://wikipedia.org/wiki/Sokkate"}, {"title": "Pagan dynasty", "link": "https://wikipedia.org/wiki/Pagan_dynasty"}]}, {"year": "1204", "text": "<PERSON><PERSON><PERSON> of Norway (b. 1199)", "html": "1204 - <a href=\"https://wikipedia.org/wiki/Guttorm_of_Norway\" title=\"Guttorm of Norway\"><PERSON>uttorm of Norway</a> (b. 1199)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Guttorm_of_Norway\" title=\"Guttorm of Norway\"><PERSON>uttorm of Norway</a> (b. 1199)", "links": [{"title": "<PERSON><PERSON><PERSON> of Norway", "link": "https://wikipedia.org/wiki/Guttorm_of_Norway"}]}, {"year": "1253", "text": "<PERSON> of Assisi, Italian follower of <PERSON> Assisi (b. 1194)", "html": "1253 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_Assis<PERSON>\" title=\"<PERSON> of Assisi\"><PERSON> of Assisi</a>, Italian follower of <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_Assisi\" title=\"<PERSON> of Assisi\"><PERSON> of Assisi</a> (b. 1194)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON> of Assisi\"><PERSON> of Assisi</a>, Italian follower of <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_Assisi\" title=\"<PERSON> of Assisi\"><PERSON> of Assisi</a> (b. 1194)", "links": [{"title": "Clare of Assisi", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON><PERSON>"}, {"title": "<PERSON> of Assisi", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON><PERSON>"}]}, {"year": "1259", "text": "<PERSON><PERSON><PERSON><PERSON>, Mongolian emperor (b. 1208)", "html": "1259 - <a href=\"https://wikipedia.org/wiki/M%C3%B6<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Mongolian emperor (b. 1208)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/M%C3%B6<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Mongolian emperor (b. 1208)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/M%C3%B6ng<PERSON>_Khan"}]}, {"year": "1268", "text": "<PERSON> of Faucigny, Dame ruler of Faucigny, Countess consort of Savoy", "html": "1268 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_F<PERSON>gny\" title=\"<PERSON> of Faucigny\"><PERSON> of Faucigny</a>, Dame ruler of Faucigny, Countess consort of Savoy", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON> of Faucigny\"><PERSON> of Faucigny</a>, Dame ruler of Faucigny, Countess consort of Savoy", "links": [{"title": "<PERSON> of Faucigny", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1332", "text": "<PERSON><PERSON><PERSON> <PERSON>, Earl of Mar", "html": "1332 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>,_Earl_of_Mar\" title=\"<PERSON><PERSON><PERSON> <PERSON>, Earl of Mar\"><PERSON><PERSON><PERSON> <PERSON>, Earl of Mar</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>,_Earl_of_Mar\" title=\"<PERSON><PERSON><PERSON> <PERSON>, Earl of Mar\"><PERSON><PERSON><PERSON> <PERSON>, Earl of Mar</a>", "links": [{"title": "<PERSON><PERSON><PERSON> <PERSON>, Earl of Mar", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>,_Earl_of_Mar"}]}, {"year": "1332", "text": "<PERSON>, <PERSON><PERSON> of Scotland", "html": "1332 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>,_<PERSON><PERSON>_of_Scotland\" title=\"<PERSON>, <PERSON><PERSON> of Scotland\"><PERSON>, <PERSON><PERSON> of Scotland</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>,_<PERSON><PERSON>_of_Scotland\" title=\"<PERSON>, <PERSON><PERSON> of Scotland\"><PERSON>, <PERSON><PERSON> of Scotland</a>", "links": [{"title": "<PERSON>, <PERSON><PERSON> of Scotland", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>,_<PERSON><PERSON>_of_Scotland"}]}, {"year": "1332", "text": "<PERSON>, 2nd Earl of Moray", "html": "1332 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_2nd_Earl_of_Moray\" title=\"<PERSON>, 2nd Earl of Moray\"><PERSON>, 2nd Earl of Moray</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_2nd_Earl_of_Moray\" title=\"<PERSON>, 2nd Earl of Moray\"><PERSON>, 2nd Earl of Moray</a>", "links": [{"title": "<PERSON>, 2nd Earl of Moray", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_2nd_Earl_of_Moray"}]}, {"year": "1332", "text": "<PERSON>, Earl of Menteith", "html": "1332 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Earl_of_Menteith\" class=\"mw-redirect\" title=\"<PERSON> III, Earl of Menteith\"><PERSON>, Earl of Menteith</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Earl_of_Menteith\" class=\"mw-redirect\" title=\"<PERSON>, Earl of Menteith\"><PERSON>, Earl of Menteith</a>", "links": [{"title": "<PERSON>, Earl of Menteith", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Earl_<PERSON>_<PERSON>"}]}, {"year": "1332", "text": "<PERSON>, Lord of Liddesdale", "html": "1332 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(bastard)\" class=\"mw-redirect\" title=\"<PERSON> (bastard)\"><PERSON></a>, Lord of Liddesdale", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(bastard)\" class=\"mw-redirect\" title=\"<PERSON> (bastard)\"><PERSON></a>, Lord of Liddesdale", "links": [{"title": "<PERSON> (bastard)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(bastard)"}]}, {"year": "1456", "text": "<PERSON>, Hungarian general and politician (b. 1387)", "html": "1456 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Hungarian general and politician (b. 1387)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Hungarian general and politician (b. 1387)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1464", "text": "<PERSON> of Cusa, German cardinal and mystic (b. 1401)", "html": "1464 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_Cusa\" title=\"<PERSON> of Cusa\"><PERSON> of Cusa</a>, German cardinal and mystic (b. 1401)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_Cusa\" title=\"<PERSON> of Cusa\"><PERSON> of Cusa</a>, German cardinal and mystic (b. 1401)", "links": [{"title": "<PERSON> of Cusa", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1465", "text": "<PERSON><PERSON><PERSON>, regent of Sweden and Bishop of Linköping (b. 1433)", "html": "1465 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_(Vasa)\" title=\"<PERSON><PERSON><PERSON> (Vasa)\"><PERSON><PERSON><PERSON></a>, regent of Sweden and Bishop of Linköping (b. 1433)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_(Vasa)\" title=\"<PERSON><PERSON><PERSON> (Vasa)\"><PERSON><PERSON><PERSON></a>, regent of Sweden and Bishop of Linköping (b. 1433)", "links": [{"title": "<PERSON><PERSON><PERSON> (Vasa)", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_(Vasa)"}]}, {"year": "1486", "text": "<PERSON>, English Lord Chancellor and bishop of Winchester (b. c. 1398)", "html": "1486 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English Lord Chancellor and bishop of Winchester (b. c. 1398)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English Lord Chancellor and bishop of Winchester (b. c. 1398)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1494", "text": "<PERSON>, German-Belgian painter (b. 1430)", "html": "1494 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German-Belgian painter (b. 1430)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German-Belgian painter (b. 1430)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1519", "text": "<PERSON>, German preacher  (b. 1465)", "html": "1519 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German preacher (b. 1465)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German preacher (b. 1465)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1556", "text": "<PERSON>, English bishop", "html": "1556 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(bishop_of_Worcester)\" title=\"<PERSON> (bishop of Worcester)\"><PERSON></a>, English bishop", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(bishop_of_Worcester)\" title=\"<PERSON> (bishop of Worcester)\"><PERSON></a>, English bishop", "links": [{"title": "<PERSON> (bishop of Worcester)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(bishop_of_Worcester)"}]}, {"year": "1563", "text": "<PERSON><PERSON><PERSON>, Spanish composer and educator (b. 1500)", "html": "1563 - <a href=\"https://wikipedia.org/wiki/Bartolom%C3%A9_de_Escobedo\" title=\"<PERSON><PERSON><PERSON> de Escobedo\"><PERSON><PERSON><PERSON></a>, Spanish composer and educator (b. 1500)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Bartolom%C3%A9_de_Escobedo\" title=\"<PERSON><PERSON><PERSON> Escobedo\"><PERSON><PERSON><PERSON></a>, Spanish composer and educator (b. 1500)", "links": [{"title": "Bart<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Bartolom%C3%A9_de_Escobedo"}]}, {"year": "1578", "text": "<PERSON>, Portuguese mathematician and academic (b. 1502)", "html": "1578 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Portuguese mathematician and academic (b. 1502)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Portuguese mathematician and academic (b. 1502)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>s"}]}, {"year": "1596", "text": "<PERSON><PERSON>, son of <PERSON> (b. 1585)", "html": "1596 - <a href=\"https://wikipedia.org/wiki/Hamnet_Shakespeare\" title=\"Hamnet Shakespeare\"><PERSON><PERSON></a>, son of <PERSON> (b. 1585)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Hamnet_Shakespeare\" title=\"Hamnet Shakespeare\"><PERSON><PERSON> Shakespeare</a>, son of <PERSON> (b. 1585)", "links": [{"title": "<PERSON><PERSON> Shakespeare", "link": "https://wikipedia.org/wiki/Ham<PERSON>_Shakespeare"}]}, {"year": "1614", "text": "<PERSON><PERSON><PERSON>, Italian painter (b. 1552)", "html": "1614 - <a href=\"https://wikipedia.org/wiki/Lavinia_Fontana\" title=\"<PERSON><PERSON><PERSON> Font<PERSON>\"><PERSON><PERSON><PERSON></a>, Italian painter (b. 1552)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Lavinia_Fontana\" title=\"<PERSON><PERSON><PERSON> Font<PERSON>\"><PERSON><PERSON><PERSON></a>, Italian painter (b. 1552)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Lavinia_Fontana"}]}, {"year": "1656", "text": "<PERSON><PERSON><PERSON>, Austrian-Italian field marshal (b. 1599)", "html": "1656 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Austrian-Italian field marshal (b. 1599)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Austrian-Italian field marshal (b. 1599)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1725", "text": "Prince <PERSON><PERSON><PERSON> of Savoy (b. 1723)", "html": "1725 - <a href=\"https://wikipedia.org/wiki/Prince_<PERSON><PERSON><PERSON>_<PERSON><PERSON><PERSON>_<PERSON>_of_Savoy\" class=\"mw-redirect\" title=\"Prince <PERSON><PERSON><PERSON> of Savoy\">Prince <PERSON><PERSON><PERSON> of Savoy</a> (b. 1723)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Prince_<PERSON><PERSON><PERSON>_<PERSON><PERSON><PERSON>_<PERSON>_of_Savoy\" class=\"mw-redirect\" title=\"Prince <PERSON><PERSON><PERSON> of Savoy\">Prince <PERSON><PERSON><PERSON> of Savoy</a> (b. 1723)", "links": [{"title": "Prince <PERSON><PERSON><PERSON> of Savoy", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON><PERSON>_<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>_Savoy"}]}, {"year": "1774", "text": "<PERSON><PERSON><PERSON>, French physician and author (b. 1722)", "html": "1774 - <a href=\"https://wikipedia.org/wiki/<PERSON>-<PERSON>%C3%A<PERSON><PERSON>_<PERSON><PERSON><PERSON><PERSON>_de_la_Roche\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, French physician and author (b. 1722)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>%C3%A<PERSON><PERSON>_<PERSON><PERSON><PERSON><PERSON>_de_<PERSON>_Roche\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, French physician and author (b. 1722)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>-<PERSON>%C3%A<PERSON><PERSON>_<PERSON><PERSON><PERSON><PERSON>_<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1813", "text": "<PERSON>, English poet and politician (b. 1745)", "html": "1813 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English poet and politician (b. 1745)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English poet and politician (b. 1745)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1851", "text": "<PERSON><PERSON><PERSON>, German botanist, biologist, and ornithologist (b. 1779)", "html": "1851 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, German botanist, biologist, and ornithologist (b. 1779)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, German botanist, biologist, and ornithologist (b. 1779)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>en"}]}, {"year": "1854", "text": "<PERSON><PERSON><PERSON>, Italian physicist and academic (b. 1798)", "html": "1854 - <a href=\"https://wikipedia.org/wiki/Macedonio_Melloni\" title=\"Macedon<PERSON> Melloni\"><PERSON><PERSON><PERSON></a>, Italian physicist and academic (b. 1798)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Macedonio_Melloni\" title=\"<PERSON><PERSON><PERSON>i\"><PERSON><PERSON><PERSON></a>, Italian physicist and academic (b. 1798)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Macedonio_Melloni"}]}, {"year": "1868", "text": "<PERSON><PERSON>, Norwegian pianist and composer (b. 1815)", "html": "1868 - <a href=\"https://wikipedia.org/wiki/Halfdan_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Norwegian pianist and composer (b. 1815)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Norwegian pianist and composer (b. 1815)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Halfdan_<PERSON>"}]}, {"year": "1886", "text": "<PERSON>, Estonian poet and playwright (b. 1843)", "html": "1886 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Estonian poet and playwright (b. 1843)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Estonian poet and playwright (b. 1843)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1890", "text": "<PERSON>, English cardinal and theologian (b. 1801)", "html": "1890 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English cardinal and theologian (b. 1801)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English cardinal and theologian (b. 1801)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1892", "text": "<PERSON>, Italian mathematician and academic (b. 1813)", "html": "1892 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian mathematician and academic (b. 1813)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian mathematician and academic (b. 1813)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1903", "text": "<PERSON><PERSON><PERSON>, Puerto Rican-American  sociologist, philosopher, and lawyer (b. 1839)", "html": "1903 - <a href=\"https://wikipedia.org/wiki/E<PERSON><PERSON>_<PERSON>%C3%AD<PERSON>_de_<PERSON>os\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Puerto Rican-American sociologist, philosopher, and lawyer (b. 1839)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>%C3%AD<PERSON>_de_<PERSON>os\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Puerto Rican-American sociologist, philosopher, and lawyer (b. 1839)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Eugenio_Mar%C3%<PERSON><PERSON>_de_Hostos"}]}, {"year": "1908", "text": "<PERSON><PERSON><PERSON><PERSON>, Indian Bengali revolutionary (b. 1889)", "html": "1908 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Indian Bengali revolutionary (b. 1889)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Indian Bengali revolutionary (b. 1889)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1919", "text": "<PERSON>, Scottish-American businessman and philanthropist, founded the Carnegie Steel Company and Carnegie Hall (b. 1835)", "html": "1919 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish-American businessman and philanthropist, founded the <a href=\"https://wikipedia.org/wiki/Carnegie_Steel_Company\" title=\"Carnegie Steel Company\">Carnegie Steel Company</a> and <a href=\"https://wikipedia.org/wiki/Carnegie_Hall\" title=\"Carnegie Hall\">Carnegie Hall</a> (b. 1835)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish-American businessman and philanthropist, founded the <a href=\"https://wikipedia.org/wiki/Carnegie_Steel_Company\" title=\"Carnegie Steel Company\">Carnegie Steel Company</a> and <a href=\"https://wikipedia.org/wiki/Carnegie_Hall\" title=\"Carnegie Hall\">Carnegie Hall</a> (b. 1835)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Carnegie Steel Company", "link": "https://wikipedia.org/wiki/Carnegie_Steel_Company"}, {"title": "Carnegie Hall", "link": "https://wikipedia.org/wiki/Carnegie_Hall"}]}, {"year": "1921", "text": "<PERSON>, English philanthropist, founded the Mothers' Union (b. 1828)", "html": "1921 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English philanthropist, founded the <a href=\"https://wikipedia.org/wiki/Mothers%27_Union\" title=\"Mothers' Union\">Mothers' Union</a> (b. 1828)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English philanthropist, founded the <a href=\"https://wikipedia.org/wiki/Mothers%27_Union\" title=\"Mothers' Union\">Mothers' Union</a> (b. 1828)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Mothers' Union", "link": "https://wikipedia.org/wiki/Mothers%27_Union"}]}, {"year": "1936", "text": "<PERSON><PERSON>, Spanish historian and politician (b. 1885)", "html": "1936 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Spanish historian and politician (b. 1885)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Spanish historian and politician (b. 1885)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_Infante"}]}, {"year": "1937", "text": "<PERSON>, American novelist and short story writer (b. 1862)", "html": "1937 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American novelist and short story writer (b. 1862)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American novelist and short story writer (b. 1862)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1939", "text": "<PERSON>, German-Italian engineer (b. 1909)", "html": "1939 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German-Italian engineer (b. 1909)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German-Italian engineer (b. 1909)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1939", "text": "<PERSON><PERSON><PERSON>, Austrian fencer (b. 1872)", "html": "1939 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Austrian fencer (b. 1872)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Austrian fencer (b. 1872)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1945", "text": "<PERSON>, Polish actor and theater producer (b. 1883)", "html": "1945 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Polish actor and theater producer (b. 1883)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Polish actor and theater producer (b. 1883)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1953", "text": "<PERSON><PERSON>, Italian race car driver and motorcycle racer (b. 1892)", "html": "1953 - <a href=\"https://wikipedia.org/wiki/Tazio_Nuvolari\" title=\"Tazio Nuvolari\"><PERSON><PERSON></a>, Italian race car driver and motorcycle racer (b. 1892)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Tazio_Nuvolari\" title=\"Tazio Nuvolari\"><PERSON><PERSON></a>, Italian race car driver and motorcycle racer (b. 1892)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Tazio_Nuvolari"}]}, {"year": "1956", "text": "<PERSON>, American painter (b. 1912)", "html": "1956 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American painter (b. 1912)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>ock\"><PERSON></a>, American painter (b. 1912)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1961", "text": "<PERSON><PERSON><PERSON>, Lithuanian-American author, playwright, actor, and director (b. 1910)", "html": "1961 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_%C5%A0k%C4%97ma\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Lithuanian-American author, playwright, actor, and director (b. 1910)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_%C5%A0k%C4%97ma\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Lithuanian-American author, playwright, actor, and director (b. 1910)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Antanas_%C5%A0k%C4%97ma"}]}, {"year": "1963", "text": "<PERSON>, Austrian-American swimmer and coach (b. 1879)", "html": "1963 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Austrian-American swimmer and coach (b. 1879)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Austrian-American swimmer and coach (b. 1879)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1965", "text": "<PERSON>, Australian cricketer and educator (b. 1897)", "html": "1965 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian cricketer and educator (b. 1897)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian cricketer and educator (b. 1897)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1969", "text": "<PERSON>, English soprano and educator (b. 1885)", "html": "1969 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English soprano and educator (b. 1885)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English soprano and educator (b. 1885)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Miriam_<PERSON>"}]}, {"year": "1972", "text": "<PERSON>, South African-American virologist and academic, Nobel Prize laureate (b. 1899)", "html": "1972 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> The<PERSON>\"><PERSON></a>, South African-American virologist and academic, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Physiology_or_Medicine\" title=\"Nobel Prize in Physiology or Medicine\">Nobel Prize</a> laureate (b. 1899)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Theiler\"><PERSON></a>, South African-American virologist and academic, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Physiology_or_Medicine\" title=\"Nobel Prize in Physiology or Medicine\">Nobel Prize</a> laureate (b. 1899)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_Theiler"}, {"title": "Nobel Prize in Physiology or Medicine", "link": "https://wikipedia.org/wiki/Nobel_Prize_in_Physiology_or_Medicine"}]}, {"year": "1974", "text": "<PERSON>, Venezuelan conductor and composer (b. 1887)", "html": "1974 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Venezuelan conductor and composer (b. 1887)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Venezuelan conductor and composer (b. 1887)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1977", "text": "<PERSON>, British co-inventor of the Williams-<PERSON><PERSON><PERSON> tube, used for memory in early computer systems (b. 1911)", "html": "1977 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, British co-inventor of the <a href=\"https://wikipedia.org/wiki/<PERSON>_tube\" title=\"<PERSON> tube\"><PERSON>-Kilborn tube</a>, used for memory in early computer systems (b. 1911)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, British co-inventor of the <a href=\"https://wikipedia.org/wiki/<PERSON>_tube\" title=\"<PERSON> tube\"><PERSON>-Ki<PERSON> tube</a>, used for memory in early computer systems (b. 1911)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "<PERSON> tube", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1978", "text": "<PERSON><PERSON>, Indian-born Welsh romance novelist (b. 1878)", "html": "1978 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>\" title=\"<PERSON><PERSON>uck\"><PERSON><PERSON></a>, Indian-born Welsh romance novelist (b. 1878)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>\" title=\"<PERSON><PERSON> <PERSON>uck\"><PERSON><PERSON></a>, Indian-born Welsh romance novelist (b. 1878)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>"}]}, {"year": "1979", "text": "<PERSON><PERSON> <PERSON><PERSON>, English author (b. 1935)", "html": "1979 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> <PERSON><PERSON>\"><PERSON><PERSON> <PERSON><PERSON></a>, English author (b. 1935)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> <PERSON><PERSON>\"><PERSON><PERSON> <PERSON><PERSON></a>, English author (b. 1935)", "links": [{"title": "<PERSON><PERSON> <PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "1980", "text": "<PERSON>, French lexicographer and publisher (b. 1910)", "html": "1980 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(lexicographer)\" title=\"<PERSON> (lexicographer)\"><PERSON></a>, French lexicographer and publisher (b. 1910)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(lexicographer)\" title=\"<PERSON> (lexicographer)\"><PERSON></a>, French lexicographer and publisher (b. 1910)", "links": [{"title": "<PERSON> (lexicographer)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>(lexicographer)"}]}, {"year": "1982", "text": "<PERSON>, American actor and singer (b. 1918)", "html": "1982 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor and singer (b. 1918)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor and singer (b. 1918)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1984", "text": "<PERSON> A. <PERSON> Sr., American publisher, founded Alfred A. Knopf, Inc. (b. 1892)", "html": "1984 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American publisher, founded <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\">Alfred <PERSON>, Inc.</a> (b. 1892)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American publisher, founded <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\">Alfred <PERSON>, Inc.</a> (b. 1892)", "links": [{"title": "<PERSON> Sr.", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>_<PERSON>."}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "1984", "text": "<PERSON>, Estonian-American chemist and chess player (b. 1916)", "html": "1984 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Estonian-American chemist and chess player (b. 1916)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Estonian-American chemist and chess player (b. 1916)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1986", "text": "<PERSON><PERSON><PERSON>, Hungarian motorcycle racer (b. 1948)", "html": "1986 - <a href=\"https://wikipedia.org/wiki/J%C3%A1nos_Drap%C3%A1l\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Hungarian motorcycle racer (b. 1948)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/J%C3%A1nos_Drap%C3%A1l\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Hungarian motorcycle racer (b. 1948)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/J%C3%A1nos_Drap%C3%A1l"}]}, {"year": "1988", "text": "<PERSON>, American actress (b. 1929)", "html": "1988 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress (b. 1929)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress (b. 1929)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1989", "text": "<PERSON>, Australian actor (b. 1934)", "html": "1989 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian actor (b. 1934)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian actor (b. 1934)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1991", "text": "<PERSON><PERSON> <PERSON><PERSON>, American race car driver (b. 1938)", "html": "1991 - <a href=\"https://wikipedia.org/wiki/J<PERSON>_<PERSON><PERSON>_<PERSON>cD<PERSON><PERSON>\" title=\"<PERSON><PERSON> <PERSON><PERSON> McDuffie\"><PERSON><PERSON> <PERSON><PERSON></a>, American race car driver (b. 1938)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/J<PERSON>_<PERSON><PERSON>_<PERSON>cDuff<PERSON>\" title=\"<PERSON><PERSON> <PERSON><PERSON> McDuffie\"><PERSON><PERSON> <PERSON><PERSON></a>, American race car driver (b. 1938)", "links": [{"title": "<PERSON><PERSON> <PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON><PERSON><PERSON><PERSON><PERSON>"}]}, {"year": "1994", "text": "<PERSON>, English actor (b. 1913)", "html": "1994 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actor (b. 1913)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actor (b. 1913)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1995", "text": "<PERSON>, American singer-songwriter and actor (b. 1904)", "html": "1995 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and actor (b. 1904)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and actor (b. 1904)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1996", "text": "<PERSON>, Czech conductor and composer (b. 1914)", "html": "1996 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%ADk\" title=\"<PERSON>\"><PERSON></a>, Czech conductor and composer (b. 1914)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%ADk\" title=\"<PERSON>\"><PERSON></a>, Czech conductor and composer (b. 1914)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%ADk"}]}, {"year": "1996", "text": "<PERSON><PERSON><PERSON>, Filipino basketball player and politician (b. 1910)", "html": "1996 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Filipino basketball player and politician (b. 1910)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Filipino basketball player and politician (b. 1910)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "2000", "text": "<PERSON><PERSON><PERSON><PERSON>, Canadian composer and academic (b. 1916)", "html": "2000 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian composer and academic (b. 1916)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian composer and academic (b. 1916)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2001", "text": "<PERSON>, English cyclist and coach (b. 1909)", "html": "2001 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English cyclist and coach (b. 1909)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English cyclist and coach (b. 1909)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2002", "text": "<PERSON>, American photographer and mountaineer (b. 1940)", "html": "2002 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American photographer and mountaineer (b. 1940)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American photographer and mountaineer (b. 1940)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2003", "text": "<PERSON>, Swiss-American mathematician and academic (b. 1923)", "html": "2003 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swiss-American mathematician and academic (b. 1923)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swiss-American mathematician and academic (b. 1923)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2003", "text": "<PERSON>, American ice hockey player and coach (b. 1937)", "html": "2003 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American ice hockey player and coach (b. 1937)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American ice hockey player and coach (b. 1937)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2006", "text": "<PERSON>, American singer and talk show host (b. 1920)", "html": "2006 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer and talk show host (b. 1920)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer and talk show host (b. 1920)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2008", "text": "<PERSON>, American actor and playwright (b. 1932)", "html": "2008 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor and playwright (b. 1932)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor and playwright (b. 1932)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2008", "text": "<PERSON><PERSON><PERSON>, founding leader of the Revolutionary People's Liberation Party-Front (DHKP-C) in Turkey (b. 1952)", "html": "2008 - <a href=\"https://wikipedia.org/wiki/Durs<PERSON>_Karata%C5%9F\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, founding leader of the Revolutionary People's Liberation Party-Front (DHKP-C) in Turkey (b. 1952)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Durs<PERSON>_Karata%C5%9F\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, founding leader of the Revolutionary People's Liberation Party-Front (DHKP-C) in Turkey (b. 1952)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Dursun_Karata%C5%9F"}]}, {"year": "2009", "text": "<PERSON><PERSON><PERSON>, American activist, founded the Special Olympics  (b. 1921)", "html": "2009 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American activist, founded the <a href=\"https://wikipedia.org/wiki/Special_Olympics\" title=\"Special Olympics\">Special Olympics</a> (b. 1921)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American activist, founded the <a href=\"https://wikipedia.org/wiki/Special_Olympics\" title=\"Special Olympics\">Special Olympics</a> (b. 1921)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>"}, {"title": "Special Olympics", "link": "https://wikipedia.org/wiki/Special_Olympics"}]}, {"year": "2010", "text": "<PERSON>, British paediatric endocrinologist (b. 1920)", "html": "2010 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, British paediatric endocrinologist (b. 1920)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, British paediatric endocrinologist (b. 1920)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "2012", "text": "<PERSON>, American wrestler, trainer, and promoter (b. 1931)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/Red_Bastien\" title=\"Red Bastien\"><PERSON></a>, American wrestler, trainer, and promoter (b. 1931)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Red_Bastien\" title=\"Red Bastien\"><PERSON></a>, American wrestler, trainer, and promoter (b. 1931)", "links": [{"title": "Red Bastien", "link": "https://wikipedia.org/wiki/Red_Bastien"}]}, {"year": "2012", "text": "<PERSON>, American boxer (b. 1958)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American boxer (b. 1958)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American boxer (b. 1958)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2012", "text": "<PERSON>, Argentinian-Mexican actress and screenwriter (b. 1929)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Argentinian-Mexican actress and screenwriter (b. 1929)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Argentinian-Mexican actress and screenwriter (b. 1929)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2013", "text": "<PERSON>, French cyclist (b. 1943)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French cyclist (b. 1943)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French cyclist (b. 1943)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2013", "text": "<PERSON><PERSON><PERSON>, Indian ornithologist and author (b. 1919)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Indian ornithologist and author (b. 1919)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Indian ornithologist and author (b. 1919)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>y"}]}, {"year": "2013", "text": "<PERSON>, English ballet dancer and educator (b. 1937)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(ballet_teacher)\" title=\"<PERSON> (ballet teacher)\"><PERSON></a>, English ballet dancer and educator (b. 1937)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(ballet_teacher)\" title=\"<PERSON> (ballet teacher)\"><PERSON></a>, English ballet dancer and educator (b. 1937)", "links": [{"title": "<PERSON> (ballet teacher)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>(ballet_teacher)"}]}, {"year": "2014", "text": "<PERSON>, Croatian footballer and manager (b. 1928)", "html": "2014 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Croatian footballer and manager (b. 1928)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Croatian footballer and manager (b. 1928)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Vladimir_<PERSON>"}]}, {"year": "2014", "text": "<PERSON>, Canadian priest and politician (b. 1952)", "html": "2014 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian priest and politician (b. 1952)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian priest and politician (b. 1952)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2014", "text": "<PERSON><PERSON>, Polish author and blogger (b. 1917)", "html": "2014 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Polish author and blogger (b. 1917)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>z<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Polish author and blogger (b. 1917)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Ki<PERSON>_Szasz<PERSON>wiczowa"}]}, {"year": "2014", "text": "<PERSON>, American actor and comedian (b. 1951)", "html": "2014 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor and comedian (b. 1951)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor and comedian (b. 1951)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2015", "text": "<PERSON>, French viola player and educator (b. 1923)", "html": "2015 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French viola player and educator (b. 1923)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French viola player and educator (b. 1923)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>lot"}]}, {"year": "2015", "text": "<PERSON>, Danish footballer and manager (b. 1941)", "html": "2015 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Danish footballer and manager (b. 1941)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Danish footballer and manager (b. 1941)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2015", "text": "<PERSON>, Salvadoran-American metallurgist and engineer (b. 1920)", "html": "2015 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Salvadoran-American metallurgist and engineer (b. 1920)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Salvadoran-American metallurgist and engineer (b. 1920)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2017", "text": "<PERSON><PERSON><PERSON><PERSON>, Polish-Israeli supercentenarian;  oldest living Holocaust survivor and one of the ten oldest men ever (b. 1903)", "html": "2017 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Polish-Israeli supercentenarian; oldest living Holocaust survivor and one of the ten oldest men ever (b. 1903)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Polish-Israeli supercentenarian; oldest living Holocaust survivor and one of the ten oldest men ever (b. 1903)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "2017", "text": "<PERSON><PERSON>, Nigerian musician and journalist (b. 1946)", "html": "2017 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Nigerian musician and journalist (b. 1946)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Nigerian musician and journalist (b. 1946)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "2018", "text": "<PERSON> <PERSON>, British writer, Nobel Prize laureate (b. 1932)", "html": "2018 - <a href=\"https://wikipedia.org/wiki/V_S_Naipaul\" class=\"mw-redirect\" title=\"V S Naipaul\">V <PERSON></a>, British writer, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Literature\" title=\"Nobel Prize in Literature\">Nobel Prize</a> laureate (b. 1932)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/V_S_Naipaul\" class=\"mw-redirect\" title=\"V S Naipaul\">V <PERSON></a>, British writer, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Literature\" title=\"Nobel Prize in Literature\">Nobel Prize</a> laureate (b. 1932)", "links": [{"title": "V S <PERSON>", "link": "https://wikipedia.org/wiki/V_S_Naipaul"}, {"title": "Nobel Prize in Literature", "link": "https://wikipedia.org/wiki/Nobel_Prize_in_Literature"}]}, {"year": "2019", "text": "<PERSON>, Mexican Roman Catholic cardinal (b. 1931)", "html": "2019 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Mexican Roman Catholic cardinal (b. 1931)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Mexican Roman Catholic cardinal (b. 1931)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "2020", "text": "<PERSON><PERSON>, American singer and guitarist (b. 1937)", "html": "2020 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American singer and guitarist (b. 1937)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American singer and guitarist (b. 1937)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "2020", "text": "<PERSON>, American billionaire businessman (b. 1923)", "html": "2020 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>tone\"><PERSON></a>, American billionaire businessman (b. 1923)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>tone\"><PERSON></a>, American billionaire businessman (b. 1923)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_Redstone"}]}, {"year": "2022", "text": "<PERSON>, American actress (b. 1969)", "html": "2022 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress (b. 1969)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress (b. 1969)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2022", "text": "<PERSON><PERSON>, Japanese fashion designer (b. 1926)", "html": "2022 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Japanese fashion designer (b. 1926)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Japanese fashion designer (b. 1926)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "2023", "text": "<PERSON>, Australian politician, 32nd Premier of Queensland (b. 1942)", "html": "2023 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(Australian_politician)\" title=\"<PERSON> (Australian politician)\"><PERSON></a>, Australian politician, 32nd <a href=\"https://wikipedia.org/wiki/Premier_of_Queensland\" title=\"Premier of Queensland\">Premier of Queensland</a> (b. 1942)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(Australian_politician)\" title=\"<PERSON> (Australian politician)\"><PERSON></a>, Australian politician, 32nd <a href=\"https://wikipedia.org/wiki/Premier_of_Queensland\" title=\"Premier of Queensland\">Premier of Queensland</a> (b. 1942)", "links": [{"title": "<PERSON> (Australian politician)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(Australian_politician)"}, {"title": "Premier of Queensland", "link": "https://wikipedia.org/wiki/Premier_of_Queensland"}]}, {"year": "2024", "text": "<PERSON>, Cuban-American comedian and actor (b. 1956)", "html": "2024 - <a href=\"https://wikipedia.org/wiki/%C3%81ngel_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Cuban-American comedian and actor (b. 1956)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/%C3%81nge<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Cuban-American comedian and actor (b. 1956)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/%C3%81ngel_Salazar"}]}, {"year": "2024", "text": "<PERSON><PERSON>, Irish Roman Catholic prelate (b. 1950)", "html": "2024 - <a href=\"https://wikipedia.org/wiki/No%C3%ABl_Treanor\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Irish Roman Catholic prelate (b. 1950)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/No%C3%ABl_Treanor\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Irish Roman Catholic prelate (b. 1950)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/No%C3%ABl_Treanor"}]}]}}