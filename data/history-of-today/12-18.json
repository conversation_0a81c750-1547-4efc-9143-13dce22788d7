{"date": "December 18", "url": "https://wikipedia.org/wiki/December_18", "data": {"Events": [{"year": "1118", "text": "The city of Zaragoza is conquered by king <PERSON> of Aragon from the Almoravid.", "html": "1118 - The city of <a href=\"https://wikipedia.org/wiki/Zaragoza\" title=\"Zaragoza\">Zaragoza</a> is conquered by king <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Aragon\" class=\"mw-redirect\" title=\"<PERSON> of Aragon\"><PERSON> of Aragon</a> from the <a href=\"https://wikipedia.org/wiki/Almoravid_dynasty\" title=\"Almoravid dynasty\">Almoravid</a>.", "no_year_html": "The city of <a href=\"https://wikipedia.org/wiki/Zaragoza\" title=\"Zaragoza\">Zaragoza</a> is conquered by king <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Aragon\" class=\"mw-redirect\" title=\"<PERSON> of Aragon\"><PERSON> of Aragon</a> from the <a href=\"https://wikipedia.org/wiki/Almoravid_dynasty\" title=\"Almoravid dynasty\">Almoravid</a>.", "links": [{"title": "Zaragoza", "link": "https://wikipedia.org/wiki/Zaragoza"}, {"title": "<PERSON> of Aragon", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Aragon"}, {"title": "Almoravid dynasty", "link": "https://wikipedia.org/wiki/Almoravid_dynasty"}]}, {"year": "1271", "text": "<PERSON><PERSON><PERSON> Khan renames his empire \"Yuan\" (元 yuán), officially marking the start of the Yuan dynasty of Mongolia and China.", "html": "1271 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a> renames his empire \"Yuan\" (元 yuán), officially marking the start of the <a href=\"https://wikipedia.org/wiki/Yuan_dynasty\" title=\"Yuan dynasty\">Yuan dynasty</a> of <a href=\"https://wikipedia.org/wiki/Mongolia\" title=\"Mongolia\">Mongolia</a> and China.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a> renames his empire \"Yuan\" (元 yuán), officially marking the start of the <a href=\"https://wikipedia.org/wiki/Yuan_dynasty\" title=\"Yuan dynasty\">Yuan dynasty</a> of <a href=\"https://wikipedia.org/wiki/Mongolia\" title=\"Mongolia\">Mongolia</a> and China.", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}, {"title": "Yuan dynasty", "link": "https://wikipedia.org/wiki/Yuan_dynasty"}, {"title": "Mongolia", "link": "https://wikipedia.org/wiki/Mongolia"}]}, {"year": "1499", "text": "A rebellion breaks out in Alpujarras in response to the forced conversions of Muslims in Spain.", "html": "1499 - <a href=\"https://wikipedia.org/wiki/Rebellion_of_the_Alpujarras_(1499%E2%80%931501)\" title=\"Rebellion of the Alpujarras (1499-1501)\">A rebellion breaks out</a> in <a href=\"https://wikipedia.org/wiki/Alpujarras\" title=\"Alpujarras\">Alpujarras</a> in response to the <a href=\"https://wikipedia.org/wiki/Forced_conversions_of_Muslims_in_Spain\" title=\"Forced conversions of Muslims in Spain\">forced conversions of Muslims in Spain</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Rebellion_of_the_Alpujarras_(1499%E2%80%931501)\" title=\"Rebellion of the Alpujarras (1499-1501)\">A rebellion breaks out</a> in <a href=\"https://wikipedia.org/wiki/Alpujarras\" title=\"Alpujarras\">Alpujarras</a> in response to the <a href=\"https://wikipedia.org/wiki/Forced_conversions_of_Muslims_in_Spain\" title=\"Forced conversions of Muslims in Spain\">forced conversions of Muslims in Spain</a>.", "links": [{"title": "Rebellion of the Alpujarras (1499-1501)", "link": "https://wikipedia.org/wiki/Rebellion_of_the_Alpujarras_(1499%E2%80%931501)"}, {"title": "Alpujarras", "link": "https://wikipedia.org/wiki/Alpujarras"}, {"title": "Forced conversions of Muslims in Spain", "link": "https://wikipedia.org/wiki/Forced_conversions_of_Muslims_in_Spain"}]}, {"year": "1622", "text": "Portuguese forces score a military victory over the Kingdom of Kongo at the Battle of Mbumbi in present-day Angola.", "html": "1622 - <a href=\"https://wikipedia.org/wiki/Portuguese_Empire\" title=\"Portuguese Empire\">Portuguese</a> forces score a military victory over the <a href=\"https://wikipedia.org/wiki/Kingdom_of_Kongo\" title=\"Kingdom of Kongo\">Kingdom of Kongo</a> at the <a href=\"https://wikipedia.org/wiki/Battle_of_Mbumbi\" title=\"Battle of Mbumbi\">Battle of Mbumbi</a> in present-day <a href=\"https://wikipedia.org/wiki/Angola\" title=\"Angola\">Angola</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Portuguese_Empire\" title=\"Portuguese Empire\">Portuguese</a> forces score a military victory over the <a href=\"https://wikipedia.org/wiki/Kingdom_of_Kongo\" title=\"Kingdom of Kongo\">Kingdom of Kongo</a> at the <a href=\"https://wikipedia.org/wiki/Battle_of_Mbumbi\" title=\"Battle of Mbumbi\">Battle of Mbumbi</a> in present-day <a href=\"https://wikipedia.org/wiki/Angola\" title=\"Angola\">Angola</a>.", "links": [{"title": "Portuguese Empire", "link": "https://wikipedia.org/wiki/Portuguese_Empire"}, {"title": "Kingdom of Kongo", "link": "https://wikipedia.org/wiki/Kingdom_of_Kongo"}, {"title": "Battle of Mbumbi", "link": "https://wikipedia.org/wiki/Battle_of_Mbumbi"}, {"title": "Angola", "link": "https://wikipedia.org/wiki/Angola"}]}, {"year": "1655", "text": "The Whitehall Conference ends with the determination that there was no law preventing Jews from re-entering England after the Edict of Expulsion of 1290.", "html": "1655 - The <a href=\"https://wikipedia.org/wiki/Whitehall_Conference\" title=\"Whitehall Conference\">Whitehall Conference</a> ends with the determination that there was no law preventing Jews from re-entering England after the <a href=\"https://wikipedia.org/wiki/Edict_of_Expulsion\" title=\"Edict of Expulsion\">Edict of Expulsion</a> of 1290.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Whitehall_Conference\" title=\"Whitehall Conference\">Whitehall Conference</a> ends with the determination that there was no law preventing Jews from re-entering England after the <a href=\"https://wikipedia.org/wiki/Edict_of_Expulsion\" title=\"Edict of Expulsion\">Edict of Expulsion</a> of 1290.", "links": [{"title": "Whitehall Conference", "link": "https://wikipedia.org/wiki/Whitehall_Conference"}, {"title": "Edict of Expulsion", "link": "https://wikipedia.org/wiki/Edict_of_Expulsion"}]}, {"year": "1777", "text": "The United States celebrates its first Thanksgiving, marking the recent victory by the American rebels over British General <PERSON> at Saratoga in October.", "html": "1777 - The United States celebrates its first <a href=\"https://wikipedia.org/wiki/Thanksgiving_(United_States)\" title=\"Thanksgiving (United States)\">Thanksgiving</a>, marking the recent victory by the American rebels over British General <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> at <a href=\"https://wikipedia.org/wiki/Battles_of_Saratoga\" title=\"Battles of Saratoga\">Saratoga</a> in October.", "no_year_html": "The United States celebrates its first <a href=\"https://wikipedia.org/wiki/Thanksgiving_(United_States)\" title=\"Thanksgiving (United States)\">Thanksgiving</a>, marking the recent victory by the American rebels over British General <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> at <a href=\"https://wikipedia.org/wiki/Battles_of_Saratoga\" title=\"Battles of Saratoga\">Saratoga</a> in October.", "links": [{"title": "Thanksgiving (United States)", "link": "https://wikipedia.org/wiki/Thanksgiving_(United_States)"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}, {"title": "Battles of Saratoga", "link": "https://wikipedia.org/wiki/Battles_of_Saratoga"}]}, {"year": "1787", "text": "New Jersey becomes the third state to ratify the U.S. Constitution.", "html": "1787 - <a href=\"https://wikipedia.org/wiki/New_Jersey\" title=\"New Jersey\">New Jersey</a> becomes the third state to ratify the <a href=\"https://wikipedia.org/wiki/United_States_Constitution\" class=\"mw-redirect\" title=\"United States Constitution\">U.S. Constitution</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/New_Jersey\" title=\"New Jersey\">New Jersey</a> becomes the third state to ratify the <a href=\"https://wikipedia.org/wiki/United_States_Constitution\" class=\"mw-redirect\" title=\"United States Constitution\">U.S. Constitution</a>.", "links": [{"title": "New Jersey", "link": "https://wikipedia.org/wiki/New_Jersey"}, {"title": "United States Constitution", "link": "https://wikipedia.org/wiki/United_States_Constitution"}]}, {"year": "1793", "text": "Surrender of the frigate La Lutine by French Royalists to Lord <PERSON>; renamed HMS <PERSON>tine, she later becomes a famous treasure wreck.", "html": "1793 - Surrender of the frigate <i><PERSON></i> by French Royalists to <a href=\"https://wikipedia.org/wiki/<PERSON>,_1st_Viscount_<PERSON>\" title=\"<PERSON>, 1st Viscount <PERSON>\">Lord <PERSON></a>; renamed <a href=\"https://wikipedia.org/wiki/HMS_<PERSON><PERSON>_(1779)\" title=\"HMS Lutine (1779)\">HMS <i><PERSON><PERSON></i></a>, she later becomes a famous treasure wreck.", "no_year_html": "Surrender of the frigate <i><PERSON></i> by French Royalists to <a href=\"https://wikipedia.org/wiki/<PERSON>,_1st_Viscount_<PERSON>\" title=\"<PERSON>, 1st Viscount <PERSON>\">Lord <PERSON></a>; renamed <a href=\"https://wikipedia.org/wiki/HMS_<PERSON><PERSON>_(1779)\" title=\"HMS Lutine (1779)\">HMS <i><PERSON><PERSON></i></a>, she later becomes a famous treasure wreck.", "links": [{"title": "<PERSON>, 1st Viscount <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>,_1st_Viscount_<PERSON>"}, {"title": "HMS <PERSON>tine (1779)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_(1779)"}]}, {"year": "1833", "text": "The national anthem of the Russian Empire, \"God Save the Tsar!\", is first performed.", "html": "1833 - The <a href=\"https://wikipedia.org/wiki/National_anthem\" title=\"National anthem\">national anthem</a> of the <a href=\"https://wikipedia.org/wiki/Russian_Empire\" title=\"Russian Empire\">Russian Empire</a>, \"<a href=\"https://wikipedia.org/wiki/God_Save_the_Tsar!\" title=\"God Save the Tsar!\">God Save the Tsar!</a>\", is first performed.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/National_anthem\" title=\"National anthem\">national anthem</a> of the <a href=\"https://wikipedia.org/wiki/Russian_Empire\" title=\"Russian Empire\">Russian Empire</a>, \"<a href=\"https://wikipedia.org/wiki/God_Save_the_Tsar!\" title=\"God Save the Tsar!\">God Save the Tsar!</a>\", is first performed.", "links": [{"title": "National anthem", "link": "https://wikipedia.org/wiki/National_anthem"}, {"title": "Russian Empire", "link": "https://wikipedia.org/wiki/Russian_Empire"}, {"title": "God Save the Tsar!", "link": "https://wikipedia.org/wiki/God_Save_the_Tsar!"}]}, {"year": "1854", "text": "The Legislative Assembly of the Province of Canada abolishes the seigneurial system.", "html": "1854 - The <a href=\"https://wikipedia.org/wiki/Legislative_Assembly_of_the_Province_of_Canada\" title=\"Legislative Assembly of the Province of Canada\">Legislative Assembly of the Province of Canada</a> abolishes the <a href=\"https://wikipedia.org/wiki/Seigneurial_system_of_New_France\" title=\"Seigneurial system of New France\">seigneurial system</a>.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Legislative_Assembly_of_the_Province_of_Canada\" title=\"Legislative Assembly of the Province of Canada\">Legislative Assembly of the Province of Canada</a> abolishes the <a href=\"https://wikipedia.org/wiki/Seigneurial_system_of_New_France\" title=\"Seigneurial system of New France\">seigneurial system</a>.", "links": [{"title": "Legislative Assembly of the Province of Canada", "link": "https://wikipedia.org/wiki/Legislative_Assembly_of_the_Province_of_Canada"}, {"title": "Seigneurial system of New France", "link": "https://wikipedia.org/wiki/Seigneurial_system_of_New_France"}]}, {"year": "1865", "text": "US Secretary of State <PERSON> proclaims the adoption of the Thirteenth Amendment, prohibiting slavery throughout the United States.", "html": "1865 - US Secretary of State <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> proclaims the adoption of the <a href=\"https://wikipedia.org/wiki/Thirteenth_Amendment_to_the_United_States_Constitution\" title=\"Thirteenth Amendment to the United States Constitution\">Thirteenth Amendment</a>, prohibiting <a href=\"https://wikipedia.org/wiki/Slavery_in_the_United_States\" title=\"Slavery in the United States\">slavery</a> throughout the United States.", "no_year_html": "US Secretary of State <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> proclaims the adoption of the <a href=\"https://wikipedia.org/wiki/Thirteenth_Amendment_to_the_United_States_Constitution\" title=\"Thirteenth Amendment to the United States Constitution\">Thirteenth Amendment</a>, prohibiting <a href=\"https://wikipedia.org/wiki/Slavery_in_the_United_States\" title=\"Slavery in the United States\">slavery</a> throughout the United States.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "Thirteenth Amendment to the United States Constitution", "link": "https://wikipedia.org/wiki/Thirteenth_Amendment_to_the_United_States_Constitution"}, {"title": "Slavery in the United States", "link": "https://wikipedia.org/wiki/Slavery_in_the_United_States"}]}, {"year": "1867", "text": "A magnitude 7.0 earthquakes strikes off the coast of Taiwan, triggering a tsunami and killing at least 580 people.", "html": "1867 - A <a href=\"https://wikipedia.org/wiki/1867_Keelung_earthquake\" title=\"1867 Keelung earthquake\">magnitude 7.0 earthquakes</a> strikes off the coast of <a href=\"https://wikipedia.org/wiki/Taiwan\" title=\"Taiwan\">Taiwan</a>, triggering a <a href=\"https://wikipedia.org/wiki/Tsunami\" title=\"Tsunami\">tsunami</a> and killing at least 580 people.", "no_year_html": "A <a href=\"https://wikipedia.org/wiki/1867_Keelung_earthquake\" title=\"1867 Keelung earthquake\">magnitude 7.0 earthquakes</a> strikes off the coast of <a href=\"https://wikipedia.org/wiki/Taiwan\" title=\"Taiwan\">Taiwan</a>, triggering a <a href=\"https://wikipedia.org/wiki/Tsunami\" title=\"Tsunami\">tsunami</a> and killing at least 580 people.", "links": [{"title": "1867 Keelung earthquake", "link": "https://wikipedia.org/wiki/1867_Keelung_earthquake"}, {"title": "Taiwan", "link": "https://wikipedia.org/wiki/Taiwan"}, {"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Tsunami"}]}, {"year": "1878", "text": "The <PERSON><PERSON><PERSON><PERSON> family become the rulers of the state of Qatar.", "html": "1878 - The <a href=\"https://wikipedia.org/wiki/House_of_Thani\" title=\"House of Thani\">Al-Thani</a> family become the rulers of the state of <a href=\"https://wikipedia.org/wiki/Qatar\" title=\"Qatar\">Qatar</a>.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/House_of_Thani\" title=\"House of Thani\">Al-Thani</a> family become the rulers of the state of <a href=\"https://wikipedia.org/wiki/Qatar\" title=\"Qatar\">Qatar</a>.", "links": [{"title": "House of Thani", "link": "https://wikipedia.org/wiki/House_of_<PERSON>i"}, {"title": "Qatar", "link": "https://wikipedia.org/wiki/Qatar"}]}, {"year": "1892", "text": "Premiere performance of The Nutcracker by <PERSON><PERSON><PERSON> in Saint Petersburg, Russia.", "html": "1892 - Premiere performance of <i><a href=\"https://wikipedia.org/wiki/The_Nutcracker\" title=\"The Nutcracker\">The Nutcracker</a></i> by <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a> in <a href=\"https://wikipedia.org/wiki/Saint_Petersburg\" title=\"Saint Petersburg\">Saint Petersburg</a>, Russia.", "no_year_html": "Premiere performance of <i><a href=\"https://wikipedia.org/wiki/The_Nutcracker\" title=\"The Nutcracker\">The Nutcracker</a></i> by <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a> in <a href=\"https://wikipedia.org/wiki/Saint_Petersburg\" title=\"Saint Petersburg\">Saint Petersburg</a>, Russia.", "links": [{"title": "The Nutcracker", "link": "https://wikipedia.org/wiki/The_Nutcracker"}, {"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON><PERSON><PERSON>_<PERSON><PERSON>"}, {"title": "Saint Petersburg", "link": "https://wikipedia.org/wiki/Saint_Petersburg"}]}, {"year": "1898", "text": "<PERSON><PERSON> sets the first officially recognized land speed record of 63.159 km/h (39.245 mph) in a Jeantaud electric car.", "html": "1898 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> sets the first officially recognized <a href=\"https://wikipedia.org/wiki/Land_speed_record\" title=\"Land speed record\">land speed record</a> of 63.159 km/h (39.245 mph) in a <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\">Jean<PERSON><PERSON></a> electric car.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> sets the first officially recognized <a href=\"https://wikipedia.org/wiki/Land_speed_record\" title=\"Land speed record\">land speed record</a> of 63.159 km/h (39.245 mph) in a <a href=\"https://wikipedia.org/wiki/Jean<PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a> electric car.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>-<PERSON>"}, {"title": "Land speed record", "link": "https://wikipedia.org/wiki/Land_speed_record"}, {"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>"}]}, {"year": "1916", "text": "World War I: The Battle of Verdun ends when the second French offensive pushes the Germans back two or three kilometres, causing them to cease their attacks.", "html": "1916 - <a href=\"https://wikipedia.org/wiki/World_War_I\" title=\"World War I\">World War I</a>: The <a href=\"https://wikipedia.org/wiki/Battle_of_Verdun\" title=\"Battle of Verdun\">Battle of Verdun</a> ends when the second French offensive pushes the Germans back two or three kilometres, causing them to cease their attacks.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/World_War_I\" title=\"World War I\">World War I</a>: The <a href=\"https://wikipedia.org/wiki/Battle_of_Verdun\" title=\"Battle of Verdun\">Battle of Verdun</a> ends when the second French offensive pushes the Germans back two or three kilometres, causing them to cease their attacks.", "links": [{"title": "World War I", "link": "https://wikipedia.org/wiki/World_War_I"}, {"title": "Battle of Verdun", "link": "https://wikipedia.org/wiki/Battle_of_Verdun"}]}, {"year": "1917", "text": "The resolution containing the language of the Eighteenth Amendment to enact Prohibition is passed by the United States Congress.", "html": "1917 - The resolution containing the language of the <a href=\"https://wikipedia.org/wiki/Eighteenth_Amendment_to_the_United_States_Constitution\" title=\"Eighteenth Amendment to the United States Constitution\">Eighteenth Amendment</a> to enact <a href=\"https://wikipedia.org/wiki/Prohibition_in_the_United_States\" title=\"Prohibition in the United States\">Prohibition</a> is passed by the <a href=\"https://wikipedia.org/wiki/United_States_Congress\" title=\"United States Congress\">United States Congress</a>.", "no_year_html": "The resolution containing the language of the <a href=\"https://wikipedia.org/wiki/Eighteenth_Amendment_to_the_United_States_Constitution\" title=\"Eighteenth Amendment to the United States Constitution\">Eighteenth Amendment</a> to enact <a href=\"https://wikipedia.org/wiki/Prohibition_in_the_United_States\" title=\"Prohibition in the United States\">Prohibition</a> is passed by the <a href=\"https://wikipedia.org/wiki/United_States_Congress\" title=\"United States Congress\">United States Congress</a>.", "links": [{"title": "Eighteenth Amendment to the United States Constitution", "link": "https://wikipedia.org/wiki/Eighteenth_Amendment_to_the_United_States_Constitution"}, {"title": "Prohibition in the United States", "link": "https://wikipedia.org/wiki/Prohibition_in_the_United_States"}, {"title": "United States Congress", "link": "https://wikipedia.org/wiki/United_States_Congress"}]}, {"year": "1932", "text": "The Chicago Bears defeat the Portsmouth Spartans in the first NFL playoff game to win the NFL Championship.", "html": "1932 - The <a href=\"https://wikipedia.org/wiki/Chicago_Bears\" title=\"Chicago Bears\">Chicago Bears</a> defeat the <a href=\"https://wikipedia.org/wiki/Portsmouth_Spartans\" title=\"Portsmouth Spartans\">Portsmouth Spartans</a> in the <a href=\"https://wikipedia.org/wiki/1932_NFL_Playoff_Game\" title=\"1932 NFL Playoff Game\">first NFL playoff game</a> to win the NFL Championship.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Chicago_Bears\" title=\"Chicago Bears\">Chicago Bears</a> defeat the <a href=\"https://wikipedia.org/wiki/Portsmouth_Spartans\" title=\"Portsmouth Spartans\">Portsmouth Spartans</a> in the <a href=\"https://wikipedia.org/wiki/1932_NFL_Playoff_Game\" title=\"1932 NFL Playoff Game\">first NFL playoff game</a> to win the NFL Championship.", "links": [{"title": "Chicago Bears", "link": "https://wikipedia.org/wiki/Chicago_Bears"}, {"title": "Portsmouth Spartans", "link": "https://wikipedia.org/wiki/Portsmouth_Spartans"}, {"title": "1932 NFL Playoff Game", "link": "https://wikipedia.org/wiki/1932_NFL_Playoff_Game"}]}, {"year": "1935", "text": "The Lanka Sama Samaja Party is founded in Ceylon.", "html": "1935 - The <a href=\"https://wikipedia.org/wiki/Lanka_Sama_Samaja_Party\" title=\"Lanka Sama Samaja Party\">Lanka Sama Samaja Party</a> is founded in <a href=\"https://wikipedia.org/wiki/Sri_Lanka\" title=\"Sri Lanka\">Ceylon</a>.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Lanka_Sama_Samaja_Party\" title=\"Lanka Sama Samaja Party\">Lanka Sama Samaja Party</a> is founded in <a href=\"https://wikipedia.org/wiki/Sri_Lanka\" title=\"Sri Lanka\">Ceylon</a>.", "links": [{"title": "Lanka Sama Samaja Party", "link": "https://wikipedia.org/wiki/Lanka_Sama_Samaja_Party"}, {"title": "Sri Lanka", "link": "https://wikipedia.org/wiki/Sri_Lanka"}]}, {"year": "1939", "text": "World War II: The Battle of the Heligoland Bight, the first major air battle of the war, takes place.", "html": "1939 - <a href=\"https://wikipedia.org/wiki/World_War_II\" title=\"World War II\">World War II</a>: The <a href=\"https://wikipedia.org/wiki/Battle_of_the_Heligoland_Bight_(1939)\" title=\"Battle of the Heligoland Bight (1939)\">Battle of the Heligoland Bight</a>, the first major air battle of the war, takes place.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/World_War_II\" title=\"World War II\">World War II</a>: The <a href=\"https://wikipedia.org/wiki/Battle_of_the_Heligoland_Bight_(1939)\" title=\"Battle of the Heligoland Bight (1939)\">Battle of the Heligoland Bight</a>, the first major air battle of the war, takes place.", "links": [{"title": "World War II", "link": "https://wikipedia.org/wiki/World_War_II"}, {"title": "Battle of the Heligoland Bight (1939)", "link": "https://wikipedia.org/wiki/Battle_of_the_Heligoland_Bight_(1939)"}]}, {"year": "1944", "text": "World War II: XX Bomber Command responds to the Japanese Operation Ichi-Go offensive by dropping five hundred tons of incendiary bombs on a supply base in Hankow, China.", "html": "1944 - World War II: <a href=\"https://wikipedia.org/wiki/XX_Bomber_Command\" title=\"XX Bomber Command\">XX Bomber Command</a> responds to the Japanese <a href=\"https://wikipedia.org/wiki/Operation_Ichi-Go\" title=\"Operation Ichi-Go\">Operation Ichi-Go</a> offensive by dropping five hundred tons of incendiary bombs on a supply base in <a href=\"https://wikipedia.org/wiki/Hankou\" title=\"Hankou\">Hankow, China</a>.", "no_year_html": "World War II: <a href=\"https://wikipedia.org/wiki/XX_Bomber_Command\" title=\"XX Bomber Command\">XX Bomber Command</a> responds to the Japanese <a href=\"https://wikipedia.org/wiki/Operation_Ichi-Go\" title=\"Operation Ichi-Go\">Operation Ichi-Go</a> offensive by dropping five hundred tons of incendiary bombs on a supply base in <a href=\"https://wikipedia.org/wiki/Hankou\" title=\"Hankou\">Hankow, China</a>.", "links": [{"title": "XX Bomber Command", "link": "https://wikipedia.org/wiki/XX_Bomber_Command"}, {"title": "Operation Ichi-Go", "link": "https://wikipedia.org/wiki/Operation_<PERSON>chi-Go"}, {"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>"}]}, {"year": "1944", "text": "The Supreme Court of the United States issued its decision in Korematsu v. United States supporting <PERSON>'s Executive Order 9066 which cleared the way for the incarceration of nearly all 120,000 Japanese Americans, two-thirds of whom were U.S. citizens, born and raised in the United States.", "html": "1944 - The <a href=\"https://wikipedia.org/wiki/Supreme_Court_of_the_United_States\" title=\"Supreme Court of the United States\">Supreme Court of the United States</a> issued its decision in <a href=\"https://wikipedia.org/wiki/Korematsu_v._United_States\" title=\"Korematsu v. United States\">Korematsu v. United States</a> supporting <a href=\"https://wikipedia.org/wiki/Franklin_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>'s <a href=\"https://wikipedia.org/wiki/Executive_Order_9066\" title=\"Executive Order 9066\">Executive Order 9066</a> which cleared the way for the incarceration of nearly all 120,000 Japanese Americans, two-thirds of whom were U.S. citizens, born and raised in the United States.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Supreme_Court_of_the_United_States\" title=\"Supreme Court of the United States\">Supreme Court of the United States</a> issued its decision in <a href=\"https://wikipedia.org/wiki/Korematsu_v._United_States\" title=\"Korematsu v. United States\">Korematsu v. United States</a> supporting <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>'s <a href=\"https://wikipedia.org/wiki/Executive_Order_9066\" title=\"Executive Order 9066\">Executive Order 9066</a> which cleared the way for the incarceration of nearly all 120,000 Japanese Americans, two-thirds of whom were U.S. citizens, born and raised in the United States.", "links": [{"title": "Supreme Court of the United States", "link": "https://wikipedia.org/wiki/Supreme_Court_of_the_United_States"}, {"title": "Korematsu v. United States", "link": "https://wikipedia.org/wiki/Korematsu_v._United_States"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "Executive Order 9066", "link": "https://wikipedia.org/wiki/Executive_Order_9066"}]}, {"year": "1957", "text": "A violent F5 tornado wipes out the entire community of Sunfield, Illinois.", "html": "1957 - A <a href=\"https://wikipedia.org/wiki/1957_Sunfield_tornado\" class=\"mw-redirect\" title=\"1957 Sunfield tornado\">violent F5 tornado</a> wipes out the entire community of Sunfield, Illinois.", "no_year_html": "A <a href=\"https://wikipedia.org/wiki/1957_Sunfield_tornado\" class=\"mw-redirect\" title=\"1957 Sunfield tornado\">violent F5 tornado</a> wipes out the entire community of Sunfield, Illinois.", "links": [{"title": "1957 Sunfield tornado", "link": "https://wikipedia.org/wiki/1957_Sunfield_tornado"}]}, {"year": "1958", "text": "Project SCORE, the world's first communications satellite, is launched.", "html": "1958 - <a href=\"https://wikipedia.org/wiki/SCORE_(satellite)\" title=\"SCORE (satellite)\">Project SCORE</a>, the world's first <a href=\"https://wikipedia.org/wiki/Communications_satellite\" title=\"Communications satellite\">communications satellite</a>, is launched.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/SCORE_(satellite)\" title=\"SCORE (satellite)\">Project SCORE</a>, the world's first <a href=\"https://wikipedia.org/wiki/Communications_satellite\" title=\"Communications satellite\">communications satellite</a>, is launched.", "links": [{"title": "SCORE (satellite)", "link": "https://wikipedia.org/wiki/SCORE_(satellite)"}, {"title": "Communications satellite", "link": "https://wikipedia.org/wiki/Communications_satellite"}]}, {"year": "1966", "text": "Saturn's moon Epimetheus is discovered by astronomer <PERSON>.", "html": "1966 - <a href=\"https://wikipedia.org/wiki/Saturn\" title=\"Saturn\">Saturn</a>'s moon <a href=\"https://wikipedia.org/wiki/Epimetheus_(moon)\" title=\"Epimetheus (moon)\">Epimetheus</a> is discovered by astronomer <PERSON>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Saturn\" title=\"Saturn\">Saturn</a>'s moon <a href=\"https://wikipedia.org/wiki/Epimetheus_(moon)\" title=\"Epimetheus (moon)\">Epimetheus</a> is discovered by astronomer <PERSON>.", "links": [{"title": "Saturn", "link": "https://wikipedia.org/wiki/Saturn"}, {"title": "Epimetheus (moon)", "link": "https://wikipedia.org/wiki/Epimetheus_(moon)"}]}, {"year": "1972", "text": "Vietnam War: President <PERSON> announces that the United States will engage North Vietnam in Operation Linebacker II, a series of Christmas bombings, after peace talks collapsed with North Vietnam on the 13th.", "html": "1972 - <a href=\"https://wikipedia.org/wiki/Vietnam_War\" title=\"Vietnam War\">Vietnam War</a>: <a href=\"https://wikipedia.org/wiki/President_of_the_United_States\" title=\"President of the United States\">President</a> <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> announces that the United States will engage <a href=\"https://wikipedia.org/wiki/North_Vietnam\" title=\"North Vietnam\">North Vietnam</a> in <a href=\"https://wikipedia.org/wiki/Operation_Linebacker_II\" title=\"Operation Linebacker II\">Operation Linebacker II</a>, a series of <a href=\"https://wikipedia.org/wiki/Christmas\" title=\"Christmas\">Christmas</a> bombings, after peace talks collapsed with North Vietnam on the 13th.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Vietnam_War\" title=\"Vietnam War\">Vietnam War</a>: <a href=\"https://wikipedia.org/wiki/President_of_the_United_States\" title=\"President of the United States\">President</a> <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> announces that the United States will engage <a href=\"https://wikipedia.org/wiki/North_Vietnam\" title=\"North Vietnam\">North Vietnam</a> in <a href=\"https://wikipedia.org/wiki/Operation_Linebacker_II\" title=\"Operation Linebacker II\">Operation Linebacker II</a>, a series of <a href=\"https://wikipedia.org/wiki/Christmas\" title=\"Christmas\">Christmas</a> bombings, after peace talks collapsed with North Vietnam on the 13th.", "links": [{"title": "Vietnam War", "link": "https://wikipedia.org/wiki/Vietnam_War"}, {"title": "President of the United States", "link": "https://wikipedia.org/wiki/President_of_the_United_States"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "North Vietnam", "link": "https://wikipedia.org/wiki/North_Vietnam"}, {"title": "Operation Linebacker II", "link": "https://wikipedia.org/wiki/Operation_Linebacker_II"}, {"title": "Christmas", "link": "https://wikipedia.org/wiki/Christmas"}]}, {"year": "1973", "text": "Soviet Soyuz Programme: Soyuz 13, crewed by cosmonauts <PERSON><PERSON> and <PERSON><PERSON><PERSON>, is launched from Baikonur in the Soviet Union.", "html": "1973 - <a href=\"https://wikipedia.org/wiki/Soyuz_programme\" title=\"Soyuz programme\">Soviet Soyuz Programme</a>: <i><a href=\"https://wikipedia.org/wiki/Soyuz_13\" title=\"Soyuz 13\">Soyuz 13</a></i>, crewed by <a href=\"https://wikipedia.org/wiki/List_of_cosmonauts\" title=\"List of cosmonauts\">cosmonauts</a> <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a> and <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, is launched from <a href=\"https://wikipedia.org/wiki/Baikonur\" title=\"Baikonur\">Baikonur</a> in the <a href=\"https://wikipedia.org/wiki/Soviet_Union\" title=\"Soviet Union\">Soviet Union</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Soyuz_programme\" title=\"Soyuz programme\">Soviet Soyuz Programme</a>: <i><a href=\"https://wikipedia.org/wiki/Soyuz_13\" title=\"Soyuz 13\">Soyuz 13</a></i>, crewed by <a href=\"https://wikipedia.org/wiki/List_of_cosmonauts\" title=\"List of cosmonauts\">cosmonauts</a> <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a> and <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, is launched from <a href=\"https://wikipedia.org/wiki/Baikonur\" title=\"Baikonur\">Baikonur</a> in the <a href=\"https://wikipedia.org/wiki/Soviet_Union\" title=\"Soviet Union\">Soviet Union</a>.", "links": [{"title": "Soyuz programme", "link": "https://wikipedia.org/wiki/Soyuz_programme"}, {"title": "Soyuz 13", "link": "https://wikipedia.org/wiki/Soyuz_13"}, {"title": "List of cosmonauts", "link": "https://wikipedia.org/wiki/List_of_cosmonauts"}, {"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}, {"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}, {"title": "Baikonur", "link": "https://wikipedia.org/wiki/Baikonur"}, {"title": "Soviet Union", "link": "https://wikipedia.org/wiki/Soviet_Union"}]}, {"year": "1977", "text": "United Airlines Flight 2860 crashes near Kaysville, Utah, killing all three crew members on board.", "html": "1977 - <a href=\"https://wikipedia.org/wiki/United_Airlines_Flight_2860\" title=\"United Airlines Flight 2860\">United Airlines Flight 2860</a> crashes near <a href=\"https://wikipedia.org/wiki/Kaysville,_Utah\" title=\"Kaysville, Utah\">Kaysville, Utah</a>, killing all three crew members on board.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/United_Airlines_Flight_2860\" title=\"United Airlines Flight 2860\">United Airlines Flight 2860</a> crashes near <a href=\"https://wikipedia.org/wiki/Kaysville,_Utah\" title=\"Kaysville, Utah\">Kaysville, Utah</a>, killing all three crew members on board.", "links": [{"title": "United Airlines Flight 2860", "link": "https://wikipedia.org/wiki/United_Airlines_Flight_2860"}, {"title": "Kaysville, Utah", "link": "https://wikipedia.org/wiki/Kaysville,_Utah"}]}, {"year": "1977", "text": "SA de Transport Aérien Flight 730 crashes near Madeira Airport in Funchal, Madeira, Portugal, killing 36.", "html": "1977 - <a href=\"https://wikipedia.org/wiki/SA_de_Transport_A%C3%A9rien_Flight_730\" title=\"SA de Transport Aérien Flight 730\">SA de Transport Aérien Flight 730</a> crashes near <a href=\"https://wikipedia.org/wiki/Cristiano_Ronaldo_International_Airport\" class=\"mw-redirect\" title=\"Cristiano Ronaldo International Airport\">Madeira Airport</a> in <a href=\"https://wikipedia.org/wiki/Funchal\" title=\"Funchal\">Funchal</a>, <a href=\"https://wikipedia.org/wiki/Madeira\" title=\"Madeira\">Madeira</a>, <a href=\"https://wikipedia.org/wiki/Portugal\" title=\"Portugal\">Portugal</a>, killing 36.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/SA_de_Transport_A%C3%A9rien_Flight_730\" title=\"SA de Transport Aérien Flight 730\">SA de Transport Aérien Flight 730</a> crashes near <a href=\"https://wikipedia.org/wiki/Cristiano_Ronaldo_International_Airport\" class=\"mw-redirect\" title=\"Cristiano Ronaldo International Airport\">Madeira Airport</a> in <a href=\"https://wikipedia.org/wiki/Funchal\" title=\"Funchal\">Funchal</a>, <a href=\"https://wikipedia.org/wiki/Madeira\" title=\"Madeira\">Madeira</a>, <a href=\"https://wikipedia.org/wiki/Portugal\" title=\"Portugal\">Portugal</a>, killing 36.", "links": [{"title": "SA de Transport Aérien Flight 730", "link": "https://wikipedia.org/wiki/SA_de_Transport_A%C3%A9rien_Flight_730"}, {"title": "<PERSON><PERSON>iano <PERSON>o International Airport", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_Ronaldo_International_Airport"}, {"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Funchal"}, {"title": "Madeira", "link": "https://wikipedia.org/wiki/Madeira"}, {"title": "Portugal", "link": "https://wikipedia.org/wiki/Portugal"}]}, {"year": "1981", "text": "First flight of the Russian heavy strategic bomber Tu-160, the world's largest combat aircraft, largest supersonic aircraft and largest variable-sweep wing aircraft built.", "html": "1981 - First flight of the Russian <a href=\"https://wikipedia.org/wiki/Heavy_bomber\" title=\"Heavy bomber\">heavy</a> <a href=\"https://wikipedia.org/wiki/Strategic_bomber\" title=\"Strategic bomber\">strategic bomber</a> <a href=\"https://wikipedia.org/wiki/Tupolev_Tu-160\" title=\"Tupolev Tu-160\">Tu-160</a>, the world's largest <a href=\"https://wikipedia.org/wiki/Combat_aircraft\" class=\"mw-redirect\" title=\"Combat aircraft\">combat aircraft</a>, largest <a href=\"https://wikipedia.org/wiki/Supersonic_aircraft\" title=\"Supersonic aircraft\">supersonic aircraft</a> and largest <a href=\"https://wikipedia.org/wiki/Variable-sweep_wing\" title=\"Variable-sweep wing\">variable-sweep wing</a> aircraft built.", "no_year_html": "First flight of the Russian <a href=\"https://wikipedia.org/wiki/Heavy_bomber\" title=\"Heavy bomber\">heavy</a> <a href=\"https://wikipedia.org/wiki/Strategic_bomber\" title=\"Strategic bomber\">strategic bomber</a> <a href=\"https://wikipedia.org/wiki/Tupolev_Tu-160\" title=\"Tupolev Tu-160\">Tu-160</a>, the world's largest <a href=\"https://wikipedia.org/wiki/Combat_aircraft\" class=\"mw-redirect\" title=\"Combat aircraft\">combat aircraft</a>, largest <a href=\"https://wikipedia.org/wiki/Supersonic_aircraft\" title=\"Supersonic aircraft\">supersonic aircraft</a> and largest <a href=\"https://wikipedia.org/wiki/Variable-sweep_wing\" title=\"Variable-sweep wing\">variable-sweep wing</a> aircraft built.", "links": [{"title": "Heavy bomber", "link": "https://wikipedia.org/wiki/Heavy_bomber"}, {"title": "Strategic bomber", "link": "https://wikipedia.org/wiki/Strategic_bomber"}, {"title": "Tupolev Tu-160", "link": "https://wikipedia.org/wiki/Tupolev_Tu-160"}, {"title": "Combat aircraft", "link": "https://wikipedia.org/wiki/Combat_aircraft"}, {"title": "Supersonic aircraft", "link": "https://wikipedia.org/wiki/Supersonic_aircraft"}, {"title": "Variable-sweep wing", "link": "https://wikipedia.org/wiki/Variable-sweep_wing"}]}, {"year": "1995", "text": " A Lockheed L-188 Electra crashes in Jamba, Cuando Cubango, Angola, killing 141 people.", "html": "1995 - A <a href=\"https://wikipedia.org/wiki/Lockheed_L-188_Electra\" title=\"Lockheed L-188 Electra\">Lockheed L-188 Electra</a> <a href=\"https://wikipedia.org/wiki/1995_Trans_Service_Airlift_Electra_crash\" title=\"1995 Trans Service Airlift Electra crash\">crashes</a> in <a href=\"https://wikipedia.org/wiki/Jamba,_Cuando_Cubango\" title=\"Jamba, Cuando Cubango\">Jamba, Cuando Cubango</a>, <a href=\"https://wikipedia.org/wiki/Angola\" title=\"Angola\">Angola</a>, killing 141 people.", "no_year_html": "A <a href=\"https://wikipedia.org/wiki/Lockheed_L-188_Electra\" title=\"Lockheed L-188 Electra\">Lockheed L-188 Electra</a> <a href=\"https://wikipedia.org/wiki/1995_Trans_Service_Airlift_Electra_crash\" title=\"1995 Trans Service Airlift Electra crash\">crashes</a> in <a href=\"https://wikipedia.org/wiki/Jamba,_Cuando_Cubango\" title=\"Jamba, Cuando Cubango\">Jamba, Cuando Cubango</a>, <a href=\"https://wikipedia.org/wiki/Angola\" title=\"Angola\">Angola</a>, killing 141 people.", "links": [{"title": "Lockheed L-188 Electra", "link": "https://wikipedia.org/wiki/Lockheed_L-188_Electra"}, {"title": "1995 Trans Service Airlift Electra crash", "link": "https://wikipedia.org/wiki/1995_Trans_Service_Airlift_Electra_crash"}, {"title": "Jamba, Cuando Cubango", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>,_Cuando_Cubango"}, {"title": "Angola", "link": "https://wikipedia.org/wiki/Angola"}]}, {"year": "1999", "text": "NASA launches into orbit the Terra platform carrying five Earth Observation instruments, including ASTER, CERES, MISR, MODIS and MOPITT.", "html": "1999 - <a href=\"https://wikipedia.org/wiki/NASA\" title=\"NASA\">NASA</a> launches into orbit the <a href=\"https://wikipedia.org/wiki/Terra_(satellite)\" title=\"Terra (satellite)\">Terra</a> platform carrying five Earth Observation instruments, including <a href=\"https://wikipedia.org/wiki/Advanced_Spaceborne_Thermal_Emission_and_Reflection_Radiometer\" title=\"Advanced Spaceborne Thermal Emission and Reflection Radiometer\">ASTER</a>, <a href=\"https://wikipedia.org/wiki/Clouds_and_the_Earth%27s_Radiant_Energy_System\" title=\"Clouds and the Earth's Radiant Energy System\">CERES</a>, <a href=\"https://wikipedia.org/wiki/Multi-angle_Imaging_SpectroRadiometer\" class=\"mw-redirect\" title=\"Multi-angle Imaging SpectroRadiometer\">MISR</a>, <a href=\"https://wikipedia.org/wiki/Moderate-Resolution_Imaging_Spectroradiometer\" class=\"mw-redirect\" title=\"Moderate-Resolution Imaging Spectroradiometer\">MODIS</a> and <a href=\"https://wikipedia.org/wiki/MOPITT\" title=\"MOPITT\">MOPITT</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/NASA\" title=\"NASA\">NASA</a> launches into orbit the <a href=\"https://wikipedia.org/wiki/Terra_(satellite)\" title=\"Terra (satellite)\">Terra</a> platform carrying five Earth Observation instruments, including <a href=\"https://wikipedia.org/wiki/Advanced_Spaceborne_Thermal_Emission_and_Reflection_Radiometer\" title=\"Advanced Spaceborne Thermal Emission and Reflection Radiometer\">ASTER</a>, <a href=\"https://wikipedia.org/wiki/Clouds_and_the_Earth%27s_Radiant_Energy_System\" title=\"Clouds and the Earth's Radiant Energy System\">CERES</a>, <a href=\"https://wikipedia.org/wiki/Multi-angle_Imaging_SpectroRadiometer\" class=\"mw-redirect\" title=\"Multi-angle Imaging SpectroRadiometer\">MISR</a>, <a href=\"https://wikipedia.org/wiki/Moderate-Resolution_Imaging_Spectroradiometer\" class=\"mw-redirect\" title=\"Moderate-Resolution Imaging Spectroradiometer\">MODIS</a> and <a href=\"https://wikipedia.org/wiki/MOPITT\" title=\"MOPITT\">MOPITT</a>.", "links": [{"title": "NASA", "link": "https://wikipedia.org/wiki/NASA"}, {"title": "Terra (satellite)", "link": "https://wikipedia.org/wiki/Terra_(satellite)"}, {"title": "Advanced Spaceborne Thermal Emission and Reflection Radiometer", "link": "https://wikipedia.org/wiki/Advanced_Spaceborne_Thermal_Emission_and_Reflection_Radiometer"}, {"title": "Clouds and the Earth's Radiant Energy System", "link": "https://wikipedia.org/wiki/Clouds_and_the_Earth%27s_Radiant_Energy_System"}, {"title": "Multi-angle Imaging SpectroRadiometer", "link": "https://wikipedia.org/wiki/Multi-angle_Imaging_SpectroRadiometer"}, {"title": "Moderate-Resolution Imaging Spectroradiometer", "link": "https://wikipedia.org/wiki/Moderate-Resolution_Imaging_Spectroradiometer"}, {"title": "MOPITT", "link": "https://wikipedia.org/wiki/MOPITT"}]}, {"year": "2002", "text": "California gubernatorial recall: Then Governor of California <PERSON> announces that the state would face a record budget deficit of $35 billion, roughly double the figure reported during his reelection campaign one month earlier.", "html": "2002 - <a href=\"https://wikipedia.org/wiki/2003_California_gubernatorial_recall_election\" title=\"2003 California gubernatorial recall election\">California gubernatorial recall</a>: Then <a href=\"https://wikipedia.org/wiki/Governor_of_California\" title=\"Governor of California\">Governor of California</a> <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> announces that the <a href=\"https://wikipedia.org/wiki/California\" title=\"California\">state</a> would face a record <a href=\"https://wikipedia.org/wiki/Government_budget_balance\" title=\"Government budget balance\">budget deficit</a> of <a href=\"https://wikipedia.org/wiki/United_States_dollar\" title=\"United States dollar\">$</a>35 billion, roughly double the figure reported during his reelection campaign one month earlier.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/2003_California_gubernatorial_recall_election\" title=\"2003 California gubernatorial recall election\">California gubernatorial recall</a>: Then <a href=\"https://wikipedia.org/wiki/Governor_of_California\" title=\"Governor of California\">Governor of California</a> <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> announces that the <a href=\"https://wikipedia.org/wiki/California\" title=\"California\">state</a> would face a record <a href=\"https://wikipedia.org/wiki/Government_budget_balance\" title=\"Government budget balance\">budget deficit</a> of <a href=\"https://wikipedia.org/wiki/United_States_dollar\" title=\"United States dollar\">$</a>35 billion, roughly double the figure reported during his reelection campaign one month earlier.", "links": [{"title": "2003 California gubernatorial recall election", "link": "https://wikipedia.org/wiki/2003_California_gubernatorial_recall_election"}, {"title": "Governor of California", "link": "https://wikipedia.org/wiki/Governor_of_California"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "California", "link": "https://wikipedia.org/wiki/California"}, {"title": "Government budget balance", "link": "https://wikipedia.org/wiki/Government_budget_balance"}, {"title": "United States dollar", "link": "https://wikipedia.org/wiki/United_States_dollar"}]}, {"year": "2005", "text": "The Chadian Civil War begins when rebel groups, allegedly backed by neighbouring Sudan, launch an attack in Adré.", "html": "2005 - The <a href=\"https://wikipedia.org/wiki/Chadian_Civil_War_(2005%E2%80%9310)\" class=\"mw-redirect\" title=\"Chadian Civil War (2005-10)\">Chadian Civil War</a> begins when rebel groups, allegedly backed by neighbouring <a href=\"https://wikipedia.org/wiki/Sudan\" title=\"Sudan\">Sudan</a>, launch an <a href=\"https://wikipedia.org/wiki/Battle_of_Adr%C3%A9\" title=\"Battle of Adré\">attack in Adré</a>.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Chadian_Civil_War_(2005%E2%80%9310)\" class=\"mw-redirect\" title=\"Chadian Civil War (2005-10)\">Chadian Civil War</a> begins when rebel groups, allegedly backed by neighbouring <a href=\"https://wikipedia.org/wiki/Sudan\" title=\"Sudan\">Sudan</a>, launch an <a href=\"https://wikipedia.org/wiki/Battle_of_Adr%C3%A9\" title=\"Battle of Adré\">attack in Adré</a>.", "links": [{"title": "Chadian Civil War (2005-10)", "link": "https://wikipedia.org/wiki/Chadian_Civil_War_(2005%E2%80%9310)"}, {"title": "Sudan", "link": "https://wikipedia.org/wiki/Sudan"}, {"title": "Battle of Adré", "link": "https://wikipedia.org/wiki/Battle_of_Adr%C3%A9"}]}, {"year": "2006", "text": "The first of a series of floods strikes Malaysia. The death toll of all flooding is at least 118, with over 400,000 people displaced.", "html": "2006 - The first of a <a href=\"https://wikipedia.org/wiki/2006%E2%80%9307_Malaysian_floods\" class=\"mw-redirect\" title=\"2006-07 Malaysian floods\">series of floods</a> strikes <a href=\"https://wikipedia.org/wiki/Malaysia\" title=\"Malaysia\">Malaysia</a>. The death toll of all flooding is at least 118, with over 400,000 people displaced.", "no_year_html": "The first of a <a href=\"https://wikipedia.org/wiki/2006%E2%80%9307_Malaysian_floods\" class=\"mw-redirect\" title=\"2006-07 Malaysian floods\">series of floods</a> strikes <a href=\"https://wikipedia.org/wiki/Malaysia\" title=\"Malaysia\">Malaysia</a>. The death toll of all flooding is at least 118, with over 400,000 people displaced.", "links": [{"title": "2006-07 Malaysian floods", "link": "https://wikipedia.org/wiki/2006%E2%80%9307_Malaysian_floods"}, {"title": "Malaysia", "link": "https://wikipedia.org/wiki/Malaysia"}]}, {"year": "2006", "text": "United Arab Emirates holds its first-ever elections.", "html": "2006 - <a href=\"https://wikipedia.org/wiki/United_Arab_Emirates\" title=\"United Arab Emirates\">United Arab Emirates</a> holds its <a href=\"https://wikipedia.org/wiki/United_Arab_Emirates_parliamentary_election,_2006\" class=\"mw-redirect\" title=\"United Arab Emirates parliamentary election, 2006\">first-ever elections</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/United_Arab_Emirates\" title=\"United Arab Emirates\">United Arab Emirates</a> holds its <a href=\"https://wikipedia.org/wiki/United_Arab_Emirates_parliamentary_election,_2006\" class=\"mw-redirect\" title=\"United Arab Emirates parliamentary election, 2006\">first-ever elections</a>.", "links": [{"title": "United Arab Emirates", "link": "https://wikipedia.org/wiki/United_Arab_Emirates"}, {"title": "United Arab Emirates parliamentary election, 2006", "link": "https://wikipedia.org/wiki/United_Arab_Emirates_parliamentary_election,_2006"}]}, {"year": "2015", "text": "Kellingley Colliery, the last deep coal mine in Great Britain, closes.", "html": "2015 - <a href=\"https://wikipedia.org/wiki/Kellingley_Colliery\" title=\"Kellingley Colliery\">Kellingley Colliery</a>, the last deep <a href=\"https://wikipedia.org/wiki/Coal_mine\" class=\"mw-redirect\" title=\"Coal mine\">coal mine</a> in <a href=\"https://wikipedia.org/wiki/Great_Britain\" title=\"Great Britain\">Great Britain</a>, closes.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Kellingley_Colliery\" title=\"Kellingley Colliery\">Kellingley Colliery</a>, the last deep <a href=\"https://wikipedia.org/wiki/Coal_mine\" class=\"mw-redirect\" title=\"Coal mine\">coal mine</a> in <a href=\"https://wikipedia.org/wiki/Great_Britain\" title=\"Great Britain\">Great Britain</a>, closes.", "links": [{"title": "Kellingley Colliery", "link": "https://wikipedia.org/wiki/Kellingley_Colliery"}, {"title": "Coal mine", "link": "https://wikipedia.org/wiki/Coal_mine"}, {"title": "Great Britain", "link": "https://wikipedia.org/wiki/Great_Britain"}]}, {"year": "2017", "text": "Amtrak Cascades passenger train 501, derailed near DuPont, Washington, a city in United States near Olympia, Washington killing six people, and injuring 70 others.", "html": "2017 - <a href=\"https://wikipedia.org/wiki/Amtrak_Cascades\" title=\"Amtrak Cascades\">Amtrak Cascades</a> passenger train 501, <a href=\"https://wikipedia.org/wiki/2017_Washington_train_derailment\" title=\"2017 Washington train derailment\">derailed</a> near <a href=\"https://wikipedia.org/wiki/DuPont,_Washington\" title=\"DuPont, Washington\">DuPont, Washington</a>, a city in United States near <a href=\"https://wikipedia.org/wiki/Olympia,_Washington\" title=\"Olympia, Washington\">Olympia, Washington</a> killing six people, and injuring 70 others.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Amtrak_Cascades\" title=\"Amtrak Cascades\">Amtrak Cascades</a> passenger train 501, <a href=\"https://wikipedia.org/wiki/2017_Washington_train_derailment\" title=\"2017 Washington train derailment\">derailed</a> near <a href=\"https://wikipedia.org/wiki/DuPont,_Washington\" title=\"DuPont, Washington\">DuPont, Washington</a>, a city in United States near <a href=\"https://wikipedia.org/wiki/Olympia,_Washington\" title=\"Olympia, Washington\">Olympia, Washington</a> killing six people, and injuring 70 others.", "links": [{"title": "Amtrak Cascades", "link": "https://wikipedia.org/wiki/Amtrak_Cascades"}, {"title": "2017 Washington train derailment", "link": "https://wikipedia.org/wiki/2017_Washington_train_derailment"}, {"title": "DuPont, Washington", "link": "https://wikipedia.org/wiki/DuPont,_Washington"}, {"title": "Olympia, Washington", "link": "https://wikipedia.org/wiki/Olympia,_Washington"}]}, {"year": "2018", "text": "List of bolides: A meteor exploded over the Bering Sea with a force over 10 times greater than the atomic bomb that destroyed Hiroshima in 1945.", "html": "2018 - <a href=\"https://wikipedia.org/wiki/List_of_bolides\" title=\"List of bolides\">List of bolides</a>: A meteor exploded over the Bering Sea with a force over 10 times greater than the atomic bomb that destroyed Hiroshima in 1945.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/List_of_bolides\" title=\"List of bolides\">List of bolides</a>: A meteor exploded over the Bering Sea with a force over 10 times greater than the atomic bomb that destroyed Hiroshima in 1945.", "links": [{"title": "List of bolides", "link": "https://wikipedia.org/wiki/List_of_bolides"}]}, {"year": "2019", "text": "The United States House of Representatives impeaches <PERSON> for the first time.", "html": "2019 - The United States House of Representatives <a href=\"https://wikipedia.org/wiki/First_impeachment_of_<PERSON>_<PERSON>\" title=\"First impeachment of <PERSON>\">impeaches <PERSON> for the first time</a>.", "no_year_html": "The United States House of Representatives <a href=\"https://wikipedia.org/wiki/First_impeachment_of_<PERSON>_<PERSON>\" title=\"First impeachment of <PERSON>\">impeaches <PERSON> for the first time</a>.", "links": [{"title": "First impeachment of <PERSON>", "link": "https://wikipedia.org/wiki/First_impeachment_of_<PERSON>_<PERSON>"}]}, {"year": "2022", "text": "Argentina win the 2022 FIFA World Cup final, defeating title holders France 4-2 on penalties following a 3-3 draw after extra time.", "html": "2022 - <a href=\"https://wikipedia.org/wiki/Argentina_national_football_team\" title=\"Argentina national football team\">Argentina</a> win the <a href=\"https://wikipedia.org/wiki/2022_FIFA_World_Cup_final\" title=\"2022 FIFA World Cup final\">2022 FIFA World Cup final</a>, defeating title holders <a href=\"https://wikipedia.org/wiki/France_national_football_team\" title=\"France national football team\">France</a> 4-2 on penalties following a 3-3 draw after extra time.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Argentina_national_football_team\" title=\"Argentina national football team\">Argentina</a> win the <a href=\"https://wikipedia.org/wiki/2022_FIFA_World_Cup_final\" title=\"2022 FIFA World Cup final\">2022 FIFA World Cup final</a>, defeating title holders <a href=\"https://wikipedia.org/wiki/France_national_football_team\" title=\"France national football team\">France</a> 4-2 on penalties following a 3-3 draw after extra time.", "links": [{"title": "Argentina national football team", "link": "https://wikipedia.org/wiki/Argentina_national_football_team"}, {"title": "2022 FIFA World Cup final", "link": "https://wikipedia.org/wiki/2022_FIFA_World_Cup_final"}, {"title": "France national football team", "link": "https://wikipedia.org/wiki/France_national_football_team"}]}], "Births": [{"year": "1406", "text": "<PERSON>, French Roman Catholic bishop and cardinal (d. 1470)", "html": "1406 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French Roman Catholic <a href=\"https://wikipedia.org/wiki/Bishop\" title=\"Bishop\">bishop</a> and <a href=\"https://wikipedia.org/wiki/Cardinal_(Catholic_Church)\" title=\"Cardinal (Catholic Church)\">cardinal</a> (d. 1470)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French Roman Catholic <a href=\"https://wikipedia.org/wiki/Bishop\" title=\"Bishop\">bishop</a> and <a href=\"https://wikipedia.org/wiki/Cardinal_(Catholic_Church)\" title=\"Cardinal (Catholic Church)\">cardinal</a> (d. 1470)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Bishop"}, {"title": "<PERSON> (Catholic Church)", "link": "https://wikipedia.org/wiki/<PERSON>_(Catholic_Church)"}]}, {"year": "1481", "text": "<PERSON> of Mecklenburg, Duchess of Mecklenburg, Duchess of Saxony (d. 1503)", "html": "1481 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_Mecklenburg_(1481%E2%80%931503)\" title=\"<PERSON> of Mecklenburg (1481-1503)\"><PERSON> of Mecklenburg</a>, Duchess of Mecklenburg, Duchess of Saxony (d. 1503)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_Mecklenburg_(1481%E2%80%931503)\" title=\"<PERSON> of Mecklenburg (1481-1503)\"><PERSON> of Mecklenburg</a>, Duchess of Mecklenburg, Duchess of Saxony (d. 1503)", "links": [{"title": "<PERSON> of Mecklenburg (1481-1503)", "link": "https://wikipedia.org/wiki/<PERSON>_of_Mecklenburg_(1481%E2%80%931503)"}]}, {"year": "1499", "text": "<PERSON><PERSON><PERSON>, German musicologist and theologian (d. 1561)", "html": "1499 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, German musicologist and theologian (d. 1561)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, German musicologist and theologian (d. 1561)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1505", "text": "<PERSON>, German explorer (d. 1546)", "html": "1505 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German explorer (d. 1546)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German explorer (d. 1546)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1507", "text": "<PERSON><PERSON>, Japanese daimyō (d. 1551)", "html": "1507 - <a href=\"https://wikipedia.org/wiki/%C5%8C<PERSON>_<PERSON>shi<PERSON>ka\" title=\"<PERSON><PERSON>shi<PERSON>\"><PERSON><PERSON></a>, Japanese daimyō (d. 1551)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/%C5%8C<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>shi<PERSON>\"><PERSON><PERSON></a>, Japanese daimyō (d. 1551)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/%C5%8C<PERSON>_<PERSON><PERSON><PERSON>"}]}, {"year": "1552", "text": "<PERSON>, Moroccan writer, judge and mathematician (d. 1616)", "html": "1552 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Moroccan writer, judge and mathematician (d. 1616)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Moroccan writer, judge and mathematician (d. 1616)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1590", "text": "<PERSON>, Count of Nassau-Saarbrücken (d. 1640)", "html": "1590 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Count_of_Nassau-Saarbr%C3%BCcken\" title=\"<PERSON>, Count of Nassau-Saarbrücken\"><PERSON>, Count of Nassau-Saarbrücken</a> (d. 1640)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Count_of_Nassau-Saarbr%C3%<PERSON>cken\" title=\"<PERSON>, Count of Nassau-Saarbrücken\"><PERSON>, Count of Nassau-Saarbrücken</a> (d. 1640)", "links": [{"title": "<PERSON>, Count of Nassau-Saarbrücken", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Count_of_Nassau-Saarbr%C3%BC<PERSON>n"}]}, {"year": "1602", "text": "<PERSON><PERSON>, English historian and politician (d. 1650)", "html": "1602 - <a href=\"https://wikipedia.org/wiki/Simonds_d%27Ewes\" title=\"<PERSON><PERSON> d'E<PERSON>\"><PERSON><PERSON></a>, English historian and politician (d. 1650)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Simonds_d%27Ewes\" title=\"<PERSON><PERSON> d'E<PERSON>\"><PERSON><PERSON></a>, English historian and politician (d. 1650)", "links": [{"title": "Simonds d'Ewes", "link": "https://wikipedia.org/wiki/Simonds_d%27Ewes"}]}, {"year": "1610", "text": "<PERSON>, <PERSON><PERSON> du <PERSON>, French philologist and historian (d. 1688)", "html": "1610 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_sieur_du_<PERSON>\" title=\"<PERSON>, sieur du <PERSON>\"><PERSON>, sieur du <PERSON></a>, French philologist and historian (d. 1688)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>,_sieur_du_<PERSON>\" title=\"<PERSON>, sieur du <PERSON>\"><PERSON>, sieur du <PERSON></a>, French philologist and historian (d. 1688)", "links": [{"title": "<PERSON>, sieur du <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>,_<PERSON><PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1620", "text": "<PERSON>, German missionary and scholar (d. 1668)", "html": "1620 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German missionary and scholar (d. 1668)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German missionary and scholar (d. 1668)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1624", "text": "<PERSON>, colonial American merchant and politician (d. 1683)", "html": "1624 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(merchant)\" title=\"<PERSON> (merchant)\"><PERSON></a>, colonial American merchant and politician (d. 1683)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(merchant)\" title=\"<PERSON> (merchant)\"><PERSON></a>, colonial American merchant and politician (d. 1683)", "links": [{"title": "<PERSON> (merchant)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(merchant)"}]}, {"year": "1626", "text": "<PERSON>, Queen of Sweden (d. 1689)", "html": "1626 - <a href=\"https://wikipedia.org/wiki/<PERSON>,_Queen_of_Sweden\" title=\"<PERSON>, Queen of Sweden\"><PERSON>, Queen of Sweden</a> (d. 1689)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>,_Queen_of_Sweden\" title=\"<PERSON>, Queen of Sweden\"><PERSON>, Queen of Sweden</a> (d. 1689)", "links": [{"title": "<PERSON>, Queen of Sweden", "link": "https://wikipedia.org/wiki/<PERSON>,_Queen_of_Sweden"}]}, {"year": "1660", "text": "Countess <PERSON> of Hanau-Lichtenberg (d. 1715)", "html": "1660 - <a href=\"https://wikipedia.org/wiki/Countess_<PERSON>_<PERSON>_of_Hanau-Lichtenberg\" title=\"Countess <PERSON> of Hanau-Lichtenberg\">Countess <PERSON> of Hanau-Lichtenberg</a> (d. 1715)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Countess_<PERSON>_<PERSON>_of_Hanau-Lichtenberg\" title=\"Countess <PERSON> of Hanau-Lichtenberg\">Countess <PERSON> of Hanau-Lichtenberg</a> (d. 1715)", "links": [{"title": "Countess <PERSON> of Hanau-Lichtenberg", "link": "https://wikipedia.org/wiki/Countess_<PERSON>_<PERSON>_<PERSON>_Hanau-<PERSON>"}]}, {"year": "1661", "text": "<PERSON>, Swedish physicist and inventor (d. 1751)", "html": "1661 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swedish physicist and inventor (d. 1751)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swedish physicist and inventor (d. 1751)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1662", "text": "<PERSON>, 2nd Duke of Queensberry, Scottish colonel and politician, Secretary of State for Scotland (d. 1711)", "html": "1662 - <a href=\"https://wikipedia.org/wiki/<PERSON>,_2nd_Duke_of_Queensberry\" title=\"<PERSON>, 2nd Duke of Queensberry\"><PERSON>, 2nd Duke of Queensberry</a>, Scottish colonel and politician, <a href=\"https://wikipedia.org/wiki/Secretary_of_State_for_Scotland\" title=\"Secretary of State for Scotland\">Secretary of State for Scotland</a> (d. 1711)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>,_2nd_Duke_of_Queensberry\" title=\"<PERSON>, 2nd Duke of Queensberry\"><PERSON>, 2nd Duke of Queensberry</a>, Scottish colonel and politician, <a href=\"https://wikipedia.org/wiki/Secretary_of_State_for_Scotland\" title=\"Secretary of State for Scotland\">Secretary of State for Scotland</a> (d. 1711)", "links": [{"title": "<PERSON>, 2nd Duke of Queensberry", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_2nd_Duke_<PERSON>_Queensberry"}, {"title": "Secretary of State for Scotland", "link": "https://wikipedia.org/wiki/Secretary_of_State_for_Scotland"}]}, {"year": "1707", "text": "<PERSON>, English missionary and composer (d. 1788)", "html": "1707 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English missionary and composer (d. 1788)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English missionary and composer (d. 1788)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1725", "text": "<PERSON>, German historian and theologian (d. 1791)", "html": "1725 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German historian and theologian (d. 1791)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German historian and theologian (d. 1791)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1734", "text": "<PERSON><PERSON><PERSON>, French conductor and composer (d. 1810)", "html": "1734 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, French conductor and composer (d. 1810)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, French conductor and composer (d. 1810)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>"}]}, {"year": "1800", "text": "<PERSON>, English brewer and businessman (d. 1884)", "html": "1800 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English brewer and businessman (d. 1884)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English brewer and businessman (d. 1884)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1824", "text": "<PERSON>, English-New Zealand politician, 12th Prime Minister of New Zealand (d. 1907)", "html": "1824 - <a href=\"https://wikipedia.org/wiki/<PERSON>_(New_Zealand_politician)\" title=\"<PERSON> (New Zealand politician)\"><PERSON></a>, English-New Zealand politician, 12th <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_New_Zealand\" title=\"Prime Minister of New Zealand\">Prime Minister of New Zealand</a> (d. 1907)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(New_Zealand_politician)\" title=\"<PERSON> (New Zealand politician)\"><PERSON></a>, English-New Zealand politician, 12th <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_New_Zealand\" title=\"Prime Minister of New Zealand\">Prime Minister of New Zealand</a> (d. 1907)", "links": [{"title": "<PERSON> (New Zealand politician)", "link": "https://wikipedia.org/wiki/<PERSON>_(New_Zealand_politician)"}, {"title": "Prime Minister of New Zealand", "link": "https://wikipedia.org/wiki/Prime_Minister_of_New_Zealand"}]}, {"year": "1825", "text": "<PERSON>, American general (d. 1876)", "html": "1825 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American general (d. 1876)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American general (d. 1876)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1825", "text": "<PERSON>, American surveyor and politician (d. 1906)", "html": "1825 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American surveyor and politician (d. 1906)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American surveyor and politician (d. 1906)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1825", "text": "<PERSON>, Peruvian general, twice President of Peru (d. 1901)", "html": "1825 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Peruvian general, twice <a href=\"https://wikipedia.org/wiki/President_of_Peru\" title=\"President of Peru\">President of Peru</a> (d. 1901)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Peruvian general, twice <a href=\"https://wikipedia.org/wiki/President_of_Peru\" title=\"President of Peru\">President of Peru</a> (d. 1901)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "President of Peru", "link": "https://wikipedia.org/wiki/President_of_Peru"}]}, {"year": "1835", "text": "<PERSON><PERSON>, American minister, theologian, and author (d. 1922)", "html": "1835 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American minister, theologian, and author (d. 1922)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American minister, theologian, and author (d. 1922)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1847", "text": "<PERSON>, French pianist and composer (d. 1903)", "html": "1847 - <a href=\"https://wikipedia.org/wiki/Augusta_Holm%C3%A8s\" title=\"<PERSON>\"><PERSON></a>, French pianist and composer (d. 1903)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Augusta_Holm%C3%A8s\" title=\"<PERSON>\"><PERSON></a>, French pianist and composer (d. 1903)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Augusta_Holm%C3%A8s"}]}, {"year": "1849", "text": "<PERSON>, Canadian activist and author (d. 1931)", "html": "1849 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian activist and author (d. 1931)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian activist and author (d. 1931)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1856", "text": "<PERSON><PERSON> <PERSON><PERSON>, English physicist and academic, Nobel Prize laureate (d. 1940)", "html": "1856 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> <PERSON><PERSON>\"><PERSON><PERSON> <PERSON><PERSON></a>, English physicist and academic, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Physics\" title=\"Nobel Prize in Physics\">Nobel Prize</a> laureate (d. 1940)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> <PERSON><PERSON>\"><PERSON><PERSON> <PERSON><PERSON></a>, English physicist and academic, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Physics\" title=\"Nobel Prize in Physics\">Nobel Prize</a> laureate (d. 1940)", "links": [{"title": "<PERSON><PERSON> <PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>"}, {"title": "Nobel Prize in Physics", "link": "https://wikipedia.org/wiki/Nobel_Prize_in_Physics"}]}, {"year": "1860", "text": "<PERSON>, American pianist and composer (d. 1908)", "html": "1860 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American pianist and composer (d. 1908)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American pianist and composer (d. 1908)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1861", "text": "<PERSON>, English composer and critic (d. 1924)", "html": "1861 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English composer and critic (d. 1924)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English composer and critic (d. 1924)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1863", "text": "Archduke <PERSON> of Austria (d. 1914)", "html": "1863 - <a href=\"https://wikipedia.org/wiki/Archduke_<PERSON>_<PERSON>_of_Austria\" title=\"Archduke <PERSON> of Austria\">Archduke <PERSON> of Austria</a> (d. 1914)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Archduke_<PERSON>_<PERSON>_of_Austria\" title=\"Archduke <PERSON> of Austria\">Archduke <PERSON> of Austria</a> (d. 1914)", "links": [{"title": "Archduke <PERSON> of Austria", "link": "https://wikipedia.org/wiki/Archdu<PERSON>_<PERSON>_<PERSON>_of_Austria"}]}, {"year": "1867", "text": "<PERSON><PERSON> <PERSON>, American polo player and horse breeder (d. 1941)", "html": "1867 - <a href=\"https://wikipedia.org/wiki/Foxhall_P._<PERSON>\" title=\"Foxhall P<PERSON> Keene\">Fox<PERSON> <PERSON></a>, American polo player and horse breeder (d. 1941)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Foxhall_P._<PERSON>\" title=\"Foxhall P. Keene\">Fox<PERSON> <PERSON></a>, American polo player and horse breeder (d. 1941)", "links": [{"title": "Foxhall P<PERSON>", "link": "https://wikipedia.org/wiki/Foxhall_P._Keene"}]}, {"year": "1869", "text": "<PERSON>, American painter and educator (d. 1965)", "html": "1869 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American painter and educator (d. 1965)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American painter and educator (d. 1965)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1870", "text": "<PERSON><PERSON>, British short story writer (d. 1916)", "html": "1870 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, British short story writer (d. 1916)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, British short story writer (d. 1916)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>ki"}]}, {"year": "1873", "text": "<PERSON>, American general and politician, 6th Governor-General of the Philippines (d. 1957)", "html": "1873 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American general and politician, 6th <a href=\"https://wikipedia.org/wiki/Governor-General_of_the_Philippines\" title=\"Governor-General of the Philippines\">Governor-General of the Philippines</a> (d. 1957)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American general and politician, 6th <a href=\"https://wikipedia.org/wiki/Governor-General_of_the_Philippines\" title=\"Governor-General of the Philippines\">Governor-General of the Philippines</a> (d. 1957)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "Governor-General of the Philippines", "link": "https://wikipedia.org/wiki/Governor-General_of_the_Philippines"}]}, {"year": "1875", "text": "<PERSON>, Irish-American hammer thrower (d. 1941)", "html": "1875 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Irish-American hammer thrower (d. 1941)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Irish-American hammer thrower (d. 1941)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1878", "text": "<PERSON>, Georgian-Russian marshal and politician,General Secretary of the Communist Party of the Soviet Union (d. 1953)", "html": "1878 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Georgian-Russian marshal and politician,<a href=\"https://wikipedia.org/wiki/General_Secretary_of_the_Communist_Party_of_the_Soviet_Union\" title=\"General Secretary of the Communist Party of the Soviet Union\">General Secretary of the Communist Party of the Soviet Union</a> (d. 1953)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Georgian-Russian marshal and politician,<a href=\"https://wikipedia.org/wiki/General_Secretary_of_the_Communist_Party_of_the_Soviet_Union\" title=\"General Secretary of the Communist Party of the Soviet Union\">General Secretary of the Communist Party of the Soviet Union</a> (d. 1953)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}, {"title": "General Secretary of the Communist Party of the Soviet Union", "link": "https://wikipedia.org/wiki/General_Secretary_of_the_Communist_Party_of_the_Soviet_Union"}]}, {"year": "1879", "text": "<PERSON>, Swiss-German painter and educator (d. 1940)", "html": "1879 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swiss-German painter and educator (d. 1940)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swiss-German painter and educator (d. 1940)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1882", "text": "<PERSON>, American-Argentinian engineer, designed the Salta-Antofagasta railway (d. 1950)", "html": "1882 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American-Argentinian engineer, designed the <a href=\"https://wikipedia.org/wiki/Salta%E2%80%93Antofagasta_railway\" title=\"Salta-Antofagasta railway\">Salta-Antofagasta railway</a> (d. 1950)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American-Argentinian engineer, designed the <a href=\"https://wikipedia.org/wiki/Salta%E2%80%93Antofagasta_railway\" title=\"Salta-Antofagasta railway\">Salta-Antofagasta railway</a> (d. 1950)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Salta-Antofagasta railway", "link": "https://wikipedia.org/wiki/Salta%E2%80%93Antofagasta_railway"}]}, {"year": "1884", "text": "<PERSON>, Czech pharmacologist, co-founded clinical pharmacology (d. 1942)", "html": "1884 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Czech pharmacologist, co-founded <a href=\"https://wikipedia.org/wiki/Clinical_pharmacology\" title=\"Clinical pharmacology\">clinical pharmacology</a> (d. 1942)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Czech pharmacologist, co-founded <a href=\"https://wikipedia.org/wiki/Clinical_pharmacology\" title=\"Clinical pharmacology\">clinical pharmacology</a> (d. 1942)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}, {"title": "Clinical pharmacology", "link": "https://wikipedia.org/wiki/Clinical_pharmacology"}]}, {"year": "1886", "text": "<PERSON>, American baseball player and manager (d. 1961)", "html": "1886 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player and manager (d. 1961)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player and manager (d. 1961)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1887", "text": "<PERSON><PERSON><PERSON><PERSON>, Indian actor, singer, and playwright (d. 1971)", "html": "1887 - <a href=\"https://wikipedia.org/wiki/B<PERSON>khar<PERSON>_Thakur\" title=\"Bhikhari Thakur\"><PERSON><PERSON><PERSON><PERSON></a>, Indian actor, singer, and playwright (d. 1971)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_Thakur\" title=\"B<PERSON>khar<PERSON> Thakur\"><PERSON><PERSON><PERSON><PERSON></a>, Indian actor, singer, and playwright (d. 1971)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>hakur"}]}, {"year": "1888", "text": "<PERSON>, English actress and singer (d. 1971)", "html": "1888 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actress and singer (d. 1971)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actress and singer (d. 1971)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1888", "text": "<PERSON>, American urban planner (d. 1981)", "html": "1888 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American urban planner (d. 1981)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American urban planner (d. 1981)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1890", "text": "<PERSON>, American engineer, invented FM radio (d. 1954)", "html": "1890 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American engineer, invented <a href=\"https://wikipedia.org/wiki/FM_radio\" class=\"mw-redirect\" title=\"FM radio\">FM radio</a> (d. 1954)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American engineer, invented <a href=\"https://wikipedia.org/wiki/FM_radio\" class=\"mw-redirect\" title=\"FM radio\">FM radio</a> (d. 1954)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "FM radio", "link": "https://wikipedia.org/wiki/FM_radio"}]}, {"year": "1896", "text": "<PERSON>, English colonel and cricketer (d. 1977)", "html": "1896 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(British_Army_officer)\" title=\"<PERSON> (British Army officer)\"><PERSON></a>, English colonel and cricketer (d. 1977)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(British_Army_officer)\" title=\"<PERSON> (British Army officer)\"><PERSON></a>, English colonel and cricketer (d. 1977)", "links": [{"title": "<PERSON> (British Army officer)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>(British_Army_officer)"}]}, {"year": "1897", "text": "<PERSON>, American pianist and composer (d. 1952)", "html": "1897 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American pianist and composer (d. 1952)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American pianist and composer (d. 1952)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1899", "text": "<PERSON>, Norwegian philosopher and author (d. 1990)", "html": "1899 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Norwegian philosopher and author (d. 1990)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Norwegian philosopher and author (d. 1990)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1904", "text": "<PERSON>, American director, producer, screenwriter, and cinematographer (d. 1975)", "html": "1904 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American director, producer, screenwriter, and cinematographer (d. 1975)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American director, producer, screenwriter, and cinematographer (d. 1975)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1907", "text": "<PERSON>, American race car driver (d. 1984)", "html": "1907 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American race car driver (d. 1984)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American race car driver (d. 1984)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1907", "text": "<PERSON>, American guitarist and educator (d. 2009)", "html": "1907 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American guitarist and educator (d. 2009)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American guitarist and educator (d. 2009)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1908", "text": "<PERSON>, English actress (d. 1982)", "html": "1908 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actress (d. 1982)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actress (d. 1982)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1908", "text": "<PERSON>, American geographer and explorer (d. 1969)", "html": "1908 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American geographer and explorer (d. 1969)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American geographer and explorer (d. 1969)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1910", "text": "<PERSON>, American author, playwright, and director (d. 1985)", "html": "1910 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author, playwright, and director (d. 1985)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author, playwright, and director (d. 1985)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1910", "text": "<PERSON>, New Zealand rugby player, cricketer, and umpire (d. 2010)", "html": "1910 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, New Zealand rugby player, cricketer, and umpire (d. 2010)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, New Zealand rugby player, cricketer, and umpire (d. 2010)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1911", "text": "<PERSON>, American-Greek actor, director, producer, and screenwriter (d. 2008)", "html": "1911 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American-Greek actor, director, producer, and screenwriter (d. 2008)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American-Greek actor, director, producer, and screenwriter (d. 2008)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1912", "text": "<PERSON>, Jr., American general and pilot (d. 2002)", "html": "1912 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>,_Jr.\" class=\"mw-redirect\" title=\"<PERSON>, Jr.\"><PERSON>, Jr.</a>, American general and pilot (d. 2002)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>,_Jr.\" class=\"mw-redirect\" title=\"<PERSON>, Jr.\"><PERSON>, Jr.</a>, American general and pilot (d. 2002)", "links": [{"title": "<PERSON>, Jr.", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>,_Jr."}]}, {"year": "1913", "text": "<PERSON>, American author and screenwriter (d. 1987)", "html": "1913 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author and screenwriter (d. 1987)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON> Bester\"><PERSON></a>, American author and screenwriter (d. 1987)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1913", "text": "<PERSON>, German politician, 4th Chancellor of Germany, Nobel Prize laureate (d. 1992)", "html": "1913 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German politician, 4th <a href=\"https://wikipedia.org/wiki/Chancellor_of_Germany_(Federal_Republic_of_Germany)\" class=\"mw-redirect\" title=\"Chancellor of Germany (Federal Republic of Germany)\">Chancellor of Germany</a>, <a href=\"https://wikipedia.org/wiki/Nobel_Peace_Prize\" title=\"Nobel Peace Prize\">Nobel Prize</a> laureate (d. 1992)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German politician, 4th <a href=\"https://wikipedia.org/wiki/Chancellor_of_Germany_(Federal_Republic_of_Germany)\" class=\"mw-redirect\" title=\"Chancellor of Germany (Federal Republic of Germany)\">Chancellor of Germany</a>, <a href=\"https://wikipedia.org/wiki/Nobel_Peace_Prize\" title=\"Nobel Peace Prize\">Nobel Prize</a> laureate (d. 1992)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Chancellor of Germany (Federal Republic of Germany)", "link": "https://wikipedia.org/wiki/Chancellor_of_Germany_(Federal_Republic_of_Germany)"}, {"title": "Nobel Peace Prize", "link": "https://wikipedia.org/wiki/Nobel_Peace_Prize"}]}, {"year": "1913", "text": "<PERSON>, American basketball player and coach (d. 2006)", "html": "1913 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player and coach (d. 2006)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player and coach (d. 2006)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1916", "text": "<PERSON>, Scottish-American trade union leader and academic (d. 2008)", "html": "1916 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish-American trade union leader and academic (d. 2008)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish-American trade union leader and academic (d. 2008)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1916", "text": "<PERSON>, American actress, singer, and dancer (d. 1973)", "html": "1916 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Grable\"><PERSON></a>, American actress, singer, and dancer (d. 1973)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_Grable\" title=\"<PERSON> Grable\"><PERSON></a>, American actress, singer, and dancer (d. 1973)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1917", "text": "<PERSON><PERSON>, American actor and activist (d. 2005)", "html": "1917 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American actor and activist (d. 2005)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American actor and activist (d. 2005)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1920", "text": "<PERSON>, American soldier and author (d. 2001)", "html": "1920 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(author)\" title=\"<PERSON> (author)\"><PERSON></a>, American soldier and author (d. 2001)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(author)\" title=\"<PERSON> (author)\"><PERSON></a>, American soldier and author (d. 2001)", "links": [{"title": "<PERSON> (author)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(author)"}]}, {"year": "1922", "text": "<PERSON>, American colonel, lawyer, and politician (d. 2012)", "html": "1922 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(American_politician)\" title=\"<PERSON> (American politician)\"><PERSON></a>, American colonel, lawyer, and politician (d. 2012)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(American_politician)\" title=\"<PERSON> (American politician)\"><PERSON></a>, American colonel, lawyer, and politician (d. 2012)", "links": [{"title": "<PERSON> (American politician)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(American_politician)"}]}, {"year": "1922", "text": "<PERSON>, American microbiologist (d. 2006)", "html": "1922 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American microbiologist (d. 2006)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American microbiologist (d. 2006)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1923", "text": "<PERSON>, Baron <PERSON>, English field marshal and politician, Lord Lieutenant of Greater London (d. 2019)", "html": "1923 - <a href=\"https://wikipedia.org/wiki/<PERSON>,_Baron_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>, Baron <PERSON>\"><PERSON>, Baron <PERSON></a>, English field marshal and politician, <a href=\"https://wikipedia.org/wiki/Lord_Lieutenant_of_Greater_London\" class=\"mw-redirect\" title=\"Lord Lieutenant of Greater London\">Lord Lieutenant of Greater London</a> (d. 2019)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>,_Baron_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>, Baron <PERSON>\"><PERSON>, Baron <PERSON></a>, English field marshal and politician, <a href=\"https://wikipedia.org/wiki/Lord_Lieutenant_of_Greater_London\" class=\"mw-redirect\" title=\"Lord Lieutenant of Greater London\">Lord Lieutenant of Greater London</a> (d. 2019)", "links": [{"title": "<PERSON>, Baron <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Baron_<PERSON>"}, {"title": "Lord Lieutenant of Greater London", "link": "https://wikipedia.org/wiki/Lord_Lieutenant_of_Greater_London"}]}, {"year": "1927", "text": "<PERSON>, American lawyer and politician, 66th United States Attorney General (d. 2021)", "html": "1927 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and politician, 66th <a href=\"https://wikipedia.org/wiki/United_States_Attorney_General\" title=\"United States Attorney General\">United States Attorney General</a> (d. 2021)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and politician, 66th <a href=\"https://wikipedia.org/wiki/United_States_Attorney_General\" title=\"United States Attorney General\">United States Attorney General</a> (d. 2021)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "United States Attorney General", "link": "https://wikipedia.org/wiki/United_States_Attorney_General"}]}, {"year": "1927", "text": "<PERSON><PERSON><PERSON><PERSON>, Canadian journalist and politician, 25th Governor General of Canada (d. 2009)", "html": "1927 - <a href=\"https://wikipedia.org/wiki/Rom%C3%A9o_LeBlanc\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Canadian journalist and politician, 25th <a href=\"https://wikipedia.org/wiki/Governor_General_of_Canada\" title=\"Governor General of Canada\">Governor General of Canada</a> (d. 2009)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Rom%C3%A9o_LeBlanc\" title=\"Rom<PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Canadian journalist and politician, 25th <a href=\"https://wikipedia.org/wiki/Governor_General_of_Canada\" title=\"Governor General of Canada\">Governor General of Canada</a> (d. 2009)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Rom%C3%A9o_LeBlanc"}, {"title": "Governor General of Canada", "link": "https://wikipedia.org/wiki/Governor_General_of_Canada"}]}, {"year": "1928", "text": "<PERSON>, Indian-English caliph and author (d. 2003)", "html": "1928 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Indian-English caliph and author (d. 2003)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Indian-English caliph and author (d. 2003)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "1928", "text": "<PERSON>, American tenor saxophonist (d. 2001)", "html": "1928 - <a href=\"https://wikipedia.org/wiki/Harold_<PERSON>\" title=\"Harold Land\"><PERSON></a>, American tenor saxophonist (d. 2001)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Harold_Land\" title=\"Harold Land\"><PERSON></a>, American tenor saxophonist (d. 2001)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Harold_<PERSON>"}]}, {"year": "1929", "text": "<PERSON><PERSON>, American baseball player (d. 2011)", "html": "1929 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_C<PERSON>li\" title=\"<PERSON><PERSON> Cimoli\"><PERSON><PERSON></a>, American baseball player (d. 2011)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_Cimoli\" title=\"<PERSON><PERSON> Cimoli\"><PERSON><PERSON></a>, American baseball player (d. 2011)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1929", "text": "<PERSON><PERSON><PERSON>, Polish cardinal (d. 2013)", "html": "1929 - <a href=\"https://wikipedia.org/wiki/J%C3%B3<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Polish cardinal (d. 2013)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/J%C3%B3<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Polish cardinal (d. 2013)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/J%C3%B3<PERSON>_<PERSON>p"}]}, {"year": "1930", "text": "<PERSON>, American baseball player (d. 2012)", "html": "1930 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player (d. 2012)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player (d. 2012)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1931", "text": "<PERSON>, American businessman and music publisher (d. 2009)", "html": "1931 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American businessman and music publisher (d. 2009)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American businessman and music publisher (d. 2009)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1931", "text": "<PERSON>, English historian and author (d. 2007)", "html": "1931 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English historian and author (d. 2007)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English historian and author (d. 2007)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1931", "text": "<PERSON>, American basketball player, coach, and executive (d. 2022)", "html": "1931 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player, coach, and executive (d. 2022)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player, coach, and executive (d. 2022)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1931", "text": "<PERSON>, American television host (d. 2014)", "html": "1931 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(television_host)\" title=\"<PERSON> (television host)\"><PERSON></a>, American television host (d. 2014)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(television_host)\" title=\"<PERSON> (television host)\"><PERSON></a>, American television host (d. 2014)", "links": [{"title": "<PERSON> (television host)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(television_host)"}]}, {"year": "1932", "text": "<PERSON><PERSON>, Australian rugby league player, coach, and businessman (d. 2021)", "html": "1932 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Australian rugby league player, coach, and businessman (d. 2021)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Australian rugby league player, coach, and businessman (d. 2021)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1932", "text": "<PERSON>, American actor, producer, and screenwriter (d. 2017)", "html": "1932 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(actor)\" title=\"<PERSON> (actor)\"><PERSON></a>, American actor, producer, and screenwriter (d. 2017)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(actor)\" title=\"<PERSON> (actor)\"><PERSON></a>, American actor, producer, and screenwriter (d. 2017)", "links": [{"title": "<PERSON> (actor)", "link": "https://wikipedia.org/wiki/<PERSON>_(actor)"}]}, {"year": "1933", "text": "<PERSON><PERSON><PERSON>, American blues singer and guitarist (d. 2017)", "html": "1933 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American blues singer and guitarist (d. 2017)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American blues singer and guitarist (d. 2017)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1934", "text": "<PERSON>, Belgian-American businessman, founded Glencore (d. 2013)", "html": "1934 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Belgian-American businessman, founded <a href=\"https://wikipedia.org/wiki/Glencore\" title=\"Glencore\">Glencore</a> (d. 2013)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Belgian-American businessman, founded <a href=\"https://wikipedia.org/wiki/Glencore\" title=\"Glencore\">Glencore</a> (d. 2013)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Glencore", "link": "https://wikipedia.org/wiki/Glencore"}]}, {"year": "1934", "text": "<PERSON>, Russian colonel, engineer, and cosmonaut", "html": "1934 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian colonel, engineer, and cosmonaut", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian colonel, engineer, and cosmonaut", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1935", "text": "<PERSON>, English actress (d. 2017)", "html": "1935 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actress (d. 2017)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actress (d. 2017)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1935", "text": "<PERSON>, French-American chef and author", "html": "1935 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A9pin\" title=\"<PERSON>\"><PERSON></a>, French-American chef and author", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A9pin\" title=\"<PERSON>\"><PERSON></a>, French-American chef and author", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Jacques_P%C3%A9pin"}]}, {"year": "1936", "text": "<PERSON>, English rugby player and wrestler (d. 1987)", "html": "1936 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English rugby player and wrestler (d. 1987)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English rugby player and wrestler (d. 1987)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1937", "text": "<PERSON>, American politician (d. 1990)", "html": "1937 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American politician (d. 1990)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American politician (d. 1990)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1938", "text": "<PERSON><PERSON>, English bass player and producer (d. 1996)", "html": "1938 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, English bass player and producer (d. 1996)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, English bass player and producer (d. 1996)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1938", "text": "<PERSON>, American songwriter and composer (d. 2005)", "html": "1938 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American songwriter and composer (d. 2005)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American songwriter and composer (d. 2005)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1939", "text": "<PERSON>, Nicaraguan footballer (d. 2018)", "html": "1939 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%B3n\" title=\"<PERSON>\"><PERSON></a>, Nicaraguan footballer (d. 2018)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%B3n\" title=\"<PERSON>\"><PERSON></a>, Nicaraguan footballer (d. 2018)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%B3n"}]}, {"year": "1939", "text": "<PERSON>, English author and songwriter", "html": "1939 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English author and songwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English author and songwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1939", "text": "<PERSON>, American biologist and academic, Nobel Prize laureate", "html": "1939 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American biologist and academic, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Physiology_or_Medicine\" title=\"Nobel Prize in Physiology or Medicine\">Nobel Prize</a> laureate", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American biologist and academic, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Physiology_or_Medicine\" title=\"Nobel Prize in Physiology or Medicine\">Nobel Prize</a> laureate", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_Varmus"}, {"title": "Nobel Prize in Physiology or Medicine", "link": "https://wikipedia.org/wiki/Nobel_Prize_in_Physiology_or_Medicine"}]}, {"year": "1940", "text": "<PERSON><PERSON>, Italian football manager (d. 2023)", "html": "1940 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Italian football manager (d. 2023)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Italian football manager (d. 2023)", "links": [{"title": "Ilario <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1940", "text": "<PERSON>, English sprinter and hurdler (d. 1974)", "html": "1940 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(athlete)\" class=\"mw-redirect\" title=\"<PERSON> (athlete)\"><PERSON></a>, English sprinter and hurdler (d. 1974)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(athlete)\" class=\"mw-redirect\" title=\"<PERSON> (athlete)\"><PERSON></a>, English sprinter and hurdler (d. 1974)", "links": [{"title": "<PERSON> (athlete)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>(athlete)"}]}, {"year": "1941", "text": "<PERSON>, American singer-songwriter and guitarist  (d. 2015)", "html": "1941 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and guitarist (d. 2015)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and guitarist (d. 2015)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1941", "text": "<PERSON><PERSON><PERSON>, American trumpet player and composer", "html": "1941 - <a href=\"https://wikipedia.org/wiki/Wada<PERSON>_<PERSON>_<PERSON>\" title=\"Wadada <PERSON>\">W<PERSON><PERSON> <PERSON></a>, American trumpet player and composer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Wada<PERSON>_<PERSON>_<PERSON>\" title=\"Wadada <PERSON>\">W<PERSON><PERSON> <PERSON></a>, American trumpet player and composer", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Wada<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1941", "text": "<PERSON>, American historian, author, and academic", "html": "1941 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American historian, author, and academic", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American historian, author, and academic", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1942", "text": "<PERSON><PERSON>, American mathematician and academic", "html": "1942 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American mathematician and academic", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American mathematician and academic", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1942", "text": "<PERSON>, Australian rugby league player (d. 2022)", "html": "1942 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(rugby_league)\" title=\"<PERSON> (rugby league)\"><PERSON></a>, Australian rugby league player (d. 2022)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(rugby_league)\" title=\"<PERSON> (rugby league)\"><PERSON></a>, Australian rugby league player (d. 2022)", "links": [{"title": "<PERSON> (rugby league)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(rugby_league)"}]}, {"year": "1943", "text": "<PERSON>, American saxophone player (d. 2014)", "html": "1943 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American saxophone player (d. 2014)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Keys\"><PERSON></a>, American saxophone player (d. 2014)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1943", "text": "<PERSON>, English musician", "html": "1943 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English musician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English musician", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1943", "text": "<PERSON>, American director and screenwriter", "html": "1943 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American director and screenwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American director and screenwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1944", "text": "<PERSON><PERSON><PERSON>-<PERSON>, English trumpet player and educator", "html": "1944 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>-<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, English trumpet player and educator", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>-<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, English trumpet player and educator", "links": [{"title": "<PERSON><PERSON><PERSON>-<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>-<PERSON>"}]}, {"year": "1945", "text": "<PERSON>, Canadian ice hockey player and coach", "html": "1945 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player and coach", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1946", "text": "<PERSON>, South African activist, founded the Black Consciousness Movement (d. 1977)", "html": "1946 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, South African activist, founded the <a href=\"https://wikipedia.org/wiki/Black_Consciousness_Movement\" title=\"Black Consciousness Movement\">Black Consciousness Movement</a> (d. 1977)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, South African activist, founded the <a href=\"https://wikipedia.org/wiki/Black_Consciousness_Movement\" title=\"Black Consciousness Movement\">Black Consciousness Movement</a> (d. 1977)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Black Consciousness Movement", "link": "https://wikipedia.org/wiki/Black_Consciousness_Movement"}]}, {"year": "1946", "text": "<PERSON>, American director, producer, and screenwriter, co-founded DreamWorks", "html": "1946 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American director, producer, and screenwriter, co-founded <a href=\"https://wikipedia.org/wiki/DreamWorks_Pictures\" title=\"DreamWorks Pictures\">DreamWorks</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American director, producer, and screenwriter, co-founded <a href=\"https://wikipedia.org/wiki/DreamWorks_Pictures\" title=\"DreamWorks Pictures\">DreamWorks</a>", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}, {"title": "DreamWorks Pictures", "link": "https://wikipedia.org/wiki/DreamWorks_Pictures"}]}, {"year": "1947", "text": "<PERSON><PERSON>, Russian author and screenwriter", "html": "1947 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Russian author and screenwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Russian author and screenwriter", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1948", "text": "<PERSON>, American basketball player", "html": "1948 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "1948", "text": "<PERSON>, English singer-songwriter and guitarist", "html": "1948 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(musician)\" title=\"<PERSON> (musician)\"><PERSON></a>, English singer-songwriter and guitarist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(musician)\" title=\"<PERSON> (musician)\"><PERSON></a>, English singer-songwriter and guitarist", "links": [{"title": "<PERSON> (musician)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>(musician)"}]}, {"year": "1948", "text": "<PERSON><PERSON><PERSON>, Italian sculptor and painter", "html": "1948 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Italian sculptor and painter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Italian sculptor and painter", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1948", "text": "<PERSON>, French-English singer-songwriter and guitarist", "html": "1948 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French-English singer-songwriter and guitarist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French-English singer-songwriter and guitarist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1949", "text": "<PERSON>, American volcanologist and geologist (d. 1980)", "html": "1949 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American volcanologist and geologist (d. 1980)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American volcanologist and geologist (d. 1980)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "1950", "text": "<PERSON>, Australian director, producer, and screenwriter", "html": "1950 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian director, producer, and screenwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian director, producer, and screenwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1950", "text": "<PERSON>, American drummer and songwriter (d. 2002)", "html": "1950 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American drummer and songwriter (d. 2002)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American drummer and songwriter (d. 2002)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1950", "text": "<PERSON><PERSON>, Sri Lankan general and politician", "html": "1950 - <a href=\"https://wikipedia.org/wiki/Sara<PERSON>_<PERSON>\" title=\"Sarath Fonseka\"><PERSON><PERSON></a>, Sri Lankan general and politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Sara<PERSON>_<PERSON>\" title=\"Sarath Fonseka\"><PERSON><PERSON></a>, Sri Lankan general and politician", "links": [{"title": "<PERSON>th Fonseka", "link": "https://wikipedia.org/wiki/Sarath_Fonseka"}]}, {"year": "1950", "text": "<PERSON><PERSON>, Mexican wrestler (d. 2015)", "html": "1950 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Mexican wrestler (d. 2015)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Mexican wrestler (d. 2015)", "links": [{"title": "Lizmark", "link": "https://wikipedia.org/wiki/Liz<PERSON>"}]}, {"year": "1950", "text": "<PERSON>, American historian, author, and critic", "html": "1950 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American historian, author, and critic", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American historian, author, and critic", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1951", "text": "<PERSON>, American basketball player", "html": "1951 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(basketball,_born_1951)\" title=\"<PERSON> (basketball, born 1951)\"><PERSON></a>, American basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(basketball,_born_1951)\" title=\"<PERSON> (basketball, born 1951)\"><PERSON></a>, American basketball player", "links": [{"title": "<PERSON> (basketball, born 1951)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(basketball,_born_1951)"}]}, {"year": "1952", "text": "<PERSON>, American songwriter and producer", "html": "1952 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American songwriter and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American songwriter and producer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1953", "text": "<PERSON>, English footballer (d. 2018)", "html": "1953 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English footballer (d. 2018)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English footballer (d. 2018)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1953", "text": "<PERSON>, American guitarist and singer", "html": "1953 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American guitarist and singer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Easton\"><PERSON></a>, American guitarist and singer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1954", "text": "<PERSON>, English race car driver", "html": "1954 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(motor_racing)\" title=\"<PERSON> (motor racing)\"><PERSON></a>, English race car driver", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(motor_racing)\" title=\"<PERSON> (motor racing)\"><PERSON></a>, English race car driver", "links": [{"title": "<PERSON> (motor racing)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(motor_racing)"}]}, {"year": "1954", "text": "<PERSON>, American actor (d. 2022)", "html": "1954 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor (d. 2022)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor (d. 2022)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1954", "text": "<PERSON><PERSON>, German runner", "html": "1954 - <a href=\"https://wikipedia.org/wiki/Willi_W%C3%BClbeck\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, German runner", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Willi_W%C3%<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, German runner", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Willi_W%C3%BClbeck"}]}, {"year": "1955", "text": "<PERSON>, Indian businessman and politician", "html": "1955 - <a href=\"https://wikipedia.org/wiki/Vijay_Mallya\" title=\"Vijay Mallya\"><PERSON></a>, Indian businessman and politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Vijay_Mallya\" title=\"Vijay Mallya\"><PERSON></a>, Indian businessman and politician", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Vijay_Mallya"}]}, {"year": "1955", "text": "<PERSON><PERSON><PERSON>, Polish runner", "html": "1955 - <a href=\"https://wikipedia.org/wiki/Bogus%C5%82aw_Mami%C5%84ski\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Polish runner", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Bogus%C5%82aw_Mami%C5%84ski\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Polish runner", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Bogus%C5%82aw_Mami%C5%84ski"}]}, {"year": "1956", "text": "<PERSON>, American comedian", "html": "1956 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American comedian", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American comedian", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1957", "text": "<PERSON>, English astrologer and author (d. 2016)", "html": "1957 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English astrologer and author (d. 2016)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English astrologer and author (d. 2016)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1958", "text": "<PERSON><PERSON><PERSON>, English guitarist (d. 2023)", "html": "1958 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, English guitarist (d. 2023)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, English guitarist (d. 2023)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1958", "text": "<PERSON>, American composer and educator", "html": "1958 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American composer and educator", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American composer and educator", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1960", "text": "<PERSON><PERSON><PERSON><PERSON>, Japanese economist and academic", "html": "1960 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>ek<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>ek<PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Japanese economist and academic", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>ek<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>ek<PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Japanese economist and academic", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1960", "text": "<PERSON><PERSON>, Japanese singer, guitarist and composer", "html": "1960 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Japanese singer, guitarist and composer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Japanese singer, guitarist and composer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>o"}]}, {"year": "1961", "text": "<PERSON>, Canadian figure skater and coach", "html": "1961 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian figure skater and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian figure skater and coach", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1961", "text": "<PERSON><PERSON><PERSON>, Indian cricketer", "html": "1961 - <a href=\"https://wikipedia.org/wiki/Lalchan<PERSON>_Rajput\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Indian cricketer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_Rajput\" title=\"<PERSON><PERSON><PERSON> Rajput\"><PERSON><PERSON><PERSON></a>, Indian cricketer", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Lal<PERSON><PERSON>_<PERSON>put"}]}, {"year": "1961", "text": "<PERSON><PERSON>, American singer, producer, author, and poet", "html": "1961 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American singer, producer, author, and poet", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American singer, producer, author, and poet", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1961", "text": "<PERSON>, American singer, songwriter, and actress (d. 2025)", "html": "1961 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer, songwriter, and actress (d. 2025)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Stone\"><PERSON></a>, American singer, songwriter, and actress (d. 2025)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1963", "text": "<PERSON>, American drummer", "html": "1963 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%27Angelo\" title=\"<PERSON>\"><PERSON></a>, American drummer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%27Ange<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American drummer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>%27Angelo"}]}, {"year": "1963", "text": "<PERSON>, American football player and coach", "html": "1963 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player and coach", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1963", "text": "<PERSON>, Burundian soldier and politician, 9th President of Burundi (d. 2020)", "html": "1963 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Burundian soldier and politician, 9th <a href=\"https://wikipedia.org/wiki/President_of_Burundi\" title=\"President of Burundi\">President of Burundi</a> (d. 2020)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Burundian soldier and politician, 9th <a href=\"https://wikipedia.org/wiki/President_of_Burundi\" title=\"President of Burundi\">President of Burundi</a> (d. 2020)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "President of Burundi", "link": "https://wikipedia.org/wiki/President_of_Burundi"}]}, {"year": "1963", "text": "<PERSON>, American basketball player and coach", "html": "1963 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player and coach", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1963", "text": "<PERSON>, American actor and producer", "html": "1963 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor and producer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1964", "text": "<PERSON>, American wrestler and producer", "html": "1964 - <a href=\"https://wikipedia.org/wiki/Stone_Cold_<PERSON>_<PERSON>\" title=\"Stone Cold Steve Austin\">Stone Cold <PERSON></a>, American wrestler and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_Cold_<PERSON>_<PERSON>\" title=\"Stone Cold Steve Austin\">Stone Cold <PERSON></a>, American wrestler and producer", "links": [{"title": "<PERSON> Cold <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1964", "text": "<PERSON>, American football player and coach", "html": "1964 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player and coach", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1965", "text": "<PERSON>, American actor, director, and screenwriter", "html": "1965 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor, director, and screenwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor, director, and screenwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1965", "text": "<PERSON><PERSON>, Spanish footballer (d. 2012)", "html": "1965 - <a href=\"https://wikipedia.org/wiki/Manolo_Pe%C3%B1a\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Spanish footballer (d. 2012)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Manolo_Pe%C3%B1a\" title=\"Man<PERSON> Peña\"><PERSON><PERSON></a>, Spanish footballer (d. 2012)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Manolo_Pe%C3%B1a"}]}, {"year": "1966", "text": "<PERSON><PERSON><PERSON><PERSON>, Italian footballer and sportscaster", "html": "1966 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Italian footballer and sportscaster", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Italian footballer and sportscaster", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1967", "text": "<PERSON><PERSON>, German singer-songwriter and guitarist", "html": "1967 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, German singer-songwriter and guitarist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, German singer-songwriter and guitarist", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1967", "text": "<PERSON><PERSON>, Dutch journalist", "html": "1967 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Dutch journalist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Dutch journalist", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1968", "text": "<PERSON>, German footballer and manager", "html": "1968 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German footballer and manager", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German footballer and manager", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1968", "text": "<PERSON>, Australian actress", "html": "1968 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian actress", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1968", "text": "<PERSON>, Spanish singer-songwriter and guitarist", "html": "1968 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Spanish singer-songwriter and guitarist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Spanish singer-songwriter and guitarist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1968", "text": "<PERSON>, American actor and producer", "html": "1968 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor and producer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1969", "text": "<PERSON>, Spanish footballer", "html": "1969 - <a href=\"https://wikipedia.org/wiki/Santiago_Ca%C3%B1izares\" title=\"Santiago Cañizares\">Santiago Cañizares</a>, Spanish footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Santiago_Ca%C3%B1izares\" title=\"Santiago Cañizares\">Santiago Cañizares</a>, Spanish footballer", "links": [{"title": "Santiago Cañizares", "link": "https://wikipedia.org/wiki/Santiago_Ca%C3%B1izares"}]}, {"year": "1969", "text": "<PERSON>, English footballer and manager (d. 2019)", "html": "1969 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English footballer and manager (d. 2019)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Justin_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English footballer and manager (d. 2019)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Justin_<PERSON>"}]}, {"year": "1969", "text": "<PERSON>, Japanese race car driver", "html": "1969 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Japanese race car driver", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Japanese race car driver", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>"}]}, {"year": "1970", "text": "<PERSON>, American singer and guitarist", "html": "1970 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(guitarist)\" title=\"<PERSON> (guitarist)\"><PERSON></a>, American singer and guitarist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(guitarist)\" title=\"<PERSON> (guitarist)\"><PERSON></a>, American singer and guitarist", "links": [{"title": "<PERSON> (guitarist)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>(guitarist)"}]}, {"year": "1970", "text": "<PERSON><PERSON>, American rapper and actor (d. 2021)", "html": "1970 - <a href=\"https://wikipedia.org/wiki/DMX\" title=\"DMX\"><PERSON><PERSON></a>, American rapper and actor (d. 2021)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/DMX\" title=\"DMX\"><PERSON><PERSON></a>, American rapper and actor (d. 2021)", "links": [{"title": "DMX", "link": "https://wikipedia.org/wiki/DMX"}]}, {"year": "1970", "text": "<PERSON><PERSON>, American basketball player", "html": "1970 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American basketball player", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1970", "text": "<PERSON><PERSON><PERSON>, Greek singer-songwriter", "html": "1970 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Greek singer-songwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Greek singer-songwriter", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1970", "text": "<PERSON>, American wrestler", "html": "1970 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American wrestler", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American wrestler", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Rob_<PERSON>_Dam"}]}, {"year": "1970", "text": "<PERSON>, English painter", "html": "1970 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English painter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English painter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1971", "text": "<PERSON><PERSON>, Indian journalist", "html": "1971 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"Barkha Dutt\"><PERSON><PERSON></a>, Indian journalist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Bar<PERSON>_Dutt\" title=\"Bar<PERSON> Dutt\"><PERSON><PERSON></a>, Indian journalist", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1971", "text": "<PERSON><PERSON>, Japanese pianist and composer", "html": "1971 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Japanese pianist and composer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Japanese pianist and composer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1971", "text": "<PERSON><PERSON><PERSON>, Spanish tennis player and sportscaster", "html": "1971 - <a href=\"https://wikipedia.org/wiki/Arantxa_S%C3%A1nchez_Vicario\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Spanish tennis player and sportscaster", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Arantxa_S%C3%A1nchez_Vicario\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Spanish tennis player and sportscaster", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Arantxa_S%C3%A1nchez_Vicario"}]}, {"year": "1972", "text": "<PERSON><PERSON><PERSON><PERSON>, Ukrainian pole vaulter", "html": "1972 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Ukrainian pole vaulter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Ukrainian pole vaulter", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1972", "text": "<PERSON>, American drummer and songwriter", "html": "1972 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American drummer and songwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American drummer and songwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1972", "text": "<PERSON> <PERSON><PERSON>, Latvian-American musician", "html": "1972 - <a href=\"https://wikipedia.org/wiki/DJ_<PERSON><PERSON>\" title=\"DJ Lethal\">DJ <PERSON></a>, Latvian-American musician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/DJ_<PERSON><PERSON>\" title=\"DJ Lethal\">DJ <PERSON><PERSON></a>, Latvian-American musician", "links": [{"title": "DJ <PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>"}]}, {"year": "1972", "text": "<PERSON>, Singaporean civilist and politician, 4th Prime Minister of Singapore", "html": "1972 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Singaporean civilist and politician, 4th <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Singapore\" title=\"Prime Minister of Singapore\">Prime Minister of Singapore</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Singaporean civilist and politician, 4th <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Singapore\" title=\"Prime Minister of Singapore\">Prime Minister of Singapore</a>", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}, {"title": "Prime Minister of Singapore", "link": "https://wikipedia.org/wiki/Prime_Minister_of_Singapore"}]}, {"year": "1973", "text": "<PERSON><PERSON>, Ethiopian runner", "html": "1973 - <a href=\"https://wikipedia.org/wiki/Fatuma_Roba\" title=\"Fatuma Roba\"><PERSON><PERSON></a>, Ethiopian runner", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Fatuma_Roba\" title=\"Fatuma Roba\"><PERSON><PERSON></a>, Ethiopian runner", "links": [{"title": "Fatuma Roba", "link": "https://wikipedia.org/wiki/Fatuma_Roba"}]}, {"year": "1974", "text": "<PERSON>, American football player and politician", "html": "1974 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player and politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player and politician", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1974", "text": "<PERSON><PERSON>, Norwegian singer, guitarist, and producer", "html": "1974 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>einer\" title=\"<PERSON><PERSON>einer\"><PERSON><PERSON></a>, Norwegian singer, guitarist, and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>einer\" title=\"<PERSON><PERSON>einer\"><PERSON><PERSON></a>, Norwegian singer, guitarist, and producer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>er"}]}, {"year": "1975", "text": "<PERSON>, American singer-songwriter and guitarist", "html": "1975 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and guitarist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and guitarist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1975", "text": "<PERSON><PERSON>, Australian singer-songwriter", "html": "1975 - <a href=\"https://wikipedia.org/wiki/Sia\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Australian singer-songwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Sia\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Australian singer-songwriter", "links": [{"title": "Sia", "link": "https://wikipedia.org/wiki/Sia"}]}, {"year": "1975", "text": "<PERSON><PERSON>, Canadian wrestler and actress", "html": "1975 - <a href=\"https://wikipedia.org/wiki/Trish_Stratus\" title=\"<PERSON>sh Stratus\"><PERSON><PERSON></a>, Canadian wrestler and actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Tri<PERSON>_Stratus\" title=\"<PERSON>sh Stratus\"><PERSON><PERSON></a>, Canadian wrestler and actress", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Trish_Stratus"}]}, {"year": "1977", "text": "<PERSON><PERSON><PERSON>, Swedish DJ, record producer, member of Swedish House Mafia", "html": "1977 - <a href=\"https://wikipedia.org/wiki/Axwell\" title=\"Axwell\"><PERSON><PERSON><PERSON></a>, Swedish DJ, record producer, member of <a href=\"https://wikipedia.org/wiki/Swedish_House_Mafia\" title=\"Swedish House Mafia\">Swedish House Mafia</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Axwell\" title=\"Axwell\"><PERSON><PERSON><PERSON></a>, Swedish DJ, record producer, member of <a href=\"https://wikipedia.org/wiki/Swedish_House_Mafia\" title=\"Swedish House Mafia\">Swedish House Mafia</a>", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Axwell"}, {"title": "Swedish House Mafia", "link": "https://wikipedia.org/wiki/Swedish_House_Mafia"}]}, {"year": "1977", "text": "<PERSON>, German runner", "html": "1977 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German runner", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German runner", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1978", "text": "<PERSON>, Canadian ice hockey player ", "html": "1978 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player ", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player ", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1978", "text": "<PERSON>, American soccer player", "html": "1978 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American soccer player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American soccer player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1978", "text": "<PERSON>, American actor", "html": "1978 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1978", "text": "<PERSON>, American actress", "html": "1978 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1980", "text": "<PERSON>, American singer-songwriter, producer, and actress", "html": "1980 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter, producer, and actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter, producer, and actress", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1980", "text": "<PERSON>, English actor and basketball player, one of the tallest 25 men in the world (d. 2017)", "html": "1980 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actor and basketball player, one of the tallest 25 men in the world (d. 2017)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actor and basketball player, one of the tallest 25 men in the world (d. 2017)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1980", "text": "<PERSON>, American football player", "html": "1980 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, American football player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, American football player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1983", "text": "<PERSON>, Canadian football player", "html": "1983 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian football player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian football player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1984", "text": "<PERSON>, American ice hockey player", "html": "1984 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American ice hockey player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American ice hockey player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1984", "text": "<PERSON>, English footballer", "html": "1984 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(footballer,_born_1984)\" title=\"<PERSON> (footballer, born 1984)\"><PERSON></a>, English footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(footballer,_born_1984)\" title=\"<PERSON> (footballer, born 1984)\"><PERSON></a>, English footballer", "links": [{"title": "<PERSON> (footballer, born 1984)", "link": "https://wikipedia.org/wiki/<PERSON>(footballer,_born_1984)"}]}, {"year": "1984", "text": "<PERSON><PERSON><PERSON><PERSON>, Italian skier", "html": "1984 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Italian skier", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Italian skier", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1984", "text": "<PERSON>, American bass player and singer", "html": "1984 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American bass player and singer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American bass player and singer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1986", "text": "<PERSON>, American baseball player", "html": "1986 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(infielder)\" title=\"<PERSON> (infielder)\"><PERSON></a>, American baseball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(infielder)\" title=\"<PERSON> (infielder)\"><PERSON></a>, American baseball player", "links": [{"title": "<PERSON> (infielder)", "link": "https://wikipedia.org/wiki/<PERSON>_(infielder)"}]}, {"year": "1986", "text": "<PERSON>, Canadian speed skater", "html": "1986 - <a href=\"https://wikipedia.org/wiki/Fran%C3%A<PERSON><PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian speed skater", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Fran%C3%A<PERSON><PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian speed skater", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Fran%C3%A7<PERSON>_<PERSON><PERSON>"}]}, {"year": "1986", "text": "<PERSON><PERSON>, Pakistani-Australian cricketer", "html": "1986 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"Us<PERSON> Khawaja\"><PERSON><PERSON></a>, Pakistani-Australian cricketer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"Us<PERSON> Khawaja\"><PERSON><PERSON></a>, Pakistani-Australian cricketer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1987", "text": "<PERSON><PERSON>, Japanese figure skater", "html": "1987 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Japanese figure skater", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Japanese figure skater", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>"}]}, {"year": "1988", "text": "<PERSON>, English cyclist", "html": "1988 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English cyclist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English cyclist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1988", "text": "<PERSON>, American football player", "html": "1988 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1988", "text": "<PERSON><PERSON>, Canadian heptathlete", "html": "1988 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Canadian heptathlete", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Canadian heptathlete", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>isen-<PERSON>"}]}, {"year": "1988", "text": "<PERSON><PERSON>, Pakistani cricketer", "html": "1988 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Pakistani cricketer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Pakistani cricketer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1989", "text": "<PERSON>, American actress and singer", "html": "1989 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress and singer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress and singer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1990", "text": "<PERSON>, Swedish ice hockey player", "html": "1990 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swedish ice hockey player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swedish ice hockey player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1990", "text": "<PERSON>, American singer-songwriter", "html": "1990 - <a href=\"https://wikipedia.org/wiki/Sierra_Kay\" title=\"Sierra Kay\"><PERSON></a>, American singer-songwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Sierra_Kay\" title=\"Sierra Kay\"><PERSON></a>, American singer-songwriter", "links": [{"title": "Sierra Kay", "link": "https://wikipedia.org/wiki/Sierra_Kay"}]}, {"year": "1991", "text": "<PERSON>, English model and YouTuber", "html": "1991 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English model and YouTuber", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English model and YouTuber", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1992", "text": "<PERSON>, American shot putter", "html": "1992 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American shot putter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American shot putter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1992", "text": "<PERSON><PERSON><PERSON>, American singer-songwriter and actress", "html": "1992 - <a href=\"https://wikipedia.org/wiki/Bridgit_Mendler\" title=\"Bridgit Mendler\"><PERSON><PERSON><PERSON>dler</a>, American singer-songwriter and actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Bridgit_Mendler\" title=\"Bridgit Mendler\"><PERSON><PERSON><PERSON>dler</a>, American singer-songwriter and actress", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Brid<PERSON>_Mendler"}]}, {"year": "1993", "text": "<PERSON>, American baseball player", "html": "1993 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1993", "text": "<PERSON>, Finnish footballer", "html": "1993 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Finnish footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Finnish footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1994", "text": "<PERSON>, Spanish footballer", "html": "1994 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Spanish footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Spanish footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1994", "text": "<PERSON><PERSON><PERSON>, American-Austrian singer", "html": "1994 - <a href=\"https://wikipedia.org/wiki/Nat%C3%<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American-Austrian singer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Nat%C3%<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American-Austrian singer", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Nat%C3%A1<PERSON>_Kelly"}]}, {"year": "1995", "text": "<PERSON><PERSON><PERSON>, Czech tennis player", "html": "1995 - <a href=\"https://wikipedia.org/wiki/Barbora_Krej%C4%8D%C3%ADkov%C3%A1\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Czech tennis player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Barbora_Krej%C4%8D%C3%ADkov%C3%A1\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Czech tennis player", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Barbora_Krej%C4%8D%C3%ADkov%C3%A1"}]}, {"year": "1997", "text": "<PERSON>, Venezuelan baseball player", "html": "1997 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%B1a_Jr.\" title=\"<PERSON>\"><PERSON>.</a>, Venezuelan baseball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%B1a_Jr.\" title=\"<PERSON>\"><PERSON>.</a>, Venezuelan baseball player", "links": [{"title": "<PERSON>.", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>%C3%B1a_<PERSON>."}]}, {"year": "1997", "text": "<PERSON>, American ice hockey player", "html": "1997 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American ice hockey player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American ice hockey player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_DeB<PERSON>cat"}]}, {"year": "2000", "text": "<PERSON><PERSON>, American football player", "html": "2000 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American football player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American football player", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "2000", "text": "<PERSON><PERSON><PERSON>, Thai actor and singer", "html": "2000 - <a href=\"https://wikipedia.org/wiki/Korapat_Kirdpan\" title=\"Korapat Kirdpan\"><PERSON><PERSON><PERSON> Ki<PERSON>pan</a>, Thai actor and singer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Korapat_Kirdpan\" title=\"Korapat Kirdpan\"><PERSON><PERSON><PERSON></a>, Thai actor and singer", "links": [{"title": "Korapat Ki<PERSON>pan", "link": "https://wikipedia.org/wiki/Korapat_Kirdpan"}]}, {"year": "2000", "text": "<PERSON><PERSON><PERSON>, American football player", "html": "2000 - <a href=\"https://wikipedia.org/wiki/<PERSON>ra<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American football player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>ra<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American football player", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>ra<PERSON>_<PERSON>"}]}, {"year": "2001", "text": "<PERSON>, American singer", "html": "2001 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2001", "text": "<PERSON><PERSON><PERSON>, American basketball player", "html": "2001 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American basketball player", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "2002", "text": "<PERSON><PERSON><PERSON><PERSON>, Argentine footballer", "html": "2002 - <a href=\"https://wikipedia.org/wiki/Giu<PERSON><PERSON>_Si<PERSON>one\" title=\"<PERSON><PERSON><PERSON>o Si<PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Argentine footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Argentine footballer", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/G<PERSON><PERSON><PERSON>_Si<PERSON>one"}]}], "Deaths": [{"year": "919", "text": "<PERSON>, wife of <PERSON><PERSON> (b. 858)", "html": "919 - <a href=\"https://wikipedia.org/wiki/Lady_<PERSON>_(<PERSON><PERSON>_<PERSON>%27s_wife)\" title=\"Lady <PERSON> (<PERSON><PERSON>'s wife)\">Lady <PERSON></a>, wife of <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a> (b. 858)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Lady_<PERSON>_(<PERSON><PERSON>_<PERSON>%27s_wife)\" title=\"Lady <PERSON> (<PERSON><PERSON>'s wife)\">Lady <PERSON></a>, wife of <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a> (b. 858)", "links": [{"title": "<PERSON> (<PERSON><PERSON>'s wife)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(<PERSON><PERSON>_<PERSON>%27s_wife)"}, {"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "933", "text": "<PERSON><PERSON>, Chinese empress dowager", "html": "933 - <a href=\"https://wikipedia.org/wiki/Empress_Dow<PERSON>_Yaonian_Yanmujin\" class=\"mw-redirect\" title=\"Empress Dowager Yaonian Yanmujin\">Yaonian Yanmujin</a>, Chinese <a href=\"https://wikipedia.org/wiki/Empress_dowager\" title=\"Empress dowager\">empress dowager</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Empress_Dow<PERSON>_Yaonian_Yanmujin\" class=\"mw-redirect\" title=\"Empress Dowager Yaonian Yanmujin\">Yaonian Yanmujin</a>, Chinese <a href=\"https://wikipedia.org/wiki/Empress_dowager\" title=\"Empress dowager\">empress dowager</a>", "links": [{"title": "Empress <PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Empress_<PERSON><PERSON>_Yao<PERSON>_Yanmujin"}, {"title": "Empress dowager", "link": "https://wikipedia.org/wiki/Empress_dowager"}]}, {"year": "1075", "text": "<PERSON> of Wessex (b. 1025)", "html": "1075 - <a href=\"https://wikipedia.org/wiki/<PERSON>_of_Wessex\" title=\"<PERSON> of Wessex\"><PERSON> of Wessex</a> (b. 1025)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_Wessex\" title=\"<PERSON> of Wessex\"><PERSON> of Wessex</a> (b. 1025)", "links": [{"title": "<PERSON> of Wessex", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_Wessex"}]}, {"year": "1133", "text": "<PERSON><PERSON><PERSON>, French poet and scholar (b. 1055)", "html": "1133 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, French poet and scholar (b. 1055)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, French poet and scholar (b. 1055)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>"}]}, {"year": "1290", "text": "<PERSON>, king of Sweden (b. 1240)", "html": "1290 - <a href=\"https://wikipedia.org/wiki/<PERSON>_III_of_Sweden\" class=\"mw-redirect\" title=\"<PERSON> III of Sweden\"><PERSON> III</a>, king of Sweden (b. 1240)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_III_of_Sweden\" class=\"mw-redirect\" title=\"<PERSON> III of Sweden\"><PERSON></a>, king of Sweden (b. 1240)", "links": [{"title": "<PERSON> of Sweden", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Sweden"}]}, {"year": "1442", "text": "<PERSON>, French Catholic bishop (b. 1371)", "html": "1442 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French Catholic bishop (b. 1371)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French Catholic bishop (b. 1371)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1495", "text": "<PERSON> of Naples (b. 1448)", "html": "1495 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Naples\" title=\"<PERSON> of Naples\"><PERSON> of Naples</a> (b. 1448)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Naples\" title=\"<PERSON> of Naples\"><PERSON> of Naples</a> (b. 1448)", "links": [{"title": "<PERSON> of Naples", "link": "https://wikipedia.org/wiki/Alfonso_II_of_Naples"}]}, {"year": "1577", "text": "<PERSON> of Saxony, Princess consort of Orange (b. 1544)", "html": "1577 - <a href=\"https://wikipedia.org/wiki/Anna_of_Saxony\" title=\"<PERSON> of Saxony\"><PERSON> of Saxony</a>, Princess consort of Orange (b. 1544)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Anna_of_Saxony\" title=\"<PERSON> of Saxony\"><PERSON> of Saxony</a>, Princess consort of Orange (b. 1544)", "links": [{"title": "Anna of Saxony", "link": "https://wikipedia.org/wiki/Anna_of_Saxony"}]}, {"year": "1645", "text": "<PERSON><PERSON>, empress consort of the Mughal Empire (b. 1577)", "html": "1645 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, empress consort of the Mughal Empire (b. 1577)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, empress consort of the Mughal Empire (b. 1577)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1651", "text": "<PERSON>, 1st Earl of Meath, English lawyer and politician (b. 1580)", "html": "1651 - <a href=\"https://wikipedia.org/wiki/<PERSON>,_1st_Earl_of_Meath\" title=\"<PERSON>, 1st Earl of Meath\"><PERSON>, 1st Earl of Meath</a>, English lawyer and politician (b. 1580)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>,_1st_Earl_of_Meath\" title=\"<PERSON>, 1st Earl of Meath\"><PERSON>, 1st Earl of Meath</a>, English lawyer and politician (b. 1580)", "links": [{"title": "<PERSON>, 1st Earl of Meath", "link": "https://wikipedia.org/wiki/<PERSON>,_1st_Earl_of_Meath"}]}, {"year": "1692", "text": "<PERSON><PERSON> <PERSON>, German scholar and politician (b. 1626)", "html": "1692 - <a href=\"https://wikipedia.org/wiki/V<PERSON>_<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON> <PERSON>\"><PERSON><PERSON> <PERSON></a>, German scholar and politician (b. 1626)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/V<PERSON>_<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON> <PERSON>\"><PERSON><PERSON> <PERSON></a>, German scholar and politician (b. 1626)", "links": [{"title": "<PERSON><PERSON> <PERSON>", "link": "https://wikipedia.org/wiki/V<PERSON>_<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1737", "text": "<PERSON>, Italian instrument maker (b. 1644)", "html": "1737 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian instrument maker (b. 1644)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian instrument maker (b. 1644)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>iva<PERSON>"}]}, {"year": "1787", "text": "<PERSON><PERSON>, English poet and politician (b. 1704)", "html": "1787 - <a href=\"https://wikipedia.org/wiki/Soame_<PERSON>\" title=\"Soame <PERSON>\"><PERSON><PERSON></a>, English poet and politician (b. 1704)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/So<PERSON>_<PERSON>\" title=\"So<PERSON>\"><PERSON><PERSON></a>, English poet and politician (b. 1704)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/So<PERSON>_<PERSON>s"}]}, {"year": "1799", "text": "<PERSON><PERSON><PERSON>, French mathematician and historian (b. 1725)", "html": "1799 - <a href=\"https://wikipedia.org/wiki/<PERSON>-%C3%89tien<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, French mathematician and historian (b. 1725)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>%C3%89tien<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, French mathematician and historian (b. 1725)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>-%C3%89tien<PERSON>_<PERSON>"}]}, {"year": "1803", "text": "<PERSON>, German philosopher, theologian, and poet (b. 1744)", "html": "1803 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German philosopher, theologian, and poet (b. 1744)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German philosopher, theologian, and poet (b. 1744)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1829", "text": "<PERSON><PERSON><PERSON>, French soldier, biologist, and academic (b. 1744)", "html": "1829 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, French soldier, biologist, and academic (b. 1744)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, French soldier, biologist, and academic (b. 1744)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>"}]}, {"year": "1843", "text": "<PERSON>, 1st Baron <PERSON>, Scottish-English general and politician (b. 1748)", "html": "1843 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_1st_Baron_<PERSON>do<PERSON>\" title=\"<PERSON>, 1st Baron L<PERSON>ch\"><PERSON>, 1st Baron <PERSON></a>, Scottish-English general and politician (b. 1748)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>,_1st_Baron_<PERSON>\" title=\"<PERSON>, 1st Baron L<PERSON>ch\"><PERSON>, 1st Baron <PERSON></a>, Scottish-English general and politician (b. 1748)", "links": [{"title": "<PERSON>, 1st Baron <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_1st_Baron_<PERSON>"}]}, {"year": "1848", "text": "<PERSON>, Bohemian priest and mathematician (b. 1781)", "html": "1848 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Bohemian priest and mathematician (b. 1781)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Bohemian priest and mathematician (b. 1781)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1864", "text": "<PERSON>, Mexican politician, President of Mexico (1836-1837) (b. 1794)", "html": "1864 - <a href=\"https://wikipedia.org/wiki/Jos%C3%A9_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Mexican politician, <a href=\"https://wikipedia.org/wiki/President_of_Mexico\" title=\"President of Mexico\">President of Mexico</a> (1836-1837) (b. 1794)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Jos%C3%A9_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Mexican politician, <a href=\"https://wikipedia.org/wiki/President_of_Mexico\" title=\"President of Mexico\">President of Mexico</a> (1836-1837) (b. 1794)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Jos%C3%A9_<PERSON><PERSON>_<PERSON>"}, {"title": "President of Mexico", "link": "https://wikipedia.org/wiki/President_of_Mexico"}]}, {"year": "1869", "text": "<PERSON>, American pianist and composer (b. 1829)", "html": "1869 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American pianist and composer (b. 1829)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American pianist and composer (b. 1829)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1880", "text": "<PERSON>, French mathematician and academic (b. 1793)", "html": "1880 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French mathematician and academic (b. 1793)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French mathematician and academic (b. 1793)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1892", "text": "<PERSON>, English biologist, anatomist, and paleontologist (b. 1804)", "html": "1892 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English biologist, anatomist, and paleontologist (b. 1804)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English biologist, anatomist, and paleontologist (b. 1804)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1919", "text": "<PERSON>, English captain and pilot (b. 1892)", "html": "1919 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(RAF_officer)\" title=\"<PERSON> (RAF officer)\"><PERSON></a>, English captain and pilot (b. 1892)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(RAF_officer)\" title=\"<PERSON> (RAF officer)\"><PERSON></a>, English captain and pilot (b. 1892)", "links": [{"title": "<PERSON> (RAF officer)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(RAF_officer)"}]}, {"year": "1922", "text": "Sir <PERSON>, 1st Baronet, German-English banker and businessman (b. 1851)", "html": "1922 - <a href=\"https://wikipedia.org/wiki/Sir_<PERSON>_<PERSON>,_1st_Baronet\" title=\"Sir <PERSON>, 1st Baronet\">Sir <PERSON>, 1st Baronet</a>, German-English banker and businessman (b. 1851)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Sir_<PERSON>_<PERSON>,_1st_Baronet\" title=\"Sir <PERSON>, 1st Baronet\">Sir <PERSON>, 1st Baronet</a>, German-English banker and businessman (b. 1851)", "links": [{"title": "Sir <PERSON>, 1st Baronet", "link": "https://wikipedia.org/wiki/Sir_<PERSON>,_1st_Baronet"}]}, {"year": "1925", "text": "<PERSON><PERSON>, English sculptor and academic (b. 1850)", "html": "1925 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, English sculptor and academic (b. 1850)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, English sculptor and academic (b. 1850)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1932", "text": "<PERSON>, German theorist and politician (b. 1850)", "html": "1932 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German theorist and politician (b. 1850)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German theorist and politician (b. 1850)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1936", "text": "<PERSON><PERSON><PERSON>, Croatian meteorologist and seismologist (b. 1857)", "html": "1936 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>ovi%C4%8Di%C4%87\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Croatian meteorologist and seismologist (b. 1857)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>%C4%8Di%C4%87\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Croatian meteorologist and seismologist (b. 1857)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_Mohorovi%C4%8Di%C4%87"}]}, {"year": "1939", "text": "<PERSON>, Canadian-American painter (b. 1873)", "html": "1939 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian-American painter (b. 1873)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian-American painter (b. 1873)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1961", "text": "<PERSON>, American violinist and bandleader (b. 1897)", "html": "1961 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American violinist and bandleader (b. 1897)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American violinist and bandleader (b. 1897)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1969", "text": "<PERSON>, American pole vaulter and coach (b. 1878)", "html": "1969 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American pole vaulter and coach (b. 1878)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American pole vaulter and coach (b. 1878)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1971", "text": "<PERSON>, American golfer and lawyer (b. 1902)", "html": "1971 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(golfer)\" title=\"<PERSON> (golfer)\"><PERSON></a>, American golfer and lawyer (b. 1902)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(golfer)\" title=\"<PERSON> (golfer)\"><PERSON></a>, American golfer and lawyer (b. 1902)", "links": [{"title": "<PERSON> (golfer)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(golfer)"}]}, {"year": "1971", "text": "<PERSON>, American actress (b. 1926)", "html": "1971 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress (b. 1926)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress (b. 1926)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1972", "text": "<PERSON><PERSON>, first wife of <PERSON> (b. 1942)", "html": "1972 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, first wife of <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> (b. 1942)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, first wife of <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> (b. 1942)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1973", "text": "<PERSON><PERSON><PERSON>, Indian-Pakistani religious leader and philosopher (b. 1908)", "html": "1973 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON><PERSON>_Turabi\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Indian-Pakistani religious leader and philosopher (b. 1908)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON><PERSON>_Turabi\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Indian-Pakistani religious leader and philosopher (b. 1908)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON><PERSON><PERSON>_<PERSON>i"}]}, {"year": "1974", "text": "<PERSON>, American baseball player, coach, and manager (b. 1887)", "html": "1974 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player, coach, and manager (b. 1887)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player, coach, and manager (b. 1887)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1975", "text": "<PERSON><PERSON><PERSON>, Ukrainian geneticist and biologist (b. 1900)", "html": "1975 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Ukrainian geneticist and biologist (b. 1900)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Ukrainian geneticist and biologist (b. 1900)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1977", "text": "<PERSON><PERSON><PERSON>, Japanese baseball player and manager (b. 1921)", "html": "1977 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese baseball player and manager (b. 1921)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese baseball player and manager (b. 1921)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON><PERSON>"}]}, {"year": "1977", "text": "<PERSON> poet, anthologist, critic (b. 1885)", "html": "1977 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> American poet, anthologist, critic (b. 1885)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> American poet, anthologist, critic (b. 1885)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1980", "text": "<PERSON><PERSON><PERSON>, Croatian poet and translator (b. 1902)", "html": "1980 - <a href=\"https://wikipedia.org/wiki/Dobri%C5%A1a_Cesari%C4%87\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Croatian poet and translator (b. 1902)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Dobri%C5%A1a_Cesari%C4%87\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Croatian poet and translator (b. 1902)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Dobri%C5%A1a_Cesari%C4%87"}]}, {"year": "1980", "text": "<PERSON>, Russian soldier and politician, 8th Premier of the Soviet Union (b. 1904)", "html": "1980 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian soldier and politician, 8th <a href=\"https://wikipedia.org/wiki/Premier_of_the_Soviet_Union\" title=\"Premier of the Soviet Union\">Premier of the Soviet Union</a> (b. 1904)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian soldier and politician, 8th <a href=\"https://wikipedia.org/wiki/Premier_of_the_Soviet_Union\" title=\"Premier of the Soviet Union\">Premier of the Soviet Union</a> (b. 1904)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Premier of the Soviet Union", "link": "https://wikipedia.org/wiki/Premier_of_the_Soviet_Union"}]}, {"year": "1982", "text": "<PERSON><PERSON><PERSON>, German colonel and pilot (b. 1916)", "html": "1982 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, German colonel and pilot (b. 1916)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, German colonel and pilot (b. 1916)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>"}]}, {"year": "1985", "text": "<PERSON><PERSON>, Vietnamese poet and author (b. 1916)", "html": "1985 - <a href=\"https://wikipedia.org/wiki/Xu%C3%A2n_Di%E1%BB%87u\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Vietnamese poet and author (b. 1916)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Xu%C3%A2n_Di%E1%BB%87u\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Vietnamese poet and author (b. 1916)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Xu%C3%A2n_Di%E1%BB%87u"}]}, {"year": "1987", "text": "<PERSON><PERSON>, German keyboard player and producer  (b. 1940)", "html": "1987 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>k\" title=\"Conny Plank\"><PERSON><PERSON></a>, German keyboard player and producer (b. 1940)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"Conny Plank\"><PERSON><PERSON></a>, German keyboard player and producer (b. 1940)", "links": [{"title": "Conny Plank", "link": "https://wikipedia.org/wiki/Conny_Plank"}]}, {"year": "1988", "text": "<PERSON><PERSON><PERSON>, Turkish Cypriot-English sociologist and academic (b. 1908)", "html": "1988 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Turkish Cypriot-English sociologist and academic (b. 1908)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Turkish Cypriot-English sociologist and academic (b. 1908)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1990", "text": "<PERSON>, American actress (b. 1903)", "html": "1990 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress (b. 1903)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress (b. 1903)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1990", "text": "<PERSON>, French cellist and composer (b. 1914)", "html": "1990 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French cellist and composer (b. 1914)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French cellist and composer (b. 1914)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1990", "text": "<PERSON>, Lithuanian-American psychologist and academic (b. 1900)", "html": "1990 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Lithuanian-American psychologist and academic (b. 1900)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Lithuanian-American psychologist and academic (b. 1900)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1991", "text": "<PERSON>, English race car driver (b. 1913)", "html": "1991 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English race car driver (b. 1913)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English race car driver (b. 1913)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1992", "text": "<PERSON>, American game show producer, created <PERSON> Feud and The Price Is Right (b. 1915)", "html": "1992 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American game show producer, created <i><a href=\"https://wikipedia.org/wiki/Family_Feud\" title=\"Family Feud\">Family Feud</a></i> and <i><a href=\"https://wikipedia.org/wiki/The_Price_Is_Right_(U.S._game_show)\" class=\"mw-redirect\" title=\"The Price Is Right (U.S. game show)\">The Price Is Right</a></i> (b. 1915)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American game show producer, created <i><a href=\"https://wikipedia.org/wiki/Family_Feud\" title=\"Family Feud\">Family Feud</a></i> and <i><a href=\"https://wikipedia.org/wiki/The_Price_Is_Right_(U.S._game_show)\" class=\"mw-redirect\" title=\"The Price Is Right (U.S. game show)\">The Price Is Right</a></i> (b. 1915)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Family Feud", "link": "https://wikipedia.org/wiki/Family_Feud"}, {"title": "The Price Is Right (U.S. game show)", "link": "https://wikipedia.org/wiki/The_Price_Is_Right_(U.S._game_show)"}]}, {"year": "1993", "text": "<PERSON><PERSON>, German race car driver (b. 1909)", "html": "1993 - <a href=\"https://wikipedia.org/wiki/He<PERSON>_Gl%C3%B6ckler\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, German race car driver (b. 1909)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/He<PERSON>_Gl%C3%B6ckler\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, German race car driver (b. 1909)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Helm_Gl%C3%B6ckler"}]}, {"year": "1993", "text": "<PERSON>, American-English actor, director, and producer (b. 1919)", "html": "1993 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American-English actor, director, and producer (b. 1919)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American-English actor, director, and producer (b. 1919)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1994", "text": "<PERSON>, Greek-French mathematician and academic (b. 1916)", "html": "1994 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A9ry\" title=\"<PERSON>\"><PERSON></a>, Greek-French mathematician and academic (b. 1916)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A9ry\" title=\"<PERSON>\"><PERSON></a>, Greek-French mathematician and academic (b. 1916)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Roger_Ap%C3%A9ry"}]}, {"year": "1994", "text": "<PERSON><PERSON>, Austrian-American actress (b. 1896)", "html": "1994 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Austrian-American actress (b. 1896)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Austrian-American actress (b. 1896)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>a"}]}, {"year": "1995", "text": "<PERSON>, English organist, composer, and conductor (b. 1926)", "html": "1995 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English organist, composer, and conductor (b. 1926)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English organist, composer, and conductor (b. 1926)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1995", "text": "<PERSON>, American author (b. 1926)", "html": "1995 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(author)\" title=\"<PERSON> (author)\"><PERSON></a>, American author (b. 1926)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(author)\" title=\"<PERSON> (author)\"><PERSON></a>, American author (b. 1926)", "links": [{"title": "<PERSON> (author)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(author)"}]}, {"year": "1995", "text": "<PERSON>, German engineer, designed the Z3 computer (b. 1910)", "html": "1995 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German engineer, designed the <a href=\"https://wikipedia.org/wiki/Z3_(computer)\" title=\"Z3 (computer)\">Z3 computer</a> (b. 1910)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German engineer, designed the <a href=\"https://wikipedia.org/wiki/Z3_(computer)\" title=\"Z3 (computer)\">Z3 computer</a> (b. 1910)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Z3 (computer)", "link": "https://wikipedia.org/wiki/Z3_(computer)"}]}, {"year": "1996", "text": "<PERSON><PERSON><PERSON>, Russian physicist and academic (b. 1904)", "html": "1996 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Russian physicist and academic (b. 1904)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Russian physicist and academic (b. 1904)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1996", "text": "<PERSON>, American composer (b. 1895)", "html": "1996 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Caesar\"><PERSON></a>, American composer (b. 1895)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Caesar\"><PERSON></a>, American composer (b. 1895)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1997", "text": "<PERSON>, American comedian and actor (b. 1964)", "html": "1997 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American comedian and actor (b. 1964)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American comedian and actor (b. 1964)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1998", "text": "<PERSON>, Russian colonel, pilot, and astronaut (b. 1926)", "html": "1998 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian colonel, pilot, and astronaut (b. 1926)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian colonel, pilot, and astronaut (b. 1926)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1999", "text": "<PERSON>, French director and screenwriter (b. 1901)", "html": "1999 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French director and screenwriter (b. 1901)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French director and screenwriter (b. 1901)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2000", "text": "<PERSON>, American race car driver (b. 1952)", "html": "2000 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American race car driver (b. 1952)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American race car driver (b. 1952)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2000", "text": "<PERSON>, American businessman (b. 1915)", "html": "2000 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>st\" title=\"<PERSON> Hearst\"><PERSON></a>, American businessman (b. 1915)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>st\" title=\"<PERSON> Hearst\"><PERSON></a>, American businessman (b. 1915)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON><PERSON>_<PERSON>st"}]}, {"year": "2000", "text": "<PERSON><PERSON><PERSON>, British singer-songwriter (b. 1959)", "html": "2000 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, British singer-songwriter (b. 1959)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, British singer-songwriter (b. 1959)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "2001", "text": "<PERSON>, French singer-songwriter, pianist, and actor (b. 1927)", "html": "2001 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A9caud\" title=\"<PERSON>\"><PERSON></a>, French singer-songwriter, pianist, and actor (b. 1927)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A9caud\" title=\"<PERSON>\"><PERSON></a>, French singer-songwriter, pianist, and actor (b. 1927)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Gilbert_B%C3%A9caud"}]}, {"year": "2001", "text": "<PERSON><PERSON>, Greek violinist and composer (b. 1914)", "html": "2001 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Greek violinist and composer (b. 1914)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Greek violinist and composer (b. 1914)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "2001", "text": "<PERSON>, French saxophonist and educator (b. 1901)", "html": "2001 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French saxophonist and educator (b. 1901)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French saxophonist and educator (b. 1901)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2002", "text": "<PERSON><PERSON><PERSON>, Turkish historian and academic (b. 1954)", "html": "2002 - <a href=\"https://wikipedia.org/wiki/Necip_Hablemito%C4%9Flu\" title=\"Necip <PERSON>\"><PERSON><PERSON><PERSON></a>, Turkish historian and academic (b. 1954)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Necip_Hablemito%C4%9Flu\" title=\"Necip <PERSON>\"><PERSON><PERSON><PERSON></a>, Turkish historian and academic (b. 1954)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Necip_Hablemito%C4%9Flu"}]}, {"year": "2002", "text": "<PERSON>, Canadian lawyer and politician, 24th Governor General of Canada (b. 1934)", "html": "2002 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian lawyer and politician, 24th <a href=\"https://wikipedia.org/wiki/Governor_General_of_Canada\" title=\"Governor General of Canada\">Governor General of Canada</a> (b. 1934)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian lawyer and politician, 24th <a href=\"https://wikipedia.org/wiki/Governor_General_of_Canada\" title=\"Governor General of Canada\">Governor General of Canada</a> (b. 1934)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Ray_<PERSON>hyn"}, {"title": "Governor General of Canada", "link": "https://wikipedia.org/wiki/Governor_General_of_Canada"}]}, {"year": "2002", "text": "<PERSON>, American lawyer and politician (b. 1937)", "html": "2002 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and politician (b. 1937)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and politician (b. 1937)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2002", "text": "<PERSON>, Irish-American author (b. 1963)", "html": "2002 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Irish-American author (b. 1963)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Irish-American author (b. 1963)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2004", "text": "<PERSON>, English journalist and author (b. 1926)", "html": "2004 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English journalist and author (b. 1926)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English journalist and author (b. 1926)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2005", "text": "<PERSON>, American engineer and urban planner (b. 1922)", "html": "2005 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American engineer and urban planner (b. 1922)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American engineer and urban planner (b. 1922)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2006", "text": "<PERSON>, American animator, director, and producer, co-founded <PERSON><PERSON><PERSON><PERSON> (b. 1911)", "html": "2006 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American animator, director, and producer, co-founded <a href=\"https://wikipedia.org/wiki/<PERSON>-Barber<PERSON>\" title=\"<PERSON><PERSON>Barbera\"><PERSON><PERSON></a> (b. 1911)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American animator, director, and producer, co-founded <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>\" title=\"<PERSON><PERSON>Barber<PERSON>\"><PERSON><PERSON></a> (b. 1911)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>-<PERSON>"}]}, {"year": "2006", "text": "<PERSON>, German-American photographer (b. 1905)", "html": "2006 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German-American photographer (b. 1905)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German-American photographer (b. 1905)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2006", "text": "<PERSON><PERSON><PERSON><PERSON>, Pakistani author and activist (b. 1923)", "html": "2006 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Pakistani author and activist (b. 1923)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Pakistani author and activist (b. 1923)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "2007", "text": "<PERSON>, Polish-German actor, director, and screenwriter (b. 1918)", "html": "2007 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Polish-German actor, director, and screenwriter (b. 1918)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Polish-German actor, director, and screenwriter (b. 1918)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2007", "text": "<PERSON>, Canadian lawyer and judge (b. 1924)", "html": "2007 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian lawyer and judge (b. 1924)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian lawyer and judge (b. 1924)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2007", "text": "<PERSON>, American author and playwright (b. 1947)", "html": "2007 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author and playwright (b. 1947)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author and playwright (b. 1947)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2007", "text": "<PERSON>, American businessman and critic (b. 1931)", "html": "2007 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American businessman and critic (b. 1931)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American businessman and critic (b. 1931)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2008", "text": "<PERSON><PERSON>, American actress and producer (b. 1932)", "html": "2008 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American actress and producer (b. 1932)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American actress and producer (b. 1932)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "2008", "text": "<PERSON>, American FBI agent and informant (b. 1913)", "html": "2008 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American <a href=\"https://wikipedia.org/wiki/FBI\" class=\"mw-redirect\" title=\"FBI\">FBI</a> agent and informant (b. 1913)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American <a href=\"https://wikipedia.org/wiki/FBI\" class=\"mw-redirect\" title=\"FBI\">FBI</a> agent and informant (b. 1913)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "FBI", "link": "https://wikipedia.org/wiki/FBI"}]}, {"year": "2010", "text": "<PERSON>, American baseball player and manager (b. 1916)", "html": "2010 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player and manager (b. 1916)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player and manager (b. 1916)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Phil_C<PERSON>rretta"}]}, {"year": "2010", "text": "<PERSON>, French philologist, author, and scholar (b. 1913)", "html": "2010 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French philologist, author, and scholar (b. 1913)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French philologist, author, and scholar (b. 1913)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "2010", "text": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, Italian economist and politician, Italian Minister of Economy and Finances (b. 1940)", "html": "2010 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>-<PERSON>hi<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Italian economist and politician, <a href=\"https://wikipedia.org/wiki/List_of_Italian_Ministers_of_Economy_and_Finances\" class=\"mw-redirect\" title=\"List of Italian Ministers of Economy and Finances\">Italian Minister of Economy and Finances</a> (b. 1940)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>-<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Italian economist and politician, <a href=\"https://wikipedia.org/wiki/List_of_Italian_Ministers_of_Economy_and_Finances\" class=\"mw-redirect\" title=\"List of Italian Ministers of Economy and Finances\">Italian Minister of Economy and Finances</a> (b. 1940)", "links": [{"title": "<PERSON><PERSON><PERSON>-<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_Padoa-Sc<PERSON>"}, {"title": "List of Italian Ministers of Economy and Finances", "link": "https://wikipedia.org/wiki/List_of_Italian_Ministers_of_Economy_and_Finances"}]}, {"year": "2010", "text": "<PERSON>, English judge and journalist (b. 1925)", "html": "2010 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English judge and journalist (b. 1925)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English judge and journalist (b. 1925)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "2011", "text": "<PERSON><PERSON><PERSON><PERSON>, Czech poet, playwright, and politician, 1st President of the Czech Republic (b. 1936)", "html": "2011 - <a href=\"https://wikipedia.org/wiki/V%C3%<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Czech poet, playwright, and politician, 1st <a href=\"https://wikipedia.org/wiki/President_of_the_Czech_Republic\" title=\"President of the Czech Republic\">President of the Czech Republic</a> (b. 1936)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/V%C3%<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Czech poet, playwright, and politician, 1st <a href=\"https://wikipedia.org/wiki/President_of_the_Czech_Republic\" title=\"President of the Czech Republic\">President of the Czech Republic</a> (b. 1936)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/V%C3%<PERSON><PERSON><PERSON>_<PERSON>l"}, {"title": "President of the Czech Republic", "link": "https://wikipedia.org/wiki/President_of_the_Czech_Republic"}]}, {"year": "2012", "text": "<PERSON>, American lawyer and academic (b. 1941)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and academic (b. 1941)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and academic (b. 1941)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "2012", "text": "<PERSON>, Mauritanian colonel and politician, President of Mauritania (b. 1936)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Mauritanian colonel and politician, <a href=\"https://wikipedia.org/wiki/President_of_Mauritania\" class=\"mw-redirect\" title=\"President of Mauritania\">President of Mauritania</a> (b. 1936)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Mauritanian colonel and politician, <a href=\"https://wikipedia.org/wiki/President_of_Mauritania\" class=\"mw-redirect\" title=\"President of Mauritania\">President of Mauritania</a> (b. 1936)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "President of Mauritania", "link": "https://wikipedia.org/wiki/President_of_Mauritania"}]}, {"year": "2012", "text": "<PERSON>, American football player (b. 1943)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player (b. 1943)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player (b. 1943)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2012", "text": "<PERSON><PERSON><PERSON><PERSON>, Ukrainian footballer, coach, and manager (b. 1931)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Ukrainian footballer, coach, and manager (b. 1931)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Ukrainian footballer, coach, and manager (b. 1931)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "2013", "text": "<PERSON>, American football player (b. 1952)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player (b. 1952)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player (b. 1952)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2013", "text": "<PERSON>, South African-English businessman (b. 1949)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(businessman)\" title=\"<PERSON> (businessman)\"><PERSON></a>, South African-English businessman (b. 1949)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(businessman)\" title=\"<PERSON> (businessman)\"><PERSON></a>, South African-English businessman (b. 1949)", "links": [{"title": "<PERSON> (businessman)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>(businessman)"}]}, {"year": "2014", "text": "<PERSON>, American soldier and politician (b. 1925)", "html": "2014 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American soldier and politician (b. 1925)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American soldier and politician (b. 1925)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "2014", "text": "<PERSON>, Israeli soldier and politician (b. 1923)", "html": "2014 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Israeli soldier and politician (b. 1923)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Israeli soldier and politician (b. 1923)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2014", "text": "<PERSON>, American singer-songwriter (b. 1937)", "html": "2014 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter (b. 1937)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter (b. 1937)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2014", "text": "<PERSON><PERSON><PERSON>, Italian actress (b. 1936)", "html": "2014 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Italian actress (b. 1936)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Italian actress (b. 1936)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON><PERSON>"}]}, {"year": "2014", "text": "<PERSON>-<PERSON>, English model and actress (b. 1944)", "html": "2014 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English model and actress (b. 1944)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English model and actress (b. 1944)", "links": [{"title": "<PERSON>-<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2014", "text": "<PERSON>, American meteorologist and author (b. 1912)", "html": "2014 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(meteorologist)\" title=\"<PERSON> (meteorologist)\"><PERSON></a>, American meteorologist and author (b. 1912)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(meteorologist)\" title=\"<PERSON> (meteorologist)\"><PERSON></a>, American meteorologist and author (b. 1912)", "links": [{"title": "<PERSON> (meteorologist)", "link": "https://wikipedia.org/wiki/<PERSON>_(meteorologist)"}]}, {"year": "2015", "text": "<PERSON>, Belgian pianist, composer, and conductor (b. 1959)", "html": "2015 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Belgian pianist, composer, and conductor (b. 1959)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Belgian pianist, composer, and conductor (b. 1959)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2015", "text": "<PERSON><PERSON>, Norwegian businessman and politician (b. 1969)", "html": "2015 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Norwegian businessman and politician (b. 1969)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Norwegian businessman and politician (b. 1969)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>"}]}, {"year": "2016", "text": "<PERSON><PERSON>, Hungarian-American actress and socialite (b. 1917)", "html": "2016 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_Gabor\" title=\"<PERSON><PERSON> Z<PERSON> Gabor\"><PERSON><PERSON></a>, Hungarian-American actress and socialite (b. 1917)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>abor\" title=\"<PERSON><PERSON> Z<PERSON> Gabor\"><PERSON><PERSON></a>, Hungarian-American actress and socialite (b. 1917)", "links": [{"title": "Zsa Zsa Gabor", "link": "https://wikipedia.org/wiki/Zsa_Z<PERSON>_Gabor"}]}, {"year": "2017", "text": "<PERSON>, South Korean singer (b. 1990)", "html": "2017 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, South Korean singer (b. 1990)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, South Korean singer (b. 1990)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>yun"}]}, {"year": "2020", "text": "<PERSON>, American politician and member of the Minnesota Senate (b. 1944)", "html": "2020 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American politician and member of the <a href=\"https://wikipedia.org/wiki/Minnesota_Senate\" title=\"Minnesota Senate\">Minnesota Senate</a> (b. 1944)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American politician and member of the <a href=\"https://wikipedia.org/wiki/Minnesota_Senate\" title=\"Minnesota Senate\">Minnesota Senate</a> (b. 1944)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>ph"}, {"title": "Minnesota Senate", "link": "https://wikipedia.org/wiki/Minnesota_Senate"}]}, {"year": "2021", "text": "<PERSON><PERSON>, Japanese actress and singer (b. 1986)", "html": "2021 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Japanese actress and singer (b. 1986)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Japanese actress and singer (b. 1986)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>nda"}]}, {"year": "2024", "text": "<PERSON>, American singer-songwriter and guitarist (b. 1951)", "html": "2024 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Du<PERSON>lap\"><PERSON></a>, American singer-songwriter and guitarist (b. 1951)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Du<PERSON>lap\"><PERSON></a>, American singer-songwriter and guitarist (b. 1951)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2024", "text": "<PERSON>, Australian writer (b. 1950)", "html": "2024 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(writer)\" title=\"<PERSON> (writer)\"><PERSON></a>, Australian writer (b. 1950)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(writer)\" title=\"<PERSON> (writer)\"><PERSON></a>, Australian writer (b. 1950)", "links": [{"title": "<PERSON> (writer)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(writer)"}]}]}}