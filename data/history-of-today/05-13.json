{"date": "May 13", "url": "https://wikipedia.org/wiki/May_13", "data": {"Events": [{"year": "1344", "text": "A Latin Christian fleet defeats a larger Turkish fleet in the battle of Pallene during the Smyrniote crusades.", "html": "1344 - A <a href=\"https://wikipedia.org/wiki/Latin_Christian\" class=\"mw-redirect\" title=\"Latin Christian\">Latin Christian</a> fleet defeats a larger Turkish fleet in the <a href=\"https://wikipedia.org/wiki/Battle_of_Pallene\" title=\"Battle of Pallene\">battle of Pallene</a> during the <a href=\"https://wikipedia.org/wiki/Smyrniote_crusades\" title=\"Smyrniote crusades\">Smyrniote crusades</a>.", "no_year_html": "A <a href=\"https://wikipedia.org/wiki/Latin_Christian\" class=\"mw-redirect\" title=\"Latin Christian\">Latin Christian</a> fleet defeats a larger Turkish fleet in the <a href=\"https://wikipedia.org/wiki/Battle_of_Pallene\" title=\"Battle of Pallene\">battle of Pallene</a> during the <a href=\"https://wikipedia.org/wiki/Smyrniote_crusades\" title=\"Smyrniote crusades\">Smyrniote crusades</a>.", "links": [{"title": "Latin Christian", "link": "https://wikipedia.org/wiki/Latin_Christian"}, {"title": "Battle of Pallene", "link": "https://wikipedia.org/wiki/Battle_of_Pallene"}, {"title": "Smyrniote crusades", "link": "https://wikipedia.org/wiki/Smyrniote_crusades"}]}, {"year": "1373", "text": "<PERSON> of Norwich has visions of <PERSON> while suffering from a life-threatening illness, visions which are later described and interpreted in her book Revelations of Divine Love.", "html": "1373 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON> of Norwich\"><PERSON> of Norwich</a> has <a href=\"https://wikipedia.org/wiki/Visions_of_<PERSON>_and_Mary\" title=\"Visions of Jesus and Mary\">visions of <PERSON></a> while suffering from a life-threatening illness, visions which are later described and interpreted in her book <i><a href=\"https://wikipedia.org/wiki/Revelations_of_Divine_Love\" title=\"Revelations of Divine Love\">Revelations of Divine Love</a></i>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON> of Norwich\"><PERSON> of Norwich</a> has <a href=\"https://wikipedia.org/wiki/Visions_of_<PERSON>_and_Mary\" title=\"Visions of Jesus and Mary\">visions of <PERSON></a> while suffering from a life-threatening illness, visions which are later described and interpreted in her book <i><a href=\"https://wikipedia.org/wiki/Revelations_of_Divine_Love\" title=\"Revelations of Divine Love\">Revelations of Divine Love</a></i>.", "links": [{"title": "<PERSON> Norwich", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_Norwich"}, {"title": "Visions of Jesus and Mary", "link": "https://wikipedia.org/wiki/Visions_of_<PERSON>_and_<PERSON>"}, {"title": "Revelations of Divine Love", "link": "https://wikipedia.org/wiki/Revelations_of_Divine_Love"}]}, {"year": "1501", "text": "<PERSON><PERSON><PERSON>, this time under Portuguese flag, set sail for western lands.", "html": "1501 - <a href=\"https://wikipedia.org/wiki/Amerigo_<PERSON>\" title=\"<PERSON>eri<PERSON>\"><PERSON><PERSON><PERSON></a>, this time under Portuguese flag, set sail for western lands.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Ameri<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, this time under Portuguese flag, set sail for western lands.", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Amerigo_<PERSON>"}]}, {"year": "1568", "text": "<PERSON>, Queen of Scots, is defeated at the Battle of Langside, part of the civil war between <PERSON> <PERSON> and the supporters of her son, <PERSON>.", "html": "1568 - <a href=\"https://wikipedia.org/wiki/<PERSON>,_Queen_of_Scots\" title=\"<PERSON>, Queen of Scots\"><PERSON>, Queen of Scots</a>, is defeated at the <a href=\"https://wikipedia.org/wiki/Battle_of_Langside\" title=\"Battle of Langside\">Battle of Langside</a>, part of the <a href=\"https://wikipedia.org/wiki/Marian_civil_war\" title=\"Marian civil war\">civil war between Queen <PERSON> and the supporters of her son, <PERSON>.</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>,_Queen_of_Scots\" title=\"<PERSON>, Queen of Scots\"><PERSON>, Queen of Scots</a>, is defeated at the <a href=\"https://wikipedia.org/wiki/Battle_of_Langside\" title=\"Battle of Langside\">Battle of Langside</a>, part of the <a href=\"https://wikipedia.org/wiki/Marian_civil_war\" title=\"Marian civil war\">civil war between Queen <PERSON> and the supporters of her son, <PERSON>.</a>", "links": [{"title": "<PERSON>, Queen of Scots", "link": "https://wikipedia.org/wiki/<PERSON>,_Queen_of_Scots"}, {"title": "Battle of Langside", "link": "https://wikipedia.org/wiki/Battle_of_Langside"}, {"title": "Marian civil war", "link": "https://wikipedia.org/wiki/Marian_civil_war"}]}, {"year": "1612", "text": "Sword duel between <PERSON><PERSON><PERSON> and <PERSON><PERSON> on the shores of Ganryū Island. <PERSON><PERSON><PERSON> dies at the end.", "html": "1612 - Sword duel between <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a> and <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a> on the shores of Ganryū Island. <PERSON><PERSON><PERSON> dies at the end.", "no_year_html": "Sword duel between <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a> and <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a> on the shores of Ganryū Island. <PERSON><PERSON><PERSON> dies at the end.", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}, {"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1619", "text": "Dutch statesman <PERSON> is executed in The Hague after being convicted of treason.", "html": "1619 - Dutch statesman <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> is executed in <a href=\"https://wikipedia.org/wiki/The_Hague\" title=\"The Hague\">The Hague</a> after being convicted of <a href=\"https://wikipedia.org/wiki/Treason\" title=\"Treason\">treason</a>.", "no_year_html": "Dutch statesman <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> is executed in <a href=\"https://wikipedia.org/wiki/The_Hague\" title=\"The Hague\">The Hague</a> after being convicted of <a href=\"https://wikipedia.org/wiki/Treason\" title=\"Treason\">treason</a>.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "The Hague", "link": "https://wikipedia.org/wiki/The_Hague"}, {"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Treason"}]}, {"year": "1654", "text": "A Venetian fleet under Admiral <PERSON><PERSON> breaks through a line of galleys and defeats the Turkish navy.", "html": "1654 - A Venetian fleet under Admiral <a href=\"https://wikipedia.org/wiki/Cort_Adeler\" title=\"Cort Adeler\"><PERSON><PERSON> Adeler</a> breaks through a line of galleys and defeats the Turkish navy.", "no_year_html": "A Venetian fleet under Admiral <a href=\"https://wikipedia.org/wiki/Cort_Adeler\" title=\"Cort Adeler\"><PERSON><PERSON> Adeler</a> breaks through a line of galleys and defeats the Turkish navy.", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Cort_<PERSON>r"}]}, {"year": "1779", "text": "War of the Bavarian Succession: Russian and French mediators at the Congress of Teschen negotiate an end to the war. In the agreement Austria receives the part of its territory that was taken from it (the Innviertel).", "html": "1779 - <a href=\"https://wikipedia.org/wiki/War_of_the_Bavarian_Succession\" title=\"War of the Bavarian Succession\">War of the Bavarian Succession</a>: Russian and French mediators at the <a href=\"https://wikipedia.org/wiki/Congress_of_Teschen\" class=\"mw-redirect\" title=\"Congress of Teschen\">Congress of Teschen</a> negotiate an end to the war. In the agreement Austria receives the part of its territory that was taken from it (the <a href=\"https://wikipedia.org/wiki/Innviertel\" title=\"Innviertel\">Innviertel</a>).", "no_year_html": "<a href=\"https://wikipedia.org/wiki/War_of_the_Bavarian_Succession\" title=\"War of the Bavarian Succession\">War of the Bavarian Succession</a>: Russian and French mediators at the <a href=\"https://wikipedia.org/wiki/Congress_of_Teschen\" class=\"mw-redirect\" title=\"Congress of Teschen\">Congress of Teschen</a> negotiate an end to the war. In the agreement Austria receives the part of its territory that was taken from it (the <a href=\"https://wikipedia.org/wiki/Innviertel\" title=\"Innviertel\">Innviertel</a>).", "links": [{"title": "War of the Bavarian Succession", "link": "https://wikipedia.org/wiki/War_of_the_Bavarian_Succession"}, {"title": "Congress of Teschen", "link": "https://wikipedia.org/wiki/Congress_of_Teschen"}, {"title": "Innviertel", "link": "https://wikipedia.org/wiki/Innviertel"}]}, {"year": "1780", "text": "The Cumberland Compact is signed by leaders of the settlers in the Cumberland River area of what would become the U.S. state of Tennessee, providing for democratic government and a formal system of justice.", "html": "1780 - The <a href=\"https://wikipedia.org/wiki/Cumberland_Compact\" title=\"Cumberland Compact\">Cumberland Compact</a> is signed by leaders of the settlers in the <a href=\"https://wikipedia.org/wiki/Cumberland_River\" title=\"Cumberland River\">Cumberland River</a> area of what would become the <a href=\"https://wikipedia.org/wiki/U.S._state\" title=\"U.S. state\">U.S. state</a> of <a href=\"https://wikipedia.org/wiki/Tennessee\" title=\"Tennessee\">Tennessee</a>, providing for democratic government and a formal system of justice.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Cumberland_Compact\" title=\"Cumberland Compact\">Cumberland Compact</a> is signed by leaders of the settlers in the <a href=\"https://wikipedia.org/wiki/Cumberland_River\" title=\"Cumberland River\">Cumberland River</a> area of what would become the <a href=\"https://wikipedia.org/wiki/U.S._state\" title=\"U.S. state\">U.S. state</a> of <a href=\"https://wikipedia.org/wiki/Tennessee\" title=\"Tennessee\">Tennessee</a>, providing for democratic government and a formal system of justice.", "links": [{"title": "Cumberland Compact", "link": "https://wikipedia.org/wiki/Cumberland_Compact"}, {"title": "Cumberland River", "link": "https://wikipedia.org/wiki/Cumberland_River"}, {"title": "U.S. state", "link": "https://wikipedia.org/wiki/U.S._state"}, {"title": "Tennessee", "link": "https://wikipedia.org/wiki/Tennessee"}]}, {"year": "1804", "text": "Forces sent by <PERSON> of Tripoli to retake Derna from the Americans attack the city.", "html": "1804 - Forces sent by <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> of Tripoli to <a href=\"https://wikipedia.org/wiki/Battle_of_Derne\" class=\"mw-redirect\" title=\"Battle of Derne\">retake Derna</a> from the Americans attack the city.", "no_year_html": "Forces sent by <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> of Tripoli to <a href=\"https://wikipedia.org/wiki/Battle_of_Derne\" class=\"mw-redirect\" title=\"Battle of Derne\">retake Derna</a> from the Americans attack the city.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Battle of Derne", "link": "https://wikipedia.org/wiki/Battle_of_Derne"}]}, {"year": "1830", "text": "Ecuador gains its independence from Gran Colombia.", "html": "1830 - <a href=\"https://wikipedia.org/wiki/Ecuador\" title=\"Ecuador\">Ecuador</a> gains its independence from <a href=\"https://wikipedia.org/wiki/Gran_Colombia\" title=\"Gran Colombia\">Gran Colombia</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Ecuador\" title=\"Ecuador\">Ecuador</a> gains its independence from <a href=\"https://wikipedia.org/wiki/Gran_Colombia\" title=\"Gran Colombia\">Gran Colombia</a>.", "links": [{"title": "Ecuador", "link": "https://wikipedia.org/wiki/Ecuador"}, {"title": "Gran Colombia", "link": "https://wikipedia.org/wiki/Gran_Colombia"}]}, {"year": "1846", "text": "Mexican-American War: The United States declares war on the Federal Republic of Mexico following a dispute over the American annexation of the Republic of Texas and a Mexican military incursion.", "html": "1846 - <a href=\"https://wikipedia.org/wiki/Mexican%E2%80%93American_War\" title=\"Mexican-American War\">Mexican-American War</a>: The <a href=\"https://wikipedia.org/wiki/United_States\" title=\"United States\">United States</a> <a href=\"https://wikipedia.org/wiki/United_States_declaration_of_war_on_Mexico\" title=\"United States declaration of war on Mexico\">declares war</a> on the <a href=\"https://wikipedia.org/wiki/Second_Federal_Republic_of_Mexico\" title=\"Second Federal Republic of Mexico\">Federal Republic of Mexico</a> following a dispute over the American <a href=\"https://wikipedia.org/wiki/Texas_annexation\" title=\"Texas annexation\">annexation</a> of the <a href=\"https://wikipedia.org/wiki/Republic_of_Texas\" title=\"Republic of Texas\">Republic of Texas</a> and a Mexican military <a href=\"https://wikipedia.org/wiki/Thornton_Affair\" title=\"Thornton Affair\">incursion</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Mexican%E2%80%93American_War\" title=\"Mexican-American War\">Mexican-American War</a>: The <a href=\"https://wikipedia.org/wiki/United_States\" title=\"United States\">United States</a> <a href=\"https://wikipedia.org/wiki/United_States_declaration_of_war_on_Mexico\" title=\"United States declaration of war on Mexico\">declares war</a> on the <a href=\"https://wikipedia.org/wiki/Second_Federal_Republic_of_Mexico\" title=\"Second Federal Republic of Mexico\">Federal Republic of Mexico</a> following a dispute over the American <a href=\"https://wikipedia.org/wiki/Texas_annexation\" title=\"Texas annexation\">annexation</a> of the <a href=\"https://wikipedia.org/wiki/Republic_of_Texas\" title=\"Republic of Texas\">Republic of Texas</a> and a Mexican military <a href=\"https://wikipedia.org/wiki/Thornton_Affair\" title=\"Thornton Affair\">incursion</a>.", "links": [{"title": "Mexican-American War", "link": "https://wikipedia.org/wiki/Mexican%E2%80%93American_War"}, {"title": "United States", "link": "https://wikipedia.org/wiki/United_States"}, {"title": "United States declaration of war on Mexico", "link": "https://wikipedia.org/wiki/United_States_declaration_of_war_on_Mexico"}, {"title": "Second Federal Republic of Mexico", "link": "https://wikipedia.org/wiki/Second_Federal_Republic_of_Mexico"}, {"title": "Texas annexation", "link": "https://wikipedia.org/wiki/Texas_annexation"}, {"title": "Republic of Texas", "link": "https://wikipedia.org/wiki/Republic_of_Texas"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Thornton_Affair"}]}, {"year": "1861", "text": "American Civil War: Queen <PERSON> of the United Kingdom issues a \"proclamation of neutrality\" which recognizes the Confederacy as having belligerent rights.", "html": "1861 - <a href=\"https://wikipedia.org/wiki/American_Civil_War\" title=\"American Civil War\">American Civil War</a>: Queen <PERSON> of the United Kingdom issues a \"<a href=\"https://wikipedia.org/wiki/United_Kingdom_and_the_American_Civil_War\" title=\"United Kingdom and the American Civil War\">proclamation of neutrality</a>\" which recognizes the Confederacy as having belligerent rights.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/American_Civil_War\" title=\"American Civil War\">American Civil War</a>: Queen <PERSON> of the United Kingdom issues a \"<a href=\"https://wikipedia.org/wiki/United_Kingdom_and_the_American_Civil_War\" title=\"United Kingdom and the American Civil War\">proclamation of neutrality</a>\" which recognizes the Confederacy as having belligerent rights.", "links": [{"title": "American Civil War", "link": "https://wikipedia.org/wiki/American_Civil_War"}, {"title": "United Kingdom and the American Civil War", "link": "https://wikipedia.org/wiki/United_Kingdom_and_the_American_Civil_War"}]}, {"year": "1861", "text": "The Great Comet of 1861 is discovered by <PERSON> of Windsor, New South Wales, Australia.", "html": "1861 - The <a href=\"https://wikipedia.org/wiki/Great_Comet_of_1861\" class=\"mw-redirect\" title=\"Great Comet of 1861\">Great Comet of 1861</a> is discovered by <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> of <a href=\"https://wikipedia.org/wiki/Windsor,_New_South_Wales\" title=\"Windsor, New South Wales\">Windsor, New South Wales</a>, Australia.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Great_Comet_of_1861\" class=\"mw-redirect\" title=\"Great Comet of 1861\">Great Comet of 1861</a> is discovered by <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> of <a href=\"https://wikipedia.org/wiki/Windsor,_New_South_Wales\" title=\"Windsor, New South Wales\">Windsor, New South Wales</a>, Australia.", "links": [{"title": "Great Comet of 1861", "link": "https://wikipedia.org/wiki/Great_Comet_of_1861"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}, {"title": "Windsor, New South Wales", "link": "https://wikipedia.org/wiki/Windsor,_New_South_Wales"}]}, {"year": "1861", "text": "Pakistan's (then a part of British India) first railway line  opens, from Karachi to Kotri.", "html": "1861 - <a href=\"https://wikipedia.org/wiki/Pakistan_Railways\" title=\"Pakistan Railways\">Pakistan</a>'s (then a part of British India) <a href=\"https://wikipedia.org/wiki/History_of_rail_transport_in_Pakistan\" title=\"History of rail transport in Pakistan\">first railway line</a> opens, from <a href=\"https://wikipedia.org/wiki/Karachi\" title=\"Karachi\">Karachi</a> to <a href=\"https://wikipedia.org/wiki/Kotri\" title=\"Kotri\"><PERSON><PERSON></a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Pakistan_Railways\" title=\"Pakistan Railways\">Pakistan</a>'s (then a part of British India) <a href=\"https://wikipedia.org/wiki/History_of_rail_transport_in_Pakistan\" title=\"History of rail transport in Pakistan\">first railway line</a> opens, from <a href=\"https://wikipedia.org/wiki/Karachi\" title=\"Karachi\">Karachi</a> to <a href=\"https://wikipedia.org/wiki/Kotri\" title=\"Kotri\">Ko<PERSON></a>.", "links": [{"title": "Pakistan Railways", "link": "https://wikipedia.org/wiki/Pakistan_Railways"}, {"title": "History of rail transport in Pakistan", "link": "https://wikipedia.org/wiki/History_of_rail_transport_in_Pakistan"}, {"title": "Karachi", "link": "https://wikipedia.org/wiki/Karachi"}, {"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>"}]}, {"year": "1862", "text": "The USS Planter, a steamer and gunship, steals through Confederate lines and is passed to the Union, by a southern slave, <PERSON>, who later was officially appointed as captain, becoming the first black man to command a United States ship.", "html": "1862 - The <a href=\"https://wikipedia.org/wiki/USS_Planter_(1862)\" title=\"USS Planter (1862)\">USS <i>Planter</i></a>, a steamer and gunship, steals through Confederate lines and is passed to the Union, by a southern slave, <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, who later was officially appointed as captain, becoming the first black man to command a United States ship.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/USS_Planter_(1862)\" title=\"USS Planter (1862)\">USS <i>Planter</i></a>, a steamer and gunship, steals through Confederate lines and is passed to the Union, by a southern slave, <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, who later was officially appointed as captain, becoming the first black man to command a United States ship.", "links": [{"title": "USS Planter (1862)", "link": "https://wikipedia.org/wiki/USS_Planter_(1862)"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1888", "text": "With the passage of the Lei Áurea (\"Golden Law\"), the Empire of Brazil abolishes slavery.", "html": "1888 - With the passage of the <i><a href=\"https://wikipedia.org/wiki/Lei_%C3%81urea\" title=\"Lei Áure<PERSON>\"><PERSON><PERSON></a></i> (\"Golden Law\"), the <a href=\"https://wikipedia.org/wiki/Empire_of_Brazil\" title=\"Empire of Brazil\">Empire of Brazil</a> abolishes <a href=\"https://wikipedia.org/wiki/Slavery\" title=\"Slavery\">slavery</a>.", "no_year_html": "With the passage of the <i><a href=\"https://wikipedia.org/wiki/Lei_%C3%81urea\" title=\"Lei Áurea\"><PERSON><PERSON></a></i> (\"Golden Law\"), the <a href=\"https://wikipedia.org/wiki/Empire_of_Brazil\" title=\"Empire of Brazil\">Empire of Brazil</a> abolishes <a href=\"https://wikipedia.org/wiki/Slavery\" title=\"Slavery\">slavery</a>.", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Lei_%C3%81urea"}, {"title": "Empire of Brazil", "link": "https://wikipedia.org/wiki/Empire_of_Brazil"}, {"title": "Slavery", "link": "https://wikipedia.org/wiki/Slavery"}]}, {"year": "1909", "text": "The first edition of the Giro d'Italia, a long-distance multiple-stage bicycle race, began in Milan; the Italian cyclist <PERSON> was the eventual winner.", "html": "1909 - <a href=\"https://wikipedia.org/wiki/1909_Giro_d%27Italia\" title=\"1909 Giro d'Italia\">The first edition</a> of the <a href=\"https://wikipedia.org/wiki/Giro_d%27Italia\" title=\"Giro d'Italia\">Giro d'Italia</a>, a long-distance multiple-<a href=\"https://wikipedia.org/wiki/Race_stage\" title=\"Race stage\">stage</a> bicycle race, began in <a href=\"https://wikipedia.org/wiki/Milan\" title=\"Milan\">Milan</a>; the Italian cyclist <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Luigi <PERSON>\"><PERSON></a> was the eventual winner.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/1909_Giro_d%27Italia\" title=\"1909 Giro d'Italia\">The first edition</a> of the <a href=\"https://wikipedia.org/wiki/Giro_d%27Italia\" title=\"Giro d'Italia\">Giro d'Italia</a>, a long-distance multiple-<a href=\"https://wikipedia.org/wiki/Race_stage\" title=\"Race stage\">stage</a> bicycle race, began in <a href=\"https://wikipedia.org/wiki/Milan\" title=\"Milan\">Milan</a>; the Italian cyclist <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Luigi <PERSON>\"><PERSON></a> was the eventual winner.", "links": [{"title": "1909 Giro d'Italia", "link": "https://wikipedia.org/wiki/1909_Giro_d%27Italia"}, {"title": "Giro d'Italia", "link": "https://wikipedia.org/wiki/Giro_d%27Italia"}, {"title": "Race stage", "link": "https://wikipedia.org/wiki/Race_stage"}, {"title": "Milan", "link": "https://wikipedia.org/wiki/Milan"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1912", "text": "The Royal Flying Corps, the forerunner of the Royal Air Force, is established in the United Kingdom.", "html": "1912 - The <a href=\"https://wikipedia.org/wiki/Royal_Flying_Corps\" title=\"Royal Flying Corps\">Royal Flying Corps</a>, the forerunner of the <a href=\"https://wikipedia.org/wiki/Royal_Air_Force\" title=\"Royal Air Force\">Royal Air Force</a>, is established in the United Kingdom.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Royal_Flying_Corps\" title=\"Royal Flying Corps\">Royal Flying Corps</a>, the forerunner of the <a href=\"https://wikipedia.org/wiki/Royal_Air_Force\" title=\"Royal Air Force\">Royal Air Force</a>, is established in the United Kingdom.", "links": [{"title": "Royal Flying Corps", "link": "https://wikipedia.org/wiki/Royal_Flying_Corps"}, {"title": "Royal Air Force", "link": "https://wikipedia.org/wiki/Royal_Air_Force"}]}, {"year": "1917", "text": "Three children report the first apparition of Our Lady of Fátima in Fátima, Portugal.", "html": "1917 - Three children report the first apparition of <a href=\"https://wikipedia.org/wiki/Our_Lady_of_F%C3%A1tima\" title=\"Our Lady of Fátima\">Our Lady of Fátima</a> in <a href=\"https://wikipedia.org/wiki/F%C3%A1tima,_Portugal\" title=\"Fátima, Portugal\">Fátima, Portugal</a>.", "no_year_html": "Three children report the first apparition of <a href=\"https://wikipedia.org/wiki/Our_Lady_of_F%C3%A1tima\" title=\"Our Lady of Fátima\">Our Lady of Fátima</a> in <a href=\"https://wikipedia.org/wiki/F%C3%A1tima,_Portugal\" title=\"Fátima, Portugal\">Fátima, Portugal</a>.", "links": [{"title": "Our Lady of Fátima", "link": "https://wikipedia.org/wiki/Our_Lady_of_F%C3%A1tima"}, {"title": "Fátima, Portugal", "link": "https://wikipedia.org/wiki/F%C3%A1tima,_Portugal"}]}, {"year": "1940", "text": "World War II: Germany's conquest of France begins, as the German army crosses the Meuse. <PERSON> makes his \"blood, toil, tears, and sweat\" speech to the House of Commons.", "html": "1940 - <a href=\"https://wikipedia.org/wiki/World_War_II\" title=\"World War II\">World War II</a>: <a href=\"https://wikipedia.org/wiki/Battle_of_France\" title=\"Battle of France\">Germany's conquest of France</a> begins, as the German army crosses the <a href=\"https://wikipedia.org/wiki/Meuse\" title=\"Meuse\">Meuse</a>. <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> makes his \"<a href=\"https://wikipedia.org/wiki/Blood,_toil,_tears,_and_sweat\" class=\"mw-redirect\" title=\"Blood, toil, tears, and sweat\">blood, toil, tears, and sweat</a>\" speech to the <a href=\"https://wikipedia.org/wiki/House_of_Commons_of_the_United_Kingdom\" title=\"House of Commons of the United Kingdom\">House of Commons</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/World_War_II\" title=\"World War II\">World War II</a>: <a href=\"https://wikipedia.org/wiki/Battle_of_France\" title=\"Battle of France\">Germany's conquest of France</a> begins, as the German army crosses the <a href=\"https://wikipedia.org/wiki/Meuse\" title=\"Meuse\">Meuse</a>. <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>\" title=\"<PERSON>\"><PERSON></a> makes his \"<a href=\"https://wikipedia.org/wiki/Blood,_toil,_tears,_and_sweat\" class=\"mw-redirect\" title=\"Blood, toil, tears, and sweat\">blood, toil, tears, and sweat</a>\" speech to the <a href=\"https://wikipedia.org/wiki/House_of_Commons_of_the_United_Kingdom\" title=\"House of Commons of the United Kingdom\">House of Commons</a>.", "links": [{"title": "World War II", "link": "https://wikipedia.org/wiki/World_War_II"}, {"title": "Battle of France", "link": "https://wikipedia.org/wiki/Battle_of_France"}, {"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Meuse"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>"}, {"title": "Blood, toil, tears, and sweat", "link": "https://wikipedia.org/wiki/Blood,_toil,_tears,_and_sweat"}, {"title": "House of Commons of the United Kingdom", "link": "https://wikipedia.org/wiki/House_of_Commons_of_the_United_Kingdom"}]}, {"year": "1943", "text": "World War II: Operations Vulcan and Strike force the surrender of the last Axis troops in Tunisia.", "html": "1943 - World War II: <a href=\"https://wikipedia.org/wiki/Operations_Vulcan_and_Strike\" title=\"Operations Vulcan and Strike\">Operations Vulcan and Strike</a> force the surrender of the last Axis troops in Tunisia.", "no_year_html": "World War II: <a href=\"https://wikipedia.org/wiki/Operations_Vulcan_and_Strike\" title=\"Operations Vulcan and Strike\">Operations Vulcan and Strike</a> force the surrender of the last Axis troops in Tunisia.", "links": [{"title": "Operations Vulcan and Strike", "link": "https://wikipedia.org/wiki/Operations_Vulcan_and_Strike"}]}, {"year": "1945", "text": "World War II: <PERSON><PERSON><PERSON>'s photograph Raising a Flag over the Reichstag is published in Ogonyok magazine.", "html": "1945 - World War II: <a href=\"https://wikipedia.org/wiki/Ye<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>'s photograph <i><a href=\"https://wikipedia.org/wiki/Raising_a_Flag_over_the_Reichstag\" title=\"Raising a Flag over the Reichstag\">Raising a Flag over the Reichstag</a></i> is published in <i><a href=\"https://wikipedia.org/wiki/Ogonyok\" class=\"mw-redirect\" title=\"Ogonyok\">Ogonyok</a></i> magazine.", "no_year_html": "World War II: <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>'s photograph <i><a href=\"https://wikipedia.org/wiki/Raising_a_Flag_over_the_Reichstag\" title=\"Raising a Flag over the Reichstag\">Raising a Flag over the Reichstag</a></i> is published in <i><a href=\"https://wikipedia.org/wiki/Ogonyok\" class=\"mw-redirect\" title=\"Ogonyok\">Ogonyok</a></i> magazine.", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}, {"title": "Raising a Flag over the Reichstag", "link": "https://wikipedia.org/wiki/Raising_a_Flag_over_the_Reichstag"}, {"title": "Ogonyok", "link": "https://wikipedia.org/wiki/Ogonyok"}]}, {"year": "1948", "text": "Arab-Israeli War: The Kfar Etzion massacre occurs, a day prior to the Israeli Declaration of Independence.", "html": "1948 - <a href=\"https://wikipedia.org/wiki/1948_Arab%E2%80%93Israeli_War\" title=\"1948 Arab-Israeli War\">Arab-Israeli War</a>: The <a href=\"https://wikipedia.org/wiki/Kfar_Etzion_massacre\" title=\"Kfar Etzion massacre\">Kfar Etzion massacre</a> occurs, a day prior to the <a href=\"https://wikipedia.org/wiki/Israeli_Declaration_of_Independence\" title=\"Israeli Declaration of Independence\">Israeli Declaration of Independence</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/1948_Arab%E2%80%93Israeli_War\" title=\"1948 Arab-Israeli War\">Arab-Israeli War</a>: The <a href=\"https://wikipedia.org/wiki/Kfar_Etzion_massacre\" title=\"Kfar Etzion massacre\">Kfar Etzion massacre</a> occurs, a day prior to the <a href=\"https://wikipedia.org/wiki/Israeli_Declaration_of_Independence\" title=\"Israeli Declaration of Independence\">Israeli Declaration of Independence</a>.", "links": [{"title": "1948 Arab-Israeli War", "link": "https://wikipedia.org/wiki/1948_Arab%E2%80%93Israeli_War"}, {"title": "Kfar Etzion massacre", "link": "https://wikipedia.org/wiki/K<PERSON>_<PERSON><PERSON><PERSON>_massacre"}, {"title": "Israeli Declaration of Independence", "link": "https://wikipedia.org/wiki/Israeli_Declaration_of_Independence"}]}, {"year": "1950", "text": "The inaugural Formula One World Championship race takes place at Silverstone Circuit. The race was won by <PERSON>, who would go on to become the inaugural champion that year.", "html": "1950 - The <a href=\"https://wikipedia.org/wiki/1950_British_Grand_Prix\" title=\"1950 British Grand Prix\">inaugural</a> <a href=\"https://wikipedia.org/wiki/Formula_One\" title=\"Formula One\">Formula One</a> World Championship race takes place at <a href=\"https://wikipedia.org/wiki/Silverstone_Circuit\" title=\"Silverstone Circuit\">Silverstone Circuit</a>. The race was won by <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, who would go on to become the inaugural <a href=\"https://wikipedia.org/wiki/List_of_Formula_One_World_Drivers%27_Champions\" title=\"List of Formula One World Drivers' Champions\">champion</a> that year.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/1950_British_Grand_Prix\" title=\"1950 British Grand Prix\">inaugural</a> <a href=\"https://wikipedia.org/wiki/Formula_One\" title=\"Formula One\">Formula One</a> World Championship race takes place at <a href=\"https://wikipedia.org/wiki/Silverstone_Circuit\" title=\"Silverstone Circuit\">Silverstone Circuit</a>. The race was won by <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, who would go on to become the inaugural <a href=\"https://wikipedia.org/wiki/List_of_Formula_One_World_Drivers%27_Champions\" title=\"List of Formula One World Drivers' Champions\">champion</a> that year.", "links": [{"title": "1950 British Grand Prix", "link": "https://wikipedia.org/wiki/1950_British_Grand_Prix"}, {"title": "Formula One", "link": "https://wikipedia.org/wiki/Formula_One"}, {"title": "Silverstone Circuit", "link": "https://wikipedia.org/wiki/Silverstone_Circuit"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "List of Formula One World Drivers' Champions", "link": "https://wikipedia.org/wiki/List_of_Formula_One_World_Drivers%27_Champions"}]}, {"year": "1951", "text": "The 400th anniversary of the founding of the National University of San Marcos is commemorated by the opening of the first large-capacity stadium in Peru.", "html": "1951 - The 400th anniversary of the founding of the <a href=\"https://wikipedia.org/wiki/National_University_of_San_Marcos\" title=\"National University of San Marcos\">National University of San Marcos</a> is commemorated by the opening of the <a href=\"https://wikipedia.org/wiki/Estadio_Universidad_San_Marcos\" title=\"Estadio Universidad San Marcos\">first large-capacity stadium in Peru</a>.", "no_year_html": "The 400th anniversary of the founding of the <a href=\"https://wikipedia.org/wiki/National_University_of_San_Marcos\" title=\"National University of San Marcos\">National University of San Marcos</a> is commemorated by the opening of the <a href=\"https://wikipedia.org/wiki/Estadio_Universidad_San_Marcos\" title=\"Estadio Universidad San Marcos\">first large-capacity stadium in Peru</a>.", "links": [{"title": "National University of San Marcos", "link": "https://wikipedia.org/wiki/National_University_of_San_Marcos"}, {"title": "Estadio Universidad San Marcos", "link": "https://wikipedia.org/wiki/Estadio_Universidad_San_Marcos"}]}, {"year": "1952", "text": "The Rajya Sabha, the upper house of the Parliament of India, holds its first sitting.", "html": "1952 - The <a href=\"https://wikipedia.org/wiki/Rajya_Sabha\" title=\"Rajya Sabha\">Rajya Sabha</a>, the <a href=\"https://wikipedia.org/wiki/Upper_house\" title=\"Upper house\">upper house</a> of the <a href=\"https://wikipedia.org/wiki/Parliament_of_India\" title=\"Parliament of India\">Parliament of India</a>, holds its first sitting.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Rajya_Sabha\" title=\"Rajya Sabha\">Rajya Sabha</a>, the <a href=\"https://wikipedia.org/wiki/Upper_house\" title=\"Upper house\">upper house</a> of the <a href=\"https://wikipedia.org/wiki/Parliament_of_India\" title=\"Parliament of India\">Parliament of India</a>, holds its first sitting.", "links": [{"title": "Rajya Sabha", "link": "https://wikipedia.org/wiki/Rajya_Sabha"}, {"title": "Upper house", "link": "https://wikipedia.org/wiki/Upper_house"}, {"title": "Parliament of India", "link": "https://wikipedia.org/wiki/Parliament_of_India"}]}, {"year": "1954", "text": "The anti-National Service Riots, by Chinese middle school students in Singapore, take place.", "html": "1954 - The <a href=\"https://wikipedia.org/wiki/1954_National_Service_riots\" title=\"1954 National Service riots\">anti-National Service Riots</a>, by Chinese middle school students in <a href=\"https://wikipedia.org/wiki/Singapore\" title=\"Singapore\">Singapore</a>, take place.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/1954_National_Service_riots\" title=\"1954 National Service riots\">anti-National Service Riots</a>, by Chinese middle school students in <a href=\"https://wikipedia.org/wiki/Singapore\" title=\"Singapore\">Singapore</a>, take place.", "links": [{"title": "1954 National Service riots", "link": "https://wikipedia.org/wiki/1954_National_Service_riots"}, {"title": "Singapore", "link": "https://wikipedia.org/wiki/Singapore"}]}, {"year": "1958", "text": "During a visit to Caracas, Venezuela, the US Vice President <PERSON>'s car is attacked by anti-American demonstrators.", "html": "1958 - During a visit to <a href=\"https://wikipedia.org/wiki/Caracas\" title=\"Caracas\">Caracas</a>, Venezuela, the US Vice President <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>'s car <a href=\"https://wikipedia.org/wiki/Attack_on_<PERSON>_<PERSON>%27s_motorcade\" title=\"Attack on <PERSON>'s motorcade\">is attacked by anti-American demonstrators</a>.", "no_year_html": "During a visit to <a href=\"https://wikipedia.org/wiki/Caracas\" title=\"Caracas\">Caracas</a>, Venezuela, the US Vice President <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>'s car <a href=\"https://wikipedia.org/wiki/Attack_on_<PERSON>_<PERSON>%27s_motorcade\" title=\"Attack on <PERSON>'s motorcade\">is attacked by anti-American demonstrators</a>.", "links": [{"title": "Caracas", "link": "https://wikipedia.org/wiki/Caracas"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Attack on <PERSON>'s motorcade", "link": "https://wikipedia.org/wiki/Attack_on_<PERSON>_<PERSON>%27s_motorcade"}]}, {"year": "1958", "text": "May 1958 crisis: A group of French military officers lead a coup in Algiers demanding that a government of national unity be formed with <PERSON> at its head in order to defend French control of Algeria.", "html": "1958 - <a href=\"https://wikipedia.org/wiki/May_1958_crisis\" class=\"mw-redirect\" title=\"May 1958 crisis\">May 1958 crisis</a>: A group of French military officers lead a coup in <a href=\"https://wikipedia.org/wiki/Algiers\" title=\"Algiers\">Algiers</a> demanding that a government of national unity be formed with <a href=\"https://wikipedia.org/wiki/Charles<PERSON>\" title=\"<PERSON>\"><PERSON></a> at its head in order to defend <a href=\"https://wikipedia.org/wiki/French_Algeria\" title=\"French Algeria\">French control of Algeria</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/May_1958_crisis\" class=\"mw-redirect\" title=\"May 1958 crisis\">May 1958 crisis</a>: A group of French military officers lead a coup in <a href=\"https://wikipedia.org/wiki/Algiers\" title=\"Algiers\">Algiers</a> demanding that a government of national unity be formed with <a href=\"https://wikipedia.org/wiki/Charles<PERSON>\" title=\"<PERSON>\"><PERSON></a> at its head in order to defend <a href=\"https://wikipedia.org/wiki/French_Algeria\" title=\"French Algeria\">French control of Algeria</a>.", "links": [{"title": "May 1958 crisis", "link": "https://wikipedia.org/wiki/May_1958_crisis"}, {"title": "Algiers", "link": "https://wikipedia.org/wiki/Algiers"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "French Algeria", "link": "https://wikipedia.org/wiki/French_Algeria"}]}, {"year": "1958", "text": "<PERSON> becomes the first (and only) person to circumnavigate the world by amphibious vehicle, having travelled over 17,000 kilometres (11,000 mi) by sea and 62,000 kilometres (39,000 mi) by land during a ten-year journey.", "html": "1958 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> becomes the first (and only) person to <a href=\"https://wikipedia.org/wiki/Circumnavigation\" title=\"Circumnavigation\">circumnavigate</a> the world by <a href=\"https://wikipedia.org/wiki/Amphibious_vehicle\" title=\"Amphibious vehicle\">amphibious vehicle</a>, having travelled over 17,000 kilometres (11,000 mi) by sea and 62,000 kilometres (39,000 mi) by land during a ten-year journey.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> becomes the first (and only) person to <a href=\"https://wikipedia.org/wiki/Circumnavigation\" title=\"Circumnavigation\">circumnavigate</a> the world by <a href=\"https://wikipedia.org/wiki/Amphibious_vehicle\" title=\"Amphibious vehicle\">amphibious vehicle</a>, having travelled over 17,000 kilometres (11,000 mi) by sea and 62,000 kilometres (39,000 mi) by land during a ten-year journey.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Circumnavigation", "link": "https://wikipedia.org/wiki/Circumnavigation"}, {"title": "Amphibious vehicle", "link": "https://wikipedia.org/wiki/Amphibious_vehicle"}]}, {"year": "1960", "text": "Hundreds of University of California, Berkeley students congregate for the first day of protest against a visit by the House Committee on Un-American Activities.", "html": "1960 - Hundreds of <a href=\"https://wikipedia.org/wiki/University_of_California,_Berkeley\" title=\"University of California, Berkeley\">University of California, Berkeley</a> students congregate for the first day of protest against a visit by the <a href=\"https://wikipedia.org/wiki/House_Un-American_Activities_Committee\" title=\"House Un-American Activities Committee\">House Committee on Un-American Activities</a>.", "no_year_html": "Hundreds of <a href=\"https://wikipedia.org/wiki/University_of_California,_Berkeley\" title=\"University of California, Berkeley\">University of California, Berkeley</a> students congregate for the first day of protest against a visit by the <a href=\"https://wikipedia.org/wiki/House_Un-American_Activities_Committee\" title=\"House Un-American Activities Committee\">House Committee on Un-American Activities</a>.", "links": [{"title": "University of California, Berkeley", "link": "https://wikipedia.org/wiki/University_of_California,_Berkeley"}, {"title": "House Un-American Activities Committee", "link": "https://wikipedia.org/wiki/House_Un-American_Activities_Committee"}]}, {"year": "1967", "text": "Dr. <PERSON><PERSON><PERSON> becomes the third President of India. He is the first Muslim President of the Indian Union. He holds this position until August 24, 1969.", "html": "1967 - Dr. <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a> becomes the third <a href=\"https://wikipedia.org/wiki/President_of_India\" title=\"President of India\">President of India</a>. He is the first Muslim President of the Indian Union. He holds this position until August 24, 1969.", "no_year_html": "Dr. <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a> becomes the third <a href=\"https://wikipedia.org/wiki/President_of_India\" title=\"President of India\">President of India</a>. He is the first Muslim President of the Indian Union. He holds this position until August 24, 1969.", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}, {"title": "President of India", "link": "https://wikipedia.org/wiki/President_of_India"}]}, {"year": "1969", "text": "In the aftermath of the 1969 Malaysian general election, Sino-Malay sectarian violence erupted in Kuala Lumpur, Malaysia.", "html": "1969 - In the aftermath of the <a href=\"https://wikipedia.org/wiki/1969_Malaysian_general_election\" title=\"1969 Malaysian general election\">1969 Malaysian general election</a>, <a href=\"https://wikipedia.org/wiki/13_May_incident\" title=\"13 May incident\">Sino-Malay sectarian violence erupted</a> in <a href=\"https://wikipedia.org/wiki/Kuala_Lumpur\" title=\"Kuala Lumpur\">Kuala Lumpur</a>, Malaysia.", "no_year_html": "In the aftermath of the <a href=\"https://wikipedia.org/wiki/1969_Malaysian_general_election\" title=\"1969 Malaysian general election\">1969 Malaysian general election</a>, <a href=\"https://wikipedia.org/wiki/13_May_incident\" title=\"13 May incident\">Sino-Malay sectarian violence erupted</a> in <a href=\"https://wikipedia.org/wiki/Kuala_Lumpur\" title=\"Kuala Lumpur\">Kuala Lumpur</a>, Malaysia.", "links": [{"title": "1969 Malaysian general election", "link": "https://wikipedia.org/wiki/1969_Malaysian_general_election"}, {"title": "13 May incident", "link": "https://wikipedia.org/wiki/13_May_incident"}, {"title": "Kuala Lumpur", "link": "https://wikipedia.org/wiki/Kuala_Lumpur"}]}, {"year": "1972", "text": "A fire occurs in the Sennichi Department Store in Osaka, Japan. Blocked exits and non-functional elevators result in 118 fatalities (many victims leaping to their deaths).", "html": "1972 - A <a href=\"https://wikipedia.org/wiki/Sennichi_Department_Store_Building_fire\" title=\"Sennichi Department Store Building fire\">fire</a> occurs in the Sennichi Department Store in <a href=\"https://wikipedia.org/wiki/Osaka\" title=\"Osaka\">Osaka</a>, Japan. Blocked exits and non-functional elevators result in 118 fatalities (many victims leaping to their deaths).", "no_year_html": "A <a href=\"https://wikipedia.org/wiki/Sennichi_Department_Store_Building_fire\" title=\"Sennichi Department Store Building fire\">fire</a> occurs in the Sennichi Department Store in <a href=\"https://wikipedia.org/wiki/Osaka\" title=\"Osaka\">Osaka</a>, Japan. Blocked exits and non-functional elevators result in 118 fatalities (many victims leaping to their deaths).", "links": [{"title": "Sennichi Department Store Building fire", "link": "https://wikipedia.org/wiki/Sennichi_Department_Store_Building_fire"}, {"title": "Osaka", "link": "https://wikipedia.org/wiki/Osaka"}]}, {"year": "1972", "text": "The Troubles: A car bombing outside a crowded pub in Belfast sparks a two-day gun battle involving the Provisional IRA, Ulster Volunteer Force and British Army. Seven people are killed and over 66 injured.", "html": "1972 - <a href=\"https://wikipedia.org/wiki/The_Troubles\" title=\"The Troubles\">The Troubles</a>: A car bombing outside a crowded pub in <a href=\"https://wikipedia.org/wiki/Belfast\" title=\"Belfast\">Belfast</a> sparks <a href=\"https://wikipedia.org/wiki/Battle_at_Springmartin\" title=\"Battle at Springmartin\">a two-day gun battle</a> involving the <a href=\"https://wikipedia.org/wiki/Provisional_Irish_Republican_Army\" title=\"Provisional Irish Republican Army\">Provisional IRA</a>, <a href=\"https://wikipedia.org/wiki/Ulster_Volunteer_Force\" title=\"Ulster Volunteer Force\">Ulster Volunteer Force</a> and British Army. Seven people are killed and over 66 injured.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/The_Troubles\" title=\"The Troubles\">The Troubles</a>: A car bombing outside a crowded pub in <a href=\"https://wikipedia.org/wiki/Belfast\" title=\"Belfast\">Belfast</a> sparks <a href=\"https://wikipedia.org/wiki/Battle_at_Springmartin\" title=\"Battle at Springmartin\">a two-day gun battle</a> involving the <a href=\"https://wikipedia.org/wiki/Provisional_Irish_Republican_Army\" title=\"Provisional Irish Republican Army\">Provisional IRA</a>, <a href=\"https://wikipedia.org/wiki/Ulster_Volunteer_Force\" title=\"Ulster Volunteer Force\">Ulster Volunteer Force</a> and British Army. Seven people are killed and over 66 injured.", "links": [{"title": "The Troubles", "link": "https://wikipedia.org/wiki/The_Troubles"}, {"title": "Belfast", "link": "https://wikipedia.org/wiki/Belfast"}, {"title": "Battle at Springmartin", "link": "https://wikipedia.org/wiki/Battle_at_Springmartin"}, {"title": "Provisional Irish Republican Army", "link": "https://wikipedia.org/wiki/Provisional_Irish_Republican_Army"}, {"title": "Ulster Volunteer Force", "link": "https://wikipedia.org/wiki/Ulster_Volunteer_Force"}]}, {"year": "1980", "text": "An F3 tornado hits Kalamazoo County, Michigan. President <PERSON> declares it a federal disaster area.", "html": "1980 - An <a href=\"https://wikipedia.org/wiki/Fujita_scale\" title=\"Fujita scale\">F3 tornado</a> <a href=\"https://wikipedia.org/wiki/1980_Kalamazoo_tornado\" title=\"1980 Kalamazoo tornado\">hits</a> <a href=\"https://wikipedia.org/wiki/Kalamazoo_County,_Michigan\" title=\"Kalamazoo County, Michigan\">Kalamazoo County, Michigan</a>. President <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> declares it a federal <a href=\"https://wikipedia.org/wiki/Disaster_area\" title=\"Disaster area\">disaster area</a>.", "no_year_html": "An <a href=\"https://wikipedia.org/wiki/Fujita_scale\" title=\"Fujita scale\">F3 tornado</a> <a href=\"https://wikipedia.org/wiki/1980_Kalamazoo_tornado\" title=\"1980 Kalamazoo tornado\">hits</a> <a href=\"https://wikipedia.org/wiki/Kalamazoo_County,_Michigan\" title=\"Kalamazoo County, Michigan\">Kalamazoo County, Michigan</a>. President <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> declares it a federal <a href=\"https://wikipedia.org/wiki/Disaster_area\" title=\"Disaster area\">disaster area</a>.", "links": [{"title": "Fujita scale", "link": "https://wikipedia.org/wiki/Fujita_scale"}, {"title": "1980 Kalamazoo tornado", "link": "https://wikipedia.org/wiki/1980_Kalamazoo_tornado"}, {"title": "Kalamazoo County, Michigan", "link": "https://wikipedia.org/wiki/Kalamazoo_County,_Michigan"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Disaster area", "link": "https://wikipedia.org/wiki/Disaster_area"}]}, {"year": "1981", "text": "<PERSON><PERSON><PERSON> attempts to assassinate Pope <PERSON> in St. Peter's Square in Rome. The Pope is rushed to the Agostino Gemelli University Polyclinic to undergo emergency surgery and survives.", "html": "1981 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>%C4%9Fca\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a> <a href=\"https://wikipedia.org/wiki/Attempted_assassination_of_<PERSON>_<PERSON>_<PERSON>_<PERSON>\" title=\"Attempted assassination of Pope <PERSON>\">attempts to assassinate</a> <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Pope John <PERSON> II\">Pope <PERSON> II</a> in <a href=\"https://wikipedia.org/wiki/St._Peter%27s_Square\" title=\"St. Peter's Square\">St. Peter's Square</a> in Rome. The <PERSON> is rushed to the <a href=\"https://wikipedia.org/wiki/Agostino_Gemelli_University_Polyclinic\" class=\"mw-redirect\" title=\"Agostino Gemelli University Polyclinic\">Agostino Gemelli University Polyclinic</a> to undergo emergency <a href=\"https://wikipedia.org/wiki/Surgery\" title=\"Surgery\">surgery</a> and survives.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>%C4%9Fca\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a> <a href=\"https://wikipedia.org/wiki/Attempted_assassination_of_<PERSON>_<PERSON>_<PERSON>_<PERSON>\" title=\"Attempted assassination of Pope <PERSON>\">attempts to assassinate</a> <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Pope John <PERSON> II\">Pope <PERSON> II</a> in <a href=\"https://wikipedia.org/wiki/St._Peter%27s_Square\" title=\"St. Peter's Square\">St. Peter's Square</a> in Rome. The Pope is rushed to the <a href=\"https://wikipedia.org/wiki/Agostino_Gemelli_University_Polyclinic\" class=\"mw-redirect\" title=\"Agostino Gemelli University Polyclinic\">Agostino Gemelli University Polyclinic</a> to undergo emergency <a href=\"https://wikipedia.org/wiki/Surgery\" title=\"Surgery\">surgery</a> and survives.", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_A%C4%9Fca"}, {"title": "Attempted assassination of Pope <PERSON>", "link": "https://wikipedia.org/wiki/Attempted_assassination_of_<PERSON>_<PERSON>_<PERSON>_<PERSON>"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>"}, {"title": "St. Peter's Square", "link": "https://wikipedia.org/wiki/St._Peter%27s_Square"}, {"title": "Agostino Gemelli University Polyclinic", "link": "https://wikipedia.org/wiki/Agostino_Gemelli_University_Polyclinic"}, {"title": "Surgery", "link": "https://wikipedia.org/wiki/Surgery"}]}, {"year": "1985", "text": "Police bombed MOVE headquarters in Philadelphia, killing six adults and five children, and destroying the homes of 250 city residents.", "html": "1985 - Police <a href=\"https://wikipedia.org/wiki/1985_MOVE_bombing\" title=\"1985 MOVE bombing\">bombed MOVE headquarters</a> in <a href=\"https://wikipedia.org/wiki/Philadelphia\" title=\"Philadelphia\">Philadelphia</a>, killing six adults and five children, and destroying the homes of 250 city residents.", "no_year_html": "Police <a href=\"https://wikipedia.org/wiki/1985_MOVE_bombing\" title=\"1985 MOVE bombing\">bombed MOVE headquarters</a> in <a href=\"https://wikipedia.org/wiki/Philadelphia\" title=\"Philadelphia\">Philadelphia</a>, killing six adults and five children, and destroying the homes of 250 city residents.", "links": [{"title": "1985 MOVE bombing", "link": "https://wikipedia.org/wiki/1985_MOVE_bombing"}, {"title": "Philadelphia", "link": "https://wikipedia.org/wiki/Philadelphia"}]}, {"year": "1989", "text": "Large groups of students occupy Tiananmen Square and begin a hunger strike.", "html": "1989 - Large groups of students <a href=\"https://wikipedia.org/wiki/Tiananmen_Square_protests_of_1989\" class=\"mw-redirect\" title=\"Tiananmen Square protests of 1989\">occupy</a> <a href=\"https://wikipedia.org/wiki/Tiananmen_Square\" title=\"Tiananmen Square\">Tiananmen Square</a> and begin a hunger strike.", "no_year_html": "Large groups of students <a href=\"https://wikipedia.org/wiki/Tiananmen_Square_protests_of_1989\" class=\"mw-redirect\" title=\"Tiananmen Square protests of 1989\">occupy</a> <a href=\"https://wikipedia.org/wiki/Tiananmen_Square\" title=\"Tiananmen Square\">Tiananmen Square</a> and begin a hunger strike.", "links": [{"title": "Tiananmen Square protests of 1989", "link": "https://wikipedia.org/wiki/Tiananmen_Square_protests_of_1989"}, {"title": "Tiananmen Square", "link": "https://wikipedia.org/wiki/Tiananmen_Square"}]}, {"year": "1990", "text": "The Dinamo-Red Star riot took place at Maksimir Stadium in Zagreb, Croatia between the Bad Blue Boys (fans of Dinamo Zagreb) and the Delije (fans of Red Star Belgrade).", "html": "1990 - The <a href=\"https://wikipedia.org/wiki/Dinamo%E2%80%93Red_Star_riot\" title=\"Dinamo-Red Star riot\">Dinamo-Red Star riot</a> took place at <a href=\"https://wikipedia.org/wiki/Stadion_Maksimir\" title=\"Stadion Maksimir\">Maksimir Stadium</a> in <a href=\"https://wikipedia.org/wiki/Zagreb\" title=\"Zagreb\">Zagreb</a>, Croatia between the Bad Blue Boys (fans of <a href=\"https://wikipedia.org/wiki/GNK_Dinamo_Zagreb\" title=\"GNK Dinamo Zagreb\">Dinamo Zagreb</a>) and the <a href=\"https://wikipedia.org/wiki/Delije\" title=\"Delije\">Delije</a> (fans of <a href=\"https://wikipedia.org/wiki/Red_Star_Belgrade\" title=\"Red Star Belgrade\">Red Star Belgrade</a>).", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Dinamo%E2%80%93Red_Star_riot\" title=\"Dinamo-Red Star riot\">Dinamo-Red Star riot</a> took place at <a href=\"https://wikipedia.org/wiki/Stadion_Maksimir\" title=\"Stadion Maksimir\">Maksimir Stadium</a> in <a href=\"https://wikipedia.org/wiki/Zagreb\" title=\"Zagreb\">Zagreb</a>, Croatia between the Bad Blue Boys (fans of <a href=\"https://wikipedia.org/wiki/GNK_Dinamo_Zagreb\" title=\"GNK Dinamo Zagreb\">Dinamo Zagreb</a>) and the <a href=\"https://wikipedia.org/wiki/Delije\" title=\"Delije\">Delije</a> (fans of <a href=\"https://wikipedia.org/wiki/Red_Star_Belgrade\" title=\"Red Star Belgrade\">Red Star Belgrade</a>).", "links": [{"title": "Dinamo-Red Star riot", "link": "https://wikipedia.org/wiki/Dinamo%E2%80%93Red_Star_riot"}, {"title": "Stadion Maksimir", "link": "https://wikipedia.org/wiki/Stadion_Maksimir"}, {"title": "Zagreb", "link": "https://wikipedia.org/wiki/Zagreb"}, {"title": "GNK Dinamo Zagreb", "link": "https://wikipedia.org/wiki/GNK_Dinamo_Zagreb"}, {"title": "Delije", "link": "https://wikipedia.org/wiki/Delije"}, {"title": "Red Star Belgrade", "link": "https://wikipedia.org/wiki/Red_Star_Belgrade"}]}, {"year": "1992", "text": "<PERSON> gives the first public lecture on Falun Gong in Changchun, People's Republic of China.", "html": "1992 - <a href=\"https://wikipedia.org/wiki/Li_Hongzhi\" title=\"Li Hongzhi\"><PERSON></a> gives the first public lecture on <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"Fal<PERSON> Gong\"><PERSON><PERSON><PERSON></a> in <a href=\"https://wikipedia.org/wiki/Changchun\" title=\"Changchun\">Changchun</a>, People's Republic of China.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Li_Hongzhi\" title=\"Li Hongzhi\"><PERSON></a> gives the first public lecture on <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_Gong\" title=\"<PERSON>al<PERSON> Gong\"><PERSON><PERSON><PERSON></a> in <a href=\"https://wikipedia.org/wiki/Changchun\" title=\"Changchun\">Changchun</a>, People's Republic of China.", "links": [{"title": "Li Hongzhi", "link": "https://wikipedia.org/wiki/Li_Hongzhi"}, {"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>al<PERSON>_<PERSON>"}, {"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Chang<PERSON>n"}]}, {"year": "1995", "text": "<PERSON>, a 33-year-old British mother, becomes the first woman to conquer Everest without oxygen or the help of sherpas.", "html": "1995 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, a 33-year-old British mother, becomes the first woman to conquer <a href=\"https://wikipedia.org/wiki/Everest\" class=\"mw-redirect\" title=\"Everest\">Everest</a> without oxygen or the help of sherpas.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, a 33-year-old British mother, becomes the first woman to conquer <a href=\"https://wikipedia.org/wiki/Everest\" class=\"mw-redirect\" title=\"Everest\">Everest</a> without oxygen or the help of sherpas.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Everest", "link": "https://wikipedia.org/wiki/Everest"}]}, {"year": "1996", "text": "Severe thunderstorms and a tornado in Bangladesh kill 600 people.", "html": "1996 - Severe <a href=\"https://wikipedia.org/wiki/Thunderstorm\" title=\"Thunderstorm\">thunderstorms</a> and a <a href=\"https://wikipedia.org/wiki/Tornado\" title=\"Tornado\">tornado</a> in <a href=\"https://wikipedia.org/wiki/Bangladesh\" title=\"Bangladesh\">Bangladesh</a> kill 600 people.", "no_year_html": "Severe <a href=\"https://wikipedia.org/wiki/Thunderstorm\" title=\"Thunderstorm\">thunderstorms</a> and a <a href=\"https://wikipedia.org/wiki/Tornado\" title=\"Tornado\">tornado</a> in <a href=\"https://wikipedia.org/wiki/Bangladesh\" title=\"Bangladesh\">Bangladesh</a> kill 600 people.", "links": [{"title": "Thunderstorm", "link": "https://wikipedia.org/wiki/Thunderstorm"}, {"title": "Tornado", "link": "https://wikipedia.org/wiki/Tornado"}, {"title": "Bangladesh", "link": "https://wikipedia.org/wiki/Bangladesh"}]}, {"year": "1998", "text": "Race riots break out in Jakarta, Indonesia, where shops owned by Indonesians of Chinese descent are looted and women raped.", "html": "1998 - <a href=\"https://wikipedia.org/wiki/May_1998_riots_of_Indonesia\" title=\"May 1998 riots of Indonesia\">Race riots</a> break out in <a href=\"https://wikipedia.org/wiki/Jakarta\" title=\"Jakarta\">Jakarta</a>, Indonesia, where shops owned by <a href=\"https://wikipedia.org/wiki/Chinese_Indonesians\" title=\"Chinese Indonesians\">Indonesians of Chinese descent</a> are looted and women raped.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/May_1998_riots_of_Indonesia\" title=\"May 1998 riots of Indonesia\">Race riots</a> break out in <a href=\"https://wikipedia.org/wiki/Jakarta\" title=\"Jakarta\">Jakarta</a>, Indonesia, where shops owned by <a href=\"https://wikipedia.org/wiki/Chinese_Indonesians\" title=\"Chinese Indonesians\">Indonesians of Chinese descent</a> are looted and women raped.", "links": [{"title": "May 1998 riots of Indonesia", "link": "https://wikipedia.org/wiki/May_1998_riots_of_Indonesia"}, {"title": "Jakarta", "link": "https://wikipedia.org/wiki/Jakarta"}, {"title": "Chinese Indonesians", "link": "https://wikipedia.org/wiki/Chinese_Indonesians"}]}, {"year": "1998", "text": "India carries out two nuclear weapon tests at Pokhran, following the three conducted on May 11. The United States and Japan impose economic sanctions on India.", "html": "1998 - India carries out two <a href=\"https://wikipedia.org/wiki/Pokhran-II\" title=\"Pokhran-II\">nuclear weapon tests at Pokhran</a>, following the three conducted on <a href=\"https://wikipedia.org/wiki/May_11\" title=\"May 11\">May 11</a>. The United States and Japan impose economic sanctions on India.", "no_year_html": "India carries out two <a href=\"https://wikipedia.org/wiki/Pokhran-II\" title=\"Pokhran-II\">nuclear weapon tests at Pokhran</a>, following the three conducted on <a href=\"https://wikipedia.org/wiki/May_11\" title=\"May 11\">May 11</a>. The United States and Japan impose economic sanctions on India.", "links": [{"title": "Pokhran-II", "link": "https://wikipedia.org/wiki/Pokhran-II"}, {"title": "May 11", "link": "https://wikipedia.org/wiki/May_11"}]}, {"year": "2000", "text": "A fireworks storage depot explodes in a residential neighborhood in Enschede, Netherlands, killing 23 people and injuring 950 others.", "html": "2000 - A fireworks storage depot <a href=\"https://wikipedia.org/wiki/Enschede_fireworks_disaster\" title=\"Enschede fireworks disaster\">explodes in a residential neighborhood</a> in <a href=\"https://wikipedia.org/wiki/Enschede\" title=\"Enschede\">Enschede</a>, Netherlands, killing 23 people and injuring 950 others.", "no_year_html": "A fireworks storage depot <a href=\"https://wikipedia.org/wiki/Enschede_fireworks_disaster\" title=\"Enschede fireworks disaster\">explodes in a residential neighborhood</a> in <a href=\"https://wikipedia.org/wiki/Enschede\" title=\"Enschede\">Enschede</a>, Netherlands, killing 23 people and injuring 950 others.", "links": [{"title": "Enschede fireworks disaster", "link": "https://wikipedia.org/wiki/Enschede_fireworks_disaster"}, {"title": "Enschede", "link": "https://wikipedia.org/wiki/Enschede"}]}, {"year": "2005", "text": "Andijan uprising, Uzbekistan; Troops open fire on crowds of protestors after a prison break; at least 187 people were killed according to official estimates.", "html": "2005 - <a href=\"https://wikipedia.org/wiki/2005_Andijan_Unrest\" class=\"mw-redirect\" title=\"2005 Andijan Unrest\">Andijan uprising</a>, <a href=\"https://wikipedia.org/wiki/Uzbekistan\" title=\"Uzbekistan\">Uzbekistan</a>; Troops open fire on crowds of protestors after a prison break; at least 187 people were killed according to official estimates.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/2005_Andijan_Unrest\" class=\"mw-redirect\" title=\"2005 Andijan Unrest\">Andijan uprising</a>, <a href=\"https://wikipedia.org/wiki/Uzbekistan\" title=\"Uzbekistan\">Uzbekistan</a>; Troops open fire on crowds of protestors after a prison break; at least 187 people were killed according to official estimates.", "links": [{"title": "2005 Andijan Unrest", "link": "https://wikipedia.org/wiki/2005_Andijan_Unrest"}, {"title": "Uzbekistan", "link": "https://wikipedia.org/wiki/Uzbekistan"}]}, {"year": "2006", "text": "São Paulo violence: Rebellions occur in several prisons in Brazil.", "html": "2006 - <a href=\"https://wikipedia.org/wiki/May_2006_S%C3%A3o_Paulo_violence\" class=\"mw-redirect\" title=\"May 2006 São Paulo violence\">São Paulo violence</a>: Rebellions occur in several prisons in Brazil.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/May_2006_S%C3%A3o_Paulo_violence\" class=\"mw-redirect\" title=\"May 2006 São Paulo violence\">São Paulo violence</a>: Rebellions occur in several prisons in Brazil.", "links": [{"title": "May 2006 São Paulo violence", "link": "https://wikipedia.org/wiki/May_2006_S%C3%A3o_Paulo_violence"}]}, {"year": "2011", "text": "Two bombs explode in the Charsadda District of Pakistan killing 98 people and wounding 140 others.", "html": "2011 - <a href=\"https://wikipedia.org/wiki/2011_Charsadda_bombing\" title=\"2011 Charsadda bombing\">Two bombs explode</a> in the <a href=\"https://wikipedia.org/wiki/Charsadda_District,_Pakistan\" title=\"Charsadda District, Pakistan\">Charsadda District</a> of Pakistan killing 98 people and wounding 140 others.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/2011_Charsadda_bombing\" title=\"2011 Charsadda bombing\">Two bombs explode</a> in the <a href=\"https://wikipedia.org/wiki/Charsadda_District,_Pakistan\" title=\"Charsadda District, Pakistan\">Charsadda District</a> of Pakistan killing 98 people and wounding 140 others.", "links": [{"title": "2011 Charsadda bombing", "link": "https://wikipedia.org/wiki/2011_Charsadda_bombing"}, {"title": "Charsadda District, Pakistan", "link": "https://wikipedia.org/wiki/Charsadda_District,_Pakistan"}]}, {"year": "2012", "text": "Forty-nine dismembered bodies are discovered by Mexican authorities on Mexican Federal Highway 40.", "html": "2012 - Forty-nine dismembered bodies are <a href=\"https://wikipedia.org/wiki/Cadereyta_Jim%C3%A9nez_massacre\" title=\"Cadereyta Jiménez massacre\">discovered</a> by Mexican authorities on <a href=\"https://wikipedia.org/wiki/Mexican_Federal_Highway_40\" title=\"Mexican Federal Highway 40\">Mexican Federal Highway 40</a>.", "no_year_html": "Forty-nine dismembered bodies are <a href=\"https://wikipedia.org/wiki/Cadereyta_Jim%C3%A9nez_massacre\" title=\"Cadereyta Jiménez massacre\">discovered</a> by Mexican authorities on <a href=\"https://wikipedia.org/wiki/Mexican_Federal_Highway_40\" title=\"Mexican Federal Highway 40\">Mexican Federal Highway 40</a>.", "links": [{"title": "<PERSON><PERSON><PERSON> massacre", "link": "https://wikipedia.org/wiki/Cadereyta_Jim%C3%A9nez_massacre"}, {"title": "Mexican Federal Highway 40", "link": "https://wikipedia.org/wiki/Mexican_Federal_Highway_40"}]}, {"year": "2013", "text": "American physician <PERSON><PERSON><PERSON> is found guilty in Pennsylvania of murdering three infants born alive during attempted abortions, involuntary manslaughter of a woman during an abortion procedure, and other charges.", "html": "2013 - American physician <a href=\"https://wikipedia.org/wiki/Kermit_Gosnell\" title=\"<PERSON><PERSON><PERSON> Go<PERSON>nell\"><PERSON><PERSON><PERSON></a> is found guilty in <a href=\"https://wikipedia.org/wiki/Pennsylvania\" title=\"Pennsylvania\">Pennsylvania</a> of murdering three infants born alive during attempted abortions, <a href=\"https://wikipedia.org/wiki/Involuntary_manslaughter\" class=\"mw-redirect\" title=\"Involuntary manslaughter\">involuntary manslaughter</a> of a woman during an abortion procedure, and other charges.", "no_year_html": "American physician <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_Go<PERSON>nell\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a> is found guilty in <a href=\"https://wikipedia.org/wiki/Pennsylvania\" title=\"Pennsylvania\">Pennsylvania</a> of murdering three infants born alive during attempted abortions, <a href=\"https://wikipedia.org/wiki/Involuntary_manslaughter\" class=\"mw-redirect\" title=\"Involuntary manslaughter\">involuntary manslaughter</a> of a woman during an abortion procedure, and other charges.", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Kermit_Go<PERSON>nell"}, {"title": "Pennsylvania", "link": "https://wikipedia.org/wiki/Pennsylvania"}, {"title": "Involuntary manslaughter", "link": "https://wikipedia.org/wiki/Involuntary_manslaughter"}]}, {"year": "2014", "text": "An explosion at an underground coal mine in southwest Turkey kills 301 miners.", "html": "2014 - An <a href=\"https://wikipedia.org/wiki/Soma_mine_disaster\" title=\"Soma mine disaster\">explosion</a> at an underground coal mine in southwest <a href=\"https://wikipedia.org/wiki/Turkey\" title=\"Turkey\">Turkey</a> kills 301 miners.", "no_year_html": "An <a href=\"https://wikipedia.org/wiki/Soma_mine_disaster\" title=\"Soma mine disaster\">explosion</a> at an underground coal mine in southwest <a href=\"https://wikipedia.org/wiki/Turkey\" title=\"Turkey\">Turkey</a> kills 301 miners.", "links": [{"title": "Soma mine disaster", "link": "https://wikipedia.org/wiki/Soma_mine_disaster"}, {"title": "Turkey", "link": "https://wikipedia.org/wiki/Turkey"}]}], "Births": [{"year": "1024", "text": "<PERSON> of Cluny, French abbot and saint (d. 1109)", "html": "1024 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_Cluny\" title=\"<PERSON> of Cluny\"><PERSON> of Cluny</a>, French abbot and saint (d. 1109)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_Clu<PERSON>\" title=\"<PERSON> of Cluny\"><PERSON> of Cluny</a>, French abbot and saint (d. 1109)", "links": [{"title": "<PERSON> of Cluny", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1179", "text": "<PERSON><PERSON><PERSON> <PERSON>, Count of Champagne (d. 1201)", "html": "1179 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_III,_Count_of_Champagne\" title=\"<PERSON><PERSON><PERSON> III, Count of Champagne\"><PERSON><PERSON><PERSON> III, Count of Champagne</a> (d. 1201)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_III,_Count_of_Champagne\" title=\"<PERSON><PERSON><PERSON> III, Count of Champagne\"><PERSON><PERSON><PERSON>, Count of Champagne</a> (d. 1201)", "links": [{"title": "<PERSON><PERSON><PERSON> <PERSON>, Count of Champagne", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>,_Count_of_Champagne"}]}, {"year": "1221", "text": "<PERSON>, Russian prince and saint (d. 1263)", "html": "1221 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian prince and saint (d. 1263)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian prince and saint (d. 1263)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1254", "text": "<PERSON>ant, Queen of France (d. 1321)", "html": "1254 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>,_Queen_of_France\" title=\"<PERSON>ant, Queen of France\"><PERSON>, Queen of France</a> (d. 1321)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>,_Queen_of_France\" title=\"<PERSON>, Queen of France\"><PERSON>ant, Queen of France</a> (d. 1321)", "links": [{"title": "<PERSON> Brabant, Queen of France", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>,_Queen_of_France"}]}, {"year": "1453", "text": "<PERSON>, Countess of Arran, Scottish princess (d. 1488)", "html": "1453 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Countess_of_Arran\" title=\"<PERSON>, Countess of Arran\"><PERSON>, Countess of Arran</a>, Scottish princess (d. 1488)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Countess_of_Arran\" title=\"<PERSON>, Countess of Arran\"><PERSON>, Countess of Arran</a>, Scottish princess (d. 1488)", "links": [{"title": "<PERSON>, Countess of Arran", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Countess_<PERSON>_<PERSON><PERSON>n"}]}, {"year": "1588", "text": "<PERSON>, Danish physician and historian (d. 1654)", "html": "1588 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Worm\"><PERSON></a>, Danish physician and historian (d. 1654)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Ole Worm\"><PERSON></a>, Danish physician and historian (d. 1654)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>orm"}]}, {"year": "1638", "text": "<PERSON>, French priest and scholar (d. 1712)", "html": "1638 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>(priest)\" title=\"<PERSON> (priest)\"><PERSON></a>, French priest and scholar (d. 1712)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>(priest)\" title=\"<PERSON> (priest)\"><PERSON></a>, French priest and scholar (d. 1712)", "links": [{"title": "<PERSON> (priest)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>(priest)"}]}, {"year": "1699", "text": "<PERSON><PERSON><PERSON><PERSON>, 1st Marquis of Pombal, Portuguese politician, Prime Minister of Portugal (d. 1782)", "html": "1699 - <a href=\"https://wikipedia.org/wiki/Sebasti%C3%A3o_Jos%C3%A<PERSON>_<PERSON>_<PERSON>_<PERSON>_<PERSON>,_1st_Marquis_of_Pombal\" title=\"<PERSON><PERSON><PERSON><PERSON>, 1st Marquis of Pombal\"><PERSON><PERSON><PERSON><PERSON>, 1st Marquis of Pombal</a>, Portuguese politician, <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Portugal\" title=\"Prime Minister of Portugal\">Prime Minister of Portugal</a> (d. 1782)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Sebasti%C3%A3o_Jos%C3%A<PERSON>_<PERSON>_<PERSON>_<PERSON>_<PERSON>,_1st_Marquis_of_Pombal\" title=\"<PERSON><PERSON><PERSON><PERSON>, 1st Marquis of Pombal\"><PERSON><PERSON><PERSON><PERSON>, 1st Marquis of Pombal</a>, Portuguese politician, <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Portugal\" title=\"Prime Minister of Portugal\">Prime Minister of Portugal</a> (d. 1782)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>, 1st Marquis of Pombal", "link": "https://wikipedia.org/wiki/Sebasti%C3%A3o_Jos%C3%A9_<PERSON>_<PERSON><PERSON><PERSON>_e_<PERSON>,_1st_Marquis_of_Pombal"}, {"title": "Prime Minister of Portugal", "link": "https://wikipedia.org/wiki/Prime_Minister_of_Portugal"}]}, {"year": "1712", "text": "Count <PERSON>, Danish politician and diplomat (d. 1772)", "html": "1712 - <a href=\"https://wikipedia.org/wiki/Count_<PERSON>_<PERSON>_<PERSON>\" title=\"Count <PERSON>\">Count <PERSON></a>, Danish politician and diplomat (d. 1772)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Count_<PERSON>_<PERSON>_<PERSON>\" title=\"Count <PERSON>\">Count <PERSON></a>, Danish politician and diplomat (d. 1772)", "links": [{"title": "Count <PERSON>", "link": "https://wikipedia.org/wiki/Count_<PERSON>_<PERSON>_<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1713", "text": "<PERSON>, French mathematician, astronomer, and geophysicist (d. 1765)", "html": "1713 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French mathematician, astronomer, and geophysicist (d. 1765)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French mathematician, astronomer, and geophysicist (d. 1765)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1717", "text": "<PERSON>, Archduchess, Queen, and Empress; Austrian wife of <PERSON>, Holy Roman Emperor (d. 1780)", "html": "1717 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Archduchess, Queen, and Empress; Austrian wife of <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Holy_Roman_Emperor\" title=\"<PERSON>, Holy Roman Emperor\"><PERSON>, Holy Roman Emperor</a> (d. 1780)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>\" title=\"<PERSON>\"><PERSON></a>, Archduchess, Queen, and Empress; Austrian wife of <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Holy_Roman_Emperor\" title=\"<PERSON>, Holy Roman Emperor\"><PERSON>, Holy Roman Emperor</a> (d. 1780)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "<PERSON>, Holy Roman Emperor", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Holy_Roman_Emperor"}]}, {"year": "1730", "text": "<PERSON>, 2nd Marquess of Rockingham, English politician, Prime Minister of Great Britain (d. 1782)", "html": "1730 - <a href=\"https://wikipedia.org/wiki/<PERSON>,_2nd_Marquess_of_Rockingham\" title=\"<PERSON>, 2nd Marquess of Rockingham\"><PERSON>, 2nd Marquess of Rockingham</a>, English politician, <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_the_United_Kingdom\" title=\"Prime Minister of the United Kingdom\">Prime Minister of Great Britain</a> (d. 1782)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>,_2nd_Marquess_of_Rockingham\" title=\"<PERSON>, 2nd Marquess of Rockingham\"><PERSON>, 2nd Marquess of Rockingham</a>, English politician, <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_the_United_Kingdom\" title=\"Prime Minister of the United Kingdom\">Prime Minister of Great Britain</a> (d. 1782)", "links": [{"title": "<PERSON>, 2nd Marquess of Rockingham", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_2nd_Marquess_of_Rockingham"}, {"title": "Prime Minister of the United Kingdom", "link": "https://wikipedia.org/wiki/Prime_Minister_of_the_United_Kingdom"}]}, {"year": "1735", "text": "<PERSON>, French violinist and composer (d. 1821)", "html": "1735 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Co<PERSON>et\"><PERSON></a>, French violinist and composer (d. 1821)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Co<PERSON>\"><PERSON></a>, French violinist and composer (d. 1821)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>et"}]}, {"year": "1742", "text": "<PERSON>, Duchess of Teschen (d. 1798)", "html": "1742 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Duchess_of_Teschen\" title=\"<PERSON>, Duchess of Teschen\"><PERSON>, Duchess of Teschen</a> (d. 1798)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Duchess_of_Teschen\" title=\"<PERSON>, Duchess of Teschen\"><PERSON>, Duchess of Teschen</a> (d. 1798)", "links": [{"title": "<PERSON>, Duchess of Teschen", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Duchess_of_Teschen"}]}, {"year": "1753", "text": "<PERSON><PERSON><PERSON>, French general, mathematician, and politician, French Minister of the Interior (d. 1823)", "html": "1753 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, French general, mathematician, and politician, <a href=\"https://wikipedia.org/wiki/Minister_of_the_Interior_(France)\" class=\"mw-redirect\" title=\"Minister of the Interior (France)\">French Minister of the Interior</a> (d. 1823)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, French general, mathematician, and politician, <a href=\"https://wikipedia.org/wiki/Minister_of_the_Interior_(France)\" class=\"mw-redirect\" title=\"Minister of the Interior (France)\">French Minister of the Interior</a> (d. 1823)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>not"}, {"title": "Minister of the Interior (France)", "link": "https://wikipedia.org/wiki/Minister_of_the_Interior_(France)"}]}, {"year": "1792", "text": "<PERSON> (d. 1878)", "html": "1792 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_IX\" title=\"Pope Pius IX\"><PERSON> <PERSON></a> (d. 1878)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"Pope Pius <PERSON>\"><PERSON> <PERSON></a> (d. 1878)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1794", "text": "<PERSON>, French painter (d. 1835)", "html": "1794 - <a href=\"https://wikipedia.org/wiki/Louis_L%C3%A9op<PERSON>_<PERSON>\" title=\"<PERSON>old <PERSON>\"><PERSON></a>, French painter (d. 1835)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Louis_L%C3%A9op<PERSON>_<PERSON>\" title=\"<PERSON>old <PERSON>\"><PERSON></a>, French painter (d. 1835)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Louis_L%C3%A9op<PERSON>_<PERSON>"}]}, {"year": "1795", "text": "<PERSON><PERSON>, French geologist and chronologist (d. 1875)", "html": "1795 - <a href=\"https://wikipedia.org/wiki/G%C3%A9<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, French geologist and chronologist (d. 1875)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/G%C3%A9<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, French geologist and chronologist (d. 1875)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/G%C3%A9<PERSON><PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1804", "text": "<PERSON> <PERSON><PERSON><PERSON>, Swedo-Finnish treasurer of Tavastia province, manor host, and paternal grandfather of President <PERSON><PERSON> <PERSON><PERSON> (d. 1866)", "html": "1804 - <a href=\"https://wikipedia.org/wiki/Per_<PERSON><PERSON><PERSON>_<PERSON>hufvud_af_<PERSON>\" title=\"<PERSON> <PERSON><PERSON><PERSON>vud af <PERSON>\"><PERSON> <PERSON><PERSON><PERSON><PERSON></a>, Swedo-Finnish treasurer of <a href=\"https://wikipedia.org/wiki/Tavastia_(historical_province)\" title=\"Tavastia (historical province)\">Tavastia province</a>, manor host, and paternal grandfather of President <a href=\"https://wikipedia.org/wiki/P._E._Svinhufvud\" class=\"mw-redirect\" title=\"P. E. Svinhufvud\"><PERSON><PERSON> <PERSON><PERSON> Svinhufvud</a> (d. 1866)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Per_<PERSON><PERSON><PERSON>_<PERSON>hufvud_af_<PERSON>\" title=\"Per <PERSON><PERSON><PERSON>hufvud af <PERSON>\"><PERSON> <PERSON><PERSON><PERSON> a<PERSON></a>, Swedo-Finnish treasurer of <a href=\"https://wikipedia.org/wiki/Tavastia_(historical_province)\" title=\"Tavastia (historical province)\">Tavastia province</a>, manor host, and paternal grandfather of President <a href=\"https://wikipedia.org/wiki/P._E._Svinhufvud\" class=\"mw-redirect\" title=\"P. E. Svinhufvud\"><PERSON><PERSON> <PERSON><PERSON>hufvud</a> (d. 1866)", "links": [{"title": "<PERSON> <PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Per_<PERSON><PERSON><PERSON>_<PERSON>vinhufvud_af_<PERSON>"}, {"title": "Tavastia (historical province)", "link": "https://wikipedia.org/wiki/Tavastia_(historical_province)"}, {"title": "<PERSON><PERSON> <PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>._<PERSON><PERSON>_Svinhufvud"}]}, {"year": "1811", "text": "<PERSON>, President of Mexico (1853) (d. 1859)", "html": "1811 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"Juan <PERSON>\"><PERSON></a>, President of Mexico (1853) (d. 1859)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"Juan <PERSON>\"><PERSON></a>, President of Mexico (1853) (d. 1859)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1822", "text": "<PERSON>, Duke of Cádiz (d. 1902)", "html": "1822 - <a href=\"https://wikipedia.org/wiki/<PERSON>,_Duke_of_C%C3%A1diz\" class=\"mw-redirect\" title=\"<PERSON>, Duke of Cádiz\"><PERSON>, Duke of Cádiz</a> (d. 1902)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>,_Duke_of_C%C3%A1diz\" class=\"mw-redirect\" title=\"<PERSON>, Duke of Cádiz\"><PERSON>, Duke of Cádiz</a> (d. 1902)", "links": [{"title": "<PERSON>, Duke of Cádiz", "link": "https://wikipedia.org/wiki/<PERSON>,_<PERSON>_of_C%C3%A1diz"}]}, {"year": "1830", "text": "<PERSON><PERSON><PERSON>, American colonel, lawyer, and politician, 37th Governor of North Carolina (d. 1894)", "html": "1830 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American colonel, lawyer, and politician, 37th <a href=\"https://wikipedia.org/wiki/Governor_of_North_Carolina\" title=\"Governor of North Carolina\">Governor of North Carolina</a> (d. 1894)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American colonel, lawyer, and politician, 37th <a href=\"https://wikipedia.org/wiki/Governor_of_North_Carolina\" title=\"Governor of North Carolina\">Governor of North Carolina</a> (d. 1894)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>"}, {"title": "Governor of North Carolina", "link": "https://wikipedia.org/wiki/Governor_of_North_Carolina"}]}, {"year": "1832", "text": "<PERSON><PERSON>, Latvian philologist and author (d. 1864)", "html": "1832 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>%C4%81ns\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Latvian philologist and author (d. 1864)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>%C4%81ns\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Latvian philologist and author (d. 1864)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>%C4%81ns"}]}, {"year": "1840", "text": "<PERSON><PERSON><PERSON>, French author, poet, and playwright (d. 1897)", "html": "1840 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, French author, poet, and playwright (d. 1897)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, French author, poet, and playwright (d. 1897)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1842", "text": "<PERSON>, English composer (d. 1900)", "html": "1842 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English composer (d. 1900)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English composer (d. 1900)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1856", "text": "<PERSON>, American boxer and manager (d. 1938)", "html": "1856 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%27R<PERSON><PERSON>_(boxing_manager)\" title=\"<PERSON> (boxing manager)\"><PERSON></a>, American boxer and manager (d. 1938)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%27R<PERSON><PERSON>_(boxing_manager)\" title=\"<PERSON> (boxing manager)\"><PERSON></a>, American boxer and manager (d. 1938)", "links": [{"title": "<PERSON> (boxing manager)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>%27Rour<PERSON>_(boxing_manager)"}]}, {"year": "1857", "text": "<PERSON>, Indian-English physician and mathematician, Nobel Prize laureate (d. 1932)", "html": "1857 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Indian-English physician and mathematician, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Physiology_or_Medicine\" title=\"Nobel Prize in Physiology or Medicine\">Nobel Prize</a> laureate (d. 1932)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Indian-English physician and mathematician, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Physiology_or_Medicine\" title=\"Nobel Prize in Physiology or Medicine\">Nobel Prize</a> laureate (d. 1932)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Nobel Prize in Physiology or Medicine", "link": "https://wikipedia.org/wiki/Nobel_Prize_in_Physiology_or_Medicine"}]}, {"year": "1868", "text": "<PERSON>, American target shooter (d. 1904)", "html": "1868 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American target shooter (d. 1904)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American target shooter (d. 1904)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1869", "text": "<PERSON><PERSON><PERSON>, Turkish writer (d. 1944)", "html": "1869 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>et <PERSON>\"><PERSON><PERSON><PERSON></a>, Turkish writer (d. 1944)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"Mehmet <PERSON>\"><PERSON><PERSON><PERSON></a>, Turkish writer (d. 1944)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON><PERSON>_<PERSON>l"}]}, {"year": "1877", "text": "<PERSON>, Scottish international footballer (d. 1948)", "html": "1877 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(Scottish_footballer)\" title=\"<PERSON> (Scottish footballer)\"><PERSON></a>, Scottish international footballer (d. 1948)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(Scottish_footballer)\" title=\"<PERSON> (Scottish footballer)\"><PERSON></a>, Scottish international footballer (d. 1948)", "links": [{"title": "<PERSON> (Scottish footballer)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(Scottish_footballer)"}]}, {"year": "1881", "text": "<PERSON>, Brazilian journalist and author (d. 1922)", "html": "1881 - <a href=\"https://wikipedia.org/wiki/Lima_Barreto\" title=\"Lima Barreto\"><PERSON></a>, Brazilian journalist and author (d. 1922)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Lima_Barreto\" title=\"Lima Barreto\"><PERSON></a>, Brazilian journalist and author (d. 1922)", "links": [{"title": "Lima Barreto", "link": "https://wikipedia.org/wiki/Lima_Barreto"}]}, {"year": "1881", "text": "<PERSON>, American runner (d. 1964)", "html": "1881 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(athlete)\" title=\"<PERSON> (athlete)\"><PERSON></a>, American runner (d. 1964)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(athlete)\" title=\"<PERSON> (athlete)\"><PERSON></a>, American runner (d. 1964)", "links": [{"title": "<PERSON> (athlete)", "link": "https://wikipedia.org/wiki/<PERSON>_(athlete)"}]}, {"year": "1882", "text": "<PERSON>, French painter and sculptor (d. 1963)", "html": "1882 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French painter and sculptor (d. 1963)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French painter and sculptor (d. 1963)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Georges_<PERSON>"}]}, {"year": "1883", "text": "<PERSON><PERSON>, Greek-American pathologist, invented the pap smear (d. 1962)", "html": "1883 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Greek-American pathologist, invented the <a href=\"https://wikipedia.org/wiki/Pap_smear\" class=\"mw-redirect\" title=\"Pap smear\">pap smear</a> (d. 1962)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Greek-American pathologist, invented the <a href=\"https://wikipedia.org/wiki/Pap_smear\" class=\"mw-redirect\" title=\"Pap smear\">pap smear</a> (d. 1962)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Georg<PERSON>_<PERSON>"}, {"title": "Pap smear", "link": "https://wikipedia.org/wiki/Pap_smear"}]}, {"year": "1884", "text": "<PERSON><PERSON>, Jewish-Austrian writer and Holocaust victim (d. 1944)", "html": "1884 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Jewish-Austrian writer and Holocaust victim (d. 1944)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Jewish-Austrian writer and Holocaust victim (d. 1944)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1885", "text": "<PERSON><PERSON><PERSON>, Maltese archbishop (d. 1984)", "html": "1885 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON> Go<PERSON>\"><PERSON><PERSON><PERSON></a>, Maltese archbishop (d. 1984)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Maltese archbishop (d. 1984)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON><PERSON>i"}]}, {"year": "1887", "text": "<PERSON><PERSON>, Australian educator and educational psychologist (d. 1951)", "html": "1887 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Australian educator and <a href=\"https://wikipedia.org/wiki/Educational_psychologist\" title=\"Educational psychologist\">educational psychologist</a> (d. 1951)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Australian educator and <a href=\"https://wikipedia.org/wiki/Educational_psychologist\" title=\"Educational psychologist\">educational psychologist</a> (d. 1951)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}, {"title": "Educational psychologist", "link": "https://wikipedia.org/wiki/Educational_psychologist"}]}, {"year": "1888", "text": "<PERSON><PERSON>, Danish seismologist and geophysicist (d. 1993)", "html": "1888 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"In<PERSON>hmann\"><PERSON><PERSON></a>, Danish seismologist and geophysicist (d. 1993)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"In<PERSON>\"><PERSON><PERSON></a>, Danish seismologist and geophysicist (d. 1993)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/In<PERSON>_<PERSON>"}]}, {"year": "1894", "text": "<PERSON><PERSON><PERSON><PERSON>, Icelandic politician, 2nd President of Iceland (d. 1972)", "html": "1894 - <a href=\"https://wikipedia.org/wiki/%C3%81sgeir_%C3%81sgeirsson\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Icelandic politician, 2nd <a href=\"https://wikipedia.org/wiki/President_of_Iceland\" title=\"President of Iceland\">President of Iceland</a> (d. 1972)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/%C3%81sgeir_%C3%81sgeirsson\" title=\"Ásge<PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Icelandic politician, 2nd <a href=\"https://wikipedia.org/wiki/President_of_Iceland\" title=\"President of Iceland\">President of Iceland</a> (d. 1972)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/%C3%81sgeir_%C3%81sgeirsson"}, {"title": "President of Iceland", "link": "https://wikipedia.org/wiki/President_of_Iceland"}]}, {"year": "1895", "text": "<PERSON><PERSON>, Hungarian-American psychologist, parapsychologist, and author (d. 1964)", "html": "1895 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Hungarian-American psychologist, parapsychologist, and author (d. 1964)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Hungarian-American psychologist, parapsychologist, and author (d. 1964)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1905", "text": "<PERSON><PERSON><PERSON><PERSON>, Indian lawyer and politician, 5th President of India (d. 1977)", "html": "1905 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Indian lawyer and politician, 5th <a href=\"https://wikipedia.org/wiki/President_of_India\" title=\"President of India\">President of India</a> (d. 1977)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Indian lawyer and politician, 5th <a href=\"https://wikipedia.org/wiki/President_of_India\" title=\"President of India\">President of India</a> (d. 1977)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>_<PERSON>"}, {"title": "President of India", "link": "https://wikipedia.org/wiki/President_of_India"}]}, {"year": "1907", "text": "<PERSON>, English novelist and playwright (d. 1989)", "html": "1907 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English novelist and playwright (d. 1989)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English novelist and playwright (d. 1989)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1909", "text": "<PERSON>, American composer and conductor (d. 1992)", "html": "1909 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American composer and conductor (d. 1992)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American composer and conductor (d. 1992)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1911", "text": "<PERSON><PERSON>, American singer and actress (d. 1987)", "html": "1911 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American singer and actress (d. 1987)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American singer and actress (d. 1987)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1912", "text": "<PERSON>, Canadian-American pianist, composer, and bandleader (d. 1988)", "html": "1912 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian-American pianist, composer, and bandleader (d. 1988)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian-American pianist, composer, and bandleader (d. 1988)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1913", "text": "<PERSON>, English actor, singer, and dancer (d. 1989)", "html": "1913 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actor, singer, and dancer (d. 1989)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actor, singer, and dancer (d. 1989)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1913", "text": "<PERSON>, Jr., Liberian politician, 20th President of Liberia (d. 1980)", "html": "1913 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Jr.\" class=\"mw-redirect\" title=\"<PERSON>, Jr.\"><PERSON>, Jr.</a>, Liberian politician, 20th <a href=\"https://wikipedia.org/wiki/President_of_Liberia\" title=\"President of Liberia\">President of Liberia</a> (d. 1980)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Jr.\" class=\"mw-redirect\" title=\"<PERSON>, Jr.\"><PERSON>, Jr.</a>, Liberian politician, 20th <a href=\"https://wikipedia.org/wiki/President_of_Liberia\" title=\"President of Liberia\">President of Liberia</a> (d. 1980)", "links": [{"title": "<PERSON>, Jr.", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>,_Jr."}, {"title": "President of Liberia", "link": "https://wikipedia.org/wiki/President_of_Liberia"}]}, {"year": "1914", "text": "<PERSON>, American boxer (d. 1981)", "html": "1914 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American boxer (d. 1981)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American boxer (d. 1981)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1914", "text": "<PERSON><PERSON>, American singer-songwriter and guitarist (d. 2011)", "html": "1914 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American singer-songwriter and guitarist (d. 2011)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American singer-songwriter and guitarist (d. 2011)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1922", "text": "<PERSON><PERSON>, German graphic designer and typographer (d. 1991)", "html": "1922 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, German graphic designer and typographer (d. 1991)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, German graphic designer and typographer (d. 1991)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/O<PERSON>_<PERSON>cher"}]}, {"year": "1922", "text": "<PERSON><PERSON>, American actress and singer (d. 2009)", "html": "1922 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American actress and singer (d. 2009)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American actress and singer (d. 2009)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1924", "text": "<PERSON>, South African anti-apartheid leader, lawyer, and Ambassador (d. 2010)", "html": "1924 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, South African anti-apartheid leader, lawyer, and Ambassador (d. 2010)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, South African anti-apartheid leader, lawyer, and Ambassador (d. 2010)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1928", "text": "<PERSON>, Nicaraguan politician, President of Nicaragua (d. 2021)", "html": "1928 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%B1os\" title=\"<PERSON>\"><PERSON></a>, Nicaraguan politician, <a href=\"https://wikipedia.org/wiki/President_of_Nicaragua\" class=\"mw-redirect\" title=\"President of Nicaragua\">President of Nicaragua</a> (d. 2021)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>%C3%B1os\" title=\"<PERSON>\"><PERSON></a>, Nicaraguan politician, <a href=\"https://wikipedia.org/wiki/President_of_Nicaragua\" class=\"mw-redirect\" title=\"President of Nicaragua\">President of Nicaragua</a> (d. 2021)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_Bo<PERSON>%C3%B1os"}, {"title": "President of Nicaragua", "link": "https://wikipedia.org/wiki/President_of_Nicaragua"}]}, {"year": "1930", "text": "<PERSON>, American politician (d. 2021)", "html": "1930 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American politician (d. 2021)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American politician (d. 2021)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1931", "text": "<PERSON>, American cult leader, founder of the Peoples Temple (d. 1978)", "html": "1931 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American cult leader, founder of the <a href=\"https://wikipedia.org/wiki/Peoples_Temple\" title=\"Peoples Temple\">Peoples Temple</a> (d. 1978)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American cult leader, founder of the <a href=\"https://wikipedia.org/wiki/Peoples_Temple\" title=\"Peoples Temple\">Peoples Temple</a> (d. 1978)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Peoples Temple", "link": "https://wikipedia.org/wiki/Peoples_Temple"}]}, {"year": "1933", "text": "<PERSON>, American baseball player and coach (d. 2002)", "html": "1933 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player and coach (d. 2002)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"John <PERSON>\"><PERSON></a>, American baseball player and coach (d. 2002)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1934", "text": "<PERSON><PERSON><PERSON>, Israeli archaeologist, architect, and academic (d. 2010)", "html": "1934 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Israeli archaeologist, architect, and academic (d. 2010)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Israeli archaeologist, architect, and academic (d. 2010)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>zer"}]}, {"year": "1937", "text": "<PERSON><PERSON><PERSON>, American actress", "html": "1937 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American actress", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1937", "text": "<PERSON>, American author and poet (d. 1995)", "html": "1937 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author and poet (d. 1995)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author and poet (d. 1995)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1938", "text": "<PERSON><PERSON><PERSON><PERSON>, Italian academic and politician, 48th Prime Minister of Italy", "html": "1938 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Italian academic and politician, 48th <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Italy\" title=\"Prime Minister of Italy\">Prime Minister of Italy</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Italian academic and politician, 48th <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Italy\" title=\"Prime Minister of Italy\">Prime Minister of Italy</a>", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>"}, {"title": "Prime Minister of Italy", "link": "https://wikipedia.org/wiki/Prime_Minister_of_Italy"}]}, {"year": "1939", "text": "<PERSON>, American actor", "html": "1939 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>l"}]}, {"year": "1940", "text": "<PERSON>, English author (d. 1989)", "html": "1940 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English author (d. 1989)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English author (d. 1989)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1941", "text": "<PERSON><PERSON>, Austrian actress", "html": "1941 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Austrian actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Austrian actress", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1941", "text": "<PERSON><PERSON>, American basketball player and coach", "html": "1941 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American basketball player and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American basketball player and coach", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1941", "text": "<PERSON>, American singer-songwriter and guitarist (d. 1959)", "html": "1941 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and guitarist (d. 1959)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and guitarist (d. 1959)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1943", "text": "<PERSON>, American singer-songwriter (d. 1992)", "html": "1943 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter (d. 1992)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter (d. 1992)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1944", "text": "<PERSON><PERSON><PERSON>, American author, screenwriter, and actor", "html": "1944 - <a href=\"https://wikipedia.org/wiki/Armistead_Maupin\" title=\"Armistead Maupin\"><PERSON><PERSON><PERSON></a>, American author, screenwriter, and actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Armistead_Maupin\" title=\"Armistead Maupin\"><PERSON><PERSON><PERSON></a>, American author, screenwriter, and actor", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Armistead_<PERSON>"}]}, {"year": "1945", "text": "<PERSON><PERSON>, Swedish singer-songwriter, guitarist, and actor (d. 2023)", "html": "1945 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Swedish singer-songwriter, guitarist, and actor (d. 2023)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Swedish singer-songwriter, guitarist, and actor (d. 2023)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1945", "text": "<PERSON>, American saxophonist and composer", "html": "1945 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American saxophonist and composer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American saxophonist and composer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1946", "text": "<PERSON>-<PERSON>, English actor and author (d. 2017)", "html": "1946 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actor and author (d. 2017)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actor and author (d. 2017)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1946", "text": "<PERSON><PERSON>, American author", "html": "1946 - <a href=\"https://wikipedia.org/wiki/<PERSON>v_<PERSON>\" title=\"<PERSON>v Wolfman\"><PERSON><PERSON></a>, American author", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>v_<PERSON>\" title=\"Marv Wolfman\"><PERSON><PERSON></a>, American author", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Marv_<PERSON>"}]}, {"year": "1949", "text": "<PERSON><PERSON><PERSON>, American-British actress", "html": "1949 - <a href=\"https://wikipedia.org/wiki/Zo%C3%AB_Wanamaker\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American-British actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Zo%C3%AB_Wanamaker\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American-British actress", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Zo%C3%AB_Wanamaker"}]}, {"year": "1950", "text": "<PERSON>, English singer-songwriter and guitarist (d. 2018)", "html": "1950 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English singer-songwriter and guitarist (d. 2018)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English singer-songwriter and guitarist (d. 2018)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1950", "text": "<PERSON>, American baseball player and manager", "html": "1950 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player and manager", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player and manager", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1950", "text": "<PERSON>, American singer-songwriter, pianist, and producer", "html": "1950 - <a href=\"https://wikipedia.org/wiki/Stevie_Wonder\" title=\"Stevie Wonder\"><PERSON></a>, American singer-songwriter, pianist, and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Stevie_Wonder\" title=\"Stevie Wonder\"><PERSON></a>, American singer-songwriter, pianist, and producer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Stevie_<PERSON>"}]}, {"year": "1952", "text": "<PERSON>, American politician, 69th Governor of Ohio", "html": "1952 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American politician, 69th <a href=\"https://wikipedia.org/wiki/Governor_of_Ohio\" class=\"mw-redirect\" title=\"Governor of Ohio\">Governor of Ohio</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American politician, 69th <a href=\"https://wikipedia.org/wiki/Governor_of_Ohio\" class=\"mw-redirect\" title=\"Governor of Ohio\">Governor of Ohio</a>", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Governor of Ohio", "link": "https://wikipedia.org/wiki/Governor_of_Ohio"}]}, {"year": "1952", "text": "<PERSON>, Canadian actress, producer, and screenwriter", "html": "1952 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(actress)\" title=\"<PERSON> (actress)\"><PERSON></a>, Canadian actress, producer, and screenwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>(actress)\" title=\"<PERSON> (actress)\"><PERSON></a>, Canadian actress, producer, and screenwriter", "links": [{"title": "<PERSON> (actress)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>(actress)"}]}, {"year": "1952", "text": "<PERSON><PERSON>, American academic and author", "html": "1952 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American academic and author", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American academic and author", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1954", "text": "<PERSON>, Australian-Irish singer-songwriter and guitarist", "html": "1954 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(singer)\" title=\"<PERSON> (singer)\"><PERSON></a>, Australian-Irish singer-songwriter and guitarist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>(singer)\" title=\"<PERSON> (singer)\"><PERSON></a>, Australian-Irish singer-songwriter and guitarist", "links": [{"title": "<PERSON> (singer)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>(singer)"}]}, {"year": "1956", "text": "<PERSON>, English journalist and author", "html": "1956 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English journalist and author", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English journalist and author", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1957", "text": "<PERSON>, Filipino economist and politician, 24th Secretary of the Interior and Local Government", "html": "1957 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Filipino economist and politician, 24th <a href=\"https://wikipedia.org/wiki/Secretary_of_the_Interior_and_Local_Government\" title=\"Secretary of the Interior and Local Government\">Secretary of the Interior and Local Government</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Filipino economist and politician, 24th <a href=\"https://wikipedia.org/wiki/Secretary_of_the_Interior_and_Local_Government\" title=\"Secretary of the Interior and Local Government\">Secretary of the Interior and Local Government</a>", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON><PERSON>"}, {"title": "Secretary of the Interior and Local Government", "link": "https://wikipedia.org/wiki/Secretary_of_the_Interior_and_Local_Government"}]}, {"year": "1961", "text": "<PERSON><PERSON><PERSON>, American actress", "html": "1961 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American actress", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1961", "text": "<PERSON>, American basketball player, wrestler, and actor", "html": "1961 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player, wrestler, and actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player, wrestler, and actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1963", "text": "<PERSON>, English politician", "html": "1963 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English politician", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1964", "text": "<PERSON>, American comedian and talk show host", "html": "1964 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American comedian and talk show host", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American comedian and talk show host", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1965", "text": "<PERSON>, Dominican baseball player", "html": "1965 - <a href=\"https://wikipedia.org/wiki/Jos%C3%A9_Ri<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dominican baseball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Jos%C3%A9_Rijo\" title=\"<PERSON>\"><PERSON></a>, Dominican baseball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Jos%C3%A9_<PERSON>ijo"}]}, {"year": "1965", "text": "<PERSON><PERSON>, American singer-songwriter, producer, and actress (d. 2018)", "html": "1965 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American singer-songwriter, producer, and actress (d. 2018)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American singer-songwriter, producer, and actress (d. 2018)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1966", "text": "<PERSON>, English singer-songwriter and producer", "html": "1966 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English singer-songwriter and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English singer-songwriter and producer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1966", "text": "<PERSON>, American singer-songwriter and guitarist", "html": "1966 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and guitarist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Ruck<PERSON>\"><PERSON></a>, American singer-songwriter and guitarist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1967", "text": "<PERSON>, American singer-songwriter and guitarist (d. 2001)", "html": "1967 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and guitarist (d. 2001)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and guitarist (d. 2001)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1967", "text": "<PERSON>, American-German singer (d. 2001)", "html": "1967 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American-German singer (d. 2001)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American-German singer (d. 2001)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1968", "text": "<PERSON>, Australian politician, 30th Prime Minister of Australia", "html": "1968 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian politician, 30th <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Australia\" title=\"Prime Minister of Australia\">Prime Minister of Australia</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian politician, 30th <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Australia\" title=\"Prime Minister of Australia\">Prime Minister of Australia</a>", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Prime Minister of Australia", "link": "https://wikipedia.org/wiki/Prime_Minister_of_Australia"}]}, {"year": "1969", "text": "<PERSON><PERSON><PERSON>, American guitarist and songwriter", "html": "1969 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>\" title=\"<PERSON>eth<PERSON>\"><PERSON><PERSON><PERSON></a>, American guitarist and songwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>\" title=\"<PERSON>eth<PERSON>\"><PERSON><PERSON><PERSON></a>, American guitarist and songwriter", "links": [{"title": "Buck<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Buckethead"}]}, {"year": "1972", "text": "<PERSON>, Canadian ice hockey player and coach", "html": "1972 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player and coach", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1973", "text": "<PERSON><PERSON><PERSON>, Austrian politician", "html": "1973 - <a href=\"https://wikipedia.org/wiki/<PERSON>in<PERSON>_<PERSON>ner\" title=\"<PERSON>in<PERSON> Einwallner\"><PERSON><PERSON><PERSON></a>, Austrian politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>in<PERSON>_<PERSON>\" title=\"Reinhold Einwallner\"><PERSON><PERSON><PERSON></a>, Austrian politician", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1976", "text": "<PERSON><PERSON><PERSON>, American basketball player and executive", "html": "1976 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"T<PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American basketball player and executive", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"T<PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American basketball player and executive", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1977", "text": "<PERSON>, American politician", "html": "1977 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American politician", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1977", "text": "<PERSON><PERSON>, Dutch singer-songwriter", "html": "1977 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Dutch singer-songwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Dutch singer-songwriter", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Il<PERSON>_DeLange"}]}, {"year": "1977", "text": "<PERSON>, English actress and director", "html": "1977 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actress and director", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actress and director", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1977", "text": "<PERSON><PERSON><PERSON>, American rapper", "html": "1977 - <a href=\"https://wikipedia.org/wiki/Pusha_T\" title=\"Pusha T\"><PERSON><PERSON><PERSON></a>, American rapper", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Pusha_T\" title=\"Pusha T\"><PERSON><PERSON><PERSON> <PERSON></a>, American rapper", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>usha_T"}]}, {"year": "1978", "text": "<PERSON>, American basketball player and coach", "html": "1978 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player and coach", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1978", "text": "<PERSON>, American baseball player", "html": "1978 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1979", "text": "Prince <PERSON>, Duke of Värmland", "html": "1979 - <a href=\"https://wikipedia.org/wiki/Prince_<PERSON>_<PERSON>,_Duke_of_V%C3%A4rmland\" title=\"Prince <PERSON>, Duke of Värmland\">Prince <PERSON>, Duke of Värmland</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Prince_<PERSON>_<PERSON>,_Duke_of_V%C3%A4rmland\" title=\"Prince <PERSON>, Duke of Värmland\">Prince <PERSON>, Duke of Värmland</a>", "links": [{"title": "Prince <PERSON>, Duke of Värmland", "link": "https://wikipedia.org/wiki/Prince_<PERSON>_<PERSON>,_Duke_of_V%C3%A4rmland"}]}, {"year": "1979", "text": "<PERSON>, English footballer", "html": "1979 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1981", "text": "<PERSON><PERSON>, English politician", "html": "1981 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, English politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, English politician", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1981", "text": "<PERSON><PERSON>, Russian sprinter", "html": "1981 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Russian sprinter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Russian sprinter", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1982", "text": "<PERSON>, Filipino basketball player", "html": "1982 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Filipino basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Filipino basketball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1982", "text": "<PERSON><PERSON>, American footballer", "html": "1982 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> Onyewu\"><PERSON><PERSON></a>, American footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> Onyewu\"><PERSON><PERSON></a>, American footballer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1983", "text": "<PERSON>, English actress and singer", "html": "1983 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actress and singer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actress and singer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1983", "text": "<PERSON>, Hungarian handball player", "html": "1983 - <a href=\"https://wikipedia.org/wiki/Anita_G%C3%B6<PERSON><PERSON>\" title=\"<PERSON>\"><PERSON></a>, Hungarian handball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Anita_G%C3%B6<PERSON><PERSON>\" title=\"<PERSON>\"><PERSON></a>, Hungarian handball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Anita_G%C3%B6rbicz"}]}, {"year": "1983", "text": "<PERSON><PERSON>, Ivorian footballer", "html": "1983 - <a href=\"https://wikipedia.org/wiki/Yaya_Tour%C3%A9\" title=\"Yaya Touré\"><PERSON><PERSON></a>, Ivorian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Yaya_Tour%C3%A9\" title=\"Yaya Touré\"><PERSON><PERSON></a>, Ivorian footballer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Yaya_Tour%C3%A9"}]}, {"year": "1984", "text": "<PERSON>, American hurdler", "html": "1984 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American hurdler", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American hurdler", "links": [{"title": "<PERSON>-<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>-<PERSON>"}]}, {"year": "1985", "text": "<PERSON><PERSON><PERSON>, Slovak ice hockey player", "html": "1985 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>%C3%A1k\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Slovak ice hockey player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>%C3%A1k\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Slovak ice hockey player", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>%C3%A1k"}]}, {"year": "1985", "text": "<PERSON><PERSON>, Welsh actor and singer", "html": "1985 - <a href=\"https://wikipedia.org/wiki/Iwan_Rheon\" title=\"Iwan Rheon\"><PERSON><PERSON></a>, Welsh actor and singer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Iwan_Rheon\" title=\"Iwan Rheon\"><PERSON><PERSON></a>, Welsh actor and singer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>on"}]}, {"year": "1985", "text": "<PERSON>, Canadian ice hockey player", "html": "1985 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1986", "text": "<PERSON>, American actress, director, and screenwriter", "html": "1986 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress, director, and screenwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress, director, and screenwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1986", "text": "<PERSON>, English actor", "html": "1986 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1986", "text": "<PERSON>, Belarusian-Norwegian singer-songwriter, violinist, and actor", "html": "1986 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Belarusian-Norwegian singer-songwriter, violinist, and actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Belarusian-Norwegian singer-songwriter, violinist, and actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1986", "text": "<PERSON>, Canadian ice hockey player", "html": "1986 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1987", "text": "<PERSON><PERSON>, American singer-songwriter and actress", "html": "1987 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_King\" title=\"Candice King\"><PERSON><PERSON></a>, American singer-songwriter and actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_King\" title=\"Candice King\"><PERSON><PERSON></a>, American singer-songwriter and actress", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_King"}]}, {"year": "1987", "text": "<PERSON>, Dutch cyclist", "html": "1987 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch cyclist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch cyclist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1988", "text": "<PERSON>, Filipino actor and singer", "html": "1988 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Filipino actor and singer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Filipino actor and singer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Paulo_Avelino"}]}, {"year": "1988", "text": "<PERSON>, Australian singer-songwriter", "html": "1988 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>(singer)\" title=\"<PERSON> (singer)\"><PERSON></a>, Australian singer-songwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>(singer)\" title=\"<PERSON> (singer)\"><PERSON></a>, Australian singer-songwriter", "links": [{"title": "<PERSON> (singer)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>(singer)"}]}, {"year": "1988", "text": "<PERSON>, Australian footballer", "html": "1988 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1989", "text": "<PERSON><PERSON> <PERSON><PERSON>, Canadian ice hockey player", "html": "1989 - <a href=\"https://wikipedia.org/wiki/<PERSON>._<PERSON><PERSON>_Subban\" title=\"P. K. Subban\"><PERSON><PERSON> <PERSON><PERSON></a>, Canadian ice hockey player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/P._<PERSON>._Subban\" title=\"P. K. Subban\"><PERSON><PERSON> <PERSON><PERSON></a>, Canadian ice hockey player", "links": [{"title": "P. K. <PERSON>ban", "link": "https://wikipedia.org/wiki/P._K._Subban"}]}, {"year": "1990", "text": "<PERSON><PERSON>, American baseball player", "html": "1990 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American baseball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American baseball player", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>s"}]}, {"year": "1991", "text": "<PERSON>, Scottish footballer", "html": "1991 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1991", "text": "<PERSON>, French footballer", "html": "1991 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1992", "text": "<PERSON><PERSON>, Venezuelan baseball player", "html": "1992 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Venezuelan baseball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Venezuelan baseball player", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1992", "text": "<PERSON><PERSON><PERSON>, American football player", "html": "1992 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American football player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American football player", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1992", "text": "<PERSON>, New Zealand-Australian rugby league player", "html": "1992 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, New Zealand-Australian rugby league player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, New Zealand-Australian rugby league player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1992", "text": "<PERSON>, Canadian hockey player", "html": "1992 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian hockey player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian hockey player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1993", "text": "<PERSON>, American footballer", "html": "1993 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1993", "text": "<PERSON><PERSON>, Belgian footballer", "html": "1993 - <a href=\"https://wikipedia.org/wiki/Rome<PERSON>_Lukaku\" title=\"Romelu Lukaku\"><PERSON><PERSON></a>, Belgian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Rome<PERSON>_Lukaku\" title=\"Romelu Lukaku\"><PERSON><PERSON></a>, Belgian footballer", "links": [{"title": "<PERSON>lu Lu<PERSON>", "link": "https://wikipedia.org/wiki/Romelu_Lukaku"}]}, {"year": "1993", "text": "<PERSON><PERSON>, American actress and singer", "html": "1993 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American actress and singer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American actress and singer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1993", "text": "<PERSON>, American singer-songwriter", "html": "1993 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Wall<PERSON>\"><PERSON></a>, American singer-songwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Wallen\"><PERSON></a>, American singer-songwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Morgan_<PERSON>en"}]}, {"year": "1997", "text": "<PERSON>, American baseball player", "html": "1997 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2003", "text": "<PERSON><PERSON><PERSON>, American basketball player", "html": "2003 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>.\" title=\"<PERSON><PERSON><PERSON> Jr.\"><PERSON><PERSON><PERSON>.</a>, American basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>.\" title=\"<PERSON><PERSON><PERSON> Jr.\"><PERSON><PERSON><PERSON>.</a>, American basketball player", "links": [{"title": "<PERSON><PERSON><PERSON>.", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>."}]}], "Deaths": [{"year": "189", "text": "Emperor <PERSON> of Han, Chinese emperor (b. 156)", "html": "189 - <a href=\"https://wikipedia.org/wiki/Emperor_<PERSON>_of_Han\" title=\"Emperor <PERSON> of Han\">Emperor <PERSON> of Han</a>, Chinese emperor (b. 156)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Emperor_<PERSON>_of_Han\" title=\"Emperor <PERSON> of Han\">Emperor <PERSON> of Han</a>, Chinese emperor (b. 156)", "links": [{"title": "Emperor <PERSON> of Han", "link": "https://wikipedia.org/wiki/Emperor_<PERSON>_<PERSON>_Han"}]}, {"year": "1112", "text": "<PERSON><PERSON><PERSON> <PERSON>, Margrave of Carniola", "html": "1112 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>,_<PERSON><PERSON>_of_Carniola\" title=\"<PERSON><PERSON><PERSON> <PERSON>, Margrave of Carniola\"><PERSON><PERSON><PERSON> <PERSON>, <PERSON><PERSON> of Carniola</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>,_<PERSON><PERSON>_of_Carniola\" title=\"<PERSON><PERSON><PERSON> <PERSON>, Margrave of Carniola\"><PERSON><PERSON><PERSON>, <PERSON><PERSON> of Carniola</a>", "links": [{"title": "<PERSON><PERSON><PERSON> <PERSON>, Margrave of Carniola", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>,_<PERSON><PERSON>_of_Carniola"}]}, {"year": "1176", "text": "<PERSON>, Duke of Lorraine (b. 1119)", "html": "1176 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Duke_of_Lorraine\" title=\"<PERSON>, Duke of Lorraine\"><PERSON>, Duke of Lorraine</a> (b. 1119)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Duke_of_Lorraine\" title=\"<PERSON>, Duke of Lorraine\"><PERSON>, Duke of Lorraine</a> (b. 1119)", "links": [{"title": "<PERSON>, Duke of Lorraine", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Duke_of_Lorraine"}]}, {"year": "1285", "text": "<PERSON>, 1st Baron <PERSON>", "html": "1285 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_(died_1285)\" title=\"<PERSON> (died 1285)\"><PERSON>, 1st Baron <PERSON></a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_(died_1285)\" title=\"<PERSON> (died 1285)\"><PERSON>, 1st Baron <PERSON></a>", "links": [{"title": "<PERSON> (died 1285)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_(died_1285)"}]}, {"year": "1312", "text": "<PERSON><PERSON><PERSON> <PERSON>, Duke of Lorraine (b. 1263)", "html": "1312 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>,_Duke_of_Lorraine\" title=\"<PERSON><PERSON><PERSON> <PERSON>, Duke of Lorraine\"><PERSON><PERSON><PERSON> <PERSON>, Duke of Lorraine</a> (b. 1263)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>,_Duke_of_Lorraine\" title=\"<PERSON><PERSON><PERSON> <PERSON>, Duke of Lorraine\"><PERSON><PERSON><PERSON> <PERSON>, Duke of Lorraine</a> (b. 1263)", "links": [{"title": "<PERSON><PERSON><PERSON> <PERSON>, Duke of Lorraine", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>,_<PERSON>_of_Lorraine"}]}, {"year": "1573", "text": "<PERSON><PERSON>, Japanese daimyō (b. 1521)", "html": "1573 - <a href=\"https://wikipedia.org/wiki/Takeda_Shingen\" title=\"Takeda Shingen\"><PERSON><PERSON></a>, Japanese daimyō (b. 1521)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Takeda_Shingen\" title=\"Takeda Shingen\"><PERSON><PERSON></a>, Japanese daimyō (b. 1521)", "links": [{"title": "Takeda Shingen", "link": "https://wikipedia.org/wiki/Takeda_Shingen"}]}, {"year": "1612", "text": "<PERSON><PERSON>, Japanese master swordsman (b. 1575)", "html": "1612 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>%C5%8D\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Japanese master <a href=\"https://wikipedia.org/wiki/Swordsman\" class=\"mw-redirect\" title=\"Swordsman\">swordsman</a> (b. 1575)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>%C5%8D\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Japanese master <a href=\"https://wikipedia.org/wiki/Swordsman\" class=\"mw-redirect\" title=\"Swordsman\">swordsman</a> (b. 1575)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>jir%C5%8D"}, {"title": "Swordsman", "link": "https://wikipedia.org/wiki/Swordsman"}]}, {"year": "1619", "text": "<PERSON>, Dutch politician (b. 1547)", "html": "1619 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch politician (b. 1547)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch politician (b. 1547)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1704", "text": "<PERSON>, French preacher and author (b. 1632)", "html": "1704 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French preacher and author (b. 1632)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French preacher and author (b. 1632)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1726", "text": "<PERSON>, Italian singer (b. 1659)", "html": "1726 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian singer (b. 1659)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian singer (b. 1659)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1782", "text": "<PERSON>, Swedish-English botanist and phycologist (b. 1736)", "html": "1782 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swedish-English botanist and phycologist (b. 1736)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swedish-English botanist and phycologist (b. 1736)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1807", "text": "<PERSON><PERSON><PERSON>, American colonel, lawyer, and politician (b. 1721)", "html": "1807 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American colonel, lawyer, and politician (b. 1721)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American colonel, lawyer, and politician (b. 1721)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1809", "text": "<PERSON><PERSON><PERSON>, English bishop (b. 1731)", "html": "1809 - <a href=\"https://wikipedia.org/wiki/Be<PERSON><PERSON>_Porteus\" title=\"<PERSON><PERSON><PERSON> Porteus\"><PERSON><PERSON><PERSON></a>, English bishop (b. 1731)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_Porteus\" title=\"<PERSON><PERSON><PERSON> Porteus\"><PERSON><PERSON><PERSON></a>, English bishop (b. 1731)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Be<PERSON>by_Porteus"}]}, {"year": "1832", "text": "<PERSON>, French zoologist and academic (b. 1769)", "html": "1832 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French zoologist and academic (b. 1769)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French zoologist and academic (b. 1769)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1835", "text": "<PERSON>, English architect, designed the Royal Pavilion (b. 1752)", "html": "1835 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>(architect)\" title=\"<PERSON> (architect)\"><PERSON></a>, English architect, designed the <a href=\"https://wikipedia.org/wiki/Royal_Pavilion\" title=\"Royal Pavilion\">Royal Pavilion</a> (b. 1752)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>(architect)\" title=\"<PERSON> (architect)\"><PERSON></a>, English architect, designed the <a href=\"https://wikipedia.org/wiki/Royal_Pavilion\" title=\"Royal Pavilion\">Royal Pavilion</a> (b. 1752)", "links": [{"title": "<PERSON> (architect)", "link": "https://wikipedia.org/wiki/<PERSON>(architect)"}, {"title": "Royal Pavilion", "link": "https://wikipedia.org/wiki/Royal_Pavilion"}]}, {"year": "1836", "text": "<PERSON>, American sheriff and Methodist preacher (b. 1756)", "html": "1836 - <a href=\"https://wikipedia.org/wiki/<PERSON>_(preacher)\" title=\"<PERSON> (preacher)\"><PERSON></a>, American sheriff and <a href=\"https://wikipedia.org/wiki/Methodism\" title=\"Methodism\">Methodist</a> preacher (b. 1756)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>(preacher)\" title=\"<PERSON> (preacher)\"><PERSON></a>, American sheriff and <a href=\"https://wikipedia.org/wiki/Methodism\" title=\"Methodism\">Methodist</a> preacher (b. 1756)", "links": [{"title": "<PERSON> (preacher)", "link": "https://wikipedia.org/wiki/<PERSON>_(preacher)"}, {"title": "Methodism", "link": "https://wikipedia.org/wiki/Methodism"}]}, {"year": "1866", "text": "<PERSON>, Czech-Russian mathematician and academic (b. 1796)", "html": "1866 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Czech-Russian mathematician and academic (b. 1796)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Czech-Russian mathematician and academic (b. 1796)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1878", "text": "<PERSON>, American physicist and academic (b. 1797)", "html": "1878 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American physicist and academic (b. 1797)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American physicist and academic (b. 1797)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1884", "text": "<PERSON>, American businessman, co-founded the International Harvester Company (b. 1809)", "html": "1884 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American businessman, co-founded the <a href=\"https://wikipedia.org/wiki/International_Harvester\" title=\"International Harvester\">International Harvester Company</a> (b. 1809)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American businessman, co-founded the <a href=\"https://wikipedia.org/wiki/International_Harvester\" title=\"International Harvester\">International Harvester Company</a> (b. 1809)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "International Harvester", "link": "https://wikipedia.org/wiki/International_Harvester"}]}, {"year": "1885", "text": "<PERSON>, German physician, pathologist, and anatomist (b. 1809)", "html": "1885 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German physician, pathologist, and anatomist (b. 1809)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German physician, pathologist, and anatomist (b. 1809)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1903", "text": "<PERSON><PERSON><PERSON><PERSON>, Filipino lawyer and politician, 1st Prime Minister of the Philippines (b. 1864)", "html": "1903 - <a href=\"https://wikipedia.org/wiki/Apolinario_Ma<PERSON>i\" title=\"Apolinar<PERSON>\">Apolinar<PERSON></a>, Filipino lawyer and politician, 1st <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_the_Philippines\" title=\"Prime Minister of the Philippines\">Prime Minister of the Philippines</a> (b. 1864)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Apolinario_<PERSON>\" title=\"Apolinar<PERSON>\">Apolinar<PERSON></a>, Filipino lawyer and politician, 1st <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_the_Philippines\" title=\"Prime Minister of the Philippines\">Prime Minister of the Philippines</a> (b. 1864)", "links": [{"title": "Apoli<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Apolinario_<PERSON><PERSON>i"}, {"title": "Prime Minister of the Philippines", "link": "https://wikipedia.org/wiki/Prime_Minister_of_the_Philippines"}]}, {"year": "1916", "text": "<PERSON><PERSON><PERSON>, Ukrainian-American author and playwright (b. 1859)", "html": "1916 - <a href=\"https://wikipedia.org/wiki/Sholem_Aleichem\" title=\"Sholem Aleichem\"><PERSON><PERSON><PERSON> Aleichem</a>, Ukrainian-American author and playwright (b. 1859)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Sholem_Aleichem\" title=\"Sholem Aleichem\">Shole<PERSON> Aleichem</a>, Ukrainian-American author and playwright (b. 1859)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Sholem_Aleichem"}]}, {"year": "1921", "text": "<PERSON>, French author, poet, and playwright (b. 1848)", "html": "1921 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French author, poet, and playwright (b. 1848)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French author, poet, and playwright (b. 1848)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1926", "text": "<PERSON><PERSON>, Belgian-American bishop (b. 1857)", "html": "1926 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Belgian-American bishop (b. 1857)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Belgian-American bishop (b. 1857)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_Bo<PERSON>ms"}]}, {"year": "1929", "text": "<PERSON>, German electrical engineer, invented the Enigma machine (b. 1878)", "html": "1929 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German electrical engineer, invented the <a href=\"https://wikipedia.org/wiki/Enigma_machine\" title=\"Enigma machine\">Enigma machine</a> (b. 1878)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German electrical engineer, invented the <a href=\"https://wikipedia.org/wiki/Enigma_machine\" title=\"Enigma machine\">Enigma machine</a> (b. 1878)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Enigma machine", "link": "https://wikipedia.org/wiki/Enigma_machine"}]}, {"year": "1930", "text": "<PERSON><PERSON><PERSON><PERSON><PERSON>, Norwegian scientist, explorer, and academic, Nobel Prize laureate (b. 1861)", "html": "1930 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON><PERSON></a>, Norwegian scientist, explorer, and academic, <a href=\"https://wikipedia.org/wiki/Nobel_Peace_Prize\" title=\"Nobel Peace Prize\">Nobel Prize</a> laureate (b. 1861)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON><PERSON></a>, Norwegian scientist, explorer, and academic, <a href=\"https://wikipedia.org/wiki/Nobel_Peace_Prize\" title=\"Nobel Peace Prize\">Nobel Prize</a> laureate (b. 1861)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>t<PERSON><PERSON>_<PERSON>"}, {"title": "Nobel Peace Prize", "link": "https://wikipedia.org/wiki/Nobel_Peace_Prize"}]}, {"year": "1938", "text": "<PERSON>, Swiss-French physicist and academic, Nobel Prize laureate (b. 1861)", "html": "1938 - <a href=\"https://wikipedia.org/wiki/<PERSON>_%C3%89<PERSON><PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swiss-French physicist and academic, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Physics\" title=\"Nobel Prize in Physics\">Nobel Prize</a> laureate (b. 1861)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_%C3%89<PERSON><PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swiss-French physicist and academic, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Physics\" title=\"Nobel Prize in Physics\">Nobel Prize</a> laureate (b. 1861)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_%C3%89<PERSON><PERSON>_<PERSON>"}, {"title": "Nobel Prize in Physics", "link": "https://wikipedia.org/wiki/Nobel_Prize_in_Physics"}]}, {"year": "1941", "text": "<PERSON>, English cricketer (b. 1877)", "html": "1941 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(cricketer)\" class=\"mw-redirect\" title=\"<PERSON> (cricketer)\"><PERSON></a>, English cricketer (b. 1877)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(cricketer)\" class=\"mw-redirect\" title=\"<PERSON> (cricketer)\"><PERSON></a>, English cricketer (b. 1877)", "links": [{"title": "<PERSON> (cricketer)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(cricketer)"}]}, {"year": "1941", "text": "<PERSON><PERSON><PERSON>, Japanese sumo wrestler, the 26th <PERSON><PERSON><PERSON><PERSON> (b. 1891)", "html": "1941 - <a href=\"https://wikipedia.org/wiki/%C5%8Cnishiki_Uichir%C5%8D\" title=\"Ō<PERSON><PERSON> Uichirō\"><PERSON><PERSON><PERSON></a>, Japanese sumo wrestler, the 26th <a href=\"https://wikipedia.org/wiki/Yokozuna\" class=\"mw-redirect\" title=\"Yo<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a> (b. 1891)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/%C5%8Cnishiki_Uichir%C5%8D\" title=\"Ōnish<PERSON> Uichirō\"><PERSON><PERSON><PERSON></a>, Japanese sumo wrestler, the 26th <a href=\"https://wikipedia.org/wiki/Yoko<PERSON>na\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a> (b. 1891)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/%C5%8Cnishiki_Uichir%C5%8D"}, {"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Yokozuna"}]}, {"year": "1945", "text": "<PERSON><PERSON>, American drummer (b. 1895)", "html": "1945 - <a href=\"https://wikipedia.org/wiki/Tubby_Hall\" title=\"Tubby Hall\"><PERSON><PERSON></a>, American drummer (b. 1895)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Tubby_Hall\" title=\"Tubby Hall\"><PERSON><PERSON></a>, American drummer (b. 1895)", "links": [{"title": "Tubby Hall", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1946", "text": "<PERSON><PERSON>, American suffragist (b. 1869)", "html": "1946 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American suffragist (b. 1869)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American suffragist (b. 1869)", "links": [{"title": "<PERSON>ara <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1947", "text": "<PERSON><PERSON><PERSON>, Indian poet and playwright (b. 1926)", "html": "1947 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Indian poet and playwright (b. 1926)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Indian poet and playwright (b. 1926)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1948", "text": "<PERSON>, Marchioness of Hartington (b. 1920)", "html": "1948 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Marchioness_of_Hartington\" title=\"<PERSON>, Marchioness of Hartington\"><PERSON>, Marchioness of Hartington</a> (b. 1920)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Marchioness_of_Hartington\" title=\"<PERSON>, Marchioness of Hartington\"><PERSON>, Marchioness of Hartington</a> (b. 1920)", "links": [{"title": "<PERSON>, Marchioness of Hartington", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Marchioness_of_Hart<PERSON>"}]}, {"year": "1957", "text": "<PERSON>, Hungarian-Israeli mathematician and academic (b. 1886)", "html": "1957 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Hungarian-Israeli mathematician and academic (b. 1886)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Hungarian-Israeli mathematician and academic (b. 1886)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1961", "text": "<PERSON>, American actor (b. 1901)", "html": "1961 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor (b. 1901)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor (b. 1901)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1962", "text": "<PERSON>, American dentist (b. 1893)", "html": "1962 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, American dentist (b. 1893)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, American dentist (b. 1893)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "1962", "text": "<PERSON>, American painter and academic (b. 1910)", "html": "1962 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American painter and academic (b. 1910)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American painter and academic (b. 1910)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1963", "text": "<PERSON><PERSON>, Austrian-Italian bishop (b. 1885)", "html": "1963 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Austrian-Italian bishop (b. 1885)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Austrian-Italian bishop (b. 1885)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1972", "text": "<PERSON>, American actor (b. 1928)", "html": "1972 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor (b. 1928)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor (b. 1928)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>er"}]}, {"year": "1974", "text": "<PERSON>, Mexican poet and diplomat (b. 1902)", "html": "1974 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Mexican poet and diplomat (b. 1902)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Mexican poet and diplomat (b. 1902)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1974", "text": "<PERSON>, American colonel and author (b. 1898)", "html": "1974 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American colonel and author (b. 1898)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American colonel and author (b. 1898)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1975", "text": "<PERSON>, French physicist (b. 1909)", "html": "1975 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French physicist (b. 1909)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French physicist (b. 1909)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1975", "text": "<PERSON>, American singer-songwriter and actor (b. 1905)", "html": "1975 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and actor (b. 1905)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and actor (b. 1905)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1977", "text": "<PERSON>, American mobster (b. 1934)", "html": "1977 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(mobster)\" title=\"<PERSON> (mobster)\"><PERSON></a>, American mobster (b. 1934)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(mobster)\" title=\"<PERSON> (mobster)\"><PERSON></a>, American mobster (b. 1934)", "links": [{"title": "<PERSON> (mobster)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(mobster)"}]}, {"year": "1985", "text": "<PERSON><PERSON><PERSON>, American actress (b. 1893)", "html": "1985 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON> Joy\"><PERSON><PERSON><PERSON></a>, American actress (b. 1893)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON> Joy\"><PERSON><PERSON><PERSON></a>, American actress (b. 1893)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>e_Joy"}]}, {"year": "1985", "text": "<PERSON>, American literary critic and biographer (b. 1918)", "html": "1985 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American literary critic and biographer (b. 1918)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American literary critic and biographer (b. 1918)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1988", "text": "<PERSON><PERSON>, American singer and trumpet player (b. 1929)", "html": "1988 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON>et <PERSON>\"><PERSON><PERSON></a>, American singer and trumpet player (b. 1929)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> <PERSON>\"><PERSON><PERSON></a>, American singer and trumpet player (b. 1929)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Chet_<PERSON>"}]}, {"year": "1992", "text": "<PERSON><PERSON> <PERSON><PERSON>, Irish sculptor (b. 1909)", "html": "1992 - <a href=\"https://wikipedia.org/wiki/F<PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> <PERSON><PERSON>\"><PERSON><PERSON> <PERSON><PERSON></a>, Irish sculptor (b. 1909)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>ill<PERSON>\" title=\"<PERSON><PERSON> <PERSON><PERSON> Mc<PERSON>ill<PERSON>\"><PERSON><PERSON> <PERSON><PERSON></a>, Irish sculptor (b. 1909)", "links": [{"title": "<PERSON><PERSON> <PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON><PERSON><PERSON>"}]}, {"year": "1994", "text": "<PERSON>, Irish-English race car driver (b. 1920)", "html": "1994 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(racing_driver)\" title=\"<PERSON> (racing driver)\"><PERSON></a>, Irish-English race car driver (b. 1920)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(racing_driver)\" title=\"<PERSON> (racing driver)\"><PERSON></a>, Irish-English race car driver (b. 1920)", "links": [{"title": "<PERSON> (racing driver)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(racing_driver)"}]}, {"year": "1994", "text": "<PERSON>, Canadian-American jurist and politician, 42nd Governor of Michigan (b. 1925)", "html": "1994 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian-American jurist and politician, 42nd <a href=\"https://wikipedia.org/wiki/Governor_of_Michigan\" title=\"Governor of Michigan\">Governor of Michigan</a> (b. 1925)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian-American jurist and politician, 42nd <a href=\"https://wikipedia.org/wiki/Governor_of_Michigan\" title=\"Governor of Michigan\">Governor of Michigan</a> (b. 1925)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Governor of Michigan", "link": "https://wikipedia.org/wiki/Governor_of_Michigan"}]}, {"year": "1995", "text": "<PERSON><PERSON>, Chinese-American logician, philosopher, and mathematician (b. 1921)", "html": "1995 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_(academic)\" title=\"<PERSON><PERSON> (academic)\"><PERSON><PERSON></a>, Chinese-American logician, philosopher, and mathematician (b. 1921)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_(academic)\" title=\"<PERSON><PERSON> (academic)\"><PERSON><PERSON></a>, Chinese-American logician, philosopher, and mathematician (b. 1921)", "links": [{"title": "<PERSON><PERSON> (academic)", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_(academic)"}]}, {"year": "1999", "text": "<PERSON> <PERSON><PERSON>, Saudi Arabian scholar and academic (b. 1910)", "html": "1999 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON> ibn <PERSON>\"><PERSON> ibn <PERSON></a>, Saudi Arabian scholar and academic (b. 1910)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON> ibn <PERSON>\"><PERSON> ibn <PERSON></a>, Saudi Arabian scholar and academic (b. 1910)", "links": [{"title": "<PERSON> <PERSON><PERSON><PERSON> ibn <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1999", "text": "<PERSON>, American golfer and journalist (b. 1902)", "html": "1999 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American golfer and journalist (b. 1902)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American golfer and journalist (b. 1902)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2000", "text": "<PERSON>, American actor, director, and screenwriter (b. 1938)", "html": "2000 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor, director, and screenwriter (b. 1938)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor, director, and screenwriter (b. 1938)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "2000", "text": "<PERSON><PERSON>, Japanese wrestler (b. 1951)", "html": "2000 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_Tsuruta\" title=\"<PERSON><PERSON> Tsuruta\"><PERSON><PERSON></a>, Japanese wrestler (b. 1951)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>su<PERSON>\" title=\"<PERSON><PERSON> Tsuruta\"><PERSON><PERSON></a>, Japanese wrestler (b. 1951)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON><PERSON><PERSON>"}]}, {"year": "2001", "text": "<PERSON>, American actor and playwright (b. 1939)", "html": "2001 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(playwright)\" title=\"<PERSON> (playwright)\"><PERSON></a>, American actor and playwright (b. 1939)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(playwright)\" title=\"<PERSON> (playwright)\"><PERSON></a>, American actor and playwright (b. 1939)", "links": [{"title": "<PERSON> (playwright)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(playwright)"}]}, {"year": "2002", "text": "<PERSON><PERSON><PERSON>, Ukrainian footballer and manager (b. 1939)", "html": "2002 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>y_Lo<PERSON>i\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Ukrainian footballer and manager (b. 1939)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>y_<PERSON>i\" title=\"<PERSON><PERSON>y <PERSON>\"><PERSON><PERSON><PERSON></a>, Ukrainian footballer and manager (b. 1939)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>y_<PERSON>i"}]}, {"year": "2005", "text": "<PERSON>, French record producer, founded Barclay Records (b. 1921)", "html": "2005 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French record producer, founded <a href=\"https://wikipedia.org/wiki/Barclay_Records\" class=\"mw-redirect\" title=\"Barclay Records\">Barclay Records</a> (b. 1921)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>\" title=\"<PERSON>\"><PERSON></a>, French record producer, founded <a href=\"https://wikipedia.org/wiki/Barclay_Records\" class=\"mw-redirect\" title=\"Barclay Records\">Barclay Records</a> (b. 1921)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Barclay Records", "link": "https://wikipedia.org/wiki/Barclay_Records"}]}, {"year": "2005", "text": "<PERSON>, American mathematician and academic (b. 1914)", "html": "2005 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American mathematician and academic (b. 1914)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American mathematician and academic (b. 1914)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2006", "text": "<PERSON><PERSON><PERSON>, American historian and scholar (b. 1923)", "html": "2006 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American historian and scholar (b. 1923)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American historian and scholar (b. 1923)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "2006", "text": "<PERSON><PERSON>, Jr., American singer (b. 1949)", "html": "2006 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>,_Jr.\" class=\"mw-redirect\" title=\"<PERSON><PERSON>, Jr.\"><PERSON><PERSON>, Jr.</a>, American singer (b. 1949)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>,_Jr.\" class=\"mw-redirect\" title=\"<PERSON><PERSON>, Jr.\"><PERSON><PERSON>, Jr.</a>, American singer (b. 1949)", "links": [{"title": "<PERSON><PERSON>, Jr.", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>,_Jr."}]}, {"year": "2008", "text": "<PERSON><PERSON>, Kuwaiti ruler, Emir of Kuwait (b. 1930)", "html": "2008 - <a href=\"https://wikipedia.org/wiki/Saad_Al-Salim_Al-Sabah\" title=\"Saad Al-Salim Al-Sabah\"><PERSON><PERSON>-Salim <PERSON>-Sabah</a>, Kuwaiti ruler, <a href=\"https://wikipedia.org/wiki/List_of_emirs_of_Kuwait\" class=\"mw-redirect\" title=\"List of emirs of Kuwait\">Emir of Kuwait</a> (b. 1930)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Saad_Al-Salim_Al-Sabah\" title=\"Saad Al-Salim Al-Sabah\"><PERSON><PERSON> Al-Salim Al-Sabah</a>, Kuwaiti ruler, <a href=\"https://wikipedia.org/wiki/List_of_emirs_of_Kuwait\" class=\"mw-redirect\" title=\"List of emirs of Kuwait\">Emir of Kuwait</a> (b. 1930)", "links": [{"title": "<PERSON>ad <PERSON>", "link": "https://wikipedia.org/wiki/Saad_Al-Salim_Al-Sabah"}, {"title": "List of emirs of Kuwait", "link": "https://wikipedia.org/wiki/List_of_emirs_of_Kuwait"}]}, {"year": "2008", "text": "<PERSON>, American journalist and author (b. 1936)", "html": "2008 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(reporter)\" title=\"<PERSON> (reporter)\"><PERSON></a>, American journalist and author (b. 1936)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(reporter)\" title=\"<PERSON> (reporter)\"><PERSON></a>, American journalist and author (b. 1936)", "links": [{"title": "<PERSON> (reporter)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(reporter)"}]}, {"year": "2009", "text": "<PERSON>, American actor (b. 1926)", "html": "2009 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor (b. 1926)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor (b. 1926)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2009", "text": "<PERSON><PERSON>, Belgian rabbi (b. 1934)", "html": "2009 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Belgian rabbi (b. 1934)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Belgian rabbi (b. 1934)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "2009", "text": "<PERSON><PERSON><PERSON>, Italian skier and mountaineer (b. 1914)", "html": "2009 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Italian skier and mountaineer (b. 1914)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Italian skier and mountaineer (b. 1914)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/A<PERSON><PERSON>_<PERSON>i"}]}, {"year": "2011", "text": "<PERSON>, Canadian ice hockey player (b. 1982)", "html": "2011 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player (b. 1982)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player (b. 1982)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2011", "text": "<PERSON>, American sculptor and educator (b. 1933)", "html": "2011 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American sculptor and educator (b. 1933)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American sculptor and educator (b. 1933)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2011", "text": "<PERSON>, Canadian businessman, co-founded McCain Foods (b. 1930)", "html": "2011 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian businessman, co-founded <a href=\"https://wikipedia.org/wiki/McCain_Foods\" title=\"McCain Foods\">McCain Foods</a> (b. 1930)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian businessman, co-founded <a href=\"https://wikipedia.org/wiki/McCain_Foods\" title=\"McCain Foods\">McCain Foods</a> (b. 1930)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "McCain Foods", "link": "https://wikipedia.org/wiki/McCain_Foods"}]}, {"year": "2011", "text": "<PERSON>, American director and producer (b. 1942)", "html": "2011 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American director and producer (b. 1942)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American director and producer (b. 1942)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2012", "text": "<PERSON><PERSON><PERSON>, Afghan politician (b. 1937)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Afghan politician (b. 1937)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Afghan politician (b. 1937)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>"}]}, {"year": "2012", "text": "<PERSON> \"<PERSON>\" <PERSON>, American bass player, songwriter, and producer (b. 1941)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/<PERSON>_%22Duck%22_<PERSON>\" title='<PERSON> \"<PERSON>\" <PERSON>'><PERSON> \"<PERSON>\" <PERSON></a>, American bass player, songwriter, and producer (b. 1941)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_%22Duck%22_<PERSON>\" title='<PERSON> \"<PERSON>\" <PERSON>'><PERSON> \"<PERSON>\" <PERSON></a>, American bass player, songwriter, and producer (b. 1941)", "links": [{"title": "<PERSON> \"<PERSON>\" <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_%22Duck%22_<PERSON>"}]}, {"year": "2012", "text": "<PERSON>-<PERSON>, Cuban-American theologian, author, and academic (b. 1943)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Cuban-American theologian, author, and academic (b. 1943)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Cuban-American theologian, author, and academic (b. 1943)", "links": [{"title": "<PERSON>-<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "2012", "text": "<PERSON>, English speedway rider (b. 1979)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(speedway_rider)\" title=\"<PERSON> (speedway rider)\"><PERSON></a>, English speedway rider (b. 1979)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(speedway_rider)\" title=\"<PERSON> (speedway rider)\"><PERSON></a>, English speedway rider (b. 1979)", "links": [{"title": "<PERSON> (speedway rider)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(speedway_rider)"}]}, {"year": "2012", "text": "<PERSON>, Australian humanitarian (b. 1925)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian humanitarian (b. 1925)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian humanitarian (b. 1925)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2012", "text": "<PERSON><PERSON><PERSON><PERSON>, Vietnamese bishop (b. 1906)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/Nguy%E1%BB%85n_V%C4%83n_Thi%E1%BB%87n\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Vietnamese bishop (b. 1906)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Nguy%E1%BB%85n_V%C4%83n_Thi%E1%BB%87n\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Vietnamese bishop (b. 1906)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Nguy%E1%BB%85n_V%C4%83n_Thi%E1%BB%87n"}]}, {"year": "2013", "text": "<PERSON>, American psychologist, author, and actress (b. 1927)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/<PERSON>_Brothers\" title=\"Joyce Brothers\"><PERSON></a>, American psychologist, author, and actress (b. 1927)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Joyce_Brothers\" title=\"Joyce Brothers\"><PERSON></a>, American psychologist, author, and actress (b. 1927)", "links": [{"title": "Joyce Brothers", "link": "https://wikipedia.org/wiki/<PERSON>_Brothers"}]}, {"year": "2013", "text": "<PERSON>, Namibian lawyer and politician (b. 1937)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Namibian lawyer and politician (b. 1937)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Namibian lawyer and politician (b. 1937)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2013", "text": "<PERSON><PERSON><PERSON><PERSON>, Indian photographer (b. 1954)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/Jagdish_Mali\" title=\"Jagdish Mali\">Jagdish Mali</a>, Indian photographer (b. 1954)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Jagdish_Mali\" title=\"Jagdish Mali\">Jagdish Mali</a>, Indian photographer (b. 1954)", "links": [{"title": "Jagdish Mali", "link": "https://wikipedia.org/wiki/Jagdish_Mali"}]}, {"year": "2013", "text": "<PERSON>, American football player (b. 1953)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player (b. 1953)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player (b. 1953)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2013", "text": "<PERSON><PERSON><PERSON>, Russian footballer (b. 1973)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Russian footballer (b. 1973)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Russian footballer (b. 1973)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "2013", "text": "<PERSON><PERSON>, Canadian politician (b. 1943)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Canadian politician (b. 1943)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Canadian politician (b. 1943)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "2014", "text": "<PERSON>, Australian philosopher and author (b. 1926)", "html": "2014 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian philosopher and author (b. 1926)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian philosopher and author (b. 1926)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "2014", "text": "<PERSON>, Swedish director and producer (b. 1977)", "html": "2014 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swedish director and producer (b. 1977)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swedish director and producer (b. 1977)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2014", "text": "<PERSON><PERSON> <PERSON><PERSON>, American soldier and pilot (b. 1918)", "html": "2014 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> <PERSON><PERSON>\"><PERSON><PERSON> <PERSON><PERSON></a>, American soldier and pilot (b. 1918)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> <PERSON><PERSON>\"><PERSON><PERSON> <PERSON><PERSON></a>, American soldier and pilot (b. 1918)", "links": [{"title": "<PERSON><PERSON> <PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "2014", "text": "<PERSON>, Canadian lawyer and politician (b. 1949)", "html": "2014 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian lawyer and politician (b. 1949)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian lawyer and politician (b. 1949)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2014", "text": "<PERSON> <PERSON>-<PERSON><PERSON>, American occultist and author (b. 1948)", "html": "2014 - <a href=\"https://wikipedia.org/wiki/Morning_Glory_Zell-Ravenheart\" title=\"Morning Glory Zell-Ravenheart\">Morning Glory Zell-Ravenheart</a>, American occultist and author (b. 1948)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Morning_Glory_Zell-Ravenheart\" title=\"Morning Glory Zell-Ravenheart\">Morning Glory Zell-Ravenheart</a>, American occultist and author (b. 1948)", "links": [{"title": "Morning Glory Zell-<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Morning_Glory_<PERSON><PERSON>-<PERSON>heart"}]}, {"year": "2015", "text": "<PERSON>, Jr., American baseball player (b. 1931)", "html": "2015 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Jr.\" class=\"mw-redirect\" title=\"<PERSON>, Jr.\"><PERSON>, Jr.</a>, American baseball player (b. 1931)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Jr.\" class=\"mw-redirect\" title=\"<PERSON>, Jr.\"><PERSON>, Jr.</a>, American baseball player (b. 1931)", "links": [{"title": "<PERSON>, Jr.", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Jr."}]}, {"year": "2015", "text": "<PERSON>, American clarinet player and composer (b. 1927)", "html": "2015 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American clarinet player and composer (b. 1927)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American clarinet player and composer (b. 1927)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2015", "text": "<PERSON>, Russian runner (b. 1928)", "html": "2015 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian runner (b. 1928)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian runner (b. 1928)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2015", "text": "<PERSON>, American-Canadian physician and academic (b. 1934)", "html": "2015 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American-Canadian physician and academic (b. 1934)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American-Canadian physician and academic (b. 1934)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "2015", "text": "<PERSON><PERSON><PERSON>, Russian cyclist (b. 1937)", "html": "2015 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Russian cyclist (b. 1937)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Russian cyclist (b. 1937)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "2016", "text": "<PERSON>, American sociologist and academic (b. 1926)", "html": "2016 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON> <PERSON></a>, American sociologist and academic (b. 1926)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American sociologist and academic (b. 1926)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "2019", "text": "<PERSON>, American singer and actress (b. 1922)", "html": "2019 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Day\"><PERSON></a>, American singer and actress (b. 1922)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Day\"><PERSON></a>, American singer and actress (b. 1922)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2019", "text": "<PERSON><PERSON>, American civil rights activist and politician (b. 1933)", "html": "2019 - <a href=\"https://wikipedia.org/wiki/Unita_Blackwell\" title=\"Unita Blackwell\"><PERSON><PERSON></a>, American civil rights activist and politician (b. 1933)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Unita_Blackwell\" title=\"Unita Blackwell\"><PERSON><PERSON></a>, American civil rights activist and politician (b. 1933)", "links": [{"title": "Unita Blackwell", "link": "https://wikipedia.org/wiki/Unita_Blackwell"}]}, {"year": "2022", "text": "<PERSON><PERSON><PERSON> <PERSON> <PERSON><PERSON><PERSON>, 2nd President of the United Arab Emirates (b. 1948)", "html": "2022 - <a href=\"https://wikipedia.org/wiki/Khali<PERSON>_bin_<PERSON><PERSON><PERSON>_<PERSON>_Nahyan\" title=\"K<PERSON><PERSON> bin <PERSON>\"><PERSON><PERSON><PERSON> bin <PERSON><PERSON><PERSON></a>, 2nd <a href=\"https://wikipedia.org/wiki/President_of_the_United_Arab_Emirates\" title=\"President of the United Arab Emirates\">President of the United Arab Emirates</a> (b. 1948)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Khali<PERSON>_bin_<PERSON><PERSON><PERSON>_<PERSON>Nah<PERSON>\" title=\"K<PERSON><PERSON> bin <PERSON>\"><PERSON><PERSON><PERSON> bin <PERSON><PERSON><PERSON></a>, 2nd <a href=\"https://wikipedia.org/wiki/President_of_the_United_Arab_Emirates\" title=\"President of the United Arab Emirates\">President of the United Arab Emirates</a> (b. 1948)", "links": [{"title": "<PERSON><PERSON><PERSON> <PERSON> <PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>"}, {"title": "President of the United Arab Emirates", "link": "https://wikipedia.org/wiki/President_of_the_United_Arab_Emirates"}]}, {"year": "2024", "text": "<PERSON>, Canadian short story writer (b. 1931)", "html": "2024 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian short story writer (b. 1931)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian short story writer (b. 1931)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2024", "text": "<PERSON>, American forensic pathologist (b. 1931)", "html": "2024 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American forensic pathologist (b. 1931)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American forensic pathologist (b. 1931)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2024", "text": "<PERSON><PERSON><PERSON><PERSON>, American playwright and screenwriter (b. 1946)", "html": "2024 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>-<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>-<PERSON>\"><PERSON><PERSON>-<PERSON></a>, American playwright and screenwriter (b. 1946)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>-<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>-<PERSON>\"><PERSON><PERSON>-<PERSON></a>, American playwright and screenwriter (b. 1946)", "links": [{"title": "Samm-<PERSON>", "link": "https://wikipedia.org/wiki/Samm-Art_Williams"}]}]}}