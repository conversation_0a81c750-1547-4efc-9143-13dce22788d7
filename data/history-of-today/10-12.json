{"date": "October 12", "url": "https://wikipedia.org/wiki/October_12", "data": {"Events": [{"year": "539 BC", "text": "The army of <PERSON> the Great of Persia takes Babylon, ending the Babylonian empire. (Julian calendar)", "html": "539 BC - 539 BC - The army of <a href=\"https://wikipedia.org/wiki/<PERSON>_the_Great\" title=\"<PERSON> the Great\"><PERSON> the Great</a> of <a href=\"https://wikipedia.org/wiki/Persia\" class=\"mw-redirect\" title=\"Persia\">Persia</a> takes <a href=\"https://wikipedia.org/wiki/Babylon\" title=\"Babylon\">Babylon</a>, ending the Babylonian empire. (<a href=\"https://wikipedia.org/wiki/Julian_calendar\" title=\"Julian calendar\">Julian calendar</a>)", "no_year_html": "539 BC - The army of <a href=\"https://wikipedia.org/wiki/<PERSON>_the_Great\" title=\"<PERSON> the Great\"><PERSON> the Great</a> of <a href=\"https://wikipedia.org/wiki/Persia\" class=\"mw-redirect\" title=\"Persia\">Persia</a> takes <a href=\"https://wikipedia.org/wiki/Babylon\" title=\"Babylon\">Babylon</a>, ending the Babylonian empire. (<a href=\"https://wikipedia.org/wiki/Julian_calendar\" title=\"Julian calendar\">Julian calendar</a>)", "links": [{"title": "<PERSON> the Great", "link": "https://wikipedia.org/wiki/<PERSON>_the_Great"}, {"title": "Persia", "link": "https://wikipedia.org/wiki/Persia"}, {"title": "Babylon", "link": "https://wikipedia.org/wiki/Babylon"}, {"title": "Julian calendar", "link": "https://wikipedia.org/wiki/Julian_calendar"}]}, {"year": "633", "text": " Battle of Hatfield Chase: King <PERSON> of Northumbria is defeated and killed by an alliance under <PERSON><PERSON> of Mercia and Cadwallon of Gwynedd.", "html": "633 - <a href=\"https://wikipedia.org/wiki/Battle_of_Hatfield_Chase\" title=\"Battle of Hatfield Chase\">Battle of Hatfield Chase</a>: King <a href=\"https://wikipedia.org/wiki/<PERSON>_of_Northumbria\" title=\"<PERSON> of Northumbria\"><PERSON> of Northumbria</a> is defeated and killed by an alliance under <a href=\"https://wikipedia.org/wiki/Penda_of_Mercia\" title=\"Penda of Mercia\">Penda of Mercia</a> and <a href=\"https://wikipedia.org/wiki/Cadwallon_ap_Cadfan\" title=\"Cadwallon ap Cadfan\">Cadwallon of Gwynedd</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Battle_of_Hatfield_Chase\" title=\"Battle of Hatfield Chase\">Battle of Hatfield Chase</a>: King <a href=\"https://wikipedia.org/wiki/<PERSON>_of_Northumbria\" title=\"<PERSON> of Northumbria\"><PERSON> of Northumbria</a> is defeated and killed by an alliance under <a href=\"https://wikipedia.org/wiki/Penda_of_Mercia\" title=\"Penda of Mercia\">Penda of Mercia</a> and <a href=\"https://wikipedia.org/wiki/Cadwallon_ap_Cadfan\" title=\"Cadwallon ap Cadfan\">Cadwallon of Gwynedd</a>.", "links": [{"title": "Battle of Hatfield Chase", "link": "https://wikipedia.org/wiki/Battle_of_Hatfield_Chase"}, {"title": "<PERSON> of Northumbria", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_Northumbria"}, {"title": "Penda of Mercia", "link": "https://wikipedia.org/wiki/Penda_of_Mercia"}, {"title": "Cadwallon ap <PERSON>adfan", "link": "https://wikipedia.org/wiki/Cadwallon_ap_Cadfan"}]}, {"year": "1279", "text": "The Nichiren Shōshū branch of Buddhism is founded in Japan.", "html": "1279 - The <i><a href=\"https://wikipedia.org/wiki/Nichiren_Sh%C5%8Dsh%C5%AB\" title=\"Nichiren Shōshū\">Nichiren Shōshū</a></i> branch of Buddhism is founded in Japan.", "no_year_html": "The <i><a href=\"https://wikipedia.org/wiki/Nichiren_Sh%C5%8Dsh%C5%AB\" title=\"Nichiren Shōshū\">Nichiren Shōshū</a></i> branch of Buddhism is founded in Japan.", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Nichiren_Sh%C5%8Dsh%C5%AB"}]}, {"year": "1398", "text": "In the Treaty of Salynas, Lithuania cedes Samogitia to the Teutonic Knights.", "html": "1398 - In the <a href=\"https://wikipedia.org/wiki/Treaty_of_Salynas\" title=\"Treaty of Salynas\">Treaty of Salynas</a>, <a href=\"https://wikipedia.org/wiki/Lithuania\" title=\"Lithuania\">Lithuania</a> cedes <a href=\"https://wikipedia.org/wiki/Samogitia\" title=\"Samogitia\">Samogitia</a> to the <a href=\"https://wikipedia.org/wiki/Teutonic_Order\" title=\"Teutonic Order\">Teutonic Knights</a>.", "no_year_html": "In the <a href=\"https://wikipedia.org/wiki/Treaty_of_Salynas\" title=\"Treaty of Salynas\">Treaty of Salynas</a>, <a href=\"https://wikipedia.org/wiki/Lithuania\" title=\"Lithuania\">Lithuania</a> cedes <a href=\"https://wikipedia.org/wiki/Samogitia\" title=\"Samogitia\">Samogitia</a> to the <a href=\"https://wikipedia.org/wiki/Teutonic_Order\" title=\"Teutonic Order\">Teutonic Knights</a>.", "links": [{"title": "Treaty of Salynas", "link": "https://wikipedia.org/wiki/Treaty_of_Salynas"}, {"title": "Lithuania", "link": "https://wikipedia.org/wiki/Lithuania"}, {"title": "Samogitia", "link": "https://wikipedia.org/wiki/Samogitia"}, {"title": "Teutonic Order", "link": "https://wikipedia.org/wiki/Teutonic_Order"}]}, {"year": "1406", "text": "<PERSON>, the only person from Indonesia known to have visited dynastic Korea, reaches Seoul after having set out from Java four months before.", "html": "1406 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, the only person from Indonesia known to have visited dynastic Korea, reaches <a href=\"https://wikipedia.org/wiki/Seoul\" title=\"Seoul\">Seoul</a> after having set out from <a href=\"https://wikipedia.org/wiki/Java\" title=\"Java\">Java</a> four months before.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, the only person from Indonesia known to have visited dynastic Korea, reaches <a href=\"https://wikipedia.org/wiki/Seoul\" title=\"Seoul\">Seoul</a> after having set out from <a href=\"https://wikipedia.org/wiki/Java\" title=\"Java\">Java</a> four months before.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Seoul", "link": "https://wikipedia.org/wiki/Seoul"}, {"title": "Java", "link": "https://wikipedia.org/wiki/Java"}]}, {"year": "1492", "text": "<PERSON>'s first expedition makes landfall in the Caribbean, specifically on San Salvador Island. (Julian calendar)", "html": "1492 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON> Columbus\"><PERSON></a>'s <a href=\"https://wikipedia.org/wiki/Voyages_of_Christopher_Columbus\" title=\"Voyages of Christopher Columbus\">first expedition</a> makes landfall in the <a href=\"https://wikipedia.org/wiki/Caribbean\" title=\"Caribbean\">Caribbean</a>, specifically on <a href=\"https://wikipedia.org/wiki/San_Salvador_Island\" title=\"San Salvador Island\">San Salvador Island</a>. (<a href=\"https://wikipedia.org/wiki/Julian_calendar\" title=\"Julian calendar\">Julian calendar</a>)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Christopher_Columbus\" title=\"Christopher Columbus\"><PERSON></a>'s <a href=\"https://wikipedia.org/wiki/Voyages_of_Christopher_Columbus\" title=\"Voyages of Christopher Columbus\">first expedition</a> makes landfall in the <a href=\"https://wikipedia.org/wiki/Caribbean\" title=\"Caribbean\">Caribbean</a>, specifically on <a href=\"https://wikipedia.org/wiki/San_Salvador_Island\" title=\"San Salvador Island\">San Salvador Island</a>. (<a href=\"https://wikipedia.org/wiki/Julian_calendar\" title=\"Julian calendar\">Julian calendar</a>)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Voyages of <PERSON>", "link": "https://wikipedia.org/wiki/Voyages_of_<PERSON>_<PERSON>"}, {"title": "Caribbean", "link": "https://wikipedia.org/wiki/Caribbean"}, {"title": "San Salvador Island", "link": "https://wikipedia.org/wiki/San_Salvador_Island"}, {"title": "Julian calendar", "link": "https://wikipedia.org/wiki/Julian_calendar"}]}, {"year": "1654", "text": "The Delft Explosion devastates the city in the Netherlands, killing more than 100 people.", "html": "1654 - The <a href=\"https://wikipedia.org/wiki/Delft_Explosion\" class=\"mw-redirect\" title=\"Delft Explosion\">Delft Explosion</a> devastates the city in the Netherlands, killing more than 100 people.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Delft_Explosion\" class=\"mw-redirect\" title=\"Delft Explosion\">Delft Explosion</a> devastates the city in the Netherlands, killing more than 100 people.", "links": [{"title": "Delft Explosion", "link": "https://wikipedia.org/wiki/Delft_Explosion"}]}, {"year": "1692", "text": "The Salem witch trials are ended by a letter from Province of Massachusetts Bay Governor <PERSON>.", "html": "1692 - The <a href=\"https://wikipedia.org/wiki/Salem_witch_trials\" title=\"Salem witch trials\">Salem witch trials</a> are ended by a letter from <a href=\"https://wikipedia.org/wiki/Province_of_Massachusetts_Bay\" title=\"Province of Massachusetts Bay\">Province of Massachusetts Bay</a> <a href=\"https://wikipedia.org/wiki/List_of_colonial_governors_of_Massachusetts\" title=\"List of colonial governors of Massachusetts\">Governor</a> <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Salem_witch_trials\" title=\"Salem witch trials\">Salem witch trials</a> are ended by a letter from <a href=\"https://wikipedia.org/wiki/Province_of_Massachusetts_Bay\" title=\"Province of Massachusetts Bay\">Province of Massachusetts Bay</a> <a href=\"https://wikipedia.org/wiki/List_of_colonial_governors_of_Massachusetts\" title=\"List of colonial governors of Massachusetts\">Governor</a> <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>.", "links": [{"title": "Salem witch trials", "link": "https://wikipedia.org/wiki/Salem_witch_trials"}, {"title": "Province of Massachusetts Bay", "link": "https://wikipedia.org/wiki/Province_of_Massachusetts_Bay"}, {"title": "List of colonial governors of Massachusetts", "link": "https://wikipedia.org/wiki/List_of_colonial_governors_of_Massachusetts"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1748", "text": "War of Jenkins' Ear: A British squadron wins a tactical victory over a Spanish squadron off Havana.", "html": "1748 - <a href=\"https://wikipedia.org/wiki/War_of_Jenkins%27_Ear\" title=\"War of Jenkins' Ear\">War of Jenkins' Ear</a>: A British squadron <a href=\"https://wikipedia.org/wiki/Battle_of_Havana_(1748)\" title=\"Battle of Havana (1748)\">wins a tactical victory</a> over a Spanish squadron off Havana.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/War_of_Jenkins%27_Ear\" title=\"War of Jenkins' Ear\">War of Jenkins' Ear</a>: A British squadron <a href=\"https://wikipedia.org/wiki/Battle_of_Havana_(1748)\" title=\"Battle of Havana (1748)\">wins a tactical victory</a> over a Spanish squadron off Havana.", "links": [{"title": "War of Jenkins' Ear", "link": "https://wikipedia.org/wiki/War_of_Jenkins%27_Ear"}, {"title": "Battle of Havana (1748)", "link": "https://wikipedia.org/wiki/Battle_of_Havana_(1748)"}]}, {"year": "1773", "text": "America's first insane asylum opens.", "html": "1773 - America's <a href=\"https://wikipedia.org/wiki/Eastern_State_Hospital_(Virginia)\" title=\"Eastern State Hospital (Virginia)\">first insane asylum</a> opens.", "no_year_html": "America's <a href=\"https://wikipedia.org/wiki/Eastern_State_Hospital_(Virginia)\" title=\"Eastern State Hospital (Virginia)\">first insane asylum</a> opens.", "links": [{"title": "Eastern State Hospital (Virginia)", "link": "https://wikipedia.org/wiki/Eastern_State_Hospital_(Virginia)"}]}, {"year": "1792", "text": "The first celebration of Columbus Day is held in New York City.", "html": "1792 - The first celebration of <a href=\"https://wikipedia.org/wiki/Columbus_Day\" title=\"Columbus Day\">Columbus Day</a> is held in New York City.", "no_year_html": "The first celebration of <a href=\"https://wikipedia.org/wiki/Columbus_Day\" title=\"Columbus Day\">Columbus Day</a> is held in New York City.", "links": [{"title": "Columbus Day", "link": "https://wikipedia.org/wiki/Columbus_Day"}]}, {"year": "1793", "text": "The cornerstone of Old East, the oldest state university building in the United States, is laid at the University of North Carolina at Chapel Hill.", "html": "1793 - The cornerstone of <a href=\"https://wikipedia.org/wiki/Old_East\" title=\"Old East\">Old East</a>, the oldest state university building in the United States, is laid at the <a href=\"https://wikipedia.org/wiki/University_of_North_Carolina_at_Chapel_Hill\" title=\"University of North Carolina at Chapel Hill\">University of North Carolina at Chapel Hill</a>.", "no_year_html": "The cornerstone of <a href=\"https://wikipedia.org/wiki/Old_East\" title=\"Old East\">Old East</a>, the oldest state university building in the United States, is laid at the <a href=\"https://wikipedia.org/wiki/University_of_North_Carolina_at_Chapel_Hill\" title=\"University of North Carolina at Chapel Hill\">University of North Carolina at Chapel Hill</a>.", "links": [{"title": "Old East", "link": "https://wikipedia.org/wiki/Old_East"}, {"title": "University of North Carolina at Chapel Hill", "link": "https://wikipedia.org/wiki/University_of_North_Carolina_at_Chapel_Hill"}]}, {"year": "1798", "text": "Flemish and Luxembourgish peasants launch the rebellion against French rule known as the Peasants' War.", "html": "1798 - Flemish and Luxembourgish peasants launch the rebellion against French rule known as the <a href=\"https://wikipedia.org/wiki/Peasants%27_War_(1798)\" title=\"Peasants' War (1798)\">Peasants' War</a>.", "no_year_html": "Flemish and Luxembourgish peasants launch the rebellion against French rule known as the <a href=\"https://wikipedia.org/wiki/Peasants%27_War_(1798)\" title=\"Peasants' War (1798)\">Peasants' War</a>.", "links": [{"title": "Peasants' War (1798)", "link": "https://wikipedia.org/wiki/Peasants%27_War_(1798)"}]}, {"year": "1799", "text": "<PERSON> becomes the first woman to jump from a balloon with a parachute.", "html": "1799 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A8<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a> becomes the first woman to jump from a balloon with a <a href=\"https://wikipedia.org/wiki/Parachute\" title=\"Parachute\">parachute</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A8ve_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a> becomes the first woman to jump from a balloon with a <a href=\"https://wikipedia.org/wiki/Parachute\" title=\"Parachute\">parachute</a>.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_Genevi%C3%A8ve_Labrosse"}, {"title": "Parachute", "link": "https://wikipedia.org/wiki/Parachute"}]}, {"year": "1810", "text": "The citizens of Munich hold the first Oktoberfest in celebration of the marriage of Crown Prince <PERSON> of Bavaria and Princess <PERSON><PERSON> of Saxe-Hildburghausen.", "html": "1810 - The citizens of <a href=\"https://wikipedia.org/wiki/Munich\" title=\"Munich\">Munich</a> hold the first <a href=\"https://wikipedia.org/wiki/Oktoberfest\" title=\"Oktoberfest\">Oktoberfest</a> in celebration of the marriage of Crown Prince <a href=\"https://wikipedia.org/wiki/Ludwig_I_of_Bavaria\" title=\"Ludwig I of Bavaria\"><PERSON> of Bavaria</a> and Princess <a href=\"https://wikipedia.org/wiki/Therese_of_Saxe-Hildburghausen\" title=\"Therese of Saxe-Hildburghausen\">Therese of Saxe-Hildburghausen</a>.", "no_year_html": "The citizens of <a href=\"https://wikipedia.org/wiki/Munich\" title=\"Munich\">Munich</a> hold the first <a href=\"https://wikipedia.org/wiki/Oktoberfest\" title=\"Oktoberfest\">Oktoberfest</a> in celebration of the marriage of Crown Prince <a href=\"https://wikipedia.org/wiki/Ludwig_I_of_Bavaria\" title=\"Ludwig I of Bavaria\"><PERSON> of Bavaria</a> and Princess <a href=\"https://wikipedia.org/wiki/Therese_of_Saxe-Hildburghausen\" title=\"Therese of Saxe-Hildburghausen\">Therese of Saxe-Hildburghausen</a>.", "links": [{"title": "Munich", "link": "https://wikipedia.org/wiki/Munich"}, {"title": "Oktoberfest", "link": "https://wikipedia.org/wiki/Oktoberfest"}, {"title": "<PERSON> of Bavaria", "link": "https://wikipedia.org/wiki/Ludwig_I_of_Bavaria"}, {"title": "<PERSON><PERSON> of Saxe-Hildburghausen", "link": "https://wikipedia.org/wiki/Therese_of_Saxe-Hildburghausen"}]}, {"year": "1822", "text": "<PERSON> of Brazil is proclaimed the emperor.", "html": "1822 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Brazil\" title=\"<PERSON> of Brazil\"><PERSON> of Brazil</a> is proclaimed the emperor.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Brazil\" title=\"<PERSON> of Brazil\"><PERSON> of Brazil</a> is proclaimed the emperor.", "links": [{"title": "<PERSON> of Brazil", "link": "https://wikipedia.org/wiki/Pedro_I_of_Brazil"}]}, {"year": "1849", "text": "The city of Manizales, Colombia, is founded by 'The Expedition of the 20'.", "html": "1849 - The city of <a href=\"https://wikipedia.org/wiki/Manizales\" title=\"Manizales\">Manizales</a>, Colombia, is founded by 'The Expedition of the 20'.", "no_year_html": "The city of <a href=\"https://wikipedia.org/wiki/Manizales\" title=\"Manizales\">Manizales</a>, Colombia, is founded by 'The Expedition of the 20'.", "links": [{"title": "Man<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Manizales"}]}, {"year": "1856", "text": "An M 7.7-8.3 earthquake off the Greek island of Crete cause major damage as far as Egypt and Malta.", "html": "1856 - An <a href=\"https://wikipedia.org/wiki/1856_Heraklion_earthquake\" title=\"1856 Heraklion earthquake\">M 7.7-8.3 earthquake</a> off the Greek island of Crete cause major damage as far as Egypt and Malta.", "no_year_html": "An <a href=\"https://wikipedia.org/wiki/1856_Heraklion_earthquake\" title=\"1856 Heraklion earthquake\">M 7.7-8.3 earthquake</a> off the Greek island of Crete cause major damage as far as Egypt and Malta.", "links": [{"title": "1856 Heraklion earthquake", "link": "https://wikipedia.org/wiki/1856_Heraklion_earthquake"}]}, {"year": "1871", "text": "The British in India enact the Criminal Tribes Act, naming many local communities \"Criminal Tribes\".", "html": "1871 - The British in India enact the <a href=\"https://wikipedia.org/wiki/Criminal_Tribes_Act\" title=\"Criminal Tribes Act\">Criminal Tribes Act</a>, naming many local communities \"Criminal Tribes\".", "no_year_html": "The British in India enact the <a href=\"https://wikipedia.org/wiki/Criminal_Tribes_Act\" title=\"Criminal Tribes Act\">Criminal Tribes Act</a>, naming many local communities \"Criminal Tribes\".", "links": [{"title": "Criminal Tribes Act", "link": "https://wikipedia.org/wiki/Criminal_Tribes_Act"}]}, {"year": "1890", "text": "Uddevalla Suffrage Association is formed.", "html": "1890 - <a href=\"https://wikipedia.org/wiki/Uddevalla_Suffrage_Association\" title=\"Uddevalla Suffrage Association\">Uddevalla Suffrage Association</a> is formed.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Uddevalla_Suffrage_Association\" title=\"Uddevalla Suffrage Association\">Uddevalla Suffrage Association</a> is formed.", "links": [{"title": "Uddevalla Suffrage Association", "link": "https://wikipedia.org/wiki/Uddevalla_Suffrage_Association"}]}, {"year": "1892", "text": "The Pledge of Allegiance is first recited by students in many US public schools.", "html": "1892 - The <a href=\"https://wikipedia.org/wiki/Pledge_of_Allegiance_(United_States)\" class=\"mw-redirect\" title=\"Pledge of Allegiance (United States)\">Pledge of Allegiance</a> is first recited by students in many US public schools.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Pledge_of_Allegiance_(United_States)\" class=\"mw-redirect\" title=\"Pledge of Allegiance (United States)\">Pledge of Allegiance</a> is first recited by students in many US public schools.", "links": [{"title": "Pledge of Allegiance (United States)", "link": "https://wikipedia.org/wiki/Pledge_of_Allegiance_(United_States)"}]}, {"year": "1901", "text": "President <PERSON> officially renames the \"Executive Mansion\" to the White House.", "html": "1901 - President <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> officially renames the \"Executive Mansion\" to the <a href=\"https://wikipedia.org/wiki/White_House\" title=\"White House\">White House</a>.", "no_year_html": "President <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> officially renames the \"Executive Mansion\" to the <a href=\"https://wikipedia.org/wiki/White_House\" title=\"White House\">White House</a>.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}, {"title": "White House", "link": "https://wikipedia.org/wiki/White_House"}]}, {"year": "1909", "text": "Foundation of Coritiba Foot Ball Club.", "html": "1909 - Foundation of <a href=\"https://wikipedia.org/wiki/Coritiba_Foot_Ball_Club\" title=\"Coritiba Foot Ball Club\">Coritiba Foot Ball Club</a>.", "no_year_html": "Foundation of <a href=\"https://wikipedia.org/wiki/Coritiba_Foot_Ball_Club\" title=\"Coritiba Foot Ball Club\">Coritiba Foot Ball Club</a>.", "links": [{"title": "Coritiba Foot Ball Club", "link": "https://wikipedia.org/wiki/Coritiba_Foot_Ball_Club"}]}, {"year": "1915", "text": "World War I: British nurse <PERSON> is executed by a German firing squad for helping Allied soldiers escape from occupied Belgium.", "html": "1915 - <a href=\"https://wikipedia.org/wiki/World_War_I\" title=\"World War I\">World War I</a>: British nurse <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> is executed by a German firing squad for helping Allied soldiers escape from <a href=\"https://wikipedia.org/wiki/German_occupation_of_Belgium_during_World_War_I\" title=\"German occupation of Belgium during World War I\">occupied Belgium</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/World_War_I\" title=\"World War I\">World War I</a>: British nurse <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> is executed by a German firing squad for helping Allied soldiers escape from <a href=\"https://wikipedia.org/wiki/German_occupation_of_Belgium_during_World_War_I\" title=\"German occupation of Belgium during World War I\">occupied Belgium</a>.", "links": [{"title": "World War I", "link": "https://wikipedia.org/wiki/World_War_I"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "German occupation of Belgium during World War I", "link": "https://wikipedia.org/wiki/German_occupation_of_Belgium_during_World_War_I"}]}, {"year": "1917", "text": "World War I: The First Battle of Passchendaele takes place resulting in the largest single-day loss of life in New Zealand history.", "html": "1917 - World War I: The <a href=\"https://wikipedia.org/wiki/First_Battle_of_Passchendaele\" title=\"First Battle of Passchendaele\">First Battle of Passchendaele</a> takes place resulting in the largest single-day loss of life in New Zealand history.", "no_year_html": "World War I: The <a href=\"https://wikipedia.org/wiki/First_Battle_of_Passchendaele\" title=\"First Battle of Passchendaele\">First Battle of Passchendaele</a> takes place resulting in the largest single-day loss of life in New Zealand history.", "links": [{"title": "First Battle of Passchendaele", "link": "https://wikipedia.org/wiki/First_Battle_of_Passchendaele"}]}, {"year": "1918", "text": "A massive forest fire kills 453 people in Minnesota.", "html": "1918 - A <a href=\"https://wikipedia.org/wiki/Cloquet_fire\" title=\"Cloquet fire\">massive forest fire</a> kills 453 people in Minnesota.", "no_year_html": "A <a href=\"https://wikipedia.org/wiki/Cloquet_fire\" title=\"Cloquet fire\">massive forest fire</a> kills 453 people in Minnesota.", "links": [{"title": "Cloquet fire", "link": "https://wikipedia.org/wiki/Cloquet_fire"}]}, {"year": "1928", "text": "An iron lung respirator is used for the first time at Boston Children's Hospital.", "html": "1928 - An <a href=\"https://wikipedia.org/wiki/Iron_lung\" title=\"Iron lung\">iron lung</a> respirator is used for the first time at <a href=\"https://wikipedia.org/wiki/Boston_Children%27s_Hospital\" title=\"Boston Children's Hospital\">Boston Children's Hospital</a>.", "no_year_html": "An <a href=\"https://wikipedia.org/wiki/Iron_lung\" title=\"Iron lung\">iron lung</a> respirator is used for the first time at <a href=\"https://wikipedia.org/wiki/Boston_Children%27s_Hospital\" title=\"Boston Children's Hospital\">Boston Children's Hospital</a>.", "links": [{"title": "Iron lung", "link": "https://wikipedia.org/wiki/Iron_lung"}, {"title": "Boston Children's Hospital", "link": "https://wikipedia.org/wiki/Boston_Children%27s_Hospital"}]}, {"year": "1933", "text": "The military Alcatraz Citadel becomes the civilian Alcatraz Federal Penitentiary.", "html": "1933 - The military <a href=\"https://wikipedia.org/wiki/Alcatraz_Citadel\" class=\"mw-redirect\" title=\"Alcatraz Citadel\">Alcatraz Citadel</a> becomes the civilian <a href=\"https://wikipedia.org/wiki/Alcatraz_Federal_Penitentiary\" title=\"Alcatraz Federal Penitentiary\">Alcatraz Federal Penitentiary</a>.", "no_year_html": "The military <a href=\"https://wikipedia.org/wiki/Alcatraz_Citadel\" class=\"mw-redirect\" title=\"Alcatraz Citadel\">Alcatraz Citadel</a> becomes the civilian <a href=\"https://wikipedia.org/wiki/Alcatraz_Federal_Penitentiary\" title=\"Alcatraz Federal Penitentiary\">Alcatraz Federal Penitentiary</a>.", "links": [{"title": "Alcatraz Citadel", "link": "https://wikipedia.org/wiki/Alcatraz_Citadel"}, {"title": "Alcatraz Federal Penitentiary", "link": "https://wikipedia.org/wiki/Alcatraz_Federal_Penitentiary"}]}, {"year": "1944", "text": "World War II: The Axis occupation of Athens comes to an end.", "html": "1944 - <a href=\"https://wikipedia.org/wiki/World_War_II\" title=\"World War II\">World War II</a>: The <a href=\"https://wikipedia.org/wiki/Axis_occupation_of_Greece\" title=\"Axis occupation of Greece\">Axis occupation of Athens</a> comes to an end.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/World_War_II\" title=\"World War II\">World War II</a>: The <a href=\"https://wikipedia.org/wiki/Axis_occupation_of_Greece\" title=\"Axis occupation of Greece\">Axis occupation of Athens</a> comes to an end.", "links": [{"title": "World War II", "link": "https://wikipedia.org/wiki/World_War_II"}, {"title": "Axis occupation of Greece", "link": "https://wikipedia.org/wiki/Axis_occupation_of_Greece"}]}, {"year": "1945", "text": "World War II: <PERSON> is the first conscientious objector to receive the U.S. Medal of Honor.", "html": "1945 - World War II: <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> is the first conscientious objector to receive the U.S. Medal of Honor.", "no_year_html": "World War II: <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> is the first conscientious objector to receive the U.S. Medal of Honor.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1945", "text": "The Lao Issara took control of Laos' government and reaffirmed the country's independence.", "html": "1945 - The <a href=\"https://wikipedia.org/wiki/Lao_Issara\" title=\"Lao Issara\"><PERSON></a> took control of Laos' government and reaffirmed the country's independence.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Lao_Issara\" title=\"Lao Issara\"><PERSON></a> took control of Laos' government and reaffirmed the country's independence.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Lao_Is<PERSON>a"}]}, {"year": "1959", "text": "At the national congress of the American Popular Revolutionary Alliance in Peru, a group of leftist radicals are expelled from the party who later form APRA Rebelde.", "html": "1959 - At the national congress of the American Popular Revolutionary Alliance in Peru, a group of leftist radicals are expelled from the party who later form <a href=\"https://wikipedia.org/wiki/APRA_Rebelde\" title=\"APRA Rebelde\">APRA Rebelde</a>.", "no_year_html": "At the national congress of the American Popular Revolutionary Alliance in Peru, a group of leftist radicals are expelled from the party who later form <a href=\"https://wikipedia.org/wiki/APRA_Rebelde\" title=\"APRA Rebelde\">APRA Rebelde</a>.", "links": [{"title": "APRA Rebelde", "link": "https://wikipedia.org/wiki/APRA_Rebelde"}]}, {"year": "1960", "text": "Soviet Premier <PERSON><PERSON> pounds his shoe on a desk at the United Nations to protest a Philippine assertion.", "html": "1960 - Soviet Premier <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a> <a href=\"https://wikipedia.org/wiki/Shoe-banging_incident\" title=\"Shoe-banging incident\">pounds his shoe on a desk</a> at the United Nations to protest a Philippine assertion.", "no_year_html": "Soviet Premier <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a> <a href=\"https://wikipedia.org/wiki/Shoe-banging_incident\" title=\"Shoe-banging incident\">pounds his shoe on a desk</a> at the United Nations to protest a Philippine assertion.", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}, {"title": "Shoe-banging incident", "link": "https://wikipedia.org/wiki/Shoe-banging_incident"}]}, {"year": "1962", "text": "The Columbus Day Storm strikes the U.S. Pacific Northwest with record wind velocities. There was at least U.S. $230 million in damages and 46 people died.", "html": "1962 - The <a href=\"https://wikipedia.org/wiki/Columbus_Day_Storm_of_1962\" class=\"mw-redirect\" title=\"Columbus Day Storm of 1962\">Columbus Day Storm</a> strikes the <a href=\"https://wikipedia.org/wiki/Pacific_Northwest\" title=\"Pacific Northwest\">U.S. Pacific Northwest</a> with record wind velocities. There was at least U.S. $230 million in damages and 46 people died.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Columbus_Day_Storm_of_1962\" class=\"mw-redirect\" title=\"Columbus Day Storm of 1962\">Columbus Day Storm</a> strikes the <a href=\"https://wikipedia.org/wiki/Pacific_Northwest\" title=\"Pacific Northwest\">U.S. Pacific Northwest</a> with record wind velocities. There was at least U.S. $230 million in damages and 46 people died.", "links": [{"title": "Columbus Day Storm of 1962", "link": "https://wikipedia.org/wiki/Columbus_Day_Storm_of_1962"}, {"title": "Pacific Northwest", "link": "https://wikipedia.org/wiki/Pacific_Northwest"}]}, {"year": "1963", "text": "After nearly 23 years of imprisonment, Reverend <PERSON>, a Jesuit missionary, was released from the Soviet Union.", "html": "1963 - After nearly 23 years of imprisonment, Reverend <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, a Jesuit missionary, was released from the Soviet Union.", "no_year_html": "After nearly 23 years of imprisonment, Reverend <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, a Jesuit missionary, was released from the Soviet Union.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1964", "text": "The Soviet Union launches the Voskhod 1 into Earth orbit as the first spacecraft with a multi-person crew, and the first flight without pressure suits.", "html": "1964 - The Soviet Union launches the <a href=\"https://wikipedia.org/wiki/Voskhod_1\" title=\"Voskhod 1\">Voskhod 1</a> into Earth orbit as the first spacecraft with a multi-person crew, and the first flight without pressure suits.", "no_year_html": "The Soviet Union launches the <a href=\"https://wikipedia.org/wiki/Voskhod_1\" title=\"Voskhod 1\">Voskhod 1</a> into Earth orbit as the first spacecraft with a multi-person crew, and the first flight without pressure suits.", "links": [{"title": "Voskhod 1", "link": "https://wikipedia.org/wiki/Voskhod_1"}]}, {"year": "1967", "text": "A bomb explodes on board Cyprus Airways Flight 284 while flying over the Mediterranean Sea, killing 66.", "html": "1967 - A bomb explodes on board <a href=\"https://wikipedia.org/wiki/Cyprus_Airways_Flight_284\" title=\"Cyprus Airways Flight 284\">Cyprus Airways Flight 284</a> while flying over the <a href=\"https://wikipedia.org/wiki/Mediterranean_Sea\" title=\"Mediterranean Sea\">Mediterranean Sea</a>, killing 66.", "no_year_html": "A bomb explodes on board <a href=\"https://wikipedia.org/wiki/Cyprus_Airways_Flight_284\" title=\"Cyprus Airways Flight 284\">Cyprus Airways Flight 284</a> while flying over the <a href=\"https://wikipedia.org/wiki/Mediterranean_Sea\" title=\"Mediterranean Sea\">Mediterranean Sea</a>, killing 66.", "links": [{"title": "Cyprus Airways Flight 284", "link": "https://wikipedia.org/wiki/Cyprus_Airways_Flight_284"}, {"title": "Mediterranean Sea", "link": "https://wikipedia.org/wiki/Mediterranean_Sea"}]}, {"year": "1968", "text": "Equatorial Guinea becomes independent from Spain.", "html": "1968 - <a href=\"https://wikipedia.org/wiki/Equatorial_Guinea\" title=\"Equatorial Guinea\">Equatorial Guinea</a> becomes independent from Spain.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Equatorial_Guinea\" title=\"Equatorial Guinea\">Equatorial Guinea</a> becomes independent from Spain.", "links": [{"title": "Equatorial Guinea", "link": "https://wikipedia.org/wiki/Equatorial_Guinea"}]}, {"year": "1970", "text": "Vietnam War: Vietnamization continues as President <PERSON> announces that the United States will withdraw 40,000 more troops before Christmas.", "html": "1970 - Vietnam War: <a href=\"https://wikipedia.org/wiki/Vietnamization\" title=\"Vietnamization\">Vietnamization</a> continues as President <PERSON> announces that the United States will withdraw 40,000 more troops before Christmas.", "no_year_html": "Vietnam War: <a href=\"https://wikipedia.org/wiki/Vietnamization\" title=\"Vietnamization\">Vietnamization</a> continues as President <PERSON> announces that the United States will withdraw 40,000 more troops before Christmas.", "links": [{"title": "Vietnamization", "link": "https://wikipedia.org/wiki/Vietnamization"}]}, {"year": "1971", "text": "The 2,500 year celebration of the Persian Empire begins.", "html": "1971 - The <a href=\"https://wikipedia.org/wiki/2,500_year_celebration_of_the_Persian_Empire\" class=\"mw-redirect\" title=\"2,500 year celebration of the Persian Empire\">2,500 year celebration of the Persian Empire</a> begins.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/2,500_year_celebration_of_the_Persian_Empire\" class=\"mw-redirect\" title=\"2,500 year celebration of the Persian Empire\">2,500 year celebration of the Persian Empire</a> begins.", "links": [{"title": "2,500 year celebration of the Persian Empire", "link": "https://wikipedia.org/wiki/2,500_year_celebration_of_the_Persian_Empire"}]}, {"year": "1973", "text": "President <PERSON> nominates House Minority Leader <PERSON> as the successor to Vice President <PERSON><PERSON><PERSON>.", "html": "1973 - President <PERSON> nominates House Minority Leader <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> as the successor to Vice President <a href=\"https://wikipedia.org/wiki/Spiro_Agnew\" title=\"<PERSON><PERSON><PERSON>gnew\"><PERSON><PERSON><PERSON></a>.", "no_year_html": "President <PERSON> nominates House Minority Leader <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> as the successor to Vice President <a href=\"https://wikipedia.org/wiki/Spiro_Agnew\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/S<PERSON><PERSON>_<PERSON>gnew"}]}, {"year": "1976", "text": "Indian Airlines Flight 171 crashes at Santacruz Airport in Bombay, India, killing 95.", "html": "1976 - <a href=\"https://wikipedia.org/wiki/Indian_Airlines_Flight_171\" title=\"Indian Airlines Flight 171\">Indian Airlines Flight 171</a> crashes at <a href=\"https://wikipedia.org/wiki/Chhatrapati_Shivaji_Maharaj_International_Airport\" title=\"Chhatrapati Shivaji Maharaj International Airport\">Santacruz Airport</a> in <a href=\"https://wikipedia.org/wiki/Mumbai\" title=\"Mumbai\">Bombay</a>, India, killing 95.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Indian_Airlines_Flight_171\" title=\"Indian Airlines Flight 171\">Indian Airlines Flight 171</a> crashes at <a href=\"https://wikipedia.org/wiki/Chhatrapati_Shivaji_Maharaj_International_Airport\" title=\"Chhatrapati Shivaji Maharaj International Airport\">Santacruz Airport</a> in <a href=\"https://wikipedia.org/wiki/Mumbai\" title=\"Mumbai\">Bombay</a>, India, killing 95.", "links": [{"title": "Indian Airlines Flight 171", "link": "https://wikipedia.org/wiki/Indian_Airlines_Flight_171"}, {"title": "<PERSON><PERSON><PERSON><PERSON> Maharaj International Airport", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON><PERSON>_Maharaj_International_Airport"}, {"title": "Mumbai", "link": "https://wikipedia.org/wiki/Mumbai"}]}, {"year": "1977", "text": "<PERSON><PERSON> succeeds <PERSON> as paramount leader of China.", "html": "1977 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a> succeeds <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> as <a href=\"https://wikipedia.org/wiki/Paramount_leader\" title=\"Paramount leader\">paramount leader</a> of China.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a> succeeds <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> as <a href=\"https://wikipedia.org/wiki/Paramount_leader\" title=\"Paramount leader\">paramount leader</a> of China.", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Paramount leader", "link": "https://wikipedia.org/wiki/Paramount_leader"}]}, {"year": "1979", "text": "Typhoon <PERSON><PERSON> becomes the largest and most intense tropical cyclone ever recorded.", "html": "1979 - <a href=\"https://wikipedia.org/wiki/Typhoon_Tip\" title=\"Typhoon Tip\">Typhoon Tip</a> becomes the largest and most intense tropical cyclone ever recorded.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Typhoon_Tip\" title=\"Typhoon Tip\">Typhoon Tip</a> becomes the largest and most intense tropical cyclone ever recorded.", "links": [{"title": "Typhoon Tip", "link": "https://wikipedia.org/wiki/Typhoon_Tip"}]}, {"year": "1983", "text": "Japan's former Prime Minister <PERSON> is found guilty of taking a $2 million bribe from the Lockheed Corporation, and is sentenced to four years in jail.", "html": "1983 - Japan's former Prime Minister <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON></a> is found guilty of taking a <a href=\"https://wikipedia.org/wiki/Lockheed_bribery_scandals\" title=\"Lockheed bribery scandals\">$2 million bribe</a> from the <a href=\"https://wikipedia.org/wiki/Lockheed_Corporation\" title=\"Lockheed Corporation\">Lockheed Corporation</a>, and is sentenced to four years in jail.", "no_year_html": "Japan's former Prime Minister <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON></a> is found guilty of taking a <a href=\"https://wikipedia.org/wiki/Lockheed_bribery_scandals\" title=\"Lockheed bribery scandals\">$2 million bribe</a> from the <a href=\"https://wikipedia.org/wiki/Lockheed_Corporation\" title=\"Lockheed Corporation\">Lockheed Corporation</a>, and is sentenced to four years in jail.", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}, {"title": "Lockheed bribery scandals", "link": "https://wikipedia.org/wiki/Lockheed_bribery_scandals"}, {"title": "Lockheed Corporation", "link": "https://wikipedia.org/wiki/Lockheed_Corporation"}]}, {"year": "1984", "text": "The Provisional Irish Republican Army fail to assassinate Prime Minister <PERSON> and her cabinet.  The bomb kills five people and wounds at least 31 others.", "html": "1984 - The <a href=\"https://wikipedia.org/wiki/Provisional_Irish_Republican_Army\" title=\"Provisional Irish Republican Army\">Provisional Irish Republican Army</a> <a href=\"https://wikipedia.org/wiki/Brighton_hotel_bombing\" title=\"Brighton hotel bombing\">fail to assassinate</a> Prime Minister <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> and her <a href=\"https://wikipedia.org/wiki/Second_Thatcher_ministry\" title=\"Second Thatcher ministry\">cabinet</a>. The bomb kills five people and wounds at least 31 others.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Provisional_Irish_Republican_Army\" title=\"Provisional Irish Republican Army\">Provisional Irish Republican Army</a> <a href=\"https://wikipedia.org/wiki/Brighton_hotel_bombing\" title=\"Brighton hotel bombing\">fail to assassinate</a> Prime Minister <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> and her <a href=\"https://wikipedia.org/wiki/Second_Thatcher_ministry\" title=\"Second Thatcher ministry\">cabinet</a>. The bomb kills five people and wounds at least 31 others.", "links": [{"title": "Provisional Irish Republican Army", "link": "https://wikipedia.org/wiki/Provisional_Irish_Republican_Army"}, {"title": "Brighton hotel bombing", "link": "https://wikipedia.org/wiki/Brighton_hotel_bombing"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Second <PERSON> ministry", "link": "https://wikipedia.org/wiki/Second_Thatcher_ministry"}]}, {"year": "1988", "text": "Two officers of the Victoria Police are gunned down execution-style in the Walsh Street police shootings, Australia.", "html": "1988 - Two officers of the <a href=\"https://wikipedia.org/wiki/Victoria_Police\" title=\"Victoria Police\">Victoria Police</a> are gunned down execution-style in the <a href=\"https://wikipedia.org/wiki/Walsh_Street_police_shootings\" title=\"Walsh Street police shootings\">Walsh Street police shootings</a>, Australia.", "no_year_html": "Two officers of the <a href=\"https://wikipedia.org/wiki/Victoria_Police\" title=\"Victoria Police\">Victoria Police</a> are gunned down execution-style in the <a href=\"https://wikipedia.org/wiki/Walsh_Street_police_shootings\" title=\"Walsh Street police shootings\">Walsh Street police shootings</a>, Australia.", "links": [{"title": "Victoria Police", "link": "https://wikipedia.org/wiki/Victoria_Police"}, {"title": "Walsh Street police shootings", "link": "https://wikipedia.org/wiki/Walsh_Street_police_shootings"}]}, {"year": "1992", "text": "A 5.8 earthquake occurred in Cairo, Egypt. At least 510 died.", "html": "1992 - A <a href=\"https://wikipedia.org/wiki/1992_Cairo_earthquake\" title=\"1992 Cairo earthquake\">5.8 earthquake occurred</a> in Cairo, Egypt. At least 510 died.", "no_year_html": "A <a href=\"https://wikipedia.org/wiki/1992_Cairo_earthquake\" title=\"1992 Cairo earthquake\">5.8 earthquake occurred</a> in Cairo, Egypt. At least 510 died.", "links": [{"title": "1992 Cairo earthquake", "link": "https://wikipedia.org/wiki/1992_Cairo_earthquake"}]}, {"year": "1994", "text": "The Magellan spacecraft burns up in the atmosphere of Venus.", "html": "1994 - The <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_(spacecraft)\" title=\"<PERSON><PERSON><PERSON> (spacecraft)\">Magellan spacecraft</a> burns up in the <a href=\"https://wikipedia.org/wiki/Atmosphere_of_Venus\" title=\"Atmosphere of Venus\">atmosphere of Venus</a>.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_(spacecraft)\" title=\"<PERSON><PERSON><PERSON> (spacecraft)\">Magellan spacecraft</a> burns up in the <a href=\"https://wikipedia.org/wiki/Atmosphere_of_Venus\" title=\"Atmosphere of Venus\">atmosphere of Venus</a>.", "links": [{"title": "<PERSON><PERSON><PERSON> (spacecraft)", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_(spacecraft)"}, {"title": "Atmosphere of Venus", "link": "https://wikipedia.org/wiki/Atmosphere_of_Venus"}]}, {"year": "1994", "text": "Iran Aseman Airlines Flight 746 crashes near Natanz, Iran, killing all 66 people on board.", "html": "1994 - <a href=\"https://wikipedia.org/wiki/Iran_Aseman_Airlines_Flight_746\" title=\"Iran Aseman Airlines Flight 746\">Iran Aseman Airlines Flight 746</a> crashes near <a href=\"https://wikipedia.org/wiki/Natanz\" title=\"Natanz\">Natanz</a>, Iran, killing all 66 people on board.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Iran_Aseman_Airlines_Flight_746\" title=\"Iran Aseman Airlines Flight 746\">Iran Aseman Airlines Flight 746</a> crashes near <a href=\"https://wikipedia.org/wiki/Natanz\" title=\"Natanz\">Natanz</a>, Iran, killing all 66 people on board.", "links": [{"title": "Iran Aseman Airlines Flight 746", "link": "https://wikipedia.org/wiki/Iran_Aseman_Airlines_Flight_746"}, {"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Natanz"}]}, {"year": "1996", "text": "New Zealand holds its first general election under the new mixed-member proportional representation system, which led to <PERSON>'s National Party forming a coalition government with <PERSON>'s New Zealand First.", "html": "1996 - <a href=\"https://wikipedia.org/wiki/New_Zealand\" title=\"New Zealand\">New Zealand</a> holds its <a href=\"https://wikipedia.org/wiki/1996_New_Zealand_general_election\" title=\"1996 New Zealand general election\">first general election</a> under the new <a href=\"https://wikipedia.org/wiki/Mixed-member_proportional_representation\" title=\"Mixed-member proportional representation\">mixed-member proportional representation</a> system, which led to <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>'s <a href=\"https://wikipedia.org/wiki/New_Zealand_National_Party\" title=\"New Zealand National Party\">National Party</a> forming a <a href=\"https://wikipedia.org/wiki/Coalition_government\" title=\"Coalition government\">coalition government</a> with <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON>'s</a> <a href=\"https://wikipedia.org/wiki/New_Zealand_First\" title=\"New Zealand First\">New Zealand First</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/New_Zealand\" title=\"New Zealand\">New Zealand</a> holds its <a href=\"https://wikipedia.org/wiki/1996_New_Zealand_general_election\" title=\"1996 New Zealand general election\">first general election</a> under the new <a href=\"https://wikipedia.org/wiki/Mixed-member_proportional_representation\" title=\"Mixed-member proportional representation\">mixed-member proportional representation</a> system, which led to <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>'s <a href=\"https://wikipedia.org/wiki/New_Zealand_National_Party\" title=\"New Zealand National Party\">National Party</a> forming a <a href=\"https://wikipedia.org/wiki/Coalition_government\" title=\"Coalition government\">coalition government</a> with <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON>'s</a> <a href=\"https://wikipedia.org/wiki/New_Zealand_First\" title=\"New Zealand First\">New Zealand First</a>.", "links": [{"title": "New Zealand", "link": "https://wikipedia.org/wiki/New_Zealand"}, {"title": "1996 New Zealand general election", "link": "https://wikipedia.org/wiki/1996_New_Zealand_general_election"}, {"title": "Mixed-member proportional representation", "link": "https://wikipedia.org/wiki/Mixed-member_proportional_representation"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "New Zealand National Party", "link": "https://wikipedia.org/wiki/New_Zealand_National_Party"}, {"title": "Coalition government", "link": "https://wikipedia.org/wiki/Coalition_government"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "New Zealand First", "link": "https://wikipedia.org/wiki/New_Zealand_First"}]}, {"year": "1997", "text": "The <PERSON><PERSON> massacre in Algeria kills 43 people at a fake roadblock.", "html": "1997 - The <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_massacre\" class=\"mw-redirect\" title=\"<PERSON><PERSON>oud massacre\"><PERSON><PERSON> massacre</a> in Algeria kills 43 people at a fake roadblock.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_massacre\" class=\"mw-redirect\" title=\"<PERSON><PERSON>oud massacre\"><PERSON><PERSON> massacre</a> in Algeria kills 43 people at a fake roadblock.", "links": [{"title": "<PERSON><PERSON> massacre", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_massacre"}]}, {"year": "1998", "text": "<PERSON>, a gay student at University of Wyoming, dies five days after he was beaten outside of Laramie.", "html": "1998 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, a gay student at <a href=\"https://wikipedia.org/wiki/University_of_Wyoming\" title=\"University of Wyoming\">University of Wyoming</a>, dies five days after he was beaten outside of Laramie.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, a gay student at <a href=\"https://wikipedia.org/wiki/University_of_Wyoming\" title=\"University of Wyoming\">University of Wyoming</a>, dies five days after he was beaten outside of Laramie.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}, {"title": "University of Wyoming", "link": "https://wikipedia.org/wiki/University_of_Wyoming"}]}, {"year": "1999", "text": "<PERSON><PERSON> takes power in Pakistan from Nawaz Sharif through a bloodless coup.", "html": "1999 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a> takes power in Pakistan from <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a> through a bloodless <a href=\"https://wikipedia.org/wiki/1999_Pakistani_coup_d%27%C3%A9tat\" title=\"1999 Pakistani coup d'état\">coup</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a> takes power in Pakistan from <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a> through a bloodless <a href=\"https://wikipedia.org/wiki/1999_Pakistani_coup_d%27%C3%A9tat\" title=\"1999 Pakistani coup d'état\">coup</a>.", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>f"}, {"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}, {"title": "1999 Pakistani coup d'état", "link": "https://wikipedia.org/wiki/1999_Pakistani_coup_d%27%C3%A9tat"}]}, {"year": "1999", "text": "The former Autonomous Soviet Republic of Abkhazia declares its independence from Georgia.", "html": "1999 - The former Autonomous Soviet Republic of <a href=\"https://wikipedia.org/wiki/Abkhazia\" title=\"Abkhazia\">Abkhazia</a> declares its independence from <a href=\"https://wikipedia.org/wiki/Georgia_(country)\" title=\"Georgia (country)\">Georgia</a>.", "no_year_html": "The former Autonomous Soviet Republic of <a href=\"https://wikipedia.org/wiki/Abkhazia\" title=\"Abkhazia\">Abkhazia</a> declares its independence from <a href=\"https://wikipedia.org/wiki/Georgia_(country)\" title=\"Georgia (country)\">Georgia</a>.", "links": [{"title": "Abkhazia", "link": "https://wikipedia.org/wiki/Abkhazia"}, {"title": "Georgia (country)", "link": "https://wikipedia.org/wiki/Georgia_(country)"}]}, {"year": "2000", "text": "The USS Cole, a US Navy destroyer, is badly damaged by two al-Qaeda suicide bombers, killing 17 crew members and wounding at least 39.", "html": "2000 - The <a href=\"https://wikipedia.org/wiki/USS_Cole_(DDG-67)\" title=\"USS Cole (DDG-67)\">USS <i>Cole</i></a>, a <a href=\"https://wikipedia.org/wiki/US_Navy\" class=\"mw-redirect\" title=\"US Navy\">US Navy</a> <a href=\"https://wikipedia.org/wiki/Destroyer\" title=\"Destroyer\">destroyer</a>, <a href=\"https://wikipedia.org/wiki/USS_Cole_bombing\" title=\"USS Cole bombing\">is badly damaged</a> by two <a href=\"https://wikipedia.org/wiki/Al-Qaeda\" title=\"Al-Qaeda\">al-Qaeda</a> suicide bombers, killing 17 crew members and wounding at least 39.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/USS_Cole_(DDG-67)\" title=\"USS Cole (DDG-67)\">USS <i>Cole</i></a>, a <a href=\"https://wikipedia.org/wiki/US_Navy\" class=\"mw-redirect\" title=\"US Navy\">US Navy</a> <a href=\"https://wikipedia.org/wiki/Destroyer\" title=\"Destroyer\">destroyer</a>, <a href=\"https://wikipedia.org/wiki/USS_Cole_bombing\" title=\"USS Cole bombing\">is badly damaged</a> by two <a href=\"https://wikipedia.org/wiki/Al-Qaeda\" title=\"Al-Qaeda\">al-Qaeda</a> suicide bombers, killing 17 crew members and wounding at least 39.", "links": [{"title": "<PERSON> (DDG-67)", "link": "https://wikipedia.org/wiki/USS_Cole_(DDG-67)"}, {"title": "US Navy", "link": "https://wikipedia.org/wiki/US_Navy"}, {"title": "Destroyer", "link": "https://wikipedia.org/wiki/Destroyer"}, {"title": "USS Cole bombing", "link": "https://wikipedia.org/wiki/USS_Cole_bombing"}, {"title": "Al-Qaeda", "link": "https://wikipedia.org/wiki/Al-Qaeda"}]}, {"year": "2002", "text": "Terrorists detonate bombs in two nightclubs in Kuta, Bali, Indonesia, killing 202 and wounding over 200.", "html": "2002 - Terrorists <a href=\"https://wikipedia.org/wiki/2002_Bali_bombings\" title=\"2002 Bali bombings\">detonate bombs</a> in two nightclubs in <a href=\"https://wikipedia.org/wiki/Kuta,_Bali\" title=\"Kuta, Bali\">Kuta, Bali</a>, Indonesia, killing 202 and wounding over 200.", "no_year_html": "Terrorists <a href=\"https://wikipedia.org/wiki/2002_Bali_bombings\" title=\"2002 Bali bombings\">detonate bombs</a> in two nightclubs in <a href=\"https://wikipedia.org/wiki/Kuta,_Bali\" title=\"Kuta, Bali\">Kuta, Bali</a>, Indonesia, killing 202 and wounding over 200.", "links": [{"title": "2002 Bali bombings", "link": "https://wikipedia.org/wiki/2002_Bali_bombings"}, {"title": "Kuta, Bali", "link": "https://wikipedia.org/wiki/Kuta,_Bali"}]}, {"year": "2005", "text": "The second Chinese human spaceflight, Shenzhou 6, is launched, carrying two cosmonauts in orbit for five days.", "html": "2005 - The second Chinese human spaceflight, <i><a href=\"https://wikipedia.org/wiki/Shenzhou_6\" title=\"Shenzhou 6\">Shenzhou 6</a></i>, is launched, carrying two cosmonauts in orbit for five days.", "no_year_html": "The second Chinese human spaceflight, <i><a href=\"https://wikipedia.org/wiki/Shenzhou_6\" title=\"Shenzhou 6\">Shenzhou 6</a></i>, is launched, carrying two cosmonauts in orbit for five days.", "links": [{"title": "Shenzhou 6", "link": "https://wikipedia.org/wiki/Shenzhou_6"}]}, {"year": "2010", "text": "The Finnish Yle TV2 channel's <PERSON><PERSON><PERSON><PERSON><PERSON> kakkonen current affairs program airs controversial Homoilta episode (literally \"gay night\"), which leads to the resignation of almost 50,000 Finns from the Evangelical Lutheran Church.", "html": "2010 - The Finnish <a href=\"https://wikipedia.org/wiki/Yle_TV2\" title=\"Yle TV2\">Yle TV2</a> channel's <i><a href=\"https://wikipedia.org/wiki/A<PERSON><PERSON><PERSON><PERSON>_kakkonen\" title=\"Ajanko<PERSON>inen kakkonen\">Ajan<PERSON><PERSON><PERSON> kakkonen</a></i> current affairs program airs controversial <a href=\"https://wikipedia.org/wiki/Homoilta\" class=\"mw-redirect\" title=\"Homoilta\"><i>Homoilta</i> episode</a> (literally \"gay night\"), which leads to the resignation of almost 50,000 <a href=\"https://wikipedia.org/wiki/Finns\" title=\"Finns\">Finns</a> from the <a href=\"https://wikipedia.org/wiki/Evangelical_Lutheran_Church_of_Finland\" title=\"Evangelical Lutheran Church of Finland\">Evangelical Lutheran Church</a>.", "no_year_html": "The Finnish <a href=\"https://wikipedia.org/wiki/Yle_TV2\" title=\"Yle TV2\">Yle TV2</a> channel's <i><a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON><PERSON>_kakkonen\" title=\"Ajanko<PERSON>inen kakkonen\">A<PERSON><PERSON><PERSON><PERSON> kakkonen</a></i> current affairs program airs controversial <a href=\"https://wikipedia.org/wiki/Homoilta\" class=\"mw-redirect\" title=\"Homoilta\"><i>Homoilta</i> episode</a> (literally \"gay night\"), which leads to the resignation of almost 50,000 <a href=\"https://wikipedia.org/wiki/Finns\" title=\"Finns\">Finns</a> from the <a href=\"https://wikipedia.org/wiki/Evangelical_Lutheran_Church_of_Finland\" title=\"Evangelical Lutheran Church of Finland\">Evangelical Lutheran Church</a>.", "links": [{"title": "Yle TV2", "link": "https://wikipedia.org/wiki/Yle_TV2"}, {"title": "<PERSON><PERSON><PERSON><PERSON><PERSON> kakkon<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON><PERSON>_kakkonen"}, {"title": "Ho<PERSON>ilt<PERSON>", "link": "https://wikipedia.org/wiki/Homoilta"}, {"title": "Finns", "link": "https://wikipedia.org/wiki/Finns"}, {"title": "Evangelical Lutheran Church of Finland", "link": "https://wikipedia.org/wiki/Evangelical_Lutheran_Church_of_Finland"}]}, {"year": "2012", "text": "The European Union wins the 2012 Nobel Peace Prize.", "html": "2012 - The <a href=\"https://wikipedia.org/wiki/European_Union\" title=\"European Union\">European Union</a> wins the <a href=\"https://wikipedia.org/wiki/2012_Nobel_Peace_Prize\" title=\"2012 Nobel Peace Prize\">2012 Nobel Peace Prize</a>.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/European_Union\" title=\"European Union\">European Union</a> wins the <a href=\"https://wikipedia.org/wiki/2012_Nobel_Peace_Prize\" title=\"2012 Nobel Peace Prize\">2012 Nobel Peace Prize</a>.", "links": [{"title": "European Union", "link": "https://wikipedia.org/wiki/European_Union"}, {"title": "2012 Nobel Peace Prize", "link": "https://wikipedia.org/wiki/2012_Nobel_Peace_Prize"}]}, {"year": "2013", "text": "Fifty-one people are killed after a truck veers off a cliff in Peru.", "html": "2013 - Fifty-one people are killed after a truck <a href=\"https://wikipedia.org/wiki/2013_Peru_bus_disaster\" title=\"2013 Peru bus disaster\">veers off a cliff</a> in Peru.", "no_year_html": "Fifty-one people are killed after a truck <a href=\"https://wikipedia.org/wiki/2013_Peru_bus_disaster\" title=\"2013 Peru bus disaster\">veers off a cliff</a> in Peru.", "links": [{"title": "2013 Peru bus disaster", "link": "https://wikipedia.org/wiki/2013_Peru_bus_disaster"}]}, {"year": "2013", "text": "An apartment building collapse in Medellín, Colombia results in the deaths of twelve people.", "html": "2013 - <a href=\"https://wikipedia.org/wiki/Collapse_of_the_Space_Building\" title=\"Collapse of the Space Building\">An apartment building collapse</a> in <a href=\"https://wikipedia.org/wiki/Medell%C3%ADn\" title=\"Medellín\">Medellín, Colombia</a> results in the deaths of twelve people.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Collapse_of_the_Space_Building\" title=\"Collapse of the Space Building\">An apartment building collapse</a> in <a href=\"https://wikipedia.org/wiki/Medell%C3%ADn\" title=\"Medellín\">Medellín, Colombia</a> results in the deaths of twelve people.", "links": [{"title": "Collapse of the Space Building", "link": "https://wikipedia.org/wiki/Collapse_of_the_Space_Building"}, {"title": "Medellín", "link": "https://wikipedia.org/wiki/Medell%C3%ADn"}]}, {"year": "2017", "text": "The United States announces its decision to withdraw from UNESCO. Israel immediately follows.", "html": "2017 - The United States announces its decision to withdraw from <a href=\"https://wikipedia.org/wiki/UNESCO\" title=\"UNESCO\">UNESCO</a>. Israel immediately follows.", "no_year_html": "The United States announces its decision to withdraw from <a href=\"https://wikipedia.org/wiki/UNESCO\" title=\"UNESCO\">UNESCO</a>. Israel immediately follows.", "links": [{"title": "UNESCO", "link": "https://wikipedia.org/wiki/UNESCO"}]}, {"year": "2018", "text": "Princess <PERSON><PERSON><PERSON> marries <PERSON> at St. George's Chapel, Windsor Castle.", "html": "2018 - <a href=\"https://wikipedia.org/wiki/Princess_<PERSON><PERSON><PERSON>\" title=\"Princess <PERSON><PERSON><PERSON>\">Princess <PERSON><PERSON><PERSON></a> <a href=\"https://wikipedia.org/wiki/Wedding_of_Princess_<PERSON><PERSON>ie_and_<PERSON>_<PERSON>\" title=\"Wedding of Princess <PERSON><PERSON><PERSON> and <PERSON>\">marries</a> <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> at <a href=\"https://wikipedia.org/wiki/St_George%27s_Chapel,_Windsor_Castle\" title=\"St George's Chapel, Windsor Castle\">St. George's Chapel, Windsor Castle</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Princess_<PERSON><PERSON><PERSON>\" title=\"Princess <PERSON><PERSON><PERSON>\">Princess <PERSON><PERSON><PERSON></a> <a href=\"https://wikipedia.org/wiki/Wedding_of_Princess_<PERSON><PERSON>ie_and_<PERSON>_<PERSON>\" title=\"Wedding of Princess <PERSON><PERSON><PERSON> and <PERSON>\">marries</a> <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> at <a href=\"https://wikipedia.org/wiki/St_George%27s_Chapel,_Windsor_Castle\" title=\"St George's Chapel, Windsor Castle\">St. George's Chapel, Windsor Castle</a>.", "links": [{"title": "Princess <PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Princess_<PERSON><PERSON><PERSON>"}, {"title": "Wedding of Princess <PERSON><PERSON><PERSON> and <PERSON>", "link": "https://wikipedia.org/wiki/Wedding_of_Princess_<PERSON><PERSON><PERSON>_and_<PERSON>_<PERSON>"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "St George's Chapel, Windsor Castle", "link": "https://wikipedia.org/wiki/St_George%27s_Chapel,_Windsor_Castle"}]}, {"year": "2019", "text": "Typhoon <PERSON><PERSON><PERSON> makes landfall in Japan, killing 10 and forcing the evacuation of one million people.", "html": "2019 - <a href=\"https://wikipedia.org/wiki/Typhoon_Hagibis_(2019)\" class=\"mw-redirect\" title=\"Typhoon Hagibis (2019)\">Typhoon Ha<PERSON></a> makes landfall in Japan, killing 10 and forcing the evacuation of one million people.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Typhoon_Hagibis_(2019)\" class=\"mw-redirect\" title=\"Typhoon Hagibis (2019)\">Typhoon <PERSON><PERSON></a> makes landfall in Japan, killing 10 and forcing the evacuation of one million people.", "links": [{"title": "Typhoon Hagibis (2019)", "link": "https://wikipedia.org/wiki/Typhoon_Hagi<PERSON>_(2019)"}]}, {"year": "2019", "text": "<PERSON><PERSON> from Kenya becomes the first person to run a marathon in less than two hours with a time of 1:59:40 in Vienna.", "html": "2019 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a> from Kenya becomes the first person to run a <a href=\"https://wikipedia.org/wiki/Marathon\" title=\"Marathon\">marathon</a> in <a href=\"https://wikipedia.org/wiki/Ineos_1:59_Challenge\" title=\"Ineos 1:59 Challenge\">less than two hours</a> with a time of 1:59:40 in Vienna.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a> from Kenya becomes the first person to run a <a href=\"https://wikipedia.org/wiki/Marathon\" title=\"Marathon\">marathon</a> in <a href=\"https://wikipedia.org/wiki/Ineos_1:59_Challenge\" title=\"Ineos 1:59 Challenge\">less than two hours</a> with a time of 1:59:40 in Vienna.", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}, {"title": "Marathon", "link": "https://wikipedia.org/wiki/Marathon"}, {"title": "Ineos 1:59 Challenge", "link": "https://wikipedia.org/wiki/Ineos_1:59_Challenge"}]}, {"year": "2019", "text": "The Hard Rock Hotel in New Orleans, which is under construction, collapses, killing three workers and injuring 30 others.", "html": "2019 - The <a href=\"https://wikipedia.org/wiki/1031_Canal\" title=\"1031 Canal\">Hard Rock Hotel in New Orleans</a>, which is under construction, collapses, killing three workers and injuring 30 others.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/1031_Canal\" title=\"1031 Canal\">Hard Rock Hotel in New Orleans</a>, which is under construction, collapses, killing three workers and injuring 30 others.", "links": [{"title": "1031 Canal", "link": "https://wikipedia.org/wiki/1031_Canal"}]}, {"year": "2022", "text": "2022 Bratislava shooting, killing 3 (including the perpetrator) and injuring one. The shooting occurred outside of a gay bar in Bratislava known as Tepláreň. Two people (excluding the perpetrator) died as a result of the shooting. <PERSON><PERSON>, a non-binary person, and <PERSON><PERSON><PERSON>, a bisexual man. The perpetrator (<PERSON><PERSON>) was found dead due to a self inflicted gunshot the morning after the attack.", "html": "2022 - <a href=\"https://wikipedia.org/wiki/2022_Bratislava_shooting\" title=\"2022 Bratislava shooting\">2022 Bratislava shooting</a>, killing 3 (including the perpetrator) and injuring one. The shooting occurred outside of a gay bar in Bratislava known as Tepláreň. Two people (excluding the perpetrator) died as a result of the shooting. <PERSON><PERSON>, a non-binary person, and <PERSON><PERSON><PERSON>, a bisexual man. The perpetrator (<PERSON><PERSON>) was found dead due to a self inflicted gunshot the morning after the attack.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/2022_Bratislava_shooting\" title=\"2022 Bratislava shooting\">2022 Bratislava shooting</a>, killing 3 (including the perpetrator) and injuring one. The shooting occurred outside of a gay bar in Bratislava known as Tepláreň. Two people (excluding the perpetrator) died as a result of the shooting. <PERSON><PERSON>, a non-binary person, and <PERSON><PERSON><PERSON>, a bisexual man. The perpetrator (<PERSON><PERSON>) was found dead due to a self inflicted gunshot the morning after the attack.", "links": [{"title": "2022 Bratislava shooting", "link": "https://wikipedia.org/wiki/2022_Bratislava_shooting"}]}], "Births": [{"year": "1008", "text": "<PERSON><PERSON><PERSON><PERSON><PERSON>, emperor of Japan (d. 1036)", "html": "1008 - <a href=\"https://wikipedia.org/wiki/Emperor_<PERSON>-I<PERSON>j%C5%8D\" title=\"Emperor <PERSON>-Ichijō\"><PERSON><PERSON><PERSON><PERSON><PERSON></a>, emperor of Japan (d. 1036)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Emperor_<PERSON>-<PERSON><PERSON>j%C5%8D\" title=\"Emperor <PERSON>-Ichijō\"><PERSON><PERSON><PERSON><PERSON><PERSON></a>, emperor of Japan (d. 1036)", "links": [{"title": "Emperor <PERSON><PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Emperor_<PERSON>-Ichij%C5%8D"}]}, {"year": "1240", "text": "<PERSON><PERSON><PERSON><PERSON>, emperor of Vietnam (then Đại Việt) (d. 1290)", "html": "1240 - <a href=\"https://wikipedia.org/wiki/Tr%E1%BA%A7n_Th%C3%A1nh_T%C3%B4ng\" title=\"Trần Thánh Tông\"><PERSON><PERSON><PERSON><PERSON> <PERSON></a>, emperor of Vietnam (then <a href=\"https://wikipedia.org/wiki/%C4%90%E1%BA%A1i_Vi%E1%BB%87t\" title=\"Đại Việt\">Đ<PERSON><PERSON></a>) (d. 1290)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Tr%E1%BA%A7n_Th%C3%A1nh_T%C3%B4ng\" title=\"Trần Thánh Tông\"><PERSON><PERSON><PERSON><PERSON> <PERSON></a>, emperor of Vietnam (then <a href=\"https://wikipedia.org/wiki/%C4%90%E1%BA%A1i_Vi%E1%BB%87t\" title=\"Đại Việt\">Đ<PERSON><PERSON></a>) (d. 1290)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Tr%E1%BA%A7n_Th%C3%A1nh_T%C3%B4ng"}, {"title": "Đại <PERSON>", "link": "https://wikipedia.org/wiki/%C4%90%E1%BA%A1i_Vi%E1%BB%87t"}]}, {"year": "1350", "text": "<PERSON><PERSON><PERSON>, Grand Duke of Moscow (d. 1389)", "html": "1350 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Grand Duke of Moscow (d. 1389)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Grand Duke of Moscow (d. 1389)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1490", "text": "<PERSON>, Italian composer and priest (d. 1548)", "html": "1490 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian composer and priest (d. 1548)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian composer and priest (d. 1548)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1533", "text": "<PERSON><PERSON><PERSON>, Japanese ruler (d. 1573)", "html": "1533 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>shikage\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese ruler (d. 1573)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>ka<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese ruler (d. 1573)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON><PERSON><PERSON>ge"}]}, {"year": "1537", "text": "<PERSON>, king of England (d. 1553)", "html": "1537 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_England\" class=\"mw-redirect\" title=\"<PERSON> VI of England\"><PERSON> VI</a>, king of England (d. 1553)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_VI_of_England\" class=\"mw-redirect\" title=\"<PERSON> VI of England\"><PERSON> VI</a>, king of England (d. 1553)", "links": [{"title": "<PERSON> of England", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_England"}]}, {"year": "1555", "text": "<PERSON><PERSON><PERSON>, 13th Baron <PERSON>, English diplomat (d. 1601)", "html": "1555 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>,_13th_Baron_<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON> <PERSON>, 13th Baron <PERSON>\"><PERSON><PERSON><PERSON>, 13th Baron <PERSON></a>, English diplomat (d. 1601)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>,_13th_Baron_<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON> <PERSON>, 13th Baron <PERSON>\"><PERSON><PERSON><PERSON> <PERSON>, 13th Baron <PERSON></a>, English diplomat (d. 1601)", "links": [{"title": "<PERSON><PERSON><PERSON>, 13th Baron <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>,_13th_Baron_<PERSON>_<PERSON>_<PERSON><PERSON>"}]}, {"year": "1558", "text": "<PERSON>, archduke of Austria (d. 1618)", "html": "1558 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Archduke_of_Austria\" title=\"<PERSON>, Archduke of Austria\"><PERSON></a>, archduke of Austria (d. 1618)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Archduke_of_Austria\" title=\"<PERSON>, Archduke of Austria\"><PERSON></a>, archduke of Austria (d. 1618)", "links": [{"title": "<PERSON>, Archduke of Austria", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Archduke_of_Austria"}]}, {"year": "1559", "text": "<PERSON>, French scholar and Jesuit (d. 1651)", "html": "1559 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French scholar and Jesuit (d. 1651)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French scholar and Jesuit (d. 1651)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1576", "text": "<PERSON>, English-American soldier and politician, 3rd Governor of the Massachusetts Bay Colony (d. 1653)", "html": "1576 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English-American soldier and politician, 3rd <a href=\"https://wikipedia.org/wiki/List_of_colonial_governors_of_Massachusetts\" title=\"List of colonial governors of Massachusetts\">Governor of the Massachusetts Bay Colony</a> (d. 1653)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English-American soldier and politician, 3rd <a href=\"https://wikipedia.org/wiki/List_of_colonial_governors_of_Massachusetts\" title=\"List of colonial governors of Massachusetts\">Governor of the Massachusetts Bay Colony</a> (d. 1653)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "List of colonial governors of Massachusetts", "link": "https://wikipedia.org/wiki/List_of_colonial_governors_of_Massachusetts"}]}, {"year": "1602", "text": "<PERSON>, English scholar and theologian (d. 1644)", "html": "1602 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English scholar and theologian (d. 1644)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English scholar and theologian (d. 1644)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1614", "text": "<PERSON>, English philosopher (d. 1687)", "html": "1614 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English philosopher (d. 1687)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> More\"><PERSON></a>, English philosopher (d. 1687)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1687", "text": "<PERSON><PERSON><PERSON><PERSON>, German lute player and composer (d. 1750)", "html": "1687 - <a href=\"https://wikipedia.org/wiki/<PERSON>yl<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>yl<PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, German lute player and composer (d. 1750)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>yl<PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, German lute player and composer (d. 1750)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>yl<PERSON><PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1710", "text": "<PERSON>, American colonel and politician, 16th Governor of Connecticut (d. 1785)", "html": "1710 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American colonel and politician, 16th <a href=\"https://wikipedia.org/wiki/Governor_of_Connecticut\" class=\"mw-redirect\" title=\"Governor of Connecticut\">Governor of Connecticut</a> (d. 1785)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American colonel and politician, 16th <a href=\"https://wikipedia.org/wiki/Governor_of_Connecticut\" class=\"mw-redirect\" title=\"Governor of Connecticut\">Governor of Connecticut</a> (d. 1785)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Governor of Connecticut", "link": "https://wikipedia.org/wiki/Governor_of_Connecticut"}]}, {"year": "1725", "text": "<PERSON>, French pharmacist and entomologist (d. 1810)", "html": "1725 - <a href=\"https://wikipedia.org/wiki/%C3%89tienne_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French pharmacist and entomologist (d. 1810)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/%C3%89tienne_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French pharmacist and entomologist (d. 1810)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/%C3%89tien<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1792", "text": "<PERSON>, German chemist and pharmacist (d. 1860)", "html": "1792 - <a href=\"https://wikipedia.org/wiki/Christian_<PERSON>\" title=\"Christian <PERSON>\"><PERSON></a>, German chemist and pharmacist (d. 1860)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Christian_<PERSON>\" title=\"Christian G<PERSON>\"><PERSON></a>, German chemist and pharmacist (d. 1860)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Christian_G<PERSON>in"}]}, {"year": "1798", "text": "<PERSON>, emperor of Brazil (d. 1834)", "html": "1798 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Brazil\" title=\"<PERSON> of Brazil\"><PERSON></a>, emperor of Brazil (d. 1834)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Brazil\" title=\"<PERSON> of Brazil\"><PERSON></a>, emperor of Brazil (d. 1834)", "links": [{"title": "<PERSON> of Brazil", "link": "https://wikipedia.org/wiki/Pedro_I_of_Brazil"}]}, {"year": "1801", "text": "<PERSON>, Swiss lawyer and politician, 5th President of the Swiss Confederation (d. 1873)", "html": "1801 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>-<PERSON>%C3%A9\" title=\"<PERSON>\"><PERSON></a>, Swiss lawyer and politician, 5th <a href=\"https://wikipedia.org/wiki/President_of_the_Swiss_Confederation\" title=\"President of the Swiss Confederation\">President of the Swiss Confederation</a> (d. 1873)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>-<PERSON>%C3%A9\" title=\"<PERSON>\"><PERSON></a>, Swiss lawyer and politician, 5th <a href=\"https://wikipedia.org/wiki/President_of_the_Swiss_Confederation\" title=\"President of the Swiss Confederation\">President of the Swiss Confederation</a> (d. 1873)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>-Hero<PERSON>%C3%A9"}, {"title": "President of the Swiss Confederation", "link": "https://wikipedia.org/wiki/President_of_the_Swiss_Confederation"}]}, {"year": "1815", "text": "<PERSON>, American general (d. 1873)", "html": "1815 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American general (d. 1873)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American general (d. 1873)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1838", "text": "<PERSON>, Australian politician, 6th Premier of Queensland (d. 1905)", "html": "1838 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian politician, 6th <a href=\"https://wikipedia.org/wiki/Premier_of_Queensland\" title=\"Premier of Queensland\">Premier of Queensland</a> (d. 1905)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian politician, 6th <a href=\"https://wikipedia.org/wiki/Premier_of_Queensland\" title=\"Premier of Queensland\">Premier of Queensland</a> (d. 1905)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Premier of Queensland", "link": "https://wikipedia.org/wiki/Premier_of_Queensland"}]}, {"year": "1840", "text": "<PERSON>, Polish-American actress (d. 1909)", "html": "1840 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Polish-American actress (d. 1909)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Polish-American actress (d. 1909)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Helena_<PERSON>d<PERSON>"}]}, {"year": "1855", "text": "<PERSON>, Hungarian conductor and academic (d. 1922)", "html": "1855 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Hungarian conductor and academic (d. 1922)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Hungarian conductor and academic (d. 1922)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1860", "text": "<PERSON>, American engineer and businessman, co-invented the gyrocompass (d. 1930)", "html": "1860 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American engineer and businessman, co-invented the <a href=\"https://wikipedia.org/wiki/Gyrocompass\" title=\"Gyrocompass\">gyrocompass</a> (d. 1930)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American engineer and businessman, co-invented the <a href=\"https://wikipedia.org/wiki/Gyrocompass\" title=\"Gyrocompass\">gyrocompass</a> (d. 1930)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "Gyrocompass", "link": "https://wikipedia.org/wiki/Gyrocompass"}]}, {"year": "1864", "text": "<PERSON><PERSON>, British India's first female graduate, Bengali poet, social activist, and feminist writer (d. 1933)", "html": "1864 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, British India's first female graduate, Bengali poet, social activist, and feminist writer (d. 1933)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, British India's first female graduate, Bengali poet, social activist, and feminist writer (d. 1933)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1865", "text": "<PERSON>, English biochemist and academic, Nobel Prize laureate (d. 1940)", "html": "1865 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English biochemist and academic, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Chemistry\" title=\"Nobel Prize in Chemistry\">Nobel Prize</a> laureate (d. 1940)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English biochemist and academic, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Chemistry\" title=\"Nobel Prize in Chemistry\">Nobel Prize</a> laureate (d. 1940)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Nobel Prize in Chemistry", "link": "https://wikipedia.org/wiki/Nobel_Prize_in_Chemistry"}]}, {"year": "1866", "text": "<PERSON>, Scottish journalist and politician, Prime Minister of the United Kingdom (d. 1937)", "html": "1866 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish journalist and politician, <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_the_United_Kingdom\" title=\"Prime Minister of the United Kingdom\">Prime Minister of the United Kingdom</a> (d. 1937)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish journalist and politician, <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_the_United_Kingdom\" title=\"Prime Minister of the United Kingdom\">Prime Minister of the United Kingdom</a> (d. 1937)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Prime Minister of the United Kingdom", "link": "https://wikipedia.org/wiki/Prime_Minister_of_the_United_Kingdom"}]}, {"year": "1868", "text": "<PERSON>, German engineer and businessman, founded Audi (d. 1951)", "html": "1868 - <a href=\"https://wikipedia.org/wiki/August_<PERSON>rch\" title=\"August <PERSON>rch\">August <PERSON></a>, German engineer and businessman, founded <a href=\"https://wikipedia.org/wiki/Audi\" title=\"Audi\">Audi</a> (d. 1951)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/August_<PERSON><PERSON>\" title=\"August <PERSON>rch\"><PERSON></a>, German engineer and businessman, founded <a href=\"https://wikipedia.org/wiki/Audi\" title=\"Audi\">Audi</a> (d. 1951)", "links": [{"title": "August <PERSON><PERSON>", "link": "https://wikipedia.org/wiki/August_Horch"}, {"title": "Audi", "link": "https://wikipedia.org/wiki/Audi"}]}, {"year": "1868", "text": "<PERSON>, Filipino general and politician, 1st Vice President of the Philippines (d. 1914)", "html": "1868 - <a href=\"https://wikipedia.org/wiki/<PERSON>_Tr%C3%ADas\" title=\"<PERSON>\"><PERSON></a>, Filipino general and politician, 1st <a href=\"https://wikipedia.org/wiki/Vice_President_of_the_Philippines\" title=\"Vice President of the Philippines\">Vice President of the Philippines</a> (d. 1914)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_Tr%C3%ADas\" title=\"<PERSON>\"><PERSON></a>, Filipino general and politician, 1st <a href=\"https://wikipedia.org/wiki/Vice_President_of_the_Philippines\" title=\"Vice President of the Philippines\">Vice President of the Philippines</a> (d. 1914)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Mariano_Tr%C3%ADas"}, {"title": "Vice President of the Philippines", "link": "https://wikipedia.org/wiki/Vice_President_of_the_Philippines"}]}, {"year": "1872", "text": "<PERSON>, English composer and educator (d. 1958)", "html": "1872 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English composer and educator (d. 1958)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English composer and educator (d. 1958)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1874", "text": "<PERSON>, American baseball player and manager (d. 1942)", "html": "1874 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(baseball)\" title=\"<PERSON> (baseball)\"><PERSON></a>, American baseball player and manager (d. 1942)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(baseball)\" title=\"<PERSON> (baseball)\"><PERSON></a>, American baseball player and manager (d. 1942)", "links": [{"title": "<PERSON> (baseball)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(baseball)"}]}, {"year": "1875", "text": "<PERSON><PERSON><PERSON>, English magician and author (d. 1947)", "html": "1875 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, English magician and author (d. 1947)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, English magician and author (d. 1947)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1878", "text": "<PERSON><PERSON><PERSON><PERSON>, American football player and hammer thrower (d. 1956)", "html": "1878 - <a href=\"https://wikipedia.org/wiki/Trux<PERSON>_Hare\" title=\"Truxtun Hare\"><PERSON><PERSON><PERSON><PERSON> <PERSON></a>, American football player and hammer thrower (d. 1956)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Truxtun_Hare\" title=\"Truxtun Hare\"><PERSON><PERSON><PERSON><PERSON> <PERSON></a>, American football player and hammer thrower (d. 1956)", "links": [{"title": "Truxtun Hare", "link": "https://wikipedia.org/wiki/Truxtun_Hare"}]}, {"year": "1880", "text": "<PERSON>, French-Canadian author (d. 1913)", "html": "1880 - <a href=\"https://wikipedia.org/wiki/Louis_H%C3%A9mon\" title=\"<PERSON>\"><PERSON></a>, French-Canadian author (d. 1913)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Louis_H%C3%A9mon\" title=\"<PERSON>\"><PERSON></a>, French-Canadian author (d. 1913)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Louis_H%C3%A9mon"}]}, {"year": "1880", "text": "<PERSON><PERSON><PERSON>, Finnish Speaker of the Parliament, the Prime Minister of the FSWR and the Supreme Commander of the Red Guards (d. 1939)", "html": "1880 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_Mann<PERSON>\" title=\"Kuller<PERSON> Manner\"><PERSON><PERSON><PERSON> Mann<PERSON></a>, Finnish <a href=\"https://wikipedia.org/wiki/Speaker_of_the_Parliament_of_Finland\" title=\"Speaker of the Parliament of Finland\">Speaker of the Parliament</a>, the <a href=\"https://wikipedia.org/wiki/Finnish_Socialist_Workers%27_Republic\" title=\"Finnish Socialist Workers' Republic\">Prime Minister of the FSWR</a> and the Supreme Commander of the <a href=\"https://wikipedia.org/wiki/Red_Guard_(Finland)\" class=\"mw-redirect\" title=\"Red Guard (Finland)\">Red Guards</a> (d. 1939)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_Manner\" title=\"Kuller<PERSON> Manner\"><PERSON><PERSON><PERSON> Mann<PERSON></a>, Finnish <a href=\"https://wikipedia.org/wiki/Speaker_of_the_Parliament_of_Finland\" title=\"Speaker of the Parliament of Finland\">Speaker of the Parliament</a>, the <a href=\"https://wikipedia.org/wiki/Finnish_Socialist_Workers%27_Republic\" title=\"Finnish Socialist Workers' Republic\">Prime Minister of the FSWR</a> and the Supreme Commander of the <a href=\"https://wikipedia.org/wiki/Red_Guard_(Finland)\" class=\"mw-redirect\" title=\"Red Guard (Finland)\">Red Guards</a> (d. 1939)", "links": [{"title": "<PERSON><PERSON><PERSON> Manner", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>er"}, {"title": "Speaker of the Parliament of Finland", "link": "https://wikipedia.org/wiki/Speaker_of_the_Parliament_of_Finland"}, {"title": "Finnish Socialist Workers' Republic", "link": "https://wikipedia.org/wiki/Finnish_Socialist_Workers%27_Republic"}, {"title": "Red Guard (Finland)", "link": "https://wikipedia.org/wiki/Red_Guard_(Finland)"}]}, {"year": "1889", "text": "<PERSON>, German Catholic philosopher and author (d. 1977)", "html": "1889 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German Catholic philosopher and author (d. 1977)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German Catholic philosopher and author (d. 1977)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1891", "text": "<PERSON>, Polish nun and martyr; later canonized (d. 1942)", "html": "1891 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Polish nun and martyr; later canonized (d. 1942)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Polish nun and martyr; later canonized (d. 1942)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1891", "text": "<PERSON><PERSON><PERSON><PERSON>, Japanese soldier and politician, 39th Prime Minister of Japan (d. 1945)", "html": "1891 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Japanese soldier and politician, 39th <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Japan\" title=\"Prime Minister of Japan\">Prime Minister of Japan</a> (d. 1945)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Japanese soldier and politician, 39th <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Japan\" title=\"Prime Minister of Japan\">Prime Minister of Japan</a> (d. 1945)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>e"}, {"title": "Prime Minister of Japan", "link": "https://wikipedia.org/wiki/Prime_Minister_of_Japan"}]}, {"year": "1892", "text": "<PERSON><PERSON>, Italian soprano and actress (d. 1975)", "html": "1892 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>lla_<PERSON>izza\" title=\"<PERSON><PERSON> dalla Rizza\"><PERSON><PERSON></a>, Italian soprano and actress (d. 1975)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_dalla_<PERSON>za\" title=\"<PERSON><PERSON> dalla Rizza\"><PERSON><PERSON></a>, Italian soprano and actress (d. 1975)", "links": [{"title": "<PERSON><PERSON> Riz<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "1893", "text": "<PERSON><PERSON><PERSON><PERSON>, American spy (d. 1980)", "html": "1893 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, American spy (d. 1980)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, American spy (d. 1980)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1894", "text": "<PERSON> of Romania, queen consort of Greece (d. 1956)", "html": "1894 - <a href=\"https://wikipedia.org/wiki/<PERSON>_of_Romania\" title=\"Elisabeth of Romania\"><PERSON> of Romania</a>, queen consort of Greece (d. 1956)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Elisabeth_of_Romania\" title=\"<PERSON> of Romania\"><PERSON> of Romania</a>, queen consort of Greece (d. 1956)", "links": [{"title": "Elisabeth of Romania", "link": "https://wikipedia.org/wiki/Elisabeth_of_Romania"}]}, {"year": "1896", "text": "<PERSON><PERSON><PERSON>, Italian poet and translator, Nobel Prize laureate (d. 1981)", "html": "1896 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Italian poet and translator, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Literature\" title=\"Nobel Prize in Literature\">Nobel Prize</a> laureate (d. 1981)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Italian poet and translator, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Literature\" title=\"Nobel Prize in Literature\">Nobel Prize</a> laureate (d. 1981)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}, {"title": "Nobel Prize in Literature", "link": "https://wikipedia.org/wiki/Nobel_Prize_in_Literature"}]}, {"year": "1903", "text": "<PERSON>, American actress (d. 1998)", "html": "1903 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress (d. 1998)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress (d. 1998)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1904", "text": "<PERSON>, American journalist and author (d. 1959)", "html": "1904 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American journalist and author (d. 1959)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American journalist and author (d. 1959)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1904", "text": "<PERSON>, American orthopedic surgeon and professor (d. 2005)", "html": "1904 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American orthopedic surgeon and professor (d. 2005)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American orthopedic surgeon and professor (d. 2005)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON><PERSON>"}]}, {"year": "1904", "text": "<PERSON><PERSON>, Chinese author and educator (d. 1986)", "html": "1904 - <a href=\"https://wikipedia.org/wiki/<PERSON>g_<PERSON>\" title=\"<PERSON>g <PERSON>\"><PERSON><PERSON></a>, Chinese author and educator (d. 1986)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>g_<PERSON>\" title=\"<PERSON>g <PERSON>\"><PERSON><PERSON> <PERSON></a>, Chinese author and educator (d. 1986)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Ding_<PERSON>"}]}, {"year": "1906", "text": "<PERSON>, American baseball player and manager (d. 1984)", "html": "1906 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player and manager (d. 1984)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player and manager (d. 1984)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1906", "text": "<PERSON>, American playwright and producer (d. 1984)", "html": "1906 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(playwright)\" title=\"<PERSON> (playwright)\"><PERSON></a>, American playwright and producer (d. 1984)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>(playwright)\" title=\"<PERSON> (playwright)\"><PERSON></a>, American playwright and producer (d. 1984)", "links": [{"title": "<PERSON> (playwright)", "link": "https://wikipedia.org/wiki/<PERSON>(playwright)"}]}, {"year": "1906", "text": "<PERSON><PERSON>, Italian race car driver and motorcycle racer (d. 1988)", "html": "1906 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Italian race car driver and motorcycle racer (d. 1988)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Italian race car driver and motorcycle racer (d. 1988)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1908", "text": "<PERSON>, American novelist, poet, playwright, and critic (d. 1991)", "html": "1908 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American novelist, poet, playwright, and critic (d. 1991)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American novelist, poet, playwright, and critic (d. 1991)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1908", "text": "<PERSON>, American novelist (d. 1997)", "html": "1908 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American novelist (d. 1997)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American novelist (d. 1997)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1909", "text": "<PERSON>, Canadian poet (d. 1996)", "html": "1909 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian poet (d. 1996)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian poet (d. 1996)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1910", "text": "<PERSON>, American poet, critic, and translator (d. 1985)", "html": "1910 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American poet, critic, and translator (d. 1985)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American poet, critic, and translator (d. 1985)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1910", "text": "<PERSON>, American chemist and academic (d. 2013)", "html": "1910 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American chemist and academic (d. 2013)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American chemist and academic (d. 2013)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1911", "text": "<PERSON>, Indian cricketer (d. 1987)", "html": "1911 - <a href=\"https://wikipedia.org/wiki/Vijay_Merchant\" title=\"Vijay <PERSON>\"><PERSON></a>, Indian cricketer (d. 1987)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Vijay_Merchant\" title=\"Vijay <PERSON>\"><PERSON></a>, Indian cricketer (d. 1987)", "links": [{"title": "Vijay Merchant", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1912", "text": "<PERSON>, Bangladeshi academic and former Minister of Foreign Affairs (d. 2006)", "html": "1912 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Bangladeshi academic and former Minister of Foreign Affairs (d. <a href=\"https://wikipedia.org/wiki/2006\" title=\"2006\">2006</a>)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Bangladeshi academic and former Minister of Foreign Affairs (d. <a href=\"https://wikipedia.org/wiki/2006\" title=\"2006\">2006</a>)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON><PERSON>_<PERSON>"}, {"title": "2006", "link": "https://wikipedia.org/wiki/2006"}]}, {"year": "1913", "text": "<PERSON>, English author and educator (d. 2004)", "html": "1913 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English author and educator (d. 2004)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English author and educator (d. 2004)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1914", "text": "<PERSON>, African-American chemist (d. 1996)", "html": "1914 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, African-American chemist (d. 1996)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, African-American chemist (d. 1996)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "1916", "text": "<PERSON>, American actress and playwright (d. 1994)", "html": "1916 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress and playwright (d. 1994)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress and playwright (d. 1994)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1916", "text": "<PERSON>, American actor (d. 1959)", "html": "1916 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Martin\"><PERSON></a>, American actor (d. 1959)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Martin\"><PERSON></a>, American actor (d. 1959)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1917", "text": "<PERSON><PERSON><PERSON>, Uruguayan footballer and manager (d. 2004)", "html": "1917 - <a href=\"https://wikipedia.org/wiki/Roque_M%C3%A1spoli\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Uruguayan footballer and manager (d. 2004)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Roque_M%C3%A1spoli\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Uruguayan footballer and manager (d. 2004)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Roque_M%C3%A1spoli"}]}, {"year": "1917", "text": "<PERSON>, Australian Poet (d. 1976)", "html": "1917 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, Australian Poet (d. 1976)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, Australian Poet (d. 1976)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1919", "text": "<PERSON>, Canadian politician, 34th Mayor of Trois-Rivières (d. 2007)", "html": "1919 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian politician, 34th <a href=\"https://wikipedia.org/wiki/Mayor_of_Trois-Rivi%C3%A8res\" class=\"mw-redirect\" title=\"Mayor of Trois-Rivières\">Mayor of Trois-Rivières</a> (d. 2007)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian politician, 34th <a href=\"https://wikipedia.org/wiki/Mayor_of_Trois-Rivi%C3%A8res\" class=\"mw-redirect\" title=\"Mayor of Trois-Rivières\">Mayor of Trois-Rivières</a> (d. 2007)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}, {"title": "Mayor of Trois-Rivières", "link": "https://wikipedia.org/wiki/Mayor_of_Trois-Rivi%C3%A8res"}]}, {"year": "1919", "text": "<PERSON>, American cook and soldier (d. 1943)", "html": "1919 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American cook and soldier (d. 1943)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American cook and soldier (d. 1943)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1920", "text": "<PERSON>, English politician and diplomat, Governor of Southern Rhodesia (d. 1987)", "html": "1920 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English politician and diplomat, <a href=\"https://wikipedia.org/wiki/Governor_of_Southern_Rhodesia\" title=\"Governor of Southern Rhodesia\">Governor of Southern Rhodesia</a> (d. 1987)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English politician and diplomat, <a href=\"https://wikipedia.org/wiki/Governor_of_Southern_Rhodesia\" title=\"Governor of Southern Rhodesia\">Governor of Southern Rhodesia</a> (d. 1987)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Governor of Southern Rhodesia", "link": "https://wikipedia.org/wiki/Governor_of_Southern_Rhodesia"}]}, {"year": "1921", "text": "<PERSON>, American animator, producer, screenwriter, and voice actor, created <PERSON><PERSON><PERSON> (d. 2010)", "html": "1921 - <a href=\"https://wikipedia.org/wiki/Art_Clokey\" title=\"Art Clokey\"><PERSON></a>, American animator, producer, screenwriter, and voice actor, created <i><a href=\"https://wikipedia.org/wiki/Gumby\" title=\"Gumby\">Gumby</a></i> (d. 2010)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Art_C<PERSON>key\" title=\"Art Clokey\"><PERSON></a>, American animator, producer, screenwriter, and voice actor, created <i><a href=\"https://wikipedia.org/wiki/Gumby\" title=\"Gumby\">Gumby</a></i> (d. 2010)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Art_Clokey"}, {"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Gumby"}]}, {"year": "1921", "text": "<PERSON><PERSON><PERSON>, Czech-English tennis player and ice hockey player (d. 2001)", "html": "1921 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>%C3%BD\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Czech-English tennis player and ice hockey player (d. 2001)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>%C3%BD\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Czech-English tennis player and ice hockey player (d. 2001)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>n%C3%BD"}]}, {"year": "1921", "text": "<PERSON><PERSON>, Scottish rugby player and journalist (d. 2020)", "html": "1921 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"Logie <PERSON>\"><PERSON><PERSON></a>, Scottish rugby player and journalist (d. 2020)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"Logie <PERSON>\"><PERSON><PERSON></a>, Scottish rugby player and journalist (d. 2020)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1922", "text": "<PERSON>, American soldier and diplomat, United States Ambassador to the Philippines (d. 2013)", "html": "1922 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American soldier and diplomat, <a href=\"https://wikipedia.org/wiki/United_States_Ambassador_to_the_Philippines\" class=\"mw-redirect\" title=\"United States Ambassador to the Philippines\">United States Ambassador to the Philippines</a> (d. 2013)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American soldier and diplomat, <a href=\"https://wikipedia.org/wiki/United_States_Ambassador_to_the_Philippines\" class=\"mw-redirect\" title=\"United States Ambassador to the Philippines\">United States Ambassador to the Philippines</a> (d. 2013)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "United States Ambassador to the Philippines", "link": "https://wikipedia.org/wiki/United_States_Ambassador_to_the_Philippines"}]}, {"year": "1923", "text": "<PERSON>, American businesswoman, co-founded Weight Watchers (d. 2015)", "html": "1923 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American businesswoman, co-founded <a href=\"https://wikipedia.org/wiki/WW_International\" title=\"WW International\">Weight Watchers</a> (d. 2015)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American businesswoman, co-founded <a href=\"https://wikipedia.org/wiki/WW_International\" title=\"WW International\">Weight Watchers</a> (d. 2015)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "WW International", "link": "https://wikipedia.org/wiki/WW_International"}]}, {"year": "1923", "text": "<PERSON><PERSON>, American boxer, trainer, and manager (d. 2012)", "html": "1923 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American boxer, trainer, and manager (d. 2012)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American boxer, trainer, and manager (d. 2012)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1924", "text": "<PERSON><PERSON>, Greek politician (d. 2011)", "html": "1924 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Greek politician (d. 2011)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Greek politician (d. 2011)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Leonidas_<PERSON>kos"}]}, {"year": "1925", "text": "<PERSON>, Canadian psychiatrist and politician (d. 2008)", "html": "1925 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian psychiatrist and politician (d. 2008)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian psychiatrist and politician (d. 2008)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1926", "text": "<PERSON><PERSON><PERSON>, Czech gymnast (d. 1948)", "html": "1926 - <a href=\"https://wikipedia.org/wiki/Eli%C5%A1ka_Mis%C3%A1kov%C3%A1\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Czech gymnast (d. 1948)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Eli%C5%A1ka_Mis%C3%A1kov%C3%A1\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Czech gymnast (d. 1948)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Eli%C5%A1ka_Mis%C3%A1kov%C3%A1"}]}, {"year": "1928", "text": "<PERSON>, American painter and academic (d. 2005)", "html": "1928 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American painter and academic (d. 2005)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_Held\" title=\"<PERSON>\"><PERSON></a>, American painter and academic (d. 2005)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Al_Held"}]}, {"year": "1928", "text": "<PERSON><PERSON>, Greek singer and musicologist (d. 2012)", "html": "1928 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Greek singer and musicologist (d. 2012)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Greek singer and musicologist (d. 2012)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1929", "text": "<PERSON><PERSON>, American R&B singer-songwriter (d. 2008)", "html": "1929 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American R&amp;B singer-songwriter (d. 2008)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American R&amp;B singer-songwriter (d. 2008)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1929", "text": "<PERSON>, American psychologist, author, and academic", "html": "1929 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(psychiatrist)\" title=\"<PERSON> (psychiatrist)\"><PERSON></a>, American psychologist, author, and academic", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(psychiatrist)\" title=\"<PERSON> (psychiatrist)\"><PERSON></a>, American psychologist, author, and academic", "links": [{"title": "<PERSON> (psychiatrist)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(psychiatrist)"}]}, {"year": "1929", "text": "<PERSON>, Icelandic journalist and academic (d. 2007)", "html": "1929 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Icelandic journalist and academic (d. 2007)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Icelandic journalist and academic (d. 2007)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1930", "text": "<PERSON>, Canadian ice hockey player and photographer (d. 2013)", "html": "1930 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player and photographer (d. 2013)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player and photographer (d. 2013)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1930", "text": "<PERSON><PERSON>, Slovenian historian and author (d. 2021)", "html": "1930 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Slovenian historian and author (d. 2021)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Slovenian historian and author (d. 2021)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>z"}]}, {"year": "1931", "text": "<PERSON><PERSON><PERSON>, Norwegian computer scientist and academic, co-developed Simula (d. 2002)", "html": "1931 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Norwegian computer scientist and academic, co-developed <a href=\"https://wikipedia.org/wiki/Simula\" title=\"Simula\">Simula</a> (d. 2002)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Norwegian computer scientist and academic, co-developed <a href=\"https://wikipedia.org/wiki/Simula\" title=\"Simula\">Simu<PERSON></a> (d. 2002)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>"}, {"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Simula"}]}, {"year": "1932", "text": "<PERSON>, American comedian, actor, and author (d. 2017)", "html": "1932 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American comedian, actor, and author (d. 2017)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American comedian, actor, and author (d. 2017)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1932", "text": "<PERSON>, American race car driver and sportscaster", "html": "1932 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American race car driver and sportscaster", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American race car driver and sportscaster", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1933", "text": "<PERSON>, Canadian painter and art collector (d. 2004)", "html": "1933 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian painter and art collector (d. 2004)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian painter and art collector (d. 2004)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1934", "text": "<PERSON> \"<PERSON>\" <PERSON>, American singer-songwriter and pianist (d. 2012)", "html": "1934 - <a href=\"https://wikipedia.org/wiki/<PERSON>_%22S<PERSON>r_<PERSON>%22_<PERSON>\" title='<PERSON> \"<PERSON> Boy\" Crawford'><PERSON> \"<PERSON>\" <PERSON></a>, American singer-songwriter and pianist (d. 2012)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_%22S<PERSON>r_<PERSON>%22_<PERSON>\" title='<PERSON> \"<PERSON> Boy\" <PERSON>'><PERSON> \"<PERSON> Boy\" <PERSON></a>, American singer-songwriter and pianist (d. 2012)", "links": [{"title": "<PERSON> \"<PERSON> Boy\" <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_%22S<PERSON>r_Boy%22_<PERSON>"}]}, {"year": "1934", "text": "<PERSON>, American architect, designed the Getty Center and City Tower", "html": "1934 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American architect, designed the <a href=\"https://wikipedia.org/wiki/Getty_Center\" title=\"Getty Center\">Getty Center</a> and <a href=\"https://wikipedia.org/wiki/City_Tower_(Prague)\" title=\"City Tower (Prague)\">City Tower</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American architect, designed the <a href=\"https://wikipedia.org/wiki/Getty_Center\" title=\"Getty Center\">Getty Center</a> and <a href=\"https://wikipedia.org/wiki/City_Tower_(Prague)\" title=\"City Tower (Prague)\">City Tower</a>", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Getty Center", "link": "https://wikipedia.org/wiki/Getty_Center"}, {"title": "City Tower (Prague)", "link": "https://wikipedia.org/wiki/City_Tower_(Prague)"}]}, {"year": "1934", "text": "<PERSON>, Russian mathematician and academic", "html": "1934 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian mathematician and academic", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian mathematician and academic", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1934", "text": "<PERSON><PERSON><PERSON>, Turkish engineer and author (d. 1977)", "html": "1934 - <a href=\"https://wikipedia.org/wiki/O%C4%9Fuz_Atay\" title=\"<PERSON><PERSON><PERSON> Atay\"><PERSON><PERSON><PERSON></a>, Turkish engineer and author (d. 1977)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/O%C4%9Fuz_Atay\" title=\"<PERSON><PERSON><PERSON> Atay\"><PERSON><PERSON><PERSON></a>, Turkish engineer and author (d. 1977)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/O%C4%9Fuz_Atay"}]}, {"year": "1934", "text": "<PERSON>, Greek-born American photographer (d. 2025)", "html": "1934 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Greek-born American photographer (d. 2025)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Greek-born American photographer (d. 2025)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Constantine_Manos"}]}, {"year": "1935", "text": "<PERSON>, English footballer and manager (d. 2015)", "html": "1935 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English footballer and manager (d. 2015)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English footballer and manager (d. 2015)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1935", "text": "<PERSON>, American baseball player and sportscaster", "html": "1935 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player and sportscaster", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player and sportscaster", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1935", "text": "<PERSON>, American soul singer-songwriter (d. 2025)", "html": "1935 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American soul singer-songwriter (d. 2025)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American soul singer-songwriter (d. 2025)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1935", "text": "<PERSON><PERSON><PERSON>, Indian lawyer and politician, Indian Minister of Defence", "html": "1935 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Indian lawyer and politician, <a href=\"https://wikipedia.org/wiki/Ministry_of_Defence_(India)\" title=\"Ministry of Defence (India)\">Indian Minister of Defence</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Indian lawyer and politician, <a href=\"https://wikipedia.org/wiki/Ministry_of_Defence_(India)\" title=\"Ministry of Defence (India)\">Indian Minister of Defence</a>", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}, {"title": "Ministry of Defence (India)", "link": "https://wikipedia.org/wiki/Ministry_of_Defence_(India)"}]}, {"year": "1935", "text": "<PERSON>, Italian tenor and actor (d. 2007)", "html": "1935 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian tenor and actor (d. 2007)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian tenor and actor (d. 2007)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1937", "text": "<PERSON>, Australian race car driver (d. 1969)", "html": "1937 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(racing_driver)\" title=\"<PERSON> (racing driver)\"><PERSON></a>, Australian race car driver (d. 1969)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(racing_driver)\" title=\"<PERSON> (racing driver)\"><PERSON></a>, Australian race car driver (d. 1969)", "links": [{"title": "<PERSON> (racing driver)", "link": "https://wikipedia.org/wiki/<PERSON>_(racing_driver)"}]}, {"year": "1937", "text": "<PERSON>, American painter", "html": "1937 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American painter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American painter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1941", "text": "<PERSON>, English lawyer, academic, and republican", "html": "1941 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English lawyer, academic, and republican", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English lawyer, academic, and republican", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1942", "text": "<PERSON>, American soul bass singer (d. 1995)", "html": "1942 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American soul bass singer (d. 1995)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American soul bass singer (d. 1995)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1943", "text": "<PERSON><PERSON><PERSON>, Greek actor (d. 2015)", "html": "1943 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Greek actor (d. 2015)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Greek actor (d. 2015)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1944", "text": "<PERSON>, English journalist and author", "html": "1944 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English journalist and author", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English journalist and author", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1945", "text": "<PERSON><PERSON><PERSON>, French actress", "html": "1945 - <a href=\"https://wikipedia.org/wiki/Aurore_Cl%C3%A9ment\" title=\"Aurore Clé<PERSON>\"><PERSON><PERSON><PERSON></a>, French actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Aurore_Cl%C3%A9ment\" title=\"Aurore Clé<PERSON>\"><PERSON><PERSON><PERSON></a>, French actress", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Aurore_Cl%C3%A9ment"}]}, {"year": "1945", "text": "<PERSON>, American wrestler (d. 2015)", "html": "1945 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American wrestler (d. 2015)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American wrestler (d. 2015)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1946", "text": "<PERSON>, American politician", "html": "1946 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American politician", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1946", "text": "<PERSON><PERSON>, Indian cricketer (d. 2008)", "html": "1946 - <a href=\"https://wikipedia.org/wiki/Ashok_Mankad\" title=\"Ashok Mankad\"><PERSON><PERSON></a>, Indian cricketer (d. 2008)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Ashok_Mankad\" title=\"Ashok Mankad\"><PERSON><PERSON></a>, Indian cricketer (d. 2008)", "links": [{"title": "Ashok <PERSON>", "link": "https://wikipedia.org/wiki/Ashok_Mankad"}]}, {"year": "1946", "text": "<PERSON>, English bassist and composer", "html": "1946 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English bassist and composer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English bassist and composer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1947", "text": "<PERSON>, American journalist", "html": "1947 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American journalist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American journalist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1948", "text": "<PERSON>, American businessman and politician, 46th Governor of Michigan", "html": "1948 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American businessman and politician, 46th <a href=\"https://wikipedia.org/wiki/Governor_of_Michigan\" title=\"Governor of Michigan\">Governor of Michigan</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American businessman and politician, 46th <a href=\"https://wikipedia.org/wiki/Governor_of_Michigan\" title=\"Governor of Michigan\">Governor of Michigan</a>", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Governor of Michigan", "link": "https://wikipedia.org/wiki/Governor_of_Michigan"}]}, {"year": "1948", "text": "<PERSON>, English singer-songwriter and guitarist (d. 2016)", "html": "1948 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English singer-songwriter and guitarist (d. 2016)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English singer-songwriter and guitarist (d. 2016)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1949", "text": "<PERSON>, English cyclist and coach", "html": "1949 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(cyclist)\" title=\"<PERSON> (cyclist)\"><PERSON></a>, English cyclist and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>(cyclist)\" title=\"<PERSON> (cyclist)\"><PERSON></a>, English cyclist and coach", "links": [{"title": "<PERSON> (cyclist)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>(cyclist)"}]}, {"year": "1949", "text": "<PERSON> the <PERSON>, Venezuelan terrorist", "html": "1949 - <a href=\"https://wikipedia.org/wiki/<PERSON>_the_Jack<PERSON>\" title=\"<PERSON> the Jack<PERSON>\"><PERSON> the <PERSON></a>, Venezuelan terrorist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_the_Jack<PERSON>\" title=\"<PERSON> the Jack<PERSON>\"><PERSON> the <PERSON></a>, Venezuelan terrorist", "links": [{"title": "<PERSON> the Jackal", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1949", "text": "<PERSON>, American artist", "html": "1949 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Shaw\"><PERSON></a>, American artist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Shaw\"><PERSON></a>, American artist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1949", "text": "<PERSON>, English footballer and manager (d. 2017)", "html": "1949 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English footballer and manager (d. 2017)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English footballer and manager (d. 2017)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1950", "text": "<PERSON>, American actress and model", "html": "1950 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress and model", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress and model", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1950", "text": "<PERSON>, American economist and politician, 31st Governor of Wyoming", "html": "1950 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American economist and politician, 31st <a href=\"https://wikipedia.org/wiki/Governor_of_Wyoming\" class=\"mw-redirect\" title=\"Governor of Wyoming\">Governor of Wyoming</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American economist and politician, 31st <a href=\"https://wikipedia.org/wiki/Governor_of_Wyoming\" class=\"mw-redirect\" title=\"Governor of Wyoming\">Governor of Wyoming</a>", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}, {"title": "Governor of Wyoming", "link": "https://wikipedia.org/wiki/Governor_of_Wyoming"}]}, {"year": "1951", "text": "<PERSON>, South African-American golfer", "html": "1951 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, South African-American golfer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, South African-American golfer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1951", "text": "<PERSON>, American businessman and politician", "html": "1951 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(politician)\" title=\"<PERSON> (politician)\"><PERSON></a>, American businessman and politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(politician)\" title=\"<PERSON> (politician)\"><PERSON></a>, American businessman and politician", "links": [{"title": "<PERSON> (politician)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(politician)"}]}, {"year": "1951", "text": "<PERSON><PERSON>, Japanese golfer", "html": "1951 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_(golfer)\" title=\"<PERSON><PERSON> (golfer)\"><PERSON><PERSON></a>, Japanese golfer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_(golfer)\" title=\"<PERSON><PERSON> (golfer)\"><PERSON><PERSON></a>, Japanese golfer", "links": [{"title": "<PERSON><PERSON> (golfer)", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_(golfer)"}]}, {"year": "1952", "text": "<PERSON>, Australian cricketer and coach", "html": "1952 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian cricketer and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian cricketer and coach", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1952", "text": "<PERSON><PERSON><PERSON>, Hungarian educator and politician (d. 2012)", "html": "1952 - <a href=\"https://wikipedia.org/wiki/B%C3%A9la_Cs%C3%A9csei\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Hungarian educator and politician (d. 2012)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/B%C3%A9la_Cs%C3%A9csei\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Hungarian educator and politician (d. 2012)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/B%C3%A9la_Cs%C3%A9csei"}]}, {"year": "1952", "text": "<PERSON>, English mathematician and theorist", "html": "1952 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English mathematician and theorist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English mathematician and theorist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1953", "text": "<PERSON>, English comedian and actor", "html": "1953 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English comedian and actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English comedian and actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1953", "text": "<PERSON>, English actor and director", "html": "1953 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actor and director", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actor and director", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1954", "text": "<PERSON><PERSON>, Anguillian politician and member of the House of Assembly of Anguilla", "html": "1954 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Anguillian politician and member of the <a href=\"https://wikipedia.org/wiki/House_of_Assembly_of_Anguilla\" class=\"mw-redirect\" title=\"House of Assembly of Anguilla\">House of Assembly of Anguilla</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Anguillian politician and member of the <a href=\"https://wikipedia.org/wiki/House_of_Assembly_of_Anguilla\" class=\"mw-redirect\" title=\"House of Assembly of Anguilla\">House of Assembly of Anguilla</a>", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>"}, {"title": "House of Assembly of Anguilla", "link": "https://wikipedia.org/wiki/House_of_Assembly_of_Anguilla"}]}, {"year": "1954", "text": "<PERSON><PERSON>, Italian actor", "html": "1954 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Italian actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Italian actor", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Massimo_<PERSON>hini"}]}, {"year": "1954", "text": "<PERSON>, American singer, songwriter, and record producer", "html": "1954 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer, songwriter, and record producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer, songwriter, and record producer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1954", "text": "<PERSON><PERSON>, Jamaican singer and producer", "html": "1954 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Jamaican singer and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Jamaican singer and producer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1955", "text": "<PERSON><PERSON>, Norwegian footballer", "html": "1955 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Norwegian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Norwegian footballer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1955", "text": "<PERSON>, American singer-songwriter and guitarist (d. 2017)", "html": "1955 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and guitarist (d. 2017)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and guitarist (d. 2017)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Pat_DiNizio"}]}, {"year": "1955", "text": "<PERSON><PERSON>, Croatian general", "html": "1955 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Croatian general", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> Gotov<PERSON>\"><PERSON><PERSON></a>, Croatian general", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1955", "text": "<PERSON>, Canadian singer-songwriter and producer", "html": "1955 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian singer-songwriter and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian singer-songwriter and producer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1956", "text": "<PERSON>, Spanish author", "html": "1956 - <a href=\"https://wikipedia.org/wiki/<PERSON>_%C3%81balos\" title=\"<PERSON>\"><PERSON></a>, Spanish author", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_%C3%81balos\" title=\"<PERSON>\"><PERSON></a>, Spanish author", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_%C3%81balos"}]}, {"year": "1956", "text": "<PERSON>, Scottish footballer", "html": "1956 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(footballer)\" title=\"<PERSON> (footballer)\"><PERSON></a>, Scottish footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>(footballer)\" title=\"<PERSON> (footballer)\"><PERSON></a>, Scottish footballer", "links": [{"title": "<PERSON> (footballer)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(footballer)"}]}, {"year": "1956", "text": "<PERSON><PERSON>, German cyclist", "html": "1956 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>n\" title=\"<PERSON><PERSON> Ha<PERSON>n\"><PERSON><PERSON></a>, German cyclist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>n\" title=\"<PERSON><PERSON> Ha<PERSON>n\"><PERSON><PERSON></a>, German cyclist", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>n"}]}, {"year": "1956", "text": "<PERSON>, Australian judge", "html": "1956 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian judge", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian judge", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1956", "text": "<PERSON><PERSON><PERSON>, German figure skater", "html": "1956 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, German figure skater", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, German figure skater", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>l"}]}, {"year": "1956", "text": "<PERSON>, English singer-songwriter", "html": "1956 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English singer-songwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English singer-songwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1957", "text": "<PERSON><PERSON><PERSON><PERSON>, French actress, singer, and director", "html": "1957 - <a href=\"https://wikipedia.org/wiki/Cl%C3%A9mentine_C%C3%A9lari%C3%A9\" title=\"Clémentine Célarié\">Clément<PERSON></a>, French actress, singer, and director", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Cl%C3%A9mentine_C%C3%A9lari%C3%A9\" title=\"Clémentine Célarié\">Clément<PERSON> Célari<PERSON></a>, French actress, singer, and director", "links": [{"title": "Clémentine Célarié", "link": "https://wikipedia.org/wiki/Cl%C3%A9mentine_C%C3%A9lari%C3%A9"}]}, {"year": "1957", "text": "<PERSON>, French comic book artist and illustrator", "html": "1957 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French comic book artist and illustrator", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French comic book artist and illustrator", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1957", "text": "<PERSON>, Welsh football goalkeeper", "html": "1957 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Welsh football goalkeeper", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Welsh football goalkeeper", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1957", "text": "<PERSON><PERSON>, Belgian journalist and music promoter (d. 2014)", "html": "1957 - <a href=\"https://wikipedia.org/wiki/Annik_Honor%C3%A9\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Belgian journalist and music promoter (d. 2014)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Annik_Honor%C3%A9\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Belgian journalist and music promoter (d. 2014)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Annik_Honor%C3%A9"}]}, {"year": "1957", "text": "<PERSON>, Australian biologist", "html": "1957 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian biologist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian biologist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "1958", "text": "<PERSON>, American lawyer and politician", "html": "1958 - <a href=\"https://wikipedia.org/wiki/Steve_<PERSON>\" title=\"Steve <PERSON>\"><PERSON></a>, American lawyer and politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Steve_Austria\" title=\"Steve Austria\"><PERSON></a>, American lawyer and politician", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Steve_<PERSON>"}]}, {"year": "1958", "text": "<PERSON><PERSON>, Portuguese historian, author, and academic (d. 2013)", "html": "1958 - <a href=\"https://wikipedia.org/wiki/<PERSON>_de_<PERSON>%C3%<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>_Sequeira_Dias\" title=\"<PERSON> Fátima Silva de Sequeira Dias\"><PERSON> Seque<PERSON></a>, Portuguese historian, author, and academic (d. 2013)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_de_<PERSON>%C3%<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>_Se<PERSON>ira_Dias\" title=\"<PERSON> de Fátima Silva de Sequeira Dias\"><PERSON> F<PERSON></a>, Portuguese historian, author, and academic (d. 2013)", "links": [{"title": "Maria de Fátima <PERSON> de Sequeira Dias", "link": "https://wikipedia.org/wiki/Maria_de_F%C3%<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1958", "text": "<PERSON>, American rock singer-songwriter", "html": "1958 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, American rock singer-songwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, American rock singer-songwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1958", "text": "<PERSON>, Welsh bass player (d. 2015)", "html": "1958 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Welsh bass player (d. 2015)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Welsh bass player (d. 2015)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1959", "text": "<PERSON>, American lawyer and politician, 42nd Treasurer of the United States", "html": "1959 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and politician, 42nd <a href=\"https://wikipedia.org/wiki/Treasurer_of_the_United_States\" title=\"Treasurer of the United States\">Treasurer of the United States</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and politician, 42nd <a href=\"https://wikipedia.org/wiki/Treasurer_of_the_United_States\" title=\"Treasurer of the United States\">Treasurer of the United States</a>", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Anna_Escobedo_Cabral"}, {"title": "Treasurer of the United States", "link": "https://wikipedia.org/wiki/Treasurer_of_the_United_States"}]}, {"year": "1960", "text": "<PERSON>, American golfer", "html": "1960 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American golfer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American golfer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1960", "text": "<PERSON>, Italian footballer and manager", "html": "1960 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(footballer,_born_October_1960)\" title=\"<PERSON> (footballer, born October 1960)\"><PERSON></a>, Italian footballer and manager", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(footballer,_born_October_1960)\" title=\"<PERSON> (footballer, born October 1960)\"><PERSON></a>, Italian footballer and manager", "links": [{"title": "<PERSON> (footballer, born October 1960)", "link": "https://wikipedia.org/wiki/<PERSON>_(footballer,_born_October_1960)"}]}, {"year": "1960", "text": "<PERSON><PERSON><PERSON>, Japanese actor and martial artist", "html": "1960 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"Hiroy<PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese actor and martial artist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"Hiroy<PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese actor and martial artist", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1960", "text": "<PERSON><PERSON><PERSON>, German Paralympic cyclist", "html": "1960 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_Vieth\" title=\"<PERSON><PERSON><PERSON> Vieth\"><PERSON><PERSON><PERSON></a>, German Paralympic cyclist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_Vieth\" title=\"<PERSON><PERSON><PERSON> Vieth\"><PERSON><PERSON><PERSON></a>, German Paralympic cyclist", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_Vieth"}]}, {"year": "1961", "text": "<PERSON><PERSON>, Spanish footballer", "html": "1961 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Spanish footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>do\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Spanish footballer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>do"}]}, {"year": "1962", "text": "<PERSON>, American actor and director", "html": "1962 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor and director", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor and director", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1962", "text": "<PERSON>, South African actress (d. 2024)", "html": "1962 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, South African actress (d. 2024)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, South African actress (d. 2024)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1962", "text": "<PERSON>, American trumpet player and composer", "html": "1962 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American trumpet player and composer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American trumpet player and composer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1962", "text": "<PERSON>, English footballer and manager", "html": "1962 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(footballer,_born_1962)\" title=\"<PERSON> (footballer, born 1962)\"><PERSON></a>, English footballer and manager", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(footballer,_born_1962)\" title=\"<PERSON> (footballer, born 1962)\"><PERSON></a>, English footballer and manager", "links": [{"title": "<PERSON> (footballer, born 1962)", "link": "https://wikipedia.org/wiki/<PERSON>(footballer,_born_1962)"}]}, {"year": "1962", "text": "<PERSON><PERSON><PERSON>, Macedonian engineer and politician, 3rd President of the Republic of Macedonia", "html": "1962 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Macedonian engineer and politician, 3rd <a href=\"https://wikipedia.org/wiki/President_of_the_Republic_of_Macedonia\" class=\"mw-redirect\" title=\"President of the Republic of Macedonia\">President of the Republic of Macedonia</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Macedonian engineer and politician, 3rd <a href=\"https://wikipedia.org/wiki/President_of_the_Republic_of_Macedonia\" class=\"mw-redirect\" title=\"President of the Republic of Macedonia\">President of the Republic of Macedonia</a>", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}, {"title": "President of the Republic of Macedonia", "link": "https://wikipedia.org/wiki/President_of_the_Republic_of_Macedonia"}]}, {"year": "1962", "text": "<PERSON>, American actress and photographer", "html": "1962 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress and photographer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress and photographer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1962", "text": "<PERSON><PERSON>, Norwegian guitarist and composer", "html": "1962 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_(musician)\" title=\"<PERSON><PERSON> (musician)\"><PERSON><PERSON></a>, Norwegian guitarist and composer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_(musician)\" title=\"<PERSON><PERSON> (musician)\"><PERSON><PERSON></a>, Norwegian guitarist and composer", "links": [{"title": "<PERSON><PERSON> (musician)", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_(musician)"}]}, {"year": "1963", "text": "<PERSON><PERSON>, German footballer", "html": "1963 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, German footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, German footballer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1963", "text": "<PERSON><PERSON><PERSON>, Japanese composer", "html": "1963 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese composer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese composer", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON><PERSON>"}]}, {"year": "1963", "text": "<PERSON><PERSON>, Japanese animator and screenwriter (d. 2010)", "html": "1963 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Japanese animator and screenwriter (d. 2010)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Japanese animator and screenwriter (d. 2010)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Sato<PERSON>_Kon"}]}, {"year": "1963", "text": "<PERSON>, English actor and mixed martial artist (d. 2014)", "html": "1963 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actor and mixed martial artist (d. 2014)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actor and mixed martial artist (d. 2014)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1963", "text": "<PERSON>, Irish footballer and manager (d. 2012)", "html": "1963 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(Northern_Ireland_footballer)\" title=\"<PERSON> (Northern Ireland footballer)\"><PERSON></a>, Irish footballer and manager (d. 2012)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(Northern_Ireland_footballer)\" title=\"<PERSON> (Northern Ireland footballer)\"><PERSON></a>, Irish footballer and manager (d. 2012)", "links": [{"title": "<PERSON> (Northern Ireland footballer)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(Northern_Ireland_footballer)"}]}, {"year": "1963", "text": "<PERSON>, Dominican baseball player", "html": "1963 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dominican baseball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dominican baseball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Luis_<PERSON>"}]}, {"year": "1965", "text": "<PERSON>, English author", "html": "1965 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English author", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English author", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1965", "text": "<PERSON>, American football player", "html": "1965 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(American_football)\" title=\"<PERSON> (American football)\"><PERSON></a>, American football player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(American_football)\" title=\"<PERSON> (American football)\"><PERSON></a>, American football player", "links": [{"title": "<PERSON> (American football)", "link": "https://wikipedia.org/wiki/<PERSON>_(American_football)"}]}, {"year": "1965", "text": "<PERSON><PERSON> <PERSON><PERSON>, Canadian ice hockey player and coach", "html": "1965 - <a href=\"https://wikipedia.org/wiki/J<PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> <PERSON><PERSON>\"><PERSON><PERSON> <PERSON><PERSON></a>, Canadian ice hockey player and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/J<PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> <PERSON><PERSON>\"><PERSON><PERSON> <PERSON><PERSON></a>, Canadian ice hockey player and coach", "links": [{"title": "<PERSON><PERSON> <PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "1965", "text": "<PERSON>, American captain and pilot", "html": "1965 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%27Grady\" title=\"<PERSON>\"><PERSON></a>, American captain and pilot", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%27Grady\" title=\"<PERSON>\"><PERSON></a>, American captain and pilot", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Scott_O%27Grady"}]}, {"year": "1966", "text": "<PERSON>, Canadian actor and voice over artist (d. 2015)", "html": "1966 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian actor and voice over artist (d. 2015)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian actor and voice over artist (d. 2015)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1966", "text": "<PERSON><PERSON>, Dutch footballer", "html": "1966 - <a href=\"https://wikipedia.org/wiki/Wim_<PERSON>\" title=\"Wim <PERSON>\"><PERSON><PERSON></a>, Dutch footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Wim_<PERSON>\" title=\"Wim <PERSON>\"><PERSON><PERSON></a>, Dutch footballer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Wim_<PERSON>k"}]}, {"year": "1966", "text": "<PERSON>, Northern Irish singer-songwriter and guitarist", "html": "1966 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>(singer)\" title=\"<PERSON> (singer)\"><PERSON></a>, Northern Irish singer-songwriter and guitarist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>(singer)\" title=\"<PERSON> (singer)\"><PERSON></a>, Northern Irish singer-songwriter and guitarist", "links": [{"title": "<PERSON> (singer)", "link": "https://wikipedia.org/wiki/<PERSON>(singer)"}]}, {"year": "1966", "text": "<PERSON>, American game designer", "html": "1966 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American game designer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American game designer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1967", "text": "<PERSON>, American golfer", "html": "1967 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American golfer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American golfer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1968", "text": "<PERSON>, American race car driver", "html": "1968 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American race car driver", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American race car driver", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1968", "text": "<PERSON>, Australian rugby league player and sportscaster", "html": "1968 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian rugby league player and sportscaster", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian rugby league player and sportscaster", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1968", "text": "<PERSON>, Australian actor, singer, and producer", "html": "1968 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian actor, singer, and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian actor, singer, and producer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1968", "text": "<PERSON>, American football player", "html": "1968 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1968", "text": "<PERSON>, American actor (d. 2023)", "html": "1968 - <a href=\"https://wikipedia.org/wiki/<PERSON>_Rich\" title=\"Adam Rich\"><PERSON></a>, American actor (d. 2023)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Adam_Rich\" title=\"Adam Rich\"><PERSON></a>, American actor (d. 2023)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1969", "text": "<PERSON><PERSON>, American singer-songwriter, violinist, and producer", "html": "1969 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American singer-songwriter, violinist, and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American singer-songwriter, violinist, and producer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1969", "text": "<PERSON><PERSON><PERSON><PERSON>, Slovenian footballer", "html": "1969 - <a href=\"https://wikipedia.org/wiki/%C5%BDeljk<PERSON>_Milinovi%C4%8D\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Slovenian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/%C5%B<PERSON><PERSON><PERSON><PERSON>_Milinovi%C4%8D\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Slovenian footballer", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/%C5%B<PERSON><PERSON><PERSON><PERSON>_Milinovi%C4%8D"}]}, {"year": "1969", "text": "<PERSON><PERSON>, Canadian ice hockey player and coach", "html": "1969 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Canadian ice hockey player and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Canadian ice hockey player and coach", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1969", "text": "<PERSON>, American baseball player, coach, and manager", "html": "1969 - <a href=\"https://wikipedia.org/wiki/Jos%C3%A9_Valent%C3%ADn\" title=\"<PERSON>\"><PERSON></a>, American baseball player, coach, and manager", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Jos%C3%A9_Valent%C3%ADn\" title=\"<PERSON>\"><PERSON></a>, American baseball player, coach, and manager", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Jos%C3%A9_Valent%C3%ADn"}]}, {"year": "1970", "text": "<PERSON>, American actor, screenwriter, and Christian evangelical/anti-evolution activist", "html": "1970 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor, screenwriter, and Christian evangelical/anti-evolution activist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor, screenwriter, and Christian evangelical/anti-evolution activist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1970", "text": "<PERSON>, Belgian diver and physiotherapist (d. 2011)", "html": "1970 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Belgian diver and physiotherapist (d. 2011)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Belgian diver and physiotherapist (d. 2011)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1970", "text": "<PERSON><PERSON>, American baseball player", "html": "1970 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_St<PERSON>ze\" title=\"Tanyon Sturtze\"><PERSON><PERSON></a>, American baseball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_Sturtze\" title=\"Tanyon Sturtze\"><PERSON><PERSON></a>, American baseball player", "links": [{"title": "<PERSON><PERSON> Sturtze", "link": "https://wikipedia.org/wiki/Tanyon_Sturtze"}]}, {"year": "1970", "text": "<PERSON>, American basketball player and coach", "html": "1970 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player and coach", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1971", "text": "<PERSON>, American baseball player", "html": "1971 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1971", "text": "<PERSON>, Australian motorcycle racer", "html": "1971 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian motorcycle racer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian motorcycle racer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1971", "text": "<PERSON><PERSON>, American football player and actor (d. 2013)", "html": "1971 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American football player and actor (d. 2013)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American football player and actor (d. 2013)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1972", "text": "<PERSON>, Argentinian race car driver", "html": "1972 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Argentinian race car driver", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Argentinian race car driver", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1972", "text": "<PERSON>, Belgian footballer", "html": "1972 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Belgian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Belgian footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1973", "text": "<PERSON><PERSON>, Dominican baseball player", "html": "1973 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Dominican baseball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Dominican baseball player", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>rea"}]}, {"year": "1973", "text": "<PERSON>, English rugby player", "html": "1973 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(rugby_union)\" title=\"<PERSON> (rugby union)\"><PERSON></a>, English rugby player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(rugby_union)\" title=\"<PERSON> (rugby union)\"><PERSON></a>, English rugby player", "links": [{"title": "<PERSON> (rugby union)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(rugby_union)"}]}, {"year": "1974", "text": "<PERSON>, English snooker player", "html": "1974 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(snooker_player)\" title=\"<PERSON> (snooker player)\"><PERSON></a>, English snooker player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(snooker_player)\" title=\"<PERSON> (snooker player)\"><PERSON></a>, English snooker player", "links": [{"title": "<PERSON> (snooker player)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>(snooker_player)"}]}, {"year": "1975", "text": "<PERSON><PERSON>, Portuguese singer-songwriter, producer, and actress", "html": "1975 - <a href=\"https://wikipedia.org/wiki/Susan<PERSON>_F%C3%A9lix\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Portuguese singer-songwriter, producer, and actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Susan<PERSON>_<PERSON>%C3%A9lix\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Portuguese singer-songwriter, producer, and actress", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Susana_F%C3%A9lix"}]}, {"year": "1975", "text": "<PERSON>, American basketball player and runner", "html": "1975 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player and runner", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player and runner", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1975", "text": "<PERSON>, Canadian ice hockey player", "html": "1975 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1976", "text": "<PERSON>, New Zealand politician", "html": "1976 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, New Zealand politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, New Zealand politician", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1977", "text": "<PERSON><PERSON><PERSON>, American golfer", "html": "1977 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American golfer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American golfer", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1977", "text": "<PERSON><PERSON>, American skier", "html": "1977 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American skier", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> Miller\"><PERSON><PERSON></a>, American skier", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1977", "text": "<PERSON>, Venezuelan footballer", "html": "1977 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Venezuelan footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Venezuelan footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1978", "text": "<PERSON>, German footballer", "html": "1978 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>der"}]}, {"year": "1978", "text": "<PERSON>, Australian cyclist", "html": "1978 - <a href=\"https://wikipedia.org/wiki/<PERSON>_Cooke\" title=\"<PERSON> Cooke\"><PERSON></a>, Australian cyclist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_Cooke\" title=\"<PERSON> Cooke\"><PERSON></a>, Australian cyclist", "links": [{"title": "Baden Cooke", "link": "https://wikipedia.org/wiki/Baden_Cooke"}]}, {"year": "1978", "text": "<PERSON><PERSON>, Serbian basketball player", "html": "1978 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>%C4%87\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Serbian basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>%C4%87\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Serbian basketball player", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Marko_Jari%C4%87"}]}, {"year": "1979", "text": "<PERSON>, Northern Irish politician", "html": "1979 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Northern Irish politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Northern Irish politician", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1979", "text": "<PERSON>, English rugby player", "html": "1979 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English rugby player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English rugby player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1979", "text": "<PERSON>, American singer-songwriter and guitarist", "html": "1979 - <a href=\"https://wikipedia.org/wiki/Jordan_Pundik\" title=\"Jordan Pund<PERSON>\"><PERSON></a>, American singer-songwriter and guitarist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Jordan_Pundik\" title=\"Jordan Pundik\"><PERSON></a>, American singer-songwriter and guitarist", "links": [{"title": "Jordan Pundik", "link": "https://wikipedia.org/wiki/Jordan_Pundik"}]}, {"year": "1980", "text": "<PERSON><PERSON>, English footballer", "html": "1980 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_King\" title=\"Ledley King\"><PERSON><PERSON></a>, English footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_King\" title=\"Ledley King\"><PERSON><PERSON></a>, English footballer", "links": [{"title": "<PERSON><PERSON> King", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1980", "text": "<PERSON>, Belgian basketball player", "html": "1980 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Belgian basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Belgian basketball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1981", "text": "<PERSON>, American actor", "html": "1981 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1981", "text": "<PERSON>, Slovak ice hockey player", "html": "1981 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Slovak ice hockey player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Slovak ice hockey player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1981", "text": "<PERSON>, Scottish footballer and manager", "html": "1981 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(Scottish_footballer)\" title=\"<PERSON> (Scottish footballer)\"><PERSON></a>, Scottish footballer and manager", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(Scottish_footballer)\" title=\"<PERSON> (Scottish footballer)\"><PERSON></a>, Scottish footballer and manager", "links": [{"title": "<PERSON> (Scottish footballer)", "link": "https://wikipedia.org/wiki/<PERSON>(Scottish_footballer)"}]}, {"year": "1981", "text": "<PERSON>, American rower", "html": "1981 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American rower", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American rower", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1981", "text": "<PERSON>, American actor", "html": "1981 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1981", "text": "<PERSON>, New Zealand rugby player", "html": "1981 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, New Zealand rugby player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, New Zealand rugby player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1981", "text": "<PERSON>, Chinese tennis player", "html": "1981 - <a href=\"https://wikipedia.org/wiki/Sun_Tiantian\" title=\"Sun Tiantian\"><PERSON></a>, Chinese tennis player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Sun_Tiantian\" title=\"Sun Tiantian\"><PERSON></a>, Chinese tennis player", "links": [{"title": "Sun Tiant<PERSON>", "link": "https://wikipedia.org/wiki/Sun_Tiantian"}]}, {"year": "1983", "text": "<PERSON>, Australian footballer", "html": "1983 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Alex_<PERSON>"}]}, {"year": "1983", "text": "<PERSON>, English footballer", "html": "1983 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_Cole\" title=\"<PERSON> Cole\"><PERSON></a>, English footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_Cole"}]}, {"year": "1983", "text": "<PERSON>, English philanthropist, broadcaster, and acid violence survivor", "html": "1983 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English philanthropist, broadcaster, and <a href=\"https://wikipedia.org/wiki/Acid_violence\" class=\"mw-redirect\" title=\"Acid violence\">acid violence</a> survivor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English philanthropist, broadcaster, and <a href=\"https://wikipedia.org/wiki/Acid_violence\" class=\"mw-redirect\" title=\"Acid violence\">acid violence</a> survivor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Acid violence", "link": "https://wikipedia.org/wiki/Acid_violence"}]}, {"year": "1983", "text": "<PERSON><PERSON>, Japanese cricketer", "html": "1983 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Japanese cricketer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Japanese cricketer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1985", "text": "<PERSON>, American shot putter", "html": "1985 - <a href=\"https://wikipedia.org/wiki/<PERSON>(athlete)\" title=\"<PERSON> (athlete)\"><PERSON></a>, American shot putter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>(athlete)\" title=\"<PERSON> (athlete)\"><PERSON></a>, American shot putter", "links": [{"title": "<PERSON> (athlete)", "link": "https://wikipedia.org/wiki/<PERSON>(athlete)"}]}, {"year": "1985", "text": "<PERSON>, Canadian hockey player", "html": "1985 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(ice_hockey,_born_1985)\" title=\"<PERSON> (ice hockey, born 1985)\"><PERSON></a>, Canadian hockey player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(ice_hockey,_born_1985)\" title=\"<PERSON> (ice hockey, born 1985)\"><PERSON></a>, Canadian hockey player", "links": [{"title": "<PERSON> (ice hockey, born 1985)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(ice_hockey,_born_1985)"}]}, {"year": "1985", "text": "<PERSON>, Estonian high jumper", "html": "1985 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C5%A1t%C5%<PERSON><PERSON>\" title=\"<PERSON>\"><PERSON></a>, Estonian high jumper", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C5%A1t%C5%<PERSON><PERSON>\" title=\"<PERSON>\"><PERSON></a>, Estonian high jumper", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Anna_Ilju%C5%A1t%C5%A1enko"}]}, {"year": "1985", "text": "<PERSON><PERSON><PERSON>, Scottish rugby player", "html": "1985 - <a href=\"https://wikipedia.org/wiki/G<PERSON><PERSON>_Laidlaw\" title=\"Greig Laidlaw\"><PERSON><PERSON><PERSON></a>, Scottish rugby player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_Laidlaw\" title=\"G<PERSON>ig Laidlaw\"><PERSON><PERSON><PERSON></a>, Scottish rugby player", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>dlaw"}]}, {"year": "1985", "text": "<PERSON>, Swedish ice hockey player", "html": "1985 - <a href=\"https://wikipedia.org/wiki/Carl_S%C3%B6derberg\" title=\"<PERSON>\"><PERSON></a>, Swedish ice hockey player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Carl_S%C3%B6<PERSON><PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swedish ice hockey player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Carl_S%C3%B6<PERSON><PERSON>"}]}, {"year": "1986", "text": "<PERSON><PERSON><PERSON>, Greek footballer", "html": "1986 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Greek footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Greek footballer", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1986", "text": "<PERSON>, German footballer", "html": "1986 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1986", "text": "<PERSON><PERSON><PERSON>, Uruguayan footballer", "html": "1986 - <a href=\"https://wikipedia.org/wiki/Cristhian_Stuani\" title=\"Cristhian Stuani\"><PERSON><PERSON><PERSON></a>, Uruguayan footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Cristhian_Stuani\" title=\"Cristhian Stuani\"><PERSON><PERSON><PERSON></a>, Uruguayan footballer", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Cristhian_Stuani"}]}, {"year": "1986", "text": "<PERSON>, American actor", "html": "1986 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1987", "text": "<PERSON>, Belgian footballer", "html": "1987 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Belgian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Belgian footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1988", "text": "<PERSON>, New Zealand rugby player", "html": "1988 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, New Zealand rugby player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, New Zealand rugby player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1988", "text": "<PERSON><PERSON>, British singer", "html": "1988 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, British singer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, British singer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1989", "text": "<PERSON>, Japanese curler", "html": "1989 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Japanese curler", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Japanese curler", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1990", "text": "<PERSON>, English footballer", "html": "1990 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1991", "text": "<PERSON><PERSON>, Italian footballer", "html": "1991 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Italian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Italian footballer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1992", "text": "<PERSON>, American actor and producer", "html": "1992 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor and producer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1993", "text": "<PERSON><PERSON>, Dominican baseball player", "html": "1993 - <a href=\"https://wikipedia.org/wiki/Ketel_Marte\" title=\"Ketel Marte\"><PERSON><PERSON></a>, Dominican baseball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Ketel_Marte\" title=\"Ketel Marte\"><PERSON><PERSON></a>, Dominican baseball player", "links": [{"title": "Ketel Marte", "link": "https://wikipedia.org/wiki/Ketel_Marte"}]}, {"year": "1994", "text": "<PERSON>, Canadian ice hockey player", "html": "1994 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1994", "text": "<PERSON>, American swimmer", "html": "1994 - <a href=\"https://wikipedia.org/wiki/Olivia_Smoliga\" title=\"Olivia Smoliga\"><PERSON></a>, American swimmer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Olivia_Smoliga\" title=\"Olivia Smoliga\"><PERSON></a>, American swimmer", "links": [{"title": "Olivia <PERSON>", "link": "https://wikipedia.org/wiki/Olivia_Smoliga"}]}, {"year": "1995", "text": "<PERSON>, Welsh artistic gymnast", "html": "1995 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Welsh artistic gymnast", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Welsh artistic gymnast", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1996", "text": "<PERSON>, British singer", "html": "1996 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>(singer)\" title=\"<PERSON> (singer)\"><PERSON></a>, British singer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>(singer)\" title=\"<PERSON> (singer)\"><PERSON></a>, British singer", "links": [{"title": "<PERSON> (singer)", "link": "https://wikipedia.org/wiki/<PERSON>(singer)"}]}, {"year": "1996", "text": "<PERSON>, Welsh rugby player", "html": "1996 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Welsh rugby player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Welsh rugby player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1997", "text": "<PERSON>, Australian rugby league player", "html": "1997 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian rugby league player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian rugby league player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1999", "text": "<PERSON><PERSON><PERSON><PERSON>, Irish actor and musician", "html": "1999 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Irish actor and musician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Irish actor and musician", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>-<PERSON>"}]}, {"year": "2002", "text": "<PERSON>, American actress", "html": "2002 - <a href=\"https://wikipedia.org/wiki/Iris_Apatow\" title=\"<PERSON> Apatow\"><PERSON></a>, American actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Iris_Apatow\" title=\"<PERSON> Apatow\"><PERSON></a>, American actress", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Iris_Apatow"}]}, {"year": "2004", "text": "<PERSON><PERSON>, American ventriloquist", "html": "2004 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON></a>, American ventriloquist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> <PERSON><PERSON>\"><PERSON><PERSON></a>, American ventriloquist", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>"}]}], "Deaths": [{"year": "322 BC", "text": "<PERSON><PERSON><PERSON><PERSON>, Athenian statesman, (b. 384 BC)", "html": "322 BC - 322 BC - <a href=\"https://wikipedia.org/wiki/Demosthen<PERSON>\" title=\"Demosthen<PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Athenian statesman, (b. 384 BC)", "no_year_html": "322 BC - <a href=\"https://wikipedia.org/wiki/Demosthen<PERSON>\" title=\"Demosthen<PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Athenian statesman, (b. 384 BC)", "links": [{"title": "Demosthen<PERSON>", "link": "https://wikipedia.org/wiki/Demosthenes"}]}, {"year": "638", "text": "<PERSON><PERSON>, pope of the Catholic Church", "html": "638 - <a href=\"https://wikipedia.org/wiki/Pope_<PERSON>ius_<PERSON>\" title=\"Pope Honorius <PERSON>\"><PERSON><PERSON></a>, pope of the Catholic Church", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Pope_<PERSON><PERSON>_<PERSON>\" title=\"Pope Honorius I\"><PERSON><PERSON></a>, pope of the Catholic Church", "links": [{"title": "<PERSON> <PERSON> I", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "642", "text": "<PERSON>, pope of the Catholic Church", "html": "642 - <a href=\"https://wikipedia.org/wiki/Pope_<PERSON>_IV\" title=\"Pope John IV\"><PERSON> IV</a>, pope of the Catholic Church", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Pope_<PERSON>_IV\" title=\"Pope John IV\"><PERSON> IV</a>, pope of the Catholic Church", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "884", "text": "<PERSON><PERSON><PERSON><PERSON>, Japanese prince (b. 825)", "html": "884 - <a href=\"https://wikipedia.org/wiki/Prince_<PERSON>sun<PERSON><PERSON>\" title=\"Prince Tsunesada\"><PERSON><PERSON><PERSON><PERSON></a>, Japanese prince (b. 825)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Prince_<PERSON>sun<PERSON><PERSON>\" title=\"Prince Tsunesada\"><PERSON><PERSON><PERSON><PERSON></a>, Japanese prince (b. 825)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "974", "text": "<PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON> caliph (b. 913/14)", "html": "974 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, <PERSON><PERSON> caliph (b. 913/14)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, <PERSON><PERSON> caliph (b. 913/14)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>"}]}, {"year": "1095", "text": "<PERSON>, margrave of Austria (b. 1050)", "html": "1095 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Mar<PERSON>_of_Austria\" title=\"<PERSON>, Margrave of Austria\"><PERSON></a>, margrave of Austria (b. 1050)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_<PERSON><PERSON>_of_Austria\" title=\"<PERSON> II, Margrave of Austria\"><PERSON></a>, margrave of Austria (b. 1050)", "links": [{"title": "<PERSON>, Margrave of Austria", "link": "https://wikipedia.org/wiki/Leopold_<PERSON>,_<PERSON><PERSON>_of_Austria"}]}, {"year": "1152", "text": "<PERSON> of Berg, German nobleman (b. 1080)", "html": "1152 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_Berg\" title=\"<PERSON> of Berg\"><PERSON> Berg</a>, German nobleman (b. 1080)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON> of Berg\"><PERSON> of Berg</a>, German nobleman (b. 1080)", "links": [{"title": "<PERSON> Berg", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_Berg"}]}, {"year": "1176", "text": "<PERSON>, 1st Earl of Arundel, English politician (b. 1109)", "html": "1176 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%27<PERSON><PERSON><PERSON>,_1st_Earl_of_Arundel\" title=\"<PERSON>, 1st Earl of Arundel\"><PERSON>, 1st Earl of Arundel</a>, English politician (b. 1109)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%27<PERSON><PERSON><PERSON>,_1st_Earl_of_Arundel\" title=\"<PERSON>, 1st Earl of Arundel\"><PERSON>, 1st Earl of Arundel</a>, English politician (b. 1109)", "links": [{"title": "<PERSON>, 1st Earl of Arundel", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>%27<PERSON><PERSON><PERSON>,_1st_Earl_of_Arundel"}]}, {"year": "1320", "text": "<PERSON>, Byzantine emperor (b. 1277)", "html": "1320 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Byzantine emperor (b. 1277)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>laiologos\"><PERSON></a>, Byzantine emperor (b. 1277)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1328", "text": "<PERSON><PERSON> of Hungary, queen consort of France and Navarre (b. 1293)", "html": "1328 - <a href=\"https://wikipedia.org/wiki/Clementia_of_Hungary\" title=\"Clementia of Hungary\"><PERSON><PERSON> of Hungary</a>, queen consort of France and Navarre (b. 1293)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Clementia_of_Hungary\" title=\"<PERSON><PERSON> of Hungary\"><PERSON><PERSON> of Hungary</a>, queen consort of France and Navarre (b. 1293)", "links": [{"title": "Clementia of Hungary", "link": "https://wikipedia.org/wiki/Clementia_of_Hungary"}]}, {"year": "1448", "text": "<PERSON>, Chinese prince, historian and playwright (b. 1378)", "html": "1448 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Chinese prince, historian and playwright (b. 1378)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Chinese prince, historian and playwright (b. 1378)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1491", "text": "<PERSON>, German painter (b. 1449)", "html": "1491 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German painter (b. 1449)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German painter (b. 1449)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1492", "text": "<PERSON><PERSON>, Italian mathematician and painter (b. 1415)", "html": "1492 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Italian mathematician and painter (b. 1415)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Italian mathematician and painter (b. 1415)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1565", "text": "<PERSON>, French-American lieutenant and navigator (b. 1520)", "html": "1565 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French-American lieutenant and navigator (b. 1520)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French-American lieutenant and navigator (b. 1520)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1576", "text": "<PERSON>, Holy Roman Emperor (b. 1527)", "html": "1576 - <a href=\"https://wikipedia.org/wiki/<PERSON>_II,_Holy_Roman_Emperor\" title=\"<PERSON> II, Holy Roman Emperor\"><PERSON></a>, Holy Roman Emperor (b. 1527)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_II,_Holy_Roman_Emperor\" title=\"<PERSON> II, Holy Roman Emperor\"><PERSON></a>, Holy Roman Emperor (b. 1527)", "links": [{"title": "<PERSON>, Holy Roman Emperor", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Holy_Roman_Emperor"}]}, {"year": "1590", "text": "<PERSON><PERSON><PERSON>, Japanese painter and educator (b. 1543)", "html": "1590 - <a href=\"https://wikipedia.org/wiki/Kan%C5%8D_E<PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese painter and educator (b. 1543)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Kan%C5%8D_E<PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese painter and educator (b. 1543)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Kan%C5%8D_<PERSON><PERSON>ku"}]}, {"year": "1600", "text": "<PERSON>, Spanish priest and philosopher (b. 1535)", "html": "1600 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Spanish priest and philosopher (b. 1535)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Spanish priest and philosopher (b. 1535)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1601", "text": "<PERSON>, English landowner (b. 1560)", "html": "1601 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>ren<PERSON>\" title=\"<PERSON> Brend\"><PERSON></a>, English landowner (b. 1560)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>ren<PERSON>\" title=\"<PERSON> Brend\"><PERSON></a>, English landowner (b. 1560)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>rend"}]}, {"year": "1632", "text": "<PERSON><PERSON><PERSON>, Japanese commander (b. 1549)", "html": "1632 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese commander (b. 1549)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese commander (b. 1549)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON><PERSON>na"}]}, {"year": "1646", "text": "<PERSON>, French general and courtier (b. 1579)", "html": "1646 - <a href=\"https://wikipedia.org/wiki/Fran%C3%A<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French general and courtier (b. 1579)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Fran%C3%A<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French general and courtier (b. 1579)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Fran%C3%A<PERSON><PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1654", "text": "<PERSON><PERSON>, Dutch painter (b. 1622)", "html": "1654 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_Fabrit<PERSON>\" title=\"<PERSON><PERSON> Fabritius\"><PERSON><PERSON></a>, Dutch painter (b. 1622)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>ab<PERSON>\" title=\"<PERSON><PERSON> Fabritius\"><PERSON><PERSON></a>, Dutch painter (b. 1622)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>abritius"}]}, {"year": "1678", "text": "<PERSON>, English lawyer and judge (b. 1621)", "html": "1678 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English lawyer and judge (b. 1621)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English lawyer and judge (b. 1621)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1679", "text": "<PERSON>, English minister, theologian, and author (b. 1617)", "html": "1679 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English minister, theologian, and author (b. 1617)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English minister, theologian, and author (b. 1617)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1685", "text": "<PERSON>, Austrian lawyer and jurist (b. 1628)", "html": "1685 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Austrian lawyer and jurist (b. 1628)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Austrian lawyer and jurist (b. 1628)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "1730", "text": "<PERSON>, king of Denmark and Norway (b. 1671)", "html": "1730 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Denmark\" title=\"<PERSON> IV of Denmark\"><PERSON> IV</a>, king of Denmark and Norway (b. 1671)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Denmark\" title=\"<PERSON> IV of Denmark\"><PERSON> IV</a>, king of Denmark and Norway (b. 1671)", "links": [{"title": "<PERSON> of Denmark", "link": "https://wikipedia.org/wiki/Frederick_IV_of_Denmark"}]}, {"year": "1758", "text": "<PERSON>, 3rd Viscount <PERSON>, Irish field marshal and politician (b. 1680)", "html": "1758 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_3rd_Viscount_<PERSON>worth\" title=\"<PERSON>, 3rd Viscount <PERSON>worth\"><PERSON>, 3rd Viscount <PERSON></a>, Irish field marshal and politician (b. 1680)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_3rd_Viscount_<PERSON>\" title=\"<PERSON>, 3rd Viscount <PERSON>\"><PERSON>, 3rd Viscount <PERSON></a>, Irish field marshal and politician (b. 1680)", "links": [{"title": "<PERSON>, 3rd Viscount <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>,_3rd_Viscount_<PERSON>"}]}, {"year": "1812", "text": "<PERSON>, Argentinian lawyer and politician (b. 1764)", "html": "1812 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A9_<PERSON><PERSON>\" title=\"<PERSON>\"><PERSON></a>, Argentinian lawyer and politician (b. 1764)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Argentinian lawyer and politician (b. 1764)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A9_<PERSON>"}]}, {"year": "1828", "text": "<PERSON><PERSON> of Pindus, Aromanian physician and noble (b. 1737)", "html": "1828 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_of_Pindus\" title=\"<PERSON><PERSON> of Pindus\"><PERSON><PERSON> of Pindus</a>, Aromanian physician and noble (b. 1737)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_of_Pindus\" title=\"<PERSON><PERSON> of Pindus\"><PERSON><PERSON> of Pindus</a>, Aromanian physician and noble (b. 1737)", "links": [{"title": "<PERSON><PERSON> of Pindus", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_of_Pindus"}]}, {"year": "1845", "text": "<PERSON>, English prison reformer, Quaker and philanthropist (b. 1780)", "html": "1845 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English prison reformer, Quaker and philanthropist (b. 1780)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English prison reformer, Quaker and philanthropist (b. 1780)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1858", "text": "<PERSON><PERSON><PERSON><PERSON>, Japanese painter (b. 1797)", "html": "1858 - <a href=\"https://wikipedia.org/wiki/Hiroshige\" title=\"<PERSON>roshige\"><PERSON><PERSON><PERSON><PERSON></a>, Japanese painter (b. 1797)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>roshige\" title=\"<PERSON>roshige\"><PERSON><PERSON><PERSON><PERSON></a>, Japanese painter (b. 1797)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Hiroshige"}]}, {"year": "1870", "text": "<PERSON>, American general (b. 1807)", "html": "1870 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American general (b. 1807)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American general (b. 1807)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "1875", "text": "<PERSON><PERSON><PERSON>, French sculptor and painter (b. 1827)", "html": "1875 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, French sculptor and painter (b. 1827)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, French sculptor and painter (b. 1827)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>"}]}, {"year": "1896", "text": "<PERSON>, Danish lawyer and politician, 9th Council President of Denmark (b. 1817)", "html": "1896 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>-<PERSON>\" title=\"<PERSON>Vind-Frijs\"><PERSON></a>, Danish lawyer and politician, 9th <a href=\"https://wikipedia.org/wiki/Council_President_of_Denmark\" class=\"mw-redirect\" title=\"Council President of Denmark\">Council President of Denmark</a> (b. 1817)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Christian <PERSON>-Frijs\"><PERSON></a>, Danish lawyer and politician, 9th <a href=\"https://wikipedia.org/wiki/Council_President_of_Denmark\" class=\"mw-redirect\" title=\"Council President of Denmark\">Council President of Denmark</a> (b. 1817)", "links": [{"title": "<PERSON>-Vin<PERSON>-Fr<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>-<PERSON>-<PERSON>-<PERSON>s"}, {"title": "Council President of Denmark", "link": "https://wikipedia.org/wiki/Council_President_of_Denmark"}]}, {"year": "1898", "text": "<PERSON>, American minister and activist (b. 1816)", "html": "1898 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American minister and activist (b. 1816)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American minister and activist (b. 1816)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Calvin_<PERSON>bank"}]}, {"year": "1914", "text": "<PERSON>, American inventor (b. 1838)", "html": "1914 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American inventor (b. 1838)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American inventor (b. 1838)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1915", "text": "<PERSON>, English nurse (b. 1865)", "html": "1915 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English nurse (b. 1865)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English nurse (b. 1865)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1923", "text": "<PERSON>, English cricketer (b. 1857)", "html": "1923 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> <PERSON><PERSON>\"><PERSON></a>, English cricketer (b. 1857)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> <PERSON><PERSON>\"><PERSON></a>, English cricketer (b. 1857)", "links": [{"title": "<PERSON><PERSON> <PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "1924", "text": "<PERSON><PERSON><PERSON>, French journalist, novelist, and poet, Nobel Prize laureate (b. 1844)", "html": "1924 - <a href=\"https://wikipedia.org/wiki/Anatole_France\" title=\"Anatole France\"><PERSON><PERSON><PERSON></a>, French journalist, novelist, and poet, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Literature\" title=\"Nobel Prize in Literature\">Nobel Prize</a> laureate (b. 1844)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Anatole_France\" title=\"Anatole France\"><PERSON><PERSON><PERSON> France</a>, French journalist, novelist, and poet, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Literature\" title=\"Nobel Prize in Literature\">Nobel Prize</a> laureate (b. 1844)", "links": [{"title": "Anatole France", "link": "https://wikipedia.org/wiki/Anatole_France"}, {"title": "Nobel Prize in Literature", "link": "https://wikipedia.org/wiki/Nobel_Prize_in_Literature"}]}, {"year": "1926", "text": "<PERSON>, English theologian and author (b. 1838)", "html": "1926 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English theologian and author (b. 1838)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English theologian and author (b. 1838)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1933", "text": "<PERSON>, English philanthropist and politician (b. 1847)", "html": "1933 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(UK_politician)\" class=\"mw-redirect\" title=\"<PERSON> (UK politician)\"><PERSON></a>, English philanthropist and politician (b. 1847)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(UK_politician)\" class=\"mw-redirect\" title=\"<PERSON> (UK politician)\"><PERSON></a>, English philanthropist and politician (b. 1847)", "links": [{"title": "<PERSON> (UK politician)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(UK_politician)"}]}, {"year": "1940", "text": "<PERSON>, American actor, director, producer, and screenwriter (b. 1880)", "html": "1940 - <a href=\"https://wikipedia.org/wiki/Tom_Mix\" title=\"Tom Mix\"><PERSON></a>, American actor, director, producer, and screenwriter (b. 1880)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Tom_Mix\" title=\"Tom Mix\"><PERSON></a>, American actor, director, producer, and screenwriter (b. 1880)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1946", "text": "<PERSON>, American general (b. 1883)", "html": "1946 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American general (b. 1883)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American general (b. 1883)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1948", "text": "<PERSON>, English psychologist and psychoanalyst (b. 1885)", "html": "1948 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English psychologist and psychoanalyst (b. 1885)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English psychologist and psychoanalyst (b. 1885)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1954", "text": "<PERSON>, American soldier and pilot (b. 1918)", "html": "1954 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(pilot)\" title=\"<PERSON> (pilot)\"><PERSON></a>, American soldier and pilot (b. 1918)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(pilot)\" title=\"<PERSON> (pilot)\"><PERSON></a>, American soldier and pilot (b. 1918)", "links": [{"title": "<PERSON> (pilot)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(pilot)"}]}, {"year": "1956", "text": "<PERSON>, Italian composer and painter (b. 1872)", "html": "1956 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian composer and painter (b. 1872)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian composer and painter (b. 1872)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1957", "text": "<PERSON><PERSON>, Indonesian-Dutch linguist and physician (b. 1865)", "html": "1957 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Indonesian-Dutch linguist and physician (b. 1865)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Indonesian-Dutch linguist and physician (b. 1865)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1958", "text": "<PERSON>, American actor, director, and producer (b. 1907)", "html": "1958 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor, director, and producer (b. 1907)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor, director, and producer (b. 1907)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1960", "text": "<PERSON><PERSON><PERSON>, Japanese lawyer and politician (b. 1898)", "html": "1960 - <a href=\"https://wikipedia.org/wiki/Inejiro_Asanuma\" class=\"mw-redirect\" title=\"Inejiro Asanuma\"><PERSON><PERSON><PERSON></a>, Japanese lawyer and politician (b. 1898)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Inejiro_<PERSON>anuma\" class=\"mw-redirect\" title=\"Inejiro Asanuma\"><PERSON><PERSON><PERSON></a>, Japanese lawyer and politician (b. 1898)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Inej<PERSON>_<PERSON>"}]}, {"year": "1965", "text": "<PERSON>, Swiss chemist and academic, Nobel Prize laureate (b. 1899)", "html": "1965 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>%C3%BCller\" title=\"<PERSON>\"><PERSON></a>, Swiss chemist and academic, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Physiology_or_Medicine\" title=\"Nobel Prize in Physiology or Medicine\">Nobel Prize</a> laureate (b. 1899)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>%C3%BCller\" title=\"<PERSON>\"><PERSON></a>, Swiss chemist and academic, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Physiology_or_Medicine\" title=\"Nobel Prize in Physiology or Medicine\">Nobel Prize</a> laureate (b. 1899)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>%C3%BCller"}, {"title": "Nobel Prize in Physiology or Medicine", "link": "https://wikipedia.org/wiki/Nobel_Prize_in_Physiology_or_Medicine"}]}, {"year": "1967", "text": "<PERSON>, Indian activist and politician (b. 1910)", "html": "1967 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Indian activist and politician (b. 1910)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Indian activist and politician (b. 1910)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1969", "text": "<PERSON><PERSON>, Norwegian figure skater and actress (b. 1912)", "html": "1969 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Norwegian figure skater and actress (b. 1912)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Norwegian figure skater and actress (b. 1912)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>"}]}, {"year": "1969", "text": "<PERSON>, Russian-French painter and academic (b. 1906)", "html": "1969 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian-French painter and academic (b. 1906)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian-French painter and academic (b. 1906)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1969", "text": "<PERSON>, Finnish javelin thrower and soldier (b. 1891)", "html": "1969 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Finnish javelin thrower and soldier (b. 1891)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Finnish javelin thrower and soldier (b. 1891)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1970", "text": "<PERSON><PERSON><PERSON>, Russian-American illustrator and painter (b. 1891)", "html": "1970 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Russian-American illustrator and painter (b. 1891)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Russian-American illustrator and painter (b. 1891)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1970", "text": "<PERSON>, Pakistani poet and academic (b. 1930)", "html": "1970 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Pakistani poet and academic (b. 1930)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Pakistani poet and academic (b. 1930)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1971", "text": "<PERSON>, American lawyer and politician, 51st United States Secretary of State (b. 1893)", "html": "1971 - <a href=\"https://wikipedia.org/wiki/Dean_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and politician, 51st <a href=\"https://wikipedia.org/wiki/United_States_Secretary_of_State\" title=\"United States Secretary of State\">United States Secretary of State</a> (b. 1893)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and politician, 51st <a href=\"https://wikipedia.org/wiki/United_States_Secretary_of_State\" title=\"United States Secretary of State\">United States Secretary of State</a> (b. 1893)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "United States Secretary of State", "link": "https://wikipedia.org/wiki/United_States_Secretary_of_State"}]}, {"year": "1971", "text": "<PERSON>, American musician (b. 1935)", "html": "1971 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American musician (b. 1935)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American musician (b. 1935)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1972", "text": "<PERSON>, French-Argentinian actor and politician (b. 1900)", "html": "1972 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French-Argentinian actor and politician (b. 1900)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French-Argentinian actor and politician (b. 1900)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1973", "text": "<PERSON>, Austrian mountaineer, geographer, and cartographer (b. 1899)", "html": "1973 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Austrian mountaineer, geographer, and cartographer (b. 1899)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Austrian mountaineer, geographer, and cartographer (b. 1899)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1978", "text": "<PERSON>, American figure of the 1970s punk rock scene (b. 1958)", "html": "1978 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American figure of the 1970s punk rock scene (b. 1958)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American figure of the 1970s punk rock scene (b. 1958)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>ngen"}]}, {"year": "1984", "text": "<PERSON>, English politician (b. 1925)", "html": "1984 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English politician (b. 1925)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English politician (b. 1925)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1985", "text": "<PERSON>, American radio host and game show announcer (b. 1910)", "html": "1985 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American radio host and game show announcer (b. 1910)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American radio host and game show announcer (b. 1910)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1985", "text": "<PERSON>, American singer-songwriter and guitarist (b. 1953)", "html": "1985 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(American_musician)\" class=\"mw-redirect\" title=\"<PERSON> (American musician)\"><PERSON></a>, American singer-songwriter and guitarist (b. 1953)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(American_musician)\" class=\"mw-redirect\" title=\"<PERSON> (American musician)\"><PERSON></a>, American singer-songwriter and guitarist (b. 1953)", "links": [{"title": "<PERSON> (American musician)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(American_musician)"}]}, {"year": "1987", "text": "<PERSON><PERSON>, American lieutenant and politician, 26th Governor of Kansas (b. 1887)", "html": "1987 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> <PERSON>\"><PERSON><PERSON></a>, American lieutenant and politician, 26th <a href=\"https://wikipedia.org/wiki/Governor_of_Kansas\" class=\"mw-redirect\" title=\"Governor of Kansas\">Governor of Kansas</a> (b. 1887)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> <PERSON>\"><PERSON><PERSON></a>, American lieutenant and politician, 26th <a href=\"https://wikipedia.org/wiki/Governor_of_Kansas\" class=\"mw-redirect\" title=\"Governor of Kansas\">Governor of Kansas</a> (b. 1887)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Alf_<PERSON>"}, {"title": "Governor of Kansas", "link": "https://wikipedia.org/wiki/Governor_of_Kansas"}]}, {"year": "1987", "text": "<PERSON><PERSON><PERSON>, Turkish commander and politician, 6th President of Turkey (b. 1903)", "html": "1987 - <a href=\"https://wikipedia.org/wiki/F<PERSON>ri_Korut%C3%BCrk\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Turkish commander and politician, 6th <a href=\"https://wikipedia.org/wiki/President_of_Turkey\" title=\"President of Turkey\">President of Turkey</a> (b. 1903)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>ri_Korut%C3%BCrk\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Turkish commander and politician, 6th <a href=\"https://wikipedia.org/wiki/President_of_Turkey\" title=\"President of Turkey\">President of Turkey</a> (b. 1903)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Fahri_Korut%C3%BCrk"}, {"title": "President of Turkey", "link": "https://wikipedia.org/wiki/President_of_Turkey"}]}, {"year": "1988", "text": "<PERSON><PERSON>, Welsh-English poet and author (b. 1886)", "html": "1988 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Welsh-English poet and author (b. 1886)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Welsh-English poet and author (b. 1886)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1988", "text": "<PERSON><PERSON>, American painter and illustrator (b. 1913)", "html": "1988 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_Whit<PERSON>\" title=\"<PERSON><PERSON> Whitmore\"><PERSON><PERSON></a>, American painter and illustrator (b. 1913)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_Whitmore\" title=\"Co<PERSON> Whitmore\"><PERSON><PERSON></a>, American painter and illustrator (b. 1913)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Coby_<PERSON>hitmore"}]}, {"year": "1989", "text": "<PERSON>, American animator, producer, and screenwriter, founded Jay Ward Productions (b. 1920)", "html": "1989 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American animator, producer, and screenwriter, founded <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_Productions\" title=\"Jay Ward Productions\">Jay Ward Productions</a> (b. 1920)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American animator, producer, and screenwriter, founded <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_Productions\" title=\"Jay Ward Productions\">Jay Ward Productions</a> (b. 1920)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Jay <PERSON> Productions", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1990", "text": "<PERSON><PERSON><PERSON><PERSON>, Egyptian politician (b. 1926)", "html": "1990 - <a href=\"https://wikipedia.org/wiki/Rifaa<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON> <PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Egyptian politician (b. 1926)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Rifaa<PERSON>_<PERSON>\" title=\"<PERSON>if<PERSON><PERSON> <PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON> <PERSON></a>, Egyptian politician (b. 1926)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Rifaa<PERSON>_<PERSON>-<PERSON>"}]}, {"year": "1990", "text": "<PERSON>, Norwegian physician, mountaineer, and author (b. 1899)", "html": "1990 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Norwegian physician, mountaineer, and author (b. 1899)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Norwegian physician, mountaineer, and author (b. 1899)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1991", "text": "<PERSON>, Australian actress (b. 1916)", "html": "1991 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian actress (b. 1916)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian actress (b. 1916)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1991", "text": "<PERSON><PERSON>, Russian author and translator (b. 1925)", "html": "1991 - <a href=\"https://wikipedia.org/wiki/Arkady_Strugatsky\" class=\"mw-redirect\" title=\"Arkady Strugatsky\"><PERSON><PERSON></a>, Russian author and translator (b. 1925)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Arkady_Strugatsky\" class=\"mw-redirect\" title=\"Arkady Strugatsky\"><PERSON><PERSON></a>, Russian author and translator (b. 1925)", "links": [{"title": "Arkady Strugatsky", "link": "https://wikipedia.org/wiki/Arkady_Strugatsky"}]}, {"year": "1991", "text": "<PERSON>, American actor (b. 1898)", "html": "1991 - <a href=\"https://wikipedia.org/wiki/<PERSON>_Too<PERSON>y\" title=\"<PERSON> Toomey\"><PERSON></a>, American actor (b. 1898)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_Toomey\" title=\"<PERSON> Too<PERSON>\"><PERSON></a>, American actor (b. 1898)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Regis_Toomey"}]}, {"year": "1993", "text": "<PERSON>, American actor (b. 1902)", "html": "1993 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor (b. 1902)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor (b. 1902)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1994", "text": "<PERSON><PERSON><PERSON>, Canadian journalist and politician (b. 1938)", "html": "1994 - <a href=\"https://wikipedia.org/wiki/G%C3%A9<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Canadian journalist and politician (b. 1938)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/G%C3%A9<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Canadian journalist and politician (b. 1938)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/G%C3%A9ral<PERSON>_<PERSON>in"}]}, {"year": "1996", "text": "<PERSON>, French tennis player and fashion designer, co-founded <PERSON><PERSON><PERSON> (b. 1904)", "html": "1996 - <a href=\"https://wikipedia.org/wiki/Ren%C3%A9_Lacoste\" title=\"<PERSON>\"><PERSON></a>, French tennis player and fashion designer, co-founded <a href=\"https://wikipedia.org/wiki/Lacoste\" title=\"Lacost<PERSON>\"><PERSON><PERSON><PERSON></a> (b. 1904)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Ren%C3%A9_Lacoste\" title=\"<PERSON>\"><PERSON></a>, French tennis player and fashion designer, co-founded <a href=\"https://wikipedia.org/wiki/Lacoste\" title=\"Lacost<PERSON>\"><PERSON><PERSON><PERSON></a> (b. 1904)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Ren%C3%A9_Lacoste"}, {"title": "Lacoste", "link": "https://wikipedia.org/wiki/Lacoste"}]}, {"year": "1996", "text": "<PERSON>, French cyclist (b. 1911)", "html": "1996 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A9bie\" title=\"<PERSON>\"><PERSON></a>, French cyclist (b. 1911)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A9bie\" title=\"<PERSON>\"><PERSON></a>, French cyclist (b. 1911)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A9bie"}]}, {"year": "1997", "text": "<PERSON>, American singer-songwriter, guitarist, and actor (b. 1943)", "html": "1997 - <a href=\"https://wikipedia.org/wiki/John_Denver\" title=\"John Denver\"><PERSON></a>, American singer-songwriter, guitarist, and actor (b. 1943)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/John_Denver\" title=\"John Denver\"><PERSON></a>, American singer-songwriter, guitarist, and actor (b. 1943)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/John_<PERSON>"}]}, {"year": "1998", "text": "<PERSON>, Canadian lawyer and politician (b. 1930)", "html": "1998 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(Canadian_senator)\" class=\"mw-redirect\" title=\"<PERSON> (Canadian senator)\"><PERSON></a>, Canadian lawyer and politician (b. 1930)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(Canadian_senator)\" class=\"mw-redirect\" title=\"<PERSON> (Canadian senator)\"><PERSON></a>, Canadian lawyer and politician (b. 1930)", "links": [{"title": "<PERSON> (Canadian senator)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(Canadian_senator)"}]}, {"year": "1998", "text": "<PERSON>, American murder victim (b. 1976)", "html": "1998 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American murder victim (b. 1976)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American murder victim (b. 1976)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1999", "text": "<PERSON><PERSON>, American basketball player and coach (b. 1936)", "html": "1999 - <a href=\"https://wikipedia.org/wiki/Wilt_<PERSON>\" title=\"Wilt <PERSON>\"><PERSON><PERSON> <PERSON></a>, American basketball player and coach (b. 1936)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/W<PERSON>_<PERSON>\" title=\"Wilt <PERSON>\"><PERSON><PERSON> <PERSON></a>, American basketball player and coach (b. 1936)", "links": [{"title": "W<PERSON>", "link": "https://wikipedia.org/wiki/Wilt_<PERSON>"}]}, {"year": "1999", "text": "<PERSON>, Australian lawyer and judge (b. 1919)", "html": "1999 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian lawyer and judge (b. 1919)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian lawyer and judge (b. 1919)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "2001", "text": "<PERSON><PERSON><PERSON>, Baron <PERSON> of St Marylebone, English academic and politician, Lord High Chancellor of Great Britain (b. 1907)", "html": "2001 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>,_Baron_<PERSON>_of_St_Marylebone\" title=\"<PERSON><PERSON><PERSON>, Baron <PERSON> of St Marylebone\"><PERSON><PERSON><PERSON>, Baron <PERSON> of St Marylebone</a>, English academic and politician, <a href=\"https://wikipedia.org/wiki/Lord_Chancellor\" title=\"Lord Chancellor\">Lord High Chancellor of Great Britain</a> (b. 1907)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>,_Baron_<PERSON>_of_St_Marylebone\" title=\"<PERSON><PERSON><PERSON>, Baron <PERSON> of St Marylebone\"><PERSON><PERSON><PERSON>, Baron <PERSON> of St Marylebone</a>, English academic and politician, <a href=\"https://wikipedia.org/wiki/Lord_Chancellor\" title=\"Lord Chancellor\">Lord High Chancellor of Great Britain</a> (b. 1907)", "links": [{"title": "<PERSON><PERSON><PERSON>, Baron <PERSON> of St Marylebone", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>,_Baron_<PERSON>_of_St_Marylebone"}, {"title": "Lord Chancellor", "link": "https://wikipedia.org/wiki/Lord_Chancellor"}]}, {"year": "2001", "text": "<PERSON><PERSON><PERSON>, Turkish conductor (b. 1924)", "html": "2001 - <a href=\"https://wikipedia.org/wiki/Hikmet_%C5%9Eim%C5%9Fek\" title=\"<PERSON>k<PERSON>im<PERSON>\"><PERSON><PERSON><PERSON></a>, Turkish conductor (b. 1924)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Hikmet_%C5%9Eim%C5%9Fek\" title=\"<PERSON>k<PERSON>im<PERSON>\"><PERSON><PERSON><PERSON></a>, Turkish conductor (b. 1924)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Hikmet_%C5%9Eim%C5%9Fek"}]}, {"year": "2001", "text": "<PERSON>, Ballet critic and writer (b. 1916)", "html": "2001 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Ballet critic and writer (b. 1916)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Ballet critic and writer (b. 1916)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2002", "text": "<PERSON>, American bandleader and composer (b. 1916)", "html": "2002 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American bandleader and composer (b. 1916)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American bandleader and composer (b. 1916)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2002", "text": "<PERSON>, French biologist and diver (b. 1974)", "html": "2002 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French biologist and diver (b. 1974)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French biologist and diver (b. 1974)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2003", "text": "<PERSON>, Australian economist and politician, 4th Deputy Prime Minister of Australia (b. 1914)", "html": "2003 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian economist and politician, 4th <a href=\"https://wikipedia.org/wiki/Deputy_Prime_Minister_of_Australia\" title=\"Deputy Prime Minister of Australia\">Deputy Prime Minister of Australia</a> (b. 1914)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian economist and politician, 4th <a href=\"https://wikipedia.org/wiki/Deputy_Prime_Minister_of_Australia\" title=\"Deputy Prime Minister of Australia\">Deputy Prime Minister of Australia</a> (b. 1914)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Deputy Prime Minister of Australia", "link": "https://wikipedia.org/wiki/Deputy_Prime_Minister_of_Australia"}]}, {"year": "2003", "text": "<PERSON>, American philanthropist (b. 1928)", "html": "2003 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American philanthropist (b. 1928)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American philanthropist (b. 1928)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2003", "text": "<PERSON>, American jockey (b. 1931)", "html": "2003 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American jockey (b. 1931)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American jockey (b. 1931)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2005", "text": "<PERSON><PERSON>, American activist and politician (b. 1927)", "html": "2005 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American activist and politician (b. 1927)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American activist and politician (b. 1927)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "2006", "text": "<PERSON><PERSON>, German glider pilot (b. 1956)", "html": "2006 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, German glider pilot (b. 1956)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, German glider pilot (b. 1956)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>k"}]}, {"year": "2006.", "text": "<PERSON>, French race car driver (b. 1915)", "html": "2006. - 2006. - <a href=\"https://wikipedia.org/wiki/Eug%C3%A<PERSON><PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French race car driver (b. 1915)", "no_year_html": "2006. - <a href=\"https://wikipedia.org/wiki/Eug%C3%A8<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French race car driver (b. 1915)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Eug%C3%A8ne_<PERSON>"}]}, {"year": "2006", "text": "<PERSON><PERSON>, Italian director and screenwriter (b. 1919)", "html": "2006 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Italian director and screenwriter (b. 1919)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Italian director and screenwriter (b. 1919)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_Pontecorvo"}]}, {"year": "2007", "text": "<PERSON><PERSON><PERSON>, Japanese architect, designed the Nakagin Capsule Tower (b. 1934)", "html": "2007 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese architect, designed the <a href=\"https://wikipedia.org/wiki/Nakagin_Capsule_Tower\" title=\"Nakagin Capsule Tower\">Nakagin Capsule Tower</a> (b. 1934)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese architect, designed the <a href=\"https://wikipedia.org/wiki/Nakagin_Capsule_Tower\" title=\"Nakagin Capsule Tower\">Nakagin Capsule Tower</a> (b. 1934)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>awa"}, {"title": "Nakagin Capsule Tower", "link": "https://wikipedia.org/wiki/Nakagin_Capsule_Tower"}]}, {"year": "2008", "text": "<PERSON>, Maltese physician and politician (b. 1965)", "html": "2008 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Maltese physician and politician (b. 1965)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Maltese physician and politician (b. 1965)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2009", "text": "<PERSON><PERSON> singer-songwriter and bass player (b. 1948)", "html": "2009 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a> American singer-songwriter and bass player (b. 1948)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a> American singer-songwriter and bass player (b. 1948)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "2009", "text": "<PERSON>, Belgian cyclist (b. 1974)", "html": "2009 - <a href=\"https://wikipedia.org/wiki/<PERSON>_(cyclist)\" title=\"<PERSON> (cyclist)\"><PERSON></a>, Belgian cyclist (b. 1974)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(cyclist)\" title=\"<PERSON> (cyclist)\"><PERSON></a>, Belgian cyclist (b. 1974)", "links": [{"title": "<PERSON> (cyclist)", "link": "https://wikipedia.org/wiki/<PERSON>_(cyclist)"}]}, {"year": "2010", "text": "<PERSON>, Northern Irish soldier and politician (b. 1917)", "html": "2010 - <a href=\"https://wikipedia.org/wiki/Austin_Ardill\" title=\"Austin Ardill\"><PERSON></a>, Northern Irish soldier and politician (b. 1917)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Austin_Ardill\" title=\"<PERSON> Ardill\"><PERSON></a>, Northern Irish soldier and politician (b. 1917)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Austin_<PERSON>ll"}]}, {"year": "2010", "text": "<PERSON>, American football player (b. 1943)", "html": "2010 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player (b. 1943)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Peoples\"><PERSON></a>, American football player (b. 1943)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2010", "text": "<PERSON><PERSON>, American author (b. 1919)", "html": "2010 - <a href=\"https://wikipedia.org/wiki/Belva_Plain\" title=\"Belva Plain\">Belva Plain</a>, American author (b. 1919)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Belva_Plain\" title=\"Belva Plain\">Belva Plain</a>, American author (b. 1919)", "links": [{"title": "Belva Plain", "link": "https://wikipedia.org/wiki/Belva_Plain"}]}, {"year": "2011", "text": "<PERSON>, American actress (b. 1931)", "html": "2011 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress (b. 1931)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress (b. 1931)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2011", "text": "<PERSON>, American computer scientist, created the C programming language (b. 1941)", "html": "2011 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American computer scientist, created the <a href=\"https://wikipedia.org/wiki/C_(programming_language)\" title=\"C (programming language)\">C programming language</a> (b. 1941)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American computer scientist, created the <a href=\"https://wikipedia.org/wiki/C_(programming_language)\" title=\"C (programming language)\">C programming language</a> (b. 1941)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "C (programming language)", "link": "https://wikipedia.org/wiki/C_(programming_language)"}]}, {"year": "2012", "text": "<PERSON>, Canadian lawyer and banker, 2nd Governor of the Bank of Canada (b. 1910)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian lawyer and banker, 2nd <a href=\"https://wikipedia.org/wiki/Governor_of_the_Bank_of_Canada\" title=\"Governor of the Bank of Canada\">Governor of the Bank of Canada</a> (b. 1910)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian lawyer and banker, 2nd <a href=\"https://wikipedia.org/wiki/Governor_of_the_Bank_of_Canada\" title=\"Governor of the Bank of Canada\">Governor of the Bank of Canada</a> (b. 1910)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "Governor of the Bank of Canada", "link": "https://wikipedia.org/wiki/Governor_of_the_Bank_of_Canada"}]}, {"year": "2012", "text": "<PERSON><PERSON>, American hot rod builder and actor (b. 1933)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American hot rod builder and actor (b. 1933)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American hot rod builder and actor (b. 1933)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "2012", "text": "<PERSON><PERSON><PERSON><PERSON>, Indian judge and politician, 14th Governor of Kerala (b. 1931)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>de<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Indian judge and politician, 14th <a href=\"https://wikipedia.org/wiki/Governor_of_Kerala\" class=\"mw-redirect\" title=\"Governor of Kerala\">Governor of Kerala</a> (b. 1931)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>de<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Indian judge and politician, 14th <a href=\"https://wikipedia.org/wiki/Governor_of_Kerala\" class=\"mw-redirect\" title=\"Governor of Kerala\">Governor of Kerala</a> (b. 1931)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>_<PERSON>"}, {"title": "Governor of Kerala", "link": "https://wikipedia.org/wiki/Governor_of_Kerala"}]}, {"year": "2012", "text": "<PERSON><PERSON><PERSON>, Iraqi-Armenian patriarch (b. 1919)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/Torkom_Manoogian\" title=\"Torkom Manoogian\">Torkom Manoogian</a>, Iraqi-Armenian patriarch (b. 1919)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Torkom_Manoogian\" title=\"Torkom Manoogian\">Torkom Manoogian</a>, Iraqi-Armenian patriarch (b. 1919)", "links": [{"title": "Torkom Manoogian", "link": "https://wikipedia.org/wiki/Torkom_Manoogian"}]}, {"year": "2012", "text": "<PERSON>, Danish bassist, composer, and bandleader (b. 1930)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Danish bassist, composer, and bandleader (b. 1930)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Danish bassist, composer, and bandleader (b. 1930)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2012", "text": "<PERSON><PERSON><PERSON><PERSON>, Czech animator, director, and screenwriter (b. 1923)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/B%C5%99<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Czech animator, director, and screenwriter (b. 1923)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/B%C5%99<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Czech animator, director, and screenwriter (b. 1923)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/B%C5%99eti<PERSON>_<PERSON>"}]}, {"year": "2013", "text": "<PERSON>, American astronomer and academic (b. 1920)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American astronomer and academic (b. 1920)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American astronomer and academic (b. 1920)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2013", "text": "<PERSON>, American author and academic (b. 1951)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author and academic (b. 1951)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author and academic (b. 1951)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2013", "text": "<PERSON>, Norwegian diplomat (b. 1942)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Norwegian diplomat (b. 1942)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Norwegian diplomat (b. 1942)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2013", "text": "<PERSON>, American chemist and academic (b. 1910)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American chemist and academic (b. 1910)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American chemist and academic (b. 1910)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2014", "text": "<PERSON>, Kenyan-American political scientist, philosopher, and academic (b. 1933)", "html": "2014 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Kenyan-American political scientist, philosopher, and academic (b. 1933)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Kenyan-American political scientist, philosopher, and academic (b. 1933)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2014", "text": "<PERSON>, English snooker player (b. 1941)", "html": "2014 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English snooker player (b. 1941)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Miles\"><PERSON></a>, English snooker player (b. 1941)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2014", "text": "<PERSON>, Argentinian footballer and coach (b. 1943)", "html": "2014 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Argentinian footballer and coach (b. 1943)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Argentinian footballer and coach (b. 1943)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2015", "text": "<PERSON><PERSON><PERSON>, Tanzanian politician, 8th Tanzanian Minister of Industry and Trade (b. 1953)", "html": "2015 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Tanzanian politician, 8th <a href=\"https://wikipedia.org/wiki/Minister_of_Industry_and_Trade_(Tanzania)\" title=\"Minister of Industry and Trade (Tanzania)\">Tanzanian Minister of Industry and Trade</a> (b. 1953)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Tanzanian politician, 8th <a href=\"https://wikipedia.org/wiki/Minister_of_Industry_and_Trade_(Tanzania)\" title=\"Minister of Industry and Trade (Tanzania)\">Tanzanian Minister of Industry and Trade</a> (b. 1953)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}, {"title": "Minister of Industry and Trade (Tanzania)", "link": "https://wikipedia.org/wiki/Minister_of_Industry_and_Trade_(Tanzania)"}]}, {"year": "2015", "text": "<PERSON>, American actress, dancer, and vaudevillian (b. 1925)", "html": "2015 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress, dancer, and vaudevillian (b. 1925)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress, dancer, and vaudevillian (b. 1925)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2017", "text": "<PERSON><PERSON><PERSON>, Venezuelan journalist (b. 1938)", "html": "2017 - <a href=\"https://wikipedia.org/wiki/Margarita_D%27Amico\" title=\"Margarita D'Amico\"><PERSON><PERSON><PERSON>Am<PERSON></a>, Venezuelan journalist (b. 1938)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Margarita_D%27Amico\" title=\"Margarita D'Amico\"><PERSON><PERSON><PERSON>Am<PERSON></a>, Venezuelan journalist (b. 1938)", "links": [{"title": "Margarita D'Amico", "link": "https://wikipedia.org/wiki/Margarita_D%27Amico"}]}, {"year": "2020", "text": "<PERSON><PERSON><PERSON>, American actress (b. 1943)", "html": "2020 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American actress (b. 1943)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American actress (b. 1943)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>cha<PERSON>_<PERSON>"}]}, {"year": "2020", "text": "<PERSON>, American socialite and oil heiress (b. 1912)", "html": "2020 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American socialite and oil heiress (b. 1912)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American socialite and oil heiress (b. 1912)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2023", "text": "<PERSON>, Colombian serial killer (b. 1957)", "html": "2023 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Colombian serial killer (b. 1957)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Colombian serial killer (b. 1957)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2024", "text": "<PERSON><PERSON>, Scottish DJ and record producer (b. 1986)", "html": "2024 - <a href=\"https://wikipedia.org/wiki/Jack<PERSON>\" title=\"Jack<PERSON>\"><PERSON><PERSON></a>, Scottish DJ and record producer (b. 1986)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Scottish DJ and record producer (b. 1986)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>master"}]}, {"year": "2024", "text": "<PERSON>, American rapper (b. 1972)", "html": "2024 - <a href=\"https://wikipedia.org/wiki/<PERSON>_(rapper)\" title=\"<PERSON> (rapper)\"><PERSON></a>, American rapper (b. 1972)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(rapper)\" title=\"<PERSON> (rapper)\"><PERSON></a>, American rapper (b. 1972)", "links": [{"title": "<PERSON> (rapper)", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>(rapper)"}]}, {"year": "2024", "text": "<PERSON>, American activist (b. 1938)", "html": "2024 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American activist (b. 1938)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American activist (b. 1938)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2024", "text": "<PERSON>, South African politician (b. 1959)", "html": "2024 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, South African politician (b. 1959)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, South African politician (b. 1959)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2024", "text": "<PERSON>, Canadian film and television director (b. 1927)", "html": "2024 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian film and television director (b. 1927)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian film and television director (b. 1927)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2024", "text": "<PERSON>, Scottish economist and politician, First Minister of Scotland (b. 1954)", "html": "2024 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish economist and politician, <a href=\"https://wikipedia.org/wiki/First_Minister_of_Scotland\" title=\"First Minister of Scotland\">First Minister of Scotland</a> (b. 1954)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish economist and politician, <a href=\"https://wikipedia.org/wiki/First_Minister_of_Scotland\" title=\"First Minister of Scotland\">First Minister of Scotland</a> (b. 1954)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "First Minister of Scotland", "link": "https://wikipedia.org/wiki/First_Minister_of_Scotland"}]}, {"year": "2024", "text": "<PERSON>, Indian politician (b. 1958)", "html": "2024 - <a href=\"https://wikipedia.org/wiki/<PERSON>_Sid<PERSON>\" title=\"Baba Siddique\"><PERSON></a>, Indian politician (b. 1958)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_Sid<PERSON>que\" title=\"Baba Siddique\"><PERSON></a>, Indian politician (b. 1958)", "links": [{"title": "Baba Siddique", "link": "https://wikipedia.org/wiki/Baba_Siddique"}]}]}}