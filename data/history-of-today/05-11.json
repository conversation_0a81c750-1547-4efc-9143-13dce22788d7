{"date": "May 11", "url": "https://wikipedia.org/wiki/May_11", "data": {"Events": [{"year": "330", "text": "<PERSON> the Great dedicates the much-expanded and rebuilt city of Byzantium, changing its name to New Rome and declaring it the new capital of the Eastern Roman Empire.", "html": "330 - <a href=\"https://wikipedia.org/wiki/<PERSON>_the_Great\" title=\"<PERSON> the Great\"><PERSON> the Great</a> dedicates the much-expanded and rebuilt city of <a href=\"https://wikipedia.org/wiki/Byzantium\" title=\"Byzantium\">Byzantium</a>, changing its name to New Rome and declaring it the new capital of the Eastern Roman Empire.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_the_Great\" title=\"<PERSON> the Great\"><PERSON> the Great</a> dedicates the much-expanded and rebuilt city of <a href=\"https://wikipedia.org/wiki/Byzantium\" title=\"Byzantium\">Byzantium</a>, changing its name to New Rome and declaring it the new capital of the Eastern Roman Empire.", "links": [{"title": "<PERSON> the Great", "link": "https://wikipedia.org/wiki/<PERSON>_the_Great"}, {"title": "Byzantium", "link": "https://wikipedia.org/wiki/Byzantium"}]}, {"year": "868", "text": "A copy of the Diamond Sūtra is published,  making it the earliest dated and printed book known.", "html": "868 - A copy of the <a href=\"https://wikipedia.org/wiki/Diamond_S%C5%ABtra\" class=\"mw-redirect\" title=\"Diamond Sūtra\">Diamond Sūtra</a> is published, making it the earliest dated and printed book known.", "no_year_html": "A copy of the <a href=\"https://wikipedia.org/wiki/Diamond_S%C5%ABtra\" class=\"mw-redirect\" title=\"Diamond Sūtra\">Diamond Sūtra</a> is published, making it the earliest dated and printed book known.", "links": [{"title": "Diamond Sūtra", "link": "https://wikipedia.org/wiki/Diamond_S%C5%ABtra"}]}, {"year": "973", "text": "In the first coronation ceremony ever held for an English monarch, <PERSON> the <PERSON> is crowned King of England, having ruled since 959 AD. His wife, <PERSON><PERSON><PERSON><PERSON><PERSON>, is crowned queen, the first recorded coronation for a Queen of England.", "html": "973 - In the first coronation ceremony ever held for an English monarch, <a href=\"https://wikipedia.org/wiki/<PERSON>,_King_of_England\" title=\"<PERSON>, King of England\"><PERSON> the <PERSON>ful</a> is crowned <a href=\"https://wikipedia.org/wiki/List_of_English_monarchs\" title=\"List of English monarchs\">King of England</a>, having ruled since 959 AD. His wife, <a href=\"https://wikipedia.org/wiki/%C3%86lfthryth_(wife_of_<PERSON>)\" title=\"<PERSON><PERSON><PERSON><PERSON><PERSON> (wife of <PERSON>)\"><PERSON><PERSON><PERSON><PERSON><PERSON></a>, is crowned queen, the first recorded coronation for a Queen of England.", "no_year_html": "In the first coronation ceremony ever held for an English monarch, <a href=\"https://wikipedia.org/wiki/<PERSON>,_King_of_England\" title=\"<PERSON>, King of England\"><PERSON> the <PERSON>ful</a> is crowned <a href=\"https://wikipedia.org/wiki/List_of_English_monarchs\" title=\"List of English monarchs\">King of England</a>, having ruled since 959 AD. His wife, <a href=\"https://wikipedia.org/wiki/%C3%86lfthryth_(wife_of_<PERSON>)\" title=\"<PERSON><PERSON><PERSON><PERSON><PERSON> (wife of <PERSON>)\"><PERSON><PERSON><PERSON><PERSON><PERSON></a>, is crowned queen, the first recorded coronation for a Queen of England.", "links": [{"title": "<PERSON>, King of England", "link": "https://wikipedia.org/wiki/<PERSON>,_King_of_England"}, {"title": "List of English monarchs", "link": "https://wikipedia.org/wiki/List_of_English_monarchs"}, {"title": "<PERSON><PERSON><PERSON><PERSON><PERSON> (wife of <PERSON>)", "link": "https://wikipedia.org/wiki/%C3%86lfthryth_(wife_of_<PERSON>)"}]}, {"year": "1068", "text": "<PERSON> of Flanders, wife of <PERSON> the Conqueror, is crowned Queen of England.", "html": "1068 - <a href=\"https://wikipedia.org/wiki/Matilda_of_Flanders\" title=\"Matilda of Flanders\">Matilda of Flanders</a>, wife of <a href=\"https://wikipedia.org/wiki/<PERSON>_the_Conqueror\" title=\"<PERSON> the Conqueror\"><PERSON> the Conqueror</a>, is crowned <a href=\"https://wikipedia.org/wiki/List_of_English_royal_consorts\" title=\"List of English royal consorts\">Queen of England</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Matilda_of_Flanders\" title=\"Matilda of Flanders\">Matilda of Flanders</a>, wife of <a href=\"https://wikipedia.org/wiki/<PERSON>_the_Conqueror\" title=\"<PERSON> the Conqueror\"><PERSON> the Conqueror</a>, is crowned <a href=\"https://wikipedia.org/wiki/List_of_English_royal_consorts\" title=\"List of English royal consorts\">Queen of England</a>.", "links": [{"title": "<PERSON> of Flanders", "link": "https://wikipedia.org/wiki/Matilda_of_Flanders"}, {"title": "<PERSON> the Conqueror", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "List of English royal consorts", "link": "https://wikipedia.org/wiki/List_of_English_royal_consorts"}]}, {"year": "1258", "text": "<PERSON> of France and <PERSON> of <PERSON> sign the Treaty of Corbeil, renouncing claims of feudal overlordship in one another's territories and separating the House of Barcelona from the politics of France.", "html": "1258 - <a href=\"https://wikipedia.org/wiki/Louis_IX_of_France\" title=\"<PERSON> IX of France\"><PERSON> of France</a> and <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_Aragon\" title=\"<PERSON> of Aragon\"><PERSON> of Aragon</a> sign the <a href=\"https://wikipedia.org/wiki/Treaty_of_Corbeil_(1258)\" title=\"Treaty of Corbeil (1258)\">Treaty of Corbeil</a>, renouncing claims of feudal overlordship in one another's territories and separating the <a href=\"https://wikipedia.org/wiki/House_of_Barcelona\" title=\"House of Barcelona\">House of Barcelona</a> from the politics of France.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Louis_IX_of_France\" title=\"<PERSON> IX of France\"><PERSON> of France</a> and <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_Aragon\" title=\"<PERSON> of Aragon\"><PERSON> of Aragon</a> sign the <a href=\"https://wikipedia.org/wiki/Treaty_of_Corbeil_(1258)\" title=\"Treaty of Corbeil (1258)\">Treaty of Corbeil</a>, renouncing claims of feudal overlordship in one another's territories and separating the <a href=\"https://wikipedia.org/wiki/House_of_Barcelona\" title=\"House of Barcelona\">House of Barcelona</a> from the politics of France.", "links": [{"title": "<PERSON> of France", "link": "https://wikipedia.org/wiki/Louis_IX_of_France"}, {"title": "<PERSON> of Aragon", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_Aragon"}, {"title": "Treaty of Corbeil (1258)", "link": "https://wikipedia.org/wiki/Treaty_of_Corbeil_(1258)"}, {"title": "House of Barcelona", "link": "https://wikipedia.org/wiki/House_of_Barcelona"}]}, {"year": "1713", "text": "Great Northern War: After losing the Battle of Helsinki to the Russians, the Swedish and Finnish troops burn the entire city, so that it would not remain intact in the hands of the Russians.", "html": "1713 - <a href=\"https://wikipedia.org/wiki/Great_Northern_War\" title=\"Great Northern War\">Great Northern War</a>: After losing the <a href=\"https://wikipedia.org/wiki/Battle_of_Helsinki_(1713)\" title=\"Battle of Helsinki (1713)\">Battle of Helsinki</a> to the Russians, the Swedish and Finnish troops burn the entire city, so that it would not remain intact in the hands of the Russians.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Great_Northern_War\" title=\"Great Northern War\">Great Northern War</a>: After losing the <a href=\"https://wikipedia.org/wiki/Battle_of_Helsinki_(1713)\" title=\"Battle of Helsinki (1713)\">Battle of Helsinki</a> to the Russians, the Swedish and Finnish troops burn the entire city, so that it would not remain intact in the hands of the Russians.", "links": [{"title": "Great Northern War", "link": "https://wikipedia.org/wiki/Great_Northern_War"}, {"title": "Battle of Helsinki (1713)", "link": "https://wikipedia.org/wiki/Battle_of_Helsinki_(1713)"}]}, {"year": "1812", "text": "Prime Minister <PERSON> is assassinated by <PERSON> in the lobby of the British House of Commons.", "html": "1812 - Prime Minister <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> is <a href=\"https://wikipedia.org/wiki/Assassination_of_<PERSON>_<PERSON>\" title=\"Assassination of <PERSON>\">assassinated</a> by <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> in the lobby of the British House of Commons.", "no_year_html": "Prime Minister <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> is <a href=\"https://wikipedia.org/wiki/Assassination_of_<PERSON>_<PERSON>\" title=\"Assassination of <PERSON>\">assassinated</a> by <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> in the lobby of the British House of Commons.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Assassination of <PERSON>", "link": "https://wikipedia.org/wiki/Assassination_of_<PERSON>_<PERSON>"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1813", "text": "<PERSON>, <PERSON> and <PERSON> discover a route across the Blue Mountains, opening up inland Australia to settlement.", "html": "1813 - <a href=\"https://wikipedia.org/wiki/<PERSON>(explorer)\" title=\"<PERSON> (explorer)\"><PERSON></a>, <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> and <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> discover a <a href=\"https://wikipedia.org/wiki/1813_crossing_of_the_Blue_Mountains\" title=\"1813 crossing of the Blue Mountains\">route across the Blue Mountains</a>, opening up inland Australia to settlement.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>(explorer)\" title=\"<PERSON> (explorer)\"><PERSON></a>, <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> and <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> discover a <a href=\"https://wikipedia.org/wiki/1813_crossing_of_the_Blue_Mountains\" title=\"1813 crossing of the Blue Mountains\">route across the Blue Mountains</a>, opening up inland Australia to settlement.", "links": [{"title": "<PERSON> (explorer)", "link": "https://wikipedia.org/wiki/<PERSON>_(explorer)"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "1813 crossing of the Blue Mountains", "link": "https://wikipedia.org/wiki/1813_crossing_of_the_Blue_Mountains"}]}, {"year": "1857", "text": "Indian Rebellion of 1857: Indian rebels seize Delhi from the British.", "html": "1857 - <a href=\"https://wikipedia.org/wiki/Indian_Rebellion_of_1857\" title=\"Indian Rebellion of 1857\">Indian Rebellion of 1857</a>: Indian rebels seize Delhi from the British.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Indian_Rebellion_of_1857\" title=\"Indian Rebellion of 1857\">Indian Rebellion of 1857</a>: Indian rebels seize Delhi from the British.", "links": [{"title": "Indian Rebellion of 1857", "link": "https://wikipedia.org/wiki/Indian_Rebellion_of_1857"}]}, {"year": "1880", "text": "Seven people are killed in the Mussel Slough Tragedy, a gun battle in California.", "html": "1880 - Seven people are killed in the <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_Slough_Tragedy\" title=\"<PERSON>ssel Slough Tragedy\">Mu<PERSON> Slough Tragedy</a>, a gun battle in California.", "no_year_html": "Seven people are killed in the <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_Slough_Tragedy\" title=\"<PERSON><PERSON> Slough Tragedy\">Mu<PERSON> Slough Tragedy</a>, a gun battle in California.", "links": [{"title": "<PERSON><PERSON> Tragedy", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_Tragedy"}]}, {"year": "1889", "text": "An attack upon a U.S. Army paymaster and escort results in the theft of over $28,000 and the award of two Medals of Honor.", "html": "1889 - An <a href=\"https://wikipedia.org/wiki/Wham_Paymaster_robbery\" title=\"Wham Paymaster robbery\">attack upon a U.S. Army paymaster and escort</a> results in the theft of over $28,000 and the award of two <a href=\"https://wikipedia.org/wiki/Medal_of_Honor\" title=\"Medal of Honor\">Medals of Honor</a>.", "no_year_html": "An <a href=\"https://wikipedia.org/wiki/Wham_Paymaster_robbery\" title=\"Wham Paymaster robbery\">attack upon a U.S. Army paymaster and escort</a> results in the theft of over $28,000 and the award of two <a href=\"https://wikipedia.org/wiki/Medal_of_Honor\" title=\"Medal of Honor\">Medals of Honor</a>.", "links": [{"title": "Wham Paymaster robbery", "link": "https://wikipedia.org/wiki/Wham_Paymaster_robbery"}, {"title": "Medal of Honor", "link": "https://wikipedia.org/wiki/Medal_of_Honor"}]}, {"year": "1894", "text": "Four thousand Pullman Palace Car Company workers go on a wildcat strike.", "html": "1894 - Four thousand Pullman Palace Car Company workers go on a <a href=\"https://wikipedia.org/wiki/Pullman_Strike\" title=\"Pullman Strike\">wildcat strike</a>.", "no_year_html": "Four thousand Pullman Palace Car Company workers go on a <a href=\"https://wikipedia.org/wiki/Pullman_Strike\" title=\"Pullman Strike\">wildcat strike</a>.", "links": [{"title": "Pullman Strike", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_Strike"}]}, {"year": "1919", "text": "Uruguay becomes a signatory to the Buenos Aires copyright treaty.", "html": "1919 - <a href=\"https://wikipedia.org/wiki/Uruguay\" title=\"Uruguay\">Uruguay</a> becomes a signatory to the <a href=\"https://wikipedia.org/wiki/Buenos_Aires_Convention\" title=\"Buenos Aires Convention\">Buenos Aires</a> <a href=\"https://wikipedia.org/wiki/Copyright\" title=\"Copyright\">copyright</a> <a href=\"https://wikipedia.org/wiki/Treaty\" title=\"Treaty\">treaty</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Uruguay\" title=\"Uruguay\">Uruguay</a> becomes a signatory to the <a href=\"https://wikipedia.org/wiki/Buenos_Aires_Convention\" title=\"Buenos Aires Convention\">Buenos Aires</a> <a href=\"https://wikipedia.org/wiki/Copyright\" title=\"Copyright\">copyright</a> <a href=\"https://wikipedia.org/wiki/Treaty\" title=\"Treaty\">treaty</a>.", "links": [{"title": "Uruguay", "link": "https://wikipedia.org/wiki/Uruguay"}, {"title": "Buenos Aires Convention", "link": "https://wikipedia.org/wiki/Buenos_Aires_Convention"}, {"title": "Copyright", "link": "https://wikipedia.org/wiki/Copyright"}, {"title": "Treaty", "link": "https://wikipedia.org/wiki/Treaty"}]}, {"year": "1970", "text": "The 1970 Lubbock tornado kills 26 and causes $250 million in damage.", "html": "1970 - The <a href=\"https://wikipedia.org/wiki/Lubbock_tornado\" title=\"Lubbock tornado\">1970 Lubbock tornado</a> kills 26 and causes $250 million in damage.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Lubbock_tornado\" title=\"Lubbock tornado\">1970 Lubbock tornado</a> kills 26 and causes $250 million in damage.", "links": [{"title": "Lubbock tornado", "link": "https://wikipedia.org/wiki/Lubbock_tornado"}]}, {"year": "1973", "text": "Citing government misconduct, <PERSON>'s charges for his involvement in releasing the Pentagon Papers to The New York Times are dismissed.", "html": "1973 - Citing government misconduct, <PERSON>'s charges for his involvement in releasing the <i><a href=\"https://wikipedia.org/wiki/Pentagon_Papers\" title=\"Pentagon Papers\">Pentagon Papers</a></i> to <i>The New York Times</i> are dismissed.", "no_year_html": "Citing government misconduct, <PERSON>'s charges for his involvement in releasing the <i><a href=\"https://wikipedia.org/wiki/Pentagon_Papers\" title=\"Pentagon Papers\">Pentagon Papers</a></i> to <i>The New York Times</i> are dismissed.", "links": [{"title": "Pentagon Papers", "link": "https://wikipedia.org/wiki/Pentagon_Papers"}]}, {"year": "1973", "text": "Aeroflot Flight 6551 crashes in Semey, Kazakh Soviet Socialist Republic (now Kazakhstan), killing all 63 aboard.", "html": "1973 - <a href=\"https://wikipedia.org/wiki/Aeroflot_Flight_6551\" title=\"Aeroflot Flight 6551\">Aeroflot Flight 6551</a> crashes in <a href=\"https://wikipedia.org/wiki/Semey\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, <a href=\"https://wikipedia.org/wiki/Kazakh_Soviet_Socialist_Republic\" title=\"Kazakh Soviet Socialist Republic\">Kazakh Soviet Socialist Republic</a> (now <a href=\"https://wikipedia.org/wiki/Kazakhstan\" title=\"Kazakhstan\">Kazakhstan</a>), killing all 63 aboard.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Aeroflot_Flight_6551\" title=\"Aeroflot Flight 6551\">Aeroflot Flight 6551</a> crashes in <a href=\"https://wikipedia.org/wiki/Semey\" title=\"Seme<PERSON>\"><PERSON><PERSON><PERSON></a>, <a href=\"https://wikipedia.org/wiki/Kazakh_Soviet_Socialist_Republic\" title=\"Kazakh Soviet Socialist Republic\">Kazakh Soviet Socialist Republic</a> (now <a href=\"https://wikipedia.org/wiki/Kazakhstan\" title=\"Kazakhstan\">Kazakhstan</a>), killing all 63 aboard.", "links": [{"title": "Aeroflot Flight 6551", "link": "https://wikipedia.org/wiki/Aeroflot_Flight_6551"}, {"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Semey"}, {"title": "Kazakh Soviet Socialist Republic", "link": "https://wikipedia.org/wiki/Kazakh_Soviet_Socialist_Republic"}, {"title": "Kazakhstan", "link": "https://wikipedia.org/wiki/Kazakhstan"}]}, {"year": "1985", "text": "Fifty-six spectators die and more than 200 are injured in the Bradford City stadium fire.", "html": "1985 - Fifty-six spectators die and more than 200 are injured in the <a href=\"https://wikipedia.org/wiki/Bradford_City_stadium_fire\" title=\"Bradford City stadium fire\">Bradford City stadium fire</a>.", "no_year_html": "Fifty-six spectators die and more than 200 are injured in the <a href=\"https://wikipedia.org/wiki/Bradford_City_stadium_fire\" title=\"Bradford City stadium fire\">Bradford City stadium fire</a>.", "links": [{"title": "Bradford City stadium fire", "link": "https://wikipedia.org/wiki/Bradford_City_stadium_fire"}]}, {"year": "1987", "text": "<PERSON> goes on trial in Lyon for war crimes committed during World War II.", "html": "1987 - <a href=\"https://wikipedia.org/wiki/<PERSON>_Barbie\" title=\"Klaus Barbie\"><PERSON></a> goes on trial in <a href=\"https://wikipedia.org/wiki/Lyon\" title=\"Lyon\">Lyon</a> for war crimes committed during <a href=\"https://wikipedia.org/wiki/World_War_II\" title=\"World War II\">World War II</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_Barbie\" title=\"Klaus Barbie\"><PERSON></a> goes on trial in <a href=\"https://wikipedia.org/wiki/Lyon\" title=\"Lyon\">Lyon</a> for war crimes committed during <a href=\"https://wikipedia.org/wiki/World_War_II\" title=\"World War II\">World War II</a>.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Lyon", "link": "https://wikipedia.org/wiki/Lyon"}, {"title": "World War II", "link": "https://wikipedia.org/wiki/World_War_II"}]}, {"year": "1996", "text": "After the aircraft's departure from Miami, a fire started by improperly handled chemical oxygen generators in the cargo hold of Atlanta-bound ValuJet Airlines Flight 592 causes the Douglas DC-9 to crash in the Florida Everglades, killing all 110 on board.", "html": "1996 - After the aircraft's departure from Miami, a fire started by improperly handled chemical oxygen generators in the cargo hold of Atlanta-bound <a href=\"https://wikipedia.org/wiki/ValuJet_Airlines_Flight_592\" class=\"mw-redirect\" title=\"ValuJet Airlines Flight 592\">ValuJet Airlines Flight 592</a> causes the Douglas DC-9 to crash in the Florida Everglades, killing all 110 on board.", "no_year_html": "After the aircraft's departure from Miami, a fire started by improperly handled chemical oxygen generators in the cargo hold of Atlanta-bound <a href=\"https://wikipedia.org/wiki/ValuJet_Airlines_Flight_592\" class=\"mw-redirect\" title=\"ValuJet Airlines Flight 592\">ValuJet Airlines Flight 592</a> causes the Douglas DC-9 to crash in the Florida Everglades, killing all 110 on board.", "links": [{"title": "ValuJet Airlines Flight 592", "link": "https://wikipedia.org/wiki/ValuJet_Airlines_Flight_592"}]}, {"year": "1997", "text": "<PERSON>, a chess-playing supercomputer, defeats <PERSON> in the last game of the rematch, becoming the first computer to beat a world-champion chess player in a classic match format.", "html": "1997 - <a href=\"https://wikipedia.org/wiki/Deep_Blue_(chess_computer)\" title=\"Deep Blue (chess computer)\">Deep Blue</a>, a chess-playing supercomputer, defeats <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> in the <a href=\"https://wikipedia.org/wiki/Deep_Blue_versus_<PERSON>,_1997,_Game_6\" title=\"Deep Blue versus <PERSON>sp<PERSON><PERSON>, 1997, Game 6\">last game</a> of the rematch, becoming the first computer to beat a world-champion chess player in a classic match format.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Deep_Blue_(chess_computer)\" title=\"Deep Blue (chess computer)\">Deep Blue</a>, a chess-playing supercomputer, defeats <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> in the <a href=\"https://wikipedia.org/wiki/Deep_Blue_versus_<PERSON>,_1997,_Game_6\" title=\"Deep Blue versus <PERSON>sp<PERSON><PERSON>, 1997, Game 6\">last game</a> of the rematch, becoming the first computer to beat a world-champion chess player in a classic match format.", "links": [{"title": "<PERSON> Blue (chess computer)", "link": "https://wikipedia.org/wiki/Deep_Blue_(chess_computer)"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Deep Blue versus <PERSON><PERSON><PERSON><PERSON>, 1997, Game 6", "link": "https://wikipedia.org/wiki/Deep_Blue_versus_<PERSON><PERSON><PERSON><PERSON>,_1997,_Game_6"}]}, {"year": "1998", "text": "India conducts three underground atomic tests in Pokhran.", "html": "1998 - India conducts <a href=\"https://wikipedia.org/wiki/Pokhran-II\" title=\"Pokhran-II\">three underground atomic tests</a> in Pokhran.", "no_year_html": "India conducts <a href=\"https://wikipedia.org/wiki/Pokhran-II\" title=\"Pokhran-II\">three underground atomic tests</a> in Pokhran.", "links": [{"title": "Pokhran-II", "link": "https://wikipedia.org/wiki/Pokhran-II"}]}, {"year": "2000", "text": "Second Chechen War: Chechen separatists ambush Russian paramilitary forces in the Republic of Ingushetia.", "html": "2000 - <a href=\"https://wikipedia.org/wiki/Second_Chechen_War\" title=\"Second Chechen War\">Second Chechen War</a>: Chechen separatists <a href=\"https://wikipedia.org/wiki/Galashki_ambush\" title=\"Galashki ambush\">ambush</a> Russian paramilitary forces in the <a href=\"https://wikipedia.org/wiki/Republic_of_Ingushetia\" class=\"mw-redirect\" title=\"Republic of Ingushetia\">Republic of Ingushetia</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Second_Chechen_War\" title=\"Second Chechen War\">Second Chechen War</a>: Chechen separatists <a href=\"https://wikipedia.org/wiki/Galashki_ambush\" title=\"Galashki ambush\">ambush</a> Russian paramilitary forces in the <a href=\"https://wikipedia.org/wiki/Republic_of_Ingushetia\" class=\"mw-redirect\" title=\"Republic of Ingushetia\">Republic of Ingushetia</a>.", "links": [{"title": "Second Chechen War", "link": "https://wikipedia.org/wiki/Second_Chechen_War"}, {"title": "Galashki ambush", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_ambush"}, {"title": "Republic of Ingushetia", "link": "https://wikipedia.org/wiki/Republic_of_Ingushetia"}]}, {"year": "2009", "text": "An American soldier in Iraq opens fire on a counseling center at Camp Liberty in Baghdad, killing five other US soldiers and wounding three.", "html": "2009 - An American soldier in <a href=\"https://wikipedia.org/wiki/Iraq\" title=\"Iraq\">Iraq</a> <a href=\"https://wikipedia.org/wiki/Camp_Liberty_shooting\" title=\"Camp Liberty shooting\">opens fire</a> on a counseling center at <a href=\"https://wikipedia.org/wiki/Camp_Liberty\" title=\"Camp Liberty\">Camp Liberty</a> in <a href=\"https://wikipedia.org/wiki/Baghdad\" title=\"Baghdad\">Baghdad</a>, killing five other US soldiers and wounding three.", "no_year_html": "An American soldier in <a href=\"https://wikipedia.org/wiki/Iraq\" title=\"Iraq\">Iraq</a> <a href=\"https://wikipedia.org/wiki/Camp_Liberty_shooting\" title=\"Camp Liberty shooting\">opens fire</a> on a counseling center at <a href=\"https://wikipedia.org/wiki/Camp_Liberty\" title=\"Camp Liberty\">Camp Liberty</a> in <a href=\"https://wikipedia.org/wiki/Baghdad\" title=\"Baghdad\">Baghdad</a>, killing five other US soldiers and wounding three.", "links": [{"title": "Iraq", "link": "https://wikipedia.org/wiki/Iraq"}, {"title": "Camp Liberty shooting", "link": "https://wikipedia.org/wiki/Camp_Liberty_shooting"}, {"title": "Camp Liberty", "link": "https://wikipedia.org/wiki/Camp_Liberty"}, {"title": "Baghdad", "link": "https://wikipedia.org/wiki/Baghdad"}]}, {"year": "2009", "text": "Space Shuttle Atlantis is launched on the final mission to service the Hubble Space Telescope.", "html": "2009 - <a href=\"https://wikipedia.org/wiki/Space_Shuttle_Atlantis\" title=\"Space Shuttle Atlantis\">Space Shuttle <i>Atlantis</i></a> is launched on the <a href=\"https://wikipedia.org/wiki/STS-125\" title=\"STS-125\">final mission</a> to service the <a href=\"https://wikipedia.org/wiki/Hubble_Space_Telescope\" title=\"Hubble Space Telescope\">Hubble Space Telescope</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Space_Shuttle_Atlantis\" title=\"Space Shuttle Atlantis\">Space Shuttle <i>Atlantis</i></a> is launched on the <a href=\"https://wikipedia.org/wiki/STS-125\" title=\"STS-125\">final mission</a> to service the <a href=\"https://wikipedia.org/wiki/Hubble_Space_Telescope\" title=\"Hubble Space Telescope\">Hubble Space Telescope</a>.", "links": [{"title": "Space Shuttle Atlantis", "link": "https://wikipedia.org/wiki/Space_Shuttle_Atlantis"}, {"title": "STS-125", "link": "https://wikipedia.org/wiki/STS-125"}, {"title": "<PERSON>bble Space Telescope", "link": "https://wikipedia.org/wiki/Hubble_Space_Telescope"}]}, {"year": "2010", "text": "<PERSON> takes office as Prime Minister of the United Kingdom as the Conservatives and Liberal Democrats form the country's first coalition government since the Second World War.", "html": "2010 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> takes office as <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_the_United_Kingdom\" title=\"Prime Minister of the United Kingdom\">Prime Minister of the United Kingdom</a> as the <a href=\"https://wikipedia.org/wiki/Conservative_Party_(UK)\" title=\"Conservative Party (UK)\">Conservatives</a> and <a href=\"https://wikipedia.org/wiki/Liberal_Democrats_(UK)\" title=\"Liberal Democrats (UK)\">Liberal Democrats</a> form <a href=\"https://wikipedia.org/wiki/Cameron%E2%80%93Clegg_coalition\" title=\"Cameron-Clegg coalition\">the country's first coalition government since the Second World War</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> takes office as <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_the_United_Kingdom\" title=\"Prime Minister of the United Kingdom\">Prime Minister of the United Kingdom</a> as the <a href=\"https://wikipedia.org/wiki/Conservative_Party_(UK)\" title=\"Conservative Party (UK)\">Conservatives</a> and <a href=\"https://wikipedia.org/wiki/Liberal_Democrats_(UK)\" title=\"Liberal Democrats (UK)\">Liberal Democrats</a> form <a href=\"https://wikipedia.org/wiki/Cameron%E2%80%93Clegg_coalition\" title=\"Cameron-Clegg coalition\">the country's first coalition government since the Second World War</a>.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Prime Minister of the United Kingdom", "link": "https://wikipedia.org/wiki/Prime_Minister_of_the_United_Kingdom"}, {"title": "Conservative Party (UK)", "link": "https://wikipedia.org/wiki/Conservative_Party_(UK)"}, {"title": "Liberal Democrats (UK)", "link": "https://wikipedia.org/wiki/Liberal_Democrats_(UK)"}, {"title": "Cameron-Clegg coalition", "link": "https://wikipedia.org/wiki/Cameron%E2%80%93Clegg_coalition"}]}, {"year": "2011", "text": "An earthquake of magnitude 5.1 hits Lorca, Spain.", "html": "2011 - An <a href=\"https://wikipedia.org/wiki/2011_Lorca_earthquake\" title=\"2011 Lorca earthquake\">earthquake of magnitude 5.1</a> hits <a href=\"https://wikipedia.org/wiki/Lorca,_Spain\" title=\"Lorca, Spain\">Lorca, Spain</a>.", "no_year_html": "An <a href=\"https://wikipedia.org/wiki/2011_Lorca_earthquake\" title=\"2011 Lorca earthquake\">earthquake of magnitude 5.1</a> hits <a href=\"https://wikipedia.org/wiki/Lorca,_Spain\" title=\"Lorca, Spain\">Lorca, Spain</a>.", "links": [{"title": "2011 Lorca earthquake", "link": "https://wikipedia.org/wiki/2011_Lorca_earthquake"}, {"title": "Lorca, Spain", "link": "https://wikipedia.org/wiki/Lorca,_Spain"}]}, {"year": "2011", "text": "The Istanbul Convention is signed in Istanbul, Turkey.", "html": "2011 - The <a href=\"https://wikipedia.org/wiki/Istanbul_Convention\" title=\"Istanbul Convention\">Istanbul Convention</a> is signed in <a href=\"https://wikipedia.org/wiki/Istanbul\" title=\"Istanbul\">Istanbul</a>, Turkey.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Istanbul_Convention\" title=\"Istanbul Convention\">Istanbul Convention</a> is signed in <a href=\"https://wikipedia.org/wiki/Istanbul\" title=\"Istanbul\">Istanbul</a>, Turkey.", "links": [{"title": "Istanbul Convention", "link": "https://wikipedia.org/wiki/Istanbul_Convention"}, {"title": "Istanbul", "link": "https://wikipedia.org/wiki/Istanbul"}]}, {"year": "2013", "text": "Fifty-two people are killed in a bombing in Reyhanlı, Turkey.", "html": "2013 - Fifty-two people are killed in a <a href=\"https://wikipedia.org/wiki/2013_Reyhanl%C4%B1_car_bombings\" title=\"2013 Reyhanlı car bombings\">bombing in Reyhanlı, Turkey</a>.", "no_year_html": "Fifty-two people are killed in a <a href=\"https://wikipedia.org/wiki/2013_Reyhanl%C4%B1_car_bombings\" title=\"2013 Reyhanlı car bombings\">bombing in Reyhanlı, Turkey</a>.", "links": [{"title": "2013 Reyhanlı car bombings", "link": "https://wikipedia.org/wiki/2013_Reyhanl%C4%B1_car_bombings"}]}, {"year": "2014", "text": "Fifteen people are killed and 46 injured in Kinshasa, DRC, in a stampede caused by tear gas being thrown into soccer stands by police officers.", "html": "2014 - Fifteen people are killed and 46 injured in <a href=\"https://wikipedia.org/wiki/Kinshasa\" title=\"Kinshasa\">Kinshasa</a>, <a href=\"https://wikipedia.org/wiki/Democratic_Republic_of_the_Congo\" title=\"Democratic Republic of the Congo\">DRC</a>, in a <a href=\"https://wikipedia.org/wiki/2014_Stade_Tata_Rapha%C3%ABl_disaster\" title=\"2014 Stade Tata Raphaël disaster\">stampede</a> caused by tear gas being thrown into soccer stands by police officers.", "no_year_html": "Fifteen people are killed and 46 injured in <a href=\"https://wikipedia.org/wiki/Kinshasa\" title=\"Kinshasa\">Kinshasa</a>, <a href=\"https://wikipedia.org/wiki/Democratic_Republic_of_the_Congo\" title=\"Democratic Republic of the Congo\">DRC</a>, in a <a href=\"https://wikipedia.org/wiki/2014_Stade_Tata_Rapha%C3%ABl_disaster\" title=\"2014 Stade Tata Raphaël disaster\">stampede</a> caused by tear gas being thrown into soccer stands by police officers.", "links": [{"title": "Kinshasa", "link": "https://wikipedia.org/wiki/Kinshasa"}, {"title": "Democratic Republic of the Congo", "link": "https://wikipedia.org/wiki/Democratic_Republic_of_the_Congo"}, {"title": "2014 Stade Tata Raphaël disaster", "link": "https://wikipedia.org/wiki/2014_Stade_Tata_Rapha%C3%ABl_disaster"}]}, {"year": "2016", "text": "One hundred and ten people are killed in an ISIL bombing in Baghdad.", "html": "2016 - One hundred and ten people are killed <a href=\"https://wikipedia.org/wiki/11_May_2016_Baghdad_bombing\" class=\"mw-redirect\" title=\"11 May 2016 Baghdad bombing\">in an ISIL bombing in Baghdad</a>.", "no_year_html": "One hundred and ten people are killed <a href=\"https://wikipedia.org/wiki/11_May_2016_Baghdad_bombing\" class=\"mw-redirect\" title=\"11 May 2016 Baghdad bombing\">in an ISIL bombing in Baghdad</a>.", "links": [{"title": "11 May 2016 Baghdad bombing", "link": "https://wikipedia.org/wiki/11_May_2016_Baghdad_bombing"}]}, {"year": "2022", "text": "The Burmese military executes at least 37 villagers during the Mon Taing Pin massacre in Sagaing, Myanmar.", "html": "2022 - The <a href=\"https://wikipedia.org/wiki/Tatmadaw\" title=\"Tatmadaw\">Burmese military</a> executes at least 37 villagers during the <a href=\"https://wikipedia.org/wiki/Mon_Taing_Pin_massacre\" title=\"Mon Taing Pin massacre\">Mon Taing Pin massacre</a> in <a href=\"https://wikipedia.org/wiki/Sagaing_Region\" title=\"Sagaing Region\">Sagaing</a>, <a href=\"https://wikipedia.org/wiki/Myanmar\" title=\"Myanmar\">Myanmar</a>.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Tatmadaw\" title=\"Tatmadaw\">Burmese military</a> executes at least 37 villagers during the <a href=\"https://wikipedia.org/wiki/Mon_Taing_Pin_massacre\" title=\"Mon Taing Pin massacre\">Mon Taing Pin massacre</a> in <a href=\"https://wikipedia.org/wiki/Sagaing_Region\" title=\"Sagaing Region\">Sagaing</a>, <a href=\"https://wikipedia.org/wiki/Myanmar\" title=\"Myanmar\">Myanmar</a>.", "links": [{"title": "Tatmadaw", "link": "https://wikipedia.org/wiki/Tatmadaw"}, {"title": "Mon Taing Pin massacre", "link": "https://wikipedia.org/wiki/Mon_Taing_Pin_massacre"}, {"title": "Sagaing Region", "link": "https://wikipedia.org/wiki/Sagaing_Region"}, {"title": "Myanmar", "link": "https://wikipedia.org/wiki/Myanmar"}]}, {"year": "2022", "text": "Palestinian-American journalist <PERSON><PERSON> is killed while covering a raid in Jenin. Israel eventually admitted and apologized for the murder, after initial denials.", "html": "2022 - Palestinian-American journalist <a href=\"https://wikipedia.org/wiki/<PERSON>en_<PERSON>\" title=\"<PERSON>en <PERSON>kleh\"><PERSON><PERSON></a> is <a href=\"https://wikipedia.org/wiki/Killing_of_<PERSON><PERSON>_<PERSON>\" title=\"Killing of <PERSON><PERSON>\">killed</a> while covering a raid in <a href=\"https://wikipedia.org/wiki/Jenin\" title=\"Jenin\">Jen<PERSON></a>. Israel eventually admitted and apologized for the murder, after initial denials.", "no_year_html": "Palestinian-American journalist <a href=\"https://wikipedia.org/wiki/<PERSON>en_<PERSON>\" title=\"Shireen Abu Akleh\"><PERSON><PERSON></a> is <a href=\"https://wikipedia.org/wiki/Killing_of_<PERSON><PERSON>_<PERSON>\" title=\"Killing of <PERSON><PERSON>\">killed</a> while covering a raid in <a href=\"https://wikipedia.org/wiki/Jenin\" title=\"Jen<PERSON>\"><PERSON><PERSON></a>. Israel eventually admitted and apologized for the murder, after initial denials.", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>"}, {"title": "Killing of <PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Killing_of_<PERSON><PERSON>_<PERSON>_<PERSON>"}, {"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Jenin"}]}, {"year": "2024", "text": "Start/Middle of the May 2024 Solar Storms, the most powerful set of Geomagnetic storms since the 2003 Halloween solar storms.", "html": "2024 - Start/Middle of the <a href=\"https://wikipedia.org/wiki/May_2024_solar_storms\" title=\"May 2024 solar storms\">May 2024 Solar Storms</a>, the most powerful set of Geomagnetic storms since the <a href=\"https://wikipedia.org/wiki/2003_Halloween_solar_storms\" title=\"2003 Halloween solar storms\">2003 Halloween solar storms</a>.", "no_year_html": "Start/Middle of the <a href=\"https://wikipedia.org/wiki/May_2024_solar_storms\" title=\"May 2024 solar storms\">May 2024 Solar Storms</a>, the most powerful set of Geomagnetic storms since the <a href=\"https://wikipedia.org/wiki/2003_Halloween_solar_storms\" title=\"2003 Halloween solar storms\">2003 Halloween solar storms</a>.", "links": [{"title": "May 2024 solar storms", "link": "https://wikipedia.org/wiki/May_2024_solar_storms"}, {"title": "2003 Halloween solar storms", "link": "https://wikipedia.org/wiki/2003_Halloween_solar_storms"}]}, {"year": "2024", "text": "The 68th edition of the Eurovision Song Contest is held in Malmö, Sweden. <PERSON><PERSON><PERSON> from Switzerland wins with their song \"The Code\", making them the contest's first non-binary winner.", "html": "2024 - The <a href=\"https://wikipedia.org/wiki/Eurovision_Song_Contest_2024\" title=\"Eurovision Song Contest 2024\">68th edition of the Eurovision Song Contest</a> is held in <a href=\"https://wikipedia.org/wiki/Malm%C3%B6\" title=\"Malmö\">Malmö</a>, Sweden. <a href=\"https://wikipedia.org/wiki/Nemo_(singer)\" title=\"<PERSON><PERSON><PERSON> (singer)\">Nemo</a> from <a href=\"https://wikipedia.org/wiki/Switzerland_in_the_Eurovision_Song_Contest_2024\" title=\"Switzerland in the Eurovision Song Contest 2024\">Switzerland</a> wins with their song <a href=\"https://wikipedia.org/wiki/The_Code_(Nemo_song)\" title=\"The Code (Nemo song)\">\"The Code\"</a>, making them the contest's first <a href=\"https://wikipedia.org/wiki/Non-binary_gender\" title=\"Non-binary gender\">non-binary</a> winner.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Eurovision_Song_Contest_2024\" title=\"Eurovision Song Contest 2024\">68th edition of the Eurovision Song Contest</a> is held in <a href=\"https://wikipedia.org/wiki/Malm%C3%B6\" title=\"Malmö\">Malmö</a>, Sweden. <a href=\"https://wikipedia.org/wiki/N<PERSON><PERSON>_(singer)\" title=\"<PERSON><PERSON><PERSON> (singer)\">Nemo</a> from <a href=\"https://wikipedia.org/wiki/Switzerland_in_the_Eurovision_Song_Contest_2024\" title=\"Switzerland in the Eurovision Song Contest 2024\">Switzerland</a> wins with their song <a href=\"https://wikipedia.org/wiki/The_Code_(Nemo_song)\" title=\"The Code (Nemo song)\">\"The Code\"</a>, making them the contest's first <a href=\"https://wikipedia.org/wiki/Non-binary_gender\" title=\"Non-binary gender\">non-binary</a> winner.", "links": [{"title": "Eurovision Song Contest 2024", "link": "https://wikipedia.org/wiki/Eurovision_Song_Contest_2024"}, {"title": "Malmö", "link": "https://wikipedia.org/wiki/Malm%C3%B6"}, {"title": "<PERSON><PERSON><PERSON> (singer)", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_(singer)"}, {"title": "Switzerland in the Eurovision Song Contest 2024", "link": "https://wikipedia.org/wiki/Switzerland_in_the_Eurovision_Song_Contest_2024"}, {"title": "The Code (<PERSON>emo song)", "link": "https://wikipedia.org/wiki/The_Code_(Nemo_song)"}, {"title": "Non-binary gender", "link": "https://wikipedia.org/wiki/Non-binary_gender"}]}], "Births": [{"year": "1571", "text": "<PERSON><PERSON>, Japanese daimyō (d. 1637)", "html": "1571 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>gashige\" title=\"<PERSON><PERSON> Nagashige\"><PERSON><PERSON></a>, Japanese daimyō (d. 1637)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>hige\" title=\"<PERSON><PERSON> Nagashige\"><PERSON><PERSON></a>, Japanese daimyō (d. 1637)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>gashige"}]}, {"year": "1715", "text": "<PERSON>, German organist (d. 1739)", "html": "1715 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON><PERSON></a>, German organist (d. 1739)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON><PERSON></a>, German organist (d. 1739)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1752", "text": "<PERSON>, German physician, physiologist, and anthropologist (d. 1840): 94 ", "html": "1752 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German physician, physiologist, and anthropologist (d. 1840)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German physician, physiologist, and anthropologist (d. 1840)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1797", "text": "<PERSON>, Mexican general and politician (d. 1867)", "html": "1797 - <a href=\"https://wikipedia.org/wiki/Jos%C3%A<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Mexican general and politician (d. 1867)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Jos%C3%A<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Mexican general and politician (d. 1867)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Jos%C3%A9_<PERSON>_<PERSON>"}]}, {"year": "1811", "text": "<PERSON><PERSON><PERSON>, Swiss politician (d. 1893)", "html": "1811 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Swiss politician (d. 1893)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Swiss politician (d. 1893)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>-<PERSON>"}]}, {"year": "1811", "text": "<PERSON> and <PERSON><PERSON>, Siamese-American showmen, the original conjoined twins (d. 1874)", "html": "1811 - <a href=\"https://wikipedia.org/wiki/<PERSON>_and_<PERSON><PERSON>_Bunker\" title=\"<PERSON> and <PERSON><PERSON> Bunker\"><PERSON> and <PERSON><PERSON>unker</a>, Siamese-American showmen, the original <a href=\"https://wikipedia.org/wiki/Conjoined_twins\" title=\"Conjoined twins\">conjoined twins</a> (d. 1874)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_and_<PERSON><PERSON>_Bunker\" title=\"<PERSON> and <PERSON>g Bunker\"><PERSON> and <PERSON><PERSON>unker</a>, Siamese-American showmen, the original <a href=\"https://wikipedia.org/wiki/Conjoined_twins\" title=\"Conjoined twins\">conjoined twins</a> (d. 1874)", "links": [{"title": "<PERSON> and <PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_and_<PERSON><PERSON>_Bunker"}, {"title": "Conjoined twins", "link": "https://wikipedia.org/wiki/Conjoined_twins"}]}, {"year": "1852", "text": "<PERSON>, American journalist and politician, 26th United States Vice President (d. 1918)", "html": "1852 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American journalist and politician, 26th <a href=\"https://wikipedia.org/wiki/United_States_Vice_President\" class=\"mw-redirect\" title=\"United States Vice President\">United States Vice President</a> (d. 1918)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American journalist and politician, 26th <a href=\"https://wikipedia.org/wiki/United_States_Vice_President\" class=\"mw-redirect\" title=\"United States Vice President\">United States Vice President</a> (d. 1918)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>"}, {"title": "United States Vice President", "link": "https://wikipedia.org/wiki/United_States_Vice_President"}]}, {"year": "1854", "text": "<PERSON>, Australian cricketer (d. 1932)", "html": "1854 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian cricketer (d. 1932)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian cricketer (d. 1932)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1869", "text": "<PERSON>, English tennis player (d. 1943)", "html": "1869 - <a href=\"https://wikipedia.org/wiki/<PERSON>_Warden\" title=\"Archibald Warden\"><PERSON></a>, English tennis player (d. 1943)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_Warden\" title=\"Archibald Warden\"><PERSON></a>, English tennis player (d. 1943)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1871", "text": "<PERSON>, American astronomer and author (d. 1943)", "html": "1871 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American astronomer and author (d. 1943)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American astronomer and author (d. 1943)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1875", "text": "<PERSON>, American pilot and screenwriter (d. 1912)", "html": "1875 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American pilot and screenwriter (d. 1912)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American pilot and screenwriter (d. 1912)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1881", "text": "<PERSON>, Spanish-Cuban baseball player and manager (d. 1964)", "html": "1881 - <a href=\"https://wikipedia.org/wiki/Al_Cabrera\" title=\"Al Cabrera\"><PERSON></a>, Spanish-Cuban baseball player and manager (d. 1964)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Al_Cabrera\" title=\"Al Cabrera\"><PERSON></a>, Spanish-Cuban baseball player and manager (d. 1964)", "links": [{"title": "Al Cabrera", "link": "https://wikipedia.org/wiki/Al_Cabrera"}]}, {"year": "1881", "text": "<PERSON>, Dutch composer and conductor (d. 1944)", "html": "1881 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch composer and conductor (d. 1944)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch composer and conductor (d. 1944)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1881", "text": "<PERSON>, Hungarian-American mathematician, physicist, and engineer (d. 1963)", "html": "1881 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>%C3%A1rm%C3%A1n\" title=\"<PERSON>\"><PERSON></a>, Hungarian-American mathematician, physicist, and engineer (d. 1963)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>%C3%A1rm%C3%A1n\" title=\"<PERSON>\"><PERSON></a>, Hungarian-American mathematician, physicist, and engineer (d. 1963)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>%C3%A1rm%C3%A1n"}]}, {"year": "1888", "text": "<PERSON>, Belarusian-American pianist and composer (d. 1989)", "html": "1888 - <a href=\"https://wikipedia.org/wiki/Irving_Berlin\" title=\"Irving Berlin\"><PERSON></a>, Belarusian-American pianist and composer (d. 1989)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Irving_Berlin\" title=\"Irving Berlin\"><PERSON></a>, Belarusian-American pianist and composer (d. 1989)", "links": [{"title": "Irving <PERSON>", "link": "https://wikipedia.org/wiki/Irving_Berlin"}]}, {"year": "1888", "text": "<PERSON>, American admiral (d. 1945)", "html": "1888 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American admiral (d. 1945)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American admiral (d. 1945)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1889", "text": "<PERSON>, British painter (d. 1946)", "html": "1889 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(artist)\" title=\"<PERSON> (artist)\"><PERSON></a>, British painter (d. 1946)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(artist)\" title=\"<PERSON> (artist)\"><PERSON></a>, British painter (d. 1946)", "links": [{"title": "<PERSON> (artist)", "link": "https://wikipedia.org/wiki/<PERSON>(artist)"}]}, {"year": "1890", "text": "<PERSON>, English-American sprinter (d. 1958)", "html": "1890 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English-American sprinter (d. 1958)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English-American sprinter (d. 1958)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1890", "text": "<PERSON><PERSON>, Norwegian decathlete (d. 1984)", "html": "1890 - <a href=\"https://wikipedia.org/wiki/He<PERSON>_L%C3%B8vland\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Norwegian decathlete (d. 1984)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/He<PERSON>_L%C3%B8vland\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Norwegian decathlete (d. 1984)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Helge_L%C3%B8vland"}]}, {"year": "1894", "text": "<PERSON>, American dancer and choreographer (d. 1991)", "html": "1894 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American dancer and choreographer (d. 1991)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American dancer and choreographer (d. 1991)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1895", "text": "<PERSON>, French tennis player (d. 1978)", "html": "1895 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French tennis player (d. 1978)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French tennis player (d. 1978)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1895", "text": "<PERSON><PERSON><PERSON>, Indian philosopher and speaker (d. 1986)", "html": "1895 - <a href=\"https://wikipedia.org/wiki/Jidd<PERSON>_<PERSON>\" title=\"Jidd<PERSON> Krishnamurti\"><PERSON><PERSON><PERSON></a>, Indian philosopher and speaker (d. 1986)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Jidd<PERSON>_<PERSON>\" title=\"Jidd<PERSON> Krishnamu<PERSON>i\"><PERSON><PERSON><PERSON></a>, Indian philosopher and speaker (d. 1986)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>dd<PERSON>_<PERSON>i"}]}, {"year": "1895", "text": "<PERSON>, American composer and conductor (d. 1978)", "html": "1895 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American composer and conductor (d. 1978)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American composer and conductor (d. 1978)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1896", "text": "<PERSON><PERSON><PERSON><PERSON><PERSON>, Croatian composer and academic (d. 1955)", "html": "1896 - <a href=\"https://wikipedia.org/wiki/Josip_%C5%A0tolcer-<PERSON>nski\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Croatian composer and academic (d. 1955)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Jo<PERSON><PERSON>_%C5%A0tolcer-<PERSON>nski\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Croatian composer and academic (d. 1955)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Josip_%C5%A0tolcer-Slavenski"}]}, {"year": "1897", "text": "<PERSON>, American businessman (d. 1961)", "html": "1897 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_(businessman)\" title=\"<PERSON> (businessman)\"><PERSON></a>, American businessman (d. 1961)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_(businessman)\" title=\"<PERSON> (businessman)\"><PERSON></a>, American businessman (d. 1961)", "links": [{"title": "<PERSON> (businessman)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>_(businessman)"}]}, {"year": "1901", "text": "<PERSON>, poet and author (d. 1988)", "html": "1901 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>l%C3%A4nder\" title=\"<PERSON>\"><PERSON></a>, poet and author (d. 1988)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_Au<PERSON>l%C3%A4nder\" title=\"<PERSON>\"><PERSON></a>, poet and author (d. 1988)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Rose_Ausl%C3%A4nder"}]}, {"year": "1901", "text": "<PERSON>, American painter (d. 1967)", "html": "1901 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American painter (d. 1967)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American painter (d. 1967)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1903", "text": "<PERSON>, American baseball player and manager (d. 1993)", "html": "1903 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player and manager (d. 1993)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player and manager (d. 1993)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1904", "text": "<PERSON>, Spanish artist (d. 1989)", "html": "1904 - <a href=\"https://wikipedia.org/wiki/Salvador_Dal%C3%AD\" title=\"Salvador Dalí\"><PERSON></a>, Spanish artist (d. 1989)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Salvador_Dal%C3%AD\" title=\"Salvador Dalí\"><PERSON></a>, Spanish artist (d. 1989)", "links": [{"title": "Salvador Dalí", "link": "https://wikipedia.org/wiki/Salvador_Dal%C3%AD"}]}, {"year": "1905", "text": "<PERSON><PERSON>, Mauritian SOE agent, war hero (d. 2004)", "html": "1905 - <a href=\"https://wikipedia.org/wiki/Lise_de_Baissac\" title=\"Lise de Baissac\"><PERSON><PERSON> Baissac</a>, <PERSON><PERSON><PERSON> <a href=\"https://wikipedia.org/wiki/Special_Operations_Executive\" title=\"Special Operations Executive\">SOE</a> agent, war hero (d. 2004)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Lise_de_Baissac\" title=\"Lise de Baissac\"><PERSON><PERSON> Baissac</a>, <PERSON><PERSON><PERSON> <a href=\"https://wikipedia.org/wiki/Special_Operations_Executive\" title=\"Special Operations Executive\">SOE</a> agent, war hero (d. 2004)", "links": [{"title": "Lise de Baissac", "link": "https://wikipedia.org/wiki/Lise_de_Baissac"}, {"title": "Special Operations Executive", "link": "https://wikipedia.org/wiki/Special_Operations_Executive"}]}, {"year": "1905", "text": "<PERSON>, American architect and public housing advocate (d. 1964)", "html": "1905 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American architect and public housing advocate (d. 1964)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American architect and public housing advocate (d. 1964)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1907", "text": "<PERSON><PERSON>, American baseball player and coach (d. 1989)", "html": "1907 - <a href=\"https://wikipedia.org/wiki/R<PERSON>_<PERSON><PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American baseball player and coach (d. 1989)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/R<PERSON>_<PERSON><PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American baseball player and coach (d. 1989)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Rip_<PERSON><PERSON>"}]}, {"year": "1911", "text": "<PERSON>, American actor and comedian (d. 1985)", "html": "1911 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor and comedian (d. 1985)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Silvers\"><PERSON></a>, American actor and comedian (d. 1985)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Phil_<PERSON>"}]}, {"year": "1912", "text": "<PERSON><PERSON><PERSON>, Pakistani author and screenwriter (d. 1955)", "html": "1912 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Pakistani author and screenwriter (d. 1955)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Pakistani author and screenwriter (d. 1955)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1916", "text": "<PERSON><PERSON>, Spanish author and politician, Nobel Prize laureate (d. 2002)", "html": "1916 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>%C3%A9_<PERSON><PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Spanish author and politician, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Literature\" title=\"Nobel Prize in Literature\">Nobel Prize</a> laureate (d. 2002)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>%C3%A9_<PERSON><PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Spanish author and politician, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Literature\" title=\"Nobel Prize in Literature\">Nobel Prize</a> laureate (d. 2002)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Camilo_Jos%C3%A9_Cela"}, {"title": "Nobel Prize in Literature", "link": "https://wikipedia.org/wiki/Nobel_Prize_in_Literature"}]}, {"year": "1918", "text": "<PERSON>, American physicist and engineer, Nobel Prize laureate (d. 1988)", "html": "1918 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American physicist and engineer, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Physics\" title=\"Nobel Prize in Physics\">Nobel Prize</a> laureate (d. 1988)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American physicist and engineer, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Physics\" title=\"Nobel Prize in Physics\">Nobel Prize</a> laureate (d. 1988)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Nobel Prize in Physics", "link": "https://wikipedia.org/wiki/Nobel_Prize_in_Physics"}]}, {"year": "1924", "text": "<PERSON>, English astronomer and academic, Nobel Prize laureate (d. 2021)", "html": "1924 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English astronomer and academic, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Physics\" title=\"Nobel Prize in Physics\">Nobel Prize</a> laureate (d. 2021)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English astronomer and academic, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Physics\" title=\"Nobel Prize in Physics\">Nobel Prize</a> laureate (d. 2021)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Nobel Prize in Physics", "link": "https://wikipedia.org/wiki/Nobel_Prize_in_Physics"}]}, {"year": "1925", "text": "<PERSON>, American politician, 66th Governor of Massachusetts (d. 2006)", "html": "1925 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American politician, 66th <a href=\"https://wikipedia.org/wiki/Governor_of_Massachusetts\" title=\"Governor of Massachusetts\">Governor of Massachusetts</a> (d. 2006)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American politician, 66th <a href=\"https://wikipedia.org/wiki/Governor_of_Massachusetts\" title=\"Governor of Massachusetts\">Governor of Massachusetts</a> (d. 2006)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "Governor of Massachusetts", "link": "https://wikipedia.org/wiki/Governor_of_Massachusetts"}]}, {"year": "1927", "text": "<PERSON>, British actor (d. 2016)", "html": "1927 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(actor)\" title=\"<PERSON> (actor)\"><PERSON></a>, British actor (d. 2016)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(actor)\" title=\"<PERSON> (actor)\"><PERSON></a>, British actor (d. 2016)", "links": [{"title": "<PERSON> (actor)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(actor)"}]}, {"year": "1927", "text": "<PERSON>, American explorer, author, and scholar (d. 2007)", "html": "1927 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American explorer, author, and scholar (d. 2007)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American explorer, author, and scholar (d. 2007)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1929", "text": "<PERSON>, Austrian actor (d. 2024)", "html": "1929 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Austrian actor (d. 2024)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Austrian actor (d. 2024)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1930", "text": "<PERSON><PERSON><PERSON> <PERSON>, Dutch computer scientist and academic (d. 2002)", "html": "1930 - <a href=\"https://wikipedia.org/wiki/Eds<PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON> <PERSON>\"><PERSON><PERSON><PERSON> <PERSON><PERSON></a>, Dutch computer scientist and academic (d. 2002)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>s<PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON> <PERSON><PERSON></a>, Dutch computer scientist and academic (d. 2002)", "links": [{"title": "<PERSON><PERSON><PERSON> W<PERSON>", "link": "https://wikipedia.org/wiki/Eds<PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "1930", "text": "<PERSON>, American Ukrainian Greek Catholic hierarch (d. 2024)", "html": "1930 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American Ukrainian Greek Catholic hierarch (d. 2024)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American Ukrainian Greek Catholic hierarch (d. 2024)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1932", "text": "<PERSON><PERSON>, Italian fashion designer", "html": "1932 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON><PERSON>vani\"><PERSON><PERSON></a>, Italian fashion designer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Italian fashion designer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Vale<PERSON>_<PERSON>"}]}, {"year": "1933", "text": "<PERSON>, American religious leader", "html": "1933 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American religious leader", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American religious leader", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1934", "text": "<PERSON>, American lawyer and politician (d. 2014)", "html": "1934 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and politician (d. 2014)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and politician (d. 2014)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1934", "text": "<PERSON>, American basketball player (d. 2012)", "html": "1934 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player (d. 2012)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player (d. 2012)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1937", "text": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, Hungarian Olympic and world champion foil fencer", "html": "1937 - <a href=\"https://wikipedia.org/wiki/Ildik%C3%B3_%C3%9Ajlaky-Rejt%C5%91\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON><PERSON>-<PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Hungarian Olympic and world champion foil fencer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Ildik%C3%B3_%C3%9Ajlaky-Rejt%C5%91\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON><PERSON>-Rejt<PERSON>\"><PERSON><PERSON><PERSON><PERSON>-<PERSON></a>, Hungarian Olympic and world champion foil fencer", "links": [{"title": "Ildikó <PERSON>-Rejtő", "link": "https://wikipedia.org/wiki/Ildik%C3%B3_%C3%9Ajlaky-Rejt%C5%91"}]}, {"year": "1941", "text": "<PERSON>, English musician", "html": "1941 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English musician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English musician", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1941", "text": "<PERSON>, Australian cricketer and coach (d. 2024)", "html": "1941 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian cricketer and coach (d. 2024)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian cricketer and coach (d. 2024)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1943", "text": "<PERSON>, Canadian skier and politician", "html": "1943 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, Canadian skier and politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, Canadian skier and politician", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1945", "text": "<PERSON>, Venezuelan biologist", "html": "1945 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A<PERSON><PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Venezuelan biologist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A<PERSON><PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Venezuelan biologist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Hilda_P%C3%A9<PERSON>_<PERSON>l"}]}, {"year": "1947", "text": "<PERSON>, American drummer (d. 2017)", "html": "1947 - <a href=\"https://wikipedia.org/wiki/<PERSON>_Trucks\" title=\"Butch Trucks\"><PERSON></a>, American drummer (d. 2017)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_Trucks\" title=\"Butch Trucks\"><PERSON> Truck<PERSON></a>, American drummer (d. 2017)", "links": [{"title": "<PERSON>s", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>s"}]}, {"year": "1948", "text": "<PERSON><PERSON><PERSON>, British politician", "html": "1948 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, British politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, British politician", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>a"}]}, {"year": "1948", "text": "<PERSON>, Welsh actress", "html": "1948 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Welsh actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Welsh actress", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1950", "text": "<PERSON>, English journalist and author", "html": "1950 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English journalist and author", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English journalist and author", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1950", "text": "<PERSON><PERSON><PERSON>, Indian actor (d. 2014)", "html": "1950 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>rap<PERSON>\"><PERSON><PERSON><PERSON></a>, Indian actor (d. 2014)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>rap<PERSON>\"><PERSON><PERSON><PERSON></a>, Indian actor (d. 2014)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON><PERSON><PERSON>"}]}, {"year": "1951", "text": "<PERSON>, Canadian farmer and politician, 13th Premier of Alberta", "html": "1951 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian farmer and politician, 13th <a href=\"https://wikipedia.org/wiki/Premier_of_Alberta\" title=\"Premier of Alberta\">Premier of Alberta</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian farmer and politician, 13th <a href=\"https://wikipedia.org/wiki/Premier_of_Alberta\" title=\"Premier of Alberta\">Premier of Alberta</a>", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Premier of Alberta", "link": "https://wikipedia.org/wiki/Premier_of_Alberta"}]}, {"year": "1954", "text": "<PERSON>, English footballer and manager", "html": "1954 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>(footballer)\" title=\"<PERSON> (footballer)\"><PERSON></a>, English footballer and manager", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>(footballer)\" title=\"<PERSON> (footballer)\"><PERSON></a>, English footballer and manager", "links": [{"title": "<PERSON> (footballer)", "link": "https://wikipedia.org/wiki/<PERSON>(footballer)"}]}, {"year": "1955", "text": "<PERSON>, American businessman", "html": "1955 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American businessman", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American businessman", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1957", "text": "<PERSON>, Northern Irish journalist and politician", "html": "1957 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Northern Irish journalist and politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Northern Irish journalist and politician", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1958", "text": "<PERSON><PERSON>, Japanese singer-songwriter", "html": "1958 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Japanese singer-songwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Japanese singer-songwriter", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1963", "text": "<PERSON>, English actress (d. 2009)", "html": "1963 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actress (d. 2009)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actress (d. 2009)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1964", "text": "<PERSON>, American actor", "html": "1964 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1964", "text": "<PERSON>, American baseball player", "html": "1964 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1967", "text": "<PERSON>, Mexican footballer and manager", "html": "1967 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%ADa_As<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Mexican footballer and manager", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%ADa_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Mexican footballer and manager", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Alberto_Garc%C3%ADa_Aspe"}]}, {"year": "1969", "text": "<PERSON>, Australian rugby league player and coach", "html": "1969 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian rugby league player and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian rugby league player and coach", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1969", "text": "<PERSON>, Dutch runner", "html": "1969 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch runner", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch runner", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1970", "text": "<PERSON>, American lawyer and politician", "html": "1970 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>.\" title=\"<PERSON> Jr.\"><PERSON>.</a>, American lawyer and politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>.\" title=\"<PERSON> Jr.\"><PERSON>.</a>, American lawyer and politician", "links": [{"title": "<PERSON> Jr.", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>."}]}, {"year": "1974", "text": "<PERSON>, Papua New Guinean rugby league player", "html": "1974 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Papua New Guinean rugby league player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Papua New Guinean rugby league player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1975", "text": "<PERSON>, Dominican baseball player", "html": "1975 - <a href=\"https://wikipedia.org/wiki/Francisco_Cordero\" title=\"Francisco Cordero\"><PERSON></a>, Dominican baseball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Francisco_Cordero\" title=\"Francisco Cordero\"><PERSON></a>, Dominican baseball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Francisco_Cordero"}]}, {"year": "1975", "text": "<PERSON><PERSON><PERSON>, Lebanese terrorist, September 11 attacks (d. 2001)[citation needed]", "html": "1975 - <a href=\"https://wikipedia.org/wiki/Ziad_Jarrah\" title=\"Ziad Jarrah\"><PERSON><PERSON><PERSON></a>, Lebanese terrorist, <a href=\"https://wikipedia.org/wiki/September_11_attacks\" title=\"September 11 attacks\">September 11 attacks</a> (d. 2001)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Ziad_Jarrah\" title=\"Ziad Jarrah\"><PERSON><PERSON><PERSON></a>, Lebanese terrorist, <a href=\"https://wikipedia.org/wiki/September_11_attacks\" title=\"September 11 attacks\">September 11 attacks</a> (d. 2001)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Ziad_J<PERSON>h"}, {"title": "September 11 attacks", "link": "https://wikipedia.org/wiki/September_11_attacks"}]}, {"year": "1976", "text": "<PERSON><PERSON><PERSON>, Canadian rapper and record producer/executive", "html": "1976 - <a href=\"https://wikipedia.org/wiki/Kardinal_Offishall\" title=\"Kardinal Offishall\"><PERSON><PERSON><PERSON> Offishall</a>, Canadian rapper and record producer/executive", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Kardinal_Offishall\" title=\"Kardinal Offishall\"><PERSON><PERSON><PERSON> Offishall</a>, Canadian rapper and record producer/executive", "links": [{"title": "<PERSON><PERSON><PERSON>all", "link": "https://wikipedia.org/wiki/Kardinal_Offishall"}]}, {"year": "1977", "text": "<PERSON>, Uruguayan footballer", "html": "1977 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>%C3%ADa\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, Uruguayan footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>%C3%ADa\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, Uruguayan footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>%C3%ADa"}]}, {"year": "1977", "text": "<PERSON>, South African rugby player, coach, and sportscaster", "html": "1977 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, South African rugby player, coach, and sportscaster", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, South African rugby player, coach, and sportscaster", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1977", "text": "<PERSON>, Canadian professional wrestler", "html": "1977 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian professional wrestler", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian professional wrestler", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1978", "text": "<PERSON><PERSON><PERSON>, French model and actress", "html": "1978 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, French model and actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, French model and actress", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Laetitia_Casta"}]}, {"year": "1978", "text": "<PERSON>, Filipino actress", "html": "1978 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Filipino actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Filipino actress", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1981", "text": "<PERSON>, Australian basketball player", "html": "1981 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian basketball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1981", "text": "<PERSON>, American actor, voice actor and comedian", "html": "1981 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, American actor, voice actor and comedian", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, American actor, voice actor and comedian", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1982", "text": "<PERSON>, Canadian actor and singer (d. 2013)", "html": "1982 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian actor and singer (d. 2013)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian actor and singer (d. 2013)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1983", "text": "<PERSON>, American football player", "html": "1983 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1983", "text": "<PERSON>, American-Israeli journalist (d. 2014)", "html": "1983 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American-Israeli journalist (d. 2014)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American-Israeli journalist (d. 2014)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1983", "text": "<PERSON>,  Australian actress, singer and model", "html": "1983 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian actress, singer and model", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian actress, singer and model", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1984", "text": "<PERSON>, Spanish footballer", "html": "1984 - <a href=\"https://wikipedia.org/wiki/Andr%C3%A9s_Iniesta\" title=\"<PERSON>\"><PERSON></a>, Spanish footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Andr%C3%A9s_Iniesta\" title=\"<PERSON>\"><PERSON></a>, Spanish footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Andr%C3%A9s_Iniesta"}]}, {"year": "1985", "text": "<PERSON>, Australian rugby league player and television host", "html": "1985 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian rugby league player and television host", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian rugby league player and television host", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1986", "text": "<PERSON><PERSON><PERSON>, French footballer", "html": "1986 - <a href=\"https://wikipedia.org/wiki/Abo<PERSON>_<PERSON><PERSON>\" title=\"Abo<PERSON> Diaby\"><PERSON><PERSON><PERSON></a>, French footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Abo<PERSON>_<PERSON><PERSON>\" title=\"Abou Diaby\"><PERSON><PERSON><PERSON></a>, French footballer", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Abou_<PERSON>aby"}]}, {"year": "1986", "text": "<PERSON>, Portuguese footballer", "html": "1986 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Portuguese footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Portuguese footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1987", "text": "<PERSON>, South Korean singer and actor", "html": "1987 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>-ong\" title=\"<PERSON>-ong\"><PERSON></a>, South Korean singer and actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>-on<PERSON>\" title=\"<PERSON>-ong\"><PERSON></a>, South Korean singer and actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>-ong"}]}, {"year": "1988", "text": "<PERSON>, American football player", "html": "1988 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1988", "text": "<PERSON>, Canadian ice hockey player", "html": "1988 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>and"}]}, {"year": "1989", "text": "<PERSON><PERSON><PERSON>, Mexican footballer", "html": "1989 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Mexican footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Mexican footballer", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1989", "text": "<PERSON>, American football player", "html": "1989 - <a href=\"https://wikipedia.org/wiki/Cam_<PERSON>\" title=\"Cam Newton\"><PERSON></a>, American football player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Cam_<PERSON>\" title=\"Cam Newton\"><PERSON></a>, American football player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1992", "text": "<PERSON><PERSON><PERSON><PERSON>, Belgian footballer", "html": "1992 - <a href=\"https://wikipedia.org/wiki/Thi<PERSON><PERSON>_<PERSON>\" title=\"Thi<PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Belgian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Thi<PERSON><PERSON>_<PERSON>\" title=\"Thi<PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Belgian footballer", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Thiba<PERSON>_<PERSON>ois"}]}, {"year": "1992", "text": "<PERSON>, Spanish footballer", "html": "1992 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Pablo <PERSON>\"><PERSON></a>, Spanish footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Pablo <PERSON>\"><PERSON></a>, Spanish footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Pablo_Sarabia"}]}, {"year": "1992", "text": "<PERSON><PERSON>, Portuguese Rafe<PERSON> do Alentejo, oldest recorded dog (d. 2023)", "html": "1992 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_(dog)\" title=\"<PERSON><PERSON> (dog)\"><PERSON><PERSON></a>, Portuguese <a href=\"https://wikipedia.org/wiki/Rafeiro_do_Alentejo\" title=\"Rafeiro do Alentejo\"><PERSON><PERSON> do Alentejo</a>, <a href=\"https://wikipedia.org/wiki/List_of_longest_living_dogs\" class=\"mw-redirect\" title=\"List of longest living dogs\">oldest recorded dog</a> (d. 2023)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_(dog)\" title=\"<PERSON><PERSON> (dog)\"><PERSON><PERSON></a>, Portuguese <a href=\"https://wikipedia.org/wiki/Rafeiro_do_Alentejo\" title=\"Rafeiro do Alentejo\"><PERSON><PERSON> do Alentejo</a>, <a href=\"https://wikipedia.org/wiki/List_of_longest_living_dogs\" class=\"mw-redirect\" title=\"List of longest living dogs\">oldest recorded dog</a> (d. 2023)", "links": [{"title": "<PERSON><PERSON> (dog)", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_(dog)"}, {"title": "Rafeiro do Alentejo", "link": "https://wikipedia.org/wiki/Rafeiro_do_Alentejo"}, {"title": "List of longest living dogs", "link": "https://wikipedia.org/wiki/List_of_longest_living_dogs"}]}, {"year": "1993", "text": "<PERSON>, American-Puerto Rican basketball player", "html": "1993 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American-Puerto Rican basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American-Puerto Rican basketball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1993", "text": "<PERSON>, Dominican baseball player", "html": "1993 - <a href=\"https://wikipedia.org/wiki/Miguel_San%C3%B3\" title=\"<PERSON> Sanó\"><PERSON></a>, Dominican baseball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Miguel_San%C3%B3\" title=\"<PERSON>\"><PERSON></a>, Dominican baseball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Miguel_San%C3%B3"}]}, {"year": "1994", "text": "<PERSON><PERSON>, Ethiopian runner", "html": "1994 - <a href=\"https://wikipedia.org/wiki/Hagos_<PERSON>ebrhiwet\" title=\"Hagos <PERSON>ebrhiwet\"><PERSON><PERSON></a>, Ethiopian runner", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Hagos_<PERSON>ebrhiwet\" title=\"Hagos <PERSON>ebrhiwet\"><PERSON><PERSON></a>, Ethiopian runner", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Hagos_<PERSON>eb<PERSON>hi<PERSON>t"}]}, {"year": "1994", "text": "<PERSON><PERSON>, Papua New Guinean rugby league player", "html": "1994 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Papua New Guinean rugby league player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Papua New Guinean rugby league player", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1995", "text": "<PERSON><PERSON><PERSON>, Portuguese footballer", "html": "1995 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Portuguese footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Portuguese footballer", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1995", "text": "<PERSON><PERSON>, American tennis player", "html": "1995 - <a href=\"https://wikipedia.org/wiki/Sachia_Vickery\" title=\"Sachia Vickery\"><PERSON><PERSON></a>, American tennis player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Sachia_Vickery\" title=\"Sachia Vickery\"><PERSON><PERSON></a>, American tennis player", "links": [{"title": "Sachia Vickery", "link": "https://wikipedia.org/wiki/Sachia_Vickery"}]}, {"year": "1996", "text": "<PERSON><PERSON>, Canadian ice hockey player", "html": "1996 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Canadian ice hockey player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Canadian ice hockey player", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>in_<PERSON>"}]}, {"year": "1997", "text": "<PERSON>, American actress", "html": "1997 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Lana_Condor"}]}, {"year": "1998", "text": "<PERSON><PERSON><PERSON><PERSON><PERSON>, Slovak tennis player", "html": "1998 - <a href=\"https://wikipedia.org/wiki/Vikt%C3%B3ria_Ku%C5%BEmov%C3%A1\" class=\"mw-redirect\" title=\"Viktória Kužmová\"><PERSON><PERSON><PERSON><PERSON><PERSON></a>, Slovak tennis player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Vikt%C3%B3ria_Ku%C5%BEmov%C3%A1\" class=\"mw-redirect\" title=\"Viktória Kužmová\"><PERSON><PERSON><PERSON><PERSON><PERSON></a>, Slovak tennis player", "links": [{"title": "Viktória Kužmová", "link": "https://wikipedia.org/wiki/Vikt%C3%B3ria_Ku%C5%BEmov%C3%A1"}]}, {"year": "1999", "text": "<PERSON>, American singer and actress", "html": "1999 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer and actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer and actress", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2000", "text": "<PERSON><PERSON><PERSON>, Japanese racing driver", "html": "2000 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese racing driver", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese racing driver", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "2003", "text": "<PERSON><PERSON><PERSON>, Spanish footballer", "html": "2003 - <a href=\"https://wikipedia.org/wiki/Ferm%C3%ADn_L%C3%B3pez\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Spanish footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Ferm%C3%ADn_L%C3%B3pez\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Spanish footballer", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Ferm%C3%ADn_L%C3%B3pez"}]}, {"year": "2006", "text": "<PERSON><PERSON><PERSON>, Finnish ice hockey player", "html": "2006 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Finnish ice hockey player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Finnish ice hockey player", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}], "Deaths": [{"year": "912", "text": "<PERSON> <PERSON> the Wise, Byzantine Emperor, the second ruler of the Macedonian dynasty (b. 866)", "html": "912 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_the_Wise\" title=\"<PERSON> VI the Wise\"><PERSON> VI the Wise</a>, <a href=\"https://wikipedia.org/wiki/Byzantine_Emperor\" class=\"mw-redirect\" title=\"Byzantine Emperor\">Byzantine Emperor</a>, the second ruler of the <a href=\"https://wikipedia.org/wiki/Macedonian_dynasty\" title=\"Macedonian dynasty\">Macedonian dynasty</a> (b. 866)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_VI_the_Wise\" title=\"<PERSON> VI the Wise\"><PERSON> VI the Wise</a>, <a href=\"https://wikipedia.org/wiki/Byzantine_Emperor\" class=\"mw-redirect\" title=\"Byzantine Emperor\">Byzantine Emperor</a>, the second ruler of the <a href=\"https://wikipedia.org/wiki/Macedonian_dynasty\" title=\"Macedonian dynasty\">Macedonian dynasty</a> (b. 866)", "links": [{"title": "<PERSON> the Wise", "link": "https://wikipedia.org/wiki/<PERSON>_VI_the_<PERSON>"}, {"title": "Byzantine Emperor", "link": "https://wikipedia.org/wiki/Byzantine_Emperor"}, {"title": "Macedonian dynasty", "link": "https://wikipedia.org/wiki/Macedonian_dynasty"}]}, {"year": "1610", "text": "<PERSON>, Italian priest and mathematician (b. 1552)", "html": "1610 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian priest and mathematician (b. 1552)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian priest and mathematician (b. 1552)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1778", "text": "<PERSON>, 1st Earl of Chatham, English politician, Prime Minister of Great Britain (b. 1708)", "html": "1778 - <a href=\"https://wikipedia.org/wiki/<PERSON>,_1st_Earl_of_Chatham\" title=\"<PERSON>, 1st Earl of Chatham\"><PERSON>, 1st Earl of Chatham</a>, English politician, <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Great_Britain\" class=\"mw-redirect\" title=\"Prime Minister of Great Britain\">Prime Minister of Great Britain</a> (b. 1708)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>,_1st_Earl_of_Chatham\" title=\"<PERSON>, 1st Earl of Chatham\"><PERSON>, 1st Earl of Chatham</a>, English politician, <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Great_Britain\" class=\"mw-redirect\" title=\"Prime Minister of Great Britain\">Prime Minister of Great Britain</a> (b. 1708)", "links": [{"title": "<PERSON>, 1st Earl of Chatham", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_1st_Earl_of_Chatham"}, {"title": "Prime Minister of Great Britain", "link": "https://wikipedia.org/wiki/Prime_Minister_of_Great_Britain"}]}, {"year": "1779", "text": "<PERSON>, American lawyer and politician (b. 1711)", "html": "1779 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(New_Jersey_politician)\" title=\"<PERSON> (New Jersey politician)\"><PERSON></a>, American lawyer and politician (b. 1711)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(New_Jersey_politician)\" title=\"<PERSON> (New Jersey politician)\"><PERSON></a>, American lawyer and politician (b. 1711)", "links": [{"title": "<PERSON> (New Jersey politician)", "link": "https://wikipedia.org/wiki/<PERSON>(New_Jersey_politician)"}]}, {"year": "1812", "text": "<PERSON>, English lawyer and politician, Prime Minister of the United Kingdom (b. 1762)", "html": "1812 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English lawyer and politician, <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_the_United_Kingdom\" title=\"Prime Minister of the United Kingdom\">Prime Minister of the United Kingdom</a> (b. 1762)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English lawyer and politician, <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_the_United_Kingdom\" title=\"Prime Minister of the United Kingdom\">Prime Minister of the United Kingdom</a> (b. 1762)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Prime Minister of the United Kingdom", "link": "https://wikipedia.org/wiki/Prime_Minister_of_the_United_Kingdom"}]}, {"year": "1848", "text": "<PERSON>, English boxer (b. 1781)", "html": "1848 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English boxer (b. 1781)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English boxer (b. 1781)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>"}]}, {"year": "1849", "text": "<PERSON>, French businesswoman (b. 1777)", "html": "1849 - <a href=\"https://wikipedia.org/wiki/Juliette_R%C3%A9camier\" title=\"<PERSON>\"><PERSON></a>, French businesswoman (b. 1777)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Juliette_R%C3%A9camier\" title=\"<PERSON>\"><PERSON></a>, French businesswoman (b. 1777)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Juliette_R%C3%A9camier"}]}, {"year": "1882", "text": "<PERSON>, Scottish-Australian politician, 9th Premier of Tasmania (b. 1816)", "html": "1882 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish-Australian politician, 9th <a href=\"https://wikipedia.org/wiki/Premier_of_Tasmania\" title=\"Premier of Tasmania\">Premier of Tasmania</a> (b. 1816)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish-Australian politician, 9th <a href=\"https://wikipedia.org/wiki/Premier_of_Tasmania\" title=\"Premier of Tasmania\">Premier of Tasmania</a> (b. 1816)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Frederick_Innes"}, {"title": "Premier of Tasmania", "link": "https://wikipedia.org/wiki/Premier_of_Tasmania"}]}, {"year": "1889", "text": "<PERSON>, English businessman and philanthropist, founded the Cadbury Company (b. 1801)", "html": "1889 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English businessman and philanthropist, founded the <a href=\"https://wikipedia.org/wiki/Cadbury\" title=\"Cadbury\">Cadbury Company</a> (b. 1801)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English businessman and philanthropist, founded the <a href=\"https://wikipedia.org/wiki/Cadbury\" title=\"Cadbury\">Cadbury Company</a> (b. 1801)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Cadbury", "link": "https://wikipedia.org/wiki/Cadbury"}]}, {"year": "1908", "text": "<PERSON>, Australian politician, 20th Premier of South Australia (b. 1850)", "html": "1908 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian politician, 20th <a href=\"https://wikipedia.org/wiki/Premier_of_South_Australia\" title=\"Premier of South Australia\">Premier of South Australia</a> (b. 1850)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Charles <PERSON>\"><PERSON></a>, Australian politician, 20th <a href=\"https://wikipedia.org/wiki/Premier_of_South_Australia\" title=\"Premier of South Australia\">Premier of South Australia</a> (b. 1850)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Premier of South Australia", "link": "https://wikipedia.org/wiki/Premier_of_South_Australia"}]}, {"year": "1916", "text": "<PERSON>, German astronomer and physicist (b. 1873): xix ", "html": "1916 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German astronomer and physicist (b. 1873)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German astronomer and physicist (b. 1873)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1918", "text": "<PERSON>, Australian politician, 25th Premier of Victoria (b. 1861)", "html": "1918 - <a href=\"https://wikipedia.org/wiki/<PERSON>_(Australian_politician)\" class=\"mw-redirect\" title=\"<PERSON> (Australian politician)\"><PERSON></a>, Australian politician, 25th <a href=\"https://wikipedia.org/wiki/Premier_of_Victoria\" title=\"Premier of Victoria\">Premier of Victoria</a> (b. 1861)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(Australian_politician)\" class=\"mw-redirect\" title=\"<PERSON> (Australian politician)\"><PERSON></a>, Australian politician, 25th <a href=\"https://wikipedia.org/wiki/Premier_of_Victoria\" title=\"Premier of Victoria\">Premier of Victoria</a> (b. 1861)", "links": [{"title": "<PERSON> (Australian politician)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(Australian_politician)"}, {"title": "Premier of Victoria", "link": "https://wikipedia.org/wiki/Premier_of_Victoria"}]}, {"year": "1920", "text": "<PERSON>, Italian-American mob boss (b. 1878)", "html": "1920 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, Italian-American mob boss (b. 1878)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, Italian-American mob boss (b. 1878)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1920", "text": "<PERSON>, American novelist, literary critic, and playwright (b. 1837)", "html": "1920 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American novelist, literary critic, and playwright (b. 1837)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American novelist, literary critic, and playwright (b. 1837)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1927", "text": "<PERSON>, Spanish painter and sculptor (b. 1887)", "html": "1927 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Spanish painter and sculptor (b. 1887)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Spanish painter and sculptor (b. 1887)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1929", "text": "<PERSON><PERSON>, Slovak-American priest, architect, botanist, and painter (b. 1864)", "html": "1929 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>%C5%A1\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Slovak-American priest, architect, botanist, and painter (b. 1864)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>%C5%A1\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Slovak-American priest, architect, botanist, and painter (b. 1864)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>%C5%A1"}]}, {"year": "1938", "text": "<PERSON>, Canadian golfer and cricketer (b. 1858)", "html": "1938 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(golfer)\" title=\"<PERSON> (golfer)\"><PERSON></a>, Canadian golfer and cricketer (b. 1858)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(golfer)\" title=\"<PERSON> (golfer)\"><PERSON></a>, Canadian golfer and cricketer (b. 1858)", "links": [{"title": "<PERSON> (golfer)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(golfer)"}]}, {"year": "1946", "text": "<PERSON><PERSON>, Irish Republican, died on hunger strike (b. 1915)", "html": "1946 - <a href=\"https://wikipedia.org/wiki/Se%C3%A1<PERSON>_<PERSON><PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Irish Republican, died on hunger strike (b. 1915)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Se%C3%A1<PERSON>_<PERSON><PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Irish Republican, died on hunger strike (b. 1915)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Se%C3%A1<PERSON>_<PERSON><PERSON><PERSON><PERSON><PERSON>"}]}, {"year": "1955", "text": "<PERSON>, English cricketer (b. 1874)", "html": "1955 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English cricketer (b. 1874)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English cricketer (b. 1874)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1960", "text": "<PERSON>, American businessman and philanthropist (b. 1874)", "html": "1960 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>.\" title=\"<PERSON> Jr.\"><PERSON> Jr.</a>, American businessman and philanthropist (b. 1874)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>.\" title=\"<PERSON> Jr.\"><PERSON> Jr.</a>, American businessman and philanthropist (b. 1874)", "links": [{"title": "<PERSON> Jr.", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>."}]}, {"year": "1963", "text": "<PERSON>, American physiologist and academic, Nobel Prize laureate (b. 1888): 169 ", "html": "1963 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American physiologist and academic, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Physiology_or_Medicine\" title=\"Nobel Prize in Physiology or Medicine\">Nobel Prize</a> laureate (b. 1888)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American physiologist and academic, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Physiology_or_Medicine\" title=\"Nobel Prize in Physiology or Medicine\">Nobel Prize</a> laureate (b. 1888)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "Nobel Prize in Physiology or Medicine", "link": "https://wikipedia.org/wiki/Nobel_Prize_in_Physiology_or_Medicine"}]}, {"year": "1964", "text": "<PERSON><PERSON>, Finnish politician (b. 1901)", "html": "1964 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Finnish politician (b. 1901)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Finnish politician (b. 1901)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1967", "text": "<PERSON>, American painter (b. 1930)", "html": "1967 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American painter (b. 1930)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American painter (b. 1930)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "1979", "text": "<PERSON>, American singer-songwriter and guitarist (b. 1914)", "html": "1979 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and guitarist (b. 1914)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and guitarist (b. 1914)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1980", "text": "<PERSON><PERSON>, Norwegian sculptor and painter (b. 1903)", "html": "1980 - <a href=\"https://wikipedia.org/wiki/D<PERSON>_Vaa\" title=\"Dyre Vaa\"><PERSON><PERSON></a>, Norwegian sculptor and painter (b. 1903)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_Vaa\" title=\"<PERSON>yre Vaa\"><PERSON><PERSON></a>, Norwegian sculptor and painter (b. 1903)", "links": [{"title": "Dyre Vaa", "link": "https://wikipedia.org/wiki/Dyre_Vaa"}]}, {"year": "1981", "text": "<PERSON>, Norwegian chemist and academic, Nobel Prize laureate (b. 1897)", "html": "1981 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Norwegian chemist and academic, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Chemistry\" title=\"Nobel Prize in Chemistry\">Nobel Prize</a> laureate (b. 1897)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>\" title=\"<PERSON>\"><PERSON></a>, Norwegian chemist and academic, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Chemistry\" title=\"Nobel Prize in Chemistry\">Nobel Prize</a> laureate (b. 1897)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Nobel Prize in Chemistry", "link": "https://wikipedia.org/wiki/Nobel_Prize_in_Chemistry"}]}, {"year": "1981", "text": "<PERSON>, Jamaican singer-songwriter and guitarist (b. 1945)", "html": "1981 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Jamaican singer-songwriter and guitarist (b. 1945)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Marley\"><PERSON></a>, Jamaican singer-songwriter and guitarist (b. 1945)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1983", "text": "<PERSON><PERSON>, American writer (b. 1917)", "html": "1983 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American writer (b. 1917)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American writer (b. 1917)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1985", "text": "<PERSON>, American cartoonist, created <PERSON> (b. 1900)", "html": "1985 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American cartoonist, created <i><a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a></i> (b. 1900)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American cartoonist, created <i><a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a></i> (b. 1900)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_Gould"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1986", "text": "<PERSON>, American football player and coach (b. 1894)", "html": "1986 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player and coach (b. 1894)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player and coach (b. 1894)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1988", "text": "<PERSON>, British-Soviet double agent (b. 1912)", "html": "1988 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, British-Soviet double agent (b. 1912)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, British-Soviet double agent (b. 1912)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1990", "text": "<PERSON><PERSON><PERSON>, Greek Singer, composer and lyricist (b. 1935)", "html": "1990 - <a href=\"https://wikipedia.org/wiki/Stratos_Dionysiou\" title=\"Stratos Dionysiou\"><PERSON><PERSON><PERSON></a>, <a href=\"https://wikipedia.org/wiki/Greeks\" title=\"Greeks\">Greek</a> <a href=\"https://wikipedia.org/wiki/Singing\" title=\"Singing\">Singer</a>, <a href=\"https://wikipedia.org/wiki/Composer\" title=\"Composer\">composer</a> and <a href=\"https://wikipedia.org/wiki/Lyricist\" title=\"Lyricist\">lyricist</a> (b. 1935)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Strato<PERSON>_Dionysiou\" title=\"Stratos Dionysiou\"><PERSON><PERSON><PERSON></a>, <a href=\"https://wikipedia.org/wiki/Greeks\" title=\"Greeks\">Greek</a> <a href=\"https://wikipedia.org/wiki/Singing\" title=\"Singing\">Singer</a>, <a href=\"https://wikipedia.org/wiki/Composer\" title=\"Composer\">composer</a> and <a href=\"https://wikipedia.org/wiki/Lyricist\" title=\"Lyricist\">lyricist</a> (b. 1935)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/St<PERSON>s_Dionysiou"}, {"title": "Greeks", "link": "https://wikipedia.org/wiki/Greeks"}, {"title": "Singing", "link": "https://wikipedia.org/wiki/Singing"}, {"title": "Composer", "link": "https://wikipedia.org/wiki/Composer"}, {"title": "Lyricist", "link": "https://wikipedia.org/wiki/Lyricist"}]}, {"year": "1994", "text": "<PERSON>, American actor, director, and producer (b. 1928)", "html": "1994 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor, director, and producer (b. 1928)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor, director, and producer (b. 1928)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2001", "text": "<PERSON>, English novelist and screenwriter (b. 1952)", "html": "2001 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English novelist and screenwriter (b. 1952)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English novelist and screenwriter (b. 1952)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2002", "text": "<PERSON><PERSON><PERSON>, Canadian journalist and politician (b. 1912)", "html": "2002 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Canadian journalist and politician (b. 1912)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Canadian journalist and politician (b. 1912)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "2002", "text": "<PERSON>, American animator and screenwriter (b. 1915)", "html": "2002 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American animator and screenwriter (b. 1915)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American animator and screenwriter (b. 1915)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2003", "text": "<PERSON>, English bass player (b. 1945)", "html": "2003 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English bass player (b. 1945)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English bass player (b. 1945)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2005", "text": "<PERSON><PERSON><PERSON>, Canadian politician, 17th Canadian Minister of National Defence (b. 1908)", "html": "2005 - <a href=\"https://wikipedia.org/wiki/L%C3%A9o_Cadieux\" title=\"<PERSON>é<PERSON>\"><PERSON><PERSON><PERSON></a>, Canadian politician, 17th <a href=\"https://wikipedia.org/wiki/Minister_of_National_Defence_(Canada)\" title=\"Minister of National Defence (Canada)\">Canadian Minister of National Defence</a> (b. 1908)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/L%C3%A9o_Cadieux\" title=\"<PERSON>é<PERSON>\"><PERSON><PERSON><PERSON></a>, Canadian politician, 17th <a href=\"https://wikipedia.org/wiki/Minister_of_National_Defence_(Canada)\" title=\"Minister of National Defence (Canada)\">Canadian Minister of National Defence</a> (b. 1908)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/L%C3%A9o_C<PERSON>eux"}, {"title": "Minister of National Defence (Canada)", "link": "https://wikipedia.org/wiki/Minister_of_National_Defence_(Canada)"}]}, {"year": "2005", "text": "<PERSON>, Welsh minister and historian (b. 1916)", "html": "2005 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Welsh minister and historian (b. 1916)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Welsh minister and historian (b. 1916)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2006", "text": "<PERSON>, American boxer and actor (b. 1935)", "html": "2006 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American boxer and actor (b. 1935)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American boxer and actor (b. 1935)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2007", "text": "<PERSON><PERSON><PERSON>, Samoan ruler (b. 1913)", "html": "2007 - <a href=\"https://wikipedia.org/wiki/Malietoa_Tanumafili_II\" title=\"Malietoa Tanumafili II\"><PERSON><PERSON><PERSON> Tanumafili II</a>, Samoan ruler (b. 1913)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Malietoa_Tanumafili_II\" title=\"Malietoa Tanumafili II\"><PERSON><PERSON><PERSON> Tanumafili II</a>, Samoan ruler (b. 1913)", "links": [{"title": "Malietoa Tanumafili II", "link": "https://wikipedia.org/wiki/Malietoa_Tanumafili_II"}]}, {"year": "2008", "text": "<PERSON>, Canadian drummer (b. 1953)", "html": "2008 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian drummer (b. 1953)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian drummer (b. 1953)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2009", "text": "<PERSON>, Central African physician and politician, Prime Minister of the Central African Republic (b. 1926)", "html": "2009 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Central African physician and politician, <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_the_Central_African_Republic\" class=\"mw-redirect\" title=\"Prime Minister of the Central African Republic\">Prime Minister of the Central African Republic</a> (b. 1926)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Central African physician and politician, <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_the_Central_African_Republic\" class=\"mw-redirect\" title=\"Prime Minister of the Central African Republic\">Prime Minister of the Central African Republic</a> (b. 1926)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Prime Minister of the Central African Republic", "link": "https://wikipedia.org/wiki/Prime_Minister_of_the_Central_African_Republic"}]}, {"year": "2009", "text": "<PERSON>, Chilean economist and politician, Chilean Minister Secretary-General of Government (b. 1939)", "html": "2009 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Chilean economist and politician, <a href=\"https://wikipedia.org/wiki/Ministry_General_Secretariat_of_Government_(Chile)\" title=\"Ministry General Secretariat of Government (Chile)\">Chilean Minister Secretary-General of Government</a> (b. 1939)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Chilean economist and politician, <a href=\"https://wikipedia.org/wiki/Ministry_General_Secretariat_of_Government_(Chile)\" title=\"Ministry General Secretariat of Government (Chile)\">Chilean Minister Secretary-General of Government</a> (b. 1939)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Ministry General Secretariat of Government (Chile)", "link": "https://wikipedia.org/wiki/Ministry_General_Secretariat_of_Government_(Chile)"}]}, {"year": "2009", "text": "<PERSON><PERSON><PERSON><PERSON>, Indian admiral (b. 1915)", "html": "2009 - <a href=\"https://wikipedia.org/wiki/Sardarilal_Mathradas_Nanda\" title=\"Sardarilal Mathradas Nanda\"><PERSON><PERSON><PERSON><PERSON> Mathrada<PERSON></a>, Indian admiral (b. 1915)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Sarda<PERSON>al_Mathradas_Nanda\" title=\"Sardarilal Mathradas Nanda\"><PERSON><PERSON><PERSON><PERSON>rada<PERSON></a>, Indian admiral (b. 1915)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Sardarilal_Mathradas_Nanda"}]}, {"year": "2010", "text": "<PERSON>, American dancer and vaudevillian (b. 1904)", "html": "2010 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American dancer and vaudevillian (b. 1904)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American dancer and vaudevillian (b. 1904)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "2011", "text": "<PERSON>, American basketball player (b. 1977)", "html": "2011 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player (b. 1977)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player (b. 1977)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2019", "text": "<PERSON>, American actress, model, and singer (b. 1946)", "html": "2019 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress, model, and singer (b. 1946)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress, model, and singer (b. 1946)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2019", "text": "<PERSON>, American murderer (b. 1952)", "html": "2019 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American murderer (b. 1952)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American murderer (b. 1952)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2020", "text": "<PERSON>, American comedian, actor (b. 1927)", "html": "2020 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American comedian, actor (b. 1927)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American comedian, actor (b. 1927)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2021", "text": "<PERSON>, American quarterback (b. 1983)", "html": "2021 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American quarterback (b. 1983)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American quarterback (b. 1983)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2021", "text": "<PERSON>, American actor, producer and director (b. 1914)", "html": "2021 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor, producer and director (b. 1914)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Lloyd\"><PERSON></a>, American actor, producer and director (b. 1914)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2024", "text": "<PERSON>, American actress and stuntwoman (b. 1946)", "html": "2024 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress and stuntwoman (b. 1946)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress and stuntwoman (b. 1946)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}]}}