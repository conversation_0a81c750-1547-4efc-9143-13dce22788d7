{"date": "March 23", "url": "https://wikipedia.org/wiki/March_23", "data": {"Events": [{"year": "1400", "text": "The Trần dynasty of Vietnam is deposed, after one hundred and seventy-five years of rule, by <PERSON><PERSON>, a court official.", "html": "1400 - The <a href=\"https://wikipedia.org/wiki/Tr%E1%BA%A7n_dynasty\" title=\"Trần dynasty\">Trần dynasty</a> of <a href=\"https://wikipedia.org/wiki/Vietnam\" title=\"Vietnam\">Vietnam</a> is deposed, after one hundred and seventy-five years of rule, by <a href=\"https://wikipedia.org/wiki/H%E1%BB%93_Qu%C3%BD_Ly\" title=\"<PERSON>ồ Quý Ly\"><PERSON><PERSON></a>, a court official.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Tr%E1%BA%A7n_dynasty\" title=\"Trần dynasty\">Trần dynasty</a> of <a href=\"https://wikipedia.org/wiki/Vietnam\" title=\"Vietnam\">Vietnam</a> is deposed, after one hundred and seventy-five years of rule, by <a href=\"https://wikipedia.org/wiki/H%E1%BB%93_Qu%C3%BD_Ly\" title=\"<PERSON>ồ Quý Ly\"><PERSON><PERSON> Ly</a>, a court official.", "links": [{"title": "Trần dynasty", "link": "https://wikipedia.org/wiki/Tr%E1%BA%A7n_dynasty"}, {"title": "Vietnam", "link": "https://wikipedia.org/wiki/Vietnam"}, {"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/H%E1%BB%93_Qu%C3%BD_Ly"}]}, {"year": "1540", "text": "Waltham Abbey is surrendered to King <PERSON> of England; the last religious community to be closed during the Dissolution of the Monasteries.", "html": "1540 - <a href=\"https://wikipedia.org/wiki/Waltham_Abbey_Church\" title=\"Waltham Abbey Church\">Waltham Abbey</a> is surrendered to King <a href=\"https://wikipedia.org/wiki/<PERSON>_VIII_of_England\" class=\"mw-redirect\" title=\"<PERSON> VIII of England\"><PERSON> VIII of England</a>; the last religious community to be closed during the <a href=\"https://wikipedia.org/wiki/Dissolution_of_the_Monasteries\" class=\"mw-redirect\" title=\"Dissolution of the Monasteries\">Dissolution of the Monasteries</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Waltham_Abbey_Church\" title=\"Waltham Abbey Church\">Waltham Abbey</a> is surrendered to King <a href=\"https://wikipedia.org/wiki/<PERSON>_VIII_of_England\" class=\"mw-redirect\" title=\"Henry VIII of England\"><PERSON> VIII of England</a>; the last religious community to be closed during the <a href=\"https://wikipedia.org/wiki/Dissolution_of_the_Monasteries\" class=\"mw-redirect\" title=\"Dissolution of the Monasteries\">Dissolution of the Monasteries</a>.", "links": [{"title": "Waltham Abbey Church", "link": "https://wikipedia.org/wiki/Waltham_Abbey_Church"}, {"title": "<PERSON> of England", "link": "https://wikipedia.org/wiki/Henry_VIII_of_England"}, {"title": "Dissolution of the Monasteries", "link": "https://wikipedia.org/wiki/Dissolution_of_the_Monasteries"}]}, {"year": "1568", "text": "The Peace of Longjumeau is signed, ending the second phase of the French Wars of Religion.", "html": "1568 - The <a href=\"https://wikipedia.org/wiki/Peace_of_Longjumeau\" title=\"Peace of Longjumeau\">Peace of Longjumeau</a> is signed, ending the second phase of the <a href=\"https://wikipedia.org/wiki/French_Wars_of_Religion\" title=\"French Wars of Religion\">French Wars of Religion</a>.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Peace_of_Longjumeau\" title=\"Peace of Longjumeau\">Peace of Longjumeau</a> is signed, ending the second phase of the <a href=\"https://wikipedia.org/wiki/French_Wars_of_Religion\" title=\"French Wars of Religion\">French Wars of Religion</a>.", "links": [{"title": "Peace of Longjumeau", "link": "https://wikipedia.org/wiki/Peace_of_Longjumeau"}, {"title": "French Wars of Religion", "link": "https://wikipedia.org/wiki/French_Wars_of_Religion"}]}, {"year": "1775", "text": "American Revolutionary War: <PERSON> delivers his speech - \"Give me liberty or give me death!\" - at St. John's Episcopal Church, Richmond, Virginia.", "html": "1775 - <a href=\"https://wikipedia.org/wiki/American_Revolutionary_War\" title=\"American Revolutionary War\">American Revolutionary War</a>: <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> delivers his speech - \"<a href=\"https://wikipedia.org/wiki/Give_me_liberty_or_give_me_death!\" title=\"Give me liberty or give me death!\">Give me liberty or give me death!</a>\" - at <a href=\"https://wikipedia.org/wiki/St._John%27s_Episcopal_Church,_Richmond,_Virginia\" class=\"mw-redirect\" title=\"St. John's Episcopal Church, Richmond, Virginia\">St. John's Episcopal Church, Richmond, Virginia</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/American_Revolutionary_War\" title=\"American Revolutionary War\">American Revolutionary War</a>: <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> delivers his speech - \"<a href=\"https://wikipedia.org/wiki/Give_me_liberty_or_give_me_death!\" title=\"Give me liberty or give me death!\">Give me liberty or give me death!</a>\" - at <a href=\"https://wikipedia.org/wiki/St._John%27s_Episcopal_Church,_Richmond,_Virginia\" class=\"mw-redirect\" title=\"St. John's Episcopal Church, Richmond, Virginia\">St. John's Episcopal Church, Richmond, Virginia</a>.", "links": [{"title": "American Revolutionary War", "link": "https://wikipedia.org/wiki/American_Revolutionary_War"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Give me liberty or give me death!", "link": "https://wikipedia.org/wiki/Give_me_liberty_or_give_me_death!"}, {"title": "St. John's Episcopal Church, Richmond, Virginia", "link": "https://wikipedia.org/wiki/St._<PERSON>%27s_Episcopal_Church,_Richmond,_Virginia"}]}, {"year": "1801", "text": "Tsar <PERSON> of Russia is struck with a sword, then strangled, and finally trampled to death inside his bedroom at St. Michael's Castle.", "html": "1801 - <a href=\"https://wikipedia.org/wiki/Tsar\" title=\"Tsar\">Tsar</a> <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Russia\" title=\"<PERSON> of Russia\"><PERSON> of Russia</a> is struck with a sword, then <a href=\"https://wikipedia.org/wiki/Strangle\" class=\"mw-redirect\" title=\"Strangle\">strangled</a>, and finally trampled to death inside his bedroom at <a href=\"https://wikipedia.org/wiki/St._Michael%27s_Castle\" class=\"mw-redirect\" title=\"St. Michael's Castle\">St. Michael's Castle</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Tsar\" title=\"Tsar\">Tsar</a> <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Russia\" title=\"<PERSON> of Russia\"><PERSON> of Russia</a> is struck with a sword, then <a href=\"https://wikipedia.org/wiki/Strangle\" class=\"mw-redirect\" title=\"Strangle\">strangled</a>, and finally trampled to death inside his bedroom at <a href=\"https://wikipedia.org/wiki/St._Michael%27s_Castle\" class=\"mw-redirect\" title=\"St. Michael's Castle\">St. Michael's Castle</a>.", "links": [{"title": "Tsar", "link": "https://wikipedia.org/wiki/Tsar"}, {"title": "<PERSON> of Russia", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Russia"}, {"title": "Strangle", "link": "https://wikipedia.org/wiki/Strangle"}, {"title": "St. Michael's Castle", "link": "https://wikipedia.org/wiki/St._<PERSON>%27s_Castle"}]}, {"year": "1806", "text": "After traveling through the Louisiana Purchase and reaching the Pacific Ocean, explorers <PERSON> and <PERSON> and their \"Corps of Discovery\" begin their arduous journey home.", "html": "1806 - After traveling through the <a href=\"https://wikipedia.org/wiki/Louisiana_Purchase\" title=\"Louisiana Purchase\">Louisiana Purchase</a> and reaching the Pacific Ocean, explorers <a href=\"https://wikipedia.org/wiki/<PERSON>_and_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON> and <PERSON>\"><PERSON> and <PERSON></a> and their \"<a href=\"https://wikipedia.org/wiki/Corps_of_Discovery\" title=\"Corps of Discovery\">Corps of Discovery</a>\" begin their arduous journey home.", "no_year_html": "After traveling through the <a href=\"https://wikipedia.org/wiki/Louisiana_Purchase\" title=\"Louisiana Purchase\">Louisiana Purchase</a> and reaching the Pacific Ocean, explorers <a href=\"https://wikipedia.org/wiki/<PERSON>_and_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON> and <PERSON>\"><PERSON> and <PERSON></a> and their \"<a href=\"https://wikipedia.org/wiki/Corps_of_Discovery\" title=\"Corps of Discovery\">Corps of Discovery</a>\" begin their arduous journey home.", "links": [{"title": "Louisiana Purchase", "link": "https://wikipedia.org/wiki/Louisiana_Purchase"}, {"title": "<PERSON> and <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_and_<PERSON>"}, {"title": "Corps of Discovery", "link": "https://wikipedia.org/wiki/Corps_of_Discovery"}]}, {"year": "1821", "text": "Greek War of Independence: Battle and fall of city of Kalamata.", "html": "1821 - <a href=\"https://wikipedia.org/wiki/Greek_War_of_Independence\" title=\"Greek War of Independence\">Greek War of Independence</a>: Battle and fall of city of <a href=\"https://wikipedia.org/wiki/Kalamata\" title=\"Kalamata\">Kalamata</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Greek_War_of_Independence\" title=\"Greek War of Independence\">Greek War of Independence</a>: Battle and fall of city of <a href=\"https://wikipedia.org/wiki/Kalamata\" title=\"Kalamata\">Kalamata</a>.", "links": [{"title": "Greek War of Independence", "link": "https://wikipedia.org/wiki/Greek_War_of_Independence"}, {"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Kalamata"}]}, {"year": "1839", "text": "A massive earthquake destroys the former capital Inwa of the Konbaung dynasty, present-day Myanmar.", "html": "1839 - A <a href=\"https://wikipedia.org/wiki/1839_Ava_earthquake\" title=\"1839 Ava earthquake\">massive earthquake</a> destroys the former capital <a href=\"https://wikipedia.org/wiki/Inwa\" title=\"Inwa\">Inwa</a> of the <a href=\"https://wikipedia.org/wiki/Konbaung_dynasty\" title=\"Konbaung dynasty\">Konbaung dynasty</a>, present-day <a href=\"https://wikipedia.org/wiki/Myanmar\" title=\"Myanmar\">Myanmar</a>.", "no_year_html": "A <a href=\"https://wikipedia.org/wiki/1839_Ava_earthquake\" title=\"1839 Ava earthquake\">massive earthquake</a> destroys the former capital <a href=\"https://wikipedia.org/wiki/Inwa\" title=\"Inwa\">Inwa</a> of the <a href=\"https://wikipedia.org/wiki/Konbaung_dynasty\" title=\"Konbaung dynasty\">Konbaung dynasty</a>, present-day <a href=\"https://wikipedia.org/wiki/Myanmar\" title=\"Myanmar\">Myanmar</a>.", "links": [{"title": "1839 Ava earthquake", "link": "https://wikipedia.org/wiki/1839_Ava_earthquake"}, {"title": "Inwa", "link": "https://wikipedia.org/wiki/Inwa"}, {"title": "Konbaung dynasty", "link": "https://wikipedia.org/wiki/Konbaung_dynasty"}, {"title": "Myanmar", "link": "https://wikipedia.org/wiki/Myanmar"}]}, {"year": "1848", "text": "The ship <PERSON> arrives at Port Chalmers carrying the first Scottish settlers for Dunedin, New Zealand. Otago province is founded.", "html": "1848 - The ship <i><a href=\"https://wikipedia.org/wiki/<PERSON>_(ship)\" title=\"<PERSON> (ship)\"><PERSON></a></i> arrives at <a href=\"https://wikipedia.org/wiki/Port_Chalmers\" title=\"Port Chalmers\">Port Chalmers</a> carrying the first Scottish settlers for <a href=\"https://wikipedia.org/wiki/Dunedin\" title=\"Dunedin\">Dunedin</a>, New Zealand. <a href=\"https://wikipedia.org/wiki/Otago\" title=\"Otago\">Otago</a> province is founded.", "no_year_html": "The ship <i><a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(ship)\" title=\"<PERSON> (ship)\"><PERSON></a></i> arrives at <a href=\"https://wikipedia.org/wiki/Port_Chalmers\" title=\"Port Chalmers\">Port Chalmers</a> carrying the first Scottish settlers for <a href=\"https://wikipedia.org/wiki/Dunedin\" title=\"Dunedin\">Dunedin</a>, New Zealand. <a href=\"https://wikipedia.org/wiki/Otago\" title=\"Otago\">Otago</a> province is founded.", "links": [{"title": "<PERSON> (ship)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(ship)"}, {"title": "Port Chalmers", "link": "https://wikipedia.org/wiki/Port_Chalmers"}, {"title": "Dunedin", "link": "https://wikipedia.org/wiki/Dunedin"}, {"title": "Otago", "link": "https://wikipedia.org/wiki/Otago"}]}, {"year": "1857", "text": "<PERSON><PERSON>'s first elevator is installed at 488 Broadway New York City.", "html": "1857 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>'s first <a href=\"https://wikipedia.org/wiki/Elevator\" title=\"Elevator\">elevator</a> is installed at 488 <a href=\"https://wikipedia.org/wiki/Broadway_(Manhattan)\" title=\"Broadway (Manhattan)\">Broadway</a> New York City.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>'s first <a href=\"https://wikipedia.org/wiki/Elevator\" title=\"Elevator\">elevator</a> is installed at 488 <a href=\"https://wikipedia.org/wiki/Broadway_(Manhattan)\" title=\"Broadway (Manhattan)\">Broadway</a> New York City.", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}, {"title": "Elevator", "link": "https://wikipedia.org/wiki/Elevator"}, {"title": "Broadway (Manhattan)", "link": "https://wikipedia.org/wiki/Broadway_(Manhattan)"}]}, {"year": "1862", "text": "American Civil War: The First Battle of Kernstown, Virginia, marks the start of Stonewall Jackson's Valley Campaign. Although a Confederate defeat, the engagement distracts Federal efforts to capture Richmond.", "html": "1862 - <a href=\"https://wikipedia.org/wiki/American_Civil_War\" title=\"American Civil War\">American Civil War</a>: The <a href=\"https://wikipedia.org/wiki/First_Battle_of_Kernstown\" title=\"First Battle of Kernstown\">First Battle of Kernstown</a>, Virginia, marks the start of <a href=\"https://wikipedia.org/wiki/Stonewall_Jackson\" title=\"Stonewall Jackson\">Stonewall Jackson</a>'s <a href=\"https://wikipedia.org/wiki/Jackson%27s_Valley_Campaign\" class=\"mw-redirect\" title=\"Jackson's Valley Campaign\">Valley Campaign</a>. Although a Confederate defeat, the engagement distracts Federal efforts to capture Richmond.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/American_Civil_War\" title=\"American Civil War\">American Civil War</a>: The <a href=\"https://wikipedia.org/wiki/First_Battle_of_Kernstown\" title=\"First Battle of Kernstown\">First Battle of Kernstown</a>, Virginia, marks the start of <a href=\"https://wikipedia.org/wiki/Stonewall_Jackson\" title=\"Stonewall Jackson\">Stone<PERSON> Jackson</a>'s <a href=\"https://wikipedia.org/wiki/Jackson%27s_Valley_Campaign\" class=\"mw-redirect\" title=\"Jackson's Valley Campaign\">Valley Campaign</a>. Although a Confederate defeat, the engagement distracts Federal efforts to capture Richmond.", "links": [{"title": "American Civil War", "link": "https://wikipedia.org/wiki/American_Civil_War"}, {"title": "First Battle of Kernstown", "link": "https://wikipedia.org/wiki/First_Battle_of_Kernstown"}, {"title": "<PERSON><PERSON> Jackson", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_Jackson"}, {"title": "Jackson's Valley Campaign", "link": "https://wikipedia.org/wiki/Jackson%27s_Valley_Campaign"}]}, {"year": "1868", "text": "The University of California is founded in Oakland, California when the Organic Act is signed into law.", "html": "1868 - The <a href=\"https://wikipedia.org/wiki/University_of_California\" title=\"University of California\">University of California</a> is founded in <a href=\"https://wikipedia.org/wiki/Oakland,_California\" title=\"Oakland, California\">Oakland, California</a> when the <a href=\"https://wikipedia.org/wiki/Organic_Act\" class=\"mw-redirect\" title=\"Organic Act\">Organic Act</a> is signed into law.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/University_of_California\" title=\"University of California\">University of California</a> is founded in <a href=\"https://wikipedia.org/wiki/Oakland,_California\" title=\"Oakland, California\">Oakland, California</a> when the <a href=\"https://wikipedia.org/wiki/Organic_Act\" class=\"mw-redirect\" title=\"Organic Act\">Organic Act</a> is signed into law.", "links": [{"title": "University of California", "link": "https://wikipedia.org/wiki/University_of_California"}, {"title": "Oakland, California", "link": "https://wikipedia.org/wiki/Oakland,_California"}, {"title": "Organic Act", "link": "https://wikipedia.org/wiki/Organic_Act"}]}, {"year": "1879", "text": "War of the Pacific: The Battle of Topáter, the first battle of the war is fought between Chile and the joint forces of Bolivia and Peru.", "html": "1879 - <a href=\"https://wikipedia.org/wiki/War_of_the_Pacific\" title=\"War of the Pacific\">War of the Pacific</a>: The <a href=\"https://wikipedia.org/wiki/Battle_of_Top%C3%A1ter\" title=\"Battle of Topáter\">Battle of Topáter</a>, the first battle of the war is fought between <a href=\"https://wikipedia.org/wiki/Chile\" title=\"Chile\">Chile</a> and the joint forces of <a href=\"https://wikipedia.org/wiki/Bolivia\" title=\"Bolivia\">Bolivia</a> and <a href=\"https://wikipedia.org/wiki/Peru\" title=\"Peru\">Peru</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/War_of_the_Pacific\" title=\"War of the Pacific\">War of the Pacific</a>: The <a href=\"https://wikipedia.org/wiki/Battle_of_Top%C3%A1ter\" title=\"Battle of Topáter\">Battle of Topáter</a>, the first battle of the war is fought between <a href=\"https://wikipedia.org/wiki/Chile\" title=\"Chile\">Chile</a> and the joint forces of <a href=\"https://wikipedia.org/wiki/Bolivia\" title=\"Bolivia\">Bolivia</a> and <a href=\"https://wikipedia.org/wiki/Peru\" title=\"Peru\">Peru</a>.", "links": [{"title": "War of the Pacific", "link": "https://wikipedia.org/wiki/War_of_the_Pacific"}, {"title": "Battle of Topáter", "link": "https://wikipedia.org/wiki/Battle_of_Top%C3%A1ter"}, {"title": "Chile", "link": "https://wikipedia.org/wiki/Chile"}, {"title": "Bolivia", "link": "https://wikipedia.org/wiki/Bolivia"}, {"title": "Peru", "link": "https://wikipedia.org/wiki/Peru"}]}, {"year": "1885", "text": "Sino-French War: Chinese victory in the Battle of Phu Lam Tao near Hưng Hóa, northern Vietnam.", "html": "1885 - <a href=\"https://wikipedia.org/wiki/Sino-French_War\" title=\"Sino-French War\">Sino-French War</a>: Chinese victory in the <a href=\"https://wikipedia.org/wiki/Battle_of_Phu_Lam_Tao\" title=\"Battle of Phu Lam Tao\">Battle of Phu Lam Tao</a> near Hưng Hóa, northern <a href=\"https://wikipedia.org/wiki/Vietnam\" title=\"Vietnam\">Vietnam</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Sino-French_War\" title=\"Sino-French War\">Sino-French War</a>: Chinese victory in the <a href=\"https://wikipedia.org/wiki/Battle_of_Phu_Lam_Tao\" title=\"Battle of Phu Lam Tao\">Battle of Phu Lam Tao</a> near Hưng Hóa, northern <a href=\"https://wikipedia.org/wiki/Vietnam\" title=\"Vietnam\">Vietnam</a>.", "links": [{"title": "Sino-French War", "link": "https://wikipedia.org/wiki/Sino-French_War"}, {"title": "Battle of Phu Lam Tao", "link": "https://wikipedia.org/wiki/Battle_of_<PERSON><PERSON>_<PERSON>_<PERSON>"}, {"title": "Vietnam", "link": "https://wikipedia.org/wiki/Vietnam"}]}, {"year": "1888", "text": "In England, The Football League, the world's oldest professional association football league, meets for the first time.", "html": "1888 - In England, <a href=\"https://wikipedia.org/wiki/English_Football_League\" title=\"English Football League\">The Football League</a>, the world's oldest professional <a href=\"https://wikipedia.org/wiki/Association_football\" title=\"Association football\">association football</a> league, meets for the first time.", "no_year_html": "In England, <a href=\"https://wikipedia.org/wiki/English_Football_League\" title=\"English Football League\">The Football League</a>, the world's oldest professional <a href=\"https://wikipedia.org/wiki/Association_football\" title=\"Association football\">association football</a> league, meets for the first time.", "links": [{"title": "English Football League", "link": "https://wikipedia.org/wiki/English_Football_League"}, {"title": "Association football", "link": "https://wikipedia.org/wiki/Association_football"}]}, {"year": "1889", "text": "The Ahmadiyya Muslim Community is established by <PERSON> in Qadian, British India.", "html": "1889 - The <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>\" title=\"Ahmad<PERSON><PERSON>\">Ahmadiyya</a> Muslim Community is established by <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> in <a href=\"https://wikipedia.org/wiki/Qadian\" title=\"Qadian\">Qadian, British India</a>.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>\" title=\"Ahmad<PERSON><PERSON>\">Ahmadiyya</a> Muslim Community is established by <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> in <a href=\"https://wikipedia.org/wiki/Qadian\" title=\"Qadian\">Qadian, British India</a>.", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON><PERSON>_<PERSON>"}, {"title": "Qadian", "link": "https://wikipedia.org/wiki/Qadian"}]}, {"year": "1901", "text": "<PERSON>, only President of the First Philippine Republic, is captured at Palanan, Isabela by the forces of American General <PERSON>.", "html": "1901 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, only President of the <a href=\"https://wikipedia.org/wiki/First_Philippine_Republic\" title=\"First Philippine Republic\">First Philippine Republic</a>, is captured at <a href=\"https://wikipedia.org/wiki/Palanan,_Isabela\" class=\"mw-redirect\" title=\"Palanan, Isabela\">Palanan, Isabela</a> by the forces of American General <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, only President of the <a href=\"https://wikipedia.org/wiki/First_Philippine_Republic\" title=\"First Philippine Republic\">First Philippine Republic</a>, is captured at <a href=\"https://wikipedia.org/wiki/Palanan,_Isabela\" class=\"mw-redirect\" title=\"Palanan, Isabela\">Palanan, Isabela</a> by the forces of American General <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "First Philippine Republic", "link": "https://wikipedia.org/wiki/First_Philippine_Republic"}, {"title": "Palanan, Isabela", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>,_Isabela"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1905", "text": "<PERSON><PERSON><PERSON><PERSON> calls for Crete's union with Greece, and begins what is to be known as the Theriso revolt.", "html": "1905 - <a href=\"https://wikipedia.org/wiki/Eleftherios_Venizelos\" title=\"Eleftherios Venizelos\">Eleftherios Venizelos</a> calls for <a href=\"https://wikipedia.org/wiki/Crete\" title=\"Crete\">Crete</a>'s union with Greece, and begins what is to be known as the <a href=\"https://wikipedia.org/wiki/Theriso_revolt\" title=\"Theriso revolt\">Theriso revolt</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Eleftherios_Venizelos\" title=\"Eleftherios Venizelos\">Eleftherios Venizelos</a> calls for <a href=\"https://wikipedia.org/wiki/Crete\" title=\"Crete\">Crete</a>'s union with Greece, and begins what is to be known as the <a href=\"https://wikipedia.org/wiki/Theriso_revolt\" title=\"Theriso revolt\">Theriso revolt</a>.", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Eleftherios_Venizelos"}, {"title": "Crete", "link": "https://wikipedia.org/wiki/Crete"}, {"title": "Theriso revolt", "link": "https://wikipedia.org/wiki/Theriso_revolt"}]}, {"year": "1909", "text": "<PERSON> leaves New York for a post-presidency safari in Africa. The trip is sponsored by the Smithsonian Institution and National Geographic Society.", "html": "1909 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> leaves New York for a post-<a href=\"https://wikipedia.org/wiki/Presidency\" title=\"Presidency\">presidency</a> <a href=\"https://wikipedia.org/wiki/Smithsonian%E2%80%93Roosevelt_African_Expedition\" class=\"mw-redirect\" title=\"Smithsonian-Roosevelt African Expedition\">safari</a> in Africa. The trip is sponsored by the <a href=\"https://wikipedia.org/wiki/Smithsonian_Institution\" title=\"Smithsonian Institution\">Smithsonian Institution</a> and <a href=\"https://wikipedia.org/wiki/National_Geographic_Society\" title=\"National Geographic Society\">National Geographic Society</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> leaves New York for a post-<a href=\"https://wikipedia.org/wiki/Presidency\" title=\"Presidency\">presidency</a> <a href=\"https://wikipedia.org/wiki/Smithsonian%E2%80%93Roosevelt_African_Expedition\" class=\"mw-redirect\" title=\"Smithsonian-Roosevelt African Expedition\">safari</a> in Africa. The trip is sponsored by the <a href=\"https://wikipedia.org/wiki/Smithsonian_Institution\" title=\"Smithsonian Institution\">Smithsonian Institution</a> and <a href=\"https://wikipedia.org/wiki/National_Geographic_Society\" title=\"National Geographic Society\">National Geographic Society</a>.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}, {"title": "Presidency", "link": "https://wikipedia.org/wiki/Presidency"}, {"title": "Smithsonian-Roosevelt African Expedition", "link": "https://wikipedia.org/wiki/Smithsonian%E2%80%93Roosevelt_African_Expedition"}, {"title": "Smithsonian Institution", "link": "https://wikipedia.org/wiki/Smithsonian_Institution"}, {"title": "National Geographic Society", "link": "https://wikipedia.org/wiki/National_Geographic_Society"}]}, {"year": "1913", "text": "A tornado outbreak kills more than 240 people in the central United States, while an ongoing flood in the Ohio River watershed was killing 650 people.", "html": "1913 - A <a href=\"https://wikipedia.org/wiki/Tornado_outbreak_sequence_of_March_1913\" class=\"mw-redirect\" title=\"Tornado outbreak sequence of March 1913\">tornado outbreak</a> kills more than 240 people in the central United States, while an ongoing <a href=\"https://wikipedia.org/wiki/Great_Flood_of_1913\" title=\"Great Flood of 1913\">flood</a> in the Ohio River watershed was killing 650 people.", "no_year_html": "A <a href=\"https://wikipedia.org/wiki/Tornado_outbreak_sequence_of_March_1913\" class=\"mw-redirect\" title=\"Tornado outbreak sequence of March 1913\">tornado outbreak</a> kills more than 240 people in the central United States, while an ongoing <a href=\"https://wikipedia.org/wiki/Great_Flood_of_1913\" title=\"Great Flood of 1913\">flood</a> in the Ohio River watershed was killing 650 people.", "links": [{"title": "Tornado outbreak sequence of March 1913", "link": "https://wikipedia.org/wiki/Tornado_outbreak_sequence_of_March_1913"}, {"title": "Great Flood of 1913", "link": "https://wikipedia.org/wiki/Great_Flood_of_1913"}]}, {"year": "1918", "text": "First World War: On the third day of the German Spring Offensive, the 10th Battalion of the Royal West Kent Regiment is annihilated with many of the men becoming prisoners of war", "html": "1918 - <a href=\"https://wikipedia.org/wiki/First_World_War\" class=\"mw-redirect\" title=\"First World War\">First World War</a>: On the third day of the German <a href=\"https://wikipedia.org/wiki/Operation_Michael\" title=\"Operation Michael\">Spring Offensive</a>, the 10th Battalion of the <a href=\"https://wikipedia.org/wiki/Royal_West_Kent_Regiment\" class=\"mw-redirect\" title=\"Royal West Kent Regiment\">Royal West Kent Regiment</a> is annihilated with many of the men becoming <a href=\"https://wikipedia.org/wiki/Prisoners_of_war\" class=\"mw-redirect\" title=\"Prisoners of war\">prisoners of war</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/First_World_War\" class=\"mw-redirect\" title=\"First World War\">First World War</a>: On the third day of the German <a href=\"https://wikipedia.org/wiki/Operation_Michael\" title=\"Operation Michael\">Spring Offensive</a>, the 10th Battalion of the <a href=\"https://wikipedia.org/wiki/Royal_West_Kent_Regiment\" class=\"mw-redirect\" title=\"Royal West Kent Regiment\">Royal West Kent Regiment</a> is annihilated with many of the men becoming <a href=\"https://wikipedia.org/wiki/Prisoners_of_war\" class=\"mw-redirect\" title=\"Prisoners of war\">prisoners of war</a>", "links": [{"title": "First World War", "link": "https://wikipedia.org/wiki/First_World_War"}, {"title": "Operation Michael", "link": "https://wikipedia.org/wiki/Operation_Michael"}, {"title": "Royal West Kent Regiment", "link": "https://wikipedia.org/wiki/Royal_West_Kent_Regiment"}, {"title": "Prisoners of war", "link": "https://wikipedia.org/wiki/Prisoners_of_war"}]}, {"year": "1919", "text": "In Milan, Italy, <PERSON> founds his Fascist political movement.", "html": "1919 - In <a href=\"https://wikipedia.org/wiki/Milan\" title=\"Milan\">Milan</a>, Italy, <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Benito <PERSON>\"><PERSON></a> founds his <a href=\"https://wikipedia.org/wiki/Italian_Fascism\" class=\"mw-redirect\" title=\"Italian Fascism\">Fascist</a> political movement.", "no_year_html": "In <a href=\"https://wikipedia.org/wiki/Milan\" title=\"Milan\">Milan</a>, Italy, <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Benito <PERSON>\"><PERSON></a> founds his <a href=\"https://wikipedia.org/wiki/Italian_Fascism\" class=\"mw-redirect\" title=\"Italian Fascism\">Fascist</a> political movement.", "links": [{"title": "Milan", "link": "https://wikipedia.org/wiki/Milan"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Italian Fascism", "link": "https://wikipedia.org/wiki/Italian_Fascism"}]}, {"year": "1931", "text": "<PERSON><PERSON><PERSON>, <PERSON><PERSON> and <PERSON><PERSON><PERSON><PERSON> are hanged for the killing of a deputy superintendent of police during the Indian independence movement.", "html": "1931 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a> and <a href=\"https://wikipedia.org/wiki/Sukhdev_Thapar\" title=\"Sukhdev Thapar\">Sukhdev Thapar</a> are hanged for the killing of a deputy superintendent of police during the <a href=\"https://wikipedia.org/wiki/Indian_independence_movement\" title=\"Indian independence movement\">Indian independence movement</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_Raj<PERSON>\" title=\"<PERSON><PERSON>gu<PERSON>\"><PERSON><PERSON></a> and <a href=\"https://wikipedia.org/wiki/Sukhdev_Thapar\" title=\"Sukhdev Thapar\">Sukhdev Thapar</a> are hanged for the killing of a deputy superintendent of police during the <a href=\"https://wikipedia.org/wiki/Indian_independence_movement\" title=\"Indian independence movement\">Indian independence movement</a>.", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}, {"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}, {"title": "<PERSON><PERSON><PERSON><PERSON> T<PERSON>par", "link": "https://wikipedia.org/wiki/Sukhdev_Thapar"}, {"title": "Indian independence movement", "link": "https://wikipedia.org/wiki/Indian_independence_movement"}]}, {"year": "1933", "text": "The Reichstag passes the Enabling Act of 1933, making <PERSON> dictator of Germany.", "html": "1933 - The <i><a href=\"https://wikipedia.org/wiki/Reichstag_(Weimar_Republic)\" title=\"Reichstag (Weimar Republic)\">Reichstag</a></i> passes the <a href=\"https://wikipedia.org/wiki/Enabling_Act_of_1933\" title=\"Enabling Act of 1933\">Enabling Act of 1933</a>, making <a href=\"https://wikipedia.org/wiki/Adolf_Hitler\" title=\"Adolf Hitler\"><PERSON></a> dictator of Germany.", "no_year_html": "The <i><a href=\"https://wikipedia.org/wiki/Reichstag_(Weimar_Republic)\" title=\"Reichstag (Weimar Republic)\">Reichstag</a></i> passes the <a href=\"https://wikipedia.org/wiki/Enabling_Act_of_1933\" title=\"Enabling Act of 1933\">Enabling Act of 1933</a>, making <a href=\"https://wikipedia.org/wiki/Adolf_Hitler\" title=\"Adolf <PERSON>\"><PERSON></a> dictator of Germany.", "links": [{"title": "Reichstag (Weimar Republic)", "link": "https://wikipedia.org/wiki/Reichstag_(Weimar_Republic)"}, {"title": "Enabling Act of 1933", "link": "https://wikipedia.org/wiki/Enabling_Act_of_1933"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1935", "text": "Signing of the Constitution of the Commonwealth of the Philippines.", "html": "1935 - Signing of the <a href=\"https://wikipedia.org/wiki/Constitution_of_the_Philippines#Commonwealth_and_Third_Republic\" title=\"Constitution of the Philippines\">Constitution</a> of the <a href=\"https://wikipedia.org/wiki/Commonwealth_of_the_Philippines\" title=\"Commonwealth of the Philippines\">Commonwealth of the Philippines</a>.", "no_year_html": "Signing of the <a href=\"https://wikipedia.org/wiki/Constitution_of_the_Philippines#Commonwealth_and_Third_Republic\" title=\"Constitution of the Philippines\">Constitution</a> of the <a href=\"https://wikipedia.org/wiki/Commonwealth_of_the_Philippines\" title=\"Commonwealth of the Philippines\">Commonwealth of the Philippines</a>.", "links": [{"title": "Constitution of the Philippines", "link": "https://wikipedia.org/wiki/Constitution_of_the_Philippines#Commonwealth_and_Third_Republic"}, {"title": "Commonwealth of the Philippines", "link": "https://wikipedia.org/wiki/Commonwealth_of_the_Philippines"}]}, {"year": "1939", "text": "The Hungarian air force attacks the headquarters of the Slovak air force in Spišská Nová Ves, killing 13 people and beginning the Slovak-Hungarian War.", "html": "1939 - The <a href=\"https://wikipedia.org/wiki/Kingdom_of_Hungary_(1920%E2%80%9346)\" class=\"mw-redirect\" title=\"Kingdom of Hungary (1920-46)\">Hungarian</a> air force attacks the headquarters of the <a href=\"https://wikipedia.org/wiki/Slovak_Air_Force_(1939%E2%80%9345)\" class=\"mw-redirect\" title=\"Slovak Air Force (1939-45)\">Slovak air force</a> in <a href=\"https://wikipedia.org/wiki/Spi%C5%A1sk%C3%A1_Nov%C3%A1_Ves\" title=\"Spišská Nová Ves\">Spišská Nová Ves</a>, killing 13 people and beginning the <a href=\"https://wikipedia.org/wiki/Slovak%E2%80%93Hungarian_War\" title=\"Slovak-Hungarian War\">Slovak-Hungarian War</a>.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Kingdom_of_Hungary_(1920%E2%80%9346)\" class=\"mw-redirect\" title=\"Kingdom of Hungary (1920-46)\">Hungarian</a> air force attacks the headquarters of the <a href=\"https://wikipedia.org/wiki/Slovak_Air_Force_(1939%E2%80%9345)\" class=\"mw-redirect\" title=\"Slovak Air Force (1939-45)\">Slovak air force</a> in <a href=\"https://wikipedia.org/wiki/Spi%C5%A1sk%C3%A1_Nov%C3%A1_Ves\" title=\"Spišská Nová Ves\">Spišsk<PERSON> Nová Ves</a>, killing 13 people and beginning the <a href=\"https://wikipedia.org/wiki/Slovak%E2%80%93Hungarian_War\" title=\"Slovak-Hungarian War\">Slovak-Hungarian War</a>.", "links": [{"title": "Kingdom of Hungary (1920-46)", "link": "https://wikipedia.org/wiki/Kingdom_of_Hungary_(1920%E2%80%9346)"}, {"title": "Slovak Air Force (1939-45)", "link": "https://wikipedia.org/wiki/Slovak_Air_Force_(1939%E2%80%9345)"}, {"title": "Spišská Nová Ves", "link": "https://wikipedia.org/wiki/Spi%C5%A1sk%C3%A1_Nov%C3%A1_Ves"}, {"title": "Slovak-Hungarian War", "link": "https://wikipedia.org/wiki/Slovak%E2%80%93Hungarian_War"}]}, {"year": "1940", "text": "The Lahore Resolution (Qarardad-e-Pakistan or Qarardad-e-Lahore) is put forward at the Annual General Convention of the All-India Muslim League.", "html": "1940 - The <a href=\"https://wikipedia.org/wiki/Lahore_Resolution\" title=\"Lahore Resolution\">Lahore Resolution</a> (<i>Qarardad-e-Pakistan</i> or <i>Qarardad-e-Lahore</i>) is put forward at the Annual General Convention of the <a href=\"https://wikipedia.org/wiki/All-India_Muslim_League\" title=\"All-India Muslim League\">All-India Muslim League</a>.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Lahore_Resolution\" title=\"Lahore Resolution\">Lahore Resolution</a> (<i>Qarardad-e-Pakistan</i> or <i>Qarardad-e-Lahore</i>) is put forward at the Annual General Convention of the <a href=\"https://wikipedia.org/wiki/All-India_Muslim_League\" title=\"All-India Muslim League\">All-India Muslim League</a>.", "links": [{"title": "Lahore Resolution", "link": "https://wikipedia.org/wiki/Lahore_Resolution"}, {"title": "All-India Muslim League", "link": "https://wikipedia.org/wiki/All-India_Muslim_League"}]}, {"year": "1956", "text": "Pakistan becomes the first Islamic republic in the world. This date is now celebrated as Republic Day in Pakistan.", "html": "1956 - <a href=\"https://wikipedia.org/wiki/Pakistan\" title=\"Pakistan\">Pakistan</a> becomes the first <a href=\"https://wikipedia.org/wiki/Islamic_republic\" title=\"Islamic republic\">Islamic republic</a> in the world. This date is now celebrated as <a href=\"https://wikipedia.org/wiki/Republic_Day#March_23rd_in_Pakistan\" title=\"Republic Day\">Republic Day</a> in Pakistan.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Pakistan\" title=\"Pakistan\">Pakistan</a> becomes the first <a href=\"https://wikipedia.org/wiki/Islamic_republic\" title=\"Islamic republic\">Islamic republic</a> in the world. This date is now celebrated as <a href=\"https://wikipedia.org/wiki/Republic_Day#March_23rd_in_Pakistan\" title=\"Republic Day\">Republic Day</a> in Pakistan.", "links": [{"title": "Pakistan", "link": "https://wikipedia.org/wiki/Pakistan"}, {"title": "Islamic republic", "link": "https://wikipedia.org/wiki/Islamic_republic"}, {"title": "Republic Day", "link": "https://wikipedia.org/wiki/Republic_Day#March_23rd_in_Pakistan"}]}, {"year": "1965", "text": "NASA launches Gemini 3, the United States' first two-man space flight (crew: <PERSON> and <PERSON>).", "html": "1965 - <a href=\"https://wikipedia.org/wiki/NASA\" title=\"NASA\">NASA</a> launches <a href=\"https://wikipedia.org/wiki/Gemini_3\" title=\"Gemini 3\">Gemini 3</a>, the United States' first two-man space flight (crew: <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> and <a href=\"https://wikipedia.org/wiki/<PERSON>(astronaut)\" title=\"<PERSON> (astronaut)\"><PERSON></a>).", "no_year_html": "<a href=\"https://wikipedia.org/wiki/NASA\" title=\"NASA\">NASA</a> launches <a href=\"https://wikipedia.org/wiki/Gemini_3\" title=\"Gemini 3\">Gemini 3</a>, the United States' first two-man space flight (crew: <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> and <a href=\"https://wikipedia.org/wiki/<PERSON>(astronaut)\" title=\"<PERSON> (astronaut)\"><PERSON></a>).", "links": [{"title": "NASA", "link": "https://wikipedia.org/wiki/NASA"}, {"title": "Gemini 3", "link": "https://wikipedia.org/wiki/Gemini_3"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "<PERSON> (astronaut)", "link": "https://wikipedia.org/wiki/<PERSON>(astronaut)"}]}, {"year": "1977", "text": "The first of The <PERSON> Interviews (12 will be recorded over four weeks) is videotaped with British journalist <PERSON> interviewing former United States President <PERSON> about the Watergate scandal and the <PERSON> tapes.", "html": "1977 - The first of <a href=\"https://wikipedia.org/wiki/The_Nixon_Interviews\" class=\"mw-redirect\" title=\"The Nixon Interviews\">The Nixon Interviews</a> (12 will be recorded over four weeks) is videotaped with British journalist <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> interviewing former United States President <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> about the <a href=\"https://wikipedia.org/wiki/Watergate_scandal\" title=\"Watergate scandal\">Watergate scandal</a> and the <a href=\"https://wikipedia.org/wiki/Nixon_tapes\" class=\"mw-redirect\" title=\"Nixon tapes\">Nixon tapes</a>.", "no_year_html": "The first of <a href=\"https://wikipedia.org/wiki/The_Nixon_Interviews\" class=\"mw-redirect\" title=\"The Nixon Interviews\">The Nixon Interviews</a> (12 will be recorded over four weeks) is videotaped with British journalist <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> interviewing former United States President <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> about the <a href=\"https://wikipedia.org/wiki/Watergate_scandal\" title=\"Watergate scandal\">Watergate scandal</a> and the <a href=\"https://wikipedia.org/wiki/Nixon_tapes\" class=\"mw-redirect\" title=\"Nixon tapes\">Nixon tapes</a>.", "links": [{"title": "The Nixon Interviews", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_Interviews"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Watergate scandal", "link": "https://wikipedia.org/wiki/Watergate_scandal"}, {"title": "<PERSON> tapes", "link": "https://wikipedia.org/wiki/Nixon_tapes"}]}, {"year": "1978", "text": "The first UNIFIL troops arrived in Lebanon for peacekeeping mission along the Blue Line.", "html": "1978 - The first <a href=\"https://wikipedia.org/wiki/UNIFIL\" class=\"mw-redirect\" title=\"UNIFIL\">UNIFIL</a> troops arrived in <a href=\"https://wikipedia.org/wiki/Lebanon\" title=\"Lebanon\">Lebanon</a> for <a href=\"https://wikipedia.org/wiki/Timeline_of_UN_peacekeeping_missions\" class=\"mw-redirect\" title=\"Timeline of UN peacekeeping missions\">peacekeeping mission</a> along the <a href=\"https://wikipedia.org/wiki/Blue_Line_(Lebanon)\" class=\"mw-redirect\" title=\"Blue Line (Lebanon)\">Blue Line</a>.", "no_year_html": "The first <a href=\"https://wikipedia.org/wiki/UNIFIL\" class=\"mw-redirect\" title=\"UNIFIL\">UNIFIL</a> troops arrived in <a href=\"https://wikipedia.org/wiki/Lebanon\" title=\"Lebanon\">Lebanon</a> for <a href=\"https://wikipedia.org/wiki/Timeline_of_UN_peacekeeping_missions\" class=\"mw-redirect\" title=\"Timeline of UN peacekeeping missions\">peacekeeping mission</a> along the <a href=\"https://wikipedia.org/wiki/Blue_Line_(Lebanon)\" class=\"mw-redirect\" title=\"Blue Line (Lebanon)\">Blue Line</a>.", "links": [{"title": "UNIFIL", "link": "https://wikipedia.org/wiki/UNIFIL"}, {"title": "Lebanon", "link": "https://wikipedia.org/wiki/Lebanon"}, {"title": "Timeline of UN peacekeeping missions", "link": "https://wikipedia.org/wiki/Timeline_of_UN_peacekeeping_missions"}, {"title": "Blue Line (Lebanon)", "link": "https://wikipedia.org/wiki/Blue_Line_(Lebanon)"}]}, {"year": "1980", "text": "Archbishop <PERSON><PERSON><PERSON> of El Salvador gives his famous speech appealing to men of the El Salvadoran armed forces to stop killing the Salvadorans.", "html": "1980 - Archbishop <a href=\"https://wikipedia.org/wiki/%C3%93<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a> of <a href=\"https://wikipedia.org/wiki/El_Salvador\" title=\"El Salvador\">El Salvador</a> gives his famous speech appealing to men of the El Salvadoran armed forces to stop killing the Salvadorans.", "no_year_html": "Archbishop <a href=\"https://wikipedia.org/wiki/%C3%93<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a> of <a href=\"https://wikipedia.org/wiki/El_Salvador\" title=\"El Salvador\">El Salvador</a> gives his famous speech appealing to men of the El Salvadoran armed forces to stop killing the Salvadorans.", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/%C3%93scar_<PERSON>"}, {"title": "El Salvador", "link": "https://wikipedia.org/wiki/El_Salvador"}]}, {"year": "1982", "text": "Guatemala's government, headed by <PERSON> is overthrown in a military coup by right-wing General <PERSON><PERSON><PERSON>.", "html": "1982 - <a href=\"https://wikipedia.org/wiki/Guatemala\" title=\"Guatemala\">Guatemala</a>'s government, headed by <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>%C3%ADa\" title=\"<PERSON>\"><PERSON></a> is overthrown in a military coup by right-wing General <a href=\"https://wikipedia.org/wiki/Efra%C3%ADn_R%C3%ADos_Montt\" title=\"Efraín <PERSON>s Montt\"><PERSON><PERSON><PERSON></a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Guatemala\" title=\"Guatemala\">Guatemala</a>'s government, headed by <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_Gar<PERSON>%C3%ADa\" title=\"<PERSON>\"><PERSON></a> is overthrown in a military coup by right-wing General <a href=\"https://wikipedia.org/wiki/Efra%C3%ADn_R%C3%ADos_Montt\" title=\"Efraín Ríos Montt\"><PERSON><PERSON><PERSON></a>.", "links": [{"title": "Guatemala", "link": "https://wikipedia.org/wiki/Guatemala"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>%C3%ADa"}, {"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Efra%C3%ADn_R%C3%ADos_Montt"}]}, {"year": "1983", "text": "Strategic Defense Initiative: President <PERSON> makes his initial proposal to develop technology to intercept enemy missiles.", "html": "1983 - <a href=\"https://wikipedia.org/wiki/Strategic_Defense_Initiative\" title=\"Strategic Defense Initiative\">Strategic Defense Initiative</a>: President <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> makes his initial proposal to develop technology to intercept enemy missiles.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Strategic_Defense_Initiative\" title=\"Strategic Defense Initiative\">Strategic Defense Initiative</a>: President <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> makes his initial proposal to develop technology to intercept enemy missiles.", "links": [{"title": "Strategic Defense Initiative", "link": "https://wikipedia.org/wiki/Strategic_Defense_Initiative"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1988", "text": "Angolan and Cuban forces defeat South Africa in the Battle of Cuito Cuanavale.", "html": "1988 - Angolan and Cuban forces defeat South Africa in the <a href=\"https://wikipedia.org/wiki/Battle_of_Cuito_Cuanavale\" title=\"Battle of Cuito Cuanavale\">Battle of Cuito Cuanavale</a>.", "no_year_html": "Angolan and Cuban forces defeat South Africa in the <a href=\"https://wikipedia.org/wiki/Battle_of_Cuito_Cuanavale\" title=\"Battle of Cuito Cuanavale\">Battle of Cuito Cuanavale</a>.", "links": [{"title": "Battle of Cuito Cuanavale", "link": "https://wikipedia.org/wiki/Battle_of_Cuito_Cuanavale"}]}, {"year": "1991", "text": "The Revolutionary United Front, with support from the special forces of <PERSON>'s National Patriotic Front of Liberia, invades Sierra Leone in an attempt to overthrow <PERSON>, sparking the 11-year Sierra Leone Civil War.", "html": "1991 - The <a href=\"https://wikipedia.org/wiki/Revolutionary_United_Front\" title=\"Revolutionary United Front\">Revolutionary United Front</a>, with support from the special forces of <a href=\"https://wikipedia.org/wiki/<PERSON>(Liberian_politician)\" title=\"<PERSON> (Liberian politician)\"><PERSON></a>'s <a href=\"https://wikipedia.org/wiki/National_Patriotic_Front_of_Liberia\" title=\"National Patriotic Front of Liberia\">National Patriotic Front of Liberia</a>, invades <a href=\"https://wikipedia.org/wiki/Sierra_Leone\" title=\"Sierra Leone\">Sierra Leone</a> in an attempt to overthrow <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, sparking the 11-year <a href=\"https://wikipedia.org/wiki/Sierra_Leone_Civil_War\" title=\"Sierra Leone Civil War\">Sierra Leone Civil War</a>.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Revolutionary_United_Front\" title=\"Revolutionary United Front\">Revolutionary United Front</a>, with support from the special forces of <a href=\"https://wikipedia.org/wiki/<PERSON>(Liberian_politician)\" title=\"<PERSON> (Liberian politician)\"><PERSON></a>'s <a href=\"https://wikipedia.org/wiki/National_Patriotic_Front_of_Liberia\" title=\"National Patriotic Front of Liberia\">National Patriotic Front of Liberia</a>, invades <a href=\"https://wikipedia.org/wiki/Sierra_Leone\" title=\"Sierra Leone\">Sierra Leone</a> in an attempt to overthrow <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, sparking the 11-year <a href=\"https://wikipedia.org/wiki/Sierra_Leone_Civil_War\" title=\"Sierra Leone Civil War\">Sierra Leone Civil War</a>.", "links": [{"title": "Revolutionary United Front", "link": "https://wikipedia.org/wiki/Revolutionary_United_Front"}, {"title": "<PERSON> (Liberian politician)", "link": "https://wikipedia.org/wiki/<PERSON>_(Liberian_politician)"}, {"title": "National Patriotic Front of Liberia", "link": "https://wikipedia.org/wiki/National_Patriotic_Front_of_Liberia"}, {"title": "Sierra Leone", "link": "https://wikipedia.org/wiki/Sierra_Leone"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "Sierra Leone Civil War", "link": "https://wikipedia.org/wiki/Sierra_Leone_Civil_War"}]}, {"year": "1994", "text": "At an election rally in Tijuana, Mexican presidential candidate <PERSON> is assassinated by <PERSON>.", "html": "1994 - At an election rally in <a href=\"https://wikipedia.org/wiki/Tijuana\" title=\"Tijuana\">Tijuana</a>, Mexican presidential candidate <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a> is assassinated by <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>%C3%ADnez\" title=\"<PERSON>\"><PERSON></a>.", "no_year_html": "At an election rally in <a href=\"https://wikipedia.org/wiki/Tijuana\" title=\"Tijuana\">Tijuana</a>, Mexican presidential candidate <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a> is assassinated by <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>%C3%ADnez\" title=\"<PERSON>\"><PERSON></a>.", "links": [{"title": "Tijuana", "link": "https://wikipedia.org/wiki/Tijuana"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Mario_Aburto_Mart%C3%ADnez"}]}, {"year": "1994", "text": "A United States Air Force (USAF) F-16 aircraft collides with a USAF C-130 at Pope Air Force Base and then crashes, killing 24 United States Army soldiers on the ground. This later became known as the Green Ramp disaster.", "html": "1994 - A United States Air Force (USAF) <a href=\"https://wikipedia.org/wiki/General_Dynamics_F-16_Fighting_Falcon\" title=\"General Dynamics F-16 Fighting Falcon\">F-16</a> aircraft collides with a USAF <a href=\"https://wikipedia.org/wiki/C-130_Hercules\" class=\"mw-redirect\" title=\"C-130 Hercules\">C-130</a> at <a href=\"https://wikipedia.org/wiki/Pope_Air_Force_Base\" class=\"mw-redirect\" title=\"Pope Air Force Base\">Pope Air Force Base</a> and then crashes, killing 24 United States Army soldiers on the ground. This later became known as the <a href=\"https://wikipedia.org/wiki/Green_Ramp_disaster\" title=\"Green Ramp disaster\">Green Ramp disaster</a>.", "no_year_html": "A United States Air Force (USAF) <a href=\"https://wikipedia.org/wiki/General_Dynamics_F-16_Fighting_Falcon\" title=\"General Dynamics F-16 Fighting Falcon\">F-16</a> aircraft collides with a USAF <a href=\"https://wikipedia.org/wiki/C-130_Hercules\" class=\"mw-redirect\" title=\"C-130 Hercules\">C-130</a> at <a href=\"https://wikipedia.org/wiki/Pope_Air_Force_Base\" class=\"mw-redirect\" title=\"Pope Air Force Base\">Pope Air Force Base</a> and then crashes, killing 24 United States Army soldiers on the ground. This later became known as the <a href=\"https://wikipedia.org/wiki/Green_Ramp_disaster\" title=\"Green Ramp disaster\">Green Ramp disaster</a>.", "links": [{"title": "General Dynamics F-16 Fighting Falcon", "link": "https://wikipedia.org/wiki/General_Dynamics_F-16_Fighting_Falcon"}, {"title": "C-130 Hercules", "link": "https://wikipedia.org/wiki/C-130_Hercules"}, {"title": "Pope Air Force Base", "link": "https://wikipedia.org/wiki/Pope_Air_Force_Base"}, {"title": "Green Ramp disaster", "link": "https://wikipedia.org/wiki/Green_Ramp_disaster"}]}, {"year": "1994", "text": "Aeroflot Flight 593 crashed into the Kuznetsk Alatau mountain, Kemerovo Oblast, Russia, killing 75.", "html": "1994 - <a href=\"https://wikipedia.org/wiki/Aeroflot_Flight_593\" title=\"Aeroflot Flight 593\">Aeroflot Flight 593</a> crashed into the Kuznetsk Alatau mountain, Kemerovo Oblast, Russia, killing 75.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Aeroflot_Flight_593\" title=\"Aeroflot Flight 593\">Aeroflot Flight 593</a> crashed into the Kuznetsk Alatau mountain, Kemerovo Oblast, Russia, killing 75.", "links": [{"title": "Aeroflot Flight 593", "link": "https://wikipedia.org/wiki/Aeroflot_Flight_593"}]}, {"year": "1996", "text": "Taiwan holds its first direct elections and chooses <PERSON> as President.", "html": "1996 - <a href=\"https://wikipedia.org/wiki/Taiwan\" title=\"Taiwan\">Taiwan</a> holds its <a href=\"https://wikipedia.org/wiki/1996_Taiwanese_presidential_election\" title=\"1996 Taiwanese presidential election\">first direct elections</a> and chooses <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>-hui\" title=\"<PERSON>-hui\"><PERSON>-<PERSON></a> as <a href=\"https://wikipedia.org/wiki/President_of_the_Republic_of_China\" title=\"President of the Republic of China\">President</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Taiwan\" title=\"Taiwan\">Taiwan</a> holds its <a href=\"https://wikipedia.org/wiki/1996_Taiwanese_presidential_election\" title=\"1996 Taiwanese presidential election\">first direct elections</a> and chooses <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>-hui\" title=\"<PERSON>-h<PERSON>\"><PERSON>-<PERSON></a> as <a href=\"https://wikipedia.org/wiki/President_of_the_Republic_of_China\" title=\"President of the Republic of China\">President</a>.", "links": [{"title": "Taiwan", "link": "https://wikipedia.org/wiki/Taiwan"}, {"title": "1996 Taiwanese presidential election", "link": "https://wikipedia.org/wiki/1996_Taiwanese_presidential_election"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>-hui"}, {"title": "President of the Republic of China", "link": "https://wikipedia.org/wiki/President_of_the_Republic_of_China"}]}, {"year": "1999", "text": "Gunmen assassinate Paraguay's Vice President <PERSON>.", "html": "1999 - Gunmen assassinate <a href=\"https://wikipedia.org/wiki/Paraguay\" title=\"Paraguay\">Paraguay</a>'s <a href=\"https://wikipedia.org/wiki/Vice_President_of_Paraguay\" title=\"Vice President of Paraguay\">Vice President</a> <a href=\"https://wikipedia.org/wiki/Luis_Mar%C3%ADa_Arga%C3%B1a\" title=\"<PERSON>\"><PERSON></a>.", "no_year_html": "Gunmen assassinate <a href=\"https://wikipedia.org/wiki/Paraguay\" title=\"Paraguay\">Paraguay</a>'s <a href=\"https://wikipedia.org/wiki/Vice_President_of_Paraguay\" title=\"Vice President of Paraguay\">Vice President</a> <a href=\"https://wikipedia.org/wiki/Luis_Mar%C3%ADa_Arga%C3%B1a\" title=\"<PERSON>\"><PERSON></a>.", "links": [{"title": "Paraguay", "link": "https://wikipedia.org/wiki/Paraguay"}, {"title": "Vice President of Paraguay", "link": "https://wikipedia.org/wiki/Vice_President_of_Paraguay"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Luis_Mar%C3%ADa_Arga%C3%B1a"}]}, {"year": "2001", "text": "The Russian Mir space station is disposed of, breaking up in the atmosphere before falling into the southern Pacific Ocean near Fiji.", "html": "2001 - The Russian <i><a href=\"https://wikipedia.org/wiki/Mir\" title=\"Mir\">Mir</a></i> space station is <a href=\"https://wikipedia.org/wiki/Deorbit_of_Mir\" title=\"Deorbit of Mir\">disposed of</a>, breaking up in the atmosphere before falling into the southern Pacific Ocean near <a href=\"https://wikipedia.org/wiki/Fiji\" title=\"Fiji\">Fiji</a>.", "no_year_html": "The Russian <i><a href=\"https://wikipedia.org/wiki/Mir\" title=\"Mir\">Mir</a></i> space station is <a href=\"https://wikipedia.org/wiki/Deorbit_of_Mir\" title=\"Deorbit of Mir\">disposed of</a>, breaking up in the atmosphere before falling into the southern Pacific Ocean near <a href=\"https://wikipedia.org/wiki/Fiji\" title=\"Fiji\">Fiji</a>.", "links": [{"title": "Mir", "link": "https://wikipedia.org/wiki/Mir"}, {"title": "Deorbit of Mir", "link": "https://wikipedia.org/wiki/Deorbit_of_Mir"}, {"title": "Fiji", "link": "https://wikipedia.org/wiki/Fiji"}]}, {"year": "2003", "text": "Battle of Nasiriyah, first major conflict during the invasion of Iraq.", "html": "2003 - <a href=\"https://wikipedia.org/wiki/Battle_of_Nasiriyah\" title=\"Battle of Nasiriyah\">Battle of Nasiriyah</a>, first major conflict during the <a href=\"https://wikipedia.org/wiki/2003_invasion_of_Iraq\" title=\"2003 invasion of Iraq\">invasion of Iraq</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Battle_of_Nasiriyah\" title=\"Battle of Nasiriyah\">Battle of Nasiriyah</a>, first major conflict during the <a href=\"https://wikipedia.org/wiki/2003_invasion_of_Iraq\" title=\"2003 invasion of Iraq\">invasion of Iraq</a>.", "links": [{"title": "Battle of Nasiriyah", "link": "https://wikipedia.org/wiki/Battle_of_Nasiriyah"}, {"title": "2003 invasion of Iraq", "link": "https://wikipedia.org/wiki/2003_invasion_of_Iraq"}]}, {"year": "2008", "text": "Official opening of Rajiv Gandhi International Airport in Hyderabad, India", "html": "2008 - Official opening of <a href=\"https://wikipedia.org/wiki/Rajiv_Gandhi_International_Airport\" title=\"Rajiv Gandhi International Airport\">Rajiv Gandhi International Airport</a> in <a href=\"https://wikipedia.org/wiki/Hyderabad\" title=\"Hyderabad\">Hyderabad</a>, India", "no_year_html": "Official opening of <a href=\"https://wikipedia.org/wiki/Rajiv_Gandhi_International_Airport\" title=\"Rajiv Gandhi International Airport\">Rajiv Gandhi International Airport</a> in <a href=\"https://wikipedia.org/wiki/Hyderabad\" title=\"Hyderabad\">Hyderabad</a>, India", "links": [{"title": "<PERSON>iv Gandhi International Airport", "link": "https://wikipedia.org/wiki/<PERSON>iv_Gandhi_International_Airport"}, {"title": "Hyderabad", "link": "https://wikipedia.org/wiki/Hyderabad"}]}, {"year": "2009", "text": "FedEx Express Flight 80: A McDonnell Douglas MD-11 flying from Guangzhou, China crashes at Tokyo's Narita International Airport, killing both the captain and the co-pilot.", "html": "2009 - <a href=\"https://wikipedia.org/wiki/FedEx_Express_Flight_80\" title=\"FedEx Express Flight 80\">FedEx Express Flight 80</a>: A <a href=\"https://wikipedia.org/wiki/McDonnell_Douglas_MD-11\" title=\"McDonnell Douglas MD-11\">McDonnell Douglas MD-11</a> flying from <a href=\"https://wikipedia.org/wiki/Guangzhou\" title=\"Guangzhou\">Guangzhou</a>, China crashes at Tokyo's <a href=\"https://wikipedia.org/wiki/Narita_International_Airport\" title=\"Narita International Airport\">Narita International Airport</a>, killing both the captain and the co-pilot.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/FedEx_Express_Flight_80\" title=\"FedEx Express Flight 80\">FedEx Express Flight 80</a>: A <a href=\"https://wikipedia.org/wiki/McDonnell_Douglas_MD-11\" title=\"McDonnell Douglas MD-11\">McDonnell Douglas MD-11</a> flying from <a href=\"https://wikipedia.org/wiki/Guangzhou\" title=\"Guangzhou\">Guangzhou</a>, China crashes at Tokyo's <a href=\"https://wikipedia.org/wiki/Narita_International_Airport\" title=\"Narita International Airport\">Narita International Airport</a>, killing both the captain and the co-pilot.", "links": [{"title": "FedEx Express Flight 80", "link": "https://wikipedia.org/wiki/FedEx_Express_Flight_80"}, {"title": "McDonnell Douglas MD-11", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>-11"}, {"title": "Guangzhou", "link": "https://wikipedia.org/wiki/Guangzhou"}, {"title": "Narita International Airport", "link": "https://wikipedia.org/wiki/Narita_International_Airport"}]}, {"year": "2010", "text": "The Affordable Care Act becomes law in the United States.", "html": "2010 - The <a href=\"https://wikipedia.org/wiki/Affordable_Care_Act\" title=\"Affordable Care Act\">Affordable Care Act</a> becomes law in the United States.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Affordable_Care_Act\" title=\"Affordable Care Act\">Affordable Care Act</a> becomes law in the United States.", "links": [{"title": "Affordable Care Act", "link": "https://wikipedia.org/wiki/Affordable_Care_Act"}]}, {"year": "2014", "text": "The World Health Organization (WHO) reports cases of Ebola in the forested rural region of southeastern Guinea, marking the beginning of the largest Ebola outbreak in history.", "html": "2014 - The <a href=\"https://wikipedia.org/wiki/World_Health_Organization\" title=\"World Health Organization\">World Health Organization</a> (WHO) reports cases of <a href=\"https://wikipedia.org/wiki/Ebola\" title=\"Ebola\">Ebola</a> in the forested rural region of southeastern Guinea, marking the beginning of the largest Ebola outbreak in history.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/World_Health_Organization\" title=\"World Health Organization\">World Health Organization</a> (WHO) reports cases of <a href=\"https://wikipedia.org/wiki/Ebola\" title=\"Ebola\">Ebola</a> in the forested rural region of southeastern Guinea, marking the beginning of the largest Ebola outbreak in history.", "links": [{"title": "World Health Organization", "link": "https://wikipedia.org/wiki/World_Health_Organization"}, {"title": "Ebola", "link": "https://wikipedia.org/wiki/Ebola"}]}, {"year": "2018", "text": "President of Peru <PERSON> resigns from the presidency amid a mass corruption scandal before certain impeachment by the opposition-majority Congress of Peru.", "html": "2018 - <a href=\"https://wikipedia.org/wiki/President_of_Peru\" title=\"President of Peru\">President of Peru</a> <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> resigns from the presidency amid a <a href=\"https://wikipedia.org/wiki/Operation_Car_Wash\" title=\"Operation Car Wash\">mass corruption scandal</a> before certain <a href=\"https://wikipedia.org/wiki/Impeachment\" title=\"Impeachment\">impeachment</a> by the <a href=\"https://wikipedia.org/wiki/Parliamentary_opposition\" title=\"Parliamentary opposition\">opposition</a>-majority <a href=\"https://wikipedia.org/wiki/Congress_of_the_Republic_of_Peru\" title=\"Congress of the Republic of Peru\">Congress of Peru</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/President_of_Peru\" title=\"President of Peru\">President of Peru</a> <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> resigns from the presidency amid a <a href=\"https://wikipedia.org/wiki/Operation_Car_Wash\" title=\"Operation Car Wash\">mass corruption scandal</a> before certain <a href=\"https://wikipedia.org/wiki/Impeachment\" title=\"Impeachment\">impeachment</a> by the <a href=\"https://wikipedia.org/wiki/Parliamentary_opposition\" title=\"Parliamentary opposition\">opposition</a>-majority <a href=\"https://wikipedia.org/wiki/Congress_of_the_Republic_of_Peru\" title=\"Congress of the Republic of Peru\">Congress of Peru</a>.", "links": [{"title": "President of Peru", "link": "https://wikipedia.org/wiki/President_of_Peru"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "Operation Car Wash", "link": "https://wikipedia.org/wiki/Operation_Car_Wash"}, {"title": "Impeachment", "link": "https://wikipedia.org/wiki/Impeachment"}, {"title": "Parliamentary opposition", "link": "https://wikipedia.org/wiki/Parliamentary_opposition"}, {"title": "Congress of the Republic of Peru", "link": "https://wikipedia.org/wiki/Congress_of_the_Republic_of_Peru"}]}, {"year": "2019", "text": "The Kazakh capital of Astana was renamed to <PERSON><PERSON><PERSON><PERSON>.", "html": "2019 - The <a href=\"https://wikipedia.org/wiki/Kazakhstan\" title=\"Kazakhstan\">Kazakh</a> capital of Astana was renamed to <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>-<PERSON>\" class=\"mw-redirect\" title=\"<PERSON><PERSON>-Sultan\"><PERSON><PERSON>-<PERSON></a>.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Kazakhstan\" title=\"Kazakhstan\">Kazakh</a> capital of Astana was renamed to <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>-Sultan\" class=\"mw-redirect\" title=\"<PERSON><PERSON>-Sultan\"><PERSON><PERSON>-<PERSON></a>.", "links": [{"title": "Kazakhstan", "link": "https://wikipedia.org/wiki/Kazakhstan"}, {"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>-<PERSON>"}]}, {"year": "2019", "text": "The US-backed Syrian Democratic Forces capture the town of Baghuz in Eastern Syria, declaring military victory over the Islamic State of Iraq and the Levant after four years of fighting, although the group maintains a scattered presence and sleeper cells across Syria and Iraq.", "html": "2019 - The US-backed <i>Syrian Democratic Forces</i> <a href=\"https://wikipedia.org/wiki/Battle_of_Baghuz_Fawqani\" title=\"Battle of Baghuz Fawqani\">capture</a> the town of <a href=\"https://wikipedia.org/wiki/Baghuz\" class=\"mw-redirect\" title=\"Baghuz\">Baghuz</a> in Eastern Syria, declaring military victory over the <a href=\"https://wikipedia.org/wiki/Islamic_State_of_Iraq_and_the_Levant\" class=\"mw-redirect\" title=\"Islamic State of Iraq and the Levant\">Islamic State of Iraq and the Levant</a> after four years of fighting, although the group maintains a scattered presence and sleeper cells across Syria and Iraq.", "no_year_html": "The US-backed <i>Syrian Democratic Forces</i> <a href=\"https://wikipedia.org/wiki/Battle_of_Baghuz_Fawqani\" title=\"Battle of Baghuz Fawqani\">capture</a> the town of <a href=\"https://wikipedia.org/wiki/Baghuz\" class=\"mw-redirect\" title=\"Baghuz\">Baghuz</a> in Eastern Syria, declaring military victory over the <a href=\"https://wikipedia.org/wiki/Islamic_State_of_Iraq_and_the_Levant\" class=\"mw-redirect\" title=\"Islamic State of Iraq and the Levant\">Islamic State of Iraq and the Levant</a> after four years of fighting, although the group maintains a scattered presence and sleeper cells across Syria and Iraq.", "links": [{"title": "Battle of Baghuz Fawqani", "link": "https://wikipedia.org/wiki/Battle_of_Baghuz_Fawqani"}, {"title": "Baghuz", "link": "https://wikipedia.org/wiki/Baghuz"}, {"title": "Islamic State of Iraq and the Levant", "link": "https://wikipedia.org/wiki/Islamic_State_of_Iraq_and_the_Levant"}]}, {"year": "2020", "text": "Prime Minister <PERSON> put the United Kingdom into its first national lockdown in response to COVID-19.", "html": "2020 - <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_the_United_Kingdom\" title=\"Prime Minister of the United Kingdom\">Prime Minister</a> <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> put the <a href=\"https://wikipedia.org/wiki/United_Kingdom\" title=\"United Kingdom\">United Kingdom</a> into its first national lockdown in response to <a href=\"https://wikipedia.org/wiki/COVID-19\" title=\"COVID-19\">COVID-19</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Prime_Minister_of_the_United_Kingdom\" title=\"Prime Minister of the United Kingdom\">Prime Minister</a> <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> put the <a href=\"https://wikipedia.org/wiki/United_Kingdom\" title=\"United Kingdom\">United Kingdom</a> into its first national lockdown in response to <a href=\"https://wikipedia.org/wiki/COVID-19\" title=\"COVID-19\">COVID-19</a>.", "links": [{"title": "Prime Minister of the United Kingdom", "link": "https://wikipedia.org/wiki/Prime_Minister_of_the_United_Kingdom"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "United Kingdom", "link": "https://wikipedia.org/wiki/United_Kingdom"}, {"title": "COVID-19", "link": "https://wikipedia.org/wiki/COVID-19"}]}, {"year": "2021", "text": "A container ship runs aground and obstructs the Suez Canal for six days.", "html": "2021 - A container ship <a href=\"https://wikipedia.org/wiki/2021_Suez_Canal_obstruction\" title=\"2021 Suez Canal obstruction\">runs aground and obstructs</a> the <a href=\"https://wikipedia.org/wiki/Suez_Canal\" title=\"Suez Canal\">Suez Canal</a> for six days.", "no_year_html": "A container ship <a href=\"https://wikipedia.org/wiki/2021_Suez_Canal_obstruction\" title=\"2021 Suez Canal obstruction\">runs aground and obstructs</a> the <a href=\"https://wikipedia.org/wiki/Suez_Canal\" title=\"Suez Canal\">Suez Canal</a> for six days.", "links": [{"title": "2021 Suez Canal obstruction", "link": "https://wikipedia.org/wiki/2021_Suez_Canal_obstruction"}, {"title": "Suez Canal", "link": "https://wikipedia.org/wiki/Suez_Canal"}]}], "Births": [{"year": "1338", "text": "Emperor <PERSON><PERSON><PERSON><PERSON><PERSON> of Japan (d. 1374)", "html": "1338 - <a href=\"https://wikipedia.org/wiki/Emperor_<PERSON>-K%C5%8Dgon\" title=\"Emperor Go-Kōgon\">Emperor Go-Kōgon</a> of Japan (d. 1374)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Emperor_<PERSON>-K%C5%8Dgon\" title=\"Emperor Go-Kōgon\">Emperor Go-Kōgon</a> of Japan (d. 1374)", "links": [{"title": "Emperor <PERSON><PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Emperor_<PERSON>-K%C5%8Dgon"}]}, {"year": "1430", "text": "<PERSON> Anjou (d. 1482)", "html": "1430 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_Anjou\" title=\"<PERSON> of Anjou\"><PERSON> of Anjou</a> (d. 1482)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON> of Anjou\"><PERSON> of Anjou</a> (d. 1482)", "links": [{"title": "<PERSON> of Anjou", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1514", "text": "<PERSON><PERSON><PERSON><PERSON>, Italian writer and assassin (d. 1548)", "html": "1514 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_de%27_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Italian writer and assassin (d. 1548)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>%27_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON> <PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Italian writer and assassin (d. 1548)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>renzino_de%27_Medici"}]}, {"year": "1599", "text": "<PERSON>, German composer (d. 1663)", "html": "1599 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German composer (d. 1663)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German composer (d. 1663)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1614", "text": "<PERSON><PERSON><PERSON>, Mughal princess (d. 1681)", "html": "1614 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Mughal princess (d. 1681)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Mughal princess (d. 1681)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/J<PERSON>ara_Begum"}]}, {"year": "1643", "text": "<PERSON> of <PERSON>, Spanish Dominican lay sister and mystic (d. 1731)", "html": "1643 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>_<PERSON>%C3%B3n_<PERSON>_<PERSON>\" title=\"<PERSON> of <PERSON> y <PERSON>\"><PERSON> of <PERSON> y <PERSON></a>, Spanish Dominican lay sister and mystic (d. 1731)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Mary_<PERSON>_<PERSON>_<PERSON>_<PERSON>%C3%B3n_y_<PERSON><PERSON>\" title=\"<PERSON> of <PERSON> y <PERSON>\"><PERSON> of <PERSON></a>, Spanish Dominican lay sister and mystic (d. 1731)", "links": [{"title": "<PERSON> of <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_de_<PERSON>%C3%B3n_y_<PERSON>"}]}, {"year": "1732", "text": "Princess <PERSON> of France (d. 1800)", "html": "1732 - <a href=\"https://wikipedia.org/wiki/Princess_<PERSON>_<PERSON>%C3%A9la%C3%AFde_of_France\" class=\"mw-redirect\" title=\"Princess <PERSON> of France\">Princess <PERSON> of France</a> (d. 1800)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Princess_<PERSON>_<PERSON>%C3%A9la%C3%AFde_of_France\" class=\"mw-redirect\" title=\"Princess <PERSON> of France\">Princess <PERSON> of France</a> (d. 1800)", "links": [{"title": "Princess <PERSON> of France", "link": "https://wikipedia.org/wiki/Princess_<PERSON>_Ad%C3%A9la%C3%AFde_of_France"}]}, {"year": "1749", "text": "<PERSON><PERSON><PERSON>, French mathematician and astronomer (d. 1827)", "html": "1749 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, French mathematician and astronomer (d. 1827)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, French mathematician and astronomer (d. 1827)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>"}]}, {"year": "1750", "text": "<PERSON>, Austrian bassist and composer (d. 1812)", "html": "1750 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Austrian bassist and composer (d. 1812)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Austrian bassist and composer (d. 1812)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1754", "text": "<PERSON><PERSON><PERSON>, Slovene mathematician, physicist and artillery officer (d. 1802)", "html": "1754 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Slovene mathematician, physicist and artillery officer (d. 1802)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Slovene mathematician, physicist and artillery officer (d. 1802)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1769", "text": "<PERSON><PERSON> <PERSON>, French general and diplomat (d. 1832)", "html": "1769 - <a href=\"https://wikipedia.org/wiki/Augustin_<PERSON>_<PERSON>\" title=\"Augustin <PERSON>\">Augustin <PERSON></a>, French general and diplomat (d. 1832)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Augustin_<PERSON>_<PERSON>\" title=\"Augustin <PERSON>\">Augustin <PERSON></a>, French general and diplomat (d. 1832)", "links": [{"title": "Augustin <PERSON>", "link": "https://wikipedia.org/wiki/August<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1769", "text": "<PERSON>, English geologist and cartographer (d. 1839)", "html": "1769 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(geologist)\" title=\"<PERSON> (geologist)\"><PERSON></a>, English geologist and cartographer (d. 1839)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>(geologist)\" title=\"<PERSON> (geologist)\"><PERSON></a>, English geologist and cartographer (d. 1839)", "links": [{"title": "<PERSON> (geologist)", "link": "https://wikipedia.org/wiki/<PERSON>_(geologist)"}]}, {"year": "1823", "text": "<PERSON><PERSON><PERSON>, American journalist and politician, 17th Vice President of the United States (d. 1885)", "html": "1823 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON> Colfax\"><PERSON><PERSON><PERSON></a>, American journalist and politician, 17th <a href=\"https://wikipedia.org/wiki/Vice_President_of_the_United_States\" title=\"Vice President of the United States\">Vice President of the United States</a> (d. 1885)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American journalist and politician, 17th <a href=\"https://wikipedia.org/wiki/Vice_President_of_the_United_States\" title=\"Vice President of the United States\">Vice President of the United States</a> (d. 1885)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}, {"title": "Vice President of the United States", "link": "https://wikipedia.org/wiki/Vice_President_of_the_United_States"}]}, {"year": "1826", "text": "<PERSON>, Austrian violinist and composer (d. 1917)", "html": "1826 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Austrian violinist and composer (d. 1917)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Austrian violinist and composer (d. 1917)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1834", "text": "<PERSON>, German pianist and composer (d. 1858)", "html": "1834 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German pianist and composer (d. 1858)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German pianist and composer (d. 1858)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1838", "text": "<PERSON>, Swiss women's rights activist and unionist (d. 1908)", "html": "1838 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swiss women's rights activist and unionist (d. 1908)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swiss women's rights activist and unionist (d. 1908)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1842", "text": "<PERSON>, Estonian-German historian, businessman and composer (d. 1909)", "html": "1842 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Estonian-German historian, businessman and composer (d. 1909)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Estonian-German historian, businessman and composer (d. 1909)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1842", "text": "<PERSON>, American mathematician (d. 1921)", "html": "1842 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American mathematician (d. 1921)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American mathematician (d. 1921)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1858", "text": "<PERSON>, German activist and politician, Nobel Prize laureate (d. 1941)", "html": "1858 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German activist and politician, <a href=\"https://wikipedia.org/wiki/Nobel_Peace_Prize\" title=\"Nobel Peace Prize\">Nobel Prize</a> laureate (d. 1941)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German activist and politician, <a href=\"https://wikipedia.org/wiki/Nobel_Peace_Prize\" title=\"Nobel Peace Prize\">Nobel Prize</a> laureate (d. 1941)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Ludwig_<PERSON>de"}, {"title": "Nobel Peace Prize", "link": "https://wikipedia.org/wiki/Nobel_Peace_Prize"}]}, {"year": "1860", "text": "<PERSON><PERSON><PERSON>, British politician and businessman (d. 1933)", "html": "1860 - <a href=\"https://wikipedia.org/wiki/<PERSON>rat<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, British politician and businessman (d. 1933)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>rat<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, British politician and businessman (d. 1933)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Horatio_<PERSON>"}]}, {"year": "1862", "text": "<PERSON>, American criminal (d. 1950)", "html": "1862 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(outlaw)\" title=\"<PERSON> (outlaw)\"><PERSON></a>, American criminal (d. 1950)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(outlaw)\" title=\"<PERSON> (outlaw)\"><PERSON></a>, American criminal (d. 1950)", "links": [{"title": "<PERSON> (outlaw)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(outlaw)"}]}, {"year": "1868", "text": "<PERSON>, German journalist and politician (d. 1923)", "html": "1868 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German journalist and politician (d. 1923)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German journalist and politician (d. 1923)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1869", "text": "<PERSON><PERSON><PERSON>, Turkish-Armenian businessman and philanthropist (d. 1955)", "html": "1869 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Turkish-Armenian businessman and philanthropist (d. 1955)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Turkish-Armenian businessman and philanthropist (d. 1955)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1872", "text": "<PERSON>, Australian-New Zealand union leader and politician, 23rd Prime Minister of New Zealand (d. 1940)", "html": "1872 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian-New Zealand union leader and politician, 23rd <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_New_Zealand\" title=\"Prime Minister of New Zealand\">Prime Minister of New Zealand</a> (d. 1940)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian-New Zealand union leader and politician, 23rd <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_New_Zealand\" title=\"Prime Minister of New Zealand\">Prime Minister of New Zealand</a> (d. 1940)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "Prime Minister of New Zealand", "link": "https://wikipedia.org/wiki/Prime_Minister_of_New_Zealand"}]}, {"year": "1874", "text": "<PERSON><PERSON>, English hurdler (d. 1947)", "html": "1874 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, English hurdler (d. 1947)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, English hurdler (d. 1947)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1874", "text": "<PERSON><PERSON> <PERSON><PERSON>, German-American painter and illustrator (d. 1951)", "html": "1874 - <a href=\"https://wikipedia.org/wiki/J._<PERSON><PERSON>_Leyendecker\" title=\"J. <PERSON><PERSON> Leyendecker\"><PERSON><PERSON> <PERSON><PERSON></a>, German-American painter and illustrator (d. 1951)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/J._<PERSON><PERSON>_Leyendecker\" title=\"J. C. Leyendecker\"><PERSON><PERSON> <PERSON><PERSON></a>, German-American painter and illustrator (d. 1951)", "links": [{"title": "<PERSON><PERSON> <PERSON><PERSON>", "link": "https://wikipedia.org/wiki/J._<PERSON>._Leyendecker"}]}, {"year": "1876", "text": "<PERSON><PERSON>, Turkish sociologist, poet and activist (d. 1924)", "html": "1876 - <a href=\"https://wikipedia.org/wiki/Ziya_G%C3%B6kalp\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Turkish sociologist, poet and activist (d. 1924)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Ziya_G%C3%B6kalp\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Turkish sociologist, poet and activist (d. 1924)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Ziya_G%C3%B6kalp"}]}, {"year": "1876", "text": "<PERSON><PERSON><PERSON>, Burmese poet, writer and political leader (d. 1964)", "html": "1876 - <a href=\"https://wikipedia.org/wiki/Thakin_<PERSON>daw_<PERSON>\" title=\"Thakin <PERSON>daw <PERSON>\">T<PERSON><PERSON></a>, Burmese poet, writer and political leader (d. 1964)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Thakin_<PERSON>daw_<PERSON>\" title=\"Thakin Kodaw <PERSON>\">Thak<PERSON></a>, Burmese poet, writer and political leader (d. 1964)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Thakin_<PERSON>daw_<PERSON>ing"}]}, {"year": "1878", "text": "<PERSON>, Austrian composer and conductor (d. 1934)", "html": "1878 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Austrian composer and conductor (d. 1934)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Austrian composer and conductor (d. 1934)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1880", "text": "<PERSON><PERSON><PERSON>, Finnish lawyer and politician, Finnish Minister of the Interior (d. 1922)", "html": "1880 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Finnish lawyer and politician, <a href=\"https://wikipedia.org/wiki/Minister_of_the_Interior_(Finland)\" title=\"Minister of the Interior (Finland)\">Finnish Minister of the Interior</a> (d. 1922)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Finnish lawyer and politician, <a href=\"https://wikipedia.org/wiki/Minister_of_the_Interior_(Finland)\" title=\"Minister of the Interior (Finland)\">Finnish Minister of the Interior</a> (d. 1922)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}, {"title": "Minister of the Interior (Finland)", "link": "https://wikipedia.org/wiki/Minister_of_the_Interior_(Finland)"}]}, {"year": "1881", "text": "<PERSON>, American sprinter (d. 1969)", "html": "1881 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American sprinter (d. 1969)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Hearn\"><PERSON></a>, American sprinter (d. 1969)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>n"}]}, {"year": "1881", "text": "<PERSON>, French novelist and paleographer, Nobel Prize laureate (d. 1958)", "html": "1881 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French novelist and paleographer, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Literature\" title=\"Nobel Prize in Literature\">Nobel Prize</a> laureate (d. 1958)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French novelist and paleographer, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Literature\" title=\"Nobel Prize in Literature\">Nobel Prize</a> laureate (d. 1958)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>"}, {"title": "Nobel Prize in Literature", "link": "https://wikipedia.org/wiki/Nobel_Prize_in_Literature"}]}, {"year": "1881", "text": "<PERSON>, German chemist and academic, Nobel Prize laureate (d. 1965)", "html": "1881 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German chemist and academic, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Chemistry\" title=\"Nobel Prize in Chemistry\">Nobel Prize</a> laureate (d. 1965)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German chemist and academic, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Chemistry\" title=\"Nobel Prize in Chemistry\">Nobel Prize</a> laureate (d. 1965)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Nobel Prize in Chemistry", "link": "https://wikipedia.org/wiki/Nobel_Prize_in_Chemistry"}]}, {"year": "1882", "text": "<PERSON>, Jewish German-American mathematician, physicist and academic (d. 1935)", "html": "1882 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Jewish German-American mathematician, physicist and academic (d. 1935)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Jewish German-American mathematician, physicist and academic (d. 1935)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>er"}]}, {"year": "1884", "text": "<PERSON>, English sailor (d. 1967)", "html": "1884 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English sailor (d. 1967)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English sailor (d. 1967)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1885", "text": "<PERSON><PERSON>, American jumper and politician (d. 1961)", "html": "1885 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American jumper and politician (d. 1961)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American jumper and politician (d. 1961)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1885", "text": "<PERSON><PERSON><PERSON>, Mexican general and acting president (1915) (d. 1962)", "html": "1885 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>%C3%A1lez_Garza\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Mexican general and acting president (1915) (d. 1962)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>%C3%A1lez_Garza\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Mexican general and acting president (1915) (d. 1962)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Roque_Gonz%C3%A1lez_Garza"}]}, {"year": "1886", "text": "<PERSON>, American long jumper (d. 1942)", "html": "1886 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Irons\"><PERSON></a>, American long jumper (d. 1942)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Frank Irons\"><PERSON></a>, American long jumper (d. 1942)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1887", "text": "<PERSON>, Czech painter and poet (d. 1945)", "html": "1887 - <a href=\"https://wikipedia.org/wiki/<PERSON>_%C4%8Capek\" title=\"<PERSON>\"><PERSON></a>, Czech painter and poet (d. 1945)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_%C4%8Capek\" title=\"<PERSON>\"><PERSON></a>, Czech painter and poet (d. 1945)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_%C4%8Capek"}]}, {"year": "1887", "text": "<PERSON>, German author (d. 1975)", "html": "1887 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German author (d. 1975)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German author (d. 1975)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1887", "text": "<PERSON>, Spanish painter and sculptor (d. 1927)", "html": "1887 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Spanish painter and sculptor (d. 1927)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Spanish painter and sculptor (d. 1927)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1887", "text": "<PERSON>, Lithuanian-born American labor leader (d. 1946)", "html": "1887 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Lithuanian-born American labor leader (d. 1946)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Lithuanian-born American labor leader (d. 1946)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1891", "text": "<PERSON>, Burmese author and educationist (d. 1942)", "html": "1891 - <a href=\"https://wikipedia.org/wiki/Po_Kya\" title=\"Po Kya\"><PERSON></a>, Burmese author and educationist (d. 1942)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Po_Kya\" title=\"Po Kya\"><PERSON></a>, Burmese author and educationist (d. 1942)", "links": [{"title": "Po Kya", "link": "https://wikipedia.org/wiki/Po_Kya"}]}, {"year": "1893", "text": "<PERSON><PERSON>, Irish-American art director and production designer (d. 1960)", "html": "1893 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Irish-American art director and production designer (d. 1960)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Irish-American art director and production designer (d. 1960)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1893", "text": "<PERSON><PERSON><PERSON><PERSON>, Indian engineer and businessman (d. 1974)", "html": "1893 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>_<PERSON>id<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Indian engineer and businessman (d. 1974)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>_<PERSON>id<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Indian engineer and businessman (d. 1974)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1894", "text": "<PERSON>, English international footballer and cricketer (d. 1963)", "html": "1894 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English international footballer and cricketer (d. 1963)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English international footballer and cricketer (d. 1963)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1895", "text": "<PERSON><PERSON><PERSON><PERSON>, Filipino historian and educator (d. 2001)", "html": "1895 - <a href=\"https://wikipedia.org/wiki/Encarnacion_Alzona\" class=\"mw-redirect\" title=\"Encarnacion Alzona\">Encarnacion Alzona</a>, Filipino historian and educator (d. 2001)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Encarnacion_Alzona\" class=\"mw-redirect\" title=\"Encarnacion Alzona\">Encarnacion Alzona</a>, Filipino historian and educator (d. 2001)", "links": [{"title": "Encarnacion Alzona", "link": "https://wikipedia.org/wiki/Encarnacion_Alzona"}]}, {"year": "1895", "text": "<PERSON>, French-American astrologer, author and composer (d. 1985)", "html": "1895 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French-American astrologer, author and composer (d. 1985)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French-American astrologer, author and composer (d. 1985)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1898", "text": "<PERSON>, Slovenian-American author, translator and politician (d. 1951)", "html": "1898 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Slovenian-American author, translator and politician (d. 1951)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Slovenian-American author, translator and politician (d. 1951)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1898", "text": "<PERSON>, Duchess of Parma (d. 1984)", "html": "1898 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>-<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Duchess of Parma (d. 1984)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>-<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Duchess of Parma (d. 1984)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>-Bus<PERSON>"}]}, {"year": "1899", "text": "<PERSON>, German actress and singer (d. 1943)", "html": "1899 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German actress and singer (d. 1943)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German actress and singer (d. 1943)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1900", "text": "<PERSON>, German psychologist and sociologist (d. 1980)", "html": "1900 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German psychologist and sociologist (d. 1980)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German psychologist and sociologist (d. 1980)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1901", "text": "<PERSON><PERSON>, Indian guru and religious writer (d. 1982)", "html": "1901 - <a href=\"https://wikipedia.org/wiki/B<PERSON>_Hridaya_Bon\" title=\"Bhakti Hridaya Bon\"><PERSON><PERSON> Hridaya Bon</a>, Indian guru and religious writer (d. 1982)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON><PERSON>_Bon\" title=\"Bhakti Hridaya Bon\"><PERSON><PERSON> Hrida<PERSON></a>, Indian guru and religious writer (d. 1982)", "links": [{"title": "<PERSON>hakti Hridaya Bon", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON><PERSON>_Bon"}]}, {"year": "1903", "text": "<PERSON>, New Zealand writer (d. 1982)", "html": "1903 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, New Zealand writer (d. 1982)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, New Zealand writer (d. 1982)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1905", "text": "<PERSON><PERSON>, German chanson singer-songwriter (d. 1972)", "html": "1905 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, German chanson singer-songwriter (d. 1972)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, German chanson singer-songwriter (d. 1972)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1907", "text": "<PERSON>, Swiss-Italian pharmacologist and academic, Nobel Prize laureate (d. 1992)", "html": "1907 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swiss-Italian pharmacologist and academic, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Physiology_or_Medicine\" title=\"Nobel Prize in Physiology or Medicine\">Nobel Prize</a> laureate (d. 1992)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swiss-Italian pharmacologist and academic, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Physiology_or_Medicine\" title=\"Nobel Prize in Physiology or Medicine\">Nobel Prize</a> laureate (d. 1992)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Nobel Prize in Physiology or Medicine", "link": "https://wikipedia.org/wiki/Nobel_Prize_in_Physiology_or_Medicine"}]}, {"year": "1909", "text": "<PERSON>, American cartoonist (d. 1997)", "html": "1909 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American cartoonist (d. 1997)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American cartoonist (d. 1997)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1910", "text": "<PERSON>, English runner, colonial officer and educator (d. 2001)", "html": "1910 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English runner, colonial officer and educator (d. 2001)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English runner, colonial officer and educator (d. 2001)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1910", "text": "<PERSON>, Japanese director, producer and screenwriter (d. 1998) ", "html": "1910 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Japanese director, producer and screenwriter (d. 1998) ", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Japanese director, producer and screenwriter (d. 1998) ", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1912", "text": "<PERSON>, Canadian-American children's author and critic (d. 1996)", "html": "1912 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian-American children's author and critic (d. 1996)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian-American children's author and critic (d. 1996)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1912", "text": "<PERSON>, English-South African cricketer and coach (d. 2013)", "html": "1912 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English-South African cricketer and coach (d. 2013)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English-South African cricketer and coach (d. 2013)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1912", "text": "<PERSON><PERSON><PERSON> <PERSON>, German-American physicist and engineer (d. 1977)", "html": "1912 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON> <PERSON>\"><PERSON><PERSON><PERSON> <PERSON></a>, German-American physicist and engineer (d. 1977)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON> <PERSON>\"><PERSON><PERSON><PERSON> <PERSON></a>, German-American physicist and engineer (d. 1977)", "links": [{"title": "<PERSON><PERSON><PERSON> <PERSON>", "link": "https://wikipedia.org/wiki/We<PERSON><PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1913", "text": "<PERSON><PERSON><PERSON>, Turko-French painter and illustrator (d. 1993)", "html": "1913 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON>bid<PERSON> <PERSON>\"><PERSON><PERSON><PERSON></a>, Turko-French painter and illustrator (d. 1993)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON>bid<PERSON> <PERSON>\"><PERSON><PERSON><PERSON></a>, Turko-French painter and illustrator (d. 1993)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Abid<PERSON>_<PERSON>"}]}, {"year": "1914", "text": "<PERSON><PERSON><PERSON>, American magician and author (d. 1984)", "html": "1914 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON> Christopher\"><PERSON><PERSON><PERSON></a>, American magician and author (d. 1984)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON> Christopher\"><PERSON><PERSON><PERSON></a>, American magician and author (d. 1984)", "links": [{"title": "<PERSON><PERSON><PERSON> Christopher", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_Christopher"}]}, {"year": "1915", "text": "<PERSON><PERSON><PERSON>, Duchess of Roxburghe (d. 2014)", "html": "1915 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>-<PERSON>,_Duchess_of_Roxburghe\" title=\"<PERSON>, Duchess of Roxburghe\"><PERSON>, Duchess of Roxburghe</a> (d. 2014)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>-<PERSON>,_Duchess_of_Roxburghe\" title=\"<PERSON>, Duchess of Roxburghe\"><PERSON>, Duchess of Roxburghe</a> (d. 2014)", "links": [{"title": "<PERSON><PERSON><PERSON>, Duchess of Roxburghe", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>-<PERSON>,_Duchess_of_Roxburghe"}]}, {"year": "1915", "text": "<PERSON><PERSON>, Russian captain (d. 1991)", "html": "1915 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_(sniper)\" title=\"<PERSON><PERSON> (sniper)\"><PERSON><PERSON></a>, Russian captain (d. 1991)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_(sniper)\" title=\"<PERSON><PERSON> (sniper)\"><PERSON><PERSON></a>, Russian captain (d. 1991)", "links": [{"title": "<PERSON><PERSON> (sniper)", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_(sniper)"}]}, {"year": "1917", "text": "<PERSON>, English historian (d. 1998)", "html": "1917 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English historian (d. 1998)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English historian (d. 1998)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1918", "text": "<PERSON>, American sergeant (d. 1992)", "html": "1918 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>nham\"><PERSON></a>, American sergeant (d. 1992)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American sergeant (d. 1992)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1918", "text": "<PERSON><PERSON>, American politician (d. 2013)", "html": "1918 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American politician (d. 2013)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American politician (d. 2013)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1918", "text": "<PERSON><PERSON>, Japanese football player (d. 1940s)", "html": "1918 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Japanese football player (d. 1940s)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Japanese football player (d. 1940s)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>zu"}]}, {"year": "1919", "text": "<PERSON>, American architect and educator (d. 2013)", "html": "1919 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American architect and educator (d. 2013)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American architect and educator (d. 2013)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1919", "text": "<PERSON><PERSON><PERSON>, Indian freedom activist and politician (d. 2003)", "html": "1919 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Indian freedom activist and politician (d. 2003)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Indian freedom activist and politician (d. 2003)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>i"}]}, {"year": "1920", "text": "<PERSON><PERSON><PERSON><PERSON>, Japanese baseball player and manager (d. 2013)", "html": "1920 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Japanese baseball player and manager (d. 2013)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Japanese baseball player and manager (d. 2013)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1920", "text": "<PERSON>, American pilot, lawyer and politician (d. 2021)", "html": "1920 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, American pilot, lawyer and politician (d. 2021)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, American pilot, lawyer and politician (d. 2021)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1921", "text": "<PERSON>, English race car driver (d. 1967)", "html": "1921 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English race car driver (d. 1967)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English race car driver (d. 1967)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1921", "text": "<PERSON>, Australian public servant (d. 2017)", "html": "1921 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(public_servant)\" title=\"<PERSON> (public servant)\"><PERSON></a>, Australian public servant (d. 2017)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(public_servant)\" title=\"<PERSON> (public servant)\"><PERSON></a>, Australian public servant (d. 2017)", "links": [{"title": "<PERSON> (public servant)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(public_servant)"}]}, {"year": "1922", "text": "<PERSON>, American comedian and actor (d. 2018)", "html": "1922 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American comedian and actor (d. 2018)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American comedian and actor (d. 2018)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1922", "text": "<PERSON><PERSON>, Italian actor (d. 1990)", "html": "1922 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Italian actor (d. 1990)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Italian actor (d. 1990)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1923", "text": "<PERSON>, American soldier and judge (d. 2013)", "html": "1923 - <a href=\"https://wikipedia.org/wiki/Angelo_In<PERSON>\" title=\"Angelo In<PERSON>\"><PERSON></a>, American soldier and judge (d. 2013)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Angelo Ingrassia\"><PERSON></a>, American soldier and judge (d. 2013)", "links": [{"title": "Angelo <PERSON>", "link": "https://wikipedia.org/wiki/Angelo_Ingrassia"}]}, {"year": "1924", "text": "<PERSON>, Sr., American lieutenant and politician (d. 2013)", "html": "1924 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>,_Sr.\" class=\"mw-redirect\" title=\"<PERSON>, Sr.\"><PERSON>, Sr.</a>, American lieutenant and politician (d. 2013)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>,_Sr.\" class=\"mw-redirect\" title=\"<PERSON>, Sr.\"><PERSON>, Sr.</a>, American lieutenant and politician (d. 2013)", "links": [{"title": "<PERSON>, Sr.", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>,_Sr."}]}, {"year": "1924", "text": "<PERSON>, English crystallographer and academic (d. 2023)", "html": "1924 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English crystallographer and academic (d. 2023)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English crystallographer and academic (d. 2023)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1924", "text": "<PERSON>, English architect (d. 2012)", "html": "1924 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English architect (d. 2012)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English architect (d. 2012)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1924", "text": "<PERSON><PERSON>, American inventor of Liquid Paper (d. 1980)", "html": "1924 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_N<PERSON>_<PERSON>\" title=\"<PERSON><PERSON> Nesmith Graham\"><PERSON><PERSON> N<PERSON><PERSON></a>, American inventor of <a href=\"https://wikipedia.org/wiki/Liquid_Paper\" title=\"Liquid Paper\">Liquid Paper</a> (d. 1980)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON> Nesmith Graham\"><PERSON><PERSON><PERSON></a>, American inventor of <a href=\"https://wikipedia.org/wiki/Liquid_Paper\" title=\"Liquid Paper\">Liquid Paper</a> (d. 1980)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON><PERSON>_<PERSON>"}, {"title": "Liquid Paper", "link": "https://wikipedia.org/wiki/Liquid_Paper"}]}, {"year": "1925", "text": "<PERSON>, English cinematographer (d. 2008)", "html": "1925 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(cinematographer)\" title=\"<PERSON> (cinematographer)\"><PERSON></a>, English cinematographer (d. 2008)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(cinematographer)\" title=\"<PERSON> (cinematographer)\"><PERSON></a>, English cinematographer (d. 2008)", "links": [{"title": "<PERSON> (cinematographer)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(cinematographer)"}]}, {"year": "1928", "text": "<PERSON>, American banjo player (d. 2021)", "html": "1928 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American banjo player (d. 2021)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American banjo player (d. 2021)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1929", "text": "<PERSON>, English middle-distance runner, neurologist and academic (d. 2018)", "html": "1929 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English middle-distance runner, neurologist and academic (d. 2018)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English middle-distance runner, neurologist and academic (d. 2018)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1929", "text": "<PERSON>, English architect and engineer (d. 2016)", "html": "1929 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English architect and engineer (d. 2016)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English architect and engineer (d. 2016)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1929", "text": "<PERSON>, American actor, director and producer", "html": "1929 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor, director and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor, director and producer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1931", "text": "<PERSON><PERSON><PERSON>, Russian speed skater (d. 2005)", "html": "1931 - <a href=\"https://wikipedia.org/wiki/Ye<PERSON><PERSON>_<PERSON>_(speed_skater)\" title=\"<PERSON><PERSON><PERSON> (speed skater)\"><PERSON><PERSON><PERSON></a>, Russian speed skater (d. 2005)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Yev<PERSON>_<PERSON>_(speed_skater)\" title=\"<PERSON><PERSON><PERSON> (speed skater)\"><PERSON><PERSON><PERSON></a>, Russian speed skater (d. 2005)", "links": [{"title": "<PERSON><PERSON><PERSON> (speed skater)", "link": "https://wikipedia.org/wiki/Ye<PERSON><PERSON>_<PERSON>_(speed_skater)"}]}, {"year": "1931", "text": "<PERSON>, Russian chess player and author (d. 2016)", "html": "1931 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian chess player and author (d. 2016)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian chess player and author (d. 2016)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1931", "text": "<PERSON><PERSON><PERSON><PERSON><PERSON>, Russian skier (d. 2013)", "html": "1931 - <a href=\"https://wikipedia.org/wiki/Yev<PERSON><PERSON>ya_Me<PERSON>hilo\" title=\"Yevdokiya Mekshilo\"><PERSON><PERSON><PERSON><PERSON><PERSON></a>, Russian skier (d. 2013)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Yev<PERSON><PERSON>ya_<PERSON>hilo\" title=\"Yevdokiya Mekshilo\"><PERSON><PERSON><PERSON><PERSON><PERSON></a>, Russian skier (d. 2013)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Yev<PERSON>kiya_Mekshilo"}]}, {"year": "1932", "text": "<PERSON>, Canadian ice hockey player (d. 2024)", "html": "1932 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON></a>, Canadian ice hockey player (d. 2024)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON></a>, Canadian ice hockey player (d. 2024)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1933", "text": "<PERSON>, English opera singer and educator (d. 2021)", "html": "1933 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(bass-baritone)\" title=\"<PERSON> (bass-baritone)\"><PERSON></a>, English opera singer and educator (d. 2021)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(bass-baritone)\" title=\"<PERSON> (bass-baritone)\"><PERSON></a>, English opera singer and educator (d. 2021)", "links": [{"title": "<PERSON> (bass-baritone)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(bass-baritone)"}]}, {"year": "1933", "text": "<PERSON>, American psychologist and academic (d. 2024)", "html": "1933 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American psychologist and academic (d. 2024)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American psychologist and academic (d. 2024)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1934", "text": "<PERSON>, English psychologist", "html": "1934 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English psychologist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English psychologist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1934", "text": "<PERSON><PERSON><PERSON><PERSON>, Russian mathematician and physicist (d. 2017)", "html": "1934 - <a href=\"https://wikipedia.org/wiki/Ludvi<PERSON>_<PERSON>\" title=\"<PERSON>dvi<PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Russian mathematician and physicist (d. 2017)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Ludvi<PERSON>_<PERSON>\" title=\"<PERSON>dvi<PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Russian mathematician and physicist (d. 2017)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>d<PERSON><PERSON>_<PERSON>"}]}, {"year": "1935", "text": "<PERSON>, English comedian, actor and screenwriter (d. 2022)", "html": "1935 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English comedian, actor and screenwriter (d. 2022)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English comedian, actor and screenwriter (d. 2022)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1936", "text": "<PERSON><PERSON>, Greek painter and sculptor (d. 2017)", "html": "1936 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Greek painter and sculptor (d. 2017)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Greek painter and sculptor (d. 2017)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1937", "text": "<PERSON>, American race car driver (d. 2023)", "html": "1937 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American race car driver (d. 2023)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American race car driver (d. 2023)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1937", "text": "<PERSON>, American actor, comedian, boxer and football player (d. 2016)", "html": "1937 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor, comedian, boxer and football player (d. 2016)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor, comedian, boxer and football player (d. 2016)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1937", "text": "<PERSON>, American physician and academic", "html": "1937 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American physician and academic", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American physician and academic", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1938", "text": "<PERSON>, Australian actor and screenwriter (d. 2012)", "html": "1938 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian actor and screenwriter (d. 2012)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian actor and screenwriter (d. 2012)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1942", "text": "<PERSON>, Austrian director, producer and screenwriter", "html": "1942 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Austrian director, producer and screenwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Austrian director, producer and screenwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1942", "text": "<PERSON>, American record producer and musician (d. 1994)", "html": "1942 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American record producer and musician (d. 1994)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American record producer and musician (d. 1994)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1942", "text": "<PERSON>, Guyanese historian, scholar and activist (d. 1980)", "html": "1942 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Guyanese historian, scholar and activist (d. 1980)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Guyanese historian, scholar and activist (d. 1980)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1943", "text": "<PERSON>, Scottish-English economist and banker (d. 2012)", "html": "1943 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(banker)\" title=\"<PERSON> (banker)\"><PERSON></a>, Scottish-English economist and banker (d. 2012)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(banker)\" title=\"<PERSON> (banker)\"><PERSON></a>, Scottish-English economist and banker (d. 2012)", "links": [{"title": "<PERSON> (banker)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(banker)"}]}, {"year": "1943", "text": "<PERSON><PERSON><PERSON><PERSON><PERSON>, Finnish singer, author and director (d. 2001)", "html": "1943 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>-<PERSON><PERSON>_<PERSON>ap%C3%A4%C3%A4\" title=\"<PERSON><PERSON>-<PERSON><PERSON>ap<PERSON>\"><PERSON><PERSON>-<PERSON><PERSON></a>, Finnish singer, author and director (d. 2001)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>-<PERSON><PERSON>_<PERSON>ap%C3%A4%C3%A4\" title=\"<PERSON><PERSON>-<PERSON><PERSON>ap<PERSON>\"><PERSON><PERSON>-<PERSON><PERSON></a>, Finnish singer, author and director (d. 2001)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Ni<PERSON>-<PERSON><PERSON>_Valkeap%C3%A4%C3%A4"}]}, {"year": "1944", "text": "<PERSON>, English singer-songwriter and guitarist (d. 2023)", "html": "1944 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English singer-songwriter and guitarist (d. 2023)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English singer-songwriter and guitarist (d. 2023)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1944", "text": "<PERSON>, English composer of minimalist music and pianist", "html": "1944 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English composer of minimalist music and pianist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English composer of minimalist music and pianist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1944", "text": "<PERSON><PERSON>, American singer-songwriter, guitarist and producer (d. 2019)", "html": "1944 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American singer-songwriter, guitarist and producer (d. 2019)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American singer-songwriter, guitarist and producer (d. 2019)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1945", "text": "<PERSON>, Italian singer-songwriter and director (d. 2021)", "html": "1945 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian singer-songwriter and director (d. 2021)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian singer-songwriter and director (d. 2021)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Franco_Battiato"}]}, {"year": "1945", "text": "<PERSON>, American mandolin player and composer", "html": "1945 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American mandolin player and composer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American mandolin player and composer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1946", "text": "<PERSON>, English screenwriter and producer", "html": "1946 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English screenwriter and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English screenwriter and producer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1947", "text": "<PERSON>, American author", "html": "1947 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1948", "text": "<PERSON><PERSON>, Pakistani cricketer", "html": "1948 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Pakistani cricketer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Pakistani cricketer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1948", "text": "<PERSON>, German-Canadian educator and politician", "html": "1948 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German-Canadian educator and politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German-Canadian educator and politician", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1950", "text": "<PERSON><PERSON><PERSON>, French actress", "html": "1950 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>l%C3%A9ry\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, French actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>l%C3%A9ry\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, French actress", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>rinne_Cl%C3%A9ry"}]}, {"year": "1950", "text": "<PERSON>, English keyboard player and songwriter", "html": "1950 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, English keyboard player and songwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, English keyboard player and songwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1950", "text": "<PERSON><PERSON><PERSON>, Egyptian author and translator", "html": "1950 - <a href=\"https://wikipedia.org/wiki/Ahda<PERSON>_<PERSON>ue<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Egyptian author and translator", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Ahda<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Egyptian author and translator", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>da<PERSON>_<PERSON>if"}]}, {"year": "1951", "text": "<PERSON>, American football player and sportscaster", "html": "1951 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player and sportscaster", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player and sportscaster", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1951", "text": "<PERSON>, English businessman, founded Reynard Motorsport", "html": "1951 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English businessman, founded <a href=\"https://wikipedia.org/wiki/Reynard_Motorsport\" title=\"Reynard Motorsport\">Reynard Motorsport</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English businessman, founded <a href=\"https://wikipedia.org/wiki/Reynard_Motorsport\" title=\"Reynard Motorsport\">Reynard Motorsport</a>", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}, {"title": "Reynard Motorsport", "link": "https://wikipedia.org/wiki/Reynard_Motorsport"}]}, {"year": "1952", "text": "<PERSON>, Italian painter and illustrator", "html": "1952 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian painter and illustrator", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian painter and illustrator", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1952", "text": "<PERSON>, American author", "html": "1952 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1952", "text": "<PERSON>, American businessman, engineer and diplomat; 69th United States Secretary of State", "html": "1952 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American businessman, engineer and diplomat; 69th <a href=\"https://wikipedia.org/wiki/United_States_Secretary_of_State\" title=\"United States Secretary of State\">United States Secretary of State</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American businessman, engineer and diplomat; 69th <a href=\"https://wikipedia.org/wiki/United_States_Secretary_of_State\" title=\"United States Secretary of State\">United States Secretary of State</a>", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "United States Secretary of State", "link": "https://wikipedia.org/wiki/United_States_Secretary_of_State"}]}, {"year": "1953", "text": "<PERSON>, Venezuelan baseball player (d. 1990)", "html": "1953 - <a href=\"https://wikipedia.org/wiki/Bo_D%C3%ADaz\" title=\"<PERSON>\"><PERSON></a>, Venezuelan baseball player (d. 1990)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Bo_D%C3%ADaz\" title=\"<PERSON>\"><PERSON></a>, Venezuelan baseball player (d. 1990)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Bo_D%C3%ADaz"}]}, {"year": "1953", "text": "<PERSON><PERSON>, American singer-songwriter", "html": "1953 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American singer-songwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American singer-songwriter", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1953", "text": "<PERSON>-<PERSON>, Indian zoologist and businesswoman", "html": "1953 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>-<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Indian zoologist and businesswoman", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>-<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Indian zoologist and businesswoman", "links": [{"title": "<PERSON>-<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>-<PERSON>"}]}, {"year": "1954", "text": "<PERSON><PERSON>, Italian-American basketball player and coach", "html": "1954 - <a href=\"https://wikipedia.org/wiki/Geno_Auriemma\" title=\"Geno Auriemma\"><PERSON><PERSON></a>, Italian-American basketball player and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Geno_Auriemma\" title=\"Geno Auriemma\"><PERSON><PERSON></a>, Italian-American basketball player and coach", "links": [{"title": "Geno Auriemma", "link": "https://wikipedia.org/wiki/Geno_Auriemma"}]}, {"year": "1954", "text": "<PERSON>, American fashion designer, founded Kenneth <PERSON> Productions", "html": "1954 - <a href=\"https://wikipedia.org/wiki/<PERSON>(designer)\" title=\"<PERSON> (designer)\"><PERSON></a>, American fashion designer, founded <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"Kenneth <PERSON> Productions\"><PERSON></a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>(designer)\" title=\"<PERSON> (designer)\"><PERSON></a>, American fashion designer, founded <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"Kenneth <PERSON> Productions\"><PERSON></a>", "links": [{"title": "<PERSON> (designer)", "link": "https://wikipedia.org/wiki/<PERSON>(designer)"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1954", "text": "<PERSON>, Scottish Labour Party politician", "html": "1954 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish Labour Party politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish Labour Party politician", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1954", "text": "<PERSON>, English-Welsh footballer and manager", "html": "1954 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(footballer)\" title=\"<PERSON> (footballer)\"><PERSON></a>, English-Welsh footballer and manager", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(footballer)\" title=\"<PERSON> (footballer)\"><PERSON></a>, English-Welsh footballer and manager", "links": [{"title": "<PERSON> (footballer)", "link": "https://wikipedia.org/wiki/<PERSON>_(footballer)"}]}, {"year": "1955", "text": "<PERSON>, American basketball player (d. 2015)", "html": "1955 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player (d. 2015)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player (d. 2015)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1956", "text": "<PERSON>, Portuguese academic and politician, 115th Prime Minister of Portugal", "html": "1956 - <a href=\"https://wikipedia.org/wiki/Jos%C3%A<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Portuguese academic and politician, 115th <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Portugal\" title=\"Prime Minister of Portugal\">Prime Minister of Portugal</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Jo<PERSON>%C3%A<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Portuguese academic and politician, 115th <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Portugal\" title=\"Prime Minister of Portugal\">Prime Minister of Portugal</a>", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Jos%C3%A<PERSON>_<PERSON>_<PERSON>"}, {"title": "Prime Minister of Portugal", "link": "https://wikipedia.org/wiki/Prime_Minister_of_Portugal"}]}, {"year": "1957", "text": "<PERSON><PERSON>, Ecuadorian politician, 52nd President of Ecuador", "html": "1957 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_Guti%C3%A9rrez\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Ecuadorian politician, 52nd <a href=\"https://wikipedia.org/wiki/President_of_Ecuador\" title=\"President of Ecuador\">President of Ecuador</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_G<PERSON>%C3%A9rrez\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Ecuadorian politician, 52nd <a href=\"https://wikipedia.org/wiki/President_of_Ecuador\" title=\"President of Ecuador\">President of Ecuador</a>", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Lucio_Guti%C3%A9rrez"}, {"title": "President of Ecuador", "link": "https://wikipedia.org/wiki/President_of_Ecuador"}]}, {"year": "1957", "text": "<PERSON>, Welsh footballer and manager (d. 1998)", "html": "1957 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Welsh footballer and manager (d. 1998)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Welsh footballer and manager (d. 1998)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1957", "text": "<PERSON>, American actress", "html": "1957 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1958", "text": "<PERSON><PERSON><PERSON>, Belgian cyclist", "html": "1958 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Belgian cyclist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Belgian cyclist", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1958", "text": "<PERSON>, Scottish business executive", "html": "1958 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(business_executive)\" title=\"<PERSON> (business executive)\"><PERSON></a>, Scottish business executive", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(business_executive)\" title=\"<PERSON> (business executive)\"><PERSON></a>, Scottish business executive", "links": [{"title": "<PERSON> (business executive)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>(business_executive)"}]}, {"year": "1958", "text": "<PERSON><PERSON><PERSON><PERSON><PERSON>, Swedish ice hockey player and coach", "html": "1958 - <a href=\"https://wikipedia.org/wiki/Bengt-%C3%85<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>-<PERSON><PERSON>\"><PERSON><PERSON>-<PERSON><PERSON></a>, Swedish ice hockey player and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Bengt-%C3%85<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>-<PERSON><PERSON>\"><PERSON><PERSON>-<PERSON><PERSON></a>, Swedish ice hockey player and coach", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Bengt-%C3%85<PERSON>_<PERSON><PERSON>"}]}, {"year": "1959", "text": "<PERSON>, American actress", "html": "1959 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1960", "text": "<PERSON><PERSON>, Greek actor, screenwriter, and lyricist", "html": "1960 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Greek actor, screenwriter, and lyricist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Greek actor, screenwriter, and lyricist", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1960", "text": "<PERSON><PERSON>, Baron <PERSON>, Scottish lawyer and politician, 2nd Deputy First Minister of Scotland", "html": "1960 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>,_Baron_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON><PERSON>, Baron <PERSON>\"><PERSON><PERSON>, Baron <PERSON></a>, Scottish lawyer and politician, 2nd <a href=\"https://wikipedia.org/wiki/Deputy_First_Minister_of_Scotland\" title=\"Deputy First Minister of Scotland\">Deputy First Minister of Scotland</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>,_Baron_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON><PERSON>, Baron <PERSON>\"><PERSON><PERSON>, Baron <PERSON></a>, Scottish lawyer and politician, 2nd <a href=\"https://wikipedia.org/wiki/Deputy_First_Minister_of_Scotland\" title=\"Deputy First Minister of Scotland\">Deputy First Minister of Scotland</a>", "links": [{"title": "<PERSON><PERSON>, Baron <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>,_<PERSON>_<PERSON>"}, {"title": "Deputy First Minister of Scotland", "link": "https://wikipedia.org/wiki/Deputy_First_Minister_of_Scotland"}]}, {"year": "1961", "text": "<PERSON>, English philosopher and academic", "html": "1961 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English philosopher and academic", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English philosopher and academic", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1961", "text": "<PERSON>, New Zealand rugby player", "html": "1961 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(rugby_union)\" title=\"<PERSON> (rugby union)\"><PERSON></a>, New Zealand rugby player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(rugby_union)\" title=\"<PERSON> (rugby union)\"><PERSON></a>, New Zealand rugby player", "links": [{"title": "<PERSON> (rugby union)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(rugby_union)"}]}, {"year": "1961", "text": "<PERSON><PERSON><PERSON>, Indonesian journalist and producer", "html": "1961 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Indonesian journalist and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Indonesian journalist and producer", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1962", "text": "<PERSON>, English rower", "html": "1962 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English rower", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English rower", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1963", "text": "<PERSON>, Spanish footballer and manager", "html": "1963 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%B3n_L%C3%B3pez_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Spanish footballer and manager", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%B3n_L%C3%B3pez_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Spanish footballer and manager", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%B3n_L%C3%B3pez_Caro"}]}, {"year": "1963", "text": "<PERSON><PERSON><PERSON>, Spanish footballer and manager", "html": "1963 - <a href=\"https://wikipedia.org/wiki/M%C3%<PERSON><PERSON>_(footballer,_born_1963)\" title=\"<PERSON><PERSON><PERSON> (footballer, born 1963)\"><PERSON><PERSON><PERSON></a>, Spanish footballer and manager", "no_year_html": "<a href=\"https://wikipedia.org/wiki/M%C3%<PERSON><PERSON>_(footballer,_born_1963)\" title=\"<PERSON><PERSON><PERSON> (footballer, born 1963)\"><PERSON><PERSON><PERSON></a>, Spanish footballer and manager", "links": [{"title": "<PERSON><PERSON><PERSON> (footballer, born 1963)", "link": "https://wikipedia.org/wiki/M%C3%<PERSON><PERSON>_(footballer,_born_1963)"}]}, {"year": "1963", "text": "<PERSON>, Cuban runner", "html": "1963 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>del<PERSON>_Quirot\" title=\"<PERSON> Fidelia Quirot\"><PERSON></a>, Cuban runner", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>del<PERSON>_Quirot\" title=\"<PERSON> Fidelia Quirot\"><PERSON></a>, Cuban runner", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON><PERSON>_Quirot"}]}, {"year": "1964", "text": "<PERSON>, American actress", "html": "1964 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1965", "text": "<PERSON>, American actor, artist, and model", "html": "1965 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor, artist, and model", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor, artist, and model", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1965", "text": "<PERSON>, American poet and painter", "html": "1965 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American poet and painter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American poet and painter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1966", "text": "<PERSON>, American sprinter", "html": "1966 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American sprinter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American sprinter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1966", "text": "<PERSON>, American actress", "html": "1966 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1966", "text": "<PERSON><PERSON><PERSON>, Greek footballer and manager", "html": "1966 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Greek footballer and manager", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Greek footballer and manager", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1968", "text": "<PERSON>, English singer-songwriter, producer and actor", "html": "1968 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English singer-songwriter, producer and actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English singer-songwriter, producer and actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1968", "text": "<PERSON>, English cricketer and journalist", "html": "1968 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, English cricketer and journalist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, English cricketer and journalist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1968", "text": "<PERSON>, Spanish footballer and manager", "html": "1968 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Spanish footballer and manager", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Spanish footballer and manager", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1968", "text": "<PERSON>, French actor and screenwriter", "html": "1968 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French actor and screenwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French actor and screenwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1971", "text": "<PERSON><PERSON><PERSON>, Canadian model", "html": "1971 - <a href=\"https://wikipedia.org/wiki/Ya<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Canadian model", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Ya<PERSON><PERSON>_<PERSON><PERSON>\" title=\"Ya<PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Canadian model", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON><PERSON>"}]}, {"year": "1971", "text": "<PERSON>, Scottish model and television host", "html": "1971 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish model and television host", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish model and television host", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1971", "text": "<PERSON>, Russian ice hockey player", "html": "1971 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian ice hockey player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian ice hockey player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1971", "text": "<PERSON><PERSON><PERSON>, Japanese wrestler", "html": "1971 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese wrestler", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese wrestler", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1972", "text": "<PERSON>, Swedish-Monégasque tennis player and coach", "html": "1972 - <a href=\"https://wikipedia.org/wiki/<PERSON>_Bj%C3%B6rkman\" title=\"<PERSON>\"><PERSON></a>, Swedish-Monégasque tennis player and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_Bj%C3%B6<PERSON><PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swedish-Monégasque tennis player and coach", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_Bj%C3%B6rkman"}]}, {"year": "1972", "text": "<PERSON>, Welsh boxer", "html": "1972 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Welsh boxer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Welsh boxer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1972", "text": "<PERSON>, French actress and author", "html": "1972 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A8che\" title=\"<PERSON>\"><PERSON></a>, French actress and author", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A8che\" title=\"<PERSON>\"><PERSON></a>, French actress and author", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A8che"}]}, {"year": "1973", "text": "<PERSON><PERSON><PERSON>, Polish footballer", "html": "1973 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Polish footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Polish footballer", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1973", "text": "<PERSON><PERSON>, Belgian race car driver", "html": "1973 - <a href=\"https://wikipedia.org/wiki/Wim_<PERSON>\" title=\"Wim <PERSON>\"><PERSON><PERSON></a>, Belgian race car driver", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Wim_<PERSON>\" title=\"Wim <PERSON>\"><PERSON><PERSON></a>, Belgian race car driver", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Wim_<PERSON>"}]}, {"year": "1973", "text": "<PERSON>, American basketball player and coach", "html": "1973 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player and coach", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1973", "text": "<PERSON><PERSON>, Serbian-Hungarian handball player", "html": "1973 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>i%C4%87\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Serbian-Hungarian handball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>i%C4%87\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Serbian-Hungarian handball player", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Bojana_Radulovi%C4%87"}]}, {"year": "1974", "text": "<PERSON>, New Zealand mixed martial artist", "html": "1974 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, New Zealand mixed martial artist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, New Zealand mixed martial artist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1974", "text": "<PERSON>, American actor, director and screenwriter", "html": "1974 - <a href=\"https://wikipedia.org/wiki/Randall_Park\" title=\"Randall Park\"><PERSON></a>, American actor, director and screenwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Randall_Park\" title=\"Randall Park\"><PERSON></a>, American actor, director and screenwriter", "links": [{"title": "Randall <PERSON>", "link": "https://wikipedia.org/wiki/Randall_Park"}]}, {"year": "1975", "text": "<PERSON><PERSON><PERSON>, Turkish drummer", "html": "1975 - <a href=\"https://wikipedia.org/wiki/Burak_G%C3%BCrp%C4%B1nar\" title=\"<PERSON><PERSON><PERSON>ürpınar\"><PERSON><PERSON><PERSON></a>, Turkish drummer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Burak_G%C3%BCrp%C4%B1nar\" title=\"<PERSON><PERSON><PERSON> Gürpınar\"><PERSON><PERSON><PERSON></a>, Turkish drummer", "links": [{"title": "Burak Gürpınar", "link": "https://wikipedia.org/wiki/Burak_G%C3%BCrp%C4%B1nar"}]}, {"year": "1975", "text": "<PERSON>, English footballer and manager", "html": "1975 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>(footballer)\" title=\"<PERSON> (footballer)\"><PERSON></a>, English footballer and manager", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>(footballer)\" title=\"<PERSON> (footballer)\"><PERSON></a>, English footballer and manager", "links": [{"title": "<PERSON> (footballer)", "link": "https://wikipedia.org/wiki/<PERSON>(footballer)"}]}, {"year": "1976", "text": "<PERSON>, Scottish cyclist", "html": "1976 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish cyclist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish cyclist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1976", "text": "<PERSON><PERSON><PERSON><PERSON>, Indian actress, producer and politician, Indian Minister of Human Resource Development", "html": "1976 - <a href=\"https://wikipedia.org/wiki/Smriti_Irani\" title=\"Smriti <PERSON>i\"><PERSON><PERSON><PERSON><PERSON></a>, Indian actress, producer and politician, <a href=\"https://wikipedia.org/wiki/Ministry_of_Human_Resource_Development_(India)\" class=\"mw-redirect\" title=\"Ministry of Human Resource Development (India)\">Indian Minister of Human Resource Development</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Smriti_Irani\" title=\"Smriti <PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Indian actress, producer and politician, <a href=\"https://wikipedia.org/wiki/Ministry_of_Human_Resource_Development_(India)\" class=\"mw-redirect\" title=\"Ministry of Human Resource Development (India)\">Indian Minister of Human Resource Development</a>", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Smriti_Irani"}, {"title": "Ministry of Human Resource Development (India)", "link": "https://wikipedia.org/wiki/Ministry_of_Human_Resource_Development_(India)"}]}, {"year": "1976", "text": "<PERSON><PERSON>, English motorcycle racer", "html": "1976 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, English motorcycle racer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, English motorcycle racer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1976", "text": "<PERSON>, American actress", "html": "1976 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1976", "text": "<PERSON>, Dominican baseball player", "html": "1976 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dominican baseball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dominican baseball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1976", "text": "<PERSON><PERSON>, American actress", "html": "1976 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American actress", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1976", "text": "<PERSON>, Chinese television host", "html": "1976 - <a href=\"https://wikipedia.org/wiki/Benny_Sa\" title=\"Benny Sa\"><PERSON></a>, Chinese television host", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Benny_Sa\" title=\"Benny Sa\"><PERSON></a>, Chinese television host", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1976", "text": "<PERSON>, Brazilian race car driver", "html": "1976 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Brazilian race car driver", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Brazilian race car driver", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1977", "text": "<PERSON><PERSON><PERSON>, Canadian actor and screenwriter", "html": "1977 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Canadian actor and screenwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Canadian actor and screenwriter", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1978", "text": "<PERSON>, Swedish illustrator", "html": "1978 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A4rdenfors\" title=\"<PERSON>\"><PERSON></a>, Swedish illustrator", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A4rdenfors\" title=\"<PERSON>\"><PERSON></a>, Swedish illustrator", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Simon_G%C3%A4rdenfors"}]}, {"year": "1978", "text": "<PERSON>, American blogger", "html": "1978 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Hilton\"><PERSON></a>, American blogger", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Hilton\"><PERSON></a>, American blogger", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1978", "text": "<PERSON>, Argentine footballer", "html": "1978 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Argentine footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Argentine footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1978", "text": "<PERSON><PERSON><PERSON>, American actress", "html": "1978 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American actress", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1978", "text": "<PERSON>, Chinese actor", "html": "1978 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(actor)\" title=\"<PERSON> (actor)\"><PERSON></a>, Chinese actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(actor)\" title=\"<PERSON> (actor)\"><PERSON></a>, Chinese actor", "links": [{"title": "<PERSON> (actor)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(actor)"}]}, {"year": "1979", "text": "<PERSON>, American baseball player", "html": "1979 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1979", "text": "<PERSON><PERSON><PERSON>, Irish rugby player", "html": "1979 - <a href=\"https://wikipedia.org/wiki/Donncha_O%27Callaghan\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Irish rugby player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Donnch<PERSON>_<PERSON>%27C<PERSON>aghan\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Irish rugby player", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Donncha_O%27<PERSON><PERSON><PERSON><PERSON>"}]}, {"year": "1981", "text": "<PERSON>, Czech ice hockey player", "html": "1981 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Czech ice hockey player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Czech ice hockey player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1981", "text": "<PERSON>, American race car driver", "html": "1981 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American race car driver", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American race car driver", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1981", "text": "<PERSON>, Dominican baseball player", "html": "1981 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%B1a_<PERSON>.\" title=\"<PERSON>\"><PERSON>.</a>, Dominican baseball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%B1a_<PERSON>.\" title=\"<PERSON>\"><PERSON>.</a>, Dominican baseball player", "links": [{"title": "<PERSON>.", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>e%C3%B1a_<PERSON>."}]}, {"year": "1981", "text": "<PERSON>, English bobsledder", "html": "1981 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English bobsledder", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English bobsledder", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1981", "text": "<PERSON>, Italian footballer", "html": "1981 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1981", "text": "<PERSON>, American singer-songwriter", "html": "1981 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>(singer)\" title=\"<PERSON> (singer)\"><PERSON></a>, American singer-songwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>(singer)\" title=\"<PERSON> (singer)\"><PERSON></a>, American singer-songwriter", "links": [{"title": "<PERSON> (singer)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>(singer)"}]}, {"year": "1982", "text": "<PERSON>, Chilean footballer", "html": "1982 - <a href=\"https://wikipedia.org/wiki/Jos%C3%A9_Contreras_Arrau\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, Chilean footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Jos%C3%A9_Contreras_Arrau\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, Chilean footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Jos%C3%A9_<PERSON><PERSON><PERSON>_<PERSON>rrau"}]}, {"year": "1982", "text": "<PERSON>, Italian footballer", "html": "1982 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1982", "text": "<PERSON><PERSON><PERSON><PERSON>, Estonian ice dancer", "html": "1982 - <a href=\"https://wikipedia.org/wiki/Evgen<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Estonian ice dancer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Ev<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Estonian ice dancer", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Evgen<PERSON>_<PERSON>v"}]}, {"year": "1983", "text": "<PERSON><PERSON>, Turkish footballer", "html": "1983 - <a href=\"https://wikipedia.org/wiki/Ha<PERSON>_<PERSON>lta\" title=\"Hakan Balta\"><PERSON><PERSON></a>, Turkish footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Ha<PERSON>_<PERSON>lta\" title=\"Hakan Balta\"><PERSON><PERSON></a>, Turkish footballer", "links": [{"title": "Hakan Ba<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>lta"}]}, {"year": "1983", "text": "<PERSON>, Somali-English runner", "html": "1983 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Somali-English runner", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Somali-English runner", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1983", "text": "<PERSON><PERSON><PERSON>, German footballer", "html": "1983 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, German footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, German footballer", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON><PERSON><PERSON>"}]}, {"year": "1983", "text": "<PERSON>, English footballer", "html": "1983 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1984", "text": "<PERSON>, Filipino basketball player", "html": "1984 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%B1a\" title=\"<PERSON>\"><PERSON></a>, Filipino basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%B1a\" title=\"<PERSON>\"><PERSON></a>, Filipino basketball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%B1a"}]}, {"year": "1984", "text": "<PERSON>, American football player", "html": "1984 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1985", "text": "<PERSON>-<PERSON>, American football player", "html": "1985 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player", "links": [{"title": "<PERSON>-<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1985", "text": "<PERSON><PERSON><PERSON><PERSON>, American tennis player", "html": "1985 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American tennis player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American tennis player", "links": [{"title": "<PERSON><PERSON><PERSON>-<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>-<PERSON>"}]}, {"year": "1986", "text": "<PERSON>, Canadian ice hockey player", "html": "1986 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1986", "text": "<PERSON>, Italian motorcycle racer", "html": "1986 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian motorcycle racer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian motorcycle racer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1986", "text": "<PERSON>, American singer-songwriter and musician", "html": "1986 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and musician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and musician", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1986", "text": "<PERSON><PERSON>, Indian actress", "html": "1986 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Indian actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Indian actress", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Kang<PERSON>_<PERSON>ut"}]}, {"year": "1987", "text": "<PERSON>, Australian footballer", "html": "1987 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1988", "text": "<PERSON><PERSON>, American baseball player", "html": "1988 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American baseball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American baseball player", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>nces"}]}, {"year": "1988", "text": "<PERSON>, English cyclist", "html": "1988 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English cyclist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English cyclist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1988", "text": "<PERSON><PERSON>, Czech ice hockey player", "html": "1988 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Czech ice hockey player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Czech ice hockey player", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1989", "text": "<PERSON><PERSON>, Canadian-American chef, author and television personality", "html": "1989 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Canadian-American chef, author and television personality", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Canadian-American chef, author and television personality", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1989", "text": "<PERSON>, Serbian footballer", "html": "1989 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Serbian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Serbian footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1989", "text": "<PERSON>, Mexican footballer", "html": "1989 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Mexican footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Mexican footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1990", "text": "<PERSON>, Spanish race car driver", "html": "1990 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Spanish race car driver", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Spanish race car driver", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1990", "text": "<PERSON>, Canadian ice hockey player", "html": "1990 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1990", "text": "Princess <PERSON><PERSON><PERSON>, English royal", "html": "1990 - <a href=\"https://wikipedia.org/wiki/Princess_<PERSON><PERSON><PERSON>\" title=\"Princess <PERSON><PERSON><PERSON>\">Princess <PERSON><PERSON><PERSON></a>, English royal", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Princess_<PERSON><PERSON><PERSON>\" title=\"Princess <PERSON><PERSON><PERSON>\">Princess <PERSON><PERSON><PERSON></a>, English royal", "links": [{"title": "Princess <PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Princess_<PERSON><PERSON><PERSON>"}]}, {"year": "1990", "text": "<PERSON>, American basketball player", "html": "1990 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1990", "text": "<PERSON>, German footballer", "html": "1990 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1991", "text": "<PERSON><PERSON><PERSON><PERSON>, Argentine basketball player", "html": "1991 - <a href=\"https://wikipedia.org/wiki/Facundo_Campazzo\" title=\"Facundo Campazzo\"><PERSON>ac<PERSON><PERSON></a>, Argentine basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Facundo_Campazzo\" title=\"Facundo Campazzo\"><PERSON>ac<PERSON><PERSON></a>, Argentine basketball player", "links": [{"title": "Facundo <PERSON>azzo", "link": "https://wikipedia.org/wiki/Facundo_Campazzo"}]}, {"year": "1991", "text": "<PERSON>, Finnish ice hockey player", "html": "1991 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Finnish ice hockey player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Finnish ice hockey player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1991", "text": "<PERSON>, Scottish footballer", "html": "1991 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1992", "text": "<PERSON><PERSON><PERSON>, German-Turkish footballer", "html": "1992 - <a href=\"https://wikipedia.org/wiki/Tolga_Ci%C4%9Ferci\" title=\"Tolga Ciğerci\"><PERSON><PERSON><PERSON></a>, German-Turkish footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Tolga_Ci%C4%9Ferci\" title=\"<PERSON>lga Ciğerci\"><PERSON><PERSON><PERSON>ğ<PERSON></a>, German-Turkish footballer", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Tolga_Ci%C4%9Ferci"}]}, {"year": "1992", "text": "<PERSON><PERSON><PERSON>, Australian-American basketball player", "html": "1992 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Australian-American basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Australian-American basketball player", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1992", "text": "<PERSON>, Canadian actress", "html": "1992 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian actress", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1993", "text": "<PERSON>, American basketball player", "html": "1993 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1993", "text": "<PERSON><PERSON><PERSON>, Czech ice hockey player", "html": "1993 - <a href=\"https://wikipedia.org/wiki/Tom%C3%A1%C5%A1_Hyka\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Czech ice hockey player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Tom%C3%A1%C5%A1_Hyka\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Czech ice hockey player", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Tom%C3%A1%C5%A1_Hyka"}]}, {"year": "1993", "text": "<PERSON><PERSON><PERSON><PERSON>, Russian-Czech ice hockey player", "html": "1993 - <a href=\"https://wikipedia.org/wiki/Dmit<PERSON>j_Ja%C5%A1kin\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Russian-Czech ice hockey player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/D<PERSON><PERSON>j_Ja%C5%A1kin\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Russian-Czech ice hockey player", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Dmit<PERSON>j_Ja%C5%A1kin"}]}, {"year": "1993", "text": "<PERSON><PERSON><PERSON><PERSON>, Turkish footballer", "html": "1993 - <a href=\"https://wikipedia.org/wiki/Ayta%C3%A7_Kara\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Turkish footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Ayta%C3%A7_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Turkish footballer", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Ayta%C3%A7_Kara"}]}, {"year": "1994", "text": "<PERSON>, English footballer", "html": "1994 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1994", "text": "<PERSON><PERSON>, Swedish ice hockey player", "html": "1994 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Swedish ice hockey player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Swedish ice hockey player", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>"}]}, {"year": "1994", "text": "<PERSON><PERSON>, American actor", "html": "1994 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American actor", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1995", "text": "<PERSON>, Estonian footballer", "html": "1995 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Estonian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Estonian footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1995", "text": "<PERSON>, Canadian pianist", "html": "1995 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian pianist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian pianist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1995", "text": "<PERSON>, American actress", "html": "1995 - <a href=\"https://wikipedia.org/wiki/Victoria_Ped<PERSON>ti\" title=\"<PERSON>ti\"><PERSON></a>, American actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Victoria_Pedretti\" title=\"<PERSON> Pedretti\"><PERSON></a>, American actress", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Victoria_Pedretti"}]}, {"year": "1995", "text": "<PERSON><PERSON>, Turkish footballer", "html": "1995 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Turkish footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Turkish footballer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1996", "text": "<PERSON>, Thai-British race car driver", "html": "1996 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, Thai-British race car driver", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, Thai-British race car driver", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1996", "text": "<PERSON>, Finnish ice hockey player", "html": "1996 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Finnish ice hockey player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Finnish ice hockey player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1997", "text": "<PERSON>, Australian cricketer", "html": "1997 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian cricketer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian cricketer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}], "Deaths": [{"year": "59", "text": "<PERSON><PERSON><PERSON><PERSON> the Younger, Roman empress (b. 15)", "html": "59 - <a href=\"https://wikipedia.org/wiki/AD_59\" title=\"AD 59\">59</a> - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_the_Younger\" title=\"<PERSON><PERSON><PERSON><PERSON> the Younger\"><PERSON><PERSON><PERSON><PERSON> the Younger</a>, Roman empress (b. 15)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/AD_59\" title=\"AD 59\">59</a> - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_the_Younger\" title=\"<PERSON><PERSON><PERSON><PERSON> the Younger\"><PERSON><PERSON><PERSON><PERSON> the Younger</a>, Roman empress (b. 15)", "links": [{"title": "AD 59", "link": "https://wikipedia.org/wiki/AD_59"}, {"title": "<PERSON><PERSON><PERSON><PERSON> the Younger", "link": "https://wikipedia.org/wiki/Ag<PERSON><PERSON>_the_Younger"}]}, {"year": "851", "text": "<PERSON>, Chinese historian and politician (b. 793)", "html": "851 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Chinese historian and politician (b. 793)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Chinese historian and politician (b. 793)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1022", "text": "<PERSON><PERSON>, Chinese emperor (b. 968)", "html": "1022 - <a href=\"https://wikipedia.org/wiki/Emperor_<PERSON><PERSON><PERSON>\" title=\"Emperor <PERSON><PERSON><PERSON>\"><PERSON><PERSON></a>, Chinese emperor (b. 968)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Emperor_<PERSON><PERSON><PERSON>\" title=\"Emperor <PERSON><PERSON><PERSON>\"><PERSON><PERSON></a>, Chinese emperor (b. 968)", "links": [{"title": "Emperor <PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Emperor_<PERSON><PERSON><PERSON>"}]}, {"year": "1103", "text": "<PERSON><PERSON>, duke of Burgundy (b. 1058)", "html": "1103 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>,_Duke_of_Burgundy\" class=\"mw-redirect\" title=\"<PERSON><PERSON> <PERSON>, Duke of Burgundy\"><PERSON><PERSON> I</a>, duke of Burgundy (b. 1058)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>,_Duke_of_Burgundy\" class=\"mw-redirect\" title=\"<PERSON><PERSON> <PERSON>, Duke of Burgundy\"><PERSON><PERSON></a>, duke of Burgundy (b. 1058)", "links": [{"title": "<PERSON><PERSON>, Duke of Burgundy", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>,_Duke_of_Burgundy"}]}, {"year": "1361", "text": "<PERSON> Grosmont, 1st Duke of Lancaster, English politician, Lord <PERSON> of England (b. 1310)", "html": "1361 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>,_1st_Duke_of_Lancaster\" class=\"mw-redirect\" title=\"<PERSON> of Grosmont, 1st Duke of Lancaster\"><PERSON> of Grosmont, 1st Duke of Lancaster</a>, English politician, <a href=\"https://wikipedia.org/wiki/Lord_<PERSON>_St<PERSON>ard\" title=\"Lord <PERSON> Steward\">Lord <PERSON> Stew<PERSON> of England</a> (b. 1310)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>,_1st_Duke_of_Lancaster\" class=\"mw-redirect\" title=\"<PERSON> of Grosmont, 1st Duke of Lancaster\"><PERSON> of Grosmont, 1st Duke of Lancaster</a>, English politician, <a href=\"https://wikipedia.org/wiki/Lord_<PERSON>_St<PERSON>ard\" title=\"Lord <PERSON> Steward\">Lord <PERSON> Steward of England</a> (b. 1310)", "links": [{"title": "<PERSON> Grosmont, 1st Duke of Lancaster", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>,_1st_Duke_of_Lancaster"}, {"title": "Lord High Steward", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_Steward"}]}, {"year": "1369", "text": "<PERSON>, king of Castile and León (b. 1334)", "html": "1369 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_Castile\" title=\"<PERSON> of Castile\"><PERSON></a>, king of Castile and León (b. 1334)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_Castile\" title=\"<PERSON> of Castile\"><PERSON></a>, king of Castile and León (b. 1334)", "links": [{"title": "<PERSON> of Castile", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1483", "text": "<PERSON><PERSON><PERSON>, duchess of Lorraine (b. 1428)", "html": "1483 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>,_Duchess_of_Lorraine\" title=\"<PERSON><PERSON><PERSON>, Duchess of Lorraine\"><PERSON><PERSON><PERSON></a>, duchess of Lorraine (b. 1428)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>,_Duchess_of_Lorraine\" title=\"<PERSON><PERSON><PERSON>, Duchess of Lorraine\"><PERSON><PERSON><PERSON></a>, duchess of Lorraine (b. 1428)", "links": [{"title": "<PERSON><PERSON><PERSON>, Duchess of Lorraine", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>,_Duchess_of_Lorraine"}]}, {"year": "1548", "text": "<PERSON><PERSON><PERSON>, Japanese samurai (b. 1489)", "html": "1548 - <a href=\"https://wikipedia.org/wiki/Itagaki_Nobukata\" title=\"Itagaki Nobukata\"><PERSON><PERSON><PERSON></a>, Japanese samurai (b. 1489)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Itaga<PERSON>_<PERSON>kata\" title=\"Itagaki Nobukata\"><PERSON><PERSON><PERSON></a>, Japanese samurai (b. 1489)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Itagaki_Nobukata"}]}, {"year": "1555", "text": "<PERSON>, pope of the Catholic Church (b. 1487)", "html": "1555 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_III\" title=\"Pope Julius <PERSON>\"><PERSON></a>, pope of the Catholic Church (b. 1487)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"Pope Julius <PERSON>\"><PERSON></a>, pope of the Catholic Church (b. 1487)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1559", "text": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, Ethiopian emperor (b. 1521)", "html": "1559 - <a href=\"https://wikipedia.org/wiki/G<PERSON>wdewos\" title=\"G<PERSON>wdewos\"><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON></a>, Ethiopian emperor (b. 1521)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/G<PERSON>wdewos\" title=\"<PERSON><PERSON>wdewos\"><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON></a>, Ethiopian emperor (b. 1521)", "links": [{"title": "Gelawdewos", "link": "https://wikipedia.org/wiki/Gelawdewos"}]}, {"year": "1596", "text": "<PERSON>, English diplomat (b. 1557)", "html": "1596 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English diplomat (b. 1557)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English diplomat (b. 1557)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1606", "text": "<PERSON><PERSON>, Flemish philologist and scholar (b. 1547)", "html": "1606 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Flemish philologist and scholar (b. 1547)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Flemish philologist and scholar (b. 1547)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1618", "text": "<PERSON>, 1st Earl of Abercorn, Scottish police officer and politician (b. 1575)", "html": "1618 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_1st_Earl_of_Abercorn\" title=\"<PERSON>, 1st Earl of Abercorn\"><PERSON>, 1st Earl of Abercorn</a>, Scottish police officer and politician (b. 1575)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_1st_Earl_of_Abercorn\" title=\"<PERSON>, 1st Earl of Abercorn\"><PERSON>, 1st Earl of Abercorn</a>, Scottish police officer and politician (b. 1575)", "links": [{"title": "<PERSON>, 1st Earl of Abercorn", "link": "https://wikipedia.org/wiki/<PERSON>,_1st_Earl_of_<PERSON>orn"}]}, {"year": "1629", "text": "<PERSON>, 1st Earl of Westmorland, English landowner and politician (b. 1580)", "html": "1629 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_1st_Earl_of_Westmorland\" title=\"<PERSON>, 1st Earl of Westmorland\"><PERSON>, 1st Earl of Westmorland</a>, English landowner and politician (b. 1580)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>,_1st_Earl_of_Westmorland\" title=\"<PERSON>, 1st Earl of Westmorland\"><PERSON>, 1st Earl of Westmorland</a>, English landowner and politician (b. 1580)", "links": [{"title": "<PERSON>, 1st Earl of Westmorland", "link": "https://wikipedia.org/wiki/<PERSON>,_1st_Earl_of_Westmorland"}]}, {"year": "1675", "text": "<PERSON><PERSON><PERSON>, Dutch organist and composer (b. 1619)", "html": "1675 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Dutch organist and composer (b. 1619)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Dutch organist and composer (b. 1619)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1680", "text": "<PERSON>, French politician (b. 1615)", "html": "1680 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French politician (b. 1615)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French politician (b. 1615)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1712", "text": "<PERSON><PERSON><PERSON>, Lithuanian-born rabbi and writer (b. c. 1650)", "html": "1712 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>danover\"><PERSON><PERSON><PERSON></a>, Lithuanian-born rabbi and writer (b. c. 1650)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>over\"><PERSON><PERSON><PERSON></a>, Lithuanian-born rabbi and writer (b. c. 1650)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "1742", "text": "<PERSON><PERSON><PERSON>, French historian and author (b. 1670)", "html": "1742 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, French historian and author (b. 1670)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, French historian and author (b. 1670)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>"}]}, {"year": "1747", "text": "<PERSON>, French general (b. 1675)", "html": "1747 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, French general (b. 1675)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, French general (b. 1675)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1748", "text": "<PERSON>, German organist and composer (b. 1684)", "html": "1748 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German organist and composer (b. 1684)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German organist and composer (b. 1684)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1754", "text": "<PERSON>, Swiss theologian and critic (b. 1693)", "html": "1754 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swiss theologian and critic (b. 1693)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swiss theologian and critic (b. 1693)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1783", "text": "<PERSON>, English barrister and politician (b. 1723)", "html": "1783 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(barrister)\" title=\"<PERSON> (barrister)\"><PERSON></a>, English barrister and politician (b. 1723)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>(barrister)\" title=\"<PERSON> (barrister)\"><PERSON></a>, English barrister and politician (b. 1723)", "links": [{"title": "<PERSON> (barrister)", "link": "https://wikipedia.org/wiki/<PERSON>(barrister)"}]}, {"year": "1792", "text": "<PERSON><PERSON>, Portuguese philosopher and pedagogue (b. 1713)", "html": "1792 - <a href=\"https://wikipedia.org/wiki/Lu%C3%ADs_Ant%C3%B3nio_Verney\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Portuguese philosopher and pedagogue (b. 1713)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Lu%C3%ADs_Ant%C3%B3nio_Verney\" title=\"<PERSON><PERSON>erne<PERSON>\"><PERSON><PERSON></a>, Portuguese philosopher and pedagogue (b. 1713)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Lu%C3%ADs_Ant%C3%B3nio_Verney"}]}, {"year": "1801", "text": "<PERSON>, Russian emperor (b. 1754)", "html": "1801 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Russia\" title=\"<PERSON> of Russia\"><PERSON></a>, Russian emperor (b. 1754)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Russia\" title=\"<PERSON> of Russia\"><PERSON></a>, Russian emperor (b. 1754)", "links": [{"title": "<PERSON> of Russia", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Russia"}]}, {"year": "1842", "text": "<PERSON><PERSON><PERSON>, French novelist (b. 1783)", "html": "1842 - <a href=\"https://wikipedia.org/wiki/Stendhal\" title=\"Stendhal\"><PERSON><PERSON><PERSON></a>, French novelist (b. 1783)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Stendhal\" title=\"Stendhal\"><PERSON><PERSON><PERSON></a>, French novelist (b. 1783)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Stendhal"}]}, {"year": "1862", "text": "<PERSON>, Unconstitutional Mexican interim president, 1858-1859 (b. 1817)", "html": "1862 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_Pezuela\" title=\"<PERSON> Pezuela\"><PERSON></a>, Unconstitutional Mexican interim president, 1858-1859 (b. 1817)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_Pezuela\" title=\"<PERSON> Pezuela\"><PERSON></a>, Unconstitutional Mexican interim president, 1858-1859 (b. 1817)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>ezuel<PERSON>"}]}, {"year": "1883", "text": "<PERSON>, Scottish-Australian politician, 2nd Premier of Queensland (b. 1818)", "html": "1883 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish-Australian politician, 2nd <a href=\"https://wikipedia.org/wiki/Premier_of_Queensland\" title=\"Premier of Queensland\">Premier of Queensland</a> (b. 1818)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish-Australian politician, 2nd <a href=\"https://wikipedia.org/wiki/Premier_of_Queensland\" title=\"Premier of Queensland\">Premier of Queensland</a> (b. 1818)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Arthur_<PERSON>"}, {"title": "Premier of Queensland", "link": "https://wikipedia.org/wiki/Premier_of_Queensland"}]}, {"year": "1884", "text": "<PERSON>, American businessman (b. 1824)", "html": "1884 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American businessman (b. 1824)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American businessman (b. 1824)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1910", "text": "<PERSON><PERSON>, French photographer, journalist, and author (b. 1820)", "html": "1910 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_(photographer)\" class=\"mw-redirect\" title=\"<PERSON><PERSON> (photographer)\"><PERSON><PERSON></a>, French photographer, journalist, and author (b. 1820)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_(photographer)\" class=\"mw-redirect\" title=\"<PERSON><PERSON> (photographer)\"><PERSON><PERSON></a>, French photographer, journalist, and author (b. 1820)", "links": [{"title": "<PERSON><PERSON> (photographer)", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_(photographer)"}]}, {"year": "1914", "text": "<PERSON><PERSON><PERSON>, Lebanese saint (b. 1832)", "html": "1914 - <a href=\"https://wikipedia.org/wiki/Raf<PERSON>_Pietra_Choboq_Ar-Ray%C3%A8s\" title=\"Rafqa Pietra Choboq Ar<PERSON>\"><PERSON><PERSON><PERSON></a>, Lebanese saint (b. 1832)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Ra<PERSON><PERSON>_Pietra_Choboq_Ar-Ray%C3%A8s\" title=\"Rafqa Pietra Choboq Ar-<PERSON>\"><PERSON><PERSON><PERSON></a>, Lebanese saint (b. 1832)", "links": [{"title": "Rafqa Pietra Choboq Ar-Rayès", "link": "https://wikipedia.org/wiki/Raf<PERSON>_Pietra_Choboq_Ar-Ray%C3%A8s"}]}, {"year": "1923", "text": "<PERSON><PERSON><PERSON><PERSON>, Armenian poet and author (b. 1869)", "html": "1923 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Armenian poet and author (b. 1869)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Armenian poet and author (b. 1869)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1927", "text": "<PERSON>, French painter and etcher (b. 1859)", "html": "1927 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A9<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French painter and etcher (b. 1859)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A9<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French painter and etcher (b. 1859)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Paul_C%C3%A9<PERSON>_<PERSON>eu"}]}, {"year": "1931", "text": "<PERSON><PERSON>, Indian activist (b. 1908)", "html": "1931 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>ru\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Indian activist (b. 1908)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>ru\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Indian activist (b. 1908)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1931", "text": "<PERSON><PERSON><PERSON>, Indian activist (b. 1907)", "html": "1931 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Indian activist (b. 1907)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Indian activist (b. 1907)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1931", "text": "<PERSON><PERSON><PERSON><PERSON>, Indian activist (b. 1907)", "html": "1931 - <a href=\"https://wikipedia.org/wiki/Sukhdev_Thapar\" title=\"Sukhdev Thapar\"><PERSON><PERSON><PERSON><PERSON> Thapar</a>, Indian activist (b. 1907)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Sukhdev_Thapar\" title=\"Sukhdev Thapar\"><PERSON><PERSON><PERSON><PERSON> Thapar</a>, Indian activist (b. 1907)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON> T<PERSON>par", "link": "https://wikipedia.org/wiki/Sukhdev_Thapar"}]}, {"year": "1946", "text": "<PERSON>, American chemist (b. 1875)", "html": "1946 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American chemist (b. 1875)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American chemist (b. 1875)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1953", "text": "<PERSON>, French painter and illustrator (b. 1877)", "html": "1953 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French painter and illustrator (b. 1877)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French painter and illustrator (b. 1877)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1953", "text": "<PERSON><PERSON>, Estonian author and playwright (b. 1887)", "html": "1953 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Estonian author and playwright (b. 1887)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Estonian author and playwright (b. 1887)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1955", "text": "<PERSON><PERSON>, Brazilian politician, 12th President of Brazil (b. 1875)", "html": "1955 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Brazilian politician, 12th <a href=\"https://wikipedia.org/wiki/President_of_Brazil\" title=\"President of Brazil\">President of Brazil</a> (b. 1875)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Brazilian politician, 12th <a href=\"https://wikipedia.org/wiki/President_of_Brazil\" title=\"President of Brazil\">President of Brazil</a> (b. 1875)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>ur_<PERSON>"}, {"title": "President of Brazil", "link": "https://wikipedia.org/wiki/President_of_Brazil"}]}, {"year": "1960", "text": "<PERSON>, American journalist and author (b. 1881)", "html": "1960 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, American journalist and author (b. 1881)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, American journalist and author (b. 1881)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1960", "text": "<PERSON>, Turkish theologian and scholar (b. 1878)", "html": "1960 - <a href=\"https://wikipedia.org/wiki/Said_Nurs%C3%AE\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, Turkish theologian and scholar (b. 1878)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Said_Nurs%C3%AE\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, Turkish theologian and scholar (b. 1878)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Said_Nurs%C3%AE"}]}, {"year": "1961", "text": "<PERSON>, American painter and educator (b. 1882)", "html": "1961 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American painter and educator (b. 1882)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American painter and educator (b. 1882)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1961", "text": "<PERSON>, English cricketer (b. 1887)", "html": "1961 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(cricketer,_born_1887)\" title=\"<PERSON> (cricketer, born 1887)\"><PERSON></a>, English cricketer (b. 1887)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(cricketer,_born_1887)\" title=\"<PERSON> (cricketer, born 1887)\"><PERSON></a>, English cricketer (b. 1887)", "links": [{"title": "<PERSON> (cricketer, born 1887)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(cricketer,_born_1887)"}]}, {"year": "1963", "text": "<PERSON><PERSON><PERSON>, Norwegian mathematician and logician (b. 1887)", "html": "1963 - <a href=\"https://wikipedia.org/wiki/Thor<PERSON>f_Skolem\" title=\"Thor<PERSON>f Skolem\"><PERSON><PERSON><PERSON></a>, Norwegian mathematician and logician (b. 1887)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Thor<PERSON><PERSON>_S<PERSON>lem\" title=\"Thor<PERSON>f Skolem\"><PERSON><PERSON><PERSON></a>, Norwegian mathematician and logician (b. 1887)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Thoralf_Skolem"}]}, {"year": "1964", "text": "<PERSON>, American actor (b. 1904)", "html": "1964 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor (b. 1904)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor (b. 1904)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1965", "text": "<PERSON>, American actress, dancer, producer, and screenwriter (b. 1885)", "html": "1965 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress, dancer, producer, and screenwriter (b. 1885)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress, dancer, producer, and screenwriter (b. 1885)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1967", "text": "<PERSON><PERSON>, Norwegian singer and actress (b. 1889)", "html": "1967 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Norwegian singer and actress (b. 1889)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Norwegian singer and actress (b. 1889)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1968", "text": "<PERSON>, American journalist and author (b. 1918)", "html": "1968 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%27Connor\" title=\"<PERSON>\"><PERSON></a>, American journalist and author (b. 1918)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%27Connor\" title=\"<PERSON>\"><PERSON></a>, American journalist and author (b. 1918)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Edwin_O%27Connor"}]}, {"year": "1972", "text": "<PERSON><PERSON><PERSON><PERSON>, Spanish fashion designer, founded <PERSON><PERSON><PERSON><PERSON> (b. 1895)", "html": "1972 - <a href=\"https://wikipedia.org/wiki/Crist%C3%B3bal_Balenciaga\" title=\"Cristóbal <PERSON>len<PERSON>ga\"><PERSON><PERSON><PERSON><PERSON></a>, Spanish fashion designer, founded <a href=\"https://wikipedia.org/wiki/Ba<PERSON>ciaga\" title=\"Balenciaga\"><PERSON><PERSON><PERSON><PERSON></a> (b. 1895)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Crist%C3%B3bal_Balenciaga\" title=\"Cristóbal <PERSON>len<PERSON>ga\"><PERSON><PERSON><PERSON><PERSON></a>, Spanish fashion designer, founded <a href=\"https://wikipedia.org/wiki/Balenciaga\" title=\"Balenciaga\"><PERSON><PERSON><PERSON><PERSON></a> (b. 1895)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Crist%C3%B3bal_<PERSON><PERSON>ga"}, {"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Balenciaga"}]}, {"year": "1978", "text": "<PERSON><PERSON>, Israeli biochemist and academic (b. 1893)", "html": "1978 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Israeli biochemist and academic (b. 1893)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Israeli biochemist and academic (b. 1893)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1978", "text": "<PERSON><PERSON>, Ukrainian teacher and anarchist revolutionary (b. 1897)", "html": "1978 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Ukrainian teacher and anarchist revolutionary (b. 1897)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Ukrainian teacher and anarchist revolutionary (b. 1897)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>zmenko"}]}, {"year": "1979", "text": "<PERSON>, English footballer (b. 1911)", "html": "1979 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(footballer)\" title=\"<PERSON> (footballer)\"><PERSON></a>, English footballer (b. 1911)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>(footballer)\" title=\"<PERSON> (footballer)\"><PERSON></a>, English footballer (b. 1911)", "links": [{"title": "<PERSON> (footballer)", "link": "https://wikipedia.org/wiki/<PERSON>(footballer)"}]}, {"year": "1980", "text": "<PERSON>, American economist and academic (b. 1928)", "html": "1980 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American economist and academic (b. 1928)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American economist and academic (b. 1928)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1981", "text": "<PERSON>, English-New Zealand astronomer and cosmologist (b. 1941)", "html": "1981 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English-New Zealand astronomer and cosmologist (b. 1941)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English-New Zealand astronomer and cosmologist (b. 1941)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1981", "text": "<PERSON>, English motorcyclist (b. 1940)", "html": "1981 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English motorcyclist (b. 1940)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English motorcyclist (b. 1940)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1985", "text": "<PERSON>, <PERSON>, English physicist and engineer (b. 1913)", "html": "1985 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Baron_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>, <PERSON>\"><PERSON>, Baron <PERSON></a>, English physicist and engineer (b. 1913)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Baron_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>, <PERSON>\"><PERSON>, Baron <PERSON></a>, English physicist and engineer (b. 1913)", "links": [{"title": "<PERSON>, Baron <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_<PERSON>_<PERSON>"}]}, {"year": "1985", "text": "<PERSON>, Greek-American scholar and educator (b. 1908)", "html": "1985 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Greek-American scholar and educator (b. 1908)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Greek-American scholar and educator (b. 1908)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1986", "text": "<PERSON><PERSON>, American Orthodox Rabbi and posek (b. 1895)", "html": "1986 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American Orthodox Rabbi and <a href=\"https://wikipedia.org/wiki/Posek\" title=\"Pose<PERSON>\">posek</a> (b. 1895)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American Orthodox Rabbi and <a href=\"https://wikipedia.org/wiki/Posek\" title=\"Posek\">posek</a> (b. 1895)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}, {"title": "Posek", "link": "https://wikipedia.org/wiki/Posek"}]}, {"year": "1987", "text": "<PERSON><PERSON>, Estonian singer and violinist (b. 1901)", "html": "1987 - <a href=\"https://wikipedia.org/wiki/Olev_Roomet\" title=\"Olev Roomet\"><PERSON><PERSON></a>, Estonian singer and violinist (b. 1901)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Ole<PERSON>_Roomet\" title=\"Olev Roomet\"><PERSON><PERSON></a>, Estonian singer and violinist (b. 1901)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Olev_Roomet"}]}, {"year": "1990", "text": "<PERSON>, English director and producer (b. 1925)", "html": "1990 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English director and producer (b. 1925)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English director and producer (b. 1925)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1991", "text": "<PERSON>, American historian and author (b. 1899)", "html": "1991 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American historian and author (b. 1899)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American historian and author (b. 1899)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1991", "text": "<PERSON><PERSON>, Indian soldier, Victoria Cross recipient (b. 1913)", "html": "1991 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Indian soldier, <a href=\"https://wikipedia.org/wiki/Victoria_Cross\" title=\"Victoria Cross\">Victoria Cross</a> recipient (b. 1913)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Indian soldier, <a href=\"https://wikipedia.org/wiki/Victoria_Cross\" title=\"Victoria Cross\">Victoria Cross</a> recipient (b. 1913)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}, {"title": "Victoria Cross", "link": "https://wikipedia.org/wiki/Victoria_Cross"}]}, {"year": "1992", "text": "<PERSON>, Austrian-German economist, philosopher, and academic, Nobel Prize laureate (b. 1899)", "html": "1992 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Austrian-German economist, philosopher, and academic, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Economics\" class=\"mw-redirect\" title=\"Nobel Prize in Economics\">Nobel Prize</a> laureate (b. 1899)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Austrian-German economist, philosopher, and academic, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Economics\" class=\"mw-redirect\" title=\"Nobel Prize in Economics\">Nobel Prize</a> laureate (b. 1899)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Nobel Prize in Economics", "link": "https://wikipedia.org/wiki/Nobel_Prize_in_Economics"}]}, {"year": "1992", "text": "<PERSON>, Canadian ice hockey player and coach (b. 1949)", "html": "1992 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player and coach (b. 1949)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player and coach (b. 1949)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1994", "text": "<PERSON>, Mexican economist and politician (b. 1950)", "html": "1994 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, Mexican economist and politician (b. 1950)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, Mexican economist and politician (b. 1950)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1994", "text": "<PERSON><PERSON><PERSON><PERSON>, Italian actress (b. 1921)", "html": "1994 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_Ma<PERSON>\" title=\"<PERSON><PERSON>lie<PERSON> Masina\"><PERSON><PERSON><PERSON><PERSON></a>, Italian actress (b. 1921)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON> Ma<PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Italian actress (b. 1921)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Giulie<PERSON>_Masina"}]}, {"year": "1995", "text": "<PERSON><PERSON>, Scottish footballer and coach (b. 1956)", "html": "1995 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Scottish footballer and coach (b. 1956)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Scottish footballer and coach (b. 1956)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1999", "text": "<PERSON>, Paraguayan judge and politician, Vice President of Paraguay (b. 1932)", "html": "1999 - <a href=\"https://wikipedia.org/wiki/Luis_<PERSON>%C3%ADa_Arga%C3%B1a\" title=\"<PERSON>\"><PERSON></a>, Paraguayan judge and politician, <a href=\"https://wikipedia.org/wiki/Vice_President_of_Paraguay\" title=\"Vice President of Paraguay\">Vice President of Paraguay</a> (b. 1932)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Luis_<PERSON>%C3%ADa_Arga%C3%B1a\" title=\"<PERSON>\"><PERSON></a>, Paraguayan judge and politician, <a href=\"https://wikipedia.org/wiki/Vice_President_of_Paraguay\" title=\"Vice President of Paraguay\">Vice President of Paraguay</a> (b. 1932)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Luis_Mar%C3%ADa_Arga%C3%B1a"}, {"title": "Vice President of Paraguay", "link": "https://wikipedia.org/wiki/Vice_President_of_Paraguay"}]}, {"year": "1999", "text": "<PERSON><PERSON><PERSON>, Canadian director and cinematographer (b. 1898)", "html": "1999 - <a href=\"https://wikipedia.org/wiki/Osmond_Borradaile\" title=\"Osmond Borradaile\">O<PERSON><PERSON></a>, Canadian director and cinematographer (b. 1898)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>smo<PERSON>_Bo<PERSON>dai<PERSON>\" title=\"Osmond Borradaile\"><PERSON><PERSON><PERSON></a>, Canadian director and cinematographer (b. 1898)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Osmo<PERSON>_<PERSON>daile"}]}, {"year": "2001", "text": "<PERSON>, American journalist (b. 1921)", "html": "2001 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American journalist (b. 1921)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American journalist (b. 1921)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2001", "text": "<PERSON>, British archaeologist (b. 1916)", "html": "2001 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, British <a href=\"https://wikipedia.org/wiki/Archaeology\" title=\"Archaeology\">archaeologist</a> (b. 1916)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, British <a href=\"https://wikipedia.org/wiki/Archaeology\" title=\"Archaeology\">archaeologist</a> (b. 1916)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "Archaeology", "link": "https://wikipedia.org/wiki/Archaeology"}]}, {"year": "2001", "text": "<PERSON>, American author (b. 1923)", "html": "2001 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author (b. 1923)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author (b. 1923)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2001", "text": "<PERSON>, Canadian badminton player and environmentalist (b. 1932)", "html": "2001 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian badminton player and environmentalist (b. 1932)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian badminton player and environmentalist (b. 1932)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2002", "text": "<PERSON>, American soprano (b. 1920)", "html": "2002 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American soprano (b. 1920)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American soprano (b. 1920)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2002", "text": "<PERSON>, Australian-English cricketer (b. 1977)", "html": "2002 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian-English cricketer (b. 1977)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian-English cricketer (b. 1977)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2003", "text": "<PERSON>, Austrian-English flute player and journalist (b. 1926)", "html": "2003 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Austrian-English flute player and journalist (b. 1926)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Austrian-English flute player and journalist (b. 1926)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>l"}]}, {"year": "2004", "text": "<PERSON>, Australian soldier, lawyer, and politician, 39th Premier of Victoria (b. 1916)", "html": "2004 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian soldier, lawyer, and politician, 39th <a href=\"https://wikipedia.org/wiki/Premier_of_Victoria\" title=\"Premier of Victoria\">Premier of Victoria</a> (b. 1916)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian soldier, lawyer, and politician, 39th <a href=\"https://wikipedia.org/wiki/Premier_of_Victoria\" title=\"Premier of Victoria\">Premier of Victoria</a> (b. 1916)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}, {"title": "Premier of Victoria", "link": "https://wikipedia.org/wiki/Premier_of_Victoria"}]}, {"year": "2006", "text": "<PERSON>, American sergeant, Medal of Honor recipient (b. 1932)", "html": "2006 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American sergeant, <a href=\"https://wikipedia.org/wiki/Medal_of_Honor\" title=\"Medal of Honor\">Medal of Honor</a> recipient (b. 1932)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American sergeant, <a href=\"https://wikipedia.org/wiki/Medal_of_Honor\" title=\"Medal of Honor\">Medal of Honor</a> recipient (b. 1932)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>"}, {"title": "Medal of Honor", "link": "https://wikipedia.org/wiki/Medal_of_Honor"}]}, {"year": "2006", "text": "<PERSON>, American soldier, Medal of Honor recipient (b. 1919)", "html": "2006 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American soldier, <a href=\"https://wikipedia.org/wiki/Medal_of_Honor\" title=\"Medal of Honor\">Medal of Honor</a> recipient (b. 1919)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American soldier, <a href=\"https://wikipedia.org/wiki/Medal_of_Honor\" title=\"Medal of Honor\">Medal of Honor</a> recipient (b. 1919)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Medal of Honor", "link": "https://wikipedia.org/wiki/Medal_of_Honor"}]}, {"year": "2006", "text": "<PERSON>, American singer-songwriter and dancer (b. 1918)", "html": "2006 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and dancer (b. 1918)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and dancer (b. 1918)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2007", "text": "<PERSON>, American mathematician and theorist (b. 1934)", "html": "2007 - <a href=\"https://wikipedia.org/wiki/<PERSON>(mathematician)\" class=\"mw-redirect\" title=\"<PERSON> (mathematician)\"><PERSON></a>, American mathematician and theorist (b. 1934)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>(mathematician)\" class=\"mw-redirect\" title=\"<PERSON> (mathematician)\"><PERSON></a>, American mathematician and theorist (b. 1934)", "links": [{"title": "<PERSON> (mathematician)", "link": "https://wikipedia.org/wiki/<PERSON>(mathematician)"}]}, {"year": "2007", "text": "<PERSON>, American race car driver (b. 1973)", "html": "2007 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American race car driver (b. 1973)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American race car driver (b. 1973)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2008", "text": "<PERSON><PERSON><PERSON>, Estonian psychiatrist, author, and playwright (b. 1940)", "html": "2008 - <a href=\"https://wikipedia.org/wiki/<PERSON>ain<PERSON>_<PERSON>\" title=\"<PERSON>ain<PERSON>\"><PERSON><PERSON><PERSON></a>, Estonian psychiatrist, author, and playwright (b. 1940)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>ain<PERSON>_<PERSON>\" title=\"<PERSON>ain<PERSON>\"><PERSON><PERSON><PERSON></a>, Estonian psychiatrist, author, and playwright (b. 1940)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Vaino_<PERSON>ahing"}]}, {"year": "2009", "text": "<PERSON><PERSON><PERSON>, Armenian sculptor (b. 1923)", "html": "2009 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Armenian sculptor (b. 1923)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Armenian sculptor (b. 1923)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "2009", "text": "<PERSON><PERSON>, Mexican boxer and trainer (b. 1934)", "html": "2009 - <a href=\"https://wikipedia.org/wiki/Ra%C3%BAl_Mac%C3%ADas\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Mexican boxer and trainer (b. 1934)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Ra%C3%BAl_Mac%C3%ADas\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Mexican boxer and trainer (b. 1934)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Ra%C3%BAl_Mac%C3%ADas"}]}, {"year": "2011", "text": "<PERSON>, American computer scientist and engineer (b. 1924)", "html": "2011 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American computer scientist and engineer (b. 1924)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American computer scientist and engineer (b. 1924)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2011", "text": "<PERSON>, Puerto Rican poet and writer (b. 1930)", "html": "2011 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Puerto Rican poet and writer (b. 1930)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Morales\"><PERSON></a>, Puerto Rican poet and writer (b. 1930)", "links": [{"title": "Rosario Morales", "link": "https://wikipedia.org/wiki/<PERSON>_Morales"}]}, {"year": "2011", "text": "<PERSON>, American-British actress, socialite and humanitarian (b. 1932)", "html": "2011 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American-British actress, socialite and humanitarian (b. 1932)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American-British actress, socialite and humanitarian (b. 1932)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2012", "text": "<PERSON><PERSON>, Somalian politician, President of Somalia (b. 1934)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Somalian politician, <a href=\"https://wikipedia.org/wiki/President_of_Somalia\" title=\"President of Somalia\">President of Somalia</a> (b. 1934)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Somalian politician, <a href=\"https://wikipedia.org/wiki/President_of_Somalia\" title=\"President of Somalia\">President of Somalia</a> (b. 1934)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>"}, {"title": "President of Somalia", "link": "https://wikipedia.org/wiki/President_of_Somalia"}]}, {"year": "2012", "text": "<PERSON>, American animator, director, and producer (b. 1937)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(animator)\" title=\"<PERSON> (animator)\"><PERSON></a>, American animator, director, and producer (b. 1937)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(animator)\" title=\"<PERSON> (animator)\"><PERSON></a>, American animator, director, and producer (b. 1937)", "links": [{"title": "<PERSON> (animator)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(animator)"}]}, {"year": "2012", "text": "<PERSON><PERSON>, Iraqi politician, 52nd Prime Minister of Iraq (b. 1917)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Iraqi politician, 52nd <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Iraq\" title=\"Prime Minister of Iraq\">Prime Minister of Iraq</a> (b. 1917)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Iraqi politician, 52nd <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Iraq\" title=\"Prime Minister of Iraq\">Prime Minister of Iraq</a> (b. 1917)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>b"}, {"title": "Prime Minister of Iraq", "link": "https://wikipedia.org/wiki/Prime_Minister_of_Iraq"}]}, {"year": "2012", "text": "<PERSON><PERSON><PERSON>, American basketball and football player (b. 1945)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American basketball and football player (b. 1945)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American basketball and football player (b. 1945)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "2013", "text": "<PERSON>, Russian-born Soviet-British mathematician and businessman (b. 1946)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/<PERSON>_(businessman)\" title=\"<PERSON> (businessman)\"><PERSON></a>, Russian-born Soviet-British mathematician and businessman (b. 1946)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(businessman)\" title=\"<PERSON> (businessman)\"><PERSON></a>, Russian-born Soviet-British mathematician and businessman (b. 1946)", "links": [{"title": "<PERSON> (businessman)", "link": "https://wikipedia.org/wiki/<PERSON>_(businessman)"}]}, {"year": "2013", "text": "<PERSON><PERSON><PERSON>, Filipino economist, historian, and academic (b. 1926)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/Onofre_Corpuz\" title=\"Onofre Corpuz\">Onof<PERSON> Corpuz</a>, Filipino economist, historian, and academic (b. 1926)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Onofre_Corpuz\" title=\"Onofre Corpuz\">Onof<PERSON>uz</a>, Filipino economist, historian, and academic (b. 1926)", "links": [{"title": "Onofre Corpuz", "link": "https://wikipedia.org/wiki/Onofre_Corpuz"}]}, {"year": "2013", "text": "<PERSON>, American baseball player and coach (b. 1917)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Virgil Trucks\"><PERSON></a>, American baseball player and coach (b. 1917)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Virgil Trucks\"><PERSON></a>, American baseball player and coach (b. 1917)", "links": [{"title": "Virgil Trucks", "link": "https://wikipedia.org/wiki/Virgil_Trucks"}]}, {"year": "2013", "text": "<PERSON>, Canadian-American bodybuilder and publisher, co-founded the International Federation of BodyBuilding & Fitness (b. 1919)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian-American bodybuilder and publisher, co-founded the <a href=\"https://wikipedia.org/wiki/International_Federation_of_BodyBuilding_%26_Fitness\" class=\"mw-redirect\" title=\"International Federation of BodyBuilding &amp; Fitness\">International Federation of BodyBuilding &amp; Fitness</a> (b. 1919)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian-American bodybuilder and publisher, co-founded the <a href=\"https://wikipedia.org/wiki/International_Federation_of_BodyBuilding_%26_Fitness\" class=\"mw-redirect\" title=\"International Federation of BodyBuilding &amp; Fitness\">International Federation of BodyBuilding &amp; Fitness</a> (b. 1919)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "International Federation of BodyBuilding & Fitness", "link": "https://wikipedia.org/wiki/International_Federation_of_BodyBuilding_%26_Fitness"}]}, {"year": "2014", "text": "<PERSON>, Canadian-American singer-songwriter and bass player (b. 1963)", "html": "2014 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian-American singer-songwriter and bass player (b. 1963)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian-American singer-songwriter and bass player (b. 1963)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2014", "text": "<PERSON><PERSON><PERSON>, Czech painter and illustrator (b. 1928)", "html": "2014 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_%C5%A0er%C3%BDch\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Czech painter and illustrator (b. 1928)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_%C5%A0er%C3%BDch\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Czech painter and illustrator (b. 1928)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_%C5%A0er%C3%BDch"}]}, {"year": "2014", "text": "<PERSON><PERSON>, Spanish lawyer and politician, 1st Prime Minister of Spain (b. 1932)", "html": "2014 - <a href=\"https://wikipedia.org/wiki/Adolfo_Su%C3%A1rez\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Spanish lawyer and politician, 1st <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Spain\" title=\"Prime Minister of Spain\">Prime Minister of Spain</a> (b. 1932)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Adolfo_Su%C3%A1rez\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Spanish lawyer and politician, 1st <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Spain\" title=\"Prime Minister of Spain\">Prime Minister of Spain</a> (b. 1932)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Adolfo_Su%C3%A1rez"}, {"title": "Prime Minister of Spain", "link": "https://wikipedia.org/wiki/Prime_Minister_of_Spain"}]}, {"year": "2015", "text": "<PERSON><PERSON>, Italian director, producer, and screenwriter (b. 1930)", "html": "2015 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Italian director, producer, and screenwriter (b. 1930)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Italian director, producer, and screenwriter (b. 1930)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "2015", "text": "<PERSON>, Singaporean lawyer and politician, 1st Prime Minister of Singapore (b. 1923)", "html": "2015 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Singaporean lawyer and politician, 1st <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Singapore\" title=\"Prime Minister of Singapore\">Prime Minister of Singapore</a> (b. 1923)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Singaporean lawyer and politician, 1st <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Singapore\" title=\"Prime Minister of Singapore\">Prime Minister of Singapore</a> (b. 1923)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>w"}, {"title": "Prime Minister of Singapore", "link": "https://wikipedia.org/wiki/Prime_Minister_of_Singapore"}]}, {"year": "2015", "text": "<PERSON>, American basketball player and lieutenant (b. 1923)", "html": "2015 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player and lieutenant (b. 1923)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player and lieutenant (b. 1923)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "2015", "text": "<PERSON><PERSON> <PERSON>, English singer-songwriter, actor, and television personality (b. 1990)", "html": "2015 - <a href=\"https://wikipedia.org/wiki/<PERSON>%27_<PERSON>\" title=\"<PERSON><PERSON> <PERSON>\"><PERSON><PERSON> <PERSON></a>, English singer-songwriter, actor, and television personality (b. 1990)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>%27_<PERSON>\" title=\"<PERSON><PERSON> <PERSON>\"><PERSON><PERSON> <PERSON></a>, English singer-songwriter, actor, and television personality (b. 1990)", "links": [{"title": "<PERSON>' <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>%27_<PERSON>"}]}, {"year": "2016", "text": "<PERSON>, American baseball player and sportscaster (b. 1926)", "html": "2016 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player and sportscaster (b. 1926)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player and sportscaster (b. 1926)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2016", "text": "<PERSON>, American actor (b. 1944)", "html": "2016 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor (b. 1944)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor (b. 1944)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2017", "text": "<PERSON><PERSON><PERSON>, Mexican investigative journalist (b. 1962)", "html": "2017 - <a href=\"https://wikipedia.org/wiki/Miros<PERSON>_Breach\" title=\"Mir<PERSON><PERSON> Breach\"><PERSON><PERSON><PERSON></a>, Mexican investigative journalist (b. 1962)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Miros<PERSON>_Breach\" title=\"Miroslava Breach\"><PERSON><PERSON><PERSON></a>, Mexican investigative journalist (b. 1962)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Miros<PERSON>_Breach"}]}, {"year": "2021", "text": "<PERSON>, American actor (b. 1934)", "html": "2021 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor (b. 1934)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor (b. 1934)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2021", "text": "<PERSON>, French snowboarder (b. 1980)", "html": "2021 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French snowboarder (b. 1980)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French snowboarder (b. 1980)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2022", "text": "<PERSON>, Czechoslovakian-American diplomat, 64th United States Secretary of State (b. 1937)", "html": "2022 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Czechoslovakian-American diplomat, 64th <a href=\"https://wikipedia.org/wiki/United_States_Secretary_of_State\" title=\"United States Secretary of State\">United States Secretary of State</a> (b. 1937)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Czechoslovakian-American diplomat, 64th <a href=\"https://wikipedia.org/wiki/United_States_Secretary_of_State\" title=\"United States Secretary of State\">United States Secretary of State</a> (b. 1937)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "United States Secretary of State", "link": "https://wikipedia.org/wiki/United_States_Secretary_of_State"}]}]}}