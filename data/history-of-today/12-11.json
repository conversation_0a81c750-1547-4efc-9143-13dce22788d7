{"date": "December 11", "url": "https://wikipedia.org/wiki/December_11", "data": {"Events": [{"year": "220", "text": "Emperor <PERSON><PERSON> of Han is forced to abdicate the throne by <PERSON>'s son <PERSON>, ending the Han dynasty.", "html": "220 - <a href=\"https://wikipedia.org/wiki/Emperor_<PERSON><PERSON>_of_Han\" title=\"Emperor <PERSON><PERSON> of Han\">Emperor <PERSON><PERSON> of Han</a> is forced to abdicate the throne by <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>'s son <a href=\"https://wikipedia.org/wiki/Cao_Pi\" title=\"Cao Pi\"><PERSON></a>, <a href=\"https://wikipedia.org/wiki/End_of_the_Han_dynasty\" title=\"End of the Han dynasty\">ending the Han dynasty</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Emperor_<PERSON><PERSON>_of_Han\" title=\"Emperor <PERSON><PERSON> of Han\">Emperor <PERSON><PERSON> of Han</a> is forced to abdicate the throne by <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>'s son <a href=\"https://wikipedia.org/wiki/Cao_Pi\" title=\"Cao Pi\"><PERSON></a>, <a href=\"https://wikipedia.org/wiki/End_of_the_Han_dynasty\" title=\"End of the Han dynasty\">ending the Han dynasty</a>.", "links": [{"title": "Emperor <PERSON><PERSON> of Han", "link": "https://wikipedia.org/wiki/Emperor_<PERSON><PERSON>_of_Han"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Cao_<PERSON>"}, {"title": "End of the Han dynasty", "link": "https://wikipedia.org/wiki/End_of_the_Han_dynasty"}]}, {"year": "361", "text": "<PERSON> enters Constantinople as sole Roman Emperor.", "html": "361 - <a href=\"https://wikipedia.org/wiki/<PERSON>_(emperor)\" title=\"<PERSON> (emperor)\"><PERSON></a> enters <a href=\"https://wikipedia.org/wiki/Constantinople\" title=\"Constantinople\">Constantinople</a> as sole <a href=\"https://wikipedia.org/wiki/Roman_emperor\" title=\"Roman emperor\">Roman Emperor</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(emperor)\" title=\"<PERSON> (emperor)\"><PERSON></a> enters <a href=\"https://wikipedia.org/wiki/Constantinople\" title=\"Constantinople\">Constantinople</a> as sole <a href=\"https://wikipedia.org/wiki/Roman_emperor\" title=\"Roman emperor\">Roman Emperor</a>.", "links": [{"title": "<PERSON> (emperor)", "link": "https://wikipedia.org/wiki/<PERSON>_(emperor)"}, {"title": "Constantinople", "link": "https://wikipedia.org/wiki/Constantinople"}, {"title": "Roman emperor", "link": "https://wikipedia.org/wiki/Roman_emperor"}]}, {"year": "861", "text": "Assassination of the Abbasid caliph <PERSON><PERSON><PERSON><PERSON> by the Turkish guard, who raise <PERSON><PERSON><PERSON><PERSON><PERSON> to the throne, start of the \"Anarchy at Samarra\".", "html": "861 - Assassination of the <a href=\"https://wikipedia.org/wiki/Abbasid\" class=\"mw-redirect\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a> caliph <a href=\"https://wikipedia.org/wiki/Al-Mutawakkil\" title=\"Al-Mutawakkil\">al-<PERSON><PERSON>wak<PERSON>l</a> by the Turkish guard, who raise <a href=\"https://wikipedia.org/wiki/Al-Muntasir\" title=\"Al-Muntasir\">al-<PERSON><PERSON><PERSON></a> to the throne, start of the \"<a href=\"https://wikipedia.org/wiki/Anarchy_at_Samarra\" title=\"Anarchy at Samarra\">Anarchy at Samarra</a>\".", "no_year_html": "Assassination of the <a href=\"https://wikipedia.org/wiki/Abbasid\" class=\"mw-redirect\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a> caliph <a href=\"https://wikipedia.org/wiki/Al-Mutawakkil\" title=\"Al<PERSON>Mutawakkil\">al-Mu<PERSON>wakkil</a> by the Turkish guard, who raise <a href=\"https://wikipedia.org/wiki/Al-Muntasir\" title=\"Al-Muntasir\">al-<PERSON><PERSON><PERSON></a> to the throne, start of the \"<a href=\"https://wikipedia.org/wiki/Anarchy_at_Samarra\" title=\"Anarchy at Samarra\">Anarchy at Samarra</a>\".", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>id"}, {"title": "Al-Mutawakkil", "link": "https://wikipedia.org/wiki/Al-Mutawakkil"}, {"title": "Al-<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>-<PERSON>"}, {"title": "Anarchy at Samarra", "link": "https://wikipedia.org/wiki/Anarchy_at_Samarra"}]}, {"year": "969", "text": "Byzantine Emperor <PERSON><PERSON><PERSON><PERSON> is assassinated by his wife <PERSON><PERSON><PERSON> and her lover, the later Emperor <PERSON>.", "html": "969 - <a href=\"https://wikipedia.org/wiki/Byzantine_Emperor\" class=\"mw-redirect\" title=\"Byzantine Emperor\">Byzantine Emperor</a> <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_II_Phokas\" title=\"<PERSON><PERSON><PERSON><PERSON> II Phokas\"><PERSON><PERSON><PERSON><PERSON> II Phokas</a> is assassinated by his wife <a href=\"https://wikipedia.org/wiki/Theo<PERSON><PERSON>_(born_<PERSON><PERSON><PERSON>)\" title=\"<PERSON><PERSON><PERSON> (born <PERSON><PERSON><PERSON>)\"><PERSON><PERSON><PERSON></a> and her lover, the later Emperor <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Byzantine_Emperor\" class=\"mw-redirect\" title=\"Byzantine Emperor\">Byzantine Emperor</a> <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_II_Phokas\" title=\"<PERSON><PERSON><PERSON><PERSON> II Phokas\"><PERSON><PERSON><PERSON><PERSON> II Phokas</a> is assassinated by his wife <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_(born_<PERSON><PERSON><PERSON>)\" title=\"<PERSON><PERSON><PERSON> (born <PERSON><PERSON><PERSON>)\"><PERSON><PERSON><PERSON></a> and her lover, the later Emperor <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>.", "links": [{"title": "Byzantine Emperor", "link": "https://wikipedia.org/wiki/Byzantine_Emperor"}, {"title": "Nikephoros II Phokas", "link": "https://wikipedia.org/wiki/<PERSON>ph<PERSON>s_II_<PERSON>okas"}, {"title": "<PERSON><PERSON><PERSON> (born <PERSON><PERSON><PERSON>)", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_(born_<PERSON><PERSON><PERSON>)"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1041", "text": "<PERSON>, adoptive son of Empress <PERSON><PERSON><PERSON> of Byzantium, is proclaimed emperor of the Eastern Roman Empire.", "html": "1041 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, adoptive son of Empress <a href=\"https://wikipedia.org/wiki/Zo%C3%AB_Porphyrogenita\" class=\"mw-redirect\" title=\"<PERSON>o<PERSON> Porphyrogenita\"><PERSON><PERSON><PERSON> of Byzantium</a>, is proclaimed emperor of the <a href=\"https://wikipedia.org/wiki/Byzantine_Empire\" title=\"Byzantine Empire\">Eastern Roman Empire</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, adoptive son of Empress <a href=\"https://wikipedia.org/wiki/Zo%C3%AB_Porphyrogenita\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON> of Byzantium</a>, is proclaimed emperor of the <a href=\"https://wikipedia.org/wiki/Byzantine_Empire\" title=\"Byzantine Empire\">Eastern Roman Empire</a>.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "Z<PERSON>ë Porphyrogenita", "link": "https://wikipedia.org/wiki/Zo%C3%AB_Porphyrogenita"}, {"title": "Byzantine Empire", "link": "https://wikipedia.org/wiki/Byzantine_Empire"}]}, {"year": "1239", "text": "Treaty of Benavente: the heiresses of the Kingdom of León renounce their throne to King <PERSON> III of Castile", "html": "1239 - <a href=\"https://wikipedia.org/wiki/Treaty_of_Benavente\" title=\"Treaty of Benavente\">Treaty of Benavente</a>: the heiresses of the <a href=\"https://wikipedia.org/wiki/Kingdom_of_Le%C3%B3n\" title=\"Kingdom of León\">Kingdom of León</a> renounce their throne to King <a href=\"https://wikipedia.org/wiki/<PERSON>_III_of_Castile\" title=\"<PERSON> III of Castile\"><PERSON> of Castile</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Treaty_of_Benavente\" title=\"Treaty of Benavente\">Treaty of Benavente</a>: the heiresses of the <a href=\"https://wikipedia.org/wiki/Kingdom_of_Le%C3%B3n\" title=\"Kingdom of León\">Kingdom of León</a> renounce their throne to King <a href=\"https://wikipedia.org/wiki/<PERSON>_III_of_Castile\" title=\"<PERSON> III of Castile\"><PERSON> of Castile</a>", "links": [{"title": "Treaty of Benavente", "link": "https://wikipedia.org/wiki/Treaty_of_Benavente"}, {"title": "Kingdom of León", "link": "https://wikipedia.org/wiki/Kingdom_of_Le%C3%B3n"}, {"title": "<PERSON> of Castile", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Castile"}]}, {"year": "1282", "text": "Battle of Orewin Bridge: <PERSON><PERSON><PERSON><PERSON> <PERSON>, the last native Prince of Wales, is killed at Cilmeri near Builth Wells in mid-Wales.", "html": "1282 - <a href=\"https://wikipedia.org/wiki/Battle_of_Orewin_Bridge\" title=\"Battle of Orewin Bridge\">Battle of Orewin Bridge</a>: <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_ap_<PERSON><PERSON>udd\" title=\"<PERSON><PERSON><PERSON><PERSON> ap Gruffudd\"><PERSON><PERSON><PERSON><PERSON> ap <PERSON></a>, the last native <a href=\"https://wikipedia.org/wiki/Prince_of_Wales\" title=\"Prince of Wales\">Prince of Wales</a>, is killed at <a href=\"https://wikipedia.org/wiki/Cilmeri\" title=\"Cilmeri\">Cilmeri</a> near <a href=\"https://wikipedia.org/wiki/Builth_Wells\" title=\"Builth Wells\">Builth Wells</a> in <a href=\"https://wikipedia.org/wiki/Mid_Wales\" title=\"Mid Wales\">mid-Wales</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Battle_of_Orewin_Bridge\" title=\"Battle of Orewin Bridge\">Battle of Orewin Bridge</a>: <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_ap_G<PERSON>udd\" title=\"<PERSON><PERSON><PERSON><PERSON> ap Gruffudd\"><PERSON><PERSON><PERSON><PERSON> ap <PERSON></a>, the last native <a href=\"https://wikipedia.org/wiki/Prince_of_Wales\" title=\"Prince of Wales\">Prince of Wales</a>, is killed at <a href=\"https://wikipedia.org/wiki/Cilmeri\" title=\"Cilmeri\">Cilmeri</a> near <a href=\"https://wikipedia.org/wiki/Builth_Wells\" title=\"Builth Wells\">Builth Wells</a> in <a href=\"https://wikipedia.org/wiki/Mid_Wales\" title=\"Mid Wales\">mid-Wales</a>.", "links": [{"title": "Battle of Orewin Bridge", "link": "https://wikipedia.org/wiki/Battle_of_Or<PERSON>in_Bridge"}, {"title": "<PERSON><PERSON><PERSON><PERSON> ap <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_a<PERSON>_<PERSON><PERSON><PERSON>"}, {"title": "Prince of Wales", "link": "https://wikipedia.org/wiki/Prince_of_Wales"}, {"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Cilmeri"}, {"title": "Built<PERSON>", "link": "https://wikipedia.org/wiki/Built<PERSON>_Wells"}, {"title": "Mid Wales", "link": "https://wikipedia.org/wiki/Mid_Wales"}]}, {"year": "1602", "text": "A surprise attack by forces under the command of <PERSON>, Duke of Savoy, and his brother-in-law, <PERSON> of Spain, is repelled by the citizens of Geneva. (Commemorated annually by the Fête de l'Escalade.)", "html": "1602 - A surprise attack by forces under the command of <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>,_Duke_of_Savoy\" title=\"<PERSON>, Duke of Savoy\"><PERSON>, Duke of Savoy</a>, and his brother-in-law, <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Spain\" title=\"<PERSON> of Spain\"><PERSON> of Spain</a>, is repelled by the citizens of <a href=\"https://wikipedia.org/wiki/Geneva\" title=\"Geneva\">Geneva</a>. (Commemorated annually by the <i><a href=\"https://wikipedia.org/wiki/L%27Escalade\" title=\"L'Escalade\">Fête de l'Escalade</a></i>.)", "no_year_html": "A surprise attack by forces under the command of <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>,_Duke_of_Savoy\" title=\"<PERSON>, Duke of Savoy\"><PERSON>, Duke of Savoy</a>, and his brother-in-law, <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Spain\" title=\"<PERSON> of Spain\"><PERSON> of Spain</a>, is repelled by the citizens of <a href=\"https://wikipedia.org/wiki/Geneva\" title=\"Geneva\">Geneva</a>. (Commemorated annually by the <i><a href=\"https://wikipedia.org/wiki/L%27Escalade\" title=\"L'Escalade\">Fête de l'Escalade</a></i>.)", "links": [{"title": "<PERSON>, Duke of Savoy", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>,_Duke_of_Savoy"}, {"title": "<PERSON> of Spain", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Spain"}, {"title": "Geneva", "link": "https://wikipedia.org/wiki/Geneva"}, {"title": "L'Escalade", "link": "https://wikipedia.org/wiki/L%27Escalade"}]}, {"year": "1640", "text": "The Root and Branch petition, signed by 15,000 Londoners calling for the abolition of the episcopacy, is presented to the Long Parliament.", "html": "1640 - The <a href=\"https://wikipedia.org/wiki/<PERSON>_and_<PERSON>_petition\" title=\"<PERSON> and Branch petition\">Root and Branch petition</a>, signed by 15,000 Londoners calling for the abolition of the <a href=\"https://wikipedia.org/wiki/Episcopal_polity\" title=\"Episcopal polity\">episcopacy</a>, is presented to the <a href=\"https://wikipedia.org/wiki/Long_Parliament\" title=\"Long Parliament\">Long Parliament</a>.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/<PERSON>_and_Branch_petition\" title=\"Root and Branch petition\">Root and Branch petition</a>, signed by 15,000 Londoners calling for the abolition of the <a href=\"https://wikipedia.org/wiki/Episcopal_polity\" title=\"Episcopal polity\">episcopacy</a>, is presented to the <a href=\"https://wikipedia.org/wiki/Long_Parliament\" title=\"Long Parliament\">Long Parliament</a>.", "links": [{"title": "Root and Branch petition", "link": "https://wikipedia.org/wiki/Root_and_Branch_petition"}, {"title": "Episcopal polity", "link": "https://wikipedia.org/wiki/Episcopal_polity"}, {"title": "Long Parliament", "link": "https://wikipedia.org/wiki/Long_Parliament"}]}, {"year": "1675", "text": "<PERSON> expedition enters San Rafael Lake in western Patagonia.", "html": "1675 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_expedition\" title=\"Antonio de Vea expedition\">Antonio <PERSON> expedition</a> enters <a href=\"https://wikipedia.org/wiki/San_Rafael_Lake\" title=\"San Rafael Lake\">San Rafael Lake</a> in western Patagonia.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_expedition\" title=\"Antonio <PERSON> expedition\">Antonio <PERSON> expedition</a> enters <a href=\"https://wikipedia.org/wiki/San_Rafael_Lake\" title=\"San Rafael Lake\">San Rafael Lake</a> in western Patagonia.", "links": [{"title": "<PERSON> expedition", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_expedition"}, {"title": "San Rafael Lake", "link": "https://wikipedia.org/wiki/San_Rafael_Lake"}]}, {"year": "1688", "text": "Glorious Revolution: <PERSON> of England, while trying to flee to France, throws the Great Seal of the Realm into the River Thames.", "html": "1688 - <a href=\"https://wikipedia.org/wiki/Glorious_Revolution\" title=\"Glorious Revolution\">Glorious Revolution</a>: <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_England\" title=\"James II of England\"><PERSON> of England</a>, while trying to flee to France, throws the <a href=\"https://wikipedia.org/wiki/Great_Seal_of_the_Realm\" title=\"Great Seal of the Realm\">Great Seal of the Realm</a> into the <a href=\"https://wikipedia.org/wiki/River_Thames\" title=\"River Thames\">River Thames</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Glorious_Revolution\" title=\"Glorious Revolution\">Glorious Revolution</a>: <a href=\"https://wikipedia.org/wiki/<PERSON>_II_of_England\" title=\"James II of England\"><PERSON> of England</a>, while trying to flee to France, throws the <a href=\"https://wikipedia.org/wiki/Great_Seal_of_the_Realm\" title=\"Great Seal of the Realm\">Great Seal of the Realm</a> into the <a href=\"https://wikipedia.org/wiki/River_Thames\" title=\"River Thames\">River Thames</a>.", "links": [{"title": "Glorious Revolution", "link": "https://wikipedia.org/wiki/Glorious_Revolution"}, {"title": "<PERSON> of <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_England"}, {"title": "Great Seal of the Realm", "link": "https://wikipedia.org/wiki/Great_Seal_of_the_Realm"}, {"title": "River Thames", "link": "https://wikipedia.org/wiki/River_Thames"}]}, {"year": "1792", "text": "French Revolution: King <PERSON> of France is put on trial for treason by the National Convention.", "html": "1792 - <a href=\"https://wikipedia.org/wiki/French_Revolution\" title=\"French Revolution\">French Revolution</a>: King <a href=\"https://wikipedia.org/wiki/Louis_XVI_of_France\" class=\"mw-redirect\" title=\"Louis XVI of France\"><PERSON> XVI of France</a> <a href=\"https://wikipedia.org/wiki/Trial_of_Louis_XVI\" title=\"Trial of Louis XVI\">is put on trial for treason</a> by the <a href=\"https://wikipedia.org/wiki/National_Convention\" title=\"National Convention\">National Convention</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/French_Revolution\" title=\"French Revolution\">French Revolution</a>: King <a href=\"https://wikipedia.org/wiki/Louis_XVI_of_France\" class=\"mw-redirect\" title=\"Louis XVI of France\"><PERSON> of France</a> <a href=\"https://wikipedia.org/wiki/Trial_of_Louis_XVI\" title=\"Trial of Louis XVI\">is put on trial for treason</a> by the <a href=\"https://wikipedia.org/wiki/National_Convention\" title=\"National Convention\">National Convention</a>.", "links": [{"title": "French Revolution", "link": "https://wikipedia.org/wiki/French_Revolution"}, {"title": "<PERSON> of France", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_France"}, {"title": "Trial of Louis XVI", "link": "https://wikipedia.org/wiki/Trial_of_<PERSON>_<PERSON>"}, {"title": "National Convention", "link": "https://wikipedia.org/wiki/National_Convention"}]}, {"year": "1815", "text": "The U.S. Senate creates a select committee on finance and a uniform national currency, predecessor of the United States Senate Committee on Finance.", "html": "1815 - The <a href=\"https://wikipedia.org/wiki/United_States_Senate\" title=\"United States Senate\">U.S. Senate</a> creates a select committee on finance and a uniform national currency, predecessor of the <a href=\"https://wikipedia.org/wiki/United_States_Senate_Committee_on_Finance\" title=\"United States Senate Committee on Finance\">United States Senate Committee on Finance</a>.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/United_States_Senate\" title=\"United States Senate\">U.S. Senate</a> creates a select committee on finance and a uniform national currency, predecessor of the <a href=\"https://wikipedia.org/wiki/United_States_Senate_Committee_on_Finance\" title=\"United States Senate Committee on Finance\">United States Senate Committee on Finance</a>.", "links": [{"title": "United States Senate", "link": "https://wikipedia.org/wiki/United_States_Senate"}, {"title": "United States Senate Committee on Finance", "link": "https://wikipedia.org/wiki/United_States_Senate_Committee_on_Finance"}]}, {"year": "1816", "text": "Indiana becomes the 19th U.S. state.", "html": "1816 - <a href=\"https://wikipedia.org/wiki/Indiana\" title=\"Indiana\">Indiana</a> becomes the 19th <a href=\"https://wikipedia.org/wiki/U.S._state\" title=\"U.S. state\">U.S. state</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Indiana\" title=\"Indiana\">Indiana</a> becomes the 19th <a href=\"https://wikipedia.org/wiki/U.S._state\" title=\"U.S. state\">U.S. state</a>.", "links": [{"title": "Indiana", "link": "https://wikipedia.org/wiki/Indiana"}, {"title": "U.S. state", "link": "https://wikipedia.org/wiki/U.S._state"}]}, {"year": "1868", "text": "Paraguayan War: Brazilian troops defeat the Paraguayan Army at the Battle of Avay.", "html": "1868 - <a href=\"https://wikipedia.org/wiki/Paraguayan_War\" title=\"Paraguayan War\">Paraguayan War</a>: <a href=\"https://wikipedia.org/wiki/Brazilian_Army\" title=\"Brazilian Army\">Brazilian troops</a> defeat the <a href=\"https://wikipedia.org/wiki/Paraguayan_Army\" title=\"Paraguayan Army\">Paraguayan Army</a> at the <a href=\"https://wikipedia.org/wiki/Battle_of_Avay\" title=\"Battle of Avay\">Battle of Avay</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Paraguayan_War\" title=\"Paraguayan War\">Paraguayan War</a>: <a href=\"https://wikipedia.org/wiki/Brazilian_Army\" title=\"Brazilian Army\">Brazilian troops</a> defeat the <a href=\"https://wikipedia.org/wiki/Paraguayan_Army\" title=\"Paraguayan Army\">Paraguayan Army</a> at the <a href=\"https://wikipedia.org/wiki/Battle_of_Avay\" title=\"Battle of Avay\">Battle of Avay</a>.", "links": [{"title": "Paraguayan War", "link": "https://wikipedia.org/wiki/Paraguayan_War"}, {"title": "Brazilian Army", "link": "https://wikipedia.org/wiki/Brazilian_Army"}, {"title": "Paraguayan Army", "link": "https://wikipedia.org/wiki/Paraguayan_Army"}, {"title": "Battle of Avay", "link": "https://wikipedia.org/wiki/Battle_of_Avay"}]}, {"year": "1899", "text": "Second Boer War: In the Battle of Magersfontein the Boers commanded by general <PERSON><PERSON> inflict a defeat on the forces of the British Empire commanded by Lord <PERSON><PERSON><PERSON> trying to relieve the Siege of Kimberley.", "html": "1899 - <a href=\"https://wikipedia.org/wiki/Second_Boer_War\" title=\"Second Boer War\">Second Boer War</a>: In the <a href=\"https://wikipedia.org/wiki/Battle_of_Magersfontein\" title=\"Battle of Magersfontein\">Battle of Magersfontein</a> the <a href=\"https://wikipedia.org/wiki/Boer\" class=\"mw-redirect\" title=\"Boer\">Boers</a> commanded by general <a href=\"https://wikipedia.org/wiki/Piet_Cronj%C3%A9\" title=\"Piet Cronjé\">Piet <PERSON></a> inflict a defeat on the forces of the <a href=\"https://wikipedia.org/wiki/British_Empire\" title=\"British Empire\">British Empire</a> commanded by <a href=\"https://wikipedia.org/wiki/<PERSON>,_3rd_Baron_<PERSON>\" title=\"<PERSON>, 3rd <PERSON>\">Lord <PERSON></a> trying to relieve the <a href=\"https://wikipedia.org/wiki/Siege_of_Kimberley\" title=\"Siege of Kimberley\">Siege of Kimberley</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Second_Boer_War\" title=\"Second Boer War\">Second Boer War</a>: In the <a href=\"https://wikipedia.org/wiki/Battle_of_Magersfontein\" title=\"Battle of Magersfontein\">Battle of Magersfontein</a> the <a href=\"https://wikipedia.org/wiki/Boer\" class=\"mw-redirect\" title=\"Boer\">Boers</a> commanded by general <a href=\"https://wikipedia.org/wiki/Piet_Cronj%C3%A9\" title=\"Piet Cronj<PERSON>\">Piet <PERSON></a> inflict a defeat on the forces of the <a href=\"https://wikipedia.org/wiki/British_Empire\" title=\"British Empire\">British Empire</a> commanded by <a href=\"https://wikipedia.org/wiki/<PERSON>,_3rd_Baron_<PERSON>\" title=\"<PERSON>, 3rd Baron <PERSON>\">Lord <PERSON></a> trying to relieve the <a href=\"https://wikipedia.org/wiki/Siege_of_Kimberley\" title=\"Siege of Kimberley\">Siege of Kimberley</a>.", "links": [{"title": "Second Boer War", "link": "https://wikipedia.org/wiki/Second_Boer_War"}, {"title": "Battle of Magersfontein", "link": "https://wikipedia.org/wiki/Battle_of_Magersfontein"}, {"title": "Boer", "link": "https://wikipedia.org/wiki/Boer"}, {"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Piet_Cronj%C3%A9"}, {"title": "British Empire", "link": "https://wikipedia.org/wiki/British_Empire"}, {"title": "<PERSON>, 3rd Baron <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>,_3rd_Baron_<PERSON>"}, {"title": "Siege of Kimberley", "link": "https://wikipedia.org/wiki/Siege_of_Kimberley"}]}, {"year": "1901", "text": "<PERSON><PERSON><PERSON><PERSON><PERSON> transmits the first transatlantic radio signal from Poldhu, Cornwall, England to Saint John's, Newfoundland.", "html": "1901 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON><PERSON></a> transmits the first transatlantic radio signal from <a href=\"https://wikipedia.org/wiki/Poldhu,_Cornwall\" class=\"mw-redirect\" title=\"Poldhu, Cornwall\">Poldhu, Cornwall</a>, England to <a href=\"https://wikipedia.org/wiki/St._John%27s,_Newfoundland_and_Labrador\" title=\"St. John's, Newfoundland and Labrador\">Saint John's</a>, <a href=\"https://wikipedia.org/wiki/Dominion_of_Newfoundland\" title=\"Dominion of Newfoundland\">Newfoundland</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON><PERSON></a> transmits the first transatlantic radio signal from <a href=\"https://wikipedia.org/wiki/Poldhu,_Cornwall\" class=\"mw-redirect\" title=\"Poldhu, Cornwall\">Poldhu, Cornwall</a>, England to <a href=\"https://wikipedia.org/wiki/St._John%27s,_Newfoundland_and_Labrador\" title=\"St. John's, Newfoundland and Labrador\">Saint John's</a>, <a href=\"https://wikipedia.org/wiki/Dominion_of_Newfoundland\" title=\"Dominion of Newfoundland\">Newfoundland</a>.", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON><PERSON>_<PERSON>"}, {"title": "Poldhu, Cornwall", "link": "https://wikipedia.org/wiki/Poldhu,_Cornwall"}, {"title": "St. John's, Newfoundland and Labrador", "link": "https://wikipedia.org/wiki/St._<PERSON>%27s,_Newfoundland_and_Labrador"}, {"title": "Dominion of Newfoundland", "link": "https://wikipedia.org/wiki/Dominion_of_Newfoundland"}]}, {"year": "1905", "text": "A workers' uprising occurs in Kyiv, Ukraine (then part of the Russian Empire), and establishes the Shuliavka Republic.", "html": "1905 - A workers' uprising occurs in <a href=\"https://wikipedia.org/wiki/Kyiv\" title=\"Kyiv\">Kyiv</a>, Ukraine (then part of the <a href=\"https://wikipedia.org/wiki/Russian_Empire\" title=\"Russian Empire\">Russian Empire</a>), and establishes the <a href=\"https://wikipedia.org/wiki/Shuliavka_Republic\" title=\"Shuliavka Republic\">Shuliavka Republic</a>.", "no_year_html": "A workers' uprising occurs in <a href=\"https://wikipedia.org/wiki/Kyiv\" title=\"Kyiv\">Kyiv</a>, Ukraine (then part of the <a href=\"https://wikipedia.org/wiki/Russian_Empire\" title=\"Russian Empire\">Russian Empire</a>), and establishes the <a href=\"https://wikipedia.org/wiki/Shuliavka_Republic\" title=\"Shuliavka Republic\">Shuliavka Republic</a>.", "links": [{"title": "Kyiv", "link": "https://wikipedia.org/wiki/Kyiv"}, {"title": "Russian Empire", "link": "https://wikipedia.org/wiki/Russian_Empire"}, {"title": "Shuliavka Republic", "link": "https://wikipedia.org/wiki/Shuliavka_Republic"}]}, {"year": "1907", "text": "The New Zealand Parliament Buildings are almost completely destroyed by fire.", "html": "1907 - The <a href=\"https://wikipedia.org/wiki/New_Zealand_Parliament_Buildings\" title=\"New Zealand Parliament Buildings\">New Zealand Parliament Buildings</a> are almost completely destroyed by fire.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/New_Zealand_Parliament_Buildings\" title=\"New Zealand Parliament Buildings\">New Zealand Parliament Buildings</a> are almost completely destroyed by fire.", "links": [{"title": "New Zealand Parliament Buildings", "link": "https://wikipedia.org/wiki/New_Zealand_Parliament_Buildings"}]}, {"year": "1913", "text": "More than two years after it was stolen from the Louvre, <PERSON>'s painting <PERSON> is recovered in Florence, Italy. The thief, <PERSON>, is immediately arrested.", "html": "1913 - More than two years after it was stolen from the <a href=\"https://wikipedia.org/wiki/Louvre\" title=\"Louvre\"><PERSON><PERSON></a>, <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> da Vinci\"><PERSON></a>'s painting <i><a href=\"https://wikipedia.org/wiki/<PERSON>_Lisa\" title=\"<PERSON> Lisa\"><PERSON></a></i> is recovered in <a href=\"https://wikipedia.org/wiki/Florence\" title=\"Florence\">Florence</a>, Italy. The thief, <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, is immediately arrested.", "no_year_html": "More than two years after it was stolen from the <a href=\"https://wikipedia.org/wiki/Louvre\" title=\"Louvre\"><PERSON><PERSON></a>, <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> da Vinci\"><PERSON></a>'s painting <i><a href=\"https://wikipedia.org/wiki/<PERSON>_Lisa\" title=\"<PERSON> Lisa\"><PERSON></a></i> is recovered in <a href=\"https://wikipedia.org/wiki/Florence\" title=\"Florence\">Florence</a>, Italy. The thief, <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, is immediately arrested.", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>vre"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Florence", "link": "https://wikipedia.org/wiki/Florence"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Vincenzo_<PERSON>"}]}, {"year": "1917", "text": "World War I: British General <PERSON> enters Jerusalem on foot and declares martial law.", "html": "1917 - <a href=\"https://wikipedia.org/wiki/World_War_I\" title=\"World War I\">World War I</a>: British General <a href=\"https://wikipedia.org/wiki/<PERSON>,_1st_Viscount_<PERSON>\" title=\"<PERSON>, 1st Viscount <PERSON>\"><PERSON></a> enters <a href=\"https://wikipedia.org/wiki/Jerusalem\" title=\"Jerusalem\">Jerusalem</a> on foot and declares <a href=\"https://wikipedia.org/wiki/Martial_law\" title=\"Martial law\">martial law</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/World_War_I\" title=\"World War I\">World War I</a>: British General <a href=\"https://wikipedia.org/wiki/<PERSON>,_1st_Viscount_<PERSON>\" title=\"<PERSON>, 1st Viscount <PERSON>\"><PERSON></a> enters <a href=\"https://wikipedia.org/wiki/Jerusalem\" title=\"Jerusalem\">Jerusalem</a> on foot and declares <a href=\"https://wikipedia.org/wiki/Martial_law\" title=\"Martial law\">martial law</a>.", "links": [{"title": "World War I", "link": "https://wikipedia.org/wiki/World_War_I"}, {"title": "<PERSON>, 1st Viscount <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>,_1st_Viscount_<PERSON>"}, {"title": "Jerusalem", "link": "https://wikipedia.org/wiki/Jerusalem"}, {"title": "Martial law", "link": "https://wikipedia.org/wiki/Martial_law"}]}, {"year": "1920", "text": "Irish War of Independence: In retaliation for a recent IRA ambush, British forces burn and loot numerous buildings in Cork city. Many civilians report being beaten, shot at, robbed and verbally abused by British forces.", "html": "1920 - <a href=\"https://wikipedia.org/wiki/Irish_War_of_Independence\" title=\"Irish War of Independence\">Irish War of Independence</a>: In retaliation for a recent <a href=\"https://wikipedia.org/wiki/Irish_Republican_Army\" title=\"Irish Republican Army\">IRA</a> ambush, British forces <a href=\"https://wikipedia.org/wiki/Burning_of_Cork\" title=\"Burning of Cork\">burn and loot numerous buildings in Cork city</a>. Many civilians report being beaten, shot at, robbed and verbally abused by British forces.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Irish_War_of_Independence\" title=\"Irish War of Independence\">Irish War of Independence</a>: In retaliation for a recent <a href=\"https://wikipedia.org/wiki/Irish_Republican_Army\" title=\"Irish Republican Army\">IRA</a> ambush, British forces <a href=\"https://wikipedia.org/wiki/Burning_of_Cork\" title=\"Burning of Cork\">burn and loot numerous buildings in Cork city</a>. Many civilians report being beaten, shot at, robbed and verbally abused by British forces.", "links": [{"title": "Irish War of Independence", "link": "https://wikipedia.org/wiki/Irish_War_of_Independence"}, {"title": "Irish Republican Army", "link": "https://wikipedia.org/wiki/Irish_Republican_Army"}, {"title": "Burning of Cork", "link": "https://wikipedia.org/wiki/Burning_of_Cork"}]}, {"year": "1925", "text": "Roman Catholic papal encyclical Q<PERSON><PERSON> primas introduces the Feast of Christ the King.", "html": "1925 - <a href=\"https://wikipedia.org/wiki/Catholic_Church\" title=\"Catholic Church\">Roman Catholic</a> <a href=\"https://wikipedia.org/wiki/Pope\" title=\"Pope\">papal</a> <a href=\"https://wikipedia.org/wiki/Encyclical\" title=\"Encyclical\">encyclical</a> <i><a href=\"https://wikipedia.org/wiki/Quas_primas\" title=\"Quas primas\">Quas primas</a></i> introduces the <a href=\"https://wikipedia.org/wiki/Feast_of_Christ_the_King\" title=\"Feast of Christ the King\">Feast of Christ the King</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Catholic_Church\" title=\"Catholic Church\">Roman Catholic</a> <a href=\"https://wikipedia.org/wiki/Pope\" title=\"Pope\">papal</a> <a href=\"https://wikipedia.org/wiki/Encyclical\" title=\"Encyclical\">encyclical</a> <i><a href=\"https://wikipedia.org/wiki/Quas_primas\" title=\"Quas primas\">Quas primas</a></i> introduces the <a href=\"https://wikipedia.org/wiki/Feast_of_Christ_the_King\" title=\"Feast of Christ the King\">Feast of Christ the King</a>.", "links": [{"title": "Catholic Church", "link": "https://wikipedia.org/wiki/Catholic_Church"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Pope"}, {"title": "Encyclical", "link": "https://wikipedia.org/wiki/Encyclical"}, {"title": "Quas primas", "link": "https://wikipedia.org/wiki/Qua<PERSON>_primas"}, {"title": "Feast of Christ the King", "link": "https://wikipedia.org/wiki/Feast_of_<PERSON>_the_King"}]}, {"year": "1927", "text": "Guangzhou Uprising: Communist Red Guards launch an uprising in Guangzhou, China, taking over most of the city and announcing the formation of a Guangzhou Soviet.", "html": "1927 - <a href=\"https://wikipedia.org/wiki/Guangzhou_Uprising\" title=\"Guangzhou Uprising\">Guangzhou Uprising</a>: <a href=\"https://wikipedia.org/wiki/Communist_Party_of_China\" class=\"mw-redirect\" title=\"Communist Party of China\">Communist</a> <a href=\"https://wikipedia.org/wiki/Red_Guards_(China)\" class=\"mw-redirect\" title=\"Red Guards (China)\">Red Guards</a> launch an uprising in <a href=\"https://wikipedia.org/wiki/Guangzhou\" title=\"Guangzhou\">Guangzhou</a>, China, taking over most of the city and announcing the formation of a Guangzhou <a href=\"https://wikipedia.org/wiki/Soviet_(council)\" title=\"Soviet (council)\">Soviet</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Guangzhou_Uprising\" title=\"Guangzhou Uprising\">Guangzhou Uprising</a>: <a href=\"https://wikipedia.org/wiki/Communist_Party_of_China\" class=\"mw-redirect\" title=\"Communist Party of China\">Communist</a> <a href=\"https://wikipedia.org/wiki/Red_Guards_(China)\" class=\"mw-redirect\" title=\"Red Guards (China)\">Red Guards</a> launch an uprising in <a href=\"https://wikipedia.org/wiki/Guangzhou\" title=\"Guangzhou\">Guangzhou</a>, China, taking over most of the city and announcing the formation of a Guangzhou <a href=\"https://wikipedia.org/wiki/Soviet_(council)\" title=\"Soviet (council)\">Soviet</a>.", "links": [{"title": "Guangzhou Uprising", "link": "https://wikipedia.org/wiki/Guangzhou_Uprising"}, {"title": "Communist Party of China", "link": "https://wikipedia.org/wiki/Communist_Party_of_China"}, {"title": "Red Guards (China)", "link": "https://wikipedia.org/wiki/Red_Guards_(China)"}, {"title": "Guangzhou", "link": "https://wikipedia.org/wiki/Guangzhou"}, {"title": "Soviet (council)", "link": "https://wikipedia.org/wiki/Soviet_(council)"}]}, {"year": "1931", "text": "Statute of Westminster 1931: The British Parliament establishes legislative equality between the UK and the Dominions of the Commonwealth—Australia, Canada, Newfoundland, New Zealand, South Africa, and Ireland.", "html": "1931 - <a href=\"https://wikipedia.org/wiki/Statute_of_Westminster_1931\" title=\"Statute of Westminster 1931\">Statute of Westminster 1931</a>: The <a href=\"https://wikipedia.org/wiki/Parliament_of_the_United_Kingdom\" title=\"Parliament of the United Kingdom\">British Parliament</a> establishes legislative equality between the UK and the <a href=\"https://wikipedia.org/wiki/Dominion\" title=\"Dominion\">Dominions</a> of the <a href=\"https://wikipedia.org/wiki/Commonwealth_of_Nations\" title=\"Commonwealth of Nations\">Commonwealth</a>—Australia, Canada, <a href=\"https://wikipedia.org/wiki/Dominion_of_Newfoundland\" title=\"Dominion of Newfoundland\">Newfoundland</a>, <a href=\"https://wikipedia.org/wiki/Dominion_of_New_Zealand\" title=\"Dominion of New Zealand\">New Zealand</a>, <a href=\"https://wikipedia.org/wiki/Union_of_South_Africa\" title=\"Union of South Africa\">South Africa</a>, and <a href=\"https://wikipedia.org/wiki/Irish_Free_State\" title=\"Irish Free State\">Ireland</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Statute_of_Westminster_1931\" title=\"Statute of Westminster 1931\">Statute of Westminster 1931</a>: The <a href=\"https://wikipedia.org/wiki/Parliament_of_the_United_Kingdom\" title=\"Parliament of the United Kingdom\">British Parliament</a> establishes legislative equality between the UK and the <a href=\"https://wikipedia.org/wiki/Dominion\" title=\"Dominion\">Dominions</a> of the <a href=\"https://wikipedia.org/wiki/Commonwealth_of_Nations\" title=\"Commonwealth of Nations\">Commonwealth</a>—Australia, Canada, <a href=\"https://wikipedia.org/wiki/Dominion_of_Newfoundland\" title=\"Dominion of Newfoundland\">Newfoundland</a>, <a href=\"https://wikipedia.org/wiki/Dominion_of_New_Zealand\" title=\"Dominion of New Zealand\">New Zealand</a>, <a href=\"https://wikipedia.org/wiki/Union_of_South_Africa\" title=\"Union of South Africa\">South Africa</a>, and <a href=\"https://wikipedia.org/wiki/Irish_Free_State\" title=\"Irish Free State\">Ireland</a>.", "links": [{"title": "Statute of Westminster 1931", "link": "https://wikipedia.org/wiki/Statute_of_Westminster_1931"}, {"title": "Parliament of the United Kingdom", "link": "https://wikipedia.org/wiki/Parliament_of_the_United_Kingdom"}, {"title": "Dominion", "link": "https://wikipedia.org/wiki/Dominion"}, {"title": "Commonwealth of Nations", "link": "https://wikipedia.org/wiki/Commonwealth_of_Nations"}, {"title": "Dominion of Newfoundland", "link": "https://wikipedia.org/wiki/Dominion_of_Newfoundland"}, {"title": "Dominion of New Zealand", "link": "https://wikipedia.org/wiki/Dominion_of_New_Zealand"}, {"title": "Union of South Africa", "link": "https://wikipedia.org/wiki/Union_of_South_Africa"}, {"title": "Irish Free State", "link": "https://wikipedia.org/wiki/Irish_Free_State"}]}, {"year": "1934", "text": "<PERSON>, co-founder of Alcoholics Anonymous, takes his last drink and enters treatment for the final time.", "html": "1934 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>.\" title=\"<PERSON>\"><PERSON></a>, co-founder of <a href=\"https://wikipedia.org/wiki/Alcoholics_Anonymous\" title=\"Alcoholics Anonymous\">Alcoholics Anonymous</a>, takes his last drink and enters treatment for the final time.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>.\" title=\"<PERSON>\"><PERSON></a>, co-founder of <a href=\"https://wikipedia.org/wiki/Alcoholics_Anonymous\" title=\"Alcoholics Anonymous\">Alcoholics Anonymous</a>, takes his last drink and enters treatment for the final time.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>."}, {"title": "Alcoholics Anonymous", "link": "https://wikipedia.org/wiki/Alcoholics_Anonymous"}]}, {"year": "1936", "text": "Abdication Crisis: <PERSON>'s abdication as King of the United Kingdom and the British Dominions beyond the Seas, and Emperor of India, becomes effective.", "html": "1936 - <a href=\"https://wikipedia.org/wiki/<PERSON>_VIII_abdication_crisis\" class=\"mw-redirect\" title=\"<PERSON> VIII abdication crisis\">Abdication Crisis</a>: <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> VIII\"><PERSON></a>'s abdication as King of the United Kingdom and the <a href=\"https://wikipedia.org/wiki/Dominion\" title=\"Dominion\">British Dominions beyond the Seas</a>, and <a href=\"https://wikipedia.org/wiki/Emperor_of_India\" title=\"Emperor of India\">Emperor of India</a>, becomes effective.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_VIII_abdication_crisis\" class=\"mw-redirect\" title=\"<PERSON> VIII abdication crisis\">Abdication Crisis</a>: <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> VIII\"><PERSON></a>'s abdication as King of the United Kingdom and the <a href=\"https://wikipedia.org/wiki/Dominion\" title=\"Dominion\">British Dominions beyond the Seas</a>, and <a href=\"https://wikipedia.org/wiki/Emperor_of_India\" title=\"Emperor of India\">Emperor of India</a>, becomes effective.", "links": [{"title": "Edward VIII abdication crisis", "link": "https://wikipedia.org/wiki/<PERSON>_VIII_abdication_crisis"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Dominion", "link": "https://wikipedia.org/wiki/Dominion"}, {"title": "Emperor of India", "link": "https://wikipedia.org/wiki/Emperor_of_India"}]}, {"year": "1937", "text": "Second Italo-Ethiopian War: Italy leaves the League of Nations.", "html": "1937 - <a href=\"https://wikipedia.org/wiki/Second_Italo-Ethiopian_War\" title=\"Second Italo-Ethiopian War\">Second Italo-Ethiopian War</a>: <a href=\"https://wikipedia.org/wiki/Kingdom_of_Italy\" title=\"Kingdom of Italy\">Italy</a> leaves the <a href=\"https://wikipedia.org/wiki/League_of_Nations\" title=\"League of Nations\">League of Nations</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Second_Italo-Ethiopian_War\" title=\"Second Italo-Ethiopian War\">Second Italo-Ethiopian War</a>: <a href=\"https://wikipedia.org/wiki/Kingdom_of_Italy\" title=\"Kingdom of Italy\">Italy</a> leaves the <a href=\"https://wikipedia.org/wiki/League_of_Nations\" title=\"League of Nations\">League of Nations</a>.", "links": [{"title": "Second Italo-Ethiopian War", "link": "https://wikipedia.org/wiki/Second_Italo-Ethiopian_War"}, {"title": "Kingdom of Italy", "link": "https://wikipedia.org/wiki/Kingdom_of_Italy"}, {"title": "League of Nations", "link": "https://wikipedia.org/wiki/League_of_Nations"}]}, {"year": "1941", "text": "World War II: Germany and Italy declare war on the United States, following the Americans' declaration of war on the Empire of Japan in the wake of the attack on Pearl Harbor. The United States, in turn, declares war on them.", "html": "1941 - <a href=\"https://wikipedia.org/wiki/World_War_II\" title=\"World War II\">World War II</a>: <a href=\"https://wikipedia.org/wiki/Nazi_Germany\" title=\"Nazi Germany\">Germany</a> and Italy declare war on the United States, following the Americans' declaration of war on the <a href=\"https://wikipedia.org/wiki/Empire_of_Japan\" title=\"Empire of Japan\">Empire of Japan</a> in the wake of the <a href=\"https://wikipedia.org/wiki/Attack_on_Pearl_Harbor\" title=\"Attack on Pearl Harbor\">attack on Pearl Harbor</a>. The United States, in turn, declares war on them.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/World_War_II\" title=\"World War II\">World War II</a>: <a href=\"https://wikipedia.org/wiki/Nazi_Germany\" title=\"Nazi Germany\">Germany</a> and Italy declare war on the United States, following the Americans' declaration of war on the <a href=\"https://wikipedia.org/wiki/Empire_of_Japan\" title=\"Empire of Japan\">Empire of Japan</a> in the wake of the <a href=\"https://wikipedia.org/wiki/Attack_on_Pearl_Harbor\" title=\"Attack on Pearl Harbor\">attack on Pearl Harbor</a>. The United States, in turn, declares war on them.", "links": [{"title": "World War II", "link": "https://wikipedia.org/wiki/World_War_II"}, {"title": "Nazi Germany", "link": "https://wikipedia.org/wiki/Nazi_Germany"}, {"title": "Empire of Japan", "link": "https://wikipedia.org/wiki/Empire_of_Japan"}, {"title": "Attack on Pearl Harbor", "link": "https://wikipedia.org/wiki/Attack_on_Pearl_Harbor"}]}, {"year": "1941", "text": "World War II: Poland declares war on the Empire of Japan.", "html": "1941 - World War II: <a href=\"https://wikipedia.org/wiki/Polish_government-in-exile\" title=\"Polish government-in-exile\">Poland</a> declares war on the <a href=\"https://wikipedia.org/wiki/Empire_of_Japan\" title=\"Empire of Japan\">Empire of Japan</a>.", "no_year_html": "World War II: <a href=\"https://wikipedia.org/wiki/Polish_government-in-exile\" title=\"Polish government-in-exile\">Poland</a> declares war on the <a href=\"https://wikipedia.org/wiki/Empire_of_Japan\" title=\"Empire of Japan\">Empire of Japan</a>.", "links": [{"title": "Polish government-in-exile", "link": "https://wikipedia.org/wiki/Polish_government-in-exile"}, {"title": "Empire of Japan", "link": "https://wikipedia.org/wiki/Empire_of_Japan"}]}, {"year": "1941", "text": "World War II: The Imperial Japanese Navy suffers its first loss of surface vessels during the Battle of Wake Island.", "html": "1941 - World War II: The <a href=\"https://wikipedia.org/wiki/Imperial_Japanese_Navy\" title=\"Imperial Japanese Navy\">Imperial Japanese Navy</a> suffers its first loss of surface vessels during the <a href=\"https://wikipedia.org/wiki/Battle_of_Wake_Island\" title=\"Battle of Wake Island\">Battle of Wake Island</a>.", "no_year_html": "World War II: The <a href=\"https://wikipedia.org/wiki/Imperial_Japanese_Navy\" title=\"Imperial Japanese Navy\">Imperial Japanese Navy</a> suffers its first loss of surface vessels during the <a href=\"https://wikipedia.org/wiki/Battle_of_Wake_Island\" title=\"Battle of Wake Island\">Battle of Wake Island</a>.", "links": [{"title": "Imperial Japanese Navy", "link": "https://wikipedia.org/wiki/Imperial_Japanese_Navy"}, {"title": "Battle of Wake Island", "link": "https://wikipedia.org/wiki/Battle_of_Wake_Island"}]}, {"year": "1946", "text": "The United Nations International Children's Emergency Fund (UNICEF) is established.", "html": "1946 - The United Nations International Children's Emergency Fund (<a href=\"https://wikipedia.org/wiki/UNICEF\" title=\"UNICEF\">UNICEF</a>) is established.", "no_year_html": "The United Nations International Children's Emergency Fund (<a href=\"https://wikipedia.org/wiki/UNICEF\" title=\"UNICEF\">UNICEF</a>) is established.", "links": [{"title": "UNICEF", "link": "https://wikipedia.org/wiki/UNICEF"}]}, {"year": "1948", "text": "Arab-Israeli War: The United Nations passes General Assembly Resolution 194, creating a Conciliation Commission to mediate the conflict.", "html": "1948 - <a href=\"https://wikipedia.org/wiki/1948_Arab%E2%80%93Israeli_War\" title=\"1948 Arab-Israeli War\">Arab-Israeli War</a>: The <a href=\"https://wikipedia.org/wiki/United_Nations\" title=\"United Nations\">United Nations</a> passes <a href=\"https://wikipedia.org/wiki/United_Nations_General_Assembly_Resolution_194\" title=\"United Nations General Assembly Resolution 194\">General Assembly Resolution 194</a>, creating a <a href=\"https://wikipedia.org/wiki/United_Nations_Conciliation_Commission\" class=\"mw-redirect\" title=\"United Nations Conciliation Commission\">Conciliation Commission</a> to mediate the conflict.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/1948_Arab%E2%80%93Israeli_War\" title=\"1948 Arab-Israeli War\">Arab-Israeli War</a>: The <a href=\"https://wikipedia.org/wiki/United_Nations\" title=\"United Nations\">United Nations</a> passes <a href=\"https://wikipedia.org/wiki/United_Nations_General_Assembly_Resolution_194\" title=\"United Nations General Assembly Resolution 194\">General Assembly Resolution 194</a>, creating a <a href=\"https://wikipedia.org/wiki/United_Nations_Conciliation_Commission\" class=\"mw-redirect\" title=\"United Nations Conciliation Commission\">Conciliation Commission</a> to mediate the conflict.", "links": [{"title": "1948 Arab-Israeli War", "link": "https://wikipedia.org/wiki/1948_Arab%E2%80%93Israeli_War"}, {"title": "United Nations", "link": "https://wikipedia.org/wiki/United_Nations"}, {"title": "United Nations General Assembly Resolution 194", "link": "https://wikipedia.org/wiki/United_Nations_General_Assembly_Resolution_194"}, {"title": "United Nations Conciliation Commission", "link": "https://wikipedia.org/wiki/United_Nations_Conciliation_Commission"}]}, {"year": "1958", "text": "French Upper Volta and French Dahomey gain self-government from France, becoming the Republic of Upper Volta (now Burkina Faso) and the Republic of Dahomey (now Benin), respectively, and joining the French Community.", "html": "1958 - <a href=\"https://wikipedia.org/wiki/French_Upper_Volta\" title=\"French Upper Volta\">French Upper Volta</a> and <a href=\"https://wikipedia.org/wiki/French_Dahomey\" title=\"French Dahomey\">French Dahomey</a> gain self-government from <a href=\"https://wikipedia.org/wiki/French_Fourth_Republic\" title=\"French Fourth Republic\">France</a>, becoming the <a href=\"https://wikipedia.org/wiki/Republic_of_Upper_Volta\" title=\"Republic of Upper Volta\">Republic of Upper Volta</a> (now <a href=\"https://wikipedia.org/wiki/Burkina_Faso\" title=\"Burkina Faso\">Burkina Faso</a>) and the <a href=\"https://wikipedia.org/wiki/Republic_of_Dahomey\" title=\"Republic of Dahomey\">Republic of Dahomey</a> (now <a href=\"https://wikipedia.org/wiki/Benin\" title=\"Benin\">Benin</a>), respectively, and joining the <a href=\"https://wikipedia.org/wiki/French_Community\" title=\"French Community\">French Community</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/French_Upper_Volta\" title=\"French Upper Volta\">French Upper Volta</a> and <a href=\"https://wikipedia.org/wiki/French_Dahomey\" title=\"French Dahomey\">French Dahomey</a> gain self-government from <a href=\"https://wikipedia.org/wiki/French_Fourth_Republic\" title=\"French Fourth Republic\">France</a>, becoming the <a href=\"https://wikipedia.org/wiki/Republic_of_Upper_Volta\" title=\"Republic of Upper Volta\">Republic of Upper Volta</a> (now <a href=\"https://wikipedia.org/wiki/Burkina_Faso\" title=\"Burkina Faso\">Burkina Faso</a>) and the <a href=\"https://wikipedia.org/wiki/Republic_of_Dahomey\" title=\"Republic of Dahomey\">Republic of Dahomey</a> (now <a href=\"https://wikipedia.org/wiki/Benin\" title=\"Benin\">Benin</a>), respectively, and joining the <a href=\"https://wikipedia.org/wiki/French_Community\" title=\"French Community\">French Community</a>.", "links": [{"title": "French Upper Volta", "link": "https://wikipedia.org/wiki/French_Upper_Volta"}, {"title": "French Dahomey", "link": "https://wikipedia.org/wiki/French_Dahomey"}, {"title": "French Fourth Republic", "link": "https://wikipedia.org/wiki/French_Fourth_Republic"}, {"title": "Republic of Upper Volta", "link": "https://wikipedia.org/wiki/Republic_of_Upper_Volta"}, {"title": "Burkina Faso", "link": "https://wikipedia.org/wiki/Burkina_Faso"}, {"title": "Republic of Dahomey", "link": "https://wikipedia.org/wiki/Republic_of_Dahomey"}, {"title": "Benin", "link": "https://wikipedia.org/wiki/Benin"}, {"title": "French Community", "link": "https://wikipedia.org/wiki/French_Community"}]}, {"year": "1960", "text": "French forces crack down in a violent clash with protesters in French Algeria during a visit by French President <PERSON>.", "html": "1960 - French forces crack down in a violent clash with protesters in <a href=\"https://wikipedia.org/wiki/French_Algeria\" title=\"French Algeria\">French Algeria</a> during a visit by <a href=\"https://wikipedia.org/wiki/President_of_France\" title=\"President of France\">French President</a> <a href=\"https://wikipedia.org/wiki/Charles<PERSON>\" title=\"<PERSON>\"><PERSON></a>.", "no_year_html": "French forces crack down in a violent clash with protesters in <a href=\"https://wikipedia.org/wiki/French_Algeria\" title=\"French Algeria\">French Algeria</a> during a visit by <a href=\"https://wikipedia.org/wiki/President_of_France\" title=\"President of France\">French President</a> <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>.", "links": [{"title": "French Algeria", "link": "https://wikipedia.org/wiki/French_Algeria"}, {"title": "President of France", "link": "https://wikipedia.org/wiki/President_of_France"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1962", "text": "<PERSON>, convicted of murder, is the last person to be executed in Canada.", "html": "1962 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, convicted of murder, is the last person to be <a href=\"https://wikipedia.org/wiki/Capital_punishment_in_Canada\" title=\"Capital punishment in Canada\">executed in Canada</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, convicted of murder, is the last person to be <a href=\"https://wikipedia.org/wiki/Capital_punishment_in_Canada\" title=\"Capital punishment in Canada\">executed in Canada</a>.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Capital punishment in Canada", "link": "https://wikipedia.org/wiki/Capital_punishment_in_Canada"}]}, {"year": "1964", "text": "<PERSON><PERSON> speaks at the United Nations General Assembly in New York City.", "html": "1964 - <a href=\"https://wikipedia.org/wiki/Ch<PERSON>_Guevara\" title=\"Che Guevara\"><PERSON><PERSON></a> speaks at the <a href=\"https://wikipedia.org/wiki/United_Nations_General_Assembly\" title=\"United Nations General Assembly\">United Nations General Assembly</a> in New York City.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_Guevara\" title=\"Che Guevara\"><PERSON><PERSON></a> speaks at the <a href=\"https://wikipedia.org/wiki/United_Nations_General_Assembly\" title=\"United Nations General Assembly\">United Nations General Assembly</a> in New York City.", "links": [{"title": "<PERSON>e Guevara", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>ra"}, {"title": "United Nations General Assembly", "link": "https://wikipedia.org/wiki/United_Nations_General_Assembly"}]}, {"year": "1972", "text": "Apollo 17 becomes the sixth and final Apollo mission to land on the Moon.", "html": "1972 - <a href=\"https://wikipedia.org/wiki/Apollo_17\" title=\"Apollo 17\">Apollo 17</a> becomes the sixth and final <a href=\"https://wikipedia.org/wiki/Apollo_program\" title=\"Apollo program\">Apollo mission</a> to land on the <a href=\"https://wikipedia.org/wiki/Moon\" title=\"Moon\">Moon</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Apollo_17\" title=\"Apollo 17\">Apollo 17</a> becomes the sixth and final <a href=\"https://wikipedia.org/wiki/Apollo_program\" title=\"Apollo program\">Apollo mission</a> to land on the <a href=\"https://wikipedia.org/wiki/Moon\" title=\"Moon\">Moon</a>.", "links": [{"title": "Apollo 17", "link": "https://wikipedia.org/wiki/Apollo_17"}, {"title": "Apollo program", "link": "https://wikipedia.org/wiki/Apollo_program"}, {"title": "Moon", "link": "https://wikipedia.org/wiki/Moon"}]}, {"year": "1978", "text": "The Lufthansa heist is committed by a group led by Lucchese family associate <PERSON>. It was the largest cash robbery ever committed on American soil, at that time.", "html": "1978 - The <a href=\"https://wikipedia.org/wiki/Lufthan<PERSON>_heist\" title=\"Lufthansa heist\">Lufthansa heist</a> is committed by a group led by <a href=\"https://wikipedia.org/wiki/Lucchese_crime_family\" title=\"Lucchese crime family\">Lucchese family</a> associate <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(gangster)\" title=\"<PERSON> (gangster)\"><PERSON></a>. It was the largest cash robbery ever committed on American soil, at that time.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/<PERSON>fthan<PERSON>_heist\" title=\"Lufthan<PERSON> heist\">Lufthansa heist</a> is committed by a group led by <a href=\"https://wikipedia.org/wiki/Lucchese_crime_family\" title=\"Lucchese crime family\">Lucchese family</a> associate <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(gangster)\" title=\"<PERSON> (gangster)\"><PERSON></a>. It was the largest cash robbery ever committed on American soil, at that time.", "links": [{"title": "Lu<PERSON><PERSON><PERSON> heist", "link": "https://wikipedia.org/wiki/Lufthan<PERSON>_heist"}, {"title": "Lucchese crime family", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>e_crime_family"}, {"title": "<PERSON> (gangster)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(gangster)"}]}, {"year": "1980", "text": "The Comprehensive Environmental Response, Compensation, and Liability Act (Superfund) is enacted by the U.S. Congress.", "html": "1980 - The Comprehensive Environmental Response, Compensation, and Liability Act (<a href=\"https://wikipedia.org/wiki/Superfund\" title=\"Superfund\">Superfund</a>) is enacted by the <a href=\"https://wikipedia.org/wiki/United_States_Congress\" title=\"United States Congress\">U.S. Congress</a>.", "no_year_html": "The Comprehensive Environmental Response, Compensation, and Liability Act (<a href=\"https://wikipedia.org/wiki/Superfund\" title=\"Superfund\">Superfund</a>) is enacted by the <a href=\"https://wikipedia.org/wiki/United_States_Congress\" title=\"United States Congress\">U.S. Congress</a>.", "links": [{"title": "Superfund", "link": "https://wikipedia.org/wiki/Superfund"}, {"title": "United States Congress", "link": "https://wikipedia.org/wiki/United_States_Congress"}]}, {"year": "1981", "text": "El Mozote massacre: Armed forces in El Salvador kill an estimated 900 civilians in an anti-guerrilla campaign during the Salvadoran Civil War.", "html": "1981 - <a href=\"https://wikipedia.org/wiki/El_Mozote_massacre\" title=\"El Mozote massacre\">El Mozote massacre</a>: Armed forces in <a href=\"https://wikipedia.org/wiki/El_Salvador\" title=\"El Salvador\">El Salvador</a> kill an estimated 900 civilians in an anti-guerrilla campaign during the <a href=\"https://wikipedia.org/wiki/Salvadoran_Civil_War\" title=\"Salvadoran Civil War\">Salvadoran Civil War</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/El_Mozote_massacre\" title=\"El Mozote massacre\">El Mozote massacre</a>: Armed forces in <a href=\"https://wikipedia.org/wiki/El_Salvador\" title=\"El Salvador\">El Salvador</a> kill an estimated 900 civilians in an anti-guerrilla campaign during the <a href=\"https://wikipedia.org/wiki/Salvadoran_Civil_War\" title=\"Salvadoran Civil War\">Salvadoran Civil War</a>.", "links": [{"title": "El Mozote massacre", "link": "https://wikipedia.org/wiki/El_Mozote_massacre"}, {"title": "El Salvador", "link": "https://wikipedia.org/wiki/El_Salvador"}, {"title": "Salvadoran Civil War", "link": "https://wikipedia.org/wiki/Salvadoran_Civil_War"}]}, {"year": "1988", "text": "A Soviet Air Force Il-76 aircraft crashes while participating in the Armenian earthquake relief, killing 78 people.", "html": "1988 - A <a href=\"https://wikipedia.org/wiki/Soviet_Air_Forces\" title=\"Soviet Air Forces\">Soviet Air Force</a> Il-76 aircraft <a href=\"https://wikipedia.org/wiki/1988_Soviet_Air_Force_Il-76_crash\" class=\"mw-redirect\" title=\"1988 Soviet Air Force Il-76 crash\">crashes</a> while participating in the <a href=\"https://wikipedia.org/wiki/1988_Armenian_earthquake\" title=\"1988 Armenian earthquake\">Armenian earthquake</a> relief, killing 78 people.", "no_year_html": "A <a href=\"https://wikipedia.org/wiki/Soviet_Air_Forces\" title=\"Soviet Air Forces\">Soviet Air Force</a> Il-76 aircraft <a href=\"https://wikipedia.org/wiki/1988_Soviet_Air_Force_Il-76_crash\" class=\"mw-redirect\" title=\"1988 Soviet Air Force Il-76 crash\">crashes</a> while participating in the <a href=\"https://wikipedia.org/wiki/1988_Armenian_earthquake\" title=\"1988 Armenian earthquake\">Armenian earthquake</a> relief, killing 78 people.", "links": [{"title": "Soviet Air Forces", "link": "https://wikipedia.org/wiki/Soviet_Air_Forces"}, {"title": "1988 Soviet Air Force Il-76 crash", "link": "https://wikipedia.org/wiki/1988_Soviet_Air_Force_Il-76_crash"}, {"title": "1988 Armenian earthquake", "link": "https://wikipedia.org/wiki/1988_Armenian_earthquake"}]}, {"year": "1990", "text": "Demonstrations by students and workers across Albania begin, which eventually trigger the fall of communism in Albania.", "html": "1990 - Demonstrations by students and workers across <a href=\"https://wikipedia.org/wiki/Albania\" title=\"Albania\">Albania</a> begin, which eventually trigger the <a href=\"https://wikipedia.org/wiki/Fall_of_communism_in_Albania\" title=\"Fall of communism in Albania\">fall of communism in Albania</a>.", "no_year_html": "Demonstrations by students and workers across <a href=\"https://wikipedia.org/wiki/Albania\" title=\"Albania\">Albania</a> begin, which eventually trigger the <a href=\"https://wikipedia.org/wiki/Fall_of_communism_in_Albania\" title=\"Fall of communism in Albania\">fall of communism in Albania</a>.", "links": [{"title": "Albania", "link": "https://wikipedia.org/wiki/Albania"}, {"title": "Fall of communism in Albania", "link": "https://wikipedia.org/wiki/Fall_of_communism_in_Albania"}]}, {"year": "1990", "text": "Several fatal collisions in the 1990 Interstate 75 fog disaster result in a total of 12 deaths and 42 being injured", "html": "1990 - Several fatal collisions in the <a href=\"https://wikipedia.org/wiki/1990_Interstate_75_fog_disaster\" title=\"1990 Interstate 75 fog disaster\">1990 Interstate 75 fog disaster</a> result in a total of 12 deaths and 42 being injured", "no_year_html": "Several fatal collisions in the <a href=\"https://wikipedia.org/wiki/1990_Interstate_75_fog_disaster\" title=\"1990 Interstate 75 fog disaster\">1990 Interstate 75 fog disaster</a> result in a total of 12 deaths and 42 being injured", "links": [{"title": "1990 Interstate 75 fog disaster", "link": "https://wikipedia.org/wiki/1990_Interstate_75_fog_disaster"}]}, {"year": "1993", "text": "A block of the Highland Towers condominium complex collapses following a landslide caused by heavy rain and water flowing from a construction site at Ampang district in Kuala Lumpur, Malaysia. 48 of its residents die, including one who died in hospital after being rescued alive, leaving only two survivors.", "html": "1993 - A block of the <a href=\"https://wikipedia.org/wiki/Highland_Towers_collapse\" title=\"Highland Towers collapse\">Highland Towers</a> <a href=\"https://wikipedia.org/wiki/Condominium_(living_space)\" class=\"mw-redirect\" title=\"Condominium (living space)\">condominium</a> complex collapses following a <a href=\"https://wikipedia.org/wiki/Landslide\" title=\"Landslide\">landslide</a> caused by heavy rain and water flowing from a construction site at <a href=\"https://wikipedia.org/wiki/Ampang,_Kuala_Lumpur\" title=\"Ampang, Kuala Lumpur\">Ampang</a> district in <a href=\"https://wikipedia.org/wiki/Kuala_Lumpur\" title=\"Kuala Lumpur\">Kuala Lumpur</a>, Malaysia. 48 of its residents die, including one who died in hospital after being rescued alive, leaving only two survivors.", "no_year_html": "A block of the <a href=\"https://wikipedia.org/wiki/Highland_Towers_collapse\" title=\"Highland Towers collapse\">Highland Towers</a> <a href=\"https://wikipedia.org/wiki/Condominium_(living_space)\" class=\"mw-redirect\" title=\"Condominium (living space)\">condominium</a> complex collapses following a <a href=\"https://wikipedia.org/wiki/Landslide\" title=\"Landslide\">landslide</a> caused by heavy rain and water flowing from a construction site at <a href=\"https://wikipedia.org/wiki/Ampang,_Kuala_Lumpur\" title=\"Ampang, Kuala Lumpur\">Ampang</a> district in <a href=\"https://wikipedia.org/wiki/Kuala_Lumpur\" title=\"Kuala Lumpur\">Kuala Lumpur</a>, Malaysia. 48 of its residents die, including one who died in hospital after being rescued alive, leaving only two survivors.", "links": [{"title": "Highland Towers collapse", "link": "https://wikipedia.org/wiki/Highland_Towers_collapse"}, {"title": "Condominium (living space)", "link": "https://wikipedia.org/wiki/Condominium_(living_space)"}, {"title": "Landslide", "link": "https://wikipedia.org/wiki/Landslide"}, {"title": "Ampang, Kuala Lumpur", "link": "https://wikipedia.org/wiki/Ampang,_Kuala_Lumpur"}, {"title": "Kuala Lumpur", "link": "https://wikipedia.org/wiki/Kuala_Lumpur"}]}, {"year": "1994", "text": "First Chechen War: Russian President <PERSON> orders Russian troops into Chechnya.", "html": "1994 - <a href=\"https://wikipedia.org/wiki/First_Chechen_War\" title=\"First Chechen War\">First Chechen War</a>: <a href=\"https://wikipedia.org/wiki/President_of_Russia\" title=\"President of Russia\">Russian President</a> <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> orders Russian troops into <a href=\"https://wikipedia.org/wiki/Chechnya\" title=\"Chechnya\">Chechnya</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/First_Chechen_War\" title=\"First Chechen War\">First Chechen War</a>: <a href=\"https://wikipedia.org/wiki/President_of_Russia\" title=\"President of Russia\">Russian President</a> <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> orders Russian troops into <a href=\"https://wikipedia.org/wiki/Chechnya\" title=\"Chechnya\">Chechnya</a>.", "links": [{"title": "First Chechen War", "link": "https://wikipedia.org/wiki/First_Chechen_War"}, {"title": "President of Russia", "link": "https://wikipedia.org/wiki/President_of_Russia"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}, {"title": "Chechnya", "link": "https://wikipedia.org/wiki/Chechnya"}]}, {"year": "1994", "text": "A bomb explodes on Philippine Airlines Flight 434, en route from Manila, Philippines, to Tokyo, Japan, killing one. The captain is able to land the plane safely.", "html": "1994 - A bomb explodes on <a href=\"https://wikipedia.org/wiki/Philippine_Airlines_Flight_434\" title=\"Philippine Airlines Flight 434\">Philippine Airlines Flight 434</a>, en route from <a href=\"https://wikipedia.org/wiki/Manila\" title=\"Manila\">Manila</a>, <a href=\"https://wikipedia.org/wiki/Philippines\" title=\"Philippines\">Philippines</a>, to Tokyo, Japan, killing one. The captain is able to land the plane safely.", "no_year_html": "A bomb explodes on <a href=\"https://wikipedia.org/wiki/Philippine_Airlines_Flight_434\" title=\"Philippine Airlines Flight 434\">Philippine Airlines Flight 434</a>, en route from <a href=\"https://wikipedia.org/wiki/Manila\" title=\"Manila\">Manila</a>, <a href=\"https://wikipedia.org/wiki/Philippines\" title=\"Philippines\">Philippines</a>, to Tokyo, Japan, killing one. The captain is able to land the plane safely.", "links": [{"title": "Philippine Airlines Flight 434", "link": "https://wikipedia.org/wiki/Philippine_Airlines_Flight_434"}, {"title": "Manila", "link": "https://wikipedia.org/wiki/Manila"}, {"title": "Philippines", "link": "https://wikipedia.org/wiki/Philippines"}]}, {"year": "1997", "text": "The Kyoto Protocol opens for signature.", "html": "1997 - The <a href=\"https://wikipedia.org/wiki/Kyoto_Protocol\" title=\"Kyoto Protocol\">Kyoto Protocol</a> opens for signature.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Kyoto_Protocol\" title=\"Kyoto Protocol\">Kyoto Protocol</a> opens for signature.", "links": [{"title": "Kyoto Protocol", "link": "https://wikipedia.org/wiki/Kyoto_Protocol"}]}, {"year": "1998", "text": "Thai Airways Flight 261 crashes near Surat Thani Airport, killing 101. The pilot flying the Airbus A310-200 is thought to have suffered spatial disorientation.", "html": "1998 - <a href=\"https://wikipedia.org/wiki/Thai_Airways_International_Flight_261\" title=\"Thai Airways International Flight 261\">Thai Airways Flight 261 crashes</a> near <a href=\"https://wikipedia.org/wiki/Surat_Thani_Airport\" class=\"mw-redirect\" title=\"Surat Thani Airport\">Surat Thani Airport</a>, killing 101. The pilot flying the <a href=\"https://wikipedia.org/wiki/Airbus_A310-200\" class=\"mw-redirect\" title=\"Airbus A310-200\">Airbus A310-200</a> is thought to have suffered <a href=\"https://wikipedia.org/wiki/Spatial_disorientation\" title=\"Spatial disorientation\">spatial disorientation</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Thai_Airways_International_Flight_261\" title=\"Thai Airways International Flight 261\">Thai Airways Flight 261 crashes</a> near <a href=\"https://wikipedia.org/wiki/Surat_Thani_Airport\" class=\"mw-redirect\" title=\"Surat Thani Airport\">Surat Thani Airport</a>, killing 101. The pilot flying the <a href=\"https://wikipedia.org/wiki/Airbus_A310-200\" class=\"mw-redirect\" title=\"Airbus A310-200\">Airbus A310-200</a> is thought to have suffered <a href=\"https://wikipedia.org/wiki/Spatial_disorientation\" title=\"Spatial disorientation\">spatial disorientation</a>.", "links": [{"title": "Thai Airways International Flight 261", "link": "https://wikipedia.org/wiki/Thai_Airways_International_Flight_261"}, {"title": "Surat Thani Airport", "link": "https://wikipedia.org/wiki/Surat_Thani_Airport"}, {"title": "Airbus A310-200", "link": "https://wikipedia.org/wiki/Airbus_A310-200"}, {"title": "Spatial disorientation", "link": "https://wikipedia.org/wiki/Spatial_disorientation"}]}, {"year": "1999", "text": "SATA Air Açores Flight 530M crashes into Pico da Esperança on São Jorge Island in the Azores, killing 35.", "html": "1999 - <a href=\"https://wikipedia.org/wiki/SATA_Air_A%C3%A7ores_Flight_530M\" title=\"SATA Air Açores Flight 530M\">SATA Air Açores Flight 530M</a> crashes into <a href=\"https://wikipedia.org/wiki/Pico_da_Esperan%C3%A7a\" title=\"Pico da Esperança\"><PERSON><PERSON> Esperança</a> on <a href=\"https://wikipedia.org/wiki/S%C3%A3o_Jorge_Island\" title=\"São Jorge Island\">São Jorge Island</a> in the <a href=\"https://wikipedia.org/wiki/Azores\" title=\"Azores\">Azores</a>, killing 35.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/SATA_Air_A%C3%A7ores_Flight_530M\" title=\"SATA Air Açores Flight 530M\">SATA Air Açores Flight 530M</a> crashes into <a href=\"https://wikipedia.org/wiki/Pico_da_Esperan%C3%A7a\" title=\"Pico da Esperança\"><PERSON><PERSON> da Esperança</a> on <a href=\"https://wikipedia.org/wiki/S%C3%A3o_Jorge_Island\" title=\"São Jorge Island\">São Jorge Island</a> in the <a href=\"https://wikipedia.org/wiki/Azores\" title=\"Azores\">Azores</a>, killing 35.", "links": [{"title": "SATA Air Açores Flight 530M", "link": "https://wikipedia.org/wiki/SATA_Air_A%C3%A7ores_Flight_530M"}, {"title": "Pico da Esperança", "link": "https://wikipedia.org/wiki/Pico_da_Esperan%C3%A7a"}, {"title": "São Jorge Island", "link": "https://wikipedia.org/wiki/S%C3%A3o_Jorge_Island"}, {"title": "Azores", "link": "https://wikipedia.org/wiki/Azores"}]}, {"year": "2001", "text": "China joins the World Trade Organization (WTO).", "html": "2001 - China joins the <a href=\"https://wikipedia.org/wiki/World_Trade_Organization\" title=\"World Trade Organization\">World Trade Organization</a> (WTO).", "no_year_html": "China joins the <a href=\"https://wikipedia.org/wiki/World_Trade_Organization\" title=\"World Trade Organization\">World Trade Organization</a> (WTO).", "links": [{"title": "World Trade Organization", "link": "https://wikipedia.org/wiki/World_Trade_Organization"}]}, {"year": "2005", "text": "The Buncefield Oil Depot catches fire in Hemel Hempstead, England.", "html": "2005 - The <a href=\"https://wikipedia.org/wiki/Buncefield_fire\" title=\"Buncefield fire\">Buncefield Oil Depot catches fire</a> in <a href=\"https://wikipedia.org/wiki/Hemel_Hempstead\" title=\"Hemel Hempstead\"><PERSON><PERSON> Hempstead</a>, England.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Buncefield_fire\" title=\"Buncefield fire\">Buncefield Oil Depot catches fire</a> in <a href=\"https://wikipedia.org/wiki/Hemel_Hempstead\" title=\"Hemel Hempstead\"><PERSON><PERSON> He<PERSON>tead</a>, England.", "links": [{"title": "Buncefield fire", "link": "https://wikipedia.org/wiki/B<PERSON><PERSON><PERSON>_fire"}, {"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Hemel_Hempstead"}]}, {"year": "2005", "text": "Cronulla riots: Thousands of White Australians demonstrate against ethnic violence resulting in a riot against anyone thought to be Lebanese in Cronulla, New South Wales; these are followed up by retaliatory ethnic attacks on Cronulla.", "html": "2005 - <a href=\"https://wikipedia.org/wiki/2005_Cronulla_riots\" title=\"2005 Cronulla riots\">Cronulla riots</a>: Thousands of White Australians demonstrate against ethnic violence resulting in a riot against anyone thought to be <a href=\"https://wikipedia.org/wiki/Lebanon\" title=\"Lebanon\">Lebanese</a> in <a href=\"https://wikipedia.org/wiki/Cronulla,_New_South_Wales\" title=\"Cronulla, New South Wales\">Cronulla, New South Wales</a>; these are followed up by retaliatory ethnic attacks on Cronulla.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/2005_Cronulla_riots\" title=\"2005 Cronulla riots\">Cronulla riots</a>: Thousands of White Australians demonstrate against ethnic violence resulting in a riot against anyone thought to be <a href=\"https://wikipedia.org/wiki/Lebanon\" title=\"Lebanon\">Lebanese</a> in <a href=\"https://wikipedia.org/wiki/Cronulla,_New_South_Wales\" title=\"Cronulla, New South Wales\">Cronulla, New South Wales</a>; these are followed up by retaliatory ethnic attacks on Cronulla.", "links": [{"title": "2005 Cronulla riots", "link": "https://wikipedia.org/wiki/2005_Cronulla_riots"}, {"title": "Lebanon", "link": "https://wikipedia.org/wiki/Lebanon"}, {"title": "Cronulla, New South Wales", "link": "https://wikipedia.org/wiki/Cronulla,_New_South_Wales"}]}, {"year": "2006", "text": "The International Conference to Review the Global Vision of the Holocaust is opened in Tehran, Iran, by then-president <PERSON><PERSON><PERSON>; nations such as Israel and the United States express concern.", "html": "2006 - The <a href=\"https://wikipedia.org/wiki/International_Conference_to_Review_the_Global_Vision_of_the_Holocaust\" title=\"International Conference to Review the Global Vision of the Holocaust\">International Conference to Review the Global Vision of the Holocaust</a> is opened in <a href=\"https://wikipedia.org/wiki/Tehran\" title=\"Tehran\">Tehran</a>, <a href=\"https://wikipedia.org/wiki/Iran\" title=\"Iran\">Iran</a>, by then-president <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>; nations such as Israel and the United States express concern.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/International_Conference_to_Review_the_Global_Vision_of_the_Holocaust\" title=\"International Conference to Review the Global Vision of the Holocaust\">International Conference to Review the Global Vision of the Holocaust</a> is opened in <a href=\"https://wikipedia.org/wiki/Tehran\" title=\"Tehran\">Tehran</a>, <a href=\"https://wikipedia.org/wiki/Iran\" title=\"Iran\">Iran</a>, by then-president <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>; nations such as Israel and the United States express concern.", "links": [{"title": "International Conference to Review the Global Vision of the Holocaust", "link": "https://wikipedia.org/wiki/International_Conference_to_Review_the_Global_Vision_of_the_Holocaust"}, {"title": "Tehran", "link": "https://wikipedia.org/wiki/Tehran"}, {"title": "Iran", "link": "https://wikipedia.org/wiki/Iran"}, {"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "2006", "text": "<PERSON>, the President of Mexico, launches a military-led offensive to put down the drug cartel violence in the state of Michoacán. This effort is often regarded as the first event in the Mexican Drug War.", "html": "2006 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%B3n\" title=\"<PERSON>\"><PERSON></a>, the <a href=\"https://wikipedia.org/wiki/President_of_Mexico\" title=\"President of Mexico\">President of Mexico</a>, launches a <a href=\"https://wikipedia.org/wiki/Operation_Michoac%C3%A1n\" title=\"Operation Michoacán\">military-led offensive</a> to put down the drug cartel violence in the state of <a href=\"https://wikipedia.org/wiki/Michoac%C3%A1n\" title=\"Michoacán\">Michoacán</a>. This effort is often regarded as the first event in the <a href=\"https://wikipedia.org/wiki/Mexican_Drug_War\" class=\"mw-redirect\" title=\"Mexican Drug War\">Mexican Drug War</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%B3n\" title=\"<PERSON>\"><PERSON></a>, the <a href=\"https://wikipedia.org/wiki/President_of_Mexico\" title=\"President of Mexico\">President of Mexico</a>, launches a <a href=\"https://wikipedia.org/wiki/Operation_Michoac%C3%A1n\" title=\"Operation Michoacán\">military-led offensive</a> to put down the drug cartel violence in the state of <a href=\"https://wikipedia.org/wiki/Michoac%C3%A1n\" title=\"Michoacán\">Michoacán</a>. This effort is often regarded as the first event in the <a href=\"https://wikipedia.org/wiki/Mexican_Drug_War\" class=\"mw-redirect\" title=\"Mexican Drug War\">Mexican Drug War</a>.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Felipe_Calder%C3%B3n"}, {"title": "President of Mexico", "link": "https://wikipedia.org/wiki/President_of_Mexico"}, {"title": "Operation Michoacán", "link": "https://wikipedia.org/wiki/Operation_Michoac%C3%A1n"}, {"title": "Michoacán", "link": "https://wikipedia.org/wiki/Michoac%C3%A1n"}, {"title": "Mexican Drug War", "link": "https://wikipedia.org/wiki/Mexican_Drug_War"}]}, {"year": "2007", "text": "Insurgency in the Maghreb: Two car bombs explode in Algiers, Algeria, one near the Supreme Constitutional Court and the other near the offices of the United Nations.", "html": "2007 - <a href=\"https://wikipedia.org/wiki/Insurgency_in_the_Maghreb_(2002%E2%80%93present)\" title=\"Insurgency in the Maghreb (2002-present)\">Insurgency in the Maghreb</a>: <a href=\"https://wikipedia.org/wiki/December_11,_2007_Algiers_bombings\" class=\"mw-redirect\" title=\"December 11, 2007 Algiers bombings\">Two car bombs explode</a> in <a href=\"https://wikipedia.org/wiki/Algiers\" title=\"Algiers\">Algiers</a>, <a href=\"https://wikipedia.org/wiki/Algeria\" title=\"Algeria\">Algeria</a>, one near the Supreme Constitutional Court and the other near the offices of the United Nations.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Insurgency_in_the_Maghreb_(2002%E2%80%93present)\" title=\"Insurgency in the Maghreb (2002-present)\">Insurgency in the Maghreb</a>: <a href=\"https://wikipedia.org/wiki/December_11,_2007_Algiers_bombings\" class=\"mw-redirect\" title=\"December 11, 2007 Algiers bombings\">Two car bombs explode</a> in <a href=\"https://wikipedia.org/wiki/Algiers\" title=\"Algiers\">Algiers</a>, <a href=\"https://wikipedia.org/wiki/Algeria\" title=\"Algeria\">Algeria</a>, one near the Supreme Constitutional Court and the other near the offices of the United Nations.", "links": [{"title": "Insurgency in the Maghreb (2002-present)", "link": "https://wikipedia.org/wiki/Insurgency_in_the_Maghreb_(2002%E2%80%93present)"}, {"title": "December 11, 2007 Algiers bombings", "link": "https://wikipedia.org/wiki/December_11,_2007_Algiers_bombings"}, {"title": "Algiers", "link": "https://wikipedia.org/wiki/Algiers"}, {"title": "Algeria", "link": "https://wikipedia.org/wiki/Algeria"}]}, {"year": "2008", "text": "<PERSON> is arrested and charged with securities fraud in a $50 billion Ponzi scheme.", "html": "2008 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> is arrested and charged with securities fraud in a $50 billion <a href=\"https://wikipedia.org/wiki/Ponzi_scheme\" title=\"Ponzi scheme\">Ponzi scheme</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Madoff\"><PERSON></a> is arrested and charged with securities fraud in a $50 billion <a href=\"https://wikipedia.org/wiki/Ponzi_scheme\" title=\"Ponzi scheme\">Ponzi scheme</a>.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Ponzi scheme", "link": "https://wikipedia.org/wiki/Ponzi_scheme"}]}, {"year": "2009", "text": "Finnish game developer Rovio Entertainment releases the hit mobile game Angry Birds internationally on iOS.", "html": "2009 - Finnish game developer <a href=\"https://wikipedia.org/wiki/Rovio_Entertainment\" title=\"Rovio Entertainment\">Rovio Entertainment</a> releases the hit mobile game <i><a href=\"https://wikipedia.org/wiki/Angry_Birds_(video_game)\" title=\"Angry Birds (video game)\">Angry Birds</a></i> internationally on <a href=\"https://wikipedia.org/wiki/IOS\" title=\"IOS\">iOS</a>.", "no_year_html": "Finnish game developer <a href=\"https://wikipedia.org/wiki/Rovio_Entertainment\" title=\"Rovio Entertainment\">Rovio Entertainment</a> releases the hit mobile game <i><a href=\"https://wikipedia.org/wiki/Angry_Birds_(video_game)\" title=\"Angry Birds (video game)\">Angry Birds</a></i> internationally on <a href=\"https://wikipedia.org/wiki/IOS\" title=\"IOS\">iOS</a>.", "links": [{"title": "Rovio Entertainment", "link": "https://wikipedia.org/wiki/Rovio_Entertainment"}, {"title": "Angry Birds (video game)", "link": "https://wikipedia.org/wiki/Angry_Birds_(video_game)"}, {"title": "IOS", "link": "https://wikipedia.org/wiki/IOS"}]}, {"year": "2012", "text": "At least 125 people are killed and up to 200 injured in bombings in the Alawite village of Aqrab, Syria.", "html": "2012 - At least 125 people are killed and up to 200 injured in <a href=\"https://wikipedia.org/wiki/Aqrab_massacre\" title=\"Aqrab massacre\">bombings</a> in the <a href=\"https://wikipedia.org/wiki/Alawite\" class=\"mw-redirect\" title=\"Alawite\">Alawite</a> village of <a href=\"https://wikipedia.org/wiki/Aqrab\" title=\"Aqrab\">Aqrab</a>, <a href=\"https://wikipedia.org/wiki/Syria\" title=\"Syria\">Syria</a>.", "no_year_html": "At least 125 people are killed and up to 200 injured in <a href=\"https://wikipedia.org/wiki/Aqrab_massacre\" title=\"Aqrab massacre\">bombings</a> in the <a href=\"https://wikipedia.org/wiki/Alawite\" class=\"mw-redirect\" title=\"Alawite\">Alawite</a> village of <a href=\"https://wikipedia.org/wiki/Aqrab\" title=\"Aqrab\">Aqrab</a>, <a href=\"https://wikipedia.org/wiki/Syria\" title=\"Syria\">Syria</a>.", "links": [{"title": "<PERSON><PERSON>rab massacre", "link": "https://wikipedia.org/wiki/A<PERSON><PERSON>_massacre"}, {"title": "Alawite", "link": "https://wikipedia.org/wiki/Alawite"}, {"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Aqrab"}, {"title": "Syria", "link": "https://wikipedia.org/wiki/Syria"}]}, {"year": "2017", "text": "New York City Subway bombing: A pipe bomb partially detonates in the New York City Subway, in the Times Square-42nd Street/Port Authority Bus Terminal. Four people are injured, including the perpetrator.", "html": "2017 - <a href=\"https://wikipedia.org/wiki/2017_New_York_City_Subway_bombing\" title=\"2017 New York City Subway bombing\">New York City Subway bombing</a>: A pipe bomb partially detonates in the <a href=\"https://wikipedia.org/wiki/New_York_City_Subway\" title=\"New York City Subway\">New York City Subway</a>, in the <a href=\"https://wikipedia.org/wiki/Times_Square%E2%80%9342nd_Street/Port_Authority_Bus_Terminal_(New_York_City_Subway)\" class=\"mw-redirect\" title=\"Times Square-42nd Street/Port Authority Bus Terminal (New York City Subway)\">Times Square-42nd Street/Port Authority Bus Terminal</a>. Four people are injured, including the perpetrator.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/2017_New_York_City_Subway_bombing\" title=\"2017 New York City Subway bombing\">New York City Subway bombing</a>: A pipe bomb partially detonates in the <a href=\"https://wikipedia.org/wiki/New_York_City_Subway\" title=\"New York City Subway\">New York City Subway</a>, in the <a href=\"https://wikipedia.org/wiki/Times_Square%E2%80%9342nd_Street/Port_Authority_Bus_Terminal_(New_York_City_Subway)\" class=\"mw-redirect\" title=\"Times Square-42nd Street/Port Authority Bus Terminal (New York City Subway)\">Times Square-42nd Street/Port Authority Bus Terminal</a>. Four people are injured, including the perpetrator.", "links": [{"title": "2017 New York City Subway bombing", "link": "https://wikipedia.org/wiki/2017_New_York_City_Subway_bombing"}, {"title": "New York City Subway", "link": "https://wikipedia.org/wiki/New_York_City_Subway"}, {"title": "Times Square-42nd Street/Port Authority Bus Terminal (New York City Subway)", "link": "https://wikipedia.org/wiki/Times_Square%E2%80%9342nd_Street/Port_Authority_Bus_Terminal_(New_York_City_Subway)"}]}, {"year": "2019", "text": "The results of the 2019 Bougainvillean independence referendum are announced. The results are overwhelmingly one-sided. Over 98% of voters vote for Bougainville's independence.", "html": "2019 - The results of the <a href=\"https://wikipedia.org/wiki/2019_Bougainvillean_independence_referendum\" title=\"2019 Bougainvillean independence referendum\">2019 Bougainvillean independence referendum</a> are announced. The results are overwhelmingly one-sided. Over 98% of voters vote for <a href=\"https://wikipedia.org/wiki/Autonomous_Region_of_Bougainville\" title=\"Autonomous Region of Bougainville\">Bougainville</a>'s independence.", "no_year_html": "The results of the <a href=\"https://wikipedia.org/wiki/2019_Bougainvillean_independence_referendum\" title=\"2019 Bougainvillean independence referendum\">2019 Bougainvillean independence referendum</a> are announced. The results are overwhelmingly one-sided. Over 98% of voters vote for <a href=\"https://wikipedia.org/wiki/Autonomous_Region_of_Bougainville\" title=\"Autonomous Region of Bougainville\">Bougainville</a>'s independence.", "links": [{"title": "2019 Bougainvillean independence referendum", "link": "https://wikipedia.org/wiki/2019_Bougainvillean_independence_referendum"}, {"title": "Autonomous Region of Bougainville", "link": "https://wikipedia.org/wiki/Autonomous_Region_of_Bougainville"}]}, {"year": "2020", "text": "The Food and Drug Administration issues an Emergency Use Authorization on the Pfizer-BioNTech COVID-19 vaccine, the first COVID-19 vaccine to be approved by the agency.", "html": "2020 - The <a href=\"https://wikipedia.org/wiki/Food_and_Drug_Administration\" title=\"Food and Drug Administration\">Food and Drug Administration</a> issues an <a href=\"https://wikipedia.org/wiki/Emergency_Use_Authorization\" title=\"Emergency Use Authorization\">Emergency Use Authorization</a> on the <a href=\"https://wikipedia.org/wiki/Pfizer%E2%80%93BioNTech_COVID-19_vaccine\" title=\"Pfizer-BioNTech COVID-19 vaccine\">Pfizer-BioNTech COVID-19 vaccine</a>, the first <a href=\"https://wikipedia.org/wiki/COVID-19_vaccine\" title=\"COVID-19 vaccine\">COVID-19 vaccine</a> to be approved by the agency.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Food_and_Drug_Administration\" title=\"Food and Drug Administration\">Food and Drug Administration</a> issues an <a href=\"https://wikipedia.org/wiki/Emergency_Use_Authorization\" title=\"Emergency Use Authorization\">Emergency Use Authorization</a> on the <a href=\"https://wikipedia.org/wiki/Pfizer%E2%80%93BioNTech_COVID-19_vaccine\" title=\"Pfizer-BioNTech COVID-19 vaccine\">Pfizer-BioNTech COVID-19 vaccine</a>, the first <a href=\"https://wikipedia.org/wiki/COVID-19_vaccine\" title=\"COVID-19 vaccine\">COVID-19 vaccine</a> to be approved by the agency.", "links": [{"title": "Food and Drug Administration", "link": "https://wikipedia.org/wiki/Food_and_Drug_Administration"}, {"title": "Emergency Use Authorization", "link": "https://wikipedia.org/wiki/Emergency_Use_Authorization"}, {"title": "Pfizer-BioNTech COVID-19 vaccine", "link": "https://wikipedia.org/wiki/Pfizer%E2%80%93BioNTech_COVID-19_vaccine"}, {"title": "COVID-19 vaccine", "link": "https://wikipedia.org/wiki/COVID-19_vaccine"}]}], "Births": [{"year": "1445", "text": "<PERSON><PERSON><PERSON>, Duke of Württemberg (d. 1496)", "html": "1445 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>,_Duke_of_W%C3%BCrttemberg\" title=\"<PERSON><PERSON><PERSON>, Duke of Württemberg\"><PERSON><PERSON><PERSON>, Duke of Württemberg</a> (d. 1496)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>,_Duke_of_W%C3%BCrttemberg\" title=\"<PERSON><PERSON><PERSON>, Duke of Württemberg\"><PERSON><PERSON><PERSON>, Duke of Württemberg</a> (d. 1496)", "links": [{"title": "<PERSON><PERSON><PERSON>, Duke of Württemberg", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>,_Duke_of_W%C3%BCrttemberg"}]}, {"year": "1465", "text": "<PERSON><PERSON><PERSON>, Japanese shogun (d. 1489)", "html": "1465 - <a href=\"https://wikipedia.org/wiki/Ash<PERSON><PERSON>_Yoshihisa\" title=\"Ashika<PERSON> Yoshihisa\"><PERSON><PERSON><PERSON></a>, Japanese shogun (d. 1489)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_Yoshi<PERSON>a\" title=\"Ash<PERSON><PERSON> Yoshihisa\"><PERSON><PERSON><PERSON></a>, Japanese shogun (d. 1489)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON><PERSON>a"}]}, {"year": "1475", "text": "<PERSON> (d. 1521)", "html": "1475 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"Pope <PERSON>\">Pope <PERSON></a> (d. 1521)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"Pope <PERSON>\">Pope <PERSON></a> (d. 1521)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1566", "text": "<PERSON>, Portuguese organist and composer (d. 1650)", "html": "1566 - <a href=\"https://wikipedia.org/wiki/<PERSON>_(composer)\" title=\"<PERSON> (composer)\"><PERSON></a>, Portuguese organist and composer (d. 1650)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(composer)\" title=\"<PERSON> (composer)\"><PERSON></a>, Portuguese organist and composer (d. 1650)", "links": [{"title": "<PERSON> (composer)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(composer)"}]}, {"year": "1595", "text": "<PERSON><PERSON>, Korean politician, poet and scholar (d. 1682)", "html": "1595 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Korean politician, poet and scholar (d. 1682)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Korean politician, poet and scholar (d. 1682)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>"}]}, {"year": "1613", "text": "<PERSON>, Rajput nobleman (d. 1644)", "html": "1613 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Rajput nobleman (d. 1644)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Rajput nobleman (d. 1644)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1712", "text": "<PERSON>, Italian poet, philosopher, and critic (d. 1764)", "html": "1712 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian poet, philosopher, and critic (d. 1764)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian poet, philosopher, and critic (d. 1764)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1725", "text": "<PERSON>, American lawyer and politician (d. 1792)", "html": "1725 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and politician (d. 1792)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and politician (d. 1792)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1758", "text": "<PERSON>, German composer, conductor, and educator (d. 1832)", "html": "1758 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German composer, conductor, and educator (d. 1832)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German composer, conductor, and educator (d. 1832)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1761", "text": "<PERSON><PERSON>, Italian physicist, economist, and jurist (d. 1835)", "html": "1761 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Italian physicist, economist, and jurist (d. 1835)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON> <PERSON></a>, Italian physicist, economist, and jurist (d. 1835)", "links": [{"title": "<PERSON><PERSON> <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1781", "text": "<PERSON>, Scottish physicist, mathematician, and astronomer (d. 1868)", "html": "1781 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish physicist, mathematician, and astronomer (d. 1868)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish physicist, mathematician, and astronomer (d. 1868)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1801", "text": "<PERSON>, German poet and playwright (d. 1836)", "html": "1801 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"Christian <PERSON>\"><PERSON></a>, German poet and playwright (d. 1836)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"Christian <PERSON>\"><PERSON></a>, German poet and playwright (d. 1836)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1803", "text": "<PERSON>, French composer, conductor, and critic (d. 1869)", "html": "1803 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French composer, conductor, and critic (d. 1869)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French composer, conductor, and critic (d. 1869)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1810", "text": "<PERSON>, French dramatist, poet, and novelist (d. 1857)", "html": "1810 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French dramatist, poet, and novelist (d. 1857)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French dramatist, poet, and novelist (d. 1857)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1830", "text": "<PERSON><PERSON><PERSON><PERSON> of Hawaii (d. 1872)", "html": "1830 - <a href=\"https://wikipedia.org/wiki/Kamehameha_V\" title=\"Kamehameha V\">Kamehameha V</a> of Hawaii (d. 1872)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Kamehameha_V\" title=\"Kamehameha V\">Kamehameha V</a> of Hawaii (d. 1872)", "links": [{"title": "Kamehameha V", "link": "https://wikipedia.org/wiki/Kamehameha_V"}]}, {"year": "1837", "text": "<PERSON>, English civil engineer (d. 1887)", "html": "1837 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English civil engineer (d. 1887)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English civil engineer (d. 1887)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1838", "text": "<PERSON>, Canadian brewer and businessman (d. 1915)", "html": "1838 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian brewer and businessman (d. 1915)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian brewer and businessman (d. 1915)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1843", "text": "<PERSON>, German microbiologist and physician, Nobel Prize laureate (d. 1910)", "html": "1843 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German microbiologist and physician, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Physiology_or_Medicine\" title=\"Nobel Prize in Physiology or Medicine\">Nobel Prize</a> laureate (d. 1910)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German microbiologist and physician, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Physiology_or_Medicine\" title=\"Nobel Prize in Physiology or Medicine\">Nobel Prize</a> laureate (d. 1910)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Nobel Prize in Physiology or Medicine", "link": "https://wikipedia.org/wiki/Nobel_Prize_in_Physiology_or_Medicine"}]}, {"year": "1856", "text": "<PERSON><PERSON>, Russian philosopher, theorist, and author (d. 1918)", "html": "1856 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Russian philosopher, theorist, and author (d. 1918)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Russian philosopher, theorist, and author (d. 1918)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1858", "text": "<PERSON>-<PERSON><PERSON>, Russian director, producer, and playwright (d. 1943)", "html": "1858 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian director, producer, and playwright (d. 1943)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian director, producer, and playwright (d. 1943)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>"}]}, {"year": "1861", "text": "<PERSON>, 5th Baron <PERSON>, British Army officer and Anglo-Irish peer (d. 1923)", "html": "1861 - <a href=\"https://wikipedia.org/wiki/<PERSON>,_5th_Baron_<PERSON>\" title=\"<PERSON>, 5th Baron <PERSON>\"><PERSON>, 5th Baron <PERSON></a>, British Army officer and Anglo-Irish peer (d. 1923)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>,_5th_Baron_<PERSON>\" title=\"<PERSON>, 5th Baron <PERSON>\"><PERSON>, 5th Baron <PERSON></a>, British Army officer and Anglo-Irish peer (d. 1923)", "links": [{"title": "<PERSON>, 5th Baron <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_5th_Baron_<PERSON>"}]}, {"year": "1863", "text": "<PERSON>, American astronomer and academic (d. 1941)", "html": "1863 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"Annie Jump Cannon\"><PERSON></a>, American astronomer and academic (d. 1941)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"Annie Jump Cannon\"><PERSON></a>, American astronomer and academic (d. 1941)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1872", "text": "<PERSON>, British illustrator and photographer (d. 1942)", "html": "1872 - <a href=\"https://wikipedia.org/wiki/Ren%C3%A9_Bull\" title=\"<PERSON>\"><PERSON></a>, British illustrator and photographer (d. 1942)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Ren%C3%A9_Bull\" title=\"<PERSON>\"><PERSON></a>, British illustrator and photographer (d. 1942)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Ren%C3%A9_Bull"}]}, {"year": "1873", "text": "<PERSON><PERSON><PERSON>, Slovenian mathematician and academic (d. 1967)", "html": "1873 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Slovenian mathematician and academic (d. 1967)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Slovenian mathematician and academic (d. 1967)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1875", "text": "<PERSON><PERSON><PERSON>, Moldovan-Israeli rabbi and politician (d. 1962)", "html": "1875 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Moldovan-Israeli rabbi and politician (d. 1962)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Moldovan-Israeli rabbi and politician (d. 1962)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "1880", "text": "<PERSON>, Australian cricketer and umpire (d. 1951)", "html": "1880 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian cricketer and umpire (d. 1951)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian cricketer and umpire (d. 1951)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1882", "text": "<PERSON><PERSON><PERSON>, Indian journalist and poet (d. 1921)", "html": "1882 - <a href=\"https://wikipedia.org/wiki/Subramania_Bharati\" title=\"Subramania Bharati\">Subramania Bharati</a>, Indian journalist and poet (d. 1921)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Subramania_Bharati\" title=\"Subramania Bharati\"><PERSON>rama<PERSON> B<PERSON></a>, Indian journalist and poet (d. 1921)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Subramania_Bharati"}]}, {"year": "1882", "text": "<PERSON>, German physicist and mathematician, Nobel Prize laureate (d. 1970)", "html": "1882 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Born\"><PERSON></a>, German physicist and mathematician, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Physics\" title=\"Nobel Prize in Physics\">Nobel Prize</a> laureate (d. 1970)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_Born\" title=\"Max Born\"><PERSON></a>, German physicist and mathematician, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Physics\" title=\"Nobel Prize in Physics\">Nobel Prize</a> laureate (d. 1970)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Nobel Prize in Physics", "link": "https://wikipedia.org/wiki/Nobel_Prize_in_Physics"}]}, {"year": "1882", "text": "<PERSON><PERSON><PERSON>, American lawyer and politician, 99th Mayor of New York City (d. 1947)", "html": "1882 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_H._La_Guardia\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON> H. La Guardia\"><PERSON><PERSON><PERSON> <PERSON></a>, American lawyer and politician, 99th <a href=\"https://wikipedia.org/wiki/Mayor_of_New_York_City\" title=\"Mayor of New York City\">Mayor of New York City</a> (d. 1947)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_H._La_Guardia\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON> H. La Guardia\"><PERSON><PERSON><PERSON> <PERSON></a>, American lawyer and politician, 99th <a href=\"https://wikipedia.org/wiki/Mayor_of_New_York_City\" title=\"Mayor of New York City\">Mayor of New York City</a> (d. 1947)", "links": [{"title": "Fi<PERSON>llo H. La Guardia", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>._La_Guardia"}, {"title": "Mayor of New York City", "link": "https://wikipedia.org/wiki/Mayor_of_New_York_City"}]}, {"year": "1884", "text": "<PERSON><PERSON>, Dutch swimmer and water polo player (d. 1961)", "html": "1884 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Dutch swimmer and water polo player (d. 1961)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Dutch swimmer and water polo player (d. 1961)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Piet_Ooms"}]}, {"year": "1889", "text": "<PERSON>, American farmer and businessman, founded K<PERSON><PERSON>'s Berry Farm (d. 1981)", "html": "1889 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American farmer and businessman, founded <a href=\"https://wikipedia.org/wiki/Knott%27s_Berry_Farm\" title=\"Knott's Berry Farm\">Knott's Berry Farm</a> (d. 1981)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American farmer and businessman, founded <a href=\"https://wikipedia.org/wiki/Knott%27s_Berry_Farm\" title=\"Knott's Berry Farm\">Knott's Berry Farm</a> (d. 1981)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Knott's Berry Farm", "link": "https://wikipedia.org/wiki/Knott%27s_Berry_Farm"}]}, {"year": "1890", "text": "<PERSON>, French-Argentinian singer-songwriter and actor (d. 1935)", "html": "1890 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French-Argentinian singer-songwriter and actor (d. 1935)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French-Argentinian singer-songwriter and actor (d. 1935)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1890", "text": "<PERSON>, American-Swiss painter and educator (d. 1976)", "html": "1890 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American-Swiss painter and educator (d. 1976)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American-Swiss painter and educator (d. 1976)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1892", "text": "<PERSON>, Finnish military hero of Polish descent (d. 1942)", "html": "1892 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Finnish military hero of Polish descent (d. 1942)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Finnish military hero of Polish descent (d. 1942)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1893", "text": "<PERSON>, Russian-American pianist and composer (d. 2002)", "html": "1893 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian-American pianist and composer (d. 2002)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian-American pianist and composer (d. 2002)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1897", "text": "<PERSON>, English soldier (d. 1977)", "html": "1897 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English soldier (d. 1977)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English soldier (d. 1977)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1899", "text": "<PERSON>, Argentinian violinist, composer, and conductor (d. 1980)", "html": "1899 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Argentinian violinist, composer, and conductor (d. 1980)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Argentinian violinist, composer, and conductor (d. 1980)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1900", "text": "<PERSON><PERSON><PERSON><PERSON>, Czechoslovakian animator, screenwriter, and film director (d. 1993)", "html": "1900 - <a href=\"https://wikipedia.org/wiki/Herm%C3%ADna_T%C3%BDrlov%C3%A1\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Czechoslovakian animator, screenwriter, and film director (d. 1993)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Herm%C3%ADna_T%C3%BDrlov%C3%A1\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Czechoslovakian animator, screenwriter, and film director (d. 1993)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Herm%C3%ADna_T%C3%BDrlov%C3%A1"}]}, {"year": "1900", "text": "<PERSON><PERSON>, German Modernist artist, co-creator of Isotype (d. 1988)", "html": "1900 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, German Modernist artist, co-creator of <a href=\"https://wikipedia.org/wiki/Isotype_(picture_language)\" title=\"Isotype (picture language)\">Isotype</a> (d. 1988)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, German Modernist artist, co-creator of <a href=\"https://wikipedia.org/wiki/Isotype_(picture_language)\" title=\"Isotype (picture language)\">Isotype</a> (d. 1988)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}, {"title": "Isotype (picture language)", "link": "https://wikipedia.org/wiki/Isotype_(picture_language)"}]}, {"year": "1904", "text": "<PERSON><PERSON>, American cartoonist (d. 1993)", "html": "1904 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_(cartoonist)\" title=\"<PERSON><PERSON> (cartoonist)\"><PERSON><PERSON></a>, American cartoonist (d. 1993)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_(cartoonist)\" title=\"<PERSON><PERSON> (cartoonist)\"><PERSON><PERSON></a>, American cartoonist (d. 1993)", "links": [{"title": "<PERSON><PERSON> (cartoonist)", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_(cartoonist)"}]}, {"year": "1905", "text": "<PERSON>, English farmer, author, and broadcaster (d. 1967)", "html": "1905 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English farmer, author, and broadcaster (d. 1967)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English farmer, author, and broadcaster (d. 1967)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1905", "text": "<PERSON>, Mexican-American actor and singer (d. 1994)", "html": "1905 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Mexican-American actor and singer (d. 1994)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Mexican-American actor and singer (d. 1994)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1908", "text": "<PERSON>, American composer and academic (d. 2012)", "html": "1908 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American composer and academic (d. 2012)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American composer and academic (d. 2012)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1908", "text": "<PERSON><PERSON><PERSON>, Portuguese actor, director, producer, and screenwriter (d. 2015)", "html": "1908 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Portuguese actor, director, producer, and screenwriter (d. 2015)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Portuguese actor, director, producer, and screenwriter (d. 2015)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1908", "text": "<PERSON><PERSON><PERSON>, Faroese educator and politician, fourth Prime Minister of the Faroe Islands (d. 1987)", "html": "1908 - <a href=\"https://wikipedia.org/wiki/H%C3%A1kun_Djurhuus\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Faroese educator and politician, fourth <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_the_Faroe_Islands\" class=\"mw-redirect\" title=\"Prime Minister of the Faroe Islands\">Prime Minister of the Faroe Islands</a> (d. 1987)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/H%C3%A1kun_Dju<PERSON>huus\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Faroese educator and politician, fourth <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_the_Faroe_Islands\" class=\"mw-redirect\" title=\"Prime Minister of the Faroe Islands\">Prime Minister of the Faroe Islands</a> (d. 1987)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/H%C3%A1kun_Djurhuus"}, {"title": "Prime Minister of the Faroe Islands", "link": "https://wikipedia.org/wiki/Prime_Minister_of_the_Faroe_Islands"}]}, {"year": "1908", "text": "<PERSON><PERSON>, Austrian Nazi war criminal (d. 1946)", "html": "1908 - <a href=\"https://wikipedia.org/wiki/Amon_G%C3%B6th\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Austrian Nazi war criminal (d. 1946)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Amon_G%C3%B6th\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Austrian Nazi war criminal (d. 1946)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Amon_G%C3%B6th"}]}, {"year": "1909", "text": "<PERSON>, Australian soldier, journalist, and author (d. 1991)", "html": "1909 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian soldier, journalist, and author (d. 1991)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian soldier, journalist, and author (d. 1991)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1910", "text": "<PERSON><PERSON>, Native American chairwoman and educator (d. 1997)", "html": "1910 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Native American chairwoman and educator (d. 1997)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Native American chairwoman and educator (d. 1997)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1911", "text": "<PERSON>, English-American director, producer, screenwriter, and composer (d. 2006)", "html": "1911 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Guest\"><PERSON></a>, English-American director, producer, screenwriter, and composer (d. 2006)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Guest\"><PERSON></a>, English-American director, producer, screenwriter, and composer (d. 2006)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1911", "text": "<PERSON><PERSON><PERSON>, Egyptian author, playwright, and screenwriter, Nobel Prize laureate (d. 2006)", "html": "1911 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Egyptian author, playwright, and screenwriter, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Literature\" title=\"Nobel Prize in Literature\">Nobel Prize</a> laureate (d. 2006)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Egyptian author, playwright, and screenwriter, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Literature\" title=\"Nobel Prize in Literature\">Nobel Prize</a> laureate (d. 2006)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}, {"title": "Nobel Prize in Literature", "link": "https://wikipedia.org/wiki/Nobel_Prize_in_Literature"}]}, {"year": "1911", "text": "<PERSON><PERSON>, Chinese aerodynamicist and academic (d. 2009)", "html": "1911 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Chinese aerodynamicist and academic (d. 2009)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Chinese aerodynamicist and academic (d. 2009)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1912", "text": "<PERSON>, Italian-Swiss film producer (d. 2007)", "html": "1912 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian-Swiss film producer (d. 2007)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian-Swiss film producer (d. 2007)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1913", "text": "<PERSON>, French actor and director (d. 1998)", "html": "1913 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French actor and director (d. 1998)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French actor and director (d. 1998)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1916", "text": "<PERSON>, Mexican author and playwright (d. 1998)", "html": "1916 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Mexican author and playwright (d. 1998)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Mexican author and playwright (d. 1998)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1916", "text": "<PERSON>, Cuban-Mexican singer-songwriter, pianist, and bandleader (d. 1989)", "html": "1916 - <a href=\"https://wikipedia.org/wiki/P%C3%A9rez_Prado\" title=\"<PERSON>\"><PERSON></a>, Cuban-Mexican singer-songwriter, pianist, and bandleader (d. 1989)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/P%C3%A9rez_Prado\" title=\"<PERSON>\"><PERSON></a>, Cuban-Mexican singer-songwriter, pianist, and bandleader (d. 1989)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/P%C3%A9rez_Prado"}]}, {"year": "1918", "text": "<PERSON>, American painter and historian (d. 2002)", "html": "1918 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American painter and historian (d. 2002)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American painter and historian (d. 2002)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1918", "text": "<PERSON>, Russian novelist, historian, and short story writer, Nobel Prize laureate (d. 2008)", "html": "1918 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian novelist, historian, and short story writer, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Literature\" title=\"Nobel Prize in Literature\">Nobel Prize</a> laureate (d. 2008)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian novelist, historian, and short story writer, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Literature\" title=\"Nobel Prize in Literature\">Nobel Prize</a> laureate (d. 2008)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Nobel Prize in Literature", "link": "https://wikipedia.org/wiki/Nobel_Prize_in_Literature"}]}, {"year": "1919", "text": "<PERSON>, English television host and producer (d. 2016)", "html": "1919 - <a href=\"https://wikipedia.org/wiki/Cliff_<PERSON>\" title=\"Cliff Michelmore\"><PERSON></a>, English television host and producer (d. 2016)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Cliff_Michelmore\" title=\"Cliff Michelmore\"><PERSON></a>, English television host and producer (d. 2016)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Cliff_Michelmore"}]}, {"year": "1919", "text": "<PERSON>, American actress (d. 2000)", "html": "1919 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress (d. 2000)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress (d. 2000)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1920", "text": "<PERSON>, American environmental activist (d. 2007)", "html": "1920 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American environmental activist (d. 2007)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American environmental activist (d. 2007)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1920", "text": "<PERSON>, English motorcycle racer and journalist (d. 1996)", "html": "1920 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English motorcycle racer and journalist (d. 1996)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English motorcycle racer and journalist (d. 1996)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1921", "text": "<PERSON><PERSON>, Estonian poet and publicist (d. 2000)", "html": "1921 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Estonian poet and publicist (d. 2000)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Estonian poet and publicist (d. 2000)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>n"}]}, {"year": "1921", "text": "<PERSON>, English actress (d. 2016)", "html": "1921 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(actress)\" title=\"<PERSON> (actress)\"><PERSON></a>, English actress (d. 2016)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(actress)\" title=\"<PERSON> (actress)\"><PERSON></a>, English actress (d. 2016)", "links": [{"title": "<PERSON> (actress)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(actress)"}]}, {"year": "1922", "text": "<PERSON><PERSON><PERSON>, Greek singer-songwriter (d. 2005)", "html": "1922 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Greek singer-songwriter (d. 2005)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Greek singer-songwriter (d. 2005)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1922", "text": "<PERSON><PERSON>, Indian actor, director, and screenwriter (d. 2021)", "html": "1922 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Indian actor, director, and screenwriter (d. 2021)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Indian actor, director, and screenwriter (d. 2021)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1922", "text": "<PERSON><PERSON>, Finnish-American actress, producer, and screenwriter (d. 2008)", "html": "1922 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Finnish-American actress, producer, and screenwriter (d. 2008)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>urmi\"><PERSON><PERSON></a>, Finnish-American actress, producer, and screenwriter (d. 2008)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1922", "text": "<PERSON>, American short story writer and poet (d. 2007)", "html": "1922 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American short story writer and poet (d. 2007)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American short story writer and poet (d. 2007)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1923", "text": "<PERSON>, American actress and dancer (d. 2009)", "html": "1923 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress and dancer (d. 2009)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress and dancer (d. 2009)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1923", "text": "<PERSON>, Hungarian-born American businesswoman, co-founded Coach, Inc. (d. 2013)", "html": "1923 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Hungarian-born American businesswoman, co-founded <a href=\"https://wikipedia.org/wiki/Coach,_Inc.\" class=\"mw-redirect\" title=\"Coach, Inc.\">Coach, Inc.</a> (d. 2013)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Hungarian-born American businesswoman, co-founded <a href=\"https://wikipedia.org/wiki/Coach,_Inc.\" class=\"mw-redirect\" title=\"Coach, Inc.\">Coach, Inc.</a> (d. 2013)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Coach, Inc.", "link": "https://wikipedia.org/wiki/Coach,_Inc."}]}, {"year": "1923", "text": "<PERSON><PERSON>, American comics creator (d. 2014)", "html": "1923 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American comics creator (d. 2014)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American comics creator (d. 2014)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1924", "text": "<PERSON>, American football player and colonel (d. 2009)", "html": "1924 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player and colonel (d. 2009)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player and colonel (d. 2009)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1925", "text": "<PERSON>, American businessman and philanthropist (d. 2021)", "html": "1925 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American businessman and philanthropist (d. 2021)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American businessman and philanthropist (d. 2021)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1925", "text": "<PERSON>, American neuroscientist and academic, Nobel Prize laureate (d. 2019)", "html": "1925 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American neuroscientist and academic, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Physiology_or_Medicine\" title=\"Nobel Prize in Physiology or Medicine\">Nobel Prize</a> laureate (d. 2019)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American neuroscientist and academic, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Physiology_or_Medicine\" title=\"Nobel Prize in Physiology or Medicine\">Nobel Prize</a> laureate (d. 2019)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Nobel Prize in Physiology or Medicine", "link": "https://wikipedia.org/wiki/Nobel_Prize_in_Physiology_or_Medicine"}]}, {"year": "1925", "text": "<PERSON>, American politician (d. 2012)", "html": "1925 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(city_manager)\" title=\"<PERSON> (city manager)\"><PERSON></a>, American politician (d. 2012)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(city_manager)\" title=\"<PERSON> (city manager)\"><PERSON></a>, American politician (d. 2012)", "links": [{"title": "<PERSON> (city manager)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(city_manager)"}]}, {"year": "1926", "text": "<PERSON> <PERSON>, American singer-songwriter (d. 1984)", "html": "1926 - <a href=\"https://wikipedia.org/wiki/Big_Mama_Thornton\" title=\"Big Mama Thornton\">Big Mama <PERSON></a>, American singer-songwriter (d. 1984)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Big_Mama_Thornton\" title=\"Big Mama Thornton\">Big Mama <PERSON></a>, American singer-songwriter (d. 1984)", "links": [{"title": "Big Mama Thornton", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1927", "text": "<PERSON>, American illustrator (d. 2002)", "html": "1927 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American illustrator (d. 2002)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American illustrator (d. 2002)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1929", "text": "<PERSON>, German actor and production manager (d. 2012)", "html": "1929 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German actor and production manager (d. 2012)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German actor and production manager (d. 2012)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1929", "text": "<PERSON><PERSON><PERSON>, Indian cricketer (d. 2002)", "html": "1929 - <a href=\"https://wikipedia.org/wiki/<PERSON>has<PERSON>_G<PERSON>te\" title=\"<PERSON><PERSON><PERSON> Gupte\"><PERSON><PERSON><PERSON></a>, Indian cricketer (d. 2002)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>has<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON> Gupte\"><PERSON><PERSON><PERSON></a>, Indian cricketer (d. 2002)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Subhash_<PERSON>upte"}]}, {"year": "1930", "text": "<PERSON><PERSON>, Spanish actress (d. 2016)", "html": "1930 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Spanish actress (d. 2016)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Spanish actress (d. 2016)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_Lampreave"}]}, {"year": "1930", "text": "<PERSON><PERSON><PERSON>, French actor, director, and screenwriter (d. 2022)", "html": "1930 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, French actor, director, and screenwriter (d. 2022)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, French actor, director, and screenwriter (d. 2022)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>"}]}, {"year": "1931", "text": "<PERSON>, American basketball player", "html": "1931 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1931", "text": "<PERSON>, American philosopher and scholar (d. 2013)", "html": "1931 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American philosopher and scholar (d. 2013)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American philosopher and scholar (d. 2013)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1931", "text": "<PERSON>, Puerto Rican actress, singer, and dancer", "html": "1931 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Puerto Rican actress, singer, and dancer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Puerto Rican actress, singer, and dancer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1931", "text": "<PERSON>, Canadian ice hockey player (d. 2017)", "html": "1931 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player (d. 2017)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player (d. 2017)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1931", "text": "<PERSON><PERSON><PERSON>, Indian guru, mystic, and educator (d. 1990)", "html": "1931 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Indian guru, mystic, and educator (d. 1990)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Indian guru, mystic, and educator (d. 1990)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>"}]}, {"year": "1932", "text": "<PERSON>, Nicaraguan colonel and engineer (d. 1991)", "html": "1932 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%BAdez\" title=\"<PERSON>\"><PERSON></a>, Nicaraguan colonel and engineer (d. 1991)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%BAdez\" title=\"<PERSON>\"><PERSON></a>, Nicaraguan colonel and engineer (d. 1991)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Enrique_Berm%C3%BAdez"}]}, {"year": "1932", "text": "<PERSON>, American author and poet (d. 2023)", "html": "1932 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author and poet (d. 2023)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author and poet (d. 2023)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Keith_<PERSON>"}]}, {"year": "1933", "text": "<PERSON><PERSON><PERSON>, Jr., Filipino civil servant and politician, 23rd President of the Senate of the Philippines (d. 2019)", "html": "1933 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>,_Jr.\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON>, Jr.\"><PERSON><PERSON><PERSON>, Jr.</a>, Filipino civil servant and politician, 23rd <a href=\"https://wikipedia.org/wiki/President_of_the_Senate_of_the_Philippines\" title=\"President of the Senate of the Philippines\">President of the Senate of the Philippines</a> (d. 2019)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>,_Jr.\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON>, Jr.\"><PERSON><PERSON><PERSON>, Jr.</a>, Filipino civil servant and politician, 23rd <a href=\"https://wikipedia.org/wiki/President_of_the_Senate_of_the_Philippines\" title=\"President of the Senate of the Philippines\">President of the Senate of the Philippines</a> (d. 2019)", "links": [{"title": "<PERSON><PERSON><PERSON>, Jr.", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>,_Jr."}, {"title": "President of the Senate of the Philippines", "link": "https://wikipedia.org/wiki/President_of_the_Senate_of_the_Philippines"}]}, {"year": "1934", "text": "<PERSON><PERSON>, Afghan-Indian cricketer (d. 2023)", "html": "1934 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Afghan-Indian cricketer (d. 2023)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Afghan-Indian cricketer (d. 2023)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1935", "text": "<PERSON><PERSON><PERSON>, Indian journalist and politician, 13th President of India (d. 2020)", "html": "1935 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Indian journalist and politician, 13th <a href=\"https://wikipedia.org/wiki/President_of_India\" title=\"President of India\">President of India</a> (d. 2020)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Indian journalist and politician, 13th <a href=\"https://wikipedia.org/wiki/President_of_India\" title=\"President of India\">President of India</a> (d. 2020)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}, {"title": "President of India", "link": "https://wikipedia.org/wiki/President_of_India"}]}, {"year": "1935", "text": "<PERSON>, Canadian ice hockey player (d. 1998)", "html": "1935 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player (d. 1998)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player (d. 1998)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1936", "text": "<PERSON>, Dutch lawyer and politician, Dutch Minister of Foreign Affairs", "html": "1936 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch lawyer and politician, <a href=\"https://wikipedia.org/wiki/List_of_Ministers_of_Foreign_Affairs_of_the_Netherlands\" class=\"mw-redirect\" title=\"List of Ministers of Foreign Affairs of the Netherlands\">Dutch Minister of Foreign Affairs</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch lawyer and politician, <a href=\"https://wikipedia.org/wiki/List_of_Ministers_of_Foreign_Affairs_of_the_Netherlands\" class=\"mw-redirect\" title=\"List of Ministers of Foreign Affairs of the Netherlands\">Dutch Minister of Foreign Affairs</a>", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>"}, {"title": "List of Ministers of Foreign Affairs of the Netherlands", "link": "https://wikipedia.org/wiki/List_of_Ministers_of_Foreign_Affairs_of_the_Netherlands"}]}, {"year": "1936", "text": "<PERSON><PERSON>, Japanese politician", "html": "1936 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Japanese politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Japanese politician", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1937", "text": "<PERSON>, American novelist, essayist, and poet (d. 2016)", "html": "1937 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American novelist, essayist, and poet (d. 2016)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American novelist, essayist, and poet (d. 2016)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1938", "text": "<PERSON>, Algerian-French singer-songwriter and guitarist", "html": "1938 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Algerian-French singer-songwriter and guitarist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Algerian-French singer-songwriter and guitarist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1938", "text": "<PERSON>, American jazz musician (d. 2020)", "html": "1938 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American jazz musician (d. 2020)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American jazz musician (d. 2020)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1939", "text": "<PERSON>, American activist and politician (d. 2016)", "html": "1939 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American activist and politician (d. 2016)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American activist and politician (d. 2016)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1939", "text": "<PERSON>, American novelist, short story writer, and screenwriter", "html": "1939 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American novelist, short story writer, and screenwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American novelist, short story writer, and screenwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1940", "text": "<PERSON>, American singer-songwriter, guitarist, and producer", "html": "1940 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter, guitarist, and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter, guitarist, and producer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1940", "text": "<PERSON>, American actress and producer", "html": "1940 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress and producer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1941", "text": "<PERSON>, American lawyer, politician, and diplomat, 11th United States Ambassador to China", "html": "1941 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer, politician, and diplomat, 11th <a href=\"https://wikipedia.org/wiki/United_States_Ambassador_to_China\" class=\"mw-redirect\" title=\"United States Ambassador to China\">United States Ambassador to China</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer, politician, and diplomat, 11th <a href=\"https://wikipedia.org/wiki/United_States_Ambassador_to_China\" class=\"mw-redirect\" title=\"United States Ambassador to China\">United States Ambassador to China</a>", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "United States Ambassador to China", "link": "https://wikipedia.org/wiki/United_States_Ambassador_to_China"}]}, {"year": "1941", "text": "<PERSON><PERSON> <PERSON><PERSON>, Canadian ice hockey player, coach, and manager (d. 2015)", "html": "1941 - <a href=\"https://wikipedia.org/wiki/J._P._Paris%C3%A9\" title=\"J. P. Parisé\"><PERSON><PERSON> <PERSON><PERSON></a>, Canadian ice hockey player, coach, and manager (d. 2015)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/J._P._Paris%C3%A9\" title=\"J. P. <PERSON>\"><PERSON><PERSON> <PERSON><PERSON></a>, Canadian ice hockey player, coach, and manager (d. 2015)", "links": [{"title": "<PERSON><PERSON> <PERSON><PERSON>", "link": "https://wikipedia.org/wiki/J._P._Paris%C3%A9"}]}, {"year": "1941", "text": "<PERSON><PERSON><PERSON>, Dutch conductor and composer (d. 1988)", "html": "1941 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON> O<PERSON>loo\"><PERSON><PERSON><PERSON></a>, Dutch conductor and composer (d. 1988)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON> O<PERSON>loo\"><PERSON><PERSON><PERSON> <PERSON></a>, Dutch conductor and composer (d. 1988)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1941", "text": "<PERSON><PERSON>, American singer-songwriter (d. 1991)", "html": "1941 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON> <PERSON></a>, American singer-songwriter (d. 1991)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON> <PERSON></a>, American singer-songwriter (d. 1991)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1942", "text": "<PERSON>, English actress", "html": "1942 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actress", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1943", "text": "<PERSON>, American lieutenant, lawyer, and politician, 68th United States Secretary of State", "html": "1943 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lieutenant, lawyer, and politician, 68th <a href=\"https://wikipedia.org/wiki/United_States_Secretary_of_State\" title=\"United States Secretary of State\">United States Secretary of State</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lieutenant, lawyer, and politician, 68th <a href=\"https://wikipedia.org/wiki/United_States_Secretary_of_State\" title=\"United States Secretary of State\">United States Secretary of State</a>", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "United States Secretary of State", "link": "https://wikipedia.org/wiki/United_States_Secretary_of_State"}]}, {"year": "1944", "text": "<PERSON><PERSON>, American actress and comedian (d. 2024)", "html": "1944 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American actress and comedian (d. 2024)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American actress and comedian (d. 2024)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>r"}]}, {"year": "1944", "text": "<PERSON>, American tenor and educator", "html": "1944 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American tenor and educator", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American tenor and educator", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1944", "text": "<PERSON><PERSON><PERSON>, American actress", "html": "1944 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"Lynda <PERSON> George\"><PERSON><PERSON><PERSON> <PERSON></a>, American actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"Lynda <PERSON> George\"><PERSON><PERSON><PERSON> <PERSON></a>, American actress", "links": [{"title": "Lynda <PERSON>", "link": "https://wikipedia.org/wiki/Lynda_Day_George"}]}, {"year": "1944", "text": "<PERSON>, American concert promoter and producer (d. 2022)", "html": "1944 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(producer)\" title=\"<PERSON> (producer)\"><PERSON></a>, American concert promoter and producer (d. 2022)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>(producer)\" title=\"<PERSON> (producer)\"><PERSON></a>, American concert promoter and producer (d. 2022)", "links": [{"title": "<PERSON> (producer)", "link": "https://wikipedia.org/wiki/<PERSON>(producer)"}]}, {"year": "1944", "text": "<PERSON>, American singer-songwriter", "html": "1944 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1946", "text": "<PERSON><PERSON><PERSON>, Indonesian singer-songwriter, guitarist, and actor", "html": "1946 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Indonesian singer-songwriter, guitarist, and actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Indonesian singer-songwriter, guitarist, and actor", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON><PERSON>"}]}, {"year": "1946", "text": "<PERSON>, Australian cricketer", "html": "1946 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian cricketer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian cricketer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1946", "text": "<PERSON>, American journalist and author", "html": "1946 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(author)\" title=\"<PERSON> (author)\"><PERSON></a>, American journalist and author", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(author)\" title=\"<PERSON> (author)\"><PERSON></a>, American journalist and author", "links": [{"title": "<PERSON> (author)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(author)"}]}, {"year": "1948", "text": "<PERSON><PERSON><PERSON>, Greek guitarist and composer", "html": "1948 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Greek guitarist and composer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Greek guitarist and composer", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>ama<PERSON>_<PERSON>"}]}, {"year": "1948", "text": "<PERSON><PERSON>, Japanese singer-songwriter (d. 2023)", "html": "1948 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Japanese singer-songwriter (d. 2023)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Japanese singer-songwriter (d. 2023)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>"}]}, {"year": "1949", "text": "<PERSON>, Greek-Argentine businesswoman, socialite, and heiress (d. 1988)", "html": "1949 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Greek-Argentine businesswoman, socialite, and heiress (d. 1988)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Greek-Argentine businesswoman, socialite, and heiress (d. 1988)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1951", "text": "<PERSON><PERSON><PERSON>, Malaysian astrophysicist and astronomer", "html": "1951 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Malaysian astrophysicist and astronomer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Malaysian astrophysicist and astronomer", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1951", "text": "<PERSON><PERSON>, Dutch discus thrower and shot putter", "html": "1951 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Dutch discus thrower and shot putter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Dutch discus thrower and shot putter", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Ria_Stalman"}]}, {"year": "1953", "text": "<PERSON><PERSON>, American actress", "html": "1953 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American actress", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1954", "text": "<PERSON>, American golfer", "html": "1954 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American golfer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American golfer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1954", "text": "<PERSON>, Barbadian cricketer (d. 1999)", "html": "1954 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Barbadian cricketer (d. 1999)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Barbadian cricketer (d. 1999)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1954", "text": "<PERSON>, Mexican lawyer and politician, Mexican Secretary of the Interior", "html": "1954 - <a href=\"https://wikipedia.org/wiki/Santiago_Creel\" title=\"Santiago Creel\">Santiago Creel</a>, Mexican lawyer and politician, <a href=\"https://wikipedia.org/wiki/Secretariat_of_the_Interior_(Mexico)\" class=\"mw-redirect\" title=\"Secretariat of the Interior (Mexico)\">Mexican Secretary of the Interior</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Santiago_Creel\" title=\"Santiago Creel\">Santiago Creel</a>, Mexican lawyer and politician, <a href=\"https://wikipedia.org/wiki/Secretariat_of_the_Interior_(Mexico)\" class=\"mw-redirect\" title=\"Secretariat of the Interior (Mexico)\">Mexican Secretary of the Interior</a>", "links": [{"title": "Santiago Creel", "link": "https://wikipedia.org/wiki/Santiago_Creel"}, {"title": "Secretariat of the Interior (Mexico)", "link": "https://wikipedia.org/wiki/Secretariat_of_the_Interior_(Mexico)"}]}, {"year": "1954", "text": "<PERSON><PERSON><PERSON>, American singer-songwriter, bass player, and producer", "html": "1954 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American singer-songwriter, bass player, and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American singer-songwriter, bass player, and producer", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1954", "text": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, Icelandic guitarist, mathematician, and engineer", "html": "1954 - <a href=\"https://wikipedia.org/wiki/Gu%C3%B0lau<PERSON><PERSON>_<PERSON><PERSON><PERSON>_%C3%93<PERSON><PERSON><PERSON>\" title=\"<PERSON>u<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON></a>, Icelandic guitarist, mathematician, and engineer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Gu%C3%B0lau<PERSON><PERSON>_<PERSON><PERSON><PERSON>_%C3%93<PERSON><PERSON><PERSON>\" title=\"<PERSON>u<PERSON>lau<PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON></a>, Icelandic guitarist, mathematician, and engineer", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Gu%C3%B0lau<PERSON><PERSON>_<PERSON><PERSON><PERSON>_%C3%93tta<PERSON>son"}]}, {"year": "1955", "text": "<PERSON>, American economist and academic", "html": "1955 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American economist and academic", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American economist and academic", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1955", "text": "<PERSON><PERSON>, American basketball player, coach, and manager", "html": "1955 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> <PERSON>\"><PERSON><PERSON></a>, American basketball player, coach, and manager", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> <PERSON>\"><PERSON><PERSON></a>, American basketball player, coach, and manager", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1955", "text": "<PERSON>, British fashion designer", "html": "1955 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, British fashion designer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, British fashion designer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1955", "text": "<PERSON>, German footballer and manager", "html": "1955 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German footballer and manager", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German footballer and manager", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1956", "text": "<PERSON><PERSON>, American actress and director", "html": "1956 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American actress and director", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American actress and director", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1956", "text": "<PERSON>, English politician, Secretary of State for Health", "html": "1956 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English politician, <a href=\"https://wikipedia.org/wiki/Secretary_of_State_for_Health\" class=\"mw-redirect\" title=\"Secretary of State for Health\">Secretary of State for Health</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English politician, <a href=\"https://wikipedia.org/wiki/Secretary_of_State_for_Health\" class=\"mw-redirect\" title=\"Secretary of State for Health\">Secretary of State for Health</a>", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Secretary of State for Health", "link": "https://wikipedia.org/wiki/Secretary_of_State_for_Health"}]}, {"year": "1957", "text": "<PERSON>, American author and illustrator", "html": "1957 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author and illustrator", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author and illustrator", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1958", "text": "<PERSON>, English-born Irish footballer and manager", "html": "1958 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English-born Irish footballer and manager", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English-born Irish footballer and manager", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1958", "text": "<PERSON>, American actor, director, producer, and screenwriter", "html": "1958 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor, director, producer, and screenwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor, director, producer, and screenwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1958", "text": "<PERSON>, American bass player, songwriter, and producer", "html": "1958 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Sixx\"><PERSON></a>, American bass player, songwriter, and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Sixx\"><PERSON></a>, American bass player, songwriter, and producer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_Sixx"}]}, {"year": "1960", "text": "<PERSON>, Swedish ice hockey player and coach", "html": "1960 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swedish ice hockey player and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swedish ice hockey player and coach", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1961", "text": "<PERSON>, Irish-American singer-songwriter and guitarist", "html": "1961 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(Irish_singer)\" class=\"mw-redirect\" title=\"<PERSON> (Irish singer)\"><PERSON></a>, Irish-American singer-songwriter and guitarist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(Irish_singer)\" class=\"mw-redirect\" title=\"<PERSON> (Irish singer)\"><PERSON></a>, Irish-American singer-songwriter and guitarist", "links": [{"title": "<PERSON> (Irish singer)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(Irish_singer)"}]}, {"year": "1961", "text": "<PERSON>, Scottish footballer and manager", "html": "1961 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish footballer and manager", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish footballer and manager", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1961", "text": "<PERSON><PERSON>, Senegalese engineer and politician, fourth President of Senegal", "html": "1961 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Senegalese engineer and politician, fourth <a href=\"https://wikipedia.org/wiki/List_of_Presidents_of_Senegal\" class=\"mw-redirect\" title=\"List of Presidents of Senegal\">President of Senegal</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Senegalese engineer and politician, fourth <a href=\"https://wikipedia.org/wiki/List_of_Presidents_of_Senegal\" class=\"mw-redirect\" title=\"List of Presidents of Senegal\">President of Senegal</a>", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>l"}, {"title": "List of Presidents of Senegal", "link": "https://wikipedia.org/wiki/List_of_Presidents_of_Senegal"}]}, {"year": "1961", "text": "<PERSON>, English chef and mentor", "html": "1961 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English chef and mentor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English chef and mentor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1962", "text": "<PERSON>, American actor", "html": "1962 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1963", "text": "<PERSON>, Dutch footballer and manager", "html": "1963 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch footballer and manager", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch footballer and manager", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1963", "text": "<PERSON>, New Zealand cricketer", "html": "1963 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, New Zealand cricketer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, New Zealand cricketer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1963", "text": "<PERSON><PERSON><PERSON><PERSON>, German tennis player", "html": "1963 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German tennis player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German tennis player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1963", "text": "<PERSON>, Dutch footballer and manager", "html": "1963 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(footballer)\" title=\"<PERSON> (footballer)\"><PERSON></a>, Dutch footballer and manager", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(footballer)\" title=\"<PERSON> (footballer)\"><PERSON></a>, Dutch footballer and manager", "links": [{"title": "<PERSON> (footballer)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(footballer)"}]}, {"year": "1963", "text": "<PERSON>, English footballer and coach", "html": "1963 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English footballer and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English footballer and coach", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1964", "text": "<PERSON>, Scottish singer-songwriter and guitarist", "html": "1964 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish singer-songwriter and guitarist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish singer-songwriter and guitarist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1964", "text": "<PERSON>, American singer-songwriter, bass player, and producer", "html": "1964 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter, bass player, and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter, bass player, and producer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Dave_<PERSON>"}]}, {"year": "1964", "text": "<PERSON>, Canadian swimmer and sportscaster", "html": "1964 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian swimmer and sportscaster", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian swimmer and sportscaster", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1965", "text": "<PERSON>, American baseball player and coach", "html": "1965 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player and coach", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1965", "text": "<PERSON>, New Zealand rugby player", "html": "1965 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, New Zealand rugby player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, New Zealand rugby player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1965", "text": "<PERSON>, Australian rugby league player and politician", "html": "1965 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian rugby league player and politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian rugby league player and politician", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1965", "text": "<PERSON><PERSON><PERSON>, Greek economist and politician, Greek Minister for National Defence", "html": "1965 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Greek economist and politician, <a href=\"https://wikipedia.org/wiki/Ministry_of_National_Defence_(Greece)\" title=\"Ministry of National Defence (Greece)\">Greek Minister for National Defence</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Greek economist and politician, <a href=\"https://wikipedia.org/wiki/Ministry_of_National_Defence_(Greece)\" title=\"Ministry of National Defence (Greece)\">Greek Minister for National Defence</a>", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}, {"title": "Ministry of National Defence (Greece)", "link": "https://wikipedia.org/wiki/Ministry_of_National_Defence_(Greece)"}]}, {"year": "1966", "text": "<PERSON>, American actor", "html": "1966 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1966", "text": "<PERSON>, Norwegian guitarist and producer", "html": "1966 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A9\" title=\"<PERSON>\"><PERSON></a>, Norwegian guitarist and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A9\" title=\"<PERSON>\"><PERSON></a>, Norwegian guitarist and producer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A9"}]}, {"year": "1966", "text": "<PERSON><PERSON><PERSON>, Swedish race car driver and mountaineer (d. 2002)", "html": "1966 - <a href=\"https://wikipedia.org/wiki/G%C3%B6<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Swedish race car driver and mountaineer (d. 2002)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/G%C3%B6<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Swedish race car driver and mountaineer (d. 2002)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/G%C3%B6<PERSON>_<PERSON>p"}]}, {"year": "1966", "text": "<PERSON>, Hong Kong singer and actor", "html": "1966 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Hong Kong singer and actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Hong Kong singer and actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1967", "text": "<PERSON>, Australian voice actor", "html": "1967 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian voice actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian voice actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1967", "text": "<PERSON><PERSON><PERSON><PERSON>, American comedian, actress, and producer", "html": "1967 - <a href=\"https://wikipedia.org/wiki/Mo%27Nique\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, American comedian, actress, and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Mo%27Nique\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, American comedian, actress, and producer", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Mo%27Nique"}]}, {"year": "1967", "text": "<PERSON>, English animator, director, producer, and screenwriter", "html": "1967 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English animator, director, producer, and screenwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English animator, director, producer, and screenwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1967", "text": "<PERSON>, American basketball player and coach", "html": "1967 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Katy Steding\"><PERSON></a>, American basketball player and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_Steding\" title=\"Katy Steding\"><PERSON></a>, American basketball player and coach", "links": [{"title": "Katy Steding", "link": "https://wikipedia.org/wiki/Katy_Steding"}]}, {"year": "1968", "text": "<PERSON><PERSON>, French researcher in microbiology, genetics and biochemistry, and Nobel laureate", "html": "1968 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, French researcher in microbiology, genetics and biochemistry, and Nobel laureate", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, French researcher in microbiology, genetics and biochemistry, and Nobel laureate", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1968", "text": "<PERSON><PERSON><PERSON><PERSON>, Italian footballer and manager", "html": "1968 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Italian footballer and manager", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Italian footballer and manager", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1969", "text": "<PERSON><PERSON><PERSON><PERSON>, Indian chess player", "html": "1969 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Indian chess player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Indian chess player", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1969", "text": "<PERSON><PERSON> Bjørneby<PERSON>, Norwegian footballer and manager", "html": "1969 - <a href=\"https://wikipedia.org/wiki/Stig_Inge_Bj%C3%B8rnebye\" title=\"Stig Inge Bjørnebye\"><PERSON><PERSON> Inge Bjørnebye</a>, Norwegian footballer and manager", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Stig_Inge_Bj%C3%B8rnebye\" title=\"Stig Inge Bjørnebye\"><PERSON><PERSON> Inge Bjørnebye</a>, Norwegian footballer and manager", "links": [{"title": "Stig Inge Bjørnebye", "link": "https://wikipedia.org/wiki/Stig_Inge_Bj%C3%B8rnebye"}]}, {"year": "1969", "text": "<PERSON>, American actor, director, and screenwriter", "html": "1969 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor, director, and screenwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Martin<PERSON>\"><PERSON></a>, American actor, director, and screenwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1969", "text": "<PERSON>, Italian footballer and manager", "html": "1969 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian footballer and manager", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian footballer and manager", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1970", "text": "<PERSON>, American model and actress", "html": "1970 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(model)\" title=\"<PERSON> (model)\"><PERSON></a>, American model and actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(model)\" title=\"<PERSON> (model)\"><PERSON></a>, American model and actress", "links": [{"title": "<PERSON> (model)", "link": "https://wikipedia.org/wiki/<PERSON>_Fuller_(model)"}]}, {"year": "1971", "text": "<PERSON>, American football player and sportscaster", "html": "1971 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player and sportscaster", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player and sportscaster", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1972", "text": "<PERSON>, Swedish ice hockey player", "html": "1972 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swedish ice hockey player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swedish ice hockey player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1972", "text": "<PERSON>, Saudi Arabian footballer and manager", "html": "1972 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Saudi Arabian footballer and manager", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Saudi Arabian footballer and manager", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1972", "text": "<PERSON>, Zimbabwean cricketer", "html": "1972 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Zimbabwean cricketer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Zimbabwean cricketer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1972", "text": "<PERSON><PERSON><PERSON>, Ukrainian footballer and manager (d. 2014)", "html": "1972 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Ukrainian footballer and manager (d. 2014)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Ukrainian footballer and manager (d. 2014)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1973", "text": "<PERSON><PERSON>, American rapper", "html": "1973 - <a href=\"https://wikipedia.org/wiki/Mo<PERSON>_Def\" class=\"mw-redirect\" title=\"Mos Def\"><PERSON><PERSON></a>, American rapper", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Mo<PERSON>_Def\" class=\"mw-redirect\" title=\"Mos Def\"><PERSON><PERSON></a>, American rapper", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Mos_Def"}]}, {"year": "1974", "text": "<PERSON><PERSON><PERSON>, Dutch golfer", "html": "1974 - <a href=\"https://wikipedia.org/wiki/<PERSON>arte<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Dutch golfer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Dutch golfer", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1974", "text": "<PERSON>, American wrestler", "html": "1974 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Mysterio\"><PERSON></a>, American wrestler", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Rey Mysterio\"><PERSON></a>, American wrestler", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Rey_Mysterio"}]}, {"year": "1974", "text": "<PERSON>, American theatre and voice actress", "html": "1974 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American theatre and voice actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American theatre and voice actress", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1974", "text": "<PERSON>, English journalist and television host", "html": "1974 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English journalist and television host", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English journalist and television host", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1974", "text": "<PERSON><PERSON>, Ethiopian runner", "html": "1974 - <a href=\"https://wikipedia.org/wiki/<PERSON>e_<PERSON>ami\" title=\"Gete Wami\"><PERSON><PERSON></a>, Ethiopian runner", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>\" title=\"Gete Wami\"><PERSON><PERSON></a>, Ethiopian runner", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Gete_Wami"}]}, {"year": "1975", "text": "<PERSON><PERSON><PERSON>, Dutch cyclist", "html": "1975 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Dutch cyclist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Dutch cyclist", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>gt"}]}, {"year": "1976", "text": "<PERSON><PERSON><PERSON>, American basketball player, coach, and manager", "html": "1976 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON> <PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American basketball player, coach, and manager", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American basketball player, coach, and manager", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON><PERSON>-<PERSON>"}]}, {"year": "1976", "text": "<PERSON><PERSON><PERSON>, Japanese actor", "html": "1976 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese actor", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1977", "text": "<PERSON>, Swiss ice hockey player", "html": "1977 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swiss ice hockey player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swiss ice hockey player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Mark_<PERSON>reit"}]}, {"year": "1978", "text": "<PERSON>, Jr., American comedian, actor, and radio host", "html": "1978 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Jr.\" class=\"mw-redirect\" title=\"<PERSON>, Jr.\"><PERSON>, Jr.</a>, American comedian, actor, and radio host", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Jr.\" class=\"mw-redirect\" title=\"<PERSON>, Jr.\"><PERSON>, Jr.</a>, American comedian, actor, and radio host", "links": [{"title": "<PERSON>, Jr.", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Jr."}]}, {"year": "1979", "text": "<PERSON><PERSON>, American author", "html": "1979 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American author", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American author", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1979", "text": "<PERSON><PERSON>, Estonian figure skater", "html": "1979 - <a href=\"https://wikipedia.org/wiki/Valdis_Mintals\" title=\"Valdis Mintals\"><PERSON><PERSON> Mintals</a>, Estonian figure skater", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Valdis_Mintals\" title=\"Valdis Mintals\"><PERSON>dis Mintals</a>, Estonian figure skater", "links": [{"title": "Valdis Mintals", "link": "https://wikipedia.org/wiki/Valdis_Mintals"}]}, {"year": "1979", "text": "<PERSON>, American actor, director, producer, and screenwriter", "html": "1979 - <a href=\"https://wikipedia.org/wiki/Rider_Strong\" title=\"Rider Strong\"><PERSON> Strong</a>, American actor, director, producer, and screenwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Rider_Strong\" title=\"Rider Strong\"><PERSON> Strong</a>, American actor, director, producer, and screenwriter", "links": [{"title": "Rider Strong", "link": "https://wikipedia.org/wiki/Rider_Strong"}]}, {"year": "1980", "text": "<PERSON><PERSON>, Israeli poet", "html": "1980 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Israeli poet", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Israeli poet", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>i_<PERSON><PERSON>r"}]}, {"year": "1980", "text": "<PERSON><PERSON><PERSON>, Estonian basketball player", "html": "1980 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Estonian basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Estonian basketball player", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1981", "text": "<PERSON><PERSON><PERSON><PERSON>, American basketball player and coach", "html": "1981 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, American basketball player and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, American basketball player and coach", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON><PERSON><PERSON>"}]}, {"year": "1981", "text": "<PERSON>, American journalist", "html": "1981 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(TV_personality)\" title=\"<PERSON> (TV personality)\"><PERSON></a>, American journalist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(TV_personality)\" title=\"<PERSON> (TV personality)\"><PERSON></a>, American journalist", "links": [{"title": "<PERSON> (TV personality)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(TV_personality)"}]}, {"year": "1981", "text": "<PERSON>, American author and illustrator", "html": "1981 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author and illustrator", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author and illustrator", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1981", "text": "<PERSON>, Australian footballer", "html": "1981 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1981", "text": "<PERSON>, Argentine footballer", "html": "1981 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Argentine footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Argentine footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1982", "text": "<PERSON>, American football player", "html": "1982 - <a href=\"https://wikipedia.org/wiki/<PERSON>_Harper\" title=\"Roman Harper\"><PERSON></a>, American football player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Roman Harper\"><PERSON></a>, American football player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_Harper"}]}, {"year": "1982", "text": "<PERSON>, Argentine race car driver", "html": "1982 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A9rez_Companc\" title=\"<PERSON>anc\"><PERSON></a>, Argentine race car driver", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A9rez_Companc\" title=\"<PERSON>mpanc\"><PERSON></a>, Argentine race car driver", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Pablo_P%C3%A9<PERSON>_Companc"}]}, {"year": "1984", "text": "<PERSON><PERSON>, English footballer", "html": "1984 - <a href=\"https://wikipedia.org/wiki/Leighton_Baines\" title=\"Leighton Baines\"><PERSON><PERSON></a>, English footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Leighton_Baines\" title=\"Leighton Baines\"><PERSON><PERSON></a>, English footballer", "links": [{"title": "Leighton Baines", "link": "https://wikipedia.org/wiki/Leighton_Baines"}]}, {"year": "1984", "text": "<PERSON>, Mexican actress and singer", "html": "1984 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%ADa\" title=\"<PERSON>\"><PERSON></a>, Mexican actress and singer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%ADa\" title=\"<PERSON>\"><PERSON></a>, Mexican actress and singer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%ADa"}]}, {"year": "1984", "text": "<PERSON>, American wrestler", "html": "1984 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(wrestler)\" title=\"<PERSON> (wrestler)\"><PERSON></a>, American wrestler", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(wrestler)\" title=\"<PERSON> (wrestler)\"><PERSON></a>, American wrestler", "links": [{"title": "<PERSON> (wrestler)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(wrestler)"}]}, {"year": "1984", "text": "<PERSON><PERSON><PERSON>, American actress", "html": "1984 - <a href=\"https://wikipedia.org/wiki/<PERSON>osh<PERSON>_R<PERSON>more\" title=\"Xosha Roquemore\"><PERSON><PERSON><PERSON></a>, American actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Xosh<PERSON>_<PERSON>more\" title=\"Xosh<PERSON> R<PERSON>more\"><PERSON><PERSON><PERSON></a>, American actress", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>osha_<PERSON>"}]}, {"year": "1985", "text": "<PERSON><PERSON>, Mexican actress", "html": "1985 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Mexican actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Mexican actress", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1986", "text": "<PERSON>, American basketball player", "html": "1986 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1987", "text": "<PERSON><PERSON>, German politician", "html": "1987 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, German politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, German politician", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1987", "text": "<PERSON>, American football player", "html": "1987 - <a href=\"https://wikipedia.org/wiki/Clifton_Geathers\" title=\"Clifton Geathers\"><PERSON></a>, American football player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Clifton_Geathers\" title=\"Clifton Geathers\"><PERSON></a>, American football player", "links": [{"title": "Clifton Geathers", "link": "https://wikipedia.org/wiki/Clifton_Geathers"}]}, {"year": "1987", "text": "<PERSON>, Australian actor", "html": "1987 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(actor)\" title=\"<PERSON> (actor)\"><PERSON></a>, Australian actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>(actor)\" title=\"<PERSON> (actor)\"><PERSON></a>, Australian actor", "links": [{"title": "<PERSON> (actor)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(actor)"}]}, {"year": "1987", "text": "<PERSON>, Australian actress", "html": "1987 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian actress", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1988", "text": "<PERSON>, New Zealand cricketer", "html": "1988 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, New Zealand cricketer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, New Zealand cricketer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1989", "text": "<PERSON><PERSON>, Irish boxer", "html": "1989 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Irish boxer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Irish boxer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1990", "text": "<PERSON><PERSON>, American actress and singer", "html": "1990 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>\" title=\"<PERSON><PERSON> Demie\"><PERSON><PERSON></a>, American actress and singer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>\" title=\"<PERSON><PERSON> Demie\"><PERSON><PERSON></a>, American actress and singer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Alexa_Demie"}]}, {"year": "1992", "text": "<PERSON>, American singer-songwriter", "html": "1992 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1992", "text": "<PERSON>, American basketball player", "html": "1992 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1993", "text": "<PERSON><PERSON><PERSON>, Mexican actress", "html": "1993 - <a href=\"https://wikipedia.org/wiki/Ya<PERSON><PERSON>_Aparicio\" title=\"Yalitza Aparicio\"><PERSON><PERSON><PERSON></a>, Mexican actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Ya<PERSON><PERSON>_Aparicio\" title=\"Ya<PERSON><PERSON> Aparicio\"><PERSON><PERSON><PERSON></a>, Mexican actress", "links": [{"title": "<PERSON><PERSON><PERSON> Aparicio", "link": "https://wikipedia.org/wiki/Ya<PERSON><PERSON>_<PERSON><PERSON><PERSON>o"}]}, {"year": "1995", "text": "<PERSON><PERSON><PERSON>, Scottish footballer", "html": "1995 - <a href=\"https://wikipedia.org/wiki/A<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Scottish footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Scottish footballer", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/A<PERSON>i_<PERSON>"}]}, {"year": "1996", "text": "<PERSON><PERSON>, American actress, singer and songwriter", "html": "1996 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>feld\"><PERSON><PERSON></a>, American actress, singer and songwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American actress, singer and songwriter", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1997", "text": "<PERSON>, American ice hockey player", "html": "1997 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American ice hockey player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American ice hockey player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2000", "text": "<PERSON><PERSON><PERSON>, American basketball player", "html": "2000 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>ye<PERSON>_<PERSON>\" title=\"Onye<PERSON> Okongwu\"><PERSON><PERSON><PERSON></a>, American basketball player", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Onyeka_<PERSON>wu"}]}], "Deaths": [{"year": "384", "text": "<PERSON> (b. c.304)", "html": "384 - <a href=\"https://wikipedia.org/wiki/Pope_Damasus_I\" title=\"Pope Damasus I\">Pope Damasus I</a> (b. c.304)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Pope_Damasus_I\" title=\"Pope Damasus I\">Pope Damasus I</a> (b. c.304)", "links": [{"title": "Pope Damasus I", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_I"}]}, {"year": "861", "text": "<PERSON><PERSON><PERSON><PERSON>, chief confidant and councillor to <PERSON><PERSON>Mu<PERSON>wak<PERSON><PERSON>", "html": "861 - <a href=\"https://wikipedia.org/wiki/<PERSON>-<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, chief confidant and councillor to <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON> ibn <PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, chief confidant and councillor to <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>_<PERSON>"}]}, {"year": "969", "text": "<PERSON><PERSON><PERSON><PERSON>, Byzantine emperor (b. 912)", "html": "969 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_II_Phokas\" title=\"<PERSON><PERSON><PERSON><PERSON> II Phokas\"><PERSON><PERSON><PERSON><PERSON> Phokas</a>, Byzantine emperor (b. 912)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_II_Phokas\" title=\"<PERSON><PERSON><PERSON><PERSON> II Phokas\"><PERSON><PERSON><PERSON><PERSON> Phokas</a>, Byzantine emperor (b. 912)", "links": [{"title": "Nikephoros II Phokas", "link": "https://wikipedia.org/wiki/<PERSON>ph<PERSON>s_II_<PERSON>okas"}]}, {"year": "1121", "text": "<PERSON><PERSON><PERSON><PERSON><PERSON>, Egyptian political adviser (b. 1066)", "html": "1121 - <a href=\"https://wikipedia.org/wiki/<PERSON>-<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON><PERSON></a>, Egyptian political adviser (b. 1066)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>-<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON><PERSON></a>, Egyptian political adviser (b. 1066)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>-<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1198", "text": "<PERSON><PERSON><PERSON>, Spanish astronomer, physicist, and philosopher (b. 1126)", "html": "1198 - <a href=\"https://wikipedia.org/wiki/Averroes\" title=\"Averro<PERSON>\"><PERSON><PERSON><PERSON></a>, Spanish astronomer, physicist, and philosopher (b. 1126)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Averroes\" title=\"Averro<PERSON>\"><PERSON><PERSON><PERSON></a>, Spanish astronomer, physicist, and philosopher (b. 1126)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Averroes"}]}, {"year": "1241", "text": "<PERSON><PERSON><PERSON>, Mongolian emperor (b. 1186)", "html": "1241 - <a href=\"https://wikipedia.org/wiki/%C3%96<PERSON><PERSON>_Khan\" title=\"<PERSON>gedei Khan\"><PERSON><PERSON><PERSON></a>, Mongolian emperor (b. 1186)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/%C3%96<PERSON><PERSON>_Khan\" title=\"Ögedei Khan\"><PERSON><PERSON><PERSON></a>, Mongolian emperor (b. 1186)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/%C3%96ged<PERSON>_Khan"}]}, {"year": "1282", "text": "<PERSON><PERSON><PERSON><PERSON>, Welsh prince (b. 1223)", "html": "1282 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_ap_<PERSON><PERSON>udd\" title=\"<PERSON><PERSON><PERSON><PERSON> ap G<PERSON>udd\"><PERSON><PERSON><PERSON><PERSON> ap <PERSON></a>, Welsh prince (b. 1223)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_ap_<PERSON>udd\" title=\"<PERSON><PERSON><PERSON><PERSON> ap G<PERSON>udd\"><PERSON><PERSON><PERSON><PERSON> ap <PERSON></a>, Welsh prince (b. 1223)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON> ap <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_a<PERSON>_<PERSON><PERSON><PERSON>"}]}, {"year": "1282", "text": "<PERSON>, Byzantine emperor (b. 1225)", "html": "1282 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Byzantine emperor (b. 1225)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Byzantine emperor (b. 1225)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1474", "text": "<PERSON> of Castile, King of the Crown of Castile (b. 1425)", "html": "1474 - <a href=\"https://wikipedia.org/wiki/<PERSON>_IV_of_Castile\" title=\"<PERSON> IV of Castile\"><PERSON> IV of Castile</a>, King of the <a href=\"https://wikipedia.org/wiki/Crown_of_Castile\" title=\"Crown of Castile\">Crown of Castile</a> (b. 1425)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_IV_of_Castile\" title=\"<PERSON> IV of Castile\"><PERSON> IV of Castile</a>, King of the <a href=\"https://wikipedia.org/wiki/Crown_of_Castile\" title=\"Crown of Castile\">Crown of Castile</a> (b. 1425)", "links": [{"title": "<PERSON> of Castile", "link": "https://wikipedia.org/wiki/<PERSON>_IV_of_Castile"}, {"title": "Crown of Castile", "link": "https://wikipedia.org/wiki/Crown_of_Castile"}]}, {"year": "1532", "text": "<PERSON>, Italian cardinal (b. 1455)", "html": "1532 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian cardinal (b. 1455)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian cardinal (b. 1455)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>i"}]}, {"year": "1582", "text": "<PERSON>, 3rd Duke of Alba, Spanish general and politician, 12th Constable of Portugal (b. 1508)", "html": "1582 - <a href=\"https://wikipedia.org/wiki/<PERSON>_%C3%<PERSON><PERSON><PERSON><PERSON>_<PERSON>_<PERSON>,_3rd_Duke_of_Alba\" title=\"<PERSON>, 3rd Duke of Alba\"><PERSON>, 3rd Duke of Alba</a>, Spanish general and politician, 12th <a href=\"https://wikipedia.org/wiki/Constable_of_Portugal\" title=\"Constable of Portugal\">Constable of Portugal</a> (b. 1508)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_%C3%<PERSON><PERSON><PERSON><PERSON>_<PERSON>_<PERSON>,_3rd_Duke_of_Alba\" title=\"<PERSON>, 3rd Duke of Alba\"><PERSON>, 3rd Duke of Alba</a>, Spanish general and politician, 12th <a href=\"https://wikipedia.org/wiki/Constable_of_Portugal\" title=\"Constable of Portugal\">Constable of Portugal</a> (b. 1508)", "links": [{"title": "<PERSON>, 3rd Duke of Alba", "link": "https://wikipedia.org/wiki/<PERSON>_%C3%<PERSON><PERSON><PERSON><PERSON>_<PERSON>_<PERSON>,_3rd_Duke_of_Alba"}, {"title": "Constable of Portugal", "link": "https://wikipedia.org/wiki/Constable_of_Portugal"}]}, {"year": "1610", "text": "<PERSON>, German artist working in Rome (b. 1578)", "html": "1610 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German artist working in Rome (b. 1578)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German artist working in Rome (b. 1578)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1686", "text": "<PERSON>, <PERSON>, French general (b. 1621)", "html": "1686 - <a href=\"https://wikipedia.org/wiki/<PERSON>,_Grand_Cond%C3%A9\" title=\"<PERSON>, Grand Condé\"><PERSON>, Grand <PERSON></a>, French general (b. 1621)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>,_Grand_Cond%C3%A9\" title=\"<PERSON>, Grand Condé\"><PERSON>, Grand Condé</a>, French general (b. 1621)", "links": [{"title": "<PERSON>, Grand Condé", "link": "https://wikipedia.org/wiki/<PERSON>,_Grand_Cond%C3%A9"}]}, {"year": "1694", "text": "<PERSON><PERSON><PERSON><PERSON>, Duke of Parma (b. 1630)", "html": "1694 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>_<PERSON>,_Duke_of_Parma\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON><PERSON> <PERSON>, Duke of Parma\"><PERSON><PERSON><PERSON><PERSON>, Duke of Parma</a> (b. 1630)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>_<PERSON>,_Duke_of_Parma\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON><PERSON>, Duke of Parma\"><PERSON><PERSON><PERSON><PERSON>, Duke of Parma</a> (b. 1630)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>, Duke of Parma", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>_<PERSON>,_Duke_of_Parma"}]}, {"year": "1737", "text": "<PERSON>, English priest, historian, and author (b. 1643)", "html": "1737 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English priest, historian, and author (b. 1643)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English priest, historian, and author (b. 1643)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1747", "text": "<PERSON>, English bookseller and publisher (b. 1675)", "html": "1747 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English bookseller and publisher (b. 1675)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English bookseller and publisher (b. 1675)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1797", "text": "<PERSON>, English physician (b. 1722)", "html": "1797 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English physician (b. 1722)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English physician (b. 1722)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1826", "text": "<PERSON> of Austria (b. 1797)", "html": "1826 - <a href=\"https://wikipedia.org/wiki/Maria_Leopoldina_of_Austria\" title=\"<PERSON> of Austria\"><PERSON> of Austria</a> (b. 1797)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Maria_Leopoldina_of_Austria\" title=\"<PERSON> of Austria\"><PERSON> of Austria</a> (b. 1797)", "links": [{"title": "<PERSON> of Austria", "link": "https://wikipedia.org/wiki/Maria_Leopoldina_of_Austria"}]}, {"year": "1840", "text": "Emperor <PERSON><PERSON><PERSON><PERSON> of Japan (b. 1771)", "html": "1840 - <a href=\"https://wikipedia.org/wiki/Emperor_K%C5%8Dkaku\" title=\"Emperor <PERSON><PERSON><PERSON><PERSON>\">Emperor <PERSON><PERSON><PERSON><PERSON></a> of Japan (b. 1771)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Emperor_K%C5%8Dka<PERSON>\" title=\"Emperor <PERSON><PERSON><PERSON><PERSON>\">Emperor <PERSON><PERSON><PERSON><PERSON></a> of Japan (b. 1771)", "links": [{"title": "Emperor <PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Emperor_K%C5%8Dkaku"}]}, {"year": "1872", "text": "<PERSON><PERSON><PERSON><PERSON> of Hawaii (b. 1830)", "html": "1872 - <a href=\"https://wikipedia.org/wiki/Kamehameha_V\" title=\"Kamehameha V\">Kamehameha V</a> of Hawaii (b. 1830)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Kamehameha_V\" title=\"Kamehameha V\">Kamehameha V</a> of Hawaii (b. 1830)", "links": [{"title": "Kamehameha V", "link": "https://wikipedia.org/wiki/Kamehameha_V"}]}, {"year": "1880", "text": "<PERSON>, American businessman, founded the Winchester Repeating Arms Company (b. 1810)", "html": "1880 - <a href=\"https://wikipedia.org/wiki/Oliver_<PERSON>\" title=\"Oliver Winchester\"><PERSON></a>, American businessman, founded the <a href=\"https://wikipedia.org/wiki/Winchester_Repeating_Arms_Company\" title=\"Winchester Repeating Arms Company\">Winchester Repeating Arms Company</a> (b. 1810)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Oliver_Winchester\" title=\"Oliver Winchester\"><PERSON></a>, American businessman, founded the <a href=\"https://wikipedia.org/wiki/Winchester_Repeating_Arms_Company\" title=\"Winchester Repeating Arms Company\">Winchester Repeating Arms Company</a> (b. 1810)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Winchester Repeating Arms Company", "link": "https://wikipedia.org/wiki/Winchester_Repeating_Arms_Company"}]}, {"year": "1892", "text": "<PERSON>, Scottish theologian and scholar (b. 1821)", "html": "1892 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish theologian and scholar (b. 1821)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish theologian and scholar (b. 1821)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1906", "text": "<PERSON>, American fencer, engineer, and academic (b. 1872)", "html": "1906 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(fencer)\" class=\"mw-redirect\" title=\"<PERSON> (fencer)\"><PERSON></a>, American fencer, engineer, and academic (b. 1872)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(fencer)\" class=\"mw-redirect\" title=\"<PERSON> (fencer)\"><PERSON></a>, American fencer, engineer, and academic (b. 1872)", "links": [{"title": "<PERSON> (fencer)", "link": "https://wikipedia.org/wiki/<PERSON>(fencer)"}]}, {"year": "1909", "text": "<PERSON>, German-born chemist and British industrialist who discovered the metal carbonyls (b. 1839)", "html": "1909 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German-born chemist and British industrialist who discovered the <a href=\"https://wikipedia.org/wiki/Metal_carbonyl\" title=\"Metal carbonyl\">metal carbonyls</a> (b. 1839)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German-born chemist and British industrialist who discovered the <a href=\"https://wikipedia.org/wiki/Metal_carbonyl\" title=\"Metal carbonyl\">metal carbonyls</a> (b. 1839)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Ludwig_<PERSON>d"}, {"title": "Metal carbonyl", "link": "https://wikipedia.org/wiki/Metal_carbonyl"}]}, {"year": "1913", "text": "<PERSON>, Governor of Liechtenstein (b. 1852)", "html": "1913 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON> Maur\"><PERSON></a>, Governor of Liechtenstein (b. 1852)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON> Maur\"><PERSON></a>, Governor of Liechtenstein (b. 1852)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1918", "text": "<PERSON>, Slovenian author, poet, and playwright (b. 1876)", "html": "1918 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Slovenian author, poet, and playwright (b. 1876)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Slovenian author, poet, and playwright (b. 1876)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1920", "text": "<PERSON>, South African author and activist (b. 1855)", "html": "1920 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, South African author and activist (b. 1855)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, South African author and activist (b. 1855)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1936", "text": "<PERSON><PERSON>, American baseball player (b. 1875)", "html": "1936 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American baseball player (b. 1875)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American baseball player (b. 1875)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>"}]}, {"year": "1937", "text": "<PERSON><PERSON>, Estonian theorist and politician (b. 1884)", "html": "1937 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Estonian theorist and politician (b. 1884)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Estonian theorist and politician (b. 1884)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1937", "text": "<PERSON>, English architect and painter (b. 1853)", "html": "1937 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English architect and painter (b. 1853)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English architect and painter (b. 1853)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1938", "text": "<PERSON>, Norwegian historian and educator, Nobel Prize laureate (b. 1869)", "html": "1938 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Norwegian historian and educator, <a href=\"https://wikipedia.org/wiki/Nobel_Peace_Prize\" title=\"Nobel Peace Prize\">Nobel Prize</a> laureate (b. 1869)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Norwegian historian and educator, <a href=\"https://wikipedia.org/wiki/Nobel_Peace_Prize\" title=\"Nobel Peace Prize\">Nobel Prize</a> laureate (b. 1869)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "Nobel Peace Prize", "link": "https://wikipedia.org/wiki/Nobel_Peace_Prize"}]}, {"year": "1941", "text": "<PERSON>, Jr., American pilot and poet (b. 1922)", "html": "1941 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>,_Jr.\" class=\"mw-redirect\" title=\"<PERSON>, Jr.\"><PERSON>, Jr.</a>, American pilot and poet (b. 1922)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>,_Jr.\" class=\"mw-redirect\" title=\"<PERSON>, Jr.\"><PERSON>, Jr.</a>, American pilot and poet (b. 1922)", "links": [{"title": "<PERSON>, Jr.", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>,_Jr."}]}, {"year": "1941", "text": "<PERSON><PERSON>, French mathematician and academic (b. 1856)", "html": "1941 - <a href=\"https://wikipedia.org/wiki/%C3%89mile_Picard\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, French mathematician and academic (b. 1856)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/%C3%89mile_Picard\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, French mathematician and academic (b. 1856)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/%C3%89mile_Picard"}]}, {"year": "1945", "text": "<PERSON>, French physicist and academic (b. 1867)", "html": "1945 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French physicist and academic (b. 1867)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French physicist and academic (b. 1867)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1950", "text": "<PERSON>, New Zealand astronomer and author (b. 1893)", "html": "1950 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, New Zealand astronomer and author (b. 1893)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, New Zealand astronomer and author (b. 1893)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1951", "text": "<PERSON>, Turkish general (b. 1882)", "html": "1951 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C4%9Flal%C4%B1\" title=\"<PERSON>\"><PERSON></a>, Turkish general (b. 1882)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C4%9Flal%C4%B1\" title=\"<PERSON>\"><PERSON></a>, Turkish general (b. 1882)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Mustafa_Mu%C4%9Flal%C4%B1"}]}, {"year": "1951", "text": "<PERSON><PERSON><PERSON>, Iraqi Turkmen poet and writer  (b. 1881)", "html": "1951 - <a href=\"https://wikipedia.org/wiki/Hijri_Dede\" title=\"Hijri Dede\"><PERSON><PERSON><PERSON></a>, Iraqi Turkmen poet and writer (b. 1881)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Hijri_Dede\" title=\"Hijri Dede\"><PERSON><PERSON><PERSON></a>, Iraqi Turkmen poet and writer (b. 1881)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Hijri_Dede"}]}, {"year": "1953", "text": "<PERSON><PERSON><PERSON>, Turkish journalist and director (b. 1896)", "html": "1953 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Turkish journalist and director (b. 1896)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Turkish journalist and director (b. 1896)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Seda<PERSON>_Si<PERSON>vi"}]}, {"year": "1957", "text": "<PERSON><PERSON><PERSON><PERSON>, French actress, director, producer, and screenwriter (b. 1889)", "html": "1957 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, French actress, director, producer, and screenwriter (b. 1889)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, French actress, director, producer, and screenwriter (b. 1889)", "links": [{"title": "Musidora", "link": "https://wikipedia.org/wiki/Musidora"}]}, {"year": "1959", "text": "<PERSON>, American baseball player and manager (b. 1900)", "html": "1959 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player and manager (b. 1900)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player and manager (b. 1900)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1964", "text": "<PERSON>, American singer-songwriter (b. 1931)", "html": "1964 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter (b. 1931)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter (b. 1931)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1964", "text": "<PERSON>, American actor (b. 1888)", "html": "1964 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor (b. 1888)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor (b. 1888)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1966", "text": "<PERSON>, American psychologist, specialist in juvenile psychology (b. 1881)", "html": "1966 - <a href=\"https://wikipedia.org/wiki/Augusta_Fox_Bronner\" title=\"Augusta Fox Bronner\"><PERSON></a>, American psychologist, specialist in juvenile psychology (b. 1881)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Augusta_Fox_Bronner\" title=\"Augusta Fox Bronner\"><PERSON></a>, American psychologist, specialist in juvenile psychology (b. 1881)", "links": [{"title": "<PERSON> Bronner", "link": "https://wikipedia.org/wiki/Augusta_Fox_Bronner"}]}, {"year": "1968", "text": "<PERSON>, Estonian painter and author (b. 1910)", "html": "1968 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Estonian painter and author (b. 1910)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Estonian painter and author (b. 1910)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1968", "text": "<PERSON>, American publisher (b. 1891)", "html": "1968 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American publisher (b. 1891)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American publisher (b. 1891)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1971", "text": "<PERSON>, American businessman, co-founded McDonald's (b. 1902)", "html": "1971 - <a href=\"https://wikipedia.org/wiki/<PERSON>_and_<PERSON>_<PERSON>\" title=\"<PERSON> and <PERSON>\"><PERSON></a>, American businessman, co-founded <a href=\"https://wikipedia.org/wiki/<PERSON>%27s\" title=\"McDonald's\"><PERSON>'s</a> (b. 1902)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_and_<PERSON>_<PERSON>\" title=\"<PERSON> and <PERSON>\"><PERSON></a>, American businessman, co-founded <a href=\"https://wikipedia.org/wiki/<PERSON>%27s\" title=\"McDonald's\"><PERSON>'s</a> (b. 1902)", "links": [{"title": "<PERSON> and <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_and_<PERSON>_<PERSON>"}, {"title": "McDonald's", "link": "https://wikipedia.org/wiki/<PERSON>%27s"}]}, {"year": "1975", "text": "<PERSON>, American singer (b. 1908)", "html": "1975 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer (b. 1908)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer (b. 1908)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1975", "text": "<PERSON><PERSON>, Turkish philosopher, author, and poet (b. 1905)", "html": "1975 - <a href=\"https://wikipedia.org/wiki/Nihal_Ats%C4%B1z\" title=\"<PERSON><PERSON> Atsız\"><PERSON><PERSON></a>, Turkish philosopher, author, and poet (b. 1905)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Nihal_Ats%C4%B1z\" title=\"<PERSON><PERSON> Atsız\"><PERSON><PERSON></a>, Turkish philosopher, author, and poet (b. 1905)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Nihal_Ats%C4%B1z"}]}, {"year": "1978", "text": "<PERSON>, American biochemist and academic, Nobel Prize laureate (b. 1901)", "html": "1978 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American biochemist and academic, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Chemistry\" title=\"Nobel Prize in Chemistry\">Nobel Prize</a> laureate (b. 1901)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American biochemist and academic, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Chemistry\" title=\"Nobel Prize in Chemistry\">Nobel Prize</a> laureate (b. 1901)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "Nobel Prize in Chemistry", "link": "https://wikipedia.org/wiki/Nobel_Prize_in_Chemistry"}]}, {"year": "1978", "text": "<PERSON>, American baseball player and manager (b. 1920)", "html": "1978 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%27Dea\" title=\"<PERSON>\"><PERSON></a>, American baseball player and manager (b. 1920)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%27Dea\" title=\"<PERSON>\"><PERSON></a>, American baseball player and manager (b. 1920)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_O%27Dea"}]}, {"year": "1979", "text": "<PERSON>, American psychologist and author (b. 1904)", "html": "1979 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American psychologist and author (b. 1904)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American psychologist and author (b. 1904)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1983", "text": "<PERSON>, Guyanese-English general (b. 1897)", "html": "1983 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Guyanese-English general (b. 1897)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Guyanese-English general (b. 1897)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1984", "text": "<PERSON><PERSON>, German-American author, poet, and scholar (b. 1911)", "html": "1984 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, German-American author, poet, and scholar (b. 1911)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, German-American author, poet, and scholar (b. 1911)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1984", "text": "<PERSON>, American director, producer and actor (b. 1894)", "html": "1984 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American director, producer and actor (b. 1894)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American director, producer and actor (b. 1894)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1987", "text": "<PERSON><PERSON> <PERSON><PERSON>, Indian author and academic (b. 1923)", "html": "1987 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> <PERSON><PERSON>\"><PERSON><PERSON> <PERSON><PERSON></a>, Indian author and academic (b. 1923)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> <PERSON><PERSON>\"><PERSON><PERSON> <PERSON><PERSON></a>, Indian author and academic (b. 1923)", "links": [{"title": "<PERSON><PERSON> <PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "1989", "text": "<PERSON>, American photographer (b. 1895)", "html": "1989 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American photographer (b. 1895)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American photographer (b. 1895)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1991", "text": "<PERSON>, American actor, comedian, game show host/panelist, and television personality (b. 1921)", "html": "1991 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor, comedian, game show host/panelist, and television personality (b. 1921)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor, comedian, game show host/panelist, and television personality (b. 1921)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "1991", "text": "<PERSON><PERSON>, Swedish author and critic (b. 1906)", "html": "1991 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Swedish author and critic (b. 1906)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Swedish author and critic (b. 1906)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1994", "text": "<PERSON>, American archaeologist and scholar (b. 1900)", "html": "1994 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(archaeologist)\" title=\"<PERSON> (archaeologist)\"><PERSON></a>, American archaeologist and scholar (b. 1900)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(archaeologist)\" title=\"<PERSON> (archaeologist)\"><PERSON></a>, American archaeologist and scholar (b. 1900)", "links": [{"title": "<PERSON> (archaeologist)", "link": "https://wikipedia.org/wiki/<PERSON>_(archaeologist)"}]}, {"year": "1995", "text": "<PERSON>, American minister and philosopher (b. 1948)", "html": "1995 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American minister and philosopher (b. 1948)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American minister and philosopher (b. 1948)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1996", "text": "<PERSON>, English cartoonist, author, and publisher, co-founded Private Eye (b. 1937)", "html": "1996 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English cartoonist, author, and publisher, co-founded <i><a href=\"https://wikipedia.org/wiki/Private_Eye\" title=\"Private Eye\">Private Eye</a></i> (b. 1937)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English cartoonist, author, and publisher, co-founded <i><a href=\"https://wikipedia.org/wiki/Private_Eye\" title=\"Private Eye\">Private Eye</a></i> (b. 1937)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Private Eye", "link": "https://wikipedia.org/wiki/Private_Eye"}]}, {"year": "1997", "text": "<PERSON>, English spy (b. 1914)", "html": "1997 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English spy (b. 1914)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English spy (b. 1914)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1997", "text": "<PERSON>, English guitarist and composer (b. 1949)", "html": "1997 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English guitarist and composer (b. 1949)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English guitarist and composer (b. 1949)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1998", "text": "<PERSON>, French physicist and mathematician (b. 1915)", "html": "1998 - <a href=\"https://wikipedia.org/wiki/Andr%C3%A9_<PERSON><PERSON><PERSON>owicz\" title=\"<PERSON>\"><PERSON></a>, French physicist and mathematician (b. 1915)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Andr%C3%A9_<PERSON><PERSON><PERSON>owicz\" title=\"<PERSON>\"><PERSON></a>, French physicist and mathematician (b. 1915)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Andr%C3%A9_<PERSON><PERSON><PERSON><PERSON>icz"}]}, {"year": "1998", "text": "<PERSON>, American singer-songwriter (b. 1968)", "html": "1998 - <a href=\"https://wikipedia.org/wiki/Lynn_Strait\" title=\"Lynn Strait\"><PERSON></a>, American singer-songwriter (b. 1968)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Lynn_Strait\" title=\"Lynn Strait\"><PERSON></a>, American singer-songwriter (b. 1968)", "links": [{"title": "Lynn Strait", "link": "https://wikipedia.org/wiki/Lynn_Strait"}]}, {"year": "2000", "text": "<PERSON><PERSON><PERSON>, Pakistani politician and diplomat (b. 1915)", "html": "2000 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>kramu<PERSON>\"><PERSON><PERSON><PERSON></a>, Pakistani politician and diplomat (b. 1915)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Pakistani politician and diplomat (b. 1915)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON><PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "2000", "text": "<PERSON>, American actor (b. 1916)", "html": "2000 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(American_actor)\" title=\"<PERSON> (American actor)\"><PERSON></a>, American actor (b. 1916)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(American_actor)\" title=\"<PERSON> (American actor)\"><PERSON></a>, American actor (b. 1916)", "links": [{"title": "<PERSON> (American actor)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(American_actor)"}]}, {"year": "2001", "text": "<PERSON><PERSON>, Zambian lawyer and politician, first Prime Minister of Zambia (b. 1930)", "html": "2001 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Zambian lawyer and politician, first <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Zambia\" title=\"Prime Minister of Zambia\">Prime Minister of Zambia</a> (b. 1930)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Zambian lawyer and politician, first <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Zambia\" title=\"Prime Minister of Zambia\">Prime Minister of Zambia</a> (b. 1930)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Mainz<PERSON>_<PERSON>na"}, {"title": "Prime Minister of Zambia", "link": "https://wikipedia.org/wiki/Prime_Minister_of_Zambia"}]}, {"year": "2003", "text": "<PERSON><PERSON>, Ivorian author and playwright (b. 1927)", "html": "2003 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Ivorian author and playwright (b. 1927)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Ivorian author and playwright (b. 1927)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "2004", "text": "<PERSON>, Argentinian footballer (b. 1962)", "html": "2004 - <a href=\"https://wikipedia.org/wiki/Jos%C3%A9_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Argentinian footballer (b. 1962)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Jos%C3%A9_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Argentinian footballer (b. 1962)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Jos%C3%A9_<PERSON>_<PERSON>"}]}, {"year": "2004", "text": "<PERSON>, New Zealand runner and coach (b. 1917)", "html": "2004 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, New Zealand runner and coach (b. 1917)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, New Zealand runner and coach (b. 1917)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2008", "text": "<PERSON><PERSON>, American model (b. 1923)", "html": "2008 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> Page\"><PERSON><PERSON></a>, American model (b. 1923)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American model (b. 1923)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "2010", "text": "<PERSON>, American football player (b. 1922)", "html": "2010 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player (b. 1922)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player (b. 1922)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2011", "text": "<PERSON>, American cardinal (b. 1935)", "html": "2011 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American cardinal (b. 1935)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American cardinal (b. 1935)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2012", "text": "<PERSON><PERSON><PERSON>, Russian soprano and actress (b. 1926)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Russian soprano and actress (b. 1926)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Russian soprano and actress (b. 1926)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "2012", "text": "<PERSON><PERSON>, Polish-Israeli rabbi and scholar (b. 1933)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"Men<PERSON>\"><PERSON><PERSON></a>, Polish-Israeli rabbi and scholar (b. 1933)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Polish-Israeli rabbi and scholar (b. 1933)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "2012", "text": "<PERSON>, Indian-American sitar player and composer (b. 1920)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Indian-American <a href=\"https://wikipedia.org/wiki/Sitar\" title=\"Sitar\">sitar</a> player and composer (b. 1920)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Indian-American <a href=\"https://wikipedia.org/wiki/Sitar\" title=\"Sitar\">sitar</a> player and composer (b. 1920)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "<PERSON>ar", "link": "https://wikipedia.org/wiki/Sitar"}]}, {"year": "2013", "text": "<PERSON><PERSON>, Portuguese painter and architect (b. 1920)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/Na<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Portuguese painter and architect (b. 1920)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Na<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Portuguese painter and architect (b. 1920)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Na<PERSON>_<PERSON><PERSON>"}]}, {"year": "2013", "text": "<PERSON>, Canadian-American author and academic (b. 1929)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian-American author and academic (b. 1929)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian-American author and academic (b. 1929)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2013", "text": "<PERSON> (boxer), Mexican boxer (b. 1973)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%<PERSON><PERSON><PERSON><PERSON>_(boxer)\" title=\"<PERSON> (boxer)\"><PERSON> (boxer)</a>, Mexican boxer (b. 1973)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%<PERSON><PERSON><PERSON><PERSON>_(boxer)\" title=\"<PERSON> (boxer)\"><PERSON> (boxer)</a>, Mexican boxer (b. 1973)", "links": [{"title": "<PERSON> (boxer)", "link": "https://wikipedia.org/wiki/<PERSON>_J%C3%<PERSON><PERSON><PERSON><PERSON>_(boxer)"}]}, {"year": "2013", "text": "Sheikh <PERSON><PERSON>, Indian philosopher and scholar (b. 1942)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/Sheikh_<PERSON><PERSON>_<PERSON>\" title=\"Sheikh <PERSON><PERSON>\">Sheikh <PERSON><PERSON></a>, Indian philosopher and scholar (b. 1942)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Sheikh_<PERSON><PERSON>_<PERSON>\" title=\"Sheikh <PERSON><PERSON>\">Sheikh <PERSON><PERSON></a>, Indian philosopher and scholar (b. 1942)", "links": [{"title": "Sheikh <PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON><PERSON>"}]}, {"year": "2014", "text": "<PERSON>, German conductor and director (b. 1929)", "html": "2014 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German conductor and director (b. 1929)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German conductor and director (b. 1929)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2015", "text": "<PERSON><PERSON>, Kazakh academic and politician (b. 1939)", "html": "2015 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Kazakh academic and politician (b. 1939)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Kazakh academic and politician (b. 1939)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>"}]}, {"year": "2015", "text": "<PERSON><PERSON> <PERSON>, Canadian businessman, philanthropist, and academic (b. 1933)", "html": "2015 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON> <PERSON></a>, Canadian businessman, philanthropist, and academic (b. 1933)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON> <PERSON></a>, Canadian businessman, philanthropist, and academic (b. 1933)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "2015", "text": "<PERSON><PERSON>, Indian painter and sculptor (b. 1972)", "html": "2015 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>hya<PERSON>\" title=\"<PERSON><PERSON>hya<PERSON>\"><PERSON><PERSON></a>, Indian painter and sculptor (b. 1972)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>\" title=\"<PERSON><PERSON>hya<PERSON>\"><PERSON><PERSON></a>, Indian painter and sculptor (b. 1972)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON><PERSON>y"}]}, {"year": "2015", "text": "<PERSON> \"<PERSON> <PERSON>\" <PERSON>, American basketball player (b. 1962)", "html": "2015 - <a href=\"https://wikipedia.org/wiki/<PERSON>_%22Hot_Rod%22_<PERSON>\" title='<PERSON> \"Hot Rod\" Williams'><PERSON> \"Hot Rod\" <PERSON></a>, American basketball player (b. 1962)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_%22Hot_Rod%22_<PERSON>\" title='<PERSON> \"Hot Rod\" Williams'><PERSON> \"Hot Rod\" <PERSON></a>, American basketball player (b. 1962)", "links": [{"title": "<PERSON> \"Hot Rod\" Williams", "link": "https://wikipedia.org/wiki/<PERSON>_%22Hot_Rod%22_Williams"}]}, {"year": "2015", "text": "<PERSON>, Australian architect (b. 1933)", "html": "2015 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian architect (b. 1933)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian architect (b. 1933)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2017", "text": "<PERSON>, British TV presenter (b. 1957)", "html": "2017 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, British TV presenter (b. 1957)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, British TV presenter (b. 1957)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2020", "text": "<PERSON>, New Zealand intelligence researcher. (b. 1934)", "html": "2020 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(academic)\" title=\"<PERSON> (academic)\"><PERSON></a>, New Zealand intelligence researcher. (b. 1934)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(academic)\" title=\"<PERSON> (academic)\"><PERSON></a>, New Zealand intelligence researcher. (b. 1934)", "links": [{"title": "<PERSON> (academic)", "link": "https://wikipedia.org/wiki/<PERSON>_(academic)"}]}, {"year": "2021", "text": "<PERSON>, American author (b. 1941)", "html": "2021 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author (b. 1941)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author (b. 1941)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2023", "text": "<PERSON>, American actor (b. 1962)", "html": "2023 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor (b. 1962)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor (b. 1962)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "2024", "text": "<PERSON>, American billionaire businessman (b. 1942)", "html": "2024 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American billionaire businessman (b. 1942)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American billionaire businessman (b. 1942)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "2024", "text": "<PERSON><PERSON><PERSON>, Afghan politician and warlord (b. 1966)", "html": "2024 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Afghan politician and warlord (b. 1966)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Afghan politician and warlord (b. 1966)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "2024", "text": "<PERSON><PERSON><PERSON><PERSON>, Indian musician, singer and composer (b. 1934)", "html": "2024 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_Upadhyay\" title=\"<PERSON><PERSON><PERSON><PERSON> Upadhyay\"><PERSON><PERSON><PERSON><PERSON>ad<PERSON></a>, Indian musician, singer and composer (b. 1934)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_Upadhyay\" title=\"<PERSON><PERSON><PERSON><PERSON> Upadhyay\"><PERSON><PERSON><PERSON><PERSON>ad<PERSON></a>, Indian musician, singer and composer (b. 1934)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_Upadhyay"}]}]}}