{"date": "March 21", "url": "https://wikipedia.org/wiki/March_21", "data": {"Events": [{"year": "537", "text": "Siege of Rome: King <PERSON><PERSON><PERSON> attempts to assault the northern and eastern city walls, but is repulsed at the Praenestine Gate, known as the Vivarium, by the defenders under the Byzantine generals <PERSON><PERSON><PERSON> and <PERSON><PERSON><PERSON>.", "html": "537 - <a href=\"https://wikipedia.org/wiki/Siege_of_Rome_(537%E2%80%93538)\" title=\"Siege of Rome (537-538)\">Siege of Rome</a>: King <a href=\"https://wikipedia.org/wiki/Vitiges\" title=\"Vitiges\">Vitiges</a> attempts to assault the northern and eastern <a href=\"https://wikipedia.org/wiki/Aurelian_Walls\" title=\"Aurelian Walls\">city walls</a>, but is repulsed at the <a href=\"https://wikipedia.org/wiki/Porta_Maggiore\" title=\"Porta Maggiore\">Praenestine Gate</a>, known as the <i><a href=\"https://wikipedia.org/wiki/Vivarium_(Rome)\" title=\"Vivarium (Rome)\">Vivarium</a></i>, by the defenders under the <a href=\"https://wikipedia.org/wiki/Byzantine_Empire\" title=\"Byzantine Empire\">Byzantine</a> generals <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_(magister_militum)\" title=\"<PERSON><PERSON><PERSON> (magister militum)\"><PERSON><PERSON><PERSON></a> and <a href=\"https://wikipedia.org/wiki/Peranius_the_Iberian\" title=\"Peranius the Iberian\">Peranius</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Siege_of_Rome_(537%E2%80%93538)\" title=\"Siege of Rome (537-538)\">Siege of Rome</a>: King <a href=\"https://wikipedia.org/wiki/Vitiges\" title=\"Vitiges\">Vitiges</a> attempts to assault the northern and eastern <a href=\"https://wikipedia.org/wiki/Aurelian_Walls\" title=\"Aurelian Walls\">city walls</a>, but is repulsed at the <a href=\"https://wikipedia.org/wiki/Porta_Maggiore\" title=\"Porta Maggiore\">Praenestine Gate</a>, known as the <i><a href=\"https://wikipedia.org/wiki/Vivarium_(Rome)\" title=\"Vivarium (Rome)\">Vivarium</a></i>, by the defenders under the <a href=\"https://wikipedia.org/wiki/Byzantine_Empire\" title=\"Byzantine Empire\">Byzantine</a> generals <a href=\"https://wikipedia.org/wiki/Be<PERSON><PERSON>_(magister_militum)\" title=\"<PERSON><PERSON><PERSON> (magister militum)\"><PERSON><PERSON><PERSON></a> and <a href=\"https://wikipedia.org/wiki/Peranius_the_Iberian\" title=\"Peranius the Iberian\">Peranius</a>.", "links": [{"title": "Siege of Rome (537-538)", "link": "https://wikipedia.org/wiki/Siege_of_Rome_(537%E2%80%93538)"}, {"title": "Vitiges", "link": "https://wikipedia.org/wiki/Vitiges"}, {"title": "Aurelian Walls", "link": "https://wikipedia.org/wiki/Aurelian_Walls"}, {"title": "Porta Maggiore", "link": "https://wikipedia.org/wiki/Porta_Maggiore"}, {"title": "Vivarium (Rome)", "link": "https://wikipedia.org/wiki/Vivarium_(Rome)"}, {"title": "Byzantine Empire", "link": "https://wikipedia.org/wiki/Byzantine_Empire"}, {"title": "<PERSON><PERSON><PERSON> (magister militum)", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_(magister_militum)"}, {"title": "<PERSON><PERSON><PERSON> the Iberian", "link": "https://wikipedia.org/wiki/<PERSON>anius_the_Iberian"}]}, {"year": "630", "text": "Emperor <PERSON><PERSON><PERSON> returns the True Cross, one of the holiest Christian relics, to Jerusalem.", "html": "630 - Emperor <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a> returns the <a href=\"https://wikipedia.org/wiki/True_Cross\" title=\"True Cross\">True Cross</a>, one of the holiest Christian <a href=\"https://wikipedia.org/wiki/Relic\" title=\"Relic\">relics</a>, to <a href=\"https://wikipedia.org/wiki/Jerusalem\" title=\"Jerusalem\">Jerusalem</a>.", "no_year_html": "Emperor <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a> returns the <a href=\"https://wikipedia.org/wiki/True_Cross\" title=\"True Cross\">True Cross</a>, one of the holiest Christian <a href=\"https://wikipedia.org/wiki/Relic\" title=\"Relic\">relics</a>, to <a href=\"https://wikipedia.org/wiki/Jerusalem\" title=\"Jerusalem\">Jerusalem</a>.", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>"}, {"title": "True Cross", "link": "https://wikipedia.org/wiki/True_Cross"}, {"title": "Relic", "link": "https://wikipedia.org/wiki/Relic"}, {"title": "Jerusalem", "link": "https://wikipedia.org/wiki/Jerusalem"}]}, {"year": "717", "text": "Battle of Vincy between <PERSON> and <PERSON><PERSON><PERSON><PERSON>.", "html": "717 - <a href=\"https://wikipedia.org/wiki/Battle_of_Vincy\" title=\"Battle of Vincy\">Battle of Vincy</a> between <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> and <a href=\"https://wikipedia.org/wiki/Ragenfrid\" title=\"Ragenfrid\">Ragenfrid</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Battle_of_Vincy\" title=\"Battle of Vincy\">Battle of Vincy</a> between <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> and <a href=\"https://wikipedia.org/wiki/Ragenfrid\" title=\"Ragenfrid\">Ragenfrid</a>.", "links": [{"title": "Battle of Vincy", "link": "https://wikipedia.org/wiki/Battle_of_Vincy"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Ragenfrid"}]}, {"year": "1152", "text": "Annulment of the marriage of King <PERSON> of France and Queen <PERSON> of Aquitaine.", "html": "1152 - Annulment of the marriage of King <a href=\"https://wikipedia.org/wiki/Louis_VII_of_France\" title=\"Louis VII of France\"><PERSON> of France</a> and Queen <a href=\"https://wikipedia.org/wiki/Eleanor_of_Aquitaine\" title=\"<PERSON> of Aquitaine\"><PERSON> of Aquitaine</a>.", "no_year_html": "Annulment of the marriage of King <a href=\"https://wikipedia.org/wiki/Louis_VII_of_France\" title=\"Louis VII of France\"><PERSON> of France</a> and Queen <a href=\"https://wikipedia.org/wiki/Eleanor_of_Aquitaine\" title=\"<PERSON> of Aquitaine\"><PERSON> of Aquitaine</a>.", "links": [{"title": "<PERSON> of France", "link": "https://wikipedia.org/wiki/Louis_VII_of_France"}, {"title": "<PERSON> of Aquitaine", "link": "https://wikipedia.org/wiki/Eleanor_of_Aquitaine"}]}, {"year": "1180", "text": "Emperor <PERSON><PERSON><PERSON> accedes to the throne of Japan.", "html": "1180 - <a href=\"https://wikipedia.org/wiki/Emperor_<PERSON><PERSON><PERSON>\" title=\"Emperor <PERSON><PERSON><PERSON>\">Emperor <PERSON><PERSON><PERSON></a> accedes to the throne of Japan.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Emperor_<PERSON><PERSON><PERSON>\" title=\"Emperor <PERSON><PERSON><PERSON>\">Emperor <PERSON><PERSON><PERSON></a> accedes to the throne of Japan.", "links": [{"title": "Emperor <PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON><PERSON>"}]}, {"year": "1556", "text": "On the day of his execution in Oxford, former archbishop of Canterbury <PERSON> deviates from the scripted sermon by renouncing the recantations he has made and adds, \"And as for the pope, I refuse him, as <PERSON>'s enemy, and Anti<PERSON><PERSON> with all his false doctrine.\"", "html": "1556 - On the day of his execution in <a href=\"https://wikipedia.org/wiki/Oxford\" title=\"Oxford\">Oxford</a>, former <a href=\"https://wikipedia.org/wiki/Archbishop_of_Canterbury\" title=\"Archbishop of Canterbury\">archbishop of Canterbury</a> <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> deviates from the scripted sermon by renouncing the recantations he has made and adds, \"And as for the pope, I refuse him, as <PERSON>'s enemy, and <a href=\"https://wikipedia.org/wiki/Antichrist\" title=\"Antichrist\">Antichrist</a> with all his false doctrine.\"", "no_year_html": "On the day of his execution in <a href=\"https://wikipedia.org/wiki/Oxford\" title=\"Oxford\">Oxford</a>, former <a href=\"https://wikipedia.org/wiki/Archbishop_of_Canterbury\" title=\"Archbishop of Canterbury\">archbishop of Canterbury</a> <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> deviates from the scripted sermon by renouncing the recantations he has made and adds, \"And as for the pope, I refuse him, as <PERSON>'s enemy, and <a href=\"https://wikipedia.org/wiki/Antichrist\" title=\"Antichrist\">Antichrist</a> with all his false doctrine.\"", "links": [{"title": "Oxford", "link": "https://wikipedia.org/wiki/Oxford"}, {"title": "Archbishop of Canterbury", "link": "https://wikipedia.org/wiki/Archbishop_of_Canterbury"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Antichrist", "link": "https://wikipedia.org/wiki/Antichrist"}]}, {"year": "1788", "text": "A fire in New Orleans leaves most of the town in ruins.", "html": "1788 - <a href=\"https://wikipedia.org/wiki/Great_New_Orleans_Fire_(1788)\" title=\"Great New Orleans Fire (1788)\">A fire in New Orleans</a> leaves most of the town in ruins.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Great_New_Orleans_Fire_(1788)\" title=\"Great New Orleans Fire (1788)\">A fire in New Orleans</a> leaves most of the town in ruins.", "links": [{"title": "Great New Orleans Fire (1788)", "link": "https://wikipedia.org/wiki/Great_New_Orleans_Fire_(1788)"}]}, {"year": "1800", "text": "With the church leadership driven out of Rome during an armed conflict, <PERSON> is crowned Pope in Venice with a temporary papal tiara made of papier-mâché.", "html": "1800 - With the <a href=\"https://wikipedia.org/wiki/Papal_States\" title=\"Papal States\">church</a> leadership driven out of <a href=\"https://wikipedia.org/wiki/Rome\" title=\"Rome\">Rome</a> during an armed conflict, <a href=\"https://wikipedia.org/wiki/Pope_Pius_VII\" title=\"Pope Pius VII\"><PERSON> VII</a> is <a href=\"https://wikipedia.org/wiki/Papal_coronation\" title=\"Papal coronation\">crowned Pope</a> in <a href=\"https://wikipedia.org/wiki/Venice\" title=\"Venice\">Venice</a> with a <a href=\"https://wikipedia.org/wiki/List_of_papal_tiaras_in_existence\" title=\"List of papal tiaras in existence\">temporary papal tiara made of papier-mâché</a>.", "no_year_html": "With the <a href=\"https://wikipedia.org/wiki/Papal_States\" title=\"Papal States\">church</a> leadership driven out of <a href=\"https://wikipedia.org/wiki/Rome\" title=\"Rome\">Rome</a> during an armed conflict, <a href=\"https://wikipedia.org/wiki/<PERSON>_Pius_VII\" title=\"Pope Pius VII\"><PERSON> VII</a> is <a href=\"https://wikipedia.org/wiki/Papal_coronation\" title=\"Papal coronation\">crowned Pope</a> in <a href=\"https://wikipedia.org/wiki/Venice\" title=\"Venice\">Venice</a> with a <a href=\"https://wikipedia.org/wiki/List_of_papal_tiaras_in_existence\" title=\"List of papal tiaras in existence\">temporary papal tiara made of papier-mâché</a>.", "links": [{"title": "Papal States", "link": "https://wikipedia.org/wiki/Papal_States"}, {"title": "Rome", "link": "https://wikipedia.org/wiki/Rome"}, {"title": "<PERSON> <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "Papal coronation", "link": "https://wikipedia.org/wiki/Papal_coronation"}, {"title": "Venice", "link": "https://wikipedia.org/wiki/Venice"}, {"title": "List of papal tiaras in existence", "link": "https://wikipedia.org/wiki/List_of_papal_tiaras_in_existence"}]}, {"year": "1801", "text": "The Battle of Alexandria is fought between British and French forces near the ruins of Nicopolis near Alexandria in Egypt.", "html": "1801 - The <a href=\"https://wikipedia.org/wiki/Battle_of_Alexandria_(1801)\" title=\"Battle of Alexandria (1801)\">Battle of Alexandria</a> is fought between <a href=\"https://wikipedia.org/wiki/United_Kingdom_of_Great_Britain_and_Ireland\" title=\"United Kingdom of Great Britain and Ireland\">British</a> and <a href=\"https://wikipedia.org/wiki/French_First_Republic\" title=\"French First Republic\">French</a> forces near the ruins of Nicopolis near <a href=\"https://wikipedia.org/wiki/Alexandria\" title=\"Alexandria\">Alexandria</a> in <a href=\"https://wikipedia.org/wiki/Egypt\" title=\"Egypt\">Egypt</a>.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Battle_of_Alexandria_(1801)\" title=\"Battle of Alexandria (1801)\">Battle of Alexandria</a> is fought between <a href=\"https://wikipedia.org/wiki/United_Kingdom_of_Great_Britain_and_Ireland\" title=\"United Kingdom of Great Britain and Ireland\">British</a> and <a href=\"https://wikipedia.org/wiki/French_First_Republic\" title=\"French First Republic\">French</a> forces near the ruins of Nicopolis near <a href=\"https://wikipedia.org/wiki/Alexandria\" title=\"Alexandria\">Alexandria</a> in <a href=\"https://wikipedia.org/wiki/Egypt\" title=\"Egypt\">Egypt</a>.", "links": [{"title": "Battle of Alexandria (1801)", "link": "https://wikipedia.org/wiki/Battle_of_Alexandria_(1801)"}, {"title": "United Kingdom of Great Britain and Ireland", "link": "https://wikipedia.org/wiki/United_Kingdom_of_Great_Britain_and_Ireland"}, {"title": "French First Republic", "link": "https://wikipedia.org/wiki/French_First_Republic"}, {"title": "Alexandria", "link": "https://wikipedia.org/wiki/Alexandria"}, {"title": "Egypt", "link": "https://wikipedia.org/wiki/Egypt"}]}, {"year": "1804", "text": "Code Napoléon is adopted as French civil law.", "html": "1804 - <a href=\"https://wikipedia.org/wiki/Napoleonic_Code\" title=\"Napoleonic Code\">Code Napoléon</a> is adopted as French <a href=\"https://wikipedia.org/wiki/Civil_law_(legal_system)\" title=\"Civil law (legal system)\">civil law</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Napoleonic_Code\" title=\"Napoleonic Code\">Code Napoléon</a> is adopted as French <a href=\"https://wikipedia.org/wiki/Civil_law_(legal_system)\" title=\"Civil law (legal system)\">civil law</a>.", "links": [{"title": "Napoleonic Code", "link": "https://wikipedia.org/wiki/Napoleonic_Code"}, {"title": "Civil law (legal system)", "link": "https://wikipedia.org/wiki/Civil_law_(legal_system)"}]}, {"year": "1814", "text": "Napoleonic Wars: Austrian forces repel French troops in the Battle of Arcis-sur-Aube.", "html": "1814 - <a href=\"https://wikipedia.org/wiki/Napoleonic_Wars\" title=\"Napoleonic Wars\">Napoleonic Wars</a>: <a href=\"https://wikipedia.org/wiki/Austrian_Empire\" title=\"Austrian Empire\">Austrian</a> forces repel <a href=\"https://wikipedia.org/wiki/First_French_Empire\" title=\"First French Empire\">French</a> troops in the <a href=\"https://wikipedia.org/wiki/Battle_of_Arcis-sur-Aube\" title=\"Battle of Arcis-sur-Aube\">Battle of Arcis-sur-Aube</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Napoleonic_Wars\" title=\"Napoleonic Wars\">Napoleonic Wars</a>: <a href=\"https://wikipedia.org/wiki/Austrian_Empire\" title=\"Austrian Empire\">Austrian</a> forces repel <a href=\"https://wikipedia.org/wiki/First_French_Empire\" title=\"First French Empire\">French</a> troops in the <a href=\"https://wikipedia.org/wiki/Battle_of_Arcis-sur-Aube\" title=\"Battle of Arcis-sur-Aube\">Battle of Arcis-sur-Aube</a>.", "links": [{"title": "Napoleonic Wars", "link": "https://wikipedia.org/wiki/Napoleonic_Wars"}, {"title": "Austrian Empire", "link": "https://wikipedia.org/wiki/Austrian_Empire"}, {"title": "First French Empire", "link": "https://wikipedia.org/wiki/First_French_Empire"}, {"title": "Battle of Arcis-sur-Aube", "link": "https://wikipedia.org/wiki/Battle_of_Arcis-sur-Aube"}]}, {"year": "1821", "text": "Greek War of Independence: Greek revolutionaries seize Kalavryta.", "html": "1821 - <a href=\"https://wikipedia.org/wiki/Greek_War_of_Independence\" title=\"Greek War of Independence\">Greek War of Independence</a>: Greek revolutionaries seize <a href=\"https://wikipedia.org/wiki/Kalav<PERSON><PERSON>\" title=\"Kalav<PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Greek_War_of_Independence\" title=\"Greek War of Independence\">Greek War of Independence</a>: Greek revolutionaries seize <a href=\"https://wikipedia.org/wiki/Ka<PERSON><PERSON><PERSON>\" title=\"Kalav<PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>.", "links": [{"title": "Greek War of Independence", "link": "https://wikipedia.org/wiki/Greek_War_of_Independence"}, {"title": "Ka<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Ka<PERSON><PERSON>ta"}]}, {"year": "1829", "text": "The Wellington-Winchilsea duel takes place in London involving the Prime Minister the <PERSON> of Wellington", "html": "1829 - The <a href=\"https://wikipedia.org/wiki/Wellington%E2%80%93Winchilsea_duel\" title=\"Wellington-Winchilsea duel\">Wellington-Winchilsea duel</a> takes place in <a href=\"https://wikipedia.org/wiki/London\" title=\"London\">London</a> involving the <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_the_United_Kingdom\" title=\"Prime Minister of the United Kingdom\">Prime Minister</a> the <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_Wellington\" class=\"mw-redirect\" title=\"Duke of Wellington\">Duke of Wellington</a>", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Wellington%E2%80%93Winchilsea_duel\" title=\"Wellington-Winchilsea duel\">Wellington-Winchilsea duel</a> takes place in <a href=\"https://wikipedia.org/wiki/London\" title=\"London\">London</a> involving the <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_the_United_Kingdom\" title=\"Prime Minister of the United Kingdom\">Prime Minister</a> the <a href=\"https://wikipedia.org/wiki/<PERSON>_of_Wellington\" class=\"mw-redirect\" title=\"Duke of Wellington\">Duke of Wellington</a>", "links": [{"title": "Wellington-Win<PERSON>ls<PERSON> duel", "link": "https://wikipedia.org/wiki/Wellington%E2%80%93Winchilsea_duel"}, {"title": "London", "link": "https://wikipedia.org/wiki/London"}, {"title": "Prime Minister of the United Kingdom", "link": "https://wikipedia.org/wiki/Prime_Minister_of_the_United_Kingdom"}, {"title": "<PERSON> of Wellington", "link": "https://wikipedia.org/wiki/<PERSON>_of_Wellington"}]}, {"year": "1844", "text": "The Baháʼí calendar begins. This is the first day of the first year of the Baháʼí calendar. It is annually celebrated by members of the Baháʼí Faith as the Baháʼí New Year or Náw-Rúz.", "html": "1844 - The <a href=\"https://wikipedia.org/wiki/Bah%C3%A1%CA%BC%C3%AD_calendar\" title=\"Baháʼí calendar\">Baháʼí calendar</a> begins. This is the first day of the first year of the Baháʼí calendar. It is annually celebrated by members of the <a href=\"https://wikipedia.org/wiki/Bah%C3%A1%CA%BC%C3%AD_Faith\" title=\"Baháʼí Faith\">Baháʼí Faith</a> as the Baháʼí New Year or <a href=\"https://wikipedia.org/wiki/Bah%C3%A1%CA%BC%C3%AD_Naw-R%C3%BAz\" title=\"Baháʼ<PERSON> Naw-Rúz\">Náw-Rúz</a>.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Bah%C3%A1%CA%BC%C3%AD_calendar\" title=\"Baháʼí calendar\">Baháʼí calendar</a> begins. This is the first day of the first year of the Baháʼí calendar. It is annually celebrated by members of the <a href=\"https://wikipedia.org/wiki/Bah%C3%A1%CA%BC%C3%AD_Faith\" title=\"Baháʼí Faith\">Baháʼí Faith</a> as the Baháʼí New Year or <a href=\"https://wikipedia.org/wiki/Bah%C3%A1%CA%BC%C3%AD_Naw-R%C3%BAz\" title=\"Baháʼ<PERSON> Naw-Rúz\">Náw-Rúz</a>.", "links": [{"title": "Baháʼí calendar", "link": "https://wikipedia.org/wiki/Bah%C3%A1%CA%BC%C3%AD_calendar"}, {"title": "Baháʼí Faith", "link": "https://wikipedia.org/wiki/Bah%C3%A1%CA%BC%C3%AD_Faith"}, {"title": "Baháʼí Naw-Rúz", "link": "https://wikipedia.org/wiki/Bah%C3%A1%CA%BC%C3%AD_Naw-R%C3%BAz"}]}, {"year": "1861", "text": "<PERSON> gives the Cornerstone Speech.", "html": "1861 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> gives the <a href=\"https://wikipedia.org/wiki/Cornerstone_Speech\" title=\"Cornerstone Speech\">Cornerstone Speech</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> gives the <a href=\"https://wikipedia.org/wiki/Cornerstone_Speech\" title=\"Cornerstone Speech\">Cornerstone Speech</a>.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "Cornerstone Speech", "link": "https://wikipedia.org/wiki/Cornerstone_Speech"}]}, {"year": "1871", "text": "<PERSON> is appointed as the first Chancellor of the German Empire.", "html": "1871 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> is appointed as the first <a href=\"https://wikipedia.org/wiki/Chancellor_of_the_German_Empire\" class=\"mw-redirect\" title=\"Chancellor of the German Empire\">Chancellor of the German Empire</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> is appointed as the first <a href=\"https://wikipedia.org/wiki/Chancellor_of_the_German_Empire\" class=\"mw-redirect\" title=\"Chancellor of the German Empire\">Chancellor of the German Empire</a>.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Chancellor of the German Empire", "link": "https://wikipedia.org/wiki/Chancellor_of_the_German_Empire"}]}, {"year": "1871", "text": "Journalist <PERSON> begins his trek to find the missionary and explorer <PERSON>.", "html": "1871 - Journalist <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> begins his trek to find the missionary and explorer <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>.", "no_year_html": "Journalist <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> begins his trek to find the missionary and explorer <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1918", "text": "World War I: The first phase of the German spring offensive, Operation Michael, begins.", "html": "1918 - <a href=\"https://wikipedia.org/wiki/World_War_I\" title=\"World War I\">World War I</a>: The first phase of the <a href=\"https://wikipedia.org/wiki/German_spring_offensive\" title=\"German spring offensive\">German spring offensive</a>, <a href=\"https://wikipedia.org/wiki/Operation_Michael\" title=\"Operation Michael\">Operation Michael</a>, begins.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/World_War_I\" title=\"World War I\">World War I</a>: The first phase of the <a href=\"https://wikipedia.org/wiki/German_spring_offensive\" title=\"German spring offensive\">German spring offensive</a>, <a href=\"https://wikipedia.org/wiki/Operation_Michael\" title=\"Operation Michael\">Operation Michael</a>, begins.", "links": [{"title": "World War I", "link": "https://wikipedia.org/wiki/World_War_I"}, {"title": "German spring offensive", "link": "https://wikipedia.org/wiki/German_spring_offensive"}, {"title": "Operation Michael", "link": "https://wikipedia.org/wiki/Operation_Michael"}]}, {"year": "1919", "text": "The Hungarian Soviet Republic is established becoming the first Communist government to be formed in Europe after the October Revolution in Russia.", "html": "1919 - The <a href=\"https://wikipedia.org/wiki/Hungarian_Soviet_Republic\" title=\"Hungarian Soviet Republic\">Hungarian Soviet Republic</a> is established becoming the first <a href=\"https://wikipedia.org/wiki/Communism\" title=\"Communism\">Communist</a> government to be formed in Europe after the <a href=\"https://wikipedia.org/wiki/October_Revolution\" title=\"October Revolution\">October Revolution</a> in <a href=\"https://wikipedia.org/wiki/Russian_Empire\" title=\"Russian Empire\">Russia</a>.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Hungarian_Soviet_Republic\" title=\"Hungarian Soviet Republic\">Hungarian Soviet Republic</a> is established becoming the first <a href=\"https://wikipedia.org/wiki/Communism\" title=\"Communism\">Communist</a> government to be formed in Europe after the <a href=\"https://wikipedia.org/wiki/October_Revolution\" title=\"October Revolution\">October Revolution</a> in <a href=\"https://wikipedia.org/wiki/Russian_Empire\" title=\"Russian Empire\">Russia</a>.", "links": [{"title": "Hungarian Soviet Republic", "link": "https://wikipedia.org/wiki/Hungarian_Soviet_Republic"}, {"title": "Communism", "link": "https://wikipedia.org/wiki/Communism"}, {"title": "October Revolution", "link": "https://wikipedia.org/wiki/October_Revolution"}, {"title": "Russian Empire", "link": "https://wikipedia.org/wiki/Russian_Empire"}]}, {"year": "1921", "text": "The New Economic Policy is implemented by the Bolshevik Party in response to the economic failure as a result of war communism.", "html": "1921 - The <a href=\"https://wikipedia.org/wiki/New_Economic_Policy\" title=\"New Economic Policy\">New Economic Policy</a> is implemented by the <a href=\"https://wikipedia.org/wiki/Bolshevik\" class=\"mw-redirect\" title=\"Bolshevik\">Bolshevik</a> Party in response to the economic failure as a result of <a href=\"https://wikipedia.org/wiki/War_communism\" title=\"War communism\">war communism</a>.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/New_Economic_Policy\" title=\"New Economic Policy\">New Economic Policy</a> is implemented by the <a href=\"https://wikipedia.org/wiki/Bolshevik\" class=\"mw-redirect\" title=\"Bolshevik\">Bolshevik</a> Party in response to the economic failure as a result of <a href=\"https://wikipedia.org/wiki/War_communism\" title=\"War communism\">war communism</a>.", "links": [{"title": "New Economic Policy", "link": "https://wikipedia.org/wiki/New_Economic_Policy"}, {"title": "Bolshevik", "link": "https://wikipedia.org/wiki/Bolshevik"}, {"title": "War communism", "link": "https://wikipedia.org/wiki/War_communism"}]}, {"year": "1925", "text": "The Butler Act prohibits the teaching of human evolution in Tennessee.", "html": "1925 - The <a href=\"https://wikipedia.org/wiki/Butler_Act\" title=\"Butler Act\">Butler Act</a> prohibits the teaching of <a href=\"https://wikipedia.org/wiki/Human_evolution\" title=\"Human evolution\">human evolution</a> in <a href=\"https://wikipedia.org/wiki/Tennessee\" title=\"Tennessee\">Tennessee</a>.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Butler_Act\" title=\"Butler Act\">Butler Act</a> prohibits the teaching of <a href=\"https://wikipedia.org/wiki/Human_evolution\" title=\"Human evolution\">human evolution</a> in <a href=\"https://wikipedia.org/wiki/Tennessee\" title=\"Tennessee\">Tennessee</a>.", "links": [{"title": "Butler Act", "link": "https://wikipedia.org/wiki/Butler_Act"}, {"title": "Human evolution", "link": "https://wikipedia.org/wiki/Human_evolution"}, {"title": "Tennessee", "link": "https://wikipedia.org/wiki/Tennessee"}]}, {"year": "1925", "text": "<PERSON><PERSON><PERSON> is removed from office after being impeached as the President of the Provisional Government of the Republic of Korea.", "html": "1925 - <a href=\"https://wikipedia.org/wiki/<PERSON>yn<PERSON>_<PERSON>\" title=\"Syngman Rhee\">Syn<PERSON></a> is removed from office after being <a href=\"https://wikipedia.org/wiki/Impeachment\" title=\"Impeachment\">impeached</a> as the President of the <a href=\"https://wikipedia.org/wiki/Provisional_Government_of_the_Republic_of_Korea\" title=\"Provisional Government of the Republic of Korea\">Provisional Government of the Republic of Korea</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>yn<PERSON>_<PERSON>\" title=\"Syngman Rhee\">Syn<PERSON></a> is removed from office after being <a href=\"https://wikipedia.org/wiki/Impeachment\" title=\"Impeachment\">impeached</a> as the President of the <a href=\"https://wikipedia.org/wiki/Provisional_Government_of_the_Republic_of_Korea\" title=\"Provisional Government of the Republic of Korea\">Provisional Government of the Republic of Korea</a>.", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}, {"title": "Impeachment", "link": "https://wikipedia.org/wiki/Impeachment"}, {"title": "Provisional Government of the Republic of Korea", "link": "https://wikipedia.org/wiki/Provisional_Government_of_the_Republic_of_Korea"}]}, {"year": "1925", "text": "<PERSON><PERSON>'s opera <PERSON>'enfant et les sortilèges, to a libretto by <PERSON><PERSON>, is premiered at the Opéra de Monte-Carlo.", "html": "1925 - <PERSON><PERSON>'s opera <i><a href=\"https://wikipedia.org/wiki/L%27enfant_et_les_sortil%C3%A8ges\" title=\"L'enfant et les sortilèges\">L'enfant et les sortilèges</a></i>, to a libretto by <a href=\"https://wikipedia.org/wiki/Cole<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, is premiered at the <a href=\"https://wikipedia.org/wiki/Op%C3%A9ra_de_Monte-Carlo\" title=\"Opéra de Monte-Carlo\">Opéra de Monte-Carlo</a>.", "no_year_html": "<PERSON><PERSON>'s opera <i><a href=\"https://wikipedia.org/wiki/L%27enfant_et_les_sortil%C3%A8ges\" title=\"L'enfant et les sortilèges\">L'enfant et les sortilèges</a></i>, to a libretto by <a href=\"https://wikipedia.org/wiki/Cole<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, is premiered at the <a href=\"https://wikipedia.org/wiki/Op%C3%A9ra_de_Monte-Carlo\" title=\"Opéra de Monte-Carlo\">Opéra de Monte-Carlo</a>.", "links": [{"title": "L'enfant et les sortilèges", "link": "https://wikipedia.org/wiki/L%27enfant_et_les_sortil%C3%A8ges"}, {"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Colette"}, {"title": "Opéra de Monte-Carlo", "link": "https://wikipedia.org/wiki/Op%C3%A9ra_<PERSON>_<PERSON>-<PERSON>"}]}, {"year": "1928", "text": "<PERSON> is presented with the Medal of Honor for the first solo trans-Atlantic flight.", "html": "1928 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> is presented with the <a href=\"https://wikipedia.org/wiki/Medal_of_Honor\" title=\"Medal of Honor\">Medal of Honor</a> for the first solo trans-Atlantic flight.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> is presented with the <a href=\"https://wikipedia.org/wiki/Medal_of_Honor\" title=\"Medal of Honor\">Medal of Honor</a> for the first solo trans-Atlantic flight.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}, {"title": "Medal of Honor", "link": "https://wikipedia.org/wiki/Medal_of_Honor"}]}, {"year": "1934", "text": "The landmark Australian Eastern Mission led by <PERSON> departs on its three-month tour of East and South-East Asia.", "html": "1934 - The landmark <a href=\"https://wikipedia.org/wiki/Australian_Eastern_Mission\" title=\"Australian Eastern Mission\">Australian Eastern Mission</a> led by <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a> departs on its three-month tour of East and South-East Asia.", "no_year_html": "The landmark <a href=\"https://wikipedia.org/wiki/Australian_Eastern_Mission\" title=\"Australian Eastern Mission\">Australian Eastern Mission</a> led by <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a> departs on its three-month tour of East and South-East Asia.", "links": [{"title": "Australian Eastern Mission", "link": "https://wikipedia.org/wiki/Australian_Eastern_Mission"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1935", "text": "Shah of Iran <PERSON><PERSON> formally asks the international community to call Persia by its native name, Iran.", "html": "1935 - <a href=\"https://wikipedia.org/wiki/List_of_monarchs_of_Persia\" class=\"mw-redirect\" title=\"List of monarchs of Persia\"><PERSON> of Iran</a> <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a> formally asks the international community to call <a href=\"https://wikipedia.org/wiki/Iran\" title=\"Iran\">Persia</a> by its native name, <i><a href=\"https://wikipedia.org/wiki/Name_of_Iran\" title=\"Name of Iran\">Iran</a></i>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/List_of_monarchs_of_Persia\" class=\"mw-redirect\" title=\"List of monarchs of Persia\"><PERSON> of Iran</a> <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a> formally asks the international community to call <a href=\"https://wikipedia.org/wiki/Iran\" title=\"Iran\">Persia</a> by its native name, <i><a href=\"https://wikipedia.org/wiki/Name_of_Iran\" title=\"Name of Iran\">Iran</a></i>.", "links": [{"title": "List of monarchs of Persia", "link": "https://wikipedia.org/wiki/List_of_monarchs_of_Persia"}, {"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}, {"title": "Iran", "link": "https://wikipedia.org/wiki/Iran"}, {"title": "Name of Iran", "link": "https://wikipedia.org/wiki/Name_of_Iran"}]}, {"year": "1937", "text": "Ponce massacre: Nineteen unarmed civilians in Ponce, Puerto Rico are gunned down by police in a terrorist attack ordered by the US-appointed Governor, <PERSON><PERSON><PERSON>.", "html": "1937 - <a href=\"https://wikipedia.org/wiki/Ponce_massacre\" title=\"Ponce massacre\">Ponce massacre</a>: Nineteen unarmed civilians in <a href=\"https://wikipedia.org/wiki/Ponce,_Puerto_Rico\" title=\"Ponce, Puerto Rico\">Ponce, Puerto Rico</a> are gunned down by police in a terrorist attack ordered by the US-appointed Governor, <a href=\"https://wikipedia.org/wiki/Blanton_Winship\" title=\"Blanton Winship\">Blanton Winship</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Ponce_massacre\" title=\"Ponce massacre\">Ponce massacre</a>: Nineteen unarmed civilians in <a href=\"https://wikipedia.org/wiki/Ponce,_Puerto_Rico\" title=\"Ponce, Puerto Rico\">Ponce, Puerto Rico</a> are gunned down by police in a terrorist attack ordered by the US-appointed Governor, <a href=\"https://wikipedia.org/wiki/Blanton_Winship\" title=\"Blanton Winship\">Blanton Winship</a>.", "links": [{"title": "Ponce massacre", "link": "https://wikipedia.org/wiki/Ponce_massacre"}, {"title": "Ponce, Puerto Rico", "link": "https://wikipedia.org/wiki/Ponce,_Puerto_Rico"}, {"title": "<PERSON><PERSON><PERSON> Winship", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_Winship"}]}, {"year": "1943", "text": "Wehrmacht officer <PERSON> plots to assassinate <PERSON> by using a suicide bomb, but the plan falls through; <PERSON> is able to defuse the bomb in time and avoid suspicion.", "html": "1943 - <a href=\"https://wikipedia.org/wiki/Wehrmacht\" title=\"Wehrmacht\">Wehrmacht</a> officer <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON></a> plots to assassinate <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Adolf <PERSON>\"><PERSON></a> by using a suicide bomb, but the plan falls through; <PERSON> is able to defuse the bomb in time and avoid suspicion.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Wehrmacht\" title=\"Wehrmacht\">Wehrmacht</a> officer <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON></a> plots to assassinate <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"Adolf <PERSON>\"><PERSON></a> by using a suicide bomb, but the plan falls through; <PERSON> is able to defuse the bomb in time and avoid suspicion.", "links": [{"title": "Wehrmacht", "link": "https://wikipedia.org/wiki/Wehrmacht"}, {"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1945", "text": "World War II: British troops liberate Mandalay, Burma.", "html": "1945 - <a href=\"https://wikipedia.org/wiki/World_War_II\" title=\"World War II\">World War II</a>: British troops liberate <a href=\"https://wikipedia.org/wiki/Mandalay\" title=\"Mandalay\">Mandalay</a>, <a href=\"https://wikipedia.org/wiki/Burma\" class=\"mw-redirect\" title=\"Burma\">Burma</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/World_War_II\" title=\"World War II\">World War II</a>: British troops liberate <a href=\"https://wikipedia.org/wiki/Mandalay\" title=\"Mandalay\">Mandalay</a>, <a href=\"https://wikipedia.org/wiki/Burma\" class=\"mw-redirect\" title=\"Burma\">Burma</a>.", "links": [{"title": "World War II", "link": "https://wikipedia.org/wiki/World_War_II"}, {"title": "Mandalay", "link": "https://wikipedia.org/wiki/Mandalay"}, {"title": "Burma", "link": "https://wikipedia.org/wiki/Burma"}]}, {"year": "1945", "text": "World War II: Operation Carthage: Royal Air Force planes bomb Gestapo headquarters in Copenhagen, Denmark. They also accidentally hit a school, killing 125 civilians.", "html": "1945 - World War II: <a href=\"https://wikipedia.org/wiki/Operation_Carthage\" title=\"Operation Carthage\">Operation Carthage</a>: <a href=\"https://wikipedia.org/wiki/Royal_Air_Force\" title=\"Royal Air Force\">Royal Air Force</a> planes bomb <a href=\"https://wikipedia.org/wiki/Gestapo\" title=\"Gestapo\">Gestapo</a> headquarters in <a href=\"https://wikipedia.org/wiki/Copenhagen\" title=\"Copenhagen\">Copenhagen</a>, Denmark. They also accidentally hit a school, killing 125 civilians.", "no_year_html": "World War II: <a href=\"https://wikipedia.org/wiki/Operation_Carthage\" title=\"Operation Carthage\">Operation Carthage</a>: <a href=\"https://wikipedia.org/wiki/Royal_Air_Force\" title=\"Royal Air Force\">Royal Air Force</a> planes bomb <a href=\"https://wikipedia.org/wiki/Gestapo\" title=\"Gestapo\">Gestapo</a> headquarters in <a href=\"https://wikipedia.org/wiki/Copenhagen\" title=\"Copenhagen\">Copenhagen</a>, Denmark. They also accidentally hit a school, killing 125 civilians.", "links": [{"title": "Operation Carthage", "link": "https://wikipedia.org/wiki/Operation_Carthage"}, {"title": "Royal Air Force", "link": "https://wikipedia.org/wiki/Royal_Air_Force"}, {"title": "Gestapo", "link": "https://wikipedia.org/wiki/Gestapo"}, {"title": "Copenhagen", "link": "https://wikipedia.org/wiki/Copenhagen"}]}, {"year": "1945", "text": "World War II: Bulgaria and the Soviet Union successfully complete their defense of the north bank of the Drava River as the Battle of the Transdanubian Hills concludes.", "html": "1945 - World War II: <a href=\"https://wikipedia.org/wiki/Kingdom_of_Bulgaria\" class=\"mw-redirect\" title=\"Kingdom of Bulgaria\">Bulgaria</a> and the <a href=\"https://wikipedia.org/wiki/Soviet_Union\" title=\"Soviet Union\">Soviet Union</a> successfully complete their defense of the north bank of the <a href=\"https://wikipedia.org/wiki/Drava\" title=\"Drava\">Drava</a> River as the <a href=\"https://wikipedia.org/wiki/Battle_of_the_Transdanubian_Hills\" title=\"Battle of the Transdanubian Hills\">Battle of the Transdanubian Hills</a> concludes.", "no_year_html": "World War II: <a href=\"https://wikipedia.org/wiki/Kingdom_of_Bulgaria\" class=\"mw-redirect\" title=\"Kingdom of Bulgaria\">Bulgaria</a> and the <a href=\"https://wikipedia.org/wiki/Soviet_Union\" title=\"Soviet Union\">Soviet Union</a> successfully complete their defense of the north bank of the <a href=\"https://wikipedia.org/wiki/Drava\" title=\"Drava\">Drava</a> River as the <a href=\"https://wikipedia.org/wiki/Battle_of_the_Transdanubian_Hills\" title=\"Battle of the Transdanubian Hills\">Battle of the Transdanubian Hills</a> concludes.", "links": [{"title": "Kingdom of Bulgaria", "link": "https://wikipedia.org/wiki/Kingdom_of_Bulgaria"}, {"title": "Soviet Union", "link": "https://wikipedia.org/wiki/Soviet_Union"}, {"title": "Drava", "link": "https://wikipedia.org/wiki/Drava"}, {"title": "Battle of the Transdanubian Hills", "link": "https://wikipedia.org/wiki/Battle_of_the_Transdanubian_Hills"}]}, {"year": "1946", "text": "The Los Angeles Rams sign <PERSON>, making him the first African American player in professional American football since 1933.", "html": "1946 - The <a href=\"https://wikipedia.org/wiki/Los_Angeles_Rams\" title=\"Los Angeles Rams\">Los Angeles Rams</a> sign <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>(American_football)\" title=\"<PERSON> (American football)\"><PERSON></a>, making him the first <a href=\"https://wikipedia.org/wiki/African_American\" class=\"mw-redirect\" title=\"African American\">African American</a> <a href=\"https://wikipedia.org/wiki/Black_players_in_professional_American_football\" title=\"Black players in professional American football\">player</a> in professional <a href=\"https://wikipedia.org/wiki/American_football\" title=\"American football\">American football</a> since 1933.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Los_Angeles_Rams\" title=\"Los Angeles Rams\">Los Angeles Rams</a> sign <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(American_football)\" title=\"<PERSON> (American football)\"><PERSON></a>, making him the first <a href=\"https://wikipedia.org/wiki/African_American\" class=\"mw-redirect\" title=\"African American\">African American</a> <a href=\"https://wikipedia.org/wiki/Black_players_in_professional_American_football\" title=\"Black players in professional American football\">player</a> in professional <a href=\"https://wikipedia.org/wiki/American_football\" title=\"American football\">American football</a> since 1933.", "links": [{"title": "Los Angeles Rams", "link": "https://wikipedia.org/wiki/Los_Angeles_Rams"}, {"title": "<PERSON> (American football)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(American_football)"}, {"title": "African American", "link": "https://wikipedia.org/wiki/African_American"}, {"title": "Black players in professional American football", "link": "https://wikipedia.org/wiki/Black_players_in_professional_American_football"}, {"title": "American football", "link": "https://wikipedia.org/wiki/American_football"}]}, {"year": "1952", "text": "<PERSON> presents the Moondog Coronation Ball, the first rock and roll concert, in Cleveland, Ohio.", "html": "1952 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> presents the <a href=\"https://wikipedia.org/wiki/Moondog_Coronation_Ball\" title=\"Moondog Coronation Ball\">Moondog Coronation Ball</a>, the first <a href=\"https://wikipedia.org/wiki/Rock_and_roll\" title=\"Rock and roll\">rock and roll</a> concert, in <a href=\"https://wikipedia.org/wiki/Cleveland,_Ohio\" class=\"mw-redirect\" title=\"Cleveland, Ohio\">Cleveland, Ohio</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> presents the <a href=\"https://wikipedia.org/wiki/Moondog_Coronation_Ball\" title=\"Moondog Coronation Ball\">Moondog Coronation Ball</a>, the first <a href=\"https://wikipedia.org/wiki/Rock_and_roll\" title=\"Rock and roll\">rock and roll</a> concert, in <a href=\"https://wikipedia.org/wiki/Cleveland,_Ohio\" class=\"mw-redirect\" title=\"Cleveland, Ohio\">Cleveland, Ohio</a>.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_Freed"}, {"title": "Moondog Coronation Ball", "link": "https://wikipedia.org/wiki/Moondog_Coronation_Ball"}, {"title": "Rock and roll", "link": "https://wikipedia.org/wiki/Rock_and_roll"}, {"title": "Cleveland, Ohio", "link": "https://wikipedia.org/wiki/Cleveland,_Ohio"}]}, {"year": "1960", "text": "Apartheid: Sharpeville massacre, South Africa: Police open fire on a group of black South African demonstrators, killing 69 and wounding 180.", "html": "1960 - <a href=\"https://wikipedia.org/wiki/Apartheid\" title=\"Apartheid\">Apartheid</a>: <a href=\"https://wikipedia.org/wiki/Sharpeville_massacre\" title=\"Sharpeville massacre\">Sharpeville massacre</a>, South Africa: Police open fire on a group of black South African demonstrators, killing 69 and wounding 180.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Apartheid\" title=\"Apartheid\">Apartheid</a>: <a href=\"https://wikipedia.org/wiki/Sharpeville_massacre\" title=\"Sharpeville massacre\">Sharpeville massacre</a>, South Africa: Police open fire on a group of black South African demonstrators, killing 69 and wounding 180.", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>heid"}, {"title": "Sharpeville massacre", "link": "https://wikipedia.org/wiki/Sharpeville_massacre"}]}, {"year": "1963", "text": "Alcatraz Federal Penitentiary closes.", "html": "1963 - <a href=\"https://wikipedia.org/wiki/Alcatraz_Federal_Penitentiary\" title=\"Alcatraz Federal Penitentiary\">Alcatraz Federal Penitentiary</a> closes.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Alcatraz_Federal_Penitentiary\" title=\"Alcatraz Federal Penitentiary\">Alcatraz Federal Penitentiary</a> closes.", "links": [{"title": "Alcatraz Federal Penitentiary", "link": "https://wikipedia.org/wiki/Alcatraz_Federal_Penitentiary"}]}, {"year": "1965", "text": "Ranger program: NASA launches Ranger 9, the last in a series of uncrewed lunar space probes.", "html": "1965 - <a href=\"https://wikipedia.org/wiki/Ranger_program\" title=\"Ranger program\">Ranger program</a>: <a href=\"https://wikipedia.org/wiki/NASA\" title=\"NASA\">NASA</a> launches <a href=\"https://wikipedia.org/wiki/Ranger_9\" title=\"Ranger 9\">Ranger 9</a>, the last in a series of uncrewed lunar <a href=\"https://wikipedia.org/wiki/Space_probe\" class=\"mw-redirect\" title=\"Space probe\">space probes</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Ranger_program\" title=\"Ranger program\">Ranger program</a>: <a href=\"https://wikipedia.org/wiki/NASA\" title=\"NASA\">NASA</a> launches <a href=\"https://wikipedia.org/wiki/Ranger_9\" title=\"Ranger 9\">Ranger 9</a>, the last in a series of uncrewed lunar <a href=\"https://wikipedia.org/wiki/Space_probe\" class=\"mw-redirect\" title=\"Space probe\">space probes</a>.", "links": [{"title": "Ranger program", "link": "https://wikipedia.org/wiki/Ranger_program"}, {"title": "NASA", "link": "https://wikipedia.org/wiki/NASA"}, {"title": "Ranger 9", "link": "https://wikipedia.org/wiki/<PERSON>_9"}, {"title": "Space probe", "link": "https://wikipedia.org/wiki/Space_probe"}]}, {"year": "1965", "text": "<PERSON> leads 3,200 people on the start of the third and finally successful civil rights march from Selma to Montgomery, Alabama.", "html": "1965 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>.\" title=\"<PERSON> Jr.\"><PERSON> Jr.</a> leads 3,200 people on the start of the third and finally successful <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_to_Montgomery_marches\" title=\"Se<PERSON> to Montgomery marches\">civil rights march</a> from <a href=\"https://wikipedia.org/wiki/Selma,_Alabama\" title=\"Selma, Alabama\">Selma</a> to <a href=\"https://wikipedia.org/wiki/Montgomery,_Alabama\" title=\"Montgomery, Alabama\">Montgomery, Alabama</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>.\" title=\"<PERSON> Jr.\"><PERSON>.</a> leads 3,200 people on the start of the third and finally successful <a href=\"https://wikipedia.org/wiki/Se<PERSON>_to_Montgomery_marches\" title=\"Selma to Montgomery marches\">civil rights march</a> from <a href=\"https://wikipedia.org/wiki/Selma,_Alabama\" title=\"Selma, Alabama\">Selma</a> to <a href=\"https://wikipedia.org/wiki/Montgomery,_Alabama\" title=\"Montgomery, Alabama\">Montgomery, Alabama</a>.", "links": [{"title": "<PERSON> Jr.", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>."}, {"title": "<PERSON><PERSON> to Montgomery marches", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>_marches"}, {"title": "Selma, Alabama", "link": "https://wikipedia.org/wiki/Selma,_Alabama"}, {"title": "Montgomery, Alabama", "link": "https://wikipedia.org/wiki/Montgomery,_Alabama"}]}, {"year": "1968", "text": "Battle of Karameh in Jordan between the Israel Defense Forces and the combined forces of the Jordanian Armed Forces and PLO.", "html": "1968 - <a href=\"https://wikipedia.org/wiki/Battle_of_Karameh\" title=\"Battle of Karameh\">Battle of Karameh</a> in <a href=\"https://wikipedia.org/wiki/Jordan\" title=\"Jordan\">Jordan</a> between the <a href=\"https://wikipedia.org/wiki/Israel_Defense_Forces\" title=\"Israel Defense Forces\">Israel Defense Forces</a> and the combined forces of the <a href=\"https://wikipedia.org/wiki/Jordanian_Armed_Forces\" title=\"Jordanian Armed Forces\">Jordanian Armed Forces</a> and <a href=\"https://wikipedia.org/wiki/PLO\" class=\"mw-redirect\" title=\"PLO\">PLO</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Battle_of_Karameh\" title=\"Battle of Karameh\">Battle of Karameh</a> in <a href=\"https://wikipedia.org/wiki/Jordan\" title=\"Jordan\">Jordan</a> between the <a href=\"https://wikipedia.org/wiki/Israel_Defense_Forces\" title=\"Israel Defense Forces\">Israel Defense Forces</a> and the combined forces of the <a href=\"https://wikipedia.org/wiki/Jordanian_Armed_Forces\" title=\"Jordanian Armed Forces\">Jordanian Armed Forces</a> and <a href=\"https://wikipedia.org/wiki/PLO\" class=\"mw-redirect\" title=\"PLO\">PLO</a>.", "links": [{"title": "Battle of Karameh", "link": "https://wikipedia.org/wiki/Battle_of_Karameh"}, {"title": "Jordan", "link": "https://wikipedia.org/wiki/Jordan"}, {"title": "Israel Defense Forces", "link": "https://wikipedia.org/wiki/Israel_Defense_Forces"}, {"title": "Jordanian Armed Forces", "link": "https://wikipedia.org/wiki/Jordanian_Armed_Forces"}, {"title": "PLO", "link": "https://wikipedia.org/wiki/PLO"}]}, {"year": "1970", "text": "The first Earth Day proclamation is issued by <PERSON>, Mayor of San Francisco.", "html": "1970 - The first <a href=\"https://wikipedia.org/wiki/Earth_Day\" title=\"Earth Day\">Earth Day</a> proclamation is issued by <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, <a href=\"https://wikipedia.org/wiki/Mayor_of_San_Francisco\" title=\"Mayor of San Francisco\">Mayor of San Francisco</a>.", "no_year_html": "The first <a href=\"https://wikipedia.org/wiki/Earth_Day\" title=\"Earth Day\">Earth Day</a> proclamation is issued by <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, <a href=\"https://wikipedia.org/wiki/Mayor_of_San_Francisco\" title=\"Mayor of San Francisco\">Mayor of San Francisco</a>.", "links": [{"title": "Earth Day", "link": "https://wikipedia.org/wiki/Earth_Day"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Mayor of San Francisco", "link": "https://wikipedia.org/wiki/Mayor_of_San_Francisco"}]}, {"year": "1970", "text": "San Diego Comic-Con, the largest pop and culture festival in the world, hosts its inaugural event.", "html": "1970 - <a href=\"https://wikipedia.org/wiki/San_Diego_Comic-Con\" title=\"San Diego Comic-Con\">San Diego Comic-Con</a>, the largest <a href=\"https://wikipedia.org/wiki/Comic_book_convention\" title=\"Comic book convention\">pop and culture festival</a> in the world, hosts its inaugural event.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/San_Diego_Comic-Con\" title=\"San Diego Comic-Con\">San Diego Comic-Con</a>, the largest <a href=\"https://wikipedia.org/wiki/Comic_book_convention\" title=\"Comic book convention\">pop and culture festival</a> in the world, hosts its inaugural event.", "links": [{"title": "San Diego Comic-Con", "link": "https://wikipedia.org/wiki/San_Diego_Comic-Con"}, {"title": "Comic book convention", "link": "https://wikipedia.org/wiki/Comic_book_convention"}]}, {"year": "1980", "text": "Cold War: American President <PERSON> announces a United States boycott of the 1980 Summer Olympics in Moscow to protest the Soviet-Afghan War.", "html": "1980 - <a href=\"https://wikipedia.org/wiki/Cold_War\" title=\"Cold War\">Cold War</a>: American President <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> announces a United States <a href=\"https://wikipedia.org/wiki/1980_Summer_Olympics_boycott\" title=\"1980 Summer Olympics boycott\">boycott</a> of the <a href=\"https://wikipedia.org/wiki/1980_Summer_Olympics\" title=\"1980 Summer Olympics\">1980 Summer Olympics</a> in <a href=\"https://wikipedia.org/wiki/Moscow\" title=\"Moscow\">Moscow</a> to protest the <a href=\"https://wikipedia.org/wiki/Soviet%E2%80%93Afghan_War\" title=\"Soviet-Afghan War\">Soviet-Afghan War</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Cold_War\" title=\"Cold War\">Cold War</a>: American President <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> announces a United States <a href=\"https://wikipedia.org/wiki/1980_Summer_Olympics_boycott\" title=\"1980 Summer Olympics boycott\">boycott</a> of the <a href=\"https://wikipedia.org/wiki/1980_Summer_Olympics\" title=\"1980 Summer Olympics\">1980 Summer Olympics</a> in <a href=\"https://wikipedia.org/wiki/Moscow\" title=\"Moscow\">Moscow</a> to protest the <a href=\"https://wikipedia.org/wiki/Soviet%E2%80%93Afghan_War\" title=\"Soviet-Afghan War\">Soviet-Afghan War</a>.", "links": [{"title": "Cold War", "link": "https://wikipedia.org/wiki/Cold_War"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "1980 Summer Olympics boycott", "link": "https://wikipedia.org/wiki/1980_Summer_Olympics_boycott"}, {"title": "1980 Summer Olympics", "link": "https://wikipedia.org/wiki/1980_Summer_Olympics"}, {"title": "Moscow", "link": "https://wikipedia.org/wiki/Moscow"}, {"title": "Soviet-Afghan War", "link": "https://wikipedia.org/wiki/Soviet%E2%80%93Afghan_War"}]}, {"year": "1983", "text": "The first cases of the 1983 West Bank fainting epidemic begin; Israelis and Palestinians accuse each other of poison gas, but the cause is later determined mostly to be psychosomatic.", "html": "1983 - The first cases of the <a href=\"https://wikipedia.org/wiki/1983_West_Bank_fainting_epidemic\" title=\"1983 West Bank fainting epidemic\">1983 West Bank fainting epidemic</a> begin; <a href=\"https://wikipedia.org/wiki/Israel\" title=\"Israel\">Israelis</a> and <a href=\"https://wikipedia.org/wiki/State_of_Palestine\" class=\"mw-redirect\" title=\"State of Palestine\">Palestinians</a> accuse each other of poison gas, but the cause is later determined mostly to be <a href=\"https://wikipedia.org/wiki/Psychosomatic\" class=\"mw-redirect\" title=\"Psychosomatic\">psychosomatic</a>.", "no_year_html": "The first cases of the <a href=\"https://wikipedia.org/wiki/1983_West_Bank_fainting_epidemic\" title=\"1983 West Bank fainting epidemic\">1983 West Bank fainting epidemic</a> begin; <a href=\"https://wikipedia.org/wiki/Israel\" title=\"Israel\">Israelis</a> and <a href=\"https://wikipedia.org/wiki/State_of_Palestine\" class=\"mw-redirect\" title=\"State of Palestine\">Palestinians</a> accuse each other of poison gas, but the cause is later determined mostly to be <a href=\"https://wikipedia.org/wiki/Psychosomatic\" class=\"mw-redirect\" title=\"Psychosomatic\">psychosomatic</a>.", "links": [{"title": "1983 West Bank fainting epidemic", "link": "https://wikipedia.org/wiki/1983_West_Bank_fainting_epidemic"}, {"title": "Israel", "link": "https://wikipedia.org/wiki/Israel"}, {"title": "State of Palestine", "link": "https://wikipedia.org/wiki/State_of_Palestine"}, {"title": "Psychosomatic", "link": "https://wikipedia.org/wiki/Psychosomatic"}]}, {"year": "1986", "text": "<PERSON><PERSON> became the first African American to win the World Figure Skating Championships", "html": "1986 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a> became the first African American to win the <a href=\"https://wikipedia.org/wiki/World_Figure_Skating_Championships\" title=\"World Figure Skating Championships\">World Figure Skating Championships</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a> became the first African American to win the <a href=\"https://wikipedia.org/wiki/World_Figure_Skating_Championships\" title=\"World Figure Skating Championships\">World Figure Skating Championships</a>", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}, {"title": "World Figure Skating Championships", "link": "https://wikipedia.org/wiki/World_Figure_Skating_Championships"}]}, {"year": "1989", "text": "Transbrasil Flight 801 crashes into a slum near São Paulo/Guarulhos International Airport, killing 25 people.", "html": "1989 - <a href=\"https://wikipedia.org/wiki/Transbrasil_Flight_801\" title=\"Transbrasil Flight 801\">Transbrasil Flight 801</a> crashes into a <a href=\"https://wikipedia.org/wiki/Slum\" title=\"Slum\">slum</a> near <a href=\"https://wikipedia.org/wiki/S%C3%A3o_Paulo/Guarulhos_International_Airport\" title=\"São Paulo/Guarulhos International Airport\">São Paulo/Guarulhos International Airport</a>, killing 25 people.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Transbrasil_Flight_801\" title=\"Transbrasil Flight 801\">Transbrasil Flight 801</a> crashes into a <a href=\"https://wikipedia.org/wiki/Slum\" title=\"Slum\">slum</a> near <a href=\"https://wikipedia.org/wiki/S%C3%A3o_Paulo/Guarulhos_International_Airport\" title=\"São Paulo/Guarulhos International Airport\">São Paulo/Guarulhos International Airport</a>, killing 25 people.", "links": [{"title": "Transbrasil Flight 801", "link": "https://wikipedia.org/wiki/Transbrasil_Flight_801"}, {"title": "Slum", "link": "https://wikipedia.org/wiki/Slum"}, {"title": "São Paulo/Guarulhos International Airport", "link": "https://wikipedia.org/wiki/S%C3%A3o_Paulo/Guarulhos_International_Airport"}]}, {"year": "1990", "text": "Namibia becomes independent after 75 years of South African rule.", "html": "1990 - <a href=\"https://wikipedia.org/wiki/Namibia\" title=\"Namibia\">Namibia</a> becomes independent after 75 years of <a href=\"https://wikipedia.org/wiki/South_Africa\" title=\"South Africa\">South African</a> rule.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Namibia\" title=\"Namibia\">Namibia</a> becomes independent after 75 years of <a href=\"https://wikipedia.org/wiki/South_Africa\" title=\"South Africa\">South African</a> rule.", "links": [{"title": "Namibia", "link": "https://wikipedia.org/wiki/Namibia"}, {"title": "South Africa", "link": "https://wikipedia.org/wiki/South_Africa"}]}, {"year": "1994", "text": "The United Nations Framework Convention on Climate Change enters into force.", "html": "1994 - The <a href=\"https://wikipedia.org/wiki/United_Nations_Framework_Convention_on_Climate_Change\" title=\"United Nations Framework Convention on Climate Change\">United Nations Framework Convention on Climate Change</a> enters into force.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/United_Nations_Framework_Convention_on_Climate_Change\" title=\"United Nations Framework Convention on Climate Change\">United Nations Framework Convention on Climate Change</a> enters into force.", "links": [{"title": "United Nations Framework Convention on Climate Change", "link": "https://wikipedia.org/wiki/United_Nations_Framework_Convention_on_Climate_Change"}]}, {"year": "1999", "text": "<PERSON> and <PERSON> become the first to circumnavigate the Earth in a hot air balloon.", "html": "1999 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> and <a href=\"https://wikipedia.org/wiki/<PERSON>(aeronaut)\" title=\"<PERSON> (aeronaut)\"><PERSON></a> become the first to <a href=\"https://wikipedia.org/wiki/Circumnavigation\" title=\"Circumnavigation\">circumnavigate</a> the Earth in a <a href=\"https://wikipedia.org/wiki/Hot_air_balloon\" title=\"Hot air balloon\">hot air balloon</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> and <a href=\"https://wikipedia.org/wiki/<PERSON>(aeronaut)\" title=\"<PERSON> (aeronaut)\"><PERSON></a> become the first to <a href=\"https://wikipedia.org/wiki/Circumnavigation\" title=\"Circumnavigation\">circumnavigate</a> the Earth in a <a href=\"https://wikipedia.org/wiki/Hot_air_balloon\" title=\"Hot air balloon\">hot air balloon</a>.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "<PERSON> (aeronaut)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(aeronaut)"}, {"title": "Circumnavigation", "link": "https://wikipedia.org/wiki/Circumnavigation"}, {"title": "Hot air balloon", "link": "https://wikipedia.org/wiki/Hot_air_balloon"}]}, {"year": "2000", "text": "Pope <PERSON> makes his first ever pontifical visit to Israel.", "html": "2000 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>\" title=\"Pope <PERSON>\">Pope <PERSON> II</a> makes his first ever <a href=\"https://wikipedia.org/wiki/Roman_Pontifical\" title=\"Roman Pontifical\">pontifical</a> visit to <a href=\"https://wikipedia.org/wiki/Israel\" title=\"Israel\">Israel</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>\" title=\"Pope <PERSON>\">Pope <PERSON> II</a> makes his first ever <a href=\"https://wikipedia.org/wiki/Roman_Pontifical\" title=\"Roman Pontifical\">pontifical</a> visit to <a href=\"https://wikipedia.org/wiki/Israel\" title=\"Israel\">Israel</a>.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>"}, {"title": "Roman Pontifical", "link": "https://wikipedia.org/wiki/Roman_Pontifical"}, {"title": "Israel", "link": "https://wikipedia.org/wiki/Israel"}]}, {"year": "2006", "text": "The social media site X (former Twitter) is founded.", "html": "2006 - The <a href=\"https://wikipedia.org/wiki/Social_media\" title=\"Social media\">social media</a> site <a href=\"https://wikipedia.org/wiki/Twitter\" title=\"Twitter\">X</a> (former Twitter) is founded.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Social_media\" title=\"Social media\">social media</a> site <a href=\"https://wikipedia.org/wiki/Twitter\" title=\"Twitter\">X</a> (former Twitter) is founded.", "links": [{"title": "Social media", "link": "https://wikipedia.org/wiki/Social_media"}, {"title": "Twitter", "link": "https://wikipedia.org/wiki/Twitter"}]}, {"year": "2019", "text": "The 2019 Xiangshui chemical plant explosion occurs, killing at least 47 people and injuring 640 others.", "html": "2019 - The <a href=\"https://wikipedia.org/wiki/2019_Xiangshui_chemical_plant_explosion\" title=\"2019 Xiangshui chemical plant explosion\">2019 Xiangshui chemical plant explosion</a> occurs, killing at least 47 people and injuring 640 others.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/2019_Xiangshui_chemical_plant_explosion\" title=\"2019 Xiangshui chemical plant explosion\">2019 Xiangshui chemical plant explosion</a> occurs, killing at least 47 people and injuring 640 others.", "links": [{"title": "2019 Xiangshui chemical plant explosion", "link": "https://wikipedia.org/wiki/2019_Xiangshui_chemical_plant_explosion"}]}, {"year": "2022", "text": "China Eastern Airlines Flight 5735 crashes in Guangxi, China, killing 132 people.", "html": "2022 - <a href=\"https://wikipedia.org/wiki/China_Eastern_Airlines_Flight_5735\" title=\"China Eastern Airlines Flight 5735\">China Eastern Airlines Flight 5735</a> crashes in <a href=\"https://wikipedia.org/wiki/Guangxi\" title=\"Guangxi\">Guangxi</a>, China, killing 132 people.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/China_Eastern_Airlines_Flight_5735\" title=\"China Eastern Airlines Flight 5735\">China Eastern Airlines Flight 5735</a> crashes in <a href=\"https://wikipedia.org/wiki/Guangxi\" title=\"Guangxi\">Guangxi</a>, China, killing 132 people.", "links": [{"title": "China Eastern Airlines Flight 5735", "link": "https://wikipedia.org/wiki/China_Eastern_Airlines_Flight_5735"}, {"title": "Guangxi", "link": "https://wikipedia.org/wiki/Guangxi"}]}], "Births": [{"year": "927", "text": "Emperor <PERSON><PERSON> of Song (d. 976)", "html": "927 - <a href=\"https://wikipedia.org/wiki/Emperor_<PERSON><PERSON>_of_Song\" title=\"Emperor <PERSON><PERSON> of Song\">Emperor <PERSON><PERSON> of Song</a> (d. 976)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Emperor_<PERSON><PERSON>_of_Song\" title=\"Emperor <PERSON><PERSON> of Song\">Emperor <PERSON><PERSON> of Song</a> (d. 976)", "links": [{"title": "Emperor <PERSON><PERSON> of Song", "link": "https://wikipedia.org/wiki/Emperor_<PERSON><PERSON>_of_Song"}]}, {"year": "1474", "text": "<PERSON>, Italian educator and saint (d. 1540)", "html": "1474 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian educator and saint (d. 1540)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian educator and saint (d. 1540)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1501", "text": "<PERSON>, Baroness <PERSON>, English noble (d. 1558)", "html": "1501 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Baroness_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>, Baroness Cobham\"><PERSON>, Baroness <PERSON></a>, English noble (d. 1558)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>,_Baroness_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>, Baroness <PERSON>\"><PERSON>, Baroness <PERSON></a>, English noble (d. 1558)", "links": [{"title": "<PERSON>, Baroness <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_<PERSON>_<PERSON>"}]}, {"year": "1521", "text": "<PERSON>, Elector of Saxony (d. 1553)", "html": "1521 - <a href=\"https://wikipedia.org/wiki/<PERSON>,_Elector_of_Saxony\" title=\"<PERSON>, Elector of Saxony\"><PERSON>, Elector of Saxony</a> (d. 1553)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>,_Elector_of_Saxony\" title=\"<PERSON>, Elector of Saxony\"><PERSON>, Elector of Saxony</a> (d. 1553)", "links": [{"title": "<PERSON>, Elector of Saxony", "link": "https://wikipedia.org/wiki/<PERSON>,_Elector_of_Saxony"}]}, {"year": "1527", "text": "<PERSON>, German composer and educator (d. 1558)", "html": "1527 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German composer and educator (d. 1558)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German composer and educator (d. 1558)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1555", "text": "<PERSON>, English politician (d. 1615)", "html": "1555 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English politician (d. 1615)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English politician (d. 1615)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1557", "text": "<PERSON>, Countess of Arundel, English countess and poet (d. 1630)", "html": "1557 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Countess_of_Arundel\" title=\"<PERSON>, Countess of Arundel\"><PERSON>, Countess of Arundel</a>, English countess and poet (d. 1630)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>,_Countess_of_Arundel\" title=\"<PERSON>, Countess of Arundel\"><PERSON>, Countess of Arundel</a>, English countess and poet (d. 1630)", "links": [{"title": "<PERSON>, Countess of Arundel", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Countess_of_Arundel"}]}, {"year": "1626", "text": "<PERSON> of Saint <PERSON>, Spanish saint and missionary (d. 1667)", "html": "1626 - <a href=\"https://wikipedia.org/wiki/<PERSON>_of_<PERSON>_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON> of Saint <PERSON>\"><PERSON> of Saint <PERSON></a>, Spanish saint and missionary (d. 1667)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_of_Saint_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON> of Saint <PERSON>\"><PERSON> of Saint <PERSON></a>, Spanish saint and missionary (d. 1667)", "links": [{"title": "<PERSON> of <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1672", "text": "<PERSON>, Italian poet and translator (d. 1742)", "html": "1672 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian poet and translator (d. 1742)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian poet and translator (d. 1742)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1685", "text": "<PERSON>, German Baroque composer and musician (d. 1750)", "html": "1685 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German Baroque composer and musician (d. 1750)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German Baroque composer and musician (d. 1750)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1713", "text": "<PERSON>, Welsh-American merchant and politician (d. 1803)", "html": "1713 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Welsh-American merchant and politician (d. 1803)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Welsh-American merchant and politician (d. 1803)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1716", "text": "<PERSON>, Bohemian organist, composer, and educator (d. 1782)", "html": "1716 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Bohemian organist, composer, and educator (d. 1782)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Bohemian organist, composer, and educator (d. 1782)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1752", "text": "<PERSON>, American inventor (d. 1837)", "html": "1752 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American inventor (d. 1837)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American inventor (d. 1837)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1763", "text": "<PERSON>, German journalist and author (d. 1825)", "html": "1763 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German journalist and author (d. 1825)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German journalist and author (d. 1825)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1768", "text": "<PERSON>, French mathematician and physicist (d. 1830)", "html": "1768 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French mathematician and physicist (d. 1830)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French mathematician and physicist (d. 1830)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1779", "text": "<PERSON>, Marquis of Torre Tagle, Peruvian soldier and politician, 2nd President of Peru (d. 1825)", "html": "1779 - <a href=\"https://wikipedia.org/wiki/Jos%C3%A9_<PERSON>_<PERSON>_<PERSON>_y_<PERSON>carrero,_Marquis_of_Torre_Tagle\" class=\"mw-redirect\" title=\"<PERSON>, Marquis of Torre Tagle\"><PERSON>, Marquis of Torre Tagle</a>, Peruvian soldier and politician, 2nd President of Peru (d. 1825)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Jos%C3%A9_<PERSON>_<PERSON>_<PERSON>_y_Portocarrero,_Marquis_of_Torre_Tagle\" class=\"mw-redirect\" title=\"<PERSON>, Marquis of Torre Tagle\"><PERSON>, Marquis of Torre Tagle</a>, Peruvian soldier and politician, 2nd President of Peru (d. 1825)", "links": [{"title": "<PERSON>, Marquis of Torre Tagle", "link": "https://wikipedia.org/wiki/Jos%C3%A<PERSON>_<PERSON>_<PERSON>_<PERSON>_<PERSON>_<PERSON>,_<PERSON>_of_Torre_<PERSON>le"}]}, {"year": "1802", "text": "<PERSON>, Welsh writer and patron of the arts (d. 1896)", "html": "1802 - <a href=\"https://wikipedia.org/wiki/Augusta_Hall,_Baroness_<PERSON>\" title=\"Augusta Hall, Baroness <PERSON>\"><PERSON></a>, Welsh writer and patron of the arts (d. 1896)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Augusta_Hall,_Baroness_<PERSON>\" title=\"Augusta Hall, Baroness <PERSON>\"><PERSON></a>, Welsh writer and patron of the arts (d. 1896)", "links": [{"title": "<PERSON>, <PERSON>", "link": "https://wikipedia.org/wiki/Augusta_Hall,_<PERSON>_<PERSON>"}]}, {"year": "1806", "text": "<PERSON>, Mexican lawyer and politician, 25th President of Mexico (d. 1872)", "html": "1806 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A1rez\" title=\"<PERSON>\"><PERSON></a>, Mexican lawyer and politician, 25th <a href=\"https://wikipedia.org/wiki/President_of_Mexico\" title=\"President of Mexico\">President of Mexico</a> (d. 1872)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A1rez\" title=\"<PERSON>\"><PERSON></a>, Mexican lawyer and politician, 25th <a href=\"https://wikipedia.org/wiki/President_of_Mexico\" title=\"President of Mexico\">President of Mexico</a> (d. 1872)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Benito_Ju%C3%A1rez"}, {"title": "President of Mexico", "link": "https://wikipedia.org/wiki/President_of_Mexico"}]}, {"year": "1811", "text": "<PERSON>, English priest and educator (d. 1891)", "html": "1811 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English priest and educator (d. 1891)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English priest and educator (d. 1891)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1825", "text": "<PERSON>, Russian soldier and engineer (d. 1890)", "html": "1825 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian soldier and engineer (d. 1890)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian soldier and engineer (d. 1890)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1831", "text": "<PERSON>, English suffragist, educational reformer and author (d. 1906)", "html": "1831 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English suffragist, educational reformer and author (d. 1906)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English suffragist, educational reformer and author (d. 1906)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1835", "text": "<PERSON>, English cricketer (d. 1876)", "html": "1835 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(cricketer)\" title=\"<PERSON> (cricketer)\"><PERSON></a>, English cricketer (d. 1876)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>(cricketer)\" title=\"<PERSON> (cricketer)\"><PERSON></a>, English cricketer (d. 1876)", "links": [{"title": "<PERSON> (cricketer)", "link": "https://wikipedia.org/wiki/<PERSON>_(cricketer)"}]}, {"year": "1839", "text": "<PERSON>st <PERSON>, Russian pianist and composer (d. 1881)", "html": "1839 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON>st <PERSON>\"><PERSON>st <PERSON></a>, Russian pianist and composer (d. 1881)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON>st <PERSON>\"><PERSON><PERSON></a>, Russian pianist and composer (d. 1881)", "links": [{"title": "<PERSON>st <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>"}]}, {"year": "1854", "text": "<PERSON><PERSON>, Australian cricketer and coach (d. 1924)", "html": "1854 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> Bannerman\"><PERSON><PERSON></a>, Australian cricketer and coach (d. 1924)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>man\"><PERSON><PERSON></a>, Australian cricketer and coach (d. 1924)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1857", "text": "<PERSON>, Australian journalist and activist (d. 1943)", "html": "1857 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian journalist and activist (d. 1943)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian journalist and activist (d. 1943)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1859", "text": "<PERSON><PERSON>, American golfer (d. 1938)", "html": "1859 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American golfer (d. 1938)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American golfer (d. 1938)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1865", "text": "<PERSON>, American general (d. 1934)", "html": "1865 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American general (d. 1934)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American general (d. 1934)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1866", "text": "<PERSON><PERSON>, American astronomer and astrophysicist (d. 1952)", "html": "1866 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American astronomer and astrophysicist (d. 1952)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American astronomer and astrophysicist (d. 1952)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Anton<PERSON>_<PERSON>"}]}, {"year": "1867", "text": "<PERSON><PERSON><PERSON>, Jr., American director and producer (d. 1932)", "html": "1867 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>,_Jr.\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON>, Jr.\"><PERSON><PERSON><PERSON>, Jr.</a>, American director and producer (d. 1932)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>,_Jr.\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON>, Jr.\"><PERSON><PERSON><PERSON>, Jr.</a>, American director and producer (d. 1932)", "links": [{"title": "<PERSON><PERSON><PERSON>, Jr.", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>,_Jr."}]}, {"year": "1869", "text": "<PERSON>, Scottish-English golfer and rugby player (d. 1937)", "html": "1869 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(golfer)\" class=\"mw-redirect\" title=\"<PERSON> (golfer)\"><PERSON></a>, Scottish-English golfer and rugby player (d. 1937)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(golfer)\" class=\"mw-redirect\" title=\"<PERSON> (golfer)\"><PERSON></a>, Scottish-English golfer and rugby player (d. 1937)", "links": [{"title": "<PERSON> (golfer)", "link": "https://wikipedia.org/wiki/<PERSON>(golfer)"}]}, {"year": "1874", "text": "<PERSON>, English runner (d. 1901)", "html": "1874 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English runner (d. 1901)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English runner (d. 1901)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1876", "text": "<PERSON>, American runner and hurdler (d. 1968)", "html": "1876 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American runner and hurdler (d. 1968)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American runner and hurdler (d. 1968)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1877", "text": "<PERSON>, French race car driver and pilot (d. 1964)", "html": "1877 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French race car driver and pilot (d. 1964)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French race car driver and pilot (d. 1964)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1878", "text": "<PERSON>, American architect (d. 1944)", "html": "1878 - <a href=\"https://wikipedia.org/wiki/Morris_H<PERSON>_<PERSON>\" title=\"Morris H. Whitehouse\"><PERSON></a>, American architect (d. 1944)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Morris_H._<PERSON>\" title=\"Morris H. Whitehouse\"><PERSON></a>, American architect (d. 1944)", "links": [{"title": "Morris H. <PERSON>", "link": "https://wikipedia.org/wiki/Morris_H._Whitehouse"}]}, {"year": "1880", "text": "<PERSON><PERSON><PERSON>, American actor, director, and producer (d. 1971)", "html": "1880 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"Broncho <PERSON>\"><PERSON><PERSON><PERSON> <PERSON></a>, American actor, director, and producer (d. 1971)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"B<PERSON><PERSON>\"><PERSON><PERSON><PERSON> <PERSON></a>, American actor, director, and producer (d. 1971)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1880", "text": "<PERSON>, German-American painter and academic (d. 1966)", "html": "1880 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German-American painter and academic (d. 1966)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German-American painter and academic (d. 1966)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1882", "text": "<PERSON><PERSON><PERSON><PERSON>, Estonian politician (d. 1963)", "html": "1882 - <a href=\"https://wikipedia.org/wiki/Aleksander_Kesk%C3%BCla\" title=\"Aleksan<PERSON>sküla\"><PERSON><PERSON><PERSON><PERSON></a>, Estonian politician (d. 1963)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Aleksander_Kesk%C3%BCla\" title=\"<PERSON>eksan<PERSON>üla\"><PERSON><PERSON><PERSON><PERSON></a>, Estonian politician (d. 1963)", "links": [{"title": "Aleksander <PERSON>", "link": "https://wikipedia.org/wiki/Aleksander_Kesk%C3%BCla"}]}, {"year": "1884", "text": "<PERSON>, American mathematician (d. 1944)", "html": "1884 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American mathematician (d. 1944)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American mathematician (d. 1944)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1885", "text": "<PERSON>, French actor and director (d. 1952)", "html": "1885 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French actor and director (d. 1952)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French actor and director (d. 1952)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1886", "text": "<PERSON>, American pole vaulter (d. 1973)", "html": "1886 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American pole vaulter (d. 1973)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American pole vaulter (d. 1973)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1887", "text": "<PERSON><PERSON><PERSON>, Australian painter (d. 1935)", "html": "1887 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Australian painter (d. 1935)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Australian painter (d. 1935)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1887", "text": "<PERSON><PERSON>, Hungarian poet, novelist and painter (d. 1967)", "html": "1887 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_Kass%C3%A1k\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Hungarian poet, novelist and painter (d. 1967)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>ss%C3%A1k\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Hungarian poet, novelist and painter (d. 1967)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Lajos_Kass%C3%A1k"}]}, {"year": "1887", "text": "<PERSON><PERSON> <PERSON><PERSON>, Indian philosopher and politician (d. 1954)", "html": "1887 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> <PERSON><PERSON>\"><PERSON><PERSON> <PERSON><PERSON></a>, Indian philosopher and politician (d. 1954)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> <PERSON><PERSON>\"><PERSON><PERSON> <PERSON><PERSON></a>, Indian philosopher and politician (d. 1954)", "links": [{"title": "<PERSON><PERSON> <PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "1889", "text": "<PERSON><PERSON>, American football player and coach (d. 1948)", "html": "1889 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American football player and coach (d. 1948)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American football player and coach (d. 1948)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1894", "text": "<PERSON>, Norwegian textile artist (d. 1970)", "html": "1894 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Norwegian textile artist (d. 1970)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Norwegian textile artist (d. 1970)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1896", "text": "<PERSON>, Austrian mathematician, physicist, and philosopher from the Vienna Circle (d. 1959)", "html": "1896 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Austrian mathematician, physicist, and philosopher from the Vienna Circle (d. 1959)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Austrian mathematician, physicist, and philosopher from the Vienna Circle (d. 1959)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1897", "text": "<PERSON><PERSON>, Dutch composer and conductor (d. 1943)", "html": "1897 - <a href=\"https://wikipedia.org/wiki/Sim_Go<PERSON>kes\" title=\"Sim Gokkes\"><PERSON><PERSON></a>, Dutch composer and conductor (d. 1943)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Sim_Gokkes\" title=\"Sim Gokkes\"><PERSON><PERSON></a>, Dutch composer and conductor (d. 1943)", "links": [{"title": "<PERSON>m Go<PERSON>", "link": "https://wikipedia.org/wiki/Sim_Gokkes"}]}, {"year": "1897", "text": "<PERSON>, Mexican wrestling promoter, founded Consejo Mundial de Lucha Libre (d. 1987)", "html": "1897 - <a href=\"https://wikipedia.org/wiki/Salvador_Lu<PERSON>oth\" title=\"<PERSON> Lu<PERSON>\"><PERSON></a>, Mexican wrestling promoter, founded <a href=\"https://wikipedia.org/wiki/Consejo_Mundial_de_Lucha_Libre\" title=\"Consejo Mundial de Lucha Libre\">Consejo Mundial de Lucha Libre</a> (d. 1987)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Salvador_Lutteroth\" title=\"<PERSON> Lu<PERSON>\"><PERSON></a>, Mexican wrestling promoter, founded <a href=\"https://wikipedia.org/wiki/Consejo_Mundial_de_Lucha_Libre\" title=\"Consejo Mundial de Lucha Libre\">Consejo Mundial de Lucha Libre</a> (d. 1987)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Salvador_Lutteroth"}, {"title": "Consejo Mundial de Lucha <PERSON>", "link": "https://wikipedia.org/wiki/Consejo_Mundi<PERSON>_<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1899", "text": "<PERSON><PERSON><PERSON><PERSON>, Greek politician, Prime Minister of Greece (d. 1970)", "html": "1899 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Greek politician, <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Greece\" title=\"Prime Minister of Greece\">Prime Minister of Greece</a> (d. 1970)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Greek politician, <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Greece\" title=\"Prime Minister of Greece\">Prime Minister of Greece</a> (d. 1970)", "links": [{"title": "Panagi<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Panagiot<PERSON>_<PERSON>lis"}, {"title": "Prime Minister of Greece", "link": "https://wikipedia.org/wiki/Prime_Minister_of_Greece"}]}, {"year": "1901", "text": "<PERSON>, German businessman and politician, President of the German Bundesrat (d. 1958)", "html": "1901 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German businessman and politician, <a href=\"https://wikipedia.org/wiki/President_of_the_German_Bundesrat\" title=\"President of the German Bundesrat\">President of the German Bundesrat</a> (d. 1958)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German businessman and politician, <a href=\"https://wikipedia.org/wiki/President_of_the_German_Bundesrat\" title=\"President of the German Bundesrat\">President of the German Bundesrat</a> (d. 1958)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "President of the German Bundesrat", "link": "https://wikipedia.org/wiki/President_of_the_German_Bundesrat"}]}, {"year": "1902", "text": "<PERSON>, American blues singer-songwriter and guitarist (d. 1988)", "html": "1902 - <a href=\"https://wikipedia.org/wiki/Son_House\" title=\"Son House\"><PERSON> House</a>, American blues singer-songwriter and guitarist (d. 1988)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Son_House\" title=\"Son House\"><PERSON> House</a>, American blues singer-songwriter and guitarist (d. 1988)", "links": [{"title": "Son House", "link": "https://wikipedia.org/wiki/Son_House"}]}, {"year": "1904", "text": "<PERSON><PERSON>, Canadian journalist and author (d. 1987)", "html": "1904 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>%C3%AEt\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Canadian journalist and author (d. 1987)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>%C3%AEt\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Canadian journalist and author (d. 1987)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>%C3%AEt"}]}, {"year": "1904", "text": "<PERSON>, Sr., American candy maker, created M&M's and Mars bar (d. 1999)", "html": "1904 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Sr.\" class=\"mw-redirect\" title=\"<PERSON>, Sr.\"><PERSON>, Sr.</a>, American candy maker, created <a href=\"https://wikipedia.org/wiki/M%26M%27s\" title=\"M&amp;M's\">M&amp;M's</a> and <a href=\"https://wikipedia.org/wiki/Mars_(chocolate_bar)\" class=\"mw-redirect\" title=\"Mars (chocolate bar)\">Mars bar</a> (d. 1999)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Sr.\" class=\"mw-redirect\" title=\"<PERSON>, Sr.\"><PERSON>, Sr.</a>, American candy maker, created <a href=\"https://wikipedia.org/wiki/M%26M%27s\" title=\"M&amp;M's\">M&amp;M's</a> and <a href=\"https://wikipedia.org/wiki/Mars_(chocolate_bar)\" class=\"mw-redirect\" title=\"Mars (chocolate bar)\">Mars bar</a> (d. 1999)", "links": [{"title": "<PERSON>, Sr.", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Sr."}, {"title": "M&M's", "link": "https://wikipedia.org/wiki/M%26M%27s"}, {"title": "Mars (chocolate bar)", "link": "https://wikipedia.org/wiki/Mars_(chocolate_bar)"}]}, {"year": "1904", "text": "<PERSON><PERSON>, Greek violinist and composer (d. 1949)", "html": "1904 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Greek violinist and composer (d. 1949)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Greek violinist and composer (d. 1949)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Nik<PERSON>_Skalkottas"}]}, {"year": "1905", "text": "<PERSON>, American author and poet (d. 1978)", "html": "1905 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author and poet (d. 1978)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author and poet (d. 1978)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1906", "text": "<PERSON>, Brazilian musician and songwriter (d. 1974)", "html": "1906 - <a href=\"https://wikipedia.org/wiki/Andr%C3%A9_<PERSON><PERSON><PERSON>\" title=\"<PERSON>\"><PERSON></a>, Brazilian musician and songwriter (d. 1974)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Andr%C3%A9_<PERSON><PERSON>\" title=\"<PERSON>\"><PERSON></a>, Brazilian musician and songwriter (d. 1974)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Andr%C3%A9_Fi<PERSON>ho"}]}, {"year": "1906", "text": "<PERSON>, American philanthropist (d. 1978)", "html": "1906 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON> III\"><PERSON> III</a>, American philanthropist (d. 1978)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON> III\"><PERSON></a>, American philanthropist (d. 1978)", "links": [{"title": "<PERSON> III", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1906", "text": "<PERSON>, American businessman (d. 1967)", "html": "1906 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(designer)\" title=\"<PERSON> (designer)\"><PERSON></a>, American businessman (d. 1967)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(designer)\" title=\"<PERSON> (designer)\"><PERSON></a>, American businessman (d. 1967)", "links": [{"title": "<PERSON> (designer)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(designer)"}]}, {"year": "1907", "text": "<PERSON><PERSON><PERSON>, Hungarian sculptor (d. 1965)", "html": "1907 - <a href=\"https://wikipedia.org/wiki/Zolt%C3%A1n_Kem%C3%A9ny\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Hungarian sculptor (d. 1965)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Zolt%C3%A1n_Kem%C3%A9ny\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Hungarian sculptor (d. 1965)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Zolt%C3%A1n_Kem%C3%A9ny"}]}, {"year": "1909", "text": "<PERSON>, English footballer (d. 1977)", "html": "1909 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(footballer,_born_1909)\" title=\"<PERSON> (footballer, born 1909)\"><PERSON></a>, English footballer (d. 1977)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(footballer,_born_1909)\" title=\"<PERSON> (footballer, born 1909)\"><PERSON></a>, English footballer (d. 1977)", "links": [{"title": "<PERSON> (footballer, born 1909)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>(footballer,_born_1909)"}]}, {"year": "1910", "text": "<PERSON>, American businessman, co-founded E & J Gallo Winery (d. 1993)", "html": "1910 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American businessman, co-founded <a href=\"https://wikipedia.org/wiki/E_%26_<PERSON>_<PERSON>allo_Winery\" title=\"E &amp; <PERSON> Winery\">E &amp; J <PERSON> Winery</a> (d. 1993)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American businessman, co-founded <a href=\"https://wikipedia.org/wiki/E_%26_<PERSON>_<PERSON>allo_Winery\" title=\"E &amp; <PERSON> Winery\">E &amp; J <PERSON> Winery</a> (d. 1993)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "E & J Gallo Winery", "link": "https://wikipedia.org/wiki/E_%26_<PERSON>_<PERSON>_Winery"}]}, {"year": "1910", "text": "<PERSON>, Bangladeshi librarian and educator (d. 1978)", "html": "1910 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Bangladeshi librarian and educator (d. 1978)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Bangladeshi librarian and educator (d. 1978)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1911", "text": "<PERSON>, American scientist and inventor (d. 1992)", "html": "1911 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American scientist and inventor (d. 1992)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American scientist and inventor (d. 1992)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1912", "text": "<PERSON>, Canadian journalist, playwright, and politician (d. 1968)", "html": "1912 - <a href=\"https://wikipedia.org/wiki/Andr%C3%A9_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian journalist, playwright, and politician (d. 1968)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Andr%C3%A9_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian journalist, playwright, and politician (d. 1968)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Andr%C3%A9_<PERSON>"}]}, {"year": "1913", "text": "<PERSON>, English race car driver and pilot (d. 1991)", "html": "1913 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English race car driver and pilot (d. 1991)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English race car driver and pilot (d. 1991)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1913", "text": "<PERSON>, Mexican astronomer (d. 1988)", "html": "1913 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Mexican astronomer (d. 1988)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Mexican astronomer (d. 1988)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1914", "text": "<PERSON>, French cellist and composer (d. 1990)", "html": "1914 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French cellist and composer (d. 1990)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French cellist and composer (d. 1990)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1916", "text": "<PERSON><PERSON><PERSON><PERSON>, Indian shehnai player (d. 2006)", "html": "1916 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Indian <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\">she<PERSON><PERSON></a> player (d. 2006)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Indian <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\">she<PERSON><PERSON></a> player (d. 2006)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>"}, {"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>"}]}, {"year": "1916", "text": "<PERSON>, English race car driver (d. 1957)", "html": "1916 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English race car driver (d. 1957)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English race car driver (d. 1957)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1917", "text": "<PERSON>, Australian journalist, author, and playwright (d. 1994)", "html": "1917 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian journalist, author, and playwright (d. 1994)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian journalist, author, and playwright (d. 1994)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1918", "text": "<PERSON>, American captain and politician, 38th Governor of Wisconsin (d. 2014)", "html": "1918 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American captain and politician, 38th <a href=\"https://wikipedia.org/wiki/Governor_of_Wisconsin\" title=\"Governor of Wisconsin\">Governor of Wisconsin</a> (d. 2014)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American captain and politician, 38th <a href=\"https://wikipedia.org/wiki/Governor_of_Wisconsin\" title=\"Governor of Wisconsin\">Governor of Wisconsin</a> (d. 2014)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Governor of Wisconsin", "link": "https://wikipedia.org/wiki/Governor_of_Wisconsin"}]}, {"year": "1918", "text": "<PERSON>, American pianist and composer (d. 2016)", "html": "1918 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(jazz)\" title=\"<PERSON> (jazz)\"><PERSON></a>, American pianist and composer (d. 2016)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(jazz)\" title=\"<PERSON> (jazz)\"><PERSON></a>, American pianist and composer (d. 2016)", "links": [{"title": "<PERSON> (jazz)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(jazz)"}]}, {"year": "1919", "text": "<PERSON>, Australian bishop (d. 2013)", "html": "1919 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(bishop)\" title=\"<PERSON> (bishop)\"><PERSON></a>, Australian bishop (d. 2013)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>(bishop)\" title=\"<PERSON> (bishop)\"><PERSON></a>, Australian bishop (d. 2013)", "links": [{"title": "<PERSON> (bishop)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(bishop)"}]}, {"year": "1920", "text": "<PERSON><PERSON>, Greek singer-songwriter and bouzouki player (d. 1970)", "html": "1920 - <a href=\"https://wikipedia.org/wiki/Manolis_Chiotis\" title=\"Man<PERSON> Chiotis\"><PERSON><PERSON></a>, Greek singer-songwriter and <a href=\"https://wikipedia.org/wiki/<PERSON>uz<PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\">bouzouki</a> player (d. 1970)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Manolis_Chiotis\" title=\"<PERSON><PERSON> Chiotis\"><PERSON><PERSON></a>, Greek singer-songwriter and <a href=\"https://wikipedia.org/wiki/<PERSON>uz<PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\">bouzouki</a> player (d. 1970)", "links": [{"title": "Manolis <PERSON>ot<PERSON>", "link": "https://wikipedia.org/wiki/Manolis_Chiotis"}, {"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>uz<PERSON><PERSON>"}]}, {"year": "1920", "text": "<PERSON><PERSON>, French director, film critic, journalist, novelist and screenwriter (d. 2010)", "html": "1920 - <a href=\"https://wikipedia.org/wiki/%C3%89<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, French director, film critic, journalist, novelist and screenwriter (d. 2010)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/%C3%89<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, French director, film critic, journalist, novelist and screenwriter (d. 2010)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/%C3%89<PERSON>_<PERSON><PERSON><PERSON>"}]}, {"year": "1921", "text": "<PERSON>, Belgian violinist and pianist (d. 1986)", "html": "1921 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Belgian violinist and pianist (d. 1986)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Belgian violinist and pianist (d. 1986)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1921", "text": "<PERSON>, English pianist, composer, and conductor (d. 2014)", "html": "1921 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English pianist, composer, and conductor (d. 2014)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English pianist, composer, and conductor (d. 2014)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1922", "text": "<PERSON>, American director, producer, and screenwriter (d. 2004)", "html": "1922 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American director, producer, and screenwriter (d. 2004)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American director, producer, and screenwriter (d. 2004)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1923", "text": "<PERSON><PERSON><PERSON>, Canadian geographer, author, and academic (d. 2020)", "html": "1923 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Canadian geographer, author, and academic (d. 2020)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Canadian geographer, author, and academic (d. 2020)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1923", "text": "<PERSON><PERSON><PERSON><PERSON>, Hungarian politician (d. 2018)", "html": "1923 - <a href=\"https://wikipedia.org/wiki/Rezs%C5%91_Nyers\" title=\"<PERSON><PERSON><PERSON><PERSON>yers\"><PERSON><PERSON><PERSON><PERSON></a>, Hungarian politician (d. 2018)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Rezs%C5%91_Nyers\" title=\"<PERSON><PERSON><PERSON><PERSON>yers\"><PERSON><PERSON><PERSON><PERSON></a>, Hungarian politician (d. 2018)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Rezs%C5%91_Nyers"}]}, {"year": "1923", "text": "<PERSON><PERSON>, Syrian poet, publisher, and diplomat (d. 1998)", "html": "1923 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Syrian poet, publisher, and diplomat (d. 1998)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Syrian poet, publisher, and diplomat (d. 1998)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>i"}]}, {"year": "1923", "text": "<PERSON><PERSON><PERSON>, Indian religious leader, founded <PERSON><PERSON>ja Yoga (d. 2011)", "html": "1923 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON> Srivastava\"><PERSON><PERSON><PERSON></a>, Indian religious leader, founded <a href=\"https://wikipedia.org/wiki/Sahaja_Yoga\" title=\"Sahaja Yoga\"><PERSON><PERSON><PERSON> Yoga</a> (d. 2011)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON> Srivastava\"><PERSON><PERSON><PERSON></a>, Indian religious leader, founded <a href=\"https://wikipedia.org/wiki/Sahaja_Yoga\" title=\"Sahaja Yoga\">Sa<PERSON>ja Yoga</a> (d. 2011)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>va"}, {"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_Yoga"}]}, {"year": "1924", "text": "<PERSON>, American actor (d. 1998)", "html": "1924 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor (d. 1998)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor (d. 1998)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1924", "text": "<PERSON><PERSON>, Lithuanian-Israeli lawyer and politician (d. 2010)", "html": "1924 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Lithuanian-Israeli lawyer and politician (d. 2010)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Lithuanian-Israeli lawyer and politician (d. 2010)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1925", "text": "<PERSON>, American saxophonist (d. 2003)", "html": "1925 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American saxophonist (d. 2003)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American saxophonist (d. 2003)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1925", "text": "<PERSON>, English-French director and producer (d. 2022)", "html": "1925 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Peter Brook\"><PERSON></a>, English-French director and producer (d. 2022)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Peter Brook\"><PERSON></a>, English-French director and producer (d. 2022)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1925", "text": "<PERSON>, Swiss cyclist (d. 1964)", "html": "1925 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swiss cyclist (d. 1964)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swiss cyclist (d. 1964)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1926", "text": "<PERSON>, Belgian director and screenwriter (d. 2002)", "html": "1926 - <a href=\"https://wikipedia.org/wiki/Andr%C3%A9_Del<PERSON>ux\" title=\"<PERSON>\"><PERSON></a>, Belgian director and screenwriter (d. 2002)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Andr%C3%A9_Del<PERSON>ux\" title=\"<PERSON>\"><PERSON></a>, Belgian director and screenwriter (d. 2002)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Andr%C3%A9_<PERSON><PERSON>ux"}]}, {"year": "1927", "text": "<PERSON><PERSON>, American-German astronomer and critic (d. 2013)", "html": "1927 - <a href=\"https://wikipedia.org/wiki/Halton_Arp\" title=\"Halton Arp\"><PERSON><PERSON></a>, American-German astronomer and critic (d. 2013)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Halton_Arp\" title=\"Halton Arp\"><PERSON><PERSON></a>, American-German astronomer and critic (d. 2013)", "links": [{"title": "Halton Arp", "link": "https://wikipedia.org/wiki/Halton_Arp"}]}, {"year": "1927", "text": "<PERSON><PERSON><PERSON>, German soldier and politician, Vice-Chancellor of Germany (d. 2016)", "html": "1927 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, German soldier and politician, <a href=\"https://wikipedia.org/wiki/Vice-Chancellor_of_Germany\" title=\"Vice-Chancellor of Germany\">Vice-Chancellor of Germany</a> (d. 2016)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, German soldier and politician, <a href=\"https://wikipedia.org/wiki/Vice-Chancellor_of_Germany\" title=\"Vice-Chancellor of Germany\">Vice-Chancellor of Germany</a> (d. 2016)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>"}, {"title": "Vice-Chancellor of Germany", "link": "https://wikipedia.org/wiki/Vice-Chancellor_of_Germany"}]}, {"year": "1928", "text": "<PERSON><PERSON>, Nepalese politician, 24th Prime Minister of Nepal (d. 2015)", "html": "1928 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Nepalese politician, 24th <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Nepal\" title=\"Prime Minister of Nepal\">Prime Minister of Nepal</a> (d. 2015)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Nepalese politician, 24th <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Nepal\" title=\"Prime Minister of Nepal\">Prime Minister of Nepal</a> (d. 2015)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>"}, {"title": "Prime Minister of Nepal", "link": "https://wikipedia.org/wiki/Prime_Minister_of_Nepal"}]}, {"year": "1929", "text": "<PERSON>, American wrestler (d. 2005)", "html": "1929 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American wrestler (d. 2005)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American wrestler (d. 2005)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1930", "text": "<PERSON>, American actor (d. 1987)", "html": "1930 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor (d. 1987)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor (d. 1987)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1930", "text": "<PERSON>, American blues pianist, singer and composer (d. 1970)", "html": "1930 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American blues pianist, singer and composer (d. 1970)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>nn\"><PERSON></a>, American blues pianist, singer and composer (d. 1970)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>nn"}]}, {"year": "1931", "text": "<PERSON>, American-English engineer and academic (d. 2021)", "html": "1931 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American-English engineer and academic (d. 2021)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American-English engineer and academic (d. 2021)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "1931", "text": "<PERSON>, Scottish swimmer (d. 2013)", "html": "1931 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish swimmer (d. 2013)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish swimmer (d. 2013)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1931", "text": "<PERSON><PERSON><PERSON><PERSON>, Japanese sumo wrestler (d. 1998)", "html": "1931 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Japanese sumo wrestler (d. 1998)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Japanese sumo wrestler (d. 1998)", "links": [{"title": "Toyonobori", "link": "https://wikipedia.org/wiki/Toy<PERSON>bori"}]}, {"year": "1931", "text": "<PERSON>, American illustrator (d. 2010)", "html": "1931 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American illustrator (d. 2010)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>\" title=\"<PERSON>\"><PERSON></a>, American illustrator (d. 2010)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_Williamson"}]}, {"year": "1932", "text": "<PERSON>, American physicist and chemist, Nobel Prize laureate", "html": "1932 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American physicist and chemist, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Chemistry\" title=\"Nobel Prize in Chemistry\">Nobel Prize</a> laureate", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American physicist and chemist, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Chemistry\" title=\"Nobel Prize in Chemistry\">Nobel Prize</a> laureate", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Nobel Prize in Chemistry", "link": "https://wikipedia.org/wiki/Nobel_Prize_in_Chemistry"}]}, {"year": "1932", "text": "<PERSON>, American violinist and conductor (d. 2015)", "html": "1932 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American violinist and conductor (d. 2015)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American violinist and conductor (d. 2015)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1933", "text": "<PERSON>, English businessman", "html": "1933 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(English_businessman)\" title=\"<PERSON> (English businessman)\"><PERSON></a>, English businessman", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(English_businessman)\" title=\"<PERSON> (English businessman)\"><PERSON></a>, English businessman", "links": [{"title": "<PERSON> (English businessman)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(English_businessman)"}]}, {"year": "1933", "text": "<PERSON>, Welsh businessman and politician, Deputy Prime Minister of the United Kingdom", "html": "1933 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Welsh businessman and politician, <a href=\"https://wikipedia.org/wiki/Deputy_Prime_Minister_of_the_United_Kingdom\" title=\"Deputy Prime Minister of the United Kingdom\">Deputy Prime Minister of the United Kingdom</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Welsh businessman and politician, <a href=\"https://wikipedia.org/wiki/Deputy_Prime_Minister_of_the_United_Kingdom\" title=\"Deputy Prime Minister of the United Kingdom\">Deputy Prime Minister of the United Kingdom</a>", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}, {"title": "Deputy Prime Minister of the United Kingdom", "link": "https://wikipedia.org/wiki/Deputy_Prime_Minister_of_the_United_Kingdom"}]}, {"year": "1934", "text": "<PERSON>, Jr., American actor and director (d. 2012)", "html": "1934 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Jr.\" class=\"mw-redirect\" title=\"<PERSON>, Jr.\"><PERSON>, Jr.</a>, American actor and director (d. 2012)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Jr.\" class=\"mw-redirect\" title=\"<PERSON>, Jr.\"><PERSON>, Jr.</a>, American actor and director (d. 2012)", "links": [{"title": "<PERSON>, Jr.", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Jr."}]}, {"year": "1935", "text": "<PERSON>, English footballer and manager (d. 2004)", "html": "1935 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English footballer and manager (d. 2004)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English footballer and manager (d. 2004)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1936", "text": "<PERSON>, Canadian pilot and politician (d. 2024)", "html": "1936 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian pilot and politician (d. 2024)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian pilot and politician (d. 2024)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1936", "text": "<PERSON>, English pianist and composer", "html": "1936 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English pianist and composer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English pianist and composer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1937", "text": "<PERSON>, Welsh journalist and politician, Shadow Secretary of State for Wales (d. 2023)", "html": "1937 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Welsh journalist and politician, <a href=\"https://wikipedia.org/wiki/Shadow_Secretary_of_State_for_Wales\" title=\"Shadow Secretary of State for Wales\">Shadow Secretary of State for Wales</a> (d. 2023)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Welsh journalist and politician, <a href=\"https://wikipedia.org/wiki/Shadow_Secretary_of_State_for_Wales\" title=\"Shadow Secretary of State for Wales\">Shadow Secretary of State for Wales</a> (d. 2023)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Ann_<PERSON>"}, {"title": "Shadow Secretary of State for Wales", "link": "https://wikipedia.org/wiki/Shadow_Secretary_of_State_for_Wales"}]}, {"year": "1937", "text": "<PERSON>, American football player and coach", "html": "1937 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player and coach", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1937", "text": "<PERSON><PERSON><PERSON>, French diplomat and author (d. 2010)", "html": "1937 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>%C3%A9my\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, French diplomat and author (d. 2010)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>%C3%A9my\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, French diplomat and author (d. 2010)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>%C3%A9my"}]}, {"year": "1938", "text": "<PERSON>, English author and illustrator", "html": "1938 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(author/illustrator)\" class=\"mw-redirect\" title=\"<PERSON> (author/illustrator)\"><PERSON></a>, English author and illustrator", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(author/illustrator)\" class=\"mw-redirect\" title=\"<PERSON> (author/illustrator)\"><PERSON></a>, English author and illustrator", "links": [{"title": "<PERSON> (author/illustrator)", "link": "https://wikipedia.org/wiki/<PERSON>_(author/illustrator)"}]}, {"year": "1938", "text": "<PERSON><PERSON>, Australian cricketer", "html": "1938 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Australian cricketer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Australian cricketer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1939", "text": "<PERSON>, American actress", "html": "1939 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1940", "text": "<PERSON>, American singer-songwriter (d. 2010)", "html": "1940 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter (d. 2010)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter (d. 2010)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1940", "text": "<PERSON>, German bicyclist", "html": "1940 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German bicyclist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German bicyclist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1942", "text": "<PERSON><PERSON>, French actress (d. 1967)", "html": "1942 - <a href=\"https://wikipedia.org/wiki/Fran%C3%A7<PERSON>_Dorl%C3%A9ac\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, French actress (d. 1967)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Fran%C3%A7oise_Dorl%C3%A9ac\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, French actress (d. 1967)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Fran%C3%A7oise_Dorl%C3%A9ac"}]}, {"year": "1942", "text": "<PERSON><PERSON>, American singer-songwriter and pianist", "html": "1942 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American singer-songwriter and pianist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American singer-songwriter and pianist", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1942", "text": "<PERSON><PERSON><PERSON>, Greek basketball player and coach (d. 2018)", "html": "1942 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Greek basketball player and coach (d. 2018)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Greek basketball player and coach (d. 2018)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Kostas_Politis"}]}, {"year": "1942", "text": "<PERSON><PERSON>, Indian metallurgist, educator and administrator (d. 2010)", "html": "1942 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Indian metallurgist, educator and administrator (d. 2010)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Indian metallurgist, educator and administrator (d. 2010)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1943", "text": "<PERSON><PERSON><PERSON>, Hungarian sprinter and sportscaster (d. 2006)", "html": "1943 - <a href=\"https://wikipedia.org/wiki/Istv%C3%A1n_Gyu<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Hungarian sprinter and sportscaster (d. 2006)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Istv%C3%A1n_Gyu<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Hungarian sprinter and sportscaster (d. 2006)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Istv%C3%A1n_<PERSON><PERSON><PERSON>"}]}, {"year": "1943", "text": "<PERSON><PERSON><PERSON>, German conductor", "html": "1943 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON> Ha<PERSON>\"><PERSON><PERSON><PERSON></a>, German conductor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON> Ha<PERSON>\"><PERSON><PERSON><PERSON></a>, German conductor", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1943", "text": "<PERSON>, English singer-songwriter, guitarist, and painter (d. 1995)", "html": "1943 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English singer-songwriter, guitarist, and painter (d. 1995)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English singer-songwriter, guitarist, and painter (d. 1995)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1944", "text": "<PERSON><PERSON><PERSON>, French actress", "html": "1944 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, French actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, French actress", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>"}]}, {"year": "1944", "text": "<PERSON>, American-English journalist and author", "html": "1944 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American-English journalist and author", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American-English journalist and author", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1944", "text": "<PERSON><PERSON><PERSON>, Japanese guitarist", "html": "1944 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese guitarist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese guitarist", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON><PERSON>"}]}, {"year": "1944", "text": "<PERSON>, English general (d. 2024)", "html": "1944 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(British_Army_officer)\" title=\"<PERSON> (British Army officer)\"><PERSON></a>, English general (d. 2024)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(British_Army_officer)\" title=\"<PERSON> (British Army officer)\"><PERSON></a>, English general (d. 2024)", "links": [{"title": "<PERSON> (British Army officer)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(British_Army_officer)"}]}, {"year": "1944", "text": "<PERSON>, American guitarist, songwriter, and producer (d. 2023)", "html": "1944 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(musician)\" title=\"<PERSON> (musician)\"><PERSON></a>, American guitarist, songwriter, and producer (d. 2023)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(musician)\" title=\"<PERSON> (musician)\"><PERSON></a>, American guitarist, songwriter, and producer (d. 2023)", "links": [{"title": "<PERSON> (musician)", "link": "https://wikipedia.org/wiki/<PERSON>_(musician)"}]}, {"year": "1945", "text": "<PERSON>, Baron <PERSON>, English lawyer", "html": "1945 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Baron_<PERSON>\" title=\"<PERSON>, Baron <PERSON>\"><PERSON>, Baron <PERSON></a>, English lawyer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Baron_<PERSON>\" title=\"<PERSON>, Baron <PERSON>\"><PERSON>, Baron <PERSON></a>, English lawyer", "links": [{"title": "<PERSON>, Baron <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_<PERSON>_<PERSON>"}]}, {"year": "1945", "text": "<PERSON>, American sprinter and coach (d. 2022)", "html": "1945 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(athlete)\" class=\"mw-redirect\" title=\"<PERSON> (athlete)\"><PERSON></a>, American sprinter and coach (d. 2022)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(athlete)\" class=\"mw-redirect\" title=\"<PERSON> (athlete)\"><PERSON></a>, American sprinter and coach (d. 2022)", "links": [{"title": "<PERSON> (athlete)", "link": "https://wikipedia.org/wiki/<PERSON>(athlete)"}]}, {"year": "1945", "text": "<PERSON>, American singer-songwriter and keyboard player", "html": "1945 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and keyboard player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Stone\"><PERSON></a>, American singer-songwriter and keyboard player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1946", "text": "<PERSON>, Welsh-English actor", "html": "1946 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Welsh-English actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Welsh-English actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1946", "text": "<PERSON>, English singer-songwriter and guitarist", "html": "1946 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English singer-songwriter and guitarist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Ray_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English singer-songwriter and guitarist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Ray_Dorset"}]}, {"year": "1946", "text": "<PERSON>, Japanese cardinal", "html": "1946 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Japanese cardinal", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Japanese cardinal", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1947", "text": "<PERSON>, Scottish footballer", "html": "1947 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(footballer,_born_1947)\" title=\"<PERSON> (footballer, born 1947)\"><PERSON></a>, Scottish footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(footballer,_born_1947)\" title=\"<PERSON> (footballer, born 1947)\"><PERSON></a>, Scottish footballer", "links": [{"title": "<PERSON> (footballer, born 1947)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(footballer,_born_1947)"}]}, {"year": "1948", "text": "<PERSON>, American computer scientist and academic", "html": "1948 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American computer scientist and academic", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American computer scientist and academic", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1949", "text": "<PERSON>, Guyanese cricketer and coach", "html": "1949 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Guyanese cricketer and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Guyanese cricketer and coach", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1949", "text": "<PERSON>, Scottish-English politician", "html": "1949 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish-English politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Love\"><PERSON></a>, Scottish-English politician", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1949", "text": "<PERSON>, American singer-songwriter and guitarist (d. 2019)", "html": "1949 - <a href=\"https://wikipedia.org/wiki/<PERSON>_Money\" title=\"Eddie Money\"><PERSON></a>, American singer-songwriter and guitarist (d. 2019)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Eddie_Money\" title=\"Eddie Money\"><PERSON></a>, American singer-songwriter and guitarist (d. 2019)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1949", "text": "<PERSON><PERSON><PERSON><PERSON>, Slovenian sociologist, philosopher, and academic", "html": "1949 - <a href=\"https://wikipedia.org/wiki/Slavoj_%C5%BDi%C5%BEek\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Slovenian sociologist, philosopher, and academic", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Slavoj_%C5%BDi%C5%BEek\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Slovenian sociologist, philosopher, and academic", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Slavoj_%C5%BDi%C5%BEek"}]}, {"year": "1950", "text": "<PERSON>, English singer-songwriter and keyboard player", "html": "1950 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English singer-songwriter and keyboard player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English singer-songwriter and keyboard player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1950", "text": "<PERSON>, Russian politician and diplomat, Russian Minister of Foreign Affairs", "html": "1950 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian politician and diplomat, <a href=\"https://wikipedia.org/wiki/Minister_of_Foreign_Affairs_(Russia)\" title=\"Minister of Foreign Affairs (Russia)\">Russian Minister of Foreign Affairs</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian politician and diplomat, <a href=\"https://wikipedia.org/wiki/Minister_of_Foreign_Affairs_(Russia)\" title=\"Minister of Foreign Affairs (Russia)\">Russian Minister of Foreign Affairs</a>", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Minister of Foreign Affairs (Russia)", "link": "https://wikipedia.org/wiki/Minister_of_Foreign_Affairs_(Russia)"}]}, {"year": "1950", "text": "<PERSON>, American minister and politician, 19th Mayor of Palm Springs", "html": "1950 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American minister and politician, 19th <a href=\"https://wikipedia.org/wiki/Mayor_of_Palm_Springs\" class=\"mw-redirect\" title=\"Mayor of Palm Springs\">Mayor of Palm Springs</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American minister and politician, 19th <a href=\"https://wikipedia.org/wiki/Mayor_of_Palm_Springs\" class=\"mw-redirect\" title=\"Mayor of Palm Springs\">Mayor of Palm Springs</a>", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Mayor of Palm Springs", "link": "https://wikipedia.org/wiki/Mayor_of_Palm_Springs"}]}, {"year": "1951", "text": "<PERSON>, American bass player", "html": "1951 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American bass player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American bass player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1951", "text": "<PERSON>, American singer-songwriter", "html": "1951 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>.\" title=\"<PERSON>\"><PERSON>.</a>, American singer-songwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>.\" title=\"<PERSON>\"><PERSON>.</a>, American singer-songwriter", "links": [{"title": "<PERSON>.", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>."}]}, {"year": "1953", "text": "<PERSON>, English computer scientist and academic", "html": "1953 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English computer scientist and academic", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English computer scientist and academic", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1953", "text": "<PERSON>, American photographer, author, and educator (d. 2023)", "html": "1953 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American photographer, author, and educator (d. 2023)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American photographer, author, and educator (d. 2023)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1953", "text": "<PERSON>, English-American author and illustrator (d. 2002)", "html": "1953 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English-American author and illustrator (d. 2002)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English-American author and illustrator (d. 2002)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1954", "text": "<PERSON><PERSON><PERSON>, Thai politician, Prime Minister of Thailand", "html": "1954 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>-o-cha\" title=\"<PERSON><PERSON><PERSON>-o-cha\"><PERSON><PERSON><PERSON>-<PERSON>-cha</a>, Thai politician, <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Thailand\" title=\"Prime Minister of Thailand\">Prime Minister of Thailand</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>-o-cha\" title=\"<PERSON><PERSON><PERSON>-o-cha\"><PERSON><PERSON><PERSON>-cha</a>, Thai politician, <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Thailand\" title=\"Prime Minister of Thailand\">Prime Minister of Thailand</a>", "links": [{"title": "<PERSON><PERSON><PERSON>a", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>-o-cha"}, {"title": "Prime Minister of Thailand", "link": "https://wikipedia.org/wiki/Prime_Minister_of_Thailand"}]}, {"year": "1954", "text": "<PERSON>, American basketball player, coach, and executive", "html": "1954 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>.\" title=\"<PERSON>.\"><PERSON>.</a>, American basketball player, coach, and executive", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>.\" title=\"<PERSON>.\"><PERSON>.</a>, American basketball player, coach, and executive", "links": [{"title": "<PERSON>.", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>."}]}, {"year": "1954", "text": "<PERSON>, American basketball player", "html": "1954 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1955", "text": "<PERSON><PERSON>, Lebanese economist and politician", "html": "1955 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>\" title=\"<PERSON><PERSON>oud\"><PERSON><PERSON></a>, Lebanese economist and politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>\" title=\"<PERSON><PERSON>oud\"><PERSON><PERSON></a>, Lebanese economist and politician", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>"}]}, {"year": "1955", "text": "<PERSON>, American singer-songwriter and guitarist", "html": "1955 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(singer-songwriter)\" title=\"<PERSON> (singer-songwriter)\"><PERSON></a>, American singer-songwriter and guitarist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(singer-songwriter)\" title=\"<PERSON> (singer-songwriter)\"><PERSON></a>, American singer-songwriter and guitarist", "links": [{"title": "<PERSON> (singer-songwriter)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(singer-songwriter)"}]}, {"year": "1955", "text": "<PERSON><PERSON>, Brazilian politician and retired military officer, 38th President of Brazil", "html": "1955 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Brazilian politician and retired military officer, 38th <a href=\"https://wikipedia.org/wiki/President_of_Brazil\" title=\"President of Brazil\">President of Brazil</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Brazilian politician and retired military officer, 38th <a href=\"https://wikipedia.org/wiki/President_of_Brazil\" title=\"President of Brazil\">President of Brazil</a>", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}, {"title": "President of Brazil", "link": "https://wikipedia.org/wiki/President_of_Brazil"}]}, {"year": "1955", "text": "<PERSON><PERSON>, Greek politician", "html": "1955 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Greek politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Greek politician", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1955", "text": "<PERSON><PERSON><PERSON><PERSON>, German sprinter", "html": "1955 - <a href=\"https://wikipedia.org/wiki/B%C3%A4rbel_W%C3%B6ckel\" title=\"Bärb<PERSON> Wöckel\"><PERSON><PERSON><PERSON><PERSON></a>, German sprinter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/B%C3%A4rbel_W%C3%B6ckel\" title=\"Bärb<PERSON> Wöckel\"><PERSON><PERSON><PERSON><PERSON></a>, German sprinter", "links": [{"title": "Bärbel Wöckel", "link": "https://wikipedia.org/wiki/B%C3%A4rbel_W%C3%B6ckel"}]}, {"year": "1956", "text": "<PERSON>, American runner", "html": "1956 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American runner", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American runner", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1956", "text": "<PERSON>, German-English singer-songwriter and guitarist", "html": "1956 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German-English singer-songwriter and guitarist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German-English singer-songwriter and guitarist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1956", "text": "<PERSON>, English guitarist, keyboard player, composer, and producer (d. 2021)", "html": "1956 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English guitarist, keyboard player, composer, and producer (d. 2021)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English guitarist, keyboard player, composer, and producer (d. 2021)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1956", "text": "<PERSON>, Norwegian runner", "html": "1956 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Norwegian runner", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Norwegian runner", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1958", "text": "<PERSON><PERSON>, German sprinter", "html": "1958 - <a href=\"https://wikipedia.org/wiki/Marlies_G%C3%B6hr\" title=\"Mar<PERSON> Göhr\"><PERSON><PERSON></a>, German sprinter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Marlies_G%C3%B6hr\" title=\"Marlies Göhr\"><PERSON><PERSON></a>, German sprinter", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Marlies_G%C3%B6hr"}]}, {"year": "1958", "text": "<PERSON>, American comedian, director, and screenwriter", "html": "1958 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Brad <PERSON>\"><PERSON></a>, American comedian, director, and screenwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Brad Hall\"><PERSON></a>, American comedian, director, and screenwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Brad_Hall"}]}, {"year": "1958", "text": "<PERSON>, English actor, filmmaker, musician, and author", "html": "1958 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actor, filmmaker, musician, and author", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actor, filmmaker, musician, and author", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1959", "text": "<PERSON>, English singer-songwriter", "html": "1959 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>(singer)\" title=\"<PERSON> (singer)\"><PERSON></a>, English singer-songwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_(singer)\" title=\"<PERSON> (singer)\"><PERSON></a>, English singer-songwriter", "links": [{"title": "<PERSON> (singer)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_(singer)"}]}, {"year": "1959", "text": "<PERSON><PERSON>, Israeli diplomat", "html": "1959 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>ote<PERSON>\" title=\"<PERSON><PERSON> Rotem\"><PERSON><PERSON></a>, Israeli diplomat", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>otem\" title=\"<PERSON><PERSON> Rotem\"><PERSON><PERSON></a>, Israeli diplomat", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>otem"}]}, {"year": "1959", "text": "<PERSON><PERSON><PERSON>, Japanese keyboard player and composer", "html": "1959 - <a href=\"https://wikipedia.org/wiki/<PERSON>bu<PERSON>_<PERSON>ematsu\" title=\"<PERSON><PERSON><PERSON> Uematsu\"><PERSON><PERSON><PERSON></a>, Japanese keyboard player and composer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>bu<PERSON>_<PERSON>ematsu\" title=\"<PERSON>bu<PERSON> Uematsu\"><PERSON><PERSON><PERSON></a>, Japanese keyboard player and composer", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Nobuo_Uematsu"}]}, {"year": "1960", "text": "<PERSON>, Filipino general", "html": "1960 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Filipino general", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Filipino general", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1960", "text": "<PERSON><PERSON>, Syrian actor and voice actor", "html": "1960 - <a href=\"https://wikipedia.org/wiki/Marwan_<PERSON>\" title=\"<PERSON>wan <PERSON>\"><PERSON><PERSON></a>, Syrian actor and voice actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON>wan Farhat\"><PERSON><PERSON></a>, Syrian actor and voice actor", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>hat"}]}, {"year": "1960", "text": "<PERSON><PERSON>, Estonian architect", "html": "1960 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_Pu<PERSON>pp\" title=\"Raivo Puusepp\"><PERSON><PERSON></a>, Estonian architect", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_Puusepp\" title=\"Raivo Puusepp\"><PERSON><PERSON></a>, Estonian architect", "links": [{"title": "Raivo <PERSON>pp", "link": "https://wikipedia.org/wiki/Raivo_Puusepp"}]}, {"year": "1960", "text": "<PERSON><PERSON><PERSON>, Brazilian race car driver (d. 1994)", "html": "1960 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Brazilian race car driver (d. 1994)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Brazilian race car driver (d. 1994)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Ayr<PERSON>_Senna"}]}, {"year": "1960", "text": "<PERSON>, American drummer and producer", "html": "1960 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(musician)\" title=\"<PERSON> (musician)\"><PERSON></a>, American drummer and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(musician)\" title=\"<PERSON> (musician)\"><PERSON></a>, American drummer and producer", "links": [{"title": "<PERSON> (musician)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(musician)"}]}, {"year": "1961", "text": "<PERSON><PERSON>, American actress", "html": "1961 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American actress", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1961", "text": "<PERSON><PERSON>, German footballer and manager", "html": "1961 - <a href=\"https://wikipedia.org/wiki/Lothar_Matth%C3%A4us\" title=\"<PERSON><PERSON>hä<PERSON>\"><PERSON><PERSON></a>, German footballer and manager", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_Matth%C3%A4us\" title=\"<PERSON><PERSON>ä<PERSON>\"><PERSON><PERSON></a>, German footballer and manager", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Lothar_Matth%C3%A4us"}]}, {"year": "1961", "text": "<PERSON>, English footballer", "html": "1961 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%27Reilly\" title=\"<PERSON>\"><PERSON></a>, English footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%27Reilly\" title=\"<PERSON>\"><PERSON></a>, English footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>%27Reilly"}]}, {"year": "1961", "text": "<PERSON>, American hurdler", "html": "1961 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American hurdler", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American hurdler", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1962", "text": "<PERSON>, American actor", "html": "1962 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1962", "text": "<PERSON>, Canadian actress and screenwriter", "html": "1962 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian actress and screenwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian actress and screenwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1962", "text": "<PERSON>, American actress, producer, and talk show host", "html": "1962 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%27Donnell\" title=\"<PERSON>\"><PERSON></a>, American actress, producer, and talk show host", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%27Donnell\" title=\"<PERSON>\"><PERSON></a>, American actress, producer, and talk show host", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Rosie_O%27Donnell"}]}, {"year": "1962", "text": "<PERSON>, American author", "html": "1962 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>aid"}]}, {"year": "1963", "text": "<PERSON><PERSON>, American baseball player", "html": "1963 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> Dunston\"><PERSON><PERSON></a>, American baseball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> Dunston\"><PERSON><PERSON></a>, American baseball player", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1963", "text": "<PERSON>, Dutch footballer and manager", "html": "1963 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch footballer and manager", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch footballer and manager", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1963", "text": "<PERSON>, American guitarist, songwriter, and producer (d. 2003)", "html": "1963 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Shawn <PERSON>\"><PERSON></a>, American guitarist, songwriter, and producer (d. 2003)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"Shawn Lane\"><PERSON></a>, American guitarist, songwriter, and producer (d. 2003)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1963", "text": "<PERSON><PERSON><PERSON>, American bass player", "html": "1963 - <a href=\"https://wikipedia.org/wiki/Share_<PERSON><PERSON>rsen\" class=\"mw-redirect\" title=\"Share Pedersen\"><PERSON><PERSON> <PERSON><PERSON><PERSON></a>, American bass player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Share_<PERSON><PERSON><PERSON>\" class=\"mw-redirect\" title=\"Share P<PERSON>rsen\"><PERSON><PERSON> <PERSON><PERSON><PERSON></a>, American bass player", "links": [{"title": "Share <PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON><PERSON>"}]}, {"year": "1964", "text": "<PERSON><PERSON><PERSON>, Welsh rugby player", "html": "1964 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Welsh rugby player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Welsh rugby player", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1964", "text": "<PERSON><PERSON>, Danish cyclist", "html": "1964 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Danish cyclist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Danish cyclist", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1965", "text": "<PERSON>, French businessman and politician, French Minister of Social Affairs", "html": "1965 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French businessman and politician, <a href=\"https://wikipedia.org/wiki/Minister_of_Social_Affairs_(France)\" class=\"mw-redirect\" title=\"Minister of Social Affairs (France)\">French Minister of Social Affairs</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French businessman and politician, <a href=\"https://wikipedia.org/wiki/Minister_of_Social_Affairs_(France)\" class=\"mw-redirect\" title=\"Minister of Social Affairs (France)\">French Minister of Social Affairs</a>", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Minister of Social Affairs (France)", "link": "https://wikipedia.org/wiki/Minister_of_Social_Affairs_(France)"}]}, {"year": "1965", "text": "<PERSON>, American author, historian and political analyst", "html": "1965 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author, historian and political analyst", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author, historian and political analyst", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1965", "text": "<PERSON>, American actress", "html": "1965 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1966", "text": "<PERSON>, Mexican footballer, referee, lawyer, and economist", "html": "1966 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Mexican footballer, referee, lawyer, and economist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Mexican footballer, referee, lawyer, and economist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Benito_Archundia"}]}, {"year": "1966", "text": "<PERSON><PERSON>, German runner", "html": "1966 - <a href=\"https://wikipedia.org/wiki/Ha<PERSON>_<PERSON>hlbr%C3%BCgge\" title=\"<PERSON><PERSON>gg<PERSON>\"><PERSON><PERSON></a>, German runner", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>br%C3%BCgge\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, German runner", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Hauke_Fuhlbr%C3%BCgge"}]}, {"year": "1966", "text": "<PERSON>, American ice hockey player", "html": "1966 - <a href=\"https://wikipedia.org/wiki/Al_Iafrate\" title=\"Al Iafrate\"><PERSON></a>, American ice hockey player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Al_Iafrate\" title=\"Al Iafrate\"><PERSON></a>, American ice hockey player", "links": [{"title": "Al Iafrate", "link": "https://wikipedia.org/wiki/Al_Iafrate"}]}, {"year": "1966", "text": "<PERSON><PERSON>, Swedish author", "html": "1966 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Swedish author", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Swedish author", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1966", "text": "<PERSON>, English cricketer and coach", "html": "1966 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English cricketer and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English cricketer and coach", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1966", "text": "DJ Premier, American DJ and producer", "html": "1966 - <a href=\"https://wikipedia.org/wiki/DJ_Premier\" title=\"DJ Premier\">DJ Premier</a>, American DJ and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/DJ_Premier\" title=\"DJ Premier\">DJ Premier</a>, American DJ and producer", "links": [{"title": "DJ Premier", "link": "https://wikipedia.org/wiki/DJ_Premier"}]}, {"year": "1967", "text": "<PERSON>, Swedish singer-songwriter, musician, and producer", "html": "1967 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swedish singer-songwriter, musician, and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swedish singer-songwriter, musician, and producer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1967", "text": "<PERSON><PERSON>, Welsh lawyer and politician, First Minister of Wales", "html": "1967 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Welsh lawyer and politician, <a href=\"https://wikipedia.org/wiki/First_Minister_of_Wales\" title=\"First Minister of Wales\">First Minister of Wales</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Welsh lawyer and politician, <a href=\"https://wikipedia.org/wiki/First_Minister_of_Wales\" title=\"First Minister of Wales\">First Minister of Wales</a>", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}, {"title": "First Minister of Wales", "link": "https://wikipedia.org/wiki/First_Minister_of_Wales"}]}, {"year": "1967", "text": "<PERSON>, English musician and songwriter", "html": "1967 - <a href=\"https://wikipedia.org/wiki/<PERSON>_(musician)\" title=\"<PERSON> (musician)\"><PERSON></a>, English musician and songwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(musician)\" title=\"<PERSON> (musician)\"><PERSON></a>, English musician and songwriter", "links": [{"title": "<PERSON> (musician)", "link": "https://wikipedia.org/wiki/<PERSON>_(musician)"}]}, {"year": "1967", "text": "<PERSON><PERSON>, American costume and fashion designer", "html": "1967 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American costume and fashion designer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American costume and fashion designer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1968", "text": "<PERSON>, Australian businessman", "html": "1968 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian businessman", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian businessman", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>"}]}, {"year": "1968", "text": "<PERSON>, American singer and musician", "html": "1968 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, American singer and musician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, American singer and musician", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1968", "text": "<PERSON>, English actor, producer, and screenwriter", "html": "1968 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(actor)\" title=\"<PERSON> (actor)\"><PERSON></a>, English actor, producer, and screenwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>(actor)\" title=\"<PERSON> (actor)\"><PERSON></a>, English actor, producer, and screenwriter", "links": [{"title": "<PERSON> (actor)", "link": "https://wikipedia.org/wiki/<PERSON>(actor)"}]}, {"year": "1968", "text": "<PERSON>, Swedish ice hockey player", "html": "1968 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%B6v\" title=\"<PERSON>\"><PERSON></a>, Swedish ice hockey player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%B6v\" title=\"<PERSON>\"><PERSON></a>, Swedish ice hockey player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%B6v"}]}, {"year": "1968", "text": "<PERSON><PERSON><PERSON>, Turkish footballer and manager", "html": "1968 - <a href=\"https://wikipedia.org/wiki/Tolu<PERSON>_Kaf<PERSON>\" title=\"Tolunay <PERSON>\"><PERSON><PERSON><PERSON></a>, Turkish footballer and manager", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Tolu<PERSON>_Kaf<PERSON>\" title=\"Tolu<PERSON>\"><PERSON><PERSON><PERSON></a>, Turkish footballer and manager", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1968", "text": "<PERSON>, English footballer and coach", "html": "1968 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English footballer and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English footballer and coach", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1968", "text": "<PERSON>, American basketball player and sportscaster", "html": "1968 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(basketball)\" title=\"<PERSON> (basketball)\"><PERSON></a>, American basketball player and sportscaster", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(basketball)\" title=\"<PERSON> (basketball)\"><PERSON></a>, American basketball player and sportscaster", "links": [{"title": "<PERSON> (basketball)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(basketball)"}]}, {"year": "1969", "text": "<PERSON>, American journalist and author", "html": "1969 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American journalist and author", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American journalist and author", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1970", "text": "<PERSON><PERSON>, Japanese voice actress (d. 2000)", "html": "1970 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Japanese voice actress (d. 2000)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Japanese voice actress (d. 2000)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON><PERSON>"}]}, {"year": "1970", "text": "<PERSON><PERSON>, Turkish-American political activist", "html": "1970 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Turkish-American political activist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Turkish-American political activist", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1971", "text": "<PERSON><PERSON><PERSON>, Hungarian decathlete", "html": "1971 - <a href=\"https://wikipedia.org/wiki/Zsolt_K%C3%BCrt%C3%B6si\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Hungarian decathlete", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Zsolt_K%C3%BCrt%C3%B6si\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Hungarian decathlete", "links": [{"title": "Zsolt Kürtösi", "link": "https://wikipedia.org/wiki/Zsolt_K%C3%BCrt%C3%B6si"}]}, {"year": "1972", "text": "<PERSON>, American wrestler (d. 2005)", "html": "1972 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American wrestler (d. 2005)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American wrestler (d. 2005)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1972", "text": "<PERSON><PERSON><PERSON><PERSON><PERSON>, Hungarian hammer thrower", "html": "1972 - <a href=\"https://wikipedia.org/wiki/Bal%C3%<PERSON><PERSON><PERSON>_<PERSON>_(athlete)\" title=\"<PERSON><PERSON><PERSON><PERSON><PERSON> (athlete)\"><PERSON><PERSON><PERSON><PERSON><PERSON></a>, Hungarian hammer thrower", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Bal%C3%<PERSON><PERSON><PERSON>_<PERSON>_(athlete)\" title=\"<PERSON><PERSON><PERSON><PERSON><PERSON> (athlete)\"><PERSON><PERSON><PERSON><PERSON><PERSON></a>, Hungarian hammer thrower", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON><PERSON> (athlete)", "link": "https://wikipedia.org/wiki/Bal%C3%<PERSON><PERSON><PERSON>_<PERSON>_(athlete)"}]}, {"year": "1972", "text": "<PERSON>, Russian ice hockey player", "html": "1972 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian ice hockey player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian ice hockey player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1972", "text": "<PERSON><PERSON><PERSON>, Ethiopian runner", "html": "1972 - <a href=\"https://wikipedia.org/wiki/Derart<PERSON>_Tulu\" title=\"Derart<PERSON> Tulu\"><PERSON><PERSON><PERSON></a>, Ethiopian runner", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>art<PERSON>_<PERSON>lu\" title=\"Derart<PERSON> Tulu\"><PERSON><PERSON><PERSON></a>, Ethiopian runner", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Derart<PERSON>_<PERSON>lu"}]}, {"year": "1972", "text": "<PERSON>, English cricketer", "html": "1972 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English cricketer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English cricketer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1973", "text": "<PERSON><PERSON>, American television host", "html": "1973 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American television host", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American television host", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1973", "text": "<PERSON>, English footballer and manager", "html": "1973 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English footballer and manager", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English footballer and manager", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1973", "text": "Large Professor, American rapper and producer", "html": "1973 - <a href=\"https://wikipedia.org/wiki/Large_Professor\" title=\"Large Professor\">Large Professor</a>, American rapper and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Large_Professor\" title=\"Large Professor\">Large Professor</a>, American rapper and producer", "links": [{"title": "Large Professor", "link": "https://wikipedia.org/wiki/Large_Professor"}]}, {"year": "1974", "text": "<PERSON>, American actress", "html": "1974 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1974", "text": "<PERSON>, New Zealand comedian and actor", "html": "1974 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, New Zealand comedian and actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, New Zealand comedian and actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1974", "text": "<PERSON><PERSON>, Japanese sumo wrestler", "html": "1974 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> Takeharu\"><PERSON><PERSON></a>, Japanese sumo wrestler", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> Takeharu\"><PERSON><PERSON></a>, Japanese sumo wrestler", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>u"}]}, {"year": "1975", "text": "<PERSON><PERSON><PERSON>, American singer-songwriter", "html": "1975 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American singer-songwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American singer-songwriter", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1975", "text": "<PERSON><PERSON><PERSON>, South African rugby player", "html": "1975 - <a href=\"https://wikipedia.org/wiki/Corn%C3%A9_<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, South African rugby player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Corn%C3%A9_<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, South African rugby player", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Corn%C3%A9_<PERSON><PERSON>e"}]}, {"year": "1975", "text": "<PERSON><PERSON><PERSON><PERSON>, Argentinian-Italian basketball player", "html": "1975 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Argentinian-Italian basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Argentinian-Italian basketball player", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>ab<PERSON><PERSON>_<PERSON>"}]}, {"year": "1975", "text": "<PERSON><PERSON>, Ukrainian basketball player and coach", "html": "1975 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Ukrainian basketball player and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Ukrainian basketball player and coach", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1975", "text": "<PERSON>, Welsh snooker player", "html": "1975 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(snooker_player)\" title=\"<PERSON> (snooker player)\"><PERSON></a>, Welsh snooker player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(snooker_player)\" title=\"<PERSON> (snooker player)\"><PERSON></a>, Welsh snooker player", "links": [{"title": "<PERSON> (snooker player)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(snooker_player)"}]}, {"year": "1976", "text": "<PERSON><PERSON><PERSON>, American voice actress and singer", "html": "1976 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American voice actress and singer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American voice actress and singer", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1976", "text": "<PERSON><PERSON><PERSON>, Filipino singer-songwriter and guitarist", "html": "1976 - <a href=\"https://wikipedia.org/wiki/Bamboo_Ma%C3%B1alac\" title=\"Bamboo Mañalac\"><PERSON><PERSON><PERSON></a>, Filipino singer-songwriter and guitarist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Bamboo_Ma%C3%B1alac\" title=\"Bamboo Mañalac\"><PERSON><PERSON><PERSON> Maña<PERSON></a>, Filipino singer-songwriter and guitarist", "links": [{"title": "Bamboo Mañalac", "link": "https://wikipedia.org/wiki/Bamboo_Ma%C3%B1alac"}]}, {"year": "1976", "text": "<PERSON><PERSON>, German-Turkish footballer", "html": "1976 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>zlog\" title=\"<PERSON><PERSON> Sazlog\"><PERSON><PERSON></a>, German-Turkish footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>zlog\" title=\"<PERSON><PERSON> Sazlog\"><PERSON><PERSON></a>, German-Turkish footballer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>zlog"}]}, {"year": "1977", "text": "<PERSON>, Italian footballer", "html": "1977 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1977", "text": "<PERSON>, English tennis player", "html": "1977 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English tennis player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English tennis player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1978", "text": "<PERSON>, Kenyan runner", "html": "1978 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Kenyan runner", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Kenyan runner", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1978", "text": "<PERSON><PERSON><PERSON>, Australian journalist (d. 2007)", "html": "1978 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Australian journalist (d. 2007)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Australian journalist (d. 2007)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1978", "text": "<PERSON>, American dancer and television personality", "html": "1978 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American dancer and television personality", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American dancer and television personality", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1978", "text": "<PERSON><PERSON><PERSON>, Dominican baseball player", "html": "1978 - <a href=\"https://wikipedia.org/wiki/Cristian_<PERSON>%C3%A1n\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Dominican baseball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/C<PERSON><PERSON>_<PERSON>%C3%A1n\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Dominican baseball player", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Cristian_<PERSON>uzm%C3%A1n"}]}, {"year": "1978", "text": "<PERSON>, Filipino movie and TV actress", "html": "1978 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Filipino movie and TV actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Filipino movie and TV actress", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1978", "text": "<PERSON>, Iranian wrestler", "html": "1978 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(wrestler,_born_1978)\" title=\"<PERSON> (wrestler, born 1978)\"><PERSON></a>, Iranian wrestler", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(wrestler,_born_1978)\" title=\"<PERSON> (wrestler, born 1978)\"><PERSON></a>, Iranian wrestler", "links": [{"title": "<PERSON> (wrestler, born 1978)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(wrestler,_born_1978)"}]}, {"year": "1980", "text": "<PERSON><PERSON>, Swiss ice hockey player", "html": "1980 - <a href=\"https://wikipedia.org/wiki/Goran_Bezina\" title=\"Goran Bezina\"><PERSON><PERSON></a>, Swiss ice hockey player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Goran_Bezina\" title=\"Goran Bezina\"><PERSON><PERSON></a>, Swiss ice hockey player", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>a"}]}, {"year": "1980", "text": "<PERSON><PERSON>, Norwegian skier", "html": "1980 - <a href=\"https://wikipedia.org/wiki/Marit_Bj%C3%B8rgen\" title=\"<PERSON><PERSON>jø<PERSON>\"><PERSON><PERSON></a>, Norwegian skier", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Marit_Bj%C3%B8rgen\" title=\"<PERSON><PERSON>jø<PERSON>\"><PERSON><PERSON></a>, Norwegian skier", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Marit_Bj%C3%B8rgen"}]}, {"year": "1980", "text": "<PERSON>, South Korean singer and actress", "html": "1980 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, South Korean singer and actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, South Korean singer and actress", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1980", "text": "<PERSON><PERSON><PERSON>, Brazilian footballer", "html": "1980 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Brazilian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Brazilian footballer", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>"}]}, {"year": "1980", "text": "<PERSON><PERSON><PERSON>, Canadian singer-songwriter, guitarist, and producer", "html": "1980 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_Whibley\" title=\"<PERSON><PERSON><PERSON> Whibley\"><PERSON><PERSON><PERSON> Whibley</a>, Canadian singer-songwriter, guitarist, and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_Whibley\" title=\"<PERSON><PERSON><PERSON> Whibley\"><PERSON><PERSON><PERSON> Whibley</a>, Canadian singer-songwriter, guitarist, and producer", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_Whibley"}]}, {"year": "1981", "text": "<PERSON><PERSON><PERSON><PERSON>, French cyclist", "html": "1981 - <a href=\"https://wikipedia.org/wiki/S%C3%A<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, French cyclist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/S%C3%A<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, French cyclist", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/S%C3%<PERSON><PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1981", "text": "<PERSON><PERSON>, Brazilian footballer", "html": "1981 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_(footballer,_born_1981)\" title=\"<PERSON><PERSON> (footballer, born 1981)\"><PERSON><PERSON></a>, Brazilian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_(footballer,_born_1981)\" title=\"<PERSON><PERSON> (footballer, born 1981)\"><PERSON><PERSON></a>, Brazilian footballer", "links": [{"title": "<PERSON><PERSON> (footballer, born 1981)", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_(footballer,_born_1981)"}]}, {"year": "1982", "text": "<PERSON>, Italian tennis player", "html": "1982 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian tennis player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian tennis player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1982", "text": "<PERSON><PERSON><PERSON><PERSON><PERSON>, Ethiopian runner", "html": "1982 - <a href=\"https://wikipedia.org/wiki/Eje<PERSON><PERSON><PERSON>_Di<PERSON>ba\" title=\"Ejegayehu Di<PERSON>ba\"><PERSON><PERSON><PERSON><PERSON><PERSON></a>, Ethiopian runner", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Eje<PERSON><PERSON><PERSON>_Di<PERSON>\" title=\"Ejegayehu Dibaba\"><PERSON><PERSON><PERSON><PERSON><PERSON></a>, Ethiopian runner", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Ejegayehu_Dibaba"}]}, {"year": "1982", "text": "<PERSON>, American baseball player", "html": "1982 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(baseball)\" title=\"<PERSON> (baseball)\"><PERSON></a>, American baseball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(baseball)\" title=\"<PERSON> (baseball)\"><PERSON></a>, American baseball player", "links": [{"title": "<PERSON> (baseball)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(baseball)"}]}, {"year": "1982", "text": "<PERSON>, Northern Irish race car driver", "html": "1982 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Northern Irish race car driver", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Northern Irish race car driver", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1983", "text": "<PERSON>, Cameroonian footballer", "html": "1983 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Cameroonian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Cameroonian footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1983", "text": "<PERSON><PERSON>, Spanish basketball player", "html": "1983 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_Pascua\" title=\"<PERSON><PERSON> Pascua\"><PERSON><PERSON></a>, Spanish basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>s<PERSON>a\" title=\"<PERSON><PERSON>cu<PERSON>\"><PERSON><PERSON></a>, Spanish basketball player", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1984", "text": "<PERSON><PERSON><PERSON>, American basketball player", "html": "1984 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American basketball player", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1984", "text": "<PERSON>, Uruguayan footballer", "html": "1984 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>%C3%ADguez\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, Uruguayan footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>%C3%ADguez\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, Uruguayan footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>%C3%ADguez"}]}, {"year": "1985", "text": "<PERSON>, American ice hockey player", "html": "1985 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American ice hockey player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American ice hockey player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1985", "text": "<PERSON><PERSON><PERSON>, American actress", "html": "1985 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American actress", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>e<PERSON>_<PERSON>-<PERSON>"}]}, {"year": "1985", "text": "<PERSON>, American football player", "html": "1985 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1986", "text": "<PERSON><PERSON>, Greek swimmer", "html": "1986 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Greek swimmer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Greek swimmer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1986", "text": "<PERSON>, American actor", "html": "1986 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1986", "text": "<PERSON><PERSON><PERSON>, Greek pole vaulter", "html": "1986 - <a href=\"https://wikipedia.org/wiki/Nik<PERSON>ta_Kyriakopoulou\" title=\"<PERSON><PERSON><PERSON> Kyriakopoulou\"><PERSON><PERSON><PERSON></a>, Greek pole vaulter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Nikoleta_Kyriakopoulou\" title=\"Nik<PERSON><PERSON> Kyriakopoulou\"><PERSON><PERSON><PERSON></a>, Greek pole vaulter", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Nikoleta_Kyriako<PERSON>ulou"}]}, {"year": "1986", "text": "<PERSON><PERSON>, Spanish footballer", "html": "1986 - <a href=\"https://wikipedia.org/wiki/Michu\" title=\"<PERSON>chu\"><PERSON><PERSON></a>, Spanish footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>chu\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Spanish footballer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>chu"}]}, {"year": "1987", "text": "<PERSON>, Venezuelan baseball pitcher", "html": "1987 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(baseball)\" title=\"<PERSON> (baseball)\"><PERSON></a>, Venezuelan baseball pitcher", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(baseball)\" title=\"<PERSON> (baseball)\"><PERSON></a>, Venezuelan baseball pitcher", "links": [{"title": "<PERSON> (baseball)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(baseball)"}]}, {"year": "1988", "text": "<PERSON>, Nigerian basketball player", "html": "1988 - <a href=\"https://wikipedia.org/wiki/Solomon_Alabi\" title=\"Solomon Alabi\"><PERSON></a>, Nigerian basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Solomon_Al<PERSON>\" title=\"Solomon Alabi\"><PERSON></a>, Nigerian basketball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Solomon_Alabi"}]}, {"year": "1988", "text": "<PERSON><PERSON><PERSON>, Czech sprinter", "html": "1988 - <a href=\"https://wikipedia.org/wiki/Kate%C5%99ina_%C4%8Cechov%C3%A1\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Czech sprinter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Kate%C5%99ina_%C4%8Cechov%C3%A1\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Czech sprinter", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Kate%C5%99ina_%C4%8Cechov%C3%A1"}]}, {"year": "1988", "text": "<PERSON>, American ice hockey player", "html": "1988 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American ice hockey player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American ice hockey player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1988", "text": "<PERSON>, German sprinter", "html": "1988 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%BCger\" title=\"<PERSON>\"><PERSON></a>, German sprinter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%BCger\" title=\"<PERSON>\"><PERSON></a>, German sprinter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>r%C3%BCger"}]}, {"year": "1988", "text": "<PERSON>, Austrian footballer", "html": "1988 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Austrian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Austrian footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1989", "text": "<PERSON><PERSON>, Spanish footballer", "html": "1989 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> Alba\"><PERSON><PERSON></a>, Spanish footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> Alba\"><PERSON><PERSON></a>, Spanish footballer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1989", "text": "<PERSON><PERSON><PERSON>, Uruguayan footballer", "html": "1989 - <a href=\"https://wikipedia.org/wiki/Nicol%C3%A1s_Lodeiro\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Uruguayan footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Nicol%C3%A1s_Lodeiro\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Uruguayan footballer", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Nicol%C3%A1s_Lodeiro"}]}, {"year": "1989", "text": "<PERSON><PERSON>, Japanese actor", "html": "1989 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Japanese actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Japanese actor", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>h"}]}, {"year": "1990", "text": "<PERSON>, German singer-songwriter and dancer", "html": "1990 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German singer-songwriter and dancer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German singer-songwriter and dancer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1990", "text": "<PERSON><PERSON>, American runner and heptathlete", "html": "1990 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American runner and heptathlete", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American runner and heptathlete", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>n_<PERSON>"}]}, {"year": "1990", "text": "<PERSON>, American basketball player", "html": "1990 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1990", "text": "<PERSON>, Liberian-American soccer player", "html": "1990 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Liberian-American soccer player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Liberian-American soccer player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1991", "text": "<PERSON>, English footballer", "html": "1991 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1991", "text": "<PERSON>, French footballer", "html": "1991 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1992", "text": "<PERSON><PERSON><PERSON><PERSON><PERSON>, South African footballer", "html": "1992 - <a href=\"https://wikipedia.org/wiki/Le<PERSON><PERSON><PERSON><PERSON>_Masalesa\" title=\"Lehlogonolo Masalesa\"><PERSON><PERSON><PERSON><PERSON><PERSON></a>, South African footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Le<PERSON><PERSON><PERSON><PERSON>_Masalesa\" title=\"Lehlogonolo Masalesa\"><PERSON><PERSON><PERSON><PERSON><PERSON></a>, South African footballer", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Lehlogonolo_Masalesa"}]}, {"year": "1992", "text": "<PERSON><PERSON>, American basketball player", "html": "1992 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American basketball player", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1992", "text": "<PERSON><PERSON><PERSON><PERSON>, Czech tennis player", "html": "1992 - <a href=\"https://wikipedia.org/wiki/Karol%C3%ADna_Pl%C3%AD%C5%A1kov%C3%A1\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Czech tennis player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Karol%C3%ADna_Pl%C3%AD%C5%A1kov%C3%A1\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Czech tennis player", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Karol%C3%ADna_Pl%C3%AD%C5%A1kov%C3%A1"}]}, {"year": "1992", "text": "<PERSON><PERSON><PERSON><PERSON>, Czech tennis player", "html": "1992 - <a href=\"https://wikipedia.org/wiki/Krist%C3%BDna_Pl%C3%AD%C5%A1kov%C3%A1\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Czech tennis player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Krist%C3%BDna_Pl%C3%AD%C5%A1kov%C3%A1\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Czech tennis player", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Krist%C3%BDna_Pl%C3%AD%C5%A1kov%C3%A1"}]}, {"year": "1993", "text": "<PERSON>, Swiss ice hockey player", "html": "1993 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swiss ice hockey player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swiss ice hockey player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1993", "text": "<PERSON>, English footballer", "html": "1993 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1993", "text": "<PERSON>, Finnish footballer", "html": "1993 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Finnish footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Finnish footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1993", "text": "<PERSON>, Dominican baseball player", "html": "1993 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dominican baseball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dominican baseball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1994", "text": "<PERSON><PERSON><PERSON>, American actress", "html": "1994 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American actress", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_Brown"}]}, {"year": "1995", "text": "<PERSON><PERSON>, American actor", "html": "1995 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American actor", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>"}]}, {"year": "1995", "text": "<PERSON>, American football player", "html": "1995 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1995", "text": "<PERSON><PERSON>, Swiss ice hockey player", "html": "1995 - <a href=\"https://wikipedia.org/wiki/Mirco_M%C3%BCller\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Swiss ice hockey player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Mirco_M%C3%BCller\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Swiss ice hockey player", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Mirco_M%C3%BCller"}]}, {"year": "1996", "text": "<PERSON>, Norwegian footballer", "html": "1996 - <a href=\"https://wikipedia.org/wiki/<PERSON>_Mi<PERSON>sen\" title=\"Aurora Mi<PERSON>\"><PERSON></a>, Norwegian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Norwegian footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>sen"}]}, {"year": "1997", "text": "<PERSON>, English footballer", "html": "1997 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1997", "text": "<PERSON>, Argentine actress", "html": "1997 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, Argentine actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, Argentine actress", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1998", "text": "<PERSON>, American basketball player", "html": "1998 - <a href=\"https://wikipedia.org/wiki/Miles_Bridges\" title=\"Miles Bridges\"><PERSON></a>, American basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Miles_Bridges\" title=\"Miles Bridges\"><PERSON></a>, American basketball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_Bridges"}]}, {"year": "2000", "text": "<PERSON>, American actor", "html": "2000 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Jace Norman\"><PERSON></a>, American actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Jace Norman\"><PERSON></a>, American actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2000", "text": "<PERSON><PERSON>, South Korean singer and actor", "html": "2000 - <a href=\"https://wikipedia.org/wiki/Yoon_<PERSON>-ha\" title=\"Yoon San-ha\"><PERSON><PERSON></a>, South Korean singer and actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Yoon_San-ha\" title=\"Yoon San-ha\"><PERSON><PERSON></a>, South Korean singer and actor", "links": [{"title": "Yoon <PERSON>-ha", "link": "https://wikipedia.org/wiki/<PERSON>on_San-ha"}]}, {"year": "2003", "text": "<PERSON><PERSON><PERSON>, English racing driver", "html": "2003 - <a href=\"https://wikipedia.org/wiki/Abbi_Pulling\" title=\"Abbi Pulling\">A<PERSON><PERSON></a>, English racing driver", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Abbi_Pulling\" title=\"Abbi Pulling\"><PERSON><PERSON><PERSON></a>, English racing driver", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Abbi_<PERSON>"}]}, {"year": "2007", "text": "<PERSON>, English footballer", "html": "2007 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}], "Deaths": [{"year": "543 or 547", "text": "<PERSON> of Nursia, Italian saint (b. 480)", "html": "543 or 547 - <a href=\"https://wikipedia.org/wiki/543\" title=\"543\">543</a> or <a href=\"https://wikipedia.org/wiki/547\" title=\"547\">547</a> - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_Nurs<PERSON>\" title=\"<PERSON> of Nursia\"><PERSON> of Nursia</a>, Italian saint (b. 480)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/543\" title=\"543\">543</a> or <a href=\"https://wikipedia.org/wiki/547\" title=\"547\">547</a> - <a href=\"https://wikipedia.org/wiki/<PERSON>_of_Nursia\" title=\"<PERSON> of Nursia\"><PERSON> of Nursia</a>, Italian saint (b. 480)", "links": [{"title": "543", "link": "https://wikipedia.org/wiki/543"}, {"title": "547", "link": "https://wikipedia.org/wiki/547"}, {"title": "Benedict of Nursia", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_Nursia"}]}, {"year": "867", "text": "<PERSON><PERSON>, king of Northumbria", "html": "867 - <a href=\"https://wikipedia.org/wiki/%C3%86lla_of_Northumbria\" title=\"<PERSON><PERSON> of Northumbria\"><PERSON><PERSON></a>, king of <a href=\"https://wikipedia.org/wiki/Kingdom_of_Northumbria\" class=\"mw-redirect\" title=\"Kingdom of Northumbria\">Northumbria</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/%C3%86lla_of_Northumbria\" title=\"<PERSON><PERSON> of Northumbria\"><PERSON><PERSON></a>, king of <a href=\"https://wikipedia.org/wiki/Kingdom_of_Northumbria\" class=\"mw-redirect\" title=\"Kingdom of Northumbria\">Northumbria</a>", "links": [{"title": "<PERSON><PERSON> of Northumbria", "link": "https://wikipedia.org/wiki/%C3%86lla_of_Northumbria"}, {"title": "Kingdom of Northumbria", "link": "https://wikipedia.org/wiki/Kingdom_of_Northumbria"}]}, {"year": "867", "text": "<PERSON><PERSON><PERSON><PERSON>, king of Northumbria", "html": "867 - <a href=\"https://wikipedia.org/wiki/O<PERSON><PERSON><PERSON>_of_Northumbria\" title=\"<PERSON><PERSON><PERSON><PERSON> of Northumbria\"><PERSON><PERSON><PERSON><PERSON></a>, king of Northumbria", "no_year_html": "<a href=\"https://wikipedia.org/wiki/O<PERSON><PERSON><PERSON>_of_Northumbria\" title=\"<PERSON><PERSON><PERSON><PERSON> of Northumbria\"><PERSON><PERSON><PERSON><PERSON></a>, king of Northumbria", "links": [{"title": "<PERSON><PERSON><PERSON>ht of Northumbria", "link": "https://wikipedia.org/wiki/Osberht_of_Northumbria"}]}, {"year": "1034", "text": "<PERSON><PERSON>, Count <PERSON><PERSON> of Lotharingia (b. 955)", "html": "1034 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>,_Count_<PERSON><PERSON>_of_Lotharingia\" title=\"<PERSON><PERSON>, Count Pa<PERSON> of Lotharingia\"><PERSON><PERSON>, Count <PERSON><PERSON> of Lotharingia</a> (b. 955)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>,_Count_<PERSON><PERSON>_of_Lotharingia\" title=\"<PERSON><PERSON>, Count <PERSON><PERSON> of Lotharingia\"><PERSON><PERSON>, Count <PERSON><PERSON> of Lotharingia</a> (b. 955)", "links": [{"title": "<PERSON><PERSON>, Count Pa<PERSON> of Lotharingia", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>,_Count_<PERSON><PERSON>_of_Lotharingia"}]}, {"year": "1063", "text": "<PERSON><PERSON><PERSON> of Lotharingia (b. 995)", "html": "1063 - <a href=\"https://wikipedia.org/wiki/Richeza_of_Lotharingia\" title=\"<PERSON><PERSON><PERSON> of Lotharingia\"><PERSON>eza of Lotharingia</a> (b. 995)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Richeza_of_Lotharingia\" title=\"<PERSON>ez<PERSON> of Lotharingia\">Richeza of Lotharingia</a> (b. 995)", "links": [{"title": "Richeza of Lotharingia", "link": "https://wikipedia.org/wiki/Richeza_of_Lotharingia"}]}, {"year": "1076", "text": "<PERSON>, Duke of Burgundy (b. 1011)", "html": "1076 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Duke_of_Burgundy\" title=\"<PERSON>, Duke of Burgundy\"><PERSON>, Duke of Burgundy</a> (b. 1011)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Duke_of_Burgundy\" title=\"<PERSON>, Duke of Burgundy\"><PERSON>, Duke of Burgundy</a> (b. 1011)", "links": [{"title": "<PERSON>, Duke of Burgundy", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Duke_of_Burgundy"}]}, {"year": "1201", "text": "<PERSON><PERSON><PERSON><PERSON>, Danish archbishop (b. c. 1128)", "html": "1201 - <a href=\"https://wikipedia.org/wiki/Absalon\" title=\"Absalon\"><PERSON><PERSON><PERSON><PERSON></a>, Danish archbishop (b. c. 1128)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Absalon\" title=\"Absalon\"><PERSON><PERSON><PERSON><PERSON></a>, Danish archbishop (b. c. 1128)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Absalon"}]}, {"year": "1306", "text": "<PERSON>, Duke of Burgundy (b. 1248)", "html": "1306 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Duke_of_Burgundy\" title=\"<PERSON>, Duke of Burgundy\"><PERSON>, Duke of Burgundy</a> (b. 1248)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Duke_of_Burgundy\" title=\"<PERSON>, Duke of Burgundy\"><PERSON>, Duke of Burgundy</a> (b. 1248)", "links": [{"title": "<PERSON>, Duke of Burgundy", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Duke_of_Burgundy"}]}, {"year": "1372", "text": "<PERSON>, Margrave of Baden", "html": "1372 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Mar<PERSON>_of_Baden\" title=\"<PERSON>, Margrave of Baden\"><PERSON>, Margrave of Baden</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_<PERSON><PERSON>_of_Baden\" title=\"<PERSON>, Margrave of Baden\"><PERSON>, Margrave of Baden</a>", "links": [{"title": "<PERSON>, Margrave of Baden", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_<PERSON><PERSON>_of_Baden"}]}, {"year": "1487", "text": "<PERSON> of Flüe, Swiss monk and saint (b. 1417)", "html": "1487 - <a href=\"https://wikipedia.org/wiki/<PERSON>_of_Fl%C3%BCe\" title=\"<PERSON> of Flüe\"><PERSON> of Flüe</a>, Swiss monk and saint (b. 1417)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_of_Fl%C3%BCe\" title=\"<PERSON> of Flüe\"><PERSON> of <PERSON>lüe</a>, Swiss monk and saint (b. 1417)", "links": [{"title": "<PERSON> of Flüe", "link": "https://wikipedia.org/wiki/<PERSON>_of_Fl%C3%BCe"}]}, {"year": "1540", "text": "<PERSON>, 15th Earl of Oxford, English peer and courtier (b. c. 1482)", "html": "1540 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_15th_Earl_of_Oxford\" title=\"<PERSON>, 15th Earl of Oxford\"><PERSON>, 15th Earl of Oxford</a>, English peer and courtier (b. c. 1482)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_15th_Earl_of_Oxford\" title=\"<PERSON>, 15th Earl of Oxford\"><PERSON>, 15th Earl of Oxford</a>, English peer and courtier (b. c. 1482)", "links": [{"title": "<PERSON>, 15th Earl of Oxford", "link": "https://wikipedia.org/wiki/<PERSON>,_15th_Earl_of_Oxford"}]}, {"year": "1556", "text": "<PERSON>, English archbishop (b. 1489)", "html": "1556 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English archbishop (b. 1489)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English archbishop (b. 1489)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1571", "text": "<PERSON><PERSON>, French cardinal and Protestant (b. 1517)", "html": "1571 - <a href=\"https://wikipedia.org/wiki/Odet_de_Coligny\" title=\"O<PERSON> de Coligny\"><PERSON><PERSON></a>, French cardinal and Protestant (b. 1517)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Odet_de_Coligny\" title=\"Odet de Coligny\"><PERSON><PERSON></a>, French cardinal and Protestant (b. 1517)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Odet_de_Coligny"}]}, {"year": "1617", "text": "<PERSON><PERSON><PERSON><PERSON>, Algonquian Indigenous woman (b. c. 1595)", "html": "1617 - <a href=\"https://wikipedia.org/wiki/Pocahontas\" title=\"Pocahon<PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Algonquian Indigenous woman (b. c. 1595)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Pocahontas\" title=\"Pocahon<PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Algonquian Indigenous woman (b. c. 1595)", "links": [{"title": "Pocahontas", "link": "https://wikipedia.org/wiki/Pocahontas"}]}, {"year": "1653", "text": "<PERSON><PERSON><PERSON><PERSON>, Albanian politician, Grand Vizier of the Ottoman Empire", "html": "1653 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Albanian politician, <a href=\"https://wikipedia.org/wiki/List_of_Ottoman_Grand_Viziers\" class=\"mw-redirect\" title=\"List of Ottoman Grand Viziers\">Grand Vizier of the Ottoman Empire</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Albanian politician, <a href=\"https://wikipedia.org/wiki/List_of_Ottoman_Grand_Viziers\" class=\"mw-redirect\" title=\"List of Ottoman Grand Viziers\">Grand Vizier of the Ottoman Empire</a>", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>_<PERSON>"}, {"title": "List of Ottoman Grand Viziers", "link": "https://wikipedia.org/wiki/List_of_Ottoman_Grand_Viziers"}]}, {"year": "1656", "text": "<PERSON>, Irish archbishop (b. 1581)", "html": "1656 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Irish archbishop (b. 1581)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Irish archbishop (b. 1581)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1676", "text": "<PERSON>, French historian and author (b. 1623)", "html": "1676 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French historian and author (b. 1623)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French historian and author (b. 1623)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1729", "text": "<PERSON>, Scottish-French economist and politician, Controller-General of Finances (b. 1671)", "html": "1729 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(economist)\" title=\"<PERSON> (economist)\"><PERSON></a>, Scottish-French economist and politician, <a href=\"https://wikipedia.org/wiki/Controller-General_of_Finances\" title=\"Controller-General of Finances\">Controller-General of Finances</a> (b. 1671)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(economist)\" title=\"<PERSON> (economist)\"><PERSON></a>, Scottish-French economist and politician, <a href=\"https://wikipedia.org/wiki/Controller-General_of_Finances\" title=\"Controller-General of Finances\">Controller-General of Finances</a> (b. 1671)", "links": [{"title": "<PERSON> (economist)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(economist)"}, {"title": "Controller-General of Finances", "link": "https://wikipedia.org/wiki/Controller-General_of_Finances"}]}, {"year": "1729", "text": "<PERSON><PERSON><PERSON><PERSON>, politically influential Polish magnate (b. 1669)", "html": "1729 - <a href=\"https://wikipedia.org/wiki/El%C5%BCbieta_Sieniawska\" title=\"Elżbieta Sieniawska\"><PERSON><PERSON><PERSON><PERSON> Sieniaws<PERSON></a>, politically influential Polish magnate (b. 1669)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/El%C5%BCbieta_Sieniawska\" title=\"Elżbieta Sieniawska\"><PERSON><PERSON><PERSON><PERSON> Sieniawska</a>, politically influential Polish magnate (b. 1669)", "links": [{"title": "Elżbieta <PERSON>wska", "link": "https://wikipedia.org/wiki/El%C5%BCbieta_Sieniawska"}]}, {"year": "1734", "text": "<PERSON>, Scottish historian and author (b. 1679)", "html": "1734 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish historian and author (b. 1679)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish historian and author (b. 1679)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1751", "text": "<PERSON>, German publisher (b. 1706)", "html": "1751 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German publisher (b. 1706)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German publisher (b. 1706)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1752", "text": "<PERSON><PERSON>, Maltese painter (b. 1698)", "html": "1752 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Maltese painter (b. 1698)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Maltese painter (b. 1698)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1762", "text": "<PERSON>, French priest, astronomer, and academic (b. 1713)", "html": "1762 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, French priest, astronomer, and academic (b. 1713)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, French priest, astronomer, and academic (b. 1713)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1772", "text": "<PERSON><PERSON><PERSON>, French geographer and cartographer (b. 1703)", "html": "1772 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, French geographer and cartographer (b. 1703)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, French geographer and cartographer (b. 1703)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>"}]}, {"year": "1795", "text": "<PERSON>, Italian miner and geologist (b. 1714)", "html": "1795 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(geologist)\" class=\"mw-redirect\" title=\"<PERSON> (geologist)\"><PERSON></a>, Italian miner and geologist (b. 1714)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(geologist)\" class=\"mw-redirect\" title=\"<PERSON> (geologist)\"><PERSON></a>, Italian miner and geologist (b. 1714)", "links": [{"title": "<PERSON> (geologist)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(geologist)"}]}, {"year": "1801", "text": "<PERSON>, Italian composer and educator (b. 1741)", "html": "1801 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian composer and educator (b. 1741)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian composer and educator (b. 1741)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1804", "text": "<PERSON>, Duke of Enghien (b. 1772)", "html": "1804 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Duke_of_Enghien\" title=\"<PERSON>, Duke of Enghien\"><PERSON>, Duke of Enghien</a> (b. 1772)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Duke_of_Enghien\" title=\"<PERSON>, Duke of Enghien\"><PERSON>, Duke of Enghien</a> (b. 1772)", "links": [{"title": "<PERSON>, Duke of Enghien", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Duke_<PERSON>_En<PERSON>"}]}, {"year": "1843", "text": "<PERSON>, English poet, historian, and translator (b. 1774)", "html": "1843 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English poet, historian, and translator (b. 1774)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English poet, historian, and translator (b. 1774)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1843", "text": "<PERSON>, Mexican general and politician, 1st President of Mexico (b. 1786)", "html": "1843 - <a href=\"https://wikipedia.org/wiki/Guadalupe_Victoria\" title=\"Guadalupe Victoria\"><PERSON> Victoria</a>, Mexican general and politician, 1st <a href=\"https://wikipedia.org/wiki/President_of_Mexico\" title=\"President of Mexico\">President of Mexico</a> (b. 1786)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Guadalupe_Victoria\" title=\"Guadalupe Victoria\"><PERSON> Victoria</a>, Mexican general and politician, 1st <a href=\"https://wikipedia.org/wiki/President_of_Mexico\" title=\"President of Mexico\">President of Mexico</a> (b. 1786)", "links": [{"title": "Guadalupe Victoria", "link": "https://wikipedia.org/wiki/Guadalupe_Victoria"}, {"title": "President of Mexico", "link": "https://wikipedia.org/wiki/President_of_Mexico"}]}, {"year": "1854", "text": "<PERSON>, Mexican soldier. President (1847-1848) (b. 1795)", "html": "1854 - <a href=\"https://wikipedia.org/wiki/Pedro_Mar%C3%ADa_de_Anaya\" title=\"<PERSON>\"><PERSON></a>, Mexican soldier. President (1847-1848) (b. 1795)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Pedro_Mar%C3%ADa_de_Anaya\" title=\"<PERSON>\"><PERSON></a>, Mexican soldier. President (1847-1848) (b. 1795)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Pedro_Mar%C3%AD<PERSON>_de_<PERSON>ya"}]}, {"year": "1863", "text": "<PERSON>, American general (b. 1797)", "html": "1863 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American general (b. 1797)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American general (b. 1797)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1869", "text": "<PERSON>, son of <PERSON>, was a Mexican soldier and diplomat who served as a regent in the Second Mexican Empire (1863-1864) (b. 1803)", "html": "1869 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, son of <a href=\"https://wikipedia.org/wiki/Jos%C3%A9_Mar%C3%<PERSON><PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, was a Mexican soldier and diplomat who served as a <a href=\"https://wikipedia.org/wiki/Regent\" title=\"Regent\">regent</a> in the <a href=\"https://wikipedia.org/wiki/Second_Mexican_Empire\" title=\"Second Mexican Empire\">Second Mexican Empire</a> (1863-1864) (b. 1803)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, son of <a href=\"https://wikipedia.org/wiki/Jos%C3%A9_Mar%C3%<PERSON><PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, was a Mexican soldier and diplomat who served as a <a href=\"https://wikipedia.org/wiki/Regent\" title=\"Regent\">regent</a> in the <a href=\"https://wikipedia.org/wiki/Second_Mexican_Empire\" title=\"Second Mexican Empire\">Second Mexican Empire</a> (1863-1864) (b. 1803)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Jos%C3%A9_Mar%C3%ADa_Morelos"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Regent"}, {"title": "Second Mexican Empire", "link": "https://wikipedia.org/wiki/Second_Mexican_Empire"}]}, {"year": "1884", "text": "<PERSON>, American scholar and academic (b. 1819)", "html": "1884 - <a href=\"https://wikipedia.org/wiki/<PERSON>_Abbot\" title=\"Ezra Abbot\"><PERSON> Abbot</a>, American scholar and academic (b. 1819)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_Abbot\" title=\"Ezra Abbot\"><PERSON></a>, American scholar and academic (b. 1819)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1891", "text": "<PERSON>, American general (b. 1807)", "html": "1891 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American general (b. 1807)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American general (b. 1807)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1892", "text": "<PERSON><PERSON><PERSON>, Italian astronomer (b. 1819)[citation needed]", "html": "1892 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Italian astronomer (b. 1819)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Italian astronomer (b. 1819)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1915", "text": "<PERSON>, American golfer, tennis player, and engineer (b. 1856)", "html": "1915 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American golfer, tennis player, and engineer (b. 1856)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American golfer, tennis player, and engineer (b. 1856)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1920", "text": "<PERSON><PERSON>, British suffragette and aid worker (b. 1867)", "html": "1920 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, British suffragette and aid worker (b. 1867)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, British suffragette and aid worker (b. 1867)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1927", "text": "<PERSON>, Greek actor (b. 1864)", "html": "1927 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Greek actor (b. 1864)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Greek actor (b. 1864)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1933", "text": "<PERSON>, Italian mathematician (b. 1842)", "html": "1933 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%27Ovidio\" title=\"<PERSON>Ovid<PERSON>\"><PERSON></a>, Italian mathematician (b. 1842)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%27Ovidio\" title=\"<PERSON>Ovid<PERSON>\"><PERSON></a>, Italian mathematician (b. 1842)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Enrico_D%27Ovidio"}]}, {"year": "1934", "text": "<PERSON>, Austrian composer and conductor (b. 1878)", "html": "1934 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Austrian composer and conductor (b. 1878)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Austrian composer and conductor (b. 1878)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1934", "text": "<PERSON><PERSON>, American actress (b. 1896)", "html": "1934 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American actress (b. 1896)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American actress (b. 1896)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1936", "text": "<PERSON>, Russian composer and conductor (b. 1865)", "html": "1936 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian composer and conductor (b. 1865)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian composer and conductor (b. 1865)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1939", "text": "<PERSON><PERSON>, Estonian composer and conductor (b. 1900)", "html": "1939 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_Aav\" title=\"<PERSON><PERSON> Aav\"><PERSON><PERSON></a>, Estonian composer and conductor (b. 1900)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Estonian composer and conductor (b. 1900)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Evald_Aav"}]}, {"year": "1939", "text": "<PERSON>, Turkish general and politician (b. 1877)", "html": "1939 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>rdem\"><PERSON></a>, Turkish general and politician (b. 1877)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON> Ayerdem\"><PERSON></a>, Turkish general and politician (b. 1877)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>m"}]}, {"year": "1943", "text": "<PERSON><PERSON><PERSON>, American soldier and pilot (b. 1919)", "html": "1943 - <a href=\"https://wikipedia.org/wiki/Cornelia_Fort\" title=\"Cornelia Fort\">Cornelia Fort</a>, American soldier and pilot (b. 1919)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Cornelia_Fort\" title=\"Cornelia Fort\">Cornelia Fort</a>, American soldier and pilot (b. 1919)", "links": [{"title": "Cornelia Fort", "link": "https://wikipedia.org/wiki/Cornelia_Fort"}]}, {"year": "1945", "text": "<PERSON>, German SS officer (b. 1894)", "html": "1945 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German <a href=\"https://wikipedia.org/wiki/SS\" class=\"mw-redirect\" title=\"SS\">SS</a> officer (b. 1894)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German <a href=\"https://wikipedia.org/wiki/SS\" class=\"mw-redirect\" title=\"SS\">SS</a> officer (b. 1894)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "SS", "link": "https://wikipedia.org/wiki/SS"}]}, {"year": "1946", "text": "<PERSON>, Irish Judge, photographer and author (b. 1871)", "html": "1946 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Irish Judge, photographer and author (b. 1871)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Irish Judge, photographer and author (b. 1871)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1951", "text": "<PERSON>, Dutch conductor and composer (b. 1871)", "html": "1951 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch conductor and composer (b. 1871)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch conductor and composer (b. 1871)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1953", "text": "<PERSON>, American basketball player (b. 1922)", "html": "1953 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player (b. 1922)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player (b. 1922)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1956", "text": "<PERSON><PERSON>, Turkish politician (b. 1890)", "html": "1956 - <a href=\"https://wikipedia.org/wiki/Hat%C4%B1_%C3%87%C4%B1rpan\" title=\"<PERSON><PERSON> Çırpan\"><PERSON><PERSON></a>, Turkish politician (b. 1890)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Hat%C4%B1_%C3%87%C4%B1rpan\" title=\"<PERSON><PERSON> Çırpan\"><PERSON><PERSON></a>, Turkish politician (b. 1890)", "links": [{"title": "Hatı Çırpan", "link": "https://wikipedia.org/wiki/Hat%C4%B1_%C3%87%C4%B1rpan"}]}, {"year": "1958", "text": "<PERSON>, American soldier and author (b. 1923)", "html": "1958 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American soldier and author (b. 1923)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American soldier and author (b. 1923)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1970", "text": "<PERSON><PERSON>, Greek singer-songwriter and bouzouki player (b. 1920)", "html": "1970 - <a href=\"https://wikipedia.org/wiki/Manolis_Chiotis\" title=\"Man<PERSON> Chiotis\"><PERSON><PERSON></a>, Greek singer-songwriter and <a href=\"https://wikipedia.org/wiki/<PERSON>uz<PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\">bouzouki</a> player (b. 1920)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Manolis_Chiotis\" title=\"<PERSON><PERSON> Chiotis\"><PERSON><PERSON></a>, Greek singer-songwriter and <a href=\"https://wikipedia.org/wiki/Bouzou<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\">bouzouki</a> player (b. 1920)", "links": [{"title": "Manolis <PERSON>ot<PERSON>", "link": "https://wikipedia.org/wiki/Manolis_Chiotis"}, {"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>uz<PERSON><PERSON>"}]}, {"year": "1975", "text": "<PERSON>, American baseball player and coach (b. 1911)", "html": "1975 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player and coach (b. 1911)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player and coach (b. 1911)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1978", "text": "<PERSON><PERSON><PERSON><PERSON>, President of Ireland (b. 1911)", "html": "1978 - <a href=\"https://wikipedia.org/wiki/<PERSON>arbhall_%C3%93_D%C3%A1laigh\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, President of Ireland (b. 1911)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_%C3%93_D%C3%A1laigh\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, President of Ireland (b. 1911)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Cearbhall_%C3%93_D%C3%A1laigh"}]}, {"year": "1980", "text": "<PERSON>, American mathematician and astronomer (b. 1888)", "html": "1980 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American mathematician and astronomer (b. 1888)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American mathematician and astronomer (b. 1888)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1985", "text": "<PERSON>, English actor, director, and manager (b. 1908)", "html": "1985 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actor, director, and manager (b. 1908)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actor, director, and manager (b. 1908)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1987", "text": "<PERSON>, Canadian accountant, lawyer, and politician, 22nd Canadian Minister of Finance (b. 1906)", "html": "1987 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian accountant, lawyer, and politician, 22nd <a href=\"https://wikipedia.org/wiki/Minister_of_Finance_(Canada)\" title=\"Minister of Finance (Canada)\">Canadian Minister of Finance</a> (b. 1906)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian accountant, lawyer, and politician, 22nd <a href=\"https://wikipedia.org/wiki/Minister_of_Finance_(Canada)\" title=\"Minister of Finance (Canada)\">Canadian Minister of Finance</a> (b. 1906)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "Minister of Finance (Canada)", "link": "https://wikipedia.org/wiki/Minister_of_Finance_(Canada)"}]}, {"year": "1987", "text": "<PERSON>, American captain, actor, and singer (b. 1918)", "html": "1987 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(actor)\" title=\"<PERSON> (actor)\"><PERSON></a>, American captain, actor, and singer (b. 1918)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(actor)\" title=\"<PERSON> (actor)\"><PERSON></a>, American captain, actor, and singer (b. 1918)", "links": [{"title": "<PERSON> (actor)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>(actor)"}]}, {"year": "1991", "text": "<PERSON><PERSON><PERSON>, Turkish architect and politician, Mayor of Ankara (b. 1927)", "html": "1991 - <a href=\"https://wikipedia.org/wiki/Vedat_Dalokay\" title=\"Vedat Daloka<PERSON>\"><PERSON><PERSON><PERSON></a>, Turkish architect and politician, <a href=\"https://wikipedia.org/wiki/List_of_mayors_of_Ankara\" title=\"List of mayors of Ankara\">Mayor of Ankara</a> (b. 1927)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Vedat_Dalokay\" title=\"Vedat Dalokay\"><PERSON><PERSON><PERSON></a>, Turkish architect and politician, <a href=\"https://wikipedia.org/wiki/List_of_mayors_of_Ankara\" title=\"List of mayors of Ankara\">Mayor of Ankara</a> (b. 1927)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Vedat_Dalokay"}, {"title": "List of mayors of Ankara", "link": "https://wikipedia.org/wiki/List_of_mayors_of_Ankara"}]}, {"year": "1991", "text": "<PERSON>, American businessman, founded Fender Musical Instruments Corporation (b. 1909)", "html": "1991 - <a href=\"https://wikipedia.org/wiki/Leo_Fender\" title=\"Leo Fender\"><PERSON></a>, American businessman, founded <a href=\"https://wikipedia.org/wiki/Fender_Musical_Instruments_Corporation\" class=\"mw-redirect\" title=\"Fender Musical Instruments Corporation\">Fender Musical Instruments Corporation</a> (b. 1909)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Leo_Fender\" title=\"Leo Fender\"><PERSON></a>, American businessman, founded <a href=\"https://wikipedia.org/wiki/Fender_Musical_Instruments_Corporation\" class=\"mw-redirect\" title=\"Fender Musical Instruments Corporation\">Fender Musical Instruments Corporation</a> (b. 1909)", "links": [{"title": "Leo Fender", "link": "https://wikipedia.org/wiki/<PERSON>_Fender"}, {"title": "Fender Musical Instruments Corporation", "link": "https://wikipedia.org/wiki/Fender_Musical_Instruments_Corporation"}]}, {"year": "1992", "text": "<PERSON>, Canadian-American actor and director (b. 1914)", "html": "1992 - <a href=\"https://wikipedia.org/wiki/John_<PERSON>_(actor)\" class=\"mw-redirect\" title=\"<PERSON> (actor)\"><PERSON></a>, Canadian-American actor and director (b. 1914)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/John_Ireland_(actor)\" class=\"mw-redirect\" title=\"<PERSON> (actor)\"><PERSON></a>, Canadian-American actor and director (b. 1914)", "links": [{"title": "<PERSON> (actor)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(actor)"}]}, {"year": "1992", "text": "<PERSON>, American pianist and composer (b. 1930)", "html": "1992 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American pianist and composer (b. 1930)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American pianist and composer (b. 1930)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1994", "text": "<PERSON>, American actor (b. 1913)", "html": "1994 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor (b. 1913)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor (b. 1913)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1994", "text": "<PERSON><PERSON>, French-American actress and singer (b. 1904)", "html": "1994 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, French-American actress and singer (b. 1904)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, French-American actress and singer (b. 1904)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>"}]}, {"year": "1994", "text": "<PERSON><PERSON>, Latvian-born explorer (b. 1911)", "html": "1994 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, <a href=\"https://wikipedia.org/wiki/Latvia\" title=\"Latvia\">Latvian</a>-born explorer (b. 1911)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, <a href=\"https://wikipedia.org/wiki/Latvia\" title=\"Latvia\">Latvian</a>-born explorer (b. 1911)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}, {"title": "Latvia", "link": "https://wikipedia.org/wiki/Latvia"}]}, {"year": "1997", "text": "<PERSON><PERSON><PERSON>, English cleric and author, created The Railway Series, the basis for <PERSON> the Tank Engine (b. 1911)", "html": "1997 - <a href=\"https://wikipedia.org/wiki/Wil<PERSON>_<PERSON>wdry\" title=\"<PERSON><PERSON><PERSON>w<PERSON>\"><PERSON><PERSON><PERSON></a>, English cleric and author, created <i><a href=\"https://wikipedia.org/wiki/The_Railway_Series\" title=\"The Railway Series\">The Railway Series</a></i>, the basis for <i><a href=\"https://wikipedia.org/wiki/Thomas_the_Tank_Engine\" title=\"Thomas the Tank Engine\">Thomas the Tank Engine</a></i> (b. 1911)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/W<PERSON><PERSON>_<PERSON>wdr<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, English cleric and author, created <i><a href=\"https://wikipedia.org/wiki/The_Railway_Series\" title=\"The Railway Series\">The Railway Series</a></i>, the basis for <i><a href=\"https://wikipedia.org/wiki/Thomas_the_Tank_Engine\" title=\"Thomas the Tank Engine\">Thomas the Tank Engine</a></i> (b. 1911)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/W<PERSON><PERSON>_<PERSON>dry"}, {"title": "The Railway Series", "link": "https://wikipedia.org/wiki/The_Railway_Series"}, {"title": "<PERSON> the Tank Engine", "link": "https://wikipedia.org/wiki/Thomas_the_Tank_Engine"}]}, {"year": "1998", "text": "<PERSON><PERSON><PERSON>, Russian ballerina (b. 1910)", "html": "1998 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Russian ballerina (b. 1910)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Russian ballerina (b. 1910)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1999", "text": "<PERSON>, French philosopher and author (b. 1905)", "html": "1999 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French philosopher and author (b. 1905)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French philosopher and author (b. 1905)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1999", "text": "<PERSON>, English comedian and actor (b. 1925)", "html": "1999 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English comedian and actor (b. 1925)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Wise\"><PERSON></a>, English comedian and actor (b. 1925)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2001", "text": "<PERSON>, South Korean businessman, founded Hyundai (b. 1915)", "html": "2001 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>-<PERSON>\" title=\"<PERSON>\"><PERSON></a>, South Korean businessman, founded <a href=\"https://wikipedia.org/wiki/Hyundai_Group\" title=\"Hyundai Group\">Hyundai</a> (b. 1915)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, South Korean businessman, founded <a href=\"https://wikipedia.org/wiki/Hyundai_Group\" title=\"Hyundai Group\">Hyundai</a> (b. 1915)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>-<PERSON>"}, {"title": "Hyundai Group", "link": "https://wikipedia.org/wiki/Hyundai_Group"}]}, {"year": "2001", "text": "<PERSON>, English actor and singer (b. 1920)", "html": "2001 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(actor)\" title=\"<PERSON> (actor)\"><PERSON></a>, English actor and singer (b. 1920)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(actor)\" title=\"<PERSON> (actor)\"><PERSON></a>, English actor and singer (b. 1920)", "links": [{"title": "<PERSON> (actor)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(actor)"}]}, {"year": "2002", "text": "<PERSON>, American lieutenant, lawyer, and politician, 70th Governor of Georgia (b. 1913)", "html": "2002 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lieutenant, lawyer, and politician, 70th <a href=\"https://wikipedia.org/wiki/Governor_of_Georgia\" title=\"Governor of Georgia\">Governor of Georgia</a> (b. 1913)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lieutenant, lawyer, and politician, 70th <a href=\"https://wikipedia.org/wiki/Governor_of_Georgia\" title=\"Governor of Georgia\">Governor of Georgia</a> (b. 1913)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}, {"title": "Governor of Georgia", "link": "https://wikipedia.org/wiki/Governor_of_Georgia"}]}, {"year": "2003", "text": "<PERSON><PERSON>, Indian author (b. 1923)", "html": "2003 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Indian author (b. 1923)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Indian author (b. 1923)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>"}]}, {"year": "2003", "text": "<PERSON><PERSON>, Indonesian general and politician, 4th Vice President of Indonesia (b. 1924)", "html": "2003 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Indonesian general and politician, 4th <a href=\"https://wikipedia.org/wiki/Vice_President_of_Indonesia\" title=\"Vice President of Indonesia\">Vice President of Indonesia</a> (b. 1924)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Indonesian general and politician, 4th <a href=\"https://wikipedia.org/wiki/Vice_President_of_Indonesia\" title=\"Vice President of Indonesia\">Vice President of Indonesia</a> (b. 1924)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Umar_<PERSON>ira<PERSON>ik<PERSON><PERSON>h"}, {"title": "Vice President of Indonesia", "link": "https://wikipedia.org/wiki/Vice_President_of_Indonesia"}]}, {"year": "2004", "text": "<PERSON><PERSON><PERSON><PERSON>, French actress, dancer, and choreographer (b. 1924)", "html": "2004 - <a href=\"https://wikipedia.org/wiki/Ludmilla_Tch%C3%A9rina\" title=\"<PERSON>d<PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, French actress, dancer, and choreographer (b. 1924)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Lud<PERSON><PERSON>_Tch%C3%A9<PERSON>\" title=\"<PERSON>d<PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, French actress, dancer, and choreographer (b. 1924)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Ludmilla_Tch%C3%A9rina"}]}, {"year": "2005", "text": "<PERSON>, American police officer and actor (b. 1923)", "html": "2005 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American police officer and actor (b. 1923)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American police officer and actor (b. 1923)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2005", "text": "<PERSON>, American singer and pianist (b. 1924)", "html": "2005 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer and pianist (b. 1924)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer and pianist (b. 1924)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2007", "text": "<PERSON>, American author and illustrator (b. 1969)", "html": "2007 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author and illustrator (b. 1969)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author and illustrator (b. 1969)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2007", "text": "<PERSON>, Norwegian hurdler and journalist (b. 1936)", "html": "2007 - <a href=\"https://wikipedia.org/wiki/Sven_<PERSON>_H%C3%B8iby\" title=\"<PERSON>\"><PERSON></a>, Norwegian hurdler and journalist (b. 1936)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Sven_<PERSON><PERSON>_H%C3%B8iby\" title=\"<PERSON>\"><PERSON></a>, Norwegian hurdler and journalist (b. 1936)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Sven_<PERSON><PERSON>_H%C3%B8iby"}]}, {"year": "2008", "text": "<PERSON>, English-American geographer and academic (b. 1948)", "html": "2008 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English-American geographer and academic (b. 1948)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English-American geographer and academic (b. 1948)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2008", "text": "<PERSON>, Chilean architect and academic (b. 1931)", "html": "2008 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>_Fuente\" title=\"<PERSON>\"><PERSON></a>, Chilean architect and academic (b. 1931)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>_Fu<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Chilean architect and academic (b. 1931)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "2008", "text": "<PERSON>, American murderer (b. 1925)", "html": "2008 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(serial_killer)\" class=\"mw-redirect\" title=\"<PERSON> (serial killer)\"><PERSON></a>, American murderer (b. 1925)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(serial_killer)\" class=\"mw-redirect\" title=\"<PERSON> (serial killer)\"><PERSON></a>, American murderer (b. 1925)", "links": [{"title": "<PERSON> (serial killer)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(serial_killer)"}]}, {"year": "2009", "text": "<PERSON><PERSON>, Indian army officer (b. 1978)", "html": "2009 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_(soldier)\" title=\"<PERSON><PERSON> (soldier)\"><PERSON><PERSON></a>, Indian army officer (b. 1978)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_(soldier)\" title=\"<PERSON><PERSON> (soldier)\"><PERSON><PERSON></a>, Indian army officer (b. 1978)", "links": [{"title": "<PERSON><PERSON> (soldier)", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_(soldier)"}]}, {"year": "2009", "text": "<PERSON>, Canadian ice hockey player and coach (b. 1960)", "html": "2009 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player and coach (b. 1960)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player and coach (b. 1960)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2010", "text": "<PERSON>, German director and manager (b. 1919)", "html": "2010 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German director and manager (b. 1919)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German director and manager (b. 1919)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2011", "text": "<PERSON><PERSON><PERSON>, American singer-songwriter (b. 1946)", "html": "2011 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American singer-songwriter (b. 1946)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American singer-songwriter (b. 1946)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "2011", "text": "<PERSON><PERSON>, German footballer (b. 1944)", "html": "2011 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, German footballer (b. 1944)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, German footballer (b. 1944)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "2011", "text": "<PERSON><PERSON><PERSON>, Czech footballer and manager (b. 1931)", "html": "2011 - <a href=\"https://wikipedia.org/wiki/Ladislav_Nov%C3%A1k\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Czech footballer and manager (b. 1931)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Ladislav_Nov%C3%A1k\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Czech footballer and manager (b. 1931)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Ladislav_Nov%C3%A1k"}]}, {"year": "2011", "text": "<PERSON><PERSON>, American singer and pianist (b. 1913)", "html": "2011 - <a href=\"https://wikipedia.org/wiki/Pine<PERSON>_Perkins\" title=\"Pine<PERSON> Perkins\"><PERSON><PERSON></a>, American singer and pianist (b. 1913)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Pinetop_Perkins\" title=\"Pine<PERSON> Perkins\"><PERSON><PERSON></a>, American singer and pianist (b. 1913)", "links": [{"title": "Pinetop Perkins", "link": "https://wikipedia.org/wiki/Pine<PERSON>_Perkins"}]}, {"year": "2012", "text": "<PERSON><PERSON>, German economist and businessman (b. 1926)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, German economist and businessman (b. 1926)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, German economist and businessman (b. 1926)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "2012", "text": "<PERSON>, American football player and coach (b. 1931)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player and coach (b. 1931)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player and coach (b. 1931)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2012", "text": "<PERSON>, English director, screenwriter, and production designer (b. 1927)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English director, screenwriter, and production designer (b. 1927)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English director, screenwriter, and production designer (b. 1927)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2012", "text": "<PERSON><PERSON>, Italian poet and screenwriter (b. 1920)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_G<PERSON>\" title=\"Tonino Guerra\"><PERSON><PERSON></a>, Italian poet and screenwriter (b. 1920)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"Tonino Guerra\"><PERSON><PERSON></a>, Italian poet and screenwriter (b. 1920)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>no_Guerra"}]}, {"year": "2012", "text": "<PERSON>, American sociologist, author, and academic (b. 1929)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American sociologist, author, and academic (b. 1929)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American sociologist, author, and academic (b. 1929)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2012", "text": "<PERSON>, Russian chess player and trainer (b. 1945)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian chess player and trainer (b. 1945)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian chess player and trainer (b. 1945)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "2012", "text": "<PERSON>, Russian geologist and politician (b. 1934)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/Marina_Salye\" title=\"Marina Salye\"><PERSON></a>, Russian geologist and politician (b. 1934)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Marina_Salye\" title=\"Marina Salye\"><PERSON></a>, Russian geologist and politician (b. 1934)", "links": [{"title": "Marina <PERSON>", "link": "https://wikipedia.org/wiki/Marina_Salye"}]}, {"year": "2013", "text": "<PERSON><PERSON>, Nigerian novelist, poet, and critic (b. 1930)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/Chinua_Achebe\" title=\"Chinua Achebe\"><PERSON><PERSON></a>, Nigerian novelist, poet, and critic (b. 1930)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Chinua_Achebe\" title=\"Chinua Achebe\"><PERSON><PERSON></a>, Nigerian novelist, poet, and critic (b. 1930)", "links": [{"title": "Chinua Achebe", "link": "https://wikipedia.org/wiki/Chinua_Achebe"}]}, {"year": "2013", "text": "<PERSON>, American author and screenwriter (b. 1949)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author and screenwriter (b. 1949)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author and screenwriter (b. 1949)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2013", "text": "<PERSON><PERSON><PERSON>, American football player and coach (b. 1932)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/Harlon_Hill\" title=\"Harlon Hill\"><PERSON><PERSON><PERSON></a>, American football player and coach (b. 1932)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Harlon_<PERSON>\" title=\"Harlon Hill\"><PERSON><PERSON><PERSON></a>, American football player and coach (b. 1932)", "links": [{"title": "Harlon Hill", "link": "https://wikipedia.org/wiki/Harlon_<PERSON>"}]}, {"year": "2013", "text": "<PERSON>, Italian sprinter and politician (b. 1952)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian sprinter and politician (b. 1952)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian sprinter and politician (b. 1952)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2013", "text": "<PERSON><PERSON><PERSON><PERSON>, Italian director and screenwriter (b. 1926)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Italian director and screenwriter (b. 1926)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Italian director and screenwriter (b. 1926)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "2014", "text": "<PERSON><PERSON><PERSON><PERSON>, Fijian lawyer and politician, 25th Attorney-General of Fiji (b. 1929)", "html": "2014 - <a href=\"https://wikipedia.org/wiki/<PERSON>ori<PERSON><PERSON>_<PERSON>\" title=\"Qori<PERSON><PERSON> Bale\"><PERSON><PERSON><PERSON><PERSON></a>, Fijian lawyer and politician, 25th <a href=\"https://wikipedia.org/wiki/Attorney_General_of_Fiji\" class=\"mw-redirect\" title=\"Attorney General of Fiji\">Attorney-General of Fiji</a> (b. 1929)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>ori<PERSON><PERSON>_<PERSON>\" title=\"Qori<PERSON><PERSON>le\"><PERSON><PERSON><PERSON><PERSON></a>, Fijian lawyer and politician, 25th <a href=\"https://wikipedia.org/wiki/Attorney_General_of_Fiji\" class=\"mw-redirect\" title=\"Attorney General of Fiji\">Attorney-General of Fiji</a> (b. 1929)", "links": [{"title": "Qori<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>ori<PERSON><PERSON>_<PERSON>le"}, {"title": "Attorney General of Fiji", "link": "https://wikipedia.org/wiki/Attorney_General_of_Fiji"}]}, {"year": "2014", "text": "<PERSON>, American football player and soldier (b. 1924)", "html": "2014 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player and soldier (b. 1924)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player and soldier (b. 1924)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "2014", "text": "<PERSON>, American golfer (b. 1921)", "html": "2014 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American golfer (b. 1921)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American golfer (b. 1921)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2014", "text": "<PERSON><PERSON><PERSON>, Nigerian police officer and politician (b. 1945)", "html": "2014 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Nigerian police officer and politician (b. 1945)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Nigerian police officer and politician (b. 1945)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>meon_<PERSON>e"}]}, {"year": "2014", "text": "<PERSON>, American actor (b. 1948)", "html": "2014 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor (b. 1948)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor (b. 1948)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2014", "text": "<PERSON><PERSON><PERSON>, Iraqi patriarch (b. 1933)", "html": "2014 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Iraqi patriarch (b. 1933)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"I<PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Iraqi patriarch (b. 1933)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>was"}]}, {"year": "2015", "text": "<PERSON><PERSON><PERSON>, Nigerian general and politician, Governor of Benue State (b. 1947)", "html": "2015 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Nigerian general and politician, <a href=\"https://wikipedia.org/wiki/List_of_Governors_of_Benue_State\" class=\"mw-redirect\" title=\"List of Governors of Benue State\">Governor of Benue State</a> (b. 1947)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Nigerian general and politician, <a href=\"https://wikipedia.org/wiki/List_of_Governors_of_Benue_State\" class=\"mw-redirect\" title=\"List of Governors of Benue State\">Governor of Benue State</a> (b. 1947)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>t"}, {"title": "List of Governors of Benue State", "link": "https://wikipedia.org/wiki/List_of_Governors_of_Benue_State"}]}, {"year": "2015", "text": "<PERSON>, American lieutenant and football player (b. 1925)", "html": "2015 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lieutenant and football player (b. 1925)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lieutenant and football player (b. 1925)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2015", "text": "<PERSON>, American sergeant (b. 1938)", "html": "2015 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_Binnicker\" title=\"<PERSON>\"><PERSON></a>, American sergeant (b. 1938)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_Binnicker\" title=\"<PERSON>nick<PERSON>\"><PERSON></a>, American sergeant (b. 1938)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON><PERSON>"}]}, {"year": "2015", "text": "<PERSON>, Swiss painter, sculptor, and illustrator (b. 1909)", "html": "2015 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swiss painter, sculptor, and illustrator (b. 1909)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swiss painter, sculptor, and illustrator (b. 1909)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>i"}]}, {"year": "2015", "text": "<PERSON><PERSON><PERSON>, Danish singer and guitarist (b. 1925)", "html": "2015 - <a href=\"https://wikipedia.org/wiki/J%C3%B8<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Danish singer and guitarist (b. 1925)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/J%C3%B8<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Danish singer and guitarist (b. 1925)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/J%C3%B8<PERSON>_<PERSON>"}]}, {"year": "2015", "text": "<PERSON>, Canadian actress (b. 1955)", "html": "2015 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian actress (b. 1955)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian actress (b. 1955)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Alberta_Watson"}]}, {"year": "2017", "text": "<PERSON>, American game show host and producer (b. 1929)", "html": "2017 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American game show host and producer (b. 1929)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American game show host and producer (b. 1929)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2017", "text": "<PERSON>, English author (b. 1930)", "html": "2017 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English author (b. 1930)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English author (b. 1930)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2017", "text": "<PERSON>, Irish republican and deputy First Minister of Northern Ireland (b. 1950)", "html": "2017 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Irish republican and <a href=\"https://wikipedia.org/wiki/First_Minister_and_deputy_First_Minister\" class=\"mw-redirect\" title=\"First Minister and deputy First Minister\">deputy First Minister of Northern Ireland</a> (b. 1950)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Irish republican and <a href=\"https://wikipedia.org/wiki/First_Minister_and_deputy_First_Minister\" class=\"mw-redirect\" title=\"First Minister and deputy First Minister\">deputy First Minister of Northern Ireland</a> (b. 1950)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "First Minister and deputy First Minister", "link": "https://wikipedia.org/wiki/First_Minister_and_deputy_First_Minister"}]}, {"year": "2017", "text": "<PERSON>, British cyclist (b. 1981)", "html": "2017 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(cyclist)\" title=\"<PERSON> (cyclist)\"><PERSON></a>, British cyclist (b. 1981)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(cyclist)\" title=\"<PERSON> (cyclist)\"><PERSON></a>, British cyclist (b. 1981)", "links": [{"title": "<PERSON> (cyclist)", "link": "https://wikipedia.org/wiki/<PERSON>_(cyclist)"}]}, {"year": "2019", "text": "<PERSON>, British music promoter (b. 1923)", "html": "2019 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> CBE, British music promoter (b. 1923)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> CBE, British music promoter (b. 1923)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2019", "text": "<PERSON><PERSON><PERSON>, Peruvian sociologist (b. 1949)", "html": "2019 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Peruvian sociologist (b. 1949)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Peruvian sociologist (b. 1949)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "2021", "text": "<PERSON><PERSON>, Egyptian secularist, feminist (b. 1931)", "html": "2021 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Egyptian secularist, feminist (b. 1931)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Egyptian secularist, feminist (b. 1931)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>"}]}, {"year": "2023", "text": "<PERSON>, American basketball player (b. 1942)", "html": "2023 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player (b. 1942)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player (b. 1942)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}]}}