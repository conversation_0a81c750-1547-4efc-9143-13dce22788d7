{"date": "October 21", "url": "https://wikipedia.org/wiki/October_21", "data": {"Events": [{"year": "1096", "text": "A Seljuk Turkish army successfully fights off the People's Crusade at the Battle of Civetot.", "html": "1096 - A Seljuk Turkish army successfully fights off the <a href=\"https://wikipedia.org/wiki/People%27s_Crusade\" title=\"People's Crusade\">People's Crusade</a> at the <a href=\"https://wikipedia.org/wiki/Battle_of_Civetot\" title=\"Battle of Civetot\">Battle of Civetot</a>.", "no_year_html": "A Seljuk Turkish army successfully fights off the <a href=\"https://wikipedia.org/wiki/People%27s_Crusade\" title=\"People's Crusade\">People's Crusade</a> at the <a href=\"https://wikipedia.org/wiki/Battle_of_Civetot\" title=\"Battle of Civetot\">Battle of Civetot</a>.", "links": [{"title": "People's Crusade", "link": "https://wikipedia.org/wiki/People%27s_Crusade"}, {"title": "Battle of Civetot", "link": "https://wikipedia.org/wiki/Battle_of_Civetot"}]}, {"year": "1097", "text": "First Crusade: Crusaders led by <PERSON> of Bouillon, <PERSON><PERSON><PERSON> of Taranto, and <PERSON>, Count of Toulouse, begin the Siege of Antioch.", "html": "1097 - <a href=\"https://wikipedia.org/wiki/First_Crusade\" title=\"First Crusade\">First Crusade</a>: Crusaders led by <a href=\"https://wikipedia.org/wiki/<PERSON>_of_Bouillon\" title=\"<PERSON> of Bouillon\"><PERSON> of Bouillon</a>, <a href=\"https://wikipedia.org/wiki/Bohemond_I_of_Antioch\" title=\"Bohemond I of Antioch\"><PERSON><PERSON><PERSON> of Taranto</a>, and <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Count_of_Toulouse\" title=\"<PERSON>, Count of Toulouse\"><PERSON>, Count of Toulouse</a>, begin the <a href=\"https://wikipedia.org/wiki/Siege_of_Antioch\" title=\"Siege of Antioch\">Siege of Antioch</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/First_Crusade\" title=\"First Crusade\">First Crusade</a>: Crusaders led by <a href=\"https://wikipedia.org/wiki/<PERSON>_of_Bouillon\" title=\"<PERSON> of Bouillon\"><PERSON> of Bouillon</a>, <a href=\"https://wikipedia.org/wiki/Bohemond_I_of_Antioch\" title=\"Bohemond I of Antioch\">Bo<PERSON><PERSON> of Taranto</a>, and <a href=\"https://wikipedia.org/wiki/<PERSON>,_Count_of_Toulouse\" title=\"<PERSON>, Count of Toulouse\"><PERSON>, Count of Toulouse</a>, begin the <a href=\"https://wikipedia.org/wiki/Siege_of_Antioch\" title=\"Siege of Antioch\">Siege of Antioch</a>.", "links": [{"title": "First Crusade", "link": "https://wikipedia.org/wiki/First_Crusade"}, {"title": "<PERSON> of Bouillon", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_Bouillon"}, {"title": "<PERSON><PERSON><PERSON> I of Antioch", "link": "https://wikipedia.org/wiki/Bohemond_I_of_Antioch"}, {"title": "<PERSON>, Count of Toulouse", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Count_of_Toulouse"}, {"title": "Siege of Antioch", "link": "https://wikipedia.org/wiki/Siege_of_Antioch"}]}, {"year": "1392", "text": "Japanese Emperor <PERSON><PERSON><PERSON><PERSON><PERSON> abdicates in favor of rival claimant <PERSON><PERSON><PERSON><PERSON><PERSON>.", "html": "1392 - Japanese <a href=\"https://wikipedia.org/wiki/Emperor_<PERSON>-Ka<PERSON>\" title=\"Emperor <PERSON><PERSON>Kameyama\">Emperor <PERSON><PERSON><PERSON></a> abdicates in favor of rival claimant <a href=\"https://wikipedia.org/wiki/Emperor_<PERSON>-Komatsu\" title=\"Emperor Go-Komatsu\"><PERSON><PERSON><PERSON><PERSON><PERSON></a>.", "no_year_html": "Japanese <a href=\"https://wikipedia.org/wiki/Emperor_<PERSON><PERSON>Ka<PERSON>\" title=\"Emperor <PERSON><PERSON>Ka<PERSON>\">Emperor <PERSON><PERSON></a> abdicates in favor of rival claimant <a href=\"https://wikipedia.org/wiki/Emperor_<PERSON><PERSON>Ko<PERSON>su\" title=\"Emperor Go-Komatsu\"><PERSON><PERSON><PERSON><PERSON><PERSON></a>.", "links": [{"title": "Emperor <PERSON><PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>"}, {"title": "Emperor <PERSON><PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>"}]}, {"year": "1512", "text": "<PERSON> joins the theological faculty of the University of Wittenberg.", "html": "1512 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> joins the theological faculty of the <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_University_of_Halle-Wittenberg\" class=\"mw-redirect\" title=\"Martin Luther University of Halle-Wittenberg\">University of Wittenberg</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> joins the theological faculty of the <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_University_of_Halle-Wittenberg\" class=\"mw-redirect\" title=\"Martin Luther University of Halle-Wittenberg\">University of Wittenberg</a>.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Martin <PERSON> University of Halle-Wittenberg", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_University_of_Halle-Wittenberg"}]}, {"year": "1520", "text": "<PERSON> discovers the islands of Saint Pierre and Miquelon, bestowing them their original name of \"Islands of the 11,000 Virgins\".", "html": "1520 - <a href=\"https://wikipedia.org/wiki/Jo%C3%A3o_%C3%81l<PERSON><PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> discovers the islands of <a href=\"https://wikipedia.org/wiki/Saint_Pierre_and_Miquelon\" title=\"Saint Pierre and Miquelon\">Saint Pierre and Miquelon</a>, bestowing them their original name of \"Islands of the 11,000 Virgins\".", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Jo%C3%A3o_%C3%81l<PERSON><PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> discovers the islands of <a href=\"https://wikipedia.org/wiki/<PERSON>_Pierre_and_Miquelon\" title=\"Saint Pierre and Miquelon\">Saint Pierre and Miquelon</a>, bestowing them their original name of \"Islands of the 11,000 Virgins\".", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Jo%C3%A3o_%C3%81l<PERSON><PERSON>_<PERSON>"}, {"title": "Saint Pierre and Miquelon", "link": "https://wikipedia.org/wiki/Saint_Pierre_and_Miquelon"}]}, {"year": "1600", "text": "Tokugawa <PERSON><PERSON><PERSON><PERSON> defeats the leaders of rival Japanese clans in the Battle of Sekigahara and becomes shōgun of Japan.", "html": "1600 - <a href=\"https://wikipedia.org/wiki/Tokugawa_Ieyasu\" title=\"Tokugawa Ieyasu\">Tokugawa Ieyasu</a> defeats the leaders of rival Japanese clans in the <a href=\"https://wikipedia.org/wiki/Battle_of_Sekigahara\" title=\"Battle of Sekigahara\">Battle of Sekigahara</a> and becomes shōgun of Japan.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Tokugawa_Ieyasu\" title=\"Tokugawa Ieyasu\">Tokugawa Ieyasu</a> defeats the leaders of rival Japanese clans in the <a href=\"https://wikipedia.org/wiki/Battle_of_Sekigahara\" title=\"Battle of Sekigahara\">Battle of Sekigahara</a> and becomes shōgun of Japan.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON><PERSON>u"}, {"title": "Battle of Sekigahara", "link": "https://wikipedia.org/wiki/Battle_of_Sekigahara"}]}, {"year": "1774", "text": "The flag of Taunton, Massachusetts is the first to include the word \"Liberty\".", "html": "1774 - The <a href=\"https://wikipedia.org/wiki/Flag_of_Taunton,_Massachusetts\" title=\"Flag of Taunton, Massachusetts\">flag of Taunton, Massachusetts</a> is the first to include the word \"Liberty\".", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Flag_of_Taunton,_Massachusetts\" title=\"Flag of Taunton, Massachusetts\">flag of Taunton, Massachusetts</a> is the first to include the word \"Liberty\".", "links": [{"title": "Flag of Taunton, Massachusetts", "link": "https://wikipedia.org/wiki/Flag_of_Taunton,_Massachusetts"}]}, {"year": "1797", "text": "In Boston Harbor, the 44-gun United States Navy frigate USS Constitution is launched.", "html": "1797 - In <a href=\"https://wikipedia.org/wiki/Boston_Harbor\" title=\"Boston Harbor\">Boston Harbor</a>, the 44-gun <a href=\"https://wikipedia.org/wiki/United_States_Navy\" title=\"United States Navy\">United States Navy</a> frigate <a href=\"https://wikipedia.org/wiki/USS_Constitution\" title=\"USS Constitution\">USS <i>Constitution</i></a> is launched.", "no_year_html": "In <a href=\"https://wikipedia.org/wiki/Boston_Harbor\" title=\"Boston Harbor\">Boston Harbor</a>, the 44-gun <a href=\"https://wikipedia.org/wiki/United_States_Navy\" title=\"United States Navy\">United States Navy</a> frigate <a href=\"https://wikipedia.org/wiki/USS_Constitution\" title=\"USS Constitution\">USS <i>Constitution</i></a> is launched.", "links": [{"title": "Boston Harbor", "link": "https://wikipedia.org/wiki/Boston_Harbor"}, {"title": "United States Navy", "link": "https://wikipedia.org/wiki/United_States_Navy"}, {"title": "USS Constitution", "link": "https://wikipedia.org/wiki/USS_Constitution"}]}, {"year": "1805", "text": "Napoleonic Wars: A British fleet led by Lord <PERSON> defeats a combined French and Spanish fleet under Admiral <PERSON><PERSON><PERSON> in the Battle of Trafalgar.", "html": "1805 - <a href=\"https://wikipedia.org/wiki/Napoleonic_Wars\" title=\"Napoleonic Wars\">Napoleonic Wars</a>: A British fleet led by <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>,_1st_Viscount_<PERSON>\" title=\"<PERSON><PERSON><PERSON>, 1st Viscount <PERSON>\">Lord <PERSON></a> defeats a combined French and Spanish fleet under <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON><PERSON><PERSON>\">Admiral <PERSON><PERSON><PERSON></a> in the <a href=\"https://wikipedia.org/wiki/Battle_of_Trafalgar\" title=\"Battle of Trafalgar\">Battle of Trafalgar</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Napoleonic_Wars\" title=\"Napoleonic Wars\">Napoleonic Wars</a>: A British fleet led by <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>,_1st_Viscount_<PERSON>\" title=\"<PERSON><PERSON><PERSON>, 1st Viscount <PERSON>\">Lord <PERSON></a> defeats a combined French and Spanish fleet under <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON><PERSON><PERSON>\">Admiral <PERSON><PERSON><PERSON></a> in the <a href=\"https://wikipedia.org/wiki/Battle_of_Trafalgar\" title=\"Battle of Trafalgar\">Battle of Trafalgar</a>.", "links": [{"title": "Napoleonic Wars", "link": "https://wikipedia.org/wiki/Napoleonic_Wars"}, {"title": "<PERSON><PERSON><PERSON>, 1st Viscount <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>rat<PERSON>_<PERSON>,_1st_Viscount_<PERSON>"}, {"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}, {"title": "Battle of Trafalgar", "link": "https://wikipedia.org/wiki/Battle_of_Trafalgar"}]}, {"year": "1824", "text": "Portland cement is patented.", "html": "1824 - <a href=\"https://wikipedia.org/wiki/Portland_cement\" title=\"Portland cement\">Portland cement</a> is patented.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Portland_cement\" title=\"Portland cement\">Portland cement</a> is patented.", "links": [{"title": "Portland cement", "link": "https://wikipedia.org/wiki/Portland_cement"}]}, {"year": "1854", "text": "Florence Nightingale and a staff of 38 nurses are sent to the Crimean War.", "html": "1854 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Nightingale\"><PERSON> Nightingale</a> and a staff of 38 nurses are sent to the <a href=\"https://wikipedia.org/wiki/Crimean_War\" title=\"Crimean War\">Crimean War</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Nightingale\"><PERSON> Nightingale</a> and a staff of 38 nurses are sent to the <a href=\"https://wikipedia.org/wiki/Crimean_War\" title=\"Crimean War\">Crimean War</a>.", "links": [{"title": "Florence Nightingale", "link": "https://wikipedia.org/wiki/Florence_Nightingale"}, {"title": "Crimean War", "link": "https://wikipedia.org/wiki/Crimean_War"}]}, {"year": "1861", "text": "American Civil War: Union forces under Colonel <PERSON> are defeated by Confederate troops in the second major battle of the war.", "html": "1861 - <a href=\"https://wikipedia.org/wiki/American_Civil_War\" title=\"American Civil War\">American Civil War</a>: Union forces under Colonel <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> are <a href=\"https://wikipedia.org/wiki/Battle_of_Ball%27s_Bluff\" title=\"Battle of Ball's Bluff\">defeated</a> by Confederate troops in the second major battle of the war.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/American_Civil_War\" title=\"American Civil War\">American Civil War</a>: Union forces under Colonel <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> are <a href=\"https://wikipedia.org/wiki/Battle_of_Ball%27s_Bluff\" title=\"Battle of Ball's Bluff\">defeated</a> by Confederate troops in the second major battle of the war.", "links": [{"title": "American Civil War", "link": "https://wikipedia.org/wiki/American_Civil_War"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "Battle of Ball's Bluff", "link": "https://wikipedia.org/wiki/Battle_of_Ball%27s_Bluff"}]}, {"year": "1867", "text": "The Medicine Lodge Treaty is signed by southern Great Plains Indian leaders. The treaty requires Native American Plains tribes to relocate to a reservation in the western Indian Territory.", "html": "1867 - The <a href=\"https://wikipedia.org/wiki/Medicine_Lodge_Treaty\" title=\"Medicine Lodge Treaty\">Medicine Lodge Treaty</a> is signed by southern <a href=\"https://wikipedia.org/wiki/Plains_Indians\" title=\"Plains Indians\">Great Plains Indian</a> leaders. The treaty requires Native American Plains tribes to relocate to a reservation in the western <a href=\"https://wikipedia.org/wiki/Indian_Territory\" title=\"Indian Territory\">Indian Territory</a>.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Medicine_Lodge_Treaty\" title=\"Medicine Lodge Treaty\">Medicine Lodge Treaty</a> is signed by southern <a href=\"https://wikipedia.org/wiki/Plains_Indians\" title=\"Plains Indians\">Great Plains Indian</a> leaders. The treaty requires Native American Plains tribes to relocate to a reservation in the western <a href=\"https://wikipedia.org/wiki/Indian_Territory\" title=\"Indian Territory\">Indian Territory</a>.", "links": [{"title": "Medicine Lodge Treaty", "link": "https://wikipedia.org/wiki/Medicine_Lodge_Treaty"}, {"title": "Plains Indians", "link": "https://wikipedia.org/wiki/Plains_Indians"}, {"title": "Indian Territory", "link": "https://wikipedia.org/wiki/Indian_Territory"}]}, {"year": "1879", "text": "<PERSON> applies for a patent for his design for an incandescent light bulb.", "html": "1879 - <a href=\"https://wikipedia.org/wiki/Thomas_<PERSON>\" title=\"Thomas Edison\"><PERSON></a> applies for a patent for his design for an <a href=\"https://wikipedia.org/wiki/Incandescent_light_bulb\" title=\"Incandescent light bulb\">incandescent light bulb</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Thomas_Edison\" title=\"Thomas Edison\"><PERSON></a> applies for a patent for his design for an <a href=\"https://wikipedia.org/wiki/Incandescent_light_bulb\" title=\"Incandescent light bulb\">incandescent light bulb</a>.", "links": [{"title": "Thomas <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Incandescent light bulb", "link": "https://wikipedia.org/wiki/Incandescent_light_bulb"}]}, {"year": "1888", "text": "The Swiss Social Democratic Party is founded.", "html": "1888 - The <a href=\"https://wikipedia.org/wiki/Social_Democratic_Party_of_Switzerland\" title=\"Social Democratic Party of Switzerland\">Swiss Social Democratic Party</a> is founded.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Social_Democratic_Party_of_Switzerland\" title=\"Social Democratic Party of Switzerland\">Swiss Social Democratic Party</a> is founded.", "links": [{"title": "Social Democratic Party of Switzerland", "link": "https://wikipedia.org/wiki/Social_Democratic_Party_of_Switzerland"}]}, {"year": "1892", "text": "Opening ceremonies for the World's Columbian Exposition are held in Chicago, though because construction was behind schedule, the exposition did not open until May 1, 1893.", "html": "1892 - Opening ceremonies for the <a href=\"https://wikipedia.org/wiki/World%27s_Columbian_Exposition\" title=\"World's Columbian Exposition\">World's Columbian Exposition</a> are held in Chicago, though because construction was behind schedule, the exposition did not open until May 1, 1893.", "no_year_html": "Opening ceremonies for the <a href=\"https://wikipedia.org/wiki/World%27s_Columbian_Exposition\" title=\"World's Columbian Exposition\">World's Columbian Exposition</a> are held in Chicago, though because construction was behind schedule, the exposition did not open until May 1, 1893.", "links": [{"title": "World's Columbian Exposition", "link": "https://wikipedia.org/wiki/World%27s_Columbian_Exposition"}]}, {"year": "1895", "text": "The capitulation of Tainan completes the Japanese conquest of Taiwan.", "html": "1895 - The <a href=\"https://wikipedia.org/wiki/Capitulation_of_Tainan\" title=\"Capitulation of Tainan\">capitulation of Tainan</a> completes the Japanese conquest of Taiwan.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Capitulation_of_Tainan\" title=\"Capitulation of Tainan\">capitulation of Tainan</a> completes the Japanese conquest of Taiwan.", "links": [{"title": "Capitulation of Tainan", "link": "https://wikipedia.org/wiki/Capitulation_of_Tainan"}]}, {"year": "1907", "text": "The 1907 Qaratog earthquake hits the borders of Uzbekistan and Tajikistan, killing between 12,000 and 15,000 people.", "html": "1907 - The <a href=\"https://wikipedia.org/wiki/1907_Qaratog_earthquake\" title=\"1907 Qaratog earthquake\">1907 Qaratog earthquake</a> hits the borders of Uzbekistan and Tajikistan, killing between 12,000 and 15,000 people.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/1907_Qaratog_earthquake\" title=\"1907 Qaratog earthquake\">1907 Qaratog earthquake</a> hits the borders of Uzbekistan and Tajikistan, killing between 12,000 and 15,000 people.", "links": [{"title": "1907 Qaratog earthquake", "link": "https://wikipedia.org/wiki/1907_Qaratog_earthquake"}]}, {"year": "1910", "text": "HMS <PERSON><PERSON> arrives in Halifax Harbour to become the first ship of the Royal Canadian Navy.", "html": "1910 - <a href=\"https://wikipedia.org/wiki/HMS_Niobe_(1897)\" title=\"HMS Niobe (1897)\">HMS <i><PERSON>obe</i></a> arrives in <a href=\"https://wikipedia.org/wiki/Halifax_Harbour\" title=\"Halifax Harbour\">Halifax Harbour</a> to become the first ship of the <a href=\"https://wikipedia.org/wiki/Royal_Canadian_Navy\" title=\"Royal Canadian Navy\">Royal Canadian Navy</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/HMS_Niobe_(1897)\" title=\"HMS Niobe (1897)\">HMS <i><PERSON>obe</i></a> arrives in <a href=\"https://wikipedia.org/wiki/Halifax_Harbour\" title=\"Halifax Harbour\">Halifax Harbour</a> to become the first ship of the <a href=\"https://wikipedia.org/wiki/Royal_Canadian_Navy\" title=\"Royal Canadian Navy\">Royal Canadian Navy</a>.", "links": [{"title": "HMS Niobe (1897)", "link": "https://wikipedia.org/wiki/HMS_<PERSON><PERSON>_(1897)"}, {"title": "Halifax Harbour", "link": "https://wikipedia.org/wiki/Halifax_Harbour"}, {"title": "Royal Canadian Navy", "link": "https://wikipedia.org/wiki/Royal_Canadian_Navy"}]}, {"year": "1912", "text": "First Balkan War: The Greek navy completes the capture of the island of Lemnos for use as a forward base against the Dardanelles.", "html": "1912 - <a href=\"https://wikipedia.org/wiki/First_Balkan_War\" title=\"First Balkan War\">First Balkan War</a>: The Greek navy <a href=\"https://wikipedia.org/wiki/Battle_of_Lemnos_(1912)\" class=\"mw-redirect\" title=\"Battle of Lemnos (1912)\">completes the capture</a> of the island of <a href=\"https://wikipedia.org/wiki/Lemnos\" title=\"Lemnos\">Lemnos</a> for use as a forward base against the <a href=\"https://wikipedia.org/wiki/Dardanelles\" title=\"Dardanelles\">Dardanelles</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/First_Balkan_War\" title=\"First Balkan War\">First Balkan War</a>: The Greek navy <a href=\"https://wikipedia.org/wiki/Battle_of_Lemnos_(1912)\" class=\"mw-redirect\" title=\"Battle of Lemnos (1912)\">completes the capture</a> of the island of <a href=\"https://wikipedia.org/wiki/Lemnos\" title=\"Lemnos\">Lemnos</a> for use as a forward base against the <a href=\"https://wikipedia.org/wiki/Dardanelles\" title=\"Dardanelles\">Dardanelles</a>.", "links": [{"title": "First Balkan War", "link": "https://wikipedia.org/wiki/First_Balkan_War"}, {"title": "Battle of Lemnos (1912)", "link": "https://wikipedia.org/wiki/Battle_of_Lemnos_(1912)"}, {"title": "Lemnos", "link": "https://wikipedia.org/wiki/Lemnos"}, {"title": "Dardanelles", "link": "https://wikipedia.org/wiki/Dardanelles"}]}, {"year": "1921", "text": "President <PERSON> delivers the first speech by a sitting U.S. president against lynching in the Deep South.", "html": "1921 - President <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> delivers the first speech by a sitting U.S. president against <a href=\"https://wikipedia.org/wiki/Lynching_in_the_United_States\" title=\"Lynching in the United States\">lynching</a> in the <a href=\"https://wikipedia.org/wiki/Deep_South\" title=\"Deep South\">Deep South</a>.", "no_year_html": "President <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> delivers the first speech by a sitting U.S. president against <a href=\"https://wikipedia.org/wiki/Lynching_in_the_United_States\" title=\"Lynching in the United States\">lynching</a> in the <a href=\"https://wikipedia.org/wiki/Deep_South\" title=\"Deep South\">Deep South</a>.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>"}, {"title": "Lynching in the United States", "link": "https://wikipedia.org/wiki/Lynching_in_the_United_States"}, {"title": "Deep South", "link": "https://wikipedia.org/wiki/Deep_South"}]}, {"year": "1931", "text": "A secret society in the Imperial Japanese Army launches an abortive coup d'état attempt.", "html": "1931 - A secret society in the <a href=\"https://wikipedia.org/wiki/Imperial_Japanese_Army\" title=\"Imperial Japanese Army\">Imperial Japanese Army</a> launches an abortive <a href=\"https://wikipedia.org/wiki/October_incident\" title=\"October incident\">coup d'état attempt</a>.", "no_year_html": "A secret society in the <a href=\"https://wikipedia.org/wiki/Imperial_Japanese_Army\" title=\"Imperial Japanese Army\">Imperial Japanese Army</a> launches an abortive <a href=\"https://wikipedia.org/wiki/October_incident\" title=\"October incident\">coup d'état attempt</a>.", "links": [{"title": "Imperial Japanese Army", "link": "https://wikipedia.org/wiki/Imperial_Japanese_Army"}, {"title": "October incident", "link": "https://wikipedia.org/wiki/October_incident"}]}, {"year": "1940", "text": "The first edition of the <PERSON> novel For Whom the Bell Tolls is published.", "html": "1940 - The first edition of the <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> novel <i><a href=\"https://wikipedia.org/wiki/For_Whom_the_Bell_Tolls\" title=\"For Whom the Bell Tolls\">For Whom the Bell Tolls</a></i> is published.", "no_year_html": "The first edition of the <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> novel <i><a href=\"https://wikipedia.org/wiki/For_Whom_the_Bell_Tolls\" title=\"For Whom the Bell Tolls\">For Whom the Bell Tolls</a></i> is published.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}, {"title": "For Whom the Bell Tolls", "link": "https://wikipedia.org/wiki/For_Whom_the_Bell_Tolls"}]}, {"year": "1941", "text": "World War II: The Kragujevac massacre against Serbian men and boys takes place.", "html": "1941 - <a href=\"https://wikipedia.org/wiki/World_War_II\" title=\"World War II\">World War II</a>: The <a href=\"https://wikipedia.org/wiki/Kragujevac_massacre\" title=\"Kragujevac massacre\">Kragujevac massacre</a> against Serbian men and boys takes place.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/World_War_II\" title=\"World War II\">World War II</a>: The <a href=\"https://wikipedia.org/wiki/Kragujevac_massacre\" title=\"Kragujevac massacre\">Kragujevac massacre</a> against Serbian men and boys takes place.", "links": [{"title": "World War II", "link": "https://wikipedia.org/wiki/World_War_II"}, {"title": "Kragujevac massacre", "link": "https://wikipedia.org/wiki/Kragujevac_massacre"}]}, {"year": "1943", "text": "World War II: The Provisional Government of Free India is formally established in Japanese-occupied Singapore.", "html": "1943 - World War II: The <a href=\"https://wikipedia.org/wiki/Azad_Hind\" title=\"Azad Hind\">Provisional Government of Free India</a> is formally established in <a href=\"https://wikipedia.org/wiki/Japanese_occupation_of_Singapore\" title=\"Japanese occupation of Singapore\">Japanese-occupied Singapore</a>.", "no_year_html": "World War II: The <a href=\"https://wikipedia.org/wiki/Azad_Hind\" title=\"Azad Hind\">Provisional Government of Free India</a> is formally established in <a href=\"https://wikipedia.org/wiki/Japanese_occupation_of_Singapore\" title=\"Japanese occupation of Singapore\">Japanese-occupied Singapore</a>.", "links": [{"title": "<PERSON>zad Hind", "link": "https://wikipedia.org/wiki/Azad_Hind"}, {"title": "Japanese occupation of Singapore", "link": "https://wikipedia.org/wiki/Japanese_occupation_of_Singapore"}]}, {"year": "1944", "text": "World War II: The first kamikaze attack damages HMAS Australia as the Battle of Leyte Gulf begins.", "html": "1944 - World War II: The first kamikaze attack damages <a href=\"https://wikipedia.org/wiki/HMAS_Australia_(D84)\" title=\"HMAS Australia (D84)\">HMAS <i>Australia</i></a> as the <a href=\"https://wikipedia.org/wiki/Battle_of_Leyte_Gulf\" title=\"Battle of Leyte Gulf\">Battle of Leyte Gulf</a> begins.", "no_year_html": "World War II: The first kamikaze attack damages <a href=\"https://wikipedia.org/wiki/HMAS_Australia_(D84)\" title=\"HMAS Australia (D84)\">HMAS <i>Australia</i></a> as the <a href=\"https://wikipedia.org/wiki/Battle_of_Leyte_Gulf\" title=\"Battle of Leyte Gulf\">Battle of Leyte Gulf</a> begins.", "links": [{"title": "HMAS Australia (D84)", "link": "https://wikipedia.org/wiki/HMAS_Australia_(D84)"}, {"title": "Battle of Leyte Gulf", "link": "https://wikipedia.org/wiki/Battle_of_Leyte_Gulf"}]}, {"year": "1944", "text": "World War II: The Nemmersdorf massacre against German civilians takes place.", "html": "1944 - World War II: The <a href=\"https://wikipedia.org/wiki/Nemmersdorf_massacre\" title=\"Nemmersdorf massacre\">Nemmersdorf massacre</a> against German civilians takes place.", "no_year_html": "World War II: The <a href=\"https://wikipedia.org/wiki/Nemmersdorf_massacre\" title=\"Nemmersdorf massacre\">Nemmersdorf massacre</a> against German civilians takes place.", "links": [{"title": "Nemmersdorf massacre", "link": "https://wikipedia.org/wiki/Nemmersdorf_massacre"}]}, {"year": "1944", "text": "World War II: The city of Aachen falls to American forces after three weeks of fighting, the first German city to fall to the Allies.", "html": "1944 - World War II: The city of <a href=\"https://wikipedia.org/wiki/Battle_of_Aachen\" title=\"Battle of Aachen\">Aachen falls</a> to American forces after three weeks of fighting, the first German city to fall to the Allies.", "no_year_html": "World War II: The city of <a href=\"https://wikipedia.org/wiki/Battle_of_Aachen\" title=\"Battle of Aachen\">Aachen falls</a> to American forces after three weeks of fighting, the first German city to fall to the Allies.", "links": [{"title": "Battle of Aachen", "link": "https://wikipedia.org/wiki/Battle_of_Aachen"}]}, {"year": "1945", "text": "In the 1945 French legislative election French women vote for the first time.", "html": "1945 - In the <a href=\"https://wikipedia.org/wiki/1945_French_legislative_election\" title=\"1945 French legislative election\">1945 French legislative election</a> French women vote for the first time.", "no_year_html": "In the <a href=\"https://wikipedia.org/wiki/1945_French_legislative_election\" title=\"1945 French legislative election\">1945 French legislative election</a> French women vote for the first time.", "links": [{"title": "1945 French legislative election", "link": "https://wikipedia.org/wiki/1945_French_legislative_election"}]}, {"year": "1950", "text": "Korean War: Heavy fighting begins between British and Australian forces and North Koreans during the Battle of Yongju.", "html": "1950 - Korean War: Heavy fighting begins between British and Australian forces and North Koreans during the <a href=\"https://wikipedia.org/wiki/Battle_of_Yongju\" class=\"mw-redirect\" title=\"Battle of Yongju\">Battle of Yongju</a>.", "no_year_html": "Korean War: Heavy fighting begins between British and Australian forces and North Koreans during the <a href=\"https://wikipedia.org/wiki/Battle_of_Yongju\" class=\"mw-redirect\" title=\"Battle of Yongju\">Battle of Yongju</a>.", "links": [{"title": "Battle of Yongju", "link": "https://wikipedia.org/wiki/Battle_of_Yongju"}]}, {"year": "1956", "text": "The Mau Mau Uprising in Kenya is defeated.", "html": "1956 - The <a href=\"https://wikipedia.org/wiki/Mau_Mau_rebellion\" title=\"Mau Mau rebellion\">Mau Mau Uprising</a> in Kenya is defeated.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Mau_Mau_rebellion\" title=\"Mau Mau rebellion\">Mau Mau Uprising</a> in Kenya is defeated.", "links": [{"title": "Mau Mau rebellion", "link": "https://wikipedia.org/wiki/Mau_<PERSON>u_rebellion"}]}, {"year": "1959", "text": "In New York City, the Solomon R. Guggenheim Museum opens to the public.", "html": "1959 - In New York City, the <a href=\"https://wikipedia.org/wiki/Solomon_R._Guggenheim_Museum\" title=\"Solomon R. Guggenheim Museum\">Solomon R. Guggenheim Museum</a> opens to the public.", "no_year_html": "In New York City, the <a href=\"https://wikipedia.org/wiki/Solomon_R._Guggenheim_Museum\" title=\"Solomon R. Guggenheim Museum\">Solomon R. Guggenheim Museum</a> opens to the public.", "links": [{"title": "Solomon R. Guggenheim Museum", "link": "https://wikipedia.org/wiki/Solomon_R._Guggenheim_Museum"}]}, {"year": "1959", "text": "President <PERSON> approves the transfer of all US Army space-related activities to NASA, including most of the Army Ballistic Missile Agency.", "html": "1959 - President <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> approves the transfer of all <a href=\"https://wikipedia.org/wiki/United_States_Army\" title=\"United States Army\">US Army</a> space-related activities to <a href=\"https://wikipedia.org/wiki/NASA\" title=\"NASA\">NASA</a>, including most of the <a href=\"https://wikipedia.org/wiki/Army_Ballistic_Missile_Agency\" title=\"Army Ballistic Missile Agency\">Army Ballistic Missile Agency</a>.", "no_year_html": "President <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> approves the transfer of all <a href=\"https://wikipedia.org/wiki/United_States_Army\" title=\"United States Army\">US Army</a> space-related activities to <a href=\"https://wikipedia.org/wiki/NASA\" title=\"NASA\">NASA</a>, including most of the <a href=\"https://wikipedia.org/wiki/Army_Ballistic_Missile_Agency\" title=\"Army Ballistic Missile Agency\">Army Ballistic Missile Agency</a>.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "United States Army", "link": "https://wikipedia.org/wiki/United_States_Army"}, {"title": "NASA", "link": "https://wikipedia.org/wiki/NASA"}, {"title": "Army Ballistic Missile Agency", "link": "https://wikipedia.org/wiki/Army_Ballistic_Missile_Agency"}]}, {"year": "1965", "text": "Comet Ikeya<PERSON><PERSON><PERSON> approaches perihelion, passing 450,000 kilometers (279,617 miles) from the sun.", "html": "1965 - <a href=\"https://wikipedia.org/wiki/Comet_Ikeya%E2%80%93Seki\" title=\"Comet Ikeya-Seki\">Comet Ikeya-Se<PERSON></a> approaches perihelion, passing 450,000 kilometers (279,617 miles) from the sun.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Comet_Ikeya%E2%80%93Seki\" title=\"Comet Ikeya-Seki\">Comet Ikeya-Seki</a> approaches perihelion, passing 450,000 kilometers (279,617 miles) from the sun.", "links": [{"title": "Comet Ikeya-<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Comet_Ikeya%E2%80%93Seki"}]}, {"year": "1966", "text": "A colliery spoil tip slips onto houses and a school in the village of Aberfan in Wales, killing 144 people, 116 of whom were schoolchildren.", "html": "1966 - A colliery spoil tip <a href=\"https://wikipedia.org/wiki/Aberfan_disaster\" title=\"Aberfan disaster\">slips onto houses and a school in the village of Aberfan</a> in Wales, killing 144 people, 116 of whom were schoolchildren.", "no_year_html": "A colliery spoil tip <a href=\"https://wikipedia.org/wiki/Aberfan_disaster\" title=\"Aberfan disaster\">slips onto houses and a school in the village of Aberfan</a> in Wales, killing 144 people, 116 of whom were schoolchildren.", "links": [{"title": "Aberfan disaster", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_disaster"}]}, {"year": "1967", "text": "The National Mobilization Committee to End the War in Vietnam organizes a march of fifty thousand people from the Lincoln Memorial to the Pentagon.", "html": "1967 - The <a href=\"https://wikipedia.org/wiki/National_Mobilization_Committee_to_End_the_War_in_Vietnam\" title=\"National Mobilization Committee to End the War in Vietnam\">National Mobilization Committee to End the War in Vietnam</a> organizes a march of fifty thousand people from the Lincoln Memorial to the Pentagon.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/National_Mobilization_Committee_to_End_the_War_in_Vietnam\" title=\"National Mobilization Committee to End the War in Vietnam\">National Mobilization Committee to End the War in Vietnam</a> organizes a march of fifty thousand people from the Lincoln Memorial to the Pentagon.", "links": [{"title": "National Mobilization Committee to End the War in Vietnam", "link": "https://wikipedia.org/wiki/National_Mobilization_Committee_to_End_the_War_in_Vietnam"}]}, {"year": "1969", "text": "The 1969 Somali coup d'état establishes a Marxist-Leninist administration.", "html": "1969 - The <a href=\"https://wikipedia.org/wiki/1969_Somali_coup_d%27%C3%A9tat\" title=\"1969 Somali coup d'état\">1969 Somali coup d'état</a> establishes a Marxist-Leninist administration.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/1969_Somali_coup_d%27%C3%A9tat\" title=\"1969 Somali coup d'état\">1969 Somali coup d'état</a> establishes a Marxist-Leninist administration.", "links": [{"title": "1969 Somali coup d'état", "link": "https://wikipedia.org/wiki/1969_Somali_coup_d%27%C3%A9tat"}]}, {"year": "1971", "text": "A gas explosion kills 22 people at a shopping centre near Glasgow, Scotland.", "html": "1971 - A <a href=\"https://wikipedia.org/wiki/Clarkston_explosion\" title=\"Clarkston explosion\">gas explosion</a> kills 22 people at a shopping centre near Glasgow, Scotland.", "no_year_html": "A <a href=\"https://wikipedia.org/wiki/Clarkston_explosion\" title=\"Clarkston explosion\">gas explosion</a> kills 22 people at a shopping centre near Glasgow, Scotland.", "links": [{"title": "Clarkston explosion", "link": "https://wikipedia.org/wiki/Clarkston_explosion"}]}, {"year": "1973", "text": "<PERSON> of the Los Angeles Rams becomes the first player in NFL history to score two safeties in the same game.", "html": "1973 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> of the Los Angeles Rams becomes the first player in NFL history to score two safeties in the same game.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> of the Los Angeles Rams becomes the first player in NFL history to score two safeties in the same game.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1978", "text": "Australian civilian pilot <PERSON> vanishes over the Bass Strait south of Melbourne, after reporting contact with an unidentified aircraft.", "html": "1978 - Australian civilian pilot <a href=\"https://wikipedia.org/wiki/Disappearance_of_<PERSON>_<PERSON>\" title=\"Disappearance of <PERSON>\"><PERSON> vanishes</a> over the <a href=\"https://wikipedia.org/wiki/Bass_Strait\" title=\"Bass Strait\">Bass Strait</a> south of <a href=\"https://wikipedia.org/wiki/Melbourne\" title=\"Melbourne\">Melbourne</a>, after reporting contact with an unidentified aircraft.", "no_year_html": "Australian civilian pilot <a href=\"https://wikipedia.org/wiki/Disappearance_of_<PERSON>_<PERSON>\" title=\"Disappearance of <PERSON>\"><PERSON> vanishes</a> over the <a href=\"https://wikipedia.org/wiki/Bass_Strait\" title=\"Bass Strait\">Bass Strait</a> south of <a href=\"https://wikipedia.org/wiki/Melbourne\" title=\"Melbourne\">Melbourne</a>, after reporting contact with an unidentified aircraft.", "links": [{"title": "Disappearance of <PERSON>", "link": "https://wikipedia.org/wiki/Disappearance_of_<PERSON>_<PERSON>"}, {"title": "Bass Strait", "link": "https://wikipedia.org/wiki/Bass_Strait"}, {"title": "Melbourne", "link": "https://wikipedia.org/wiki/Melbourne"}]}, {"year": "1979", "text": "<PERSON><PERSON> resigns from the Israeli government because of strong disagreements with Prime Minister <PERSON><PERSON><PERSON> over policy towards the Arabs.", "html": "1979 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a> resigns from the Israeli government because of strong disagreements with Prime Minister <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a> over policy towards the Arabs.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a> resigns from the Israeli government because of strong disagreements with Prime Minister <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a> over policy towards the Arabs.", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}, {"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>gin"}]}, {"year": "1981", "text": "<PERSON> becomes Prime Minister of Greece, ending an almost 50-year-long system of power dominated by conservative forces.", "html": "1981 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> becomes Prime Minister of Greece, ending an almost 50-year-long system of power dominated by conservative forces.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> becomes Prime Minister of Greece, ending an almost 50-year-long system of power dominated by conservative forces.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1983", "text": "The metre is defined as the distance light travels in a vacuum in 1/299,792,458 of a second.", "html": "1983 - The <a href=\"https://wikipedia.org/wiki/Metre\" title=\"Metre\">metre</a> is defined as the distance light travels in a vacuum in 1/299,792,458 of a second.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Metre\" title=\"Metre\">metre</a> is defined as the distance light travels in a vacuum in 1/299,792,458 of a second.", "links": [{"title": "Metre", "link": "https://wikipedia.org/wiki/Metre"}]}, {"year": "1984", "text": "<PERSON><PERSON> claims his third and final Formula One Drivers' Championship Title by half a point ahead of McLaren team-mate <PERSON> at the Portuguese Grand Prix.", "html": "1984 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>\" title=\"<PERSON><PERSON>da\"><PERSON><PERSON></a> claims his third and final <a href=\"https://wikipedia.org/wiki/Formula_One\" title=\"Formula One\">Formula One</a> <a href=\"https://wikipedia.org/wiki/List_of_Formula_One_World_Drivers%27_Champions\" title=\"List of Formula One World Drivers' Champions\">Drivers' Championship Title</a> by half a point ahead of <a href=\"https://wikipedia.org/wiki/McLaren\" title=\"McLaren\">McLaren</a> team-mate <a href=\"https://wikipedia.org/wiki/Alain_Prost\" title=\"Alain Prost\"><PERSON></a> at the <a href=\"https://wikipedia.org/wiki/1984_Portuguese_Grand_Prix\" title=\"1984 Portuguese Grand Prix\">Portuguese Grand Prix</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>\" title=\"<PERSON><PERSON>da\"><PERSON><PERSON></a> claims his third and final <a href=\"https://wikipedia.org/wiki/Formula_One\" title=\"Formula One\">Formula One</a> <a href=\"https://wikipedia.org/wiki/List_of_Formula_One_World_Drivers%27_Champions\" title=\"List of Formula One World Drivers' Champions\">Drivers' Championship Title</a> by half a point ahead of <a href=\"https://wikipedia.org/wiki/McLaren\" title=\"McLaren\">McLaren</a> team-mate <a href=\"https://wikipedia.org/wiki/Alain_Prost\" title=\"Alain Prost\"><PERSON></a> at the <a href=\"https://wikipedia.org/wiki/1984_Portuguese_Grand_Prix\" title=\"1984 Portuguese Grand Prix\">Portuguese Grand Prix</a>.", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>i_Lauda"}, {"title": "Formula One", "link": "https://wikipedia.org/wiki/Formula_One"}, {"title": "List of Formula One World Drivers' Champions", "link": "https://wikipedia.org/wiki/List_of_Formula_One_World_Drivers%27_Champions"}, {"title": "McLaren", "link": "https://wikipedia.org/wiki/McLaren"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "1984 Portuguese Grand Prix", "link": "https://wikipedia.org/wiki/1984_Portuguese_Grand_Prix"}]}, {"year": "1986", "text": "In Lebanon, pro-Iran kidnappers claim to have abducted American writer <PERSON> (he is released in August 1991).", "html": "1986 - In Lebanon, pro-Iran kidnappers claim to have <a href=\"https://wikipedia.org/wiki/Lebanon_hostage_crisis\" title=\"Lebanon hostage crisis\">abducted</a> American writer <PERSON> (he is released in August 1991).", "no_year_html": "In Lebanon, pro-Iran kidnappers claim to have <a href=\"https://wikipedia.org/wiki/Lebanon_hostage_crisis\" title=\"Lebanon hostage crisis\">abducted</a> American writer <PERSON> (he is released in August 1991).", "links": [{"title": "Lebanon hostage crisis", "link": "https://wikipedia.org/wiki/Lebanon_hostage_crisis"}]}, {"year": "1987", "text": "The Jaffna hospital massacre is carried out by Indian peacekeeping forces in Sri Lanka, killing 70 Tamil patients, doctors and nurses.", "html": "1987 - The <a href=\"https://wikipedia.org/wiki/Jaffna_hospital_massacre\" title=\"Jaffna hospital massacre\">Jaffna hospital massacre</a> is carried out by Indian peacekeeping forces in Sri Lanka, killing 70 Tamil patients, doctors and nurses.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Jaffna_hospital_massacre\" title=\"Jaffna hospital massacre\">Jaffna hospital massacre</a> is carried out by Indian peacekeeping forces in Sri Lanka, killing 70 Tamil patients, doctors and nurses.", "links": [{"title": "Jaffna hospital massacre", "link": "https://wikipedia.org/wiki/Jaffna_hospital_massacre"}]}, {"year": "1989", "text": "In Honduras, 131 people are killed when a Boeing 727 crashes on approach to Toncontín International Airport near the nation's capital Tegucigalpa.", "html": "1989 - In <a href=\"https://wikipedia.org/wiki/Honduras\" title=\"Honduras\">Honduras</a>, 131 people are killed when a <a href=\"https://wikipedia.org/wiki/Boeing_727\" title=\"Boeing 727\">Boeing 727</a> <a href=\"https://wikipedia.org/wiki/TAN-SAHSA_Flight_414\" title=\"TAN-SAHSA Flight 414\">crashes</a> on approach to <a href=\"https://wikipedia.org/wiki/Toncont%C3%ADn_International_Airport\" title=\"Toncontín International Airport\">Toncontín International Airport</a> near the nation's capital <a href=\"https://wikipedia.org/wiki/Tegucigalpa\" title=\"Tegucigalpa\">Tegucigalpa</a>.", "no_year_html": "In <a href=\"https://wikipedia.org/wiki/Honduras\" title=\"Honduras\">Honduras</a>, 131 people are killed when a <a href=\"https://wikipedia.org/wiki/Boeing_727\" title=\"Boeing 727\">Boeing 727</a> <a href=\"https://wikipedia.org/wiki/TAN-SAHSA_Flight_414\" title=\"TAN-SAHSA Flight 414\">crashes</a> on approach to <a href=\"https://wikipedia.org/wiki/Toncont%C3%ADn_International_Airport\" title=\"Toncontín International Airport\">Toncontín International Airport</a> near the nation's capital <a href=\"https://wikipedia.org/wiki/Tegucigalpa\" title=\"Tegucigalpa\">Tegucigalpa</a>.", "links": [{"title": "Honduras", "link": "https://wikipedia.org/wiki/Honduras"}, {"title": "Boeing 727", "link": "https://wikipedia.org/wiki/Boeing_727"}, {"title": "TAN-SAHSA Flight 414", "link": "https://wikipedia.org/wiki/TAN-SAHSA_Flight_414"}, {"title": "Toncontín International Airport", "link": "https://wikipedia.org/wiki/Toncont%C3%ADn_International_Airport"}, {"title": "Tegucigalpa", "link": "https://wikipedia.org/wiki/Tegucigalpa"}]}, {"year": "1994", "text": "North Korea and the United States sign an Agreed Framework that requires North Korea to stop its nuclear weapons program and agree to inspections.", "html": "1994 - North Korea and the United States sign an <a href=\"https://wikipedia.org/wiki/Agreed_Framework\" title=\"Agreed Framework\">Agreed Framework</a> that requires North Korea to stop its nuclear weapons program and agree to inspections.", "no_year_html": "North Korea and the United States sign an <a href=\"https://wikipedia.org/wiki/Agreed_Framework\" title=\"Agreed Framework\">Agreed Framework</a> that requires North Korea to stop its nuclear weapons program and agree to inspections.", "links": [{"title": "Agreed Framework", "link": "https://wikipedia.org/wiki/Agreed_Framework"}]}, {"year": "1994", "text": "In Seoul, South Korea, 32 people are killed when a span of the Seongsu Bridge collapses.", "html": "1994 - In Seoul, South Korea, 32 people are killed when a span of the <a href=\"https://wikipedia.org/wiki/Seongsu_Bridge_disaster\" title=\"Seongsu Bridge disaster\">Seongsu Bridge collapses</a>.", "no_year_html": "In Seoul, South Korea, 32 people are killed when a span of the <a href=\"https://wikipedia.org/wiki/Seongsu_Bridge_disaster\" title=\"Seongsu Bridge disaster\">Seongsu Bridge collapses</a>.", "links": [{"title": "Seongsu Bridge disaster", "link": "https://wikipedia.org/wiki/Seongsu_Bridge_disaster"}]}, {"year": "2005", "text": "Images of the dwarf planet <PERSON><PERSON> are taken and subsequently used in documenting its discovery.", "html": "2005 - Images of the dwarf planet <a href=\"https://wikipedia.org/wiki/136199_<PERSON><PERSON>\" class=\"mw-redirect\" title=\"136199 <PERSON>ris\"><PERSON><PERSON></a> are taken and subsequently used in documenting its discovery.", "no_year_html": "Images of the dwarf planet <a href=\"https://wikipedia.org/wiki/136199_<PERSON><PERSON>\" class=\"mw-redirect\" title=\"136199 <PERSON>ris\"><PERSON><PERSON></a> are taken and subsequently used in documenting its discovery.", "links": [{"title": "136199 <PERSON><PERSON>", "link": "https://wikipedia.org/wiki/136199_<PERSON><PERSON>"}]}, {"year": "2011", "text": "Iraq War: President <PERSON> announces that the withdrawal of United States troops from Iraq will be complete by the end of the year.", "html": "2011 - <a href=\"https://wikipedia.org/wiki/Iraq_War\" title=\"Iraq War\">Iraq War</a>: President <a href=\"https://wikipedia.org/wiki/Barack_Obama\" title=\"<PERSON> Obama\"><PERSON></a> announces that the <a href=\"https://wikipedia.org/wiki/Withdrawal_of_United_States_troops_from_Iraq_(2007%E2%80%932011)\" title=\"Withdrawal of United States troops from Iraq (2007-2011)\">withdrawal of United States troops from Iraq</a> will be complete by the end of the year.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Iraq_War\" title=\"Iraq War\">Iraq War</a>: President <a href=\"https://wikipedia.org/wiki/Barack_Obama\" title=\"<PERSON> Obama\"><PERSON></a> announces that the <a href=\"https://wikipedia.org/wiki/Withdrawal_of_United_States_troops_from_Iraq_(2007%E2%80%932011)\" title=\"Withdrawal of United States troops from Iraq (2007-2011)\">withdrawal of United States troops from Iraq</a> will be complete by the end of the year.", "links": [{"title": "Iraq War", "link": "https://wikipedia.org/wiki/Iraq_War"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Barack<PERSON>Obama"}, {"title": "<PERSON><PERSON><PERSON> of United States troops from Iraq (2007-2011)", "link": "https://wikipedia.org/wiki/Withdrawal_of_United_States_troops_from_Iraq_(2007%E2%80%932011)"}]}, {"year": "2019", "text": "Thirty people are killed in a fiery bus crash in western Democratic Republic of the Congo.", "html": "2019 - Thirty people are killed in a fiery bus crash in western <a href=\"https://wikipedia.org/wiki/Democratic_Republic_of_the_Congo\" title=\"Democratic Republic of the Congo\">Democratic Republic of the Congo</a>.", "no_year_html": "Thirty people are killed in a fiery bus crash in western <a href=\"https://wikipedia.org/wiki/Democratic_Republic_of_the_Congo\" title=\"Democratic Republic of the Congo\">Democratic Republic of the Congo</a>.", "links": [{"title": "Democratic Republic of the Congo", "link": "https://wikipedia.org/wiki/Democratic_Republic_of_the_Congo"}]}, {"year": "2019", "text": "In Canada, the 2019 Canadian federal election ends, resulting in incumbent Prime Minister <PERSON> remaining in office, albeit with the Liberal Party in a minority government.", "html": "2019 - In <a href=\"https://wikipedia.org/wiki/Canada\" title=\"Canada\">Canada</a>, the <a href=\"https://wikipedia.org/wiki/2019_Canadian_federal_election\" title=\"2019 Canadian federal election\">2019 Canadian federal election</a> ends, resulting in incumbent Prime Minister <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> remaining in office, albeit with the <a href=\"https://wikipedia.org/wiki/Liberal_Party_of_Canada\" title=\"Liberal Party of Canada\">Liberal Party</a> in a <a href=\"https://wikipedia.org/wiki/Minority_government\" title=\"Minority government\">minority government</a>.", "no_year_html": "In <a href=\"https://wikipedia.org/wiki/Canada\" title=\"Canada\">Canada</a>, the <a href=\"https://wikipedia.org/wiki/2019_Canadian_federal_election\" title=\"2019 Canadian federal election\">2019 Canadian federal election</a> ends, resulting in incumbent Prime Minister <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>\" title=\"<PERSON>\"><PERSON></a> remaining in office, albeit with the <a href=\"https://wikipedia.org/wiki/Liberal_Party_of_Canada\" title=\"Liberal Party of Canada\">Liberal Party</a> in a <a href=\"https://wikipedia.org/wiki/Minority_government\" title=\"Minority government\">minority government</a>.", "links": [{"title": "Canada", "link": "https://wikipedia.org/wiki/Canada"}, {"title": "2019 Canadian federal election", "link": "https://wikipedia.org/wiki/2019_Canadian_federal_election"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Liberal Party of Canada", "link": "https://wikipedia.org/wiki/Liberal_Party_of_Canada"}, {"title": "Minority government", "link": "https://wikipedia.org/wiki/Minority_government"}]}, {"year": "2021", "text": "A shooting occurs on the set of the film Rust, in which actor <PERSON> discharged a prop weapon which had been loaded, killing the director of photography, <PERSON><PERSON>, and injuring director <PERSON>.", "html": "2021 - A <a href=\"https://wikipedia.org/wiki/Rust_shooting_incident\" title=\"Rust shooting incident\">shooting</a> occurs on the set of the film <i><a href=\"https://wikipedia.org/wiki/Rust_(2024_film)\" title=\"Rust (2024 film)\">Rust</a></i>, in which actor <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> discharged a prop weapon which had been loaded, killing the director of photography, <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, and injuring director <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>.", "no_year_html": "A <a href=\"https://wikipedia.org/wiki/Rust_shooting_incident\" title=\"Rust shooting incident\">shooting</a> occurs on the set of the film <i><a href=\"https://wikipedia.org/wiki/Rust_(2024_film)\" title=\"Rust (2024 film)\">Rust</a></i>, in which actor <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> discharged a prop weapon which had been loaded, killing the director of photography, <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, and injuring director <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>.", "links": [{"title": "Rust shooting incident", "link": "https://wikipedia.org/wiki/Rust_shooting_incident"}, {"title": "Rust (2024 film)", "link": "https://wikipedia.org/wiki/Rust_(2024_film)"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}], "Births": [{"year": "1328", "text": "Hong<PERSON> Emperor of China (d. 1398)", "html": "1328 - <a href=\"https://wikipedia.org/wiki/Hongwu_Emperor\" title=\"Hongwu Emperor\">Hongwu Emperor</a> of China (d. 1398)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Hongwu_Emperor\" title=\"Hongwu Emperor\">Hongwu Emperor</a> of China (d. 1398)", "links": [{"title": "Hongwu Emperor", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_Emperor"}]}, {"year": "1409", "text": "<PERSON>, Italian condottiero (d. 1473)", "html": "1409 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian condottiero (d. 1473)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian condottiero (d. 1473)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1449", "text": "<PERSON>, 1st Duke of Clarence, Irish-English son of <PERSON><PERSON>, Duchess of York (d. 1478)", "html": "1449 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_1st_Duke_<PERSON>_Clarence\" class=\"mw-redirect\" title=\"<PERSON>, 1st Duke of Clarence\"><PERSON>, 1st Duke of Clarence</a>, Irish-English son of <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>,_Duchess_of_York\" title=\"<PERSON><PERSON>, Duchess of York\"><PERSON><PERSON>, Duchess of York</a> (d. 1478)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>,_1st_Duke_<PERSON>_Clarence\" class=\"mw-redirect\" title=\"<PERSON>, 1st Duke of Clarence\"><PERSON>, 1st Duke of Clarence</a>, Irish-English son of <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>,_Duchess_of_York\" title=\"<PERSON><PERSON>, Duchess of York\"><PERSON><PERSON>, Duchess of York</a> (d. 1478)", "links": [{"title": "<PERSON>, 1st Duke of Clarence", "link": "https://wikipedia.org/wiki/<PERSON>_Plantagenet,_1st_Duke_<PERSON>_Clarence"}, {"title": "<PERSON><PERSON>, Duchess of York", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>,_Duchess_of_York"}]}, {"year": "1527", "text": "<PERSON>, <PERSON> of Guise (d. 1578)", "html": "1527 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_<PERSON>_of_Guise\" class=\"mw-redirect\" title=\"<PERSON>, Cardinal of Guise\"><PERSON>, Cardinal of Guise</a> (d. 1578)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_<PERSON>_of_Guise\" class=\"mw-redirect\" title=\"<PERSON>, Cardinal of Guise\"><PERSON>, Cardinal of Guise</a> (d. 1578)", "links": [{"title": "<PERSON>, Cardinal of Guise", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_<PERSON>_<PERSON>_Guise"}]}, {"year": "1536", "text": "<PERSON>, Prince of Anhalt (d. 1586)", "html": "1536 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Prince_of_Anhalt\" title=\"<PERSON>, Prince of Anhalt\"><PERSON>, Prince of Anhalt</a> (d. 1586)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Prince_of_Anhalt\" title=\"<PERSON>, Prince of Anhalt\"><PERSON>, Prince of Anhalt</a> (d. 1586)", "links": [{"title": "<PERSON>, Prince of Anhalt", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Prince_of_Anhalt"}]}, {"year": "1581", "text": "<PERSON><PERSON><PERSON>, Italian painter (d. 1641)", "html": "1581 - <a href=\"https://wikipedia.org/wiki/Dome<PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Italian painter (d. 1641)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Dome<PERSON><PERSON>\" title=\"Dome<PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Italian painter (d. 1641)", "links": [{"title": "Domenichino", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>"}]}, {"year": "1650", "text": "<PERSON>, French admiral (d. 1702)", "html": "1650 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French admiral (d. 1702)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French admiral (d. 1702)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1658", "text": "<PERSON>, French nobleman (d. 1722)", "html": "1658 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>liers\"><PERSON></a>, French nobleman (d. 1722)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>liers\"><PERSON></a>, French nobleman (d. 1722)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1675", "text": "Emperor <PERSON><PERSON><PERSON><PERSON> of Japan (d. 1710)", "html": "1675 - <a href=\"https://wikipedia.org/wiki/Emperor_<PERSON><PERSON><PERSON><PERSON>\" title=\"Emperor <PERSON><PERSON><PERSON><PERSON>\">Emperor <PERSON><PERSON><PERSON><PERSON></a> of Japan (d. 1710)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Emperor_<PERSON><PERSON><PERSON><PERSON>\" title=\"Emperor <PERSON><PERSON><PERSON><PERSON>\">Emperor <PERSON><PERSON><PERSON><PERSON></a> of Japan (d. 1710)", "links": [{"title": "Emperor <PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Emperor_<PERSON><PERSON><PERSON>"}]}, {"year": "1687", "text": "<PERSON><PERSON>, Swiss mathematician and theorist (d. 1759)", "html": "1687 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Swiss mathematician and theorist (d. 1759)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Swiss mathematician and theorist (d. 1759)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1712", "text": "<PERSON>, Scottish economist and author (d. 1780)", "html": "1712 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(economist)\" title=\"<PERSON> (economist)\"><PERSON></a>, Scottish economist and author (d. 1780)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(economist)\" title=\"<PERSON> (economist)\"><PERSON></a>, Scottish economist and author (d. 1780)", "links": [{"title": "<PERSON> (economist)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(economist)"}]}, {"year": "1725", "text": "<PERSON>, Austrian field marshal (d. 1801)", "html": "1725 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Austrian field marshal (d. 1801)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Austrian field marshal (d. 1801)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1757", "text": "<PERSON>, French general (d. 1816)", "html": "1757 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, French general (d. 1816)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, French general (d. 1816)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1762", "text": "<PERSON>, Dutch general, lawyer, and politician, 36th Governor-General of the Dutch East Indies (d. 1818)", "html": "1762 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch general, lawyer, and politician, 36th <a href=\"https://wikipedia.org/wiki/Governor-General_of_the_Dutch_East_Indies\" title=\"Governor-General of the Dutch East Indies\">Governor-General of the Dutch East Indies</a> (d. 1818)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch general, lawyer, and politician, 36th <a href=\"https://wikipedia.org/wiki/Governor-General_of_the_Dutch_East_Indies\" title=\"Governor-General of the Dutch East Indies\">Governor-General of the Dutch East Indies</a> (d. 1818)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Governor-General of the Dutch East Indies", "link": "https://wikipedia.org/wiki/Governor-General_of_the_Dutch_East_Indies"}]}, {"year": "1772", "text": "<PERSON>, English poet, philosopher, and critic (d. 1834)", "html": "1772 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English poet, philosopher, and critic (d. 1834)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English poet, philosopher, and critic (d. 1834)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1775", "text": "<PERSON>, Italian priest, composer, and critic (d. 1844)", "html": "1775 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian priest, composer, and critic (d. 1844)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian priest, composer, and critic (d. 1844)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1790", "text": "<PERSON><PERSON><PERSON>, French poet and politician, French Head of State (d. 1869)", "html": "1790 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, French poet and politician, <a href=\"https://wikipedia.org/wiki/French_Head_of_State\" class=\"mw-redirect\" title=\"French Head of State\">French Head of State</a> (d. 1869)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, French poet and politician, <a href=\"https://wikipedia.org/wiki/French_Head_of_State\" class=\"mw-redirect\" title=\"French Head of State\">French Head of State</a> (d. 1869)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>"}, {"title": "French Head of State", "link": "https://wikipedia.org/wiki/French_Head_of_State"}]}, {"year": "1809", "text": "<PERSON>, American Jesuit (d. 1885)", "html": "1809 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(Jesuit)\" title=\"<PERSON> (Jesuit)\"><PERSON></a>, American Jesuit (d. 1885)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>(Jesuit)\" title=\"<PERSON> (Jesuit)\"><PERSON></a>, American Jesuit (d. 1885)", "links": [{"title": "<PERSON> (Jesuit)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(Jesuit)"}]}, {"year": "1811", "text": "<PERSON><PERSON><PERSON>, Italian operatic baritone (d. 1863)", "html": "1811 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Italian operatic baritone (d. 1863)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Italian operatic baritone (d. 1863)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1821", "text": "<PERSON>, English tenor and actor (d. 1900)", "html": "1821 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English tenor and actor (d. 1900)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English tenor and actor (d. 1900)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1833", "text": "<PERSON>, Swedish chemist and engineer, invented dynamite and founded the Nobel Prize (d. 1896)", "html": "1833 - <a href=\"https://wikipedia.org/wiki/Alfred_Nobel\" title=\"Alfred Nobel\">Alfred Nobel</a>, Swedish chemist and engineer, invented <a href=\"https://wikipedia.org/wiki/Dynamite\" title=\"Dynamite\">dynamite</a> and founded the <a href=\"https://wikipedia.org/wiki/Nobel_Prize\" title=\"Nobel Prize\">Nobel Prize</a> (d. 1896)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Alfred_Nobel\" title=\"Alfred Nobel\">Alfred Nobel</a>, Swedish chemist and engineer, invented <a href=\"https://wikipedia.org/wiki/Dynamite\" title=\"Dynamite\">dynamite</a> and founded the <a href=\"https://wikipedia.org/wiki/Nobel_Prize\" title=\"Nobel Prize\">Nobel Prize</a> (d. 1896)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Dynamite", "link": "https://wikipedia.org/wiki/Dynamite"}, {"title": "Nobel Prize", "link": "https://wikipedia.org/wiki/Nobel_Prize"}]}, {"year": "1845", "text": "<PERSON>, American poet and journalist (d. 1912)", "html": "1845 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American poet and journalist (d. 1912)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American poet and journalist (d. 1912)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1847", "text": "<PERSON>, Italian poet and playwright (d. 1906)", "html": "1847 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian poet and playwright (d. 1906)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian poet and playwright (d. 1906)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Giuseppe_<PERSON>"}]}, {"year": "1851", "text": "<PERSON>, English cricketer and footballer (d. 1898)", "html": "1851 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English cricketer and footballer (d. 1898)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English cricketer and footballer (d. 1898)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1868", "text": "<PERSON>, British Army officer (d. 1951)", "html": "1868 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, British Army officer (d. 1951)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, British Army officer (d. 1951)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1874", "text": "<PERSON>, Chinese businessman, community leader, communist and philanthropist (d. 1961)", "html": "1874 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Chinese businessman, community leader, communist and philanthropist (d. 1961)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Chinese businessman, community leader, communist and philanthropist (d. 1961)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1877", "text": "<PERSON>, Canadian-American physician and microbiologist (d. 1955)", "html": "1877 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian-American physician and microbiologist (d. 1955)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian-American physician and microbiologist (d. 1955)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1884", "text": "<PERSON>, German singer and actress (d. 1957)", "html": "1884 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German singer and actress (d. 1957)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German singer and actress (d. 1957)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1886", "text": "<PERSON>, American soldier and pilot (d. 1911)", "html": "1886 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American soldier and pilot (d. 1911)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American soldier and pilot (d. 1911)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1887", "text": "<PERSON>, Indian lawyer and politician, 1st Chief Minister of Bihar (d. 1961)", "html": "1887 - <a href=\"https://wikipedia.org/wiki/<PERSON>(politician)\" class=\"mw-redirect\" title=\"<PERSON> (politician)\"><PERSON></a>, Indian lawyer and politician, 1st <a href=\"https://wikipedia.org/wiki/Chief_Minister_of_Bihar\" class=\"mw-redirect\" title=\"Chief Minister of Bihar\">Chief Minister of Bihar</a> (d. 1961)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>(politician)\" class=\"mw-redirect\" title=\"<PERSON> (politician)\"><PERSON></a>, Indian lawyer and politician, 1st <a href=\"https://wikipedia.org/wiki/Chief_Minister_of_Bihar\" class=\"mw-redirect\" title=\"Chief Minister of Bihar\">Chief Minister of Bihar</a> (d. 1961)", "links": [{"title": "<PERSON> (politician)", "link": "https://wikipedia.org/wiki/<PERSON>_(politician)"}, {"title": "Chief Minister of Bihar", "link": "https://wikipedia.org/wiki/Chief_Minister_of_Bihar"}]}, {"year": "1894", "text": "<PERSON><PERSON>, Japanese author and critic (d. 1965)", "html": "1894 - <a href=\"https://wikipedia.org/wiki/Edogawa_Ranpo\" title=\"Edogawa Ranpo\"><PERSON><PERSON></a>, Japanese author and critic (d. 1965)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Edogawa_Ranpo\" title=\"Edogawa Ranpo\"><PERSON><PERSON></a>, Japanese author and critic (d. 1965)", "links": [{"title": "<PERSON><PERSON> Ranpo", "link": "https://wikipedia.org/wiki/Edogawa_Ranpo"}]}, {"year": "1895", "text": "<PERSON><PERSON><PERSON>, Finnish javelin thrower and decathlete (d. 1983)", "html": "1895 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Finnish javelin thrower and decathlete (d. 1983)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Finnish javelin thrower and decathlete (d. 1983)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1895", "text": "<PERSON>, American actress (d. 1958)", "html": "1895 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress (d. 1958)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress (d. 1958)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1896", "text": "<PERSON>-<PERSON><PERSON><PERSON><PERSON>, Russian-Canadian poet and screenwriter (d. 1985)", "html": "1896 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>-<PERSON>\" title=\"<PERSON>\"><PERSON>-<PERSON></a>, Russian-Canadian poet and screenwriter (d. 1985)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>-<PERSON>\" title=\"<PERSON>-<PERSON>\"><PERSON>-<PERSON></a>, Russian-Canadian poet and screenwriter (d. 1985)", "links": [{"title": "<PERSON>-<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>-<PERSON>"}]}, {"year": "1898", "text": "<PERSON>, Estonian wrestler and actor (d. 1960)", "html": "1898 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%BCtsep\" title=\"<PERSON>\"><PERSON></a>, Estonian wrestler and actor (d. 1960)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%<PERSON>ts<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Estonian wrestler and actor (d. 1960)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Eduard_P%C3%BCtsep"}]}, {"year": "1900", "text": "<PERSON><PERSON>, French Olympic fencer (d. 1973)", "html": "1900 - <a href=\"https://wikipedia.org/wiki/Andr%C3%A<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, French Olympic fencer (d. 1973)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Andr%C3%A<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, French Olympic fencer (d. 1973)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Andr%C3%A9e_<PERSON><PERSON>"}]}, {"year": "1902", "text": "<PERSON>, American footballer (d. 1943)", "html": "1902 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American footballer (d. 1943)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American footballer (d. 1943)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1907", "text": "<PERSON><PERSON>, Greek painter and poet (d. 1985)", "html": "1907 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Greek painter and poet (d. 1985)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Greek painter and poet (d. 1985)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1908", "text": "<PERSON><PERSON><PERSON>, Cypriot-English sociologist and academic (d. 1988)", "html": "1908 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Cypriot-English sociologist and academic (d. 1988)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Cypriot-English sociologist and academic (d. 1988)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1911", "text": "<PERSON>, American illustrator and animator (d. 1978)", "html": "1911 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American illustrator and animator (d. 1978)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American illustrator and animator (d. 1978)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1912", "text": "<PERSON>, American saxophonist and educator (d. 1972)", "html": "1912 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American saxophonist and educator (d. 1972)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American saxophonist and educator (d. 1972)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1912", "text": "<PERSON>, Argentinian race car driver (d. 1990)", "html": "1912 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A1n\" title=\"<PERSON>\"><PERSON></a>, Argentinian race car driver (d. 1990)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A1n\" title=\"<PERSON>\"><PERSON></a>, Argentinian race car driver (d. 1990)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Alfredo_Pi%C3%A1n"}]}, {"year": "1912", "text": "<PERSON>, Hungarian-English conductor and director (d. 1997)", "html": "1912 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Hungarian-English conductor and director (d. 1997)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Hungarian-English conductor and director (d. 1997)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1914", "text": "<PERSON>, American mathematician and author (d. 2010)", "html": "1914 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American mathematician and author (d. 2010)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American mathematician and author (d. 2010)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1915", "text": "<PERSON>, American country music record producer (d. 1998)", "html": "1915 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American country music record producer (d. 1998)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American country music record producer (d. 1998)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1917", "text": "<PERSON><PERSON>, American trumpet player, composer, and bandleader (d. 1993)", "html": "1917 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American trumpet player, composer, and bandleader (d. 1993)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American trumpet player, composer, and bandleader (d. 1993)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1918", "text": "<PERSON>, American sociologist and author (d. 2006)", "html": "1918 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American sociologist and author (d. 2006)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American sociologist and author (d. 2006)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>farb"}]}, {"year": "1918", "text": "<PERSON><PERSON>, South African anti-apartheid activist (d. 2011)", "html": "1918 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, South African anti-apartheid activist (d. 2011)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, South African anti-apartheid activist (d. 2011)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1919", "text": "<PERSON>, English-Canadian sergeant and pilot (d. 2013)", "html": "1919 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English-Canadian sergeant and pilot (d. 2013)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English-Canadian sergeant and pilot (d. 2013)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1921", "text": "<PERSON>, English composer (d. 2006)", "html": "1921 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English composer (d. 2006)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English composer (d. 2006)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1921", "text": "<PERSON>, Australian-English actor (d. 2013)", "html": "1921 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian-English actor (d. 2013)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian-English actor (d. 2013)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1921", "text": "<PERSON>, Canadian actor (d. 1999)", "html": "1921 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian actor (d. 1999)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian actor (d. 1999)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1921", "text": "<PERSON>, American fiddler and composer (d. 2013)", "html": "1921 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American fiddler and composer (d. 2013)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American fiddler and composer (d. 2013)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1921", "text": "<PERSON>-<PERSON><PERSON><PERSON><PERSON>, Dutch astronomer and academic (d. 2015)", "html": "1921 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>-<PERSON>d\" title=\"<PERSON>\"><PERSON></a>, Dutch astronomer and academic (d. 2015)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>-<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch astronomer and academic (d. 2015)", "links": [{"title": "<PERSON>-<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>-G<PERSON>veld"}]}, {"year": "1922", "text": "<PERSON><PERSON><PERSON>, French businesswoman and philanthropist (d. 2017)", "html": "1922 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, French businesswoman and philanthropist (d. 2017)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, French businesswoman and philanthropist (d. 2017)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1923", "text": "<PERSON>, Iranian director, screenwriter, and author (d. 2001)", "html": "1923 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Iranian director, screenwriter, and author (d. 2001)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Iranian director, screenwriter, and author (d. 2001)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1924", "text": "<PERSON>, American actress (d. 2024)", "html": "1924 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress (d. 2024)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress (d. 2024)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1924", "text": "<PERSON>, American actress and singer (d. 2015)", "html": "1924 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress and singer (d. 2015)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress and singer (d. 2015)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1925", "text": "<PERSON>, Cuban-American singer (d. 2003)", "html": "1925 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Cuban-American singer (d. 2003)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Cuban-American singer (d. 2003)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1925", "text": "<PERSON>, Romanian soprano and educator (d. 2023)", "html": "1925 - <a href=\"https://wikipedia.org/wiki/Virginia_Zeani\" title=\"Virginia Zeani\"><PERSON></a>, Romanian soprano and educator (d. 2023)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Virginia_Zeani\" title=\"Virginia Zeani\"><PERSON></a>, Romanian soprano and educator (d. 2023)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Virginia_Zeani"}]}, {"year": "1926", "text": "<PERSON>, American golfer (d. 2009)", "html": "1926 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American golfer (d. 2009)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American golfer (d. 2009)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Bob_<PERSON>"}]}, {"year": "1926", "text": "<PERSON>, English actor (d. 1984)", "html": "1926 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actor (d. 1984)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actor (d. 1984)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1927", "text": "<PERSON>, Austrian mountaineer (d. 2018)", "html": "1927 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Austrian mountaineer (d. 2018)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Austrian mountaineer (d. 2018)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1927", "text": "<PERSON>, American director and photographer (d. 2009)", "html": "1927 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American director and photographer (d. 2009)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American director and photographer (d. 2009)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1928", "text": "<PERSON><PERSON>, American baseball player and coach (d. 2020)", "html": "1928 - <a href=\"https://wikipedia.org/wiki/Whitey_Ford\" title=\"Whitey Ford\"><PERSON><PERSON></a>, American baseball player and coach (d. 2020)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Whitey_Ford\" title=\"Whitey Ford\"><PERSON><PERSON></a>, American baseball player and coach (d. 2020)", "links": [{"title": "Whitey <PERSON>", "link": "https://wikipedia.org/wiki/Whitey_Ford"}]}, {"year": "1928", "text": "<PERSON><PERSON><PERSON><PERSON>, Brazilian zoologist (d. 2015)", "html": "1928 - <a href=\"https://wikipedia.org/wiki/Eud%C3%B3<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Brazilian zoologist (d. 2015)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Eud%C3%B3<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Brazilian zoologist (d. 2015)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Eud%C3%B3<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1928", "text": "<PERSON><PERSON>, American basketball player and coach (d. 2013)", "html": "1928 - <a href=\"https://wikipedia.org/wiki/Vern_<PERSON>\" title=\"<PERSON>ern Mikkelsen\"><PERSON><PERSON></a>, American basketball player and coach (d. 2013)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>ern_<PERSON>\" title=\"<PERSON>ern Mikkelsen\"><PERSON><PERSON></a>, American basketball player and coach (d. 2013)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1929", "text": "<PERSON>, French radio and television host (d. 2018)", "html": "1929 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French radio and television host (d. 2018)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French radio and television host (d. 2018)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1929", "text": "<PERSON>, Austrian footballer (d. 1994)", "html": "1929 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Austrian footballer (d. 1994)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Austrian footballer (d. 1994)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1929", "text": "<PERSON>, American author and critic (d. 2018)", "html": "1929 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author and critic (d. 2018)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author and critic (d. 2018)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1929", "text": "<PERSON>, wrongfully convicted African-American inmate; second youngest person in the U.S. to be executed (d. 1944)", "html": "1929 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>.\" class=\"mw-redirect\" title=\"<PERSON>.\"><PERSON>.</a>, wrongfully convicted African-American inmate; second youngest person in the U.S. to be executed (d. 1944)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>.\" class=\"mw-redirect\" title=\"<PERSON>.\"><PERSON>.</a>, wrongfully convicted African-American inmate; second youngest person in the U.S. to be executed (d. 1944)", "links": [{"title": "<PERSON>.", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>."}]}, {"year": "1930", "text": "<PERSON>, Russian engineer and politician, 19th Prime Minister of Russia (d. 2023)", "html": "1930 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian engineer and politician, 19th <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Russia\" title=\"Prime Minister of Russia\">Prime Minister of Russia</a> (d. 2023)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian engineer and politician, 19th <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Russia\" title=\"Prime Minister of Russia\">Prime Minister of Russia</a> (d. 2023)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Prime Minister of Russia", "link": "https://wikipedia.org/wiki/Prime_Minister_of_Russia"}]}, {"year": "1931", "text": "<PERSON><PERSON><PERSON>, Indian actor and director (d. 2011)", "html": "1931 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Indian actor and director (d. 2011)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Indian actor and director (d. 2011)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1931", "text": "<PERSON>, English cricketer and manager  (d. 2022)", "html": "1931 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>.\" class=\"mw-redirect\" title=\"<PERSON>.\"><PERSON>.</a>, English cricketer and manager (d. 2022)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>.\" class=\"mw-redirect\" title=\"<PERSON>.\"><PERSON>.</a>, English cricketer and manager (d. 2022)", "links": [{"title": "<PERSON> Jr.", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>."}]}, {"year": "1932", "text": "<PERSON><PERSON><PERSON>, Hungarian footballer and manager (d. 2013)", "html": "1932 - <a href=\"https://wikipedia.org/wiki/P%C3%A1l_Csernai\" title=\"<PERSON><PERSON>l Csernai\"><PERSON><PERSON><PERSON></a>, Hungarian footballer and manager (d. 2013)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/P%C3%A1l_Csernai\" title=\"P<PERSON>l Csernai\"><PERSON><PERSON><PERSON></a>, Hungarian footballer and manager (d. 2013)", "links": [{"title": "P<PERSON>l <PERSON>", "link": "https://wikipedia.org/wiki/P%C3%A1l_Csernai"}]}, {"year": "1933", "text": "<PERSON>, English author, poet, playwright and activist", "html": "1933 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English author, poet, playwright and activist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English author, poet, playwright and activist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1933", "text": "<PERSON>, Spanish footballer and manager (d. 2022)", "html": "1933 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"Francisco Gento\"><PERSON></a>, Spanish footballer and manager (d. 2022)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON> Gento\"><PERSON></a>, Spanish footballer and manager (d. 2022)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Francisco_<PERSON>"}]}, {"year": "1935", "text": "<PERSON>, Irish harp player, pianist, and songwriter (d. 2002)", "html": "1935 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(musician)\" title=\"<PERSON> (musician)\"><PERSON></a>, Irish harp player, pianist, and songwriter (d. 2002)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(musician)\" title=\"<PERSON> (musician)\"><PERSON></a>, Irish harp player, pianist, and songwriter (d. 2002)", "links": [{"title": "<PERSON> (musician)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(musician)"}]}, {"year": "1935", "text": "<PERSON>, American country music singer-songwriter and guitarist (d. 1978)", "html": "1935 - <a href=\"https://wikipedia.org/wiki/Mel_Street\" title=\"Mel Street\">Mel Street</a>, American country music singer-songwriter and guitarist (d. 1978)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Mel_Street\" title=\"Mel Street\"><PERSON> Street</a>, American country music singer-songwriter and guitarist (d. 1978)", "links": [{"title": "Mel Street", "link": "https://wikipedia.org/wiki/Mel_Street"}]}, {"year": "1937", "text": "<PERSON>, Russian spiritual leader and scholar (d. 2012)", "html": "1937 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian spiritual leader and scholar (d. 2012)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian spiritual leader and scholar (d. 2012)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON><PERSON>_<PERSON>-<PERSON>"}]}, {"year": "1937", "text": "<PERSON>, Australian historian and academic (d. 2012)", "html": "1937 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian historian and academic (d. 2012)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian historian and academic (d. 2012)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1938", "text": "<PERSON>, Canadian ice hockey player (d. 2001)", "html": "1938 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(ice_hockey)\" title=\"<PERSON> (ice hockey)\"><PERSON></a>, Canadian ice hockey player (d. 2001)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(ice_hockey)\" title=\"<PERSON> (ice hockey)\"><PERSON></a>, Canadian ice hockey player (d. 2001)", "links": [{"title": "<PERSON> (ice hockey)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(ice_hockey)"}]}, {"year": "1940", "text": "<PERSON>, English cricketer and sportscaster", "html": "1940 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English cricketer and sportscaster", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English cricketer and sportscaster", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1940", "text": "<PERSON>, American journalist and author", "html": "1940 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(journalist)\" title=\"<PERSON> (journalist)\"><PERSON></a>, American journalist and author", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(journalist)\" title=\"<PERSON> (journalist)\"><PERSON></a>, American journalist and author", "links": [{"title": "<PERSON> (journalist)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(journalist)"}]}, {"year": "1940", "text": "<PERSON><PERSON><PERSON>, American actress", "html": "1940 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American actress", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON><PERSON><PERSON><PERSON>"}]}, {"year": "1940", "text": "<PERSON>, South African-English keyboard player and producer", "html": "1940 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(musician)\" title=\"<PERSON> (musician)\"><PERSON></a>, South African-English keyboard player and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(musician)\" title=\"<PERSON> (musician)\"><PERSON></a>, South African-English keyboard player and producer", "links": [{"title": "<PERSON> (musician)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>(musician)"}]}, {"year": "1940", "text": "<PERSON><PERSON>, Faroese educator and politician, Prime Minister of the Faroe Islands (d. 2001)", "html": "1940 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Faroese educator and politician, <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_the_Faroe_Islands\" class=\"mw-redirect\" title=\"Prime Minister of the Faroe Islands\">Prime Minister of the Faroe Islands</a> (d. 2001)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Faroese educator and politician, <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_the_Faroe_Islands\" class=\"mw-redirect\" title=\"Prime Minister of the Faroe Islands\">Prime Minister of the Faroe Islands</a> (d. 2001)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}, {"title": "Prime Minister of the Faroe Islands", "link": "https://wikipedia.org/wiki/Prime_Minister_of_the_Faroe_Islands"}]}, {"year": "1941", "text": "<PERSON>, American guitarist, songwriter, producer, and actor", "html": "1941 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American guitarist, songwriter, producer, and actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American guitarist, songwriter, producer, and actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1942", "text": "<PERSON><PERSON>, American singer-songwriter and guitarist", "html": "1942 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American singer-songwriter and guitarist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American singer-songwriter and guitarist", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1942", "text": "<PERSON>, Australian race car driver and politician", "html": "1942 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian race car driver and politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian race car driver and politician", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1942", "text": "<PERSON>, American ice hockey player, coach, and manager", "html": "1942 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American ice hockey player, coach, and manager", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American ice hockey player, coach, and manager", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1942", "text": "<PERSON>, American judge and television host", "html": "1942 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American judge and television host", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American judge and television host", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1942", "text": "<PERSON>, American economist and statistician, Nobel Prize laureate", "html": "1942 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American economist and statistician, <a href=\"https://wikipedia.org/wiki/Nobel_Memorial_Prize_in_Economic_Sciences\" title=\"Nobel Memorial Prize in Economic Sciences\">Nobel Prize</a> laureate", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American economist and statistician, <a href=\"https://wikipedia.org/wiki/Nobel_Memorial_Prize_in_Economic_Sciences\" title=\"Nobel Memorial Prize in Economic Sciences\">Nobel Prize</a> laureate", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "Nobel Memorial Prize in Economic Sciences", "link": "https://wikipedia.org/wiki/Nobel_Memorial_Prize_in_Economic_Sciences"}]}, {"year": "1942", "text": "<PERSON>, <PERSON> of Kirkwhelpington, English police officer and academic", "html": "1942 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Baron_<PERSON>_of_Kirkwhelpington\" title=\"<PERSON>, <PERSON> of Kirkwhelpington\"><PERSON>, Baron <PERSON> of Kirkwhelpington</a>, English police officer and academic", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>,_Baron_<PERSON>_of_Kirkwhelpington\" title=\"<PERSON>, <PERSON> of Kirkwhelpington\"><PERSON>, Baron <PERSON> of Kirkwhelpington</a>, English police officer and academic", "links": [{"title": "<PERSON>, <PERSON> of Kirkwhelpington", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_<PERSON>_<PERSON>_of_Kirkwhelpington"}]}, {"year": "1943", "text": "<PERSON><PERSON><PERSON>, Pakistani historian and author", "html": "1943 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Pakistani historian and author", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Pakistani historian and author", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1943", "text": "<PERSON>, American singer-songwriter, guitarist, and composer", "html": "1943 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(musician)\" title=\"<PERSON> (musician)\"><PERSON></a>, American singer-songwriter, guitarist, and composer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(musician)\" title=\"<PERSON> (musician)\"><PERSON></a>, American singer-songwriter, guitarist, and composer", "links": [{"title": "<PERSON> (musician)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(musician)"}]}, {"year": "1944", "text": "<PERSON>-<PERSON>, English model and actress (d. 2014)", "html": "1944 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English model and actress (d. 2014)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English model and actress (d. 2014)", "links": [{"title": "<PERSON>-<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1944", "text": "<PERSON>, English lawyer and judge", "html": "1944 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English lawyer and judge", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English lawyer and judge", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1945", "text": "<PERSON>, American actor", "html": "1945 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> McGill\"><PERSON></a>, American actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Everett McGill\"><PERSON></a>, American actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1945", "text": "<PERSON><PERSON>, Russian filmmaker", "html": "1945 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Russian filmmaker", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Russian filmmaker", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>"}]}, {"year": "1945", "text": "<PERSON>, English journalist", "html": "1945 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(journalist)\" title=\"<PERSON> (journalist)\"><PERSON></a>, English journalist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>(journalist)\" title=\"<PERSON> (journalist)\"><PERSON></a>, English journalist", "links": [{"title": "<PERSON> (journalist)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>(journalist)"}]}, {"year": "1946", "text": "<PERSON>, English philosopher and academic", "html": "1946 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English philosopher and academic", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English philosopher and academic", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1946", "text": "<PERSON>, American football player and sportscaster", "html": "1946 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(American_football)\" class=\"mw-redirect\" title=\"<PERSON> (American football)\"><PERSON></a>, American football player and sportscaster", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(American_football)\" class=\"mw-redirect\" title=\"<PERSON> (American football)\"><PERSON></a>, American football player and sportscaster", "links": [{"title": "<PERSON> (American football)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(American_football)"}]}, {"year": "1946", "text": "<PERSON><PERSON>, American singer-songwriter (d. 2009)", "html": "1946 - <a href=\"https://wikipedia.org/wiki/Lux_Interior\" title=\"Lux Interior\"><PERSON><PERSON></a>, American singer-songwriter (d. 2009)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Lux_Interior\" title=\"Lux Interior\"><PERSON><PERSON></a>, American singer-songwriter (d. 2009)", "links": [{"title": "Lux Interior", "link": "https://wikipedia.org/wiki/Lux_Interior"}]}, {"year": "1946", "text": "<PERSON>, American singer-songwriter and trumpet player", "html": "1946 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and trumpet player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and trumpet player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1948", "text": "<PERSON><PERSON><PERSON>, American historian and academic", "html": "1948 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> <PERSON>\"><PERSON><PERSON> <PERSON></a>, American historian and academic", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON> <PERSON><PERSON></a>, American historian and academic", "links": [{"title": "<PERSON><PERSON> J<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "1948", "text": "<PERSON>, American archbishop", "html": "1948 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, American archbishop", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, American archbishop", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1949", "text": "<PERSON>, Canadian ice hockey player (d. 1971)", "html": "1949 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A8re\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player (d. 1971)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A8re\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player (d. 1971)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>%C3%A8re"}]}, {"year": "1949", "text": "<PERSON>, Canadian ice hockey player and coach", "html": "1949 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player and coach", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1949", "text": "<PERSON>, Israeli captain and politician, 9th Prime Minister of Israel", "html": "1949 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Israeli captain and politician, 9th <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Israel\" title=\"Prime Minister of Israel\">Prime Minister of Israel</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Israeli captain and politician, 9th <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Israel\" title=\"Prime Minister of Israel\">Prime Minister of Israel</a>", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}, {"title": "Prime Minister of Israel", "link": "https://wikipedia.org/wiki/Prime_Minister_of_Israel"}]}, {"year": "1950", "text": "<PERSON>, American physicist and astronaut (d. 1986)", "html": "1950 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American physicist and astronaut (d. 1986)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American physicist and astronaut (d. 1986)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1950", "text": "<PERSON><PERSON>, Belizean musician and cultural conservationist (d. 2017)", "html": "1950 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Belizean musician and cultural conservationist (d. 2017)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Belizean musician and cultural conservationist (d. 2017)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1952", "text": "<PERSON>, American actress and author", "html": "1952 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress and author", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress and author", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1952", "text": "<PERSON>, American poet and author (d. 2010)", "html": "1952 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American poet and author (d. 2010)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American poet and author (d. 2010)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1952", "text": "<PERSON>, German-American keyboard player (d. 1990)", "html": "1952 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German-American keyboard player (d. 1990)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German-American keyboard player (d. 1990)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Brent_Mydland"}]}, {"year": "1953", "text": "<PERSON>, American guitarist and songwriter", "html": "1953 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American guitarist and songwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American guitarist and songwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Charlotte_<PERSON>y"}]}, {"year": "1953", "text": "<PERSON>, Scottish singer-songwriter and guitarist", "html": "1953 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish singer-songwriter and guitarist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish singer-songwriter and guitarist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1953", "text": "<PERSON>, American singer-songwriter, pianist, and minister (d. 1982)", "html": "1953 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter, pianist, and minister (d. 1982)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter, pianist, and minister (d. 1982)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1953", "text": "<PERSON>, American bassist, composer, and bandleader", "html": "1953 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(musician)\" class=\"mw-redirect\" title=\"<PERSON> (musician)\"><PERSON></a>, American bassist, composer, and bandleader", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(musician)\" class=\"mw-redirect\" title=\"<PERSON> (musician)\"><PERSON></a>, American bassist, composer, and bandleader", "links": [{"title": "<PERSON> (musician)", "link": "https://wikipedia.org/wiki/<PERSON>_(musician)"}]}, {"year": "1953", "text": "<PERSON>, English journalist and politician, Secretary of State for Northern Ireland", "html": "1953 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English journalist and politician, <a href=\"https://wikipedia.org/wiki/Secretary_of_State_for_Northern_Ireland\" title=\"Secretary of State for Northern Ireland\">Secretary of State for Northern Ireland</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English journalist and politician, <a href=\"https://wikipedia.org/wiki/Secretary_of_State_for_Northern_Ireland\" title=\"Secretary of State for Northern Ireland\">Secretary of State for Northern Ireland</a>", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}, {"title": "Secretary of State for Northern Ireland", "link": "https://wikipedia.org/wiki/Secretary_of_State_for_Northern_Ireland"}]}, {"year": "1954", "text": "<PERSON>, Canadian journalist and politician, 6th Premier of Newfoundland", "html": "1954 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian journalist and politician, 6th <a href=\"https://wikipedia.org/wiki/Premier_of_Newfoundland\" class=\"mw-redirect\" title=\"Premier of Newfoundland\">Premier of Newfoundland</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian journalist and politician, 6th <a href=\"https://wikipedia.org/wiki/Premier_of_Newfoundland\" class=\"mw-redirect\" title=\"Premier of Newfoundland\">Premier of Newfoundland</a>", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Premier of Newfoundland", "link": "https://wikipedia.org/wiki/Premier_of_Newfoundland"}]}, {"year": "1955", "text": "<PERSON>, American businessman", "html": "1955 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American businessman", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American businessman", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1955", "text": "<PERSON>, American film director, producer, and screenwriter", "html": "1955 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American film director, producer, and screenwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American film director, producer, and screenwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1955", "text": "<PERSON>, American pianist and composer", "html": "1955 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American pianist and composer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American pianist and composer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1955", "text": "<PERSON>, American singer-songwriter (d. 1997)", "html": "1955 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter (d. 1997)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter (d. 1997)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1956", "text": "<PERSON>, American actress and screenwriter (d. 2016)", "html": "1956 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress and screenwriter (d. 2016)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress and screenwriter (d. 2016)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1956", "text": "<PERSON>, American pole vaulter", "html": "1956 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American pole vaulter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American pole vaulter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1957", "text": "<PERSON>, English singer-songwriter", "html": "1957 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English singer-songwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English singer-songwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1957", "text": "<PERSON>, Scottish lawn bowler", "html": "1957 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish lawn bowler", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish lawn bowler", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1957", "text": "<PERSON>, German physicist and academic, Nobel Prize laureate", "html": "1957 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German physicist and academic, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Physics\" title=\"Nobel Prize in Physics\">Nobel Prize</a> laureate", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German physicist and academic, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Physics\" title=\"Nobel Prize in Physics\">Nobel Prize</a> laureate", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Nobel Prize in Physics", "link": "https://wikipedia.org/wiki/Nobel_Prize_in_Physics"}]}, {"year": "1957", "text": "<PERSON>, American singer-songwriter, guitarist, and producer", "html": "1957 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter, guitarist, and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter, guitarist, and producer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1958", "text": "<PERSON>, Russian-English physicist and academic, Nobel Prize laureate", "html": "1958 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian-English physicist and academic, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Physics\" title=\"Nobel Prize in Physics\">Nobel Prize</a> laureate", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian-English physicist and academic, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Physics\" title=\"Nobel Prize in Physics\">Nobel Prize</a> laureate", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Andre_<PERSON>"}, {"title": "Nobel Prize in Physics", "link": "https://wikipedia.org/wiki/Nobel_Prize_in_Physics"}]}, {"year": "1959", "text": "<PERSON>, Dominican baseball player", "html": "1959 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(outfielder)\" title=\"<PERSON> (outfielder)\"><PERSON></a>, Dominican baseball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(outfielder)\" title=\"<PERSON> (outfielder)\"><PERSON></a>, Dominican baseball player", "links": [{"title": "<PERSON> (outfielder)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(outfielder)"}]}, {"year": "1959", "text": "<PERSON>, Scottish singer-songwriter and guitarist", "html": "1959 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish singer-songwriter and guitarist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish singer-songwriter and guitarist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1959", "text": "<PERSON>, English director, producer, and screenwriter", "html": "1959 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English director, producer, and screenwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English director, producer, and screenwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1959", "text": "<PERSON>, Welsh-Irish footballer and manager", "html": "1959 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(Irish_footballer)\" title=\"<PERSON> (Irish footballer)\"><PERSON></a>, Welsh-Irish footballer and manager", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(Irish_footballer)\" title=\"<PERSON> (Irish footballer)\"><PERSON></a>, Welsh-Irish footballer and manager", "links": [{"title": "<PERSON> (Irish footballer)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(Irish_footballer)"}]}, {"year": "1959", "text": "<PERSON>, Japanese actor and producer", "html": "1959 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Japanese actor and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Japanese actor and producer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1959", "text": "<PERSON><PERSON>, American actress, director, and writer", "html": "1959 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American actress, director, and writer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American actress, director, and writer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1962", "text": "<PERSON>, Australian rugby player and coach", "html": "1962 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian rugby player and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian rugby player and coach", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1964", "text": "<PERSON>, American singer-songwriter, guitarist, and producer", "html": "1964 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter, guitarist, and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter, guitarist, and producer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1965", "text": "<PERSON>, Spanish footballer and manager", "html": "1965 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>ikoet<PERSON>\" class=\"mw-redirect\" title=\"Ion <PERSON> Go<PERSON>\"><PERSON></a>, Spanish footballer and manager", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>et<PERSON>\" class=\"mw-redirect\" title=\"Ion Andoni Goikoet<PERSON>a\"><PERSON></a>, Spanish footballer and manager", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>a"}]}, {"year": "1965", "text": "<PERSON>, American wrestler", "html": "1965 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American wrestler", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American wrestler", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1965", "text": "<PERSON><PERSON>, Japanese singer-songwriter and guitarist", "html": "1965 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> Imai\"><PERSON><PERSON></a>, Japanese singer-songwriter and guitarist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> I<PERSON>i\"><PERSON><PERSON></a>, Japanese singer-songwriter and guitarist", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>i"}]}, {"year": "1966", "text": "<PERSON>, Welsh golfer", "html": "1966 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Welsh golfer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON> Price\"><PERSON></a>, Welsh golfer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1966", "text": "<PERSON>, Estonian footballer and manager", "html": "1966 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Estonian footballer and manager", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Estonian footballer and manager", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>s"}]}, {"year": "1966", "text": "<PERSON><PERSON>, Norwegian footballer and manager", "html": "1966 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>%C3%B8\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Norwegian footballer and manager", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>%C3%B8\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Norwegian footballer and manager", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Arne_<PERSON>t%C3%B8"}]}, {"year": "1967", "text": "<PERSON><PERSON>, Bulgarian high jumper (d. 1996)", "html": "1967 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Bulgarian high jumper (d. 1996)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Bulgarian high jumper (d. 1996)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1967", "text": "<PERSON>, English footballer and manager", "html": "1967 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English footballer and manager", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English footballer and manager", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Paul_Ince"}]}, {"year": "1967", "text": "<PERSON>, New Zealand javelin thrower and graphic designer", "html": "1967 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, New Zealand javelin thrower and graphic designer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, New Zealand javelin thrower and graphic designer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1968", "text": "<PERSON><PERSON><PERSON>, Greek footballer and manager", "html": "1968 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Greek footballer and manager", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Greek footballer and manager", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1968", "text": "<PERSON><PERSON><PERSON>, German politician", "html": "1968 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, German politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, German politician", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1969", "text": "<PERSON>, Australian rugby league player", "html": "1969 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(rugby_league)\" title=\"<PERSON> (rugby league)\"><PERSON></a>, Australian rugby league player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(rugby_league)\" title=\"<PERSON> (rugby league)\"><PERSON></a>, Australian rugby league player", "links": [{"title": "<PERSON> (rugby league)", "link": "https://wikipedia.org/wiki/<PERSON>_(rugby_league)"}]}, {"year": "1969", "text": "<PERSON>, American football player", "html": "1969 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1970", "text": "<PERSON>, Hong Kong actor and singer", "html": "1970 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Hong Kong actor and singer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Hong Kong actor and singer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1971", "text": "<PERSON>, Scottish author and poet", "html": "1971 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish author and poet", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish author and poet", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1971", "text": "<PERSON>, Australian cricketer", "html": "1971 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian cricketer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian cricketer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1971", "text": "<PERSON>, American singer-songwriter and bass player", "html": "1971 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and bass player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and bass player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1971", "text": "<PERSON>, Irish rugby player and coach", "html": "1971 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%27Shea\" title=\"<PERSON>\"><PERSON></a>, Irish rugby player and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%27Shea\" title=\"<PERSON>\"><PERSON></a>, Irish rugby player and coach", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>%27Shea"}]}, {"year": "1971", "text": "<PERSON>, Scottish footballer and coach", "html": "1971 - <a href=\"https://wikipedia.org/wiki/<PERSON>_(footballer)\" title=\"<PERSON> (footballer)\"><PERSON></a>, Scottish footballer and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>(footballer)\" title=\"<PERSON> (footballer)\"><PERSON></a>, Scottish footballer and coach", "links": [{"title": "<PERSON> (footballer)", "link": "https://wikipedia.org/wiki/<PERSON>_(footballer)"}]}, {"year": "1971", "text": "<PERSON>, Norwegian curler", "html": "1971 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Norwegian curler", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Norwegian curler", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1972", "text": "<PERSON><PERSON><PERSON>, Indian businessman and cricketer", "html": "1972 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Indian businessman and cricketer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Indian businessman and cricketer", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1972", "text": "<PERSON>, American singer-songwriter and guitarist", "html": "1972 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and guitarist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and guitarist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1972", "text": "<PERSON>, American football player (d. 2014)", "html": "1972 - <a href=\"https://wikipedia.org/wiki/<PERSON>_Thomas\" title=\"<PERSON> Thomas\"><PERSON></a>, American football player (d. 2014)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_Thomas\" title=\"<PERSON> Thomas\"><PERSON></a>, American football player (d. 2014)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_Thomas"}]}, {"year": "1972", "text": "<PERSON><PERSON><PERSON>, Ukrainian scholar and academic", "html": "1972 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Ukrainian scholar and academic", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Ukrainian scholar and academic", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON><PERSON><PERSON>"}]}, {"year": "1973", "text": "<PERSON><PERSON>, Russian-American pianist and composer", "html": "1973 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Russian-American pianist and composer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Russian-American pianist and composer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1973", "text": "<PERSON>, American pianist and songwriter", "html": "1973 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American pianist and songwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American pianist and songwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1974", "text": "<PERSON><PERSON><PERSON>, Romanian tenor", "html": "1974 - <a href=\"https://wikipedia.org/wiki/Costel_Busuioc\" title=\"Costel Busuioc\"><PERSON><PERSON><PERSON></a>, Romanian tenor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Costel_Busuioc\" title=\"Costel Busuioc\"><PERSON><PERSON><PERSON></a>, Romanian tenor", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Costel_<PERSON>oc"}]}, {"year": "1975", "text": "<PERSON>, American baseball player", "html": "1975 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Toby Hall\"><PERSON></a>, American baseball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Toby Hall\"><PERSON></a>, American baseball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Toby_<PERSON>"}]}, {"year": "1975", "text": "<PERSON><PERSON>, Portuguese footballer", "html": "1975 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>%C3%A1rio\" class=\"mw-redirect\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Portuguese footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>%C3%A1rio\" class=\"mw-redirect\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Portuguese footballer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Henrique_Hil%C3%A1rio"}]}, {"year": "1976", "text": "<PERSON>, Swedish footballer", "html": "1976 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swedish footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swedish footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1976", "text": "<PERSON>, American actor and singer", "html": "1976 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor and singer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor and singer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1976", "text": "<PERSON><PERSON><PERSON>, Romanian gymnast", "html": "1976 - <a href=\"https://wikipedia.org/wiki/Lavinia_Milo%C8%99ovici\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Romanian gymnast", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Lavinia_Milo%C8%99ovici\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Romanian gymnast", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Lavinia_Milo%C8%99ovici"}]}, {"year": "1976", "text": "<PERSON>, American singer-songwriter and guitarist", "html": "1976 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and guitarist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and guitarist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1976", "text": "<PERSON>, Irish actor", "html": "1976 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(actor)\" title=\"<PERSON> (actor)\"><PERSON></a>, Irish actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>(actor)\" title=\"<PERSON> (actor)\"><PERSON></a>, Irish actor", "links": [{"title": "<PERSON> (actor)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>(actor)"}]}, {"year": "1976", "text": "<PERSON><PERSON><PERSON><PERSON>, Canadian skier", "html": "1976 - <a href=\"https://wikipedia.org/wiki/M%C3%A9<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Canadian skier", "no_year_html": "<a href=\"https://wikipedia.org/wiki/M%C3%A9<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Canadian skier", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/M%C3%A9<PERSON><PERSON>_<PERSON>on"}]}, {"year": "1978", "text": "<PERSON>, American actor", "html": "1978 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1978", "text": "<PERSON>, American football player and sportscaster", "html": "1978 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player and sportscaster", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player and sportscaster", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1978", "text": "<PERSON>, Finnish singer and keyboard player", "html": "1978 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Finnish singer and keyboard player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Finnish singer and keyboard player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1978", "text": "<PERSON>, American actor", "html": "1978 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1979", "text": "<PERSON><PERSON><PERSON>, American baseball player", "html": "1979 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American baseball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American baseball player", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1979", "text": "<PERSON>, American baseball player", "html": "1979 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1980", "text": "<PERSON>, American reality television personality, actress, model, businesswoman and socialite", "html": "1980 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American reality television personality, actress, model, businesswoman and socialite", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American reality television personality, actress, model, businesswoman and socialite", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1980", "text": "<PERSON>, American bass player", "html": "1980 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American bass player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American bass player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1981", "text": "<PERSON>, Argentinian-Italian rugby player", "html": "1981 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, Argentinian-Italian rugby player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, Argentinian-Italian rugby player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1981", "text": "<PERSON>, French racing driver", "html": "1981 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French racing driver", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French racing driver", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1981", "text": "<PERSON><PERSON><PERSON>, Serbian footballer", "html": "1981 - <a href=\"https://wikipedia.org/wiki/Nemanja_Vidi%C4%87\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Serbian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Nemanja_Vidi%C4%87\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Serbian footballer", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Nemanja_Vidi%C4%87"}]}, {"year": "1982", "text": "<PERSON>, American actor", "html": "1982 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Matt Dallas\"><PERSON></a>, American actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Matt_<PERSON>\" title=\"Matt Dallas\"><PERSON></a>, American actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Matt_<PERSON>"}]}, {"year": "1982", "text": "<PERSON>, American baseball player", "html": "1982 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(baseball)\" title=\"<PERSON> (baseball)\"><PERSON></a>, American baseball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(baseball)\" title=\"<PERSON> (baseball)\"><PERSON></a>, American baseball player", "links": [{"title": "<PERSON> (baseball)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(baseball)"}]}, {"year": "1982", "text": "<PERSON>, English footballer", "html": "1982 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1982", "text": "<PERSON>, American comedian, actor, and podcaster", "html": "1982 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American comedian, actor, and podcaster", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American comedian, actor, and podcaster", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Hari_<PERSON>bolu"}]}, {"year": "1982", "text": "<PERSON>, American football player", "html": "1982 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1982", "text": "<PERSON>, Malaysian badminton player", "html": "1982 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Malaysian badminton player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Malaysian badminton player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1982", "text": "<PERSON>, American basketball player", "html": "1982 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(basketball)\" title=\"<PERSON> (basketball)\"><PERSON></a>, American basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(basketball)\" title=\"<PERSON> (basketball)\"><PERSON></a>, American basketball player", "links": [{"title": "<PERSON> (basketball)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(basketball)"}]}, {"year": "1983", "text": "<PERSON>, American baseball player", "html": "1983 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Fi<PERSON>\"><PERSON></a>, American baseball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>en"}]}, {"year": "1983", "text": "<PERSON>, American baseball player", "html": "1983 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1983", "text": "<PERSON>, Canadian swimmer", "html": "1983 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian swimmer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian swimmer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1983", "text": "<PERSON><PERSON><PERSON>, Argentinian footballer", "html": "1983 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Argentinian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Argentinian footballer", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1983", "text": "<PERSON>, Dominican baseball player (d. 2017)", "html": "1983 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dominican baseball player (d. 2017)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dominican baseball player (d. 2017)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1983", "text": "<PERSON>, American model", "html": "1983 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American model", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American model", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1983", "text": "<PERSON>, English-Scottish martial artist", "html": "1983 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English-Scottish martial artist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English-Scottish martial artist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1983", "text": "<PERSON>, Canadian actress", "html": "1983 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian actress", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1983", "text": "<PERSON><PERSON>, Israeli singer", "html": "1983 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>b\"><PERSON><PERSON></a>, Israeli singer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>yeb\"><PERSON><PERSON></a>, Israeli singer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>yeb"}]}, {"year": "1983", "text": "<PERSON><PERSON><PERSON>, American basketball player", "html": "1983 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American basketball player", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1984", "text": "<PERSON>, Russian heptathlete", "html": "1984 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian heptathlete", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian heptathlete", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1984", "text": "<PERSON>, American football player", "html": "1984 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1984", "text": "<PERSON>, American soccer player", "html": "1984 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American soccer player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American soccer player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1984", "text": "<PERSON><PERSON><PERSON>, Canadian speed skater", "html": "1984 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>-<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Canadian speed skater", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>-<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Canadian speed skater", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>-Boucher"}]}, {"year": "1984", "text": "<PERSON>, Venezuelan baseball player", "html": "1984 - <a href=\"https://wikipedia.org/wiki/Jos%C3%A9_Lobat%C3%B3n\" title=\"<PERSON>\"><PERSON></a>, Venezuelan baseball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Jos%C3%A9_Lobat%C3%B3n\" title=\"<PERSON>\"><PERSON></a>, Venezuelan baseball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Jos%C3%A9_Lobat%C3%B3n"}]}, {"year": "1984", "text": "<PERSON>, American football player", "html": "1984 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1984", "text": "<PERSON><PERSON>, English footballer", "html": "1984 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, English footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, English footballer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1985", "text": "<PERSON>, Italian footballer", "html": "1985 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1985", "text": "<PERSON>, Australian rugby league player", "html": "1985 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian rugby league player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian rugby league player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1985", "text": "<PERSON><PERSON>, Belgian-Turkish singer-songwriter and dancer", "html": "1985 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Belgian-Turkish singer-songwriter and dancer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Belgian-Turkish singer-songwriter and dancer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>"}]}, {"year": "1986", "text": "<PERSON><PERSON>, Swiss footballer", "html": "1986 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Swiss footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Swiss footballer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_Abdi"}]}, {"year": "1986", "text": "<PERSON><PERSON><PERSON>, Nigerian footballer", "html": "1986 - <a href=\"https://wikipedia.org/wiki/Chibuzor_Chilaka\" title=\"Chibuzor Chilaka\"><PERSON><PERSON><PERSON></a>, Nigerian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Chibuzor_Chilaka\" title=\"Chibuzor Chilaka\"><PERSON><PERSON><PERSON></a>, Nigerian footballer", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Chibuzor_Chilaka"}]}, {"year": "1986", "text": "<PERSON>, English footballer", "html": "1986 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1987", "text": "<PERSON><PERSON><PERSON>, Norwegian politician", "html": "1987 - <a href=\"https://wikipedia.org/wiki/Ton<PERSON>_<PERSON>\" title=\"Tonje <PERSON>\"><PERSON><PERSON><PERSON></a>, Norwegian politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Ton<PERSON>_<PERSON>\" title=\"Tonje <PERSON>\"><PERSON><PERSON><PERSON></a>, Norwegian politician", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/To<PERSON><PERSON>_<PERSON>na"}]}, {"year": "1987", "text": "<PERSON>, American baseball player", "html": "1987 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1987", "text": "<PERSON><PERSON>, Russian swimmer", "html": "1987 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Russian swimmer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Russian swimmer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>y_<PERSON>"}]}, {"year": "1988", "text": "<PERSON><PERSON>, Danish footballer", "html": "1988 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Danish footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Danish footballer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1988", "text": "<PERSON>, American actor", "html": "1988 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Powell\"><PERSON></a>, American actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1988", "text": "<PERSON>, Austrian cyclist", "html": "1988 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Austrian cyclist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Austrian cyclist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1989", "text": "<PERSON><PERSON>, Norwegian footballer", "html": "1989 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Norwegian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Norwegian footballer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1989", "text": "<PERSON><PERSON>, Nigerian-American basketball player", "html": "1989 - <a href=\"https://wikipedia.org/wiki/Festus_Ezeli\" title=\"Festus Ezeli\"><PERSON><PERSON></a>, Nigerian-American basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Festus_Ezeli\" title=\"Festus Ezeli\"><PERSON><PERSON></a>, Nigerian-American basketball player", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Festus_Ezeli"}]}, {"year": "1989", "text": "<PERSON>, English footballer", "html": "1989 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1989", "text": "<PERSON>, Spanish footballer", "html": "1989 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Spanish footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Spanish footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1989", "text": "<PERSON>, English-Welsh footballer", "html": "1989 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English-Welsh footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English-Welsh footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>s"}]}, {"year": "1990", "text": "Bengali-<PERSON><PERSON><PERSON>, French footballer", "html": "1990 - <a href=\"https://wikipedia.org/wiki/Bengali-Fod%C3%A9_<PERSON><PERSON>\" title=\"Bengali-<PERSON><PERSON><PERSON>\">Bengali-<PERSON><PERSON><PERSON></a>, French footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Bengali-Fod%C3%A9_<PERSON><PERSON>\" title=\"Bengali-<PERSON><PERSON><PERSON>\">Bengali-<PERSON><PERSON><PERSON></a>, French footballer", "links": [{"title": "Bengali-<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Bengali-Fod%C3%A9_<PERSON>ita"}]}, {"year": "1990", "text": "<PERSON><PERSON>, French footballer", "html": "1990 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, French footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, French footballer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1990", "text": "<PERSON>, Spanish basketball player", "html": "1990 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Spanish basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> R<PERSON>\"><PERSON></a>, Spanish basketball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1990", "text": "<PERSON><PERSON><PERSON><PERSON>, Icelandic politician", "html": "1990 - <a href=\"https://wikipedia.org/wiki/Kristj%C3%A1n_%C3%9E%C3%B3r%C3%B0ur_Sn%C3%A6b<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Icelandic politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Kristj%C3%A1n_%C3%9E%C3%B3r%C3%B0ur_Sn%C3%A<PERSON><PERSON><PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Icelandic politician", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Kristj%C3%A1n_%C3%9E%C3%B3r%C3%B0ur_Sn%C3%A6<PERSON><PERSON><PERSON><PERSON>"}]}, {"year": "1991", "text": "<PERSON>, Russian ice hockey player", "html": "1991 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian ice hockey player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian ice hockey player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1991", "text": "<PERSON>, English footballer", "html": "1991 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1991", "text": "<PERSON><PERSON>, Belgian footballer", "html": "1991 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Belgian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Belgian footballer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1991", "text": "<PERSON>, English cricketer", "html": "1991 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English cricketer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English cricketer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1991", "text": "<PERSON><PERSON><PERSON>, English footballer", "html": "1991 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON> Oliver\"><PERSON><PERSON><PERSON></a>, English footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON> Oliver\"><PERSON><PERSON><PERSON></a>, English footballer", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1991", "text": "<PERSON>, English footballer", "html": "1991 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1992", "text": "<PERSON>, Australian actress", "html": "1992 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian actress", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1992", "text": "<PERSON><PERSON>, Italian businessperson and former YouTuber", "html": "1992 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Italian businessperson and former YouTuber", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Italian businessperson and former YouTuber", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1992", "text": "<PERSON><PERSON>, American basketball player", "html": "1992 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American basketball player", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1992", "text": "<PERSON>, German-Australian tennis player", "html": "1992 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German-Australian tennis player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German-Australian tennis player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1993", "text": "<PERSON>, American singer and songwriter", "html": "1993 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer and songwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer and songwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1995", "text": "<PERSON>, Scottish-Australian footballer", "html": "1995 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish-Australian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish-Australian footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1995", "text": "<PERSON><PERSON>, American rapper, singer and songwriter", "html": "1995 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_Cat\" title=\"Doja Cat\"><PERSON><PERSON></a>, American rapper, singer and songwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_Cat\" title=\"Doja Cat\"><PERSON><PERSON></a>, American rapper, singer and songwriter", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1995", "text": "<PERSON><PERSON>, Cameroonian swimmer", "html": "1995 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_Mouafo\" title=\"<PERSON><PERSON> Mo<PERSON>\"><PERSON><PERSON></a>, Cameroonian swimmer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_Mo<PERSON>fo\" title=\"<PERSON><PERSON> G<PERSON> Mo<PERSON>\"><PERSON><PERSON></a>, Cameroonian swimmer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>"}]}], "Deaths": [{"year": "645", "text": "<PERSON><PERSON><PERSON><PERSON>, khan of Xueyantuo", "html": "645 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON>hen<PERSON><PERSON> Khan\"><PERSON><PERSON><PERSON><PERSON></a>, khan of <a href=\"https://wikipedia.org/wiki/Xu<PERSON>ant<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON>hen<PERSON><PERSON> Khan\"><PERSON><PERSON><PERSON><PERSON></a>, khan of <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>ant<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>", "links": [{"title": "<PERSON>henz<PERSON> Khan", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>"}, {"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>eyantuo"}]}, {"year": "1023", "text": "<PERSON><PERSON>, Archbishop of Magdeburg", "html": "1023 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_(archbishop_of_Magdeburg)\" title=\"<PERSON><PERSON> (archbishop of Magdeburg)\"><PERSON><PERSON></a>, Archbishop of Magdeburg", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_(archbishop_of_Magdeburg)\" title=\"<PERSON><PERSON> (archbishop of Magdeburg)\"><PERSON><PERSON></a>, Archbishop of Magdeburg", "links": [{"title": "<PERSON><PERSON> (archbishop of Magdeburg)", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_(archbishop_of_Magdeburg)"}]}, {"year": "1096", "text": "<PERSON>, a leader of the First Crusade", "html": "1096 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON> San<PERSON> A<PERSON>ir\"><PERSON></a>, a leader of the <a href=\"https://wikipedia.org/wiki/First_Crusade\" title=\"First Crusade\">First Crusade</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON> A<PERSON>ir\"><PERSON></a>, a leader of the <a href=\"https://wikipedia.org/wiki/First_Crusade\" title=\"First Crusade\">First Crusade</a>", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Walter_Sans_Avoir"}, {"title": "First Crusade", "link": "https://wikipedia.org/wiki/First_Crusade"}]}, {"year": "1125", "text": "<PERSON><PERSON><PERSON> of Prague, Bohemian priest and historian (b. 1045)", "html": "1125 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_of_Prague\" title=\"<PERSON><PERSON><PERSON> of Prague\"><PERSON><PERSON><PERSON> of Prague</a>, Bohemian priest and historian (b. 1045)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_of_Prague\" title=\"<PERSON><PERSON><PERSON> of Prague\"><PERSON><PERSON><PERSON> of Prague</a>, Bohemian priest and historian (b. 1045)", "links": [{"title": "<PERSON><PERSON><PERSON> of Prague", "link": "https://wikipedia.org/wiki/Cosmas_of_Prague"}]}, {"year": "1204", "text": "<PERSON>, 4th Earl of Leicester, English politician", "html": "1204 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_4th_Earl_of_Leicester\" title=\"<PERSON>, 4th Earl of Leicester\"><PERSON>, 4th Earl of Leicester</a>, English politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>,_4th_Earl_of_Leicester\" title=\"<PERSON>, 4th Earl of Leicester\"><PERSON>, 4th Earl of Leicester</a>, English politician", "links": [{"title": "<PERSON>, 4th Earl of Leicester", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>,_4th_Earl_of_Leicester"}]}, {"year": "1221", "text": "<PERSON><PERSON>, Duchess of Brittany (b. 1201)", "html": "1221 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>,_Duchess_of_Brittany\" title=\"<PERSON><PERSON>, Duchess of Brittany\"><PERSON><PERSON>, Duchess of Brittany</a> (b. 1201)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>,_Duchess_of_Brittany\" title=\"<PERSON><PERSON>, Duchess of Brittany\"><PERSON><PERSON>, Duchess of Brittany</a> (b. 1201)", "links": [{"title": "<PERSON><PERSON>, Duchess of Brittany", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>,_Duchess_of_Brittany"}]}, {"year": "1266", "text": "<PERSON><PERSON><PERSON>, Swedish politician (b. 1210)", "html": "1266 - <a href=\"https://wikipedia.org/wiki/Bir<PERSON>_<PERSON>\" title=\"<PERSON>ir<PERSON>\"><PERSON><PERSON><PERSON></a>, Swedish politician (b. 1210)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>ir<PERSON>_<PERSON>\" title=\"<PERSON>ir<PERSON>\"><PERSON><PERSON><PERSON></a>, Swedish politician (b. 1210)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Birger_<PERSON>l"}]}, {"year": "1314", "text": "<PERSON>, 1st Baron <PERSON>", "html": "1314 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>,_1st_Baron_<PERSON>\" title=\"<PERSON>, 1st Baron <PERSON>\"><PERSON>, 1st Baron <PERSON></a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>,_1st_Baron_<PERSON>\" title=\"<PERSON>, 1st Baron <PERSON>\"><PERSON>, 1st Baron <PERSON></a>", "links": [{"title": "<PERSON>, 1st Baron <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>,_1st_Baron_<PERSON>"}]}, {"year": "1422", "text": "<PERSON> of France (b. 1368)", "html": "1422 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_France\" title=\"Charles VI of France\"><PERSON> of France</a> (b. 1368)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_France\" title=\"Charles VI of France\"><PERSON> of France</a> (b. 1368)", "links": [{"title": "<PERSON> of France", "link": "https://wikipedia.org/wiki/Charles_VI_of_France"}]}, {"year": "1500", "text": "Emperor <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> of Japan (b. 1442)", "html": "1500 - <a href=\"https://wikipedia.org/wiki/Emperor_<PERSON>-Tsuchimikado\" title=\"Emperor Go-Tsuchimikado\">Emperor <PERSON><PERSON>T<PERSON><PERSON><PERSON>ka<PERSON></a> of Japan (b. 1442)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Emperor_<PERSON>-Tsuchimikado\" title=\"Emperor Go-Tsuchimikado\">Emperor <PERSON><PERSON>T<PERSON><PERSON><PERSON>ka<PERSON></a> of Japan (b. 1442)", "links": [{"title": "Emperor <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>-<PERSON><PERSON><PERSON><PERSON><PERSON>"}]}, {"year": "1505", "text": "<PERSON>, German mathematician and educator (b. 1460)", "html": "1505 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German mathematician and educator (b. 1460)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German mathematician and educator (b. 1460)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1556", "text": "<PERSON>, Italian author (b. 1492)", "html": "1556 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian author (b. 1492)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian author (b. 1492)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1558", "text": "<PERSON>, Italian physician and scholar (b. 1484)", "html": "1558 - <a href=\"https://wikipedia.org/wiki/<PERSON>_Caesar_<PERSON>ger\" title=\"Julius Caesar <PERSON>ger\"><PERSON></a>, Italian physician and scholar (b. 1484)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>ger\" title=\"Julius Caesar <PERSON>ger\"><PERSON></a>, Italian physician and scholar (b. 1484)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>ger"}]}, {"year": "1600", "text": "<PERSON><PERSON>, Japanese samurai (b. 1558)", "html": "1600 - <a href=\"https://wikipedia.org/wiki/%C5%8Ctani_Yoshitsugu\" title=\"Ōtani Yoshitsugu\"><PERSON><PERSON>gu</a>, Japanese samurai (b. 1558)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/%C5%8Ctani_Yoshitsugu\" title=\"Ōtani Yoshitsugu\"><PERSON><PERSON></a>, Japanese samurai (b. 1558)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/%C5%8C<PERSON>_<PERSON><PERSON>gu"}]}, {"year": "1623", "text": "<PERSON>, English politician and diplomat, Lieutenant of the Tower of London (b. 1546)", "html": "1623 - <a href=\"https://wikipedia.org/wiki/<PERSON>(English_politician)\" title=\"<PERSON> (English politician)\"><PERSON></a>, English politician and diplomat, <a href=\"https://wikipedia.org/wiki/Lieutenant_of_the_Tower_of_London\" title=\"Lieutenant of the Tower of London\">Lieutenant of the Tower of London</a> (b. 1546)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>(English_politician)\" title=\"<PERSON> (English politician)\"><PERSON></a>, English politician and diplomat, <a href=\"https://wikipedia.org/wiki/Lieutenant_of_the_Tower_of_London\" title=\"Lieutenant of the Tower of London\">Lieutenant of the Tower of London</a> (b. 1546)", "links": [{"title": "<PERSON> (English politician)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>(English_politician)"}, {"title": "Lieutenant of the Tower of London", "link": "https://wikipedia.org/wiki/Lieutenant_of_the_Tower_of_London"}]}, {"year": "1662", "text": "<PERSON>, English composer (b. 1595)", "html": "1662 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English composer (b. 1595)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English composer (b. 1595)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1687", "text": "<PERSON>, English poet and politician (b. 1606)", "html": "1687 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English poet and politician (b. 1606)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English poet and politician (b. 1606)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1765", "text": "<PERSON>, Italian painter and architect (b. 1691)", "html": "1765 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian painter and architect (b. 1691)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian painter and architect (b. 1691)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1775", "text": "<PERSON>, American lawyer and politician, 1st President of the Continental Congress (b. 1721)", "html": "1775 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and politician, 1st <a href=\"https://wikipedia.org/wiki/President_of_the_Continental_Congress\" title=\"President of the Continental Congress\">President of the Continental Congress</a> (b. 1721)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and politician, 1st <a href=\"https://wikipedia.org/wiki/President_of_the_Continental_Congress\" title=\"President of the Continental Congress\">President of the Continental Congress</a> (b. 1721)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "President of the Continental Congress", "link": "https://wikipedia.org/wiki/President_of_the_Continental_Congress"}]}, {"year": "1777", "text": "<PERSON>, English actor and playwright (b. 1720)", "html": "1777 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actor and playwright (b. 1720)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actor and playwright (b. 1720)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1805", "text": "<PERSON>, English captain (b. 1763)", "html": "1805 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(Royal_Navy_officer)\" title=\"<PERSON> (Royal Navy officer)\"><PERSON></a>, English captain (b. 1763)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(Royal_Navy_officer)\" title=\"<PERSON> (Royal Navy officer)\"><PERSON></a>, English captain (b. 1763)", "links": [{"title": "<PERSON> (Royal Navy officer)", "link": "https://wikipedia.org/wiki/<PERSON>_(Royal_Navy_officer)"}]}, {"year": "1805", "text": "<PERSON>, Scottish captain (b. 1764)", "html": "1805 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish captain (b. 1764)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish captain (b. 1764)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1805", "text": "<PERSON><PERSON><PERSON>, 1st Viscount <PERSON>, English admiral (b. 1758)", "html": "1805 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>,_1st_Viscount_<PERSON>\" title=\"<PERSON><PERSON><PERSON>, 1st Viscount <PERSON>\"><PERSON><PERSON><PERSON>, 1st Viscount <PERSON></a>, English admiral (b. 1758)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>,_1st_Viscount_<PERSON>\" title=\"<PERSON><PERSON><PERSON>, 1st Viscount <PERSON>\"><PERSON><PERSON><PERSON>, 1st Viscount <PERSON></a>, English admiral (b. 1758)", "links": [{"title": "<PERSON><PERSON><PERSON>, 1st Viscount <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>rat<PERSON>_<PERSON>,_1st_Viscount_<PERSON>"}]}, {"year": "1821", "text": "<PERSON>, German actress (b. 1752)", "html": "1821 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German actress (b. 1752)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German actress (b. 1752)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1835", "text": "<PERSON><PERSON><PERSON>, Indian poet and composer (b. 1775)", "html": "1835 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Indian poet and composer (b. 1775)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Indian poet and composer (b. 1775)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1861", "text": "<PERSON>, American congressman and colonel (b. 1811)", "html": "1861 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American congressman and colonel (b. 1811)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American congressman and colonel (b. 1811)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1872", "text": "<PERSON>, French physicist, mathematician, and astronomer (b. 1794)", "html": "1872 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French physicist, mathematician, and astronomer (b. 1794)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French physicist, mathematician, and astronomer (b. 1794)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1873", "text": "<PERSON>, Norwegian author, poet, and critic (b. 1807)", "html": "1873 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Norwegian author, poet, and critic (b. 1807)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Norwegian author, poet, and critic (b. 1807)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1896", "text": "<PERSON>, South African-English engineer (b. 1844)", "html": "1896 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, South African-English engineer (b. 1844)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, South African-English engineer (b. 1844)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1903", "text": "<PERSON><PERSON><PERSON>, Japanese sumo wrestler, the 12th <PERSON><PERSON><PERSON><PERSON> (b. 1829)", "html": "1903 - <a href=\"https://wikipedia.org/wiki/Jinmaku_Ky%C5%ABgor%C5%8D\" title=\"<PERSON><PERSON><PERSON>ūgor<PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese sumo wrestler, the 12th <a href=\"https://wikipedia.org/wiki/Ma<PERSON>uchi#Yokozuna\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a> (b. 1829)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>ma<PERSON>_Ky%C5%ABgor%C5%8D\" title=\"<PERSON><PERSON><PERSON> K<PERSON>ūgor<PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese sumo wrestler, the 12th <a href=\"https://wikipedia.org/wiki/Ma<PERSON><PERSON>#Yokozuna\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a> (b. 1829)", "links": [{"title": "Jinmaku <PERSON>", "link": "https://wikipedia.org/wiki/Jinmaku_Ky%C5%ABgor%C5%8D"}, {"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Makuuchi#Yokozuna"}]}, {"year": "1904", "text": "<PERSON>, Swiss explorer and journalist (b. 1877)", "html": "1904 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swiss explorer and journalist (b. 1877)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swiss explorer and journalist (b. 1877)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1907", "text": "<PERSON>, French priest, founded the Missionaries of the Sacred Heart (b. 1824)", "html": "1907 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French priest, founded the <a href=\"https://wikipedia.org/wiki/Missionaries_of_the_Sacred_Heart\" title=\"Missionaries of the Sacred Heart\">Missionaries of the Sacred Heart</a> (b. 1824)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French priest, founded the <a href=\"https://wikipedia.org/wiki/Missionaries_of_the_Sacred_Heart\" title=\"Missionaries of the Sacred Heart\">Missionaries of the Sacred Heart</a> (b. 1824)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Missionaries of the Sacred Heart", "link": "https://wikipedia.org/wiki/Missionaries_of_the_Sacred_Heart"}]}, {"year": "1927", "text": "<PERSON><PERSON>, Serbian author (b. 1876)", "html": "1927 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>%C4%87\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Serbian author (b. 1876)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>%C4%87\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Serbian author (b. 1876)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>av_Stankovi%C4%87"}]}, {"year": "1931", "text": "<PERSON>, Austrian author and playwright (b. 1862)", "html": "1931 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Austrian author and playwright (b. 1862)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Austrian author and playwright (b. 1862)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1938", "text": "<PERSON>, American actress (b. 1905)", "html": "1938 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress (b. 1905)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress (b. 1905)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1939", "text": "<PERSON><PERSON><PERSON>, Dutch civil engineer (b. 1859)", "html": "1939 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Dutch civil engineer (b. <a href=\"https://wikipedia.org/wiki/1859\" title=\"1859\">1859</a>)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Dutch civil engineer (b. <a href=\"https://wikipedia.org/wiki/1859\" title=\"1859\">1859</a>)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}, {"title": "1859", "link": "https://wikipedia.org/wiki/1859"}]}, {"year": "1940", "text": "<PERSON>, American journalist, lawyer, and politician, 18th Governor of West Virginia (b. 1866)", "html": "1940 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American journalist, lawyer, and politician, 18th <a href=\"https://wikipedia.org/wiki/Governor_of_West_Virginia\" class=\"mw-redirect\" title=\"Governor of West Virginia\">Governor of West Virginia</a> (b. 1866)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American journalist, lawyer, and politician, 18th <a href=\"https://wikipedia.org/wiki/Governor_of_West_Virginia\" class=\"mw-redirect\" title=\"Governor of West Virginia\">Governor of West Virginia</a> (b. 1866)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>"}, {"title": "Governor of West Virginia", "link": "https://wikipedia.org/wiki/Governor_of_West_Virginia"}]}, {"year": "1941", "text": "<PERSON>, Australian biologist (b. 1852)", "html": "1941 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian biologist (b. 1852)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian biologist (b. 1852)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1944", "text": "<PERSON><PERSON>, German-French missionary (b. 1877)", "html": "1944 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, German-French missionary (b. 1877)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, German-French missionary (b. 1877)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1952", "text": "<PERSON>, South African geologist and philanthropist (b. 1871)", "html": "1952 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, South African geologist and philanthropist (b. 1871)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, South African geologist and philanthropist (b. 1871)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1963", "text": "<PERSON><PERSON><PERSON>, Polish sergeant (b. 1918)", "html": "1963 - <a href=\"https://wikipedia.org/wiki/J%C3%B3<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Polish sergeant (b. 1918)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/J%C3%B<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Polish sergeant (b. 1918)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/J%C3%B3<PERSON>_<PERSON>"}]}, {"year": "1965", "text": "<PERSON>, American bass player and bandleader (b. 1926)", "html": "1965 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American bass player and bandleader (b. 1926)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American bass player and bandleader (b. 1926)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1969", "text": "<PERSON>, American novelist and poet (b. 1922)", "html": "1969 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American novelist and poet (b. 1922)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American novelist and poet (b. 1922)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1969", "text": "<PERSON><PERSON><PERSON>, Polish mathematician and academic (b. 1882)", "html": "1969 - <a href=\"https://wikipedia.org/wiki/Wac%C5%82aw_Sierpi%C5%84ski\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Polish mathematician and academic (b. 1882)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Wac%C5%82aw_Sierpi%C5%84ski\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Polish mathematician and academic (b. 1882)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Wac%C5%82aw_Sierpi%C5%84ski"}]}, {"year": "1970", "text": "<PERSON>, Chinese educator and diplomat (b. 1896)", "html": "1970 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Chinese educator and diplomat (b. 1896)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Chinese educator and diplomat (b. 1896)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1971", "text": "<PERSON><PERSON>, American artist (b. 1888)", "html": "1971 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American artist (b. 1888)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American artist (b. 1888)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1973", "text": "<PERSON><PERSON><PERSON>, Argentinian race car driver (b. 1932)", "html": "1973 - <a href=\"https://wikipedia.org/wiki/Nasif_Est%C3%A9fano\" title=\"Nasi<PERSON>\"><PERSON><PERSON><PERSON></a>, Argentinian race car driver (b. 1932)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Nasif_Est%C3%A9fano\" title=\"Nasi<PERSON>\"><PERSON><PERSON><PERSON></a>, Argentinian race car driver (b. 1932)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Nasif_Est%C3%A9fano"}]}, {"year": "1975", "text": "<PERSON>, American runner and general (b. 1887)", "html": "1975 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American runner and general (b. 1887)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American runner and general (b. 1887)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1977", "text": "<PERSON><PERSON>, Turkish composer (b. 1929)", "html": "1977 - <a href=\"https://wikipedia.org/wiki/Ferit_T%C3%BCz%C3%BCn\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Turkish composer (b. 1929)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Ferit_T%C3%BCz%C3%BCn\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Turkish composer (b. 1929)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Ferit_T%C3%BCz%C3%BCn"}]}, {"year": "1978", "text": "<PERSON><PERSON><PERSON>, Armenian-Russian civil servant and politician (b. 1895)", "html": "1978 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Armenian-Russian civil servant and politician (b. 1895)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Armenian-Russian civil servant and politician (b. 1895)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON><PERSON>"}]}, {"year": "1980", "text": "<PERSON>, Austrian physician and psychologist (b. 1906)", "html": "1980 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Austrian physician and psychologist (b. 1906)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Austrian physician and psychologist (b. 1906)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1982", "text": "<PERSON><PERSON><PERSON>, Norwegian singer-songwriter (b. 1952)", "html": "1982 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>ff\"><PERSON><PERSON><PERSON></a>, Norwegian singer-songwriter (b. 1952)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Norwegian singer-songwriter (b. 1952)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>d<PERSON>_<PERSON>ff"}]}, {"year": "1983", "text": "<PERSON>, American government official (b. 1919)", "html": "1983 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American government official (b. 1919)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American government official (b. 1919)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1984", "text": "<PERSON>, French actor, director, producer, and screenwriter (b. 1932)", "html": "1984 - <a href=\"https://wikipedia.org/wiki/Fran%C3%A7<PERSON>_T<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French actor, director, producer, and screenwriter (b. 1932)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Fran%C3%A<PERSON><PERSON>_T<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French actor, director, producer, and screenwriter (b. 1932)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Fran%C3%A<PERSON><PERSON>_<PERSON><PERSON><PERSON>"}]}, {"year": "1985", "text": "<PERSON>, American assassin and politician (b. 1946)", "html": "1985 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American assassin and politician (b. 1946)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American assassin and politician (b. 1946)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1986", "text": "<PERSON>, Australian jurist and politician, 22nd Attorney-General of Australia (b. 1922)", "html": "1986 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian jurist and politician, 22nd <a href=\"https://wikipedia.org/wiki/Attorney-General_of_Australia\" title=\"Attorney-General of Australia\">Attorney-General of Australia</a> (b. 1922)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian jurist and politician, 22nd <a href=\"https://wikipedia.org/wiki/Attorney-General_of_Australia\" title=\"Attorney-General of Australia\">Attorney-General of Australia</a> (b. 1922)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Attorney-General of Australia", "link": "https://wikipedia.org/wiki/Attorney-General_of_Australia"}]}, {"year": "1989", "text": "<PERSON>, Hungarian-French director, producer, and screenwriter (b. 1910)", "html": "1989 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Jean Image\"><PERSON></a>, Hungarian-French director, producer, and screenwriter (b. 1910)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Jean_<PERSON>\" title=\"Jean Image\"><PERSON></a>, Hungarian-French director, producer, and screenwriter (b. 1910)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1990", "text": "<PERSON><PERSON>, Lebanese engineer and politician (b. 1934)", "html": "1990 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Lebanese engineer and politician (b. 1934)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Lebanese engineer and politician (b. 1934)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1990", "text": "<PERSON><PERSON><PERSON>, Indian spiritual guru, philosopher and author (b. 1921)", "html": "1990 - <a href=\"https://wikipedia.org/wiki/Prab<PERSON>_Ranjan_<PERSON>rkar\" title=\"<PERSON><PERSON><PERSON> Ranjan Sarkar\"><PERSON><PERSON><PERSON></a>, Indian spiritual guru, philosopher and author (b. 1921)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/P<PERSON><PERSON>_<PERSON>njan_<PERSON>\" title=\"<PERSON><PERSON>hat Ranjan Sarkar\"><PERSON><PERSON><PERSON></a>, Indian spiritual guru, philosopher and author (b. 1921)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_Ranjan_Sarkar"}]}, {"year": "1991", "text": "<PERSON><PERSON><PERSON>, Albanian composer, conductor, and musicologist (b. 1909)", "html": "1991 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Albanian composer, conductor, and musicologist (b. 1909)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Albanian composer, conductor, and musicologist (b. 1909)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>renc_Antoni"}]}, {"year": "1992", "text": "<PERSON><PERSON>, Croatian politician, writer and publisher (b. 1898)", "html": "1992 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"An<PERSON> Ciliga\"><PERSON><PERSON></a>, Croatian politician, writer and publisher (b. 1898)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"An<PERSON> Ciliga\"><PERSON><PERSON></a>, Croatian politician, writer and publisher (b. 1898)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Ante_<PERSON>ga"}]}, {"year": "1992", "text": "<PERSON>, American lawyer and judge (b. 1921)", "html": "1992 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and judge (b. 1921)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and judge (b. 1921)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1993", "text": "<PERSON>, American journalist and critic (b. 1899)", "html": "1993 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American journalist and critic (b. 1899)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American journalist and critic (b. 1899)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1995", "text": "<PERSON><PERSON>, American singer (b. 1916)", "html": "1995 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American singer (b. 1916)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American singer (b. 1916)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1995", "text": "<PERSON><PERSON><PERSON>, Spanish author and illustrator (b. 1919)", "html": "1995 - <a href=\"https://wikipedia.org/wiki/Jes%C3%BAs_Blasco\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Spanish author and illustrator (b. 1919)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Jes%C3%BAs_Blasco\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Spanish author and illustrator (b. 1919)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Jes%C3%BAs_Blasco"}]}, {"year": "1995", "text": "<PERSON>, American sculptor and painter (b. 1939)", "html": "1995 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American sculptor and painter (b. 1939)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American sculptor and painter (b. 1939)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1995", "text": "<PERSON>, American singer-songwriter and guitarist (b. 1967)", "html": "1995 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and guitarist (b. 1967)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and guitarist (b. 1967)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1996", "text": "<PERSON><PERSON>, Greek general and politician (b. 1910)", "html": "1996 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Greek general and politician (b. 1910)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Greek general and politician (b. 1910)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1998", "text": "<PERSON>, American soldier and politician, 64th Governor of Massachusetts (b. 1915)", "html": "1998 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, American soldier and politician, 64th <a href=\"https://wikipedia.org/wiki/Governor_of_Massachusetts\" title=\"Governor of Massachusetts\">Governor of Massachusetts</a> (b. 1915)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, American soldier and politician, 64th <a href=\"https://wikipedia.org/wiki/Governor_of_Massachusetts\" title=\"Governor of Massachusetts\">Governor of Massachusetts</a> (b. 1915)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>"}, {"title": "Governor of Massachusetts", "link": "https://wikipedia.org/wiki/Governor_of_Massachusetts"}]}, {"year": "1999", "text": "<PERSON>, Danish author and illustrator (b. 1924)", "html": "1999 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Danish author and illustrator (b. 1924)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Danish author and illustrator (b. 1924)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1999", "text": "<PERSON><PERSON>, Turkish political scientist, lawyer, and politician (b. 1939)", "html": "1999 - <a href=\"https://wikipedia.org/wiki/Ahmet_Taner_K%C4%B1%C5%9Flal%C4%B1\" title=\"<PERSON><PERSON> Taner Kışlalı\"><PERSON><PERSON></a>, Turkish political scientist, lawyer, and politician (b. 1939)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Ahmet_Taner_K%C4%B1%C5%9Flal%C4%B1\" title=\"<PERSON><PERSON> Taner Kışlalı\"><PERSON><PERSON></a>, Turkish political scientist, lawyer, and politician (b. 1939)", "links": [{"title": "<PERSON><PERSON> Taner Kışlalı", "link": "https://wikipedia.org/wiki/Ahmet_Taner_K%C4%B1%C5%9Flal%C4%B1"}]}, {"year": "2002", "text": "<PERSON>, American academic and president of Pace University (b. 1917)", "html": "2002 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American academic and president of Pace University (b. 1917)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American academic and president of Pace University (b. 1917)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "2003", "text": "<PERSON>, American politician (b. 1916)", "html": "2003 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American politician (b. 1916)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American politician (b. 1916)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "2003", "text": "<PERSON>, Puerto Rican engineer and politician, 3rd Governor of Puerto Rico (b. 1904)", "html": "2003 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_Ferr%C3%A9\" title=\"<PERSON>\"><PERSON></a>, Puerto Rican engineer and politician, 3rd <a href=\"https://wikipedia.org/wiki/Governor_of_Puerto_Rico\" title=\"Governor of Puerto Rico\">Governor of Puerto Rico</a> (b. 1904)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_Ferr%C3%A9\" title=\"<PERSON>\"><PERSON></a>, Puerto Rican engineer and politician, 3rd <a href=\"https://wikipedia.org/wiki/Governor_of_Puerto_Rico\" title=\"Governor of Puerto Rico\">Governor of Puerto Rico</a> (b. 1904)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Luis_<PERSON>._Ferr%C3%A9"}, {"title": "Governor of Puerto Rico", "link": "https://wikipedia.org/wiki/Governor_of_Puerto_Rico"}]}, {"year": "2003", "text": "<PERSON>, American singer-songwriter and guitarist (b. 1969)", "html": "2003 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and guitarist (b. 1969)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and guitarist (b. 1969)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2006", "text": "<PERSON>, American singer-songwriter and drummer  (b. 1959)", "html": "2006 - <a href=\"https://wikipedia.org/wiki/Sandy_West\" title=\"Sandy West\"><PERSON></a>, American singer-songwriter and drummer (b. 1959)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Sandy_West\" title=\"Sandy West\"><PERSON></a>, American singer-songwriter and drummer (b. 1959)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Sandy_West"}]}, {"year": "2007", "text": "<PERSON>, English singer-songwriter and guitarist (b. 1951)", "html": "2007 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(musician)\" title=\"<PERSON> (musician)\"><PERSON></a>, English singer-songwriter and guitarist (b. 1951)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(musician)\" title=\"<PERSON> (musician)\"><PERSON></a>, English singer-songwriter and guitarist (b. 1951)", "links": [{"title": "<PERSON> (musician)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(musician)"}]}, {"year": "2010", "text": "<PERSON><PERSON>, Indian poet and translator (b. 1949)", "html": "2010 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON><PERSON>\" title=\"A. Ayyappan\"><PERSON><PERSON></a>, Indian poet and translator (b. 1949)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON> Ayyappan\"><PERSON><PERSON></a>, Indian poet and translator (b. 1949)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON><PERSON>"}]}, {"year": "2011", "text": "<PERSON><PERSON><PERSON>, Turkish journalist and author (b. 1954)", "html": "2011 - <a href=\"https://wikipedia.org/wiki/Hikmet_Bil%C3%A2\" title=\"Hikmet Bilâ\"><PERSON>kmet Bilâ</a>, Turkish journalist and author (b. 1954)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Hikmet_Bil%C3%A2\" title=\"Hikmet Bilâ\"><PERSON>k<PERSON> Bilâ</a>, Turkish journalist and author (b. 1954)", "links": [{"title": "Hikmet Bilâ", "link": "https://wikipedia.org/wiki/Hikmet_Bil%C3%A2"}]}, {"year": "2011", "text": "<PERSON><PERSON>, Slovenian poet and author (b. 1928)", "html": "2011 - <a href=\"https://wikipedia.org/wiki/Tone_Pav%C4%8Dek\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Slovenian poet and author (b. 1928)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Tone_Pav%C4%8Dek\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Slovenian poet and author (b. 1928)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Tone_Pav%C4%8Dek"}]}, {"year": "2012", "text": "<PERSON><PERSON>, Indian director, producer, and screenwriter (b. 1932)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Indian director, producer, and screenwriter (b. 1932)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Indian director, producer, and screenwriter (b. 1932)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "2012", "text": "<PERSON><PERSON>, Polish educator (b. 1904)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Polish educator (b. 1904)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Polish educator (b. 1904)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>ki"}]}, {"year": "2012", "text": "<PERSON><PERSON><PERSON>, Czech volleyball player and educator (b. 1907)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>%C3%ADk\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Czech volleyball player and educator (b. 1907)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>%C3%ADk\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Czech volleyball player and educator (b. 1907)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>l%C3%ADk"}]}, {"year": "2012", "text": "<PERSON><PERSON>, South African photographer and journalist (b. 1930)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/Al<PERSON>_<PERSON>mal<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, South African photographer and journalist (b. 1930)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>mal<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, South African photographer and journalist (b. 1930)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Alf_Kumalo"}]}, {"year": "2012", "text": "<PERSON>, American historian, lieutenant, and politician (b. 1922)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American historian, lieutenant, and politician (b. 1922)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American historian, lieutenant, and politician (b. 1922)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2013", "text": "<PERSON>, American businessman (b. 1923)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American businessman (b. 1923)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American businessman (b. 1923)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2013", "text": "<PERSON><PERSON><PERSON>, Italian composer and conductor (b. 1924)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Italian composer and conductor (b. 1924)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Italian composer and conductor (b. 1924)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "2013", "text": "<PERSON><PERSON> <PERSON>, Danish author, poet, and illustrator (b. 1957)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> T<PERSON>\"><PERSON><PERSON> <PERSON></a>, Danish author, poet, and illustrator (b. 1957)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON> <PERSON></a>, Danish author, poet, and illustrator (b. 1957)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON><PERSON>"}]}, {"year": "2013", "text": "Colonel <PERSON>, American singer-songwriter and drummer (b. 1954)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/Colonel_<PERSON>_<PERSON>\" title=\"Colonel <PERSON>\">Colonel <PERSON></a>, American singer-songwriter and drummer (b. 1954)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Colonel_<PERSON>_<PERSON>\" title=\"Colonel <PERSON>\">Colonel <PERSON></a>, American singer-songwriter and drummer (b. 1954)", "links": [{"title": "Colonel <PERSON>", "link": "https://wikipedia.org/wiki/Colonel_<PERSON>_<PERSON>"}]}, {"year": "2013", "text": "<PERSON>, American librarian and politician (b. 1936)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Major <PERSON>\"><PERSON></a>, American librarian and politician (b. 1936)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Major <PERSON>\">Major <PERSON></a>, American librarian and politician (b. 1936)", "links": [{"title": "Major <PERSON>", "link": "https://wikipedia.org/wiki/Major_<PERSON>"}]}, {"year": "2013", "text": "<PERSON>, Welsh swimmer (b. 1924)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Welsh swimmer (b. 1924)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Welsh swimmer (b. 1924)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2013", "text": "<PERSON>, Venezuelan journalist and author (b. 1927)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, Venezuelan journalist and author (b. 1927)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, Venezuelan journalist and author (b. 1927)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2014", "text": "<PERSON>, American journalist and author (b. 1921)", "html": "2014 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American journalist and author (b. 1921)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American journalist and author (b. 1921)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2014", "text": "<PERSON>, American businessman (b. 1926)", "html": "2014 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>unker_Hunt\" title=\"Nelson Bunker Hunt\"><PERSON></a>, American businessman (b. 1926)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>unker_Hunt\" title=\"Nelson Bunker Hunt\"><PERSON></a>, American businessman (b. 1926)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "2014", "text": "<PERSON><PERSON><PERSON><PERSON>, Iranian cleric and politician, Prime Minister of Iran (b. 1931)", "html": "2014 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Iranian cleric and politician, <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Iran\" title=\"Prime Minister of Iran\">Prime Minister of Iran</a> (b. 1931)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Iranian cleric and politician, <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Iran\" title=\"Prime Minister of Iran\">Prime Minister of Iran</a> (b. 1931)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>-<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>"}, {"title": "Prime Minister of Iran", "link": "https://wikipedia.org/wiki/Prime_Minister_of_Iran"}]}, {"year": "2014", "text": "<PERSON>, Hawaiian genealogist, author, and hula expert (b. 1925)", "html": "2014 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Hawaiian genealogist, author, and hula expert (b. 1925)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Hawaiian genealogist, author, and hula expert (b. 1925)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "2014", "text": "<PERSON><PERSON>, Australian lieutenant, lawyer, and politician, 21st Prime Minister of Australia (b. 1916)", "html": "2014 - <a href=\"https://wikipedia.org/wiki/Gough_<PERSON>hit<PERSON>\" title=\"Gough Whitlam\"><PERSON><PERSON></a>, Australian lieutenant, lawyer, and politician, 21st <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Australia\" title=\"Prime Minister of Australia\">Prime Minister of Australia</a> (b. 1916)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Gough_<PERSON>hit<PERSON>\" title=\"Gough Whitlam\"><PERSON><PERSON></a>, Australian lieutenant, lawyer, and politician, 21st <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Australia\" title=\"Prime Minister of Australia\">Prime Minister of Australia</a> (b. 1916)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Go<PERSON>_Whitlam"}, {"title": "Prime Minister of Australia", "link": "https://wikipedia.org/wiki/Prime_Minister_of_Australia"}]}, {"year": "2015", "text": "<PERSON>, Slovenian lawyer and politician (b. 1923)", "html": "2015 - <a href=\"https://wikipedia.org/wiki/France_Bu%C4%8Dar\" title=\"France Bučar\">France Buč<PERSON></a>, Slovenian lawyer and politician (b. 1923)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/France_Bu%C4%8Dar\" title=\"France Bučar\">France Bu<PERSON><PERSON></a>, Slovenian lawyer and politician (b. 1923)", "links": [{"title": "France Bučar", "link": "https://wikipedia.org/wiki/France_Bu%C4%8Dar"}]}, {"year": "2015", "text": "<PERSON>, American actor (b. 1936)", "html": "2015 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor (b. 1936)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor (b. 1936)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2015", "text": "<PERSON>, English conservationist and author (b. 1923)", "html": "2015 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English conservationist and author (b. 1923)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English conservationist and author (b. 1923)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "2015", "text": "<PERSON>, American philosopher, theorist, and academic (b. 1922)", "html": "2015 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American philosopher, theorist, and academic (b. 1922)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American philosopher, theorist, and academic (b. 1922)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2020", "text": "<PERSON>, English television presenter (b. 1933)", "html": "2020 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English television presenter (b. 1933)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English television presenter (b. 1933)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2021", "text": "<PERSON>, Dutch conductor and violinist (b. 1929)", "html": "2021 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch conductor and violinist (b. 1929)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch conductor and violinist (b. 1929)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2023", "text": "<PERSON>, English footballer and manager (b. 1937)", "html": "2023 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English footballer and manager (b. 1937)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English footballer and manager (b. 1937)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2023", "text": "<PERSON><PERSON>, Portuguese dog (b. 1992)", "html": "2023 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_(dog)\" title=\"<PERSON><PERSON> (dog)\"><PERSON><PERSON></a>, Portuguese dog (b. 1992)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_(dog)\" title=\"<PERSON><PERSON> (dog)\"><PERSON><PERSON></a>, Portuguese dog (b. 1992)", "links": [{"title": "<PERSON><PERSON> (dog)", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_(dog)"}]}, {"year": "2023", "text": "<PERSON>, Australian politician, 21st Governor General of Australia (b. 1933)", "html": "2023 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian politician, 21st <a href=\"https://wikipedia.org/wiki/Governor_General_of_Australia\" class=\"mw-redirect\" title=\"Governor General of Australia\">Governor General of Australia</a> (b. 1933)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian politician, 21st <a href=\"https://wikipedia.org/wiki/Governor_General_of_Australia\" class=\"mw-redirect\" title=\"Governor General of Australia\">Governor General of Australia</a> (b. 1933)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Governor General of Australia", "link": "https://wikipedia.org/wiki/Governor_General_of_Australia"}]}, {"year": "2024", "text": "<PERSON>, Canadian singer and comedian (b. 1933)", "html": "2024 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian singer and comedian (b. 1933)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian singer and comedian (b. 1933)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}]}}