{"date": "November 7", "url": "https://wikipedia.org/wiki/November_7", "data": {"Events": [{"year": "335", "text": "<PERSON><PERSON><PERSON>, 20th pope of Alexandria, is banished to Trier on the charge that he prevented a grain fleet from sailing to Constantinople.", "html": "335 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_of_Alexandria\" title=\"<PERSON><PERSON><PERSON> of Alexandria\"><PERSON><PERSON><PERSON></a>, 20th <a href=\"https://wikipedia.org/wiki/List_of_Greek_Orthodox_patriarchs_of_Alexandria\" title=\"List of Greek Orthodox patriarchs of Alexandria\">pope of Alexandria</a>, is banished to <a href=\"https://wikipedia.org/wiki/Trier\" title=\"Trier\">Trier</a> on the charge that he prevented a <a href=\"https://wikipedia.org/wiki/Cereal\" title=\"Cereal\">grain</a> fleet from sailing to <a href=\"https://wikipedia.org/wiki/Constantinople\" title=\"Constantinople\">Constantinople</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_of_Alexandria\" title=\"<PERSON><PERSON><PERSON> of Alexandria\"><PERSON><PERSON><PERSON></a>, 20th <a href=\"https://wikipedia.org/wiki/List_of_Greek_Orthodox_patriarchs_of_Alexandria\" title=\"List of Greek Orthodox patriarchs of Alexandria\">pope of Alexandria</a>, is banished to <a href=\"https://wikipedia.org/wiki/Trier\" title=\"Trier\">Trier</a> on the charge that he prevented a <a href=\"https://wikipedia.org/wiki/Cereal\" title=\"Cereal\">grain</a> fleet from sailing to <a href=\"https://wikipedia.org/wiki/Constantinople\" title=\"Constantinople\">Constantinople</a>.", "links": [{"title": "<PERSON><PERSON><PERSON> of Alexandria", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_of_Alexandria"}, {"title": "List of Greek Orthodox patriarchs of Alexandria", "link": "https://wikipedia.org/wiki/List_of_Greek_Orthodox_patriarchs_of_Alexandria"}, {"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Trier"}, {"title": "Cereal", "link": "https://wikipedia.org/wiki/Cereal"}, {"title": "Constantinople", "link": "https://wikipedia.org/wiki/Constantinople"}]}, {"year": "680", "text": "The Sixth Ecumenical Council commences in Constantinople.", "html": "680 - The <a href=\"https://wikipedia.org/wiki/Third_Council_of_Constantinople\" title=\"Third Council of Constantinople\">Sixth Ecumenical Council</a> commences in Constantinople.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Third_Council_of_Constantinople\" title=\"Third Council of Constantinople\">Sixth Ecumenical Council</a> commences in Constantinople.", "links": [{"title": "Third Council of Constantinople", "link": "https://wikipedia.org/wiki/Third_Council_of_Constantinople"}]}, {"year": "921", "text": "Treaty of Bonn: The Frankish kings <PERSON> the <PERSON> and <PERSON> sign a peace treaty or 'pact of friendship' (amicitia) to recognize their borders along the Rhine.", "html": "921 - <a href=\"https://wikipedia.org/wiki/Treaty_of_Bonn\" title=\"Treaty of Bonn\">Treaty of Bonn</a>: The Frankish kings <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_Simple\" title=\"Charles the Simple\"><PERSON> the Simple</a> and <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_Fowler\" title=\"<PERSON> Fowler\"><PERSON> Fowler</a> sign a <a href=\"https://wikipedia.org/wiki/Peace_treaty\" title=\"Peace treaty\">peace treaty</a> or 'pact of friendship' (<span title=\"Latin-language text\"><i lang=\"la\"><a href=\"https://wikipedia.org/wiki/Friendship\" title=\"Friendship\">amicitia</a></i></span>) to recognize their borders along the <a href=\"https://wikipedia.org/wiki/Rhine\" title=\"Rhine\">Rhine</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Treaty_of_Bonn\" title=\"Treaty of Bonn\">Treaty of Bonn</a>: The Frankish kings <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_Simple\" title=\"Charles the Simple\"><PERSON> Simple</a> and <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_Fowler\" title=\"<PERSON> Fowler\"><PERSON></a> sign a <a href=\"https://wikipedia.org/wiki/Peace_treaty\" title=\"Peace treaty\">peace treaty</a> or 'pact of friendship' (<span title=\"Latin-language text\"><i lang=\"la\"><a href=\"https://wikipedia.org/wiki/Friendship\" title=\"Friendship\">amicitia</a></i></span>) to recognize their borders along the <a href=\"https://wikipedia.org/wiki/Rhine\" title=\"Rhine\">Rhine</a>.", "links": [{"title": "Treaty of Bonn", "link": "https://wikipedia.org/wiki/Treaty_of_Bonn"}, {"title": "<PERSON> the <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_Simple"}, {"title": "<PERSON> the <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "Peace treaty", "link": "https://wikipedia.org/wiki/Peace_treaty"}, {"title": "Friendship", "link": "https://wikipedia.org/wiki/Friendship"}, {"title": "Rhine", "link": "https://wikipedia.org/wiki/Rhine"}]}, {"year": "1426", "text": "Lam S<PERSON>n uprising: Lam S<PERSON> rebels emerge victorious against the Ming army in the Battle of Tốt Động - Chúc Động taking place in Đông Quan, in now Hanoi.", "html": "1426 - <a href=\"https://wikipedia.org/wiki/Lam_S%C6%A1n_uprising\" title=\"Lam Sơn uprising\"><span title=\"Vietnamese-language text\"><span lang=\"vi\" style=\"font-style: normal;\"><PERSON></span></span> uprising</a>: <span title=\"Vietnamese-language text\"><span lang=\"vi\" style=\"font-style: normal;\"><PERSON></span></span> rebels emerge victorious against the <a href=\"https://wikipedia.org/wiki/Ming_dynasty\" title=\"Ming dynasty\">Ming</a> army in the <a href=\"https://wikipedia.org/wiki/Battle_of_T%E1%BB%91t_%C4%90%E1%BB%99ng_%E2%80%93_Ch%C3%BAc_%C4%90%E1%BB%99ng\" title=\"Battle of Tốt Động - Chúc Động\">Battle of <span title=\"Vietnamese-language text\"><span lang=\"vi\" style=\"font-style: normal;\">Tốt <PERSON> - <PERSON><PERSON></span></span></a> taking place in <span title=\"Vietnamese-language text\"><span lang=\"vi\" style=\"font-style: normal;\">Đ<PERSON>ng <PERSON></span></span>, in now <a href=\"https://wikipedia.org/wiki/Hanoi\" title=\"Hanoi\">Hanoi</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Lam_S%C6%A1n_uprising\" title=\"Lam Sơn uprising\"><span title=\"Vietnamese-language text\"><span lang=\"vi\" style=\"font-style: normal;\"><PERSON></span></span> uprising</a>: <span title=\"Vietnamese-language text\"><span lang=\"vi\" style=\"font-style: normal;\">Lam <PERSON></span></span> rebels emerge victorious against the <a href=\"https://wikipedia.org/wiki/Ming_dynasty\" title=\"Ming dynasty\">Ming</a> army in the <a href=\"https://wikipedia.org/wiki/Battle_of_T%E1%BB%91t_%C4%90%E1%BB%99ng_%E2%80%93_Ch%C3%BAc_%C4%90%E1%BB%99ng\" title=\"Battle of Tốt Động - Chúc Động\">Battle of <span title=\"Vietnamese-language text\"><span lang=\"vi\" style=\"font-style: normal;\">Tốt <PERSON>ng - <PERSON><PERSON><PERSON></span></span></a> taking place in <span title=\"Vietnamese-language text\"><span lang=\"vi\" style=\"font-style: normal;\">Đ<PERSON><PERSON>uan</span></span>, in now <a href=\"https://wikipedia.org/wiki/Hanoi\" title=\"Hanoi\">Hanoi</a>.", "links": [{"title": "Lam Sơn uprising", "link": "https://wikipedia.org/wiki/Lam_S%C6%A1n_uprising"}, {"title": "Ming dynasty", "link": "https://wikipedia.org/wiki/Ming_dynasty"}, {"title": "Battle of Tốt Động - <PERSON><PERSON>c Động", "link": "https://wikipedia.org/wiki/Battle_of_T%E1%BB%91t_%C4%90%E1%BB%99ng_%E2%80%93_Ch%C3%BAc_%C4%90%E1%BB%99ng"}, {"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>oi"}]}, {"year": "1492", "text": "The Ensisheim meteorite, the oldest meteorite with a known date of impact, strikes the Earth in a wheat field outside the village of Ensisheim, Alsace, France.", "html": "1492 - The <a href=\"https://wikipedia.org/wiki/Ensisheim_(meteorite)\" class=\"mw-redirect\" title=\"Ensisheim (meteorite)\">Ensisheim meteorite</a>, the oldest <a href=\"https://wikipedia.org/wiki/Meteorite\" title=\"Meteorite\">meteorite</a> with a known date of impact, strikes the Earth in a <a href=\"https://wikipedia.org/wiki/Wheat\" title=\"Wheat\">wheat</a> field outside the village of <a href=\"https://wikipedia.org/wiki/Ensisheim\" title=\"Ensisheim\">Ensisheim</a>, <a href=\"https://wikipedia.org/wiki/Alsace\" title=\"Alsace\">Alsace</a>, France.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Ensisheim_(meteorite)\" class=\"mw-redirect\" title=\"Ensisheim (meteorite)\">Ensisheim meteorite</a>, the oldest <a href=\"https://wikipedia.org/wiki/Meteorite\" title=\"Meteorite\">meteorite</a> with a known date of impact, strikes the Earth in a <a href=\"https://wikipedia.org/wiki/Wheat\" title=\"Wheat\">wheat</a> field outside the village of <a href=\"https://wikipedia.org/wiki/Ensisheim\" title=\"Ensisheim\">Ensisheim</a>, <a href=\"https://wikipedia.org/wiki/Alsace\" title=\"Alsace\">Alsace</a>, France.", "links": [{"title": "Ensisheim (meteorite)", "link": "https://wikipedia.org/wiki/En<PERSON><PERSON>_(meteorite)"}, {"title": "Meteorite", "link": "https://wikipedia.org/wiki/Meteorite"}, {"title": "Wheat", "link": "https://wikipedia.org/wiki/Wheat"}, {"title": "Ensisheim", "link": "https://wikipedia.org/wiki/Ensisheim"}, {"title": "Alsace", "link": "https://wikipedia.org/wiki/Alsace"}]}, {"year": "1504", "text": "<PERSON> returns from his fourth and last voyage.", "html": "1504 - <a href=\"https://wikipedia.org/wiki/Christopher_<PERSON>\" title=\"Christopher <PERSON>\"><PERSON></a> returns from his fourth and last voyage.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Christopher_Columbus\" title=\"Christopher Columbus\"><PERSON></a> returns from his fourth and last voyage.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1619", "text": "<PERSON> is crowned Queen of Bohemia.", "html": "1619 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Queen_of_Bohemia\" title=\"<PERSON>, Queen of Bohemia\"><PERSON></a> is <a href=\"https://wikipedia.org/wiki/Coronation\" title=\"Coronation\">crowned</a> <a href=\"https://wikipedia.org/wiki/Queen_of_Bohemia\" class=\"mw-redirect\" title=\"Queen of Bohemia\">Queen of Bohemia</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Queen_of_Bohemia\" title=\"<PERSON>, Queen of Bohemia\"><PERSON></a> is <a href=\"https://wikipedia.org/wiki/Coronation\" title=\"Coronation\">crowned</a> <a href=\"https://wikipedia.org/wiki/Queen_of_Bohemia\" class=\"mw-redirect\" title=\"Queen of Bohemia\">Queen of Bohemia</a>.", "links": [{"title": "<PERSON>, Queen of Bohemia", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Queen_of_Bohemia"}, {"title": "Coronation", "link": "https://wikipedia.org/wiki/Coronation"}, {"title": "Queen of Bohemia", "link": "https://wikipedia.org/wiki/Queen_of_Bohemia"}]}, {"year": "1665", "text": "The London Gazette, the oldest surviving journal, is first published.", "html": "1665 - <i><a href=\"https://wikipedia.org/wiki/The_London_Gazette\" title=\"The London Gazette\">The London Gazette</a></i>, the oldest surviving journal, is first published.", "no_year_html": "<i><a href=\"https://wikipedia.org/wiki/The_London_Gazette\" title=\"The London Gazette\">The London Gazette</a></i>, the oldest surviving journal, is first published.", "links": [{"title": "The London Gazette", "link": "https://wikipedia.org/wiki/The_London_Gazette"}]}, {"year": "1723", "text": "<PERSON>, du Donnerwort, BWV 60, a dialogue cantata by <PERSON> for Leipzig, was first performed.", "html": "1723 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_du_<PERSON>wort,_BWV_60\" title=\"<PERSON> Ewigkeit, du Donnerwort, BWV 60\"><i><PERSON> E<PERSON>ke<PERSON>, du Donnerwort</i>, BWV 60</a>, a dialogue cantata by <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> for Leipzig, was first performed.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_du_<PERSON>wort,_BWV_60\" title=\"<PERSON> Ewigkeit, du Donnerwort, BWV 60\"><i><PERSON>ke<PERSON>, du Donnerwort</i>, BWV 60</a>, a dialogue cantata by <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> for Leipzig, was first performed.", "links": [{"title": "<PERSON>, du Donner<PERSON>rt, BWV 60", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_<PERSON>_<PERSON>wo<PERSON>,_<PERSON>_60"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1775", "text": "<PERSON> (also known as Lord <PERSON>), the Royal Governor of the Colony of Virginia, starts the first mass emancipation of slaves in North America by issuing Lord <PERSON><PERSON>'s Offer of Emancipation, which offers freedom to slaves who abandoned their colonial masters to fight with <PERSON> and the British.", "html": "1775 - <a href=\"https://wikipedia.org/wiki/<PERSON>,_4th_Earl_of_Dunmore\" title=\"<PERSON>, 4th Earl of Dunmore\"><PERSON></a> (also known as Lord <PERSON>), the <a href=\"https://wikipedia.org/wiki/Royal_Governor_of_Virginia\" class=\"mw-redirect\" title=\"Royal Governor of Virginia\">Royal Governor</a> of the <a href=\"https://wikipedia.org/wiki/Colony_of_Virginia\" title=\"Colony of Virginia\">Colony of Virginia</a>, starts the first mass emancipation of slaves in North America by issuing <a href=\"https://wikipedia.org/wiki/Dunmore%27s_Proclamation\" title=\"<PERSON><PERSON><PERSON>'s Proclamation\">Lord <PERSON><PERSON>'s Offer of Emancipation</a>, which offers freedom to slaves who abandoned their colonial masters to fight with <PERSON> and the British.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>,_4th_Earl_of_Dunmore\" title=\"<PERSON>, 4th Earl of Dunmore\"><PERSON></a> (also known as Lord <PERSON>), the <a href=\"https://wikipedia.org/wiki/Royal_Governor_of_Virginia\" class=\"mw-redirect\" title=\"Royal Governor of Virginia\">Royal Governor</a> of the <a href=\"https://wikipedia.org/wiki/Colony_of_Virginia\" title=\"Colony of Virginia\">Colony of Virginia</a>, starts the first mass emancipation of slaves in North America by issuing <a href=\"https://wikipedia.org/wiki/Dunmore%27s_Proclamation\" title=\"<PERSON><PERSON><PERSON>'s Proclamation\">Lord <PERSON>'s Offer of Emancipation</a>, which offers freedom to slaves who abandoned their colonial masters to fight with <PERSON> and the British.", "links": [{"title": "<PERSON>, 4th Earl of Dunmore", "link": "https://wikipedia.org/wiki/<PERSON>,_4th_Earl_of_Dunmore"}, {"title": "Royal Governor of Virginia", "link": "https://wikipedia.org/wiki/Royal_Governor_of_Virginia"}, {"title": "Colony of Virginia", "link": "https://wikipedia.org/wiki/Colony_of_Virginia"}, {"title": "<PERSON><PERSON><PERSON>'s Proclamation", "link": "https://wikipedia.org/wiki/Dunmore%27s_Proclamation"}]}, {"year": "1786", "text": "The oldest musical organization in the United States is founded as the Stoughton Musical Society.", "html": "1786 - The oldest musical organization in the United States is founded as the <a href=\"https://wikipedia.org/wiki/Stoughton_Musical_Society\" title=\"Stoughton Musical Society\">Stoughton Musical Society</a>.", "no_year_html": "The oldest musical organization in the United States is founded as the <a href=\"https://wikipedia.org/wiki/Stoughton_Musical_Society\" title=\"Stoughton Musical Society\">Stoughton Musical Society</a>.", "links": [{"title": "Stoughton Musical Society", "link": "https://wikipedia.org/wiki/Stoughton_Musical_Society"}]}, {"year": "1811", "text": "Tecumseh's War: The Battle of Tippecanoe is fought near present-day Battle Ground, Indiana, United States.", "html": "1811 - <a href=\"https://wikipedia.org/wiki/Tecumseh%27s_War\" title=\"Tecumseh's War\">Tecumseh's War</a>: The <a href=\"https://wikipedia.org/wiki/Battle_of_Tippecanoe\" title=\"Battle of Tippecanoe\">Battle of Tippecanoe</a> is fought near present-day <a href=\"https://wikipedia.org/wiki/Battle_Ground,_Indiana\" title=\"Battle Ground, Indiana\">Battle Ground, Indiana</a>, United States.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Tecumseh%27s_War\" title=\"Tecumseh's War\">Tecumseh's War</a>: The <a href=\"https://wikipedia.org/wiki/Battle_of_Tippecanoe\" title=\"Battle of Tippecanoe\">Battle of Tippecanoe</a> is fought near present-day <a href=\"https://wikipedia.org/wiki/Battle_Ground,_Indiana\" title=\"Battle Ground, Indiana\">Battle Ground, Indiana</a>, United States.", "links": [{"title": "Tecumseh's War", "link": "https://wikipedia.org/wiki/Tecumseh%27s_War"}, {"title": "Battle of Tippecanoe", "link": "https://wikipedia.org/wiki/Battle_of_Tippecanoe"}, {"title": "Battle Ground, Indiana", "link": "https://wikipedia.org/wiki/Battle_Ground,_Indiana"}]}, {"year": "1837", "text": "In Alton, Illinois, abolitionist printer <PERSON> is shot dead by a mob while attempting to protect his printing shop from being destroyed a third time.", "html": "1837 - In <a href=\"https://wikipedia.org/wiki/Alton,_Illinois\" title=\"Alton, Illinois\">Alton, Illinois</a>, <a href=\"https://wikipedia.org/wiki/Abolitionism_in_the_United_States\" title=\"Abolitionism in the United States\">abolitionist</a> printer <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_Lovejoy\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a> is shot dead by a mob while attempting to protect his printing shop from being destroyed a third time.", "no_year_html": "In <a href=\"https://wikipedia.org/wiki/Alton,_Illinois\" title=\"Alton, Illinois\">Alton, Illinois</a>, <a href=\"https://wikipedia.org/wiki/Abolitionism_in_the_United_States\" title=\"Abolitionism in the United States\">abolitionist</a> printer <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_Lovejoy\" class=\"mw-redirect\" title=\"<PERSON> Lovejoy\"><PERSON></a> is shot dead by a mob while attempting to protect his printing shop from being destroyed a third time.", "links": [{"title": "Alton, Illinois", "link": "https://wikipedia.org/wiki/Alton,_Illinois"}, {"title": "Abolitionism in the United States", "link": "https://wikipedia.org/wiki/Abolitionism_in_the_United_States"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>joy"}]}, {"year": "1861", "text": "American Civil War: Battle of Belmont: In Belmont, Missouri, Union forces led by General <PERSON> overrun a Confederate camp but are forced to retreat when Confederate reinforcements arrive.", "html": "1861 - <a href=\"https://wikipedia.org/wiki/American_Civil_War\" title=\"American Civil War\">American Civil War</a>: <a href=\"https://wikipedia.org/wiki/Battle_of_Belmont\" title=\"Battle of Belmont\">Battle of Belmont</a>: In <a href=\"https://wikipedia.org/wiki/Belmont,_Missouri\" title=\"Belmont, Missouri\">Belmont, Missouri</a>, <a href=\"https://wikipedia.org/wiki/United_States\" title=\"United States\">Union</a> forces led by <a href=\"https://wikipedia.org/wiki/General\" class=\"mw-redirect\" title=\"General\">General</a> <a href=\"https://wikipedia.org/wiki/Ulysses_<PERSON>._Grant\" title=\"Ulysses S. Grant\">Ulysses <PERSON> Grant</a> overrun a <a href=\"https://wikipedia.org/wiki/Confederate_States_of_America\" title=\"Confederate States of America\">Confederate</a> camp but are forced to retreat when Confederate reinforcements arrive.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/American_Civil_War\" title=\"American Civil War\">American Civil War</a>: <a href=\"https://wikipedia.org/wiki/Battle_of_Belmont\" title=\"Battle of Belmont\">Battle of Belmont</a>: In <a href=\"https://wikipedia.org/wiki/Belmont,_Missouri\" title=\"Belmont, Missouri\">Belmont, Missouri</a>, <a href=\"https://wikipedia.org/wiki/United_States\" title=\"United States\">Union</a> forces led by <a href=\"https://wikipedia.org/wiki/General\" class=\"mw-redirect\" title=\"General\">General</a> <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>._Grant\" title=\"Ulysses S. Grant\">Ulysses <PERSON> Grant</a> overrun a <a href=\"https://wikipedia.org/wiki/Confederate_States_of_America\" title=\"Confederate States of America\">Confederate</a> camp but are forced to retreat when Confederate reinforcements arrive.", "links": [{"title": "American Civil War", "link": "https://wikipedia.org/wiki/American_Civil_War"}, {"title": "Battle of Belmont", "link": "https://wikipedia.org/wiki/Battle_of_Belmont"}, {"title": "Belmont, Missouri", "link": "https://wikipedia.org/wiki/Belmont,_Missouri"}, {"title": "United States", "link": "https://wikipedia.org/wiki/United_States"}, {"title": "General", "link": "https://wikipedia.org/wiki/General"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>"}, {"title": "Confederate States of America", "link": "https://wikipedia.org/wiki/Confederate_States_of_America"}]}, {"year": "1861", "text": "The first Melbourne Cup horse race is held in Melbourne, Australia.", "html": "1861 - The first <a href=\"https://wikipedia.org/wiki/Melbourne_Cup\" title=\"Melbourne Cup\">Melbourne Cup</a> horse race is held in <a href=\"https://wikipedia.org/wiki/Melbourne\" title=\"Melbourne\">Melbourne</a>, <a href=\"https://wikipedia.org/wiki/Australia\" title=\"Australia\">Australia</a>.", "no_year_html": "The first <a href=\"https://wikipedia.org/wiki/Melbourne_Cup\" title=\"Melbourne Cup\">Melbourne Cup</a> horse race is held in <a href=\"https://wikipedia.org/wiki/Melbourne\" title=\"Melbourne\">Melbourne</a>, <a href=\"https://wikipedia.org/wiki/Australia\" title=\"Australia\">Australia</a>.", "links": [{"title": "Melbourne Cup", "link": "https://wikipedia.org/wiki/Melbourne_Cup"}, {"title": "Melbourne", "link": "https://wikipedia.org/wiki/Melbourne"}, {"title": "Australia", "link": "https://wikipedia.org/wiki/Australia"}]}, {"year": "1874", "text": "A cartoon by <PERSON> in Harper's Weekly, is considered the first important use of an elephant as a symbol for the United States Republican Party.", "html": "1874 - A <a href=\"https://wikipedia.org/wiki/Cartoon\" title=\"Cartoon\">cartoon</a> by <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> in <i><a href=\"https://wikipedia.org/wiki/Harper%27s_Weekly\" title=\"Harper's Weekly\">Harper's Weekly</a></i>, is considered the first important use of an <a href=\"https://wikipedia.org/wiki/Elephant\" title=\"Elephant\">elephant</a> as a symbol for the <a href=\"https://wikipedia.org/wiki/Republican_Party_(United_States)\" title=\"Republican Party (United States)\">United States Republican Party</a>.", "no_year_html": "A <a href=\"https://wikipedia.org/wiki/Cartoon\" title=\"Cartoon\">cartoon</a> by <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> in <i><a href=\"https://wikipedia.org/wiki/Harper%27s_Weekly\" title=\"Harper's Weekly\">Harper's Weekly</a></i>, is considered the first important use of an <a href=\"https://wikipedia.org/wiki/Elephant\" title=\"Elephant\">elephant</a> as a symbol for the <a href=\"https://wikipedia.org/wiki/Republican_Party_(United_States)\" title=\"Republican Party (United States)\">United States Republican Party</a>.", "links": [{"title": "Cartoon", "link": "https://wikipedia.org/wiki/Cartoon"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Harper's Weekly", "link": "https://wikipedia.org/wiki/Harper%27s_Weekly"}, {"title": "Elephant", "link": "https://wikipedia.org/wiki/Elephant"}, {"title": "Republican Party (United States)", "link": "https://wikipedia.org/wiki/Republican_Party_(United_States)"}]}, {"year": "1881", "text": "Mapuche uprising of 1881: Mapuche rebels destroy the Chilean settlement of Nueva Imperial after defenders fled to the hills.", "html": "1881 - <a href=\"https://wikipedia.org/wiki/Mapuche_uprising_of_1881\" title=\"Mapuche uprising of 1881\">Mapuche uprising of 1881</a>: <a href=\"https://wikipedia.org/wiki/Mapuche\" title=\"Mapuche\">Mapuche</a> rebels destroy the Chilean settlement of <a href=\"https://wikipedia.org/wiki/Nueva_Imperial\" title=\"Nueva Imperial\">Nueva Imperial</a> after defenders fled to the hills.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Mapuche_uprising_of_1881\" title=\"Mapuche uprising of 1881\">Mapuche uprising of 1881</a>: <a href=\"https://wikipedia.org/wiki/Mapuche\" title=\"Mapuche\">Mapuche</a> rebels destroy the Chilean settlement of <a href=\"https://wikipedia.org/wiki/Nueva_Imperial\" title=\"Nueva Imperial\">Nueva Imperial</a> after defenders fled to the hills.", "links": [{"title": "Mapuche uprising of 1881", "link": "https://wikipedia.org/wiki/Mapuche_uprising_of_1881"}, {"title": "Mapuche", "link": "https://wikipedia.org/wiki/Mapuche"}, {"title": "Nueva Imperial", "link": "https://wikipedia.org/wiki/Nueva_Imperial"}]}, {"year": "1885", "text": "The completion of Canada's first transcontinental railway is symbolized by the Last Spike ceremony at Craigellachie, British Columbia.", "html": "1885 - The completion of Canada's first transcontinental railway is symbolized by the <a href=\"https://wikipedia.org/wiki/Last_Spike_(Canadian_Pacific_Railway)\" class=\"mw-redirect\" title=\"Last Spike (Canadian Pacific Railway)\">Last Spike</a> ceremony at <a href=\"https://wikipedia.org/wiki/Craigellachie,_British_Columbia\" title=\"Craigellachie, British Columbia\">Craigellachie, British Columbia</a>.", "no_year_html": "The completion of Canada's first transcontinental railway is symbolized by the <a href=\"https://wikipedia.org/wiki/Last_Spike_(Canadian_Pacific_Railway)\" class=\"mw-redirect\" title=\"Last Spike (Canadian Pacific Railway)\">Last Spike</a> ceremony at <a href=\"https://wikipedia.org/wiki/Craigellachie,_British_Columbia\" title=\"Craigellachie, British Columbia\">Craigellachie, British Columbia</a>.", "links": [{"title": "Last Spike (Canadian Pacific Railway)", "link": "https://wikipedia.org/wiki/Last_Spike_(Canadian_Pacific_Railway)"}, {"title": "<PERSON>ella<PERSON>e, British Columbia", "link": "https://wikipedia.org/wiki/Craigella<PERSON><PERSON>,_British_Columbia"}]}, {"year": "1893", "text": "Women's suffrage: Women in the U.S. state of Colorado are granted the right to vote, the second state to do so.", "html": "1893 - <a href=\"https://wikipedia.org/wiki/Women%27s_suffrage_in_the_United_States\" title=\"Women's suffrage in the United States\">Women's suffrage</a>: Women in the U.S. state of <a href=\"https://wikipedia.org/wiki/Colorado\" title=\"Colorado\">Colorado</a> are granted the right to vote, the second state to do so.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Women%27s_suffrage_in_the_United_States\" title=\"Women's suffrage in the United States\">Women's suffrage</a>: Women in the U.S. state of <a href=\"https://wikipedia.org/wiki/Colorado\" title=\"Colorado\">Colorado</a> are granted the right to vote, the second state to do so.", "links": [{"title": "Women's suffrage in the United States", "link": "https://wikipedia.org/wiki/Women%27s_suffrage_in_the_United_States"}, {"title": "Colorado", "link": "https://wikipedia.org/wiki/Colorado"}]}, {"year": "1893", "text": "An anarchist throws two bombs in Barcelona's Liceu opera house, killing 20", "html": "1893 - An anarchist <a href=\"https://wikipedia.org/wiki/Liceu_bombing\" title=\"Liceu bombing\">throws two bombs in Barcelona's Liceu opera house</a>, killing 20", "no_year_html": "An anarchist <a href=\"https://wikipedia.org/wiki/Liceu_bombing\" title=\"Liceu bombing\">throws two bombs in Barcelona's Liceu opera house</a>, killing 20", "links": [{"title": "Liceu bombing", "link": "https://wikipedia.org/wiki/Liceu_bombing"}]}, {"year": "1900", "text": "Second Boer War: The Battle of Leliefontein takes place, during which the Royal Canadian Dragoons win three Victoria Crosses.", "html": "1900 - <a href=\"https://wikipedia.org/wiki/Second_Boer_War\" title=\"Second Boer War\">Second Boer War</a>: The <a href=\"https://wikipedia.org/wiki/Battle_of_Leliefontein\" title=\"Battle of Leliefontein\">Battle of <span title=\"Afrikaans-language text\"><span lang=\"af\" style=\"font-style: normal;\">Leliefontein</span></span></a> takes place, during which the <a href=\"https://wikipedia.org/wiki/Royal_Canadian_Dragoons\" title=\"Royal Canadian Dragoons\">Royal Canadian Dragoons</a> win three <a href=\"https://wikipedia.org/wiki/Victoria_Cross\" title=\"Victoria Cross\">Victoria Crosses</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Second_Boer_War\" title=\"Second Boer War\">Second Boer War</a>: The <a href=\"https://wikipedia.org/wiki/Battle_of_Leliefontein\" title=\"Battle of Leliefontein\">Battle of <span title=\"Afrikaans-language text\"><span lang=\"af\" style=\"font-style: normal;\">Leliefontein</span></span></a> takes place, during which the <a href=\"https://wikipedia.org/wiki/Royal_Canadian_Dragoons\" title=\"Royal Canadian Dragoons\">Royal Canadian Dragoons</a> win three <a href=\"https://wikipedia.org/wiki/Victoria_Cross\" title=\"Victoria Cross\">Victoria Crosses</a>.", "links": [{"title": "Second Boer War", "link": "https://wikipedia.org/wiki/Second_Boer_War"}, {"title": "Battle of Leliefontein", "link": "https://wikipedia.org/wiki/Battle_of_Leliefontein"}, {"title": "Royal Canadian Dragoons", "link": "https://wikipedia.org/wiki/Royal_Canadian_Dragoons"}, {"title": "Victoria Cross", "link": "https://wikipedia.org/wiki/Victoria_Cross"}]}, {"year": "1907", "text": "<PERSON><PERSON><PERSON> saves the entire town of Nacozari de García by driving a burning train full of dynamite six kilometres (3.7 miles) away before it can explode.", "html": "1907 - <span title=\"Spanish-language text\"><span lang=\"es\" style=\"font-style: normal;\"><a href=\"https://wikipedia.org/wiki/Jes%C3%BAs_Garc%C3%ADa\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a></span></span> saves the entire town of <span title=\"Spanish-language text\"><span lang=\"es\" style=\"font-style: normal;\"><a href=\"https://wikipedia.org/wiki/Nacozari_de_Garc%C3%ADa\" title=\"Nacozari de <PERSON>\">Na<PERSON><PERSON><PERSON> <PERSON></a></span></span> by driving a burning train full of <a href=\"https://wikipedia.org/wiki/Dynamite\" title=\"Dynamite\">dynamite</a> six kilometres (3.7 miles) away before it can explode.", "no_year_html": "<span title=\"Spanish-language text\"><span lang=\"es\" style=\"font-style: normal;\"><a href=\"https://wikipedia.org/wiki/Jes%C3%BAs_Garc%C3%ADa\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a></span></span> saves the entire town of <span title=\"Spanish-language text\"><span lang=\"es\" style=\"font-style: normal;\"><a href=\"https://wikipedia.org/wiki/Nacozari_de_Garc%C3%ADa\" title=\"Nacozari de <PERSON>\">Naco<PERSON><PERSON> <PERSON></a></span></span> by driving a burning train full of <a href=\"https://wikipedia.org/wiki/Dynamite\" title=\"Dynamite\">dynamite</a> six kilometres (3.7 miles) away before it can explode.", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Jes%C3%BAs_Garc%C3%ADa"}, {"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Nacozari_de_Garc%C3%ADa"}, {"title": "Dynamite", "link": "https://wikipedia.org/wiki/Dynamite"}]}, {"year": "1910", "text": "The first air freight shipment (from Dayton, Ohio, to Columbus, Ohio) is undertaken by the Wright brothers and department store owner <PERSON>.", "html": "1910 - The first <a href=\"https://wikipedia.org/wiki/Air_freight\" class=\"mw-redirect\" title=\"Air freight\">air freight</a> shipment (from <a href=\"https://wikipedia.org/wiki/Dayton,_Ohio\" title=\"Dayton, Ohio\">Dayton, Ohio</a>, to <a href=\"https://wikipedia.org/wiki/Columbus,_Ohio\" title=\"Columbus, Ohio\">Columbus, Ohio</a>) is undertaken by the <a href=\"https://wikipedia.org/wiki/<PERSON>_brothers\" title=\"<PERSON> brothers\"><PERSON> brothers</a> and department store owner <PERSON>.", "no_year_html": "The first <a href=\"https://wikipedia.org/wiki/Air_freight\" class=\"mw-redirect\" title=\"Air freight\">air freight</a> shipment (from <a href=\"https://wikipedia.org/wiki/Dayton,_Ohio\" title=\"Dayton, Ohio\">Dayton, Ohio</a>, to <a href=\"https://wikipedia.org/wiki/Columbus,_Ohio\" title=\"Columbus, Ohio\">Columbus, Ohio</a>) is undertaken by the <a href=\"https://wikipedia.org/wiki/<PERSON>_brothers\" title=\"<PERSON> brothers\"><PERSON> brothers</a> and department store owner <PERSON>.", "links": [{"title": "Air freight", "link": "https://wikipedia.org/wiki/Air_freight"}, {"title": "Dayton, Ohio", "link": "https://wikipedia.org/wiki/Dayton,_Ohio"}, {"title": "Columbus, Ohio", "link": "https://wikipedia.org/wiki/Columbus,_Ohio"}, {"title": "<PERSON> brothers", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1912", "text": "The Deutsche Opernhaus (now Deutsche Oper Berlin) opens in the Berlin neighborhood of Charlottenburg, with a production of <PERSON>'s Fidelio.", "html": "1912 - The <span title=\"German-language text\"><span lang=\"de\" style=\"font-style: normal;\">Deutsche Opernhaus</span></span> (now <span title=\"German-language text\"><span lang=\"de\" style=\"font-style: normal;\"><a href=\"https://wikipedia.org/wiki/Deutsche_Oper_Berlin\" title=\"Deutsche Oper Berlin\">Deutsche Oper Berlin</a></span></span>) opens in the Berlin neighborhood of <a href=\"https://wikipedia.org/wiki/Charlottenburg\" title=\"Charlottenburg\">Charlottenburg</a>, with a production of <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON> van <PERSON>\">Beethoven</a>'s <i><a href=\"https://wikipedia.org/wiki/Fidelio\" title=\"Fidelio\">Fidelio</a></i>.", "no_year_html": "The <span title=\"German-language text\"><span lang=\"de\" style=\"font-style: normal;\">Deutsche Opernhaus</span></span> (now <span title=\"German-language text\"><span lang=\"de\" style=\"font-style: normal;\"><a href=\"https://wikipedia.org/wiki/Deutsche_Oper_Berlin\" title=\"Deutsche Oper Berlin\">Deutsche Oper Berlin</a></span></span>) opens in the Berlin neighborhood of <a href=\"https://wikipedia.org/wiki/Charlottenburg\" title=\"Charlottenburg\">Charlottenburg</a>, with a production of <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON> van <PERSON>\">Beethoven</a>'s <i><a href=\"https://wikipedia.org/wiki/Fidelio\" title=\"Fidelio\">Fidelio</a></i>.", "links": [{"title": "Deutsche Oper Berlin", "link": "https://wikipedia.org/wiki/Deutsche_Oper_Berlin"}, {"title": "Charlottenburg", "link": "https://wikipedia.org/wiki/Charlottenburg"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Fidelio"}]}, {"year": "1913", "text": "The first day of the Great Lakes Storm of 1913, a massive blizzard that ultimately killed 250 and caused over $5 million (about $159,243,000 in 2024 dollars) damage. Winds reach hurricane force on this date.", "html": "1913 - The first day of the <a href=\"https://wikipedia.org/wiki/Great_Lakes_Storm_of_1913\" title=\"Great Lakes Storm of 1913\">Great Lakes Storm of 1913</a>, a massive blizzard that ultimately killed 250 and caused over $5 million (about $159,243,000 in 2024 dollars) damage. Winds reach hurricane force on this date.", "no_year_html": "The first day of the <a href=\"https://wikipedia.org/wiki/Great_Lakes_Storm_of_1913\" title=\"Great Lakes Storm of 1913\">Great Lakes Storm of 1913</a>, a massive blizzard that ultimately killed 250 and caused over $5 million (about $159,243,000 in 2024 dollars) damage. Winds reach hurricane force on this date.", "links": [{"title": "Great Lakes Storm of 1913", "link": "https://wikipedia.org/wiki/Great_Lakes_Storm_of_1913"}]}, {"year": "1914", "text": "The German colony of Kiaochow Bay and its centre at Tsingtao are captured by Japanese forces.", "html": "1914 - The <a href=\"https://wikipedia.org/wiki/German_Empire\" title=\"German Empire\">German</a> colony of <a href=\"https://wikipedia.org/wiki/Jiaozhou_Bay\" title=\"Jiaozhou Bay\">Kiaochow Bay</a> and its centre at <a href=\"https://wikipedia.org/wiki/Qingdao\" title=\"Qingdao\">Tsingtao</a> are captured by Japanese forces.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/German_Empire\" title=\"German Empire\">German</a> colony of <a href=\"https://wikipedia.org/wiki/Jiaozhou_Bay\" title=\"Jiaozhou Bay\">Kiaochow Bay</a> and its centre at <a href=\"https://wikipedia.org/wiki/Qingdao\" title=\"Qingdao\">Tsingtao</a> are captured by Japanese forces.", "links": [{"title": "German Empire", "link": "https://wikipedia.org/wiki/German_Empire"}, {"title": "Jiaozhou Bay", "link": "https://wikipedia.org/wiki/Jiaozhou_Bay"}, {"title": "Qingdao", "link": "https://wikipedia.org/wiki/Qingdao"}]}, {"year": "1916", "text": "<PERSON><PERSON> is the first woman elected to the United States Congress.", "html": "1916 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a> is the first woman <a href=\"https://wikipedia.org/wiki/1916_United_States_House_of_Representatives_elections\" title=\"1916 United States House of Representatives elections\">elected</a> to the <a href=\"https://wikipedia.org/wiki/United_States_Congress\" title=\"United States Congress\">United States Congress</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a> is the first woman <a href=\"https://wikipedia.org/wiki/1916_United_States_House_of_Representatives_elections\" title=\"1916 United States House of Representatives elections\">elected</a> to the <a href=\"https://wikipedia.org/wiki/United_States_Congress\" title=\"United States Congress\">United States Congress</a>.", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}, {"title": "1916 United States House of Representatives elections", "link": "https://wikipedia.org/wiki/1916_United_States_House_of_Representatives_elections"}, {"title": "United States Congress", "link": "https://wikipedia.org/wiki/United_States_Congress"}]}, {"year": "1916", "text": "<PERSON> is reelected as President of the United States.", "html": "1916 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> is <a href=\"https://wikipedia.org/wiki/1916_United_States_presidential_election\" title=\"1916 United States presidential election\">reelected</a> as <a href=\"https://wikipedia.org/wiki/President_of_the_United_States\" title=\"President of the United States\">President of the United States</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> is <a href=\"https://wikipedia.org/wiki/1916_United_States_presidential_election\" title=\"1916 United States presidential election\">reelected</a> as <a href=\"https://wikipedia.org/wiki/President_of_the_United_States\" title=\"President of the United States\">President of the United States</a>.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}, {"title": "1916 United States presidential election", "link": "https://wikipedia.org/wiki/1916_United_States_presidential_election"}, {"title": "President of the United States", "link": "https://wikipedia.org/wiki/President_of_the_United_States"}]}, {"year": "1916", "text": "Boston Elevated Railway Company's streetcar No. 393 smashes through the warning gates of the open Summer Street drawbridge in Boston, Massachusetts, plunging into the frigid waters of Fort Point Channel, killing 46 people.", "html": "1916 - <a href=\"https://wikipedia.org/wiki/Boston_Elevated_Railway\" title=\"Boston Elevated Railway\">Boston Elevated Railway Company's</a> streetcar No. 393 <a href=\"https://wikipedia.org/wiki/Summer_Street_Bridge_disaster\" title=\"Summer Street Bridge disaster\">smashes</a> through the warning gates of the open Summer Street drawbridge in <a href=\"https://wikipedia.org/wiki/Boston\" title=\"Boston\">Boston, Massachusetts</a>, plunging into the frigid waters of Fort Point Channel, killing 46 people.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Boston_Elevated_Railway\" title=\"Boston Elevated Railway\">Boston Elevated Railway Company's</a> streetcar No. 393 <a href=\"https://wikipedia.org/wiki/Summer_Street_Bridge_disaster\" title=\"Summer Street Bridge disaster\">smashes</a> through the warning gates of the open Summer Street drawbridge in <a href=\"https://wikipedia.org/wiki/Boston\" title=\"Boston\">Boston, Massachusetts</a>, plunging into the frigid waters of Fort Point Channel, killing 46 people.", "links": [{"title": "Boston Elevated Railway", "link": "https://wikipedia.org/wiki/Boston_Elevated_Railway"}, {"title": "Summer Street Bridge disaster", "link": "https://wikipedia.org/wiki/Summer_Street_Bridge_disaster"}, {"title": "Boston", "link": "https://wikipedia.org/wiki/Boston"}]}, {"year": "1917", "text": "The October Revolution, which gets its name from the Julian calendar date of 25 October, occurs, according to the Gregorian calendar; on this date, the Bolsheviks storm the Winter Palace.", "html": "1917 - The <a href=\"https://wikipedia.org/wiki/October_Revolution\" title=\"October Revolution\">October Revolution</a>, which gets its name from the Julian calendar date of <a href=\"https://wikipedia.org/wiki/25_October\" class=\"mw-redirect\" title=\"25 October\">25 October</a>, occurs, according to the Gregorian calendar; on this date, the Bolsheviks storm the <a href=\"https://wikipedia.org/wiki/Winter_Palace\" title=\"Winter Palace\">Winter Palace</a>.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/October_Revolution\" title=\"October Revolution\">October Revolution</a>, which gets its name from the Julian calendar date of <a href=\"https://wikipedia.org/wiki/25_October\" class=\"mw-redirect\" title=\"25 October\">25 October</a>, occurs, according to the Gregorian calendar; on this date, the Bolsheviks storm the <a href=\"https://wikipedia.org/wiki/Winter_Palace\" title=\"Winter Palace\">Winter Palace</a>.", "links": [{"title": "October Revolution", "link": "https://wikipedia.org/wiki/October_Revolution"}, {"title": "25 October", "link": "https://wikipedia.org/wiki/25_October"}, {"title": "Winter Palace", "link": "https://wikipedia.org/wiki/Winter_Palace"}]}, {"year": "1917", "text": "World War I: The Third Battle of Gaza ends, with British forces capturing Gaza from the Ottoman Empire.", "html": "1917 - <a href=\"https://wikipedia.org/wiki/World_War_I\" title=\"World War I\">World War I</a>: The <a href=\"https://wikipedia.org/wiki/Third_Battle_of_Gaza\" title=\"Third Battle of Gaza\">Third Battle of Gaza</a> ends, with British forces capturing <a href=\"https://wikipedia.org/wiki/Gaza_City\" title=\"Gaza City\">Gaza</a> from the <a href=\"https://wikipedia.org/wiki/Ottoman_Empire\" title=\"Ottoman Empire\">Ottoman Empire</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/World_War_I\" title=\"World War I\">World War I</a>: The <a href=\"https://wikipedia.org/wiki/Third_Battle_of_Gaza\" title=\"Third Battle of Gaza\">Third Battle of Gaza</a> ends, with British forces capturing <a href=\"https://wikipedia.org/wiki/Gaza_City\" title=\"Gaza City\">Gaza</a> from the <a href=\"https://wikipedia.org/wiki/Ottoman_Empire\" title=\"Ottoman Empire\">Ottoman Empire</a>.", "links": [{"title": "World War I", "link": "https://wikipedia.org/wiki/World_War_I"}, {"title": "Third Battle of Gaza", "link": "https://wikipedia.org/wiki/Third_Battle_of_Gaza"}, {"title": "Gaza City", "link": "https://wikipedia.org/wiki/Gaza_City"}, {"title": "Ottoman Empire", "link": "https://wikipedia.org/wiki/Ottoman_Empire"}]}, {"year": "1918", "text": "The 1918 influenza epidemic spreads to Western Samoa, killing 7,542 (about 20% of the population) by the end of the year.", "html": "1918 - The <a href=\"https://wikipedia.org/wiki/1918_influenza_epidemic\" class=\"mw-redirect\" title=\"1918 influenza epidemic\">1918 influenza epidemic</a> spreads to <a href=\"https://wikipedia.org/wiki/Western_Samoa\" class=\"mw-redirect\" title=\"Western Samoa\">Western Samoa</a>, killing 7,542 (about 20% of the population) by the end of the year.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/1918_influenza_epidemic\" class=\"mw-redirect\" title=\"1918 influenza epidemic\">1918 influenza epidemic</a> spreads to <a href=\"https://wikipedia.org/wiki/Western_Samoa\" class=\"mw-redirect\" title=\"Western Samoa\">Western Samoa</a>, killing 7,542 (about 20% of the population) by the end of the year.", "links": [{"title": "1918 influenza epidemic", "link": "https://wikipedia.org/wiki/1918_influenza_epidemic"}, {"title": "Western Samoa", "link": "https://wikipedia.org/wiki/Western_Samoa"}]}, {"year": "1918", "text": "<PERSON> overthrows the W<PERSON>lsbach dynasty in the Kingdom of Bavaria.", "html": "1918 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> overthrows the <a href=\"https://wikipedia.org/wiki/Wittelsbach_dynasty\" class=\"mw-redirect\" title=\"Wittelsbach dynasty\">Wittelsbach dynasty</a> in the <a href=\"https://wikipedia.org/wiki/Kingdom_of_Bavaria\" title=\"Kingdom of Bavaria\">Kingdom of Bavaria</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> overthrows the <a href=\"https://wikipedia.org/wiki/Wittelsbach_dynasty\" class=\"mw-redirect\" title=\"Wittelsbach dynasty\">Wittelsbach dynasty</a> in the <a href=\"https://wikipedia.org/wiki/Kingdom_of_Bavaria\" title=\"Kingdom of Bavaria\">Kingdom of Bavaria</a>.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Wittelsbach dynasty", "link": "https://wikipedia.org/wiki/W<PERSON>lsbach_dynasty"}, {"title": "Kingdom of Bavaria", "link": "https://wikipedia.org/wiki/Kingdom_of_Bavaria"}]}, {"year": "1919", "text": "The first Palmer Raid is conducted on the second anniversary of the Russian Revolution. Over 10,000 suspected communists and anarchists are arrested in 23 U.S. cities.", "html": "1919 - The first <a href=\"https://wikipedia.org/wiki/Palmer_Raids\" title=\"Palmer Raids\">Palmer Raid</a> is conducted on the second anniversary of the <a href=\"https://wikipedia.org/wiki/Russian_Revolution\" title=\"Russian Revolution\">Russian Revolution</a>. Over 10,000 suspected communists and anarchists are arrested in 23 U.S. cities.", "no_year_html": "The first <a href=\"https://wikipedia.org/wiki/<PERSON>_Rai<PERSON>\" title=\"Palmer Raids\">Palmer Raid</a> is conducted on the second anniversary of the <a href=\"https://wikipedia.org/wiki/Russian_Revolution\" title=\"Russian Revolution\">Russian Revolution</a>. Over 10,000 suspected communists and anarchists are arrested in 23 U.S. cities.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Palmer_Raids"}, {"title": "Russian Revolution", "link": "https://wikipedia.org/wiki/Russian_Revolution"}]}, {"year": "1920", "text": "Patriarch <PERSON><PERSON><PERSON> of Moscow issues a decree that leads to the formation of the Russian Orthodox Church Outside Russia.", "html": "1920 - <a href=\"https://wikipedia.org/wiki/Patriarch_<PERSON><PERSON><PERSON>_of_Moscow\" title=\"Patriarch <PERSON><PERSON><PERSON> of Moscow\">Patriarch <PERSON><PERSON><PERSON> of Moscow</a> issues a decree that leads to the formation of the <a href=\"https://wikipedia.org/wiki/Russian_Orthodox_Church_Outside_Russia\" class=\"mw-redirect\" title=\"Russian Orthodox Church Outside Russia\">Russian Orthodox Church Outside Russia</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Patriarch_<PERSON><PERSON><PERSON>_of_Moscow\" title=\"Patriarch <PERSON><PERSON><PERSON> of Moscow\">Patriarch <PERSON><PERSON><PERSON> of Moscow</a> issues a decree that leads to the formation of the <a href=\"https://wikipedia.org/wiki/Russian_Orthodox_Church_Outside_Russia\" class=\"mw-redirect\" title=\"Russian Orthodox Church Outside Russia\">Russian Orthodox Church Outside Russia</a>.", "links": [{"title": "Patriarch <PERSON><PERSON><PERSON> of Moscow", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON><PERSON>_of_Moscow"}, {"title": "Russian Orthodox Church Outside Russia", "link": "https://wikipedia.org/wiki/Russian_Orthodox_Church_Outside_Russia"}]}, {"year": "1929", "text": "In New York City, the Museum of Modern Art opens to the public.", "html": "1929 - In New York City, the <a href=\"https://wikipedia.org/wiki/Museum_of_Modern_Art\" title=\"Museum of Modern Art\">Museum of Modern Art</a> opens to the public.", "no_year_html": "In New York City, the <a href=\"https://wikipedia.org/wiki/Museum_of_Modern_Art\" title=\"Museum of Modern Art\">Museum of Modern Art</a> opens to the public.", "links": [{"title": "Museum of Modern Art", "link": "https://wikipedia.org/wiki/Museum_of_Modern_Art"}]}, {"year": "1931", "text": "The Chinese Soviet Republic is proclaimed on the anniversary of the October Revolution.", "html": "1931 - The <a href=\"https://wikipedia.org/wiki/Chinese_Soviet_Republic\" title=\"Chinese Soviet Republic\">Chinese Soviet Republic</a> is proclaimed on the anniversary of the <a href=\"https://wikipedia.org/wiki/October_Revolution\" title=\"October Revolution\">October Revolution</a>.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Chinese_Soviet_Republic\" title=\"Chinese Soviet Republic\">Chinese Soviet Republic</a> is proclaimed on the anniversary of the <a href=\"https://wikipedia.org/wiki/October_Revolution\" title=\"October Revolution\">October Revolution</a>.", "links": [{"title": "Chinese Soviet Republic", "link": "https://wikipedia.org/wiki/Chinese_Soviet_Republic"}, {"title": "October Revolution", "link": "https://wikipedia.org/wiki/October_Revolution"}]}, {"year": "1933", "text": "<PERSON><PERSON><PERSON> is elected the 99th mayor of New York City.", "html": "1933 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_H._La_Guardia\" class=\"mw-redirect\" title=\"<PERSON>orello H. La Guardia\"><PERSON><PERSON><PERSON> H<PERSON> La Guardia</a> is elected the 99th <a href=\"https://wikipedia.org/wiki/Mayor_of_New_York_City\" title=\"Mayor of New York City\">mayor of New York City</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_H._La_Guardia\" class=\"mw-redirect\" title=\"<PERSON>orello H. La Guardia\"><PERSON><PERSON><PERSON> H<PERSON> La Guardia</a> is elected the 99th <a href=\"https://wikipedia.org/wiki/Mayor_of_New_York_City\" title=\"Mayor of New York City\">mayor of New York City</a>.", "links": [{"title": "Fi<PERSON>llo H. La Guardia", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>._La_Guardia"}, {"title": "Mayor of New York City", "link": "https://wikipedia.org/wiki/Mayor_of_New_York_City"}]}, {"year": "1936", "text": "Spanish Civil War: The Madrid Defense Council is formed to coordinate the Defense of Madrid against nationalist forces.", "html": "1936 - <a href=\"https://wikipedia.org/wiki/Spanish_Civil_War\" title=\"Spanish Civil War\">Spanish Civil War</a>: The <a href=\"https://wikipedia.org/wiki/Madrid_Defense_Council\" title=\"Madrid Defense Council\">Madrid Defense Council</a> is formed to coordinate the <a href=\"https://wikipedia.org/wiki/Siege_of_Madrid\" title=\"Siege of Madrid\">Defense of Madrid</a> against <a href=\"https://wikipedia.org/wiki/Nationalist_faction_(Spanish_Civil_War)\" title=\"Nationalist faction (Spanish Civil War)\">nationalist forces</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Spanish_Civil_War\" title=\"Spanish Civil War\">Spanish Civil War</a>: The <a href=\"https://wikipedia.org/wiki/Madrid_Defense_Council\" title=\"Madrid Defense Council\">Madrid Defense Council</a> is formed to coordinate the <a href=\"https://wikipedia.org/wiki/Siege_of_Madrid\" title=\"Siege of Madrid\">Defense of Madrid</a> against <a href=\"https://wikipedia.org/wiki/Nationalist_faction_(Spanish_Civil_War)\" title=\"Nationalist faction (Spanish Civil War)\">nationalist forces</a>.", "links": [{"title": "Spanish Civil War", "link": "https://wikipedia.org/wiki/Spanish_Civil_War"}, {"title": "Madrid Defense Council", "link": "https://wikipedia.org/wiki/Madrid_Defense_Council"}, {"title": "Siege of Madrid", "link": "https://wikipedia.org/wiki/Siege_of_Madrid"}, {"title": "Nationalist faction (Spanish Civil War)", "link": "https://wikipedia.org/wiki/Nationalist_faction_(Spanish_Civil_War)"}]}, {"year": "1940", "text": "In Tacoma, Washington, the original Tacoma Narrows Bridge collapses in a windstorm, a mere four months after the bridge's completion.", "html": "1940 - In <a href=\"https://wikipedia.org/wiki/Tacoma,_Washington\" title=\"Tacoma, Washington\">Tacoma, Washington</a>, the <a href=\"https://wikipedia.org/wiki/Tacoma_Narrows_Bridge_(1940)\" title=\"Tacoma Narrows Bridge (1940)\">original Tacoma Narrows Bridge</a> collapses in a <a href=\"https://wikipedia.org/wiki/Windstorm\" class=\"mw-redirect\" title=\"Windstorm\">windstorm</a>, a mere four months after the bridge's completion.", "no_year_html": "In <a href=\"https://wikipedia.org/wiki/Tacoma,_Washington\" title=\"Tacoma, Washington\">Tacoma, Washington</a>, the <a href=\"https://wikipedia.org/wiki/Tacoma_Narrows_Bridge_(1940)\" title=\"Tacoma Narrows Bridge (1940)\">original Tacoma Narrows Bridge</a> collapses in a <a href=\"https://wikipedia.org/wiki/Windstorm\" class=\"mw-redirect\" title=\"Windstorm\">windstorm</a>, a mere four months after the bridge's completion.", "links": [{"title": "Tacoma, Washington", "link": "https://wikipedia.org/wiki/Tacoma,_Washington"}, {"title": "Tacoma Narrows Bridge (1940)", "link": "https://wikipedia.org/wiki/Tacoma_Narrows_Bridge_(1940)"}, {"title": "Windstorm", "link": "https://wikipedia.org/wiki/Windstorm"}]}, {"year": "1941", "text": "World War II: Soviet hospital ship Armenia is sunk by German planes while evacuating refugees and wounded military and staff of several Crimean hospitals. It is estimated that over 5,000 people died in the sinking.", "html": "1941 - <a href=\"https://wikipedia.org/wiki/World_War_II\" title=\"World War II\">World War II</a>: Soviet <a href=\"https://wikipedia.org/wiki/Hospital_ship\" title=\"Hospital ship\">hospital ship</a> <i><a href=\"https://wikipedia.org/wiki/Armenia_(Soviet_hospital_ship)\" class=\"mw-redirect\" title=\"Armenia (Soviet hospital ship)\">Armenia</a></i> is sunk by German planes while evacuating refugees and wounded military and staff of several <a href=\"https://wikipedia.org/wiki/Crimea\" title=\"Crimea\">Crimean</a> hospitals. It is estimated that over 5,000 people died in the sinking.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/World_War_II\" title=\"World War II\">World War II</a>: Soviet <a href=\"https://wikipedia.org/wiki/Hospital_ship\" title=\"Hospital ship\">hospital ship</a> <i><a href=\"https://wikipedia.org/wiki/Armenia_(Soviet_hospital_ship)\" class=\"mw-redirect\" title=\"Armenia (Soviet hospital ship)\">Armenia</a></i> is sunk by German planes while evacuating refugees and wounded military and staff of several <a href=\"https://wikipedia.org/wiki/Crimea\" title=\"Crimea\">Crimean</a> hospitals. It is estimated that over 5,000 people died in the sinking.", "links": [{"title": "World War II", "link": "https://wikipedia.org/wiki/World_War_II"}, {"title": "Hospital ship", "link": "https://wikipedia.org/wiki/Hospital_ship"}, {"title": "Armenia (Soviet hospital ship)", "link": "https://wikipedia.org/wiki/Armenia_(Soviet_hospital_ship)"}, {"title": "Crimea", "link": "https://wikipedia.org/wiki/Crimea"}]}, {"year": "1944", "text": "Soviet spy <PERSON>, a half-Russian, half-German World War I veteran, is hanged by his Japanese captors along with 34 of his ring.", "html": "1944 - Soviet spy <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, a half-Russian, half-German World War I veteran, is hanged by his Japanese captors along with 34 of his ring.", "no_year_html": "Soviet spy <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, a half-Russian, half-German World War I veteran, is hanged by his Japanese captors along with 34 of his ring.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1944", "text": "<PERSON> is elected for a record fourth term as President of the United States.", "html": "1944 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> is <a href=\"https://wikipedia.org/wiki/1944_United_States_presidential_election\" title=\"1944 United States presidential election\">elected</a> for a record fourth term as President of the United States.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> is <a href=\"https://wikipedia.org/wiki/1944_United_States_presidential_election\" title=\"1944 United States presidential election\">elected</a> for a record fourth term as President of the United States.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "1944 United States presidential election", "link": "https://wikipedia.org/wiki/1944_United_States_presidential_election"}]}, {"year": "1949", "text": "The first oil was taken in Oil Rocks (Neft Daşları), the world's oldest offshore oil platform.", "html": "1949 - The first oil was taken in <a href=\"https://wikipedia.org/wiki/Neft_Da%C5%9Flar%C4%B1\" title=\"Neft Daşları\">Oil Rocks (Neft Daşları)</a>, the world's oldest offshore oil platform.", "no_year_html": "The first oil was taken in <a href=\"https://wikipedia.org/wiki/Neft_Da%C5%9Flar%C4%B1\" title=\"Neft Daşları\">Oil Rocks (Neft Daşları)</a>, the world's oldest offshore oil platform.", "links": [{"title": "Neft Daşları", "link": "https://wikipedia.org/wiki/Neft_Da%C5%9Flar%C4%B1"}]}, {"year": "1956", "text": "Suez Crisis: The United Nations General Assembly adopts a resolution calling for the United Kingdom, France and Israel to immediately withdraw their troops from Egypt.", "html": "1956 - <a href=\"https://wikipedia.org/wiki/Suez_Crisis\" title=\"Suez Crisis\">Suez Crisis</a>: The <a href=\"https://wikipedia.org/wiki/United_Nations_General_Assembly\" title=\"United Nations General Assembly\">United Nations General Assembly</a> adopts a resolution calling for the United Kingdom, France and <a href=\"https://wikipedia.org/wiki/Israel\" title=\"Israel\">Israel</a> to immediately withdraw their troops from <a href=\"https://wikipedia.org/wiki/Egypt\" title=\"Egypt\">Egypt</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Suez_Crisis\" title=\"Suez Crisis\">Suez Crisis</a>: The <a href=\"https://wikipedia.org/wiki/United_Nations_General_Assembly\" title=\"United Nations General Assembly\">United Nations General Assembly</a> adopts a resolution calling for the United Kingdom, France and <a href=\"https://wikipedia.org/wiki/Israel\" title=\"Israel\">Israel</a> to immediately withdraw their troops from <a href=\"https://wikipedia.org/wiki/Egypt\" title=\"Egypt\">Egypt</a>.", "links": [{"title": "Suez Crisis", "link": "https://wikipedia.org/wiki/Suez_Crisis"}, {"title": "United Nations General Assembly", "link": "https://wikipedia.org/wiki/United_Nations_General_Assembly"}, {"title": "Israel", "link": "https://wikipedia.org/wiki/Israel"}, {"title": "Egypt", "link": "https://wikipedia.org/wiki/Egypt"}]}, {"year": "1956", "text": "Hungarian Revolution: <PERSON><PERSON><PERSON> returns to Budapest in a Soviet armored convoy, officially taking office as the next Hungarian leader. By this point, most armed resistance has been defeated.", "html": "1956 - <a href=\"https://wikipedia.org/wiki/Hungarian_Revolution_of_1956\" title=\"Hungarian Revolution of 1956\">Hungarian Revolution</a>: <a href=\"https://wikipedia.org/wiki/J%C3%A1nos_K%C3%A1d%C3%A1r\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a> returns to Budapest in a Soviet armored convoy, officially taking office as the next Hungarian leader. By this point, most armed resistance has been defeated.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Hungarian_Revolution_of_1956\" title=\"Hungarian Revolution of 1956\">Hungarian Revolution</a>: <a href=\"https://wikipedia.org/wiki/J%C3%A1nos_K%C3%A1d%C3%A1r\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a> returns to Budapest in a Soviet armored convoy, officially taking office as the next Hungarian leader. By this point, most armed resistance has been defeated.", "links": [{"title": "Hungarian Revolution of 1956", "link": "https://wikipedia.org/wiki/Hungarian_Revolution_of_1956"}, {"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/J%C3%A1nos_K%C3%A1d%C3%A1r"}]}, {"year": "1957", "text": "Cold War: The Gaither Report calls for more American missiles and fallout shelters.", "html": "1957 - <a href=\"https://wikipedia.org/wiki/Cold_War\" title=\"Cold War\">Cold War</a>: The <a href=\"https://wikipedia.org/wiki/Gaither_Report\" title=\"G<PERSON>her Report\">Gaither Report</a> calls for more American missiles and <a href=\"https://wikipedia.org/wiki/Fallout_shelter\" title=\"Fallout shelter\">fallout shelters</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Cold_War\" title=\"Cold War\">Cold War</a>: The <a href=\"https://wikipedia.org/wiki/Gaither_Report\" title=\"Gaither Report\">Gaither Report</a> calls for more American missiles and <a href=\"https://wikipedia.org/wiki/Fallout_shelter\" title=\"Fallout shelter\">fallout shelters</a>.", "links": [{"title": "Cold War", "link": "https://wikipedia.org/wiki/Cold_War"}, {"title": "Gaither Report", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}, {"title": "Fallout shelter", "link": "https://wikipedia.org/wiki/Fallout_shelter"}]}, {"year": "1967", "text": "<PERSON> is elected as Mayor of Cleveland, Ohio, becoming the first African American mayor of a major American city.", "html": "1967 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> is elected as Mayor of <a href=\"https://wikipedia.org/wiki/Cleveland,_Ohio\" class=\"mw-redirect\" title=\"Cleveland, Ohio\">Cleveland, Ohio</a>, becoming the first <a href=\"https://wikipedia.org/wiki/African_American\" class=\"mw-redirect\" title=\"African American\">African American</a> mayor of a major American city.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> is elected as Mayor of <a href=\"https://wikipedia.org/wiki/Cleveland,_Ohio\" class=\"mw-redirect\" title=\"Cleveland, Ohio\">Cleveland, Ohio</a>, becoming the first <a href=\"https://wikipedia.org/wiki/African_American\" class=\"mw-redirect\" title=\"African American\">African American</a> mayor of a major American city.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Cleveland, Ohio", "link": "https://wikipedia.org/wiki/Cleveland,_Ohio"}, {"title": "African American", "link": "https://wikipedia.org/wiki/African_American"}]}, {"year": "1967", "text": "US President <PERSON> signs the Public Broadcasting Act of 1967, establishing the Corporation for Public Broadcasting.", "html": "1967 - US President <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> signs the <a href=\"https://wikipedia.org/wiki/Public_Broadcasting_Act_of_1967\" title=\"Public Broadcasting Act of 1967\">Public Broadcasting Act of 1967</a>, establishing the <a href=\"https://wikipedia.org/wiki/Corporation_for_Public_Broadcasting\" title=\"Corporation for Public Broadcasting\">Corporation for Public Broadcasting</a>.", "no_year_html": "US President <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> signs the <a href=\"https://wikipedia.org/wiki/Public_Broadcasting_Act_of_1967\" title=\"Public Broadcasting Act of 1967\">Public Broadcasting Act of 1967</a>, establishing the <a href=\"https://wikipedia.org/wiki/Corporation_for_Public_Broadcasting\" title=\"Corporation for Public Broadcasting\">Corporation for Public Broadcasting</a>.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>"}, {"title": "Public Broadcasting Act of 1967", "link": "https://wikipedia.org/wiki/Public_Broadcasting_Act_of_1967"}, {"title": "Corporation for Public Broadcasting", "link": "https://wikipedia.org/wiki/Corporation_for_Public_Broadcasting"}]}, {"year": "1972", "text": "United States presidential election: U.S. President <PERSON> is re-elected in the largest landslide victory at the time.", "html": "1972 - <a href=\"https://wikipedia.org/wiki/1972_United_States_presidential_election\" title=\"1972 United States presidential election\">United States presidential election</a>: U.S. President <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> is re-elected in the largest landslide victory at the time.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/1972_United_States_presidential_election\" title=\"1972 United States presidential election\">United States presidential election</a>: U.S. President <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>\" title=\"<PERSON>\"><PERSON></a> is re-elected in the largest landslide victory at the time.", "links": [{"title": "1972 United States presidential election", "link": "https://wikipedia.org/wiki/1972_United_States_presidential_election"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1973", "text": "The United States Congress overrides President <PERSON>'s veto of the War Powers Resolution, which limits presidential power to wage war without congressional approval.", "html": "1973 - The <a href=\"https://wikipedia.org/wiki/United_States_Congress\" title=\"United States Congress\">United States Congress</a> overrides President <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>'s veto of the <a href=\"https://wikipedia.org/wiki/War_Powers_Resolution\" title=\"War Powers Resolution\">War Powers Resolution</a>, which limits presidential power to wage war without congressional approval.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/United_States_Congress\" title=\"United States Congress\">United States Congress</a> overrides President <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>'s veto of the <a href=\"https://wikipedia.org/wiki/War_Powers_Resolution\" title=\"War Powers Resolution\">War Powers Resolution</a>, which limits presidential power to wage war without congressional approval.", "links": [{"title": "United States Congress", "link": "https://wikipedia.org/wiki/United_States_Congress"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "War Powers Resolution", "link": "https://wikipedia.org/wiki/War_Powers_Resolution"}]}, {"year": "1975", "text": "In Bangladesh, a joint force of people and soldiers takes part in an uprising led by Colonel <PERSON> that ousts and kills Brigadier <PERSON><PERSON><PERSON>, freeing the then house-arrested army chief and future president Major General <PERSON><PERSON><PERSON>.", "html": "1975 - In <a href=\"https://wikipedia.org/wiki/Bangladesh\" title=\"Bangladesh\">Bangladesh</a>, a joint force of people and soldiers takes part in an uprising led by Colonel <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> that ousts and kills Brigadier <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, freeing the then <a href=\"https://wikipedia.org/wiki/House_arrest\" title=\"House arrest\">house-arrested</a> <a href=\"https://wikipedia.org/wiki/Chief_of_Army_Staff_of_the_Bangladesh_Army\" class=\"mw-redirect\" title=\"Chief of Army Staff of the Bangladesh Army\">army chief</a> and future <a href=\"https://wikipedia.org/wiki/President_of_Bangladesh\" title=\"President of Bangladesh\">president</a> <a href=\"https://wikipedia.org/wiki/Major_General\" class=\"mw-redirect\" title=\"Major General\">Major General</a> <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>.", "no_year_html": "In <a href=\"https://wikipedia.org/wiki/Bangladesh\" title=\"Bangladesh\">Bangladesh</a>, a joint force of people and soldiers takes part in an uprising led by Colonel <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> that ousts and kills Brigadier <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, freeing the then <a href=\"https://wikipedia.org/wiki/House_arrest\" title=\"House arrest\">house-arrested</a> <a href=\"https://wikipedia.org/wiki/Chief_of_Army_Staff_of_the_Bangladesh_Army\" class=\"mw-redirect\" title=\"Chief of Army Staff of the Bangladesh Army\">army chief</a> and future <a href=\"https://wikipedia.org/wiki/President_of_Bangladesh\" title=\"President of Bangladesh\">president</a> <a href=\"https://wikipedia.org/wiki/Major_General\" class=\"mw-redirect\" title=\"Major General\">Major General</a> <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>.", "links": [{"title": "Bangladesh", "link": "https://wikipedia.org/wiki/Bangladesh"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}, {"title": "House arrest", "link": "https://wikipedia.org/wiki/House_arrest"}, {"title": "Chief of Army Staff of the Bangladesh Army", "link": "https://wikipedia.org/wiki/Chief_of_Army_Staff_of_the_Bangladesh_Army"}, {"title": "President of Bangladesh", "link": "https://wikipedia.org/wiki/President_of_Bangladesh"}, {"title": "Major General", "link": "https://wikipedia.org/wiki/Major_General"}, {"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1982", "text": "Colonel <PERSON><PERSON>, president of the military government of Upper Volta, is ousted from power in a coup d'état led by Colonel <PERSON>.", "html": "1982 - Colonel <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, president of the military government of Upper Volta, is ousted from power in a <a href=\"https://wikipedia.org/wiki/1982_Upper_Voltan_coup_d%27%C3%A9tat\" title=\"1982 Upper Voltan coup d'état\">coup d'état</a> led by Colonel <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_Som%C3%A9\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>.", "no_year_html": "Colonel <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, president of the military government of Upper Volta, is ousted from power in a <a href=\"https://wikipedia.org/wiki/1982_Upper_Voltan_coup_d%27%C3%A9tat\" title=\"1982 Upper Voltan coup d'état\">coup d'état</a> led by Colonel <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_Som%C3%A9\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>.", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}, {"title": "1982 Upper Voltan coup d'état", "link": "https://wikipedia.org/wiki/1982_Upper_Voltan_coup_d%27%C3%A9tat"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_Som%C3%A9"}]}, {"year": "1983", "text": "United States Senate bombing: A bomb explodes inside the United States Capitol. No one is injured, but an estimated $250,000 in damage is caused.", "html": "1983 - <a href=\"https://wikipedia.org/wiki/1983_United_States_Senate_bombing\" title=\"1983 United States Senate bombing\">United States Senate bombing</a>: A bomb explodes inside the <a href=\"https://wikipedia.org/wiki/United_States_Capitol\" title=\"United States Capitol\">United States Capitol</a>. No one is injured, but an estimated $250,000 in damage is caused.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/1983_United_States_Senate_bombing\" title=\"1983 United States Senate bombing\">United States Senate bombing</a>: A bomb explodes inside the <a href=\"https://wikipedia.org/wiki/United_States_Capitol\" title=\"United States Capitol\">United States Capitol</a>. No one is injured, but an estimated $250,000 in damage is caused.", "links": [{"title": "1983 United States Senate bombing", "link": "https://wikipedia.org/wiki/1983_United_States_Senate_bombing"}, {"title": "United States Capitol", "link": "https://wikipedia.org/wiki/United_States_Capitol"}]}, {"year": "1983", "text": "Cold War: The command post exercise Able Archer 83 begins, eventually leading to the Soviet Union to place air units in East Germany and Poland on alert, for fear that NATO was preparing for war", "html": "1983 - <a href=\"https://wikipedia.org/wiki/Cold_War\" title=\"Cold War\">Cold War</a>: The <a href=\"https://wikipedia.org/wiki/Command_post_exercise\" class=\"mw-redirect\" title=\"Command post exercise\">command post exercise</a> <a href=\"https://wikipedia.org/wiki/Able_Archer_83\" title=\"Able Archer 83\">Able Archer 83</a> begins, eventually leading to the <a href=\"https://wikipedia.org/wiki/Soviet_Union\" title=\"Soviet Union\">Soviet Union</a> to place air units in <a href=\"https://wikipedia.org/wiki/East_Germany\" title=\"East Germany\">East Germany</a> and <a href=\"https://wikipedia.org/wiki/Polish_People%27s_Republic\" title=\"Polish People's Republic\">Poland</a> on alert, for fear that <a href=\"https://wikipedia.org/wiki/NATO\" title=\"NATO\">NATO</a> was preparing for war", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Cold_War\" title=\"Cold War\">Cold War</a>: The <a href=\"https://wikipedia.org/wiki/Command_post_exercise\" class=\"mw-redirect\" title=\"Command post exercise\">command post exercise</a> <a href=\"https://wikipedia.org/wiki/Able_Archer_83\" title=\"Able Archer 83\">Able Archer 83</a> begins, eventually leading to the <a href=\"https://wikipedia.org/wiki/Soviet_Union\" title=\"Soviet Union\">Soviet Union</a> to place air units in <a href=\"https://wikipedia.org/wiki/East_Germany\" title=\"East Germany\">East Germany</a> and <a href=\"https://wikipedia.org/wiki/Polish_People%27s_Republic\" title=\"Polish People's Republic\">Poland</a> on alert, for fear that <a href=\"https://wikipedia.org/wiki/NATO\" title=\"NATO\">NATO</a> was preparing for war", "links": [{"title": "Cold War", "link": "https://wikipedia.org/wiki/Cold_War"}, {"title": "Command post exercise", "link": "https://wikipedia.org/wiki/Command_post_exercise"}, {"title": "<PERSON><PERSON> 83", "link": "https://wikipedia.org/wiki/Able_Archer_83"}, {"title": "Soviet Union", "link": "https://wikipedia.org/wiki/Soviet_Union"}, {"title": "East Germany", "link": "https://wikipedia.org/wiki/East_Germany"}, {"title": "Polish People's Republic", "link": "https://wikipedia.org/wiki/Polish_People%27s_Republic"}, {"title": "NATO", "link": "https://wikipedia.org/wiki/NATO"}]}, {"year": "1987", "text": "In Tunisia, president <PERSON><PERSON><PERSON> is overthrown and replaced by Prime Minister <PERSON><PERSON>.", "html": "1987 - In <a href=\"https://wikipedia.org/wiki/Tunisia\" title=\"Tunisia\">Tunisia</a>, president <a href=\"https://wikipedia.org/wiki/Habib_Bourguiba\" title=\"<PERSON><PERSON><PERSON> Bourguiba\"><PERSON><PERSON><PERSON></a> is overthrown and replaced by Prime Minister <a href=\"https://wikipedia.org/wiki/<PERSON>ine_El_Abidine_<PERSON>_<PERSON>\" title=\"Zine <PERSON> A<PERSON>ine <PERSON>\"><PERSON><PERSON></a>.", "no_year_html": "In <a href=\"https://wikipedia.org/wiki/Tunisia\" title=\"Tunisia\">Tunisia</a>, president <a href=\"https://wikipedia.org/wiki/Habib_Bourguiba\" title=\"<PERSON><PERSON><PERSON> Bourguiba\"><PERSON><PERSON><PERSON>urg<PERSON></a> is overthrown and replaced by Prime Minister <a href=\"https://wikipedia.org/wiki/Zine_El_Abidine_<PERSON>_<PERSON>\" title=\"Zine <PERSON>\"><PERSON><PERSON></a>.", "links": [{"title": "Tunisia", "link": "https://wikipedia.org/wiki/Tunisia"}, {"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Ha<PERSON>b_Bourguiba"}, {"title": "<PERSON>ine <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1987", "text": "The Mass Rapid Transit (MRT) system in Singapore opens for passenger service.", "html": "1987 - The <a href=\"https://wikipedia.org/wiki/Mass_Rapid_Transit_(Singapore)\" title=\"Mass Rapid Transit (Singapore)\">Mass Rapid Transit</a> (MRT) system in <a href=\"https://wikipedia.org/wiki/Singapore\" title=\"Singapore\">Singapore</a> opens for passenger service.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Mass_Rapid_Transit_(Singapore)\" title=\"Mass Rapid Transit (Singapore)\">Mass Rapid Transit</a> (MRT) system in <a href=\"https://wikipedia.org/wiki/Singapore\" title=\"Singapore\">Singapore</a> opens for passenger service.", "links": [{"title": "Mass Rapid Transit (Singapore)", "link": "https://wikipedia.org/wiki/Mass_Rapid_Transit_(Singapore)"}, {"title": "Singapore", "link": "https://wikipedia.org/wiki/Singapore"}]}, {"year": "1989", "text": "<PERSON> wins the governor's seat in Virginia, becoming the first elected African American governor in the United States.", "html": "1989 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> wins the governor's seat in <a href=\"https://wikipedia.org/wiki/Virginia\" title=\"Virginia\">Virginia</a>, becoming the first elected <a href=\"https://wikipedia.org/wiki/African_American\" class=\"mw-redirect\" title=\"African American\">African American</a> governor in the United States.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> wins the governor's seat in <a href=\"https://wikipedia.org/wiki/Virginia\" title=\"Virginia\">Virginia</a>, becoming the first elected <a href=\"https://wikipedia.org/wiki/African_American\" class=\"mw-redirect\" title=\"African American\">African American</a> governor in the United States.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Virginia", "link": "https://wikipedia.org/wiki/Virginia"}, {"title": "African American", "link": "https://wikipedia.org/wiki/African_American"}]}, {"year": "1989", "text": "<PERSON> becomes the first African American to be elected Mayor of New York City.", "html": "1989 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> becomes the first <a href=\"https://wikipedia.org/wiki/African_American\" class=\"mw-redirect\" title=\"African American\">African American</a> to be elected <a href=\"https://wikipedia.org/wiki/Mayor_of_New_York_City\" title=\"Mayor of New York City\">Mayor of New York City</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> becomes the first <a href=\"https://wikipedia.org/wiki/African_American\" class=\"mw-redirect\" title=\"African American\">African American</a> to be elected <a href=\"https://wikipedia.org/wiki/Mayor_of_New_York_City\" title=\"Mayor of New York City\">Mayor of New York City</a>.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}, {"title": "African American", "link": "https://wikipedia.org/wiki/African_American"}, {"title": "Mayor of New York City", "link": "https://wikipedia.org/wiki/Mayor_of_New_York_City"}]}, {"year": "1989", "text": "East German Prime Minister <PERSON><PERSON>, along with his entire cabinet, is forced to resign after huge anti-government protests.", "html": "1989 - <a href=\"https://wikipedia.org/wiki/East_Germany\" title=\"East Germany\">East German</a> Prime Minister <a href=\"https://wikipedia.org/wiki/Willi_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, along with his entire cabinet, is forced to resign after huge anti-government protests.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/East_Germany\" title=\"East Germany\">East German</a> Prime Minister <a href=\"https://wikipedia.org/wiki/Will<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, along with his entire cabinet, is forced to resign after huge anti-government protests.", "links": [{"title": "East Germany", "link": "https://wikipedia.org/wiki/East_Germany"}, {"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>i_<PERSON>h"}]}, {"year": "1990", "text": "<PERSON> becomes the first woman to be elected President of the Republic of Ireland.", "html": "1990 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> becomes the first woman to be elected <a href=\"https://wikipedia.org/wiki/President_of_Ireland\" title=\"President of Ireland\">President of the Republic of Ireland</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> becomes the first woman to be elected <a href=\"https://wikipedia.org/wiki/President_of_Ireland\" title=\"President of Ireland\">President of the Republic of Ireland</a>.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "President of Ireland", "link": "https://wikipedia.org/wiki/President_of_Ireland"}]}, {"year": "1991", "text": "<PERSON> announces that he is HIV-positive and retires from the NBA.", "html": "1991 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Johnson\"><PERSON></a> announces that he is <a href=\"https://wikipedia.org/wiki/HIV\" title=\"HIV\">HIV</a>-positive and retires from the <a href=\"https://wikipedia.org/wiki/National_Basketball_Association\" title=\"National Basketball Association\">NBA</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Johnson\"><PERSON></a> announces that he is <a href=\"https://wikipedia.org/wiki/HIV\" title=\"HIV\">HIV</a>-positive and retires from the <a href=\"https://wikipedia.org/wiki/National_Basketball_Association\" title=\"National Basketball Association\">NBA</a>.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_Johnson"}, {"title": "HIV", "link": "https://wikipedia.org/wiki/HIV"}, {"title": "National Basketball Association", "link": "https://wikipedia.org/wiki/National_Basketball_Association"}]}, {"year": "1994", "text": "WXYC, the student radio station of the University of North Carolina at Chapel Hill, launches the world's first internet radio broadcast.", "html": "1994 - <a href=\"https://wikipedia.org/wiki/WXYC\" title=\"WXYC\">WXYC</a>, the student radio station of the <a href=\"https://wikipedia.org/wiki/University_of_North_Carolina_at_Chapel_Hill\" title=\"University of North Carolina at Chapel Hill\">University of North Carolina at Chapel Hill</a>, launches the world's first <a href=\"https://wikipedia.org/wiki/Webcast\" title=\"Webcast\">internet radio broadcast</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/WXYC\" title=\"WXYC\">WXYC</a>, the student radio station of the <a href=\"https://wikipedia.org/wiki/University_of_North_Carolina_at_Chapel_Hill\" title=\"University of North Carolina at Chapel Hill\">University of North Carolina at Chapel Hill</a>, launches the world's first <a href=\"https://wikipedia.org/wiki/Webcast\" title=\"Webcast\">internet radio broadcast</a>.", "links": [{"title": "WXYC", "link": "https://wikipedia.org/wiki/WXYC"}, {"title": "University of North Carolina at Chapel Hill", "link": "https://wikipedia.org/wiki/University_of_North_Carolina_at_Chapel_Hill"}, {"title": "Webcast", "link": "https://wikipedia.org/wiki/Webcast"}]}, {"year": "1996", "text": "NASA launches the Mars Global Surveyor.", "html": "1996 - <a href=\"https://wikipedia.org/wiki/NASA\" title=\"NASA\">NASA</a> launches the <a href=\"https://wikipedia.org/wiki/Mars_Global_Surveyor\" title=\"Mars Global Surveyor\">Mars Global Surveyor</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/NASA\" title=\"NASA\">NASA</a> launches the <a href=\"https://wikipedia.org/wiki/Mars_Global_Surveyor\" title=\"Mars Global Surveyor\">Mars Global Surveyor</a>.", "links": [{"title": "NASA", "link": "https://wikipedia.org/wiki/NASA"}, {"title": "Mars Global Surveyor", "link": "https://wikipedia.org/wiki/Mars_Global_Surveyor"}]}, {"year": "1996", "text": "ADC Airlines Flight 086 crashes into the Lagos Lagoon in Epe, Lagos State, Nigeria, killing all 144 people on board.", "html": "1996 - <a href=\"https://wikipedia.org/wiki/ADC_Airlines_Flight_086\" title=\"ADC Airlines Flight 086\">ADC Airlines Flight 086</a> crashes into the <a href=\"https://wikipedia.org/wiki/Lagos_Lagoon\" title=\"Lagos Lagoon\">Lagos Lagoon</a> in <a href=\"https://wikipedia.org/wiki/Epe,_Lagos_State\" title=\"Epe, Lagos State\">Epe, Lagos State</a>, <a href=\"https://wikipedia.org/wiki/Nigeria\" title=\"Nigeria\">Nigeria</a>, killing all 144 people on board.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/ADC_Airlines_Flight_086\" title=\"ADC Airlines Flight 086\">ADC Airlines Flight 086</a> crashes into the <a href=\"https://wikipedia.org/wiki/Lagos_Lagoon\" title=\"Lagos Lagoon\">Lagos Lagoon</a> in <a href=\"https://wikipedia.org/wiki/Epe,_Lagos_State\" title=\"Epe, Lagos State\">Epe, Lagos State</a>, <a href=\"https://wikipedia.org/wiki/Nigeria\" title=\"Nigeria\">Nigeria</a>, killing all 144 people on board.", "links": [{"title": "ADC Airlines Flight 086", "link": "https://wikipedia.org/wiki/ADC_Airlines_Flight_086"}, {"title": "Lagos Lagoon", "link": "https://wikipedia.org/wiki/Lagos_Lagoon"}, {"title": "Epe, Lagos State", "link": "https://wikipedia.org/wiki/Epe,_Lagos_State"}, {"title": "Nigeria", "link": "https://wikipedia.org/wiki/Nigeria"}]}, {"year": "2000", "text": "The controversial US presidential election is later resolved in the <PERSON> v. Gore Supreme Court case, electing <PERSON> as the 43rd President of the United States.", "html": "2000 - The controversial <a href=\"https://wikipedia.org/wiki/2000_United_States_presidential_election\" title=\"2000 United States presidential election\">US presidential election</a> is later resolved in the <a href=\"https://wikipedia.org/wiki/<PERSON>_v<PERSON>_<PERSON>\" title=\"<PERSON> v<PERSON> <PERSON>\"><PERSON> v<PERSON></a> Supreme Court case, electing <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> as the <a href=\"https://wikipedia.org/wiki/Presidency_of_<PERSON>_<PERSON>_<PERSON>\" title=\"Presidency of <PERSON>\">43rd</a> <a href=\"https://wikipedia.org/wiki/President_of_the_United_States\" title=\"President of the United States\">President of the United States</a>.", "no_year_html": "The controversial <a href=\"https://wikipedia.org/wiki/2000_United_States_presidential_election\" title=\"2000 United States presidential election\">US presidential election</a> is later resolved in the <a href=\"https://wikipedia.org/wiki/<PERSON>_v<PERSON>_<PERSON>\" title=\"<PERSON> v<PERSON> <PERSON>\"><PERSON> v<PERSON></a> Supreme Court case, electing <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> as the <a href=\"https://wikipedia.org/wiki/Presidency_of_<PERSON>_<PERSON>_<PERSON>\" title=\"Presidency of <PERSON>\">43rd</a> <a href=\"https://wikipedia.org/wiki/President_of_the_United_States\" title=\"President of the United States\">President of the United States</a>.", "links": [{"title": "2000 United States presidential election", "link": "https://wikipedia.org/wiki/2000_United_States_presidential_election"}, {"title": "<PERSON> v. <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_v._<PERSON>"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>"}, {"title": "Presidency of <PERSON>", "link": "https://wikipedia.org/wiki/Presidency_of_<PERSON>_<PERSON><PERSON>_<PERSON>"}, {"title": "President of the United States", "link": "https://wikipedia.org/wiki/President_of_the_United_States"}]}, {"year": "2000", "text": "The U.S. Drug Enforcement Administration discovers one of the country's largest LSD labs inside a converted military missile silo in Wamego, Kansas.", "html": "2000 - The U.S. <a href=\"https://wikipedia.org/wiki/Drug_Enforcement_Administration\" title=\"Drug Enforcement Administration\">Drug Enforcement Administration</a> discovers one of the country's largest <a href=\"https://wikipedia.org/wiki/Lysergic_acid_diethylamide\" class=\"mw-redirect\" title=\"Lysergic acid diethylamide\">LSD</a> <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\">labs</a> inside a converted military <a href=\"https://wikipedia.org/wiki/Missile_silo\" class=\"mw-redirect\" title=\"Missile silo\">missile silo</a> in <a href=\"https://wikipedia.org/wiki/Wamego,_Kansas\" title=\"Wamego, Kansas\">Wamego, Kansas</a>.", "no_year_html": "The U.S. <a href=\"https://wikipedia.org/wiki/Drug_Enforcement_Administration\" title=\"Drug Enforcement Administration\">Drug Enforcement Administration</a> discovers one of the country's largest <a href=\"https://wikipedia.org/wiki/Lysergic_acid_diethylamide\" class=\"mw-redirect\" title=\"Lysergic acid diethylamide\">LSD</a> <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\">labs</a> inside a converted military <a href=\"https://wikipedia.org/wiki/Missile_silo\" class=\"mw-redirect\" title=\"Missile silo\">missile silo</a> in <a href=\"https://wikipedia.org/wiki/Wamego,_Kansas\" title=\"Wamego, Kansas\">Wamego, Kansas</a>.", "links": [{"title": "Drug Enforcement Administration", "link": "https://wikipedia.org/wiki/Drug_Enforcement_Administration"}, {"title": "Lysergic acid diethylamide", "link": "https://wikipedia.org/wiki/Lysergic_acid_diethylamide"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Missile silo", "link": "https://wikipedia.org/wiki/Missile_silo"}, {"title": "Wamego, Kansas", "link": "https://wikipedia.org/wiki/Wamego,_Kansas"}]}, {"year": "2004", "text": "Iraq War: The interim government of Iraq calls for a 60-day state of emergency as U.S. forces storm the insurgent stronghold of Fallujah.", "html": "2004 - <a href=\"https://wikipedia.org/wiki/Iraq_War\" title=\"Iraq War\">Iraq War</a>: The interim government of <a href=\"https://wikipedia.org/wiki/Iraq\" title=\"Iraq\">Iraq</a> calls for a 60-day <a href=\"https://wikipedia.org/wiki/State_of_emergency\" title=\"State of emergency\">state of emergency</a> as U.S. forces storm the insurgent stronghold of <a href=\"https://wikipedia.org/wiki/Fallujah\" title=\"Fallujah\">Fallujah</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Iraq_War\" title=\"Iraq War\">Iraq War</a>: The interim government of <a href=\"https://wikipedia.org/wiki/Iraq\" title=\"Iraq\">Iraq</a> calls for a 60-day <a href=\"https://wikipedia.org/wiki/State_of_emergency\" title=\"State of emergency\">state of emergency</a> as U.S. forces storm the insurgent stronghold of <a href=\"https://wikipedia.org/wiki/Fallujah\" title=\"Fallujah\">Fallujah</a>.", "links": [{"title": "Iraq War", "link": "https://wikipedia.org/wiki/Iraq_War"}, {"title": "Iraq", "link": "https://wikipedia.org/wiki/Iraq"}, {"title": "State of emergency", "link": "https://wikipedia.org/wiki/State_of_emergency"}, {"title": "Fallujah", "link": "https://wikipedia.org/wiki/Fallujah"}]}, {"year": "2007", "text": "The Jokela school shooting in Jokela, Tuusula, Finland, takes place, resulting in the death of nine people.", "html": "2007 - The <a href=\"https://wikipedia.org/wiki/Jokela_school_shooting\" title=\"Jokela school shooting\">Jokela school shooting</a> in <a href=\"https://wikipedia.org/wiki/Jokela\" title=\"Jokel<PERSON>\"><PERSON><PERSON><PERSON></a>, <a href=\"https://wikipedia.org/wiki/Tuusula\" title=\"Tuusula\">Tuusula</a>, Finland, takes place, resulting in the death of nine people.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Jokela_school_shooting\" title=\"Jokela school shooting\">Jokela school shooting</a> in <a href=\"https://wikipedia.org/wiki/Jokela\" title=\"Jokel<PERSON>\"><PERSON><PERSON><PERSON></a>, <a href=\"https://wikipedia.org/wiki/Tuusula\" title=\"Tuusula\">Tuusula</a>, Finland, takes place, resulting in the death of nine people.", "links": [{"title": "Jokela school shooting", "link": "https://wikipedia.org/wiki/Jokela_school_shooting"}, {"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>a"}, {"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Tuusula"}]}, {"year": "2012", "text": "An earthquake off the Pacific coast of Guatemala kills at least 52 people.", "html": "2012 - An <a href=\"https://wikipedia.org/wiki/2012_Guatemala_earthquake\" title=\"2012 Guatemala earthquake\">earthquake</a> off the Pacific coast of <a href=\"https://wikipedia.org/wiki/Guatemala\" title=\"Guatemala\">Guatemala</a> kills at least 52 people.", "no_year_html": "An <a href=\"https://wikipedia.org/wiki/2012_Guatemala_earthquake\" title=\"2012 Guatemala earthquake\">earthquake</a> off the Pacific coast of <a href=\"https://wikipedia.org/wiki/Guatemala\" title=\"Guatemala\">Guatemala</a> kills at least 52 people.", "links": [{"title": "2012 Guatemala earthquake", "link": "https://wikipedia.org/wiki/2012_Guatemala_earthquake"}, {"title": "Guatemala", "link": "https://wikipedia.org/wiki/Guatemala"}]}, {"year": "2017", "text": "Shamshad TV is attacked by armed gunmen and suicide bombers, with a security guard killed and 20 people wounded; ISIS claims responsibility for the attack.", "html": "2017 - <a href=\"https://wikipedia.org/wiki/Shamshad_TV\" title=\"Shamshad TV\">Shamshad TV</a> is attacked by armed gunmen and <a href=\"https://wikipedia.org/wiki/Suicide_bomber\" class=\"mw-redirect\" title=\"Suicide bomber\">suicide bombers</a>, with a security guard killed and 20 people wounded; <a href=\"https://wikipedia.org/wiki/Islamic_State_of_Iraq_and_the_Levant\" class=\"mw-redirect\" title=\"Islamic State of Iraq and the Levant\">ISIS</a> claims responsibility for the attack.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Shamshad_TV\" title=\"Shamshad TV\">Shamshad TV</a> is attacked by armed gunmen and <a href=\"https://wikipedia.org/wiki/Suicide_bomber\" class=\"mw-redirect\" title=\"Suicide bomber\">suicide bombers</a>, with a security guard killed and 20 people wounded; <a href=\"https://wikipedia.org/wiki/Islamic_State_of_Iraq_and_the_Levant\" class=\"mw-redirect\" title=\"Islamic State of Iraq and the Levant\">ISIS</a> claims responsibility for the attack.", "links": [{"title": "Shamshad TV", "link": "https://wikipedia.org/wiki/Shamshad_TV"}, {"title": "Suicide bomber", "link": "https://wikipedia.org/wiki/Suicide_bomber"}, {"title": "Islamic State of Iraq and the Levant", "link": "https://wikipedia.org/wiki/Islamic_State_of_Iraq_and_the_Levant"}]}, {"year": "2020", "text": "<PERSON> is elected the 46th president of the United States, defeating incumbent <PERSON>.", "html": "2020 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> is <a href=\"https://wikipedia.org/wiki/2020_United_States_presidential_election\" title=\"2020 United States presidential election\">elected</a> the <a href=\"https://wikipedia.org/wiki/Presidency_of_<PERSON>_<PERSON>\" title=\"Presidency of <PERSON>\">46th</a> <a href=\"https://wikipedia.org/wiki/President_of_the_United_States\" title=\"President of the United States\">president of the United States</a>, defeating incumbent <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> is <a href=\"https://wikipedia.org/wiki/2020_United_States_presidential_election\" title=\"2020 United States presidential election\">elected</a> the <a href=\"https://wikipedia.org/wiki/Presidency_of_<PERSON>_<PERSON>\" title=\"Presidency of <PERSON>\">46th</a> <a href=\"https://wikipedia.org/wiki/President_of_the_United_States\" title=\"President of the United States\">president of the United States</a>, defeating incumbent <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "2020 United States presidential election", "link": "https://wikipedia.org/wiki/2020_United_States_presidential_election"}, {"title": "Presidency of <PERSON>", "link": "https://wikipedia.org/wiki/Presidency_of_<PERSON>_<PERSON>"}, {"title": "President of the United States", "link": "https://wikipedia.org/wiki/President_of_the_United_States"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "2023", "text": "<PERSON><PERSON><PERSON><PERSON> resigns as Prime Minister of Portugal following news of an investigation in a corruption scandal implicating members of his cabinet.", "html": "2023 - <a href=\"https://wikipedia.org/wiki/Ant%C3%B3nio_Costa\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a> resigns as <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Portugal\" title=\"Prime Minister of Portugal\">Prime Minister of Portugal</a> following news of <a href=\"https://wikipedia.org/wiki/2023_Portuguese_government_corruption_investigation\" class=\"mw-redirect\" title=\"2023 Portuguese government corruption investigation\">an investigation in a corruption scandal</a> implicating members of his cabinet.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Ant%C3%B3nio_Costa\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a> resigns as <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Portugal\" title=\"Prime Minister of Portugal\">Prime Minister of Portugal</a> following news of <a href=\"https://wikipedia.org/wiki/2023_Portuguese_government_corruption_investigation\" class=\"mw-redirect\" title=\"2023 Portuguese government corruption investigation\">an investigation in a corruption scandal</a> implicating members of his cabinet.", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Ant%C3%B3nio_Costa"}, {"title": "Prime Minister of Portugal", "link": "https://wikipedia.org/wiki/Prime_Minister_of_Portugal"}, {"title": "2023 Portuguese government corruption investigation", "link": "https://wikipedia.org/wiki/2023_Portuguese_government_corruption_investigation"}]}], "Births": [{"year": "630", "text": "<PERSON><PERSON><PERSON>, Byzantine emperor (d. 668)", "html": "630 - <a href=\"https://wikipedia.org/wiki/Constans_II\" title=\"Constans II\"><PERSON><PERSON><PERSON> II</a>, Byzantine emperor (d. 668)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Constans_II\" title=\"Constans II\"><PERSON><PERSON><PERSON> II</a>, Byzantine emperor (d. 668)", "links": [{"title": "Constans II", "link": "https://wikipedia.org/wiki/Constans_II"}]}, {"year": "994", "text": "<PERSON>, Arabian philosopher and scholar (d. 1069)", "html": "994 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Arabian philosopher and scholar (d. 1069)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Arabian philosopher and scholar (d. 1069)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Ibn_<PERSON>"}]}, {"year": "1186", "text": "<PERSON><PERSON><PERSON>, Mongol ruler, 2nd Great Khan of the Mongol Empire (d. 1241)", "html": "1186 - <a href=\"https://wikipedia.org/wiki/%C3%96<PERSON><PERSON>_Khan\" title=\"Ögedei Khan\"><PERSON>ged<PERSON></a>, Mongol ruler, 2nd <a href=\"https://wikipedia.org/wiki/Great_Khan\" class=\"mw-redirect\" title=\"Great Khan\">Great Khan of the Mongol Empire</a> (d. 1241)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/%C3%96<PERSON><PERSON>_Khan\" title=\"Ögedei Khan\"><PERSON><PERSON><PERSON> Khan</a>, Mongol ruler, 2nd <a href=\"https://wikipedia.org/wiki/Great_Khan\" class=\"mw-redirect\" title=\"Great Khan\">Great Khan of the Mongol Empire</a> (d. 1241)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/%C3%96ged<PERSON>_Khan"}, {"title": "Great Khan", "link": "https://wikipedia.org/wiki/Great_<PERSON>"}]}, {"year": "1316", "text": "<PERSON><PERSON><PERSON> of Russia (d. 1353)", "html": "1316 - <a href=\"https://wikipedia.org/wiki/Simeon_of_Russia\" class=\"mw-redirect\" title=\"Simeon of Russia\">Simeon of Russia</a> (d. 1353)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Simeon_of_Russia\" class=\"mw-redirect\" title=\"Simeon of Russia\">Simeon of Russia</a> (d. 1353)", "links": [{"title": "Simeon of Russia", "link": "https://wikipedia.org/wiki/Simeon_of_Russia"}]}, {"year": "1456", "text": "<PERSON> of Bavaria, <PERSON><PERSON>tress <PERSON>, Princess of Bavaria-Landshut by birth (d. 1501)", "html": "1456 - <a href=\"https://wikipedia.org/wiki/<PERSON>_of_Bavaria,_Electress_Pa<PERSON>\" title=\"Margaret of Bavaria, Electress <PERSON>\"><PERSON> of Bavaria, <PERSON>ectress <PERSON></a>, Princess of Bavaria-Landshut by birth (d. 1501)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_of_Bavaria,_Electress_<PERSON><PERSON>\" title=\"<PERSON> of Bavaria, Electress <PERSON>latine\"><PERSON> of Bavaria, <PERSON><PERSON>tress <PERSON></a>, Princess of Bavaria-Landshut by birth (d. 1501)", "links": [{"title": "<PERSON> of Bavaria, Electress <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_Bavaria,_Electress_Palatine"}]}, {"year": "1525", "text": "<PERSON>, German lawyer and politician (d. 1575)", "html": "1525 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German lawyer and politician (d. 1575)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German lawyer and politician (d. 1575)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Georg_Cracow"}]}, {"year": "1598", "text": "<PERSON>, Spanish painter (d. 1664)", "html": "1598 - <a href=\"https://wikipedia.org/wiki/Francisco_de_Zurbar%C3%A1n\" title=\"Francisco de Zurbarán\"><PERSON></a>, Spanish painter (d. 1664)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Francisco_de_Zurbar%C3%A1n\" title=\"Francisco de Zurbarán\"><PERSON></a>, Spanish painter (d. 1664)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Francisco_de_Zurbar%C3%A1n"}]}, {"year": "1619", "text": "<PERSON><PERSON><PERSON><PERSON><PERSON>, French author and poet (d. 1692)", "html": "1619 - <a href=\"https://wikipedia.org/wiki/G%C3%A9d%C3%A9<PERSON>_Tallemant_des_R%C3%A9aux\" title=\"<PERSON><PERSON><PERSON><PERSON><PERSON> Tallemant des Réaux\"><PERSON><PERSON><PERSON><PERSON><PERSON> Réaux</a>, French author and poet (d. 1692)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/G%C3%A9d%C3%A9<PERSON>_Tallemant_des_R%C3%A9aux\" title=\"<PERSON><PERSON><PERSON><PERSON><PERSON> Tallemant des Réaux\"><PERSON><PERSON><PERSON><PERSON><PERSON>éaux</a>, French author and poet (d. 1692)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON><PERSON>éaux", "link": "https://wikipedia.org/wiki/G%C3%A9d%C3%A9on_Tallemant_des_R%C3%A9aux"}]}, {"year": "1650", "text": "<PERSON>, English bishop and diplomat (d. 1723)", "html": "1650 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(bishop_of_London)\" title=\"<PERSON> (bishop of London)\"><PERSON></a>, English bishop and diplomat (d. 1723)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(bishop_of_London)\" title=\"<PERSON> (bishop of London)\"><PERSON></a>, English bishop and diplomat (d. 1723)", "links": [{"title": "<PERSON> (bishop of London)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(bishop_of_London)"}]}, {"year": "1683", "text": "<PERSON>, German-Estonian clergyman, author, and translator (d. 1748)", "html": "1683 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German-Estonian clergyman, author, and translator (d. 1748)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German-Estonian clergyman, author, and translator (d. 1748)", "links": [{"title": "<PERSON> thor <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "1687", "text": "<PERSON>, English archaeologist and physician (d. 1765)", "html": "1687 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English archaeologist and physician (d. 1765)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English archaeologist and physician (d. 1765)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1706", "text": "<PERSON>, Italian violinist and composer (d. 1761)", "html": "1706 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian violinist and composer (d. 1761)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian violinist and composer (d. 1761)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1728", "text": "<PERSON>, English captain, navigator, and cartographer (d. 1779)", "html": "1728 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English captain, navigator, and cartographer (d. 1779)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English captain, navigator, and cartographer (d. 1779)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1750", "text": "<PERSON>-Stolberg, German poet and lawyer (d. 1819)", "html": "1750 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_Stolberg-Stolberg\" title=\"<PERSON> zu Stolberg-Stolberg\"><PERSON> zu Stolberg-Stolberg</a>, German poet and lawyer (d. 1819)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_Stolberg-Stolberg\" title=\"<PERSON> zu Stolberg-Stolberg\"><PERSON> zu Stolberg-Stolberg</a>, German poet and lawyer (d. 1819)", "links": [{"title": "<PERSON> Stolberg-Stolberg", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_Stolberg-Stolberg"}]}, {"year": "1787", "text": "<PERSON>, Polish-born actor and theatre director (d. 1854)", "html": "1787 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Polish-born actor and theatre director (d. 1854)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Polish-born actor and theatre director (d. 1854)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1789", "text": "<PERSON>, American legislator, canal builder, and railroad magnate (d. 1859)", "html": "1789 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American legislator, canal builder, and railroad magnate (d. 1859)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American legislator, canal builder, and railroad magnate (d. 1859)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1800", "text": "<PERSON><PERSON>, American calligrapher and educator (d. 1864)", "html": "1800 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American calligrapher and educator (d. 1864)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American calligrapher and educator (d. 1864)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1805", "text": "<PERSON>, English engineer and businessman (d. 1870)", "html": "1805 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English engineer and businessman (d. 1870)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English engineer and businessman (d. 1870)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1818", "text": "<PERSON>, German physician and physiologist (d. 1896)", "html": "1818 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German physician and physiologist (d. 1896)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German physician and physiologist (d. 1896)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1821", "text": "<PERSON>, Maltese trader and explorer (d. 1871)", "html": "1821 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Maltese trader and explorer (d. 1871)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Maltese trader and explorer (d. 1871)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1830", "text": "<PERSON><PERSON>, Maltese architect and civil engineer (d. 1907)", "html": "1830 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Maltese architect and civil engineer (d. 1907)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Maltese architect and civil engineer (d. 1907)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1832", "text": "<PERSON>, American historian, academic, and diplomat, co-founded Cornell University (d. 1918)", "html": "1832 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American historian, academic, and diplomat, co-founded <a href=\"https://wikipedia.org/wiki/Cornell_University\" title=\"Cornell University\">Cornell University</a> (d. 1918)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American historian, academic, and diplomat, co-founded <a href=\"https://wikipedia.org/wiki/Cornell_University\" title=\"Cornell University\">Cornell University</a> (d. 1918)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "Cornell University", "link": "https://wikipedia.org/wiki/Cornell_University"}]}, {"year": "1838", "text": "<PERSON>Isle<PERSON>, French author and playwright (d. 1889)", "html": "1838 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>%27Isle-Adam\" title=\"<PERSON> l'Isle-Adam\"><PERSON>Isle<PERSON>Adam</a>, French author and playwright (d. 1889)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>%27Isle-Adam\" title=\"<PERSON> l'Isle-Adam\"><PERSON>Isle<PERSON>Adam</a>, French author and playwright (d. 1889)", "links": [{"title": "<PERSON>'Isle-Adam", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>%27Isle-<PERSON>"}]}, {"year": "1843", "text": "<PERSON>, American businessman, industrialist and banker (d. 1905)", "html": "1843 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American businessman, industrialist and banker (d. 1905)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American businessman, industrialist and banker (d. 1905)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1846", "text": "<PERSON><PERSON><PERSON>, Austrian pianist and composer (d. 1907)", "html": "1846 - <a href=\"https://wikipedia.org/wiki/Ignaz_Br%C3%BCll\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Austrian pianist and composer (d. 1907)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Ignaz_Br%C3%BCll\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Austrian pianist and composer (d. 1907)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Ignaz_Br%C3%BCll"}]}, {"year": "1851", "text": "<PERSON>, German-American businessman (d. 1913)", "html": "1851 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German-American businessman (d. 1913)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German-American businessman (d. 1913)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1858", "text": "<PERSON><PERSON><PERSON>, Indian academic and activist (d. 1932)", "html": "1858 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Indian academic and activist (d. 1932)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Indian academic and activist (d. 1932)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>l"}]}, {"year": "1860", "text": "<PERSON>, French general and engineer (d. 1936)", "html": "1860 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>%C3%A8ne_Est<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French general and engineer (d. 1936)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>%C3%A8ne_Estienne\" title=\"<PERSON>\"><PERSON></a>, French general and engineer (d. 1936)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>%C3%A8ne_Estienne"}]}, {"year": "1860", "text": "<PERSON>, Canadian painter and academic (d. 1892)", "html": "1860 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian painter and academic (d. 1892)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian painter and academic (d. 1892)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1861", "text": "<PERSON>, American police officer (d. 1947)", "html": "1861 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American police officer (d. 1947)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American police officer (d. 1947)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1861", "text": "<PERSON>, German painter (d. 1931)", "html": "1861 - <a href=\"https://wikipedia.org/wiki/Lesser_Ury\" title=\"Lesser Ury\">Lesser Ury</a>, German painter (d. 1931)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Lesser_Ury\" title=\"Lesser Ury\">Lesser Ury</a>, German painter (d. 1931)", "links": [{"title": "Lesser Ury", "link": "https://wikipedia.org/wiki/Lesser_Ury"}]}, {"year": "1867", "text": "<PERSON>, Polish chemist and physicist, Nobel Prize laureate (d. 1934)", "html": "1867 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Polish chemist and physicist, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Physics\" title=\"Nobel Prize in Physics\">Nobel Prize</a> laureate (d. 1934)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Polish chemist and physicist, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Physics\" title=\"Nobel Prize in Physics\">Nobel Prize</a> laureate (d. 1934)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Nobel Prize in Physics", "link": "https://wikipedia.org/wiki/Nobel_Prize_in_Physics"}]}, {"year": "1872", "text": "<PERSON><PERSON>, American actress (d. 1945)", "html": "1872 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_La_Verne\" title=\"<PERSON><PERSON> La Verne\"><PERSON><PERSON></a>, American actress (d. 1945)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>erne\" title=\"<PERSON>ille La Verne\"><PERSON><PERSON></a>, American actress (d. 1945)", "links": [{"title": "Lucille La Verne", "link": "https://wikipedia.org/wiki/Lucille_La_Verne"}]}, {"year": "1872", "text": "<PERSON><PERSON>, American poet and violinist (d. 1956)", "html": "1872 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American poet and violinist (d. 1956)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American poet and violinist (d. 1956)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1876", "text": "<PERSON>, English cricketer and lawyer (d. 1958)", "html": "1876 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English cricketer and lawyer (d. 1958)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English cricketer and lawyer (d. 1958)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1878", "text": "<PERSON><PERSON>, Austrian-Swedish physicist and academic (d. 1968)", "html": "1878 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Austrian-Swedish physicist and academic (d. 1968)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Austrian-Swedish physicist and academic (d. 1968)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1879", "text": "<PERSON>, American actor, director, and screenwriter (d. 1948)", "html": "1879 - <a href=\"https://wikipedia.org/wiki/King_<PERSON>\" title=\"King Baggot\">King <PERSON></a>, American actor, director, and screenwriter (d. 1948)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/King_<PERSON>\" title=\"King Baggot\">King <PERSON></a>, American actor, director, and screenwriter (d. 1948)", "links": [{"title": "King <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>ot"}]}, {"year": "1879", "text": "<PERSON>, Russian theorist and politician, founded the Red Army (d. 1940)", "html": "1879 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian theorist and politician, founded the <a href=\"https://wikipedia.org/wiki/Red_Army\" title=\"Red Army\">Red Army</a> (d. 1940)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian theorist and politician, founded the <a href=\"https://wikipedia.org/wiki/Red_Army\" title=\"Red Army\">Red Army</a> (d. 1940)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Red Army", "link": "https://wikipedia.org/wiki/Red_Army"}]}, {"year": "1886", "text": "<PERSON><PERSON>, Russian-Danish chess player and theoretician (d. 1935)", "html": "1886 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Russian-Danish chess player and theoretician (d. 1935)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Russian-Danish chess player and theoretician (d. 1935)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1888", "text": "<PERSON><PERSON> <PERSON><PERSON>, Indian physicist and academic, Nobel Prize laureate (d. 1970)", "html": "1888 - <a href=\"https://wikipedia.org/wiki/C._V._Raman\" title=\"C. V. Raman\"><PERSON><PERSON> <PERSON><PERSON></a>, Indian physicist and academic, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Physics\" title=\"Nobel Prize in Physics\">Nobel Prize</a> laureate (d. 1970)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/C._V._Raman\" title=\"C. V. Raman\"><PERSON><PERSON> <PERSON><PERSON></a>, Indian physicist and academic, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Physics\" title=\"Nobel Prize in Physics\">Nobel Prize</a> laureate (d. 1970)", "links": [{"title": "<PERSON><PERSON> <PERSON><PERSON>", "link": "https://wikipedia.org/wiki/C._<PERSON><PERSON>_<PERSON>n"}, {"title": "Nobel Prize in Physics", "link": "https://wikipedia.org/wiki/Nobel_Prize_in_Physics"}]}, {"year": "1888", "text": "<PERSON><PERSON><PERSON>, Ukrainian anarchist revolutionary (d. 1934)", "html": "1888 - <a href=\"https://wikipedia.org/wiki/Nest<PERSON>_<PERSON>\" title=\"Nest<PERSON>\"><PERSON><PERSON><PERSON></a>, Ukrainian anarchist revolutionary (d. 1934)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/N<PERSON><PERSON>_<PERSON>\" title=\"Nest<PERSON>\"><PERSON><PERSON><PERSON></a>, Ukrainian anarchist revolutionary (d. 1934)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Nestor_<PERSON><PERSON>"}]}, {"year": "1890", "text": "<PERSON>, Czech-American painter and illustrator (d. 1972)", "html": "1890 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Czech-American painter and illustrator (d. 1972)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Czech-American painter and illustrator (d. 1972)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1891", "text": "<PERSON><PERSON><PERSON>, director of the NKVD (d. 1938)", "html": "1891 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>go<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, director of the <a href=\"https://wikipedia.org/wiki/NKVD\" title=\"NKVD\">NKVD</a> (d. 1938)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, director of the <a href=\"https://wikipedia.org/wiki/NKVD\" title=\"NKVD\">NKVD</a> (d. 1938)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Genrikh_Yagoda"}, {"title": "NKVD", "link": "https://wikipedia.org/wiki/NKVD"}]}, {"year": "1893", "text": "<PERSON><PERSON><PERSON>, American actress (d. 1985)", "html": "1893 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON> Joy\"><PERSON><PERSON><PERSON></a>, American actress (d. 1985)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON> Joy\"><PERSON><PERSON><PERSON></a>, American actress (d. 1985)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>e_Joy"}]}, {"year": "1893", "text": "<PERSON>, American historian and author (d. 1974)", "html": "1893 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American historian and author (d. 1974)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American historian and author (d. 1974)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1896", "text": "<PERSON><PERSON><PERSON><PERSON>, Canadian economist and sociologist (d. 1975)", "html": "1896 - <a href=\"https://wikipedia.org/wiki/Es<PERSON><PERSON>_<PERSON>\" title=\"Esdras Minville\"><PERSON><PERSON><PERSON><PERSON></a>, Canadian economist and sociologist (d. 1975)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/E<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON>s<PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Canadian economist and sociologist (d. 1975)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Es<PERSON>s_Minville"}]}, {"year": "1897", "text": "<PERSON>, American director, producer, and screenwriter (d. 1953)", "html": "1897 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American director, producer, and screenwriter (d. 1953)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American director, producer, and screenwriter (d. 1953)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1897", "text": "<PERSON>, American author and illustrator (d. 1976)", "html": "1897 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> S<PERSON>ry\"><PERSON></a>, American author and illustrator (d. 1976)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author and illustrator (d. 1976)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Armstrong_<PERSON>perry"}]}, {"year": "1898", "text": "<PERSON>, American actress (d. 1968)", "html": "1898 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(actress)\" title=\"<PERSON> (actress)\"><PERSON></a>, American actress (d. 1968)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(actress)\" title=\"<PERSON> (actress)\"><PERSON></a>, American actress (d. 1968)", "links": [{"title": "<PERSON> (actress)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(actress)"}]}, {"year": "1898", "text": "<PERSON><PERSON><PERSON>, Greek-French mathematician and academic (d. 1963)", "html": "1898 - <a href=\"https://wikipedia.org/wiki/Rapha%C3%ABl_Salem\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Greek-French mathematician and academic (d. 1963)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Rapha%C3%ABl_Salem\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Greek-French mathematician and academic (d. 1963)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Rapha%C3%ABl_Salem"}]}, {"year": "1899", "text": "<PERSON><PERSON><PERSON>, Russian-Israeli journalist and poet (d. 1954)", "html": "1899 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Russian-Israeli journalist and poet (d. 1954)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Russian-Israeli journalist and poet (d. 1954)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1900", "text": "<PERSON><PERSON>, Mexican writer who chronicled the Mexican Revolution (d. 1986)", "html": "1900 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Mexican writer who chronicled the <a href=\"https://wikipedia.org/wiki/Mexican_Revolution\" title=\"Mexican Revolution\">Mexican Revolution</a> (d. 1986)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Mexican writer who chronicled the <a href=\"https://wikipedia.org/wiki/Mexican_Revolution\" title=\"Mexican Revolution\">Mexican Revolution</a> (d. 1986)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}, {"title": "Mexican Revolution", "link": "https://wikipedia.org/wiki/Mexican_Revolution"}]}, {"year": "1901", "text": "<PERSON><PERSON>, Irish painter and illustrator (d. 1980)", "html": "1901 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Irish painter and illustrator (d. 1980)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Irish painter and illustrator (d. 1980)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1903", "text": "<PERSON><PERSON>, Brazilian pianist and composer (d. 1964)", "html": "1903 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Brazilian pianist and composer (d. 1964)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Brazilian pianist and composer (d. 1964)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1903", "text": "<PERSON>, American actor (d. 1991)", "html": "1903 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor (d. 1991)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor (d. 1991)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1903", "text": "<PERSON>, Austrian zoologist, ethologist, and ornithologist, Nobel Prize laureate (d. 1989)", "html": "1903 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Austrian zoologist, ethologist, and ornithologist, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Physiology_or_Medicine\" title=\"Nobel Prize in Physiology or Medicine\">Nobel Prize</a> laureate (d. 1989)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Austrian zoologist, ethologist, and ornithologist, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Physiology_or_Medicine\" title=\"Nobel Prize in Physiology or Medicine\">Nobel Prize</a> laureate (d. 1989)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Nobel Prize in Physiology or Medicine", "link": "https://wikipedia.org/wiki/Nobel_Prize_in_Physiology_or_Medicine"}]}, {"year": "1905", "text": "<PERSON>, English composer, conductor, and educator (d. 1985)", "html": "1905 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English composer, conductor, and educator (d. 1985)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English composer, conductor, and educator (d. 1985)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1906", "text": "<PERSON>, American minister and educator (d. 1985)", "html": "1906 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American minister and educator (d. 1985)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American minister and educator (d. 1985)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1908", "text": "<PERSON><PERSON><PERSON>, French author and illustrator (d. 1994)", "html": "1908 - <a href=\"https://wikipedia.org/wiki/Marija<PERSON>\" title=\"Mari<PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, French author and illustrator (d. 1994)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Marija<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, French author and illustrator (d. 1994)", "links": [{"title": "Marijac", "link": "https://wikipedia.org/wiki/Marijac"}]}, {"year": "1909", "text": "<PERSON>, American civil rights activist (d. 1980)", "html": "1909 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American civil rights activist (d. 1980)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American civil rights activist (d. 1980)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1909", "text": "<PERSON>, American director, producer, screenwriter, and playwright (d. 1984)", "html": "1909 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American director, producer, screenwriter, and playwright (d. 1984)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American director, producer, screenwriter, and playwright (d. 1984)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1912", "text": "<PERSON>, German-English actor (d. 1977)", "html": "1912 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German-English actor (d. 1977)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German-English actor (d. 1977)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1913", "text": "<PERSON>, French novelist, philosopher, and journalist, Nobel Prize laureate (d. 1960)", "html": "1913 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French novelist, philosopher, and journalist, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Literature\" title=\"Nobel Prize in Literature\">Nobel Prize</a> laureate (d. 1960)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French novelist, philosopher, and journalist, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Literature\" title=\"Nobel Prize in Literature\">Nobel Prize</a> laureate (d. 1960)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Nobel Prize in Literature", "link": "https://wikipedia.org/wiki/Nobel_Prize_in_Literature"}]}, {"year": "1913", "text": "<PERSON><PERSON><PERSON>, Greek director and screenwriter (d. 1991)", "html": "1913 - <a href=\"https://wikipedia.org/wiki/Alekos_Sakellarios\" title=\"<PERSON>ek<PERSON>\"><PERSON><PERSON><PERSON></a>, Greek director and screenwriter (d. 1991)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Alek<PERSON>_Sakellarios\" title=\"<PERSON>ek<PERSON>\"><PERSON><PERSON><PERSON></a>, Greek director and screenwriter (d. 1991)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Alekos_Sakellarios"}]}, {"year": "1913", "text": "<PERSON>, Soviet politician, member of the Politburo of the Central Committee of the Communist Party of the Soviet Union (d. 2008)", "html": "1913 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Soviet politician, member of the <a href=\"https://wikipedia.org/wiki/Politburo_of_the_Central_Committee_of_the_Communist_Party_of_the_Soviet_Union\" class=\"mw-redirect\" title=\"Politburo of the Central Committee of the Communist Party of the Soviet Union\">Politburo of the Central Committee of the Communist Party of the Soviet Union</a> (d. 2008)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Soviet politician, member of the <a href=\"https://wikipedia.org/wiki/Politburo_of_the_Central_Committee_of_the_Communist_Party_of_the_Soviet_Union\" class=\"mw-redirect\" title=\"Politburo of the Central Committee of the Communist Party of the Soviet Union\">Politburo of the Central Committee of the Communist Party of the Soviet Union</a> (d. 2008)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Politburo of the Central Committee of the Communist Party of the Soviet Union", "link": "https://wikipedia.org/wiki/Politburo_of_the_Central_Committee_of_the_Communist_Party_of_the_Soviet_Union"}]}, {"year": "1914", "text": "<PERSON>, American actor, singer, and screenwriter (d. 1987)", "html": "1914 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(comedian)\" title=\"<PERSON> (comedian)\"><PERSON></a>, American actor, singer, and screenwriter (d. 1987)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(comedian)\" title=\"<PERSON> (comedian)\"><PERSON></a>, American actor, singer, and screenwriter (d. 1987)", "links": [{"title": "<PERSON> (comedian)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(comedian)"}]}, {"year": "1914", "text": "<PERSON><PERSON> <PERSON><PERSON>, American author (d. 2002)", "html": "1914 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> <PERSON><PERSON>\"><PERSON><PERSON> <PERSON><PERSON></a>, American author (d. 2002)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> <PERSON><PERSON>\"><PERSON><PERSON> <PERSON><PERSON></a>, American author (d. 2002)", "links": [{"title": "<PERSON><PERSON> <PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "1915", "text": "<PERSON>, American astrophysicist and academic (d. 2005)", "html": "1915 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American astrophysicist and academic (d. 2005)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American astrophysicist and academic (d. 2005)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1915", "text": "<PERSON><PERSON>, American activist and politician (d. 2006)", "html": "1915 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON><PERSON>_<PERSON>\" title=\"M. Athalie Range\"><PERSON><PERSON></a>, American activist and politician (d. 2006)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON><PERSON>_<PERSON>\" title=\"M. Athalie Range\"><PERSON><PERSON></a>, American activist and politician (d. 2006)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1917", "text": "<PERSON><PERSON>, Greek actor (d. 2003)", "html": "1917 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Greek actor (d. 2003)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Greek actor (d. 2003)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1918", "text": "<PERSON>, French general (d. 2013)", "html": "1918 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French general (d. 2013)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French general (d. 2013)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1918", "text": "<PERSON>, American minister and author (d. 2018)", "html": "1918 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American minister and author (d. 2018)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American minister and author (d. 2018)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1918", "text": "<PERSON>, Portuguese singer (d. 1993)", "html": "1918 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Portuguese singer (d. 1993)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Portuguese singer (d. 1993)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1919", "text": "<PERSON>, American director and producer (d. 2011)", "html": "1919 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American director and producer (d. 2011)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American director and producer (d. 2011)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1920", "text": "<PERSON>, American lawyer and diplomat (d. 2013)", "html": "1920 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and diplomat (d. 2013)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and diplomat (d. 2013)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1920", "text": "<PERSON>, Welsh writer, aquatic ape hypothesis (d. 2013)", "html": "1920 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Welsh writer, <a href=\"https://wikipedia.org/wiki/Aquatic_ape_hypothesis\" title=\"Aquatic ape hypothesis\">aquatic ape hypothesis</a> (d. 2013)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Welsh writer, <a href=\"https://wikipedia.org/wiki/Aquatic_ape_hypothesis\" title=\"Aquatic ape hypothesis\">aquatic ape hypothesis</a> (d. 2013)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Aquatic ape hypothesis", "link": "https://wikipedia.org/wiki/Aquatic_ape_hypothesis"}]}, {"year": "1921", "text": "<PERSON>, American singer-songwriter and journalist (d. 2015)", "html": "1921 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and journalist (d. 2015)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and journalist (d. 2015)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1921", "text": "<PERSON>, American golfer (d. 2014)", "html": "1921 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American golfer (d. 2014)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American golfer (d. 2014)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1921", "text": "<PERSON><PERSON>, member of the White Rose (d. 2012)", "html": "1921 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, member of the <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"White Rose\"><PERSON></a> (d. 2012)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, member of the <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Rose\"><PERSON></a> (d. 2012)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}, {"title": "White Rose", "link": "https://wikipedia.org/wiki/<PERSON>_Rose"}]}, {"year": "1922", "text": "<PERSON><PERSON><PERSON>, Bangladeshi politician (d. 2014)", "html": "1922 - <a href=\"https://wikipedia.org/wiki/<PERSON>hul<PERSON>_Azam\" title=\"<PERSON><PERSON><PERSON> Azam\"><PERSON><PERSON><PERSON></a>, Bangladeshi politician (d. 2014)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_Azam\" title=\"<PERSON><PERSON><PERSON> Azam\"><PERSON><PERSON><PERSON></a>, Bangladeshi politician (d. 2014)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON><PERSON>"}]}, {"year": "1922", "text": "<PERSON>, American trumpet player and bandleader (d. 1999)", "html": "1922 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American trumpet player and bandleader (d. 1999)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_Hi<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American trumpet player and bandleader (d. 1999)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_Hirt"}]}, {"year": "1923", "text": "<PERSON>, American art director and production designer (d. 1990)", "html": "1923 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(production_designer)\" class=\"mw-redirect\" title=\"<PERSON> (production designer)\"><PERSON></a>, American art director and production designer (d. 1990)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(production_designer)\" class=\"mw-redirect\" title=\"<PERSON> (production designer)\"><PERSON></a>, American art director and production designer (d. 1990)", "links": [{"title": "<PERSON> (production designer)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(production_designer)"}]}, {"year": "1926", "text": "<PERSON>, Australian soprano (d. 2010)", "html": "1926 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian soprano (d. 2010)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian soprano (d. 2010)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1927", "text": "<PERSON>, American tennis player (d. 1980)", "html": "1927 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American tennis player (d. 1980)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American tennis player (d. 1980)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>lam"}]}, {"year": "1927", "text": "<PERSON><PERSON><PERSON>, Japanese businessman (d. 2013)", "html": "1927 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese businessman (d. 2013)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese businessman (d. 2013)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1928", "text": "<PERSON>, American engineer and religious leader (d. 2015)", "html": "1928 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American engineer and religious leader (d. 2015)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American engineer and religious leader (d. 2015)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "1929", "text": "<PERSON><PERSON><PERSON> Polanco, Spanish publisher and businessman (d. 2007)", "html": "1929 - <a href=\"https://wikipedia.org/wiki/Jes%C3%BAs_de_Polanco\" title=\"<PERSON><PERSON><PERSON> de Polanco\"><PERSON><PERSON><PERSON> Polanco</a>, Spanish publisher and businessman (d. 2007)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Jes%C3%BAs_de_Polanco\" title=\"<PERSON><PERSON><PERSON> de Polanco\"><PERSON><PERSON><PERSON> de Polanco</a>, Spanish publisher and businessman (d. 2007)", "links": [{"title": "Je<PERSON>ús de Polanco", "link": "https://wikipedia.org/wiki/Jes%C3%BAs_de_Polanco"}]}, {"year": "1929", "text": "<PERSON>, Austrian-American neuroscientist and psychiatrist, Nobel Prize laureate", "html": "1929 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Austrian-American neuroscientist and psychiatrist, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Physiology_or_Medicine\" title=\"Nobel Prize in Physiology or Medicine\">Nobel Prize</a> laureate", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Austrian-American neuroscientist and psychiatrist, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Physiology_or_Medicine\" title=\"Nobel Prize in Physiology or Medicine\">Nobel Prize</a> laureate", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}, {"title": "Nobel Prize in Physiology or Medicine", "link": "https://wikipedia.org/wiki/Nobel_Prize_in_Physiology_or_Medicine"}]}, {"year": "1929", "text": "<PERSON>, English actress (d. 2012)", "html": "1929 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actress (d. 2012)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actress (d. 2012)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1930", "text": "<PERSON>, German-American politician", "html": "1930 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German-American politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German-American politician", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1931", "text": "<PERSON><PERSON>, American director, producer, and author", "html": "1931 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON> <PERSON></a>, American director, producer, and author", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON> <PERSON></a>, American director, producer, and author", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1935", "text": "<PERSON><PERSON><PERSON><PERSON><PERSON>, Czech animator, producer and author (d. 1995)", "html": "1935 - <a href=\"https://wikipedia.org/wiki/Lubom%C3%ADr_Bene%C5%A1\" title=\"<PERSON><PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON><PERSON></a>, Czech animator, producer and author (d. 1995)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Lubom%C3%ADr_Bene%C5%A1\" title=\"<PERSON><PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON><PERSON></a>, Czech animator, producer and author (d. 1995)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Lubom%C3%ADr_Bene%C5%A1"}]}, {"year": "1935", "text": "<PERSON><PERSON> <PERSON><PERSON>, Indonesian poet and playwright (d. 2009)", "html": "1935 - <a href=\"https://wikipedia.org/wiki/W._<PERSON><PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"W. S. Rendra\"><PERSON><PERSON> <PERSON><PERSON></a>, Indonesian poet and playwright (d. 2009)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/W._<PERSON><PERSON>_<PERSON>dra\" class=\"mw-redirect\" title=\"W. S. Rendra\"><PERSON><PERSON> <PERSON><PERSON></a>, Indonesian poet and playwright (d. 2009)", "links": [{"title": "<PERSON><PERSON> <PERSON><PERSON>", "link": "https://wikipedia.org/wiki/W._<PERSON><PERSON>_Rendra"}]}, {"year": "1936", "text": "<PERSON>, American basketball player and coach (d. 2024)", "html": "1936 - <a href=\"https://wikipedia.org/wiki/Al_<PERSON>tles\" title=\"Al Attles\"><PERSON></a>, American basketball player and coach (d. 2024)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Al_Attles\" title=\"Al Attles\"><PERSON></a>, American basketball player and coach (d. 2024)", "links": [{"title": "Al <PERSON>", "link": "https://wikipedia.org/wiki/Al_Attles"}]}, {"year": "1936", "text": "<PERSON><PERSON><PERSON>, Welsh soprano", "html": "1936 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_(soprano)\" title=\"<PERSON><PERSON><PERSON> (soprano)\"><PERSON><PERSON><PERSON></a>, Welsh soprano", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_(soprano)\" title=\"<PERSON><PERSON><PERSON> (soprano)\"><PERSON><PERSON><PERSON></a>, Welsh soprano", "links": [{"title": "<PERSON><PERSON><PERSON> (soprano)", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_(soprano)"}]}, {"year": "1937", "text": "<PERSON>, American journalist and author (d. 2022)", "html": "1937 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American journalist and author (d. 2022)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American journalist and author (d. 2022)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1938", "text": "<PERSON>, American singer-songwriter (d. 1990)", "html": "1938 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter (d. 1990)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter (d. 1990)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1938", "text": "<PERSON>, American baseball player and coach", "html": "1938 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player and coach", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1938", "text": "<PERSON>, American baseball player, coach, and sportscaster", "html": "1938 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player, coach, and sportscaster", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player, coach, and sportscaster", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1938", "text": "<PERSON>, American actor (d. 2023)", "html": "1938 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor (d. 2023)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor (d. 2023)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1939", "text": "<PERSON>, American computer scientist and academic", "html": "1939 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American computer scientist and academic", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American computer scientist and academic", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Barbara_<PERSON>"}]}, {"year": "1940", "text": "<PERSON><PERSON>, American actor, director, and playwright", "html": "1940 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American actor, director, and playwright", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American actor, director, and playwright", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1940", "text": "<PERSON>, Chilean author and academic", "html": "1940 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>k%C3%A1rmeta\" title=\"<PERSON>\"><PERSON></a>, Chilean author and academic", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>k%C3%A1rmeta\" title=\"<PERSON>\"><PERSON></a>, Chilean author and academic", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Antonio_Sk%C3%A1rmeta"}]}, {"year": "1941", "text": "<PERSON>, American poet and architect (d. 2014)", "html": "1941 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American poet and architect (d. 2014)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American poet and architect (d. 2014)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1941", "text": "<PERSON>, Italian cardinal and philosopher", "html": "1941 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian cardinal and philosopher", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian cardinal and philosopher", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1942", "text": "<PERSON>, American businessman and author", "html": "1942 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American businessman and author", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American businessman and author", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1942", "text": "<PERSON>, American singer-songwriter, guitarist, and producer", "html": "1942 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter, guitarist, and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter, guitarist, and producer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1942", "text": "<PERSON>, English model and actress", "html": "1942 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English model and actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English model and actress", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1943", "text": "<PERSON><PERSON><PERSON>, New Zealand lawyer, judge, and politician, 18th Governor-General of New Zealand", "html": "1943 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, New Zealand lawyer, judge, and politician, 18th <a href=\"https://wikipedia.org/wiki/Governor-General_of_New_Zealand\" title=\"Governor-General of New Zealand\">Governor-General of New Zealand</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, New Zealand lawyer, judge, and politician, 18th <a href=\"https://wikipedia.org/wiki/Governor-General_of_New_Zealand\" title=\"Governor-General of New Zealand\">Governor-General of New Zealand</a>", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}, {"title": "Governor-General of New Zealand", "link": "https://wikipedia.org/wiki/Governor-General_of_New_Zealand"}]}, {"year": "1943", "text": "<PERSON>, American theorist, scholar, and critic", "html": "1943 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American theorist, scholar, and critic", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American theorist, scholar, and critic", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1943", "text": "<PERSON>, Russian general and politician, Governor of Moscow Oblast", "html": "1943 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian general and politician, <a href=\"https://wikipedia.org/wiki/Governor_of_Moscow_Oblast\" title=\"Governor of Moscow Oblast\">Governor of Moscow Oblast</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian general and politician, <a href=\"https://wikipedia.org/wiki/Governor_of_Moscow_Oblast\" title=\"Governor of Moscow Oblast\">Governor of Moscow Oblast</a>", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}, {"title": "Governor of Moscow Oblast", "link": "https://wikipedia.org/wiki/Governor_of_Moscow_Oblast"}]}, {"year": "1943", "text": "<PERSON><PERSON>, Canadian singer-songwriter and guitarist", "html": "1943 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Canadian singer-songwriter and guitarist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Canadian singer-songwriter and guitarist", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1943", "text": "<PERSON>, American economist and academic, Nobel Prize laureate", "html": "1943 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American economist and academic, <a href=\"https://wikipedia.org/wiki/Nobel_Memorial_Prize_in_Economic_Sciences\" title=\"Nobel Memorial Prize in Economic Sciences\">Nobel Prize</a> laureate", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American economist and academic, <a href=\"https://wikipedia.org/wiki/Nobel_Memorial_Prize_in_Economic_Sciences\" title=\"Nobel Memorial Prize in Economic Sciences\">Nobel Prize</a> laureate", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Nobel Memorial Prize in Economic Sciences", "link": "https://wikipedia.org/wiki/Nobel_Memorial_Prize_in_Economic_Sciences"}]}, {"year": "1944", "text": "<PERSON><PERSON>, Italian footballer and manager (d. 2024)", "html": "1944 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Italian footballer and manager (d. 2024)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Italian footballer and manager (d. 2024)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_Riva"}]}, {"year": "1944", "text": "<PERSON>, English journalist", "html": "1944 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English journalist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English journalist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1945", "text": "<PERSON>, American baseball player (d. 2006)", "html": "1945 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player (d. 2006)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player (d. 2006)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1946", "text": "<PERSON><PERSON><PERSON><PERSON>, American writer and activist", "html": "1946 - <a href=\"https://wikipedia.org/wiki/Chry<PERSON><PERSON>\" title=\"<PERSON>ry<PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, American writer and activist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>ry<PERSON><PERSON>\" title=\"<PERSON>ry<PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, American writer and activist", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Chrystos"}]}, {"year": "1947", "text": "<PERSON>, English darts player", "html": "1947 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(darts_player)\" title=\"<PERSON> (darts player)\"><PERSON></a>, English darts player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(darts_player)\" title=\"<PERSON> (darts player)\"><PERSON></a>, English darts player", "links": [{"title": "<PERSON> (darts player)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(darts_player)"}]}, {"year": "1947", "text": "<PERSON>, American television producer", "html": "1947 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American television producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American television producer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1947", "text": "<PERSON><PERSON><PERSON>, Japanese baseball player and coach", "html": "1947 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese baseball player and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese baseball player and coach", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON><PERSON>"}]}, {"year": "1947", "text": "<PERSON>, American screenwriter and producer (d. 2008)", "html": "1947 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American screenwriter and producer (d. 2008)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American screenwriter and producer (d. 2008)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1947", "text": "<PERSON><PERSON>, Thai journalist and politician", "html": "1947 - <a href=\"https://wikipedia.org/wiki/Sondhi_Limthongkul\" title=\"Sondhi Limthongkul\"><PERSON><PERSON></a>, Thai journalist and politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Sondhi_Limthongkul\" title=\"Sondhi Limthongkul\"><PERSON><PERSON></a>, Thai journalist and politician", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Sondhi_Limthongkul"}]}, {"year": "1948", "text": "<PERSON>, Baron <PERSON> of Hurstpierpoint, English businessman and politician", "html": "1948 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Baron_<PERSON>_of_Hurstpierpoint\" title=\"<PERSON>, Baron Green of Hurstpierpoint\"><PERSON>, Baron <PERSON> of Hurstpierpoint</a>, English businessman and politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>,_Baron_<PERSON>_of_Hurstpierpoint\" title=\"<PERSON>, Baron Green of Hurstpierpoint\"><PERSON>, Baron <PERSON> of Hurstpierpoint</a>, English businessman and politician", "links": [{"title": "<PERSON>, Baron <PERSON> of Hurstpierpoint", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_<PERSON>_<PERSON>_<PERSON>_Hurstpierpoint"}]}, {"year": "1948", "text": "<PERSON>, American baseball player and manager", "html": "1948 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player and manager", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player and manager", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1948", "text": "<PERSON>, Brazilian race car driver", "html": "1948 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Brazilian race car driver", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Brazilian race car driver", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1949", "text": "<PERSON>, American guitarist, songwriter, and producer (d. 2009)", "html": "1949 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American guitarist, songwriter, and producer (d. 2009)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American guitarist, songwriter, and producer (d. 2009)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1949", "text": "<PERSON>, American composer and academic (d. 2016)", "html": "1949 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American composer and academic (d. 2016)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American composer and academic (d. 2016)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1949", "text": "<PERSON>, American saxophonist, composer, and bandleader (d. 2012)", "html": "1949 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American saxophonist, composer, and bandleader (d. 2012)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American saxophonist, composer, and bandleader (d. 2012)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1950", "text": "<PERSON>, Scottish actress", "html": "1950 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish actress", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1950", "text": "<PERSON>, Australian rugby league player and coach", "html": "1950 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(Australian_rugby_league)\" title=\"<PERSON> (Australian rugby league)\"><PERSON></a>, Australian rugby league player and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(Australian_rugby_league)\" title=\"<PERSON> (Australian rugby league)\"><PERSON></a>, Australian rugby league player and coach", "links": [{"title": "<PERSON> (Australian rugby league)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(Australian_rugby_league)"}]}, {"year": "1951", "text": "<PERSON>, New Zealand astronomer and academic", "html": "1951 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, New Zealand astronomer and academic", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, New Zealand astronomer and academic", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "1951", "text": "<PERSON>, Canadian guitarist, songwriter, and record producer (d. 2002)", "html": "1951 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian guitarist, songwriter, and record producer (d. 2002)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian guitarist, songwriter, and record producer (d. 2002)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1951", "text": "<PERSON>, American journalist and talk show host", "html": "1951 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%27Donnell\" title=\"<PERSON>\"><PERSON></a>, American journalist and talk show host", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%27Donnell\" title=\"<PERSON>\"><PERSON></a>, American journalist and talk show host", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Lawrence_O%27Donnell"}]}, {"year": "1951", "text": "<PERSON>, American baseball player and coach", "html": "1951 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player and coach", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1952", "text": "<PERSON>, American general, Director of the Central Intelligence Agency", "html": "1952 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American general, <a href=\"https://wikipedia.org/wiki/Director_of_the_Central_Intelligence_Agency\" title=\"Director of the Central Intelligence Agency\">Director of the Central Intelligence Agency</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American general, <a href=\"https://wikipedia.org/wiki/Director_of_the_Central_Intelligence_Agency\" title=\"Director of the Central Intelligence Agency\">Director of the Central Intelligence Agency</a>", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}, {"title": "Director of the Central Intelligence Agency", "link": "https://wikipedia.org/wiki/Director_of_the_Central_Intelligence_Agency"}]}, {"year": "1952", "text": "<PERSON><PERSON><PERSON>, Sudanese-Malian police officer and politician, Prime Minister of Mali", "html": "1952 - <a href=\"https://wikipedia.org/wiki/Modibo_Sidib%C3%A9\" title=\"Modibo Sidibé\"><PERSON><PERSON><PERSON></a>, Sudanese-Malian police officer and politician, <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Mali\" class=\"mw-redirect\" title=\"Prime Minister of Mali\">Prime Minister of Mali</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Modibo_Sidib%C3%A9\" title=\"Modibo Sidibé\"><PERSON><PERSON><PERSON></a>, Sudanese-Malian police officer and politician, <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Mali\" class=\"mw-redirect\" title=\"Prime Minister of Mali\">Prime Minister of Mali</a>", "links": [{"title": "Modibo <PERSON>", "link": "https://wikipedia.org/wiki/Modibo_Sidib%C3%A9"}, {"title": "Prime Minister of Mali", "link": "https://wikipedia.org/wiki/Prime_Minister_of_Mali"}]}, {"year": "1952", "text": "<PERSON><PERSON><PERSON>, Ukrainian footballer and manager (d. 2016)", "html": "1952 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Ukrainian footballer and manager (d. 2016)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Ukrainian footballer and manager (d. 2016)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1953", "text": "<PERSON><PERSON>, Estonian journalist and author", "html": "1953 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Estonian journalist and author", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Estonian journalist and author", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1953", "text": "<PERSON>, Norwegian saxophonist and composer", "html": "1953 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Norwegian saxophonist and composer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Norwegian saxophonist and composer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1953", "text": "<PERSON>, English bishop", "html": "1953 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(bishop)\" title=\"<PERSON> (bishop)\"><PERSON></a>, English bishop", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(bishop)\" title=\"<PERSON> (bishop)\"><PERSON></a>, English bishop", "links": [{"title": "<PERSON> (bishop)", "link": "https://wikipedia.org/wiki/<PERSON>_(bishop)"}]}, {"year": "1953", "text": "<PERSON><PERSON>, English equestrian and journalist", "html": "1953 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_Green\" title=\"Lucinda Green\"><PERSON><PERSON></a>, English equestrian and journalist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> Green\"><PERSON><PERSON></a>, English equestrian and journalist", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1954", "text": "<PERSON>, Scottish politician", "html": "1954 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(British_politician)\" title=\"<PERSON> (British politician)\"><PERSON></a>, Scottish politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(British_politician)\" title=\"<PERSON> (British politician)\"><PERSON></a>, Scottish politician", "links": [{"title": "<PERSON> (British politician)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>(British_politician)"}]}, {"year": "1954", "text": "<PERSON>, Tamil actor, director, producer, and screenwriter", "html": "1954 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Tamil actor, director, producer, and screenwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Tamil actor, director, producer, and screenwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1954", "text": "<PERSON>, Canadian lawyer and author", "html": "1954 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian lawyer and author", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian lawyer and author", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1954", "text": "<PERSON>, American director, producer, and screenwriter", "html": "1954 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American director, producer, and screenwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American director, producer, and screenwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1956", "text": "<PERSON>, Ukrainian pianist and composer (d. 2018)", "html": "1956 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Ukrainian pianist and composer (d. 2018)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Ukrainian pianist and composer (d. 2018)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1956", "text": "<PERSON>, English race car driver and businessman", "html": "1956 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English race car driver and businessman", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English race car driver and businessman", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1956", "text": "<PERSON>, American comedian, actress, and comedy musician (d. 2022)", "html": "1956 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American comedian, actress, and comedy musician (d. 2022)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American comedian, actress, and comedy musician (d. 2022)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1957", "text": "<PERSON>, American DJ, songwriter, and producer", "html": "1957 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American DJ, songwriter, and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American DJ, songwriter, and producer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1957", "text": "<PERSON>, American wrestler (d. 2019)", "html": "1957 - <a href=\"https://wikipedia.org/wiki/King_Kong_Bundy\" title=\"King Kong Bundy\">King <PERSON></a>, American wrestler (d. 2019)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/King_Kong_Bundy\" title=\"King Kong Bundy\">King Kong Bundy</a>, American wrestler (d. 2019)", "links": [{"title": "King Kong Bundy", "link": "https://wikipedia.org/wiki/King_Kong_Bundy"}]}, {"year": "1957", "text": "<PERSON>, American actor", "html": "1957 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(actor)\" title=\"<PERSON> (actor)\"><PERSON></a>, American actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(actor)\" title=\"<PERSON> (actor)\"><PERSON></a>, American actor", "links": [{"title": "<PERSON> (actor)", "link": "https://wikipedia.org/wiki/<PERSON>_(actor)"}]}, {"year": "1958", "text": "<PERSON>, Russian politician; Deputy Prime Minister of the Russian Federation", "html": "1958 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian politician; Deputy Prime Minister of the Russian Federation", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian politician; Deputy Prime Minister of the Russian Federation", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1959", "text": "<PERSON>, American basketball player and coach", "html": "1959 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player and coach", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1959", "text": "<PERSON>, Brazilian-Costa Rican footballer and manager", "html": "1959 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A3es\" title=\"<PERSON>\"><PERSON></a>, Brazilian-Costa Rican footballer and manager", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A3es\" title=\"<PERSON>\"><PERSON></a>, Brazilian-Costa Rican footballer and manager", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>%C3%A3es"}]}, {"year": "1960", "text": "<PERSON>, American guitarist and songwriter", "html": "1960 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American guitarist and songwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American guitarist and songwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1960", "text": "<PERSON><PERSON><PERSON><PERSON><PERSON>, Indian filmmaker", "html": "1960 - <a href=\"https://wikipedia.org/wiki/Shya<PERSON>prasad\" title=\"Shya<PERSON>prasad\"><PERSON><PERSON><PERSON><PERSON><PERSON></a>, Indian filmmaker", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Shyamaprasad\" title=\"Shya<PERSON>prasad\"><PERSON><PERSON><PERSON><PERSON><PERSON></a>, Indian filmmaker", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON><PERSON>"}]}, {"year": "1961", "text": "<PERSON>, American baseball player and coach", "html": "1961 - <a href=\"https://wikipedia.org/wiki/Orlando_Mercado\" title=\"<PERSON> Mercado\"><PERSON></a>, American baseball player and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Orlando_Mercado\" title=\"Orlando Mercado\"><PERSON></a>, American baseball player and coach", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Orlando_Mercado"}]}, {"year": "1962", "text": "<PERSON><PERSON><PERSON>, American actress and journalist", "html": "1962 - <a href=\"https://wikipedia.org/wiki/Tracie_<PERSON>\" title=\"Tracie <PERSON>\">T<PERSON><PERSON></a>, American actress and journalist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Trac<PERSON>_<PERSON>\" title=\"Trac<PERSON>\">T<PERSON><PERSON></a>, American actress and journalist", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Tracie_<PERSON>"}]}, {"year": "1962", "text": "<PERSON>, American model, actor, and director (d. 2015)", "html": "1962 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American model, actor, and director (d. 2015)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American model, actor, and director (d. 2015)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1963", "text": "<PERSON>, Jamaican-English footballer and manager", "html": "1963 - <a href=\"https://wikipedia.org/wiki/<PERSON>(footballer)\" class=\"mw-redirect\" title=\"<PERSON> (footballer)\"><PERSON></a>, Jamaican-English footballer and manager", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>(footballer)\" class=\"mw-redirect\" title=\"<PERSON> (footballer)\"><PERSON></a>, Jamaican-English footballer and manager", "links": [{"title": "<PERSON> (footballer)", "link": "https://wikipedia.org/wiki/<PERSON>(footballer)"}]}, {"year": "1963", "text": "<PERSON>, American farmer and politician", "html": "1963 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American farmer and politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American farmer and politician", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1964", "text": "<PERSON>, American actress, director, and screenwriter", "html": "1964 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, American actress, director, and screenwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, American actress, director, and screenwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1964", "text": "<PERSON>, English politician", "html": "1964 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English politician", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1964", "text": "<PERSON>, Irish keyboard player and songwriter", "html": "1964 - <a href=\"https://wikipedia.org/wiki/<PERSON>_%C3%93_Maonla%C3%AD\" title=\"<PERSON>\"><PERSON></a>, Irish keyboard player and songwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_%C3%93_Maonla%C3%AD\" title=\"<PERSON>\"><PERSON></a>, Irish keyboard player and songwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_%C3%93_Maonla%C3%AD"}]}, {"year": "1964", "text": "<PERSON>, American actress (d. 1999)", "html": "1964 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Plato\"><PERSON></a>, American actress (d. 1999)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Plato\"><PERSON></a>, American actress (d. 1999)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1964", "text": "<PERSON>, American skier and scholar", "html": "1964 - <a href=\"https://wikipedia.org/wiki/Bonnie_St._John\" title=\"Bonnie St. John\">Bonnie <PERSON>. John</a>, American skier and scholar", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Bonnie_St._John\" title=\"Bonnie St. John\">Bonnie St. John</a>, American skier and scholar", "links": [{"title": "Bonnie St. John", "link": "https://wikipedia.org/wiki/Bonnie_St._John"}]}, {"year": "1965", "text": "<PERSON>, English footballer and manager", "html": "1965 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English footballer and manager", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English footballer and manager", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1965", "text": "<PERSON><PERSON><PERSON>, German runner and physiotherapist", "html": "1965 - <a href=\"https://wikipedia.org/wiki/Sig<PERSON>_Woda<PERSON>\" title=\"<PERSON>grun <PERSON>oda<PERSON>\"><PERSON><PERSON><PERSON></a>, German runner and physiotherapist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>g<PERSON>_<PERSON>oda<PERSON>\" title=\"<PERSON>grun <PERSON>oda<PERSON>\"><PERSON><PERSON><PERSON></a>, German runner and physiotherapist", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>g<PERSON>_Wodars"}]}, {"year": "1966", "text": "<PERSON>, American jockey", "html": "1966 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American jockey", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American jockey", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1967", "text": "<PERSON>, American bass player", "html": "1967 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American bass player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American bass player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1967", "text": "<PERSON>, French DJ, record producer, remixer, and songwriter", "html": "1967 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French DJ, record producer, remixer, and songwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French DJ, record producer, remixer, and songwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1967", "text": "<PERSON><PERSON><PERSON>, Japanese radio host", "html": "1967 - <a href=\"https://wikipedia.org/wiki/Hikaru_Ij%C5%ABin\" title=\"Hikaru Ijūin\"><PERSON><PERSON><PERSON></a>, Japanese radio host", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Hikaru_Ij%C5%ABin\" title=\"Hikaru Ijūin\"><PERSON><PERSON><PERSON></a>, Japanese radio host", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Hikaru_Ij%C5%ABin"}]}, {"year": "1967", "text": "<PERSON>, Dominican wrestler", "html": "1967 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dominican wrestler", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dominican wrestler", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1967", "text": "<PERSON><PERSON><PERSON>, Scottish singer-songwriter and actress", "html": "1967 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Scottish singer-songwriter and actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Scottish singer-songwriter and actress", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1968", "text": "<PERSON>, American baseball player", "html": "1968 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1969", "text": "<PERSON>, American actress", "html": "1969 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1969", "text": "<PERSON><PERSON><PERSON><PERSON>, French pianist", "html": "1969 - <a href=\"https://wikipedia.org/wiki/H%C3%A9l%C3%A8ne_G<PERSON>ud\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, French pianist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/H%C3%A9l%C3%A8<PERSON>_Grimaud\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, French pianist", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/H%C3%A9l%C3%A8ne_Grimaud"}]}, {"year": "1969", "text": "<PERSON>, Canadian ice hockey player and scout", "html": "1969 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(ice_hockey)\" title=\"<PERSON> (ice hockey)\"><PERSON></a>, Canadian ice hockey player and scout", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(ice_hockey)\" title=\"<PERSON> (ice hockey)\"><PERSON></a>, Canadian ice hockey player and scout", "links": [{"title": "<PERSON> (ice hockey)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(ice_hockey)"}]}, {"year": "1970", "text": "<PERSON>, American race car driver", "html": "1970 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American race car driver", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American race car driver", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1970", "text": "<PERSON>, Swiss-Monacan tennis player", "html": "1970 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swiss-Monacan tennis player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swiss-Monacan tennis player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1970", "text": "<PERSON>, American director, producer, and screenwriter (d. 2024)", "html": "1970 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American director, producer, and screenwriter (d. 2024)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American director, producer, and screenwriter (d. 2024)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1970", "text": "<PERSON>, English footballer (d. 2013)", "html": "1970 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English footballer (d. 2013)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English footballer (d. 2013)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1971", "text": "<PERSON>, American guitarist and songwriter", "html": "1971 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American guitarist and songwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American guitarist and songwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1971", "text": "<PERSON>, American singer-songwriter and guitarist", "html": "1971 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(musician)\" title=\"<PERSON> (musician)\"><PERSON></a>, American singer-songwriter and guitarist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>(musician)\" title=\"<PERSON> (musician)\"><PERSON></a>, American singer-songwriter and guitarist", "links": [{"title": "<PERSON> (musician)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(musician)"}]}, {"year": "1971", "text": "<PERSON><PERSON><PERSON>, Indian director and screenwriter", "html": "1971 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Indian director and screenwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON> Sri<PERSON>vas\"><PERSON><PERSON><PERSON></a>, Indian director and screenwriter", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Trivik<PERSON>_<PERSON>vas"}]}, {"year": "1972", "text": "<PERSON>, English rugby player", "html": "1972 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English rugby player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English rugby player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1972", "text": "<PERSON>, American actor and producer", "html": "1972 - <a href=\"https://wikipedia.org/wiki/Jason_<PERSON>\" title=\"Jason London\"><PERSON></a>, American actor and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Jason_<PERSON>\" title=\"Jason London\"><PERSON></a>, American actor and producer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Jason_<PERSON>"}]}, {"year": "1972", "text": "<PERSON>, American actor and producer", "html": "1972 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Jeremy <PERSON>\"><PERSON></a>, American actor and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Jeremy_<PERSON>\" title=\"Jeremy London\"><PERSON></a>, American actor and producer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Jeremy_<PERSON>"}]}, {"year": "1972", "text": "<PERSON><PERSON>, American boxer", "html": "1972 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American boxer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American boxer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1972", "text": "<PERSON>, English footballer and coach", "html": "1972 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English footballer and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English footballer and coach", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1973", "text": "<PERSON><PERSON>, Brazilian footballer and manager (d. 2011)", "html": "1973 - <a href=\"https://wikipedia.org/wiki/Cat%C3%AA\" title=\"Cat<PERSON>\"><PERSON><PERSON></a>, Brazilian footballer and manager (d. 2011)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Cat%C3%AA\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Brazilian footballer and manager (d. 2011)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Cat%C3%AA"}]}, {"year": "1973", "text": "<PERSON><PERSON>, South Korean-American actress", "html": "1973 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, South Korean-American actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, South Korean-American actress", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1973", "text": "<PERSON>, Argentinian footballer and manager", "html": "1973 - <a href=\"https://wikipedia.org/wiki/Mart%C3%ADn_Palermo\" title=\"Martín <PERSON>\"><PERSON></a>, Argentinian footballer and manager", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Mart%C3%ADn_Palermo\" title=\"Martín <PERSON>\"><PERSON></a>, Argentinian footballer and manager", "links": [{"title": "Martín <PERSON>", "link": "https://wikipedia.org/wiki/Mart%C3%ADn_Palermo"}]}, {"year": "1974", "text": "<PERSON>, American baseball player", "html": "1974 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1974", "text": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, Jamaican hurdler", "html": "1974 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Jamaican hurdler", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Jamaican hurdler", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1974", "text": "<PERSON>, Argentinian footballer", "html": "1974 - <a href=\"https://wikipedia.org/wiki/Christian_G%C3%B3mez\" title=\"<PERSON>\"><PERSON></a>, Argentinian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Christian_G%C3%B3mez\" title=\"<PERSON>\"><PERSON></a>, Argentinian footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Christian_G%C3%B3mez"}]}, {"year": "1974", "text": "<PERSON>, Norwegian drummer", "html": "1974 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(drummer)\" title=\"<PERSON> (drummer)\"><PERSON></a>, Norwegian drummer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(drummer)\" title=\"<PERSON> (drummer)\"><PERSON></a>, Norwegian drummer", "links": [{"title": "<PERSON> (drummer)", "link": "https://wikipedia.org/wiki/<PERSON>_(drummer)"}]}, {"year": "1975", "text": "<PERSON><PERSON><PERSON>, Indian actor, director, and screenwriter", "html": "1975 - <a href=\"https://wikipedia.org/wiki/Venkat_Prabhu\" title=\"Venkat Prabhu\"><PERSON><PERSON><PERSON></a>, Indian actor, director, and screenwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Venkat_Prabhu\" title=\"Venkat Prabhu\"><PERSON><PERSON><PERSON></a>, Indian actor, director, and screenwriter", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Venkat_Prabhu"}]}, {"year": "1976", "text": "<PERSON>, American guitarist and producer", "html": "1976 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American guitarist and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>\" title=\"<PERSON>\"><PERSON></a>, American guitarist and producer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1976", "text": "<PERSON>, Australian tennis player and model", "html": "1976 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian tennis player and model", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian tennis player and model", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1977", "text": "<PERSON>, American journalist and sportscaster", "html": "1977 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American journalist and sportscaster", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American journalist and sportscaster", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1977", "text": "<PERSON><PERSON>, Estonian footballer", "html": "1977 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Estonian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Estonian footballer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_Oper"}]}, {"year": "1977", "text": "<PERSON>, Spanish tennis player", "html": "1977 - <a href=\"https://wikipedia.org/wiki/Mar%C3%ADa_S%C3%<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Spanish tennis player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Mar%C3%ADa_S%C3%<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Spanish tennis player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Mar%C3%ADa_S%C3%<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1977", "text": "<PERSON>, American football player and coach", "html": "1977 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(American_football)\" title=\"<PERSON> (American football)\"><PERSON></a>, American football player and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(American_football)\" title=\"<PERSON> (American football)\"><PERSON></a>, American football player and coach", "links": [{"title": "<PERSON> (American football)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(American_football)"}]}, {"year": "1978", "text": "<PERSON>, Egyptian footballer", "html": "1978 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Egyptian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Mohamed <PERSON>\"><PERSON></a>, Egyptian footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_Aboutrika"}]}, {"year": "1978", "text": "<PERSON>, American volleyball player and coach", "html": "1978 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American volleyball player and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American volleyball player and coach", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1978", "text": "<PERSON>, English footballer", "html": "1978 - <a href=\"https://wikipedia.org/wiki/<PERSON>_Ferdinand\" title=\"Rio Ferdinand\"><PERSON></a>, English footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Rio_Ferdinand\" title=\"Rio Ferdinand\"><PERSON></a>, English footballer", "links": [{"title": "Rio <PERSON>", "link": "https://wikipedia.org/wiki/Rio_Ferdinand"}]}, {"year": "1978", "text": "<PERSON><PERSON>, Japanese singer-songwriter", "html": "1978 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> Nagase\"><PERSON><PERSON></a>, Japanese singer-songwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>gase\"><PERSON><PERSON></a>, Japanese singer-songwriter", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>e"}]}, {"year": "1978", "text": "<PERSON>, Scottish footballer", "html": "1978 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1978", "text": "<PERSON> of Hesselink, Dutch footballer", "html": "1978 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Hesselink\" title=\"<PERSON> of Hesselink\"><PERSON> of Hesselink</a>, Dutch footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Hesselink\" title=\"<PERSON> of Hesselink\"><PERSON> of Hesselink</a>, Dutch footballer", "links": [{"title": "<PERSON> of Hesselink", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Hesselink"}]}, {"year": "1979", "text": "<PERSON>, Canadian ice hockey player", "html": "1979 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1979", "text": "<PERSON>, American football player", "html": "1979 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1979", "text": "<PERSON>, Costa Rican footballer", "html": "1979 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Costa Rican footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Costa Rican footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1979", "text": "<PERSON>, English television host and actor", "html": "1979 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English television host and actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English television host and actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1979", "text": "<PERSON>, American singer-songwriter and actor", "html": "1979 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1979", "text": "<PERSON>, American actress, model and snowboarder", "html": "1979 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress, model and snowboarder", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress, model and snowboarder", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1979", "text": "<PERSON>, American wrestler", "html": "1979 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(wrestler)\" title=\"<PERSON> (wrestler)\"><PERSON></a>, American wrestler", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(wrestler)\" title=\"<PERSON> (wrestler)\"><PERSON></a>, American wrestler", "links": [{"title": "<PERSON> (wrestler)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>(wrestler)"}]}, {"year": "1979", "text": "<PERSON><PERSON>, American singer-songwriter and actress", "html": "1979 - <a href=\"https://wikipedia.org/wiki/Otep_Shamaya\" title=\"Otep Shamaya\"><PERSON><PERSON></a>, American singer-songwriter and actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Otep_Shamaya\" title=\"Otep Shamaya\"><PERSON><PERSON></a>, American singer-songwriter and actress", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Otep_Shamaya"}]}, {"year": "1980", "text": "<PERSON>, Argentinian footballer", "html": "1980 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%B3n_(footballer,_born_1980)\" title=\"<PERSON> (footballer, born 1980)\"><PERSON></a>, Argentinian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%B3n_(footballer,_born_1980)\" title=\"<PERSON> (footballer, born 1980)\"><PERSON></a>, Argentinian footballer", "links": [{"title": "<PERSON> (footballer, born 1980)", "link": "https://wikipedia.org/wiki/<PERSON>_Almir%C3%B3n_(footballer,_born_1980)"}]}, {"year": "1980", "text": "<PERSON><PERSON><PERSON><PERSON>, Spanish gymnast", "html": "1980 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Spanish gymnast", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Spanish gymnast", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Gervas<PERSON>_<PERSON>ferr"}]}, {"year": "1980", "text": "<PERSON>, New Zealand cricketer", "html": "1980 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>(cricketer)\" title=\"<PERSON> (cricketer)\"><PERSON></a>, New Zealand cricketer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>(cricketer)\" title=\"<PERSON> (cricketer)\"><PERSON></a>, New Zealand cricketer", "links": [{"title": "<PERSON> (cricketer)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>(cricketer)"}]}, {"year": "1980", "text": "<PERSON><PERSON><PERSON>, Indian singer-songwriter", "html": "1980 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_(singer)\" title=\"<PERSON><PERSON><PERSON> (singer)\"><PERSON><PERSON><PERSON></a>, Indian singer-songwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_(singer)\" title=\"<PERSON><PERSON><PERSON> (singer)\"><PERSON><PERSON><PERSON></a>, Indian singer-songwriter", "links": [{"title": "<PERSON><PERSON><PERSON> (singer)", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_(singer)"}]}, {"year": "1980", "text": "<PERSON><PERSON>, Argentinian model, actress, and singer", "html": "1980 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Argentinian model, actress, and singer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Argentinian model, actress, and singer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Lucian<PERSON>_<PERSON>"}]}, {"year": "1981", "text": "<PERSON>, American wrestler and educator", "html": "1981 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(wrestler)\" title=\"<PERSON> (wrestler)\"><PERSON></a>, American wrestler and educator", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>(wrestler)\" title=\"<PERSON> (wrestler)\"><PERSON></a>, American wrestler and educator", "links": [{"title": "<PERSON> (wrestler)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(wrestler)"}]}, {"year": "1981", "text": "<PERSON>, Japanese model, actress, and singer", "html": "1981 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Japanese model, actress, and singer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Japanese model, actress, and singer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1981", "text": "<PERSON><PERSON><PERSON>, Indian actress", "html": "1981 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Indian actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Indian actress", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1981", "text": "<PERSON><PERSON>, Japanese actress and model", "html": "1981 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Japanese actress and model", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Japanese actress and model", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1982", "text": "<PERSON>, Canadian ice hockey player", "html": "1982 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1983", "text": "<PERSON>, American actor, comedian, screenwriter, producer, and singer", "html": "1983 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor, comedian, screenwriter, producer, and singer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor, comedian, screenwriter, producer, and singer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1983", "text": "<PERSON>, American singer-songwriter and guitarist", "html": "1983 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and guitarist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and guitarist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1983", "text": "<PERSON>, Norwegian ice hockey player", "html": "1983 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Norwegian ice hockey player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Norwegian ice hockey player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1983", "text": "<PERSON><PERSON><PERSON><PERSON>, Dominican baseball player", "html": "1983 - <a href=\"https://wikipedia.org/wiki/Esmerling_V%C3%A1squez\" title=\"<PERSON>sm<PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Dominican baseball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Esm<PERSON>ling_V%C3%A1squez\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Dominican baseball player", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Esmerling_V%C3%A1squez"}]}, {"year": "1984", "text": "<PERSON><PERSON><PERSON>, Estonian footballer", "html": "1984 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Estonian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Estonian footballer", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>u"}]}, {"year": "1984", "text": "<PERSON>, American-Israeli soccer player", "html": "1984 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American-Israeli soccer player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American-Israeli soccer player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1984", "text": "<PERSON><PERSON><PERSON><PERSON>, Malagasy footballer", "html": "1984 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Malagasy footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Malagasy footballer", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>er<PERSON><PERSON>_<PERSON>"}]}, {"year": "1984", "text": "<PERSON>, Dominican actress and singer, Miss Universe 2003", "html": "1984 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Amelia Vega\"><PERSON></a>, Dominican actress and singer, <a href=\"https://wikipedia.org/wiki/Miss_Universe\" title=\"Miss Universe\">Miss Universe 2003</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Amelia Vega\"><PERSON></a>, Dominican actress and singer, <a href=\"https://wikipedia.org/wiki/Miss_Universe\" title=\"Miss Universe\">Miss Universe 2003</a>", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Miss Universe", "link": "https://wikipedia.org/wiki/Miss_Universe"}]}, {"year": "1985", "text": "<PERSON>, Swedish motorcycle racer", "html": "1985 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A9n\" title=\"<PERSON>\"><PERSON></a>, Swedish motorcycle racer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A9n\" title=\"<PERSON>\"><PERSON></a>, Swedish motorcycle racer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Sebastian_Ald%C3%A9n"}]}, {"year": "1985", "text": "<PERSON><PERSON>, American basketball player", "html": "1985 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American basketball player", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1985", "text": "<PERSON>, American actor", "html": "1985 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>eff"}]}, {"year": "1986", "text": "<PERSON>, American singer-songwriter and guitarist", "html": "1986 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and guitarist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and guitarist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1986", "text": "<PERSON>, American football player", "html": "1986 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(wide_receiver)\" title=\"<PERSON> (wide receiver)\"><PERSON></a>, American football player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(wide_receiver)\" title=\"<PERSON> (wide receiver)\"><PERSON></a>, American football player", "links": [{"title": "<PERSON> (wide receiver)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>(wide_receiver)"}]}, {"year": "1986", "text": "<PERSON><PERSON><PERSON>, Greek model and television host", "html": "1986 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON> Nomikou\"><PERSON><PERSON><PERSON></a>, Greek model and television host", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON> Nomikou\"><PERSON><PERSON><PERSON></a>, Greek model and television host", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>u"}]}, {"year": "1987", "text": "<PERSON>, Australian rugby league player", "html": "1987 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(rugby_league)\" title=\"<PERSON> (rugby league)\"><PERSON></a>, Australian rugby league player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(rugby_league)\" title=\"<PERSON> (rugby league)\"><PERSON></a>, Australian rugby league player", "links": [{"title": "<PERSON> (rugby league)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(rugby_league)"}]}, {"year": "1987", "text": "<PERSON><PERSON>, Slovak tennis player", "html": "1987 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Slovak tennis player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Slovak tennis player", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1988", "text": "<PERSON><PERSON><PERSON>, Ukrainian tennis player", "html": "1988 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Ukrainian tennis player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Ukrainian tennis player", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1988", "text": "<PERSON>, Italian rugby player", "html": "1988 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian rugby player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian rugby player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1988", "text": "<PERSON><PERSON>, Nigerian-American basketball player", "html": "1988 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>al\" title=\"Gani Lawal\"><PERSON><PERSON></a>, Nigerian-American basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>al\" title=\"<PERSON>ani Lawal\"><PERSON><PERSON></a>, Nigerian-American basketball player", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_Lawal"}]}, {"year": "1988", "text": "<PERSON>, German sprinter", "html": "1988 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(runner)\" class=\"mw-redirect\" title=\"<PERSON> (runner)\"><PERSON></a>, German sprinter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(runner)\" class=\"mw-redirect\" title=\"<PERSON> (runner)\"><PERSON></a>, German sprinter", "links": [{"title": "<PERSON> (runner)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>(runner)"}]}, {"year": "1988", "text": "<PERSON><PERSON>, English rapper and producer", "html": "1988 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>mpah\"><PERSON><PERSON></a>, English rapper and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>mp<PERSON>\"><PERSON><PERSON></a>, English rapper and producer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1989", "text": "<PERSON>, American baseball player", "html": "1989 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1989", "text": "<PERSON><PERSON><PERSON><PERSON>, Russian singer and political activist", "html": "1989 - <a href=\"https://wikipedia.org/wiki/Nadez<PERSON><PERSON>_<PERSON>lo<PERSON>nikov<PERSON>\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Russian singer and political activist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Na<PERSON><PERSON><PERSON>_<PERSON><PERSON>\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Russian singer and political activist", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Nadez<PERSON><PERSON>_<PERSON><PERSON><PERSON>a"}]}, {"year": "1990", "text": "<PERSON>, Spanish footballer", "html": "1990 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Spanish footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Spanish footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1990", "text": "<PERSON>, Australian singer-songwriter and guitarist", "html": "1990 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian singer-songwriter and guitarist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian singer-songwriter and guitarist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1990", "text": "<PERSON>, Spanish footballer", "html": "1990 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Spanish footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Spanish footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1990", "text": "<PERSON><PERSON>, Australian singer-songwriter", "html": "1990 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Australian singer-songwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Australian singer-songwriter", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1991", "text": "<PERSON>, Swedish race car driver", "html": "1991 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swedish race car driver", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swedish race car driver", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1992", "text": "<PERSON><PERSON><PERSON>, Australian-Fijian rugby league player", "html": "1992 - <a href=\"https://wikipedia.org/wiki/A<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Australian-Fijian rugby league player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Australian-Fijian rugby league player", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/A<PERSON><PERSON>_<PERSON>"}]}, {"year": "1994", "text": "<PERSON><PERSON><PERSON><PERSON>, American boxer", "html": "1994 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, American boxer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, American boxer", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1994", "text": "<PERSON><PERSON><PERSON>, Japanese singer and actress", "html": "1994 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese singer and actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese singer and actress", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON><PERSON><PERSON>o"}]}, {"year": "1994", "text": "<PERSON><PERSON>, American actor and singer", "html": "1994 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American actor and singer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American actor and singer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1996", "text": "<PERSON><PERSON>, New Zealand singer-songwriter", "html": "1996 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, New Zealand singer-songwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, New Zealand singer-songwriter", "links": [{"title": "<PERSON>e", "link": "https://wikipedia.org/wiki/<PERSON>e"}]}, {"year": "1997", "text": "<PERSON><PERSON>, Estonian tennis player", "html": "1997 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Estonian tennis player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Estonian tennis player", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1997", "text": "<PERSON>, Japanese singer", "html": "1997 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Japanese singer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Japanese singer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1998", "text": "<PERSON><PERSON><PERSON>, South Korean rapper, singer, producer, and songwriter", "html": "1998 - <a href=\"https://wikipedia.org/wiki/Hongjoong\" title=\"Hongjoong\"><PERSON><PERSON><PERSON></a>, South Korean rapper, singer, producer, and songwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Hongjoong\" title=\"Hongjo<PERSON>\"><PERSON><PERSON><PERSON></a>, South Korean rapper, singer, producer, and songwriter", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Hongjoong"}]}, {"year": "1998", "text": "<PERSON>, American social media personality", "html": "1998 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American social media personality", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American social media personality", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2000", "text": "<PERSON><PERSON><PERSON>, English footballer", "html": "2000 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English footballer", "links": [{"title": "<PERSON>-<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>i"}]}, {"year": "2001", "text": "<PERSON><PERSON>, Irish actress", "html": "2001 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Irish actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Irish actress", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}], "Deaths": [{"year": "691", "text": "<PERSON><PERSON>, official of the Tang dynasty", "html": "691 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, official of the <a href=\"https://wikipedia.org/wiki/Tang_dynasty\" title=\"Tang dynasty\">Tang dynasty</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, official of the <a href=\"https://wikipedia.org/wiki/Tang_dynasty\" title=\"Tang dynasty\">Tang dynasty</a>", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}, {"title": "Tang dynasty", "link": "https://wikipedia.org/wiki/Tang_dynasty"}]}, {"year": "691", "text": "<PERSON><PERSON>, official of the Tang dynasty", "html": "691 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> Fuyuan\"><PERSON><PERSON></a>, official of the Tang dynasty", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>yuan\"><PERSON><PERSON></a>, official of the Tang dynasty", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>e_<PERSON>yuan"}]}, {"year": "927", "text": "<PERSON>, general of Later Tang", "html": "927 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, general of Later Tang", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, general of Later Tang", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>n"}]}, {"year": "1173", "text": "<PERSON><PERSON><PERSON> of Goryeo, Korean monarch of the Goryeo dynasty (b. 1127)", "html": "1173 - <a href=\"https://wikipedia.org/wiki/Ui<PERSON>_of_Goryeo\" title=\"<PERSON><PERSON><PERSON> of Goryeo\"><PERSON><PERSON><PERSON> of Goryeo</a>, Korean monarch of the Goryeo dynasty (b. 1127)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Ui<PERSON>_of_Goryeo\" title=\"<PERSON><PERSON><PERSON> of Goryeo\"><PERSON><PERSON><PERSON> of Goryeo</a>, Korean monarch of the Goryeo dynasty (b. 1127)", "links": [{"title": "<PERSON><PERSON><PERSON> of Goryeo", "link": "https://wikipedia.org/wiki/Uijong_of_Goryeo"}]}, {"year": "1225", "text": "<PERSON><PERSON><PERSON> of Berg, German archbishop and saint (b. 1186)", "html": "1225 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_of_Berg\" title=\"<PERSON><PERSON><PERSON> <PERSON> of Berg\"><PERSON><PERSON><PERSON> <PERSON> of Berg</a>, German archbishop and saint (b. 1186)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_of_Berg\" title=\"<PERSON><PERSON><PERSON> of Berg\"><PERSON><PERSON><PERSON> <PERSON> of Berg</a>, German archbishop and saint (b. 1186)", "links": [{"title": "<PERSON><PERSON><PERSON> of Berg", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_of_Berg"}]}, {"year": "1497", "text": "<PERSON>, Duke of Savoy (b. 1443)", "html": "1497 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Duke_of_Savoy\" title=\"<PERSON>, Duke of Savoy\"><PERSON>, Duke of Savoy</a> (b. 1443)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Duke_of_Savoy\" title=\"<PERSON>, Duke of Savoy\"><PERSON>, Duke of Savoy</a> (b. 1443)", "links": [{"title": "<PERSON>, Duke of Savoy", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Duke_of_Savoy"}]}, {"year": "1550", "text": "<PERSON><PERSON>, Icelandic bishop and poet (b. 1484)", "html": "1550 - <a href=\"https://wikipedia.org/wiki/J%C3%B3n_<PERSON><PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Icelandic bishop and poet (b. 1484)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/J%C3%B3n_<PERSON><PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Icelandic bishop and poet (b. 1484)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/J%C3%B3n_<PERSON>son"}]}, {"year": "1561", "text": "<PERSON>, Swiss nun and writer  (b. 1503)", "html": "1561 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swiss nun and writer (b. 1503)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swiss nun and writer (b. 1503)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1562", "text": "<PERSON><PERSON><PERSON>, Rao of Marwar (b. 1511)", "html": "1562 - <a href=\"https://wikipedia.org/wiki/Mal<PERSON><PERSON>_Rathore\" title=\"Mal<PERSON><PERSON> Rathore\"><PERSON><PERSON><PERSON></a>, <PERSON> of Marwar (b. 1511)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Mal<PERSON><PERSON>_Rathore\" title=\"<PERSON><PERSON><PERSON>hore\"><PERSON><PERSON><PERSON></a>, <PERSON> of Marwar (b. 1511)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Maldeo_Rathore"}]}, {"year": "1574", "text": "<PERSON>, Polish rabbi and educator (b. 1510)", "html": "1574 - <a href=\"https://wikipedia.org/wiki/Solomon_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Polish rabbi and educator (b. 1510)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Solomon_Lu<PERSON>\" title=\"Solomon Luria\"><PERSON></a>, Polish rabbi and educator (b. 1510)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Solomon_Luria"}]}, {"year": "1581", "text": "<PERSON>, Welsh bishop and scholar (b. 1505)", "html": "1581 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(bishop)\" title=\"<PERSON> (bishop)\"><PERSON></a>, Welsh bishop and scholar (b. 1505)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(bishop)\" title=\"<PERSON> (bishop)\"><PERSON></a>, Welsh bishop and scholar (b. 1505)", "links": [{"title": "<PERSON> (bishop)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(bishop)"}]}, {"year": "1599", "text": "<PERSON><PERSON><PERSON>, Italian surgeon and educator (b. 1546)", "html": "1599 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Italian surgeon and educator (b. 1546)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Italian surgeon and educator (b. 1546)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1627", "text": "<PERSON><PERSON><PERSON><PERSON>, Mughal emperor (b. 1569)", "html": "1627 - <a href=\"https://wikipedia.org/wiki/Jahangir\" title=\"Jahangir\"><PERSON><PERSON><PERSON><PERSON></a>, Mughal emperor (b. 1569)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Jahangir\" title=\"Jahangir\"><PERSON><PERSON><PERSON><PERSON></a>, Mughal emperor (b. 1569)", "links": [{"title": "Jahangir", "link": "https://wikipedia.org/wiki/Jahangir"}]}, {"year": "1633", "text": "<PERSON><PERSON><PERSON>, Dutch inventor (b. 1572)", "html": "1633 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"Corne<PERSON> Drebbel\"><PERSON><PERSON><PERSON></a>, Dutch inventor (b. 1572)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"Cornelis Drebbel\"><PERSON><PERSON><PERSON></a>, Dutch inventor (b. 1572)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>bel"}]}, {"year": "1639", "text": "<PERSON>, 1st Baron <PERSON> of Wardour, English politician (b. 1560)", "html": "1639 - <a href=\"https://wikipedia.org/wiki/<PERSON>,_1st_Baron_<PERSON>_of_Wardour\" title=\"<PERSON>, 1st Baron <PERSON> of Wardour\"><PERSON>, 1st Baron <PERSON> of Wardour</a>, English politician (b. 1560)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>,_1st_Baron_<PERSON>_of_Wardour\" title=\"<PERSON>, 1st Baron <PERSON> of Wardour\"><PERSON>, 1st Baron <PERSON> of Wardour</a>, English politician (b. 1560)", "links": [{"title": "<PERSON>, 1st Baron <PERSON> of Wardour", "link": "https://wikipedia.org/wiki/<PERSON>,_1st_Baron_<PERSON>_of_Wardour"}]}, {"year": "1642", "text": "<PERSON>, 1st Earl of Manchester, English judge and politician, Lord High Treasurer of The United Kingdom (b. 1563)", "html": "1642 - <a href=\"https://wikipedia.org/wiki/<PERSON>,_1st_Earl_of_Manchester\" title=\"<PERSON>, 1st Earl of Manchester\"><PERSON>, 1st Earl of Manchester</a>, English judge and politician, <a href=\"https://wikipedia.org/wiki/Lord_High_Treasurer\" title=\"Lord High Treasurer\">Lord High Treasurer of The United Kingdom</a> (b. 1563)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>,_1st_Earl_of_Manchester\" title=\"<PERSON>, 1st Earl of Manchester\"><PERSON>, 1st Earl of Manchester</a>, English judge and politician, <a href=\"https://wikipedia.org/wiki/Lord_High_Treasurer\" title=\"Lord High Treasurer\">Lord High Treasurer of The United Kingdom</a> (b. 1563)", "links": [{"title": "<PERSON>, 1st Earl of Manchester", "link": "https://wikipedia.org/wiki/<PERSON>,_1st_Earl_of_Manchester"}, {"title": "Lord High Treasurer", "link": "https://wikipedia.org/wiki/Lord_High_Treasurer"}]}, {"year": "1652", "text": "<PERSON> of Nassau-Siegen, German count, officer in the Dutch Army, diplomat for the Dutch Republic (b. 1611)", "html": "1652 - <a href=\"https://wikipedia.org/wiki/<PERSON>_of_Nassau-Siegen_(1611%E2%80%931652)\" title=\"<PERSON> of Nassau-Siegen (1611-1652)\"><PERSON> of Nassau-Siegen</a>, German count, officer in the Dutch Army, diplomat for the Dutch Republic (b. <a href=\"https://wikipedia.org/wiki/1611\" title=\"1611\">1611</a>)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_of_Nassau-Siegen_(1611%E2%80%931652)\" title=\"<PERSON> of Nassau-Siegen (1611-1652)\"><PERSON> of Nassau-Siegen</a>, German count, officer in the Dutch Army, diplomat for the Dutch Republic (b. <a href=\"https://wikipedia.org/wiki/1611\" title=\"1611\">1611</a>)", "links": [{"title": "<PERSON> of Nassau-Siegen (1611-1652)", "link": "https://wikipedia.org/wiki/Henry_of_Nassau-Siegen_(1611%E2%80%931652)"}, {"title": "1611", "link": "https://wikipedia.org/wiki/1611"}]}, {"year": "1713", "text": "<PERSON>, English actress (b. 1658)", "html": "1713 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actress (b. 1658)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actress (b. 1658)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1809", "text": "<PERSON>, English painter and cartographer (b. 1725)", "html": "1809 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English painter and cartographer (b. 1725)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English painter and cartographer (b. 1725)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1837", "text": "<PERSON>, American minister and journalist (b. 1809)", "html": "1837 - <a href=\"https://wikipedia.org/wiki/Elijah_Parish_Lovejoy\" title=\"Elijah Parish Lovejoy\"><PERSON> Lovejoy</a>, American minister and journalist (b. 1809)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Elijah_Parish_Lovejoy\" title=\"Elijah Parish Lovejoy\"><PERSON> Lovejoy</a>, American minister and journalist (b. 1809)", "links": [{"title": "<PERSON> Lovejoy", "link": "https://wikipedia.org/wiki/Elijah_Parish_Lovejoy"}]}, {"year": "1862", "text": "<PERSON>, Mughal emperor (b. 1775)", "html": "1862 - <a href=\"https://wikipedia.org/wiki/Bahadur_Shah_II\" class=\"mw-redirect\" title=\"Bahadur Shah II\"><PERSON></a>, Mughal emperor (b. 1775)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Bahadur_Shah_<PERSON>\" class=\"mw-redirect\" title=\"Bahadur Shah II\"><PERSON></a>, Mughal emperor (b. 1775)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1872", "text": "<PERSON>, German mathematician and academic (b. 1833)", "html": "1872 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German mathematician and academic (b. 1833)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German mathematician and academic (b. 1833)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1881", "text": "<PERSON>, Irish archbishop (b. 1791)", "html": "1881 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Irish archbishop (b. 1791)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Irish archbishop (b. 1791)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1906", "text": "<PERSON>, German engineer and poet (b. 1842)", "html": "1906 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German engineer and poet (b. 1842)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German engineer and poet (b. 1842)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1907", "text": "<PERSON><PERSON><PERSON>, Mexican railroad brakeman (b. 1881)", "html": "1907 - <a href=\"https://wikipedia.org/wiki/Jes%C3%BAs_Garc%C3%ADa\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Mexican railroad brakeman (b. 1881)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Jes%C3%BAs_Garc%C3%ADa\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Mexican railroad brakeman (b. 1881)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Jes%C3%BAs_Garc%C3%ADa"}]}, {"year": "1913", "text": "<PERSON>, Welsh-English biologist and geographer (b. 1823)", "html": "1913 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Welsh-English biologist and geographer (b. 1823)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Welsh-English biologist and geographer (b. 1823)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1916", "text": "<PERSON>, American painter and academic (b. 1858)", "html": "1916 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American painter and academic (b. 1858)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American painter and academic (b. 1858)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1919", "text": "<PERSON>, German lawyer, jurist, and politician (b. 1863)", "html": "1919 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German lawyer, jurist, and politician (b. 1863)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German lawyer, jurist, and politician (b. 1863)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1922", "text": "<PERSON>, American baseball player (b. 1860)", "html": "1922 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(outfielder)\" title=\"<PERSON> (outfielder)\"><PERSON></a>, American baseball player (b. 1860)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(outfielder)\" title=\"<PERSON> (outfielder)\"><PERSON></a>, American baseball player (b. 1860)", "links": [{"title": "<PERSON> (outfielder)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(outfielder)"}]}, {"year": "1923", "text": "<PERSON><PERSON><PERSON>, Indian educator and philanthropist (b. 1856)", "html": "1923 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Indian educator and philanthropist (b. 1856)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Indian educator and philanthropist (b. 1856)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1930", "text": "<PERSON><PERSON><PERSON>, Japanese sumo wrestler, the 23rd <PERSON><PERSON><PERSON><PERSON> (b. 1878)", "html": "1930 - <a href=\"https://wikipedia.org/wiki/%C5%8Ckido_Moriemon\" title=\"<PERSON><PERSON><PERSON> Mo<PERSON>mon\"><PERSON><PERSON><PERSON></a>, Japanese sumo wrestler, the 23rd <a href=\"https://wikipedia.org/wiki/Yoko<PERSON>na_(sumo)\" class=\"mw-redirect\" title=\"Yokozuna (sumo)\"><PERSON><PERSON><PERSON><PERSON></a> (b. 1878)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/%C5%8Ckido_Moriemon\" title=\"<PERSON><PERSON><PERSON> Mo<PERSON>mon\"><PERSON><PERSON><PERSON></a>, Japanese sumo wrestler, the 23rd <a href=\"https://wikipedia.org/wiki/Yo<PERSON><PERSON>na_(sumo)\" class=\"mw-redirect\" title=\"Yo<PERSON><PERSON>na (sumo)\"><PERSON><PERSON><PERSON><PERSON></a> (b. 1878)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/%C5%8Ckido_Moriemon"}, {"title": "<PERSON><PERSON><PERSON><PERSON> (sumo)", "link": "https://wikipedia.org/wiki/Yokozuna_(sumo)"}]}, {"year": "1933", "text": "<PERSON>, American golfer and architect (b. 1882)", "html": "1933 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American golfer and architect (b. 1882)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American golfer and architect (b. 1882)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1941", "text": "<PERSON>, English lawyer and businessman (b. 1878)", "html": "1941 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English lawyer and businessman (b. 1878)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Pick\"><PERSON></a>, English lawyer and businessman (b. 1878)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1944", "text": "<PERSON>, Azerbaijani-German journalist and spy (b. 1895)", "html": "1944 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Azerbaijani-German journalist and spy (b. 1895)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Azerbaijani-German journalist and spy (b. 1895)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1944", "text": "<PERSON>, Hungarian-Israeli soldier and poet (b. 1921)", "html": "1944 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Hungarian-Israeli soldier and poet (b. 1921)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Hungarian-Israeli soldier and poet (b. 1921)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1947", "text": "<PERSON><PERSON>, Indian-Sri Lankan journalist and politician (b. 1887)", "html": "1947 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> <PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Indian-Sri Lankan journalist and politician (b. 1887)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON></a>, Indian-Sri Lankan journalist and politician (b. 1887)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "1959", "text": "<PERSON>, English-American boxer and actor (b. 1883)", "html": "1959 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English-American boxer and actor (b. 1883)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English-American boxer and actor (b. 1883)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1962", "text": "<PERSON>, American humanitarian and politician, 39th First Lady of the United States (b. 1884)", "html": "1962 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American humanitarian and politician, 39th <a href=\"https://wikipedia.org/wiki/First_Lady_of_the_United_States\" title=\"First Lady of the United States\">First Lady of the United States</a> (b. 1884)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American humanitarian and politician, 39th <a href=\"https://wikipedia.org/wiki/First_Lady_of_the_United_States\" title=\"First Lady of the United States\">First Lady of the United States</a> (b. 1884)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "First Lady of the United States", "link": "https://wikipedia.org/wiki/First_Lady_of_the_United_States"}]}, {"year": "1964", "text": "<PERSON><PERSON><PERSON>, German-Swedish biochemist and academic, Nobel Prize laureate (b. 1863)", "html": "1964 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>pin\" title=\"<PERSON>pin\"><PERSON></a>, German-Swedish biochemist and academic, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Chemistry\" title=\"Nobel Prize in Chemistry\">Nobel Prize</a> laureate (b. 1863)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>pin\" title=\"<PERSON>\"><PERSON></a>, German-Swedish biochemist and academic, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Chemistry\" title=\"Nobel Prize in Chemistry\">Nobel Prize</a> laureate (b. 1863)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>-<PERSON><PERSON>"}, {"title": "Nobel Prize in Chemistry", "link": "https://wikipedia.org/wiki/Nobel_Prize_in_Chemistry"}]}, {"year": "1966", "text": "<PERSON><PERSON>, American baseball player (b. 1894)", "html": "1966 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>ler\" title=\"<PERSON><PERSON> Bressler\"><PERSON><PERSON></a>, American baseball player (b. 1894)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>ler\" title=\"<PERSON><PERSON> Bressler\"><PERSON><PERSON></a>, American baseball player (b. 1894)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1967", "text": "<PERSON>, American lawyer and politician, 32nd Vice President of the United States (b. 1868)", "html": "1967 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and politician, 32nd <a href=\"https://wikipedia.org/wiki/Vice_President_of_the_United_States\" title=\"Vice President of the United States\">Vice President of the United States</a> (b. 1868)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and politician, 32nd <a href=\"https://wikipedia.org/wiki/Vice_President_of_the_United_States\" title=\"Vice President of the United States\">Vice President of the United States</a> (b. 1868)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "Vice President of the United States", "link": "https://wikipedia.org/wiki/Vice_President_of_the_United_States"}]}, {"year": "1968", "text": "<PERSON>, Australian footballer and coach (b. 1901)", "html": "1968 - <a href=\"https://wikipedia.org/wiki/Gordon_<PERSON>\" title=\"Gordon <PERSON>\"><PERSON></a>, Australian footballer and coach (b. 1901)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Gordon_Coventry\" title=\"Gordon Coventry\"><PERSON></a>, Australian footballer and coach (b. 1901)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Gordon_<PERSON>"}]}, {"year": "1968", "text": "<PERSON>, Russian mathematician, cryptographer, and academic (b. 1906)", "html": "1968 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian mathematician, cryptographer, and academic (b. 1906)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian mathematician, cryptographer, and academic (b. 1906)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1974", "text": "<PERSON>, Welsh-Scottish author and academic (b. 1899)", "html": "1974 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Welsh-Scottish author and academic (b. 1899)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Welsh-Scottish author and academic (b. 1899)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1975", "text": "<PERSON><PERSON>, Italian footballer, businessman and race car driver (b. 1899)", "html": "1975 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Italian footballer, businessman and race car driver (b. 1899)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Italian footballer, businessman and race car driver (b. 1899)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1978", "text": "<PERSON><PERSON><PERSON>, Indian surgeon and politician, 6th Chief Minister of Gujarat (b. 1887)", "html": "1978 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Indian surgeon and politician, 6th <a href=\"https://wikipedia.org/wiki/Chief_Minister_of_Gujarat\" class=\"mw-redirect\" title=\"Chief Minister of Gujarat\">Chief Minister of Gujarat</a> (b. 1887)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Indian surgeon and politician, 6th <a href=\"https://wikipedia.org/wiki/Chief_Minister_of_Gujarat\" class=\"mw-redirect\" title=\"Chief Minister of Gujarat\">Chief Minister of Gujarat</a> (b. 1887)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>"}, {"title": "Chief Minister of Gujarat", "link": "https://wikipedia.org/wiki/Chief_Minister_of_Gujarat"}]}, {"year": "1978", "text": "<PERSON>, American boxer and actor (b. 1897)", "html": "1978 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American boxer and actor (b. 1897)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American boxer and actor (b. 1897)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1979", "text": "<PERSON>, American actor, rancher, and painter", "html": "1979 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%27C<PERSON><PERSON>_(actor,_born_1897)\" title=\"<PERSON> (actor, born 1897)\"><PERSON></a>, American actor, rancher, and painter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%27<PERSON><PERSON><PERSON>_(actor,_born_1897)\" title=\"<PERSON> (actor, born 1897)\"><PERSON></a>, American actor, rancher, and painter", "links": [{"title": "<PERSON> (actor, born 1897)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>%27<PERSON><PERSON><PERSON>_(actor,_born_1897)"}]}, {"year": "1980", "text": "<PERSON><PERSON><PERSON>, Turkish publisher (b. 1944)", "html": "1980 - <a href=\"https://wikipedia.org/wiki/%C4%B0l<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Turkish publisher (b. 1944)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/%C4%B0<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Turkish publisher (b. 1944)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/%C4%B0<PERSON><PERSON>_<PERSON>"}]}, {"year": "1980", "text": "<PERSON>, American actor and producer (b. 1930)", "html": "1980 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor and producer (b. 1930)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor and producer (b. 1930)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1981", "text": "<PERSON>, American historian and philosopher (b. 1885)", "html": "1981 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American historian and philosopher (b. 1885)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American historian and philosopher (b. 1885)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1983", "text": "<PERSON><PERSON>, French pianist and composer (b. 1892)", "html": "1983 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, French pianist and composer (b. 1892)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, French pianist and composer (b. 1892)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1986", "text": "<PERSON>, Australian bass player (b. 1957)", "html": "1986 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian bass player (b. 1957)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian bass player (b. 1957)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1988", "text": "<PERSON>, American cartoonist (b. 1926)", "html": "1988 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American cartoonist (b. 1926)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American cartoonist (b. 1926)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1990", "text": "<PERSON>, British novelist, poet, dramatist, (b. 1912)", "html": "1990 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, British novelist, poet, dramatist, (b. 1912)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, British novelist, poet, dramatist, (b. 1912)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1990", "text": "<PERSON>, Irish singer and actor, (b. 1924)", "html": "1990 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(singer)\" title=\"<PERSON> (singer)\"><PERSON></a>, Irish singer and actor, (b. 1924)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(singer)\" title=\"<PERSON> (singer)\"><PERSON></a>, Irish singer and actor, (b. 1924)", "links": [{"title": "<PERSON> (singer)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(singer)"}]}, {"year": "1991", "text": "<PERSON> of Finland, Finnish illustrator (b. 1920)", "html": "1991 - <a href=\"https://wikipedia.org/wiki/Tom_of_Finland\" title=\"Tom of Finland\"><PERSON> of Finland</a>, Finnish illustrator (b. 1920)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Tom_of_Finland\" title=\"Tom of Finland\"><PERSON> of Finland</a>, Finnish illustrator (b. 1920)", "links": [{"title": "Tom of Finland", "link": "https://wikipedia.org/wiki/Tom_of_Finland"}]}, {"year": "1991", "text": "<PERSON><PERSON>, Iraqi psychologist and philosopher of education, (b. 1914) ", "html": "1991 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>a%27far\" title=\"<PERSON><PERSON>'far\"><PERSON><PERSON></a>, Iraqi psychologist and philosopher of education, (b. 1914) ", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>%27far\" title=\"<PERSON><PERSON>'far\"><PERSON><PERSON></a>, Iraqi psychologist and philosopher of education, (b. 1914) ", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Nuri_Ja%27far"}]}, {"year": "1992", "text": "<PERSON>, Slovak soldier and politician (b. 1921)", "html": "1992 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C4%8Dek\" title=\"<PERSON>\"><PERSON></a>, Slovak soldier and politician (b. 1921)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C4%8Dek\" title=\"<PERSON>\"><PERSON></a>, Slovak soldier and politician (b. 1921)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Alexander_Dub%C4%8Dek"}]}, {"year": "1992", "text": "<PERSON>, American actor and politician (b. 1927)", "html": "1992 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(actor)\" title=\"<PERSON> (actor)\"><PERSON></a>, American actor and politician (b. 1927)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(actor)\" title=\"<PERSON> (actor)\"><PERSON></a>, American actor and politician (b. 1927)", "links": [{"title": "<PERSON> (actor)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(actor)"}]}, {"year": "1993", "text": "<PERSON>, American-English singer, actress, and dancer (b. 1901)", "html": "1993 - <a href=\"https://wikipedia.org/wiki/Adelaide_Hall\" title=\"Adelaide Hall\">Adelaide Hall</a>, American-English singer, actress, and dancer (b. 1901)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Adelaide_Hall\" title=\"Adelaide Hall\">Adelaide Hall</a>, American-English singer, actress, and dancer (b. 1901)", "links": [{"title": "Adelaide Hall", "link": "https://wikipedia.org/wiki/Adelaide_Hall"}]}, {"year": "1993", "text": "<PERSON>, American stage, film, and television actor (b. 1925)", "html": "1993 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American stage, film, and television actor (b. 1925)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American stage, film, and television actor (b. 1925)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Charles_Aidman"}]}, {"year": "1994", "text": "<PERSON><PERSON>, American trumpet player and composer (b. 1924)", "html": "1994 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American trumpet player and composer (b. 1924)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American trumpet player and composer (b. 1924)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1995", "text": "<PERSON>, American anthropologist and academic (b. 1942)", "html": "1995 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American anthropologist and academic (b. 1942)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American anthropologist and academic (b. 1942)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1996", "text": "<PERSON>, Nigerian political scientist and academic (b. 1939)", "html": "1996 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Nigerian political scientist and academic (b. 1939)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Nigerian political scientist and academic (b. 1939)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1996", "text": "<PERSON><PERSON>, Nigerian lawyer and politician, Nigerian Minister of Foreign Affairs (b. 1918)", "html": "1996 - <a href=\"https://wikipedia.org/wiki/<PERSON>aja_Wachuku\" title=\"Jaja Wachuku\"><PERSON><PERSON></a>, Nigerian lawyer and politician, <a href=\"https://wikipedia.org/wiki/Minister_of_Foreign_Affairs_(Nigeria)\" class=\"mw-redirect\" title=\"Minister of Foreign Affairs (Nigeria)\">Nigerian Minister of Foreign Affairs</a> (b. 1918)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_Wachuku\" title=\"Jaja Wachuku\"><PERSON><PERSON></a>, Nigerian lawyer and politician, <a href=\"https://wikipedia.org/wiki/Minister_of_Foreign_Affairs_(Nigeria)\" class=\"mw-redirect\" title=\"Minister of Foreign Affairs (Nigeria)\">Nigerian Minister of Foreign Affairs</a> (b. 1918)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Jaja_Wachuku"}, {"title": "Minister of Foreign Affairs (Nigeria)", "link": "https://wikipedia.org/wiki/Minister_of_Foreign_Affairs_(Nigeria)"}]}, {"year": "2000", "text": "<PERSON> of Sweden (b. 1910)", "html": "2000 - <a href=\"https://wikipedia.org/wiki/<PERSON>_of_Sweden\" title=\"<PERSON> of Sweden\"><PERSON> of Sweden</a> (b. 1910)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_of_Sweden\" title=\"<PERSON> of Sweden\"><PERSON> of Sweden</a> (b. 1910)", "links": [{"title": "Ingrid of Sweden", "link": "https://wikipedia.org/wiki/<PERSON>_of_Sweden"}]}, {"year": "2000", "text": "<PERSON><PERSON><PERSON>, Sri Lankan educator and politician (b. 1950)", "html": "2000 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>aranayagam\" title=\"<PERSON><PERSON><PERSON> Soundaranayagam\"><PERSON><PERSON><PERSON></a>, Sri Lankan educator and politician (b. 1950)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>aranayagam\" title=\"<PERSON><PERSON><PERSON> Soundaranayagam\"><PERSON><PERSON><PERSON></a>, Sri Lankan educator and politician (b. 1950)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>gam"}]}, {"year": "2000", "text": "<PERSON><PERSON><PERSON><PERSON>, Indian publisher and politician, Indian Minister of Defence (b. 1910)", "html": "2000 - <a href=\"https://wikipedia.org/wiki/Chidambaram_Subramaniam\" title=\"Chidambaram Subramaniam\">Chidambaram Subramaniam</a>, Indian publisher and politician, <a href=\"https://wikipedia.org/wiki/Minister_of_Defence_(India)\" title=\"Minister of Defence (India)\">Indian Minister of Defence</a> (b. 1910)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Chidambaram_Subramaniam\" title=\"Chidambaram Subramaniam\">Chidambaram Subramaniam</a>, Indian publisher and politician, <a href=\"https://wikipedia.org/wiki/Minister_of_Defence_(India)\" title=\"Minister of Defence (India)\">Indian Minister of Defence</a> (b. 1910)", "links": [{"title": "Chidambaram Subramaniam", "link": "https://wikipedia.org/wiki/Chidambaram_Subramaniam"}, {"title": "Minister of Defence (India)", "link": "https://wikipedia.org/wiki/Minister_of_Defence_(India)"}]}, {"year": "2001", "text": "<PERSON><PERSON>, Filipino actress (b. 1936)", "html": "2001 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Filipino actress (b. 1936)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Filipino actress (b. 1936)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>a"}]}, {"year": "2001", "text": "<PERSON>, English author and playwright (b. 1926)", "html": "2001 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(writer)\" title=\"<PERSON> (writer)\"><PERSON></a>, English author and playwright (b. 1926)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(writer)\" title=\"<PERSON> (writer)\"><PERSON></a>, English author and playwright (b. 1926)", "links": [{"title": "<PERSON> (writer)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(writer)"}]}, {"year": "2002", "text": "<PERSON>, German journalist, co-founded Der Spiegel (b. 1923)", "html": "2002 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German journalist, co-founded <i><a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Spiegel\"><PERSON>gel</a></i> (b. 1923)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German journalist, co-founded <i><a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Spiegel\"><PERSON></a></i> (b. 1923)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Der Spiegel", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2003", "text": "<PERSON><PERSON>, British drag queen and nightclub owner (b. 1937)", "html": "2003 - <a href=\"https://wikipedia.org/wiki/Foo_Foo_Lammar\" title=\"Foo Foo Lammar\"><PERSON><PERSON> <PERSON><PERSON></a>, British drag queen and nightclub owner (b. 1937)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Foo_Foo_Lammar\" title=\"Foo Foo Lammar\"><PERSON><PERSON> <PERSON><PERSON></a>, British drag queen and nightclub owner (b. 1937)", "links": [{"title": "<PERSON>oo <PERSON>oo <PERSON>", "link": "https://wikipedia.org/wiki/Foo_Foo_Lammar"}]}, {"year": "2004", "text": "<PERSON>, American actor and singer (b. 1919)", "html": "2004 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor and singer (b. 1919)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor and singer (b. 1919)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2005", "text": "<PERSON>, English author, screenwriter, and producer (b. 1960)", "html": "2005 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English author, screenwriter, and producer (b. 1960)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English author, screenwriter, and producer (b. 1960)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2006", "text": "<PERSON><PERSON>, Estonian chess player and engineer (b. 1930)", "html": "2006 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Estonian chess player and engineer (b. 1930)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Estonian chess player and engineer (b. 1930)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Ain<PERSON>_<PERSON>k"}]}, {"year": "2006", "text": "<PERSON>, American football player (b. 1984)", "html": "2006 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player (b. 1984)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player (b. 1984)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2006", "text": "<PERSON>, American baseball player and coach (b. 1917)", "html": "2006 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player and coach (b. 1917)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player and coach (b. 1917)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2006", "text": "<PERSON><PERSON><PERSON><PERSON><PERSON>, French journalist and politician, co-founded L'Express (b. 1924)", "html": "2006 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>ei<PERSON>\" title=\"<PERSON><PERSON><PERSON>ei<PERSON>\"><PERSON><PERSON><PERSON></a>, French journalist and politician, co-founded <i><a href=\"https://wikipedia.org/wiki/L%27Express\" title=\"L'Express\">L'Express</a></i> (b. 1924)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>ei<PERSON>\" title=\"<PERSON><PERSON><PERSON>ei<PERSON>\"><PERSON><PERSON><PERSON></a>, French journalist and politician, co-founded <i><a href=\"https://wikipedia.org/wiki/L%27Express\" title=\"L'Express\">L'Express</a></i> (b. 1924)", "links": [{"title": "<PERSON><PERSON><PERSON>hr<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>-<PERSON>_<PERSON>-<PERSON>"}, {"title": "L'Express", "link": "https://wikipedia.org/wiki/L%27Express"}]}, {"year": "2006", "text": "<PERSON>, Indian cricketer and manager (b. 1926)", "html": "2006 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Indian cricketer and manager (b. 1926)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Indian cricketer and manager (b. 1926)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2007", "text": "<PERSON>, American activist and politician (b. 1932)", "html": "2007 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American activist and politician (b. 1932)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American activist and politician (b. 1932)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2007", "text": "<PERSON>, American screenwriter and producer (b. 1920)", "html": "2007 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American screenwriter and producer (b. 1920)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American screenwriter and producer (b. 1920)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "2011", "text": "<PERSON>, American boxer (b. 1944)", "html": "2011 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American boxer (b. 1944)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American boxer (b. 1944)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2011", "text": "<PERSON><PERSON><PERSON><PERSON>, Japanese sumo wrestler (b. 1952)", "html": "2011 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Japanese sumo wrestler (b. 1952)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Japanese sumo wrestler (b. 1952)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>de"}]}, {"year": "2012", "text": "<PERSON>, American boxer (b. 1927)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American boxer (b. 1927)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American boxer (b. 1927)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Carmen_Basilio"}]}, {"year": "2012", "text": "<PERSON>, Jr., American author (b. 1950)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>27D<PERSON>,_Jr.\" class=\"mw-redirect\" title=\"<PERSON>, Jr.\"><PERSON>, Jr.</a>, American author (b. 1950)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>27D<PERSON>,_Jr.\" class=\"mw-redirect\" title=\"<PERSON>, Jr.\"><PERSON>, Jr.</a>, American author (b. 1950)", "links": [{"title": "<PERSON>, Jr.", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>%27D<PERSON>,_<PERSON>."}]}, {"year": "2012", "text": "<PERSON><PERSON>, New Zealand cricketer (b. 1940)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_Page\" title=\"<PERSON><PERSON> Page\"><PERSON><PERSON></a>, New Zealand cricketer (b. 1940)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_Page\" title=\"<PERSON><PERSON> Page\"><PERSON><PERSON></a>, New Zealand cricketer (b. 1940)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Glenys_Page"}]}, {"year": "2012", "text": "<PERSON>, Australian general (b. 1918)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian general (b. 1918)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian general (b. 1918)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2012", "text": "<PERSON>, American football player and coach (b. 1924)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player and coach (b. 1924)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player and coach (b. 1924)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2012", "text": "<PERSON>, American lawyer and politician (b. 1932)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and politician (b. 1932)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and politician (b. 1932)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "2013", "text": "<PERSON>, Irish-English journalist and author (b. 1927)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(journalist)\" title=\"<PERSON> (journalist)\"><PERSON></a>, Irish-English journalist and author (b. 1927)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(journalist)\" title=\"<PERSON> (journalist)\"><PERSON></a>, Irish-English journalist and author (b. 1927)", "links": [{"title": "<PERSON> (journalist)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>(journalist)"}]}, {"year": "2013", "text": "<PERSON>, Australian basketball player and coach (b. 1956)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(basketball)\" title=\"<PERSON> (basketball)\"><PERSON></a>, Australian basketball player and coach (b. 1956)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(basketball)\" title=\"<PERSON> (basketball)\"><PERSON></a>, Australian basketball player and coach (b. 1956)", "links": [{"title": "<PERSON> (basketball)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(basketball)"}]}, {"year": "2013", "text": "<PERSON>, English footballer and manager (b. 1914)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English footballer and manager (b. 1914)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English footballer and manager (b. 1914)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2013", "text": "<PERSON>, American publisher, founded Modern Tales (b. 1965)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American publisher, founded <i><a href=\"https://wikipedia.org/wiki/Modern_Tales\" title=\"Modern Tales\">Modern Tales</a></i> (b. 1965)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American publisher, founded <i><a href=\"https://wikipedia.org/wiki/Modern_Tales\" title=\"Modern Tales\">Modern Tales</a></i> (b. 1965)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Modern Tales", "link": "https://wikipedia.org/wiki/Modern_Tales"}]}, {"year": "2013", "text": "<PERSON>, American photographer and author (b. 1925)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(photographer)\" title=\"<PERSON> (photographer)\"><PERSON></a>, American photographer and author (b. 1925)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(photographer)\" title=\"<PERSON> (photographer)\"><PERSON></a>, American photographer and author (b. 1925)", "links": [{"title": "<PERSON> (photographer)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(photographer)"}]}, {"year": "2013", "text": "<PERSON>, German lawyer and politician (b. 1928)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German lawyer and politician (b. 1928)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German lawyer and politician (b. 1928)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2014", "text": "<PERSON>, American general (b. 1928)", "html": "2014 - <a href=\"https://wikipedia.org/wiki/Lincoln_D._<PERSON>aurer\" title=\"<PERSON> Faurer\"><PERSON></a>, American general (b. 1928)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Lincoln_D._<PERSON>aurer\" title=\"Lincoln D. Faurer\"><PERSON></a>, American general (b. 1928)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Lincoln_<PERSON><PERSON>_<PERSON><PERSON>"}]}, {"year": "2014", "text": "<PERSON><PERSON><PERSON>, Slovenian journalist and poet (b. 1931)", "html": "2014 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>%C4%8D\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Slovenian journalist and poet (b. 1931)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>%C4%8D\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Slovenian journalist and poet (b. 1931)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>%C4%8D"}]}, {"year": "2014", "text": "<PERSON>, American baseball player (b. 1952)", "html": "2014 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player (b. 1952)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player (b. 1952)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2015", "text": "<PERSON><PERSON><PERSON><PERSON>, Indian director and poet (b. 1970)", "html": "2015 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>ya_Bandopadhyay\" title=\"<PERSON>ppa<PERSON><PERSON> Bandopadhyay\"><PERSON><PERSON><PERSON><PERSON>opadhya<PERSON></a>, Indian director and poet (b. 1970)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_Bandopadhyay\" title=\"Bappa<PERSON><PERSON> Bandopadhyay\"><PERSON><PERSON><PERSON><PERSON>opadhya<PERSON></a>, Indian director and poet (b. 1970)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Bappaditya_Bandopadhyay"}]}, {"year": "2015", "text": "<PERSON><PERSON>, North Korean marshal and politician (b. 1921)", "html": "2015 - <a href=\"https://wikipedia.org/wiki/Ri_Ul-sol\" title=\"Ri Ul-sol\"><PERSON><PERSON>-<PERSON></a>, North Korean marshal and politician (b. 1921)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Ri_Ul-sol\" title=\"Ri Ul-sol\"><PERSON><PERSON>-sol</a>, North Korean marshal and politician (b. 1921)", "links": [{"title": "Ri <PERSON>sol", "link": "https://wikipedia.org/wiki/Ri_Ul-sol"}]}, {"year": "2016", "text": "<PERSON>, Canadian singer-songwriter and poet (b. 1934)", "html": "2016 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian singer-songwriter and poet (b. 1934)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian singer-songwriter and poet (b. 1934)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2016", "text": "<PERSON>, American lawyer and government official; Attorney General of the United States (1993-2001) (b. 1938)", "html": "2016 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and government official; <a href=\"https://wikipedia.org/wiki/United_States_Attorney_General\" title=\"United States Attorney General\">Attorney General of the United States (1993-2001)</a> (b. 1938)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and government official; <a href=\"https://wikipedia.org/wiki/United_States_Attorney_General\" title=\"United States Attorney General\">Attorney General of the United States (1993-2001)</a> (b. 1938)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "United States Attorney General", "link": "https://wikipedia.org/wiki/United_States_Attorney_General"}]}, {"year": "2016", "text": "<PERSON>, British singer and radio personality (b. 1921)", "html": "2016 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(broadcaster)\" title=\"<PERSON> (broadcaster)\"><PERSON></a>, British singer and radio personality (b. 1921)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(broadcaster)\" title=\"<PERSON> (broadcaster)\"><PERSON></a>, British singer and radio personality (b. 1921)", "links": [{"title": "<PERSON> (broadcaster)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(broadcaster)"}]}, {"year": "2017", "text": "<PERSON>, American baseball player (b. 1977)", "html": "2017 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player (b. 1977)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player (b. 1977)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2017", "text": "<PERSON>, Welsh Assembly minister (b. 1968)", "html": "2017 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Welsh Assembly minister (b. 1968)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Welsh Assembly minister (b. 1968)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2017", "text": "<PERSON>, American naval officer and engineer, 5th Director of NASA Marshall Space Flight Center (b. 1936)", "html": "2017 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON> Jr.\"><PERSON> Jr.</a>, American naval officer and engineer, 5th Director of NASA Marshall Space Flight Center (b. 1936)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON>.</a>, American naval officer and engineer, 5th Director of NASA Marshall Space Flight Center (b. 1936)", "links": [{"title": "<PERSON> Jr.", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>_<PERSON>."}]}, {"year": "2019", "text": "<PERSON><PERSON>, American physician, author, and pioneer in occupational and environmental health (b. 1930)", "html": "2019 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American physician, author, and pioneer in occupational and environmental health (b. 1930)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American physician, author, and pioneer in occupational and environmental health (b. 1930)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "2020", "text": "<PERSON>, former Chief Rabbi of the Commonwealth and member of the House of Lords (b. 1948)", "html": "2020 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON> Sa<PERSON>\"><PERSON></a>, former <a href=\"https://wikipedia.org/wiki/Chief_Rabbi\" title=\"Chief Rabbi\">Chief Rabbi</a> of the <a href=\"https://wikipedia.org/wiki/Commonwealth_of_Nations\" title=\"Commonwealth of Nations\">Commonwealth</a> and member of the <a href=\"https://wikipedia.org/wiki/House_of_Lords\" title=\"House of Lords\">House of Lords</a> (b. 1948)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, former <a href=\"https://wikipedia.org/wiki/Chief_Rabbi\" title=\"Chief Rabbi\">Chief Rabbi</a> of the <a href=\"https://wikipedia.org/wiki/Commonwealth_of_Nations\" title=\"Commonwealth of Nations\">Commonwealth</a> and member of the <a href=\"https://wikipedia.org/wiki/House_of_Lords\" title=\"House of Lords\">House of Lords</a> (b. 1948)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Chief Rabbi", "link": "https://wikipedia.org/wiki/Chief_Rabbi"}, {"title": "Commonwealth of Nations", "link": "https://wikipedia.org/wiki/Commonwealth_of_Nations"}, {"title": "House of Lords", "link": "https://wikipedia.org/wiki/House_of_Lords"}]}, {"year": "2021", "text": "<PERSON>, American actor (b. 1936)", "html": "2021 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor (b. 1936)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor (b. 1936)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2023", "text": "<PERSON>, American astronaut (b. 1928)", "html": "2023 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American astronaut (b. 1928)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American astronaut (b. 1928)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "2024", "text": "<PERSON>, American writer (b. 1945)", "html": "2024 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American writer (b. 1945)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American writer (b. 1945)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}]}}