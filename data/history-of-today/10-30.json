{"date": "October 30", "url": "https://wikipedia.org/wiki/October_30", "data": {"Events": [{"year": "637", "text": "Arab-Byzantine wars: Antioch surrenders to the Rashidun Caliphate after the Battle of the Iron Bridge.", "html": "637 - <a href=\"https://wikipedia.org/wiki/Arab%E2%80%93Byzantine_wars\" title=\"Arab-Byzantine wars\">Arab-Byzantine wars</a>: <a href=\"https://wikipedia.org/wiki/Antioch\" title=\"Antioch\">Antioch</a> surrenders to the <a href=\"https://wikipedia.org/wiki/Rashidun_Caliphate\" title=\"Rashidun Caliphate\">Rashidun Caliphate</a> after the <a href=\"https://wikipedia.org/wiki/Battle_of_the_Iron_Bridge\" title=\"Battle of the Iron Bridge\">Battle of the Iron Bridge</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Arab%E2%80%93Byzantine_wars\" title=\"Arab-Byzantine wars\">Arab-Byzantine wars</a>: <a href=\"https://wikipedia.org/wiki/Antioch\" title=\"Antioch\">Antioch</a> surrenders to the <a href=\"https://wikipedia.org/wiki/Rashidun_Caliphate\" title=\"Rashidun Caliphate\">Rashidun Caliphate</a> after the <a href=\"https://wikipedia.org/wiki/Battle_of_the_Iron_Bridge\" title=\"Battle of the Iron Bridge\">Battle of the Iron Bridge</a>.", "links": [{"title": "Arab-Byzantine wars", "link": "https://wikipedia.org/wiki/Arab%E2%80%93Byzantine_wars"}, {"title": "Antioch", "link": "https://wikipedia.org/wiki/Antioch"}, {"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Rashidun_Caliphate"}, {"title": "Battle of the Iron Bridge", "link": "https://wikipedia.org/wiki/Battle_of_the_Iron_Bridge"}]}, {"year": "758", "text": "Guangzhou is sacked by Arab and Persian pirates.", "html": "758 - <a href=\"https://wikipedia.org/wiki/Guangzhou\" title=\"Guangzhou\">Guangzhou</a> is sacked by Arab and Persian pirates.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Guangzhou\" title=\"Guangzhou\">Guangzhou</a> is sacked by Arab and Persian pirates.", "links": [{"title": "Guangzhou", "link": "https://wikipedia.org/wiki/Guangzhou"}]}, {"year": "1137", "text": "<PERSON><PERSON><PERSON> of Apulia defeats <PERSON> of Sicily at the Battle of Rignano, securing his position as duke until his death two years later.", "html": "1137 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_of_Apulia\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON> of Apulia\"><PERSON><PERSON><PERSON> of Apulia</a> defeats <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Sicily\" title=\"<PERSON> of Sicily\"><PERSON> of Sicily</a> at the <a href=\"https://wikipedia.org/wiki/Battle_of_Rignano\" title=\"Battle of Rignano\">Battle of Rignano</a>, securing his position as duke until his death two years later.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_of_Apulia\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON> of Apulia\"><PERSON><PERSON><PERSON> of Apulia</a> defeats <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Sicily\" title=\"<PERSON> of Sicily\"><PERSON> of Sicily</a> at the <a href=\"https://wikipedia.org/wiki/Battle_of_Rignano\" title=\"Battle of Rignano\">Battle of Rignano</a>, securing his position as duke until his death two years later.", "links": [{"title": "<PERSON><PERSON><PERSON> of Apulia", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_of_Apulia"}, {"title": "<PERSON> of Sicily", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Sicily"}, {"title": "Battle of Rignano", "link": "https://wikipedia.org/wiki/Battle_of_Rignano"}]}, {"year": "1270", "text": "The Eighth Crusade ends by an agreement between <PERSON> of Anjou (replacing his deceased brother King <PERSON> of France) and the Hafsid dynasty of Tunis, Tunisia.", "html": "1270 - The <a href=\"https://wikipedia.org/wiki/Eighth_Crusade\" title=\"Eighth Crusade\">Eighth Crusade</a> ends by an agreement between <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_Anjou\" title=\"<PERSON> of Anjou\"><PERSON> of Anjou</a> (replacing his deceased brother <a href=\"https://wikipedia.org/wiki/King_<PERSON>_<PERSON>_of_France\" class=\"mw-redirect\" title=\"King <PERSON> IX of France\">King <PERSON> IX of France</a>) and the <a href=\"https://wikipedia.org/wiki/Hafsid_dynasty\" title=\"Hafsid dynasty\">Hafsid dynasty</a> of <a href=\"https://wikipedia.org/wiki/Tunis\" title=\"Tunis\">Tunis</a>, Tunisia.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Eighth_Crusade\" title=\"Eighth Crusade\">Eighth Crusade</a> ends by an agreement between <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Anjou\" title=\"<PERSON> of Anjou\"><PERSON> of Anjou</a> (replacing his deceased brother <a href=\"https://wikipedia.org/wiki/King_<PERSON>_IX_of_France\" class=\"mw-redirect\" title=\"King <PERSON> IX of France\">King <PERSON> IX of France</a>) and the <a href=\"https://wikipedia.org/wiki/Hafsid_dynasty\" title=\"Hafsid dynasty\">Hafsid dynasty</a> of <a href=\"https://wikipedia.org/wiki/Tunis\" title=\"Tunis\">Tunis</a>, Tunisia.", "links": [{"title": "Eighth Crusade", "link": "https://wikipedia.org/wiki/Eighth_Crusade"}, {"title": "<PERSON> of Anjou", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_Anjou"}, {"title": "King <PERSON> of France", "link": "https://wikipedia.org/wiki/King_<PERSON>_<PERSON>_of_France"}, {"title": "Hafsid dynasty", "link": "https://wikipedia.org/wiki/Ha<PERSON>id_dynasty"}, {"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>nis"}]}, {"year": "1340", "text": "Reconquista: Portuguese and Castilian forces halt a Muslim invasion at the Battle of Río Salado.", "html": "1340 - <i><a href=\"https://wikipedia.org/wiki/Reconquista\" title=\"Reconquista\">Reconquista</a></i>: <a href=\"https://wikipedia.org/wiki/Portugal\" title=\"Portugal\">Portuguese</a> and <a href=\"https://wikipedia.org/wiki/Crown_of_Castile\" title=\"Crown of Castile\">Castilian</a> forces halt a Muslim invasion at the <a href=\"https://wikipedia.org/wiki/Battle_of_R%C3%ADo_Salado\" title=\"Battle of Río Salado\">Battle of Río Salado</a>.", "no_year_html": "<i><a href=\"https://wikipedia.org/wiki/Reconquista\" title=\"Reconquista\">Reconquista</a></i>: <a href=\"https://wikipedia.org/wiki/Portugal\" title=\"Portugal\">Portuguese</a> and <a href=\"https://wikipedia.org/wiki/Crown_of_Castile\" title=\"Crown of Castile\">Castilian</a> forces halt a Muslim invasion at the <a href=\"https://wikipedia.org/wiki/Battle_of_R%C3%ADo_Salado\" title=\"Battle of Río Salado\">Battle of Río Salado</a>.", "links": [{"title": "Reconquista", "link": "https://wikipedia.org/wiki/Reconquista"}, {"title": "Portugal", "link": "https://wikipedia.org/wiki/Portugal"}, {"title": "Crown of Castile", "link": "https://wikipedia.org/wiki/Crown_of_Castile"}, {"title": "Battle of Río Salado", "link": "https://wikipedia.org/wiki/Battle_of_R%C3%ADo_Salado"}]}, {"year": "1657", "text": "Anglo-Spanish War: Spanish forces fail to retake Jamaica at the Battle of Ocho Rios.", "html": "1657 - <a href=\"https://wikipedia.org/wiki/Anglo-Spanish_War_(1654%E2%80%931660)\" title=\"Anglo-Spanish War (1654-1660)\">Anglo-Spanish War</a>: Spanish forces fail to retake <a href=\"https://wikipedia.org/wiki/Jamaica\" title=\"Jamaica\">Jamaica</a> at the <a href=\"https://wikipedia.org/wiki/Battle_of_Ocho_Rios\" title=\"Battle of Ocho Rios\">Battle of Ocho Rios</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Anglo-Spanish_War_(1654%E2%80%931660)\" title=\"Anglo-Spanish War (1654-1660)\">Anglo-Spanish War</a>: Spanish forces fail to retake <a href=\"https://wikipedia.org/wiki/Jamaica\" title=\"Jamaica\">Jamaica</a> at the <a href=\"https://wikipedia.org/wiki/Battle_of_Ocho_Rios\" title=\"Battle of Ocho Rios\">Battle of Ocho Rios</a>.", "links": [{"title": "Anglo-Spanish War (1654-1660)", "link": "https://wikipedia.org/wiki/Anglo-Spanish_War_(1654%E2%80%931660)"}, {"title": "Jamaica", "link": "https://wikipedia.org/wiki/Jamaica"}, {"title": "Battle of Ocho Rios", "link": "https://wikipedia.org/wiki/Battle_of_Ocho_Rios"}]}, {"year": "1806", "text": "War of the Fourth Coalition: Convinced that he is facing a much larger force, Prussian General <PERSON>, commanding 5,300 men, surrenders the city of Stettin to 800 French soldiers.", "html": "1806 - <a href=\"https://wikipedia.org/wiki/War_of_the_Fourth_Coalition\" title=\"War of the Fourth Coalition\">War of the Fourth Coalition</a>: Convinced that he is facing a much larger force, Prussian General <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\">von <PERSON></a>, commanding 5,300 men, <a href=\"https://wikipedia.org/wiki/Capitulation_of_Stettin\" title=\"Capitulation of Stettin\">surrenders the city of Stettin</a> to 800 French soldiers.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/War_of_the_Fourth_Coalition\" title=\"War of the Fourth Coalition\">War of the Fourth Coalition</a>: Convinced that he is facing a much larger force, Prussian General <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\">von <PERSON></a>, commanding 5,300 men, <a href=\"https://wikipedia.org/wiki/Capitulation_of_Stettin\" title=\"Capitulation of Stettin\">surrenders the city of Stettin</a> to 800 French soldiers.", "links": [{"title": "War of the Fourth Coalition", "link": "https://wikipedia.org/wiki/War_of_the_Fourth_Coalition"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>_<PERSON>"}, {"title": "Capitulation of Stettin", "link": "https://wikipedia.org/wiki/Capitulation_of_Stettin"}]}, {"year": "1817", "text": "<PERSON><PERSON><PERSON> becomes President of the Third Republic of Venezuela.", "html": "1817 - <a href=\"https://wikipedia.org/wiki/Sim%C3%B3n_Bol%C3%ADvar\" title=\"<PERSON><PERSON><PERSON> Bolívar\"><PERSON><PERSON><PERSON></a> becomes President of the <a href=\"https://wikipedia.org/wiki/Third_Republic_of_Venezuela\" title=\"Third Republic of Venezuela\">Third Republic of Venezuela</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Sim%C3%B3n_Bol%C3%ADvar\" title=\"<PERSON><PERSON><PERSON> Bolívar\"><PERSON><PERSON><PERSON></a> becomes President of the <a href=\"https://wikipedia.org/wiki/Third_Republic_of_Venezuela\" title=\"Third Republic of Venezuela\">Third Republic of Venezuela</a>.", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Sim%C3%B3n_Bol%C3%ADvar"}, {"title": "Third Republic of Venezuela", "link": "https://wikipedia.org/wiki/Third_Republic_of_Venezuela"}]}, {"year": "1831", "text": "<PERSON> is arrested for leading the bloodiest slave rebellion in United States history.", "html": "1831 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> is arrested for leading the bloodiest <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%27s_slave_rebellion\" class=\"mw-redirect\" title=\"<PERSON>'s slave rebellion\">slave rebellion</a> in United States history.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> is arrested for leading the bloodiest <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%27s_slave_rebellion\" class=\"mw-redirect\" title=\"<PERSON>'s slave rebellion\">slave rebellion</a> in United States history.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "<PERSON>'s slave rebellion", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>%27s_slave_rebellion"}]}, {"year": "1863", "text": "Danish Prince <PERSON><PERSON><PERSON><PERSON> arrives in Athens to assume his throne as <PERSON>, King of the Hellenes.", "html": "1863 - Danish Prince <PERSON><PERSON><PERSON><PERSON> arrives in Athens to assume his throne as <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Greece\" title=\"<PERSON> of Greece\"><PERSON></a>, King of the Hellenes.", "no_year_html": "Danish Prince <PERSON><PERSON><PERSON><PERSON> arrives in Athens to assume his throne as <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Greece\" title=\"<PERSON> of Greece\"><PERSON></a>, King of the Hellenes.", "links": [{"title": "<PERSON> of Greece", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Greece"}]}, {"year": "1864", "text": "Second War of Schleswig: The Treaty of Vienna is signed, by which Denmark relinquishes one province each to Prussia and Austria.", "html": "1864 - <a href=\"https://wikipedia.org/wiki/Second_War_of_Schleswig\" class=\"mw-redirect\" title=\"Second War of Schleswig\">Second War of Schleswig</a>: The <a href=\"https://wikipedia.org/wiki/Treaty_of_Vienna_(1864)\" title=\"Treaty of Vienna (1864)\">Treaty of Vienna</a> is signed, by which Denmark relinquishes one province each to <a href=\"https://wikipedia.org/wiki/Prussia\" title=\"Prussia\">Prussia</a> and <a href=\"https://wikipedia.org/wiki/Austria\" title=\"Austria\">Austria</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Second_War_of_Schleswig\" class=\"mw-redirect\" title=\"Second War of Schleswig\">Second War of Schleswig</a>: The <a href=\"https://wikipedia.org/wiki/Treaty_of_Vienna_(1864)\" title=\"Treaty of Vienna (1864)\">Treaty of Vienna</a> is signed, by which Denmark relinquishes one province each to <a href=\"https://wikipedia.org/wiki/Prussia\" title=\"Prussia\">Prussia</a> and <a href=\"https://wikipedia.org/wiki/Austria\" title=\"Austria\">Austria</a>.", "links": [{"title": "Second War of Schleswig", "link": "https://wikipedia.org/wiki/Second_War_of_Schleswig"}, {"title": "Treaty of Vienna (1864)", "link": "https://wikipedia.org/wiki/Treaty_of_Vienna_(1864)"}, {"title": "Prussia", "link": "https://wikipedia.org/wiki/Prussia"}, {"title": "Austria", "link": "https://wikipedia.org/wiki/Austria"}]}, {"year": "1858", "text": "Approximately 20 people die in Bradford, England, UK, after being poisoned from ingesting sweets that had been accidentally adulterated with arsenic trioxide.", "html": "1858 - Approximately 20 people die in <a href=\"https://wikipedia.org/wiki/Bradford\" title=\"Bradford\">Bradford</a>, <a href=\"https://wikipedia.org/wiki/England\" title=\"England\">England</a>, UK, after being <a href=\"https://wikipedia.org/wiki/1858_Bradford_sweets_poisoning\" title=\"1858 Bradford sweets poisoning\">poisoned</a> from ingesting <a href=\"https://wikipedia.org/wiki/Candy\" title=\"Candy\">sweets</a> that had been accidentally <a href=\"https://wikipedia.org/wiki/Adulterant\" title=\"Adulterant\">adulterated</a> with <a href=\"https://wikipedia.org/wiki/Arsenic_trioxide\" title=\"Arsenic trioxide\">arsenic trioxide</a>.", "no_year_html": "Approximately 20 people die in <a href=\"https://wikipedia.org/wiki/Bradford\" title=\"Bradford\">Bradford</a>, <a href=\"https://wikipedia.org/wiki/England\" title=\"England\">England</a>, UK, after being <a href=\"https://wikipedia.org/wiki/1858_Bradford_sweets_poisoning\" title=\"1858 Bradford sweets poisoning\">poisoned</a> from ingesting <a href=\"https://wikipedia.org/wiki/Candy\" title=\"Candy\">sweets</a> that had been accidentally <a href=\"https://wikipedia.org/wiki/Adulterant\" title=\"Adulterant\">adulterated</a> with <a href=\"https://wikipedia.org/wiki/Arsenic_trioxide\" title=\"Arsenic trioxide\">arsenic trioxide</a>.", "links": [{"title": "Bradford", "link": "https://wikipedia.org/wiki/Bradford"}, {"title": "England", "link": "https://wikipedia.org/wiki/England"}, {"title": "1858 Bradford sweets poisoning", "link": "https://wikipedia.org/wiki/1858_Bradford_sweets_poisoning"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Candy"}, {"title": "Adulterant", "link": "https://wikipedia.org/wiki/Adulterant"}, {"title": "Arsenic trioxide", "link": "https://wikipedia.org/wiki/Arsenic_trioxide"}]}, {"year": "1888", "text": "The Rudd Concession is granted by Matabeleland to agents of Cecil <PERSON>.", "html": "1888 - The <a href=\"https://wikipedia.org/wiki/Rudd_Concession\" title=\"Rudd Concession\">Rudd Concession</a> is granted by <a href=\"https://wikipedia.org/wiki/Matabeleland\" title=\"Matabeleland\">Matabeleland</a> to agents of <a href=\"https://wikipedia.org/wiki/Cecil_<PERSON>\" title=\"Cecil Rhodes\"><PERSON></a>.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Rudd_Concession\" title=\"Rudd Concession\">Rudd Concession</a> is granted by <a href=\"https://wikipedia.org/wiki/Matabeleland\" title=\"Matabeleland\">Matabeleland</a> to agents of <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Cecil Rhodes\"><PERSON></a>.", "links": [{"title": "<PERSON><PERSON> Concession", "link": "https://wikipedia.org/wiki/Rudd_Concession"}, {"title": "Matabeleland", "link": "https://wikipedia.org/wiki/Matabeleland"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1905", "text": "Tsar <PERSON> issues the October Manifesto, nominally granting the Russian peoples basic civil liberties and the right to form a duma. (October 17 in the Julian calendar)", "html": "1905 - <a href=\"https://wikipedia.org/wiki/Tsar_<PERSON>_II\" class=\"mw-redirect\" title=\"Tsar Nicholas II\">Tsar <PERSON> II</a> issues the <a href=\"https://wikipedia.org/wiki/October_Manifesto\" title=\"October Manifesto\">October Manifesto</a>, nominally granting the Russian peoples basic civil liberties and the right to form a <a href=\"https://wikipedia.org/wiki/Duma\" title=\"Duma\">duma</a>. (October 17 in the Julian calendar)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Tsar_<PERSON>_II\" class=\"mw-redirect\" title=\"Tsar Nicholas II\">Tsar <PERSON> II</a> issues the <a href=\"https://wikipedia.org/wiki/October_Manifesto\" title=\"October Manifesto\">October Manifesto</a>, nominally granting the Russian peoples basic civil liberties and the right to form a <a href=\"https://wikipedia.org/wiki/Duma\" title=\"Duma\">duma</a>. (October 17 in the Julian calendar)", "links": [{"title": "Tsar <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "October Manifesto", "link": "https://wikipedia.org/wiki/October_Manifesto"}, {"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>"}]}, {"year": "1918", "text": "World War I: The Ottoman Empire signs the Armistice of Mudros with the Allies.", "html": "1918 - <a href=\"https://wikipedia.org/wiki/World_War_I\" title=\"World War I\">World War I</a>: The Ottoman Empire signs the <a href=\"https://wikipedia.org/wiki/Armistice_of_Mudros\" title=\"Armistice of Mudros\">Armistice of Mudros</a> with the Allies.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/World_War_I\" title=\"World War I\">World War I</a>: The Ottoman Empire signs the <a href=\"https://wikipedia.org/wiki/Armistice_of_Mudros\" title=\"Armistice of Mudros\">Armistice of Mudros</a> with the Allies.", "links": [{"title": "World War I", "link": "https://wikipedia.org/wiki/World_War_I"}, {"title": "Armistice of Mudros", "link": "https://wikipedia.org/wiki/Armistice_of_Mudros"}]}, {"year": "1918", "text": "World War I: Lands of the Crown of Saint Stephen, a state union of Kingdom of Hungary and Triune Kingdom of Croatia, Slavonia and Dalmatia is abolished with decisions of Croatian and Hungarian parliaments", "html": "1918 - World War I: <a href=\"https://wikipedia.org/wiki/Lands_of_the_Crown_of_Saint_Stephen\" title=\"Lands of the Crown of Saint Stephen\">Lands of the Crown of Saint Stephen</a>, a state union of <a href=\"https://wikipedia.org/wiki/Kingdom_of_Hungary\" title=\"Kingdom of Hungary\">Kingdom of Hungary</a> and <a href=\"https://wikipedia.org/wiki/Triune_Kingdom\" title=\"Triune Kingdom\">Triune Kingdom</a> of Croatia, Slavonia and Dalmatia is abolished with decisions of <a href=\"https://wikipedia.org/wiki/Croatian_parliament\" class=\"mw-redirect\" title=\"Croatian parliament\">Croatian</a> and <a href=\"https://wikipedia.org/wiki/Hungarian_parliament\" class=\"mw-redirect\" title=\"Hungarian parliament\">Hungarian parliaments</a>", "no_year_html": "World War I: <a href=\"https://wikipedia.org/wiki/Lands_of_the_Crown_of_Saint_Stephen\" title=\"Lands of the Crown of Saint Stephen\">Lands of the Crown of Saint Stephen</a>, a state union of <a href=\"https://wikipedia.org/wiki/Kingdom_of_Hungary\" title=\"Kingdom of Hungary\">Kingdom of Hungary</a> and <a href=\"https://wikipedia.org/wiki/Triune_Kingdom\" title=\"Triune Kingdom\">Triune Kingdom</a> of Croatia, Slavonia and Dalmatia is abolished with decisions of <a href=\"https://wikipedia.org/wiki/Croatian_parliament\" class=\"mw-redirect\" title=\"Croatian parliament\">Croatian</a> and <a href=\"https://wikipedia.org/wiki/Hungarian_parliament\" class=\"mw-redirect\" title=\"Hungarian parliament\">Hungarian parliaments</a>", "links": [{"title": "Lands of the Crown of Saint Stephen", "link": "https://wikipedia.org/wiki/Lands_of_the_Crown_of_Saint_Stephen"}, {"title": "Kingdom of Hungary", "link": "https://wikipedia.org/wiki/Kingdom_of_Hungary"}, {"title": "Triune Kingdom", "link": "https://wikipedia.org/wiki/Triune_Kingdom"}, {"title": "Croatian parliament", "link": "https://wikipedia.org/wiki/Croatian_parliament"}, {"title": "Hungarian parliament", "link": "https://wikipedia.org/wiki/Hungarian_parliament"}]}, {"year": "1920", "text": "The Communist Party of Australia is founded in Sydney.", "html": "1920 - The <a href=\"https://wikipedia.org/wiki/Communist_Party_of_Australia\" title=\"Communist Party of Australia\">Communist Party of Australia</a> is founded in Sydney.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Communist_Party_of_Australia\" title=\"Communist Party of Australia\">Communist Party of Australia</a> is founded in Sydney.", "links": [{"title": "Communist Party of Australia", "link": "https://wikipedia.org/wiki/Communist_Party_of_Australia"}]}, {"year": "1938", "text": "<PERSON><PERSON> broadcasts a radio adaptation of <PERSON><PERSON> <PERSON><PERSON>'s The War of the Worlds, causing a massive panic in some of the audience in the United States.", "html": "1938 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> Welles\"><PERSON><PERSON></a> broadcasts a radio adaptation of <PERSON><PERSON> <PERSON><PERSON>'s <i><a href=\"https://wikipedia.org/wiki/The_War_of_the_Worlds_(1938_radio_drama)\" title=\"The War of the Worlds (1938 radio drama)\">The War of the Worlds</a></i>, causing a massive panic in some of the audience in the United States.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> Welles\"><PERSON><PERSON></a> broadcasts a radio adaptation of <PERSON><PERSON> <PERSON><PERSON>'s <i><a href=\"https://wikipedia.org/wiki/The_War_of_the_Worlds_(1938_radio_drama)\" title=\"The War of the Worlds (1938 radio drama)\">The War of the Worlds</a></i>, causing a massive panic in some of the audience in the United States.", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}, {"title": "The War of the Worlds (1938 radio drama)", "link": "https://wikipedia.org/wiki/The_War_of_the_Worlds_(1938_radio_drama)"}]}, {"year": "1941", "text": "President <PERSON> approves $1 billion in Lend-Lease aid to the Allied nations.", "html": "1941 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\">President <PERSON></a> approves $1 billion in <a href=\"https://wikipedia.org/wiki/Lend-Lease\" title=\"Lend-Lease\">Lend-Lease</a> aid to the Allied nations.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\">President <PERSON></a> approves $1 billion in <a href=\"https://wikipedia.org/wiki/Lend-Lease\" title=\"Lend-Lease\">Lend-Lease</a> aid to the Allied nations.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "Lend-Lease", "link": "https://wikipedia.org/wiki/Lend-Lease"}]}, {"year": "1941", "text": "Holocaust: Fifteen hundred Jews from Pidhaytsi are sent by Nazis to Bełżec extermination camp.", "html": "1941 - <a href=\"https://wikipedia.org/wiki/Holocaust\" class=\"mw-redirect\" title=\"Holocaust\">Holocaust</a>: Fifteen hundred Jews from <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a> are sent by Nazis to <a href=\"https://wikipedia.org/wiki/Be%C5%82%C5%BCec_extermination_camp\" class=\"mw-redirect\" title=\"Bełżec extermination camp\">Bełżec extermination camp</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Holocaust\" class=\"mw-redirect\" title=\"Holocaust\">Holocaust</a>: Fifteen hundred Jews from <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a> are sent by Nazis to <a href=\"https://wikipedia.org/wiki/Be%C5%82%C5%BCec_extermination_camp\" class=\"mw-redirect\" title=\"Bełżec extermination camp\">Bełżec extermination camp</a>.", "links": [{"title": "Holocaust", "link": "https://wikipedia.org/wiki/Holocaust"}, {"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>"}, {"title": "Bełżec extermination camp", "link": "https://wikipedia.org/wiki/Be%C5%82%C5%BCec_extermination_camp"}]}, {"year": "1942", "text": "World War II: Lt. <PERSON> and Able Seaman <PERSON> drown while taking code books from the sinking German submarine U-559.", "html": "1942 - <a href=\"https://wikipedia.org/wiki/World_War_II\" title=\"World War II\">World War II</a>: Lt. <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> and <PERSON><PERSON> <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> drown while taking code books from the sinking <a href=\"https://wikipedia.org/wiki/German_submarine_U-559\" title=\"German submarine U-559\">German submarine U-559</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/World_War_II\" title=\"World War II\">World War II</a>: Lt. <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> and <PERSON><PERSON> <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> drown while taking code books from the sinking <a href=\"https://wikipedia.org/wiki/German_submarine_U-559\" title=\"German submarine U-559\">German submarine U-559</a>.", "links": [{"title": "World War II", "link": "https://wikipedia.org/wiki/World_War_II"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "German submarine U-559", "link": "https://wikipedia.org/wiki/German_submarine_U-559"}]}, {"year": "1944", "text": "Holocaust: <PERSON> and <PERSON><PERSON> are deported from Auschwitz to the Bergen-Belsen concentration camp, where they die from disease the following year, shortly before the end of WWII.", "html": "1944 - Holocaust: <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> and <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_Frank\" title=\"Margot Frank\">Mar<PERSON></a> are deported from <a href=\"https://wikipedia.org/wiki/Auschwitz_concentration_camp\" title=\"Auschwitz concentration camp\">Auschwitz</a> to the <a href=\"https://wikipedia.org/wiki/Bergen-Belsen_concentration_camp\" title=\"Bergen-Belsen concentration camp\">Bergen-Belsen concentration camp</a>, where they die from disease the following year, shortly before the end of WWII.", "no_year_html": "Holocaust: <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> and <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_Frank\" title=\"Margot Frank\"><PERSON><PERSON></a> are deported from <a href=\"https://wikipedia.org/wiki/Auschwitz_concentration_camp\" title=\"Auschwitz concentration camp\">Auschwitz</a> to the <a href=\"https://wikipedia.org/wiki/Bergen-Belsen_concentration_camp\" title=\"Bergen-Belsen concentration camp\">Bergen-Belsen concentration camp</a>, where they die from disease the following year, shortly before the end of WWII.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}, {"title": "Auschwitz concentration camp", "link": "https://wikipedia.org/wiki/Auschwitz_concentration_camp"}, {"title": "Bergen-Belsen concentration camp", "link": "https://wikipedia.org/wiki/Bergen-Belsen_concentration_camp"}]}, {"year": "1945", "text": "<PERSON> of the Kansas City Monarchs signs a contract for the Brooklyn Dodgers, breaking the baseball color line.", "html": "1945 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> of the <a href=\"https://wikipedia.org/wiki/Kansas_City_Monarchs\" title=\"Kansas City Monarchs\">Kansas City Monarchs</a> signs a contract for the <a href=\"https://wikipedia.org/wiki/History_of_the_Brooklyn_Dodgers\" class=\"mw-redirect\" title=\"History of the Brooklyn Dodgers\">Brooklyn Dodgers</a>, breaking the baseball color line.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> of the <a href=\"https://wikipedia.org/wiki/Kansas_City_Monarchs\" title=\"Kansas City Monarchs\">Kansas City Monarchs</a> signs a contract for the <a href=\"https://wikipedia.org/wiki/History_of_the_Brooklyn_Dodgers\" class=\"mw-redirect\" title=\"History of the Brooklyn Dodgers\">Brooklyn Dodgers</a>, breaking the baseball color line.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Kansas City Monarchs", "link": "https://wikipedia.org/wiki/Kansas_City_Monarchs"}, {"title": "History of the Brooklyn Dodgers", "link": "https://wikipedia.org/wiki/History_of_the_Brooklyn_Dodgers"}]}, {"year": "1947", "text": "The General Agreement on Tariffs and Trade (GATT), the foundation of the World Trade Organization (WTO), is founded.", "html": "1947 - The <a href=\"https://wikipedia.org/wiki/General_Agreement_on_Tariffs_and_Trade\" title=\"General Agreement on Tariffs and Trade\">General Agreement on Tariffs and Trade</a> (GATT), the foundation of the <a href=\"https://wikipedia.org/wiki/World_Trade_Organization\" title=\"World Trade Organization\">World Trade Organization</a> (WTO), is founded.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/General_Agreement_on_Tariffs_and_Trade\" title=\"General Agreement on Tariffs and Trade\">General Agreement on Tariffs and Trade</a> (GATT), the foundation of the <a href=\"https://wikipedia.org/wiki/World_Trade_Organization\" title=\"World Trade Organization\">World Trade Organization</a> (WTO), is founded.", "links": [{"title": "General Agreement on Tariffs and Trade", "link": "https://wikipedia.org/wiki/General_Agreement_on_Tariffs_and_Trade"}, {"title": "World Trade Organization", "link": "https://wikipedia.org/wiki/World_Trade_Organization"}]}, {"year": "1948", "text": "A luzzu fishing boat overloaded with passengers capsizes and sinks in the Gozo Channel off Qala, Gozo, Malta, killing 23 of the 27 people on board.", "html": "1948 - A <i><a href=\"https://wikipedia.org/wiki/Luzzu\" title=\"Luzzu\">luzzu</a></i> fishing boat overloaded with passengers <a href=\"https://wikipedia.org/wiki/1948_Gozo_luzzu_disaster\" title=\"1948 Gozo luzzu disaster\">capsizes and sinks</a> in the <a href=\"https://wikipedia.org/wiki/Gozo_Channel\" title=\"Gozo Channel\">Gozo Channel</a> off <a href=\"https://wikipedia.org/wiki/Qala,_Malta\" title=\"Qala, Malta\">Qala</a>, <a href=\"https://wikipedia.org/wiki/Gozo\" title=\"Gozo\">Gozo</a>, <a href=\"https://wikipedia.org/wiki/Crown_Colony_of_Malta\" title=\"Crown Colony of Malta\">Malta</a>, killing 23 of the 27 people on board.", "no_year_html": "A <i><a href=\"https://wikipedia.org/wiki/Luzzu\" title=\"Luzzu\">luzzu</a></i> fishing boat overloaded with passengers <a href=\"https://wikipedia.org/wiki/1948_Gozo_luzzu_disaster\" title=\"1948 Gozo luzzu disaster\">capsizes and sinks</a> in the <a href=\"https://wikipedia.org/wiki/Gozo_Channel\" title=\"Gozo Channel\">Gozo Channel</a> off <a href=\"https://wikipedia.org/wiki/Qala,_Malta\" title=\"Qala, Malta\">Qala</a>, <a href=\"https://wikipedia.org/wiki/Gozo\" title=\"Gozo\">Gozo</a>, <a href=\"https://wikipedia.org/wiki/Crown_Colony_of_Malta\" title=\"Crown Colony of Malta\">Malta</a>, killing 23 of the 27 people on board.", "links": [{"title": "Luzzu", "link": "https://wikipedia.org/wiki/Luzzu"}, {"title": "1948 Gozo luzzu disaster", "link": "https://wikipedia.org/wiki/1948_Gozo_luzzu_disaster"}, {"title": "Gozo Channel", "link": "https://wikipedia.org/wiki/Gozo_Channel"}, {"title": "Qala, Malta", "link": "https://wikipedia.org/wiki/Qala,_Malta"}, {"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Gozo"}, {"title": "Crown Colony of Malta", "link": "https://wikipedia.org/wiki/Crown_Colony_of_Malta"}]}, {"year": "1953", "text": "President <PERSON> approves the top-secret document NSC 162/2 concerning the maintenance of a strong nuclear deterrent force against the Soviet Union.", "html": "1953 - <a href=\"https://wikipedia.org/wiki/President_<PERSON>\" class=\"mw-redirect\" title=\"President <PERSON>\">President <PERSON></a> approves the top-secret document <a href=\"https://wikipedia.org/wiki/NSC_162/2\" title=\"NSC 162/2\">NSC 162/2</a> concerning the maintenance of a strong nuclear deterrent force against the Soviet Union.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/President_<PERSON>\" class=\"mw-redirect\" title=\"President <PERSON>\">President <PERSON></a> approves the top-secret document <a href=\"https://wikipedia.org/wiki/NSC_162/2\" title=\"NSC 162/2\">NSC 162/2</a> concerning the maintenance of a strong nuclear deterrent force against the Soviet Union.", "links": [{"title": "President <PERSON>", "link": "https://wikipedia.org/wiki/President_<PERSON>"}, {"title": "NSC 162/2", "link": "https://wikipedia.org/wiki/NSC_162/2"}]}, {"year": "1956", "text": "Hungarian Revolution: The government of Imre Nagy recognizes newly established revolutionary workers' councils. Army officer <PERSON><PERSON><PERSON> leads anti-Soviet militias in an attack on the headquarters of the Hungarian Working People's Party.", "html": "1956 - <a href=\"https://wikipedia.org/wiki/Hungarian_Revolution_of_1956\" title=\"Hungarian Revolution of 1956\">Hungarian Revolution</a>: The <a href=\"https://wikipedia.org/wiki/Governments_of_Imre_Nagy\" title=\"Governments of Imre Nagy\">government of Imre Nagy</a> recognizes newly established revolutionary workers' councils. Army officer <a href=\"https://wikipedia.org/wiki/B%C3%A9la_Kir%C3%A1ly\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a> leads anti-Soviet <a href=\"https://wikipedia.org/wiki/Militia\" title=\"Militia\">militias</a> in an attack on the headquarters of the <a href=\"https://wikipedia.org/wiki/Hungarian_Working_People%27s_Party\" title=\"Hungarian Working People's Party\">Hungarian Working People's Party</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Hungarian_Revolution_of_1956\" title=\"Hungarian Revolution of 1956\">Hungarian Revolution</a>: The <a href=\"https://wikipedia.org/wiki/Governments_of_Imre_Nagy\" title=\"Governments of Imre Nagy\">government of Imre Nagy</a> recognizes newly established revolutionary workers' councils. Army officer <a href=\"https://wikipedia.org/wiki/B%C3%A9la_Kir%C3%A1ly\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a> leads anti-Soviet <a href=\"https://wikipedia.org/wiki/Militia\" title=\"Militia\">militias</a> in an attack on the headquarters of the <a href=\"https://wikipedia.org/wiki/Hungarian_Working_People%27s_Party\" title=\"Hungarian Working People's Party\">Hungarian Working People's Party</a>.", "links": [{"title": "Hungarian Revolution of 1956", "link": "https://wikipedia.org/wiki/Hungarian_Revolution_of_1956"}, {"title": "Governments of Imre Nagy", "link": "https://wikipedia.org/wiki/Governments_of_Imre_Nagy"}, {"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/B%C3%A9la_Kir%C3%A1ly"}, {"title": "Militia", "link": "https://wikipedia.org/wiki/Militia"}, {"title": "Hungarian Working People's Party", "link": "https://wikipedia.org/wiki/Hungarian_Working_People%27s_Party"}]}, {"year": "1959", "text": "Piedmont Airlines Flight 349 crashes on approach to Charlottesville-Albemarle Airport in Albemarle County, Virginia, killing 26 of the 27 on board.", "html": "1959 - <a href=\"https://wikipedia.org/wiki/Piedmont_Airlines_Flight_349\" title=\"Piedmont Airlines Flight 349\">Piedmont Airlines Flight 349</a> crashes on approach to <a href=\"https://wikipedia.org/wiki/Charlottesville%E2%80%93Albemarle_Airport\" title=\"Charlottesville-Albemarle Airport\">Charlottesville-Albemarle Airport</a> in <a href=\"https://wikipedia.org/wiki/Albemarle_County,_Virginia\" title=\"Albemarle County, Virginia\">Albemarle County, Virginia</a>, killing 26 of the 27 on board.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Piedmont_Airlines_Flight_349\" title=\"Piedmont Airlines Flight 349\">Piedmont Airlines Flight 349</a> crashes on approach to <a href=\"https://wikipedia.org/wiki/Charlottesville%E2%80%93Albemarle_Airport\" title=\"Charlottesville-Albemarle Airport\">Charlottesville-Albemarle Airport</a> in <a href=\"https://wikipedia.org/wiki/Albemarle_County,_Virginia\" title=\"Albemarle County, Virginia\">Albemarle County, Virginia</a>, killing 26 of the 27 on board.", "links": [{"title": "Piedmont Airlines Flight 349", "link": "https://wikipedia.org/wiki/Piedmont_Airlines_Flight_349"}, {"title": "Charlottesville-Albemarle Airport", "link": "https://wikipedia.org/wiki/Charlottesville%E2%80%93Albemarle_Airport"}, {"title": "Albemarle County, Virginia", "link": "https://wikipedia.org/wiki/Albemarle_County,_Virginia"}]}, {"year": "1961", "text": "The Soviet Union detonates the Tsar Bomba, the most powerful explosive device ever detonated.", "html": "1961 - The <a href=\"https://wikipedia.org/wiki/Soviet_Union\" title=\"Soviet Union\">Soviet Union</a> detonates the <a href=\"https://wikipedia.org/wiki/Tsar_Bomba\" title=\"Tsar Bomba\">Tsar Bomba</a>, the most powerful explosive device ever detonated.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Soviet_Union\" title=\"Soviet Union\">Soviet Union</a> detonates the <a href=\"https://wikipedia.org/wiki/Tsar_Bomba\" title=\"Tsar Bomba\">Tsar Bomba</a>, the most powerful explosive device ever detonated.", "links": [{"title": "Soviet Union", "link": "https://wikipedia.org/wiki/Soviet_Union"}, {"title": "Tsar Bomba", "link": "https://wikipedia.org/wiki/Tsar_<PERSON>a"}]}, {"year": "1961", "text": "Due to \"violations of <PERSON>'s precepts\", it is decreed that <PERSON>'s body be removed from its place of honour inside <PERSON>'s tomb and buried near the Kremlin Wall with a plain granite marker.", "html": "1961 - Due to \"violations of <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>'s precepts\", it is decreed that <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>'s body be removed from its place of honour inside <a href=\"https://wikipedia.org/wiki/Lenin%27s_tomb\" class=\"mw-redirect\" title=\"<PERSON>'s tomb\"><PERSON>'s tomb</a> and buried near the <a href=\"https://wikipedia.org/wiki/Kremlin_Wall\" class=\"mw-redirect\" title=\"Kremlin Wall\">Kremlin Wall</a> with a plain granite marker.", "no_year_html": "Due to \"violations of <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>'s precepts\", it is decreed that <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>'s body be removed from its place of honour inside <a href=\"https://wikipedia.org/wiki/Lenin%27s_tomb\" class=\"mw-redirect\" title=\"<PERSON>'s tomb\"><PERSON>'s tomb</a> and buried near the <a href=\"https://wikipedia.org/wiki/Kremlin_Wall\" class=\"mw-redirect\" title=\"Kremlin Wall\">Kremlin Wall</a> with a plain granite marker.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_Lenin"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}, {"title": "<PERSON>'s tomb", "link": "https://wikipedia.org/wiki/Lenin%27s_tomb"}, {"title": "Kremlin Wall", "link": "https://wikipedia.org/wiki/Kremlin_Wall"}]}, {"year": "1968", "text": "A squad of 120 North Korean Army commandos land in boats along a 25-mile long section of the eastern coast of South Korea in a failed attempt to overthrow the dictatorship of <PERSON> and bring about the reunification of Korea.", "html": "1968 - A squad of 120 <a href=\"https://wikipedia.org/wiki/Korean_People%27s_Army\" title=\"Korean People's Army\">North Korean Army</a> commandos <a href=\"https://wikipedia.org/wiki/Uljin%E2%80%93Samcheok_Landings\" title=\"Uljin-Samcheok Landings\">land in boats</a> along a 25-mile long section of the eastern coast of South Korea in a failed attempt to overthrow the dictatorship of <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> and bring about the <a href=\"https://wikipedia.org/wiki/Korean_reunification\" title=\"Korean reunification\">reunification of Korea</a>.", "no_year_html": "A squad of 120 <a href=\"https://wikipedia.org/wiki/Korean_People%27s_Army\" title=\"Korean People's Army\">North Korean Army</a> commandos <a href=\"https://wikipedia.org/wiki/Uljin%E2%80%93Samcheok_Landings\" title=\"Uljin-Samcheok Landings\">land in boats</a> along a 25-mile long section of the eastern coast of South Korea in a failed attempt to overthrow the dictatorship of <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> and bring about the <a href=\"https://wikipedia.org/wiki/Korean_reunification\" title=\"Korean reunification\">reunification of Korea</a>.", "links": [{"title": "Korean People's Army", "link": "https://wikipedia.org/wiki/Korean_People%27s_Army"}, {"title": "Uljin-Samcheok Landings", "link": "https://wikipedia.org/wiki/Uljin%E2%80%93Samcheok_Landings"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "Korean reunification", "link": "https://wikipedia.org/wiki/Korean_reunification"}]}, {"year": "1973", "text": "The Bosphorus Bridge in Turkey is completed, connecting the continents of Europe and Asia over the Bosphorus for the second time.", "html": "1973 - The <a href=\"https://wikipedia.org/wiki/Bosphorus_Bridge\" title=\"Bosphorus Bridge\">Bosphorus Bridge</a> in Turkey is completed, connecting the continents of Europe and Asia over the Bosphorus for the second time.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Bosphorus_Bridge\" title=\"Bosphorus Bridge\">Bosphorus Bridge</a> in Turkey is completed, connecting the continents of Europe and Asia over the Bosphorus for the second time.", "links": [{"title": "Bosphorus Bridge", "link": "https://wikipedia.org/wiki/Bosphorus_Bridge"}]}, {"year": "1975", "text": "Prince <PERSON> of Spain becomes acting head of state, taking over for the country's ailing dictator, <PERSON><PERSON>.", "html": "1975 - Prince <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_of_Spain\" class=\"mw-redirect\" title=\"<PERSON> of Spain\"><PERSON> of Spain</a> becomes acting head of state, taking over for the country's ailing dictator, <PERSON>. <a href=\"https://wikipedia.org/wiki/Francisco_<PERSON>\" title=\"Francisco <PERSON>\"><PERSON></a>.", "no_year_html": "Prince <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_of_Spain\" class=\"mw-redirect\" title=\"<PERSON> of Spain\"><PERSON> of Spain</a> becomes acting head of state, taking over for the country's ailing dictator, <PERSON>. <a href=\"https://wikipedia.org/wiki/Francisco_<PERSON>\" title=\"Francisco <PERSON>\"><PERSON></a>.", "links": [{"title": "<PERSON> of Spain", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_of_Spain"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Francisco_Franco"}]}, {"year": "1975", "text": "Forty-five people are killed when Inex-Adria Aviopromet Flight 450 crashes into Suchdol, Prague, while on approach to Prague Ruzyně Airport (now Václav Havel Airport Prague) in Czechoslovakia (present-day Czech Republic).", "html": "1975 - Forty-five people are killed when <a href=\"https://wikipedia.org/wiki/Inex-Adria_Aviopromet_Flight_450\" title=\"Inex-Adria Aviopromet Flight 450\">Inex-Adria Aviopromet Flight 450</a> crashes into <a href=\"https://wikipedia.org/wiki/Suchdol_(Prague)\" title=\"Suchdol (Prague)\">Suchdol</a>, <a href=\"https://wikipedia.org/wiki/Prague\" title=\"Prague\">Prague</a>, while on approach to Prague Ruzyně Airport (now <a href=\"https://wikipedia.org/wiki/V%C3%A1clav_Havel_Airport_Prague\" title=\"Václav Havel Airport Prague\">Václav <PERSON>l Airport Prague</a>) in <a href=\"https://wikipedia.org/wiki/Czechoslovakia\" title=\"Czechoslovakia\">Czechoslovakia</a> (present-day <a href=\"https://wikipedia.org/wiki/Czech_Republic\" title=\"Czech Republic\">Czech Republic</a>).", "no_year_html": "Forty-five people are killed when <a href=\"https://wikipedia.org/wiki/Inex-Adria_Aviopromet_Flight_450\" title=\"Inex-Adria Aviopromet Flight 450\">Inex-Adria Aviopromet Flight 450</a> crashes into <a href=\"https://wikipedia.org/wiki/Suchdol_(Prague)\" title=\"Suchdol (Prague)\">Suchdol</a>, <a href=\"https://wikipedia.org/wiki/Prague\" title=\"Prague\">Prague</a>, while on approach to Prague Ruzyně Airport (now <a href=\"https://wikipedia.org/wiki/V%C3%A1clav_Havel_Airport_Prague\" title=\"Václav Havel Airport Prague\">Václav Havel Airport Prague</a>) in <a href=\"https://wikipedia.org/wiki/Czechoslovakia\" title=\"Czechoslovakia\">Czechoslovakia</a> (present-day <a href=\"https://wikipedia.org/wiki/Czech_Republic\" title=\"Czech Republic\">Czech Republic</a>).", "links": [{"title": "Inex-Adria Aviopromet Flight 450", "link": "https://wikipedia.org/wiki/Inex-Adria_Aviopromet_Flight_450"}, {"title": "<PERSON><PERSON><PERSON> (Prague)", "link": "https://wikipedia.org/wiki/Suchdol_(Prague)"}, {"title": "Prague", "link": "https://wikipedia.org/wiki/Prague"}, {"title": "Václav <PERSON> Airport Prague", "link": "https://wikipedia.org/wiki/V%C3%A1<PERSON>lav_Havel_Airport_Prague"}, {"title": "Czechoslovakia", "link": "https://wikipedia.org/wiki/Czechoslovakia"}, {"title": "Czech Republic", "link": "https://wikipedia.org/wiki/Czech_Republic"}]}, {"year": "1980", "text": "El Salvador and Honduras agree to put the border dispute fought over in 1969's Football War before the International Court of Justice.", "html": "1980 - <a href=\"https://wikipedia.org/wiki/El_Salvador\" title=\"El Salvador\">El Salvador</a> and <a href=\"https://wikipedia.org/wiki/Honduras\" title=\"Honduras\">Honduras</a> agree to put the border dispute fought over in 1969's <a href=\"https://wikipedia.org/wiki/Football_War\" title=\"Football War\">Football War</a> before the <a href=\"https://wikipedia.org/wiki/International_Court_of_Justice\" title=\"International Court of Justice\">International Court of Justice</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/El_Salvador\" title=\"El Salvador\">El Salvador</a> and <a href=\"https://wikipedia.org/wiki/Honduras\" title=\"Honduras\">Honduras</a> agree to put the border dispute fought over in 1969's <a href=\"https://wikipedia.org/wiki/Football_War\" title=\"Football War\">Football War</a> before the <a href=\"https://wikipedia.org/wiki/International_Court_of_Justice\" title=\"International Court of Justice\">International Court of Justice</a>.", "links": [{"title": "El Salvador", "link": "https://wikipedia.org/wiki/El_Salvador"}, {"title": "Honduras", "link": "https://wikipedia.org/wiki/Honduras"}, {"title": "Football War", "link": "https://wikipedia.org/wiki/Football_War"}, {"title": "International Court of Justice", "link": "https://wikipedia.org/wiki/International_Court_of_Justice"}]}, {"year": "1983", "text": "The first democratic elections in Argentina, after seven years of military rule, are held.", "html": "1983 - The first <a href=\"https://wikipedia.org/wiki/1983_Argentine_general_election\" title=\"1983 Argentine general election\">democratic elections in Argentina</a>, after seven years of military rule, are held.", "no_year_html": "The first <a href=\"https://wikipedia.org/wiki/1983_Argentine_general_election\" title=\"1983 Argentine general election\">democratic elections in Argentina</a>, after seven years of military rule, are held.", "links": [{"title": "1983 Argentine general election", "link": "https://wikipedia.org/wiki/1983_Argentine_general_election"}]}, {"year": "1983", "text": "A magnitude 6.6 earthquake in the Turkish provinces of Erzurum and Kars leaves approximately 1,340 people dead.", "html": "1983 - A <a href=\"https://wikipedia.org/wiki/1983_Erzurum_earthquake\" title=\"1983 Erzurum earthquake\">magnitude 6.6 earthquake</a> in the Turkish provinces of <a href=\"https://wikipedia.org/wiki/Erzurum_Province\" title=\"Erzurum Province\">Erzurum</a> and <a href=\"https://wikipedia.org/wiki/Kars_Province\" title=\"Kars Province\">Kars</a> leaves approximately 1,340 people dead.", "no_year_html": "A <a href=\"https://wikipedia.org/wiki/1983_Erzurum_earthquake\" title=\"1983 Erzurum earthquake\">magnitude 6.6 earthquake</a> in the Turkish provinces of <a href=\"https://wikipedia.org/wiki/Erzurum_Province\" title=\"Erzurum Province\">Erzurum</a> and <a href=\"https://wikipedia.org/wiki/Kars_Province\" title=\"Kars Province\">Kars</a> leaves approximately 1,340 people dead.", "links": [{"title": "1983 Erzurum earthquake", "link": "https://wikipedia.org/wiki/1983_Erzurum_earthquake"}, {"title": "Erzurum Province", "link": "https://wikipedia.org/wiki/Erzurum_Province"}, {"title": "Kars Province", "link": "https://wikipedia.org/wiki/Kars_Province"}]}, {"year": "1985", "text": "Space Shuttle Challenger lifts off for mission STS-61-A, its final successful mission.", "html": "1985 - <a href=\"https://wikipedia.org/wiki/Space_Shuttle_Challenger\" title=\"Space Shuttle Challenger\">Space Shuttle <i>Challenger</i></a> lifts off for mission <a href=\"https://wikipedia.org/wiki/STS-61-A\" title=\"STS-61-A\">STS-61-A</a>, its final successful mission.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Space_Shuttle_Challenger\" title=\"Space Shuttle Challenger\">Space Shuttle <i>Challenger</i></a> lifts off for mission <a href=\"https://wikipedia.org/wiki/STS-61-A\" title=\"STS-61-A\">STS-61-A</a>, its final successful mission.", "links": [{"title": "Space Shuttle Challenger", "link": "https://wikipedia.org/wiki/Space_Shuttle_Challenger"}, {"title": "STS-61-A", "link": "https://wikipedia.org/wiki/STS-61-A"}]}, {"year": "1991", "text": "The Israeli-Palestinian conflict: The Madrid Conference commences in an effort to revive peace negotiations between Israel and Palestine.", "html": "1991 - <a href=\"https://wikipedia.org/wiki/Israeli%E2%80%93Palestinian_conflict\" title=\"Israeli-Palestinian conflict\">The Israeli-Palestinian conflict</a>: The <a href=\"https://wikipedia.org/wiki/Madrid_Conference_of_1991\" title=\"Madrid Conference of 1991\">Madrid Conference</a> commences in an effort to revive <a href=\"https://wikipedia.org/wiki/Peace_negotiations\" class=\"mw-redirect\" title=\"Peace negotiations\">peace negotiations</a> between <a href=\"https://wikipedia.org/wiki/Israel\" title=\"Israel\">Israel</a> and <a href=\"https://wikipedia.org/wiki/Palestinian_territories\" class=\"mw-redirect\" title=\"Palestinian territories\">Palestine</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Israeli%E2%80%93Palestinian_conflict\" title=\"Israeli-Palestinian conflict\">The Israeli-Palestinian conflict</a>: The <a href=\"https://wikipedia.org/wiki/Madrid_Conference_of_1991\" title=\"Madrid Conference of 1991\">Madrid Conference</a> commences in an effort to revive <a href=\"https://wikipedia.org/wiki/Peace_negotiations\" class=\"mw-redirect\" title=\"Peace negotiations\">peace negotiations</a> between <a href=\"https://wikipedia.org/wiki/Israel\" title=\"Israel\">Israel</a> and <a href=\"https://wikipedia.org/wiki/Palestinian_territories\" class=\"mw-redirect\" title=\"Palestinian territories\">Palestine</a>.", "links": [{"title": "Israeli-Palestinian conflict", "link": "https://wikipedia.org/wiki/Israeli%E2%80%93Palestinian_conflict"}, {"title": "Madrid Conference of 1991", "link": "https://wikipedia.org/wiki/Madrid_Conference_of_1991"}, {"title": "Peace negotiations", "link": "https://wikipedia.org/wiki/Peace_negotiations"}, {"title": "Israel", "link": "https://wikipedia.org/wiki/Israel"}, {"title": "Palestinian territories", "link": "https://wikipedia.org/wiki/Palestinian_territories"}]}, {"year": "1995", "text": "Quebec citizens narrowly vote (50.58% to 49.42%) in favour of remaining a province of Canada in their second referendum on national sovereignty.", "html": "1995 - <a href=\"https://wikipedia.org/wiki/Quebec\" title=\"Quebec\">Quebec</a> citizens narrowly vote (50.58% to 49.42%) in favour of remaining a <a href=\"https://wikipedia.org/wiki/Provinces_and_territories_of_Canada\" title=\"Provinces and territories of Canada\">province of Canada</a> in their <a href=\"https://wikipedia.org/wiki/1995_Quebec_referendum\" title=\"1995 Quebec referendum\">second referendum</a> on national sovereignty.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Quebec\" title=\"Quebec\">Quebec</a> citizens narrowly vote (50.58% to 49.42%) in favour of remaining a <a href=\"https://wikipedia.org/wiki/Provinces_and_territories_of_Canada\" title=\"Provinces and territories of Canada\">province of Canada</a> in their <a href=\"https://wikipedia.org/wiki/1995_Quebec_referendum\" title=\"1995 Quebec referendum\">second referendum</a> on national sovereignty.", "links": [{"title": "Quebec", "link": "https://wikipedia.org/wiki/Quebec"}, {"title": "Provinces and territories of Canada", "link": "https://wikipedia.org/wiki/Provinces_and_territories_of_Canada"}, {"title": "1995 Quebec referendum", "link": "https://wikipedia.org/wiki/1995_Quebec_referendum"}]}, {"year": "2005", "text": "The rebuilt Dresden Frauenkirche (destroyed in the firebombing of Dresden during World War II) is reconsecrated after a thirteen-year rebuilding project.", "html": "2005 - The rebuilt <a href=\"https://wikipedia.org/wiki/Dresden_Frauenkirche\" class=\"mw-redirect\" title=\"Dresden Frauenkirche\">Dresden Frauenkirche</a> (destroyed in the <a href=\"https://wikipedia.org/wiki/Firebombing_of_Dresden\" class=\"mw-redirect\" title=\"Firebombing of Dresden\">firebombing of Dresden</a> during World War II) is reconsecrated after a thirteen-year rebuilding project.", "no_year_html": "The rebuilt <a href=\"https://wikipedia.org/wiki/Dresden_Frauenkirche\" class=\"mw-redirect\" title=\"Dresden Frauenkirche\">Dresden Frauenkirche</a> (destroyed in the <a href=\"https://wikipedia.org/wiki/Firebombing_of_Dresden\" class=\"mw-redirect\" title=\"Firebombing of Dresden\">firebombing of Dresden</a> during World War II) is reconsecrated after a thirteen-year rebuilding project.", "links": [{"title": "Dresden Frauenkirche", "link": "https://wikipedia.org/wiki/Dresden_Frauenkirche"}, {"title": "Firebombing of Dresden", "link": "https://wikipedia.org/wiki/Firebombing_of_Dresden"}]}, {"year": "2013", "text": "Forty-five people are killed and seven injured after a bus catches fire in Mahabubnagar district, Andhra Pradesh (present-day Telangana), India.", "html": "2013 - Forty-five people are killed and seven injured after a <a href=\"https://wikipedia.org/wiki/2013_Mahabubnagar_bus_accident\" title=\"2013 Mahabubnagar bus accident\">bus catches fire</a> in <a href=\"https://wikipedia.org/wiki/Mahabubnagar_district\" title=\"Mahabubnagar district\">Mahabubnagar district</a>, <a href=\"https://wikipedia.org/wiki/Andhra_Pradesh\" title=\"Andhra Pradesh\">Andhra Pradesh</a> (present-day <a href=\"https://wikipedia.org/wiki/Telangana\" title=\"Telangana\">Telangana</a>), India.", "no_year_html": "Forty-five people are killed and seven injured after a <a href=\"https://wikipedia.org/wiki/2013_Mahabubnagar_bus_accident\" title=\"2013 Mahabubnagar bus accident\">bus catches fire</a> in <a href=\"https://wikipedia.org/wiki/Mahabubnagar_district\" title=\"Mahabubnagar district\">Mahabubnagar district</a>, <a href=\"https://wikipedia.org/wiki/Andhra_Pradesh\" title=\"Andhra Pradesh\">Andhra Pradesh</a> (present-day <a href=\"https://wikipedia.org/wiki/Telangana\" title=\"Telangana\">Telangana</a>), India.", "links": [{"title": "2013 Mahabubnagar bus accident", "link": "https://wikipedia.org/wiki/2013_Mahabubnagar_bus_accident"}, {"title": "Mahabubnagar district", "link": "https://wikipedia.org/wiki/Mahabubnagar_district"}, {"title": "Andhra Pradesh", "link": "https://wikipedia.org/wiki/Andhra_Pradesh"}, {"title": "Telangana", "link": "https://wikipedia.org/wiki/Telangana"}]}, {"year": "2014", "text": "Sweden becomes the first European Union member state to officially recognize the State of Palestine.", "html": "2014 - Sweden becomes the first <a href=\"https://wikipedia.org/wiki/European_Union\" title=\"European Union\">European Union</a> member state to officially <a href=\"https://wikipedia.org/wiki/International_recognition_of_the_State_of_Palestine\" class=\"mw-redirect\" title=\"International recognition of the State of Palestine\">recognize</a> the <a href=\"https://wikipedia.org/wiki/State_of_Palestine\" class=\"mw-redirect\" title=\"State of Palestine\">State of Palestine</a>.", "no_year_html": "Sweden becomes the first <a href=\"https://wikipedia.org/wiki/European_Union\" title=\"European Union\">European Union</a> member state to officially <a href=\"https://wikipedia.org/wiki/International_recognition_of_the_State_of_Palestine\" class=\"mw-redirect\" title=\"International recognition of the State of Palestine\">recognize</a> the <a href=\"https://wikipedia.org/wiki/State_of_Palestine\" class=\"mw-redirect\" title=\"State of Palestine\">State of Palestine</a>.", "links": [{"title": "European Union", "link": "https://wikipedia.org/wiki/European_Union"}, {"title": "International recognition of the State of Palestine", "link": "https://wikipedia.org/wiki/International_recognition_of_the_State_of_Palestine"}, {"title": "State of Palestine", "link": "https://wikipedia.org/wiki/State_of_Palestine"}]}, {"year": "2014", "text": "Four people are killed when a Beechcraft Super King Air crashes at Wichita Dwight D. Eisenhower National Airport in Wichita, Kansas.", "html": "2014 - Four people are killed when a <a href=\"https://wikipedia.org/wiki/Beechcraft_Super_King_Air\" title=\"Beechcraft Super King Air\">Beechcraft Super King Air</a> <a href=\"https://wikipedia.org/wiki/2014_Wichita_King_Air_crash\" title=\"2014 Wichita King Air crash\">crashes</a> at <a href=\"https://wikipedia.org/wiki/Wichita_Dwight_D._Eisenhower_National_Airport\" title=\"Wichita Dwight D. Eisenhower National Airport\">Wichita Dwight D. Eisenhower National Airport</a> in <a href=\"https://wikipedia.org/wiki/Wichita,_Kansas\" title=\"Wichita, Kansas\">Wichita, Kansas</a>.", "no_year_html": "Four people are killed when a <a href=\"https://wikipedia.org/wiki/Beechcraft_Super_King_Air\" title=\"Beechcraft Super King Air\">Beechcraft Super King Air</a> <a href=\"https://wikipedia.org/wiki/2014_Wichita_King_Air_crash\" title=\"2014 Wichita King Air crash\">crashes</a> at <a href=\"https://wikipedia.org/wiki/Wichita_Dwight_D._Eisenhower_National_Airport\" title=\"Wichita Dwight D. Eisenhower National Airport\">Wichita Dwight D. Eisenhower National Airport</a> in <a href=\"https://wikipedia.org/wiki/Wichita,_Kansas\" title=\"Wichita, Kansas\">Wichita, Kansas</a>.", "links": [{"title": "Beechcraft Super King Air", "link": "https://wikipedia.org/wiki/Beechcraft_Super_King_Air"}, {"title": "2014 Wichita King Air crash", "link": "https://wikipedia.org/wiki/2014_Wichita_King_Air_crash"}, {"title": "Wichita Dwight D. Eisenhower National Airport", "link": "https://wikipedia.org/wiki/Wichita_<PERSON>_<PERSON>_<PERSON>_National_Airport"}, {"title": "Wichita, Kansas", "link": "https://wikipedia.org/wiki/Wichita,_Kansas"}]}, {"year": "2015", "text": "A fire in a nightclub in the Romanian capital of Bucharest kills sixty-four people and leaves more than 147 injured.", "html": "2015 - A <a href=\"https://wikipedia.org/wiki/Colectiv_nightclub_fire\" title=\"Colectiv nightclub fire\">fire in a nightclub</a> in the Romanian capital of <a href=\"https://wikipedia.org/wiki/Bucharest\" title=\"Bucharest\">Bucharest</a> kills sixty-four people and leaves more than 147 injured.", "no_year_html": "A <a href=\"https://wikipedia.org/wiki/Colectiv_nightclub_fire\" title=\"Colectiv nightclub fire\">fire in a nightclub</a> in the Romanian capital of <a href=\"https://wikipedia.org/wiki/Bucharest\" title=\"Bucharest\">Bucharest</a> kills sixty-four people and leaves more than 147 injured.", "links": [{"title": "Colectiv nightclub fire", "link": "https://wikipedia.org/wiki/Colectiv_nightclub_fire"}, {"title": "Bucharest", "link": "https://wikipedia.org/wiki/Bucharest"}]}, {"year": "2020", "text": "A magnitude 7.0 earthquake strikes the Aegean Sea between Greece and Turkey, triggering a tsunami. At least 119 people die mainly due to collapsed buildings.", "html": "2020 - A <a href=\"https://wikipedia.org/wiki/2020_Aegean_Sea_earthquake\" title=\"2020 Aegean Sea earthquake\">magnitude 7.0 earthquake strikes the Aegean Sea</a> between Greece and Turkey, triggering a tsunami. At least 119 people die mainly due to collapsed buildings.", "no_year_html": "A <a href=\"https://wikipedia.org/wiki/2020_Aegean_Sea_earthquake\" title=\"2020 Aegean Sea earthquake\">magnitude 7.0 earthquake strikes the Aegean Sea</a> between Greece and Turkey, triggering a tsunami. At least 119 people die mainly due to collapsed buildings.", "links": [{"title": "2020 Aegean Sea earthquake", "link": "https://wikipedia.org/wiki/2020_Aegean_Sea_earthquake"}]}, {"year": "2022", "text": "A pedestrian suspension bridge collapses in the city of Morbi, Gujarat, leading to the deaths of at least 135 people.", "html": "2022 - A pedestrian suspension bridge <a href=\"https://wikipedia.org/wiki/2022_Morbi_bridge_collapse\" title=\"2022 Morbi bridge collapse\">collapses</a> in the city of <a href=\"https://wikipedia.org/wiki/Morbi\" title=\"Morb<PERSON>\"><PERSON><PERSON><PERSON></a>, <a href=\"https://wikipedia.org/wiki/Gujarat\" title=\"Gujarat\">Gujarat</a>, leading to the deaths of at least 135 people.", "no_year_html": "A pedestrian suspension bridge <a href=\"https://wikipedia.org/wiki/2022_Morbi_bridge_collapse\" title=\"2022 Morbi bridge collapse\">collapses</a> in the city of <a href=\"https://wikipedia.org/wiki/Morbi\" title=\"Morb<PERSON>\"><PERSON><PERSON><PERSON></a>, <a href=\"https://wikipedia.org/wiki/Gujarat\" title=\"Gujarat\">Gujarat</a>, leading to the deaths of at least 135 people.", "links": [{"title": "2022 Morbi bridge collapse", "link": "https://wikipedia.org/wiki/2022_Morbi_bridge_collapse"}, {"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Morbi"}, {"title": "Gujarat", "link": "https://wikipedia.org/wiki/Gujarat"}]}], "Births": [{"year": "39 BC", "text": "<PERSON> the Elder, Roman daughter of <PERSON> (d. 14)", "html": "39 BC - 39 BC - <a href=\"https://wikipedia.org/wiki/<PERSON>_the_Elder\" title=\"<PERSON> the Elder\"><PERSON> the Elder</a>, Roman daughter of <a href=\"https://wikipedia.org/wiki/Augustus\" title=\"Augustus\">Augustus</a> (d. 14)", "no_year_html": "39 BC - <a href=\"https://wikipedia.org/wiki/<PERSON>_the_Elder\" title=\"<PERSON> the Elder\"><PERSON> the Elder</a>, Roman daughter of <a href=\"https://wikipedia.org/wiki/Augustus\" title=\"<PERSON>\">Augustus</a> (d. 14)", "links": [{"title": "<PERSON> the Elder", "link": "https://wikipedia.org/wiki/<PERSON>_the_<PERSON>"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Augustus"}]}, {"year": "1218", "text": "Emperor <PERSON><PERSON><PERSON><PERSON> of Japan (d. 1234)", "html": "1218 - <a href=\"https://wikipedia.org/wiki/Emperor_Ch%C5%ABky%C5%8D\" title=\"Emperor Chūkyō\">Emperor <PERSON><PERSON>ky<PERSON></a> of Japan (d. 1234)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Emperor_Ch%C5%ABky%C5%8D\" title=\"Emperor Chūky<PERSON>\">Emperor <PERSON><PERSON><PERSON><PERSON></a> of Japan (d. 1234)", "links": [{"title": "Emperor <PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Emperor_Ch%C5%ABky%C5%8D"}]}, {"year": "1327", "text": "<PERSON>, Duke of Calabria (d. 1345)", "html": "1327 - <a href=\"https://wikipedia.org/wiki/<PERSON>,_Duke_of_Calabria\" title=\"<PERSON>, Duke of Calabria\"><PERSON>, Duke of Calabria</a> (d. 1345)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>,_Duke_of_Calabria\" title=\"<PERSON>, Duke of Calabria\"><PERSON>, Duke of Calabria</a> (d. 1345)", "links": [{"title": "<PERSON>, Duke of Calabria", "link": "https://wikipedia.org/wiki/<PERSON>,_Duke_of_Calabria"}]}, {"year": "1447", "text": "<PERSON>, Prince-Bishop of Warmia (d. 1512)", "html": "1447 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Prince-Bishop of Warmia (d. 1512)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Prince-Bishop of Warmia (d. 1512)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>e"}]}, {"year": "1492", "text": "<PERSON>, French noblewoman (d. 1562)", "html": "1492 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%27Alen%C3%A7on\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, French noblewoman (d. 1562)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%27Alen%C3%A7on\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, French noblewoman (d. 1562)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>%27Alen%C3%A7on"}]}, {"year": "1513", "text": "<PERSON>, French bishop and translator (d. 1593)", "html": "1513 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French bishop and translator (d. 1593)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French bishop and translator (d. 1593)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1558", "text": "<PERSON><PERSON><PERSON><PERSON><PERSON>, du<PERSON> <PERSON>, Marshal of France (d. 1652)", "html": "1558 - <a href=\"https://wikipedia.org/wiki/<PERSON>-<PERSON>_<PERSON>_<PERSON>,_duc_<PERSON>_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON><PERSON><PERSON>, duc de <PERSON>\"><PERSON><PERSON><PERSON><PERSON><PERSON>, duc de <PERSON></a>, Marshal of France (d. 1652)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>,_duc_<PERSON>_La_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON><PERSON>, duc de La Force\"><PERSON><PERSON><PERSON><PERSON><PERSON>, duc de <PERSON></a>, Marshal of France (d. 1652)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON><PERSON>, duc de La Force", "link": "https://wikipedia.org/wiki/<PERSON>-<PERSON>_<PERSON>_<PERSON>,_duc_<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1624", "text": "<PERSON>, French historian and author (d. 1693)", "html": "1624 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French historian and author (d. 1693)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French historian and author (d. 1693)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1632", "text": "<PERSON>, English physicist, mathematician, and architect, designed St Paul's Cathedral (d. 1723)", "html": "1632 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English physicist, mathematician, and architect, designed <a href=\"https://wikipedia.org/wiki/St_Paul%27s_Cathedral\" title=\"St Paul's Cathedral\">St Paul's Cathedral</a> (d. 1723)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English physicist, mathematician, and architect, designed <a href=\"https://wikipedia.org/wiki/St_Paul%27s_Cathedral\" title=\"St Paul's Cathedral\">St Paul's Cathedral</a> (d. 1723)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "St Paul's Cathedral", "link": "https://wikipedia.org/wiki/St_Paul%27s_Cathedral"}]}, {"year": "1660", "text": "<PERSON>, Duke of Schleswig-Holstein-Sonderburg-Augustenburg (d. 1731)", "html": "1660 - <a href=\"https://wikipedia.org/wiki/<PERSON>_August,_Duke_of_Schleswig-Holstein-Sonderburg-Augustenburg\" title=\"<PERSON> August, Duke of Schleswig-Holstein-Sonderburg-Augustenburg\"><PERSON>, Duke of Schleswig-Holstein-Sonderburg-Augustenburg</a> (d. 1731)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_August,_Duke_of_Schleswig-Holstein-Sonderburg-Augustenburg\" title=\"<PERSON> August, Duke of Schleswig-Holstein-Sonderburg-Augustenburg\"><PERSON>, Duke of Schleswig-Holstein-Sonderburg-Augustenburg</a> (d. 1731)", "links": [{"title": "<PERSON>, Duke of Schleswig-Holstein-Sonderburg-Augustenburg", "link": "https://wikipedia.org/wiki/<PERSON>_August,_Duke_of_Schleswig-Holstein-Sonderburg-Augustenburg"}]}, {"year": "1668", "text": "<PERSON> of Hanover (d. 1705)", "html": "1668 - <a href=\"https://wikipedia.org/wiki/<PERSON>_Charlotte_of_Hanover\" title=\"<PERSON> of Hanover\"><PERSON> of Hanover</a> (d. 1705)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Hanover\" title=\"<PERSON> of Hanover\"><PERSON> of Hanover</a> (d. 1705)", "links": [{"title": "<PERSON> Hanover", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_Hanover"}]}, {"year": "1712", "text": "<PERSON>, Maltese linguist, historian and cleric (d. 1770)", "html": "1712 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>_<PERSON>_<PERSON>dan<PERSON>\" title=\"<PERSON> Soldanis\"><PERSON></a>, Maltese linguist, historian and cleric (d. 1770)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>_<PERSON>_<PERSON>dan<PERSON>\" title=\"<PERSON>danis\"><PERSON></a>, Maltese linguist, historian and cleric (d. 1770)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1728", "text": "<PERSON>, English businesswoman (d. 1808)", "html": "1728 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English businesswoman (d. 1808)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English businesswoman (d. 1808)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1735", "text": "<PERSON>, American lawyer and politician, 2nd President of the United States (d. 1826)", "html": "1735 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and politician, 2nd <a href=\"https://wikipedia.org/wiki/President_of_the_United_States\" title=\"President of the United States\">President of the United States</a> (d. 1826)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and politician, 2nd <a href=\"https://wikipedia.org/wiki/President_of_the_United_States\" title=\"President of the United States\">President of the United States</a> (d. 1826)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "President of the United States", "link": "https://wikipedia.org/wiki/President_of_the_United_States"}]}, {"year": "1741", "text": "<PERSON>, painter (d. 1807)", "html": "1741 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, painter (d. 1807)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, painter (d. 1807)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1751", "text": "<PERSON>, Irish-English poet, playwright, and politician, Treasurer of the Navy (d. 1816)", "html": "1751 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Irish-English poet, playwright, and politician, <a href=\"https://wikipedia.org/wiki/Treasurer_of_the_Navy\" title=\"Treasurer of the Navy\">Treasurer of the Navy</a> (d. 1816)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Irish-English poet, playwright, and politician, <a href=\"https://wikipedia.org/wiki/Treasurer_of_the_Navy\" title=\"Treasurer of the Navy\">Treasurer of the Navy</a> (d. 1816)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "Treasurer of the Navy", "link": "https://wikipedia.org/wiki/Treasurer_of_the_Navy"}]}, {"year": "1762", "text": "<PERSON>, Turkish-French poet and playwright (d. 1794)", "html": "1762 - <a href=\"https://wikipedia.org/wiki/Andr%C3%A9_Ch%C3%A9nier\" title=\"<PERSON>\"><PERSON></a>, Turkish-French poet and playwright (d. 1794)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Andr%C3%A9_Ch%C3%A9nier\" title=\"<PERSON>\"><PERSON></a>, Turkish-French poet and playwright (d. 1794)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Andr%C3%A9_Ch%C3%A9nier"}]}, {"year": "1786", "text": "<PERSON><PERSON><PERSON>, Canadian captain and author (d. 1871)", "html": "1786 - <a href=\"https://wikipedia.org/wiki/<PERSON>-<PERSON>_<PERSON>_<PERSON>_<PERSON>%C3%A9\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Canadian captain and author (d. 1871)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>_<PERSON>%C3%A9\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Canadian captain and author (d. 1871)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>-<PERSON>_<PERSON>_<PERSON>_<PERSON>%C3%A9"}]}, {"year": "1799", "text": "<PERSON><PERSON><PERSON>, Canadian bishop (d. 1885)", "html": "1799 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Canadian bishop (d. 1885)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Canadian bishop (d. 1885)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/I<PERSON><PERSON>_<PERSON>"}]}, {"year": "1839", "text": "<PERSON>, French-English painter (d. 1899)", "html": "1839 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French-English painter (d. 1899)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French-English painter (d. 1899)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1857", "text": "<PERSON>, French-Swiss physician and neurologist (d. 1904)", "html": "1857 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French-Swiss physician and neurologist (d. 1904)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French-Swiss physician and neurologist (d. 1904)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1861", "text": "<PERSON>, French sculptor and painter (d. 1929)", "html": "1861 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French sculptor and painter (d. 1929)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French sculptor and painter (d. 1929)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1868", "text": "<PERSON><PERSON><PERSON><PERSON>, Portuguese polygraph (d. 1953)", "html": "1868 - <a href=\"https://wikipedia.org/wiki/Ant%C3%B3nio_Cabreira\" title=\"<PERSON><PERSON><PERSON><PERSON> Cabreira\"><PERSON><PERSON><PERSON><PERSON></a>, Portuguese polygraph (d. 1953)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Ant%C3%B3nio_Cabreira\" title=\"<PERSON><PERSON><PERSON><PERSON> Cabreira\"><PERSON><PERSON><PERSON><PERSON></a>, Portuguese polygraph (d. 1953)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Ant%C3%B3nio_Cabreira"}]}, {"year": "1871", "text": "<PERSON>, American baseball player (d. 1949)", "html": "1871 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player (d. 1949)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player (d. 1949)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1871", "text": "<PERSON>, French poet and philosopher (d. 1945)", "html": "1871 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A9ry\" title=\"<PERSON>\"><PERSON></a>, French poet and philosopher (d. 1945)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A9ry\" title=\"<PERSON>\"><PERSON></a>, French poet and philosopher (d. 1945)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Paul_Val%C3%A9ry"}]}, {"year": "1873", "text": "<PERSON>, Mexican businessman and politician, 33rd President of Mexico (d. 1913)", "html": "1873 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"Francisco <PERSON>\"><PERSON></a>, Mexican businessman and politician, 33rd <a href=\"https://wikipedia.org/wiki/President_of_Mexico\" title=\"President of Mexico\">President of Mexico</a> (d. 1913)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Francisco_<PERSON>_<PERSON>\" title=\"Francisco <PERSON>\"><PERSON></a>, Mexican businessman and politician, 33rd <a href=\"https://wikipedia.org/wiki/President_of_Mexico\" title=\"President of Mexico\">President of Mexico</a> (d. 1913)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>"}, {"title": "President of Mexico", "link": "https://wikipedia.org/wiki/President_of_Mexico"}]}, {"year": "1877", "text": "<PERSON>, Latvian politician, Prime Minister of Latvia (d. 1941)", "html": "1877 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C5%86%C5%A1\" title=\"<PERSON>\"><PERSON></a>, Latvian politician, <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Latvia\" title=\"Prime Minister of Latvia\">Prime Minister of Latvia</a> (d. 1941)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C5%86%C5%A1\" title=\"<PERSON>\"><PERSON></a>, Latvian politician, <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Latvia\" title=\"Prime Minister of Latvia\">Prime Minister of Latvia</a> (d. 1941)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>i%C5%86%C5%A1"}, {"title": "Prime Minister of Latvia", "link": "https://wikipedia.org/wiki/Prime_Minister_of_Latvia"}]}, {"year": "1878", "text": "<PERSON>, German electrical engineer, invented the Enigma machine (d. 1929)", "html": "1878 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German electrical engineer, invented the <a href=\"https://wikipedia.org/wiki/Enigma_machine\" title=\"Enigma machine\">Enigma machine</a> (d. 1929)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German electrical engineer, invented the <a href=\"https://wikipedia.org/wiki/Enigma_machine\" title=\"Enigma machine\">Enigma machine</a> (d. 1929)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Enigma machine", "link": "https://wikipedia.org/wiki/Enigma_machine"}]}, {"year": "1881", "text": "<PERSON>, American poet and author (d. 1941)", "html": "1881 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American poet and author (d. 1941)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American poet and author (d. 1941)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1882", "text": "<PERSON><PERSON><PERSON>, Czech chess player and composer (d. 1957)", "html": "1882 - <a href=\"https://wikipedia.org/wiki/Old%C5%99ich_Du<PERSON>\" title=\"<PERSON><PERSON><PERSON> Du<PERSON>\"><PERSON><PERSON><PERSON></a>, Czech chess player and composer (d. 1957)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Old%C5%99ich_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Czech chess player and composer (d. 1957)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Old%C5%99ich_Duras"}]}, {"year": "1882", "text": "<PERSON>, American admiral (d. 1959)", "html": "1882 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>.\" title=\"<PERSON>\"><PERSON>.</a>, American admiral (d. 1959)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>.\" title=\"<PERSON>\"><PERSON>.</a>, American admiral (d. 1959)", "links": [{"title": "<PERSON>.", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>."}]}, {"year": "1882", "text": "<PERSON><PERSON><PERSON><PERSON>, Polish-German field marshal (d. 1944)", "html": "1882 - <a href=\"https://wikipedia.org/wiki/G%C3%<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON> <PERSON>\"><PERSON><PERSON><PERSON><PERSON> <PERSON></a>, Polish-German field marshal (d. 1944)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/G%C3%<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON> <PERSON>\"><PERSON><PERSON><PERSON><PERSON> <PERSON></a>, Polish-German field marshal (d. 1944)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/G%C3%<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1885", "text": "<PERSON>, American poet and critic (d. 1972)", "html": "1885 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Ezra Pound\"><PERSON></a>, American poet and critic (d. 1972)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_Pound\" title=\"Ezra Pound\"><PERSON></a>, American poet and critic (d. 1972)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1886", "text": "<PERSON><PERSON><PERSON>, American author, poet, and playwright (d. 1958)", "html": "1886 - <a href=\"https://wikipedia.org/wiki/Zo%C3%AB_Akins\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American author, poet, and playwright (d. 1958)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Zo%C3%AB_Akins\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American author, poet, and playwright (d. 1958)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Zo%C3%AB_Akins"}]}, {"year": "1887", "text": "<PERSON><PERSON>, Indian-Bangladeshi author, poet, and playwright (d. 1923)", "html": "1887 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Indian-Bangladeshi author, poet, and playwright (d. 1923)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Indian-Bangladeshi author, poet, and playwright (d. 1923)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1888", "text": "<PERSON>, American soccer player, soldier, and politician (d. 1969)", "html": "1888 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American soccer player, soldier, and politician (d. 1969)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American soccer player, soldier, and politician (d. 1969)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1888", "text": "<PERSON><PERSON>, Greek footballer and high jumper (d. 1913)", "html": "1888 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Greek footballer and high jumper (d. 1913)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Greek footballer and high jumper (d. 1913)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Konstantin<PERSON>_<PERSON>"}]}, {"year": "1892", "text": "<PERSON>, Italian-American bodybuilder (d. 1972)", "html": "1892 - <a href=\"https://wikipedia.org/wiki/Charles_Atlas\" title=\"Charles Atlas\"><PERSON></a>, Italian-American bodybuilder (d. 1972)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Charles_Atlas\" title=\"Charles Atlas\"><PERSON></a>, Italian-American bodybuilder (d. 1972)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Charles_Atlas"}]}, {"year": "1893", "text": "<PERSON>, German soldier, lawyer, and judge (d. 1945)", "html": "1893 - <a href=\"https://wikipedia.org/wiki/1893\" title=\"1893\">1893</a> - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German soldier, lawyer, and judge (d. 1945)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/1893\" title=\"1893\">1893</a> - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German soldier, lawyer, and judge (d. 1945)", "links": [{"title": "1893", "link": "https://wikipedia.org/wiki/1893"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1894", "text": "<PERSON>, French biologist and philosopher (d. 1977)", "html": "1894 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French biologist and philosopher (d. 1977)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French biologist and philosopher (d. 1977)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1894", "text": "<PERSON>, English composer and critic (d. 1930)", "html": "1894 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English composer and critic (d. 1930)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English composer and critic (d. 1930)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1895", "text": "<PERSON>, German pathologist and bacteriologist, Nobel Prize laureate (d. 1964)", "html": "1895 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German pathologist and bacteriologist, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Physiology_or_Medicine\" title=\"Nobel Prize in Physiology or Medicine\">Nobel Prize</a> laureate (d. 1964)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German pathologist and bacteriologist, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Physiology_or_Medicine\" title=\"Nobel Prize in Physiology or Medicine\">Nobel Prize</a> laureate (d. 1964)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}, {"title": "Nobel Prize in Physiology or Medicine", "link": "https://wikipedia.org/wiki/Nobel_Prize_in_Physiology_or_Medicine"}]}, {"year": "1895", "text": "<PERSON>, American physician and physiologist, Nobel Prize laureate (d. 1973)", "html": "1895 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American physician and physiologist, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Physiology_or_Medicine\" title=\"Nobel Prize in Physiology or Medicine\">Nobel Prize</a> laureate (d. 1973)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American physician and physiologist, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Physiology_or_Medicine\" title=\"Nobel Prize in Physiology or Medicine\">Nobel Prize</a> laureate (d. 1973)", "links": [{"title": "Dickinson W<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>"}, {"title": "Nobel Prize in Physiology or Medicine", "link": "https://wikipedia.org/wiki/Nobel_Prize_in_Physiology_or_Medicine"}]}, {"year": "1896", "text": "<PERSON>, American actor (d. 1928)", "html": "1896 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor (d. 1928)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor (d. 1928)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1896", "text": "<PERSON>, American actress and screenwriter (d. 1985)", "html": "1896 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress and screenwriter (d. 1985)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress and screenwriter (d. 1985)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1896", "text": "<PERSON><PERSON><PERSON>, Greek poet and educator (d. 1928)", "html": "1896 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Greek poet and educator (d. 1928)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Greek poet and educator (d. 1928)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1896", "text": "<PERSON>, American soldier (d. 1980)", "html": "1896 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American soldier (d. 1980)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American soldier (d. 1980)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1896", "text": "<PERSON><PERSON>, Italian conductor (d. 1985)", "html": "1896 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Italian conductor (d. 1985)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Italian conductor (d. 1985)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1897", "text": "<PERSON><PERSON><PERSON><PERSON>, Mexican singer-songwriter and actor (d. 1970)", "html": "1897 - <a href=\"https://wikipedia.org/wiki/Agust%C3%ADn_Lara\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Mexican singer-songwriter and actor (d. 1970)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Agust%C3%ADn_Lara\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Mexican singer-songwriter and actor (d. 1970)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Agust%C3%ADn_Lara"}]}, {"year": "1898", "text": "<PERSON>, American baseball player and manager (d. 1989)", "html": "1898 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player and manager (d. 1989)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player and manager (d. 1989)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1900", "text": "<PERSON><PERSON><PERSON>, Finnish-Swedish physiologist and academic, Nobel Prize laureate (d. 1991)", "html": "1900 - <a href=\"https://wikipedia.org/wiki/<PERSON>gna<PERSON>_<PERSON>\" title=\"Ragnar Gran<PERSON>\"><PERSON><PERSON><PERSON></a>, Finnish-Swedish physiologist and academic, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Physiology_or_Medicine\" title=\"Nobel Prize in Physiology or Medicine\">Nobel Prize</a> laureate (d. 1991)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"Ragnar Gran<PERSON>\"><PERSON><PERSON><PERSON></a>, Finnish-Swedish physiologist and academic, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Physiology_or_Medicine\" title=\"Nobel Prize in Physiology or Medicine\">Nobel Prize</a> laureate (d. 1991)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Ragnar_Granit"}, {"title": "Nobel Prize in Physiology or Medicine", "link": "https://wikipedia.org/wiki/Nobel_Prize_in_Physiology_or_Medicine"}]}, {"year": "1905", "text": "<PERSON>, English-Canadian runner (d. 2003)", "html": "1905 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English-Canadian runner (d. 2003)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Miles\"><PERSON></a>, English-Canadian runner (d. 2003)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1906", "text": "<PERSON>, Italian race car driver (d. 1966)", "html": "1906 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian race car driver (d. 1966)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian race car driver (d. 1966)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1906", "text": "<PERSON>, German general (d. 1945)", "html": "1906 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German general (d. 1945)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German general (d. 1945)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1906", "text": "<PERSON>, German-American linguist and translator (d. 1970)", "html": "1906 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German-American linguist and translator (d. 1970)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German-American linguist and translator (d. 1970)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1907", "text": "<PERSON>, American anthropologist and academic (d. 1995)", "html": "1907 - <a href=\"https://wikipedia.org/wiki/Sol_Tax\" title=\"Sol Tax\">Sol Tax</a>, American anthropologist and academic (d. 1995)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Sol_Tax\" title=\"Sol Tax\">Sol Tax</a>, American anthropologist and academic (d. 1995)", "links": [{"title": "Sol Tax", "link": "https://wikipedia.org/wiki/Sol_Tax"}]}, {"year": "1908", "text": "<PERSON><PERSON>, American singer-songwriter and actress (d. 1996)", "html": "1908 - <a href=\"https://wikipedia.org/wiki/<PERSON>sy_<PERSON>\" title=\"Patsy <PERSON>\"><PERSON><PERSON></a>, American singer-songwriter and actress (d. 1996)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>sy_<PERSON>\" title=\"Patsy Montana\"><PERSON><PERSON></a>, American singer-songwriter and actress (d. 1996)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>sy_<PERSON>"}]}, {"year": "1908", "text": "<PERSON><PERSON><PERSON>, Indian politician (d. 1963)", "html": "1908 - <a href=\"https://wikipedia.org/wiki/U._Muthuramalingam_Thevar\" class=\"mw-redirect\" title=\"U. Muthuramalingam Thevar\"><PERSON><PERSON></a>, Indian politician (d. 1963)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/U._Muthuramalingam_Thevar\" class=\"mw-redirect\" title=\"U. Muthuramalingam Thevar\"><PERSON><PERSON> <PERSON></a>, Indian politician (d. 1963)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/U._Mu<PERSON><PERSON><PERSON><PERSON><PERSON>_<PERSON>var"}]}, {"year": "1908", "text": "<PERSON>, English cricketer (d. 1967)", "html": "1908 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(cricketer,_born_1908)\" class=\"mw-redirect\" title=\"<PERSON> (cricketer, born 1908)\"><PERSON></a>, English cricketer (d. 1967)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(cricketer,_born_1908)\" class=\"mw-redirect\" title=\"<PERSON> (cricketer, born 1908)\"><PERSON></a>, English cricketer (d. 1967)", "links": [{"title": "<PERSON> (cricketer, born 1908)", "link": "https://wikipedia.org/wiki/<PERSON>_(cricketer,_born_1908)"}]}, {"year": "1908", "text": "<PERSON>, Marshal of the Soviet Union (d. 1984)", "html": "1908 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, <a href=\"https://wikipedia.org/wiki/Marshal_of_the_Soviet_Union\" title=\"Marshal of the Soviet Union\">Marshal of the Soviet Union</a> (d. 1984)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, <a href=\"https://wikipedia.org/wiki/Marshal_of_the_Soviet_Union\" title=\"Marshal of the Soviet Union\">Marshal of the Soviet Union</a> (d. 1984)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Marshal of the Soviet Union", "link": "https://wikipedia.org/wiki/Marshal_of_the_Soviet_Union"}]}, {"year": "1909", "text": "<PERSON><PERSON>, Indian-French physicist and academic (d. 1966)", "html": "1909 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Indian-French physicist and academic (d. 1966)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Indian-French physicist and academic (d. 1966)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "1910", "text": "<PERSON>, Spanish poet and playwright (d. 1942)", "html": "1910 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A1ndez\" title=\"<PERSON>\"><PERSON></a>, Spanish poet and playwright (d. 1942)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A1ndez\" title=\"<PERSON>\"><PERSON></a>, Spanish poet and playwright (d. 1942)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Miguel_Hern%C3%A1ndez"}]}, {"year": "1910", "text": "<PERSON>, Italian-Monegasque organist and composer (d. 1994)", "html": "1910 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian-Monegasque organist and composer (d. 1994)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian-Monegasque organist and composer (d. 1994)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1911", "text": "<PERSON>, American actress (d. 2005)", "html": "1911 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress (d. 2005)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress (d. 2005)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1914", "text": "<PERSON>, American minister and composer (d. 1986)", "html": "1914 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American minister and composer (d. 1986)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American minister and composer (d. 1986)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1914", "text": "<PERSON><PERSON><PERSON>, Basotho lawyer and politician, 2nd Prime Minister of Lesotho (d. 1987)", "html": "1914 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON> Jonathan\"><PERSON><PERSON><PERSON></a>, Basotho lawyer and politician, 2nd <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Lesotho\" class=\"mw-redirect\" title=\"Prime Minister of Lesotho\">Prime Minister of Lesotho</a> (d. 1987)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Basotho lawyer and politician, 2nd <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Lesotho\" class=\"mw-redirect\" title=\"Prime Minister of Lesotho\">Prime Minister of Lesotho</a> (d. 1987)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}, {"title": "Prime Minister of Lesotho", "link": "https://wikipedia.org/wiki/Prime_Minister_of_Lesotho"}]}, {"year": "1914", "text": "<PERSON>, English actress (d. 2013)", "html": "1914 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actress (d. 2013)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actress (d. 2013)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1915", "text": "<PERSON>, American journalist and producer (d. 1998)", "html": "1915 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American journalist and producer (d. 1998)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American journalist and producer (d. 1998)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "1915", "text": "<PERSON>, American-Swiss actress and singer (d. 2009)", "html": "1915 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American-Swiss actress and singer (d. 2009)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American-Swiss actress and singer (d. 2009)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1916", "text": "<PERSON>, American baseball player (d. 1995)", "html": "1916 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Leon Day\"><PERSON></a>, American baseball player (d. 1995)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Leon Day\"><PERSON></a>, American baseball player (d. 1995)", "links": [{"title": "Leon Day", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1917", "text": "<PERSON>, American baseball player, coach, and manager (d. 2010)", "html": "1917 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player, coach, and manager (d. 2010)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player, coach, and manager (d. 2010)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1917", "text": "<PERSON><PERSON>, Estonian writer and poet (d. 1994)", "html": "1917 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Estonian writer and poet (d. 1994)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Estonian writer and poet (d. 1994)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1917", "text": "<PERSON>, Russian field marshal (d. 1994)", "html": "1917 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian field marshal (d. 1994)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian field marshal (d. 1994)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1917", "text": "<PERSON>, French race car driver (d. 2005)", "html": "1917 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French race car driver (d. 2005)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French race car driver (d. 2005)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1920", "text": "<PERSON>, Irish sportsman (d. 1979)", "html": "1920 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Christy Ring\"><PERSON></a>, Irish sportsman (d. 1979)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Christy Ring\"><PERSON></a>, Irish sportsman (d. 1979)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1921", "text": "<PERSON><PERSON>-<PERSON><PERSON><PERSON><PERSON> (d. 2016)", "html": "1921 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>-Bogatkina\" title=\"<PERSON><PERSON>-Bogatkina\">Valli <PERSON>-Bogatkina</a> (d. 2016)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Val<PERSON>_Le<PERSON>-Bogatkina\" title=\"Val<PERSON>-Bogatkina\">Valli Le<PERSON>-Bogatkina</a> (d. 2016)", "links": [{"title": "<PERSON><PERSON>-Bogatkina", "link": "https://wikipedia.org/wiki/Valli_Lember-Bogatkina"}]}, {"year": "1922", "text": "<PERSON>, Ukrainian exile (d. 1993)", "html": "1922 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Ukrainian exile (d. 1993)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Ukrainian exile (d. 1993)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1922", "text": "<PERSON><PERSON>, Romanian Holocaust survivor (d. 2021)", "html": "1922 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_%C8%9Auc%C4%83rman\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Romanian Holocaust survivor (d. 2021)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_%C8%9Auc%C4%83rman\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Romanian Holocaust survivor (d. 2021)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Iancu_%C8%9Auc%C4%83rman"}]}, {"year": "1922", "text": "<PERSON>, American actress and singer (d. 2011)", "html": "1922 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress and singer (d. 2011)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress and singer (d. 2011)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1923", "text": "<PERSON>, American poet and academic (d. 2011)", "html": "1923 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American poet and academic (d. 2011)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American poet and academic (d. 2011)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_Oden"}]}, {"year": "1924", "text": "<PERSON>, American soldier and engineer (d. 2015)", "html": "1924 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American soldier and engineer (d. 2015)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American soldier and engineer (d. 2015)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1925", "text": "<PERSON>, American singer and bandleader (d. 1999)", "html": "1925 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer and bandleader (d. 1999)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer and bandleader (d. 1999)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1926", "text": "<PERSON>, Belgian race car driver and manager (d. 2010)", "html": "1926 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Belgian race car driver and manager (d. 2010)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Belgian race car driver and manager (d. 2010)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1927", "text": "<PERSON>, American baseball player and manager (d. 1999)", "html": "1927 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player and manager (d. 1999)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player and manager (d. 1999)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1928", "text": "<PERSON>, American microbiologist and academic, Nobel Prize laureate (d. 1999)", "html": "1928 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American microbiologist and academic, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Physiology_or_Medicine\" title=\"Nobel Prize in Physiology or Medicine\">Nobel Prize</a> laureate (d. 1999)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American microbiologist and academic, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Physiology_or_Medicine\" title=\"Nobel Prize in Physiology or Medicine\">Nobel Prize</a> laureate (d. 1999)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Nobel Prize in Physiology or Medicine", "link": "https://wikipedia.org/wiki/Nobel_Prize_in_Physiology_or_Medicine"}]}, {"year": "1929", "text": "<PERSON>, English author", "html": "1929 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English author", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English author", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1929", "text": "<PERSON>, Argentinian actress (d. 2012)", "html": "1929 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Argentinian actress (d. 2012)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Argentinian actress (d. 2012)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1930", "text": "<PERSON><PERSON><PERSON>, Spanish-American director and cinematographer (d. 1992)", "html": "1930 - <a href=\"https://wikipedia.org/wiki/N%C3%A9stor_Almendros\" title=\"<PERSON>éstor <PERSON>\"><PERSON><PERSON><PERSON></a>, Spanish-American director and cinematographer (d. 1992)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/N%C3%A9stor_Almendros\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Spanish-American director and cinematographer (d. 1992)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/N%C3%A9stor_Almendros"}]}, {"year": "1930", "text": "<PERSON>, English economist and academic (d. 2022)", "html": "1930 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(economist)\" title=\"<PERSON> (economist)\"><PERSON></a>, English economist and academic (d. 2022)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(economist)\" title=\"<PERSON> (economist)\"><PERSON></a>, English economist and academic (d. 2022)", "links": [{"title": "<PERSON> (economist)", "link": "https://wikipedia.org/wiki/<PERSON>(economist)"}]}, {"year": "1930", "text": "<PERSON>, American trumpet player and composer (d. 1956)", "html": "1930 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American trumpet player and composer (d. 1956)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American trumpet player and composer (d. 1956)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1930", "text": "<PERSON>, American basketball player (d. 2013)", "html": "1930 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player (d. 2013)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player (d. 2013)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1931", "text": "<PERSON>, American lieutenant and politician (d. 2014)", "html": "1931 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lieutenant and politician (d. 2014)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lieutenant and politician (d. 2014)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1931", "text": "<PERSON>, Manx archaeologist, historian, and curator", "html": "1931 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Manx archaeologist, historian, and curator", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Manx archaeologist, historian, and curator", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1932", "text": "<PERSON><PERSON>, Indian historian and author (d. 2013)", "html": "1932 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Indian historian and author (d. 2013)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Indian historian and author (d. 2013)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1932", "text": "<PERSON>, French director, producer, and screenwriter (d. 1995)", "html": "1932 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French director, producer, and screenwriter (d. 1995)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French director, producer, and screenwriter (d. 1995)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1933", "text": "<PERSON>, New Zealand gardener and television host (d. 2012)", "html": "1933 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, New Zealand gardener and television host (d. 2012)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, New Zealand gardener and television host (d. 2012)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1934", "text": "<PERSON>, Welsh-Australian rugby player and coach (d. 2024)", "html": "1934 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Welsh-Australian rugby player and coach (d. 2024)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Welsh-Australian rugby player and coach (d. 2024)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1934", "text": "<PERSON><PERSON>, Dutch flute player and conductor (d. 2014)", "html": "1934 - <a href=\"https://wikipedia.org/wiki/Frans_Br%C3%BCggen\" title=\"Frans Brüggen\"><PERSON><PERSON></a>, Dutch flute player and conductor (d. 2014)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Frans_Br%C3%BCggen\" title=\"Frans Brüggen\"><PERSON><PERSON></a>, Dutch flute player and conductor (d. 2014)", "links": [{"title": "Frans Brüggen", "link": "https://wikipedia.org/wiki/Frans_Br%C3%BCggen"}]}, {"year": "1935", "text": "<PERSON>, American journalist and author", "html": "1935 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American journalist and author", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American journalist and author", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1935", "text": "<PERSON><PERSON><PERSON>, Hungarian-Swiss author (d. 2011)", "html": "1935 - <a href=\"https://wikipedia.org/wiki/%C3%81gota_Krist%C3%B3f\" title=\"<PERSON><PERSON><PERSON>tóf\"><PERSON><PERSON><PERSON></a>, Hungarian-Swiss author (d. 2011)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/%C3%81gota_Krist%C3%B3f\" title=\"<PERSON><PERSON><PERSON>tóf\"><PERSON><PERSON><PERSON></a>, Hungarian-Swiss author (d. 2011)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/%C3%81gota_Krist%C3%B3f"}]}, {"year": "1935", "text": "<PERSON>, American baseball player", "html": "1935 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(baseball)\" title=\"<PERSON> (baseball)\"><PERSON></a>, American baseball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(baseball)\" title=\"<PERSON> (baseball)\"><PERSON></a>, American baseball player", "links": [{"title": "<PERSON> (baseball)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(baseball)"}]}, {"year": "1935", "text": "<PERSON>, English director, producer, and screenwriter (d. 2013)", "html": "1935 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Michael Winner\"><PERSON></a>, English director, producer, and screenwriter (d. 2013)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Michael Winner\"><PERSON></a>, English director, producer, and screenwriter (d. 2013)", "links": [{"title": "<PERSON> Winner", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1936", "text": "<PERSON><PERSON>, Ukrainian gymnast and trainer (d. 2005)", "html": "1936 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Ukrainian gymnast and trainer (d. 2005)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Ukrainian gymnast and trainer (d. 2005)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1936", "text": "<PERSON>, American football player and coach", "html": "1936 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player and coach", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1937", "text": "<PERSON>, French actor, director, producer, and screenwriter", "html": "1937 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French actor, director, producer, and screenwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French actor, director, producer, and screenwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1937", "text": "<PERSON>, Welsh rugby player (d. 2023)", "html": "1937 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(rugby_union)\" title=\"<PERSON> (rugby union)\"><PERSON></a>, Welsh rugby player (d. 2023)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(rugby_union)\" title=\"<PERSON> (rugby union)\"><PERSON></a>, Welsh rugby player (d. 2023)", "links": [{"title": "<PERSON> (rugby union)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(rugby_union)"}]}, {"year": "1938", "text": "<PERSON>, Australian novelist, short story writer, and playwright (d. 2014)", "html": "1938 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian novelist, short story writer, and playwright (d. 2014)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian novelist, short story writer, and playwright (d. 2014)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Morris_Lurie"}]}, {"year": "1939", "text": "<PERSON>, English statistician and academic (d. 2020)", "html": "1939 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English statistician and academic (d. 2020)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English statistician and academic (d. 2020)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1939", "text": "<PERSON><PERSON>, American biologist and academic, Nobel Prize laureate", "html": "1939 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American biologist and academic, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Physiology_or_Medicine\" title=\"Nobel Prize in Physiology or Medicine\">Nobel Prize</a> laureate", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON> <PERSON></a>, American biologist and academic, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Physiology_or_Medicine\" title=\"Nobel Prize in Physiology or Medicine\">Nobel Prize</a> laureate", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>"}, {"title": "Nobel Prize in Physiology or Medicine", "link": "https://wikipedia.org/wiki/Nobel_Prize_in_Physiology_or_Medicine"}]}, {"year": "1939", "text": "<PERSON>, American singer-songwriter and producer", "html": "1939 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Holland\"><PERSON></a>, American singer-songwriter and producer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1939", "text": "<PERSON>, American singer-songwriter and model", "html": "1939 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>lick\" title=\"<PERSON> Slick\"><PERSON></a>, American singer-songwriter and model", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>lick\" title=\"<PERSON> Slick\"><PERSON></a>, American singer-songwriter and model", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>lick"}]}, {"year": "1941", "text": "<PERSON>, French-English lawyer, journalist, and academic (d. 2019)", "html": "1941 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French-English lawyer, journalist, and academic (d. 2019)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French-English lawyer, journalist, and academic (d. 2019)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Marcel_Berlins"}]}, {"year": "1941", "text": "<PERSON>, Russian-Estonian linguist and academic", "html": "1941 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian-Estonian linguist and academic", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian-Estonian linguist and academic", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1941", "text": "<PERSON>, German physicist and academic, Nobel Prize laureate", "html": "1941 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>._H%C3%A4nsch\" title=\"<PERSON>\"><PERSON></a>, German physicist and academic, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Physics\" title=\"Nobel Prize in Physics\">Nobel Prize</a> laureate", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>._H%C3%A4nsch\" title=\"<PERSON>\"><PERSON></a>, German physicist and academic, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Physics\" title=\"Nobel Prize in Physics\">Nobel Prize</a> laureate", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Theodor_W._H%C3%A4nsch"}, {"title": "Nobel Prize in Physics", "link": "https://wikipedia.org/wiki/Nobel_Prize_in_Physics"}]}, {"year": "1941", "text": "<PERSON>, American singer-songwriter and producer", "html": "1941 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and producer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1941", "text": "<PERSON>, English footballer and sportscaster", "html": "1941 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(footballer,_born_1941)\" title=\"<PERSON> (footballer, born 1941)\"><PERSON></a>, English footballer and sportscaster", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(footballer,_born_1941)\" title=\"<PERSON> (footballer, born 1941)\"><PERSON></a>, English footballer and sportscaster", "links": [{"title": "<PERSON> (footballer, born 1941)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(footballer,_born_1941)"}]}, {"year": "1942", "text": "Sven<PERSON><PERSON>, Swedish composer and historian (d. 2019)", "html": "1942 - <a href=\"https://wikipedia.org/wiki/<PERSON>-<PERSON>_<PERSON>%C3%B6m\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Swedish composer and historian (d. 2019)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>-<PERSON>_<PERSON>%C3%B6m\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Swedish composer and historian (d. 2019)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>-<PERSON>_<PERSON>%C3%B6m"}]}, {"year": "1943", "text": "<PERSON>, Belgian poet and translator", "html": "1943 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Belgian poet and translator", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Belgian poet and translator", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1943", "text": "<PERSON>, Canadian actress", "html": "1943 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian actress", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1943", "text": "<PERSON>, Baron <PERSON>, English union leader and politician", "html": "1943 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Baron_<PERSON>\" title=\"<PERSON>, Baron <PERSON>\"><PERSON>, Baron <PERSON></a>, English union leader and politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>,_Baron_<PERSON>\" title=\"<PERSON>, Baron <PERSON>\"><PERSON>, Baron <PERSON></a>, English union leader and politician", "links": [{"title": "<PERSON>, Baron <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>,_Baron_<PERSON>"}]}, {"year": "1944", "text": "<PERSON>, Iraqi businessman and politician (d. 2015)", "html": "1944 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Iraqi businessman and politician (d. 2015)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Iraqi businessman and politician (d. 2015)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1945", "text": "<PERSON>, American actor, comedian, director, and producer", "html": "1945 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor, comedian, director, and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor, comedian, director, and producer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1945", "text": "<PERSON><PERSON>, American actress (d. 2024) ", "html": "1945 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American actress (d. 2024) ", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American actress (d. 2024) ", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1946", "text": "<PERSON>, American captain, pilot, and astronaut", "html": "1946 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American captain, pilot, and astronaut", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American captain, pilot, and astronaut", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1946", "text": "<PERSON>, American journalist", "html": "1946 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American journalist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American journalist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1946", "text": "<PERSON>, English economist, author, and academic", "html": "1946 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English economist, author, and academic", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English economist, author, and academic", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1946", "text": "<PERSON>, Welsh drummer", "html": "1946 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Welsh drummer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Welsh drummer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1947", "text": "<PERSON>, American soldier (d. 1968)", "html": "1947 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American soldier (d. 1968)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American soldier (d. 1968)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1947", "text": "<PERSON>, American illustrator and designer", "html": "1947 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American illustrator and designer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American illustrator and designer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1947", "text": "<PERSON>, American singer-songwriter and bass player", "html": "1947 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and bass player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and bass player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1947", "text": "<PERSON><PERSON><PERSON>, American screenwriter and producer", "html": "1947 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American screenwriter and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American screenwriter and producer", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1948", "text": "<PERSON>, English dancer and choreographer", "html": "1948 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(choreographer)\" title=\"<PERSON> (choreographer)\"><PERSON></a>, English dancer and choreographer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(choreographer)\" title=\"<PERSON> (choreographer)\"><PERSON></a>, English dancer and choreographer", "links": [{"title": "<PERSON> (choreographer)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(choreographer)"}]}, {"year": "1948", "text": "<PERSON>, Australian actor and screenwriter", "html": "1948 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian actor and screenwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian actor and screenwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1949", "text": "<PERSON>, American murderer (d. 1996)", "html": "1949 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American murderer (d. 1996)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American murderer (d. 1996)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1949", "text": "<PERSON>, American actor", "html": "1949 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1950", "text": "<PERSON>, American basketball player and broadcaster", "html": "1950 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player and broadcaster", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player and broadcaster", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1950", "text": "<PERSON>, Australian rugby league player and coach", "html": "1950 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian rugby league player and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian rugby league player and coach", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1951", "text": "<PERSON>., American race car driver and businessman (d. 2000)", "html": "1951 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_Jr.\" title=\"<PERSON> Jr.\"><PERSON>.</a>, American race car driver and businessman (d. 2000)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_Jr.\" title=\"<PERSON> Jr.\"><PERSON>.</a>, American race car driver and businessman (d. 2000)", "links": [{"title": "<PERSON> Jr.", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>."}]}, {"year": "1951", "text": "<PERSON><PERSON><PERSON>, Indian drummer and songwriter", "html": "1951 - <a href=\"https://wikipedia.org/wiki/Trilok_Gurtu\" title=\"Trilok Gurtu\"><PERSON><PERSON><PERSON></a>, Indian drummer and songwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Trilok_Gurtu\" title=\"Trilok Gurtu\"><PERSON><PERSON><PERSON></a>, Indian drummer and songwriter", "links": [{"title": "Trilok Gurtu", "link": "https://wikipedia.org/wiki/Trilok_Gurtu"}]}, {"year": "1951", "text": "<PERSON>, American actor", "html": "1951 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1951", "text": "<PERSON><PERSON><PERSON>, American singer and conga player", "html": "1951 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American singer and <a href=\"https://wikipedia.org/wiki/Conga\" title=\"Conga\">conga</a> player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American singer and <a href=\"https://wikipedia.org/wiki/Conga\" title=\"Conga\">conga</a> player", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}, {"title": "Conga", "link": "https://wikipedia.org/wiki/Conga"}]}, {"year": "1953", "text": "<PERSON>, Dutch-American lawyer and politician", "html": "1953 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch-American lawyer and politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch-American lawyer and politician", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1953", "text": "<PERSON>, American actor, director, and screenwriter", "html": "1953 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor, director, and screenwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor, director, and screenwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1954", "text": "<PERSON><PERSON><PERSON>, Egyptian footballer", "html": "1954 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Egyptian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Egyptian footballer", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1954", "text": "<PERSON><PERSON>, American country singer-songwriter", "html": "1954 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\">T<PERSON> <PERSON></a>, American country singer-songwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON> <PERSON></a>, American country singer-songwriter", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1954", "text": "<PERSON><PERSON>, American country singer-songwriter", "html": "1954 - <a href=\"https://wikipedia.org/wiki/The_Kendalls\" title=\"The Kendalls\"><PERSON><PERSON></a>, American country singer-songwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/The_Kendalls\" title=\"The Kendalls\"><PERSON><PERSON></a>, American country singer-songwriter", "links": [{"title": "The Kendalls", "link": "https://wikipedia.org/wiki/The_Kendalls"}]}, {"year": "1954", "text": "<PERSON>, Peruvian-English photographer", "html": "1954 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Peruvian-English photographer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Peruvian-English photographer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1955", "text": "<PERSON>, American lawyer and politician, 28th Attorney General of North Dakota", "html": "1955 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and politician, 28th <a href=\"https://wikipedia.org/wiki/Attorney_General_of_North_Dakota\" class=\"mw-redirect\" title=\"Attorney General of North Dakota\">Attorney General of North Dakota</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and politician, 28th <a href=\"https://wikipedia.org/wiki/Attorney_General_of_North_Dakota\" class=\"mw-redirect\" title=\"Attorney General of North Dakota\">Attorney General of North Dakota</a>", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Attorney General of North Dakota", "link": "https://wikipedia.org/wiki/Attorney_General_of_North_Dakota"}]}, {"year": "1956", "text": "<PERSON>, English actress", "html": "1956 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actress", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1957", "text": "<PERSON><PERSON><PERSON>, Israeli violinist and conductor", "html": "1957 - <a href=\"https://wikipedia.org/wiki/<PERSON>hl<PERSON>_<PERSON>z\" title=\"Shl<PERSON> Mintz\"><PERSON><PERSON><PERSON></a>, Israeli violinist and conductor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>hl<PERSON>_<PERSON>z\" title=\"Shl<PERSON>z\"><PERSON><PERSON><PERSON></a>, Israeli violinist and conductor", "links": [{"title": "Shlomo Mintz", "link": "https://wikipedia.org/wiki/Shlomo_Mintz"}]}, {"year": "1957", "text": "<PERSON>, American actor, game show host, and producer", "html": "1957 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor, game show host, and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor, game show host, and producer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1958", "text": "<PERSON><PERSON>, Norwegian saxophonist and composer (d. 2014)", "html": "1958 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Norwegian saxophonist and composer (d. 2014)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Norwegian saxophonist and composer (d. 2014)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1958", "text": "<PERSON>, American football player (d. 1983)", "html": "1958 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player (d. 1983)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player (d. 1983)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1958", "text": "<PERSON><PERSON>, American cyclist and photographer", "html": "1958 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_d%27Viola\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American cyclist and photographer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_d%27Viola\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American cyclist and photographer", "links": [{"title": "<PERSON><PERSON>'<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_d%27Viola"}]}, {"year": "1958", "text": "<PERSON><PERSON><PERSON>, Icelandic basketball player and coach", "html": "1958 - <a href=\"https://wikipedia.org/wiki/P%C3%A9tur_Gu%C3%<PERSON><PERSON><PERSON><PERSON>_(basketball)\" title=\"<PERSON><PERSON><PERSON> (basketball)\"><PERSON><PERSON><PERSON></a>, Icelandic basketball player and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/P%C3%A9tur_Gu%C3%<PERSON><PERSON><PERSON><PERSON>_(basketball)\" title=\"<PERSON><PERSON><PERSON> (basketball)\"><PERSON><PERSON><PERSON></a>, Icelandic basketball player and coach", "links": [{"title": "<PERSON><PERSON><PERSON> (basketball)", "link": "https://wikipedia.org/wiki/P%C3%A9tur_Gu%C3%B<PERSON><PERSON><PERSON>_(basketball)"}]}, {"year": "1959", "text": "<PERSON>, French actor, singer, and game show host", "html": "1959 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%27\" title=\"<PERSON>'\"><PERSON>'</a>, French actor, singer, and game show host", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%27\" title=\"<PERSON>'\"><PERSON>'</a>, French actor, singer, and game show host", "links": [{"title": "<PERSON>'", "link": "https://wikipedia.org/wiki/Vincent_Lagaf%27"}]}, {"year": "1960", "text": "<PERSON><PERSON><PERSON>, American actress and singer", "html": "1960 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American actress and singer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American actress and singer", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1960", "text": "<PERSON>, American singer-songwriter", "html": "1960 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1960", "text": "<PERSON>, Argentinian footballer, coach, and manager (d. 2020)", "html": "1960 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Argentinian footballer, coach, and manager (d. 2020)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Diego <PERSON>\"><PERSON></a>, Argentinian footballer, coach, and manager (d. 2020)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Diego_<PERSON>"}]}, {"year": "1961", "text": "<PERSON>, American baseball player", "html": "1961 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1961", "text": "<PERSON><PERSON><PERSON>, Greek economist and politician, Greek Minister of Finance", "html": "1961 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Greek economist and politician, <a href=\"https://wikipedia.org/wiki/Ministry_of_Finance_(Greece)\" class=\"mw-redirect\" title=\"Ministry of Finance (Greece)\">Greek Minister of Finance</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Greek economist and politician, <a href=\"https://wikipedia.org/wiki/Ministry_of_Finance_(Greece)\" class=\"mw-redirect\" title=\"Ministry of Finance (Greece)\">Greek Minister of Finance</a>", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>konstantino<PERSON>"}, {"title": "Ministry of Finance (Greece)", "link": "https://wikipedia.org/wiki/Ministry_of_Finance_(Greece)"}]}, {"year": "1961", "text": "<PERSON>, American comedian and television host", "html": "1961 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American comedian and television host", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American comedian and television host", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1962", "text": "<PERSON>, German footballer and manager", "html": "1962 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German footballer and manager", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German footballer and manager", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1962", "text": "<PERSON>, Puerto Rican baseball player", "html": "1962 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Puerto Rican baseball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Puerto Rican baseball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1962", "text": "<PERSON>, Jamaican cricketer", "html": "1962 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Jamaican cricketer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Jamaican cricketer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1963", "text": "<PERSON>, American actor and producer", "html": "1963 - <a href=\"https://wikipedia.org/wiki/Michael_<PERSON>\" title=\"Michael Beach\"><PERSON></a>, American actor and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Michael_<PERSON>\" title=\"Michael Beach\"><PERSON></a>, American actor and producer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Michael_<PERSON>"}]}, {"year": "1963", "text": "<PERSON>, American video game designer and programmer", "html": "1963 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American video game designer and programmer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American video game designer and programmer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1963", "text": "<PERSON>, American-English journalist and author", "html": "1963 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American-English journalist and author", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American-English journalist and author", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1963", "text": "<PERSON>, Australian cricketer and coach", "html": "1963 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian cricketer and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian cricketer and coach", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1963", "text": "<PERSON><PERSON>, American actress", "html": "1963 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American actress", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1964", "text": "<PERSON><PERSON>, Emirati footballer", "html": "1964 - <a href=\"https://wikipedia.org/wiki/Adnan_Al_Talyani\" title=\"Adnan Al Talyani\"><PERSON><PERSON></a>, Emirati footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Ad<PERSON>_<PERSON>_<PERSON>lya<PERSON>\" title=\"Adnan Al Talyani\"><PERSON><PERSON></a>, Emirati footballer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1964", "text": "<PERSON><PERSON><PERSON>, Bangladeshi journalist and author", "html": "1964 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Bangladeshi journalist and author", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Bangladeshi journalist and author", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1964", "text": "<PERSON>, American poker player", "html": "1964 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American poker player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American poker player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1965", "text": "<PERSON>, English singer-songwriter, guitarist, and actor", "html": "1965 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English singer-songwriter, guitarist, and actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English singer-songwriter, guitarist, and actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1967", "text": "<PERSON>, Canadian ice hockey player", "html": "1967 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Brad_<PERSON>"}]}, {"year": "1967", "text": "<PERSON><PERSON>, Greek violinist and conductor", "html": "1967 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Greek violinist and conductor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Greek violinist and conductor", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1968", "text": "<PERSON><PERSON>, French biathlete (d. 2013)", "html": "1968 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, French biathlete (d. 2013)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, French biathlete (d. 2013)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1968", "text": "<PERSON>, American actor, director, and producer", "html": "1968 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor, director, and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor, director, and producer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1968", "text": "<PERSON>, American singer-songwriter and guitarist", "html": "1968 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and guitarist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and guitarist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1969", "text": "<PERSON><PERSON>, Czech lawyer and politician, 5th Prime Minister of the Czech Republic (d. 2015)", "html": "1969 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Czech lawyer and politician, 5th <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_the_Czech_Republic\" title=\"Prime Minister of the Czech Republic\">Prime Minister of the Czech Republic</a> (d. 2015)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Czech lawyer and politician, 5th <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_the_Czech_Republic\" title=\"Prime Minister of the Czech Republic\">Prime Minister of the Czech Republic</a> (d. 2015)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}, {"title": "Prime Minister of the Czech Republic", "link": "https://wikipedia.org/wiki/Prime_Minister_of_the_Czech_Republic"}]}, {"year": "1969", "text": "<PERSON>, Canadian rapper and reggae singer-songwriter", "html": "1969 - <a href=\"https://wikipedia.org/wiki/<PERSON>_(musician)\" title=\"<PERSON> (musician)\"><PERSON></a>, Canadian rapper and reggae singer-songwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(musician)\" title=\"<PERSON> (musician)\"><PERSON></a>, Canadian rapper and reggae singer-songwriter", "links": [{"title": "<PERSON> (musician)", "link": "https://wikipedia.org/wiki/<PERSON>_(musician)"}]}, {"year": "1969", "text": "<PERSON><PERSON><PERSON>, Greek basketball player", "html": "1969 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Greek basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Greek basketball player", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1970", "text": "<PERSON>, American comedian and game show host", "html": "1970 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American comedian and game show host", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American comedian and game show host", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1970", "text": "<PERSON>, American visual effects designer and television presenter", "html": "1970 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American visual effects designer and television presenter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American visual effects designer and television presenter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1970", "text": "<PERSON><PERSON><PERSON>, Filipino journalist and actress", "html": "1970 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Filipino journalist and actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Filipino journalist and actress", "links": [{"title": "<PERSON>-<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>-<PERSON>"}]}, {"year": "1970", "text": "<PERSON>, American actor", "html": "1970 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(actor)\" title=\"<PERSON> (actor)\"><PERSON></a>, American actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(actor)\" title=\"<PERSON> (actor)\"><PERSON></a>, American actor", "links": [{"title": "<PERSON> (actor)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(actor)"}]}, {"year": "1970", "text": "<PERSON><PERSON>, American actress", "html": "1970 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American actress", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1970", "text": "<PERSON><PERSON><PERSON><PERSON>, Greek discus thrower", "html": "1970 - <a href=\"https://wikipedia.org/wiki/Ekaterini_Voggoli\" title=\"Ekaterini Voggoli\">Ekateri<PERSON> Voggoli</a>, Greek discus thrower", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Ekaterini_Voggoli\" title=\"Ekaterini Voggoli\">Ekateri<PERSON> Voggoli</a>, Greek discus thrower", "links": [{"title": "Ekaterini <PERSON>i", "link": "https://wikipedia.org/wiki/Ekaterini_Voggoli"}]}, {"year": "1971", "text": "<PERSON><PERSON>, Slovenian-German footballer and manager", "html": "1971 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Slovenian-German footballer and manager", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Slovenian-German footballer and manager", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1971", "text": "<PERSON>, Canadian actor, voice actor and screenwriters", "html": "1971 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian actor, voice actor and screenwriters", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian actor, voice actor and screenwriters", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1971", "text": "<PERSON><PERSON><PERSON>, Greek basketball player", "html": "1971 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Greek basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Greek basketball player", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1971", "text": "<PERSON><PERSON>, Dutch field hockey player", "html": "1971 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_van_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON> van <PERSON>\"><PERSON><PERSON></a>, Dutch field hockey player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_van_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON> van <PERSON>\"><PERSON><PERSON></a>, Dutch field hockey player", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1972", "text": "<PERSON>, English actress, producer, and screenwriter", "html": "1972 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actress, producer, and screenwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actress, producer, and screenwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1973", "text": "<PERSON>, Australian rugby league player and official", "html": "1973 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian rugby league player and official", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian rugby league player and official", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1973", "text": "<PERSON><PERSON><PERSON>, Colombian lawyer and journalist", "html": "1973 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Colombian lawyer and journalist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Colombian lawyer and journalist", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>l<PERSON>_<PERSON>"}]}, {"year": "1973", "text": "<PERSON>, Canadian wrestler and actor", "html": "1973 - <a href=\"https://wikipedia.org/wiki/<PERSON>_(wrestler)\" title=\"<PERSON> (wrestler)\"><PERSON></a>, Canadian wrestler and actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(wrestler)\" title=\"<PERSON> (wrestler)\"><PERSON></a>, Canadian wrestler and actor", "links": [{"title": "<PERSON> (wrestler)", "link": "https://wikipedia.org/wiki/<PERSON>_(wrestler)"}]}, {"year": "1973", "text": "<PERSON>, English footballer and manager", "html": "1973 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English footballer and manager", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English footballer and manager", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1973", "text": "<PERSON><PERSON>, Turkish actor, producer, and screenwriter", "html": "1973 - <a href=\"https://wikipedia.org/wiki/Raci_%C5%9Ea%C5%9Fmaz\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Turkish actor, producer, and screenwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Raci_%C5%9Ea%C5%9Fmaz\" title=\"<PERSON><PERSON>ş<PERSON>z\"><PERSON><PERSON></a>, Turkish actor, producer, and screenwriter", "links": [{"title": "<PERSON>ci <PERSON>", "link": "https://wikipedia.org/wiki/Raci_%C5%9Ea%C5%9Fmaz"}]}, {"year": "1975", "text": "<PERSON>, English-Canadian singer-songwriter, guitarist, and producer", "html": "1975 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%27Sa\" title=\"<PERSON>\"><PERSON></a>, English-Canadian singer-songwriter, guitarist, and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%27Sa\" title=\"<PERSON>\"><PERSON></a>, English-Canadian singer-songwriter, guitarist, and producer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Ian_D%27Sa"}]}, {"year": "1975", "text": "<PERSON>, Venezuelan baseball player", "html": "1975 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Venezuelan baseball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Venezuelan baseball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1976", "text": "<PERSON>, Trinidadian footballer", "html": "1976 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> John\"><PERSON></a>, Trinidadian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> John\"><PERSON></a>, Trinidadian footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1976", "text": "<PERSON><PERSON>, Turkish footballer and manager", "html": "1976 - <a href=\"https://wikipedia.org/wiki/%C3%9Cmit_%C3%96zat\" title=\"Ümit Özat\"><PERSON><PERSON></a>, Turkish footballer and manager", "no_year_html": "<a href=\"https://wikipedia.org/wiki/%C3%9Cmit_%C3%96zat\" title=\"Ümit Özat\"><PERSON><PERSON></a>, Turkish footballer and manager", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/%C3%9Cmit_%C3%96zat"}]}, {"year": "1976", "text": "<PERSON>, American basketball player", "html": "1976 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1978", "text": "<PERSON>, American football player", "html": "1978 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1978", "text": "<PERSON>, American chef", "html": "1978 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> I<PERSON>\"><PERSON></a>, American chef", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Stephanie Izard\"><PERSON></a>, American chef", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1978", "text": "<PERSON>, American singer-songwriter and actor", "html": "1978 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1978", "text": "<PERSON>, English physician and politician", "html": "1978 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English physician and politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English physician and politician", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1978", "text": "<PERSON><PERSON>, New Zealand rugby player and cricketer", "html": "1978 - <a href=\"https://wikipedia.org/wiki/Derren_Witcombe\" title=\"Derren Witcombe\"><PERSON><PERSON></a>, New Zealand rugby player and cricketer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Derren_Witcombe\" title=\"Derren Witcombe\"><PERSON><PERSON></a>, New Zealand rugby player and cricketer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Derren_Witcombe"}]}, {"year": "1979", "text": "<PERSON>, American baseball player", "html": "1979 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(baseball)\" title=\"<PERSON> (baseball)\"><PERSON></a>, American baseball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(baseball)\" title=\"<PERSON> (baseball)\"><PERSON></a>, American baseball player", "links": [{"title": "<PERSON> (baseball)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(baseball)"}]}, {"year": "1980", "text": "<PERSON>, Filipino-Japanese basketball player", "html": "1980 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Filipino-Japanese basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Filipino-Japanese basketball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1980", "text": "<PERSON>, South Korean wrestler and mixed martial artist", "html": "1980 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>-man\" class=\"mw-redirect\" title=\"<PERSON> Hong-man\"><PERSON></a>, South Korean wrestler and mixed martial artist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>-man\" class=\"mw-redirect\" title=\"<PERSON> Hong-man\"><PERSON></a>, South Korean wrestler and mixed martial artist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>-man"}]}, {"year": "1980", "text": "<PERSON><PERSON><PERSON>, American basketball player", "html": "1980 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"Ka<PERSON><PERSON> Rush\"><PERSON><PERSON><PERSON></a>, American basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"Ka<PERSON><PERSON> Rush\"><PERSON><PERSON><PERSON></a>, American basketball player", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1981", "text": "<PERSON>, American actress", "html": "1981 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1981", "text": "<PERSON>, American magician and author", "html": "1981 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American magician and author", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American magician and author", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1981", "text": "<PERSON>, South Korean model and actress", "html": "1981 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, South Korean model and actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, South Korean model and actress", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>-<PERSON>yun"}]}, {"year": "1981", "text": "<PERSON><PERSON><PERSON>, Japanese singer and actress", "html": "1981 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese singer and actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese singer and actress", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1981", "text": "<PERSON>, Canadian actor", "html": "1981 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1981", "text": "<PERSON>, American baseball player", "html": "1981 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1981", "text": "<PERSON><PERSON>, American model and businesswoman", "html": "1981 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American model and businesswoman", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American model and businesswoman", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1982", "text": "<PERSON>, American ice hockey player", "html": "1982 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American ice hockey player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American ice hockey player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1982", "text": "<PERSON>, American baseball player", "html": "1982 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1982", "text": "<PERSON><PERSON><PERSON><PERSON>, French actress and model", "html": "1982 - <a href=\"https://wikipedia.org/wiki/Cl%C3%A9mence_Po%C3%A9sy\" title=\"Clémence Poésy\"><PERSON><PERSON><PERSON><PERSON></a>, French actress and model", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Cl%C3%A9mence_Po%C3%A9sy\" title=\"Clémence Poésy\"><PERSON><PERSON><PERSON><PERSON></a>, French actress and model", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Cl%C3%A9mence_Po%C3%A9sy"}]}, {"year": "1982", "text": "<PERSON><PERSON><PERSON>, American rapper", "html": "1982 - <a href=\"https://wikipedia.org/wiki/Stalley\" title=\"Stalley\"><PERSON><PERSON><PERSON></a>, American rapper", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Stalley\" title=\"Stalley\"><PERSON><PERSON><PERSON></a>, American rapper", "links": [{"title": "Stalley", "link": "https://wikipedia.org/wiki/Stalley"}]}, {"year": "1983", "text": "<PERSON>, American football player", "html": "1983 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1983", "text": "<PERSON>, Canadian soccer player", "html": "1983 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian soccer player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian soccer player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1983", "text": "<PERSON><PERSON>, Israeli footballer", "html": "1983 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Israeli footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Israeli footballer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1984", "text": "<PERSON><PERSON><PERSON>, Egyptian footballer", "html": "1984 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_(footballer)\" title=\"<PERSON><PERSON><PERSON> (footballer)\"><PERSON><PERSON><PERSON></a>, Egyptian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_(footballer)\" title=\"<PERSON><PERSON><PERSON> (footballer)\"><PERSON><PERSON><PERSON></a>, Egyptian footballer", "links": [{"title": "<PERSON><PERSON><PERSON> (footballer)", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_(footballer)"}]}, {"year": "1984", "text": "<PERSON>, American model and actress", "html": "1984 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American model and actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American model and actress", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1984", "text": "<PERSON>, Irish footballer", "html": "1984 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Irish footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Irish footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1984", "text": "<PERSON>, New Zealand rugby player", "html": "1984 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, New Zealand rugby player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, New Zealand rugby player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1984", "text": "<PERSON>, Canadian ice hockey player", "html": "1984 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Tyson_<PERSON>"}]}, {"year": "1985", "text": "<PERSON><PERSON><PERSON>, Estonian footballer", "html": "1985 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Estonian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Estonian footballer", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1986", "text": "<PERSON>, American baseball player", "html": "1986 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1986", "text": "<PERSON>, Austrian ski jumper", "html": "1986 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Austrian ski jumper", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Austrian ski jumper", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1986", "text": "<PERSON><PERSON><PERSON>, Japanese actor", "html": "1986 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese actor", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>a"}]}, {"year": "1986", "text": "<PERSON><PERSON>, American basketball player", "html": "1986 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_Young\" title=\"Tamera Young\"><PERSON><PERSON></a>, American basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_Young\" title=\"<PERSON><PERSON> Young\"><PERSON><PERSON></a>, American basketball player", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Tamera_Young"}]}, {"year": "1987", "text": "<PERSON>, Canadian entrepreneur, co-founder and chief scientist of LightSail Energy", "html": "1987 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian entrepreneur, co-founder and chief scientist of <a href=\"https://wikipedia.org/wiki/LightSail_Energy\" title=\"LightSail Energy\">LightSail Energy</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian entrepreneur, co-founder and chief scientist of <a href=\"https://wikipedia.org/wiki/LightSail_Energy\" title=\"LightSail Energy\">LightSail Energy</a>", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "LightSail Energy", "link": "https://wikipedia.org/wiki/LightSail_Energy"}]}, {"year": "1987", "text": "<PERSON>, American model", "html": "1987 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American model", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American model", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1987", "text": "<PERSON>, New Zealand footballer", "html": "1987 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, New Zealand footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, New Zealand footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1988", "text": "<PERSON><PERSON>, American actress and singer", "html": "1988 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American actress and singer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American actress and singer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1989", "text": "<PERSON>, Austrian-English footballer", "html": "1989 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Austrian-English footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Austrian-English footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1989", "text": "<PERSON><PERSON><PERSON>, Russian-American gymnast and actress", "html": "1989 - <a href=\"https://wikipedia.org/wiki/Na<PERSON><PERSON>_<PERSON>\" title=\"Na<PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Russian-American gymnast and actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Na<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Russian-American gymnast and actress", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Nastia_<PERSON>"}]}, {"year": "1990", "text": "<PERSON>, American baseball player", "html": "1990 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1990", "text": "<PERSON><PERSON><PERSON><PERSON>, Gambian sprinter", "html": "1990 - <a href=\"https://wikipedia.org/wiki/Suwaibo<PERSON>_<PERSON>\" title=\"Suwai<PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Gambian sprinter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Suwaibo<PERSON>_<PERSON>\" title=\"Suwai<PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Gambian sprinter", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Suwai<PERSON><PERSON>_<PERSON>h"}]}, {"year": "1991", "text": "<PERSON><PERSON><PERSON>, American basketball player", "html": "1991 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American basketball player", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1991", "text": "<PERSON><PERSON>, Russian ice hockey player", "html": "1991 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Russian ice hockey player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Russian ice hockey player", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Artemi_Panarin"}]}, {"year": "1991", "text": "<PERSON><PERSON><PERSON>, Czech basketball player", "html": "1991 - <a href=\"https://wikipedia.org/wiki/Tom%C3%A1%C5%A1_Satoransk%C3%BD\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Czech basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Tom%C3%A1%C5%A1_Satoransk%C3%BD\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Czech basketball player", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Tom%C3%A1%C5%A1_Satoransk%C3%BD"}]}, {"year": "1992", "text": "<PERSON>, Australian rugby league player", "html": "1992 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian rugby league player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian rugby league player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1992", "text": "<PERSON><PERSON>, Chilean tennis player", "html": "1992 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_(tennis)\" title=\"<PERSON><PERSON> (tennis)\"><PERSON><PERSON></a>, Chilean tennis player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_(tennis)\" title=\"<PERSON><PERSON> (tennis)\"><PERSON><PERSON></a>, Chilean tennis player", "links": [{"title": "<PERSON><PERSON> (tennis)", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_(tennis)"}]}, {"year": "1993", "text": "<PERSON>, American football player", "html": "1993 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1994", "text": "<PERSON>, Finnish tennis player", "html": "1994 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Finnish tennis player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Finnish tennis player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1996", "text": "<PERSON>, American basketball player", "html": "1996 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1996", "text": "<PERSON>, American ice hockey player", "html": "1996 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(ice_hockey)\" title=\"<PERSON> (ice hockey)\"><PERSON></a>, American ice hockey player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(ice_hockey)\" title=\"<PERSON> (ice hockey)\"><PERSON></a>, American ice hockey player", "links": [{"title": "<PERSON> (ice hockey)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(ice_hockey)"}]}, {"year": "1996", "text": "<PERSON>, American actress", "html": "1996 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1997", "text": "<PERSON><PERSON>, American ice hockey player", "html": "1997 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American ice hockey player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American ice hockey player", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1998", "text": "<PERSON>, Canadian ice hockey player", "html": "1998 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2000", "text": "<PERSON><PERSON>, Japanese singer", "html": "2000 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_(singer)\" title=\"Giselle (singer)\"><PERSON><PERSON></a>, Japanese singer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/G<PERSON>_(singer)\" title=\"Giselle (singer)\"><PERSON><PERSON></a>, Japanese singer", "links": [{"title": "<PERSON><PERSON> (singer)", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_(singer)"}]}], "Deaths": [{"year": "526", "text": "<PERSON> of Edessa, Syriac Orthodox bishop of Edessa", "html": "526 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON> of Edessa\"><PERSON> of <PERSON>essa</a>, <a href=\"https://wikipedia.org/wiki/Syriac_Orthodox\" class=\"mw-redirect\" title=\"Syriac Orthodox\">Syriac Orthodox</a> bishop of <a href=\"https://wikipedia.org/wiki/Edessa,_Mesopotamia\" class=\"mw-redirect\" title=\"Edessa, Mesopotamia\">Ed<PERSON><PERSON></a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON> of Edessa\"><PERSON> of <PERSON></a>, <a href=\"https://wikipedia.org/wiki/Syriac_Orthodox\" class=\"mw-redirect\" title=\"Syriac Orthodox\">Syriac Orthodox</a> bishop of <a href=\"https://wikipedia.org/wiki/Edess<PERSON>,_Mesopotamia\" class=\"mw-redirect\" title=\"Edessa, Mesopotamia\">Ed<PERSON><PERSON></a>", "links": [{"title": "<PERSON> of Edessa", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON><PERSON>"}, {"title": "Syriac Orthodox", "link": "https://wikipedia.org/wiki/Syriac_Orthodox"}, {"title": "Edessa, Mesopotamia", "link": "https://wikipedia.org/wiki/Edessa,_Mesopotamia"}]}, {"year": "1137", "text": "<PERSON><PERSON><PERSON>, Duke of Naples", "html": "1137 - <a href=\"https://wikipedia.org/wiki/Ser<PERSON><PERSON>_VII_of_Naples\" title=\"<PERSON>gi<PERSON> VII of Naples\">Ser<PERSON>us VII</a>, <a href=\"https://wikipedia.org/wiki/Duke_of_Naples\" title=\"Duke of Naples\">Duke of Naples</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_VII_of_Naples\" title=\"<PERSON>gi<PERSON> VII of Naples\"><PERSON><PERSON><PERSON> VII</a>, <a href=\"https://wikipedia.org/wiki/Duke_of_Naples\" title=\"Duke of Naples\">Duke of Naples</a>", "links": [{"title": "<PERSON><PERSON><PERSON> VII of Naples", "link": "https://wikipedia.org/wiki/Sergius_VII_of_Naples"}, {"title": "Duke of Naples", "link": "https://wikipedia.org/wiki/Duke_of_Naples"}]}, {"year": "1282", "text": "<PERSON>, Iraqi scholar and judge (b. 1211)", "html": "1282 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Iraqi scholar and judge (b. 1211)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Iraqi scholar and judge (b. 1211)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1459", "text": "<PERSON><PERSON>, Italian scholar and translator (b. 1380)", "html": "1459 - <a href=\"https://wikipedia.org/wiki/Po<PERSON>_Bracciolini\" title=\"Poggio Bracciolini\"><PERSON><PERSON> Braccio<PERSON></a>, Italian scholar and translator (b. 1380)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Po<PERSON>_Bracciolini\" title=\"Poggio Bracciolini\"><PERSON><PERSON></a>, Italian scholar and translator (b. 1380)", "links": [{"title": "Poggio Bracciolini", "link": "https://wikipedia.org/wiki/Po<PERSON>_<PERSON>lini"}]}, {"year": "1466", "text": "<PERSON>, German printer (b. c. 1400)", "html": "1466 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German printer (b. c. 1400)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German printer (b. c. 1400)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1522", "text": "<PERSON>, French composer and educator (b. 1459)", "html": "1522 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French composer and educator (b. 1459)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French composer and educator (b. 1459)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1553", "text": "<PERSON>, German politician (b. 1489)", "html": "1553 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German politician (b. 1489)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German politician (b. 1489)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1602", "text": "<PERSON><PERSON><PERSON>, French poet and illustrator (b. 1528)", "html": "1602 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, French poet and illustrator (b. 1528)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, French poet and illustrator (b. 1528)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>"}]}, {"year": "1611", "text": "<PERSON> of Sweden (b. 1550)", "html": "1611 - <a href=\"https://wikipedia.org/wiki/Charles_IX_of_Sweden\" title=\"Charles IX of Sweden\"><PERSON> of Sweden</a> (b. 1550)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Sweden\" title=\"<PERSON> IX of Sweden\"><PERSON> of Sweden</a> (b. 1550)", "links": [{"title": "<PERSON> of Sweden", "link": "https://wikipedia.org/wiki/Charles_IX_of_Sweden"}]}, {"year": "1626", "text": "<PERSON><PERSON><PERSON><PERSON>, Dutch astronomer and mathematician (b. 1580)", "html": "1626 - <a href=\"https://wikipedia.org/wiki/Will<PERSON><PERSON><PERSON>_<PERSON>nell\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON><PERSON> Snell\"><PERSON><PERSON><PERSON><PERSON></a>, Dutch astronomer and mathematician (b. 1580)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Will<PERSON><PERSON><PERSON>_<PERSON>nell\" class=\"mw-redirect\" title=\"Will<PERSON><PERSON><PERSON> Snell\"><PERSON><PERSON><PERSON><PERSON></a>, Dutch astronomer and mathematician (b. 1580)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1632", "text": "<PERSON>, French admiral and politician (b. 1595)", "html": "1632 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, French admiral and politician (b. 1595)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, French admiral and politician (b. 1595)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1654", "text": "Emperor <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> of Japan (b. 1633)", "html": "1654 - <a href=\"https://wikipedia.org/wiki/Emperor_<PERSON>-K%C5%8Dmy%C5%8D\" title=\"Emperor Go-Kōmyō\">Emperor <PERSON><PERSON>K<PERSON></a> of Japan (b. 1633)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Emperor_<PERSON>-K%C5%8Dmy%C5%8D\" title=\"Emperor Go-Kōmyō\">Emperor <PERSON><PERSON>Kōmy<PERSON></a> of Japan (b. 1633)", "links": [{"title": "Emperor <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Emperor_<PERSON>-K%C5%8Dmy%C5%8D"}]}, {"year": "1680", "text": "<PERSON><PERSON>, French-Flemish mystic (b. 1616)", "html": "1680 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, French-Flemish mystic (b. 1616)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, French-Flemish mystic (b. 1616)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1685", "text": "<PERSON>, French lawyer and politician, French Secretary of State for War (b. 1603)", "html": "1685 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French lawyer and politician, <a href=\"https://wikipedia.org/wiki/Minister_of_Defence_(France)\" class=\"mw-redirect\" title=\"Minister of Defence (France)\">French Secretary of State for War</a> (b. 1603)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French lawyer and politician, <a href=\"https://wikipedia.org/wiki/Minister_of_Defence_(France)\" class=\"mw-redirect\" title=\"Minister of Defence (France)\">French Secretary of State for War</a> (b. 1603)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Minister of Defence (France)", "link": "https://wikipedia.org/wiki/Minister_of_Defence_(France)"}]}, {"year": "1690", "text": "<PERSON><PERSON><PERSON><PERSON>, Dutch diplomat and politician (b. 1614)", "html": "1690 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Dutch diplomat and politician (b. 1614)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Dutch diplomat and politician (b. 1614)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1730", "text": "<PERSON><PERSON><PERSON>, Turkish poet (b. 1681)", "html": "1730 - <a href=\"https://wikipedia.org/wiki/Ned%C3%AEm\" title=\"Nedî<PERSON>\"><PERSON><PERSON><PERSON></a>, Turkish poet (b. 1681)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Ned%C3%AEm\" title=\"Nedî<PERSON>\"><PERSON><PERSON><PERSON></a>, Turkish poet (b. 1681)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Ned%C3%AEm"}]}, {"year": "1757", "text": "<PERSON><PERSON>, Ottoman sultan (b. 1699)", "html": "1757 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_III\" title=\"Osman III\"><PERSON><PERSON> <PERSON></a>, Ottoman sultan (b. 1699)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>sman_III\" title=\"Osman III\"><PERSON><PERSON> <PERSON></a>, Ottoman sultan (b. 1699)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_III"}]}, {"year": "1757", "text": "<PERSON>, English admiral and politician (b. 1684)", "html": "1757 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English admiral and politician (b. 1684)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English admiral and politician (b. 1684)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1809", "text": "<PERSON>, 3rd Duke of Portland, English politician, Prime Minister of the United Kingdom (b. 1738)", "html": "1809 - <a href=\"https://wikipedia.org/wiki/<PERSON>,_3rd_Duke_of_Portland\" title=\"<PERSON>, 3rd Duke of Portland\"><PERSON>, 3rd Duke of Portland</a>, English politician, <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_the_United_Kingdom\" title=\"Prime Minister of the United Kingdom\">Prime Minister of the United Kingdom</a> (b. 1738)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>,_3rd_Duke_of_Portland\" title=\"<PERSON>, 3rd Duke of Portland\"><PERSON>, 3rd Duke of Portland</a>, English politician, <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_the_United_Kingdom\" title=\"Prime Minister of the United Kingdom\">Prime Minister of the United Kingdom</a> (b. 1738)", "links": [{"title": "<PERSON>, 3rd Duke of Portland", "link": "https://wikipedia.org/wiki/<PERSON>,_3rd_Duke_of_Portland"}, {"title": "Prime Minister of the United Kingdom", "link": "https://wikipedia.org/wiki/Prime_Minister_of_the_United_Kingdom"}]}, {"year": "1816", "text": "<PERSON> of Württemberg (b. 1754)", "html": "1816 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_W%C3%BCrttemberg\" title=\"<PERSON> of Württemberg\"><PERSON> of Württemberg</a> (b. 1754)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_W%C3%BCrttemberg\" title=\"<PERSON> of Württemberg\"><PERSON> of Württemberg</a> (b. 1754)", "links": [{"title": "<PERSON> of Württemberg", "link": "https://wikipedia.org/wiki/<PERSON>_I_of_W%C3%BCrttemberg"}]}, {"year": "1842", "text": "<PERSON>, Scottish author and poet (b. 1784)", "html": "1842 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(author)\" title=\"<PERSON> (author)\"><PERSON></a>, Scottish author and poet (b. 1784)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(author)\" title=\"<PERSON> (author)\"><PERSON></a>, Scottish author and poet (b. 1784)", "links": [{"title": "<PERSON> (author)", "link": "https://wikipedia.org/wiki/<PERSON>_(author)"}]}, {"year": "1853", "text": "<PERSON>, Italian composer (b. 1786)", "html": "1853 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian composer (b. 1786)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian composer (b. 1786)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1882", "text": "<PERSON>, Indian-Australian politician, 4th Premier of New South Wales (b. 1818)", "html": "1882 - <a href=\"https://wikipedia.org/wiki/<PERSON>_(Australian_politician)\" title=\"<PERSON> (Australian politician)\"><PERSON></a>, Indian-Australian politician, 4th <a href=\"https://wikipedia.org/wiki/Premier_of_New_South_Wales\" title=\"Premier of New South Wales\">Premier of New South Wales</a> (b. 1818)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>(Australian_politician)\" title=\"<PERSON> (Australian politician)\"><PERSON></a>, Indian-Australian politician, 4th <a href=\"https://wikipedia.org/wiki/Premier_of_New_South_Wales\" title=\"Premier of New South Wales\">Premier of New South Wales</a> (b. 1818)", "links": [{"title": "<PERSON> (Australian politician)", "link": "https://wikipedia.org/wiki/<PERSON>(Australian_politician)"}, {"title": "Premier of New South Wales", "link": "https://wikipedia.org/wiki/Premier_of_New_South_Wales"}]}, {"year": "1883", "text": "<PERSON><PERSON><PERSON>, Indian philosopher and scholar (b. 1824)", "html": "1883 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Indian philosopher and scholar (b. 1824)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Indian philosopher and scholar (b. 1824)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1883", "text": "<PERSON>, German pianist and composer (b. 1815)", "html": "1883 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German pianist and composer (b. 1815)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German pianist and composer (b. 1815)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1893", "text": "<PERSON>, Canadian lawyer and politician, 3rd Prime Minister of Canada (b. 1821)", "html": "1893 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian lawyer and politician, 3rd <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Canada\" title=\"Prime Minister of Canada\">Prime Minister of Canada</a> (b. 1821)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian lawyer and politician, 3rd <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Canada\" title=\"Prime Minister of Canada\">Prime Minister of Canada</a> (b. 1821)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Prime Minister of Canada", "link": "https://wikipedia.org/wiki/Prime_Minister_of_Canada"}]}, {"year": "1894", "text": "<PERSON><PERSON>, Canadian lawyer and politician, 9th Premier of Quebec (b. 1840)", "html": "1894 - <a href=\"https://wikipedia.org/wiki/Honor%C3%A9_Me<PERSON>ier\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Canadian lawyer and politician, 9th <a href=\"https://wikipedia.org/wiki/Premier_of_Quebec\" title=\"Premier of Quebec\">Premier of Quebec</a> (b. 1840)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Honor%C3%A9_Me<PERSON>ier\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Canadian lawyer and politician, 9th <a href=\"https://wikipedia.org/wiki/Premier_of_Quebec\" title=\"Premier of Quebec\">Premier of Quebec</a> (b. 1840)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Honor%C3%A9_Me<PERSON>ier"}, {"title": "Premier of Quebec", "link": "https://wikipedia.org/wiki/Premier_of_Quebec"}]}, {"year": "1895", "text": "<PERSON>, English-Australian politician, 17th Premier of Victoria (b. 1833)", "html": "1895 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(Australian_politician)\" title=\"<PERSON> (Australian politician)\"><PERSON></a>, English-Australian politician, 17th <a href=\"https://wikipedia.org/wiki/Premier_of_Victoria\" title=\"Premier of Victoria\">Premier of Victoria</a> (b. 1833)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(Australian_politician)\" title=\"<PERSON> (Australian politician)\"><PERSON></a>, English-Australian politician, 17th <a href=\"https://wikipedia.org/wiki/Premier_of_Victoria\" title=\"Premier of Victoria\">Premier of Victoria</a> (b. 1833)", "links": [{"title": "<PERSON> (Australian politician)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(Australian_politician)"}, {"title": "Premier of Victoria", "link": "https://wikipedia.org/wiki/Premier_of_Victoria"}]}, {"year": "1896", "text": "<PERSON>, Czech architect, designed Peleș Castle (b. 1822)", "html": "1896 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Czech architect, designed <a href=\"https://wikipedia.org/wiki/Pele%C8%99_Castle\" title=\"Peleș Castle\">Peleș Castle</a> (b. 1822)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Czech architect, designed <a href=\"https://wikipedia.org/wiki/Pele%C8%99_Castle\" title=\"Peleș Castle\">Peleș Castle</a> (b. 1822)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Peleș Castle", "link": "https://wikipedia.org/wiki/Pele%C8%99_Castle"}]}, {"year": "1899", "text": "<PERSON>, American shipbuilder and philanthropist (b. 1816)", "html": "1899 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American shipbuilder and philanthropist (b. 1816)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American shipbuilder and philanthropist (b. 1816)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1905", "text": "<PERSON>, Australian politician, 10th Premier of Queensland (b. 1843)", "html": "1905 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian politician, 10th <a href=\"https://wikipedia.org/wiki/Premier_of_Queensland\" title=\"Premier of Queensland\">Premier of Queensland</a> (b. 1843)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian politician, 10th <a href=\"https://wikipedia.org/wiki/Premier_of_Queensland\" title=\"Premier of Queensland\">Premier of Queensland</a> (b. 1843)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "Premier of Queensland", "link": "https://wikipedia.org/wiki/Premier_of_Queensland"}]}, {"year": "1910", "text": "<PERSON>, Swiss activist, founded the Red Cross, Nobel Prize laureate (b. 1828)", "html": "1910 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swiss activist, founded the <a href=\"https://wikipedia.org/wiki/International_Red_Cross_and_Red_Crescent_Movement\" title=\"International Red Cross and Red Crescent Movement\">Red Cross</a>, <a href=\"https://wikipedia.org/wiki/Nobel_Peace_Prize\" title=\"Nobel Peace Prize\">Nobel Prize</a> laureate (b. 1828)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swiss activist, founded the <a href=\"https://wikipedia.org/wiki/International_Red_Cross_and_Red_Crescent_Movement\" title=\"International Red Cross and Red Crescent Movement\">Red Cross</a>, <a href=\"https://wikipedia.org/wiki/Nobel_Peace_Prize\" title=\"Nobel Peace Prize\">Nobel Prize</a> laureate (b. 1828)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "International Red Cross and Red Crescent Movement", "link": "https://wikipedia.org/wiki/International_Red_Cross_and_Red_Crescent_Movement"}, {"title": "Nobel Peace Prize", "link": "https://wikipedia.org/wiki/Nobel_Peace_Prize"}]}, {"year": "1912", "text": "<PERSON>, Chilean colonel (b. 1840)", "html": "1912 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Chilean colonel (b. 1840)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Chilean colonel (b. 1840)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1912", "text": "<PERSON>, American lawyer and politician, 27th Vice President of the United States (b. 1855)", "html": "1912 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and politician, 27th <a href=\"https://wikipedia.org/wiki/Vice_President_of_the_United_States\" title=\"Vice President of the United States\">Vice President of the United States</a> (b. 1855)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and politician, 27th <a href=\"https://wikipedia.org/wiki/Vice_President_of_the_United_States\" title=\"Vice President of the United States\">Vice President of the United States</a> (b. 1855)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "Vice President of the United States", "link": "https://wikipedia.org/wiki/Vice_President_of_the_United_States"}]}, {"year": "1915", "text": "<PERSON>, Canadian physician, lawyer, and politician, 6th Prime Minister of Canada (b. 1821)", "html": "1915 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian physician, lawyer, and politician, 6th <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Canada\" title=\"Prime Minister of Canada\">Prime Minister of Canada</a> (b. 1821)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian physician, lawyer, and politician, 6th <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Canada\" title=\"Prime Minister of Canada\">Prime Minister of Canada</a> (b. 1821)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Prime Minister of Canada", "link": "https://wikipedia.org/wiki/Prime_Minister_of_Canada"}]}, {"year": "1917", "text": "<PERSON>, Canadian lawyer and soldier (b. 1883)", "html": "1917 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian lawyer and soldier (b. 1883)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian lawyer and soldier (b. 1883)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1919", "text": "<PERSON>, American author and poet (b. 1850)", "html": "1919 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author and poet (b. 1850)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author and poet (b. 1850)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1923", "text": "<PERSON><PERSON>, Canadian-English banker and politician, Prime Minister of the United Kingdom (b. 1858)", "html": "1923 - <a href=\"https://wikipedia.org/wiki/Bonar_Law\" title=\"Bonar Law\"><PERSON><PERSON></a>, Canadian-English banker and politician, <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_the_United_Kingdom\" title=\"Prime Minister of the United Kingdom\">Prime Minister of the United Kingdom</a> (b. 1858)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Bonar_Law\" title=\"Bonar Law\"><PERSON><PERSON></a>, Canadian-English banker and politician, <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_the_United_Kingdom\" title=\"Prime Minister of the United Kingdom\">Prime Minister of the United Kingdom</a> (b. 1858)", "links": [{"title": "Bonar Law", "link": "https://wikipedia.org/wiki/Bonar_Law"}, {"title": "Prime Minister of the United Kingdom", "link": "https://wikipedia.org/wiki/Prime_Minister_of_the_United_Kingdom"}]}, {"year": "1929", "text": "<PERSON>, Indian-English hurdler and actor (b. 1877)", "html": "1929 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Indian-English hurdler and actor (b. 1877)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Indian-English hurdler and actor (b. 1877)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1933", "text": "<PERSON><PERSON>, Danish actor (b. 1869)", "html": "1933 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Danish actor (b. 1869)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Danish actor (b. 1869)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1942", "text": "<PERSON>, English polo player and stockbroker, co-founder of Buckmaster & Moore (b. 1872)", "html": "1942 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English polo player and stockbroker, co-founder of <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_%26_<PERSON>\" title=\"<PERSON><PERSON> &amp; <PERSON>\"><PERSON><PERSON> &amp; <PERSON></a> (b. 1872)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English polo player and stockbroker, co-founder of <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_%26_<PERSON>\" title=\"<PERSON><PERSON> &amp; <PERSON>\"><PERSON><PERSON> &amp; <PERSON></a> (b. 1872)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Buckmaster & Moore", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_%26_<PERSON>"}]}, {"year": "1943", "text": "<PERSON>, Austrian-born American actor and director (b. 1873)", "html": "1943 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Austrian-born American actor and director (b. 1873)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Austrian-born American actor and director (b. 1873)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1957", "text": "<PERSON>, American baseball player and coach (b. 1880)", "html": "1957 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player and coach (b. 1880)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player and coach (b. 1880)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1961", "text": "<PERSON>, Italian economist and politician, 2nd President of the Italian Republic (b. 1874)", "html": "1961 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian economist and politician, 2nd <a href=\"https://wikipedia.org/wiki/President_of_the_Italian_Republic\" class=\"mw-redirect\" title=\"President of the Italian Republic\">President of the Italian Republic</a> (b. 1874)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian economist and politician, 2nd <a href=\"https://wikipedia.org/wiki/President_of_the_Italian_Republic\" class=\"mw-redirect\" title=\"President of the Italian Republic\">President of the Italian Republic</a> (b. 1874)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "President of the Italian Republic", "link": "https://wikipedia.org/wiki/President_of_the_Italian_Republic"}]}, {"year": "1963", "text": "<PERSON><PERSON><PERSON><PERSON><PERSON>, Indian lawyer and politician (b. 1908)", "html": "1963 - <a href=\"https://wikipedia.org/wiki/U._Muthuramalingam_Thevar\" class=\"mw-redirect\" title=\"U. Muthuramalingam Thevar\"><PERSON><PERSON> <PERSON></a>, Indian lawyer and politician (b. 1908)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/U._Muthuramalingam_Thevar\" class=\"mw-redirect\" title=\"U. Muthuramalingam Thevar\"><PERSON><PERSON> <PERSON></a>, Indian lawyer and politician (b. 1908)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/U._Mu<PERSON><PERSON><PERSON><PERSON><PERSON>_<PERSON>var"}]}, {"year": "1965", "text": "<PERSON>, Sr., American historian and author (b. 1888)", "html": "1965 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>,_Sr.\" class=\"mw-redirect\" title=\"<PERSON>, Sr.\"><PERSON>, Sr.</a>, American historian and author (b. 1888)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>,_Sr.\" class=\"mw-redirect\" title=\"<PERSON>, Sr.\"><PERSON>, Sr.</a>, American historian and author (b. 1888)", "links": [{"title": "<PERSON>, Sr.", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>,_<PERSON>."}]}, {"year": "1966", "text": "<PERSON><PERSON><PERSON>, Greek author and playwright (b. 1906)", "html": "1966 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Greek author and playwright (b. 1906)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Greek author and playwright (b. 1906)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>as"}]}, {"year": "1968", "text": "<PERSON>, Mexican-American actor, singer, and director (b. 1899)", "html": "1968 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Mexican-American actor, singer, and director (b. 1899)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Mexican-American actor, singer, and director (b. 1899)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1968", "text": "<PERSON>, American journalist and novelist (b. 1890)", "html": "1968 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American journalist and novelist (b. 1890)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American journalist and novelist (b. 1890)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1968", "text": "<PERSON>, American journalist and author (b. 1886)", "html": "1968 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"Rose Wilder Lane\"><PERSON></a>, American journalist and author (b. 1886)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"Rose Wilder Lane\"><PERSON></a>, American journalist and author (b. 1886)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1973", "text": "<PERSON><PERSON>, Estonian actor and director (b. 1894)", "html": "1973 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Estonian actor and director (b. 1894)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Estonian actor and director (b. 1894)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>"}]}, {"year": "1975", "text": "<PERSON>, German physicist and academic, Nobel Prize laureate (b. 1887)", "html": "1975 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German physicist and academic, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Physics\" title=\"Nobel Prize in Physics\">Nobel Prize</a> laureate (b. 1887)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German physicist and academic, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Physics\" title=\"Nobel Prize in Physics\">Nobel Prize</a> laureate (b. 1887)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Nobel Prize in Physics", "link": "https://wikipedia.org/wiki/Nobel_Prize_in_Physics"}]}, {"year": "1979", "text": "<PERSON>, English scientist and engineer, inventor of the \"bouncing bomb\" (b. 1887)", "html": "1979 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English scientist and engineer, inventor of the \"<a href=\"https://wikipedia.org/wiki/Bouncing_bomb\" title=\"Bouncing bomb\">bouncing bomb</a>\" (b. 1887)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English scientist and engineer, inventor of the \"<a href=\"https://wikipedia.org/wiki/Bouncing_bomb\" title=\"Bouncing bomb\">bouncing bomb</a>\" (b. 1887)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Bouncing bomb", "link": "https://wikipedia.org/wiki/Bouncing_bomb"}]}, {"year": "1982", "text": "<PERSON><PERSON><PERSON>, Ukrainian author and educator (b. 1907)", "html": "1982 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Ukrainian author and educator (b. 1907)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Ukrainian author and educator (b. 1907)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1987", "text": "<PERSON>, American mythologist, scholar, and author (b. 1904)", "html": "1987 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American mythologist, scholar, and author (b. 1904)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American mythologist, scholar, and author (b. 1904)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1988", "text": "<PERSON><PERSON>, American animator and screenwriter (b. 1911)", "html": "1988 - <a href=\"https://wikipedia.org/wiki/T._<PERSON><PERSON>\" title=\"T. Hee\"><PERSON><PERSON> <PERSON></a>, American animator and screenwriter (b. 1911)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/T._<PERSON><PERSON>\" title=\"T. Hee\"><PERSON><PERSON> <PERSON><PERSON></a>, American animator and screenwriter (b. 1911)", "links": [{"title": "<PERSON><PERSON> <PERSON>e", "link": "https://wikipedia.org/wiki/T._<PERSON>e"}]}, {"year": "1988", "text": "<PERSON>, English trainer and breeder of racehorses (b. 1894)", "html": "1988 - <a href=\"https://wikipedia.org/wiki/Florence_Nagle\" title=\"Florence Nagle\"><PERSON></a>, English trainer and breeder of <a href=\"https://wikipedia.org/wiki/Horse_racing\" title=\"Horse racing\">racehorses</a> (b. 1894)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Florence_Nagle\" title=\"Florence Nagle\"><PERSON></a>, English trainer and breeder of <a href=\"https://wikipedia.org/wiki/Horse_racing\" title=\"Horse racing\">racehorses</a> (b. 1894)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Florence_Nagle"}, {"title": "Horse racing", "link": "https://wikipedia.org/wiki/Horse_racing"}]}, {"year": "1990", "text": "<PERSON><PERSON>, Indian actor, director, and producer (b. 1901)", "html": "1990 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Indian actor, director, and producer (b. 1901)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Indian actor, director, and producer (b. 1901)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1992", "text": "<PERSON>, American painter (b. 1925)", "html": "1992 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American painter (b. 1925)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American painter (b. 1925)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1993", "text": "<PERSON>, Canadian cardinal (b. 1911)", "html": "1993 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A9go<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian cardinal (b. 1911)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A9go<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian cardinal (b. 1911)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Paul_Gr%C3%A9goire"}]}, {"year": "1996", "text": "<PERSON>, Scottish actor (b. 1916)", "html": "1996 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(actor)\" title=\"<PERSON> (actor)\"><PERSON></a>, Scottish actor (b. 1916)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(actor)\" title=\"<PERSON> (actor)\"><PERSON></a>, Scottish actor (b. 1916)", "links": [{"title": "<PERSON> (actor)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>(actor)"}]}, {"year": "1997", "text": "<PERSON>, American actor, director, producer, and screenwriter (b. 1912)", "html": "1997 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor, director, producer, and screenwriter (b. 1912)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor, director, producer, and screenwriter (b. 1912)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1999", "text": "<PERSON><PERSON><PERSON>, Latvian basketball player (b. 1933)", "html": "1999 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Latvian basketball player (b. 1933)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Latvian basketball player (b. 1933)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "2000", "text": "<PERSON>, American actor, television personality, game show panelist, and talk show host (b. 1921)", "html": "2000 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor, television personality, game show panelist, and talk show host (b. 1921)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor, television personality, game show panelist, and talk show host (b. 1921)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2002", "text": "<PERSON>, Spanish actor, director, and screenwriter (b. 1922)", "html": "2002 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Spanish actor, director, and screenwriter (b. 1922)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Spanish actor, director, and screenwriter (b. 1922)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2002", "text": "<PERSON>, American rapper and producer (b. 1965)", "html": "2002 - <a href=\"https://wikipedia.org/wiki/Jam_Master_Jay\" title=\"Jam Master Jay\"><PERSON> Master <PERSON></a>, American rapper and producer (b. 1965)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Jam_Master_Jay\" title=\"Jam Master Jay\"><PERSON> Master <PERSON></a>, American rapper and producer (b. 1965)", "links": [{"title": "Jam Master <PERSON>", "link": "https://wikipedia.org/wiki/Jam_Master_<PERSON>"}]}, {"year": "2003", "text": "<PERSON>, English race car driver and manager (b. 1940)", "html": "2003 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%27Rourke\" title=\"<PERSON>\"><PERSON></a>, English race car driver and manager (b. 1940)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%27Rourke\" title=\"<PERSON>\"><PERSON></a>, English race car driver and manager (b. 1940)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>%27Rourke"}]}, {"year": "2004", "text": "<PERSON>, Australian philanthropist, founded Keep Australia Beautiful (b. 1917)", "html": "2004 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian philanthropist, founded Keep Australia Beautiful (b. 1917)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian philanthropist, founded Keep Australia Beautiful (b. 1917)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2004", "text": "<PERSON>, American actress and dancer (b. 1924)", "html": "2004 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress and dancer (b. 1924)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress and dancer (b. 1924)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2005", "text": "<PERSON>, American baseball player and manager (b. 1908)", "html": "2005 - <a href=\"https://wikipedia.org/wiki/Al_L%C3%B3pez\" title=\"<PERSON>\"><PERSON></a>, American baseball player and manager (b. 1908)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Al_L%C3%B3pez\" title=\"<PERSON>\"><PERSON></a>, American baseball player and manager (b. 1908)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Al_L%C3%B3pez"}]}, {"year": "2005", "text": "<PERSON><PERSON><PERSON>, Indian politician (b. 1942)", "html": "2005 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Indian politician (b. 1942)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Indian politician (b. 1942)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>"}]}, {"year": "2006", "text": "<PERSON>, American anthropologist and author (b. 1926)", "html": "2006 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American anthropologist and author (b. 1926)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American anthropologist and author (b. 1926)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2006", "text": "<PERSON><PERSON>, Japanese playwright and scholar (b. 1914)", "html": "2006 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Japanese playwright and scholar (b. 1914)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Japanese playwright and scholar (b. 1914)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>"}]}, {"year": "2007", "text": "<PERSON><PERSON>, American chimpanzee (b. 1965)", "html": "2007 - <a href=\"https://wikipedia.org/wiki/Washoe_(chimpanzee)\" title=\"Washoe (chimpanzee)\"><PERSON><PERSON></a>, American chimpanzee (b. 1965)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Washoe_(chimpanzee)\" title=\"Washoe (chimpanzee)\"><PERSON><PERSON></a>, American chimpanzee (b. 1965)", "links": [{"title": "Was<PERSON> (chimpanzee)", "link": "https://wikipedia.org/wiki/<PERSON>hoe_(chimpanzee)"}]}, {"year": "2007", "text": "<PERSON>, American actor and singer (b. 1933)", "html": "2007 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor and singer (b. 1933)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor and singer (b. 1933)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2007", "text": "<PERSON>, American businesswoman and manager (b. 1945)", "html": "2007 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American businesswoman and manager (b. 1945)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American businesswoman and manager (b. 1945)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "2007", "text": "<PERSON>, American runner and colonel (b. 1915)", "html": "2007 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(athlete)\" class=\"mw-redirect\" title=\"<PERSON> (athlete)\"><PERSON></a>, American runner and colonel (b. 1915)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(athlete)\" class=\"mw-redirect\" title=\"<PERSON> (athlete)\"><PERSON></a>, American runner and colonel (b. 1915)", "links": [{"title": "<PERSON> (athlete)", "link": "https://wikipedia.org/wiki/<PERSON>_(athlete)"}]}, {"year": "2008", "text": "<PERSON>, Argentinian businessman (b. 1950)", "html": "2008 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Argentinian businessman (b. 1950)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Argentinian businessman (b. 1950)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2009", "text": "<PERSON><PERSON>, French anthropologist and ethnologist (b. 1908)", "html": "2009 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A9<PERSON>-<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French anthropologist and ethnologist (b. 1908)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A9<PERSON>-<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French anthropologist and ethnologist (b. 1908)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_L%C3%A9vi-<PERSON>"}]}, {"year": "2010", "text": "<PERSON>, Dutch author, poet, and playwright (b. 1927)", "html": "2010 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch author, poet, and playwright (b. 1927)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch author, poet, and playwright (b. 1927)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2012", "text": "<PERSON><PERSON><PERSON>, French politician (b. 1961)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/<PERSON>an<PERSON>_B<PERSON>cheri\" title=\"Franck Biancheri\"><PERSON><PERSON><PERSON></a>, French politician (b. 1961)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>i\" title=\"Franck Biancheri\"><PERSON><PERSON><PERSON></a>, French politician (b. 1961)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>an<PERSON>_<PERSON>i"}]}, {"year": "2012", "text": "<PERSON><PERSON>, Pakistani poet and educator (b. 1961)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Pakistani poet and educator (b. 1961)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Pakistani poet and educator (b. 1961)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "2012", "text": "<PERSON>, American basketball player and coach (b. 1940)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player and coach (b. 1940)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player and coach (b. 1940)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2013", "text": "<PERSON>, American baseball player (b. 1928)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(baseball)\" title=\"<PERSON> (baseball)\"><PERSON></a>, American baseball player (b. 1928)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(baseball)\" title=\"<PERSON> (baseball)\"><PERSON></a>, American baseball player (b. 1928)", "links": [{"title": "<PERSON> (baseball)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(baseball)"}]}, {"year": "2013", "text": "<PERSON>, English singer-songwriter and guitarist (b. 1951)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English singer-songwriter and guitarist (b. 1951)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English singer-songwriter and guitarist (b. 1951)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2013", "text": "<PERSON>, American physician and author (b. 1942)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(novelist)\" title=\"<PERSON> (novelist)\"><PERSON></a>, American physician and author (b. 1942)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>(novelist)\" title=\"<PERSON> (novelist)\"><PERSON></a>, American physician and author (b. 1942)", "links": [{"title": "<PERSON> (novelist)", "link": "https://wikipedia.org/wiki/<PERSON>(novelist)"}]}, {"year": "2013", "text": "<PERSON>, American saxophonist and flute player (b. 1922)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American saxophonist and flute player (b. 1922)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American saxophonist and flute player (b. 1922)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2014", "text": "<PERSON>, Sudanese general and politician (b. 1937)", "html": "2014 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Sudanese general and politician (b. 1937)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Sudanese general and politician (b. 1937)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "2014", "text": "<PERSON><PERSON>, English actress (b. 1915)", "html": "2014 - <a href=\"https://wikipedia.org/wiki/Ren%C3%A9<PERSON>_<PERSON><PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, English actress (b. 1915)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Ren%C3%A9<PERSON>_<PERSON><PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, English actress (b. 1915)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Ren%C3%A9e_<PERSON><PERSON>"}]}, {"year": "2014", "text": "<PERSON>, Filipino physician and politician (b. 1935)", "html": "2014 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Filipino physician and politician (b. 1935)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Filipino physician and politician (b. 1935)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2014", "text": "<PERSON>, Australian actress and radio host (b. 1916)", "html": "2014 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian actress and radio host (b. 1916)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian actress and radio host (b. 1916)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "2014", "text": "<PERSON>, American wrestler and promoter (b. 1924)", "html": "2014 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American wrestler and promoter (b. 1924)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American wrestler and promoter (b. 1924)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2014", "text": "<PERSON>, American businessman and politician, 53rd Mayor of Boston (b. 1942)", "html": "2014 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American businessman and politician, 53rd <a href=\"https://wikipedia.org/wiki/Mayor_of_Boston\" title=\"Mayor of Boston\">Mayor of Boston</a> (b. 1942)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American businessman and politician, 53rd <a href=\"https://wikipedia.org/wiki/Mayor_of_Boston\" title=\"Mayor of Boston\">Mayor of Boston</a> (b. 1942)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Mayor of Boston", "link": "https://wikipedia.org/wiki/Mayor_of_Boston"}]}, {"year": "2015", "text": "<PERSON>, American basketball player and coach (b. 1944)", "html": "2015 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player and coach (b. 1944)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player and coach (b. 1944)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2015", "text": "<PERSON>, American actor (b. 1919)", "html": "2015 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor (b. 1919)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor (b. 1919)", "links": [{"title": "Al <PERSON>ro", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>ro"}]}, {"year": "2015", "text": "<PERSON><PERSON>, Turkish boxer (b. 1974)", "html": "2015 - <a href=\"https://wikipedia.org/wiki/Sinan_%C5%9Eamil_Sam\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Turkish boxer (b. 1974)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Sin<PERSON>_%C5%9Eamil_Sam\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Turkish boxer (b. 1974)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Sinan_%C5%9Eamil_Sam"}]}, {"year": "2015", "text": "<PERSON><PERSON>, American baseball player and scout (b. 1933)", "html": "2015 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"Nor<PERSON> Sieb<PERSON>\"><PERSON><PERSON></a>, American baseball player and scout (b. 1933)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> Si<PERSON>\"><PERSON><PERSON></a>, American baseball player and scout (b. 1933)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "2017", "text": "<PERSON>, South Korean actor (b. 1972)", "html": "2017 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>yu<PERSON>\" title=\"<PERSON>\"><PERSON></a>, South Korean actor (b. 1972)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>yu<PERSON>\" title=\"<PERSON>yu<PERSON>\"><PERSON></a>, South Korean actor (b. 1972)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>yuk"}]}, {"year": "2024", "text": "<PERSON>, Australian journalist and author (b. 1952)", "html": "2024 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian journalist and author (b. 1952)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian journalist and author (b. 1952)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}]}}