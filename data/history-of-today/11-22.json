{"date": "November 22", "url": "https://wikipedia.org/wiki/November_22", "data": {"Events": [{"year": "498", "text": "After the death of <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> is elected Pope in the Lateran Palace, while <PERSON><PERSON> is elected Pope in Santa Maria Maggiore.", "html": "498 - After the death of <a href=\"https://wikipedia.org/wiki/Pope_Anastasius_II\" title=\"Pope Anastasius II\"><PERSON><PERSON><PERSON> II</a>, <a href=\"https://wikipedia.org/wiki/Pope_Symmachus\" title=\"Pope Symmachus\"><PERSON><PERSON><PERSON><PERSON></a> is elected Pope in the <a href=\"https://wikipedia.org/wiki/Lateran_Palace\" title=\"Lateran Palace\">Lateran Palace</a>, while <a href=\"https://wikipedia.org/wiki/Antipope_Laurentius\" title=\"Antipop<PERSON>\"><PERSON><PERSON></a> is elected Pope in <a href=\"https://wikipedia.org/wiki/Basilica_di_Santa_Maria_Maggiore\" class=\"mw-redirect\" title=\"Basilica di Santa Maria Maggiore\">Santa Maria Maggiore</a>.", "no_year_html": "After the death of <a href=\"https://wikipedia.org/wiki/Pope_Anastasius_II\" title=\"Pope Anastasius II\"><PERSON><PERSON><PERSON> II</a>, <a href=\"https://wikipedia.org/wiki/Pope_Symmachus\" title=\"Pope Symmachus\"><PERSON><PERSON><PERSON><PERSON></a> is elected Pope in the <a href=\"https://wikipedia.org/wiki/Lateran_Palace\" title=\"Lateran Palace\">Lateran Palace</a>, while <a href=\"https://wikipedia.org/wiki/Antipope_Laurentius\" title=\"Antipop<PERSON> Laurent<PERSON>\"><PERSON><PERSON></a> is elected Pope in <a href=\"https://wikipedia.org/wiki/Basilica_di_Santa_Maria_Maggiore\" class=\"mw-redirect\" title=\"Basilica di Santa Maria Maggiore\">Santa Maria Maggiore</a>.", "links": [{"title": "<PERSON> <PERSON><PERSON> II", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_II"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Pope_Symmachus"}, {"title": "Lateran Palace", "link": "https://wikipedia.org/wiki/Lateran_Palace"}, {"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Anti<PERSON><PERSON>_<PERSON>"}, {"title": "Basilica di Santa Maria Maggiore", "link": "https://wikipedia.org/wiki/Basilica_di_Santa_Maria_Maggiore"}]}, {"year": "845", "text": "The first duke of Brittany, <PERSON><PERSON><PERSON>, defeats the Frankish king <PERSON> at the Battle of Ballon near Redon.", "html": "845 - The first duke of <a href=\"https://wikipedia.org/wiki/Brittany\" title=\"Brittany\">Brittany</a>, <a href=\"https://wikipedia.org/wiki/Nominoe\" title=\"Nominoe\"><PERSON>min<PERSON></a>, defeats the Frankish king <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_Ba<PERSON>\" title=\"<PERSON> the Bald\"><PERSON> the Ba<PERSON></a> at the <a href=\"https://wikipedia.org/wiki/Battle_of_Ballon\" title=\"Battle of Ballon\">Battle of Ballon</a> near <a href=\"https://wikipedia.org/wiki/Redon,_Ille-et-Vilaine\" title=\"Redon, Ille-et-Vilaine\">Redon</a>.", "no_year_html": "The first duke of <a href=\"https://wikipedia.org/wiki/Brittany\" title=\"Brittany\">Brittany</a>, <a href=\"https://wikipedia.org/wiki/Nominoe\" title=\"Nominoe\"><PERSON><PERSON><PERSON></a>, defeats the Frankish king <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_Ba<PERSON>\" title=\"<PERSON> the Bald\"><PERSON> the Bald</a> at the <a href=\"https://wikipedia.org/wiki/Battle_of_Ballon\" title=\"Battle of Ballon\">Battle of Ballon</a> near <a href=\"https://wikipedia.org/wiki/Redon,_Ille-et-Vilaine\" title=\"Redon, Ille-et-Vilaine\">Red<PERSON></a>.", "links": [{"title": "Brittany", "link": "https://wikipedia.org/wiki/Brittany"}, {"title": "Nominoe", "link": "https://wikipedia.org/wiki/Nominoe"}, {"title": "<PERSON> the <PERSON>ld", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "Battle of Ballon", "link": "https://wikipedia.org/wiki/Battle_of_Ballon"}, {"title": "Redon, Ille-et-Vilaine", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>,_Ille-et-Vilaine"}]}, {"year": "1307", "text": "<PERSON> <PERSON> issues the papal bull Pastoralis Praeeminentiae which instructed all Christian monarchs in Europe to arrest all Templars and seize their assets.", "html": "1307 - <a href=\"https://wikipedia.org/wiki/Pope_Clement_V\" title=\"Pope Clement V\">Pope <PERSON></a> issues the papal bull <i><a href=\"https://wikipedia.org/wiki/Pastoralis_Praeeminentiae\" class=\"mw-redirect\" title=\"Pastoralis Praeeminentiae\">Pastoralis Praeeminentiae</a></i> which instructed all Christian monarchs in Europe to arrest all <a href=\"https://wikipedia.org/wiki/Knights_Templar\" title=\"Knights Templar\">Templars</a> and seize their assets.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_Clement_V\" title=\"Pope Clement V\">Pope <PERSON> V</a> issues the papal bull <i><a href=\"https://wikipedia.org/wiki/Pastoralis_Praeeminentiae\" class=\"mw-redirect\" title=\"Pastoralis Praeeminentiae\">Pastoralis Praeeminentiae</a></i> which instructed all Christian monarchs in Europe to arrest all <a href=\"https://wikipedia.org/wiki/Knights_Templar\" title=\"Knights Templar\">Templars</a> and seize their assets.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "Pastoralis Praeeminentiae", "link": "https://wikipedia.org/wiki/Pastoralis_Praeeminentiae"}, {"title": "Knights Templar", "link": "https://wikipedia.org/wiki/Knights_Templar"}]}, {"year": "1574", "text": "Spanish navigator <PERSON> discovers islands now known as the Juan Fernández Islands off Chile.", "html": "1574 - Spanish navigator <a href=\"https://wikipedia.org/wiki/<PERSON>_Fe<PERSON>%C3%A1<PERSON><PERSON>_(explorer)\" title=\"<PERSON> (explorer)\"><PERSON></a> discovers islands now known as the <a href=\"https://wikipedia.org/wiki/Juan_Fern%C3%A1ndez_Islands\" title=\"Juan Fernández Islands\">Juan Fernández Islands</a> off <a href=\"https://wikipedia.org/wiki/Chile\" title=\"Chile\">Chile</a>.", "no_year_html": "Spanish navigator <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A1<PERSON><PERSON>_(explorer)\" title=\"<PERSON> (explorer)\"><PERSON></a> discovers islands now known as the <a href=\"https://wikipedia.org/wiki/Juan_Fern%C3%A1ndez_Islands\" title=\"Juan Fernández Islands\">Juan Fernández Islands</a> off <a href=\"https://wikipedia.org/wiki/Chile\" title=\"Chile\">Chile</a>.", "links": [{"title": "<PERSON> (explorer)", "link": "https://wikipedia.org/wiki/<PERSON>_Fe<PERSON>%C3%A1<PERSON><PERSON>_(explorer)"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Juan_Fern%C3%A1ndez_Islands"}, {"title": "Chile", "link": "https://wikipedia.org/wiki/Chile"}]}, {"year": "1635", "text": "Dutch colonial forces on Taiwan launch a pacification campaign against native villages, resulting in Dutch control of the middle and south of the island.", "html": "1635 - <a href=\"https://wikipedia.org/wiki/Dutch_Formosa\" title=\"Dutch Formosa\">Dutch colonial forces</a> on <a href=\"https://wikipedia.org/wiki/Taiwan\" title=\"Taiwan\">Taiwan</a> launch a <a href=\"https://wikipedia.org/wiki/Dutch_pacification_campaign_on_Formosa\" title=\"Dutch pacification campaign on Formosa\">pacification campaign</a> against <a href=\"https://wikipedia.org/wiki/Taiwanese_aborigines\" class=\"mw-redirect\" title=\"Taiwanese aborigines\">native villages</a>, resulting in Dutch control of the middle and south of the island.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Dutch_Formosa\" title=\"Dutch Formosa\">Dutch colonial forces</a> on <a href=\"https://wikipedia.org/wiki/Taiwan\" title=\"Taiwan\">Taiwan</a> launch a <a href=\"https://wikipedia.org/wiki/Dutch_pacification_campaign_on_Formosa\" title=\"Dutch pacification campaign on Formosa\">pacification campaign</a> against <a href=\"https://wikipedia.org/wiki/Taiwanese_aborigines\" class=\"mw-redirect\" title=\"Taiwanese aborigines\">native villages</a>, resulting in Dutch control of the middle and south of the island.", "links": [{"title": "Dutch Formosa", "link": "https://wikipedia.org/wiki/Dutch_Formosa"}, {"title": "Taiwan", "link": "https://wikipedia.org/wiki/Taiwan"}, {"title": "Dutch pacification campaign on Formosa", "link": "https://wikipedia.org/wiki/Dutch_pacification_campaign_on_Formosa"}, {"title": "Taiwanese aborigines", "link": "https://wikipedia.org/wiki/Taiwanese_aborigines"}]}, {"year": "1718", "text": "Royal Navy Lieutenant <PERSON> attacks and boards the vessels of the British pirate <PERSON> (best known as \"<PERSON><PERSON><PERSON>\") off the coast of North Carolina. The casualties on both sides include <PERSON>'s first officer <PERSON> and <PERSON><PERSON> himself.", "html": "1718 - <a href=\"https://wikipedia.org/wiki/Royal_Navy\" title=\"Royal Navy\">Royal Navy</a> Lieutenant <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> attacks and boards the vessels of the British pirate <PERSON> (best known as \"<a href=\"https://wikipedia.org/wiki/Blackbeard\" title=\"Blackbeard\">Blackbeard</a>\") off the coast of <a href=\"https://wikipedia.org/wiki/North_Carolina\" title=\"North Carolina\">North Carolina</a>. The casualties on both sides include <PERSON>'s first officer <PERSON> and <PERSON><PERSON> himself.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Royal_Navy\" title=\"Royal Navy\">Royal Navy</a> Lieutenant <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> attacks and boards the vessels of the British pirate <PERSON> (best known as \"<a href=\"https://wikipedia.org/wiki/Blackbeard\" title=\"Blackbeard\">Blackbeard</a>\") off the coast of <a href=\"https://wikipedia.org/wiki/North_Carolina\" title=\"North Carolina\">North Carolina</a>. The casualties on both sides include <PERSON>'s first officer <PERSON> and <PERSON><PERSON> himself.", "links": [{"title": "Royal Navy", "link": "https://wikipedia.org/wiki/Royal_Navy"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Blackbeard", "link": "https://wikipedia.org/wiki/Blackbeard"}, {"title": "North Carolina", "link": "https://wikipedia.org/wiki/North_Carolina"}]}, {"year": "1837", "text": "Canadian journalist and politician <PERSON> calls for a rebellion against the United Kingdom in his essay \"To the People of Upper Canada\", published in his newspaper The Constitution.", "html": "1837 - Canadian journalist and politician <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> calls for a <a href=\"https://wikipedia.org/wiki/Upper_Canada_Rebellion\" title=\"Upper Canada Rebellion\">rebellion</a> against the United Kingdom in his essay \"To the People of <a href=\"https://wikipedia.org/wiki/Upper_Canada\" title=\"Upper Canada\">Upper Canada</a>\", published in his newspaper <i>The Constitution</i>.", "no_year_html": "Canadian journalist and politician <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> calls for a <a href=\"https://wikipedia.org/wiki/Upper_Canada_Rebellion\" title=\"Upper Canada Rebellion\">rebellion</a> against the United Kingdom in his essay \"To the People of <a href=\"https://wikipedia.org/wiki/Upper_Canada\" title=\"Upper Canada\">Upper Canada</a>\", published in his newspaper <i>The Constitution</i>.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "Upper Canada Rebellion", "link": "https://wikipedia.org/wiki/Upper_Canada_Rebellion"}, {"title": "Upper Canada", "link": "https://wikipedia.org/wiki/Upper_Canada"}]}, {"year": "1855", "text": "In Birmingham, England, <PERSON>, <PERSON> lays the foundation stone of the Birmingham and Midland Institute.", "html": "1855 - In <a href=\"https://wikipedia.org/wiki/Birmingham\" title=\"Birmingham\">Birmingham</a>, England, <a href=\"https://wikipedia.org/wiki/<PERSON>,_Prince_Consort\" class=\"mw-redirect\" title=\"<PERSON>, Prince <PERSON>\"><PERSON>, Prince <PERSON></a> lays the foundation stone of the <a href=\"https://wikipedia.org/wiki/Birmingham_and_Midland_Institute\" title=\"Birmingham and Midland Institute\">Birmingham and Midland Institute</a>.", "no_year_html": "In <a href=\"https://wikipedia.org/wiki/Birmingham\" title=\"Birmingham\">Birmingham</a>, England, <a href=\"https://wikipedia.org/wiki/<PERSON>,_Prince_Consort\" class=\"mw-redirect\" title=\"<PERSON>, Prince <PERSON>\"><PERSON>, Prince <PERSON></a> lays the foundation stone of the <a href=\"https://wikipedia.org/wiki/Birmingham_and_Midland_Institute\" title=\"Birmingham and Midland Institute\">Birmingham and Midland Institute</a>.", "links": [{"title": "Birmingham", "link": "https://wikipedia.org/wiki/Birmingham"}, {"title": "<PERSON>, Prince <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>,_Prince_Consort"}, {"title": "Birmingham and Midland Institute", "link": "https://wikipedia.org/wiki/Birmingham_and_Midland_Institute"}]}, {"year": "1869", "text": "In Dumbarton, Scotland, the clipper Cutty Sark is launched.", "html": "1869 - In <a href=\"https://wikipedia.org/wiki/Dumbarton\" title=\"Dumbarton\">Dumbarton</a>, Scotland, the <a href=\"https://wikipedia.org/wiki/Clipper\" title=\"Clipper\">clipper</a> <i><a href=\"https://wikipedia.org/wiki/Cutty_Sark\" title=\"Cutty Sark\">Cutty Sark</a></i> is launched.", "no_year_html": "In <a href=\"https://wikipedia.org/wiki/Dumbarton\" title=\"Dumbarton\">Dumbarton</a>, Scotland, the <a href=\"https://wikipedia.org/wiki/Clipper\" title=\"Clipper\">clipper</a> <i><a href=\"https://wikipedia.org/wiki/Cutty_Sark\" title=\"Cutty Sark\">Cutty Sark</a></i> is launched.", "links": [{"title": "Dumbarton", "link": "https://wikipedia.org/wiki/Dumbarton"}, {"title": "Clipper", "link": "https://wikipedia.org/wiki/Clipper"}, {"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>rk"}]}, {"year": "1873", "text": "The French steamer SS Ville du Havre sinks in 12 minutes after colliding with the Scottish iron clipper Loch Earn in the Atlantic, with a loss of 226 lives.", "html": "1873 - The French steamer <a href=\"https://wikipedia.org/wiki/SS_Ville_du_Havre\" title=\"SS Ville du Havre\">SS <i>Ville du Havre</i></a> sinks in 12 minutes after colliding with the Scottish iron clipper <i><a href=\"https://wikipedia.org/wiki/Loch_Line\" title=\"Loch Line\">Loch Earn</a></i> in the Atlantic, with a loss of 226 lives.", "no_year_html": "The French steamer <a href=\"https://wikipedia.org/wiki/SS_Ville_du_Havre\" title=\"SS Ville du Havre\">SS <i>Ville du Havre</i></a> sinks in 12 minutes after colliding with the Scottish iron clipper <i><a href=\"https://wikipedia.org/wiki/Loch_Line\" title=\"Loch Line\">Loch Earn</a></i> in the Atlantic, with a loss of 226 lives.", "links": [{"title": "SS Ville du Havre", "link": "https://wikipedia.org/wiki/SS_Ville_du_Havre"}, {"title": "Loch Line", "link": "https://wikipedia.org/wiki/Loch_Line"}]}, {"year": "1908", "text": "The Congress of Manastir establishes the Albanian alphabet.", "html": "1908 - The <a href=\"https://wikipedia.org/wiki/Congress_of_Manastir\" title=\"Congress of Manastir\">Congress of Manastir</a> establishes the <a href=\"https://wikipedia.org/wiki/Albanian_alphabet\" title=\"Albanian alphabet\">Albanian alphabet</a>.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Congress_of_Manastir\" title=\"Congress of Manastir\">Congress of Manastir</a> establishes the <a href=\"https://wikipedia.org/wiki/Albanian_alphabet\" title=\"Albanian alphabet\">Albanian alphabet</a>.", "links": [{"title": "Congress of Manastir", "link": "https://wikipedia.org/wiki/Congress_of_Manastir"}, {"title": "Albanian alphabet", "link": "https://wikipedia.org/wiki/Albanian_alphabet"}]}, {"year": "1921", "text": "During The Troubles in Northern Ireland (1920-1922), 22 Irish Nationalists are killed in Belfast in one day.", "html": "1921 - During <a href=\"https://wikipedia.org/wiki/The_Troubles_in_Northern_Ireland_(1920%E2%80%931922)\" class=\"mw-redirect\" title=\"The Troubles in Northern Ireland (1920-1922)\">The Troubles in Northern Ireland (1920-1922)</a>, 22 <a href=\"https://wikipedia.org/wiki/Irish_nationalism\" title=\"Irish nationalism\">Irish Nationalists</a> are killed in Belfast in one day.", "no_year_html": "During <a href=\"https://wikipedia.org/wiki/The_Troubles_in_Northern_Ireland_(1920%E2%80%931922)\" class=\"mw-redirect\" title=\"The Troubles in Northern Ireland (1920-1922)\">The Troubles in Northern Ireland (1920-1922)</a>, 22 <a href=\"https://wikipedia.org/wiki/Irish_nationalism\" title=\"Irish nationalism\">Irish Nationalists</a> are killed in Belfast in one day.", "links": [{"title": "The Troubles in Northern Ireland (1920-1922)", "link": "https://wikipedia.org/wiki/The_Troubles_in_Northern_Ireland_(1920%E2%80%931922)"}, {"title": "Irish nationalism", "link": "https://wikipedia.org/wiki/Irish_nationalism"}]}, {"year": "1935", "text": "The China Clipper inaugurates the first commercial transpacific air service, connecting Alameda, California with Manila.", "html": "1935 - The <i><a href=\"https://wikipedia.org/wiki/China_Clipper\" title=\"China Clipper\">China Clipper</a></i> inaugurates the first commercial transpacific air service, connecting <a href=\"https://wikipedia.org/wiki/China_Clipper_flight_departure_site\" title=\"China Clipper flight departure site\">Alameda, California</a> with <a href=\"https://wikipedia.org/wiki/Manila\" title=\"Manila\">Manila</a>.", "no_year_html": "The <i><a href=\"https://wikipedia.org/wiki/China_Clipper\" title=\"China Clipper\">China Clipper</a></i> inaugurates the first commercial transpacific air service, connecting <a href=\"https://wikipedia.org/wiki/China_Clipper_flight_departure_site\" title=\"China Clipper flight departure site\">Alameda, California</a> with <a href=\"https://wikipedia.org/wiki/Manila\" title=\"Manila\">Manila</a>.", "links": [{"title": "China Clipper", "link": "https://wikipedia.org/wiki/China_Clipper"}, {"title": "China Clipper flight departure site", "link": "https://wikipedia.org/wiki/China_Clipper_flight_departure_site"}, {"title": "Manila", "link": "https://wikipedia.org/wiki/Manila"}]}, {"year": "1940", "text": "World War II: Following the initial Italian invasion, Greek troops counterattack into Italian-occupied Albania and capture Korytsa.", "html": "1940 - <a href=\"https://wikipedia.org/wiki/World_War_II\" title=\"World War II\">World War II</a>: Following the initial <a href=\"https://wikipedia.org/wiki/Greco-Italian_War\" title=\"Greco-Italian War\">Italian invasion</a>, <a href=\"https://wikipedia.org/wiki/Kingdom_of_Greece_(Gl%C3%BCcksburg)\" class=\"mw-redirect\" title=\"Kingdom of Greece (Glücksburg)\">Greek</a> troops counterattack into <a href=\"https://wikipedia.org/wiki/Albanian_Kingdom_(1939%E2%80%9343)\" class=\"mw-redirect\" title=\"Albanian Kingdom (1939-43)\">Italian-occupied Albania</a> and capture <a href=\"https://wikipedia.org/wiki/Kor%C3%A7%C3%AB\" title=\"Korçë\">Korytsa</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/World_War_II\" title=\"World War II\">World War II</a>: Following the initial <a href=\"https://wikipedia.org/wiki/Greco-Italian_War\" title=\"Greco-Italian War\">Italian invasion</a>, <a href=\"https://wikipedia.org/wiki/Kingdom_of_Greece_(Gl%C3%BCcksburg)\" class=\"mw-redirect\" title=\"Kingdom of Greece (Glücksburg)\">Greek</a> troops counterattack into <a href=\"https://wikipedia.org/wiki/Albanian_Kingdom_(1939%E2%80%9343)\" class=\"mw-redirect\" title=\"Albanian Kingdom (1939-43)\">Italian-occupied Albania</a> and capture <a href=\"https://wikipedia.org/wiki/Kor%C3%A7%C3%AB\" title=\"Korçë\">Korytsa</a>.", "links": [{"title": "World War II", "link": "https://wikipedia.org/wiki/World_War_II"}, {"title": "Greco-Italian War", "link": "https://wikipedia.org/wiki/Greco-Italian_War"}, {"title": "Kingdom of Greece (Glücksburg)", "link": "https://wikipedia.org/wiki/Kingdom_of_Greece_(Gl%C3%BCcksburg)"}, {"title": "Albanian Kingdom (1939-43)", "link": "https://wikipedia.org/wiki/Albanian_Kingdom_(1939%E2%80%9343)"}, {"title": "Korçë", "link": "https://wikipedia.org/wiki/Kor%C3%A7%C3%AB"}]}, {"year": "1942", "text": "World War II: Battle of Stalingrad: General <PERSON> sends <PERSON> a telegram saying that the German 6th Army is surrounded.", "html": "1942 - World War II: <a href=\"https://wikipedia.org/wiki/Battle_of_Stalingrad\" title=\"Battle of Stalingrad\">Battle of Stalingrad</a>: General <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> sends <a href=\"https://wikipedia.org/wiki/Adolf_<PERSON>\" title=\"Adolf Hitler\"><PERSON></a> a <a href=\"https://wikipedia.org/wiki/Telegraphy\" title=\"Telegraphy\">telegram</a> saying that the German <a href=\"https://wikipedia.org/wiki/6th_Army_(Wehrmacht)\" title=\"6th Army (Wehrmacht)\">6th Army</a> is surrounded.", "no_year_html": "World War II: <a href=\"https://wikipedia.org/wiki/Battle_of_Stalingrad\" title=\"Battle of Stalingrad\">Battle of Stalingrad</a>: General <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> sends <a href=\"https://wikipedia.org/wiki/Adolf_<PERSON>\" title=\"Adolf <PERSON>\"><PERSON></a> a <a href=\"https://wikipedia.org/wiki/Telegraphy\" title=\"Telegraphy\">telegram</a> saying that the German <a href=\"https://wikipedia.org/wiki/6th_Army_(Wehrmacht)\" title=\"6th Army (Wehrmacht)\">6th Army</a> is surrounded.", "links": [{"title": "Battle of Stalingrad", "link": "https://wikipedia.org/wiki/Battle_of_Stalingrad"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Telegraphy", "link": "https://wikipedia.org/wiki/Telegraphy"}, {"title": "6th Army (Wehrmacht)", "link": "https://wikipedia.org/wiki/6th_Army_(Wehrmacht)"}]}, {"year": "1943", "text": "World War II: Cairo Conference: U.S. President <PERSON>, British Prime Minister <PERSON>, and Chinese Premier <PERSON> meet in Cairo, Egypt, to discuss ways to defeat Japan.", "html": "1943 - World War II: <a href=\"https://wikipedia.org/wiki/Cairo_Conference\" title=\"Cairo Conference\">Cairo Conference</a>: U.S. President <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, British Prime Minister <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, and Chinese Premier <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> meet in <a href=\"https://wikipedia.org/wiki/Cairo\" title=\"Cairo\">Cairo</a>, Egypt, to discuss ways to defeat Japan.", "no_year_html": "World War II: <a href=\"https://wikipedia.org/wiki/Cairo_Conference\" title=\"Cairo Conference\">Cairo Conference</a>: U.S. President <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, British Prime Minister <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, and Chinese Premier <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> meet in <a href=\"https://wikipedia.org/wiki/Cairo\" title=\"Cairo\">Cairo</a>, Egypt, to discuss ways to defeat Japan.", "links": [{"title": "Cairo Conference", "link": "https://wikipedia.org/wiki/Cairo_Conference"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>-<PERSON>"}, {"title": "Cairo", "link": "https://wikipedia.org/wiki/Cairo"}]}, {"year": "1943", "text": "Lebanon gains independence from France, nearly two years after it was first announced by the Free French government.", "html": "1943 - <a href=\"https://wikipedia.org/wiki/Lebanon\" title=\"Lebanon\">Lebanon</a> gains independence from France, nearly two years after it was first announced by the <a href=\"https://wikipedia.org/wiki/Free_French\" class=\"mw-redirect\" title=\"Free French\">Free French</a> government.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Lebanon\" title=\"Lebanon\">Lebanon</a> gains independence from France, nearly two years after it was first announced by the <a href=\"https://wikipedia.org/wiki/Free_French\" class=\"mw-redirect\" title=\"Free French\">Free French</a> government.", "links": [{"title": "Lebanon", "link": "https://wikipedia.org/wiki/Lebanon"}, {"title": "Free French", "link": "https://wikipedia.org/wiki/Free_French"}]}, {"year": "1952", "text": "A Douglas C-124 Globemaster II crashes into Mount Gannet, Alaska, killing all 52 aboard.", "html": "1952 - A <a href=\"https://wikipedia.org/wiki/Douglas_C-124_Globemaster_II\" title=\"Douglas C-124 Globemaster II\">Douglas C-124 Globemaster II</a> <a href=\"https://wikipedia.org/wiki/1952_Mount_Gannett_C-124_crash\" title=\"1952 Mount Gannett C-124 crash\">crashes</a> into <a href=\"https://wikipedia.org/wiki/Mount_Gannett\" title=\"Mount Gannett\">Mount Gannet</a>, Alaska, killing all 52 aboard.", "no_year_html": "A <a href=\"https://wikipedia.org/wiki/Douglas_C-124_Globemaster_II\" title=\"Douglas C-124 Globemaster II\">Douglas C-124 Globemaster II</a> <a href=\"https://wikipedia.org/wiki/1952_Mount_Gannett_C-124_crash\" title=\"1952 Mount Gannett C-124 crash\">crashes</a> into <a href=\"https://wikipedia.org/wiki/Mount_Gannett\" title=\"Mount Gannett\">Mount Gannet</a>, Alaska, killing all 52 aboard.", "links": [{"title": "Douglas C-124 Globemaster II", "link": "https://wikipedia.org/wiki/Douglas_C-124_Globemaster_II"}, {"title": "1952 Mount Gannett C-124 crash", "link": "https://wikipedia.org/wiki/1952_Mount_Gannett_C-124_crash"}, {"title": "Mount Gannett", "link": "https://wikipedia.org/wiki/Mount_Gannett"}]}, {"year": "1955", "text": "The Soviet Union launches RDS-37, a 1.6 megaton two stage hydrogen bomb designed by <PERSON>. The bomb was dropped over Semipalatinsk.", "html": "1955 - The <a href=\"https://wikipedia.org/wiki/Soviet_Union\" title=\"Soviet Union\">Soviet Union</a> launches <a href=\"https://wikipedia.org/wiki/RDS-37\" title=\"RDS-37\">RDS-37</a>, a 1.6 megaton <a href=\"https://wikipedia.org/wiki/Thermonuclear_weapon\" title=\"Thermonuclear weapon\">two stage hydrogen bomb</a> designed by <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>. The bomb was dropped over <a href=\"https://wikipedia.org/wiki/Semipalatinsk_Test_Site\" title=\"Semipalatinsk Test Site\">Semipalatinsk</a>.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Soviet_Union\" title=\"Soviet Union\">Soviet Union</a> launches <a href=\"https://wikipedia.org/wiki/RDS-37\" title=\"RDS-37\">RDS-37</a>, a 1.6 megaton <a href=\"https://wikipedia.org/wiki/Thermonuclear_weapon\" title=\"Thermonuclear weapon\">two stage hydrogen bomb</a> designed by <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>. The bomb was dropped over <a href=\"https://wikipedia.org/wiki/Semipalatinsk_Test_Site\" title=\"Semipalatinsk Test Site\">Semipalatinsk</a>.", "links": [{"title": "Soviet Union", "link": "https://wikipedia.org/wiki/Soviet_Union"}, {"title": "RDS-37", "link": "https://wikipedia.org/wiki/RDS-37"}, {"title": "Thermonuclear weapon", "link": "https://wikipedia.org/wiki/Thermonuclear_weapon"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}, {"title": "Semipalatinsk Test Site", "link": "https://wikipedia.org/wiki/Semipalatinsk_Test_Site"}]}, {"year": "1963", "text": "U.S. President <PERSON> is assassinated and Texas Governor <PERSON> is seriously wounded by <PERSON>, who also kills Dallas Police officer <PERSON><PERSON> <PERSON><PERSON> after fleeing the scene. Vice President <PERSON> is sworn in as the 36th President of the United States afterwards.", "html": "1963 - U.S. President <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> is <a href=\"https://wikipedia.org/wiki/Assassination_of_<PERSON>_<PERSON>_<PERSON>\" title=\"Assassination of <PERSON>\">assassinated</a> and <a href=\"https://wikipedia.org/wiki/Texas\" title=\"Texas\">Texas</a> Governor <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> is seriously wounded by <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, who also kills Dallas Police officer <a href=\"https://wikipedia.org/wiki/J._D._Tippit\" title=\"J. D. Tippit\">J. D. Tippit</a> after fleeing the scene. Vice President <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> is <a href=\"https://wikipedia.org/wiki/First_inauguration_of_<PERSON>_<PERSON><PERSON>\" title=\"First inauguration of <PERSON> B. <PERSON>\">sworn in</a> as the 36th <a href=\"https://wikipedia.org/wiki/President_of_the_United_States\" title=\"President of the United States\">President of the United States</a> afterwards.", "no_year_html": "U.S. President <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> is <a href=\"https://wikipedia.org/wiki/Assassination_of_<PERSON>_<PERSON>_<PERSON>\" title=\"Assassination of <PERSON>\">assassinated</a> and <a href=\"https://wikipedia.org/wiki/Texas\" title=\"Texas\">Texas</a> Governor <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> is seriously wounded by <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, who also kills Dallas Police officer <a href=\"https://wikipedia.org/wiki/J._D._Tippit\" title=\"J. D. Tippit\">J. D. Tippit</a> after fleeing the scene. Vice President <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> is <a href=\"https://wikipedia.org/wiki/First_inauguration_of_<PERSON>_<PERSON><PERSON>\" title=\"First inauguration of <PERSON> B. <PERSON>\">sworn in</a> as the 36th <a href=\"https://wikipedia.org/wiki/President_of_the_United_States\" title=\"President of the United States\">President of the United States</a> afterwards.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>"}, {"title": "Assassination of <PERSON>", "link": "https://wikipedia.org/wiki/Assassination_of_<PERSON>_<PERSON><PERSON>_<PERSON>"}, {"title": "Texas", "link": "https://wikipedia.org/wiki/Texas"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "<PERSON><PERSON> <PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_Tippit"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>"}, {"title": "First inauguration of <PERSON>", "link": "https://wikipedia.org/wiki/First_inauguration_of_<PERSON>_<PERSON><PERSON>_<PERSON>"}, {"title": "President of the United States", "link": "https://wikipedia.org/wiki/President_of_the_United_States"}]}, {"year": "1963", "text": "Five Indian generals are killed in a helicopter crash, due to collision with two parallel lines of telegraph cables.", "html": "1963 - Five Indian generals are killed in a <a href=\"https://wikipedia.org/wiki/1963_Poonch_Indian_Air_Force_helicopter_crash\" title=\"1963 Poonch Indian Air Force helicopter crash\">helicopter crash</a>, due to collision with two parallel lines of telegraph cables.", "no_year_html": "Five Indian generals are killed in a <a href=\"https://wikipedia.org/wiki/1963_Poonch_Indian_Air_Force_helicopter_crash\" title=\"1963 Poonch Indian Air Force helicopter crash\">helicopter crash</a>, due to collision with two parallel lines of telegraph cables.", "links": [{"title": "1963 Poonch Indian Air Force helicopter crash", "link": "https://wikipedia.org/wiki/1963_Poonch_Indian_Air_Force_helicopter_crash"}]}, {"year": "1967", "text": "UN Security Council Resolution 242 is adopted, establishing a set of the principles aimed at guiding negotiations for an Arab-Israeli peace settlement.", "html": "1967 - <a href=\"https://wikipedia.org/wiki/United_Nations_Security_Council_Resolution_242\" title=\"United Nations Security Council Resolution 242\">UN Security Council Resolution 242</a> is adopted, establishing a set of the principles aimed at guiding negotiations for an <a href=\"https://wikipedia.org/wiki/Arabs\" title=\"Arabs\">Arab</a>-<a href=\"https://wikipedia.org/wiki/Israel\" title=\"Israel\">Israeli</a> peace settlement.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/United_Nations_Security_Council_Resolution_242\" title=\"United Nations Security Council Resolution 242\">UN Security Council Resolution 242</a> is adopted, establishing a set of the principles aimed at guiding negotiations for an <a href=\"https://wikipedia.org/wiki/Arabs\" title=\"Arabs\">Arab</a>-<a href=\"https://wikipedia.org/wiki/Israel\" title=\"Israel\">Israeli</a> peace settlement.", "links": [{"title": "United Nations Security Council Resolution 242", "link": "https://wikipedia.org/wiki/United_Nations_Security_Council_Resolution_242"}, {"title": "Arabs", "link": "https://wikipedia.org/wiki/Arabs"}, {"title": "Israel", "link": "https://wikipedia.org/wiki/Israel"}]}, {"year": "1968", "text": "Japan Air Lines Flight 2 accidentally ditches in San Francisco Bay while on approach to San Francisco International Airport. No one is injured.", "html": "1968 - <a href=\"https://wikipedia.org/wiki/Japan_Air_Lines_Flight_2\" title=\"Japan Air Lines Flight 2\">Japan Air Lines Flight 2</a> accidentally <a href=\"https://wikipedia.org/wiki/Water_landing\" title=\"Water landing\">ditches</a> in <a href=\"https://wikipedia.org/wiki/San_Francisco_Bay\" title=\"San Francisco Bay\">San Francisco Bay</a> while on approach to <a href=\"https://wikipedia.org/wiki/San_Francisco_International_Airport\" title=\"San Francisco International Airport\">San Francisco International Airport</a>. No one is injured.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Japan_Air_Lines_Flight_2\" title=\"Japan Air Lines Flight 2\">Japan Air Lines Flight 2</a> accidentally <a href=\"https://wikipedia.org/wiki/Water_landing\" title=\"Water landing\">ditches</a> in <a href=\"https://wikipedia.org/wiki/San_Francisco_Bay\" title=\"San Francisco Bay\">San Francisco Bay</a> while on approach to <a href=\"https://wikipedia.org/wiki/San_Francisco_International_Airport\" title=\"San Francisco International Airport\">San Francisco International Airport</a>. No one is injured.", "links": [{"title": "Japan Air Lines Flight 2", "link": "https://wikipedia.org/wiki/Japan_Air_Lines_Flight_2"}, {"title": "Water landing", "link": "https://wikipedia.org/wiki/Water_landing"}, {"title": "San Francisco Bay", "link": "https://wikipedia.org/wiki/San_Francisco_Bay"}, {"title": "San Francisco International Airport", "link": "https://wikipedia.org/wiki/San_Francisco_International_Airport"}]}, {"year": "1971", "text": "In Britain's worst mountaineering tragedy, the Cairngorm Plateau Disaster, five children and one of their leaders are found dead from exposure in the Scottish mountains.", "html": "1971 - In Britain's worst mountaineering tragedy, the <a href=\"https://wikipedia.org/wiki/Cairngorm_Plateau_Disaster\" class=\"mw-redirect\" title=\"Cairngorm Plateau Disaster\">Cairngorm Plateau Disaster</a>, five children and one of their leaders are found dead from <a href=\"https://wikipedia.org/wiki/Hypothermia\" title=\"Hypothermia\">exposure</a> in the Scottish mountains.", "no_year_html": "In Britain's worst mountaineering tragedy, the <a href=\"https://wikipedia.org/wiki/Cairngorm_Plateau_Disaster\" class=\"mw-redirect\" title=\"Cairngorm Plateau Disaster\">Cairngorm Plateau Disaster</a>, five children and one of their leaders are found dead from <a href=\"https://wikipedia.org/wiki/Hypothermia\" title=\"Hypothermia\">exposure</a> in the Scottish mountains.", "links": [{"title": "Cairngorm Plateau Disaster", "link": "https://wikipedia.org/wiki/Cairngorm_Plateau_Disaster"}, {"title": "Hypothermia", "link": "https://wikipedia.org/wiki/Hypothermia"}]}, {"year": "1975", "text": "<PERSON> is declared King of Spain following the death of <PERSON>.", "html": "1975 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_of_Spain\" class=\"mw-redirect\" title=\"<PERSON> of Spain\"><PERSON></a> is declared <a href=\"https://wikipedia.org/wiki/List_of_Spanish_monarchs\" title=\"List of Spanish monarchs\">King of Spain</a> following the death of <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_of_Spain\" class=\"mw-redirect\" title=\"<PERSON> of Spain\"><PERSON></a> is declared <a href=\"https://wikipedia.org/wiki/List_of_Spanish_monarchs\" title=\"List of Spanish monarchs\">King of Spain</a> following the death of <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>.", "links": [{"title": "<PERSON> of Spain", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_of_Spain"}, {"title": "List of Spanish monarchs", "link": "https://wikipedia.org/wiki/List_of_Spanish_monarchs"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Francisco_Franco"}]}, {"year": "1987", "text": "The Max Headroom signal hijacking incident takes place, in which a pirate broadcast interrupts television broadcasts in Chicago.", "html": "1987 - The <a href=\"https://wikipedia.org/wiki/Max_Headroom_signal_hijacking\" title=\"Max Headroom signal hijacking\">Max Headroom signal hijacking</a> incident takes place, in which a pirate broadcast interrupts television broadcasts in <a href=\"https://wikipedia.org/wiki/Chicago\" title=\"Chicago\">Chicago</a>.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Max_Headroom_signal_hijacking\" title=\"Max Headroom signal hijacking\">Max Headroom signal hijacking</a> incident takes place, in which a pirate broadcast interrupts television broadcasts in <a href=\"https://wikipedia.org/wiki/Chicago\" title=\"Chicago\">Chicago</a>.", "links": [{"title": "Max Headroom signal hijacking", "link": "https://wikipedia.org/wiki/Max_Headroom_signal_hijacking"}, {"title": "Chicago", "link": "https://wikipedia.org/wiki/Chicago"}]}, {"year": "1989", "text": "NASA launches Space Shuttle Discovery on STS-33, a classified mission for the United States Department of Defense.", "html": "1989 - <a href=\"https://wikipedia.org/wiki/NASA\" title=\"NASA\">NASA</a> launches <a href=\"https://wikipedia.org/wiki/Space_Shuttle_Discovery\" title=\"Space Shuttle Discovery\">Space Shuttle <i>Discovery</i></a> on <a href=\"https://wikipedia.org/wiki/STS-33\" title=\"STS-33\">STS-33</a>, a classified mission for the <a href=\"https://wikipedia.org/wiki/United_States_Department_of_Defense\" title=\"United States Department of Defense\">United States Department of Defense</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/NASA\" title=\"NASA\">NASA</a> launches <a href=\"https://wikipedia.org/wiki/Space_Shuttle_Discovery\" title=\"Space Shuttle Discovery\">Space Shuttle <i>Discovery</i></a> on <a href=\"https://wikipedia.org/wiki/STS-33\" title=\"STS-33\">STS-33</a>, a classified mission for the <a href=\"https://wikipedia.org/wiki/United_States_Department_of_Defense\" title=\"United States Department of Defense\">United States Department of Defense</a>.", "links": [{"title": "NASA", "link": "https://wikipedia.org/wiki/NASA"}, {"title": "Space Shuttle Discovery", "link": "https://wikipedia.org/wiki/Space_Shuttle_Discovery"}, {"title": "STS-33", "link": "https://wikipedia.org/wiki/STS-33"}, {"title": "United States Department of Defense", "link": "https://wikipedia.org/wiki/United_States_Department_of_Defense"}]}, {"year": "1990", "text": "British Prime Minister <PERSON> withdraws from the Conservative Party leadership election, confirming the end of her Prime-Ministership.", "html": "1990 - British <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_the_United_Kingdom\" title=\"Prime Minister of the United Kingdom\">Prime Minister</a> <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>\" title=\"<PERSON>\"><PERSON></a> withdraws from the <a href=\"https://wikipedia.org/wiki/1990_Conservative_Party_leadership_election\" title=\"1990 Conservative Party leadership election\">Conservative Party leadership election</a>, confirming the end of her <a href=\"https://wikipedia.org/wiki/Premiership_of_<PERSON>_<PERSON>\" title=\"Premiership of <PERSON>\">Prime-Ministership</a>.", "no_year_html": "British <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_the_United_Kingdom\" title=\"Prime Minister of the United Kingdom\">Prime Minister</a> <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> withdraws from the <a href=\"https://wikipedia.org/wiki/1990_Conservative_Party_leadership_election\" title=\"1990 Conservative Party leadership election\">Conservative Party leadership election</a>, confirming the end of her <a href=\"https://wikipedia.org/wiki/Premiership_of_<PERSON>_<PERSON>\" title=\"Premiership of <PERSON>\">Prime-Ministership</a>.", "links": [{"title": "Prime Minister of the United Kingdom", "link": "https://wikipedia.org/wiki/Prime_Minister_of_the_United_Kingdom"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "1990 Conservative Party leadership election", "link": "https://wikipedia.org/wiki/1990_Conservative_Party_leadership_election"}, {"title": "Premiership of <PERSON>", "link": "https://wikipedia.org/wiki/Premiership_of_<PERSON>_<PERSON>"}]}, {"year": "1994", "text": "A Trans World Airlines McDonnell Douglas MD-80 and Cessna 441 Conquest II aircraft collide on the runway at St. Louis Lambert International Airport in Bridgeton, Missouri, killing two people and injuring eight.", "html": "1994 - A <a href=\"https://wikipedia.org/wiki/Trans_World_Airlines\" title=\"Trans World Airlines\">Trans World Airlines</a> <a href=\"https://wikipedia.org/wiki/McDonnell_Douglas_MD-80\" title=\"McDonnell Douglas MD-80\">McDonnell Douglas MD-80</a> and <a href=\"https://wikipedia.org/wiki/Cessna_441_Conquest_II\" title=\"Cessna 441 Conquest II\">Cessna 441 Conquest II</a> aircraft <a href=\"https://wikipedia.org/wiki/1994_St._Louis_Airport_collision\" title=\"1994 St. Louis Airport collision\">collide</a> on the runway at <a href=\"https://wikipedia.org/wiki/St._Louis_Lambert_International_Airport\" title=\"St. Louis Lambert International Airport\">St. Louis Lambert International Airport</a> in <a href=\"https://wikipedia.org/wiki/Bridgeton,_Missouri\" title=\"Bridgeton, Missouri\">Bridgeton, Missouri</a>, killing two people and injuring eight.", "no_year_html": "A <a href=\"https://wikipedia.org/wiki/Trans_World_Airlines\" title=\"Trans World Airlines\">Trans World Airlines</a> <a href=\"https://wikipedia.org/wiki/McDonnell_Douglas_MD-80\" title=\"McDonnell Douglas MD-80\">McDonnell Douglas MD-80</a> and <a href=\"https://wikipedia.org/wiki/Cessna_441_Conquest_II\" title=\"Cessna 441 Conquest II\">Cessna 441 Conquest II</a> aircraft <a href=\"https://wikipedia.org/wiki/1994_St._Louis_Airport_collision\" title=\"1994 St. Louis Airport collision\">collide</a> on the runway at <a href=\"https://wikipedia.org/wiki/St._Louis_Lambert_International_Airport\" title=\"St. Louis Lambert International Airport\">St. Louis Lambert International Airport</a> in <a href=\"https://wikipedia.org/wiki/Bridgeton,_Missouri\" title=\"Bridgeton, Missouri\">Bridgeton, Missouri</a>, killing two people and injuring eight.", "links": [{"title": "Trans World Airlines", "link": "https://wikipedia.org/wiki/Trans_World_Airlines"}, {"title": "McDonnell Douglas MD-80", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>-80"}, {"title": "Cessna 441 Conquest II", "link": "https://wikipedia.org/wiki/Cessna_441_Conquest_II"}, {"title": "1994 St. Louis Airport collision", "link": "https://wikipedia.org/wiki/1994_St._Louis_Airport_collision"}, {"title": "St. Louis Lambert International Airport", "link": "https://wikipedia.org/wiki/St._Louis_Lambert_International_Airport"}, {"title": "Bridgeton, Missouri", "link": "https://wikipedia.org/wiki/Bridgeton,_Missouri"}]}, {"year": "2003", "text": "Baghdad DHL attempted shootdown incident: Shortly after takeoff, a DHL Express cargo plane is struck on the left wing by a surface-to-air missile and forced to land.", "html": "2003 - <a href=\"https://wikipedia.org/wiki/2003_Baghdad_DHL_attempted_shootdown_incident\" title=\"2003 Baghdad DHL attempted shootdown incident\">Baghdad DHL attempted shootdown incident</a>: Shortly after takeoff, a <a href=\"https://wikipedia.org/wiki/DHL_Express\" class=\"mw-redirect\" title=\"DHL Express\">DHL Express</a> cargo plane is struck on the left wing by a surface-to-air missile and forced to land.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/2003_Baghdad_DHL_attempted_shootdown_incident\" title=\"2003 Baghdad DHL attempted shootdown incident\">Baghdad DHL attempted shootdown incident</a>: Shortly after takeoff, a <a href=\"https://wikipedia.org/wiki/DHL_Express\" class=\"mw-redirect\" title=\"DHL Express\">DHL Express</a> cargo plane is struck on the left wing by a surface-to-air missile and forced to land.", "links": [{"title": "2003 Baghdad DHL attempted shootdown incident", "link": "https://wikipedia.org/wiki/2003_Baghdad_DHL_attempted_shootdown_incident"}, {"title": "DHL Express", "link": "https://wikipedia.org/wiki/DHL_Express"}]}, {"year": "2004", "text": "The Orange Revolution begins in Ukraine, resulting from the presidential elections.", "html": "2004 - The <a href=\"https://wikipedia.org/wiki/Orange_Revolution\" title=\"Orange Revolution\">Orange Revolution</a> begins in <a href=\"https://wikipedia.org/wiki/Ukraine\" title=\"Ukraine\">Ukraine</a>, resulting from the presidential elections.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Orange_Revolution\" title=\"Orange Revolution\">Orange Revolution</a> begins in <a href=\"https://wikipedia.org/wiki/Ukraine\" title=\"Ukraine\">Ukraine</a>, resulting from the presidential elections.", "links": [{"title": "Orange Revolution", "link": "https://wikipedia.org/wiki/Orange_Revolution"}, {"title": "Ukraine", "link": "https://wikipedia.org/wiki/Ukraine"}]}, {"year": "2010", "text": "During the Cambodian water festival, a stampede in Koh Pich, Phnom Penh, kills 347 people.", "html": "2010 - During the <a href=\"https://wikipedia.org/wiki/<PERSON>_O<PERSON>_Touk\" title=\"Bon Om Touk\">Cambodian water festival</a>, a <a href=\"https://wikipedia.org/wiki/Phnom_Penh_stampede\" title=\"Phnom Penh stampede\">stampede</a> in <a href=\"https://wikipedia.org/wiki/Koh_Pich\" title=\"<PERSON><PERSON> Pi<PERSON>\"><PERSON><PERSON></a>, <a href=\"https://wikipedia.org/wiki/Phnom_Penh\" title=\"Phnom Penh\">Phnom Penh</a>, kills 347 people.", "no_year_html": "During the <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_Touk\" title=\"Bon Om Touk\">Cambodian water festival</a>, a <a href=\"https://wikipedia.org/wiki/Phnom_Penh_stampede\" title=\"Phnom Penh stampede\">stampede</a> in <a href=\"https://wikipedia.org/wiki/Koh_Pich\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, <a href=\"https://wikipedia.org/wiki/Phnom_Penh\" title=\"Phnom Penh\">Phnom Penh</a>, kills 347 people.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>uk"}, {"title": "Phnom Penh stampede", "link": "https://wikipedia.org/wiki/Phnom_Penh_stampede"}, {"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>ch"}, {"title": "Phnom Penh", "link": "https://wikipedia.org/wiki/Phnom_Penh"}]}, {"year": "2014", "text": "While playing with a toy gun in Cleveland, 12-year-old African American <PERSON><PERSON> is killed by a white police officer.", "html": "2014 - While playing with a toy gun in Cleveland, 12-year-old <a href=\"https://wikipedia.org/wiki/African_Americans\" title=\"African Americans\">African American</a> <PERSON><PERSON> is <a href=\"https://wikipedia.org/wiki/Killing_of_<PERSON><PERSON>_<PERSON>\" title=\"Killing of <PERSON><PERSON>\">killed</a> by a white police officer.", "no_year_html": "While playing with a toy gun in Cleveland, 12-year-old <a href=\"https://wikipedia.org/wiki/African_Americans\" title=\"African Americans\">African American</a> <PERSON><PERSON> is <a href=\"https://wikipedia.org/wiki/Killing_of_<PERSON><PERSON>_<PERSON>\" title=\"Killing of <PERSON><PERSON>\">killed</a> by a white police officer.", "links": [{"title": "African Americans", "link": "https://wikipedia.org/wiki/African_Americans"}, {"title": "Killing of <PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Killing_of_<PERSON><PERSON>_<PERSON>"}]}, {"year": "2022", "text": "A shooting at a Walmart in Chesapeake, Virginia leaves seven workers dead, including the shooter, and four others injured.", "html": "2022 - <a href=\"https://wikipedia.org/wiki/2022_Chesapeake_shooting\" title=\"2022 Chesapeake shooting\">A shooting</a> at a <a href=\"https://wikipedia.org/wiki/Walmart\" title=\"Walmart\">Walmart</a> in <a href=\"https://wikipedia.org/wiki/Chesapeake,_Virginia\" title=\"Chesapeake, Virginia\">Chesapeake, Virginia</a> leaves seven workers dead, including the shooter, and four others injured.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/2022_Chesapeake_shooting\" title=\"2022 Chesapeake shooting\">A shooting</a> at a <a href=\"https://wikipedia.org/wiki/Walmart\" title=\"Walmart\">Walmart</a> in <a href=\"https://wikipedia.org/wiki/Chesapeake,_Virginia\" title=\"Chesapeake, Virginia\">Chesapeake, Virginia</a> leaves seven workers dead, including the shooter, and four others injured.", "links": [{"title": "2022 Chesapeake shooting", "link": "https://wikipedia.org/wiki/2022_Chesapeake_shooting"}, {"title": "Walmart", "link": "https://wikipedia.org/wiki/Walmart"}, {"title": "Chesapeake, Virginia", "link": "https://wikipedia.org/wiki/Chesapeake,_Virginia"}]}], "Births": [{"year": "1428", "text": "<PERSON>, 16th Earl of Warwick, English nobleman, known as \"the Kingmaker\" (d. 1471)", "html": "1428 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_16th_Earl_of_Warwick\" title=\"<PERSON>, 16th Earl of Warwick\"><PERSON>, 16th Earl of Warwick</a>, English nobleman, known as \"the Kingmaker\" (d. 1471)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_16th_Earl_of_Warwick\" title=\"<PERSON>, 16th Earl of Warwick\"><PERSON>, 16th Earl of Warwick</a>, English nobleman, known as \"the Kingmaker\" (d. 1471)", "links": [{"title": "<PERSON>, 16th Earl of Warwick", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_16th_Earl_of_Warwick"}]}, {"year": "1515", "text": "<PERSON> Guise, Queen of Scots (d. 1560)", "html": "1515 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_G<PERSON>\" title=\"<PERSON> of Guise\"><PERSON> of Guise</a>, Queen of Scots (d. 1560)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON> of Guise\"><PERSON> of Guise</a>, Queen of Scots (d. 1560)", "links": [{"title": "<PERSON> of Guise", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1519", "text": "<PERSON>, German humanist and physician (d. 1585)", "html": "1519 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German humanist and physician (d. 1585)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German humanist and physician (d. 1585)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1533", "text": "<PERSON>, Duke of Ferrara, Italian noble (d. 1597)", "html": "1533 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_d%27<PERSON><PERSON>,_Duke_of_Ferrara\" class=\"mw-redirect\" title=\"<PERSON>, Duke of Ferrara\"><PERSON>, Duke of Ferrara</a>, Italian noble (d. 1597)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_d%27<PERSON><PERSON>,_Duke_of_Ferrara\" class=\"mw-redirect\" title=\"<PERSON>, Duke of Ferrara\"><PERSON>, Duke of Ferrara</a>, Italian noble (d. 1597)", "links": [{"title": "<PERSON>, Duke of Ferrara", "link": "https://wikipedia.org/wiki/<PERSON>_II_d%27Este,_<PERSON>_of_Ferrara"}]}, {"year": "1564", "text": "<PERSON>, 11th Baron <PERSON>, English politician, Lord Lieutenant of Kent (d. 1619)", "html": "1564 - <a href=\"https://wikipedia.org/wiki/<PERSON>,_11th_Baron_<PERSON>\" title=\"<PERSON>, 11th Baron <PERSON>\"><PERSON>, 11th Baron <PERSON></a>, English politician, <a href=\"https://wikipedia.org/wiki/Lord_Lieutenant_of_Kent\" title=\"Lord Lieutenant of Kent\">Lord Lieutenant of Kent</a> (d. 1619)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>,_11th_Baron_<PERSON>\" title=\"<PERSON>, 11th Baron <PERSON>\"><PERSON>, 11th Baron <PERSON></a>, English politician, <a href=\"https://wikipedia.org/wiki/Lord_Lieutenant_of_Kent\" title=\"Lord Lieutenant of Kent\">Lord Lieutenant of Kent</a> (d. 1619)", "links": [{"title": "<PERSON>, 11th Baron <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_11th_Baron_<PERSON>"}, {"title": "Lord Lieutenant of Kent", "link": "https://wikipedia.org/wiki/Lord_Lieutenant_of_Kent"}]}, {"year": "1602", "text": "<PERSON> of France (d. 1644)", "html": "1602 - <a href=\"https://wikipedia.org/wiki/<PERSON>_of_France_(1602%E2%80%931644)\" class=\"mw-redirect\" title=\"<PERSON> of France (1602-1644)\"><PERSON> of France</a> (d. 1644)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_of_France_(1602%E2%80%931644)\" class=\"mw-redirect\" title=\"<PERSON> of France (1602-1644)\"><PERSON> of France</a> (d. 1644)", "links": [{"title": "<PERSON> of France (1602-1644)", "link": "https://wikipedia.org/wiki/Elisabeth_of_France_(1602%E2%80%931644)"}]}, {"year": "1635", "text": "<PERSON>, English ornithologist and ichthyologist (d. 1672)", "html": "1635 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English ornithologist and ichthyologist (d. 1672)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English ornithologist and ichthyologist (d. 1672)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1643", "text": "<PERSON><PERSON><PERSON>, <PERSON><PERSON>, French explorer (d. 1687)", "html": "1643 - <a href=\"https://wikipedia.org/wiki/Ren%C3%A9-<PERSON>,_<PERSON><PERSON>_<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>, <PERSON><PERSON>\"><PERSON><PERSON><PERSON>, <PERSON><PERSON></a>, French explorer (d. 1687)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Ren%C3%A9-<PERSON>,_<PERSON><PERSON>_<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>, <PERSON><PERSON>\"><PERSON><PERSON><PERSON>, <PERSON><PERSON></a>, French explorer (d. 1687)", "links": [{"title": "<PERSON><PERSON><PERSON>, <PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Ren%C3%A9-<PERSON>,_<PERSON><PERSON>_<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1690", "text": "<PERSON>, French composer (d. 1760)", "html": "1690 - <a href=\"https://wikipedia.org/wiki/Fran%C3%A<PERSON><PERSON>_<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French composer (d. 1760)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Fran%C3%A<PERSON><PERSON>_<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French composer (d. 1760)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Fran%C3%<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1698", "text": "<PERSON>, <PERSON>, Canadian-American politician, Governor of Louisiana (d. 1778)", "html": "1698 - <a href=\"https://wikipedia.org/wiki/<PERSON>,_Marquis_<PERSON>_<PERSON>-<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>, Marquis <PERSON>\"><PERSON>, <PERSON></a>, Canadian-American politician, <a href=\"https://wikipedia.org/wiki/List_of_colonial_governors_of_Louisiana\" title=\"List of colonial governors of Louisiana\">Governor of Louisiana</a> (d. 1778)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>,_Marquis_<PERSON>_<PERSON>-<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>, <PERSON>\"><PERSON>, <PERSON></a>, Canadian-American politician, <a href=\"https://wikipedia.org/wiki/List_of_colonial_governors_of_Louisiana\" title=\"List of colonial governors of Louisiana\">Governor of Louisiana</a> (d. 1778)", "links": [{"title": "<PERSON>, Marquis <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>,_Marquis_<PERSON>_<PERSON>-<PERSON>"}, {"title": "List of colonial governors of Louisiana", "link": "https://wikipedia.org/wiki/List_of_colonial_governors_of_Louisiana"}]}, {"year": "1710", "text": "<PERSON>, German composer (d. 1784)", "html": "1710 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German composer (d. 1784)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German composer (d. 1784)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1728", "text": "<PERSON>, Grand Duke of Baden (d. 1811)", "html": "1728 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Grand_Duke_of_Baden\" title=\"<PERSON>, Grand Duke of Baden\"><PERSON>, Grand Duke of Baden</a> (d. 1811)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Grand_Duke_of_Baden\" title=\"<PERSON>, Grand Duke of Baden\"><PERSON>, Grand Duke of Baden</a> (d. 1811)", "links": [{"title": "<PERSON>, Grand Duke of Baden", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Grand_Duke_of_Baden"}]}, {"year": "1744", "text": "<PERSON>, American wife of <PERSON>, 2nd First Lady of the United States (d. 1818)", "html": "1744 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American wife of <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, 2nd <a href=\"https://wikipedia.org/wiki/List_of_First_Ladies_of_the_United_States\" class=\"mw-redirect\" title=\"List of First Ladies of the United States\">First Lady of the United States</a> (d. 1818)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American wife of <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, 2nd <a href=\"https://wikipedia.org/wiki/List_of_First_Ladies_of_the_United_States\" class=\"mw-redirect\" title=\"List of First Ladies of the United States\">First Lady of the United States</a> (d. 1818)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "List of First Ladies of the United States", "link": "https://wikipedia.org/wiki/List_of_First_Ladies_of_the_United_States"}]}, {"year": "1780", "text": "<PERSON><PERSON>, German composer (d. 1849)", "html": "1780 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, German composer (d. 1849)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, German composer (d. 1849)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1780", "text": "<PERSON>, Honduran journalist, lawyer, and politician, Foreign Minister of Mexico (d. 1834)", "html": "1780 - <a href=\"https://wikipedia.org/wiki/Jos%C3%A9_<PERSON>io_del_Valle\" title=\"<PERSON> Valle\"><PERSON></a>, Honduran journalist, lawyer, and politician, <a href=\"https://wikipedia.org/wiki/Secretariat_of_Foreign_Affairs_(Mexico)\" title=\"Secretariat of Foreign Affairs (Mexico)\">Foreign Minister of Mexico</a> (d. 1834)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Jos%C3%A9_<PERSON>io_del_Valle\" title=\"<PERSON> Valle\"><PERSON></a>, Honduran journalist, lawyer, and politician, <a href=\"https://wikipedia.org/wiki/Secretariat_of_Foreign_Affairs_(Mexico)\" title=\"Secretariat of Foreign Affairs (Mexico)\">Foreign Minister of Mexico</a> (d. 1834)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Jos%C3%A9_<PERSON><PERSON>_del_Valle"}, {"title": "Secretariat of Foreign Affairs (Mexico)", "link": "https://wikipedia.org/wiki/Secretariat_of_Foreign_Affairs_(Mexico)"}]}, {"year": "1787", "text": "<PERSON><PERSON>, Danish linguist, philologist, and scholar (d. 1832)", "html": "1787 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"Rasmus Rask\"><PERSON><PERSON></a>, Danish linguist, philologist, and scholar (d. 1832)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"Rasmus Rask\"><PERSON><PERSON></a>, Danish linguist, philologist, and scholar (d. 1832)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1808", "text": "<PERSON>, English businessman, founded Thomas Cook Group (d. 1892)", "html": "1808 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English businessman, founded <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_Group\" title=\"Thomas Cook Group\">Thomas Cook Group</a> (d. 1892)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English businessman, founded <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_Group\" title=\"Thomas Cook Group\">Thomas Cook Group</a> (d. 1892)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Thomas <PERSON> Group", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1819", "text": "<PERSON>, English novelist and poet (d. 1880)", "html": "1819 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English novelist and poet (d. 1880)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English novelist and poet (d. 1880)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1820", "text": "<PERSON>, Irish supercentenarian (d. 1932)", "html": "1820 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Irish supercentenarian (d. 1932)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Irish supercentenarian (d. 1932)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1849", "text": "<PERSON>, German painter and printmaker (d. 1938)", "html": "1849 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German painter and printmaker (d. 1938)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German painter and printmaker (d. 1938)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1852", "text": "<PERSON><PERSON><PERSON><PERSON><PERSON>, French politician and diplomat, Nobel Prize laureate (d. 1924)", "html": "1852 - <a href=\"https://wikipedia.org/wiki/<PERSON>_d%27Estour<PERSON><PERSON>_de_<PERSON>stant\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON>'Estournelles de Constant\"><PERSON><PERSON><PERSON>urn<PERSON> Constant</a>, French politician and diplomat, <a href=\"https://wikipedia.org/wiki/Nobel_Peace_Prize\" title=\"Nobel Peace Prize\">Nobel Prize</a> laureate (d. 1924)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_d%27Estour<PERSON><PERSON>_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON>Estournelles de Constant\"><PERSON><PERSON>urn<PERSON> Constant</a>, French politician and diplomat, <a href=\"https://wikipedia.org/wiki/Nobel_Peace_Prize\" title=\"Nobel Peace Prize\">Nobel Prize</a> laureate (d. 1924)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>%27Estour<PERSON><PERSON>_<PERSON>_<PERSON>"}, {"title": "Nobel Peace Prize", "link": "https://wikipedia.org/wiki/Nobel_Peace_Prize"}]}, {"year": "1857", "text": "<PERSON>, English novelist (d. 1903)", "html": "1857 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English novelist (d. 1903)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English novelist (d. 1903)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1859", "text": "<PERSON>, English folk song scholar (d. 1924)", "html": "1859 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English folk song scholar (d. 1924)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English folk song scholar (d. 1924)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1861", "text": "Ranavalona III of Madagascar (d. 1917)", "html": "1861 - <a href=\"https://wikipedia.org/wiki/Ranavalona_III\" title=\"Ranavalona III\">Ranavalona III</a> of Madagascar (d. 1917)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Ranavalona_III\" title=\"Ranavalona III\">Ranavalona III</a> of Madagascar (d. 1917)", "links": [{"title": "Ranavalona III", "link": "https://wikipedia.org/wiki/Ranavalona_III"}]}, {"year": "1861", "text": "<PERSON>, American sculptor (d. 1944)", "html": "1861 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American sculptor (d. 1944)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American sculptor (d. 1944)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1868", "text": "<PERSON>, American politician, 32nd Vice President of the United States (d. 1967)", "html": "1868 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American politician, 32nd <a href=\"https://wikipedia.org/wiki/Vice_President_of_the_United_States\" title=\"Vice President of the United States\">Vice President of the United States</a> (d. 1967)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American politician, 32nd <a href=\"https://wikipedia.org/wiki/Vice_President_of_the_United_States\" title=\"Vice President of the United States\">Vice President of the United States</a> (d. 1967)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "Vice President of the United States", "link": "https://wikipedia.org/wiki/Vice_President_of_the_United_States"}]}, {"year": "1869", "text": "<PERSON>, French novelist, essayist, and dramatist, Nobel Prize laureate (d. 1951)", "html": "1869 - <a href=\"https://wikipedia.org/wiki/Andr%C3%A9_Gide\" title=\"<PERSON>\"><PERSON></a>, French novelist, essayist, and dramatist, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Literature\" title=\"Nobel Prize in Literature\">Nobel Prize</a> laureate (d. 1951)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Andr%C3%A9_Gide\" title=\"<PERSON>\"><PERSON></a>, French novelist, essayist, and dramatist, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Literature\" title=\"Nobel Prize in Literature\">Nobel Prize</a> laureate (d. 1951)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Andr%C3%A9_Gide"}, {"title": "Nobel Prize in Literature", "link": "https://wikipedia.org/wiki/Nobel_Prize_in_Literature"}]}, {"year": "1870", "text": "<PERSON>, American composer (d. 1951)", "html": "1870 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American composer (d. 1951)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American composer (d. 1951)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1870", "text": "<PERSON>, Australian cricketer (d. 1911)", "html": "1870 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>(cricketer)\" title=\"<PERSON> (cricketer)\"><PERSON></a>, Australian cricketer (d. 1911)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>(cricketer)\" title=\"<PERSON> (cricketer)\"><PERSON></a>, Australian cricketer (d. 1911)", "links": [{"title": "<PERSON> (cricketer)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>(cricketer)"}]}, {"year": "1873", "text": "<PERSON>, Indian-English journalist and politician, Secretary of State for the Colonies (d. 1955)", "html": "1873 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Indian-English journalist and politician, <a href=\"https://wikipedia.org/wiki/Secretary_of_State_for_the_Colonies\" title=\"Secretary of State for the Colonies\">Secretary of State for the Colonies</a> (d. 1955)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Indian-English journalist and politician, <a href=\"https://wikipedia.org/wiki/Secretary_of_State_for_the_Colonies\" title=\"Secretary of State for the Colonies\">Secretary of State for the Colonies</a> (d. 1955)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_Amery"}, {"title": "Secretary of State for the Colonies", "link": "https://wikipedia.org/wiki/Secretary_of_State_for_the_Colonies"}]}, {"year": "1873", "text": "<PERSON>, English cricketer (d. 1930)", "html": "1873 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English cricketer (d. 1930)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English cricketer (d. 1930)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1876", "text": "<PERSON>, American gymnast and triathlete (d. 1934)", "html": "1876 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American gymnast and triathlete (d. 1934)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American gymnast and triathlete (d. 1934)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1877", "text": "<PERSON><PERSON>, Hungarian journalist and poet (d. 1919)", "html": "1877 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Hungarian journalist and poet (d. 1919)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Hungarian journalist and poet (d. 1919)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>y"}]}, {"year": "1877", "text": "<PERSON>, Swiss-Spanish footballer, founded FC Barcelona (d. 1930)", "html": "1877 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swiss-Spanish footballer, founded <a href=\"https://wikipedia.org/wiki/FC_Barcelona\" title=\"FC Barcelona\">FC Barcelona</a> (d. 1930)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swiss-Spanish footballer, founded <a href=\"https://wikipedia.org/wiki/FC_Barcelona\" title=\"FC Barcelona\">FC Barcelona</a> (d. 1930)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "FC Barcelona", "link": "https://wikipedia.org/wiki/FC_Barcelona"}]}, {"year": "1881", "text": "<PERSON><PERSON>, Ottoman general and politician (d. 1922)", "html": "1881 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Ottoman general and politician (d. 1922)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Ottoman general and politician (d. 1922)", "links": [{"title": "<PERSON><PERSON> Pasha", "link": "https://wikipedia.org/wiki/En<PERSON>_<PERSON>"}]}, {"year": "1884", "text": "<PERSON><PERSON> <PERSON><PERSON> \"<PERSON>\" <PERSON>, Australian entrepreneur (d. 1926)", "html": "1884 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON><PERSON> <PERSON><PERSON> \"<PERSON>\" <PERSON></a>, Australian entrepreneur (d. 1926)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON><PERSON> <PERSON><PERSON> \"<PERSON>\" <PERSON></a>, Australian entrepreneur (d. 1926)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1890", "text": "<PERSON>, French general and politician, President of France (d. 1970)", "html": "1890 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French general and politician, <a href=\"https://wikipedia.org/wiki/List_of_presidents_of_France\" title=\"List of presidents of France\">President of France</a> (d. 1970)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French general and politician, <a href=\"https://wikipedia.org/wiki/List_of_presidents_of_France\" title=\"List of presidents of France\">President of France</a> (d. 1970)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "List of presidents of France", "link": "https://wikipedia.org/wiki/List_of_presidents_of_France"}]}, {"year": "1890", "text": " <PERSON>, British politician and trade unionist, General Secretary of the Communist Party of Great Britain (d. 1960)", "html": "1890 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, British politician and trade unionist, <a href=\"https://wikipedia.org/wiki/Communist_Party_of_Great_Britain#General_secretaries\" title=\"Communist Party of Great Britain\">General Secretary of the Communist Party of Great Britain</a> (d. 1960)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, British politician and trade unionist, <a href=\"https://wikipedia.org/wiki/Communist_Party_of_Great_Britain#General_secretaries\" title=\"Communist Party of Great Britain\">General Secretary of the Communist Party of Great Britain</a> (d. 1960)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Communist Party of Great Britain", "link": "https://wikipedia.org/wiki/Communist_Party_of_Great_Britain#General_secretaries"}]}, {"year": "1891", "text": "<PERSON>, American publicist (d. 1995)", "html": "1891 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American publicist (d. 1995)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American publicist (d. 1995)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1893", "text": "<PERSON>, American industrial designer (d. 1969)", "html": "1893 - <a href=\"https://wikipedia.org/wiki/<PERSON>_Earl\" title=\"<PERSON> Earl\"><PERSON></a>, American industrial designer (d. 1969)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_Earl\" title=\"Harley Earl\"><PERSON></a>, American industrial designer (d. 1969)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1893", "text": "<PERSON><PERSON>, Soviet politician (d. 1991)", "html": "1893 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Soviet politician (d. 1991)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Soviet politician (d. 1991)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1898", "text": "<PERSON>, American pilot (d. 1935)", "html": "1898 - <a href=\"https://wikipedia.org/wiki/Wiley_Post\" title=\"Wiley Post\">Wiley Post</a>, American pilot (d. 1935)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Wiley_Post\" title=\"Wiley Post\"><PERSON></a>, American pilot (d. 1935)", "links": [{"title": "Wiley Post", "link": "https://wikipedia.org/wiki/Wiley_Post"}]}, {"year": "1899", "text": "<PERSON><PERSON><PERSON>, American singer-songwriter, pianist, and actor (d. 1981)", "html": "1899 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American singer-songwriter, pianist, and actor (d. 1981)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American singer-songwriter, pianist, and actor (d. 1981)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1901", "text": "<PERSON><PERSON><PERSON>, Hungarian-Austrian philosopher from the Vienna Circle (d. 1971)", "html": "1901 - <a href=\"https://wikipedia.org/wiki/B%C3%A9<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Hungarian-Austrian philosopher from the Vienna Circle (d. 1971)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/B%C3%A9<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Hungarian-Austrian philosopher from the Vienna Circle (d. 1971)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/B%C3%A9la_Juhos"}]}, {"year": "1901", "text": "<PERSON><PERSON><PERSON><PERSON>, Spanish pianist and composer (d. 1999)", "html": "1901 - <a href=\"https://wikipedia.org/wiki/Joaqu%C3%AD<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Spanish pianist and composer (d. 1999)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Joaqu%C3%AD<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Spanish pianist and composer (d. 1999)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Joaqu%C3%ADn_Rodrigo"}]}, {"year": "1902", "text": "<PERSON>, French general (d. 1947)", "html": "1902 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French general (d. 1947)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French general (d. 1947)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1902", "text": "<PERSON>, Austrian-American cellist (d. 1942)", "html": "1902 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Austrian-American cellist (d. 1942)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Austrian-American cellist (d. 1942)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1904", "text": "<PERSON>, Mexican painter and illustrator (d. 1957)", "html": "1904 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Mexican painter and illustrator (d. 1957)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Mexican painter and illustrator (d. 1957)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1904", "text": "<PERSON>, French physicist and academic, Nobel Prize laureate (d. 2000)", "html": "1904 - <a href=\"https://wikipedia.org/wiki/Louis_N%C3%A9el\" title=\"<PERSON>\"><PERSON></a>, French physicist and academic, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Physics\" title=\"Nobel Prize in Physics\">Nobel Prize</a> laureate (d. 2000)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Louis_N%C3%A9el\" title=\"<PERSON>\"><PERSON></a>, French physicist and academic, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Physics\" title=\"Nobel Prize in Physics\">Nobel Prize</a> laureate (d. 2000)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Louis_N%C3%A9el"}, {"title": "Nobel Prize in Physics", "link": "https://wikipedia.org/wiki/Nobel_Prize_in_Physics"}]}, {"year": "1904", "text": "<PERSON><PERSON>, Japanese author (d. 2005)", "html": "1904 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"Fu<PERSON>wa\"><PERSON><PERSON></a>, Japanese author (d. 2005)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"Fu<PERSON>\"><PERSON><PERSON></a>, Japanese author (d. 2005)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>wa"}]}, {"year": "1906", "text": "<PERSON><PERSON><PERSON>, Norwegian football player and journalist (d. 1983)", "html": "1906 - <a href=\"https://wikipedia.org/wiki/J%C3%B8<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Norwegian football player and journalist (d. 1983)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/J%C3%B8<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Norwegian football player and journalist (d. 1983)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/J%C3%B8<PERSON>_<PERSON>ve"}]}, {"year": "1910", "text": "<PERSON>, American actress (d. 2005)", "html": "1910 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(actress)\" title=\"<PERSON> (actress)\"><PERSON></a>, American actress (d. 2005)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(actress)\" title=\"<PERSON> (actress)\"><PERSON></a>, American actress (d. 2005)", "links": [{"title": "<PERSON> (actress)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(actress)"}]}, {"year": "1911", "text": "<PERSON>, American golfer (d. 1987)", "html": "1911 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American golfer (d. 1987)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American golfer (d. 1987)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1912", "text": "<PERSON>, American heiress and philanthropist (d. 1993)", "html": "1912 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American heiress and philanthropist (d. 1993)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American heiress and philanthropist (d. 1993)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1913", "text": "<PERSON>, English pianist, composer, and conductor (d. 1976)", "html": "1913 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English pianist, composer, and conductor (d. 1976)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English pianist, composer, and conductor (d. 1976)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1913", "text": "<PERSON><PERSON><PERSON>, American tennis player (d. 2016)", "html": "1913 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American tennis player (d. 2016)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American tennis player (d. 2016)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>y"}]}, {"year": "1914", "text": "<PERSON>, British captain and pilot (d. 1995)", "html": "1914 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(RAF_officer)\" title=\"<PERSON> (RAF officer)\"><PERSON></a>, British captain and pilot (d. 1995)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(RAF_officer)\" title=\"<PERSON> (RAF officer)\"><PERSON></a>, British captain and pilot (d. 1995)", "links": [{"title": "<PERSON> (RAF officer)", "link": "https://wikipedia.org/wiki/<PERSON>(RAF_officer)"}]}, {"year": "1915", "text": "<PERSON>, British cinematographer (d. 2014)", "html": "1915 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, British cinematographer (d. 2014)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, British cinematographer (d. 2014)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1917", "text": "<PERSON>, Australian author and playwright (d. 2010)", "html": "1917 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian author and playwright (d. 2010)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian author and playwright (d. 2010)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1917", "text": "<PERSON>, English physiologist and biophysicist, Nobel Prize laureate (d. 2012)", "html": "1917 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English physiologist and biophysicist, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Physiology_or_Medicine\" title=\"Nobel Prize in Physiology or Medicine\">Nobel Prize</a> laureate (d. 2012)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English physiologist and biophysicist, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Physiology_or_Medicine\" title=\"Nobel Prize in Physiology or Medicine\">Nobel Prize</a> laureate (d. 2012)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}, {"title": "Nobel Prize in Physiology or Medicine", "link": "https://wikipedia.org/wiki/Nobel_Prize_in_Physiology_or_Medicine"}]}, {"year": "1917", "text": "<PERSON>, Australian diplomat (d. 1988)", "html": "1917 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian diplomat (d. 1988)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian diplomat (d. 1988)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1918", "text": "<PERSON><PERSON><PERSON>, American politician (d. 2009)", "html": "1918 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American politician (d. 2009)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American politician (d. 2009)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON><PERSON>"}]}, {"year": "1919", "text": "<PERSON><PERSON><PERSON>, Irish politician (d. 1976)", "html": "1919 - <a href=\"https://wikipedia.org/wiki/M%C3%A1ire_Drumm\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Irish politician (d. 1976)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/M%C3%A1ire_Drumm\" title=\"<PERSON><PERSON><PERSON> Drum<PERSON>\"><PERSON><PERSON><PERSON></a>, Irish politician (d. 1976)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/M%C3%A1ire_Drumm"}]}, {"year": "1920", "text": "<PERSON>, British actress (d. 1956)", "html": "1920 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, British actress (d. 1956)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, British actress (d. 1956)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1920", "text": "<PERSON><PERSON><PERSON><PERSON>, Indian economist (d. 2019)", "html": "1920 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>sra\" title=\"<PERSON><PERSON><PERSON><PERSON> Misra\"><PERSON><PERSON><PERSON><PERSON></a>, Indian economist (d. 2019)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>sra\" title=\"<PERSON><PERSON><PERSON><PERSON> Misra\"><PERSON><PERSON><PERSON><PERSON></a>, Indian economist (d. 2019)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>ra"}]}, {"year": "1921", "text": "<PERSON>, Irish writer and broadcaster (d. 2003)", "html": "1921 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Irish writer and broadcaster (d. 2003)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Irish writer and broadcaster (d. 2003)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1921", "text": "<PERSON>, American comedian, actor, rapper, and screenwriter (d. 2004)", "html": "1921 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American comedian, actor, rapper, and screenwriter (d. 2004)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American comedian, actor, rapper, and screenwriter (d. 2004)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1922", "text": "<PERSON>, American engineer and weapons designer, designed the AR-15 rifle (d. 1997)", "html": "1922 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American engineer and weapons designer, designed the <a href=\"https://wikipedia.org/wiki/AR-15\" class=\"mw-redirect\" title=\"AR-15\">AR-15 rifle</a> (d. 1997)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American engineer and weapons designer, designed the <a href=\"https://wikipedia.org/wiki/AR-15\" class=\"mw-redirect\" title=\"AR-15\">AR-15 rifle</a> (d. 1997)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "AR-15", "link": "https://wikipedia.org/wiki/AR-15"}]}, {"year": "1923", "text": "<PERSON>, Canadian-American director (d. 2016)", "html": "1923 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian-American director (d. 2016)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian-American director (d. 2016)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1923", "text": "<PERSON><PERSON>, American composer and singer (d. 2006)", "html": "1923 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American composer and singer (d. 2006)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American composer and singer (d. 2006)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Dika_Newlin"}]}, {"year": "1924", "text": "<PERSON>, Australian politician (d. 2015)", "html": "1924 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian politician (d. 2015)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian politician (d. 2015)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1924", "text": "<PERSON><PERSON>, American actress and singer (d. 1987)", "html": "1924 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American actress and singer (d. 1987)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American actress and singer (d. 1987)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1925", "text": "<PERSON><PERSON>, American pilot (d. 2014)", "html": "1925 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American pilot (d. 2014)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American pilot (d. 2014)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1925", "text": "<PERSON><PERSON>, American horn player, composer, and conductor (d. 2015)", "html": "1925 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American horn player, composer, and conductor (d. 2015)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American horn player, composer, and conductor (d. 2015)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1926", "text": "<PERSON><PERSON>, American baseball player and coach (d. 2007)", "html": "1926 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>det<PERSON>\"><PERSON><PERSON></a>, American baseball player and coach (d. 2007)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American baseball player and coach (d. 2007)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON><PERSON>"}]}, {"year": "1927", "text": "<PERSON>, American academic administrator (d. 2013)", "html": "1927 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American academic administrator (d. 2013)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American academic administrator (d. 2013)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1928", "text": "<PERSON>, English priest and politician (d. 2008)", "html": "1928 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English priest and politician (d. 2008)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English priest and politician (d. 2008)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1928", "text": "<PERSON>, American basketball player (d. 2018)", "html": "1928 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player (d. 2018)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player (d. 2018)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1929", "text": "<PERSON><PERSON><PERSON>, American lawyer, historian, author, and activist (d. 2022)", "html": "1929 - <a href=\"https://wikipedia.org/wiki/Staughton_Lynd\" title=\"Staughton Lynd\"><PERSON><PERSON><PERSON> Lynd</a>, American lawyer, historian, author, and activist (d. 2022)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Staughton_Lynd\" title=\"Staughton Lynd\"><PERSON><PERSON><PERSON>ynd</a>, American lawyer, historian, author, and activist (d. 2022)", "links": [{"title": "Staughton Lynd", "link": "https://wikipedia.org/wiki/Staugh<PERSON>_Lynd"}]}, {"year": "1930", "text": "<PERSON>, English director (d. 2017)", "html": "1930 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(director)\" title=\"<PERSON> (director)\"><PERSON></a>, English director (d. 2017)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(director)\" title=\"<PERSON> (director)\"><PERSON></a>, English director (d. 2017)", "links": [{"title": "<PERSON> (director)", "link": "https://wikipedia.org/wiki/<PERSON>_(director)"}]}, {"year": "1930", "text": "<PERSON>, English organist and composer (d. 2019)", "html": "1930 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English organist and composer (d. 2019)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English organist and composer (d. 2019)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1932", "text": "<PERSON>, American actor and director (d. 2016)", "html": "1932 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor and director (d. 2016)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor and director (d. 2016)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1933", "text": "<PERSON><PERSON><PERSON>, Australian Olympic athlete (d. 2016)", "html": "1933 - <a href=\"https://wikipedia.org/wiki/Merv_<PERSON>\" title=\"Merv <PERSON>\"><PERSON><PERSON><PERSON> <PERSON></a>, Australian Olympic athlete (d. 2016)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Merv_<PERSON>\" title=\"Merv <PERSON>\"><PERSON><PERSON><PERSON></a>, Australian Olympic athlete (d. 2016)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Merv_<PERSON>"}]}, {"year": "1934", "text": "<PERSON>, Greek singer (d. 1999)", "html": "1934 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Greek singer (d. 1999)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Greek singer (d. 1999)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>u"}]}, {"year": "1935", "text": "<PERSON><PERSON><PERSON><PERSON>, Soviet ice skater (d. 2017)", "html": "1935 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Soviet ice skater (d. 2017)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Soviet ice skater (d. 2017)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1936", "text": "<PERSON>, English actor, writer and satirist (d. 2022)", "html": "1936 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(actor)\" title=\"<PERSON> (actor)\"><PERSON></a>, English actor, writer and satirist (d. 2022)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(actor)\" title=\"<PERSON> (actor)\"><PERSON></a>, English actor, writer and satirist (d. 2022)", "links": [{"title": "<PERSON> (actor)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(actor)"}]}, {"year": "1937", "text": "<PERSON><PERSON>, Polish pilot and military officer", "html": "1937 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Polish pilot and military officer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Polish pilot and military officer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1937", "text": "<PERSON>, Soviet pianist and composer (d. 2020)", "html": "1937 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Soviet pianist and composer (d. 2020)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Soviet pianist and composer (d. 2020)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1938", "text": "<PERSON>, American convicted murderer (d. 2010)", "html": "1938 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A8<PERSON>_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, American convicted murderer (d. 2010)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A8<PERSON>_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, American convicted murderer (d. 2010)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/John_Eleuth%C3%A8<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1939", "text": "<PERSON>, American technologist (d. 2011)", "html": "1939 - <a href=\"https://wikipedia.org/wiki/Tom_West\" title=\"Tom West\"><PERSON></a>, American technologist (d. 2011)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Tom_West\" title=\"Tom West\"><PERSON></a>, American technologist (d. 2011)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Tom_<PERSON>"}]}, {"year": "1939", "text": "<PERSON><PERSON><PERSON>, Indian politician, Indian Minister of Defence (d. 2022)", "html": "1939 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>dav\" title=\"<PERSON><PERSON><PERSON> Yadav\"><PERSON><PERSON><PERSON>v</a>, Indian politician, <a href=\"https://wikipedia.org/wiki/Minister_of_Defence_(India)\" title=\"Minister of Defence (India)\">Indian Minister of Defence</a> (d. 2022)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>dav\" title=\"<PERSON><PERSON><PERSON> Yadav\"><PERSON><PERSON><PERSON>dav</a>, Indian politician, <a href=\"https://wikipedia.org/wiki/Minister_of_Defence_(India)\" title=\"Minister of Defence (India)\">Indian Minister of Defence</a> (d. 2022)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_Yadav"}, {"title": "Minister of Defence (India)", "link": "https://wikipedia.org/wiki/Minister_of_Defence_(India)"}]}, {"year": "1940", "text": "<PERSON>, American-English actor, director, animator, and screenwriter", "html": "1940 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American-English actor, director, animator, and screenwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American-English actor, director, animator, and screenwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1940", "text": "<PERSON>, American author", "html": "1940 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1940", "text": "<PERSON><PERSON><PERSON>, Polish director and screenwriter (d. 2016)", "html": "1940 - <a href=\"https://wikipedia.org/wiki/And<PERSON><PERSON>_%C5%BBu%C5%82awski\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Polish director and screenwriter (d. 2016)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/And<PERSON><PERSON>_%C5%BBu%C5%82awski\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Polish director and screenwriter (d. 2016)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/And<PERSON><PERSON>_%C5%BBu%C5%82<PERSON>ki"}]}, {"year": "1941", "text": "<PERSON>, Scottish actor and director", "html": "1941 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish actor and director", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish actor and director", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1941", "text": "<PERSON>, Canadian ice hockey player and coach", "html": "1941 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A8re\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A8re\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player and coach", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A8re"}]}, {"year": "1941", "text": "<PERSON>, American singer-songwriter (d. 1996)", "html": "1941 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter (d. 1996)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter (d. 1996)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1941", "text": "<PERSON>, American singer-songwriter and bass player", "html": "1941 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and bass player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and bass player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1942", "text": "<PERSON><PERSON><PERSON>, American astronaut", "html": "1942 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American astronaut", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American astronaut", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/G<PERSON><PERSON>_<PERSON>"}]}, {"year": "1942", "text": "<PERSON>, Canadian drummer (d. 2023)", "html": "1942 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>nee<PERSON>\" title=\"<PERSON>need\"><PERSON></a>, Canadian drummer (d. 2023)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>nee<PERSON>\" title=\"<PERSON>need\"><PERSON></a>, Canadian drummer (d. 2023)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>need"}]}, {"year": "1943", "text": "<PERSON><PERSON>, Canadian ice hockey player", "html": "1943 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Canadian ice hockey player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Canadian ice hockey player", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1943", "text": "<PERSON>, American tennis player", "html": "1943 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"Billie <PERSON> King\"><PERSON></a>, American tennis player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"Billie Jean King\"><PERSON></a>, American tennis player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1943", "text": "<PERSON><PERSON><PERSON><PERSON>, Pakistani cricketer", "html": "1943 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Pakistani cricketer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Pakistani cricketer", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1945", "text": "<PERSON>, American director and producer, founded Buzzco Associates (d. 2012)", "html": "1945 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Po<PERSON>kin\"><PERSON></a>, American director and producer, founded <a href=\"https://wikipedia.org/wiki/Buzzco_Associates\" title=\"Buzzco Associates\">Buzzco Associates</a> (d. 2012)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Po<PERSON>kin\"><PERSON></a>, American director and producer, founded <a href=\"https://wikipedia.org/wiki/Buzzco_Associates\" title=\"Buzzco Associates\">Buzzco Associates</a> (d. 2012)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Buzzco Associates", "link": "https://wikipedia.org/wiki/Buzzco_Associates"}]}, {"year": "1945  - <PERSON><PERSON>, Finnish singer (d. 2010)[132]", "text": null, "html": "1945  - <PERSON><PERSON>, Finnish singer (d. 2010)[132] - 1945  - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Finnish singer (d. 2010)", "no_year_html": "1945  - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Finnish singer (d. 2010)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1947", "text": "<PERSON>, American baseball executive", "html": "1947 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball executive", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball executive", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1947", "text": "<PERSON>, English guitarist and songwriter (d. 2005)", "html": "1947 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Price\"><PERSON></a>, English guitarist and songwriter (d. 2005)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_Price\" title=\"<PERSON> Price\"><PERSON></a>, English guitarist and songwriter (d. 2005)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_Price"}]}, {"year": "1947", "text": "<PERSON><PERSON><PERSON>, Italian footballer and manager", "html": "1947 - <a href=\"https://wikipedia.org/wiki/Nev<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Italian footballer and manager", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Italian footballer and manager", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Nevio_<PERSON>ala"}]}, {"year": "1947", "text": "<PERSON>, American race car driver (d. 2012)", "html": "1947 - <a href=\"https://wikipedia.org/wiki/<PERSON>_Walt<PERSON>\" title=\"Salt Walther\"><PERSON></a>, American race car driver (d. 2012)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_Walther\" title=\"Salt Walther\"><PERSON></a>, American race car driver (d. 2012)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Salt_Walther"}]}, {"year": "1947", "text": "<PERSON>, American journalist and author", "html": "1947 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American journalist and author", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American journalist and author", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1948", "text": "<PERSON><PERSON><PERSON>, Serbian footballer and manager (d. 2020)", "html": "1948 - <a href=\"https://wikipedia.org/wiki/Radomir_Anti%C4%87\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Serbian footballer and manager (d. 2020)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Radomir_Anti%C4%87\" title=\"Ra<PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Serbian footballer and manager (d. 2020)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Radomir_Anti%C4%87"}]}, {"year": "1948", "text": "<PERSON><PERSON><PERSON>, Indian dance choreographer, known as \"The Mother of Dance/Choreography in India\" (d. 2020)", "html": "1948 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Indian dance choreographer, known as \"The Mother of Dance/Choreography in India\" (d. 2020)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Indian dance choreographer, known as \"The Mother of Dance/Choreography in India\" (d. 2020)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1948", "text": "<PERSON>, English photographer (d. 2021)", "html": "1948 - <a href=\"https://wikipedia.org/wiki/Mick_Rock\" title=\"Mick Rock\"><PERSON></a>, English photographer (d. 2021)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Mick_Rock\" title=\"Mick Rock\"><PERSON></a>, English photographer (d. 2021)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1949", "text": "<PERSON>, American physician and politician, Surgeon General of the United States", "html": "1949 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American physician and politician, <a href=\"https://wikipedia.org/wiki/Surgeon_General_of_the_United_States\" title=\"Surgeon General of the United States\">Surgeon General of the United States</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American physician and politician, <a href=\"https://wikipedia.org/wiki/Surgeon_General_of_the_United_States\" title=\"Surgeon General of the United States\">Surgeon General of the United States</a>", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}, {"title": "Surgeon General of the United States", "link": "https://wikipedia.org/wiki/Surgeon_General_of_the_United_States"}]}, {"year": "1950", "text": "<PERSON><PERSON>, American baseball player (d. 1978)", "html": "1950 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American baseball player (d. 1978)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American baseball player (d. 1978)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>stock"}]}, {"year": "1950", "text": "<PERSON>, Scottish footballer and manager", "html": "1950 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(footballer)\" title=\"<PERSON> (footballer)\"><PERSON></a>, Scottish footballer and manager", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(footballer)\" title=\"<PERSON> (footballer)\"><PERSON></a>, Scottish footballer and manager", "links": [{"title": "<PERSON> (footballer)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(footballer)"}]}, {"year": "1950", "text": "<PERSON>, American singer-songwriter, guitarist, producer, and actor", "html": "1950 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter, guitarist, producer, and actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter, guitarist, producer, and actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1950", "text": "<PERSON>, American singer-songwriter and bass player", "html": "1950 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and bass player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and bass player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1951", "text": "<PERSON>, American conductor", "html": "1951 - <a href=\"https://wikipedia.org/wiki/Kent_Nagano\" title=\"<PERSON>\"><PERSON></a>, American conductor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Kent_<PERSON>gano\" title=\"<PERSON>\"><PERSON></a>, American conductor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Kent_Nagano"}]}, {"year": "1953", "text": "<PERSON>, English cricketer", "html": "1953 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English cricketer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English cricketer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1954", "text": "<PERSON>, Cameroonian journalist at the head of the Africa management of TV5 Monde", "html": "1954 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A9\" title=\"<PERSON>\"><PERSON></a>, Cameroonian journalist at the head of the Africa management of <a href=\"https://wikipedia.org/wiki/TV5Monde\" title=\"TV5Monde\">TV5 Monde</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A9\" title=\"<PERSON>\"><PERSON></a>, Cameroonian journalist at the head of the Africa management of <a href=\"https://wikipedia.org/wiki/TV5Monde\" title=\"TV5Monde\">TV5 Monde</a>", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_E<PERSON>%C3%A9"}, {"title": "TV5Monde", "link": "https://wikipedia.org/wiki/TV5Monde"}]}, {"year": "1954", "text": "<PERSON>, Italian politician, Prime Minister of Italy", "html": "1954 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian politician, <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Italy\" title=\"Prime Minister of Italy\">Prime Minister of Italy</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian politician, <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Italy\" title=\"Prime Minister of Italy\">Prime Minister of Italy</a>", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Prime Minister of Italy", "link": "https://wikipedia.org/wiki/Prime_Minister_of_Italy"}]}, {"year": "1954", "text": "<PERSON>, Australian sports shooter", "html": "1954 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian sports shooter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian sports shooter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1955", "text": "<PERSON>, British journalist (d. 2023)", "html": "1955 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, British journalist (d. 2023)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, British journalist (d. 2023)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1955", "text": "<PERSON>, American basketball player", "html": "1955 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(basketball)\" title=\"<PERSON> (basketball)\"><PERSON></a>, American basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(basketball)\" title=\"<PERSON> (basketball)\"><PERSON></a>, American basketball player", "links": [{"title": "<PERSON> (basketball)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(basketball)"}]}, {"year": "1956", "text": "<PERSON>, Scottish-Canadian singer-songwriter and keyboard player", "html": "1956 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish-Canadian singer-songwriter and keyboard player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish-Canadian singer-songwriter and keyboard player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1956", "text": "<PERSON>, American actor", "html": "1956 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Kind\"><PERSON></a>, American actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Richard Kind\"><PERSON></a>, American actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1957", "text": "<PERSON><PERSON>, American businessman and television host", "html": "1957 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American businessman and television host", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American businessman and television host", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1957", "text": "<PERSON>, American engineer and planetary scientist", "html": "1957 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American engineer and planetary scientist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American engineer and planetary scientist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1958", "text": "<PERSON>, American actress", "html": "1958 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1958", "text": "<PERSON>, American baseball player", "html": "1958 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1958", "text": "<PERSON> of Johor, Sultan of Johor and the 17th and current <PERSON> or the King of Malaysia", "html": "1958 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Johor\" class=\"mw-redirect\" title=\"<PERSON> of Johor\"><PERSON> of Johor</a>, Sultan of Johor and the 17th and current <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a> or the King of Malaysia", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Johor\" class=\"mw-redirect\" title=\"<PERSON> of Johor\"><PERSON> of Johor</a>, Sultan of Johor and the 17th and current <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a> or the King of Malaysia", "links": [{"title": "<PERSON> of Johor", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_Johor"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1958", "text": "<PERSON>, American singer-songwriter and guitarist", "html": "1958 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and guitarist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and guitarist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1959", "text": "<PERSON>, Scottish footballer", "html": "1959 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1959", "text": "<PERSON><PERSON><PERSON>, Colombian cyclist", "html": "1959 - <a href=\"https://wikipedia.org/wiki/<PERSON>abi<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Colombian cyclist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Colombian cyclist", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1960", "text": "<PERSON><PERSON>, French actor, director, and screenwriter", "html": "1960 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, French actor, director, and screenwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, French actor, director, and screenwriter", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Leos_Carax"}]}, {"year": "1961", "text": "<PERSON><PERSON>, American actress", "html": "1961 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American actress", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1961", "text": "<PERSON>, English-Australian pianist and composer", "html": "1961 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English-Australian pianist and composer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English-Australian pianist and composer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1962", "text": "<PERSON><PERSON>, South Korean soprano", "html": "1962 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> <PERSON>\"><PERSON><PERSON></a>, South Korean soprano", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> <PERSON>\"><PERSON><PERSON></a>, South Korean soprano", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1962", "text": "<PERSON>, Russian author", "html": "1962 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian author", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian author", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1963", "text": "<PERSON>, American football player", "html": "1963 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1963", "text": "<PERSON>, English footballer and manager", "html": "1963 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English footballer and manager", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English footballer and manager", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1963", "text": "<PERSON>, Samoan-American football player and coach", "html": "1963 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Samoan-American football player and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Samoan-American football player and coach", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1963", "text": "<PERSON>, American actor, director, producer, and screenwriter", "html": "1963 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor, director, producer, and screenwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor, director, producer, and screenwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1964", "text": "<PERSON><PERSON><PERSON>, Norwegian YouTuber (d. 2021)", "html": "1964 - <a href=\"https://wikipedia.org/wiki/Apetor\" title=\"Apetor\"><PERSON><PERSON><PERSON></a>, Norwegian YouTuber (d. 2021)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Apetor\" title=\"Apetor\"><PERSON><PERSON><PERSON></a>, Norwegian YouTuber (d. 2021)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Apetor"}]}, {"year": "1964", "text": "<PERSON><PERSON>, American basketball player", "html": "1964 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American basketball player", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1964", "text": "<PERSON>, American actor", "html": "1964 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1964", "text": "<PERSON>, English-Australian footballer and sportscaster", "html": "1964 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English-Australian footballer and sportscaster", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English-Australian footballer and sportscaster", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1965", "text": "<PERSON><PERSON>, Estonian chess player", "html": "1965 - <a href=\"https://wikipedia.org/wiki/Valeriya_Gansvind\" title=\"Valeriya Gansvind\"><PERSON><PERSON> Gansvind</a>, Estonian chess player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Valeriya_Gansvind\" title=\"Valeriya Gansvind\"><PERSON><PERSON></a>, Estonian chess player", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Valeriya_Gansvind"}]}, {"year": "1965", "text": "<PERSON>, Russian artist", "html": "1965 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian artist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian artist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1965", "text": "<PERSON><PERSON>, Danish actor", "html": "1965 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Danish actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Danish actor", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>"}]}, {"year": "1966", "text": "<PERSON>, English politician", "html": "1966 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(politician)\" title=\"<PERSON> (politician)\"><PERSON></a>, English politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(politician)\" title=\"<PERSON> (politician)\"><PERSON></a>, English politician", "links": [{"title": "<PERSON> (politician)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(politician)"}]}, {"year": "1966", "text": "<PERSON>, British actor", "html": "1966 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(actor)\" title=\"<PERSON> (actor)\"><PERSON></a>, British actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(actor)\" title=\"<PERSON> (actor)\"><PERSON></a>, British actor", "links": [{"title": "<PERSON> (actor)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(actor)"}]}, {"year": "1966", "text": "<PERSON>, American actor (d. 2021)", "html": "1966 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor (d. 2021)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor (d. 2021)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1967", "text": "<PERSON>, German tennis player", "html": "1967 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German tennis player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German tennis player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1967", "text": "<PERSON>, Australian investment banker", "html": "1967 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(radio_personality)\" title=\"<PERSON> (radio personality)\"><PERSON></a>, Australian investment banker", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(radio_personality)\" title=\"<PERSON> (radio personality)\"><PERSON></a>, Australian investment banker", "links": [{"title": "<PERSON> (radio personality)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(radio_personality)"}]}, {"year": "1967", "text": "<PERSON>, American actor", "html": "1967 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1967", "text": "<PERSON>, Dutch-Belgian speed skater and coach", "html": "1967 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch-Belgian speed skater and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch-Belgian speed skater and coach", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1968", "text": "<PERSON><PERSON><PERSON>, American basketball player and coach (d. 2018)", "html": "1968 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American basketball player and coach (d. 2018)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American basketball player and coach (d. 2018)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1968", "text": "<PERSON><PERSON>, Danish actress", "html": "1968 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>nuds<PERSON>\"><PERSON><PERSON></a>, Danish actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>nudsen\"><PERSON><PERSON></a>, Danish actress", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "1969", "text": "<PERSON>, American basketball player", "html": "1969 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Byron Houston\"><PERSON></a>, American basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Byron Houston\"><PERSON></a>, American basketball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Byron_<PERSON>"}]}, {"year": "1969", "text": "<PERSON><PERSON><PERSON>, Iranian author and illustrator", "html": "1969 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Iranian author and illustrator", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Iranian author and illustrator", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1970", "text": "<PERSON><PERSON>, Sri Lankan cricketer", "html": "1970 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Sri Lankan cricketer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Sri Lankan cricketer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1970", "text": "<PERSON>, American drummer", "html": "1970 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American drummer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American drummer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1970", "text": "<PERSON><PERSON>, English author and screenwriter", "html": "1970 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_Pavlou\" title=\"St<PERSON> Pavlou\"><PERSON><PERSON></a>, English author and screenwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_Pavlou\" title=\"St<PERSON> Pavlou\"><PERSON><PERSON></a>, English author and screenwriter", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1971", "text": "<PERSON><PERSON>, English rower", "html": "1971 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON><PERSON> <PERSON>\"><PERSON><PERSON></a>, English rower", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON><PERSON> <PERSON>\"><PERSON><PERSON></a>, English rower", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1971", "text": "<PERSON><PERSON><PERSON>, Irish-English rugby player", "html": "1971 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_Bracken\" title=\"<PERSON>yra<PERSON> Bracken\"><PERSON><PERSON><PERSON></a>, Irish-English rugby player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_Bracken\" title=\"<PERSON>yra<PERSON> Bracken\"><PERSON><PERSON><PERSON></a>, Irish-English rugby player", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>en"}]}, {"year": "1972", "text": "<PERSON>, French rugby player", "html": "1972 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French rugby player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French rugby player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1972", "text": "<PERSON>, English footballer", "html": "1972 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1972", "text": "<PERSON>, American baseball player", "html": "1972 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1973", "text": "<PERSON>, Australian rugby player", "html": "1973 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(rugby)\" title=\"<PERSON> (rugby)\"><PERSON></a>, Australian rugby player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(rugby)\" title=\"<PERSON> (rugby)\"><PERSON></a>, Australian rugby player", "links": [{"title": "<PERSON> (rugby)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(rugby)"}]}, {"year": "1974", "text": "<PERSON>, American baseball player", "html": "1974 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1974", "text": "<PERSON>, Canadian figure skater and coach", "html": "1974 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian figure skater and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian figure skater and coach", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1976", "text": "<PERSON>, Belgian footballer", "html": "1976 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Belgian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Belgian footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1976", "text": "<PERSON><PERSON>, German footballer and coach", "html": "1976 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, German footballer and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, German footballer and coach", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>ings"}]}, {"year": "1976", "text": "<PERSON>, German boxer", "html": "1976 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German boxer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German boxer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Regina_Hal<PERSON>h"}]}, {"year": "1976", "text": "<PERSON>, Finnish singer-songwriter", "html": "1976 - <a href=\"https://wikipedia.org/wiki/Ville_Valo\" title=\"Ville Valo\"><PERSON></a>, Finnish singer-songwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Ville_Valo\" title=\"Ville Valo\"><PERSON></a>, Finnish singer-songwriter", "links": [{"title": "Ville Valo", "link": "https://wikipedia.org/wiki/Ville_Valo"}]}, {"year": "1977", "text": "<PERSON><PERSON>, Turkish basketball player", "html": "1977 - <a href=\"https://wikipedia.org/wiki/Kerem_G%C3%B6nl%C3%BCm\" title=\"<PERSON><PERSON> Gönlüm\"><PERSON><PERSON></a>, Turkish basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Kerem_G%C3%B6nl%C3%BCm\" title=\"<PERSON><PERSON> Gönlüm\"><PERSON><PERSON></a>, Turkish basketball player", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Kerem_G%C3%B6nl%C3%BCm"}]}, {"year": "1978", "text": "<PERSON>, Australian rugby league player", "html": "1978 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Best\"><PERSON></a>, Australian rugby league player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Best\"><PERSON></a>, Australian rugby league player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1979", "text": "<PERSON>, Italian footballer", "html": "1979 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1980", "text": "<PERSON>, English footballer and coach", "html": "1980 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English footballer and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English footballer and coach", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1980", "text": "<PERSON>, American computer programmer and businessman, founded Napster", "html": "1980 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American computer programmer and businessman, founded <a href=\"https://wikipedia.org/wiki/Napster\" title=\"Naps<PERSON>\"><PERSON><PERSON><PERSON></a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American computer programmer and businessman, founded <a href=\"https://wikipedia.org/wiki/Napster\" title=\"Napster\">Naps<PERSON></a>", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Napster"}]}, {"year": "1980", "text": "<PERSON><PERSON><PERSON>, Russian high jumper", "html": "1980 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Russian high jumper", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Russian high jumper", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1981", "text": "<PERSON><PERSON>, Senegalese basketball player", "html": "1981 - <a href=\"https://wikipedia.org/wiki/Pape_Sow\" title=\"Pape Sow\"><PERSON><PERSON><PERSON></a>, Senegalese basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Pape_Sow\" title=\"Pape Sow\"><PERSON><PERSON><PERSON></a>, Senegalese basketball player", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Pape_Sow"}]}, {"year": "1982", "text": "<PERSON>, Australian cricketer", "html": "1982 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian cricketer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian cricketer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1982", "text": "<PERSON>, American football player", "html": "1982 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1982", "text": "<PERSON><PERSON><PERSON>, Nigerian footballer", "html": "1982 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_(footballer)\" title=\"<PERSON><PERSON><PERSON> (footballer)\"><PERSON><PERSON><PERSON></a>, Nigerian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_(footballer)\" title=\"<PERSON><PERSON><PERSON> (footballer)\"><PERSON><PERSON><PERSON></a>, Nigerian footballer", "links": [{"title": "<PERSON><PERSON><PERSON> (footballer)", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_(footballer)"}]}, {"year": "1983", "text": "<PERSON>, American actor and singer-songwriter", "html": "1983 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor and singer-songwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Hilton\"><PERSON></a>, American actor and singer-songwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1983", "text": "<PERSON>, English footballer", "html": "1983 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1984", "text": "<PERSON>, American actress", "html": "1984 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1985", "text": "<PERSON><PERSON><PERSON>, Ghanaian footballer", "html": "1985 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Ghanaian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Ghanaian footballer", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1985", "text": "<PERSON><PERSON><PERSON><PERSON>, Congolese footballer", "html": "1985 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>rc<PERSON>_Mbokani\" title=\"Dieumerc<PERSON> Mbokani\"><PERSON><PERSON><PERSON><PERSON></a>, Congolese footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_Mbokani\" title=\"<PERSON>umerc<PERSON> Mbokani\"><PERSON><PERSON><PERSON><PERSON></a>, Congolese footballer", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Dieumerci_Mbokani"}]}, {"year": "1985", "text": "<PERSON>, Luxembourgian tennis player", "html": "1985 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Luxembourgian tennis player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Luxembourgian tennis player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1985", "text": "<PERSON>, American baseball player", "html": "1985 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1985", "text": "<PERSON>, English rugby league player", "html": "1985 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English rugby league player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English rugby league player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1986", "text": "<PERSON>, South African sprinter and convicted murderer", "html": "1986 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, South African sprinter and convicted murderer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, South African sprinter and convicted murderer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1987", "text": "<PERSON><PERSON>, Estonian swimmer", "html": "1987 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Estonian swimmer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Estonian swimmer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1987", "text": "<PERSON>, American wrestler", "html": "1987 - <a href=\"https://wikipedia.org/wiki/<PERSON>_(wrestler)\" title=\"<PERSON> (wrestler)\"><PERSON></a>, American wrestler", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(wrestler)\" title=\"<PERSON> (wrestler)\"><PERSON></a>, American wrestler", "links": [{"title": "<PERSON> (wrestler)", "link": "https://wikipedia.org/wiki/<PERSON>(wrestler)"}]}, {"year": "1987", "text": "<PERSON><PERSON><PERSON>, Belgian footballer", "html": "1987 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Belgian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Belgian footballer", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1988", "text": "<PERSON>, English actor, singer, and model", "html": "1988 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actor, singer, and model", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actor, singer, and model", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1988", "text": "<PERSON>, American baseball player", "html": "1988 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Drew_<PERSON>z"}]}, {"year": "1988", "text": "<PERSON>, American baseball player", "html": "1988 - <a href=\"https://wikipedia.org/wiki/Austin_Romine\" title=\"Austin Romine\"><PERSON></a>, American baseball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Austin_Romine\" title=\"Austin Romine\"><PERSON></a>, American baseball player", "links": [{"title": "<PERSON> Romine", "link": "https://wikipedia.org/wiki/Austin_Romine"}]}, {"year": "1989", "text": "<PERSON><PERSON>, American actor", "html": "1989 - <a href=\"https://wikipedia.org/wiki/Al<PERSON>_<PERSON>ich\" title=\"Al<PERSON> Ehrenreich\"><PERSON><PERSON></a>, American actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Al<PERSON>_<PERSON>ich\" title=\"<PERSON><PERSON> Ehrenreich\"><PERSON><PERSON></a>, American actor", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Alden_E<PERSON>reich"}]}, {"year": "1989", "text": "<PERSON><PERSON>, American singer", "html": "1989 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American singer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American singer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1989", "text": "<PERSON>, English footballer", "html": "1989 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1989", "text": "<PERSON>, Romanian footballer", "html": "1989 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Romanian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Romanian footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1990", "text": "<PERSON>, American football player", "html": "1990 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1990", "text": "<PERSON>, South Korean singer and actor", "html": "1990 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>-woo\" title=\"<PERSON>woo\"><PERSON>w<PERSON></a>, South Korean singer and actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>-woo\" title=\"<PERSON>woo\"><PERSON></a>, South Korean singer and actor", "links": [{"title": "<PERSON>woo", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>-woo"}]}, {"year": "1991", "text": "<PERSON><PERSON>, American basketball player", "html": "1991 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American basketball player", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1992", "text": "<PERSON>, Canadian basketball player", "html": "1992 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian basketball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1992", "text": "<PERSON><PERSON>, Spanish footballer", "html": "1992 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Spanish footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Spanish footballer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1992", "text": "<PERSON><PERSON>, Russian ice hockey player", "html": "1992 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Russian ice hockey player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Russian ice hockey player", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>"}]}, {"year": "1994", "text": "<PERSON>, Mexican volleyball player", "html": "1994 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Mexican volleyball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Mexican volleyball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON><PERSON>"}]}, {"year": "1994", "text": "<PERSON><PERSON>, Australian actor", "html": "1994 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Australian actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Australian actor", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1994", "text": "<PERSON><PERSON><PERSON>, Argentine footballer", "html": "1994 - <a href=\"https://wikipedia.org/wiki/Nicol%C3%A1<PERSON>_<PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Argentine footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Nicol%C3%<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Argentine footballer", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Nicol%C3%<PERSON><PERSON>_<PERSON>elli"}]}, {"year": "1994", "text": "<PERSON><PERSON>, Japanese figure skater", "html": "1994 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Japanese figure skater", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Japanese figure skater", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1995", "text": "<PERSON>, American actress", "html": "1995 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1996", "text": "<PERSON>, American model", "html": "1996 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Bieber\"><PERSON></a>, American model", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Bieber\"><PERSON></a>, American model", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1996", "text": "<PERSON>, American actress", "html": "1996 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1996", "text": "<PERSON><PERSON><PERSON><PERSON>, American football player", "html": "1996 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American football player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American football player", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1996", "text": "<PERSON><PERSON><PERSON>, South Korean singer, songwriter, record producer, member of boy band Seventeen", "html": "1996 - <a href=\"https://wikipedia.org/wiki/Woozi\" title=\"Woozi\"><PERSON><PERSON><PERSON></a>, South Korean singer, songwriter, record producer, member of boy band Seventeen", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Woozi\" title=\"Woozi\"><PERSON><PERSON><PERSON></a>, South Korean singer, songwriter, record producer, member of boy band Seventeen", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Woozi"}]}, {"year": "1999", "text": "<PERSON>, American football player", "html": "1999 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1999   -<PERSON>, English footballer[228]", "text": null, "html": "1999   -<PERSON>, English footballer[228] - 1999 -<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English footballer", "no_year_html": "1999 -<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2000", "text": "<PERSON><PERSON><PERSON><PERSON>, American actress and singer", "html": "2000 - <a href=\"https://wikipedia.org/wiki/Auli%CA%BB<PERSON>_C<PERSON>ho\" title=\"<PERSON><PERSON><PERSON><PERSON> Cravalho\"><PERSON><PERSON><PERSON><PERSON></a>, American actress and singer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Auli%CA%BB<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON> Cravalho\"><PERSON><PERSON><PERSON><PERSON></a>, American actress and singer", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON> Cra<PERSON>ho", "link": "https://wikipedia.org/wiki/Auli%CA%BB<PERSON>_<PERSON>"}]}, {"year": "2001", "text": "<PERSON><PERSON>, Chinese singer", "html": "2001 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Chinese singer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Chinese singer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>"}]}, {"year": "2002", "text": "<PERSON>, American basketball player", "html": "2002 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(basketball,_born_2002)\" title=\"<PERSON> (basketball, born 2002)\"><PERSON></a>, American basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(basketball,_born_2002)\" title=\"<PERSON> (basketball, born 2002)\"><PERSON></a>, American basketball player", "links": [{"title": "<PERSON> (basketball, born 2002)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(basketball,_born_2002)"}]}, {"year": "2002", "text": "<PERSON>, Canadian ice hockey player", "html": "2002 - <a href=\"https://wikipedia.org/wiki/Owen_<PERSON>\" title=\"Owen Power\"><PERSON></a>, Canadian ice hockey player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Owen_Power\" title=\"Owen Power\"><PERSON></a>, Canadian ice hockey player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}], "Deaths": [{"year": "365", "text": "Anti<PERSON><PERSON>", "html": "365 - <a href=\"https://wikipedia.org/wiki/Antipope_Felix_II\" title=\"Antipope Felix II\">Antipope Felix II</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Antipope_Felix_II\" title=\"Antipope Felix II\">Antipope Felix II</a>", "links": [{"title": "Anti<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>"}]}, {"year": "950", "text": "<PERSON><PERSON><PERSON> of Italy (b. 926)", "html": "950 - <a href=\"https://wikipedia.org/wiki/Lothair_II_of_Italy\" title=\"Lothair II of Italy\">Lothair II of Italy</a> (b. 926)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Lothair_II_of_Italy\" title=\"Lothair II of Italy\">Lothair II of Italy</a> (b. 926)", "links": [{"title": "Lothair II of Italy", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>r_II_of_Italy"}]}, {"year": "1249", "text": "<PERSON><PERSON><PERSON><PERSON>, ruler of Egypt", "html": "1249 - <a href=\"https://wikipedia.org/wiki/<PERSON>-<PERSON><PERSON>_<PERSON>\" title=\"<PERSON>-<PERSON><PERSON>\"><PERSON>-<PERSON><PERSON></a>, ruler of Egypt", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>-<PERSON><PERSON>_<PERSON>\" title=\"<PERSON>-<PERSON><PERSON> Ayyu<PERSON>\"><PERSON>-<PERSON><PERSON></a>, ruler of Egypt", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>-<PERSON><PERSON>_<PERSON>"}]}, {"year": "1286", "text": "<PERSON> of Denmark (b. 1249)", "html": "1286 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Denmark\" title=\"<PERSON> of Denmark\"><PERSON> of Denmark</a> (b. 1249)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Denmark\" title=\"<PERSON> of Denmark\"><PERSON> of Denmark</a> (b. 1249)", "links": [{"title": "<PERSON> of Denmark", "link": "https://wikipedia.org/wiki/Eric_<PERSON>_of_Denmark"}]}, {"year": "1318", "text": "<PERSON> Tver (b. 1271)", "html": "1318 - <a href=\"https://wikipedia.org/wiki/<PERSON>_of_Tver\" title=\"<PERSON> of Tver\"><PERSON> of Tver</a> (b. 1271)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_of_Tver\" title=\"<PERSON> of Tver\"><PERSON> of Tver</a> (b. 1271)", "links": [{"title": "<PERSON> of Tver", "link": "https://wikipedia.org/wiki/<PERSON>_of_Tver"}]}, {"year": "1538", "text": "<PERSON>, English Protestant martyr", "html": "1538 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(Protestant_martyr)\" class=\"mw-redirect\" title=\"<PERSON> (Protestant martyr)\"><PERSON></a>, English Protestant martyr", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(Protestant_martyr)\" class=\"mw-redirect\" title=\"<PERSON> (Protestant martyr)\"><PERSON></a>, English Protestant martyr", "links": [{"title": "<PERSON> (Protestant martyr)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(Protestant_martyr)"}]}, {"year": "1617", "text": "<PERSON>, Sultan of the Ottoman Empire and <PERSON><PERSON><PERSON> of Islam (b. 1590)", "html": "1617 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON> I</a>, <a href=\"https://wikipedia.org/wiki/Sultan_of_the_Ottoman_Empire\" class=\"mw-redirect\" title=\"Sultan of the Ottoman Empire\">Sultan of the Ottoman Empire</a> and <a href=\"https://wikipedia.org/wiki/Ottoman_Caliphate\" title=\"Ottoman Caliphate\">Cal<PERSON>h of Islam</a> (b. 1590)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON> I</a>, <a href=\"https://wikipedia.org/wiki/Sultan_of_the_Ottoman_Empire\" class=\"mw-redirect\" title=\"Sultan of the Ottoman Empire\">Sultan of the Ottoman Empire</a> and <a href=\"https://wikipedia.org/wiki/Ottoman_Caliphate\" title=\"Ottoman Caliphate\">Caliph of Islam</a> (b. 1590)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Sultan of the Ottoman Empire", "link": "https://wikipedia.org/wiki/Sultan_of_the_Ottoman_Empire"}, {"title": "Ottoman Caliphate", "link": "https://wikipedia.org/wiki/Ottoman_Caliphate"}]}, {"year": "1694", "text": "<PERSON>, English archbishop (b. 1630)", "html": "1694 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English archbishop (b. 1630)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English archbishop (b. 1630)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1697", "text": "<PERSON><PERSON><PERSON><PERSON>, French architect and academic, designed <PERSON> (b. c. 1635)", "html": "1697 - <a href=\"https://wikipedia.org/wiki/Lib%C3%A9ral_Bruant\" title=\"Libéral Bruant\"><PERSON><PERSON><PERSON><PERSON></a>, French architect and academic, designed <a href=\"https://wikipedia.org/wiki/<PERSON>_In<PERSON>ides\" title=\"Les Invalides\"><PERSON></a> (b. c. 1635)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Lib%C3%A9ral_Bruant\" title=\"Libéra<PERSON> Bruant\"><PERSON><PERSON><PERSON><PERSON></a>, French architect and academic, designed <a href=\"https://wikipedia.org/wiki/<PERSON>_In<PERSON>ides\" title=\"Les Invalides\"><PERSON></a> (b. c. 1635)", "links": [{"title": "Libéra<PERSON>", "link": "https://wikipedia.org/wiki/Lib%C3%A9ral_Bruant"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Les_Invalides"}]}, {"year": "1718", "text": "<PERSON><PERSON><PERSON>, English pirate (b. 1680)", "html": "1718 - <a href=\"https://wikipedia.org/wiki/<PERSON>beard\" title=\"<PERSON>beard\"><PERSON><PERSON><PERSON></a>, English pirate (b. 1680)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>beard\" title=\"Blackbeard\"><PERSON><PERSON><PERSON></a>, English pirate (b. 1680)", "links": [{"title": "Blackbeard", "link": "https://wikipedia.org/wiki/Blackbeard"}]}, {"year": "1758", "text": "<PERSON>, 1st Baron <PERSON>, English politician, Lord Lieutenant of Cornwall (b. 1680)", "html": "1758 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_1st_Baron_<PERSON>\" title=\"<PERSON>, 1st Baron <PERSON>\"><PERSON>, 1st Baron <PERSON></a>, English politician, <a href=\"https://wikipedia.org/wiki/Lord_Lieutenant_of_Cornwall\" title=\"Lord Lieutenant of Cornwall\">Lord Lieutenant of Cornwall</a> (b. 1680)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>,_1st_Baron_<PERSON>\" title=\"<PERSON>, 1st Baron <PERSON>\"><PERSON>, 1st Baron <PERSON></a>, English politician, <a href=\"https://wikipedia.org/wiki/Lord_Lieutenant_of_Cornwall\" title=\"Lord Lieutenant of Cornwall\">Lord Lieutenant of Cornwall</a> (b. 1680)", "links": [{"title": "<PERSON>, 1st Baron <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_1st_Baron_<PERSON>"}, {"title": "Lord Lieutenant of Cornwall", "link": "https://wikipedia.org/wiki/Lord_Lieutenant_of_Cornwall"}]}, {"year": "1774", "text": "<PERSON>, English general, politician and first British governor of Bengal (b. 1725)", "html": "1774 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English general, politician and first British governor of Bengal (b. 1725)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English general, politician and first British governor of Bengal (b. 1725)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1813", "text": "<PERSON>, German physician, physiologist, and anatomist (b. 1759)", "html": "1813 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German physician, physiologist, and anatomist (b. 1759)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German physician, physiologist, and anatomist (b. 1759)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1819", "text": "<PERSON>, English botanist (b. 1742)", "html": "1819 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(botanist)\" title=\"<PERSON> (botanist)\"><PERSON></a>, English botanist (b. 1742)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(botanist)\" title=\"<PERSON> (botanist)\"><PERSON></a>, English botanist (b. 1742)", "links": [{"title": "<PERSON> (botanist)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(botanist)"}]}, {"year": "1871", "text": "<PERSON>, African American activist and politician, Lieutenant Governor of Louisiana 1868-1871 (b. 1826)", "html": "1871 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, African American activist and politician, Lieutenant Governor of Louisiana 1868-1871 (b. 1826)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, African American activist and politician, Lieutenant Governor of Louisiana 1868-1871 (b. 1826)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1875", "text": "<PERSON>, American politician, 18th Vice President of the United States (b. 1812)", "html": "1875 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American politician, 18th <a href=\"https://wikipedia.org/wiki/Vice_President_of_the_United_States\" title=\"Vice President of the United States\">Vice President of the United States</a> (b. 1812)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American politician, 18th <a href=\"https://wikipedia.org/wiki/Vice_President_of_the_United_States\" title=\"Vice President of the United States\">Vice President of the United States</a> (b. 1812)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Vice President of the United States", "link": "https://wikipedia.org/wiki/Vice_President_of_the_United_States"}]}, {"year": "1886", "text": "<PERSON>, American author (b. 1823)", "html": "1886 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_Chesnut\" title=\"<PERSON> Boykin Chesnut\"><PERSON></a>, American author (b. 1823)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_Chesnut\" title=\"Mary Boykin Chesnut\"><PERSON>es<PERSON></a>, American author (b. 1823)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1896", "text": "<PERSON> Jr<PERSON>, American engineer, invented the Ferris wheel (b. 1859)", "html": "1896 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_Ferris_Jr.\" title=\"<PERSON> Washington Gale Ferris Jr.\"><PERSON> Ferris Jr.</a>, American engineer, invented the <a href=\"https://wikipedia.org/wiki/Ferris_wheel\" title=\"Ferris wheel\">Ferris wheel</a> (b. 1859)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_Ferris_Jr.\" title=\"<PERSON> Gale Ferris Jr.\"><PERSON> Ferris Jr.</a>, American engineer, invented the <a href=\"https://wikipedia.org/wiki/Ferris_wheel\" title=\"Ferris wheel\">Ferris wheel</a> (b. 1859)", "links": [{"title": "<PERSON> Jr.", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>_<PERSON>."}, {"title": "Ferris wheel", "link": "https://wikipedia.org/wiki/Ferris_wheel"}]}, {"year": "1900", "text": "<PERSON>, English composer (b. 1842)", "html": "1900 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English composer (b. 1842)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English composer (b. 1842)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1902", "text": "<PERSON>, American physician and entomologist (b. 1851)", "html": "1902 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American physician and entomologist (b. 1851)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American physician and entomologist (b. 1851)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1913", "text": "<PERSON>, Japanese shōgun (b. 1837)", "html": "1913 - <a href=\"https://wikipedia.org/wiki/Tokugawa_Yoshinobu\" title=\"Tokugawa Yoshinobu\"><PERSON></a>, Japanese shōgun (b. 1837)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Tokugawa_Yoshinobu\" title=\"Tokugawa Yoshinobu\"><PERSON></a>, Japanese shō<PERSON> (b. 1837)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON><PERSON><PERSON>"}]}, {"year": "1916", "text": "<PERSON>, American novelist and journalist (b. 1876)", "html": "1916 - <a href=\"https://wikipedia.org/wiki/Jack_London\" title=\"Jack London\"><PERSON></a>, American novelist and journalist (b. 1876)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Jack_London\" title=\"Jack London\"><PERSON></a>, American novelist and journalist (b. 1876)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Jack_<PERSON>"}]}, {"year": "1919", "text": "<PERSON>, Argentinian explorer and academic (b. 1852)", "html": "1919 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Argentinian explorer and academic (b. 1852)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Francisco Moreno\"><PERSON></a>, Argentinian explorer and academic (b. 1852)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1921", "text": "<PERSON>, American serial/spree killer and bank robber (b.1887)", "html": "1921 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American serial/spree killer and bank robber (b.1887)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American serial/spree killer and bank robber (b.1887)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1923", "text": "<PERSON> (Irish republican) died on Hunger Strike", "html": "1923 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%27Sullivan_(Irish_republican)\" class=\"mw-redirect\" title=\"<PERSON> (Irish republican)\"><PERSON> (Irish republican)</a> died on Hunger Strike", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%27Sulliva<PERSON>_(Irish_republican)\" class=\"mw-redirect\" title=\"<PERSON> (Irish republican)\"><PERSON> (Irish republican)</a> died on Hunger Strike", "links": [{"title": "<PERSON> (Irish republican)", "link": "https://wikipedia.org/wiki/<PERSON>_O%27Sulliva<PERSON>_(Irish_republican)"}]}, {"year": "1941", "text": "<PERSON>, German colonel and pilot (b. 1913)", "html": "1941 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%B6lders\" title=\"<PERSON>\"><PERSON></a>, German colonel and pilot (b. 1913)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%B6lder<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German colonel and pilot (b. 1913)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Werner_M%C3%B6lders"}]}, {"year": "1943", "text": "<PERSON><PERSON><PERSON>, American composer (b. 1895)", "html": "1943 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American composer (b. 1895)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American composer (b. 1895)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1944", "text": "<PERSON>, English astrophysicist and astronomer (b. 1882)", "html": "1944 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English astrophysicist and astronomer (b. 1882)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English astrophysicist and astronomer (b. 1882)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>dington"}]}, {"year": "1946", "text": "<PERSON>, German jurist and politician, German Minister of Justice (b. 1889)", "html": "1946 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German jurist and politician, <a href=\"https://wikipedia.org/wiki/Reich_Ministry_of_Justice\" title=\"Reich Ministry of Justice\">German Minister of Justice</a> (b. 1889)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German jurist and politician, <a href=\"https://wikipedia.org/wiki/Reich_Ministry_of_Justice\" title=\"Reich Ministry of Justice\">German Minister of Justice</a> (b. 1889)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Reich Ministry of Justice", "link": "https://wikipedia.org/wiki/Reich_Ministry_of_Justice"}]}, {"year": "1955", "text": "<PERSON><PERSON>, American actor and comedian (b. 1895)", "html": "1955 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American actor and comedian (b. 1895)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American actor and comedian (b. 1895)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/She<PERSON>_<PERSON>"}]}, {"year": "1956", "text": "<PERSON>, Russian-American actor, ballet dancer, and choreographer (b. 1882)", "html": "1956 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian-American actor, ballet dancer, and choreographer (b. 1882)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian-American actor, ballet dancer, and choreographer (b. 1882)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1963", "text": "<PERSON><PERSON><PERSON>, English novelist and philosopher (b. 1894)", "html": "1963 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, English novelist and philosopher (b. 1894)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, English novelist and philosopher (b. 1894)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1963", "text": "<PERSON>, American politician, 35th President of the United States (b. 1917)", "html": "1963 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American politician, 35th <a href=\"https://wikipedia.org/wiki/President_of_the_United_States\" title=\"President of the United States\">President of the United States</a> (b. 1917)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American politician, 35th <a href=\"https://wikipedia.org/wiki/President_of_the_United_States\" title=\"President of the United States\">President of the United States</a> (b. 1917)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>"}, {"title": "President of the United States", "link": "https://wikipedia.org/wiki/President_of_the_United_States"}]}, {"year": "1963", "text": "<PERSON><PERSON> <PERSON><PERSON>, British writer, literary scholar, and Anglican lay theologian (b. 1898)", "html": "1963 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"C<PERSON> <PERSON><PERSON>\"><PERSON><PERSON> <PERSON><PERSON></a>, British writer, literary scholar, and Anglican lay theologian (b. 1898)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> <PERSON><PERSON>\"><PERSON><PERSON> <PERSON><PERSON></a>, British writer, literary scholar, and Anglican lay theologian (b. 1898)", "links": [{"title": "<PERSON><PERSON> <PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "1963", "text": "<PERSON><PERSON> <PERSON><PERSON>, American police officer (b. 1924)", "html": "1963 - <a href=\"https://wikipedia.org/wiki/J._<PERSON>._Tippit\" title=\"J. D. Tippit\"><PERSON><PERSON> <PERSON><PERSON></a>, American police officer (b. 1924)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/J._<PERSON>._Tippit\" title=\"J. D. Tippit\"><PERSON><PERSON> <PERSON><PERSON></a>, American police officer (b. 1924)", "links": [{"title": "<PERSON><PERSON> <PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_Tippit"}]}, {"year": "1966", "text": "<PERSON>, English footballer (b. 1882)", "html": "1966 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English footballer (b. 1882)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English footballer (b. 1882)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1966", "text": "<PERSON><PERSON>, French actor (b. 1890)", "html": "1966 - <a href=\"https://wikipedia.org/wiki/%C3%89mile_Drain\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, French actor (b. 1890)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/%C3%89mile_Drain\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, French actor (b. 1890)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/%C3%89mile_Drain"}]}, {"year": "1980", "text": "<PERSON>, Canadian journalist and politician, Governor General of Canada (b. 1913)", "html": "1980 - <a href=\"https://wikipedia.org/wiki/Jules_<PERSON>%C3%A9ger\" title=\"<PERSON>\"><PERSON></a>, Canadian journalist and politician, <a href=\"https://wikipedia.org/wiki/Governor_General_of_Canada\" title=\"Governor General of Canada\">Governor General of Canada</a> (b. 1913)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Jules_<PERSON>%C3%A9ger\" title=\"<PERSON>\"><PERSON></a>, Canadian journalist and politician, <a href=\"https://wikipedia.org/wiki/Governor_General_of_Canada\" title=\"Governor General of Canada\">Governor General of Canada</a> (b. 1913)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Jules_L%C3%A9ger"}, {"title": "Governor General of Canada", "link": "https://wikipedia.org/wiki/Governor_General_of_Canada"}]}, {"year": "1980", "text": "<PERSON><PERSON>, Irish painter and illustrator (b. 1901)", "html": "1980 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Irish painter and illustrator (b. 1901)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Irish painter and illustrator (b. 1901)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1980", "text": "<PERSON>, American stage and film actress (b. 1893)", "html": "1980 - <a href=\"https://wikipedia.org/wiki/Mae_West\" title=\"Mae West\"><PERSON></a>, American stage and film actress (b. 1893)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Mae_West\" title=\"Mae West\"><PERSON></a>, American stage and film actress (b. 1893)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Mae_West"}]}, {"year": "1981", "text": "<PERSON>, German-English physician and biochemist, Nobel Prize laureate (b. 1900)", "html": "1981 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, German-English physician and biochemist, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Physiology_or_Medicine\" title=\"Nobel Prize in Physiology or Medicine\">Nobel Prize</a> laureate (b. 1900)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, German-English physician and biochemist, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Physiology_or_Medicine\" title=\"Nobel Prize in Physiology or Medicine\">Nobel Prize</a> laureate (b. 1900)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Nobel Prize in Physiology or Medicine", "link": "https://wikipedia.org/wiki/Nobel_Prize_in_Physiology_or_Medicine"}]}, {"year": "1986", "text": "<PERSON><PERSON><PERSON>, American actor and comedian (b. 1910)", "html": "1986 - <a href=\"https://wikipedia.org/wiki/Scatman_Crothers\" title=\"Scatman Crothers\"><PERSON><PERSON><PERSON></a>, American actor and comedian (b. 1910)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Scatman_Crothers\" title=\"Scatman Crothers\"><PERSON><PERSON><PERSON></a>, American actor and comedian (b. 1910)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>roth<PERSON>"}]}, {"year": "1988", "text": "<PERSON>, Mexican architect and engineer (b. 1902)", "html": "1988 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A1n\" title=\"<PERSON>\"><PERSON></a>, Mexican architect and engineer (b. 1902)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A1n\" title=\"<PERSON>\"><PERSON></a>, Mexican architect and engineer (b. 1902)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A1n"}]}, {"year": "1989", "text": "<PERSON>, Lebanese lawyer and politician, 13th President of Lebanon (b. 1925)", "html": "1989 - <a href=\"https://wikipedia.org/wiki/Ren%C3%A9_<PERSON>d\" title=\"<PERSON>\"><PERSON></a>, Lebanese lawyer and politician, 13th <a href=\"https://wikipedia.org/wiki/List_of_Presidents_of_Lebanon\" class=\"mw-redirect\" title=\"List of Presidents of Lebanon\">President of Lebanon</a> (b. 1925)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Ren%C3%A9_<PERSON><PERSON>\" title=\"<PERSON>\"><PERSON></a>, Lebanese lawyer and politician, 13th <a href=\"https://wikipedia.org/wiki/List_of_Presidents_of_Lebanon\" class=\"mw-redirect\" title=\"List of Presidents of Lebanon\">President of Lebanon</a> (b. 1925)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Ren%C3%A9_Moawad"}, {"title": "List of Presidents of Lebanon", "link": "https://wikipedia.org/wiki/List_of_Presidents_of_Lebanon"}]}, {"year": "1991", "text": "<PERSON><PERSON>, Japanese director (b. 1912)", "html": "1991 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Japanese director (b. 1912)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Japanese director (b. 1912)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>i"}]}, {"year": "1992", "text": "<PERSON>, American actor (b. 1905)", "html": "1992 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor (b. 1905)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor (b. 1905)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1993", "text": "<PERSON>, English novelist, playwright, and critic (b. 1917)", "html": "1993 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English novelist, playwright, and critic (b. 1917)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English novelist, playwright, and critic (b. 1917)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1993", "text": "<PERSON>, Soviet pianist, composer, and teacher (b. 1924)", "html": "1993 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, Soviet pianist, composer, and teacher (b. 1924)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, Soviet pianist, composer, and teacher (b. 1924)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1996", "text": "<PERSON>, English photographer and director (b. 1936)", "html": "1996 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(photographer)\" title=\"<PERSON> (photographer)\"><PERSON></a>, English photographer and director (b. 1936)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(photographer)\" title=\"<PERSON> (photographer)\"><PERSON></a>, English photographer and director (b. 1936)", "links": [{"title": "<PERSON> (photographer)", "link": "https://wikipedia.org/wiki/<PERSON>(photographer)"}]}, {"year": "1997", "text": "<PERSON>, Australian singer-songwriter (b. 1960)", "html": "1997 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian singer-songwriter (b. 1960)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian singer-songwriter (b. 1960)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1998", "text": "<PERSON><PERSON>, American poker player (b. 1953)", "html": "1998 - <a href=\"https://wikipedia.org/wiki/<PERSON>u_Ungar\" title=\"St<PERSON> Ungar\"><PERSON><PERSON></a>, American poker player (b. 1953)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>u_Ungar\" title=\"St<PERSON> Ungar\"><PERSON><PERSON></a>, American poker player (b. 1953)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Stu_Ungar"}]}, {"year": "2000", "text": "<PERSON>, French actor, director, and screenwriter (b. 1927)", "html": "2000 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Christian <PERSON>\"><PERSON></a>, French actor, director, and screenwriter (b. 1927)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Christian <PERSON>\"><PERSON></a>, French actor, director, and screenwriter (b. 1927)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2001", "text": "<PERSON>, American businesswoman, founded Mary Kay, Inc. (b. 1918)", "html": "2001 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American businesswoman, founded <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\">Mary <PERSON>, Inc.</a> (b. 1918)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American businesswoman, founded <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\">Mary <PERSON>, Inc.</a> (b. 1918)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2001", "text": "<PERSON>, English historian and academic (b. 1923)", "html": "2001 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English historian and academic (b. 1923)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English historian and academic (b. 1923)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2001", "text": "<PERSON>, American record producer, founded Verve Records (b. 1918)", "html": "2001 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American record producer, founded <a href=\"https://wikipedia.org/wiki/Verve_Records\" title=\"Verve Records\">Verve Records</a> (b. 1918)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American record producer, founded <a href=\"https://wikipedia.org/wiki/Verve_Records\" title=\"Verve Records\">Verve Records</a> (b. 1918)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Verve Records", "link": "https://wikipedia.org/wiki/Verve_Records"}]}, {"year": "2002", "text": "<PERSON><PERSON>, American actor (b. 1914)", "html": "2002 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American actor (b. 1914)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American actor (b. 1914)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "2005", "text": "<PERSON>, American jockey and trainer (b. 1920)", "html": "2005 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American jockey and trainer (b. 1920)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American jockey and trainer (b. 1920)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2006", "text": "<PERSON><PERSON>, Indian chemist (b. 1917)", "html": "2006 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> Chatterjee\"><PERSON><PERSON></a>, Indian chemist (b. 1917)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> Chatterjee\"><PERSON><PERSON></a>, Indian chemist (b. 1917)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "2006", "text": "<PERSON>, American baseball player and coach (b. 1942)", "html": "2006 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player and coach (b. 1942)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player and coach (b. 1942)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2007", "text": "<PERSON>, French-Swiss dancer, choreographer, and director (b. 1927)", "html": "2007 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A9jart\" title=\"<PERSON>\"><PERSON></a>, French-Swiss dancer, choreographer, and director (b. 1927)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A9jart\" title=\"<PERSON>\"><PERSON></a>, French-Swiss dancer, choreographer, and director (b. 1927)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Maurice_B%C3%A9jart"}]}, {"year": "2007", "text": "<PERSON>, English television producer (b. 1935)", "html": "2007 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English television producer (b. 1935)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English television producer (b. 1935)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2010", "text": "<PERSON>, American baseball player (b. 1928)", "html": "2010 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player (b. 1928)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player (b. 1928)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2010", "text": "<PERSON>, Australian virologist and microbiologist (b. 1914)", "html": "2010 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian virologist and microbiologist (b. 1914)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian virologist and microbiologist (b. 1914)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2011", "text": "<PERSON><PERSON><PERSON>, Russian-American author (b. 1926)", "html": "2011 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Russian-American author (b. 1926)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Russian-American author (b. 1926)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "2011", "text": "<PERSON><PERSON>, Bosnian-Austrian soprano (b. 1921)", "html": "2011 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Bosnian-Austrian soprano (b. 1921)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Bosnian-Austrian soprano (b. 1921)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "2011", "text": "<PERSON>, American biologist and academic (b. 1938)", "html": "2011 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American biologist and academic (b. 1938)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American biologist and academic (b. 1938)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2011", "text": "<PERSON>, American drummer and composer (b. 1931)", "html": "2011 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American drummer and composer (b. 1931)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American drummer and composer (b. 1931)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "2012", "text": "<PERSON>, South African-Australian author (b. 1933)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, South African-Australian author (b. 1933)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, South African-Australian author (b. 1933)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2013", "text": "<PERSON>, Irish businessman (b. 1935)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(businessman)\" title=\"<PERSON> (businessman)\"><PERSON></a>, Irish businessman (b. 1935)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(businessman)\" title=\"<PERSON> (businessman)\"><PERSON></a>, Irish businessman (b. 1935)", "links": [{"title": "<PERSON> (businessman)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(businessman)"}]}, {"year": "2013", "text": "<PERSON>, French director and screenwriter (b. 1926)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French director and screenwriter (b. 1926)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French director and screenwriter (b. 1926)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2013", "text": "<PERSON>, Irish priest and activist (b. 1931)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Irish priest and activist (b. 1931)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Irish priest and activist (b. 1931)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2015", "text": "<PERSON><PERSON><PERSON>, Bangladeshi politician (b. 1949)", "html": "2015 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Bangladeshi politician (b. 1949)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Bangladeshi politician (b. 1949)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>"}]}, {"year": "2015", "text": "<PERSON>, Bangladeshi politician (b. 1948)", "html": "2015 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Bangladeshi politician (b. 1948)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Bangladeshi politician (b. 1948)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>_<PERSON>"}]}, {"year": "2015", "text": "<PERSON>, South Korean soldier and politician, President of South Korea (b. 1927)", "html": "2015 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, South Korean soldier and politician, <a href=\"https://wikipedia.org/wiki/President_of_South_Korea\" title=\"President of South Korea\">President of South Korea</a> (b. 1927)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, South Korean soldier and politician, <a href=\"https://wikipedia.org/wiki/President_of_South_Korea\" title=\"President of South Korea\">President of South Korea</a> (b. 1927)", "links": [{"title": "<PERSON>m", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>-<PERSON>m"}, {"title": "President of South Korea", "link": "https://wikipedia.org/wiki/President_of_South_Korea"}]}, {"year": "2016", "text": "<PERSON><PERSON>, Indian vocalist and singer (b. 1930)", "html": "2016 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Indian vocalist and singer (b. 1930)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Indian vocalist and singer (b. 1930)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON><PERSON><PERSON><PERSON>"}]}, {"year": "2017", "text": "<PERSON>, American music producer (b. 1919)", "html": "2017 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American music producer (b. 1919)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American music producer (b. 1919)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2017", "text": "<PERSON><PERSON><PERSON>, Russian operatic baritone (b. 1962)", "html": "2017 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Russian operatic baritone (b. 1962)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Russian operatic baritone (b. 1962)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "2017", "text": "<PERSON>, American singer-songwriter (b. 1958)", "html": "2017 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter (b. 1958)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter (b. 1958)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2020", "text": "<PERSON>, Austrian-born British physiologist (b. 1924)", "html": "2020 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Austrian-born British physiologist (b. 1924)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Austrian-born British physiologist (b. 1924)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2022", "text": "<PERSON>, American soldier, lawyer, and politician, 55th Governor of Kentucky (b. 1933)", "html": "2022 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON> Jr.\"><PERSON> Jr.</a>, American soldier, lawyer, and politician, 55th <a href=\"https://wikipedia.org/wiki/Governor_of_Kentucky\" title=\"Governor of Kentucky\">Governor of Kentucky</a> (b. 1933)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON> Jr.\"><PERSON> Jr.</a>, American soldier, lawyer, and politician, 55th <a href=\"https://wikipedia.org/wiki/Governor_of_Kentucky\" title=\"Governor of Kentucky\">Governor of Kentucky</a> (b. 1933)", "links": [{"title": "<PERSON> Jr.", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>_<PERSON>."}, {"title": "Governor of Kentucky", "link": "https://wikipedia.org/wiki/Governor_of_Kentucky"}]}, {"year": "2024", "text": "<PERSON>, Vanuatuan politician, 4th Prime Minister of Vanuatu (b. 1955)", "html": "2024 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Vanuatuan politician, 4th <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Vanuatu\" title=\"Prime Minister of Vanuatu\">Prime Minister of Vanuatu</a> (b. 1955)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Vanuatuan politician, 4th <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Vanuatu\" title=\"Prime Minister of Vanuatu\">Prime Minister of Vanuatu</a> (b. 1955)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Prime Minister of Vanuatu", "link": "https://wikipedia.org/wiki/Prime_Minister_of_Vanuatu"}]}]}}