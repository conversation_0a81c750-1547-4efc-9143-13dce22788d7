{"date": "June 29", "url": "https://wikipedia.org/wiki/June_29", "data": {"Events": [{"year": "226", "text": "<PERSON> succeeds his father as emperor of Wei.", "html": "226 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Rui\"><PERSON></a> succeeds his father as emperor of <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Wei\"><PERSON></a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Rui\"><PERSON></a> succeeds his father as emperor of <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Wei\"><PERSON></a>.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1149", "text": "<PERSON> of Poitiers is defeated and killed at the Battle of Inab by <PERSON><PERSON> <PERSON><PERSON>.", "html": "1149 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Poitiers\"><PERSON> Po<PERSON></a> is defeated and killed at the <a href=\"https://wikipedia.org/wiki/Battle_of_Inab\" title=\"Battle of Inab\">Battle of Inab</a> by <a href=\"https://wikipedia.org/wiki/Nur_ad-<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>ur ad-<PERSON>\"><PERSON><PERSON> <PERSON><PERSON><PERSON></a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Poitiers\"><PERSON></a> is defeated and killed at the <a href=\"https://wikipedia.org/wiki/Battle_of_Inab\" title=\"Battle of Inab\">Battle of Inab</a> by <a href=\"https://wikipedia.org/wiki/Nur_ad-<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>ur ad<PERSON>\"><PERSON><PERSON> <PERSON><PERSON><PERSON></a>.", "links": [{"title": "<PERSON> of Poitiers", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_Po<PERSON>"}, {"title": "Battle of Inab", "link": "https://wikipedia.org/wiki/Battle_of_Inab"}, {"title": "<PERSON><PERSON> <PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>-<PERSON>_<PERSON>"}]}, {"year": "1170", "text": "A major earthquake hits Syria, badly damaging towns such as Hama and Shaizar and structures such as the Krak des Chevaliers and the cathedral of St. Peter in Antioch.", "html": "1170 - A <a href=\"https://wikipedia.org/wiki/1170_Syria_earthquake\" title=\"1170 Syria earthquake\">major earthquake hits Syria</a>, badly damaging towns such as <a href=\"https://wikipedia.org/wiki/Hama\" title=\"Hama\"><PERSON>a</a> and <a href=\"https://wikipedia.org/wiki/Shaizar\" title=\"Shaizar\">Shaizar</a> and structures such as the <a href=\"https://wikipedia.org/wiki/Krak_des_Chevaliers\" title=\"Krak des Chevaliers\">Krak des Chevaliers</a> and the <a href=\"https://wikipedia.org/wiki/Church_of_Cassian\" title=\"Church of Cassian\">cathedral of St. Peter</a> in <a href=\"https://wikipedia.org/wiki/Antioch\" title=\"Antioch\">Antioch</a>.", "no_year_html": "A <a href=\"https://wikipedia.org/wiki/1170_Syria_earthquake\" title=\"1170 Syria earthquake\">major earthquake hits Syria</a>, badly damaging towns such as <a href=\"https://wikipedia.org/wiki/Hama\" title=\"Hama\">Hama</a> and <a href=\"https://wikipedia.org/wiki/Shaizar\" title=\"Shaizar\">Shaizar</a> and structures such as the <a href=\"https://wikipedia.org/wiki/Krak_des_Chevaliers\" title=\"Krak des Chevaliers\">Krak des Chevaliers</a> and the <a href=\"https://wikipedia.org/wiki/Church_of_Cassian\" title=\"Church of Cassian\">cathedral of St. Peter</a> in <a href=\"https://wikipedia.org/wiki/Antioch\" title=\"Antioch\">Antioch</a>.", "links": [{"title": "1170 Syria earthquake", "link": "https://wikipedia.org/wiki/1170_Syria_earthquake"}, {"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Hama"}, {"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>"}, {"title": "Krak des Chevaliers", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>s"}, {"title": "Church of Cassian", "link": "https://wikipedia.org/wiki/Church_of_Cassian"}, {"title": "Antioch", "link": "https://wikipedia.org/wiki/Antioch"}]}, {"year": "1194", "text": "<PERSON><PERSON><PERSON> is crowned King of Norway, leading to his excommunication by the Catholic Church and civil war.", "html": "1194 - <a href=\"https://wikipedia.org/wiki/Sverre_of_Norway\" title=\"Sverre of Norway\"><PERSON><PERSON><PERSON></a> is crowned <a href=\"https://wikipedia.org/wiki/King_of_Norway\" class=\"mw-redirect\" title=\"King of Norway\">King of Norway</a>, leading to his <a href=\"https://wikipedia.org/wiki/Excommunication_(Catholic_Church)\" class=\"mw-redirect\" title=\"Excommunication (Catholic Church)\">excommunication</a> by the <a href=\"https://wikipedia.org/wiki/Catholic_Church\" title=\"Catholic Church\">Catholic Church</a> and <a href=\"https://wikipedia.org/wiki/Civil_war\" title=\"Civil war\">civil war</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Sverre_of_Norway\" title=\"Sverre of Norway\"><PERSON><PERSON><PERSON></a> is crowned <a href=\"https://wikipedia.org/wiki/King_of_Norway\" class=\"mw-redirect\" title=\"King of Norway\">King of Norway</a>, leading to his <a href=\"https://wikipedia.org/wiki/Excommunication_(Catholic_Church)\" class=\"mw-redirect\" title=\"Excommunication (Catholic Church)\">excommunication</a> by the <a href=\"https://wikipedia.org/wiki/Catholic_Church\" title=\"Catholic Church\">Catholic Church</a> and <a href=\"https://wikipedia.org/wiki/Civil_war\" title=\"Civil war\">civil war</a>.", "links": [{"title": "Sverre of Norway", "link": "https://wikipedia.org/wiki/Sverre_of_Norway"}, {"title": "King of Norway", "link": "https://wikipedia.org/wiki/King_of_Norway"}, {"title": "Excommunication (Catholic Church)", "link": "https://wikipedia.org/wiki/Excommunication_(Catholic_Church)"}, {"title": "Catholic Church", "link": "https://wikipedia.org/wiki/Catholic_Church"}, {"title": "Civil war", "link": "https://wikipedia.org/wiki/Civil_war"}]}, {"year": "1444", "text": "Skanderbeg defeats an Ottoman invasion force at Torvioll.", "html": "1444 - <a href=\"https://wikipedia.org/wiki/Skanderbeg\" title=\"Skanderbeg\">Skanderbeg</a> defeats an Ottoman invasion force at <a href=\"https://wikipedia.org/wiki/Battle_of_Torvioll\" title=\"Battle of Torvioll\">Torvioll</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Skanderbeg\" title=\"Skanderbeg\">Skanderbeg</a> defeats an Ottoman invasion force at <a href=\"https://wikipedia.org/wiki/Battle_of_Torvioll\" title=\"Battle of Torvioll\">Torvioll</a>.", "links": [{"title": "Skanderbeg", "link": "https://wikipedia.org/wiki/Skanderbeg"}, {"title": "Battle of Torvioll", "link": "https://wikipedia.org/wiki/Battle_of_Torvioll"}]}, {"year": "1457", "text": "The Dutch city of Dordrecht is devastated by fire", "html": "1457 - The Dutch city of <a href=\"https://wikipedia.org/wiki/Dordrecht\" title=\"Dordrecht\">Dordrecht</a> is devastated by fire", "no_year_html": "The Dutch city of <a href=\"https://wikipedia.org/wiki/Dordrecht\" title=\"Dordrecht\">Dordrecht</a> is devastated by fire", "links": [{"title": "Dordrecht", "link": "https://wikipedia.org/wiki/Dordrecht"}]}, {"year": "1534", "text": "<PERSON> is the first European to reach Prince Edward Island.", "html": "1534 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> is the first European to reach <a href=\"https://wikipedia.org/wiki/Prince_Edward_Island\" title=\"Prince Edward Island\">Prince Edward Island</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> is the first European to reach <a href=\"https://wikipedia.org/wiki/Prince_Edward_Island\" title=\"Prince Edward Island\">Prince Edward Island</a>.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}, {"title": "Prince Edward Island", "link": "https://wikipedia.org/wiki/Prince_Edward_Island"}]}, {"year": "1613", "text": "The Globe Theatre in London, built by <PERSON>'s playing company, the Lord Chamberlain's Men, burns to the ground.", "html": "1613 - The <a href=\"https://wikipedia.org/wiki/Globe_Theatre#Shakespeare's_Globe_Theatres\" title=\"Globe Theatre\">Globe Theatre</a> in London, built by <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"William Shakespeare\"><PERSON></a><span class=\"nowrap\" style=\"padding-left:0.1em;\">'s</span> <a href=\"https://wikipedia.org/wiki/Playing_company\" title=\"Playing company\">playing company</a>, the <a href=\"https://wikipedia.org/wiki/<PERSON>_Chamberlain%27s_Men\" title=\"Lord Chamberlain's Men\">Lord <PERSON>'s Men</a>, burns to the ground.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Globe_Theatre#Shakespeare's_Globe_Theatres\" title=\"Globe Theatre\">Globe Theatre</a> in London, built by <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Shakespeare\"><PERSON></a><span class=\"nowrap\" style=\"padding-left:0.1em;\">'s</span> <a href=\"https://wikipedia.org/wiki/Playing_company\" title=\"Playing company\">playing company</a>, the <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%27s_Men\" title=\"Lord Chamberlain's Men\">Lord <PERSON>'s Men</a>, burns to the ground.", "links": [{"title": "Globe Theatre", "link": "https://wikipedia.org/wiki/Globe_Theatre#Shakespeare's_Globe_Theatres"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Playing company", "link": "https://wikipedia.org/wiki/Playing_company"}, {"title": "Lord Chamberlain's Men", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>%27s_Men"}]}, {"year": "1620", "text": "English crown bans tobacco growing in England, giving the Virginia Company a monopoly in exchange for tax of one shilling per pound.", "html": "1620 - English crown bans <a href=\"https://wikipedia.org/wiki/History_of_tobacco#European_usage\" title=\"History of tobacco\">tobacco growing in England</a>, giving the <a href=\"https://wikipedia.org/wiki/Virginia_Company\" title=\"Virginia Company\">Virginia Company</a> a monopoly in exchange for tax of one shilling per pound.", "no_year_html": "English crown bans <a href=\"https://wikipedia.org/wiki/History_of_tobacco#European_usage\" title=\"History of tobacco\">tobacco growing in England</a>, giving the <a href=\"https://wikipedia.org/wiki/Virginia_Company\" title=\"Virginia Company\">Virginia Company</a> a monopoly in exchange for tax of one shilling per pound.", "links": [{"title": "History of tobacco", "link": "https://wikipedia.org/wiki/History_of_tobacco#European_usage"}, {"title": "Virginia Company", "link": "https://wikipedia.org/wiki/Virginia_Company"}]}, {"year": "1644", "text": "<PERSON> of England defeats a Parliamentarian detachment at the Battle of Cropredy Bridge.", "html": "1644 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_England\" title=\"Charles I of England\"><PERSON> of England</a> defeats a <a href=\"https://wikipedia.org/wiki/Roundhead\" title=\"Roundhead\">Parliamentarian</a> detachment at the <a href=\"https://wikipedia.org/wiki/Battle_of_Cropredy_Bridge\" title=\"Battle of Cropredy Bridge\">Battle of Cropredy Bridge</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_England\" title=\"<PERSON> I of England\"><PERSON> of England</a> defeats a <a href=\"https://wikipedia.org/wiki/Roundhead\" title=\"Roundhead\">Parliamentarian</a> detachment at the <a href=\"https://wikipedia.org/wiki/Battle_of_Cropredy_Bridge\" title=\"Battle of Cropredy Bridge\">Battle of Cropredy Bridge</a>.", "links": [{"title": "<PERSON> of England", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_England"}, {"title": "Roundhead", "link": "https://wikipedia.org/wiki/Roundhead"}, {"title": "Battle of Cropredy Bridge", "link": "https://wikipedia.org/wiki/Battle_of_Cropredy_Bridge"}]}, {"year": "1659", "text": "At the Battle of Konotop the Ukrainian armies of <PERSON> defeat the Russians led by <PERSON>.", "html": "1659 - At the <a href=\"https://wikipedia.org/wiki/Battle_of_Konotop_(1659)\" class=\"mw-redirect\" title=\"Battle of Konotop (1659)\">Battle of Konotop</a> the Ukrainian armies of <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> defeat the Russians led by <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON></a>.", "no_year_html": "At the <a href=\"https://wikipedia.org/wiki/Battle_of_Konotop_(1659)\" class=\"mw-redirect\" title=\"Battle of Konotop (1659)\">Battle of Konotop</a> the Ukrainian armies of <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> defeat the Russians led by <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>ru<PERSON>\">Prince <PERSON></a>.", "links": [{"title": "Battle of Konotop (1659)", "link": "https://wikipedia.org/wiki/Battle_of_Konotop_(1659)"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1764", "text": "One of the strongest tornadoes in history strikes Woldegk, Germany, killing one person while leveling numerous mansions with winds estimated greater than 300 miles per hour (480 km/h).", "html": "1764 - One of the <a href=\"https://wikipedia.org/wiki/1764_Woldegk_tornado\" title=\"1764 Woldegk tornado\">strongest tornadoes in history</a> strikes <a href=\"https://wikipedia.org/wiki/Woldegk\" title=\"Woldegk\">Woldegk</a>, <a href=\"https://wikipedia.org/wiki/Germany\" title=\"Germany\">Germany</a>, killing one person while leveling numerous mansions with winds estimated greater than 300 miles per hour (480 km/h).", "no_year_html": "One of the <a href=\"https://wikipedia.org/wiki/1764_Woldegk_tornado\" title=\"1764 Woldegk tornado\">strongest tornadoes in history</a> strikes <a href=\"https://wikipedia.org/wiki/Woldegk\" title=\"Woldegk\">Woldegk</a>, <a href=\"https://wikipedia.org/wiki/Germany\" title=\"Germany\">Germany</a>, killing one person while leveling numerous mansions with winds estimated greater than 300 miles per hour (480 km/h).", "links": [{"title": "1764 Woldegk tornado", "link": "https://wikipedia.org/wiki/1764_Woldegk_tornado"}, {"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Woldegk"}, {"title": "Germany", "link": "https://wikipedia.org/wiki/Germany"}]}, {"year": "1786", "text": "<PERSON> and over five hundred Roman Catholic highlanders leave Scotland to settle in Glengarry County, Ontario.", "html": "1786 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(bishop_of_Kingston)\" title=\"<PERSON> (bishop of Kingston)\"><PERSON></a> and over five hundred <a href=\"https://wikipedia.org/wiki/Roman_Catholic\" class=\"mw-redirect\" title=\"Roman Catholic\">Roman Catholic</a> highlanders leave Scotland to settle in <a href=\"https://wikipedia.org/wiki/Glengarry_County,_Ontario\" title=\"Glengarry County, Ontario\">Glengarry County, Ontario</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(bishop_of_Kingston)\" title=\"<PERSON> (bishop of Kingston)\"><PERSON></a> and over five hundred <a href=\"https://wikipedia.org/wiki/Roman_Catholic\" class=\"mw-redirect\" title=\"Roman Catholic\">Roman Catholic</a> highlanders leave Scotland to settle in <a href=\"https://wikipedia.org/wiki/Glengarry_County,_Ontario\" title=\"Glengarry County, Ontario\">Glengarry County, Ontario</a>.", "links": [{"title": "<PERSON> (bishop of Kingston)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(bishop_of_Kingston)"}, {"title": "Roman Catholic", "link": "https://wikipedia.org/wiki/Roman_Catholic"}, {"title": "Glengarry County, Ontario", "link": "https://wikipedia.org/wiki/Glengarry_County,_Ontario"}]}, {"year": "1807", "text": "Russo-Turkish War: Admiral <PERSON> destroys the Ottoman fleet in the Battle of Athos.", "html": "1807 - <a href=\"https://wikipedia.org/wiki/Russo-Turkish_War_(1806%E2%80%9312)\" class=\"mw-redirect\" title=\"Russo-Turkish War (1806-12)\">Russo-Turkish War</a>: Admiral <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> destroys the <a href=\"https://wikipedia.org/wiki/Ottoman_Empire\" title=\"Ottoman Empire\">Ottoman</a> fleet in the <a href=\"https://wikipedia.org/wiki/Battle_of_Athos\" title=\"Battle of Athos\">Battle of Athos</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Russo-Turkish_War_(1806%E2%80%9312)\" class=\"mw-redirect\" title=\"Russo-Turkish War (1806-12)\">Russo-Turkish War</a>: Admiral <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> destroys the <a href=\"https://wikipedia.org/wiki/Ottoman_Empire\" title=\"Ottoman Empire\">Ottoman</a> fleet in the <a href=\"https://wikipedia.org/wiki/Battle_of_Athos\" title=\"Battle of Athos\">Battle of Athos</a>.", "links": [{"title": "Russo-Turkish War (1806-12)", "link": "https://wikipedia.org/wiki/Russo-Turkish_War_(1806%E2%80%9312)"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Ottoman Empire", "link": "https://wikipedia.org/wiki/Ottoman_Empire"}, {"title": "Battle of Athos", "link": "https://wikipedia.org/wiki/Battle_of_Athos"}]}, {"year": "1850", "text": "Autocephaly officially granted by the Ecumenical Patriarchate of Constantinople to the Church of Greece.", "html": "1850 - <a href=\"https://wikipedia.org/wiki/Autocephaly\" title=\"Autocephaly\">Autocephaly</a> officially granted by the <a href=\"https://wikipedia.org/wiki/Ecumenical_Patriarchate_of_Constantinople\" title=\"Ecumenical Patriarchate of Constantinople\">Ecumenical Patriarchate of Constantinople</a> to the <a href=\"https://wikipedia.org/wiki/Church_of_Greece\" title=\"Church of Greece\">Church of Greece</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Autocephaly\" title=\"Autocephaly\">Autocephaly</a> officially granted by the <a href=\"https://wikipedia.org/wiki/Ecumenical_Patriarchate_of_Constantinople\" title=\"Ecumenical Patriarchate of Constantinople\">Ecumenical Patriarchate of Constantinople</a> to the <a href=\"https://wikipedia.org/wiki/Church_of_Greece\" title=\"Church of Greece\">Church of Greece</a>.", "links": [{"title": "Autocephaly", "link": "https://wikipedia.org/wiki/Autocephaly"}, {"title": "Ecumenical Patriarchate of Constantinople", "link": "https://wikipedia.org/wiki/Ecumenical_Patriarchate_of_Constantinople"}, {"title": "Church of Greece", "link": "https://wikipedia.org/wiki/Church_of_Greece"}]}, {"year": "1864", "text": "At least 99 people, mostly German and Polish immigrants, are killed in Canada's worst railway disaster after a train fails to stop for an open drawbridge and plunges into the Rivière Richelieu near St-Hilaire, Quebec.", "html": "1864 - At least 99 people, mostly <a href=\"https://wikipedia.org/wiki/German_people\" class=\"mw-redirect\" title=\"German people\">German</a> and <a href=\"https://wikipedia.org/wiki/Polish_people\" title=\"Polish people\">Polish</a> <a href=\"https://wikipedia.org/wiki/Immigration\" title=\"Immigration\">immigrants</a>, are killed in Canada's worst <a href=\"https://wikipedia.org/wiki/St-Hilaire_train_disaster\" class=\"mw-redirect\" title=\"St-Hilaire train disaster\">railway disaster</a> after a train fails to stop for an open <a href=\"https://wikipedia.org/wiki/Drawbridge\" title=\"Drawbridge\">drawbridge</a> and plunges into the <a href=\"https://wikipedia.org/wiki/Richelieu_River\" title=\"Richelieu River\">Rivière Richelieu</a> near <a href=\"https://wikipedia.org/wiki/Mont-Saint-Hilaire,_Quebec\" class=\"mw-redirect\" title=\"Mont-Saint-Hilaire, Quebec\">St-Hilaire</a>, <a href=\"https://wikipedia.org/wiki/Quebec\" title=\"Quebec\">Quebec</a>.", "no_year_html": "At least 99 people, mostly <a href=\"https://wikipedia.org/wiki/German_people\" class=\"mw-redirect\" title=\"German people\">German</a> and <a href=\"https://wikipedia.org/wiki/Polish_people\" title=\"Polish people\">Polish</a> <a href=\"https://wikipedia.org/wiki/Immigration\" title=\"Immigration\">immigrants</a>, are killed in Canada's worst <a href=\"https://wikipedia.org/wiki/St-Hilaire_train_disaster\" class=\"mw-redirect\" title=\"St-Hilaire train disaster\">railway disaster</a> after a train fails to stop for an open <a href=\"https://wikipedia.org/wiki/Drawbridge\" title=\"Drawbridge\">drawbridge</a> and plunges into the <a href=\"https://wikipedia.org/wiki/Richelieu_River\" title=\"Richelieu River\">Rivière Richelieu</a> near <a href=\"https://wikipedia.org/wiki/Mont-Saint-Hilaire,_Quebec\" class=\"mw-redirect\" title=\"Mont-Saint-Hilaire, Quebec\">St-Hilaire</a>, <a href=\"https://wikipedia.org/wiki/Quebec\" title=\"Quebec\">Quebec</a>.", "links": [{"title": "German people", "link": "https://wikipedia.org/wiki/German_people"}, {"title": "Polish people", "link": "https://wikipedia.org/wiki/Polish_people"}, {"title": "Immigration", "link": "https://wikipedia.org/wiki/Immigration"}, {"title": "St-Hilaire train disaster", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON><PERSON>_train_disaster"}, {"title": "Drawbridge", "link": "https://wikipedia.org/wiki/Drawbridge"}, {"title": "Richelieu River", "link": "https://wikipedia.org/wiki/Richelieu_River"}, {"title": "Mont-Saint-Hi<PERSON>e, Quebec", "link": "https://wikipedia.org/wiki/Mont-Saint-Hilaire,_Quebec"}, {"title": "Quebec", "link": "https://wikipedia.org/wiki/Quebec"}]}, {"year": "1874", "text": "Greek politician <PERSON><PERSON><PERSON><PERSON> publishes a manifesto in the Athens daily Kairoi entitled \"Who's to Blame?\" leveling complaints against King <PERSON>. <PERSON><PERSON><PERSON> is elected Prime Minister of Greece the next year.", "html": "1874 - Greek politician <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a> publishes a manifesto in the <a href=\"https://wikipedia.org/wiki/Athens\" title=\"Athens\">Athens</a> daily <i><PERSON><PERSON></i> entitled \"Who's to Blame?\" leveling complaints against <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Greece\" title=\"<PERSON> of Greece\">King <PERSON></a>. <PERSON><PERSON><PERSON><PERSON> is elected <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Greece\" title=\"Prime Minister of Greece\">Prime Minister of Greece</a> the next year.", "no_year_html": "Greek politician <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a> publishes a manifesto in the <a href=\"https://wikipedia.org/wiki/Athens\" title=\"Athens\">Athens</a> daily <i><PERSON><PERSON></i> entitled \"Who's to Blame?\" leveling complaints against <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Greece\" title=\"<PERSON> I of Greece\">King <PERSON></a>. <PERSON><PERSON><PERSON><PERSON> is elected <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Greece\" title=\"Prime Minister of Greece\">Prime Minister of Greece</a> the next year.", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>s_Tri<PERSON>"}, {"title": "Athens", "link": "https://wikipedia.org/wiki/Athens"}, {"title": "<PERSON> of Greece", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Greece"}, {"title": "Prime Minister of Greece", "link": "https://wikipedia.org/wiki/Prime_Minister_of_Greece"}]}, {"year": "1880", "text": "France annexes Tahiti, renaming the independent Kingdom of Tahiti as \"Etablissements de français de l'Océanie\".", "html": "1880 - France annexes <a href=\"https://wikipedia.org/wiki/Tahiti\" title=\"Tahiti\">Tahiti</a>, renaming the independent <a href=\"https://wikipedia.org/wiki/Kingdom_of_Tahiti\" title=\"Kingdom of Tahiti\">Kingdom of Tahiti</a> as \"Etablissements de français de l'Océanie\".", "no_year_html": "France annexes <a href=\"https://wikipedia.org/wiki/Tahiti\" title=\"Tahiti\">Tahiti</a>, renaming the independent <a href=\"https://wikipedia.org/wiki/Kingdom_of_Tahiti\" title=\"Kingdom of Tahiti\">Kingdom of Tahiti</a> as \"Etablissements de français de l'Océanie\".", "links": [{"title": "Tahiti", "link": "https://wikipedia.org/wiki/Tahiti"}, {"title": "Kingdom of Tahiti", "link": "https://wikipedia.org/wiki/Kingdom_of_Tahiti"}]}, {"year": "1881", "text": "In Sudan, <PERSON> declares himself to be the Mahdi, the messianic redeemer of Islam.", "html": "1881 - In Sudan, <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> declares himself to be the <a href=\"https://wikipedia.org/wiki/Mahdi\" title=\"Mahdi\"><PERSON><PERSON></a>, the messianic redeemer of Islam.", "no_year_html": "In Sudan, <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> declares himself to be the <a href=\"https://wikipedia.org/wiki/Mahdi\" title=\"Mahdi\"><PERSON><PERSON></a>, the messianic redeemer of Islam.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>"}]}, {"year": "1888", "text": "<PERSON> records Handel's Israel in Egypt onto a phonograph cylinder, thought for many years to be the oldest known recording of music.", "html": "1888 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> records <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>'s <i><a href=\"https://wikipedia.org/wiki/Israel_in_Egypt\" title=\"Israel in Egypt\">Israel in Egypt</a></i> onto a <a href=\"https://wikipedia.org/wiki/Phonograph_cylinder\" title=\"Phonograph cylinder\">phonograph cylinder</a>, thought for many years to be the oldest known recording of music.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> records <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>'s <i><a href=\"https://wikipedia.org/wiki/Israel_in_Egypt\" title=\"Israel in Egypt\">Israel in Egypt</a></i> onto a <a href=\"https://wikipedia.org/wiki/Phonograph_cylinder\" title=\"Phonograph cylinder\">phonograph cylinder</a>, thought for many years to be the oldest known recording of music.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON><PERSON>_<PERSON>"}, {"title": "Israel in Egypt", "link": "https://wikipedia.org/wiki/Israel_in_Egypt"}, {"title": "Phonograph cylinder", "link": "https://wikipedia.org/wiki/Phonograph_cylinder"}]}, {"year": "1889", "text": "Hyde Park and several other Illinois townships vote to be annexed by Chicago, forming the largest United States city in area and second largest in population at the time.", "html": "1889 - <a href=\"https://wikipedia.org/wiki/Hyde_Park_Township,_Cook_County,_Illinois\" class=\"mw-redirect\" title=\"Hyde Park Township, Cook County, Illinois\">Hyde Park</a> and several other <a href=\"https://wikipedia.org/wiki/Illinois\" title=\"Illinois\">Illinois</a> townships vote to be annexed by <a href=\"https://wikipedia.org/wiki/Chicago\" title=\"Chicago\">Chicago</a>, forming the largest United States city in area and second largest in population at the time.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Hyde_Park_Township,_Cook_County,_Illinois\" class=\"mw-redirect\" title=\"Hyde Park Township, Cook County, Illinois\">Hyde Park</a> and several other <a href=\"https://wikipedia.org/wiki/Illinois\" title=\"Illinois\">Illinois</a> townships vote to be annexed by <a href=\"https://wikipedia.org/wiki/Chicago\" title=\"Chicago\">Chicago</a>, forming the largest United States city in area and second largest in population at the time.", "links": [{"title": "Hyde Park Township, Cook County, Illinois", "link": "https://wikipedia.org/wiki/Hyde_Park_Township,_Cook_County,_Illinois"}, {"title": "Illinois", "link": "https://wikipedia.org/wiki/Illinois"}, {"title": "Chicago", "link": "https://wikipedia.org/wiki/Chicago"}]}, {"year": "1915", "text": "The North Saskatchewan River flood of 1915 is the worst flood in Edmonton history.", "html": "1915 - The <a href=\"https://wikipedia.org/wiki/North_Saskatchewan_River_flood_of_1915\" title=\"North Saskatchewan River flood of 1915\">North Saskatchewan River flood of 1915</a> is the worst flood in <a href=\"https://wikipedia.org/wiki/Edmonton\" title=\"Edmonton\">Edmonton</a> history.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/North_Saskatchewan_River_flood_of_1915\" title=\"North Saskatchewan River flood of 1915\">North Saskatchewan River flood of 1915</a> is the worst flood in <a href=\"https://wikipedia.org/wiki/Edmonton\" title=\"Edmonton\">Edmonton</a> history.", "links": [{"title": "North Saskatchewan River flood of 1915", "link": "https://wikipedia.org/wiki/North_Saskatchewan_River_flood_of_1915"}, {"title": "Edmonton", "link": "https://wikipedia.org/wiki/Edmonton"}]}, {"year": "1916", "text": "British diplomat turned Irish nationalist <PERSON> is sentenced to death for his part in the Easter Rising.", "html": "1916 - British <a href=\"https://wikipedia.org/wiki/Diplomat\" title=\"Diplomat\">diplomat</a> turned <a href=\"https://wikipedia.org/wiki/Irish_nationalism\" title=\"Irish nationalism\">Irish nationalist</a> <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> is sentenced to death for his part in the <a href=\"https://wikipedia.org/wiki/Easter_Rising\" title=\"Easter Rising\">Easter Rising</a>.", "no_year_html": "British <a href=\"https://wikipedia.org/wiki/Diplomat\" title=\"Diplomat\">diplomat</a> turned <a href=\"https://wikipedia.org/wiki/Irish_nationalism\" title=\"Irish nationalism\">Irish nationalist</a> <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> is sentenced to death for his part in the <a href=\"https://wikipedia.org/wiki/Easter_Rising\" title=\"Easter Rising\">Easter Rising</a>.", "links": [{"title": "Diplomat", "link": "https://wikipedia.org/wiki/Diplomat"}, {"title": "Irish nationalism", "link": "https://wikipedia.org/wiki/Irish_nationalism"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>ment"}, {"title": "Easter Rising", "link": "https://wikipedia.org/wiki/Easter_Rising"}]}, {"year": "1922", "text": "France grants \"one square kilometer\" at Vimy Ridge \"freely, and for all time, to the Government of Canada, the free use of the land exempt from all taxes\".", "html": "1922 - France grants \"one square kilometer\" at <a href=\"https://wikipedia.org/wiki/Vimy_Ridge\" class=\"mw-redirect\" title=\"Vimy Ridge\">Vimy Ridge</a> \"freely, and for all time, to the Government of Canada, the free use of the land exempt from all taxes\".", "no_year_html": "France grants \"one square kilometer\" at <a href=\"https://wikipedia.org/wiki/Vimy_Ridge\" class=\"mw-redirect\" title=\"Vimy Ridge\">Vimy Ridge</a> \"freely, and for all time, to the Government of Canada, the free use of the land exempt from all taxes\".", "links": [{"title": "Vimy Ridge", "link": "https://wikipedia.org/wiki/Vimy_Ridge"}]}, {"year": "1927", "text": "The Bird of Paradise, a U.S. Army Air Corps Fokker tri-motor, completes the first transpacific flight, from the mainland United States to Hawaii.", "html": "1927 - The <i><a href=\"https://wikipedia.org/wiki/Bird_of_Paradise_(aircraft)\" title=\"Bird of Paradise (aircraft)\">Bird of Paradise</a></i>, a <a href=\"https://wikipedia.org/wiki/U.S._Army_Air_Corps\" class=\"mw-redirect\" title=\"U.S. Army Air Corps\">U.S. Army Air Corps</a> Fokker tri-motor, completes the first transpacific flight, from the mainland United States to Hawaii.", "no_year_html": "The <i><a href=\"https://wikipedia.org/wiki/Bird_of_Paradise_(aircraft)\" title=\"Bird of Paradise (aircraft)\">Bird of Paradise</a></i>, a <a href=\"https://wikipedia.org/wiki/U.S._Army_Air_Corps\" class=\"mw-redirect\" title=\"U.S. Army Air Corps\">U.S. Army Air Corps</a> Fokker tri-motor, completes the first transpacific flight, from the mainland United States to Hawaii.", "links": [{"title": "Bird of Paradise (aircraft)", "link": "https://wikipedia.org/wiki/Bird_of_Paradise_(aircraft)"}, {"title": "U.S. Army Air Corps", "link": "https://wikipedia.org/wiki/U.S._Army_Air_Corps"}]}, {"year": "1945", "text": "The Soviet Union annexes the Czechoslovak province of Carpathian Ruthenia.", "html": "1945 - The <a href=\"https://wikipedia.org/wiki/Soviet_Union\" title=\"Soviet Union\">Soviet Union</a> annexes the <a href=\"https://wikipedia.org/wiki/Czechoslovakia\" title=\"Czechoslovakia\">Czechoslovak</a> province of <a href=\"https://wikipedia.org/wiki/Carpathian_Ruthenia\" class=\"mw-redirect\" title=\"Carpathian Ruthenia\">Carpathian Ruthenia</a>.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Soviet_Union\" title=\"Soviet Union\">Soviet Union</a> annexes the <a href=\"https://wikipedia.org/wiki/Czechoslovakia\" title=\"Czechoslovakia\">Czechoslovak</a> province of <a href=\"https://wikipedia.org/wiki/Carpathian_Ruthenia\" class=\"mw-redirect\" title=\"Carpathian Ruthenia\">Carpathian Ruthenia</a>.", "links": [{"title": "Soviet Union", "link": "https://wikipedia.org/wiki/Soviet_Union"}, {"title": "Czechoslovakia", "link": "https://wikipedia.org/wiki/Czechoslovakia"}, {"title": "Carpathian Ruthenia", "link": "https://wikipedia.org/wiki/Carpathian_Ruthenia"}]}, {"year": "1950", "text": "Korean War: U.S. President <PERSON> authorizes a sea blockade of Korea.", "html": "1950 - <a href=\"https://wikipedia.org/wiki/Korean_War\" title=\"Korean War\">Korean War</a>: U.S. <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\">President <PERSON></a> authorizes a sea blockade of Korea.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Korean_War\" title=\"Korean War\">Korean War</a>: U.S. <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\">President <PERSON></a> authorizes a sea blockade of Korea.", "links": [{"title": "Korean War", "link": "https://wikipedia.org/wiki/Korean_War"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1952", "text": "The first Miss Universe pageant is held. <PERSON><PERSON> from Finland wins the title of Miss Universe 1952.", "html": "1952 - The first <a href=\"https://wikipedia.org/wiki/Miss_Universe\" title=\"Miss Universe\">Miss Universe</a> pageant is held. <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a> from Finland wins the title of <a href=\"https://wikipedia.org/wiki/Miss_Universe_1952\" title=\"Miss Universe 1952\">Miss Universe 1952</a>.", "no_year_html": "The first <a href=\"https://wikipedia.org/wiki/Miss_Universe\" title=\"Miss Universe\">Miss Universe</a> pageant is held. <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a> from Finland wins the title of <a href=\"https://wikipedia.org/wiki/Miss_Universe_1952\" title=\"Miss Universe 1952\">Miss Universe 1952</a>.", "links": [{"title": "Miss Universe", "link": "https://wikipedia.org/wiki/Miss_Universe"}, {"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>la"}, {"title": "Miss Universe 1952", "link": "https://wikipedia.org/wiki/Miss_Universe_1952"}]}, {"year": "1956", "text": "The Federal Aid Highway Act of 1956 is signed by U.S. President <PERSON>, officially creating the United States Interstate Highway System.", "html": "1956 - The <a href=\"https://wikipedia.org/wiki/Federal_Aid_Highway_Act_of_1956\" class=\"mw-redirect\" title=\"Federal Aid Highway Act of 1956\">Federal Aid Highway Act of 1956</a> is signed by U.S. President <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, officially creating the United States <a href=\"https://wikipedia.org/wiki/Interstate_Highway_System\" title=\"Interstate Highway System\">Interstate Highway System</a>.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Federal_Aid_Highway_Act_of_1956\" class=\"mw-redirect\" title=\"Federal Aid Highway Act of 1956\">Federal Aid Highway Act of 1956</a> is signed by U.S. President <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, officially creating the United States <a href=\"https://wikipedia.org/wiki/Interstate_Highway_System\" title=\"Interstate Highway System\">Interstate Highway System</a>.", "links": [{"title": "Federal Aid Highway Act of 1956", "link": "https://wikipedia.org/wiki/Federal_Aid_Highway_Act_of_1956"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "Interstate Highway System", "link": "https://wikipedia.org/wiki/Interstate_Highway_System"}]}, {"year": "1971", "text": "Prior to re-entry (following a record-setting stay aboard the Soviet Union's Salyut 1 space station), the crew capsule of the Soyuz 11 spacecraft depressurizes, killing the three cosmonauts on board. <PERSON><PERSON>, <PERSON><PERSON> and <PERSON> are the first humans to die in space.", "html": "1971 - Prior to re-entry (following a record-setting stay aboard the <a href=\"https://wikipedia.org/wiki/Soviet_Union\" title=\"Soviet Union\">Soviet Union</a>'s <a href=\"https://wikipedia.org/wiki/Salyut_1\" title=\"Salyut 1\">Salyut 1</a> space station), the crew capsule of the <a href=\"https://wikipedia.org/wiki/Soyuz_11\" title=\"Soyuz 11\">Soyuz 11</a> spacecraft depressurizes, killing the three cosmonauts on board. <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>ky\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a> and <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> are the first humans to die in space.", "no_year_html": "Prior to re-entry (following a record-setting stay aboard the <a href=\"https://wikipedia.org/wiki/Soviet_Union\" title=\"Soviet Union\">Soviet Union</a>'s <a href=\"https://wikipedia.org/wiki/Salyut_1\" title=\"Salyut 1\">Salyut 1</a> space station), the crew capsule of the <a href=\"https://wikipedia.org/wiki/Soyuz_11\" title=\"Soyuz 11\">Soyuz 11</a> spacecraft depressurizes, killing the three cosmonauts on board. <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a> and <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> are the first humans to die in space.", "links": [{"title": "Soviet Union", "link": "https://wikipedia.org/wiki/Soviet_Union"}, {"title": "Salyut 1", "link": "https://wikipedia.org/wiki/Salyut_1"}, {"title": "Soyuz 11", "link": "https://wikipedia.org/wiki/Soyuz_11"}, {"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>ky"}, {"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1972", "text": "The United States Supreme Court rules in the case <PERSON><PERSON> v. Georgia that arbitrary and inconsistent imposition of the death penalty violates the Eighth and Fourteenth Amendments and constitutes cruel and unusual punishment.", "html": "1972 - The <a href=\"https://wikipedia.org/wiki/United_States_Supreme_Court\" class=\"mw-redirect\" title=\"United States Supreme Court\">United States Supreme Court</a> rules in the case <i><a href=\"https://wikipedia.org/wiki/Furman_v._Georgia\" title=\"Furman v. Georgia\"><PERSON>rman v. Georgia</a></i> that arbitrary and inconsistent imposition of the death penalty violates the <a href=\"https://wikipedia.org/wiki/Eighth_Amendment_to_the_United_States_Constitution\" title=\"Eighth Amendment to the United States Constitution\">Eighth</a> and <a href=\"https://wikipedia.org/wiki/Fourteenth_Amendment_to_the_United_States_Constitution\" title=\"Fourteenth Amendment to the United States Constitution\">Fourteenth</a> Amendments and constitutes cruel and unusual punishment.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/United_States_Supreme_Court\" class=\"mw-redirect\" title=\"United States Supreme Court\">United States Supreme Court</a> rules in the case <i><a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_v._Georgia\" title=\"<PERSON>rman v. Georgia\"><PERSON><PERSON> v. Georgia</a></i> that arbitrary and inconsistent imposition of the death penalty violates the <a href=\"https://wikipedia.org/wiki/Eighth_Amendment_to_the_United_States_Constitution\" title=\"Eighth Amendment to the United States Constitution\">Eighth</a> and <a href=\"https://wikipedia.org/wiki/Fourteenth_Amendment_to_the_United_States_Constitution\" title=\"Fourteenth Amendment to the United States Constitution\">Fourteenth</a> Amendments and constitutes cruel and unusual punishment.", "links": [{"title": "United States Supreme Court", "link": "https://wikipedia.org/wiki/United_States_Supreme_Court"}, {"title": "<PERSON><PERSON> v. Georgia", "link": "https://wikipedia.org/wiki/Furman_v._Georgia"}, {"title": "Eighth Amendment to the United States Constitution", "link": "https://wikipedia.org/wiki/Eighth_Amendment_to_the_United_States_Constitution"}, {"title": "Fourteenth Amendment to the United States Constitution", "link": "https://wikipedia.org/wiki/Fourteenth_Amendment_to_the_United_States_Constitution"}]}, {"year": "1972", "text": "A Convair CV-580 and De Havilland Canada DHC-6 Twin Otter collide above Lake Winnebago near Appleton, Wisconsin, killing 13.", "html": "1972 - A <a href=\"https://wikipedia.org/wiki/Convair_CV-240_family\" title=\"Convair CV-240 family\">Convair CV-580</a> and <a href=\"https://wikipedia.org/wiki/De_Havilland_Canada_DHC-6_Twin_Otter\" title=\"De Havilland Canada DHC-6 Twin Otter\">De Havilland Canada DHC-6 Twin Otter</a> <a href=\"https://wikipedia.org/wiki/1972_Lake_Winnebago_mid-air_collision\" title=\"1972 Lake Winnebago mid-air collision\">collide</a> above <a href=\"https://wikipedia.org/wiki/Lake_Winnebago\" title=\"Lake Winnebago\">Lake Winnebago</a> near <a href=\"https://wikipedia.org/wiki/Appleton,_Wisconsin\" title=\"Appleton, Wisconsin\">Appleton, Wisconsin</a>, killing 13.", "no_year_html": "A <a href=\"https://wikipedia.org/wiki/Convair_CV-240_family\" title=\"Convair CV-240 family\">Convair CV-580</a> and <a href=\"https://wikipedia.org/wiki/De_Havilland_Canada_DHC-6_Twin_Otter\" title=\"De Havilland Canada DHC-6 Twin Otter\">De Havilland Canada DHC-6 Twin Otter</a> <a href=\"https://wikipedia.org/wiki/1972_Lake_Winnebago_mid-air_collision\" title=\"1972 Lake Winnebago mid-air collision\">collide</a> above <a href=\"https://wikipedia.org/wiki/Lake_Winnebago\" title=\"Lake Winnebago\">Lake Winnebago</a> near <a href=\"https://wikipedia.org/wiki/Appleton,_Wisconsin\" title=\"Appleton, Wisconsin\">Appleton, Wisconsin</a>, killing 13.", "links": [{"title": "Convair CV-240 family", "link": "https://wikipedia.org/wiki/Convair_CV-240_family"}, {"title": "De Havilland Canada DHC-6 Twin Otter", "link": "https://wikipedia.org/wiki/<PERSON>_Havilland_Canada_DHC-6_<PERSON>_<PERSON>tter"}, {"title": "1972 Lake Winnebago mid-air collision", "link": "https://wikipedia.org/wiki/1972_Lake_Winnebago_mid-air_collision"}, {"title": "Lake Winnebago", "link": "https://wikipedia.org/wiki/Lake_Winnebago"}, {"title": "Appleton, Wisconsin", "link": "https://wikipedia.org/wiki/Appleton,_Wisconsin"}]}, {"year": "1974", "text": "Vice President <PERSON> assumes powers and duties as Acting President of Argentina, while her husband President <PERSON> is terminally ill.", "html": "1974 - Vice President <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%B3n\" title=\"<PERSON>\"><PERSON></a> assumes powers and duties as Acting <a href=\"https://wikipedia.org/wiki/President_of_Argentina\" title=\"President of Argentina\">President of Argentina</a>, while her husband President <a href=\"https://wikipedia.org/wiki/Juan_<PERSON>%C3%B3n\" title=\"<PERSON>\"><PERSON></a> is terminally ill.", "no_year_html": "Vice President <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%B3n\" title=\"<PERSON>\"><PERSON></a> assumes powers and duties as Acting <a href=\"https://wikipedia.org/wiki/President_of_Argentina\" title=\"President of Argentina\">President of Argentina</a>, while her husband President <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%B3n\" title=\"<PERSON>\"><PERSON></a> is terminally ill.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Isabel_Per%C3%B3n"}, {"title": "President of Argentina", "link": "https://wikipedia.org/wiki/President_of_Argentina"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Juan_Per%C3%B3n"}]}, {"year": "1974", "text": "<PERSON> defects from the Soviet Union to Canada while on tour with the Kirov Ballet.", "html": "1974 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> defects from the <a href=\"https://wikipedia.org/wiki/Soviet_Union\" title=\"Soviet Union\">Soviet Union</a> to Canada while on tour with the <a href=\"https://wikipedia.org/wiki/Kirov_Ballet\" class=\"mw-redirect\" title=\"Kirov Ballet\">Kirov Ballet</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> defects from the <a href=\"https://wikipedia.org/wiki/Soviet_Union\" title=\"Soviet Union\">Soviet Union</a> to Canada while on tour with the <a href=\"https://wikipedia.org/wiki/Kirov_Ballet\" class=\"mw-redirect\" title=\"Kirov Ballet\">Kirov Ballet</a>.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Soviet Union", "link": "https://wikipedia.org/wiki/Soviet_Union"}, {"title": "Kirov Ballet", "link": "https://wikipedia.org/wiki/<PERSON>rov_Ballet"}]}, {"year": "1976", "text": "The Seychelles become independent from the United Kingdom.", "html": "1976 - The <a href=\"https://wikipedia.org/wiki/Seychelles\" title=\"Seychelles\">Seychelles</a> become independent from the United Kingdom.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Seychelles\" title=\"Seychelles\">Seychelles</a> become independent from the United Kingdom.", "links": [{"title": "Seychelles", "link": "https://wikipedia.org/wiki/Seychelles"}]}, {"year": "1976", "text": "The Conference of Communist and Workers Parties of Europe convenes in East Berlin.", "html": "1976 - The <a href=\"https://wikipedia.org/wiki/1976_Conference_of_Communist_and_Workers_Parties_of_Europe\" title=\"1976 Conference of Communist and Workers Parties of Europe\">Conference of Communist and Workers Parties of Europe</a> convenes in <a href=\"https://wikipedia.org/wiki/East_Berlin\" title=\"East Berlin\">East Berlin</a>.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/1976_Conference_of_Communist_and_Workers_Parties_of_Europe\" title=\"1976 Conference of Communist and Workers Parties of Europe\">Conference of Communist and Workers Parties of Europe</a> convenes in <a href=\"https://wikipedia.org/wiki/East_Berlin\" title=\"East Berlin\">East Berlin</a>.", "links": [{"title": "1976 Conference of Communist and Workers Parties of Europe", "link": "https://wikipedia.org/wiki/1976_Conference_of_Communist_and_Workers_Parties_of_Europe"}, {"title": "East Berlin", "link": "https://wikipedia.org/wiki/East_Berlin"}]}, {"year": "1987", "text": "<PERSON>'s painting, the Le Pont de Trinquetaille, is bought for $20.4 million at an auction in London, England.", "html": "1987 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>'s painting, the <i>Le Pont de Trinquetaille</i>, is bought for $20.4 million at an auction in <a href=\"https://wikipedia.org/wiki/London\" title=\"London\">London</a>, England.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>'s painting, the <i>Le Pont de Trinquetaille</i>, is bought for $20.4 million at an auction in <a href=\"https://wikipedia.org/wiki/London\" title=\"London\">London</a>, England.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "London", "link": "https://wikipedia.org/wiki/London"}]}, {"year": "1995", "text": "Space Shuttle program: STS-71 Mission (Atlantis) docks with the Russian space station Mir for the first time.", "html": "1995 - <a href=\"https://wikipedia.org/wiki/Space_Shuttle_program\" title=\"Space Shuttle program\">Space Shuttle program</a>: <a href=\"https://wikipedia.org/wiki/STS-71\" title=\"STS-71\">STS-71</a> Mission (<i><a href=\"https://wikipedia.org/wiki/Space_Shuttle_Atlantis\" title=\"Space Shuttle Atlantis\">Atlantis</a></i>) docks with the <a href=\"https://wikipedia.org/wiki/Mir\" title=\"Mir\">Russian space station <i>Mir</i></a> for the first time.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Space_Shuttle_program\" title=\"Space Shuttle program\">Space Shuttle program</a>: <a href=\"https://wikipedia.org/wiki/STS-71\" title=\"STS-71\">STS-71</a> Mission (<i><a href=\"https://wikipedia.org/wiki/Space_Shuttle_Atlantis\" title=\"Space Shuttle Atlantis\">Atlantis</a></i>) docks with the <a href=\"https://wikipedia.org/wiki/Mir\" title=\"Mir\">Russian space station <i>Mir</i></a> for the first time.", "links": [{"title": "Space Shuttle program", "link": "https://wikipedia.org/wiki/Space_Shuttle_program"}, {"title": "STS-71", "link": "https://wikipedia.org/wiki/STS-71"}, {"title": "Space Shuttle Atlantis", "link": "https://wikipedia.org/wiki/Space_Shuttle_Atlantis"}, {"title": "Mir", "link": "https://wikipedia.org/wiki/Mir"}]}, {"year": "1995", "text": "The Sampoong Department Store collapses in the Seocho District of Seoul, South Korea, killing 502 and injuring 937.", "html": "1995 - The <a href=\"https://wikipedia.org/wiki/Sampoong_Department_Store_collapse\" title=\"Sampoong Department Store collapse\">Sampoong Department Store collapses</a> in the <a href=\"https://wikipedia.org/wiki/Seocho_District\" title=\"Seocho District\">Seocho District</a> of <a href=\"https://wikipedia.org/wiki/Seoul\" title=\"Seoul\">Seoul</a>, South Korea, killing 502 and injuring 937.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Sampoong_Department_Store_collapse\" title=\"Sampoong Department Store collapse\">Sampoong Department Store collapses</a> in the <a href=\"https://wikipedia.org/wiki/Seocho_District\" title=\"Seocho District\">Seocho District</a> of <a href=\"https://wikipedia.org/wiki/Seoul\" title=\"Seoul\">Seoul</a>, South Korea, killing 502 and injuring 937.", "links": [{"title": "Sampoong Department Store collapse", "link": "https://wikipedia.org/wiki/Sampoong_Department_Store_collapse"}, {"title": "Seocho District", "link": "https://wikipedia.org/wiki/Seocho_District"}, {"title": "Seoul", "link": "https://wikipedia.org/wiki/Seoul"}]}, {"year": "2002", "text": "Naval clashes between South Korea and North Korea lead to the death of six South Korean sailors and sinking of a North Korean vessel.", "html": "2002 - <a href=\"https://wikipedia.org/wiki/Battle_of_Yeongpyeong_(2002)\" title=\"Battle of Yeongpyeong (2002)\">Naval clashes</a> between South Korea and <a href=\"https://wikipedia.org/wiki/North_Korea\" title=\"North Korea\">North Korea</a> lead to the death of six South Korean sailors and sinking of a North Korean vessel.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Battle_of_Yeongpyeong_(2002)\" title=\"Battle of Yeongpyeong (2002)\">Naval clashes</a> between South Korea and <a href=\"https://wikipedia.org/wiki/North_Korea\" title=\"North Korea\">North Korea</a> lead to the death of six South Korean sailors and sinking of a North Korean vessel.", "links": [{"title": "Battle of Yeongpyeong (2002)", "link": "https://wikipedia.org/wiki/Battle_of_Yeongpyeong_(2002)"}, {"title": "North Korea", "link": "https://wikipedia.org/wiki/North_Korea"}]}, {"year": "2006", "text": "<PERSON><PERSON> v. <PERSON><PERSON><PERSON>: The U.S. Supreme Court rules that President <PERSON>'s plan to try Guantanamo Bay detainees in military tribunals violates U.S. and international law.", "html": "2006 - <i><a href=\"https://wikipedia.org/wiki/<PERSON>dan_v._<PERSON><PERSON><PERSON>\" title=\"Hamdan v. R<PERSON>feld\"><PERSON>dan v. R<PERSON><PERSON></a></i>: The <a href=\"https://wikipedia.org/wiki/U.S._Supreme_Court\" class=\"mw-redirect\" title=\"U.S. Supreme Court\">U.S. Supreme Court</a> rules that President <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>'s plan to try <a href=\"https://wikipedia.org/wiki/Guantanamo_Bay_detention_camp\" title=\"Guantanamo Bay detention camp\">Guantanamo Bay detainees</a> in military tribunals violates U.S. and <a href=\"https://wikipedia.org/wiki/International_law\" title=\"International law\">international law</a>.", "no_year_html": "<i><a href=\"https://wikipedia.org/wiki/<PERSON>dan_v._<PERSON><PERSON><PERSON>\" title=\"Hamdan v. <PERSON>\">Hamdan v. <PERSON></a></i>: The <a href=\"https://wikipedia.org/wiki/U.S._Supreme_Court\" class=\"mw-redirect\" title=\"U.S. Supreme Court\">U.S. Supreme Court</a> rules that President <a href=\"https://wikipedia.org/wiki/George_<PERSON>_<PERSON>\" title=\"George <PERSON>\"><PERSON></a>'s plan to try <a href=\"https://wikipedia.org/wiki/Guantanamo_Bay_detention_camp\" title=\"Guantanamo Bay detention camp\">Guantanamo Bay detainees</a> in military tribunals violates U.S. and <a href=\"https://wikipedia.org/wiki/International_law\" title=\"International law\">international law</a>.", "links": [{"title": "<PERSON>dan v. Rumsfeld", "link": "https://wikipedia.org/wiki/<PERSON>dan_v._<PERSON><PERSON><PERSON>"}, {"title": "U.S. Supreme Court", "link": "https://wikipedia.org/wiki/U.S._Supreme_Court"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>"}, {"title": "Guantanamo Bay detention camp", "link": "https://wikipedia.org/wiki/Guantanamo_Bay_detention_camp"}, {"title": "International law", "link": "https://wikipedia.org/wiki/International_law"}]}, {"year": "2007", "text": "Apple Inc. releases its first mobile phone, the iPhone.", "html": "2007 - <a href=\"https://wikipedia.org/wiki/Apple_Inc.\" title=\"Apple Inc.\">Apple Inc.</a> releases its first mobile phone, the <a href=\"https://wikipedia.org/wiki/IPhone_(1st_generation)\" title=\"IPhone (1st generation)\">iPhone</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Apple_Inc.\" title=\"Apple Inc.\">Apple Inc.</a> releases its first mobile phone, the <a href=\"https://wikipedia.org/wiki/IPhone_(1st_generation)\" title=\"IPhone (1st generation)\">iPhone</a>.", "links": [{"title": "Apple Inc.", "link": "https://wikipedia.org/wiki/Apple_Inc."}, {"title": "IPhone (1st generation)", "link": "https://wikipedia.org/wiki/IPhone_(1st_generation)"}]}, {"year": "2012", "text": "A derecho sweeps across the eastern United States, leaving at least 22 people dead and millions without power.", "html": "2012 - A <a href=\"https://wikipedia.org/wiki/Derecho\" title=\"Derecho\">derecho</a> <a href=\"https://wikipedia.org/wiki/June_2012_North_American_derecho\" title=\"June 2012 North American derecho\">sweeps</a> across the eastern United States, leaving at least 22 people dead and millions without power.", "no_year_html": "A <a href=\"https://wikipedia.org/wiki/Derecho\" title=\"Derecho\">derecho</a> <a href=\"https://wikipedia.org/wiki/June_2012_North_American_derecho\" title=\"June 2012 North American derecho\">sweeps</a> across the eastern United States, leaving at least 22 people dead and millions without power.", "links": [{"title": "Derecho", "link": "https://wikipedia.org/wiki/Derecho"}, {"title": "June 2012 North American derecho", "link": "https://wikipedia.org/wiki/June_2012_North_American_derecho"}]}, {"year": "2014", "text": "The Islamic State of Iraq and the Levant self-declares its caliphate in Syria and northern Iraq.", "html": "2014 - The <a href=\"https://wikipedia.org/wiki/Islamic_State_of_Iraq_and_the_Levant\" class=\"mw-redirect\" title=\"Islamic State of Iraq and the Levant\">Islamic State of Iraq and the Levant</a> self-declares its caliphate in Syria and northern Iraq.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Islamic_State_of_Iraq_and_the_Levant\" class=\"mw-redirect\" title=\"Islamic State of Iraq and the Levant\">Islamic State of Iraq and the Levant</a> self-declares its caliphate in Syria and northern Iraq.", "links": [{"title": "Islamic State of Iraq and the Levant", "link": "https://wikipedia.org/wiki/Islamic_State_of_Iraq_and_the_Levant"}]}], "Births": [{"year": "1136", "text": "<PERSON><PERSON><PERSON> of Aragon (d. 1173)", "html": "1136 - <a href=\"https://wikipedia.org/wiki/Petronilla_of_Aragon\" title=\"Petronilla of Aragon\">Petronilla of Aragon</a> (d. 1173)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Petronilla_of_Aragon\" title=\"Petronilla of Aragon\">Petronilla of Aragon</a> (d. 1173)", "links": [{"title": "Petronilla of Aragon", "link": "https://wikipedia.org/wiki/Petronilla_of_Aragon"}]}, {"year": "1326", "text": "<PERSON><PERSON>, Ottoman Sultan (d. 1389)", "html": "1326 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON>rad I\"><PERSON><PERSON> <PERSON></a>, Ottoman Sultan (d. 1389)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON>rad I\"><PERSON><PERSON> <PERSON></a>, Ottoman Sultan (d. 1389)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_I"}]}, {"year": "1398", "text": "<PERSON> of Aragon and Navarre (d. 1479)", "html": "1398 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Aragon_and_Navarre\" class=\"mw-redirect\" title=\"<PERSON> of Aragon and Navarre\"><PERSON> of Aragon and Navarre</a> (d. 1479)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Aragon_and_Navarre\" class=\"mw-redirect\" title=\"<PERSON> of Aragon and Navarre\"><PERSON> of Aragon and Navarre</a> (d. 1479)", "links": [{"title": "<PERSON> of Aragon and Navarre", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Aragon_and_Navarre"}]}, {"year": "1443", "text": "<PERSON>, English knight (d. 1506)", "html": "1443 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(died_1506)\" title=\"<PERSON> (died 1506)\"><PERSON></a>, English knight (d. 1506)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(died_1506)\" title=\"<PERSON> (died 1506)\"><PERSON></a>, English knight (d. 1506)", "links": [{"title": "<PERSON> (died 1506)", "link": "https://wikipedia.org/wiki/<PERSON>_(died_1506)"}]}, {"year": "1482", "text": "<PERSON>, Queen of Portugal (d. 1517)", "html": "1482 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>,_Queen_of_Portugal\" title=\"<PERSON> of Aragon, Queen of Portugal\"><PERSON>, Queen of Portugal</a> (d. 1517)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_Aragon,_Queen_of_Portugal\" title=\"<PERSON> Aragon, Queen of Portugal\"><PERSON>, Queen of Portugal</a> (d. 1517)", "links": [{"title": "<PERSON>, Queen of Portugal", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_Aragon,_Queen_of_Portugal"}]}, {"year": "1488", "text": "<PERSON>, Catholic cardinal (d. 1560)", "html": "1488 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Catholic cardinal (d. 1560)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Catholic cardinal (d. 1560)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1517", "text": "<PERSON><PERSON><PERSON>, Flemish physician and botanist (d. 1585)", "html": "1517 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Flemish physician and botanist (d. 1585)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Flemish physician and botanist (d. 1585)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1525", "text": "<PERSON>, German humanist, theologian, diplomat and statesman (d. 1585)", "html": "1525 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German humanist, theologian, diplomat and statesman (d. 1585)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German humanist, theologian, diplomat and statesman (d. 1585)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1528", "text": "<PERSON>, Duke of Brunswick-Lüneburg (d. 1589)", "html": "1528 - <a href=\"https://wikipedia.org/wiki/<PERSON>,_Duke_of_Brunswick-L%C3%BCneburg\" title=\"<PERSON>, Duke of Brunswick-Lüneburg\"><PERSON>, Duke of Brunswick-Lüneburg</a> (d. 1589)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>,_Duke_of_Brunswick-L%C3%BCneburg\" title=\"<PERSON>, Duke of Brunswick-Lüneburg\"><PERSON>, Duke of Brunswick-Lüneburg</a> (d. 1589)", "links": [{"title": "<PERSON>, Duke of Brunswick-Lüneburg", "link": "https://wikipedia.org/wiki/<PERSON>,_Duke_of_Brunswick-L%C3%BCneburg"}]}, {"year": "1543", "text": "<PERSON> of Hesse, Duchess consort of Holstein-Gottorp (d. 1604)", "html": "1543 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_Hesse\" title=\"Christine of Hesse\"><PERSON> of Hesse</a>, Duchess consort of Holstein-Gottorp (d. 1604)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_Hesse\" title=\"Christine of Hesse\"><PERSON> of Hesse</a>, Duchess consort of Holstein-Gottorp (d. 1604)", "links": [{"title": "<PERSON> of Hesse", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1596", "text": "Emperor <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> of Japan (d. 1680)", "html": "1596 - <a href=\"https://wikipedia.org/wiki/Emperor_<PERSON>-<PERSON>\" title=\"Emperor <PERSON><PERSON>Mi<PERSON>\">Emperor <PERSON><PERSON><PERSON><PERSON></a> of Japan (d. 1680)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Emperor_<PERSON>-<PERSON>\" title=\"Emperor <PERSON><PERSON>Mi<PERSON>\">Emperor <PERSON><PERSON><PERSON></a> of Japan (d. 1680)", "links": [{"title": "Emperor <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>-<PERSON>"}]}, {"year": "1621", "text": "<PERSON>, Dutch Admiral (d. 1669)", "html": "1621 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch Admiral (d. 1669)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch Admiral (d. 1669)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1686", "text": "<PERSON>, Maltese artist (d. 1743)", "html": "1686 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Maltese artist (d. 1743)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Maltese artist (d. 1743)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1746", "text": "<PERSON>, German linguist, author, and educator (d. 1818)", "html": "1746 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German linguist, author, and educator (d. 1818)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German linguist, author, and educator (d. 1818)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1768", "text": "<PERSON>, Maltese sculptor (d. 1831)", "html": "1768 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Maltese sculptor (d. 1831)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Maltese sculptor (d. 1831)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1787", "text": "<PERSON><PERSON><PERSON>, American poet, school founder (d. 1820)", "html": "1787 - <a href=\"https://wikipedia.org/wiki/Lavinia_Stoddard\" title=\"<PERSON><PERSON><PERSON> Stoddard\"><PERSON><PERSON><PERSON></a>, American poet, school founder (d. 1820)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/La<PERSON>ia_Stoddard\" title=\"<PERSON><PERSON><PERSON> Stoddard\"><PERSON><PERSON><PERSON></a>, American poet, school founder (d. 1820)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Lavinia_Stoddard"}]}, {"year": "1793", "text": "<PERSON>, Czech-Austrian inventor, invented the propeller (d. 1857)", "html": "1793 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Czech-Austrian inventor, invented the <a href=\"https://wikipedia.org/wiki/Propeller\" title=\"Propeller\">propeller</a> (d. 1857)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Czech-Austrian inventor, invented the <a href=\"https://wikipedia.org/wiki/Propeller\" title=\"Propeller\">propeller</a> (d. 1857)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Propeller", "link": "https://wikipedia.org/wiki/Propeller"}]}, {"year": "1798", "text": "<PERSON><PERSON><PERSON>, German author and poet (d. 1871)", "html": "1798 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, German author and poet (d. 1871)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, German author and poet (d. 1871)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1798", "text": "<PERSON>, Italian poet and philosopher (d. 1837)", "html": "1798 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian poet and philosopher (d. 1837)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian poet and philosopher (d. 1837)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1801", "text": "<PERSON><PERSON><PERSON><PERSON><PERSON>, French economist and theorist (d. 1850)", "html": "1801 - <a href=\"https://wikipedia.org/wiki/Fr%C3%A9d%C3%A9ric_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON><PERSON></a>, French economist and theorist (d. 1850)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Fr%C3%A9d%C3%A9<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON><PERSON></a>, French economist and theorist (d. 1850)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Fr%C3%A9d%C3%A9ric_Bastiat"}]}, {"year": "1803", "text": "<PERSON>, American minister and author (d. 1868)", "html": "1803 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American minister and author (d. 1868)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American minister and author (d. 1868)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1818", "text": "<PERSON>, Italian astronomer and academic (d. 1878)", "html": "1818 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian astronomer and academic (d. 1878)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian astronomer and academic (d. 1878)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1819", "text": "<PERSON>, American poet, playwright, and politician (d. 1902)", "html": "1819 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON> English\"><PERSON></a>, American poet, playwright, and politician (d. 1902)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"Thomas <PERSON> English\"><PERSON></a>, American poet, playwright, and politician (d. 1902)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1833", "text": "<PERSON>, Norwegian chemist and academic (d. 1900)", "html": "1833 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Norwegian chemist and academic (d. 1900)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Norwegian chemist and academic (d. 1900)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>ge"}]}, {"year": "1835", "text": "<PERSON>, American poet and story writer (d. 1894)", "html": "1835 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American poet and story writer (d. 1894)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American poet and story writer (d. 1894)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>"}]}, {"year": "1844", "text": "<PERSON> of Serbia (d. 1921)", "html": "1844 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Serbia\" title=\"<PERSON> of Serbia\"><PERSON> of Serbia</a> (d. 1921)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Serbia\" title=\"<PERSON> of Serbia\"><PERSON> of Serbia</a> (d. 1921)", "links": [{"title": "<PERSON> of Serbia", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_Serbia"}]}, {"year": "1849", "text": "<PERSON>, Chilean lawyer and politician, 15th President of Chile (d. 1910)", "html": "1849 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Chilean lawyer and politician, 15th <a href=\"https://wikipedia.org/wiki/President_of_Chile\" title=\"President of Chile\">President of Chile</a> (d. 1910)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Chilean lawyer and politician, 15th <a href=\"https://wikipedia.org/wiki/President_of_Chile\" title=\"President of Chile\">President of Chile</a> (d. 1910)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "President of Chile", "link": "https://wikipedia.org/wiki/President_of_Chile"}]}, {"year": "1849", "text": "<PERSON>, Russian politician, 1st Chairmen of Council of Ministers of the Russian Empire (d. 1915)", "html": "1849 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian politician, 1st <a href=\"https://wikipedia.org/wiki/List_of_heads_of_government_of_Russia\" title=\"List of heads of government of Russia\">Chairmen of Council of Ministers of the Russian Empire</a> (d. 1915)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian politician, 1st <a href=\"https://wikipedia.org/wiki/List_of_heads_of_government_of_Russia\" title=\"List of heads of government of Russia\">Chairmen of Council of Ministers of the Russian Empire</a> (d. 1915)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>itte"}, {"title": "List of heads of government of Russia", "link": "https://wikipedia.org/wiki/List_of_heads_of_government_of_Russia"}]}, {"year": "1849", "text": "<PERSON>, American businessman and politician, 51st Governor of Delaware (d. 1926)", "html": "1849 - <a href=\"https://wikipedia.org/wiki/<PERSON>_(governor)\" title=\"<PERSON> (governor)\"><PERSON></a>, American businessman and politician, 51st <a href=\"https://wikipedia.org/wiki/Governor_of_Delaware\" class=\"mw-redirect\" title=\"Governor of Delaware\">Governor of Delaware</a> (d. 1926)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>(governor)\" title=\"<PERSON> (governor)\"><PERSON></a>, American businessman and politician, 51st <a href=\"https://wikipedia.org/wiki/Governor_of_Delaware\" class=\"mw-redirect\" title=\"Governor of Delaware\">Governor of Delaware</a> (d. 1926)", "links": [{"title": "<PERSON> (governor)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(governor)"}, {"title": "Governor of Delaware", "link": "https://wikipedia.org/wiki/Governor_of_Delaware"}]}, {"year": "1858", "text": "<PERSON>, American general and engineer, co-designed the Panama Canal (d. 1928)", "html": "1858 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_Goethals\" title=\"George Washington Goethals\"><PERSON></a>, American general and engineer, co-designed the <a href=\"https://wikipedia.org/wiki/Panama_Canal\" title=\"Panama Canal\">Panama Canal</a> (d. 1928)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_Go<PERSON>\" title=\"George Washington Goethals\"><PERSON></a>, American general and engineer, co-designed the <a href=\"https://wikipedia.org/wiki/Panama_Canal\" title=\"Panama Canal\">Panama Canal</a> (d. 1928)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/George_Washington_Goethals"}, {"title": "Panama Canal", "link": "https://wikipedia.org/wiki/Panama_Canal"}]}, {"year": "1858", "text": "<PERSON>, American activist and politician (d. 1932)", "html": "1858 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American activist and politician (d. 1932)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American activist and politician (d. 1932)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1861", "text": "<PERSON>, American physician and surgeon, co-founded the Mayo Clinic (d. 1939)", "html": "1861 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American physician and surgeon, co-founded the <a href=\"https://wikipedia.org/wiki/Mayo_Clinic\" title=\"Mayo Clinic\">Mayo Clinic</a> (d. 1939)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American physician and surgeon, co-founded the <a href=\"https://wikipedia.org/wiki/Mayo_Clinic\" title=\"Mayo Clinic\">Mayo Clinic</a> (d. 1939)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "Mayo Clinic", "link": "https://wikipedia.org/wiki/Mayo_Clinic"}]}, {"year": "1863", "text": "<PERSON><PERSON><PERSON>, American baseball player, coach, and manager (d. 1934)", "html": "1863 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American baseball player, coach, and manager (d. 1934)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American baseball player, coach, and manager (d. 1934)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/W<PERSON><PERSON>_<PERSON>"}]}, {"year": "1866", "text": "<PERSON><PERSON><PERSON><PERSON>, Dutch swimmer (d. 1939)", "html": "1866 - <a href=\"https://wikipedia.org/wiki/Bartholomeus_Roodenburch\" title=\"Bartholomeus Roodenburch\"><PERSON><PERSON><PERSON><PERSON></a>, Dutch swimmer (d. 1939)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Bartholomeus_Roodenburch\" title=\"Bartholomeus Roodenburch\"><PERSON><PERSON><PERSON><PERSON></a>, Dutch swimmer (d. 1939)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Bartholomeus_Roodenburch"}]}, {"year": "1868", "text": "<PERSON>, American astronomer and journalist (d. 1938)", "html": "1868 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American astronomer and journalist (d. 1938)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American astronomer and journalist (d. 1938)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1870", "text": "<PERSON>, American tenor, composer, and director (d. 1926)", "html": "1870 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American tenor, composer, and director (d. 1926)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American tenor, composer, and director (d. 1926)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1873", "text": "<PERSON>, German ethnologist and archaeologist (d. 1938)", "html": "1873 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German ethnologist and archaeologist (d. 1938)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German ethnologist and archaeologist (d. 1938)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_Frobenius"}]}, {"year": "1879", "text": "<PERSON><PERSON><PERSON>, Italian cardinal (d. 1970)", "html": "1879 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Italian cardinal (d. 1970)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Italian cardinal (d. 1970)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1880", "text": "<PERSON>, German general (d. 1944)", "html": "1880 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German general (d. 1944)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German general (d. 1944)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1881", "text": "<PERSON>, American director, producer, and agent (d. 1929)", "html": "1881 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American director, producer, and agent (d. 1929)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American director, producer, and agent (d. 1929)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1881", "text": "<PERSON><PERSON>, German-American composer and musicologist (d. 1959)", "html": "1881 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, German-American composer and musicologist (d. 1959)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, German-American composer and musicologist (d. 1959)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1882", "text": "<PERSON>, English runner (d. 1961)", "html": "1882 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English runner (d. 1961)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English runner (d. 1961)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1882", "text": "<PERSON>, German captain and politician, Reich Minister for Labour (d. 1947)", "html": "1882 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German captain and politician, <a href=\"https://wikipedia.org/wiki/Federal_Ministry_of_Labour_and_Social_Affairs\" title=\"Federal Ministry of Labour and Social Affairs\">Reich Minister for Labour</a> (d. 1947)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German captain and politician, <a href=\"https://wikipedia.org/wiki/Federal_Ministry_of_Labour_and_Social_Affairs\" title=\"Federal Ministry of Labour and Social Affairs\">Reich Minister for Labour</a> (d. 1947)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Federal Ministry of Labour and Social Affairs", "link": "https://wikipedia.org/wiki/Federal_Ministry_of_Labour_and_Social_Affairs"}]}, {"year": "1885", "text": "<PERSON><PERSON><PERSON>, Hungarian football player and coach (d. 1941)", "html": "1885 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>%C3%BCrschner\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Hungarian football player and coach (d. 1941)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>%C3%<PERSON>rschner\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Hungarian football player and coach (d. 1941)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>zidor_K%C3%<PERSON><PERSON><PERSON>"}]}, {"year": "1886", "text": "<PERSON>, Luxembourgian-French lawyer and politician, Prime Minister of France (d. 1963)", "html": "1886 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Luxembourgian-French lawyer and politician, <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_France\" title=\"Prime Minister of France\">Prime Minister of France</a> (d. 1963)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Luxembourgian-French lawyer and politician, <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_France\" title=\"Prime Minister of France\">Prime Minister of France</a> (d. 1963)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Prime Minister of France", "link": "https://wikipedia.org/wiki/Prime_Minister_of_France"}]}, {"year": "1888", "text": "<PERSON><PERSON><PERSON>, Australian gangster (d. 1927)", "html": "1888 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON> <PERSON>\"><PERSON><PERSON><PERSON></a>, Australian gangster (d. 1927)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON> <PERSON>\"><PERSON><PERSON><PERSON></a>, Australian gangster (d. 1927)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1889", "text": "<PERSON>, Scottish-American golfer (d. 1961)", "html": "1889 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish-American golfer (d. 1961)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish-American golfer (d. 1961)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1890", "text": "<PERSON>, American sculptor and academic (d. 1970)", "html": "1890 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American sculptor and academic (d. 1970)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American sculptor and academic (d. 1970)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1890", "text": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, Dutch supercentenarian (d. 2005)", "html": "1890 - <a href=\"https://wikipedia.org/wiki/He<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>-<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>Sc<PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Dutch supercentenarian (d. 2005)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>_<PERSON>-<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Dutch supercentenarian (d. 2005)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>_<PERSON><PERSON>-<PERSON>"}]}, {"year": "1893", "text": "<PERSON><PERSON><PERSON><PERSON>, Indian economist and statistician (d. 1972)", "html": "1893 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Indian economist and statistician (d. 1972)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Indian economist and statistician (d. 1972)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1893", "text": "<PERSON><PERSON><PERSON>, Finnish composer and educator (d. 1958)", "html": "1893 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_Me<PERSON>\" title=\"Aarre Me<PERSON>nto\"><PERSON><PERSON><PERSON></a>, Finnish composer and educator (d. 1958)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"Aarre Me<PERSON>nto\"><PERSON><PERSON><PERSON></a>, Finnish composer and educator (d. 1958)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/A<PERSON><PERSON>_<PERSON>"}]}, {"year": "1897", "text": "<PERSON><PERSON><PERSON>, Canadian journalist and publisher (d. 2001)", "html": "1897 - <a href=\"https://wikipedia.org/wiki/Fulgence_Charpentier\" title=\"Fulgence Charpentier\">Fulge<PERSON> Charpentier</a>, Canadian journalist and publisher (d. 2001)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Fulgence_Charpentier\" title=\"Fulgence Charpentier\">Fulge<PERSON> Charpentier</a>, Canadian journalist and publisher (d. 2001)", "links": [{"title": "Fulge<PERSON> Charpentier", "link": "https://wikipedia.org/wiki/Fulgence_Cha<PERSON>entier"}]}, {"year": "1898", "text": "<PERSON>, French pianist and educator (d. 1986)", "html": "1898 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A9bure\" title=\"<PERSON>\"><PERSON></a>, French pianist and educator (d. 1986)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A9bure\" title=\"<PERSON>\"><PERSON></a>, French pianist and educator (d. 1986)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>%C3%A9bure"}]}, {"year": "1900", "text": "<PERSON><PERSON>, French poet and pilot (d. 1944)", "html": "1900 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>-<PERSON>up%C3%A9ry\" title=\"<PERSON>\"><PERSON></a>, French poet and pilot (d. 1944)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>-<PERSON>%C3%A9ry\" title=\"<PERSON>\"><PERSON></a>, French poet and pilot (d. 1944)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>-Exup%C3%A9ry"}]}, {"year": "1901", "text": "<PERSON>, American singer and actor (d. 1967)", "html": "1901 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer and actor (d. 1967)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Eddy\"><PERSON></a>, American singer and actor (d. 1967)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1903", "text": "<PERSON>, English engineer, developed the H2S radar (d. 1942)", "html": "1903 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English engineer, developed the <a href=\"https://wikipedia.org/wiki/H2S_radar\" class=\"mw-redirect\" title=\"H2S radar\">H2S radar</a> (d. 1942)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English engineer, developed the <a href=\"https://wikipedia.org/wiki/H2S_radar\" class=\"mw-redirect\" title=\"H2S radar\">H2S radar</a> (d. 1942)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "H2S radar", "link": "https://wikipedia.org/wiki/H2S_radar"}]}, {"year": "1904", "text": "<PERSON><PERSON><PERSON>, Polish mathematician (d. 1956)", "html": "1904 - <a href=\"https://wikipedia.org/wiki/Wito<PERSON>_<PERSON>\" title=\"Wito<PERSON>\"><PERSON><PERSON><PERSON></a>, Polish mathematician (d. 1956)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Wito<PERSON>_<PERSON>\" title=\"Wito<PERSON>\"><PERSON><PERSON><PERSON></a>, Polish mathematician (d. 1956)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Wito<PERSON>_<PERSON>"}]}, {"year": "1906", "text": "<PERSON>, Ukrainian general (d. 1945)", "html": "1906 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Ukrainian general (d. 1945)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Ukrainian general (d. 1945)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1906", "text": "<PERSON>, German general (d. 2000)", "html": "1906 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German general (d. 2000)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German general (d. 2000)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1908", "text": "<PERSON>, American composer and conductor (d. 1975)", "html": "1908 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American composer and conductor (d. 1975)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American composer and conductor (d. 1975)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1908", "text": "<PERSON>, Swedish javelin thrower (d. 1963)", "html": "1908 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swedish javelin thrower (d. 1963)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swedish javelin thrower (d. 1963)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1910", "text": "<PERSON>, American composer and conductor (d. 1969)", "html": "1910 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American composer and conductor (d. 1969)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American composer and conductor (d. 1969)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1910", "text": "<PERSON>, American baseball player (d. 1993)", "html": "1910 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player (d. 1993)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player (d. 1993)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1911", "text": "<PERSON> of Lippe-Biesterfeld (d. 2004)", "html": "1911 - <a href=\"https://wikipedia.org/wiki/Prince_<PERSON>_of_Lippe-Biesterfeld\" title=\"Prince <PERSON> of Lippe-Biesterfeld\">Prince <PERSON> of Lippe-Biesterfeld</a> (d. 2004)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Prince_<PERSON>_of_Lippe-Biesterfeld\" title=\"Prince <PERSON> of Lippe-Biesterfeld\">Prince <PERSON> of Lippe-Biesterfeld</a> (d. 2004)", "links": [{"title": "<PERSON> of Lippe-Biesterfeld", "link": "https://wikipedia.org/wiki/Prince_<PERSON>_<PERSON>_Lippe-Biesterfeld"}]}, {"year": "1911", "text": "<PERSON>, Canadian-American actress (d. 1995)", "html": "1911 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian-American actress (d. 1995)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian-American actress (d. 1995)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1911", "text": "<PERSON>, American composer and conductor (d. 1975)", "html": "1911 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American composer and conductor (d. 1975)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American composer and conductor (d. 1975)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1912", "text": "<PERSON>, Mexican pianist, composer, and conductor (d. 1958)", "html": "1912 - <a href=\"https://wikipedia.org/wiki/Jos%C3%A9_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Mexican pianist, composer, and conductor (d. 1958)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Jos%C3%A9_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Mexican pianist, composer, and conductor (d. 1958)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Jos%C3%A9_<PERSON>_<PERSON>"}]}, {"year": "1912", "text": "<PERSON><PERSON>, French oenologist and academic (d. 2004)", "html": "1912 - <a href=\"https://wikipedia.org/wiki/%C3%89mile_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, French oenologist and academic (d. 2004)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/%C3%89mile_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, French oenologist and academic (d. 2004)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/%C3%89mile_<PERSON><PERSON><PERSON><PERSON>"}]}, {"year": "1912", "text": "<PERSON>, American historian and author (d. 2004)", "html": "1912 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(author)\" class=\"mw-redirect\" title=\"<PERSON> (author)\"><PERSON></a>, American historian and author (d. 2004)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(author)\" class=\"mw-redirect\" title=\"<PERSON> (author)\"><PERSON></a>, American historian and author (d. 2004)", "links": [{"title": "<PERSON> (author)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(author)"}]}, {"year": "1913", "text": "<PERSON>, American pole vaulter (d. 1992)", "html": "1913 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Meadows\"><PERSON></a>, American pole vaulter (d. 1992)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Earle Meadows\"><PERSON></a>, American pole vaulter (d. 1992)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1914", "text": "<PERSON>, Czech-American conductor and composer (d. 1996)", "html": "1914 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%ADk\" title=\"<PERSON>\"><PERSON></a>, Czech-American conductor and composer (d. 1996)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%ADk\" title=\"<PERSON>\"><PERSON></a>, Czech-American conductor and composer (d. 1996)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%ADk"}]}, {"year": "1914", "text": "<PERSON><PERSON>, Greek-American mathematician and academic (d. 1976)", "html": "1914 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Greek-American mathematician and academic (d. 1976)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Greek-American mathematician and academic (d. 1976)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1916", "text": "<PERSON>, American actress and activist (d. 2005)", "html": "1916 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress and activist (d. 2005)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress and activist (d. 2005)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1917", "text": "<PERSON>, Chinese politician (d. 2018)", "html": "1917 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(politician)\" class=\"mw-redirect\" title=\"<PERSON> (politician)\"><PERSON></a>, Chinese politician (d. 2018)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(politician)\" class=\"mw-redirect\" title=\"<PERSON> (politician)\"><PERSON></a>, Chinese politician (d. 2018)", "links": [{"title": "<PERSON> (politician)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(politician)"}]}, {"year": "1918", "text": "<PERSON><PERSON>, Swiss ice hockey player (d. 2011)", "html": "1918 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Swiss ice hockey player (d. 2011)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Swiss ice hockey player (d. 2011)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1918", "text": "<PERSON>, U.S admiral (d. 2016)", "html": "1918 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, U.S admiral (d. 2016)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, U.S admiral (d. 2016)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1918", "text": "<PERSON>, United States Air Force major general (d. 2019)", "html": "1918 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, United States Air Force major general (d. 2019)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, United States Air Force major general (d. 2019)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "1919", "text": "<PERSON>, Mexican cardinal (d. 2008)", "html": "1919 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON> y <PERSON></a>, Mexican cardinal (d. 2008)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON> y <PERSON>\"><PERSON></a>, Mexican cardinal (d. 2008)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1919", "text": "<PERSON>, Commander of British Far East Land Forces (d. 2017)", "html": "1919 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Commander of British Far East Land Forces (d. 2017)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Commander of British Far East Land Forces (d. 2017)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1919", "text": "<PERSON>, Cuban composer (d. 2008)", "html": "1919 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Cuban composer (d. 2008)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Blanco\"><PERSON></a>, Cuban composer (d. 2008)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1919", "text": "<PERSON>, American actor and rodeo performer (d. 1983)", "html": "1919 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Pickens\"><PERSON></a>, American actor and rodeo performer (d. 1983)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Slim Pickens\"><PERSON></a>, American actor and rodeo performer (d. 1983)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>ens"}]}, {"year": "1919", "text": "<PERSON>, Canadian-American theatre director, actor, and dean (d. 2006)", "html": "1919 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian-American theatre director, actor, and dean (d. 2006)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian-American theatre director, actor, and dean (d. 2006)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1920", "text": "<PERSON>, Spanish footballer and manager (d. 1995)", "html": "1920 - <a href=\"https://wikipedia.org/wiki/C%C3%A9sar_Rodr%C3%ADguez_%C3%81l<PERSON><PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, Spanish footballer and manager (d. 1995)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/C%C3%A9sar_Rodr%C3%ADguez_%C3%81l<PERSON><PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, Spanish footballer and manager (d. 1995)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/C%C3%A9sar_Rodr%C3%ADguez_%C3%<PERSON><PERSON><PERSON><PERSON>"}]}, {"year": "1920", "text": "<PERSON>, American animator and producer (d. 2013)", "html": "1920 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American animator and producer (d. 2013)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American animator and producer (d. 2013)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1920", "text": "<PERSON>, Duchess of Bedford (d. 2012)", "html": "1920 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Duchess_of_Bedford\" title=\"<PERSON>, Duchess of Bedford\"><PERSON>, Duchess of Bedford</a> (d. 2012)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Duchess_of_Bedford\" title=\"<PERSON>, Duchess of Bedford\"><PERSON>, Duchess of Bedford</a> (d. 2012)", "links": [{"title": "<PERSON>, Duchess of Bedford", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Duchess_<PERSON>_Bedford"}]}, {"year": "1920", "text": "<PERSON>, British tibetologist (d. 2016)", "html": "1920 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, British tibetologist (d. 2016)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, British tibetologist (d. 2016)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1921", "text": "<PERSON><PERSON><PERSON><PERSON><PERSON>, French author and screenwriter (d. 2000)", "html": "1921 - <a href=\"https://wikipedia.org/wiki/Fr%C3%A9d%C3%A9<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON><PERSON></a>, French author and screenwriter (d. 2000)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Fr%C3%A9d%C3%A9<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON><PERSON></a>, French author and screenwriter (d. 2000)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Fr%C3%A9d%C3%A9ric_Dard"}]}, {"year": "1921", "text": "<PERSON>, English actress (d. 2013)", "html": "1921 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actress (d. 2013)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actress (d. 2013)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1921", "text": "<PERSON><PERSON><PERSON>, German businessman (d. 2009)", "html": "1921 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, German businessman (d. 2009)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, German businessman (d. 2009)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1921", "text": "<PERSON>, French-American race car driver (d. 1960)", "html": "1921 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French-American race car driver (d. 1960)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French-American race car driver (d. 1960)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1922", "text": "<PERSON>, American songwriter, bandleader, composer, conductor, arranger and pianist (d. 2001)", "html": "1922 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American songwriter, bandleader, composer, conductor, arranger and pianist (d. 2001)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American songwriter, bandleader, composer, conductor, arranger and pianist (d. 2001)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1922", "text": "<PERSON><PERSON><PERSON>, Serbian poet and academic (d. 1991)", "html": "1922 - <a href=\"https://wikipedia.org/wiki/Vasko_Popa\" title=\"Vasko Popa\"><PERSON><PERSON><PERSON></a>, Serbian poet and academic (d. 1991)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Vasko_Popa\" title=\"Vasko Popa\"><PERSON><PERSON><PERSON></a>, Serbian poet and academic (d. 1991)", "links": [{"title": "Vask<PERSON> Pop<PERSON>", "link": "https://wikipedia.org/wiki/Vasko_Popa"}]}, {"year": "1922", "text": "<PERSON>, Jr., American general (d. 2016)", "html": "1922 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Jr.\" class=\"mw-redirect\" title=\"<PERSON>, Jr.\"><PERSON>, Jr.</a>, American general (d. 2016)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>,_Jr.\" class=\"mw-redirect\" title=\"<PERSON>, Jr.\"><PERSON>, Jr.</a>, American general (d. 2016)", "links": [{"title": "<PERSON>, Jr.", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>,_Jr."}]}, {"year": "1923", "text": "<PERSON><PERSON>, Chinese-American composer and educator (d. 2019)", "html": "1923 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>-chung\" title=\"<PERSON><PERSON>-chung\"><PERSON><PERSON></a>, Chinese-American composer and educator (d. 2019)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>-chung\" title=\"<PERSON><PERSON>-chung\"><PERSON><PERSON></a>, Chinese-American composer and educator (d. 2019)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>-chung"}]}, {"year": "1924", "text": "<PERSON>, American composer and educator (d. 2015)", "html": "1924 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American composer and educator (d. 2015)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American composer and educator (d. 2015)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1924", "text": "<PERSON>, American pathologist and gerontologist (d. 2004)", "html": "1924 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American pathologist and gerontologist (d. 2004)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American pathologist and gerontologist (d. 2004)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1924", "text": "<PERSON>, American politician (d. 2018)", "html": "1924 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American politician (d. 2018)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American politician (d. 2018)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1925", "text": "<PERSON>, American World War II Medal of Honor recipient (d. 2019)", "html": "1925 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American World War II Medal of Honor recipient (d. 2019)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American World War II Medal of Honor recipient (d. 2019)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1925", "text": "<PERSON>, Italian journalist and politician, 11th President of Italy (d. 2023)", "html": "1925 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian journalist and politician, 11th <a href=\"https://wikipedia.org/wiki/President_of_Italy\" title=\"President of Italy\">President of Italy</a> (d. 2023)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian journalist and politician, 11th <a href=\"https://wikipedia.org/wiki/President_of_Italy\" title=\"President of Italy\">President of Italy</a> (d. 2023)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "President of Italy", "link": "https://wikipedia.org/wiki/President_of_Italy"}]}, {"year": "1925", "text": "<PERSON>, American dancer and author (d. 1999)", "html": "1925 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American dancer and author (d. 1999)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American dancer and author (d. 1999)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1925", "text": "<PERSON>, American actress (d. 2014)", "html": "1925 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress (d. 2014)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress (d. 2014)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1925", "text": "<PERSON>, American actress (d. 2021)", "html": "1925 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress (d. 2021)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress (d. 2021)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1926", "text": "<PERSON><PERSON><PERSON>, Kuwaiti ruler, 3rd Emir of Kuwait (d. 2006)", "html": "1926 - <a href=\"https://wikipedia.org/wiki/Jaber_<PERSON>-<PERSON>_<PERSON>-Sabah\" title=\"Jaber Al<PERSON>Sabah\"><PERSON><PERSON><PERSON></a>, Kuwaiti ruler, 3rd <a href=\"https://wikipedia.org/wiki/Emir_of_Kuwait\" title=\"Emir of Kuwait\">Emir of Kuwait</a> (d. 2006)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Jaber_<PERSON>-<PERSON>_<PERSON>-Sabah\" title=\"Jaber Al<PERSON>-Sabah\"><PERSON><PERSON><PERSON></a>, Kuwaiti ruler, 3rd <a href=\"https://wikipedia.org/wiki/Emir_of_Kuwait\" title=\"Emir of Kuwait\">Emir of Kuwait</a> (d. 2006)", "links": [{"title": "<PERSON><PERSON>r <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>"}, {"title": "Emir of Kuwait", "link": "https://wikipedia.org/wiki/Emir_of_Kuwait"}]}, {"year": "1926", "text": "<PERSON>, Jr., U.S lieutenant general (d. 2023)", "html": "1926 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>,_Jr.\" class=\"mw-redirect\" title=\"<PERSON>, Jr.\"><PERSON>, Jr.</a>, U.S lieutenant general (d. 2023)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>,_Jr.\" class=\"mw-redirect\" title=\"<PERSON>, Jr.\"><PERSON>, Jr.</a>, U.S lieutenant general (d. 2023)", "links": [{"title": "<PERSON>, Jr.", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>,_Jr."}]}, {"year": "1926", "text": "<PERSON>, Nova Scotia politician (d. 2021)", "html": "1926 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Nova Scotia politician (d. 2021)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Nova Scotia politician (d. 2021)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1926", "text": "<PERSON>, American professional baseball player (d. 2023)", "html": "1926 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(baseball)\" title=\"<PERSON> (baseball)\"><PERSON></a>, American professional baseball player (d. 2023)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(baseball)\" title=\"<PERSON> (baseball)\"><PERSON></a>, American professional baseball player (d. 2023)", "links": [{"title": "<PERSON> (baseball)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(baseball)"}]}, {"year": "1927", "text": "<PERSON>, Canadian director and screenwriter (d. 1999)", "html": "1927 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian director and screenwriter (d. 1999)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian director and screenwriter (d. 1999)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1927", "text": "<PERSON>, Canadian politician", "html": "1927 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>h%C3%A9r%C3%A8<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>h%C3%A9r%C3%A8<PERSON>_<PERSON><PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian politician", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Marie_Th%C3%A9r%C3%A8se_<PERSON>ens"}]}, {"year": "1928", "text": "<PERSON>, Scottish actor (d. 1999)", "html": "1928 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish actor (d. 1999)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish actor (d. 1999)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1928", "text": "<PERSON><PERSON><PERSON>, French author and illustrator (d. 2023)", "html": "1928 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, French author and illustrator (d. 2023)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, French author and illustrator (d. 2023)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>"}]}, {"year": "1928", "text": "<PERSON><PERSON>, Indonesian economist and politician (d. 2005)", "html": "1928 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Indonesian economist and politician (d. 2005)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Indonesian economist and politician (d. 2005)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1929", "text": "<PERSON>, American actress (d. 2019)", "html": "1929 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress (d. 2019)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress (d. 2019)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1929", "text": "<PERSON>, American weightlifter (d. 2021)", "html": "1929 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American weightlifter (d. 2021)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American weightlifter (d. 2021)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1929", "text": "<PERSON><PERSON>, Italian journalist and author (d. 2006)", "html": "1929 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Italian journalist and author (d. 2006)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Italian journalist and author (d. 2006)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Oriana_Fallaci"}]}, {"year": "1930", "text": "<PERSON>, German economist and politician, 6th Prime Minister of Lower Saxony (d. 2014)", "html": "1930 - <a href=\"https://wikipedia.org/wiki/<PERSON>(politician,_born_1930)\" title=\"<PERSON> (politician, born 1930)\"><PERSON></a>, German economist and politician, 6th <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Lower_Saxony\" class=\"mw-redirect\" title=\"Prime Minister of Lower Saxony\">Prime Minister of Lower Saxony</a> (d. 2014)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>(politician,_born_1930)\" title=\"<PERSON> (politician, born 1930)\"><PERSON></a>, German economist and politician, 6th <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Lower_Saxony\" class=\"mw-redirect\" title=\"Prime Minister of Lower Saxony\">Prime Minister of Lower Saxony</a> (d. 2014)", "links": [{"title": "<PERSON> (politician, born 1930)", "link": "https://wikipedia.org/wiki/<PERSON>_(politician,_born_1930)"}, {"title": "Prime Minister of Lower Saxony", "link": "https://wikipedia.org/wiki/Prime_Minister_of_Lower_Saxony"}]}, {"year": "1930", "text": "<PERSON>, American actor and producer (d. 2019)", "html": "1930 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor and producer (d. 2019)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor and producer (d. 2019)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1930", "text": "<PERSON>, American-Canadian actress and politician (d. 2023)", "html": "1930 - <a href=\"https://wikipedia.org/wiki/Viola_L%C3%A9ger\" title=\"<PERSON>\"><PERSON></a>, American-Canadian actress and politician (d. 2023)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Viola_L%C3%A9ger\" title=\"<PERSON> L<PERSON>\"><PERSON></a>, American-Canadian actress and politician (d. 2023)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Viola_L%C3%A9ger"}]}, {"year": "1930", "text": "<PERSON><PERSON><PERSON><PERSON>, Polish-French author and playwright (d. 2013)", "html": "1930 - <a href=\"https://wikipedia.org/wiki/S%C5%82awomir_Mro%C5%BCek\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Polish-French author and playwright (d. 2013)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/S%C5%82awomir_Mro%C5%BCek\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Polish-French author and playwright (d. 2013)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/S%C5%82awomir_Mro%C5%BCek"}]}, {"year": "1931", "text": "<PERSON><PERSON><PERSON>, Turkish author (d. 1983)", "html": "1931 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Turkish author (d. 1983)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Turkish author (d. 1983)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>k"}]}, {"year": "1932", "text": "<PERSON>, Baron <PERSON>, British jurist; Lord Chief Justice of Northern Ireland (d. 2020)", "html": "1932 - <a href=\"https://wikipedia.org/wiki/<PERSON>,_<PERSON>_<PERSON>\" title=\"<PERSON>, Baron <PERSON>\"><PERSON>, Baron <PERSON></a>, British jurist; <a href=\"https://wikipedia.org/wiki/Lord_Chief_Justice_of_Northern_Ireland\" title=\"Lord Chief Justice of Northern Ireland\">Lord Chief Justice of Northern Ireland</a> (d. 2020)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>,_<PERSON>_<PERSON>\" title=\"<PERSON>, Baron <PERSON>\"><PERSON>, Baron <PERSON></a>, British jurist; <a href=\"https://wikipedia.org/wiki/Lord_Chief_Justice_of_Northern_Ireland\" title=\"Lord Chief Justice of Northern Ireland\">Lord Chief Justice of Northern Ireland</a> (d. 2020)", "links": [{"title": "<PERSON>, <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_<PERSON>_<PERSON>"}, {"title": "Lord Chief Justice of Northern Ireland", "link": "https://wikipedia.org/wiki/Lord_Chief_Justice_of_Northern_Ireland"}]}, {"year": "1933", "text": "<PERSON>, American baseball player and manager (d. 2010)", "html": "1933 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(baseball)\" title=\"<PERSON> (baseball)\"><PERSON></a>, American baseball player and manager (d. 2010)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(baseball)\" title=\"<PERSON> (baseball)\"><PERSON></a>, American baseball player and manager (d. 2010)", "links": [{"title": "<PERSON> (baseball)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(baseball)"}]}, {"year": "1933", "text": "<PERSON>, American theologian and author (d. 2016)", "html": "1933 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(author)\" title=\"<PERSON> (author)\"><PERSON></a>, American theologian and author (d. 2016)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(author)\" title=\"<PERSON> (author)\"><PERSON></a>, American theologian and author (d. 2016)", "links": [{"title": "<PERSON> (author)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(author)"}]}, {"year": "1934", "text": "<PERSON>, American actor, director, and producer (d. 2010)", "html": "1934 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor, director, and producer (d. 2010)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor, director, and producer (d. 2010)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1935", "text": "<PERSON><PERSON><PERSON>, Greek captain and businessman (d. 2011)", "html": "1935 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>stant<PERSON>los\" class=\"mw-redirect\" title=\"V<PERSON><PERSON>\"><PERSON><PERSON><PERSON> <PERSON></a>, Greek captain and businessman (d. 2011)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"Vassilis <PERSON>\"><PERSON><PERSON><PERSON> <PERSON></a>, Greek captain and businessman (d. 2011)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON><PERSON>_<PERSON><PERSON><PERSON><PERSON>"}]}, {"year": "1935", "text": "<PERSON><PERSON><PERSON>, Japanese baseball player and manager (d. 2020)", "html": "1935 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese baseball player and manager (d. 2020)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese baseball player and manager (d. 2020)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1936", "text": "<PERSON><PERSON>, American baseball player (d. 2011)", "html": "1936 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>rew\" title=\"<PERSON>rmon Killebrew\"><PERSON><PERSON></a>, American baseball player (d. 2011)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_Kill<PERSON>rew\" title=\"<PERSON>rmon Killebrew\"><PERSON><PERSON></a>, American baseball player (d. 2011)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>ebrew"}]}, {"year": "1936", "text": "<PERSON>, Australian land rights activist (d. 1992)", "html": "1936 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian land rights activist (d. 1992)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Ma<PERSON>\"><PERSON></a>, Australian land rights activist (d. 1992)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1939", "text": "<PERSON>, Australian cricketer", "html": "1939 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>(cricketer)\" title=\"<PERSON> (cricketer)\"><PERSON></a>, Australian cricketer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>(cricketer)\" title=\"<PERSON> (cricketer)\"><PERSON></a>, Australian cricketer", "links": [{"title": "<PERSON> (cricketer)", "link": "https://wikipedia.org/wiki/<PERSON>(cricketer)"}]}, {"year": "1939", "text": "<PERSON><PERSON><PERSON>, Brazilian footballer and coach", "html": "1939 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>_Silveira\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON> Silveira\"><PERSON><PERSON><PERSON> Silveira</a>, Brazilian footballer and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>_Silveira\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON> Silveira\"><PERSON><PERSON><PERSON>lveira</a>, Brazilian footballer and coach", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1940", "text": "<PERSON><PERSON><PERSON><PERSON>, Russian composer", "html": "1940 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Russian composer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Russian composer", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1940", "text": "<PERSON>, Welsh rugby player and coach (d. 2021)", "html": "1940 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Welsh rugby player and coach (d. 2021)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Welsh rugby player and coach (d. 2021)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1941", "text": "<PERSON>, American baseball player", "html": "1941 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1941", "text": "<PERSON><PERSON>, Trinidadian-American activist (d. 1998)", "html": "1941 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Trinidadian-American activist (d. 1998)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Trinidadian-American activist (d. 1998)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1942", "text": "<PERSON>, English author and screenwriter", "html": "1942 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English author and screenwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English author and screenwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_Bingham"}]}, {"year": "1942", "text": "<PERSON>, Australian journalist and producer (d. 2019)", "html": "1942 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian journalist and producer (d. 2019)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian journalist and producer (d. 2019)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1943", "text": "<PERSON>, American singer (d. 2003)", "html": "1943 - <a href=\"https://wikipedia.org/wiki/<PERSON>_Eva\" title=\"<PERSON> Eva\"><PERSON> Eva</a>, American singer (d. 2003)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_Eva\" title=\"Little Eva\"><PERSON> Eva</a>, American singer (d. 2003)", "links": [{"title": "Little Eva", "link": "https://wikipedia.org/wiki/<PERSON>_Eva"}]}, {"year": "1943", "text": "<PERSON>, French entrepreneur and chairman of Montpellier HSC (d. 2017)", "html": "1943 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French entrepreneur and chairman of <a href=\"https://wikipedia.org/wiki/Montpellier_HSC\" title=\"Montpellier HSC\">Montpellier HSC</a> (d. 2017)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French entrepreneur and chairman of <a href=\"https://wikipedia.org/wiki/Montpellier_HSC\" title=\"Montpellier HSC\">Montpellier HSC</a> (d. 2017)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Montpellier HSC", "link": "https://wikipedia.org/wiki/Montpellier_HSC"}]}, {"year": "1944", "text": "<PERSON>, American actor", "html": "1944 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1944", "text": "<PERSON>, American football player (d. 2021)", "html": "1944 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player (d. 2021)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player (d. 2021)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1944", "text": "<PERSON><PERSON>, Spanish economist, academic, and politician", "html": "1944 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Spanish economist, academic, and politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Spanish economist, academic, and politician", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>-<PERSON>"}]}, {"year": "1944", "text": "<PERSON><PERSON>, American cardinal", "html": "1944 - <a href=\"https://wikipedia.org/wiki/Se%C3%A1n_<PERSON>_<PERSON>%27Malley\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American cardinal", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Se%C3%A1n_<PERSON>_<PERSON>%27Malley\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American cardinal", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Se%C3%A1n_<PERSON>_O%27Malley"}]}, {"year": "1945", "text": "<PERSON><PERSON><PERSON>, Sri Lankan journalist and politician, 5th President of Sri Lanka", "html": "1945 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Sri Lankan journalist and politician, 5th <a href=\"https://wikipedia.org/wiki/President_of_Sri_Lanka\" title=\"President of Sri Lanka\">President of Sri Lanka</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Sri Lankan journalist and politician, 5th <a href=\"https://wikipedia.org/wiki/President_of_Sri_Lanka\" title=\"President of Sri Lanka\">President of Sri Lanka</a>", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>a"}, {"title": "President of Sri Lanka", "link": "https://wikipedia.org/wiki/President_of_Sri_Lanka"}]}, {"year": "1946", "text": "<PERSON>, Panamanian politician, 33rd President of Panama", "html": "1946 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A<PERSON><PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Panamanian politician, 33rd <a href=\"https://wikipedia.org/wiki/President_of_Panama\" class=\"mw-redirect\" title=\"President of Panama\">President of Panama</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A<PERSON><PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Panamanian politician, 33rd <a href=\"https://wikipedia.org/wiki/President_of_Panama\" class=\"mw-redirect\" title=\"President of Panama\">President of Panama</a>", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Ernesto_P%C3%A9<PERSON>_<PERSON>ares"}, {"title": "President of Panama", "link": "https://wikipedia.org/wiki/President_of_Panama"}]}, {"year": "1946", "text": "<PERSON><PERSON>, Swiss fashion designer (d. 2004)", "html": "1946 - <a href=\"https://wikipedia.org/wiki/E<PERSON>_<PERSON>_<PERSON>%C3%BCrstenberg\" class=\"mw-redirect\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Swiss fashion designer (d. 2004)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>%C3%BCrstenberg\" class=\"mw-redirect\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Swiss fashion designer (d. 2004)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Egon_von_F%C3%BCrstenberg"}]}, {"year": "1947", "text": "<PERSON>, American actor and screenwriter (d. 2024)", "html": "1947 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(comedian)\" title=\"<PERSON> (comedian)\"><PERSON></a>, American actor and screenwriter (d. 2024)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(comedian)\" title=\"<PERSON> (comedian)\"><PERSON></a>, American actor and screenwriter (d. 2024)", "links": [{"title": "<PERSON> (comedian)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(comedian)"}]}, {"year": "1948", "text": "<PERSON>, South African-Dutch saxophonist and flute player (d. 2012)", "html": "1948 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, South African-Dutch saxophonist and flute player (d. 2012)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, South African-Dutch saxophonist and flute player (d. 2012)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1948", "text": "<PERSON>, American actor and politician", "html": "1948 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor and politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor and politician", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1948", "text": "<PERSON>, English drummer, songwriter, and producer", "html": "1948 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English drummer, songwriter, and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English drummer, songwriter, and producer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1948", "text": "<PERSON><PERSON>, <PERSON>, Kenyan-English politician", "html": "1948 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>,_Baroness_<PERSON>\" title=\"<PERSON><PERSON>, <PERSON>\"><PERSON><PERSON>, <PERSON></a>, Kenyan-English politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>,_Baroness_<PERSON>\" title=\"<PERSON><PERSON>, <PERSON>\"><PERSON><PERSON>, <PERSON></a>, Kenyan-English politician", "links": [{"title": "<PERSON><PERSON>, <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>,_<PERSON>_<PERSON>"}]}, {"year": "1949", "text": "<PERSON>, American football player and sportscaster", "html": "1949 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player and sportscaster", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player and sportscaster", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1949", "text": "<PERSON>, Spanish anesthesiologist and politician, 116th Mayor of Barcelona", "html": "1949 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Spanish anesthesiologist and politician, 116th <a href=\"https://wikipedia.org/wiki/Mayor_of_Barcelona\" class=\"mw-redirect\" title=\"Mayor of Barcelona\">Mayor of Barcelona</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Spanish anesthesiologist and politician, 116th <a href=\"https://wikipedia.org/wiki/Mayor_of_Barcelona\" class=\"mw-redirect\" title=\"Mayor of Barcelona\">Mayor of Barcelona</a>", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Mayor of Barcelona", "link": "https://wikipedia.org/wiki/Mayor_of_Barcelona"}]}, {"year": "1949", "text": "<PERSON>, American lawyer and politician, 27th United States Secretary of Agriculture", "html": "1949 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and politician, 27th <a href=\"https://wikipedia.org/wiki/United_States_Secretary_of_Agriculture\" title=\"United States Secretary of Agriculture\">United States Secretary of Agriculture</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and politician, 27th <a href=\"https://wikipedia.org/wiki/United_States_Secretary_of_Agriculture\" title=\"United States Secretary of Agriculture\">United States Secretary of Agriculture</a>", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "United States Secretary of Agriculture", "link": "https://wikipedia.org/wiki/United_States_Secretary_of_Agriculture"}]}, {"year": "1950", "text": "<PERSON>, American illustrator", "html": "1950 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American illustrator", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American illustrator", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1950", "text": "<PERSON>, American singer and songwriter", "html": "1950 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer and songwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer and songwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1950", "text": "<PERSON>, American artist", "html": "1950 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American artist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American artist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1951", "text": "<PERSON>, American sportscaster (d. 2016)", "html": "1951 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American sportscaster (d. 2016)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American sportscaster (d. 2016)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1953", "text": "<PERSON>, American singer-songwriter and guitarist", "html": "1953 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and guitarist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and guitarist", "links": [{"title": "Don Dokken", "link": "https://wikipedia.org/wiki/Don_<PERSON>"}]}, {"year": "1953", "text": "<PERSON>, Scottish-Australian singer and guitarist", "html": "1953 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish-Australian singer and guitarist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish-Australian singer and guitarist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1954", "text": "<PERSON>, American baseball player and coach", "html": "1954 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player and coach", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1954", "text": "<PERSON><PERSON><PERSON>, Brazilian footballer, coach, and manager", "html": "1954 - <a href=\"https://wikipedia.org/wiki/L%C3%A9o_J%C3%BAnior\" title=\"Léo Júnior\"><PERSON><PERSON><PERSON></a>, Brazilian footballer, coach, and manager", "no_year_html": "<a href=\"https://wikipedia.org/wiki/L%C3%A9o_J%C3%BAnior\" title=\"Léo Júnior\"><PERSON><PERSON><PERSON></a>, Brazilian footballer, coach, and manager", "links": [{"title": "Léo Júnior", "link": "https://wikipedia.org/wiki/L%C3%A9o_J%C3%BAnior"}]}, {"year": "1955", "text": "<PERSON>, American colonel, pilot, and astronaut", "html": "1955 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American colonel, pilot, and astronaut", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American colonel, pilot, and astronaut", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1956", "text": "<PERSON>, English economist and businessman", "html": "1956 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English economist and businessman", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English economist and businessman", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1956", "text": "<PERSON>, American illustrator and painter", "html": "1956 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, American illustrator and painter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, American illustrator and painter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1956", "text": "<PERSON>, Dominican baseball player and manager", "html": "1956 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(baseball,_born_1956)\" class=\"mw-redirect\" title=\"<PERSON> (baseball, born 1956)\"><PERSON></a>, Dominican baseball player and manager", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(baseball,_born_1956)\" class=\"mw-redirect\" title=\"<PERSON> (baseball, born 1956)\"><PERSON></a>, Dominican baseball player and manager", "links": [{"title": "<PERSON> (baseball, born 1956)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(baseball,_born_1956)"}]}, {"year": "1956", "text": "<PERSON>, Portuguese lawyer and politician, 118th Prime Minister of Portugal", "html": "1956 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Portuguese lawyer and politician, 118th <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Portugal\" title=\"Prime Minister of Portugal\">Prime Minister of Portugal</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Portuguese lawyer and politician, 118th <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Portugal\" title=\"Prime Minister of Portugal\">Prime Minister of Portugal</a>", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "Prime Minister of Portugal", "link": "https://wikipedia.org/wiki/Prime_Minister_of_Portugal"}]}, {"year": "1956", "text": "<PERSON><PERSON><PERSON>, Belarusian footballer and manager (d. 2012)", "html": "1956 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Belarusian footballer and manager (d. 2012)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Belarusian footballer and manager (d. 2012)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1957", "text": "<PERSON><PERSON><PERSON><PERSON><PERSON>, Turkmen dentist and politician, 2nd President of Turkmenistan", "html": "1957 - <a href=\"https://wikipedia.org/wiki/Gurbanguly_Berdimuhamedow\" title=\"Gurbanguly Berdimuhamedow\"><PERSON><PERSON><PERSON><PERSON><PERSON></a>, Turkmen dentist and politician, 2nd <a href=\"https://wikipedia.org/wiki/President_of_Turkmenistan\" title=\"President of Turkmenistan\">President of Turkmenistan</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Gurbanguly_Berdimuhamedow\" title=\"Gurbang<PERSON>y Berdimuhamedow\"><PERSON><PERSON><PERSON><PERSON><PERSON></a>, Turkmen dentist and politician, 2nd <a href=\"https://wikipedia.org/wiki/President_of_Turkmenistan\" title=\"President of Turkmenistan\">President of Turkmenistan</a>", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/G<PERSON><PERSON><PERSON><PERSON>_Berdi<PERSON>dow"}, {"title": "President of Turkmenistan", "link": "https://wikipedia.org/wiki/President_of_Turkmenistan"}]}, {"year": "1957", "text": "<PERSON>, Cuban-Venezuelan singer and actress", "html": "1957 - <a href=\"https://wikipedia.org/wiki/Mar%C3%<PERSON><PERSON>_<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Cuban-Venezuelan singer and actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Mar%C3%<PERSON><PERSON>_<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Cuban-Venezuelan singer and actress", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Mar%C3%<PERSON><PERSON>_<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1957", "text": "<PERSON>, Australian singer-songwriter and guitarist", "html": "1957 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(musician)\" title=\"<PERSON> (musician)\"><PERSON></a>, Australian singer-songwriter and guitarist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>(musician)\" title=\"<PERSON> (musician)\"><PERSON></a>, Australian singer-songwriter and guitarist", "links": [{"title": "<PERSON> (musician)", "link": "https://wikipedia.org/wiki/<PERSON>(musician)"}]}, {"year": "1957", "text": "<PERSON>, American politician, 98th Mayor of Philadelphia", "html": "1957 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American politician, 98th <a href=\"https://wikipedia.org/wiki/Mayor_of_Philadelphia\" title=\"Mayor of Philadelphia\">Mayor of Philadelphia</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American politician, 98th <a href=\"https://wikipedia.org/wiki/Mayor_of_Philadelphia\" title=\"Mayor of Philadelphia\">Mayor of Philadelphia</a>", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Mayor of Philadelphia", "link": "https://wikipedia.org/wiki/Mayor_of_Philadelphia"}]}, {"year": "1957", "text": "<PERSON>, English physicist and academic", "html": "1957 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English physicist and academic", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English physicist and academic", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1958", "text": "<PERSON><PERSON>, German politician", "html": "1958 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, German politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, German politician", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1958", "text": "<PERSON>, Portuguese runner", "html": "1958 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Portuguese runner", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Portuguese runner", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1961", "text": "<PERSON>, American actress, singer, and dancer", "html": "1961 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress, singer, and dancer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress, singer, and dancer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1962", "text": "<PERSON>, English actress", "html": "1962 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actress", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1962", "text": "<PERSON>, Spanish lawyer and politician", "html": "1962 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Spanish lawyer and politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Spanish lawyer and politician", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1962", "text": "<PERSON>, American colonel, pilot, and astronaut", "html": "1962 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American colonel, pilot, and astronaut", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American colonel, pilot, and astronaut", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1963", "text": "<PERSON><PERSON><PERSON>, German violinist", "html": "1963 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, German violinist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, German violinist", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>"}]}, {"year": "1963", "text": "<PERSON>, American actress and educator", "html": "1963 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress and educator", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress and educator", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1964", "text": "<PERSON><PERSON><PERSON>, English singer-songwriter and dancer", "html": "1964 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, English singer-songwriter and dancer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, English singer-songwriter and dancer", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1965", "text": "<PERSON><PERSON>, American guitarist", "html": "1965 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>n\" title=\"<PERSON><PERSON>n\"><PERSON><PERSON></a>, American guitarist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>n\" title=\"<PERSON><PERSON> E<PERSON>n\"><PERSON><PERSON></a>, American guitarist", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>isen"}]}, {"year": "1965", "text": "<PERSON>, English cricketer", "html": "1965 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English cricketer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English cricketer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1966", "text": "<PERSON><PERSON>, Japanese author and comic artist", "html": "1966 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Japanese author and comic artist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Japanese author and comic artist", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1967", "text": "<PERSON>, American race car driver", "html": "1967 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American race car driver", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American race car driver", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1967", "text": "<PERSON><PERSON>, American actress and singer", "html": "1967 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American actress and singer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American actress and singer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>in"}]}, {"year": "1967", "text": "<PERSON><PERSON>, Northern Irish cinematographer", "html": "1967 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Northern Irish cinematographer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Northern Irish cinematographer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1968", "text": "<PERSON>, American actor and musician", "html": "1968 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%27A<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American actor and musician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%27<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON><PERSON></a>, American actor and musician", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>%27A<PERSON><PERSON>_<PERSON>"}]}, {"year": "1968", "text": "<PERSON><PERSON>, Canadian ice hockey player", "html": "1968 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Canadian ice hockey player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Canadian ice hockey player", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1969", "text": "<PERSON>, Canadian politician (d. 2010)", "html": "1969 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A<PERSON><PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian politician (d. 2010)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A<PERSON><PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian politician (d. 2010)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Claude_B%C3%A9chard"}]}, {"year": "1969", "text": "<PERSON><PERSON><PERSON>, Greek footballer and manager", "html": "1969 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Greek footballer and manager", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Greek footballer and manager", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1969", "text": "<PERSON><PERSON><PERSON>, Japanese lawyer and politician", "html": "1969 - <a href=\"https://wikipedia.org/wiki/T%C5%8D<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese lawyer and politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/T%C5%8D<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese lawyer and politician", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/T%C5%8Dru_<PERSON><PERSON><PERSON>"}]}, {"year": "1970", "text": "<PERSON>, German sprinter", "html": "1970 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German sprinter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German sprinter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1970", "text": "<PERSON>, American actress and singer", "html": "1970 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(actress,_born_1970)\" title=\"<PERSON> (actress, born 1970)\"><PERSON></a>, American actress and singer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(actress,_born_1970)\" title=\"<PERSON> (actress, born 1970)\"><PERSON></a>, American actress and singer", "links": [{"title": "<PERSON> (actress, born 1970)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(actress,_born_1970)"}]}, {"year": "1971", "text": "<PERSON>, Canadian singer-songwriter and guitarist", "html": "1971 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian singer-songwriter and guitarist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Good\"><PERSON></a>, Canadian singer-songwriter and guitarist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1973", "text": "<PERSON>, American actor", "html": "1973 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Lance Barber\"><PERSON></a>, American actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Lance Barber\"><PERSON></a>, American actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1973", "text": "<PERSON>, American cyclist", "html": "1973 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American cyclist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American cyclist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1976", "text": "<PERSON>, Swedish race car driver", "html": "1976 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(rally_driver)\" title=\"<PERSON> (rally driver)\"><PERSON></a>, Swedish race car driver", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(rally_driver)\" title=\"<PERSON> (rally driver)\"><PERSON></a>, Swedish race car driver", "links": [{"title": "<PERSON> (rally driver)", "link": "https://wikipedia.org/wiki/<PERSON>_(rally_driver)"}]}, {"year": "1976", "text": "<PERSON><PERSON>, New Zealand comedian, actor, musician, songwriter, and producer", "html": "1976 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> <PERSON>\"><PERSON><PERSON></a>, New Zealand comedian, actor, musician, songwriter, and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> <PERSON>\"><PERSON><PERSON></a>, New Zealand comedian, actor, musician, songwriter, and producer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1977", "text": "<PERSON><PERSON><PERSON>, Greek footballer", "html": "1977 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Greek footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Greek footballer", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1977", "text": "<PERSON><PERSON><PERSON><PERSON>, English actress", "html": "1977 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, English actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, English actress", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1978", "text": "<PERSON>, American singer-songwriter, dancer, and actress", "html": "1978 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter, dancer, and actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter, dancer, and actress", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1979", "text": "<PERSON>, Australian footballer", "html": "1979 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1979", "text": "<PERSON>, English footballer", "html": "1979 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%27B<PERSON>_(footballer)\" title=\"<PERSON> (footballer)\"><PERSON></a>, English footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%27B<PERSON>_(footballer)\" title=\"<PERSON> (footballer)\"><PERSON></a>, English footballer", "links": [{"title": "<PERSON> (footballer)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>%27B<PERSON>_(footballer)"}]}, {"year": "1979", "text": "<PERSON><PERSON>, Dutch swimmer", "html": "1979 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Dutch swimmer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Dutch swimmer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1980", "text": "<PERSON>, Welsh soprano and actress", "html": "1980 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Welsh soprano and actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Welsh soprano and actress", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1981", "text": "<PERSON>, Australian rugby league player", "html": "1981 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian rugby league player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian rugby league player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1981", "text": "<PERSON>, American basketball player", "html": "1981 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(basketball)\" title=\"<PERSON> (basketball)\"><PERSON></a>, American basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(basketball)\" title=\"<PERSON> (basketball)\"><PERSON></a>, American basketball player", "links": [{"title": "<PERSON> (basketball)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(basketball)"}]}, {"year": "1981", "text": "<PERSON><PERSON><PERSON>, Argentinian race car driver (d. 2005)", "html": "1981 - <a href=\"https://wikipedia.org/wiki/Nicol%C3%<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Argentinian race car driver (d. 2005)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Nicol%C3%<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Argentinian race car driver (d. 2005)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Nicol%C3%<PERSON><PERSON>_<PERSON><PERSON>"}]}, {"year": "1981", "text": "<PERSON><PERSON><PERSON><PERSON>, American rabbi, author, and educator", "html": "1981 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, American rabbi, author, and educator", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, American rabbi, author, and educator", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1982", "text": "<PERSON>, American comedian", "html": "1982 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American comedian", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American comedian", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1982", "text": "<PERSON>, American baseball player", "html": "1982 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(baseball)\" title=\"<PERSON> (baseball)\"><PERSON></a>, American baseball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(baseball)\" title=\"<PERSON> (baseball)\"><PERSON></a>, American baseball player", "links": [{"title": "<PERSON> (baseball)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(baseball)"}]}, {"year": "1982", "text": "<PERSON>, American actress", "html": "1982 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1982", "text": "<PERSON><PERSON> <PERSON><PERSON>, American sprinter", "html": "1982 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"O<PERSON> <PERSON><PERSON>\"><PERSON><PERSON> <PERSON><PERSON></a>, American sprinter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"O<PERSON> <PERSON><PERSON>\"><PERSON><PERSON> <PERSON><PERSON></a>, American sprinter", "links": [{"title": "<PERSON><PERSON> <PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "1983", "text": "<PERSON><PERSON><PERSON>, American singer-songwriter and dancer", "html": "1983 - <a href=\"https://wikipedia.org/wiki/Aundrea_Fimbres\" title=\"Aundrea Fimbres\"><PERSON><PERSON><PERSON></a>, American singer-songwriter and dancer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Aundrea_Fimbres\" title=\"Aundrea Fimbres\"><PERSON><PERSON><PERSON></a>, American singer-songwriter and dancer", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Aundrea_Fimbres"}]}, {"year": "1983", "text": "<PERSON>, American cyclist", "html": "1983 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American cyclist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American cyclist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1984", "text": "<PERSON>, Russian high jumper", "html": "1984 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian high jumper", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian high jumper", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1985", "text": "<PERSON><PERSON><PERSON>, American football player", "html": "1985 - <a href=\"https://wikipedia.org/wiki/Quintin_<PERSON><PERSON>\" title=\"Quintin <PERSON>\"><PERSON><PERSON><PERSON></a>, American football player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Quintin_<PERSON><PERSON>\" title=\"Quintin <PERSON>\"><PERSON><PERSON><PERSON></a>, American football player", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Quintin_<PERSON><PERSON>"}]}, {"year": "1986", "text": "<PERSON>, Spanish footballer", "html": "1986 - <a href=\"https://wikipedia.org/wiki/Jos%C3%A<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Spanish footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Jos%C3%A<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Spanish footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Jos%C3%A9_<PERSON>_<PERSON>"}]}, {"year": "1986", "text": "<PERSON>, Romanian singer-songwriter and producer", "html": "1986 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Edward <PERSON>\"><PERSON></a>, Romanian singer-songwriter and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Edward Maya\"><PERSON></a>, Romanian singer-songwriter and producer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1988", "text": "<PERSON><PERSON>, Argentinian footballer", "html": "1988 - <a href=\"https://wikipedia.org/wiki/%C3%89ver_Banega\" title=\"Éver Banega\"><PERSON><PERSON></a>, Argentinian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/%C3%89ver_Banega\" title=\"Éver Banega\"><PERSON><PERSON></a>, Argentinian footballer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/%C3%89ver_Banega"}]}, {"year": "1990", "text": "<PERSON>, Scottish footballer", "html": "1990 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1990", "text": "<PERSON><PERSON>, French footballer", "html": "1990 - <a href=\"https://wikipedia.org/wiki/Yan<PERSON>_<PERSON>%27Vila\" title=\"<PERSON><PERSON> M<PERSON>V<PERSON>\"><PERSON><PERSON></a>, French footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Yan<PERSON>_<PERSON>%27Vila\" title=\"<PERSON><PERSON> M<PERSON>V<PERSON>\"><PERSON><PERSON></a>, French footballer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Yann_M%27Vila"}]}, {"year": "1991", "text": "<PERSON><PERSON>, South Korean footballer", "html": "1991 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>-jun\" title=\"<PERSON><PERSON>-jun\"><PERSON><PERSON>-jun</a>, South Korean footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>-jun\" title=\"<PERSON><PERSON>-jun\"><PERSON><PERSON>-jun</a>, South Korean footballer", "links": [{"title": "<PERSON><PERSON>jun", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>-jun"}]}, {"year": "1991", "text": "<PERSON><PERSON><PERSON>, American basketball player", "html": "1991 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON> <PERSON>\"><PERSON><PERSON><PERSON> <PERSON></a>, American basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON> <PERSON>\"><PERSON><PERSON><PERSON></a>, American basketball player", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Kawhi_<PERSON>"}]}, {"year": "1991", "text": "<PERSON>, American actress", "html": "1991 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1993", "text": "<PERSON>, Australian actor", "html": "1993 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1993", "text": "<PERSON>, American singer-songwriter", "html": "1993 - <a href=\"https://wikipedia.org/wiki/<PERSON>_Tree\" title=\"Oliver Tree\"><PERSON></a>, American singer-songwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_Tree\" title=\"Oliver Tree\"><PERSON></a>, American singer-songwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1994", "text": "<PERSON><PERSON>, American actress and model", "html": "1994 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American actress and model", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American actress and model", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Camila_Mendes"}]}, {"year": "1996", "text": "<PERSON>, New Zealand rugby league player", "html": "1996 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, New Zealand rugby league player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, New Zealand rugby league player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1998", "text": "<PERSON>, American basketball player", "html": "1998 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>.\" title=\"<PERSON> Jr.\"><PERSON>.</a>, American basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>.\" title=\"<PERSON> Jr.\"><PERSON>.</a>, American basketball player", "links": [{"title": "<PERSON>.", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>."}]}, {"year": "2001", "text": "<PERSON>, American basketball player", "html": "2001 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2001", "text": "<PERSON><PERSON>, American baseball player", "html": "2001 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American baseball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American baseball player", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "2001", "text": "<PERSON>, Australian rugby league player", "html": "2001 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian rugby league player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian rugby league player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2002", "text": "<PERSON>, Canadian ice hockey player", "html": "2002 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2003", "text": "<PERSON>, English footballer", "html": "2003 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2006", "text": "<PERSON>, American child voice actor", "html": "2006 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American child voice actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American child voice actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}], "Deaths": [{"year": "226", "text": "<PERSON>, Chinese emperor (b. 187)", "html": "226 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Cao Pi\"><PERSON></a>, Chinese emperor (b. 187)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Cao Pi\"><PERSON></a>, Chinese emperor (b. 187)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Cao_<PERSON>"}]}, {"year": "884", "text": "<PERSON>, general of the Tang Dynasty", "html": "884 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, general of the Tang Dynasty", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, general of the Tang Dynasty", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "976", "text": "<PERSON><PERSON>, archbishop of Cologne", "html": "976 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_(archbishop_of_Cologne)\" title=\"<PERSON><PERSON> (archbishop of Cologne)\"><PERSON><PERSON></a>, archbishop of <a href=\"https://wikipedia.org/wiki/Cologne\" title=\"Cologne\">Cologne</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_(archbishop_of_Cologne)\" title=\"<PERSON><PERSON> (archbishop of Cologne)\"><PERSON><PERSON></a>, archbishop of <a href=\"https://wikipedia.org/wiki/Cologne\" title=\"Cologne\">Cologne</a>", "links": [{"title": "<PERSON><PERSON> (archbishop of Cologne)", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_(archbishop_of_Cologne)"}, {"title": "Cologne", "link": "https://wikipedia.org/wiki/Cologne"}]}, {"year": "1059", "text": "<PERSON>, Duke of Saxony (b. 995)", "html": "1059 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Duke_of_Saxony\" title=\"<PERSON>, Duke of Saxony\"><PERSON>, Duke of Saxony</a> (b. 995)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Duke_of_Saxony\" title=\"<PERSON>, Duke of Saxony\"><PERSON>, Duke of Saxony</a> (b. 995)", "links": [{"title": "<PERSON>, Duke of Saxony", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Duke_of_Saxony"}]}, {"year": "1149", "text": "<PERSON> Po<PERSON>ers, Prince of Antioch (b. 1115)", "html": "1149 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON> of Poitiers\"><PERSON></a>, Prince of Antioch (b. 1115)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON> of Poitiers\"><PERSON></a>, Prince of Antioch (b. 1115)", "links": [{"title": "<PERSON> of Poitiers", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_Po<PERSON>"}]}, {"year": "1153", "text": "<PERSON><PERSON><PERSON><PERSON><PERSON>, King of the Isles", "html": "1153 - <a href=\"https://wikipedia.org/wiki/%C3%93l%C3%A1fr_Gu%C3%B0r%C3%B8%C3%B0<PERSON><PERSON>_(died_1153)\" title=\"<PERSON><PERSON><PERSON><PERSON><PERSON> (died 1153)\"><PERSON><PERSON><PERSON><PERSON><PERSON></a>, King of the Isles", "no_year_html": "<a href=\"https://wikipedia.org/wiki/%C3%93l%C3%A1fr_Gu%C3%B0r%C3%B8%C3%B0<PERSON><PERSON>_(died_1153)\" title=\"<PERSON><PERSON><PERSON><PERSON><PERSON> (died 1153)\"><PERSON><PERSON><PERSON><PERSON><PERSON></a>, King of the Isles", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON><PERSON> (died 1153)", "link": "https://wikipedia.org/wiki/%C3%93l%C3%A1fr_Gu%C3%B0r%C3%B8%C3%B0arson_(died_1153)"}]}, {"year": "1252", "text": "<PERSON>, King of Denmark (b. 1218)", "html": "1252 - <a href=\"https://wikipedia.org/wiki/<PERSON>,_King_of_Denmark\" title=\"<PERSON>, King of Denmark\"><PERSON>, King of Denmark</a> (b. 1218)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>,_King_of_Denmark\" title=\"<PERSON>, King of Denmark\"><PERSON>, King of Denmark</a> (b. 1218)", "links": [{"title": "<PERSON>, King of Denmark", "link": "https://wikipedia.org/wiki/<PERSON>,_King_of_Denmark"}]}, {"year": "1293", "text": "<PERSON> of Ghent, philosopher (b. c.1217)", "html": "1293 - <a href=\"https://wikipedia.org/wiki/Henry_of_Ghent\" title=\"<PERSON> of Ghent\"><PERSON> of Ghent</a>, philosopher (b. c.1217)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Henry_of_Ghent\" title=\"<PERSON> of Ghent\"><PERSON> of Ghent</a>, philosopher (b. c.1217)", "links": [{"title": "<PERSON> of Ghent", "link": "https://wikipedia.org/wiki/Henry_of_Ghent"}]}, {"year": "1315", "text": "<PERSON>, Spanish philosopher (b. 1235)", "html": "1315 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Spanish philosopher (b. 1235)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Spanish philosopher (b. 1235)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1344", "text": "<PERSON> of Savoy, duchess consort of Brittany, throne claimant of Savoy (b. 1310)", "html": "1344 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_Savoy\" title=\"<PERSON> of Savoy\"><PERSON> of Savoy</a>, duchess consort of Brittany, throne claimant of Savoy (b. 1310)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON> of Savoy\"><PERSON> of Savoy</a>, duchess consort of Brittany, throne claimant of Savoy (b. 1310)", "links": [{"title": "<PERSON> of Savoy", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1374", "text": "<PERSON> of Kroměříž, Czech priest and reformer", "html": "1374 - <a href=\"https://wikipedia.org/wiki/Jan_<PERSON>l%C3%AD%C4%8D\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON> of Kroměříž</a>, <a href=\"https://wikipedia.org/wiki/Czechs\" title=\"Czechs\">Czech</a> <a href=\"https://wikipedia.org/wiki/Priest\" title=\"Priest\">priest</a> and <a href=\"https://wikipedia.org/wiki/Reform_movement\" class=\"mw-redirect\" title=\"Reform movement\">reformer</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%AD%C4%8D\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON> of Kroměříž</a>, <a href=\"https://wikipedia.org/wiki/Czechs\" title=\"Czechs\">Czech</a> <a href=\"https://wikipedia.org/wiki/Priest\" title=\"Priest\">priest</a> and <a href=\"https://wikipedia.org/wiki/Reform_movement\" class=\"mw-redirect\" title=\"Reform movement\">reformer</a>", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Jan_Mil%C3%AD%C4%8D"}, {"title": "Czechs", "link": "https://wikipedia.org/wiki/Czechs"}, {"title": "Priest", "link": "https://wikipedia.org/wiki/Priest"}, {"title": "Reform movement", "link": "https://wikipedia.org/wiki/Reform_movement"}]}, {"year": "1432", "text": "<PERSON><PERSON> of Cyprus (b. 1375)", "html": "1432 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_of_Cyprus\" class=\"mw-redirect\" title=\"<PERSON><PERSON> of Cyprus\"><PERSON><PERSON> of Cyprus</a> (b. 1375)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_of_Cyprus\" class=\"mw-redirect\" title=\"<PERSON><PERSON> of Cyprus\"><PERSON><PERSON> of Cyprus</a> (b. 1375)", "links": [{"title": "<PERSON><PERSON> of Cyprus", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_of_Cyprus"}]}, {"year": "1509", "text": "<PERSON>, Countess of Richmond and Derby (b. 1443)", "html": "1509 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Countess_of_Richmond_and_Derby\" class=\"mw-redirect\" title=\"<PERSON>, Countess of Richmond and Derby\"><PERSON>, Countess of Richmond and Derby</a> (b. 1443)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Countess_of_Richmond_and_Derby\" class=\"mw-redirect\" title=\"<PERSON>, Countess of Richmond and Derby\"><PERSON>, Countess of Richmond and Derby</a> (b. 1443)", "links": [{"title": "<PERSON>, Countess of Richmond and Derby", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Countess_of_Richmond_and_Derby"}]}, {"year": "1520", "text": "Mo<PERSON><PERSON><PERSON> <PERSON>, Aztec ruler (b. 1466)", "html": "1520 - <a href=\"https://wikipedia.org/wiki/Moctezuma_II\" title=\"Moctezuma II\">Moctezuma II</a>, Aztec ruler (b. 1466)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Moctezuma_II\" title=\"Moctezuma II\">Moctezuma II</a>, Aztec ruler (b. 1466)", "links": [{"title": "Moctezuma II", "link": "https://wikipedia.org/wiki/Moctezuma_II"}]}, {"year": "1575", "text": "<PERSON>, Japanese samurai (b. 1515)", "html": "1575 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Baba Nobuharu\"><PERSON></a>, Japanese samurai (b. 1515)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Baba Nobuharu\"><PERSON></a>, Japanese samurai (b. 1515)", "links": [{"title": "Baba Nobuharu", "link": "https://wikipedia.org/wiki/Baba_<PERSON>u"}]}, {"year": "1594", "text": "<PERSON><PERSON>, Danish politician, Chancellor of Denmark (b. 1535)", "html": "1594 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Danish politician, <a href=\"https://wikipedia.org/wiki/Chancellor_of_Denmark\" class=\"mw-redirect\" title=\"Chancellor of Denmark\">Chancellor of Denmark</a> (b. 1535)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Danish politician, <a href=\"https://wikipedia.org/wiki/Chancellor_of_Denmark\" class=\"mw-redirect\" title=\"Chancellor of Denmark\">Chancellor of Denmark</a> (b. 1535)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}, {"title": "Chancellor of Denmark", "link": "https://wikipedia.org/wiki/Chancellor_of_Denmark"}]}, {"year": "1626", "text": "<PERSON><PERSON><PERSON>, Italian cardinal and archivist (b. 1564)", "html": "1626 - <a href=\"https://wikipedia.org/wiki/Scipione_Cobelluzzi\" title=\"Scipione Cobelluzzi\"><PERSON><PERSON><PERSON> Cobelluzzi</a>, Italian cardinal and archivist (b. 1564)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Scipione_Cobelluzzi\" title=\"Scipione Cobelluzzi\"><PERSON><PERSON><PERSON> Cobelluzzi</a>, Italian cardinal and archivist (b. 1564)", "links": [{"title": "Scipione Cobelluzzi", "link": "https://wikipedia.org/wiki/Scipione_Cobelluzzi"}]}, {"year": "1646", "text": "<PERSON><PERSON><PERSON>, Gaelic-Irish Lord", "html": "1646 - <a href=\"https://wikipedia.org/wiki/Lau<PERSON>lin_%C3%93_Cellaigh\" title=\"<PERSON><PERSON><PERSON> Ó Cellaigh\"><PERSON><PERSON><PERSON></a>, Gaelic-Irish Lord", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Lau<PERSON>lin_%C3%93_Cellaigh\" title=\"<PERSON><PERSON><PERSON> Ó Cellaigh\"><PERSON><PERSON><PERSON> Cell<PERSON></a>, Gaelic-Irish Lord", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Laughlin_%C3%93_Cellaigh"}]}, {"year": "1725", "text": "<PERSON><PERSON>, Japanese philosopher, academic, and politician (b. 1657)", "html": "1725 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Japanese philosopher, academic, and politician (b. 1657)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Japanese philosopher, academic, and politician (b. 1657)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>i"}]}, {"year": "1729", "text": "<PERSON>, American-English poet, pastor, and physician (b. circa 1642)", "html": "1729 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American-English poet, pastor, and physician (b. circa 1642)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American-English poet, pastor, and physician (b. circa 1642)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1744", "text": "<PERSON>, French composer and conductor (b. 1660)", "html": "1744 - <a href=\"https://wikipedia.org/wiki/Andr%C3%A9_<PERSON><PERSON>\" title=\"<PERSON>\"><PERSON></a>, French composer and conductor (b. 1660)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Andr%C3%A9_<PERSON>ra\" title=\"<PERSON>\"><PERSON></a>, French composer and conductor (b. 1660)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Andr%C3%A9_Campra"}]}, {"year": "1764", "text": "<PERSON>, English businessman and philanthropist (b. 1693)", "html": "1764 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English businessman and philanthropist (b. 1693)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English businessman and philanthropist (b. 1693)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1779", "text": "<PERSON>, German painter (b. 1728)", "html": "1779 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German painter (b. 1728)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German painter (b. 1728)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1831", "text": "<PERSON> und zum <PERSON>, Prussian minister and politician (b. 1757)", "html": "1831 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_vom_und_zum_<PERSON>\" title=\"<PERSON> vom und zum Stein\"><PERSON> vom und zum <PERSON></a>, Prussian minister and politician (b. 1757)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_vom_und_zum_<PERSON>\" title=\"<PERSON> vom und zum Stein\"><PERSON> vom und zum Stein</a>, Prussian minister and politician (b. 1757)", "links": [{"title": "<PERSON> und zum Stein", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_vom_und_zum_<PERSON>"}]}, {"year": "1840", "text": "<PERSON>, French prince (b. 1775)", "html": "1840 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French prince (b. 1775)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French prince (b. 1775)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1852", "text": "<PERSON>, American lawyer and politician, 9th United States Secretary of State (b. 1777)", "html": "1852 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and politician, 9th <a href=\"https://wikipedia.org/wiki/United_States_Secretary_of_State\" title=\"United States Secretary of State\">United States Secretary of State</a> (b. 1777)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and politician, 9th <a href=\"https://wikipedia.org/wiki/United_States_Secretary_of_State\" title=\"United States Secretary of State\">United States Secretary of State</a> (b. 1777)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "United States Secretary of State", "link": "https://wikipedia.org/wiki/United_States_Secretary_of_State"}]}, {"year": "1853", "text": "<PERSON><PERSON><PERSON><PERSON>, French botanist and academic (b. 1797)", "html": "1853 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>-<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, French botanist and academic (b. 1797)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>-<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, French botanist and academic (b. 1797)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>-<PERSON>"}]}, {"year": "1855", "text": "<PERSON>, American physician and humanitarian (b. 1803)", "html": "1855 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American physician and humanitarian (b. 1803)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American physician and humanitarian (b. 1803)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1860", "text": "<PERSON>, English physician and endocrinologist (b. 1793)", "html": "1860 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English physician and endocrinologist (b. 1793)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English physician and endocrinologist (b. 1793)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1861", "text": "<PERSON>, English poet and translator (b. 1806)", "html": "1861 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English poet and translator (b. 1806)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English poet and translator (b. 1806)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1873", "text": "<PERSON>, Indian poet and playwright (b. 1824)", "html": "1873 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Indian poet and playwright (b. 1824)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Indian poet and playwright (b. 1824)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1875", "text": "<PERSON> of Austria (b. 1793)", "html": "1875 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Austria\" title=\"<PERSON> of Austria\"><PERSON> of Austria</a> (b. 1793)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Austria\" title=\"<PERSON> of Austria\"><PERSON> of Austria</a> (b. 1793)", "links": [{"title": "<PERSON> of Austria", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Austria"}]}, {"year": "1895", "text": "<PERSON>, English biologist (b. 1825)", "html": "1895 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English biologist (b. 1825)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English biologist (b. 1825)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1900", "text": "<PERSON>, Russian mathematician and academic (b. 1827)", "html": "1900 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, Russian mathematician and academic (b. 1827)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, Russian mathematician and academic (b. 1827)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1907", "text": "<PERSON><PERSON>, Greek painter and academic (b. 1837)", "html": "1907 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Greek painter and academic (b. 1837)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Greek painter and academic (b. 1837)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1919", "text": "<PERSON> physician and educator (b. 1864)", "html": "1919 - <a href=\"https://wikipedia.org/wiki/Jos%C3%A9_<PERSON><PERSON>_Hern%C3%A1ndez\" title=\"<PERSON>\"><PERSON></a> Venezuelan physician and educator (b. 1864)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Jos%C3%A9_<PERSON><PERSON>_Hern%C3%A1ndez\" title=\"<PERSON>\"><PERSON></a> Venezuelan physician and educator (b. 1864)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Jos%C3%A9_<PERSON><PERSON>_Hern%C3%A1ndez"}]}, {"year": "1931", "text": "<PERSON><PERSON><PERSON><PERSON>, Canadian poet and physician (b. 1850)", "html": "1931 - <a href=\"https://wikipedia.org/wiki/N%C3%A9r%C3%A9e_Beauchemin\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Canadian poet and physician (b. 1850)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/N%C3%A9r%C3%A9e_Beauchemin\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Canadian poet and physician (b. 1850)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/N%C3%A9r%C3%A9e_Beauchemin"}]}, {"year": "1933", "text": "<PERSON><PERSON><PERSON>, American actor, director, and screenwriter (b. 1887)", "html": "1933 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>rbuckle\" title=\"<PERSON><PERSON><PERSON> Arbuckle\"><PERSON><PERSON><PERSON></a>, American actor, director, and screenwriter (b. 1887)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>rbuckle\" title=\"<PERSON><PERSON><PERSON> Arbuckle\"><PERSON><PERSON><PERSON></a>, American actor, director, and screenwriter (b. 1887)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1935", "text": "<PERSON>, Irish-American baseball player and manager (b. 1873)", "html": "1935 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%27Neil<PERSON>_(baseball)\" title=\"<PERSON> (baseball)\"><PERSON></a>, Irish-American baseball player and manager (b. 1873)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%27N<PERSON><PERSON>_(baseball)\" title=\"<PERSON> (baseball)\"><PERSON></a>, Irish-American baseball player and manager (b. 1873)", "links": [{"title": "<PERSON> (baseball)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>%27Neill_(baseball)"}]}, {"year": "1936", "text": "<PERSON><PERSON><PERSON>, Slovene priest and missionary (b. 1872)", "html": "1936 - <a href=\"https://wikipedia.org/wiki/J%C3%A1nos_Szlepecz\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Slovene priest and missionary (b. 1872)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/J%C3%A1nos_Szlepecz\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Slovene priest and missionary (b. 1872)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/J%C3%A1nos_Szlepecz"}]}, {"year": "1940", "text": "<PERSON>, Swiss painter and illustrator (b. 1879)", "html": "1940 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swiss painter and illustrator (b. 1879)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swiss painter and illustrator (b. 1879)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1941", "text": "<PERSON><PERSON><PERSON>, Polish pianist, composer, and politician, 2nd Prime Minister of Poland (b. 1860)", "html": "1941 - <a href=\"https://wikipedia.org/wiki/Ignacy_<PERSON>\" title=\"Ignacy <PERSON>\">Ignacy <PERSON></a>, Polish pianist, composer, and politician, 2nd <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Poland\" title=\"Prime Minister of Poland\">Prime Minister of Poland</a> (b. 1860)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Ignacy_<PERSON>\" title=\"Ignacy <PERSON>\">Igna<PERSON></a>, Polish pianist, composer, and politician, 2nd <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Poland\" title=\"Prime Minister of Poland\">Prime Minister of Poland</a> (b. 1860)", "links": [{"title": "<PERSON>gna<PERSON>", "link": "https://wikipedia.org/wiki/Igna<PERSON>_<PERSON>_<PERSON>"}, {"title": "Prime Minister of Poland", "link": "https://wikipedia.org/wiki/Prime_Minister_of_Poland"}]}, {"year": "1942", "text": "<PERSON>, German politician, Mayor of Marburg (b. 1864)", "html": "1942 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German politician, <a href=\"https://wikipedia.org/wiki/List_of_mayors_of_Marburg\" title=\"List of mayors of Marburg\">Mayor of Marburg</a> (b. 1864)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German politician, <a href=\"https://wikipedia.org/wiki/List_of_mayors_of_Marburg\" title=\"List of mayors of Marburg\">Mayor of Marburg</a> (b. 1864)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "List of mayors of Marburg", "link": "https://wikipedia.org/wiki/List_of_mayors_of_Marburg"}]}, {"year": "1949", "text": "<PERSON><PERSON><PERSON><PERSON>, Greek politician, 115th Prime Minister of Greece (b. 1860)", "html": "1949 - <a href=\"https://wikipedia.org/wiki/Themistok<PERSON>_Sofoulis\" title=\"Themistok<PERSON> Sofouli<PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Greek politician, 115th <a href=\"https://wikipedia.org/wiki/List_of_Prime_Ministers_of_Greece\" class=\"mw-redirect\" title=\"List of Prime Ministers of Greece\">Prime Minister of Greece</a> (b. 1860)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Themistok<PERSON>_Sofoulis\" title=\"Themistoklis Sofoulis\"><PERSON><PERSON><PERSON><PERSON></a>, Greek politician, 115th <a href=\"https://wikipedia.org/wiki/List_of_Prime_Ministers_of_Greece\" class=\"mw-redirect\" title=\"List of Prime Ministers of Greece\">Prime Minister of Greece</a> (b. 1860)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Themistok<PERSON>_So<PERSON>ulis"}, {"title": "List of Prime Ministers of Greece", "link": "https://wikipedia.org/wiki/List_of_Prime_Ministers_of_Greece"}]}, {"year": "1955", "text": "<PERSON>, German painter and academic (b. 1881)", "html": "1955 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German painter and academic (b. 1881)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German painter and academic (b. 1881)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1960", "text": "<PERSON>, Canadian ice hockey player and coach (b. 1885)", "html": "1960 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(ice_hockey)\" title=\"<PERSON> (ice hockey)\"><PERSON></a>, Canadian ice hockey player and coach (b. 1885)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(ice_hockey)\" title=\"<PERSON> (ice hockey)\"><PERSON></a>, Canadian ice hockey player and coach (b. 1885)", "links": [{"title": "<PERSON> (ice hockey)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>(ice_hockey)"}]}, {"year": "1962", "text": "<PERSON>, American historian (b. 1883)", "html": "1962 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American historian (b. 1883)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American historian (b. 1883)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1964", "text": "<PERSON>, American saxophonist, composer, and bandleader (b. 1928)", "html": "1964 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American saxophonist, composer, and bandleader (b. 1928)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American saxophonist, composer, and bandleader (b. 1928)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1967", "text": "<PERSON><PERSON><PERSON>, Italian boxer and actor (b. 1906)", "html": "1967 - <a href=\"https://wikipedia.org/wiki/Primo_Carnera\" title=\"Primo Carnera\"><PERSON><PERSON><PERSON></a>, Italian boxer and actor (b. 1906)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Primo_Carnera\" title=\"Primo Carnera\"><PERSON><PERSON><PERSON></a>, Italian boxer and actor (b. 1906)", "links": [{"title": "Primo Carnera", "link": "https://wikipedia.org/wiki/Primo_Carnera"}]}, {"year": "1967", "text": "<PERSON>, American actress (b. 1933)", "html": "1967 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress (b. 1933)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress (b. 1933)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1969", "text": "<PERSON><PERSON>, Congolese accountant and politician, Prime Minister of the Democratic Republic of the Congo (b. 1919)", "html": "1969 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>omb<PERSON>\" class=\"mw-redirect\" title=\"<PERSON><PERSON> Tshombe\"><PERSON><PERSON></a>, Congolese accountant and politician, <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_the_Democratic_Republic_of_the_Congo\" title=\"Prime Minister of the Democratic Republic of the Congo\">Prime Minister of the Democratic Republic of the Congo</a> (b. 1919)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON><PERSON> Tshombe\"><PERSON><PERSON></a>, Congolese accountant and politician, <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_the_Democratic_Republic_of_the_Congo\" title=\"Prime Minister of the Democratic Republic of the Congo\">Prime Minister of the Democratic Republic of the Congo</a> (b. 1919)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>e"}, {"title": "Prime Minister of the Democratic Republic of the Congo", "link": "https://wikipedia.org/wiki/Prime_Minister_of_the_Democratic_Republic_of_the_Congo"}]}, {"year": "1971", "text": "<PERSON><PERSON><PERSON>, Mexican operatic tenor and bolero vocalist (b. 1908)", "html": "1971 - <a href=\"https://wikipedia.org/wiki/Nestor_<PERSON><PERSON>_<PERSON>\" title=\"Nestor <PERSON>\">Nest<PERSON> <PERSON><PERSON></a>, Mexican operatic tenor and bolero vocalist (b. 1908)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/N<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON>estor <PERSON>\"><PERSON><PERSON><PERSON> <PERSON><PERSON></a>, Mexican operatic tenor and bolero vocalist (b. 1908)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Nest<PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "1975", "text": "<PERSON>, American singer-songwriter and guitarist (b. 1947)", "html": "1975 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and guitarist (b. 1947)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and guitarist (b. 1947)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1978", "text": "<PERSON>, American actor (b. 1928)", "html": "1978 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor (b. 1928)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor (b. 1928)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1979", "text": "<PERSON>, American singer-songwriter, guitarist, and producer (b. 1945)", "html": "1979 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> George\"><PERSON></a>, American singer-songwriter, guitarist, and producer (b. 1945)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> George\"><PERSON></a>, American singer-songwriter, guitarist, and producer (b. 1945)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_George"}]}, {"year": "1980", "text": "<PERSON>, Peruvian historian (b. 1903)", "html": "1980 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Peruvian historian (b. 1903)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Peruvian historian (b. 1903)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1981", "text": "<PERSON>, English-Australian painter (b. 1912)", "html": "1981 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English-Australian painter (b. 1912)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English-Australian painter (b. 1912)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>sdale"}]}, {"year": "1982", "text": "<PERSON>, French fashion designer, founded <PERSON><PERSON><PERSON> (b. 1914)", "html": "1982 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French fashion designer, founded <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_(fashion_house)\" title=\"<PERSON><PERSON><PERSON> (fashion house)\"><PERSON><PERSON><PERSON></a> (b. 1914)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French fashion designer, founded <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_(fashion_house)\" title=\"<PERSON><PERSON><PERSON> (fashion house)\"><PERSON><PERSON><PERSON></a> (b. 1914)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}, {"title": "Balmain (fashion house)", "link": "https://wikipedia.org/wiki/Balma<PERSON>_(fashion_house)"}]}, {"year": "1982", "text": "<PERSON>, American actor, director, producer, and screenwriter (b. 1886)", "html": "1982 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(director)\" title=\"<PERSON> (director)\"><PERSON></a>, American actor, director, producer, and screenwriter (b. 1886)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(director)\" title=\"<PERSON> (director)\"><PERSON></a>, American actor, director, producer, and screenwriter (b. 1886)", "links": [{"title": "<PERSON> (director)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(director)"}]}, {"year": "1986", "text": "<PERSON>, Australian politician, 16th Premier of Western Australia (b. 1897)", "html": "1986 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian politician, 16th <a href=\"https://wikipedia.org/wiki/Premier_of_Western_Australia\" title=\"Premier of Western Australia\">Premier of Western Australia</a> (b. 1897)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian politician, 16th <a href=\"https://wikipedia.org/wiki/Premier_of_Western_Australia\" title=\"Premier of Western Australia\">Premier of Western Australia</a> (b. 1897)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Premier of Western Australia", "link": "https://wikipedia.org/wiki/Premier_of_Western_Australia"}]}, {"year": "1990", "text": "<PERSON>, American author and screenwriter (b. 1916)", "html": "1990 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author and screenwriter (b. 1916)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author and screenwriter (b. 1916)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1992", "text": "<PERSON>, Algerian soldier and politician, President of Algeria (b. 1919)", "html": "1992 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Algerian soldier and politician, <a href=\"https://wikipedia.org/wiki/President_of_Algeria\" title=\"President of Algeria\">President of Algeria</a> (b. 1919)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Algerian soldier and politician, <a href=\"https://wikipedia.org/wiki/President_of_Algeria\" title=\"President of Algeria\">President of Algeria</a> (b. 1919)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "President of Algeria", "link": "https://wikipedia.org/wiki/President_of_Algeria"}]}, {"year": "1993", "text": "<PERSON><PERSON><PERSON>, Puerto Rican-American singer-songwriter (b. 1946)", "html": "1993 - <a href=\"https://wikipedia.org/wiki/H%C3%A9<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Puerto Rican-American singer-songwriter (b. 1946)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/H%C3%A9<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Puerto Rican-American singer-songwriter (b. 1946)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/H%C3%A9ctor_<PERSON>voe"}]}, {"year": "1994", "text": "<PERSON>, German conductor and educator (b. 1908)", "html": "1994 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German conductor and educator (b. 1908)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German conductor and educator (b. 1908)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1995", "text": "<PERSON>, American actress (b. 1921)", "html": "1995 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress (b. 1921)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress (b. 1921)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1997", "text": "<PERSON>, American actor (b. 1927)", "html": "1997 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(actor)\" title=\"<PERSON> (actor)\"><PERSON></a>, American actor (b. 1927)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(actor)\" title=\"<PERSON> (actor)\"><PERSON></a>, American actor (b. 1927)", "links": [{"title": "<PERSON> (actor)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(actor)"}]}, {"year": "1997", "text": "<PERSON>, Scottish campaigner for the arts and environment of Orkney (b. 1909)", "html": "1997 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish campaigner for the arts and environment of Orkney (b. 1909)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish campaigner for the arts and environment of Orkney (b. 1909)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1998", "text": "<PERSON><PERSON>, German pianist and composer (b. 1936)", "html": "1998 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, German pianist and composer (b. 1936)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, German pianist and composer (b. 1936)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1999", "text": "<PERSON><PERSON><PERSON>, Syrian-Armenian patriarch (b. 1950)", "html": "1999 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"Karekin I\"><PERSON><PERSON><PERSON> <PERSON></a>, Syrian-Armenian patriarch (b. 1950)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"Karekin I\"><PERSON><PERSON><PERSON> <PERSON></a>, Syrian-Armenian patriarch (b. 1950)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1999", "text": "<PERSON>, American screenwriter and producer (b. 1937)", "html": "1999 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American screenwriter and producer (b. 1937)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American screenwriter and producer (b. 1937)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2000", "text": "<PERSON><PERSON><PERSON>, Italian actor and director (b. 1922)", "html": "2000 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Italian actor and director (b. 1922)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Italian actor and director (b. 1922)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Vitt<PERSON>_<PERSON>"}]}, {"year": "2000", "text": "<PERSON>, <PERSON>, Canadian-English publisher and politician (b. 1913)", "html": "2000 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Baroness_<PERSON>\" title=\"<PERSON>, <PERSON>\"><PERSON>, Baroness <PERSON></a>, Canadian-English publisher and politician (b. 1913)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Baroness_<PERSON>\" title=\"<PERSON>, Baroness <PERSON>\"><PERSON>, Baroness <PERSON></a>, Canadian-English publisher and politician (b. 1913)", "links": [{"title": "<PERSON>, <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_<PERSON>_<PERSON>"}]}, {"year": "2002", "text": "<PERSON>, American singer and actress (b. 1928)", "html": "2002 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer and actress (b. 1928)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer and actress (b. 1928)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2003", "text": "<PERSON><PERSON>, American actress (b. 1907)", "html": "2003 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American actress (b. 1907)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American actress (b. 1907)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "2004", "text": "<PERSON>, American physician and biochemist (b. 1935)", "html": "2004 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American physician and biochemist (b. 1935)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American physician and biochemist (b. 1935)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2004", "text": "<PERSON>, Canadian lieutenant and politician, 18th Canadian Minister of Agriculture (b. 1912)", "html": "2004 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian lieutenant and politician, 18th <a href=\"https://wikipedia.org/wiki/Minister_of_Agriculture_(Canada)\" class=\"mw-redirect\" title=\"Minister of Agriculture (Canada)\">Canadian Minister of Agriculture</a> (b. 1912)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian lieutenant and politician, 18th <a href=\"https://wikipedia.org/wiki/Minister_of_Agriculture_(Canada)\" class=\"mw-redirect\" title=\"Minister of Agriculture (Canada)\">Canadian Minister of Agriculture</a> (b. 1912)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Minister of Agriculture (Canada)", "link": "https://wikipedia.org/wiki/Minister_of_Agriculture_(Canada)"}]}, {"year": "2006", "text": "<PERSON><PERSON><PERSON>, Argentinian director and screenwriter (b. 1959)", "html": "2006 - <a href=\"https://wikipedia.org/wiki/Fabi%C3%A1n_Bielinsky\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Argentinian director and screenwriter (b. 1959)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Fabi%C3%A1n_Biel<PERSON>ky\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Argentinian director and screenwriter (b. 1959)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Fabi%C3%<PERSON><PERSON>_<PERSON><PERSON><PERSON>"}]}, {"year": "2006", "text": "<PERSON>, Canadian-American theatre director, actor, and dean (b. 1919)", "html": "2006 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian-American theatre director, actor, and dean (b. 1919)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian-American theatre director, actor, and dean (b. 1919)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2006", "text": "<PERSON>, American football player and coach (b. 1954)", "html": "2006 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(American_football_coach)\" title=\"<PERSON> (American football coach)\"><PERSON></a>, American football player and coach (b. 1954)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(American_football_coach)\" title=\"<PERSON> (American football coach)\"><PERSON></a>, American football player and coach (b. 1954)", "links": [{"title": "<PERSON> (American football coach)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(American_football_coach)"}]}, {"year": "2007", "text": "<PERSON>, American soldier and author (b. 1930)", "html": "2007 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American soldier and author (b. 1930)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American soldier and author (b. 1930)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2007", "text": "<PERSON>, American journalist and critic (b. 1943)", "html": "2007 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American journalist and critic (b. 1943)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American journalist and critic (b. 1943)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2009", "text": "<PERSON>, American, target shooter and boot-maker (b. 1925)", "html": "2009 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(marksman)\" title=\"<PERSON> (marksman)\"><PERSON></a>, American, target shooter and boot-maker (b. 1925)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(marksman)\" title=\"<PERSON> (marksman)\"><PERSON></a>, American, target shooter and boot-maker (b. 1925)", "links": [{"title": "<PERSON> (marksman)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(marksman)"}]}, {"year": "2011", "text": "<PERSON><PERSON> <PERSON><PERSON>,  Indian poet, scholar, writer, philosopher, and cultural critic (b. 1904)", "html": "2011 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"K. D. <PERSON>\"><PERSON><PERSON> <PERSON><PERSON></a>, Indian poet, scholar, writer, philosopher, and cultural critic (b. 1904)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"K. D. <PERSON>\"><PERSON><PERSON> <PERSON><PERSON></a>, Indian poet, scholar, writer, philosopher, and cultural critic (b. 1904)", "links": [{"title": "<PERSON><PERSON> <PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "2012", "text": "<PERSON>, Singaporean politician, Singaporean Minister of Health (b. 1918)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Singaporean politician, <a href=\"https://wikipedia.org/wiki/Ministry_of_Health_(Singapore)\" title=\"Ministry of Health (Singapore)\">Singaporean Minister of Health</a> (b. 1918)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Singaporean politician, <a href=\"https://wikipedia.org/wiki/Ministry_of_Health_(Singapore)\" title=\"Ministry of Health (Singapore)\">Singaporean Minister of Health</a> (b. 1918)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "Ministry of Health (Singapore)", "link": "https://wikipedia.org/wiki/Ministry_of_Health_(Singapore)"}]}, {"year": "2012", "text": "<PERSON>, American political scientist and academic (b. 1919)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American political scientist and academic (b. 1919)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American political scientist and academic (b. 1919)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2012", "text": "<PERSON>, Chilean triple jumper (b. 1911)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Chilean triple jumper (b. 1911)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Chilean triple jumper (b. 1911)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2012", "text": "<PERSON>, American baseball player, coach, and manager (b. 1926)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/Floyd_Temple\" title=\"Floyd Temple\"><PERSON></a>, American baseball player, coach, and manager (b. 1926)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Floyd_Temple\" title=\"Floyd Temple\"><PERSON></a>, American baseball player, coach, and manager (b. 1926)", "links": [{"title": "Floyd <PERSON>", "link": "https://wikipedia.org/wiki/Floyd_Temple"}]}, {"year": "2013", "text": "<PERSON>, Irish footballer and manager (b. 1937)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(footballer)\" title=\"<PERSON> (footballer)\"><PERSON></a>, Irish footballer and manager (b. 1937)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(footballer)\" title=\"<PERSON> (footballer)\"><PERSON></a>, Irish footballer and manager (b. 1937)", "links": [{"title": "<PERSON> (footballer)", "link": "https://wikipedia.org/wiki/<PERSON>(footballer)"}]}, {"year": "2013", "text": "<PERSON>, American-Canadian football player, coach, and manager (b. 1929)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Jack Gotta\"><PERSON></a>, American-Canadian football player, coach, and manager (b. 1929)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Jack Gotta\"><PERSON></a>, American-Canadian football player, coach, and manager (b. 1929)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2013", "text": "<PERSON><PERSON><PERSON><PERSON>, Italian astrophysicist and author (b. 1922)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/Margh<PERSON>ta_<PERSON>ck\" title=\"Margh<PERSON><PERSON> Hack\"><PERSON><PERSON><PERSON><PERSON></a>, Italian astrophysicist and author (b. 1922)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Mar<PERSON><PERSON><PERSON>_<PERSON>ck\" title=\"<PERSON>gh<PERSON><PERSON> Hack\"><PERSON><PERSON><PERSON><PERSON></a>, Italian astrophysicist and author (b. 1922)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Mar<PERSON><PERSON><PERSON>_<PERSON>ck"}]}, {"year": "2013", "text": "<PERSON><PERSON>, Colombian politician (b. 1956)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>%C3%A9nez\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Colombian politician (b. 1956)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>%C3%A9nez\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Colombian politician (b. 1956)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Gilma_Jim%C3%A9nez"}]}, {"year": "2014", "text": "<PERSON>, South African cricketer (b. 1960)", "html": "2014 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%27Oliveira\" title=\"<PERSON>\"><PERSON></a>, South African cricketer (b. 1960)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%27Oliveira\" title=\"<PERSON>\"><PERSON></a>, South African cricketer (b. 1960)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Damian_D%27Oliveira"}]}, {"year": "2014", "text": "<PERSON><PERSON>, Irish author, poet, and playwright (b. 1947)", "html": "2014 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Irish author, poet, and playwright (b. 1947)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Irish author, poet, and playwright (b. 1947)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "2015", "text": "<PERSON><PERSON>, Egyptian lawyer and judge (b. 1950)", "html": "2015 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Egyptian lawyer and judge (b. 1950)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Egyptian lawyer and judge (b. 1950)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "2015", "text": "<PERSON>, Czech footballer and coach (b. 1931)", "html": "2015 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Czech footballer and coach (b. 1931)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Czech footballer and coach (b. 1931)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2015", "text": "<PERSON>, French businessman and politician, French Minister of the Interior (b. 1927)", "html": "2015 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French businessman and politician, <a href=\"https://wikipedia.org/wiki/Minister_of_the_Interior_(France)\" class=\"mw-redirect\" title=\"Minister of the Interior (France)\">French Minister of the Interior</a> (b. 1927)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French businessman and politician, <a href=\"https://wikipedia.org/wiki/Minister_of_the_Interior_(France)\" class=\"mw-redirect\" title=\"Minister of the Interior (France)\">French Minister of the Interior</a> (b. 1927)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Minister of the Interior (France)", "link": "https://wikipedia.org/wiki/Minister_of_the_Interior_(France)"}]}, {"year": "2016", "text": "<PERSON>, Springbok cyclist and five times South African National Rally Champion (b. 1933)", "html": "2016 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Springbok cyclist and five times South African National Rally Champion (b. 1933)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Springbok cyclist and five times South African National Rally Champion (b. 1933)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2017", "text": "<PERSON>, French entrepreneur and chairman of Montpellier HSC from 1974 to his death (b. 1943)", "html": "2017 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French entrepreneur and chairman of <a href=\"https://wikipedia.org/wiki/Montpellier_HSC\" title=\"Montpellier HSC\">Montpellier HSC</a> from 1974 to his death (b. 1943)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French entrepreneur and chairman of <a href=\"https://wikipedia.org/wiki/Montpellier_HSC\" title=\"Montpellier HSC\">Montpellier HSC</a> from 1974 to his death (b. 1943)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Montpellier HSC", "link": "https://wikipedia.org/wiki/Montpellier_HSC"}]}, {"year": "2017", "text": "<PERSON>, Canadian ice hockey player (b. 1957)", "html": "2017 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player (b. 1957)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player (b. 1957)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2018", "text": "<PERSON>, American comic writer and illustrator (b. 1927)", "html": "2018 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American comic writer and illustrator (b. 1927)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American comic writer and illustrator (b. 1927)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2020", "text": "<PERSON>, American actor, director, producer, and screenwriter (b. 1922)", "html": "2020 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor, director, producer, and screenwriter (b. 1922)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor, director, producer, and screenwriter (b. 1922)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2020", "text": "<PERSON><PERSON>, American rap artist (b. 1988)", "html": "2020 - <a href=\"https://wikipedia.org/wiki/Injury_Reserve\" title=\"Injury Reserve\"><PERSON><PERSON></a>, American rap artist (b. 1988)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Injury_Reserve\" title=\"Injury Reserve\"><PERSON><PERSON></a>, American rap artist (b. 1988)", "links": [{"title": "Injury Reserve", "link": "https://wikipedia.org/wiki/Injury_Reserve"}]}, {"year": "2020", "text": "<PERSON><PERSON><PERSON>,  Ethiopian singer, songwriter (b. 1986)", "html": "2020 - <a href=\"https://wikipedia.org/wiki/Hachalu_Hundessa\" title=\"<PERSON>chal<PERSON>\"><PERSON><PERSON><PERSON></a>, <a href=\"https://wikipedia.org/wiki/Ethiopia\" title=\"Ethiopia\">Ethiopian</a> singer, songwriter (b. 1986)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Hachal<PERSON>_Hu<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, <a href=\"https://wikipedia.org/wiki/Ethiopia\" title=\"Ethiopia\">Ethiopian</a> singer, songwriter (b. 1986)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}, {"title": "Ethiopia", "link": "https://wikipedia.org/wiki/Ethiopia"}]}, {"year": "2021", "text": "<PERSON>, American captain and politician, 13th United States Secretary of Defense (b. 1932)", "html": "2021 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American captain and politician, 13th <a href=\"https://wikipedia.org/wiki/United_States_Secretary_of_Defense\" title=\"United States Secretary of Defense\">United States Secretary of Defense</a> (b. 1932)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American captain and politician, 13th <a href=\"https://wikipedia.org/wiki/United_States_Secretary_of_Defense\" title=\"United States Secretary of Defense\">United States Secretary of Defense</a> (b. 1932)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}, {"title": "United States Secretary of Defense", "link": "https://wikipedia.org/wiki/United_States_Secretary_of_Defense"}]}, {"year": "2022", "text": "<PERSON><PERSON><PERSON> <PERSON><PERSON>, American Marine Corps warrant officer, last living Medal of Honor recipient from World War II (b. 1923)", "html": "2022 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON> <PERSON><PERSON>\">Her<PERSON><PERSON> <PERSON><PERSON></a>, American Marine Corps warrant officer, last living Medal of Honor recipient from World War II (b. 1923)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON> <PERSON><PERSON>\">Her<PERSON><PERSON> <PERSON><PERSON></a>, American Marine Corps warrant officer, last living Medal of Honor recipient from World War II (b. 1923)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "2023", "text": "<PERSON>, American actor (b. 1934)", "html": "2023 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor (b. 1934)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor (b. 1934)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2024", "text": "Princess <PERSON><PERSON>, Princess <PERSON><PERSON> of Morocco (b. 1946)", "html": "2024 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_Amahzo<PERSON>\" title=\"<PERSON><PERSON> Amahzo<PERSON>\">Princess <PERSON><PERSON></a>, Princess <PERSON><PERSON> of Morocco (b. 1946)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>zo<PERSON>\">Princess <PERSON><PERSON></a>, Princess <PERSON><PERSON> of Morocco (b. 1946)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON><PERSON>_<PERSON>"}]}]}}