{"date": "June 7", "url": "https://wikipedia.org/wiki/June_7", "data": {"Events": [{"year": "421", "text": "Emperor <PERSON><PERSON><PERSON> marries <PERSON><PERSON><PERSON> at Constantinople (Byzantine Empire).", "html": "421 - Emperor <a href=\"https://wikipedia.org/wiki/Theodosius_II\" title=\"Theodosius II\"><PERSON><PERSON><PERSON> II</a> marries <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a> at <a href=\"https://wikipedia.org/wiki/Istanbul\" title=\"Istanbul\">Constantinople</a> (<a href=\"https://wikipedia.org/wiki/Byzantine_Empire\" title=\"Byzantine Empire\">Byzantine Empire</a>).", "no_year_html": "Emperor <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_II\" title=\"Theodosius II\"><PERSON><PERSON><PERSON> II</a> marries <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a> at <a href=\"https://wikipedia.org/wiki/Istanbul\" title=\"Istanbul\">Constantinople</a> (<a href=\"https://wikipedia.org/wiki/Byzantine_Empire\" title=\"Byzantine Empire\">Byzantine Empire</a>).", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Theodosius_II"}, {"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}, {"title": "Istanbul", "link": "https://wikipedia.org/wiki/Istanbul"}, {"title": "Byzantine Empire", "link": "https://wikipedia.org/wiki/Byzantine_Empire"}]}, {"year": "879", "text": "<PERSON> <PERSON> recognises the Duchy of Croatia under <PERSON> <PERSON><PERSON><PERSON> as an independent state.", "html": "879 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON> John <PERSON>\">Pope <PERSON></a> recognises the <a href=\"https://wikipedia.org/wiki/Duchy_of_Croatia\" title=\"Duchy of Croatia\">Duchy of Croatia</a> under Duke <a href=\"https://wikipedia.org/wiki/Branimir_of_Croatia\" class=\"mw-redirect\" title=\"Branimir of Croatia\">Branimir</a> as an independent state.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON> John <PERSON>\">Pope <PERSON></a> recognises the <a href=\"https://wikipedia.org/wiki/Duchy_of_Croatia\" title=\"Duchy of Croatia\">Duchy of Croatia</a> under Duke <a href=\"https://wikipedia.org/wiki/Branimir_of_Croatia\" class=\"mw-redirect\" title=\"Branimir of Croatia\">Branimir</a> as an independent state.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "Duchy of Croatia", "link": "https://wikipedia.org/wiki/Duchy_of_Croatia"}, {"title": "B<PERSON>mir of Croatia", "link": "https://wikipedia.org/wiki/Branimir_of_Croatia"}]}, {"year": "1002", "text": "<PERSON>, a cousin of Emperor <PERSON>, is elected and crowned King of Germany.", "html": "1002 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Holy_Roman_Emperor\" title=\"<PERSON> II, Holy Roman Emperor\"><PERSON> II</a>, a cousin of Emperor <a href=\"https://wikipedia.org/wiki/Otto_III\" class=\"mw-redirect\" title=\"<PERSON> III\"><PERSON> III</a>, is elected and crowned <a href=\"https://wikipedia.org/wiki/Kingdom_of_Germany\" title=\"Kingdom of Germany\">King of Germany</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Holy_Roman_Emperor\" title=\"<PERSON> II, Holy Roman Emperor\"><PERSON> II</a>, a cousin of Emperor <a href=\"https://wikipedia.org/wiki/<PERSON>_III\" class=\"mw-redirect\" title=\"<PERSON> III\"><PERSON></a>, is elected and crowned <a href=\"https://wikipedia.org/wiki/Kingdom_of_Germany\" title=\"Kingdom of Germany\">King of Germany</a>.", "links": [{"title": "<PERSON>, Holy Roman Emperor", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Holy_Roman_Emperor"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Kingdom of Germany", "link": "https://wikipedia.org/wiki/Kingdom_of_Germany"}]}, {"year": "1099", "text": "First Crusade: The Siege of Jerusalem begins.", "html": "1099 - <a href=\"https://wikipedia.org/wiki/First_Crusade\" title=\"First Crusade\">First Crusade</a>: The <a href=\"https://wikipedia.org/wiki/Siege_of_Jerusalem_(1099)\" title=\"Siege of Jerusalem (1099)\">Siege of Jerusalem</a> begins.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/First_Crusade\" title=\"First Crusade\">First Crusade</a>: The <a href=\"https://wikipedia.org/wiki/Siege_of_Jerusalem_(1099)\" title=\"Siege of Jerusalem (1099)\">Siege of Jerusalem</a> begins.", "links": [{"title": "First Crusade", "link": "https://wikipedia.org/wiki/First_Crusade"}, {"title": "Siege of Jerusalem (1099)", "link": "https://wikipedia.org/wiki/Siege_of_Jerusalem_(1099)"}]}, {"year": "1420", "text": "Troops of the Republic of Venice capture Udine, ending the independence of the Patria del Friuli.", "html": "1420 - Troops of the <a href=\"https://wikipedia.org/wiki/Republic_of_Venice\" title=\"Republic of Venice\">Republic of Venice</a> capture <a href=\"https://wikipedia.org/wiki/Udine\" title=\"Udine\">Udine</a>, ending the independence of the <a href=\"https://wikipedia.org/wiki/Patria_del_Friuli\" title=\"Patria del Friuli\">Patria del Friuli</a>.", "no_year_html": "Troops of the <a href=\"https://wikipedia.org/wiki/Republic_of_Venice\" title=\"Republic of Venice\">Republic of Venice</a> capture <a href=\"https://wikipedia.org/wiki/Udine\" title=\"Udine\">Udine</a>, ending the independence of the <a href=\"https://wikipedia.org/wiki/Patria_del_Friuli\" title=\"Patria del Friuli\">Patria del Friuli</a>.", "links": [{"title": "Republic of Venice", "link": "https://wikipedia.org/wiki/Republic_of_Venice"}, {"title": "Udine", "link": "https://wikipedia.org/wiki/Udine"}, {"title": "Patria del Friuli", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1494", "text": "Spain and Portugal sign the Treaty of Tordesillas which divides the New World between the two countries.", "html": "1494 - <a href=\"https://wikipedia.org/wiki/Spain\" title=\"Spain\">Spain</a> and <a href=\"https://wikipedia.org/wiki/Portugal\" title=\"Portugal\">Portugal</a> sign the <a href=\"https://wikipedia.org/wiki/Treaty_of_Tordesillas\" title=\"Treaty of Tordesillas\">Treaty of Tordesillas</a> which divides the <a href=\"https://wikipedia.org/wiki/New_World\" title=\"New World\">New World</a> between the two countries.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Spain\" title=\"Spain\">Spain</a> and <a href=\"https://wikipedia.org/wiki/Portugal\" title=\"Portugal\">Portugal</a> sign the <a href=\"https://wikipedia.org/wiki/Treaty_of_Tordesillas\" title=\"Treaty of Tordesillas\">Treaty of Tordesillas</a> which divides the <a href=\"https://wikipedia.org/wiki/New_World\" title=\"New World\">New World</a> between the two countries.", "links": [{"title": "Spain", "link": "https://wikipedia.org/wiki/Spain"}, {"title": "Portugal", "link": "https://wikipedia.org/wiki/Portugal"}, {"title": "Treaty of Tordesillas", "link": "https://wikipedia.org/wiki/Treaty_of_Tordesillas"}, {"title": "New World", "link": "https://wikipedia.org/wiki/New_World"}]}, {"year": "1628", "text": "The Petition of Right, a major English constitutional document, is granted the Royal Assent by <PERSON> and becomes law.", "html": "1628 - The <a href=\"https://wikipedia.org/wiki/Petition_of_Right\" title=\"Petition of Right\">Petition of Right</a>, a <a href=\"https://wikipedia.org/wiki/Fundamental_Laws_of_England\" title=\"Fundamental Laws of England\">major English constitutional document</a>, is granted the <a href=\"https://wikipedia.org/wiki/Royal_Assent\" class=\"mw-redirect\" title=\"Royal Assent\">Royal Assent</a> by <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_England\" title=\"<PERSON> of England\"><PERSON> I</a> and becomes law.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Petition_of_Right\" title=\"Petition of Right\">Petition of Right</a>, a <a href=\"https://wikipedia.org/wiki/Fundamental_Laws_of_England\" title=\"Fundamental Laws of England\">major English constitutional document</a>, is granted the <a href=\"https://wikipedia.org/wiki/Royal_Assent\" class=\"mw-redirect\" title=\"Royal Assent\">Royal Assent</a> by <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_England\" title=\"<PERSON> of England\"><PERSON> I</a> and becomes law.", "links": [{"title": "Petition of Right", "link": "https://wikipedia.org/wiki/Petition_of_Right"}, {"title": "Fundamental Laws of England", "link": "https://wikipedia.org/wiki/Fundamental_Laws_of_England"}, {"title": "Royal Assent", "link": "https://wikipedia.org/wiki/Royal_Assent"}, {"title": "<PERSON> of England", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_England"}]}, {"year": "1640", "text": "Corpus de Sang in Barcelona: Catalan reapers rioted against Spanish Royal soldiers and officers, killing the Viceroy of Catalonia, <PERSON><PERSON><PERSON>. Escalation of hostilities between the Principality of Catalonia and the Spanish Monarchy, leading to the Reapers' War.", "html": "1640 - <a href=\"https://wikipedia.org/wiki/Corpus_de_Sang\" title=\"Corpus de Sang\">Corpus de Sang</a> in <a href=\"https://wikipedia.org/wiki/Barcelona\" title=\"Barcelona\">Barcelona</a>: Catalan reapers rioted against Spanish Royal soldiers and officers, killing the <a href=\"https://wikipedia.org/wiki/Viceroy_of_Catalonia\" class=\"mw-redirect\" title=\"Viceroy of Catalonia\">Viceroy of Catalonia</a>, <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_de_Queralt,_Count_of_Santa_Coloma\" title=\"<PERSON><PERSON><PERSON>, Count of Santa Coloma\"><PERSON><PERSON><PERSON> de Queralt</a>. Escalation of hostilities between the <a href=\"https://wikipedia.org/wiki/Principality_of_Catalonia\" title=\"Principality of Catalonia\">Principality of Catalonia</a> and the <a href=\"https://wikipedia.org/wiki/Habsburg_Spain\" title=\"Habsburg Spain\">Spanish Monarchy</a>, leading to the <a href=\"https://wikipedia.org/wiki/Reapers%27_War\" title=\"Reapers' War\">Reapers' War</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Corpus_de_Sang\" title=\"Corpus de Sang\">Corpus de Sang</a> in <a href=\"https://wikipedia.org/wiki/Barcelona\" title=\"Barcelona\">Barcelona</a>: Catalan reapers rioted against Spanish Royal soldiers and officers, killing the <a href=\"https://wikipedia.org/wiki/Viceroy_of_Catalonia\" class=\"mw-redirect\" title=\"Viceroy of Catalonia\">Viceroy of Catalonia</a>, <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_de_Que<PERSON>,_Count_of_Santa_Coloma\" title=\"<PERSON><PERSON><PERSON>, Count of Santa Coloma\"><PERSON><PERSON><PERSON> Queralt</a>. Escalation of hostilities between the <a href=\"https://wikipedia.org/wiki/Principality_of_Catalonia\" title=\"Principality of Catalonia\">Principality of Catalonia</a> and the <a href=\"https://wikipedia.org/wiki/Habsburg_Spain\" title=\"Habsburg Spain\">Spanish Monarchy</a>, leading to the <a href=\"https://wikipedia.org/wiki/Reapers%27_War\" title=\"Reapers' War\">Reapers' War</a>.", "links": [{"title": "Corpus de Sang", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "Barcelona", "link": "https://wikipedia.org/wiki/Barcelona"}, {"title": "Viceroy of Catalonia", "link": "https://wikipedia.org/wiki/Viceroy_of_Catalonia"}, {"title": "<PERSON><PERSON><PERSON>, Count of Santa Coloma", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>,_Count_of_Santa_Coloma"}, {"title": "Principality of Catalonia", "link": "https://wikipedia.org/wiki/Principality_of_Catalonia"}, {"title": "Habsburg Spain", "link": "https://wikipedia.org/wiki/Habsburg_Spain"}, {"title": "Reapers' War", "link": "https://wikipedia.org/wiki/Reapers%27_War"}]}, {"year": "1654", "text": "<PERSON> is crowned King of France.", "html": "1654 - <a href=\"https://wikipedia.org/wiki/<PERSON>_XIV\" title=\"Louis XIV\"><PERSON></a> is crowned King of <a href=\"https://wikipedia.org/wiki/Kingdom_of_France\" title=\"Kingdom of France\">France</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_XIV\" title=\"Louis XIV\"><PERSON></a> is crowned King of <a href=\"https://wikipedia.org/wiki/Kingdom_of_France\" title=\"Kingdom of France\">France</a>.", "links": [{"title": "Louis XIV", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Kingdom of France", "link": "https://wikipedia.org/wiki/Kingdom_of_France"}]}, {"year": "1692", "text": "Port Royal, Jamaica, is hit by a catastrophic earthquake; in just three minutes, 1,600 people are killed and 3,000 are seriously injured.", "html": "1692 - <a href=\"https://wikipedia.org/wiki/Port_Royal,_Jamaica\" class=\"mw-redirect\" title=\"Port Royal, Jamaica\">Port Royal, Jamaica</a>, is hit by a <a href=\"https://wikipedia.org/wiki/1692_Jamaica_earthquake\" title=\"1692 Jamaica earthquake\">catastrophic earthquake</a>; in just three minutes, 1,600 people are killed and 3,000 are seriously injured.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Port_Royal,_Jamaica\" class=\"mw-redirect\" title=\"Port Royal, Jamaica\">Port Royal, Jamaica</a>, is hit by a <a href=\"https://wikipedia.org/wiki/1692_Jamaica_earthquake\" title=\"1692 Jamaica earthquake\">catastrophic earthquake</a>; in just three minutes, 1,600 people are killed and 3,000 are seriously injured.", "links": [{"title": "Port Royal, Jamaica", "link": "https://wikipedia.org/wiki/Port_Royal,_Jamaica"}, {"title": "1692 Jamaica earthquake", "link": "https://wikipedia.org/wiki/1692_Jamaica_earthquake"}]}, {"year": "1776", "text": "<PERSON> presents the \"Lee Resolution\" to the Continental Congress. The motion is seconded by <PERSON> and will lead to the United States Declaration of Independence.", "html": "1776 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> presents the \"<a href=\"https://wikipedia.org/wiki/Lee_Resolution\" title=\"Lee Resolution\">Lee Resolution</a>\" to the <a href=\"https://wikipedia.org/wiki/Continental_Congress\" title=\"Continental Congress\">Continental Congress</a>. The motion is seconded by <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> and will lead to the <a href=\"https://wikipedia.org/wiki/United_States_Declaration_of_Independence\" title=\"United States Declaration of Independence\">United States Declaration of Independence</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> presents the \"<a href=\"https://wikipedia.org/wiki/Lee_Resolution\" title=\"Lee Resolution\">Lee Resolution</a>\" to the <a href=\"https://wikipedia.org/wiki/Continental_Congress\" title=\"Continental Congress\">Continental Congress</a>. The motion is seconded by <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> and will lead to the <a href=\"https://wikipedia.org/wiki/United_States_Declaration_of_Independence\" title=\"United States Declaration of Independence\">United States Declaration of Independence</a>.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "Lee Resolution", "link": "https://wikipedia.org/wiki/Lee_Resolution"}, {"title": "Continental Congress", "link": "https://wikipedia.org/wiki/Continental_Congress"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "United States Declaration of Independence", "link": "https://wikipedia.org/wiki/United_States_Declaration_of_Independence"}]}, {"year": "1788", "text": "French Revolution: Day of the Tiles: Civilians in Grenoble toss roof tiles and various objects down upon royal troops.", "html": "1788 - <a href=\"https://wikipedia.org/wiki/French_Revolution\" title=\"French Revolution\">French Revolution</a>: <a href=\"https://wikipedia.org/wiki/Day_of_the_Tiles\" title=\"Day of the Tiles\">Day of the Tiles</a>: Civilians in <a href=\"https://wikipedia.org/wiki/Grenoble\" title=\"Grenoble\">Grenoble</a> toss roof tiles and various objects down upon royal troops.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/French_Revolution\" title=\"French Revolution\">French Revolution</a>: <a href=\"https://wikipedia.org/wiki/Day_of_the_Tiles\" title=\"Day of the Tiles\">Day of the Tiles</a>: Civilians in <a href=\"https://wikipedia.org/wiki/Grenoble\" title=\"Grenoble\">Grenoble</a> toss roof tiles and various objects down upon royal troops.", "links": [{"title": "French Revolution", "link": "https://wikipedia.org/wiki/French_Revolution"}, {"title": "Day of the Tiles", "link": "https://wikipedia.org/wiki/Day_of_the_Tiles"}, {"title": "Grenoble", "link": "https://wikipedia.org/wiki/Grenoble"}]}, {"year": "1800", "text": "<PERSON> reaches the mouth of the Saskatchewan River in Manitoba.", "html": "1800 - <a href=\"https://wikipedia.org/wiki/<PERSON>(explorer)\" title=\"<PERSON> (explorer)\"><PERSON></a> reaches the mouth of the <a href=\"https://wikipedia.org/wiki/Saskatchewan_River\" title=\"Saskatchewan River\">Saskatchewan River</a> in <a href=\"https://wikipedia.org/wiki/Manitoba\" title=\"Manitoba\">Manitoba</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>(explorer)\" title=\"<PERSON> (explorer)\"><PERSON></a> reaches the mouth of the <a href=\"https://wikipedia.org/wiki/Saskatchewan_River\" title=\"Saskatchewan River\">Saskatchewan River</a> in <a href=\"https://wikipedia.org/wiki/Manitoba\" title=\"Manitoba\">Manitoba</a>.", "links": [{"title": "<PERSON> (explorer)", "link": "https://wikipedia.org/wiki/<PERSON>_(explorer)"}, {"title": "Saskatchewan River", "link": "https://wikipedia.org/wiki/Saskatchewan_River"}, {"title": "Manitoba", "link": "https://wikipedia.org/wiki/Manitoba"}]}, {"year": "1810", "text": "The newspaper Gazeta de Buenos Ayres is first published in Argentina.", "html": "1810 - The newspaper <i><a href=\"https://wikipedia.org/wiki/Gazeta_de_Buenos_Ayres\" class=\"mw-redirect\" title=\"Gazeta de Buenos Ayres\">Gazeta de Buenos Ayres</a></i> is first published in <a href=\"https://wikipedia.org/wiki/Argentina\" title=\"Argentina\">Argentina</a>.", "no_year_html": "The newspaper <i><a href=\"https://wikipedia.org/wiki/Gazeta_de_Buenos_Ayres\" class=\"mw-redirect\" title=\"Gazeta de Buenos Ayres\">Gazeta de Buenos Ayres</a></i> is first published in <a href=\"https://wikipedia.org/wiki/Argentina\" title=\"Argentina\">Argentina</a>.", "links": [{"title": "Gazeta de Buenos Ayres", "link": "https://wikipedia.org/wiki/Gazeta_de_Buenos_Ayres"}, {"title": "Argentina", "link": "https://wikipedia.org/wiki/Argentina"}]}, {"year": "1832", "text": "The Great Reform Act of England and Wales receives royal assent.", "html": "1832 - The <a href=\"https://wikipedia.org/wiki/Reform_Act_1832\" title=\"Reform Act 1832\">Great Reform Act</a> of England and Wales receives royal assent.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Reform_Act_1832\" title=\"Reform Act 1832\">Great Reform Act</a> of England and Wales receives royal assent.", "links": [{"title": "Reform Act 1832", "link": "https://wikipedia.org/wiki/Reform_Act_1832"}]}, {"year": "1832", "text": "Asian cholera reaches Quebec, brought by Irish immigrants, and kills about 6,000 people in Lower Canada.", "html": "1832 - <a href=\"https://wikipedia.org/wiki/1829%E2%80%9351_cholera_pandemic\" class=\"mw-redirect\" title=\"1829-51 cholera pandemic\">Asian cholera</a> reaches <a href=\"https://wikipedia.org/wiki/Quebec\" title=\"Quebec\">Quebec</a>, brought by <a href=\"https://wikipedia.org/wiki/Irish_Canadian\" class=\"mw-redirect\" title=\"Irish Canadian\">Irish immigrants</a>, and kills about 6,000 people in <a href=\"https://wikipedia.org/wiki/Lower_Canada\" title=\"Lower Canada\">Lower Canada</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/1829%E2%80%9351_cholera_pandemic\" class=\"mw-redirect\" title=\"1829-51 cholera pandemic\">Asian cholera</a> reaches <a href=\"https://wikipedia.org/wiki/Quebec\" title=\"Quebec\">Quebec</a>, brought by <a href=\"https://wikipedia.org/wiki/Irish_Canadian\" class=\"mw-redirect\" title=\"Irish Canadian\">Irish immigrants</a>, and kills about 6,000 people in <a href=\"https://wikipedia.org/wiki/Lower_Canada\" title=\"Lower Canada\">Lower Canada</a>.", "links": [{"title": "1829-51 cholera pandemic", "link": "https://wikipedia.org/wiki/1829%E2%80%9351_cholera_pandemic"}, {"title": "Quebec", "link": "https://wikipedia.org/wiki/Quebec"}, {"title": "Irish Canadian", "link": "https://wikipedia.org/wiki/Irish_Canadian"}, {"title": "Lower Canada", "link": "https://wikipedia.org/wiki/Lower_Canada"}]}, {"year": "1862", "text": "The United States and the United Kingdom agree in the Lyons-Seward Treaty to suppress the African slave trade.", "html": "1862 - The United States and the <a href=\"https://wikipedia.org/wiki/United_Kingdom\" title=\"United Kingdom\">United Kingdom</a> agree in the <a href=\"https://wikipedia.org/wiki/Lyons%E2%80%93Seward_Treaty_of_1862\" title=\"Lyons-Seward Treaty of 1862\">Lyons-Seward Treaty</a> to suppress the <a href=\"https://wikipedia.org/wiki/Atlantic_slave_trade\" title=\"Atlantic slave trade\">African slave trade</a>.", "no_year_html": "The United States and the <a href=\"https://wikipedia.org/wiki/United_Kingdom\" title=\"United Kingdom\">United Kingdom</a> agree in the <a href=\"https://wikipedia.org/wiki/Lyons%E2%80%93Seward_Treaty_of_1862\" title=\"Lyons-Seward Treaty of 1862\">Lyons-Seward Treaty</a> to suppress the <a href=\"https://wikipedia.org/wiki/Atlantic_slave_trade\" title=\"Atlantic slave trade\">African slave trade</a>.", "links": [{"title": "United Kingdom", "link": "https://wikipedia.org/wiki/United_Kingdom"}, {"title": "Lyons-Seward Treaty of 1862", "link": "https://wikipedia.org/wiki/Lyons%E2%80%93Seward_Treaty_of_1862"}, {"title": "Atlantic slave trade", "link": "https://wikipedia.org/wiki/Atlantic_slave_trade"}]}, {"year": "1866", "text": "One thousand eight hundred Fenian raiders are repelled back to the United States after looting and plundering the Saint-Armand and Frelighsburg areas of Canada East.", "html": "1866 - One thousand eight hundred <a href=\"https://wikipedia.org/wiki/Fenian_raids\" title=\"Fenian raids\">Fenian raiders</a> are repelled back to the United States after looting and plundering the <a href=\"https://wikipedia.org/wiki/Saint-Armand,_Quebec\" title=\"Saint-Armand, Quebec\"><PERSON><PERSON><PERSON></a> and <a href=\"https://wikipedia.org/wiki/Frelighsburg,_Quebec\" class=\"mw-redirect\" title=\"Frelighsburg, Quebec\">Frelighsburg</a> areas of <a href=\"https://wikipedia.org/wiki/Canada_East\" title=\"Canada East\">Canada East</a>.", "no_year_html": "One thousand eight hundred <a href=\"https://wikipedia.org/wiki/Fenian_raids\" title=\"Fenian raids\">Fenian raiders</a> are repelled back to the United States after looting and plundering the <a href=\"https://wikipedia.org/wiki/<PERSON>-<PERSON>,_Quebec\" title=\"Saint-Armand, Quebec\"><PERSON><PERSON><PERSON></a> and <a href=\"https://wikipedia.org/wiki/Frelighsburg,_Quebec\" class=\"mw-redirect\" title=\"Frelighsburg, Quebec\">Frelighsburg</a> areas of <a href=\"https://wikipedia.org/wiki/Canada_East\" title=\"Canada East\">Canada East</a>.", "links": [{"title": "Fenian raids", "link": "https://wikipedia.org/wiki/Fenian_raids"}, {"title": "Saint-Armand, Quebec", "link": "https://wikipedia.org/wiki/Saint-Armand,_Quebec"}, {"title": "Frelighsburg, Quebec", "link": "https://wikipedia.org/wiki/Frelighsburg,_Quebec"}, {"title": "Canada East", "link": "https://wikipedia.org/wiki/Canada_East"}]}, {"year": "1880", "text": "War of the Pacific: The Battle of Arica, the assault and capture of Morro de Arica (Arica Cape), ends the Campaña del Desierto (Desert Campaign).", "html": "1880 - <a href=\"https://wikipedia.org/wiki/War_of_the_Pacific\" title=\"War of the Pacific\">War of the Pacific</a>: The <a href=\"https://wikipedia.org/wiki/Battle_of_Arica\" title=\"Battle of Arica\">Battle of Arica</a>, the assault and capture of <a href=\"https://wikipedia.org/wiki/Morro_de_Arica\" title=\"Morro de Arica\">Morro de Arica</a> (Arica Cape), ends the <i>Campaña del Desierto</i> (Desert Campaign).", "no_year_html": "<a href=\"https://wikipedia.org/wiki/War_of_the_Pacific\" title=\"War of the Pacific\">War of the Pacific</a>: The <a href=\"https://wikipedia.org/wiki/Battle_of_Arica\" title=\"Battle of Arica\">Battle of Arica</a>, the assault and capture of <a href=\"https://wikipedia.org/wiki/Morro_de_Arica\" title=\"Morro de Arica\">Morro de Arica</a> (Arica Cape), ends the <i>Campaña del Desierto</i> (Desert Campaign).", "links": [{"title": "War of the Pacific", "link": "https://wikipedia.org/wiki/War_of_the_Pacific"}, {"title": "Battle of Arica", "link": "https://wikipedia.org/wiki/Battle_of_Arica"}, {"title": "Morro de Arica", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_de_<PERSON>"}]}, {"year": "1892", "text": "<PERSON> is arrested for refusing to leave his seat in the \"whites-only\" car of a train; he lost the resulting court case, <PERSON><PERSON><PERSON> v. Ferguson.", "html": "1892 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Plessy\"><PERSON></a> is arrested for refusing to leave his seat in the \"whites-only\" car of a train; he lost the resulting court case, <i><a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_v<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON> v<PERSON>\"><PERSON><PERSON><PERSON> v<PERSON> Ferguson</a></i>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Pless<PERSON>\"><PERSON></a> is arrested for refusing to leave his seat in the \"whites-only\" car of a train; he lost the resulting court case, <i><a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_v<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON> v<PERSON>\"><PERSON><PERSON><PERSON> v<PERSON> Ferguson</a></i>.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "<PERSON><PERSON><PERSON> v. Ferguson", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_v._<PERSON>"}]}, {"year": "1899", "text": "American Temperance crusader <PERSON> begins her campaign of vandalizing alcohol-serving establishments by destroying the inventory in a saloon in Kiowa, Kansas.", "html": "1899 - American <a href=\"https://wikipedia.org/wiki/Temperance_movement_in_the_United_States\" title=\"Temperance movement in the United States\">Temperance</a> crusader <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Carrie Nation\"><PERSON></a> begins her campaign of <a href=\"https://wikipedia.org/wiki/Vandalism\" title=\"Vandalism\">vandalizing</a> alcohol-serving establishments by destroying the inventory in a saloon in <a href=\"https://wikipedia.org/wiki/Kiowa,_Kansas\" title=\"Kiowa, Kansas\">Kiowa, Kansas</a>.", "no_year_html": "American <a href=\"https://wikipedia.org/wiki/Temperance_movement_in_the_United_States\" title=\"Temperance movement in the United States\">Temperance</a> crusader <a href=\"https://wikipedia.org/wiki/Carrie_<PERSON>\" title=\"Carrie Nation\"><PERSON></a> begins her campaign of <a href=\"https://wikipedia.org/wiki/Vandalism\" title=\"Vandalism\">vandalizing</a> alcohol-serving establishments by destroying the inventory in a saloon in <a href=\"https://wikipedia.org/wiki/Kiowa,_Kansas\" title=\"Kiowa, Kansas\">Kiowa, Kansas</a>.", "links": [{"title": "Temperance movement in the United States", "link": "https://wikipedia.org/wiki/Temperance_movement_in_the_United_States"}, {"title": "Carrie <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Vandalism", "link": "https://wikipedia.org/wiki/Vandalism"}, {"title": "Kiowa, Kansas", "link": "https://wikipedia.org/wiki/Kiowa,_Kansas"}]}, {"year": "1905", "text": "Norway's parliament dissolves its union with Sweden. The vote was confirmed by a national plebiscite on August 13 of that year.", "html": "1905 - Norway's parliament <a href=\"https://wikipedia.org/wiki/Dissolution_of_the_union_between_Norway_and_Sweden\" title=\"Dissolution of the union between Norway and Sweden\">dissolves</a> its <a href=\"https://wikipedia.org/wiki/Union_between_Sweden_and_Norway\" title=\"Union between Sweden and Norway\">union with Sweden</a>. The vote was confirmed by a national <a href=\"https://wikipedia.org/wiki/1905_Norwegian_union_dissolution_referendum\" title=\"1905 Norwegian union dissolution referendum\">plebiscite</a> on <a href=\"https://wikipedia.org/wiki/August_13\" title=\"August 13\">August 13</a> of that year.", "no_year_html": "Norway's parliament <a href=\"https://wikipedia.org/wiki/Dissolution_of_the_union_between_Norway_and_Sweden\" title=\"Dissolution of the union between Norway and Sweden\">dissolves</a> its <a href=\"https://wikipedia.org/wiki/Union_between_Sweden_and_Norway\" title=\"Union between Sweden and Norway\">union with Sweden</a>. The vote was confirmed by a national <a href=\"https://wikipedia.org/wiki/1905_Norwegian_union_dissolution_referendum\" title=\"1905 Norwegian union dissolution referendum\">plebiscite</a> on <a href=\"https://wikipedia.org/wiki/August_13\" title=\"August 13\">August 13</a> of that year.", "links": [{"title": "Dissolution of the union between Norway and Sweden", "link": "https://wikipedia.org/wiki/Dissolution_of_the_union_between_Norway_and_Sweden"}, {"title": "Union between Sweden and Norway", "link": "https://wikipedia.org/wiki/Union_between_Sweden_and_Norway"}, {"title": "1905 Norwegian union dissolution referendum", "link": "https://wikipedia.org/wiki/1905_Norwegian_union_dissolution_referendum"}, {"title": "August 13", "link": "https://wikipedia.org/wiki/August_13"}]}, {"year": "1906", "text": "Cunard Line's RMS Lusitania is launched from the John Brown Shipyard, Glasgow (Clydebank), Scotland.", "html": "1906 - <a href=\"https://wikipedia.org/wiki/Cunard_Line\" title=\"Cunard Line\">Cunard Line</a>'s <a href=\"https://wikipedia.org/wiki/RMS_Lusitania\" title=\"RMS Lusitania\">RMS <i>Lusitania</i></a> is launched from the <a href=\"https://wikipedia.org/wiki/John_Brown_Shipyard\" class=\"mw-redirect\" title=\"John Brown Shipyard\">John <PERSON></a>, <a href=\"https://wikipedia.org/wiki/Glasgow\" title=\"Glasgow\">Glasgow</a> (<a href=\"https://wikipedia.org/wiki/Clydebank\" title=\"Clydebank\">Clydebank</a>), Scotland.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Cunard_Line\" title=\"Cunard Line\">Cunard Line</a>'s <a href=\"https://wikipedia.org/wiki/RMS_Lusitania\" title=\"RMS Lusitania\">RMS <i>Lusitania</i></a> is launched from the <a href=\"https://wikipedia.org/wiki/John_Brown_Shipyard\" class=\"mw-redirect\" title=\"John Brown Shipyard\">John Brown Shipyard</a>, <a href=\"https://wikipedia.org/wiki/Glasgow\" title=\"Glasgow\">Glasgow</a> (<a href=\"https://wikipedia.org/wiki/Clydebank\" title=\"Clydebank\">Clydebank</a>), Scotland.", "links": [{"title": "Cunard Line", "link": "https://wikipedia.org/wiki/Cunard_Line"}, {"title": "RMS Lusitania", "link": "https://wikipedia.org/wiki/RMS_Lusitania"}, {"title": "John <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_Shipyard"}, {"title": "Glasgow", "link": "https://wikipedia.org/wiki/Glasgow"}, {"title": "Clydebank", "link": "https://wikipedia.org/wiki/Clydebank"}]}, {"year": "1917", "text": "World War I: Battle of Messines: Allied soldiers detonate a series of mines underneath German trenches at Messines Ridge, killing 10,000 German troops.", "html": "1917 - <a href=\"https://wikipedia.org/wiki/World_War_I\" title=\"World War I\">World War I</a>: <a href=\"https://wikipedia.org/wiki/Battle_of_Messines_(1917)\" title=\"Battle of Messines (1917)\">Battle of Messines</a>: Allied soldiers detonate <a href=\"https://wikipedia.org/wiki/Mines_in_the_Battle_of_Messines_(1917)\" title=\"Mines in the Battle of Messines (1917)\">a series of mines</a> underneath German trenches at Messines Ridge, killing 10,000 German troops.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/World_War_I\" title=\"World War I\">World War I</a>: <a href=\"https://wikipedia.org/wiki/Battle_of_Messines_(1917)\" title=\"Battle of Messines (1917)\">Battle of Messines</a>: Allied soldiers detonate <a href=\"https://wikipedia.org/wiki/Mines_in_the_Battle_of_Messines_(1917)\" title=\"Mines in the Battle of Messines (1917)\">a series of mines</a> underneath German trenches at Messines Ridge, killing 10,000 German troops.", "links": [{"title": "World War I", "link": "https://wikipedia.org/wiki/World_War_I"}, {"title": "Battle of Messines (1917)", "link": "https://wikipedia.org/wiki/Battle_of_Messines_(1917)"}, {"title": "Mines in the Battle of Messines (1917)", "link": "https://wikipedia.org/wiki/Mines_in_the_Battle_of_Messines_(1917)"}]}, {"year": "1919", "text": "Sette Giugno: Nationalist riots break out in Valletta, the capital of Malta. British soldiers fire into the crowd, killing four people.", "html": "1919 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_G<PERSON>\" title=\"Sette Giugno\"><PERSON><PERSON></a>: Nationalist riots break out in <a href=\"https://wikipedia.org/wiki/Valletta\" title=\"Valletta\">Valletta</a>, the capital of <a href=\"https://wikipedia.org/wiki/Malta\" title=\"Malta\">Malta</a>. British soldiers fire into the crowd, killing four people.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"Set<PERSON> Giugno\"><PERSON><PERSON></a>: Nationalist riots break out in <a href=\"https://wikipedia.org/wiki/Valletta\" title=\"Valletta\">Valletta</a>, the capital of <a href=\"https://wikipedia.org/wiki/Malta\" title=\"Malta\">Malta</a>. British soldiers fire into the crowd, killing four people.", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}, {"title": "Valletta", "link": "https://wikipedia.org/wiki/Valletta"}, {"title": "Malta", "link": "https://wikipedia.org/wiki/Malta"}]}, {"year": "1929", "text": "The Lateran Treaty is ratified, bringing Vatican City into existence.", "html": "1929 - The <a href=\"https://wikipedia.org/wiki/Lateran_Treaty\" title=\"Lateran Treaty\">Lateran Treaty</a> is ratified, bringing <a href=\"https://wikipedia.org/wiki/Vatican_City\" title=\"Vatican City\">Vatican City</a> into existence.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Lateran_Treaty\" title=\"Lateran Treaty\">Lateran Treaty</a> is ratified, bringing <a href=\"https://wikipedia.org/wiki/Vatican_City\" title=\"Vatican City\">Vatican City</a> into existence.", "links": [{"title": "Lateran Treaty", "link": "https://wikipedia.org/wiki/Lateran_Treaty"}, {"title": "Vatican City", "link": "https://wikipedia.org/wiki/Vatican_City"}]}, {"year": "1938", "text": "The Douglas DC-4E makes its first test flight.", "html": "1938 - The <a href=\"https://wikipedia.org/wiki/Douglas_DC-4E\" title=\"Douglas DC-4E\">Douglas DC-4E</a> makes its first test flight.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Douglas_DC-4E\" title=\"Douglas DC-4E\">Douglas DC-4E</a> makes its first test flight.", "links": [{"title": "Douglas DC-4E", "link": "https://wikipedia.org/wiki/Douglas_DC-4E"}]}, {"year": "1938", "text": "Second Sino-Japanese War: The Chinese Nationalist government creates the 1938 Yellow River flood to halt Japanese forces. Five hundred thousand to nine hundred thousand civilians are killed.", "html": "1938 - <a href=\"https://wikipedia.org/wiki/Second_Sino-Japanese_War\" title=\"Second Sino-Japanese War\">Second Sino-Japanese War</a>: The <a href=\"https://wikipedia.org/wiki/Kuomintang\" title=\"Kuomintang\">Chinese Nationalist</a> government creates the <a href=\"https://wikipedia.org/wiki/1938_Yellow_River_flood\" title=\"1938 Yellow River flood\">1938 Yellow River flood</a> to halt <a href=\"https://wikipedia.org/wiki/Imperial_Japanese_Army\" title=\"Imperial Japanese Army\">Japanese forces</a>. Five hundred thousand to nine hundred thousand civilians are killed.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Second_Sino-Japanese_War\" title=\"Second Sino-Japanese War\">Second Sino-Japanese War</a>: The <a href=\"https://wikipedia.org/wiki/Kuomintang\" title=\"Kuomintang\">Chinese Nationalist</a> government creates the <a href=\"https://wikipedia.org/wiki/1938_Yellow_River_flood\" title=\"1938 Yellow River flood\">1938 Yellow River flood</a> to halt <a href=\"https://wikipedia.org/wiki/Imperial_Japanese_Army\" title=\"Imperial Japanese Army\">Japanese forces</a>. Five hundred thousand to nine hundred thousand civilians are killed.", "links": [{"title": "Second Sino-Japanese War", "link": "https://wikipedia.org/wiki/Second_Sino-Japanese_War"}, {"title": "Kuomintang", "link": "https://wikipedia.org/wiki/Kuomintang"}, {"title": "1938 Yellow River flood", "link": "https://wikipedia.org/wiki/1938_Yellow_River_flood"}, {"title": "Imperial Japanese Army", "link": "https://wikipedia.org/wiki/Imperial_Japanese_Army"}]}, {"year": "1940", "text": "King <PERSON><PERSON><PERSON>, Crown Prince <PERSON><PERSON> and the Norwegian government leave Tromsø and go into exile in London. They return exactly five years later.", "html": "1940 - <a href=\"https://wikipedia.org/wiki/Haakon_VII_of_Norway\" class=\"mw-redirect\" title=\"Haakon VII of Norway\">King <PERSON><PERSON><PERSON> VII</a>, <a href=\"https://wikipedia.org/wiki/Olav_V_of_Norway\" class=\"mw-redirect\" title=\"<PERSON>lav V of Norway\">Crown Prince <PERSON></a> and the Norwegian government leave <a href=\"https://wikipedia.org/wiki/Troms%C3%B8_(city)\" title=\"Tromsø (city)\">Tromsø</a> and go into exile in London. They return exactly five years later.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Haakon_VII_of_Norway\" class=\"mw-redirect\" title=\"Haakon VII of Norway\">King <PERSON><PERSON><PERSON> VII</a>, <a href=\"https://wikipedia.org/wiki/Olav_V_of_Norway\" class=\"mw-redirect\" title=\"Olav V of Norway\">Crown Prince <PERSON><PERSON></a> and the Norwegian government leave <a href=\"https://wikipedia.org/wiki/Troms%C3%B8_(city)\" title=\"Tromsø (city)\">Tromsø</a> and go into exile in London. They return exactly five years later.", "links": [{"title": "Haakon VII of Norway", "link": "https://wikipedia.org/wiki/Haakon_VII_of_Norway"}, {"title": "<PERSON><PERSON> of <PERSON>", "link": "https://wikipedia.org/wiki/Olav_V_of_Norway"}, {"title": "Tromsø (city)", "link": "https://wikipedia.org/wiki/Troms%C3%B8_(city)"}]}, {"year": "1942", "text": "World War II: The Battle of Midway ends in American victory.", "html": "1942 - <a href=\"https://wikipedia.org/wiki/World_War_II\" title=\"World War II\">World War II</a>: The <a href=\"https://wikipedia.org/wiki/Battle_of_Midway\" title=\"Battle of Midway\">Battle of Midway</a> ends in American victory.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/World_War_II\" title=\"World War II\">World War II</a>: The <a href=\"https://wikipedia.org/wiki/Battle_of_Midway\" title=\"Battle of Midway\">Battle of Midway</a> ends in American victory.", "links": [{"title": "World War II", "link": "https://wikipedia.org/wiki/World_War_II"}, {"title": "Battle of Midway", "link": "https://wikipedia.org/wiki/Battle_of_Midway"}]}, {"year": "1942", "text": "World War II: Aleutian Islands Campaign: Imperial Japanese soldiers begin occupying the American islands of Attu and Kiska, in the Aleutian Islands off Alaska.", "html": "1942 - World War II: <a href=\"https://wikipedia.org/wiki/Aleutian_Islands_Campaign\" class=\"mw-redirect\" title=\"Aleutian Islands Campaign\">Aleutian Islands Campaign</a>: <a href=\"https://wikipedia.org/wiki/Empire_of_Japan\" title=\"Empire of Japan\">Imperial Japanese</a> soldiers begin occupying the American islands of <a href=\"https://wikipedia.org/wiki/Attu_Island\" title=\"Attu Island\">Attu</a> and <a href=\"https://wikipedia.org/wiki/Kiska\" title=\"Kiska\">Kiska</a>, in the <a href=\"https://wikipedia.org/wiki/Aleutian_Islands\" title=\"Aleutian Islands\">Aleutian Islands</a> off Alaska.", "no_year_html": "World War II: <a href=\"https://wikipedia.org/wiki/Aleutian_Islands_Campaign\" class=\"mw-redirect\" title=\"Aleutian Islands Campaign\">Aleutian Islands Campaign</a>: <a href=\"https://wikipedia.org/wiki/Empire_of_Japan\" title=\"Empire of Japan\">Imperial Japanese</a> soldiers begin occupying the American islands of <a href=\"https://wikipedia.org/wiki/Attu_Island\" title=\"Attu Island\">Attu</a> and <a href=\"https://wikipedia.org/wiki/Kiska\" title=\"Kiska\">Kiska</a>, in the <a href=\"https://wikipedia.org/wiki/Aleutian_Islands\" title=\"Aleutian Islands\">Aleutian Islands</a> off Alaska.", "links": [{"title": "Aleutian Islands Campaign", "link": "https://wikipedia.org/wiki/Aleutian_Islands_Campaign"}, {"title": "Empire of Japan", "link": "https://wikipedia.org/wiki/Empire_of_Japan"}, {"title": "Attu Island", "link": "https://wikipedia.org/wiki/Attu_Island"}, {"title": "Kiska", "link": "https://wikipedia.org/wiki/Kiska"}, {"title": "Aleutian Islands", "link": "https://wikipedia.org/wiki/Aleutian_Islands"}]}, {"year": "1944", "text": "World War II: Battle of Normandy: At Ardenne Abbey, members of the SS Division Hitlerjugend massacre 23 Canadian prisoners of war.", "html": "1944 - World War II: <a href=\"https://wikipedia.org/wiki/Invasion_of_Normandy\" class=\"mw-redirect\" title=\"Invasion of Normandy\">Battle of Normandy</a>: At <a href=\"https://wikipedia.org/wiki/Ardenne_Abbey\" title=\"Ardenne Abbey\">Ardenne Abbey</a>, members of the <a href=\"https://wikipedia.org/wiki/SS_Division_Hitlerjugend\" class=\"mw-redirect\" title=\"SS Division Hitlerjugend\">SS Division Hitlerjugend</a> <a href=\"https://wikipedia.org/wiki/Ardenne_Abbey_massacre\" title=\"Ardenne Abbey massacre\">massacre</a> 23 Canadian <a href=\"https://wikipedia.org/wiki/Prisoners_of_war\" class=\"mw-redirect\" title=\"Prisoners of war\">prisoners of war</a>.", "no_year_html": "World War II: <a href=\"https://wikipedia.org/wiki/Invasion_of_Normandy\" class=\"mw-redirect\" title=\"Invasion of Normandy\">Battle of Normandy</a>: At <a href=\"https://wikipedia.org/wiki/Ardenne_Abbey\" title=\"Ardenne Abbey\">Ardenne Abbey</a>, members of the <a href=\"https://wikipedia.org/wiki/SS_Division_Hitlerjugend\" class=\"mw-redirect\" title=\"SS Division Hitlerjugend\">SS Division Hitlerjugend</a> <a href=\"https://wikipedia.org/wiki/Ardenne_Abbey_massacre\" title=\"Ardenne Abbey massacre\">massacre</a> 23 Canadian <a href=\"https://wikipedia.org/wiki/Prisoners_of_war\" class=\"mw-redirect\" title=\"Prisoners of war\">prisoners of war</a>.", "links": [{"title": "Invasion of Normandy", "link": "https://wikipedia.org/wiki/Invasion_of_Normandy"}, {"title": "Ardenne Abbey", "link": "https://wikipedia.org/wiki/Ardenne_Abbey"}, {"title": "SS Division Hitlerjugend", "link": "https://wikipedia.org/wiki/SS_Division_Hitlerjugend"}, {"title": "Ardenne Abbey massacre", "link": "https://wikipedia.org/wiki/Ardenne_Abbey_massacre"}, {"title": "Prisoners of war", "link": "https://wikipedia.org/wiki/Prisoners_of_war"}]}, {"year": "1945", "text": "King <PERSON><PERSON><PERSON> of Norway returns from exactly five years in exile during World War II.", "html": "1945 - King <a href=\"https://wikipedia.org/wiki/Haakon_VII_of_Norway\" class=\"mw-redirect\" title=\"Haakon VII of Norway\">Haakon VII of Norway</a> returns from exactly <a href=\"https://wikipedia.org/wiki/Union_Dissolution_Day#Royal_return_after_WWII\" title=\"Union Dissolution Day\">five years</a> in <a href=\"https://wikipedia.org/wiki/Exile\" title=\"Exile\">exile</a> during World War II.", "no_year_html": "King <a href=\"https://wikipedia.org/wiki/Haakon_VII_of_Norway\" class=\"mw-redirect\" title=\"Haakon VII of Norway\">Haakon VII of Norway</a> returns from exactly <a href=\"https://wikipedia.org/wiki/Union_Dissolution_Day#Royal_return_after_WWII\" title=\"Union Dissolution Day\">five years</a> in <a href=\"https://wikipedia.org/wiki/Exile\" title=\"Exile\">exile</a> during World War II.", "links": [{"title": "Haakon VII of Norway", "link": "https://wikipedia.org/wiki/Haakon_VII_of_Norway"}, {"title": "Union Dissolution Day", "link": "https://wikipedia.org/wiki/Union_Dissolution_Day#Royal_return_after_WWII"}, {"title": "Exile", "link": "https://wikipedia.org/wiki/Exile"}]}, {"year": "1946", "text": "The United Kingdom's BBC returns to broadcasting its television service, which has been off air for seven years because of World War II.", "html": "1946 - The United Kingdom's <a href=\"https://wikipedia.org/wiki/BBC\" title=\"BBC\">BBC</a> returns to broadcasting its television service, which has been off air for seven years because of World War II.", "no_year_html": "The United Kingdom's <a href=\"https://wikipedia.org/wiki/BBC\" title=\"BBC\">BBC</a> returns to broadcasting its television service, which has been off air for seven years because of World War II.", "links": [{"title": "BBC", "link": "https://wikipedia.org/wiki/BBC"}]}, {"year": "1948", "text": "Anti-Jewish riots in Oujda and Jerada take place.", "html": "1948 - <a href=\"https://wikipedia.org/wiki/1948_Anti-Jewish_riots_in_Oujda_and_Jerada\" class=\"mw-redirect\" title=\"1948 Anti-Jewish riots in Oujda and Jerada\">Anti-Jewish riots in Oujda and Jerada</a> take place.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/1948_Anti-Jewish_riots_in_Oujda_and_Jerada\" class=\"mw-redirect\" title=\"1948 Anti-Jewish riots in Oujda and Jerada\">Anti-Jewish riots in Oujda and Jerada</a> take place.", "links": [{"title": "1948 Anti-Jewish riots in Oujda and Jerada", "link": "https://wikipedia.org/wiki/1948_Anti-Jewish_riots_in_Oujda_and_Je<PERSON>"}]}, {"year": "1948", "text": "<PERSON><PERSON> resigns as President of Czechoslovakia rather than signing the Ninth-of-May Constitution, making his nation a Communist state.", "html": "1948 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>%C5%A1\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a> resigns as <a href=\"https://wikipedia.org/wiki/List_of_Presidents_of_Czechoslovakia\" class=\"mw-redirect\" title=\"List of Presidents of Czechoslovakia\">President</a> of <a href=\"https://wikipedia.org/wiki/Czechoslovakia\" title=\"Czechoslovakia\">Czechoslovakia</a> rather than signing the <a href=\"https://wikipedia.org/wiki/Ninth-of-May_Constitution\" title=\"Ninth-of-May Constitution\">Ninth-of-May Constitution</a>, making his nation a <a href=\"https://wikipedia.org/wiki/Communist_state\" title=\"Communist state\">Communist state</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>%C5%A1\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a> resigns as <a href=\"https://wikipedia.org/wiki/List_of_Presidents_of_Czechoslovakia\" class=\"mw-redirect\" title=\"List of Presidents of Czechoslovakia\">President</a> of <a href=\"https://wikipedia.org/wiki/Czechoslovakia\" title=\"Czechoslovakia\">Czechoslovakia</a> rather than signing the <a href=\"https://wikipedia.org/wiki/Ninth-of-May_Constitution\" title=\"Ninth-of-May Constitution\">Ninth-of-May Constitution</a>, making his nation a <a href=\"https://wikipedia.org/wiki/Communist_state\" title=\"Communist state\">Communist state</a>.", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Edvard_Bene%C5%A1"}, {"title": "List of Presidents of Czechoslovakia", "link": "https://wikipedia.org/wiki/List_of_Presidents_of_Czechoslovakia"}, {"title": "Czechoslovakia", "link": "https://wikipedia.org/wiki/Czechoslovakia"}, {"title": "Ninth-of-May Constitution", "link": "https://wikipedia.org/wiki/Ninth-of-May_Constitution"}, {"title": "Communist state", "link": "https://wikipedia.org/wiki/Communist_state"}]}, {"year": "1955", "text": "Lux Radio Theatre signs off the air permanently. The show launched in New York in 1934, and featured radio adaptations of Broadway shows and popular films.", "html": "1955 - <i><a href=\"https://wikipedia.org/wiki/Lux_Radio_Theatre\" title=\"Lux Radio Theatre\">Lux Radio Theatre</a></i> signs off the air permanently. The show launched in New York in 1934, and featured radio adaptations of <a href=\"https://wikipedia.org/wiki/Broadway_theatre\" title=\"Broadway theatre\">Broadway</a> shows and popular films.", "no_year_html": "<i><a href=\"https://wikipedia.org/wiki/Lux_Radio_Theatre\" title=\"Lux Radio Theatre\">Lux Radio Theatre</a></i> signs off the air permanently. The show launched in New York in 1934, and featured radio adaptations of <a href=\"https://wikipedia.org/wiki/Broadway_theatre\" title=\"Broadway theatre\">Broadway</a> shows and popular films.", "links": [{"title": "Lux Radio Theatre", "link": "https://wikipedia.org/wiki/Lux_Radio_Theatre"}, {"title": "Broadway theatre", "link": "https://wikipedia.org/wiki/Broadway_theatre"}]}, {"year": "1962", "text": "The Organisation Armée Secrète (OAS) sets fire to the University of Algiers library building, destroying about 500,000 books.", "html": "1962 - The <a href=\"https://wikipedia.org/wiki/Organisation_Arm%C3%A9e_Secr%C3%A8te\" class=\"mw-redirect\" title=\"Organisation Armée Secrète\">Organisation Armée Secrète</a> (OAS) sets fire to the <a href=\"https://wikipedia.org/wiki/University_of_Algiers\" class=\"mw-redirect\" title=\"University of Algiers\">University of Algiers</a> library building, destroying about 500,000 books.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Organisation_Arm%C3%A9e_Secr%C3%A8te\" class=\"mw-redirect\" title=\"Organisation Armée Secrète\">Organisation Armée Secrète</a> (OAS) sets fire to the <a href=\"https://wikipedia.org/wiki/University_of_Algiers\" class=\"mw-redirect\" title=\"University of Algiers\">University of Algiers</a> library building, destroying about 500,000 books.", "links": [{"title": "Organisation Armée Secrète", "link": "https://wikipedia.org/wiki/Organisation_Arm%C3%A9e_Secr%C3%A8te"}, {"title": "University of Algiers", "link": "https://wikipedia.org/wiki/University_of_Algiers"}]}, {"year": "1965", "text": "The Supreme Court of the United States hands down its decision in <PERSON><PERSON><PERSON> v. Connecticut, prohibiting the states from criminalizing the use of contraception by married couples.", "html": "1965 - The <a href=\"https://wikipedia.org/wiki/Supreme_Court_of_the_United_States\" title=\"Supreme Court of the United States\">Supreme Court of the United States</a> hands down its decision in <i><a href=\"https://wikipedia.org/wiki/Griswold_v._Connecticut\" title=\"Griswold v. Connecticut\"><PERSON><PERSON><PERSON> v. Connecticut</a></i>, prohibiting the states from criminalizing the use of <a href=\"https://wikipedia.org/wiki/Contraception\" class=\"mw-redirect\" title=\"Contraception\">contraception</a> by married couples.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Supreme_Court_of_the_United_States\" title=\"Supreme Court of the United States\">Supreme Court of the United States</a> hands down its decision in <i><a href=\"https://wikipedia.org/wiki/Griswold_v._Connecticut\" title=\"Griswold v. Connecticut\"><PERSON><PERSON><PERSON> v. Connecticut</a></i>, prohibiting the states from criminalizing the use of <a href=\"https://wikipedia.org/wiki/Contraception\" class=\"mw-redirect\" title=\"Contraception\">contraception</a> by married couples.", "links": [{"title": "Supreme Court of the United States", "link": "https://wikipedia.org/wiki/Supreme_Court_of_the_United_States"}, {"title": "<PERSON><PERSON><PERSON> v. Connecticut", "link": "https://wikipedia.org/wiki/Griswold_v._Connecticut"}, {"title": "Contraception", "link": "https://wikipedia.org/wiki/Contraception"}]}, {"year": "1967", "text": "Six-Day War: Israeli soldiers enter Jerusalem.", "html": "1967 - <a href=\"https://wikipedia.org/wiki/Six-Day_War\" title=\"Six-Day War\">Six-Day War</a>: <a href=\"https://wikipedia.org/wiki/Israel\" title=\"Israel\">Israeli</a> soldiers enter <a href=\"https://wikipedia.org/wiki/Jerusalem\" title=\"Jerusalem\">Jerusalem</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Six-Day_War\" title=\"Six-Day War\">Six-Day War</a>: <a href=\"https://wikipedia.org/wiki/Israel\" title=\"Israel\">Israeli</a> soldiers enter <a href=\"https://wikipedia.org/wiki/Jerusalem\" title=\"Jerusalem\">Jerusalem</a>.", "links": [{"title": "Six-Day War", "link": "https://wikipedia.org/wiki/Six-Day_War"}, {"title": "Israel", "link": "https://wikipedia.org/wiki/Israel"}, {"title": "Jerusalem", "link": "https://wikipedia.org/wiki/Jerusalem"}]}, {"year": "1971", "text": "The United States Supreme Court overturns the conviction of <PERSON> for disturbing the peace, setting the precedent that vulgar writing is protected under the First Amendment to the United States Constitution.", "html": "1971 - The United States Supreme Court overturns the conviction of <a href=\"https://wikipedia.org/wiki/<PERSON>_v._California\" title=\"Cohen v. California\"><PERSON></a> for disturbing the peace, setting the precedent that vulgar writing is protected under the <a href=\"https://wikipedia.org/wiki/First_Amendment_to_the_United_States_Constitution\" title=\"First Amendment to the United States Constitution\">First Amendment to the United States Constitution</a>.", "no_year_html": "The United States Supreme Court overturns the conviction of <a href=\"https://wikipedia.org/wiki/<PERSON>_v._California\" title=\"Cohen v. California\"><PERSON></a> for disturbing the peace, setting the precedent that vulgar writing is protected under the <a href=\"https://wikipedia.org/wiki/First_Amendment_to_the_United_States_Constitution\" title=\"First Amendment to the United States Constitution\">First Amendment to the United States Constitution</a>.", "links": [{"title": "<PERSON> v. California", "link": "https://wikipedia.org/wiki/Cohen_v._California"}, {"title": "First Amendment to the United States Constitution", "link": "https://wikipedia.org/wiki/First_Amendment_to_the_United_States_Constitution"}]}, {"year": "1971", "text": "The Alcohol, Tobacco, and Firearms Division of the U.S. Internal Revenue Service raids the home of <PERSON> for illegal possession of hand grenades.", "html": "1971 - The <a href=\"https://wikipedia.org/wiki/Bureau_of_Alcohol,_Tobacco,_Firearms_and_Explosives\" title=\"Bureau of Alcohol, Tobacco, Firearms and Explosives\">Alcohol, Tobacco, and Firearms Division</a> of the U.S. <a href=\"https://wikipedia.org/wiki/Internal_Revenue_Service\" title=\"Internal Revenue Service\">Internal Revenue Service</a> raids the home of <a href=\"https://wikipedia.org/wiki/Ken_<PERSON>_raid\" title=\"Ken Ballew raid\"><PERSON></a> for illegal possession of <a href=\"https://wikipedia.org/wiki/Hand_grenade\" class=\"mw-redirect\" title=\"Hand grenade\">hand grenades</a>.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Bureau_of_Alcohol,_Tobacco,_Firearms_and_Explosives\" title=\"Bureau of Alcohol, Tobacco, Firearms and Explosives\">Alcohol, Tobacco, and Firearms Division</a> of the U.S. <a href=\"https://wikipedia.org/wiki/Internal_Revenue_Service\" title=\"Internal Revenue Service\">Internal Revenue Service</a> raids the home of <a href=\"https://wikipedia.org/wiki/Ken_<PERSON>_raid\" title=\"Ken Ballew raid\"><PERSON></a> for illegal possession of <a href=\"https://wikipedia.org/wiki/Hand_grenade\" class=\"mw-redirect\" title=\"Hand grenade\">hand grenades</a>.", "links": [{"title": "Bureau of Alcohol, Tobacco, Firearms and Explosives", "link": "https://wikipedia.org/wiki/Bureau_of_Alcohol,_Tobacco,_Firearms_and_Explosives"}, {"title": "Internal Revenue Service", "link": "https://wikipedia.org/wiki/Internal_Revenue_Service"}, {"title": "<PERSON> raid", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_raid"}, {"title": "Hand grenade", "link": "https://wikipedia.org/wiki/Hand_grenade"}]}, {"year": "1971", "text": "Allegheny Airlines Flight 485 crashes on approach to Tweed New Haven Airport in New Haven, Connecticut, killing 28 of 31 aboard.", "html": "1971 - <a href=\"https://wikipedia.org/wiki/Allegheny_Airlines_Flight_485\" title=\"Allegheny Airlines Flight 485\">Allegheny Airlines Flight 485</a> crashes on approach to <a href=\"https://wikipedia.org/wiki/Tweed_New_Haven_Airport\" title=\"Tweed New Haven Airport\">Tweed New Haven Airport</a> in <a href=\"https://wikipedia.org/wiki/New_Haven,_Connecticut\" title=\"New Haven, Connecticut\">New Haven, Connecticut</a>, killing 28 of 31 aboard.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Allegheny_Airlines_Flight_485\" title=\"Allegheny Airlines Flight 485\">Allegheny Airlines Flight 485</a> crashes on approach to <a href=\"https://wikipedia.org/wiki/Tweed_New_Haven_Airport\" title=\"Tweed New Haven Airport\">Tweed New Haven Airport</a> in <a href=\"https://wikipedia.org/wiki/New_Haven,_Connecticut\" title=\"New Haven, Connecticut\">New Haven, Connecticut</a>, killing 28 of 31 aboard.", "links": [{"title": "Allegheny Airlines Flight 485", "link": "https://wikipedia.org/wiki/Allegheny_Airlines_Flight_485"}, {"title": "Tweed New Haven Airport", "link": "https://wikipedia.org/wiki/Tweed_New_Haven_Airport"}, {"title": "New Haven, Connecticut", "link": "https://wikipedia.org/wiki/New_Haven,_Connecticut"}]}, {"year": "1975", "text": "Sony launches Betamax, the first videocassette recorder format.", "html": "1975 - <a href=\"https://wikipedia.org/wiki/Sony\" title=\"Sony\">Sony</a> launches <a href=\"https://wikipedia.org/wiki/Betamax\" title=\"Betamax\">Betamax</a>, the first <a href=\"https://wikipedia.org/wiki/Videocassette_recorder\" title=\"Videocassette recorder\">videocassette recorder</a> format.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Sony\" title=\"Sony\">Sony</a> launches <a href=\"https://wikipedia.org/wiki/Betamax\" title=\"Betamax\">Betamax</a>, the first <a href=\"https://wikipedia.org/wiki/Videocassette_recorder\" title=\"Videocassette recorder\">videocassette recorder</a> format.", "links": [{"title": "Sony", "link": "https://wikipedia.org/wiki/Sony"}, {"title": "Betamax", "link": "https://wikipedia.org/wiki/Betamax"}, {"title": "Videocassette recorder", "link": "https://wikipedia.org/wiki/Videocassette_recorder"}]}, {"year": "1977", "text": "Five hundred million people watch the high day of the Silver Jubilee of Queen <PERSON> begin on television.", "html": "1977 - Five hundred million people watch the high day of the <a href=\"https://wikipedia.org/wiki/Silver_Jubilee_of_<PERSON>_II\" title=\"Silver Jubilee of Elizabeth II\">Silver Jubilee</a> of Queen <a href=\"https://wikipedia.org/wiki/Elizabeth_II\" title=\"Elizabeth II\"><PERSON> II</a> begin on television.", "no_year_html": "Five hundred million people watch the high day of the <a href=\"https://wikipedia.org/wiki/Silver_Jubilee_of_Elizabeth_II\" title=\"Silver Jubilee of Elizabeth II\">Silver Jubilee</a> of Queen <a href=\"https://wikipedia.org/wiki/Elizabeth_II\" title=\"Elizabeth II\"><PERSON> II</a> begin on television.", "links": [{"title": "Silver Jubilee of Elizabeth II", "link": "https://wikipedia.org/wiki/Silver_Jubilee_of_<PERSON>_II"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_II"}]}, {"year": "1981", "text": "The Israeli Air Force destroys Iraq's Osiraq nuclear reactor during Operation Opera.", "html": "1981 - The <a href=\"https://wikipedia.org/wiki/Israeli_Air_Force\" title=\"Israeli Air Force\">Israeli Air Force</a> destroys <a href=\"https://wikipedia.org/wiki/Iraq\" title=\"Iraq\">Iraq</a>'s <PERSON><PERSON><PERSON> <a href=\"https://wikipedia.org/wiki/Nuclear_reactor\" title=\"Nuclear reactor\">nuclear reactor</a> during <a href=\"https://wikipedia.org/wiki/Operation_Opera\" title=\"Operation Opera\">Operation Opera</a>.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Israeli_Air_Force\" title=\"Israeli Air Force\">Israeli Air Force</a> destroys <a href=\"https://wikipedia.org/wiki/Iraq\" title=\"Iraq\">Iraq</a>'s <PERSON>siraq <a href=\"https://wikipedia.org/wiki/Nuclear_reactor\" title=\"Nuclear reactor\">nuclear reactor</a> during <a href=\"https://wikipedia.org/wiki/Operation_Opera\" title=\"Operation Opera\">Operation Opera</a>.", "links": [{"title": "Israeli Air Force", "link": "https://wikipedia.org/wiki/Israeli_Air_Force"}, {"title": "Iraq", "link": "https://wikipedia.org/wiki/Iraq"}, {"title": "Nuclear reactor", "link": "https://wikipedia.org/wiki/Nuclear_reactor"}, {"title": "Operation Opera", "link": "https://wikipedia.org/wiki/Operation_Opera"}]}, {"year": "1982", "text": "<PERSON><PERSON><PERSON> opens Graceland to the public; the bathroom where <PERSON> died five years earlier is kept off-limits.", "html": "1982 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a> opens <a href=\"https://wikipedia.org/wiki/Graceland\" title=\"Graceland\">Graceland</a> to the public; the bathroom where <a href=\"https://wikipedia.org/wiki/Elvis_Presley\" title=\"Elvis Presley\"><PERSON></a> died five years earlier is kept off-limits.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a> opens <a href=\"https://wikipedia.org/wiki/Graceland\" title=\"Graceland\">Graceland</a> to the public; the bathroom where <a href=\"https://wikipedia.org/wiki/Elvis_Presley\" title=\"Elvis Presley\"><PERSON></a> died five years earlier is kept off-limits.", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}, {"title": "Graceland", "link": "https://wikipedia.org/wiki/Graceland"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1989", "text": "Surinam Airways Flight 764 crashes on approach to Paramaribo-Zanderij International Airport in Suriname because of pilot error, killing 176 of 187 aboard.", "html": "1989 - <a href=\"https://wikipedia.org/wiki/Surinam_Airways_Flight_764\" title=\"Surinam Airways Flight 764\">Surinam Airways Flight 764</a> crashes on approach to <a href=\"https://wikipedia.org/wiki/Paramaribo-Zanderij_International_Airport\" class=\"mw-redirect\" title=\"Paramaribo-Zanderij International Airport\">Paramaribo-Zanderij International Airport</a> in <a href=\"https://wikipedia.org/wiki/Suriname\" title=\"Suriname\">Suriname</a> because of pilot error, killing 176 of 187 aboard.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Surinam_Airways_Flight_764\" title=\"Surinam Airways Flight 764\">Surinam Airways Flight 764</a> crashes on approach to <a href=\"https://wikipedia.org/wiki/Paramaribo-Zanderij_International_Airport\" class=\"mw-redirect\" title=\"Paramaribo-Zanderij International Airport\">Paramaribo-Zanderij International Airport</a> in <a href=\"https://wikipedia.org/wiki/Suriname\" title=\"Suriname\">Suriname</a> because of pilot error, killing 176 of 187 aboard.", "links": [{"title": "Surinam Airways Flight 764", "link": "https://wikipedia.org/wiki/Surinam_Airways_Flight_764"}, {"title": "Paramaribo-Zanderij International Airport", "link": "https://wikipedia.org/wiki/Paramaribo-Zanderij_International_Airport"}, {"title": "Suriname", "link": "https://wikipedia.org/wiki/Suriname"}]}, {"year": "1991", "text": "Mount Pinatubo erupts, generating an ash column 7 kilometres (4.3 mi) high.", "html": "1991 - <a href=\"https://wikipedia.org/wiki/Mount_Pinatubo\" title=\"Mount Pinatubo\">Mount Pinatubo</a> <a href=\"https://wikipedia.org/wiki/1991_eruption_of_Mount_Pinatubo\" title=\"1991 eruption of Mount Pinatubo\">erupts</a>, generating an ash column 7 kilometres (4.3 mi) high.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Mount_Pinatubo\" title=\"Mount Pinatubo\">Mount Pinatubo</a> <a href=\"https://wikipedia.org/wiki/1991_eruption_of_Mount_Pinatubo\" title=\"1991 eruption of Mount Pinatubo\">erupts</a>, generating an ash column 7 kilometres (4.3 mi) high.", "links": [{"title": "Mount Pinatubo", "link": "https://wikipedia.org/wiki/Mount_Pinatubo"}, {"title": "1991 eruption of Mount Pinatubo", "link": "https://wikipedia.org/wiki/1991_eruption_of_Mount_Pinatubo"}]}, {"year": "2000", "text": "The United Nations defines the Blue Line as the border between Israel and Lebanon.", "html": "2000 - The <a href=\"https://wikipedia.org/wiki/United_Nations\" title=\"United Nations\">United Nations</a> defines the <a href=\"https://wikipedia.org/wiki/Blue_Line_(Lebanon)\" class=\"mw-redirect\" title=\"Blue Line (Lebanon)\">Blue Line</a> as the border between <a href=\"https://wikipedia.org/wiki/Israel\" title=\"Israel\">Israel</a> and <a href=\"https://wikipedia.org/wiki/Lebanon\" title=\"Lebanon\">Lebanon</a>.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/United_Nations\" title=\"United Nations\">United Nations</a> defines the <a href=\"https://wikipedia.org/wiki/Blue_Line_(Lebanon)\" class=\"mw-redirect\" title=\"Blue Line (Lebanon)\">Blue Line</a> as the border between <a href=\"https://wikipedia.org/wiki/Israel\" title=\"Israel\">Israel</a> and <a href=\"https://wikipedia.org/wiki/Lebanon\" title=\"Lebanon\">Lebanon</a>.", "links": [{"title": "United Nations", "link": "https://wikipedia.org/wiki/United_Nations"}, {"title": "Blue Line (Lebanon)", "link": "https://wikipedia.org/wiki/Blue_Line_(Lebanon)"}, {"title": "Israel", "link": "https://wikipedia.org/wiki/Israel"}, {"title": "Lebanon", "link": "https://wikipedia.org/wiki/Lebanon"}]}, {"year": "2017", "text": "A Myanmar Air Force Shaanxi Y-8 crashes into the Andaman Sea near Dawei, Myanmar, killing all 122 aboard.", "html": "2017 - A <a href=\"https://wikipedia.org/wiki/Myanmar_Air_Force\" title=\"Myanmar Air Force\">Myanmar Air Force</a> <a href=\"https://wikipedia.org/wiki/Shaanxi_Y-8\" title=\"Shaanxi Y-8\">Shaanxi Y-8</a> <a href=\"https://wikipedia.org/wiki/2017_Myanmar_Air_Force_Shaanxi_Y-8_crash\" title=\"2017 Myanmar Air Force Shaanxi Y-8 crash\">crashes</a> into the <a href=\"https://wikipedia.org/wiki/Andaman_Sea\" title=\"Andaman Sea\">Andaman Sea</a> near <a href=\"https://wikipedia.org/wiki/Dawei\" title=\"Dawei\">Dawei</a>, <a href=\"https://wikipedia.org/wiki/Myanmar\" title=\"Myanmar\">Myanmar</a>, killing all 122 aboard.", "no_year_html": "A <a href=\"https://wikipedia.org/wiki/Myanmar_Air_Force\" title=\"Myanmar Air Force\">Myanmar Air Force</a> <a href=\"https://wikipedia.org/wiki/Shaanxi_Y-8\" title=\"Shaanxi Y-8\">Shaanxi Y-8</a> <a href=\"https://wikipedia.org/wiki/2017_Myanmar_Air_Force_Shaanxi_Y-8_crash\" title=\"2017 Myanmar Air Force Shaanxi Y-8 crash\">crashes</a> into the <a href=\"https://wikipedia.org/wiki/Andaman_Sea\" title=\"Andaman Sea\">Andaman Sea</a> near <a href=\"https://wikipedia.org/wiki/Dawei\" title=\"Dawei\">Dawei</a>, <a href=\"https://wikipedia.org/wiki/Myanmar\" title=\"Myanmar\">Myanmar</a>, killing all 122 aboard.", "links": [{"title": "Myanmar Air Force", "link": "https://wikipedia.org/wiki/Myanmar_Air_Force"}, {"title": "Shaanxi Y-8", "link": "https://wikipedia.org/wiki/Shaanxi_Y-8"}, {"title": "2017 Myanmar Air Force Shaanxi Y-8 crash", "link": "https://wikipedia.org/wiki/2017_Myanmar_Air_Force_Shaanxi_Y-8_crash"}, {"title": "Andaman Sea", "link": "https://wikipedia.org/wiki/Andaman_Sea"}, {"title": "Dawei", "link": "https://wikipedia.org/wiki/Dawei"}, {"title": "Myanmar", "link": "https://wikipedia.org/wiki/Myanmar"}]}], "Births": [{"year": "1003", "text": "Emperor <PERSON><PERSON> of Western Xia (d. 1048)", "html": "1003 - <a href=\"https://wikipedia.org/wiki/Emperor_<PERSON><PERSON>_of_Western_Xia\" title=\"Emperor <PERSON><PERSON> of Western Xia\">Emperor <PERSON><PERSON> of Western Xia</a> (d. 1048)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Emperor_<PERSON><PERSON>_of_Western_Xia\" title=\"Emperor <PERSON><PERSON> of Western Xia\">Emperor <PERSON><PERSON> of Western Xia</a> (d. 1048)", "links": [{"title": "Emperor <PERSON><PERSON> of Western Xia", "link": "https://wikipedia.org/wiki/Emperor_<PERSON><PERSON>_of_Western_Xia"}]}, {"year": "1402", "text": "<PERSON><PERSON><PERSON>, Japanese noble (d. 1481)", "html": "1402 - <a href=\"https://wikipedia.org/wiki/Ichij%C5%8D_<PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese noble (d. 1481)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Ichij%C5%8D_<PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese noble (d. 1481)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Ichij%C5%8D_<PERSON>"}]}, {"year": "1422", "text": "<PERSON>, Italian condottiero (d. 1482)", "html": "1422 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian condottiero (d. 1482)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian condottiero (d. 1482)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1502", "text": "<PERSON> of Portugal (d. 1557)", "html": "1502 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Portugal\" title=\"<PERSON> III of Portugal\"><PERSON> of Portugal</a> (d. 1557)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Portugal\" title=\"<PERSON> of Portugal\"><PERSON> of Portugal</a> (d. 1557)", "links": [{"title": "<PERSON> of Portugal", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Portugal"}]}, {"year": "1529", "text": "<PERSON>, French lawyer and jurist (d. 1615)", "html": "1529 - <a href=\"https://wikipedia.org/wiki/%C3%89tien<PERSON>_<PERSON>er\" title=\"<PERSON>\"><PERSON></a>, French lawyer and jurist (d. 1615)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/%C3%89tien<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French lawyer and jurist (d. 1615)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/%C3%89tien<PERSON>_<PERSON><PERSON>er"}]}, {"year": "1561", "text": "<PERSON>, Count of Nassau-Siegen, German count and military theorist (d. 1623)", "html": "1561 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Count_of_Nassau-Siegen\" title=\"<PERSON>, Count of Nassau-Siegen\"><PERSON>, Count of Nassau-Siegen</a>, German count and military theorist (d. 1623)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Count_of_Nassau-Siegen\" title=\"<PERSON>, Count of Nassau-Siegen\"><PERSON>, Count of Nassau-Siegen</a>, German count and military theorist (d. 1623)", "links": [{"title": "<PERSON>, Count of Nassau-Siegen", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Count_of_Nassau-<PERSON>n"}]}, {"year": "1687", "text": "<PERSON><PERSON><PERSON>, Italian actor and singer (d. 1734)", "html": "1687 - <a href=\"https://wikipedia.org/wiki/Gaetano_<PERSON>\" title=\"Gaetano <PERSON>\"><PERSON><PERSON><PERSON></a>, Italian actor and singer (d. 1734)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Gae<PERSON>_<PERSON>\" title=\"Gaetano <PERSON>\"><PERSON><PERSON><PERSON></a>, Italian actor and singer (d. 1734)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Gaetano_Be<PERSON>stadt"}]}, {"year": "1702", "text": "<PERSON>, Margrave of Baden-Baden (d. 1761)", "html": "1702 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_<PERSON><PERSON>_of_Baden-Baden\" title=\"<PERSON>, Margrave of Baden-Baden\"><PERSON>, Margrave of Baden-Baden</a> (d. 1761)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_<PERSON><PERSON>_of_Baden-Baden\" title=\"<PERSON>, Margrave of Baden-Baden\"><PERSON>, Margrave of Baden-Baden</a> (d. 1761)", "links": [{"title": "<PERSON>, Margrave of Baden-Baden", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_<PERSON><PERSON>_of_Baden-Baden"}]}, {"year": "1757", "text": "<PERSON><PERSON>, Duchess of Devonshire (d. 1806)", "html": "1757 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>,_Duchess_of_Devonshire\" title=\"<PERSON><PERSON>, Duchess of Devonshire\"><PERSON><PERSON>, Duchess of Devonshire</a> (d. 1806)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>,_Duchess_of_Devonshire\" title=\"<PERSON><PERSON>, Duchess of Devonshire\"><PERSON><PERSON>, Duchess of Devonshire</a> (d. 1806)", "links": [{"title": "<PERSON><PERSON>, Duchess of Devonshire", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>,_Duchess_of_Devonshire"}]}, {"year": "1761", "text": "<PERSON> the Elder, Scottish engineer (d. 1821)", "html": "1761 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_the_Elder\" title=\"<PERSON> the Elder\"><PERSON> the Elder</a>, Scottish engineer (d. 1821)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_the_Elder\" title=\"<PERSON> the Elder\"><PERSON> the Elder</a>, Scottish engineer (d. 1821)", "links": [{"title": "<PERSON> the Elder", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_the_Elder"}]}, {"year": "1770", "text": "<PERSON>, 2nd Earl of Liverpool, English politician, Prime Minister of the United Kingdom (d. 1828)", "html": "1770 - <a href=\"https://wikipedia.org/wiki/<PERSON>,_2nd_Earl_of_Liverpool\" title=\"<PERSON>, 2nd Earl of Liverpool\"><PERSON>, 2nd Earl of Liverpool</a>, English politician, <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_the_United_Kingdom\" title=\"Prime Minister of the United Kingdom\">Prime Minister of the United Kingdom</a> (d. 1828)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>,_2nd_Earl_of_Liverpool\" title=\"<PERSON>, 2nd Earl of Liverpool\"><PERSON>, 2nd Earl of Liverpool</a>, English politician, <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_the_United_Kingdom\" title=\"Prime Minister of the United Kingdom\">Prime Minister of the United Kingdom</a> (d. 1828)", "links": [{"title": "<PERSON>, 2nd Earl of Liverpool", "link": "https://wikipedia.org/wiki/<PERSON>,_2nd_Earl_of_Liverpool"}, {"title": "Prime Minister of the United Kingdom", "link": "https://wikipedia.org/wiki/Prime_Minister_of_the_United_Kingdom"}]}, {"year": "1778", "text": "<PERSON>, English cricketer and fashion designer (d. 1840)", "html": "1778 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English cricketer and fashion designer (d. 1840)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English cricketer and fashion designer (d. 1840)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1811", "text": "<PERSON>, Scottish obstetrician (d. 1870)", "html": "1811 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish obstetrician (d. 1870)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish obstetrician (d. 1870)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1831", "text": "<PERSON>, English journalist and author (d. 1892)", "html": "1831 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English journalist and author (d. 1892)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English journalist and author (d. 1892)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1837", "text": "<PERSON><PERSON>, Austrian civil servant (d. 1903)", "html": "1837 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Austrian civil servant (d. 1903)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Austrian civil servant (d. 1903)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1840", "text": "<PERSON><PERSON> of Mexico (d. 1927)", "html": "1840 - <a href=\"https://wikipedia.org/wiki/Carlota_of_Mexico\" class=\"mw-redirect\" title=\"<PERSON><PERSON> of Mexico\"><PERSON><PERSON> of Mexico</a> (d. 1927)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Carlota_of_Mexico\" class=\"mw-redirect\" title=\"<PERSON><PERSON> of Mexico\"><PERSON><PERSON> of Mexico</a> (d. 1927)", "links": [{"title": "Carlota of Mexico", "link": "https://wikipedia.org/wiki/Carlota_of_Mexico"}]}, {"year": "1845", "text": "<PERSON>, Hungarian violinist, composer, and conductor (d. 1930)", "html": "1845 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Hungarian violinist, composer, and conductor (d. 1930)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Hungarian violinist, composer, and conductor (d. 1930)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1847", "text": "<PERSON>, American legislator from Iowa (d. 1915)", "html": "1847 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_(Iowa_Democrat)\" title=\"<PERSON> (Iowa Democrat)\"><PERSON></a>, American legislator from Iowa (d. 1915)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_(Iowa_Democrat)\" title=\"<PERSON> (Iowa Democrat)\"><PERSON></a>, American legislator from Iowa (d. 1915)", "links": [{"title": "<PERSON> (Iowa Democrat)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_Ball_(Iowa_Democrat)"}]}, {"year": "1848", "text": "<PERSON>, French painter and sculptor (d. 1903)", "html": "1848 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French painter and sculptor (d. 1903)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French painter and sculptor (d. 1903)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1851", "text": "<PERSON><PERSON>, Swedish journalist and politician (d. 1922)", "html": "1851 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Swedish journalist and politician (d. 1922)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Swedish journalist and politician (d. 1922)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1861", "text": "<PERSON><PERSON>, New Zealand photographer and suffragist (d. 1942)", "html": "1861 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, New Zealand photographer and suffragist (d. 1942)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, New Zealand photographer and suffragist (d. 1942)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>l"}]}, {"year": "1862", "text": "<PERSON>, Slovak-German physicist and academic, Nobel Prize laureate (d. 1947)", "html": "1862 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Slovak-German physicist and academic, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Physics\" title=\"Nobel Prize in Physics\">Nobel Prize</a> laureate (d. 1947)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Slovak-German physicist and academic, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Physics\" title=\"Nobel Prize in Physics\">Nobel Prize</a> laureate (d. 1947)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Nobel Prize in Physics", "link": "https://wikipedia.org/wiki/Nobel_Prize_in_Physics"}]}, {"year": "1863", "text": "<PERSON>, American baseball player and manager (d. 1952)", "html": "1863 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Ely\"><PERSON></a>, American baseball player and manager (d. 1952)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Ely\"><PERSON></a>, American baseball player and manager (d. 1952)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_Ely"}]}, {"year": "1868", "text": "<PERSON>, Scottish painter and architect (d. 1928)", "html": "1868 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish painter and architect (d. 1928)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish painter and architect (d. 1928)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "1877", "text": "<PERSON><PERSON><PERSON>, Dutch-American rower and engineer (d. 1960)", "html": "1877 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Dutch-American rower and engineer (d. 1960)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Dutch-American rower and engineer (d. 1960)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1879", "text": "<PERSON><PERSON><PERSON>, Danish anthropologist and explorer (d. 1933)", "html": "1879 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Danish anthropologist and explorer (d. 1933)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Danish anthropologist and explorer (d. 1933)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1879", "text": "<PERSON>, Dutch astronomer and academic (d. 1963)", "html": "1879 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%BBte\" title=\"<PERSON>\"><PERSON></a>, Dutch astronomer and academic (d. 1963)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%BBte\" title=\"<PERSON>\"><PERSON></a>, Dutch astronomer and academic (d. 1963)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Joan_Vo%C3%BBte"}]}, {"year": "1883", "text": "<PERSON><PERSON><PERSON><PERSON>, American archaeologist and scholar (d. 1948)", "html": "1883 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, American archaeologist and scholar (d. 1948)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, American archaeologist and scholar (d. 1948)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>yl<PERSON><PERSON>_<PERSON>"}]}, {"year": "1884", "text": "<PERSON><PERSON>, Swedish landscape architect (d. 1931)", "html": "1884 - <a href=\"https://wikipedia.org/wiki/Ester_Claesson\" title=\"Ester Claesson\"><PERSON><PERSON></a>, Swedish landscape architect (d. 1931)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Ester_Cla<PERSON>on\" title=\"Ester Claesson\"><PERSON><PERSON></a>, Swedish landscape architect (d. 1931)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1886", "text": "<PERSON>, Romanian engineer, designed the Coandă-1910 (d. 1972)", "html": "1886 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C4%83\" title=\"<PERSON>\"><PERSON></a>, Romanian engineer, designed the <a href=\"https://wikipedia.org/wiki/Coand%C4%83-1910\" title=\"Coandă-1910\">Coandă-1910</a> (d. 1972)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>%C4%83\" title=\"<PERSON>\"><PERSON></a>, Romanian engineer, designed the <a href=\"https://wikipedia.org/wiki/Coand%C4%83-1910\" title=\"Coandă-1910\">Coandă-1910</a> (d. 1972)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Henri_Coand%C4%83"}, {"title": "Coandă-1910", "link": "https://wikipedia.org/wiki/Coand%C4%83-1910"}]}, {"year": "1888", "text": "<PERSON>, American runner and educator (d. 1958)", "html": "1888 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American runner and educator (d. 1958)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American runner and educator (d. 1958)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1890", "text": "<PERSON>, American psychologist and behaviorist (d. 1958)", "html": "1890 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American psychologist and behaviorist (d. 1958)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American psychologist and behaviorist (d. 1958)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1892", "text": "<PERSON>, Canadian ice hockey player (d. 1975)", "html": "1892 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player (d. 1975)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player (d. 1975)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1893", "text": "<PERSON><PERSON>, Swedish figure skater and architect (d. 1938)", "html": "1893 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>r%C3%B6m\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Swedish figure skater and architect (d. 1938)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>%C3%B6m\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Swedish figure skater and architect (d. 1938)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Gillis_Grafstr%C3%B6m"}]}, {"year": "1894", "text": "<PERSON>, Georgian-American pilot and engineer, co-designed the Republic P-47 Thunderbolt (d. 1974)", "html": "1894 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, Georgian-American pilot and engineer, co-designed the <a href=\"https://wikipedia.org/wiki/Republic_P-47_Thunderbolt\" title=\"Republic P-47 Thunderbolt\">Republic P-47 Thunderbolt</a> (d. 1974)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, Georgian-American pilot and engineer, co-designed the <a href=\"https://wikipedia.org/wiki/Republic_P-47_Thunderbolt\" title=\"Republic P-47 Thunderbolt\">Republic P-47 Thunderbolt</a> (d. 1974)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>"}, {"title": "Republic P-47 Thunderbolt", "link": "https://wikipedia.org/wiki/Republic_P-47_Thunderbolt"}]}, {"year": "1896", "text": "<PERSON>, American lieutenant and pilot (d. 1990)", "html": "1896 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(aviator)\" title=\"<PERSON> (aviator)\"><PERSON></a>, American lieutenant and pilot (d. 1990)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(aviator)\" title=\"<PERSON> (aviator)\"><PERSON></a>, American lieutenant and pilot (d. 1990)", "links": [{"title": "<PERSON> (aviator)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(aviator)"}]}, {"year": "1896", "text": "<PERSON>, American physicist and chemist, Nobel Prize laureate (d. 1986)", "html": "1896 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American physicist and chemist, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Chemistry\" title=\"Nobel Prize in Chemistry\">Nobel Prize</a> laureate (d. 1986)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American physicist and chemist, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Chemistry\" title=\"Nobel Prize in Chemistry\">Nobel Prize</a> laureate (d. 1986)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "Nobel Prize in Chemistry", "link": "https://wikipedia.org/wiki/Nobel_Prize_in_Chemistry"}]}, {"year": "1896", "text": "<PERSON><PERSON><PERSON>, Hungarian soldier and politician, 44th Prime Minister of Hungary (d. 1958)", "html": "1896 - <a href=\"https://wikipedia.org/wiki/I<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Hungarian soldier and politician, 44th <a href=\"https://wikipedia.org/wiki/List_of_Prime_Ministers_of_Hungary\" class=\"mw-redirect\" title=\"List of Prime Ministers of Hungary\">Prime Minister of Hungary</a> (d. 1958)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Hungarian soldier and politician, 44th <a href=\"https://wikipedia.org/wiki/List_of_Prime_Ministers_of_Hungary\" class=\"mw-redirect\" title=\"List of Prime Ministers of Hungary\">Prime Minister of Hungary</a> (d. 1958)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/I<PERSON><PERSON>_Nagy"}, {"title": "List of Prime Ministers of Hungary", "link": "https://wikipedia.org/wiki/List_of_Prime_Ministers_of_Hungary"}]}, {"year": "1897", "text": "<PERSON>, Hungarian-American conductor and composer (d. 1970)", "html": "1897 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Hungarian-American conductor and composer (d. 1970)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Hungarian-American conductor and composer (d. 1970)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1899", "text": "<PERSON>, Anglo-Irish author and critic (d. 1973)", "html": "1899 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Anglo-Irish author and critic (d. 1973)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Anglo-Irish author and critic (d. 1973)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1902", "text": "<PERSON>, French composer (d. 1971)", "html": "1902 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French composer (d. 1971)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French composer (d. 1971)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1902", "text": "<PERSON>, American banker, author, and academic (d. 2000)", "html": "1902 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American banker, author, and academic (d. 2000)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American banker, author, and academic (d. 2000)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1905", "text": "<PERSON>, American world heavyweight boxing champion (d. 1974)", "html": "1905 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American world heavyweight boxing champion (d. 1974)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American world heavyweight boxing champion (d. 1974)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1906", "text": "<PERSON>, American saxophonist and bandleader (d. 1963)", "html": "1906 - <a href=\"https://wikipedia.org/wiki/Glen_Gray\" title=\"Glen Gray\"><PERSON></a>, American saxophonist and bandleader (d. 1963)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Glen_Gray\" title=\"Glen Gray\"><PERSON></a>, American saxophonist and bandleader (d. 1963)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Glen_Gray"}]}, {"year": "1907", "text": "<PERSON><PERSON><PERSON>, Count of Wisborg (d. 2002)", "html": "1907 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Count of Wisborg (d. 2002)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Count of Wisborg (d. 2002)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1909", "text": "<PERSON>, American anesthesiologist and pediatrician, developed the <PERSON><PERSON>gar test (d. 1974)", "html": "1909 - <a href=\"https://wikipedia.org/wiki/Virginia_Apgar\" title=\"Virginia Apgar\">Virginia Apgar</a>, American anesthesiologist and pediatrician, developed the <a href=\"https://wikipedia.org/wiki/Apgar_test\" class=\"mw-redirect\" title=\"Apgar test\">Apgar test</a> (d. 1974)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Virginia_Apgar\" title=\"Virginia Apgar\">Virginia Apgar</a>, American anesthesiologist and pediatrician, developed the <a href=\"https://wikipedia.org/wiki/Apgar_test\" class=\"mw-redirect\" title=\"Apgar test\">Apgar test</a> (d. 1974)", "links": [{"title": "Virginia Apgar", "link": "https://wikipedia.org/wiki/Virginia_Apgar"}, {"title": "Apgar test", "link": "https://wikipedia.org/wiki/Apgar_test"}]}, {"year": "1909", "text": "<PERSON>, American lawyer, and politician (d. 2005)", "html": "1909 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer, and politician (d. 2005)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer, and politician (d. 2005)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "1909", "text": "<PERSON>, English-American actress (d. 1994)", "html": "1909 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English-American actress (d. 1994)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English-American actress (d. 1994)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1910", "text": "<PERSON>, American actor and producer (d. 2014)", "html": "1910 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(producer)\" title=\"<PERSON> (producer)\"><PERSON></a>, American actor and producer (d. 2014)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(producer)\" title=\"<PERSON> (producer)\"><PERSON></a>, American actor and producer (d. 2014)", "links": [{"title": "<PERSON> (producer)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(producer)"}]}, {"year": "1910", "text": "<PERSON><PERSON>, German actor and screenwriter (d. 1995)", "html": "1910 - <a href=\"https://wikipedia.org/wiki/Til_<PERSON><PERSON>\" title=\"T<PERSON> <PERSON><PERSON>\"><PERSON><PERSON></a>, German actor and screenwriter (d. 1995)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/T<PERSON>_<PERSON><PERSON>\" title=\"T<PERSON> <PERSON><PERSON>\"><PERSON><PERSON></a>, German actor and screenwriter (d. 1995)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Til_<PERSON><PERSON>"}]}, {"year": "1910", "text": "<PERSON>, American football player and coach (d. 1989)", "html": "1910 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player and coach (d. 1989)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player and coach (d. 1989)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1910", "text": "<PERSON>, American mountaineer, photographer, and cartographer (d. 2007)", "html": "1910 - <a href=\"https://wikipedia.org/wiki/Bradford_Washburn\" title=\"Bradford Washburn\"><PERSON></a>, American mountaineer, photographer, and cartographer (d. 2007)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Bradford_Washburn\" title=\"Bradford Washburn\"><PERSON></a>, American mountaineer, photographer, and cartographer (d. 2007)", "links": [{"title": "Bradford Washburn", "link": "https://wikipedia.org/wiki/Bradford_Washburn"}]}, {"year": "1910", "text": "<PERSON>, American photographer (d. 1990)", "html": "1910 - <a href=\"https://wikipedia.org/wiki/Marion_<PERSON>_Wol<PERSON>\" title=\"Marion Post Wolcott\"><PERSON></a>, American photographer (d. 1990)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Marion_Post_Wolcott\" title=\"Marion Post Wolcott\"><PERSON></a>, American photographer (d. 1990)", "links": [{"title": "Marion <PERSON> W<PERSON>cott", "link": "https://wikipedia.org/wiki/Marion_Post_Wolcott"}]}, {"year": "1910", "text": "<PERSON><PERSON>, Australian cattle dog, second-oldest recorded dog (d. 1939)", "html": "1910 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_(long-lived_dog)\" title=\"<PERSON><PERSON> (long-lived dog)\"><PERSON><PERSON></a>, <a href=\"https://wikipedia.org/wiki/Australian_cattle_dog\" class=\"mw-redirect\" title=\"Australian cattle dog\">Australian cattle dog</a>, <a href=\"https://wikipedia.org/wiki/List_of_longest_living_dogs\" class=\"mw-redirect\" title=\"List of longest living dogs\">second-oldest recorded dog</a> (d. 1939)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_(long-lived_dog)\" title=\"<PERSON><PERSON> (long-lived dog)\"><PERSON><PERSON></a>, <a href=\"https://wikipedia.org/wiki/Australian_cattle_dog\" class=\"mw-redirect\" title=\"Australian cattle dog\">Australian cattle dog</a>, <a href=\"https://wikipedia.org/wiki/List_of_longest_living_dogs\" class=\"mw-redirect\" title=\"List of longest living dogs\">second-oldest recorded dog</a> (d. 1939)", "links": [{"title": "<PERSON><PERSON> (long-lived dog)", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_(long-lived_dog)"}, {"title": "Australian cattle dog", "link": "https://wikipedia.org/wiki/Australian_cattle_dog"}, {"title": "List of longest living dogs", "link": "https://wikipedia.org/wiki/List_of_longest_living_dogs"}]}, {"year": "1911", "text": "<PERSON>, American engineer and designer, designed the Wienermobile (d. 1995)", "html": "1911 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American engineer and designer, designed the <a href=\"https://wikipedia.org/wiki/Wienermobile\" title=\"Wienermobile\">Wienermobile</a> (d. 1995)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American engineer and designer, designed the <a href=\"https://wikipedia.org/wiki/Wienermobile\" title=\"Wienermobile\">Wienermobile</a> (d. 1995)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Wienermobile", "link": "https://wikipedia.org/wiki/Wienermobile"}]}, {"year": "1912", "text": "<PERSON>, French bandleader (d. 1986)", "html": "1912 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A9lian\" title=\"<PERSON>\"><PERSON></a>, French bandleader (d. 1986)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A9lian\" title=\"<PERSON>\"><PERSON></a>, French bandleader (d. 1986)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Jacques_H%C3%A9lian"}]}, {"year": "1917", "text": "<PERSON><PERSON><PERSON>, American poet (d. 2000)", "html": "1917 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American poet (d. 2000)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American poet (d. 2000)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1917", "text": "<PERSON>, American singer, actor, and producer (d. 1995)", "html": "1917 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer, actor, and producer (d. 1995)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer, actor, and producer (d. 1995)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1920", "text": "<PERSON>, French mechanic and politician (d. 1997)", "html": "1920 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French mechanic and politician (d. 1997)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French mechanic and politician (d. 1997)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1923", "text": "<PERSON>, Canadian lawyer and judge (d. 2000)", "html": "1923 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%AAnes\" title=\"<PERSON>\"><PERSON></a>, Canadian lawyer and judge (d. 2000)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%AAnes\" title=\"<PERSON>\"><PERSON></a>, Canadian lawyer and judge (d. 2000)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Jules_Desch%C3%AAnes"}]}, {"year": "1925", "text": "<PERSON><PERSON>, Argentine publisher and executive (d. 2017)", "html": "1925 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Argentine publisher and executive (d. 2017)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Argentine publisher and executive (d. 2017)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1926", "text": "<PERSON><PERSON><PERSON><PERSON>, Canadian lawyer and politician (d. 2020)", "html": "1926 - <a href=\"https://wikipedia.org/wiki/<PERSON>-<PERSON>%C3%ABl_Tremblay\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Canadian lawyer and politician (d. 2020)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>-<PERSON>%C3%ABl_Tremblay\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Canadian lawyer and politician (d. 2020)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>-No%C3%ABl_Tremblay"}]}, {"year": "1927", "text": "<PERSON>, American conductor and educator (d. 2014)", "html": "1927 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American conductor and educator (d. 2014)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American conductor and educator (d. 2014)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1927", "text": "<PERSON>, American tropical fish expert, publisher of pet books, and entrepreneur (d. 2017)", "html": "1927 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American <a href=\"https://wikipedia.org/wiki/Tropical_fish\" title=\"Tropical fish\">tropical fish</a> expert, publisher of pet books, and entrepreneur (d. 2017)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American <a href=\"https://wikipedia.org/wiki/Tropical_fish\" title=\"Tropical fish\">tropical fish</a> expert, publisher of pet books, and entrepreneur (d. 2017)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>"}, {"title": "Tropical fish", "link": "https://wikipedia.org/wiki/Tropical_fish"}]}, {"year": "1928", "text": "<PERSON>, American director, producer, and screenwriter", "html": "1928 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American director, producer, and screenwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American director, producer, and screenwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1929", "text": "<PERSON>, Canadian lawyer and politician, 17th Prime Minister of Canada (d. 2020)", "html": "1929 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian lawyer and politician, 17th <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Canada\" title=\"Prime Minister of Canada\">Prime Minister of Canada</a> (d. 2020)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian lawyer and politician, 17th <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Canada\" title=\"Prime Minister of Canada\">Prime Minister of Canada</a> (d. 2020)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Prime Minister of Canada", "link": "https://wikipedia.org/wiki/Prime_Minister_of_Canada"}]}, {"year": "1931", "text": "<PERSON>, English actress and author", "html": "1931 - <a href=\"https://wikipedia.org/wiki/Virginia_McKenna\" title=\"<PERSON>\"><PERSON></a>, English actress and author", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_McKenna\" title=\"<PERSON> McKenna\"><PERSON></a>, English actress and author", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Virginia_McKenna"}]}, {"year": "1932", "text": "<PERSON>, Norwegian historian, academic, and politician (d. 2013)", "html": "1932 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Norwegian historian, academic, and politician (d. 2013)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Norwegian historian, academic, and politician (d. 2013)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON><PERSON>"}]}, {"year": "1935", "text": "<PERSON>, American novelist, playwright, short story writer, and essayist (d. 2012)", "html": "1935 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American novelist, playwright, short story writer, and essayist (d. 2012)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American novelist, playwright, short story writer, and essayist (d. 2012)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1936", "text": "<PERSON>, American author and boxing historian (d. 2012)", "html": "1936 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author and boxing historian (d. 2012)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Sugar\"><PERSON></a>, American author and boxing historian (d. 2012)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1938", "text": "<PERSON>, Scottish international footballer and manager (d. 2021)", "html": "1938 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"Ian St John\"><PERSON></a>, Scottish international footballer and manager (d. 2021)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"Ian St John\"><PERSON></a>, Scottish international footballer and manager (d. 2021)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Ian_<PERSON>_John"}]}, {"year": "1939", "text": "<PERSON><PERSON>, Russian-Canadian cellist, conductor and educator (d. 2013)", "html": "1939 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Russian-Canadian cellist, conductor and educator (d. 2013)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Russian-Canadian cellist, conductor and educator (d. 2013)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1940", "text": "<PERSON>, Welsh singer and actor", "html": "1940 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(singer)\" title=\"<PERSON> (singer)\"><PERSON></a>, Welsh singer and actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(singer)\" title=\"<PERSON> (singer)\"><PERSON></a>, Welsh singer and actor", "links": [{"title": "<PERSON> (singer)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(singer)"}]}, {"year": "1940", "text": "<PERSON>, English actor (d. 2021)", "html": "1940 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actor (d. 2021)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Pickup\"><PERSON></a>, English actor (d. 2021)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1941", "text": "<PERSON> <PERSON>, British party planner, writer and socialite (d. 2020)", "html": "1941 - <a href=\"https://wikipedia.org/wiki/Lady_<PERSON>_<PERSON>\" title=\"Lady <PERSON>\">Lady <PERSON></a>, British party planner, writer and socialite (d. 2020)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Lady_<PERSON>_<PERSON>\" title=\"Lady <PERSON>\">Lady <PERSON></a>, British party planner, writer and socialite (d. 2020)", "links": [{"title": "Lady <PERSON>", "link": "https://wikipedia.org/wiki/Lady_<PERSON>_<PERSON>"}]}, {"year": "1943", "text": "<PERSON>, American poet, writer and activist (d. 2024)", "html": "1943 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American poet, writer and activist (d. 2024)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American poet, writer and activist (d. 2024)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1943", "text": "\"Superstar\" <PERSON>, American wrestler (d. 2023)", "html": "1943 - <a href=\"https://wikipedia.org/wiki/%22Superstar%22_<PERSON>_<PERSON>\" title='\"Superstar\" <PERSON>'>\"Superstar\" <PERSON></a>, American wrestler (d. 2023)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/%22Superstar%22_<PERSON>_<PERSON>\" title='\"Superstar\" <PERSON>'>\"Superstar\" <PERSON></a>, American wrestler (d. 2023)", "links": [{"title": "\"Superstar\" <PERSON>", "link": "https://wikipedia.org/wiki/%22Superstar%22_<PERSON>_<PERSON>"}]}, {"year": "1944", "text": "<PERSON>, American guitarist and singer (d. 1973)", "html": "1944 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American guitarist and singer (d. 1973)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American guitarist and singer (d. 1973)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1945", "text": "<PERSON>, Austrian lawyer and politician, 26th Chancellor of Austria", "html": "1945 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%BCssel\" title=\"<PERSON>\"><PERSON></a>, Austrian lawyer and politician, 26th <a href=\"https://wikipedia.org/wiki/Chancellor_of_Austria\" title=\"Chancellor of Austria\">Chancellor of Austria</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%BCssel\" title=\"<PERSON>\"><PERSON></a>, Austrian lawyer and politician, 26th <a href=\"https://wikipedia.org/wiki/Chancellor_of_Austria\" title=\"Chancellor of Austria\">Chancellor of Austria</a>", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>h%C3%BCssel"}, {"title": "Chancellor of Austria", "link": "https://wikipedia.org/wiki/Chancellor_of_Austria"}]}, {"year": "1946", "text": "<PERSON><PERSON><PERSON><PERSON>, Polish musician (d. 1979)", "html": "1946 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Polish musician (d. 1979)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Polish musician (d. 1979)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1947", "text": "<PERSON><PERSON><PERSON>, American baseball player (d. 1979)", "html": "1947 - <a href=\"https://wikipedia.org/wiki/T<PERSON><PERSON>_<PERSON>\" title=\"T<PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American baseball player (d. 1979)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/T<PERSON><PERSON>_<PERSON>\" title=\"T<PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American baseball player (d. 1979)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/T<PERSON><PERSON>_<PERSON><PERSON>"}]}, {"year": "1952", "text": "<PERSON>, Irish-American actor", "html": "1952 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Irish-American actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Irish-American actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1952", "text": "<PERSON><PERSON>, Turkish-American novelist, screenwriter, and academic, Nobel Prize laureate", "html": "1952 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Turkish-American novelist, screenwriter, and academic, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Literature\" title=\"Nobel Prize in Literature\">Nobel Prize</a> laureate", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Turkish-American novelist, screenwriter, and academic, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Literature\" title=\"Nobel Prize in Literature\">Nobel Prize</a> laureate", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}, {"title": "Nobel Prize in Literature", "link": "https://wikipedia.org/wiki/Nobel_Prize_in_Literature"}]}, {"year": "1954", "text": "<PERSON>, American novelist and poet", "html": "1954 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American novelist and poet", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American novelist and poet", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1957", "text": "<PERSON>, Dominican singer, composer, and record producer.", "html": "1957 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dominican singer, composer, and record producer.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dominican singer, composer, and record producer.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1958", "text": "<PERSON>, American singer-songwriter, multi-instrumentalist, producer, and actor (d. 2016)", "html": "1958 - <a href=\"https://wikipedia.org/wiki/<PERSON>_(musician)\" title=\"<PERSON> (musician)\"><PERSON></a>, American singer-songwriter, multi-instrumentalist, producer, and actor (d. 2016)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(musician)\" title=\"<PERSON> (musician)\"><PERSON></a>, American singer-songwriter, multi-instrumentalist, producer, and actor (d. 2016)", "links": [{"title": "<PERSON> (musician)", "link": "https://wikipedia.org/wiki/<PERSON>_(musician)"}]}, {"year": "1959", "text": "<PERSON>, 48th Vice President of the United States, 50th Governor of Indiana", "html": "1959 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, 48th <a href=\"https://wikipedia.org/wiki/Vice_President_of_the_United_States\" title=\"Vice President of the United States\">Vice President of the United States</a>, 50th <a href=\"https://wikipedia.org/wiki/Governor_of_Indiana\" title=\"Governor of Indiana\">Governor of Indiana</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, 48th <a href=\"https://wikipedia.org/wiki/Vice_President_of_the_United_States\" title=\"Vice President of the United States\">Vice President of the United States</a>, 50th <a href=\"https://wikipedia.org/wiki/Governor_of_Indiana\" title=\"Governor of Indiana\">Governor of Indiana</a>", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Vice President of the United States", "link": "https://wikipedia.org/wiki/Vice_President_of_the_United_States"}, {"title": "Governor of Indiana", "link": "https://wikipedia.org/wiki/Governor_of_Indiana"}]}, {"year": "1960", "text": "<PERSON>, American screenwriter and producer", "html": "1960 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American screenwriter and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American screenwriter and producer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1962", "text": "<PERSON>, American actor (d. 2023)", "html": "1962 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor (d. 2023)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor (d. 2023)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1965", "text": "<PERSON>, English painter and art collector", "html": "1965 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English painter and art collector", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English painter and art collector", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1965", "text": "<PERSON>, American wrestler", "html": "1965 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American wrestler", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American wrestler", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1967", "text": "<PERSON>, American musician", "html": "1967 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American musician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American musician", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1970", "text": "<PERSON><PERSON><PERSON>, Brazilian footballer", "html": "1970 - <a href=\"https://wikipedia.org/wiki/Cafu\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Brazilian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Cafu\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Brazilian footballer", "links": [{"title": "Cafu", "link": "https://wikipedia.org/wiki/Cafu"}]}, {"year": "1974", "text": "<PERSON>, English adventurer, author, and television host", "html": "1974 - <a href=\"https://wikipedia.org/wiki/Bear_Grylls\" title=\"Bear Grylls\"><PERSON></a>, English adventurer, author, and television host", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Bear_G<PERSON>ls\" title=\"Bear Grylls\"><PERSON></a>, English adventurer, author, and television host", "links": [{"title": "<PERSON>ls", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>rylls"}]}, {"year": "1975", "text": "<PERSON>, American basketball player", "html": "1975 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1978", "text": "<PERSON>, American actor, comedian, and screenwriter", "html": "1978 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor, comedian, and screenwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor, comedian, and screenwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1981", "text": "<PERSON>, Russian tennis player", "html": "1981 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian tennis player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian tennis player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1988", "text": "<PERSON>, Canadian actor and musician", "html": "1988 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian actor and musician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian actor and musician", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1990", "text": "<PERSON><PERSON>, Australian rapper, singer, songwriter, and model", "html": "1990 - <a href=\"https://wikipedia.org/wiki/Iggy_Azalea\" title=\"Iggy Azalea\"><PERSON><PERSON></a>, Australian rapper, singer, songwriter, and model", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Iggy_Azalea\" title=\"Iggy Azalea\"><PERSON><PERSON></a>, Australian rapper, singer, songwriter, and model", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Iggy_Azalea"}]}, {"year": "1991", "text": "<PERSON><PERSON>, American rapper, singer, and songwriter", "html": "1991 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_Wap\" title=\"<PERSON><PERSON> Wap\"><PERSON><PERSON></a>, American rapper, singer, and songwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_Wap\" title=\"<PERSON><PERSON> Wap\"><PERSON><PERSON></a>, American rapper, singer, and songwriter", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_Wap"}]}, {"year": "1991", "text": "<PERSON>, American model and actress", "html": "1991 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American model and actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American model and actress", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1992", "text": "<PERSON>, Filipino-American basketball player", "html": "1992 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Filipino-American basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Filipino-American basketball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1993", "text": "<PERSON>, English singer-songwriter", "html": "1993 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English singer-songwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English singer-songwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1993", "text": "<PERSON><PERSON><PERSON>, American rapper", "html": "1993 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American rapper", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON> <PERSON>\"><PERSON><PERSON><PERSON></a>, American rapper", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1996", "text": "<PERSON>, American football player", "html": "1996 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1997", "text": "<PERSON>, American football player", "html": "1997 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(American_football)\" title=\"<PERSON> (American football)\"><PERSON></a>, American football player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(American_football)\" title=\"<PERSON> (American football)\"><PERSON></a>, American football player", "links": [{"title": "<PERSON> (American football)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(American_football)"}]}], "Deaths": [{"year": "555", "text": "<PERSON><PERSON><PERSON>, first pope of the Byzantine Papacy (b. 500)", "html": "555 - <a href=\"https://wikipedia.org/wiki/Pope_Vigilius\" title=\"Pope Vigilius\">Vigilius</a>, first <a href=\"https://wikipedia.org/wiki/Pope\" title=\"Pope\">pope</a> of the <a href=\"https://wikipedia.org/wiki/Byzantine_Papacy\" title=\"Byzantine Papacy\">Byzantine Papacy</a> (b. 500)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Pope_Vigilius\" title=\"Pope Vigilius\">Vigilius</a>, first <a href=\"https://wikipedia.org/wiki/Pope\" title=\"Pope\">pope</a> of the <a href=\"https://wikipedia.org/wiki/Byzantine_Papacy\" title=\"Byzantine Papacy\">Byzantine Papacy</a> (b. 500)", "links": [{"title": "<PERSON> <PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_Vigilius"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Pope"}, {"title": "Byzantine Papacy", "link": "https://wikipedia.org/wiki/Byzantine_Papacy"}]}, {"year": "862", "text": "<PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON> caliph (b. 837)", "html": "862 - <a href=\"https://wikipedia.org/wiki/Al-Muntasir\" title=\"Al-Muntasir\"><PERSON><PERSON><PERSON></a>, <PERSON><PERSON> caliph (b. 837)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Al-Muntasir\" title=\"Al-Muntasir\">Al-Munta<PERSON></a>, <PERSON><PERSON> caliph (b. 837)", "links": [{"title": "Al-<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>-<PERSON>"}]}, {"year": "929", "text": "<PERSON><PERSON><PERSON><PERSON><PERSON>, Countess of Flanders (b. 877)", "html": "929 - <a href=\"https://wikipedia.org/wiki/%C3%86<PERSON><PERSON><PERSON><PERSON>,_Countess_of_Flanders\" title=\"<PERSON><PERSON><PERSON><PERSON><PERSON>, Countess of Flanders\"><PERSON><PERSON><PERSON><PERSON><PERSON>, Countess of Flanders</a> (b. 877)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/%C3%86<PERSON><PERSON><PERSON><PERSON>,_Countess_of_Flanders\" title=\"<PERSON><PERSON><PERSON><PERSON><PERSON>, Countess of Flanders\"><PERSON><PERSON><PERSON><PERSON><PERSON>, Countess of Flanders</a> (b. 877)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON><PERSON>, Countess of Flanders", "link": "https://wikipedia.org/wiki/%C3%86lfthryth,_Countess_of_Flanders"}]}, {"year": "940", "text": "<PERSON><PERSON>, heir apparent of <PERSON><PERSON><PERSON> (b. 925)", "html": "940 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, <a href=\"https://wikipedia.org/wiki/Heir_apparent\" title=\"Heir apparent\">heir apparent</a> of <a href=\"https://wikipedia.org/wiki/Wuyue\" title=\"<PERSON>yu<PERSON>\"><PERSON><PERSON><PERSON></a> (b. 925)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, <a href=\"https://wikipedia.org/wiki/Heir_apparent\" title=\"Heir apparent\">heir apparent</a> of <a href=\"https://wikipedia.org/wiki/<PERSON>yue\" title=\"<PERSON>yu<PERSON>\"><PERSON><PERSON><PERSON></a> (b. 925)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}, {"title": "Heir apparent", "link": "https://wikipedia.org/wiki/Heir_apparent"}, {"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>yue"}]}, {"year": "951", "text": "<PERSON>, Chinese chancellor (b. 876)", "html": "951 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Wenji\"><PERSON></a>, Chinese chancellor (b. 876)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Wenji\"><PERSON></a>, Chinese chancellor (b. 876)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>ji"}]}, {"year": "1329", "text": "<PERSON>, Scottish king (b. 1274)", "html": "1329 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON> the <PERSON>\"><PERSON> the <PERSON></a>, Scottish king (b. 1274)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON> the <PERSON>\"><PERSON> the <PERSON></a>, Scottish king (b. 1274)", "links": [{"title": "<PERSON> the <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1337", "text": "<PERSON>, Count of Hainaut (b. 1286)", "html": "1337 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Count_of_Hainaut\" title=\"<PERSON>, Count of Hainaut\"><PERSON>, Count of Hainaut</a> (b. 1286)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Count_of_Hainaut\" title=\"<PERSON>, Count of Hainaut\"><PERSON>, Count of Hainaut</a> (b. 1286)", "links": [{"title": "<PERSON>, Count of Hainaut", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Count_of_Hai<PERSON>ut"}]}, {"year": "1341", "text": "<PERSON><PERSON><PERSON><PERSON>, Egyptian sultan (b. 1285)", "html": "1341 - <a href=\"https://wikipedia.org/wiki/<PERSON>-<PERSON><PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Egyptian sultan (b. 1285)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>-<PERSON><PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Egyptian sultan (b. 1285)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>-<PERSON><PERSON>_<PERSON>"}]}, {"year": "1358", "text": "<PERSON><PERSON><PERSON>, Japanese shōgun (b. 1305)", "html": "1358 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"Ashikaga Takauji\"><PERSON><PERSON><PERSON></a>, Japanese shōgun (b. 1305)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>ji\" title=\"Ash<PERSON><PERSON> Takauji\"><PERSON><PERSON><PERSON></a>, Japanese shōgun (b. 1305)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Ashika<PERSON>_<PERSON>ji"}]}, {"year": "1394", "text": "<PERSON> of Bohemia, English queen (b. 1366)", "html": "1394 - <a href=\"https://wikipedia.org/wiki/Anne_of_Bohemia\" title=\"<PERSON> of Bohemia\"><PERSON> of Bohemia</a>, English queen (b. 1366)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Anne_<PERSON>_Bohemia\" title=\"<PERSON> of Bohemia\"><PERSON> of Bohemia</a>, English queen (b. 1366)", "links": [{"title": "<PERSON> of Bohemia", "link": "https://wikipedia.org/wiki/Anne_of_Bohemia"}]}, {"year": "1492", "text": "<PERSON>, Grand Duke of Lithuania from 1440 and King of Poland from 1447 (b. 1427)", "html": "1492 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Grand Duke of Lithuania from 1440 and King of Poland from 1447 (b. 1427)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Grand Duke of Lithuania from 1440 and King of Poland from 1447 (b. 1427)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1594", "text": "<PERSON>, physician of Queen <PERSON> (b. 1525)", "html": "1594 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%B3<PERSON><PERSON>_(physician)\" class=\"mw-redirect\" title=\"<PERSON> (physician)\"><PERSON></a>, physician of <PERSON> <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> I\"><PERSON></a> (b. 1525)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%B3<PERSON><PERSON>_(physician)\" class=\"mw-redirect\" title=\"<PERSON> (physician)\"><PERSON></a>, physician of <PERSON> <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> I\"><PERSON></a> (b. 1525)", "links": [{"title": "<PERSON> (physician)", "link": "https://wikipedia.org/wiki/Rodrigo_L%C3%B3<PERSON><PERSON>_(physician)"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1618", "text": "<PERSON>, 3rd Baron <PERSON>, English politician, Colonial Governor of Virginia (b. 1577)", "html": "1618 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_3rd_Baron_<PERSON>_<PERSON>\" title=\"<PERSON>, 3rd Baron <PERSON>\"><PERSON>, 3rd Baron <PERSON></a>, English politician, <a href=\"https://wikipedia.org/wiki/List_of_colonial_governors_of_Virginia\" title=\"List of colonial governors of Virginia\">Colonial Governor of Virginia</a> (b. 1577)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>,_3rd_Baron_<PERSON>_<PERSON>\" title=\"<PERSON>, 3rd Baron <PERSON>\"><PERSON>, 3rd Baron <PERSON></a>, English politician, <a href=\"https://wikipedia.org/wiki/List_of_colonial_governors_of_Virginia\" title=\"List of colonial governors of Virginia\">Colonial Governor of Virginia</a> (b. 1577)", "links": [{"title": "<PERSON>, 3rd Baron <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_3rd_Baron_<PERSON>_<PERSON>_<PERSON>"}, {"title": "List of colonial governors of Virginia", "link": "https://wikipedia.org/wiki/List_of_colonial_governors_of_Virginia"}]}, {"year": "1660", "text": "<PERSON> <PERSON>, Prince of Transylvania (b. 1621)", "html": "1660 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_R%C3%A1k%C3%B3czi\" title=\"George II Rák<PERSON>i\"><PERSON></a>, Prince of Transylvania (b. 1621)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>%C3%A1k%C3%B3cz<PERSON>\" title=\"George II Rákóczi\"><PERSON></a>, Prince of Transylvania (b. 1621)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/George_II_R%C3%A1k%C3%B3czi"}]}, {"year": "1711", "text": "<PERSON>, Irish scholar and theologian (b. 1641)", "html": "1711 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Irish scholar and theologian (b. 1641)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Irish scholar and theologian (b. 1641)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1740", "text": "<PERSON>, Moroccan-American colonial and politician, Lieutenant Governor of Virginia (b. 1676)", "html": "1740 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Moroccan-American colonial and politician, <a href=\"https://wikipedia.org/wiki/List_of_colonial_governors_of_Virginia\" title=\"List of colonial governors of Virginia\">Lieutenant Governor of Virginia</a> (b. 1676)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Moroccan-American colonial and politician, <a href=\"https://wikipedia.org/wiki/List_of_colonial_governors_of_Virginia\" title=\"List of colonial governors of Virginia\">Lieutenant Governor of Virginia</a> (b. 1676)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Alexander_Spotswood"}, {"title": "List of colonial governors of Virginia", "link": "https://wikipedia.org/wiki/List_of_colonial_governors_of_Virginia"}]}, {"year": "1779", "text": "<PERSON>, English bishop and critic (b. 1698)", "html": "1779 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English bishop and critic (b. 1698)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English bishop and critic (b. 1698)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1792", "text": "<PERSON>, American general and surveyor (b. 1738)", "html": "1792 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American general and surveyor (b. 1738)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American general and surveyor (b. 1738)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1810", "text": "<PERSON>, Italian engraver and etcher (b. 1765)", "html": "1810 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian engraver and etcher (b. 1765)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian engraver and etcher (b. 1765)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1826", "text": "<PERSON>, German optician, physicist, and astronomer (b. 1787)", "html": "1826 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German optician, physicist, and astronomer (b. 1787)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German optician, physicist, and astronomer (b. 1787)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1840", "text": "<PERSON> of Prussia (b. 1770)", "html": "1840 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_of_Prussia\" title=\"<PERSON> of Prussia\"><PERSON> of Prussia</a> (b. 1770)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_of_Prussia\" title=\"<PERSON> of Prussia\"><PERSON> of Prussia</a> (b. 1770)", "links": [{"title": "<PERSON> of Prussia", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>_Prussia"}]}, {"year": "1843", "text": "<PERSON>, German lyric poet and author (b. 1770)", "html": "1843 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%B6<PERSON><PERSON>\" title=\"<PERSON>\"><PERSON></a>, German lyric poet and author (b. 1770)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%B6<PERSON><PERSON>\" title=\"<PERSON>\"><PERSON></a>, German lyric poet and author (b. 1770)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Friedrich_H%C3%B6<PERSON><PERSON>"}]}, {"year": "1853", "text": "<PERSON><PERSON>, Canadian missionary and bishop (b. 1787)", "html": "1853 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Canadian missionary and bishop (b. 1787)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Canadian missionary and bishop (b. 1787)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1854", "text": "<PERSON>, French admiral (b. 1792)", "html": "1854 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French admiral (b. 1792)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French admiral (b. 1792)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1859", "text": "<PERSON>, English painter (b. 1783)", "html": "1859 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(artist)\" title=\"<PERSON> (artist)\"><PERSON></a>, English painter (b. 1783)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(artist)\" title=\"<PERSON> (artist)\"><PERSON></a>, English painter (b. 1783)", "links": [{"title": "<PERSON> (artist)", "link": "https://wikipedia.org/wiki/<PERSON>_(artist)"}]}, {"year": "1861", "text": "<PERSON>, Anglo-Irish priest and author (b. 1777)", "html": "1861 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>t%C3%AB\" title=\"<PERSON>\"><PERSON></a>, Anglo-Irish priest and author (b. 1777)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%AB\" title=\"<PERSON>\"><PERSON></a>, Anglo-Irish priest and author (b. 1777)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Patrick_Bront%C3%AB"}]}, {"year": "1863", "text": "<PERSON>, Latin American liberator (b. 1790)", "html": "1863 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>ab%C3%A9\" title=\"<PERSON>\"><PERSON></a>, Latin American liberator (b. 1790)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>ab%C3%A9\" title=\"<PERSON>\"><PERSON></a>, Latin American liberator (b. 1790)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_Bernab%C3%A9"}]}, {"year": "1866", "text": "Chief <PERSON>, American tribal chief (b. 1780)", "html": "1866 - <a href=\"https://wikipedia.org/wiki/Chief_Seattle\" title=\"Chief Seattle\">Chief Seattle</a>, American tribal chief (b. 1780)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Chief_Seattle\" title=\"Chief Seattle\">Chief <PERSON></a>, American tribal chief (b. 1780)", "links": [{"title": "Chief <PERSON>", "link": "https://wikipedia.org/wiki/Chief_<PERSON>"}]}, {"year": "1879", "text": "<PERSON>, English dermatologist and academic (b. 1836)", "html": "1879 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English dermatologist and academic (b. 1836)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English dermatologist and academic (b. 1836)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "1896", "text": "<PERSON><PERSON><PERSON>, Greek composer (b. 1829)", "html": "1896 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Greek composer (b. 1829)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Greek composer (b. 1829)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1911", "text": "<PERSON>, French politician, Prime Minister of France (b. 1842)", "html": "1911 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French politician, <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_France\" title=\"Prime Minister of France\">Prime Minister of France</a> (b. 1842)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French politician, <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_France\" title=\"Prime Minister of France\">Prime Minister of France</a> (b. 1842)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}, {"title": "Prime Minister of France", "link": "https://wikipedia.org/wiki/Prime_Minister_of_France"}]}, {"year": "1915", "text": "<PERSON>, American banker and politician, founded the First Hawaiian Bank (b. 1822)", "html": "1915 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American banker and politician, founded the <a href=\"https://wikipedia.org/wiki/First_Hawaiian_Bank\" title=\"First Hawaiian Bank\">First Hawaiian Bank</a> (b. 1822)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American banker and politician, founded the <a href=\"https://wikipedia.org/wiki/First_Hawaiian_Bank\" title=\"First Hawaiian Bank\">First Hawaiian Bank</a> (b. 1822)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "First Hawaiian Bank", "link": "https://wikipedia.org/wiki/First_Hawaiian_Bank"}]}, {"year": "1916", "text": "<PERSON><PERSON>, French author and critic (b. 1847)", "html": "1916 - <a href=\"https://wikipedia.org/wiki/%C3%89mile_Faguet\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, French author and critic (b. 1847)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/%C3%89mile_Faguet\" title=\"É<PERSON>\"><PERSON><PERSON></a>, French author and critic (b. 1847)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/%C3%89mile_Faguet"}]}, {"year": "1921", "text": "<PERSON>, executed Irish republican (b. 1889)", "html": "1921 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(Irish_republican)\" title=\"<PERSON> (Irish republican)\"><PERSON></a>, executed Irish republican (b. 1889)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(Irish_republican)\" title=\"<PERSON> (Irish republican)\"><PERSON></a>, executed Irish republican (b. 1889)", "links": [{"title": "<PERSON> (Irish republican)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(Irish_republican)"}]}, {"year": "1921", "text": "<PERSON>, executed Irish republican (b. 1897)", "html": "1921 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, executed Irish republican (b. 1897)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, executed Irish republican (b. 1897)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1924", "text": "<PERSON>, 1st Viscount <PERSON>, Irish businessman and politician, Lord Mayor of Belfast (b. 1847)", "html": "1924 - <a href=\"https://wikipedia.org/wiki/<PERSON>,_1st_Viscount_<PERSON>\" title=\"<PERSON>, 1st Viscount <PERSON>\"><PERSON>, 1st Viscount <PERSON></a>, Irish businessman and politician, <a href=\"https://wikipedia.org/wiki/Lord_Mayor_of_Belfast\" class=\"mw-redirect\" title=\"Lord Mayor of Belfast\">Lord Mayor of Belfast</a> (b. 1847)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>,_1st_Viscount_<PERSON>\" title=\"<PERSON>, 1st Viscount <PERSON>\"><PERSON>, 1st Viscount <PERSON></a>, Irish businessman and politician, <a href=\"https://wikipedia.org/wiki/Lord_Mayor_of_Belfast\" class=\"mw-redirect\" title=\"Lord Mayor of Belfast\">Lord Mayor of Belfast</a> (b. 1847)", "links": [{"title": "<PERSON>, 1st Viscount <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>,_1st_Viscount_<PERSON>"}, {"title": "Lord Mayor of Belfast", "link": "https://wikipedia.org/wiki/Lord_Mayor_of_Belfast"}]}, {"year": "1927", "text": "<PERSON>, English motorcycle racer (b. 1905)", "html": "1927 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English motorcycle racer (b. 1905)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English motorcycle racer (b. 1905)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1927", "text": "<PERSON>, Canadian lawyer and politician, 10th Premier of Quebec (b. 1847)", "html": "1927 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian lawyer and politician, 10th <a href=\"https://wikipedia.org/wiki/Premier_of_Quebec\" title=\"Premier of Quebec\">Premier of Quebec</a> (b. 1847)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian lawyer and politician, 10th <a href=\"https://wikipedia.org/wiki/Premier_of_Quebec\" title=\"Premier of Quebec\">Premier of Quebec</a> (b. 1847)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Premier of Quebec", "link": "https://wikipedia.org/wiki/Premier_of_Quebec"}]}, {"year": "1932", "text": "<PERSON>, English-Australian politician, 26th Premier of South Australia (b. 1856)", "html": "1932 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English-Australian politician, 26th <a href=\"https://wikipedia.org/wiki/Premier_of_South_Australia\" title=\"Premier of South Australia\">Premier of South Australia</a> (b. 1856)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English-Australian politician, 26th <a href=\"https://wikipedia.org/wiki/Premier_of_South_Australia\" title=\"Premier of South Australia\">Premier of South Australia</a> (b. 1856)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Premier of South Australia", "link": "https://wikipedia.org/wiki/Premier_of_South_Australia"}]}, {"year": "1933", "text": "<PERSON><PERSON><PERSON>, Croatian lawyer, judge, and poet (b. 1875)", "html": "1933 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>%C4%87\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Croatian lawyer, judge, and poet (b. 1875)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>%C4%87\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Croatian lawyer, judge, and poet (b. 1875)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_Domjani%C4%87"}]}, {"year": "1936", "text": "<PERSON><PERSON><PERSON>, Croatian explorer (b. 1875)", "html": "1936 - <a href=\"https://wikipedia.org/wiki/St<PERSON><PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Croatian explorer (b. 1875)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Croatian explorer (b. 1875)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>je<PERSON>_<PERSON>"}]}, {"year": "1937", "text": "<PERSON>, American actress and singer (b. 1911)", "html": "1937 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress and singer (b. 1911)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress and singer (b. 1911)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1942", "text": "<PERSON>, English engineer (b. 1903)", "html": "1942 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English engineer (b. 1903)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English engineer (b. 1903)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1945", "text": "<PERSON><PERSON>, Japanese philosopher and academic (b. 1870)", "html": "1945 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Japanese philosopher and academic (b. 1870)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>shi<PERSON>\"><PERSON><PERSON></a>, Japanese philosopher and academic (b. 1870)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>da"}]}, {"year": "1954", "text": "<PERSON>, English mathematician and computer scientist (b. 1912)", "html": "1954 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English mathematician and computer scientist (b. 1912)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Turin<PERSON>\"><PERSON></a>, English mathematician and computer scientist (b. 1912)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1956", "text": "<PERSON>, Australian politician, 15th Premier of Western Australia (b. 1879)", "html": "1956 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian politician, 15th <a href=\"https://wikipedia.org/wiki/Premier_of_Western_Australia\" title=\"Premier of Western Australia\">Premier of Western Australia</a> (b. 1879)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian politician, 15th <a href=\"https://wikipedia.org/wiki/Premier_of_Western_Australia\" title=\"Premier of Western Australia\">Premier of Western Australia</a> (b. 1879)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}, {"title": "Premier of Western Australia", "link": "https://wikipedia.org/wiki/Premier_of_Western_Australia"}]}, {"year": "1965", "text": "<PERSON>, American actress and singer (b. 1921)", "html": "1965 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress and singer (b. 1921)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress and singer (b. 1921)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1966", "text": "<PERSON>, German-French sculptor, painter, and poet (b. 1886)", "html": "1966 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German-French sculptor, painter, and poet (b. 1886)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German-French sculptor, painter, and poet (b. 1886)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1967", "text": "<PERSON><PERSON><PERSON>, Russian mathematician and academic (b. 1909)", "html": "1967 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Russian mathematician and academic (b. 1909)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Russian mathematician and academic (b. 1909)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1967", "text": "<PERSON>, American poet, short story writer, critic, and satirist (b. 1893)", "html": "1967 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American poet, short story writer, critic, and satirist (b. 1893)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American poet, short story writer, critic, and satirist (b. 1893)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1968", "text": "<PERSON>, American actor and singer (b. 1907)", "html": "1968 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor and singer (b. 1907)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor and singer (b. 1907)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1970", "text": "<PERSON><PERSON> <PERSON><PERSON>, English novelist, short story writer, essayist (b. 1879)", "html": "1970 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> <PERSON><PERSON>\"><PERSON><PERSON> <PERSON><PERSON></a>, English novelist, short story writer, essayist (b. 1879)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> <PERSON><PERSON>\"><PERSON><PERSON> <PERSON><PERSON></a>, English novelist, short story writer, essayist (b. 1879)", "links": [{"title": "<PERSON><PERSON> <PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "1978", "text": "<PERSON>, English chemist and academic, Nobel Prize laureate (b. 1897)", "html": "1978 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English chemist and academic, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Chemistry\" title=\"Nobel Prize in Chemistry\">Nobel Prize</a> laureate (b. 1897)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English chemist and academic, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Chemistry\" title=\"Nobel Prize in Chemistry\">Nobel Prize</a> laureate (b. 1897)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>"}, {"title": "Nobel Prize in Chemistry", "link": "https://wikipedia.org/wiki/Nobel_Prize_in_Chemistry"}]}, {"year": "1980", "text": "<PERSON>, Scottish journalist and economist (b. 1883)", "html": "1980 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(writer)\" title=\"<PERSON> (writer)\"><PERSON></a>, Scottish journalist and economist (b. 1883)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(writer)\" title=\"<PERSON> (writer)\"><PERSON></a>, Scottish journalist and economist (b. 1883)", "links": [{"title": "<PERSON> (writer)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(writer)"}]}, {"year": "1980", "text": "<PERSON>, Canadian-American painter and educator (b. 1913)", "html": "1980 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian-American painter and educator (b. 1913)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian-American painter and educator (b. 1913)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1980", "text": "<PERSON>, American novelist and essayist (b. 1891)", "html": "1980 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American novelist and essayist (b. 1891)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American novelist and essayist (b. 1891)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1985", "text": "<PERSON><PERSON><PERSON>, Estonian opera singer and educator (b. 1906)", "html": "1985 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Estonian opera singer and educator (b. 1906)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Estonian opera singer and educator (b. 1906)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1987", "text": "<PERSON><PERSON><PERSON>, Turkish poet and author (b. 1940)", "html": "1987 - <a href=\"https://wikipedia.org/wiki/Cahit_Zarifo%C4%9Flu\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Turkish poet and author (b. 1940)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Cahit_Zarifo%C4%9Flu\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Turkish poet and author (b. 1940)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Cahit_Zarifo%C4%9Flu"}]}, {"year": "1992", "text": "<PERSON>, American race car driver and businessman, co-founded NASCAR (b. 1909)", "html": "1992 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>.\"><PERSON>.</a>, American race car driver and businessman, co-founded <a href=\"https://wikipedia.org/wiki/NASCAR\" title=\"NASCAR\">NASCAR</a> (b. 1909)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>.\" title=\"<PERSON> Sr.\"><PERSON>.</a>, American race car driver and businessman, co-founded <a href=\"https://wikipedia.org/wiki/NASCAR\" title=\"NASCAR\">NASCAR</a> (b. 1909)", "links": [{"title": "<PERSON> Sr.", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_Sr."}, {"title": "NASCAR", "link": "https://wikipedia.org/wiki/NASCAR"}]}, {"year": "1995", "text": "<PERSON><PERSON><PERSON>, Chinese monk and educator (b. 1918)", "html": "1995 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Chinese monk and educator (b. 1918)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Chinese monk and educator (b. 1918)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>a"}]}, {"year": "2001", "text": "<PERSON><PERSON><PERSON>, Bolivian politician, 52nd President of Bolivia (b. 1907)", "html": "2001 - <a href=\"https://wikipedia.org/wiki/V%C3%<PERSON><PERSON>_<PERSON>_Estenssoro\" title=\"<PERSON><PERSON><PERSON>stensso<PERSON>\"><PERSON><PERSON><PERSON></a>, Bolivian politician, 52nd <a href=\"https://wikipedia.org/wiki/President_of_Bolivia\" title=\"President of Bolivia\">President of Bolivia</a> (b. 1907)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/V%C3%<PERSON><PERSON>_<PERSON>_Estenssoro\" title=\"<PERSON><PERSON><PERSON>stensso<PERSON>\"><PERSON><PERSON><PERSON></a>, Bolivian politician, 52nd <a href=\"https://wikipedia.org/wiki/President_of_Bolivia\" title=\"President of Bolivia\">President of Bolivia</a> (b. 1907)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/V%C3%<PERSON><PERSON>_<PERSON>_<PERSON>"}, {"title": "President of Bolivia", "link": "https://wikipedia.org/wiki/President_of_Bolivia"}]}, {"year": "2001", "text": "<PERSON>, English nurse and author (b. 1910)", "html": "2001 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English nurse and author (b. 1910)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English nurse and author (b. 1910)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2002", "text": "<PERSON><PERSON>, Swedish-American actress (b. 1915)", "html": "2002 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Swedish-American actress (b. 1915)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Swedish-American actress (b. 1915)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>"}]}, {"year": "2012", "text": "<PERSON>, South African paleontologist and academic (b. 1925)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, South African paleontologist and academic (b. 1925)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, South African paleontologist and academic (b. 1925)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "2013", "text": "<PERSON>, French educator and politician, Prime Minister of France (b. 1928)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French educator and politician, <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_France\" title=\"Prime Minister of France\">Prime Minister of France</a> (b. 1928)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French educator and politician, <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_France\" title=\"Prime Minister of France\">Prime Minister of France</a> (b. 1928)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Prime Minister of France", "link": "https://wikipedia.org/wiki/Prime_Minister_of_France"}]}, {"year": "2013", "text": "<PERSON>, American serial killer and sex offender (b. 1960)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American serial killer and sex offender (b. 1960)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American serial killer and sex offender (b. 1960)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2015", "text": "<PERSON>, English actor (b. 1922)", "html": "2015 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actor (b. 1922)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actor (b. 1922)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2023", "text": "<PERSON> <PERSON>, Iranian-American wrestler and actor (b. 1942)", "html": "2023 - <a href=\"https://wikipedia.org/wiki/The_Iron_Sheik\" title=\"The Iron Sheik\">The Iron Sheik</a>, Iranian-American wrestler and actor (b. 1942)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/The_Iron_Sheik\" title=\"The Iron Sheik\">The Iron Sheik</a>, Iranian-American wrestler and actor (b. 1942)", "links": [{"title": "The Iron Sheik", "link": "https://wikipedia.org/wiki/The_Iron_Sheik"}]}, {"year": "2024", "text": "<PERSON>, American astronaut and lunar explorer (b. 1933)", "html": "2024 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American astronaut and lunar explorer (b. 1933)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American astronaut and lunar explorer (b. 1933)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}]}}