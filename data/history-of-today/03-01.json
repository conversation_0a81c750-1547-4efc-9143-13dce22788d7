{"date": "March 1", "url": "https://wikipedia.org/wiki/March_1", "data": {"Events": [{"year": "509 BC", "text": "<PERSON><PERSON> celebrates the first triumph of the Roman Republic after his victory over the deposed king <PERSON> at the Battle of Silva Arsia.", "html": "509 BC - 509 BC - <a href=\"https://wikipedia.org/wiki/Publius_Valerius_Publicola\" class=\"mw-redirect\" title=\"Pub<PERSON>\">Pub<PERSON></a> celebrates the first <a href=\"https://wikipedia.org/wiki/Roman_triumph\" title=\"Roman triumph\">triumph</a> of the <a href=\"https://wikipedia.org/wiki/Roman_Republic\" title=\"Roman Republic\">Roman Republic</a> after his victory over the deposed king <a href=\"https://wikipedia.org/wiki/Lucius_Tarquinius_Superbus\" title=\"Lucius Tarquinius Superbus\"><PERSON>ius Superbus</a> at the <a href=\"https://wikipedia.org/wiki/Battle_of_Silva_Arsia\" title=\"Battle of Silva Arsia\">Battle of Silva Arsia</a>.", "no_year_html": "509 BC - <a href=\"https://wikipedia.org/wiki/Publius_Valerius_Publicola\" class=\"mw-redirect\" title=\"Publius <PERSON>rius <PERSON>\">Pub<PERSON></a> celebrates the first <a href=\"https://wikipedia.org/wiki/Roman_triumph\" title=\"Roman triumph\">triumph</a> of the <a href=\"https://wikipedia.org/wiki/Roman_Republic\" title=\"Roman Republic\">Roman Republic</a> after his victory over the deposed king <a href=\"https://wikipedia.org/wiki/Lucius_Tarquinius_Superbus\" title=\"Lucius Tarquinius Superbus\">Lucius <PERSON>ius Superbus</a> at the <a href=\"https://wikipedia.org/wiki/Battle_of_Silva_Arsia\" title=\"Battle of Silva Arsia\">Battle of Silva Arsia</a>.", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Publius_Valerius_Publicola"}, {"title": "Roman triumph", "link": "https://wikipedia.org/wiki/Roman_triumph"}, {"title": "Roman Republic", "link": "https://wikipedia.org/wiki/Roman_Republic"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_Ta<PERSON><PERSON><PERSON>_Superbus"}, {"title": "Battle of Silva Arsia", "link": "https://wikipedia.org/wiki/Battle_of_Silva_Ars<PERSON>"}]}, {"year": "293", "text": "Emperor <PERSON><PERSON><PERSON><PERSON> and <PERSON><PERSON> appoint <PERSON><PERSON><PERSON> and <PERSON><PERSON> as Caesars. This is considered the beginning of the Tetrarchy, known as the Quattuor Principes Mundi (\"Four Rulers of the World\").", "html": "293 - Emperor <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>ian\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a> and <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a> appoint <a href=\"https://wikipedia.org/wiki/Constantius_Chlorus\" title=\"<PERSON>stantius Chlorus\"><PERSON><PERSON><PERSON> Chlorus</a> and <a href=\"https://wikipedia.org/wiki/Galerius\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a> as <a href=\"https://wikipedia.org/wiki/Caesar_(title)\" title=\"Caesar (title)\"><PERSON><PERSON></a>. This is considered the beginning of the <a href=\"https://wikipedia.org/wiki/Tetrarchy\" title=\"Tetrarchy\">Tetrarchy</a>, known as the <i>Quattuor Principes Mundi</i> (\"Four Rulers of the World\").", "no_year_html": "Emperor <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a> and <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a> appoint <a href=\"https://wikipedia.org/wiki/Con<PERSON>ius_Chlorus\" title=\"<PERSON><PERSON>ius Chlorus\"><PERSON><PERSON><PERSON> Chlorus</a> and <a href=\"https://wikipedia.org/wiki/Galerius\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a> as <a href=\"https://wikipedia.org/wiki/Caesar_(title)\" title=\"Caesar (title)\"><PERSON><PERSON></a>. This is considered the beginning of the <a href=\"https://wikipedia.org/wiki/Tetrarchy\" title=\"Tetrarchy\">Tetrarchy</a>, known as the <i>Quattuor Principes Mundi</i> (\"Four Rulers of the World\").", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>ian"}, {"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>ian"}, {"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Constantius_Chlorus"}, {"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>rius"}, {"title": "<PERSON> (title)", "link": "https://wikipedia.org/wiki/<PERSON>_(title)"}, {"title": "Tetrarchy", "link": "https://wikipedia.org/wiki/Tetrarchy"}]}, {"year": "350", "text": "<PERSON><PERSON><PERSON><PERSON> proclaims himself <PERSON> after being encouraged to do so by <PERSON><PERSON><PERSON>, sister of <PERSON><PERSON><PERSON>.", "html": "350 - <a href=\"https://wikipedia.org/wiki/Vetranio\" title=\"Vetranio\"><PERSON><PERSON><PERSON><PERSON></a> proclaims himself <PERSON> after being encouraged to do so by <a href=\"https://wikipedia.org/wiki/Constantina\" title=\"Con<PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, sister of <a href=\"https://wikipedia.org/wiki/Constantius_II\" title=\"Constantius II\">Constantius II</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Vetranio\" title=\"Vetranio\"><PERSON><PERSON><PERSON><PERSON></a> proclaims himself Caesar after being encouraged to do so by <a href=\"https://wikipedia.org/wiki/Constantina\" title=\"Constant<PERSON>\"><PERSON><PERSON><PERSON></a>, sister of <a href=\"https://wikipedia.org/wiki/Constantius_II\" title=\"Constantius II\">Constantius II</a>.", "links": [{"title": "Vetranio", "link": "https://wikipedia.org/wiki/Vetranio"}, {"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Constantina"}, {"title": "<PERSON><PERSON><PERSON> II", "link": "https://wikipedia.org/wiki/Constantius_II"}]}, {"year": "834", "text": "Emperor <PERSON> is restored as sole ruler of the Frankish Empire.", "html": "834 - Emperor <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_Pi<PERSON>\" title=\"<PERSON> the Pi<PERSON>\"><PERSON> the Pi<PERSON></a> is restored as sole ruler of the <a href=\"https://wikipedia.org/wiki/Francia\" title=\"Francia\">Frankish Empire</a>.", "no_year_html": "Emperor <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON> Pi<PERSON>\"><PERSON> the <PERSON></a> is restored as sole ruler of the <a href=\"https://wikipedia.org/wiki/Francia\" title=\"Francia\">Frankish Empire</a>.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "Francia", "link": "https://wikipedia.org/wiki/Francia"}]}, {"year": "1476", "text": "Forces of the Catholic Monarchs engage the combined Portuguese-Castilian armies of <PERSON><PERSON><PERSON> and <PERSON> at the Battle of Toro.", "html": "1476 - Forces of the <a href=\"https://wikipedia.org/wiki/Catholic_Monarchs\" class=\"mw-redirect\" title=\"Catholic Monarchs\">Catholic Monarchs</a> engage the combined Portuguese-Castilian armies of <a href=\"https://wikipedia.org/wiki/Afonso_V_of_Portugal\" title=\"Afonso V of Portugal\">Afonso V</a> and <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Portugal\" title=\"John II of Portugal\">Prince <PERSON></a> at the <a href=\"https://wikipedia.org/wiki/Battle_of_Toro\" title=\"Battle of Toro\">Battle of Toro</a>.", "no_year_html": "Forces of the <a href=\"https://wikipedia.org/wiki/Catholic_Monarchs\" class=\"mw-redirect\" title=\"Catholic Monarchs\">Catholic Monarchs</a> engage the combined Portuguese-Castilian armies of <a href=\"https://wikipedia.org/wiki/Afonso_V_of_Portugal\" title=\"Afonso V of Portugal\"><PERSON><PERSON>nso V</a> and <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Portugal\" title=\"John II of Portugal\">Prince <PERSON></a> at the <a href=\"https://wikipedia.org/wiki/Battle_of_Toro\" title=\"Battle of Toro\">Battle of Toro</a>.", "links": [{"title": "Catholic Monarchs", "link": "https://wikipedia.org/wiki/Catholic_Monarchs"}, {"title": "<PERSON><PERSON><PERSON> V of Portugal", "link": "https://wikipedia.org/wiki/Afonso_V_of_Portugal"}, {"title": "<PERSON> of Portugal", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Portugal"}, {"title": "Battle of Toro", "link": "https://wikipedia.org/wiki/Battle_of_Toro"}]}, {"year": "1562", "text": "Sixty-three Huguenots are massacred in Wassy, France, marking the start of the French Wars of Religion.", "html": "1562 - Sixty-three <a href=\"https://wikipedia.org/wiki/Huguenot\" class=\"mw-redirect\" title=\"Huguenot\">Huguenots</a> are <a href=\"https://wikipedia.org/wiki/Massacre_of_Wassy\" class=\"mw-redirect\" title=\"Massacre of Wassy\">massacred in Wassy, France</a>, marking the start of the <a href=\"https://wikipedia.org/wiki/French_Wars_of_Religion\" title=\"French Wars of Religion\">French Wars of Religion</a>.", "no_year_html": "Sixty-three <a href=\"https://wikipedia.org/wiki/Huguenot\" class=\"mw-redirect\" title=\"Huguenot\">Huguenots</a> are <a href=\"https://wikipedia.org/wiki/Massacre_of_Wassy\" class=\"mw-redirect\" title=\"Massacre of Wassy\">massacred in Wassy, France</a>, marking the start of the <a href=\"https://wikipedia.org/wiki/French_Wars_of_Religion\" title=\"French Wars of Religion\">French Wars of Religion</a>.", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>not"}, {"title": "Massacre of <PERSON>sy", "link": "https://wikipedia.org/wiki/Massacre_of_<PERSON><PERSON>"}, {"title": "French Wars of Religion", "link": "https://wikipedia.org/wiki/French_Wars_of_Religion"}]}, {"year": "1628", "text": "Writs issued in February by Charles I of England mandate that every county in England (not just seaport towns) pay ship tax by this date.", "html": "1628 - Writs issued in February by <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_England\" title=\"Charles I of England\"><PERSON> of England</a> mandate that every county in England (not just seaport towns) pay <a href=\"https://wikipedia.org/wiki/Ship_tax\" class=\"mw-redirect\" title=\"Ship tax\">ship tax</a> by this date.", "no_year_html": "Writs issued in February by <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_England\" title=\"Charles I of England\"><PERSON> of England</a> mandate that every county in England (not just seaport towns) pay <a href=\"https://wikipedia.org/wiki/Ship_tax\" class=\"mw-redirect\" title=\"Ship tax\">ship tax</a> by this date.", "links": [{"title": "<PERSON> of England", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_England"}, {"title": "Ship tax", "link": "https://wikipedia.org/wiki/Ship_tax"}]}, {"year": "1633", "text": "<PERSON> reclaims his role as commander of New France on behalf of <PERSON><PERSON><PERSON>.", "html": "1633 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> reclaims his role as commander of <a href=\"https://wikipedia.org/wiki/New_France\" title=\"New France\">New France</a> on behalf of <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> reclaims his role as commander of <a href=\"https://wikipedia.org/wiki/New_France\" title=\"New France\">New France</a> on behalf of <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "New France", "link": "https://wikipedia.org/wiki/New_France"}, {"title": "<PERSON> <PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1692", "text": "<PERSON>, <PERSON>, and <PERSON><PERSON><PERSON> are brought before local magistrates in Salem Village, Massachusetts, beginning what would become known as the Salem witch trials.", "html": "1692 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, and <a href=\"https://wikipedia.org/wiki/Tituba\" title=\"Tituba\">Tituba</a> are brought before local magistrates in <a href=\"https://wikipedia.org/wiki/Danvers,_Massachusetts\" title=\"Danvers, Massachusetts\">Salem Village, Massachusetts</a>, beginning what would become known as the <a href=\"https://wikipedia.org/wiki/Salem_witch_trials\" title=\"Salem witch trials\">Salem witch trials</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, and <a href=\"https://wikipedia.org/wiki/Tituba\" title=\"Tituba\">Tituba</a> are brought before local magistrates in <a href=\"https://wikipedia.org/wiki/Danvers,_Massachusetts\" title=\"Danvers, Massachusetts\">Salem Village, Massachusetts</a>, beginning what would become known as the <a href=\"https://wikipedia.org/wiki/Salem_witch_trials\" title=\"Salem witch trials\">Salem witch trials</a>.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Tituba", "link": "https://wikipedia.org/wiki/Tituba"}, {"title": "Danvers, Massachusetts", "link": "https://wikipedia.org/wiki/Danvers,_Massachusetts"}, {"title": "Salem witch trials", "link": "https://wikipedia.org/wiki/Salem_witch_trials"}]}, {"year": "1781", "text": "The Articles of Confederation goes into effect in the United States.", "html": "1781 - The <a href=\"https://wikipedia.org/wiki/Articles_of_Confederation\" title=\"Articles of Confederation\">Articles of Confederation</a> goes into effect in the United States.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Articles_of_Confederation\" title=\"Articles of Confederation\">Articles of Confederation</a> goes into effect in the United States.", "links": [{"title": "Articles of Confederation", "link": "https://wikipedia.org/wiki/Articles_of_Confederation"}]}, {"year": "1796", "text": "The Dutch East India Company is nationalized by the Batavian Republic.", "html": "1796 - The <a href=\"https://wikipedia.org/wiki/Dutch_East_India_Company\" title=\"Dutch East India Company\">Dutch East India Company</a> is nationalized by the <a href=\"https://wikipedia.org/wiki/Batavian_Republic\" title=\"Batavian Republic\">Batavian Republic</a>.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Dutch_East_India_Company\" title=\"Dutch East India Company\">Dutch East India Company</a> is nationalized by the <a href=\"https://wikipedia.org/wiki/Batavian_Republic\" title=\"Batavian Republic\">Batavian Republic</a>.", "links": [{"title": "Dutch East India Company", "link": "https://wikipedia.org/wiki/Dutch_East_India_Company"}, {"title": "Batavian Republic", "link": "https://wikipedia.org/wiki/Batavian_Republic"}]}, {"year": "1805", "text": "Justice <PERSON> is acquitted at the end of his impeachment trial by the U.S. Senate.", "html": "1805 - Justice <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> is acquitted at the end of his <a href=\"https://wikipedia.org/wiki/Impeachment\" title=\"Impeachment\">impeachment</a> trial by the <a href=\"https://wikipedia.org/wiki/U.S._Senate\" class=\"mw-redirect\" title=\"U.S. Senate\">U.S. Senate</a>.", "no_year_html": "Justice <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> is acquitted at the end of his <a href=\"https://wikipedia.org/wiki/Impeachment\" title=\"Impeachment\">impeachment</a> trial by the <a href=\"https://wikipedia.org/wiki/U.S._Senate\" class=\"mw-redirect\" title=\"U.S. Senate\">U.S. Senate</a>.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Impeachment", "link": "https://wikipedia.org/wiki/Impeachment"}, {"title": "U.S. Senate", "link": "https://wikipedia.org/wiki/U.S._Senate"}]}, {"year": "1811", "text": "Leaders of the Mamluk dynasty are killed by Egyptian ruler <PERSON>.", "html": "1811 - <a href=\"https://wikipedia.org/wiki/Mamluk#End_of_power_in_Egypt\" title=\"Mamluk\">Leaders of the Mamluk dynasty are killed</a> by <a href=\"https://wikipedia.org/wiki/Egypt\" title=\"Egypt\">Egyptian</a> ruler <a href=\"https://wikipedia.org/wiki/Muhammad_Ali_of_Egypt\" title=\"<PERSON> of Egypt\"><PERSON></a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Mamluk#End_of_power_in_Egypt\" title=\"Mamluk\">Leaders of the Mamluk dynasty are killed</a> by <a href=\"https://wikipedia.org/wiki/Egypt\" title=\"Egypt\">Egyptian</a> ruler <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Egypt\" title=\"<PERSON> of Egypt\"><PERSON></a>.", "links": [{"title": "Mamluk", "link": "https://wikipedia.org/wiki/Mamluk#End_of_power_in_Egypt"}, {"title": "Egypt", "link": "https://wikipedia.org/wiki/Egypt"}, {"title": "<PERSON> of Egypt", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Egypt"}]}, {"year": "1815", "text": "<PERSON> returns to France from his banishment on Elba.", "html": "1815 - <a href=\"https://wikipedia.org/wiki/Napoleon\" title=\"<PERSON>\"><PERSON></a> returns to France from his banishment on <a href=\"https://wikipedia.org/wiki/Elba\" title=\"Elba\">Elba</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> returns to France from his banishment on <a href=\"https://wikipedia.org/wiki/Elba\" title=\"Elba\">Elba</a>.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Napoleon"}, {"title": "Elba", "link": "https://wikipedia.org/wiki/Elba"}]}, {"year": "1836", "text": "A convention of delegates from 57 Texas communities convenes in Washington-on-the-Brazos, Texas, to deliberate independence from Mexico.", "html": "1836 - A <a href=\"https://wikipedia.org/wiki/Convention_of_1836\" title=\"Convention of 1836\">convention</a> of delegates from 57 <a href=\"https://wikipedia.org/wiki/Texas\" title=\"Texas\">Texas</a> communities convenes in <a href=\"https://wikipedia.org/wiki/Washington-on-the-Brazos,_Texas\" title=\"Washington-on-the-Brazos, Texas\">Washington-on-the-Brazos, Texas</a>, to deliberate independence from Mexico.", "no_year_html": "A <a href=\"https://wikipedia.org/wiki/Convention_of_1836\" title=\"Convention of 1836\">convention</a> of delegates from 57 <a href=\"https://wikipedia.org/wiki/Texas\" title=\"Texas\">Texas</a> communities convenes in <a href=\"https://wikipedia.org/wiki/Washington-on-the-Brazos,_Texas\" title=\"Washington-on-the-Brazos, Texas\">Washington-on-the-Brazos, Texas</a>, to deliberate independence from Mexico.", "links": [{"title": "Convention of 1836", "link": "https://wikipedia.org/wiki/Convention_of_1836"}, {"title": "Texas", "link": "https://wikipedia.org/wiki/Texas"}, {"title": "Washington-on-the-Brazos, Texas", "link": "https://wikipedia.org/wiki/Washington-on-the-Brazos,_Texas"}]}, {"year": "1845", "text": "United States President <PERSON> signs a bill authorizing the United States to annex the Republic of Texas.", "html": "1845 - United States President <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> signs a bill authorizing the United States to annex the <a href=\"https://wikipedia.org/wiki/Republic_of_Texas\" title=\"Republic of Texas\">Republic of Texas</a>.", "no_year_html": "United States President <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> signs a bill authorizing the United States to annex the <a href=\"https://wikipedia.org/wiki/Republic_of_Texas\" title=\"Republic of Texas\">Republic of Texas</a>.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Republic of Texas", "link": "https://wikipedia.org/wiki/Republic_of_Texas"}]}, {"year": "1867", "text": "Nebraska is admitted as the 37th U.S. state.", "html": "1867 - <a href=\"https://wikipedia.org/wiki/Nebraska\" title=\"Nebraska\">Nebraska</a> is admitted as the 37th <a href=\"https://wikipedia.org/wiki/U.S._state\" title=\"U.S. state\">U.S. state</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Nebraska\" title=\"Nebraska\">Nebraska</a> is admitted as the 37th <a href=\"https://wikipedia.org/wiki/U.S._state\" title=\"U.S. state\">U.S. state</a>.", "links": [{"title": "Nebraska", "link": "https://wikipedia.org/wiki/Nebraska"}, {"title": "U.S. state", "link": "https://wikipedia.org/wiki/U.S._state"}]}, {"year": "1870", "text": "Marshal <PERSON><PERSON> <PERSON><PERSON> dies during the Battle of Cerro Corá thus marking the end of the Paraguayan War.", "html": "1870 - Marshal <a href=\"https://wikipedia.org/wiki/Francisco_Solano_L%C3%B3pez\" title=\"<PERSON>\"><PERSON><PERSON> <PERSON><PERSON></a> dies during the <a href=\"https://wikipedia.org/wiki/Battle_of_Cerro_Cor%C3%A1\" title=\"Battle of Cerro Corá\">Battle of Cerro Corá</a> thus marking the end of the <a href=\"https://wikipedia.org/wiki/Paraguayan_War\" title=\"Paraguayan War\">Paraguayan War</a>.", "no_year_html": "Marshal <a href=\"https://wikipedia.org/wiki/Francisco_Solano_L%C3%B3pez\" title=\"<PERSON>\"><PERSON><PERSON> <PERSON><PERSON></a> dies during the <a href=\"https://wikipedia.org/wiki/Battle_of_Cerro_Cor%C3%A1\" title=\"Battle of Cerro Corá\">Battle of Cerro Corá</a> thus marking the end of the <a href=\"https://wikipedia.org/wiki/Paraguayan_War\" title=\"Paraguayan War\">Paraguayan War</a>.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Francisco_Solano_L%C3%B3pez"}, {"title": "Battle of Cerro Corá", "link": "https://wikipedia.org/wiki/Battle_of_Cerro_Cor%C3%A1"}, {"title": "Paraguayan War", "link": "https://wikipedia.org/wiki/Paraguayan_War"}]}, {"year": "1871", "text": "The victorious Prussian Army parades through Paris, France, after the end of the Siege of Paris during the Franco-Prussian War.", "html": "1871 - The victorious <a href=\"https://wikipedia.org/wiki/Prussian_Army\" title=\"Prussian Army\">Prussian Army</a> parades through Paris, France, after the end of the <a href=\"https://wikipedia.org/wiki/Siege_of_Paris_(1870%E2%80%9371)\" class=\"mw-redirect\" title=\"Siege of Paris (1870-71)\">Siege of Paris</a> during the <a href=\"https://wikipedia.org/wiki/Franco-Prussian_War\" title=\"Franco-Prussian War\">Franco-Prussian War</a>.", "no_year_html": "The victorious <a href=\"https://wikipedia.org/wiki/Prussian_Army\" title=\"Prussian Army\">Prussian Army</a> parades through Paris, France, after the end of the <a href=\"https://wikipedia.org/wiki/Siege_of_Paris_(1870%E2%80%9371)\" class=\"mw-redirect\" title=\"Siege of Paris (1870-71)\">Siege of Paris</a> during the <a href=\"https://wikipedia.org/wiki/Franco-Prussian_War\" title=\"Franco-Prussian War\">Franco-Prussian War</a>.", "links": [{"title": "Prussian Army", "link": "https://wikipedia.org/wiki/Prussian_Army"}, {"title": "Siege of Paris (1870-71)", "link": "https://wikipedia.org/wiki/Siege_of_Paris_(1870%E2%80%9371)"}, {"title": "Franco-Prussian War", "link": "https://wikipedia.org/wiki/Franco-Prussian_War"}]}, {"year": "1872", "text": "Yellowstone National Park is established as the world's first national park.", "html": "1872 - <a href=\"https://wikipedia.org/wiki/Yellowstone_National_Park\" title=\"Yellowstone National Park\">Yellowstone National Park</a> is established as the world's first <a href=\"https://wikipedia.org/wiki/National_park\" title=\"National park\">national park</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Yellowstone_National_Park\" title=\"Yellowstone National Park\">Yellowstone National Park</a> is established as the world's first <a href=\"https://wikipedia.org/wiki/National_park\" title=\"National park\">national park</a>.", "links": [{"title": "Yellowstone National Park", "link": "https://wikipedia.org/wiki/Yellowstone_National_Park"}, {"title": "National park", "link": "https://wikipedia.org/wiki/National_park"}]}, {"year": "1893", "text": "Electrical engineer <PERSON> gives the first public demonstration of radio in St. Louis, Missouri.", "html": "1893 - Electrical engineer <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> gives the first public demonstration of <a href=\"https://wikipedia.org/wiki/Radio\" title=\"Radio\">radio</a> in <a href=\"https://wikipedia.org/wiki/St._Louis\" title=\"St. Louis\">St. Louis</a>, <a href=\"https://wikipedia.org/wiki/Missouri\" title=\"Missouri\">Missouri</a>.", "no_year_html": "Electrical engineer <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> gives the first public demonstration of <a href=\"https://wikipedia.org/wiki/Radio\" title=\"Radio\">radio</a> in <a href=\"https://wikipedia.org/wiki/St._Louis\" title=\"St. Louis\">St. Louis</a>, <a href=\"https://wikipedia.org/wiki/Missouri\" title=\"Missouri\">Missouri</a>.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Radio", "link": "https://wikipedia.org/wiki/Radio"}, {"title": "St. Louis", "link": "https://wikipedia.org/wiki/St._Louis"}, {"title": "Missouri", "link": "https://wikipedia.org/wiki/Missouri"}]}, {"year": "1896", "text": "Battle of Adwa: An Ethiopian army defeats an outnumbered Italian force, ending the First Italo-Ethiopian War.", "html": "1896 - <a href=\"https://wikipedia.org/wiki/Battle_of_Adwa\" title=\"Battle of Adwa\">Battle of Adwa</a>: An <a href=\"https://wikipedia.org/wiki/Ethiopia\" title=\"Ethiopia\">Ethiopian</a> army defeats an outnumbered <a href=\"https://wikipedia.org/wiki/Kingdom_of_Italy\" title=\"Kingdom of Italy\">Italian</a> force, ending the <a href=\"https://wikipedia.org/wiki/First_Italo-Ethiopian_War\" title=\"First Italo-Ethiopian War\">First Italo-Ethiopian War</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Battle_of_Adwa\" title=\"Battle of Adwa\">Battle of Adwa</a>: An <a href=\"https://wikipedia.org/wiki/Ethiopia\" title=\"Ethiopia\">Ethiopian</a> army defeats an outnumbered <a href=\"https://wikipedia.org/wiki/Kingdom_of_Italy\" title=\"Kingdom of Italy\">Italian</a> force, ending the <a href=\"https://wikipedia.org/wiki/First_Italo-Ethiopian_War\" title=\"First Italo-Ethiopian War\">First Italo-Ethiopian War</a>.", "links": [{"title": "Battle of Adwa", "link": "https://wikipedia.org/wiki/Battle_of_Adwa"}, {"title": "Ethiopia", "link": "https://wikipedia.org/wiki/Ethiopia"}, {"title": "Kingdom of Italy", "link": "https://wikipedia.org/wiki/Kingdom_of_Italy"}, {"title": "First Italo-Ethiopian War", "link": "https://wikipedia.org/wiki/First_Italo-Ethiopian_War"}]}, {"year": "1896", "text": "<PERSON> discovers radioactive decay.", "html": "1896 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> discovers <a href=\"https://wikipedia.org/wiki/Radioactive_decay\" title=\"Radioactive decay\">radioactive decay</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> discovers <a href=\"https://wikipedia.org/wiki/Radioactive_decay\" title=\"Radioactive decay\">radioactive decay</a>.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}, {"title": "Radioactive decay", "link": "https://wikipedia.org/wiki/Radioactive_decay"}]}, {"year": "1901", "text": "The Australian Army is formed.", "html": "1901 - The <a href=\"https://wikipedia.org/wiki/Australian_Army\" title=\"Australian Army\">Australian Army</a> is formed.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Australian_Army\" title=\"Australian Army\">Australian Army</a> is formed.", "links": [{"title": "Australian Army", "link": "https://wikipedia.org/wiki/Australian_Army"}]}, {"year": "1910", "text": "The deadliest avalanche in United States history buries a Great Northern Railway train in northeastern King County, Washington, killing 96 people.", "html": "1910 - The deadliest <a href=\"https://wikipedia.org/wiki/Wellington,_Washington#1910_avalanche\" title=\"Wellington, Washington\">avalanche</a> in United States history buries a <a href=\"https://wikipedia.org/wiki/Great_Northern_Railway_(U.S.)\" title=\"Great Northern Railway (U.S.)\">Great Northern Railway</a> train in northeastern <a href=\"https://wikipedia.org/wiki/King_County,_Washington\" title=\"King County, Washington\">King County, Washington</a>, killing 96 people.", "no_year_html": "The deadliest <a href=\"https://wikipedia.org/wiki/Wellington,_Washington#1910_avalanche\" title=\"Wellington, Washington\">avalanche</a> in United States history buries a <a href=\"https://wikipedia.org/wiki/Great_Northern_Railway_(U.S.)\" title=\"Great Northern Railway (U.S.)\">Great Northern Railway</a> train in northeastern <a href=\"https://wikipedia.org/wiki/King_County,_Washington\" title=\"King County, Washington\">King County, Washington</a>, killing 96 people.", "links": [{"title": "Wellington, Washington", "link": "https://wikipedia.org/wiki/Wellington,_Washington#1910_avalanche"}, {"title": "Great Northern Railway (U.S.)", "link": "https://wikipedia.org/wiki/Great_Northern_Railway_(U.S.)"}, {"title": "King County, Washington", "link": "https://wikipedia.org/wiki/King_County,_Washington"}]}, {"year": "1914", "text": "China joins the Universal Postal Union.", "html": "1914 - <a href=\"https://wikipedia.org/wiki/Republic_of_China_(1912%E2%80%9349)\" class=\"mw-redirect\" title=\"Republic of China (1912-49)\">China</a> joins the <a href=\"https://wikipedia.org/wiki/Universal_Postal_Union\" title=\"Universal Postal Union\">Universal Postal Union</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Republic_of_China_(1912%E2%80%9349)\" class=\"mw-redirect\" title=\"Republic of China (1912-49)\">China</a> joins the <a href=\"https://wikipedia.org/wiki/Universal_Postal_Union\" title=\"Universal Postal Union\">Universal Postal Union</a>.", "links": [{"title": "Republic of China (1912-49)", "link": "https://wikipedia.org/wiki/Republic_of_China_(1912%E2%80%9349)"}, {"title": "Universal Postal Union", "link": "https://wikipedia.org/wiki/Universal_Postal_Union"}]}, {"year": "1917", "text": "The Zimmermann Telegram is reprinted in newspapers across the United States after the U.S. government releases its unencrypted text.", "html": "1917 - The <a href=\"https://wikipedia.org/wiki/Zimmermann_Telegram\" class=\"mw-redirect\" title=\"Zimmermann Telegram\">Zimmermann Telegram</a> is reprinted in newspapers across the United States after the <a href=\"https://wikipedia.org/wiki/Federal_government_of_the_United_States\" title=\"Federal government of the United States\">U.S. government</a> releases its <a href=\"https://wikipedia.org/wiki/Plaintext\" title=\"Plaintext\">unencrypted text</a>.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Z<PERSON>mann_Telegram\" class=\"mw-redirect\" title=\"Zimmermann Telegram\">Zimmermann Telegram</a> is reprinted in newspapers across the United States after the <a href=\"https://wikipedia.org/wiki/Federal_government_of_the_United_States\" title=\"Federal government of the United States\">U.S. government</a> releases its <a href=\"https://wikipedia.org/wiki/Plaintext\" title=\"Plaintext\">unencrypted text</a>.", "links": [{"title": "Zimmermann Telegram", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>mann_Telegram"}, {"title": "Federal government of the United States", "link": "https://wikipedia.org/wiki/Federal_government_of_the_United_States"}, {"title": "Plaintext", "link": "https://wikipedia.org/wiki/Plaintext"}]}, {"year": "1919", "text": "March 1st Movement begins in Korea under Japanese rule.", "html": "1919 - <a href=\"https://wikipedia.org/wiki/March_1st_Movement\" class=\"mw-redirect\" title=\"March 1st Movement\">March 1st Movement</a> begins in <a href=\"https://wikipedia.org/wiki/Korea_under_Japanese_rule\" title=\"Korea under Japanese rule\">Korea under Japanese rule</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/March_1st_Movement\" class=\"mw-redirect\" title=\"March 1st Movement\">March 1st Movement</a> begins in <a href=\"https://wikipedia.org/wiki/Korea_under_Japanese_rule\" title=\"Korea under Japanese rule\">Korea under Japanese rule</a>.", "links": [{"title": "March 1st Movement", "link": "https://wikipedia.org/wiki/March_1st_Movement"}, {"title": "Korea under Japanese rule", "link": "https://wikipedia.org/wiki/Korea_under_Japanese_rule"}]}, {"year": "1921", "text": "The Australian cricket team captained by <PERSON> becomes the first team to complete a whitewash of The Ashes, something that would not be repeated for 86 years.", "html": "1921 - The <a href=\"https://wikipedia.org/wiki/Australia_national_cricket_team\" title=\"Australia national cricket team\">Australian cricket team</a> captained by <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> becomes the first team to complete a <a href=\"https://wikipedia.org/wiki/Whitewash_(sport)\" title=\"Whitewash (sport)\">whitewash</a> of <a href=\"https://wikipedia.org/wiki/The_Ashes\" title=\"The Ashes\">The Ashes</a>, something that would not be repeated for 86 years.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Australia_national_cricket_team\" title=\"Australia national cricket team\">Australian cricket team</a> captained by <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> becomes the first team to complete a <a href=\"https://wikipedia.org/wiki/Whitewash_(sport)\" title=\"Whitewash (sport)\">whitewash</a> of <a href=\"https://wikipedia.org/wiki/The_Ashes\" title=\"The Ashes\">The Ashes</a>, something that would not be repeated for 86 years.", "links": [{"title": "Australia national cricket team", "link": "https://wikipedia.org/wiki/Australia_national_cricket_team"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Whitewash (sport)", "link": "https://wikipedia.org/wiki/Whitewash_(sport)"}, {"title": "The Ashes", "link": "https://wikipedia.org/wiki/The_Ashes"}]}, {"year": "1921", "text": "Following mass protests in Petrograd demanding greater freedom in the RSFSR, the Kronstadt rebellion begins, with sailors and citizens taking up arms against the Bolsheviks.", "html": "1921 - Following mass protests in <a href=\"https://wikipedia.org/wiki/Petrograd\" class=\"mw-redirect\" title=\"Petrograd\">Petrograd</a> demanding greater freedom in the <a href=\"https://wikipedia.org/wiki/RSFSR\" class=\"mw-redirect\" title=\"RSFSR\">RSFSR</a>, the <a href=\"https://wikipedia.org/wiki/Kronstadt_rebellion\" title=\"Kronstadt rebellion\">Kronstadt rebellion</a> begins, with sailors and citizens taking up arms against the <a href=\"https://wikipedia.org/wiki/Bolsheviks\" title=\"Bolsheviks\">Bolsheviks</a>.", "no_year_html": "Following mass protests in <a href=\"https://wikipedia.org/wiki/Petrograd\" class=\"mw-redirect\" title=\"Petrograd\">Petrograd</a> demanding greater freedom in the <a href=\"https://wikipedia.org/wiki/RSFSR\" class=\"mw-redirect\" title=\"RSFSR\">RSFSR</a>, the <a href=\"https://wikipedia.org/wiki/Kronstadt_rebellion\" title=\"Kronstadt rebellion\">Kronstadt rebellion</a> begins, with sailors and citizens taking up arms against the <a href=\"https://wikipedia.org/wiki/Bolsheviks\" title=\"Bolsheviks\">Bolsheviks</a>.", "links": [{"title": "Petrograd", "link": "https://wikipedia.org/wiki/Petrograd"}, {"title": "RSFSR", "link": "https://wikipedia.org/wiki/RSFSR"}, {"title": "Kronstadt rebellion", "link": "https://wikipedia.org/wiki/Kronstadt_rebellion"}, {"title": "Bolsheviks", "link": "https://wikipedia.org/wiki/Bolsheviks"}]}, {"year": "1932", "text": "Aviator <PERSON>'s 20-month-old son <PERSON> is kidnapped from his home in East Amwell, New Jersey. His body would not be found until May 12.", "html": "1932 - Aviator <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>'s 20-month-old son <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_kidnapping\" title=\"<PERSON><PERSON><PERSON><PERSON> kidnapping\"><PERSON> is kidnapped</a> from his home in <a href=\"https://wikipedia.org/wiki/East_Amwell_Township,_New_Jersey\" title=\"East Amwell Township, New Jersey\">East Amwell</a>, New Jersey. His body would not be found until May 12.", "no_year_html": "Aviator <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>'s 20-month-old son <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_kidnapping\" title=\"<PERSON><PERSON><PERSON><PERSON> kidnapping\"><PERSON> is kidnapped</a> from his home in <a href=\"https://wikipedia.org/wiki/East_Amwell_Township,_New_Jersey\" title=\"East Amwell Township, New Jersey\">East Amwell</a>, New Jersey. His body would not be found until May 12.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}, {"title": "<PERSON><PERSON><PERSON><PERSON> kidnapping", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_kidnapping"}, {"title": "East Amwell Township, New Jersey", "link": "https://wikipedia.org/wiki/East_Amwell_Township,_New_Jersey"}]}, {"year": "1939", "text": "An Imperial Japanese Army ammunition dump explodes at Hirakata, Osaka, Japan, killing 94.", "html": "1939 - An <a href=\"https://wikipedia.org/wiki/Imperial_Japanese_Army\" title=\"Imperial Japanese Army\">Imperial Japanese Army</a> ammunition dump explodes at <a href=\"https://wikipedia.org/wiki/Hirakata,_Osaka\" class=\"mw-redirect\" title=\"Hirakata, Osaka\">Hirakata, Osaka</a>, Japan, killing 94.", "no_year_html": "An <a href=\"https://wikipedia.org/wiki/Imperial_Japanese_Army\" title=\"Imperial Japanese Army\">Imperial Japanese Army</a> ammunition dump explodes at <a href=\"https://wikipedia.org/wiki/Hirakata,_Osaka\" class=\"mw-redirect\" title=\"Hirakata, Osaka\">Hirakata, Osaka</a>, Japan, killing 94.", "links": [{"title": "Imperial Japanese Army", "link": "https://wikipedia.org/wiki/Imperial_Japanese_Army"}, {"title": "Hirakata, Osaka", "link": "https://wikipedia.org/wiki/Hirakata,_Osaka"}]}, {"year": "1941", "text": "World War II: Bulgaria signs the Tripartite Pact, allying itself with the Axis powers.", "html": "1941 - <a href=\"https://wikipedia.org/wiki/World_War_II\" title=\"World War II\">World War II</a>: <a href=\"https://wikipedia.org/wiki/Bulgaria\" title=\"Bulgaria\">Bulgaria</a> signs the <a href=\"https://wikipedia.org/wiki/Tripartite_Pact\" title=\"Tripartite Pact\">Tripartite Pact</a>, allying itself with the <a href=\"https://wikipedia.org/wiki/Axis_powers\" title=\"Axis powers\">Axis powers</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/World_War_II\" title=\"World War II\">World War II</a>: <a href=\"https://wikipedia.org/wiki/Bulgaria\" title=\"Bulgaria\">Bulgaria</a> signs the <a href=\"https://wikipedia.org/wiki/Tripartite_Pact\" title=\"Tripartite Pact\">Tripartite Pact</a>, allying itself with the <a href=\"https://wikipedia.org/wiki/Axis_powers\" title=\"Axis powers\">Axis powers</a>.", "links": [{"title": "World War II", "link": "https://wikipedia.org/wiki/World_War_II"}, {"title": "Bulgaria", "link": "https://wikipedia.org/wiki/Bulgaria"}, {"title": "Tripartite Pact", "link": "https://wikipedia.org/wiki/Tripartite_Pact"}, {"title": "Axis powers", "link": "https://wikipedia.org/wiki/Axis_powers"}]}, {"year": "1942", "text": "World War II: Japanese forces land on Java, the main island of the Dutch East Indies, at Merak and Banten Bay (Banten), Eretan Wetan (Indramayu) and Kragan (Rembang).", "html": "1942 - World War II: <a href=\"https://wikipedia.org/wiki/Empire_of_Japan\" title=\"Empire of Japan\">Japanese</a> forces land on <a href=\"https://wikipedia.org/wiki/Java\" title=\"Java\">Java</a>, the main island of the <a href=\"https://wikipedia.org/wiki/Dutch_East_Indies\" title=\"Dutch East Indies\">Dutch East Indies</a>, at <a href=\"https://wikipedia.org/wiki/Port_of_Merak\" title=\"Port of Merak\">Merak</a> and <a href=\"https://wikipedia.org/wiki/Banten_Bay\" title=\"Banten Bay\">Banten Bay</a> (<a href=\"https://wikipedia.org/wiki/Banten\" title=\"Banten\">Banten</a>), Eretan Wetan (<a href=\"https://wikipedia.org/wiki/Indramayu\" title=\"Indramayu\">Indramayu</a>) and Kragan (<a href=\"https://wikipedia.org/wiki/Rembang_Regency\" title=\"Rembang Regency\">Rembang</a>).", "no_year_html": "World War II: <a href=\"https://wikipedia.org/wiki/Empire_of_Japan\" title=\"Empire of Japan\">Japanese</a> forces land on <a href=\"https://wikipedia.org/wiki/Java\" title=\"Java\">Java</a>, the main island of the <a href=\"https://wikipedia.org/wiki/Dutch_East_Indies\" title=\"Dutch East Indies\">Dutch East Indies</a>, at <a href=\"https://wikipedia.org/wiki/Port_of_Merak\" title=\"Port of Merak\">Merak</a> and <a href=\"https://wikipedia.org/wiki/Banten_Bay\" title=\"Banten Bay\">Banten Bay</a> (<a href=\"https://wikipedia.org/wiki/Banten\" title=\"Banten\">Banten</a>), Eretan Wetan (<a href=\"https://wikipedia.org/wiki/Indramayu\" title=\"Indramayu\">Indramayu</a>) and Kragan (<a href=\"https://wikipedia.org/wiki/Rembang_Regency\" title=\"Rembang Regency\">Rembang</a>).", "links": [{"title": "Empire of Japan", "link": "https://wikipedia.org/wiki/Empire_of_Japan"}, {"title": "Java", "link": "https://wikipedia.org/wiki/Java"}, {"title": "Dutch East Indies", "link": "https://wikipedia.org/wiki/Dutch_East_Indies"}, {"title": "Port of Merak", "link": "https://wikipedia.org/wiki/Port_of_Merak"}, {"title": "Banten Bay", "link": "https://wikipedia.org/wiki/Banten_Bay"}, {"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>ten"}, {"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Indramayu"}, {"title": "Rembang Regency", "link": "https://wikipedia.org/wiki/Rembang_Regency"}]}, {"year": "1946", "text": "The Bank of England is nationalised.", "html": "1946 - The <a href=\"https://wikipedia.org/wiki/Bank_of_England\" title=\"Bank of England\">Bank of England</a> is nationalised.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Bank_of_England\" title=\"Bank of England\">Bank of England</a> is nationalised.", "links": [{"title": "Bank of England", "link": "https://wikipedia.org/wiki/Bank_of_England"}]}, {"year": "1947", "text": "The International Monetary Fund begins financial operations.", "html": "1947 - The <a href=\"https://wikipedia.org/wiki/International_Monetary_Fund\" title=\"International Monetary Fund\">International Monetary Fund</a> begins financial operations.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/International_Monetary_Fund\" title=\"International Monetary Fund\">International Monetary Fund</a> begins financial operations.", "links": [{"title": "International Monetary Fund", "link": "https://wikipedia.org/wiki/International_Monetary_Fund"}]}, {"year": "1950", "text": "Cold War: <PERSON> is convicted of spying for the Soviet Union by disclosing top secret atomic bomb data.", "html": "1950 - <a href=\"https://wikipedia.org/wiki/Cold_War\" title=\"Cold War\">Cold War</a>: <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> is convicted of spying for the <a href=\"https://wikipedia.org/wiki/Soviet_Union\" title=\"Soviet Union\">Soviet Union</a> by disclosing top secret <a href=\"https://wikipedia.org/wiki/Atomic_bomb\" class=\"mw-redirect\" title=\"Atomic bomb\">atomic bomb</a> data.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Cold_War\" title=\"Cold War\">Cold War</a>: <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>\" title=\"<PERSON>\"><PERSON></a> is convicted of spying for the <a href=\"https://wikipedia.org/wiki/Soviet_Union\" title=\"Soviet Union\">Soviet Union</a> by disclosing top secret <a href=\"https://wikipedia.org/wiki/Atomic_bomb\" class=\"mw-redirect\" title=\"Atomic bomb\">atomic bomb</a> data.", "links": [{"title": "Cold War", "link": "https://wikipedia.org/wiki/Cold_War"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Soviet Union", "link": "https://wikipedia.org/wiki/Soviet_Union"}, {"title": "Atomic bomb", "link": "https://wikipedia.org/wiki/Atomic_bomb"}]}, {"year": "1953", "text": "Soviet Premier <PERSON> suffers a stroke and collapses; he dies four days later.", "html": "1953 - Soviet Premier <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> suffers a stroke and collapses; he dies four days later.", "no_year_html": "Soviet Premier <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> suffers a stroke and collapses; he dies four days later.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1954", "text": "Nuclear weapons testing: The Castle Bravo, a 15-megaton hydrogen bomb, is detonated on Bikini Atoll in the Pacific Ocean, resulting in the worst radioactive contamination ever caused by the United States.", "html": "1954 - <a href=\"https://wikipedia.org/wiki/Nuclear_weapons_testing\" title=\"Nuclear weapons testing\">Nuclear weapons testing</a>: The <a href=\"https://wikipedia.org/wiki/Castle_Bravo\" title=\"Castle Bravo\">Castle Bravo</a>, a 15-<a href=\"https://wikipedia.org/wiki/TNT_equivalent\" title=\"TNT equivalent\">megaton</a> <a href=\"https://wikipedia.org/wiki/Hydrogen_bomb\" class=\"mw-redirect\" title=\"Hydrogen bomb\">hydrogen bomb</a>, is detonated on <a href=\"https://wikipedia.org/wiki/Bikini_Atoll\" title=\"Bikini Atoll\">Bikini Atoll</a> in the Pacific Ocean, resulting in the worst <a href=\"https://wikipedia.org/wiki/Radioactive_contamination\" title=\"Radioactive contamination\">radioactive contamination</a> ever caused by the United States.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Nuclear_weapons_testing\" title=\"Nuclear weapons testing\">Nuclear weapons testing</a>: The <a href=\"https://wikipedia.org/wiki/Castle_Bravo\" title=\"Castle Bravo\">Castle Bravo</a>, a 15-<a href=\"https://wikipedia.org/wiki/TNT_equivalent\" title=\"TNT equivalent\">megaton</a> <a href=\"https://wikipedia.org/wiki/Hydrogen_bomb\" class=\"mw-redirect\" title=\"Hydrogen bomb\">hydrogen bomb</a>, is detonated on <a href=\"https://wikipedia.org/wiki/Bikini_Atoll\" title=\"Bikini Atoll\">Bikini Atoll</a> in the Pacific Ocean, resulting in the worst <a href=\"https://wikipedia.org/wiki/Radioactive_contamination\" title=\"Radioactive contamination\">radioactive contamination</a> ever caused by the United States.", "links": [{"title": "Nuclear weapons testing", "link": "https://wikipedia.org/wiki/Nuclear_weapons_testing"}, {"title": "Castle Bravo", "link": "https://wikipedia.org/wiki/Castle_Bravo"}, {"title": "TNT equivalent", "link": "https://wikipedia.org/wiki/TNT_equivalent"}, {"title": "Hydrogen bomb", "link": "https://wikipedia.org/wiki/Hydrogen_bomb"}, {"title": "Bikini Atoll", "link": "https://wikipedia.org/wiki/Bikini_Atoll"}, {"title": "Radioactive contamination", "link": "https://wikipedia.org/wiki/Radioactive_contamination"}]}, {"year": "1954", "text": "Armed Puerto Rican nationalists attack the United States Capitol building, injuring five Representatives.", "html": "1954 - <a href=\"https://wikipedia.org/wiki/Independence_movement_in_Puerto_Rico\" title=\"Independence movement in Puerto Rico\">Armed Puerto Rican nationalists</a> attack the <a href=\"https://wikipedia.org/wiki/United_States_Capitol\" title=\"United States Capitol\">United States Capitol</a> building, <a href=\"https://wikipedia.org/wiki/1954_United_States_Capitol_shooting\" title=\"1954 United States Capitol shooting\">injuring five Representatives</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Independence_movement_in_Puerto_Rico\" title=\"Independence movement in Puerto Rico\">Armed Puerto Rican nationalists</a> attack the <a href=\"https://wikipedia.org/wiki/United_States_Capitol\" title=\"United States Capitol\">United States Capitol</a> building, <a href=\"https://wikipedia.org/wiki/1954_United_States_Capitol_shooting\" title=\"1954 United States Capitol shooting\">injuring five Representatives</a>.", "links": [{"title": "Independence movement in Puerto Rico", "link": "https://wikipedia.org/wiki/Independence_movement_in_Puerto_Rico"}, {"title": "United States Capitol", "link": "https://wikipedia.org/wiki/United_States_Capitol"}, {"title": "1954 United States Capitol shooting", "link": "https://wikipedia.org/wiki/1954_United_States_Capitol_shooting"}]}, {"year": "1956", "text": "The International Air Transport Association finalizes a draft of the Radiotelephony spelling alphabet for the International Civil Aviation Organization.", "html": "1956 - The <a href=\"https://wikipedia.org/wiki/International_Air_Transport_Association\" title=\"International Air Transport Association\">International Air Transport Association</a> finalizes a draft of the <a href=\"https://wikipedia.org/wiki/Radiotelephony_spelling_alphabet\" class=\"mw-redirect\" title=\"Radiotelephony spelling alphabet\">Radiotelephony spelling alphabet</a> for the <a href=\"https://wikipedia.org/wiki/International_Civil_Aviation_Organization\" title=\"International Civil Aviation Organization\">International Civil Aviation Organization</a>.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/International_Air_Transport_Association\" title=\"International Air Transport Association\">International Air Transport Association</a> finalizes a draft of the <a href=\"https://wikipedia.org/wiki/Radiotelephony_spelling_alphabet\" class=\"mw-redirect\" title=\"Radiotelephony spelling alphabet\">Radiotelephony spelling alphabet</a> for the <a href=\"https://wikipedia.org/wiki/International_Civil_Aviation_Organization\" title=\"International Civil Aviation Organization\">International Civil Aviation Organization</a>.", "links": [{"title": "International Air Transport Association", "link": "https://wikipedia.org/wiki/International_Air_Transport_Association"}, {"title": "Radiotelephony spelling alphabet", "link": "https://wikipedia.org/wiki/Radiotelephony_spelling_alphabet"}, {"title": "International Civil Aviation Organization", "link": "https://wikipedia.org/wiki/International_Civil_Aviation_Organization"}]}, {"year": "1956", "text": "Formation of the East German Nationale Volksarmee.", "html": "1956 - Formation of the <a href=\"https://wikipedia.org/wiki/East_Germany\" title=\"East Germany\">East German</a> <a href=\"https://wikipedia.org/wiki/National_People%27s_Army\" title=\"National People's Army\">Nationale Volksarmee</a>.", "no_year_html": "Formation of the <a href=\"https://wikipedia.org/wiki/East_Germany\" title=\"East Germany\">East German</a> <a href=\"https://wikipedia.org/wiki/National_People%27s_Army\" title=\"National People's Army\">Nationale Volksarmee</a>.", "links": [{"title": "East Germany", "link": "https://wikipedia.org/wiki/East_Germany"}, {"title": "National People's Army", "link": "https://wikipedia.org/wiki/National_People%27s_Army"}]}, {"year": "1958", "text": "<PERSON> is appointed Pro-Prefect of the Propagation of Faith and thus becomes the first U.S. member of the Roman Curia.", "html": "1958 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> is appointed <a href=\"https://wikipedia.org/wiki/Pro-Prefect\" class=\"mw-redirect\" title=\"Pro-Prefect\">Pro-Prefect</a> of the <a href=\"https://wikipedia.org/wiki/Propagation_of_Faith\" class=\"mw-redirect\" title=\"Propagation of Faith\">Propagation of Faith</a> and thus becomes the first U.S. member of the <a href=\"https://wikipedia.org/wiki/Roman_Curia\" title=\"Roman Curia\">Roman Curia</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> is appointed <a href=\"https://wikipedia.org/wiki/Pro-Prefect\" class=\"mw-redirect\" title=\"Pro-Prefect\">Pro-Prefect</a> of the <a href=\"https://wikipedia.org/wiki/Propagation_of_Faith\" class=\"mw-redirect\" title=\"Propagation of Faith\">Propagation of Faith</a> and thus becomes the first U.S. member of the <a href=\"https://wikipedia.org/wiki/Roman_Curia\" title=\"Roman Curia\">Roman Curia</a>.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Pro-Prefect", "link": "https://wikipedia.org/wiki/Pro-Prefect"}, {"title": "Propagation of Faith", "link": "https://wikipedia.org/wiki/Propagation_of_Faith"}, {"title": "Roman Curia", "link": "https://wikipedia.org/wiki/Roman_Curia"}]}, {"year": "1961", "text": "Uganda becomes self-governing and holds its first elections.", "html": "1961 - <a href=\"https://wikipedia.org/wiki/Uganda\" title=\"Uganda\">Uganda</a> becomes self-governing and holds its first elections.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Uganda\" title=\"Uganda\">Uganda</a> becomes self-governing and holds its first elections.", "links": [{"title": "Uganda", "link": "https://wikipedia.org/wiki/Uganda"}]}, {"year": "1962", "text": "American Airlines Flight 1 crashes into Jamaica Bay in New York, killing 95.", "html": "1962 - <a href=\"https://wikipedia.org/wiki/American_Airlines_Flight_1_(1962)\" title=\"American Airlines Flight 1 (1962)\">American Airlines Flight 1</a> crashes into <a href=\"https://wikipedia.org/wiki/Jamaica_Bay\" title=\"Jamaica Bay\">Jamaica Bay</a> in <a href=\"https://wikipedia.org/wiki/New_York_(state)\" title=\"New York (state)\">New York</a>, killing 95.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/American_Airlines_Flight_1_(1962)\" title=\"American Airlines Flight 1 (1962)\">American Airlines Flight 1</a> crashes into <a href=\"https://wikipedia.org/wiki/Jamaica_Bay\" title=\"Jamaica Bay\">Jamaica Bay</a> in <a href=\"https://wikipedia.org/wiki/New_York_(state)\" title=\"New York (state)\">New York</a>, killing 95.", "links": [{"title": "American Airlines Flight 1 (1962)", "link": "https://wikipedia.org/wiki/American_Airlines_Flight_1_(1962)"}, {"title": "Jamaica Bay", "link": "https://wikipedia.org/wiki/Jamaica_Bay"}, {"title": "New York (state)", "link": "https://wikipedia.org/wiki/New_York_(state)"}]}, {"year": "1964", "text": "Villarrica Volcano begins a strombolian eruption causing lahars that destroy half of the town of Coñaripe.", "html": "1964 - <a href=\"https://wikipedia.org/wiki/Villarrica_(volcano)\" title=\"Villarrica (volcano)\">Villarrica Volcano</a> begins a <a href=\"https://wikipedia.org/wiki/Strombolian_eruption\" title=\"Strombolian eruption\">strombolian eruption</a> causing <a href=\"https://wikipedia.org/wiki/Lahar\" title=\"Lahar\">lahars</a> that destroy half of the town of <a href=\"https://wikipedia.org/wiki/Co%C3%B1aripe\" title=\"Coñaripe\">Coñaripe</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Villarrica_(volcano)\" title=\"Villarrica (volcano)\">Villarrica Volcano</a> begins a <a href=\"https://wikipedia.org/wiki/Strombolian_eruption\" title=\"Strombolian eruption\">strombolian eruption</a> causing <a href=\"https://wikipedia.org/wiki/Lahar\" title=\"Lahar\">lahars</a> that destroy half of the town of <a href=\"https://wikipedia.org/wiki/Co%C3%B1aripe\" title=\"Coñaripe\">Coñaripe</a>.", "links": [{"title": "Villarrica (volcano)", "link": "https://wikipedia.org/wiki/Villarrica_(volcano)"}, {"title": "Strombolian eruption", "link": "https://wikipedia.org/wiki/Strombolian_eruption"}, {"title": "Lahar", "link": "https://wikipedia.org/wiki/Lahar"}, {"title": "Coñaripe", "link": "https://wikipedia.org/wiki/Co%C3%B1aripe"}]}, {"year": "1964", "text": "Paradise Airlines Flight 901A crashes near Lake Tahoe, Nevada, killing 85.", "html": "1964 - <a href=\"https://wikipedia.org/wiki/Paradise_Airlines_Flight_901A\" title=\"Paradise Airlines Flight 901A\">Paradise Airlines Flight 901A</a> crashes near <a href=\"https://wikipedia.org/wiki/Lake_Tahoe\" title=\"Lake Tahoe\">Lake Tahoe</a>, Nevada, killing 85.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Paradise_Airlines_Flight_901A\" title=\"Paradise Airlines Flight 901A\">Paradise Airlines Flight 901A</a> crashes near <a href=\"https://wikipedia.org/wiki/Lake_Tahoe\" title=\"Lake Tahoe\">Lake Tahoe</a>, Nevada, killing 85.", "links": [{"title": "Paradise Airlines Flight 901A", "link": "https://wikipedia.org/wiki/Paradise_Airlines_Flight_901A"}, {"title": "Lake Tahoe", "link": "https://wikipedia.org/wiki/Lake_Tahoe"}]}, {"year": "1966", "text": "Venera 3 Soviet space probe crashes on Venus becoming the first spacecraft to land on another planet's surface.", "html": "1966 - <a href=\"https://wikipedia.org/wiki/Venera_3\" title=\"Venera 3\">Venera 3</a> <a href=\"https://wikipedia.org/wiki/Soviet_Union\" title=\"Soviet Union\">Soviet</a> <a href=\"https://wikipedia.org/wiki/Space_probe\" class=\"mw-redirect\" title=\"Space probe\">space probe</a> crashes on <a href=\"https://wikipedia.org/wiki/Venus\" title=\"Venus\">Venus</a> becoming the first <a href=\"https://wikipedia.org/wiki/Spacecraft\" title=\"Spacecraft\">spacecraft</a> to land on another <a href=\"https://wikipedia.org/wiki/Planet\" title=\"Planet\">planet</a>'s surface.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Venera_3\" title=\"Venera 3\">Venera 3</a> <a href=\"https://wikipedia.org/wiki/Soviet_Union\" title=\"Soviet Union\">Soviet</a> <a href=\"https://wikipedia.org/wiki/Space_probe\" class=\"mw-redirect\" title=\"Space probe\">space probe</a> crashes on <a href=\"https://wikipedia.org/wiki/Venus\" title=\"Venus\">Venus</a> becoming the first <a href=\"https://wikipedia.org/wiki/Spacecraft\" title=\"Spacecraft\">spacecraft</a> to land on another <a href=\"https://wikipedia.org/wiki/Planet\" title=\"Planet\">planet</a>'s surface.", "links": [{"title": "Venera 3", "link": "https://wikipedia.org/wiki/Venera_3"}, {"title": "Soviet Union", "link": "https://wikipedia.org/wiki/Soviet_Union"}, {"title": "Space probe", "link": "https://wikipedia.org/wiki/Space_probe"}, {"title": "Venus", "link": "https://wikipedia.org/wiki/Venus"}, {"title": "Spacecraft", "link": "https://wikipedia.org/wiki/Spacecraft"}, {"title": "Planet", "link": "https://wikipedia.org/wiki/Planet"}]}, {"year": "1966", "text": "The Ba'ath Party takes power in Syria.", "html": "1966 - The <a href=\"https://wikipedia.org/wiki/Ba%27ath_Party\" title=\"Ba'ath Party\">Ba'ath Party</a> takes power in <a href=\"https://wikipedia.org/wiki/Syria\" title=\"Syria\">Syria</a>.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Ba%27ath_Party\" title=\"Ba'ath Party\">Ba'ath Party</a> takes power in <a href=\"https://wikipedia.org/wiki/Syria\" title=\"Syria\">Syria</a>.", "links": [{"title": "Ba'ath Party", "link": "https://wikipedia.org/wiki/Ba%27ath_Party"}, {"title": "Syria", "link": "https://wikipedia.org/wiki/Syria"}]}, {"year": "1971", "text": "President of Pakistan <PERSON><PERSON> indefinitely postpones the pending national assembly session, precipitating massive civil disobedience in East Pakistan.", "html": "1971 - <a href=\"https://wikipedia.org/wiki/President_of_Pakistan\" title=\"President of Pakistan\">President of Pakistan</a> <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a> indefinitely postpones the pending national assembly session, precipitating massive civil disobedience in <a href=\"https://wikipedia.org/wiki/East_Pakistan\" title=\"East Pakistan\">East Pakistan</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/President_of_Pakistan\" title=\"President of Pakistan\">President of Pakistan</a> <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a> indefinitely postpones the pending national assembly session, precipitating massive civil disobedience in <a href=\"https://wikipedia.org/wiki/East_Pakistan\" title=\"East Pakistan\">East Pakistan</a>.", "links": [{"title": "President of Pakistan", "link": "https://wikipedia.org/wiki/President_of_Pakistan"}, {"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}, {"title": "East Pakistan", "link": "https://wikipedia.org/wiki/East_Pakistan"}]}, {"year": "1973", "text": "Black September storms the Saudi embassy in Khartoum, Sudan, resulting in the assassination of three Western hostages.", "html": "1973 - <a href=\"https://wikipedia.org/wiki/Black_September_(group)\" class=\"mw-redirect\" title=\"Black September (group)\">Black September</a> storms the <a href=\"https://wikipedia.org/wiki/Saudi_Arabia\" title=\"Saudi Arabia\">Saudi</a> embassy in <a href=\"https://wikipedia.org/wiki/Khartoum\" title=\"Khartoum\">Khartoum</a>, <a href=\"https://wikipedia.org/wiki/Sudan\" title=\"Sudan\">Sudan</a>, resulting in the <a href=\"https://wikipedia.org/wiki/Attack_on_the_Saudi_Embassy_in_Khartoum\" title=\"Attack on the Saudi Embassy in Khartoum\">assassination of three Western hostages</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Black_September_(group)\" class=\"mw-redirect\" title=\"Black September (group)\">Black September</a> storms the <a href=\"https://wikipedia.org/wiki/Saudi_Arabia\" title=\"Saudi Arabia\">Saudi</a> embassy in <a href=\"https://wikipedia.org/wiki/Khartoum\" title=\"Khartoum\">Khartoum</a>, <a href=\"https://wikipedia.org/wiki/Sudan\" title=\"Sudan\">Sudan</a>, resulting in the <a href=\"https://wikipedia.org/wiki/Attack_on_the_Saudi_Embassy_in_Khartoum\" title=\"Attack on the Saudi Embassy in Khartoum\">assassination of three Western hostages</a>.", "links": [{"title": "Black September (group)", "link": "https://wikipedia.org/wiki/Black_September_(group)"}, {"title": "Saudi Arabia", "link": "https://wikipedia.org/wiki/Saudi_Arabia"}, {"title": "Khartoum", "link": "https://wikipedia.org/wiki/Khartoum"}, {"title": "Sudan", "link": "https://wikipedia.org/wiki/Sudan"}, {"title": "Attack on the Saudi Embassy in Khartoum", "link": "https://wikipedia.org/wiki/Attack_on_the_Saudi_Embassy_in_Khartoum"}]}, {"year": "1974", "text": "Watergate scandal: Seven are indicted for their role in the Watergate break-in and charged with conspiracy to obstruct justice.", "html": "1974 - <a href=\"https://wikipedia.org/wiki/Watergate_scandal\" title=\"Watergate scandal\">Watergate scandal</a>: Seven are indicted for their role in the Watergate break-in and charged with conspiracy to obstruct justice.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Watergate_scandal\" title=\"Watergate scandal\">Watergate scandal</a>: Seven are indicted for their role in the Watergate break-in and charged with conspiracy to obstruct justice.", "links": [{"title": "Watergate scandal", "link": "https://wikipedia.org/wiki/Watergate_scandal"}]}, {"year": "1981", "text": "Provisional Irish Republican Army member <PERSON> begins his hunger strike in HM Prison Maze.", "html": "1981 - <a href=\"https://wikipedia.org/wiki/Provisional_Irish_Republican_Army\" title=\"Provisional Irish Republican Army\">Provisional Irish Republican Army</a> member <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> begins his <a href=\"https://wikipedia.org/wiki/1981_Irish_hunger_strike\" title=\"1981 Irish hunger strike\">hunger strike</a> in <a href=\"https://wikipedia.org/wiki/HM_Prison_Maze\" title=\"HM Prison Maze\">HM Prison Maze</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Provisional_Irish_Republican_Army\" title=\"Provisional Irish Republican Army\">Provisional Irish Republican Army</a> member <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> begins his <a href=\"https://wikipedia.org/wiki/1981_Irish_hunger_strike\" title=\"1981 Irish hunger strike\">hunger strike</a> in <a href=\"https://wikipedia.org/wiki/HM_Prison_Maze\" title=\"HM Prison Maze\">HM Prison Maze</a>.", "links": [{"title": "Provisional Irish Republican Army", "link": "https://wikipedia.org/wiki/Provisional_Irish_Republican_Army"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "1981 Irish hunger strike", "link": "https://wikipedia.org/wiki/1981_Irish_hunger_strike"}, {"title": "HM Prison Maze", "link": "https://wikipedia.org/wiki/HM_Prison_Maze"}]}, {"year": "1990", "text": "<PERSON> is raided by the United States Secret Service, prompting the later formation of the Electronic Frontier Foundation.", "html": "1990 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"Steve <PERSON> Games\"><PERSON></a> is raided by the <a href=\"https://wikipedia.org/wiki/United_States_Secret_Service\" title=\"United States Secret Service\">United States Secret Service</a>, prompting the later formation of the <a href=\"https://wikipedia.org/wiki/Electronic_Frontier_Foundation\" title=\"Electronic Frontier Foundation\">Electronic Frontier Foundation</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"Steve <PERSON> Games\"><PERSON></a> is raided by the <a href=\"https://wikipedia.org/wiki/United_States_Secret_Service\" title=\"United States Secret Service\">United States Secret Service</a>, prompting the later formation of the <a href=\"https://wikipedia.org/wiki/Electronic_Frontier_Foundation\" title=\"Electronic Frontier Foundation\">Electronic Frontier Foundation</a>.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "United States Secret Service", "link": "https://wikipedia.org/wiki/United_States_Secret_Service"}, {"title": "Electronic Frontier Foundation", "link": "https://wikipedia.org/wiki/Electronic_Frontier_Foundation"}]}, {"year": "1991", "text": "Uprisings against <PERSON> begin in Iraq, leading to the deaths of more than 25,000 people, mostly civilians.", "html": "1991 - <a href=\"https://wikipedia.org/wiki/1991_Iraqi_uprisings\" title=\"1991 Iraqi uprisings\">Uprisings</a> against <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> begin in Iraq, leading to the deaths of more than 25,000 people, mostly civilians.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/1991_Iraqi_uprisings\" title=\"1991 Iraqi uprisings\">Uprisings</a> against <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> begin in Iraq, leading to the deaths of more than 25,000 people, mostly civilians.", "links": [{"title": "1991 Iraqi uprisings", "link": "https://wikipedia.org/wiki/1991_Iraqi_uprisings"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1992", "text": "Bosnia and Herzegovina declares its independence from Socialist Federal Republic of Yugoslavia.", "html": "1992 - <a href=\"https://wikipedia.org/wiki/Bosnia_and_Herzegovina\" title=\"Bosnia and Herzegovina\">Bosnia and Herzegovina</a> declares its independence from <a href=\"https://wikipedia.org/wiki/Socialist_Federal_Republic_of_Yugoslavia\" title=\"Socialist Federal Republic of Yugoslavia\">Socialist Federal Republic of Yugoslavia</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Bosnia_and_Herzegovina\" title=\"Bosnia and Herzegovina\">Bosnia and Herzegovina</a> declares its independence from <a href=\"https://wikipedia.org/wiki/Socialist_Federal_Republic_of_Yugoslavia\" title=\"Socialist Federal Republic of Yugoslavia\">Socialist Federal Republic of Yugoslavia</a>.", "links": [{"title": "Bosnia and Herzegovina", "link": "https://wikipedia.org/wiki/Bosnia_and_Herzegovina"}, {"title": "Socialist Federal Republic of Yugoslavia", "link": "https://wikipedia.org/wiki/Socialist_Federal_Republic_of_Yugoslavia"}]}, {"year": "1998", "text": "Titanic became the first film to gross over $1 billion worldwide.", "html": "1998 - <i><a href=\"https://wikipedia.org/wiki/Titanic_(1997_film)\" title=\"Titanic (1997 film)\">Titanic</a></i> became the first film to gross over <a href=\"https://wikipedia.org/wiki/List_of_highest-grossing_films\" title=\"List of highest-grossing films\">$1 billion worldwide</a>.", "no_year_html": "<i><a href=\"https://wikipedia.org/wiki/Titanic_(1997_film)\" title=\"Titanic (1997 film)\">Titanic</a></i> became the first film to gross over <a href=\"https://wikipedia.org/wiki/List_of_highest-grossing_films\" title=\"List of highest-grossing films\">$1 billion worldwide</a>.", "links": [{"title": "Titanic (1997 film)", "link": "https://wikipedia.org/wiki/Titanic_(1997_film)"}, {"title": "List of highest-grossing films", "link": "https://wikipedia.org/wiki/List_of_highest-grossing_films"}]}, {"year": "2002", "text": "U.S. invasion of Afghanistan: Operation Anaconda begins in eastern Afghanistan.", "html": "2002 - <a href=\"https://wikipedia.org/wiki/War_in_Afghanistan_(2001%E2%80%932021)\" title=\"War in Afghanistan (2001-2021)\">U.S. invasion of Afghanistan</a>: <a href=\"https://wikipedia.org/wiki/Operation_Anaconda\" title=\"Operation Anaconda\">Operation Anaconda</a> begins in eastern <a href=\"https://wikipedia.org/wiki/Afghanistan\" title=\"Afghanistan\">Afghanistan</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/War_in_Afghanistan_(2001%E2%80%932021)\" title=\"War in Afghanistan (2001-2021)\">U.S. invasion of Afghanistan</a>: <a href=\"https://wikipedia.org/wiki/Operation_Anaconda\" title=\"Operation Anaconda\">Operation Anaconda</a> begins in eastern <a href=\"https://wikipedia.org/wiki/Afghanistan\" title=\"Afghanistan\">Afghanistan</a>.", "links": [{"title": "War in Afghanistan (2001-2021)", "link": "https://wikipedia.org/wiki/War_in_Afghanistan_(2001%E2%80%932021)"}, {"title": "Operation Anaconda", "link": "https://wikipedia.org/wiki/Operation_Anaconda"}, {"title": "Afghanistan", "link": "https://wikipedia.org/wiki/Afghanistan"}]}, {"year": "2002", "text": "The Envisat environmental satellite successfully launches aboard an Ariane 5 rocket to reach an orbit of 800 km (500 mi) above the Earth, which was the then-largest payload at 10.5 m long and with a diameter of 4.57 m.", "html": "2002 - The <a href=\"https://wikipedia.org/wiki/Envisat\" title=\"Envisat\">Envisat</a> <a href=\"https://wikipedia.org/wiki/Environmental_satellite\" class=\"mw-redirect\" title=\"Environmental satellite\">environmental satellite</a> successfully launches aboard an <a href=\"https://wikipedia.org/wiki/Ariane_5#List_of_launches\" title=\"Ariane 5\">Ariane 5 rocket</a> to reach an orbit of 800 km (500 mi) above the Earth, which was the then-largest <a href=\"https://wikipedia.org/wiki/Payload_(air_and_space_craft)\" class=\"mw-redirect\" title=\"Payload (air and space craft)\">payload</a> at 10.5 m long and with a diameter of 4.57 m.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Envisat\" title=\"Envisat\">Envisat</a> <a href=\"https://wikipedia.org/wiki/Environmental_satellite\" class=\"mw-redirect\" title=\"Environmental satellite\">environmental satellite</a> successfully launches aboard an <a href=\"https://wikipedia.org/wiki/Ariane_5#List_of_launches\" title=\"Ariane 5\">Ariane 5 rocket</a> to reach an orbit of 800 km (500 mi) above the Earth, which was the then-largest <a href=\"https://wikipedia.org/wiki/Payload_(air_and_space_craft)\" class=\"mw-redirect\" title=\"Payload (air and space craft)\">payload</a> at 10.5 m long and with a diameter of 4.57 m.", "links": [{"title": "Envisat", "link": "https://wikipedia.org/wiki/Envisat"}, {"title": "Environmental satellite", "link": "https://wikipedia.org/wiki/Environmental_satellite"}, {"title": "Ariane 5", "link": "https://wikipedia.org/wiki/Ariane_5#List_of_launches"}, {"title": "Payload (air and space craft)", "link": "https://wikipedia.org/wiki/Payload_(air_and_space_craft)"}]}, {"year": "2002", "text": "Space Shuttle Columbia is launched on STS-109 to service the Hubble Space Telescope.", "html": "2002 - <a href=\"https://wikipedia.org/wiki/Space_Shuttle_Columbia\" title=\"Space Shuttle Columbia\">Space Shuttle <i>Columbia</i></a> is launched on <a href=\"https://wikipedia.org/wiki/STS-109\" title=\"STS-109\">STS-109</a> to service the <a href=\"https://wikipedia.org/wiki/Hubble_Space_Telescope\" title=\"Hubble Space Telescope\">Hubble Space Telescope</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Space_Shuttle_Columbia\" title=\"Space Shuttle Columbia\">Space Shuttle <i>Columbia</i></a> is launched on <a href=\"https://wikipedia.org/wiki/STS-109\" title=\"STS-109\">STS-109</a> to service the <a href=\"https://wikipedia.org/wiki/Hubble_Space_Telescope\" title=\"Hubble Space Telescope\">Hubble Space Telescope</a>.", "links": [{"title": "Space Shuttle Columbia", "link": "https://wikipedia.org/wiki/Space_Shuttle_Columbia"}, {"title": "STS-109", "link": "https://wikipedia.org/wiki/STS-109"}, {"title": "<PERSON>bble Space Telescope", "link": "https://wikipedia.org/wiki/Hubble_Space_Telescope"}]}, {"year": "2003", "text": "Management of the United States Customs Service and the United States Secret Service move to the United States Department of Homeland Security.", "html": "2003 - Management of the <a href=\"https://wikipedia.org/wiki/United_States_Customs_Service\" title=\"United States Customs Service\">United States Customs Service</a> and the <a href=\"https://wikipedia.org/wiki/United_States_Secret_Service\" title=\"United States Secret Service\">United States Secret Service</a> move to the <a href=\"https://wikipedia.org/wiki/United_States_Department_of_Homeland_Security\" title=\"United States Department of Homeland Security\">United States Department of Homeland Security</a>.", "no_year_html": "Management of the <a href=\"https://wikipedia.org/wiki/United_States_Customs_Service\" title=\"United States Customs Service\">United States Customs Service</a> and the <a href=\"https://wikipedia.org/wiki/United_States_Secret_Service\" title=\"United States Secret Service\">United States Secret Service</a> move to the <a href=\"https://wikipedia.org/wiki/United_States_Department_of_Homeland_Security\" title=\"United States Department of Homeland Security\">United States Department of Homeland Security</a>.", "links": [{"title": "United States Customs Service", "link": "https://wikipedia.org/wiki/United_States_Customs_Service"}, {"title": "United States Secret Service", "link": "https://wikipedia.org/wiki/United_States_Secret_Service"}, {"title": "United States Department of Homeland Security", "link": "https://wikipedia.org/wiki/United_States_Department_of_Homeland_Security"}]}, {"year": "2005", "text": "In <PERSON><PERSON> v<PERSON> <PERSON>, the U.S. Supreme Court rules that the execution of juveniles found guilty of any crime is unconstitutional.", "html": "2005 - In <i><a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_v<PERSON>_<PERSON>\" title=\"<PERSON><PERSON> v<PERSON>\"><PERSON><PERSON> v<PERSON></a></i>, the <a href=\"https://wikipedia.org/wiki/Supreme_Court_of_the_United_States\" title=\"Supreme Court of the United States\">U.S. Supreme Court</a> rules that the execution of juveniles found guilty of any crime is unconstitutional.", "no_year_html": "In <i><a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_v<PERSON>_<PERSON>\" title=\"<PERSON><PERSON> v<PERSON>\"><PERSON><PERSON> v<PERSON></a></i>, the <a href=\"https://wikipedia.org/wiki/Supreme_Court_of_the_United_States\" title=\"Supreme Court of the United States\">U.S. Supreme Court</a> rules that the execution of juveniles found guilty of any crime is unconstitutional.", "links": [{"title": "<PERSON><PERSON> v. Simmons", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_v._<PERSON>"}, {"title": "Supreme Court of the United States", "link": "https://wikipedia.org/wiki/Supreme_Court_of_the_United_States"}]}, {"year": "2006", "text": "English-language Wikipedia reaches its one millionth article, Jordanhill railway station.", "html": "2006 - English-language <a href=\"https://wikipedia.org/wiki/Wikipedia\" title=\"Wikipedia\">Wikipedia</a> reaches its one millionth article, <a href=\"https://wikipedia.org/wiki/Jordanhill_railway_station\" title=\"Jordanhill railway station\">Jordanhill railway station</a>.", "no_year_html": "English-language <a href=\"https://wikipedia.org/wiki/Wikipedia\" title=\"Wikipedia\">Wikipedia</a> reaches its one millionth article, <a href=\"https://wikipedia.org/wiki/Jordanhill_railway_station\" title=\"Jordanhill railway station\">Jordanhill railway station</a>.", "links": [{"title": "Wikipedia", "link": "https://wikipedia.org/wiki/Wikipedia"}, {"title": "Jordanhill railway station", "link": "https://wikipedia.org/wiki/Jordanhill_railway_station"}]}, {"year": "2007", "text": "Tornadoes break out across the southern United States, killing at least 20 people, including eight at Enterprise High School.", "html": "2007 - <a href=\"https://wikipedia.org/wiki/Tornado\" title=\"Tornado\">Tornadoes</a> <a href=\"https://wikipedia.org/wiki/February%E2%80%93March_2007_tornado_outbreak_sequence\" class=\"mw-redirect\" title=\"February-March 2007 tornado outbreak sequence\">break out across the southern United States</a>, killing at least 20 people, including eight at <a href=\"https://wikipedia.org/wiki/Enterprise_High_School_(Alabama)\" title=\"Enterprise High School (Alabama)\">Enterprise High School</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Tornado\" title=\"Tornado\">Tornadoes</a> <a href=\"https://wikipedia.org/wiki/February%E2%80%93March_2007_tornado_outbreak_sequence\" class=\"mw-redirect\" title=\"February-March 2007 tornado outbreak sequence\">break out across the southern United States</a>, killing at least 20 people, including eight at <a href=\"https://wikipedia.org/wiki/Enterprise_High_School_(Alabama)\" title=\"Enterprise High School (Alabama)\">Enterprise High School</a>.", "links": [{"title": "Tornado", "link": "https://wikipedia.org/wiki/Tornado"}, {"title": "February-March 2007 tornado outbreak sequence", "link": "https://wikipedia.org/wiki/February%E2%80%93March_2007_tornado_outbreak_sequence"}, {"title": "Enterprise High School (Alabama)", "link": "https://wikipedia.org/wiki/Enterprise_High_School_(Alabama)"}]}, {"year": "2008", "text": "The Armenian police clash with peaceful opposition rally protesting against allegedly fraudulent presidential elections, as a result ten people are killed.", "html": "2008 - The <a href=\"https://wikipedia.org/wiki/Police_of_Armenia\" title=\"Police of Armenia\">Armenian police</a> clash with peaceful opposition rally protesting against <a href=\"https://wikipedia.org/wiki/2008_Armenian_presidential_election\" title=\"2008 Armenian presidential election\">allegedly fraudulent presidential elections</a>, as a result ten people are killed.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Police_of_Armenia\" title=\"Police of Armenia\">Armenian police</a> clash with peaceful opposition rally protesting against <a href=\"https://wikipedia.org/wiki/2008_Armenian_presidential_election\" title=\"2008 Armenian presidential election\">allegedly fraudulent presidential elections</a>, as a result ten people are killed.", "links": [{"title": "Police of Armenia", "link": "https://wikipedia.org/wiki/Police_of_Armenia"}, {"title": "2008 Armenian presidential election", "link": "https://wikipedia.org/wiki/2008_Armenian_presidential_election"}]}, {"year": "2014", "text": "Thirty-five people are killed and 143 injured in a mass stabbing at Kunming Railway Station in China.", "html": "2014 - Thirty-five people are killed and 143 injured in a <a href=\"https://wikipedia.org/wiki/2014_Kunming_attack\" title=\"2014 Kunming attack\">mass stabbing</a> at <a href=\"https://wikipedia.org/wiki/Kunming_Railway_Station\" class=\"mw-redirect\" title=\"Kunming Railway Station\">Kunming Railway Station</a> in China.", "no_year_html": "Thirty-five people are killed and 143 injured in a <a href=\"https://wikipedia.org/wiki/2014_Kunming_attack\" title=\"2014 Kunming attack\">mass stabbing</a> at <a href=\"https://wikipedia.org/wiki/Kunming_Railway_Station\" class=\"mw-redirect\" title=\"Kunming Railway Station\">Kunming Railway Station</a> in China.", "links": [{"title": "2014 Kunming attack", "link": "https://wikipedia.org/wiki/2014_Kunming_attack"}, {"title": "Kunming Railway Station", "link": "https://wikipedia.org/wiki/Kunming_Railway_Station"}]}], "Births": [{"year": "1105", "text": "<PERSON>, king of León and Castile (d. 1157)", "html": "1105 - <a href=\"https://wikipedia.org/wiki/<PERSON>_VII_of_Le%C3%B3n\" class=\"mw-redirect\" title=\"<PERSON> of León\"><PERSON></a>, king of León and Castile (d. 1157)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_VII_of_Le%C3%B3n\" class=\"mw-redirect\" title=\"<PERSON> of León\"><PERSON> VII</a>, king of León and Castile (d. 1157)", "links": [{"title": "Alfonso VII of León", "link": "https://wikipedia.org/wiki/Alfonso_VII_of_Le%C3%B3n"}]}, {"year": "1389", "text": "<PERSON><PERSON> of Florence, Italian archbishop and saint (d. 1459)", "html": "1389 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_of_Florence\" title=\"<PERSON><PERSON> of Florence\"><PERSON><PERSON> of Florence</a>, Italian archbishop and saint (d. 1459)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_of_Florence\" title=\"<PERSON><PERSON> of Florence\"><PERSON><PERSON> of Florence</a>, Italian archbishop and saint (d. 1459)", "links": [{"title": "<PERSON><PERSON> of Florence", "link": "https://wikipedia.org/wiki/Antoninus_of_Florence"}]}, {"year": "1432", "text": "<PERSON> of Coimbra (d. 1455)", "html": "1432 - <a href=\"https://wikipedia.org/wiki/<PERSON>_of_Coimbra\" class=\"mw-redirect\" title=\"<PERSON> of Coimbra\"><PERSON> of Coimbra</a> (d. 1455)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_of_Coimbra\" class=\"mw-redirect\" title=\"<PERSON> of Coimbra\"><PERSON> of Coimbra</a> (d. 1455)", "links": [{"title": "<PERSON> of Coimbra", "link": "https://wikipedia.org/wiki/Isabella_of_Coimbra"}]}, {"year": "1456", "text": "<PERSON><PERSON><PERSON> of Hungary (d. 1516)", "html": "1456 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_II_of_Hungary\" title=\"<PERSON><PERSON><PERSON> II of Hungary\"><PERSON><PERSON><PERSON> of Hungary</a> (d. 1516)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_II_of_Hungary\" title=\"<PERSON><PERSON><PERSON> II of Hungary\"><PERSON><PERSON><PERSON> of Hungary</a> (d. 1516)", "links": [{"title": "<PERSON><PERSON><PERSON> of Hungary", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_II_of_Hungary"}]}, {"year": "1547", "text": "<PERSON>, German philosopher and lexicographer (d. 1628)", "html": "1547 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German philosopher and lexicographer (d. 1628)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German philosopher and lexicographer (d. 1628)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1554", "text": "<PERSON>, English courtier and conspirator (d. 1612)", "html": "1554 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(conspirator)\" title=\"<PERSON> (conspirator)\"><PERSON></a>, English courtier and conspirator (d. 1612)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(conspirator)\" title=\"<PERSON> (conspirator)\"><PERSON></a>, English courtier and conspirator (d. 1612)", "links": [{"title": "<PERSON> (conspirator)", "link": "https://wikipedia.org/wiki/<PERSON>_(conspirator)"}]}, {"year": "1577", "text": "<PERSON>, 1st Earl of Portland (d. 1635)", "html": "1577 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_1st_Earl_of_Portland\" title=\"<PERSON>, 1st Earl of Portland\"><PERSON>, 1st Earl of Portland</a> (d. 1635)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_1st_Earl_of_Portland\" title=\"<PERSON>, 1st Earl of Portland\"><PERSON>, 1st Earl of Portland</a> (d. 1635)", "links": [{"title": "<PERSON>, 1st Earl of Portland", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_1st_Earl_of_Portland"}]}, {"year": "1597", "text": "<PERSON><PERSON><PERSON>, Flemish priest and mathematician (d. 1652)", "html": "1597 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Flemish priest and mathematician (d. 1652)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Flemish priest and mathematician (d. 1652)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1611", "text": "<PERSON>, English mathematician and linguist (d. 1685)", "html": "1611 - <a href=\"https://wikipedia.org/wiki/<PERSON>_(mathematician)\" title=\"<PERSON> (mathematician)\"><PERSON></a>, English mathematician and linguist (d. 1685)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>(mathematician)\" title=\"<PERSON> (mathematician)\"><PERSON></a>, English mathematician and linguist (d. 1685)", "links": [{"title": "<PERSON> (mathematician)", "link": "https://wikipedia.org/wiki/<PERSON>(mathematician)"}]}, {"year": "1629", "text": "<PERSON>, Flemish painter (d. 1670)", "html": "1629 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Flemish painter (d. 1670)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Flemish painter (d. 1670)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1647", "text": "<PERSON>, Portuguese Jesuit missionary and martyr (d. 1693)", "html": "1647 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, Portuguese Jesuit missionary and martyr (d. 1693)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, Portuguese Jesuit missionary and martyr (d. 1693)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1657", "text": "<PERSON>, Swiss theologian and author (d. 1740)", "html": "1657 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swiss theologian and author (d. 1740)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swiss theologian and author (d. 1740)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1683", "text": "<PERSON><PERSON><PERSON>, sixth Dal<PERSON> (d. 1706)", "html": "1683 - <a href=\"https://wikipedia.org/wiki/6th_<PERSON><PERSON>_Lama\" title=\"6th Dalai Lama\"><PERSON><PERSON><PERSON></a>, sixth <PERSON><PERSON> (d. 1706)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/6th_<PERSON><PERSON>_Lama\" title=\"6th Dalai Lama\"><PERSON><PERSON><PERSON></a>, sixth <PERSON><PERSON> (d. 1706)", "links": [{"title": "6th <PERSON><PERSON> Lama", "link": "https://wikipedia.org/wiki/6th_<PERSON><PERSON>_<PERSON>"}]}, {"year": "1683", "text": "<PERSON> of Ansbach, British queen and regent (d. 1737)", "html": "1683 - <a href=\"https://wikipedia.org/wiki/<PERSON>_of_Ansbach\" title=\"<PERSON> of Ansbach\"><PERSON> of Ansbach</a>, British queen and regent (d. 1737)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_Ansbach\" title=\"<PERSON> of Ansbach\"><PERSON> of Ansbach</a>, British queen and regent (d. 1737)", "links": [{"title": "<PERSON> of Ansbach", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1724", "text": "<PERSON>, Portuguese prelate and antiquarian (d. 1814)", "html": "1724 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>%C3%A1culo\" title=\"Manuel do Cenáculo\"><PERSON>lo</a>, Portuguese prelate and antiquarian (d. 1814)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>%C3%A1culo\" title=\"Manuel do Cenáculo\"><PERSON>lo</a>, Portuguese prelate and antiquarian (d. 1814)", "links": [{"title": "<PERSON> do Cenáculo", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>%C3%A1culo"}]}, {"year": "1732", "text": "<PERSON>, American lawyer and judge (d. 1810)", "html": "1732 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and judge (d. 1810)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and judge (d. 1810)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1760", "text": "<PERSON>, French lawyer and politician (d. 1794)", "html": "1760 - <a href=\"https://wikipedia.org/wiki/Fran%C3%A7ois_Buzot\" title=\"<PERSON>\"><PERSON></a>, French lawyer and politician (d. 1794)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Fran%C3%A7ois_Buzot\" title=\"<PERSON>\"><PERSON></a>, French lawyer and politician (d. 1794)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Fran%C3%A7ois_Buzot"}]}, {"year": "1769", "text": "<PERSON>, French general (d. 1796)", "html": "1769 - <a href=\"https://wikipedia.org/wiki/Fran%C3%A7ois_S%C3%A9<PERSON><PERSON>_<PERSON>-<PERSON>gravier<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, French general (d. 1796)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Fran%C3%A7ois_S%C3%A9<PERSON><PERSON>_<PERSON>gravier<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, French general (d. 1796)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Fran%C3%A7ois_S%C3%A9<PERSON><PERSON>_<PERSON>-<PERSON>vier<PERSON>"}]}, {"year": "1807", "text": "<PERSON><PERSON><PERSON>, American religious leader, 4th President of The Church of Jesus Christ of Latter-day Saints (d. 1898)", "html": "1807 - <a href=\"https://wikipedia.org/wiki/W<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American religious leader, 4th <a href=\"https://wikipedia.org/wiki/President_of_The_Church_of_Jesus_Christ_of_Latter-day_Saints\" class=\"mw-redirect\" title=\"President of The Church of Jesus Christ of Latter-day Saints\">President of The Church of Jesus Christ of Latter-day Saints</a> (d. 1898)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/W<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American religious leader, 4th <a href=\"https://wikipedia.org/wiki/President_of_The_Church_of_Jesus_Christ_of_Latter-day_Saints\" class=\"mw-redirect\" title=\"President of The Church of Jesus Christ of Latter-day Saints\">President of The Church of Jesus Christ of Latter-day Saints</a> (d. 1898)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/W<PERSON><PERSON>_<PERSON>ruff"}, {"title": "President of The Church of Jesus Christ of Latter-day Saints", "link": "https://wikipedia.org/wiki/President_of_The_Church_of_Jesus_Christ_of_Latter-day_Saints"}]}, {"year": "1810", "text": "<PERSON><PERSON><PERSON><PERSON><PERSON>, Polish pianist and composer (d. 1849)", "html": "1810 - <a href=\"https://wikipedia.org/wiki/Fr%C3%A9d%C3%A9<PERSON>_<PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON><PERSON></a>, Polish pianist and composer (d. 1849)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Fr%C3%A9d%C3%A9<PERSON>_<PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON><PERSON></a>, Polish pianist and composer (d. 1849)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Fr%C3%A9d%C3%A9ric_Chopin"}]}, {"year": "1812", "text": "<PERSON>, English architect, co-designed the Palace of Westminster (d. 1852)", "html": "1812 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English architect, co-designed the <a href=\"https://wikipedia.org/wiki/Palace_of_Westminster\" title=\"Palace of Westminster\">Palace of Westminster</a> (d. 1852)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English architect, co-designed the <a href=\"https://wikipedia.org/wiki/Palace_of_Westminster\" title=\"Palace of Westminster\">Palace of Westminster</a> (d. 1852)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Palace of Westminster", "link": "https://wikipedia.org/wiki/Palace_of_Westminster"}]}, {"year": "1817", "text": "<PERSON>, Italian sculptor and educator (d. 1882)", "html": "1817 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>r%C3%A8\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, Italian sculptor and educator (d. 1882)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>r%C3%A8\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, Italian sculptor and educator (d. 1882)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Giovanni_Dupr%C3%A8"}]}, {"year": "1821", "text": "<PERSON>, German bishop and academic (d. 1896)", "html": "1821 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German bishop and academic (d. 1896)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German bishop and academic (d. 1896)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1835", "text": "<PERSON>, English-Australian politician, 12th Premier of Tasmania (d. 1919)", "html": "1835 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English-Australian politician, 12th <a href=\"https://wikipedia.org/wiki/Premier_of_Tasmania\" title=\"Premier of Tasmania\">Premier of Tasmania</a> (d. 1919)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English-Australian politician, 12th <a href=\"https://wikipedia.org/wiki/Premier_of_Tasmania\" title=\"Premier of Tasmania\">Premier of Tasmania</a> (d. 1919)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Premier of Tasmania", "link": "https://wikipedia.org/wiki/Premier_of_Tasmania"}]}, {"year": "1837", "text": "<PERSON>, American novelist, playwright, and critic (d. 1920)", "html": "1837 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American novelist, playwright, and critic (d. 1920)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American novelist, playwright, and critic (d. 1920)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1842", "text": "<PERSON><PERSON>, Greek painter and academic (d. 1901)", "html": "1842 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Greek painter and academic (d. 1901)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Greek painter and academic (d. 1901)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1848", "text": "<PERSON>, Irish-American sculptor and academic (d. 1907)", "html": "1848 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>-<PERSON>\" title=\"<PERSON> Saint<PERSON>Gaudens\"><PERSON></a>, Irish-American sculptor and academic (d. 1907)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>-<PERSON>\" title=\"<PERSON>Gaudens\"><PERSON></a>, Irish-American sculptor and academic (d. 1907)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Augustus_Saint-Gaudens"}]}, {"year": "1852", "text": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, French politician, French Minister of Foreign Affairs (d. 1923)", "html": "1852 - <a href=\"https://wikipedia.org/wiki/Th%C3%A9ophile_Delcass%C3%A9\" title=\"Théophile <PERSON>\">Théop<PERSON><PERSON></a>, French politician, <a href=\"https://wikipedia.org/wiki/Ministry_of_Foreign_and_European_Affairs_(France)\" class=\"mw-redirect\" title=\"Ministry of Foreign and European Affairs (France)\">French Minister of Foreign Affairs</a> (d. 1923)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Th%C3%A9ophile_Delcass%C3%A9\" title=\"Théophile <PERSON>\">Théop<PERSON><PERSON></a>, French politician, <a href=\"https://wikipedia.org/wiki/Ministry_of_Foreign_and_European_Affairs_(France)\" class=\"mw-redirect\" title=\"Ministry of Foreign and European Affairs (France)\">French Minister of Foreign Affairs</a> (d. 1923)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Th%C3%A9ophile_Delcass%C3%A9"}, {"title": "Ministry of Foreign and European Affairs (France)", "link": "https://wikipedia.org/wiki/Ministry_of_Foreign_and_European_Affairs_(France)"}]}, {"year": "1863", "text": "<PERSON>, Russian painter and set designer (d. 1930)", "html": "1863 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(artist)\" title=\"<PERSON> (artist)\"><PERSON></a>, Russian painter and set designer (d. 1930)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(artist)\" title=\"<PERSON> (artist)\"><PERSON></a>, Russian painter and set designer (d. 1930)", "links": [{"title": "<PERSON> (artist)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(artist)"}]}, {"year": "1870", "text": "<PERSON><PERSON> <PERSON><PERSON>, Greek-French astronomer and academic (d. 1944)", "html": "1870 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> <PERSON><PERSON>\"><PERSON><PERSON> <PERSON><PERSON></a>, Greek-French astronomer and academic (d. 1944)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"E<PERSON> <PERSON><PERSON>\"><PERSON><PERSON> <PERSON><PERSON></a>, Greek-French astronomer and academic (d. 1944)", "links": [{"title": "<PERSON><PERSON> <PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_Antoniadi"}]}, {"year": "1876", "text": "<PERSON>, Belgian businessman (d. 1942)", "html": "1876 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Belgian businessman (d. 1942)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Belgian businessman (d. 1942)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>-<PERSON>"}]}, {"year": "1880", "text": "<PERSON><PERSON><PERSON>, British writer and critic (d. 1932)", "html": "1880 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_Strachey\" title=\"<PERSON><PERSON><PERSON> Strachey\"><PERSON><PERSON><PERSON></a>, British writer and critic (d. 1932)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_Strachey\" title=\"<PERSON><PERSON><PERSON> Strachey\"><PERSON><PERSON><PERSON></a>, British writer and critic (d. 1932)", "links": [{"title": "<PERSON><PERSON><PERSON> Strachey", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_Strachey"}]}, {"year": "1886", "text": "<PERSON><PERSON>, Austrian-Swiss painter, poet, and playwright (d. 1980)", "html": "1886 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Austrian-Swiss painter, poet, and playwright (d. 1980)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Austrian-Swiss painter, poet, and playwright (d. 1980)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1888", "text": "<PERSON><PERSON>, English cricketer and billiards player (d. 1948)", "html": "1888 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, English cricketer and billiards player (d. 1948)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, English cricketer and billiards player (d. 1948)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/E<PERSON>_<PERSON>l"}]}, {"year": "1888", "text": "<PERSON>, English cricketer and umpire, international footballer (d. 1949)", "html": "1888 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English cricketer and umpire, international footballer (d. 1949)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English cricketer and umpire, international footballer (d. 1949)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1889", "text": "<PERSON><PERSON><PERSON>, Japanese historian and philosopher (d. 1960)", "html": "1889 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>atsuji\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese historian and philosopher (d. 1960)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese historian and philosopher (d. 1960)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>ji"}]}, {"year": "1890", "text": "<PERSON>, Polish-American painter and author (d. 2002)", "html": "1890 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Polish-American painter and author (d. 2002)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Polish-American painter and author (d. 2002)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1891", "text": "<PERSON>, Austrian-American hotelier (d. 1940)", "html": "1891 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Austrian-American hotelier (d. 1940)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Austrian-American hotelier (d. 1940)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1892", "text": "<PERSON><PERSON><PERSON><PERSON><PERSON>, Japanese author and educator (d. 1927)", "html": "1892 - <a href=\"https://wikipedia.org/wiki/Ry%C5%<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON><PERSON></a>, Japanese author and educator (d. 1927)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Ry%C5%<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON><PERSON></a>, Japanese author and educator (d. 1927)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Ry%C5%<PERSON><PERSON><PERSON>_<PERSON>awa"}]}, {"year": "1893", "text": "<PERSON>, American author, poet, and playwright (d. 1968)", "html": "1893 - <a href=\"https://wikipedia.org/wiki/<PERSON>_de_Acosta\" title=\"Mercedes de Acosta\"><PERSON> Acosta</a>, American author, poet, and playwright (d. 1968)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_de_Acosta\" title=\"Mercedes de Acosta\"><PERSON> Acos<PERSON></a>, American author, poet, and playwright (d. 1968)", "links": [{"title": "Mercedes de Acosta", "link": "https://wikipedia.org/wiki/Mercedes_de_Acosta"}]}, {"year": "1896", "text": "<PERSON>, Greek pianist, composer, and conductor (d. 1960)", "html": "1896 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Greek pianist, composer, and conductor (d. 1960)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Greek pianist, composer, and conductor (d. 1960)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1896", "text": "<PERSON><PERSON>, German playwright and producer (d. 1942)", "html": "1896 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, German playwright and producer (d. 1942)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, German playwright and producer (d. 1942)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>ler"}]}, {"year": "1899", "text": "<PERSON>, German SS officer (d. 1972)", "html": "1899 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German <a href=\"https://wikipedia.org/wiki/SS\" class=\"mw-redirect\" title=\"SS\">SS</a> officer (d. 1972)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German <a href=\"https://wikipedia.org/wiki/SS\" class=\"mw-redirect\" title=\"SS\">SS</a> officer (d. 1972)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>"}, {"title": "SS", "link": "https://wikipedia.org/wiki/SS"}]}, {"year": "1900", "text": "<PERSON>, British poet (d. 1985)", "html": "1900 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Bunting\"><PERSON></a>, British poet (d. 1985)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Bunting\"><PERSON></a>, British poet (d. 1985)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1904", "text": "<PERSON>, American actor, singer, and dancer (d. 1973)", "html": "1904 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor, singer, and dancer (d. 1973)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor, singer, and dancer (d. 1973)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1904", "text": "<PERSON>, American trombonist, composer, and bandleader (d. 1944)", "html": "1904 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American trombonist, composer, and bandleader (d. 1944)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American trombonist, composer, and bandleader (d. 1944)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1905", "text": "<PERSON>, Welsh-English actress, singer, and dancer (d. 2000)", "html": "1905 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Welsh-English actress, singer, and dancer (d. 2000)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Welsh-English actress, singer, and dancer (d. 2000)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1906", "text": "<PERSON><PERSON><PERSON>, Vietnamese lieutenant and politician, 2nd Prime Minister of Vietnam (d. 2000)", "html": "1906 - <a href=\"https://wikipedia.org/wiki/Ph%E1%BA%A1m_V%C4%83n_%C4%90%E1%BB%93ng\" title=\"Phạm Văn <PERSON>\"><PERSON><PERSON><PERSON></a>, Vietnamese lieutenant and politician, 2nd <a href=\"https://wikipedia.org/wiki/List_of_Prime_Ministers_of_Vietnam\" class=\"mw-redirect\" title=\"List of Prime Ministers of Vietnam\">Prime Minister of Vietnam</a> (d. 2000)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Ph%E1%BA%A1m_V%C4%83n_%C4%90%E1%BB%93ng\" title=\"Phạm Văn <PERSON>\"><PERSON><PERSON><PERSON></a>, Vietnamese lieutenant and politician, 2nd <a href=\"https://wikipedia.org/wiki/List_of_Prime_Ministers_of_Vietnam\" class=\"mw-redirect\" title=\"List of Prime Ministers of Vietnam\">Prime Minister of Vietnam</a> (d. 2000)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Ph%E1%BA%A1m_V%C4%83n_%C4%90%E1%BB%93ng"}, {"title": "List of Prime Ministers of Vietnam", "link": "https://wikipedia.org/wiki/List_of_Prime_Ministers_of_Vietnam"}]}, {"year": "1909", "text": "<PERSON>, English lieutenant and pilot (d. 1942)", "html": "1909 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English lieutenant and pilot (d. 1942)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English lieutenant and pilot (d. 1942)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1909", "text": "<PERSON>, American pianist and composer (d. 1978)", "html": "1909 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American pianist and composer (d. 1978)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American pianist and composer (d. 1978)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1910", "text": "<PERSON>, English chemist and academic, Nobel Prize laureate (d. 2002)", "html": "1910 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"Archer <PERSON>\"><PERSON></a>, English chemist and academic, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Chemistry\" title=\"Nobel Prize in Chemistry\">Nobel Prize</a> laureate (d. 2002)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"Archer <PERSON>\"><PERSON></a>, English chemist and academic, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Chemistry\" title=\"Nobel Prize in Chemistry\">Nobel Prize</a> laureate (d. 2002)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>"}, {"title": "Nobel Prize in Chemistry", "link": "https://wikipedia.org/wiki/Nobel_Prize_in_Chemistry"}]}, {"year": "1910", "text": "<PERSON>, English soldier and actor (d. 1983)", "html": "1910 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English soldier and actor (d. 1983)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English soldier and actor (d. 1983)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1912", "text": "<PERSON>, Canadian cardinal (d. 2003)", "html": "1912 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian cardinal (d. 2003)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian cardinal (d. 2003)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1912", "text": "<PERSON>, Polish-Russian engineer and academic (d. 2011)", "html": "1912 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Polish-Russian engineer and academic (d. 2011)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Polish-Russian engineer and academic (d. 2011)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1914", "text": "<PERSON>, American sportscaster (d. 1998)", "html": "1914 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American sportscaster (d. 1998)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American sportscaster (d. 1998)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1914", "text": "<PERSON>, American novelist and literary critic (d. 1994)", "html": "1914 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American novelist and literary critic (d. 1994)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American novelist and literary critic (d. 1994)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1917", "text": "<PERSON>, American poet (d. 1977)", "html": "1917 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American poet (d. 1977)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American poet (d. 1977)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1917", "text": "<PERSON><PERSON>, American singer and actress (d. 1994)", "html": "1917 - <a href=\"https://wikipedia.org/wiki/Dinah_Shore\" title=\"Dinah Shore\"><PERSON><PERSON></a>, American singer and actress (d. 1994)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Dinah_Shore\" title=\"Dinah Shore\"><PERSON><PERSON></a>, American singer and actress (d. 1994)", "links": [{"title": "Dinah <PERSON>", "link": "https://wikipedia.org/wiki/Dinah_<PERSON>"}]}, {"year": "1918", "text": "<PERSON>, Brazilian lawyer and politician, 24th President of Brazil (d. 1976)", "html": "1918 - <a href=\"https://wikipedia.org/wiki/Jo%C3%A3o_Go<PERSON>t\" title=\"<PERSON>\"><PERSON></a>, Brazilian lawyer and politician, 24th <a href=\"https://wikipedia.org/wiki/President_of_Brazil\" title=\"President of Brazil\">President of Brazil</a> (d. 1976)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Jo%C3%A3o_Go<PERSON>t\" title=\"<PERSON>\"><PERSON></a>, Brazilian lawyer and politician, 24th <a href=\"https://wikipedia.org/wiki/President_of_Brazil\" title=\"President of Brazil\">President of Brazil</a> (d. 1976)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Jo%C3%A3o_Go<PERSON>t"}, {"title": "President of Brazil", "link": "https://wikipedia.org/wiki/President_of_Brazil"}]}, {"year": "1918", "text": "<PERSON>, American educator and politician (d. 1988)", "html": "1918 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American educator and politician (d. 1988)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American educator and politician (d. 1988)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1920", "text": "<PERSON>, Canadian ice hockey player (d. 1984)", "html": "1920 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player (d. 1984)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Bentley\"><PERSON></a>, Canadian ice hockey player (d. 1984)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1920", "text": "<PERSON>, American poet and academic (d. 1991)", "html": "1920 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American poet and academic (d. 1991)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American poet and academic (d. 1991)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1921", "text": "<PERSON>, American race car driver and lawyer (d. 2008)", "html": "1921 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American race car driver and lawyer (d. 2008)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American race car driver and lawyer (d. 2008)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1921", "text": "<PERSON>, American cardinal (d. 1983)", "html": "1921 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American cardinal (d. 1983)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American cardinal (d. 1983)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1921", "text": "<PERSON>, American poet, translator, and essayist (d. 2017)", "html": "1921 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American poet, translator, and essayist (d. 2017)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American poet, translator, and essayist (d. 2017)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>bur"}]}, {"year": "1922", "text": "<PERSON>, American publisher (d. 1992)", "html": "1922 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American publisher (d. 1992)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American publisher (d. 1992)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1922", "text": "<PERSON><PERSON><PERSON>, Israeli general and politician, 5th Prime Minister of Israel, Nobel Prize laureate (d. 1995)", "html": "1922 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Israeli general and politician, 5th <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Israel\" title=\"Prime Minister of Israel\">Prime Minister of Israel</a>, <a href=\"https://wikipedia.org/wiki/Nobel_Peace_Prize\" title=\"Nobel Peace Prize\">Nobel Prize</a> laureate (d. 1995)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Israeli general and politician, 5th <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Israel\" title=\"Prime Minister of Israel\">Prime Minister of Israel</a>, <a href=\"https://wikipedia.org/wiki/Nobel_Peace_Prize\" title=\"Nobel Peace Prize\">Nobel Prize</a> laureate (d. 1995)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON><PERSON>"}, {"title": "Prime Minister of Israel", "link": "https://wikipedia.org/wiki/Prime_Minister_of_Israel"}, {"title": "Nobel Peace Prize", "link": "https://wikipedia.org/wiki/Nobel_Peace_Prize"}]}, {"year": "1922", "text": "<PERSON>, American basketball player (d. 2002)", "html": "1922 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player (d. 2002)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player (d. 2002)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1924", "text": "<PERSON>, American author and screenwriter (d. 2007)", "html": "1924 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author and screenwriter (d. 2007)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Drake\"><PERSON></a>, American author and screenwriter (d. 2007)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1924", "text": "<PERSON><PERSON>, American soldier, pilot, and astronaut (d. 1993)", "html": "1924 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American soldier, pilot, and astronaut (d. 1993)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American soldier, pilot, and astronaut (d. 1993)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1926", "text": "<PERSON>, French-American actor and author (d. 2022)", "html": "1926 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French-American actor and author (d. 2022)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French-American actor and author (d. 2022)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1926", "text": "<PERSON><PERSON><PERSON>, Italian-American actor (d. 1992)", "html": "1926 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Italian-American actor (d. 1992)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Italian-American actor (d. 1992)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1926", "text": "<PERSON>, American businessman and 3rd National Football League Commissioner (d. 1996)", "html": "1926 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American businessman and 3rd <a href=\"https://wikipedia.org/wiki/National_Football_League_Commissioner\" class=\"mw-redirect\" title=\"National Football League Commissioner\">National Football League Commissioner</a> (d. 1996)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American businessman and 3rd <a href=\"https://wikipedia.org/wiki/National_Football_League_Commissioner\" class=\"mw-redirect\" title=\"National Football League Commissioner\">National Football League Commissioner</a> (d. 1996)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "National Football League Commissioner", "link": "https://wikipedia.org/wiki/National_Football_League_Commissioner"}]}, {"year": "1926", "text": "<PERSON>, Canadian ice hockey player and coach (d. 2013)", "html": "1926 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player and coach (d. 2013)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player and coach (d. 2013)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1927", "text": "<PERSON>, American astronomer, academic, and skeptic (d. 1983)", "html": "1927 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American astronomer, academic, and skeptic (d. 1983)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American astronomer, academic, and skeptic (d. 1983)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1927", "text": "<PERSON>, American singer-songwriter and actor (d. 2023)", "html": "1927 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and actor (d. 2023)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and actor (d. 2023)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1927", "text": "<PERSON>, American lawyer and scholar, United States Attorney General (d. 2012)", "html": "1927 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and scholar, <a href=\"https://wikipedia.org/wiki/United_States_Attorney_General\" title=\"United States Attorney General\">United States Attorney General</a> (d. 2012)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and scholar, <a href=\"https://wikipedia.org/wiki/United_States_Attorney_General\" title=\"United States Attorney General\">United States Attorney General</a> (d. 2012)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "United States Attorney General", "link": "https://wikipedia.org/wiki/United_States_Attorney_General"}]}, {"year": "1928", "text": "<PERSON>, French director, screenwriter, and critic (d. 2016)", "html": "1928 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French director, screenwriter, and critic (d. 2016)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French director, screenwriter, and critic (d. 2016)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1929", "text": "<PERSON><PERSON>, Bulgarian journalist and author (d. 1978)", "html": "1929 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Bulgarian journalist and author (d. 1978)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Bulgarian journalist and author (d. 1978)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1930", "text": "<PERSON><PERSON>, Indian Bengali actor (d. 2020)", "html": "1930 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Indian Bengali actor (d. 2020)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Indian Bengali actor (d. 2020)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON><PERSON><PERSON><PERSON>"}]}, {"year": "1930", "text": "<PERSON><PERSON>, Italian cyclist (d. 1980)", "html": "1930 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Italian cyclist (d. 1980)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Italian cyclist (d. 1980)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1934", "text": "<PERSON><PERSON><PERSON>, Belgian painter and sculptor (d. 2005)", "html": "1934 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Belgian painter and sculptor (d. 2005)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Belgian painter and sculptor (d. 2005)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>"}]}, {"year": "1934", "text": "<PERSON>, American actress (d. 1983)", "html": "1934 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress (d. 1983)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress (d. 1983)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1935", "text": "<PERSON>, American actor, radio host and stuntman (d. 2020)", "html": "1935 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor, radio host and stuntman (d. 2020)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor, radio host and stuntman (d. 2020)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1936", "text": "<PERSON><PERSON><PERSON><PERSON>, French author (d. 1997)", "html": "1936 - <a href=\"https://wikipedia.org/wiki/<PERSON>-<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, French author (d. 1997)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, French author (d. 1997)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>-<PERSON><PERSON>_<PERSON>"}]}, {"year": "1939", "text": "<PERSON>, Cuban guitarist, composer, and conductor", "html": "1939 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Cuban guitarist, composer, and conductor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Cuban guitarist, composer, and conductor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1939", "text": "<PERSON><PERSON><PERSON>, Pakistani author", "html": "1939 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Pakistani author", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Pakistani author", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1940", "text": "<PERSON>, Australian politician, 37th Premier of Tasmania", "html": "1940 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>(Australian_politician)\" title=\"<PERSON> (Australian politician)\"><PERSON></a>, Australian politician, 37th <a href=\"https://wikipedia.org/wiki/Premier_of_Tasmania\" title=\"Premier of Tasmania\">Premier of Tasmania</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>(Australian_politician)\" title=\"<PERSON> (Australian politician)\"><PERSON></a>, Australian politician, 37th <a href=\"https://wikipedia.org/wiki/Premier_of_Tasmania\" title=\"Premier of Tasmania\">Premier of Tasmania</a>", "links": [{"title": "<PERSON> (Australian politician)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>(Australian_politician)"}, {"title": "Premier of Tasmania", "link": "https://wikipedia.org/wiki/Premier_of_Tasmania"}]}, {"year": "1940", "text": "<PERSON>, American painter, sculptor, and author (d. 2018)", "html": "1940 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(artist)\" title=\"<PERSON> (artist)\"><PERSON></a>, American painter, sculptor, and author (d. 2018)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(artist)\" title=\"<PERSON> (artist)\"><PERSON></a>, American painter, sculptor, and author (d. 2018)", "links": [{"title": "<PERSON> (artist)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(artist)"}]}, {"year": "1941", "text": "<PERSON>, American poet", "html": "1941 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American poet", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American poet", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1941", "text": "<PERSON>, American stock car racing driver", "html": "1941 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American stock car racing driver", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American stock car racing driver", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1942", "text": "<PERSON>, American general", "html": "1942 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American general", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American general", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1943", "text": "<PERSON>, American businessman", "html": "1943 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American businessman", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American businessman", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1943", "text": "<PERSON>, Spanish footballer and manager", "html": "1943 - <a href=\"https://wikipedia.org/wiki/Jos%C3%A9_%C3%81ngel_I<PERSON><PERSON>\" title=\"<PERSON>\"><PERSON></a>, Spanish footballer and manager", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Jos%C3%A9_%C3%81ngel_I<PERSON>bar\" title=\"<PERSON>\"><PERSON></a>, Spanish footballer and manager", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Jos%C3%A9_%C3%81ngel_Iribar"}]}, {"year": "1943", "text": "<PERSON>, Russian-German astronomer and physicist", "html": "1943 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian-German astronomer and physicist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian-German astronomer and physicist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1944", "text": "<PERSON><PERSON><PERSON>, Indian politician, 7th Chief Minister of West Bengal", "html": "1944 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Indian politician, 7th <a href=\"https://wikipedia.org/wiki/Chief_Minister_of_West_Bengal\" class=\"mw-redirect\" title=\"Chief Minister of West Bengal\">Chief Minister of West Bengal</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Indian politician, 7th <a href=\"https://wikipedia.org/wiki/Chief_Minister_of_West_Bengal\" class=\"mw-redirect\" title=\"Chief Minister of West Bengal\">Chief Minister of West Bengal</a>", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON><PERSON>"}, {"title": "Chief Minister of West Bengal", "link": "https://wikipedia.org/wiki/Chief_Minister_of_West_Bengal"}]}, {"year": "1944", "text": "<PERSON>, American lawyer and politician", "html": "1944 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and politician", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1944", "text": "<PERSON>, English singer", "html": "1944 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%27Abo\" title=\"<PERSON>\"><PERSON></a>, English singer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%27Abo\" title=\"<PERSON>\"><PERSON></a>, English singer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>%27Abo"}]}, {"year": "1944", "text": "<PERSON>, English singer-songwriter, producer, and actor", "html": "1944 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English singer-songwriter, producer, and actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English singer-songwriter, producer, and actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1945", "text": "<PERSON>, American actor and director", "html": "1945 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor and director", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor and director", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1946", "text": "<PERSON>, Canadian singer-songwriter (d. 1990)", "html": "1946 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian singer-songwriter (d. 1990)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian singer-songwriter (d. 1990)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1946", "text": "<PERSON>, English author and academic", "html": "1946 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English author and academic", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English author and academic", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1947", "text": "<PERSON>, Canadian-American actor and composer (d. 2016)", "html": "1947 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian-American actor and composer (d. 2016)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian-American actor and composer (d. 2016)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1951", "text": "<PERSON>, Russian-American KGB agent (d. 1973)", "html": "1951 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian-American <a href=\"https://wikipedia.org/wiki/KGB\" title=\"KGB\">KGB</a> agent (d. 1973)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian-American <a href=\"https://wikipedia.org/wiki/KGB\" title=\"KGB\">KGB</a> agent (d. 1973)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}, {"title": "KGB", "link": "https://wikipedia.org/wiki/KGB"}]}, {"year": "1952", "text": "<PERSON>, Canadian golfer", "html": "1952 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(golfer)\" title=\"<PERSON> (golfer)\"><PERSON></a>, Canadian golfer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>(golfer)\" title=\"<PERSON> (golfer)\"><PERSON></a>, Canadian golfer", "links": [{"title": "<PERSON> (golfer)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>(golfer)"}]}, {"year": "1952", "text": "<PERSON>, American actress and author", "html": "1952 - <a href=\"https://wikipedia.org/wiki/Nevada_Barr\" title=\"Nevada Barr\"><PERSON> Barr</a>, American actress and author", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Nevada_Barr\" title=\"Nevada Barr\"><PERSON> Barr</a>, American actress and author", "links": [{"title": "Nevada Barr", "link": "https://wikipedia.org/wiki/Nevada_Barr"}]}, {"year": "1952", "text": "<PERSON>, Australian footballer, coach, and sportscaster", "html": "1952 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian footballer, coach, and sportscaster", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian footballer, coach, and sportscaster", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1952", "text": "<PERSON><PERSON>, American physician and explorer (d. 2009)", "html": "1952 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American physician and explorer (d. 2009)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American physician and explorer (d. 2009)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1952", "text": "<PERSON>, Northern Irish footballer and manager", "html": "1952 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%27Neill\" title=\"<PERSON>\"><PERSON></a>, Northern Irish footballer and manager", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%27Neill\" title=\"<PERSON>\"><PERSON></a>, Northern Irish footballer and manager", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Martin_O%27Neill"}]}, {"year": "1952", "text": "<PERSON>, American basketball player and coach", "html": "1952 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player and coach", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1953", "text": "<PERSON><PERSON>, Turkish actor, director, and producer", "html": "1953 - <a href=\"https://wikipedia.org/wiki/Sinan_%C3%87etin\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Turkish actor, director, and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Sinan_%C3%87etin\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Turkish actor, director, and producer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Sinan_%C3%87etin"}]}, {"year": "1953", "text": "<PERSON>, Portuguese footballer and manager", "html": "1953 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Portuguese footballer and manager", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Portuguese footballer and manager", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1953", "text": "<PERSON><PERSON> <PERSON><PERSON>, Indian Tamil politician, 8th and incumbent Chief Minister of Tamil Nadu", "html": "1953 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> <PERSON><PERSON>\"><PERSON><PERSON> <PERSON><PERSON></a>, Indian <a href=\"https://wikipedia.org/wiki/Politics_of_Tamil_Nadu\" title=\"Politics of Tamil Nadu\">Tamil politician</a>, 8th and incumbent <a href=\"https://wikipedia.org/wiki/List_of_chief_ministers_of_Tamil_Nadu\" title=\"List of chief ministers of Tamil Nadu\">Chief Minister of Tamil Nadu</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> <PERSON><PERSON>\"><PERSON><PERSON> <PERSON><PERSON></a>, Indian <a href=\"https://wikipedia.org/wiki/Politics_of_Tamil_Nadu\" title=\"Politics of Tamil Nadu\">Tamil politician</a>, 8th and incumbent <a href=\"https://wikipedia.org/wiki/List_of_chief_ministers_of_Tamil_Nadu\" title=\"List of chief ministers of Tamil Nadu\">Chief Minister of Tamil Nadu</a>", "links": [{"title": "<PERSON><PERSON> <PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>"}, {"title": "Politics of Tamil Nadu", "link": "https://wikipedia.org/wiki/Politics_of_Tamil_Nadu"}, {"title": "List of chief ministers of Tamil Nadu", "link": "https://wikipedia.org/wiki/List_of_chief_ministers_of_Tamil_Nadu"}]}, {"year": "1954", "text": "<PERSON>, American actress", "html": "1954 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1954", "text": "<PERSON>, American actor, director, and producer", "html": "1954 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor, director, and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor, director, and producer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1956", "text": "<PERSON>, American actor, director, and producer", "html": "1956 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor, director, and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor, director, and producer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1956", "text": "<PERSON><PERSON>, Lithuanian politician, 8th President of Lithuania", "html": "1956 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_Grybauskait%C4%97\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Lithuanian politician, 8th <a href=\"https://wikipedia.org/wiki/President_of_Lithuania\" title=\"President of Lithuania\">President of Lithuania</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_G<PERSON>bauskait%C4%97\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Lithuanian politician, 8th <a href=\"https://wikipedia.org/wiki/President_of_Lithuania\" title=\"President of Lithuania\">President of Lithuania</a>", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Dalia_Grybauskait%C4%97"}, {"title": "President of Lithuania", "link": "https://wikipedia.org/wiki/President_of_Lithuania"}]}, {"year": "1958", "text": "<PERSON>, English singer-songwriter, guitarist, and producer", "html": "1958 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English singer-songwriter, guitarist, and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English singer-songwriter, guitarist, and producer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1958", "text": "<PERSON>, Australian cricketer and coach", "html": "1958 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian cricketer and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian cricketer and coach", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "1958", "text": "<PERSON>, Swiss psychiatrist and aviator", "html": "1958 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swiss psychiatrist and aviator", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swiss psychiatrist and aviator", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1959", "text": "<PERSON>, English politician", "html": "1959 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English politician", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1961", "text": "<PERSON>, American football player", "html": "1961 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1963", "text": "<PERSON>, American actor", "html": "1963 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1963", "text": "<PERSON>, Canadian ice hockey player and manager", "html": "1963 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player and manager", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player and manager", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1963", "text": "<PERSON>, Swedish ice hockey player", "html": "1963 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(ice_hockey,_born_1963)\" title=\"<PERSON> (ice hockey, born 1963)\"><PERSON></a>, Swedish ice hockey player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(ice_hockey,_born_1963)\" title=\"<PERSON> (ice hockey, born 1963)\"><PERSON></a>, Swedish ice hockey player", "links": [{"title": "<PERSON> (ice hockey, born 1963)", "link": "https://wikipedia.org/wiki/<PERSON>_(ice_hockey,_born_1963)"}]}, {"year": "1963", "text": "<PERSON>, American actor", "html": "1963 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1965", "text": "<PERSON>, American professional wrestler and sportscaster", "html": "1965 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(wrestler)\" title=\"<PERSON> (wrestler)\"><PERSON></a>, American professional wrestler and sportscaster", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(wrestler)\" title=\"<PERSON> (wrestler)\"><PERSON></a>, American professional wrestler and sportscaster", "links": [{"title": "<PERSON> (wrestler)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>(wrestler)"}]}, {"year": "1965", "text": "<PERSON>, American actor, director, screenwriter, and producer", "html": "1965 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor, director, screenwriter, and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor, director, screenwriter, and producer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1965", "text": "<PERSON>, Canadian jockey", "html": "1965 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian jockey", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian jockey", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1966", "text": "<PERSON>, American journalist", "html": "1966 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American journalist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American journalist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1966", "text": "<PERSON>, American director, producer, and screenwriter", "html": "1966 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American director, producer, and screenwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American director, producer, and screenwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1967", "text": "<PERSON>, American actor", "html": "1967 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1967", "text": "<PERSON><PERSON>, Surinamese-Dutch footballer and manager", "html": "1967 - <a href=\"https://wikipedia.org/wiki/Aron_Winter\" title=\"Aron Winter\"><PERSON><PERSON></a>, Surinamese-Dutch footballer and manager", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Aron_Winter\" title=\"Aron Winter\"><PERSON><PERSON></a>, Surinamese-Dutch footballer and manager", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Aron_Winter"}]}, {"year": "1969", "text": "<PERSON>, Spanish actor and producer", "html": "1969 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Spanish actor and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Spanish actor and producer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1970", "text": "<PERSON><PERSON><PERSON>, American basketball player and coach", "html": "1970 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American basketball player and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American basketball player and coach", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1971", "text": "<PERSON>, South Korean-American actor", "html": "1971 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>-<PERSON>\" title=\"<PERSON>\"><PERSON></a>, South Korean-American actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>-<PERSON>\" title=\"<PERSON>\"><PERSON></a>, South Korean-American actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>-<PERSON>"}]}, {"year": "1971", "text": "<PERSON>, American screenwriter, director, and producer", "html": "1971 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American screenwriter, director, and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American screenwriter, director, and producer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1973", "text": "<PERSON>, English actor", "html": "1973 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1973", "text": "<PERSON>, Canadian musician and songwriter", "html": "1973 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian musician and songwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian musician and songwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1973", "text": "<PERSON>, American basketball player and sportscaster", "html": "1973 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player and sportscaster", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player and sportscaster", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1974", "text": "<PERSON><PERSON><PERSON>, American actor", "html": "1974 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American actor", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>-<PERSON>"}]}, {"year": "1977", "text": "<PERSON><PERSON>, Dutch pole vaulter", "html": "1977 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>lo<PERSON>\" title=\"<PERSON><PERSON> Blom\"><PERSON><PERSON></a>, Dutch pole vaulter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>lo<PERSON>\" title=\"<PERSON><PERSON> Blo<PERSON>\"><PERSON><PERSON></a>, Dutch pole vaulter", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>lom"}]}, {"year": "1978", "text": "<PERSON>, American actor and musician", "html": "1978 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor and musician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor and musician", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1979", "text": "<PERSON><PERSON><PERSON>, Danish boxer", "html": "1979 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Danish boxer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Danish boxer", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1979", "text": "<PERSON>, Canadian cyclist", "html": "1979 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian cyclist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian cyclist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1980", "text": "<PERSON><PERSON>, Pakistani cricketer", "html": "1980 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Pakistani cricketer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Pakistani cricketer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON><PERSON>i"}]}, {"year": "1980", "text": "<PERSON><PERSON>, German-Turkish footballer", "html": "1980 - <a href=\"https://wikipedia.org/wiki/Sercan_G%C3%BCven%C4%B1%C5%9F%C4%B1k\" title=\"<PERSON><PERSON>ü<PERSON>ışık\"><PERSON><PERSON></a>, German-Turkish footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Sercan_G%C3%BCven%C4%B1%C5%9F%C4%B1k\" title=\"<PERSON><PERSON>ü<PERSON>ı<PERSON>\"><PERSON><PERSON></a>, German-Turkish footballer", "links": [{"title": "Sercan Güvenışık", "link": "https://wikipedia.org/wiki/Sercan_G%C3%BCven%C4%B1%C5%9F%C4%B1k"}]}, {"year": "1980", "text": "<PERSON><PERSON><PERSON>, French-Malian footballer", "html": "1980 - <a href=\"https://wikipedia.org/wiki/Dji<PERSON>_Traor%C3%A9\" title=\"<PERSON><PERSON><PERSON> T<PERSON>or<PERSON>\"><PERSON><PERSON><PERSON></a>, French-Malian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/D<PERSON><PERSON>_Traor%C3%A9\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, French-Malian footballer", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Djimi_Traor%C3%A9"}]}, {"year": "1981", "text": "<PERSON>, Australian race car driver", "html": "1981 - <a href=\"https://wikipedia.org/wiki/<PERSON>_Power\" title=\"Will Power\"><PERSON></a>, Australian race car driver", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Will_Power\" title=\"Will Power\"><PERSON></a>, Australian race car driver", "links": [{"title": "Will Power", "link": "https://wikipedia.org/wiki/Will_Power"}]}, {"year": "1982", "text": "<PERSON>, American-Italian basketball player", "html": "1982 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American-Italian basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American-Italian basketball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1983", "text": "<PERSON>, Brazilian footballer", "html": "1983 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Brazilian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Brazilian footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1983", "text": "<PERSON><PERSON><PERSON>, Kenyan-Mexican actress", "html": "1983 - <a href=\"https://wikipedia.org/wiki/Lupita_Nyong%27o\" title=\"<PERSON><PERSON><PERSON>o\"><PERSON><PERSON><PERSON></a>, Kenyan-Mexican actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Lupita_Nyong%27o\" title=\"<PERSON><PERSON><PERSON>o\"><PERSON><PERSON><PERSON></a>, Kenyan-Mexican actress", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Lupita_Nyong%27o"}]}, {"year": "1984", "text": "<PERSON>, Canadian-Swedish ice hockey player", "html": "1984 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian-Swedish ice hockey player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian-Swedish ice hockey player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1984", "text": "<PERSON>, Argentinian footballer", "html": "1984 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Argentinian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Argentinian footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1985", "text": "<PERSON>, German footballer", "html": "1985 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1986", "text": "<PERSON> <PERSON>, American professional wrestler", "html": "1986 - <a href=\"https://wikipedia.org/wiki/<PERSON>_E_(wrestler)\" title=\"<PERSON> E (wrestler)\"><PERSON></a>, American professional wrestler", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_E_(wrestler)\" title=\"<PERSON> E (wrestler)\"><PERSON></a>, American professional wrestler", "links": [{"title": "<PERSON> (wrestler)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(wrestler)"}]}, {"year": "1986", "text": "<PERSON>, American soccer player", "html": "1986 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American soccer player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American soccer player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1986", "text": "<PERSON>, Ukrainian-English actor", "html": "1986 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Ukrainian-English actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Ukrainian-English actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1987", "text": "<PERSON><PERSON>, American singer-songwriter and actress", "html": "1987 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American singer-songwriter and actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American singer-songwriter and actress", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>"}]}, {"year": "1987", "text": "<PERSON>, Canadian professional wrestler", "html": "1987 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%27Reilly\" title=\"<PERSON>\"><PERSON></a>, Canadian professional wrestler", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%27Reilly\" title=\"<PERSON>\"><PERSON></a>, Canadian professional wrestler", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Kyle_O%27Reilly"}]}, {"year": "1988", "text": "<PERSON>, American baseball player", "html": "1988 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1988", "text": "<PERSON>, American basketball player", "html": "1988 - <a href=\"https://wikipedia.org/wiki/<PERSON>_Varnado\" title=\"Jarvis Varnado\"><PERSON></a>, American basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_Varnado\" title=\"Jarvis Varnado\"><PERSON></a>, American basketball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_Varnado"}]}, {"year": "1989", "text": "<PERSON><PERSON>, Australian professional wrestler", "html": "1989 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Australian professional wrestler", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Australian professional wrestler", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1989", "text": "<PERSON><PERSON>, American actress", "html": "1989 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> Mon<PERSON>\"><PERSON><PERSON></a>, American actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> Mon<PERSON>\"><PERSON><PERSON></a>, American actress", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_Monet"}]}, {"year": "1989", "text": "<PERSON><PERSON><PERSON>, Canadian-American actress", "html": "1989 - <a href=\"https://wikipedia.org/wiki/Emeraude_Toubi<PERSON>\" title=\"Emeraude Toubia\"><PERSON><PERSON><PERSON></a>, Canadian-American actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>era<PERSON>_<PERSON>\" title=\"Emeraude Toubia\"><PERSON><PERSON><PERSON></a>, Canadian-American actress", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Emeraude_Toubi<PERSON>"}]}, {"year": "1989", "text": "<PERSON>, Mexican footballer", "html": "1989 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Mexican footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Mexican footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1991", "text": "<PERSON>, American baseball player", "html": "1991 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1992", "text": "<PERSON><PERSON><PERSON>, Senegalese footballer", "html": "1992 - <a href=\"https://wikipedia.org/wiki/%C3%89douard_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Senegalese footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/%C3%89<PERSON>uard_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Senegalese footballer", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/%C3%89<PERSON><PERSON>_<PERSON>"}]}, {"year": "1992", "text": "<PERSON>, New Zealand athlete", "html": "1992 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(shot_putter)\" title=\"<PERSON> (shot putter)\"><PERSON></a>, New Zealand athlete", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(shot_putter)\" title=\"<PERSON> (shot putter)\"><PERSON></a>, New Zealand athlete", "links": [{"title": "<PERSON> (shot putter)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(shot_putter)"}]}, {"year": "1993", "text": "<PERSON>, Spanish footballer", "html": "1993 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Spanish footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Spanish footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1993", "text": "<PERSON>, American baseball player", "html": "1993 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1993", "text": "<PERSON>, English footballer", "html": "1993 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1993", "text": "<PERSON>, Swedish ice hockey player", "html": "1993 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swedish ice hockey player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swedish ice hockey player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1993", "text": "<PERSON>, French footballer", "html": "1993 - <a href=\"https://wikipedia.org/wiki/Jordan_Veretout\" title=\"<PERSON> Vere<PERSON>ut\"><PERSON></a>, French footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Jordan_Veretout\" title=\"<PERSON> Veretout\"><PERSON></a>, French footballer", "links": [{"title": "Jordan Veretout", "link": "https://wikipedia.org/wiki/Jordan_Veretout"}]}, {"year": "1994", "text": "<PERSON>, Canadian singer-songwriter", "html": "1994 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian singer-songwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian singer-songwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1994", "text": "<PERSON><PERSON><PERSON>, Japanese sumo wrestler", "html": "1994 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_Hiroki\" title=\"Asanoyama Hiroki\"><PERSON><PERSON><PERSON></a>, Japanese sumo wrestler", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_Hiroki\" title=\"Asanoyama Hiroki\"><PERSON><PERSON><PERSON></a>, Japanese sumo wrestler", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_Hiroki"}]}, {"year": "1994", "text": "<PERSON><PERSON><PERSON>, American football player", "html": "1994 - <a href=\"https://wikipedia.org/wiki/Tyreek_Hill\" title=\"Tyreek Hill\">Ty<PERSON><PERSON> <PERSON></a>, American football player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Tyreek_Hill\" title=\"Tyreek Hill\">Tyreek Hill</a>, American football player", "links": [{"title": "Tyreek Hill", "link": "https://wikipedia.org/wiki/Tyreek_Hill"}]}, {"year": "1994", "text": "<PERSON>, German footballer", "html": "1994 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1999", "text": "<PERSON><PERSON>, Venezuelan baseball player", "html": "1999 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Venezuelan baseball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Venezuelan baseball player", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1999", "text": "<PERSON><PERSON><PERSON>, Scottish footballer", "html": "1999 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Scottish footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Scottish footballer", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "2000", "text": "<PERSON><PERSON><PERSON><PERSON><PERSON>, American football player", "html": "2000 - <a href=\"https://wikipedia.org/wiki/Ja%27Mar<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON><PERSON> <PERSON>\"><PERSON><PERSON><PERSON><PERSON><PERSON></a>, American football player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Ja%27Mar<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>'<PERSON><PERSON> <PERSON>\"><PERSON><PERSON><PERSON><PERSON><PERSON></a>, American football player", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Ja%27Marr_<PERSON>"}]}, {"year": "2001", "text": "<PERSON><PERSON>, Dominican baseball player", "html": "2001 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Dominican baseball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"Wan<PERSON> Franco\"><PERSON><PERSON></a>, Dominican baseball player", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "2001", "text": "<PERSON><PERSON><PERSON>, American YouTuber", "html": "2001 - <a href=\"https://wikipedia.org/wiki/Sapnap\" title=\"Sapnap\"><PERSON><PERSON><PERSON></a>, American YouTuber", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Sapnap\" title=\"Sapnap\"><PERSON><PERSON><PERSON></a>, American YouTuber", "links": [{"title": "Sapnap", "link": "https://wikipedia.org/wiki/Sapnap"}]}], "Deaths": [{"year": "492", "text": "<PERSON>, pope of the Catholic Church", "html": "492 - <a href=\"https://wikipedia.org/wiki/Pope_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, pope of the Catholic Church", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Pope_<PERSON>_<PERSON>\" title=\"<PERSON> Felix <PERSON>\"><PERSON> III</a>, pope of the Catholic Church", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "589", "text": "<PERSON>, Welsh bishop and saint", "html": "589 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Saint <PERSON>\"><PERSON></a>, Welsh bishop and saint", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Saint <PERSON>\"><PERSON></a>, Welsh bishop and saint", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Saint_David"}]}, {"year": "965", "text": "<PERSON>, pope of the Catholic Church", "html": "965 - <a href=\"https://wikipedia.org/wiki/Pope_<PERSON>_VIII\" title=\"Pope Leo VIII\"><PERSON></a>, pope of the Catholic Church", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"Pope Leo VIII\"><PERSON></a>, pope of the Catholic Church", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "977", "text": "<PERSON><PERSON><PERSON>, Galician bishop (b. 907)", "html": "977 - <a href=\"https://wikipedia.org/wiki/Rudes<PERSON>\" title=\"<PERSON>udes<PERSON>\"><PERSON><PERSON><PERSON></a>, Galician bishop (b. 907)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Rudes<PERSON>\" title=\"<PERSON>udes<PERSON>\"><PERSON><PERSON><PERSON></a>, Galician bishop (b. 907)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/R<PERSON>ind"}]}, {"year": "991", "text": "<PERSON><PERSON><PERSON><PERSON>, Japanese emperor (b. 959)", "html": "991 - <a href=\"https://wikipedia.org/wiki/Emperor_En%27y%C5%AB\" title=\"Emperor <PERSON>'y<PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Japanese emperor (b. 959)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Emperor_En%27y%C5%AB\" title=\"Emperor <PERSON>'y<PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Japanese emperor (b. 959)", "links": [{"title": "Emperor <PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Emperor_En%27y%C5%AB"}]}, {"year": "1058", "text": "<PERSON><PERSON><PERSON><PERSON> of Carcassonne, countess and regent of Barcelona (b. 972)", "html": "1058 - <a href=\"https://wikipedia.org/wiki/Ermesinde_of_Carcassonne\" title=\"Ermesinde of Carcassonne\"><PERSON><PERSON><PERSON><PERSON> of Carcassonne</a>, countess and regent of Barcelona (b. 972)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Ermesinde_of_Carcassonne\" title=\"<PERSON>rmesi<PERSON> of Carcassonne\"><PERSON><PERSON><PERSON><PERSON> of Carcassonne</a>, countess and regent of Barcelona (b. 972)", "links": [{"title": "Ermesinde of Carcassonne", "link": "https://wikipedia.org/wiki/Ermesinde_of_Carcassonne"}]}, {"year": "1131", "text": "<PERSON>, king of Hungary and Croatia (b. 1101)", "html": "1131 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Hungary\" title=\"<PERSON> II of Hungary\"><PERSON> II</a>, king of Hungary and Croatia (b. 1101)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Hungary\" title=\"<PERSON> II of Hungary\"><PERSON> II</a>, king of Hungary and Croatia (b. 1101)", "links": [{"title": "<PERSON> of Hungary", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Hungary"}]}, {"year": "1233", "text": "<PERSON>, count of Savoy (b. 1178)", "html": "1233 - <a href=\"https://wikipedia.org/wiki/<PERSON>,_Count_of_Savoy\" title=\"<PERSON>, Count of Savoy\"><PERSON></a>, count of Savoy (b. 1178)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>,_Count_of_Savoy\" title=\"<PERSON>, Count of Savoy\"><PERSON></a>, count of Savoy (b. 1178)", "links": [{"title": "<PERSON>, Count of Savoy", "link": "https://wikipedia.org/wiki/<PERSON>,_Count_of_Savoy"}]}, {"year": "1244", "text": "<PERSON><PERSON><PERSON><PERSON> ap <PERSON><PERSON>, Welsh noble, son of <PERSON><PERSON><PERSON><PERSON> the <PERSON> (b. 1200)", "html": "1244 - <a href=\"https://wikipedia.org/wiki/G<PERSON>ydd_ap_Llywelyn_Fawr\" class=\"mw-redirect\" title=\"<PERSON><PERSON>ydd ap Llywelyn Fawr\"><PERSON><PERSON><PERSON><PERSON> ap <PERSON><PERSON><PERSON><PERSON>aw<PERSON></a>, Welsh noble, son of <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_the_Great\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON><PERSON> the Great\"><PERSON><PERSON><PERSON><PERSON> the Great</a> (b. 1200)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/G<PERSON>yd<PERSON>_ap_Llywelyn_Fawr\" class=\"mw-redirect\" title=\"<PERSON><PERSON>ydd ap Llywelyn Fawr\"><PERSON><PERSON><PERSON><PERSON> ap <PERSON><PERSON><PERSON><PERSON></a>, Welsh noble, son of <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_the_Great\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON><PERSON> the Great\"><PERSON><PERSON><PERSON><PERSON> the Great</a> (b. 1200)", "links": [{"title": "G<PERSON><PERSON>d ap Llywelyn Fawr", "link": "https://wikipedia.org/wiki/Gruffydd_ap_Llywelyn_Fawr"}, {"title": "<PERSON><PERSON><PERSON><PERSON> the Great", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_the_Great"}]}, {"year": "1320", "text": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, Chinese emperor (b. 1286)", "html": "1320 - <a href=\"https://wikipedia.org/wiki/Ayurbarwada_Buyantu_Khan\" title=\"Ayurbarwada Buyantu Khan\"><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON></a>, Chinese emperor (b. 1286)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Ayurbarwada_Buyantu_Khan\" title=\"Ayurbarwada Buyantu Khan\"><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON></a>, Chinese emperor (b. 1286)", "links": [{"title": "A<PERSON>rb<PERSON><PERSON><PERSON> Buyantu Khan", "link": "https://wikipedia.org/wiki/Ayurbarwada_Buyantu_Khan"}]}, {"year": "1383", "text": "<PERSON><PERSON><PERSON>, count of Savoy (b. 1334)", "html": "1383 - <a href=\"https://wikipedia.org/wiki/Amade<PERSON>_VI,_Count_of_Savoy\" title=\"<PERSON><PERSON><PERSON> VI, Count of Savoy\">Am<PERSON><PERSON> VI</a>, count of Savoy (b. 1334)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Amade<PERSON>_VI,_Count_of_Savoy\" title=\"<PERSON><PERSON><PERSON> VI, Count of Savoy\">Amade<PERSON> VI</a>, count of Savoy (b. 1334)", "links": [{"title": "<PERSON><PERSON><PERSON>, Count of Savoy", "link": "https://wikipedia.org/wiki/<PERSON>ade<PERSON>_<PERSON>,_Count_of_Savoy"}]}, {"year": "1510", "text": "<PERSON>, Portuguese soldier and explorer (b. 1450)", "html": "1510 - <a href=\"https://wikipedia.org/wiki/Francisco_de_Almeida\" title=\"Francisco de Almeida\"><PERSON></a>, Portuguese soldier and explorer (b. 1450)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Francisco_<PERSON>_Almeida\" title=\"Francisco de Almeida\"><PERSON></a>, Portuguese soldier and explorer (b. 1450)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Francisco_de_Almeida"}]}, {"year": "1546", "text": "<PERSON>, Scottish minister and martyr (b. 1513)", "html": "1546 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish minister and martyr (b. 1513)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish minister and martyr (b. 1513)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1620", "text": "<PERSON>, English poet and composer (b. 1567)", "html": "1620 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English poet and composer (b. 1567)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English poet and composer (b. 1567)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1633", "text": "<PERSON>, English poet and orator (b. 1593)", "html": "1633 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English poet and orator (b. 1593)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English poet and orator (b. 1593)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1643", "text": "<PERSON><PERSON><PERSON>, Italian keyboardist and composer (b. 1583)", "html": "1643 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>di\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Italian keyboardist and composer (b. 1583)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Italian keyboardist and composer (b. 1583)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>lamo_Frescobaldi"}]}, {"year": "1661", "text": "<PERSON>, English judge and politician (b. 1590)", "html": "1661 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English judge and politician (b. 1590)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English judge and politician (b. 1590)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1697", "text": "<PERSON>, Italian physician and poet (b. 1626)", "html": "1697 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian physician and poet (b. 1626)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian physician and poet (b. 1626)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Francesco_<PERSON>i"}]}, {"year": "1734", "text": "<PERSON>, English lawyer and author (b. 1653)", "html": "1734 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(biographer)\" title=\"<PERSON> (biographer)\"><PERSON></a>, English lawyer and author (b. 1653)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(biographer)\" title=\"<PERSON> (biographer)\"><PERSON></a>, English lawyer and author (b. 1653)", "links": [{"title": "<PERSON> (biographer)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(biographer)"}]}, {"year": "1768", "text": "<PERSON>, German philosopher and author (b. 1694)", "html": "1768 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German philosopher and author (b. 1694)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German philosopher and author (b. 1694)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1773", "text": "<PERSON>, Italian architect, designed the Palace of Caserta (b. 1700)", "html": "1773 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian architect, designed the <a href=\"https://wikipedia.org/wiki/Palace_of_Caserta\" class=\"mw-redirect\" title=\"Palace of Caserta\">Palace of Caserta</a> (b. 1700)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian architect, designed the <a href=\"https://wikipedia.org/wiki/Palace_of_Caserta\" class=\"mw-redirect\" title=\"Palace of Caserta\">Palace of Caserta</a> (b. 1700)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}, {"title": "Palace of Caserta", "link": "https://wikipedia.org/wiki/Palace_of_Caserta"}]}, {"year": "1792", "text": "<PERSON>, Holy Roman Emperor (b. 1747)", "html": "1792 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Holy_Roman_Emperor\" title=\"<PERSON> II, Holy Roman Emperor\"><PERSON></a>, Holy Roman Emperor (b. 1747)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Holy_Roman_Emperor\" title=\"<PERSON> II, Holy Roman Emperor\"><PERSON></a>, Holy Roman Emperor (b. 1747)", "links": [{"title": "<PERSON>, Holy Roman Emperor", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Holy_Roman_Emperor"}]}, {"year": "1792", "text": "<PERSON>, Venetian admiral and statesman (b. 1731)", "html": "1792 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Venetian admiral and statesman (b. 1731)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Venetian admiral and statesman (b. 1731)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1841", "text": "<PERSON>, <PERSON><PERSON>, French general and politician, French Minister of Defence (b. 1764)", "html": "1841 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_<PERSON><PERSON>_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>, <PERSON><PERSON>\"><PERSON>, <PERSON><PERSON></a>, French general and politician, <a href=\"https://wikipedia.org/wiki/Minister_of_Defence_(France)\" class=\"mw-redirect\" title=\"Minister of Defence (France)\">French Minister of Defence</a> (b. 1764)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>,_<PERSON><PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>, <PERSON><PERSON>\"><PERSON>, <PERSON><PERSON></a>, French general and politician, <a href=\"https://wikipedia.org/wiki/Minister_of_Defence_(France)\" class=\"mw-redirect\" title=\"Minister of Defence (France)\">French Minister of Defence</a> (b. 1764)", "links": [{"title": "<PERSON>, <PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_<PERSON><PERSON>_<PERSON>_<PERSON>"}, {"title": "Minister of Defence (France)", "link": "https://wikipedia.org/wiki/Minister_of_Defence_(France)"}]}, {"year": "1862", "text": "<PERSON>, English mathematician and physicist (b. 1776)", "html": "1862 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(mathematician)\" title=\"<PERSON> (mathematician)\"><PERSON></a>, English mathematician and physicist (b. 1776)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(mathematician)\" title=\"<PERSON> (mathematician)\"><PERSON></a>, English mathematician and physicist (b. 1776)", "links": [{"title": "<PERSON> (mathematician)", "link": "https://wikipedia.org/wiki/<PERSON>_(mathematician)"}]}, {"year": "1875", "text": "<PERSON>, French poet and educator (b. 1845)", "html": "1875 - <a href=\"https://wikipedia.org/wiki/Tristan_Corbi%C3%A8re\" title=\"<PERSON>\"><PERSON></a>, French poet and educator (b. 1845)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Tristan_Corbi%C3%A8re\" title=\"<PERSON>\"><PERSON></a>, French poet and educator (b. 1845)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Tristan_Corbi%C3%A8re"}]}, {"year": "1882", "text": "<PERSON>, German pianist, composer, and educator (b. 1818)", "html": "1882 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German pianist, composer, and educator (b. 1818)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German pianist, composer, and educator (b. 1818)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1884", "text": "<PERSON>, English mathematician and academic (b. 1820)", "html": "1884 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English mathematician and academic (b. 1820)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English mathematician and academic (b. 1820)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1889", "text": "<PERSON>, English organist and composer  (b. 1823)", "html": "1889 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English organist and composer (b. 1823)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English organist and composer (b. 1823)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1906", "text": "<PERSON>, Spanish author (b. 1833)", "html": "1906 - <a href=\"https://wikipedia.org/wiki/Jos%C3%A9_Mar%C3%AD<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Spanish author (b. 1833)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Jos%C3%A9_Mar%C3%AD<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Spanish author (b. 1833)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Jos%C3%A9_Mar%C3%AD<PERSON>_de_<PERSON><PERSON>"}]}, {"year": "1911", "text": "<PERSON><PERSON>, Dutch-German chemist and academic, Nobel Prize laureate (b. 1852)", "html": "1911 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>_%27t_Hoff\" title=\"<PERSON><PERSON> van 't <PERSON>\"><PERSON><PERSON></a>, Dutch-German chemist and academic, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Chemistry\" title=\"Nobel Prize in Chemistry\">Nobel Prize</a> laureate (b. 1852)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>_%27t_Hoff\" title=\"<PERSON><PERSON> van 't <PERSON>\"><PERSON><PERSON></a>, Dutch-German chemist and academic, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Chemistry\" title=\"Nobel Prize in Chemistry\">Nobel Prize</a> laureate (b. 1852)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>_%27t_Hoff"}, {"title": "Nobel Prize in Chemistry", "link": "https://wikipedia.org/wiki/Nobel_Prize_in_Chemistry"}]}, {"year": "1914", "text": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, 4th Earl of Minto, English soldier and politician, 8th Governor General of Canada (b. 1845)", "html": "1914 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_4th_Earl_of_Minto\" title=\"<PERSON>, 4th Earl of Minto\"><PERSON>, 4th Earl of Minto</a>, English soldier and politician, 8th <a href=\"https://wikipedia.org/wiki/Governor_General_of_Canada\" title=\"Governor General of Canada\">Governor General of Canada</a> (b. 1845)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_4th_Earl_<PERSON>_Minto\" title=\"<PERSON>, 4th Earl of Minto\"><PERSON>, 4th Earl of Minto</a>, English soldier and politician, 8th <a href=\"https://wikipedia.org/wiki/Governor_General_of_Canada\" title=\"Governor General of Canada\">Governor General of Canada</a> (b. 1845)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, 4th Earl of Minto", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_4th_Earl_<PERSON>_Minto"}, {"title": "Governor General of Canada", "link": "https://wikipedia.org/wiki/Governor_General_of_Canada"}]}, {"year": "1920", "text": "<PERSON>, American lawyer and politician (b. 1842)", "html": "1920 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and politician (b. 1842)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and politician (b. 1842)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>head"}]}, {"year": "1922", "text": "<PERSON><PERSON><PERSON>, Spanish footballer (b. 1892)", "html": "1922 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_(footballer)\" title=\"<PERSON><PERSON><PERSON> (footballer)\"><PERSON><PERSON><PERSON></a>, Spanish footballer (b. 1892)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_(footballer)\" title=\"<PERSON><PERSON><PERSON> (footballer)\"><PERSON><PERSON><PERSON></a>, Spanish footballer (b. 1892)", "links": [{"title": "<PERSON><PERSON><PERSON> (footballer)", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_(footballer)"}]}, {"year": "1925", "text": "<PERSON>, American political activist (b. 1862 or 1863)", "html": "1925 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Plessy\"><PERSON></a>, American political activist (b. 1862 or 1863)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Plessy\"><PERSON></a>, American political activist (b. 1862 or 1863)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1932", "text": "<PERSON>, American Jazz musician (b. 1906)", "html": "1932 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American Jazz musician (b. 1906)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American Jazz musician (b. 1906)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1936", "text": "<PERSON>, Russian author and poet (b. 1871)", "html": "1936 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian author and poet (b. 1871)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian author and poet (b. 1871)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1938", "text": "<PERSON><PERSON>, Italian journalist and politician (b. 1863)", "html": "1938 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_D%27Annunzio\" title=\"<PERSON><PERSON>Ann<PERSON>\"><PERSON><PERSON></a>, Italian journalist and politician (b. 1863)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Gabriel<PERSON>_D%27Annunzio\" title=\"<PERSON><PERSON>Annun<PERSON>\"><PERSON><PERSON></a>, Italian journalist and politician (b. 1863)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Gabriele_D%27Annun<PERSON>"}]}, {"year": "1940", "text": "<PERSON><PERSON> <PERSON><PERSON>, Estonian author (b. 1878)", "html": "1940 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> <PERSON><PERSON>\"><PERSON><PERSON> <PERSON><PERSON></a>, Estonian author (b. 1878)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> <PERSON><PERSON>\"><PERSON><PERSON> <PERSON><PERSON></a>, Estonian author (b. 1878)", "links": [{"title": "<PERSON><PERSON> <PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "1942", "text": "<PERSON>, American commander (b. 1882)", "html": "1942 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American commander (b. 1882)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American commander (b. 1882)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1943", "text": "<PERSON>, Swiss-French physician and bacteriologist (b. 1863)", "html": "1943 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swiss-French physician and bacteriologist (b. 1863)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swiss-French physician and bacteriologist (b. 1863)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1952", "text": "<PERSON>, Mexican physician and author (b. 1873)", "html": "1952 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Mexican physician and author (b. 1873)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Mexican physician and author (b. 1873)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Mariano_<PERSON>"}]}, {"year": "1966", "text": "<PERSON>, Polish-German physicist and academic (b. 1903)", "html": "1966 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Polish-German physicist and academic (b. 1903)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Polish-German physicist and academic (b. 1903)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1974", "text": "<PERSON>, American pianist and composer (b. 1935)", "html": "1974 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American pianist and composer (b. 1935)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American pianist and composer (b. 1935)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1976", "text": "<PERSON>, French conductor and composer (b. 1910)", "html": "1976 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French conductor and composer (b. 1910)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French conductor and composer (b. 1910)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1978", "text": "<PERSON>, English author, poet, and playwright (b. 1920)", "html": "1978 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(novelist)\" title=\"<PERSON> (novelist)\"><PERSON></a>, English author, poet, and playwright (b. 1920)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>(novelist)\" title=\"<PERSON> (novelist)\"><PERSON></a>, English author, poet, and playwright (b. 1920)", "links": [{"title": "<PERSON> (novelist)", "link": "https://wikipedia.org/wiki/<PERSON>(novelist)"}]}, {"year": "1979", "text": "<PERSON>, Iraqi-Kurdistan politician (b. 1903)", "html": "1979 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Iraqi-Kurdistan politician (b. 1903)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Iraqi-Kurdistan politician (b. 1903)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Mustafa_<PERSON>"}]}, {"year": "1980", "text": "<PERSON><PERSON>, Dutch-American model and businesswoman, founded <PERSON><PERSON> Models (b. 1940)", "html": "1980 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Dutch-American model and businesswoman, founded <a href=\"https://wikipedia.org/wiki/<PERSON>ina_Models\" title=\"Wilhelmina Models\">Wilhelmina Models</a> (b. 1940)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Dutch-American model and businesswoman, founded <a href=\"https://wikipedia.org/wiki/<PERSON>ina_Models\" title=\"Wilhelmina Models\">Wilhelmina Models</a> (b. 1940)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}, {"title": "Wilhelmina Models", "link": "https://wikipedia.org/wiki/Wilhelmina_Models"}]}, {"year": "1980", "text": "<PERSON>, English footballer (b. 1907)", "html": "1980 - <a href=\"https://wikipedia.org/wiki/<PERSON>_Dean\" title=\"Dixie Dean\"><PERSON></a>, English footballer (b. 1907)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Dixie_Dean\" title=\"Dixie Dean\"><PERSON></a>, English footballer (b. 1907)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1983", "text": "<PERSON>, Hungarian-English journalist and author (b. 1905)", "html": "1983 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Hungarian-English journalist and author (b. 1905)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Hungarian-English journalist and author (b. 1905)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1984", "text": "<PERSON>, American actor (b. 1914)", "html": "1984 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor (b. 1914)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor (b. 1914)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1988", "text": "<PERSON>, American comedian and actor (b. 1907)", "html": "1988 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American comedian and actor (b. 1907)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American comedian and actor (b. 1907)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1989", "text": "<PERSON><PERSON><PERSON><PERSON><PERSON>, Indian politician, 5th Chief Minister of Maharashtra (b. 1917)", "html": "1989 - <a href=\"https://wikipedia.org/wiki/Vasantdada_Patil\" title=\"Vasantdada Patil\">V<PERSON><PERSON><PERSON><PERSON></a>, Indian politician, 5th <a href=\"https://wikipedia.org/wiki/Chief_Minister_of_Maharashtra\" class=\"mw-redirect\" title=\"Chief Minister of Maharashtra\">Chief Minister of Maharashtra</a> (b. 1917)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Vasantdada_Patil\" title=\"Vasantdada Patil\">V<PERSON><PERSON><PERSON><PERSON></a>, Indian politician, 5th <a href=\"https://wikipedia.org/wiki/Chief_Minister_of_Maharashtra\" class=\"mw-redirect\" title=\"Chief Minister of Maharashtra\">Chief Minister of Maharashtra</a> (b. 1917)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Vasantdada_Patil"}, {"title": "Chief Minister of Maharashtra", "link": "https://wikipedia.org/wiki/Chief_Minister_of_Maharashtra"}]}, {"year": "1991", "text": "<PERSON>, American scientist and businessman, co-founded the Polaroid Corporation (b. 1909)", "html": "1991 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"Edwin <PERSON>\"><PERSON></a>, American scientist and businessman, co-founded the <a href=\"https://wikipedia.org/wiki/Polaroid_Corporation\" title=\"Polaroid Corporation\">Polaroid Corporation</a> (b. 1909)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"Edwin <PERSON>\"><PERSON></a>, American scientist and businessman, co-founded the <a href=\"https://wikipedia.org/wiki/Polaroid_Corporation\" title=\"Polaroid Corporation\">Polaroid Corporation</a> (b. 1909)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "Polaroid Corporation", "link": "https://wikipedia.org/wiki/Polaroid_Corporation"}]}, {"year": "1993", "text": "<PERSON>, American schizophrenic serial killer (born 1955)", "html": "1993 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American schizophrenic serial killer (born 1955)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American schizophrenic serial killer (born 1955)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1995", "text": "<PERSON>, Spanish footballer and manager (b. 1920)", "html": "1995 - <a href=\"https://wikipedia.org/wiki/C%C3%A9sar_Rodr%C3%ADguez_%C3%81l<PERSON><PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, Spanish footballer and manager (b. 1920)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/C%C3%A9sar_Rodr%C3%ADguez_%C3%81l<PERSON><PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, Spanish footballer and manager (b. 1920)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/C%C3%A9sar_Rodr%C3%ADguez_%C3%<PERSON><PERSON><PERSON><PERSON>"}]}, {"year": "1995", "text": "<PERSON>, German biologist and academic, Nobel Prize laureate (b. 1946)", "html": "1995 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON><PERSON>_K%C3%B6hler\" title=\"<PERSON>\"><PERSON></a>, German biologist and academic, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Physiology_or_Medicine\" title=\"Nobel Prize in Physiology or Medicine\">Nobel Prize</a> laureate (b. 1946)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>._K%C3%B6hler\" title=\"<PERSON>\"><PERSON></a>, German biologist and academic, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Physiology_or_Medicine\" title=\"Nobel Prize in Physiology or Medicine\">Nobel Prize</a> laureate (b. 1946)", "links": [{"title": "Georges J<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>._K%C3%B6hler"}, {"title": "Nobel Prize in Physiology or Medicine", "link": "https://wikipedia.org/wiki/Nobel_Prize_in_Physiology_or_Medicine"}]}, {"year": "1998", "text": "<PERSON>, American author and illustrator (b. 1937)", "html": "1998 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(comics)\" title=\"<PERSON> (comics)\"><PERSON></a>, American author and illustrator (b. 1937)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(comics)\" title=\"<PERSON> (comics)\"><PERSON></a>, American author and illustrator (b. 1937)", "links": [{"title": "<PERSON> (comics)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(comics)"}]}, {"year": "2004", "text": "<PERSON><PERSON>, Pakistani general (b. 1914)", "html": "2004 - <a href=\"https://wikipedia.org/wiki/Mia<PERSON>_<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Pakistani general (b. 1914)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON> <PERSON><PERSON><PERSON></a>, Pakistani general (b. 1914)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "2006", "text": "<PERSON>, English footballer (b. 1947)", "html": "2006 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English footballer (b. 1947)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English footballer (b. 1947)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2006", "text": "<PERSON>, English actor (b. 1952)", "html": "2006 - <a href=\"https://wikipedia.org/wiki/Jack_<PERSON>\" title=\"Jack Wild\"><PERSON></a>, English actor (b. 1952)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Jack_Wild\" title=\"Jack Wild\"><PERSON></a>, English actor (b. 1952)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2006", "text": "<PERSON><PERSON><PERSON><PERSON> <PERSON><PERSON>, Singaporean rape and murder victim (b. 2003)", "html": "2006 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_binte_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON><PERSON> binte <PERSON>\"><PERSON><PERSON><PERSON><PERSON> binte <PERSON></a>, Singaporean rape and murder victim (b. 2003)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_binte_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON><PERSON> binte <PERSON>\"><PERSON><PERSON><PERSON><PERSON> binte <PERSON></a>, Singaporean rape and murder victim (b. 2003)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON> <PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_bin<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "2010", "text": "<PERSON><PERSON>, English television host and director (b. 1977)", "html": "2010 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, English television host and director (b. 1977)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, English television host and director (b. 1977)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "2012", "text": "<PERSON>, American journalist and publisher (b. 1969)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American journalist and publisher (b. 1969)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American journalist and publisher (b. 1969)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2012", "text": "<PERSON><PERSON>, Italian journalist (b. 1932)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Italian journalist (b. 1932)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Italian journalist (b. 1932)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "2013", "text": "<PERSON>, American actress, dancer, and singer (b. 1944)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress, dancer, and singer (b. 1944)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress, dancer, and singer (b. 1944)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2014", "text": "<PERSON>, French director, cinematographer, and screenwriter (b. 1922)", "html": "2014 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French director, cinematographer, and screenwriter (b. 1922)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French director, cinematographer, and screenwriter (b. 1922)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2015", "text": "<PERSON><PERSON>, Cuban-American baseball player and coach (b. 1922)", "html": "2015 - <a href=\"https://wikipedia.org/wiki/Min<PERSON>_<PERSON>%C3%B1oso\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Cuban-American baseball player and coach (b. 1922)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>%C3%B1oso\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Cuban-American baseball player and coach (b. 1922)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Minnie_<PERSON>%C3%B1oso"}]}, {"year": "2016", "text": "<PERSON>, French writer, photographer and actress (b. 1952)", "html": "2016 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French writer, photographer and actress (b. 1952)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French writer, photographer and actress (b. 1952)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2018", "text": "<PERSON>, Mexican television, film and stage actress (b. 1934)", "html": "2018 - <a href=\"https://wikipedia.org/wiki/Mar%C3%ADa_Rubio\" title=\"<PERSON> Rubio\"><PERSON></a>, Mexican television, film and stage actress (b. 1934)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Mar%C3%ADa_Rubio\" title=\"<PERSON> Rubio\"><PERSON></a>, Mexican television, film and stage actress (b. 1934)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Mar%C3%ADa_Rubio"}]}, {"year": "2019", "text": "<PERSON>, Australian journalist and producer (b. 1942)", "html": "2019 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian journalist and producer (b. 1942)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian journalist and producer (b. 1942)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2023", "text": "<PERSON>, French footballer (b. 1933)", "html": "2023 - <a href=\"https://wikipedia.org/wiki/Just_Fontaine\" title=\"Just Fontaine\"><PERSON></a>, French footballer (b. 1933)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Just_Fontaine\" title=\"Just Fontaine\"><PERSON></a>, French footballer (b. 1933)", "links": [{"title": "Just <PERSON>", "link": "https://wikipedia.org/wiki/Just_Fontaine"}]}, {"year": "2024", "text": "<PERSON>,  American businesswoman, interior designer, and philanthropist (b. 1921)", "html": "2024 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Apf<PERSON>\"><PERSON></a>, American businesswoman, interior designer, and philanthropist (b. 1921)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Apf<PERSON>\"><PERSON></a>, American businesswoman, interior designer, and philanthropist (b. 1921)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Iris_Apfel"}]}, {"year": "2024", "text": "<PERSON>, Japanese manga artist (b. 1955)", "html": "2024 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Japanese manga artist (b. 1955)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Japanese manga artist (b. 1955)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2025", "text": "<PERSON>, Irish poet and television presenter (b. 1942)", "html": "2025 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Irish poet and television presenter (b. 1942)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Irish poet and television presenter (b. 1942)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Pat_Ingoldsby"}]}, {"year": "2025", "text": "<PERSON>, English singer-songwriter and guitarist (b. 1947)", "html": "2025 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English singer-songwriter and guitarist (b. 1947)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English singer-songwriter and guitarist (b. 1947)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2025", "text": "<PERSON>, American singer, songwriter, and actress (b. 1961)", "html": "2025 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer, songwriter, and actress (b. 1961)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Stone\"><PERSON></a>, American singer, songwriter, and actress (b. 1961)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}]}}