{"date": "November 1", "url": "https://wikipedia.org/wiki/November_1", "data": {"Events": [{"year": "365", "text": "The Alemanni cross the Rhine and invade Gaul. Emperor <PERSON><PERSON><PERSON> I moves to Paris to command the army and defend the Gallic cities.", "html": "365 - The <a href=\"https://wikipedia.org/wiki/Alemanni\" title=\"Alemanni\"><PERSON><PERSON><PERSON></a> cross the <a href=\"https://wikipedia.org/wiki/Rhine\" title=\"Rhine\">Rhine</a> and invade <a href=\"https://wikipedia.org/wiki/Gaul\" title=\"Gaul\">Gaul</a>. Emperor <a href=\"https://wikipedia.org/wiki/Valentinian_I\" title=\"Valentinian I\">Valentinian I</a> moves to <a href=\"https://wikipedia.org/wiki/Paris\" title=\"Paris\">Paris</a> to command the army and defend the Gallic cities.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Alemanni\" title=\"Alemanni\">Alemann<PERSON></a> cross the <a href=\"https://wikipedia.org/wiki/Rhine\" title=\"Rhine\">Rhine</a> and invade <a href=\"https://wikipedia.org/wiki/Gaul\" title=\"Gaul\">Gaul</a>. Emperor <a href=\"https://wikipedia.org/wiki/Valentinian_I\" title=\"Valentinian I\">Valentinian I</a> moves to <a href=\"https://wikipedia.org/wiki/Paris\" title=\"Paris\">Paris</a> to command the army and defend the Gallic cities.", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Al<PERSON>i"}, {"title": "Rhine", "link": "https://wikipedia.org/wiki/Rhine"}, {"title": "Gaul", "link": "https://wikipedia.org/wiki/Gaul"}, {"title": "Valentinian I", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_I"}, {"title": "Paris", "link": "https://wikipedia.org/wiki/Paris"}]}, {"year": "996", "text": "Emperor <PERSON> issues a deed to <PERSON><PERSON><PERSON><PERSON>, Bishop of Freising, which is the oldest known document using the name Ostarrîchi (Austria in Old High German).", "html": "996 - Emperor <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Holy_Roman_Emperor\" title=\"<PERSON> III, Holy Roman Emperor\"><PERSON> III</a> issues a deed to Gottschal<PERSON>, <a href=\"https://wikipedia.org/wiki/Roman_Catholic_Archdiocese_of_Munich_and_Freising\" title=\"Roman Catholic Archdiocese of Munich and Freising\">Bishop of Freising</a>, which is the oldest known document using the name <i><a href=\"https://wikipedia.org/wiki/Ostarr%C3%AEchi\" class=\"mw-redirect\" title=\"Ostarrîchi\">Ostarrîchi</a></i> (Austria in <a href=\"https://wikipedia.org/wiki/Old_High_German\" title=\"Old High German\">Old High German</a>).", "no_year_html": "Emperor <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Holy_Roman_Emperor\" title=\"<PERSON> III, Holy Roman Emperor\"><PERSON> III</a> issues a deed to Gottschal<PERSON>, <a href=\"https://wikipedia.org/wiki/Roman_Catholic_Archdiocese_of_Munich_and_Freising\" title=\"Roman Catholic Archdiocese of Munich and Freising\">Bishop of Freising</a>, which is the oldest known document using the name <i><a href=\"https://wikipedia.org/wiki/Ostarr%C3%AEchi\" class=\"mw-redirect\" title=\"Ostarrîchi\">Ostarrîchi</a></i> (Austria in <a href=\"https://wikipedia.org/wiki/Old_High_German\" title=\"Old High German\">Old High German</a>).", "links": [{"title": "<PERSON>, Holy Roman Emperor", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Holy_Roman_Emperor"}, {"title": "Roman Catholic Archdiocese of Munich and Freising", "link": "https://wikipedia.org/wiki/Roman_Catholic_Archdiocese_of_Munich_and_Freising"}, {"title": "Ostarrî<PERSON>", "link": "https://wikipedia.org/wiki/Ostarr%C3%AEchi"}, {"title": "Old High German", "link": "https://wikipedia.org/wiki/Old_High_German"}]}, {"year": "1009", "text": "Berber forces led by <PERSON><PERSON> defeat the Umayyad caliph <PERSON> of Córdoba in the battle of Alcolea.", "html": "1009 - <a href=\"https://wikipedia.org/wiki/Berbers\" title=\"Berbers\">Berber</a> forces led by <a href=\"https://wikipedia.org/wiki/Sulayman_ibn_<PERSON>-<PERSON>\" title=\"Sul<PERSON> ibn <PERSON>Ha<PERSON>\"><PERSON><PERSON> ibn <PERSON></a> defeat the <a href=\"https://wikipedia.org/wiki/Umayyad_Caliphate\" title=\"Umayyad Caliphate\">Umayyad</a> caliph <a href=\"https://wikipedia.org/wiki/Muhammad_II_of_C%C3%B3rdoba\" title=\"<PERSON> II of Córdoba\"><PERSON> of Córdoba</a> in the battle of <a href=\"https://wikipedia.org/wiki/Alcolea\" title=\"Alcolea\">Alcolea</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Berbers\" title=\"Berbers\">Berber</a> forces led by <a href=\"https://wikipedia.org/wiki/Sulayman_ibn_al-<PERSON>\" title=\"Sul<PERSON> ibn al-Hakam\"><PERSON><PERSON> ibn <PERSON></a> defeat the <a href=\"https://wikipedia.org/wiki/Umayyad_Caliphate\" title=\"Umayyad Caliphate\">Umayyad</a> caliph <a href=\"https://wikipedia.org/wiki/Muhammad_II_of_C%C3%B3rdoba\" title=\"<PERSON> II of Córdoba\"><PERSON> of Córdoba</a> in the battle of <a href=\"https://wikipedia.org/wiki/Alcolea\" title=\"Alcolea\">Alcolea</a>.", "links": [{"title": "Berbers", "link": "https://wikipedia.org/wiki/Berbers"}, {"title": "<PERSON><PERSON> ibn <PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>-<PERSON>"}, {"title": "Umayyad Caliphate", "link": "https://wikipedia.org/wiki/Umayyad_Caliphate"}, {"title": "<PERSON> of Córdoba", "link": "https://wikipedia.org/wiki/Muhammad_II_of_C%C3%B3rdoba"}, {"title": "Alcolea", "link": "https://wikipedia.org/wiki/Alcolea"}]}, {"year": "1141", "text": "Empress <PERSON>'s reign as 'Lady of the English' ends with <PERSON> Blois regaining the title of 'King of England'.", "html": "1141 - <a href=\"https://wikipedia.org/wiki/Empress_<PERSON>\" title=\"Empress <PERSON>\">Empress <PERSON></a>'s reign as 'Lady of the English' ends with <a href=\"https://wikipedia.org/wiki/<PERSON>,_King_of_England\" title=\"<PERSON>, King of England\"><PERSON> of Blois</a> regaining the title of 'King of <a href=\"https://wikipedia.org/wiki/Kingdom_of_England\" title=\"Kingdom of England\">England</a>'.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Empress_<PERSON>\" title=\"Empress <PERSON>\">Empress <PERSON></a>'s reign as 'Lady of the English' ends with <a href=\"https://wikipedia.org/wiki/<PERSON>,_King_of_England\" title=\"<PERSON>, King of England\"><PERSON> of Blois</a> regaining the title of 'King of <a href=\"https://wikipedia.org/wiki/Kingdom_of_England\" title=\"Kingdom of England\">England</a>'.", "links": [{"title": "Empress <PERSON>", "link": "https://wikipedia.org/wiki/Empress_Matilda"}, {"title": "<PERSON>, King of England", "link": "https://wikipedia.org/wiki/<PERSON>,_King_of_England"}, {"title": "Kingdom of England", "link": "https://wikipedia.org/wiki/Kingdom_of_England"}]}, {"year": "1179", "text": "<PERSON> is crowned as 'King of France'.", "html": "1179 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_France\" title=\"<PERSON> II of France\"><PERSON> II</a> is <a href=\"https://wikipedia.org/wiki/Coronation\" title=\"Coronation\">crowned</a> as 'King of <a href=\"https://wikipedia.org/wiki/France_in_the_Middle_Ages\" title=\"France in the Middle Ages\">France</a>'.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_France\" title=\"<PERSON> II of France\"><PERSON> II</a> is <a href=\"https://wikipedia.org/wiki/Coronation\" title=\"Coronation\">crowned</a> as 'King of <a href=\"https://wikipedia.org/wiki/France_in_the_Middle_Ages\" title=\"France in the Middle Ages\">France</a>'.", "links": [{"title": "<PERSON> of France", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_France"}, {"title": "Coronation", "link": "https://wikipedia.org/wiki/Coronation"}, {"title": "France in the Middle Ages", "link": "https://wikipedia.org/wiki/France_in_the_Middle_Ages"}]}, {"year": "1214", "text": "The port city of Sinope surrenders to the Seljuq Turks.", "html": "1214 - The port city of <a href=\"https://wikipedia.org/wiki/Sinop,_Turkey\" title=\"Sinop, Turkey\">Sinope</a> <a href=\"https://wikipedia.org/wiki/Siege_of_Sinope\" title=\"Siege of Sinope\">surrenders</a> to the <a href=\"https://wikipedia.org/wiki/Seljuq_Turks\" class=\"mw-redirect\" title=\"Seljuq Turks\">Seljuq Turks</a>.", "no_year_html": "The port city of <a href=\"https://wikipedia.org/wiki/Sinop,_Turkey\" title=\"Sinop, Turkey\">Sinope</a> <a href=\"https://wikipedia.org/wiki/Siege_of_Sinope\" title=\"Siege of Sinope\">surrenders</a> to the <a href=\"https://wikipedia.org/wiki/Seljuq_Turks\" class=\"mw-redirect\" title=\"Seljuq Turks\">Seljuq Turks</a>.", "links": [{"title": "Sinop, Turkey", "link": "https://wikipedia.org/wiki/Sinop,_Turkey"}, {"title": "Siege of Sinope", "link": "https://wikipedia.org/wiki/Siege_of_Sinope"}, {"title": "Seljuq Turks", "link": "https://wikipedia.org/wiki/Seljuq_Turks"}]}, {"year": "1348", "text": "The anti-royalist Union of Valencia attacks the Jews of Murviedro on the pretext that they are serfs of the King of Valencia and thus \"royalists\".", "html": "1348 - The anti-royalist <a href=\"https://wikipedia.org/wiki/Union_of_Valencia\" title=\"Union of Valencia\">Union of Valencia</a> attacks the Jews of <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a> on the pretext that they are <a href=\"https://wikipedia.org/wiki/Serf\" class=\"mw-redirect\" title=\"Serf\">serfs</a> of the <a href=\"https://wikipedia.org/wiki/King_of_Valencia\" class=\"mw-redirect\" title=\"King of Valencia\">King of Valencia</a> and thus \"royalists\".", "no_year_html": "The anti-royalist <a href=\"https://wikipedia.org/wiki/Union_of_Valencia\" title=\"Union of Valencia\">Union of Valencia</a> attacks the Jews of <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a> on the pretext that they are <a href=\"https://wikipedia.org/wiki/Serf\" class=\"mw-redirect\" title=\"Serf\">serfs</a> of the <a href=\"https://wikipedia.org/wiki/King_of_Valencia\" class=\"mw-redirect\" title=\"King of Valencia\">King of Valencia</a> and thus \"royalists\".", "links": [{"title": "Union of Valencia", "link": "https://wikipedia.org/wiki/Union_of_Valencia"}, {"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>"}, {"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Serf"}, {"title": "King of Valencia", "link": "https://wikipedia.org/wiki/King_of_Valencia"}]}, {"year": "1503", "text": "<PERSON> is elected.", "html": "1503 - <a href=\"https://wikipedia.org/wiki/<PERSON>_Julius_II\" title=\"Pope Julius II\">Pope <PERSON> II</a> is elected.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_II\" title=\"Pope Julius II\">Pope <PERSON> II</a> is elected.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1512", "text": "The ceiling of the Sistine Chapel, painted by <PERSON><PERSON>, is exhibited to the public for the first time.", "html": "1512 - The ceiling of the <a href=\"https://wikipedia.org/wiki/Sistine_Chapel\" title=\"Sistine Chapel\">Sistine Chapel</a>, painted by <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, is exhibited to the public for the first time.", "no_year_html": "The ceiling of the <a href=\"https://wikipedia.org/wiki/Sistine_Chapel\" title=\"Sistine Chapel\">Sistine Chapel</a>, painted by <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, is exhibited to the public for the first time.", "links": [{"title": "Sistine Chapel", "link": "https://wikipedia.org/wiki/Sistine_Chapel"}, {"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>"}]}, {"year": "1520", "text": "The Strait of Magellan, the passage immediately south of mainland South America connecting the Pacific and the Atlantic Oceans, is first discovered and navigated by European explorer <PERSON> during the first recorded circumnavigation voyage.", "html": "1520 - The <a href=\"https://wikipedia.org/wiki/Strait_of_Magellan\" title=\"Strait of Magellan\">Strait of Magellan</a>, the passage immediately south of mainland South America connecting the Pacific and the Atlantic Oceans, is first discovered and navigated by European explorer <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> during the first recorded <a href=\"https://wikipedia.org/wiki/Circumnavigation\" title=\"Circumnavigation\">circumnavigation</a> voyage.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Strait_of_Magellan\" title=\"Strait of Magellan\">Strait of Magellan</a>, the passage immediately south of mainland South America connecting the Pacific and the Atlantic Oceans, is first discovered and navigated by European explorer <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> during the first recorded <a href=\"https://wikipedia.org/wiki/Circumnavigation\" title=\"Circumnavigation\">circumnavigation</a> voyage.", "links": [{"title": "Strait of Magellan", "link": "https://wikipedia.org/wiki/Strait_of_Magellan"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Circumnavigation", "link": "https://wikipedia.org/wiki/Circumnavigation"}]}, {"year": "1555", "text": "French Huguenots establish the France Antarctique colony in present-day Rio de Janeiro, Brazil.", "html": "1555 - French <a href=\"https://wikipedia.org/wiki/Huguenot\" class=\"mw-redirect\" title=\"Huguenot\">Huguenots</a> establish the <a href=\"https://wikipedia.org/wiki/France_Antarctique\" title=\"France Antarctique\">France Antarctique</a> colony in present-day <a href=\"https://wikipedia.org/wiki/Rio_de_Janeiro\" title=\"Rio de Janeiro\">Rio de Janeiro</a>, Brazil.", "no_year_html": "French <a href=\"https://wikipedia.org/wiki/Huguenot\" class=\"mw-redirect\" title=\"Huguenot\">Huguenots</a> establish the <a href=\"https://wikipedia.org/wiki/France_Antarctique\" title=\"France Antarctique\">France Antarctique</a> colony in present-day <a href=\"https://wikipedia.org/wiki/Rio_de_Janeiro\" title=\"Rio de Janeiro\">Rio de Janeiro</a>, Brazil.", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>not"}, {"title": "France Antarctique", "link": "https://wikipedia.org/wiki/France_Antarctique"}, {"title": "Rio de Janeiro", "link": "https://wikipedia.org/wiki/Rio_de_Janeiro"}]}, {"year": "1570", "text": "The All Saints' Flood devastates the Dutch coast.", "html": "1570 - The <a href=\"https://wikipedia.org/wiki/All_Saints%27_Flood_(1570)\" title=\"All Saints' Flood (1570)\">All Saints' Flood</a> devastates the Dutch coast.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/All_Saints%27_Flood_(1570)\" title=\"All Saints' Flood (1570)\">All Saints' Flood</a> devastates the Dutch coast.", "links": [{"title": "All Saints' Flood (1570)", "link": "https://wikipedia.org/wiki/All_Saints%27_Flood_(1570)"}]}, {"year": "1604", "text": "<PERSON>'s tragedy <PERSON><PERSON><PERSON> is performed for the first time, at Whitehall Palace in London.", "html": "1604 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"William Shakespeare\"><PERSON></a>'s tragedy <i><a href=\"https://wikipedia.org/wiki/Othello\" title=\"Othello\">Othello</a></i> is performed for the first time, at <a href=\"https://wikipedia.org/wiki/Whitehall_Palace\" class=\"mw-redirect\" title=\"Whitehall Palace\">Whitehall Palace</a> in London.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_Shakespeare\" title=\"William Shakespeare\"><PERSON></a>'s tragedy <i><a href=\"https://wikipedia.org/wiki/Othello\" title=\"Othello\">Othello</a></i> is performed for the first time, at <a href=\"https://wikipedia.org/wiki/Whitehall_Palace\" class=\"mw-redirect\" title=\"Whitehall Palace\">Whitehall Palace</a> in London.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Othello"}, {"title": "Whitehall Palace", "link": "https://wikipedia.org/wiki/Whitehall_Palace"}]}, {"year": "1611", "text": "<PERSON>'s play The Tempest is performed for the first time, at Whitehall Palace in London.", "html": "1611 - <PERSON>'s play <i><a href=\"https://wikipedia.org/wiki/The_Tempest\" title=\"The Tempest\">The Tempest</a></i> is performed for the first time, at Whitehall Palace in London.", "no_year_html": "Shakespeare's play <i><a href=\"https://wikipedia.org/wiki/The_Tempest\" title=\"The Tempest\">The Tempest</a></i> is performed for the first time, at Whitehall Palace in London.", "links": [{"title": "The Tempest", "link": "https://wikipedia.org/wiki/The_Tempest"}]}, {"year": "1612", "text": "During the Time of Troubles, Polish troops are expelled from Moscow's Kitay-gorod by Russian troops under the command of <PERSON> (22 October O.S.).", "html": "1612 - During the <a href=\"https://wikipedia.org/wiki/Time_of_Troubles\" title=\"Time of Troubles\">Time of Troubles</a>, Polish troops are expelled from Moscow's <a href=\"https://wikipedia.org/wiki/Kitay-gorod\" title=\"Kitay-gorod\">Kitay-gorod</a> by Russian troops under the command of <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> (<a href=\"https://wikipedia.org/wiki/22_October\" class=\"mw-redirect\" title=\"22 October\">22 October</a> <a href=\"https://wikipedia.org/wiki/Old_Style\" class=\"mw-redirect\" title=\"Old Style\">O.S.</a>).", "no_year_html": "During the <a href=\"https://wikipedia.org/wiki/Time_of_Troubles\" title=\"Time of Troubles\">Time of Troubles</a>, Polish troops are expelled from Moscow's <a href=\"https://wikipedia.org/wiki/Kitay-gorod\" title=\"Kitay-gorod\">Kitay-gorod</a> by Russian troops under the command of <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> (<a href=\"https://wikipedia.org/wiki/22_October\" class=\"mw-redirect\" title=\"22 October\">22 October</a> <a href=\"https://wikipedia.org/wiki/Old_Style\" class=\"mw-redirect\" title=\"Old Style\">O.S.</a>).", "links": [{"title": "Time of Troubles", "link": "https://wikipedia.org/wiki/Time_of_Troubles"}, {"title": "<PERSON><PERSON>-gorod", "link": "https://wikipedia.org/wiki/Kitay-gorod"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "22 October", "link": "https://wikipedia.org/wiki/22_October"}, {"title": "Old Style", "link": "https://wikipedia.org/wiki/Old_Style"}]}, {"year": "1683", "text": "The British Crown colony of New York is subdivided into 12 counties.", "html": "1683 - The British Crown <a href=\"https://wikipedia.org/wiki/Province_of_New_York\" title=\"Province of New York\">colony of New York</a> is subdivided into 12 <a href=\"https://wikipedia.org/wiki/List_of_counties_in_New_York\" title=\"List of counties in New York\">counties</a>.", "no_year_html": "The British Crown <a href=\"https://wikipedia.org/wiki/Province_of_New_York\" title=\"Province of New York\">colony of New York</a> is subdivided into 12 <a href=\"https://wikipedia.org/wiki/List_of_counties_in_New_York\" title=\"List of counties in New York\">counties</a>.", "links": [{"title": "Province of New York", "link": "https://wikipedia.org/wiki/Province_of_New_York"}, {"title": "List of counties in New York", "link": "https://wikipedia.org/wiki/List_of_counties_in_New_York"}]}, {"year": "1688", "text": "<PERSON> of Orange sets out a second time from Hellevoetsluis in the Netherlands to seize the crowns of England, Scotland and Ireland from King <PERSON> of England during the Glorious Revolution.", "html": "1688 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Orange\" class=\"mw-redirect\" title=\"William III of Orange\"><PERSON> of Orange</a> sets out a second time from <a href=\"https://wikipedia.org/wiki/Hell<PERSON>etsluis\" title=\"Hellevoetsluis\">Hellevoetsluis</a> in the Netherlands to seize the crowns of England, Scotland and Ireland from <a href=\"https://wikipedia.org/wiki/King_<PERSON>_<PERSON>_of_England\" class=\"mw-redirect\" title=\"King <PERSON> of England\">King <PERSON> of England</a> during the <a href=\"https://wikipedia.org/wiki/Glorious_Revolution\" title=\"Glorious Revolution\">Glorious Revolution</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Orange\" class=\"mw-redirect\" title=\"William III of Orange\"><PERSON> of Orange</a> sets out a second time from <a href=\"https://wikipedia.org/wiki/Hell<PERSON>etsluis\" title=\"Hell<PERSON>etslu<PERSON>\">Hellevoetsluis</a> in the Netherlands to seize the crowns of England, Scotland and Ireland from <a href=\"https://wikipedia.org/wiki/King_<PERSON>_II_of_England\" class=\"mw-redirect\" title=\"King <PERSON> of England\">King <PERSON> of England</a> during the <a href=\"https://wikipedia.org/wiki/Glorious_Revolution\" title=\"Glorious Revolution\">Glorious Revolution</a>.", "links": [{"title": "<PERSON> of Orange", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Orange"}, {"title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Hellevoetsluis"}, {"title": "King <PERSON> of England", "link": "https://wikipedia.org/wiki/King_<PERSON>_<PERSON>_of_England"}, {"title": "Glorious Revolution", "link": "https://wikipedia.org/wiki/Glorious_Revolution"}]}, {"year": "1755", "text": "In Portugal, Lisbon is totally devastated by a massive earthquake and tsunami, killing an estimated 40,000 to 60,000 people.", "html": "1755 - In Portugal, <a href=\"https://wikipedia.org/wiki/Lisbon\" title=\"Lisbon\">Lisbon</a> is <a href=\"https://wikipedia.org/wiki/1755_Lisbon_earthquake\" title=\"1755 Lisbon earthquake\">totally devastated</a> by a massive earthquake and <a href=\"https://wikipedia.org/wiki/Tsunami\" title=\"Tsunami\">tsunami</a>, killing an estimated 40,000 to 60,000 people.", "no_year_html": "In Portugal, <a href=\"https://wikipedia.org/wiki/Lisbon\" title=\"Lisbon\">Lisbon</a> is <a href=\"https://wikipedia.org/wiki/1755_Lisbon_earthquake\" title=\"1755 Lisbon earthquake\">totally devastated</a> by a massive earthquake and <a href=\"https://wikipedia.org/wiki/Tsunami\" title=\"Tsunami\">tsunami</a>, killing an estimated 40,000 to 60,000 people.", "links": [{"title": "Lisbon", "link": "https://wikipedia.org/wiki/Lisbon"}, {"title": "1755 Lisbon earthquake", "link": "https://wikipedia.org/wiki/1755_Lisbon_earthquake"}, {"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Tsunami"}]}, {"year": "1765", "text": "The British Parliament enacts the Stamp Act on the Thirteen Colonies in order to help pay for British military operations in North America.", "html": "1765 - The <a href=\"https://wikipedia.org/wiki/Parliament_of_Great_Britain\" title=\"Parliament of Great Britain\">British Parliament</a> enacts the <a href=\"https://wikipedia.org/wiki/Stamp_Act_1765\" title=\"Stamp Act 1765\">Stamp Act</a> on the <a href=\"https://wikipedia.org/wiki/Thirteen_Colonies\" title=\"Thirteen Colonies\">Thirteen Colonies</a> in order to help pay for British military operations in North America.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Parliament_of_Great_Britain\" title=\"Parliament of Great Britain\">British Parliament</a> enacts the <a href=\"https://wikipedia.org/wiki/Stamp_Act_1765\" title=\"Stamp Act 1765\">Stamp Act</a> on the <a href=\"https://wikipedia.org/wiki/Thirteen_Colonies\" title=\"Thirteen Colonies\">Thirteen Colonies</a> in order to help pay for British military operations in North America.", "links": [{"title": "Parliament of Great Britain", "link": "https://wikipedia.org/wiki/Parliament_of_Great_Britain"}, {"title": "Stamp Act 1765", "link": "https://wikipedia.org/wiki/Stamp_Act_1765"}, {"title": "Thirteen Colonies", "link": "https://wikipedia.org/wiki/Thirteen_Colonies"}]}, {"year": "1790", "text": "<PERSON> publishes Reflections on the Revolution in France, in which he predicts that the French Revolution will end in a disaster.", "html": "1790 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> publishes <i><a href=\"https://wikipedia.org/wiki/Reflections_on_the_Revolution_in_France\" title=\"Reflections on the Revolution in France\">Reflections on the Revolution in France</a></i>, in which he predicts that the <a href=\"https://wikipedia.org/wiki/French_Revolution\" title=\"French Revolution\">French Revolution</a> will end in a disaster.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> publishes <i><a href=\"https://wikipedia.org/wiki/Reflections_on_the_Revolution_in_France\" title=\"Reflections on the Revolution in France\">Reflections on the Revolution in France</a></i>, in which he predicts that the <a href=\"https://wikipedia.org/wiki/French_Revolution\" title=\"French Revolution\">French Revolution</a> will end in a disaster.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Reflections on the Revolution in France", "link": "https://wikipedia.org/wiki/Reflections_on_the_Revolution_in_France"}, {"title": "French Revolution", "link": "https://wikipedia.org/wiki/French_Revolution"}]}, {"year": "1800", "text": "<PERSON> becomes the first President of the United States to live in the Executive Mansion (later renamed the White House).", "html": "1800 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> becomes the first President of the United States to live in the Executive Mansion (later renamed the <a href=\"https://wikipedia.org/wiki/White_House\" title=\"White House\">White House</a>).", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> becomes the first President of the United States to live in the Executive Mansion (later renamed the <a href=\"https://wikipedia.org/wiki/White_House\" title=\"White House\">White House</a>).", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "White House", "link": "https://wikipedia.org/wiki/White_House"}]}, {"year": "1805", "text": "<PERSON> invades Austria during the War of the Third Coalition.", "html": "1805 - <a href=\"https://wikipedia.org/wiki/Napoleon_I\" class=\"mw-redirect\" title=\"Napoleon I\"><PERSON></a> invades <a href=\"https://wikipedia.org/wiki/Austrian_Empire\" title=\"Austrian Empire\">Austria</a> during the <a href=\"https://wikipedia.org/wiki/War_of_the_Third_Coalition\" title=\"War of the Third Coalition\">War of the Third Coalition</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Napoleon_I\" class=\"mw-redirect\" title=\"Napoleon I\"><PERSON></a> invades <a href=\"https://wikipedia.org/wiki/Austrian_Empire\" title=\"Austrian Empire\">Austria</a> during the <a href=\"https://wikipedia.org/wiki/War_of_the_Third_Coalition\" title=\"War of the Third Coalition\">War of the Third Coalition</a>.", "links": [{"title": "<PERSON> I", "link": "https://wikipedia.org/wiki/<PERSON>_I"}, {"title": "Austrian Empire", "link": "https://wikipedia.org/wiki/Austrian_Empire"}, {"title": "War of the Third Coalition", "link": "https://wikipedia.org/wiki/War_of_the_Third_Coalition"}]}, {"year": "1814", "text": "Congress of Vienna opens to re-draw the European political map after the defeat of France in the Napoleonic Wars.", "html": "1814 - <a href=\"https://wikipedia.org/wiki/Congress_of_Vienna\" title=\"Congress of Vienna\">Congress of Vienna</a> opens to re-draw the European political map after the defeat of France in the <a href=\"https://wikipedia.org/wiki/Napoleonic_Wars\" title=\"Napoleonic Wars\">Napoleonic Wars</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Congress_of_Vienna\" title=\"Congress of Vienna\">Congress of Vienna</a> opens to re-draw the European political map after the defeat of France in the <a href=\"https://wikipedia.org/wiki/Napoleonic_Wars\" title=\"Napoleonic Wars\">Napoleonic Wars</a>.", "links": [{"title": "Congress of Vienna", "link": "https://wikipedia.org/wiki/Congress_of_Vienna"}, {"title": "Napoleonic Wars", "link": "https://wikipedia.org/wiki/Napoleonic_Wars"}]}, {"year": "1848", "text": "In Boston, Massachusetts, the first medical school for women, Boston Female Medical School (which later merged with the Boston University School of Medicine), opens.", "html": "1848 - In <a href=\"https://wikipedia.org/wiki/Boston\" title=\"Boston\">Boston</a>, <a href=\"https://wikipedia.org/wiki/Massachusetts\" title=\"Massachusetts\">Massachusetts</a>, the first <a href=\"https://wikipedia.org/wiki/Medical_school\" title=\"Medical school\">medical school</a> for women, Boston Female Medical School (which later merged with the <a href=\"https://wikipedia.org/wiki/Boston_University_School_of_Medicine\" title=\"Boston University School of Medicine\">Boston University School of Medicine</a>), opens.", "no_year_html": "In <a href=\"https://wikipedia.org/wiki/Boston\" title=\"Boston\">Boston</a>, <a href=\"https://wikipedia.org/wiki/Massachusetts\" title=\"Massachusetts\">Massachusetts</a>, the first <a href=\"https://wikipedia.org/wiki/Medical_school\" title=\"Medical school\">medical school</a> for women, Boston Female Medical School (which later merged with the <a href=\"https://wikipedia.org/wiki/Boston_University_School_of_Medicine\" title=\"Boston University School of Medicine\">Boston University School of Medicine</a>), opens.", "links": [{"title": "Boston", "link": "https://wikipedia.org/wiki/Boston"}, {"title": "Massachusetts", "link": "https://wikipedia.org/wiki/Massachusetts"}, {"title": "Medical school", "link": "https://wikipedia.org/wiki/Medical_school"}, {"title": "Boston University School of Medicine", "link": "https://wikipedia.org/wiki/Boston_University_School_of_Medicine"}]}, {"year": "1861", "text": "American Civil War: U.S. President <PERSON> appoints <PERSON> as the commander of the Union Army, replacing General <PERSON>.", "html": "1861 - <a href=\"https://wikipedia.org/wiki/American_Civil_War\" title=\"American Civil War\">American Civil War</a>: U.S. President <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> appoints <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> as the commander of the <a href=\"https://wikipedia.org/wiki/Union_Army\" class=\"mw-redirect\" title=\"Union Army\">Union Army</a>, replacing General <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/American_Civil_War\" title=\"American Civil War\">American Civil War</a>: U.S. President <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"Abraham <PERSON>\"><PERSON></a> appoints <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> as the commander of the <a href=\"https://wikipedia.org/wiki/Union_Army\" class=\"mw-redirect\" title=\"Union Army\">Union Army</a>, replacing General <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>.", "links": [{"title": "American Civil War", "link": "https://wikipedia.org/wiki/American_Civil_War"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>"}, {"title": "Union Army", "link": "https://wikipedia.org/wiki/Union_Army"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1870", "text": "In the United States, the Weather Bureau (later renamed the National Weather Service) makes its first official meteorological forecast.", "html": "1870 - In the United States, the Weather Bureau (later renamed the <a href=\"https://wikipedia.org/wiki/National_Weather_Service\" title=\"National Weather Service\">National Weather Service</a>) makes its first official <a href=\"https://wikipedia.org/wiki/Meteorology\" title=\"Meteorology\">meteorological</a> forecast.", "no_year_html": "In the United States, the Weather Bureau (later renamed the <a href=\"https://wikipedia.org/wiki/National_Weather_Service\" title=\"National Weather Service\">National Weather Service</a>) makes its first official <a href=\"https://wikipedia.org/wiki/Meteorology\" title=\"Meteorology\">meteorological</a> forecast.", "links": [{"title": "National Weather Service", "link": "https://wikipedia.org/wiki/National_Weather_Service"}, {"title": "Meteorology", "link": "https://wikipedia.org/wiki/Meteorology"}]}, {"year": "1893", "text": "The Battle of Bembezi took place and was the most decisive battle won by the British in the First Matabele War of 1893.", "html": "1893 - The <a href=\"https://wikipedia.org/wiki/Battle_of_Bembezi\" title=\"Battle of Bembezi\">Battle of Bembezi</a> took place and was the most decisive battle won by the <a href=\"https://wikipedia.org/wiki/United_Kingdom_of_Great_Britain_and_Ireland\" title=\"United Kingdom of Great Britain and Ireland\">British</a> in the <a href=\"https://wikipedia.org/wiki/First_Matabele_War\" title=\"First Matabele War\">First Matabele War</a> of 1893.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Battle_of_Bembezi\" title=\"Battle of Bembezi\">Battle of Bembezi</a> took place and was the most decisive battle won by the <a href=\"https://wikipedia.org/wiki/United_Kingdom_of_Great_Britain_and_Ireland\" title=\"United Kingdom of Great Britain and Ireland\">British</a> in the <a href=\"https://wikipedia.org/wiki/First_Matabele_War\" title=\"First Matabele War\">First Matabele War</a> of 1893.", "links": [{"title": "Battle of Bembezi", "link": "https://wikipedia.org/wiki/Battle_of_Bembezi"}, {"title": "United Kingdom of Great Britain and Ireland", "link": "https://wikipedia.org/wiki/United_Kingdom_of_Great_Britain_and_Ireland"}, {"title": "First Matabele War", "link": "https://wikipedia.org/wiki/First_Matabele_War"}]}, {"year": "1894", "text": "<PERSON> becomes the new (and last) Tsar of Russia after his father, <PERSON>, dies.", "html": "1894 - <a href=\"https://wikipedia.org/wiki/<PERSON>_II_of_Russia\" class=\"mw-redirect\" title=\"<PERSON> II of Russia\"><PERSON> II</a> becomes the new (and last) <a href=\"https://wikipedia.org/wiki/Russian_Empire\" title=\"Russian Empire\">Tsar of Russia</a> after his father, <a href=\"https://wikipedia.org/wiki/<PERSON>_III_of_Russia\" title=\"<PERSON> III of Russia\"><PERSON> III</a>, dies.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_II_of_Russia\" class=\"mw-redirect\" title=\"Nicholas II of Russia\"><PERSON> II</a> becomes the new (and last) <a href=\"https://wikipedia.org/wiki/Russian_Empire\" title=\"Russian Empire\">Tsar of Russia</a> after his father, <a href=\"https://wikipedia.org/wiki/<PERSON>_III_of_Russia\" title=\"<PERSON> III of Russia\"><PERSON> III</a>, dies.", "links": [{"title": "<PERSON> of Russia", "link": "https://wikipedia.org/wiki/<PERSON>_II_of_Russia"}, {"title": "Russian Empire", "link": "https://wikipedia.org/wiki/Russian_Empire"}, {"title": "<PERSON> of Russia", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Russia"}]}, {"year": "1894", "text": "<PERSON>, 15 of his Native Americans, and <PERSON> were filmed by <PERSON> in his Black Maria Studio in West Orange, New Jersey.", "html": "1894 - <a href=\"https://wikipedia.org/wiki/<PERSON>_Bill\" title=\"Buffalo Bill\"><PERSON> Bill</a>, 15 of his Native Americans, and <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> were filmed by <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Thomas Edison\"><PERSON></a> in his <a href=\"https://wikipedia.org/wiki/Edison%27s_Black_Maria\" title=\"Edison's Black Maria\">Black Maria Studio</a> in <a href=\"https://wikipedia.org/wiki/West_Orange,_New_Jersey\" title=\"West Orange, New Jersey\">West Orange, New Jersey</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_Bill\" title=\"Buffalo Bill\"><PERSON> Bill</a>, 15 of his Native Americans, and <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> were filmed by <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Thomas Edison\"><PERSON></a> in his <a href=\"https://wikipedia.org/wiki/Edison%27s_Black_Maria\" title=\"Edison's Black Maria\">Black Maria Studio</a> in <a href=\"https://wikipedia.org/wiki/West_Orange,_New_Jersey\" title=\"West Orange, New Jersey\">West Orange, New Jersey</a>.", "links": [{"title": "Buffalo Bill", "link": "https://wikipedia.org/wiki/Buffalo_Bill"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Thomas <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Edison's Black Maria", "link": "https://wikipedia.org/wiki/Edison%27s_Black_Maria"}, {"title": "West Orange, New Jersey", "link": "https://wikipedia.org/wiki/West_Orange,_New_Jersey"}]}, {"year": "1896", "text": "A picture showing the bare breasts of a woman appears in National Geographic magazine for the first time.", "html": "1896 - A picture showing the bare breasts of a woman appears in <i><a href=\"https://wikipedia.org/wiki/National_Geographic_(magazine)\" class=\"mw-redirect\" title=\"National Geographic (magazine)\">National Geographic</a></i> magazine for the first time.", "no_year_html": "A picture showing the bare breasts of a woman appears in <i><a href=\"https://wikipedia.org/wiki/National_Geographic_(magazine)\" class=\"mw-redirect\" title=\"National Geographic (magazine)\">National Geographic</a></i> magazine for the first time.", "links": [{"title": "National Geographic (magazine)", "link": "https://wikipedia.org/wiki/National_Geographic_(magazine)"}]}, {"year": "1897", "text": "The first Library of Congress building opens its doors to the public; the library had previously been housed in the Congressional Reading Room in the U.S. Capitol.", "html": "1897 - The first <a href=\"https://wikipedia.org/wiki/Library_of_Congress\" title=\"Library of Congress\">Library of Congress</a> building opens its doors to the public; the library had previously been housed in the Congressional Reading Room in the <a href=\"https://wikipedia.org/wiki/United_States_Capitol\" title=\"United States Capitol\">U.S. Capitol</a>.", "no_year_html": "The first <a href=\"https://wikipedia.org/wiki/Library_of_Congress\" title=\"Library of Congress\">Library of Congress</a> building opens its doors to the public; the library had previously been housed in the Congressional Reading Room in the <a href=\"https://wikipedia.org/wiki/United_States_Capitol\" title=\"United States Capitol\">U.S. Capitol</a>.", "links": [{"title": "Library of Congress", "link": "https://wikipedia.org/wiki/Library_of_Congress"}, {"title": "United States Capitol", "link": "https://wikipedia.org/wiki/United_States_Capitol"}]}, {"year": "1897", "text": "Italian Sport-Club Juventus is founded by a group of students of Liceo Classico Massimo d'Azeglio.", "html": "1897 - Italian <a href=\"https://wikipedia.org/wiki/Juventus_F.C.\" class=\"mw-redirect\" title=\"Juventus F.C.\">Sport-Club Juventus</a> is founded by a group of students of <a href=\"https://wikipedia.org/wiki/Liceo_Classico_Massimo_d%27Azeglio\" title=\"Liceo Classico Massimo d'Azeglio\">Liceo Classico Massimo d'Azeglio</a>.", "no_year_html": "Italian <a href=\"https://wikipedia.org/wiki/Juventus_F.C.\" class=\"mw-redirect\" title=\"Juventus F.C.\">Sport-Club Juventus</a> is founded by a group of students of <a href=\"https://wikipedia.org/wiki/Liceo_Classico_Massimo_d%27Azeglio\" title=\"Liceo Classico Massimo d'Azeglio\">Liceo Classico Massimo d'Azeglio</a>.", "links": [{"title": "Juventus F.C.", "link": "https://wikipedia.org/wiki/Juventus_F.C."}, {"title": "Liceo Classico Massimo d'Azeglio", "link": "https://wikipedia.org/wiki/Liceo_Classico_Massimo_d%27Azeglio"}]}, {"year": "1905", "text": "Lahti, a city in Finland, is granted city rights by Tsar <PERSON> of Russia, the last Grand Duke of Finland.", "html": "1905 - <a href=\"https://wikipedia.org/wiki/Lahti\" title=\"Lahti\">Lahti</a>, a city in Finland, is granted city rights by Tsar <a href=\"https://wikipedia.org/wiki/<PERSON>_II_of_Russia\" class=\"mw-redirect\" title=\"<PERSON> II of Russia\"><PERSON> of Russia</a>, the last Grand Duke of Finland.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Lahti\" title=\"Lahti\">Lahti</a>, a city in Finland, is granted city rights by Tsar <a href=\"https://wikipedia.org/wiki/<PERSON>_II_of_Russia\" class=\"mw-redirect\" title=\"<PERSON> II of Russia\"><PERSON> of Russia</a>, the last Grand Duke of Finland.", "links": [{"title": "La<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Lahti"}, {"title": "<PERSON> of Russia", "link": "https://wikipedia.org/wiki/<PERSON>_II_of_Russia"}]}, {"year": "1911", "text": "World's first combat aerial bombing mission takes place in Libya during the Italo-Turkish War. Second Lieutenant <PERSON><PERSON><PERSON> of Italy drops several small bombs.", "html": "1911 - World's first combat aerial bombing mission takes place in Libya during the <a href=\"https://wikipedia.org/wiki/Italo-Turkish_War\" title=\"Italo-Turkish War\">Italo-Turkish War</a>. Second Lieutenant <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a> of <a href=\"https://wikipedia.org/wiki/Kingdom_of_Italy\" title=\"Kingdom of Italy\">Italy</a> drops several small bombs.", "no_year_html": "World's first combat aerial bombing mission takes place in Libya during the <a href=\"https://wikipedia.org/wiki/Italo-Turkish_War\" title=\"Italo-Turkish War\">Italo-Turkish War</a>. Second Lieutenant <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a> of <a href=\"https://wikipedia.org/wiki/Kingdom_of_Italy\" title=\"Kingdom of Italy\">Italy</a> drops several small bombs.", "links": [{"title": "Italo-Turkish War", "link": "https://wikipedia.org/wiki/Italo-Turkish_War"}, {"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}, {"title": "Kingdom of Italy", "link": "https://wikipedia.org/wiki/Kingdom_of_Italy"}]}, {"year": "1914", "text": "World War I: The first British Royal Navy defeat of the war with Germany, the Battle of Coronel, is fought off of the western coast of Chile, in the Pacific, with the loss of HMS Good Hope and HMS Monmouth.", "html": "1914 - <a href=\"https://wikipedia.org/wiki/World_War_I\" title=\"World War I\">World War I</a>: The first British <a href=\"https://wikipedia.org/wiki/Royal_Navy\" title=\"Royal Navy\">Royal Navy</a> defeat of the war with Germany, the <a href=\"https://wikipedia.org/wiki/Battle_of_Coronel\" title=\"Battle of Coronel\">Battle of Coronel</a>, is fought off of the western coast of <a href=\"https://wikipedia.org/wiki/Chile\" title=\"Chile\">Chile</a>, in the Pacific, with the loss of <a href=\"https://wikipedia.org/wiki/HMS_Good_Hope_(1901)\" title=\"HMS Good Hope (1901)\">HMS <i>Good Hope</i></a> and <a href=\"https://wikipedia.org/wiki/HMS_Monmouth_(1901)\" title=\"HMS Monmouth (1901)\">HMS <i>Monmouth</i></a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/World_War_I\" title=\"World War I\">World War I</a>: The first British <a href=\"https://wikipedia.org/wiki/Royal_Navy\" title=\"Royal Navy\">Royal Navy</a> defeat of the war with Germany, the <a href=\"https://wikipedia.org/wiki/Battle_of_Coronel\" title=\"Battle of Coronel\">Battle of Coronel</a>, is fought off of the western coast of <a href=\"https://wikipedia.org/wiki/Chile\" title=\"Chile\">Chile</a>, in the Pacific, with the loss of <a href=\"https://wikipedia.org/wiki/HMS_Good_Hope_(1901)\" title=\"HMS Good Hope (1901)\">HMS <i>Good Hope</i></a> and <a href=\"https://wikipedia.org/wiki/HMS_Monmouth_(1901)\" title=\"HMS Monmouth (1901)\">HMS <i>Monmouth</i></a>.", "links": [{"title": "World War I", "link": "https://wikipedia.org/wiki/World_War_I"}, {"title": "Royal Navy", "link": "https://wikipedia.org/wiki/Royal_Navy"}, {"title": "Battle of Coronel", "link": "https://wikipedia.org/wiki/Battle_of_Coronel"}, {"title": "Chile", "link": "https://wikipedia.org/wiki/Chile"}, {"title": "HMS Good Hope (1901)", "link": "https://wikipedia.org/wiki/HMS_Good_Hope_(1901)"}, {"title": "HMS Monmouth (1901)", "link": "https://wikipedia.org/wiki/HMS_Monmouth_(1901)"}]}, {"year": "1914", "text": "World War I: The Australian Imperial Force (AIF) departed by ship in a single convoy from Albany, Western Australia bound for Egypt.", "html": "1914 - World War I: The <a href=\"https://wikipedia.org/wiki/First_Australian_Imperial_Force\" title=\"First Australian Imperial Force\">Australian Imperial Force</a> (AIF) departed by ship in a single convoy from <a href=\"https://wikipedia.org/wiki/Albany,_Western_Australia\" title=\"Albany, Western Australia\">Albany, Western Australia</a> bound for Egypt.", "no_year_html": "World War I: The <a href=\"https://wikipedia.org/wiki/First_Australian_Imperial_Force\" title=\"First Australian Imperial Force\">Australian Imperial Force</a> (AIF) departed by ship in a single convoy from <a href=\"https://wikipedia.org/wiki/Albany,_Western_Australia\" title=\"Albany, Western Australia\">Albany, Western Australia</a> bound for Egypt.", "links": [{"title": "First Australian Imperial Force", "link": "https://wikipedia.org/wiki/First_Australian_Imperial_Force"}, {"title": "Albany, Western Australia", "link": "https://wikipedia.org/wiki/Albany,_Western_Australia"}]}, {"year": "1916", "text": "In Russia, <PERSON> delivers in the State Duma the famous \"stupidity or treason\" speech, precipitating the downfall of the government of <PERSON>.", "html": "1916 - In Russia, <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> delivers in the <a href=\"https://wikipedia.org/wiki/State_Duma\" title=\"State Duma\">State <PERSON></a> the famous \"stupidity or treason\" speech, precipitating the downfall of the government of <a href=\"https://wikipedia.org/wiki/Boris_St%C3%BCrmer\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>.", "no_year_html": "In Russia, <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> delivers in the <a href=\"https://wikipedia.org/wiki/State_Duma\" title=\"State Duma\">State <PERSON></a> the famous \"stupidity or treason\" speech, precipitating the downfall of the government of <a href=\"https://wikipedia.org/wiki/Boris_St%C3%BCrmer\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}, {"title": "State Duma", "link": "https://wikipedia.org/wiki/State_Duma"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Boris_St%C3%BCrmer"}]}, {"year": "1918", "text": "World War I: With a brave action carried out into the waters of the Austro-Hungarian port of Pula, two officers of the Italian Regia Marina sink with a manned torpedo the enemy battleship SMS Viribus Unitis.", "html": "1918 - World War I: With a brave action carried out into the waters of the Austro-Hungarian port of <a href=\"https://wikipedia.org/wiki/Pula\" title=\"Pula\"><PERSON><PERSON></a>, two officers of the Italian <a href=\"https://wikipedia.org/wiki/Regia_Marina\" title=\"Regia Marina\">Regia Marina</a> sink with a manned torpedo the enemy battleship <a href=\"https://wikipedia.org/wiki/SMS_Viribus_Unitis\" title=\"SMS Viribus Unitis\">SMS <i>Viribus Unitis</i></a>.", "no_year_html": "World War I: With a brave action carried out into the waters of the Austro-Hungarian port of <a href=\"https://wikipedia.org/wiki/Pula\" title=\"Pula\"><PERSON><PERSON></a>, two officers of the Italian <a href=\"https://wikipedia.org/wiki/Regia_Marina\" title=\"Regia Marina\">Regia Marina</a> sink with a manned torpedo the enemy battleship <a href=\"https://wikipedia.org/wiki/SMS_Viribus_Unitis\" title=\"SMS Viribus Unitis\">SMS <i>Viribus Unitis</i></a>.", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>ula"}, {"title": "Regia Marina", "link": "https://wikipedia.org/wiki/Regia_Marina"}, {"title": "SMS Viribus Unitis", "link": "https://wikipedia.org/wiki/SMS_Viribus_Unitis"}]}, {"year": "1918", "text": "Malbone Street wreck: The worst rapid transit accident in US history occurs under the intersection of Malbone Street and Flatbush Avenue, Brooklyn, New York City, with at least 102 deaths.", "html": "1918 - <a href=\"https://wikipedia.org/wiki/Malbone_Street_wreck\" title=\"Malbone Street wreck\">Malbone Street wreck</a>: The worst <a href=\"https://wikipedia.org/wiki/Rapid_transit\" title=\"Rapid transit\">rapid transit</a> accident in US history occurs under the intersection of Malbone Street and <a href=\"https://wikipedia.org/wiki/Flatbush_Avenue\" title=\"Flatbush Avenue\">Flatbush Avenue</a>, <a href=\"https://wikipedia.org/wiki/Brooklyn\" title=\"Brooklyn\">Brooklyn</a>, New York City, with at least 102 deaths.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Malbone_Street_wreck\" title=\"Malbone Street wreck\">Malbone Street wreck</a>: The worst <a href=\"https://wikipedia.org/wiki/Rapid_transit\" title=\"Rapid transit\">rapid transit</a> accident in US history occurs under the intersection of Malbone Street and <a href=\"https://wikipedia.org/wiki/Flatbush_Avenue\" title=\"Flatbush Avenue\">Flatbush Avenue</a>, <a href=\"https://wikipedia.org/wiki/Brooklyn\" title=\"Brooklyn\">Brooklyn</a>, New York City, with at least 102 deaths.", "links": [{"title": "Malbone Street wreck", "link": "https://wikipedia.org/wiki/Malbone_Street_wreck"}, {"title": "Rapid transit", "link": "https://wikipedia.org/wiki/Rapid_transit"}, {"title": "Flatbush Avenue", "link": "https://wikipedia.org/wiki/Flatbush_Avenue"}, {"title": "Brooklyn", "link": "https://wikipedia.org/wiki/Brooklyn"}]}, {"year": "1918", "text": "Western Ukraine separates from Austria-Hungary.", "html": "1918 - <a href=\"https://wikipedia.org/wiki/West_Ukrainian_People%27s_Republic\" title=\"West Ukrainian People's Republic\">Western Ukraine</a> separates from <a href=\"https://wikipedia.org/wiki/Austria-Hungary\" title=\"Austria-Hungary\">Austria-Hungary</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/West_Ukrainian_People%27s_Republic\" title=\"West Ukrainian People's Republic\">Western Ukraine</a> separates from <a href=\"https://wikipedia.org/wiki/Austria-Hungary\" title=\"Austria-Hungary\">Austria-Hungary</a>.", "links": [{"title": "West Ukrainian People's Republic", "link": "https://wikipedia.org/wiki/West_Ukrainian_People%27s_Republic"}, {"title": "Austria-Hungary", "link": "https://wikipedia.org/wiki/Austria-Hungary"}]}, {"year": "1922", "text": "Abolition of the Ottoman sultanate: The last sultan of the Ottoman Empire, <PERSON><PERSON><PERSON>, abdicates.", "html": "1922 - <a href=\"https://wikipedia.org/wiki/Abolition_of_the_Ottoman_sultanate\" title=\"Abolition of the Ottoman sultanate\">Abolition of the Ottoman sultanate</a>: The last sultan of the <a href=\"https://wikipedia.org/wiki/Ottoman_Empire\" title=\"Ottoman Empire\">Ottoman Empire</a>, <a href=\"https://wikipedia.org/wiki/Mehmed_VI\" title=\"Mehmed VI\">Mehmed VI</a>, abdicates.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Abolition_of_the_Ottoman_sultanate\" title=\"Abolition of the Ottoman sultanate\">Abolition of the Ottoman sultanate</a>: The last sultan of the <a href=\"https://wikipedia.org/wiki/Ottoman_Empire\" title=\"Ottoman Empire\">Ottoman Empire</a>, <a href=\"https://wikipedia.org/wiki/Mehmed_VI\" title=\"Mehmed VI\">Mehmed VI</a>, abdicates.", "links": [{"title": "Abolition of the Ottoman sultanate", "link": "https://wikipedia.org/wiki/Abolition_of_the_Ottoman_sultanate"}, {"title": "Ottoman Empire", "link": "https://wikipedia.org/wiki/Ottoman_Empire"}, {"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1923", "text": "The Finnish airline Aero O/Y (now Finnair) is founded.", "html": "1923 - The Finnish airline Aero O/Y (now <a href=\"https://wikipedia.org/wiki/Finnair\" title=\"Finnair\">Finnair</a>) is founded.", "no_year_html": "The Finnish airline Aero O/Y (now <a href=\"https://wikipedia.org/wiki/Finnair\" title=\"Finnair\">Finnair</a>) is founded.", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Finnair"}]}, {"year": "1928", "text": "The Law on the Adoption and Implementation of the Turkish Alphabet, replaces the Arabic alphabet with the Latin alphabet.", "html": "1928 - The <i>Law on the Adoption and Implementation of the <a href=\"https://wikipedia.org/wiki/Turkish_alphabet\" title=\"Turkish alphabet\">Turkish Alphabet</a></i>, replaces the <a href=\"https://wikipedia.org/wiki/Arabic_alphabet\" title=\"Arabic alphabet\">Arabic alphabet</a> with the <a href=\"https://wikipedia.org/wiki/Latin_alphabet\" title=\"Latin alphabet\">Latin alphabet</a>.", "no_year_html": "The <i>Law on the Adoption and Implementation of the <a href=\"https://wikipedia.org/wiki/Turkish_alphabet\" title=\"Turkish alphabet\">Turkish Alphabet</a></i>, replaces the <a href=\"https://wikipedia.org/wiki/Arabic_alphabet\" title=\"Arabic alphabet\">Arabic alphabet</a> with the <a href=\"https://wikipedia.org/wiki/Latin_alphabet\" title=\"Latin alphabet\">Latin alphabet</a>.", "links": [{"title": "Turkish alphabet", "link": "https://wikipedia.org/wiki/Turkish_alphabet"}, {"title": "Arabic alphabet", "link": "https://wikipedia.org/wiki/Arabic_alphabet"}, {"title": "Latin alphabet", "link": "https://wikipedia.org/wiki/Latin_alphabet"}]}, {"year": "1937", "text": "Stalinists execute Pastor <PERSON> and seven members of Azerbaijan's Lutheran community.", "html": "1937 - <a href=\"https://wikipedia.org/wiki/Stalinism\" title=\"Stalinism\">Stalinists</a> execute Pastor <PERSON> and seven members of <a href=\"https://wikipedia.org/wiki/Azerbaijan_Soviet_Socialist_Republic\" title=\"Azerbaijan Soviet Socialist Republic\">Azerbaijan's</a> <a href=\"https://wikipedia.org/wiki/Lutheran\" class=\"mw-redirect\" title=\"Lutheran\">Lutheran</a> community.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Stalinism\" title=\"Stalinism\">Stalinists</a> execute Pastor <PERSON> and seven members of <a href=\"https://wikipedia.org/wiki/Azerbaijan_Soviet_Socialist_Republic\" title=\"Azerbaijan Soviet Socialist Republic\">Azerbaijan's</a> <a href=\"https://wikipedia.org/wiki/Lutheran\" class=\"mw-redirect\" title=\"Lutheran\">Lutheran</a> community.", "links": [{"title": "Stalinism", "link": "https://wikipedia.org/wiki/Stalinism"}, {"title": "Azerbaijan Soviet Socialist Republic", "link": "https://wikipedia.org/wiki/Azerbaijan_Soviet_Socialist_Republic"}, {"title": "Lutheran", "link": "https://wikipedia.org/wiki/Lutheran"}]}, {"year": "1938", "text": "Seabiscuit defeats War Admiral in an upset victory during a match race deemed \"the match of the century\" in horse racing.", "html": "1938 - <a href=\"https://wikipedia.org/wiki/Seabiscuit\" title=\"Seabiscuit\">Seabiscuit</a> defeats <a href=\"https://wikipedia.org/wiki/War_Admiral\" title=\"War Admiral\">War Admiral</a> in an upset victory during a match race deemed \"the match of the century\" in horse racing.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Seabiscuit\" title=\"Seabiscuit\">Seabiscuit</a> defeats <a href=\"https://wikipedia.org/wiki/War_Admiral\" title=\"War Admiral\">War Admiral</a> in an upset victory during a match race deemed \"the match of the century\" in horse racing.", "links": [{"title": "Seabiscuit", "link": "https://wikipedia.org/wiki/Seabiscuit"}, {"title": "War Admiral", "link": "https://wikipedia.org/wiki/War_Admiral"}]}, {"year": "1941", "text": "American photographer <PERSON><PERSON> takes a picture of a moonrise over the town of Hernandez, New Mexico that would become one of the most famous images in the history of photography.", "html": "1941 - American photographer <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a> takes a <a href=\"https://wikipedia.org/wiki/Moonrise,_<PERSON>,_New_Mexico\" title=\"Moonrise, Hernandez, New Mexico\">picture of a moonrise</a> over the town of <a href=\"https://wikipedia.org/wiki/<PERSON>,_New_Mexico\" title=\"Hernandez, New Mexico\"><PERSON>, New Mexico</a> that would become one of the most famous images in the history of photography.", "no_year_html": "American photographer <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a> takes a <a href=\"https://wikipedia.org/wiki/Moon<PERSON>,_<PERSON>,_New_Mexico\" title=\"Moonrise, Hernandez, New Mexico\">picture of a moonrise</a> over the town of <a href=\"https://wikipedia.org/wiki/<PERSON>,_New_Mexico\" title=\"Hernandez, New Mexico\"><PERSON>, New Mexico</a> that would become one of the most famous images in the history of photography.", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}, {"title": "Moonrise, Hernandez, New Mexico", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>,_<PERSON>,_New_Mexico"}, {"title": "Hernandez, New Mexico", "link": "https://wikipedia.org/wiki/<PERSON>,_New_Mexico"}]}, {"year": "1942", "text": "World War II: Matanikau Offensive begins during the Guadalcanal Campaign and ends three days later with an American victory.", "html": "1942 - <a href=\"https://wikipedia.org/wiki/World_War_II\" title=\"World War II\">World War II</a>: <a href=\"https://wikipedia.org/wiki/Matanikau_Offensive\" title=\"Matanikau Offensive\">Matanikau Offensive</a> begins during the <a href=\"https://wikipedia.org/wiki/Guadalcanal_Campaign\" class=\"mw-redirect\" title=\"Guadalcanal Campaign\">Guadalcanal Campaign</a> and ends three days later with an American victory.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/World_War_II\" title=\"World War II\">World War II</a>: <a href=\"https://wikipedia.org/wiki/Matanikau_Offensive\" title=\"Matanikau Offensive\">Matanikau Offensive</a> begins during the <a href=\"https://wikipedia.org/wiki/Guadalcanal_Campaign\" class=\"mw-redirect\" title=\"Guadalcanal Campaign\">Guadalcanal Campaign</a> and ends three days later with an American victory.", "links": [{"title": "World War II", "link": "https://wikipedia.org/wiki/World_War_II"}, {"title": "Matanikau Offensive", "link": "https://wikipedia.org/wiki/Matanikau_Offensive"}, {"title": "Guadalcanal Campaign", "link": "https://wikipedia.org/wiki/Guadalcanal_Campaign"}]}, {"year": "1943", "text": "World War II: The 3rd Marine Division, United States Marines, landing on Bougainville in the Solomon Islands, secures a beachhead, leading that night to a naval clash at the Battle of Empress Augusta Bay.", "html": "1943 - World War II: The <a href=\"https://wikipedia.org/wiki/3rd_Marine_Division\" title=\"3rd Marine Division\">3rd Marine Division</a>, <a href=\"https://wikipedia.org/wiki/United_States_Marine_Corps\" title=\"United States Marine Corps\">United States Marines</a>, landing on <a href=\"https://wikipedia.org/wiki/Bougainville_Island\" title=\"Bougainville Island\">Bougainville</a> in the <a href=\"https://wikipedia.org/wiki/Solomon_Islands\" title=\"Solomon Islands\">Solomon Islands</a>, secures a beachhead, leading that night to a naval clash at the <a href=\"https://wikipedia.org/wiki/Battle_of_Empress_Augusta_Bay\" title=\"Battle of Empress Augusta Bay\">Battle of Empress Augusta Bay</a>.", "no_year_html": "World War II: The <a href=\"https://wikipedia.org/wiki/3rd_Marine_Division\" title=\"3rd Marine Division\">3rd Marine Division</a>, <a href=\"https://wikipedia.org/wiki/United_States_Marine_Corps\" title=\"United States Marine Corps\">United States Marines</a>, landing on <a href=\"https://wikipedia.org/wiki/Bougainville_Island\" title=\"Bougainville Island\">Bougainville</a> in the <a href=\"https://wikipedia.org/wiki/Solomon_Islands\" title=\"Solomon Islands\">Solomon Islands</a>, secures a beachhead, leading that night to a naval clash at the <a href=\"https://wikipedia.org/wiki/Battle_of_Empress_Augusta_Bay\" title=\"Battle of Empress Augusta Bay\">Battle of Empress Augusta Bay</a>.", "links": [{"title": "3rd Marine Division", "link": "https://wikipedia.org/wiki/3rd_Marine_Division"}, {"title": "United States Marine Corps", "link": "https://wikipedia.org/wiki/United_States_Marine_Corps"}, {"title": "Bougainville Island", "link": "https://wikipedia.org/wiki/Bougainville_Island"}, {"title": "Solomon Islands", "link": "https://wikipedia.org/wiki/Solomon_Islands"}, {"title": "Battle of Empress Augusta Bay", "link": "https://wikipedia.org/wiki/Battle_of_Empress_Augusta_Bay"}]}, {"year": "1944", "text": "World War II: Units of the British Army land at Walcheren.", "html": "1944 - World War II: Units of the British Army <a href=\"https://wikipedia.org/wiki/Battle_of_Walcheren_Causeway\" title=\"Battle of Walcheren Causeway\">land</a> at <a href=\"https://wikipedia.org/wiki/Walcheren\" title=\"Walcheren\">Walcheren</a>.", "no_year_html": "World War II: Units of the British Army <a href=\"https://wikipedia.org/wiki/Battle_of_Walcheren_Causeway\" title=\"Battle of Walcheren Causeway\">land</a> at <a href=\"https://wikipedia.org/wiki/Walcheren\" title=\"Walcheren\">Walcheren</a>.", "links": [{"title": "Battle of Walcheren Causeway", "link": "https://wikipedia.org/wiki/Battle_of_Walcheren_Causeway"}, {"title": "Walcheren", "link": "https://wikipedia.org/wiki/Walcheren"}]}, {"year": "1945", "text": "The official North Korean newspaper, Rodong Sinmun, is first published under the name Chongro.", "html": "1945 - The official <a href=\"https://wikipedia.org/wiki/North_Korea\" title=\"North Korea\">North Korean</a> newspaper, <i><a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a></i>, is first published under the name <i><PERSON><PERSON><PERSON></i>.", "no_year_html": "The official <a href=\"https://wikipedia.org/wiki/North_Korea\" title=\"North Korea\">North Korean</a> newspaper, <i><a href=\"https://wikipedia.org/wiki/Rodong_Sinmun\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a></i>, is first published under the name <i><PERSON><PERSON><PERSON></i>.", "links": [{"title": "North Korea", "link": "https://wikipedia.org/wiki/North_Korea"}, {"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>mun"}]}, {"year": "1948", "text": "<PERSON><PERSON><PERSON>, Ecumenical Patriarch of Constantinople, is enthroned.", "html": "1948 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_I_of_Constantinople\" title=\"<PERSON><PERSON><PERSON> I of Constantinople\"><PERSON><PERSON><PERSON> I</a>, <a href=\"https://wikipedia.org/wiki/Ecumenical_Patriarch_of_Constantinople\" title=\"Ecumenical Patriarch of Constantinople\">Ecumenical Patriarch of Constantinople</a>, is enthroned.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_I_of_Constantinople\" title=\"<PERSON><PERSON><PERSON> I of Constantinople\"><PERSON><PERSON><PERSON> I</a>, <a href=\"https://wikipedia.org/wiki/Ecumenical_Patriarch_of_Constantinople\" title=\"Ecumenical Patriarch of Constantinople\">Ecumenical Patriarch of Constantinople</a>, is enthroned.", "links": [{"title": "<PERSON><PERSON><PERSON> of Constantinople", "link": "https://wikipedia.org/wiki/Athenagoras_I_of_Constantinople"}, {"title": "Ecumenical Patriarch of Constantinople", "link": "https://wikipedia.org/wiki/Ecumenical_Patriarch_of_Constantinople"}]}, {"year": "1949", "text": "All 55 people on board Eastern Air Lines Flight 537 are killed when the Douglas DC-4 operating the flight collides in mid-air with a Bolivian Air Force Lockheed P-38 Lightning aircraft over Alexandria, Virginia.", "html": "1949 - All 55 people on board <a href=\"https://wikipedia.org/wiki/Eastern_Air_Lines_Flight_537\" title=\"Eastern Air Lines Flight 537\">Eastern Air Lines Flight 537</a> are killed when the <a href=\"https://wikipedia.org/wiki/Douglas_DC-4\" title=\"Douglas DC-4\">Douglas DC-4</a> operating the flight <a href=\"https://wikipedia.org/wiki/Mid-air_collision\" title=\"Mid-air collision\">collides in mid-air</a> with a <a href=\"https://wikipedia.org/wiki/Bolivian_Air_Force\" title=\"Bolivian Air Force\">Bolivian Air Force</a> <a href=\"https://wikipedia.org/wiki/Lockheed_P-38_Lightning\" title=\"Lockheed P-38 Lightning\">Lockheed P-38 Lightning</a> aircraft over <a href=\"https://wikipedia.org/wiki/Alexandria,_Virginia\" title=\"Alexandria, Virginia\">Alexandria, Virginia</a>.", "no_year_html": "All 55 people on board <a href=\"https://wikipedia.org/wiki/Eastern_Air_Lines_Flight_537\" title=\"Eastern Air Lines Flight 537\">Eastern Air Lines Flight 537</a> are killed when the <a href=\"https://wikipedia.org/wiki/Douglas_DC-4\" title=\"Douglas DC-4\">Douglas DC-4</a> operating the flight <a href=\"https://wikipedia.org/wiki/Mid-air_collision\" title=\"Mid-air collision\">collides in mid-air</a> with a <a href=\"https://wikipedia.org/wiki/Bolivian_Air_Force\" title=\"Bolivian Air Force\">Bolivian Air Force</a> <a href=\"https://wikipedia.org/wiki/Lockheed_P-38_Lightning\" title=\"Lockheed P-38 Lightning\">Lockheed P-38 Lightning</a> aircraft over <a href=\"https://wikipedia.org/wiki/Alexandria,_Virginia\" title=\"Alexandria, Virginia\">Alexandria, Virginia</a>.", "links": [{"title": "Eastern Air Lines Flight 537", "link": "https://wikipedia.org/wiki/Eastern_Air_Lines_Flight_537"}, {"title": "Douglas DC-4", "link": "https://wikipedia.org/wiki/Douglas_DC-4"}, {"title": "Mid-air collision", "link": "https://wikipedia.org/wiki/Mid-air_collision"}, {"title": "Bolivian Air Force", "link": "https://wikipedia.org/wiki/Bolivian_Air_Force"}, {"title": "Lockheed P-38 Lightning", "link": "https://wikipedia.org/wiki/Lockheed_P-38_Lightning"}, {"title": "Alexandria, Virginia", "link": "https://wikipedia.org/wiki/Alexandria,_Virginia"}]}, {"year": "1950", "text": "Puerto Rican nationalists <PERSON><PERSON><PERSON> and <PERSON> attempt to assassinate US President <PERSON> at Blair House.", "html": "1950 - <a href=\"https://wikipedia.org/wiki/Puerto_Rico\" title=\"Puerto Rico\">Puerto Rican</a> nationalists <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a> and <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> <a href=\"https://wikipedia.org/wiki/Attempted_assassination_of_<PERSON>_<PERSON>\" title=\"Attempted assassination of <PERSON>\">attempt to assassinate</a> US President <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> at <a href=\"https://wikipedia.org/wiki/Blair_House\" title=\"Blair House\">Blair House</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Puerto_Rico\" title=\"Puerto Rico\">Puerto Rican</a> nationalists <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a> and <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> <a href=\"https://wikipedia.org/wiki/Attempted_assassination_of_<PERSON>_<PERSON>\" title=\"Attempted assassination of <PERSON>\">attempt to assassinate</a> US President <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> at <a href=\"https://wikipedia.org/wiki/Blair_House\" title=\"Blair House\">Blair House</a>.", "links": [{"title": "Puerto Rico", "link": "https://wikipedia.org/wiki/Puerto_Rico"}, {"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Attempted assassination of <PERSON>", "link": "https://wikipedia.org/wiki/Attempted_assassination_of_<PERSON>_<PERSON>_<PERSON>"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "Blair House", "link": "https://wikipedia.org/wiki/Blair_House"}]}, {"year": "1951", "text": "Operation Buster-Jangle: Six thousand five hundred United States Army soldiers are exposed to 'Desert Rock' atomic explosions for training purposes in Nevada. Participation is not voluntary.", "html": "1951 - <a href=\"https://wikipedia.org/wiki/Operation_Buster%E2%80%93Jangle\" title=\"Operation Buster-Jangle\">Operation Buster-Jangle</a>: Six thousand five hundred <a href=\"https://wikipedia.org/wiki/United_States_Army\" title=\"United States Army\">United States Army</a> soldiers are exposed to 'Desert Rock' <a href=\"https://wikipedia.org/wiki/Atom_bomb\" class=\"mw-redirect\" title=\"Atom bomb\">atomic explosions</a> for training purposes in <a href=\"https://wikipedia.org/wiki/Nevada\" title=\"Nevada\">Nevada</a>. Participation is not voluntary.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Operation_Buster%E2%80%93Jangle\" title=\"Operation Buster-Jangle\">Operation Buster-Jangle</a>: Six thousand five hundred <a href=\"https://wikipedia.org/wiki/United_States_Army\" title=\"United States Army\">United States Army</a> soldiers are exposed to 'Desert Rock' <a href=\"https://wikipedia.org/wiki/Atom_bomb\" class=\"mw-redirect\" title=\"Atom bomb\">atomic explosions</a> for training purposes in <a href=\"https://wikipedia.org/wiki/Nevada\" title=\"Nevada\">Nevada</a>. Participation is not voluntary.", "links": [{"title": "Operation Buster-Jangle", "link": "https://wikipedia.org/wiki/Operation_Buster%E2%80%93Jangle"}, {"title": "United States Army", "link": "https://wikipedia.org/wiki/United_States_Army"}, {"title": "Atom bomb", "link": "https://wikipedia.org/wiki/Atom_bomb"}, {"title": "Nevada", "link": "https://wikipedia.org/wiki/Nevada"}]}, {"year": "1952", "text": "Nuclear weapons testing: The United States successfully detonates Ivy Mike, the first thermonuclear device, at the Eniwetok atoll. The explosion had a yield of ten megatons TNT equivalent.", "html": "1952 - <a href=\"https://wikipedia.org/wiki/Nuclear_weapons_testing\" title=\"Nuclear weapons testing\">Nuclear weapons testing</a>: The United States successfully detonates <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Mike\"><PERSON></a>, the first <a href=\"https://wikipedia.org/wiki/Thermonuclear_weapon\" title=\"Thermonuclear weapon\">thermonuclear device</a>, at the <a href=\"https://wikipedia.org/wiki/Eniwetok\" class=\"mw-redirect\" title=\"Eniwetok\">Eniwetok</a> atoll. The explosion had a yield of ten megatons <a href=\"https://wikipedia.org/wiki/TNT_equivalent\" title=\"TNT equivalent\">TNT equivalent</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Nuclear_weapons_testing\" title=\"Nuclear weapons testing\">Nuclear weapons testing</a>: The United States successfully detonates <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Ivy Mike\"><PERSON></a>, the first <a href=\"https://wikipedia.org/wiki/Thermonuclear_weapon\" title=\"Thermonuclear weapon\">thermonuclear device</a>, at the <a href=\"https://wikipedia.org/wiki/Eniwetok\" class=\"mw-redirect\" title=\"Eniwetok\">Eniwetok</a> atoll. The explosion had a yield of ten megatons <a href=\"https://wikipedia.org/wiki/TNT_equivalent\" title=\"TNT equivalent\">TNT equivalent</a>.", "links": [{"title": "Nuclear weapons testing", "link": "https://wikipedia.org/wiki/Nuclear_weapons_testing"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Thermonuclear weapon", "link": "https://wikipedia.org/wiki/Thermonuclear_weapon"}, {"title": "Eniwetok", "link": "https://wikipedia.org/wiki/Eniwetok"}, {"title": "TNT equivalent", "link": "https://wikipedia.org/wiki/TNT_equivalent"}]}, {"year": "1954", "text": "The Front de Libération Nationale fires the first shots of the Algerian War of Independence.", "html": "1954 - The <a href=\"https://wikipedia.org/wiki/National_Liberation_Front_(Algeria)\" title=\"National Liberation Front (Algeria)\">Front de Libération Nationale</a> fires the first shots of the <a href=\"https://wikipedia.org/wiki/Algerian_War_of_Independence\" class=\"mw-redirect\" title=\"Algerian War of Independence\">Algerian War of Independence</a>.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/National_Liberation_Front_(Algeria)\" title=\"National Liberation Front (Algeria)\">Front de Libération Nationale</a> fires the first shots of the <a href=\"https://wikipedia.org/wiki/Algerian_War_of_Independence\" class=\"mw-redirect\" title=\"Algerian War of Independence\">Algerian War of Independence</a>.", "links": [{"title": "National Liberation Front (Algeria)", "link": "https://wikipedia.org/wiki/National_Liberation_Front_(Algeria)"}, {"title": "Algerian War of Independence", "link": "https://wikipedia.org/wiki/Algerian_War_of_Independence"}]}, {"year": "1955", "text": "The establishment of a Military Assistance Advisory Group in South Vietnam marks the beginning of American involvement in the conflict.", "html": "1955 - The establishment of a <a href=\"https://wikipedia.org/wiki/Military_Assistance_Advisory_Group\" title=\"Military Assistance Advisory Group\">Military Assistance Advisory Group</a> in South Vietnam marks the beginning of American involvement in the conflict.", "no_year_html": "The establishment of a <a href=\"https://wikipedia.org/wiki/Military_Assistance_Advisory_Group\" title=\"Military Assistance Advisory Group\">Military Assistance Advisory Group</a> in South Vietnam marks the beginning of American involvement in the conflict.", "links": [{"title": "Military Assistance Advisory Group", "link": "https://wikipedia.org/wiki/Military_Assistance_Advisory_Group"}]}, {"year": "1955", "text": "The bombing of United Air Lines Flight 629 occurs near Longmont, Colorado, killing all 39 passengers and five crew members aboard the Douglas DC-6B airliner.", "html": "1955 - The bombing of <a href=\"https://wikipedia.org/wiki/United_Air_Lines_Flight_629\" title=\"United Air Lines Flight 629\">United Air Lines Flight 629</a> occurs near <a href=\"https://wikipedia.org/wiki/Longmont,_Colorado\" title=\"Longmont, Colorado\">Longmont, Colorado</a>, killing all 39 passengers and five crew members aboard the <a href=\"https://wikipedia.org/wiki/Douglas_DC-6\" title=\"Douglas DC-6\">Douglas DC-6</a>B airliner.", "no_year_html": "The bombing of <a href=\"https://wikipedia.org/wiki/United_Air_Lines_Flight_629\" title=\"United Air Lines Flight 629\">United Air Lines Flight 629</a> occurs near <a href=\"https://wikipedia.org/wiki/Longmont,_Colorado\" title=\"Longmont, Colorado\">Longmont, Colorado</a>, killing all 39 passengers and five crew members aboard the <a href=\"https://wikipedia.org/wiki/Douglas_DC-6\" title=\"Douglas DC-6\">Douglas DC-6</a>B airliner.", "links": [{"title": "United Air Lines Flight 629", "link": "https://wikipedia.org/wiki/United_Air_Lines_Flight_629"}, {"title": "Longmont, Colorado", "link": "https://wikipedia.org/wiki/Longmont,_Colorado"}, {"title": "Douglas DC-6", "link": "https://wikipedia.org/wiki/Douglas_DC-6"}]}, {"year": "1956", "text": "The Indian states Kerala, Andhra Pradesh, and Mysore are formally created under the States Reorganisation Act; Kanyakumari district is joined to Tamil Nadu from Kerala. Delhi was established as a union territory.", "html": "1956 - The Indian states <a href=\"https://wikipedia.org/wiki/Kerala\" title=\"Kerala\">Kerala</a>, <a href=\"https://wikipedia.org/wiki/Andhra_Pradesh\" title=\"Andhra Pradesh\">Andhra Pradesh</a>, and <a href=\"https://wikipedia.org/wiki/Mysore_State\" title=\"Mysore State\">Mysore</a> are formally created under the <a href=\"https://wikipedia.org/wiki/States_Reorganisation_Act,_1956\" title=\"States Reorganisation Act, 1956\">States Reorganisation Act</a>; <a href=\"https://wikipedia.org/wiki/Kanyakumari\" title=\"Kanyakumari\">Kanyakumari</a> district is joined to <a href=\"https://wikipedia.org/wiki/Tamil_Nadu\" title=\"Tamil Nadu\">Tamil Nadu</a> from <a href=\"https://wikipedia.org/wiki/Kerala\" title=\"Kerala\">Kerala</a>. <a href=\"https://wikipedia.org/wiki/Delhi\" title=\"Delhi\">Delhi</a> was established as a <a href=\"https://wikipedia.org/wiki/Union_territory\" title=\"Union territory\">union territory</a>.", "no_year_html": "The Indian states <a href=\"https://wikipedia.org/wiki/Kerala\" title=\"Kerala\">Kerala</a>, <a href=\"https://wikipedia.org/wiki/Andhra_Pradesh\" title=\"Andhra Pradesh\">Andhra Pradesh</a>, and <a href=\"https://wikipedia.org/wiki/Mysore_State\" title=\"Mysore State\">Mysore</a> are formally created under the <a href=\"https://wikipedia.org/wiki/States_Reorganisation_Act,_1956\" title=\"States Reorganisation Act, 1956\">States Reorganisation Act</a>; <a href=\"https://wikipedia.org/wiki/Kanyakumari\" title=\"Kanyakumari\">Kanyakumari</a> district is joined to <a href=\"https://wikipedia.org/wiki/Tamil_Nadu\" title=\"Tamil Nadu\">Tamil Nadu</a> from <a href=\"https://wikipedia.org/wiki/Kerala\" title=\"Kerala\">Kerala</a>. <a href=\"https://wikipedia.org/wiki/Delhi\" title=\"Delhi\">Delhi</a> was established as a <a href=\"https://wikipedia.org/wiki/Union_territory\" title=\"Union territory\">union territory</a>.", "links": [{"title": "Kerala", "link": "https://wikipedia.org/wiki/Kerala"}, {"title": "Andhra Pradesh", "link": "https://wikipedia.org/wiki/Andhra_Pradesh"}, {"title": "Mysore State", "link": "https://wikipedia.org/wiki/Mysore_State"}, {"title": "States Reorganisation Act, 1956", "link": "https://wikipedia.org/wiki/States_Reorganisation_Act,_1956"}, {"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Kanyakumari"}, {"title": "Tamil Nadu", "link": "https://wikipedia.org/wiki/Tamil_Nadu"}, {"title": "Kerala", "link": "https://wikipedia.org/wiki/Kerala"}, {"title": "Delhi", "link": "https://wikipedia.org/wiki/Delhi"}, {"title": "Union territory", "link": "https://wikipedia.org/wiki/Union_territory"}]}, {"year": "1956", "text": "Hungarian Revolution: <PERSON><PERSON><PERSON> announces Hungary's neutrality and withdrawal from the Warsaw Pact. Soviet troops begin to re-enter Hungary, contrary to assurances by the Soviet government. <PERSON><PERSON><PERSON> and <PERSON><PERSON><PERSON> secretly defect to the Soviets.", "html": "1956 - <a href=\"https://wikipedia.org/wiki/Hungarian_Revolution_of_1956\" title=\"Hungarian Revolution of 1956\">Hungarian Revolution</a>: <a href=\"https://wikipedia.org/wiki/I<PERSON><PERSON>_Nagy\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a> announces Hungary's neutrality and withdrawal from the <a href=\"https://wikipedia.org/wiki/Warsaw_Pact\" title=\"Warsaw Pact\">Warsaw Pact</a>. <a href=\"https://wikipedia.org/wiki/Soviet_Army\" title=\"Soviet Army\">Soviet troops</a> begin to re-enter Hungary, contrary to assurances by the Soviet government. <a href=\"https://wikipedia.org/wiki/J%C3%A1nos_K%C3%A1d%C3%A1r\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a> and <a href=\"https://wikipedia.org/wiki/Ferenc_M%C3%BCnnich\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a> secretly defect to the Soviets.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Hungarian_Revolution_of_1956\" title=\"Hungarian Revolution of 1956\">Hungarian Revolution</a>: <a href=\"https://wikipedia.org/wiki/I<PERSON><PERSON>_Nagy\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a> announces Hungary's neutrality and withdrawal from the <a href=\"https://wikipedia.org/wiki/Warsaw_Pact\" title=\"Warsaw Pact\">Warsaw Pact</a>. <a href=\"https://wikipedia.org/wiki/Soviet_Army\" title=\"Soviet Army\">Soviet troops</a> begin to re-enter Hungary, contrary to assurances by the Soviet government. <a href=\"https://wikipedia.org/wiki/J%C3%A1nos_K%C3%A1d%C3%A1r\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a> and <a href=\"https://wikipedia.org/wiki/Ferenc_M%C3%BCnnich\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a> secretly defect to the Soviets.", "links": [{"title": "Hungarian Revolution of 1956", "link": "https://wikipedia.org/wiki/Hungarian_Revolution_of_1956"}, {"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/I<PERSON><PERSON>_Nagy"}, {"title": "Warsaw Pact", "link": "https://wikipedia.org/wiki/Warsaw_Pact"}, {"title": "Soviet Army", "link": "https://wikipedia.org/wiki/Soviet_Army"}, {"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/J%C3%A1nos_K%C3%A1d%C3%A1r"}, {"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Ferenc_M%C3%BCnnich"}]}, {"year": "1956", "text": "The Springhill mining disaster in Springhill, Nova Scotia kills 39 miners; 88 are rescued.", "html": "1956 - The <a href=\"https://wikipedia.org/wiki/Springhill_mining_disasters\" title=\"Springhill mining disasters\">Springhill mining disaster</a> in <a href=\"https://wikipedia.org/wiki/Springhill,_Nova_Scotia\" title=\"Springhill, Nova Scotia\">Springhill, Nova Scotia</a> kills 39 miners; 88 are rescued.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Springhill_mining_disasters\" title=\"Springhill mining disasters\">Springhill mining disaster</a> in <a href=\"https://wikipedia.org/wiki/Springhill,_Nova_Scotia\" title=\"Springhill, Nova Scotia\">Springhill, Nova Scotia</a> kills 39 miners; 88 are rescued.", "links": [{"title": "Springhill mining disasters", "link": "https://wikipedia.org/wiki/Springhill_mining_disasters"}, {"title": "Springhill, Nova Scotia", "link": "https://wikipedia.org/wiki/Springhill,_Nova_Scotia"}]}, {"year": "1957", "text": "The Mackinac Bridge, the world's longest suspension bridge between anchorages at the time, opens to traffic connecting Michigan's upper and lower peninsulas.", "html": "1957 - The <a href=\"https://wikipedia.org/wiki/Mackinac_Bridge\" title=\"Mackinac Bridge\">Mackinac Bridge</a>, the world's longest suspension bridge between anchorages at the time, opens to traffic connecting Michigan's upper and lower peninsulas.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Mackinac_Bridge\" title=\"Mackinac Bridge\">Mackinac Bridge</a>, the world's longest suspension bridge between anchorages at the time, opens to traffic connecting Michigan's upper and lower peninsulas.", "links": [{"title": "Mackinac Bridge", "link": "https://wikipedia.org/wiki/Mackinac_Bridge"}]}, {"year": "1963", "text": "The Arecibo Observatory in Arecibo, Puerto Rico, with the largest radio telescope ever constructed, officially opens.", "html": "1963 - The <a href=\"https://wikipedia.org/wiki/Arecibo_Observatory\" title=\"Arecibo Observatory\">Arecibo Observatory</a> in <a href=\"https://wikipedia.org/wiki/Arecibo,_Puerto_Rico\" title=\"Arecibo, Puerto Rico\">Arecibo, Puerto Rico</a>, with the largest <a href=\"https://wikipedia.org/wiki/Arecibo_Telescope\" title=\"Arecibo Telescope\">radio telescope</a> ever constructed, officially opens.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Arecibo_Observatory\" title=\"Arecibo Observatory\">Arecibo Observatory</a> in <a href=\"https://wikipedia.org/wiki/Arecibo,_Puerto_Rico\" title=\"Arecibo, Puerto Rico\">Arecibo, Puerto Rico</a>, with the largest <a href=\"https://wikipedia.org/wiki/Arecibo_Telescope\" title=\"Arecibo Telescope\">radio telescope</a> ever constructed, officially opens.", "links": [{"title": "Arecibo Observatory", "link": "https://wikipedia.org/wiki/Arecibo_Observatory"}, {"title": "Arecibo, Puerto Rico", "link": "https://wikipedia.org/wiki/Arecibo,_Puerto_Rico"}, {"title": "Arecibo Telescope", "link": "https://wikipedia.org/wiki/Arecibo_Telescope"}]}, {"year": "1963", "text": "The 1963 South Vietnamese coup begins.", "html": "1963 - The <a href=\"https://wikipedia.org/wiki/1963_South_Vietnamese_coup_d%27%C3%A9tat\" title=\"1963 South Vietnamese coup d'état\">1963 South Vietnamese coup</a> begins.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/1963_South_Vietnamese_coup_d%27%C3%A9tat\" title=\"1963 South Vietnamese coup d'état\">1963 South Vietnamese coup</a> begins.", "links": [{"title": "1963 South Vietnamese coup d'état", "link": "https://wikipedia.org/wiki/1963_South_Vietnamese_coup_d%27%C3%A9tat"}]}, {"year": "1968", "text": "The Motion Picture Association of America's film rating system is officially introduced, originating with the ratings G, M, R, and X.", "html": "1968 - The <a href=\"https://wikipedia.org/wiki/Motion_Picture_Association_of_America\" class=\"mw-redirect\" title=\"Motion Picture Association of America\">Motion Picture Association of America</a>'s <a href=\"https://wikipedia.org/wiki/Motion_Picture_Association_of_America_film_rating_system\" class=\"mw-redirect\" title=\"Motion Picture Association of America film rating system\">film rating system</a> is officially introduced, originating with the ratings G, M, R, and X.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Motion_Picture_Association_of_America\" class=\"mw-redirect\" title=\"Motion Picture Association of America\">Motion Picture Association of America</a>'s <a href=\"https://wikipedia.org/wiki/Motion_Picture_Association_of_America_film_rating_system\" class=\"mw-redirect\" title=\"Motion Picture Association of America film rating system\">film rating system</a> is officially introduced, originating with the ratings G, M, R, and X.", "links": [{"title": "Motion Picture Association of America", "link": "https://wikipedia.org/wiki/Motion_Picture_Association_of_America"}, {"title": "Motion Picture Association of America film rating system", "link": "https://wikipedia.org/wiki/Motion_Picture_Association_of_America_film_rating_system"}]}, {"year": "1970", "text": "Club Cinq-Sept fire in Saint-Laurent-du-Pont, France kills 146 young people.", "html": "1970 - <a href=\"https://wikipedia.org/wiki/Club_Cinq-Sept_fire\" title=\"Club Cinq-Sept fire\">Club Cinq-Sept fire</a> in <a href=\"https://wikipedia.org/wiki/Saint-Laurent-<PERSON>-<PERSON>\" title=\"Saint-Laurent-du-Pont\">Saint-Laurent-du-Pont</a>, France kills 146 young people.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Club_Cinq-Sept_fire\" title=\"Club Cinq-Sept fire\">Club Cinq-Sept fire</a> in <a href=\"https://wikipedia.org/wiki/<PERSON>-Laurent-<PERSON>-<PERSON>\" title=\"Saint-Laurent-du-Pont\">Saint-Laurent-<PERSON>-<PERSON></a>, France kills 146 young people.", "links": [{"title": "Club Cinq-Sept fire", "link": "https://wikipedia.org/wiki/Club_Cinq-Sept_fire"}, {"title": "Saint-Laurent-du-Pont", "link": "https://wikipedia.org/wiki/<PERSON>-<PERSON>-<PERSON>"}]}, {"year": "1973", "text": "Watergate scandal: <PERSON> is appointed as the new Watergate Special Prosecutor.", "html": "1973 - <a href=\"https://wikipedia.org/wiki/Watergate_scandal\" title=\"Watergate scandal\">Watergate scandal</a>: <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> is appointed as the new Watergate Special Prosecutor.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Watergate_scandal\" title=\"Watergate scandal\">Watergate scandal</a>: <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> is appointed as the new Watergate Special Prosecutor.", "links": [{"title": "Watergate scandal", "link": "https://wikipedia.org/wiki/Watergate_scandal"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1973", "text": "The Indian state of Mysore is renamed as Karnataka to represent all the regions within Karunadu.", "html": "1973 - The Indian state of <a href=\"https://wikipedia.org/wiki/Mysore\" title=\"Mysore\">Mysore</a> is renamed as <a href=\"https://wikipedia.org/wiki/Karnataka\" title=\"Karnataka\">Karnataka</a> to represent all the regions within Karunadu.", "no_year_html": "The Indian state of <a href=\"https://wikipedia.org/wiki/Mysore\" title=\"Mysore\">Mysore</a> is renamed as <a href=\"https://wikipedia.org/wiki/Karnataka\" title=\"Karnataka\">Karnataka</a> to represent all the regions within Karunadu.", "links": [{"title": "Mysore", "link": "https://wikipedia.org/wiki/Mysore"}, {"title": "Karnataka", "link": "https://wikipedia.org/wiki/Karnataka"}]}, {"year": "1976", "text": "Burundian president <PERSON> is deposed in a bloodless military coup d'état by deputy <PERSON><PERSON><PERSON>.", "html": "1976 - Burundian president <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> is deposed in a bloodless <a href=\"https://wikipedia.org/wiki/National_Defence_Force_(Burundi)\" class=\"mw-redirect\" title=\"National Defence Force (Burundi)\">military</a> <a href=\"https://wikipedia.org/wiki/1976_Burundian_coup_d%27%C3%A9tat\" title=\"1976 Burundian coup d'état\">coup d'état</a> by deputy <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>.", "no_year_html": "Burundian president <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> is deposed in a bloodless <a href=\"https://wikipedia.org/wiki/National_Defence_Force_(Burundi)\" class=\"mw-redirect\" title=\"National Defence Force (Burundi)\">military</a> <a href=\"https://wikipedia.org/wiki/1976_Burundian_coup_d%27%C3%A9tat\" title=\"1976 Burundian coup d'état\">coup d'état</a> by deputy <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "National Defence Force (Burundi)", "link": "https://wikipedia.org/wiki/National_Defence_Force_(Burundi)"}, {"title": "1976 Burundian coup d'état", "link": "https://wikipedia.org/wiki/1976_Burundian_coup_d%27%C3%A9tat"}, {"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1979", "text": "In Bolivia, Colonel <PERSON> executes a bloody coup d'état against the constitutional government of <PERSON><PERSON><PERSON>.", "html": "1979 - In Bolivia, Colonel <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> executes a bloody <a href=\"https://wikipedia.org/wiki/Coup_d%27%C3%A9tat\" title=\"Coup d'état\">coup d'état</a> against the constitutional government of <a href=\"https://wikipedia.org/wiki/W%C3%A1lter_Guevara\" title=\"Wálter Guevara\">W<PERSON><PERSON></a>.", "no_year_html": "In Bolivia, Colonel <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> executes a bloody <a href=\"https://wikipedia.org/wiki/Coup_d%27%C3%A9tat\" title=\"Coup d'état\">coup d'état</a> against the constitutional government of <a href=\"https://wikipedia.org/wiki/W%C3%A1lter_Guevara\" title=\"Wá<PERSON> Guevara\">W<PERSON><PERSON></a>.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Coup d'état", "link": "https://wikipedia.org/wiki/Coup_d%27%C3%A9tat"}, {"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/W%C3%<PERSON><PERSON>_<PERSON>uevara"}]}, {"year": "1979", "text": "<PERSON><PERSON><PERSON> becomes the first female governor of a state of Mexico.", "html": "1979 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_%C3%81l<PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a> becomes the first female <a href=\"https://wikipedia.org/wiki/Governor_of_Colima\" title=\"Governor of Colima\">governor</a> of a state of Mexico.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_%C3%81l<PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a> becomes the first female <a href=\"https://wikipedia.org/wiki/Governor_of_Colima\" title=\"Governor of Colima\">governor</a> of a state of Mexico.", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/G<PERSON><PERSON>_%C3%<PERSON><PERSON><PERSON><PERSON>"}, {"title": "Governor of Colima", "link": "https://wikipedia.org/wiki/Governor_of_Colima"}]}, {"year": "1981", "text": "Antigua and Barbuda gains independence from the United Kingdom.", "html": "1981 - <a href=\"https://wikipedia.org/wiki/Antigua_and_Barbuda\" title=\"Antigua and Barbuda\">Antigua and Barbuda</a> gains independence from the United Kingdom.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Antigua_and_Barbuda\" title=\"Antigua and Barbuda\">Antigua and Barbuda</a> gains independence from the United Kingdom.", "links": [{"title": "Antigua and Barbuda", "link": "https://wikipedia.org/wiki/Antigua_and_Barbuda"}]}, {"year": "1982", "text": "Honda becomes the first Asian automobile company to produce cars in the United States with the opening of its factory in Marysville, Ohio; a Honda Accord is the first car produced there.", "html": "1982 - <a href=\"https://wikipedia.org/wiki/Honda\" title=\"Honda\">Honda</a> becomes the first Asian automobile company to produce cars in the United States with the opening of its factory in <a href=\"https://wikipedia.org/wiki/Marysville,_Ohio\" title=\"Marysville, Ohio\">Marysville, Ohio</a>; a <a href=\"https://wikipedia.org/wiki/Honda_Accord\" title=\"Honda Accord\">Honda Accord</a> is the first car produced there.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Honda\" title=\"Honda\">Honda</a> becomes the first Asian automobile company to produce cars in the United States with the opening of its factory in <a href=\"https://wikipedia.org/wiki/Marysville,_Ohio\" title=\"Marysville, Ohio\">Marysville, Ohio</a>; a <a href=\"https://wikipedia.org/wiki/Honda_Accord\" title=\"Honda Accord\">Honda Accord</a> is the first car produced there.", "links": [{"title": "Honda", "link": "https://wikipedia.org/wiki/Honda"}, {"title": "Marysville, Ohio", "link": "https://wikipedia.org/wiki/Marysville,_Ohio"}, {"title": "Honda Accord", "link": "https://wikipedia.org/wiki/Honda_Accord"}]}, {"year": "1984", "text": "After the assassination of <PERSON><PERSON>, Prime Minister of India on 31 October 1984, by two of her Sikh bodyguards, anti-Sikh riots erupt.", "html": "1984 - After the <a href=\"https://wikipedia.org/wiki/Assassination_of_<PERSON><PERSON>_<PERSON>\" title=\"Assassination of <PERSON><PERSON>\">assassination of <PERSON><PERSON></a>, <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_India\" title=\"Prime Minister of India\">Prime Minister of India</a> on 31 October 1984, by two of her Sikh bodyguards, <a href=\"https://wikipedia.org/wiki/1984_anti-Sikh_riots\" title=\"1984 anti-Sikh riots\">anti-Sikh riots</a> erupt.", "no_year_html": "After the <a href=\"https://wikipedia.org/wiki/Assassination_of_<PERSON><PERSON>_Gandhi\" title=\"Assassination of <PERSON><PERSON>\">assassination of <PERSON><PERSON></a>, <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_India\" title=\"Prime Minister of India\">Prime Minister of India</a> on 31 October 1984, by two of her Sikh bodyguards, <a href=\"https://wikipedia.org/wiki/1984_anti-Sikh_riots\" title=\"1984 anti-Sikh riots\">anti-Sikh riots</a> erupt.", "links": [{"title": "Assassination of <PERSON><PERSON> Gandhi", "link": "https://wikipedia.org/wiki/Assassination_of_Indira_Gandhi"}, {"title": "Prime Minister of India", "link": "https://wikipedia.org/wiki/Prime_Minister_of_India"}, {"title": "1984 anti-Sikh riots", "link": "https://wikipedia.org/wiki/1984_anti-Sikh_riots"}]}, {"year": "1987", "text": "British Rail Class 43 (HST) hits the record speed of 238 km/h for rail vehicles with on-board fuel to generate electricity for traction motors.", "html": "1987 - <a href=\"https://wikipedia.org/wiki/British_Rail_Class_43_(HST)\" title=\"British Rail Class 43 (HST)\">British Rail Class 43 (HST)</a> hits the record speed of 238 km/h for rail vehicles with on-board fuel to generate electricity for traction motors.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/British_Rail_Class_43_(HST)\" title=\"British Rail Class 43 (HST)\">British Rail Class 43 (HST)</a> hits the record speed of 238 km/h for rail vehicles with on-board fuel to generate electricity for traction motors.", "links": [{"title": "British Rail Class 43 (HST)", "link": "https://wikipedia.org/wiki/British_Rail_Class_43_(HST)"}]}, {"year": "1991", "text": "President of the Chechen Republic <PERSON><PERSON><PERSON><PERSON> declares sovereignty of the Chechen Republic of Ichkeria from the Russian Federation.", "html": "1991 - <a href=\"https://wikipedia.org/wiki/Head_of_the_Chechen_Republic\" title=\"Head of the Chechen Republic\">President of the Chechen Republic</a> <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a> declares sovereignty of the <a href=\"https://wikipedia.org/wiki/Chechen_Republic_of_Ichkeria\" title=\"Chechen Republic of Ichkeria\">Chechen Republic of Ichkeria</a> from the <a href=\"https://wikipedia.org/wiki/Russia\" title=\"Russia\">Russian Federation</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Head_of_the_Chechen_Republic\" title=\"Head of the Chechen Republic\">President of the Chechen Republic</a> <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a> declares sovereignty of the <a href=\"https://wikipedia.org/wiki/Chechen_Republic_of_Ichkeria\" title=\"Chechen Republic of Ichkeria\">Chechen Republic of Ichkeria</a> from the <a href=\"https://wikipedia.org/wiki/Russia\" title=\"Russia\">Russian Federation</a>.", "links": [{"title": "Head of the Chechen Republic", "link": "https://wikipedia.org/wiki/Head_of_the_Chechen_Republic"}, {"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>"}, {"title": "Chechen Republic of Ichkeria", "link": "https://wikipedia.org/wiki/Chechen_Republic_of_Ichkeria"}, {"title": "Russia", "link": "https://wikipedia.org/wiki/Russia"}]}, {"year": "1993", "text": "The Maastricht Treaty takes effect, formally establishing the European Union.", "html": "1993 - The <a href=\"https://wikipedia.org/wiki/Maastricht_Treaty\" title=\"Maastricht Treaty\">Maastricht Treaty</a> takes effect, formally establishing the <a href=\"https://wikipedia.org/wiki/European_Union\" title=\"European Union\">European Union</a>.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Maastricht_Treaty\" title=\"Maastricht Treaty\">Maastricht Treaty</a> takes effect, formally establishing the <a href=\"https://wikipedia.org/wiki/European_Union\" title=\"European Union\">European Union</a>.", "links": [{"title": "Maastricht Treaty", "link": "https://wikipedia.org/wiki/Maastricht_Treaty"}, {"title": "European Union", "link": "https://wikipedia.org/wiki/European_Union"}]}, {"year": "2000", "text": "Chhattisgarh officially becomes the 26th state of India, formed from sixteen districts of eastern Madhya Pradesh.", "html": "2000 - <a href=\"https://wikipedia.org/wiki/Chhattisgarh\" title=\"Chhattisgarh\">Chhattisgarh</a> officially <a href=\"https://wikipedia.org/wiki/Madhya_Pradesh_Reorganisation_Act,_2000\" title=\"Madhya Pradesh Reorganisation Act, 2000\">becomes</a> the 26th <a href=\"https://wikipedia.org/wiki/States_and_union_territories_of_India\" title=\"States and union territories of India\">state of India</a>, formed from sixteen districts of eastern <a href=\"https://wikipedia.org/wiki/Madhya_Pradesh\" title=\"Madhya Pradesh\">Madhya Pradesh</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Chhattisgarh\" title=\"Chhattisgarh\">Chhattisgarh</a> officially <a href=\"https://wikipedia.org/wiki/Madhya_Pradesh_Reorganisation_Act,_2000\" title=\"Madhya Pradesh Reorganisation Act, 2000\">becomes</a> the 26th <a href=\"https://wikipedia.org/wiki/States_and_union_territories_of_India\" title=\"States and union territories of India\">state of India</a>, formed from sixteen districts of eastern <a href=\"https://wikipedia.org/wiki/Madhya_Pradesh\" title=\"Madhya Pradesh\">Madhya Pradesh</a>.", "links": [{"title": "Chhattisgarh", "link": "https://wikipedia.org/wiki/Chhattisgarh"}, {"title": "Madhya Pradesh Reorganisation Act, 2000", "link": "https://wikipedia.org/wiki/Madhya_Pradesh_Reorganisation_Act,_2000"}, {"title": "States and union territories of India", "link": "https://wikipedia.org/wiki/States_and_union_territories_of_India"}, {"title": "Madhya Pradesh", "link": "https://wikipedia.org/wiki/Madhya_Pradesh"}]}, {"year": "2000", "text": "Serbia and Montenegro joins the United Nations.", "html": "2000 - <a href=\"https://wikipedia.org/wiki/Serbia_and_Montenegro\" title=\"Serbia and Montenegro\">Serbia and Montenegro</a> joins the <a href=\"https://wikipedia.org/wiki/United_Nations\" title=\"United Nations\">United Nations</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Serbia_and_Montenegro\" title=\"Serbia and Montenegro\">Serbia and Montenegro</a> joins the <a href=\"https://wikipedia.org/wiki/United_Nations\" title=\"United Nations\">United Nations</a>.", "links": [{"title": "Serbia and Montenegro", "link": "https://wikipedia.org/wiki/Serbia_and_Montenegro"}, {"title": "United Nations", "link": "https://wikipedia.org/wiki/United_Nations"}]}, {"year": "2001", "text": "Turkey, Australia, and Canada agree to commit troops to the invasion of Afghanistan.", "html": "2001 - <a href=\"https://wikipedia.org/wiki/Turkey\" title=\"Turkey\">Turkey</a>, <a href=\"https://wikipedia.org/wiki/Australia\" title=\"Australia\">Australia</a>, and <a href=\"https://wikipedia.org/wiki/Canada\" title=\"Canada\">Canada</a> agree to commit troops to the <a href=\"https://wikipedia.org/wiki/United_States_invasion_of_Afghanistan\" title=\"United States invasion of Afghanistan\">invasion of Afghanistan</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Turkey\" title=\"Turkey\">Turkey</a>, <a href=\"https://wikipedia.org/wiki/Australia\" title=\"Australia\">Australia</a>, and <a href=\"https://wikipedia.org/wiki/Canada\" title=\"Canada\">Canada</a> agree to commit troops to the <a href=\"https://wikipedia.org/wiki/United_States_invasion_of_Afghanistan\" title=\"United States invasion of Afghanistan\">invasion of Afghanistan</a>.", "links": [{"title": "Turkey", "link": "https://wikipedia.org/wiki/Turkey"}, {"title": "Australia", "link": "https://wikipedia.org/wiki/Australia"}, {"title": "Canada", "link": "https://wikipedia.org/wiki/Canada"}, {"title": "United States invasion of Afghanistan", "link": "https://wikipedia.org/wiki/United_States_invasion_of_Afghanistan"}]}, {"year": "2009", "text": "An Ilyushin Il-76 crashes near the Mir mine after takeoff from Mirny Airport in Yakutia, killing all 11 aboard.", "html": "2009 - An <a href=\"https://wikipedia.org/wiki/Ilyushin_Il-76\" title=\"Ilyushin Il-76\">Ilyushin Il-76</a> <a href=\"https://wikipedia.org/wiki/2009_Yakutia_Ilyushin_Il-76_crash\" title=\"2009 Yakutia Ilyushin Il-76 crash\">crashes</a> near the <a href=\"https://wikipedia.org/wiki/Mir_mine\" title=\"Mir mine\">Mir mine</a> after takeoff from <a href=\"https://wikipedia.org/wiki/Mirny_Airport\" title=\"Mirny Airport\">Mirny Airport</a> in <a href=\"https://wikipedia.org/wiki/Sakha_Republic\" title=\"Sakha Republic\">Yakutia</a>, killing all 11 aboard.", "no_year_html": "An <a href=\"https://wikipedia.org/wiki/Ilyushin_Il-76\" title=\"Ilyushin Il-76\">Ilyushin Il-76</a> <a href=\"https://wikipedia.org/wiki/2009_Yakutia_Ilyushin_Il-76_crash\" title=\"2009 Yakutia Ilyushin Il-76 crash\">crashes</a> near the <a href=\"https://wikipedia.org/wiki/Mir_mine\" title=\"Mir mine\">Mir mine</a> after takeoff from <a href=\"https://wikipedia.org/wiki/Mirny_Airport\" title=\"Mirny Airport\">Mirny Airport</a> in <a href=\"https://wikipedia.org/wiki/Sakha_Republic\" title=\"Sakha Republic\">Yakutia</a>, killing all 11 aboard.", "links": [{"title": "Ilyushin Il-76", "link": "https://wikipedia.org/wiki/Ilyushin_Il-76"}, {"title": "2009 Yakutia Ilyushin Il-76 crash", "link": "https://wikipedia.org/wiki/2009_Yakutia_Ilyushin_Il-76_crash"}, {"title": "Mir mine", "link": "https://wikipedia.org/wiki/Mir_mine"}, {"title": "Mirny Airport", "link": "https://wikipedia.org/wiki/Mirny_Airport"}, {"title": "Sakha Republic", "link": "https://wikipedia.org/wiki/Sakha_Republic"}]}, {"year": "2011", "text": "<PERSON> succeeds <PERSON><PERSON><PERSON> and becomes the third president of the European Central Bank.", "html": "2011 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> succeeds <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a> and becomes the third president of the <a href=\"https://wikipedia.org/wiki/European_Central_Bank\" title=\"European Central Bank\">European Central Bank</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> succeeds <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a> and becomes the third president of the <a href=\"https://wikipedia.org/wiki/European_Central_Bank\" title=\"European Central Bank\">European Central Bank</a>.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>"}, {"title": "European Central Bank", "link": "https://wikipedia.org/wiki/European_Central_Bank"}]}, {"year": "2012", "text": "A fuel tank truck crashes and explodes in the Saudi Arabian capital Riyadh, killing 26 people and injuring 135.", "html": "2012 - A fuel tank truck <a href=\"https://wikipedia.org/wiki/List_of_tanker_explosions\" class=\"mw-redirect\" title=\"List of tanker explosions\">crashes and explodes</a> in the <a href=\"https://wikipedia.org/wiki/Saudi_Arabia\" title=\"Saudi Arabia\">Saudi Arabian</a> capital <a href=\"https://wikipedia.org/wiki/Riyadh\" title=\"Riyadh\">Riyadh</a>, killing 26 people and injuring 135.", "no_year_html": "A fuel tank truck <a href=\"https://wikipedia.org/wiki/List_of_tanker_explosions\" class=\"mw-redirect\" title=\"List of tanker explosions\">crashes and explodes</a> in the <a href=\"https://wikipedia.org/wiki/Saudi_Arabia\" title=\"Saudi Arabia\">Saudi Arabian</a> capital <a href=\"https://wikipedia.org/wiki/Riyadh\" title=\"Riyadh\">Riyadh</a>, killing 26 people and injuring 135.", "links": [{"title": "List of tanker explosions", "link": "https://wikipedia.org/wiki/List_of_tanker_explosions"}, {"title": "Saudi Arabia", "link": "https://wikipedia.org/wiki/Saudi_Arabia"}, {"title": "Riyadh", "link": "https://wikipedia.org/wiki/Riyadh"}]}, {"year": "2024", "text": "A concrete canopy collapses at the Novi Sad railway station, killing 14 people and injuring 3.", "html": "2024 - A concrete canopy <a href=\"https://wikipedia.org/wiki/Novi_Sad_railway_station_canopy_collapse\" title=\"Novi Sad railway station canopy collapse\">collapses</a> at the <a href=\"https://wikipedia.org/wiki/Novi_Sad_railway_station\" title=\"Novi Sad railway station\">Novi Sad railway station</a>, killing 14 people and injuring 3.", "no_year_html": "A concrete canopy <a href=\"https://wikipedia.org/wiki/Novi_Sad_railway_station_canopy_collapse\" title=\"Novi Sad railway station canopy collapse\">collapses</a> at the <a href=\"https://wikipedia.org/wiki/Novi_Sad_railway_station\" title=\"Novi Sad railway station\">Novi Sad railway station</a>, killing 14 people and injuring 3.", "links": [{"title": "Novi Sad railway station canopy collapse", "link": "https://wikipedia.org/wiki/Novi_Sad_railway_station_canopy_collapse"}, {"title": "Novi Sad railway station", "link": "https://wikipedia.org/wiki/Novi_Sad_railway_station"}]}], "Births": [{"year": "846", "text": "<PERSON>r, Frankish king (d. 879)", "html": "846 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_Stammerer\" title=\"<PERSON> the Stammerer\"><PERSON> the <PERSON>merer</a>, Frankish king (d. 879)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_Stammerer\" title=\"<PERSON> the Stammerer\"><PERSON> the <PERSON></a>, Frankish king (d. 879)", "links": [{"title": "<PERSON> Stammerer", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_Stammerer"}]}, {"year": "1339", "text": "<PERSON>, Duke of Austria (d. 1365)", "html": "1339 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Duke_of_Austria\" title=\"<PERSON>, Duke of Austria\"><PERSON>, Duke of Austria</a> (d. 1365)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Duke_of_Austria\" title=\"<PERSON>, Duke of Austria\"><PERSON>, Duke of Austria</a> (d. 1365)", "links": [{"title": "<PERSON>, Duke of Austria", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Duke_of_Austria"}]}, {"year": "1351", "text": "<PERSON>, Duke of Austria (d. 1386)", "html": "1351 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Duke_of_Austria\" title=\"<PERSON>, Duke of Austria\"><PERSON>, Duke of Austria</a> (d. 1386)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Duke_of_Austria\" title=\"<PERSON>, Duke of Austria\"><PERSON>, Duke of Austria</a> (d. 1386)", "links": [{"title": "<PERSON>, Duke of Austria", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Duke_of_Austria"}]}, {"year": "1419", "text": "<PERSON>, Duke of Brunswick-Grubenhagen (d. 1485)", "html": "1419 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Duke_of_Brunswick-Grubenhagen\" title=\"<PERSON>, Duke of Brunswick-Grubenhagen\"><PERSON>, Duke of Brunswick-Grubenhagen</a> (d. 1485)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>,_Duke_of_Brunswick-Grubenhagen\" title=\"<PERSON>, Duke of Brunswick-Grubenhagen\"><PERSON>, Duke of Brunswick-Grubenhagen</a> (d. 1485)", "links": [{"title": "<PERSON>, Duke of Brunswick-Grubenhagen", "link": "https://wikipedia.org/wiki/<PERSON>,_Duke_of_Brunswick-<PERSON>"}]}, {"year": "1498", "text": "<PERSON>, Italian cardinal (d. 1574)", "html": "1498 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(bishop)\" title=\"<PERSON> (bishop)\"><PERSON></a>, Italian cardinal (d. 1574)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(bishop)\" title=\"<PERSON> (bishop)\"><PERSON></a>, Italian cardinal (d. 1574)", "links": [{"title": "<PERSON> (bishop)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(bishop)"}]}, {"year": "1499", "text": "<PERSON> of Aragon, Italian noble (d. 1512)", "html": "1499 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_Aragon\" title=\"<PERSON> of Aragon\"><PERSON> of Aragon</a>, Italian noble (d. 1512)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON> of Aragon\"><PERSON> of Aragon</a>, Italian noble (d. 1512)", "links": [{"title": "<PERSON> of Aragon", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_Aragon"}]}, {"year": "1522", "text": "<PERSON>, English landowner and politician (d. 1578)", "html": "1522 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(died_1578)\" title=\"<PERSON> (died 1578)\"><PERSON></a>, English landowner and politician (d. 1578)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(died_1578)\" title=\"<PERSON> (died 1578)\"><PERSON></a>, English landowner and politician (d. 1578)", "links": [{"title": "<PERSON> (died 1578)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(died_1578)"}]}, {"year": "1526", "text": "<PERSON>, queen of <PERSON> of Sweden (d. 1583)", "html": "1526 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, queen of <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Sweden\" title=\"<PERSON> of Sweden\"><PERSON> of Sweden</a> (d. 1583)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, queen of <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Sweden\" title=\"<PERSON> of Sweden\"><PERSON> of Sweden</a> (d. 1583)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "<PERSON> of Sweden", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Sweden"}]}, {"year": "1527", "text": "<PERSON>, 10th Baron <PERSON>, English noble and politician (d. 1597)", "html": "1527 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_10th_Baron_<PERSON>\" title=\"<PERSON>, 10th Baron Cobham\"><PERSON>, 10th Baron <PERSON>m</a>, English noble and politician (d. 1597)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>,_10th_Baron_<PERSON>\" title=\"<PERSON>, 10th Baron Cobham\"><PERSON>, 10th Baron <PERSON>m</a>, English noble and politician (d. 1597)", "links": [{"title": "<PERSON>, 10th Baron <PERSON>m", "link": "https://wikipedia.org/wiki/<PERSON>,_10th_Baron_<PERSON>"}]}, {"year": "1530", "text": "<PERSON>, French philosopher and judge (d. 1563)", "html": "1530 - <a href=\"https://wikipedia.org/wiki/%C3%89tienne_de_La_Bo%C3%A9tie\" title=\"<PERSON>\"><PERSON></a>, French philosopher and judge (d. 1563)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/%C3%89tienne_de_La_Bo%C3%A9tie\" title=\"<PERSON>\"><PERSON></a>, French philosopher and judge (d. 1563)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/%C3%89tienne_de_La_Bo%C3%A9tie"}]}, {"year": "1539", "text": "<PERSON>, French lawyer and scholar (d. 1596)", "html": "1539 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French lawyer and scholar (d. 1596)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French lawyer and scholar (d. 1596)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1550", "text": "<PERSON> of Saxe-Lauenburg, Prince-Archbishop of Bremen, Prince-Bishop of Osnabruck and Paderborn (d. 1585)", "html": "1550 - <a href=\"https://wikipedia.org/wiki/<PERSON>_of_Saxe-Lauenburg\" title=\"<PERSON> of Saxe-Lauenburg\"><PERSON> of Saxe-Lauenburg</a>, <a href=\"https://wikipedia.org/wiki/Prince-Archbishopric_of_Bremen\" title=\"Prince-Archbishopric of Bremen\">Prince-Archbishop of Bremen</a>, Prince-Bishop of Osnabruck and Paderborn (d. 1585)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_of_Saxe-Lauenburg\" title=\"Henry of Saxe-Lauenburg\"><PERSON> of Saxe-Lauenburg</a>, <a href=\"https://wikipedia.org/wiki/Prince-Archbishopric_of_Bremen\" title=\"Prince-Archbishopric of Bremen\">Prince-Archbishop of Bremen</a>, Prince-Bishop of Osnabruck and Paderborn (d. 1585)", "links": [{"title": "<PERSON> of Saxe-Lauenburg", "link": "https://wikipedia.org/wiki/<PERSON>_of_Saxe-Lauenburg"}, {"title": "Prince-Archbishopric of Bremen", "link": "https://wikipedia.org/wiki/Prince-Archbishopric_of_Bremen"}]}, {"year": "1567", "text": "<PERSON>, 1st Count of Gondomar, Spanish academic and diplomat (d. 1626)", "html": "1567 - <a href=\"https://wikipedia.org/wiki/Diego_Sarmiento_de_Acu%C3%B1a,_1st_Count_of_Gondomar\" title=\"<PERSON>, 1st Count of Gondomar\"><PERSON>, 1st Count of Gondomar</a>, Spanish academic and diplomat (d. 1626)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>rmiento_de_Acu%C3%B1a,_1st_Count_of_Gondomar\" title=\"<PERSON>, 1st Count of Gondomar\"><PERSON>, 1st Count of Gondomar</a>, Spanish academic and diplomat (d. 1626)", "links": [{"title": "<PERSON>, 1st Count of Gondomar", "link": "https://wikipedia.org/wiki/Diego_Sarmiento_de_Acu%C3%B1a,_1st_Count_of_Gondomar"}]}, {"year": "1585", "text": "<PERSON>, Polish mathematician, physician, and astronomer (d. 1652)", "html": "1585 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>%C5%BCek\" title=\"<PERSON>\"><PERSON></a>, Polish mathematician, physician, and astronomer (d. 1652)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>%C5%BCek\" title=\"<PERSON>\"><PERSON></a>, Polish mathematician, physician, and astronomer (d. 1652)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_B<PERSON>%C5%BCek"}]}, {"year": "1596", "text": "<PERSON>, Italian painter (d. 1669)", "html": "1596 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian painter (d. 1669)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian painter (d. 1669)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1607", "text": "<PERSON>, German poet and translator (d. 1658)", "html": "1607 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>%C3%B6rffer\" title=\"<PERSON>\"><PERSON></a>, German poet and translator (d. 1658)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>%C3%B6rffer\" title=\"<PERSON>\"><PERSON></a>, German poet and translator (d. 1658)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>%C3%B6rffer"}]}, {"year": "1609", "text": "<PERSON>, Lord Chief Justice of England (d. 1676)", "html": "1609 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(jurist)\" title=\"<PERSON> (jurist)\"><PERSON></a>, Lord Chief Justice of England (d. 1676)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(jurist)\" title=\"<PERSON> (jurist)\"><PERSON></a>, Lord Chief Justice of England (d. 1676)", "links": [{"title": "<PERSON> (jurist)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>(jurist)"}]}, {"year": "1611", "text": "<PERSON><PERSON><PERSON>, comte <PERSON>, Italian-French commander (d. 1656)", "html": "1611 - <a href=\"https://wikipedia.org/wiki/Fran%C3%<PERSON><PERSON><PERSON><PERSON><PERSON>,_comte_<PERSON>_B<PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>, comte de Broglie\"><PERSON><PERSON><PERSON>, comte de <PERSON></a>, Italian-French commander (d. 1656)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Fran%C3%<PERSON><PERSON><PERSON><PERSON><PERSON>,_comte_<PERSON>_Brog<PERSON>\" title=\"<PERSON><PERSON><PERSON>, comte de Broglie\"><PERSON><PERSON><PERSON>, comte de <PERSON></a>, Italian-French commander (d. 1656)", "links": [{"title": "<PERSON><PERSON><PERSON>, comte de <PERSON>", "link": "https://wikipedia.org/wiki/Fran%C3%<PERSON><PERSON><PERSON>-<PERSON>,_comte_<PERSON>_<PERSON><PERSON>"}]}, {"year": "1625", "text": "<PERSON>, Irish archbishop and saint (d. 1681)", "html": "1625 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Irish archbishop and saint (d. 1681)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Irish archbishop and saint (d. 1681)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1636", "text": "<PERSON><PERSON><PERSON><PERSON><PERSON>, French poet and critic (d. 1711)", "html": "1636 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>-Despr%C3%A9aux\" title=\"<PERSON>\"><PERSON></a>, French poet and critic (d. 1711)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>-Despr%C3%A9aux\" title=\"<PERSON>\"><PERSON></a>, French poet and critic (d. 1711)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>-Despr%C3%A9aux"}]}, {"year": "1643", "text": "<PERSON>, English priest, historian, and author (d. 1737)", "html": "1643 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English priest, historian, and author (d. 1737)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English priest, historian, and author (d. 1737)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1661", "text": "<PERSON><PERSON><PERSON>, French actor and playwright (d. 1725)", "html": "1661 - <a href=\"https://wikipedia.org/wiki/Florent_Carton_Dancourt\" class=\"mw-redirect\" title=\"Florent Carton Dancourt\">Florent <PERSON><PERSON></a>, French actor and playwright (d. 1725)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Florent_Carton_Dancourt\" class=\"mw-redirect\" title=\"Florent Carton Dancourt\">Florent <PERSON></a>, French actor and playwright (d. 1725)", "links": [{"title": "Florent <PERSON>", "link": "https://wikipedia.org/wiki/Florent_Car<PERSON>_Dancourt"}]}, {"year": "1661", "text": "<PERSON>, <PERSON>, heir apparent to the throne of France (d. 1711)", "html": "1661 - <a href=\"https://wikipedia.org/wiki/<PERSON>,_Grand_<PERSON>\" title=\"<PERSON>, <PERSON> Dauphin\"><PERSON>, Grand <PERSON></a>, heir apparent to the throne of France (d. 1711)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>,_Grand_<PERSON>\" title=\"<PERSON>, <PERSON> Dauphin\"><PERSON>, <PERSON></a>, heir apparent to the throne of France (d. 1711)", "links": [{"title": "<PERSON>, <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>,_<PERSON>_<PERSON><PERSON>"}]}, {"year": "1666", "text": "<PERSON>, English botanist and curator (d. 1738)", "html": "1666 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English botanist and curator (d. 1738)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English botanist and curator (d. 1738)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1720", "text": "<PERSON><PERSON>aint<PERSON><PERSON>, French admiral (d. 1791)", "html": "1720 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>-<PERSON>_<PERSON>_<PERSON>_<PERSON>_Motte\" title=\"To<PERSON><PERSON><PERSON><PERSON><PERSON> Motte\"><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON></a>, French admiral (d. 1791)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>-<PERSON>_<PERSON>_<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>tte\"><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON></a>, French admiral (d. 1791)", "links": [{"title": "Toussaint<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>-<PERSON>_<PERSON>_<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1727", "text": "<PERSON>, Russian art collector and philanthropist (d. 1797)", "html": "1727 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian art collector and philanthropist (d. 1797)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian art collector and philanthropist (d. 1797)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1752", "text": "<PERSON><PERSON><PERSON>, Polish general, politician (d. 1826)", "html": "1752 - <a href=\"https://wikipedia.org/wiki/J%C3%B3<PERSON>_<PERSON>%C4%85czek\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Polish general, politician (d. 1826)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/J%C3%B3<PERSON>_<PERSON>%C4%85czek\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Polish general, politician (d. 1826)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/J%C3%B3<PERSON>_<PERSON><PERSON>%C4%85czek"}]}, {"year": "1757", "text": "<PERSON>, Italian sculptor and educator (d. 1822)", "html": "1757 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian sculptor and educator (d. 1822)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian sculptor and educator (d. 1822)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1762", "text": "<PERSON>, English lawyer and politician, Prime Minister of the United Kingdom (d. 1812)", "html": "1762 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English lawyer and politician, <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_the_United_Kingdom\" title=\"Prime Minister of the United Kingdom\">Prime Minister of the United Kingdom</a> (d. 1812)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English lawyer and politician, <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_the_United_Kingdom\" title=\"Prime Minister of the United Kingdom\">Prime Minister of the United Kingdom</a> (d. 1812)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Prime Minister of the United Kingdom", "link": "https://wikipedia.org/wiki/Prime_Minister_of_the_United_Kingdom"}]}, {"year": "1769", "text": "<PERSON><PERSON><PERSON><PERSON>, German author and activist (d. 1850)", "html": "1769 - <a href=\"https://wikipedia.org/wiki/Gar<PERSON><PERSON>_<PERSON>el\" title=\"Garlie<PERSON> Merkel\"><PERSON><PERSON><PERSON><PERSON></a>, German author and activist (d. 1850)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Gar<PERSON><PERSON>_<PERSON>\" title=\"Garlie<PERSON> Merkel\"><PERSON><PERSON><PERSON><PERSON></a>, German author and activist (d. 1850)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1778", "text": "<PERSON> of Sweden (d. 1837)", "html": "1778 - <a href=\"https://wikipedia.org/wiki/Gustav_IV_Adolf_of_Sweden\" class=\"mw-redirect\" title=\"Gustav IV Adolf of Sweden\"><PERSON> IV Adolf of Sweden</a> (d. 1837)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Gustav_IV_Adolf_of_Sweden\" class=\"mw-redirect\" title=\"Gustav IV Adolf of Sweden\"><PERSON> IV Adolf of Sweden</a> (d. 1837)", "links": [{"title": "<PERSON> of Sweden", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_of_Sweden"}]}, {"year": "1782", "text": "<PERSON><PERSON> <PERSON><PERSON>, 1st Viscount <PERSON>, English politician, Prime Minister of the United Kingdom (d. 1859)", "html": "1782 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON><PERSON>,_1st_Viscount_<PERSON>\" title=\"<PERSON><PERSON> <PERSON><PERSON>, 1st Viscount <PERSON>\"><PERSON><PERSON> <PERSON><PERSON>, 1st Viscount <PERSON></a>, English politician, <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_the_United_Kingdom\" title=\"Prime Minister of the United Kingdom\">Prime Minister of the United Kingdom</a> (d. 1859)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>,_1st_Viscount_<PERSON>\" title=\"<PERSON><PERSON> <PERSON><PERSON>, 1st Viscount <PERSON>\"><PERSON><PERSON> <PERSON><PERSON>, 1st Viscount <PERSON></a>, English politician, <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_the_United_Kingdom\" title=\"Prime Minister of the United Kingdom\">Prime Minister of the United Kingdom</a> (d. 1859)", "links": [{"title": "<PERSON><PERSON> <PERSON><PERSON>, 1st Viscount <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>,_1st_Viscount_<PERSON>"}, {"title": "Prime Minister of the United Kingdom", "link": "https://wikipedia.org/wiki/Prime_Minister_of_the_United_Kingdom"}]}, {"year": "1808", "text": "<PERSON>, English-American religious leader, 3rd President of The Church of Jesus Christ of Latter-day Saints (d. 1887)", "html": "1808 - <a href=\"https://wikipedia.org/wiki/<PERSON>(Mormon)\" title=\"<PERSON> (Mormon)\"><PERSON></a>, English-American religious leader, 3rd <a href=\"https://wikipedia.org/wiki/President_of_The_Church_of_Jesus_Christ_of_Latter-day_Saints\" class=\"mw-redirect\" title=\"President of The Church of Jesus Christ of Latter-day Saints\">President of The Church of Jesus Christ of Latter-day Saints</a> (d. 1887)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>(Mormon)\" title=\"<PERSON> (Mormon)\"><PERSON></a>, English-American religious leader, 3rd <a href=\"https://wikipedia.org/wiki/President_of_The_Church_of_Jesus_Christ_of_Latter-day_Saints\" class=\"mw-redirect\" title=\"President of The Church of Jesus Christ of Latter-day Saints\">President of The Church of Jesus Christ of Latter-day Saints</a> (d. 1887)", "links": [{"title": "<PERSON> (Mormon)", "link": "https://wikipedia.org/wiki/<PERSON>_(Mormon)"}, {"title": "President of The Church of Jesus Christ of Latter-day Saints", "link": "https://wikipedia.org/wiki/President_of_The_Church_of_Jesus_Christ_of_Latter-day_Saints"}]}, {"year": "1831", "text": "<PERSON>, English-New Zealand politician, 10th Prime Minister of New Zealand (d. 1892)", "html": "1831 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English-New Zealand politician, 10th <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_New_Zealand\" title=\"Prime Minister of New Zealand\">Prime Minister of New Zealand</a> (d. 1892)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English-New Zealand politician, 10th <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_New_Zealand\" title=\"Prime Minister of New Zealand\">Prime Minister of New Zealand</a> (d. 1892)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}, {"title": "Prime Minister of New Zealand", "link": "https://wikipedia.org/wiki/Prime_Minister_of_New_Zealand"}]}, {"year": "1838", "text": "11th Dalai Lama (d. 1856)", "html": "1838 - <a href=\"https://wikipedia.org/wiki/11th_Dal<PERSON>_Lama\" title=\"11th Dalai Lama\">11th Dalai Lama</a> (d. 1856)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/11th_Dalai_Lama\" title=\"11th Dalai Lama\">11th Dalai Lama</a> (d. 1856)", "links": [{"title": "11th Dal<PERSON> Lama", "link": "https://wikipedia.org/wiki/11th_<PERSON><PERSON>_<PERSON>"}]}, {"year": "1839", "text": "<PERSON>, Ottoman general and politician, 227th Grand Vizier of the Ottoman Empire (d. 1919)", "html": "1839 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Ottoman general and politician, 227th <a href=\"https://wikipedia.org/wiki/List_of_Ottoman_Grand_Viziers\" class=\"mw-redirect\" title=\"List of Ottoman Grand Viziers\">Grand Vizier of the Ottoman Empire</a> (d. 1919)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Ottoman general and politician, 227th <a href=\"https://wikipedia.org/wiki/List_of_Ottoman_Grand_Viziers\" class=\"mw-redirect\" title=\"List of Ottoman Grand Viziers\">Grand Vizier of the Ottoman Empire</a> (d. 1919)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "List of Ottoman Grand Viziers", "link": "https://wikipedia.org/wiki/List_of_Ottoman_Grand_Viziers"}]}, {"year": "1847", "text": "<PERSON>, Canadian-English soprano and actress (d. 1930)", "html": "1847 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian-English soprano and actress (d. 1930)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian-English soprano and actress (d. 1930)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1847", "text": "<PERSON><PERSON><PERSON>, Vietnamese emperor (d. 1883)", "html": "1847 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_Ho<PERSON>\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Vietnamese emperor (d. 1883)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>\" class=\"mw-redirect\" title=\"<PERSON><PERSON> <PERSON>a\"><PERSON><PERSON><PERSON></a>, Vietnamese emperor (d. 1883)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Hiep_Hoa"}]}, {"year": "1848", "text": "<PERSON>, American physician, educator and abolitionist (d. 1919)", "html": "1848 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American physician, educator and abolitionist (d. 1919)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American physician, educator and abolitionist (d. 1919)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1848", "text": "<PERSON><PERSON>, French painter (d. 1884)", "html": "1848 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French painter (d. 1884)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French painter (d. 1884)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON><PERSON>-<PERSON>"}]}, {"year": "1849", "text": "<PERSON>, American painter and educator (d. 1916)", "html": "1849 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American painter and educator (d. 1916)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American painter and educator (d. 1916)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1859", "text": "<PERSON>, American educator, lawyer, and politician, 50th Governor of North Carolina (d. 1912)", "html": "1859 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American educator, lawyer, and politician, 50th <a href=\"https://wikipedia.org/wiki/Governor_of_North_Carolina\" title=\"Governor of North Carolina\">Governor of North Carolina</a> (d. 1912)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American educator, lawyer, and politician, 50th <a href=\"https://wikipedia.org/wiki/Governor_of_North_Carolina\" title=\"Governor of North Carolina\">Governor of North Carolina</a> (d. 1912)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "Governor of North Carolina", "link": "https://wikipedia.org/wiki/Governor_of_North_Carolina"}]}, {"year": "1862", "text": "<PERSON>, Dutch organist and composer (d. 1941)", "html": "1862 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch organist and composer (d. 1941)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch organist and composer (d. 1941)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1864", "text": "Princess <PERSON> of Hesse and by Rhine (d. 1918)", "html": "1864 - <a href=\"https://wikipedia.org/wiki/Princess_Elisabeth_of_Hesse_and_by_Rhine_(1864%E2%80%931918)\" class=\"mw-redirect\" title=\"Princess <PERSON> of Hesse and by Rhine (1864-1918)\">Princess <PERSON> of Hesse and by Rhine</a> (d. 1918)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Princess_Elisabeth_of_Hesse_and_by_Rhine_(1864%E2%80%931918)\" class=\"mw-redirect\" title=\"Princess <PERSON> of Hesse and by Rhine (1864-1918)\">Princess <PERSON> of Hesse and by Rhine</a> (d. 1918)", "links": [{"title": "Princess <PERSON> of Hesse and by Rhine (1864-1918)", "link": "https://wikipedia.org/wiki/Princess_Elisabeth_of_Hesse_and_by_Rhine_(1864%E2%80%931918)"}]}, {"year": "1871", "text": "<PERSON>, American poet, novelist, and short story writer (d. 1900)", "html": "1871 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American poet, novelist, and short story writer (d. 1900)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American poet, novelist, and short story writer (d. 1900)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1872", "text": "<PERSON>, Belgian-French painter (d. 1946)", "html": "1872 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Belgian-French painter (d. 1946)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Belgian-French painter (d. 1946)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1877", "text": "<PERSON>, English composer (d. 1953)", "html": "1877 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English composer (d. 1953)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>uilter\"><PERSON></a>, English composer (d. 1953)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>er"}]}, {"year": "1878", "text": "<PERSON>, Estonian painter and educator (d. 1925)", "html": "1878 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A4gi\" title=\"<PERSON>\"><PERSON></a>, Estonian painter and educator (d. 1925)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A4gi\" title=\"<PERSON>\"><PERSON></a>, Estonian painter and educator (d. 1925)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Konrad_M%C3%A4gi"}]}, {"year": "1878", "text": "<PERSON>, Argentinian academic and politician, Nobel Prize laureate (d. 1959)", "html": "1878 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Argentinian academic and politician, <a href=\"https://wikipedia.org/wiki/Nobel_Peace_Prize\" title=\"Nobel Peace Prize\">Nobel Prize</a> laureate (d. 1959)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Argentinian academic and politician, <a href=\"https://wikipedia.org/wiki/Nobel_Peace_Prize\" title=\"Nobel Peace Prize\">Nobel Prize</a> laureate (d. 1959)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "Nobel Peace Prize", "link": "https://wikipedia.org/wiki/Nobel_Peace_Prize"}]}, {"year": "1880", "text": "<PERSON><PERSON><PERSON>, Polish-American author and playwright (d. 1957)", "html": "1880 - <a href=\"https://wikipedia.org/wiki/Shole<PERSON>_<PERSON>\" title=\"Shole<PERSON>\"><PERSON><PERSON><PERSON></a>, Polish-American author and playwright (d. 1957)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>hole<PERSON>_<PERSON>\" title=\"<PERSON>hole<PERSON>\"><PERSON><PERSON><PERSON></a>, Polish-American author and playwright (d. 1957)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Sholem_<PERSON>ch"}]}, {"year": "1880", "text": "<PERSON><PERSON>, American journalist and poet (d. 1954)", "html": "1880 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_Rice\" title=\"Grantland Rice\"><PERSON><PERSON></a>, American journalist and poet (d. 1954)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Grant<PERSON>_Rice\" title=\"Grantland Rice\"><PERSON><PERSON></a>, American journalist and poet (d. 1954)", "links": [{"title": "<PERSON>land Rice", "link": "https://wikipedia.org/wiki/Grantland_Rice"}]}, {"year": "1880", "text": "<PERSON>, German meteorologist and geophysicist (d. 1930)", "html": "1880 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German meteorologist and geophysicist (d. 1930)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German meteorologist and geophysicist (d. 1930)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1881", "text": "<PERSON><PERSON><PERSON>, Greek admiral (d. 1965)", "html": "1881 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Greek admiral (d. 1965)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Greek admiral (d. 1965)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1886", "text": "<PERSON>, Austrian-American author and poet (d. 1951)", "html": "1886 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Austrian-American author and poet (d. 1951)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Austrian-American author and poet (d. 1951)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1886", "text": "<PERSON><PERSON><PERSON><PERSON>, Japanese poet and critic (d. 1942)", "html": "1886 - <a href=\"https://wikipedia.org/wiki/Sakutar%C5%8D_<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Japanese poet and critic (d. 1942)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Sakutar%C5%8D_<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Japanese poet and critic (d. 1942)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Sakutar%C5%8D_<PERSON>gi<PERSON>"}]}, {"year": "1887", "text": "<PERSON><PERSON><PERSON><PERSON>, English painter and illustrator (d. 1976)", "html": "1887 - <a href=\"https://wikipedia.org/wiki/L.S._Lowry\" class=\"mw-redirect\" title=\"L.S. Lowry\"><PERSON><PERSON><PERSON><PERSON></a>, English painter and illustrator (d. 1976)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/L.S._Lowry\" class=\"mw-redirect\" title=\"L.S. Lowry\"><PERSON><PERSON><PERSON><PERSON></a>, English painter and illustrator (d. 1976)", "links": [{"title": "L.S. Lowry", "link": "https://wikipedia.org/wiki/L.S._Lowry"}]}, {"year": "1888", "text": "<PERSON>, German-American painter and illustrator (d. 1971)", "html": "1888 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German-American painter and illustrator (d. 1971)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German-American painter and illustrator (d. 1971)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1888", "text": "<PERSON><PERSON><PERSON>, Polish cleric and academic (d. 1975)", "html": "1888 - <a href=\"https://wikipedia.org/wiki/Micha%C5%82_Sopo%C4%87ko\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Polish cleric and academic (d. 1975)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Micha%C5%82_Sopo%C4%87ko\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Polish cleric and academic (d. 1975)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Micha%C5%82_<PERSON>po%C4%87ko"}]}, {"year": "1889", "text": "<PERSON>, German painter and photographer (d. 1978)", "html": "1889 - <a href=\"https://wikipedia.org/wiki/<PERSON>_H%C3%B6ch\" title=\"<PERSON>\"><PERSON></a>, German painter and photographer (d. 1978)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_H%C3%B6ch\" title=\"<PERSON>\"><PERSON></a>, German painter and photographer (d. 1978)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Hannah_H%C3%B6ch"}]}, {"year": "1889", "text": "<PERSON>, Baron <PERSON>, English academic and politician, Secretary of State for Commonwealth Relations, Nobel Prize laureate (d. 1982)", "html": "1889 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Baron_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>, Baron <PERSON>\"><PERSON>, Baron <PERSON></a>, English academic and politician, <a href=\"https://wikipedia.org/wiki/Secretary_of_State_for_Commonwealth_Relations\" title=\"Secretary of State for Commonwealth Relations\">Secretary of State for Commonwealth Relations</a>, <a href=\"https://wikipedia.org/wiki/Nobel_Peace_Prize\" title=\"Nobel Peace Prize\">Nobel Prize</a> laureate (d. 1982)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>,_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>, Baron <PERSON>\"><PERSON>, Baron <PERSON></a>, English academic and politician, <a href=\"https://wikipedia.org/wiki/Secretary_of_State_for_Commonwealth_Relations\" title=\"Secretary of State for Commonwealth Relations\">Secretary of State for Commonwealth Relations</a>, <a href=\"https://wikipedia.org/wiki/Nobel_Peace_Prize\" title=\"Nobel Peace Prize\">Nobel Prize</a> laureate (d. 1982)", "links": [{"title": "<PERSON>, <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_<PERSON>_<PERSON>-<PERSON>"}, {"title": "Secretary of State for Commonwealth Relations", "link": "https://wikipedia.org/wiki/Secretary_of_State_for_Commonwealth_Relations"}, {"title": "Nobel Peace Prize", "link": "https://wikipedia.org/wiki/Nobel_Peace_Prize"}]}, {"year": "1896", "text": "<PERSON>, English author, poet, and critic (d. 1974)", "html": "1896 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English author, poet, and critic (d. 1974)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English author, poet, and critic (d. 1974)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1898", "text": "<PERSON>, Belgian race car driver (d. 1960)", "html": "1898 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Belgian race car driver (d. 1960)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Belgian race car driver (d. 1960)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1898", "text": "<PERSON><PERSON><PERSON>, American singer-songwriter and pianist (d. 1986)", "html": "1898 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American singer-songwriter and pianist (d. 1986)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American singer-songwriter and pianist (d. 1986)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1902", "text": "<PERSON><PERSON>, Norwegian journalist, author, poet, and playwright (d. 1943)", "html": "1902 - <a href=\"https://wikipedia.org/wiki/Nordahl_Grieg\" title=\"Nordahl Grieg\"><PERSON><PERSON> Grieg</a>, Norwegian journalist, author, poet, and playwright (d. 1943)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Nordahl_Grieg\" title=\"Nordahl Grieg\"><PERSON><PERSON> Grieg</a>, Norwegian journalist, author, poet, and playwright (d. 1943)", "links": [{"title": "Nordahl Grieg", "link": "https://wikipedia.org/wiki/Nordahl_Grieg"}]}, {"year": "1902", "text": "<PERSON><PERSON>, German conductor (d. 1987)", "html": "1902 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, German conductor (d. 1987)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, German conductor (d. 1987)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1903", "text": "<PERSON>, Irish-born British actor (d. 1973)", "html": "1903 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Irish-born British actor (d. 1973)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Adrian\"><PERSON></a>, Irish-born British actor (d. 1973)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1903", "text": "<PERSON>, Jr., Australian footballer (d. 1963)", "html": "1903 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Jr.\" class=\"mw-redirect\" title=\"<PERSON>, Jr.\"><PERSON>, Jr.</a>, Australian footballer (d. 1963)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Jr.\" class=\"mw-redirect\" title=\"<PERSON>, Jr.\"><PERSON>, Jr.</a>, Australian footballer (d. 1963)", "links": [{"title": "<PERSON>, Jr.", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Jr."}]}, {"year": "1904", "text": "<PERSON>, American silent film actress (d. 1996)", "html": "1904 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, American silent film actress (d. 1996)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, American silent film actress (d. 1996)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1905", "text": "<PERSON><PERSON><PERSON><PERSON>, Canadian-French painter and educator (d. 1960)", "html": "1905 - <a href=\"https://wikipedia.org/wiki/<PERSON>-%C3%89mile_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Canadian-French painter and educator (d. 1960)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>-%C3%89mile_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Canadian-French painter and educator (d. 1960)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>-%C3%89mile_Bord<PERSON>s"}]}, {"year": "1906", "text": "<PERSON>, American boxer (d. 1968)", "html": "1906 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American boxer (d. 1968)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American boxer (d. 1968)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1907", "text": "<PERSON><PERSON>, American boxer (d. 1976)", "html": "1907 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American boxer (d. 1976)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American boxer (d. 1976)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1909", "text": "<PERSON>, South African-Australian rugby league player  (d. 1960)", "html": "1909 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, South African-Australian rugby league player (d. 1960)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, South African-Australian rugby league player (d. 1960)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1911", "text": "<PERSON><PERSON>, Burmese monk and scholar (d. 1993)", "html": "1911 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>ada<PERSON>\" title=\"<PERSON><PERSON> Sayadaw\"><PERSON><PERSON></a>, Burmese monk and scholar (d. 1993)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> Sayadaw\"><PERSON><PERSON></a>, Burmese monk and scholar (d. 1993)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>adaw"}]}, {"year": "1911", "text": "<PERSON>, French historian and author (d. 2007)", "html": "1911 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French historian and author (d. 2007)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French historian and author (d. 2007)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1912", "text": "<PERSON><PERSON>, German-Canadian rabbi and author (d. 2012)", "html": "1912 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, German-Canadian rabbi and author (d. 2012)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, German-Canadian rabbi and author (d. 2012)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>t"}]}, {"year": "1914", "text": "<PERSON><PERSON>, Romanian-American rabbi (d. 2006)", "html": "1914 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_(Satmar)\" title=\"<PERSON><PERSON> (Satmar)\"><PERSON><PERSON></a>, Romanian-American rabbi (d. 2006)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_(Satmar)\" title=\"<PERSON><PERSON> (Satmar)\"><PERSON><PERSON></a>, Romanian-American rabbi (d. 2006)", "links": [{"title": "<PERSON><PERSON> (Satmar)", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_(Satmar)"}]}, {"year": "1915", "text": "<PERSON>-<PERSON><PERSON>, American painter, poet, and educator, co-founded the DuSable Museum of African American History (d. 2010)", "html": "1915 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American painter, poet, and educator, co-founded the <a href=\"https://wikipedia.org/wiki/DuSable_Museum_of_African_American_History\" class=\"mw-redirect\" title=\"DuSable Museum of African American History\">DuSable Museum of African American History</a> (d. 2010)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American painter, poet, and educator, co-founded the <a href=\"https://wikipedia.org/wiki/DuSable_Museum_of_African_American_History\" class=\"mw-redirect\" title=\"DuSable Museum of African American History\">DuSable Museum of African American History</a> (d. 2010)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "DuSable Museum of African American History", "link": "https://wikipedia.org/wiki/DuSable_Museum_of_African_American_History"}]}, {"year": "1917", "text": "<PERSON><PERSON>, American author (d. 1983)", "html": "1917 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American author (d. 1983)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American author (d. 1983)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1917", "text": "<PERSON>, American engineer and politician (d. 2011)", "html": "1917 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American engineer and politician (d. 2011)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American engineer and politician (d. 2011)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "1918", "text": "<PERSON>, English-American race car driver (d. 1966)", "html": "1918 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English-American race car driver (d. 1966)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English-American race car driver (d. 1966)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1919", "text": "<PERSON>, English-Austrian mathematician and cosmologist (d. 2005)", "html": "1919 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English-Austrian mathematician and cosmologist (d. 2005)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English-Austrian mathematician and cosmologist (d. 2005)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1920", "text": "<PERSON>, American journalist and author (d. 2010)", "html": "1920 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American journalist and author (d. 2010)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American journalist and author (d. 2010)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1920", "text": "<PERSON>, English sportscaster (d. 2011)", "html": "1920 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English sportscaster (d. 2011)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English sportscaster (d. 2011)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1921", "text": "<PERSON>, American pilot and songwriter (d. 2006)", "html": "1921 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American pilot and songwriter (d. 2006)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>\" title=\"<PERSON>\"><PERSON></a>, American pilot and songwriter (d. 2006)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "1921", "text": "<PERSON>, German businessman (d. 1967)", "html": "1921 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German businessman (d. 1967)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German businessman (d. 1967)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1922", "text": "<PERSON>, American actor (d. 2016)", "html": "1922 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor (d. 2016)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor (d. 2016)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "1922", "text": "<PERSON>, American basketball player and coach (d. 2006)", "html": "1922 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player and coach (d. 2006)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player and coach (d. 2006)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1923", "text": "<PERSON>, Spanish soprano and actress (d. 2005)", "html": "1923 - <a href=\"https://wikipedia.org/wiki/Victoria_de_los_%C3%81ngeles\" title=\"Victoria de los Ángeles\"><PERSON> los Ángeles</a>, Spanish soprano and actress (d. 2005)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Victoria_de_los_%C3%81ngeles\" title=\"Victoria de los Ángeles\"><PERSON> los Ángeles</a>, Spanish soprano and actress (d. 2005)", "links": [{"title": "Victoria de los Ángeles", "link": "https://wikipedia.org/wiki/Victoria_de_los_%C3%81ngeles"}]}, {"year": "1923", "text": "<PERSON>, Canadian-American author (d. 2001)", "html": "1923 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian-American author (d. 2001)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian-American author (d. 2001)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "1923", "text": "<PERSON><PERSON><PERSON>, German-Israeli academic and jurist (d. 2013)", "html": "1923 - <a href=\"https://wikipedia.org/wiki/Menachem_Elon\" title=\"Menachem Elon\"><PERSON><PERSON><PERSON></a>, German-Israeli academic and jurist (d. 2013)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Menachem_Elon\" title=\"<PERSON>ache<PERSON> Elon\"><PERSON><PERSON><PERSON></a>, German-Israeli academic and jurist (d. 2013)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Menachem_Elon"}]}, {"year": "1923", "text": "<PERSON>, Uruguayan painter and sculptor (d. 2014)", "html": "1923 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A1ez_Vilar%C3%B3\" title=\"<PERSON>\"><PERSON></a>, Uruguayan painter and sculptor (d. 2014)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A1ez_Vilar%C3%B3\" title=\"<PERSON>\"><PERSON></a>, Uruguayan painter and sculptor (d. 2014)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Carlos_P%C3%A1ez_Vilar%C3%B3"}]}, {"year": "1924", "text": "<PERSON><PERSON><PERSON><PERSON>, Turkish engineer and politician, 9th President of Turkey (d. 2015)", "html": "1924 - <a href=\"https://wikipedia.org/wiki/S%C3%BCleyman_Demirel\" title=\"Süley<PERSON> Demirel\"><PERSON><PERSON><PERSON><PERSON></a>, Turkish engineer and politician, 9th <a href=\"https://wikipedia.org/wiki/President_of_Turkey\" title=\"President of Turkey\">President of Turkey</a> (d. 2015)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/S%C3%BCleyman_Demirel\" title=\"Süleyman Demirel\"><PERSON><PERSON><PERSON><PERSON></a>, Turkish engineer and politician, 9th <a href=\"https://wikipedia.org/wiki/President_of_Turkey\" title=\"President of Turkey\">President of Turkey</a> (d. 2015)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/S%C3%<PERSON><PERSON><PERSON>_<PERSON><PERSON><PERSON>"}, {"title": "President of Turkey", "link": "https://wikipedia.org/wiki/President_of_Turkey"}]}, {"year": "1924", "text": "<PERSON><PERSON><PERSON>, Canadian academic and politician, 19th Canadian Minister of Labour (d. 1995)", "html": "1924 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>%C3%A9pin\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Canadian academic and politician, 19th <a href=\"https://wikipedia.org/wiki/Minister_of_Labour_(Canada)\" title=\"Minister of Labour (Canada)\">Canadian Minister of Labour</a> (d. 1995)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>%C3%A9pin\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Canadian academic and politician, 19th <a href=\"https://wikipedia.org/wiki/Minister_of_Labour_(Canada)\" title=\"Minister of Labour (Canada)\">Canadian Minister of Labour</a> (d. 1995)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>-<PERSON>_<PERSON>%C3%A9pin"}, {"title": "Minister of Labour (Canada)", "link": "https://wikipedia.org/wiki/Minister_of_Labour_(Canada)"}]}, {"year": "1926", "text": "<PERSON>, Greek-American sculptor (d. 2013)", "html": "1926 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Greek-American sculptor (d. 2013)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Greek-American sculptor (d. 2013)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1926", "text": "<PERSON>, American saxophonist (d. 2024)", "html": "1926 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American saxophonist (d. 2024)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American saxophonist (d. 2024)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1926", "text": "<PERSON>, American actress and game show panelist (d. 2015)", "html": "1926 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress and game show panelist (d. 2015)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress and game show panelist (d. 2015)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1927", "text": "<PERSON>, German documentary filmmaker", "html": "1927 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%BCls\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, German documentary filmmaker", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%BCls\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, German documentary filmmaker", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Marcel_Oph%C3%BCls"}]}, {"year": "1927", "text": "<PERSON>, Puerto Rican baseball player and coach (d. 2005)", "html": "1927 - <a href=\"https://wikipedia.org/wiki/<PERSON>_Power_(baseball)\" class=\"mw-redirect\" title=\"<PERSON> (baseball)\"><PERSON></a>, Puerto Rican baseball player and coach (d. 2005)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Vic_Power_(baseball)\" class=\"mw-redirect\" title=\"<PERSON> (baseball)\"><PERSON></a>, Puerto Rican baseball player and coach (d. 2005)", "links": [{"title": "<PERSON> (baseball)", "link": "https://wikipedia.org/wiki/Vic_Power_(baseball)"}]}, {"year": "1928", "text": "<PERSON>, American weightlifter (d. 2013)", "html": "1928 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(weightlifter)\" title=\"<PERSON> (weightlifter)\"><PERSON></a>, American weightlifter (d. 2013)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(weightlifter)\" title=\"<PERSON> (weightlifter)\"><PERSON></a>, American weightlifter (d. 2013)", "links": [{"title": "<PERSON> (weightlifter)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(weightlifter)"}]}, {"year": "1929", "text": "<PERSON>, American lawyer and politician (d. 2003)", "html": "1929 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and politician (d. 2003)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and politician (d. 2003)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1930", "text": "<PERSON><PERSON> <PERSON><PERSON>, American playwright and author (d. 2017)", "html": "1930 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> <PERSON><PERSON>\"><PERSON><PERSON> <PERSON><PERSON></a>, American playwright and author (d. 2017)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> <PERSON><PERSON>\"><PERSON><PERSON> <PERSON><PERSON></a>, American playwright and author (d. 2017)", "links": [{"title": "<PERSON><PERSON> <PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "1930", "text": "<PERSON>, American baseball player and coach (d. 2014)", "html": "1930 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player and coach (d. 2014)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player and coach (d. 2014)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1931", "text": "<PERSON><PERSON><PERSON>, Israeli wrestler and coach (d. 1972)", "html": "1931 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>freund\" title=\"<PERSON><PERSON><PERSON>freund\"><PERSON><PERSON><PERSON></a>, Israeli wrestler and coach (d. 1972)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>freund\" title=\"<PERSON><PERSON><PERSON>fre<PERSON>\"><PERSON><PERSON><PERSON></a>, Israeli wrestler and coach (d. 1972)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Yo<PERSON><PERSON>_<PERSON>freund"}]}, {"year": "1931", "text": "<PERSON><PERSON><PERSON>, Japanese composer (d. 2021)", "html": "1931 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese composer (d. 2021)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese composer (d. 2021)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1931", "text": "<PERSON><PERSON>, Norwegian footballer and manager (d. 2013)", "html": "1931 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Norwegian footballer and manager (d. 2013)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Norwegian footballer and manager (d. 2013)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1932", "text": "<PERSON>, Canadian ice hockey player and coach (d. 2015)", "html": "1932 - <a href=\"https://wikipedia.org/wiki/Al_Arbour\" title=\"Al Arbour\"><PERSON></a>, Canadian ice hockey player and coach (d. 2015)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Al_Arbour\" title=\"Al Arbour\"><PERSON></a>, Canadian ice hockey player and coach (d. 2015)", "links": [{"title": "Al Arbour", "link": "https://wikipedia.org/wiki/Al_Arbour"}]}, {"year": "1932", "text": "<PERSON>, Nigerian cardinal", "html": "1932 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Nigerian cardinal", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Nigerian cardinal", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1933", "text": "<PERSON>, Luxembourgian footballer and manager (d. 2012)", "html": "1933 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Luxembourgian footballer and manager (d. 2012)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Luxembourgian footballer and manager (d. 2012)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1934", "text": "<PERSON><PERSON>, Swiss-Italian businessman and politician (d. 2004)", "html": "1934 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Swiss-Italian businessman and politician (d. 2004)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Swiss-Italian businessman and politician (d. 2004)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1934", "text": "<PERSON>, English soprano and actress", "html": "1934 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English soprano and actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English soprano and actress", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1934", "text": "<PERSON>, Welsh pianist and composer (d. 1992)", "html": "1934 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Welsh pianist and composer (d. 1992)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Welsh pianist and composer (d. 1992)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1935", "text": "<PERSON>, South African golfer and sportscaster", "html": "1935 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Player\"><PERSON></a>, South African golfer and sportscaster", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Player\"><PERSON></a>, South African golfer and sportscaster", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1935", "text": "<PERSON>, Palestinian-American theorist, author, and academic (d. 2003)", "html": "1935 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Palestinian-American theorist, author, and academic (d. 2003)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Palestinian-American theorist, author, and academic (d. 2003)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1936", "text": "<PERSON><PERSON><PERSON><PERSON>, Japanese composer and conductor (d. 2020)", "html": "1936 - <a href=\"https://wikipedia.org/wiki/Kat<PERSON><PERSON><PERSON>_<PERSON>i\" title=\"Kat<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Japanese composer and conductor (d. 2020)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Kat<PERSON><PERSON><PERSON>_<PERSON>i\" title=\"Kat<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Japanese composer and conductor (d. 2020)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON><PERSON>i"}]}, {"year": "1936", "text": "<PERSON><PERSON>, Japanese lawyer and politician", "html": "1936 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Japanese lawyer and politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Japanese lawyer and politician", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1937", "text": "<PERSON>, American country music singer-songwriter", "html": "1937 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(singer)\" title=\"<PERSON> (singer)\"><PERSON></a>, American country music singer-songwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>(singer)\" title=\"<PERSON> (singer)\"><PERSON></a>, American country music singer-songwriter", "links": [{"title": "<PERSON> (singer)", "link": "https://wikipedia.org/wiki/<PERSON>_(singer)"}]}, {"year": "1938", "text": "<PERSON><PERSON>, Puerto Rican American Nuyorican writer", "html": "1938 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Puerto Rican American <a href=\"https://wikipedia.org/wiki/Nuyorican\" title=\"Nuyorican\">Nuyorican</a> writer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Puerto Rican American <a href=\"https://wikipedia.org/wiki/Nuyorican\" title=\"Nuyorican\">Nuyorican</a> writer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}, {"title": "Nuyorican", "link": "https://wikipedia.org/wiki/Nuyorican"}]}, {"year": "1939", "text": "<PERSON>, American actress (d. 2023)", "html": "1939 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress (d. 2023)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress (d. 2023)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1940", "text": "<PERSON>, Baron <PERSON>, English academic and politician", "html": "1940 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Baron_<PERSON>\" title=\"<PERSON>, Baron <PERSON>\"><PERSON>, Baron <PERSON></a>, English academic and politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Baron_<PERSON>\" title=\"<PERSON>, Baron <PERSON>\"><PERSON>, Baron <PERSON></a>, English academic and politician", "links": [{"title": "<PERSON>, Baron <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_<PERSON>_<PERSON>"}]}, {"year": "1940", "text": "<PERSON>, American pianist and composer", "html": "1940 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American pianist and composer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American pianist and composer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1940", "text": "<PERSON><PERSON>, Indian lawyer and jurist, 35th Chief Justice of India (d. 2022)", "html": "1940 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Indian lawyer and jurist, 35th <a href=\"https://wikipedia.org/wiki/Chief_Justice_of_India\" title=\"Chief Justice of India\">Chief Justice of India</a> (d. 2022)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Indian lawyer and jurist, 35th <a href=\"https://wikipedia.org/wiki/Chief_Justice_of_India\" title=\"Chief Justice of India\">Chief Justice of India</a> (d. 2022)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>"}, {"title": "Chief Justice of India", "link": "https://wikipedia.org/wiki/Chief_Justice_of_India"}]}, {"year": "1940", "text": "<PERSON>, American sergeant, author, actor, and singer-songwriter (d. 1989)", "html": "1940 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American sergeant, author, actor, and singer-songwriter (d. 1989)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American sergeant, author, actor, and singer-songwriter (d. 1989)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1941", "text": "<PERSON><PERSON><PERSON>, Argentinian footballer and manager", "html": "1941 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Argentinian footballer and manager", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Argentinian footballer and manager", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>fi<PERSON>_<PERSON>e"}]}, {"year": "1941", "text": "<PERSON>, American basketball player", "html": "1941 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1941", "text": "<PERSON>, American actor and director", "html": "1941 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor and director", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor and director", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1941", "text": "<PERSON>, English rugby player (d. 2021)", "html": "1941 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English rugby player (d. 2021)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English rugby player (d. 2021)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1942", "text": "<PERSON>, American publisher, founded Larry Flynt Publications (d. 2021)", "html": "1942 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American publisher, founded <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"Larry Flynt Publications\">Larry Flynt Publications</a> (d. 2021)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American publisher, founded <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"Larry Flynt Publications\">Larry Flynt Publications</a> (d. 2021)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Larry <PERSON> Publications", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_Publications"}]}, {"year": "1942", "text": "<PERSON>, Canadian journalist and politician, 12th Premier of Alberta (d. 2013)", "html": "1942 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian journalist and politician, 12th <a href=\"https://wikipedia.org/wiki/Premier_of_Alberta\" title=\"Premier of Alberta\">Premier of Alberta</a> (d. 2013)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian journalist and politician, 12th <a href=\"https://wikipedia.org/wiki/Premier_of_Alberta\" title=\"Premier of Alberta\">Premier of Alberta</a> (d. 2013)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}, {"title": "Premier of Alberta", "link": "https://wikipedia.org/wiki/Premier_of_Alberta"}]}, {"year": "1942", "text": "<PERSON>, American actress and comedian (d. 2013)", "html": "1942 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress and comedian (d. 2013)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress and comedian (d. 2013)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1943", "text": "<PERSON>, Italian-Belgian singer-songwriter", "html": "1943 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian-Belgian singer-songwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian-Belgian singer-songwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1943", "text": "<PERSON>, French economist and civil servant", "html": "1943 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French economist and civil servant", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French economist and civil servant", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1944", "text": "<PERSON><PERSON>, American singer-songwriter and author (d. 2024)", "html": "1944 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American singer-songwriter and author (d. 2024)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American singer-songwriter and author (d. 2024)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1944", "text": "<PERSON><PERSON>, Lebanese businessman and politician 60th Prime Minister of Lebanon (d. 2005)", "html": "1944 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Lebanese businessman and politician 60th <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Lebanon\" title=\"Prime Minister of Lebanon\">Prime Minister of Lebanon</a> (d. 2005)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Lebanese businessman and politician 60th <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Lebanon\" title=\"Prime Minister of Lebanon\">Prime Minister of Lebanon</a> (d. 2005)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}, {"title": "Prime Minister of Lebanon", "link": "https://wikipedia.org/wiki/Prime_Minister_of_Lebanon"}]}, {"year": "1944", "text": "<PERSON>, American wrestler, manager, and sportscaster (d. 2017)", "html": "1944 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American wrestler, manager, and sportscaster (d. 2017)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American wrestler, manager, and sportscaster (d. 2017)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1944", "text": "<PERSON>, French-Polynesian soldier and politician, President of French Polynesia", "html": "1944 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French-Polynesian soldier and politician, <a href=\"https://wikipedia.org/wiki/President_of_French_Polynesia\" title=\"President of French Polynesia\">President of French Polynesia</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French-Polynesian soldier and politician, <a href=\"https://wikipedia.org/wiki/President_of_French_Polynesia\" title=\"President of French Polynesia\">President of French Polynesia</a>", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "President of French Polynesia", "link": "https://wikipedia.org/wiki/President_of_French_Polynesia"}]}, {"year": "1945", "text": "<PERSON><PERSON><PERSON>, Indian author and activist, founded Maharashtra Andhashraddha Ni<PERSON> (d. 2013)", "html": "1945 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Indian author and activist, founded <a href=\"https://wikipedia.org/wiki/Maharashtra_Andhashradd<PERSON>_<PERSON>_<PERSON>\" title=\"Maharashtra Andhashraddha Nirmoolan <PERSON>\">Maharashtra Andhashraddha Nirmoolan <PERSON></a> (d. 2013)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Indian author and activist, founded <a href=\"https://wikipedia.org/wiki/Maharashtra_Andhashra<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"Maharashtra Andhashraddha Ni<PERSON>\">Maharashtra Andhashraddha Ni<PERSON>ool<PERSON></a> (d. 2013)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}, {"title": "Maharashtra Andhashra<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Maharashtra_Andhas<PERSON><PERSON><PERSON>_<PERSON><PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1945", "text": "<PERSON>, Australian singer-songwriter", "html": "1945 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>(singer)\" title=\"<PERSON> (singer)\"><PERSON></a>, Australian singer-songwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>(singer)\" title=\"<PERSON> (singer)\"><PERSON></a>, Australian singer-songwriter", "links": [{"title": "<PERSON> (singer)", "link": "https://wikipedia.org/wiki/<PERSON>(singer)"}]}, {"year": "1946", "text": "<PERSON><PERSON>, British rock musician (d. 1990)", "html": "1946 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, British rock musician (d. 1990)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, British rock musician (d. 1990)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/R<PERSON>_<PERSON>ch"}]}, {"year": "1946", "text": "<PERSON><PERSON>, Japanese graphic designer, created Hello Kitty", "html": "1946 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Japanese graphic designer, created <a href=\"https://wikipedia.org/wiki/Hello_Kitty\" title=\"Hello Kitty\">Hello Kitty</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Japanese graphic designer, created <a href=\"https://wikipedia.org/wiki/Hello_Kitty\" title=\"Hello Kitty\">Hello Kitty</a>", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}, {"title": "Hello Kitty", "link": "https://wikipedia.org/wiki/Hello_Kitty"}]}, {"year": "1947", "text": "<PERSON>, Guatemalan-American football player", "html": "1947 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Guatemalan-American football player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Guatemalan-American football player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1947", "text": "<PERSON>, English journalist", "html": "1947 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English journalist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English journalist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1947", "text": "<PERSON>, American songwriter and producer (d. 2021)", "html": "1947 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American songwriter and producer (d. 2021)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American songwriter and producer (d. 2021)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1948", "text": "<PERSON><PERSON>, Zanzibar accountant and politician, 6th President of Zanzibar", "html": "1948 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Zanzibar accountant and politician, 6th <a href=\"https://wikipedia.org/wiki/President_of_Zanzibar\" title=\"President of Zanzibar\">President of Zanzibar</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Zanzibar accountant and politician, 6th <a href=\"https://wikipedia.org/wiki/President_of_Zanzibar\" title=\"President of Zanzibar\">President of Zanzibar</a>", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>"}, {"title": "President of Zanzibar", "link": "https://wikipedia.org/wiki/President_of_Zanzibar"}]}, {"year": "1948", "text": "<PERSON>, English radio host and politician", "html": "1948 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(broadcaster)\" title=\"<PERSON> (broadcaster)\"><PERSON></a>, English radio host and politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(broadcaster)\" title=\"<PERSON> (broadcaster)\"><PERSON></a>, English radio host and politician", "links": [{"title": "<PERSON> (broadcaster)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(broadcaster)"}]}, {"year": "1948", "text": "<PERSON>, Canadian ice hockey player and coach", "html": "1948 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player and coach", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1948", "text": "<PERSON>, English sculptor and academic", "html": "1948 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English sculptor and academic", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English sculptor and academic", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1949", "text": "<PERSON><PERSON>, American actress", "html": "1949 - <a href=\"https://wikipedia.org/wiki/Jeannie_Berlin\" title=\"Jeannie Berlin\"><PERSON><PERSON></a>, American actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Jeannie_Berlin\" title=\"Jeannie Berlin\"><PERSON><PERSON></a>, American actress", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Jeannie_<PERSON>"}]}, {"year": "1949", "text": "<PERSON>, Canadian singer-songwriter, keyboard player, and producer", "html": "1949 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian singer-songwriter, keyboard player, and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian singer-songwriter, keyboard player, and producer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1949", "text": "<PERSON>, American physicist and engineer", "html": "1949 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American physicist and engineer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American physicist and engineer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1949", "text": "<PERSON><PERSON>, American actress and acting coach", "html": "1949 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American actress and acting coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American actress and acting coach", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1950", "text": "<PERSON>, American computer programmer and businessman, founded Lotus Software and Electronic Frontier Foundation", "html": "1950 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American computer programmer and businessman, founded <a href=\"https://wikipedia.org/wiki/Lotus_Software\" title=\"Lotus Software\">Lotus Software</a> and <a href=\"https://wikipedia.org/wiki/Electronic_Frontier_Foundation\" title=\"Electronic Frontier Foundation\">Electronic Frontier Foundation</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American computer programmer and businessman, founded <a href=\"https://wikipedia.org/wiki/Lotus_Software\" title=\"Lotus Software\">Lotus Software</a> and <a href=\"https://wikipedia.org/wiki/Electronic_Frontier_Foundation\" title=\"Electronic Frontier Foundation\">Electronic Frontier Foundation</a>", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Lotus Software", "link": "https://wikipedia.org/wiki/Lotus_Software"}, {"title": "Electronic Frontier Foundation", "link": "https://wikipedia.org/wiki/Electronic_Frontier_Foundation"}]}, {"year": "1950", "text": "<PERSON>, American physicist and academic, Nobel Prize laureate", "html": "1950 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American physicist and academic, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Physics\" title=\"Nobel Prize in Physics\">Nobel Prize</a> laureate", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American physicist and academic, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Physics\" title=\"Nobel Prize in Physics\">Nobel Prize</a> laureate", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>"}, {"title": "Nobel Prize in Physics", "link": "https://wikipedia.org/wiki/Nobel_Prize_in_Physics"}]}, {"year": "1950", "text": "<PERSON>, American singer-songwriter and musician (d. 2011)", "html": "1950 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and musician (d. 2011)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and musician (d. 2011)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1951", "text": "<PERSON>, American singer-songwriter, saxophonist, and producer (d. 2020)", "html": "1951 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(musician)\" title=\"<PERSON> (musician)\"><PERSON></a>, American singer-songwriter, saxophonist, and producer (d. 2020)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>(musician)\" title=\"<PERSON> (musician)\"><PERSON></a>, American singer-songwriter, saxophonist, and producer (d. 2020)", "links": [{"title": "<PERSON> (musician)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>(musician)"}]}, {"year": "1951", "text": "<PERSON><PERSON><PERSON>, French actor and screenwriter", "html": "1951 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, French actor and screenwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, French actor and screenwriter", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON><PERSON>i"}]}, {"year": "1951", "text": "<PERSON>, Australian cricketer and chemist", "html": "1951 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian cricketer and chemist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian cricketer and chemist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1953", "text": "<PERSON>, American engineer and astronaut", "html": "1953 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American engineer and astronaut", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American engineer and astronaut", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1953", "text": "<PERSON>, English ecologist and academic", "html": "1953 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English ecologist and academic", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English ecologist and academic", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1955", "text": "<PERSON>, American actress and singer", "html": "1955 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress and singer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress and singer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1957", "text": "<PERSON>, American singer-songwriter, guitarist, and producer", "html": "1957 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter, guitarist, and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter, guitarist, and producer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1957", "text": "<PERSON>, New Zealand rugby player", "html": "1957 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, New Zealand rugby player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, New Zealand rugby player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1958", "text": "<PERSON>, English journalist", "html": "1958 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(journalist)\" title=\"<PERSON> (journalist)\"><PERSON></a>, English journalist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(journalist)\" title=\"<PERSON> (journalist)\"><PERSON></a>, English journalist", "links": [{"title": "<PERSON> (journalist)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(journalist)"}]}, {"year": "1958", "text": "<PERSON>, English singer-songwriter", "html": "1958 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(musician)\" title=\"<PERSON> (musician)\"><PERSON></a>, English singer-songwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>(musician)\" title=\"<PERSON> (musician)\"><PERSON></a>, English singer-songwriter", "links": [{"title": "<PERSON> (musician)", "link": "https://wikipedia.org/wiki/<PERSON>(musician)"}]}, {"year": "1958", "text": "<PERSON>, American actress", "html": "1958 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1959", "text": "<PERSON><PERSON>, English author and educator", "html": "1959 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, English author and educator", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, English author and educator", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1960", "text": "<PERSON>, American businessman and engineer, current CEO of Apple Inc.", "html": "1960 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American businessman and engineer, current CEO of <a href=\"https://wikipedia.org/wiki/Apple_Inc.\" title=\"Apple Inc.\">Apple Inc.</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American businessman and engineer, current CEO of <a href=\"https://wikipedia.org/wiki/Apple_Inc.\" title=\"Apple Inc.\">Apple Inc.</a>", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Apple Inc.", "link": "https://wikipedia.org/wiki/Apple_Inc."}]}, {"year": "1960", "text": "<PERSON>, Mexican baseball player, coach, and sportscaster (d. 2024)", "html": "1960 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Mexican baseball player, coach, and sportscaster (d. 2024)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Mexican baseball player, coach, and sportscaster (d. 2024)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>nzuel<PERSON>"}]}, {"year": "1961", "text": "<PERSON>, Swedish author and screenwriter", "html": "1961 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_af_Genn%C3%A4s\" title=\"<PERSON>\"><PERSON></a>, Swedish author and screenwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_af_Genn%C3%A4s\" title=\"<PERSON>\"><PERSON></a>, Swedish author and screenwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_af_Genn%C3%A4s"}]}, {"year": "1961", "text": "<PERSON>, American basketball player and coach (d. 2018)", "html": "1961 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player and coach (d. 2018)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player and coach (d. 2018)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1961", "text": "<PERSON>, American singer-songwriter, guitarist, and producer", "html": "1961 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(musician)\" title=\"<PERSON> (musician)\"><PERSON></a>, American singer-songwriter, guitarist, and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(musician)\" title=\"<PERSON> (musician)\"><PERSON></a>, American singer-songwriter, guitarist, and producer", "links": [{"title": "<PERSON> (musician)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(musician)"}]}, {"year": "1961", "text": "<PERSON><PERSON>, Singaporean politician", "html": "1961 - <a href=\"https://wikipedia.org/wiki/Heng_<PERSON>wee_Keat\" title=\"Heng Swee Keat\">He<PERSON> <PERSON><PERSON><PERSON></a>, Singaporean politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Heng_<PERSON>wee_Keat\" title=\"Heng Swee Keat\"><PERSON><PERSON> <PERSON><PERSON><PERSON></a>, Singaporean politician", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Heng_<PERSON><PERSON><PERSON>_Keat"}]}, {"year": "1962", "text": "<PERSON><PERSON><PERSON>, English swimmer", "html": "1962 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, English swimmer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, English swimmer", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1962", "text": "<PERSON><PERSON>, Norwegian singer-songwriter and guitarist", "html": "1962 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"Ma<PERSON>\"><PERSON><PERSON></a>, Norwegian singer-songwriter and guitarist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Norwegian singer-songwriter and guitarist", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>en"}]}, {"year": "1962", "text": "<PERSON>, American singer-songwriter", "html": "1962 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1963", "text": "<PERSON>, English musician", "html": "1963 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(drummer)\" title=\"<PERSON> (drummer)\"><PERSON></a>, English musician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>(drummer)\" title=\"<PERSON> (drummer)\"><PERSON></a>, English musician", "links": [{"title": "<PERSON> (drummer)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>(drummer)"}]}, {"year": "1963", "text": "<PERSON><PERSON>, Indian businesswoman", "html": "1963 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Indian businesswoman", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Indian businesswoman", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Nita_Ambani"}]}, {"year": "1963", "text": "<PERSON>, Welsh footballer and manager", "html": "1963 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Welsh footballer and manager", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Welsh footballer and manager", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1963", "text": "<PERSON>, American singer-songwriter and guitarist", "html": "1963 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Kenny\"><PERSON></a>, American singer-songwriter and guitarist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_Kenny\" title=\"<PERSON> Kenny\"><PERSON></a>, American singer-songwriter and guitarist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1964", "text": "<PERSON>, American singer-songwriter and guitarist", "html": "1964 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and guitarist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and guitarist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "1965", "text": "<PERSON>, Australian politician", "html": "1965 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian politician", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1965", "text": "<PERSON><PERSON>, Swedish conductor", "html": "1965 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Swedish conductor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Swedish conductor", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1966", "text": "<PERSON>, American rapper and entrepreneur", "html": "1966 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> D\"><PERSON></a>, American rapper and entrepreneur", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> D\"><PERSON></a>, American rapper and entrepreneur", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1966", "text": "<PERSON>, Australian singer and musician (d. 2002)", "html": "1966 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian singer and musician (d. 2002)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian singer and musician (d. 2002)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1966", "text": "<PERSON>, American businessman and politician", "html": "1966 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(West_Virginia_politician)\" title=\"<PERSON> (West Virginia politician)\"><PERSON></a>, American businessman and politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(West_Virginia_politician)\" title=\"<PERSON> (West Virginia politician)\"><PERSON></a>, American businessman and politician", "links": [{"title": "<PERSON> (West Virginia politician)", "link": "https://wikipedia.org/wiki/<PERSON>_(West_Virginia_politician)"}]}, {"year": "1966", "text": "<PERSON>, English businessman and politician, Secretary of State for Health", "html": "1966 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English businessman and politician, <a href=\"https://wikipedia.org/wiki/Secretary_of_State_for_Health\" class=\"mw-redirect\" title=\"Secretary of State for Health\">Secretary of State for Health</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English businessman and politician, <a href=\"https://wikipedia.org/wiki/Secretary_of_State_for_Health\" class=\"mw-redirect\" title=\"Secretary of State for Health\">Secretary of State for Health</a>", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Secretary of State for Health", "link": "https://wikipedia.org/wiki/Secretary_of_State_for_Health"}]}, {"year": "1966", "text": "<PERSON><PERSON>, Indian-Bengali politician", "html": "1966 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Indian-Bengali politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Indian-Bengali politician", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1967", "text": "<PERSON>, Australian singer-songwriter, producer, and actress", "html": "1967 - <a href=\"https://wikipedia.org/wiki/<PERSON>_Arena\" title=\"Tina Arena\"><PERSON></a>, Australian singer-songwriter, producer, and actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Tina_Arena\" title=\"Tina Arena\"><PERSON></a>, Australian singer-songwriter, producer, and actress", "links": [{"title": "Tina Arena", "link": "https://wikipedia.org/wiki/Tina_Arena"}]}, {"year": "1967", "text": "<PERSON>, Dutch photographer", "html": "1967 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch photographer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch photographer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1969", "text": "<PERSON>, American basketball player", "html": "1969 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(basketball)\" title=\"<PERSON> (basketball)\"><PERSON></a>, American basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(basketball)\" title=\"<PERSON> (basketball)\"><PERSON></a>, American basketball player", "links": [{"title": "<PERSON> (basketball)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(basketball)"}]}, {"year": "1969", "text": "<PERSON><PERSON>, Canadian ice hockey player and sportscaster", "html": "1969 - <a href=\"https://wikipedia.org/wiki/Tie_<PERSON><PERSON>\" title=\"Tie Domi\"><PERSON><PERSON></a>, Canadian ice hockey player and sportscaster", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Tie_<PERSON><PERSON>\" title=\"Tie Domi\"><PERSON><PERSON></a>, Canadian ice hockey player and sportscaster", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Tie_<PERSON>i"}]}, {"year": "1970", "text": "<PERSON><PERSON><PERSON>, Barbadian cricketer", "html": "1970 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Barbadian cricketer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Barbadian cricketer", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1970", "text": "<PERSON><PERSON>, Romanian film director", "html": "1970 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Romanian film director", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Romanian film director", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_Enache"}]}, {"year": "1972", "text": "<PERSON>, Australian actress", "html": "1972 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian actress", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>lette"}]}, {"year": "1972", "text": "<PERSON>, Scottish footballer and manager", "html": "1972 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish footballer and manager", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish footballer and manager", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1972", "text": "<PERSON>, American actress and model", "html": "1972 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress and model", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress and model", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1972", "text": "<PERSON>, Canadian ice hockey player", "html": "1972 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(ice_hockey)\" title=\"<PERSON> (ice hockey)\"><PERSON></a>, Canadian ice hockey player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(ice_hockey)\" title=\"<PERSON> (ice hockey)\"><PERSON></a>, Canadian ice hockey player", "links": [{"title": "<PERSON> (ice hockey)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(ice_hockey)"}]}, {"year": "1973", "text": "<PERSON>, American actor", "html": "1973 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(actor)\" title=\"<PERSON> (actor)\"><PERSON></a>, American actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(actor)\" title=\"<PERSON> (actor)\"><PERSON></a>, American actor", "links": [{"title": "<PERSON> (actor)", "link": "https://wikipedia.org/wiki/<PERSON>_(actor)"}]}, {"year": "1973", "text": "<PERSON>, English footballer and manager", "html": "1973 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English footballer and manager", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English footballer and manager", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1973", "text": "<PERSON><PERSON><PERSON><PERSON>, Indian model and actress", "html": "1973 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Indian model and actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Indian model and actress", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Aishwarya_Rai"}]}, {"year": "1974", "text": "<PERSON><PERSON> <PERSON><PERSON> <PERSON><PERSON>, Indian cricketer", "html": "1974 - <a href=\"https://wikipedia.org/wiki/V._V._S._Laxman\" class=\"mw-redirect\" title=\"V. V. S. Laxman\">V<PERSON> <PERSON><PERSON> <PERSON><PERSON></a>, Indian cricketer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/V._V._S._Laxman\" class=\"mw-redirect\" title=\"V. V. S. Laxman\">V. V<PERSON> <PERSON><PERSON></a>, Indian cricketer", "links": [{"title": "V. <PERSON><PERSON> <PERSON><PERSON>", "link": "https://wikipedia.org/wiki/V._V._S<PERSON>_<PERSON>xman"}]}, {"year": "1975", "text": "<PERSON>, American singer and musician", "html": "1975 - <a href=\"https://wikipedia.org/wiki/Bo_Bice\" title=\"Bo Bice\"><PERSON></a>, American singer and musician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Bo_Bice\" title=\"Bo Bice\"><PERSON></a>, American singer and musician", "links": [{"title": "Bo Bice", "link": "https://wikipedia.org/wiki/Bo_Bice"}]}, {"year": "1975", "text": "<PERSON><PERSON>, South African footballer (d. 2013)", "html": "1975 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, South African footballer (d. 2013)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, South African footballer (d. 2013)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1975", "text": "<PERSON>, Canadian figure skater and coach", "html": "1975 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian figure skater and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian figure skater and coach", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1976", "text": "<PERSON>, Russian-Belarusian wrestler (d. 2012)", "html": "1976 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, Russian-Belarusian wrestler (d. 2012)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, Russian-Belarusian wrestler (d. 2012)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1976", "text": "<PERSON>, American college football coach", "html": "1976 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American college football coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American college football coach", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1977", "text": "<PERSON>, American football player", "html": "1977 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(American_football)\" title=\"<PERSON> (American football)\"><PERSON></a>, American football player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(American_football)\" title=\"<PERSON> (American football)\"><PERSON></a>, American football player", "links": [{"title": "<PERSON> (American football)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(American_football)"}]}, {"year": "1978", "text": "<PERSON>, English physicist and oceanographer", "html": "1978 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English physicist and oceanographer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English physicist and oceanographer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1978", "text": "<PERSON>, Dutch footballer and manager", "html": "1978 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch footballer and manager", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch footballer and manager", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1978", "text": "<PERSON>, American author", "html": "1978 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1979", "text": "<PERSON><PERSON>, American baseball player", "html": "1979 - <a href=\"https://wikipedia.org/wiki/Coco_Crisp\" title=\"Coco Crisp\"><PERSON><PERSON></a>, American baseball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Coco_Crisp\" title=\"Coco Crisp\"><PERSON><PERSON></a>, American baseball player", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Coco_<PERSON>risp"}]}, {"year": "1979", "text": "<PERSON>, Serbian footballer", "html": "1979 - <a href=\"https://wikipedia.org/wiki/Milan_Dudi%C4%87\" title=\"<PERSON>\"><PERSON></a>, Serbian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Milan_Dudi%C4%87\" title=\"<PERSON>\"><PERSON></a>, Serbian footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Milan_Dudi%C4%87"}]}, {"year": "1979", "text": "<PERSON>, American photographer and director", "html": "1979 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American photographer and director", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American photographer and director", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1980", "text": "<PERSON><PERSON><PERSON>, Turkish footballer", "html": "1980 - <a href=\"https://wikipedia.org/wiki/B<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Turkish footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/B<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Turkish footballer", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Bil<PERSON>_<PERSON>"}]}, {"year": "1981", "text": "<PERSON>, American actor and comedian", "html": "1981 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(actor)\" title=\"<PERSON> (actor)\"><PERSON></a>, American actor and comedian", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(actor)\" title=\"<PERSON> (actor)\"><PERSON></a>, American actor and comedian", "links": [{"title": "<PERSON> (actor)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(actor)"}]}, {"year": "1982", "text": "<PERSON>, English footballer", "html": "1982 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1982", "text": "<PERSON>, English-Italian rugby player", "html": "1982 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English-Italian rugby player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English-Italian rugby player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1983", "text": "<PERSON>, Canadian ice hockey player", "html": "1983 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1983", "text": "<PERSON><PERSON>, Japanese model and singer", "html": "1983 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Japanese model and singer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Japanese model and singer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1983", "text": "<PERSON>, English rugby player", "html": "1983 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English rugby player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English rugby player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1984", "text": "<PERSON><PERSON>, Serbian footballer", "html": "1984 - <a href=\"https://wikipedia.org/wiki/Milo%C5%A1_Krasi%C4%87\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Serbian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Milo%C5%A1_Krasi%C4%87\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Serbian footballer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Milo%C5%A1_Krasi%C4%87"}]}, {"year": "1984", "text": "<PERSON>, English actress and musician", "html": "1984 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actress and musician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actress and musician", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1984", "text": "<PERSON>, American baseball player", "html": "1984 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1985", "text": "<PERSON>, American basketball player", "html": "1985 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1985", "text": "<PERSON>, Brazilian baseball player", "html": "1985 - <a href=\"https://wikipedia.org/wiki/Paulo_Orlando\" title=\"Paulo Orlando\"><PERSON></a>, Brazilian baseball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Paulo_Orlando\" title=\"Paulo Orlando\"><PERSON></a>, Brazilian baseball player", "links": [{"title": "Paulo <PERSON>", "link": "https://wikipedia.org/wiki/Paulo_Orlando"}]}, {"year": "1986", "text": "<PERSON>, American actor and television personality", "html": "1986 - <a href=\"https://wikipedia.org/wiki/Penn_Badgley\" title=\"<PERSON> Badgley\"><PERSON></a>, American actor and television personality", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Penn_Badgley\" title=\"<PERSON> Badgley\"><PERSON></a>, American actor and television personality", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>gley"}]}, {"year": "1986", "text": "<PERSON><PERSON><PERSON>, Estonian high jumper, sprinter, and heptathlete", "html": "1986 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"Ksenija Balta\"><PERSON><PERSON><PERSON></a>, Estonian high jumper, sprinter, and heptathlete", "no_year_html": "<a href=\"https://wikipedia.org/wiki/K<PERSON><PERSON>_<PERSON>\" title=\"Ksenija Balta\"><PERSON><PERSON><PERSON></a>, Estonian high jumper, sprinter, and heptathlete", "links": [{"title": "Ksenija <PERSON>", "link": "https://wikipedia.org/wiki/Ksenija_Balta"}]}, {"year": "1987", "text": "<PERSON><PERSON><PERSON>, Indian film actress", "html": "1987 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_D%27Cruz\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Indian film actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>%27Cruz\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Indian film actress", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Ileana_D%27Cruz"}]}, {"year": "1987", "text": "<PERSON>, American football player", "html": "1987 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1988", "text": "<PERSON><PERSON><PERSON>, Japanese baseball player", "html": "1988 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese baseball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese baseball player", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1990", "text": "<PERSON>, American basketball player", "html": "1990 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1991", "text": "<PERSON>, English footballer", "html": "1991 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(footballer,_born_1991)\" title=\"<PERSON> (footballer, born 1991)\"><PERSON></a>, English footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(footballer,_born_1991)\" title=\"<PERSON> (footballer, born 1991)\"><PERSON></a>, English footballer", "links": [{"title": "<PERSON> (footballer, born 1991)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(footballer,_born_1991)"}]}, {"year": "1991", "text": "<PERSON>, American actor and singer", "html": "1991 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor and singer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor and singer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1991", "text": "<PERSON>, Chinese gymnast", "html": "1991 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Chinese gymnast", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Jiang_<PERSON>\" title=\"Jiang <PERSON>\"><PERSON></a>, Chinese gymnast", "links": [{"title": "Jiang Yuyuan", "link": "https://wikipedia.org/wiki/Jiang_Yuyuan"}]}, {"year": "1992", "text": "<PERSON><PERSON><PERSON>, American basketball player", "html": "1992 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American basketball player", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON><PERSON>"}]}, {"year": "1994", "text": "<PERSON>, American baseball player", "html": "1994 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>ook<PERSON>\"><PERSON></a>, American baseball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>ook<PERSON>\"><PERSON></a>, American baseball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>ooker"}]}, {"year": "1994", "text": "<PERSON><PERSON><PERSON><PERSON>, English footballer", "html": "1994 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English footballer", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>e"}]}, {"year": "1995", "text": "<PERSON>, American basketball player", "html": "1995 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1995", "text": "<PERSON><PERSON><PERSON>, Russian gymnast", "html": "1995 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>mu<PERSON>\"><PERSON><PERSON><PERSON></a>, Russian gymnast", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON> Mamu<PERSON>\"><PERSON><PERSON><PERSON></a>, Russian gymnast", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>mun"}]}, {"year": "1996", "text": "<PERSON><PERSON><PERSON><PERSON><PERSON>, English internet personality", "html": "1996 - <a href=\"https://wikipedia.org/wiki/GeorgeNotFound\" title=\"GeorgeNotFound\"><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON></a>, English internet personality", "no_year_html": "<a href=\"https://wikipedia.org/wiki/GeorgeNotFound\" title=\"GeorgeNotFound\"><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON></a>, English internet personality", "links": [{"title": "GeorgeNotFound", "link": "https://wikipedia.org/wiki/GeorgeNotFound"}]}, {"year": "1996", "text": "<PERSON><PERSON><PERSON><PERSON>, South Korean singer", "html": "1996 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>ye<PERSON>\" title=\"<PERSON><PERSON>ye<PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, South Korean singer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>ye<PERSON>\" title=\"<PERSON><PERSON>ye<PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, South Korean singer", "links": [{"title": "<PERSON><PERSON>yeon", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>on"}]}, {"year": "1996", "text": "<PERSON><PERSON>, American basketball player", "html": "1996 - <a href=\"https://wikipedia.org/wiki/Chinanu_Onuaku\" title=\"Chinanu Onuaku\"><PERSON><PERSON></a>, American basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Chinanu_Onuaku\" title=\"Chinanu Onuaku\"><PERSON><PERSON></a>, American basketball player", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Chinanu_Onuaku"}]}, {"year": "1996", "text": "<PERSON>, American rapper (d. 2017)", "html": "1996 - <a href=\"https://wikipedia.org/wiki/<PERSON>_Peep\" title=\"Lil Peep\"><PERSON></a>, American rapper (d. 2017)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_Peep\" title=\"Lil Peep\"><PERSON></a>, American rapper (d. 2017)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>"}]}, {"year": "1997", "text": "<PERSON>, American actor", "html": "1997 - <a href=\"https://wikipedia.org/wiki/<PERSON>_B<PERSON>er\" title=\"<PERSON> Burkholder\"><PERSON></a>, American actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Max Burkholder\"><PERSON></a>, American actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1997", "text": "<PERSON>, American actor and musician", "html": "1997 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor and musician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor and musician", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2001", "text": "<PERSON><PERSON><PERSON>-<PERSON>, Australian rugby league player", "html": "2001 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>-<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Australian rugby league player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>-<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Australian rugby league player", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Alofiana_Khan-Pereira"}]}, {"year": "2002", "text": "<PERSON><PERSON>, American rapper and YouTuber", "html": "2002 - <a href=\"https://wikipedia.org/wiki/NLE_Choppa\" title=\"NLE Choppa\"><PERSON><PERSON></a>, American rapper and YouTuber", "no_year_html": "<a href=\"https://wikipedia.org/wiki/NLE_Choppa\" title=\"NLE Choppa\"><PERSON><PERSON></a>, American rapper and YouTuber", "links": [{"title": "NLE Choppa", "link": "https://wikipedia.org/wiki/NLE_Choppa"}]}], "Deaths": [{"year": "934", "text": "<PERSON><PERSON><PERSON> of Winchester, English bishop", "html": "934 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_of_Winchester\" title=\"<PERSON><PERSON><PERSON> of Winchester\"><PERSON><PERSON><PERSON> of Winchester</a>, English bishop", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_of_Winchester\" title=\"<PERSON><PERSON><PERSON> of Winchester\"><PERSON><PERSON><PERSON> of Winchester</a>, English bishop", "links": [{"title": "<PERSON><PERSON><PERSON> of Winchester", "link": "https://wikipedia.org/wiki/Beornstan_of_Winchester"}]}, {"year": "970", "text": "<PERSON><PERSON> of Merseburg, German bishop", "html": "970 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_of_Merseburg\" title=\"<PERSON><PERSON> of Merseburg\"><PERSON><PERSON> of Merseburg</a>, German bishop", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_of_Merseburg\" title=\"<PERSON><PERSON> of Merseburg\"><PERSON><PERSON> of Merseburg</a>, German bishop", "links": [{"title": "<PERSON><PERSON> of Merseburg", "link": "https://wikipedia.org/wiki/Boso_of_Merseburg"}]}, {"year": "1038", "text": "<PERSON>, Margrave of Meissen (b. c. 980)", "html": "1038 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_<PERSON><PERSON>_of_Meissen\" title=\"<PERSON>, Margrave of Meissen\"><PERSON>, Mar<PERSON> of Meissen</a> (b. c. 980)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_<PERSON><PERSON>_of_Meissen\" title=\"<PERSON>, Mar<PERSON> of Meissen\"><PERSON>, Mar<PERSON> of Meissen</a> (b. c. 980)", "links": [{"title": "<PERSON>, Margrave of Meissen", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_<PERSON><PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1296", "text": "<PERSON>, French bishop and theologian (b. 1230)", "html": "1296 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French bishop and theologian (b. 1230)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French bishop and theologian (b. 1230)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1319", "text": "<PERSON><PERSON><PERSON><PERSON> Faggiuola, Italian condottieri (b. c. 1250)", "html": "1319 - <a href=\"https://wikipedia.org/wiki/Uguccione_della_Faggiuola\" title=\"Uguccione della Faggiuola\">Uguccione della Faggiuola</a>, Italian <a href=\"https://wikipedia.org/wiki/Condottieri\" class=\"mw-redirect\" title=\"Condottieri\">condottieri</a> (b. c. 1250)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Uguccione_della_Faggiuola\" title=\"Uguccione della Faggiuola\">Uguccione della Faggiuola</a>, Italian <a href=\"https://wikipedia.org/wiki/Condottieri\" class=\"mw-redirect\" title=\"Condo<PERSON>eri\">condottieri</a> (b. c. 1250)", "links": [{"title": "Uguccione della Faggiuola", "link": "https://wikipedia.org/wiki/Uguccione_<PERSON>_<PERSON>"}, {"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>eri"}]}, {"year": "1324", "text": "<PERSON>, Bishop of Carlisle", "html": "1324 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Bishop of Carlisle", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Bishop of Carlisle", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1391", "text": "<PERSON><PERSON><PERSON>, Count of Savoy (b. 1360)", "html": "1391 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_VII,_Count_of_Savoy\" title=\"<PERSON><PERSON><PERSON>, Count of Savoy\"><PERSON><PERSON><PERSON>, Count of Savoy</a> (b. 1360)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_VII,_Count_of_Savoy\" title=\"<PERSON><PERSON><PERSON>, Count of Savoy\"><PERSON><PERSON><PERSON>, Count of Savoy</a> (b. 1360)", "links": [{"title": "<PERSON><PERSON><PERSON>, Count of Savoy", "link": "https://wikipedia.org/wiki/Amade<PERSON>_VII,_Count_of_Savoy"}]}, {"year": "1399", "text": "<PERSON>, Duke of Brittany (b. 1339)", "html": "1399 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Duke_of_Brittany\" title=\"<PERSON>, Duke of Brittany\"><PERSON>, Duke of Brittany</a> (b. 1339)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Duke_of_Brittany\" title=\"<PERSON>, Duke of Brittany\"><PERSON>, Duke of Brittany</a> (b. 1339)", "links": [{"title": "<PERSON>, Duke of Brittany", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Duke_of_Brittany"}]}, {"year": "1406", "text": "<PERSON>, Duchess of Brabant (b. 1322)", "html": "1406 - <a href=\"https://wikipedia.org/wiki/Joanna,_Duchess_of_Brabant\" title=\"<PERSON>, Duchess of Brabant\"><PERSON>, Duchess of Brabant</a> (b. 1322)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Joanna,_Duchess_of_Brabant\" title=\"<PERSON>, Duchess of Brabant\"><PERSON>, Duchess of Brabant</a> (b. 1322)", "links": [{"title": "<PERSON>, Duchess of Brabant", "link": "https://wikipedia.org/wiki/<PERSON>,_Duchess_<PERSON>_<PERSON>"}]}, {"year": "1423", "text": "<PERSON>, Byzantine diplomat (probable date)", "html": "1423 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Byzantine diplomat (probable date)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Byzantine diplomat (probable date)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1461", "text": "<PERSON> of Trebizond (b. 1408)", "html": "1461 - <a href=\"https://wikipedia.org/wiki/<PERSON>_of_Trebizond\" title=\"<PERSON> of Trebizond\"><PERSON> of Trebizond</a> (b. 1408)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_of_Trebizond\" title=\"<PERSON> of Trebizond\"><PERSON> of Trebizond</a> (b. 1408)", "links": [{"title": "David of Trebizond", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_Trebizond"}]}, {"year": "1496", "text": "<PERSON><PERSON><PERSON> (<PERSON><PERSON>), Italian humanist writer (b. 1437)", "html": "1496 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON> (<PERSON><PERSON>)</a>, Italian humanist writer (b. 1437)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON> (<PERSON><PERSON>)</a>, Italian humanist writer (b. 1437)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1546", "text": "<PERSON><PERSON><PERSON>, Italian painter and architect (b. 1499)", "html": "1546 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_(painter)\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON> (painter)\"><PERSON><PERSON><PERSON></a>, Italian painter and architect (b. 1499)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_(painter)\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON> (painter)\"><PERSON><PERSON><PERSON></a>, Italian painter and architect (b. 1499)", "links": [{"title": "<PERSON><PERSON><PERSON> (painter)", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_(painter)"}]}, {"year": "1588", "text": "<PERSON>, French poet and scholar (b. 1508)", "html": "1588 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French poet and scholar (b. 1508)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French poet and scholar (b. 1508)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1596", "text": "<PERSON>, French lawyer and scholar (b. 1539)", "html": "1596 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French lawyer and scholar (b. 1539)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French lawyer and scholar (b. 1539)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1629", "text": "<PERSON><PERSON><PERSON>, Dutch painter (b. 1588)", "html": "1629 - <a href=\"https://wikipedia.org/wiki/He<PERSON><PERSON>_ter_B<PERSON>en\" title=\"Hend<PERSON> ter Brugghen\"><PERSON><PERSON><PERSON> <PERSON><PERSON></a>, Dutch painter (b. 1588)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/He<PERSON><PERSON>_ter_B<PERSON>\" title=\"Hend<PERSON> ter Brugghen\">He<PERSON><PERSON> <PERSON><PERSON></a>, Dutch painter (b. 1588)", "links": [{"title": "<PERSON><PERSON><PERSON> ter Brugghen", "link": "https://wikipedia.org/wiki/He<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "1642", "text": "<PERSON>, French-Canadian explorer (b. 1598)", "html": "1642 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French-Canadian explorer (b. 1598)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French-Canadian explorer (b. 1598)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1676", "text": "<PERSON><PERSON><PERSON><PERSON>, Dutch minister and theologian (b. 1589)", "html": "1676 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Dutch minister and theologian (b. 1589)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Dutch minister and theologian (b. 1589)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1678", "text": "<PERSON>, American judge and politician, 1st Governor of Rhode Island (b. 1601)", "html": "1678 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American judge and politician, 1st <a href=\"https://wikipedia.org/wiki/Governor_of_Rhode_Island\" title=\"Governor of Rhode Island\">Governor of Rhode Island</a> (b. 1601)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American judge and politician, 1st <a href=\"https://wikipedia.org/wiki/Governor_of_Rhode_Island\" title=\"Governor of Rhode Island\">Governor of Rhode Island</a> (b. 1601)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}, {"title": "Governor of Rhode Island", "link": "https://wikipedia.org/wiki/Governor_of_Rhode_Island"}]}, {"year": "1700", "text": "<PERSON> of Spain (b. 1661)", "html": "1700 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Spain\" title=\"<PERSON> II of Spain\"><PERSON> of Spain</a> (b. 1661)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Spain\" title=\"<PERSON> II of Spain\"><PERSON> of Spain</a> (b. 1661)", "links": [{"title": "<PERSON> of Spain", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Spain"}]}, {"year": "1814", "text": "<PERSON>, Russian general and politician, Russian Minister of Justice (b. 1744)", "html": "1814 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian general and politician, <a href=\"https://wikipedia.org/wiki/List_of_Ministers_of_Justice_of_Imperial_Russia\" class=\"mw-redirect\" title=\"List of Ministers of Justice of Imperial Russia\">Russian Minister of Justice</a> (b. 1744)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian general and politician, <a href=\"https://wikipedia.org/wiki/List_of_Ministers_of_Justice_of_Imperial_Russia\" class=\"mw-redirect\" title=\"List of Ministers of Justice of Imperial Russia\">Russian Minister of Justice</a> (b. 1744)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "List of Ministers of Justice of Imperial Russia", "link": "https://wikipedia.org/wiki/List_of_Ministers_of_Justice_of_Imperial_Russia"}]}, {"year": "1888", "text": "<PERSON><PERSON>, Russian geographer and explorer (b. 1838)", "html": "1888 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Russian geographer and explorer (b. 1838)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Russian geographer and explorer (b. 1838)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1894", "text": "<PERSON> of Russia (b. 1845)", "html": "1894 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Russia\" title=\"<PERSON> III of Russia\"><PERSON> of Russia</a> (b. 1845)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Russia\" title=\"<PERSON> III of Russia\"><PERSON> of Russia</a> (b. 1845)", "links": [{"title": "<PERSON> of Russia", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Russia"}]}, {"year": "1903", "text": "<PERSON>, German archaeologist, journalist, and politician, Nobel Prize laureate (b. 1817)", "html": "1903 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German archaeologist, journalist, and politician, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Literature\" title=\"Nobel Prize in Literature\">Nobel Prize</a> laureate (b. 1817)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German archaeologist, journalist, and politician, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Literature\" title=\"Nobel Prize in Literature\">Nobel Prize</a> laureate (b. 1817)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Nobel Prize in Literature", "link": "https://wikipedia.org/wiki/Nobel_Prize_in_Literature"}]}, {"year": "1907", "text": "<PERSON>, French author and playwright (b. 1873)", "html": "1907 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French author and playwright (b. 1873)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French author and playwright (b. 1873)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1920", "text": "<PERSON>, executed Irish Republican (b. 1902)", "html": "1920 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, executed Irish Republican (b. 1902)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, executed Irish Republican (b. 1902)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1925", "text": "<PERSON>, French actor, director, screenwriter, producer and comedian (b. 1883)", "html": "1925 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French actor, director, screenwriter, producer and comedian (b. 1883)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Linder\"><PERSON></a>, French actor, director, screenwriter, producer and comedian (b. 1883)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1938", "text": "<PERSON>, American businessman (b. 1874)", "html": "1938 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American businessman (b. 1874)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American businessman (b. 1874)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1942", "text": "<PERSON>, German organist, composer, and conductor (b. 1908)", "html": "1942 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German organist, composer, and conductor (b. 1908)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German organist, composer, and conductor (b. 1908)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1952", "text": "<PERSON>, American singer (b. 1911)", "html": "1952 - <a href=\"https://wikipedia.org/wiki/<PERSON>_Lee\" title=\"<PERSON> Lee\"><PERSON></a>, American singer (b. 1911)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_Lee\" title=\"Dixie Lee\"><PERSON></a>, American singer (b. 1911)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1955", "text": "<PERSON>, American author and educator (b. 1888)", "html": "1955 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author and educator (b. 1888)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author and educator (b. 1888)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1958", "text": "<PERSON><PERSON>, Turkish poet, author, and diplomat (b. 1884)", "html": "1958 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>atl%C4%B1\" title=\"<PERSON><PERSON>lı\"><PERSON><PERSON></a>, Turkish poet, author, and diplomat (b. 1884)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>atl%C4%B1\" title=\"<PERSON><PERSON>lı\"><PERSON><PERSON></a>, Turkish poet, author, and diplomat (b. 1884)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_Beyatl%C4%B1"}]}, {"year": "1962", "text": "<PERSON>, Mexican race car driver (b. 1942)", "html": "1962 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%<PERSON><PERSON><PERSON>_(racing_driver)\" title=\"<PERSON> (racing driver)\"><PERSON></a>, Mexican race car driver (b. 1942)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%<PERSON><PERSON><PERSON>_(racing_driver)\" title=\"<PERSON> (racing driver)\"><PERSON></a>, Mexican race car driver (b. 1942)", "links": [{"title": "<PERSON> (racing driver)", "link": "https://wikipedia.org/wiki/Ricardo_Rodr%C3%<PERSON><PERSON><PERSON>_(racing_driver)"}]}, {"year": "1961", "text": "<PERSON><PERSON>, Venezuelan communist (b. 1941)", "html": "1961 - <a href=\"https://wikipedia.org/wiki/Livia_Gouverneur\" title=\"<PERSON><PERSON> Gouverneur\"><PERSON><PERSON></a>, Venezuelan communist (b. 1941)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Livia_Gouverneur\" title=\"<PERSON><PERSON> Gouverneur\"><PERSON><PERSON></a>, Venezuelan communist (b. 1941)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Livia_Gouverneur"}]}, {"year": "1968", "text": "<PERSON><PERSON>, Greek economist and politician, 134th Prime Minister of Greece (b. 1888)", "html": "1968 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Greek economist and politician, 134th <a href=\"https://wikipedia.org/wiki/List_of_Prime_Ministers_of_Greece\" class=\"mw-redirect\" title=\"List of Prime Ministers of Greece\">Prime Minister of Greece</a> (b. 1888)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Greek economist and politician, 134th <a href=\"https://wikipedia.org/wiki/List_of_Prime_Ministers_of_Greece\" class=\"mw-redirect\" title=\"List of Prime Ministers of Greece\">Prime Minister of Greece</a> (b. 1888)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Georg<PERSON>_<PERSON>nd<PERSON>ou"}, {"title": "List of Prime Ministers of Greece", "link": "https://wikipedia.org/wiki/List_of_Prime_Ministers_of_Greece"}]}, {"year": "1970", "text": "<PERSON>, American sociologist and academic (b. 1892)", "html": "1970 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American sociologist and academic (b. 1892)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American sociologist and academic (b. 1892)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>ynd"}]}, {"year": "1972", "text": "<PERSON><PERSON><PERSON><PERSON>, Swedish author (b. 1902)", "html": "1972 - <a href=\"https://wikipedia.org/wiki/Waldemar_Hammenh%C3%B6g\" title=\"Wald<PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Swedish author (b. 1902)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Waldemar_Hammenh%C3%B6g\" title=\"Wald<PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Swedish author (b. 1902)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Waldemar_Hammenh%C3%B6g"}]}, {"year": "1972", "text": "<PERSON>, Canadian-American ecologist and academic (b. 1930)", "html": "1972 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, Canadian-American ecologist and academic (b. 1930)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, Canadian-American ecologist and academic (b. 1930)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1972", "text": "<PERSON>, American poet and critic (b. 1885)", "html": "1972 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Ezra Pound\"><PERSON></a>, American poet and critic (b. 1885)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_Pound\" title=\"Ezra Pound\"><PERSON></a>, American poet and critic (b. 1885)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1982", "text": "<PERSON>, American actor and director (b. 1927)", "html": "1982 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor and director (b. 1927)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor and director (b. 1927)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1982", "text": "<PERSON>, American director, producer, and screenwriter (b. 1894)", "html": "1982 - <a href=\"https://wikipedia.org/wiki/King_<PERSON>\" title=\"King <PERSON>\">King <PERSON></a>, American director, producer, and screenwriter (b. 1894)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/King_<PERSON>\" title=\"King <PERSON>\">King <PERSON></a>, American director, producer, and screenwriter (b. 1894)", "links": [{"title": "King <PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>r"}]}, {"year": "1983", "text": "<PERSON>, Dutch-Swiss musicologist and author (b. 1887)", "html": "1983 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch-Swiss musicologist and author (b. 1887)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch-Swiss musicologist and author (b. 1887)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1984", "text": "<PERSON>, American director, producer, and screenwriter (b. 1909)", "html": "1984 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American director, producer, and screenwriter (b. 1909)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American director, producer, and screenwriter (b. 1909)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1985", "text": "<PERSON>, Estonian-English footballer (b. 1902)", "html": "1985 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Estonian-English footballer (b. 1902)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Estonian-English footballer (b. 1902)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1985", "text": "<PERSON>, American actor and comedian (b. 1911)", "html": "1985 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor and comedian (b. 1911)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Silvers\"><PERSON></a>, American actor and comedian (b. 1911)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Phil_<PERSON>"}]}, {"year": "1986", "text": "<PERSON>, Canadian composer and conductor (b. 1929)", "html": "1986 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian composer and conductor (b. 1929)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian composer and conductor (b. 1929)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1987", "text": "<PERSON>, Canadian journalist and politician, 23rd Premier of Quebec (b. 1922)", "html": "1987 - <a href=\"https://wikipedia.org/wiki/Ren%C3%A9_L%C3%A9vesque\" title=\"<PERSON>\"><PERSON></a>, Canadian journalist and politician, 23rd <a href=\"https://wikipedia.org/wiki/Premier_of_Quebec\" title=\"Premier of Quebec\">Premier of Quebec</a> (b. 1922)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Ren%C3%A9_L%C3%A9vesque\" title=\"<PERSON>\"><PERSON></a>, Canadian journalist and politician, 23rd <a href=\"https://wikipedia.org/wiki/Premier_of_Quebec\" title=\"Premier of Quebec\">Premier of Quebec</a> (b. 1922)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Ren%C3%A9_L%C3%A9vesque"}, {"title": "Premier of Quebec", "link": "https://wikipedia.org/wiki/Premier_of_Quebec"}]}, {"year": "1993", "text": "<PERSON><PERSON><PERSON>, Spanish-American biochemist and academic, Nobel Prize laureate (b. 1905)", "html": "1993 - <a href=\"https://wikipedia.org/wiki/Severo_Ochoa\" title=\"<PERSON><PERSON><PERSON>cho<PERSON>\"><PERSON><PERSON><PERSON></a>, Spanish-American biochemist and academic, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Physiology_or_Medicine\" title=\"Nobel Prize in Physiology or Medicine\">Nobel Prize</a> laureate (b. 1905)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Se<PERSON><PERSON>_<PERSON>cho<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Spanish-American biochemist and academic, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Physiology_or_Medicine\" title=\"Nobel Prize in Physiology or Medicine\">Nobel Prize</a> laureate (b. 1905)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Sever<PERSON>_Ochoa"}, {"title": "Nobel Prize in Physiology or Medicine", "link": "https://wikipedia.org/wiki/Nobel_Prize_in_Physiology_or_Medicine"}]}, {"year": "1993", "text": "<PERSON><PERSON> <PERSON><PERSON><PERSON>, English historian and scholar (b. 1911)", "html": "1993 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON><PERSON><PERSON>-<PERSON>\" title=\"<PERSON><PERSON> <PERSON><PERSON>\"><PERSON><PERSON> <PERSON><PERSON></a>, English historian and scholar (b. 1911)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON><PERSON><PERSON>-<PERSON>\" title=\"<PERSON><PERSON> <PERSON><PERSON>\"><PERSON><PERSON> <PERSON><PERSON></a>, English historian and scholar (b. 1911)", "links": [{"title": "<PERSON><PERSON> <PERSON><PERSON>-<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON><PERSON><PERSON>-<PERSON>"}]}, {"year": "1994", "text": "<PERSON>, Jr., American actor (b. 1913)", "html": "1994 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Jr.\" class=\"mw-redirect\" title=\"<PERSON>, Jr.\"><PERSON>, Jr.</a>, American actor (b. 1913)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Jr.\" class=\"mw-redirect\" title=\"<PERSON>, Jr.\"><PERSON>, Jr.</a>, American actor (b. 1913)", "links": [{"title": "<PERSON>, Jr.", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Jr."}]}, {"year": "1996", "text": "<PERSON><PERSON> <PERSON><PERSON>, Sri Lankan lawyer and politician, 2nd President of Sri Lanka (b. 1906)", "html": "1996 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> <PERSON><PERSON>\"><PERSON><PERSON> <PERSON><PERSON></a>, Sri Lankan lawyer and politician, 2nd <a href=\"https://wikipedia.org/wiki/President_of_Sri_Lanka\" title=\"President of Sri Lanka\">President of Sri Lanka</a> (b. 1906)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> <PERSON><PERSON>\"><PERSON><PERSON> <PERSON><PERSON></a>, Sri Lankan lawyer and politician, 2nd <a href=\"https://wikipedia.org/wiki/President_of_Sri_Lanka\" title=\"President of Sri Lanka\">President of Sri Lanka</a> (b. 1906)", "links": [{"title": "<PERSON><PERSON> <PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON><PERSON><PERSON>"}, {"title": "President of Sri Lanka", "link": "https://wikipedia.org/wiki/President_of_Sri_Lanka"}]}, {"year": "1999", "text": "<PERSON>, American physicist and spy (b. 1925)", "html": "1999 - <a href=\"https://wikipedia.org/wiki/Theodore_<PERSON>\" title=\"Theodore Hall\"><PERSON></a>, American physicist and spy (b. 1925)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Theodore_Hall\" title=\"Theodore Hall\"><PERSON></a>, American physicist and spy (b. 1925)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Theodore_<PERSON>"}]}, {"year": "1999", "text": "<PERSON>, American football player and race car driver (b. 1954)", "html": "1999 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player and race car driver (b. 1954)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player and race car driver (b. 1954)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2000", "text": "<PERSON>, English footballer and manager (b. 1944)", "html": "2000 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(footballer)\" title=\"<PERSON> (footballer)\"><PERSON></a>, English footballer and manager (b. 1944)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(footballer)\" title=\"<PERSON> (footballer)\"><PERSON></a>, English footballer and manager (b. 1944)", "links": [{"title": "<PERSON> (footballer)", "link": "https://wikipedia.org/wiki/<PERSON>(footballer)"}]}, {"year": "2004", "text": "<PERSON>, American rapper and producer, founded Thizz Entertainment (b. 1970)", "html": "2004 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Dr<PERSON>\"><PERSON></a>, American rapper and producer, founded <a href=\"https://wikipedia.org/wiki/Thizz_Entertainment\" title=\"Thizz Entertainment\">Thizz Entertainment</a> (b. 1970)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Dr<PERSON>\"><PERSON></a>, American rapper and producer, founded <a href=\"https://wikipedia.org/wiki/Thizz_Entertainment\" title=\"Thizz Entertainment\">Thizz Entertainment</a> (b. 1970)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>e"}, {"title": "Thizz Entertainment", "link": "https://wikipedia.org/wiki/Thizz_Entertainment"}]}, {"year": "2004", "text": "<PERSON>, American singer-songwriter and producer (b. 1943)", "html": "2004 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and producer (b. 1943)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and producer (b. 1943)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2005", "text": "<PERSON><PERSON>, American pianist, composer, and conductor (b. 1918)", "html": "2005 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American pianist, composer, and conductor (b. 1918)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> <PERSON>\"><PERSON><PERSON></a>, American pianist, composer, and conductor (b. 1918)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "2005", "text": "<PERSON>, American screenwriter and producer (b. 1948)", "html": "2005 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American screenwriter and producer (b. 1948)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American screenwriter and producer (b. 1948)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "2006", "text": "<PERSON><PERSON><PERSON>, American actress, director, and screenwriter (b. 1966)", "html": "2006 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American actress, director, and screenwriter (b. 1966)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American actress, director, and screenwriter (b. 1966)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "2006", "text": "<PERSON>, American novelist and essayist (b. 1925)", "html": "2006 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American novelist and essayist (b. 1925)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American novelist and essayist (b. 1925)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2007", "text": "<PERSON><PERSON>, Indian director and screenwriter (b. 1922)", "html": "2007 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"S. Ali Ra<PERSON>\"><PERSON><PERSON></a>, Indian director and screenwriter (b. 1922)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"S. Ali <PERSON>\"><PERSON><PERSON></a>, Indian director and screenwriter (b. 1922)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>"}]}, {"year": "2007", "text": "<PERSON>, American general (b. 1915)", "html": "2007 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American general (b. 1915)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American general (b. 1915)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2008", "text": "<PERSON>, Swiss oceanographer and engineer (b. 1922)", "html": "2008 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swiss oceanographer and engineer (b. 1922)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swiss oceanographer and engineer (b. 1922)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "2008", "text": "<PERSON><PERSON><PERSON>, American record producer (b. 1974)", "html": "2008 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American record producer (b. 1974)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American record producer (b. 1974)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "2008", "text": "<PERSON><PERSON>, Peruvian-American soprano and actress (b. 1922/1923)", "html": "2008 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_Su<PERSON>c\" title=\"<PERSON><PERSON> Suma<PERSON>\"><PERSON><PERSON></a>, Peruvian-American soprano and actress (b. 1922/1923)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_Su<PERSON>\" title=\"<PERSON><PERSON> Suma<PERSON>\"><PERSON><PERSON></a>, Peruvian-American soprano and actress (b. 1922/1923)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Yma_Sumac"}]}, {"year": "2009", "text": "<PERSON>, Lithuanian-American author (b. 1930)", "html": "2009 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Lithuanian-American author (b. 1930)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Lithuanian-American author (b. 1930)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2009", "text": "<PERSON><PERSON>, Estonian scientist and academic (b. 1915)", "html": "2009 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Estonian scientist and academic (b. 1915)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Estonian scientist and academic (b. 1915)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "2009", "text": "<PERSON>, American violinist and composer (b. 1922)", "html": "2009 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American violinist and composer (b. 1922)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American violinist and composer (b. 1922)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "2010", "text": "<PERSON>, American actress (b. 1999)", "html": "2010 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress (b. 1999)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress (b. 1999)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2010", "text": "<PERSON>, Duchess of Wellington (b. 1922)", "html": "2010 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Duchess_of_Wellington\" title=\"<PERSON>, Duchess of Wellington\"><PERSON>, Duchess of Wellington</a> (b. 1922)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Duchess_of_Wellington\" title=\"<PERSON>, Duchess of Wellington\"><PERSON>, Duchess of Wellington</a> (b. 1922)", "links": [{"title": "<PERSON>, Duchess of Wellington", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Duchess_of_Wellington"}]}, {"year": "2011", "text": "<PERSON><PERSON><PERSON>, Turkish engineer and politician, Turkish Minister of Industry and Commerce (b. 1927)", "html": "2011 - <a href=\"https://wikipedia.org/wiki/C<PERSON>t_<PERSON>l\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Turkish engineer and politician, <a href=\"https://wikipedia.org/wiki/Ministry_of_Science,_Industry_and_Technology_(Turkey)\" class=\"mw-redirect\" title=\"Ministry of Science, Industry and Technology (Turkey)\">Turkish Minister of Industry and Commerce</a> (b. 1927)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/C<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Turkish engineer and politician, <a href=\"https://wikipedia.org/wiki/Ministry_of_Science,_Industry_and_Technology_(Turkey)\" class=\"mw-redirect\" title=\"Ministry of Science, Industry and Technology (Turkey)\">Turkish Minister of Industry and Commerce</a> (b. 1927)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>t_<PERSON>l"}, {"title": "Ministry of Science, Industry and Technology (Turkey)", "link": "https://wikipedia.org/wiki/Ministry_of_Science,_Industry_and_Technology_(Turkey)"}]}, {"year": "2012", "text": "<PERSON><PERSON><PERSON><PERSON>, Spanish poet, playwright, and philosopher (b. 1926)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/Agust%C3%ADn_Garc%C3%ADa_Calvo\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Spanish poet, playwright, and philosopher (b. 1926)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Agust%C3%ADn_Garc%C3%ADa_Calvo\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Spanish poet, playwright, and philosopher (b. 1926)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Agust%C3%ADn_Garc%C3%ADa_Calvo"}]}, {"year": "2012", "text": "<PERSON>, American singer (b. 1984)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer (b. 1984)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer (b. 1984)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2012", "text": "<PERSON><PERSON><PERSON><PERSON>, Dominican baseball player (b. 1957)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/Pascual_P%C3%A9<PERSON>_(baseball)\" title=\"<PERSON><PERSON><PERSON><PERSON> (baseball)\"><PERSON><PERSON><PERSON><PERSON></a>, Dominican baseball player (b. 1957)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Pas<PERSON>al_P%C3%A9<PERSON>_(baseball)\" title=\"<PERSON><PERSON><PERSON><PERSON> (baseball)\"><PERSON><PERSON><PERSON><PERSON></a>, Dominican baseball player (b. 1957)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON> (baseball)", "link": "https://wikipedia.org/wiki/Pascual_P%C3%A9rez_(baseball)"}]}, {"year": "2013", "text": "<PERSON>, American lieutenant and politician (b. 1921)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lieutenant and politician (b. 1921)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lieutenant and politician (b. 1921)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "2013", "text": "<PERSON><PERSON>, Dutch economist and academic (b. 1952)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/Piet_Rietveld\" title=\"Piet Rietveld\"><PERSON><PERSON></a>, Dutch economist and academic (b. 1952)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Piet_Rietveld\" title=\"Piet Rietveld\"><PERSON><PERSON></a>, Dutch economist and academic (b. 1952)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Piet_Rietveld"}]}, {"year": "2014", "text": "<PERSON>, Baron <PERSON>, English accountant and politician, Chief Secretary to the Treasury (b. 1923)", "html": "2014 - <a href=\"https://wikipedia.org/wiki/<PERSON>,_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>, Baron <PERSON>\"><PERSON>, Baron <PERSON></a>, English accountant and politician, <a href=\"https://wikipedia.org/wiki/Chief_Secretary_to_the_Treasury\" title=\"Chief Secretary to the Treasury\">Chief Secretary to the Treasury</a> (b. 1923)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>,_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>, Baron <PERSON>\"><PERSON>, Baron <PERSON></a>, English accountant and politician, <a href=\"https://wikipedia.org/wiki/Chief_Secretary_to_the_Treasury\" title=\"Chief Secretary to the Treasury\">Chief Secretary to the Treasury</a> (b. 1923)", "links": [{"title": "<PERSON>, <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_<PERSON>_<PERSON>"}, {"title": "Chief Secretary to the Treasury", "link": "https://wikipedia.org/wiki/Chief_Secretary_to_the_Treasury"}]}, {"year": "2014", "text": "<PERSON>, Australian runner and coach (b. 1967)", "html": "2014 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian runner and coach (b. 1967)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian runner and coach (b. 1967)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2014", "text": "<PERSON><PERSON><PERSON>, South African footballer (b. 1950)", "html": "2014 - <a href=\"https://wikipedia.org/wiki/Abedn<PERSON>_Ngcobo\" title=\"Abedn<PERSON> Ngcobo\"><PERSON><PERSON><PERSON></a>, South African footballer (b. 1950)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Abedn<PERSON>_Ngcobo\" title=\"Abedn<PERSON> Ngcobo\"><PERSON><PERSON><PERSON></a>, South African footballer (b. 1950)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Abednigo_Ngcobo"}]}, {"year": "2014", "text": "<PERSON><PERSON><PERSON>, Canadian-American baseball player, manager, and sportscaster (b. 1920)", "html": "2014 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Canadian-American baseball player, manager, and sportscaster (b. 1920)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Canadian-American baseball player, manager, and sportscaster (b. 1920)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>"}]}, {"year": "2014", "text": "<PERSON>, American singer-songwriter and guitarist (b. 1965)", "html": "2014 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Static\"><PERSON></a>, American singer-songwriter and guitarist (b. 1965)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Static\"><PERSON></a>, American singer-songwriter and guitarist (b. 1965)", "links": [{"title": "Wayne <PERSON>", "link": "https://wikipedia.org/wiki/Wayne_Static"}]}, {"year": "2015", "text": "<PERSON>, American lawyer and judge (b. 1941)", "html": "2015 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_(judge)\" title=\"<PERSON> (judge)\"><PERSON></a>, American lawyer and judge (b. 1941)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_(judge)\" title=\"<PERSON> (judge)\"><PERSON></a>, American lawyer and judge (b. 1941)", "links": [{"title": "<PERSON> (judge)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>_(judge)"}]}, {"year": "2015", "text": "<PERSON>, American sprinter (b. 1957)", "html": "2015 - <a href=\"https://wikipedia.org/wiki/Houston_McTear\" title=\"Houston McTear\"><PERSON></a>, American sprinter (b. 1957)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Houston_McTear\" title=\"Houston McTear\"><PERSON></a>, American sprinter (b. 1957)", "links": [{"title": "Houston McTear", "link": "https://wikipedia.org/wiki/Houston_McTear"}]}, {"year": "2015", "text": "<PERSON>, American entomologist and academic (b. 1918)", "html": "2015 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American entomologist and academic (b. 1918)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American entomologist and academic (b. 1918)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "2015", "text": "<PERSON><PERSON><PERSON>, German journalist and politician (b. 1929)", "html": "2015 - <a href=\"https://wikipedia.org/wiki/G%C3%<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, German journalist and politician (b. 1929)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/G%C3%<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, German journalist and politician (b. 1929)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/G%C3%<PERSON><PERSON>_<PERSON><PERSON>"}]}, {"year": "2015", "text": "<PERSON>, American actor, lawyer, and politician (b. 1942)", "html": "2015 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor, lawyer, and politician (b. 1942)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor, lawyer, and politician (b. 1942)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2020", "text": "<PERSON>, American historian expert on Romanian history (b. 1931)", "html": "2020 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American historian expert on Romanian history (b. 1931)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American historian expert on Romanian history (b. 1931)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2020", "text": "<PERSON> <PERSON>, British party planner, writer and socialite (b. 1941)", "html": "2020 - <a href=\"https://wikipedia.org/wiki/Lady_<PERSON>_<PERSON>\" title=\"Lady <PERSON>\">Lady <PERSON></a>, British party planner, writer and socialite (b. 1941)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Lady_<PERSON>_<PERSON>\" title=\"Lady <PERSON>\">Lady <PERSON></a>, British party planner, writer and socialite (b. 1941)", "links": [{"title": "Lady <PERSON>", "link": "https://wikipedia.org/wiki/Lady_<PERSON>_<PERSON>"}]}, {"year": "2021", "text": "<PERSON>, Canadian horse jockey (b. 1936)", "html": "2021 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian horse jockey (b. 1936)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian horse jockey (b. 1936)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2022", "text": "<PERSON><PERSON>, member of the American hip-hop group Migos (b. 1994)", "html": "2022 - <a href=\"https://wikipedia.org/wiki/Take<PERSON>_(rapper)\" title=\"Take<PERSON> (rapper)\">Take<PERSON></a>, member of the American hip-hop group Mi<PERSON> (b. 1994)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Take<PERSON>_(rapper)\" title=\"Take<PERSON> (rapper)\">Takeoff</a>, member of the American hip-hop group <PERSON><PERSON> (b. 1994)", "links": [{"title": "Take<PERSON> (rapper)", "link": "https://wikipedia.org/wiki/Take<PERSON>_(rapper)"}]}, {"year": "2023", "text": "<PERSON>, English cricketer (b. 1940)", "html": "2023 - <a href=\"https://wikipedia.org/wiki/2023\" title=\"2023\">2023</a> - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English cricketer (b. 1940)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/2023\" title=\"2023\">2023</a> - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English cricketer (b. 1940)", "links": [{"title": "2023", "link": "https://wikipedia.org/wiki/2023"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}]}}