{"date": "January 26", "url": "https://wikipedia.org/wiki/January_26", "data": {"Events": [{"year": "661", "text": "The Rashidun <PERSON> is effectively ended with the assassination of <PERSON>, the last caliph.", "html": "661 - The <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_Caliphate\" title=\"<PERSON><PERSON> Caliphate\"><PERSON><PERSON>ip<PERSON></a> is effectively ended with the <a href=\"https://wikipedia.org/wiki/Assassination_of_<PERSON>\" title=\"Assassination of <PERSON>\">assassination of <PERSON></a>, the last caliph.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_Calip<PERSON>\" title=\"<PERSON><PERSON> Calip<PERSON>e\"><PERSON><PERSON></a> is effectively ended with the <a href=\"https://wikipedia.org/wiki/Assassination_of_<PERSON>\" title=\"Assassination of <PERSON>\">assassination of <PERSON></a>, the last caliph.", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Rashidun_Caliphate"}, {"title": "Assassination of Ali", "link": "https://wikipedia.org/wiki/Assassination_of_<PERSON>"}]}, {"year": "1531", "text": "The 6.4-7.1 .mw-parser-output .tooltip-dotted{border-bottom:1px dotted;cursor:help}Mw Lisbon earthquake kills about thirty thousand people.", "html": "1531 - The 6.4-7.1 <a href=\"https://wikipedia.org/wiki/Seismic_magnitude_scales#Mw\" title=\"Seismic magnitude scales\"><style data-mw-deduplicate=\"TemplateStyles:r1038841319\">.mw-parser-output .tooltip-dotted{border-bottom:1px dotted;cursor:help}</style>\n<span class=\"rt-commentedText tooltip\" title=\"Moment mag. scale\">M<sub>w</sub></span></a> <a href=\"https://wikipedia.org/wiki/1531_Lisbon_earthquake\" title=\"1531 Lisbon earthquake\">Lisbon earthquake</a> kills about thirty thousand people.", "no_year_html": "The 6.4-7.1 <a href=\"https://wikipedia.org/wiki/Seismic_magnitude_scales#Mw\" title=\"Seismic magnitude scales\"><style data-mw-deduplicate=\"TemplateStyles:r1038841319\">.mw-parser-output .tooltip-dotted{border-bottom:1px dotted;cursor:help}</style>\n<span class=\"rt-commentedText tooltip\" title=\"Moment mag. scale\">M<sub>w</sub></span></a> <a href=\"https://wikipedia.org/wiki/1531_Lisbon_earthquake\" title=\"1531 Lisbon earthquake\">Lisbon earthquake</a> kills about thirty thousand people.", "links": [{"title": "Seismic magnitude scales", "link": "https://wikipedia.org/wiki/Seismic_magnitude_scales#Mw"}, {"title": "1531 Lisbon earthquake", "link": "https://wikipedia.org/wiki/1531_Lisbon_earthquake"}]}, {"year": "1564", "text": "The Council of Trent establishes an official distinction between Roman Catholicism and Protestantism.", "html": "1564 - The <a href=\"https://wikipedia.org/wiki/Council_of_Trent\" title=\"Council of Trent\">Council of Trent</a> establishes an official distinction between <a href=\"https://wikipedia.org/wiki/Roman_Catholicism\" class=\"mw-redirect\" title=\"Roman Catholicism\">Roman Catholicism</a> and <a href=\"https://wikipedia.org/wiki/Protestantism\" title=\"Protestantism\">Protestantism</a>.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Council_of_Trent\" title=\"Council of Trent\">Council of Trent</a> establishes an official distinction between <a href=\"https://wikipedia.org/wiki/Roman_Catholicism\" class=\"mw-redirect\" title=\"Roman Catholicism\">Roman Catholicism</a> and <a href=\"https://wikipedia.org/wiki/Protestantism\" title=\"Protestantism\">Protestantism</a>.", "links": [{"title": "Council of Trent", "link": "https://wikipedia.org/wiki/Council_of_Trent"}, {"title": "Roman Catholicism", "link": "https://wikipedia.org/wiki/Roman_Catholicism"}, {"title": "Protestantism", "link": "https://wikipedia.org/wiki/Protestantism"}]}, {"year": "1564", "text": "The Grand Duchy of Lithuania defeats the Tsardom of Russia in the Battle of Ula during the Livonian War.", "html": "1564 - The <a href=\"https://wikipedia.org/wiki/Grand_Duchy_of_Lithuania\" title=\"Grand Duchy of Lithuania\">Grand Duchy of Lithuania</a> defeats the <a href=\"https://wikipedia.org/wiki/Tsardom_of_Russia\" title=\"Tsardom of Russia\">Tsardom of Russia</a> in the <a href=\"https://wikipedia.org/wiki/Battle_of_Ula\" class=\"mw-redirect\" title=\"Battle of Ula\">Battle of Ula</a> during the <a href=\"https://wikipedia.org/wiki/Livonian_War\" title=\"Livonian War\">Livonian War</a>.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Grand_Duchy_of_Lithuania\" title=\"Grand Duchy of Lithuania\">Grand Duchy of Lithuania</a> defeats the <a href=\"https://wikipedia.org/wiki/Tsardom_of_Russia\" title=\"Tsardom of Russia\">Tsardom of Russia</a> in the <a href=\"https://wikipedia.org/wiki/Battle_of_Ula\" class=\"mw-redirect\" title=\"Battle of Ula\">Battle of Ula</a> during the <a href=\"https://wikipedia.org/wiki/Livonian_War\" title=\"Livonian War\">Livonian War</a>.", "links": [{"title": "Grand Duchy of Lithuania", "link": "https://wikipedia.org/wiki/Grand_Duchy_of_Lithuania"}, {"title": "Tsardom of Russia", "link": "https://wikipedia.org/wiki/Tsardom_of_Russia"}, {"title": "Battle of Ula", "link": "https://wikipedia.org/wiki/Battle_of_Ula"}, {"title": "Livonian War", "link": "https://wikipedia.org/wiki/Livonian_War"}]}, {"year": "1699", "text": "For the first time, the Ottoman Empire permanently cedes territory to the Christian powers.", "html": "1699 - For the first time, the <a href=\"https://wikipedia.org/wiki/Ottoman_Empire\" title=\"Ottoman Empire\">Ottoman Empire</a> permanently <a href=\"https://wikipedia.org/wiki/Treaty_of_Karlowitz\" title=\"Treaty of Karlowitz\">cedes territory</a> to the Christian powers.", "no_year_html": "For the first time, the <a href=\"https://wikipedia.org/wiki/Ottoman_Empire\" title=\"Ottoman Empire\">Ottoman Empire</a> permanently <a href=\"https://wikipedia.org/wiki/Treaty_of_Karlowitz\" title=\"Treaty of Karlowitz\">cedes territory</a> to the Christian powers.", "links": [{"title": "Ottoman Empire", "link": "https://wikipedia.org/wiki/Ottoman_Empire"}, {"title": "Treaty of Karlowitz", "link": "https://wikipedia.org/wiki/Treaty_of_Karlowitz"}]}, {"year": "1700", "text": "The 8.7-9.2 Mw Cascadia earthquake takes place off the west coast of North America, as evidenced by Japanese records.", "html": "1700 - The 8.7-9.2 <a href=\"https://wikipedia.org/wiki/Seismic_magnitude_scales#Mw\" title=\"Seismic magnitude scales\"><link rel=\"mw-deduplicated-inline-style\" href=\"mw-data:TemplateStyles:r1038841319\">\n<span class=\"rt-commentedText tooltip\" title=\"Moment mag. scale\">M<sub>w</sub></span></a> <a href=\"https://wikipedia.org/wiki/1700_Cascadia_earthquake\" title=\"1700 Cascadia earthquake\">Cascadia earthquake</a> takes place off the west coast of <a href=\"https://wikipedia.org/wiki/North_America\" title=\"North America\">North America</a>, as evidenced by Japanese records.", "no_year_html": "The 8.7-9.2 <a href=\"https://wikipedia.org/wiki/Seismic_magnitude_scales#Mw\" title=\"Seismic magnitude scales\"><link rel=\"mw-deduplicated-inline-style\" href=\"mw-data:TemplateStyles:r1038841319\">\n<span class=\"rt-commentedText tooltip\" title=\"Moment mag. scale\">M<sub>w</sub></span></a> <a href=\"https://wikipedia.org/wiki/1700_Cascadia_earthquake\" title=\"1700 Cascadia earthquake\">Cascadia earthquake</a> takes place off the west coast of <a href=\"https://wikipedia.org/wiki/North_America\" title=\"North America\">North America</a>, as evidenced by Japanese records.", "links": [{"title": "Seismic magnitude scales", "link": "https://wikipedia.org/wiki/Seismic_magnitude_scales#Mw"}, {"title": "1700 Cascadia earthquake", "link": "https://wikipedia.org/wiki/1700_Cascadia_earthquake"}, {"title": "North America", "link": "https://wikipedia.org/wiki/North_America"}]}, {"year": "1765", "text": "A British naval expedition arrives at and names Port Egmont in the Falkland Islands, founding a settlement there eight days later. (Arrival was 15 January 1765 O.S.)", "html": "1765 - A British naval expedition arrives at and names <a href=\"https://wikipedia.org/wiki/Port_Egmont\" title=\"Port Egmont\">Port Egmont</a> in the <a href=\"https://wikipedia.org/wiki/Falkland_Islands\" title=\"Falkland Islands\">Falkland Islands</a>, founding a settlement there eight days later. (Arrival was 15 January 1765 <a href=\"https://wikipedia.org/wiki/Old_Style_and_New_Style_dates\" title=\"Old Style and New Style dates\">O.S.</a>)", "no_year_html": "A British naval expedition arrives at and names <a href=\"https://wikipedia.org/wiki/Port_Egmont\" title=\"Port Egmont\">Port Egmont</a> in the <a href=\"https://wikipedia.org/wiki/Falkland_Islands\" title=\"Falkland Islands\">Falkland Islands</a>, founding a settlement there eight days later. (Arrival was 15 January 1765 <a href=\"https://wikipedia.org/wiki/Old_Style_and_New_Style_dates\" title=\"Old Style and New Style dates\">O.S.</a>)", "links": [{"title": "Port Egmont", "link": "https://wikipedia.org/wiki/Port_Egmont"}, {"title": "Falkland Islands", "link": "https://wikipedia.org/wiki/Falkland_Islands"}, {"title": "Old Style and New Style dates", "link": "https://wikipedia.org/wiki/Old_Style_and_New_Style_dates"}]}, {"year": "1788", "text": "The British First Fleet, led by <PERSON>, sails into Port Jackson (Sydney Harbour) to establish Sydney, the first permanent European settlement on Australia. Commemorated as Australia Day.", "html": "1788 - The British <a href=\"https://wikipedia.org/wiki/First_Fleet\" title=\"First Fleet\">First Fleet</a>, led by <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, sails into <a href=\"https://wikipedia.org/wiki/Port_Jackson\" title=\"Port Jackson\">Port Jackson (Sydney Harbour)</a> to establish <a href=\"https://wikipedia.org/wiki/Sydney\" title=\"Sydney\">Sydney</a>, the first permanent European settlement on Australia. Commemorated as <a href=\"https://wikipedia.org/wiki/Australia_Day\" title=\"Australia Day\">Australia Day</a>.", "no_year_html": "The British <a href=\"https://wikipedia.org/wiki/First_Fleet\" title=\"First Fleet\">First Fleet</a>, led by <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, sails into <a href=\"https://wikipedia.org/wiki/Port_Jackson\" title=\"Port Jackson\">Port Jackson (Sydney Harbour)</a> to establish <a href=\"https://wikipedia.org/wiki/Sydney\" title=\"Sydney\">Sydney</a>, the first permanent European settlement on Australia. Commemorated as <a href=\"https://wikipedia.org/wiki/Australia_Day\" title=\"Australia Day\">Australia Day</a>.", "links": [{"title": "First Fleet", "link": "https://wikipedia.org/wiki/First_Fleet"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Port Jackson", "link": "https://wikipedia.org/wiki/Port_Jackson"}, {"title": "Sydney", "link": "https://wikipedia.org/wiki/Sydney"}, {"title": "Australia Day", "link": "https://wikipedia.org/wiki/Australia_Day"}]}, {"year": "1808", "text": "The Rum Rebellion is the only successful (albeit short-lived) armed takeover of the government in New South Wales.", "html": "1808 - The <a href=\"https://wikipedia.org/wiki/Rum_Rebellion\" title=\"Rum Rebellion\">Rum Rebellion</a> is the only successful (albeit short-lived) armed takeover of the government in New South Wales.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Rum_Rebellion\" title=\"Rum Rebellion\">Rum Rebellion</a> is the only successful (albeit short-lived) armed takeover of the government in New South Wales.", "links": [{"title": "Rum Rebellion", "link": "https://wikipedia.org/wiki/Rum_Rebellion"}]}, {"year": "1837", "text": "Michigan is admitted as the 26th U.S. state.", "html": "1837 - <a href=\"https://wikipedia.org/wiki/Michigan\" title=\"Michigan\">Michigan</a> is admitted as the 26th <a href=\"https://wikipedia.org/wiki/U.S._state\" title=\"U.S. state\">U.S. state</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Michigan\" title=\"Michigan\">Michigan</a> is admitted as the 26th <a href=\"https://wikipedia.org/wiki/U.S._state\" title=\"U.S. state\">U.S. state</a>.", "links": [{"title": "Michigan", "link": "https://wikipedia.org/wiki/Michigan"}, {"title": "U.S. state", "link": "https://wikipedia.org/wiki/U.S._state"}]}, {"year": "1841", "text": "<PERSON> takes formal possession of Hong Kong Island at what is now Possession Point, establishing British Hong Kong.", "html": "1841 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> takes formal possession of <a href=\"https://wikipedia.org/wiki/Hong_Kong_Island\" title=\"Hong Kong Island\">Hong Kong Island</a> at what is now <a href=\"https://wikipedia.org/wiki/Possession_Point\" class=\"mw-redirect\" title=\"Possession Point\">Possession Point</a>, establishing <a href=\"https://wikipedia.org/wiki/British_Hong_Kong\" title=\"British Hong Kong\">British Hong Kong</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> takes formal possession of <a href=\"https://wikipedia.org/wiki/Hong_Kong_Island\" title=\"Hong Kong Island\">Hong Kong Island</a> at what is now <a href=\"https://wikipedia.org/wiki/Possession_Point\" class=\"mw-redirect\" title=\"Possession Point\">Possession Point</a>, establishing <a href=\"https://wikipedia.org/wiki/British_Hong_Kong\" title=\"British Hong Kong\">British Hong Kong</a>.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Hong Kong Island", "link": "https://wikipedia.org/wiki/Hong_Kong_Island"}, {"title": "Possession Point", "link": "https://wikipedia.org/wiki/Possession_Point"}, {"title": "British Hong Kong", "link": "https://wikipedia.org/wiki/British_Hong_Kong"}]}, {"year": "1855", "text": "Point No Point Treaty is signed in Washington Territory.", "html": "1855 - <a href=\"https://wikipedia.org/wiki/Point_No_Point_Treaty\" title=\"Point No Point Treaty\">Point No Point Treaty</a> is signed in <a href=\"https://wikipedia.org/wiki/Washington_Territory\" title=\"Washington Territory\">Washington Territory</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Point_No_Point_Treaty\" title=\"Point No Point Treaty\">Point No Point Treaty</a> is signed in <a href=\"https://wikipedia.org/wiki/Washington_Territory\" title=\"Washington Territory\">Washington Territory</a>.", "links": [{"title": "Point No Point Treaty", "link": "https://wikipedia.org/wiki/Point_No_Point_Treaty"}, {"title": "Washington Territory", "link": "https://wikipedia.org/wiki/Washington_Territory"}]}, {"year": "1856", "text": "First Battle of Seattle: Marines from the USS Decatur drive off American Indian attackers after all-day battle with settlers.", "html": "1856 - First <a href=\"https://wikipedia.org/wiki/Battle_of_Seattle_(1856)\" title=\"Battle of Seattle (1856)\">Battle of Seattle</a>: Marines from the <a href=\"https://wikipedia.org/wiki/USS_Decatur_(1839)\" title=\"USS Decatur (1839)\">USS <i>Decatur</i></a> drive off American Indian attackers after all-day battle with settlers.", "no_year_html": "First <a href=\"https://wikipedia.org/wiki/Battle_of_Seattle_(1856)\" title=\"Battle of Seattle (1856)\">Battle of Seattle</a>: Marines from the <a href=\"https://wikipedia.org/wiki/USS_Decatur_(1839)\" title=\"USS Decatur (1839)\">USS <i>Decatur</i></a> drive off American Indian attackers after all-day battle with settlers.", "links": [{"title": "Battle of Seattle (1856)", "link": "https://wikipedia.org/wiki/Battle_of_Seattle_(1856)"}, {"title": "USS Decatur (1839)", "link": "https://wikipedia.org/wiki/USS_Decatur_(1839)"}]}, {"year": "1861", "text": "American Civil War: The state of Louisiana secedes from the Union.", "html": "1861 - <a href=\"https://wikipedia.org/wiki/American_Civil_War\" title=\"American Civil War\">American Civil War</a>: The state of <a href=\"https://wikipedia.org/wiki/Louisiana\" title=\"Louisiana\">Louisiana</a> <a href=\"https://wikipedia.org/wiki/Louisiana_secession\" title=\"Louisiana secession\">secedes from the Union</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/American_Civil_War\" title=\"American Civil War\">American Civil War</a>: The state of <a href=\"https://wikipedia.org/wiki/Louisiana\" title=\"Louisiana\">Louisiana</a> <a href=\"https://wikipedia.org/wiki/Louisiana_secession\" title=\"Louisiana secession\">secedes from the Union</a>.", "links": [{"title": "American Civil War", "link": "https://wikipedia.org/wiki/American_Civil_War"}, {"title": "Louisiana", "link": "https://wikipedia.org/wiki/Louisiana"}, {"title": "Louisiana secession", "link": "https://wikipedia.org/wiki/Louisiana_secession"}]}, {"year": "1863", "text": "American Civil War: General <PERSON> is relieved of command of the Army of the Potomac after the disastrous Fredericksburg campaign. He is replaced by <PERSON>.", "html": "1863 - American Civil War: General <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> is relieved of command of the <a href=\"https://wikipedia.org/wiki/Army_of_the_Potomac\" title=\"Army of the Potomac\">Army of the Potomac</a> after the disastrous <a href=\"https://wikipedia.org/wiki/Battle_of_Fredericksburg\" title=\"Battle of Fredericksburg\">Fredericksburg campaign</a>. He is replaced by <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>.", "no_year_html": "American Civil War: General <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> is relieved of command of the <a href=\"https://wikipedia.org/wiki/Army_of_the_Potomac\" title=\"Army of the Potomac\">Army of the Potomac</a> after the disastrous <a href=\"https://wikipedia.org/wiki/Battle_of_Fredericksburg\" title=\"Battle of Fredericksburg\">Fredericksburg campaign</a>. He is replaced by <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Army of the Potomac", "link": "https://wikipedia.org/wiki/Army_of_the_Potomac"}, {"title": "Battle of Fredericksburg", "link": "https://wikipedia.org/wiki/Battle_of_Fredericksburg"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1863", "text": "American Civil War: Governor of Massachusetts <PERSON> receives permission from the Secretary of War to raise a militia organization for men of African descent.", "html": "1863 - American Civil War: <a href=\"https://wikipedia.org/wiki/Governor_of_Massachusetts\" title=\"Governor of Massachusetts\">Governor of Massachusetts</a> <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> receives permission from the <a href=\"https://wikipedia.org/wiki/United_States_Secretary_of_War\" title=\"United States Secretary of War\">Secretary of War</a> to raise a <a href=\"https://wikipedia.org/wiki/Militia\" title=\"Militia\">militia</a> organization for men of African descent.", "no_year_html": "American Civil War: <a href=\"https://wikipedia.org/wiki/Governor_of_Massachusetts\" title=\"Governor of Massachusetts\">Governor of Massachusetts</a> <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> receives permission from the <a href=\"https://wikipedia.org/wiki/United_States_Secretary_of_War\" title=\"United States Secretary of War\">Secretary of War</a> to raise a <a href=\"https://wikipedia.org/wiki/Militia\" title=\"Militia\">militia</a> organization for men of African descent.", "links": [{"title": "Governor of Massachusetts", "link": "https://wikipedia.org/wiki/Governor_of_Massachusetts"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_Andrew"}, {"title": "United States Secretary of War", "link": "https://wikipedia.org/wiki/United_States_Secretary_of_War"}, {"title": "Militia", "link": "https://wikipedia.org/wiki/Militia"}]}, {"year": "1870", "text": "Reconstruction Era: Virginia is readmitted to the Union.", "html": "1870 - <a href=\"https://wikipedia.org/wiki/Reconstruction_Era\" class=\"mw-redirect\" title=\"Reconstruction Era\">Reconstruction Era</a>: <a href=\"https://wikipedia.org/wiki/Virginia\" title=\"Virginia\">Virginia</a> is <a href=\"https://wikipedia.org/wiki/History_of_Virginia#Reconstruction\" title=\"History of Virginia\">readmitted to the Union</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Reconstruction_Era\" class=\"mw-redirect\" title=\"Reconstruction Era\">Reconstruction Era</a>: <a href=\"https://wikipedia.org/wiki/Virginia\" title=\"Virginia\">Virginia</a> is <a href=\"https://wikipedia.org/wiki/History_of_Virginia#Reconstruction\" title=\"History of Virginia\">readmitted to the Union</a>.", "links": [{"title": "Reconstruction Era", "link": "https://wikipedia.org/wiki/Reconstruction_Era"}, {"title": "Virginia", "link": "https://wikipedia.org/wiki/Virginia"}, {"title": "History of Virginia", "link": "https://wikipedia.org/wiki/History_of_Virginia#Reconstruction"}]}, {"year": "1885", "text": "Troops loyal to The Mahdi conquer Khartoum, killing the Governor-General <PERSON>.", "html": "1885 - Troops loyal to <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\">The Mahdi</a> conquer <a href=\"https://wikipedia.org/wiki/Siege_of_Khartoum\" title=\"Siege of Khartoum\">Khartoum</a>, killing the Governor-General <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>.", "no_year_html": "Troops loyal to <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\">The Mahdi</a> conquer <a href=\"https://wikipedia.org/wiki/Siege_of_Khartoum\" title=\"Siege of Khartoum\">Khartoum</a>, killing the Governor-General <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Siege of Khartoum", "link": "https://wikipedia.org/wiki/Siege_of_Khartoum"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1905", "text": "The world's largest diamond ever, the Cullinan, which weighs 3,106.75 carats (0.621350 kg), is found at the Premier Mine near Pretoria in South Africa.", "html": "1905 - The world's largest <a href=\"https://wikipedia.org/wiki/Diamond\" title=\"Diamond\">diamond</a> ever, the <a href=\"https://wikipedia.org/wiki/Cullinan_Diamond\" title=\"Cullinan Diamond\">Cullinan</a>, which weighs 3,106.75 carats (0.621350 kg), is found at the <a href=\"https://wikipedia.org/wiki/Premier_Mine\" title=\"Premier Mine\">Premier Mine</a> near <a href=\"https://wikipedia.org/wiki/Pretoria\" title=\"Pretoria\">Pretoria</a> in South Africa.", "no_year_html": "The world's largest <a href=\"https://wikipedia.org/wiki/Diamond\" title=\"Diamond\">diamond</a> ever, the <a href=\"https://wikipedia.org/wiki/Cullinan_Diamond\" title=\"Cullinan Diamond\">Cullinan</a>, which weighs 3,106.75 carats (0.621350 kg), is found at the <a href=\"https://wikipedia.org/wiki/Premier_Mine\" title=\"Premier Mine\">Premier Mine</a> near <a href=\"https://wikipedia.org/wiki/Pretoria\" title=\"Pretoria\">Pretoria</a> in South Africa.", "links": [{"title": "Diamond", "link": "https://wikipedia.org/wiki/Diamond"}, {"title": "Cullinan Diamond", "link": "https://wikipedia.org/wiki/Cullinan_Diamond"}, {"title": "Premier Mine", "link": "https://wikipedia.org/wiki/Premier_Mine"}, {"title": "Pretoria", "link": "https://wikipedia.org/wiki/Pretoria"}]}, {"year": "1915", "text": "The Rocky Mountain National Park is established by an act of the U.S. Congress.", "html": "1915 - The <a href=\"https://wikipedia.org/wiki/Rocky_Mountain_National_Park\" title=\"Rocky Mountain National Park\">Rocky Mountain National Park</a> is established by an act of the <a href=\"https://wikipedia.org/wiki/U.S._Congress\" class=\"mw-redirect\" title=\"U.S. Congress\">U.S. Congress</a>.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Rocky_Mountain_National_Park\" title=\"Rocky Mountain National Park\">Rocky Mountain National Park</a> is established by an act of the <a href=\"https://wikipedia.org/wiki/U.S._Congress\" class=\"mw-redirect\" title=\"U.S. Congress\">U.S. Congress</a>.", "links": [{"title": "Rocky Mountain National Park", "link": "https://wikipedia.org/wiki/Rocky_Mountain_National_Park"}, {"title": "U.S. Congress", "link": "https://wikipedia.org/wiki/U.S._Congress"}]}, {"year": "1918", "text": "Finnish Civil War: A group of Red Guards hangs a red lantern atop the tower of Helsinki Workers' Hall to symbolically mark the start of the war.", "html": "1918 - <a href=\"https://wikipedia.org/wiki/Finnish_Civil_War\" title=\"Finnish Civil War\">Finnish Civil War</a>: A group of <a href=\"https://wikipedia.org/wiki/Red_Guards_(Finland)\" title=\"Red Guards (Finland)\">Red Guards</a> hangs a red lantern atop the tower of <a href=\"https://wikipedia.org/wiki/Helsinki_Workers%27_Hall\" class=\"mw-redirect\" title=\"Helsinki Workers' Hall\">Helsinki Workers' Hall</a> to symbolically mark the start of the war.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Finnish_Civil_War\" title=\"Finnish Civil War\">Finnish Civil War</a>: A group of <a href=\"https://wikipedia.org/wiki/Red_Guards_(Finland)\" title=\"Red Guards (Finland)\">Red Guards</a> hangs a red lantern atop the tower of <a href=\"https://wikipedia.org/wiki/Helsinki_Workers%27_Hall\" class=\"mw-redirect\" title=\"Helsinki Workers' Hall\">Helsinki Workers' Hall</a> to symbolically mark the start of the war.", "links": [{"title": "Finnish Civil War", "link": "https://wikipedia.org/wiki/Finnish_Civil_War"}, {"title": "Red Guards (Finland)", "link": "https://wikipedia.org/wiki/Red_Guards_(Finland)"}, {"title": "Helsinki Workers' Hall", "link": "https://wikipedia.org/wiki/Helsinki_Workers%27_Hall"}]}, {"year": "1926", "text": "The first demonstration of the television by <PERSON>.", "html": "1926 - The first demonstration of the television by <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>.", "no_year_html": "The first demonstration of the television by <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1930", "text": "The Indian National Congress declares 26 January as Independence Day or as the day for Poorna Swaraj (\"Complete Independence\") which occurred 17 years later.", "html": "1930 - The <a href=\"https://wikipedia.org/wiki/Indian_National_Congress\" title=\"Indian National Congress\">Indian National Congress</a> declares 26 January as Independence Day or as the day for <a href=\"https://wikipedia.org/wiki/Purna_<PERSON>waraj\" title=\"<PERSON><PERSON><PERSON> Swaraj\"><PERSON><PERSON></a> (\"Complete Independence\") which occurred 17 years later.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Indian_National_Congress\" title=\"Indian National Congress\">Indian National Congress</a> declares 26 January as Independence Day or as the day for <a href=\"https://wikipedia.org/wiki/Purna_<PERSON>waraj\" title=\"<PERSON><PERSON><PERSON>j\"><PERSON><PERSON></a> (\"Complete Independence\") which occurred 17 years later.", "links": [{"title": "Indian National Congress", "link": "https://wikipedia.org/wiki/Indian_National_Congress"}, {"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>j"}]}, {"year": "1934", "text": "The Apollo Theater reopens in Harlem, New York City.", "html": "1934 - The <a href=\"https://wikipedia.org/wiki/Apollo_Theater\" title=\"Apollo Theater\">Apollo Theater</a> reopens in <a href=\"https://wikipedia.org/wiki/Harlem\" title=\"Harlem\">Harlem</a>, New York City.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Apollo_Theater\" title=\"Apollo Theater\">Apollo Theater</a> reopens in <a href=\"https://wikipedia.org/wiki/Harlem\" title=\"Harlem\">Harlem</a>, New York City.", "links": [{"title": "Apollo Theater", "link": "https://wikipedia.org/wiki/Apollo_Theater"}, {"title": "Harlem", "link": "https://wikipedia.org/wiki/Harlem"}]}, {"year": "1934", "text": "German-Polish declaration of non-aggression is signed.", "html": "1934 - <a href=\"https://wikipedia.org/wiki/German%E2%80%93Polish_declaration_of_non-aggression\" title=\"German-Polish declaration of non-aggression\">German-Polish declaration of non-aggression</a> is signed.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/German%E2%80%93Polish_declaration_of_non-aggression\" title=\"German-Polish declaration of non-aggression\">German-Polish declaration of non-aggression</a> is signed.", "links": [{"title": "German-Polish declaration of non-aggression", "link": "https://wikipedia.org/wiki/German%E2%80%93Polish_declaration_of_non-aggression"}]}, {"year": "1939", "text": "Spanish Civil War: Catalonia Offensive: Troops loyal to nationalist General <PERSON> and aided by Italy take Barcelona.", "html": "1939 - <a href=\"https://wikipedia.org/wiki/Spanish_Civil_War\" title=\"Spanish Civil War\">Spanish Civil War</a>: <a href=\"https://wikipedia.org/wiki/Catalonia_Offensive\" title=\"Catalonia Offensive\">Catalonia Offensive</a>: Troops loyal to nationalist General <a href=\"https://wikipedia.org/wiki/Francisco_<PERSON>\" title=\"Francisco <PERSON>\"><PERSON></a> and aided by <a href=\"https://wikipedia.org/wiki/Kingdom_of_Italy\" title=\"Kingdom of Italy\">Italy</a> take <a href=\"https://wikipedia.org/wiki/Barcelona\" title=\"Barcelona\">Barcelona</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Spanish_Civil_War\" title=\"Spanish Civil War\">Spanish Civil War</a>: <a href=\"https://wikipedia.org/wiki/Catalonia_Offensive\" title=\"Catalonia Offensive\">Catalonia Offensive</a>: Troops loyal to nationalist General <a href=\"https://wikipedia.org/wiki/Francisco_<PERSON>\" title=\"Francisco <PERSON>\"><PERSON></a> and aided by <a href=\"https://wikipedia.org/wiki/Kingdom_of_Italy\" title=\"Kingdom of Italy\">Italy</a> take <a href=\"https://wikipedia.org/wiki/Barcelona\" title=\"Barcelona\">Barcelona</a>.", "links": [{"title": "Spanish Civil War", "link": "https://wikipedia.org/wiki/Spanish_Civil_War"}, {"title": "Catalonia Offensive", "link": "https://wikipedia.org/wiki/Catalonia_Offensive"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Francisco_Franco"}, {"title": "Kingdom of Italy", "link": "https://wikipedia.org/wiki/Kingdom_of_Italy"}, {"title": "Barcelona", "link": "https://wikipedia.org/wiki/Barcelona"}]}, {"year": "1942", "text": "World War II: The first United States forces arrive in Europe, landing in Northern Ireland.", "html": "1942 - <a href=\"https://wikipedia.org/wiki/World_War_II\" title=\"World War II\">World War II</a>: The first United States forces arrive in Europe, landing in <a href=\"https://wikipedia.org/wiki/Northern_Ireland\" title=\"Northern Ireland\">Northern Ireland</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/World_War_II\" title=\"World War II\">World War II</a>: The first United States forces arrive in Europe, landing in <a href=\"https://wikipedia.org/wiki/Northern_Ireland\" title=\"Northern Ireland\">Northern Ireland</a>.", "links": [{"title": "World War II", "link": "https://wikipedia.org/wiki/World_War_II"}, {"title": "Northern Ireland", "link": "https://wikipedia.org/wiki/Northern_Ireland"}]}, {"year": "1945", "text": "World War II: <PERSON><PERSON> displays valor and bravery in action for which he will later be awarded the Medal of Honor.", "html": "1945 - World War II: <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a> displays valor and bravery in action for which he will later be awarded the <a href=\"https://wikipedia.org/wiki/Medal_of_Honor\" title=\"Medal of Honor\">Medal of Honor</a>.", "no_year_html": "World War II: <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a> displays valor and bravery in action for which he will later be awarded the <a href=\"https://wikipedia.org/wiki/Medal_of_Honor\" title=\"Medal of Honor\">Medal of Honor</a>.", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}, {"title": "Medal of Honor", "link": "https://wikipedia.org/wiki/Medal_of_Honor"}]}, {"year": "1949", "text": "The Hale Telescope at Palomar Observatory sees first light under the direction of <PERSON>, becoming the largest aperture optical telescope (until BTA-6 is built in 1976).", "html": "1949 - The <a href=\"https://wikipedia.org/wiki/Hale_Telescope\" title=\"Hale Telescope\">Hale Telescope</a> at <a href=\"https://wikipedia.org/wiki/Palomar_Observatory\" title=\"Palomar Observatory\">Palomar Observatory</a> sees <a href=\"https://wikipedia.org/wiki/First_light_(astronomy)\" title=\"First light (astronomy)\">first light</a> under the direction of <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, becoming the largest aperture optical telescope (until <a href=\"https://wikipedia.org/wiki/BTA-6\" title=\"BTA-6\">BTA-6</a> is built in 1976).", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Hale_Telescope\" title=\"Hale Telescope\">Hale Telescope</a> at <a href=\"https://wikipedia.org/wiki/Palomar_Observatory\" title=\"Palomar Observatory\">Palomar Observatory</a> sees <a href=\"https://wikipedia.org/wiki/First_light_(astronomy)\" title=\"First light (astronomy)\">first light</a> under the direction of <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, becoming the largest aperture optical telescope (until <a href=\"https://wikipedia.org/wiki/BTA-6\" title=\"BTA-6\">BTA-6</a> is built in 1976).", "links": [{"title": "Hale Telescope", "link": "https://wikipedia.org/wiki/Hale_Telescope"}, {"title": "Palomar Observatory", "link": "https://wikipedia.org/wiki/Palomar_Observatory"}, {"title": "First light (astronomy)", "link": "https://wikipedia.org/wiki/First_light_(astronomy)"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "BTA-6", "link": "https://wikipedia.org/wiki/BTA-6"}]}, {"year": "1950", "text": "The Constitution of India comes into force, forming a republic. <PERSON><PERSON> is sworn in as the first President of India. Observed as Republic Day in India.", "html": "1950 - The <a href=\"https://wikipedia.org/wiki/Constitution_of_India\" title=\"Constitution of India\">Constitution of India</a> <a href=\"https://wikipedia.org/wiki/Coming_into_force\" class=\"mw-redirect\" title=\"Coming into force\">comes into force</a>, forming a republic. <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a> is sworn in as the first <a href=\"https://wikipedia.org/wiki/President_of_India\" title=\"President of India\">President of India</a>. Observed as <a href=\"https://wikipedia.org/wiki/Republic_Day_(India)\" title=\"Republic Day (India)\">Republic Day</a> in India.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Constitution_of_India\" title=\"Constitution of India\">Constitution of India</a> <a href=\"https://wikipedia.org/wiki/Coming_into_force\" class=\"mw-redirect\" title=\"Coming into force\">comes into force</a>, forming a republic. <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a> is sworn in as the first <a href=\"https://wikipedia.org/wiki/President_of_India\" title=\"President of India\">President of India</a>. Observed as <a href=\"https://wikipedia.org/wiki/Republic_Day_(India)\" title=\"Republic Day (India)\">Republic Day</a> in India.", "links": [{"title": "Constitution of India", "link": "https://wikipedia.org/wiki/Constitution_of_India"}, {"title": "Coming into force", "link": "https://wikipedia.org/wiki/Coming_into_force"}, {"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}, {"title": "President of India", "link": "https://wikipedia.org/wiki/President_of_India"}, {"title": "Republic Day (India)", "link": "https://wikipedia.org/wiki/Republic_Day_(India)"}]}, {"year": "1952", "text": "Black Saturday in Egypt: rioters burn Cairo's central business district, targeting British and upper-class Egyptian businesses.", "html": "1952 - <a href=\"https://wikipedia.org/wiki/Cairo_fire\" title=\"Cairo fire\">Black Saturday</a> in Egypt: rioters burn <a href=\"https://wikipedia.org/wiki/Cairo\" title=\"Cairo\">Cairo</a>'s central business district, targeting British and upper-class Egyptian businesses.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Cairo_fire\" title=\"Cairo fire\">Black Saturday</a> in Egypt: rioters burn <a href=\"https://wikipedia.org/wiki/Cairo\" title=\"Cairo\">Cairo</a>'s central business district, targeting British and upper-class Egyptian businesses.", "links": [{"title": "Cairo fire", "link": "https://wikipedia.org/wiki/Cairo_fire"}, {"title": "Cairo", "link": "https://wikipedia.org/wiki/Cairo"}]}, {"year": "1956", "text": "Soviet Union cedes Porkkal<PERSON> back to Finland.", "html": "1956 - Soviet Union cedes <a href=\"https://wikipedia.org/wiki/Porkkala\" class=\"mw-redirect\" title=\"Porkkala\">Porkkala</a> back to Finland.", "no_year_html": "Soviet Union cedes <a href=\"https://wikipedia.org/wiki/Porkkala\" class=\"mw-redirect\" title=\"Porkkala\">Porkkala</a> back to Finland.", "links": [{"title": "Porkkala", "link": "https://wikipedia.org/wiki/Porkkala"}]}, {"year": "1959", "text": "The 41-acre (17 ha) Chain Island is listed for sale by the California State Lands Commission, with a minimum bid of $5,226.", "html": "1959 - The 41-acre (17 ha) <a href=\"https://wikipedia.org/wiki/Chain_Island\" title=\"Chain Island\">Chain Island</a> is listed for sale by the <a href=\"https://wikipedia.org/wiki/State_of_California\" class=\"mw-redirect\" title=\"State of California\">California</a> State Lands Commission, with a minimum bid of $5,226.", "no_year_html": "The 41-acre (17 ha) <a href=\"https://wikipedia.org/wiki/Chain_Island\" title=\"Chain Island\">Chain Island</a> is listed for sale by the <a href=\"https://wikipedia.org/wiki/State_of_California\" class=\"mw-redirect\" title=\"State of California\">California</a> State Lands Commission, with a minimum bid of $5,226.", "links": [{"title": "Chain Island", "link": "https://wikipedia.org/wiki/Chain_Island"}, {"title": "State of California", "link": "https://wikipedia.org/wiki/State_of_California"}]}, {"year": "1962", "text": "Ranger 3 is launched to study the Moon. The space probe later misses the Moon by 22,000 miles (35,400 km).", "html": "1962 - <a href=\"https://wikipedia.org/wiki/Ranger_3\" title=\"Ranger 3\">Ranger 3</a> is launched to study the <a href=\"https://wikipedia.org/wiki/Moon\" title=\"Moon\">Moon</a>. The <a href=\"https://wikipedia.org/wiki/Space_probe\" class=\"mw-redirect\" title=\"Space probe\">space probe</a> later misses the Moon by 22,000 miles (35,400 km).", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Ranger_3\" title=\"Ranger 3\">Ranger 3</a> is launched to study the <a href=\"https://wikipedia.org/wiki/Moon\" title=\"Moon\">Moon</a>. The <a href=\"https://wikipedia.org/wiki/Space_probe\" class=\"mw-redirect\" title=\"Space probe\">space probe</a> later misses the Moon by 22,000 miles (35,400 km).", "links": [{"title": "Ranger 3", "link": "https://wikipedia.org/wiki/Ranger_3"}, {"title": "Moon", "link": "https://wikipedia.org/wiki/Moon"}, {"title": "Space probe", "link": "https://wikipedia.org/wiki/Space_probe"}]}, {"year": "1966", "text": "The three Beaumont children disappear from a beach in Glenelg, South Australia, resulting in one of the country's largest-ever police investigations.", "html": "1966 - The three <a href=\"https://wikipedia.org/wiki/Disappearance_of_the_<PERSON>_children\" title=\"Disappearance of the <PERSON> children\">Beaumont children disappear</a> from a beach in <a href=\"https://wikipedia.org/wiki/Glenelg,_South_Australia\" title=\"Glenelg, South Australia\">Glenelg, South Australia</a>, resulting in one of the country's largest-ever police investigations.", "no_year_html": "The three <a href=\"https://wikipedia.org/wiki/Disappearance_of_the_<PERSON>_children\" title=\"Disappearance of the <PERSON> children\">Beaumont children disappear</a> from a beach in <a href=\"https://wikipedia.org/wiki/Glenelg,_South_Australia\" title=\"Glenelg, South Australia\">Glenelg, South Australia</a>, resulting in one of the country's largest-ever police investigations.", "links": [{"title": "Disappearance of the Beaumont children", "link": "https://wikipedia.org/wiki/Disappearance_of_the_<PERSON>_children"}, {"title": "Glenelg, South Australia", "link": "https://wikipedia.org/wiki/Glenelg,_South_Australia"}]}, {"year": "1972", "text": "JAT Flight 367 is destroyed by a terrorist bomb, killing 27 of the 28 people on board the DC-9. Flight attendant <PERSON><PERSON><PERSON> survives with critical injuries.", "html": "1972 - <a href=\"https://wikipedia.org/wiki/JAT_Flight_367\" title=\"JAT Flight 367\">JAT Flight 367</a> is destroyed by a terrorist bomb, killing 27 of the 28 people on board the <a href=\"https://wikipedia.org/wiki/McDonnell_Douglas_DC-9\" title=\"McDonnell Douglas DC-9\">DC-9</a>. Flight attendant <a href=\"https://wikipedia.org/wiki/Vesna_Vulovi%C4%87\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a> survives with critical injuries.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/JAT_Flight_367\" title=\"JAT Flight 367\">JAT Flight 367</a> is destroyed by a terrorist bomb, killing 27 of the 28 people on board the <a href=\"https://wikipedia.org/wiki/McDonnell_Douglas_DC-9\" title=\"McDonnell Douglas DC-9\">DC-9</a>. Flight attendant <a href=\"https://wikipedia.org/wiki/Vesna_Vulovi%C4%87\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a> survives with critical injuries.", "links": [{"title": "JAT Flight 367", "link": "https://wikipedia.org/wiki/JAT_Flight_367"}, {"title": "McDonnell Douglas DC-9", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_DC-9"}, {"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Vesna_Vulovi%C4%87"}]}, {"year": "1974", "text": "Turkish Airlines Flight 301 crashes during takeoff from Izmir Cumaovası Airport (now İzmir Adnan Menderes Airport), killing 66 of the 73 people on board the Fokker F28 Fellowship.", "html": "1974 - <a href=\"https://wikipedia.org/wiki/Turkish_Airlines_Flight_301\" title=\"Turkish Airlines Flight 301\">Turkish Airlines Flight 301</a> crashes during takeoff from Izmir Cumaovası Airport (now <a href=\"https://wikipedia.org/wiki/%C4%B0zmir_Adnan_Menderes_Airport\" title=\"İzmir Adnan Menderes Airport\">İzmir Adnan Menderes Airport</a>), killing 66 of the 73 people on board the <a href=\"https://wikipedia.org/wiki/Fokker_F28_Fellowship\" title=\"Fokker F28 Fellowship\">Fokker F28 Fellowship</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Turkish_Airlines_Flight_301\" title=\"Turkish Airlines Flight 301\">Turkish Airlines Flight 301</a> crashes during takeoff from Izmir Cumaovası Airport (now <a href=\"https://wikipedia.org/wiki/%C4%B0zmir_Adnan_Menderes_Airport\" title=\"İzmir Adnan Menderes Airport\">İzmir Adnan Menderes Airport</a>), killing 66 of the 73 people on board the <a href=\"https://wikipedia.org/wiki/Fokker_F28_Fellowship\" title=\"Fokker F28 Fellowship\">Fokker F28 Fellowship</a>.", "links": [{"title": "Turkish Airlines Flight 301", "link": "https://wikipedia.org/wiki/Turkish_Airlines_Flight_301"}, {"title": "İzmir Adnan Menderes Airport", "link": "https://wikipedia.org/wiki/%C4%B0z<PERSON>_Adnan_Menderes_Airport"}, {"title": "Fokker F28 Fellowship", "link": "https://wikipedia.org/wiki/Fokker_F28_Fellowship"}]}, {"year": "1986", "text": "The Ugandan government of <PERSON> is overthrown by the National Resistance Army, led by <PERSON><PERSON><PERSON>.", "html": "1986 - The Ugandan government of <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> is overthrown by the <a href=\"https://wikipedia.org/wiki/National_Resistance_Army\" title=\"National Resistance Army\">National Resistance Army</a>, led by <a href=\"https://wikipedia.org/wiki/Yoweri_Museveni\" title=\"Yoweri Museveni\">Yoweri Museveni</a>.", "no_year_html": "The Ugandan government of <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> is overthrown by the <a href=\"https://wikipedia.org/wiki/National_Resistance_Army\" title=\"National Resistance Army\">National Resistance Army</a>, led by <a href=\"https://wikipedia.org/wiki/Yoweri_Museveni\" title=\"Yoweri Museveni\">Yo<PERSON><PERSON>veni</a>.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "National Resistance Army", "link": "https://wikipedia.org/wiki/National_Resistance_Army"}, {"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Yoweri_Museveni"}]}, {"year": "1991", "text": "<PERSON> is removed from power in Somalia, ending centralized government, and is succeeded by <PERSON>.", "html": "1991 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON></a> is removed from power in <a href=\"https://wikipedia.org/wiki/Somalia\" title=\"Somalia\">Somalia</a>, ending centralized government, and is succeeded by <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON></a> is removed from power in <a href=\"https://wikipedia.org/wiki/Somalia\" title=\"Somalia\">Somalia</a>, ending centralized government, and is succeeded by <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>.", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>ad_Barre"}, {"title": "Somalia", "link": "https://wikipedia.org/wiki/Somalia"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1998", "text": "<PERSON><PERSON><PERSON> scandal: On American television, U.S. President <PERSON> denies having had \"sexual relations\" with former White House intern <PERSON>.", "html": "1998 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_scandal\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON> scandal\"><PERSON><PERSON>sky scandal</a>: On American television, U.S. President <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>\" title=\"<PERSON>\"><PERSON></a> denies having had \"sexual relations\" with former <a href=\"https://wikipedia.org/wiki/White_House\" title=\"White House\">White House</a> intern <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_scandal\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON> scandal\"><PERSON><PERSON>sky scandal</a>: On American television, U.S. President <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> denies having had \"sexual relations\" with former <a href=\"https://wikipedia.org/wiki/White_House\" title=\"White House\">White House</a> intern <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>.", "links": [{"title": "<PERSON><PERSON><PERSON> scandal", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_scandal"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "White House", "link": "https://wikipedia.org/wiki/White_House"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2001", "text": "The 7.7 Mw Gujarat earthquake shakes Western India, leaving 13,805-20,023 dead and about 166,800 injured.", "html": "2001 - The 7.7 <a href=\"https://wikipedia.org/wiki/Seismic_magnitude_scales#Mw\" title=\"Seismic magnitude scales\"><link rel=\"mw-deduplicated-inline-style\" href=\"mw-data:TemplateStyles:r1038841319\">\n<span class=\"rt-commentedText tooltip\" title=\"Moment mag. scale\">M<sub>w</sub></span></a> <a href=\"https://wikipedia.org/wiki/2001_Gujarat_earthquake\" title=\"2001 Gujarat earthquake\">Gujarat earthquake</a> shakes <a href=\"https://wikipedia.org/wiki/Western_India\" title=\"Western India\">Western India</a>, leaving 13,805-20,023 dead and about 166,800 injured.", "no_year_html": "The 7.7 <a href=\"https://wikipedia.org/wiki/Seismic_magnitude_scales#Mw\" title=\"Seismic magnitude scales\"><link rel=\"mw-deduplicated-inline-style\" href=\"mw-data:TemplateStyles:r1038841319\">\n<span class=\"rt-commentedText tooltip\" title=\"Moment mag. scale\">M<sub>w</sub></span></a> <a href=\"https://wikipedia.org/wiki/2001_Gujarat_earthquake\" title=\"2001 Gujarat earthquake\">Gujarat earthquake</a> shakes <a href=\"https://wikipedia.org/wiki/Western_India\" title=\"Western India\">Western India</a>, leaving 13,805-20,023 dead and about 166,800 injured.", "links": [{"title": "Seismic magnitude scales", "link": "https://wikipedia.org/wiki/Seismic_magnitude_scales#Mw"}, {"title": "2001 Gujarat earthquake", "link": "https://wikipedia.org/wiki/2001_Gujarat_earthquake"}, {"title": "Western India", "link": "https://wikipedia.org/wiki/Western_India"}]}, {"year": "2001", "text": "<PERSON>, a lacrosse coach, is killed in a dog attack in San Francisco. The resulting court case clarified the meaning of implied malice murder.", "html": "2001 - <PERSON>, a lacrosse coach, is <a href=\"https://wikipedia.org/wiki/Death_of_<PERSON>_<PERSON>\" title=\"Death of <PERSON>\">killed in a dog attack</a> in San Francisco. The resulting court case clarified the meaning of <a href=\"https://wikipedia.org/wiki/Malice_(law)\" title=\"Mali<PERSON> (law)\">implied malice murder</a>.", "no_year_html": "<PERSON>, a lacrosse coach, is <a href=\"https://wikipedia.org/wiki/Death_of_<PERSON>_<PERSON>hip<PERSON>\" title=\"Death of <PERSON> W<PERSON>\">killed in a dog attack</a> in San Francisco. The resulting court case clarified the meaning of <a href=\"https://wikipedia.org/wiki/Malice_(law)\" title=\"Mali<PERSON> (law)\">implied malice murder</a>.", "links": [{"title": "Death of <PERSON>", "link": "https://wikipedia.org/wiki/Death_of_<PERSON>_<PERSON>"}, {"title": "<PERSON><PERSON> (law)", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_(law)"}]}, {"year": "2009", "text": "Rioting breaks out in Antananarivo, Madagascar, sparking a political crisis that will result in the replacement of President <PERSON> with <PERSON><PERSON>.", "html": "2009 - Rioting breaks out in <a href=\"https://wikipedia.org/wiki/Antananarivo\" title=\"Antananarivo\">Antananarivo</a>, Madagascar, sparking a <a href=\"https://wikipedia.org/wiki/2009_Malagasy_political_crisis\" title=\"2009 Malagasy political crisis\">political crisis</a> that will result in the replacement of <a href=\"https://wikipedia.org/wiki/President_of_Madagascar\" class=\"mw-redirect\" title=\"President of Madagascar\">President</a> <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> with <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>.", "no_year_html": "Rioting breaks out in <a href=\"https://wikipedia.org/wiki/Antananarivo\" title=\"Antananarivo\">Antananarivo</a>, Madagascar, sparking a <a href=\"https://wikipedia.org/wiki/2009_Malagasy_political_crisis\" title=\"2009 Malagasy political crisis\">political crisis</a> that will result in the replacement of <a href=\"https://wikipedia.org/wiki/President_of_Madagascar\" class=\"mw-redirect\" title=\"President of Madagascar\">President</a> <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> with <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>.", "links": [{"title": "Antananarivo", "link": "https://wikipedia.org/wiki/Antananarivo"}, {"title": "2009 Malagasy political crisis", "link": "https://wikipedia.org/wiki/2009_Malagasy_political_crisis"}, {"title": "President of Madagascar", "link": "https://wikipedia.org/wiki/President_of_Madagascar"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "2009", "text": "<PERSON><PERSON><PERSON> gives birth to the world's first surviving octuplets.", "html": "2009 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a> gives birth to the <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_octuplets\" title=\"Suleman octuplets\">world's first surviving octuplets</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a> gives birth to the <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_octuplets\" title=\"Suleman octuplets\">world's first surviving octuplets</a>.", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}, {"title": "<PERSON>eman octuplets", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_octuplets"}]}, {"year": "2015", "text": "An aircraft crashes at Los Llanos Air Base in Albacete, Spain, killing 11 people and injuring 21 others.", "html": "2015 - An aircraft <a href=\"https://wikipedia.org/wiki/2015_Los_Llanos_Air_Base_crash\" title=\"2015 Los Llanos Air Base crash\">crashes</a> at <a href=\"https://wikipedia.org/wiki/Los_Llanos_Air_Base\" class=\"mw-redirect\" title=\"Los Llanos Air Base\">Los Llanos Air Base</a> in <a href=\"https://wikipedia.org/wiki/Albacete\" title=\"Albacete\">Albacete</a>, Spain, killing 11 people and injuring 21 others.", "no_year_html": "An aircraft <a href=\"https://wikipedia.org/wiki/2015_Los_Llanos_Air_Base_crash\" title=\"2015 Los Llanos Air Base crash\">crashes</a> at <a href=\"https://wikipedia.org/wiki/Los_Llanos_Air_Base\" class=\"mw-redirect\" title=\"Los Llanos Air Base\">Los Llanos Air Base</a> in <a href=\"https://wikipedia.org/wiki/Albacete\" title=\"Albacete\">Albacete</a>, Spain, killing 11 people and injuring 21 others.", "links": [{"title": "2015 Los Llanos Air Base crash", "link": "https://wikipedia.org/wiki/2015_Los_Llanos_Air_Base_crash"}, {"title": "Los Llanos Air Base", "link": "https://wikipedia.org/wiki/Los_Llanos_Air_Base"}, {"title": "Albacete", "link": "https://wikipedia.org/wiki/Albacete"}]}, {"year": "2015", "text": "Syrian civil war: The People's Protection Units (YPG) recaptures the city of Kobanî from the Islamic State of Iraq and the Levant (ISIL), marking a turning point in the Siege of Kobanî.", "html": "2015 - <a href=\"https://wikipedia.org/wiki/Syrian_civil_war\" title=\"Syrian civil war\">Syrian civil war</a>: The <a href=\"https://wikipedia.org/wiki/People%27s_Protection_Units\" class=\"mw-redirect\" title=\"People's Protection Units\">People's Protection Units</a> (YPG) recaptures the city of <a href=\"https://wikipedia.org/wiki/Koban%C3%AE\" class=\"mw-redirect\" title=\"Kobanî\">Kobanî</a> from the <a href=\"https://wikipedia.org/wiki/Islamic_State_of_Iraq_and_the_Levant\" class=\"mw-redirect\" title=\"Islamic State of Iraq and the Levant\">Islamic State of Iraq and the Levant</a> (ISIL), marking a turning point in the <a href=\"https://wikipedia.org/wiki/Siege_of_Koban%C3%AE\" title=\"Siege of Kobanî\">Siege of Kobanî</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Syrian_civil_war\" title=\"Syrian civil war\">Syrian civil war</a>: The <a href=\"https://wikipedia.org/wiki/People%27s_Protection_Units\" class=\"mw-redirect\" title=\"People's Protection Units\">People's Protection Units</a> (YPG) recaptures the city of <a href=\"https://wikipedia.org/wiki/Koban%C3%AE\" class=\"mw-redirect\" title=\"Kobanî\">Kobanî</a> from the <a href=\"https://wikipedia.org/wiki/Islamic_State_of_Iraq_and_the_Levant\" class=\"mw-redirect\" title=\"Islamic State of Iraq and the Levant\">Islamic State of Iraq and the Levant</a> (ISIL), marking a turning point in the <a href=\"https://wikipedia.org/wiki/Siege_of_Koban%C3%AE\" title=\"Siege of Kobanî\">Siege of Kobanî</a>.", "links": [{"title": "Syrian civil war", "link": "https://wikipedia.org/wiki/Syrian_civil_war"}, {"title": "People's Protection Units", "link": "https://wikipedia.org/wiki/People%27s_Protection_Units"}, {"title": "Kobanî", "link": "https://wikipedia.org/wiki/Koban%C3%AE"}, {"title": "Islamic State of Iraq and the Levant", "link": "https://wikipedia.org/wiki/Islamic_State_of_Iraq_and_the_Levant"}, {"title": "Siege of Kobanî", "link": "https://wikipedia.org/wiki/Siege_of_Koban%C3%AE"}]}, {"year": "2020", "text": "A Sikorsky S-76B flying from John Wayne Airport to Camarillo Airport crashes in Calabasas, 30 miles west of Los Angeles, killing all nine people on board, including five-time NBA champion <PERSON> and his daughter <PERSON><PERSON><PERSON>.", "html": "2020 - A <a href=\"https://wikipedia.org/wiki/Sikorsky_S-76\" title=\"Sikorsky S-76\">Sikorsky S-76B</a> flying from <a href=\"https://wikipedia.org/wiki/John_Wayne_Airport\" title=\"John Wayne Airport\">John Wayne Airport</a> to <a href=\"https://wikipedia.org/wiki/Camarillo_Airport\" title=\"Camarillo Airport\">Camarillo Airport</a> <a href=\"https://wikipedia.org/wiki/2020_Calabasas_helicopter_crash\" title=\"2020 Calabasas helicopter crash\">crashes in Calabasas</a>, 30 miles west of <a href=\"https://wikipedia.org/wiki/Los_Angeles\" title=\"Los Angeles\">Los Angeles</a>, killing all nine people on board, including five-time <a href=\"https://wikipedia.org/wiki/NBA\" class=\"mw-redirect\" title=\"NBA\">NBA</a> champion <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> and his daughter <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>.", "no_year_html": "A <a href=\"https://wikipedia.org/wiki/Sikorsky_S-76\" title=\"Sikorsky S-76\">Sikorsky S-76B</a> flying from <a href=\"https://wikipedia.org/wiki/John_Wayne_Airport\" title=\"John Wayne Airport\">John Wayne Airport</a> to <a href=\"https://wikipedia.org/wiki/Camarillo_Airport\" title=\"Camarillo Airport\">Camarillo Airport</a> <a href=\"https://wikipedia.org/wiki/2020_Calabasas_helicopter_crash\" title=\"2020 Calabasas helicopter crash\">crashes in Calabasas</a>, 30 miles west of <a href=\"https://wikipedia.org/wiki/Los_Angeles\" title=\"Los Angeles\">Los Angeles</a>, killing all nine people on board, including five-time <a href=\"https://wikipedia.org/wiki/NBA\" class=\"mw-redirect\" title=\"NBA\">NBA</a> champion <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> and his daughter <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>.", "links": [{"title": "Sikorsky S-76", "link": "https://wikipedia.org/wiki/Sikorsky_S-76"}, {"title": "John <PERSON> Airport", "link": "https://wikipedia.org/wiki/John_<PERSON>_Airport"}, {"title": "Camarillo Airport", "link": "https://wikipedia.org/wiki/Camarillo_Airport"}, {"title": "2020 Calabasas helicopter crash", "link": "https://wikipedia.org/wiki/2020_Calaba<PERSON>s_helicopter_crash"}, {"title": "Los Angeles", "link": "https://wikipedia.org/wiki/Los_Angeles"}, {"title": "NBA", "link": "https://wikipedia.org/wiki/NBA"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_Bryant"}, {"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "2021", "text": "Protesters and farmers storm the Red Fort near Delhi, clashing with police. One protester is killed and more than 80 police officers are injured.", "html": "2021 - Protesters and farmers <a href=\"https://wikipedia.org/wiki/2021_Farmers%27_Republic_Day_violence\" class=\"mw-redirect\" title=\"2021 Farmers' Republic Day violence\">storm</a> the <a href=\"https://wikipedia.org/wiki/Red_Fort\" title=\"Red Fort\">Red Fort</a> near <a href=\"https://wikipedia.org/wiki/Delhi\" title=\"Delhi\">Delhi</a>, clashing with police. One protester is killed and more than 80 police officers are injured.", "no_year_html": "Protesters and farmers <a href=\"https://wikipedia.org/wiki/2021_Farmers%27_Republic_Day_violence\" class=\"mw-redirect\" title=\"2021 Farmers' Republic Day violence\">storm</a> the <a href=\"https://wikipedia.org/wiki/Red_Fort\" title=\"Red Fort\">Red Fort</a> near <a href=\"https://wikipedia.org/wiki/Delhi\" title=\"Delhi\">Delhi</a>, clashing with police. One protester is killed and more than 80 police officers are injured.", "links": [{"title": "2021 Farmers' Republic Day violence", "link": "https://wikipedia.org/wiki/2021_Farmers%27_Republic_Day_violence"}, {"title": "Red Fort", "link": "https://wikipedia.org/wiki/Red_Fort"}, {"title": "Delhi", "link": "https://wikipedia.org/wiki/Delhi"}]}], "Births": [{"year": "183", "text": "<PERSON>, wife of <PERSON> (d. 221)", "html": "183 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>\" title=\"Lady <PERSON><PERSON>\">Lady <PERSON></a>, wife of <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Pi\"><PERSON></a> (d. 221)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>\" title=\"<PERSON> <PERSON><PERSON>\">Lady <PERSON></a>, wife of <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Pi\"><PERSON></a> (d. 221)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Cao_<PERSON>"}]}, {"year": "1541", "text": "<PERSON><PERSON><PERSON>, French poet and translator (d. 1596)", "html": "1541 - <a href=\"https://wikipedia.org/wiki/Florent_Chrestien\" title=\"Florent Chrestien\"><PERSON><PERSON><PERSON></a>, French poet and translator (d. 1596)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Florent_Chrestien\" title=\"Florent Chrestien\"><PERSON><PERSON><PERSON></a>, French poet and translator (d. 1596)", "links": [{"title": "Florent <PERSON>", "link": "https://wikipedia.org/wiki/Florent_Chrestien"}]}, {"year": "1549", "text": "<PERSON>, German theologian (d. 1614)", "html": "1549 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German theologian (d. 1614)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German theologian (d. 1614)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1582", "text": "<PERSON>, Italian painter (d. 1647)", "html": "1582 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian painter (d. 1647)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian painter (d. 1647)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1657", "text": "<PERSON>, Archbishop of Canterbury (d. 1737)", "html": "1657 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"William <PERSON>\"><PERSON></a>, Archbishop of Canterbury (d. 1737)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/William_<PERSON>\" title=\"William Wake\"><PERSON></a>, Archbishop of Canterbury (d. 1737)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1714", "text": "<PERSON><PERSON><PERSON>, French sculptor and educator (d. 1785)", "html": "1714 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, French sculptor and educator (d. 1785)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, French sculptor and educator (d. 1785)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>"}]}, {"year": "1715", "text": "<PERSON>, French philosopher (d. 1771)", "html": "1715 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>lv%C3%A9tius\" title=\"<PERSON>\"><PERSON></a>, French philosopher (d. 1771)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>lv%C3%A9tius\" title=\"<PERSON>\"><PERSON></a>, French philosopher (d. 1771)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_Helv%C3%A9tius"}]}, {"year": "1716", "text": "<PERSON>, 1st Viscount <PERSON>, English general and politician, Secretary of State for the Colonies (d. 1785)", "html": "1716 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_1st_Viscount_Sack<PERSON>\" title=\"<PERSON>, 1st Viscount Sa<PERSON>\"><PERSON>, 1st Viscount <PERSON></a>, English general and politician, <a href=\"https://wikipedia.org/wiki/Secretary_of_State_for_the_Colonies\" title=\"Secretary of State for the Colonies\">Secretary of State for the Colonies</a> (d. 1785)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>,_1st_Viscount_<PERSON>\" title=\"<PERSON>, 1st Viscount <PERSON>\"><PERSON>, 1st Viscount <PERSON></a>, English general and politician, <a href=\"https://wikipedia.org/wiki/Secretary_of_State_for_the_Colonies\" title=\"Secretary of State for the Colonies\">Secretary of State for the Colonies</a> (d. 1785)", "links": [{"title": "<PERSON>, 1st Viscount <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>,_1st_Viscount_<PERSON>"}, {"title": "Secretary of State for the Colonies", "link": "https://wikipedia.org/wiki/Secretary_of_State_for_the_Colonies"}]}, {"year": "1722", "text": "<PERSON>, Scottish minister and author (d. 1805)", "html": "1722 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish minister and author (d. 1805)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish minister and author (d. 1805)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1763", "text": "<PERSON> Sweden (d. 1844)", "html": "1763 - <a href=\"https://wikipedia.org/wiki/Charles_XIV_John_of_Sweden\" class=\"mw-redirect\" title=\"Charles XIV John of Sweden\"><PERSON> of <PERSON></a> (d. 1844)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Charles_XIV_John_of_Sweden\" class=\"mw-redirect\" title=\"Charles XIV John of Sweden\"><PERSON> of <PERSON></a> (d. 1844)", "links": [{"title": "<PERSON> of Sweden", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>_Sweden"}]}, {"year": "1781", "text": "<PERSON>, German poet and author (d. 1831)", "html": "1781 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, German poet and author (d. 1831)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, German poet and author (d. 1831)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1813", "text": "<PERSON>, Dominican philosopher and poet, founding father of the Dominican Republic (d. 1876)", "html": "1813 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dominican philosopher and poet, founding father of the <a href=\"https://wikipedia.org/wiki/Dominican_Republic\" title=\"Dominican Republic\">Dominican Republic</a> (d. 1876)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dominican philosopher and poet, founding father of the <a href=\"https://wikipedia.org/wiki/Dominican_Republic\" title=\"Dominican Republic\">Dominican Republic</a> (d. 1876)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "Dominican Republic", "link": "https://wikipedia.org/wiki/Dominican_Republic"}]}, {"year": "1824", "text": "<PERSON>, Polish chemist (d. 1888)", "html": "1824 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C5%84ski\" title=\"<PERSON>\"><PERSON></a>, Polish chemist (d. 1888)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C5%84ski\" title=\"<PERSON>\"><PERSON></a>, Polish chemist (d. 1888)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Emil_Czyrnia%C5%84ski"}]}, {"year": "1832", "text": "<PERSON>, Jr., American lawyer and Supreme Court justice (d. 1924)", "html": "1832 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Jr.\" class=\"mw-redirect\" title=\"<PERSON>, Jr.\"><PERSON>, Jr.</a>, American lawyer and Supreme Court justice (d. 1924)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>,_Jr.\" class=\"mw-redirect\" title=\"<PERSON>, Jr.\"><PERSON>, Jr.</a>, American lawyer and Supreme Court justice (d. 1924)", "links": [{"title": "<PERSON>, Jr.", "link": "https://wikipedia.org/wiki/<PERSON>,_Jr."}]}, {"year": "1842", "text": "<PERSON>, French poet and author (d. 1908)", "html": "1842 - <a href=\"https://wikipedia.org/wiki/Fran%C3%A7ois_Copp%C3%A9e\" title=\"<PERSON>\"><PERSON></a>, French poet and author (d. 1908)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Fran%C3%A7ois_Copp%C3%A9e\" title=\"<PERSON>\"><PERSON></a>, French poet and author (d. 1908)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Fran%C3%A7ois_Copp%C3%A9e"}]}, {"year": "1852", "text": "<PERSON>, Italian-French explorer (d. 1905)", "html": "1852 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_Brazza\" title=\"<PERSON> Brazza\"><PERSON></a>, Italian-French explorer (d. 1905)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_Brazza\" title=\"<PERSON> B<PERSON>\"><PERSON></a>, Italian-French explorer (d. 1905)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1861", "text": "<PERSON>, French painter (d. 1932)", "html": "1861 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French painter (d. 1932)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French painter (d. 1932)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1863", "text": "<PERSON>, Australian politician, 17th Premier of New South Wales (d. 1922)", "html": "1863 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian politician, 17th <a href=\"https://wikipedia.org/wiki/Premier_of_New_South_Wales\" title=\"Premier of New South Wales\">Premier of New South Wales</a> (d. 1922)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian politician, 17th <a href=\"https://wikipedia.org/wiki/Premier_of_New_South_Wales\" title=\"Premier of New South Wales\">Premier of New South Wales</a> (d. 1922)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Premier of New South Wales", "link": "https://wikipedia.org/wiki/Premier_of_New_South_Wales"}]}, {"year": "1864", "text": "<PERSON><PERSON><PERSON><PERSON>, Slovene-Hungarian poet and journalist (d. 1934)", "html": "1864 - <a href=\"https://wikipedia.org/wiki/J%C3%B3<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Slovene-Hungarian poet and journalist (d. 1934)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/J%C3%B3<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Slovene-Hungarian poet and journalist (d. 1934)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/J%C3%B3<PERSON><PERSON>_<PERSON>"}]}, {"year": "1866", "text": "<PERSON>, American golfer (d. 1933)", "html": "1866 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(golfer)\" title=\"<PERSON> (golfer)\"><PERSON></a>, American golfer (d. 1933)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(golfer)\" title=\"<PERSON> (golfer)\"><PERSON></a>, American golfer (d. 1933)", "links": [{"title": "<PERSON> (golfer)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(golfer)"}]}, {"year": "1877", "text": "<PERSON><PERSON>, Dutch painter (d. 1968)", "html": "1877 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Dutch painter (d. 1968)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Dutch painter (d. 1968)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1880", "text": "<PERSON>, American general, Medal of Honor recipient (d. 1964)", "html": "1880 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American general, <a href=\"https://wikipedia.org/wiki/Medal_of_Honor\" title=\"Medal of Honor\">Medal of Honor</a> recipient (d. 1964)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American general, <a href=\"https://wikipedia.org/wiki/Medal_of_Honor\" title=\"Medal of Honor\">Medal of Honor</a> recipient (d. 1964)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Medal of Honor", "link": "https://wikipedia.org/wiki/Medal_of_Honor"}]}, {"year": "1885", "text": "<PERSON>, English engineer and academic (d. 1974)", "html": "1885 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English engineer and academic (d. 1974)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English engineer and academic (d. 1974)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1885", "text": "<PERSON>, Swedish figure skater (d. 1962)", "html": "1885 - <a href=\"https://wikipedia.org/wiki/Per_Thor%C3%A9n\" title=\"<PERSON>\"><PERSON></a>, Swedish figure skater (d. 1962)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Per_Thor%C3%A9n\" title=\"<PERSON>\"><PERSON></a>, Swedish figure skater (d. 1962)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Per_Thor%C3%A9n"}]}, {"year": "1887", "text": "<PERSON>, French-Luxembourgish cyclist (d. 1915)", "html": "1887 - <a href=\"https://wikipedia.org/wiki/Fran%C3%A<PERSON><PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French-Luxembourgish cyclist (d. 1915)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Fran%C3%<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French-Luxembourgish cyclist (d. 1915)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Fran%C3%A7<PERSON>_<PERSON>"}]}, {"year": "1887", "text": "<PERSON>, American admiral and pilot (d. 1947)", "html": "1887 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American admiral and pilot (d. 1947)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American admiral and pilot (d. 1947)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1891", "text": "<PERSON>, Italian-American mob boss (d. 1973)", "html": "1891 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian-American mob boss (d. 1973)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian-American mob boss (d. 1973)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1891", "text": "<PERSON>, German priest and martyr (d. 1942)", "html": "1891 - <a href=\"https://wikipedia.org/wiki/August_<PERSON><PERSON><PERSON><PERSON>\" title=\"August <PERSON>\">August <PERSON></a>, German priest and martyr (d. 1942)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/August_<PERSON><PERSON><PERSON><PERSON>\" title=\"August <PERSON>\">August <PERSON></a>, German priest and martyr (d. 1942)", "links": [{"title": "<PERSON> <PERSON>", "link": "https://wikipedia.org/wiki/August_<PERSON><PERSON><PERSON><PERSON>"}]}, {"year": "1891", "text": "<PERSON>, American-Canadian neurosurgeon and academic (d. 1976)", "html": "1891 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American-Canadian neurosurgeon and academic (d. 1976)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American-Canadian neurosurgeon and academic (d. 1976)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1892", "text": "<PERSON><PERSON>, American pilot (d. 1926)", "html": "1892 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American pilot (d. 1926)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American pilot (d. 1926)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1893", "text": "<PERSON>, Italian mob boss (d. 1976)", "html": "1893 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian mob boss (d. 1976)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian mob boss (d. 1976)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1899", "text": "<PERSON><PERSON><PERSON><PERSON>, Russian-Estonian graphic designer and illustrator (d. 1974)", "html": "1899 - <a href=\"https://wikipedia.org/wiki/G%C3%<PERSON><PERSON><PERSON>_Reindorff\" title=\"<PERSON><PERSON><PERSON><PERSON> Reindorff\"><PERSON><PERSON><PERSON><PERSON></a>, Russian-Estonian graphic designer and illustrator (d. 1974)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/G%C3%<PERSON><PERSON><PERSON>_Reindorff\" title=\"<PERSON><PERSON><PERSON><PERSON> Reindorff\"><PERSON><PERSON><PERSON><PERSON></a>, Russian-Estonian graphic designer and illustrator (d. 1974)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/G%C3%BC<PERSON><PERSON>_Reindorff"}]}, {"year": "1900", "text": "<PERSON>, German conductor (d. 1967)", "html": "1900 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German conductor (d. 1967)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German conductor (d. 1967)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1902", "text": "<PERSON><PERSON>, Dutch author (d. 1940)", "html": "1902 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_ter_<PERSON>ra<PERSON>\" title=\"Menno ter Braak\"><PERSON><PERSON> <PERSON><PERSON></a>, Dutch author (d. 1940)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_ter_<PERSON>\" title=\"Menno ter Braak\"><PERSON><PERSON> <PERSON><PERSON></a>, Dutch author (d. 1940)", "links": [{"title": "<PERSON><PERSON> ter Braak", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_te<PERSON>_<PERSON>ak"}]}, {"year": "1904", "text": "<PERSON><PERSON>, American physiologist and nutritionist (d. 2004)", "html": "1904 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"Ancel Keys\"><PERSON><PERSON></a>, American physiologist and nutritionist (d. 2004)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"Ancel Keys\"><PERSON><PERSON></a>, American physiologist and nutritionist (d. 2004)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1904", "text": "<PERSON><PERSON>, Irish lawyer and politician, Irish Minister for External Affairs Nobel Prize laureate (d. 1988)", "html": "1904 - <a href=\"https://wikipedia.org/wiki/Se%C3%A1n_MacBride\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Irish lawyer and politician, <a href=\"https://wikipedia.org/wiki/Minister_for_Foreign_Affairs_(Ireland)\" title=\"Minister for Foreign Affairs (Ireland)\">Irish Minister for External Affairs</a> <a href=\"https://wikipedia.org/wiki/Nobel_Peace_Prize\" title=\"Nobel Peace Prize\">Nobel Prize</a> laureate (d. 1988)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Se%C3%A1n_MacBride\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Irish lawyer and politician, <a href=\"https://wikipedia.org/wiki/Minister_for_Foreign_Affairs_(Ireland)\" title=\"Minister for Foreign Affairs (Ireland)\">Irish Minister for External Affairs</a> <a href=\"https://wikipedia.org/wiki/Nobel_Peace_Prize\" title=\"Nobel Peace Prize\">Nobel Prize</a> laureate (d. 1988)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Se%C3%A1n_MacBride"}, {"title": "Minister for Foreign Affairs (Ireland)", "link": "https://wikipedia.org/wiki/Minister_for_Foreign_Affairs_(Ireland)"}, {"title": "Nobel Peace Prize", "link": "https://wikipedia.org/wiki/Nobel_Peace_Prize"}]}, {"year": "1905", "text": "<PERSON>, American actor and singer (d. 2007)", "html": "1905 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(actor,_born_1905)\" title=\"<PERSON> (actor, born 1905)\"><PERSON></a>, American actor and singer (d. 2007)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(actor,_born_1905)\" title=\"<PERSON> (actor, born 1905)\"><PERSON></a>, American actor and singer (d. 2007)", "links": [{"title": "<PERSON> (actor, born 1905)", "link": "https://wikipedia.org/wiki/<PERSON>_(actor,_born_1905)"}]}, {"year": "1905", "text": "<PERSON>, Austrian-American singer (d. 1987)", "html": "1905 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Austrian-American singer (d. 1987)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Austrian-American singer (d. 1987)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1907", "text": "<PERSON>, Australian politician (d. 1977)", "html": "1907 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian politician (d. 1977)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian politician (d. 1977)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1907", "text": "<PERSON><PERSON>, Greek priest and philologist (d. 2001)", "html": "1907 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Greek priest and philologist (d. 2001)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Greek priest and philologist (d. 2001)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1908", "text": "<PERSON>, English actress (d. 1990)", "html": "1908 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actress (d. 1990)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actress (d. 1990)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1908", "text": "<PERSON><PERSON><PERSON><PERSON>, German painter and sculptor (d. 2009)", "html": "1908 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_Geiger\" title=\"<PERSON><PERSON><PERSON><PERSON> Geiger\"><PERSON><PERSON><PERSON><PERSON> Geiger</a>, German painter and sculptor (d. 2009)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_Geiger\" title=\"Rup<PERSON><PERSON> Geiger\"><PERSON><PERSON><PERSON><PERSON>eiger</a>, German painter and sculptor (d. 2009)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON> Geiger", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_Geiger"}]}, {"year": "1908", "text": "<PERSON><PERSON><PERSON><PERSON>, French violinist (d. 1997)", "html": "1908 - <a href=\"https://wikipedia.org/wiki/St%C3%A9<PERSON><PERSON>_<PERSON>li\" title=\"<PERSON><PERSON><PERSON><PERSON>pel<PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, French violinist (d. 1997)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/St%C3%A9<PERSON><PERSON>_<PERSON>li\" title=\"<PERSON><PERSON><PERSON><PERSON> Grappelli\"><PERSON><PERSON><PERSON><PERSON></a>, French violinist (d. 1997)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/St%C3%A9<PERSON><PERSON>_<PERSON>"}]}, {"year": "1908", "text": "<PERSON>, American yachtsman (d. 1985)", "html": "1908 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American yachtsman (d. 1985)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American yachtsman (d. 1985)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1910", "text": "<PERSON>, Hungarian-French animator, director, and screenwriter (d. 1989)", "html": "1910 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Jean Image\"><PERSON></a>, Hungarian-French animator, director, and screenwriter (d. 1989)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Jean_<PERSON>\" title=\"Jean Image\"><PERSON></a>, Hungarian-French animator, director, and screenwriter (d. 1989)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1911", "text": "<PERSON><PERSON><PERSON><PERSON>, German-American physicist and academic, Nobel Prize laureate (d. 1993)", "html": "1911 - <a href=\"https://wikipedia.org/wiki/<PERSON>yk<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, German-American physicist and academic, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Physics\" title=\"Nobel Prize in Physics\">Nobel Prize</a> laureate (d. 1993)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>ykar<PERSON>_<PERSON>\" title=\"<PERSON>ykar<PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, German-American physicist and academic, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Physics\" title=\"Nobel Prize in Physics\">Nobel Prize</a> laureate (d. 1993)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Polykarp_<PERSON>"}, {"title": "Nobel Prize in Physics", "link": "https://wikipedia.org/wiki/Nobel_Prize_in_Physics"}]}, {"year": "1911", "text": "<PERSON><PERSON>, German composer and conductor (d. 2002)", "html": "1911 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, German composer and conductor (d. 2002)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, German composer and conductor (d. 2002)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1913", "text": "<PERSON>, American pianist and composer (d. 1990)", "html": "1913 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American pianist and composer (d. 1990)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American pianist and composer (d. 1990)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1914", "text": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, Imperial Princess of the Ottoman Empire (d. 2006)", "html": "1914 - <a href=\"https://wikipedia.org/wiki/D%C3%BCrr%C3%BC%C5%9F<PERSON><PERSON>_Sultan\" title=\"<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> Sultan\"><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> Sultan</a>, Imperial Princess of the Ottoman Empire (d. 2006)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/D%C3%BCrr%C3%BC%C5%9<PERSON><PERSON><PERSON>_Sultan\" title=\"<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> Sultan\"><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> Sultan</a>, Imperial Princess of the Ottoman Empire (d. 2006)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/D%C3%BCrr%C3%BC%C5%9F<PERSON><PERSON>_<PERSON>"}]}, {"year": "1915", "text": "<PERSON>, American actor (d. 1970)", "html": "1915 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor (d. 1970)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor (d. 1970)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1917", "text": "<PERSON>, American runner and captain (d. 2014)", "html": "1917 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American runner and captain (d. 2014)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American runner and captain (d. 2014)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1918", "text": "<PERSON>, American author (d. 2009)", "html": "1918 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A9_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author (d. 2009)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author (d. 2009)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A9_Farmer"}]}, {"year": "1919", "text": "<PERSON><PERSON>, Italian footballer (d. 1949)", "html": "1919 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Italian footballer (d. 1949)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Italian footballer (d. 1949)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Vale<PERSON>_<PERSON>"}]}, {"year": "1919", "text": "<PERSON>, English footballer and manager (d. 2004)", "html": "1919 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(footballer)\" title=\"<PERSON> (footballer)\"><PERSON></a>, English footballer and manager (d. 2004)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>(footballer)\" title=\"<PERSON> (footballer)\"><PERSON></a>, English footballer and manager (d. 2004)", "links": [{"title": "<PERSON> (footballer)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>(footballer)"}]}, {"year": "1919", "text": "<PERSON><PERSON>, South Korean politician, 24th Prime Minister of South Korea (d. 2020)", "html": "1919 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>-jong\" title=\"<PERSON><PERSON> <PERSON>-jong\"><PERSON><PERSON>ong</a>, South Korean politician, 24th <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_South_Korea\" title=\"Prime Minister of South Korea\">Prime Minister of South Korea</a> (d. 2020)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>-jong\" title=\"<PERSON><PERSON>-jong\"><PERSON><PERSON></a>, South Korean politician, 24th <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_South_Korea\" title=\"Prime Minister of South Korea\">Prime Minister of South Korea</a> (d. 2020)", "links": [{"title": "<PERSON><PERSON>ong", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>-jong"}, {"title": "Prime Minister of South Korea", "link": "https://wikipedia.org/wiki/Prime_Minister_of_South_Korea"}]}, {"year": "1920", "text": "<PERSON>, Austrian-American paranormal researcher and author (d. 2009)", "html": "1920 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Austrian-American paranormal researcher and author (d. 2009)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Austrian-American paranormal researcher and author (d. 2009)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1921", "text": "<PERSON>, French record producer, founded Barclay Records (d. 2005)", "html": "1921 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French record producer, founded <a href=\"https://wikipedia.org/wiki/Barclay_Records\" class=\"mw-redirect\" title=\"Barclay Records\">Barclay Records</a> (d. 2005)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>\" title=\"<PERSON>\"><PERSON></a>, French record producer, founded <a href=\"https://wikipedia.org/wiki/Barclay_Records\" class=\"mw-redirect\" title=\"Barclay Records\">Barclay Records</a> (d. 2005)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Barclay Records", "link": "https://wikipedia.org/wiki/Barclay_Records"}]}, {"year": "1921", "text": "<PERSON><PERSON><PERSON>, Japanese businessman, co-founded Sony (d. 1999)", "html": "1921 - <a href=\"https://wikipedia.org/wiki/<PERSON>kio_Mo<PERSON>\" title=\"Akio Morita\"><PERSON><PERSON><PERSON></a>, Japanese businessman, co-founded <a href=\"https://wikipedia.org/wiki/Sony\" title=\"Sony\">Sony</a> (d. 1999)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_Mo<PERSON>\" title=\"<PERSON>kio Mo<PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese businessman, co-founded <a href=\"https://wikipedia.org/wiki/Sony\" title=\"Sony\">Sony</a> (d. 1999)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Akio_Morita"}, {"title": "Sony", "link": "https://wikipedia.org/wiki/Sony"}]}, {"year": "1921", "text": "<PERSON><PERSON><PERSON><PERSON>, Finnish actor and theater councilor (d. 2008)", "html": "1921 - <a href=\"https://wikipedia.org/wiki/Veikko_Uusim%C3%A4ki\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Finnish actor and theater councilor (d. 2008)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/V<PERSON>k<PERSON>_Uusim%C3%A4ki\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Finnish actor and theater councilor (d. 2008)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Veikko_Uusim%C3%A4ki"}]}, {"year": "1922", "text": "<PERSON>, English actor and screenwriter (d. 1996)", "html": "1922 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actor and screenwriter (d. 1996)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actor and screenwriter (d. 1996)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1922", "text": "<PERSON><PERSON>, Irish footballer and politician, 7th Irish Minister for Health (d. 1993)", "html": "1922 - <a href=\"https://wikipedia.org/wiki/Se%C3%A1n_F<PERSON>gan\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Irish footballer and politician, 7th <a href=\"https://wikipedia.org/wiki/Minister_for_Health_(Ireland)\" title=\"Minister for Health (Ireland)\">Irish Minister for Health</a> (d. 1993)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Se%C3%A1n_F<PERSON>gan\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Irish footballer and politician, 7th <a href=\"https://wikipedia.org/wiki/Minister_for_Health_(Ireland)\" title=\"Minister for Health (Ireland)\">Irish Minister for Health</a> (d. 1993)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Se%C3%A1n_Flanagan"}, {"title": "Minister for Health (Ireland)", "link": "https://wikipedia.org/wiki/Minister_for_Health_(Ireland)"}]}, {"year": "1922", "text": "<PERSON>, English footballer (d. 2010)", "html": "1922 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English footballer (d. 2010)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English footballer (d. 2010)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1923", "text": "<PERSON>, American admiral (d. 2014)", "html": "1923 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American admiral (d. 2014)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American admiral (d. 2014)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "1923", "text": "<PERSON>, American actress and singer (d. 2017)", "html": "1923 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress and singer (d. 2017)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress and singer (d. 2017)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1924", "text": "<PERSON>, Swedish singer and actress (d. 2014)", "html": "1924 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Ba<PERSON>\"><PERSON></a>, Swedish singer and actress (d. 2014)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Alice Babs\"><PERSON></a>, Swedish singer and actress (d. 2014)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>bs"}]}, {"year": "1924", "text": "<PERSON><PERSON><PERSON>, American violinist (d. 2016)", "html": "1924 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American violinist (d. 2016)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American violinist (d. 2016)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1924", "text": "<PERSON>, American philanthropist and politician, Mayor of Dallas (d. 1998)", "html": "1924 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American philanthropist and politician, <a href=\"https://wikipedia.org/wiki/Mayor_of_Dallas\" title=\"Mayor of Dallas\">Mayor of Dallas</a> (d. 1998)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American philanthropist and politician, <a href=\"https://wikipedia.org/wiki/Mayor_of_Dallas\" title=\"Mayor of Dallas\">Mayor of Dallas</a> (d. 1998)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Mayor of Dallas", "link": "https://wikipedia.org/wiki/Mayor_of_Dallas"}]}, {"year": "1925", "text": "<PERSON>, English bishop and theologian (d. 2016)", "html": "1925 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(bishop)\" title=\"<PERSON> (bishop)\"><PERSON></a>, English bishop and theologian (d. 2016)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(bishop)\" title=\"<PERSON> (bishop)\"><PERSON></a>, English bishop and theologian (d. 2016)", "links": [{"title": "<PERSON> (bishop)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(bishop)"}]}, {"year": "1925", "text": "<PERSON>, American actress (d. 2015)", "html": "1925 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress (d. 2015)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress (d. 2015)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1925", "text": "<PERSON>, American actor, activist, director, race car driver, and businessman, co-founded Newman's Own (d. 2008)", "html": "1925 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor, activist, director, race car driver, and businessman, co-founded <a href=\"https://wikipedia.org/wiki/<PERSON>%27s_Own\" title=\"<PERSON>'s Own\"><PERSON>'s Own</a> (d. 2008)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor, activist, director, race car driver, and businessman, co-founded <a href=\"https://wikipedia.org/wiki/<PERSON>%27s_Own\" title=\"<PERSON>'s Own\"><PERSON>'s Own</a> (d. 2008)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "<PERSON>'s Own", "link": "https://wikipedia.org/wiki/Newman%27s_Own"}]}, {"year": "1925", "text": "<PERSON>, American football player and sportscaster (d. 2013)", "html": "1925 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player and sportscaster (d. 2013)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player and sportscaster (d. 2013)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1925", "text": "<PERSON>, Canadian journalist and politician (d. 2004)", "html": "1925 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian journalist and politician (d. 2004)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian journalist and politician (d. 2004)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1926", "text": "<PERSON><PERSON>, Pakistani linguist and scholar (d. 2013)", "html": "1926 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Pakistani linguist and scholar (d. 2013)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Pakistani linguist and scholar (d. 2013)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>"}]}, {"year": "1926", "text": "<PERSON>, Jr., American architect and businessman, co-founded the Sea Pines Company (d. 2014)", "html": "1926 - <a href=\"https://wikipedia.org/wiki/<PERSON>,_Jr.\" class=\"mw-redirect\" title=\"<PERSON>, Jr.\"><PERSON>, Jr.</a>, American architect and businessman, co-founded the <a href=\"https://wikipedia.org/wiki/Sea_Pines_Company\" title=\"Sea Pines Company\">Sea Pines Company</a> (d. 2014)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>,_Jr.\" class=\"mw-redirect\" title=\"<PERSON>, Jr.\"><PERSON>, Jr.</a>, American architect and businessman, co-founded the <a href=\"https://wikipedia.org/wiki/Sea_Pines_Company\" title=\"Sea Pines Company\">Sea Pines Company</a> (d. 2014)", "links": [{"title": "<PERSON>, Jr.", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>,_Jr."}, {"title": "Sea Pines Company", "link": "https://wikipedia.org/wiki/Sea_Pines_Company"}]}, {"year": "1927", "text": "<PERSON>, Honduran businessman and politician, President of Honduras (d. 2005)", "html": "1927 - <a href=\"https://wikipedia.org/wiki/Jos%C3%A9_Azcona_del_Hoyo\" title=\"José <PERSON> Hoyo\"><PERSON></a>, Honduran businessman and politician, <a href=\"https://wikipedia.org/wiki/President_of_Honduras\" title=\"President of Honduras\">President of Honduras</a> (d. 2005)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Jos%C3%A9_Azcona_del_Hoyo\" title=\"José <PERSON> Hoyo\"><PERSON></a>, Honduran businessman and politician, <a href=\"https://wikipedia.org/wiki/President_of_Honduras\" title=\"President of Honduras\">President of Honduras</a> (d. 2005)", "links": [{"title": "José <PERSON>", "link": "https://wikipedia.org/wiki/Jos%C3%A9_Azcona_del_Hoyo"}, {"title": "President of Honduras", "link": "https://wikipedia.org/wiki/President_of_Honduras"}]}, {"year": "1927", "text": "<PERSON>, American baseball player and scout (d. 1985)", "html": "1927 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player and scout (d. 1985)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player and scout (d. 1985)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1927", "text": "<PERSON>, German footballer and manager (d. 2013)", "html": "1927 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German footballer and manager (d. 2013)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German footballer and manager (d. 2013)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1928", "text": "<PERSON>, French actor and director (d. 2000)", "html": "1928 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French actor and director (d. 2000)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French actor and director (d. 2000)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1929", "text": "<PERSON>, American cartoonist, playwright, screenwriter, and educator (d. 2025)", "html": "1929 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American cartoonist, playwright, screenwriter, and educator (d. 2025)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American cartoonist, playwright, screenwriter, and educator (d. 2025)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1933", "text": "<PERSON>, American mathematician (d. 2017)", "html": "1933 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American mathematician (d. 2017)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American mathematician (d. 2017)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1934", "text": "<PERSON>, Canadian businessman and publisher (d. 2020)", "html": "1934 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian businessman and publisher (d. 2020)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian businessman and publisher (d. 2020)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1934", "text": "<PERSON>, American director, playwright, and critic (d. 2014)", "html": "1934 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American director, playwright, and critic (d. 2014)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American director, playwright, and critic (d. 2014)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1934", "text": "<PERSON><PERSON> \"<PERSON>\" <PERSON>, American pianist and songwriter (d. 2023)", "html": "1934 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_%22P<PERSON>%22_<PERSON>\" title='<PERSON><PERSON> \"Piano\" <PERSON>'><PERSON><PERSON> \"Piano\" <PERSON></a>, American pianist and songwriter (d. 2023)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_%22P<PERSON>%22_<PERSON>\" title='<PERSON><PERSON> \"Piano\" <PERSON>'><PERSON><PERSON> \"Piano\" <PERSON></a>, American pianist and songwriter (d. 2023)", "links": [{"title": "<PERSON><PERSON> \"Piano\" <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_%22P<PERSON>%22_<PERSON>"}]}, {"year": "1934", "text": "<PERSON>, American baseball player, sportscaster and actor (d. 2025)", "html": "1934 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player, sportscaster and actor (d. 2025)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player, sportscaster and actor (d. 2025)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1935", "text": "<PERSON><PERSON><PERSON>, Italian journalist and politician", "html": "1935 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Italian journalist and politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Italian journalist and politician", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>ias"}]}, {"year": "1935", "text": "<PERSON>, American football player (d. 1977)", "html": "1935 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player (d. 1977)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player (d. 1977)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1935", "text": "<PERSON>, Portuguese-born British visual artist (d. 2022)", "html": "1935 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Portuguese-born British visual artist (d. 2022)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Portuguese-born British visual artist (d. 2022)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1936", "text": "<PERSON>, American illustrator", "html": "1936 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American illustrator", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American illustrator", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Sal_<PERSON>ma"}]}, {"year": "1937", "text": "<PERSON>, Sierra Leonean soldier and politician, 2nd President of Sierra Leone (d. 2003)", "html": "1937 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Sierra Leonean soldier and politician, 2nd <a href=\"https://wikipedia.org/wiki/President_of_Sierra_Leone\" title=\"President of Sierra Leone\">President of Sierra Leone</a> (d. 2003)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Sierra Leonean soldier and politician, 2nd <a href=\"https://wikipedia.org/wiki/President_of_Sierra_Leone\" title=\"President of Sierra Leone\">President of Sierra Leone</a> (d. 2003)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "President of Sierra Leone", "link": "https://wikipedia.org/wiki/President_of_Sierra_Leone"}]}, {"year": "1938", "text": "<PERSON>, English-American director and screenwriter", "html": "1938 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English-American director and screenwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English-American director and screenwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1940", "text": "<PERSON><PERSON><PERSON><PERSON>, Irish bishop (d. 2019)", "html": "1940 - <a href=\"https://wikipedia.org/wiki/S%C3%A9amus_<PERSON>garty\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Irish bishop (d. 2019)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/S%C3%A9amus_Hegarty\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Irish bishop (d. 2019)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/S%C3%A9amus_<PERSON>garty"}]}, {"year": "1940", "text": "<PERSON>, English footballer and cricketer (d. 2003)", "html": "1940 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Large\"><PERSON></a>, English footballer and cricketer (d. 2003)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Large\"><PERSON></a>, English footballer and cricketer (d. 2003)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1943", "text": "<PERSON>, Venezuelan baseball player and manager (d. 2005)", "html": "1943 - <a href=\"https://wikipedia.org/wiki/C%C3%A9sar_Guti%C3%A9rrez\" title=\"<PERSON>\"><PERSON></a>, Venezuelan baseball player and manager (d. 2005)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/C%C3%A9sar_Guti%C3%A9rrez\" title=\"<PERSON>\"><PERSON></a>, Venezuelan baseball player and manager (d. 2005)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/C%C3%A9sar_Guti%C3%A9rrez"}]}, {"year": "1943", "text": "<PERSON>, Trinidadian businessman and politician", "html": "1943 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(football_executive)\" title=\"<PERSON> (football executive)\"><PERSON></a>, Trinidadian businessman and politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(football_executive)\" title=\"<PERSON> (football executive)\"><PERSON></a>, Trinidadian businessman and politician", "links": [{"title": "<PERSON> (football executive)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(football_executive)"}]}, {"year": "1944", "text": "<PERSON>, American activist, academic, and author", "html": "1944 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American activist, academic, and author", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American activist, academic, and author", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1944", "text": "<PERSON>, American football coach and criminal", "html": "1944 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football coach and criminal", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football coach and criminal", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1945", "text": "<PERSON>, English cellist (d. 1987)", "html": "1945 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>r%C3%A9\" title=\"<PERSON>\"><PERSON></a>, English cellist (d. 1987)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>r%C3%A9\" title=\"<PERSON>\"><PERSON></a>, English cellist (d. 1987)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>r%C3%A9"}]}, {"year": "1945", "text": "<PERSON>, English race car driver (d. 1985)", "html": "1945 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English race car driver (d. 1985)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English race car driver (d. 1985)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1946", "text": "<PERSON>, American mathematician", "html": "1946 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American mathematician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American mathematician", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1946", "text": "<PERSON>, Portuguese-English director, screenwriter, and playwright", "html": "1946 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Portuguese-English director, screenwriter, and playwright", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Portuguese-English director, screenwriter, and playwright", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1946", "text": "<PERSON>, American journalist and film critic (d. 1999)", "html": "1946 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American journalist and film critic (d. 1999)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American journalist and film critic (d. 1999)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1947", "text": "<PERSON>, French actor and composer (d. 1982)", "html": "1947 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French actor and composer (d. 1982)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French actor and composer (d. 1982)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1947", "text": "<PERSON>, English chemist and academic", "html": "1947 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English chemist and academic", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English chemist and academic", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>don"}]}, {"year": "1947", "text": "<PERSON><PERSON>, 4th Baron <PERSON>, Irish director, producer, and production manager", "html": "1947 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>,_4th_Baron_<PERSON>\" title=\"<PERSON><PERSON>, 4th <PERSON>\"><PERSON><PERSON>, 4th <PERSON></a>, Irish director, producer, and production manager", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>,_4th_Baron_<PERSON>\" title=\"<PERSON><PERSON>, 4th <PERSON>\"><PERSON><PERSON>, 4th <PERSON></a>, Irish director, producer, and production manager", "links": [{"title": "<PERSON><PERSON>, 4th Baron <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>,_4th_Baron_<PERSON>"}]}, {"year": "1947", "text": "<PERSON>, American actor", "html": "1947 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1947", "text": "<PERSON>, French singer-songwriter and actor", "html": "1947 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French singer-songwriter and actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French singer-songwriter and actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1948", "text": "<PERSON><PERSON>, Costa Rican jurist, writer and teacher", "html": "1948 - <a href=\"https://wikipedia.org/wiki/Alda_Facio\" title=\"Alda Facio\"><PERSON><PERSON></a>, Costa Rican jurist, writer and teacher", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Alda_Facio\" title=\"Alda Facio\"><PERSON><PERSON></a>, Costa Rican jurist, writer and teacher", "links": [{"title": "Alda Facio", "link": "https://wikipedia.org/wiki/Alda_Facio"}]}, {"year": "1948", "text": "<PERSON><PERSON>, Canadian rock drummer", "html": "1948 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Canadian rock drummer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Canadian rock drummer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>ng"}]}, {"year": "1949", "text": "<PERSON>, American author", "html": "1949 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1949", "text": "<PERSON>, American actor", "html": "1949 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1950", "text": "<PERSON><PERSON><PERSON>, Austrian lawyer and politician, Governor of Carinthia (d. 2008)", "html": "1950 - <a href=\"https://wikipedia.org/wiki/J%C3%B6rg_Haider\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Austrian lawyer and politician, <a href=\"https://wikipedia.org/wiki/List_of_governors_of_Carinthia\" title=\"List of governors of Carinthia\">Governor of Carinthia</a> (d. 2008)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/J%C3%B6rg_Haider\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Austrian lawyer and politician, <a href=\"https://wikipedia.org/wiki/List_of_governors_of_Carinthia\" title=\"List of governors of Carinthia\">Governor of Carinthia</a> (d. 2008)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/J%C3%B6rg_Haider"}, {"title": "List of governors of Carinthia", "link": "https://wikipedia.org/wiki/List_of_governors_of_Carinthia"}]}, {"year": "1950", "text": "<PERSON>, Czech ice hockey player and coach (d. 2004)", "html": "1950 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Czech ice hockey player and coach (d. 2004)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Czech ice hockey player and coach (d. 2004)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1950", "text": "<PERSON>, American football player", "html": "1950 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1951", "text": "<PERSON>, Australian guitarist, songwriter, and producer", "html": "1951 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(Australian_musician)\" title=\"<PERSON> (Australian musician)\"><PERSON></a>, Australian guitarist, songwriter, and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(Australian_musician)\" title=\"<PERSON> (Australian musician)\"><PERSON></a>, Australian guitarist, songwriter, and producer", "links": [{"title": "<PERSON> (Australian musician)", "link": "https://wikipedia.org/wiki/<PERSON>_(Australian_musician)"}]}, {"year": "1951", "text": "<PERSON>, American singer-songwriter and bass player (d. 2010)", "html": "1951 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and bass player (d. 2010)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and bass player (d. 2010)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1951", "text": "<PERSON>, English economist and academic", "html": "1951 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English economist and academic", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English economist and academic", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1952", "text": "<PERSON>, American basketball player", "html": "1952 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(basketball)\" title=\"<PERSON> (basketball)\"><PERSON></a>, American basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(basketball)\" title=\"<PERSON> (basketball)\"><PERSON></a>, American basketball player", "links": [{"title": "<PERSON> (basketball)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(basketball)"}]}, {"year": "1953", "text": "<PERSON><PERSON>, Micronesian politician, 7th Vice President of the Federated States of Micronesia", "html": "1953 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Micronesian politician, 7th <a href=\"https://wikipedia.org/wiki/Vice_President_of_the_Federated_States_of_Micronesia\" title=\"Vice President of the Federated States of Micronesia\">Vice President of the Federated States of Micronesia</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Micronesian politician, 7th <a href=\"https://wikipedia.org/wiki/Vice_President_of_the_Federated_States_of_Micronesia\" title=\"Vice President of the Federated States of Micronesia\">Vice President of the Federated States of Micronesia</a>", "links": [{"title": "Alik L. Alik", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>"}, {"title": "Vice President of the Federated States of Micronesia", "link": "https://wikipedia.org/wiki/Vice_President_of_the_Federated_States_of_Micronesia"}]}, {"year": "1953", "text": "<PERSON>, Danish politician and diplomat, 39th Prime Minister of Denmark", "html": "1953 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Danish politician and diplomat, 39th <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Denmark\" title=\"Prime Minister of Denmark\">Prime Minister of Denmark</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Danish politician and diplomat, 39th <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Denmark\" title=\"Prime Minister of Denmark\">Prime Minister of Denmark</a>", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "Prime Minister of Denmark", "link": "https://wikipedia.org/wiki/Prime_Minister_of_Denmark"}]}, {"year": "1953", "text": "<PERSON><PERSON>, American singer-songwriter and guitarist", "html": "1953 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American singer-songwriter and guitarist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American singer-songwriter and guitarist", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1954", "text": "<PERSON>, Australian cricketer", "html": "1954 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian cricketer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian cricketer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1955", "text": "<PERSON>, Dutch-American guitarist, songwriter, and producer (d. 2020)", "html": "1955 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch-American guitarist, songwriter, and producer (d. 2020)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch-American guitarist, songwriter, and producer (d. 2020)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1957", "text": "Road Warrior <PERSON>, American wrestler (d. 2003)", "html": "1957 - <a href=\"https://wikipedia.org/wiki/Road_Warrior_Hawk\" title=\"Road Warrior Hawk\">Road Warrior Hawk</a>, American wrestler (d. 2003)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Road_Warrior_Hawk\" title=\"Road Warrior Hawk\">Road Warrior Hawk</a>, American wrestler (d. 2003)", "links": [{"title": "Road Warrior Hawk", "link": "https://wikipedia.org/wiki/Road_Warrior_Hawk"}]}, {"year": "1958", "text": "<PERSON>, American singer-songwriter", "html": "1958 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1958", "text": "<PERSON>, American comedian, actress, and talk show host", "html": "1958 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American comedian, actress, and talk show host", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American comedian, actress, and talk show host", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1960", "text": "<PERSON>, American musician", "html": "1960 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American musician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American musician", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1961", "text": "<PERSON>, Canadian ice hockey player and coach", "html": "1961 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player and coach", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1961", "text": "<PERSON>, American singer-songwriter and guitarist", "html": "1961 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and guitarist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and guitarist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1962", "text": "<PERSON>, Chinese-Australian painter, sculptor, and photographer", "html": "1962 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Chinese-Australian painter, sculptor, and photographer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Chinese-Australian painter, sculptor, and photographer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1962", "text": "<PERSON>, Australian cricketer", "html": "1962 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian cricketer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian cricketer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_May"}]}, {"year": "1962", "text": "<PERSON>, Argentine footballer and manager", "html": "1962 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Argentine footballer and manager", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Argentine footballer and manager", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_Rug<PERSON>i"}]}, {"year": "1963", "text": "<PERSON><PERSON>, British DJ and music producer", "html": "1963 - <a href=\"https://wikipedia.org/wiki/Jazzie_B\" title=\"Jazzie B\"><PERSON><PERSON></a>, British DJ and music producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Jazzie_B\" title=\"Jazzie B\"><PERSON><PERSON></a>, British DJ and music producer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1963", "text": "<PERSON>, Portuguese footballer and manager", "html": "1963 - <a href=\"https://wikipedia.org/wiki/Jos%C3%A9_<PERSON><PERSON><PERSON>\" title=\"<PERSON>\"><PERSON></a>, Portuguese footballer and manager", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Jos%C3%A9_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Portuguese footballer and manager", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Jos%C3%A9_<PERSON><PERSON><PERSON><PERSON>"}]}, {"year": "1963", "text": "<PERSON>, Australian footballer, cricketer, and sportscaster", "html": "1963 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%27Donnell\" title=\"<PERSON>\"><PERSON></a>, Australian footballer, cricketer, and sportscaster", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%27Donnell\" title=\"<PERSON>\"><PERSON></a>, Australian footballer, cricketer, and sportscaster", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Simon_O%27Donnell"}]}, {"year": "1963", "text": "<PERSON>, English footballer and manager", "html": "1963 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English footballer and manager", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English footballer and manager", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1963", "text": "<PERSON>, English singer-songwriter and guitarist", "html": "1963 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English singer-songwriter and guitarist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English singer-songwriter and guitarist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1964", "text": "<PERSON>, Scottish businessman", "html": "1964 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish businessman", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish businessman", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1964", "text": "<PERSON>, American-Canadian actor", "html": "1964 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American-Canadian actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American-Canadian actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1965", "text": "<PERSON>, American politician, 55th Speaker of the United States House of Representatives", "html": "1965 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American politician, 55th <a href=\"https://wikipedia.org/wiki/Speaker_of_the_United_States_House_of_Representatives\" title=\"Speaker of the United States House of Representatives\">Speaker of the United States House of Representatives</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American politician, 55th <a href=\"https://wikipedia.org/wiki/Speaker_of_the_United_States_House_of_Representatives\" title=\"Speaker of the United States House of Representatives\">Speaker of the United States House of Representatives</a>", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Speaker of the United States House of Representatives", "link": "https://wikipedia.org/wiki/Speaker_of_the_United_States_House_of_Representatives"}]}, {"year": "1965", "text": "<PERSON>, Swedish businessman and politician", "html": "1965 - <a href=\"https://wikipedia.org/wiki/<PERSON>_%C3%96stros\" title=\"<PERSON>\"><PERSON></a>, Swedish businessman and politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_%C3%96stros\" title=\"<PERSON>\"><PERSON></a>, Swedish businessman and politician", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_%C3%96stros"}]}, {"year": "1965", "text": "<PERSON>, Russian gymnast and coach", "html": "1965 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian gymnast and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian gymnast and coach", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1966", "text": "<PERSON><PERSON><PERSON><PERSON>, Japanese baseball player and sportscaster", "html": "1966 - <a href=\"https://wikipedia.org/wiki/Ka<PERSON><PERSON>ge_Nagashima\" title=\"<PERSON><PERSON><PERSON><PERSON> Nagashima\"><PERSON><PERSON><PERSON><PERSON></a>, Japanese baseball player and sportscaster", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Ka<PERSON><PERSON><PERSON>_Nagashima\" title=\"<PERSON><PERSON><PERSON><PERSON> Nagashima\"><PERSON><PERSON><PERSON><PERSON></a>, Japanese baseball player and sportscaster", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Kazushige_Nagashima"}]}, {"year": "1967", "text": "<PERSON>, American comedian, actor, and writer", "html": "1967 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American comedian, actor, and writer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American comedian, actor, and writer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1967", "text": "<PERSON><PERSON><PERSON>, Russian chef and businessman", "html": "1967 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Russian chef and businessman", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Russian chef and businessman", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1967", "text": "<PERSON>, English businessman, co-founded Internet Movie Database", "html": "1967 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English businessman, co-founded <a href=\"https://wikipedia.org/wiki/Internet_Movie_Database\" class=\"mw-redirect\" title=\"Internet Movie Database\">Internet Movie Database</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English businessman, co-founded <a href=\"https://wikipedia.org/wiki/Internet_Movie_Database\" class=\"mw-redirect\" title=\"Internet Movie Database\">Internet Movie Database</a>", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Internet Movie Database", "link": "https://wikipedia.org/wiki/Internet_Movie_Database"}]}, {"year": "1968", "text": "<PERSON>, Brazilian singer-songwriter, film director, and actor (d. 2015)", "html": "1968 - <a href=\"https://wikipedia.org/wiki/Jupiter_Apple\" title=\"Jupiter Apple\"><PERSON> Apple</a>, Brazilian singer-songwriter, film director, and actor (d. 2015)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Jupiter_Apple\" title=\"Jupiter Apple\"><PERSON></a>, Brazilian singer-songwriter, film director, and actor (d. 2015)", "links": [{"title": "Jupiter Apple", "link": "https://wikipedia.org/wiki/Jupiter_Apple"}]}, {"year": "1969", "text": "<PERSON>, Greek-Romanian basketball player and coach", "html": "1969 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Greek-Romanian basketball player and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Greek-Romanian basketball player and coach", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1970", "text": "<PERSON>, American singer-songwriter and producer", "html": "1970 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and producer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1972", "text": "<PERSON>, American actor", "html": "1972 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1973", "text": "<PERSON>, American actress", "html": "1973 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1973", "text": "<PERSON><PERSON>, Canadian artistic gymnast", "html": "1973 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Canadian artistic gymnast", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Canadian artistic gymnast", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1973", "text": "<PERSON><PERSON>, French actor, director, and screenwriter", "html": "1973 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, French actor, director, and screenwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, French actor, director, and screenwriter", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1973", "text": "<PERSON>, Northern Irish footballer and manager", "html": "1973 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Northern Irish footballer and manager", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Northern Irish footballer and manager", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1973", "text": "<PERSON><PERSON>, Japanese author and illustrator", "html": "1973 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Japanese author and illustrator", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Japanese author and illustrator", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1976", "text": "<PERSON>, French actor", "html": "1976 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1977", "text": "<PERSON>, American basketball player", "html": "1977 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1977", "text": "<PERSON>, American tennis player and coach", "html": "1977 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American tennis player and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American tennis player and coach", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1978", "text": "<PERSON><PERSON><PERSON>, Dominican baseball player", "html": "1978 - <a href=\"https://wikipedia.org/wiki/Esteban_Germ%C3%A1n\" title=\"Este<PERSON> G<PERSON>mán\"><PERSON><PERSON><PERSON></a>, Dominican baseball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Esteban_Germ%C3%A1n\" title=\"Este<PERSON> G<PERSON>mán\"><PERSON><PERSON><PERSON></a>, Dominican baseball player", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Esteban_Germ%C3%A1n"}]}, {"year": "1978", "text": "<PERSON><PERSON>, American tennis player and sportscaster", "html": "1978 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American tennis player and sportscaster", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American tennis player and sportscaster", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1978", "text": "<PERSON>, American baseball player", "html": "1978 - <a href=\"https://wikipedia.org/wiki/Andr%C3%A9<PERSON>_<PERSON>_(baseball)\" title=\"<PERSON> (baseball)\"><PERSON></a>, American baseball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Andr%C3%A9<PERSON>_<PERSON>_(baseball)\" title=\"<PERSON> (baseball)\"><PERSON></a>, American baseball player", "links": [{"title": "<PERSON> (baseball)", "link": "https://wikipedia.org/wiki/Andr%C3%A9<PERSON>_<PERSON>_(baseball)"}]}, {"year": "1979", "text": "<PERSON>, American actress", "html": "1979 - <a href=\"https://wikipedia.org/wiki/1979\" title=\"1979\">1979</a> - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/1979\" title=\"1979\">1979</a> - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress", "links": [{"title": "1979", "link": "https://wikipedia.org/wiki/1979"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Sara_<PERSON>"}]}, {"year": "1981", "text": "<PERSON>, Mexican footballer", "html": "1981 - <a href=\"https://wikipedia.org/wiki/Jos%C3%A9_de_Jes%C3%BAs_Corona\" title=\"<PERSON> Corona\"><PERSON></a>, Mexican footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Jos%C3%A9_de_Jes%C3%BAs_Corona\" title=\"<PERSON> Je<PERSON> Corona\"><PERSON></a>, Mexican footballer", "links": [{"title": "<PERSON> Jesús Corona", "link": "https://wikipedia.org/wiki/Jos%C3%A9_de_Jes%C3%BAs_Corona"}]}, {"year": "1981", "text": "<PERSON>, Venezuelan violinist, composer, and conductor", "html": "1981 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Venezuelan violinist, composer, and conductor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Venezuelan violinist, composer, and conductor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1981", "text": "<PERSON>, Argentine cyclist", "html": "1981 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A9_Haedo\" title=\"<PERSON>\"><PERSON></a>, Argentine cyclist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A9_Haedo\" title=\"<PERSON>\"><PERSON></a>, Argentine cyclist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A9_<PERSON><PERSON>o"}]}, {"year": "1981", "text": "<PERSON>, Irish actor", "html": "1981 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%27Donoghue\" title=\"<PERSON>\"><PERSON></a>, Irish actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%27Donoghue\" title=\"<PERSON>\"><PERSON></a>, Irish actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_O%27Donoghue"}]}, {"year": "1983", "text": "<PERSON><PERSON>, Finnish footballer", "html": "1983 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Finnish footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Finnish footballer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1983", "text": "<PERSON>, American ice hockey player", "html": "1983 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American ice hockey player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American ice hockey player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1984", "text": "<PERSON>, Australian rugby league player", "html": "1984 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian rugby league player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian rugby league player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1984", "text": "<PERSON>, Scottish footballer", "html": "1984 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1984", "text": "<PERSON><PERSON>, Chinese swimmer", "html": "1984 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Chinese swimmer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Chinese swimmer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1985", "text": "<PERSON>, English rower", "html": "1985 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English rower", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English rower", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1986", "text": "<PERSON>, American basketball player", "html": "1986 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1986", "text": "<PERSON>, South Korean singer, songwriter, actor, and director", "html": "1986 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>-joong\" title=\"<PERSON>joon<PERSON>\"><PERSON>oon<PERSON></a>, South Korean singer, songwriter, actor, and director", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>-joong\" title=\"<PERSON>joon<PERSON>\"><PERSON>oon<PERSON></a>, South Korean singer, songwriter, actor, and director", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>-joong"}]}, {"year": "1986", "text": "<PERSON><PERSON><PERSON>, French-Malian footballer", "html": "1986 - <a href=\"https://wikipedia.org/wiki/Must<PERSON><PERSON>_<PERSON>bar%C3%A9\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, French-Malian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>%C3%A9\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, French-Malian footballer", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Mustapha_Yatabar%C3%A9"}]}, {"year": "1987", "text": "<PERSON>, Italian footballer", "html": "1987 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1987", "text": "<PERSON><PERSON><PERSON>, Dominican baseball player", "html": "1987 - <a href=\"https://wikipedia.org/wiki/H%C3%A9ctor_Noes%C3%AD\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Dominican baseball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/H%C3%A9ctor_Noes%C3%AD\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Dominican baseball player", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/H%C3%A9ctor_Noes%C3%AD"}]}, {"year": "1988", "text": "<PERSON>, American football player", "html": "1988 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(American_football)\" title=\"<PERSON> (American football)\"><PERSON></a>, American football player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(American_football)\" title=\"<PERSON> (American football)\"><PERSON></a>, American football player", "links": [{"title": "<PERSON> (American football)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(American_football)"}]}, {"year": "1988", "text": "<PERSON><PERSON>, Greek high jumper", "html": "1988 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Greek high jumper", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Greek high jumper", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1989", "text": "<PERSON><PERSON><PERSON>, American basketball player", "html": "1989 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American basketball player", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1989", "text": "<PERSON>, American figure skater", "html": "1989 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American figure skater", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American figure skater", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1989", "text": "<PERSON><PERSON>, American football player", "html": "1989 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American football player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American football player", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1990", "text": "<PERSON>, American football player", "html": "1990 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1990", "text": "<PERSON>, Mexican race car driver", "html": "1990 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A9rez\" title=\"<PERSON>\"><PERSON></a>, Mexican race car driver", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A9<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Mexican race car driver", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Sergio_P%C3%A9rez"}]}, {"year": "1990", "text": "<PERSON>, Slovak professional cyclist", "html": "1990 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Slovak professional cyclist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Slovak professional cyclist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1991", "text": "<PERSON><PERSON><PERSON>, Argentine footballer", "html": "1991 - <a href=\"https://wikipedia.org/wiki/Esteban_Andrada\" title=\"Esteban Andrada\"><PERSON><PERSON><PERSON></a>, Argentine footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Esteban_Andrada\" title=\"Esteban Andrada\"><PERSON><PERSON><PERSON></a>, Argentine footballer", "links": [{"title": "Esteban Andrada", "link": "https://wikipedia.org/wiki/E<PERSON><PERSON>_Andrada"}]}, {"year": "1991", "text": "<PERSON><PERSON><PERSON>, Italian-American basketball player", "html": "1991 - <a href=\"https://wikipedia.org/wiki/Nicol%C3%B2_<PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Italian-American basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Nicol%C3%B2_<PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Italian-American basketball player", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Nicol%C3%B2_<PERSON>li"}]}, {"year": "1991", "text": "<PERSON>, Brazilian footballer", "html": "1991 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Brazilian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Brazilian footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1991", "text": "<PERSON><PERSON>, American football player", "html": "1991 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>%27o\" title=\"<PERSON><PERSON>o\"><PERSON><PERSON></a>, American football player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>%27o\" title=\"<PERSON><PERSON>o\"><PERSON><PERSON></a>, American football player", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Manti_Te%27o"}]}, {"year": "1992", "text": "<PERSON>, American wrestler", "html": "1992 - <a href=\"https://wikipedia.org/wiki/Mercedes_Mon%C3%A9\" title=\"Mercedes Moné\"><PERSON></a>, American wrestler", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Mercedes_Mon%C3%A9\" title=\"Mercedes Moné\"><PERSON></a>, American wrestler", "links": [{"title": "Mercedes Moné", "link": "https://wikipedia.org/wiki/Mercedes_Mon%C3%A9"}]}, {"year": "1993", "text": "<PERSON>, Colombian footballer", "html": "1993 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Colombian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Colombian footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1993", "text": "<PERSON>, Scottish footballer", "html": "1993 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Lana_<PERSON>lland"}]}, {"year": "1993", "text": "<PERSON>, Canadian-Slovenian basketball player", "html": "1993 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian-Slovenian basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian-Slovenian basketball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1993", "text": "<PERSON>, British racing driver", "html": "1993 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, British racing driver", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, British racing driver", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1993", "text": "<PERSON><PERSON><PERSON>, French footballer", "html": "1993 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, French footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, French footballer", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>vin"}]}, {"year": "1994", "text": "<PERSON><PERSON><PERSON>, American basketball player", "html": "1994 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American basketball player", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1995", "text": "<PERSON><PERSON><PERSON>, Cameroonian footballer", "html": "1995 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Cameroonian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Cameroonian footballer", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>"}]}, {"year": "1995", "text": "<PERSON><PERSON>, New Zealand rugby league player", "html": "1995 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_(rugby_league,_born_1995)\" title=\"<PERSON><PERSON> (rugby league, born 1995)\"><PERSON><PERSON></a>, New Zealand rugby league player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_(rugby_league,_born_1995)\" title=\"<PERSON><PERSON> (rugby league, born 1995)\"><PERSON><PERSON></a>, New Zealand rugby league player", "links": [{"title": "<PERSON><PERSON> (rugby league, born 1995)", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_Kat<PERSON>_(rugby_league,_born_1995)"}]}, {"year": "1996", "text": "<PERSON><PERSON><PERSON>, Belgian footballer", "html": "1996 - <a href=\"https://wikipedia.org/wiki/Zakaria_Bakkali\" title=\"Zakaria Bakkali\">Zaka<PERSON></a>, Belgian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Zakaria_Bakkali\" title=\"Zakaria Bakkali\"><PERSON><PERSON><PERSON></a>, Belgian footballer", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Zakaria_Bakkali"}]}, {"year": "1996", "text": "<PERSON><PERSON>, South Korean football player", "html": "1996 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>e-chan\" title=\"<PERSON><PERSON>-chan\"><PERSON><PERSON>-<PERSON>an</a>, South Korean football player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>e-chan\" title=\"<PERSON><PERSON>-chan\"><PERSON><PERSON>-<PERSON>an</a>, South Korean football player", "links": [{"title": "<PERSON><PERSON>an", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>-chan"}]}, {"year": "1997", "text": "<PERSON><PERSON><PERSON>, German-born American soccer player", "html": "1997 - <a href=\"https://wikipedia.org/wiki/Gedion_Zelalem\" title=\"Gedion Zelalem\"><PERSON><PERSON><PERSON> Zelalem</a>, German-born American soccer player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Gedion_Zelalem\" title=\"Gedion Zelalem\"><PERSON><PERSON><PERSON></a>, German-born American soccer player", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Gedion_Zelalem"}]}, {"year": "1998", "text": "<PERSON><PERSON>, South Korean singer and actor. (d. 2023)", "html": "1998 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, South Korean singer and actor. (d. 2023)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, South Korean singer and actor. (d. 2023)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>bin"}]}, {"year": "1999", "text": "<PERSON>, Argentine footballer", "html": "1999 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Argentine footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Argentine footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1999", "text": "<PERSON>, American football player", "html": "1999 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2000", "text": "<PERSON><PERSON>, Spanish actress", "html": "2000 - <a href=\"https://wikipedia.org/wiki/Ester_Exp%C3%B3sito\" title=\"<PERSON><PERSON> Expósito\"><PERSON><PERSON></a>, Spanish actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Ester_Exp%C3%B3sito\" title=\"<PERSON><PERSON> Expósito\"><PERSON><PERSON></a>, Spanish actress", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Ester_Exp%C3%B3sito"}]}, {"year": "2000", "text": "<PERSON>, American basketball player", "html": "2000 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2001", "text": "<PERSON><PERSON><PERSON>, Welsh artistic gymnast", "html": "2001 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Welsh artistic gymnast", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Welsh artistic gymnast", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>van"}]}, {"year": "2001", "text": "<PERSON>, American basketball player", "html": "2001 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "2002", "text": "<PERSON><PERSON>, Russian tennis player", "html": "2002 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Russian tennis player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Russian tennis player", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON><PERSON>"}]}, {"year": "2009", "text": "<PERSON><PERSON><PERSON>, American actress", "html": "2009 - <a href=\"https://wikipedia.org/wiki/Ya<PERSON><PERSON>_<PERSON><PERSON><PERSON>\" title=\"Ya<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Ya<PERSON><PERSON>_<PERSON><PERSON><PERSON>\" title=\"Ya<PERSON>a <PERSON>\">Ya<PERSON><PERSON></a>, American actress", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Ya<PERSON><PERSON>_<PERSON><PERSON>in"}]}, {"year": "2009", "text": "The Suleman octuplets", "html": "2009 - The <a href=\"https://wikipedia.org/wiki/Suleman_octuplets\" title=\"Suleman octuplets\">Suleman octuplets</a>", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Suleman_octuplets\" title=\"Suleman octuplets\">Suleman octuplets</a>", "links": [{"title": "<PERSON>eman octuplets", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_octuplets"}]}], "Deaths": [{"year": "738", "text": "<PERSON> of Dailam, Syrian monk and saint (b. 660)", "html": "738 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_Dailam\" title=\"<PERSON> of Dailam\"><PERSON> of Dailam</a>, Syrian monk and saint (b. 660)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_Dailam\" title=\"<PERSON> of Dailam\"><PERSON> of Dailam</a>, Syrian monk and saint (b. 660)", "links": [{"title": "<PERSON> of Dailam", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1390", "text": "<PERSON><PERSON><PERSON>, Count of Holstein-Kiel (b.c 1327)", "html": "1390 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>,_Count_of_Holstein-Kiel\" title=\"<PERSON><PERSON><PERSON>, Count of Holstein-Kiel\"><PERSON><PERSON><PERSON>, Count of Holstein-Kiel</a> (b.c <a href=\"https://wikipedia.org/wiki/1327\" title=\"1327\">1327</a>)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>,_Count_of_Holstein-Kiel\" title=\"<PERSON><PERSON><PERSON>, Count of Holstein-Kiel\"><PERSON><PERSON><PERSON>, Count of Holstein-Kiel</a> (b.c <a href=\"https://wikipedia.org/wiki/1327\" title=\"1327\">1327</a>)", "links": [{"title": "<PERSON><PERSON><PERSON>, Count of Holstein-Kiel", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>,_Count_of_Holstein-Kiel"}, {"title": "1327", "link": "https://wikipedia.org/wiki/1327"}]}, {"year": "1567", "text": "<PERSON>, English courtier and diplomat (b. 1497)", "html": "1567 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English courtier and diplomat (b. 1497)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English courtier and diplomat (b. 1497)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1620", "text": "<PERSON>, ruler of Mewar (b. 1559)", "html": "1620 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON> I</a>, ruler of Mewar (b. 1559)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON> I</a>, ruler of Mewar (b. 1559)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1630", "text": "<PERSON>, English mathematician and astronomer (b. 1556)", "html": "1630 - <a href=\"https://wikipedia.org/wiki/<PERSON>(mathematician)\" title=\"<PERSON> (mathematician)\"><PERSON></a>, English mathematician and astronomer (b. 1556)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>(mathematician)\" title=\"<PERSON> (mathematician)\"><PERSON></a>, English mathematician and astronomer (b. 1556)", "links": [{"title": "<PERSON> (mathematician)", "link": "https://wikipedia.org/wiki/<PERSON>_(mathematician)"}]}, {"year": "1641", "text": "<PERSON>, English lawyer (b. 1562)", "html": "1641 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(attorney-general)\" title=\"<PERSON> (attorney-general)\"><PERSON></a>, English lawyer (b. 1562)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(attorney-general)\" title=\"<PERSON> (attorney-general)\"><PERSON></a>, English lawyer (b. 1562)", "links": [{"title": "<PERSON> (attorney-general)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(attorney-general)"}]}, {"year": "1697", "text": "<PERSON>, Danish mathematician and theorist (b. 1640)", "html": "1697 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Danish mathematician and theorist (b. 1640)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Danish mathematician and theorist (b. 1640)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1744", "text": "<PERSON>, Austrian field marshal (b. 1683)", "html": "1744 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>%C3%BCller\" title=\"<PERSON>\"><PERSON></a>, Austrian field marshal (b. 1683)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>%C3%BCller\" title=\"<PERSON>\"><PERSON></a>, Austrian field marshal (b. 1683)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>%C3%BCller"}]}, {"year": "1750", "text": "<PERSON>, Dutch philologist and academic (b. 1686)", "html": "1750 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch philologist and academic (b. 1686)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch philologist and academic (b. 1686)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1795", "text": "<PERSON>, German harpsichord player and composer (b. 1732)", "html": "1795 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German <a href=\"https://wikipedia.org/wiki/Harpsichord\" title=\"Harpsichord\">harpsichord</a> player and composer (b. 1732)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German <a href=\"https://wikipedia.org/wiki/Harpsichord\" title=\"Harpsichord\">harpsichord</a> player and composer (b. 1732)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>"}, {"title": "Harpsichord", "link": "https://wikipedia.org/wiki/Harpsichord"}]}, {"year": "1799", "text": "<PERSON>, Scottish general (b. 1722)", "html": "1799 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(British_Army_officer)\" title=\"<PERSON> (British Army officer)\"><PERSON></a>, Scottish general (b. 1722)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(British_Army_officer)\" title=\"<PERSON> (British Army officer)\"><PERSON></a>, Scottish general (b. 1722)", "links": [{"title": "<PERSON> (British Army officer)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(British_Army_officer)"}]}, {"year": "1814", "text": "<PERSON>, Portuguese prelate and antiquarian (b. 1724)", "html": "1814 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>%C3%A1culo\" title=\"Manuel do Cenáculo\"><PERSON>lo</a>, Portuguese prelate and antiquarian (b. 1724)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>%C3%A1culo\" title=\"Manuel do Cenáculo\"><PERSON>lo</a>, Portuguese prelate and antiquarian (b. 1724)", "links": [{"title": "<PERSON> do Cenáculo", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>%C3%A1culo"}]}, {"year": "1823", "text": "<PERSON>, English physician and immunologist, creator of the smallpox vaccine (b. 1749)", "html": "1823 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English physician and immunologist, creator of the <a href=\"https://wikipedia.org/wiki/Smallpox_vaccine\" title=\"Smallpox vaccine\">smallpox vaccine</a> (b. 1749)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English physician and immunologist, creator of the <a href=\"https://wikipedia.org/wiki/Smallpox_vaccine\" title=\"Smallpox vaccine\">smallpox vaccine</a> (b. 1749)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Smallpox vaccine", "link": "https://wikipedia.org/wiki/Smallpox_vaccine"}]}, {"year": "1824", "text": "<PERSON><PERSON><PERSON><PERSON><PERSON>, French painter and lithographer (b. 1791)", "html": "1824 - <a href=\"https://wikipedia.org/wiki/Th%C3%A9odore_G%C3%A9ricault\" title=\"Th<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON><PERSON></a>, French painter and lithographer (b. 1791)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Th%C3%A9odore_G%C3%A9ricault\" title=\"Théod<PERSON>\"><PERSON><PERSON><PERSON><PERSON><PERSON></a>, French painter and lithographer (b. 1791)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Th%C3%A9odore_G%C3%A9ricault"}]}, {"year": "1830", "text": "<PERSON><PERSON><PERSON>, Maltese politician (b. 1765)", "html": "1830 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Maltese politician (b. 1765)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Maltese politician (b. 1765)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1849", "text": "<PERSON>, English poet, playwright, and physician (b. 1803)", "html": "1849 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English poet, playwright, and physician (b. 1803)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English poet, playwright, and physician (b. 1803)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1855", "text": "<PERSON><PERSON>, French poet and translator (b. 1808)", "html": "1855 - <a href=\"https://wikipedia.org/wiki/G%C3%A9rar<PERSON>_<PERSON>_<PERSON>val\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, French poet and translator (b. 1808)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/G%C3%A9rar<PERSON>_<PERSON>_<PERSON>val\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, French poet and translator (b. 1808)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/G%C3%A9rard_de_<PERSON>val"}]}, {"year": "1860", "text": "<PERSON><PERSON>-<PERSON><PERSON>, opera singer (b. 1804)", "html": "1860 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>hr%C3%B6der-Devrient\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, opera singer (b. 1804)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>hr%C3%B6der-Devrient\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, opera singer (b. 1804)", "links": [{"title": "<PERSON><PERSON>-<PERSON>", "link": "https://wikipedia.org/wiki/Wilhelmine_Schr%C3%B6der-Devrient"}]}, {"year": "1869", "text": "<PERSON>, English soldier; Victoria Cross recipient (b. 1846)", "html": "1869 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English soldier; <a href=\"https://wikipedia.org/wiki/Victoria_Cross\" title=\"Victoria Cross\">Victoria Cross</a> recipient (b. 1846)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English soldier; <a href=\"https://wikipedia.org/wiki/Victoria_Cross\" title=\"Victoria Cross\">Victoria Cross</a> recipient (b. 1846)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "Victoria Cross", "link": "https://wikipedia.org/wiki/Victoria_Cross"}]}, {"year": "1885", "text": "<PERSON>, English-Australian physician and engineer (b. 1806)", "html": "1885 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English-Australian physician and engineer (b. 1806)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English-Australian physician and engineer (b. 1806)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1885", "text": "<PERSON>, English general and politician (b. 1833)", "html": "1885 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English general and politician (b. 1833)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English general and politician (b. 1833)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1886", "text": "<PERSON>, American general and politician (b. 1807)", "html": "1886 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American general and politician (b. 1807)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American general and politician (b. 1807)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1887", "text": "<PERSON><PERSON>, one of the first female Indian physicians (b. 1865)", "html": "1887 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, one of the first female Indian physicians (b. 1865)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, one of the first female Indian physicians (b. 1865)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>i"}]}, {"year": "1891", "text": "<PERSON><PERSON>, German engineer, invented the Internal combustion engine (b. 1833)", "html": "1891 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, German engineer, invented the <a href=\"https://wikipedia.org/wiki/Internal_combustion_engine\" title=\"Internal combustion engine\">Internal combustion engine</a> (b. 1833)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, German engineer, invented the <a href=\"https://wikipedia.org/wiki/Internal_combustion_engine\" title=\"Internal combustion engine\">Internal combustion engine</a> (b. 1833)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}, {"title": "Internal combustion engine", "link": "https://wikipedia.org/wiki/Internal_combustion_engine"}]}, {"year": "1893", "text": "<PERSON><PERSON>, American general (b. 1819)", "html": "1893 - <a href=\"https://wikipedia.org/wiki/A<PERSON>_Doubleday\" title=\"Abner Doubleday\"><PERSON><PERSON> Double<PERSON></a>, American general (b. 1819)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_Doubleday\" title=\"Abner Doubleday\"><PERSON><PERSON> Doubleday</a>, American general (b. 1819)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_Doubleday"}]}, {"year": "1895", "text": "<PERSON>, English mathematician and academic (b. 1825)", "html": "1895 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English mathematician and academic (b. 1825)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English mathematician and academic (b. 1825)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1896", "text": "<PERSON>, American educator, school administrator, newspaper editor, poet, and essayist (b. 1867)", "html": "1896 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(poet)\" title=\"<PERSON> (poet)\"><PERSON></a>, American educator, school administrator, newspaper editor, poet, and essayist (b. 1867)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(poet)\" title=\"<PERSON> (poet)\"><PERSON></a>, American educator, school administrator, newspaper editor, poet, and essayist (b. 1867)", "links": [{"title": "<PERSON> (poet)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(poet)"}]}, {"year": "1904", "text": "<PERSON><PERSON><PERSON>, English businessman (b. 1846)", "html": "1904 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, English businessman (b. 1846)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, English businessman (b. 1846)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1920", "text": "<PERSON>, French painter and author (b. 1898)", "html": "1920 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A9buterne\" title=\"<PERSON>\"><PERSON></a>, French painter and author (b. 1898)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A9but<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French painter and author (b. 1898)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Jeanne_<PERSON>%C3%A9buterne"}]}, {"year": "1932", "text": "<PERSON>, Jr., American businessman, founded the Wrigley Company (b. 1861)", "html": "1932 - <a href=\"https://wikipedia.org/wiki/<PERSON>,_Jr.\" class=\"mw-redirect\" title=\"<PERSON>, Jr.\"><PERSON>, Jr.</a>, American businessman, founded the <a href=\"https://wikipedia.org/wiki/Wrigley_Company\" title=\"Wrigley Company\">Wrigley Company</a> (b. 1861)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>,_Jr.\" class=\"mw-redirect\" title=\"<PERSON>, Jr.\"><PERSON>, Jr.</a>, American businessman, founded the <a href=\"https://wikipedia.org/wiki/Wrigley_Company\" title=\"Wrigley Company\">Wrigley Company</a> (b. 1861)", "links": [{"title": "<PERSON>, Jr.", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Jr."}, {"title": "Wrigley Company", "link": "https://wikipedia.org/wiki/Wrigley_Company"}]}, {"year": "1943", "text": "<PERSON>, American sociologist and eugenicist (b. 1880)", "html": "1943 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American sociologist and eugenicist (b. 1880)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American sociologist and eugenicist (b. 1880)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1943", "text": "<PERSON>, Russian botanist and geneticist (b. 1887)", "html": "1943 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian botanist and geneticist (b. 1887)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian botanist and geneticist (b. 1887)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1946", "text": "<PERSON><PERSON><PERSON>, Dutch-American astronomer and academic (b. 1884)", "html": "1946 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Dutch-American astronomer and academic (b. 1884)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Dutch-American astronomer and academic (b. 1884)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1947", "text": "<PERSON>, American soprano and actress (b. 1898)", "html": "1947 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American soprano and actress (b. 1898)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American soprano and actress (b. 1898)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1947", "text": "India adopted its constitution and transitioned into a republic on this day.", "html": "1947 - <a href=\"https://wikipedia.org/wiki/India\" title=\"India\">India</a> adopted its <a href=\"https://wikipedia.org/wiki/Constitution_of_India\" title=\"Constitution of India\">constitution</a> and transitioned into a <a href=\"https://wikipedia.org/wiki/Republic\" title=\"Republic\">republic</a> on <a href=\"https://wikipedia.org/wiki/Republic_Day_(India)\" title=\"Republic Day (India)\">this day</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/India\" title=\"India\">India</a> adopted its <a href=\"https://wikipedia.org/wiki/Constitution_of_India\" title=\"Constitution of India\">constitution</a> and transitioned into a <a href=\"https://wikipedia.org/wiki/Republic\" title=\"Republic\">republic</a> on <a href=\"https://wikipedia.org/wiki/Republic_Day_(India)\" title=\"Republic Day (India)\">this day</a>.", "links": [{"title": "India", "link": "https://wikipedia.org/wiki/India"}, {"title": "Constitution of India", "link": "https://wikipedia.org/wiki/Constitution_of_India"}, {"title": "Republic", "link": "https://wikipedia.org/wiki/Republic"}, {"title": "Republic Day (India)", "link": "https://wikipedia.org/wiki/Republic_Day_(India)"}]}, {"year": "1948", "text": "<PERSON>, American biochemist and endocrinologist (b. 1876)", "html": "1948 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American biochemist and endocrinologist (b. 1876)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American biochemist and endocrinologist (b. 1876)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1953", "text": "<PERSON><PERSON><PERSON>, Canadian lawyer and politician (b. 1882)", "html": "1953 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Canadian lawyer and politician (b. 1882)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Canadian lawyer and politician (b. 1882)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1962", "text": "<PERSON>, Italian-American mob boss (b. 1897)", "html": "1962 - <a href=\"https://wikipedia.org/wiki/<PERSON>_Luciano\" title=\"Lucky Luciano\"><PERSON></a>, Italian-American mob boss (b. 1897)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_Luciano\" title=\"Lucky Luciano\"><PERSON></a>, Italian-American mob boss (b. 1897)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1968", "text": "<PERSON>, American publisher (b. 1883)", "html": "1968 - <a href=\"https://wikipedia.org/wiki/Merrill_C._Meigs\" title=\"Merrill C. Meigs\"><PERSON></a>, American publisher (b. 1883)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Merrill_C._Meigs\" title=\"Merrill C. Meigs\"><PERSON></a>, American publisher (b. 1883)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Merrill_C._Meigs"}]}, {"year": "1973", "text": "<PERSON>, Romanian-American actor (b. 1893)", "html": "1973 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Romanian-American actor (b. 1893)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Romanian-American actor (b. 1893)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1976", "text": "<PERSON>, Portuguese bullfighter (b. 1901)", "html": "1976 - <a href=\"https://wikipedia.org/wiki/Jo%C3%A3o_Branco_N%C3%BAncio\" title=\"João <PERSON>\"><PERSON></a>, Portuguese bullfighter (b. 1901)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Jo%C3%A3o_Branco_N%C3%BAncio\" title=\"João <PERSON>ran<PERSON>\"><PERSON></a>, Portuguese bullfighter (b. 1901)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Jo%C3%A3o_Branco_N%C3%BAncio"}]}, {"year": "1977", "text": "<PERSON>, German Catholic philosopher and author (b. 1889)", "html": "1977 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German Catholic philosopher and author (b. 1889)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German Catholic philosopher and author (b. 1889)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1979", "text": "<PERSON>, American businessman and politician, 41st Vice President of the United States (b. 1908)", "html": "1979 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American businessman and politician, 41st <a href=\"https://wikipedia.org/wiki/Vice_President_of_the_United_States\" title=\"Vice President of the United States\">Vice President of the United States</a> (b. 1908)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American businessman and politician, 41st <a href=\"https://wikipedia.org/wiki/Vice_President_of_the_United_States\" title=\"Vice President of the United States\">Vice President of the United States</a> (b. 1908)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Vice President of the United States", "link": "https://wikipedia.org/wiki/Vice_President_of_the_United_States"}]}, {"year": "1983", "text": "<PERSON>, American football player and coach (b. 1913)", "html": "1983 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player and coach (b. 1913)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player and coach (b. 1913)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1985", "text": "<PERSON>, American jazz drummer and bandleader (b. 1914)", "html": "1985 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American jazz drummer and bandleader (b. 1914)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American jazz drummer and bandleader (b. 1914)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1986", "text": "<PERSON><PERSON>, Finnish linguist and professor (b. 1905)", "html": "1986 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Finnish linguist and professor (b. 1905)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Finnish linguist and professor (b. 1905)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1990", "text": "<PERSON>, American sociologist and historian (b. 1895)", "html": "1990 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American sociologist and historian (b. 1895)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American sociologist and historian (b. 1895)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1992", "text": "<PERSON>, Puerto Rican-American actor (b. 1912)", "html": "1992 - <a href=\"https://wikipedia.org/wiki/Jos%C3%A9_<PERSON><PERSON>\" title=\"<PERSON>\"><PERSON></a>, Puerto Rican-American actor (b. 1912)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Jos%C3%A<PERSON>_<PERSON><PERSON>\" title=\"<PERSON>\"><PERSON></a>, Puerto Rican-American actor (b. 1912)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Jos%C3%A9_Ferrer"}]}, {"year": "1993", "text": "<PERSON>, Dutch businessman and humanitarian (b. 1905)", "html": "1993 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch businessman and humanitarian (b. 1905)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch businessman and humanitarian (b. 1905)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>"}]}, {"year": "1993", "text": "<PERSON>, Canadian journalist and politician, Governor General of Canada (b. 1922)", "html": "1993 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A9\" title=\"<PERSON>\"><PERSON></a>, Canadian journalist and politician, <a href=\"https://wikipedia.org/wiki/Governor_General_of_Canada\" title=\"Governor General of Canada\">Governor General of Canada</a> (b. 1922)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A9\" title=\"<PERSON>\"><PERSON></a>, Canadian journalist and politician, <a href=\"https://wikipedia.org/wiki/Governor_General_of_Canada\" title=\"Governor General of Canada\">Governor General of Canada</a> (b. 1922)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Jeanne_Sauv%C3%A9"}, {"title": "Governor General of Canada", "link": "https://wikipedia.org/wiki/Governor_General_of_Canada"}]}, {"year": "1996", "text": "<PERSON>, American author and academic (b. 1930)", "html": "1996 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author and academic (b. 1930)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author and academic (b. 1930)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1996", "text": "<PERSON>, American football player and coach (b. 1909)", "html": "1996 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(American_football)\" title=\"<PERSON> (American football)\"><PERSON></a>, American football player and coach (b. 1909)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(American_football)\" title=\"<PERSON> (American football)\"><PERSON></a>, American football player and coach (b. 1909)", "links": [{"title": "<PERSON> (American football)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>(American_football)"}]}, {"year": "1996", "text": "<PERSON>, American bassist and conductor (b. 1932)", "html": "1996 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(musician)\" title=\"<PERSON> (musician)\"><PERSON></a>, American bassist and conductor (b. 1932)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(musician)\" title=\"<PERSON> (musician)\"><PERSON></a>, American bassist and conductor (b. 1932)", "links": [{"title": "<PERSON> (musician)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(musician)"}]}, {"year": "1997", "text": "<PERSON><PERSON>, American astrologer and psychic (b. 1904)", "html": "1997 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American astrologer and psychic (b. 1904)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American astrologer and psychic (b. 1904)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "2000", "text": "<PERSON>, American tennis player and coach (b. 1915)", "html": "2000 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American tennis player and coach (b. 1915)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American tennis player and coach (b. 1915)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2000", "text": "<PERSON>, English author and illustrator (b. 1898)", "html": "2000 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English author and illustrator (b. 1898)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English author and illustrator (b. 1898)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2000", "text": "<PERSON><PERSON> <PERSON><PERSON>, Canadian-American author (b. 1912)", "html": "2000 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON> <PERSON><PERSON>\"><PERSON><PERSON> <PERSON><PERSON></a>, Canadian-American author (b. 1912)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON> <PERSON><PERSON>\"><PERSON><PERSON> <PERSON><PERSON></a>, Canadian-American author (b. 1912)", "links": [{"title": "<PERSON><PERSON> <PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>_<PERSON>"}]}, {"year": "2001", "text": "<PERSON>, American basketball player and coach (b. 1928)", "html": "2001 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player and coach (b. 1928)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player and coach (b. 1928)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2003", "text": "<PERSON><PERSON><PERSON>, Russian high jumper (b. 1942)", "html": "2003 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Russian high jumper (b. 1942)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Russian high jumper (b. 1942)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON><PERSON>"}]}, {"year": "2003", "text": "<PERSON>, English historian and academic (b. 1917)", "html": "2003 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English historian and academic (b. 1917)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English historian and academic (b. 1917)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2003", "text": "<PERSON>, 4th Viscount <PERSON> of Leckie, Scottish banker and politician, Secretary of State for Scotland (b. 1931)", "html": "2003 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_4th_Viscount_Younger_of_Leckie\" title=\"<PERSON>, 4th Viscount Younger of Leckie\"><PERSON>, 4th Viscount <PERSON> of Leckie</a>, Scottish banker and politician, <a href=\"https://wikipedia.org/wiki/Secretary_of_State_for_Scotland\" title=\"Secretary of State for Scotland\">Secretary of State for Scotland</a> (b. 1931)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_4th_Viscount_Younger_of_Leckie\" title=\"<PERSON>, 4th Viscount Younger of Leckie\"><PERSON>, 4th Viscount <PERSON> of Leckie</a>, Scottish banker and politician, <a href=\"https://wikipedia.org/wiki/Secretary_of_State_for_Scotland\" title=\"Secretary of State for Scotland\">Secretary of State for Scotland</a> (b. 1931)", "links": [{"title": "<PERSON>, 4th Viscount Younger of Leckie", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_4th_Viscount_Younger_of_Le<PERSON>e"}, {"title": "Secretary of State for Scotland", "link": "https://wikipedia.org/wiki/Secretary_of_State_for_Scotland"}]}, {"year": "2004", "text": "<PERSON>, American golfer (b. 1916)", "html": "2004 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American golfer (b. 1916)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American golfer (b. 1916)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2006", "text": "<PERSON>, Pakistani politician (b. 1917)", "html": "2006 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, Pakistani politician (b. 1917)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, Pakistani politician (b. 1917)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "2007", "text": "<PERSON><PERSON>, Canadian ice hockey player (b. 1929)", "html": "2007 - <a href=\"https://wikipedia.org/wiki/Gump_W<PERSON>ley\" title=\"Gump Worsley\"><PERSON><PERSON></a>, Canadian ice hockey player (b. 1929)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Gump_W<PERSON>ley\" title=\"Gump Worsley\"><PERSON><PERSON></a>, Canadian ice hockey player (b. 1929)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/G<PERSON>_W<PERSON>ley"}]}, {"year": "2008", "text": "<PERSON>, American sculptor and designer (b. 1906)", "html": "2008 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American sculptor and designer (b. 1906)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American sculptor and designer (b. 1906)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2008", "text": "<PERSON>, Palestinian politician, founder of the PFLP (b. 1926)", "html": "2008 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Palestinian politician, founder of the <a href=\"https://wikipedia.org/wiki/PFLP\" class=\"mw-redirect\" title=\"PFLP\">PFLP</a> (b. 1926)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Palestinian politician, founder of the <a href=\"https://wikipedia.org/wiki/PFLP\" class=\"mw-redirect\" title=\"PFLP\">PFLP</a> (b. 1926)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "PFLP", "link": "https://wikipedia.org/wiki/PFLP"}]}, {"year": "2010", "text": "<PERSON>, American novelist and essayist (b. 1917)", "html": "2010 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American novelist and essayist (b. 1917)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American novelist and essayist (b. 1917)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2011", "text": "<PERSON>, Ugandan teacher and LGBT rights activist, considered a father of Uganda's gay rights movement (b. 1964)", "html": "2011 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Ugandan teacher and LGBT rights activist, considered a father of Uganda's gay rights movement (b. 1964)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Ugandan teacher and LGBT rights activist, considered a father of Uganda's gay rights movement (b. 1964)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2011", "text": "<PERSON>, American singer-songwriter and guitarist (b. 1927)", "html": "2011 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and guitarist (b. 1927)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and guitarist (b. 1927)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2012", "text": "<PERSON>, Argentinian race car driver (b. 1924)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Argentinian race car driver (b. 1924)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Argentinian race car driver (b. 1924)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2013", "text": "<PERSON>, American educator and politician (b. 1929)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American educator and politician (b. 1929)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American educator and politician (b. 1929)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "2013", "text": "<PERSON>, Polish-Swiss engineer, inventor of the Nagra (b. 1929)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Polish-Swiss engineer, inventor of the <a href=\"https://wikipedia.org/wiki/Nagra\" title=\"Nagra\">Nagra</a> (b. 1929)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Polish-Swiss engineer, inventor of the <a href=\"https://wikipedia.org/wiki/Nagra\" title=\"Nagra\">Nagra</a> (b. 1929)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Nagra", "link": "https://wikipedia.org/wiki/Nagra"}]}, {"year": "2013", "text": "<PERSON>, Indian physicist and academic (b. 1950)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Indian physicist and academic (b. 1950)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Indian physicist and academic (b. 1950)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "2013", "text": "<PERSON><PERSON><PERSON><PERSON><PERSON>, Japanese author (b. 1920)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/Sh%C5%8Dtar%C5%8D_Ya<PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON><PERSON></a>, Japanese author (b. 1920)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Sh%C5%8Dtar%C5%8D_<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON><PERSON></a>, Japanese author (b. 1920)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Sh%C5%8Dtar%C5%8D_<PERSON><PERSON><PERSON>"}]}, {"year": "2014", "text": "<PERSON>, American basketball player, coach, and politician (b. 1933)", "html": "2014 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player, coach, and politician (b. 1933)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player, coach, and politician (b. 1933)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2014", "text": "<PERSON>, Slovenian-Australian poet and translator (b. 1921)", "html": "2014 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Slovenian-Australian poet and translator (b. 1921)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Slovenian-Australian poet and translator (b. 1921)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2014", "text": "<PERSON>, Mexican poet and author (b. 1939)", "html": "2014 - <a href=\"https://wikipedia.org/wiki/Jos%C3%A<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Mexican poet and author (b. 1939)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Jos%C3%A<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Mexican poet and author (b. 1939)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Jos%C3%A9_<PERSON>_<PERSON>"}]}, {"year": "2015", "text": "<PERSON><PERSON><PERSON> \"<PERSON><PERSON>\" <PERSON>, American art director and cartoonist (b. 1932)", "html": "2015 - <a href=\"https://wikipedia.org/wiki/Cleven_%22Goodie%22_Go<PERSON><PERSON>\" class=\"mw-redirect\" title='<PERSON><PERSON><PERSON> \"<PERSON><PERSON>\" Go<PERSON><PERSON>'><PERSON><PERSON><PERSON> \"<PERSON><PERSON>\" <PERSON></a>, American art director and cartoonist (b. 1932)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Cleven_%22Goodie%22_Go<PERSON><PERSON>\" class=\"mw-redirect\" title='<PERSON><PERSON><PERSON> \"<PERSON><PERSON>\" <PERSON><PERSON><PERSON>'><PERSON><PERSON><PERSON> \"<PERSON><PERSON>\" <PERSON></a>, American art director and cartoonist (b. 1932)", "links": [{"title": "<PERSON><PERSON><PERSON> \"<PERSON><PERSON>\" <PERSON>", "link": "https://wikipedia.org/wiki/Cleven_%22Goodie%22_<PERSON><PERSON><PERSON>"}]}, {"year": "2015", "text": "<PERSON>, Australian politician (b. 1921)", "html": "2015 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian politician (b. 1921)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian politician (b. 1921)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2016", "text": "<PERSON><PERSON><PERSON><PERSON>, Pakistani military leader, foreign minister, and diplomat (b. 1920)", "html": "2016 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON></a>, Pakistani military leader, foreign minister, and diplomat (b. 1920)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON></a>, Pakistani military leader, foreign minister, and diplomat (b. 1920)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "2016", "text": "<PERSON>, American actor (b. 1921)", "html": "2016 - <a href=\"https://wikipedia.org/wiki/<PERSON>_Vigoda\" title=\"Abe Vigoda\"><PERSON></a>, American actor (b. 1921)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_Vigoda\" title=\"Abe Vigoda\"><PERSON></a>, American actor (b. 1921)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Abe_Vigoda"}]}, {"year": "2017", "text": "<PERSON>, American actor (b. 1925)", "html": "2017 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor (b. 1925)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor (b. 1925)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2017", "text": "<PERSON>, Scottish politician (b. 1932)", "html": "2017 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish politician (b. 1932)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish politician (b. 1932)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2017", "text": "<PERSON><PERSON>, Jamaican footballer and sports journalist (b. 1927)", "html": "2017 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Jamaican footballer and sports journalist (b. 1927)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Jamaican footballer and sports journalist (b. 1927)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "2017", "text": "<PERSON>, American actress (b. 1922)", "html": "2017 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress (b. 1922)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress (b. 1922)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2017", "text": "<PERSON>, Canadian sprinter and educator (b. 1920)", "html": "2017 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(athlete)\" title=\"<PERSON> (athlete)\"><PERSON></a>, Canadian sprinter and educator (b. 1920)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>(athlete)\" title=\"<PERSON> (athlete)\"><PERSON></a>, Canadian sprinter and educator (b. 1920)", "links": [{"title": "<PERSON> (athlete)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>(athlete)"}]}, {"year": "2020", "text": "<PERSON>, American college baseball coach (b. 1963)", "html": "2020 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American college baseball coach (b. 1963)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American college baseball coach (b. 1963)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2020", "text": "<PERSON><PERSON><PERSON>, American student-athlete (b. 2006)", "html": "2020 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American student-athlete (b. 2006)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American student-athlete (b. 2006)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "2020", "text": "<PERSON>, American basketball player (b. 1978)", "html": "2020 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player (b. 1978)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player (b. 1978)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_Bryant"}]}]}}