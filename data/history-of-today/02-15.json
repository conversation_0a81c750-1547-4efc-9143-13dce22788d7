{"date": "February 15", "url": "https://wikipedia.org/wiki/February_15", "data": {"Events": [{"year": "438", "text": "Roman emperor <PERSON><PERSON><PERSON> II publishes the law codex Codex <PERSON>us", "html": "438 - <a href=\"https://wikipedia.org/wiki/Ancient_Rome\" title=\"Ancient Rome\">Roman</a> emperor <a href=\"https://wikipedia.org/wiki/Theodosius_II\" title=\"<PERSON><PERSON>ius II\"><PERSON><PERSON><PERSON> II</a> publishes the law codex <a href=\"https://wikipedia.org/wiki/Codex_Theodosianus\" title=\"Codex Theodosianus\">Codex Theodosianus</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Ancient_Rome\" title=\"Ancient Rome\">Roman</a> emperor <a href=\"https://wikipedia.org/wiki/Theodosius_II\" title=\"Theodosius II\"><PERSON><PERSON><PERSON> II</a> publishes the law codex <a href=\"https://wikipedia.org/wiki/Codex_Theodosianus\" title=\"Codex Theodosianus\">Codex Theodosianus</a>", "links": [{"title": "Ancient Rome", "link": "https://wikipedia.org/wiki/Ancient_Rome"}, {"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Theodosius_II"}, {"title": "<PERSON> <PERSON><PERSON>us", "link": "https://wikipedia.org/wiki/<PERSON>_Theodosianus"}]}, {"year": "590", "text": "<PERSON><PERSON><PERSON><PERSON> is crowned king of Persia.", "html": "590 - <a href=\"https://wikipedia.org/wiki/Khosrau_II\" class=\"mw-redirect\" title=\"Khosrau II\"><PERSON>hos<PERSON><PERSON> II</a> is crowned king of <a href=\"https://wikipedia.org/wiki/Sassanid_dynasty\" class=\"mw-redirect\" title=\"Sassanid dynasty\">Persia</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Khosrau_II\" class=\"mw-redirect\" title=\"Khosrau II\">Khosrau II</a> is crowned king of <a href=\"https://wikipedia.org/wiki/Sassanid_dynasty\" class=\"mw-redirect\" title=\"Sassanid dynasty\">Persia</a>.", "links": [{"title": "Khosrau II", "link": "https://wikipedia.org/wiki/Khosrau_II"}, {"title": "Sassanid dynasty", "link": "https://wikipedia.org/wiki/Sassanid_dynasty"}]}, {"year": "706", "text": "Byzantine emperor <PERSON><PERSON> has his predecessors <PERSON><PERSON><PERSON> and <PERSON><PERSON><PERSON> publicly executed in the Hippodrome of Constantinople.", "html": "706 - <a href=\"https://wikipedia.org/wiki/Byzantine_Empire\" title=\"Byzantine Empire\">Byzantine</a> emperor <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_II\" title=\"<PERSON><PERSON> II\"><PERSON><PERSON> II</a> has his predecessors <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a> and <a href=\"https://wikipedia.org/wiki/Tiberios_III\" class=\"mw-redirect\" title=\"Tiberios III\">Tiberios III</a> publicly executed in the <a href=\"https://wikipedia.org/wiki/Hippodrome_of_Constantinople\" title=\"Hippodrome of Constantinople\">Hippodrome of Constantinople</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Byzantine_Empire\" title=\"Byzantine Empire\">Byzantine</a> emperor <a href=\"https://wikipedia.org/wiki/Justin<PERSON>_II\" title=\"<PERSON><PERSON> II\"><PERSON><PERSON> II</a> has his predecessors <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a> and <a href=\"https://wikipedia.org/wiki/Tiberios_III\" class=\"mw-redirect\" title=\"Tiberios III\">Tiberios III</a> publicly executed in the <a href=\"https://wikipedia.org/wiki/Hippodrome_of_Constantinople\" title=\"Hippodrome of Constantinople\">Hippodrome of Constantinople</a>.", "links": [{"title": "Byzantine Empire", "link": "https://wikipedia.org/wiki/Byzantine_Empire"}, {"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}, {"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>"}, {"title": "<PERSON><PERSON><PERSON> III", "link": "https://wikipedia.org/wiki/Tiberios_III"}, {"title": "Hippodrome of Constantinople", "link": "https://wikipedia.org/wiki/Hippodrome_of_Constantinople"}]}, {"year": "1002", "text": "At an assembly at Pavia of Lombard nobles, <PERSON><PERSON><PERSON> of Ivrea is restored to his domains and crowned King of Italy.", "html": "1002 - At an assembly at <a href=\"https://wikipedia.org/wiki/Pavia\" title=\"Pavia\">Pavia</a> of <a href=\"https://wikipedia.org/wiki/Lombardy\" title=\"Lombardy\">Lombard</a> nobles, <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_of_Ivrea\" title=\"<PERSON><PERSON><PERSON> of Ivrea\"><PERSON><PERSON><PERSON> of Ivrea</a> is restored to his domains and crowned <a href=\"https://wikipedia.org/wiki/King_of_Italy\" title=\"King of Italy\">King of Italy</a>.", "no_year_html": "At an assembly at <a href=\"https://wikipedia.org/wiki/Pavia\" title=\"Pavia\">Pavia</a> of <a href=\"https://wikipedia.org/wiki/Lombardy\" title=\"Lombardy\">Lombard</a> nobles, <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_of_Ivrea\" title=\"<PERSON><PERSON><PERSON> of Ivrea\"><PERSON><PERSON><PERSON> of Ivrea</a> is restored to his domains and crowned <a href=\"https://wikipedia.org/wiki/King_of_Italy\" title=\"King of Italy\">King of Italy</a>.", "links": [{"title": "Pavia", "link": "https://wikipedia.org/wiki/Pavia"}, {"title": "Lombardy", "link": "https://wikipedia.org/wiki/Lombardy"}, {"title": "<PERSON><PERSON><PERSON> of Ivrea", "link": "https://wikipedia.org/wiki/Ard<PERSON>_of_Ivrea"}, {"title": "King of Italy", "link": "https://wikipedia.org/wiki/King_of_Italy"}]}, {"year": "1113", "text": "<PERSON> <PERSON><PERSON><PERSON> II issues Pie Postulatio Voluntatis, recognizing the Order of Hospitallers.", "html": "1113 - <a href=\"https://wikipedia.org/wiki/Pope_Paschal_II\" title=\"Pope Paschal II\"><PERSON> Paschal II</a> issues <i><a href=\"https://wikipedia.org/wiki/Pie_Postulatio_Voluntatis\" class=\"mw-redirect\" title=\"Pie Postulatio Voluntatis\">Pie Postulatio Voluntatis</a></i>, recognizing <a href=\"https://wikipedia.org/wiki/Knights_Hospitaller\" title=\"Knights Hospitaller\">the Order of Hospitallers</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Pope_Paschal_II\" title=\"Pope Paschal II\"><PERSON> Paschal II</a> issues <i><a href=\"https://wikipedia.org/wiki/Pie_Postulatio_Voluntatis\" class=\"mw-redirect\" title=\"Pie Postulatio Voluntatis\">Pie Postulatio Voluntatis</a></i>, recognizing <a href=\"https://wikipedia.org/wiki/Knights_Hospitaller\" title=\"Knights Hospitaller\">the Order of Hospitallers</a>.", "links": [{"title": "<PERSON> II", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON><PERSON>_II"}, {"title": "Pie Postula<PERSON>o Voluntatis", "link": "https://wikipedia.org/wiki/Pie_Postulatio_Voluntatis"}, {"title": "Knights Hospitaller", "link": "https://wikipedia.org/wiki/Knights_Hospitaller"}]}, {"year": "1214", "text": "During the Anglo-French War (1213-1214), an English invasion force led by <PERSON>, King of England, lands at La Rochelle in France.", "html": "1214 - During the <a href=\"https://wikipedia.org/wiki/Anglo-French_War_(1213%E2%80%931214)\" title=\"Anglo-French War (1213-1214)\">Anglo-French War (1213-1214)</a>, an English invasion force led by <a href=\"https://wikipedia.org/wiki/<PERSON>,_King_of_England\" title=\"<PERSON>, King of England\"><PERSON>, King of England</a>, lands at <a href=\"https://wikipedia.org/wiki/La_Rochelle\" title=\"La Rochelle\">La Rochelle</a> in <a href=\"https://wikipedia.org/wiki/France\" title=\"France\">France</a>.", "no_year_html": "During the <a href=\"https://wikipedia.org/wiki/Anglo-French_War_(1213%E2%80%931214)\" title=\"Anglo-French War (1213-1214)\">Anglo-French War (1213-1214)</a>, an English invasion force led by <a href=\"https://wikipedia.org/wiki/<PERSON>,_King_of_England\" title=\"<PERSON>, King of England\"><PERSON>, King of England</a>, lands at <a href=\"https://wikipedia.org/wiki/La_Rochelle\" title=\"La Rochelle\">La Rochelle</a> in <a href=\"https://wikipedia.org/wiki/France\" title=\"France\">France</a>.", "links": [{"title": "Anglo-French War (1213-1214)", "link": "https://wikipedia.org/wiki/Anglo-French_War_(1213%E2%80%931214)"}, {"title": "<PERSON>, King of England", "link": "https://wikipedia.org/wiki/<PERSON>,_King_of_England"}, {"title": "La Rochelle", "link": "https://wikipedia.org/wiki/La_Rochelle"}, {"title": "France", "link": "https://wikipedia.org/wiki/France"}]}, {"year": "1493", "text": "While on board the Niña, <PERSON> writes an open letter (widely distributed upon his return to Portugal) describing his discoveries and the unexpected items he came across in the New World.", "html": "1493 - While on board the <i><a href=\"https://wikipedia.org/wiki/Ni%C3%B1a_(ship)\" class=\"mw-redirect\" title=\"<PERSON><PERSON> (ship)\"><PERSON><PERSON></a></i>, <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> <a href=\"https://wikipedia.org/wiki/Columbus%27s_letter_on_the_first_voyage\" title=\"<PERSON>'s letter on the first voyage\">writes an open letter</a> (widely distributed upon his return to <a href=\"https://wikipedia.org/wiki/Portugal\" title=\"Portugal\">Portugal</a>) describing his discoveries and the unexpected items he came across in the <a href=\"https://wikipedia.org/wiki/New_World\" title=\"New World\">New World</a>.", "no_year_html": "While on board the <i><a href=\"https://wikipedia.org/wiki/Ni%C3%B1a_(ship)\" class=\"mw-redirect\" title=\"<PERSON><PERSON> (ship)\"><PERSON><PERSON></a></i>, <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> <a href=\"https://wikipedia.org/wiki/Columbus%27s_letter_on_the_first_voyage\" title=\"<PERSON>'s letter on the first voyage\">writes an open letter</a> (widely distributed upon his return to <a href=\"https://wikipedia.org/wiki/Portugal\" title=\"Portugal\">Portugal</a>) describing his discoveries and the unexpected items he came across in the <a href=\"https://wikipedia.org/wiki/New_World\" title=\"New World\">New World</a>.", "links": [{"title": "<PERSON><PERSON> (ship)", "link": "https://wikipedia.org/wiki/Ni%C3%B1a_(ship)"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "<PERSON>'s letter on the first voyage", "link": "https://wikipedia.org/wiki/Columbus%27s_letter_on_the_first_voyage"}, {"title": "Portugal", "link": "https://wikipedia.org/wiki/Portugal"}, {"title": "New World", "link": "https://wikipedia.org/wiki/New_World"}]}, {"year": "1637", "text": "<PERSON> becomes Holy Roman Emperor.", "html": "1637 - <a href=\"https://wikipedia.org/wiki/<PERSON>_III,_Holy_Roman_Emperor\" title=\"Ferdinand III, Holy Roman Emperor\"><PERSON> III</a> becomes <a href=\"https://wikipedia.org/wiki/Holy_Roman_Emperor\" title=\"Holy Roman Emperor\">Holy Roman Emperor</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_III,_Holy_Roman_Emperor\" title=\"Ferdinand III, Holy Roman Emperor\"><PERSON> III</a> becomes <a href=\"https://wikipedia.org/wiki/Holy_Roman_Emperor\" title=\"Holy Roman Emperor\">Holy Roman Emperor</a>.", "links": [{"title": "<PERSON>, Holy Roman Emperor", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Holy_Roman_Emperor"}, {"title": "Holy Roman Emperor", "link": "https://wikipedia.org/wiki/Holy_Roman_Emperor"}]}, {"year": "1690", "text": "Constantin <PERSON>, Prince of Moldavia, and the Holy Roman Empire sign a secret treaty in Sibiu, stipulating that Moldavia would support the actions led by the House of Habsburg against the Ottoman Empire.", "html": "1690 - <a href=\"https://wikipedia.org/wiki/Constantin_Cantemir\" title=\"Constantin Cantemir\">Constant<PERSON></a>, Prince of <a href=\"https://wikipedia.org/wiki/Moldavia\" title=\"Moldavia\">Moldavia</a>, and the <a href=\"https://wikipedia.org/wiki/Holy_Roman_Empire\" title=\"Holy Roman Empire\">Holy Roman Empire</a> sign a secret treaty in <a href=\"https://wikipedia.org/wiki/Sibiu\" title=\"Sibiu\">Sibiu</a>, stipulating that Moldavia would support the actions led by the <a href=\"https://wikipedia.org/wiki/House_of_Habsburg\" title=\"House of Habsburg\">House of Habsburg</a> against the <a href=\"https://wikipedia.org/wiki/Ottoman_Empire\" title=\"Ottoman Empire\">Ottoman Empire</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Constantin_Cantemir\" title=\"Constantin Cantemir\">Con<PERSON><PERSON></a>, Prince of <a href=\"https://wikipedia.org/wiki/Moldavia\" title=\"Moldavia\">Moldavia</a>, and the <a href=\"https://wikipedia.org/wiki/Holy_Roman_Empire\" title=\"Holy Roman Empire\">Holy Roman Empire</a> sign a secret treaty in <a href=\"https://wikipedia.org/wiki/Sibiu\" title=\"Sibiu\">Sibiu</a>, stipulating that Moldavia would support the actions led by the <a href=\"https://wikipedia.org/wiki/House_of_Habsburg\" title=\"House of Habsburg\">House of Habsburg</a> against the <a href=\"https://wikipedia.org/wiki/Ottoman_Empire\" title=\"Ottoman Empire\">Ottoman Empire</a>.", "links": [{"title": "Constant<PERSON>", "link": "https://wikipedia.org/wiki/Constantin_Cantemir"}, {"title": "Moldavia", "link": "https://wikipedia.org/wiki/Moldavia"}, {"title": "Holy Roman Empire", "link": "https://wikipedia.org/wiki/Holy_Roman_Empire"}, {"title": "Sibiu", "link": "https://wikipedia.org/wiki/Sibiu"}, {"title": "House of Habsburg", "link": "https://wikipedia.org/wiki/House_of_Habsburg"}, {"title": "Ottoman Empire", "link": "https://wikipedia.org/wiki/Ottoman_Empire"}]}, {"year": "1764", "text": "The city of St. Louis is established in Spanish Louisiana (now in Missouri, USA).", "html": "1764 - The city of <a href=\"https://wikipedia.org/wiki/St._Louis\" title=\"St. Louis\">St. Louis</a> is established in <a href=\"https://wikipedia.org/wiki/Louisiana_(New_Spain)\" title=\"Louisiana (New Spain)\">Spanish Louisiana</a> (now in <a href=\"https://wikipedia.org/wiki/Missouri\" title=\"Missouri\">Missouri</a>, USA).", "no_year_html": "The city of <a href=\"https://wikipedia.org/wiki/St._Louis\" title=\"St. Louis\">St. Louis</a> is established in <a href=\"https://wikipedia.org/wiki/Louisiana_(New_Spain)\" title=\"Louisiana (New Spain)\">Spanish Louisiana</a> (now in <a href=\"https://wikipedia.org/wiki/Missouri\" title=\"Missouri\">Missouri</a>, USA).", "links": [{"title": "St. Louis", "link": "https://wikipedia.org/wiki/St._Louis"}, {"title": "Louisiana (New Spain)", "link": "https://wikipedia.org/wiki/Louisiana_(New_Spain)"}, {"title": "Missouri", "link": "https://wikipedia.org/wiki/Missouri"}]}, {"year": "1798", "text": "The Roman Republic is proclaimed after <PERSON><PERSON><PERSON>, a general of <PERSON>, had invaded the city of Rome five days earlier.", "html": "1798 - The <a href=\"https://wikipedia.org/wiki/Roman_Republic_(18th_century)\" class=\"mw-redirect\" title=\"Roman Republic (18th century)\">Roman Republic</a> is proclaimed after <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON><PERSON></a>, a general of <a href=\"https://wikipedia.org/wiki/Napoleon\" title=\"<PERSON>\"><PERSON></a>, had invaded the city of <a href=\"https://wikipedia.org/wiki/Rome\" title=\"Rome\">Rome</a> five days earlier.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Roman_Republic_(18th_century)\" class=\"mw-redirect\" title=\"Roman Republic (18th century)\">Roman Republic</a> is proclaimed after <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, a general of <a href=\"https://wikipedia.org/wiki/Napoleon\" title=\"<PERSON>\"><PERSON></a>, had invaded the city of <a href=\"https://wikipedia.org/wiki/Rome\" title=\"Rome\">Rome</a> five days earlier.", "links": [{"title": "Roman Republic (18th century)", "link": "https://wikipedia.org/wiki/Roman_Republic_(18th_century)"}, {"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Napoleon"}, {"title": "Rome", "link": "https://wikipedia.org/wiki/Rome"}]}, {"year": "1835", "text": "Serbia's Sretenje <PERSON> briefly comes into effect.", "html": "1835 - Serbia's <a href=\"https://wikipedia.org/wiki/Sretenje_Constitution\" class=\"mw-redirect\" title=\"Sretenje Constitution\">Sretenje Constitution</a> briefly comes into effect.", "no_year_html": "Serbia's <a href=\"https://wikipedia.org/wiki/Sretenje_Constitution\" class=\"mw-redirect\" title=\"Sretenje Constitution\">Sretenje Constitution</a> briefly comes into effect.", "links": [{"title": "Sretenje Constitution", "link": "https://wikipedia.org/wiki/Sretenje_Constitution"}]}, {"year": "1852", "text": "The Helsinki Cathedral (known as St. Nicholas' Church at time) is officially inaugurated in Helsinki, Finland.", "html": "1852 - The <a href=\"https://wikipedia.org/wiki/Helsinki_Cathedral\" title=\"Helsinki Cathedral\">Helsinki Cathedral</a> (known as <i>St. Nicholas' Church</i> at time) is officially inaugurated in <a href=\"https://wikipedia.org/wiki/Helsinki\" title=\"Helsinki\">Helsinki</a>, <a href=\"https://wikipedia.org/wiki/Grand_Duchy_of_Finland\" title=\"Grand Duchy of Finland\">Finland</a>.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Helsinki_Cathedral\" title=\"Helsinki Cathedral\">Helsinki Cathedral</a> (known as <i>St. Nicholas' Church</i> at time) is officially inaugurated in <a href=\"https://wikipedia.org/wiki/Helsinki\" title=\"Helsinki\">Helsinki</a>, <a href=\"https://wikipedia.org/wiki/Grand_Duchy_of_Finland\" title=\"Grand Duchy of Finland\">Finland</a>.", "links": [{"title": "Helsinki Cathedral", "link": "https://wikipedia.org/wiki/Helsinki_Cathedral"}, {"title": "Helsinki", "link": "https://wikipedia.org/wiki/Helsinki"}, {"title": "Grand Duchy of Finland", "link": "https://wikipedia.org/wiki/Grand_Duchy_of_Finland"}]}, {"year": "1862", "text": "American Civil War: Confederates commanded by Brig. Gen. <PERSON> attack General <PERSON>'s Union forces besieging Fort Donelson in Tennessee. Unable to break the fort's encirclement, the Confederates surrender the following day.", "html": "1862 - <a href=\"https://wikipedia.org/wiki/American_Civil_War\" title=\"American Civil War\">American Civil War</a>: Confederates commanded by Brig. Gen. <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> attack General <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_Grant\" title=\"Ulysses <PERSON> Grant\"><PERSON></a>'s Union forces <a href=\"https://wikipedia.org/wiki/Battle_of_Fort_Donelson\" title=\"Battle of Fort Donelson\">besieging Fort Donelson</a> in <a href=\"https://wikipedia.org/wiki/Tennessee\" title=\"Tennessee\">Tennessee</a>. Unable to break the fort's encirclement, the Confederates surrender the following day.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/American_Civil_War\" title=\"American Civil War\">American Civil War</a>: Confederates commanded by Brig. Gen. <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> attack General <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_Grant\" title=\"Ulysses <PERSON> Grant\">Ulysses <PERSON></a>'s Union forces <a href=\"https://wikipedia.org/wiki/Battle_of_Fort_Donelson\" title=\"Battle of Fort Donelson\">besieging Fort Donelson</a> in <a href=\"https://wikipedia.org/wiki/Tennessee\" title=\"Tennessee\">Tennessee</a>. Unable to break the fort's encirclement, the Confederates surrender the following day.", "links": [{"title": "American Civil War", "link": "https://wikipedia.org/wiki/American_Civil_War"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>"}, {"title": "Battle of Fort Donelson", "link": "https://wikipedia.org/wiki/Battle_of_Fort_Donelson"}, {"title": "Tennessee", "link": "https://wikipedia.org/wiki/Tennessee"}]}, {"year": "1870", "text": "Stevens Institute of Technology is founded in New Jersey, US, and offers the first Bachelor of Engineering degree in mechanical engineering.", "html": "1870 - <a href=\"https://wikipedia.org/wiki/Stevens_Institute_of_Technology\" title=\"Stevens Institute of Technology\">Stevens Institute of Technology</a> is founded in New Jersey, US, and offers the first <a href=\"https://wikipedia.org/wiki/Bachelor_of_Engineering\" title=\"Bachelor of Engineering\">Bachelor of Engineering</a> degree in <a href=\"https://wikipedia.org/wiki/Mechanical_engineering\" title=\"Mechanical engineering\">mechanical engineering</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Stevens_Institute_of_Technology\" title=\"Stevens Institute of Technology\">Stevens Institute of Technology</a> is founded in New Jersey, US, and offers the first <a href=\"https://wikipedia.org/wiki/Bachelor_of_Engineering\" title=\"Bachelor of Engineering\">Bachelor of Engineering</a> degree in <a href=\"https://wikipedia.org/wiki/Mechanical_engineering\" title=\"Mechanical engineering\">mechanical engineering</a>.", "links": [{"title": "Stevens Institute of Technology", "link": "https://wikipedia.org/wiki/Stevens_Institute_of_Technology"}, {"title": "Bachelor of Engineering", "link": "https://wikipedia.org/wiki/Bachelor_of_Engineering"}, {"title": "Mechanical engineering", "link": "https://wikipedia.org/wiki/Mechanical_engineering"}]}, {"year": "1879", "text": "Women's rights: US President <PERSON> signs a bill allowing female attorneys to argue cases before the Supreme Court of the United States.", "html": "1879 - <a href=\"https://wikipedia.org/wiki/Women%27s_rights\" title=\"Women's rights\">Women's rights</a>: US President <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> signs a bill allowing female <a href=\"https://wikipedia.org/wiki/Lawyer\" title=\"Lawyer\">attorneys</a> to argue cases before the <a href=\"https://wikipedia.org/wiki/Supreme_Court_of_the_United_States\" title=\"Supreme Court of the United States\">Supreme Court of the United States</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Women%27s_rights\" title=\"Women's rights\">Women's rights</a>: US President <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> signs a bill allowing female <a href=\"https://wikipedia.org/wiki/Lawyer\" title=\"Lawyer\">attorneys</a> to argue cases before the <a href=\"https://wikipedia.org/wiki/Supreme_Court_of_the_United_States\" title=\"Supreme Court of the United States\">Supreme Court of the United States</a>.", "links": [{"title": "Women's rights", "link": "https://wikipedia.org/wiki/Women%27s_rights"}, {"title": "<PERSON> B<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>"}, {"title": "Lawyer", "link": "https://wikipedia.org/wiki/Lawyer"}, {"title": "Supreme Court of the United States", "link": "https://wikipedia.org/wiki/Supreme_Court_of_the_United_States"}]}, {"year": "1898", "text": "The battleship USS Maine explodes and sinks in Havana harbor in Cuba, killing about 274 of the ship's roughly 354 crew. The disaster pushes the United States to declare war on Spain.", "html": "1898 - The battleship <a href=\"https://wikipedia.org/wiki/USS_Maine_(1889)\" title=\"USS Maine (1889)\">USS <i>Maine</i></a> explodes and sinks in <a href=\"https://wikipedia.org/wiki/Havana\" title=\"Havana\">Havana</a> harbor in <a href=\"https://wikipedia.org/wiki/Cuba\" title=\"Cuba\">Cuba</a>, killing about 274 of the ship's roughly 354 crew. The disaster pushes the <a href=\"https://wikipedia.org/wiki/United_States\" title=\"United States\">United States</a> to <a href=\"https://wikipedia.org/wiki/Spanish%E2%80%93American_War\" title=\"Spanish-American War\">declare war</a> on <a href=\"https://wikipedia.org/wiki/Spain\" title=\"Spain\">Spain</a>.", "no_year_html": "The battleship <a href=\"https://wikipedia.org/wiki/USS_Maine_(1889)\" title=\"USS Maine (1889)\">USS <i>Maine</i></a> explodes and sinks in <a href=\"https://wikipedia.org/wiki/Havana\" title=\"Havana\">Havana</a> harbor in <a href=\"https://wikipedia.org/wiki/Cuba\" title=\"Cuba\">Cuba</a>, killing about 274 of the ship's roughly 354 crew. The disaster pushes the <a href=\"https://wikipedia.org/wiki/United_States\" title=\"United States\">United States</a> to <a href=\"https://wikipedia.org/wiki/Spanish%E2%80%93American_War\" title=\"Spanish-American War\">declare war</a> on <a href=\"https://wikipedia.org/wiki/Spain\" title=\"Spain\">Spain</a>.", "links": [{"title": "USS Maine (1889)", "link": "https://wikipedia.org/wiki/USS_Maine_(1889)"}, {"title": "Havana", "link": "https://wikipedia.org/wiki/Havana"}, {"title": "Cuba", "link": "https://wikipedia.org/wiki/Cuba"}, {"title": "United States", "link": "https://wikipedia.org/wiki/United_States"}, {"title": "Spanish-American War", "link": "https://wikipedia.org/wiki/Spanish%E2%80%93American_War"}, {"title": "Spain", "link": "https://wikipedia.org/wiki/Spain"}]}, {"year": "1899", "text": "Tsar <PERSON> of Russia issues a declaration known as the February Manifesto, which reduces the autonomy of the Grand Duchy of Finland, thus beginning the first period of oppression.", "html": "1899 - <a href=\"https://wikipedia.org/wiki/<PERSON>_II_of_Russia\" class=\"mw-redirect\" title=\"Nicholas II of Russia\">Tsar <PERSON> of Russia</a> issues a declaration known as the <a href=\"https://wikipedia.org/wiki/February_Manifesto\" title=\"February Manifesto\">February Manifesto</a>, which reduces the autonomy of the <a href=\"https://wikipedia.org/wiki/Grand_Duchy_of_Finland\" title=\"Grand Duchy of Finland\">Grand Duchy of Finland</a>, thus beginning the <a href=\"https://wikipedia.org/wiki/Russification_of_Finland\" title=\"Russification of Finland\">first period of oppression</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_II_of_Russia\" class=\"mw-redirect\" title=\"Nicholas II of Russia\">Tsar <PERSON> of Russia</a> issues a declaration known as the <a href=\"https://wikipedia.org/wiki/February_Manifesto\" title=\"February Manifesto\">February Manifesto</a>, which reduces the autonomy of the <a href=\"https://wikipedia.org/wiki/Grand_Duchy_of_Finland\" title=\"Grand Duchy of Finland\">Grand Duchy of Finland</a>, thus beginning the <a href=\"https://wikipedia.org/wiki/Russification_of_Finland\" title=\"Russification of Finland\">first period of oppression</a>.", "links": [{"title": "<PERSON> of Russia", "link": "https://wikipedia.org/wiki/<PERSON>_II_of_Russia"}, {"title": "February <PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/February_Manifesto"}, {"title": "Grand Duchy of Finland", "link": "https://wikipedia.org/wiki/Grand_Duchy_of_Finland"}, {"title": "Russification of Finland", "link": "https://wikipedia.org/wiki/Russification_of_Finland"}]}, {"year": "1909", "text": "The Flores Theater fire in Acapulco, Mexico kills 250.", "html": "1909 - The Flores Theater fire in <a href=\"https://wikipedia.org/wiki/Acapulco\" title=\"Acapulco\">Acapulco</a>, Mexico kills 250.", "no_year_html": "The Flores Theater fire in <a href=\"https://wikipedia.org/wiki/Acapulco\" title=\"Acapulco\">Acapulco</a>, Mexico kills 250.", "links": [{"title": "Acapulco", "link": "https://wikipedia.org/wiki/Acapulco"}]}, {"year": "1923", "text": "Greece becomes the last European country to adopt the Gregorian calendar.", "html": "1923 - <a href=\"https://wikipedia.org/wiki/Greece\" title=\"Greece\">Greece</a> becomes the last European country to adopt the <a href=\"https://wikipedia.org/wiki/Gregorian_calendar\" title=\"Gregorian calendar\">Gregorian calendar</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Greece\" title=\"Greece\">Greece</a> becomes the last European country to adopt the <a href=\"https://wikipedia.org/wiki/Gregorian_calendar\" title=\"Gregorian calendar\">Gregorian calendar</a>.", "links": [{"title": "Greece", "link": "https://wikipedia.org/wiki/Greece"}, {"title": "Gregorian calendar", "link": "https://wikipedia.org/wiki/Gregorian_calendar"}]}, {"year": "1925", "text": "The 1925 serum run to Nome: The second delivery of serum arrives in Nome, Alaska.", "html": "1925 - The <a href=\"https://wikipedia.org/wiki/1925_serum_run_to_Nome\" title=\"1925 serum run to Nome\">1925 serum run to Nome</a>: The second delivery of serum arrives in <a href=\"https://wikipedia.org/wiki/Nome,_Alaska\" title=\"Nome, Alaska\">Nome, Alaska</a>.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/1925_serum_run_to_Nome\" title=\"1925 serum run to Nome\">1925 serum run to Nome</a>: The second delivery of serum arrives in <a href=\"https://wikipedia.org/wiki/Nome,_Alaska\" title=\"Nome, Alaska\">Nome, Alaska</a>.", "links": [{"title": "1925 serum run to Nome", "link": "https://wikipedia.org/wiki/1925_serum_run_to_Nome"}, {"title": "Nome, Alaska", "link": "https://wikipedia.org/wiki/Nome,_Alaska"}]}, {"year": "1933", "text": "In Miami, <PERSON> attempts to assassinate US President-elect <PERSON>, but instead shoots Chicago mayor <PERSON>, who dies of his wounds on March 6.", "html": "1933 - In <a href=\"https://wikipedia.org/wiki/Miami\" title=\"Miami\">Miami</a>, <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> attempts to assassinate US President-elect <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, but instead shoots <a href=\"https://wikipedia.org/wiki/Mayor_of_Chicago\" title=\"Mayor of Chicago\">Chicago mayor</a> <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, who dies of his wounds on March 6.", "no_year_html": "In <a href=\"https://wikipedia.org/wiki/Miami\" title=\"Miami\">Miami</a>, <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> attempts to assassinate US President-elect <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, but instead shoots <a href=\"https://wikipedia.org/wiki/Mayor_of_Chicago\" title=\"Mayor of Chicago\">Chicago mayor</a> <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, who dies of his wounds on March 6.", "links": [{"title": "Miami", "link": "https://wikipedia.org/wiki/Miami"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "Mayor of Chicago", "link": "https://wikipedia.org/wiki/Mayor_of_Chicago"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1940", "text": "<PERSON>'s Saxophone Sonata was officially premiered at the Carnegie Chamber Hall by saxophonist <PERSON>, who had commissioned it, and the composer.", "html": "1940 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>'s <a href=\"https://wikipedia.org/wiki/Saxophone_Sonata_(Creston)\" title=\"Saxophone Sonata (Creston)\">Saxophone Sonata</a> was officially premiered at the <a href=\"https://wikipedia.org/wiki/Carnegie_Chamber_Hall\" class=\"mw-redirect\" title=\"Carnegie Chamber Hall\">Carnegie Chamber Hall</a> by saxophonist <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>\" title=\"<PERSON>\"><PERSON></a>, who had commissioned it, and the composer.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>'s <a href=\"https://wikipedia.org/wiki/Saxophone_Sonata_(Creston)\" title=\"Saxophone Sonata (Creston)\">Saxophone Sonata</a> was officially premiered at the <a href=\"https://wikipedia.org/wiki/Carnegie_Chamber_Hall\" class=\"mw-redirect\" title=\"Carnegie Chamber Hall\">Carnegie Chamber Hall</a> by saxophonist <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>\" title=\"<PERSON>\"><PERSON></a>, who had commissioned it, and the composer.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Saxophone Sonata (Creston)", "link": "https://wikipedia.org/wiki/Saxophone_Sonata_(Creston)"}, {"title": "Carnegie Chamber Hall", "link": "https://wikipedia.org/wiki/Carnegie_Chamber_Hall"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1942", "text": "World War II: Fall of Singapore. Following an assault by Japanese forces, the British General <PERSON> surrenders. About 80,000 Indian, United Kingdom and Australian soldiers become prisoners of war, the largest surrender of British-led military personnel in history.", "html": "1942 - <a href=\"https://wikipedia.org/wiki/World_War_II\" title=\"World War II\">World War II</a>: <a href=\"https://wikipedia.org/wiki/Fall_of_Singapore\" title=\"Fall of Singapore\">Fall of Singapore</a>. Following an assault by <a href=\"https://wikipedia.org/wiki/Empire_of_Japan\" title=\"Empire of Japan\">Japanese</a> forces, the British General <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> surrenders. About 80,000 <a href=\"https://wikipedia.org/wiki/British_Indian_Army\" title=\"British Indian Army\">Indian</a>, United Kingdom and Australian soldiers become <a href=\"https://wikipedia.org/wiki/Prisoners_of_war\" class=\"mw-redirect\" title=\"Prisoners of war\">prisoners of war</a>, the largest surrender of British-led military personnel in history.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/World_War_II\" title=\"World War II\">World War II</a>: <a href=\"https://wikipedia.org/wiki/Fall_of_Singapore\" title=\"Fall of Singapore\">Fall of Singapore</a>. Following an assault by <a href=\"https://wikipedia.org/wiki/Empire_of_Japan\" title=\"Empire of Japan\">Japanese</a> forces, the British General <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> surrenders. About 80,000 <a href=\"https://wikipedia.org/wiki/British_Indian_Army\" title=\"British Indian Army\">Indian</a>, United Kingdom and Australian soldiers become <a href=\"https://wikipedia.org/wiki/Prisoners_of_war\" class=\"mw-redirect\" title=\"Prisoners of war\">prisoners of war</a>, the largest surrender of British-led military personnel in history.", "links": [{"title": "World War II", "link": "https://wikipedia.org/wiki/World_War_II"}, {"title": "Fall of Singapore", "link": "https://wikipedia.org/wiki/Fall_of_Singapore"}, {"title": "Empire of Japan", "link": "https://wikipedia.org/wiki/Empire_of_Japan"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "British Indian Army", "link": "https://wikipedia.org/wiki/British_Indian_Army"}, {"title": "Prisoners of war", "link": "https://wikipedia.org/wiki/Prisoners_of_war"}]}, {"year": "1944", "text": "World War II: The assault on Monte Cassino, Italy begins.", "html": "1944 - World War II: The <a href=\"https://wikipedia.org/wiki/Battle_of_Monte_Cassino\" title=\"Battle of Monte Cassino\">assault</a> on <a href=\"https://wikipedia.org/wiki/Monte_Cassino\" title=\"Monte Cassino\">Monte Cassino</a>, Italy begins.", "no_year_html": "World War II: The <a href=\"https://wikipedia.org/wiki/Battle_of_Monte_Cassino\" title=\"Battle of Monte Cassino\">assault</a> on <a href=\"https://wikipedia.org/wiki/Monte_Cassino\" title=\"Monte Cassino\">Monte Cassino</a>, Italy begins.", "links": [{"title": "Battle of Monte Cassino", "link": "https://wikipedia.org/wiki/Battle_of_Monte_Cassino"}, {"title": "Monte Cassino", "link": "https://wikipedia.org/wiki/Monte_Cassino"}]}, {"year": "1944", "text": "World War II: The Narva Offensive begins.", "html": "1944 - World War II: The <a href=\"https://wikipedia.org/wiki/Narva_Offensive_(15%E2%80%9328_February_1944)\" class=\"mw-redirect\" title=\"Narva Offensive (15-28 February 1944)\">Narva Offensive</a> begins.", "no_year_html": "World War II: The <a href=\"https://wikipedia.org/wiki/Narva_Offensive_(15%E2%80%9328_February_1944)\" class=\"mw-redirect\" title=\"Narva Offensive (15-28 February 1944)\">Narva Offensive</a> begins.", "links": [{"title": "Narva Offensive (15-28 February 1944)", "link": "https://wikipedia.org/wiki/Narva_Offensive_(15%E2%80%9328_February_1944)"}]}, {"year": "1945", "text": "World War II: Third day of bombing in Dresden.", "html": "1945 - World War II: Third day of <a href=\"https://wikipedia.org/wiki/Bombing_of_Dresden_in_World_War_II\" class=\"mw-redirect\" title=\"Bombing of Dresden in World War II\">bombing in Dresden</a>.", "no_year_html": "World War II: Third day of <a href=\"https://wikipedia.org/wiki/Bombing_of_Dresden_in_World_War_II\" class=\"mw-redirect\" title=\"Bombing of Dresden in World War II\">bombing in Dresden</a>.", "links": [{"title": "Bombing of Dresden in World War II", "link": "https://wikipedia.org/wiki/Bombing_of_Dresden_in_World_War_II"}]}, {"year": "1946", "text": "ENIAC, the first electronic general-purpose computer, is formally dedicated at the University of Pennsylvania in Philadelphia.", "html": "1946 - <a href=\"https://wikipedia.org/wiki/ENIAC\" title=\"ENIAC\">ENIAC</a>, the first electronic general-purpose computer, is formally dedicated at the <a href=\"https://wikipedia.org/wiki/University_of_Pennsylvania\" title=\"University of Pennsylvania\">University of Pennsylvania</a> in <a href=\"https://wikipedia.org/wiki/Philadelphia\" title=\"Philadelphia\">Philadelphia</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/ENIAC\" title=\"ENIAC\">ENIAC</a>, the first electronic general-purpose computer, is formally dedicated at the <a href=\"https://wikipedia.org/wiki/University_of_Pennsylvania\" title=\"University of Pennsylvania\">University of Pennsylvania</a> in <a href=\"https://wikipedia.org/wiki/Philadelphia\" title=\"Philadelphia\">Philadelphia</a>.", "links": [{"title": "ENIAC", "link": "https://wikipedia.org/wiki/ENIAC"}, {"title": "University of Pennsylvania", "link": "https://wikipedia.org/wiki/University_of_Pennsylvania"}, {"title": "Philadelphia", "link": "https://wikipedia.org/wiki/Philadelphia"}]}, {"year": "1949", "text": "<PERSON> and <PERSON> begin excavations at Cave 1 of the Qumran Caves, where they will eventually discover the first seven Dead Sea Scrolls.", "html": "1949 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> and <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> begin excavations at Cave 1 of the <a href=\"https://wikipedia.org/wiki/Qumran_Caves\" title=\"Qumran Caves\">Qumran Caves</a>, where they will eventually discover the first seven <a href=\"https://wikipedia.org/wiki/Dead_Sea_Scrolls\" title=\"Dead Sea Scrolls\">Dead Sea Scrolls</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> and <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> begin excavations at Cave 1 of the <a href=\"https://wikipedia.org/wiki/Qumran_Caves\" title=\"Qumran Caves\">Qumran Caves</a>, where they will eventually discover the first seven <a href=\"https://wikipedia.org/wiki/Dead_Sea_Scrolls\" title=\"Dead Sea Scrolls\">Dead Sea Scrolls</a>.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "Qumran Caves", "link": "https://wikipedia.org/wiki/Qumran_Caves"}, {"title": "Dead Sea Scrolls", "link": "https://wikipedia.org/wiki/Dead_Sea_Scrolls"}]}, {"year": "1952", "text": "King <PERSON> of the United Kingdom is buried in St George's Chapel, Windsor Castle.", "html": "1952 - King <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> VI\"><PERSON> VI</a> of the United Kingdom is buried in <a href=\"https://wikipedia.org/wiki/St_George%27s_Chapel,_Windsor_Castle\" title=\"St George's Chapel, Windsor Castle\">St George's Chapel, Windsor Castle</a>.", "no_year_html": "King <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"George VI\"><PERSON> VI</a> of the United Kingdom is buried in <a href=\"https://wikipedia.org/wiki/St_George%27s_Chapel,_Windsor_Castle\" title=\"St George's Chapel, Windsor Castle\">St George's Chapel, Windsor Castle</a>.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "St George's Chapel, Windsor Castle", "link": "https://wikipedia.org/wiki/St_George%27s_Chapel,_Windsor_Castle"}]}, {"year": "1954", "text": "Canada and the United States agree to construct the Distant Early Warning Line, a system of radar stations in the far northern Arctic regions of Canada and Alaska.", "html": "1954 - Canada and the United States agree to construct the <a href=\"https://wikipedia.org/wiki/Distant_Early_Warning_Line\" title=\"Distant Early Warning Line\">Distant Early Warning Line</a>, a system of <a href=\"https://wikipedia.org/wiki/Radar\" title=\"Radar\">radar</a> stations in the far northern Arctic regions of Canada and Alaska.", "no_year_html": "Canada and the United States agree to construct the <a href=\"https://wikipedia.org/wiki/Distant_Early_Warning_Line\" title=\"Distant Early Warning Line\">Distant Early Warning Line</a>, a system of <a href=\"https://wikipedia.org/wiki/Radar\" title=\"Radar\">radar</a> stations in the far northern Arctic regions of Canada and Alaska.", "links": [{"title": "Distant Early Warning Line", "link": "https://wikipedia.org/wiki/Distant_Early_Warning_Line"}, {"title": "Radar", "link": "https://wikipedia.org/wiki/Radar"}]}, {"year": "1961", "text": "Sabena Flight 548 crashes in Belgium, killing 73, including the entire United States figure skating team along with several of their coaches and family members.", "html": "1961 - <a href=\"https://wikipedia.org/wiki/Sabena_Flight_548\" title=\"Sabena Flight 548\">Sabena Flight 548</a> crashes in Belgium, killing 73, including the entire United States <a href=\"https://wikipedia.org/wiki/Figure_skating\" title=\"Figure skating\">figure skating</a> team along with several of their coaches and family members.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Sabena_Flight_548\" title=\"Sabena Flight 548\">Sabena Flight 548</a> crashes in Belgium, killing 73, including the entire United States <a href=\"https://wikipedia.org/wiki/Figure_skating\" title=\"Figure skating\">figure skating</a> team along with several of their coaches and family members.", "links": [{"title": "Sabena Flight 548", "link": "https://wikipedia.org/wiki/Sabena_Flight_548"}, {"title": "Figure skating", "link": "https://wikipedia.org/wiki/Figure_skating"}]}, {"year": "1965", "text": "The maple leaf is adopted as the flag of Canada, replacing the Canadian Red Ensign flag.", "html": "1965 - The <a href=\"https://wikipedia.org/wiki/Maple_leaf\" title=\"Maple leaf\">maple leaf</a> is adopted as the <a href=\"https://wikipedia.org/wiki/Flag_of_Canada\" title=\"Flag of Canada\">flag of Canada</a>, replacing the <a href=\"https://wikipedia.org/wiki/Canadian_Red_Ensign\" title=\"Canadian Red Ensign\">Canadian Red Ensign</a> flag.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Maple_leaf\" title=\"Maple leaf\">maple leaf</a> is adopted as the <a href=\"https://wikipedia.org/wiki/Flag_of_Canada\" title=\"Flag of Canada\">flag of Canada</a>, replacing the <a href=\"https://wikipedia.org/wiki/Canadian_Red_Ensign\" title=\"Canadian Red Ensign\">Canadian Red Ensign</a> flag.", "links": [{"title": "Maple leaf", "link": "https://wikipedia.org/wiki/Maple_leaf"}, {"title": "Flag of Canada", "link": "https://wikipedia.org/wiki/Flag_of_Canada"}, {"title": "Canadian Red Ensign", "link": "https://wikipedia.org/wiki/Canadian_Red_Ensign"}]}, {"year": "1970", "text": "A Dominicana de Aviación McDonnell Douglas DC-9 crashes into the Caribbean Sea after takeoff from Las Américas International Airport, killing 102, including members of the Puerto Rico women's national volleyball team and lightweight boxer <PERSON>.", "html": "1970 - A <a href=\"https://wikipedia.org/wiki/Dominicana_de_Aviaci%C3%B3n\" title=\"Dominicana de Aviación\">Dominicana de Aviación</a> <a href=\"https://wikipedia.org/wiki/McDonnell_Douglas_DC-9\" title=\"McDonnell Douglas DC-9\">McDonnell Douglas DC-9</a> <a href=\"https://wikipedia.org/wiki/1970_Dominicana_de_Aviaci%C3%B3n_DC-9_crash\" title=\"1970 Dominicana de Aviación DC-9 crash\">crashes</a> into the <a href=\"https://wikipedia.org/wiki/Caribbean_Sea\" title=\"Caribbean Sea\">Caribbean Sea</a> after takeoff from <a href=\"https://wikipedia.org/wiki/Las_Am%C3%A9ricas_International_Airport\" title=\"Las Américas International Airport\">Las Américas International Airport</a>, killing 102, including members of the <a href=\"https://wikipedia.org/wiki/Puerto_Rico_women%27s_national_volleyball_team\" title=\"Puerto Rico women's national volleyball team\">Puerto Rico women's national volleyball team</a> and lightweight boxer <a href=\"https://wikipedia.org/wiki/<PERSON>(boxer)\" title=\"<PERSON> (boxer)\"><PERSON></a>.", "no_year_html": "A <a href=\"https://wikipedia.org/wiki/Dominicana_de_Aviaci%C3%B3n\" title=\"Dominicana de Aviación\">Dominicana de Aviación</a> <a href=\"https://wikipedia.org/wiki/McDonnell_Douglas_DC-9\" title=\"McDonnell Douglas DC-9\"><PERSON> Douglas DC-9</a> <a href=\"https://wikipedia.org/wiki/1970_Dominicana_de_Aviaci%C3%B3n_DC-9_crash\" title=\"1970 Dominicana de Aviación DC-9 crash\">crashes</a> into the <a href=\"https://wikipedia.org/wiki/Caribbean_Sea\" title=\"Caribbean Sea\">Caribbean Sea</a> after takeoff from <a href=\"https://wikipedia.org/wiki/Las_Am%C3%A9ricas_International_Airport\" title=\"Las Américas International Airport\">Las Américas International Airport</a>, killing 102, including members of the <a href=\"https://wikipedia.org/wiki/Puerto_Rico_women%27s_national_volleyball_team\" title=\"Puerto Rico women's national volleyball team\">Puerto Rico women's national volleyball team</a> and lightweight boxer <a href=\"https://wikipedia.org/wiki/<PERSON>(boxer)\" title=\"<PERSON> (boxer)\"><PERSON></a>.", "links": [{"title": "Dominicana de Aviación", "link": "https://wikipedia.org/wiki/Dominicana_de_Aviaci%C3%B3n"}, {"title": "McDonnell Douglas DC-9", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_DC-9"}, {"title": "1970 Dominicana de Aviación DC-9 crash", "link": "https://wikipedia.org/wiki/1970_Dominicana_de_Aviaci%C3%B3n_DC-9_crash"}, {"title": "Caribbean Sea", "link": "https://wikipedia.org/wiki/Caribbean_Sea"}, {"title": "Las Américas International Airport", "link": "https://wikipedia.org/wiki/Las_Am%C3%A9ricas_International_Airport"}, {"title": "Puerto Rico women's national volleyball team", "link": "https://wikipedia.org/wiki/Puerto_Rico_women%27s_national_volleyball_team"}, {"title": "<PERSON> (boxer)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(boxer)"}]}, {"year": "1971", "text": "The decimalisation of the currencies of the United Kingdom and Ireland is completed on Decimal Day.", "html": "1971 - The <a href=\"https://wikipedia.org/wiki/Decimalisation\" title=\"Decimalisation\">decimalisation</a> of the currencies of the <a href=\"https://wikipedia.org/wiki/Pound_sterling\" title=\"Pound sterling\">United Kingdom</a> and <a href=\"https://wikipedia.org/wiki/Irish_pound\" title=\"Irish pound\">Ireland</a> is completed on <a href=\"https://wikipedia.org/wiki/Decimal_Day\" title=\"Decimal Day\">Decimal Day</a>.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Decimalisation\" title=\"Decimalisation\">decimalisation</a> of the currencies of the <a href=\"https://wikipedia.org/wiki/Pound_sterling\" title=\"Pound sterling\">United Kingdom</a> and <a href=\"https://wikipedia.org/wiki/Irish_pound\" title=\"Irish pound\">Ireland</a> is completed on <a href=\"https://wikipedia.org/wiki/Decimal_Day\" title=\"Decimal Day\">Decimal Day</a>.", "links": [{"title": "Decimalisation", "link": "https://wikipedia.org/wiki/Decimalisation"}, {"title": "Pound sterling", "link": "https://wikipedia.org/wiki/<PERSON>_sterling"}, {"title": "Irish pound", "link": "https://wikipedia.org/wiki/Irish_pound"}, {"title": "Decimal Day", "link": "https://wikipedia.org/wiki/Decimal_Day"}]}, {"year": "1972", "text": "Sound recordings are granted U.S. federal copyright protection for the first time.", "html": "1972 - <a href=\"https://wikipedia.org/wiki/Sound_recording_and_reproduction\" title=\"Sound recording and reproduction\">Sound recordings</a> are granted U.S. federal <a href=\"https://wikipedia.org/wiki/Copyright\" title=\"Copyright\">copyright</a> <a href=\"https://wikipedia.org/wiki/Phonogram_Convention\" class=\"mw-redirect\" title=\"Phonogram Convention\">protection</a> <a href=\"https://wikipedia.org/wiki/Copyright_law_of_the_United_States#Duration_of_copyright\" title=\"Copyright law of the United States\">for the first time</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Sound_recording_and_reproduction\" title=\"Sound recording and reproduction\">Sound recordings</a> are granted U.S. federal <a href=\"https://wikipedia.org/wiki/Copyright\" title=\"Copyright\">copyright</a> <a href=\"https://wikipedia.org/wiki/Phonogram_Convention\" class=\"mw-redirect\" title=\"Phonogram Convention\">protection</a> <a href=\"https://wikipedia.org/wiki/Copyright_law_of_the_United_States#Duration_of_copyright\" title=\"Copyright law of the United States\">for the first time</a>.", "links": [{"title": "Sound recording and reproduction", "link": "https://wikipedia.org/wiki/Sound_recording_and_reproduction"}, {"title": "Copyright", "link": "https://wikipedia.org/wiki/Copyright"}, {"title": "Phonogram Convention", "link": "https://wikipedia.org/wiki/Phonogram_Convention"}, {"title": "Copyright law of the United States", "link": "https://wikipedia.org/wiki/Copyright_law_of_the_United_States#Duration_of_copyright"}]}, {"year": "1972", "text": "<PERSON>, serving as President of Ecuador for the fifth time, is overthrown by the military for the fourth time.", "html": "1972 - <a href=\"https://wikipedia.org/wiki/Jos%C3%A9_Mar%C3%ADa_Velasco_<PERSON>barra\" title=\"<PERSON>\"><PERSON></a>, serving as <a href=\"https://wikipedia.org/wiki/President_of_Ecuador\" title=\"President of Ecuador\">President of Ecuador</a> for the fifth time, is overthrown by the military for the fourth time.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Jos%C3%A9_Mar%C3%ADa_Velasco_Ibarra\" title=\"<PERSON>\"><PERSON></a>, serving as <a href=\"https://wikipedia.org/wiki/President_of_Ecuador\" title=\"President of Ecuador\">President of Ecuador</a> for the fifth time, is overthrown by the military for the fourth time.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Jos%C3%A9_Mar%C3%ADa_Velasco_Ibarra"}, {"title": "President of Ecuador", "link": "https://wikipedia.org/wiki/President_of_Ecuador"}]}, {"year": "1982", "text": "The drilling rig Ocean Ranger sinks during a storm off the coast of Newfoundland, killing 84 workers.", "html": "1982 - The <a href=\"https://wikipedia.org/wiki/Drilling_rig\" title=\"Drilling rig\">drilling rig</a> <i><a href=\"https://wikipedia.org/wiki/Ocean_Ranger\" title=\"Ocean Ranger\">Ocean Ranger</a></i> sinks during a storm off the coast of <a href=\"https://wikipedia.org/wiki/Newfoundland_(island)\" title=\"Newfoundland (island)\">Newfoundland</a>, killing 84 workers.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Drilling_rig\" title=\"Drilling rig\">drilling rig</a> <i><a href=\"https://wikipedia.org/wiki/Ocean_Ranger\" title=\"Ocean Ranger\">Ocean Ranger</a></i> sinks during a storm off the coast of <a href=\"https://wikipedia.org/wiki/Newfoundland_(island)\" title=\"Newfoundland (island)\">Newfoundland</a>, killing 84 workers.", "links": [{"title": "Drilling rig", "link": "https://wikipedia.org/wiki/Drilling_rig"}, {"title": "Ocean Ranger", "link": "https://wikipedia.org/wiki/Ocean_Ranger"}, {"title": "Newfoundland (island)", "link": "https://wikipedia.org/wiki/Newfoundland_(island)"}]}, {"year": "1989", "text": "Soviet-Afghan War: The Soviet Union officially announces that all of its troops have left Afghanistan.", "html": "1989 - <a href=\"https://wikipedia.org/wiki/Soviet%E2%80%93Afghan_War\" title=\"Soviet-Afghan War\">Soviet-Afghan War</a>: The <a href=\"https://wikipedia.org/wiki/Soviet_Union\" title=\"Soviet Union\">Soviet Union</a> officially announces that all of its troops have <a href=\"https://wikipedia.org/wiki/Soviet_withdrawal_from_Afghanistan\" title=\"Soviet withdrawal from Afghanistan\">left</a> <a href=\"https://wikipedia.org/wiki/Afghanistan\" title=\"Afghanistan\">Afghanistan</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Soviet%E2%80%93Afghan_War\" title=\"Soviet-Afghan War\">Soviet-Afghan War</a>: The <a href=\"https://wikipedia.org/wiki/Soviet_Union\" title=\"Soviet Union\">Soviet Union</a> officially announces that all of its troops have <a href=\"https://wikipedia.org/wiki/Soviet_withdrawal_from_Afghanistan\" title=\"Soviet withdrawal from Afghanistan\">left</a> <a href=\"https://wikipedia.org/wiki/Afghanistan\" title=\"Afghanistan\">Afghanistan</a>.", "links": [{"title": "Soviet-Afghan War", "link": "https://wikipedia.org/wiki/Soviet%E2%80%93Afghan_War"}, {"title": "Soviet Union", "link": "https://wikipedia.org/wiki/Soviet_Union"}, {"title": "Soviet withdrawal from Afghanistan", "link": "https://wikipedia.org/wiki/Soviet_withdrawal_from_Afghanistan"}, {"title": "Afghanistan", "link": "https://wikipedia.org/wiki/Afghanistan"}]}, {"year": "1991", "text": "The Visegrád Group, establishing cooperation to move toward free-market systems, is signed by the leaders of Czechoslovakia, Hungary and Poland.", "html": "1991 - The <a href=\"https://wikipedia.org/wiki/Visegr%C3%A1d_Group\" title=\"Visegrád Group\">Visegrád Group</a>, establishing cooperation to move toward <a href=\"https://wikipedia.org/wiki/Free_market_economy\" class=\"mw-redirect\" title=\"Free market economy\">free-market systems</a>, is signed by the leaders of <a href=\"https://wikipedia.org/wiki/Czechoslovakia\" title=\"Czechoslovakia\">Czechoslovakia</a>, Hungary and Poland.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Visegr%C3%A1d_Group\" title=\"Visegrád Group\">Visegrád Group</a>, establishing cooperation to move toward <a href=\"https://wikipedia.org/wiki/Free_market_economy\" class=\"mw-redirect\" title=\"Free market economy\">free-market systems</a>, is signed by the leaders of <a href=\"https://wikipedia.org/wiki/Czechoslovakia\" title=\"Czechoslovakia\">Czechoslovakia</a>, Hungary and Poland.", "links": [{"title": "Visegrád Group", "link": "https://wikipedia.org/wiki/Visegr%C3%A1d_Group"}, {"title": "Free market economy", "link": "https://wikipedia.org/wiki/Free_market_economy"}, {"title": "Czechoslovakia", "link": "https://wikipedia.org/wiki/Czechoslovakia"}]}, {"year": "1992", "text": "Serial killer <PERSON> is sentenced in Milwaukee to 15 terms of life in prison.", "html": "1992 - Serial killer <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> is sentenced in Milwaukee to 15 terms of life in prison.", "no_year_html": "Serial killer <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> is sentenced in Milwaukee to 15 terms of life in prison.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1992", "text": "Air Transport International Flight 805 crashes in Swanton, Ohio, near Toledo Express Airport, killing all four people on board.", "html": "1992 - <a href=\"https://wikipedia.org/wiki/Air_Transport_International_Flight_805\" title=\"Air Transport International Flight 805\">Air Transport International Flight 805</a> crashes in <a href=\"https://wikipedia.org/wiki/Swanton,_Ohio\" title=\"Swanton, Ohio\">Swanton, Ohio</a>, near <a href=\"https://wikipedia.org/wiki/Toledo_Express_Airport\" title=\"Toledo Express Airport\">Toledo Express Airport</a>, killing all four people on board.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Air_Transport_International_Flight_805\" title=\"Air Transport International Flight 805\">Air Transport International Flight 805</a> crashes in <a href=\"https://wikipedia.org/wiki/Swanton,_Ohio\" title=\"Swanton, Ohio\">Swanton, Ohio</a>, near <a href=\"https://wikipedia.org/wiki/Toledo_Express_Airport\" title=\"Toledo Express Airport\">Toledo Express Airport</a>, killing all four people on board.", "links": [{"title": "Air Transport International Flight 805", "link": "https://wikipedia.org/wiki/Air_Transport_International_Flight_805"}, {"title": "Swanton, Ohio", "link": "https://wikipedia.org/wiki/Swanton,_Ohio"}, {"title": "Toledo Express Airport", "link": "https://wikipedia.org/wiki/Toledo_Express_Airport"}]}, {"year": "1996", "text": "At the Xichang Satellite Launch Center in China, a Long March 3B rocket, carrying an Intelsat 708, veers off course and crashes into a rural village after liftoff, killing somewhere between six and 100 people.", "html": "1996 - At the <a href=\"https://wikipedia.org/wiki/Xichang_Satellite_Launch_Center\" title=\"Xichang Satellite Launch Center\">Xichang Satellite Launch Center</a> in China, a <a href=\"https://wikipedia.org/wiki/Long_March_3B\" title=\"Long March 3B\">Long March 3B</a> rocket, carrying an <a href=\"https://wikipedia.org/wiki/Intelsat_708\" title=\"Intelsat 708\">Intelsat 708</a>, veers off course and crashes into a rural village after liftoff, killing somewhere between six and 100 people.", "no_year_html": "At the <a href=\"https://wikipedia.org/wiki/Xichang_Satellite_Launch_Center\" title=\"Xichang Satellite Launch Center\">Xichang Satellite Launch Center</a> in China, a <a href=\"https://wikipedia.org/wiki/Long_March_3B\" title=\"Long March 3B\">Long March 3B</a> rocket, carrying an <a href=\"https://wikipedia.org/wiki/Intelsat_708\" title=\"Intelsat 708\">Intelsat 708</a>, veers off course and crashes into a rural village after liftoff, killing somewhere between six and 100 people.", "links": [{"title": "Xichang Satellite Launch Center", "link": "https://wikipedia.org/wiki/Xichang_Satellite_Launch_Center"}, {"title": "Long March 3B", "link": "https://wikipedia.org/wiki/Long_March_3B"}, {"title": "Intelsat 708", "link": "https://wikipedia.org/wiki/Intelsat_708"}]}, {"year": "1996", "text": "The Embassy of the United States, Athens, is attacked by an antitank rocket, launched by the Revolutionary Organization 17 November.", "html": "1996 - The <a href=\"https://wikipedia.org/wiki/Embassy_of_the_United_States,_Athens\" title=\"Embassy of the United States, Athens\">Embassy of the United States, Athens</a>, is attacked by an antitank rocket, launched by the <a href=\"https://wikipedia.org/wiki/Revolutionary_Organization_17_November\" title=\"Revolutionary Organization 17 November\">Revolutionary Organization 17 November</a>.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Embassy_of_the_United_States,_Athens\" title=\"Embassy of the United States, Athens\">Embassy of the United States, Athens</a>, is attacked by an antitank rocket, launched by the <a href=\"https://wikipedia.org/wiki/Revolutionary_Organization_17_November\" title=\"Revolutionary Organization 17 November\">Revolutionary Organization 17 November</a>.", "links": [{"title": "Embassy of the United States, Athens", "link": "https://wikipedia.org/wiki/Embassy_of_the_United_States,_Athens"}, {"title": "Revolutionary Organization 17 November", "link": "https://wikipedia.org/wiki/Revolutionary_Organization_17_November"}]}, {"year": "2001", "text": "The first draft of the complete human genome is published in Nature.", "html": "2001 - The first draft of the complete <a href=\"https://wikipedia.org/wiki/Human_genome\" title=\"Human genome\">human genome</a> is published in <i><a href=\"https://wikipedia.org/wiki/Nature_(journal)\" title=\"Nature (journal)\">Nature</a></i>.", "no_year_html": "The first draft of the complete <a href=\"https://wikipedia.org/wiki/Human_genome\" title=\"Human genome\">human genome</a> is published in <i><a href=\"https://wikipedia.org/wiki/Nature_(journal)\" title=\"Nature (journal)\">Nature</a></i>.", "links": [{"title": "Human genome", "link": "https://wikipedia.org/wiki/Human_genome"}, {"title": "Nature (journal)", "link": "https://wikipedia.org/wiki/Nature_(journal)"}]}, {"year": "2003", "text": "Protests against the Iraq war take place in over 600 cities worldwide. It is estimated that between eight million and 30 million people participate, making this the largest peace demonstration in history.", "html": "2003 - <a href=\"https://wikipedia.org/wiki/15_February_2003_anti-war_protests\" title=\"15 February 2003 anti-war protests\">Protests against the Iraq war</a> take place in over 600 cities worldwide. It is estimated that between eight million and 30 million people participate, making this the largest peace demonstration in history.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/15_February_2003_anti-war_protests\" title=\"15 February 2003 anti-war protests\">Protests against the Iraq war</a> take place in over 600 cities worldwide. It is estimated that between eight million and 30 million people participate, making this the largest peace demonstration in history.", "links": [{"title": "15 February 2003 anti-war protests", "link": "https://wikipedia.org/wiki/15_February_2003_anti-war_protests"}]}, {"year": "2010", "text": "Two trains collide in the Halle train collision in Halle, Belgium, killing 19 and injuring 171 people.", "html": "2010 - Two trains collide in the <a href=\"https://wikipedia.org/wiki/Halle_train_collision\" title=\"Halle train collision\">Halle train collision</a> in <a href=\"https://wikipedia.org/wiki/Halle,_Belgium\" title=\"Halle, Belgium\">Halle, Belgium</a>, killing 19 and injuring 171 people.", "no_year_html": "Two trains collide in the <a href=\"https://wikipedia.org/wiki/Halle_train_collision\" title=\"Halle train collision\">Halle train collision</a> in <a href=\"https://wikipedia.org/wiki/Halle,_Belgium\" title=\"Halle, Belgium\">Halle, Belgium</a>, killing 19 and injuring 171 people.", "links": [{"title": "Halle train collision", "link": "https://wikipedia.org/wiki/Halle_train_collision"}, {"title": "Halle, Belgium", "link": "https://wikipedia.org/wiki/Halle,_Belgium"}]}, {"year": "2012", "text": "Three hundred and sixty people die in a fire at a Honduran prison in the city of Comayagua.", "html": "2012 - Three hundred and sixty people die in a <a href=\"https://wikipedia.org/wiki/Comayagua_prison_fire\" title=\"Comayagua prison fire\">fire</a> at a <a href=\"https://wikipedia.org/wiki/Honduras\" title=\"Honduras\">Honduran prison</a> in the city of <a href=\"https://wikipedia.org/wiki/Comayagua\" title=\"Comayagua\">Comayagua</a>.", "no_year_html": "Three hundred and sixty people die in a <a href=\"https://wikipedia.org/wiki/Comayagua_prison_fire\" title=\"Comayagua prison fire\">fire</a> at a <a href=\"https://wikipedia.org/wiki/Honduras\" title=\"Honduras\">Honduran prison</a> in the city of <a href=\"https://wikipedia.org/wiki/Comayagua\" title=\"Comayagua\">Comayagua</a>.", "links": [{"title": "Comayagua prison fire", "link": "https://wikipedia.org/wiki/Comayagua_prison_fire"}, {"title": "Honduras", "link": "https://wikipedia.org/wiki/Honduras"}, {"title": "Comayagua", "link": "https://wikipedia.org/wiki/Comayagua"}]}, {"year": "2013", "text": "A meteor explodes over Russia, injuring 1,500 people as a shock wave blows out windows and rocks buildings. This happens unexpectedly only hours before the expected closest ever approach of the larger and unrelated asteroid 2012 DA14.", "html": "2013 - <a href=\"https://wikipedia.org/wiki/Chelyabinsk_meteor\" title=\"Chelyabinsk meteor\">A meteor explodes</a> over Russia, injuring 1,500 people as a shock wave blows out windows and rocks buildings. This happens unexpectedly only hours before the <i>expected</i> closest ever approach of the larger and unrelated asteroid <a href=\"https://wikipedia.org/wiki/2012_DA14\" class=\"mw-redirect\" title=\"2012 DA14\">2012 DA14</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Chelyabinsk_meteor\" title=\"Chelyabinsk meteor\">A meteor explodes</a> over Russia, injuring 1,500 people as a shock wave blows out windows and rocks buildings. This happens unexpectedly only hours before the <i>expected</i> closest ever approach of the larger and unrelated asteroid <a href=\"https://wikipedia.org/wiki/2012_DA14\" class=\"mw-redirect\" title=\"2012 DA14\">2012 DA14</a>.", "links": [{"title": "Chelyabinsk meteor", "link": "https://wikipedia.org/wiki/Chelyabinsk_meteor"}, {"title": "2012 DA14", "link": "https://wikipedia.org/wiki/2012_DA14"}]}, {"year": "2021", "text": "Sixty people drown and hundreds are missing after a boat sinks on the Congo River near the village of Longola Ekoti, Mai-Ndombe Province, Democratic Republic of the Congo.", "html": "2021 - Sixty people drown and hundreds are missing after a boat sinks on the <a href=\"https://wikipedia.org/wiki/2021_Congo_River_disaster\" title=\"2021 Congo River disaster\">Congo River</a> near the village of Longola Ekoti, <a href=\"https://wikipedia.org/wiki/Mai-Ndombe_Province\" title=\"Mai-Ndombe Province\">Mai-Ndombe Province</a>, Democratic Republic of the Congo.", "no_year_html": "Sixty people drown and hundreds are missing after a boat sinks on the <a href=\"https://wikipedia.org/wiki/2021_Congo_River_disaster\" title=\"2021 Congo River disaster\">Congo River</a> near the village of Longola Ekoti, <a href=\"https://wikipedia.org/wiki/Mai-Ndombe_Province\" title=\"Mai-Ndombe Province\">Mai-Ndombe Province</a>, Democratic Republic of the Congo.", "links": [{"title": "2021 Congo River disaster", "link": "https://wikipedia.org/wiki/2021_Congo_River_disaster"}, {"title": "Mai-Ndombe Province", "link": "https://wikipedia.org/wiki/Mai-Ndombe_Province"}]}], "Births": [{"year": "1377", "text": "<PERSON><PERSON><PERSON> of Naples (d. 1414)", "html": "1377 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_of_Naples\" title=\"<PERSON><PERSON><PERSON> of Naples\"><PERSON><PERSON><PERSON> of Naples</a> (d. 1414)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_of_Naples\" title=\"<PERSON><PERSON><PERSON> of Naples\"><PERSON><PERSON><PERSON> of Naples</a> (d. 1414)", "links": [{"title": "<PERSON><PERSON><PERSON> of Naples", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_of_Naples"}]}, {"year": "1458", "text": "<PERSON> the <PERSON>, son of <PERSON> of Russia (d. 1490)", "html": "1458 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON> the <PERSON>\"><PERSON> the <PERSON></a>, son of <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Russia\" title=\"<PERSON> of Russia\"><PERSON> of Russia</a> (d. 1490)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON> the Young\"><PERSON> the <PERSON></a>, son of <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Russia\" title=\"<PERSON> III of Russia\"><PERSON> of Russia</a> (d. 1490)", "links": [{"title": "<PERSON> the Young", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "<PERSON> of Russia", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Russia"}]}, {"year": "1472", "text": "<PERSON><PERSON> the Unfortunate, Italian ruler (d. 1503)", "html": "1472 - <a href=\"https://wikipedia.org/wiki/Pier<PERSON>_the_Unfortunate\" title=\"<PERSON><PERSON> the Unfortunate\"><PERSON><PERSON> the Unfortunate</a>, Italian ruler (d. 1503)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Pier<PERSON>_the_Unfortunate\" title=\"<PERSON><PERSON> the Unfortunate\"><PERSON><PERSON> the Unfortunate</a>, Italian ruler (d. 1503)", "links": [{"title": "<PERSON><PERSON> the Unfortunate", "link": "https://wikipedia.org/wiki/Piero_the_Unfortunate"}]}, {"year": "1506", "text": "<PERSON> of Stolberg, German countess (d. 1580)", "html": "1506 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_Stolberg\" title=\"<PERSON> of Stolberg\"><PERSON> of Stolberg</a>, German countess (d. 1580)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_Stolberg\" title=\"<PERSON> of Stolberg\"><PERSON> of Stolberg</a>, German countess (d. 1580)", "links": [{"title": "<PERSON> of Stolberg", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1519", "text": "<PERSON>, first Spanish Governor of Florida (d. 1574)", "html": "1519 - <a href=\"https://wikipedia.org/wiki/Pedro_Men%C3%A9ndez_de_Avil%C3%A9s\" title=\"<PERSON>\"><PERSON></a>, first Spanish Governor of Florida (d. 1574)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Pedro_Men%C3%A9ndez_de_Avil%C3%A9s\" title=\"<PERSON>\"><PERSON></a>, first Spanish Governor of Florida (d. 1574)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Pedro_Men%C3%A9ndez_de_Avil%C3%A9s"}]}, {"year": "1557", "text": "<PERSON>, Italian composer (d. 1622)", "html": "1557 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian composer (d. 1622)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian composer (d. 1622)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1564", "text": "<PERSON>, Italian astronomer, physicist, and mathematician (d. 1642)", "html": "1564 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>le<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian astronomer, physicist, and mathematician (d. 1642)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Galileo Galilei\"><PERSON></a>, Italian astronomer, physicist, and mathematician (d. 1642)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_Gali<PERSON>i"}]}, {"year": "1612", "text": "<PERSON>, <PERSON><PERSON>, French soldier, founded Montreal (d. 1676)", "html": "1612 - <a href=\"https://wikipedia.org/wiki/<PERSON>,_<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>, <PERSON><PERSON>\"><PERSON>, <PERSON><PERSON></a>, French soldier, founded <a href=\"https://wikipedia.org/wiki/Montreal\" title=\"Montreal\">Montreal</a> (d. 1676)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>,_<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>, <PERSON><PERSON>\"><PERSON>, <PERSON><PERSON></a>, French soldier, founded <a href=\"https://wikipedia.org/wiki/Montreal\" title=\"Montreal\">Montreal</a> (d. 1676)", "links": [{"title": "<PERSON>, <PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>,_<PERSON><PERSON>_<PERSON>_<PERSON>"}, {"title": "Montreal", "link": "https://wikipedia.org/wiki/Montreal"}]}, {"year": "1627", "text": "<PERSON>, Cornish nonconformist minister (d. 1698)", "html": "1627 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>(educator)\" title=\"<PERSON> (educator)\"><PERSON></a>, Cornish nonconformist minister (d. 1698)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>(educator)\" title=\"<PERSON> (educator)\"><PERSON></a>, Cornish nonconformist minister (d. 1698)", "links": [{"title": "<PERSON> (educator)", "link": "https://wikipedia.org/wiki/<PERSON>(educator)"}]}, {"year": "1638", "text": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, Mughal princess and poet (d. 1702)", "html": "1638 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>-un-<PERSON><PERSON>\" title=\"<PERSON><PERSON>-un-<PERSON><PERSON>\"><PERSON><PERSON>-<PERSON>-<PERSON><PERSON></a>, Mughal princess and poet (d. 1702)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>-un-<PERSON><PERSON>\" title=\"<PERSON><PERSON>-un-<PERSON><PERSON>\"><PERSON><PERSON>-<PERSON>-<PERSON><PERSON></a>, Mughal princess and poet (d. 1702)", "links": [{"title": "Zeb-un-Nissa", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>-<PERSON>-<PERSON><PERSON>"}]}, {"year": "1705", "text": "<PERSON><PERSON><PERSON>, French painter (d. 1765)", "html": "1705 - <a href=\"https://wikipedia.org/wiki/<PERSON>-Andr%C3%A9_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, French painter (d. 1765)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>-<PERSON>r%C3%A<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, French painter (d. 1765)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>-Andr%C3%A9_<PERSON>_<PERSON>o"}]}, {"year": "1710", "text": "<PERSON> France (d. 1774)", "html": "1710 - <a href=\"https://wikipedia.org/wiki/Louis_XV_of_France\" class=\"mw-redirect\" title=\"Louis XV of France\"><PERSON> XV of France</a> (d. 1774)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Louis_XV_of_France\" class=\"mw-redirect\" title=\"Louis XV of France\"><PERSON> XV of France</a> (d. 1774)", "links": [{"title": "<PERSON> XV of France", "link": "https://wikipedia.org/wiki/Louis_XV_of_France"}]}, {"year": "1725", "text": "<PERSON>, American surveyor, lawyer, and politician (d. 1794)", "html": "1725 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American surveyor, lawyer, and politician (d. 1794)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American surveyor, lawyer, and politician (d. 1794)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1734", "text": "<PERSON>, American colonel (d. 1802)", "html": "1734 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American colonel (d. 1802)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American colonel (d. 1802)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1739", "text": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, French architect, designed the Paris Bourse (d. 1813)", "html": "1739 - <a href=\"https://wikipedia.org/wiki/Alexandre-Th%C3%A<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON></a>, French architect, designed the <a href=\"https://wikipedia.org/wiki/Paris_Bourse\" class=\"mw-redirect\" title=\"Paris Bourse\">Paris Bourse</a> (d. 1813)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>-<PERSON>h%C3%A9<PERSON><PERSON>_<PERSON>\" title=\"Alexandre<PERSON><PERSON>héod<PERSON>\"><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON></a>, French architect, designed the <a href=\"https://wikipedia.org/wiki/Paris_Bourse\" class=\"mw-redirect\" title=\"Paris Bourse\">Paris Bourse</a> (d. 1813)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Alexandre-Th%C3%A9od<PERSON>_<PERSON>rt"}, {"title": "Paris Bourse", "link": "https://wikipedia.org/wiki/Paris_Bourse"}]}, {"year": "1748", "text": "<PERSON>, English jurist and philosopher (d. 1832)", "html": "1748 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English jurist and philosopher (d. 1832)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English jurist and philosopher (d. 1832)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1759", "text": "<PERSON>, German philologist and critic (d. 1824)", "html": "1759 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"Friedrich August <PERSON>\"><PERSON></a>, German philologist and critic (d. 1824)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"Friedrich August <PERSON>\"><PERSON></a>, German philologist and critic (d. 1824)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_August_<PERSON>"}]}, {"year": "1760", "text": "<PERSON>, Norwegian road manager, land owner, and mill owner (d. 1828)", "html": "1760 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Norwegian road manager, land owner, and mill owner (d. 1828)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Norwegian road manager, land owner, and mill owner (d. 1828)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1760", "text": "<PERSON><PERSON><PERSON>, French composer and educator (d. 1837)", "html": "1760 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>%C3%A<PERSON><PERSON>_Le_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, French composer and educator (d. 1837)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>%C3%A7<PERSON>_Le_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, French composer and educator (d. 1837)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>-<PERSON><PERSON>%C3%A<PERSON><PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1809", "text": "<PERSON>, Belgian geologist and academic (d. 1857)", "html": "1809 - <a href=\"https://wikipedia.org/wiki/Andr%C3%A<PERSON>_<PERSON><PERSON>_(geologist)\" title=\"<PERSON> (geologist)\"><PERSON></a>, Belgian geologist and academic (d. 1857)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Andr%C3%A<PERSON>_<PERSON>_(geologist)\" title=\"<PERSON> (geologist)\"><PERSON></a>, Belgian geologist and academic (d. 1857)", "links": [{"title": "<PERSON> (geologist)", "link": "https://wikipedia.org/wiki/Andr%C3%A<PERSON>_<PERSON><PERSON>_(geologist)"}]}, {"year": "1809", "text": "<PERSON>, American journalist and businessman, co-founded International Harvester (d. 1884)", "html": "1809 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American journalist and businessman, co-founded <a href=\"https://wikipedia.org/wiki/International_Harvester\" title=\"International Harvester\">International Harvester</a> (d. 1884)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American journalist and businessman, co-founded <a href=\"https://wikipedia.org/wiki/International_Harvester\" title=\"International Harvester\">International Harvester</a> (d. 1884)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "International Harvester", "link": "https://wikipedia.org/wiki/International_Harvester"}]}, {"year": "1810", "text": "<PERSON>, American poet, writer, and editor (d. 1883)", "html": "1810 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON><PERSON>_Shindler\" title=\"<PERSON>dler\"><PERSON></a>, American poet, writer, and editor (d. 1883)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>dler\" title=\"<PERSON>dler\"><PERSON></a>, American poet, writer, and editor (d. 1883)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "1811", "text": "<PERSON>, Argentinian journalist and politician, 7th President of Argentina (d. 1888)", "html": "1811 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"Domingo <PERSON>\"><PERSON></a>, Argentinian journalist and politician, 7th <a href=\"https://wikipedia.org/wiki/President_of_Argentina\" title=\"President of Argentina\">President of Argentina</a> (d. 1888)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Argentinian journalist and politician, 7th <a href=\"https://wikipedia.org/wiki/President_of_Argentina\" title=\"President of Argentina\">President of Argentina</a> (d. 1888)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>ient<PERSON>"}, {"title": "President of Argentina", "link": "https://wikipedia.org/wiki/President_of_Argentina"}]}, {"year": "1812", "text": "<PERSON>, American businessman, founded Tiffany & Co. (d. 1902)", "html": "1812 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American businessman, founded <a href=\"https://wikipedia.org/wiki/Tiffany_%26_Co.\" title=\"Tiffany &amp; Co.\">Tiffany &amp; Co.</a> (d. 1902)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American businessman, founded <a href=\"https://wikipedia.org/wiki/Tiffany_%26_Co.\" title=\"Tiffany &amp; Co.\">Tiffany &amp; Co.</a> (d. 1902)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "Tiffany & Co.", "link": "https://wikipedia.org/wiki/Tiffany_%26_Co."}]}, {"year": "1820", "text": "<PERSON>, American suffragist and activist (d. 1906)", "html": "1820 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American suffragist and activist (d. 1906)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American suffragist and activist (d. 1906)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "1825", "text": "<PERSON>, Sr., American lawyer and politician, 29th Mayor of Chicago (d. 1893)", "html": "1825 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Sr.\" class=\"mw-redirect\" title=\"<PERSON>, Sr.\"><PERSON>, Sr.</a>, American lawyer and politician, 29th <a href=\"https://wikipedia.org/wiki/Mayor_of_Chicago\" title=\"Mayor of Chicago\">Mayor of Chicago</a> (d. 1893)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>,_Sr.\" class=\"mw-redirect\" title=\"<PERSON>, Sr.\"><PERSON>, Sr.</a>, American lawyer and politician, 29th <a href=\"https://wikipedia.org/wiki/Mayor_of_Chicago\" title=\"Mayor of Chicago\">Mayor of Chicago</a> (d. 1893)", "links": [{"title": "<PERSON>, Sr.", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Sr."}, {"title": "Mayor of Chicago", "link": "https://wikipedia.org/wiki/Mayor_of_Chicago"}]}, {"year": "1834", "text": "<PERSON><PERSON> <PERSON><PERSON>, Moldavian-Romanian historian, author, and playwright (d. 1901)", "html": "1834 - <a href=\"https://wikipedia.org/wiki/V._A._Urechia\" title=\"V. A. Urechia\">V. A. Urechia</a>, Moldavian-Romanian historian, author, and playwright (d. 1901)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/V._A._Urechia\" title=\"V. A. Urechia\">V. A. Urechia</a>, Moldavian-Romanian historian, author, and playwright (d. 1901)", "links": [{"title": "V. A. Urechia", "link": "https://wikipedia.org/wiki/V._A._Urechia"}]}, {"year": "1835", "text": "<PERSON><PERSON><PERSON>, Greek businessman and philanthropist (d. 1908)", "html": "1835 - <a href=\"https://wikipedia.org/wiki/De<PERSON><PERSON>_V<PERSON>\" class=\"mw-redirect\" title=\"De<PERSON><PERSON> Vike<PERSON>\"><PERSON><PERSON><PERSON></a>, Greek businessman and philanthropist (d. 1908)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/De<PERSON><PERSON>_Vike<PERSON>\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON> Vikelas\"><PERSON><PERSON><PERSON></a>, Greek businessman and philanthropist (d. 1908)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Demetrius_Vikelas"}]}, {"year": "1839", "text": "<PERSON><PERSON>, Bulgarian poet and translator (d. 1877)", "html": "1839 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Bulgarian poet and translator (d. 1877)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Bulgarian poet and translator (d. 1877)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Ray<PERSON>_Zhinzifov"}]}, {"year": "1840", "text": "<PERSON><PERSON><PERSON>, Romanian philosopher, academic, and politician, 23rd Prime Minister of Romania (d. 1917)", "html": "1840 - <a href=\"https://wikipedia.org/wiki/Tit<PERSON>_<PERSON>\" title=\"Tit<PERSON>\"><PERSON><PERSON><PERSON></a>, Romanian philosopher, academic, and politician, 23rd <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Romania\" title=\"Prime Minister of Romania\">Prime Minister of Romania</a> (d. 1917)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Tit<PERSON>_<PERSON>\" title=\"Tit<PERSON>\"><PERSON><PERSON><PERSON></a>, Romanian philosopher, academic, and politician, 23rd <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Romania\" title=\"Prime Minister of Romania\">Prime Minister of Romania</a> (d. 1917)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Tit<PERSON>_<PERSON>"}, {"title": "Prime Minister of Romania", "link": "https://wikipedia.org/wiki/Prime_Minister_of_Romania"}]}, {"year": "1841", "text": "<PERSON>, Brazilian lawyer and politician, 4th President of Brazil (d. 1913)", "html": "1841 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_Campos_Sales\" class=\"mw-redirect\" title=\"<PERSON>s Sales\"><PERSON></a>, Brazilian lawyer and politician, 4th <a href=\"https://wikipedia.org/wiki/President_of_Brazil\" title=\"President of Brazil\">President of Brazil</a> (d. 1913)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_Campos_Sales\" class=\"mw-redirect\" title=\"<PERSON>s Sales\"><PERSON></a>, Brazilian lawyer and politician, 4th <a href=\"https://wikipedia.org/wiki/President_of_Brazil\" title=\"President of Brazil\">President of Brazil</a> (d. 1913)", "links": [{"title": "<PERSON>s Sales", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>s_Sales"}, {"title": "President of Brazil", "link": "https://wikipedia.org/wiki/President_of_Brazil"}]}, {"year": "1845", "text": "<PERSON><PERSON>, American lawyer and politician, 38th United States Secretary of State, Nobel Prize laureate (d. 1937)", "html": "1845 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_Root\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American lawyer and politician, 38th <a href=\"https://wikipedia.org/wiki/United_States_Secretary_of_State\" title=\"United States Secretary of State\">United States Secretary of State</a>, <a href=\"https://wikipedia.org/wiki/Nobel_Peace_Prize\" title=\"Nobel Peace Prize\">Nobel Prize</a> laureate (d. 1937)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American lawyer and politician, 38th <a href=\"https://wikipedia.org/wiki/United_States_Secretary_of_State\" title=\"United States Secretary of State\">United States Secretary of State</a>, <a href=\"https://wikipedia.org/wiki/Nobel_Peace_Prize\" title=\"Nobel Peace Prize\">Nobel Prize</a> laureate (d. 1937)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_Root"}, {"title": "United States Secretary of State", "link": "https://wikipedia.org/wiki/United_States_Secretary_of_State"}, {"title": "Nobel Peace Prize", "link": "https://wikipedia.org/wiki/Nobel_Peace_Prize"}]}, {"year": "1847", "text": "<PERSON>, Austrian composer and educator (d. 1927)", "html": "1847 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(composer)\" title=\"<PERSON> (composer)\"><PERSON></a>, Austrian composer and educator (d. 1927)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(composer)\" title=\"<PERSON> (composer)\"><PERSON></a>, Austrian composer and educator (d. 1927)", "links": [{"title": "<PERSON> (composer)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(composer)"}]}, {"year": "1849", "text": "<PERSON><PERSON>, English surgeon and academic (d. 1925)", "html": "1849 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"Rick<PERSON> Godlee\"><PERSON><PERSON></a>, English surgeon and academic (d. 1925)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"Rick<PERSON> Godlee\"><PERSON><PERSON></a>, English surgeon and academic (d. 1925)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1850", "text": "<PERSON>, Irish mathematician, academic and activist (d. 1922)", "html": "1850 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Irish mathematician, academic and activist (d. 1922)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Irish mathematician, academic and activist (d. 1922)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1851", "text": "<PERSON><PERSON><PERSON>, Romanian mathematician, astronomer, and politician, 55th Romanian Minister of Internal Affairs (d. 1912)", "html": "1851 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Romanian mathematician, astronomer, and politician, 55th <a href=\"https://wikipedia.org/wiki/Ministry_of_Internal_Affairs_(Romania)\" title=\"Ministry of Internal Affairs (Romania)\">Romanian Minister of Internal Affairs</a> (d. 1912)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Romanian mathematician, astronomer, and politician, 55th <a href=\"https://wikipedia.org/wiki/Ministry_of_Internal_Affairs_(Romania)\" title=\"Ministry of Internal Affairs (Romania)\">Romanian Minister of Internal Affairs</a> (d. 1912)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>t"}, {"title": "Ministry of Internal Affairs (Romania)", "link": "https://wikipedia.org/wiki/Ministry_of_Internal_Affairs_(Romania)"}]}, {"year": "1856", "text": "<PERSON>, German psychiatrist and academic (d. 1926)", "html": "1856 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German psychiatrist and academic (d. 1926)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German psychiatrist and academic (d. 1926)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1861", "text": "<PERSON>, Swiss-French physicist and academic, Nobel Prize laureate (d. 1938)", "html": "1861 - <a href=\"https://wikipedia.org/wiki/<PERSON>_%C3%89<PERSON><PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swiss-French physicist and academic, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Physics\" title=\"Nobel Prize in Physics\">Nobel Prize</a> laureate (d. 1938)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_%C3%89<PERSON><PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swiss-French physicist and academic, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Physics\" title=\"Nobel Prize in Physics\">Nobel Prize</a> laureate (d. 1938)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_%C3%89<PERSON><PERSON>_<PERSON>"}, {"title": "Nobel Prize in Physics", "link": "https://wikipedia.org/wiki/Nobel_Prize_in_Physics"}]}, {"year": "1861", "text": "<PERSON>, English mathematician and philosopher (d. 1947)", "html": "1861 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English mathematician and philosopher (d. 1947)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English mathematician and philosopher (d. 1947)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Alfred_North_Whitehead"}]}, {"year": "1873", "text": "<PERSON><PERSON><PERSON>, German-Swedish biochemist and academic, Nobel Prize laureate (d. 1964)", "html": "1873 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>-<PERSON>pin\" title=\"<PERSON>\"><PERSON></a>, German-Swedish biochemist and academic, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Chemistry\" title=\"Nobel Prize in Chemistry\">Nobel Prize</a> laureate (d. 1964)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>pin\" title=\"<PERSON>\"><PERSON></a>, German-Swedish biochemist and academic, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Chemistry\" title=\"Nobel Prize in Chemistry\">Nobel Prize</a> laureate (d. 1964)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>-<PERSON><PERSON>"}, {"title": "Nobel Prize in Chemistry", "link": "https://wikipedia.org/wiki/Nobel_Prize_in_Chemistry"}]}, {"year": "1874", "text": "<PERSON>, Anglo-Irish captain and explorer (d. 1922)", "html": "1874 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Anglo-Irish captain and explorer (d. 1922)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Anglo-Irish captain and explorer (d. 1922)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1883", "text": "<PERSON><PERSON>, English-American author (d. 1959)", "html": "1883 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, English-American author (d. 1959)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, English-American author (d. 1959)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1890", "text": "<PERSON>, German politician (d. 1945)", "html": "1890 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German politician (d. 1945)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German politician (d. 1945)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1892", "text": "<PERSON>, American lieutenant and politician, 1st United States Secretary of Defense (d. 1949)", "html": "1892 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lieutenant and politician, 1st <a href=\"https://wikipedia.org/wiki/United_States_Secretary_of_Defense\" title=\"United States Secretary of Defense\">United States Secretary of Defense</a> (d. 1949)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lieutenant and politician, 1st <a href=\"https://wikipedia.org/wiki/United_States_Secretary_of_Defense\" title=\"United States Secretary of Defense\">United States Secretary of Defense</a> (d. 1949)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "United States Secretary of Defense", "link": "https://wikipedia.org/wiki/United_States_Secretary_of_Defense"}]}, {"year": "1893", "text": "<PERSON>, Polish professional tennis player (d. 1967)", "html": "1893 - <a href=\"https://wikipedia.org/wiki/Roman_Najuch\" title=\"Roman Najuch\"><PERSON></a>, Polish professional tennis player (d. 1967)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Roman_Najuch\" title=\"Roman Najuch\"><PERSON></a>, Polish professional tennis player (d. 1967)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Roman_Najuch"}]}, {"year": "1897", "text": "<PERSON><PERSON><PERSON>, Dutch gymnast and coach (d. 1943)", "html": "1897 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Dutch gymnast and coach (d. 1943)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Dutch gymnast and coach (d. 1943)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1898", "text": "<PERSON><PERSON><PERSON>, Italian actor, singer, and screenwriter (d. 1967)", "html": "1898 - <a href=\"https://wikipedia.org/wiki/Tot%C3%B2\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Italian actor, singer, and screenwriter (d. 1967)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Tot%C3%B2\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Italian actor, singer, and screenwriter (d. 1967)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Tot%C3%B2"}]}, {"year": "1899", "text": "<PERSON>, French composer (d. 1983)", "html": "1899 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French composer (d. 1983)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French composer (d. 1983)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1899", "text": "<PERSON>, Danish-American actress (d. 1985)", "html": "1899 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Danish-American actress (d. 1985)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Danish-American actress (d. 1985)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_Son<PERSON>gaard"}]}, {"year": "1904", "text": "<PERSON>, English painter (d. 1995)", "html": "1904 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English painter (d. 1995)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English painter (d. 1995)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1904", "text": "<PERSON><PERSON>, French cyclist and manager (d. 1983)", "html": "1904 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, French cyclist and manager (d. 1983)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, French cyclist and manager (d. 1983)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1905", "text": "<PERSON>, American composer (d. 1986)", "html": "1905 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American composer (d. 1986)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American composer (d. 1986)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1907", "text": "<PERSON>, French organist and composer (d. 1991)", "html": "1907 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French organist and composer (d. 1991)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French organist and composer (d. 1991)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1907", "text": "<PERSON><PERSON>, American actor (d. 1994)", "html": "1907 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American actor (d. 1994)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American actor (d. 1994)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1908", "text": "<PERSON><PERSON><PERSON>, Canadian lawyer and politician, 38th Mayor of Montreal (d. 1980)", "html": "1908 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Canadian lawyer and politician, 38th <a href=\"https://wikipedia.org/wiki/Mayor_of_Montreal\" title=\"Mayor of Montreal\">Mayor of Montreal</a> (d. 1980)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Canadian lawyer and politician, 38th <a href=\"https://wikipedia.org/wiki/Mayor_of_Montreal\" title=\"Mayor of Montreal\">Mayor of Montreal</a> (d. 1980)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Sart<PERSON>_<PERSON>"}, {"title": "Mayor of Montreal", "link": "https://wikipedia.org/wiki/Mayor_of_Montreal"}]}, {"year": "1909", "text": "<PERSON><PERSON>, Austrian-Dutch humanitarian, helped hide <PERSON> and her family (d. 2010)", "html": "1909 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Austrian-Dutch humanitarian, helped hide <a href=\"https://wikipedia.org/wiki/People_associated_with_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"People associated with <PERSON>\"><PERSON> and her family</a> (d. 2010)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Austrian-Dutch humanitarian, helped hide <a href=\"https://wikipedia.org/wiki/People_associated_with_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"People associated with <PERSON>\"><PERSON> and her family</a> (d. 2010)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Miep_Gies"}, {"title": "People associated with <PERSON>", "link": "https://wikipedia.org/wiki/People_associated_with_<PERSON>_<PERSON>"}]}, {"year": "1910", "text": "<PERSON><PERSON>, Polish nurse and humanitarian, Righteous Gentile (d. 2008)", "html": "1910 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Polish nurse and humanitarian, <a href=\"https://wikipedia.org/wiki/Righteous_Gentile\" class=\"mw-redirect\" title=\"Righteous Gentile\">Righteous Gentile</a> (d. 2008)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Polish nurse and humanitarian, <a href=\"https://wikipedia.org/wiki/Righteous_Gentile\" class=\"mw-redirect\" title=\"Righteous Gentile\">Righteous Gentile</a> (d. 2008)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>ler"}, {"title": "Righteous Gentile", "link": "https://wikipedia.org/wiki/Righteous_Gentile"}]}, {"year": "1912", "text": "<PERSON>, Hungarian-English journalist and author (d. 1987)", "html": "1912 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Hungarian-English journalist and author (d. 1987)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Hungarian-English journalist and author (d. 1987)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1913", "text": "<PERSON>, Austrian chess player (d. 1997)", "html": "1913 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Austrian chess player (d. 1997)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Austrian chess player (d. 1997)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1914", "text": "<PERSON>, American lawyer and politician (d. 1972)", "html": "1914 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and politician (d. 1972)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and politician (d. 1972)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1914", "text": "<PERSON>, American actor (d. 2010)", "html": "1914 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(actor)\" title=\"<PERSON> (actor)\"><PERSON></a>, American actor (d. 2010)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>(actor)\" title=\"<PERSON> (actor)\"><PERSON></a>, American actor (d. 2010)", "links": [{"title": "<PERSON> (actor)", "link": "https://wikipedia.org/wiki/<PERSON>(actor)"}]}, {"year": "1916", "text": "<PERSON>, American actress (d. 1999)", "html": "1916 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress (d. 1999)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress (d. 1999)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1918", "text": "<PERSON>, American actor and photographer (d. 2013)", "html": "1918 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor and photographer (d. 2013)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor and photographer (d. 2013)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1918", "text": "<PERSON>, American singer-songwriter and guitarist (d. 2009)", "html": "1918 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and guitarist (d. 2009)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and guitarist (d. 2009)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1919", "text": "<PERSON><PERSON>, American baseball player and manager (d. 2013)", "html": "1919 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American baseball player and manager (d. 2013)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American baseball player and manager (d. 2013)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1920", "text": "<PERSON><PERSON><PERSON>, American soldier, lawyer, and politician, 62nd Governor of Massachusetts (d. 1997)", "html": "1920 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_Peabody\" title=\"Endicott Peabody\"><PERSON><PERSON><PERSON></a>, American soldier, lawyer, and politician, 62nd <a href=\"https://wikipedia.org/wiki/Governor_of_Massachusetts\" title=\"Governor of Massachusetts\">Governor of Massachusetts</a> (d. 1997)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Endicott_Peabody\" title=\"Endicott Peabody\"><PERSON><PERSON><PERSON></a>, American soldier, lawyer, and politician, 62nd <a href=\"https://wikipedia.org/wiki/Governor_of_Massachusetts\" title=\"Governor of Massachusetts\">Governor of Massachusetts</a> (d. 1997)", "links": [{"title": "Endicott Peabody", "link": "https://wikipedia.org/wiki/Endicott_Peabody"}, {"title": "Governor of Massachusetts", "link": "https://wikipedia.org/wiki/Governor_of_Massachusetts"}]}, {"year": "1920", "text": "<PERSON><PERSON>, Japanese Go player (d. 2010)", "html": "1920 - <a href=\"https://wikipedia.org/wiki/E<PERSON>_Sakata\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Japanese <a href=\"https://wikipedia.org/wiki/Go_(game)\" title=\"Go (game)\">Go</a> player (d. 2010)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_Sakata\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Japanese <a href=\"https://wikipedia.org/wiki/Go_(game)\" title=\"Go (game)\">Go</a> player (d. 2010)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Eio_Sakata"}, {"title": "Go (game)", "link": "https://wikipedia.org/wiki/Go_(game)"}]}, {"year": "1921", "text": "<PERSON>, American chemist and plant scientist, (d. 2017)", "html": "1921 - <a href=\"https://wikipedia.org/wiki/Norman_<PERSON>_<PERSON>\" title=\"Norman <PERSON>\"><PERSON> <PERSON></a>, American chemist and plant scientist, (d. 2017)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Norman_<PERSON>_<PERSON>\" title=\"Norman <PERSON>\"><PERSON> <PERSON></a>, American chemist and plant scientist, (d. 2017)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>o"}]}, {"year": "1922", "text": "<PERSON>, Swedish-American lawyer and politician (d. 2017)", "html": "1922 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swedish-American lawyer and politician (d. 2017)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swedish-American lawyer and politician (d. 2017)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "1923", "text": "<PERSON><PERSON>, Soviet-Russian activist (d. 2011)", "html": "1923 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Soviet-Russian activist (d. 2011)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Soviet-Russian activist (d. 2011)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1924", "text": "<PERSON>, American director and producer (d. 2014)", "html": "1924 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American director and producer (d. 2014)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American director and producer (d. 2014)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1925", "text": "<PERSON><PERSON>, American pediatrician", "html": "1925 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American pediatrician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON> D<PERSON> Ferguson\"><PERSON><PERSON></a>, American pediatrician", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1927", "text": "<PERSON>, English actor and director", "html": "1927 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(director)\" title=\"<PERSON> (director)\"><PERSON></a>, English actor and director", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(director)\" title=\"<PERSON> (director)\"><PERSON></a>, English actor and director", "links": [{"title": "<PERSON> (director)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(director)"}]}, {"year": "1927", "text": "<PERSON>, American actor and comedian (d. 2008)", "html": "1927 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor and comedian (d. 2008)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor and comedian (d. 2008)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1927", "text": "<PERSON><PERSON><PERSON>, Israeli rabbi and scholar (d. 2013)", "html": "1927 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_Neuwirth\" title=\"<PERSON><PERSON><PERSON>euwirth\"><PERSON><PERSON><PERSON></a>, Israeli rabbi and scholar (d. 2013)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>hos<PERSON>_Neuwirth\" title=\"<PERSON><PERSON><PERSON> Neuwirth\"><PERSON><PERSON><PERSON></a>, Israeli rabbi and scholar (d. 2013)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Yehos<PERSON>_<PERSON>euwirth"}]}, {"year": "1928", "text": "<PERSON>, American composer, conductor, and educator (d. 2014)", "html": "1928 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American composer, conductor, and educator (d. 2014)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American composer, conductor, and educator (d. 2014)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1929", "text": "<PERSON>, English racing driver and businessman (d. 1975)", "html": "1929 - <a href=\"https://wikipedia.org/wiki/Graham_<PERSON>\" title=\"Graham Hill\"><PERSON></a>, English racing driver and businessman (d. 1975)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Graham_<PERSON>\" title=\"Graham Hill\"><PERSON></a>, English racing driver and businessman (d. 1975)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Graham_Hill"}]}, {"year": "1929", "text": "<PERSON>, American economist and politician, 12th United States Secretary of Defense (d. 2014)", "html": "1929 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American economist and politician, 12th <a href=\"https://wikipedia.org/wiki/United_States_Secretary_of_Defense\" title=\"United States Secretary of Defense\">United States Secretary of Defense</a> (d. 2014)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American economist and politician, 12th <a href=\"https://wikipedia.org/wiki/United_States_Secretary_of_Defense\" title=\"United States Secretary of Defense\">United States Secretary of Defense</a> (d. 2014)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>"}, {"title": "United States Secretary of Defense", "link": "https://wikipedia.org/wiki/United_States_Secretary_of_Defense"}]}, {"year": "1930", "text": "<PERSON>, Australian poet and academic (d. 2020)", "html": "1930 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian poet and academic (d. 2020)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian poet and academic (d. 2020)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1931", "text": "<PERSON>, English actress", "html": "1931 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actress", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1931", "text": "<PERSON>, English journalist and author", "html": "1931 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(journalist)\" title=\"<PERSON> (journalist)\"><PERSON></a>, English journalist and author", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(journalist)\" title=\"<PERSON> (journalist)\"><PERSON></a>, English journalist and author", "links": [{"title": "<PERSON> (journalist)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(journalist)"}]}, {"year": "1934", "text": "<PERSON>,  English footballer and manager (d. 1983)", "html": "1934 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English footballer and manager (d. 1983)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English footballer and manager (d. 1983)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1934", "text": "<PERSON>, Australian television host and actor (d. 2005)", "html": "1934 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian television host and actor (d. 2005)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian television host and actor (d. 2005)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1934", "text": "<PERSON><PERSON>, Swiss computer scientist, created the Pascal programming language (d. 2024)", "html": "1934 - <a href=\"https://wikipedia.org/wiki/<PERSON>laus_Wirth\" title=\"<PERSON><PERSON> Wirth\"><PERSON><PERSON></a>, Swiss computer scientist, created the <a href=\"https://wikipedia.org/wiki/Pascal_(programming_language)\" title=\"Pascal (programming language)\">Pascal programming language</a> (d. 2024)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_Wirth\" title=\"<PERSON><PERSON> Wirth\"><PERSON><PERSON></a>, Swiss computer scientist, created the <a href=\"https://wikipedia.org/wiki/Pascal_(programming_language)\" title=\"Pascal (programming language)\">Pascal programming language</a> (d. 2024)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_Wirth"}, {"title": "Pascal (programming language)", "link": "https://wikipedia.org/wiki/Pascal_(programming_language)"}]}, {"year": "1934", "text": "<PERSON>, American football player and minister (d. 2014)", "html": "1934 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player and minister (d. 2014)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player and minister (d. 2014)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1935", "text": "<PERSON>, American journalist and author", "html": "1935 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American journalist and author", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American journalist and author", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1935", "text": "<PERSON>, American lieutenant, engineer, and astronaut (d. 1967)", "html": "1935 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lieutenant, engineer, and astronaut (d. 1967)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lieutenant, engineer, and astronaut (d. 1967)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1935", "text": "<PERSON>, American football player (d. 2008)", "html": "1935 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player (d. 2008)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player (d. 2008)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1937", "text": "<PERSON>, American author (d. 2008)", "html": "1937 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author (d. 2008)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author (d. 2008)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1937", "text": "<PERSON><PERSON>, Dutch footballer (d. 2011)", "html": "1937 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Dutch footballer (d. 2011)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Dutch footballer (d. 2011)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1940", "text": "<PERSON><PERSON><PERSON>, Turkish journalist and politician, 45th Turkish Minister of Foreign Affairs (d. 2007)", "html": "1940 - <a href=\"https://wikipedia.org/wiki/%C4%B0smail_Cem_%C4%B0pek%C3%A7i\" class=\"mw-redirect\" title=\"İsmail Cem İpekçi\">İsmail Cem İpe<PERSON>ç<PERSON></a>, Turkish journalist and politician, 45th <a href=\"https://wikipedia.org/wiki/List_of_Ministers_of_Foreign_Affairs_(Turkey)\" class=\"mw-redirect\" title=\"List of Ministers of Foreign Affairs (Turkey)\">Turkish Minister of Foreign Affairs</a> (d. 2007)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/%C4%B0smail_Cem_%C4%B0pek%C3%A7i\" class=\"mw-redirect\" title=\"İsmail Cem İpekçi\"><PERSON><PERSON>il Cem <PERSON></a>, Turkish journalist and politician, 45th <a href=\"https://wikipedia.org/wiki/List_of_Ministers_of_Foreign_Affairs_(Turkey)\" class=\"mw-redirect\" title=\"List of Ministers of Foreign Affairs (Turkey)\">Turkish Minister of Foreign Affairs</a> (d. 2007)", "links": [{"title": "İsmail Cem İpekçi", "link": "https://wikipedia.org/wiki/%C4%B0smail_Cem_%C4%B0pek%C3%A7i"}, {"title": "List of Ministers of Foreign Affairs (Turkey)", "link": "https://wikipedia.org/wiki/List_of_Ministers_of_Foreign_Affairs_(Turkey)"}]}, {"year": "1940", "text": "<PERSON><PERSON><PERSON>, Indonesian journalist and politician, 9th Vice President of Indonesia (d. 2024)", "html": "1940 - <a href=\"https://wikipedia.org/wiki/Hamzah_Haz\" title=\"Ham<PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Indonesian journalist and politician, 9th <a href=\"https://wikipedia.org/wiki/Vice_President_of_Indonesia\" title=\"Vice President of Indonesia\">Vice President of Indonesia</a> (d. 2024)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Hamzah_<PERSON>z\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Indonesian journalist and politician, 9th <a href=\"https://wikipedia.org/wiki/Vice_President_of_Indonesia\" title=\"Vice President of Indonesia\">Vice President of Indonesia</a> (d. 2024)", "links": [{"title": "Hamzah Haz", "link": "https://wikipedia.org/wiki/Hamzah_Haz"}, {"title": "Vice President of Indonesia", "link": "https://wikipedia.org/wiki/Vice_President_of_Indonesia"}]}, {"year": "1941", "text": "<PERSON><PERSON><PERSON>, Brazilian actress", "html": "1941 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Brazilian actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Brazilian actress", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1941", "text": "<PERSON>, American songwriter and producer", "html": "1941 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American songwriter and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American songwriter and producer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1944", "text": "<PERSON>, English musician and songwriter", "html": "1944 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English musician and songwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English musician and songwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1945", "text": "<PERSON>, American-Australian author and poet", "html": "1945 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American-Australian author and poet", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American-Australian author and poet", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1945", "text": "<PERSON>, American author and academic", "html": "1945 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author and academic", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author and academic", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1946", "text": "<PERSON>, English civil servant and politician, Secretary of State for International Development", "html": "1946 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English civil servant and politician, <a href=\"https://wikipedia.org/wiki/Secretary_of_State_for_International_Development\" class=\"mw-redirect\" title=\"Secretary of State for International Development\">Secretary of State for International Development</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English civil servant and politician, <a href=\"https://wikipedia.org/wiki/Secretary_of_State_for_International_Development\" class=\"mw-redirect\" title=\"Secretary of State for International Development\">Secretary of State for International Development</a>", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Secretary of State for International Development", "link": "https://wikipedia.org/wiki/Secretary_of_State_for_International_Development"}]}, {"year": "1946", "text": "<PERSON>, American author, poet, and actor (d. 2015)", "html": "1946 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author, poet, and actor (d. 2015)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author, poet, and actor (d. 2015)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1947", "text": "<PERSON>, American composer", "html": "1947 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(composer)\" title=\"<PERSON> (composer)\"><PERSON></a>, American composer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>(composer)\" title=\"<PERSON> (composer)\"><PERSON></a>, American composer", "links": [{"title": "<PERSON> (composer)", "link": "https://wikipedia.org/wiki/<PERSON>_(composer)"}]}, {"year": "1947", "text": "<PERSON><PERSON>, American model and actress", "html": "1947 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American model and actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American model and actress", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1948", "text": "<PERSON>, Swedish-American cartoonist and critic", "html": "1948 - <a href=\"https://wikipedia.org/wiki/Art_S<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swedish-American cartoonist and critic", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_S<PERSON>\" title=\"<PERSON>man\"><PERSON></a>, Swedish-American cartoonist and critic", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>gelman"}]}, {"year": "1949", "text": "<PERSON>, American football player", "html": "1949 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(quarterback)\" title=\"<PERSON> (quarterback)\"><PERSON></a>, American football player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>(quarterback)\" title=\"<PERSON> (quarterback)\"><PERSON></a>, American football player", "links": [{"title": "<PERSON> (quarterback)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(quarterback)"}]}, {"year": "1951", "text": "<PERSON><PERSON>, Finnish racing driver", "html": "1951 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>%C3%A9n\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Finnish racing driver", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>%C3%A9n\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Finnish racing driver", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Markku_Al%C3%A9n"}]}, {"year": "1951", "text": "<PERSON>, American singer-songwriter and actress", "html": "1951 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and actress", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1951", "text": "<PERSON>, English-American actress, producer, and jewelry designer", "html": "1951 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(actress)\" title=\"<PERSON> (actress)\"><PERSON></a>, English-American actress, producer, and jewelry designer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>(actress)\" title=\"<PERSON> (actress)\"><PERSON></a>, English-American actress, producer, and jewelry designer", "links": [{"title": "<PERSON> (actress)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>(actress)"}]}, {"year": "1952", "text": "<PERSON><PERSON>, Serbian politician, 4th President of Serbia", "html": "1952 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>%C4%87\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Serbian politician, 4th <a href=\"https://wikipedia.org/wiki/President_of_Serbia\" title=\"President of Serbia\">President of Serbia</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>%C4%87\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Serbian politician, 4th <a href=\"https://wikipedia.org/wiki/President_of_Serbia\" title=\"President of Serbia\">President of Serbia</a>", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>%C4%87"}, {"title": "President of Serbia", "link": "https://wikipedia.org/wiki/President_of_Serbia"}]}, {"year": "1952", "text": "<PERSON>, Russian actor and director (d. 2013)", "html": "1952 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian actor and director (d. 2013)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian actor and director (d. 2013)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1953", "text": "<PERSON>, English footballer and manager", "html": "1953 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English footballer and manager", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English footballer and manager", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1953", "text": "<PERSON>, American actress and producer", "html": "1953 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress and producer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1954", "text": "<PERSON>, American animator, producer, and screenwriter", "html": "1954 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American animator, producer, and screenwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American animator, producer, and screenwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1955", "text": "<PERSON>, American model, agent, and author", "html": "1955 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American model, agent, and author", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American model, agent, and author", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1955", "text": "<PERSON>, American actor", "html": "1955 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1956", "text": "<PERSON>, Barbadian cricketer and coach", "html": "1956 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Barbadian cricketer and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Barbadian cricketer and coach", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1956", "text": "<PERSON>, Swedish comedian", "html": "1956 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swedish comedian", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swedish comedian", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Ann_<PERSON>"}]}, {"year": "1958", "text": "<PERSON><PERSON><PERSON><PERSON>, Canadian author", "html": "1958 - <a href=\"https://wikipedia.org/wiki/Chryst<PERSON>_<PERSON>\" title=\"<PERSON>ry<PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Canadian author", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Chryst<PERSON>_<PERSON>\" title=\"<PERSON>ry<PERSON><PERSON> Brou<PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Canadian author", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>ry<PERSON><PERSON>_<PERSON>t"}]}, {"year": "1958", "text": "<PERSON>, Canadian ice hockey player", "html": "1958 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1958", "text": "<PERSON>, American singer-songwriter", "html": "1958 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>(singer)\" title=\"<PERSON> (singer)\"><PERSON></a>, American singer-songwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>(singer)\" title=\"<PERSON> (singer)\"><PERSON></a>, American singer-songwriter", "links": [{"title": "<PERSON> (singer)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>(singer)"}]}, {"year": "1959", "text": "<PERSON>, English singer-songwriter and musician", "html": "1959 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English singer-songwriter and musician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English singer-songwriter and musician", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1959", "text": "<PERSON>, American actor", "html": "1959 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1959", "text": "<PERSON>, Canadian ice hockey player and sportscaster", "html": "1959 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player and sportscaster", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player and sportscaster", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1959", "text": "<PERSON>, Ecuadorian wrestler and sportscaster", "html": "1959 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Ecuadorian wrestler and sportscaster", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Ecuadorian wrestler and sportscaster", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1960", "text": "<PERSON>, American football player", "html": "1960 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1960", "text": "<PERSON><PERSON>, New Zealand rugby player (d. 2012)", "html": "1960 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, New Zealand rugby player (d. 2012)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, New Zealand rugby player (d. 2012)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1962", "text": "<PERSON>, Montenegrin politician, 29th Prime Minister of Montenegro", "html": "1962 - <a href=\"https://wikipedia.org/wiki/<PERSON>_%C4%90ukanovi%C4%87\" title=\"<PERSON>\"><PERSON></a>, Montenegrin politician, 29th <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Montenegro\" title=\"Prime Minister of Montenegro\">Prime Minister of Montenegro</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_%C4%90ukanovi%C4%87\" title=\"<PERSON>\"><PERSON></a>, Montenegrin politician, 29th <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Montenegro\" title=\"Prime Minister of Montenegro\">Prime Minister of Montenegro</a>", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Milo_%C4%90ukanovi%C4%87"}, {"title": "Prime Minister of Montenegro", "link": "https://wikipedia.org/wiki/Prime_Minister_of_Montenegro"}]}, {"year": "1963", "text": "<PERSON>, American actor, comedian, and politician", "html": "1963 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor, comedian, and politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor, comedian, and politician", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1964", "text": "<PERSON>, American comedian and actor (d. 1997)", "html": "1964 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American comedian and actor (d. 1997)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American comedian and actor (d. 1997)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1964", "text": "<PERSON><PERSON>, American engineer and astronaut", "html": "1964 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American engineer and astronaut", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American engineer and astronaut", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "1964", "text": "<PERSON>, American basketball player and coach", "html": "1964 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player and coach", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1965", "text": "<PERSON>, South African cricketer", "html": "1965 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, South African cricketer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, South African cricketer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1967", "text": "<PERSON>, Canadian singer-songwriter and producer", "html": "1967 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Jane Child\"><PERSON></a>, Canadian singer-songwriter and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_Child\" title=\"Jane Child\"><PERSON></a>, Canadian singer-songwriter and producer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1967", "text": "<PERSON>, English academic and politician", "html": "1967 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English academic and politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English academic and politician", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1967", "text": "<PERSON>, Canadian ice hockey player and sportscaster", "html": "1967 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player and sportscaster", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player and sportscaster", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1969", "text": "<PERSON><PERSON>, American rapper and producer", "html": "1969 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_(rapper)\" title=\"<PERSON><PERSON> (rapper)\"><PERSON><PERSON></a>, American rapper and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_(rapper)\" title=\"<PERSON><PERSON> (rapper)\"><PERSON><PERSON></a>, American rapper and producer", "links": [{"title": "<PERSON><PERSON> (rapper)", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_(rapper)"}]}, {"year": "1970", "text": "<PERSON>, American artist and activist", "html": "1970 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American artist and activist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American artist and activist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1971", "text": "<PERSON>, American actress, voice artist, producer, and screenwriter", "html": "1971 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress, voice artist, producer, and screenwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress, voice artist, producer, and screenwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1971", "text": "<PERSON>, American actress, director, and producer", "html": "1971 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%27Connor\" title=\"<PERSON>\"><PERSON></a>, American actress, director, and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%27Connor\" title=\"<PERSON>\"><PERSON></a>, American actress, director, and producer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>%27Connor"}]}, {"year": "1972", "text": "<PERSON><PERSON><PERSON><PERSON><PERSON>, Czech ice hockey player", "html": "1972 - <a href=\"https://wikipedia.org/wiki/Jarom%C3%ADr_J%C3%A1gr\" title=\"Jaromír Jágr\"><PERSON><PERSON><PERSON><PERSON><PERSON></a>, Czech ice hockey player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Jarom%C3%ADr_J%C3%A1gr\" title=\"Jaromír J<PERSON>gr\"><PERSON><PERSON><PERSON><PERSON><PERSON></a>, Czech ice hockey player", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Jarom%C3%ADr_J%C3%A1gr"}]}, {"year": "1973", "text": "<PERSON><PERSON><PERSON>, Czech skier", "html": "1973 - <a href=\"https://wikipedia.org/wiki/Kate%C5%99<PERSON>_<PERSON>%C3%A1\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Czech skier", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Kate%C5%<PERSON><PERSON>_<PERSON>%C3%A1\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Czech skier", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Kate%C5%99<PERSON>_<PERSON><PERSON>%C3%A1"}]}, {"year": "1973", "text": "<PERSON>, American swimmer", "html": "1973 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, American swimmer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, American swimmer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1973", "text": "<PERSON>, Australian actress", "html": "1973 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian actress", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1974", "text": "<PERSON>, American actress, director, and screenwriter", "html": "1974 - <a href=\"https://wikipedia.org/wiki/<PERSON>_July\" title=\"<PERSON> July\"><PERSON> July</a>, American actress, director, and screenwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_July\" title=\"<PERSON> July\"><PERSON> July</a>, American actress, director, and screenwriter", "links": [{"title": "<PERSON> July", "link": "https://wikipedia.org/wiki/Miranda_July"}]}, {"year": "1974", "text": "<PERSON><PERSON><PERSON>, Venezuelan baseball player", "html": "1974 - <a href=\"https://wikipedia.org/wiki/Ugueth_Urbina\" title=\"Ugueth Urbina\"><PERSON><PERSON><PERSON> Urbina</a>, Venezuelan baseball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Ugueth_Urbina\" title=\"Ugueth Urbina\"><PERSON><PERSON><PERSON> Urb<PERSON></a>, Venezuelan baseball player", "links": [{"title": "Ugueth Urbina", "link": "https://wikipedia.org/wiki/Ugueth_Urbina"}]}, {"year": "1974", "text": "<PERSON>, Austrian racing driver and businessman", "html": "1974 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Austrian racing driver and businessman", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Austrian racing driver and businessman", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1975", "text": "<PERSON>, Canadian ice hockey player and coach", "html": "1975 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player and coach", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1975", "text": "<PERSON><PERSON><PERSON><PERSON>, Canadian-French ice hockey player", "html": "1975 - <a href=\"https://wikipedia.org/wiki/S%C3%A<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Canadian-French ice hockey player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/S%C3%A<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Canadian-French ice hockey player", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/S%C3%<PERSON><PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1975", "text": "<PERSON><PERSON><PERSON>, Dutch sprinter", "html": "1975 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Dutch sprinter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Dutch sprinter", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1975", "text": "<PERSON><PERSON><PERSON>, American animator, producer, screenwriter, and actor", "html": "1975 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON> Small\"><PERSON><PERSON><PERSON></a>, American animator, producer, screenwriter, and actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American animator, producer, screenwriter, and actor", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1976", "text": "<PERSON>, American singer-songwriter", "html": "1976 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1976", "text": "<PERSON><PERSON><PERSON>, Spanish cyclist", "html": "1976 - <a href=\"https://wikipedia.org/wiki/%C3%93sca<PERSON>_<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Spanish cyclist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/%C3%93sca<PERSON>_<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Spanish cyclist", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/%C3%93scar_<PERSON><PERSON><PERSON>"}]}, {"year": "1976", "text": "<PERSON>, American musician and songwriter", "html": "1976 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>.\" title=\"<PERSON>.\"><PERSON>.</a>, American musician and songwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>.\" title=\"<PERSON>.\"><PERSON>.</a>, American musician and songwriter", "links": [{"title": "<PERSON>.", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>."}]}, {"year": "1977", "text": "<PERSON><PERSON>, Venezuelan baseball player", "html": "1977 - <a href=\"https://wikipedia.org/wiki/%C3%81lex_Gonz%C3%<PERSON><PERSON><PERSON>_(shortstop,_born_1977)\" title=\"<PERSON><PERSON> (shortstop, born 1977)\"><PERSON><PERSON></a>, Venezuelan baseball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/%C3%81lex_Gonz%C3%<PERSON><PERSON><PERSON>_(shortstop,_born_1977)\" title=\"<PERSON><PERSON> (shortstop, born 1977)\"><PERSON><PERSON></a>, Venezuelan baseball player", "links": [{"title": "<PERSON><PERSON> (shortstop, born 1977)", "link": "https://wikipedia.org/wiki/%C3%81lex_Gonz%C3%<PERSON><PERSON><PERSON>_(shortstop,_born_1977)"}]}, {"year": "1977", "text": "<PERSON>, Slovak ice hockey player", "html": "1977 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%BD\" title=\"<PERSON>\"><PERSON></a>, Slovak ice hockey player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%BD\" title=\"<PERSON>\"><PERSON></a>, Slovak ice hockey player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%BD"}]}, {"year": "1979", "text": "<PERSON><PERSON>, New Zealand cricketer", "html": "1979 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, New Zealand cricketer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, New Zealand cricketer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1979", "text": "<PERSON>, New Zealand cricketer", "html": "1979 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>(cricketer)\" title=\"<PERSON> (cricketer)\"><PERSON></a>, New Zealand cricketer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>(cricketer)\" title=\"<PERSON> (cricketer)\"><PERSON></a>, New Zealand cricketer", "links": [{"title": "<PERSON> (cricketer)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>(cricketer)"}]}, {"year": "1980", "text": "<PERSON>, American singer-songwriter", "html": "1980 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1981", "text": "<PERSON><PERSON><PERSON><PERSON>, Brazilian footballer", "html": "1981 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON> Go<PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Brazilian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Brazilian footballer", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>urel<PERSON>_Gomes"}]}, {"year": "1981", "text": "<PERSON>, American singer-songwriter and guitarist", "html": "1981 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and guitarist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and guitarist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1981", "text": "<PERSON>, Kenyan runner", "html": "1981 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Kenyan runner", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Kenyan runner", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>oo"}]}, {"year": "1981", "text": "<PERSON>, Mexican footballer", "html": "1981 - <a href=\"https://wikipedia.org/wiki/Diego_Mart%C3%<PERSON><PERSON><PERSON>_(Mexican_footballer,_born_1981)\" title=\"<PERSON> (Mexican footballer, born 1981)\"><PERSON></a>, Mexican footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Diego_Mart%C3%<PERSON><PERSON><PERSON>_(Mexican_footballer,_born_1981)\" title=\"<PERSON> (Mexican footballer, born 1981)\"><PERSON></a>, Mexican footballer", "links": [{"title": "<PERSON> (Mexican footballer, born 1981)", "link": "https://wikipedia.org/wiki/Diego_Mart%C3%<PERSON><PERSON><PERSON>_(Mexican_footballer,_born_1981)"}]}, {"year": "1981", "text": "<PERSON><PERSON><PERSON>, Canadian singer and songwriter", "html": "1981 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Canadian singer and songwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Canadian singer and songwriter", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Vivek_<PERSON>"}]}, {"year": "1982", "text": "<PERSON><PERSON><PERSON>, American basketball player", "html": "1982 - <a href=\"https://wikipedia.org/wiki/Shame<PERSON>_<PERSON>\" title=\"Shame<PERSON> Christon\"><PERSON><PERSON><PERSON></a>, American basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Shame<PERSON>_<PERSON>\" title=\"Shame<PERSON> Christon\"><PERSON><PERSON><PERSON></a>, American basketball player", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>on"}]}, {"year": "1982", "text": "<PERSON>, Filipino basketball player", "html": "1982 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Filipino basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Filipino basketball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1983", "text": "<PERSON>, American basketball player", "html": "1983 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1983", "text": "<PERSON>, Scottish footballer", "html": "1983 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(footballer)\" title=\"<PERSON> (footballer)\"><PERSON></a>, Scottish footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(footballer)\" title=\"<PERSON> (footballer)\"><PERSON></a>, Scottish footballer", "links": [{"title": "<PERSON> (footballer)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(footballer)"}]}, {"year": "1983", "text": "<PERSON>, Swiss footballer", "html": "1983 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swiss footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swiss footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1983", "text": "<PERSON>, Swiss footballer", "html": "1983 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swiss footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swiss footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1983", "text": "<PERSON>, Canadian baseball player", "html": "1983 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(baseball)\" title=\"<PERSON> (baseball)\"><PERSON></a>, Canadian baseball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(baseball)\" title=\"<PERSON> (baseball)\"><PERSON></a>, Canadian baseball player", "links": [{"title": "<PERSON> (baseball)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(baseball)"}]}, {"year": "1984", "text": "<PERSON>, American singer-songwriter and musician", "html": "1984 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>.\" title=\"<PERSON>\"><PERSON>.</a>, American singer-songwriter and musician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>.\" title=\"<PERSON>.\"><PERSON>.</a>, American singer-songwriter and musician", "links": [{"title": "<PERSON>.", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>."}]}, {"year": "1984", "text": "<PERSON>, American film director, writer, and producer", "html": "1984 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_brothers\" class=\"mw-redirect\" title=\"<PERSON><PERSON> brothers\"><PERSON></a>, American film director, writer, and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_brothers\" class=\"mw-redirect\" title=\"<PERSON><PERSON> brothers\"><PERSON></a>, American film director, writer, and producer", "links": [{"title": "<PERSON><PERSON> brothers", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1984", "text": "<PERSON>, American film director, writer, and producer", "html": "1984 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_brothers\" class=\"mw-redirect\" title=\"<PERSON><PERSON> brothers\"><PERSON></a>, American film director, writer, and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_brothers\" class=\"mw-redirect\" title=\"<PERSON><PERSON> brothers\"><PERSON></a>, American film director, writer, and producer", "links": [{"title": "<PERSON><PERSON> brothers", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1984", "text": "<PERSON>, American baseball player", "html": "1984 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1985", "text": "<PERSON><PERSON>, Turkish footballer", "html": "1985 - <a href=\"https://wikipedia.org/wiki/Serkan_K%C4%B1r%C4%B1nt%C4%B1l%C4%B1\" title=\"<PERSON><PERSON> Kırıntılı\"><PERSON><PERSON></a>, Turkish footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Serkan_K%C4%B1r%C4%B1nt%C4%B1l%C4%B1\" title=\"<PERSON><PERSON>ırıntılı\"><PERSON><PERSON></a>, Turkish footballer", "links": [{"title": "Serkan Kırıntılı", "link": "https://wikipedia.org/wiki/Serkan_K%C4%B1r%C4%B1nt%C4%B1l%C4%B1"}]}, {"year": "1985", "text": "<PERSON>, American actress and director", "html": "1985 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(actress)\" title=\"<PERSON> (actress)\"><PERSON></a>, American actress and director", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>(actress)\" title=\"<PERSON> (actress)\"><PERSON></a>, American actress and director", "links": [{"title": "<PERSON> (actress)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(actress)"}]}, {"year": "1986", "text": "<PERSON><PERSON>, Bulgarian footballer", "html": "1986 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Bulgarian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Bulgarian footballer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1986", "text": "<PERSON>, Dominican baseball player", "html": "1986 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dominican baseball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dominican baseball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1986", "text": "<PERSON>, American actress and singer", "html": "1986 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress and singer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress and singer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1986", "text": "<PERSON>, Andorran judoka", "html": "1986 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A9s\" title=\"<PERSON>\"><PERSON></a>, Andorran judoka", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A9s\" title=\"<PERSON>\"><PERSON></a>, Andorran judoka", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Laura_Sall%C3%A9s"}]}, {"year": "1988", "text": "<PERSON><PERSON>, Argentine footballer", "html": "1988 - <a href=\"https://wikipedia.org/wiki/Pa<PERSON>_G%C3%B3mez\" title=\"<PERSON><PERSON> <PERSON>\"><PERSON><PERSON></a>, Argentine footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Pa<PERSON>_G%C3%B3mez\" title=\"<PERSON><PERSON> <PERSON>\"><PERSON><PERSON></a>, Argentine footballer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Papu_G%C3%B3mez"}]}, {"year": "1988", "text": "<PERSON><PERSON>, Portuguese footballer", "html": "1988 - <a href=\"https://wikipedia.org/wiki/Rui_Patr%C3%ADcio\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Portuguese footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Rui_<PERSON>r%C3%ADcio\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Portuguese footballer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Rui_Patr%C3%ADcio"}]}, {"year": "1989", "text": "<PERSON>, American baseball player", "html": "1989 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1990", "text": "<PERSON>, English actor", "html": "1990 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1991", "text": "<PERSON>, Mexican footballer", "html": "1991 - <a href=\"https://wikipedia.org/wiki/%C3%81ngel_Sep%C3%BAlveda\" title=\"<PERSON>\"><PERSON></a>, Mexican footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/%C3%81ngel_Sep%C3%BAlveda\" title=\"<PERSON>\"><PERSON></a>, Mexican footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/%C3%81ngel_Sep%C3%BAlveda"}]}, {"year": "1991", "text": "<PERSON>, American wrestler", "html": "1991 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American wrestler", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American wrestler", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1993", "text": "<PERSON>, South Korean rapper", "html": "1993 - <a href=\"https://wikipedia.org/wiki/<PERSON>_(rapper)\" title=\"<PERSON> (rapper)\"><PERSON></a>, South Korean rapper", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(rapper)\" title=\"<PERSON> (rapper)\"><PERSON></a>, South Korean rapper", "links": [{"title": "<PERSON> (rapper)", "link": "https://wikipedia.org/wiki/<PERSON>_(rapper)"}]}, {"year": "1993", "text": "<PERSON>, Central African footballer", "html": "1993 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Central African footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Central African footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Geoffrey_<PERSON>"}]}, {"year": "1993", "text": "<PERSON>, Argentine footballer", "html": "1993 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Argentine footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Argentine footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1994", "text": "<PERSON><PERSON><PERSON><PERSON>, American Twitch streamer and internet personality", "html": "1994 - <a href=\"https://wikipedia.org/wiki/Sodapoppin\" title=\"Sodapoppin\"><PERSON><PERSON><PERSON><PERSON></a>, American Twitch streamer and internet personality", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Sodapoppin\" title=\"Sodapoppin\"><PERSON><PERSON><PERSON><PERSON></a>, American Twitch streamer and internet personality", "links": [{"title": "Sodapop<PERSON>", "link": "https://wikipedia.org/wiki/Sodapoppin"}]}, {"year": "1995", "text": "<PERSON>, American rapper", "html": "1995 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON>all<PERSON>\"><PERSON></a>, American rapper", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON>all<PERSON>\"><PERSON></a>, American rapper", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>allion"}]}, {"year": "1997", "text": "<PERSON>, American basketball player", "html": "1997 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>.\" title=\"<PERSON> Jr.\"><PERSON>.</a>, American basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>.\" title=\"<PERSON> Jr.\"><PERSON>.</a>, American basketball player", "links": [{"title": "<PERSON> Jr.", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>."}]}, {"year": "1997", "text": "<PERSON>, American football player", "html": "1997 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1998", "text": "<PERSON>, American actor", "html": "1998 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1998", "text": "<PERSON>, English racing driver", "html": "1998 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(racing_driver)\" title=\"<PERSON> (racing driver)\"><PERSON></a>, English racing driver", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(racing_driver)\" title=\"<PERSON> (racing driver)\"><PERSON></a>, English racing driver", "links": [{"title": "<PERSON> (racing driver)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(racing_driver)"}]}, {"year": "2000", "text": "<PERSON><PERSON><PERSON>, Polish footballer", "html": "2000 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Polish footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Polish footballer", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "2004", "text": "<PERSON><PERSON><PERSON>, Slovak ice hockey player", "html": "2004 - <a href=\"https://wikipedia.org/wiki/%C5%A0imon_Nemec\" title=\"Šimon Nemec\"><PERSON><PERSON><PERSON> Nemec</a>, Slovak ice hockey player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/%C5%A0imon_Nemec\" title=\"Šimon Nemec\">Šimo<PERSON> Nemec</a>, Slovak ice hockey player", "links": [{"title": "Šimon Nemec", "link": "https://wikipedia.org/wiki/%C5%A0imon_Nemec"}]}], "Deaths": [{"year": "670", "text": "<PERSON><PERSON><PERSON><PERSON>, king of Northumbria (b. c. 612)", "html": "670 - <a href=\"https://wikipedia.org/wiki/Oswiu\" title=\"<PERSON>s<PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, king of Northumbria (b. c. 612)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Oswiu\" title=\"<PERSON>s<PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, king of Northumbria (b. c. 612)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Oswiu"}]}, {"year": "706", "text": "<PERSON><PERSON><PERSON>, Byzantine emperor", "html": "706 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Byzantine emperor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Byzantine emperor", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>"}]}, {"year": "706", "text": "<PERSON><PERSON><PERSON>, Byzantine emperor", "html": "706 - <a href=\"https://wikipedia.org/wiki/Tiberios_III\" class=\"mw-redirect\" title=\"Tiberios III\"><PERSON><PERSON><PERSON> III</a>, Byzantine emperor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Tiberios_III\" class=\"mw-redirect\" title=\"Tiberios III\"><PERSON><PERSON><PERSON> III</a>, Byzantine emperor", "links": [{"title": "<PERSON><PERSON><PERSON> III", "link": "https://wikipedia.org/wiki/Tiberios_III"}]}, {"year": "815", "text": "<PERSON>, <PERSON><PERSON>di anti-caliph", "html": "815 - <a href=\"https://wikipedia.org/wiki/Ibn_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, <PERSON><PERSON><PERSON> anti-caliph", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Ibn<PERSON>\" title=\"<PERSON> Ta<PERSON>\"><PERSON></a>, <PERSON><PERSON><PERSON> anti-caliph", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Ibn_<PERSON>aba"}]}, {"year": "956", "text": "<PERSON>, Chinese chancellor (b. 895)", "html": "956 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Chinese chancellor (b. 895)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Chinese chancellor (b. 895)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>gui"}]}, {"year": "1043", "text": "<PERSON><PERSON><PERSON> of Swabia, Holy Roman Empress (b. 990)", "html": "1043 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_of_Swabia\" title=\"<PERSON><PERSON><PERSON> of Swabia\"><PERSON><PERSON><PERSON> of Swabia</a>, Holy Roman Empress (b. 990)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_of_Swabia\" title=\"<PERSON><PERSON><PERSON> of Swabia\"><PERSON><PERSON><PERSON> of Swabia</a>, Holy Roman Empress (b. 990)", "links": [{"title": "<PERSON><PERSON><PERSON> of Swabia", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_of_Swabia"}]}, {"year": "1145", "text": "<PERSON>, pope of the Catholic Church", "html": "1145 - <a href=\"https://wikipedia.org/wiki/<PERSON>_Lucius_II\" title=\"Pope Lucius II\"><PERSON> II</a>, pope of the Catholic Church", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_Lucius_II\" title=\"Pope Lucius II\"><PERSON> II</a>, pope of the Catholic Church", "links": [{"title": "<PERSON> <PERSON> II", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1152", "text": "<PERSON>, king of Germany (b. 1093)", "html": "1152 - <a href=\"https://wikipedia.org/wiki/<PERSON>_III_of_Germany\" title=\"<PERSON> III of Germany\"><PERSON></a>, king of Germany (b. 1093)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_III_of_Germany\" title=\"<PERSON> III of Germany\"><PERSON> III</a>, king of Germany (b. 1093)", "links": [{"title": "<PERSON> of Germany", "link": "https://wikipedia.org/wiki/Conrad_<PERSON>_of_Germany"}]}, {"year": "1382", "text": "<PERSON>, 2nd Earl of Suffolk (b. c. 1339)", "html": "1382 - <a href=\"https://wikipedia.org/wiki/<PERSON>,_2nd_Earl_of_Suffolk\" class=\"mw-redirect\" title=\"<PERSON>, 2nd Earl of Suffolk\"><PERSON>, 2nd Earl of Suffolk</a> (b. c. 1339)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>,_2nd_Earl_of_Suffolk\" class=\"mw-redirect\" title=\"<PERSON>, 2nd Earl of Suffolk\"><PERSON>, 2nd Earl of Suffolk</a> (b. c. 1339)", "links": [{"title": "<PERSON>, 2nd Earl of Suffolk", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_2nd_Earl_of_Suffolk"}]}, {"year": "1417", "text": "<PERSON>, 11th Earl of Oxford, English commander (b. 1385)", "html": "1417 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>,_11th_Earl_of_Oxford\" title=\"<PERSON>, 11th Earl of Oxford\"><PERSON>, 11th Earl <PERSON> Oxford</a>, English commander (b. 1385)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>,_11th_Earl_of_Oxford\" title=\"<PERSON>, 11th Earl of Oxford\"><PERSON>, 11th Earl <PERSON> Oxford</a>, English commander (b. 1385)", "links": [{"title": "<PERSON>, 11th Earl of Oxford", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>,_11th_Earl_of_Oxford"}]}, {"year": "1508", "text": "<PERSON>, tyrant of Bologna (b. 1443)", "html": "1508 - <a href=\"https://wikipedia.org/wiki/<PERSON>_II_Bentivoglio\" title=\"Giovanni II Bentivoglio\"><PERSON> II <PERSON>ti<PERSON>gli<PERSON></a>, tyrant of Bologna (b. 1443)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_II_<PERSON>gli<PERSON>\" title=\"Giovanni II Bentivoglio\"><PERSON> II <PERSON>ti<PERSON>gli<PERSON></a>, tyrant of Bologna (b. 1443)", "links": [{"title": "Giovanni II Bentivoglio", "link": "https://wikipedia.org/wiki/<PERSON>_II_Bentivoglio"}]}, {"year": "1600", "text": "<PERSON>, Spanish Jesuit missionary and naturalist (b. 1540)", "html": "1600 - <a href=\"https://wikipedia.org/wiki/Jos%C3%A9_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Spanish Jesuit missionary and naturalist (b. 1540)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Jos%C3%A9_de_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Spanish Jesuit missionary and naturalist (b. 1540)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Jos%C3%A9_de_<PERSON><PERSON>"}]}, {"year": "1621", "text": "<PERSON>, German organist and composer (b. 1571)", "html": "1621 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German organist and composer (b. 1571)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German organist and composer (b. 1571)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1637", "text": "<PERSON>, Holy Roman Emperor (b. 1578)", "html": "1637 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Holy_Roman_Emperor\" title=\"<PERSON> II, Holy Roman Emperor\"><PERSON>, Holy Roman Emperor</a> (b. 1578)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Holy_Roman_Emperor\" title=\"<PERSON> II, Holy Roman Emperor\"><PERSON>, Holy Roman Emperor</a> (b. 1578)", "links": [{"title": "<PERSON>, Holy Roman Emperor", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Holy_Roman_Emperor"}]}, {"year": "1738", "text": "<PERSON>, Czech sculptor (b. 1684)", "html": "1738 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Czech sculptor (b. 1684)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Czech sculptor (b. 1684)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1772", "text": "<PERSON><PERSON><PERSON><PERSON>, Greek rebel and pirate", "html": "1772 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Greek rebel and pirate", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Greek rebel and pirate", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Mitromaras"}]}, {"year": "1781", "text": "<PERSON><PERSON><PERSON> <PERSON><PERSON><PERSON><PERSON>, German philosopher, author, and critic (b. 1729)", "html": "1781 - <a href=\"https://wikipedia.org/wiki/Gotthold_<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"Gotthold <PERSON><PERSON><PERSON><PERSON>\">Gotthold <PERSON><PERSON><PERSON><PERSON></a>, German philosopher, author, and critic (b. 1729)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Gotthold_<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"Gotthold <PERSON><PERSON><PERSON><PERSON>\">Gotthold <PERSON><PERSON><PERSON><PERSON></a>, German philosopher, author, and critic (b. 1729)", "links": [{"title": "<PERSON><PERSON><PERSON> <PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Gotth<PERSON>_<PERSON><PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1818", "text": "<PERSON>, Prince of Hohenlohe-Ingelfingen (b. 1746)", "html": "1818 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Prince_of_Hohenlohe-Ingelfingen\" class=\"mw-redirect\" title=\"<PERSON>, Prince of Hohenlohe-Ingelfingen\"><PERSON>, Prince of Hohenlohe-Ingelfingen</a> (b. 1746)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Prince_of_Hohenlohe-Ingelfingen\" class=\"mw-redirect\" title=\"<PERSON>, Prince of Hohenlohe-Ingelfingen\"><PERSON>, Prince of Hohenlohe-Ingelfingen</a> (b. 1746)", "links": [{"title": "<PERSON>, Prince of Hohenlohe-Ingelfingen", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Prince_of_Hohenlohe-Ingelfingen"}]}, {"year": "1835", "text": "<PERSON>, English farmer and politician (b. 1773)", "html": "1835 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(politician)\" title=\"<PERSON> (politician)\"><PERSON></a>, English farmer and politician (b. 1773)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(politician)\" title=\"<PERSON> (politician)\"><PERSON></a>, English farmer and politician (b. 1773)", "links": [{"title": "<PERSON> (politician)", "link": "https://wikipedia.org/wiki/<PERSON>(politician)"}]}, {"year": "1839", "text": "<PERSON><PERSON><PERSON><PERSON><PERSON>, Canadian rebel (b. 1803)", "html": "1839 - <a href=\"https://wikipedia.org/wiki/Fran%C3%A<PERSON><PERSON>-<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Canadian rebel (b. 1803)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Fran%C3%A<PERSON><PERSON>-<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Canadian rebel (b. 1803)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Fran%C3%A<PERSON><PERSON>-<PERSON>-<PERSON>_<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1842", "text": "<PERSON>, Scottish surgeon and botanist (b. 1754)", "html": "1842 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish surgeon and botanist (b. 1754)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish surgeon and botanist (b. 1754)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1844", "text": "<PERSON>, 1st Viscount Sidmouth, English politician, Prime Minister of the United Kingdom (b. 1757)", "html": "1844 - <a href=\"https://wikipedia.org/wiki/<PERSON>,_1st_Viscount_Sidmouth\" class=\"mw-redirect\" title=\"<PERSON>, 1st Viscount Sidmouth\"><PERSON>, 1st Viscount Sidmouth</a>, English politician, <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_the_United_Kingdom\" title=\"Prime Minister of the United Kingdom\">Prime Minister of the United Kingdom</a> (b. 1757)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>,_1st_Viscount_Sidmouth\" class=\"mw-redirect\" title=\"<PERSON>, 1st Viscount Sidmouth\"><PERSON>, 1st Viscount Sidmouth</a>, English politician, <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_the_United_Kingdom\" title=\"Prime Minister of the United Kingdom\">Prime Minister of the United Kingdom</a> (b. 1757)", "links": [{"title": "<PERSON>, 1st Viscount Sidmouth", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_1st_Viscount_Sidmouth"}, {"title": "Prime Minister of the United Kingdom", "link": "https://wikipedia.org/wiki/Prime_Minister_of_the_United_Kingdom"}]}, {"year": "1847", "text": "Ger<PERSON><PERSON> <PERSON>, Belgian mathematician and engineer (b. 1794)", "html": "1847 - <a href=\"https://wikipedia.org/wiki/Germinal_<PERSON>\" title=\"Germinal <PERSON>\">Germina<PERSON> <PERSON></a>, Belgian mathematician and engineer (b. 1794)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Germina<PERSON>_<PERSON>_<PERSON>\" title=\"Germinal <PERSON>\">Germinal <PERSON></a>, Belgian mathematician and engineer (b. 1794)", "links": [{"title": "Germinal <PERSON>", "link": "https://wikipedia.org/wiki/Germina<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1848", "text": "<PERSON>, Prussian general and politician, Prussian Minister of War (b. 1771)", "html": "1848 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Prussian general and politician, <a href=\"https://wikipedia.org/wiki/Prussian_Minister_of_War\" class=\"mw-redirect\" title=\"Prussian Minister of War\">Prussian Minister of War</a> (b. 1771)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Prussian general and politician, <a href=\"https://wikipedia.org/wiki/Prussian_Minister_of_War\" class=\"mw-redirect\" title=\"Prussian Minister of War\">Prussian Minister of War</a> (b. 1771)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "Prussian Minister of War", "link": "https://wikipedia.org/wiki/Prussian_Minister_of_War"}]}, {"year": "1849", "text": "<PERSON>, Belgian mathematician and theorist (b. 1804)", "html": "1849 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A7ois_V<PERSON>hulst\" title=\"<PERSON>\"><PERSON></a>, Belgian mathematician and theorist (b. 1804)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A7ois_Verhulst\" title=\"<PERSON>\"><PERSON></a>, Belgian mathematician and theorist (b. 1804)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A<PERSON><PERSON>_<PERSON>hulst"}]}, {"year": "1857", "text": "<PERSON>, Russian composer (b. 1804)", "html": "1857 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian composer (b. 1804)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian composer (b. 1804)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1869", "text": "<PERSON><PERSON><PERSON>, Indian poet and educator (b. 1796)", "html": "1869 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Indian poet and educator (b. 1796)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Indian poet and educator (b. 1796)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>b"}]}, {"year": "1877", "text": "<PERSON><PERSON>, Bulgarian poet and translator (b. 1839)", "html": "1877 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Bulgarian poet and translator (b. 1839)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Bulgarian poet and translator (b. 1839)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Ray<PERSON>_Zhinzifov"}]}, {"year": "1885", "text": "<PERSON>, Estonian-Russian geologist and engineer (b. 1803)", "html": "1885 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Estonian-Russian geologist and engineer (b. 1803)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Estonian-Russian geologist and engineer (b. 1803)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1885", "text": "<PERSON>, German-American composer and conductor (b. 1832)", "html": "1885 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German-American composer and conductor (b. 1832)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German-American composer and conductor (b. 1832)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Leopold_Dam<PERSON>ch"}]}, {"year": "1897", "text": "<PERSON><PERSON>, Romanian lawyer and politician, 10th Prime Minister of Romania (b. 1816)", "html": "1897 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Romanian lawyer and politician, 10th <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Romania\" title=\"Prime Minister of Romania\">Prime Minister of Romania</a> (b. 1816)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Romanian lawyer and politician, 10th <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Romania\" title=\"Prime Minister of Romania\">Prime Minister of Romania</a> (b. 1816)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}, {"title": "Prime Minister of Romania", "link": "https://wikipedia.org/wiki/Prime_Minister_of_Romania"}]}, {"year": "1901", "text": "<PERSON>, Scottish-New Zealand educator and politician, 3rd Prime Minister of New Zealand (b. 1819)", "html": "1901 - <a href=\"https://wikipedia.org/wiki/<PERSON>(politician)\" title=\"<PERSON> (politician)\"><PERSON></a>, Scottish-New Zealand educator and politician, 3rd <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_New_Zealand\" title=\"Prime Minister of New Zealand\">Prime Minister of New Zealand</a> (b. 1819)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>(politician)\" title=\"<PERSON> (politician)\"><PERSON></a>, Scottish-New Zealand educator and politician, 3rd <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_New_Zealand\" title=\"Prime Minister of New Zealand\">Prime Minister of New Zealand</a> (b. 1819)", "links": [{"title": "<PERSON> (politician)", "link": "https://wikipedia.org/wiki/<PERSON>_(politician)"}, {"title": "Prime Minister of New Zealand", "link": "https://wikipedia.org/wiki/Prime_Minister_of_New_Zealand"}]}, {"year": "1905", "text": "<PERSON><PERSON>, American author, general, and politician, 11th Governor of New Mexico Territory (b. 1827)", "html": "1905 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American author, general, and politician, 11th <a href=\"https://wikipedia.org/wiki/Governor_of_New_Mexico_Territory\" class=\"mw-redirect\" title=\"Governor of New Mexico Territory\">Governor of New Mexico Territory</a> (b. 1827)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American author, general, and politician, 11th <a href=\"https://wikipedia.org/wiki/Governor_of_New_Mexico_Territory\" class=\"mw-redirect\" title=\"Governor of New Mexico Territory\">Governor of New Mexico Territory</a> (b. 1827)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>w_<PERSON>"}, {"title": "Governor of New Mexico Territory", "link": "https://wikipedia.org/wiki/Governor_of_New_Mexico_Territory"}]}, {"year": "1911", "text": "<PERSON>, German-Austrian pediatrician and academic (b. 1859)", "html": "1911 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German-Austrian pediatrician and academic (b. 1859)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German-Austrian pediatrician and academic (b. 1859)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1924", "text": "<PERSON>, English composer (b. 1861)", "html": "1924 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English composer (b. 1861)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English composer (b. 1861)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1928", "text": "<PERSON><PERSON> <PERSON><PERSON>, English lawyer and politician, Prime Minister of the United Kingdom (b. 1852)", "html": "1928 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> <PERSON><PERSON>\"><PERSON><PERSON> <PERSON><PERSON></a>, English lawyer and politician, <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_the_United_Kingdom\" title=\"Prime Minister of the United Kingdom\">Prime Minister of the United Kingdom</a> (b. 1852)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> <PERSON><PERSON>\"><PERSON><PERSON> <PERSON><PERSON></a>, English lawyer and politician, <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_the_United_Kingdom\" title=\"Prime Minister of the United Kingdom\">Prime Minister of the United Kingdom</a> (b. 1852)", "links": [{"title": "<PERSON><PERSON> <PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON><PERSON>"}, {"title": "Prime Minister of the United Kingdom", "link": "https://wikipedia.org/wiki/Prime_Minister_of_the_United_Kingdom"}]}, {"year": "1932", "text": "<PERSON><PERSON>, American actress and playwright (b. 1865)", "html": "1932 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American actress and playwright (b. 1865)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American actress and playwright (b. 1865)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON><PERSON>_Fiske"}]}, {"year": "1933", "text": "<PERSON>, Australian animator and producer, co-created <PERSON> the Cat (b. 1887)", "html": "1933 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(film_producer)\" title=\"<PERSON> (film producer)\"><PERSON></a>, Australian animator and producer, co-created <i><a href=\"https://wikipedia.org/wiki/<PERSON>_the_Cat\" title=\"<PERSON> the Cat\"><PERSON> the Cat</a></i> (b. 1887)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(film_producer)\" title=\"<PERSON> (film producer)\"><PERSON></a>, Australian animator and producer, co-created <i><a href=\"https://wikipedia.org/wiki/<PERSON>_the_Cat\" title=\"<PERSON> the Cat\"><PERSON> the Cat</a></i> (b. 1887)", "links": [{"title": "<PERSON> (film producer)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(film_producer)"}, {"title": "<PERSON> the Cat", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_Cat"}]}, {"year": "1939", "text": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, Russian painter and author (b. 1878)", "html": "1939 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>-<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Russian painter and author (b. 1878)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>-<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Russian painter and author (b. 1878)", "links": [{"title": "<PERSON><PERSON><PERSON>-<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>rov-<PERSON>"}]}, {"year": "1956", "text": "<PERSON>, French lawyer and politician (b. 1878)", "html": "1956 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French lawyer and politician (b. 1878)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French lawyer and politician (b. 1878)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>-<PERSON>"}]}, {"year": "1959", "text": "<PERSON>, English physicist and academic, Nobel Prize laureate (b. 1879)", "html": "1959 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, English physicist and academic, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Physics\" title=\"Nobel Prize in Physics\">Nobel Prize</a> laureate (b. 1879)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, English physicist and academic, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Physics\" title=\"Nobel Prize in Physics\">Nobel Prize</a> laureate (b. 1879)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "Nobel Prize in Physics", "link": "https://wikipedia.org/wiki/Nobel_Prize_in_Physics"}]}, {"year": "1961", "text": "<PERSON>, American figure skater (b. 1944)", "html": "1961 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American figure skater (b. 1944)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American figure skater (b. 1944)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1965", "text": "<PERSON>, American singer and pianist (b. 1919)", "html": "1965 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"Nat King <PERSON>\"><PERSON></a>, American singer and pianist (b. 1919)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"Nat King <PERSON>\"><PERSON></a>, American singer and pianist (b. 1919)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1966", "text": "<PERSON>, Polish architect and historian (b. 1909)", "html": "1966 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>%C5%82ek\" title=\"<PERSON>\"><PERSON></a>, Polish architect and historian (b. 1909)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>%C5%82ek\" title=\"<PERSON>\"><PERSON></a>, Polish architect and historian (b. 1909)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_Anton<PERSON>_Cio%C5%82ek"}]}, {"year": "1966", "text": "<PERSON><PERSON>, Colombian priest and theologian (b. 1929)", "html": "1966 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>repo\" title=\"<PERSON><PERSON>re<PERSON>\"><PERSON><PERSON></a>, Colombian priest and theologian (b. 1929)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>repo\" title=\"<PERSON><PERSON>re<PERSON>\"><PERSON><PERSON></a>, Colombian priest and theologian (b. 1929)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_Restrepo"}]}, {"year": "1967", "text": "<PERSON>, Spanish-American actor and director (b. 1887)", "html": "1967 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Spanish-American actor and director (b. 1887)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Spanish-American actor and director (b. 1887)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1970", "text": "<PERSON>, 1st Baron <PERSON>, Scottish air marshal (b. 1882)", "html": "1970 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_1st_Baron_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>, 1st Baron <PERSON>\"><PERSON>, 1st Baron <PERSON></a>, Scottish air marshal (b. 1882)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_1st_Baron_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>, 1st Baron <PERSON>\"><PERSON>, 1st Baron <PERSON></a>, Scottish air marshal (b. 1882)", "links": [{"title": "<PERSON>, 1st Baron <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>,_1st_Baron_<PERSON>"}]}, {"year": "1973", "text": "<PERSON>, American actor (b. 1924)", "html": "1973 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor (b. 1924)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor (b. 1924)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1974", "text": "<PERSON>, Swedish composer and engineer (b. 1887)", "html": "1974 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swedish composer and engineer (b. 1887)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swedish composer and engineer (b. 1887)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1981", "text": "<PERSON>, American guitarist and songwriter (b. 1943)", "html": "1981 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American guitarist and songwriter (b. 1943)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American guitarist and songwriter (b. 1943)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1981", "text": "<PERSON>, German organist and conductor (b. 1926)", "html": "1981 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(conductor)\" title=\"<PERSON> (conductor)\"><PERSON></a>, German organist and conductor (b. 1926)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(conductor)\" title=\"<PERSON> (conductor)\"><PERSON></a>, German organist and conductor (b. 1926)", "links": [{"title": "<PERSON> (conductor)", "link": "https://wikipedia.org/wiki/<PERSON>_(conductor)"}]}, {"year": "1984", "text": "<PERSON>, American actress and singer (b. 1908)", "html": "1984 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress and singer (b. 1908)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress and singer (b. 1908)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1988", "text": "<PERSON>, American physicist and academic, Nobel Prize laureate (b. 1918)", "html": "1988 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American physicist and academic, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Physics\" title=\"Nobel Prize in Physics\">Nobel Prize</a> laureate (b. 1918)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American physicist and academic, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Physics\" title=\"Nobel Prize in Physics\">Nobel Prize</a> laureate (b. 1918)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Nobel Prize in Physics", "link": "https://wikipedia.org/wiki/Nobel_Prize_in_Physics"}]}, {"year": "1992", "text": "<PERSON>, Peruvian activist (b. 1960)", "html": "1992 - <a href=\"https://wikipedia.org/wiki/Mar%C3%<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Peruvian activist (b. 1960)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Mar%C3%<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Peruvian activist (b. 1960)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Mar%C3%<PERSON><PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1992", "text": "<PERSON>, American composer and academic (b. 1910)", "html": "1992 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American composer and academic (b. 1910)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American composer and academic (b. 1910)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1996", "text": "<PERSON>, American actor (b. 1929)", "html": "1996 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor (b. 1929)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor (b. 1929)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1998", "text": "<PERSON>, American journalist and author (b. 1908)", "html": "1998 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American journalist and author (b. 1908)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American journalist and author (b. 1908)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1999", "text": "<PERSON>, American physicist and mountaineer, Nobel Prize laureate (b. 1926)", "html": "1999 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Kendall\"><PERSON></a>, American physicist and mountaineer, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Physics\" title=\"Nobel Prize in Physics\">Nobel Prize</a> laureate (b. 1926)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American physicist and mountaineer, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Physics\" title=\"Nobel Prize in Physics\">Nobel Prize</a> laureate (b. 1926)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "Nobel Prize in Physics", "link": "https://wikipedia.org/wiki/Nobel_Prize_in_Physics"}]}, {"year": "2000", "text": "<PERSON>, Canadian commander and politician, 25th Premier of Prince Edward Island (b. 1914)", "html": "2000 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian commander and politician, 25th <a href=\"https://wikipedia.org/wiki/Premier_of_Prince_Edward_Island\" title=\"Premier of Prince Edward Island\">Premier of Prince Edward Island</a> (b. 1914)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian commander and politician, 25th <a href=\"https://wikipedia.org/wiki/Premier_of_Prince_Edward_Island\" title=\"Premier of Prince Edward Island\">Premier of Prince Edward Island</a> (b. 1914)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Premier of Prince Edward Island", "link": "https://wikipedia.org/wiki/Premier_of_Prince_Edward_Island"}]}, {"year": "2002", "text": "<PERSON>, American journalist and actor (b. 1914)", "html": "2002 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American journalist and actor (b. 1914)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American journalist and actor (b. 1914)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "2002", "text": "<PERSON>, New Zealand actor (b. 1963)", "html": "2002 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(New_Zealand_actor)\" title=\"<PERSON> (New Zealand actor)\"><PERSON></a>, New Zealand actor (b. 1963)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(New_Zealand_actor)\" title=\"<PERSON> (New Zealand actor)\"><PERSON></a>, New Zealand actor (b. 1963)", "links": [{"title": "<PERSON> (New Zealand actor)", "link": "https://wikipedia.org/wiki/<PERSON>_(New_Zealand_actor)"}]}, {"year": "2004", "text": "<PERSON><PERSON>, Norwegian lawyer, judge, and politician, Norwegian Minister of Trade (b. 1917)", "html": "2004 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Norwegian lawyer, judge, and politician, <a href=\"https://wikipedia.org/wiki/Minister_of_Trade_and_Shipping\" title=\"Minister of Trade and Shipping\">Norwegian Minister of Trade</a> (b. 1917)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Norwegian lawyer, judge, and politician, <a href=\"https://wikipedia.org/wiki/Minister_of_Trade_and_Shipping\" title=\"Minister of Trade and Shipping\">Norwegian Minister of Trade</a> (b. 1917)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}, {"title": "Minister of Trade and Shipping", "link": "https://wikipedia.org/wiki/Minister_of_Trade_and_Shipping"}]}, {"year": "2005", "text": "<PERSON>, French singer-songwriter (b. 1944)", "html": "2005 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French singer-songwriter (b. 1944)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French singer-songwriter (b. 1944)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2005", "text": "<PERSON>, American historian and journalist (b. 1947)", "html": "2005 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(writer)\" title=\"<PERSON> (writer)\"><PERSON></a>, American historian and journalist (b. 1947)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(writer)\" title=\"<PERSON> (writer)\"><PERSON></a>, American historian and journalist (b. 1947)", "links": [{"title": "<PERSON> (writer)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(writer)"}]}, {"year": "2007", "text": "<PERSON>, American actor (b. 1925)", "html": "2007 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor (b. 1925)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor (b. 1925)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2007", "text": "<PERSON>, American songwriter (b. 1915)", "html": "2007 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American songwriter (b. 1915)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American songwriter (b. 1915)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2008", "text": "<PERSON>, American wrestler and sportscaster (b. 1935)", "html": "2008 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American wrestler and sportscaster (b. 1935)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American wrestler and sportscaster (b. 1935)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2010", "text": "<PERSON>, American general (b. 1921)", "html": "2010 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American general (b. 1921)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American general (b. 1921)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "2012", "text": "<PERSON>, English-Israeli physicist and academic (b. 1920)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English-Israeli physicist and academic (b. 1920)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English-Israeli physicist and academic (b. 1920)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2013", "text": "<PERSON><PERSON>, Thai general and politician (b. 1935)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Thai general and politician (b. 1935)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Thai general and politician (b. 1935)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "2013", "text": "<PERSON>, Bangladeshi atheist blogger", "html": "2013 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Bangladeshi atheist blogger", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Bangladeshi atheist blogger", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "2014", "text": "<PERSON><PERSON>, American computer scientist and engineer (b. 1924)", "html": "2014 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON>lma <PERSON>\"><PERSON><PERSON></a>, American computer scientist and engineer (b. 1924)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American computer scientist and engineer (b. 1924)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>rin"}]}, {"year": "2014", "text": "<PERSON>, Scottish-Canadian actor, director, and producer (b. 1946)", "html": "2014 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish-Canadian actor, director, and producer (b. 1946)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish-Canadian actor, director, and producer (b. 1946)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2015", "text": "<PERSON><PERSON>, Afghan diplomat, Afghan Ambassador to Japan (b. 1969)", "html": "2015 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Afghan diplomat, <a href=\"https://wikipedia.org/wiki/Afghan_Ambassador_to_Japan\" class=\"mw-redirect\" title=\"Afghan Ambassador to Japan\">Afghan Ambassador to Japan</a> (b. 1969)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Afghan diplomat, <a href=\"https://wikipedia.org/wiki/Afghan_Ambassador_to_Japan\" class=\"mw-redirect\" title=\"Afghan Ambassador to Japan\">Afghan Ambassador to Japan</a> (b. 1969)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>in"}, {"title": "Afghan Ambassador to Japan", "link": "https://wikipedia.org/wiki/Afghan_Ambassador_to_Japan"}]}, {"year": "2015", "text": "<PERSON><PERSON><PERSON>, American journalist and author (b. 1926)", "html": "2015 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American journalist and author (b. 1926)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American journalist and author (b. 1926)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>"}]}, {"year": "2015", "text": "<PERSON>, Canadian ice hockey player (b. 1979)", "html": "2015 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player (b. 1979)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player (b. 1979)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2016", "text": "<PERSON>, Finnish-American actor (b. 1917)", "html": "2016 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Finnish-American actor (b. 1917)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Finnish-American actor (b. 1917)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2016", "text": "<PERSON><PERSON>, Canadian-American singer-songwriter, dancer, and actress (b. 1959)", "html": "2016 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_(singer)\" title=\"<PERSON><PERSON> (singer)\"><PERSON><PERSON></a>, Canadian-American singer-songwriter, dancer, and actress (b. 1959)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_(singer)\" title=\"<PERSON><PERSON> (singer)\"><PERSON><PERSON></a>, Canadian-American singer-songwriter, dancer, and actress (b. 1959)", "links": [{"title": "<PERSON><PERSON> (singer)", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_(singer)"}]}, {"year": "2017", "text": "<PERSON>, Canadian radio broadcaster (b. 1948)", "html": "2017 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian radio broadcaster (b. 1948)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian radio broadcaster (b. 1948)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2019", "text": "<PERSON>, American socialite (b. 1933)", "html": "2019 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American socialite (b. 1933)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American socialite (b. 1933)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2020", "text": "<PERSON>, English actress and TV presenter (b. 1979)", "html": "2020 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actress and TV presenter (b. 1979)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actress and TV presenter (b. 1979)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>ck"}]}, {"year": "2022", "text": "<PERSON><PERSON><PERSON>, Indian singer, composer and record producer (b. 1952)", "html": "2022 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Indian singer, composer and record producer (b. 1952)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Indian singer, composer and record producer (b. 1952)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON><PERSON>i"}]}, {"year": "2022", "text": "<PERSON><PERSON><PERSON><PERSON>, American author, humorist, and journalist (b. 1947)", "html": "2022 - <a href=\"https://wikipedia.org/wiki/P.J._O%27Rourke\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, American author, humorist, and journalist (b. 1947)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/P.J._O%27Rourke\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, American author, humorist, and journalist (b. 1947)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/P.J._O%27Rourke"}]}, {"year": "2023", "text": "<PERSON><PERSON>, American actress and singer (b. 1940)", "html": "2023 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American actress and singer (b. 1940)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American actress and singer (b. 1940)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "2025", "text": "<PERSON>, American film director (b. 1942)", "html": "2025 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American film director (b. 1942)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American film director (b. 1942)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/George_Armitage"}]}, {"year": "2025", "text": "<PERSON>, Portuguese businessman (b. 1937)", "html": "2025 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Portuguese businessman (b. 1937)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Portuguese businessman (b. 1937)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "2025", "text": "<PERSON><PERSON><PERSON>, South African imam, Islamic scholar and LGBT activist (b. 1967)", "html": "2025 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, South African imam, Islamic scholar and LGBT activist (b. 1967)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, South African imam, Islamic scholar and LGBT activist (b. 1967)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}]}}