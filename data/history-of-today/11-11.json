{"date": "November 11", "url": "https://wikipedia.org/wiki/November_11", "data": {"Events": [{"year": "308", "text": "At Carnuntum, Emperor emeritus <PERSON><PERSON><PERSON><PERSON> confers with <PERSON><PERSON>, <PERSON> of the East, and <PERSON><PERSON><PERSON>, the recently returned former <PERSON> of the West, in an attempt to end the civil wars of the Tetrarchy.", "html": "308 - At <a href=\"https://wikipedia.org/wiki/Carnuntum\" title=\"Carnuntum\">Car<PERSON><PERSON><PERSON></a>, Emperor <i>emeritus</i> <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>ian\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a> confers with <a href=\"https://wikipedia.org/wiki/Galerius\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, <i><a href=\"https://wikipedia.org/wiki/Augustus_(honorific)\" class=\"mw-redirect\" title=\"<PERSON> (honorific)\">Augustus</a></i> of the East, and <a href=\"https://wikipedia.org/wiki/Maximian\" title=\"<PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, the recently returned former <i>Augustus</i> of the West, in an attempt to end the <a href=\"https://wikipedia.org/wiki/Civil_wars_of_the_Tetrarchy\" title=\"Civil wars of the Tetrarchy\">civil wars of the Tetrarchy</a>.", "no_year_html": "At <a href=\"https://wikipedia.org/wiki/Carnuntum\" title=\"Carnuntum\">Carnu<PERSON><PERSON></a>, Emperor <i>emeritus</i> <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>ian\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a> confers with <a href=\"https://wikipedia.org/wiki/Galerius\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, <i><a href=\"https://wikipedia.org/wiki/Augustus_(honorific)\" class=\"mw-redirect\" title=\"<PERSON> (honorific)\">Augustus</a></i> of the East, and <a href=\"https://wikipedia.org/wiki/Maximian\" title=\"<PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, the recently returned former <i>Augustus</i> of the West, in an attempt to end the <a href=\"https://wikipedia.org/wiki/Civil_wars_of_the_Tetrarchy\" title=\"Civil wars of the Tetrarchy\">civil wars of the Tetrarchy</a>.", "links": [{"title": "Carnuntum", "link": "https://wikipedia.org/wiki/Carnuntum"}, {"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>ian"}, {"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>rius"}, {"title": "<PERSON> (honorific)", "link": "https://wikipedia.org/wiki/<PERSON>_(honorific)"}, {"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>ian"}, {"title": "Civil wars of the Tetrarchy", "link": "https://wikipedia.org/wiki/Civil_wars_of_the_Tetrarchy"}]}, {"year": "1028", "text": "<PERSON> dies, ending his uninterrupted reign as emperor or co-emperor of the Byzantine Empire of 66 years.", "html": "1028 - <a href=\"https://wikipedia.org/wiki/<PERSON>_VIII\" title=\"Constantine VIII\"><PERSON> VIII</a> dies, ending his uninterrupted reign as <a href=\"https://wikipedia.org/wiki/List_of_Byzantine_emperors\" title=\"List of Byzantine emperors\">emperor or co-emperor</a> of the <a href=\"https://wikipedia.org/wiki/Byzantine_Empire\" title=\"Byzantine Empire\">Byzantine Empire</a> of 66 years.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Constantine_VIII\" title=\"Constantine VIII\"><PERSON> VIII</a> dies, ending his uninterrupted reign as <a href=\"https://wikipedia.org/wiki/List_of_Byzantine_emperors\" title=\"List of Byzantine emperors\">emperor or co-emperor</a> of the <a href=\"https://wikipedia.org/wiki/Byzantine_Empire\" title=\"Byzantine Empire\">Byzantine Empire</a> of 66 years.", "links": [{"title": "Constantine VIII", "link": "https://wikipedia.org/wiki/Constantine_VIII"}, {"title": "List of Byzantine emperors", "link": "https://wikipedia.org/wiki/List_of_Byzantine_emperors"}, {"title": "Byzantine Empire", "link": "https://wikipedia.org/wiki/Byzantine_Empire"}]}, {"year": "1100", "text": "<PERSON> of England marries <PERSON> of Scotland, the daughter of <PERSON> of Scotland and a direct descendant of the Saxon king <PERSON>; <PERSON> is crowned on the same day.", "html": "1100 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_England\" title=\"<PERSON> of England\"><PERSON> of England</a> marries <a href=\"https://wikipedia.org/wiki/<PERSON>_of_Scotland\" title=\"Matilda of Scotland\"><PERSON> of Scotland</a>, the daughter of <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Scotland\" title=\"<PERSON> of Scotland\"><PERSON> of Scotland</a> and a direct descendant of the Saxon king <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>; <PERSON> is crowned on the same day.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_England\" title=\"<PERSON> of England\"><PERSON> of England</a> marries <a href=\"https://wikipedia.org/wiki/<PERSON>_of_Scotland\" title=\"<PERSON> of Scotland\"><PERSON> of Scotland</a>, the daughter of <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Scotland\" title=\"<PERSON> of Scotland\"><PERSON> of Scotland</a> and a direct descendant of the Saxon king <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>; <PERSON> is crowned on the same day.", "links": [{"title": "<PERSON> of England", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_England"}, {"title": "Matilda of Scotland", "link": "https://wikipedia.org/wiki/<PERSON>_of_Scotland"}, {"title": "<PERSON> of Scotland", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Scotland"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>side"}]}, {"year": "1215", "text": "The Fourth Council of the Lateran meets, defining the doctrine of transubstantiation, the process by which bread and wine are, by that doctrine, said to transform into the body and blood of <PERSON>.", "html": "1215 - The <a href=\"https://wikipedia.org/wiki/Fourth_Council_of_the_Lateran\" title=\"Fourth Council of the Lateran\">Fourth Council of the Lateran</a> meets, defining the doctrine of <a href=\"https://wikipedia.org/wiki/Transubstantiation\" title=\"Transubstantiation\">transubstantiation</a>, the process by which bread and wine are, by that doctrine, said to transform into the body and blood of <a href=\"https://wikipedia.org/wiki/Christ\" class=\"mw-redirect\" title=\"Christ\">Christ</a>.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Fourth_Council_of_the_Lateran\" title=\"Fourth Council of the Lateran\">Fourth Council of the Lateran</a> meets, defining the doctrine of <a href=\"https://wikipedia.org/wiki/Transubstantiation\" title=\"Transubstantiation\">transubstantiation</a>, the process by which bread and wine are, by that doctrine, said to transform into the body and blood of <a href=\"https://wikipedia.org/wiki/Christ\" class=\"mw-redirect\" title=\"Christ\">Christ</a>.", "links": [{"title": "Fourth Council of the Lateran", "link": "https://wikipedia.org/wiki/Fourth_Council_of_the_Lateran"}, {"title": "Transubstantiation", "link": "https://wikipedia.org/wiki/Transubstantiation"}, {"title": "Christ", "link": "https://wikipedia.org/wiki/Christ"}]}, {"year": "1500", "text": "Treaty of Granada: <PERSON> of France and <PERSON> of <PERSON> agree to divide the Kingdom of Naples between them.", "html": "1500 - <a href=\"https://wikipedia.org/wiki/Treaty_of_Granada_(1500)\" title=\"Treaty of Granada (1500)\">Treaty of Granada</a>: <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_France\" class=\"mw-redirect\" title=\"<PERSON> of France\"><PERSON> of France</a> and <a href=\"https://wikipedia.org/wiki/Ferdinand_II_of_Aragon\" title=\"<PERSON> of Aragon\"><PERSON> of Aragon</a> agree to divide the <a href=\"https://wikipedia.org/wiki/Kingdom_of_Naples\" title=\"Kingdom of Naples\">Kingdom of Naples</a> between them.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Treaty_of_Granada_(1500)\" title=\"Treaty of Granada (1500)\">Treaty of Granada</a>: <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_France\" class=\"mw-redirect\" title=\"<PERSON> of France\"><PERSON> of France</a> and <a href=\"https://wikipedia.org/wiki/<PERSON>_II_of_Aragon\" title=\"<PERSON> of Aragon\"><PERSON> of Aragon</a> agree to divide the <a href=\"https://wikipedia.org/wiki/Kingdom_of_Naples\" title=\"Kingdom of Naples\">Kingdom of Naples</a> between them.", "links": [{"title": "Treaty of Granada (1500)", "link": "https://wikipedia.org/wiki/Treaty_of_Granada_(1500)"}, {"title": "<PERSON> of France", "link": "https://wikipedia.org/wiki/Louis_<PERSON>_of_France"}, {"title": "<PERSON> of Aragon", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Aragon"}, {"title": "Kingdom of Naples", "link": "https://wikipedia.org/wiki/Kingdom_of_Naples"}]}, {"year": "1572", "text": "<PERSON><PERSON> observes the supernova SN 1572.", "html": "1572 - <a href=\"https://wikipedia.org/wiki/Tycho_Brahe\" title=\"Tycho Brahe\"><PERSON><PERSON></a> observes the <a href=\"https://wikipedia.org/wiki/Supernova\" title=\"Supernova\">supernova</a> <a href=\"https://wikipedia.org/wiki/SN_1572\" title=\"SN 1572\">SN 1572</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Ty<PERSON>_Brahe\" title=\"Tycho Brahe\"><PERSON><PERSON></a> observes the <a href=\"https://wikipedia.org/wiki/Supernova\" title=\"Supernova\">supernova</a> <a href=\"https://wikipedia.org/wiki/SN_1572\" title=\"SN 1572\">SN 1572</a>.", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Ty<PERSON>_<PERSON>e"}, {"title": "Supernova", "link": "https://wikipedia.org/wiki/Supernova"}, {"title": "SN 1572", "link": "https://wikipedia.org/wiki/SN_1572"}]}, {"year": "1620", "text": "The Mayflower Compact is signed in what is now Provincetown Harbor near Cape Cod.", "html": "1620 - The <a href=\"https://wikipedia.org/wiki/Mayflower_Compact\" title=\"Mayflower Compact\">Mayflower Compact</a> is signed in what is now <a href=\"https://wikipedia.org/wiki/Provincetown_Harbor\" title=\"Provincetown Harbor\">Provincetown Harbor</a> near <a href=\"https://wikipedia.org/wiki/Cape_Cod\" title=\"Cape Cod\">Cape Cod</a>.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Mayflower_Compact\" title=\"Mayflower Compact\">Mayflower Compact</a> is signed in what is now <a href=\"https://wikipedia.org/wiki/Provincetown_Harbor\" title=\"Provincetown Harbor\">Provincetown Harbor</a> near <a href=\"https://wikipedia.org/wiki/Cape_Cod\" title=\"Cape Cod\">Cape Cod</a>.", "links": [{"title": "Mayflower Compact", "link": "https://wikipedia.org/wiki/Mayflower_Compact"}, {"title": "Provincetown Harbor", "link": "https://wikipedia.org/wiki/Provincetown_Harbor"}, {"title": "Cape Cod", "link": "https://wikipedia.org/wiki/Cape_Cod"}]}, {"year": "1634", "text": "Following pressure from Anglican bishop <PERSON>, the Irish House of Commons passes An Act for the Punishment for the Vice of Buggery.", "html": "1634 - Following pressure from <a href=\"https://wikipedia.org/wiki/Anglican\" class=\"mw-redirect\" title=\"Anglican\">Anglican</a> bishop <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, the <a href=\"https://wikipedia.org/wiki/Irish_House_of_Commons\" title=\"Irish House of Commons\">Irish House of Commons</a> passes <i>An Act for the Punishment for the Vice of <a href=\"https://wikipedia.org/wiki/Buggery\" class=\"mw-redirect\" title=\"Buggery\">Buggery</a></i>.", "no_year_html": "Following pressure from <a href=\"https://wikipedia.org/wiki/Anglican\" class=\"mw-redirect\" title=\"Anglican\">Anglican</a> bishop <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, the <a href=\"https://wikipedia.org/wiki/Irish_House_of_Commons\" title=\"Irish House of Commons\">Irish House of Commons</a> passes <i>An Act for the Punishment for the Vice of <a href=\"https://wikipedia.org/wiki/Buggery\" class=\"mw-redirect\" title=\"Buggery\">Buggery</a></i>.", "links": [{"title": "Anglican", "link": "https://wikipedia.org/wiki/Anglican"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}, {"title": "Irish House of Commons", "link": "https://wikipedia.org/wiki/Irish_House_of_Commons"}, {"title": "Buggery", "link": "https://wikipedia.org/wiki/Buggery"}]}, {"year": "1673", "text": "Second Battle of Khotyn in Ukraine: Polish-Lithuanian Commonwealth forces under the command of <PERSON> defeat the Ottoman army. In this battle, rockets made by <PERSON><PERSON><PERSON><PERSON> are successfully used.", "html": "1673 - Second <a href=\"https://wikipedia.org/wiki/Battle_of_Khotyn_(1673)\" title=\"Battle of Khotyn (1673)\">Battle of Khotyn</a> in <a href=\"https://wikipedia.org/wiki/Ukraine\" title=\"Ukraine\">Ukraine</a>: <a href=\"https://wikipedia.org/wiki/Polish%E2%80%93Lithuanian_Commonwealth\" title=\"Polish-Lithuanian Commonwealth\">Polish-Lithuanian Commonwealth</a> forces under the command of <a href=\"https://wikipedia.org/wiki/Jan_III_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a> defeat the <a href=\"https://wikipedia.org/wiki/Ottoman_Empire\" title=\"Ottoman Empire\">Ottoman</a> army. In this battle, rockets made by <a href=\"https://wikipedia.org/wiki/Kazi<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a> are successfully used.", "no_year_html": "Second <a href=\"https://wikipedia.org/wiki/Battle_of_Khotyn_(1673)\" title=\"Battle of Khotyn (1673)\">Battle of Khotyn</a> in <a href=\"https://wikipedia.org/wiki/Ukraine\" title=\"Ukraine\">Ukraine</a>: <a href=\"https://wikipedia.org/wiki/Polish%E2%80%93Lithuanian_Commonwealth\" title=\"Polish-Lithuanian Commonwealth\">Polish-Lithuanian Commonwealth</a> forces under the command of <a href=\"https://wikipedia.org/wiki/Jan_III_<PERSON>\" class=\"mw-redirect\" title=\"Jan <PERSON>\"><PERSON></a> defeat the <a href=\"https://wikipedia.org/wiki/Ottoman_Empire\" title=\"Ottoman Empire\">Ottoman</a> army. In this battle, rockets made by <a href=\"https://wikipedia.org/wiki/Kazimie<PERSON>_<PERSON>\" title=\"Ka<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a> are successfully used.", "links": [{"title": "Battle of Khotyn (1673)", "link": "https://wikipedia.org/wiki/Battle_of_Khotyn_(1673)"}, {"title": "Ukraine", "link": "https://wikipedia.org/wiki/Ukraine"}, {"title": "Polish-Lithuanian Commonwealth", "link": "https://wikipedia.org/wiki/Polish%E2%80%93Lithuanian_Commonwealth"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "Ottoman Empire", "link": "https://wikipedia.org/wiki/Ottoman_Empire"}, {"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1675", "text": "<PERSON><PERSON><PERSON> demonstrates integral calculus for the first time to find the area under the graph of y = ƒ(x).", "html": "1675 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a> demonstrates <a href=\"https://wikipedia.org/wiki/Integral_calculus\" class=\"mw-redirect\" title=\"Integral calculus\">integral calculus</a> for the first time to find the area under the graph of <i>y</i> = <i>ƒ</i>(<i>x</i>).", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a> demonstrates <a href=\"https://wikipedia.org/wiki/Integral_calculus\" class=\"mw-redirect\" title=\"Integral calculus\">integral calculus</a> for the first time to find the area under the graph of <i>y</i> = <i>ƒ</i>(<i>x</i>).", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}, {"title": "Integral calculus", "link": "https://wikipedia.org/wiki/Integral_calculus"}]}, {"year": "1724", "text": "<PERSON>, alias <PERSON><PERSON>, a highwayman known for attacking \"Thief-Taker General\" (and thief) <PERSON> at the Old Bailey, is hanged in London.", "html": "1724 - <a href=\"https://wikipedia.org/wiki/<PERSON>(criminal)\" title=\"<PERSON> (criminal)\"><PERSON></a>, alias <PERSON><PERSON>, a highwayman known for attacking \"Thief-Taker General\" (and thief) <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> at the <a href=\"https://wikipedia.org/wiki/<PERSON>_Bailey\" title=\"<PERSON>\"><PERSON></a>, is hanged in London.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>(criminal)\" title=\"<PERSON> (criminal)\"><PERSON></a>, alias <PERSON><PERSON>, a highwayman known for attacking \"Thief-Taker General\" (and thief) <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> at the <a href=\"https://wikipedia.org/wiki/<PERSON>_Bailey\" title=\"<PERSON>\"><PERSON></a>, is hanged in London.", "links": [{"title": "<PERSON> (criminal)", "link": "https://wikipedia.org/wiki/<PERSON>_(criminal)"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_Bailey"}]}, {"year": "1750", "text": "Riots break out in Lhasa after the murder of the Tibetan regent.", "html": "1750 - <a href=\"https://wikipedia.org/wiki/Lhasa_riot_of_1750\" title=\"Lhasa riot of 1750\">Riots break out</a> in <a href=\"https://wikipedia.org/wiki/Lhasa_(prefecture-level_city)\" class=\"mw-redirect\" title=\"Lhasa (prefecture-level city)\">Lhasa</a> after the murder of the <a href=\"https://wikipedia.org/wiki/Tibet\" title=\"Tibet\">Tibetan</a> regent.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Lhasa_riot_of_1750\" title=\"Lhasa riot of 1750\">Riots break out</a> in <a href=\"https://wikipedia.org/wiki/Lhasa_(prefecture-level_city)\" class=\"mw-redirect\" title=\"Lhasa (prefecture-level city)\">Lhasa</a> after the murder of the <a href=\"https://wikipedia.org/wiki/Tibet\" title=\"Tibet\">Tibetan</a> regent.", "links": [{"title": "Lhasa riot of 1750", "link": "https://wikipedia.org/wiki/Lhasa_riot_of_1750"}, {"title": "Lhasa (prefecture-level city)", "link": "https://wikipedia.org/wiki/Lhasa_(prefecture-level_city)"}, {"title": "Tibet", "link": "https://wikipedia.org/wiki/Tibet"}]}, {"year": "1750", "text": "The F.H.C. Society, also known as the Flat Hat Club, is formed at Raleigh Tavern, Williamsburg, Virginia. It is the first college fraternity.", "html": "1750 - The F.H.C. Society, also known as the <a href=\"https://wikipedia.org/wiki/Flat_Hat_Club\" title=\"Flat Hat Club\">Flat Hat Club</a>, is formed at Raleigh Tavern, <a href=\"https://wikipedia.org/wiki/Williamsburg,_Virginia\" title=\"Williamsburg, Virginia\">Williamsburg, Virginia</a>. It is the first college fraternity.", "no_year_html": "The F.H.C. Society, also known as the <a href=\"https://wikipedia.org/wiki/Flat_Hat_Club\" title=\"Flat Hat Club\">Flat Hat Club</a>, is formed at Raleigh Tavern, <a href=\"https://wikipedia.org/wiki/Williamsburg,_Virginia\" title=\"Williamsburg, Virginia\">Williamsburg, Virginia</a>. It is the first college fraternity.", "links": [{"title": "Flat Hat Club", "link": "https://wikipedia.org/wiki/Flat_Hat_Club"}, {"title": "Williamsburg, Virginia", "link": "https://wikipedia.org/wiki/Williamsburg,_Virginia"}]}, {"year": "1778", "text": "Cherry Valley massacre: Loyalists and Seneca Indian forces attack a fort and village in eastern New York during the American Revolutionary War, killing more than forty civilians and soldiers.", "html": "1778 - <a href=\"https://wikipedia.org/wiki/Cherry_Valley_massacre\" title=\"Cherry Valley massacre\">Cherry Valley massacre</a>: <a href=\"https://wikipedia.org/wiki/Loyalist_(American_Revolution)\" title=\"Loyalist (American Revolution)\">Loyalists</a> and <a href=\"https://wikipedia.org/wiki/Seneca_nation\" class=\"mw-redirect\" title=\"Seneca nation\">Seneca</a> Indian forces attack a fort and village in eastern New York during the <a href=\"https://wikipedia.org/wiki/American_Revolutionary_War\" title=\"American Revolutionary War\">American Revolutionary War</a>, killing more than forty civilians and soldiers.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Cherry_Valley_massacre\" title=\"Cherry Valley massacre\">Cherry Valley massacre</a>: <a href=\"https://wikipedia.org/wiki/Loyalist_(American_Revolution)\" title=\"Loyalist (American Revolution)\">Loyalists</a> and <a href=\"https://wikipedia.org/wiki/Seneca_nation\" class=\"mw-redirect\" title=\"Seneca nation\">Seneca</a> Indian forces attack a fort and village in eastern New York during the <a href=\"https://wikipedia.org/wiki/American_Revolutionary_War\" title=\"American Revolutionary War\">American Revolutionary War</a>, killing more than forty civilians and soldiers.", "links": [{"title": "Cherry Valley massacre", "link": "https://wikipedia.org/wiki/Cherry_Valley_massacre"}, {"title": "Loyalist (American Revolution)", "link": "https://wikipedia.org/wiki/Loyalist_(American_Revolution)"}, {"title": "Seneca nation", "link": "https://wikipedia.org/wiki/Seneca_nation"}, {"title": "American Revolutionary War", "link": "https://wikipedia.org/wiki/American_Revolutionary_War"}]}, {"year": "1805", "text": "Napoleonic Wars: Battle of Dürenstein: Eight thousand French troops attempt to slow the retreat of a vastly superior Russian and Austrian force.", "html": "1805 - <a href=\"https://wikipedia.org/wiki/Napoleonic_Wars\" title=\"Napoleonic Wars\">Napoleonic Wars</a>: <a href=\"https://wikipedia.org/wiki/Battle_of_D%C3%BCrenstein\" title=\"Battle of Dürenstein\">Battle of Dürenstein</a>: Eight thousand French troops attempt to slow the retreat of a vastly superior Russian and Austrian force.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Napoleonic_Wars\" title=\"Napoleonic Wars\">Napoleonic Wars</a>: <a href=\"https://wikipedia.org/wiki/Battle_of_D%C3%BCrenstein\" title=\"Battle of Dürenstein\">Battle of Dürenstein</a>: Eight thousand French troops attempt to slow the retreat of a vastly superior Russian and Austrian force.", "links": [{"title": "Napoleonic Wars", "link": "https://wikipedia.org/wiki/Napoleonic_Wars"}, {"title": "Battle of Dürenstein", "link": "https://wikipedia.org/wiki/Battle_of_D%C3%<PERSON><PERSON>stein"}]}, {"year": "1813", "text": "War of 1812: Battle of Crysler's Farm: British and Canadian forces defeat a larger American force, causing the Americans to abandon their Saint Lawrence campaign.", "html": "1813 - <a href=\"https://wikipedia.org/wiki/War_of_1812\" title=\"War of 1812\">War of 1812</a>: <a href=\"https://wikipedia.org/wiki/Battle_of_Crysler%27s_Farm\" title=\"Battle of Crysler's Farm\">Battle of Crysler's Farm</a>: British and Canadian forces defeat a larger American force, causing the Americans to abandon their Saint Lawrence campaign.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/War_of_1812\" title=\"War of 1812\">War of 1812</a>: <a href=\"https://wikipedia.org/wiki/Battle_of_Crysler%27s_Farm\" title=\"Battle of Crysler's Farm\">Battle of Crysler's Farm</a>: British and Canadian forces defeat a larger American force, causing the Americans to abandon their Saint Lawrence campaign.", "links": [{"title": "War of 1812", "link": "https://wikipedia.org/wiki/War_of_1812"}, {"title": "Battle of Crysler's Farm", "link": "https://wikipedia.org/wiki/Battle_of_Crysler%27s_Farm"}]}, {"year": "1831", "text": "In Jerusalem, Virginia, <PERSON> is hanged after inciting a violent slave uprising.", "html": "1831 - In <a href=\"https://wikipedia.org/wiki/Jerusalem,_Virginia\" class=\"mw-redirect\" title=\"Jerusalem, Virginia\">Jerusalem, Virginia</a>, <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> is hanged after inciting a violent <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%27s_slave_rebellion\" class=\"mw-redirect\" title=\"<PERSON>'s slave rebellion\">slave uprising</a>.", "no_year_html": "In <a href=\"https://wikipedia.org/wiki/Jerusalem,_Virginia\" class=\"mw-redirect\" title=\"Jerusalem, Virginia\">Jerusalem, Virginia</a>, <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> is hanged after inciting a violent <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%27s_slave_rebellion\" class=\"mw-redirect\" title=\"<PERSON>'s slave rebellion\">slave uprising</a>.", "links": [{"title": "Jerusalem, Virginia", "link": "https://wikipedia.org/wiki/Jerusalem,_Virginia"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "<PERSON>'s slave rebellion", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>%27s_slave_rebellion"}]}, {"year": "1839", "text": "The Virginia Military Institute is founded in Lexington, Virginia.", "html": "1839 - The <a href=\"https://wikipedia.org/wiki/Virginia_Military_Institute\" title=\"Virginia Military Institute\">Virginia Military Institute</a> is founded in <a href=\"https://wikipedia.org/wiki/Lexington,_Virginia\" title=\"Lexington, Virginia\">Lexington, Virginia</a>.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Virginia_Military_Institute\" title=\"Virginia Military Institute\">Virginia Military Institute</a> is founded in <a href=\"https://wikipedia.org/wiki/Lexington,_Virginia\" title=\"Lexington, Virginia\">Lexington, Virginia</a>.", "links": [{"title": "Virginia Military Institute", "link": "https://wikipedia.org/wiki/Virginia_Military_Institute"}, {"title": "Lexington, Virginia", "link": "https://wikipedia.org/wiki/Lexington,_Virginia"}]}, {"year": "1855", "text": "A powerful earthquake occurs in Edo, Japan, causing considerable damage in the Kantō region from the shaking and subsequent fires. It had a death toll of 7,000-10,000 people and destroyed around 14,000 buildings.", "html": "1855 - A powerful <a href=\"https://wikipedia.org/wiki/1855_Edo_earthquake\" title=\"1855 Edo earthquake\">earthquake</a> occurs in <a href=\"https://wikipedia.org/wiki/Edo_(Tokyo)\" class=\"mw-redirect\" title=\"Edo (Tokyo)\">Edo</a>, Japan, causing considerable damage in the Kantō region from the shaking and subsequent fires. It had a death toll of 7,000-10,000 people and destroyed around 14,000 buildings.", "no_year_html": "A powerful <a href=\"https://wikipedia.org/wiki/1855_Edo_earthquake\" title=\"1855 Edo earthquake\">earthquake</a> occurs in <a href=\"https://wikipedia.org/wiki/Edo_(Tokyo)\" class=\"mw-redirect\" title=\"Edo (Tokyo)\">Edo</a>, Japan, causing considerable damage in the Kantō region from the shaking and subsequent fires. It had a death toll of 7,000-10,000 people and destroyed around 14,000 buildings.", "links": [{"title": "1855 Edo earthquake", "link": "https://wikipedia.org/wiki/1855_Edo_earthquake"}, {"title": "Edo (Tokyo)", "link": "https://wikipedia.org/wiki/Edo_(Tokyo)"}]}, {"year": "1865", "text": "Treaty of Sinchula is signed whereby Bhutan cedes the areas east of the Teesta River to the British East India Company.", "html": "1865 - <a href=\"https://wikipedia.org/wiki/Treaty_of_Sinchula\" class=\"mw-redirect\" title=\"Treaty of Sinchula\">Treaty of Sinchula</a> is signed whereby <a href=\"https://wikipedia.org/wiki/Bhutan\" title=\"Bhutan\">Bhutan</a> cedes the areas east of the <a href=\"https://wikipedia.org/wiki/Teesta_River\" title=\"Teesta River\">Teesta River</a> to the <a href=\"https://wikipedia.org/wiki/British_East_India_Company\" class=\"mw-redirect\" title=\"British East India Company\">British East India Company</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Treaty_of_Sinchula\" class=\"mw-redirect\" title=\"Treaty of Sinchula\">Treaty of Sinchula</a> is signed whereby <a href=\"https://wikipedia.org/wiki/Bhutan\" title=\"Bhutan\">Bhutan</a> cedes the areas east of the <a href=\"https://wikipedia.org/wiki/Teesta_River\" title=\"Teesta River\">Teesta River</a> to the <a href=\"https://wikipedia.org/wiki/British_East_India_Company\" class=\"mw-redirect\" title=\"British East India Company\">British East India Company</a>.", "links": [{"title": "Treaty of Sinchula", "link": "https://wikipedia.org/wiki/Treaty_of_Sinchula"}, {"title": "Bhutan", "link": "https://wikipedia.org/wiki/Bhutan"}, {"title": "Teesta River", "link": "https://wikipedia.org/wiki/Teesta_River"}, {"title": "British East India Company", "link": "https://wikipedia.org/wiki/British_East_India_Company"}]}, {"year": "1869", "text": "The Victorian Aboriginal Protection Act is enacted in Australia, giving the government control of indigenous people's wages, their terms of employment, where they could live, and of their children, effectively leading to the Stolen Generations.", "html": "1869 - The <a href=\"https://wikipedia.org/wiki/Victoria,_Australia\" class=\"mw-redirect\" title=\"Victoria, Australia\">Victorian</a> <a href=\"https://wikipedia.org/wiki/Aboriginal_Protection_Act_1869\" title=\"Aboriginal Protection Act 1869\">Aboriginal Protection Act</a> is enacted in Australia, giving the government control of indigenous people's wages, their terms of employment, where they could live, and of their children, effectively leading to the <a href=\"https://wikipedia.org/wiki/Stolen_Generations\" title=\"Stolen Generations\">Stolen Generations</a>.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Victoria,_Australia\" class=\"mw-redirect\" title=\"Victoria, Australia\">Victorian</a> <a href=\"https://wikipedia.org/wiki/Aboriginal_Protection_Act_1869\" title=\"Aboriginal Protection Act 1869\">Aboriginal Protection Act</a> is enacted in Australia, giving the government control of indigenous people's wages, their terms of employment, where they could live, and of their children, effectively leading to the <a href=\"https://wikipedia.org/wiki/Stolen_Generations\" title=\"Stolen Generations\">Stolen Generations</a>.", "links": [{"title": "Victoria, Australia", "link": "https://wikipedia.org/wiki/Victoria,_Australia"}, {"title": "Aboriginal Protection Act 1869", "link": "https://wikipedia.org/wiki/Aboriginal_Protection_Act_1869"}, {"title": "Stolen Generations", "link": "https://wikipedia.org/wiki/Stolen_Generations"}]}, {"year": "1880", "text": "Australian bushranger <PERSON> is hanged at Melbourne Gaol.", "html": "1880 - Australian <a href=\"https://wikipedia.org/wiki/Bushranger\" title=\"Bushranger\">bushranger</a> <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> is hanged at <a href=\"https://wikipedia.org/wiki/Melbourne_Gaol\" class=\"mw-redirect\" title=\"Melbourne Gaol\">Melbourne Gaol</a>.", "no_year_html": "Australian <a href=\"https://wikipedia.org/wiki/Bushranger\" title=\"Bushranger\">bushranger</a> <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> is hanged at <a href=\"https://wikipedia.org/wiki/Melbourne_Gaol\" class=\"mw-redirect\" title=\"Melbourne Gaol\">Melbourne Gaol</a>.", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>er"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Melbourne Gaol", "link": "https://wikipedia.org/wiki/Melbourne_Gaol"}]}, {"year": "1887", "text": "Four convicted anarchists were executed as a result of the Haymarket affair.", "html": "1887 - Four convicted <a href=\"https://wikipedia.org/wiki/Anarchism_in_the_United_States\" title=\"Anarchism in the United States\">anarchists</a> were executed as a result of the <a href=\"https://wikipedia.org/wiki/Haymarket_affair\" title=\"Haymarket affair\">Haymarket affair</a>.", "no_year_html": "Four convicted <a href=\"https://wikipedia.org/wiki/Anarchism_in_the_United_States\" title=\"Anarchism in the United States\">anarchists</a> were executed as a result of the <a href=\"https://wikipedia.org/wiki/Haymarket_affair\" title=\"Haymarket affair\">Haymarket affair</a>.", "links": [{"title": "Anarchism in the United States", "link": "https://wikipedia.org/wiki/Anarchism_in_the_United_States"}, {"title": "Haymarket affair", "link": "https://wikipedia.org/wiki/Haymarket_affair"}]}, {"year": "1889", "text": "The State of Washington is admitted as the 42nd state of the United States.", "html": "1889 - The State of <a href=\"https://wikipedia.org/wiki/Washington_(state)\" title=\"Washington (state)\">Washington</a> is admitted as the 42nd <a href=\"https://wikipedia.org/wiki/U.S._state\" title=\"U.S. state\">state</a> of the United States.", "no_year_html": "The State of <a href=\"https://wikipedia.org/wiki/Washington_(state)\" title=\"Washington (state)\">Washington</a> is admitted as the 42nd <a href=\"https://wikipedia.org/wiki/U.S._state\" title=\"U.S. state\">state</a> of the United States.", "links": [{"title": "Washington (state)", "link": "https://wikipedia.org/wiki/Washington_(state)"}, {"title": "U.S. state", "link": "https://wikipedia.org/wiki/U.S._state"}]}, {"year": "1911", "text": "Many cities in the Midwestern United States break their record highs and lows on the same day as a strong cold front rolls through.", "html": "1911 - Many cities in the <a href=\"https://wikipedia.org/wiki/Midwestern_United_States\" title=\"Midwestern United States\">Midwestern United States</a> <a href=\"https://wikipedia.org/wiki/The_Great_Blue_Norther_of_11/11/11\" class=\"mw-redirect\" title=\"The Great Blue Norther of 11/11/11\">break their record highs and lows on the same day</a> as a strong cold front rolls through.", "no_year_html": "Many cities in the <a href=\"https://wikipedia.org/wiki/Midwestern_United_States\" title=\"Midwestern United States\">Midwestern United States</a> <a href=\"https://wikipedia.org/wiki/The_Great_Blue_Norther_of_11/11/11\" class=\"mw-redirect\" title=\"The Great Blue Norther of 11/11/11\">break their record highs and lows on the same day</a> as a strong cold front rolls through.", "links": [{"title": "Midwestern United States", "link": "https://wikipedia.org/wiki/Midwestern_United_States"}, {"title": "The Great Blue Norther of 11/11/11", "link": "https://wikipedia.org/wiki/The_Great_<PERSON>_Norther_of_11/11/11"}]}, {"year": "1918", "text": "World War I: Germany signs an armistice agreement with the Allies in a railroad car in the forest of Compiègne.", "html": "1918 - <a href=\"https://wikipedia.org/wiki/World_War_I\" title=\"World War I\">World War I</a>: Germany signs an <a href=\"https://wikipedia.org/wiki/Armistice_of_11_November_1918\" title=\"Armistice of 11 November 1918\">armistice</a> agreement with <a href=\"https://wikipedia.org/wiki/Allies_of_World_War_I\" title=\"Allies of World War I\">the Allies</a> in a railroad car in the <a href=\"https://wikipedia.org/wiki/Forest_of_Compi%C3%A8gne\" title=\"Forest of Compiègne\">forest of Compiègne</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/World_War_I\" title=\"World War I\">World War I</a>: Germany signs an <a href=\"https://wikipedia.org/wiki/Armistice_of_11_November_1918\" title=\"Armistice of 11 November 1918\">armistice</a> agreement with <a href=\"https://wikipedia.org/wiki/Allies_of_World_War_I\" title=\"Allies of World War I\">the Allies</a> in a railroad car in the <a href=\"https://wikipedia.org/wiki/Forest_of_Compi%C3%A8gne\" title=\"Forest of Compiègne\">forest of Compiègne</a>.", "links": [{"title": "World War I", "link": "https://wikipedia.org/wiki/World_War_I"}, {"title": "Armistice of 11 November 1918", "link": "https://wikipedia.org/wiki/Armistice_of_11_November_1918"}, {"title": "Allies of World War I", "link": "https://wikipedia.org/wiki/Allies_of_World_War_I"}, {"title": "Forest of Compiègne", "link": "https://wikipedia.org/wiki/Forest_of_Compi%C3%A8gne"}]}, {"year": "1918", "text": "<PERSON><PERSON><PERSON> assumes supreme military power in Poland - symbolic first day of Polish independence.", "html": "1918 - <a href=\"https://wikipedia.org/wiki/J%C3%B3zef_Pi%C5%82sudski\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a> assumes supreme military power in Poland - symbolic first day of <a href=\"https://wikipedia.org/wiki/Polish_Independence_Day\" class=\"mw-redirect\" title=\"Polish Independence Day\">Polish independence</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/J%C3%B3<PERSON>_Pi%C5%82sudski\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a> assumes supreme military power in Poland - symbolic first day of <a href=\"https://wikipedia.org/wiki/Polish_Independence_Day\" class=\"mw-redirect\" title=\"Polish Independence Day\">Polish independence</a>.", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/J%C3%B3zef_Pi%C5%82sudski"}, {"title": "Polish Independence Day", "link": "https://wikipedia.org/wiki/Polish_Independence_Day"}]}, {"year": "1918", "text": "Emperor <PERSON> I of Austria relinquishes power.", "html": "1918 - Emperor <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Austria\" title=\"<PERSON> of Austria\"><PERSON> of Austria</a> relinquishes power.", "no_year_html": "Emperor <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Austria\" title=\"<PERSON> of Austria\"><PERSON> of Austria</a> relinquishes power.", "links": [{"title": "<PERSON> of Austria", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_Austria"}]}, {"year": "1919", "text": "The Industrial Workers of the World attack an Armistice Day parade in Centralia, Washington, ultimately resulting in the deaths of five people.", "html": "1919 - The <a href=\"https://wikipedia.org/wiki/Industrial_Workers_of_the_World\" title=\"Industrial Workers of the World\">Industrial Workers of the World</a> <a href=\"https://wikipedia.org/wiki/Centralia_Massacre_(Washington)\" class=\"mw-redirect\" title=\"Centralia Massacre (Washington)\">attack</a> an <a href=\"https://wikipedia.org/wiki/Armistice_Day\" title=\"Armistice Day\">Armistice Day</a> parade in <a href=\"https://wikipedia.org/wiki/Centralia,_Washington\" title=\"Centralia, Washington\">Centralia, Washington</a>, ultimately resulting in the deaths of five people.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Industrial_Workers_of_the_World\" title=\"Industrial Workers of the World\">Industrial Workers of the World</a> <a href=\"https://wikipedia.org/wiki/Centralia_Massacre_(Washington)\" class=\"mw-redirect\" title=\"Centralia Massacre (Washington)\">attack</a> an <a href=\"https://wikipedia.org/wiki/Armistice_Day\" title=\"Armistice Day\">Armistice Day</a> parade in <a href=\"https://wikipedia.org/wiki/Centralia,_Washington\" title=\"Centralia, Washington\">Centralia, Washington</a>, ultimately resulting in the deaths of five people.", "links": [{"title": "Industrial Workers of the World", "link": "https://wikipedia.org/wiki/Industrial_Workers_of_the_World"}, {"title": "Centralia Massacre (Washington)", "link": "https://wikipedia.org/wiki/Centralia_Massacre_(Washington)"}, {"title": "Armistice Day", "link": "https://wikipedia.org/wiki/Armistice_Day"}, {"title": "Centralia, Washington", "link": "https://wikipedia.org/wiki/Centralia,_Washington"}]}, {"year": "1919", "text": "Latvian forces defeat the West Russian Volunteer Army at Riga in the Latvian War of Independence.", "html": "1919 - Latvian forces defeat the <a href=\"https://wikipedia.org/wiki/West_Russian_Volunteer_Army\" title=\"West Russian Volunteer Army\">West Russian Volunteer Army</a> at Riga in the <a href=\"https://wikipedia.org/wiki/Latvian_War_of_Independence\" title=\"Latvian War of Independence\">Latvian War of Independence</a>.", "no_year_html": "Latvian forces defeat the <a href=\"https://wikipedia.org/wiki/West_Russian_Volunteer_Army\" title=\"West Russian Volunteer Army\">West Russian Volunteer Army</a> at Riga in the <a href=\"https://wikipedia.org/wiki/Latvian_War_of_Independence\" title=\"Latvian War of Independence\">Latvian War of Independence</a>.", "links": [{"title": "West Russian Volunteer Army", "link": "https://wikipedia.org/wiki/West_Russian_Volunteer_Army"}, {"title": "Latvian War of Independence", "link": "https://wikipedia.org/wiki/Latvian_War_of_Independence"}]}, {"year": "1921", "text": "The Tomb of the Unknowns is dedicated by U.S. President <PERSON> at Arlington National Cemetery.", "html": "1921 - The <a href=\"https://wikipedia.org/wiki/Tomb_of_the_Unknowns\" class=\"mw-redirect\" title=\"Tomb of the Unknowns\">Tomb of the Unknowns</a> is dedicated by U.S. President <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> at <a href=\"https://wikipedia.org/wiki/Arlington_National_Cemetery\" title=\"Arlington National Cemetery\">Arlington National Cemetery</a>.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Tomb_of_the_Unknowns\" class=\"mw-redirect\" title=\"Tomb of the Unknowns\">Tomb of the Unknowns</a> is dedicated by U.S. President <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> at <a href=\"https://wikipedia.org/wiki/Arlington_National_Cemetery\" title=\"Arlington National Cemetery\">Arlington National Cemetery</a>.", "links": [{"title": "Tomb of the Unknowns", "link": "https://wikipedia.org/wiki/Tomb_of_the_Unknowns"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>"}, {"title": "Arlington National Cemetery", "link": "https://wikipedia.org/wiki/Arlington_National_Cemetery"}]}, {"year": "1923", "text": "<PERSON> is arrested in Munich for high treason for his role in the Beer Hall Putsch.", "html": "1923 - <a href=\"https://wikipedia.org/wiki/Adolf_<PERSON>\" title=\"Adolf Hitler\"><PERSON></a> is arrested in <a href=\"https://wikipedia.org/wiki/Munich\" title=\"Munich\">Munich</a> for <a href=\"https://wikipedia.org/wiki/High_treason\" class=\"mw-redirect\" title=\"High treason\">high treason</a> for his role in the <a href=\"https://wikipedia.org/wiki/Beer_Hall_Putsch\" title=\"Beer Hall Putsch\">Beer Hall Putsch</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Adolf_<PERSON>\" title=\"Adolf Hitler\"><PERSON></a> is arrested in <a href=\"https://wikipedia.org/wiki/Munich\" title=\"Munich\">Munich</a> for <a href=\"https://wikipedia.org/wiki/High_treason\" class=\"mw-redirect\" title=\"High treason\">high treason</a> for his role in the <a href=\"https://wikipedia.org/wiki/Beer_Hall_Putsch\" title=\"Beer Hall Putsch\">Beer Hall Putsch</a>.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Munich", "link": "https://wikipedia.org/wiki/Munich"}, {"title": "High treason", "link": "https://wikipedia.org/wiki/High_treason"}, {"title": "Beer Hall Putsch", "link": "https://wikipedia.org/wiki/Beer_Hall_Putsch"}]}, {"year": "1926", "text": "The United States Numbered Highway System is established.", "html": "1926 - The <a href=\"https://wikipedia.org/wiki/United_States_Numbered_Highways\" class=\"mw-redirect\" title=\"United States Numbered Highways\">United States Numbered Highway System</a> is established.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/United_States_Numbered_Highways\" class=\"mw-redirect\" title=\"United States Numbered Highways\">United States Numbered Highway System</a> is established.", "links": [{"title": "United States Numbered Highways", "link": "https://wikipedia.org/wiki/United_States_Numbered_Highways"}]}, {"year": "1930", "text": "Patent number US1781541 is awarded to <PERSON> and <PERSON><PERSON> for their invention, the Einstein refrigerator.", "html": "1930 - <a href=\"https://wikipedia.org/wiki/Patent\" title=\"Patent\">Patent</a> number US1781541 is awarded to <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON> Einstein\"><PERSON></a> and <a href=\"https://wikipedia.org/wiki/Le%C3%B3_Szil%C3%A1rd\" class=\"mw-redirect\" title=\"Le<PERSON>\"><PERSON><PERSON></a> for their invention, the <a href=\"https://wikipedia.org/wiki/Einstein_refrigerator\" title=\"Einstein refrigerator\">Einstein refrigerator</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Patent\" title=\"Patent\">Patent</a> number US1781541 is awarded to <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON> Einstein\"><PERSON></a> and <a href=\"https://wikipedia.org/wiki/Le%C3%B3_Szil%C3%A1rd\" class=\"mw-redirect\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a> for their invention, the <a href=\"https://wikipedia.org/wiki/Einstein_refrigerator\" title=\"Einstein refrigerator\">Einstein refrigerator</a>.", "links": [{"title": "Patent", "link": "https://wikipedia.org/wiki/Patent"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}, {"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Le%C3%B3_Szil%C3%A1rd"}, {"title": "Einstein refrigerator", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1934", "text": "The Shrine of Remembrance is opened in Melbourne, Australia.", "html": "1934 - The <a href=\"https://wikipedia.org/wiki/Shrine_of_Remembrance\" title=\"Shrine of Remembrance\">Shrine of Remembrance</a> is opened in <a href=\"https://wikipedia.org/wiki/Melbourne\" title=\"Melbourne\">Melbourne</a>, Australia.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Shrine_of_Remembrance\" title=\"Shrine of Remembrance\">Shrine of Remembrance</a> is opened in <a href=\"https://wikipedia.org/wiki/Melbourne\" title=\"Melbourne\">Melbourne</a>, Australia.", "links": [{"title": "Shrine of Remembrance", "link": "https://wikipedia.org/wiki/Shrine_of_Remembrance"}, {"title": "Melbourne", "link": "https://wikipedia.org/wiki/Melbourne"}]}, {"year": "1940", "text": "World War II: In the Battle of Taranto, the Royal Navy launches the first all-aircraft ship-to-ship naval attack in history.", "html": "1940 - <a href=\"https://wikipedia.org/wiki/World_War_II\" title=\"World War II\">World War II</a>: In the <a href=\"https://wikipedia.org/wiki/Battle_of_Taranto\" title=\"Battle of Taranto\">Battle of Taranto</a>, the <a href=\"https://wikipedia.org/wiki/Royal_Navy\" title=\"Royal Navy\">Royal Navy</a> launches the first all-aircraft ship-to-ship naval attack in history.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/World_War_II\" title=\"World War II\">World War II</a>: In the <a href=\"https://wikipedia.org/wiki/Battle_of_Taranto\" title=\"Battle of Taranto\">Battle of Taranto</a>, the <a href=\"https://wikipedia.org/wiki/Royal_Navy\" title=\"Royal Navy\">Royal Navy</a> launches the first all-aircraft ship-to-ship naval attack in history.", "links": [{"title": "World War II", "link": "https://wikipedia.org/wiki/World_War_II"}, {"title": "Battle of Taranto", "link": "https://wikipedia.org/wiki/Battle_of_Taranto"}, {"title": "Royal Navy", "link": "https://wikipedia.org/wiki/Royal_Navy"}]}, {"year": "1940", "text": "World War II: The German auxiliary cruiser Atlantis captures top secret British mail from the Automedon, and sends it to Japan.", "html": "1940 - World War II: The German auxiliary cruiser <i><a href=\"https://wikipedia.org/wiki/German_auxiliary_cruiser_Atlantis\" title=\"German auxiliary cruiser Atlantis\">Atlantis</a></i> captures <a href=\"https://wikipedia.org/wiki/Classified_information\" title=\"Classified information\">top secret</a> British mail from the <i><a href=\"https://wikipedia.org/wiki/SS_Automedon\" title=\"SS Automedon\">Automedon</a></i>, and sends it to Japan.", "no_year_html": "World War II: The German auxiliary cruiser <i><a href=\"https://wikipedia.org/wiki/German_auxiliary_cruiser_Atlantis\" title=\"German auxiliary cruiser Atlantis\">Atlantis</a></i> captures <a href=\"https://wikipedia.org/wiki/Classified_information\" title=\"Classified information\">top secret</a> British mail from the <i><a href=\"https://wikipedia.org/wiki/SS_Automedon\" title=\"SS Automedon\">Automedon</a></i>, and sends it to Japan.", "links": [{"title": "German auxiliary cruiser Atlantis", "link": "https://wikipedia.org/wiki/German_auxiliary_cruiser_Atlantis"}, {"title": "Classified information", "link": "https://wikipedia.org/wiki/Classified_information"}, {"title": "SS Automedon", "link": "https://wikipedia.org/wiki/SS_Automedon"}]}, {"year": "1942", "text": "World War II: France's zone libre is occupied by German forces in Case Anton.", "html": "1942 - World War II: France's <i><a href=\"https://wikipedia.org/wiki/Zone_libre\" title=\"Zone libre\">zone libre</a></i> is occupied by German forces in <a href=\"https://wikipedia.org/wiki/Case_Anton\" title=\"Case Anton\"><PERSON></a>.", "no_year_html": "World War II: France's <i><a href=\"https://wikipedia.org/wiki/Zone_libre\" title=\"Zone libre\">zone libre</a></i> is occupied by German forces in <a href=\"https://wikipedia.org/wiki/Case_Anton\" title=\"Case Anton\"><PERSON></a>.", "links": [{"title": "Zone libre", "link": "https://wikipedia.org/wiki/Zone_libre"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1942", "text": "The Turkish parliament passes the Varlık Vergisi, a capital tax mostly levied on non-Muslim citizens with the unofficial aim to inflict financial ruin on them and end their prominence in the country's economy.", "html": "1942 - The <a href=\"https://wikipedia.org/wiki/Grand_National_Assembly_of_Turkey\" title=\"Grand National Assembly of Turkey\">Turkish parliament</a> passes the <a href=\"https://wikipedia.org/wiki/Varl%C4%B1k_Vergisi\" title=\"Varlık Vergisi\">Varlık Vergisi</a>, a capital tax mostly levied on non-Muslim citizens with the unofficial aim to inflict financial ruin on them and end their prominence in the country's economy.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Grand_National_Assembly_of_Turkey\" title=\"Grand National Assembly of Turkey\">Turkish parliament</a> passes the <a href=\"https://wikipedia.org/wiki/Varl%C4%B1k_Vergisi\" title=\"Varlık Vergisi\">Varlık Vergisi</a>, a capital tax mostly levied on non-Muslim citizens with the unofficial aim to inflict financial ruin on them and end their prominence in the country's economy.", "links": [{"title": "Grand National Assembly of Turkey", "link": "https://wikipedia.org/wiki/Grand_National_Assembly_of_Turkey"}, {"title": "Varlık Vergisi", "link": "https://wikipedia.org/wiki/Varl%C4%B1k_Vergisi"}]}, {"year": "1960", "text": "A military coup against President <PERSON><PERSON> of South Vietnam is crushed.", "html": "1960 - A military coup against <a href=\"https://wikipedia.org/wiki/President_of_Vietnam\" title=\"President of Vietnam\">President</a> <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a> of <a href=\"https://wikipedia.org/wiki/South_Vietnam\" title=\"South Vietnam\">South Vietnam</a> <a href=\"https://wikipedia.org/wiki/1960_South_Vietnamese_coup_attempt\" title=\"1960 South Vietnamese coup attempt\">is crushed</a>.", "no_year_html": "A military coup against <a href=\"https://wikipedia.org/wiki/President_of_Vietnam\" title=\"President of Vietnam\">President</a> <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a> of <a href=\"https://wikipedia.org/wiki/South_Vietnam\" title=\"South Vietnam\">South Vietnam</a> <a href=\"https://wikipedia.org/wiki/1960_South_Vietnamese_coup_attempt\" title=\"1960 South Vietnamese coup attempt\">is crushed</a>.", "links": [{"title": "President of Vietnam", "link": "https://wikipedia.org/wiki/President_of_Vietnam"}, {"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>"}, {"title": "South Vietnam", "link": "https://wikipedia.org/wiki/South_Vietnam"}, {"title": "1960 South Vietnamese coup attempt", "link": "https://wikipedia.org/wiki/1960_South_Vietnamese_coup_attempt"}]}, {"year": "1961", "text": "Thirteen Italian Air Force servicemen, deployed to the Congo as a part of the UN peacekeeping force, are massacred by a mob in Kindu.", "html": "1961 - Thirteen Italian Air Force servicemen, deployed to the Congo as a part of the UN peacekeeping force, are massacred by a mob in <a href=\"https://wikipedia.org/wiki/Kindu_atrocity\" title=\"Kindu atrocity\">Kindu</a>.", "no_year_html": "Thirteen Italian Air Force servicemen, deployed to the Congo as a part of the UN peacekeeping force, are massacred by a mob in <a href=\"https://wikipedia.org/wiki/Kindu_atrocity\" title=\"Kindu atrocity\">Kindu</a>.", "links": [{"title": "Kindu atrocity", "link": "https://wikipedia.org/wiki/Kindu_atrocity"}]}, {"year": "1962", "text": "Kuwait's National Assembly ratifies the Constitution of Kuwait.", "html": "1962 - <a href=\"https://wikipedia.org/wiki/Kuwait\" title=\"Kuwait\">Kuwait</a>'s National Assembly ratifies the <a href=\"https://wikipedia.org/wiki/Constitution_of_Kuwait\" title=\"Constitution of Kuwait\">Constitution of Kuwait</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Kuwait\" title=\"Kuwait\">Kuwait</a>'s National Assembly ratifies the <a href=\"https://wikipedia.org/wiki/Constitution_of_Kuwait\" title=\"Constitution of Kuwait\">Constitution of Kuwait</a>.", "links": [{"title": "Kuwait", "link": "https://wikipedia.org/wiki/Kuwait"}, {"title": "Constitution of Kuwait", "link": "https://wikipedia.org/wiki/Constitution_of_Kuwait"}]}, {"year": "1965", "text": "Southern Rhodesia's Prime Minister <PERSON> unilaterally declares the colony independent as the unrecognised state of Rhodesia.", "html": "1965 - <a href=\"https://wikipedia.org/wiki/Southern_Rhodesia\" title=\"Southern Rhodesia\">Southern Rhodesia</a>'s Prime Minister <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> <a href=\"https://wikipedia.org/wiki/Rhodesia%27s_Unilateral_Declaration_of_Independence\" title=\"Rhodesia's Unilateral Declaration of Independence\">unilaterally declares the colony independent</a> as the <a href=\"https://wikipedia.org/wiki/List_of_historical_unrecognized_states_and_dependencies\" class=\"mw-redirect\" title=\"List of historical unrecognized states and dependencies\">unrecognised state</a> of <a href=\"https://wikipedia.org/wiki/Rhodesia\" title=\"Rhodesia\">Rhodesia</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Southern_Rhodesia\" title=\"Southern Rhodesia\">Southern Rhodesia</a>'s Prime Minister <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> <a href=\"https://wikipedia.org/wiki/Rhodesia%27s_Unilateral_Declaration_of_Independence\" title=\"Rhodesia's Unilateral Declaration of Independence\">unilaterally declares the colony independent</a> as the <a href=\"https://wikipedia.org/wiki/List_of_historical_unrecognized_states_and_dependencies\" class=\"mw-redirect\" title=\"List of historical unrecognized states and dependencies\">unrecognised state</a> of <a href=\"https://wikipedia.org/wiki/Rhodesia\" title=\"Rhodesia\">Rhodesia</a>.", "links": [{"title": "Southern Rhodesia", "link": "https://wikipedia.org/wiki/Southern_Rhodesia"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Rhodesia's Unilateral Declaration of Independence", "link": "https://wikipedia.org/wiki/Rhodesia%27s_Unilateral_Declaration_of_Independence"}, {"title": "List of historical unrecognized states and dependencies", "link": "https://wikipedia.org/wiki/List_of_historical_unrecognized_states_and_dependencies"}, {"title": "Rhodesia", "link": "https://wikipedia.org/wiki/Rhodesia"}]}, {"year": "1965", "text": "United Air Lines Flight 227 crashes at Salt Lake City International Airport, killing 43.", "html": "1965 - <a href=\"https://wikipedia.org/wiki/United_Air_Lines_Flight_227\" title=\"United Air Lines Flight 227\">United Air Lines Flight 227</a> crashes at <a href=\"https://wikipedia.org/wiki/Salt_Lake_City_International_Airport\" title=\"Salt Lake City International Airport\">Salt Lake City International Airport</a>, killing 43.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/United_Air_Lines_Flight_227\" title=\"United Air Lines Flight 227\">United Air Lines Flight 227</a> crashes at <a href=\"https://wikipedia.org/wiki/Salt_Lake_City_International_Airport\" title=\"Salt Lake City International Airport\">Salt Lake City International Airport</a>, killing 43.", "links": [{"title": "United Air Lines Flight 227", "link": "https://wikipedia.org/wiki/United_Air_Lines_Flight_227"}, {"title": "Salt Lake City International Airport", "link": "https://wikipedia.org/wiki/Salt_Lake_City_International_Airport"}]}, {"year": "1966", "text": "NASA launches Gemini 12.", "html": "1966 - <a href=\"https://wikipedia.org/wiki/NASA\" title=\"NASA\">NASA</a> launches <a href=\"https://wikipedia.org/wiki/Gemini_12\" title=\"Gemini 12\">Gemini 12</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/NASA\" title=\"NASA\">NASA</a> launches <a href=\"https://wikipedia.org/wiki/Gemini_12\" title=\"Gemini 12\">Gemini 12</a>.", "links": [{"title": "NASA", "link": "https://wikipedia.org/wiki/NASA"}, {"title": "Gemini 12", "link": "https://wikipedia.org/wiki/Gemini_12"}]}, {"year": "1967", "text": "Vietnam War: In a propaganda ceremony in Phnom Penh, Cambodia, three American prisoners of war are released by the Viet Cong and turned over to \"new left\" antiwar activist <PERSON>.", "html": "1967 - <a href=\"https://wikipedia.org/wiki/Vietnam_War\" title=\"Vietnam War\">Vietnam War</a>: In a propaganda ceremony in <a href=\"https://wikipedia.org/wiki/Phnom_Penh\" title=\"Phnom Penh\">Phnom Penh</a>, <a href=\"https://wikipedia.org/wiki/Cambodia\" title=\"Cambodia\">Cambodia</a>, three American prisoners of war are released by the <a href=\"https://wikipedia.org/wiki/Viet_Cong\" title=\"Viet Cong\">Viet Cong</a> and turned over to \"new left\" antiwar activist <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>\" title=\"<PERSON>\"><PERSON></a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Vietnam_War\" title=\"Vietnam War\">Vietnam War</a>: In a propaganda ceremony in <a href=\"https://wikipedia.org/wiki/Phnom_Penh\" title=\"Phnom Penh\">Phnom Penh</a>, <a href=\"https://wikipedia.org/wiki/Cambodia\" title=\"Cambodia\">Cambodia</a>, three American prisoners of war are released by the <a href=\"https://wikipedia.org/wiki/Viet_Cong\" title=\"Viet Cong\">Viet Cong</a> and turned over to \"new left\" antiwar activist <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>.", "links": [{"title": "Vietnam War", "link": "https://wikipedia.org/wiki/Vietnam_War"}, {"title": "Phnom Penh", "link": "https://wikipedia.org/wiki/Phnom_Penh"}, {"title": "Cambodia", "link": "https://wikipedia.org/wiki/Cambodia"}, {"title": "Viet Cong", "link": "https://wikipedia.org/wiki/Viet_Cong"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1968", "text": "Vietnam War: Operation <PERSON> Hunt initiated. The goal is to interdict men and supplies on the Ho Chi Minh trail, through Laos into South Vietnam.", "html": "1968 - Vietnam War: <a href=\"https://wikipedia.org/wiki/Operation_Commando_Hunt\" title=\"Operation Commando Hunt\">Operation Commando Hunt</a> initiated. The goal is to interdict men and supplies on the <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_trail\" title=\"<PERSON> trail\"><PERSON> trail</a>, through <a href=\"https://wikipedia.org/wiki/Laos\" title=\"Laos\">Laos</a> into <a href=\"https://wikipedia.org/wiki/South_Vietnam\" title=\"South Vietnam\">South Vietnam</a>.", "no_year_html": "Vietnam War: <a href=\"https://wikipedia.org/wiki/Operation_Commando_Hunt\" title=\"Operation Commando Hunt\">Operation Commando Hunt</a> initiated. The goal is to interdict men and supplies on the <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_trail\" title=\"<PERSON> trail\"><PERSON> trail</a>, through <a href=\"https://wikipedia.org/wiki/Laos\" title=\"Laos\">Laos</a> into <a href=\"https://wikipedia.org/wiki/South_Vietnam\" title=\"South Vietnam\">South Vietnam</a>.", "links": [{"title": "Operation Commando Hunt", "link": "https://wikipedia.org/wiki/Operation_Commando_Hunt"}, {"title": "Ho Chi Minh trail", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_trail"}, {"title": "Laos", "link": "https://wikipedia.org/wiki/Laos"}, {"title": "South Vietnam", "link": "https://wikipedia.org/wiki/South_Vietnam"}]}, {"year": "1972", "text": "Vietnam War: Vietnamization: The United States Army turns over the massive Long Binh military base to South Vietnam.", "html": "1972 - Vietnam War: <a href=\"https://wikipedia.org/wiki/Vietnamization\" title=\"Vietnamization\">Vietnamization</a>: The United States Army turns over the massive <a href=\"https://wikipedia.org/wiki/Long_Binh_military_base\" class=\"mw-redirect\" title=\"Long Binh military base\">Long Binh military base</a> to <a href=\"https://wikipedia.org/wiki/South_Vietnam\" title=\"South Vietnam\">South Vietnam</a>.", "no_year_html": "Vietnam War: <a href=\"https://wikipedia.org/wiki/Vietnamization\" title=\"Vietnamization\">Vietnamization</a>: The United States Army turns over the massive <a href=\"https://wikipedia.org/wiki/Long_Binh_military_base\" class=\"mw-redirect\" title=\"Long Binh military base\">Long Binh military base</a> to <a href=\"https://wikipedia.org/wiki/South_Vietnam\" title=\"South Vietnam\">South Vietnam</a>.", "links": [{"title": "Vietnamization", "link": "https://wikipedia.org/wiki/Vietnamization"}, {"title": "Long Binh military base", "link": "https://wikipedia.org/wiki/Long_Binh_military_base"}, {"title": "South Vietnam", "link": "https://wikipedia.org/wiki/South_Vietnam"}]}, {"year": "1975", "text": "Australian constitutional crisis of 1975: Australian Governor-General Sir <PERSON> dismisses the government of <PERSON><PERSON>, appoints <PERSON> as caretaker Prime Minister and announces a general election to be held in early December.", "html": "1975 - <a href=\"https://wikipedia.org/wiki/Australian_constitutional_crisis_of_1975\" class=\"mw-redirect\" title=\"Australian constitutional crisis of 1975\">Australian constitutional crisis of 1975</a>: Australian <a href=\"https://wikipedia.org/wiki/Governor-General_of_Australia\" title=\"Governor-General of Australia\">Governor-General</a> Sir <a href=\"https://wikipedia.org/wiki/<PERSON>(governor-general)\" title=\"<PERSON> (governor-general)\"><PERSON></a> dismisses the government of <a href=\"https://wikipedia.org/wiki/Gough_Whitlam\" title=\"<PERSON>ugh Whitlam\"><PERSON><PERSON></a>, appoints <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> as caretaker <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Australia\" title=\"Prime Minister of Australia\">Prime Minister</a> and announces a <a href=\"https://wikipedia.org/wiki/1975_Australian_federal_election\" title=\"1975 Australian federal election\">general election to be held in early December</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Australian_constitutional_crisis_of_1975\" class=\"mw-redirect\" title=\"Australian constitutional crisis of 1975\">Australian constitutional crisis of 1975</a>: Australian <a href=\"https://wikipedia.org/wiki/Governor-General_of_Australia\" title=\"Governor-General of Australia\">Governor-General</a> Sir <a href=\"https://wikipedia.org/wiki/<PERSON>(governor-general)\" title=\"<PERSON> (governor-general)\"><PERSON></a> dismisses the government of <a href=\"https://wikipedia.org/wiki/Gough_Whitlam\" title=\"Gough Whitlam\"><PERSON><PERSON></a>, appoints <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> as caretaker <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Australia\" title=\"Prime Minister of Australia\">Prime Minister</a> and announces a <a href=\"https://wikipedia.org/wiki/1975_Australian_federal_election\" title=\"1975 Australian federal election\">general election to be held in early December</a>.", "links": [{"title": "Australian constitutional crisis of 1975", "link": "https://wikipedia.org/wiki/Australian_constitutional_crisis_of_1975"}, {"title": "Governor-General of Australia", "link": "https://wikipedia.org/wiki/Governor-General_of_Australia"}, {"title": "<PERSON> (governor-general)", "link": "https://wikipedia.org/wiki/<PERSON>(governor-general)"}, {"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Go<PERSON>_Whitlam"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Prime Minister of Australia", "link": "https://wikipedia.org/wiki/Prime_Minister_of_Australia"}, {"title": "1975 Australian federal election", "link": "https://wikipedia.org/wiki/1975_Australian_federal_election"}]}, {"year": "1975", "text": "Independence of Angola.", "html": "1975 - Independence of <a href=\"https://wikipedia.org/wiki/Angola\" title=\"Angola\">Angola</a>.", "no_year_html": "Independence of <a href=\"https://wikipedia.org/wiki/Angola\" title=\"Angola\">Angola</a>.", "links": [{"title": "Angola", "link": "https://wikipedia.org/wiki/Angola"}]}, {"year": "1977", "text": "A munitions explosion at a train station in Iri, South Korea kills at least 56 people.", "html": "1977 - A <a href=\"https://wikipedia.org/wiki/Iri_station_explosion\" title=\"Iri station explosion\">munitions explosion</a> at a train station in <a href=\"https://wikipedia.org/wiki/Iks<PERSON>\" title=\"<PERSON><PERSON><PERSON>\">Iri, South Korea</a> kills at least 56 people.", "no_year_html": "A <a href=\"https://wikipedia.org/wiki/Iri_station_explosion\" title=\"Iri station explosion\">munitions explosion</a> at a train station in <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\">Iri, South Korea</a> kills at least 56 people.", "links": [{"title": "Iri station explosion", "link": "https://wikipedia.org/wiki/Iri_station_explosion"}, {"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>an"}]}, {"year": "1981", "text": "Antigua and Barbuda joins the United Nations.", "html": "1981 - <a href=\"https://wikipedia.org/wiki/Antigua_and_Barbuda\" title=\"Antigua and Barbuda\">Antigua and Barbuda</a> joins the <a href=\"https://wikipedia.org/wiki/United_Nations\" title=\"United Nations\">United Nations</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Antigua_and_Barbuda\" title=\"Antigua and Barbuda\">Antigua and Barbuda</a> joins the <a href=\"https://wikipedia.org/wiki/United_Nations\" title=\"United Nations\">United Nations</a>.", "links": [{"title": "Antigua and Barbuda", "link": "https://wikipedia.org/wiki/Antigua_and_Barbuda"}, {"title": "United Nations", "link": "https://wikipedia.org/wiki/United_Nations"}]}, {"year": "1982", "text": "Space Shuttle Columbia launches from the Kennedy Space Center on STS-5, the first operational mission of the Space Shuttle program.", "html": "1982 - <a href=\"https://wikipedia.org/wiki/Space_Shuttle_Columbia\" title=\"Space Shuttle Columbia\">Space Shuttle <i>Columbia</i></a> launches from the <a href=\"https://wikipedia.org/wiki/Kennedy_Space_Center\" title=\"Kennedy Space Center\">Kennedy Space Center</a> on <a href=\"https://wikipedia.org/wiki/STS-5\" title=\"STS-5\">STS-5</a>, the first operational mission of the <a href=\"https://wikipedia.org/wiki/Space_Shuttle_program\" title=\"Space Shuttle program\">Space Shuttle program</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Space_Shuttle_Columbia\" title=\"Space Shuttle Columbia\">Space Shuttle <i>Columbia</i></a> launches from the <a href=\"https://wikipedia.org/wiki/Kennedy_Space_Center\" title=\"Kennedy Space Center\">Kennedy Space Center</a> on <a href=\"https://wikipedia.org/wiki/STS-5\" title=\"STS-5\">STS-5</a>, the first operational mission of the <a href=\"https://wikipedia.org/wiki/Space_Shuttle_program\" title=\"Space Shuttle program\">Space Shuttle program</a>.", "links": [{"title": "Space Shuttle Columbia", "link": "https://wikipedia.org/wiki/Space_Shuttle_Columbia"}, {"title": "Kennedy Space Center", "link": "https://wikipedia.org/wiki/Kennedy_Space_Center"}, {"title": "STS-5", "link": "https://wikipedia.org/wiki/STS-5"}, {"title": "Space Shuttle program", "link": "https://wikipedia.org/wiki/Space_Shuttle_program"}]}, {"year": "1992", "text": "The General Synod of the Church of England votes to allow women to become priests.", "html": "1992 - The <a href=\"https://wikipedia.org/wiki/General_Synod_of_the_Church_of_England\" title=\"General Synod of the Church of England\">General Synod of the Church of England</a> votes to allow women to become priests.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/General_Synod_of_the_Church_of_England\" title=\"General Synod of the Church of England\">General Synod of the Church of England</a> votes to allow women to become priests.", "links": [{"title": "General Synod of the Church of England", "link": "https://wikipedia.org/wiki/General_Synod_of_the_Church_of_England"}]}, {"year": "1993", "text": "A sculpture honoring women who served in the Vietnam War is dedicated at the Vietnam Veterans Memorial in Washington, D.C.", "html": "1993 - A sculpture honoring women who served in the <a href=\"https://wikipedia.org/wiki/Vietnam_War\" title=\"Vietnam War\">Vietnam War</a> is dedicated at the <a href=\"https://wikipedia.org/wiki/Vietnam_Veterans_Memorial\" title=\"Vietnam Veterans Memorial\">Vietnam Veterans Memorial</a> in Washington, D.C.", "no_year_html": "A sculpture honoring women who served in the <a href=\"https://wikipedia.org/wiki/Vietnam_War\" title=\"Vietnam War\">Vietnam War</a> is dedicated at the <a href=\"https://wikipedia.org/wiki/Vietnam_Veterans_Memorial\" title=\"Vietnam Veterans Memorial\">Vietnam Veterans Memorial</a> in Washington, D.C.", "links": [{"title": "Vietnam War", "link": "https://wikipedia.org/wiki/Vietnam_War"}, {"title": "Vietnam Veterans Memorial", "link": "https://wikipedia.org/wiki/Vietnam_Veterans_Memorial"}]}, {"year": "1999", "text": "The House of Lords Act is given Royal Assent, restricting membership of the British House of Lords by virtue of a hereditary peerage.", "html": "1999 - The <a href=\"https://wikipedia.org/wiki/House_of_Lords_Act_1999\" title=\"House of Lords Act 1999\">House of Lords Act</a> is given <a href=\"https://wikipedia.org/wiki/Royal_Assent\" class=\"mw-redirect\" title=\"Royal Assent\">Royal Assent</a>, restricting membership of the <a href=\"https://wikipedia.org/wiki/House_of_Lords\" title=\"House of Lords\">British House of Lords</a> by virtue of a hereditary peerage.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/House_of_Lords_Act_1999\" title=\"House of Lords Act 1999\">House of Lords Act</a> is given <a href=\"https://wikipedia.org/wiki/Royal_Assent\" class=\"mw-redirect\" title=\"Royal Assent\">Royal Assent</a>, restricting membership of the <a href=\"https://wikipedia.org/wiki/House_of_Lords\" title=\"House of Lords\">British House of Lords</a> by virtue of a hereditary peerage.", "links": [{"title": "House of Lords Act 1999", "link": "https://wikipedia.org/wiki/House_of_Lords_Act_1999"}, {"title": "Royal Assent", "link": "https://wikipedia.org/wiki/Royal_Assent"}, {"title": "House of Lords", "link": "https://wikipedia.org/wiki/House_of_Lords"}]}, {"year": "2000", "text": "Kaprun disaster: One hundred fifty-five skiers and snowboarders die when a cable car catches fire in an alpine tunnel in Kaprun, Austria.", "html": "2000 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_disaster\" title=\"Kaprun disaster\">Kaprun disaster</a>: One hundred fifty-five skiers and snowboarders die when a <a href=\"https://wikipedia.org/wiki/Funicular\" title=\"Funicular\">cable car</a> catches fire in an alpine tunnel in <a href=\"https://wikipedia.org/wiki/Kaprun\" title=\"Kaprun\">Kaprun</a>, Austria.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_disaster\" title=\"Kaprun disaster\">Kaprun disaster</a>: One hundred fifty-five skiers and snowboarders die when a <a href=\"https://wikipedia.org/wiki/Funicular\" title=\"Funicular\">cable car</a> catches fire in an alpine tunnel in <a href=\"https://wikipedia.org/wiki/Kaprun\" title=\"Ka<PERSON>run\">Kaprun</a>, Austria.", "links": [{"title": "Kaprun disaster", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_disaster"}, {"title": "Funicular", "link": "https://wikipedia.org/wiki/Funicular"}, {"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>"}]}, {"year": "2001", "text": "Journalists <PERSON>, <PERSON><PERSON> and <PERSON><PERSON> are killed in Afghanistan during an attack on the convoy they are traveling in.", "html": "2001 - Journalists <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a> and <a href=\"https://wikipedia.org/wiki/Volker_Handloik\" title=\"Volker Handloik\">Volker Handloik</a> are killed in <a href=\"https://wikipedia.org/wiki/Islamic_State_of_Afghanistan\" title=\"Islamic State of Afghanistan\">Afghanistan</a> during an attack on the convoy they are traveling in.", "no_year_html": "Journalists <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a> and <a href=\"https://wikipedia.org/wiki/Volker_Handloik\" title=\"Volker Handloik\">Volker Handloik</a> are killed in <a href=\"https://wikipedia.org/wiki/Islamic_State_of_Afghanistan\" title=\"Islamic State of Afghanistan\">Afghanistan</a> during an attack on the convoy they are traveling in.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}, {"title": "Volker Handloik", "link": "https://wikipedia.org/wiki/Volker_Handloik"}, {"title": "Islamic State of Afghanistan", "link": "https://wikipedia.org/wiki/Islamic_State_of_Afghanistan"}]}, {"year": "2002", "text": "A Fokker F27 Friendship operating as Laoag International Airlines Flight 585 crashes into Manila Bay shortly after takeoff from Ninoy Aquino International Airport, killing 19 people.", "html": "2002 - A <a href=\"https://wikipedia.org/wiki/Fokker_F27_Friendship\" title=\"Fokker F27 Friendship\">Fokker F27 Friendship</a> operating as <a href=\"https://wikipedia.org/wiki/Laoag_International_Airlines_Flight_585\" title=\"Laoag International Airlines Flight 585\">Laoag International Airlines Flight 585</a> crashes into <a href=\"https://wikipedia.org/wiki/Manila_Bay\" title=\"Manila Bay\">Manila Bay</a> shortly after takeoff from <a href=\"https://wikipedia.org/wiki/Ninoy_Aquino_International_Airport\" title=\"Ninoy Aquino International Airport\">Ninoy Aquino International Airport</a>, killing 19 people.", "no_year_html": "A <a href=\"https://wikipedia.org/wiki/Fokker_F27_Friendship\" title=\"Fokker F27 Friendship\">Fokker F27 Friendship</a> operating as <a href=\"https://wikipedia.org/wiki/Laoag_International_Airlines_Flight_585\" title=\"Laoag International Airlines Flight 585\">Laoag International Airlines Flight 585</a> crashes into <a href=\"https://wikipedia.org/wiki/Manila_Bay\" title=\"Manila Bay\">Manila Bay</a> shortly after takeoff from <a href=\"https://wikipedia.org/wiki/Ninoy_Aquino_International_Airport\" title=\"Ninoy Aquino International Airport\">Ninoy Aquino International Airport</a>, killing 19 people.", "links": [{"title": "Fokker F27 Friendship", "link": "https://wikipedia.org/wiki/Fokker_F27_Friendship"}, {"title": "Laoag International Airlines Flight 585", "link": "https://wikipedia.org/wiki/Laoag_International_Airlines_Flight_585"}, {"title": "Manila Bay", "link": "https://wikipedia.org/wiki/Manila_Bay"}, {"title": "Ninoy Aquino International Airport", "link": "https://wikipedia.org/wiki/Ninoy_Aquino_International_Airport"}]}, {"year": "2002", "text": "Russian mathematician <PERSON><PERSON><PERSON> posts the first of three preprint texts with his proof of the <PERSON><PERSON><PERSON><PERSON> conjecture. It remains the only of the Millennium Prize Problems in mathematics to be solved. He later refused both the prize money from Clay Mathematics Institute as well as the Fields Medal for his work.", "html": "2002 - Russian mathematician <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a> posts the first of three preprint texts with his proof of the <a href=\"https://wikipedia.org/wiki/Poincar%C3%A9_conjecture\" title=\"<PERSON><PERSON><PERSON><PERSON> conjecture\">Po<PERSON><PERSON><PERSON> conjecture</a>. It remains the only of the <a href=\"https://wikipedia.org/wiki/Millennium_Prize_Problems\" title=\"Millennium Prize Problems\">Millennium Prize Problems</a> in mathematics to be solved. He later refused both the prize money from <a href=\"https://wikipedia.org/wiki/Clay_Mathematics_Institute\" title=\"Clay Mathematics Institute\">Clay Mathematics Institute</a> as well as the <a href=\"https://wikipedia.org/wiki/Fields_Medal\" title=\"Fields Medal\">Fields Medal</a> for his work.", "no_year_html": "Russian mathematician <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a> posts the first of three preprint texts with his proof of the <a href=\"https://wikipedia.org/wiki/Poincar%C3%A9_conjecture\" title=\"<PERSON><PERSON><PERSON><PERSON> conjecture\"><PERSON><PERSON><PERSON><PERSON> conjecture</a>. It remains the only of the <a href=\"https://wikipedia.org/wiki/Millennium_Prize_Problems\" title=\"Millennium Prize Problems\">Millennium Prize Problems</a> in mathematics to be solved. He later refused both the prize money from <a href=\"https://wikipedia.org/wiki/Clay_Mathematics_Institute\" title=\"Clay Mathematics Institute\">Clay Mathematics Institute</a> as well as the <a href=\"https://wikipedia.org/wiki/Fields_Medal\" title=\"Fields Medal\">Fields Medal</a> for his work.", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}, {"title": "<PERSON><PERSON><PERSON><PERSON> conjecture", "link": "https://wikipedia.org/wiki/Poincar%C3%A9_conjecture"}, {"title": "Millennium Prize Problems", "link": "https://wikipedia.org/wiki/Millennium_Prize_Problems"}, {"title": "Clay Mathematics Institute", "link": "https://wikipedia.org/wiki/Clay_Mathematics_Institute"}, {"title": "Fields Medal", "link": "https://wikipedia.org/wiki/Fields_Medal"}]}, {"year": "2004", "text": "New Zealand Tomb of the Unknown Warrior is dedicated at the National War Memorial, Wellington.", "html": "2004 - <a href=\"https://wikipedia.org/wiki/Tomb_of_the_Unknown_Warrior_(New_Zealand)\" title=\"Tomb of the Unknown Warrior (New Zealand)\">New Zealand Tomb of the Unknown Warrior</a> is dedicated at the <a href=\"https://wikipedia.org/wiki/National_War_Memorial_(New_Zealand)\" title=\"National War Memorial (New Zealand)\">National War Memorial</a>, <a href=\"https://wikipedia.org/wiki/Wellington\" title=\"Wellington\">Wellington</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Tomb_of_the_Unknown_Warrior_(New_Zealand)\" title=\"Tomb of the Unknown Warrior (New Zealand)\">New Zealand Tomb of the Unknown Warrior</a> is dedicated at the <a href=\"https://wikipedia.org/wiki/National_War_Memorial_(New_Zealand)\" title=\"National War Memorial (New Zealand)\">National War Memorial</a>, <a href=\"https://wikipedia.org/wiki/Wellington\" title=\"Wellington\">Wellington</a>.", "links": [{"title": "Tomb of the Unknown Warrior (New Zealand)", "link": "https://wikipedia.org/wiki/Tomb_of_the_Unknown_Warrior_(New_Zealand)"}, {"title": "National War Memorial (New Zealand)", "link": "https://wikipedia.org/wiki/National_War_Memorial_(New_Zealand)"}, {"title": "Wellington", "link": "https://wikipedia.org/wiki/Wellington"}]}, {"year": "2004", "text": "The Palestine Liberation Organization confirms the death of <PERSON><PERSON> from unidentified causes. <PERSON><PERSON><PERSON> is elected chairman of the PLO minutes later.", "html": "2004 - The <a href=\"https://wikipedia.org/wiki/Palestine_Liberation_Organization\" title=\"Palestine Liberation Organization\">Palestine Liberation Organization</a> confirms <a href=\"https://wikipedia.org/wiki/Death_of_<PERSON><PERSON>_<PERSON>\" title=\"Death of <PERSON><PERSON>\">the death</a> of <a href=\"https://wikipedia.org/wiki/Ya<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a> from unidentified causes. <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a> is elected chairman of the PLO minutes later.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Palestine_Liberation_Organization\" title=\"Palestine Liberation Organization\">Palestine Liberation Organization</a> confirms <a href=\"https://wikipedia.org/wiki/Death_of_<PERSON><PERSON>_<PERSON>\" title=\"Death of <PERSON><PERSON>\">the death</a> of <a href=\"https://wikipedia.org/wiki/Ya<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a> from unidentified causes. <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a> is elected chairman of the PLO minutes later.", "links": [{"title": "Palestine Liberation Organization", "link": "https://wikipedia.org/wiki/Palestine_Liberation_Organization"}, {"title": "Death of <PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Death_of_<PERSON><PERSON>_<PERSON>"}, {"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}, {"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "2006", "text": "Her Majesty Queen <PERSON> unveils the New Zealand War Memorial in London, United Kingdom, commemorating the loss of soldiers from the New Zealand Army and the British Army.", "html": "2006 - Her Majesty <a href=\"https://wikipedia.org/wiki/Elizabeth_II\" title=\"Elizabeth II\">Queen <PERSON> II</a> unveils the <a href=\"https://wikipedia.org/wiki/New_Zealand_War_Memorial,_London\" title=\"New Zealand War Memorial, London\">New Zealand War Memorial</a> in London, United Kingdom, commemorating the loss of soldiers from the <a href=\"https://wikipedia.org/wiki/New_Zealand_Army\" title=\"New Zealand Army\">New Zealand Army</a> and the <a href=\"https://wikipedia.org/wiki/British_Army\" title=\"British Army\">British Army</a>.", "no_year_html": "Her Majesty <a href=\"https://wikipedia.org/wiki/<PERSON>_II\" title=\"Elizabeth II\">Queen <PERSON> II</a> unveils the <a href=\"https://wikipedia.org/wiki/New_Zealand_War_Memorial,_London\" title=\"New Zealand War Memorial, London\">New Zealand War Memorial</a> in London, United Kingdom, commemorating the loss of soldiers from the <a href=\"https://wikipedia.org/wiki/New_Zealand_Army\" title=\"New Zealand Army\">New Zealand Army</a> and the <a href=\"https://wikipedia.org/wiki/British_Army\" title=\"British Army\">British Army</a>.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_II"}, {"title": "New Zealand War Memorial, London", "link": "https://wikipedia.org/wiki/New_Zealand_War_Memorial,_London"}, {"title": "New Zealand Army", "link": "https://wikipedia.org/wiki/New_Zealand_Army"}, {"title": "British Army", "link": "https://wikipedia.org/wiki/British_Army"}]}, {"year": "2011", "text": "A helicopter crash just outside Mexico City kills seven, including <PERSON> the Secretary of the Interior of Mexico.", "html": "2011 - A <a href=\"https://wikipedia.org/wiki/2011_in_Mexico\" title=\"2011 in Mexico\">helicopter crash</a> just outside Mexico City kills seven, including <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> the <a href=\"https://wikipedia.org/wiki/Secretary_of_the_Interior_(Mexico)\" class=\"mw-redirect\" title=\"Secretary of the Interior (Mexico)\">Secretary of the Interior of Mexico</a>.", "no_year_html": "A <a href=\"https://wikipedia.org/wiki/2011_in_Mexico\" title=\"2011 in Mexico\">helicopter crash</a> just outside Mexico City kills seven, including <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> the <a href=\"https://wikipedia.org/wiki/Secretary_of_the_Interior_(Mexico)\" class=\"mw-redirect\" title=\"Secretary of the Interior (Mexico)\">Secretary of the Interior of Mexico</a>.", "links": [{"title": "2011 in Mexico", "link": "https://wikipedia.org/wiki/2011_in_Mexico"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "Secretary of the Interior (Mexico)", "link": "https://wikipedia.org/wiki/Secretary_of_the_Interior_(Mexico)"}]}, {"year": "2012", "text": "A strong earthquake with the magnitude 6.8 hits northern Burma, killing at least 26 people.", "html": "2012 - A <a href=\"https://wikipedia.org/wiki/2012_Shwebo_earthquake\" title=\"2012 Shwebo earthquake\">strong earthquake</a> with the <a href=\"https://wikipedia.org/wiki/Moment_magnitude_scale\" title=\"Moment magnitude scale\">magnitude</a> 6.8 hits northern <a href=\"https://wikipedia.org/wiki/Myanmar\" title=\"Myanmar\">Burma</a>, killing at least 26 people.", "no_year_html": "A <a href=\"https://wikipedia.org/wiki/2012_Shwebo_earthquake\" title=\"2012 Shwebo earthquake\">strong earthquake</a> with the <a href=\"https://wikipedia.org/wiki/Moment_magnitude_scale\" title=\"Moment magnitude scale\">magnitude</a> 6.8 hits northern <a href=\"https://wikipedia.org/wiki/Myanmar\" title=\"Myanmar\">Burma</a>, killing at least 26 people.", "links": [{"title": "2012 Shwebo earthquake", "link": "https://wikipedia.org/wiki/2012_Shwebo_earthquake"}, {"title": "Moment magnitude scale", "link": "https://wikipedia.org/wiki/Moment_magnitude_scale"}, {"title": "Myanmar", "link": "https://wikipedia.org/wiki/Myanmar"}]}, {"year": "2014", "text": "Fifty-eight people are killed in a bus crash in the Sukkur District in southern Pakistan's Sindh province.", "html": "2014 - Fifty-eight people are killed in a <a href=\"https://wikipedia.org/wiki/2014_Khairpur_bus_crash\" title=\"2014 Khairpur bus crash\">bus crash</a> in the <a href=\"https://wikipedia.org/wiki/Sukkur_District\" title=\"Sukkur District\">Sukkur District</a> in southern <a href=\"https://wikipedia.org/wiki/Pakistan\" title=\"Pakistan\">Pakistan</a>'s <a href=\"https://wikipedia.org/wiki/Sindh\" title=\"Sindh\">Sindh</a> province.", "no_year_html": "Fifty-eight people are killed in a <a href=\"https://wikipedia.org/wiki/2014_Khairpur_bus_crash\" title=\"2014 Khairpur bus crash\">bus crash</a> in the <a href=\"https://wikipedia.org/wiki/Sukkur_District\" title=\"Sukkur District\">Sukkur District</a> in southern <a href=\"https://wikipedia.org/wiki/Pakistan\" title=\"Pakistan\">Pakistan</a>'s <a href=\"https://wikipedia.org/wiki/Sindh\" title=\"Sindh\">Sindh</a> province.", "links": [{"title": "2014 K<PERSON><PERSON><PERSON> bus crash", "link": "https://wikipedia.org/wiki/2014_<PERSON><PERSON><PERSON><PERSON>_bus_crash"}, {"title": "Sukkur District", "link": "https://wikipedia.org/wiki/Sukkur_District"}, {"title": "Pakistan", "link": "https://wikipedia.org/wiki/Pakistan"}, {"title": "Sindh", "link": "https://wikipedia.org/wiki/Sindh"}]}, {"year": "2020", "text": "Typhoon <PERSON><PERSON><PERSON> makes landfall in Luzon and several offshore islands, killing 67 people. The storm causes the worst floods in the region since Typhoon <PERSON><PERSON><PERSON> in 2009.", "html": "2020 - <a href=\"https://wikipedia.org/wiki/Typhoon_Vamco_(2020)\" class=\"mw-redirect\" title=\"Typhoon Vamco (2020)\">Typhoon Vamco</a> makes landfall in <a href=\"https://wikipedia.org/wiki/Luzon\" title=\"Luzon\">Luzon</a> and several offshore islands, killing 67 people. The storm causes the worst floods in the region since <a href=\"https://wikipedia.org/wiki/Typhoon_Ketsana\" title=\"Typhoon Ketsana\">Typhoon Ketsana</a> in 2009.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Typhoon_Vamco_(2020)\" class=\"mw-redirect\" title=\"Typhoon Vamco (2020)\">Typhoon Vamco</a> makes landfall in <a href=\"https://wikipedia.org/wiki/Luzon\" title=\"Luzon\">Luzon</a> and several offshore islands, killing 67 people. The storm causes the worst floods in the region since <a href=\"https://wikipedia.org/wiki/Typhoon_Ketsana\" title=\"Typhoon Ketsana\">Typhoon Ketsana</a> in 2009.", "links": [{"title": "Typhoon Vamco (2020)", "link": "https://wikipedia.org/wiki/Typhoon_Vamco_(2020)"}, {"title": "Luzon", "link": "https://wikipedia.org/wiki/Luzon"}, {"title": "Typhoon Ketsana", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2022", "text": "Russo-Ukrainian War: Ukrainian armed forces enter the city of Kherson following a successful two-month southern counteroffensive.", "html": "2022 - <a href=\"https://wikipedia.org/wiki/Russo-Ukrainian_War\" title=\"Russo-Ukrainian War\">Russo-Ukrainian War</a>: Ukrainian <a href=\"https://wikipedia.org/wiki/Armed_Forces_of_Ukraine\" title=\"Armed Forces of Ukraine\">armed forces</a> <a href=\"https://wikipedia.org/wiki/Liberation_of_Kherson\" title=\"Liberation of Kherson\">enter the city</a> of <a href=\"https://wikipedia.org/wiki/Kherson\" title=\"Kherson\">Kherson</a> following a successful two-month <a href=\"https://wikipedia.org/wiki/2022_Kherson_counteroffensive\" title=\"2022 Kherson counteroffensive\">southern counteroffensive</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Russo-Ukrainian_War\" title=\"Russo-Ukrainian War\">Russo-Ukrainian War</a>: Ukrainian <a href=\"https://wikipedia.org/wiki/Armed_Forces_of_Ukraine\" title=\"Armed Forces of Ukraine\">armed forces</a> <a href=\"https://wikipedia.org/wiki/Liberation_of_Kherson\" title=\"Liberation of Kherson\">enter the city</a> of <a href=\"https://wikipedia.org/wiki/Kherson\" title=\"Kherson\">Kherson</a> following a successful two-month <a href=\"https://wikipedia.org/wiki/2022_Kherson_counteroffensive\" title=\"2022 Kherson counteroffensive\">southern counteroffensive</a>.", "links": [{"title": "Russo-Ukrainian War", "link": "https://wikipedia.org/wiki/Russo-Ukrainian_War"}, {"title": "Armed Forces of Ukraine", "link": "https://wikipedia.org/wiki/Armed_Forces_of_Ukraine"}, {"title": "Liberation of Kherson", "link": "https://wikipedia.org/wiki/Liberation_of_Kherson"}, {"title": "Kherson", "link": "https://wikipedia.org/wiki/Kherson"}, {"title": "2022 Kherson counteroffensive", "link": "https://wikipedia.org/wiki/2022_Kherson_counteroffensive"}]}], "Births": [{"year": "1050", "text": "<PERSON>, Holy Roman Emperor (d. 1106)", "html": "1050 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Holy_Roman_Emperor\" title=\"<PERSON> IV, Holy Roman Emperor\"><PERSON>, Holy Roman Emperor</a> (d. 1106)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Holy_Roman_Emperor\" title=\"<PERSON> IV, Holy Roman Emperor\"><PERSON>, Holy Roman Emperor</a> (d. 1106)", "links": [{"title": "<PERSON>, Holy Roman Emperor", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Holy_Roman_Emperor"}]}, {"year": "1154", "text": "<PERSON><PERSON> of Portugal (d. 1212)", "html": "1154 - <a href=\"https://wikipedia.org/wiki/Sancho_I_of_Portugal\" title=\"Sancho I of Portugal\">Sancho I of Portugal</a> (d. 1212)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Sancho_I_of_Portugal\" title=\"Sancho I of Portugal\">Sancho I of Portugal</a> (d. 1212)", "links": [{"title": "Sancho I of Portugal", "link": "https://wikipedia.org/wiki/Sancho_I_of_Portugal"}]}, {"year": "1155", "text": "<PERSON> of Castile (d. 1214)", "html": "1155 - <a href=\"https://wikipedia.org/wiki/<PERSON>_VIII_of_Castile\" title=\"<PERSON> VIII of Castile\"><PERSON> of Castile</a> (d. 1214)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Alfonso_VIII_of_Castile\" title=\"<PERSON> VIII of Castile\"><PERSON> VIII of Castile</a> (d. 1214)", "links": [{"title": "<PERSON> of Castile", "link": "https://wikipedia.org/wiki/Alfonso_VIII_of_Castile"}]}, {"year": "1220", "text": "<PERSON><PERSON><PERSON>, Count of Poitiers (d. 1271)", "html": "1220 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>,_Count_of_Poitiers\" title=\"<PERSON><PERSON><PERSON>, Count of Poitiers\"><PERSON><PERSON><PERSON>, Count of Poitiers</a> (d. 1271)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>,_Count_of_Poitiers\" title=\"<PERSON><PERSON><PERSON>, Count of Poitiers\"><PERSON><PERSON><PERSON>, Count of Poitiers</a> (d. 1271)", "links": [{"title": "<PERSON><PERSON><PERSON>, Count of Poitiers", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>,_Count_<PERSON>_<PERSON>"}]}, {"year": "1430", "text": "<PERSON><PERSON><PERSON> of Rožmberk, Bishop of Breslau (d. 1467)", "html": "1430 - <a href=\"https://wikipedia.org/wiki/Jo%C5%A1t_of_Ro%C5%BEmber<PERSON>\" title=\"<PERSON><PERSON><PERSON> of Rožmberk\"><PERSON><PERSON><PERSON> of Rožmberk</a>, Bishop of Breslau (d. 1467)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Jo%C5%A1t_of_Ro%C5%BEmberk\" title=\"<PERSON><PERSON><PERSON> of Rožmberk\"><PERSON><PERSON><PERSON> of Rožmberk</a>, Bishop of Breslau (d. 1467)", "links": [{"title": "<PERSON><PERSON><PERSON> of Rožmberk", "link": "https://wikipedia.org/wiki/Jo%C5%A1t_of_Ro%C5%BEmberk"}]}, {"year": "1441", "text": "<PERSON> of Savoy, French queen (d. 1483)", "html": "1441 - <a href=\"https://wikipedia.org/wiki/Charlotte_of_Savoy\" title=\"<PERSON> of Savoy\"><PERSON> of Savoy</a>, French queen (d. 1483)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Charlotte_of_Savoy\" title=\"<PERSON> of Savoy\"><PERSON> of Savoy</a>, French queen (d. 1483)", "links": [{"title": "<PERSON> of Savoy", "link": "https://wikipedia.org/wiki/<PERSON>_of_Savoy"}]}, {"year": "1449", "text": "<PERSON> of Poděbrady, Hungarian queen (d. 1464)", "html": "1449 - <a href=\"https://wikipedia.org/wiki/<PERSON>_of_Pod%C4%9Bbrady\" title=\"<PERSON> of Poděbrady\"><PERSON> of Poděbrady</a>, Hungarian queen (d. 1464)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_of_Pod%C4%9Bbrady\" title=\"<PERSON> of Poděbrady\"><PERSON> of Poděbrady</a>, Hungarian queen (d. 1464)", "links": [{"title": "<PERSON> of Poděbrady", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_Pod%C4%9Bbrady"}]}, {"year": "1491", "text": "<PERSON>, German Protestant reformer (d. 1551)", "html": "1491 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German Protestant reformer (d. 1551)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German Protestant reformer (d. 1551)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1493", "text": "<PERSON><PERSON><PERSON>, Swiss-German physician, botanist, astrologer, and occultist (d. 1541)", "html": "1493 - <a href=\"https://wikipedia.org/wiki/Paracelsus\" title=\"Paracelsus\"><PERSON><PERSON><PERSON></a>, Swiss-German physician, botanist, astrologer, and occultist (d. 1541)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Paracelsus\" title=\"Paracelsus\"><PERSON><PERSON><PERSON></a>, Swiss-German physician, botanist, astrologer, and occultist (d. 1541)", "links": [{"title": "Paracelsus", "link": "https://wikipedia.org/wiki/Paracelsus"}]}, {"year": "1512", "text": "<PERSON><PERSON>, Prince-Bishop of Warmia (d. 1589)", "html": "1512 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Prince-Bishop of Warmia (d. 1589)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Prince-Bishop of Warmia (d. 1589)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1569", "text": "<PERSON> the Younger, German physician and chemist (d. 1611)", "html": "1569 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_the_Younger\" title=\"<PERSON> the Younger\"><PERSON> the Younger</a>, German physician and chemist (d. 1611)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_the_Younger\" title=\"<PERSON> the Younger\"><PERSON> the Younger</a>, German physician and chemist (d. 1611)", "links": [{"title": "<PERSON> the Younger", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_the_<PERSON>"}]}, {"year": "1579", "text": "<PERSON><PERSON>, Flemish painter (d. 1657)", "html": "1579 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Flemish painter (d. 1657)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Flemish painter (d. 1657)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1599", "text": "<PERSON> of Brandenburg (d. 1655)", "html": "1599 - <a href=\"https://wikipedia.org/wiki/<PERSON>_Eleonora_of_Brandenburg\" title=\"<PERSON> of Brandenburg\"><PERSON> of Brandenburg</a> (d. 1655)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Brandenburg\" title=\"<PERSON> of Brandenburg\"><PERSON> of Brandenburg</a> (d. 1655)", "links": [{"title": "<PERSON> of Brandenburg", "link": "https://wikipedia.org/wiki/Maria_Eleonora_of_Brandenburg"}]}, {"year": "1599", "text": "<PERSON><PERSON><PERSON>, Austrian-Italian field marshal (d. 1656)", "html": "1599 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Austrian-Italian field marshal (d. 1656)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Austrian-Italian field marshal (d. 1656)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1633", "text": "<PERSON>, 1st Marquess of Halifax, English politician, Lord President of the Council (d. 1695)", "html": "1633 - <a href=\"https://wikipedia.org/wiki/<PERSON>,_1st_Marquess_of_Halifax\" title=\"<PERSON>, 1st Marquess of Halifax\"><PERSON>, 1st Marquess of Halifax</a>, English politician, <a href=\"https://wikipedia.org/wiki/Lord_President_of_the_Council\" title=\"Lord President of the Council\">Lord President of the Council</a> (d. 1695)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>,_1st_Marquess_of_Halifax\" title=\"<PERSON>, 1st Marquess of Halifax\"><PERSON>, 1st Marquess of Halifax</a>, English politician, <a href=\"https://wikipedia.org/wiki/Lord_President_of_the_Council\" title=\"Lord President of the Council\">Lord President of the Council</a> (d. 1695)", "links": [{"title": "<PERSON>, 1st Marquess of Halifax", "link": "https://wikipedia.org/wiki/<PERSON>,_1st_Marquess_of_Halifax"}, {"title": "Lord President of the Council", "link": "https://wikipedia.org/wiki/Lord_President_of_the_Council"}]}, {"year": "1668", "text": "<PERSON>, German author and scholar (d. 1736)", "html": "1668 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German author and scholar (d. 1736)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German author and scholar (d. 1736)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1696", "text": "<PERSON>, Italian violinist and composer (d. 1757)", "html": "1696 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian violinist and composer (d. 1757)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian violinist and composer (d. 1757)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1743", "text": "<PERSON>, Swedish botanist, entomologist, and psychologist (d. 1828)", "html": "1743 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swedish botanist, entomologist, and psychologist (d. 1828)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swedish botanist, entomologist, and psychologist (d. 1828)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1748", "text": "<PERSON> of Spain (d. 1819)", "html": "1748 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Spain\" title=\"Charles IV of Spain\"><PERSON> of Spain</a> (d. 1819)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Spain\" title=\"Charles IV of Spain\"><PERSON> of Spain</a> (d. 1819)", "links": [{"title": "<PERSON> of Spain", "link": "https://wikipedia.org/wiki/Charles_<PERSON>_of_Spain"}]}, {"year": "1768", "text": "<PERSON><PERSON><PERSON>, (d. 1829) 3rd Nizam of Hyderabad State", "html": "1768 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>ah\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON> Jah\"><PERSON><PERSON><PERSON></a>, (d. 1829) 3rd <a href=\"https://wikipedia.org/wiki/Nizam\" class=\"mw-redirect\" title=\"Nizam\"><PERSON>zam</a> of <a href=\"https://wikipedia.org/wiki/Hyderabad_State\" title=\"Hyderabad State\">Hyderabad State</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>ah\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, (d. 1829) 3rd <a href=\"https://wikipedia.org/wiki/Nizam\" class=\"mw-redirect\" title=\"Nizam\"><PERSON>zam</a> of <a href=\"https://wikipedia.org/wiki/Hyderabad_State\" title=\"Hyderabad State\">Hyderabad State</a>", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}, {"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Nizam"}, {"title": "Hyderabad State", "link": "https://wikipedia.org/wiki/Hyderabad_State"}]}, {"year": "1791", "text": "<PERSON>, Swiss lawyer and politician, 3rd President of the Swiss Confederation (d. 1855)", "html": "1791 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swiss lawyer and politician, 3rd <a href=\"https://wikipedia.org/wiki/List_of_Presidents_of_the_Swiss_Confederation\" class=\"mw-redirect\" title=\"List of Presidents of the Swiss Confederation\">President of the Swiss Confederation</a> (d. 1855)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swiss lawyer and politician, 3rd <a href=\"https://wikipedia.org/wiki/List_of_Presidents_of_the_Swiss_Confederation\" class=\"mw-redirect\" title=\"List of Presidents of the Swiss Confederation\">President of the Swiss Confederation</a> (d. 1855)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}, {"title": "List of Presidents of the Swiss Confederation", "link": "https://wikipedia.org/wiki/List_of_Presidents_of_the_Swiss_Confederation"}]}, {"year": "1821", "text": "<PERSON><PERSON><PERSON>, Russian novelist, short story writer, essayist, and philosopher (d. 1881)", "html": "1821 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Russian novelist, short story writer, essayist, and philosopher (d. 1881)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Russian novelist, short story writer, essayist, and philosopher (d. 1881)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1836", "text": "<PERSON>, American poet and author (d. 1907)", "html": "1836 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American poet and author (d. 1907)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American poet and author (d. 1907)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1852", "text": "<PERSON>, Austrian-Hungarian field marshal (d. 1925)", "html": "1852 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>%C3%B6tzendorf\" title=\"<PERSON>\"><PERSON></a>, Austrian-Hungarian field marshal (d. 1925)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>%C3%B6tzendorf\" title=\"<PERSON>\"><PERSON></a>, Austrian-Hungarian field marshal (d. 1925)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>%C3%B6tzendorf"}]}, {"year": "1855", "text": "<PERSON><PERSON><PERSON>, Serbian author and activist (d. 1906)", "html": "1855 - <a href=\"https://wikipedia.org/wiki/Steva<PERSON>_Sremac\" title=\"Steva<PERSON> Sremac\"><PERSON><PERSON><PERSON></a>, Serbian author and activist (d. 1906)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>eva<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON> Sr<PERSON>\"><PERSON><PERSON><PERSON></a>, Serbian author and activist (d. 1906)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Stevan_Sremac"}]}, {"year": "1857", "text": "<PERSON>, English nun and educator (d. 1914)", "html": "1857 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English nun and educator (d. 1914)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English nun and educator (d. 1914)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "1860", "text": "<PERSON>, Australian politician, 12th Premier of Queensland (d. 1898)", "html": "1860 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian politician, 12th <a href=\"https://wikipedia.org/wiki/Premier_of_Queensland\" title=\"Premier of Queensland\">Premier of Queensland</a> (d. 1898)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian politician, 12th <a href=\"https://wikipedia.org/wiki/Premier_of_Queensland\" title=\"Premier of Queensland\">Premier of Queensland</a> (d. 1898)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Premier of Queensland", "link": "https://wikipedia.org/wiki/Premier_of_Queensland"}]}, {"year": "1863", "text": "<PERSON>, French painter and educator (d. 1935)", "html": "1863 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French painter and educator (d. 1935)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French painter and educator (d. 1935)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1864", "text": "<PERSON>, Austrian journalist and activist, Nobel Prize laureate (d. 1921)", "html": "1864 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Austrian journalist and activist, <a href=\"https://wikipedia.org/wiki/Nobel_Peace_Prize\" title=\"Nobel Peace Prize\">Nobel Prize</a> laureate (d. 1921)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Austrian journalist and activist, <a href=\"https://wikipedia.org/wiki/Nobel_Peace_Prize\" title=\"Nobel Peace Prize\">Nobel Prize</a> laureate (d. 1921)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Nobel Peace Prize", "link": "https://wikipedia.org/wiki/Nobel_Peace_Prize"}]}, {"year": "1866", "text": "<PERSON>, English chemist and mathematician (d. 1956)", "html": "1866 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English chemist and mathematician (d. 1956)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English chemist and mathematician (d. 1956)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1867", "text": "<PERSON><PERSON>, a Jain philosopher, spiritual mentor of <PERSON><PERSON><PERSON> (d. 1901)", "html": "1867 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, a <a href=\"https://wikipedia.org/wiki/Jainism\" title=\"Jainism\">Jain</a> philosopher, spiritual mentor of <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a> (d. <a href=\"https://wikipedia.org/wiki/1901\" title=\"1901\">1901</a>)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, a <a href=\"https://wikipedia.org/wiki/Jainism\" title=\"Jainism\">Jain</a> philosopher, spiritual mentor of <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a> (d. <a href=\"https://wikipedia.org/wiki/1901\" title=\"1901\">1901</a>)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}, {"title": "Jainism", "link": "https://wikipedia.org/wiki/Jainism"}, {"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}, {"title": "1901", "link": "https://wikipedia.org/wiki/1901"}]}, {"year": "1868", "text": "<PERSON><PERSON><PERSON>, French painter and academic (d. 1940)", "html": "1868 - <a href=\"https://wikipedia.org/wiki/%C3%89<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, French painter and academic (d. 1940)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/%C3%89<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, French painter and academic (d. 1940)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/%C3%89<PERSON><PERSON>_<PERSON>"}]}, {"year": "1869", "text": "<PERSON> of Italy (d. 1947)", "html": "1869 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_of_Italy\" class=\"mw-redirect\" title=\"<PERSON> of Italy\"><PERSON> of Italy</a> (d. 1947)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_of_Italy\" class=\"mw-redirect\" title=\"<PERSON> of Italy\"><PERSON> of Italy</a> (d. 1947)", "links": [{"title": "<PERSON> of Italy", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_of_Italy"}]}, {"year": "1869", "text": "<PERSON><PERSON><PERSON>, Italian anarchist assassin (d. 1901)", "html": "1869 - <a href=\"https://wikipedia.org/wiki/<PERSON>ae<PERSON>_<PERSON>\" title=\"<PERSON>ae<PERSON>\"><PERSON><PERSON><PERSON></a>, Italian anarchist assassin (d. 1901)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>ae<PERSON>_<PERSON>\" title=\"Gae<PERSON>\"><PERSON><PERSON><PERSON></a>, Italian anarchist assassin (d. 1901)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Gae<PERSON>_Bres<PERSON>"}]}, {"year": "1872", "text": "<PERSON><PERSON>, American actress (d. 1953)", "html": "1872 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American actress (d. 1953)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American actress (d. 1953)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1872", "text": "<PERSON>, American lawyer and politician, 46th Governor of Massachusetts (d. 1947)", "html": "1872 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and politician, 46th <a href=\"https://wikipedia.org/wiki/Governor_of_Massachusetts\" title=\"Governor of Massachusetts\">Governor of Massachusetts</a> (d. 1947)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and politician, 46th <a href=\"https://wikipedia.org/wiki/Governor_of_Massachusetts\" title=\"Governor of Massachusetts\">Governor of Massachusetts</a> (d. 1947)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "Governor of Massachusetts", "link": "https://wikipedia.org/wiki/Governor_of_Massachusetts"}]}, {"year": "1882", "text": "<PERSON><PERSON><PERSON> of Sweden (d. 1973)", "html": "1882 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_VI_Adolf_of_Sweden\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON> VI Adolf of Sweden\"><PERSON><PERSON><PERSON> VI <PERSON> of Sweden</a> (d. 1973)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_VI_Adolf_of_Sweden\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON> VI Adolf of Sweden\"><PERSON><PERSON><PERSON> VI <PERSON> of Sweden</a> (d. 1973)", "links": [{"title": "Gus<PERSON><PERSON> VI Adolf of Sweden", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_VI_<PERSON>_of_Sweden"}]}, {"year": "1883", "text": "<PERSON>, Swiss conductor and academic (d. 1969)", "html": "1883 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swiss conductor and academic (d. 1969)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swiss conductor and academic (d. 1969)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1885", "text": "<PERSON>, American general (d. 1945)", "html": "1885 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American general (d. 1945)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American general (d. 1945)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "1887", "text": "<PERSON>, English-American actor (d. 1953)", "html": "1887 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English-American actor (d. 1953)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English-American actor (d. 1953)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1888", "text": "<PERSON><PERSON>, Indian activist, scholar, and politician, Indian Minister of Education (d. 1958)", "html": "1888 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON><PERSON> Azad\"><PERSON><PERSON></a>, Indian activist, scholar, and politician, <a href=\"https://wikipedia.org/wiki/Ministry_of_Human_Resource_Development_(India)\" class=\"mw-redirect\" title=\"Ministry of Human Resource Development (India)\">Indian Minister of Education</a> (d. 1958)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON><PERSON> Azad\"><PERSON><PERSON></a>, Indian activist, scholar, and politician, <a href=\"https://wikipedia.org/wiki/Ministry_of_Human_Resource_Development_(India)\" class=\"mw-redirect\" title=\"Ministry of Human Resource Development (India)\">Indian Minister of Education</a> (d. 1958)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>"}, {"title": "Ministry of Human Resource Development (India)", "link": "https://wikipedia.org/wiki/Ministry_of_Human_Resource_Development_(India)"}]}, {"year": "1888", "text": "<PERSON><PERSON> <PERSON><PERSON>, Indian lawyer and politician (d. 1982)", "html": "1888 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> <PERSON><PERSON>\"><PERSON><PERSON> <PERSON><PERSON></a>, Indian lawyer and politician (d. 1982)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> <PERSON><PERSON>\"><PERSON><PERSON> <PERSON><PERSON></a>, Indian lawyer and politician (d. 1982)", "links": [{"title": "<PERSON><PERSON> <PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>i"}]}, {"year": "1891", "text": "<PERSON>, American baseball player and manager (d. 1954)", "html": "1891 - <a href=\"https://wikipedia.org/wiki/Rabbit_Maranville\" title=\"Rabbit Maranville\"><PERSON></a>, American baseball player and manager (d. 1954)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Rabbit_Maranville\" title=\"Rabbit Maranville\"><PERSON></a>, American baseball player and manager (d. 1954)", "links": [{"title": "Rabbit <PERSON>", "link": "https://wikipedia.org/wiki/Rabbit_Maranville"}]}, {"year": "1891", "text": "<PERSON><PERSON><PERSON>, Ukrainian-Russian psychiatrist and university lecturer (d. 1981)", "html": "1891 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Ukrainian-Russian psychiatrist and university lecturer (d. 1981)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Ukrainian-Russian psychiatrist and university lecturer (d. 1981)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1894", "text": "<PERSON>, American actress (d. 1982)", "html": "1894 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress (d. 1982)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress (d. 1982)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Beverly_Bayne"}]}, {"year": "1895", "text": "<PERSON><PERSON><PERSON>, American mathematician and academic (d. 1990)", "html": "1895 - <a href=\"https://wikipedia.org/wiki/Wealthy_<PERSON><PERSON><PERSON>\" title=\"Wealthy <PERSON><PERSON>cock\"><PERSON><PERSON><PERSON> <PERSON></a>, American mathematician and academic (d. 1990)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Wealthy_<PERSON><PERSON><PERSON>\" title=\"Wealthy Ba<PERSON>cock\"><PERSON><PERSON><PERSON> <PERSON></a>, American mathematician and academic (d. 1990)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Weal<PERSON>_<PERSON><PERSON><PERSON>"}]}, {"year": "1896", "text": "<PERSON>, American author, playwright, composer, and activist (d. 1977)", "html": "1896 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author, playwright, composer, and activist (d. 1977)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author, playwright, composer, and activist (d. 1977)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1896", "text": "<PERSON>, Mexican-American historian (d. 1958)", "html": "1896 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>%C3%B1eda\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, Mexican-American historian (d. 1958)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>%C3%B1eda\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, Mexican-American historian (d. 1958)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>%C3%B1eda"}]}, {"year": "1898", "text": "<PERSON>, French actor, director, producer, and screenwriter (d. 1981)", "html": "1898 - <a href=\"https://wikipedia.org/wiki/Ren%C3%A<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French actor, director, producer, and screenwriter (d. 1981)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Ren%C3%A<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French actor, director, producer, and screenwriter (d. 1981)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Ren%C3%A9_Clair"}]}, {"year": "1899", "text": "<PERSON>, American actor (d. 1983)", "html": "1899 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%27B<PERSON>_(actor)\" title=\"<PERSON> (actor)\"><PERSON></a>, American actor (d. 1983)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%27B<PERSON>_(actor)\" title=\"<PERSON> (actor)\"><PERSON></a>, American actor (d. 1983)", "links": [{"title": "<PERSON> (actor)", "link": "https://wikipedia.org/wiki/Pat_<PERSON>%27B<PERSON>_(actor)"}]}, {"year": "1900", "text": "<PERSON>, Russian stage and film actress (d. 1983)", "html": "1900 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian stage and film actress (d. 1983)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian stage and film actress (d. 1983)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1901", "text": "<PERSON>, American film producer (d. 1985)", "html": "1901 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American film producer (d. 1985)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American film producer (d. 1985)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1901", "text": "<PERSON><PERSON>, American historian and author (d. 1978)", "html": "1901 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American historian and author (d. 1978)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American historian and author (d. 1978)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1904", "text": "<PERSON><PERSON>, American lawyer and convicted spy (d. 1996)", "html": "1904 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> Hiss\"><PERSON><PERSON></a>, American lawyer and convicted spy (d. 1996)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> Hiss\"><PERSON><PERSON></a>, American lawyer and convicted spy (d. 1996)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_Hiss"}]}, {"year": "1904", "text": "<PERSON><PERSON> <PERSON><PERSON> <PERSON><PERSON>, British mathematician and academic (d. 1960)", "html": "1904 - <a href=\"https://wikipedia.org/wiki/J<PERSON>_<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> <PERSON><PERSON> <PERSON><PERSON>\"><PERSON><PERSON> <PERSON><PERSON> <PERSON><PERSON></a>, British mathematician and academic (d. 1960)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> <PERSON><PERSON> <PERSON><PERSON>\"><PERSON><PERSON> <PERSON><PERSON> <PERSON><PERSON></a>, British mathematician and academic (d. 1960)", "links": [{"title": "<PERSON><PERSON> <PERSON><PERSON> <PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "1906", "text": "<PERSON>, German-American monologuist and comedian (d. 2001)", "html": "1906 - <a href=\"https://wikipedia.org/wiki/Brother_<PERSON>\" title=\"Brother <PERSON>\">Brother <PERSON></a>, German-American monologuist and comedian (d. 2001)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Brother_<PERSON>\" title=\"Brother <PERSON>\">Brother <PERSON></a>, German-American monologuist and comedian (d. 2001)", "links": [{"title": "Brother <PERSON>", "link": "https://wikipedia.org/wiki/Brother_<PERSON>"}]}, {"year": "1907", "text": "<PERSON><PERSON><PERSON>, Greek director, screenwriter, and poet (d. 1992)", "html": "1907 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Greek director, screenwriter, and poet (d. 1992)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Greek director, screenwriter, and poet (d. 1992)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1909", "text": "<PERSON>, American actor (d. 1973)", "html": "1909 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor (d. 1973)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor (d. 1973)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1909", "text": "<PERSON><PERSON>, Italian race car driver (d. 1976)", "html": "1909 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Italian race car driver (d. 1976)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Italian race car driver (d. 1976)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>i"}]}, {"year": "1911", "text": "<PERSON>, Chilean-Italian painter and sculptor (d. 2002)", "html": "1911 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Chilean-Italian painter and sculptor (d. 2002)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Chilean-Italian painter and sculptor (d. 2002)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1912", "text": "<PERSON>, American lawyer, politician, and diplomat, United States Ambassador to El Salvador (d. 1999)", "html": "1912 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer, politician, and diplomat, <a href=\"https://wikipedia.org/wiki/United_States_Ambassador_to_El_Salvador\" class=\"mw-redirect\" title=\"United States Ambassador to El Salvador\">United States Ambassador to El Salvador</a> (d. 1999)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer, politician, and diplomat, <a href=\"https://wikipedia.org/wiki/United_States_Ambassador_to_El_Salvador\" class=\"mw-redirect\" title=\"United States Ambassador to El Salvador\">United States Ambassador to El Salvador</a> (d. 1999)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "United States Ambassador to El Salvador", "link": "https://wikipedia.org/wiki/United_States_Ambassador_to_El_Salvador"}]}, {"year": "1914", "text": "<PERSON>, American astronomer, optician, and academic (d. 2005)", "html": "1914 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American astronomer, optician, and academic (d. 2005)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American astronomer, optician, and academic (d. 2005)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1914", "text": "<PERSON>, American activist (d. 1999)", "html": "1914 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(activist)\" title=\"<PERSON> (activist)\"><PERSON></a>, American activist (d. 1999)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(activist)\" title=\"<PERSON> (activist)\"><PERSON></a>, American activist (d. 1999)", "links": [{"title": "<PERSON> (activist)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(activist)"}]}, {"year": "1914", "text": "<PERSON><PERSON><PERSON>, Nigerian academic and jurist, 2nd Chief Justice of Nigeria (d. 1991)", "html": "1914 - <a href=\"https://wikipedia.org/wiki/Tas<PERSON>_<PERSON>_<PERSON>\" title=\"Taslim <PERSON>\"><PERSON><PERSON><PERSON></a>, Nigerian academic and jurist, 2nd <a href=\"https://wikipedia.org/wiki/Chief_Justice_of_Nigeria\" title=\"Chief Justice of Nigeria\">Chief Justice of Nigeria</a> (d. 1991)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>s<PERSON>_<PERSON>_<PERSON>\" title=\"Taslim <PERSON>\"><PERSON><PERSON><PERSON></a>, Nigerian academic and jurist, 2nd <a href=\"https://wikipedia.org/wiki/Chief_Justice_of_Nigeria\" title=\"Chief Justice of Nigeria\">Chief Justice of Nigeria</a> (d. 1991)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Tas<PERSON>_<PERSON><PERSON>_Elias"}, {"title": "Chief Justice of Nigeria", "link": "https://wikipedia.org/wiki/Chief_Justice_of_Nigeria"}]}, {"year": "1914", "text": "<PERSON>, American novelist and screenwriter (d. 2003)", "html": "1914 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Howard Fast\"><PERSON></a>, American novelist and screenwriter (d. 2003)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_Fast\" title=\"Howard Fast\"><PERSON></a>, American novelist and screenwriter (d. 2003)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1914", "text": "<PERSON>, American soldier and lawyer (d. 2001)", "html": "1914 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American soldier and lawyer (d. 2001)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American soldier and lawyer (d. 2001)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1915", "text": "<PERSON>, American soldier, journalist, and politician (d. 2005)", "html": "1915 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American soldier, journalist, and politician (d. 2005)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American soldier, journalist, and politician (d. 2005)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1915", "text": "<PERSON>, American economist and author (d. 2012)", "html": "1915 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American economist and author (d. 2012)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American economist and author (d. 2012)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1916", "text": "<PERSON>, English engineer and politician, Lord President of the Council (d. 2012)", "html": "1916 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English engineer and politician, <a href=\"https://wikipedia.org/wiki/Lord_President_of_the_Council\" title=\"Lord President of the Council\">Lord President of the Council</a> (d. 2012)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English engineer and politician, <a href=\"https://wikipedia.org/wiki/Lord_President_of_the_Council\" title=\"Lord President of the Council\">Lord President of the Council</a> (d. 2012)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}, {"title": "Lord President of the Council", "link": "https://wikipedia.org/wiki/Lord_President_of_the_Council"}]}, {"year": "1918", "text": "<PERSON><PERSON><PERSON>, American entertainer (d. 1997)", "html": "1918 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON> <PERSON>\"><PERSON><PERSON><PERSON></a>, American entertainer (d. 1997)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON> <PERSON>\"><PERSON><PERSON><PERSON></a>, American entertainer (d. 1997)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1919", "text": "<PERSON><PERSON>, Finnish soldier and author (d. 2000)", "html": "1919 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>%C3%A4%C3%A4talo\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Finnish soldier and author (d. 2000)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>%C3%A4%C3%A4talo\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Finnish soldier and author (d. 2000)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Kalle_P%C3%A4%C3%A4talo"}]}, {"year": "1920", "text": "<PERSON>, British politician, President of the European Commission (d. 2003)", "html": "1920 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, British politician, <a href=\"https://wikipedia.org/wiki/President_of_the_European_Commission\" title=\"President of the European Commission\">President of the European Commission</a> (d. 2003)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, British politician, <a href=\"https://wikipedia.org/wiki/President_of_the_European_Commission\" title=\"President of the European Commission\">President of the European Commission</a> (d. 2003)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "President of the European Commission", "link": "https://wikipedia.org/wiki/President_of_the_European_Commission"}]}, {"year": "1920", "text": "<PERSON>, German captain and pilot (d. 2000)", "html": "1920 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German captain and pilot (d. 2000)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German captain and pilot (d. 2000)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1921", "text": "<PERSON><PERSON>, American sergeant, academic, and politician, 2nd United States Secretary of Education (d. 1996)", "html": "1921 - <a href=\"https://wikipedia.org/wiki/Te<PERSON>_<PERSON>\" title=\"Terrel Bell\"><PERSON><PERSON></a>, American sergeant, academic, and politician, 2nd <a href=\"https://wikipedia.org/wiki/United_States_Secretary_of_Education\" title=\"United States Secretary of Education\">United States Secretary of Education</a> (d. 1996)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"Terrel Bell\"><PERSON><PERSON></a>, American sergeant, academic, and politician, 2nd <a href=\"https://wikipedia.org/wiki/United_States_Secretary_of_Education\" title=\"United States Secretary of Education\">United States Secretary of Education</a> (d. 1996)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Te<PERSON>_<PERSON>"}, {"title": "United States Secretary of Education", "link": "https://wikipedia.org/wiki/United_States_Secretary_of_Education"}]}, {"year": "1922", "text": "<PERSON>, American novelist, short story writer, and essayist (d. 2007)", "html": "1922 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American novelist, short story writer, and essayist (d. 2007)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American novelist, short story writer, and essayist (d. 2007)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1925", "text": "<PERSON>, English-American director, producer, and screenwriter (d. 2015)", "html": "1925 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English-American director, producer, and screenwriter (d. 2015)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English-American director, producer, and screenwriter (d. 2015)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1925", "text": "<PERSON>, English actress (d. 2018)", "html": "1925 - <a href=\"https://wikipedia.org/wiki/June_Whit<PERSON>\" title=\"June Whitfield\">June W<PERSON><PERSON></a>, English actress (d. 2018)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/June_Whit<PERSON>\" title=\"June Whitfield\">June W<PERSON><PERSON></a>, English actress (d. 2018)", "links": [{"title": "June <PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/June_Whitfield"}]}, {"year": "1925", "text": "<PERSON>, American actor and screenwriter (d. 2013)", "html": "1925 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor and screenwriter (d. 2013)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor and screenwriter (d. 2013)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1926", "text": "<PERSON>, Italian race car driver (d. 2016)", "html": "1926 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian race car driver (d. 2016)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian race car driver (d. 2016)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1926", "text": "<PERSON>, Canadian ice hockey player (d. 1998)", "html": "1926 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(ice_hockey)\" title=\"<PERSON> (ice hockey)\"><PERSON></a>, Canadian ice hockey player (d. 1998)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(ice_hockey)\" title=\"<PERSON> (ice hockey)\"><PERSON></a>, Canadian ice hockey player (d. 1998)", "links": [{"title": "<PERSON> (ice hockey)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(ice_hockey)"}]}, {"year": "1927", "text": "<PERSON><PERSON>, American singer-songwriter and pianist (d. 2016)", "html": "1927 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American singer-songwriter and pianist (d. 2016)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American singer-songwriter and pianist (d. 2016)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1927", "text": "<PERSON>, Croatian general and politician, 2nd Croatian Minister of Defence (d. 2014)", "html": "1927 - <a href=\"https://wikipedia.org/wiki/<PERSON>_%C5%A0pegelj\" title=\"<PERSON>\"><PERSON></a>, Croatian general and politician, 2nd <a href=\"https://wikipedia.org/wiki/Ministry_of_Defence_(Croatia)\" title=\"Ministry of Defence (Croatia)\">Croatian Minister of Defence</a> (d. 2014)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_%C5%A0pegelj\" title=\"<PERSON>\"><PERSON></a>, Croatian general and politician, 2nd <a href=\"https://wikipedia.org/wiki/Ministry_of_Defence_(Croatia)\" title=\"Ministry of Defence (Croatia)\">Croatian Minister of Defence</a> (d. 2014)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_%C5%A0pegelj"}, {"title": "Ministry of Defence (Croatia)", "link": "https://wikipedia.org/wiki/Ministry_of_Defence_(Croatia)"}]}, {"year": "1928", "text": "<PERSON><PERSON>, American singer (d. 2016)", "html": "1928 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American singer (d. 2016)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American singer (d. 2016)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1928", "text": "<PERSON>, Mexican novelist and essayist (d. 2012)", "html": "1928 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Mexican novelist and essayist (d. 2012)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Mexican novelist and essayist (d. 2012)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1929", "text": "<PERSON><PERSON><PERSON>, American singer (d. 1997)", "html": "1929 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American singer (d. 1997)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American singer (d. 1997)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1929", "text": "<PERSON>, German author and poet (d. 2022)", "html": "1929 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German author and poet (d. 2022)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German author and poet (d. 2022)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1929", "text": "<PERSON>, English lawyer, businessman, and academic (d. 2024)", "html": "1929 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English lawyer, businessman, and academic (d. 2024)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English lawyer, businessman, and academic (d. 2024)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1930", "text": "<PERSON><PERSON>, American physicist and academic (d. 2017)", "html": "1930 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American physicist and academic (d. 2017)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American physicist and academic (d. 2017)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1930", "text": "<PERSON>, American physicist and mathematician (d. 1982)", "html": "1930 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American physicist and mathematician (d. 1982)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American physicist and mathematician (d. 1982)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1930", "text": "<PERSON>, English conductor (d. 2008)", "html": "1930 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English conductor (d. 2008)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English conductor (d. 2008)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1932", "text": "<PERSON><PERSON>, Italian journalist (d. 2012)", "html": "1932 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Italian journalist (d. 2012)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Italian journalist (d. 2012)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1933", "text": "<PERSON><PERSON>, Italian race car driver (d. 2014)", "html": "1933 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Italian race car driver (d. 2014)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Italian race car driver (d. 2014)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1933", "text": "<PERSON>, American businessman and philanthropist (d. 2013)", "html": "1933 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American businessman and philanthropist (d. 2013)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American businessman and philanthropist (d. 2013)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "1935", "text": "<PERSON><PERSON>, Swedish actress  (d. 2019)", "html": "1935 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Swedish actress (d. 2019)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Swedish actress (d. 2019)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1936", "text": "<PERSON>, American songwriter and producer (d. 2005)", "html": "1936 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(songwriter)\" title=\"<PERSON> (songwriter)\"><PERSON></a>, American songwriter and producer (d. 2005)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(songwriter)\" title=\"<PERSON> (songwriter)\"><PERSON></a>, American songwriter and producer (d. 2005)", "links": [{"title": "<PERSON> (songwriter)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>(songwriter)"}]}, {"year": "1937", "text": "<PERSON><PERSON><PERSON>, Italian race car driver (d. 2001)", "html": "1937 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Italian race car driver (d. 2001)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Italian race car driver (d. 2001)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Vitt<PERSON>_<PERSON>"}]}, {"year": "1937", "text": "<PERSON>, American basketball player (d. 2004)", "html": "1937 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player (d. 2004)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player (d. 2004)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1937", "text": "<PERSON>, Canadian politician and diplomat, 14th Canadian Ambassador to the United Nations", "html": "1937 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian politician and diplomat, 14th <a href=\"https://wikipedia.org/wiki/Canadian_Ambassador_to_the_United_Nations\" class=\"mw-redirect\" title=\"Canadian Ambassador to the United Nations\">Canadian Ambassador to the United Nations</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian politician and diplomat, 14th <a href=\"https://wikipedia.org/wiki/Canadian_Ambassador_to_the_United_Nations\" class=\"mw-redirect\" title=\"Canadian Ambassador to the United Nations\">Canadian Ambassador to the United Nations</a>", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Canadian Ambassador to the United Nations", "link": "https://wikipedia.org/wiki/Canadian_Ambassador_to_the_United_Nations"}]}, {"year": "1937", "text": "<PERSON>, American poet and scholar", "html": "1937 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American poet and scholar", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American poet and scholar", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1939", "text": "<PERSON>, American actress", "html": "1939 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1940", "text": "<PERSON>, American journalist and politician", "html": "1940 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American journalist and politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American journalist and politician", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_Boxer"}]}, {"year": "1940", "text": "<PERSON>, American guitarist", "html": "1940 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American guitarist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American guitarist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1942", "text": "<PERSON>, English journalist and businessman", "html": "1942 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English journalist and businessman", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English journalist and businessman", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1942", "text": "<PERSON>, Guyanese-American cricketer and politician (d. 2000)", "html": "1942 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Guyanese-American cricketer and politician (d. 2000)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Guyanese-American cricketer and politician (d. 2000)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1942", "text": "<PERSON><PERSON>, Korean American journalist and author (d. 2019)", "html": "1942 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Korean American journalist and author (d. 2019)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Korean American journalist and author (d. 2019)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1942", "text": "<PERSON>, American author and radio host (d. 2013)", "html": "1942 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author and radio host (d. 2013)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author and radio host (d. 2013)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1943", "text": "<PERSON>, Australian swim coach", "html": "1943 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(swimming_coach)\" title=\"<PERSON> (swimming coach)\"><PERSON></a>, Australian swim coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(swimming_coach)\" title=\"<PERSON> (swimming coach)\"><PERSON></a>, Australian swim coach", "links": [{"title": "<PERSON> (swimming coach)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(swimming_coach)"}]}, {"year": "1945", "text": "<PERSON>, English guitarist and songwriter", "html": "1945 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English guitarist and songwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English guitarist and songwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1945", "text": "<PERSON>, American singer and guitarist", "html": "1945 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer and guitarist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer and guitarist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1945", "text": "<PERSON>, Nicaraguan politician, President of Nicaragua", "html": "1945 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Nicaraguan politician, <a href=\"https://wikipedia.org/wiki/President_of_Nicaragua\" class=\"mw-redirect\" title=\"President of Nicaragua\">President of Nicaragua</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Nicaraguan politician, <a href=\"https://wikipedia.org/wiki/President_of_Nicaragua\" class=\"mw-redirect\" title=\"President of Nicaragua\">President of Nicaragua</a>", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "President of Nicaragua", "link": "https://wikipedia.org/wiki/President_of_Nicaragua"}]}, {"year": "1946", "text": "<PERSON>, American race car driver (d. 1988)", "html": "1946 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>\" title=\"<PERSON>\"><PERSON></a>, American race car driver (d. 1988)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>\" title=\"<PERSON>\"><PERSON></a>, American race car driver (d. 1988)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1948", "text": "<PERSON><PERSON><PERSON>, Polish mountaineer (d. 1986)", "html": "1948 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Polish mountaineer (d. 1986)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Polish mountaineer (d. 1986)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1948", "text": "<PERSON> \"<PERSON><PERSON><PERSON>, British-South African record producer and songwriter", "html": "1948 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_%22Mutt%22_<PERSON>\" class=\"mw-redirect\" title='<PERSON> \"<PERSON>\" <PERSON>'><PERSON> \"<PERSON>\" <PERSON></a>, British-South African record producer and songwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_%22Mutt%22_<PERSON>\" class=\"mw-redirect\" title='<PERSON> \"<PERSON>\" <PERSON>'><PERSON> \"<PERSON>\" <PERSON></a>, British-South African record producer and songwriter", "links": [{"title": "<PERSON> \"<PERSON><PERSON>\" <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_%22Mutt%22_<PERSON>"}]}, {"year": "1948", "text": "<PERSON>, American actor (d. 2005)", "html": "1948 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor (d. 2005)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor (d. 2005)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1949", "text": "<PERSON> of Kelantan (d. 2019)", "html": "1949 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Kelantan\" title=\"<PERSON> of Kelantan\"><PERSON> of Kelantan</a> (d. 2019)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Kelantan\" title=\"<PERSON> of Kelantan\"><PERSON> of Kelantan</a> (d. 2019)", "links": [{"title": "<PERSON> of Kelantan", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_Kelantan"}]}, {"year": "1949", "text": "<PERSON>, American golfer", "html": "1949 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American golfer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American golfer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1950", "text": "<PERSON><PERSON>, Romanian journalist and poet", "html": "1950 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Romanian journalist and poet", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Romanian journalist and poet", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1950", "text": "<PERSON>, American singer-songwriter and guitarist", "html": "1950 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and guitarist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and guitarist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1951", "text": "<PERSON>, American megasavant (d. 2009)", "html": "1951 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American <a href=\"https://wikipedia.org/wiki/<PERSON>vant_syndrome\" title=\"Savant syndrome\">megasavant</a> (d. 2009)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American <a href=\"https://wikipedia.org/wiki/Savant_syndrome\" title=\"<PERSON>vant syndrome\">megasavant</a> (d. 2009)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Savant syndrome", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_syndrome"}]}, {"year": "1951", "text": "<PERSON>, American television host and producer", "html": "1951 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American television host and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American television host and producer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1951", "text": "<PERSON><PERSON>, American golfer", "html": "1951 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"Fuzzy Zoeller\"><PERSON><PERSON></a>, American golfer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"Fu<PERSON> Zoeller\"><PERSON><PERSON></a>, American golfer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1953", "text": "<PERSON>, American singer-songwriter and guitarist", "html": "1953 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and guitarist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and guitarist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1953", "text": "<PERSON>, English singer-songwriter, guitarist, and record producer", "html": "1953 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English singer-songwriter, guitarist, and record producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English singer-songwriter, guitarist, and record producer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1954", "text": "<PERSON>, English rugby player", "html": "1954 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English rugby player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English rugby player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1954", "text": "<PERSON>, American novelist, essayist, and short story writer.", "html": "1954 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American novelist, essayist, and short story writer.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American novelist, essayist, and short story writer.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1954", "text": "<PERSON>, English footballer", "html": "1954 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1954", "text": "<PERSON>, American author, illustrator, screenwriter, and producer (d. 2015)", "html": "1954 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author, illustrator, screenwriter, and producer (d. 2015)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author, illustrator, screenwriter, and producer (d. 2015)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1955", "text": "<PERSON>, American singer-songwriter and guitarist", "html": "1955 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and guitarist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and guitarist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1955", "text": "<PERSON><PERSON><PERSON>, King of Bhutan", "html": "1955 - <a href=\"https://wikipedia.org/wiki/Jig<PERSON>_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"Jig<PERSON>\"><PERSON><PERSON><PERSON></a>, King of Bhutan", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Jig<PERSON>_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"Jig<PERSON>\"><PERSON><PERSON><PERSON></a>, King of Bhutan", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Jig<PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "1955", "text": "<PERSON><PERSON>, Canadian diver", "html": "1955 - <a href=\"https://wikipedia.org/wiki/<PERSON>ri_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Canadian diver", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>ri_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Canadian diver", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Teri_York"}]}, {"year": "1956", "text": "<PERSON><PERSON>, Ghazal singer", "html": "1956 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, <PERSON><PERSON><PERSON> singer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, <PERSON><PERSON><PERSON> singer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1956", "text": "<PERSON>, English guitarist", "html": "1956 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English guitarist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English guitarist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1958", "text": "<PERSON><PERSON>, Spanish singer-songwriter and actress", "html": "1958 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Spanish singer-songwriter and actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Spanish singer-songwriter and actress", "links": [{"title": "Luz Casal", "link": "https://wikipedia.org/wiki/Luz_Casal"}]}, {"year": "1958", "text": "<PERSON><PERSON><PERSON><PERSON>, Lithuanian astronomer and astrophysicist", "html": "1958 - <a href=\"https://wikipedia.org/wiki/Kazimieras_%C4%8Cernis\" title=\"<PERSON><PERSON><PERSON><PERSON>ern<PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Lithuanian astronomer and astrophysicist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Ka<PERSON><PERSON><PERSON>_%C4%8Cernis\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Lithuanian astronomer and astrophysicist", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Kazimieras_%C4%8Cernis"}]}, {"year": "1958", "text": "<PERSON>, Cuban-American actor and playwright", "html": "1958 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A1mara\" title=\"<PERSON>\"><PERSON></a>, Cuban-American actor and playwright", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A1mara\" title=\"<PERSON>\"><PERSON></a>, Cuban-American actor and playwright", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Carlos_Lac%C3%A1mara"}]}, {"year": "1958", "text": "<PERSON>, Australian-English author", "html": "1958 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian-English author", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian-English author", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1959", "text": "<PERSON>, American bodybuilder", "html": "1959 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American bodybuilder", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American bodybuilder", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1959", "text": "<PERSON>, English jockey and trainer", "html": "1959 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(horse_racing)\" title=\"<PERSON> (horse racing)\"><PERSON></a>, English jockey and trainer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(horse_racing)\" title=\"<PERSON> (horse racing)\"><PERSON></a>, English jockey and trainer", "links": [{"title": "<PERSON> (horse racing)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(horse_racing)"}]}, {"year": "1959", "text": "<PERSON>, Swiss criminologist and academic", "html": "1959 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swiss criminologist and academic", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swiss criminologist and academic", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1959", "text": "<PERSON>, American boxer (d. 2013)", "html": "1959 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(boxer)\" title=\"<PERSON> (boxer)\"><PERSON></a>, American boxer (d. 2013)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(boxer)\" title=\"<PERSON> (boxer)\"><PERSON></a>, American boxer (d. 2013)", "links": [{"title": "<PERSON> (boxer)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(boxer)"}]}, {"year": "1960", "text": "<PERSON>, English author and critic (d. 2011)", "html": "1960 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(writer)\" title=\"<PERSON> (writer)\"><PERSON></a>, English author and critic (d. 2011)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(writer)\" title=\"<PERSON> (writer)\"><PERSON></a>, English author and critic (d. 2011)", "links": [{"title": "<PERSON> (writer)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(writer)"}]}, {"year": "1960", "text": "<PERSON>, American baseball player and coach", "html": "1960 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player and coach", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1960", "text": "<PERSON><PERSON>, Jr., Filipino lawyer and politician, 37th Executive Secretary of the Philippines", "html": "1960 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>,_Jr.\" class=\"mw-redirect\" title=\"<PERSON><PERSON>, Jr.\"><PERSON><PERSON>, Jr.</a>, Filipino lawyer and politician, 37th <a href=\"https://wikipedia.org/wiki/Executive_Secretary_(Philippines)\" title=\"Executive Secretary (Philippines)\">Executive Secretary of the Philippines</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>,_Jr.\" class=\"mw-redirect\" title=\"<PERSON><PERSON>, Jr.\"><PERSON><PERSON>, Jr.</a>, Filipino lawyer and politician, 37th <a href=\"https://wikipedia.org/wiki/Executive_Secretary_(Philippines)\" title=\"Executive Secretary (Philippines)\">Executive Secretary of the Philippines</a>", "links": [{"title": "<PERSON><PERSON>, Jr.", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>,_<PERSON>."}, {"title": "Executive Secretary (Philippines)", "link": "https://wikipedia.org/wiki/Executive_Secretary_(Philippines)"}]}, {"year": "1960", "text": "<PERSON>, Kenyan-Italian journalist and author", "html": "1960 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Kenyan-Italian journalist and author", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Kenyan-Italian journalist and author", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1960", "text": "<PERSON>, American actor, producer, and screenwriter", "html": "1960 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor, producer, and screenwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor, producer, and screenwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1960", "text": "<PERSON>, American actor and director", "html": "1960 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor and director", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor and director", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1961", "text": "<PERSON>, Russian-born entrepreneur, venture capitalist and physicist", "html": "1961 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian-born entrepreneur, venture capitalist and physicist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian-born entrepreneur, venture capitalist and physicist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1962", "text": "<PERSON>, Maltese-Australian rugby league player and sportscaster", "html": "1962 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Maltese-Australian rugby league player and sportscaster", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Maltese-Australian rugby league player and sportscaster", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1962", "text": "<PERSON><PERSON>, Greek footballer (d. 1997)", "html": "1962 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> Mi<PERSON>ib<PERSON>\"><PERSON><PERSON></a>, Greek footballer (d. 1997)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Greek footballer (d. 1997)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Georgios_Mitsibonas"}]}, {"year": "1962", "text": "<PERSON><PERSON>, American actress, director, and producer", "html": "1962 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American actress, director, and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American actress, director, and producer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1962", "text": "<PERSON>, Australian trumpet player and composer", "html": "1962 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(jazz_musician)\" title=\"<PERSON> (jazz musician)\"><PERSON></a>, Australian trumpet player and composer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(jazz_musician)\" title=\"<PERSON> (jazz musician)\"><PERSON></a>, Australian trumpet player and composer", "links": [{"title": "<PERSON> (jazz musician)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(jazz_musician)"}]}, {"year": "1963", "text": "<PERSON>, American wrestler and actor", "html": "1963 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American wrestler and actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>\" title=\"<PERSON>\"><PERSON></a>, American wrestler and actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1964", "text": "<PERSON><PERSON>, American painter and potter (d. 2015)", "html": "1964 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American painter and potter (d. 2015)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American painter and potter (d. 2015)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>haw"}]}, {"year": "1964", "text": "<PERSON><PERSON>, American actress", "html": "1964 - <a href=\"https://wikipedia.org/wiki/Calista_Flockhart\" title=\"Calista Flockhart\"><PERSON><PERSON></a>, American actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Calista_Flockhart\" title=\"Calista Flockhart\"><PERSON><PERSON></a>, American actress", "links": [{"title": "Cal<PERSON>", "link": "https://wikipedia.org/wiki/Calista_Flockhart"}]}, {"year": "1964", "text": "<PERSON>, American actor (d. 2019)", "html": "1964 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor (d. 2019)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor (d. 2019)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1965", "text": "<PERSON>, American screenwriter and producer", "html": "1965 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American screenwriter and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American screenwriter and producer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1965", "text": "<PERSON>, Canadian singer-songwriter", "html": "1965 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian singer-songwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian singer-songwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1966", "text": "<PERSON><PERSON>, Italian model and actress", "html": "1966 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Italian model and actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Italian model and actress", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1966", "text": "<PERSON>, Australian actor", "html": "1966 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1966", "text": "<PERSON>, Irish model and actress", "html": "1966 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Irish model and actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Irish model and actress", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1966", "text": "<PERSON><PERSON><PERSON>, Canadian musician and producer", "html": "1966 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_(musician)\" title=\"<PERSON><PERSON><PERSON> (musician)\"><PERSON><PERSON><PERSON></a>, Canadian musician and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_(musician)\" title=\"<PERSON><PERSON><PERSON> (musician)\"><PERSON><PERSON><PERSON></a>, Canadian musician and producer", "links": [{"title": "<PERSON><PERSON><PERSON> (musician)", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_(musician)"}]}, {"year": "1967", "text": "<PERSON>, Brazilian race car driver (d.2023)", "html": "1967 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Brazilian race car driver (d.2023)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Brazilian race car driver (d.2023)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1967", "text": "<PERSON>, Northern Irish video game designer", "html": "1967 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Northern Irish video game designer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Northern Irish video game designer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1967", "text": "<PERSON>, American actor, producer, and screenwriter", "html": "1967 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor, producer, and screenwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor, producer, and screenwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1968", "text": "<PERSON>, Italian footballer and manager", "html": "1968 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian footballer and manager", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian footballer and manager", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>ser"}]}, {"year": "1969", "text": "<PERSON>, American fashion designer, television personality, and actor", "html": "1969 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American fashion designer, television personality, and actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American fashion designer, television personality, and actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1971", "text": "<PERSON>, American actor and director", "html": "1971 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor and director", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor and director", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1971", "text": "<PERSON>, Lithuanian basketball player and coach", "html": "1971 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C4%8D%C4%97sas\" title=\"<PERSON>\"><PERSON></a>, Lithuanian basketball player and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C4%8D%C4%97sas\" title=\"<PERSON>\"><PERSON></a>, Lithuanian basketball player and coach", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Tomas_Pa%C4%8D%C4%97sas"}]}, {"year": "1972", "text": "<PERSON>, Canadian actor", "html": "1972 - <a href=\"https://wikipedia.org/wiki/Adam_Beach\" title=\"Adam Beach\"><PERSON></a>, Canadian actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Adam_Beach\" title=\"Adam Beach\"><PERSON></a>, Canadian actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Adam_Beach"}]}, {"year": "1973", "text": "<PERSON>, American singer-songwriter and guitarist", "html": "1973 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(Green_Day_guitarist)\" title=\"<PERSON> (Green Day guitarist)\"><PERSON></a>, American singer-songwriter and guitarist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(Green_Day_guitarist)\" title=\"<PERSON> (Green Day guitarist)\"><PERSON></a>, American singer-songwriter and guitarist", "links": [{"title": "<PERSON> (Green Day guitarist)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(Green_Day_guitarist)"}]}, {"year": "1974", "text": "<PERSON>, American singer-songwriter and producer", "html": "1974 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>.\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>.\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and producer", "links": [{"title": "<PERSON>.", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>."}]}, {"year": "1974", "text": "<PERSON>, American actor and producer", "html": "1974 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor and producer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Leonardo_DiCaprio"}]}, {"year": "1974", "text": "<PERSON><PERSON> <PERSON>, American singer-songwriter and producer (d. 2008)", "html": "1974 - <a href=\"https://wikipedia.org/wiki/Static_Major\" title=\"Static Major\"><PERSON><PERSON> Major</a>, American singer-songwriter and producer (d. 2008)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Static_Major\" title=\"Static Major\"><PERSON><PERSON> Major</a>, American singer-songwriter and producer (d. 2008)", "links": [{"title": "Static Major", "link": "https://wikipedia.org/wiki/Static_Major"}]}, {"year": "1974", "text": "<PERSON><PERSON><PERSON><PERSON><PERSON>, Pakistani cricketer", "html": "1974 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON><PERSON></a>, Pakistani cricketer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON><PERSON></a>, Pakistani cricketer", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1975", "text": "<PERSON><PERSON>, Japanese rugby player", "html": "1975 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Japanese rugby player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Japanese rugby player", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1976", "text": "<PERSON>, American baseball player", "html": "1976 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1976", "text": "<PERSON>, Canadian bass player", "html": "1976 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian bass player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian bass player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1977", "text": "<PERSON>, Australian-English cricketer (d. 2002)", "html": "1977 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian-English cricketer (d. 2002)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian-English cricketer (d. 2002)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1977", "text": "<PERSON>, American philanthropist, activist and fashion model", "html": "1977 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American philanthropist, activist and fashion model", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American philanthropist, activist and fashion model", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1977", "text": "<PERSON><PERSON>, Portuguese footballer and manager", "html": "1977 - <a href=\"https://wikipedia.org/wiki/Maniche\" title=\"Man<PERSON>\"><PERSON><PERSON></a>, Portuguese footballer and manager", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Maniche\" title=\"Man<PERSON>\"><PERSON><PERSON></a>, Portuguese footballer and manager", "links": [{"title": "Maniche", "link": "https://wikipedia.org/wiki/Maniche"}]}, {"year": "1977", "text": "<PERSON><PERSON>, American actor and producer", "html": "1977 - <a href=\"https://wikipedia.org/wiki/Sc<PERSON>_<PERSON>cNair<PERSON>\" title=\"<PERSON>oot McNairy\"><PERSON><PERSON></a>, American actor and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>c<PERSON><PERSON>\" title=\"<PERSON>oot McNairy\"><PERSON><PERSON></a>, American actor and producer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Sc<PERSON>_<PERSON>c<PERSON><PERSON>y"}]}, {"year": "1977", "text": "<PERSON><PERSON>, Iranian-American author (d. 2014)", "html": "1977 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Iranian-American author (d. 2014)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Iranian-American author (d. 2014)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>n"}]}, {"year": "1978", "text": "<PERSON>, New Zealand cricketer", "html": "1978 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, New Zealand cricketer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, New Zealand cricketer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1980", "text": "<PERSON>, Canadian ice hockey player", "html": "1980 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(ice_hockey)\" title=\"<PERSON> (ice hockey)\"><PERSON></a>, Canadian ice hockey player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(ice_hockey)\" title=\"<PERSON> (ice hockey)\"><PERSON></a>, Canadian ice hockey player", "links": [{"title": "<PERSON> (ice hockey)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(ice_hockey)"}]}, {"year": "1980", "text": "<PERSON><PERSON><PERSON>, Zimbabwean-German rugby player", "html": "1980 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>end<PERSON>\" title=\"<PERSON><PERSON><PERSON> Ta<PERSON>end<PERSON>\"><PERSON><PERSON><PERSON></a>, Zimbabwean-German rugby player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>end<PERSON>\" title=\"<PERSON><PERSON><PERSON> Ta<PERSON>end<PERSON>\"><PERSON><PERSON><PERSON></a>, Zimbabwean-German rugby player", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>a"}]}, {"year": "1982", "text": "<PERSON><PERSON><PERSON>, Argentinian-Italian rugby player", "html": "1982 - <a href=\"https://wikipedia.org/wiki/Gonzalo_Canale\" title=\"Gonzalo Canale\"><PERSON><PERSON><PERSON></a>, Argentinian-Italian rugby player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Gonzalo_Canale\" title=\"Gonzalo Canale\"><PERSON><PERSON><PERSON></a>, Argentinian-Italian rugby player", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Go<PERSON>lo_<PERSON>e"}]}, {"year": "1982", "text": "<PERSON>, American wrestler", "html": "1982 - <a href=\"https://wikipedia.org/wiki/LA_Knight\" title=\"LA Knight\"><PERSON></a>, American wrestler", "no_year_html": "<a href=\"https://wikipedia.org/wiki/LA_Knight\" title=\"LA Knight\"><PERSON></a>, American wrestler", "links": [{"title": "LA Knight", "link": "https://wikipedia.org/wiki/LA_Knight"}]}, {"year": "1983", "text": "<PERSON><PERSON><PERSON>, Ivorian footballer", "html": "1983 - <a href=\"https://wikipedia.org/wiki/Arouna_Kon%C3%A9\" title=\"<PERSON>rouna Ko<PERSON>\"><PERSON><PERSON><PERSON></a>, Ivorian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Arouna_Kon%C3%A9\" title=\"<PERSON>rou<PERSON>\"><PERSON><PERSON><PERSON></a>, Ivorian footballer", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Arouna_Kon%C3%A9"}]}, {"year": "1983", "text": "<PERSON>, German footballer", "html": "1983 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1983", "text": "<PERSON><PERSON><PERSON><PERSON>, Japanese voice actor and singer", "html": "1983 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Japanese voice actor and singer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Japanese voice actor and singer", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1984", "text": "<PERSON>, American basketball player", "html": "1984 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1984", "text": "<PERSON>, English footballer", "html": "1984 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(footballer_born_1984)\" class=\"mw-redirect\" title=\"<PERSON> (footballer born 1984)\"><PERSON></a>, English footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(footballer_born_1984)\" class=\"mw-redirect\" title=\"<PERSON> (footballer born 1984)\"><PERSON></a>, English footballer", "links": [{"title": "<PERSON> (footballer born 1984)", "link": "https://wikipedia.org/wiki/<PERSON>(footballer_born_1984)"}]}, {"year": "1984", "text": "<PERSON><PERSON><PERSON>, Icelandic footballer", "html": "1984 - <a href=\"https://wikipedia.org/wiki/Birkir_M%C3%A1r_S%C3%A<PERSON><PERSON><PERSON>\" title=\"Birkir <PERSON>\"><PERSON><PERSON><PERSON> <PERSON><PERSON></a>, Icelandic footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Birkir_M%C3%A1r_S%C3%A<PERSON><PERSON><PERSON>\" title=\"Birkir <PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Icelandic footballer", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Birkir_M%C3%A1r_S%C3%A<PERSON><PERSON><PERSON>"}]}, {"year": "1985", "text": "<PERSON><PERSON><PERSON><PERSON>, Cuban footballer", "html": "1985 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Cuban footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Cuban footballer", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1985", "text": "<PERSON>, American football player", "html": "1985 - <a href=\"https://wikipedia.org/wiki/Austin_Collie\" title=\"Austin Collie\"><PERSON></a>, American football player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Austin_Collie\" title=\"Austin Collie\"><PERSON></a>, American football player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Austin_Collie"}]}, {"year": "1985", "text": "<PERSON><PERSON><PERSON><PERSON>, Estonian runner", "html": "1985 - <a href=\"https://wikipedia.org/wiki/Tiidrek_Nurme\" title=\"Tiidrek Nurme\">Tiidrek Nurme</a>, Estonian runner", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Tiidrek_Nurme\" title=\"Tiidrek Nurme\">Tiidre<PERSON> Nurme</a>, Estonian runner", "links": [{"title": "Tiidrek Nurme", "link": "https://wikipedia.org/wiki/Tiidrek_Nurme"}]}, {"year": "1985", "text": "<PERSON>, American singer", "html": "1985 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1985", "text": "<PERSON>, Indian cricketer", "html": "1985 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Indian cricketer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Indian cricketer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1986", "text": "<PERSON>, American singer and pianist", "html": "1986 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer and pianist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer and pianist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1986", "text": "<PERSON>, American football player", "html": "1986 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(American_football)\" class=\"mw-redirect\" title=\"<PERSON> (American football)\"><PERSON></a>, American football player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(American_football)\" class=\"mw-redirect\" title=\"<PERSON> (American football)\"><PERSON></a>, American football player", "links": [{"title": "<PERSON> (American football)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(American_football)"}]}, {"year": "1986", "text": "<PERSON>, American football player", "html": "1986 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1986", "text": "<PERSON>, French rugby player", "html": "1986 - <a href=\"https://wikipedia.org/wiki/Fran%C3%A7<PERSON>_<PERSON>-<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French rugby player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Fran%C3%A<PERSON><PERSON>_<PERSON>-<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French rugby player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Fran%C3%A7<PERSON>_<PERSON>-<PERSON>"}]}, {"year": "1987", "text": "<PERSON><PERSON>, American actor", "html": "1987 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American actor", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>o"}]}, {"year": "1987", "text": "<PERSON><PERSON>, English model and singer", "html": "1987 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, English model and singer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, English model and singer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1988", "text": "<PERSON>, Argentinian-Slovak footballer", "html": "1988 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Argentinian-Slovak footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Argentinian-Slovak footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1988", "text": "<PERSON><PERSON><PERSON>, Japanese voice actress and singer", "html": "1988 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese voice actress and singer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese voice actress and singer", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1988", "text": "<PERSON>, English footballer", "html": "1988 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1989", "text": "<PERSON>, English-Israeli footballer", "html": "1989 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English-Israeli footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English-Israeli footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1989", "text": "<PERSON>, American basketball player", "html": "1989 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1989", "text": "<PERSON>, American figure skater", "html": "1989 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American figure skater", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American figure skater", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1989", "text": "<PERSON><PERSON>, Japanese singer", "html": "1989 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Japanese singer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Japanese singer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1989", "text": "<PERSON>, Scottish race car driver", "html": "1989 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish race car driver", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish race car driver", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1990", "text": "<PERSON>, Dutch road bicycle racer", "html": "1990 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch road bicycle racer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch road bicycle racer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1990", "text": "<PERSON>, Papua New Guinean rugby league player", "html": "1990 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Papua New Guinean rugby league player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Papua New Guinean rugby league player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1990", "text": "<PERSON><PERSON><PERSON>, Dutch footballer", "html": "1990 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Dutch footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Dutch footballer", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1991", "text": "<PERSON><PERSON>, American actress", "html": "1991 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON> <PERSON><PERSON></a>, American actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON> <PERSON><PERSON></a>, American actress", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "1991", "text": "<PERSON><PERSON>, Japanese curler", "html": "1991 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_Onodera\" title=\"<PERSON><PERSON> Onodera\"><PERSON><PERSON></a>, Japanese curler", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_Onodera\" title=\"<PERSON><PERSON> Onodera\"><PERSON><PERSON></a>, Japanese curler", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_Onodera"}]}, {"year": "1992", "text": "<PERSON><PERSON><PERSON>, Argentine tennis player", "html": "1992 - <a href=\"https://wikipedia.org/wiki/Sof%C3%AD<PERSON>_<PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Argentine tennis player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Sof%C3%AD<PERSON>_<PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Argentine tennis player", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Sof%C3%AD<PERSON>_<PERSON><PERSON>"}]}, {"year": "1992", "text": "<PERSON><PERSON><PERSON>, Canadian ice hockey player", "html": "1992 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Canadian ice hockey player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Canadian ice hockey player", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>-<PERSON>"}]}, {"year": "1993", "text": "<PERSON><PERSON>, English footballer", "html": "1993 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, English footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, English footballer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1994", "text": "<PERSON><PERSON>, American wrestler", "html": "1994 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"Lio <PERSON>\"><PERSON><PERSON></a>, American wrestler", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American wrestler", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1994", "text": "<PERSON><PERSON>, Indian cricketer", "html": "1994 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Indian cricketer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Indian cricketer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_Samson"}]}, {"year": "1994", "text": "<PERSON>, English swimmer", "html": "1994 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Si<PERSON>\"><PERSON></a>, English swimmer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Simmond<PERSON>\"><PERSON></a>, English swimmer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1995", "text": "<PERSON>, New Zealand rugby league player", "html": "1995 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, New Zealand rugby league player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, New Zealand rugby league player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1995", "text": "<PERSON><PERSON>, British tennis player", "html": "1995 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, British tennis player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, British tennis player", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1996", "text": "<PERSON><PERSON>, American actor and producer", "html": "1996 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American actor and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American actor and producer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1998", "text": "<PERSON><PERSON><PERSON><PERSON>, Russian tennis player", "html": "1998 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Russian tennis player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Russian tennis player", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1999", "text": "<PERSON>, American activist", "html": "1999 - <a href=\"https://wikipedia.org/wiki/X_Gonz%C3%A1lez\" title=\"<PERSON>\"><PERSON></a>, American activist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/X_Gonz%C3%A1lez\" title=\"<PERSON>\"><PERSON></a>, American activist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/X_Gonz%C3%A1lez"}]}, {"year": "2004", "text": "<PERSON><PERSON>, American actor", "html": "2004 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American actor", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "2005", "text": "<PERSON>, Scottish footballer", "html": "2005 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}], "Deaths": [{"year": "405", "text": "<PERSON><PERSON><PERSON> of Tarsus, Tarsian archbishop (b. 324)", "html": "405 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_of_Tarsus\" title=\"<PERSON><PERSON><PERSON> of Tarsus\"><PERSON><PERSON><PERSON> of Tarsus</a>, Tarsian archbishop (b. 324)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_of_Tarsus\" title=\"<PERSON><PERSON><PERSON> of Tarsus\"><PERSON><PERSON><PERSON> of Tarsus</a>, Tarsian archbishop (b. 324)", "links": [{"title": "<PERSON><PERSON><PERSON> of Tarsus", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_of_Tarsus"}]}, {"year": "683", "text": "<PERSON><PERSON><PERSON>, Muslim caliph (b. 647)", "html": "683 - <a href=\"https://wikipedia.org/wiki/Yazi<PERSON>_<PERSON>\" title=\"<PERSON>zi<PERSON> <PERSON>\"><PERSON><PERSON><PERSON> <PERSON></a>, Muslim <a href=\"https://wikipedia.org/wiki/Caliph\" class=\"mw-redirect\" title=\"<PERSON>ip<PERSON>\">caliph</a> (b. 647)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Ya<PERSON><PERSON>_<PERSON>\" title=\"Yazid <PERSON>\"><PERSON><PERSON><PERSON> I</a>, Muslim <a href=\"https://wikipedia.org/wiki/Caliph\" class=\"mw-redirect\" title=\"Caliph\">caliph</a> (b. 647)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}, {"title": "Caliph", "link": "https://wikipedia.org/wiki/Caliph"}]}, {"year": "865", "text": "<PERSON><PERSON><PERSON>, Byzantine general", "html": "865 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_(general)\" title=\"<PERSON><PERSON><PERSON> (general)\"><PERSON><PERSON><PERSON></a>, Byzantine general", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_(general)\" title=\"<PERSON><PERSON><PERSON> (general)\"><PERSON><PERSON><PERSON></a>, Byzantine general", "links": [{"title": "<PERSON><PERSON><PERSON> (general)", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>s_(general)"}]}, {"year": "865", "text": "<PERSON> the Younger, Byzantine monk and saint (b. 785)", "html": "865 - <a href=\"https://wikipedia.org/wiki/<PERSON>_the_Younger\" title=\"<PERSON> the Younger\"><PERSON> the Younger</a>, Byzantine monk and saint (b. 785)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_the_Younger\" title=\"<PERSON> the Younger\"><PERSON> the Younger</a>, Byzantine monk and saint (b. 785)", "links": [{"title": "<PERSON> the Younger", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "875", "text": "<PERSON><PERSON><PERSON><PERSON>, queen of Lotharingia", "html": "875 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>a\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, queen of <a href=\"https://wikipedia.org/wiki/Lotharingia\" title=\"Lotharingia\">Lotharingia</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>a\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, queen of <a href=\"https://wikipedia.org/wiki/Lotharingia\" title=\"Lotharingia\">Lotharingia</a>", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>a"}, {"title": "Lotharingia", "link": "https://wikipedia.org/wiki/Lotharingia"}]}, {"year": "1028", "text": "<PERSON>, Byzantine emperor (b. 960)", "html": "1028 - <a href=\"https://wikipedia.org/wiki/Constantine_VIII\" title=\"Constantine VIII\"><PERSON> VIII</a>, Byzantine emperor (b. 960)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Constantine_VIII\" title=\"Constantine VIII\"><PERSON> VIII</a>, Byzantine emperor (b. 960)", "links": [{"title": "Constantine VIII", "link": "https://wikipedia.org/wiki/Constantine_VIII"}]}, {"year": "1078", "text": "<PERSON><PERSON> of Nellenburg, Archbishop of Trier (during the siege of Tübingen)", "html": "1078 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_(archbishop_of_Trier)\" title=\"<PERSON><PERSON> (archbishop of Trier)\"><PERSON><PERSON> of Nellenburg, Archbishop of Trier</a> (during the siege of <a href=\"https://wikipedia.org/wiki/T%C3%BCbingen\" title=\"Tübingen\">Tübingen</a>)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_(archbishop_of_Trier)\" title=\"<PERSON><PERSON> (archbishop of Trier)\"><PERSON><PERSON> of Nellenburg, Archbishop of Trier</a> (during the siege of <a href=\"https://wikipedia.org/wiki/T%C3%BCbingen\" title=\"Tübingen\">Tübingen</a>)", "links": [{"title": "<PERSON><PERSON> (archbishop of Trier)", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_(archbishop_of_Trier)"}, {"title": "Tübingen", "link": "https://wikipedia.org/wiki/T%C3%BCbingen"}]}, {"year": "1089", "text": "<PERSON> <PERSON>, Italian Benedictine monk", "html": "1089 - <PERSON> <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian Benedictine monk", "no_year_html": "<PERSON> <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian Benedictine monk", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1130", "text": "<PERSON> León, Countess of Portugal, Portuguese regent (b. 1080)", "html": "1130 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_Le%C3%B3n,_Countess_of_Portugal\" class=\"mw-redirect\" title=\"<PERSON> León, Countess of Portugal\"><PERSON>, Countess of Portugal</a>, Portuguese regent (b. 1080)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_Le%C3%B3n,_Countess_of_Portugal\" class=\"mw-redirect\" title=\"<PERSON> León, Countess of Portugal\"><PERSON>, Countess of Portugal</a>, Portuguese regent (b. 1080)", "links": [{"title": "<PERSON> León, Countess of Portugal", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_Le%C3%B3n,_<PERSON>_of_Portugal"}]}, {"year": "1189", "text": "King <PERSON> of Sicily (\"the Good\") (b. 1153)", "html": "1189 - King <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Sicily\" title=\"<PERSON> II of Sicily\"><PERSON> of Sicily</a> (\"the Good\") (b. 1153)", "no_year_html": "King <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Sicily\" title=\"<PERSON> II of Sicily\"><PERSON> of Sicily</a> (\"the Good\") (b. 1153)", "links": [{"title": "<PERSON> of Sicily", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Sicily"}]}, {"year": "1285", "text": "King <PERSON> of Aragon (b. 1239)", "html": "1285 - <PERSON> <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_Aragon\" title=\"<PERSON> of Aragon\"><PERSON> of Aragon</a> (b. 1239)", "no_year_html": "King <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_Aragon\" title=\"<PERSON> of Aragon\"><PERSON> of Aragon</a> (b. 1239)", "links": [{"title": "<PERSON> of Aragon", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_Aragon"}]}, {"year": "1331", "text": "<PERSON> of Serbia (b. c. 1285)", "html": "1331 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>%C5%A1_III_De%C4%8Danski_of_Serbia\" class=\"mw-redirect\" title=\"<PERSON> of Serbia\"><PERSON> of Serbia</a> (b. c. 1285)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>%C5%A1_III_De%C4%8Dans<PERSON>_of_Serbia\" class=\"mw-redirect\" title=\"<PERSON> of Serbia\"><PERSON> of Serbia</a> (b. c. 1285)", "links": [{"title": "<PERSON> Uroš III Dečanski of Serbia", "link": "https://wikipedia.org/wiki/Stefan_Uro%C5%A1_III_De%C4%8Danski_of_Serbia"}]}, {"year": "1561", "text": "<PERSON>, Danish reformer (b. 1494)", "html": "1561 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Danish reformer (b. 1494)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Danish reformer (b. 1494)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1583", "text": "<PERSON>, 14th Earl of Desmond, Irish rebel", "html": "1583 - <a href=\"https://wikipedia.org/wiki/<PERSON>,_14th_Earl_<PERSON>_Desmond\" title=\"<PERSON>, 14th Earl of Desmond\"><PERSON>, 14th Earl <PERSON> Desmond</a>, Irish rebel", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>,_14th_Earl_<PERSON>_Desmond\" title=\"<PERSON>, 14th Earl of Desmond\"><PERSON>, 14th Earl <PERSON> Desmond</a>, Irish rebel", "links": [{"title": "<PERSON>, 14th Earl of Desmond", "link": "https://wikipedia.org/wiki/<PERSON>,_14th_Earl_<PERSON>_<PERSON>"}]}, {"year": "1623", "text": "<PERSON>, French theorist and author (b. 1549)", "html": "1623 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French theorist and author (b. 1549)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French theorist and author (b. 1549)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1638", "text": "<PERSON><PERSON><PERSON>, Dutch painter and illustrator (b. 1562)", "html": "1638 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_Ha<PERSON>\" title=\"<PERSON><PERSON><PERSON> van Haarlem\"><PERSON><PERSON><PERSON> Ha<PERSON></a>, Dutch painter and illustrator (b. 1562)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_van_Ha<PERSON>\" title=\"<PERSON><PERSON><PERSON> van Haarlem\"><PERSON><PERSON><PERSON> Ha<PERSON></a>, Dutch painter and illustrator (b. 1562)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1724", "text": "<PERSON>, English criminal (b. 1700)", "html": "1724 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(criminal)\" title=\"<PERSON> (criminal)\"><PERSON></a>, English criminal (b. 1700)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(criminal)\" title=\"<PERSON> (criminal)\"><PERSON></a>, English criminal (b. 1700)", "links": [{"title": "<PERSON> (criminal)", "link": "https://wikipedia.org/wiki/<PERSON>_(criminal)"}]}, {"year": "1812", "text": "<PERSON><PERSON>, Russian metropolitan (b. 1737)", "html": "1812 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"Plato<PERSON>\"><PERSON><PERSON></a>, Russian metropolitan (b. 1737)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"Plato<PERSON>\"><PERSON><PERSON></a>, Russian metropolitan (b. 1737)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1831", "text": "<PERSON>, American slave and rebel leader (b. 1800)", "html": "1831 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American slave and rebel leader (b. 1800)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American slave and rebel leader (b. 1800)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1855", "text": "<PERSON><PERSON><PERSON>, Danish philosopher, author, and poet (b. 1813)", "html": "1855 - <a href=\"https://wikipedia.org/wiki/S%C3%B8<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Danish philosopher, author, and poet (b. 1813)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/S%C3%B8<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Danish philosopher, author, and poet (b. 1813)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/S%C3%B8<PERSON>_<PERSON><PERSON><PERSON>"}]}, {"year": "1861", "text": "<PERSON> of Portugal (b. 1837)", "html": "1861 - <a href=\"https://wikipedia.org/wiki/Pedro_V_of_Portugal\" title=\"Pedro V of Portugal\">Pedro V of Portugal</a> (b. 1837)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Pedro_V_of_Portugal\" title=\"Pedro V of Portugal\">Pedro V of Portugal</a> (b. 1837)", "links": [{"title": "Pedro V of Portugal", "link": "https://wikipedia.org/wiki/Pedro_V_of_Portugal"}]}, {"year": "1862", "text": "<PERSON>, American lawyer and politician, 18th United States Secretary of War (b. 1793)", "html": "1862 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and politician, 18th <a href=\"https://wikipedia.org/wiki/United_States_Secretary_of_War\" title=\"United States Secretary of War\">United States Secretary of War</a> (b. 1793)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and politician, 18th <a href=\"https://wikipedia.org/wiki/United_States_Secretary_of_War\" title=\"United States Secretary of War\">United States Secretary of War</a> (b. 1793)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "United States Secretary of War", "link": "https://wikipedia.org/wiki/United_States_Secretary_of_War"}]}, {"year": "1880", "text": "<PERSON>, Australian bushranger (b. 1855)", "html": "1880 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian bushranger (b. 1855)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian bushranger (b. 1855)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1880", "text": "<PERSON><PERSON><PERSON>, American activist (b. 1793)", "html": "1880 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American activist (b. 1793)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American activist (b. 1793)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Lucretia_Mott"}]}, {"year": "1884", "text": "<PERSON>, German zoologist, author, and illustrator (b. 1827)", "html": "1884 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German zoologist, author, and illustrator (b. 1827)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German zoologist, author, and illustrator (b. 1827)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1887", "text": "Haymarket affair defendants:", "html": "1887 - <a href=\"https://wikipedia.org/wiki/Haymarket_affair\" title=\"Haymarket affair\">Haymarket affair</a> defendants:", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Haymarket_affair\" title=\"Haymarket affair\">Haymarket affair</a> defendants:", "links": [{"title": "Haymarket affair", "link": "https://wikipedia.org/wiki/Haymarket_affair"}]}, {"year": "1887", "text": "<PERSON>, German-American businessman and activist (b. 1836)", "html": "1887 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German-American businessman and activist (b. 1836)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German-American businessman and activist (b. 1836)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1887", "text": "<PERSON><PERSON><PERSON>, German-American printer and activist (b. 1858)", "html": "1887 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, German-American printer and activist (b. 1858)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, German-American printer and activist (b. 1858)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1887", "text": "<PERSON>, American journalist and activist (b. 1848)", "html": "1887 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American journalist and activist (b. 1848)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American journalist and activist (b. 1848)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1887", "text": "<PERSON>, American journalist and activist (b. 1855)", "html": "1887 - <a href=\"https://wikipedia.org/wiki/August_Spies\" title=\"August Spies\">August Spies</a>, American journalist and activist (b. 1855)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/August_Spies\" title=\"August Spies\">August Spies</a>, American journalist and activist (b. 1855)", "links": [{"title": "August Spies", "link": "https://wikipedia.org/wiki/August_Spies"}]}, {"year": "1888", "text": "<PERSON>, Chilean pirate active in the fjords and channels of Patagonia. He was executed.", "html": "1888 - <a href=\"https://wikipedia.org/wiki/<PERSON>_%C3%91anc%C3%BApel\" title=\"<PERSON>\"><PERSON></a>, Chilean pirate active in the <a href=\"https://wikipedia.org/wiki/Fjords_and_channels_of_Chile\" title=\"Fjords and channels of Chile\">fjords and channels of Patagonia</a>. He was executed.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_%C3%91anc%C3%BApel\" title=\"<PERSON>\"><PERSON></a>, Chilean pirate active in the <a href=\"https://wikipedia.org/wiki/Fjords_and_channels_of_Chile\" title=\"Fjords and channels of Chile\">fjords and channels of Patagonia</a>. He was executed.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_%C3%91anc%C3%BApel"}, {"title": "Fjords and channels of Chile", "link": "https://wikipedia.org/wiki/Fjords_and_channels_of_Chile"}]}, {"year": "1917", "text": "<PERSON><PERSON><PERSON><PERSON> of Hawaii (b. 1838)", "html": "1917 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a> of Hawaii (b. 1838)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a> of Hawaii (b. 1838)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>"}]}, {"year": "1918", "text": "<PERSON>, Canadian soldier (b. 1892)", "html": "1918 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian soldier (b. 1892)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian soldier (b. 1892)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1919", "text": "<PERSON>, Russian painter and educator (b. 1832)", "html": "1919 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian painter and educator (b. 1832)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian painter and educator (b. 1832)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1921", "text": "<PERSON>, French target shooter (b. 1852)", "html": "1921 - <a href=\"https://wikipedia.org/wiki/L%C3%A9<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French target shooter (b. 1852)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/L%C3%A<PERSON><PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French target shooter (b. 1852)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/L%C3%A9on_<PERSON>"}]}, {"year": "1931", "text": "<PERSON><PERSON><PERSON>, Japanese businessman (b. 1840)", "html": "1931 - <a href=\"https://wikipedia.org/wiki/Shibus<PERSON>_Eiichi\" title=\"Shibusawa Eiichi\"><PERSON><PERSON><PERSON></a>, Japanese businessman (b. 1840)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Shibus<PERSON>_E<PERSON>chi\" title=\"Shibusawa Eiichi\"><PERSON><PERSON><PERSON></a>, Japanese businessman (b. 1840)", "links": [{"title": "Shibusawa Eiichi", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>chi"}]}, {"year": "1939", "text": "<PERSON>, American author and activist (b. 1901)", "html": "1939 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(wilderness_activist)\" title=\"<PERSON> (wilderness activist)\"><PERSON></a>, American author and activist (b. 1901)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(wilderness_activist)\" title=\"<PERSON> (wilderness activist)\"><PERSON></a>, American author and activist (b. 1901)", "links": [{"title": "<PERSON> (wilderness activist)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(wilderness_activist)"}]}, {"year": "1940", "text": "<PERSON><PERSON><PERSON>, Turkish general and diplomat (b. 1870)", "html": "1940 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>ky%C3%BCz\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Turkish general and diplomat (b. 1870)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>ky%C3%BCz\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Turkish general and diplomat (b. 1870)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>tin_Aky%C3%BCz"}]}, {"year": "1944", "text": "<PERSON><PERSON>, Turkish diplomat (b. 1883)", "html": "1944 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Turkish diplomat (b. 1883)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"Munir E<PERSON>gun\"><PERSON><PERSON></a>, Turkish diplomat (b. 1883)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Mu<PERSON>_<PERSON><PERSON>"}]}, {"year": "1945", "text": "<PERSON>, American composer (b. 1885)", "html": "1945 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American composer (b. 1885)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American composer (b. 1885)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1949", "text": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, Greek lawyer and politician, Greek Minister of Foreign Minister (b. 1878)", "html": "1949 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>-<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Greek lawyer and politician, <a href=\"https://wikipedia.org/wiki/List_of_foreign_ministers_of_Greece\" title=\"List of foreign ministers of Greece\">Greek Minister of Foreign Minister</a> (b. 1878)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>-<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Greek lawyer and politician, <a href=\"https://wikipedia.org/wiki/List_of_foreign_ministers_of_Greece\" title=\"List of foreign ministers of Greece\">Greek Minister of Foreign Minister</a> (b. 1878)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON><PERSON>-<PERSON>"}, {"title": "List of foreign ministers of Greece", "link": "https://wikipedia.org/wiki/List_of_foreign_ministers_of_Greece"}]}, {"year": "1950", "text": "<PERSON><PERSON><PERSON>, Greek banker and politician, 145th Prime Minister of Greece (b. 1875)", "html": "1950 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Greek banker and politician, 145th <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Greece\" title=\"Prime Minister of Greece\">Prime Minister of Greece</a> (b. 1875)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Greek banker and politician, 145th <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Greece\" title=\"Prime Minister of Greece\">Prime Minister of Greece</a> (b. 1875)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}, {"title": "Prime Minister of Greece", "link": "https://wikipedia.org/wiki/Prime_Minister_of_Greece"}]}, {"year": "1953", "text": "Princess <PERSON> of Hesse and by Rhine (b. 1866)", "html": "1953 - <a href=\"https://wikipedia.org/wiki/Princess_<PERSON>_of_Hesse_and_by_Rhine\" title=\"Princess <PERSON> of Hesse and by Rhine\">Princess <PERSON> of Hesse and by Rhine</a> (b. 1866)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Princess_<PERSON>_of_Hesse_and_by_Rhine\" title=\"Princess <PERSON> of Hesse and by Rhine\">Princess <PERSON> of Hesse and by Rhine</a> (b. 1866)", "links": [{"title": "Princess <PERSON> of Hesse and by Rhine", "link": "https://wikipedia.org/wiki/Princess_Irene_of_Hesse_and_by_Rhine"}]}, {"year": "1961", "text": "<PERSON><PERSON><PERSON>, Turkish colonel and politician, Turkish Minister of Environment and Urban Planning (b. 1876)", "html": "1961 - <a href=\"https://wikipedia.org/wiki/Behi%C3%A7_<PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Turkish colonel and politician, <a href=\"https://wikipedia.org/wiki/Ministry_of_Environment_and_Urban_Planning_(Turkey)\" class=\"mw-redirect\" title=\"Ministry of Environment and Urban Planning (Turkey)\">Turkish Minister of Environment and Urban Planning</a> (b. 1876)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Behi%C3%A7_<PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Turkish colonel and politician, <a href=\"https://wikipedia.org/wiki/Ministry_of_Environment_and_Urban_Planning_(Turkey)\" class=\"mw-redirect\" title=\"Ministry of Environment and Urban Planning (Turkey)\">Turkish Minister of Environment and Urban Planning</a> (b. 1876)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Behi%C3%A7_<PERSON><PERSON>"}, {"title": "Ministry of Environment and Urban Planning (Turkey)", "link": "https://wikipedia.org/wiki/Ministry_of_Environment_and_Urban_Planning_(Turkey)"}]}, {"year": "1962", "text": "<PERSON>, American swimmer and water polo player (b. 1878)", "html": "1962 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, American swimmer and water polo player (b. 1878)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, American swimmer and water polo player (b. 1878)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1965", "text": "<PERSON> supreme court judge and briefly acting president (b. 1900)", "html": "1965 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>%C3%A1lez_L%C3%B3pez\" title=\"<PERSON>\"><PERSON></a> Guatemalan supreme court judge and briefly acting president (b. 1900)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>%C3%A1lez_L%C3%B3pez\" title=\"<PERSON>\"><PERSON></a> Guatemalan supreme court judge and briefly acting president (b. 1900)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>%C3%A1lez_L%C3%B3pez"}]}, {"year": "1968", "text": "<PERSON>, French pianist and composer (b. 1921)", "html": "1968 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French pianist and composer (b. 1921)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French pianist and composer (b. 1921)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1972", "text": "<PERSON>, American bass player (b. 1948)", "html": "1972 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>ley\"><PERSON></a>, American bass player (b. 1948)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Berry Oakley\"><PERSON></a>, American bass player (b. 1948)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1973", "text": "<PERSON><PERSON><PERSON>, Finnish chemist and academic, Nobel Prize laureate (b. 1895)", "html": "1973 - <a href=\"https://wikipedia.org/wiki/Artturi_Ilmari_Virtanen\" title=\"Artturi Ilmari Virtanen\"><PERSON><PERSON><PERSON></a>, Finnish chemist and academic, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Chemistry\" title=\"Nobel Prize in Chemistry\">Nobel Prize</a> laureate (b. 1895)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Artturi_Ilmari_Virtanen\" title=\"Artturi Ilmari Virtanen\"><PERSON><PERSON><PERSON></a>, Finnish chemist and academic, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Chemistry\" title=\"Nobel Prize in Chemistry\">Nobel Prize</a> laureate (b. 1895)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Artturi_<PERSON><PERSON><PERSON>_<PERSON>n"}, {"title": "Nobel Prize in Chemistry", "link": "https://wikipedia.org/wiki/Nobel_Prize_in_Chemistry"}]}, {"year": "1973", "text": "<PERSON>, German race car driver and journalist (b. 1922)", "html": "1973 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German race car driver and journalist (b. 1922)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German race car driver and journalist (b. 1922)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1974", "text": "<PERSON>, Chilean dentist, composer, and academic (b. 1894)", "html": "1974 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Chilean dentist, composer, and academic (b. 1894)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Chilean dentist, composer, and academic (b. 1894)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1976", "text": "<PERSON>, American sculptor (b. 1898)", "html": "1976 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American sculptor (b. 1898)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American sculptor (b. 1898)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1977", "text": "<PERSON>, Jr., Filipino journalist and activist (b. 1950)", "html": "1977 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Jr.\" class=\"mw-redirect\" title=\"<PERSON>, Jr.\"><PERSON>, Jr.</a>, Filipino journalist and activist (b. 1950)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>,_Jr.\" class=\"mw-redirect\" title=\"<PERSON>, Jr.\"><PERSON>, Jr.</a>, Filipino journalist and activist (b. 1950)", "links": [{"title": "<PERSON>, Jr.", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Jr."}]}, {"year": "1979", "text": "<PERSON>, Ukrainian-American composer and conductor (b. 1894)", "html": "1979 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Ukrainian-American composer and conductor (b. 1894)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Ukrainian-American composer and conductor (b. 1894)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1980", "text": "<PERSON>, Australian politician, 27th Premier of Queensland (b. 1901)", "html": "1980 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian politician, 27th <a href=\"https://wikipedia.org/wiki/Premier_of_Queensland\" title=\"Premier of Queensland\">Premier of Queensland</a> (b. 1901)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian politician, 27th <a href=\"https://wikipedia.org/wiki/Premier_of_Queensland\" title=\"Premier of Queensland\">Premier of Queensland</a> (b. 1901)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Premier of Queensland", "link": "https://wikipedia.org/wiki/Premier_of_Queensland"}]}, {"year": "1982", "text": "<PERSON>, French communist politician and Holocaust survivor (b. 1900)", "html": "1982 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French communist politician and Holocaust survivor (b. 1900)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French communist politician and Holocaust survivor (b. 1900)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1984", "text": "<PERSON>, Sr., American pastor, missionary, and activist (b. 1899)", "html": "1984 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>,_Sr.\" class=\"mw-redirect\" title=\"<PERSON>, Sr.\"><PERSON>, Sr.</a>, American pastor, missionary, and activist (b. 1899)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>,_Sr.\" class=\"mw-redirect\" title=\"<PERSON>, Sr.\"><PERSON>, Sr.</a>, American pastor, missionary, and activist (b. 1899)", "links": [{"title": "<PERSON>, Sr.", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>,_Sr."}]}, {"year": "1985", "text": "<PERSON><PERSON>, Swedish ice hockey player (b. 1959)", "html": "1985 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Swedish ice hockey player (b. 1959)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Swedish ice hockey player (b. 1959)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1985", "text": "<PERSON>, American photographer and educator (b. 1915)", "html": "1985 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American photographer and educator (b. 1915)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American photographer and educator (b. 1915)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1988", "text": "<PERSON>, South African-Australian colonel and politician (b. 1897)", "html": "1988 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, South African-Australian colonel and politician (b. 1897)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, South African-Australian colonel and politician (b. 1897)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Charles_<PERSON>_Wright_<PERSON>"}]}, {"year": "1988", "text": "<PERSON>, Welsh conductor and organist (b. 1900)", "html": "1988 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Welsh conductor and organist (b. 1900)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Welsh conductor and organist (b. 1900)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "1990", "text": "<PERSON><PERSON><PERSON>, Argentinian footballer (b. 1909)", "html": "1990 - <a href=\"https://wikipedia.org/wiki/Attilio_Demar%C3%ADa\" title=\"Attilio <PERSON>\"><PERSON><PERSON><PERSON></a>, Argentinian footballer (b. 1909)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Attilio_Demar%C3%ADa\" title=\"Attil<PERSON>\"><PERSON><PERSON><PERSON></a>, Argentinian footballer (b. 1909)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Attilio_Demar%C3%ADa"}]}, {"year": "1990", "text": "<PERSON><PERSON>, Turkish physician and politician, 17th Prime Minister of Turkey (b. 1904)", "html": "1990 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Turkish physician and politician, 17th <a href=\"https://wikipedia.org/wiki/List_of_Prime_Ministers_of_Turkey\" class=\"mw-redirect\" title=\"List of Prime Ministers of Turkey\">Prime Minister of Turkey</a> (b. 1904)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Turkish physician and politician, 17th <a href=\"https://wikipedia.org/wiki/List_of_Prime_Ministers_of_Turkey\" class=\"mw-redirect\" title=\"List of Prime Ministers of Turkey\">Prime Minister of Turkey</a> (b. 1904)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>k"}, {"title": "List of Prime Ministers of Turkey", "link": "https://wikipedia.org/wiki/List_of_Prime_Ministers_of_Turkey"}]}, {"year": "1990", "text": "<PERSON>, Greek actor and director (b. 1900)", "html": "1990 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Greek actor and director (b. 1900)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Greek actor and director (b. 1900)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1990", "text": "<PERSON><PERSON><PERSON>, Greek poet and playwright (b. 1909)", "html": "1990 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Greek poet and playwright (b. 1909)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Greek poet and playwright (b. 1909)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1993", "text": "<PERSON><PERSON><PERSON>, American trumpet player and bandleader (b. 1914)", "html": "1993 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American trumpet player and bandleader (b. 1914)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American trumpet player and bandleader (b. 1914)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1993", "text": "<PERSON>, American author and illustrator (b. 1914)", "html": "1993 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(cartoonist)\" title=\"<PERSON> (cartoonist)\"><PERSON></a>, American author and illustrator (b. 1914)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>(cartoonist)\" title=\"<PERSON> (cartoonist)\"><PERSON></a>, American author and illustrator (b. 1914)", "links": [{"title": "<PERSON> (cartoonist)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>(cartoonist)"}]}, {"year": "1994", "text": "<PERSON>, American soldier and politician, 61st Governor of Massachusetts (b. 1908)", "html": "1994 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American soldier and politician, 61st <a href=\"https://wikipedia.org/wiki/Governor_of_Massachusetts\" title=\"Governor of Massachusetts\">Governor of Massachusetts</a> (b. 1908)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American soldier and politician, 61st <a href=\"https://wikipedia.org/wiki/Governor_of_Massachusetts\" title=\"Governor of Massachusetts\">Governor of Massachusetts</a> (b. 1908)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>"}, {"title": "Governor of Massachusetts", "link": "https://wikipedia.org/wiki/Governor_of_Massachusetts"}]}, {"year": "1994", "text": "<PERSON><PERSON><PERSON>, Polish journalist, historian, and publicist (b. 1922)", "html": "1994 - <a href=\"https://wikipedia.org/wiki/Tad<PERSON><PERSON>_%C5%B<PERSON><PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Polish journalist, historian, and publicist (b. 1922)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_%C5%<PERSON><PERSON><PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Polish journalist, historian, and publicist (b. 1922)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Tad<PERSON><PERSON>_%C5%<PERSON><PERSON><PERSON><PERSON><PERSON>"}]}, {"year": "1997", "text": "<PERSON>, American hurdler and coach (b. 1950)", "html": "1997 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American hurdler and coach (b. 1950)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American hurdler and coach (b. 1950)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1997", "text": "<PERSON>, American film producer and writer (b. 1916)", "html": "1997 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American film producer and writer (b. 1916)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American film producer and writer (b. 1916)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1998", "text": "<PERSON>, American ice hockey player and soldier (b. 1913)", "html": "1998 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American ice hockey player and soldier (b. 1913)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American ice hockey player and soldier (b. 1913)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1998", "text": "<PERSON>, Irish singer and actor (b. 1922)", "html": "1998 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Irish singer and actor (b. 1922)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Irish singer and actor (b. 1922)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1999", "text": "<PERSON>, American voice actress (b. 1961)", "html": "1999 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American voice actress (b. 1961)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American voice actress (b. 1961)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1999", "text": "<PERSON><PERSON>, Argentinian journalist and author (b. 1923)", "html": "1999 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Argentinian journalist and author (b. 1923)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Argentinian journalist and author (b. 1923)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>rman"}]}, {"year": "2000", "text": "<PERSON>, German skier (b. 1981)", "html": "2000 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German skier (b. 1981)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German skier (b. 1981)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "2001", "text": "<PERSON><PERSON>, Estonian sculptor (b. 1920)", "html": "2001 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_Viitol\" title=\"<PERSON><PERSON> Viitol\"><PERSON><PERSON></a>, Estonian sculptor (b. 1920)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_Viitol\" title=\"<PERSON><PERSON>iitol\"><PERSON><PERSON></a>, Estonian sculptor (b. 1920)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Erna_Viitol"}]}, {"year": "2002", "text": "<PERSON>, South African neurologist, psychiatrist, and human rights activist (b. 1920)", "html": "2002 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, South African <a href=\"https://wikipedia.org/wiki/Neurologist\" class=\"mw-redirect\" title=\"Neurologist\">neurologist</a>, psychiatrist, and human rights activist (b. 1920)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, South African <a href=\"https://wikipedia.org/wiki/Neurologist\" class=\"mw-redirect\" title=\"Neurologist\">neurologist</a>, psychiatrist, and human rights activist (b. 1920)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Neurologist", "link": "https://wikipedia.org/wiki/Neurologist"}]}, {"year": "2003", "text": "<PERSON><PERSON>, Catalan poet (b. 1929)", "html": "2003 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>%C3%AD_i_Pol\" title=\"<PERSON><PERSON> i Pol\"><PERSON><PERSON></a>, Catalan poet (b. 1929)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>%C3%AD_i_Pol\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Catalan poet (b. 1929)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Miquel_Mart%C3%AD_i_Pol"}]}, {"year": "2004", "text": "<PERSON>, American comedian and voice actor (b. 1919)", "html": "2004 - <a href=\"https://wikipedia.org/wiki/<PERSON>_Allen\" title=\"<PERSON> Allen\"><PERSON></a>, American comedian and voice actor (b. 1919)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_Allen\" title=\"<PERSON> Allen\"><PERSON></a>, American comedian and voice actor (b. 1919)", "links": [{"title": "Dayton Allen", "link": "https://wikipedia.org/wiki/Dayton_Allen"}]}, {"year": "2004", "text": "<PERSON><PERSON>, Palestinian engineer and politician, 1st President of the Palestinian National Authority, Nobel Prize laureate (b. 1929)", "html": "2004 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Palestinian engineer and politician, 1st <a href=\"https://wikipedia.org/wiki/President_of_the_Palestinian_National_Authority\" title=\"President of the Palestinian National Authority\">President of the Palestinian National Authority</a>, <a href=\"https://wikipedia.org/wiki/Nobel_Peace_Prize\" title=\"Nobel Peace Prize\">Nobel Prize</a> laureate (b. 1929)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Palestinian engineer and politician, 1st <a href=\"https://wikipedia.org/wiki/President_of_the_Palestinian_National_Authority\" title=\"President of the Palestinian National Authority\">President of the Palestinian National Authority</a>, <a href=\"https://wikipedia.org/wiki/Nobel_Peace_Prize\" title=\"Nobel Peace Prize\">Nobel Prize</a> laureate (b. 1929)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}, {"title": "President of the Palestinian National Authority", "link": "https://wikipedia.org/wiki/President_of_the_Palestinian_National_Authority"}, {"title": "Nobel Peace Prize", "link": "https://wikipedia.org/wiki/Nobel_Peace_Prize"}]}, {"year": "2004", "text": "<PERSON>, French director and screenwriter (b. 1948)", "html": "2004 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French director and screenwriter (b. 1948)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French director and screenwriter (b. 1948)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2005", "text": "<PERSON><PERSON><PERSON><PERSON>, Syrian-American director and producer (b. 1930)", "html": "2005 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>kka<PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Syrian-American director and producer (b. 1930)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Syrian-American director and producer (b. 1930)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>ust<PERSON><PERSON>_<PERSON>d"}]}, {"year": "2005", "text": "<PERSON>, 5th Earl of Lichfield, English photographer (b. 1939)", "html": "2005 - <a href=\"https://wikipedia.org/wiki/<PERSON>,_5th_Earl_of_Lichfield\" title=\"<PERSON>, 5th Earl of Lichfield\"><PERSON>, 5th Earl of Lichfield</a>, English photographer (b. 1939)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>,_5th_Earl_of_Lichfield\" title=\"<PERSON>, 5th Earl of Lichfield\"><PERSON>, 5th Earl of Lichfield</a>, English photographer (b. 1939)", "links": [{"title": "<PERSON>, 5th Earl of Lichfield", "link": "https://wikipedia.org/wiki/<PERSON>,_5th_Earl_of_Lichfield"}]}, {"year": "2005", "text": "<PERSON>, Austrian-American author, theorist, and educator (b. 1909)", "html": "2005 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Austrian-American author, theorist, and educator (b. 1909)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Austrian-American author, theorist, and educator (b. 1909)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2006", "text": "<PERSON>, Australian actress (b. 1974)", "html": "2006 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian actress (b. 1974)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian actress (b. 1974)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2007", "text": "<PERSON><PERSON>, American director and producer (b. 1920)", "html": "2007 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American director and producer (b. 1920)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American director and producer (b. 1920)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "2008", "text": "<PERSON>, American baseball player and sportscaster (b. 1933)", "html": "2008 - <a href=\"https://wikipedia.org/wiki/<PERSON>_Score\" title=\"Herb Score\"><PERSON></a>, American baseball player and sportscaster (b. 1933)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_Score\" title=\"Herb Score\"><PERSON></a>, American baseball player and sportscaster (b. 1933)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_Score"}]}, {"year": "2008", "text": "<PERSON>, Turkish colonel (b. 1903)", "html": "2008 - <a href=\"https://wikipedia.org/wiki/<PERSON>_%C5%9Eekip_Birg%C3%B6l\" title=\"<PERSON>\"><PERSON></a>, Turkish colonel (b. 1903)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_%C5%9Eekip_Birg%C3%B6l\" title=\"<PERSON>\"><PERSON></a>, Turkish colonel (b. 1903)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Mustafa_%C5%9Eekip_Birg%C3%B6l"}]}, {"year": "2010", "text": "<PERSON>, American actress and costume designer (b. 1911)", "html": "2010 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress and costume designer (b. 1911)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress and costume designer (b. 1911)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "2011", "text": "<PERSON>, Mexican lawyer and politician, Mexican Secretary of the Interior (b. 1966)", "html": "2011 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Mexican lawyer and politician, <a href=\"https://wikipedia.org/wiki/Secretariat_of_the_Interior_(Mexico)\" class=\"mw-redirect\" title=\"Secretariat of the Interior (Mexico)\">Mexican Secretary of the Interior</a> (b. 1966)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Mexican lawyer and politician, <a href=\"https://wikipedia.org/wiki/Secretariat_of_the_Interior_(Mexico)\" class=\"mw-redirect\" title=\"Secretariat of the Interior (Mexico)\">Mexican Secretary of the Interior</a> (b. 1966)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "Secretariat of the Interior (Mexico)", "link": "https://wikipedia.org/wiki/Secretariat_of_the_Interior_(Mexico)"}]}, {"year": "2012", "text": "<PERSON>, Nigerian educator and politician, Governor of Oyo State (b. 1939)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Nigerian educator and politician, <a href=\"https://wikipedia.org/wiki/Governor_of_Oyo_State\" class=\"mw-redirect\" title=\"Governor of Oyo State\">Governor of Oyo State</a> (b. 1939)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Nigerian educator and politician, <a href=\"https://wikipedia.org/wiki/Governor_of_Oyo_State\" class=\"mw-redirect\" title=\"Governor of Oyo State\">Governor of Oyo State</a> (b. 1939)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Lam_<PERSON>na"}, {"title": "Governor of Oyo State", "link": "https://wikipedia.org/wiki/Governor_of_Oyo_State"}]}, {"year": "2012", "text": "<PERSON>, English rugby player and coach (b. 1919)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(rugby_league)\" title=\"<PERSON> (rugby league)\"><PERSON></a>, English rugby player and coach (b. 1919)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(rugby_league)\" title=\"<PERSON> (rugby league)\"><PERSON></a>, English rugby player and coach (b. 1919)", "links": [{"title": "<PERSON> (rugby league)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(rugby_league)"}]}, {"year": "2012", "text": "<PERSON>, English lieutenant, pilot, and diplomat, Governor of the Falkland Islands (b. 1926)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(governor)\" class=\"mw-redirect\" title=\"<PERSON> (governor)\"><PERSON></a>, English lieutenant, pilot, and diplomat, <a href=\"https://wikipedia.org/wiki/Governor_of_the_Falkland_Islands\" title=\"Governor of the Falkland Islands\">Governor of the Falkland Islands</a> (b. 1926)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(governor)\" class=\"mw-redirect\" title=\"<PERSON> (governor)\"><PERSON></a>, English lieutenant, pilot, and diplomat, <a href=\"https://wikipedia.org/wiki/Governor_of_the_Falkland_Islands\" title=\"Governor of the Falkland Islands\">Governor of the Falkland Islands</a> (b. 1926)", "links": [{"title": "<PERSON> (governor)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(governor)"}, {"title": "Governor of the Falkland Islands", "link": "https://wikipedia.org/wiki/Governor_of_the_Falkland_Islands"}]}, {"year": "2012", "text": "<PERSON>, Belgian footballer (b. 1927)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Belgian footballer (b. 1927)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Belgian footballer (b. 1927)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2012", "text": "<PERSON>, American photographer (b. 1915)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American photographer (b. 1915)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American photographer (b. 1915)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "2013", "text": "<PERSON>, American basketball player and coach (b. 1938)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(basketball)\" title=\"<PERSON> (basketball)\"><PERSON></a>, American basketball player and coach (b. 1938)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(basketball)\" title=\"<PERSON> (basketball)\"><PERSON></a>, American basketball player and coach (b. 1938)", "links": [{"title": "<PERSON> (basketball)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(basketball)"}]}, {"year": "2013", "text": "<PERSON>, Italian cardinal and composer (b. 1917)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian cardinal and composer (b. 1917)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian cardinal and composer (b. 1917)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2013", "text": "<PERSON>, American singer-songwriter (b. 1927)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter (b. 1927)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter (b. 1927)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2013", "text": "<PERSON>, American priest and theologian (b. 1929)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American priest and theologian (b. 1929)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American priest and theologian (b. 1929)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "2013", "text": "<PERSON><PERSON>, Turkish economist and politician, 33rd Deputy Prime Minister of Turkey (b. 1931)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/Atilla_Karaosmano%C4%9Flu\" title=\"Atilla Karaosmanoğlu\"><PERSON><PERSON></a>, Turkish economist and politician, 33rd <a href=\"https://wikipedia.org/wiki/Deputy_Prime_Minister_of_Turkey\" title=\"Deputy Prime Minister of Turkey\">Deputy Prime Minister of Turkey</a> (b. 1931)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Atilla_Karaosmano%C4%9Flu\" title=\"Atilla Karaosmanoğlu\"><PERSON><PERSON></a>, Turkish economist and politician, 33rd <a href=\"https://wikipedia.org/wiki/Deputy_Prime_Minister_of_Turkey\" title=\"Deputy Prime Minister of Turkey\">Deputy Prime Minister of Turkey</a> (b. 1931)", "links": [{"title": "Atilla Karaosmanoğlu", "link": "https://wikipedia.org/wiki/Atilla_Karaosmano%C4%9Flu"}, {"title": "Deputy Prime Minister of Turkey", "link": "https://wikipedia.org/wiki/Deputy_Prime_Minister_of_Turkey"}]}, {"year": "2014", "text": "<PERSON>, American lawyer and activist (b. 1921)", "html": "2014 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and activist (b. 1921)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and activist (b. 1921)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2014", "text": "<PERSON> <PERSON>, American rapper (b. 1956)", "html": "2014 - <a href=\"https://wikipedia.org/wiki/Big_Bank_Hank\" title=\"Big Bank Hank\"><PERSON> Bank Hank</a>, American rapper (b. 1956)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Big_Bank_Hank\" title=\"Big Bank Hank\">Big Bank Hank</a>, American rapper (b. 1956)", "links": [{"title": "Big Bank Hank", "link": "https://wikipedia.org/wiki/Big_Bank_Hank"}]}, {"year": "2014", "text": "<PERSON>, American engineer and academic (b. 1920)", "html": "2014 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American engineer and academic (b. 1920)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American engineer and academic (b. 1920)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "2014", "text": "<PERSON>, American chemist, businessman, and politician (b. 1932)", "html": "2014 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American chemist, businessman, and politician (b. 1932)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American chemist, businessman, and politician (b. 1932)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2014", "text": "<PERSON>, American actress (b. 1952)", "html": "2014 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress (b. 1952)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress (b. 1952)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "2015", "text": "<PERSON>, American theologian and author (b. 1943)", "html": "2015 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American theologian and author (b. 1943)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American theologian and author (b. 1943)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2015", "text": "<PERSON>, American actor and producer (b. 1975)", "html": "2015 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor and producer (b. 1975)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor and producer (b. 1975)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2016", "text": "<PERSON>, American singer and bass player (b. 1960)", "html": "2016 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(musician)\" title=\"<PERSON> (musician)\"><PERSON></a>, American singer and bass player (b. 1960)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(musician)\" title=\"<PERSON> (musician)\"><PERSON></a>, American singer and bass player (b. 1960)", "links": [{"title": "<PERSON> (musician)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(musician)"}]}, {"year": "2016", "text": "<PERSON>, American actor (b. 1932)", "html": "2016 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor (b. 1932)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor (b. 1932)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2017", "text": "<PERSON><PERSON>, Spanish singer, actor and comedian (b. 1932)", "html": "2017 - <a href=\"https://wikipedia.org/wiki/Chi<PERSON>_de_la_Calzada\" title=\"Chiquito de la Calzada\"><PERSON><PERSON> <PERSON> la Calzada</a>, Spanish singer, actor and comedian (b. 1932)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Chiquito_de_la_Calzada\" title=\"Chiquito de la Calzada\"><PERSON><PERSON> <PERSON> la Calzada</a>, Spanish singer, actor and comedian (b. 1932)", "links": [{"title": "Chiquito de la Calzada", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_la_Calzada"}]}, {"year": "2021", "text": "<PERSON><PERSON> <PERSON><PERSON>, South African lawyer and politician, former State President of South Africa, Nobel Prize laureate (b. 1936)", "html": "2021 - <a href=\"https://wikipedia.org/wiki/F<PERSON>_<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON> <PERSON><PERSON>\"><PERSON><PERSON> <PERSON><PERSON></a>, South African lawyer and politician, former <a href=\"https://wikipedia.org/wiki/State_President_of_South_Africa\" title=\"State President of South Africa\">State President of South Africa</a>, <a href=\"https://wikipedia.org/wiki/Nobel_Peace_Prize\" title=\"Nobel Peace Prize\">Nobel Prize</a> laureate (b. 1936)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/F<PERSON>_<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON> <PERSON><PERSON>\"><PERSON><PERSON> <PERSON><PERSON></a>, South African lawyer and politician, former <a href=\"https://wikipedia.org/wiki/State_President_of_South_Africa\" title=\"State President of South Africa\">State President of South Africa</a>, <a href=\"https://wikipedia.org/wiki/Nobel_Peace_Prize\" title=\"Nobel Peace Prize\">Nobel Prize</a> laureate (b. 1936)", "links": [{"title": "<PERSON><PERSON> <PERSON><PERSON>", "link": "https://wikipedia.org/wiki/F<PERSON>_<PERSON><PERSON>_<PERSON>_<PERSON>"}, {"title": "State President of South Africa", "link": "https://wikipedia.org/wiki/State_President_of_South_Africa"}, {"title": "Nobel Peace Prize", "link": "https://wikipedia.org/wiki/Nobel_Peace_Prize"}]}, {"year": "2024", "text": "<PERSON>, German-British painter (b. 1931)", "html": "2024 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German-British painter (b. 1931)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German-British painter (b. 1931)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2024", "text": "<PERSON>, American football player and coach (b. 1935)", "html": "2024 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(American_football)\" title=\"<PERSON> (American football)\"><PERSON></a>, American football player and coach (b. 1935)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(American_football)\" title=\"<PERSON> (American football)\"><PERSON></a>, American football player and coach (b. 1935)", "links": [{"title": "<PERSON> (American football)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(American_football)"}]}]}}