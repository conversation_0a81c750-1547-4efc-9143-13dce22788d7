{"date": "July 3", "url": "https://wikipedia.org/wiki/July_3", "data": {"Events": [{"year": "324", "text": "Battle of Adrianople: <PERSON> I defeats <PERSON><PERSON><PERSON>, who flees to Byzantium.", "html": "324 - <a href=\"https://wikipedia.org/wiki/Battle_of_Adrianople_(324)\" title=\"Battle of Adrianople (324)\">Battle of Adrianople</a>: <a href=\"https://wikipedia.org/wiki/<PERSON>_the_Great\" title=\"<PERSON> the Great\"><PERSON> I</a> defeats <a href=\"https://wikipedia.org/wiki/Licinius\" title=\"Licinius\"><PERSON><PERSON><PERSON></a>, who flees to <a href=\"https://wikipedia.org/wiki/Byzantium\" title=\"Byzantium\">Byzantium</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Battle_of_Adrianople_(324)\" title=\"Battle of Adrianople (324)\">Battle of Adrianople</a>: <a href=\"https://wikipedia.org/wiki/<PERSON>_the_Great\" title=\"<PERSON> the Great\"><PERSON> I</a> defeats <a href=\"https://wikipedia.org/wiki/Licinius\" title=\"<PERSON>cinius\"><PERSON><PERSON><PERSON></a>, who flees to <a href=\"https://wikipedia.org/wiki/Byzantium\" title=\"Byzantium\">Byzantium</a>.", "links": [{"title": "Battle of Adrianople (324)", "link": "https://wikipedia.org/wiki/Battle_of_Adrian<PERSON>le_(324)"}, {"title": "<PERSON> the Great", "link": "https://wikipedia.org/wiki/<PERSON>_the_Great"}, {"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Licinius"}, {"title": "Byzantium", "link": "https://wikipedia.org/wiki/Byzantium"}]}, {"year": "987", "text": "<PERSON> is crowned King of France, the first of the Capetian dynasty that would rule France until the French Revolution in 1792.", "html": "987 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> is crowned King of France, the first of the <a href=\"https://wikipedia.org/wiki/Capetian_dynasty\" title=\"Capetian dynasty\">Capetian dynasty</a> that would rule France until the <a href=\"https://wikipedia.org/wiki/French_Revolution\" title=\"French Revolution\">French Revolution</a> in <a href=\"https://wikipedia.org/wiki/1792\" title=\"1792\">1792</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> is crowned King of France, the first of the <a href=\"https://wikipedia.org/wiki/Capetian_dynasty\" title=\"Capetian dynasty\">Capetian dynasty</a> that would rule France until the <a href=\"https://wikipedia.org/wiki/French_Revolution\" title=\"French Revolution\">French Revolution</a> in <a href=\"https://wikipedia.org/wiki/1792\" title=\"1792\">1792</a>.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Capetian dynasty", "link": "https://wikipedia.org/wiki/Capetian_dynasty"}, {"title": "French Revolution", "link": "https://wikipedia.org/wiki/French_Revolution"}, {"title": "1792", "link": "https://wikipedia.org/wiki/1792"}]}, {"year": "1035", "text": "<PERSON> the Conqueror becomes the Duke of Normandy, reigning until 1087.", "html": "1035 - <a href=\"https://wikipedia.org/wiki/<PERSON>_the_Conqueror\" title=\"<PERSON> the Conqueror\"><PERSON> the Conqueror</a> becomes the <a href=\"https://wikipedia.org/wiki/Duke_of_Normandy\" title=\"Duke of Normandy\">Duke of Normandy</a>, reigning until 1087.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_the_Conqueror\" title=\"<PERSON> the Conqueror\"><PERSON> the Conqueror</a> becomes the <a href=\"https://wikipedia.org/wiki/Duke_of_Normandy\" title=\"Duke of Normandy\">Duke of Normandy</a>, reigning until 1087.", "links": [{"title": "<PERSON> the Conqueror", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "Duke of Normandy", "link": "https://wikipedia.org/wiki/Duke_of_Normandy"}]}, {"year": "1608", "text": "Québec City is founded by <PERSON>.", "html": "1608 - <a href=\"https://wikipedia.org/wiki/Qu%C3%A9bec_City\" class=\"mw-redirect\" title=\"Québec City\">Québec City</a> is founded by <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Qu%C3%A9bec_City\" class=\"mw-redirect\" title=\"Québec City\">Québec City</a> is founded by <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>.", "links": [{"title": "Québec City", "link": "https://wikipedia.org/wiki/Qu%C3%A9bec_City"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1754", "text": "French and Indian War: <PERSON> surrenders Fort Necessity to French forces.", "html": "1754 - <a href=\"https://wikipedia.org/wiki/French_and_Indian_War\" title=\"French and Indian War\">French and Indian War</a>: <a href=\"https://wikipedia.org/wiki/George_Washington\" title=\"George Washington\"><PERSON></a> <a href=\"https://wikipedia.org/wiki/Battle_of_Fort_Necessity\" title=\"Battle of Fort Necessity\">surrenders Fort Necessity</a> to <a href=\"https://wikipedia.org/wiki/Military_history_of_France\" title=\"Military history of France\">French forces</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/French_and_Indian_War\" title=\"French and Indian War\">French and Indian War</a>: <a href=\"https://wikipedia.org/wiki/George_Washington\" title=\"George Washington\"><PERSON></a> <a href=\"https://wikipedia.org/wiki/Battle_of_Fort_Necessity\" title=\"Battle of Fort Necessity\">surrenders Fort Necessity</a> to <a href=\"https://wikipedia.org/wiki/Military_history_of_France\" title=\"Military history of France\">French forces</a>.", "links": [{"title": "French and Indian War", "link": "https://wikipedia.org/wiki/French_and_Indian_War"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/George_Washington"}, {"title": "Battle of Fort Necessity", "link": "https://wikipedia.org/wiki/Battle_of_Fort_Necessity"}, {"title": "Military history of France", "link": "https://wikipedia.org/wiki/Military_history_of_France"}]}, {"year": "1767", "text": "Pitcairn Island is discovered by Midshipman <PERSON> on an expeditionary voyage commanded by <PERSON>.", "html": "1767 - <a href=\"https://wikipedia.org/wiki/Pitcairn_Island\" title=\"Pitcairn Island\">Pitcairn Island</a> is discovered by <a href=\"https://wikipedia.org/wiki/Midshipman\" title=\"Midshipman\">Midshipman</a> <PERSON> on an expeditionary voyage commanded by <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Pitcairn_Island\" title=\"Pitcairn Island\">Pitcairn Island</a> is discovered by <a href=\"https://wikipedia.org/wiki/Midshipman\" title=\"Midshipman\">Midshipman</a> <PERSON> on an expeditionary voyage commanded by <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>.", "links": [{"title": "Pitcairn Island", "link": "https://wikipedia.org/wiki/Pitcairn_Island"}, {"title": "Midshipman", "link": "https://wikipedia.org/wiki/Midshipman"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1767", "text": "Norway's oldest newspaper still in print, Adresseavisen, is founded and the first edition is published.", "html": "1767 - Norway's oldest newspaper still in print, <i><a href=\"https://wikipedia.org/wiki/Adresseavisen\" title=\"Adresseavisen\">Adresseavisen</a></i>, is founded and the first edition is published.", "no_year_html": "Norway's oldest newspaper still in print, <i><a href=\"https://wikipedia.org/wiki/Adresseavisen\" title=\"Adresseavisen\">Adresseavisen</a></i>, is founded and the first edition is published.", "links": [{"title": "Adresseavisen", "link": "https://wikipedia.org/wiki/Adresseavisen"}]}, {"year": "1775", "text": "American Revolutionary War: <PERSON> takes command of the Continental Army at Cambridge, Massachusetts.", "html": "1775 - <a href=\"https://wikipedia.org/wiki/American_Revolutionary_War\" title=\"American Revolutionary War\">American Revolutionary War</a>: <a href=\"https://wikipedia.org/wiki/George_Washington\" title=\"George Washington\"><PERSON></a> takes command of the <a href=\"https://wikipedia.org/wiki/Continental_Army\" title=\"Continental Army\">Continental Army</a> at <a href=\"https://wikipedia.org/wiki/Cambridge,_Massachusetts\" title=\"Cambridge, Massachusetts\">Cambridge, Massachusetts</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/American_Revolutionary_War\" title=\"American Revolutionary War\">American Revolutionary War</a>: <a href=\"https://wikipedia.org/wiki/George_Washington\" title=\"George Washington\"><PERSON></a> takes command of the <a href=\"https://wikipedia.org/wiki/Continental_Army\" title=\"Continental Army\">Continental Army</a> at <a href=\"https://wikipedia.org/wiki/Cambridge,_Massachusetts\" title=\"Cambridge, Massachusetts\">Cambridge, Massachusetts</a>.", "links": [{"title": "American Revolutionary War", "link": "https://wikipedia.org/wiki/American_Revolutionary_War"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/George_Washington"}, {"title": "Continental Army", "link": "https://wikipedia.org/wiki/Continental_Army"}, {"title": "Cambridge, Massachusetts", "link": "https://wikipedia.org/wiki/Cambridge,_Massachusetts"}]}, {"year": "1778", "text": "American Revolutionary War: The Iroquois, allied with Britain, killed 360 people in the Wyoming Valley massacre.", "html": "1778 - American Revolutionary War: The Iroquois, allied with Britain, killed 360 people in the <a href=\"https://wikipedia.org/wiki/Wyoming_Valley_massacre\" class=\"mw-redirect\" title=\"Wyoming Valley massacre\">Wyoming Valley massacre</a>.", "no_year_html": "American Revolutionary War: The Iroquois, allied with Britain, killed 360 people in the <a href=\"https://wikipedia.org/wiki/Wyoming_Valley_massacre\" class=\"mw-redirect\" title=\"Wyoming Valley massacre\">Wyoming Valley massacre</a>.", "links": [{"title": "Wyoming Valley massacre", "link": "https://wikipedia.org/wiki/Wyoming_Valley_massacre"}]}, {"year": "1819", "text": "The Bank for Savings in the City of New-York, the first savings bank in the United States, opens.", "html": "1819 - <a href=\"https://wikipedia.org/wiki/The_Bank_for_Savings_in_the_City_of_New-York\" class=\"mw-redirect\" title=\"The Bank for Savings in the City of New-York\">The Bank for Savings in the City of New-York</a>, the first savings bank in the United States, opens.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/The_Bank_for_Savings_in_the_City_of_New-York\" class=\"mw-redirect\" title=\"The Bank for Savings in the City of New-York\">The Bank for Savings in the City of New-York</a>, the first savings bank in the United States, opens.", "links": [{"title": "The Bank for Savings in the City of New-York", "link": "https://wikipedia.org/wiki/The_Bank_for_Savings_in_the_City_of_New-York"}]}, {"year": "1839", "text": "The first state normal school in the United States, the forerunner to today's Framingham State University, opens in Lexington, Massachusetts with three students.", "html": "1839 - The first state <a href=\"https://wikipedia.org/wiki/Normal_school\" title=\"Normal school\">normal school</a> in the United States, the forerunner to today's <a href=\"https://wikipedia.org/wiki/Framingham_State_University\" title=\"Framingham State University\">Framingham State University</a>, opens in <a href=\"https://wikipedia.org/wiki/Lexington,_Massachusetts\" title=\"Lexington, Massachusetts\">Lexington, Massachusetts</a> with three students.", "no_year_html": "The first state <a href=\"https://wikipedia.org/wiki/Normal_school\" title=\"Normal school\">normal school</a> in the United States, the forerunner to today's <a href=\"https://wikipedia.org/wiki/Framingham_State_University\" title=\"Framingham State University\">Framingham State University</a>, opens in <a href=\"https://wikipedia.org/wiki/Lexington,_Massachusetts\" title=\"Lexington, Massachusetts\">Lexington, Massachusetts</a> with three students.", "links": [{"title": "Normal school", "link": "https://wikipedia.org/wiki/Normal_school"}, {"title": "Framingham State University", "link": "https://wikipedia.org/wiki/Framingham_State_University"}, {"title": "Lexington, Massachusetts", "link": "https://wikipedia.org/wiki/Lexington,_Massachusetts"}]}, {"year": "1848", "text": "Governor-General <PERSON> emancipates all remaining slaves in the Danish West Indies.", "html": "1848 - Governor-General <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> emancipates all remaining slaves in the Danish West Indies.", "no_year_html": "Governor-General <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> emancipates all remaining slaves in the Danish West Indies.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1849", "text": "France invades the Roman Republic and restores the Papal States.", "html": "1849 - France invades the <a href=\"https://wikipedia.org/wiki/Roman_Republic_(1849)\" class=\"mw-redirect\" title=\"Roman Republic (1849)\">Roman Republic</a> and restores the <a href=\"https://wikipedia.org/wiki/Papal_States\" title=\"Papal States\">Papal States</a>.", "no_year_html": "France invades the <a href=\"https://wikipedia.org/wiki/Roman_Republic_(1849)\" class=\"mw-redirect\" title=\"Roman Republic (1849)\">Roman Republic</a> and restores the <a href=\"https://wikipedia.org/wiki/Papal_States\" title=\"Papal States\">Papal States</a>.", "links": [{"title": "Roman Republic (1849)", "link": "https://wikipedia.org/wiki/Roman_Republic_(1849)"}, {"title": "Papal States", "link": "https://wikipedia.org/wiki/Papal_States"}]}, {"year": "1852", "text": "Congress establishes the United States' 2nd mint in San Francisco.", "html": "1852 - Congress establishes the <a href=\"https://wikipedia.org/wiki/San_Francisco_Mint\" title=\"San Francisco Mint\">United States' 2nd mint</a> in <a href=\"https://wikipedia.org/wiki/San_Francisco\" title=\"San Francisco\">San Francisco</a>.", "no_year_html": "Congress establishes the <a href=\"https://wikipedia.org/wiki/San_Francisco_Mint\" title=\"San Francisco Mint\">United States' 2nd mint</a> in <a href=\"https://wikipedia.org/wiki/San_Francisco\" title=\"San Francisco\">San Francisco</a>.", "links": [{"title": "San Francisco Mint", "link": "https://wikipedia.org/wiki/San_Francisco_Mint"}, {"title": "San Francisco", "link": "https://wikipedia.org/wiki/San_Francisco"}]}, {"year": "1863", "text": "American Civil War: The final day of the Battle of Gettysburg culminates with <PERSON><PERSON>'s Charge.", "html": "1863 - <a href=\"https://wikipedia.org/wiki/American_Civil_War\" title=\"American Civil War\">American Civil War</a>: The final day of the <a href=\"https://wikipedia.org/wiki/Battle_of_Gettysburg\" title=\"Battle of Gettysburg\">Battle of Gettysburg</a> culminates with <a href=\"https://wikipedia.org/wiki/Pickett%27s_Charge\" title=\"<PERSON><PERSON>'s Charge\"><PERSON><PERSON>'s Charge</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/American_Civil_War\" title=\"American Civil War\">American Civil War</a>: The final day of the <a href=\"https://wikipedia.org/wiki/Battle_of_Gettysburg\" title=\"Battle of Gettysburg\">Battle of Gettysburg</a> culminates with <a href=\"https://wikipedia.org/wiki/<PERSON>ett%27s_Charge\" title=\"<PERSON><PERSON>'s Charge\"><PERSON><PERSON>'s Charge</a>.", "links": [{"title": "American Civil War", "link": "https://wikipedia.org/wiki/American_Civil_War"}, {"title": "Battle of Gettysburg", "link": "https://wikipedia.org/wiki/Battle_of_Gettysburg"}, {"title": "<PERSON><PERSON>'s Charge", "link": "https://wikipedia.org/wiki/Pickett%27s_Charge"}]}, {"year": "1866", "text": "Austro-Prussian War is decided at the Battle of Königgrätz, enabling Prussia to exclude Austria from German affairs.", "html": "1866 - <a href=\"https://wikipedia.org/wiki/Austro-Prussian_War\" title=\"Austro-Prussian War\">Austro-Prussian War</a> is decided at the <a href=\"https://wikipedia.org/wiki/Battle_of_K%C3%B6niggr%C3%A4tz\" title=\"Battle of Königgrätz\">Battle of Königgrätz</a>, enabling Prussia to exclude Austria from German affairs.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Austro-Prussian_War\" title=\"Austro-Prussian War\">Austro-Prussian War</a> is decided at the <a href=\"https://wikipedia.org/wiki/Battle_of_K%C3%B6niggr%C3%A4tz\" title=\"Battle of Königgrätz\">Battle of Königgrätz</a>, enabling Prussia to exclude Austria from German affairs.", "links": [{"title": "Austro-Prussian War", "link": "https://wikipedia.org/wiki/Austro-Prussian_War"}, {"title": "Battle of Königgrätz", "link": "https://wikipedia.org/wiki/Battle_of_K%C3%B6niggr%C3%A4tz"}]}, {"year": "1884", "text": "Dow Jones & Company publishes its first stock average.", "html": "1884 - <a href=\"https://wikipedia.org/wiki/Dow_Jones_%26_Company\" title=\"Dow Jones &amp; Company\">Dow Jones &amp; Company</a> publishes its first stock average.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Dow_Jones_%26_Company\" title=\"Dow Jones &amp; Company\">Dow Jones &amp; Company</a> publishes its first stock average.", "links": [{"title": "Dow Jones & Company", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_%26_Company"}]}, {"year": "1886", "text": "Karl Benz officially unveils the Benz Patent-Motorwagen, the first purpose-built automobile.", "html": "1886 - <a href=\"https://wikipedia.org/wiki/Karl_Benz\" class=\"mw-redirect\" title=\"Karl Benz\"><PERSON></a> officially unveils the <a href=\"https://wikipedia.org/wiki/Benz_Patent-Motorwagen\" title=\"Benz Patent-Motorwagen\">Benz Patent-Motorwagen</a>, the first purpose-built automobile.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Karl_Benz\" class=\"mw-redirect\" title=\"Karl Benz\"><PERSON></a> officially unveils the <a href=\"https://wikipedia.org/wiki/Benz_Patent-Motorwagen\" title=\"Benz Patent-Motorwagen\">Benz Patent-Motorwagen</a>, the first purpose-built automobile.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Benz Patent-Motorwagen", "link": "https://wikipedia.org/wiki/Benz_Patent-Motorwagen"}]}, {"year": "1886", "text": "The New-York Tribune becomes the first newspaper to use a linotype machine, eliminating typesetting by hand.", "html": "1886 - The <i><a href=\"https://wikipedia.org/wiki/New-York_Tribune\" title=\"New-York Tribune\">New-York Tribune</a></i> becomes the first newspaper to use a <a href=\"https://wikipedia.org/wiki/Linotype_machine\" title=\"Linotype machine\">linotype machine</a>, eliminating <a href=\"https://wikipedia.org/wiki/Typesetting\" title=\"Typesetting\">typesetting</a> by hand.", "no_year_html": "The <i><a href=\"https://wikipedia.org/wiki/New-York_Tribune\" title=\"New-York Tribune\">New-York Tribune</a></i> becomes the first newspaper to use a <a href=\"https://wikipedia.org/wiki/Linotype_machine\" title=\"Linotype machine\">linotype machine</a>, eliminating <a href=\"https://wikipedia.org/wiki/Typesetting\" title=\"Typesetting\">typesetting</a> by hand.", "links": [{"title": "New-York Tribune", "link": "https://wikipedia.org/wiki/New-York_Tribune"}, {"title": "Linotype machine", "link": "https://wikipedia.org/wiki/Linotype_machine"}, {"title": "Typesetting", "link": "https://wikipedia.org/wiki/Typesetting"}]}, {"year": "1890", "text": "Idaho is admitted as the 43rd U.S. state.", "html": "1890 - <a href=\"https://wikipedia.org/wiki/Idaho\" title=\"Idaho\">Idaho</a> is admitted as the 43rd <a href=\"https://wikipedia.org/wiki/U.S._state\" title=\"U.S. state\">U.S. state</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Idaho\" title=\"Idaho\">Idaho</a> is admitted as the 43rd <a href=\"https://wikipedia.org/wiki/U.S._state\" title=\"U.S. state\">U.S. state</a>.", "links": [{"title": "Idaho", "link": "https://wikipedia.org/wiki/Idaho"}, {"title": "U.S. state", "link": "https://wikipedia.org/wiki/U.S._state"}]}, {"year": "1898", "text": "A Spanish squadron, led by <PERSON><PERSON><PERSON><PERSON>, is defeated by an American squadron under <PERSON> in the Battle of Santiago de Cuba.", "html": "1898 - A Spanish squadron, led by <a href=\"https://wikipedia.org/wiki/Pascual_Cervera_y_Topete\" title=\"Pascual Cervera y Topete\">Pascual Cervera y Topete</a>, is defeated by an American squadron under <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> in the <a href=\"https://wikipedia.org/wiki/Battle_of_Santiago_de_Cuba\" title=\"Battle of Santiago de Cuba\">Battle of Santiago de Cuba</a>.", "no_year_html": "A Spanish squadron, led by <a href=\"https://wikipedia.org/wiki/Pascual_Cervera_y_Topete\" title=\"Pascual Cervera y Topete\">Pascual Cervera y Topete</a>, is defeated by an American squadron under <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> in the <a href=\"https://wikipedia.org/wiki/Battle_of_Santiago_de_Cuba\" title=\"Battle of Santiago de Cuba\">Battle of Santiago de Cuba</a>.", "links": [{"title": "Pascual Cervera y Topete", "link": "https://wikipedia.org/wiki/Pas<PERSON>al_Cervera_y_Topete"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>"}, {"title": "Battle of Santiago de Cuba", "link": "https://wikipedia.org/wiki/Battle_of_Santiago_de_Cuba"}]}, {"year": "1913", "text": "Confederate veterans at the Great Reunion of 1913 reenact <PERSON><PERSON>'s Charge; upon reaching the high-water mark of the Confederacy they are met by the outstretched hands of friendship from Union survivors.", "html": "1913 - <a href=\"https://wikipedia.org/wiki/Confederate_States_of_America\" title=\"Confederate States of America\">Confederate</a> veterans at the <a href=\"https://wikipedia.org/wiki/Great_Reunion_of_1913\" class=\"mw-redirect\" title=\"Great Reunion of 1913\">Great Reunion of 1913</a> <a href=\"https://wikipedia.org/wiki/American_Civil_War_reenactment\" title=\"American Civil War reenactment\">reenact</a> <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>%27s_Charge\" title=\"<PERSON><PERSON>'s Charge\"><PERSON><PERSON>'s Charge</a>; upon reaching the <a href=\"https://wikipedia.org/wiki/High-water_mark_of_the_Confederacy\" title=\"High-water mark of the Confederacy\">high-water mark of the Confederacy</a> they are met by the outstretched hands of friendship from <a href=\"https://wikipedia.org/wiki/Union_(American_Civil_War)\" title=\"Union (American Civil War)\">Union</a> survivors.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Confederate_States_of_America\" title=\"Confederate States of America\">Confederate</a> veterans at the <a href=\"https://wikipedia.org/wiki/Great_Reunion_of_1913\" class=\"mw-redirect\" title=\"Great Reunion of 1913\">Great Reunion of 1913</a> <a href=\"https://wikipedia.org/wiki/American_Civil_War_reenactment\" title=\"American Civil War reenactment\">reenact</a> <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>%27s_Charge\" title=\"<PERSON><PERSON>'s Charge\"><PERSON><PERSON>'s Charge</a>; upon reaching the <a href=\"https://wikipedia.org/wiki/High-water_mark_of_the_Confederacy\" title=\"High-water mark of the Confederacy\">high-water mark of the Confederacy</a> they are met by the outstretched hands of friendship from <a href=\"https://wikipedia.org/wiki/Union_(American_Civil_War)\" title=\"Union (American Civil War)\">Union</a> survivors.", "links": [{"title": "Confederate States of America", "link": "https://wikipedia.org/wiki/Confederate_States_of_America"}, {"title": "Great Reunion of 1913", "link": "https://wikipedia.org/wiki/Great_Reunion_of_1913"}, {"title": "American Civil War reenactment", "link": "https://wikipedia.org/wiki/American_Civil_War_reenactment"}, {"title": "<PERSON><PERSON>'s Charge", "link": "https://wikipedia.org/wiki/Pickett%27s_Charge"}, {"title": "High-water mark of the Confederacy", "link": "https://wikipedia.org/wiki/High-water_mark_of_the_Confederacy"}, {"title": "Union (American Civil War)", "link": "https://wikipedia.org/wiki/Union_(American_Civil_War)"}]}, {"year": "1938", "text": "World speed record for a steam locomotive is set in England, by the Mallard, which reaches a speed of 125.88 miles per hour (202.58 km/h).", "html": "1938 - World speed record for a <a href=\"https://wikipedia.org/wiki/Steam_locomotive\" title=\"Steam locomotive\">steam locomotive</a> is set in England, by the <i><a href=\"https://wikipedia.org/wiki/LNER_Class_A4_4468_Mallard\" title=\"LNER Class A4 4468 Mallard\">Mallard</a></i>, which reaches a speed of 125.88 miles per hour (202.58 km/h).", "no_year_html": "World speed record for a <a href=\"https://wikipedia.org/wiki/Steam_locomotive\" title=\"Steam locomotive\">steam locomotive</a> is set in England, by the <i><a href=\"https://wikipedia.org/wiki/LNER_Class_A4_4468_Mallard\" title=\"LNER Class A4 4468 Mallard\">Mallard</a></i>, which reaches a speed of 125.88 miles per hour (202.58 km/h).", "links": [{"title": "Steam locomotive", "link": "https://wikipedia.org/wiki/Steam_locomotive"}, {"title": "LNER Class A4 4468 Mallard", "link": "https://wikipedia.org/wiki/LNER_Class_A4_4468_Mallard"}]}, {"year": "1938", "text": "United States President <PERSON> dedicates the Eternal Light Peace Memorial and lights the eternal flame at Gettysburg Battlefield.", "html": "1938 - United States President <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> dedicates the <a href=\"https://wikipedia.org/wiki/Eternal_Light_Peace_Memorial\" title=\"Eternal Light Peace Memorial\">Eternal Light Peace Memorial</a> and lights the eternal flame at <a href=\"https://wikipedia.org/wiki/Gettysburg_Battlefield\" title=\"Gettysburg Battlefield\">Gettysburg Battlefield</a>.", "no_year_html": "United States President <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> dedicates the <a href=\"https://wikipedia.org/wiki/Eternal_Light_Peace_Memorial\" title=\"Eternal Light Peace Memorial\">Eternal Light Peace Memorial</a> and lights the eternal flame at <a href=\"https://wikipedia.org/wiki/Gettysburg_Battlefield\" title=\"Gettysburg Battlefield\">Gettysburg Battlefield</a>.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "Eternal Light Peace Memorial", "link": "https://wikipedia.org/wiki/Eternal_Light_Peace_Memorial"}, {"title": "Gettysburg Battlefield", "link": "https://wikipedia.org/wiki/Gettysburg_Battlefield"}]}, {"year": "1940", "text": "World War II: The Royal Navy attacks the French naval squadron in Algeria, to ensure that it will not fall under German control. Of the four French battleships present, one is sunk, two are damaged, and one escapes back to France.", "html": "1940 - <a href=\"https://wikipedia.org/wiki/World_War_II\" title=\"World War II\">World War II</a>: The Royal Navy <a href=\"https://wikipedia.org/wiki/Attack_on_Mers-el-K%C3%A9bir\" title=\"Attack on Mers-el-Kébir\">attacks the French naval squadron</a> in Algeria, to ensure that it will not fall under German control. Of the four French battleships present, one is sunk, two are damaged, and one escapes back to France.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/World_War_II\" title=\"World War II\">World War II</a>: The Royal Navy <a href=\"https://wikipedia.org/wiki/Attack_on_Mers-el-K%C3%A9bir\" title=\"Attack on Mers-el-Kébir\">attacks the French naval squadron</a> in Algeria, to ensure that it will not fall under German control. Of the four French battleships present, one is sunk, two are damaged, and one escapes back to France.", "links": [{"title": "World War II", "link": "https://wikipedia.org/wiki/World_War_II"}, {"title": "Attack on Mers-el-Kébir", "link": "https://wikipedia.org/wiki/Attack_on_Mers-el-K%C3%A9bir"}]}, {"year": "1944", "text": "World War II: The Minsk Offensive clears German troops from the city.", "html": "1944 - World War II: The <a href=\"https://wikipedia.org/wiki/Minsk_Offensive\" class=\"mw-redirect\" title=\"Minsk Offensive\">Minsk Offensive</a> clears German troops from the city.", "no_year_html": "World War II: The <a href=\"https://wikipedia.org/wiki/Minsk_Offensive\" class=\"mw-redirect\" title=\"Minsk Offensive\">Minsk Offensive</a> clears German troops from the city.", "links": [{"title": "Minsk Offensive", "link": "https://wikipedia.org/wiki/Minsk_Offensive"}]}, {"year": "1952", "text": "The Constitution of Puerto Rico is approved by the United States Congress.", "html": "1952 - The <a href=\"https://wikipedia.org/wiki/Constitution_of_Puerto_Rico\" title=\"Constitution of Puerto Rico\">Constitution of Puerto Rico</a> is approved by the <a href=\"https://wikipedia.org/wiki/United_States_Congress\" title=\"United States Congress\">United States Congress</a>.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Constitution_of_Puerto_Rico\" title=\"Constitution of Puerto Rico\">Constitution of Puerto Rico</a> is approved by the <a href=\"https://wikipedia.org/wiki/United_States_Congress\" title=\"United States Congress\">United States Congress</a>.", "links": [{"title": "Constitution of Puerto Rico", "link": "https://wikipedia.org/wiki/Constitution_of_Puerto_Rico"}, {"title": "United States Congress", "link": "https://wikipedia.org/wiki/United_States_Congress"}]}, {"year": "1952", "text": "The SS United States sets sail on her maiden voyage to Southampton. During the voyage, the ship takes the Blue Riband away from the RMS Queen Mary.", "html": "1952 - The <a href=\"https://wikipedia.org/wiki/SS_United_States\" title=\"SS United States\">SS <i>United States</i></a> sets sail on her maiden voyage to <a href=\"https://wikipedia.org/wiki/Southampton\" title=\"Southampton\">Southampton</a>. During the voyage, the ship takes the <a href=\"https://wikipedia.org/wiki/Blue_Riband\" title=\"Blue Riband\">Blue Riband</a> away from the <a href=\"https://wikipedia.org/wiki/RMS_Queen_Mary\" title=\"RMS Queen Mary\">RMS <i>Queen <PERSON></i></a>.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/SS_United_States\" title=\"SS United States\">SS <i>United States</i></a> sets sail on her maiden voyage to <a href=\"https://wikipedia.org/wiki/Southampton\" title=\"Southampton\">Southampton</a>. During the voyage, the ship takes the <a href=\"https://wikipedia.org/wiki/Blue_Riband\" title=\"Blue Riband\">Blue Riband</a> away from the <a href=\"https://wikipedia.org/wiki/RMS_Queen_Mary\" title=\"RMS Queen Mary\">RMS <i>Queen Mary</i></a>.", "links": [{"title": "SS United States", "link": "https://wikipedia.org/wiki/SS_United_States"}, {"title": "Southampton", "link": "https://wikipedia.org/wiki/Southampton"}, {"title": "Blue Riband", "link": "https://wikipedia.org/wiki/Blue_Riband"}, {"title": "RMS Queen Mary", "link": "https://wikipedia.org/wiki/RMS_Queen_<PERSON>"}]}, {"year": "1967", "text": "The Aden Emergency: The Battle of the Crater in which the British Argyll and Sutherland Highlanders retake the Crater district following the Arab Police mutiny.", "html": "1967 - The <a href=\"https://wikipedia.org/wiki/Aden_Emergency\" title=\"Aden Emergency\">Aden Emergency</a>: The <a href=\"https://wikipedia.org/wiki/Battle_of_the_Crater_(Aden)\" class=\"mw-redirect\" title=\"Battle of the Crater (Aden)\">Battle of the Crater</a> in which the British <a href=\"https://wikipedia.org/wiki/Argyll_and_Sutherland_Highlanders\" title=\"Argyll and Sutherland Highlanders\">Argyll and Sutherland Highlanders</a> retake the <a href=\"https://wikipedia.org/wiki/Crater_(Aden)\" title=\"Crater (Aden)\">Crater district</a> following the <a href=\"https://wikipedia.org/wiki/Arab_Police_mutiny\" title=\"Arab Police mutiny\">Arab Police mutiny</a>.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Aden_Emergency\" title=\"Aden Emergency\">Aden Emergency</a>: The <a href=\"https://wikipedia.org/wiki/Battle_of_the_Crater_(Aden)\" class=\"mw-redirect\" title=\"Battle of the Crater (Aden)\">Battle of the Crater</a> in which the British <a href=\"https://wikipedia.org/wiki/Argyll_and_Sutherland_Highlanders\" title=\"Argyll and Sutherland Highlanders\">Argyll and Sutherland Highlanders</a> retake the <a href=\"https://wikipedia.org/wiki/Crater_(Aden)\" title=\"Crater (Aden)\">Crater district</a> following the <a href=\"https://wikipedia.org/wiki/Arab_Police_mutiny\" title=\"Arab Police mutiny\">Arab Police mutiny</a>.", "links": [{"title": "Aden Emergency", "link": "https://wikipedia.org/wiki/Aden_Emergency"}, {"title": "Battle of the Crater (Aden)", "link": "https://wikipedia.org/wiki/Battle_of_the_Crater_(Aden)"}, {"title": "Argyll and Sutherland Highlanders", "link": "https://wikipedia.org/wiki/Argyll_and_Sutherland_Highlanders"}, {"title": "<PERSON><PERSON><PERSON> (Aden)", "link": "https://wikipedia.org/wiki/Crater_(Aden)"}, {"title": "Arab Police mutiny", "link": "https://wikipedia.org/wiki/Arab_Police_mutiny"}]}, {"year": "1970", "text": "The Troubles: The \"Falls Curfew\" begins in Belfast, Northern Ireland.", "html": "1970 - <a href=\"https://wikipedia.org/wiki/The_Troubles\" title=\"The Troubles\">The Troubles</a>: The \"<a href=\"https://wikipedia.org/wiki/Falls_Curfew\" title=\"Falls Curfew\">Falls Curfew</a>\" begins in <a href=\"https://wikipedia.org/wiki/Belfast\" title=\"Belfast\">Belfast</a>, <a href=\"https://wikipedia.org/wiki/Northern_Ireland\" title=\"Northern Ireland\">Northern Ireland</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/The_Troubles\" title=\"The Troubles\">The Troubles</a>: The \"<a href=\"https://wikipedia.org/wiki/Falls_Curfew\" title=\"Falls Curfew\">Falls Curfew</a>\" begins in <a href=\"https://wikipedia.org/wiki/Belfast\" title=\"Belfast\">Belfast</a>, <a href=\"https://wikipedia.org/wiki/Northern_Ireland\" title=\"Northern Ireland\">Northern Ireland</a>.", "links": [{"title": "The Troubles", "link": "https://wikipedia.org/wiki/The_Troubles"}, {"title": "Falls Curfew", "link": "https://wikipedia.org/wiki/Falls_Curfew"}, {"title": "Belfast", "link": "https://wikipedia.org/wiki/Belfast"}, {"title": "Northern Ireland", "link": "https://wikipedia.org/wiki/Northern_Ireland"}]}, {"year": "1970", "text": "Dan-Air Flight 1903 crashes into the Les Agudes mountain in the Montseny Massif near the village of Arbúcies in Catalonia, Spain, killing all 112 people aboard.", "html": "1970 - <a href=\"https://wikipedia.org/wiki/Dan-Air_Flight_1903\" title=\"Dan-Air Flight 1903\">Dan-Air Flight 1903</a> crashes into the <a href=\"https://wikipedia.org/wiki/Les_Agudes\" title=\"Les Agudes\">Les Agudes</a> mountain in the <a href=\"https://wikipedia.org/wiki/Montseny_Massif\" title=\"Montseny Massif\">Montseny Massif</a> near the village of <a href=\"https://wikipedia.org/wiki/Arb%C3%BAcies\" title=\"Arbúcies\">Arbúcies</a> in <a href=\"https://wikipedia.org/wiki/Catalonia\" title=\"Catalonia\">Catalonia</a>, <a href=\"https://wikipedia.org/wiki/Spain\" title=\"Spain\">Spain</a>, killing all 112 people aboard.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Dan-Air_Flight_1903\" title=\"Dan-Air Flight 1903\">Dan-Air Flight 1903</a> crashes into the <a href=\"https://wikipedia.org/wiki/Les_Agudes\" title=\"Les Agudes\">Les Agu<PERSON></a> mountain in the <a href=\"https://wikipedia.org/wiki/Montseny_Massif\" title=\"Montseny Massif\">Montseny Massif</a> near the village of <a href=\"https://wikipedia.org/wiki/Arb%C3%BAcies\" title=\"Arbúcies\">Arbúcies</a> in <a href=\"https://wikipedia.org/wiki/Catalonia\" title=\"Catalonia\">Catalonia</a>, <a href=\"https://wikipedia.org/wiki/Spain\" title=\"Spain\">Spain</a>, killing all 112 people aboard.", "links": [{"title": "Dan-Air Flight 1903", "link": "https://wikipedia.org/wiki/Dan-Air_Flight_1903"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Montseny Massif", "link": "https://wikipedia.org/wiki/Montseny_Massif"}, {"title": "Arbúcies", "link": "https://wikipedia.org/wiki/Arb%C3%BAcies"}, {"title": "Catalonia", "link": "https://wikipedia.org/wiki/Catalonia"}, {"title": "Spain", "link": "https://wikipedia.org/wiki/Spain"}]}, {"year": "1973", "text": "<PERSON> retires his stage persona <PERSON><PERSON><PERSON> with the surprise announcement that it is \"the last show that we'll ever do\" on the last day of the Ziggy Stardust Tour.", "html": "1973 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> retires his stage persona <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_Stardust_(character)\" title=\"<PERSON><PERSON><PERSON> (character)\">Zig<PERSON></a> with the surprise announcement that it is \"the last show that we'll ever do\" on the last day of the <a href=\"https://wikipedia.org/wiki/Ziggy_Stardust_Tour\" title=\"Ziggy Stardust Tour\">Ziggy Stardust Tour</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> retires his stage persona <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_Stardust_(character)\" title=\"<PERSON><PERSON><PERSON>dust (character)\"><PERSON>ig<PERSON></a> with the surprise announcement that it is \"the last show that we'll ever do\" on the last day of the <a href=\"https://wikipedia.org/wiki/Ziggy_Stardust_Tour\" title=\"Ziggy Stardust Tour\">Ziggy Stardust Tour</a>.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "<PERSON><PERSON><PERSON> (character)", "link": "https://wikipedia.org/wiki/Ziggy_Stardust_(character)"}, {"title": "Ziggy Stardust Tour", "link": "https://wikipedia.org/wiki/Ziggy_Stardust_Tour"}]}, {"year": "1979", "text": "U.S. President <PERSON> signs the first directive for secret aid to the opponents of the pro-Soviet regime in Kabul.", "html": "1979 - U.S. President <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> signs the first directive for <a href=\"https://wikipedia.org/wiki/Operation_Cyclone\" title=\"Operation Cyclone\">secret aid</a> to the opponents of the pro-Soviet regime in Kabul.", "no_year_html": "U.S. President <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> signs the first directive for <a href=\"https://wikipedia.org/wiki/Operation_Cyclone\" title=\"Operation Cyclone\">secret aid</a> to the opponents of the pro-Soviet regime in Kabul.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Operation Cyclone", "link": "https://wikipedia.org/wiki/Operation_Cyclone"}]}, {"year": "1988", "text": "United States Navy warship <PERSON> Vincennes shoots down Iran Air Flight 655 over the Persian Gulf, killing all 290 people aboard.", "html": "1988 - <a href=\"https://wikipedia.org/wiki/United_States_Navy\" title=\"United States Navy\">United States Navy</a> warship <a href=\"https://wikipedia.org/wiki/USS_Vincennes_(CG-49)\" title=\"USS Vincennes (CG-49)\">USS <i>Vincennes</i></a> shoots down <a href=\"https://wikipedia.org/wiki/Iran_Air_Flight_655\" title=\"Iran Air Flight 655\">Iran Air Flight 655</a> over the <a href=\"https://wikipedia.org/wiki/Persian_Gulf\" title=\"Persian Gulf\">Persian Gulf</a>, killing all 290 people aboard.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/United_States_Navy\" title=\"United States Navy\">United States Navy</a> warship <a href=\"https://wikipedia.org/wiki/USS_Vincennes_(CG-49)\" title=\"USS Vincennes (CG-49)\">USS <i>Vincennes</i></a> shoots down <a href=\"https://wikipedia.org/wiki/Iran_Air_Flight_655\" title=\"Iran Air Flight 655\">Iran Air Flight 655</a> over the <a href=\"https://wikipedia.org/wiki/Persian_Gulf\" title=\"Persian Gulf\">Persian Gulf</a>, killing all 290 people aboard.", "links": [{"title": "United States Navy", "link": "https://wikipedia.org/wiki/United_States_Navy"}, {"title": "USS Vincennes (CG-49)", "link": "https://wikipedia.org/wiki/USS_Vincennes_(CG-49)"}, {"title": "Iran Air Flight 655", "link": "https://wikipedia.org/wiki/Iran_Air_Flight_655"}, {"title": "Persian Gulf", "link": "https://wikipedia.org/wiki/Persian_Gulf"}]}, {"year": "1988", "text": "The Fatih Sultan Mehmet Bridge in Istanbul, Turkey is completed, providing the second connection between the continents of Europe and Asia over the Bosphorus.", "html": "1988 - The <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_Sultan_Mehmet_Bridge\" title=\"Fatih Sultan Mehmet Bridge\">Fatih Sultan Mehmet Bridge</a> in Istanbul, Turkey is completed, providing the second connection between the continents of Europe and Asia over the <a href=\"https://wikipedia.org/wiki/Bosphorus\" class=\"mw-redirect\" title=\"Bosphorus\">Bosphorus</a>.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Fat<PERSON>_Sultan_Mehmet_Bridge\" title=\"Fatih Sultan Mehmet Bridge\">Fatih Sultan Mehmet Bridge</a> in Istanbul, Turkey is completed, providing the second connection between the continents of Europe and Asia over the <a href=\"https://wikipedia.org/wiki/Bosphorus\" class=\"mw-redirect\" title=\"Bosphorus\">Bosphorus</a>.", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>_<PERSON>"}, {"title": "Bosphor<PERSON>", "link": "https://wikipedia.org/wiki/Bosphorus"}]}, {"year": "1996", "text": "British Prime Minister <PERSON> announced the Stone of Scone would be returned to Scotland.", "html": "1996 - British Prime Minister <a href=\"https://wikipedia.org/wiki/<PERSON>_Major\" title=\"John Major\"><PERSON></a> announced the <a href=\"https://wikipedia.org/wiki/<PERSON>_of_Scone\" title=\"<PERSON> of Scone\"><PERSON> of Scone</a> would be returned to Scotland.", "no_year_html": "British Prime Minister <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"John Major\"><PERSON></a> announced the <a href=\"https://wikipedia.org/wiki/<PERSON>_of_Scone\" title=\"<PERSON> of Scone\"><PERSON> of Scone</a> would be returned to Scotland.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Stone of Scone", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON><PERSON>"}]}, {"year": "2013", "text": "President of Egypt <PERSON> is removed from office by the military after four days of protests all over the country calling for his resignation, to which he did not respond. The president of the Supreme Constitutional Court of Egypt, <PERSON><PERSON>, is declared acting president until further elections are held.", "html": "2013 - <a href=\"https://wikipedia.org/wiki/President_of_Egypt\" title=\"President of Egypt\">President of Egypt</a> <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> is <a href=\"https://wikipedia.org/wiki/2013_Egyptian_coup_d%27%C3%A9tat\" title=\"2013 Egyptian coup d'état\">removed from office</a> by the <a href=\"https://wikipedia.org/wiki/Egyptian_Armed_Forces\" title=\"Egyptian Armed Forces\">military</a> after four days of protests all over the country calling for his resignation, to which he did not respond. The president of the <a href=\"https://wikipedia.org/wiki/Supreme_Constitutional_Court_of_Egypt\" class=\"mw-redirect\" title=\"Supreme Constitutional Court of Egypt\">Supreme Constitutional Court of Egypt</a>, <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, is declared acting president until further elections are held.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/President_of_Egypt\" title=\"President of Egypt\">President of Egypt</a> <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> is <a href=\"https://wikipedia.org/wiki/2013_Egyptian_coup_d%27%C3%A9tat\" title=\"2013 Egyptian coup d'état\">removed from office</a> by the <a href=\"https://wikipedia.org/wiki/Egyptian_Armed_Forces\" title=\"Egyptian Armed Forces\">military</a> after four days of protests all over the country calling for his resignation, to which he did not respond. The president of the <a href=\"https://wikipedia.org/wiki/Supreme_Constitutional_Court_of_Egypt\" class=\"mw-redirect\" title=\"Supreme Constitutional Court of Egypt\">Supreme Constitutional Court of Egypt</a>, <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, is declared acting president until further elections are held.", "links": [{"title": "President of Egypt", "link": "https://wikipedia.org/wiki/President_of_Egypt"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "2013 Egyptian coup d'état", "link": "https://wikipedia.org/wiki/2013_Egyptian_coup_d%27%C3%A9tat"}, {"title": "Egyptian Armed Forces", "link": "https://wikipedia.org/wiki/Egyptian_Armed_Forces"}, {"title": "Supreme Constitutional Court of Egypt", "link": "https://wikipedia.org/wiki/Supreme_Constitutional_Court_of_Egypt"}, {"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}], "Births": [{"year": "321", "text": "<PERSON><PERSON><PERSON>, Roman emperor (d. 375)", "html": "321 - <a href=\"https://wikipedia.org/wiki/Valentinian_I\" title=\"Valentinian I\"><PERSON><PERSON><PERSON> I</a>, Roman emperor (d. 375)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Valentinian_I\" title=\"Valentinian I\">Valentin<PERSON> I</a>, Roman emperor (d. 375)", "links": [{"title": "Valentinian I", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_I"}]}, {"year": "1423", "text": "<PERSON> of France (d. 1483)", "html": "1423 - <a href=\"https://wikipedia.org/wiki/Louis_XI_of_France\" class=\"mw-redirect\" title=\"Louis XI of France\">Louis XI of France</a> (d. 1483)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Louis_XI_of_France\" class=\"mw-redirect\" title=\"Louis XI of France\"><PERSON> XI of France</a> (d. 1483)", "links": [{"title": "Louis XI of France", "link": "https://wikipedia.org/wiki/Louis_XI_of_France"}]}, {"year": "1442", "text": "Emperor <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> of Japan (d. 1500)", "html": "1442 - <a href=\"https://wikipedia.org/wiki/Emperor_<PERSON>-Tsuchimikado\" title=\"Emperor Go-Tsuchimikado\">Emperor <PERSON><PERSON>T<PERSON><PERSON><PERSON>ka<PERSON></a> of Japan (d. 1500)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Emperor_<PERSON>-Tsuchimikado\" title=\"Emperor Go-Tsuchimikado\">Emperor <PERSON><PERSON>T<PERSON><PERSON><PERSON>ka<PERSON></a> of Japan (d. 1500)", "links": [{"title": "Emperor <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>-<PERSON><PERSON><PERSON><PERSON><PERSON>"}]}, {"year": "1518", "text": "<PERSON>, Chinese physician and mineralogist (d. 1593)", "html": "1518 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Li Shizhen\"><PERSON></a>, Chinese physician and mineralogist (d. 1593)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Li_<PERSON>\" title=\"Li Shizhen\"><PERSON></a>, Chinese physician and mineralogist (d. 1593)", "links": [{"title": "Li Shizhen", "link": "https://wikipedia.org/wiki/Li_Shizhen"}]}, {"year": "1530", "text": "<PERSON>, French historian and author (d. 1601)", "html": "1530 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(historian)\" title=\"<PERSON> (historian)\"><PERSON></a>, French historian and author (d. 1601)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(historian)\" title=\"<PERSON> (historian)\"><PERSON></a>, French historian and author (d. 1601)", "links": [{"title": "<PERSON> (historian)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(historian)"}]}, {"year": "1534", "text": "<PERSON><PERSON><PERSON><PERSON> of Joseon, Ruler of Korea (d. 1567)", "html": "1534 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_of_Joseon\" title=\"<PERSON><PERSON><PERSON><PERSON> of Joseon\"><PERSON><PERSON><PERSON><PERSON> of Joseon</a>, Ruler of Korea (d. 1567)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_of_Joseon\" title=\"<PERSON><PERSON><PERSON><PERSON> of Joseon\"><PERSON><PERSON><PERSON><PERSON> of Joseon</a>, Ruler of Korea (d. 1567)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON> of Joseon", "link": "https://wikipedia.org/wiki/My<PERSON>g<PERSON>_of_Joseon"}]}, {"year": "1550", "text": "<PERSON><PERSON>, Slovenian composer (d. 1591)", "html": "1550 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> Gallus\"><PERSON><PERSON></a>, Slovenian composer (d. 1591)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> Gallus\"><PERSON><PERSON></a>, Slovenian composer (d. 1591)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1569", "text": "<PERSON>, English politician and judge (d. 1635)", "html": "1569 - <a href=\"https://wikipedia.org/wiki/<PERSON>(judge)\" title=\"<PERSON> (judge)\"><PERSON></a>, English politician and judge (d. 1635)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>(judge)\" title=\"<PERSON> (judge)\"><PERSON></a>, English politician and judge (d. 1635)", "links": [{"title": "<PERSON> (judge)", "link": "https://wikipedia.org/wiki/<PERSON>_(judge)"}]}, {"year": "1683", "text": "<PERSON>, English poet, dramatist and literary critic (Night-Thoughts) (d. 1765)", "html": "1683 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English poet, dramatist and literary critic (<i><a href=\"https://wikipedia.org/wiki/Night-Thoughts\" title=\"Night-Thoughts\">Night-Thoughts</a></i>) (d. 1765)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English poet, dramatist and literary critic (<i><a href=\"https://wikipedia.org/wiki/Night-Thoughts\" title=\"Night-Thoughts\">Night-Thoughts</a></i>) (d. 1765)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Night-Thoughts", "link": "https://wikipedia.org/wiki/Night-Thoughts"}]}, {"year": "1685", "text": "Sir <PERSON>, 4th Baronet, English field marshal and politician (d. 1768)", "html": "1685 - <a href=\"https://wikipedia.org/wiki/Sir_<PERSON>,_4th_Baronet\" title=\"Sir <PERSON>, 4th Baronet\">Sir <PERSON>, 4th Baronet</a>, English field marshal and politician (d. 1768)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Sir_<PERSON>_<PERSON>,_4th_Baronet\" title=\"Sir <PERSON>, 4th Baronet\">Sir <PERSON>, 4th Baronet</a>, English field marshal and politician (d. 1768)", "links": [{"title": "Sir <PERSON>, 4th Baronet", "link": "https://wikipedia.org/wiki/Sir_<PERSON>_<PERSON>,_4th_Baronet"}]}, {"year": "1728", "text": "<PERSON>, Scottish-English architect, designed Culzean Castle (d. 1792)", "html": "1728 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish-English architect, designed <a href=\"https://wikipedia.org/wiki/Culzean_Castle\" title=\"Culzean Castle\">Culzean Castle</a> (d. 1792)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish-English architect, designed <a href=\"https://wikipedia.org/wiki/Culzean_Castle\" title=\"Culzean Castle\">Culzean Castle</a> (d. 1792)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Culzean Castle", "link": "https://wikipedia.org/wiki/Culzean_Castle"}]}, {"year": "1738", "text": "<PERSON>, American painter (d. 1815)", "html": "1738 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American painter (d. 1815)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American painter (d. 1815)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1778", "text": "<PERSON>, German architect (d. 1840)", "html": "1778 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German architect (d. 1840)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German architect (d. 1840)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1789", "text": "<PERSON>, German-Italian painter and engraver (d. 1869)", "html": "1789 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German-Italian painter and engraver (d. 1869)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German-Italian painter and engraver (d. 1869)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1814", "text": "<PERSON>, Danish botanist and physicist (d. 1887)", "html": "1814 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Danish botanist and physicist (d. 1887)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Danish botanist and physicist (d. 1887)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1823", "text": "<PERSON>, Greek-Ottoman statesman, diplomat, playwright, and translator (d. 1891)", "html": "1823 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Greek-Ottoman statesman, diplomat, playwright, and translator (d. 1891)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Greek-Ottoman statesman, diplomat, playwright, and translator (d. 1891)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1844", "text": "<PERSON><PERSON><PERSON>, German-born American architect and engineer (d. 1900)", "html": "1844 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, German-born American architect and engineer (d. 1900)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, German-born American architect and engineer (d. 1900)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1846", "text": "<PERSON>, Russian composer and politician, Governor of Taganrog (d. 1919)", "html": "1846 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian composer and politician, <a href=\"https://wikipedia.org/wiki/Governor_of_Taganrog\" title=\"Governor of Taganrog\">Governor of Taganrog</a> (d. 1919)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian composer and politician, <a href=\"https://wikipedia.org/wiki/Governor_of_Taganrog\" title=\"Governor of Taganrog\">Governor of Taganrog</a> (d. 1919)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Achilles_Alferaki"}, {"title": "Governor of Taganrog", "link": "https://wikipedia.org/wiki/Governor_of_Taganrog"}]}, {"year": "1851", "text": "<PERSON>, English-Australian cricketer and umpire (d. 1930)", "html": "1851 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English-Australian cricketer and umpire (d. 1930)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English-Australian cricketer and umpire (d. 1930)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_Bannerman"}]}, {"year": "1854", "text": "<PERSON><PERSON>, Czech composer and theorist (d. 1928)", "html": "1854 - <a href=\"https://wikipedia.org/wiki/Leo%C5%A1_Jan%C3%A1%C4%8Dek\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Czech composer and theorist (d. 1928)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Leo%C5%A1_Jan%C3%A1%C4%8Dek\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Czech composer and theorist (d. 1928)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Leo%C5%A1_Jan%C3%A1%C4%8Dek"}]}, {"year": "1860", "text": "<PERSON>, American sociologist and author (d. 1935)", "html": "1860 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American sociologist and author (d. 1935)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American sociologist and author (d. 1935)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1866", "text": "<PERSON>, Danish painter (d. 1906)", "html": "1866 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Danish painter (d. 1906)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Danish painter (d. 1906)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1869", "text": "<PERSON><PERSON>, Danish actor (d. 1933)", "html": "1869 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Danish actor (d. 1933)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Danish actor (d. 1933)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1870", "text": "<PERSON><PERSON> <PERSON><PERSON>, Canadian lawyer and politician, 11th Prime Minister of Canada (d. 1947)", "html": "1870 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> <PERSON><PERSON>\"><PERSON><PERSON> <PERSON><PERSON></a>, Canadian lawyer and politician, 11th <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Canada\" title=\"Prime Minister of Canada\">Prime Minister of Canada</a> (d. 1947)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> <PERSON><PERSON>\"><PERSON><PERSON> <PERSON><PERSON></a>, Canadian lawyer and politician, 11th <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Canada\" title=\"Prime Minister of Canada\">Prime Minister of Canada</a> (d. 1947)", "links": [{"title": "<PERSON><PERSON> <PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>"}, {"title": "Prime Minister of Canada", "link": "https://wikipedia.org/wiki/Prime_Minister_of_Canada"}]}, {"year": "1871", "text": "<PERSON>, Welsh poet and writer (d. 1940)", "html": "1871 - <a href=\"https://wikipedia.org/wiki/W<PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> <PERSON><PERSON>\"><PERSON></a>, Welsh poet and writer (d. 1940)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/W<PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> <PERSON><PERSON>\"><PERSON></a>, Welsh poet and writer (d. 1940)", "links": [{"title": "<PERSON><PERSON> <PERSON><PERSON>", "link": "https://wikipedia.org/wiki/W<PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "1874", "text": "<PERSON>, French rugby player and tug of war competitor (d. 1928)", "html": "1874 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French rugby player and <a href=\"https://wikipedia.org/wiki/Tug_of_war\" title=\"Tug of war\">tug of war</a> competitor (d. 1928)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French rugby player and <a href=\"https://wikipedia.org/wiki/Tug_of_war\" title=\"Tug of war\">tug of war</a> competitor (d. 1928)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}, {"title": "Tug of war", "link": "https://wikipedia.org/wiki/Tug_of_war"}]}, {"year": "1875", "text": "<PERSON>, German surgeon and academic (d. 1951)", "html": "1875 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German surgeon and academic (d. 1951)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German surgeon and academic (d. 1951)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1876", "text": "<PERSON>, American philosopher and academic (d. 1957)", "html": "1876 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American philosopher and academic (d. 1957)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American philosopher and academic (d. 1957)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1878", "text": "<PERSON>, American songwriter, actor, singer, and dancer (d. 1942)", "html": "1878 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American songwriter, actor, singer, and dancer (d. 1942)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American songwriter, actor, singer, and dancer (d. 1942)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1879", "text": "<PERSON>, Polish-American mathematician, linguist, and philosopher (d. 1950)", "html": "1879 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Polish-American mathematician, linguist, and philosopher (d. 1950)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Polish-American mathematician, linguist, and philosopher (d. 1950)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1880", "text": "<PERSON>, Polish-German conductor (d. 1967)", "html": "1880 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Polish-German conductor (d. 1967)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Polish-German conductor (d. 1967)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1883", "text": "<PERSON>, Czech-Austrian author (d. 1924)", "html": "1883 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Czech-Austrian author (d. 1924)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Czech-Austrian author (d. 1924)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1885", "text": "<PERSON>, American politician (d. 1971)", "html": "1885 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American politician (d. 1971)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American politician (d. 1971)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "1886", "text": "<PERSON>, American admiral and diplomat, United States Ambassador to the Philippines (d. 1969)", "html": "1886 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>ruance\" title=\"<PERSON>\"><PERSON></a>, American admiral and diplomat, <a href=\"https://wikipedia.org/wiki/United_States_Ambassador_to_the_Philippines\" class=\"mw-redirect\" title=\"United States Ambassador to the Philippines\">United States Ambassador to the Philippines</a> (d. 1969)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>ruance\" title=\"<PERSON>\"><PERSON></a>, American admiral and diplomat, <a href=\"https://wikipedia.org/wiki/United_States_Ambassador_to_the_Philippines\" class=\"mw-redirect\" title=\"United States Ambassador to the Philippines\">United States Ambassador to the Philippines</a> (d. 1969)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>ruance"}, {"title": "United States Ambassador to the Philippines", "link": "https://wikipedia.org/wiki/United_States_Ambassador_to_the_Philippines"}]}, {"year": "1888", "text": "<PERSON>, Spanish author and playwright (d. 1963)", "html": "1888 - <a href=\"https://wikipedia.org/wiki/Ram%C3%B3n_G%C3%B3mez_de_la_Serna\" title=\"<PERSON>\"><PERSON></a>, Spanish author and playwright (d. 1963)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Ram%C3%B3n_G%C3%B3mez_de_la_Serna\" title=\"<PERSON>\"><PERSON></a>, Spanish author and playwright (d. 1963)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Ram%C3%B3n_G%C3%B3mez_de_la_Serna"}]}, {"year": "1889", "text": "<PERSON>, American actor (d. 1960)", "html": "1889 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor (d. 1960)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor (d. 1960)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1893", "text": "<PERSON><PERSON><PERSON>, Hungarian painter and graphic designer (d. 1976)", "html": "1893 - <a href=\"https://wikipedia.org/wiki/S%C3%A1ndor_Bortnyik\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Hungarian painter and graphic designer (d. 1976)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/S%C3%A1ndor_Bortnyik\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Hungarian painter and graphic designer (d. 1976)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/S%C3%A1ndor_Bortnyik"}]}, {"year": "1896", "text": "<PERSON>, English actress (d. 1968)", "html": "1896 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actress (d. 1968)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actress (d. 1968)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1897", "text": "<PERSON>, American mathematician and academic (d. 1965)", "html": "1897 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American mathematician and academic (d. 1965)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American mathematician and academic (d. 1965)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1898", "text": "<PERSON><PERSON>, Greek politician, Prime Minister of Greece (d. 1982)", "html": "1898 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Greek politician, <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Greece\" title=\"Prime Minister of Greece\">Prime Minister of Greece</a> (d. 1982)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Greek politician, <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Greece\" title=\"Prime Minister of Greece\">Prime Minister of Greece</a> (d. 1982)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}, {"title": "Prime Minister of Greece", "link": "https://wikipedia.org/wiki/Prime_Minister_of_Greece"}]}, {"year": "1900", "text": "<PERSON>, Italian director and screenwriter (d. 1987)", "html": "1900 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian director and screenwriter (d. 1987)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian director and screenwriter (d. 1987)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1901", "text": "<PERSON>, American composer (d. 1953)", "html": "1901 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American composer (d. 1953)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American composer (d. 1953)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1903", "text": "<PERSON>, Canadian ice hockey player and coach (d. 1992)", "html": "1903 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player and coach (d. 1992)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player and coach (d. 1992)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1905", "text": "<PERSON>, American hurdler and coach (d. 2006)", "html": "1905 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American hurdler and coach (d. 2006)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American hurdler and coach (d. 2006)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1905", "text": "<PERSON>, Norwegian painter and illustrator (d. 1997)", "html": "1905 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Norwegian painter and illustrator (d. 1997)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Norwegian painter and illustrator (d. 1997)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1906", "text": "<PERSON>, Russian-born British actor (d. 1972)", "html": "1906 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian-born British actor (d. 1972)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian-born British actor (d. 1972)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1908", "text": "<PERSON><PERSON> <PERSON><PERSON> <PERSON><PERSON>, American author (d. 1992)", "html": "1908 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> <PERSON><PERSON> <PERSON><PERSON>\"><PERSON><PERSON> <PERSON><PERSON> <PERSON><PERSON></a>, American author (d. 1992)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> <PERSON><PERSON> <PERSON><PERSON>\"><PERSON><PERSON> <PERSON><PERSON> <PERSON><PERSON></a>, American author (d. 1992)", "links": [{"title": "<PERSON><PERSON> <PERSON><PERSON> <PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "1908", "text": "<PERSON>, American lawyer and politician, 44th Governor of New Jersey (d. 1990)", "html": "1908 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and politician, 44th <a href=\"https://wikipedia.org/wiki/Governor_of_New_Jersey\" title=\"Governor of New Jersey\">Governor of New Jersey</a> (d. 1990)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and politician, 44th <a href=\"https://wikipedia.org/wiki/Governor_of_New_Jersey\" title=\"Governor of New Jersey\">Governor of New Jersey</a> (d. 1990)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>"}, {"title": "Governor of New Jersey", "link": "https://wikipedia.org/wiki/Governor_of_New_Jersey"}]}, {"year": "1909", "text": "<PERSON><PERSON><PERSON>, Greek shipping magnate (d. 1996)", "html": "1909 - <a href=\"https://wikipedia.org/wiki/St<PERSON>ros_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Greek shipping magnate (d. 1996)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Greek shipping magnate (d. 1996)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>s"}]}, {"year": "1910", "text": "<PERSON>, Austrian mountaineer (d. 1954)", "html": "1910 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Austrian mountaineer (d. 1954)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Austrian mountaineer (d. 1954)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1911", "text": "<PERSON>, English cricketer (d. 1990)", "html": "1911 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>.\" class=\"mw-redirect\" title=\"<PERSON>.\"><PERSON>.</a>, English cricketer (d. 1990)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>.\" class=\"mw-redirect\" title=\"<PERSON>.\"><PERSON>.</a>, English cricketer (d. 1990)", "links": [{"title": "<PERSON> Jr.", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>."}]}, {"year": "1913", "text": "<PERSON>, American journalist, actress, and author (d. 1965)", "html": "1913 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American journalist, actress, and author (d. 1965)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American journalist, actress, and author (d. 1965)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1916", "text": "<PERSON>, American basketball player and coach (d. 2017)", "html": "1916 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player and coach (d. 2017)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player and coach (d. 2017)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1917", "text": "<PERSON>, Brazilian footballer, manager, and journalist (d. 1990)", "html": "1917 - <a href=\"https://wikipedia.org/wiki/Jo%C3%A3o_Saldanha\" title=\"<PERSON>\"><PERSON></a>, Brazilian footballer, manager, and journalist (d. 1990)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Jo%C3%A3o_Saldanha\" title=\"<PERSON>\"><PERSON></a>, Brazilian footballer, manager, and journalist (d. 1990)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Jo%C3%A3o_Saldanha"}]}, {"year": "1918", "text": "<PERSON><PERSON> <PERSON><PERSON>, Indian actor, director, and producer (d. 1974)", "html": "1918 - <a href=\"https://wikipedia.org/wiki/S._<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> <PERSON><PERSON>\"><PERSON><PERSON> <PERSON><PERSON></a>, Indian actor, director, and producer (d. 1974)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/S._<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> <PERSON><PERSON><PERSON>\"><PERSON><PERSON> <PERSON><PERSON></a>, Indian actor, director, and producer (d. 1974)", "links": [{"title": "<PERSON><PERSON> <PERSON><PERSON>", "link": "https://wikipedia.org/wiki/S<PERSON>_<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "1918", "text": "<PERSON>, American golfer (d. 2006)", "html": "1918 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American golfer (d. 2006)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American golfer (d. 2006)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1919", "text": "<PERSON>, 8th Earl of Orkney (d. 1998)", "html": "1919 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_8th_Earl_of_Orkney\" title=\"<PERSON>, 8th Earl of Orkney\"><PERSON>, 8th Earl of Orkney</a> (d. 1998)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>,_8th_Earl_of_Orkney\" title=\"<PERSON>, 8th Earl of Orkney\"><PERSON>, 8th Earl of Orkney</a> (d. 1998)", "links": [{"title": "<PERSON>, 8th Earl of Orkney", "link": "https://wikipedia.org/wiki/<PERSON>,_8th_Earl_of_Orkney"}]}, {"year": "1919", "text": "<PERSON>, American soldier and academic (d. 2013)", "html": "1919 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American soldier and academic (d. 2013)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American soldier and academic (d. 2013)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "1920", "text": "<PERSON>, Belgian illustrator (d. 2012)", "html": "1920 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Belgian illustrator (d. 2012)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Belgian illustrator (d. 2012)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1920", "text": "<PERSON>, American baseball player and manager (d. 1978)", "html": "1920 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%27Dea\" title=\"<PERSON>\"><PERSON></a>, American baseball player and manager (d. 1978)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%27Dea\" title=\"<PERSON>\"><PERSON></a>, American baseball player and manager (d. 1978)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_O%27Dea"}]}, {"year": "1920", "text": "<PERSON><PERSON><PERSON>, Swedish politician (d. 2006)", "html": "1920 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_Bladh\" title=\"<PERSON><PERSON><PERSON> Bladh\"><PERSON><PERSON><PERSON></a>, Swedish politician (d. 2006)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_Bladh\" title=\"<PERSON>nar<PERSON> Bladh\"><PERSON><PERSON><PERSON></a>, Swedish politician (d. 2006)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>nart_Bladh"}]}, {"year": "1921", "text": "<PERSON><PERSON> <PERSON>, First Lady of Venezuela (d. 2013)", "html": "1921 - <a href=\"https://wikipedia.org/wiki/Flor_Mar%C3%ADa_Cha<PERSON>ud\" title=\"<PERSON><PERSON> <PERSON>\"><PERSON><PERSON></a>, First Lady of Venezuela (d. 2013)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Flor_Mar%C3%ADa_Cha<PERSON>ud\" title=\"<PERSON><PERSON> <PERSON>\"><PERSON><PERSON> <PERSON></a>, First Lady of Venezuela (d. 2013)", "links": [{"title": "<PERSON><PERSON> <PERSON>", "link": "https://wikipedia.org/wiki/Flor_Mar%C3%AD<PERSON>_<PERSON><PERSON><PERSON><PERSON>"}]}, {"year": "1921", "text": "<PERSON>, American actress (d. 1952)", "html": "1921 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress (d. 1952)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress (d. 1952)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1921", "text": "<PERSON>, French director, producer, and screenwriter (d. 1993)", "html": "1921 - <a href=\"https://wikipedia.org/wiki/Fran%C3%A7ois_<PERSON>enbach\" title=\"<PERSON>\"><PERSON></a>, French director, producer, and screenwriter (d. 1993)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Fran%C3%A7ois_<PERSON>enbach\" title=\"<PERSON>\"><PERSON></a>, French director, producer, and screenwriter (d. 1993)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Fran%C3%A7ois_Reichenbach"}]}, {"year": "1922", "text": "<PERSON>, Belgian painter and sculptor (d. 2010)", "html": "1922 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_Beverloo\" class=\"mw-redirect\" title=\"<PERSON> Beverloo\"><PERSON>lo<PERSON></a>, Belgian painter and sculptor (d. 2010)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_Beverloo\" class=\"mw-redirect\" title=\"<PERSON> Beverloo\"><PERSON>lo<PERSON></a>, Belgian painter and sculptor (d. 2010)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1922", "text": "<PERSON>, Dutch football player (d. 2003)", "html": "1922 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>.\" title=\"<PERSON>\"><PERSON>.</a>, Dutch football player (d. 2003)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>.\" title=\"<PERSON>\"><PERSON>.</a>, Dutch football player (d. 2003)", "links": [{"title": "<PERSON>.", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>."}]}, {"year": "1924", "text": "<PERSON><PERSON>, Cuban-Mexican film actress and dancer (d. 2021)", "html": "1924 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Cuban-Mexican film actress and dancer (d. 2021)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Cuban-Mexican film actress and dancer (d. 2021)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1924", "text": "<PERSON><PERSON> <PERSON><PERSON>, 6th President of Singapore (d. 2016)", "html": "1924 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> <PERSON><PERSON>\"><PERSON><PERSON> <PERSON><PERSON></a>, 6th <a href=\"https://wikipedia.org/wiki/President_of_Singapore\" title=\"President of Singapore\">President of Singapore</a> (d. 2016)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> <PERSON><PERSON>\"><PERSON><PERSON> <PERSON><PERSON></a>, 6th <a href=\"https://wikipedia.org/wiki/President_of_Singapore\" title=\"President of Singapore\">President of Singapore</a> (d. 2016)", "links": [{"title": "<PERSON><PERSON> <PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>"}, {"title": "President of Singapore", "link": "https://wikipedia.org/wiki/President_of_Singapore"}]}, {"year": "1925", "text": "<PERSON>, Australian rules footballer (d. 2011)", "html": "1925 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian rules footballer (d. 2011)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian rules footballer (d. 2011)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1925", "text": "<PERSON>, American professional boxer (d. 2010)", "html": "1925 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American professional boxer (d. 2010)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American professional boxer (d. 2010)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1925", "text": "<PERSON>, American artist (d. 2021)", "html": "1925 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American artist (d. 2021)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American artist (d. 2021)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1926", "text": "<PERSON>, American trumpet player (d. 1997)", "html": "1926 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American trumpet player (d. 1997)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American trumpet player (d. 1997)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1926", "text": "<PERSON>, American actress, singer, and director (d. 2022)", "html": "1926 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress, singer, and director (d. 2022)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress, singer, and director (d. 2022)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1926", "text": "<PERSON>, Australian jurist and former Chief Justice of the Supreme Court of New South Wales (d. 2018)", "html": "1926 - <a href=\"https://wikipedia.org/wiki/Laurence_Street\" title=\"Laurence Street\">Laurence Street</a>, Australian jurist and former Chief Justice of the Supreme Court of New South Wales (d. 2018)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Laurence_Street\" title=\"Laurence Street\">Laurence Street</a>, Australian jurist and former Chief Justice of the Supreme Court of New South Wales (d. 2018)", "links": [{"title": "Laurence Street", "link": "https://wikipedia.org/wiki/Laurence_<PERSON>"}]}, {"year": "1927", "text": "<PERSON>, English actor, director, and producer (d. 2011)", "html": "1927 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actor, director, and producer (d. 2011)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actor, director, and producer (d. 2011)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1927", "text": "<PERSON>, American actor (d. 2018)", "html": "1927 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%27C<PERSON><PERSON>_(actor)\" title=\"<PERSON> (actor)\"><PERSON></a>, American actor (d. 2018)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%27<PERSON><PERSON><PERSON>_(actor)\" title=\"<PERSON> (actor)\"><PERSON></a>, American actor (d. 2018)", "links": [{"title": "<PERSON> (actor)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>%27<PERSON><PERSON><PERSON>_(actor)"}]}, {"year": "1928", "text": "<PERSON>, English author (d. 2018)", "html": "1928 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English author (d. 2018)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English author (d. 2018)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1929", "text": "<PERSON><PERSON><PERSON>, Canadian director, producer, and screenwriter (d. 1999)", "html": "1929 - <a href=\"https://wikipedia.org/wiki/Cl%C3%A9<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON> Per<PERSON>\"><PERSON><PERSON><PERSON></a>, Canadian director, producer, and screenwriter (d. 1999)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Cl%C3%A9ment_Perron\" title=\"<PERSON><PERSON><PERSON> Per<PERSON>\"><PERSON><PERSON><PERSON></a>, Canadian director, producer, and screenwriter (d. 1999)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Cl%C3%A9ment_Perron"}]}, {"year": "1929", "text": "<PERSON>, American socialite, businesswoman, political activist, philanthropist, diplomat, and television talk show host", "html": "1929 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American socialite, businesswoman, political activist, philanthropist, diplomat, and television talk show host", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American socialite, businesswoman, political activist, philanthropist, diplomat, and television talk show host", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1930", "text": "<PERSON>, American clarinet player (d. 2016)", "html": "1930 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American clarinet player (d. 2016)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Fountain\"><PERSON></a>, American clarinet player (d. 2016)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1930", "text": "<PERSON>, German-Austrian conductor (d. 2004)", "html": "1930 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German-Austrian conductor (d. 2004)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German-Austrian conductor (d. 2004)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1930", "text": "<PERSON>, American guitarist (d. 1997)", "html": "1930 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American guitarist (d. 1997)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American guitarist (d. 1997)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1932", "text": "<PERSON>, American businessman (d. 2014)", "html": "1932 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American businessman (d. 2014)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American businessman (d. 2014)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1933", "text": "<PERSON>, Jr., American physician and mathematician (d. 2007)", "html": "1933 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Jr.\" class=\"mw-redirect\" title=\"<PERSON>, Jr.\"><PERSON>, Jr.</a>, American physician and mathematician (d. 2007)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>,_Jr.\" class=\"mw-redirect\" title=\"<PERSON>, Jr.\"><PERSON>, Jr.</a>, American physician and mathematician (d. 2007)", "links": [{"title": "<PERSON>, Jr.", "link": "https://wikipedia.org/wiki/<PERSON>,_Jr."}]}, {"year": "1935", "text": "<PERSON><PERSON>, Puerto Rican-American singer-songwriter (d. 2014)", "html": "1935 - <a href=\"https://wikipedia.org/wiki/<PERSON>eo_<PERSON><PERSON>\" title=\"Cheo Fe<PERSON>iano\"><PERSON><PERSON></a>, Puerto Rican-American singer-songwriter (d. 2014)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>eo_<PERSON><PERSON>iano\" title=\"Cheo Feliciano\"><PERSON><PERSON></a>, Puerto Rican-American singer-songwriter (d. 2014)", "links": [{"title": "<PERSON>eo <PERSON>", "link": "https://wikipedia.org/wiki/Cheo_Fe<PERSON>iano"}]}, {"year": "1935", "text": "<PERSON>, American geologist, astronaut, and politician", "html": "1935 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American geologist, astronaut, and politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American geologist, astronaut, and politician", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1936", "text": "<PERSON>, <PERSON> of Herne Hill, English lawyer and politician (d. 2020)", "html": "1936 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Baron_<PERSON>_of_Herne_Hill\" title=\"<PERSON>, Baron <PERSON> of Herne Hill\"><PERSON>, Baron <PERSON> of Herne Hill</a>, English lawyer and politician (d. 2020)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Baron_<PERSON>_of_Herne_Hill\" title=\"<PERSON>, Baron <PERSON> of Herne Hill\"><PERSON>, Baron <PERSON> of Herne Hill</a>, English lawyer and politician (d. 2020)", "links": [{"title": "<PERSON>, Baron <PERSON> of Herne Hill", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_<PERSON>_<PERSON>_of_Herne_<PERSON>"}]}, {"year": "1936", "text": "<PERSON><PERSON>, Norwegian-Danish actor (d. 2017)", "html": "1936 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Norwegian-Danish actor (d. 2017)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Norwegian-Danish actor (d. 2017)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1937", "text": "<PERSON>, English philosopher and academic", "html": "1937 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English philosopher and academic", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English philosopher and academic", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1937", "text": "<PERSON>, Czech-English playwright and screenwriter", "html": "1937 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Czech-English playwright and screenwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Czech-English playwright and screenwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1938", "text": "<PERSON>, English linguist and academic", "html": "1938 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English linguist and academic", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English linguist and academic", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1938", "text": "<PERSON><PERSON><PERSON>, Dutch footballer", "html": "1938 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON> Swart\"><PERSON><PERSON><PERSON></a>, Dutch footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON> Swart\"><PERSON><PERSON><PERSON></a>, Dutch footballer", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1939", "text": "<PERSON><PERSON><PERSON>, German soprano and director", "html": "1939 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>ba<PERSON>\"><PERSON><PERSON><PERSON></a>, German soprano and director", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>ba<PERSON>\"><PERSON><PERSON><PERSON></a>, German soprano and director", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1939", "text": "<PERSON><PERSON><PERSON><PERSON><PERSON>, Hungarian politician and diplomat, Hungarian Minister of Foreign Affairs", "html": "1939 - <a href=\"https://wikipedia.org/wiki/L%C3%A1szl%C3%B3_Kov%C3%<PERSON><PERSON>_(politician)\" title=\"<PERSON><PERSON><PERSON><PERSON><PERSON> (politician)\"><PERSON><PERSON><PERSON><PERSON><PERSON></a>, Hungarian politician and diplomat, <a href=\"https://wikipedia.org/wiki/Minister_of_Foreign_Affairs_(Hungary)\" title=\"Minister of Foreign Affairs (Hungary)\">Hungarian Minister of Foreign Affairs</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/L%C3%A1szl%C3%B3_Kov%C3%<PERSON><PERSON>_(politician)\" title=\"<PERSON><PERSON><PERSON><PERSON><PERSON> (politician)\"><PERSON><PERSON><PERSON><PERSON><PERSON></a>, Hungarian politician and diplomat, <a href=\"https://wikipedia.org/wiki/Minister_of_Foreign_Affairs_(Hungary)\" title=\"Minister of Foreign Affairs (Hungary)\">Hungarian Minister of Foreign Affairs</a>", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON><PERSON> (politician)", "link": "https://wikipedia.org/wiki/L%C3%A1szl%C3%B3_Kov%C3%<PERSON><PERSON>_(politician)"}, {"title": "Minister of Foreign Affairs (Hungary)", "link": "https://wikipedia.org/wiki/Minister_of_Foreign_Affairs_(Hungary)"}]}, {"year": "1939", "text": "<PERSON><PERSON>, Puerto Rican baseball player", "html": "1939 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Puerto Rican baseball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Puerto Rican baseball player", "links": [{"title": "Coco <PERSON>oy", "link": "https://wikipedia.org/wiki/Coco_Laboy"}]}, {"year": "1940", "text": "<PERSON>, American lawyer and politician, 5th United States Secretary of Education", "html": "1940 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and politician, 5th <a href=\"https://wikipedia.org/wiki/United_States_Secretary_of_Education\" title=\"United States Secretary of Education\">United States Secretary of Education</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and politician, 5th <a href=\"https://wikipedia.org/wiki/United_States_Secretary_of_Education\" title=\"United States Secretary of Education\">United States Secretary of Education</a>", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "United States Secretary of Education", "link": "https://wikipedia.org/wiki/United_States_Secretary_of_Education"}]}, {"year": "1940", "text": "<PERSON><PERSON><PERSON>, Polish engineer and politician, 9th Prime Minister of Poland", "html": "1940 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Polish engineer and politician, 9th <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Poland\" title=\"Prime Minister of Poland\">Prime Minister of Poland</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Polish engineer and politician, 9th <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Poland\" title=\"Prime Minister of Poland\">Prime Minister of Poland</a>", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}, {"title": "Prime Minister of Poland", "link": "https://wikipedia.org/wiki/Prime_Minister_of_Poland"}]}, {"year": "1940", "text": "<PERSON>, American actor (d. 2024)", "html": "1940 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(actor)\" title=\"<PERSON> (actor)\"><PERSON></a>, American actor (d. 2024)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>(actor)\" title=\"<PERSON> (actor)\"><PERSON></a>, American actor (d. 2024)", "links": [{"title": "<PERSON> (actor)", "link": "https://wikipedia.org/wiki/<PERSON>(actor)"}]}, {"year": "1940", "text": "<PERSON>, American swimmer (d. 2024)", "html": "1940 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American swimmer (d. 2024)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American swimmer (d. 2024)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1940", "text": "<PERSON>, Venezuelan baseball player (d. 1994)", "html": "1940 - <a href=\"https://wikipedia.org/wiki/C%C3%A9<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Venezuelan baseball player (d. 1994)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/C%C3%A9<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Venezuelan baseball player (d. 1994)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/C%C3%A9sar_<PERSON>var"}]}, {"year": "1941", "text": "<PERSON>, American lawyer and activist", "html": "1941 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and activist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and activist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_Allred"}]}, {"year": "1941", "text": "<PERSON><PERSON>, Algerian politician, 4th President of Algeria", "html": "1941 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_Z%C3%A9roual\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Algerian politician, 4th President of Algeria", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_Z%C3%A9roual\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Algerian politician, 4th President of Algeria", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Liamine_Z%C3%A9roual"}]}, {"year": "1942", "text": "<PERSON>, French singer-songwriter", "html": "1942 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French singer-songwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French singer-songwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1943", "text": "<PERSON>, British actor (d. 2022)", "html": "1943 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, British actor (d. 2022)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, British actor (d. 2022)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1943", "text": "<PERSON>, Australian folk-pop singer-songwriter and musician (d. 2022)", "html": "1943 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian folk-pop singer-songwriter and musician (d. 2022)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Judith_<PERSON>\" title=\"Judith <PERSON>\"><PERSON></a>, Australian folk-pop singer-songwriter and musician (d. 2022)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Judith_<PERSON>"}]}, {"year": "1943", "text": "<PERSON><PERSON>, American actor", "html": "1943 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American actor", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1943", "text": "<PERSON>, American astronaut", "html": "1943 - <a href=\"https://wikipedia.org/wiki/Norman_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, American astronaut", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Norman_<PERSON><PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, American astronaut", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON><PERSON>"}]}, {"year": "1945", "text": "<PERSON>, Baron <PERSON> of Springburn, Scottish politician, Speaker of the House of Commons (d. 2018)", "html": "1945 - <a href=\"https://wikipedia.org/wiki/1945\" title=\"1945\">1945</a> - <a href=\"https://wikipedia.org/wiki/<PERSON>,_Baron_<PERSON>_of_Springburn\" title=\"<PERSON>, Baron <PERSON> of Springburn\"><PERSON>, Baron <PERSON> of Springburn</a>, Scottish politician, <a href=\"https://wikipedia.org/wiki/Speaker_of_the_House_of_Commons_(United_Kingdom)\" title=\"Speaker of the House of Commons (United Kingdom)\">Speaker of the House of Commons</a> (d. 2018)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/1945\" title=\"1945\">1945</a> - <a href=\"https://wikipedia.org/wiki/<PERSON>,_Baron_<PERSON>_of_Springburn\" title=\"<PERSON>, Baron <PERSON> of Springburn\"><PERSON>, Baron <PERSON> of Springburn</a>, Scottish politician, <a href=\"https://wikipedia.org/wiki/Speaker_of_the_House_of_Commons_(United_Kingdom)\" title=\"Speaker of the House of Commons (United Kingdom)\">Speaker of the House of Commons</a> (d. 2018)", "links": [{"title": "1945", "link": "https://wikipedia.org/wiki/1945"}, {"title": "<PERSON>, Baron <PERSON> of Springburn", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Baron_<PERSON>_of_Springburn"}, {"title": "Speaker of the House of Commons (United Kingdom)", "link": "https://wikipedia.org/wiki/Speaker_of_the_House_of_Commons_(United_Kingdom)"}]}, {"year": "1946", "text": "<PERSON>, American singer and guitarist", "html": "1946 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(singer)\" title=\"<PERSON> (singer)\"><PERSON></a>, American singer and guitarist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(singer)\" title=\"<PERSON> (singer)\"><PERSON></a>, American singer and guitarist", "links": [{"title": "<PERSON> (singer)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>(singer)"}]}, {"year": "1946", "text": "<PERSON><PERSON><PERSON>, Polish political scientist and politician, 10th Prime Minister of Poland", "html": "1946 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Polish political scientist and politician, 10th <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Poland\" title=\"Prime Minister of Poland\">Prime Minister of Poland</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Polish political scientist and politician, 10th <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Poland\" title=\"Prime Minister of Poland\">Prime Minister of Poland</a>", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}, {"title": "Prime Minister of Poland", "link": "https://wikipedia.org/wiki/Prime_Minister_of_Poland"}]}, {"year": "1946", "text": "<PERSON>, American author (d. 2014)", "html": "1946 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(author)\" class=\"mw-redirect\" title=\"<PERSON> (author)\"><PERSON></a>, American author (d. 2014)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>(author)\" class=\"mw-redirect\" title=\"<PERSON> (author)\"><PERSON></a>, American author (d. 2014)", "links": [{"title": "<PERSON> (author)", "link": "https://wikipedia.org/wiki/<PERSON>(author)"}]}, {"year": "1947", "text": "<PERSON>, American journalist and author", "html": "1947 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American journalist and author", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American journalist and author", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1947", "text": "<PERSON>, American actress and singer", "html": "1947 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress and singer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress and singer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1947", "text": "<PERSON>, American swimmer", "html": "1947 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(swimmer)\" title=\"<PERSON> (swimmer)\"><PERSON></a>, American swimmer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>(swimmer)\" title=\"<PERSON> (swimmer)\"><PERSON></a>, American swimmer", "links": [{"title": "<PERSON> (swimmer)", "link": "https://wikipedia.org/wiki/<PERSON>(swimmer)"}]}, {"year": "1948", "text": "<PERSON>, American singer-songwriter and guitarist (d. 2019)", "html": "1948 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and guitarist (d. 2019)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and guitarist (d. 2019)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1948", "text": "<PERSON><PERSON><PERSON>, Finnish author and illustrator", "html": "1948 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Finnish author and illustrator", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Finnish author and illustrator", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>rm<PERSON>_<PERSON>o"}]}, {"year": "1949", "text": "<PERSON>, English actress", "html": "1949 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actress", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1949", "text": "<PERSON>, English guitarist", "html": "1949 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English guitarist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English guitarist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1949", "text": "<PERSON><PERSON>, Jr., American singer (d. 2006)", "html": "1949 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>,_Jr.\" class=\"mw-redirect\" title=\"<PERSON><PERSON>, Jr.\"><PERSON><PERSON>, Jr.</a>, American singer (d. 2006)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>,_Jr.\" class=\"mw-redirect\" title=\"<PERSON><PERSON>, Jr.\"><PERSON><PERSON>, Jr.</a>, American singer (d. 2006)", "links": [{"title": "<PERSON><PERSON>, Jr.", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>,_Jr."}]}, {"year": "1949", "text": "<PERSON>, Chinese politician, Chinese Minister of Commerce", "html": "1949 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>\" title=\"<PERSON>\"><PERSON></a>, Chinese politician, <a href=\"https://wikipedia.org/wiki/Ministry_of_Commerce_of_the_People%27s_Republic_of_China\" class=\"mw-redirect\" title=\"Ministry of Commerce of the People's Republic of China\">Chinese Minister of Commerce</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>\" title=\"<PERSON>\"><PERSON></a>, Chinese politician, <a href=\"https://wikipedia.org/wiki/Ministry_of_Commerce_of_the_People%27s_Republic_of_China\" class=\"mw-redirect\" title=\"Ministry of Commerce of the People's Republic of China\">Chinese Minister of Commerce</a>", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Ministry of Commerce of the People's Republic of China", "link": "https://wikipedia.org/wiki/Ministry_of_Commerce_of_the_People%27s_Republic_of_China"}]}, {"year": "1950", "text": "<PERSON><PERSON>, New Zealand cricketer", "html": "1950 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, New Zealand cricketer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, New Zealand cricketer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1950", "text": "<PERSON>, American judge and politician, 40th Mayor of Los Angeles", "html": "1950 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American judge and politician, 40th <a href=\"https://wikipedia.org/wiki/Mayor_of_Los_Angeles\" title=\"Mayor of Los Angeles\">Mayor of Los Angeles</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American judge and politician, 40th <a href=\"https://wikipedia.org/wiki/Mayor_of_Los_Angeles\" title=\"Mayor of Los Angeles\">Mayor of Los Angeles</a>", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Mayor of Los Angeles", "link": "https://wikipedia.org/wiki/Mayor_of_Los_Angeles"}]}, {"year": "1951", "text": "<PERSON><PERSON><PERSON>, Haitian politician, 41st President of Haiti (d. 2014)", "html": "1951 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Haitian politician, 41st <a href=\"https://wikipedia.org/wiki/President_of_Haiti\" title=\"President of Haiti\">President of Haiti</a> (d. 2014)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Haitian politician, 41st <a href=\"https://wikipedia.org/wiki/President_of_Haiti\" title=\"President of Haiti\">President of Haiti</a> (d. 2014)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>"}, {"title": "President of Haiti", "link": "https://wikipedia.org/wiki/President_of_Haiti"}]}, {"year": "1951", "text": "<PERSON>, New Zealand cricketer", "html": "1951 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, New Zealand cricketer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, New Zealand cricketer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1952", "text": "<PERSON>, American singer-songwriter (d. 2004)", "html": "1952 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter (d. 2004)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter (d. 2004)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1952", "text": "<PERSON>, Italian singer", "html": "1952 - <a href=\"https://wikipedia.org/wiki/Lu_<PERSON>\" title=\"Lu Colombo\"><PERSON></a>, Italian singer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Lu_Colombo\" title=\"Lu Colombo\"><PERSON></a>, Italian singer", "links": [{"title": "Lu Colombo", "link": "https://wikipedia.org/wiki/Lu_Colombo"}]}, {"year": "1952", "text": "<PERSON>, English singer-songwriter and bass player (d. 2015)", "html": "1952 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English singer-songwriter and bass player (d. 2015)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English singer-songwriter and bass player (d. 2015)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1952", "text": "<PERSON>, American singer-songwriter and music producer", "html": "1952 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and music producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and music producer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1952", "text": "<PERSON><PERSON>, Pakistani cricketer (d. 2006)", "html": "1952 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Pakistani cricketer (d. 2006)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Pakistani cricketer (d. 2006)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1952", "text": "<PERSON><PERSON>, Indian film playback singer, actor, director, music director and musician", "html": "1952 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_(singer)\" class=\"mw-redirect\" title=\"<PERSON><PERSON> (singer)\"><PERSON><PERSON></a>, Indian film playback singer, actor, director, music director and musician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_(singer)\" class=\"mw-redirect\" title=\"<PERSON><PERSON> (singer)\"><PERSON><PERSON></a>, Indian film playback singer, actor, director, music director and musician", "links": [{"title": "<PERSON><PERSON> (singer)", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_(singer)"}]}, {"year": "1953", "text": "<PERSON><PERSON>, Swedish alpine skier", "html": "1953 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Swedish alpine skier", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Swedish alpine skier", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_Sollander"}]}, {"year": "1954", "text": "<PERSON>, English rugby player", "html": "1954 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English rugby player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English rugby player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1955", "text": "<PERSON>, Canadian radio and television host", "html": "1955 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian radio and television host", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian radio and television host", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1956", "text": "<PERSON><PERSON>, American talk show host and television personality", "html": "1956 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American talk show host and television personality", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American talk show host and television personality", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1957", "text": "<PERSON><PERSON>, British musician (d. 2011)", "html": "1957 - <a href=\"https://wikipedia.org/wiki/Poly_Styrene\" title=\"Poly Styrene\"><PERSON><PERSON></a>, British musician (d. 2011)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Poly_Styrene\" title=\"Poly Styrene\"><PERSON><PERSON></a>, British musician (d. 2011)", "links": [{"title": "Poly Styrene", "link": "https://wikipedia.org/wiki/Poly_Styrene"}]}, {"year": "1958", "text": "<PERSON>, Canadian-English journalist and academic", "html": "1958 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(journalist)\" title=\"<PERSON> (journalist)\"><PERSON></a>, Canadian-English journalist and academic", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(journalist)\" title=\"<PERSON> (journalist)\"><PERSON></a>, Canadian-English journalist and academic", "links": [{"title": "<PERSON> (journalist)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(journalist)"}]}, {"year": "1958", "text": "<PERSON>, English actor, singer, and author", "html": "1958 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actor, singer, and author", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actor, singer, and author", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1958", "text": "<PERSON><PERSON>, Welsh meteorologist and journalist", "html": "1958 - <a href=\"https://wikipedia.org/wiki/Si%C3%A2n_Lloyd\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Welsh meteorologist and journalist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Si%C3%A2n_Lloyd\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Welsh meteorologist and journalist", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Si%C3%A2n_Lloyd"}]}, {"year": "1958", "text": "<PERSON><PERSON>, Swiss-Canadian painter", "html": "1958 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Swiss-Canadian painter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Swiss-Canadian painter", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1958", "text": "<PERSON>, American singer-songwriter, guitarist, and producer", "html": "1958 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter, guitarist, and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter, guitarist, and producer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1959", "text": "<PERSON>, English journalist and author", "html": "1959 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English journalist and author", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English journalist and author", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1959", "text": "<PERSON>-<PERSON>, American screenwriter and producer", "html": "1959 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American screenwriter and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American screenwriter and producer", "links": [{"title": "<PERSON>-<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1959", "text": "<PERSON>, American singer-songwriter and guitarist", "html": "1959 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and guitarist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and guitarist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1959", "text": "<PERSON>, Canadian screenwriter and producer", "html": "1959 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"David <PERSON>\"><PERSON></a>, Canadian screenwriter and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"David Shore\"><PERSON></a>, Canadian screenwriter and producer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1960", "text": "<PERSON>, English singer-songwriter, keyboard player, and producer", "html": "1960 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English singer-songwriter, keyboard player, and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English singer-songwriter, keyboard player, and producer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1962", "text": "<PERSON>, American record executive and entrepreneur", "html": "1962 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American record executive and entrepreneur", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American record executive and entrepreneur", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1962", "text": "<PERSON>, American actor and producer", "html": "1962 - <a href=\"https://wikipedia.org/wiki/<PERSON>_Cruise\" title=\"Tom Cruise\"><PERSON></a>, American actor and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Tom_Cruise\" title=\"Tom Cruise\"><PERSON></a>, American actor and producer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1962", "text": "<PERSON>, American actor and director", "html": "1962 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor and director", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor and director", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1963", "text": "<PERSON><PERSON>, British Artist", "html": "1963 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_Em<PERSON>\" title=\"<PERSON><PERSON> Emin\"><PERSON><PERSON></a>, British Artist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_Em<PERSON>\" title=\"<PERSON><PERSON> Emin\"><PERSON><PERSON></a>, British Artist", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_Emin"}]}, {"year": "1964", "text": "<PERSON><PERSON>, American actress, voice actress, comedian and writer", "html": "1964 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American actress, voice actress, comedian and writer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American actress, voice actress, comedian and writer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1965", "text": "<PERSON><PERSON>, Japanese wrestler (d. 2005)", "html": "1965 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Japanese wrestler (d. 2005)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Japanese wrestler (d. 2005)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>"}]}, {"year": "1965", "text": "<PERSON>, Danish-American actress", "html": "1965 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Danish-American actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Danish-American actress", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1965", "text": "<PERSON><PERSON><PERSON>, Thai lawyer and academic", "html": "1965 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Thai lawyer and academic", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Thai lawyer and academic", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>msan_Po<PERSON>g"}]}, {"year": "1965", "text": "<PERSON>, French pentathlete (d. 2007)", "html": "1965 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French pentathlete (d. 2007)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French pentathlete (d. 2007)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1966", "text": "<PERSON><PERSON><PERSON>, American baseball player", "html": "1966 - <a href=\"https://wikipedia.org/wiki/Mois%C3%A9s_<PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American baseball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Mois%C3%A9s_<PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American baseball player", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Mois%C3%A9s_Alou"}]}, {"year": "1967", "text": "<PERSON>, Scottish lawyer and politician", "html": "1967 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish lawyer and politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish lawyer and politician", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1968", "text": "<PERSON><PERSON>, Kosovo-Albanian soldier and politician, 4th Prime Minister of Kosovo", "html": "1968 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Kosovo-Albanian soldier and politician, 4th <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Kosovo\" title=\"Prime Minister of Kosovo\">Prime Minister of Kosovo</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Kosovo-Albanian soldier and politician, 4th <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Kosovo\" title=\"Prime Minister of Kosovo\">Prime Minister of Kosovo</a>", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>j"}, {"title": "Prime Minister of Kosovo", "link": "https://wikipedia.org/wiki/Prime_Minister_of_Kosovo"}]}, {"year": "1970", "text": "<PERSON><PERSON><PERSON>, Ukrainian cyclist", "html": "1970 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Ukrainian cyclist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Ukrainian cyclist", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON><PERSON>r"}]}, {"year": "1970", "text": "<PERSON><PERSON>, American actress and singer", "html": "1970 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American actress and singer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American actress and singer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1970", "text": "<PERSON><PERSON><PERSON>, Finnish ice hockey player", "html": "1970 - <a href=\"https://wikipedia.org/wiki/Teemu_Sel%C3%A4nne\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Finnish ice hockey player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Teemu_Sel%C3%A4nne\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Finnish ice hockey player", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Teemu_Sel%C3%A4nne"}]}, {"year": "1971", "text": "<PERSON>, Australian journalist, publisher, and activist, founded WikiLeaks", "html": "1971 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian journalist, publisher, and activist, founded <a href=\"https://wikipedia.org/wiki/WikiLeaks\" title=\"WikiLeaks\">WikiLeaks</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian journalist, publisher, and activist, founded <a href=\"https://wikipedia.org/wiki/WikiLeaks\" title=\"WikiLeaks\">WikiLeaks</a>", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "WikiLeaks", "link": "https://wikipedia.org/wiki/WikiLeaks"}]}, {"year": "1971", "text": "<PERSON>, English actor", "html": "1971 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1973", "text": "<PERSON>, New Zealand rugby league player", "html": "1973 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, New Zealand rugby league player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, New Zealand rugby league player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1973", "text": "<PERSON><PERSON><PERSON><PERSON>, Icelandic handball player", "html": "1973 - <a href=\"https://wikipedia.org/wiki/%C3%93lafur_Stef%C3%<PERSON><PERSON>son\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Icelandic handball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/%C3%93lafur_Stef%C3%<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Icelandic handball player", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/%C3%93lafur_Stef%C3%A1<PERSON>son"}]}, {"year": "1973", "text": "<PERSON><PERSON><PERSON>, Russian footballer (d. 2013)", "html": "1973 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Russian footballer (d. 2013)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Russian footballer (d. 2013)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1973", "text": "<PERSON>, American actor", "html": "1973 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1976", "text": "<PERSON>, Canadian ice hockey player (d. 2011)", "html": "1976 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player (d. 2011)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player (d. 2011)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1976", "text": "<PERSON>, Zimbabwean cricketer and sportscaster", "html": "1976 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Zimbabwean cricketer and sportscaster", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Zimbabwean cricketer and sportscaster", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1976", "text": "<PERSON><PERSON><PERSON><PERSON>, Brazilian-American mixed martial artist", "html": "1976 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Brazilian-American mixed martial artist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Brazilian-American mixed martial artist", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1976", "text": "<PERSON>, Zimbabwean-South African rugby union player", "html": "1976 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Zimbabwean-South African rugby union player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Zimbabwean-South African rugby union player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1977", "text": "<PERSON>, American football player", "html": "1977 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1978", "text": "<PERSON><PERSON>, Japanese runner", "html": "1978 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Japanese runner", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Japanese runner", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1979", "text": "<PERSON>, English cricketer", "html": "1979 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English cricketer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"Jamie <PERSON>\"><PERSON></a>, English cricketer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1980", "text": "<PERSON><PERSON><PERSON><PERSON>, Bangladeshi cricketer (d. 2013)", "html": "1980 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Bangladeshi cricketer (d. 2013)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"Ma<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Bangladeshi cricketer (d. 2013)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1980", "text": "<PERSON>, American actress and television host", "html": "1980 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress and television host", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress and television host", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1980", "text": "<PERSON>, South African swimmer", "html": "1980 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, South African swimmer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, South African swimmer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1980", "text": "<PERSON><PERSON><PERSON><PERSON>, Indian cricketer", "html": "1980 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Indian cricketer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Indian cricketer", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1983", "text": "<PERSON><PERSON><PERSON>, Dominican baseball player", "html": "1983 - <a href=\"https://wikipedia.org/wiki/Edinson_V%C3%B3l<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Dominican baseball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Edinson_V%C3%B3l<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Dominican baseball player", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Edinson_V%C3%B3lquez"}]}, {"year": "1984", "text": "<PERSON>, American football player", "html": "1984 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1984", "text": "<PERSON><PERSON><PERSON>, Dutch sprinter", "html": "1984 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Dutch sprinter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Dutch sprinter", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1984", "text": "<PERSON>, Canadian actor and producer", "html": "1984 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian actor and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian actor and producer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1986", "text": "<PERSON>, Brazilian footballer", "html": "1986 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%B4<PERSON>_de_Mattos_Filho\" class=\"mw-redirect\" title=\"<PERSON> Fi<PERSON>ho\"><PERSON></a>, Brazilian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%B4nio_de_Mattos_Filho\" class=\"mw-redirect\" title=\"<PERSON> Filho\"><PERSON></a>, Brazilian footballer", "links": [{"title": "<PERSON> Mattos Fi<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_An<PERSON>%C3%B4<PERSON>_<PERSON>_<PERSON>_Fi<PERSON>ho"}]}, {"year": "1986", "text": "<PERSON><PERSON><PERSON><PERSON>, Japanese sumo wrestler", "html": "1986 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Japanese sumo wrestler", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Japanese sumo wrestler", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Kisenosato_Yutaka"}]}, {"year": "1987", "text": "<PERSON>, German race car driver", "html": "1987 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German race car driver", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German race car driver", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1988", "text": "<PERSON>, New Zealand-Danish footballer", "html": "1988 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, New Zealand-Danish footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, New Zealand-Danish footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1988", "text": "<PERSON><PERSON>, Russian figure skater", "html": "1988 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Russian figure skater", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Russian figure skater", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1988", "text": "<PERSON>, Australian footballer", "html": "1988 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1989", "text": "<PERSON><PERSON>, Brazilian convicted murderer", "html": "1989 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Brazilian convicted murderer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Brazilian convicted murderer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>lo_Cavalcante"}]}, {"year": "1989", "text": "<PERSON>, Australian rugby league player", "html": "1989 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian rugby league player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian rugby league player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1989", "text": "<PERSON>, American singer, songwriter, and actress", "html": "1989 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> King\"><PERSON></a>, American singer, songwriter, and actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> King\"><PERSON></a>, American singer, songwriter, and actress", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1990", "text": "<PERSON>, Australian rugby league player", "html": "1990 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian rugby league player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian rugby league player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1990", "text": "<PERSON>, English footballer", "html": "1990 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1990", "text": "<PERSON>, Brazilian footballer", "html": "1990 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(footballer,_born_1990)\" title=\"<PERSON> (footballer, born 1990)\"><PERSON></a>, Brazilian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(footballer,_born_1990)\" title=\"<PERSON> (footballer, born 1990)\"><PERSON></a>, Brazilian footballer", "links": [{"title": "<PERSON> (footballer, born 1990)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(footballer,_born_1990)"}]}, {"year": "1990", "text": "<PERSON><PERSON>, American tennis player", "html": "1990 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American tennis player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American tennis player", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1991", "text": "<PERSON>, Scottish field hockey player", "html": "1991 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish field hockey player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish field hockey player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>"}]}, {"year": "1991", "text": "<PERSON>, Russian tennis player", "html": "1991 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian tennis player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian tennis player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Anastasia_<PERSON>"}]}, {"year": "1992", "text": "<PERSON>, American soccer player", "html": "1992 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Dunn\"><PERSON></a>, American soccer player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Dunn\"><PERSON></a>, American soccer player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_Dunn"}]}, {"year": "1993", "text": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, Canadian singer-songwriter and record producer", "html": "1993 - <a href=\"https://wikipedia.org/wiki/PartyNextDoor\" title=\"PartyNextDoor\">PartyNextD<PERSON></a>, Canadian singer-songwriter and record producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/PartyNextDoor\" title=\"PartyNextDoor\">PartyNextD<PERSON></a>, Canadian singer-songwriter and record producer", "links": [{"title": "PartyNextDoor", "link": "https://wikipedia.org/wiki/PartyNextDoor"}]}, {"year": "1994", "text": "<PERSON>, American football player", "html": "1994 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(defensive_tackle,_born_1994)\" title=\"<PERSON> (defensive tackle, born 1994)\"><PERSON></a>, American football player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(defensive_tackle,_born_1994)\" title=\"<PERSON> (defensive tackle, born 1994)\"><PERSON></a>, American football player", "links": [{"title": "<PERSON> (defensive tackle, born 1994)", "link": "https://wikipedia.org/wiki/<PERSON>_(defensive_tackle,_born_1994)"}]}, {"year": "1996", "text": "<PERSON>, American baseball player", "html": "1996 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1997", "text": "<PERSON><PERSON> <PERSON><PERSON>, American football player", "html": "1997 - <a href=\"https://wikipedia.org/wiki/T._<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> <PERSON><PERSON>\"><PERSON><PERSON> <PERSON><PERSON></a>, American football player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/T._<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> <PERSON><PERSON>\"><PERSON><PERSON> <PERSON><PERSON></a>, American football player", "links": [{"title": "<PERSON><PERSON> <PERSON><PERSON>", "link": "https://wikipedia.org/wiki/T._<PERSON><PERSON>_<PERSON>"}]}, {"year": "1999", "text": "<PERSON><PERSON><PERSON>, Bosnian tennis player", "html": "1999 - <a href=\"https://wikipedia.org/wiki/Nefisa_Berberovi%C4%87\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Bosnian tennis player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Nefisa_Berberovi%C4%87\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Bosnian tennis player", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Nefisa_Berberovi%C4%87"}]}], "Deaths": [{"year": "458", "text": "<PERSON><PERSON><PERSON> of Constantinople, Byzantine patriarch and saint (b. 449)", "html": "458 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_of_Constantinople\" title=\"<PERSON><PERSON><PERSON> of Constantinople\"><PERSON><PERSON><PERSON> of Constantinople</a>, Byzantine patriarch and saint (b. 449)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_of_Constantinople\" title=\"<PERSON><PERSON><PERSON> of Constantinople\"><PERSON><PERSON><PERSON> of Constantinople</a>, Byzantine patriarch and saint (b. 449)", "links": [{"title": "<PERSON><PERSON><PERSON> of Constantinople", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_of_Constantinople"}]}, {"year": "710", "text": "Emperor <PERSON><PERSON><PERSON> of Tang (b. 656)", "html": "710 - <a href=\"https://wikipedia.org/wiki/Emperor_<PERSON><PERSON><PERSON>_of_Tang\" title=\"Emperor <PERSON><PERSON><PERSON> of Tang\">Emperor <PERSON><PERSON><PERSON> of Tang</a> (b. 656)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Emperor_<PERSON><PERSON><PERSON>_of_Tang\" title=\"Emperor <PERSON><PERSON><PERSON> of Tang\">Emperor <PERSON><PERSON><PERSON> of Tang</a> (b. 656)", "links": [{"title": "Emperor <PERSON><PERSON><PERSON> of Tang", "link": "https://wikipedia.org/wiki/Emperor_<PERSON><PERSON><PERSON>_of_Tang"}]}, {"year": "896", "text": "<PERSON>, Chinese warlord", "html": "896 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(warlord)\" title=\"<PERSON> (warlord)\"><PERSON></a>, Chinese warlord", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(warlord)\" title=\"<PERSON> (warlord)\"><PERSON></a>, Chinese warlord", "links": [{"title": "<PERSON> (warlord)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(warlord)"}]}, {"year": "964", "text": "<PERSON>, Frankish nobleman and archbishop", "html": "964 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(archbishop_of_Trier)\" title=\"<PERSON> (archbishop of Trier)\"><PERSON></a>, <PERSON><PERSON> <a href=\"https://wikipedia.org/wiki/Nobility\" title=\"Nobility\">nobleman</a> and archbishop", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(archbishop_of_Trier)\" title=\"<PERSON> (archbishop of Trier)\"><PERSON> I</a>, <PERSON><PERSON> <a href=\"https://wikipedia.org/wiki/Nobility\" title=\"Nobility\">nobleman</a> and archbishop", "links": [{"title": "<PERSON> (archbishop of Trier)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(archbishop_of_Trier)"}, {"title": "Nobility", "link": "https://wikipedia.org/wiki/Nobility"}]}, {"year": "1090", "text": "<PERSON><PERSON><PERSON> <PERSON>, Margrave of Meissen (b. c. 1060)", "html": "1090 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>,_<PERSON><PERSON>_of_Meissen\" title=\"<PERSON><PERSON><PERSON> <PERSON>, Mar<PERSON> of Meissen\"><PERSON><PERSON><PERSON> <PERSON>, <PERSON><PERSON> of Meissen</a> (b. c. 1060)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>,_<PERSON><PERSON>_of_Meissen\" title=\"<PERSON><PERSON><PERSON> <PERSON>, <PERSON><PERSON> of Meissen\"><PERSON><PERSON><PERSON> <PERSON>, <PERSON><PERSON> of Meissen</a> (b. c. 1060)", "links": [{"title": "<PERSON><PERSON><PERSON> <PERSON>, Margrave of Meissen", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>,_<PERSON><PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1288", "text": "<PERSON>,  English-born Irish cleric and politician", "html": "1288 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English-born Irish cleric and politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English-born Irish cleric and politician", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1503", "text": "<PERSON>, Grand Master of the Knights of Rhodes (b. 1423)", "html": "1503 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%27Aub<PERSON>on\" title=\"<PERSON>\"><PERSON></a>, Grand Master of the Knights of Rhodes (b. 1423)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%27A<PERSON><PERSON><PERSON>\" title=\"<PERSON>\"><PERSON></a>, Grand Master of the Knights of Rhodes (b. 1423)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>%27<PERSON><PERSON><PERSON><PERSON>"}]}, {"year": "1570", "text": "<PERSON><PERSON><PERSON>, Italian academic and reformer (b. 1500)", "html": "1570 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Italian academic and reformer (b. 1500)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Italian academic and reformer (b. 1500)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1642", "text": "<PERSON>, French queen consort and regent (b. 1573)", "html": "1642 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%27_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French queen consort and regent (b. 1573)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%27_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French queen consort and regent (b. 1573)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>%27_<PERSON>"}]}, {"year": "1672", "text": "<PERSON>, English ornithologist and ichthyologist (b. 1635)", "html": "1672 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English ornithologist and ichthyologist (b. 1635)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English ornithologist and ichthyologist (b. 1635)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1790", "text": "<PERSON><PERSON><PERSON>, French geologist and mineralogist (b. 1736)", "html": "1790 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_Rom%C3%A9_de_l%27Isle\" title=\"<PERSON><PERSON><PERSON>'Isle\"><PERSON><PERSON><PERSON>Isle</a>, French geologist and mineralogist (b. 1736)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_Rom%C3%A9_de_l%27Isle\" title=\"<PERSON><PERSON><PERSON>'Isle\"><PERSON><PERSON><PERSON>Isle</a>, French geologist and mineralogist (b. 1736)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_Rom%C3%A9_de_l%27Isle"}]}, {"year": "1795", "text": "<PERSON><PERSON><PERSON>, French scholar and author (b. 1714)", "html": "1795 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>r%C3%A9<PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, French scholar and author (b. 1714)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>r%C3%A9<PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, French scholar and author (b. 1714)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>r%C3%A9<PERSON><PERSON>"}]}, {"year": "1795", "text": "<PERSON>, Spanish general, astronomer, and politician, 1st Colonial Governor of Louisiana (b. 1716)", "html": "1795 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Spanish general, astronomer, and politician, 1st <a href=\"https://wikipedia.org/wiki/List_of_colonial_governors_of_Louisiana\" title=\"List of colonial governors of Louisiana\">Colonial Governor of Louisiana</a> (b. 1716)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Spanish general, astronomer, and politician, 1st <a href=\"https://wikipedia.org/wiki/List_of_colonial_governors_of_Louisiana\" title=\"List of colonial governors of Louisiana\">Colonial Governor of Louisiana</a> (b. 1716)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "List of colonial governors of Louisiana", "link": "https://wikipedia.org/wiki/List_of_colonial_governors_of_Louisiana"}]}, {"year": "1809", "text": "<PERSON>, French-Canadian composer and playwright (b. 1746)", "html": "1809 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French-Canadian composer and playwright (b. 1746)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French-Canadian composer and playwright (b. 1746)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1863", "text": "<PERSON>, American general (b. 1826)", "html": "1863 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"George Hull Ward\"><PERSON></a>, American general (b. 1826)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"George Hull Ward\"><PERSON></a>, American general (b. 1826)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_Ward"}]}, {"year": "1863", "text": "<PERSON>, American tribal leader (b. 1810)", "html": "1863 - <a href=\"https://wikipedia.org/wiki/Little_Crow\" title=\"Little Crow\">Little Crow</a>, American tribal leader (b. 1810)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Little_Crow\" title=\"Little Crow\">Little Crow</a>, American tribal leader (b. 1810)", "links": [{"title": "Little Crow", "link": "https://wikipedia.org/wiki/<PERSON>_Crow"}]}, {"year": "1881", "text": "<PERSON>, Albanian astronomer, mathematician, and philosopher (b. 1811)", "html": "1881 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Albanian astronomer, mathematician, and philosopher (b. 1811)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Albanian astronomer, mathematician, and philosopher (b. 1811)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1887", "text": "<PERSON>, American rancher (b. 1841)", "html": "1887 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American rancher (b. 1841)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American rancher (b. 1841)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1888", "text": "<PERSON><PERSON><PERSON><PERSON>, Vietnamese poet and author (b. 1822)", "html": "1888 - <a href=\"https://wikipedia.org/wiki/Nguy%E1%BB%85n_%C4%90%C3%ACnh_Chi%E1%BB%83u\" title=\"Nguyễn <PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Vietnamese poet and author (b. 1822)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Nguy%E1%BB%85n_%C4%90%C3%ACnh_Chi%E1%BB%83u\" title=\"Nguyễn <PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Vietnamese poet and author (b. 1822)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Nguy%E1%BB%85n_%C4%90%C3%ACnh_Chi%E1%BB%83u"}]}, {"year": "1904", "text": "<PERSON><PERSON><PERSON>, Canadian giant and strongman (b. 1881)", "html": "1904 - <a href=\"https://wikipedia.org/wiki/%C3%89douard_<PERSON>pr%C3%A9\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Canadian giant and strongman (b. 1881)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/%C3%89<PERSON><PERSON>_<PERSON>r%C3%A9\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Canadian giant and strongman (b. 1881)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/%C3%89douard_<PERSON>pr%C3%A9"}]}, {"year": "1904", "text": "<PERSON>, Austrian journalist, playwright, and father of modern political Zionism (b. 1860)", "html": "1904 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Austrian journalist, playwright, and father of modern political Zionism (b. 1860)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Austrian journalist, playwright, and father of modern political Zionism (b. 1860)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1908", "text": "<PERSON>, American journalist and author (b. 1845)", "html": "1908 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American journalist and author (b. 1845)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American journalist and author (b. 1845)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1916", "text": "<PERSON><PERSON>, American businesswoman and financier (b. 1834)", "html": "1916 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American businesswoman and financier (b. 1834)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American businesswoman and financier (b. 1834)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1918", "text": "<PERSON><PERSON><PERSON>, Ottoman sultan (b. 1844)", "html": "1918 - <a href=\"https://wikipedia.org/wiki/Mehmed_V\" title=\"Mehmed V\"><PERSON><PERSON><PERSON> <PERSON></a>, Ottoman sultan (b. 1844)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Mehm<PERSON>_V\" title=\"Mehmed V\"><PERSON><PERSON><PERSON> <PERSON></a>, Ottoman sultan (b. 1844)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Mehmed_V"}]}, {"year": "1921", "text": "<PERSON>, Irish-American weight thrower (b. 1864)", "html": "1921 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Irish-American weight thrower (b. 1864)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Irish-American weight thrower (b. 1864)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1927", "text": "<PERSON><PERSON>, French race car driver", "html": "1927 - <a href=\"https://wikipedia.org/wiki/G%C3%A9<PERSON><PERSON>_de_<PERSON>celles\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, French race car driver", "no_year_html": "<a href=\"https://wikipedia.org/wiki/G%C3%A9<PERSON><PERSON>_de_<PERSON>urcelles\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, French race car driver", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/G%C3%A9rar<PERSON>_<PERSON>_<PERSON>es"}]}, {"year": "1933", "text": "<PERSON><PERSON><PERSON><PERSON>, Argentinian educator and politician, 19th President of Argentina (b. 1852)", "html": "1933 - <a href=\"https://wikipedia.org/wiki/Hip%C3%B3lito_Yrigoyen\" title=\"Hipólito Yrigoyen\"><PERSON><PERSON><PERSON><PERSON></a>, Argentinian educator and politician, 19th <a href=\"https://wikipedia.org/wiki/President_of_Argentina\" title=\"President of Argentina\">President of Argentina</a> (b. 1852)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Hip%C3%B3lito_Yrigoyen\" title=\"Hipólito Yrigoyen\"><PERSON><PERSON><PERSON><PERSON></a>, Argentinian educator and politician, 19th <a href=\"https://wikipedia.org/wiki/President_of_Argentina\" title=\"President of Argentina\">President of Argentina</a> (b. 1852)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Hip%C3%B3lito_<PERSON><PERSON><PERSON><PERSON>"}, {"title": "President of Argentina", "link": "https://wikipedia.org/wiki/President_of_Argentina"}]}, {"year": "1935", "text": "<PERSON>, French engineer and businessman, founded the Citroën Company (b. 1878)", "html": "1935 - <a href=\"https://wikipedia.org/wiki/Andr%C3%A9_Citro%C3%ABn\" title=\"<PERSON>\"><PERSON></a>, French engineer and businessman, founded the <a href=\"https://wikipedia.org/wiki/Citro%C3%ABn\" title=\"Citroën\">Citroën Company</a> (b. 1878)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Andr%C3%A9_Citro%C3%ABn\" title=\"<PERSON>\"><PERSON></a>, French engineer and businessman, founded the <a href=\"https://wikipedia.org/wiki/Citro%C3%ABn\" title=\"Citroën\">Citroën Company</a> (b. 1878)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Andr%C3%A9_Citro%C3%ABn"}, {"title": "Citroën", "link": "https://wikipedia.org/wiki/Citro%C3%ABn"}]}, {"year": "1937", "text": "<PERSON>, American-Canadian captain and businessman, invented the electric razor (b. 1877)", "html": "1937 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American-Canadian captain and businessman, invented the <a href=\"https://wikipedia.org/wiki/Electric_razor\" class=\"mw-redirect\" title=\"Electric razor\">electric razor</a> (b. 1877)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American-Canadian captain and businessman, invented the <a href=\"https://wikipedia.org/wiki/Electric_razor\" class=\"mw-redirect\" title=\"Electric razor\">electric razor</a> (b. 1877)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}, {"title": "Electric razor", "link": "https://wikipedia.org/wiki/Electric_razor"}]}, {"year": "1941", "text": "<PERSON>, Estonian physician and politician, Head of State of Estonia (b. 1871)", "html": "1941 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Estonian physician and politician, <a href=\"https://wikipedia.org/wiki/Head_of_State_of_Estonia\" class=\"mw-redirect\" title=\"Head of State of Estonia\">Head of State of Estonia</a> (b. 1871)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Estonian physician and politician, <a href=\"https://wikipedia.org/wiki/Head_of_State_of_Estonia\" class=\"mw-redirect\" title=\"Head of State of Estonia\">Head of State of Estonia</a> (b. 1871)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Head of State of Estonia", "link": "https://wikipedia.org/wiki/Head_of_State_of_Estonia"}]}, {"year": "1954", "text": "<PERSON><PERSON><PERSON>, German physician and general (b. 1895)", "html": "1954 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, German physician and general (b. 1895)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, German physician and general (b. 1895)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1954", "text": "<PERSON>, French-American painter, illustrator, and academic (b. 1898)", "html": "1954 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(artist)\" title=\"<PERSON> (artist)\"><PERSON></a>, French-American painter, illustrator, and academic (b. 1898)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(artist)\" title=\"<PERSON> (artist)\"><PERSON></a>, French-American painter, illustrator, and academic (b. 1898)", "links": [{"title": "<PERSON> (artist)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(artist)"}]}, {"year": "1957", "text": "<PERSON><PERSON>, Cuban baseball player and manager (b. 1890)", "html": "1957 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Cuban baseball player and manager (b. 1890)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Cuban baseball player and manager (b. 1890)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1957", "text": "<PERSON>, German composer and <PERSON><PERSON><PERSON>eister (b. 1904)", "html": "1957 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German composer and <PERSON><PERSON><PERSON>eister (b. 1904)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German composer and <PERSON><PERSON><PERSON>eister (b. 1904)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1958", "text": "<PERSON>, 1st Viscount <PERSON>, English politician, 4th Governor-General of New Zealand (b. 1867)", "html": "1958 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_1st_Viscount_<PERSON>\" title=\"<PERSON>, 1st Viscount <PERSON>\"><PERSON>, 1st Viscount <PERSON></a>, English politician, 4th <a href=\"https://wikipedia.org/wiki/Governor-General_of_New_Zealand\" title=\"Governor-General of New Zealand\">Governor-General of New Zealand</a> (b. 1867)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>,_1st_Viscount_<PERSON>\" title=\"<PERSON>, 1st Viscount <PERSON>\"><PERSON>, 1st Viscount <PERSON></a>, English politician, 4th <a href=\"https://wikipedia.org/wiki/Governor-General_of_New_Zealand\" title=\"Governor-General of New Zealand\">Governor-General of New Zealand</a> (b. 1867)", "links": [{"title": "<PERSON>, 1st Viscount <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_1st_Viscount_<PERSON>"}, {"title": "Governor-General of New Zealand", "link": "https://wikipedia.org/wiki/Governor-General_of_New_Zealand"}]}, {"year": "1969", "text": "<PERSON>, English guitarist, songwriter, and producer (b. 1942)", "html": "1969 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English guitarist, songwriter, and producer (b. 1942)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English guitarist, songwriter, and producer (b. 1942)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1971", "text": "<PERSON>, American singer-songwriter (b. 1943)", "html": "1971 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter (b. 1943)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter (b. 1943)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1974", "text": "<PERSON>, American poet and critic (b. 1888)", "html": "1974 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON> Ransom\"><PERSON></a>, American poet and critic (b. 1888)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON> Ransom\"><PERSON></a>, American poet and critic (b. 1888)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1977", "text": "<PERSON>, Russian mathematician and author (b. 1891)", "html": "1977 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(writer)\" title=\"<PERSON> (writer)\"><PERSON></a>, Russian mathematician and author (b. 1891)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(writer)\" title=\"<PERSON> (writer)\"><PERSON></a>, Russian mathematician and author (b. 1891)", "links": [{"title": "<PERSON> (writer)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(writer)"}]}, {"year": "1978", "text": "<PERSON>, American actor (b. 1918)", "html": "1978 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(actor)\" title=\"<PERSON> (actor)\"><PERSON></a>, American actor (b. 1918)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>(actor)\" title=\"<PERSON> (actor)\"><PERSON></a>, American actor (b. 1918)", "links": [{"title": "<PERSON> (actor)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>(actor)"}]}, {"year": "1979", "text": "<PERSON>, French pianist and composer (b. 1888)", "html": "1979 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French pianist and composer (b. 1888)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French pianist and composer (b. 1888)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1981", "text": "<PERSON>, American actor and director (b. 1920)", "html": "1981 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor and director (b. 1920)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor and director (b. 1920)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1985", "text": "<PERSON>, Canadian ice hockey player and manager (b. 1893)", "html": "1985 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player and manager (b. 1893)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player and manager (b. 1893)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1986", "text": "<PERSON>, American singer, saxophonist, and actor (b. 1901)", "html": "1986 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A9e\" title=\"<PERSON>\"><PERSON></a>, American singer, saxophonist, and actor (b. 1901)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A9e\" title=\"<PERSON>\"><PERSON></a>, American singer, saxophonist, and actor (b. 1901)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A9e"}]}, {"year": "1989", "text": "<PERSON>, American actor and voice artist (b. 1913)", "html": "1989 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor and voice artist (b. 1913)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor and voice artist (b. 1913)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1993", "text": "<PERSON>, American baseball player and sportscaster (b. 1936)", "html": "1993 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player and sportscaster (b. 1936)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player and sportscaster (b. 1936)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Don_<PERSON>sdale"}]}, {"year": "1994", "text": "<PERSON><PERSON>, Australian tennis player and coach (b. 1934)", "html": "1994 - <a href=\"https://wikipedia.org/wiki/<PERSON>w_Hoad\" title=\"Lew Hoad\"><PERSON><PERSON></a>, Australian tennis player and coach (b. 1934)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>w_Hoad\" title=\"<PERSON>w Hoad\"><PERSON><PERSON></a>, Australian tennis player and coach (b. 1934)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Lew_Hoad"}]}, {"year": "1995", "text": "<PERSON><PERSON>, American tennis player (b. 1928)", "html": "1995 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_Go<PERSON>\" title=\"<PERSON><PERSON> Go<PERSON>\"><PERSON><PERSON></a>, American tennis player (b. 1928)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> Go<PERSON>les\"><PERSON><PERSON></a>, American tennis player (b. 1928)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Pan<PERSON>_<PERSON>les"}]}, {"year": "1995", "text": "<PERSON>, Canadian ice hockey player (b. 1929)", "html": "1995 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player (b. 1929)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player (b. 1929)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1998", "text": "<PERSON>, American game designer and programmer (b. 1949)", "html": "1998 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American game designer and programmer (b. 1949)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American game designer and programmer (b. 1949)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1999", "text": "<PERSON>, American singer-songwriter, guitarist, and producer (b. 1952)", "html": "1999 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter, guitarist, and producer (b. 1952)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter, guitarist, and producer (b. 1952)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1999", "text": "<PERSON><PERSON><PERSON><PERSON>-<PERSON><PERSON>, Russian mathematician (b. 1899)", "html": "1999 - <a href=\"https://wikipedia.org/wiki/Pelageya_Polubarinova-Kochina\" title=\"Pelageya Polubarinova-Kochina\"><PERSON><PERSON><PERSON><PERSON>-<PERSON></a>, Russian mathematician (b. 1899)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>ela<PERSON><PERSON>_<PERSON>ubarinova-Kochina\" title=\"<PERSON>ela<PERSON>ya Polubarinova-Kochina\"><PERSON><PERSON><PERSON><PERSON>-<PERSON></a>, Russian mathematician (b. 1899)", "links": [{"title": "Pelageya <PERSON>-Kochina", "link": "https://wikipedia.org/wiki/Pelageya_Polubarinova-Kochina"}]}, {"year": "2001", "text": "<PERSON><PERSON><PERSON><PERSON>, Canadian author and screenwriter (b. 1931)", "html": "2001 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"Morde<PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Canadian author and screenwriter (b. 1931)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"Morde<PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Canadian author and screenwriter (b. 1931)", "links": [{"title": "Mordeca<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>rde<PERSON><PERSON>_Richler"}]}, {"year": "2001", "text": "<PERSON>, American singer-songwriter and guitarist (b. 1940)", "html": "2001 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(singer)\" title=\"<PERSON> (singer)\"><PERSON></a>, American singer-songwriter and guitarist (b. 1940)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>(singer)\" title=\"<PERSON> (singer)\"><PERSON></a>, American singer-songwriter and guitarist (b. 1940)", "links": [{"title": "<PERSON> (singer)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>(singer)"}]}, {"year": "2004", "text": "<PERSON><PERSON><PERSON>, Russian general, pilot, and astronaut (b. 1929)", "html": "2004 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Russian general, pilot, and astronaut (b. 1929)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Russian general, pilot, and astronaut (b. 1929)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "2005", "text": "<PERSON>, Italian actor, director, and screenwriter (b. 1914)", "html": "2005 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian actor, director, and screenwriter (b. 1914)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian actor, director, and screenwriter (b. 1914)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2005", "text": "<PERSON><PERSON>, American lawyer and politician, 35th Governor of Wisconsin (b. 1916)", "html": "2005 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American lawyer and politician, 35th <a href=\"https://wikipedia.org/wiki/Governor_of_Wisconsin\" title=\"Governor of Wisconsin\">Governor of Wisconsin</a> (b. 1916)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American lawyer and politician, 35th <a href=\"https://wikipedia.org/wiki/Governor_of_Wisconsin\" title=\"Governor of Wisconsin\">Governor of Wisconsin</a> (b. 1916)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}, {"title": "Governor of Wisconsin", "link": "https://wikipedia.org/wiki/Governor_of_Wisconsin"}]}, {"year": "2006", "text": "<PERSON>, American computer scientist, developed the OBJ programming language (b. 1941)", "html": "2006 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American computer scientist, developed the <a href=\"https://wikipedia.org/wiki/OBJ_(programming_language)\" title=\"OBJ (programming language)\">OBJ programming language</a> (b. 1941)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American computer scientist, developed the <a href=\"https://wikipedia.org/wiki/OBJ_(programming_language)\" title=\"OBJ (programming language)\">OBJ programming language</a> (b. 1941)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}, {"title": "OBJ (programming language)", "link": "https://wikipedia.org/wiki/OBJ_(programming_language)"}]}, {"year": "2007", "text": "<PERSON>, American saxophonist (b. 1927)", "html": "2007 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American saxophonist (b. 1927)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American saxophonist (b. 1927)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_Randolph"}]}, {"year": "2008", "text": "<PERSON>, English actor and drummer (b. 1944)", "html": "2008 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actor and drummer (b. 1944)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actor and drummer (b. 1944)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2008", "text": "<PERSON>, Canadian fiddler, composer, and producer (b. 1956)", "html": "2008 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian fiddler, composer, and producer (b. 1956)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian fiddler, composer, and producer (b. 1956)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2009", "text": "<PERSON><PERSON><PERSON>, Bangladeshi author and poet (b. 1932)", "html": "2009 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Bangladeshi author and poet (b. 1932)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Bangladeshi author and poet (b. 1932)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>-<PERSON>"}]}, {"year": "2009", "text": "<PERSON>, American journalist and author (b. 1930)", "html": "2009 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American journalist and author (b. 1930)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American journalist and author (b. 1930)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2010", "text": "<PERSON>, Palestinian terrorist, planned the Munich massacre (b. 1937)", "html": "2010 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Palestinian terrorist, planned the <a href=\"https://wikipedia.org/wiki/Munich_massacre\" title=\"Munich massacre\">Munich massacre</a> (b. 1937)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Palestinian terrorist, planned the <a href=\"https://wikipedia.org/wiki/Munich_massacre\" title=\"Munich massacre\">Munich massacre</a> (b. 1937)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}, {"title": "Munich massacre", "link": "https://wikipedia.org/wiki/Munich_massacre"}]}, {"year": "2011", "text": "<PERSON>, Bahraini singer and guitarist (b. 1960)", "html": "2011 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Bahraini singer and guitarist (b. 1960)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Bahraini singer and guitarist (b. 1960)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2012", "text": "<PERSON><PERSON><PERSON><PERSON>, Vietnamese general and politician (b. 1925)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/Nguy%E1%BB%85n_H%E1%BB%AFu_C%C3%B3\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Vietnamese general and politician (b. 1925)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Nguy%E1%BB%85n_H%E1%BB%AFu_C%C3%B3\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Vietnamese general and politician (b. 1925)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Nguy%E1%BB%85n_H%E1%BB%AFu_C%C3%B3"}]}, {"year": "2012", "text": "<PERSON>, American actor, singer, and producer (b. 1926)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor, singer, and producer (b. 1926)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor, singer, and producer (b. 1926)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2012", "text": "<PERSON>, American educator and politician (b. 1934)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American educator and politician (b. 1934)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American educator and politician (b. 1934)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "2012", "text": "<PERSON>, Italian engineer and politician (b. 1926)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian engineer and politician (b. 1926)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian engineer and politician (b. 1926)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2012", "text": "<PERSON>, American lawyer and politician (b. 1935)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, American lawyer and politician (b. 1935)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, American lawyer and politician (b. 1935)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>ry"}]}, {"year": "2013", "text": "<PERSON>, Slovenian footballer and manager (b. 1964)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/Roman_Ben<PERSON>z\" title=\"Roman Ben<PERSON>z\"><PERSON></a>, Slovenian footballer and manager (b. 1964)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Roman_Ben<PERSON>z\" title=\"Roman Ben<PERSON>z\"><PERSON></a>, Slovenian footballer and manager (b. 1964)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Roman_Bengez"}]}, {"year": "2013", "text": "<PERSON>, American author (b. 1944)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author (b. 1944)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author (b. 1944)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2013", "text": "<PERSON><PERSON>, Canadian actress and screenwriter (b. 1951)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/P<PERSON>_<PERSON>i\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Canadian actress and screenwriter (b. 1951)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/PJ_<PERSON>i\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Canadian actress and screenwriter (b. 1951)", "links": [{"title": "PJ Torokvei", "link": "https://wikipedia.org/wiki/PJ_Torokvei"}]}, {"year": "2013", "text": "<PERSON><PERSON>, Romanian historian and politician, 57th Prime Minister of Romania (b. 1942)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Romanian historian and politician, 57th <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Romania\" title=\"Prime Minister of Romania\">Prime Minister of Romania</a> (b. 1942)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Romanian historian and politician, 57th <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Romania\" title=\"Prime Minister of Romania\">Prime Minister of Romania</a> (b. 1942)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>le"}, {"title": "Prime Minister of Romania", "link": "https://wikipedia.org/wiki/Prime_Minister_of_Romania"}]}, {"year": "2013", "text": "<PERSON>, French trumpet player and composer (b. 1934)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French trumpet player and composer (b. 1934)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French trumpet player and composer (b. 1934)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2013", "text": "<PERSON><PERSON><PERSON>, English playwright and screenwriter (b. 1948)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON> <PERSON>\"><PERSON><PERSON><PERSON></a>, English playwright and screenwriter (b. 1948)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON> <PERSON>\"><PERSON><PERSON><PERSON></a>, English playwright and screenwriter (b. 1948)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "2014", "text": "<PERSON><PERSON>, American photographer (b. 1917)", "html": "2014 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American photographer (b. 1917)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American photographer (b. 1917)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "2014", "text": "<PERSON>, Irish hurler and coach (b. 1927)", "html": "2014 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(hurler)\" title=\"<PERSON> (hurler)\"><PERSON></a>, Irish hurler and coach (b. 1927)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(hurler)\" title=\"<PERSON> (hurler)\"><PERSON></a>, Irish hurler and coach (b. 1927)", "links": [{"title": "<PERSON> (hurler)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(hurler)"}]}, {"year": "2014", "text": "<PERSON><PERSON><PERSON>, German footballer (b. 1948)", "html": "2014 - <a href=\"https://wikipedia.org/wiki/Volkmar_Gro%C3%9F\" title=\"Volkmar G<PERSON>ß\"><PERSON><PERSON><PERSON></a>, German footballer (b. 1948)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Volkmar_Gro%C3%9F\" title=\"Volkmar Groß\"><PERSON><PERSON><PERSON></a>, German footballer (b. 1948)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Volkmar_Gro%C3%9F"}]}, {"year": "2014", "text": "<PERSON>, American politician (b. 1943)", "html": "2014 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American politician (b. 1943)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American politician (b. 1943)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2014", "text": "<PERSON><PERSON><PERSON>-<PERSON><PERSON><PERSON>, Ukrainian-American rabbi and author (b. 1924)", "html": "2014 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>-<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Ukrainian-American rabbi and author (b. 1924)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>-<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Ukrainian-American rabbi and author (b. 1924)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>-<PERSON>"}]}, {"year": "2015", "text": "<PERSON>, British-American actress (b. 1923)", "html": "2015 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, British-American actress (b. 1923)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, British-American actress (b. 1923)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2015", "text": "<PERSON>, American religious leader and educator (b. 1924)", "html": "2015 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American religious leader and educator (b. 1924)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American religious leader and educator (b. 1924)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>er"}]}, {"year": "2015", "text": "<PERSON>, American farmer and politician (b. 1926)", "html": "2015 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American farmer and politician (b. 1926)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American farmer and politician (b. 1926)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2015", "text": "<PERSON>, Australian footballer and coach (b. 1960)", "html": "2015 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(Australian_footballer)\" title=\"<PERSON> (Australian footballer)\"><PERSON></a>, Australian footballer and coach (b. 1960)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(Australian_footballer)\" title=\"<PERSON> (Australian footballer)\"><PERSON></a>, Australian footballer and coach (b. 1960)", "links": [{"title": "<PERSON> (Australian footballer)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>(Australian_footballer)"}]}, {"year": "2020", "text": "<PERSON><PERSON><PERSON>, Indian dance choreographer (b. 1948)", "html": "2020 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Indian dance choreographer (b. 1948)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Indian dance choreographer (b. 1948)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}]}}