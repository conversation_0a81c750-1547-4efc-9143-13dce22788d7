{"date": "December 17", "url": "https://wikipedia.org/wiki/December_17", "data": {"Events": [{"year": "497 BC", "text": "The first Saturnalia festival was celebrated in ancient Rome.", "html": "497 BC - 497 BC - The first <a href=\"https://wikipedia.org/wiki/Saturnalia\" title=\"Saturnalia\">Saturnalia</a> festival was celebrated in <a href=\"https://wikipedia.org/wiki/Ancient_Rome\" title=\"Ancient Rome\">ancient Rome</a>.", "no_year_html": "497 BC - The first <a href=\"https://wikipedia.org/wiki/Saturnalia\" title=\"Saturnalia\">Saturnalia</a> festival was celebrated in <a href=\"https://wikipedia.org/wiki/Ancient_Rome\" title=\"Ancient Rome\">ancient Rome</a>.", "links": [{"title": "Saturnalia", "link": "https://wikipedia.org/wiki/Saturnalia"}, {"title": "Ancient Rome", "link": "https://wikipedia.org/wiki/Ancient_Rome"}]}, {"year": "546", "text": "Siege of Rome: The Ostrogoths under king <PERSON><PERSON><PERSON> plunder the city, by bribing the Eastern Roman garrison.", "html": "546 - <a href=\"https://wikipedia.org/wiki/Sack_of_Rome_(546)\" title=\"Sack of Rome (546)\">Siege of Rome</a>: The <a href=\"https://wikipedia.org/wiki/Ostrogoths\" title=\"Ostrogoths\">Ostrogoths</a> under king <a href=\"https://wikipedia.org/wiki/Totila\" title=\"Totila\">Totila</a> plunder the city, by bribing the <a href=\"https://wikipedia.org/wiki/Byzantine_Empire\" title=\"Byzantine Empire\">Eastern Roman</a> garrison.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Sack_of_Rome_(546)\" title=\"Sack of Rome (546)\">Siege of Rome</a>: The <a href=\"https://wikipedia.org/wiki/Ostrogoths\" title=\"Ostrogoths\">Ostrogoths</a> under king <a href=\"https://wikipedia.org/wiki/Totila\" title=\"Totila\">Totila</a> plunder the city, by bribing the <a href=\"https://wikipedia.org/wiki/Byzantine_Empire\" title=\"Byzantine Empire\">Eastern Roman</a> garrison.", "links": [{"title": "Sack of Rome (546)", "link": "https://wikipedia.org/wiki/Sack_of_Rome_(546)"}, {"title": "Ostrogoths", "link": "https://wikipedia.org/wiki/Ostrogoths"}, {"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>a"}, {"title": "Byzantine Empire", "link": "https://wikipedia.org/wiki/Byzantine_Empire"}]}, {"year": "920", "text": "<PERSON><PERSON> is crowned co-emperor of the underage <PERSON>.", "html": "920 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a> is crowned co-emperor of the underage <a href=\"https://wikipedia.org/wiki/Constantine_VII\" title=\"Constantine VII\"><PERSON> VII</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a> is crowned co-emperor of the underage <a href=\"https://wikipedia.org/wiki/Constantine_VII\" title=\"Constantine VII\"><PERSON> VII</a>.", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>"}, {"title": "Constantine VII", "link": "https://wikipedia.org/wiki/Constantine_VII"}]}, {"year": "942", "text": "Assassination of <PERSON> of Normandy.", "html": "942 - Assassination of <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Normandy\" class=\"mw-redirect\" title=\"<PERSON> I of Normandy\"><PERSON> of Normandy</a>.", "no_year_html": "Assassination of <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Normandy\" class=\"mw-redirect\" title=\"<PERSON> of Normandy\"><PERSON> of Normandy</a>.", "links": [{"title": "<PERSON> of Normandy", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Normandy"}]}, {"year": "1297", "text": "King <PERSON><PERSON><PERSON><PERSON> of Pagan is overthrown by the three <PERSON><PERSON><PERSON><PERSON> brothers, marking the de facto end of the Pagan Kingdom.", "html": "1297 - King <a href=\"https://wikipedia.org/wiki/K<PERSON><PERSON>wa_of_Pagan\" title=\"<PERSON><PERSON><PERSON><PERSON> of Pagan\"><PERSON><PERSON><PERSON><PERSON> of Pagan</a> is overthrown by the <a href=\"https://wikipedia.org/wiki/Myinsaing_Kingdom\" title=\"Myinsaing Kingdom\">three Myinsaing brothers</a>, marking the de facto end of the <a href=\"https://wikipedia.org/wiki/Pagan_Kingdom\" class=\"mw-redirect\" title=\"Pagan Kingdom\">Pagan Kingdom</a>.", "no_year_html": "King <a href=\"https://wikipedia.org/wiki/K<PERSON><PERSON>wa_of_Pagan\" title=\"<PERSON>ya<PERSON><PERSON> of Pagan\"><PERSON><PERSON><PERSON><PERSON> of Pagan</a> is overthrown by the <a href=\"https://wikipedia.org/wiki/Myinsaing_Kingdom\" title=\"Myinsaing Kingdom\">three Myinsaing brothers</a>, marking the de facto end of the <a href=\"https://wikipedia.org/wiki/Pagan_Kingdom\" class=\"mw-redirect\" title=\"Pagan Kingdom\">Pagan Kingdom</a>.", "links": [{"title": "Kyawswa of Pagan", "link": "https://wikipedia.org/wiki/Kyawswa_of_Pagan"}, {"title": "Myinsaing Kingdom", "link": "https://wikipedia.org/wiki/Myinsaing_Kingdom"}, {"title": "Pagan Kingdom", "link": "https://wikipedia.org/wiki/Pagan_Kingdom"}]}, {"year": "1354", "text": "<PERSON>, Countess of Hainaut and Holy Roman Empress and her son <PERSON>, Duke of Bavaria, sign a peace treaty ending the Hook and Cod wars.", "html": "1354 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Countess_of_Hainaut\" title=\"<PERSON>, Countess of Hainaut\"><PERSON>, Countess of Hainaut and Holy Roman Empress</a> and her son <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Duke_of_Bavaria\" title=\"<PERSON>, Duke of Bavaria\"><PERSON>, Duke of Bavaria</a>, sign a peace treaty ending the <a href=\"https://wikipedia.org/wiki/Hook_and_Cod_wars\" title=\"Hook and Cod wars\">Hook and Cod wars</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>,_Countess_of_Hainaut\" title=\"<PERSON>, Countess of Hainaut\"><PERSON>, Countess of Hainaut and Holy Roman Empress</a> and her son <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Duke_of_Bavaria\" title=\"<PERSON>, Duke of Bavaria\"><PERSON>, Duke of Bavaria</a>, sign a peace treaty ending the <a href=\"https://wikipedia.org/wiki/Hook_and_Cod_wars\" title=\"Hook and Cod wars\">Hook and Cod wars</a>.", "links": [{"title": "<PERSON>, Countess of Hainaut", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Countess_of_Hai<PERSON>ut"}, {"title": "<PERSON>, Duke of Bavaria", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Duke_of_Bavaria"}, {"title": "Hook and Cod wars", "link": "https://wikipedia.org/wiki/Hook_and_Cod_wars"}]}, {"year": "1398", "text": "Sultan <PERSON><PERSON><PERSON><PERSON>'s armies in Delhi are defeated by <PERSON><PERSON>.", "html": "1398 - <a href=\"https://wikipedia.org/wiki/Tughlaq_dynasty\" title=\"Tughlaq dynasty\">Sultan <PERSON><PERSON><PERSON></a>'s armies in Delhi are defeated by <a href=\"https://wikipedia.org/wiki/Timur\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Tughlaq_dynasty\" title=\"Tughlaq dynasty\">Sultan <PERSON><PERSON><PERSON><PERSON></a>'s armies in Delhi are defeated by <a href=\"https://wikipedia.org/wiki/Timur\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>.", "links": [{"title": "Tughlaq dynasty", "link": "https://wikipedia.org/wiki/Tughlaq_dynasty"}, {"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>ur"}]}, {"year": "1538", "text": "<PERSON> excommunicates <PERSON> of England.", "html": "1538 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON> Paul <PERSON>\">Pope <PERSON></a> excommunicates <a href=\"https://wikipedia.org/wiki/<PERSON>_VIII_of_England\" class=\"mw-redirect\" title=\"Henry VIII of England\"><PERSON> VIII of England</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON> Paul III\"><PERSON> III</a> excommunicates <a href=\"https://wikipedia.org/wiki/<PERSON>_VIII_of_England\" class=\"mw-redirect\" title=\"Henry VIII of England\"><PERSON> VIII of England</a>.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "<PERSON> of England", "link": "https://wikipedia.org/wiki/Henry_VIII_of_England"}]}, {"year": "1583", "text": "Cologne War: Forces under <PERSON> defeat troops under <PERSON><PERSON><PERSON> W<PERSON> at the Siege of Godesberg.", "html": "1583 - <a href=\"https://wikipedia.org/wiki/Cologne_War\" title=\"Cologne War\">Cologne War</a>: Forces under <a href=\"https://wikipedia.org/wiki/Ernest_<PERSON>_Bavaria\" title=\"<PERSON> of Bavaria\"><PERSON> of Bavaria</a> defeat troops under <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON><PERSON><PERSON>_von_Waldburg\" title=\"<PERSON><PERSON><PERSON> Waldburg\"><PERSON><PERSON><PERSON> von Waldburg</a> at the <a href=\"https://wikipedia.org/wiki/Siege_of_Godesberg\" title=\"Siege of Godesberg\">Siege of Godesberg</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Cologne_War\" title=\"Cologne War\">Cologne War</a>: Forces under <a href=\"https://wikipedia.org/wiki/Ernest_<PERSON>_Bavaria\" title=\"<PERSON> of Bavaria\"><PERSON> of Bavaria</a> defeat troops under <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON><PERSON><PERSON>_von_Waldburg\" title=\"<PERSON><PERSON><PERSON> von Waldburg\"><PERSON><PERSON><PERSON> von Waldburg</a> at the <a href=\"https://wikipedia.org/wiki/Siege_of_Godesberg\" title=\"Siege of Godesberg\">Siege of Godesberg</a>.", "links": [{"title": "Cologne War", "link": "https://wikipedia.org/wiki/Cologne_War"}, {"title": "<PERSON> of Bavaria", "link": "https://wikipedia.org/wiki/Ernest_<PERSON>_Bavaria"}, {"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON><PERSON><PERSON><PERSON>_<PERSON>_<PERSON>"}, {"title": "Siege of Godesberg", "link": "https://wikipedia.org/wiki/Siege_of_Godesberg"}]}, {"year": "1586", "text": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> becomes Emperor of Japan.", "html": "1586 - <a href=\"https://wikipedia.org/wiki/Emperor_Go-Y%C5%8Dzei\" title=\"Emperor Go-Yōzei\">Go-Yōzei</a> becomes <a href=\"https://wikipedia.org/wiki/Emperor_of_Japan\" title=\"Emperor of Japan\">Emperor of Japan</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Emperor_Go-Y%C5%8Dzei\" title=\"Emperor Go-Yōzei\">Go-Yōzei</a> becomes <a href=\"https://wikipedia.org/wiki/Emperor_of_Japan\" title=\"Emperor of Japan\">Emperor of Japan</a>.", "links": [{"title": "Emperor <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Emperor_Go-Y%C5%8Dzei"}, {"title": "Emperor of Japan", "link": "https://wikipedia.org/wiki/Emperor_of_Japan"}]}, {"year": "1665", "text": "The first account of a blood transfusion is published, in the form of a letter from physician <PERSON> to chemist <PERSON>, in Philosophical Transactions of the Royal Society", "html": "1665 - The first account of a <a href=\"https://wikipedia.org/wiki/Blood_transfusion\" title=\"Blood transfusion\">blood transfusion</a> is published, in the form of a letter from physician <PERSON> to chemist <PERSON>, in <i><a href=\"https://wikipedia.org/wiki/Philosophical_Transactions_of_the_Royal_Society\" title=\"Philosophical Transactions of the Royal Society\">Philosophical Transactions of the Royal Society</a></i>", "no_year_html": "The first account of a <a href=\"https://wikipedia.org/wiki/Blood_transfusion\" title=\"Blood transfusion\">blood transfusion</a> is published, in the form of a letter from physician <PERSON> to chemist <PERSON>, in <i><a href=\"https://wikipedia.org/wiki/Philosophical_Transactions_of_the_Royal_Society\" title=\"Philosophical Transactions of the Royal Society\">Philosophical Transactions of the Royal Society</a></i>", "links": [{"title": "Blood transfusion", "link": "https://wikipedia.org/wiki/Blood_transfusion"}, {"title": "Philosophical Transactions of the Royal Society", "link": "https://wikipedia.org/wiki/Philosophical_Transactions_of_the_Royal_Society"}]}, {"year": "1718", "text": "War of the Quadruple Alliance: Great Britain declares war on Spain.", "html": "1718 - <a href=\"https://wikipedia.org/wiki/War_of_the_Quadruple_Alliance\" title=\"War of the Quadruple Alliance\">War of the Quadruple Alliance</a>: Great Britain declares war on Spain.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/War_of_the_Quadruple_Alliance\" title=\"War of the Quadruple Alliance\">War of the Quadruple Alliance</a>: Great Britain declares war on Spain.", "links": [{"title": "War of the Quadruple Alliance", "link": "https://wikipedia.org/wiki/War_of_the_Quadruple_Alliance"}]}, {"year": "1777", "text": "American Revolution: France formally recognizes the United States.", "html": "1777 - <a href=\"https://wikipedia.org/wiki/American_Revolution\" title=\"American Revolution\">American Revolution</a>: <a href=\"https://wikipedia.org/wiki/France_in_the_American_Revolutionary_War\" title=\"France in the American Revolutionary War\">France</a> formally <a href=\"https://wikipedia.org/wiki/France%E2%80%93United_States_relations\" title=\"France-United States relations\">recognizes</a> the United States.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/American_Revolution\" title=\"American Revolution\">American Revolution</a>: <a href=\"https://wikipedia.org/wiki/France_in_the_American_Revolutionary_War\" title=\"France in the American Revolutionary War\">France</a> formally <a href=\"https://wikipedia.org/wiki/France%E2%80%93United_States_relations\" title=\"France-United States relations\">recognizes</a> the United States.", "links": [{"title": "American Revolution", "link": "https://wikipedia.org/wiki/American_Revolution"}, {"title": "France in the American Revolutionary War", "link": "https://wikipedia.org/wiki/France_in_the_American_Revolutionary_War"}, {"title": "France-United States relations", "link": "https://wikipedia.org/wiki/France%E2%80%93United_States_relations"}]}, {"year": "1790", "text": "The Aztec calendar stone is discovered at  El Zócalo, Mexico City.", "html": "1790 - The <a href=\"https://wikipedia.org/wiki/Aztec_calendar_stone\" class=\"mw-redirect\" title=\"Aztec calendar stone\">Aztec calendar stone</a> is discovered at El Zócalo, Mexico City.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Aztec_calendar_stone\" class=\"mw-redirect\" title=\"Aztec calendar stone\">Aztec calendar stone</a> is discovered at El Zócalo, Mexico City.", "links": [{"title": "Aztec calendar stone", "link": "https://wikipedia.org/wiki/Aztec_calendar_stone"}]}, {"year": "1807", "text": "Napoleonic Wars: France issues the Milan Decree, which confirms the Continental System.", "html": "1807 - <a href=\"https://wikipedia.org/wiki/Napoleonic_Wars\" title=\"Napoleonic Wars\">Napoleonic Wars</a>: France issues the <a href=\"https://wikipedia.org/wiki/Milan_Decree\" title=\"Milan Decree\">Milan Decree</a>, which confirms the <a href=\"https://wikipedia.org/wiki/Continental_System\" title=\"Continental System\">Continental System</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Napoleonic_Wars\" title=\"Napoleonic Wars\">Napoleonic Wars</a>: France issues the <a href=\"https://wikipedia.org/wiki/Milan_Decree\" title=\"Milan Decree\">Milan Decree</a>, which confirms the <a href=\"https://wikipedia.org/wiki/Continental_System\" title=\"Continental System\">Continental System</a>.", "links": [{"title": "Napoleonic Wars", "link": "https://wikipedia.org/wiki/Napoleonic_Wars"}, {"title": "Milan Decree", "link": "https://wikipedia.org/wiki/Milan_Decree"}, {"title": "Continental System", "link": "https://wikipedia.org/wiki/Continental_System"}]}, {"year": "1812", "text": "War of 1812: U.S. forces attack a Lenape village in the Battle of the Mississinewa.", "html": "1812 - <a href=\"https://wikipedia.org/wiki/War_of_1812\" title=\"War of 1812\">War of 1812</a>: U.S. forces attack a <a href=\"https://wikipedia.org/wiki/Lenape\" title=\"Lenape\">Lenape</a> village in the <a href=\"https://wikipedia.org/wiki/Battle_of_the_Mississinewa\" title=\"Battle of the Mississinewa\">Battle of the Mississinewa</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/War_of_1812\" title=\"War of 1812\">War of 1812</a>: U.S. forces attack a <a href=\"https://wikipedia.org/wiki/Lenape\" title=\"Lenape\">Lenape</a> village in the <a href=\"https://wikipedia.org/wiki/Battle_of_the_Mississinewa\" title=\"Battle of the Mississinewa\">Battle of the Mississinewa</a>.", "links": [{"title": "War of 1812", "link": "https://wikipedia.org/wiki/War_of_1812"}, {"title": "Lenape", "link": "https://wikipedia.org/wiki/Lenape"}, {"title": "Battle of the Mississinewa", "link": "https://wikipedia.org/wiki/Battle_of_the_Mississinewa"}]}, {"year": "1819", "text": "Simón Bolívar declares the independence of Gran Colombia in Angostura (now Ciudad Bolívar in Venezuela).", "html": "1819 - <a href=\"https://wikipedia.org/wiki/Sim%C3%B3n_Bol%C3%ADvar\" title=\"Simón Bolívar\"><PERSON><PERSON><PERSON> Bolí<PERSON></a> declares the independence of <a href=\"https://wikipedia.org/wiki/Gran_Colombia\" title=\"Gran Colombia\">Gran Colombia</a> in Angostura (now <a href=\"https://wikipedia.org/wiki/Ciudad_Bol%C3%ADvar\" title=\"Ciudad Bolívar\">Ciudad Bolívar</a> in <a href=\"https://wikipedia.org/wiki/Venezuela\" title=\"Venezuela\">Venezuela</a>).", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Sim%C3%B3n_Bol%C3%ADvar\" title=\"Simón Bolívar\"><PERSON><PERSON><PERSON></a> declares the independence of <a href=\"https://wikipedia.org/wiki/Gran_Colombia\" title=\"Gran Colombia\">Gran Colombia</a> in Angostura (now <a href=\"https://wikipedia.org/wiki/Ciudad_Bol%C3%ADvar\" title=\"Ciudad Bolívar\">Ciudad Bolívar</a> in <a href=\"https://wikipedia.org/wiki/Venezuela\" title=\"Venezuela\">Venezuela</a>).", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Sim%C3%B3n_Bol%C3%ADvar"}, {"title": "Gran Colombia", "link": "https://wikipedia.org/wiki/Gran_Colombia"}, {"title": "Ciudad Bolívar", "link": "https://wikipedia.org/wiki/Ciudad_Bol%C3%ADvar"}, {"title": "Venezuela", "link": "https://wikipedia.org/wiki/Venezuela"}]}, {"year": "1835", "text": "The second Great Fire of New York destroys 53,000 square metres (13 acres) of New York City's Financial District.", "html": "1835 - The second <a href=\"https://wikipedia.org/wiki/Great_Fire_of_New_York\" title=\"Great Fire of New York\">Great Fire of New York</a> destroys 53,000 square metres (13 acres) of New York City's <a href=\"https://wikipedia.org/wiki/Financial_District,_Manhattan\" title=\"Financial District, Manhattan\">Financial District</a>.", "no_year_html": "The second <a href=\"https://wikipedia.org/wiki/Great_Fire_of_New_York\" title=\"Great Fire of New York\">Great Fire of New York</a> destroys 53,000 square metres (13 acres) of New York City's <a href=\"https://wikipedia.org/wiki/Financial_District,_Manhattan\" title=\"Financial District, Manhattan\">Financial District</a>.", "links": [{"title": "Great Fire of New York", "link": "https://wikipedia.org/wiki/Great_Fire_of_New_York"}, {"title": "Financial District, Manhattan", "link": "https://wikipedia.org/wiki/Financial_District,_Manhattan"}]}, {"year": "1837", "text": "A fire in the Winter Palace of Saint Petersburg kills 30 guards.", "html": "1837 - A <a href=\"https://wikipedia.org/wiki/Fire_in_the_Winter_Palace\" title=\"Fire in the Winter Palace\">fire in the Winter Palace</a> of <a href=\"https://wikipedia.org/wiki/Saint_Petersburg\" title=\"Saint Petersburg\">Saint Petersburg</a> kills 30 guards.", "no_year_html": "A <a href=\"https://wikipedia.org/wiki/Fire_in_the_Winter_Palace\" title=\"Fire in the Winter Palace\">fire in the Winter Palace</a> of <a href=\"https://wikipedia.org/wiki/Saint_Petersburg\" title=\"Saint Petersburg\">Saint Petersburg</a> kills 30 guards.", "links": [{"title": "Fire in the Winter Palace", "link": "https://wikipedia.org/wiki/Fire_in_the_Winter_Palace"}, {"title": "Saint Petersburg", "link": "https://wikipedia.org/wiki/Saint_Petersburg"}]}, {"year": "1862", "text": "American Civil War: General <PERSON> issues General Order No. 11, expelling Jews from parts of Tennessee, Mississippi, and Kentucky.", "html": "1862 - <a href=\"https://wikipedia.org/wiki/American_Civil_War\" title=\"American Civil War\">American Civil War</a>: General <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> issues <i><a href=\"https://wikipedia.org/wiki/General_Order_No._11_(1862)\" title=\"General Order No. 11 (1862)\">General Order No. 11</a></i>, expelling <a href=\"https://wikipedia.org/wiki/Jews\" title=\"Jews\">Jews</a> from parts of <a href=\"https://wikipedia.org/wiki/Tennessee\" title=\"Tennessee\">Tennessee</a>, <a href=\"https://wikipedia.org/wiki/Mississippi\" title=\"Mississippi\">Mississippi</a>, and <a href=\"https://wikipedia.org/wiki/Kentucky\" title=\"Kentucky\">Kentucky</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/American_Civil_War\" title=\"American Civil War\">American Civil War</a>: General <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_Grant\" title=\"Ulysses <PERSON> Grant\"><PERSON></a> issues <i><a href=\"https://wikipedia.org/wiki/General_Order_No._11_(1862)\" title=\"General Order No. 11 (1862)\">General Order No. 11</a></i>, expelling <a href=\"https://wikipedia.org/wiki/Jews\" title=\"Jews\">Jews</a> from parts of <a href=\"https://wikipedia.org/wiki/Tennessee\" title=\"Tennessee\">Tennessee</a>, <a href=\"https://wikipedia.org/wiki/Mississippi\" title=\"Mississippi\">Mississippi</a>, and <a href=\"https://wikipedia.org/wiki/Kentucky\" title=\"Kentucky\">Kentucky</a>.", "links": [{"title": "American Civil War", "link": "https://wikipedia.org/wiki/American_Civil_War"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>"}, {"title": "General Order No. 11 (1862)", "link": "https://wikipedia.org/wiki/General_Order_No._11_(1862)"}, {"title": "Jews", "link": "https://wikipedia.org/wiki/Jews"}, {"title": "Tennessee", "link": "https://wikipedia.org/wiki/Tennessee"}, {"title": "Mississippi", "link": "https://wikipedia.org/wiki/Mississippi"}, {"title": "Kentucky", "link": "https://wikipedia.org/wiki/Kentucky"}]}, {"year": "1865", "text": "First performance of the Unfinished Symphony by <PERSON>.", "html": "1865 - First performance of the <a href=\"https://wikipedia.org/wiki/Symphony_No._8_(<PERSON>)\" title=\"Symphony No. 8 (<PERSON>)\">Unfinished Symphony</a> by <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>.", "no_year_html": "First performance of the <a href=\"https://wikipedia.org/wiki/Symphony_No._8_(<PERSON>)\" title=\"Symphony No. 8 (<PERSON>)\">Unfinished Symphony</a> by <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>.", "links": [{"title": "Symphony No. 8 (<PERSON>)", "link": "https://wikipedia.org/wiki/Symphony_No._8_(<PERSON>)"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1892", "text": "First issue of Vogue is published.", "html": "1892 - First issue of <i><a href=\"https://wikipedia.org/wiki/Vogue_(magazine)\" title=\"Vogue (magazine)\">Vogue</a></i> is published.", "no_year_html": "First issue of <i><a href=\"https://wikipedia.org/wiki/Vogue_(magazine)\" title=\"Vogue (magazine)\">Vogue</a></i> is published.", "links": [{"title": "Vogue (magazine)", "link": "https://wikipedia.org/wiki/Vogue_(magazine)"}]}, {"year": "1896", "text": "Pittsburgh, Pennsylvania's Schenley Park Casino, which was the first multi-purpose arena with the technology to create an artificial ice surface in North America, is destroyed in a fire.", "html": "1896 - <a href=\"https://wikipedia.org/wiki/Pittsburgh\" title=\"Pittsburgh\">Pittsburgh, Pennsylvania's</a> <a href=\"https://wikipedia.org/wiki/Schenley_Park_Casino\" title=\"Schenley Park Casino\">Schenley Park Casino</a>, which was the first multi-purpose <a href=\"https://wikipedia.org/wiki/Arena\" title=\"Arena\">arena</a> with the technology to create an artificial ice surface in North America, is destroyed in a fire.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Pittsburgh\" title=\"Pittsburgh\">Pittsburgh, Pennsylvania's</a> <a href=\"https://wikipedia.org/wiki/Schenley_Park_Casino\" title=\"Schenley Park Casino\">Schenley Park Casino</a>, which was the first multi-purpose <a href=\"https://wikipedia.org/wiki/Arena\" title=\"Arena\">arena</a> with the technology to create an artificial ice surface in North America, is destroyed in a fire.", "links": [{"title": "Pittsburgh", "link": "https://wikipedia.org/wiki/Pittsburgh"}, {"title": "Schenley Park Casino", "link": "https://wikipedia.org/wiki/Schenley_Park_Casino"}, {"title": "Arena", "link": "https://wikipedia.org/wiki/Arena"}]}, {"year": "1903", "text": "The <PERSON> brothers make the first controlled powered, heavier-than-air flight in the Wright Flyer at Kitty Hawk, North Carolina.", "html": "1903 - The <a href=\"https://wikipedia.org/wiki/<PERSON>_brothers\" title=\"<PERSON> brothers\"><PERSON> brothers</a> make the first controlled powered, heavier-than-air flight in the <i><a href=\"https://wikipedia.org/wiki/Wright_Flyer\" title=\"Wright Flyer\"><PERSON> Flyer</a></i> at <a href=\"https://wikipedia.org/wiki/<PERSON>_Hawk,_North_Carolina\" title=\"Kitty Hawk, North Carolina\">Kitty Hawk, North Carolina</a>.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/<PERSON>_brothers\" title=\"<PERSON> brothers\"><PERSON> brothers</a> make the first controlled powered, heavier-than-air flight in the <i><a href=\"https://wikipedia.org/wiki/Wright_Flyer\" title=\"Wright Flyer\"><PERSON> Flyer</a></i> at <a href=\"https://wikipedia.org/wiki/<PERSON>_Hawk,_North_Carolina\" title=\"Kitty Hawk, North Carolina\">Kitty Hawk, North Carolina</a>.", "links": [{"title": "<PERSON> brothers", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "<PERSON> Flyer", "link": "https://wikipedia.org/wiki/<PERSON>_Flyer"}, {"title": "Kitty Hawk, North Carolina", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_North_Carolina"}]}, {"year": "1907", "text": "<PERSON><PERSON><PERSON> is crowned first King of Bhutan.", "html": "1907 - <a href=\"https://wikipedia.org/wiki/Ugyen_Wang<PERSON>ck\" title=\"Ugyen <PERSON>\"><PERSON><PERSON><PERSON></a> is crowned first King of <a href=\"https://wikipedia.org/wiki/Bhutan\" title=\"Bhutan\">Bhutan</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>gy<PERSON>_<PERSON>\" title=\"Ugyen <PERSON>\"><PERSON><PERSON><PERSON></a> is crowned first King of <a href=\"https://wikipedia.org/wiki/Bhutan\" title=\"Bhutan\">Bhutan</a>.", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Ugy<PERSON>_<PERSON>"}, {"title": "Bhutan", "link": "https://wikipedia.org/wiki/Bhutan"}]}, {"year": "1918", "text": "Darwin Rebellion: Up to 1,000 demonstrators march on Government House in Darwin, Northern Territory, Australia.", "html": "1918 - <a href=\"https://wikipedia.org/wiki/Darwin_Rebellion\" class=\"mw-redirect\" title=\"Darwin Rebellion\">Darwin Rebellion</a>: Up to 1,000 demonstrators march on <a href=\"https://wikipedia.org/wiki/Government_House,_Darwin\" title=\"Government House, Darwin\">Government House</a> in Darwin, <a href=\"https://wikipedia.org/wiki/Northern_Territory\" title=\"Northern Territory\">Northern Territory</a>, Australia.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Darwin_Rebellion\" class=\"mw-redirect\" title=\"Darwin Rebellion\">Darwin Rebellion</a>: Up to 1,000 demonstrators march on <a href=\"https://wikipedia.org/wiki/Government_House,_Darwin\" title=\"Government House, Darwin\">Government House</a> in Darwin, <a href=\"https://wikipedia.org/wiki/Northern_Territory\" title=\"Northern Territory\">Northern Territory</a>, Australia.", "links": [{"title": "Darwin Rebellion", "link": "https://wikipedia.org/wiki/Darwin_Rebellion"}, {"title": "Government House, Darwin", "link": "https://wikipedia.org/wiki/Government_House,_Darwin"}, {"title": "Northern Territory", "link": "https://wikipedia.org/wiki/Northern_Territory"}]}, {"year": "1926", "text": "<PERSON><PERSON><PERSON> assumes power in Lithuania as the 1926 coup d'état is successful.", "html": "1926 - <a href=\"https://wikipedia.org/wiki/Antanas_Smetona\" title=\"Antanas Smetona\">Antanas Smetona</a> assumes power in <a href=\"https://wikipedia.org/wiki/Lithuania\" title=\"Lithuania\">Lithuania</a> as the <a href=\"https://wikipedia.org/wiki/1926_Lithuanian_coup_d%27%C3%A9tat\" title=\"1926 Lithuanian coup d'état\">1926 coup d'état</a> is successful.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Antanas_Smetona\" title=\"Antanas Smetona\"><PERSON><PERSON>s Smet<PERSON></a> assumes power in <a href=\"https://wikipedia.org/wiki/Lithuania\" title=\"Lithuania\">Lithuania</a> as the <a href=\"https://wikipedia.org/wiki/1926_Lithuanian_coup_d%27%C3%A9tat\" title=\"1926 Lithuanian coup d'état\">1926 coup d'état</a> is successful.", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Antanas_Smetona"}, {"title": "Lithuania", "link": "https://wikipedia.org/wiki/Lithuania"}, {"title": "1926 Lithuanian coup d'état", "link": "https://wikipedia.org/wiki/1926_Lithuanian_coup_d%27%C3%A9tat"}]}, {"year": "1927", "text": "Indian revolutionary <PERSON><PERSON> is hanged in Gonda jail, Uttar Pradesh, India, two days before the scheduled date.", "html": "1927 - Indian revolutionary <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a> is hanged in Gonda jail, Uttar Pradesh, India, two days before the scheduled date.", "no_year_html": "Indian revolutionary <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a> is hanged in Gonda jail, Uttar Pradesh, India, two days before the scheduled date.", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1928", "text": "Indian revolutionaries <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> and <PERSON><PERSON> assassinate British police officer <PERSON> in Lahore, Punjab, to avenge the death of <PERSON><PERSON> at the hands of the police. The three were executed in 1931.", "html": "1928 - Indian revolutionaries <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, <a href=\"https://wikipedia.org/wiki/Sukhdev_Thapar\" title=\"Sukhdev Thapar\"><PERSON>khdev Thapar</a> and <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_Rajguru\" title=\"<PERSON><PERSON> Rajguru\"><PERSON><PERSON></a> assassinate British police officer <PERSON> in <a href=\"https://wikipedia.org/wiki/Lahore\" title=\"Lahore\">Lahore</a>, <a href=\"https://wikipedia.org/wiki/Punjab_region\" class=\"mw-redirect\" title=\"Punjab region\">Punjab</a>, to avenge the death of <a href=\"https://wikipedia.org/wiki/<PERSON>a_Lajpat_Rai\" title=\"Lala Lajpat Rai\"><PERSON><PERSON></a> at the hands of the police. The three were executed in 1931.", "no_year_html": "Indian revolutionaries <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, <a href=\"https://wikipedia.org/wiki/Sukhdev_Thapar\" title=\"Sukhdev Thapar\"><PERSON>khdev Thapar</a> and <a href=\"https://wikipedia.org/wiki/<PERSON>_Rajguru\" title=\"<PERSON><PERSON> Rajguru\"><PERSON><PERSON></a> assassinate British police officer <PERSON> in <a href=\"https://wikipedia.org/wiki/Lahore\" title=\"Lahore\">Lahore</a>, <a href=\"https://wikipedia.org/wiki/Punjab_region\" class=\"mw-redirect\" title=\"Punjab region\">Punjab</a>, to avenge the death of <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_Lajpat_Rai\" title=\"Lala Lajpat Rai\"><PERSON><PERSON></a> at the hands of the police. The three were executed in 1931.", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}, {"title": "<PERSON><PERSON><PERSON><PERSON> T<PERSON>par", "link": "https://wikipedia.org/wiki/Sukhdev_Thapar"}, {"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}, {"title": "Lahore", "link": "https://wikipedia.org/wiki/Lahore"}, {"title": "Punjab region", "link": "https://wikipedia.org/wiki/Punjab_region"}, {"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON><PERSON><PERSON>_Rai"}]}, {"year": "1933", "text": "The first NFL Championship Game is played at Wrigley Field in Chicago between the New York Giants and Chicago Bears. The Bears won 23-21.", "html": "1933 - The <a href=\"https://wikipedia.org/wiki/1933_NFL_Championship_Game\" title=\"1933 NFL Championship Game\">first NFL Championship Game</a> is played at <a href=\"https://wikipedia.org/wiki/Wrigley_Field\" title=\"Wrigley Field\">Wrigley Field</a> in <a href=\"https://wikipedia.org/wiki/Chicago\" title=\"Chicago\">Chicago</a> between the <a href=\"https://wikipedia.org/wiki/New_York_Giants\" title=\"New York Giants\">New York Giants</a> and <a href=\"https://wikipedia.org/wiki/Chicago_Bears\" title=\"Chicago Bears\">Chicago Bears</a>. The Bears won 23-21.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/1933_NFL_Championship_Game\" title=\"1933 NFL Championship Game\">first NFL Championship Game</a> is played at <a href=\"https://wikipedia.org/wiki/Wrigley_Field\" title=\"Wrigley Field\">Wrigley Field</a> in <a href=\"https://wikipedia.org/wiki/Chicago\" title=\"Chicago\">Chicago</a> between the <a href=\"https://wikipedia.org/wiki/New_York_Giants\" title=\"New York Giants\">New York Giants</a> and <a href=\"https://wikipedia.org/wiki/Chicago_Bears\" title=\"Chicago Bears\">Chicago Bears</a>. The Bears won 23-21.", "links": [{"title": "1933 NFL Championship Game", "link": "https://wikipedia.org/wiki/1933_NFL_Championship_Game"}, {"title": "Wrigley Field", "link": "https://wikipedia.org/wiki/Wrigley_Field"}, {"title": "Chicago", "link": "https://wikipedia.org/wiki/Chicago"}, {"title": "New York Giants", "link": "https://wikipedia.org/wiki/New_York_Giants"}, {"title": "Chicago Bears", "link": "https://wikipedia.org/wiki/Chicago_Bears"}]}, {"year": "1935", "text": "First flight of the Douglas DC-3.", "html": "1935 - First flight of the <a href=\"https://wikipedia.org/wiki/Douglas_DC-3\" title=\"Douglas DC-3\">Douglas DC-3</a>.", "no_year_html": "First flight of the <a href=\"https://wikipedia.org/wiki/Douglas_DC-3\" title=\"Douglas DC-3\">Douglas DC-3</a>.", "links": [{"title": "Douglas DC-3", "link": "https://wikipedia.org/wiki/Douglas_DC-3"}]}, {"year": "1938", "text": "<PERSON> discovers the nuclear fission of the heavy element uranium, the scientific and technological basis of nuclear energy.", "html": "1938 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> discovers the <a href=\"https://wikipedia.org/wiki/Nuclear_fission\" title=\"Nuclear fission\">nuclear fission</a> of the heavy element uranium, the scientific and technological basis of <a href=\"https://wikipedia.org/wiki/Nuclear_power\" title=\"Nuclear power\">nuclear energy</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> discovers the <a href=\"https://wikipedia.org/wiki/Nuclear_fission\" title=\"Nuclear fission\">nuclear fission</a> of the heavy element uranium, the scientific and technological basis of <a href=\"https://wikipedia.org/wiki/Nuclear_power\" title=\"Nuclear power\">nuclear energy</a>.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Nuclear fission", "link": "https://wikipedia.org/wiki/Nuclear_fission"}, {"title": "Nuclear power", "link": "https://wikipedia.org/wiki/Nuclear_power"}]}, {"year": "1939", "text": "World War II: Battle of the River Plate: The Admiral <PERSON> is scuttled by Captain <PERSON> outside Montevideo.", "html": "1939 - <a href=\"https://wikipedia.org/wiki/World_War_II\" title=\"World War II\">World War II</a>: <a href=\"https://wikipedia.org/wiki/Battle_of_the_River_Plate\" title=\"Battle of the River Plate\">Battle of the River Plate</a>: The <i><a href=\"https://wikipedia.org/wiki/German_cruiser_Admiral_<PERSON>_<PERSON>\" title=\"German cruiser Admiral <PERSON>\">Admiral <PERSON></a></i> is scuttled by Captain <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> outside <a href=\"https://wikipedia.org/wiki/Montevideo\" title=\"Montevideo\">Montevideo</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/World_War_II\" title=\"World War II\">World War II</a>: <a href=\"https://wikipedia.org/wiki/Battle_of_the_River_Plate\" title=\"Battle of the River Plate\">Battle of the River Plate</a>: The <i><a href=\"https://wikipedia.org/wiki/German_cruiser_Admiral_<PERSON>_<PERSON>\" title=\"German cruiser Admiral <PERSON>\">Admiral <PERSON></a></i> is scuttled by Captain <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> outside <a href=\"https://wikipedia.org/wiki/Montevideo\" title=\"Montevideo\">Montevideo</a>.", "links": [{"title": "World War II", "link": "https://wikipedia.org/wiki/World_War_II"}, {"title": "Battle of the River Plate", "link": "https://wikipedia.org/wiki/Battle_of_the_River_Plate"}, {"title": "German cruiser Admiral <PERSON>", "link": "https://wikipedia.org/wiki/German_cruiser_Admiral_<PERSON>_<PERSON>"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}, {"title": "Montevideo", "link": "https://wikipedia.org/wiki/Montevideo"}]}, {"year": "1943", "text": "All Chinese are again permitted to become citizens of the United States upon the repeal of the Act of 1882 and the introduction of the Magnuson Act.", "html": "1943 - All Chinese are again permitted to become citizens of the United States upon the repeal of the <a href=\"https://wikipedia.org/wiki/Chinese_Exclusion_Act\" title=\"Chinese Exclusion Act\">Act of 1882</a> and the introduction of the <a href=\"https://wikipedia.org/wiki/Magnuson_Act\" title=\"Magnuson Act\">Magnuson Act</a>.", "no_year_html": "All Chinese are again permitted to become citizens of the United States upon the repeal of the <a href=\"https://wikipedia.org/wiki/Chinese_Exclusion_Act\" title=\"Chinese Exclusion Act\">Act of 1882</a> and the introduction of the <a href=\"https://wikipedia.org/wiki/Magnuson_Act\" title=\"Magnuson Act\">Magnuson Act</a>.", "links": [{"title": "Chinese Exclusion Act", "link": "https://wikipedia.org/wiki/Chinese_Exclusion_Act"}, {"title": "Magnuson Act", "link": "https://wikipedia.org/wiki/Magnuson_Act"}]}, {"year": "1944", "text": "World War II: Battle of the Bulge: Malmedy massacre: American 285th Field Artillery Observation Battalion POWs are shot by Waffen-SS Kampfgruppe <PERSON>.", "html": "1944 - World War II: <a href=\"https://wikipedia.org/wiki/Battle_of_the_Bulge\" title=\"Battle of the Bulge\">Battle of the Bulge</a>: <a href=\"https://wikipedia.org/wiki/Malmedy_massacre\" title=\"Malmedy massacre\">Malmedy massacre</a>: American <a href=\"https://wikipedia.org/wiki/285th_Field_Artillery_Observation_Battalion\" title=\"285th Field Artillery Observation Battalion\">285th Field Artillery Observation Battalion</a> <a href=\"https://wikipedia.org/wiki/Prisoner_of_war\" title=\"Prisoner of war\">POWs</a> are shot by <a href=\"https://wikipedia.org/wiki/Waffen-SS\" title=\"Waffen-SS\">Waffen-SS</a> <a href=\"https://wikipedia.org/wiki/Kampfgruppe\" title=\"Kampfgruppe\">Kampfgruppe</a> <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>.", "no_year_html": "World War II: <a href=\"https://wikipedia.org/wiki/Battle_of_the_Bulge\" title=\"Battle of the Bulge\">Battle of the Bulge</a>: <a href=\"https://wikipedia.org/wiki/Malmedy_massacre\" title=\"Malmedy massacre\">Malmedy massacre</a>: American <a href=\"https://wikipedia.org/wiki/285th_Field_Artillery_Observation_Battalion\" title=\"285th Field Artillery Observation Battalion\">285th Field Artillery Observation Battalion</a> <a href=\"https://wikipedia.org/wiki/Prisoner_of_war\" title=\"Prisoner of war\">POWs</a> are shot by <a href=\"https://wikipedia.org/wiki/Waffen-SS\" title=\"Waffen-SS\">Waffen-SS</a> <a href=\"https://wikipedia.org/wiki/Kampfgruppe\" title=\"Kampfgruppe\">Kampfgruppe</a> <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>.", "links": [{"title": "Battle of the Bulge", "link": "https://wikipedia.org/wiki/Battle_of_the_Bulge"}, {"title": "Malmedy massacre", "link": "https://wikipedia.org/wiki/Malmedy_massacre"}, {"title": "285th Field Artillery Observation Battalion", "link": "https://wikipedia.org/wiki/285th_Field_Artillery_Observation_Battalion"}, {"title": "Prisoner of war", "link": "https://wikipedia.org/wiki/Prisoner_of_war"}, {"title": "Waffen-SS", "link": "https://wikipedia.org/wiki/Waffen-SS"}, {"title": "Kampfgruppe", "link": "https://wikipedia.org/wiki/Kampfgruppe"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1945", "text": "Kurdistan flag day, the flag of Kurdistan was raised for the first time in Mahabad in eastern Kurdistan (Iran).", "html": "1945 - <a href=\"https://wikipedia.org/wiki/Kurdistan\" title=\"Kurdistan\">Kurdistan</a> flag day, the <a href=\"https://wikipedia.org/wiki/Flag_of_Kurdistan\" title=\"Flag of Kurdistan\">flag of Kurdistan</a> was raised for the first time in <a href=\"https://wikipedia.org/wiki/Mahabad\" title=\"Mahabad\">Mahabad</a> in eastern Kurdistan (Iran).", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Kurdistan\" title=\"Kurdistan\">Kurdistan</a> flag day, the <a href=\"https://wikipedia.org/wiki/Flag_of_Kurdistan\" title=\"Flag of Kurdistan\">flag of Kurdistan</a> was raised for the first time in <a href=\"https://wikipedia.org/wiki/Mahabad\" title=\"Mahabad\">Mahabad</a> in eastern Kurdistan (Iran).", "links": [{"title": "Kurdistan", "link": "https://wikipedia.org/wiki/Kurdistan"}, {"title": "Flag of Kurdistan", "link": "https://wikipedia.org/wiki/Flag_of_Kurdistan"}, {"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>"}]}, {"year": "1947", "text": "First flight of the Boeing B-47 Stratojet strategic bomber.", "html": "1947 - First flight of the <a href=\"https://wikipedia.org/wiki/Boeing_B-47_Stratojet\" title=\"Boeing B-47 Stratojet\">Boeing B-47 Stratojet</a> <a href=\"https://wikipedia.org/wiki/Strategic_bomber\" title=\"Strategic bomber\">strategic bomber</a>.", "no_year_html": "First flight of the <a href=\"https://wikipedia.org/wiki/Boeing_B-47_Stratojet\" title=\"Boeing B-47 Stratojet\">Boeing B-47 Stratojet</a> <a href=\"https://wikipedia.org/wiki/Strategic_bomber\" title=\"Strategic bomber\">strategic bomber</a>.", "links": [{"title": "Boeing B-47 Stratojet", "link": "https://wikipedia.org/wiki/Boeing_B-47_Stratojet"}, {"title": "Strategic bomber", "link": "https://wikipedia.org/wiki/Strategic_bomber"}]}, {"year": "1948", "text": "The Finnish Security Police is established to remove communist leadership from its predecessor, the State Police.", "html": "1948 - The <a href=\"https://wikipedia.org/wiki/Finnish_Security_and_Intelligence_Service\" title=\"Finnish Security and Intelligence Service\">Finnish Security Police</a> is established to remove <a href=\"https://wikipedia.org/wiki/Communist_Party_of_Finland\" title=\"Communist Party of Finland\">communist leadership</a> from its predecessor, the <a href=\"https://wikipedia.org/wiki/State_Police_(Finland)\" title=\"State Police (Finland)\">State Police</a>.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Finnish_Security_and_Intelligence_Service\" title=\"Finnish Security and Intelligence Service\">Finnish Security Police</a> is established to remove <a href=\"https://wikipedia.org/wiki/Communist_Party_of_Finland\" title=\"Communist Party of Finland\">communist leadership</a> from its predecessor, the <a href=\"https://wikipedia.org/wiki/State_Police_(Finland)\" title=\"State Police (Finland)\">State Police</a>.", "links": [{"title": "Finnish Security and Intelligence Service", "link": "https://wikipedia.org/wiki/Finnish_Security_and_Intelligence_Service"}, {"title": "Communist Party of Finland", "link": "https://wikipedia.org/wiki/Communist_Party_of_Finland"}, {"title": "State Police (Finland)", "link": "https://wikipedia.org/wiki/State_Police_(Finland)"}]}, {"year": "1950", "text": "The F-86 Sabre's first mission over Korea.", "html": "1950 - The <a href=\"https://wikipedia.org/wiki/North_American_F-86_Sabre\" title=\"North American F-86 Sabre\">F-86 Sabre</a>'s first mission over Korea.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/North_American_F-86_Sabre\" title=\"North American F-86 Sabre\">F-86 Sabre</a>'s first mission over Korea.", "links": [{"title": "North American F-86 Sabre", "link": "https://wikipedia.org/wiki/North_American_F-86_Sabre"}]}, {"year": "1951", "text": "The American Civil Rights Congress delivers \"We Charge Genocide\" to the United Nations.", "html": "1951 - The American <a href=\"https://wikipedia.org/wiki/Civil_Rights_Congress\" title=\"Civil Rights Congress\">Civil Rights Congress</a> delivers \"<a href=\"https://wikipedia.org/wiki/We_Charge_Genocide\" title=\"We Charge Genocide\">We Charge Genocide</a>\" to the <a href=\"https://wikipedia.org/wiki/United_Nations\" title=\"United Nations\">United Nations</a>.", "no_year_html": "The American <a href=\"https://wikipedia.org/wiki/Civil_Rights_Congress\" title=\"Civil Rights Congress\">Civil Rights Congress</a> delivers \"<a href=\"https://wikipedia.org/wiki/We_Charge_Genocide\" title=\"We Charge Genocide\">We Charge Genocide</a>\" to the <a href=\"https://wikipedia.org/wiki/United_Nations\" title=\"United Nations\">United Nations</a>.", "links": [{"title": "Civil Rights Congress", "link": "https://wikipedia.org/wiki/Civil_Rights_Congress"}, {"title": "We Charge Genocide", "link": "https://wikipedia.org/wiki/We_Charge_Genocide"}, {"title": "United Nations", "link": "https://wikipedia.org/wiki/United_Nations"}]}, {"year": "1957", "text": "The United States successfully launches the first Atlas intercontinental ballistic missile at Cape Canaveral, Florida.", "html": "1957 - The United States successfully launches the first <a href=\"https://wikipedia.org/wiki/SM-65_Atlas\" title=\"SM-65 Atlas\">Atlas intercontinental ballistic missile</a> at <a href=\"https://wikipedia.org/wiki/Cape_Canaveral,_Florida\" title=\"Cape Canaveral, Florida\">Cape Canaveral, Florida</a>.", "no_year_html": "The United States successfully launches the first <a href=\"https://wikipedia.org/wiki/SM-65_Atlas\" title=\"SM-65 Atlas\">Atlas intercontinental ballistic missile</a> at <a href=\"https://wikipedia.org/wiki/Cape_Canaveral,_Florida\" title=\"Cape Canaveral, Florida\">Cape Canaveral, Florida</a>.", "links": [{"title": "SM-65 Atlas", "link": "https://wikipedia.org/wiki/SM-65_Atlas"}, {"title": "Cape Canaveral, Florida", "link": "https://wikipedia.org/wiki/Cape_Canaveral,_Florida"}]}, {"year": "1960", "text": "Troops loyal to Emperor <PERSON><PERSON> in Ethiopia crush the coup that began December 13, returning power to their leader upon his return from Brazil. <PERSON><PERSON> absolves his son of any guilt.", "html": "1960 - Troops loyal to Emperor <a href=\"https://wikipedia.org/wiki/Haile_Selassie\" title=\"<PERSON>le Selassie\"><PERSON><PERSON> Se<PERSON>ie</a> in <a href=\"https://wikipedia.org/wiki/Ethiopia\" title=\"Ethiopia\">Ethiopia</a> crush the <a href=\"https://wikipedia.org/wiki/1960_Ethiopian_coup_attempt\" title=\"1960 Ethiopian coup attempt\">coup</a> that began <a href=\"https://wikipedia.org/wiki/December_13\" title=\"December 13\">December 13</a>, returning power to their leader upon his return from Brazil. Hai<PERSON>ie absolves his son of any guilt.", "no_year_html": "Troops loyal to Emperor <a href=\"https://wikipedia.org/wiki/Haile_Selassie\" title=\"<PERSON>le Selassie\"><PERSON><PERSON> Se<PERSON>ie</a> in <a href=\"https://wikipedia.org/wiki/Ethiopia\" title=\"Ethiopia\">Ethiopia</a> crush the <a href=\"https://wikipedia.org/wiki/1960_Ethiopian_coup_attempt\" title=\"1960 Ethiopian coup attempt\">coup</a> that began <a href=\"https://wikipedia.org/wiki/December_13\" title=\"December 13\">December 13</a>, returning power to their leader upon his return from Brazil. Hai<PERSON>ie absolves his son of any guilt.", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}, {"title": "Ethiopia", "link": "https://wikipedia.org/wiki/Ethiopia"}, {"title": "1960 Ethiopian coup attempt", "link": "https://wikipedia.org/wiki/1960_Ethiopian_coup_attempt"}, {"title": "December 13", "link": "https://wikipedia.org/wiki/December_13"}]}, {"year": "1960", "text": "Munich C-131 crash: Twenty passengers and crew on board as well as 32 people on the ground are killed.", "html": "1960 - <a href=\"https://wikipedia.org/wiki/1960_Munich_C-131_crash\" title=\"1960 Munich C-131 crash\">Munich C-131 crash</a>: Twenty passengers and crew on board as well as 32 people on the ground are killed.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/1960_Munich_C-131_crash\" title=\"1960 Munich C-131 crash\">Munich C-131 crash</a>: Twenty passengers and crew on board as well as 32 people on the ground are killed.", "links": [{"title": "1960 Munich C-131 crash", "link": "https://wikipedia.org/wiki/1960_Munich_C-131_crash"}]}, {"year": "1961", "text": "Niterói circus fire: Fire breaks out during a performance by the Gran Circus Norte-Americano in the city of Niterói, Rio de Janeiro, Brazil, killing more than 500.", "html": "1961 - <a href=\"https://wikipedia.org/wiki/Niter%C3%B3i_circus_fire\" title=\"Niterói circus fire\">Niterói circus fire</a>: Fire breaks out during a performance by the Gran Circus Norte-Americano in the city of <a href=\"https://wikipedia.org/wiki/Niter%C3%B3i\" title=\"Niteró<PERSON>\">Niterói, Rio de Janeiro</a>, Brazil, killing more than 500.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Niter%C3%B3i_circus_fire\" title=\"Niterói circus fire\">Niterói circus fire</a>: Fire breaks out during a performance by the Gran Circus Norte-Americano in the city of <a href=\"https://wikipedia.org/wiki/Niter%C3%B3i\" title=\"Niteró<PERSON>\">Niterói, Rio de Janeiro</a>, Brazil, killing more than 500.", "links": [{"title": "Niterói circus fire", "link": "https://wikipedia.org/wiki/Niter%C3%B3i_circus_fire"}, {"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Niter%C3%B3i"}]}, {"year": "1967", "text": "<PERSON>, Prime Minister of Australia, disappears while swimming near Portsea, Victoria, and is presumed drowned.", "html": "1967 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Australia\" title=\"Prime Minister of Australia\">Prime Minister of Australia</a>, <a href=\"https://wikipedia.org/wiki/Disappearance_of_<PERSON>_<PERSON>\" title=\"Disappearance of <PERSON>\">disappears while swimming</a> near <a href=\"https://wikipedia.org/wiki/Portsea,_Victoria\" title=\"Portsea, Victoria\">Portsea, Victoria</a>, and is presumed drowned.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Australia\" title=\"Prime Minister of Australia\">Prime Minister of Australia</a>, <a href=\"https://wikipedia.org/wiki/Disappearance_of_<PERSON>_<PERSON>\" title=\"Disappearance of <PERSON>\">disappears while swimming</a> near <a href=\"https://wikipedia.org/wiki/Portsea,_Victoria\" title=\"Portsea, Victoria\">Portsea, Victoria</a>, and is presumed drowned.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Prime Minister of Australia", "link": "https://wikipedia.org/wiki/Prime_Minister_of_Australia"}, {"title": "Disappearance of <PERSON>", "link": "https://wikipedia.org/wiki/Disappearance_of_<PERSON>_<PERSON>"}, {"title": "Portsea, Victoria", "link": "https://wikipedia.org/wiki/Portsea,_Victoria"}]}, {"year": "1969", "text": "Project Blue Book: The United States Air Force closes its study of UFOs.", "html": "1969 - <a href=\"https://wikipedia.org/wiki/Project_Blue_Book\" title=\"Project Blue Book\">Project Blue Book</a>: The United States Air Force closes its study of <a href=\"https://wikipedia.org/wiki/Unidentified_flying_object\" title=\"Unidentified flying object\">UFOs</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Project_Blue_Book\" title=\"Project Blue Book\">Project Blue Book</a>: The United States Air Force closes its study of <a href=\"https://wikipedia.org/wiki/Unidentified_flying_object\" title=\"Unidentified flying object\">UFOs</a>.", "links": [{"title": "Project Blue Book", "link": "https://wikipedia.org/wiki/Project_Blue_Book"}, {"title": "Unidentified flying object", "link": "https://wikipedia.org/wiki/Unidentified_flying_object"}]}, {"year": "1970", "text": "Polish protests: In Gdynia, soldiers fire at workers emerging from trains, killing dozens.", "html": "1970 - <a href=\"https://wikipedia.org/wiki/1970_Polish_protests\" title=\"1970 Polish protests\">Polish protests</a>: In <a href=\"https://wikipedia.org/wiki/Gdynia\" title=\"Gdynia\">Gdynia</a>, soldiers fire at workers emerging from trains, killing dozens.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/1970_Polish_protests\" title=\"1970 Polish protests\">Polish protests</a>: In <a href=\"https://wikipedia.org/wiki/Gdynia\" title=\"Gdynia\">Gdynia</a>, soldiers fire at workers emerging from trains, killing dozens.", "links": [{"title": "1970 Polish protests", "link": "https://wikipedia.org/wiki/1970_Polish_protests"}, {"title": "Gdynia", "link": "https://wikipedia.org/wiki/Gdynia"}]}, {"year": "1973", "text": "Thirty passengers are killed in an attack by Palestinian terrorists on Rome's Leonardo da Vinci-Fiumicino Airport.", "html": "1973 - Thirty passengers are killed in <a href=\"https://wikipedia.org/wiki/1973_Rome_airport_attacks_and_hijacking\" title=\"1973 Rome airport attacks and hijacking\">an attack</a> by <a href=\"https://wikipedia.org/wiki/Palestinian_political_violence\" title=\"Palestinian political violence\">Palestinian terrorists</a> on Rome's <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>%E2%80%93Fiumicino_Airport\" class=\"mw-redirect\" title=\"Leonardo da Vinci-Fiumicino Airport\">Leonardo <PERSON>-Fi<PERSON>cino Airport</a>.", "no_year_html": "Thirty passengers are killed in <a href=\"https://wikipedia.org/wiki/1973_Rome_airport_attacks_and_hijacking\" title=\"1973 Rome airport attacks and hijacking\">an attack</a> by <a href=\"https://wikipedia.org/wiki/Palestinian_political_violence\" title=\"Palestinian political violence\">Palestinian terrorists</a> on Rome's <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>%E2%80%93Fiumicino_Airport\" class=\"mw-redirect\" title=\"Leonardo da Vinci-Fiumicino Airport\">Leonardo <PERSON>-Fiumicino Airport</a>.", "links": [{"title": "1973 Rome airport attacks and hijacking", "link": "https://wikipedia.org/wiki/1973_Rome_airport_attacks_and_hijacking"}, {"title": "Palestinian political violence", "link": "https://wikipedia.org/wiki/Palestinian_political_violence"}, {"title": "Leonardo da Vinci-Fiumicino Airport", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_Vinci%E2%80%93Fiumicino_Airport"}]}, {"year": "1981", "text": "American Brigadier General <PERSON> is abducted by the Red Brigades in Verona, Italy.", "html": "1981 - American Brigadier General <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> is abducted by the <a href=\"https://wikipedia.org/wiki/Red_Brigades\" title=\"Red Brigades\">Red Brigades</a> in <a href=\"https://wikipedia.org/wiki/Verona\" title=\"Verona\">Verona</a>, Italy.", "no_year_html": "American Brigadier General <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> is abducted by the <a href=\"https://wikipedia.org/wiki/Red_Brigades\" title=\"Red Brigades\">Red Brigades</a> in <a href=\"https://wikipedia.org/wiki/Verona\" title=\"Verona\">Verona</a>, Italy.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "Red Brigades", "link": "https://wikipedia.org/wiki/Red_Brigades"}, {"title": "Verona", "link": "https://wikipedia.org/wiki/Verona"}]}, {"year": "1983", "text": "Provisional IRA members detonate a car bomb at Harrods Department Store in London. Three police officers and three civilians are killed.", "html": "1983 - <a href=\"https://wikipedia.org/wiki/Provisional_Irish_Republican_Army\" title=\"Provisional Irish Republican Army\">Provisional IRA</a> members <a href=\"https://wikipedia.org/wiki/Harrods_bombings\" class=\"mw-redirect\" title=\"Harrods bombings\">detonate a car bomb</a> at <a href=\"https://wikipedia.org/wiki/Harrods\" title=\"Harrods\">Harrods</a> Department Store in London. Three police officers and three civilians are killed.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Provisional_Irish_Republican_Army\" title=\"Provisional Irish Republican Army\">Provisional IRA</a> members <a href=\"https://wikipedia.org/wiki/Harrods_bombings\" class=\"mw-redirect\" title=\"Harrods bombings\">detonate a car bomb</a> at <a href=\"https://wikipedia.org/wiki/Harrods\" title=\"Harrods\">Harrods</a> Department Store in London. Three police officers and three civilians are killed.", "links": [{"title": "Provisional Irish Republican Army", "link": "https://wikipedia.org/wiki/Provisional_Irish_Republican_Army"}, {"title": "Harrods bombings", "link": "https://wikipedia.org/wiki/Harrods_bombings"}, {"title": "Harrods", "link": "https://wikipedia.org/wiki/Harrods"}]}, {"year": "1989", "text": "Romanian Revolution: Protests continue in Timișoara, Romania, with rioters breaking into the Romanian Communist Party's District Committee building and attempting to set it on fire.", "html": "1989 - <a href=\"https://wikipedia.org/wiki/Romanian_Revolution\" class=\"mw-redirect\" title=\"Romanian Revolution\">Romanian Revolution</a>: Protests continue in <a href=\"https://wikipedia.org/wiki/Timi%C8%99oara\" title=\"Timișoara\">Timișoara</a>, Romania, with rioters breaking into the <a href=\"https://wikipedia.org/wiki/Romanian_Communist_Party\" title=\"Romanian Communist Party\">Romanian Communist Party</a>'s District Committee building and attempting to set it on fire.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Romanian_Revolution\" class=\"mw-redirect\" title=\"Romanian Revolution\">Romanian Revolution</a>: Protests continue in <a href=\"https://wikipedia.org/wiki/Timi%C8%99oara\" title=\"Timișoara\">Timișoara</a>, Romania, with rioters breaking into the <a href=\"https://wikipedia.org/wiki/Romanian_Communist_Party\" title=\"Romanian Communist Party\">Romanian Communist Party</a>'s District Committee building and attempting to set it on fire.", "links": [{"title": "Romanian Revolution", "link": "https://wikipedia.org/wiki/Romanian_Revolution"}, {"title": "Timișoara", "link": "https://wikipedia.org/wiki/Timi%C8%99oara"}, {"title": "Romanian Communist Party", "link": "https://wikipedia.org/wiki/Romanian_Communist_Party"}]}, {"year": "1989", "text": "<PERSON> defeats <PERSON><PERSON> in the second round of the Brazilian presidential election, becoming the first democratically elected President in almost 30 years.", "html": "1989 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> defeats <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_In%C3%<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a> in the second round of the <a href=\"https://wikipedia.org/wiki/Brazilian_presidential_election,_1989\" class=\"mw-redirect\" title=\"Brazilian presidential election, 1989\">Brazilian presidential election</a>, becoming the first democratically elected <a href=\"https://wikipedia.org/wiki/President_of_Brazil\" title=\"President of Brazil\">President</a> in almost 30 years.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> defeats <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_In%C3%<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a> in the second round of the <a href=\"https://wikipedia.org/wiki/Brazilian_presidential_election,_1989\" class=\"mw-redirect\" title=\"Brazilian presidential election, 1989\">Brazilian presidential election</a>, becoming the first democratically elected <a href=\"https://wikipedia.org/wiki/President_of_Brazil\" title=\"President of Brazil\">President</a> in almost 30 years.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>"}, {"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Luiz_In%C3%<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>_<PERSON>"}, {"title": "Brazilian presidential election, 1989", "link": "https://wikipedia.org/wiki/Brazilian_presidential_election,_1989"}, {"title": "President of Brazil", "link": "https://wikipedia.org/wiki/President_of_Brazil"}]}, {"year": "1989", "text": "The Simpsons premieres on television with the episode \"Simpsons Roasting on an Open Fire\".", "html": "1989 - <i><a href=\"https://wikipedia.org/wiki/The_Simpsons\" title=\"The Simpsons\">The Simpsons</a></i> premieres on television with the episode \"<a href=\"https://wikipedia.org/wiki/Simpsons_Roasting_on_an_Open_Fire\" title=\"Simpsons Roasting on an Open Fire\">Simpsons Roasting on an Open Fire</a>\".", "no_year_html": "<i><a href=\"https://wikipedia.org/wiki/The_Simpsons\" title=\"The Simpsons\">The Simpsons</a></i> premieres on television with the episode \"<a href=\"https://wikipedia.org/wiki/Simpsons_Roasting_on_an_Open_Fire\" title=\"Simpsons Roasting on an Open Fire\">Simpsons Roasting on an Open Fire</a>\".", "links": [{"title": "The Simpsons", "link": "https://wikipedia.org/wiki/The_Simpsons"}, {"title": "Simpsons Roasting on an Open Fire", "link": "https://wikipedia.org/wiki/Simpsons_Roasting_on_an_Open_Fire"}]}, {"year": "1997", "text": "Peruvian internal conflict: Fourteen members of the Túpac Amaru Revolutionary Movement provoke a hostage crisis by taking over the Japanese embassy in Lima.", "html": "1997 - <a href=\"https://wikipedia.org/wiki/Peruvian_internal_conflict\" class=\"mw-redirect\" title=\"Peruvian internal conflict\">Peruvian internal conflict</a>: Fourteen members of the <a href=\"https://wikipedia.org/wiki/T%C3%BApac_Amaru_Revolutionary_Movement\" title=\"Túpac Amaru Revolutionary Movement\">Túpac Amaru Revolutionary Movement</a> provoke a <a href=\"https://wikipedia.org/wiki/Japanese_embassy_hostage_crisis\" title=\"Japanese embassy hostage crisis\">hostage crisis by taking over the Japanese embassy</a> in Lima.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Peruvian_internal_conflict\" class=\"mw-redirect\" title=\"Peruvian internal conflict\">Peruvian internal conflict</a>: Fourteen members of the <a href=\"https://wikipedia.org/wiki/T%C3%BApac_Amaru_Revolutionary_Movement\" title=\"Túpac Amaru Revolutionary Movement\">Túpac Amaru Revolutionary Movement</a> provoke a <a href=\"https://wikipedia.org/wiki/Japanese_embassy_hostage_crisis\" title=\"Japanese embassy hostage crisis\">hostage crisis by taking over the Japanese embassy</a> in Lima.", "links": [{"title": "Peruvian internal conflict", "link": "https://wikipedia.org/wiki/Peruvian_internal_conflict"}, {"title": "Túpac Amaru Revolutionary Movement", "link": "https://wikipedia.org/wiki/T%C3%BApac_Amaru_Revolutionary_Movement"}, {"title": "Japanese embassy hostage crisis", "link": "https://wikipedia.org/wiki/Japanese_embassy_hostage_crisis"}]}, {"year": "1997", "text": "Aerosvit Flight 241: A Yakovlev Yak-42 crashes into the Pierian Mountains near Thessaloniki Airport in Thessaloniki, Greece, killing all 70 people on board.", "html": "1997 - <a href=\"https://wikipedia.org/wiki/Aerosvit_Flight_241\" title=\"Aerosvit Flight 241\">Aerosvit Flight 241</a>: A <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_Yak-42\" title=\"<PERSON><PERSON><PERSON> Yak-42\"><PERSON><PERSON><PERSON> Yak-42</a> crashes into the <a href=\"https://wikipedia.org/wiki/Pierian_Mountains\" title=\"Pierian Mountains\">Pierian Mountains</a> near <a href=\"https://wikipedia.org/wiki/Thessaloniki_Airport\" title=\"Thessaloniki Airport\">Thessaloniki Airport</a> in <a href=\"https://wikipedia.org/wiki/Thessaloniki\" title=\"Thessaloniki\">Thessaloniki</a>, Greece, killing all 70 people on board.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Aerosvit_Flight_241\" title=\"Aerosvit Flight 241\">Aerosvit Flight 241</a>: A <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_Yak-42\" title=\"<PERSON><PERSON><PERSON> Yak-42\"><PERSON><PERSON><PERSON> Yak-42</a> crashes into the <a href=\"https://wikipedia.org/wiki/Pierian_Mountains\" title=\"Pierian Mountains\">Pierian Mountains</a> near <a href=\"https://wikipedia.org/wiki/Thessaloniki_Airport\" title=\"Thessaloniki Airport\">Thessaloniki Airport</a> in <a href=\"https://wikipedia.org/wiki/Thessaloniki\" title=\"Thessaloniki\">Thessaloniki</a>, Greece, killing all 70 people on board.", "links": [{"title": "Aerosvit Flight 241", "link": "https://wikipedia.org/wiki/Aerosvit_Flight_241"}, {"title": "<PERSON><PERSON><PERSON>-42", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_Yak-42"}, {"title": "Pierian Mountains", "link": "https://wikipedia.org/wiki/Pierian_Mountains"}, {"title": "Thessaloniki Airport", "link": "https://wikipedia.org/wiki/Thessaloniki_Airport"}, {"title": "Thessaloniki", "link": "https://wikipedia.org/wiki/Thessaloniki"}]}, {"year": "2002", "text": "Second Congo War: The Congolese parties of the Inter Congolese Dialogue sign a peace accord which makes provision for transitional governance and legislative and presidential elections within two years.", "html": "2002 - <a href=\"https://wikipedia.org/wiki/Second_Congo_War\" title=\"Second Congo War\">Second Congo War</a>: The Congolese parties of the Inter Congolese Dialogue sign a peace accord which makes provision for <a href=\"https://wikipedia.org/wiki/Transitional_Government_of_the_Democratic_Republic_of_the_Congo\" title=\"Transitional Government of the Democratic Republic of the Congo\">transitional governance</a> and legislative and presidential elections within two years.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Second_Congo_War\" title=\"Second Congo War\">Second Congo War</a>: The Congolese parties of the Inter Congolese Dialogue sign a peace accord which makes provision for <a href=\"https://wikipedia.org/wiki/Transitional_Government_of_the_Democratic_Republic_of_the_Congo\" title=\"Transitional Government of the Democratic Republic of the Congo\">transitional governance</a> and legislative and presidential elections within two years.", "links": [{"title": "Second Congo War", "link": "https://wikipedia.org/wiki/Second_Congo_War"}, {"title": "Transitional Government of the Democratic Republic of the Congo", "link": "https://wikipedia.org/wiki/Transitional_Government_of_the_Democratic_Republic_of_the_Congo"}]}, {"year": "2003", "text": "The <PERSON><PERSON> murder trial ends at the Old Bailey in London, with <PERSON> found guilty of two counts of murder.  His girlfriend, <PERSON><PERSON>, is found guilty of perverting the course of justice.", "html": "2003 - The <a href=\"https://wikipedia.org/wiki/Soham_murders\" title=\"Soham murders\"><PERSON>ham murder</a> trial ends at the Old Bailey in London, with <PERSON> found guilty of two counts of murder. His girlfriend, <PERSON><PERSON>, is found guilty of perverting the course of justice.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Soham_murders\" title=\"Soham murders\">Soham murder</a> trial ends at the Old Bailey in London, with <PERSON> found guilty of two counts of murder. His girlfriend, <PERSON><PERSON>, is found guilty of perverting the course of justice.", "links": [{"title": "<PERSON><PERSON> murders", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_murders"}]}, {"year": "2003", "text": "SpaceShipOne, piloted by <PERSON>, makes its first powered and first supersonic flight.", "html": "2003 - <a href=\"https://wikipedia.org/wiki/SpaceShipOne\" title=\"SpaceShipOne\">SpaceShipOne</a>, piloted by <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, makes <a href=\"https://wikipedia.org/wiki/SpaceShipOne_flight_11P\" title=\"SpaceShipOne flight 11P\">its first powered and first supersonic flight</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/SpaceShipOne\" title=\"SpaceShipOne\">SpaceShipOne</a>, piloted by <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, makes <a href=\"https://wikipedia.org/wiki/SpaceShipOne_flight_11P\" title=\"SpaceShipOne flight 11P\">its first powered and first supersonic flight</a>.", "links": [{"title": "SpaceShipOne", "link": "https://wikipedia.org/wiki/SpaceShipOne"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "SpaceShipOne flight 11P", "link": "https://wikipedia.org/wiki/SpaceShipOne_flight_11P"}]}, {"year": "2003", "text": "Sex work rights activists establish December 17 (or \"D17\") as International Day to End Violence Against Sex Workers to memorialize victims of a serial killer who targeted prostitutes, and highlight State violence against sex workers by police and others.", "html": "2003 - Sex work rights activists establish December 17 (or \"D17\") as <a href=\"https://wikipedia.org/wiki/International_Day_to_End_Violence_Against_Sex_Workers\" title=\"International Day to End Violence Against Sex Workers\">International Day to End Violence Against Sex Workers</a> to memorialize victims of a serial killer who targeted prostitutes, and highlight State violence against sex workers by police and others.", "no_year_html": "Sex work rights activists establish December 17 (or \"D17\") as <a href=\"https://wikipedia.org/wiki/International_Day_to_End_Violence_Against_Sex_Workers\" title=\"International Day to End Violence Against Sex Workers\">International Day to End Violence Against Sex Workers</a> to memorialize victims of a serial killer who targeted prostitutes, and highlight State violence against sex workers by police and others.", "links": [{"title": "International Day to End Violence Against Sex Workers", "link": "https://wikipedia.org/wiki/International_Day_to_End_Violence_Against_Sex_Workers"}]}, {"year": "2005", "text": "Anti-World Trade Organization protesters riot in Wan Chai, Hong Kong.", "html": "2005 - Anti-<a href=\"https://wikipedia.org/wiki/World_Trade_Organization\" title=\"World Trade Organization\">World Trade Organization</a> <a href=\"https://wikipedia.org/wiki/World_Trade_Organization_Ministerial_Conference_of_2005#17_December_2005\" title=\"World Trade Organization Ministerial Conference of 2005\">protesters riot in Wan Chai, Hong Kong</a>.", "no_year_html": "Anti-<a href=\"https://wikipedia.org/wiki/World_Trade_Organization\" title=\"World Trade Organization\">World Trade Organization</a> <a href=\"https://wikipedia.org/wiki/World_Trade_Organization_Ministerial_Conference_of_2005#17_December_2005\" title=\"World Trade Organization Ministerial Conference of 2005\">protesters riot in Wan Chai, Hong Kong</a>.", "links": [{"title": "World Trade Organization", "link": "https://wikipedia.org/wiki/World_Trade_Organization"}, {"title": "World Trade Organization Ministerial Conference of 2005", "link": "https://wikipedia.org/wiki/World_Trade_Organization_Ministerial_Conference_of_2005#17_December_2005"}]}, {"year": "2005", "text": "<PERSON><PERSON><PERSON> abdicates the throne as King of Bhutan.", "html": "2005 - <a href=\"https://wikipedia.org/wiki/Jig<PERSON>_<PERSON>_<PERSON>\" title=\"Jigme <PERSON>\">Jig<PERSON></a> abdicates the throne as King of <a href=\"https://wikipedia.org/wiki/Bhutan\" title=\"Bhutan\">Bhutan</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Jig<PERSON>_<PERSON>_<PERSON>\" title=\"Jigme <PERSON>\">Jig<PERSON></a> abdicates the throne as King of <a href=\"https://wikipedia.org/wiki/Bhutan\" title=\"Bhutan\">Bhutan</a>.", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Jig<PERSON>_<PERSON><PERSON>_<PERSON>ck"}, {"title": "Bhutan", "link": "https://wikipedia.org/wiki/Bhutan"}]}, {"year": "2009", "text": "MV Danny F II sinks off the coast of Lebanon, resulting in the deaths of 44 people and over 28,000 animals.", "html": "2009 - <a href=\"https://wikipedia.org/wiki/MV_Danny_F_II\" title=\"MV Danny F II\">MV <i>Danny F II</i></a> sinks off the coast of <a href=\"https://wikipedia.org/wiki/Lebanon\" title=\"Lebanon\">Lebanon</a>, resulting in the deaths of 44 people and over 28,000 animals.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/MV_Danny_F_II\" title=\"MV Danny F II\">MV <i>Danny F II</i></a> sinks off the coast of <a href=\"https://wikipedia.org/wiki/Lebanon\" title=\"Lebanon\">Lebanon</a>, resulting in the deaths of 44 people and over 28,000 animals.", "links": [{"title": "MV Danny F II", "link": "https://wikipedia.org/wiki/MV_<PERSON>_F_II"}, {"title": "Lebanon", "link": "https://wikipedia.org/wiki/Lebanon"}]}, {"year": "2010", "text": "<PERSON> sets himself on fire. This act became the catalyst for the Tunisian Revolution and the wider Arab Spring.", "html": "2010 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> <a href=\"https://wikipedia.org/wiki/Self-immolation\" title=\"Self-immolation\">sets himself on fire</a>. This act became the catalyst for the <a href=\"https://wikipedia.org/wiki/Tunisian_Revolution\" class=\"mw-redirect\" title=\"Tunisian Revolution\">Tunisian Revolution</a> and the wider <a href=\"https://wikipedia.org/wiki/Arab_Spring\" title=\"Arab Spring\">Arab Spring</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> <a href=\"https://wikipedia.org/wiki/Self-immolation\" title=\"Self-immolation\">sets himself on fire</a>. This act became the catalyst for the <a href=\"https://wikipedia.org/wiki/Tunisian_Revolution\" class=\"mw-redirect\" title=\"Tunisian Revolution\">Tunisian Revolution</a> and the wider <a href=\"https://wikipedia.org/wiki/Arab_Spring\" title=\"Arab Spring\">Arab Spring</a>.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}, {"title": "Self-immolation", "link": "https://wikipedia.org/wiki/Self-immolation"}, {"title": "Tunisian Revolution", "link": "https://wikipedia.org/wiki/Tunisian_Revolution"}, {"title": "Arab Spring", "link": "https://wikipedia.org/wiki/Arab_Spring"}]}, {"year": "2014", "text": "The United States and Cuba re-establish diplomatic relations after severing them in 1961.", "html": "2014 - The United States and Cuba re-establish <a href=\"https://wikipedia.org/wiki/Cuba%E2%80%93United_States_relations\" title=\"Cuba-United States relations\">diplomatic relations</a> after severing them in 1961.", "no_year_html": "The United States and Cuba re-establish <a href=\"https://wikipedia.org/wiki/Cuba%E2%80%93United_States_relations\" title=\"Cuba-United States relations\">diplomatic relations</a> after severing them in 1961.", "links": [{"title": "Cuba-United States relations", "link": "https://wikipedia.org/wiki/Cuba%E2%80%93United_States_relations"}]}], "Births": [{"year": "1239", "text": "<PERSON><PERSON>, Japanese shōgun (d. 1256)", "html": "1239 - <a href=\"https://wikipedia.org/wiki/Kuj%C5%8D_Yoritsugu\" title=\"Kujō Yoritsugu\"><PERSON><PERSON> Yoritsugu</a>, Japanese shōgun (d. 1256)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Kuj%C5%8D_Yoritsugu\" title=\"Kujō Yoritsugu\"><PERSON><PERSON> Yoritsugu</a>, Japanese shōgun (d. 1256)", "links": [{"title": "Kujō Yoritsugu", "link": "https://wikipedia.org/wiki/Kuj%C5%8D_Yoritsugu"}]}, {"year": "1267", "text": "Emperor <PERSON><PERSON><PERSON><PERSON> of Japan (d. 1324)", "html": "1267 - <a href=\"https://wikipedia.org/wiki/Emperor_<PERSON>-<PERSON>da\" title=\"Emperor <PERSON><PERSON>Uda\">Emperor <PERSON><PERSON><PERSON><PERSON></a> of Japan (d. 1324)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Emperor_<PERSON>-<PERSON>da\" title=\"Emperor <PERSON>-Uda\">Emperor <PERSON><PERSON><PERSON><PERSON></a> of Japan (d. 1324)", "links": [{"title": "Emperor <PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>-<PERSON>"}]}, {"year": "1554", "text": "<PERSON> of Bavaria, Roman Catholic bishop (d. 1612)", "html": "1554 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_Bavaria\" title=\"<PERSON> of Bavaria\"><PERSON> of Bavaria</a>, Roman Catholic bishop (d. 1612)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_Bavaria\" title=\"<PERSON> of Bavaria\"><PERSON> of Bavaria</a>, Roman Catholic bishop (d. 1612)", "links": [{"title": "<PERSON> of Bavaria", "link": "https://wikipedia.org/wiki/Ernest_<PERSON>_Bavaria"}]}, {"year": "1556", "text": "<PERSON>, poet in Mughal Empire (d. 1627)", "html": "1556 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>-<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, poet in Mughal Empire (d. 1627)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>-<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, poet in Mughal Empire (d. 1627)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>-<PERSON>-<PERSON>"}]}, {"year": "1574", "text": "<PERSON>, 3rd Duke of Osuna, Spanish nobleman and politician (d. 1624)", "html": "1574 - <a href=\"https://wikipedia.org/wiki/<PERSON>_T%C3%A9llez-Gir%C3%B3n,_3rd_Duke_of_Osuna\" title=\"<PERSON>, 3rd Duke of Osuna\"><PERSON>, 3rd Duke of Osuna</a>, Spanish nobleman and politician (d. 1624)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_T%C3%A9llez-Gir%C3%B3n,_3rd_Duke_of_Osuna\" title=\"<PERSON>, 3rd Duke of Osuna\"><PERSON>, 3rd Duke of Osuna</a>, Spanish nobleman and politician (d. 1624)", "links": [{"title": "<PERSON><PERSON>, 3rd Duke of Osuna", "link": "https://wikipedia.org/wiki/Pedro_T%C3%A9llez-Gir%C3%B3n,_3rd_Duke_of_Osuna"}]}, {"year": "1616", "text": "<PERSON>, English pamphleteer and author (d. 1704)", "html": "1616 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%27Estrange\" title=\"<PERSON>\"><PERSON></a>, English pamphleteer and author (d. 1704)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%27Estrange\" title=\"<PERSON>\"><PERSON></a>, English pamphleteer and author (d. 1704)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Roger_L%27Estrange"}]}, {"year": "1619", "text": "<PERSON> of the Rhine (d. 1682)", "html": "1619 - <a href=\"https://wikipedia.org/wiki/Prince_<PERSON>_of_the_Rhine\" title=\"Prince <PERSON> of the Rhine\">Prince <PERSON> of the Rhine</a> (d. 1682)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Prince_<PERSON>_of_the_Rhine\" title=\"Prince <PERSON> of the Rhine\">Prince <PERSON> of the Rhine</a> (d. 1682)", "links": [{"title": "<PERSON> of the Rhine", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_the_Rhine"}]}, {"year": "1632", "text": "<PERSON>, English historian and author (d. 1695)", "html": "1632 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(antiquary)\" title=\"<PERSON> (antiquary)\"><PERSON></a>, English historian and author (d. 1695)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(antiquary)\" title=\"<PERSON> (antiquary)\"><PERSON></a>, English historian and author (d. 1695)", "links": [{"title": "<PERSON> (antiquary)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(antiquary)"}]}, {"year": "1685", "text": "<PERSON>, English poet (d. 1740)", "html": "1685 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English poet (d. 1740)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English poet (d. 1740)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1699", "text": "<PERSON><PERSON><PERSON>, French composer and educator (d. 1775)", "html": "1699 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, French composer and educator (d. 1775)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, French composer and educator (d. 1775)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1706", "text": "<PERSON><PERSON><PERSON>, French mathematician and physicist (d. 1749)", "html": "1706 - <a href=\"https://wikipedia.org/wiki/%C3%89milie_du_Ch%C3%A2telet\" title=\"<PERSON><PERSON><PERSON> du Châtelet\"><PERSON><PERSON><PERSON></a>, French mathematician and physicist (d. 1749)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/%C3%89milie_du_Ch%C3%A2telet\" title=\"<PERSON><PERSON><PERSON> du Châtelet\"><PERSON><PERSON><PERSON></a>, French mathematician and physicist (d. 1749)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/%C3%89milie_du_Ch%C3%A2telet"}]}, {"year": "1734", "text": "<PERSON> of Portugal (d. 1816)", "html": "1734 - <a href=\"https://wikipedia.org/wiki/Maria_I_of_Portugal\" title=\"Maria I of Portugal\"><PERSON> of Portugal</a> (d. 1816)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Maria_I_of_Portugal\" title=\"Maria I of Portugal\"><PERSON> of Portugal</a> (d. 1816)", "links": [{"title": "<PERSON> of Portugal", "link": "https://wikipedia.org/wiki/Maria_I_of_Portugal"}]}, {"year": "1749", "text": "<PERSON>, Italian composer and educator (d. 1801)", "html": "1749 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian composer and educator (d. 1801)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian composer and educator (d. 1801)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1778", "text": "<PERSON><PERSON><PERSON><PERSON>, English chemist and physicist (d. 1829)", "html": "1778 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON> <PERSON>\"><PERSON><PERSON><PERSON><PERSON> <PERSON></a>, English chemist and physicist (d. 1829)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON> <PERSON>\"><PERSON><PERSON><PERSON><PERSON> <PERSON></a>, English chemist and physicist (d. 1829)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1796", "text": "<PERSON>, Canadian judge and politician (d. 1865)", "html": "1796 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian judge and politician (d. 1865)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian judge and politician (d. 1865)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1797", "text": "<PERSON>, American physicist and engineer (d. 1878)", "html": "1797 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American physicist and engineer (d. 1878)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American physicist and engineer (d. 1878)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1807", "text": "<PERSON>, American poet and activist (d. 1892)", "html": "1807 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American poet and activist (d. 1892)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American poet and activist (d. 1892)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>hittier"}]}, {"year": "1812", "text": "<PERSON><PERSON><PERSON><PERSON>, Danish painter (d. 1880)", "html": "1812 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>_(painter)\" title=\"<PERSON><PERSON><PERSON><PERSON> (painter)\"><PERSON><PERSON><PERSON><PERSON></a>, Danish painter (d. 1880)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>_(painter)\" title=\"<PERSON><PERSON><PERSON><PERSON> (painter)\"><PERSON><PERSON><PERSON><PERSON></a>, Danish painter (d. 1880)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON> (painter)", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>_(painter)"}]}, {"year": "1827", "text": "<PERSON>, Austrian lawyer and politician (d. 1893)", "html": "1827 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Austrian lawyer and politician (d. 1893)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Austrian lawyer and politician (d. 1893)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1830", "text": "<PERSON>, French author and critic (d. 1870)", "html": "1830 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French author and critic (d. 1870)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French author and critic (d. 1870)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1835", "text": "<PERSON>, Swiss-American ichthyologist and engineer (d. 1910)", "html": "1835 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, Swiss-American ichthyologist and engineer (d. 1910)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, Swiss-American ichthyologist and engineer (d. 1910)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1840", "text": "<PERSON><PERSON>, Japanese field marshal (d. 1908)", "html": "1840 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>tsu<PERSON>\"><PERSON><PERSON></a>, Japanese field marshal (d. 1908)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>tsu<PERSON>\"><PERSON><PERSON></a>, Japanese field marshal (d. 1908)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>ra"}]}, {"year": "1842", "text": "<PERSON><PERSON>, Norwegian mathematician and academic (d. 1899)", "html": "1842 - <a href=\"https://wikipedia.org/wiki/Sophus_Lie\" title=\"Sophus Lie\"><PERSON><PERSON> Lie</a>, Norwegian mathematician and academic (d. 1899)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Sophus_Lie\" title=\"Sophus Lie\"><PERSON><PERSON> Lie</a>, Norwegian mathematician and academic (d. 1899)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/So<PERSON>_Lie"}]}, {"year": "1847", "text": "<PERSON><PERSON>, French author and critic (d. 1916)", "html": "1847 - <a href=\"https://wikipedia.org/wiki/%C3%89mile_Faguet\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, French author and critic (d. 1916)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/%C3%89mile_Faguet\" title=\"É<PERSON>\"><PERSON><PERSON></a>, French author and critic (d. 1916)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/%C3%89mile_Faguet"}]}, {"year": "1853", "text": "<PERSON>, French physician and immunologist, co-founded the Pasteur Institute (d. 1933)", "html": "1853 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_%C3%89mile_Roux\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, French physician and immunologist, co-founded the <a href=\"https://wikipedia.org/wiki/Pasteur_Institute\" title=\"Pasteur Institute\">Pasteur Institute</a> (d. 1933)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_%C3%89mile_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, French physician and immunologist, co-founded the <a href=\"https://wikipedia.org/wiki/Pasteur_Institute\" title=\"Pasteur Institute\">Pasteur Institute</a> (d. 1933)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_%C3%89mile_Roux"}, {"title": "Pasteur Institute", "link": "https://wikipedia.org/wiki/Pasteur_Institute"}]}, {"year": "1858", "text": "<PERSON>, Norwegian mezzo-soprano singer and pioneer on women's skiing (d. 1907)", "html": "1858 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Norwegian mezzo-soprano singer and pioneer on women<span class=\"nowrap\" style=\"padding-left:0.1em;\">'s</span> skiing (d. 1907)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Norwegian mezzo-soprano singer and pioneer on women<span class=\"nowrap\" style=\"padding-left:0.1em;\">'s</span> skiing (d. 1907)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1859", "text": "<PERSON>, French painter and illustrator (d. 1927)", "html": "1859 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A9<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French painter and illustrator (d. 1927)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A9<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French painter and illustrator (d. 1927)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Paul_C%C3%A9<PERSON>_<PERSON>eu"}]}, {"year": "1866", "text": "<PERSON><PERSON><PERSON>, Lithuanian physician and politician, third President of Lithuania (d. 1950)", "html": "1866 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Lithuanian physician and politician, third <a href=\"https://wikipedia.org/wiki/President_of_Lithuania\" title=\"President of Lithuania\">President of Lithuania</a> (d. 1950)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Lithuanian physician and politician, third <a href=\"https://wikipedia.org/wiki/President_of_Lithuania\" title=\"President of Lithuania\">President of Lithuania</a> (d. 1950)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}, {"title": "President of Lithuania", "link": "https://wikipedia.org/wiki/President_of_Lithuania"}]}, {"year": "1873", "text": "<PERSON>,  English novelist, poet, and critic  (d. 1939)", "html": "1873 - <a href=\"https://wikipedia.org/wiki/Ford_Madox_Ford\" title=\"Ford Madox Ford\">Ford Madox Ford</a>, English novelist, poet, and critic (d. 1939)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Ford_Madox_Ford\" title=\"Ford Madox Ford\">Ford Madox Ford</a>, English novelist, poet, and critic (d. 1939)", "links": [{"title": "Ford Madox Ford", "link": "https://wikipedia.org/wiki/Ford_Madox_Ford"}]}, {"year": "1874", "text": "<PERSON>, Canadian economist and politician, tenth Prime Minister of Canada (d. 1950)", "html": "1874 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian economist and politician, tenth <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Canada\" title=\"Prime Minister of Canada\">Prime Minister of Canada</a> (d. 1950)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON> King\"><PERSON></a>, Canadian economist and politician, tenth <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Canada\" title=\"Prime Minister of Canada\">Prime Minister of Canada</a> (d. 1950)", "links": [{"title": "<PERSON> King", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>"}, {"title": "Prime Minister of Canada", "link": "https://wikipedia.org/wiki/Prime_Minister_of_Canada"}]}, {"year": "1881", "text": "<PERSON>, South African-English cricketer and coach (d. 1930)", "html": "1881 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, South African-English cricketer and coach (d. 1930)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, South African-English cricketer and coach (d. 1930)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1884", "text": "<PERSON>, English children's book writer (d. 1976)", "html": "1884 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English children's book writer (d. 1976)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English children's book writer (d. 1976)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1887", "text": "<PERSON>, Czech painter and illustrator (d. 1957)", "html": "1887 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Czech painter and illustrator (d. 1957)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Czech painter and illustrator (d. 1957)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1890", "text": "<PERSON> Prussia (d. 1920)", "html": "1890 - <a href=\"https://wikipedia.org/wiki/Prince_<PERSON>_of_Prussia\" title=\"Prince <PERSON> of Prussia\">Prince <PERSON> of Prussia</a> (d. 1920)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Prince_<PERSON>_of_Prussia\" title=\"Prince <PERSON> of Prussia\">Prince <PERSON> of Prussia</a> (d. 1920)", "links": [{"title": "Prince <PERSON> of Prussia", "link": "https://wikipedia.org/wiki/Prince_<PERSON>_<PERSON>_Prussia"}]}, {"year": "1892", "text": "<PERSON>, American basketball player and coach (d. 1950)", "html": "1892 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player and coach (d. 1950)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player and coach (d. 1950)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1893", "text": "<PERSON>, English captain and pilot (d. 1971)", "html": "1893 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"Charles <PERSON>\"><PERSON></a>, English captain and pilot (d. 1971)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"Charles <PERSON>\"><PERSON></a>, English captain and pilot (d. 1971)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Charles_<PERSON>_<PERSON>"}]}, {"year": "1893", "text": "<PERSON>, German director and producer (d. 1966)", "html": "1893 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German director and producer (d. 1966)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON> Pi<PERSON>tor\"><PERSON></a>, German director and producer (d. 1966)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Erwin_Piscator"}]}, {"year": "1894", "text": "<PERSON>, American conductor (d. 1979)", "html": "1894 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American conductor (d. 1979)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American conductor (d. 1979)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1894", "text": "<PERSON>, Irish-American runner and soldier (d. 1969)", "html": "1894 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(athlete)\" title=\"<PERSON> (athlete)\"><PERSON></a>, Irish-American runner and soldier (d. 1969)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>(athlete)\" title=\"<PERSON> (athlete)\"><PERSON></a>, Irish-American runner and soldier (d. 1969)", "links": [{"title": "<PERSON> (athlete)", "link": "https://wikipedia.org/wiki/<PERSON>(athlete)"}]}, {"year": "1894", "text": "<PERSON><PERSON>, Dutch cartographer, engineer, and politician, Prime Minister of the Netherlands (d. 1977)", "html": "1894 - <a href=\"https://wikipedia.org/wiki/Wim_<PERSON>\" class=\"mw-redirect\" title=\"Wim <PERSON>\"><PERSON><PERSON></a>, Dutch cartographer, engineer, and politician, <a href=\"https://wikipedia.org/wiki/List_of_Prime_Ministers_of_the_Netherlands\" class=\"mw-redirect\" title=\"List of Prime Ministers of the Netherlands\">Prime Minister of the Netherlands</a> (d. 1977)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Wim_<PERSON>\" class=\"mw-redirect\" title=\"Wim <PERSON>\"><PERSON><PERSON></a>, Dutch cartographer, engineer, and politician, <a href=\"https://wikipedia.org/wiki/List_of_Prime_Ministers_of_the_Netherlands\" class=\"mw-redirect\" title=\"List of Prime Ministers of the Netherlands\">Prime Minister of the Netherlands</a> (d. 1977)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Wim_<PERSON><PERSON>"}, {"title": "List of Prime Ministers of the Netherlands", "link": "https://wikipedia.org/wiki/List_of_Prime_Ministers_of_the_Netherlands"}]}, {"year": "1895", "text": "<PERSON>, Australian tennis player (d. 1967)", "html": "1895 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian tennis player (d. 1967)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian tennis player (d. 1967)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1898", "text": "<PERSON><PERSON>, American sprinter (d. 1979)", "html": "1898 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American sprinter (d. 1979)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American sprinter (d. 1979)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1900", "text": "<PERSON>, English mathematician and academic, one of the first people to analyze a dynamical system with chaos (d. 1998)", "html": "1900 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English mathematician and academic, one of the first people to analyze a dynamical system with chaos (d. 1998)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English mathematician and academic, one of the first people to analyze a dynamical system with chaos (d. 1998)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1903", "text": "<PERSON><PERSON><PERSON>, American novelist and short story writer (d. 1987)", "html": "1903 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American novelist and short story writer (d. 1987)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American novelist and short story writer (d. 1987)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1903", "text": "<PERSON>, English bandleader, composer, and actor (d. 1978)", "html": "1903 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English bandleader, composer, and actor (d. 1978)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English bandleader, composer, and actor (d. 1978)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1904", "text": "<PERSON>, American painter and illustrator (d. 1999)", "html": "1904 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American painter and illustrator (d. 1999)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American painter and illustrator (d. 1999)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1905", "text": "<PERSON><PERSON>, Finnish soldier and sniper (d. 2002)", "html": "1905 - <a href=\"https://wikipedia.org/wiki/Simo_H%C3%A4yh%C3%A4\" title=\"Simo <PERSON>\"><PERSON><PERSON></a>, Finnish soldier and sniper (d. 2002)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Simo_H%C3%A4yh%C3%A4\" title=\"Simo <PERSON>\"><PERSON><PERSON></a>, Finnish soldier and sniper (d. 2002)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Simo_H%C3%A4yh%C3%A4"}]}, {"year": "1905", "text": "<PERSON>, 11th Chief Justice of India, and politician, sixth Vice President of India (d. 1992)", "html": "1905 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, 11th <a href=\"https://wikipedia.org/wiki/Chief_Justice_of_India\" title=\"Chief Justice of India\">Chief Justice of India</a>, and politician, sixth <a href=\"https://wikipedia.org/wiki/List_of_Vice_Presidents_of_India\" class=\"mw-redirect\" title=\"List of Vice Presidents of India\">Vice President of India</a> (d. 1992)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, 11th <a href=\"https://wikipedia.org/wiki/Chief_Justice_of_India\" title=\"Chief Justice of India\">Chief Justice of India</a>, and politician, sixth <a href=\"https://wikipedia.org/wiki/List_of_Vice_Presidents_of_India\" class=\"mw-redirect\" title=\"List of Vice Presidents of India\">Vice President of India</a> (d. 1992)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}, {"title": "Chief Justice of India", "link": "https://wikipedia.org/wiki/Chief_Justice_of_India"}, {"title": "List of Vice Presidents of India", "link": "https://wikipedia.org/wiki/List_of_Vice_Presidents_of_India"}]}, {"year": "1905", "text": "<PERSON><PERSON>, Brazilian author and translator (d. 1975)", "html": "1905 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Brazilian author and translator (d. 1975)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Brazilian author and translator (d. 1975)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1906", "text": "<PERSON><PERSON><PERSON><PERSON>, Portuguese composer and conductor (d. 1994)", "html": "1906 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>-<PERSON>ra%C3%A7a\" title=\"<PERSON>\"><PERSON></a>, Portuguese composer and conductor (d. 1994)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>-<PERSON>%C3%A7a\" title=\"<PERSON>\"><PERSON></a>, Portuguese composer and conductor (d. 1994)", "links": [{"title": "<PERSON>Graça", "link": "https://wikipedia.org/wiki/Fernando_Lo<PERSON>-Gra%C3%A7a"}]}, {"year": "1906", "text": "<PERSON>, American pilot and engineer (d. 1998)", "html": "1906 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"Russell <PERSON>\"><PERSON></a>, American pilot and engineer (d. 1998)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"Russell <PERSON>\"><PERSON></a>, American pilot and engineer (d. 1998)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Russell_<PERSON>_Newhouse"}]}, {"year": "1908", "text": "<PERSON>, American chemist and academic, Nobel Prize laureate (d. 1980)", "html": "1908 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American chemist and academic, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Chemistry\" title=\"Nobel Prize in Chemistry\">Nobel Prize</a> laureate (d. 1980)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American chemist and academic, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Chemistry\" title=\"Nobel Prize in Chemistry\">Nobel Prize</a> laureate (d. 1980)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Nobel Prize in Chemistry", "link": "https://wikipedia.org/wiki/Nobel_Prize_in_Chemistry"}]}, {"year": "1910", "text": "<PERSON><PERSON><PERSON>, Indian-American educator and author (d. 1999)", "html": "1910 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Indian-American educator and author (d. 1999)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Indian-American educator and author (d. 1999)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1910", "text": "<PERSON><PERSON>, American singer-songwriter and trumpet player (d. 1988)", "html": "1910 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> <PERSON>\"><PERSON><PERSON></a>, American singer-songwriter and trumpet player (d. 1988)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> <PERSON>\"><PERSON><PERSON></a>, American singer-songwriter and trumpet player (d. 1988)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1912", "text": "<PERSON>, Baron <PERSON>, English captain and politician, Lord President of the Council (d. 2012)", "html": "1912 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Baron_<PERSON>\" title=\"<PERSON>, Baron <PERSON>\"><PERSON>, Baron <PERSON></a>, English captain and politician, <a href=\"https://wikipedia.org/wiki/Lord_President_of_the_Council\" title=\"Lord President of the Council\">Lord President of the Council</a> (d. 2012)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>,_Baron_<PERSON>\" title=\"<PERSON>, Baron <PERSON>\"><PERSON>, Baron <PERSON></a>, English captain and politician, <a href=\"https://wikipedia.org/wiki/Lord_President_of_the_Council\" title=\"Lord President of the Council\">Lord President of the Council</a> (d. 2012)", "links": [{"title": "<PERSON>, Baron <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_<PERSON>_<PERSON>"}, {"title": "Lord President of the Council", "link": "https://wikipedia.org/wiki/Lord_President_of_the_Council"}]}, {"year": "1913", "text": "<PERSON>, American businessman, co-founded <PERSON><PERSON><PERSON>Robbins (d. 1967)", "html": "1913 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American businessman, co-founded <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>-<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a> (d. 1967)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Ba<PERSON>\"><PERSON></a>, American businessman, co-founded <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>-<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a> (d. 1967)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>-<PERSON>"}]}, {"year": "1914", "text": "<PERSON><PERSON><PERSON><PERSON>, Indian cricketer (d. 2005)", "html": "1914 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Indian cricketer (d. 2005)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Indian cricketer (d. 2005)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1914", "text": "<PERSON>, Cuban ballet dancer, co-founded the Cuban National Ballet (d. 2013)", "html": "1914 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(dancer)\" title=\"<PERSON> (dancer)\"><PERSON></a>, Cuban ballet dancer, co-founded the <a href=\"https://wikipedia.org/wiki/Cuban_National_Ballet\" title=\"Cuban National Ballet\">Cuban National Ballet</a> (d. 2013)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>(dancer)\" title=\"<PERSON> (dancer)\"><PERSON></a>, Cuban ballet dancer, co-founded the <a href=\"https://wikipedia.org/wiki/Cuban_National_Ballet\" title=\"Cuban National Ballet\">Cuban National Ballet</a> (d. 2013)", "links": [{"title": "<PERSON> (dancer)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(dancer)"}, {"title": "Cuban National Ballet", "link": "https://wikipedia.org/wiki/Cuban_National_Ballet"}]}, {"year": "1916", "text": "<PERSON>, English author and poet (d. 2000)", "html": "1916 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English author and poet (d. 2000)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English author and poet (d. 2000)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1917", "text": "<PERSON>, Nigerian historian, author, and academic (d. 1983)", "html": "1917 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Nigerian historian, author, and academic (d. 1983)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Nigerian historian, author, and academic (d. 1983)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1920", "text": "<PERSON>, Canadian computer scientist, developed the APL programming language (d. 2004)", "html": "1920 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian computer scientist, developed the <a href=\"https://wikipedia.org/wiki/APL_(programming_language)\" title=\"APL (programming language)\">APL programming language</a> (d. 2004)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian computer scientist, developed the <a href=\"https://wikipedia.org/wiki/APL_(programming_language)\" title=\"APL (programming language)\">APL programming language</a> (d. 2004)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "APL (programming language)", "link": "https://wikipedia.org/wiki/APL_(programming_language)"}]}, {"year": "1921", "text": "<PERSON><PERSON>, German-Swiss author and translator (d. 1943)", "html": "1921 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, German-Swiss author and translator (d. 1943)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, German-Swiss author and translator (d. 1943)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1922", "text": "<PERSON>, American engineer and academic (d. 2005)", "html": "1922 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American engineer and academic (d. 2005)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American engineer and academic (d. 2005)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1923", "text": "<PERSON><PERSON><PERSON>, American historian and scholar (d. 2006)", "html": "1923 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American historian and scholar (d. 2006)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American historian and scholar (d. 2006)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1926", "text": "<PERSON>, American baseball player (d. 1985)", "html": "1926 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player (d. 1985)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player (d. 1985)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1926", "text": "<PERSON>, American lawyer and politician (d. 2014)", "html": "1926 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and politician (d. 2014)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and politician (d. 2014)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1926", "text": "<PERSON>, English actor, director, screenwriter, and playwright (d. 2015)", "html": "1926 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(actor)\" title=\"<PERSON> (actor)\"><PERSON></a>, English actor, director, screenwriter, and playwright (d. 2015)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(actor)\" title=\"<PERSON> (actor)\"><PERSON></a>, English actor, director, screenwriter, and playwright (d. 2015)", "links": [{"title": "<PERSON> (actor)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(actor)"}]}, {"year": "1927", "text": "<PERSON>, American actor and director (d. 1974)", "html": "1927 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(actor)\" title=\"<PERSON> (actor)\"><PERSON></a>, American actor and director (d. 1974)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(actor)\" title=\"<PERSON> (actor)\"><PERSON></a>, American actor and director (d. 1974)", "links": [{"title": "<PERSON> (actor)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>(actor)"}]}, {"year": "1927", "text": "<PERSON>, American painter and sculptor (d. 2012)", "html": "1927 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American painter and sculptor (d. 2012)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American painter and sculptor (d. 2012)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1928", "text": "<PERSON>, American journalist (d. 2014)", "html": "1928 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American journalist (d. 2014)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American journalist (d. 2014)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1928", "text": "<PERSON>, American captain and pilot (d. 2013)", "html": "1928 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American captain and pilot (d. 2013)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Eli Beeding\"><PERSON></a>, American captain and pilot (d. 2013)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_Beeding"}]}, {"year": "1928", "text": "<PERSON>, American farmer and politician, seventh Florida Commissioner of Agriculture (d. 2012)", "html": "1928 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American farmer and politician, seventh <a href=\"https://wikipedia.org/wiki/Florida_Commissioner_of_Agriculture\" title=\"Florida Commissioner of Agriculture\">Florida Commissioner of Agriculture</a> (d. 2012)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American farmer and politician, seventh <a href=\"https://wikipedia.org/wiki/Florida_Commissioner_of_Agriculture\" title=\"Florida Commissioner of Agriculture\">Florida Commissioner of Agriculture</a> (d. 2012)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Florida Commissioner of Agriculture", "link": "https://wikipedia.org/wiki/Florida_Commissioner_of_Agriculture"}]}, {"year": "1929", "text": "<PERSON>, American journalist and author (d. 2009)", "html": "1929 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American journalist and author (d. 2009)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American journalist and author (d. 2009)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1930", "text": "<PERSON>, American photographer and publisher, founded Penthouse (d. 2010)", "html": "1930 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American photographer and publisher, founded <i><a href=\"https://wikipedia.org/wiki/Penthouse_(magazine)\" title=\"Penthouse (magazine)\">Penthouse</a></i> (d. 2010)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American photographer and publisher, founded <i><a href=\"https://wikipedia.org/wiki/Penthouse_(magazine)\" title=\"Penthouse (magazine)\">Penthouse</a></i> (d. 2010)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Penthouse (magazine)", "link": "https://wikipedia.org/wiki/Penthouse_(magazine)"}]}, {"year": "1930", "text": "<PERSON><PERSON>-<PERSON>, German actor and painter", "html": "1930 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>-<PERSON>\" title=\"<PERSON><PERSON>-<PERSON>\"><PERSON><PERSON>-<PERSON></a>, German actor and painter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>-<PERSON>\" title=\"<PERSON><PERSON>-<PERSON>\"><PERSON><PERSON></a>, German actor and painter", "links": [{"title": "<PERSON><PERSON>-<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>-<PERSON>"}]}, {"year": "1930", "text": "<PERSON>, Australian psychologist and author (d. 2019)", "html": "1930 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian psychologist and author (d. 2019)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian psychologist and author (d. 2019)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1931", "text": "<PERSON>, American director and cinematographer (d. 2011)", "html": "1931 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American director and cinematographer (d. 2011)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American director and cinematographer (d. 2011)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1931", "text": "<PERSON>, Canadian-American actor (d. 2014)", "html": "1931 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian-American actor (d. 2014)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian-American actor (d. 2014)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1931", "text": "<PERSON>, American neurobiologist and psychologist", "html": "1931 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American neurobiologist and psychologist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American neurobiologist and psychologist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1932", "text": "<PERSON>, English footballer and manager (d. 2012)", "html": "1932 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>(footballer)\" title=\"<PERSON> (footballer)\"><PERSON></a>, English footballer and manager (d. 2012)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>(footballer)\" title=\"<PERSON> (footballer)\"><PERSON></a>, English footballer and manager (d. 2012)", "links": [{"title": "<PERSON> (footballer)", "link": "https://wikipedia.org/wiki/<PERSON>(footballer)"}]}, {"year": "1934", "text": "<PERSON>, American painter and academic (d. 2018)", "html": "1934 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American painter and academic (d. 2018)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American painter and academic (d. 2018)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1934", "text": "<PERSON>, English footballer and manager (d. 2018)", "html": "1934 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(English_footballer)\" title=\"<PERSON> (English footballer)\"><PERSON></a>, English footballer and manager (d. 2018)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(English_footballer)\" title=\"<PERSON> (English footballer)\"><PERSON></a>, English footballer and manager (d. 2018)", "links": [{"title": "<PERSON> (English footballer)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(English_footballer)"}]}, {"year": "1935", "text": "<PERSON>, English cricketer (d. 2013)", "html": "1935 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English cricketer (d. 2013)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English cricketer (d. 2013)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1935", "text": "<PERSON>, American baseball player, coach, and manager (d. 1999)", "html": "1935 - <a href=\"https://wikipedia.org/wiki/Cal_Ripken_Sr.\" title=\"<PERSON> Ripken Sr.\"><PERSON> Sr.</a>, American baseball player, coach, and manager (d. 1999)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Cal_Ripken_Sr.\" title=\"Cal Ripken Sr.\"><PERSON> Sr.</a>, American baseball player, coach, and manager (d. 1999)", "links": [{"title": "Cal R<PERSON>ken Sr.", "link": "https://wikipedia.org/wiki/Cal_R<PERSON><PERSON>_Sr."}]}, {"year": "1936", "text": "<PERSON>", "html": "1936 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\">Pope <PERSON></a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1936", "text": "<PERSON>, English singer, guitarist, and actor", "html": "1936 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English singer, guitarist, and actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English singer, guitarist, and actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1937", "text": "<PERSON>, Australian-English radio host", "html": "1937 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(broadcaster)\" title=\"<PERSON> (broadcaster)\"><PERSON></a>, Australian-English radio host", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>(broadcaster)\" title=\"<PERSON> (broadcaster)\"><PERSON></a>, Australian-English radio host", "links": [{"title": "<PERSON> (broadcaster)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(broadcaster)"}]}, {"year": "1937", "text": "<PERSON>, American singer and keyboard player (d. 2019)", "html": "1937 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer and keyboard player (d. 2019)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer and keyboard player (d. 2019)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Art_Neville"}]}, {"year": "1937", "text": "<PERSON>, Australian businessman, founded World Series Cricket (d. 2005)", "html": "1937 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian businessman, founded <a href=\"https://wikipedia.org/wiki/World_Series_Cricket\" title=\"World Series Cricket\">World Series Cricket</a> (d. 2005)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian businessman, founded <a href=\"https://wikipedia.org/wiki/World_Series_Cricket\" title=\"World Series Cricket\">World Series Cricket</a> (d. 2005)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_Packer"}, {"title": "World Series Cricket", "link": "https://wikipedia.org/wiki/World_Series_Cricket"}]}, {"year": "1937", "text": "<PERSON>,  American novelist (d. 1969)", "html": "1937 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American novelist (d. 1969)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American novelist (d. 1969)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1937", "text": "<PERSON>, American general (d. 1996)", "html": "1937 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American general (d. 1996)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American general (d. 1996)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1938", "text": "<PERSON>, New Zealand runner (d. 2019)", "html": "1938 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, New Zealand runner (d. 2019)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, New Zealand runner (d. 2019)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1939", "text": "<PERSON>, American pianist (d. 1983)", "html": "1939 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American pianist (d. 1983)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American pianist (d. 1983)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1939", "text": "<PERSON>, American R&B singer-songwriter (d. 1992)", "html": "1939 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American R&amp;B singer-songwriter (d. 1992)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American R&amp;B singer-songwriter (d. 1992)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1940", "text": "<PERSON><PERSON><PERSON>, Norwegian journalist (d. 2013)", "html": "1940 - <a href=\"https://wikipedia.org/wiki/K%C3%A5re_Valebrokk\" title=\"<PERSON><PERSON><PERSON> Valebrokk\"><PERSON><PERSON><PERSON></a>, Norwegian journalist (d. 2013)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/K%C3%A5re_Valebrokk\" title=\"<PERSON><PERSON><PERSON> Valebrokk\"><PERSON><PERSON><PERSON></a>, Norwegian journalist (d. 2013)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/K%C3%A5re_Valebrokk"}]}, {"year": "1940", "text": "<PERSON>, Mexican actress, singer, director, and screenwriter (d. 2015)", "html": "1940 - <a href=\"https://wikipedia.org/wiki/Mar%C3%<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Mexican actress, singer, director, and screenwriter (d. 2015)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Mar%C3%<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Mexican actress, singer, director, and screenwriter (d. 2015)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Mar%C3%<PERSON><PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1941", "text": "<PERSON>, English singer-songwriter and guitarist (d. 2009)", "html": "1941 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English singer-songwriter and guitarist (d. 2009)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English singer-songwriter and guitarist (d. 2009)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1941", "text": "<PERSON>, Zimbabwean historian and politician, Zimbabwean Minister of Foreign Affairs (d. 2012)", "html": "1941 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Zimbabwean historian and politician, <a href=\"https://wikipedia.org/wiki/Ministry_of_Foreign_Affairs_(Zimbabwe)\" class=\"mw-redirect\" title=\"Ministry of Foreign Affairs (Zimbabwe)\">Zimbabwean Minister of Foreign Affairs</a> (d. 2012)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Zimbabwean historian and politician, <a href=\"https://wikipedia.org/wiki/Ministry_of_Foreign_Affairs_(Zimbabwe)\" class=\"mw-redirect\" title=\"Ministry of Foreign Affairs (Zimbabwe)\">Zimbabwean Minister of Foreign Affairs</a> (d. 2012)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>denge"}, {"title": "Ministry of Foreign Affairs (Zimbabwe)", "link": "https://wikipedia.org/wiki/Ministry_of_Foreign_Affairs_(Zimbabwe)"}]}, {"year": "1942", "text": "<PERSON><PERSON>, Nigerian general and politician, 7th Head of State of the Federal Republic of Nigeria", "html": "1942 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Nigerian general and politician, 7th <a href=\"https://wikipedia.org/wiki/List_of_heads_of_state_of_Nigeria\" title=\"List of heads of state of Nigeria\">Head of State of the Federal Republic of Nigeria</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Nigerian general and politician, 7th <a href=\"https://wikipedia.org/wiki/List_of_heads_of_state_of_Nigeria\" title=\"List of heads of state of Nigeria\">Head of State of the Federal Republic of Nigeria</a>", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>"}, {"title": "List of heads of state of Nigeria", "link": "https://wikipedia.org/wiki/List_of_heads_of_state_of_Nigeria"}]}, {"year": "1942", "text": "<PERSON>, American singer and harmonica player (d. 1987)", "html": "1942 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer and harmonica player (d. 1987)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer and harmonica player (d. 1987)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1943", "text": "<PERSON>, Scottish pianist and composer", "html": "1943 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish pianist and composer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish pianist and composer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1944", "text": "<PERSON>, American author and educator (d. 2005)", "html": "1944 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author and educator (d. 2005)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author and educator (d. 2005)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1944", "text": "<PERSON>, Italian-American oncologist and academic", "html": "1944 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian-American oncologist and academic", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian-American oncologist and academic", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1944", "text": "<PERSON>, English actor (d. 2024)", "html": "1944 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actor (d. 2024)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actor (d. 2024)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Bernard_<PERSON>"}]}, {"year": "1945", "text": "<PERSON>, American actor", "html": "1945 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1945", "text": "<PERSON>, English director", "html": "1945 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(director)\" title=\"<PERSON> (director)\"><PERSON></a>, English director", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(director)\" title=\"<PERSON> (director)\"><PERSON></a>, English director", "links": [{"title": "<PERSON> (director)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(director)"}]}, {"year": "1945", "text": "<PERSON>, American journalist and author", "html": "1945 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American journalist and author", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American journalist and author", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1945", "text": "<PERSON><PERSON><PERSON>, Estonian poet and critic", "html": "1945 - <a href=\"https://wikipedia.org/wiki/J%C3%<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Estonian poet and critic", "no_year_html": "<a href=\"https://wikipedia.org/wiki/J%C3%<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Estonian poet and critic", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/J%C3%<PERSON><PERSON>_<PERSON>"}]}, {"year": "1945", "text": "<PERSON>, English author and academic", "html": "1945 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English author and academic", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English author and academic", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1946", "text": "<PERSON>, English radio host", "html": "1946 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English radio host", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English radio host", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1946", "text": "<PERSON>, Canadian actor, director, and screenwriter", "html": "1946 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian actor, director, and screenwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian actor, director, and screenwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1947", "text": "<PERSON>, American actor and producer", "html": "1947 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor and producer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1948", "text": "<PERSON><PERSON>, Russian ice hockey player and coach (d. 2015)", "html": "1948 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Russian ice hockey player and coach (d. 2015)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Russian ice hockey player and coach (d. 2015)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1948", "text": "<PERSON>, American rock drummer", "html": "1948 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American rock drummer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American rock drummer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1948", "text": "<PERSON><PERSON>, Turkish economist and politician", "html": "1948 - <a href=\"https://wikipedia.org/wiki/<PERSON>mal_K%C4%B1l%C4%B1%C3%A7daro%C4%9Flu\" title=\"<PERSON><PERSON>lıçdaroğlu\"><PERSON><PERSON></a>, Turkish economist and politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>mal_K%C4%B1l%C4%B1%C3%A7daro%C4%9Flu\" title=\"<PERSON><PERSON>lıçdaroğlu\"><PERSON><PERSON></a>, Turkish economist and politician", "links": [{"title": "<PERSON>mal <PERSON>ıçdaroğlu", "link": "https://wikipedia.org/wiki/Kemal_K%C4%B1l%C4%B1%C3%A7daro%C4%9Flu"}]}, {"year": "1949", "text": "<PERSON>, American actor", "html": "1949 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1949", "text": "<PERSON><PERSON><PERSON>, Cypriot footballer", "html": "1949 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Cypriot footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Cypriot footballer", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1949", "text": "<PERSON>, English singer-songwriter and producer", "html": "1949 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English singer-songwriter and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English singer-songwriter and producer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1950", "text": "<PERSON>, American educator and author", "html": "1950 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American educator and author", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American educator and author", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "1950", "text": "<PERSON>, American sprinter and coach", "html": "1950 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American sprinter and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American sprinter and coach", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1951", "text": "<PERSON>, American football player and coach", "html": "1951 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player and coach", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Pat_<PERSON>"}]}, {"year": "1951", "text": "<PERSON>, Canadian ice hockey player and coach", "html": "1951 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player and coach", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1951", "text": "<PERSON><PERSON><PERSON>, Russian runner", "html": "1951 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Russian runner", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Russian runner", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>a"}]}, {"year": "1953", "text": "<PERSON>, American actor", "html": "1953 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1954", "text": "<PERSON><PERSON><PERSON>, Lithuanian basketball player", "html": "1954 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_Jovai%C5%A1a\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Lithuanian basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>i%C5%A1a\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Lithuanian basketball player", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Sergejus_Jovai%C5%A1a"}]}, {"year": "1955", "text": "<PERSON>, American basketball player, coach, and sportscaster", "html": "1955 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(basketball)\" title=\"<PERSON> (basketball)\"><PERSON></a>, American basketball player, coach, and sportscaster", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(basketball)\" title=\"<PERSON> (basketball)\"><PERSON></a>, American basketball player, coach, and sportscaster", "links": [{"title": "<PERSON> (basketball)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(basketball)"}]}, {"year": "1956", "text": "<PERSON>, American director, producer, and screenwriter", "html": "1956 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American director, producer, and screenwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American director, producer, and screenwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1956", "text": "<PERSON>, English journalist and author", "html": "1956 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English journalist and author", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English journalist and author", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1956", "text": "<PERSON><PERSON><PERSON>, Bulgarian runner", "html": "1956 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_Petrov<PERSON>\" title=\"To<PERSON><PERSON> Petrova\"><PERSON><PERSON><PERSON></a>, Bulgarian runner", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_Petrov<PERSON>\" title=\"To<PERSON><PERSON> Petrova\"><PERSON><PERSON><PERSON></a>, Bulgarian runner", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Tot<PERSON>_Petrova"}]}, {"year": "1957", "text": "<PERSON>, English sprinter", "html": "1957 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English sprinter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English sprinter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1957", "text": "<PERSON>, American baseball player and coach", "html": "1957 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player and coach", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1958", "text": "<PERSON>, American bass player, songwriter, and producer", "html": "1958 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American bass player, songwriter, and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American bass player, songwriter, and producer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1958", "text": "<PERSON>, American politician (d. 2024)", "html": "1958 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>.\" title=\"<PERSON> Jr.\"><PERSON>.</a>, American politician (d. 2024)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>.\" title=\"<PERSON> Jr.\"><PERSON>.</a>, American politician (d. 2024)", "links": [{"title": "<PERSON>.", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>."}]}, {"year": "1959", "text": "<PERSON>, American songwriter and guitarist (d. 1995)", "html": "1959 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American songwriter and guitarist (d. 1995)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American songwriter and guitarist (d. 1995)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1961", "text": "<PERSON><PERSON>, Bahraini journalist and author", "html": "1961 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>Jamri\"><PERSON><PERSON></a>, Bahraini journalist and author", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> al<PERSON>Jamri\"><PERSON><PERSON></a>, Bahraini journalist and author", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1961", "text": "<PERSON>, English singer", "html": "1961 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English singer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English singer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1962", "text": "<PERSON>, English footballer", "html": "1962 - <a href=\"https://wikipedia.org/wiki/<PERSON>_(footballer)\" title=\"<PERSON> (footballer)\"><PERSON></a>, English footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>(footballer)\" title=\"<PERSON> (footballer)\"><PERSON></a>, English footballer", "links": [{"title": "<PERSON> (footballer)", "link": "https://wikipedia.org/wiki/<PERSON>_(footballer)"}]}, {"year": "1962", "text": "<PERSON><PERSON><PERSON>, Russian sprinter", "html": "1962 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Russian sprinter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Russian sprinter", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>a"}]}, {"year": "1962", "text": "<PERSON><PERSON>, American golfer and journalist", "html": "1962 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American golfer and journalist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American golfer and journalist", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>occo_<PERSON>te"}]}, {"year": "1964", "text": "<PERSON>, Czech ice hockey player and coach", "html": "1964 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Czech ice hockey player and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Czech ice hockey player and coach", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1964", "text": "<PERSON>, American basketball player and coach (d. 2024)", "html": "1964 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player and coach (d. 2024)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Wolf\"><PERSON></a>, American basketball player and coach (d. 2024)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1965", "text": "<PERSON>, Canadian ice hockey player and coach", "html": "1965 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player and coach", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1965", "text": "<PERSON>, American basketball player and coach", "html": "1965 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player and coach", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1966", "text": "<PERSON>, American singer-songwriter and guitarist", "html": "1966 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and guitarist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and guitarist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1966", "text": "<PERSON><PERSON><PERSON>, Estonian politician, 23rd Estonian Minister of Foreign Affairs", "html": "1966 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Estonian politician, 23rd <a href=\"https://wikipedia.org/wiki/Minister_of_Foreign_Affairs_(Estonia)\" title=\"Minister of Foreign Affairs (Estonia)\">Estonian Minister of Foreign Affairs</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Estonian politician, 23rd <a href=\"https://wikipedia.org/wiki/Minister_of_Foreign_Affairs_(Estonia)\" title=\"Minister of Foreign Affairs (Estonia)\">Estonian Minister of Foreign Affairs</a>", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}, {"title": "Minister of Foreign Affairs (Estonia)", "link": "https://wikipedia.org/wiki/Minister_of_Foreign_Affairs_(Estonia)"}]}, {"year": "1967", "text": "<PERSON>, Canadian ice hockey player and sportscaster", "html": "1967 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player and sportscaster", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player and sportscaster", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1967", "text": "<PERSON><PERSON>, Italian muscisian, singer and DJ.", "html": "1967 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>%27Agos<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Italian muscisian, singer and DJ.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>%27Agostino\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Italian muscisian, singer and DJ.", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Gigi_D%27Agostino"}]}, {"year": "1967", "text": "<PERSON><PERSON>, German footballer and manager", "html": "1967 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, German footballer and manager", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, German footballer and manager", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1968", "text": "<PERSON>, Mexican footballer", "html": "1968 - <a href=\"https://wikipedia.org/wiki/Claudio_Su%C3%A1rez\" title=\"<PERSON>\"><PERSON></a>, Mexican footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Claudio_Su%C3%A1rez\" title=\"<PERSON>\"><PERSON></a>, Mexican footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Claudio_Su%C3%A1rez"}]}, {"year": "1968", "text": "<PERSON>, Canadian race car driver and sportscaster", "html": "1968 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian race car driver and sportscaster", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian race car driver and sportscaster", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1969", "text": "<PERSON>, American actress and model", "html": "1969 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress and model", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress and model", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1969", "text": "<PERSON><PERSON>, Russian triple jumper", "html": "1969 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Russian triple jumper", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Russian triple jumper", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1969", "text": "<PERSON>, American mixed martial artist and kick-boxer", "html": "1969 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American mixed martial artist and kick-boxer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American mixed martial artist and kick-boxer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1969", "text": "<PERSON>, English singer-songwriter, guitarist and producer", "html": "1969 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English singer-songwriter, guitarist and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English singer-songwriter, guitarist and producer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1970", "text": "<PERSON>, American actor", "html": "1970 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1971", "text": "<PERSON>, English actress", "html": "1971 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actress", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1971", "text": "<PERSON>, South African radio and TV presenter", "html": "1971 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, South African radio and TV presenter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, South African radio and TV presenter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1971", "text": "<PERSON>-<PERSON><PERSON>, American basketball player and coach (d. 2023)", "html": "1971 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>-<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player and coach (d. 2023)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>-<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player and coach (d. 2023)", "links": [{"title": "<PERSON>-<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>-<PERSON>"}]}, {"year": "1971", "text": "<PERSON>, French basketball player", "html": "1971 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French basketball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1972", "text": "<PERSON>, Indian actor and producer", "html": "1972 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Indian actor and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Indian actor and producer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1972", "text": "<PERSON><PERSON>, Cuban long jumper and coach", "html": "1972 - <a href=\"https://wikipedia.org/wiki/Iv%C3%A1n_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Cuban long jumper and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Iv%C3%A1n_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Cuban long jumper and coach", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Iv%C3%A1n_<PERSON>so"}]}, {"year": "1973", "text": "<PERSON>, American drummer", "html": "1973 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(drummer)\" title=\"<PERSON> (drummer)\"><PERSON></a>, American drummer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>(drummer)\" title=\"<PERSON> (drummer)\"><PERSON></a>, American drummer", "links": [{"title": "<PERSON> (drummer)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(drummer)"}]}, {"year": "1973", "text": "<PERSON><PERSON><PERSON><PERSON>, Greek javelin thrower", "html": "1973 - <a href=\"https://wikipedia.org/wiki/Konstadinos_Gatsioudis\" title=\"Konstadinos Gatsioudis\"><PERSON>nstadi<PERSON> Gatsioudis</a>, Greek javelin thrower", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Konstadinos_Gatsioudis\" title=\"Konstadinos Gatsioudis\">Konstadinos Gatsioudis</a>, Greek javelin thrower", "links": [{"title": "Konstadinos Gatsioudis", "link": "https://wikipedia.org/wiki/Konstadinos_Gatsioudis"}]}, {"year": "1973", "text": "<PERSON><PERSON>, American director, producer, and screenwriter", "html": "1973 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American director, producer, and screenwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American director, producer, and screenwriter", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1973", "text": "<PERSON>, English runner", "html": "1973 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English runner", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English runner", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1973", "text": "<PERSON>, German-Turkish footballer", "html": "1973 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German-Turkish footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German-Turkish footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Hasan_<PERSON>"}]}, {"year": "1974", "text": "<PERSON><PERSON>, South African cricketer", "html": "1974 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, South African cricketer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, South African cricketer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1974", "text": "<PERSON>, American actress", "html": "1974 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1974", "text": "<PERSON>, American actor", "html": "1974 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1974", "text": "<PERSON>, American actress", "html": "1974 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1975", "text": "<PERSON>, American wrestler and trainer", "html": "1975 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, American wrestler and trainer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, American wrestler and trainer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1975", "text": "<PERSON><PERSON><PERSON>, Sri Lankan sprinter", "html": "1975 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Sri Lankan sprinter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Sri Lankan sprinter", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1975", "text": "<PERSON><PERSON>, Ukrainian-American actress", "html": "1975 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Ukrainian-American actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Ukrainian-American actress", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1976", "text": "<PERSON><PERSON>, Canadian speed skater and coach", "html": "1976 - <a href=\"https://wikipedia.org/wiki/%C3%89ric_B%C3%A9dard\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Canadian speed skater and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/%C3%89ric_B%C3%A9dard\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Canadian speed skater and coach", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/%C3%89ric_B%C3%A9dard"}]}, {"year": "1976", "text": "<PERSON><PERSON>, Israeli footballer and manager", "html": "1976 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Israeli footballer and manager", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Israeli footballer and manager", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1976", "text": "<PERSON>, Swiss footballer", "html": "1976 - <a href=\"https://wikipedia.org/wiki/Patrick_M%C3%BC<PERSON>_(footballer)\" title=\"<PERSON> (footballer)\"><PERSON></a>, Swiss footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%<PERSON><PERSON>_(footballer)\" title=\"<PERSON> (footballer)\"><PERSON></a>, Swiss footballer", "links": [{"title": "<PERSON> (footballer)", "link": "https://wikipedia.org/wiki/<PERSON>_M%C3%BC<PERSON>_(footballer)"}]}, {"year": "1976", "text": "<PERSON>, English sailor (d. 2013)", "html": "1976 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(sailor)\" title=\"<PERSON> (sailor)\"><PERSON></a>, English sailor (d. 2013)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(sailor)\" title=\"<PERSON> (sailor)\"><PERSON></a>, English sailor (d. 2013)", "links": [{"title": "<PERSON> (sailor)", "link": "https://wikipedia.org/wiki/<PERSON>_(sailor)"}]}, {"year": "1976", "text": "<PERSON><PERSON>, American football player and sportscaster", "html": "1976 - <a href=\"https://wikipedia.org/wiki/Take<PERSON>_Spikes\" title=\"Takeo Spikes\"><PERSON><PERSON></a>, American football player and sportscaster", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Take<PERSON>_Spikes\" title=\"Takeo Spikes\"><PERSON><PERSON></a>, American football player and sportscaster", "links": [{"title": "Takeo Spikes", "link": "https://wikipedia.org/wiki/Takeo_Spikes"}]}, {"year": "1977", "text": "<PERSON><PERSON><PERSON>, French tennis player", "html": "1977 - <a href=\"https://wikipedia.org/wiki/Arnaud_Cl%C3%A9ment\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, French tennis player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/A<PERSON><PERSON>_<PERSON>l%C3%A9ment\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, French tennis player", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Arnaud_Cl%C3%A9ment"}]}, {"year": "1977", "text": "<PERSON>, Swedish ice hockey player", "html": "1977 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A<PERSON><PERSON><PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swedish ice hockey player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A<PERSON><PERSON><PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swedish ice hockey player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Samuel_P%C3%A5hlsson"}]}, {"year": "1977", "text": "<PERSON><PERSON><PERSON>, Canadian actress", "html": "1977 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Canadian actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Canadian actress", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1978", "text": "<PERSON>, Puerto Rican baseball player and sportscaster", "html": "1978 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%B3n\" title=\"<PERSON>\"><PERSON></a>, Puerto Rican baseball player and sportscaster", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%B3n\" title=\"<PERSON>\"><PERSON></a>, Puerto Rican baseball player and sportscaster", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Alex_Cintr%C3%B3n"}]}, {"year": "1978", "text": "<PERSON><PERSON>, Indian film actor, producer and architect", "html": "1978 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Indian film actor, producer and architect", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Indian film actor, producer and architect", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON><PERSON>h"}]}, {"year": "1978", "text": "<PERSON>, Filipino boxer and politician", "html": "1978 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Filipino boxer and politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Filipino boxer and politician", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1978", "text": "<PERSON>, Canadian drummer and songwriter", "html": "1978 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian drummer and songwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian drummer and songwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1978", "text": "<PERSON>, American baseball player", "html": "1978 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1979", "text": "<PERSON>, American ice hockey player", "html": "1979 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American ice hockey player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American ice hockey player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1979", "text": "<PERSON>, English footballer", "html": "1979 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(footballer,_born_1979)\" title=\"<PERSON> (footballer, born 1979)\"><PERSON></a>, English footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(footballer,_born_1979)\" title=\"<PERSON> (footballer, born 1979)\"><PERSON></a>, English footballer", "links": [{"title": "<PERSON> (footballer, born 1979)", "link": "https://wikipedia.org/wiki/<PERSON>_(footballer,_born_1979)"}]}, {"year": "1980", "text": "<PERSON><PERSON>, Australian basketball player", "html": "1980 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Australian basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Australian basketball player", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1980", "text": "<PERSON><PERSON><PERSON>, American race car driver", "html": "1980 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>-<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American race car driver", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>-<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American race car driver", "links": [{"title": "<PERSON>-<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1980", "text": "<PERSON>, Greek hammer thrower", "html": "1980 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Greek hammer thrower", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Greek hammer thrower", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Alexandra_<PERSON>"}]}, {"year": "1980", "text": "<PERSON>, American activist and author", "html": "1980 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American activist and author", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American activist and author", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1981", "text": "<PERSON>, American skateboarder and photographer", "html": "1981 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American skateboarder and photographer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American skateboarder and photographer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1981", "text": "<PERSON>, German footballer", "html": "1981 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1982", "text": "<PERSON>, American baseball player", "html": "1982 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1982", "text": "<PERSON>, Italian rugby player", "html": "1982 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian rugby player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian rugby player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1982", "text": "<PERSON>, Canadian activist and author", "html": "1982 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian activist and author", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian activist and author", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1982", "text": "<PERSON><PERSON><PERSON><PERSON>, Gabonese basketball player", "html": "1982 - <a href=\"https://wikipedia.org/wiki/St%C3%A9<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Gabonese basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/St%C3%A9<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Gabonese basketball player", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/St%C3%A9<PERSON><PERSON>_<PERSON>"}]}, {"year": "1983", "text": "<PERSON>, Canadian ice hockey player and executive", "html": "1983 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(ice_hockey)\" title=\"<PERSON> (ice hockey)\"><PERSON></a>, Canadian ice hockey player and executive", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(ice_hockey)\" title=\"<PERSON> (ice hockey)\"><PERSON></a>, Canadian ice hockey player and executive", "links": [{"title": "<PERSON> (ice hockey)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(ice_hockey)"}]}, {"year": "1983", "text": "<PERSON>, Canadian ice hockey player", "html": "1983 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1983", "text": "<PERSON><PERSON><PERSON>, American singer and songwriter", "html": "1983 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American singer and songwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American singer and songwriter", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON><PERSON>"}]}, {"year": "1983", "text": "<PERSON><PERSON>, Kenyan runner", "html": "1983 - <a href=\"https://wikipedia.org/wiki/Ha<PERSON>_<PERSON>\" title=\"Haron <PERSON>\"><PERSON><PERSON></a>, Kenyan runner", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"Haron Keita<PERSON>\"><PERSON><PERSON></a>, Kenyan runner", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Haron_Keitany"}]}, {"year": "1983", "text": "<PERSON><PERSON><PERSON><PERSON>, French race car driver", "html": "1983 - <a href=\"https://wikipedia.org/wiki/S%C3%A<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, French race car driver", "no_year_html": "<a href=\"https://wikipedia.org/wiki/S%C3%A<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, French race car driver", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/S%C3%<PERSON><PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1984", "text": "<PERSON>, Argentinian footballer", "html": "1984 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Argentinian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Argentinian footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1984", "text": "<PERSON>, English footballer", "html": "1984 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(footballer)\" title=\"<PERSON> (footballer)\"><PERSON></a>, English footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>(footballer)\" title=\"<PERSON> (footballer)\"><PERSON></a>, English footballer", "links": [{"title": "<PERSON> (footballer)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>(footballer)"}]}, {"year": "1984", "text": "<PERSON>, English footballer", "html": "1984 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(footballer)\" title=\"<PERSON> (footballer)\"><PERSON></a>, English footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>(footballer)\" title=\"<PERSON> (footballer)\"><PERSON></a>, English footballer", "links": [{"title": "<PERSON> (footballer)", "link": "https://wikipedia.org/wiki/<PERSON>_(footballer)"}]}, {"year": "1984", "text": "<PERSON>, American actress", "html": "1984 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1985", "text": "<PERSON>, Dominican baseball player", "html": "1985 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dominican baseball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dominican baseball player", "links": [{"title": "Fernando <PERSON>", "link": "https://wikipedia.org/wiki/Fernando_<PERSON>bad"}]}, {"year": "1985", "text": "<PERSON><PERSON><PERSON>, Polish footballer", "html": "1985 - <a href=\"https://wikipedia.org/wiki/%C5%81ukasz_Bro%C5%BA\" title=\"<PERSON><PERSON><PERSON> Bro<PERSON>\"><PERSON><PERSON><PERSON></a>, Polish footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/%C5%81uka<PERSON>_<PERSON>ro%C5%BA\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Polish footballer", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/%C5%81ukasz_Bro%C5%BA"}]}, {"year": "1985", "text": "<PERSON>, English footballer", "html": "1985 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(footballer,_born_1985)\" title=\"<PERSON> (footballer, born 1985)\"><PERSON></a>, English footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(footballer,_born_1985)\" title=\"<PERSON> (footballer, born 1985)\"><PERSON></a>, English footballer", "links": [{"title": "<PERSON> (footballer, born 1985)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(footballer,_born_1985)"}]}, {"year": "1986", "text": "<PERSON>, American actress", "html": "1986 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1986", "text": "<PERSON>, Australian-Samoan rugby league player", "html": "1986 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian-Samoan rugby league player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian-Samoan rugby league player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1986", "text": "<PERSON>, American actress", "html": "1986 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1987", "text": "<PERSON><PERSON>, Belarusian middle-distance runner", "html": "1987 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Belarusian middle-distance runner", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Belarusian middle-distance runner", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1987", "text": "<PERSON>, Chinese businessman", "html": "1987 - <a href=\"https://wikipedia.org/wiki/Bo_Guagua\" title=\"Bo Guagua\"><PERSON></a>, Chinese businessman", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Bo_Guagua\" title=\"Bo Guagua\"><PERSON></a>, Chinese businessman", "links": [{"title": "Bo <PERSON>", "link": "https://wikipedia.org/wiki/Bo_Guagua"}]}, {"year": "1987", "text": "<PERSON>, American soldier and intelligence analyst", "html": "1987 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American soldier and intelligence analyst", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American soldier and intelligence analyst", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_Manning"}]}, {"year": "1987", "text": "<PERSON>, Colombian baseball player", "html": "1987 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Colombian baseball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Colombian baseball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1988", "text": "<PERSON><PERSON>, Estonian cyclist", "html": "1988 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Estonian cyclist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Estonian cyclist", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1988", "text": "<PERSON><PERSON><PERSON>, Estonian ice dancer", "html": "1988 - <a href=\"https://wikipedia.org/wiki/Grethe_Gr%C3%BCnberg\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Estonian ice dancer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/G<PERSON><PERSON>_Gr%C3%BCnberg\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Estonian ice dancer", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Grethe_Gr%C3%BCnberg"}]}, {"year": "1988", "text": "<PERSON>, Canadian basketball player", "html": "1988 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian basketball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1988", "text": "<PERSON>, Kenyan runner", "html": "1988 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Kenyan runner", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Kenyan runner", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1988", "text": "<PERSON><PERSON>, Swiss footballer", "html": "1988 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Swiss footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Swiss footballer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1988", "text": "<PERSON>, Scottish footballer", "html": "1988 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1989", "text": "<PERSON>, Ghanaian footballer", "html": "1989 - <a href=\"https://wikipedia.org/wiki/Andr%C3%A9_Ayew\" title=\"<PERSON>\"><PERSON></a>, Ghanaian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Andr%C3%A9_Ayew\" title=\"<PERSON>\"><PERSON></a>, Ghanaian footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Andr%C3%A9_Ayew"}]}, {"year": "1989", "text": "<PERSON>, American musician", "html": "1989 - <a href=\"https://wikipedia.org/wiki/Taylor_<PERSON>\" title=\"Taylor York\"><PERSON></a>, American musician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Taylor_<PERSON>\" title=\"Taylor York\"><PERSON></a>, American musician", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Taylor_York"}]}, {"year": "1990", "text": "<PERSON>, American actor", "html": "1990 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(actor)\" title=\"<PERSON> (actor)\"><PERSON></a>, American actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>(actor)\" title=\"<PERSON> (actor)\"><PERSON></a>, American actor", "links": [{"title": "<PERSON> (actor)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(actor)"}]}, {"year": "1991", "text": "<PERSON>, Australian rugby league player", "html": "1991 - <a href=\"https://wikipedia.org/wiki/<PERSON>_Rank<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian rugby league player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_Rank<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian rugby league player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Jordan_Rankin"}]}, {"year": "1991", "text": "<PERSON><PERSON><PERSON>, Ethiopian runner", "html": "1991 - <a href=\"https://wikipedia.org/wiki/Atsedu_Tsegay\" title=\"Atsedu Tsegay\">Atsed<PERSON> Tsegay</a>, Ethiopian runner", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Atsedu_Tsegay\" title=\"Atsedu Tsegay\">Atsed<PERSON> Tsegay</a>, Ethiopian runner", "links": [{"title": "Atsedu Tsegay", "link": "https://wikipedia.org/wiki/Atsedu_Tsegay"}]}, {"year": "1992", "text": "<PERSON><PERSON><PERSON>, South African cricketer", "html": "1992 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON> <PERSON></a>, South African cricketer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, South African cricketer", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1992", "text": "<PERSON>, Bahamian basketball player", "html": "1992 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Bahamian basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Bahamian basketball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1992", "text": "<PERSON>, Canadian drummer and percussionist", "html": "1992 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian drummer and percussionist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian drummer and percussionist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1993", "text": "<PERSON><PERSON><PERSON>, American actress", "html": "1993 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American actress", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1993", "text": "<PERSON>, Peruvian tennis player", "html": "1993 - <a href=\"https://wikipedia.org/wiki/<PERSON>_K%C3%BA_Flores\" title=\"<PERSON>\"><PERSON></a>, Peruvian tennis player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%BA_Flores\" title=\"<PERSON>\"><PERSON></a>, Peruvian tennis player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Patricia_K%C3%BA_Flores"}]}, {"year": "1994", "text": "<PERSON>, New Zealand rugby league player", "html": "1994 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, New Zealand rugby league player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, New Zealand rugby league player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1994", "text": "<PERSON>, American singer-songwriter, keyboard player and actor", "html": "1994 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter, keyboard player and actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter, keyboard player and actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1995", "text": "<PERSON><PERSON><PERSON><PERSON>, French basketball player", "html": "1995 - <a href=\"https://wikipedia.org/wiki/G<PERSON><PERSON><PERSON>_<PERSON>\" title=\"Guerschon Yabusele\"><PERSON><PERSON><PERSON><PERSON></a>, French basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>sch<PERSON> Yabusele\"><PERSON><PERSON><PERSON><PERSON></a>, French basketball player", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/G<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1996", "text": "<PERSON><PERSON><PERSON>, Russian figure skater", "html": "1996 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Russian figure skater", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Russian figure skater", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1997", "text": "<PERSON><PERSON><PERSON>, British-Australian tennis player", "html": "1997 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, British-Australian tennis player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, British-Australian tennis player", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Na<PERSON><PERSON>_<PERSON>ns"}]}, {"year": "1997", "text": "<PERSON><PERSON><PERSON>, Japanese figure skater", "html": "1997 - <a href=\"https://wikipedia.org/wiki/Shoma_Uno\" title=\"Shoma Uno\"><PERSON><PERSON><PERSON></a>, Japanese figure skater", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>hom<PERSON>_Uno\" title=\"Shoma Uno\"><PERSON><PERSON><PERSON></a>, Japanese figure skater", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Shoma_Uno"}]}, {"year": "1998", "text": "<PERSON>, English actress", "html": "1998 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actress", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>field"}]}, {"year": "1998", "text": "<PERSON>, Norwegian footballer", "html": "1998 - <a href=\"https://wikipedia.org/wiki/<PERSON>_%C3%98<PERSON><PERSON>\" title=\"<PERSON>\"><PERSON></a>, Norwegian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_%C3%98<PERSON><PERSON>\" title=\"<PERSON>\"><PERSON></a>, Norwegian footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_%C3%98degaard"}]}, {"year": "1999", "text": "<PERSON><PERSON>, Japanese singer, model, and actress", "html": "1999 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Japanese singer, model, and actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Japanese singer, model, and actress", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "2000", "text": "<PERSON>, French footballer", "html": "2000 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(footballer)\" title=\"<PERSON> (footballer)\"><PERSON></a>, French footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(footballer)\" title=\"<PERSON> (footballer)\"><PERSON></a>, French footballer", "links": [{"title": "<PERSON> (footballer)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(footballer)"}]}, {"year": "2002", "text": "<PERSON><PERSON>, French footballer", "html": "2002 - <a href=\"https://wikipedia.org/wiki/Cast<PERSON>_Lukeba\" title=\"Castello Lukeba\"><PERSON><PERSON></a>, French footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Castello_Lukeba\" title=\"Castello Lukeba\"><PERSON><PERSON></a>, French footballer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Castello_Lukeba"}]}], "Deaths": [{"year": "779", "text": "<PERSON><PERSON><PERSON>, abbot of Fulda", "html": "779 - <a href=\"https://wikipedia.org/wiki/Saint_Sturm\" title=\"Saint Sturm\">Sturm</a>, <a href=\"https://wikipedia.org/wiki/Abbot\" title=\"Abbot\">abbot</a> of <a href=\"https://wikipedia.org/wiki/Fulda_monastery\" class=\"mw-redirect\" title=\"Fulda monastery\">Fulda</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Saint_Sturm\" title=\"Saint Sturm\">Sturm</a>, <a href=\"https://wikipedia.org/wiki/Abbot\" title=\"Abbot\">abbot</a> of <a href=\"https://wikipedia.org/wiki/Fulda_monastery\" class=\"mw-redirect\" title=\"Fulda monastery\">Fulda</a>", "links": [{"title": "Saint Sturm", "link": "https://wikipedia.org/wiki/Saint_Sturm"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Abbot"}, {"title": "Fulda monastery", "link": "https://wikipedia.org/wiki/Fulda_monastery"}]}, {"year": "908", "text": "<PERSON><PERSON><PERSON> ibn <PERSON><PERSON><PERSON>, <PERSON><PERSON> vizier", "html": "908 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>-<PERSON>_<PERSON>-Jarjara%27i\" title=\"<PERSON><PERSON><PERSON> ibn <PERSON> al<PERSON>ar<PERSON>'i\"><PERSON><PERSON><PERSON> ibn <PERSON> al<PERSON>'i</a>, <PERSON><PERSON> vizier", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>-<PERSON>_<PERSON>-Jarjara%27i\" title=\"<PERSON><PERSON><PERSON> ibn <PERSON> al<PERSON>'i\"><PERSON><PERSON><PERSON> ibn <PERSON> al<PERSON>'i</a>, <PERSON><PERSON> vizier", "links": [{"title": "<PERSON><PERSON><PERSON> ibn <PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>-<PERSON>_<PERSON>-Jarjar<PERSON>%27i"}]}, {"year": "908", "text": "<PERSON><PERSON><PERSON> ibn <PERSON>, Abbasid prince and poet, anti-caliph for one day", "html": "908 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>-<PERSON>%27tazz\" title=\"<PERSON><PERSON><PERSON> ibn al-<PERSON>ta<PERSON>\"><PERSON><PERSON><PERSON> ibn <PERSON></a>, Abbasid prince and poet, anti-caliph for one day", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>-<PERSON>%27tazz\" title=\"<PERSON><PERSON><PERSON> ibn al-<PERSON>ta<PERSON>\"><PERSON><PERSON><PERSON> ibn <PERSON></a>, Abbasid prince and poet, anti-caliph for one day", "links": [{"title": "<PERSON><PERSON><PERSON> ibn <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>-<PERSON>%27tazz"}]}, {"year": "942", "text": "<PERSON>, duke of Normandy", "html": "942 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_Longsword\" class=\"mw-redirect\" title=\"William I Longsword\"><PERSON></a>, duke of <a href=\"https://wikipedia.org/wiki/Duchy_of_Normandy\" title=\"Duchy of Normandy\">Normandy</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_Longsword\" class=\"mw-redirect\" title=\"William I Longsword\"><PERSON></a>, duke of <a href=\"https://wikipedia.org/wiki/Duchy_of_Normandy\" title=\"Duchy of Normandy\">Normandy</a>", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_Longsword"}, {"title": "Duchy of Normandy", "link": "https://wikipedia.org/wiki/Duchy_of_Normandy"}]}, {"year": "1187", "text": "<PERSON> (b. 1100)", "html": "1187 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_VIII\" title=\"<PERSON> Gregory VIII\"><PERSON> <PERSON> VIII</a> (b. 1100)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON> Gregory VIII\"><PERSON> VIII</a> (b. 1100)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1195", "text": "<PERSON>, Count of Hainaut (b. 1150)", "html": "1195 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Count_of_Hainaut\" title=\"<PERSON>, Count of Hainaut\"><PERSON>, Count of Hainaut</a> (b. 1150)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Count_of_Hainaut\" title=\"<PERSON>, Count of Hainaut\"><PERSON>, Count of Hainaut</a> (b. 1150)", "links": [{"title": "<PERSON>, Count of Hainaut", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Count_of_Hai<PERSON>ut"}]}, {"year": "1273", "text": "<PERSON><PERSON>, Persian jurist, theologian, and poet (b. 1207)", "html": "1273 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Persian jurist, theologian, and poet (b. 1207)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Persian jurist, theologian, and poet (b. 1207)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Rumi"}]}, {"year": "1316", "text": "<PERSON>, bishop-elect of León", "html": "1316 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A1<PERSON><PERSON>_(bishop_of_Le%C3%B3n)\" title=\"<PERSON> (bishop of León)\"><PERSON></a>, bishop-elect of León", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A1<PERSON><PERSON>_(bishop_of_Le%C3%B3n)\" title=\"<PERSON> (bishop of León)\"><PERSON></a>, bishop-elect of León", "links": [{"title": "<PERSON> (bishop of León)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A1ndez_(bishop_of_Le%C3%B3n)"}]}, {"year": "1419", "text": "<PERSON>, Chief Justice of England", "html": "1419 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Chief Justice of England", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Chief Justice of England", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1471", "text": "<PERSON><PERSON><PERSON>, Duchess of Burgundy (b. 1397)", "html": "1471 - <a href=\"https://wikipedia.org/wiki/In<PERSON><PERSON>_<PERSON>,_Duchess_of_Burgundy\" class=\"mw-redirect\" title=\"In<PERSON><PERSON> <PERSON>, Duchess of Burgundy\"><PERSON><PERSON><PERSON> <PERSON>, Duchess of Burgundy</a> (b. 1397)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/In<PERSON><PERSON>_<PERSON>,_Duchess_of_Burgundy\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON> <PERSON>, Duchess of Burgundy\"><PERSON><PERSON><PERSON> <PERSON>, Duchess of Burgundy</a> (b. 1397)", "links": [{"title": "<PERSON><PERSON><PERSON>, Duchess of Burgundy", "link": "https://wikipedia.org/wiki/In<PERSON><PERSON>_<PERSON>,_Duchess_of_Burgundy"}]}, {"year": "1559", "text": "<PERSON>, Italian Renaissance poet and painter (b. 1538)", "html": "1559 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian Renaissance poet and painter (b. 1538)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian Renaissance poet and painter (b. 1538)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1562", "text": "<PERSON><PERSON><PERSON>, Grand Duchess of Tuscany (b. 1522)", "html": "1562 - <a href=\"https://wikipedia.org/wiki/Eleonora_di_Toledo\" class=\"mw-redirect\" title=\"Eleonora di Toledo\"><PERSON><PERSON><PERSON> di Toledo</a>, Grand Duchess of Tuscany (b. 1522)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Eleonora_di_Toledo\" class=\"mw-redirect\" title=\"Eleonora di Toledo\"><PERSON><PERSON><PERSON> Toledo</a>, Grand Duchess of Tuscany (b. 1522)", "links": [{"title": "Eleonora di Toledo", "link": "https://wikipedia.org/wiki/Eleonora_di_Toledo"}]}, {"year": "1663", "text": "<PERSON><PERSON><PERSON> of Ndongo and Matamba (b. 1583)", "html": "1663 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_of_Ndongo_and_Matamba\" title=\"<PERSON><PERSON><PERSON> of Ndongo and Matamba\"><PERSON><PERSON><PERSON> of Ndongo and Matamba</a> (b. 1583)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_of_Ndongo_and_Matamba\" title=\"<PERSON><PERSON><PERSON> of Ndongo and Matamba\"><PERSON><PERSON><PERSON> of Ndongo and Matamba</a> (b. 1583)", "links": [{"title": "Nzinga of Ndongo and Matamba", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_of_Ndongo_and_Matamba"}]}, {"year": "1721", "text": "<PERSON>, 1st Earl of Scarbrough, English soldier and politician, Chancellor of the Duchy of Lancaster (b. 1640)", "html": "1721 - <a href=\"https://wikipedia.org/wiki/<PERSON>,_1st_Earl_of_Scarbrough\" title=\"<PERSON>, 1st Earl of Scarbrough\"><PERSON>, 1st Earl of Scarbrough</a>, English soldier and politician, <a href=\"https://wikipedia.org/wiki/Chancellor_of_the_Duchy_of_Lancaster\" title=\"Chancellor of the Duchy of Lancaster\">Chancellor of the Duchy of Lancaster</a> (b. 1640)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>,_1st_Earl_of_Scarbrough\" title=\"<PERSON>, 1st Earl of Scarbrough\"><PERSON>, 1st Earl of Scarbrough</a>, English soldier and politician, <a href=\"https://wikipedia.org/wiki/Chancellor_of_the_Duchy_of_Lancaster\" title=\"Chancellor of the Duchy of Lancaster\">Chancellor of the Duchy of Lancaster</a> (b. 1640)", "links": [{"title": "<PERSON>, 1st Earl of Scarbrough", "link": "https://wikipedia.org/wiki/<PERSON>,_1st_Earl_of_Scarbrough"}, {"title": "Chancellor of the Duchy of Lancaster", "link": "https://wikipedia.org/wiki/Chancellor_of_the_Duchy_of_Lancaster"}]}, {"year": "1830", "text": "<PERSON><PERSON><PERSON>, Venezuelan general and politician, second President of Venezuela (b. 1783)", "html": "1830 - <a href=\"https://wikipedia.org/wiki/Sim%C3%B3n_Bol%C3%ADvar\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Venezuelan general and politician, second <a href=\"https://wikipedia.org/wiki/President_of_Venezuela\" title=\"President of Venezuela\">President of Venezuela</a> (b. 1783)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Sim%C3%B3n_Bol%C3%ADvar\" title=\"<PERSON><PERSON><PERSON> Bolí<PERSON>\"><PERSON><PERSON><PERSON></a>, Venezuelan general and politician, second <a href=\"https://wikipedia.org/wiki/President_of_Venezuela\" title=\"President of Venezuela\">President of Venezuela</a> (b. 1783)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Sim%C3%B3n_Bol%C3%ADvar"}, {"title": "President of Venezuela", "link": "https://wikipedia.org/wiki/President_of_Venezuela"}]}, {"year": "1833", "text": "<PERSON><PERSON><PERSON>, German feral child (b. 1812?)", "html": "1833 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, German <a href=\"https://wikipedia.org/wiki/Feral_child\" title=\"Feral child\">feral child</a> (b. 1812?)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, German <a href=\"https://wikipedia.org/wiki/Feral_child\" title=\"Feral child\">feral child</a> (b. 1812?)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}, {"title": "Feral child", "link": "https://wikipedia.org/wiki/Fe<PERSON>_child"}]}, {"year": "1847", "text": "<PERSON>, Duchess of Parma (b. 1791)", "html": "1847 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Duchess_of_Parma\" title=\"<PERSON>, Duchess of Parma\"><PERSON>, Duchess of Parma</a> (b. 1791)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Duchess_of_Parma\" title=\"<PERSON>, Duchess of Parma\"><PERSON>, Duchess of Parma</a> (b. 1791)", "links": [{"title": "<PERSON>, Duchess of Parma", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Duchess_of_Parma"}]}, {"year": "1857", "text": "<PERSON>, Irish hydrographer and officer in the Royal Navy (b. 1774)", "html": "1857 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Irish hydrographer and officer in the Royal Navy (b. 1774)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Irish hydrographer and officer in the Royal Navy (b. 1774)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1891", "text": "<PERSON>, Mexican politician and interim President (1876-1877) (b. 1823)", "html": "1891 - <a href=\"https://wikipedia.org/wiki/Jos%C3%A9_Mar%C3%ADa_Iglesias\" title=\"<PERSON>\"><PERSON></a>, Mexican politician and interim President (1876-1877) (b. 1823)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Jos%C3%A9_Mar%C3%ADa_Iglesias\" title=\"<PERSON>\"><PERSON></a>, Mexican politician and interim President (1876-1877) (b. 1823)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Jos%C3%A9_Mar%C3%ADa_Iglesias"}]}, {"year": "1904", "text": "<PERSON>, Irish-Australian politician, 16th Premier of Victoria (b. 1848)", "html": "1904 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Irish-Australian politician, 16th <a href=\"https://wikipedia.org/wiki/Premier_of_Victoria\" title=\"Premier of Victoria\">Premier of Victoria</a> (b. 1848)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Irish-Australian politician, 16th <a href=\"https://wikipedia.org/wiki/Premier_of_Victoria\" title=\"Premier of Victoria\">Premier of Victoria</a> (b. 1848)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Premier of Victoria", "link": "https://wikipedia.org/wiki/Premier_of_Victoria"}]}, {"year": "1907", "text": "<PERSON>, 1st Baron <PERSON>, Irish-Scottish physicist and engineer (b. 1824)", "html": "1907 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_1st_Baron_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>, 1st Baron <PERSON>\"><PERSON>, 1st Baron <PERSON></a>, Irish-Scottish physicist and engineer (b. 1824)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>,_1st_Baron_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>, 1st Baron <PERSON>\"><PERSON>, 1st Baron <PERSON></a>, Irish-Scottish physicist and engineer (b. 1824)", "links": [{"title": "<PERSON>, 1st Baron <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_1st_Baron_<PERSON>"}]}, {"year": "1909", "text": "<PERSON> of Belgium (b. 1835)", "html": "1909 - <a href=\"https://wikipedia.org/wiki/Leopold_II_of_Belgium\" title=\"Leopold II of Belgium\"><PERSON> II of Belgium</a> (b. 1835)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Leopold_II_of_Belgium\" title=\"Leopold II of Belgium\"><PERSON> II of Belgium</a> (b. 1835)", "links": [{"title": "Leopold II of Belgium", "link": "https://wikipedia.org/wiki/Leopold_II_of_Belgium"}]}, {"year": "1917", "text": "<PERSON>, English physician and activist (b. 1836)", "html": "1917 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English physician and activist (b. 1836)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English physician and activist (b. 1836)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1927", "text": "<PERSON><PERSON>, Indian activist (b. 1892)", "html": "1927 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Indian activist (b. 1892)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Indian activist (b. 1892)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1928", "text": "<PERSON>, American photographer (b. 1861)", "html": "1928 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American photographer (b. 1861)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American photographer (b. 1861)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1929", "text": "<PERSON>, Portuguese general and politician, tenth President of Portugal (b. 1863)", "html": "1929 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>_Costa\" class=\"mw-redirect\" title=\"<PERSON> Costa\"><PERSON></a>, Portuguese general and politician, tenth <a href=\"https://wikipedia.org/wiki/President_of_Portugal\" title=\"President of Portugal\">President of Portugal</a> (b. 1863)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_Costa\" class=\"mw-redirect\" title=\"<PERSON> Costa\"><PERSON></a>, Portuguese general and politician, tenth <a href=\"https://wikipedia.org/wiki/President_of_Portugal\" title=\"President of Portugal\">President of Portugal</a> (b. 1863)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>_<PERSON>_<PERSON>"}, {"title": "President of Portugal", "link": "https://wikipedia.org/wiki/President_of_Portugal"}]}, {"year": "1930", "text": "<PERSON>, Welsh composer and critic (b. 1894)", "html": "1930 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Welsh composer and critic (b. 1894)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Welsh composer and critic (b. 1894)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1932", "text": "<PERSON>, Danish discus thrower, shot putter, and tug of war competitor (b. 1867)", "html": "1932 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Danish discus thrower, shot putter, and <a href=\"https://wikipedia.org/wiki/Tug_of_war\" title=\"Tug of war\">tug of war</a> competitor (b. 1867)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Danish discus thrower, shot putter, and <a href=\"https://wikipedia.org/wiki/Tug_of_war\" title=\"Tug of war\">tug of war</a> competitor (b. 1867)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Tug of war", "link": "https://wikipedia.org/wiki/Tug_of_war"}]}, {"year": "1933", "text": "13th <PERSON><PERSON> (b. 1876)", "html": "1933 - <a href=\"https://wikipedia.org/wiki/13th_Dal<PERSON>_Lama\" title=\"13th Dalai Lama\">13th Dalai Lama</a> (b. 1876)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/13th_Dalai_Lama\" title=\"13th Dalai Lama\">13th Dalai Lama</a> (b. 1876)", "links": [{"title": "13th Dalai Lama", "link": "https://wikipedia.org/wiki/13th_<PERSON><PERSON>_<PERSON>"}]}, {"year": "1935", "text": "<PERSON><PERSON>, American poet (b. 1856)", "html": "1935 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American poet (b. 1856)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American poet (b. 1856)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1940", "text": "<PERSON>, Anglo-Irish mathematician and academic (b. 1860)", "html": "1940 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Anglo-Irish mathematician and academic (b. 1860)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Anglo-Irish mathematician and academic (b. 1860)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1942", "text": "<PERSON>, Lord <PERSON>, English lieutenant and politician (b. 1895)", "html": "1942 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Lord_<PERSON><PERSON>\" title=\"<PERSON>, Lord <PERSON>\"><PERSON>, Lord <PERSON></a>, English lieutenant and politician (b. 1895)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Lord_<PERSON>\" title=\"<PERSON>, Lord <PERSON>\"><PERSON>, Lord <PERSON></a>, English lieutenant and politician (b. 1895)", "links": [{"title": "<PERSON>, <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_<PERSON>_<PERSON><PERSON>"}]}, {"year": "1947", "text": "<PERSON><PERSON>, Greek engineer (b. 1877)", "html": "1947 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Greek engineer (b. 1877)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Greek engineer (b. 1877)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1956", "text": "<PERSON>, American actor (b. 1903)", "html": "1956 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor (b. 1903)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor (b. 1903)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1957", "text": "<PERSON>, English author, poet, and playwright (b. 1893)", "html": "1957 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English author, poet, and playwright (b. 1893)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English author, poet, and playwright (b. 1893)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "1962", "text": "<PERSON>, American actor (b. 1892)", "html": "1962 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(actor)\" title=\"<PERSON> (actor)\"><PERSON></a>, American actor (b. 1892)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(actor)\" title=\"<PERSON> (actor)\"><PERSON></a>, American actor (b. 1892)", "links": [{"title": "<PERSON> (actor)", "link": "https://wikipedia.org/wiki/<PERSON>_(actor)"}]}, {"year": "1964", "text": "<PERSON>, Austrian-American physicist and academic, Nobel Prize laureate (b. 1883)", "html": "1964 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Austrian-American physicist and academic, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Physics\" title=\"Nobel Prize in Physics\">Nobel Prize</a> laureate (b. 1883)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Austrian-American physicist and academic, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Physics\" title=\"Nobel Prize in Physics\">Nobel Prize</a> laureate (b. 1883)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "Nobel Prize in Physics", "link": "https://wikipedia.org/wiki/Nobel_Prize_in_Physics"}]}, {"year": "1967", "text": "<PERSON>, Australian lawyer and politician, 17th Prime Minister of Australia (b. 1908)", "html": "1967 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian lawyer and politician, 17th <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Australia\" title=\"Prime Minister of Australia\">Prime Minister of Australia</a> (b. 1908)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian lawyer and politician, 17th <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Australia\" title=\"Prime Minister of Australia\">Prime Minister of Australia</a> (b. 1908)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Prime Minister of Australia", "link": "https://wikipedia.org/wiki/Prime_Minister_of_Australia"}]}, {"year": "1970", "text": "<PERSON>, American historian, author, and educator (b. 1896)", "html": "1970 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>man <PERSON>rkin\"><PERSON></a>, American historian, author, and educator (b. 1896)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON> Waterman Larkin\"><PERSON></a>, American historian, author, and educator (b. 1896)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1978", "text": "<PERSON>, American trumpet player, composer, and bandleader (b. 1934)", "html": "1978 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American trumpet player, composer, and bandleader (b. 1934)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American trumpet player, composer, and bandleader (b. 1934)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1981", "text": "<PERSON><PERSON>, Greek composer and conductor (b. 1903)", "html": "1981 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_Evangelatos\" title=\"<PERSON><PERSON> Evangelato<PERSON>\"><PERSON><PERSON></a>, Greek composer and conductor (b. 1903)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>gelatos\" title=\"<PERSON><PERSON> Evangelato<PERSON>\"><PERSON><PERSON></a>, Greek composer and conductor (b. 1903)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Antiochos_Evangelatos"}]}, {"year": "1982", "text": "<PERSON>, American lawyer, judge, and politician (b. 1889)", "html": "1982 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer, judge, and politician (b. 1889)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer, judge, and politician (b. 1889)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "1986", "text": "<PERSON>, Colombian journalist (b. 1925)", "html": "1986 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Colombian journalist (b. 1925)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Colombian journalist (b. 1925)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1987", "text": "<PERSON><PERSON>, Dutch cardinal (b. 1900)", "html": "1987 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Dutch cardinal (b. 1900)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Dutch cardinal (b. 1900)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1987", "text": "<PERSON>, American porn actress (b. 1951)", "html": "1987 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(pornographic_actress)\" title=\"<PERSON> (pornographic actress)\"><PERSON></a>, American porn actress (b. 1951)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(pornographic_actress)\" title=\"<PERSON> (pornographic actress)\"><PERSON></a>, American porn actress (b. 1951)", "links": [{"title": "<PERSON> (pornographic actress)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>(pornographic_actress)"}]}, {"year": "1987", "text": "<PERSON>, Belgian-American author and poet (b. 1903)", "html": "1987 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Belgian-American author and poet (b. 1903)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Belgian-American author and poet (b. 1903)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1992", "text": "<PERSON><PERSON><PERSON><PERSON>, German journalist and philosopher (b. 1902)", "html": "1992 - <a href=\"https://wikipedia.org/wiki/G%C3%<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON> <PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, German journalist and philosopher (b. 1902)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/G%C3%<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON> <PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, German journalist and philosopher (b. 1902)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/G%C3%<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1992", "text": "<PERSON>, American actor (b. 1909)", "html": "1992 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor (b. 1909)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor (b. 1909)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1999", "text": "<PERSON>, American singer-songwriter and actor (b. 1920)", "html": "1999 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and actor (b. 1920)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and actor (b. 1920)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1999", "text": "<PERSON><PERSON>, American singer-songwriter and saxophonist (b. 1943)", "html": "1999 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_Jr.\" title=\"<PERSON><PERSON> Jr.\"><PERSON><PERSON>.</a>, American singer-songwriter and saxophonist (b. 1943)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_Jr.\" title=\"<PERSON><PERSON> Jr.\"><PERSON><PERSON>.</a>, American singer-songwriter and saxophonist (b. 1943)", "links": [{"title": "Grove<PERSON>.", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_Jr."}]}, {"year": "1999", "text": "<PERSON><PERSON>, American historian and academic (b. 1908)", "html": "1999 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American historian and academic (b. 1908)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American historian and academic (b. 1908)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "2002", "text": "<PERSON><PERSON> <PERSON><PERSON>, Sri Lankan lawyer and politician, tenth Sri Lankan Minister of Justice (b. 1910)", "html": "2002 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>gam\" title=\"<PERSON><PERSON> <PERSON><PERSON>\"><PERSON><PERSON> <PERSON><PERSON></a>, Sri Lankan lawyer and politician, tenth <a href=\"https://wikipedia.org/wiki/Minister_of_Justice_(Sri_Lanka)\" title=\"Minister of Justice (Sri Lanka)\">Sri Lankan Minister of Justice</a> (b. 1910)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> <PERSON><PERSON>\"><PERSON><PERSON> <PERSON><PERSON></a>, Sri Lankan lawyer and politician, tenth <a href=\"https://wikipedia.org/wiki/Minister_of_Justice_(Sri_Lanka)\" title=\"Minister of Justice (Sri Lanka)\">Sri Lankan Minister of Justice</a> (b. 1910)", "links": [{"title": "<PERSON><PERSON> <PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>anayagam"}, {"title": "Minister of Justice (Sri Lanka)", "link": "https://wikipedia.org/wiki/Minister_of_Justice_(Sri_Lanka)"}]}, {"year": "2003", "text": "<PERSON>, American football player and coach (b. 1921)", "html": "2003 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player and coach (b. 1921)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player and coach (b. 1921)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2004", "text": "<PERSON>, American painter and sculptor (b. 1931)", "html": "2004 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American painter and sculptor (b. 1931)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American painter and sculptor (b. 1931)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "2005", "text": "<PERSON>, American journalist and author (b. 1922)", "html": "2005 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(columnist)\" title=\"<PERSON> (columnist)\"><PERSON></a>, American journalist and author (b. 1922)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(columnist)\" title=\"<PERSON> (columnist)\"><PERSON></a>, American journalist and author (b. 1922)", "links": [{"title": "<PERSON> (columnist)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(columnist)"}]}, {"year": "2005", "text": "<PERSON>, Canadian actor and poet (b. 1929)", "html": "2005 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian actor and poet (b. 1929)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian actor and poet (b. 1929)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2005", "text": "<PERSON><PERSON><PERSON>, Estonian orientalist and academic (b. 1936)", "html": "2005 - <a href=\"https://wikipedia.org/wiki/Haljand_Udam\" title=\"Hal<PERSON><PERSON>dam\"><PERSON><PERSON><PERSON></a>, Estonian orientalist and academic (b. 1936)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Haljand_<PERSON>dam\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Estonian orientalist and academic (b. 1936)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Haljand_Udam"}]}, {"year": "2006", "text": "<PERSON>, American baseball player and coach (b. 1935)", "html": "2006 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player and coach (b. 1935)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player and coach (b. 1935)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2008", "text": "<PERSON>, American football player and coach (b. 1914)", "html": "2008 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player and coach (b. 1914)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player and coach (b. 1914)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2008", "text": "<PERSON>, German singer-songwriter, producer, and journalist (b. 1942)", "html": "2008 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German singer-songwriter, producer, and journalist (b. 1942)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German singer-songwriter, producer, and journalist (b. 1942)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2008", "text": "<PERSON>, American baseball player and coach (b. 1955)", "html": "2008 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(pitcher,_born_1955)\" title=\"<PERSON> (pitcher, born 1955)\"><PERSON></a>, American baseball player and coach (b. 1955)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(pitcher,_born_1955)\" title=\"<PERSON> (pitcher, born 1955)\"><PERSON></a>, American baseball player and coach (b. 1955)", "links": [{"title": "<PERSON> (pitcher, born 1955)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(pitcher,_born_1955)"}]}, {"year": "2008", "text": "<PERSON><PERSON>, Congolese chimpanzee, oldest recorded (b. 1942)", "html": "2008 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_(chimpanzee)\" title=\"<PERSON><PERSON> (chimpanzee)\"><PERSON><PERSON></a>, Congolese <a href=\"https://wikipedia.org/wiki/Chimpanzee\" title=\"Chimpanzee\">chimpanzee</a>, oldest recorded (b. 1942)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_(chimpanzee)\" title=\"<PERSON><PERSON> (chimpanzee)\"><PERSON><PERSON></a>, Congolese <a href=\"https://wikipedia.org/wiki/Chimpanzee\" title=\"Chimpanzee\">chimpanzee</a>, oldest recorded (b. 1942)", "links": [{"title": "<PERSON><PERSON> (chimpanzee)", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_(chimpanzee)"}, {"title": "Chimpanzee", "link": "https://wikipedia.org/wiki/Chimpanzee"}]}, {"year": "2009", "text": "<PERSON>, American football player (b. 1983)", "html": "2009 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(wide_receiver)\" title=\"<PERSON> (wide receiver)\"><PERSON></a>, American football player (b. 1983)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(wide_receiver)\" title=\"<PERSON> (wide receiver)\"><PERSON></a>, American football player (b. 1983)", "links": [{"title": "<PERSON> (wide receiver)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>(wide_receiver)"}]}, {"year": "2009", "text": "<PERSON>, American actress (b. 1919)", "html": "2009 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress (b. 1919)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress (b. 1919)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2009", "text": "<PERSON><PERSON>, American actress (b. 1946)", "html": "2009 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American actress (b. 1946)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American actress (b. 1946)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>"}]}, {"year": "2010", "text": "<PERSON>, American singer-songwriter (b. 1941)", "html": "2010 - <a href=\"https://wikipedia.org/wiki/Captain_<PERSON><PERSON><PERSON>\" title=\"Captain <PERSON><PERSON><PERSON>\">Captain <PERSON><PERSON><PERSON></a>, American singer-songwriter (b. 1941)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Captain_<PERSON><PERSON><PERSON>\" title=\"Captain <PERSON><PERSON><PERSON>\">Captain <PERSON><PERSON><PERSON></a>, American singer-songwriter (b. 1941)", "links": [{"title": "Captain <PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Captain_<PERSON><PERSON><PERSON>"}]}, {"year": "2010", "text": "<PERSON>, American basketball and baseball player (b. 1923)", "html": "2010 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball and baseball player (b. 1923)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball and baseball player (b. 1923)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2010", "text": "<PERSON>, English footballer (b. 1946)", "html": "2010 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English footballer (b. 1946)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English footballer (b. 1946)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "2011", "text": "<PERSON>, Venezuelan journalist and author, Miss Venezuela 2000 (b. 1983)", "html": "2011 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Venezuelan journalist and author, <a href=\"https://wikipedia.org/wiki/Miss_Venezuela_2000\" class=\"mw-redirect\" title=\"Miss Venezuela 2000\">Miss Venezuela 2000</a> (b. 1983)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Venezuelan journalist and author, <a href=\"https://wikipedia.org/wiki/Miss_Venezuela_2000\" class=\"mw-redirect\" title=\"Miss Venezuela 2000\">Miss Venezuela 2000</a> (b. 1983)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Miss Venezuela 2000", "link": "https://wikipedia.org/wiki/Miss_Venezuela_2000"}]}, {"year": "2011", "text": "<PERSON>, North Korean commander and politician, second Supreme Leader of North Korea (b. 1941)", "html": "2011 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, North Korean commander and politician, second <a href=\"https://wikipedia.org/wiki/List_of_leaders_of_North_Korea\" class=\"mw-redirect\" title=\"List of leaders of North Korea\">Supreme Leader of North Korea</a> (b. 1941)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, North Korean commander and politician, second <a href=\"https://wikipedia.org/wiki/List_of_leaders_of_North_Korea\" class=\"mw-redirect\" title=\"List of leaders of North Korea\">Supreme Leader of North Korea</a> (b. 1941)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}, {"title": "List of leaders of North Korea", "link": "https://wikipedia.org/wiki/List_of_leaders_of_North_Korea"}]}, {"year": "2012", "text": "<PERSON>, Filipino-American activist (b. 1947)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(activist)\" title=\"<PERSON> (activist)\"><PERSON></a>, Filipino-American activist (b. 1947)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(activist)\" title=\"<PERSON> (activist)\"><PERSON></a>, Filipino-American activist (b. 1947)", "links": [{"title": "<PERSON> (activist)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(activist)"}]}, {"year": "2012", "text": "<PERSON>, American priest and activist, co-founded the College of the Atlantic (b. 1922)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American priest and activist, co-founded the <a href=\"https://wikipedia.org/wiki/College_of_the_Atlantic\" title=\"College of the Atlantic\">College of the Atlantic</a> (b. 1922)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American priest and activist, co-founded the <a href=\"https://wikipedia.org/wiki/College_of_the_Atlantic\" title=\"College of the Atlantic\">College of the Atlantic</a> (b. 1922)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "College of the Atlantic", "link": "https://wikipedia.org/wiki/College_of_the_Atlantic"}]}, {"year": "2012", "text": "<PERSON>, American captain and politician (b. 1924)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American captain and politician (b. 1924)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American captain and politician (b. 1924)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2012", "text": "<PERSON><PERSON>, Canadian historian, journalist, and politician (b. 1929)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Canadian historian, journalist, and politician (b. 1929)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Canadian historian, journalist, and politician (b. 1929)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "2012", "text": "<PERSON>, American baseball player and radio host (b. 1957)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player and radio host (b. 1957)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player and radio host (b. 1957)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2013", "text": "<PERSON>, Latvian-Canadian photographer (b. 1929)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Latvian-Canadian photographer (b. 1929)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Latvian-Canadian photographer (b. 1929)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2013", "text": "<PERSON>, Spanish cardinal (b. 1926)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/Ricardo_<PERSON>%C3%ADa_Carl<PERSON>_Gord%C3%B3\" title=\"<PERSON>\"><PERSON></a>, Spanish cardinal (b. 1926)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Ricardo_<PERSON>%C3%ADa_Carl<PERSON>_<PERSON>rd%C3%B3\" title=\"<PERSON>\"><PERSON></a>, Spanish cardinal (b. 1926)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Ricardo_Mar%C3%<PERSON><PERSON>_<PERSON><PERSON>_Gord%C3%B3"}]}, {"year": "2013", "text": "<PERSON>, American historian and television host (b. 1925)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American historian and television host (b. 1925)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American historian and television host (b. 1925)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2013", "text": "<PERSON><PERSON><PERSON>, Japanese drummer, songwriter, and producer (b. 1950)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/Tetsur%C5%8D_<PERSON><PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese drummer, songwriter, and producer (b. 1950)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Tetsur%C5%8D_<PERSON><PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese drummer, songwriter, and producer (b. 1950)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Tetsur%C5%8D_<PERSON><PERSON><PERSON><PERSON>"}]}, {"year": "2013", "text": "<PERSON>,  American geneticist and biologist (b. 1925)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American geneticist and biologist (b. 1925)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American geneticist and biologist (b. 1925)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2013", "text": "<PERSON><PERSON>, Dutch sailor (b. 1926)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Dutch sailor (b. 1926)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Dutch sailor (b. 1926)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>"}]}, {"year": "2014", "text": "<PERSON><PERSON>, German-American scientist and engineer (b. 1913)", "html": "2014 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, German-American scientist and engineer (b. 1913)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, German-American scientist and engineer (b. 1913)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "2014", "text": "<PERSON>, American journalist (b. 1917)", "html": "2014 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_Hottelet\" title=\"<PERSON>\"><PERSON></a>, American journalist (b. 1917)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_Hottelet\" title=\"<PERSON> Hottelet\"><PERSON></a>, American journalist (b. 1917)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Richard_<PERSON>._Hottelet"}]}, {"year": "2014", "text": "<PERSON><PERSON>, Ukrainian poet and playwright (b. 1949)", "html": "2014 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_L<PERSON>\" title=\"<PERSON><PERSON> Lysheha\"><PERSON><PERSON></a>, Ukrainian poet and playwright (b. 1949)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> L<PERSON>\"><PERSON><PERSON></a>, Ukrainian poet and playwright (b. 1949)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Ole<PERSON>_<PERSON>"}]}, {"year": "2014", "text": "<PERSON>, American captain (b. 1919)", "html": "2014 - <a href=\"https://wikipedia.org/wiki/Lowell_Steward\" title=\"Lowell Steward\"><PERSON></a>, American captain (b. 1919)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Lowell_Steward\" title=\"Lowell Steward\"><PERSON></a>, American captain (b. 1919)", "links": [{"title": "<PERSON>ard", "link": "https://wikipedia.org/wiki/Lowell_Steward"}]}, {"year": "2014", "text": "<PERSON>, Croatian colonel, lawyer, and politician, Croatian Minister of the Interior (b. 1938)", "html": "2014 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C4%87_(politician)\" title=\"<PERSON> (politician)\"><PERSON></a>, Croatian colonel, lawyer, and politician, <a href=\"https://wikipedia.org/wiki/Ministry_of_the_Interior_(Croatia)\" title=\"Ministry of the Interior (Croatia)\">Croatian Minister of the Interior</a> (b. 1938)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C4%87_(politician)\" title=\"<PERSON> (politician)\"><PERSON></a>, Croatian colonel, lawyer, and politician, <a href=\"https://wikipedia.org/wiki/Ministry_of_the_Interior_(Croatia)\" title=\"Ministry of the Interior (Croatia)\">Croatian Minister of the Interior</a> (b. 1938)", "links": [{"title": "<PERSON> (politician)", "link": "https://wikipedia.org/wiki/<PERSON>_Veki%C4%87_(politician)"}, {"title": "Ministry of the Interior (Croatia)", "link": "https://wikipedia.org/wiki/Ministry_of_the_Interior_(Croatia)"}]}, {"year": "2015", "text": "<PERSON>, American baseball player and manager (b. 1924)", "html": "2015 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player and manager (b. 1924)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player and manager (b. 1924)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2015", "text": "<PERSON><PERSON><PERSON>, American-Japanese biochemist and academic (b. 1920)", "html": "2015 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American-Japanese biochemist and academic (b. 1920)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American-Japanese biochemist and academic (b. 1920)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "2015", "text": "<PERSON>, German-American philosopher and theologian (b. 1928)", "html": "2015 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German-American philosopher and theologian (b. 1928)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German-American philosopher and theologian (b. 1928)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2016", "text": "<PERSON>, American soldier and politician (b. 1922)", "html": "2016 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, American soldier and politician (b. 1922)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, American soldier and politician (b. 1922)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "2016", "text": "<PERSON>, American doctor (b. 1920)", "html": "2016 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American doctor (b. 1920)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American doctor (b. 1920)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "2016", "text": "<PERSON>, American voice director (b. 1929)", "html": "2016 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(director)\" title=\"<PERSON> (director)\"><PERSON></a>, American voice director (b. 1929)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(director)\" title=\"<PERSON> (director)\"><PERSON></a>, American voice director (b. 1929)", "links": [{"title": "<PERSON> (director)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(director)"}]}, {"year": "2020", "text": "<PERSON>, English actor (b. 1945)", "html": "2020 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actor (b. 1945)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actor (b. 1945)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "2020", "text": "<PERSON>, American politician (b. 1921)", "html": "2020 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American politician (b. 1921)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American politician (b. 1921)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2023", "text": "<PERSON><PERSON>, Filipino actor (b. 1947)", "html": "2023 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Filipino actor (b. 1947)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Filipino actor (b. 1947)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "2023", "text": "<PERSON>, American actor (b. 1958)", "html": "2023 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(actor)\" title=\"<PERSON> (actor)\"><PERSON></a>, American actor (b. 1958)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(actor)\" title=\"<PERSON> (actor)\"><PERSON></a>, American actor (b. 1958)", "links": [{"title": "<PERSON> (actor)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(actor)"}]}, {"year": "2024", "text": "<PERSON>, Russian general (b. 1970)", "html": "2024 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(general)\" title=\"<PERSON> (general)\"><PERSON></a>, Russian general (b. 1970)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(general)\" title=\"<PERSON> (general)\"><PERSON></a>, Russian general (b. 1970)", "links": [{"title": "<PERSON> (general)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(general)"}]}, {"year": "2024", "text": "<PERSON><PERSON>, Spanish film actress (b. 1946)", "html": "2024 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Spanish film actress (b. 1946)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Spanish film actress (b. 1946)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}]}}