{"date": "September 30", "url": "https://wikipedia.org/wiki/September_30", "data": {"Events": [{"year": "489", "text": "The Ostrogoths under <PERSON><PERSON><PERSON> the <PERSON> defeat the forces of Odoacer for the second time.", "html": "489 - The <a href=\"https://wikipedia.org/wiki/Ostrogoths\" title=\"Ostrogoths\">Ostrogoths</a> under <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_the_Great\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON> the Great\"><PERSON><PERSON><PERSON> the Great</a> <a href=\"https://wikipedia.org/wiki/Battle_of_Verona_(489)\" title=\"Battle of Verona (489)\">defeat</a> the forces of <a href=\"https://wikipedia.org/wiki/Odoacer\" title=\"Odoacer\">Odoacer</a> for the second time.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Ostrogoths\" title=\"Ostrogoths\">Ostrogoths</a> under <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_the_Great\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON> the Great\"><PERSON><PERSON><PERSON> the Great</a> <a href=\"https://wikipedia.org/wiki/Battle_of_Verona_(489)\" title=\"Battle of Verona (489)\">defeat</a> the forces of <a href=\"https://wikipedia.org/wiki/Odoacer\" title=\"Odoacer\">Odoacer</a> for the second time.", "links": [{"title": "Ostrogoths", "link": "https://wikipedia.org/wiki/Ostrogoths"}, {"title": "Theo<PERSON><PERSON> the Great", "link": "https://wikipedia.org/wiki/Theoder<PERSON>_the_Great"}, {"title": "Battle of Verona (489)", "link": "https://wikipedia.org/wiki/Battle_of_Verona_(489)"}, {"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Odoacer"}]}, {"year": "737", "text": "The Turgesh drive back an Umayyad invasion of Khuttal, follow them south of the Oxus, and capture their baggage train.", "html": "737 - The Turgesh <a href=\"https://wikipedia.org/wiki/Battle_of_the_Baggage\" title=\"Battle of the Baggage\">drive back</a> an Umayyad invasion of Khuttal, follow them south of the Oxus, and capture their baggage train.", "no_year_html": "The Turgesh <a href=\"https://wikipedia.org/wiki/Battle_of_the_Baggage\" title=\"Battle of the Baggage\">drive back</a> an Umayyad invasion of Khuttal, follow them south of the Oxus, and capture their baggage train.", "links": [{"title": "Battle of the Baggage", "link": "https://wikipedia.org/wiki/Battle_of_the_Baggage"}]}, {"year": "1139", "text": "A magnitude 7.7 earthquake strikes the Caucasus mountains in the Seljuk Empire, causing mass destruction and killing up to 300,000 people.", "html": "1139 - A <a href=\"https://wikipedia.org/wiki/1139_Ganja_earthquake\" title=\"1139 Ganja earthquake\">magnitude 7.7 earthquake</a> strikes the <a href=\"https://wikipedia.org/wiki/Caucasus\" title=\"Caucasus\">Caucasus</a> mountains in the <a href=\"https://wikipedia.org/wiki/Seljuk_Empire\" title=\"Seljuk Empire\">Seljuk Empire</a>, causing mass destruction and killing up to 300,000 people.", "no_year_html": "A <a href=\"https://wikipedia.org/wiki/1139_Ganja_earthquake\" title=\"1139 Ganja earthquake\">magnitude 7.7 earthquake</a> strikes the <a href=\"https://wikipedia.org/wiki/Caucasus\" title=\"Caucasus\">Caucasus</a> mountains in the <a href=\"https://wikipedia.org/wiki/Seljuk_Empire\" title=\"Seljuk Empire\">Seljuk Empire</a>, causing mass destruction and killing up to 300,000 people.", "links": [{"title": "1139 Ganja earthquake", "link": "https://wikipedia.org/wiki/1139_Ganja_earthquake"}, {"title": "Caucasus", "link": "https://wikipedia.org/wiki/Caucasus"}, {"title": "Seljuk Empire", "link": "https://wikipedia.org/wiki/Seljuk_Empire"}]}, {"year": "1342", "text": "Battle of Morlaix in the Hundred Years' War", "html": "1342 - <a href=\"https://wikipedia.org/wiki/Battle_of_Morlaix\" title=\"Battle of Morlaix\">Battle of Morlaix</a> in the <a href=\"https://wikipedia.org/wiki/Hundred_Years%27_War\" title=\"Hundred Years' War\">Hundred Years' War</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Battle_of_Morlaix\" title=\"Battle of Morlaix\">Battle of Morlaix</a> in the <a href=\"https://wikipedia.org/wiki/Hundred_Years%27_War\" title=\"Hundred Years' War\">Hundred Years' War</a>", "links": [{"title": "Battle of Morlaix", "link": "https://wikipedia.org/wiki/Battle_of_Morlaix"}, {"title": "Hundred Years' War", "link": "https://wikipedia.org/wiki/Hundred_Years%27_War"}]}, {"year": "1399", "text": "<PERSON> is proclaimed king of England.", "html": "1399 - <a href=\"https://wikipedia.org/wiki/<PERSON>_IV_of_England\" title=\"<PERSON> IV of England\"><PERSON> IV</a> is proclaimed king of England.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_IV_of_England\" title=\"<PERSON> IV of England\"><PERSON> IV</a> is proclaimed king of England.", "links": [{"title": "<PERSON> of England", "link": "https://wikipedia.org/wiki/<PERSON>_IV_of_England"}]}, {"year": "1520", "text": "Suleiman the Magnificent is proclaimed sultan of the Ottoman Empire.", "html": "1520 - <a href=\"https://wikipedia.org/wiki/Suleim<PERSON>_the_Magnificent\" title=\"<PERSON><PERSON><PERSON> the Magnificent\"><PERSON><PERSON><PERSON> the Magnificent</a> is proclaimed sultan of the Ottoman Empire.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Suleim<PERSON>_the_Magnificent\" title=\"<PERSON><PERSON><PERSON> the Magnificent\"><PERSON><PERSON><PERSON> the Magnificent</a> is proclaimed sultan of the Ottoman Empire.", "links": [{"title": "Sul<PERSON><PERSON> the Magnificent", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_the_Magnificent"}]}, {"year": "1541", "text": "Spanish conquistador <PERSON><PERSON><PERSON> and his forces enter Tula territory in present-day western Arkansas, encountering fierce resistance.", "html": "1541 - Spanish conquistador <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a> and his forces enter <a href=\"https://wikipedia.org/wiki/Tula_people\" title=\"Tula people\">Tula</a> territory in present-day western Arkansas, encountering fierce resistance.", "no_year_html": "Spanish conquistador <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a> and his forces enter <a href=\"https://wikipedia.org/wiki/Tula_people\" title=\"Tula people\">Tula</a> territory in present-day western Arkansas, encountering fierce resistance.", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>"}, {"title": "Tula people", "link": "https://wikipedia.org/wiki/Tula_people"}]}, {"year": "1551", "text": "A coup by the military establishment of Japan's Ōuchi clan forces their lord to commit suicide, and their city is burned.", "html": "1551 - <a href=\"https://wikipedia.org/wiki/Tainei-ji_incident\" title=\"Tainei-ji incident\">A coup</a> by the military establishment of Japan's Ōuchi clan forces their lord to commit suicide, and their city is burned.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Tainei-ji_incident\" title=\"Tainei-ji incident\">A coup</a> by the military establishment of Japan's Ōuchi clan forces their lord to commit suicide, and their city is burned.", "links": [{"title": "Tainei-ji incident", "link": "https://wikipedia.org/wiki/Tainei-ji_incident"}]}, {"year": "1744", "text": "War of the Austrian Succession: France and Spain defeat Sardinia at the Battle of Madonna dell'Olmo, but soon have to withdraw from Sardinia anyway.", "html": "1744 - War of the Austrian Succession: France and Spain defeat Sardinia at the <a href=\"https://wikipedia.org/wiki/Battle_of_Madonna_dell%27Olmo\" title=\"Battle of Madonna dell'Olmo\">Battle of Madonna dell'Olmo</a>, but soon have to withdraw from Sardinia anyway.", "no_year_html": "War of the Austrian Succession: France and Spain defeat Sardinia at the <a href=\"https://wikipedia.org/wiki/Battle_of_Madonna_dell%27Olmo\" title=\"Battle of Madonna dell'Olmo\">Battle of Madonna dell'Olmo</a>, but soon have to withdraw from Sardinia anyway.", "links": [{"title": "Battle of Madonna dell'Olmo", "link": "https://wikipedia.org/wiki/Battle_of_Madonna_dell%27Olmo"}]}, {"year": "1791", "text": "The first performance of <PERSON>'s opera The Magic Flute takes place two months before his death.", "html": "1791 - The first performance of <PERSON>'s opera <i><a href=\"https://wikipedia.org/wiki/The_Magic_Flute\" title=\"The Magic Flute\">The Magic Flute</a></i> takes place two months before his death.", "no_year_html": "The first performance of <PERSON>'s opera <i><a href=\"https://wikipedia.org/wiki/The_Magic_Flute\" title=\"The Magic Flute\">The Magic Flute</a></i> takes place two months before his death.", "links": [{"title": "The Magic Flute", "link": "https://wikipedia.org/wiki/The_Magic_Flute"}]}, {"year": "1791", "text": "France's National Constituent Assembly is dissolved, to be replaced the next day by the National Legislative Assembly.", "html": "1791 - France's <a href=\"https://wikipedia.org/wiki/National_Constituent_Assembly_(France)\" title=\"National Constituent Assembly (France)\">National Constituent Assembly</a> is dissolved, to be replaced the next day by the <a href=\"https://wikipedia.org/wiki/National_Legislative_Assembly_(France)\" class=\"mw-redirect\" title=\"National Legislative Assembly (France)\">National Legislative Assembly</a>.", "no_year_html": "France's <a href=\"https://wikipedia.org/wiki/National_Constituent_Assembly_(France)\" title=\"National Constituent Assembly (France)\">National Constituent Assembly</a> is dissolved, to be replaced the next day by the <a href=\"https://wikipedia.org/wiki/National_Legislative_Assembly_(France)\" class=\"mw-redirect\" title=\"National Legislative Assembly (France)\">National Legislative Assembly</a>.", "links": [{"title": "National Constituent Assembly (France)", "link": "https://wikipedia.org/wiki/National_Constituent_Assembly_(France)"}, {"title": "National Legislative Assembly (France)", "link": "https://wikipedia.org/wiki/National_Legislative_Assembly_(France)"}]}, {"year": "1863", "text": "<PERSON>'s opera Les pêcheurs de perles, premiered in Paris.", "html": "1863 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>'s opera <i><a href=\"https://wikipedia.org/wiki/Les_p%C3%AAcheurs_de_perles\" title=\"Les pêcheurs de perles\">Les pêcheurs de perles</a></i>, premiered in <a href=\"https://wikipedia.org/wiki/Paris\" title=\"Paris\">Paris</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>'s opera <i><a href=\"https://wikipedia.org/wiki/Les_p%C3%AAcheurs_de_perles\" title=\"Les pêcheurs de perles\">Les pêcheurs de perles</a></i>, premiered in <a href=\"https://wikipedia.org/wiki/Paris\" title=\"Paris\">Paris</a>.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Les pêcheurs de perles", "link": "https://wikipedia.org/wiki/Les_p%C3%<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>les"}, {"title": "Paris", "link": "https://wikipedia.org/wiki/Paris"}]}, {"year": "1882", "text": "Thomas Edison's first commercial hydroelectric power plant (later known as Appleton Edison Light Company) begins operation.", "html": "1882 - <a href=\"https://wikipedia.org/wiki/Thomas_Edison\" title=\"Thomas Edison\">Thomas Edison</a>'s first commercial hydroelectric power plant (later known as <a href=\"https://wikipedia.org/wiki/Vulcan_Street_Plant\" title=\"Vulcan Street Plant\">Appleton Edison Light Company</a>) begins operation.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Thomas_Edison\" title=\"Thomas Edison\">Thomas Edison</a>'s first commercial hydroelectric power plant (later known as <a href=\"https://wikipedia.org/wiki/Vulcan_Street_Plant\" title=\"Vulcan Street Plant\">Appleton Edison Light Company</a>) begins operation.", "links": [{"title": "Thomas <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Vulcan Street Plant", "link": "https://wikipedia.org/wiki/Vulcan_Street_Plant"}]}, {"year": "1888", "text": "<PERSON> the Ripper kills his third and fourth victims, <PERSON> and <PERSON>.", "html": "1888 - <a href=\"https://wikipedia.org/wiki/<PERSON>_the_Ripper\" title=\"<PERSON> the Ripper\"><PERSON> the Ripper</a> kills his third and fourth victims, <PERSON> and <PERSON>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_the_Ripper\" title=\"<PERSON> the Ripper\"><PERSON> the Ripper</a> kills his third and fourth victims, <PERSON> and <PERSON>.", "links": [{"title": "<PERSON> the Rip<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON><PERSON>"}]}, {"year": "1906", "text": "The Royal Galician Academy, the Galician language's biggest linguistic authority, starts working in La Coruña, Spain.", "html": "1906 - The <a href=\"https://wikipedia.org/wiki/Royal_Galician_Academy\" title=\"Royal Galician Academy\">Royal Galician Academy</a>, the Galician language's biggest linguistic authority, starts working in La Coruña, Spain.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Royal_Galician_Academy\" title=\"Royal Galician Academy\">Royal Galician Academy</a>, the Galician language's biggest linguistic authority, starts working in La Coruña, Spain.", "links": [{"title": "Royal Galician Academy", "link": "https://wikipedia.org/wiki/Royal_Galician_Academy"}]}, {"year": "1907", "text": "The McKinley National Memorial, the final resting place of assassinated U.S. President <PERSON> and his family, is dedicated in Canton, Ohio.", "html": "1907 - The <a href=\"https://wikipedia.org/wiki/McKinley_National_Memorial\" title=\"McKinley National Memorial\">McKinley National Memorial</a>, the final resting place of assassinated U.S. President <PERSON> and his family, is dedicated in Canton, Ohio.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/McKinley_National_Memorial\" title=\"McKinley National Memorial\">McKinley National Memorial</a>, the final resting place of assassinated U.S. President <PERSON> and his family, is dedicated in Canton, Ohio.", "links": [{"title": "McKinley National Memorial", "link": "https://wikipedia.org/wiki/McKinley_National_Memorial"}]}, {"year": "1909", "text": "The Cunard Line's RMS Mauretania makes a record-breaking westbound crossing of the Atlantic, that will not be bettered for 20 years.", "html": "1909 - The Cunard Line's <a href=\"https://wikipedia.org/wiki/RMS_Mauretania_(1906)\" title=\"RMS Mauretania (1906)\">RMS <i>Mauretania</i></a> makes a record-breaking westbound crossing of the Atlantic, that will not be bettered for 20 years.", "no_year_html": "The Cunard Line's <a href=\"https://wikipedia.org/wiki/RMS_Mauretania_(1906)\" title=\"RMS Mauretania (1906)\">RMS <i>Mauretania</i></a> makes a record-breaking westbound crossing of the Atlantic, that will not be bettered for 20 years.", "links": [{"title": "RMS Mauretania (1906)", "link": "https://wikipedia.org/wiki/RMS_Mauretania_(1906)"}]}, {"year": "1915", "text": "World War I: <PERSON><PERSON><PERSON> becomes the first soldier in history to shoot down an enemy aircraft with ground-to-air fire.", "html": "1915 - <a href=\"https://wikipedia.org/wiki/World_War_I\" title=\"World War I\">World War I</a>: <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a> becomes the first soldier in history to shoot down an enemy aircraft with ground-to-air fire.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/World_War_I\" title=\"World War I\">World War I</a>: <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a> becomes the first soldier in history to shoot down an enemy aircraft with ground-to-air fire.", "links": [{"title": "World War I", "link": "https://wikipedia.org/wiki/World_War_I"}, {"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1918", "text": "Ukrainian War of Independence: Insurgent forces led by <PERSON><PERSON><PERSON> defeat the Central Powers at the battle of Dibrivka.", "html": "1918 - <a href=\"https://wikipedia.org/wiki/Ukrainian_War_of_Independence\" title=\"Ukrainian War of Independence\">Ukrainian War of Independence</a>: <a href=\"https://wikipedia.org/wiki/Revolutionary_Insurgent_Army_of_Ukraine\" title=\"Revolutionary Insurgent Army of Ukraine\">Insurgent forces</a> led by <a href=\"https://wikipedia.org/wiki/Nestor_Makhno\" title=\"Nestor Makhno\">N<PERSON><PERSON></a> defeat the <a href=\"https://wikipedia.org/wiki/Central_Powers\" title=\"Central Powers\">Central Powers</a> at the <a href=\"https://wikipedia.org/wiki/Battle_of_Dibrivka\" title=\"Battle of Dibrivka\">battle of Dibrivka</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Ukrainian_War_of_Independence\" title=\"Ukrainian War of Independence\">Ukrainian War of Independence</a>: <a href=\"https://wikipedia.org/wiki/Revolutionary_Insurgent_Army_of_Ukraine\" title=\"Revolutionary Insurgent Army of Ukraine\">Insurgent forces</a> led by <a href=\"https://wikipedia.org/wiki/Nestor_Makhno\" title=\"Nestor Makhno\">Nest<PERSON></a> defeat the <a href=\"https://wikipedia.org/wiki/Central_Powers\" title=\"Central Powers\">Central Powers</a> at the <a href=\"https://wikipedia.org/wiki/Battle_of_Dibrivka\" title=\"Battle of Dibrivka\">battle of Dibrivka</a>.", "links": [{"title": "Ukrainian War of Independence", "link": "https://wikipedia.org/wiki/Ukrainian_War_of_Independence"}, {"title": "Revolutionary Insurgent Army of Ukraine", "link": "https://wikipedia.org/wiki/Revolutionary_Insurgent_Army_of_Ukraine"}, {"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Nestor_<PERSON><PERSON>"}, {"title": "Central Powers", "link": "https://wikipedia.org/wiki/Central_Powers"}, {"title": "Battle of Dibrivka", "link": "https://wikipedia.org/wiki/Battle_of_Dibrivka"}]}, {"year": "1935", "text": "The Hoover Dam, astride the border between the U.S. states of Arizona and Nevada, is dedicated.", "html": "1935 - The <a href=\"https://wikipedia.org/wiki/Hoover_Dam\" title=\"Hoover Dam\">Hoover Dam</a>, astride the border between the U.S. states of Arizona and Nevada, is dedicated.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Hoover_Dam\" title=\"Hoover Dam\">Hoover Dam</a>, astride the border between the U.S. states of Arizona and Nevada, is dedicated.", "links": [{"title": "Hoover Dam", "link": "https://wikipedia.org/wiki/Hoover_Dam"}]}, {"year": "1938", "text": "Britain, France, Germany and Italy sign the Munich Agreement, whereby Germany annexes the Sudetenland region of Czechoslovakia.", "html": "1938 - Britain, France, Germany and Italy sign the <a href=\"https://wikipedia.org/wiki/Munich_Agreement\" title=\"Munich Agreement\">Munich Agreement</a>, whereby Germany annexes the Sudetenland region of Czechoslovakia.", "no_year_html": "Britain, France, Germany and Italy sign the <a href=\"https://wikipedia.org/wiki/Munich_Agreement\" title=\"Munich Agreement\">Munich Agreement</a>, whereby Germany annexes the Sudetenland region of Czechoslovakia.", "links": [{"title": "Munich Agreement", "link": "https://wikipedia.org/wiki/Munich_Agreement"}]}, {"year": "1938", "text": "The League of Nations unanimously outlaws \"intentional bombings of civilian populations\".", "html": "1938 - The <a href=\"https://wikipedia.org/wiki/League_of_Nations\" title=\"League of Nations\">League of Nations</a> unanimously outlaws \"intentional bombings of civilian populations\".", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/League_of_Nations\" title=\"League of Nations\">League of Nations</a> unanimously outlaws \"intentional bombings of civilian populations\".", "links": [{"title": "League of Nations", "link": "https://wikipedia.org/wiki/League_of_Nations"}]}, {"year": "1939", "text": "World War II: General <PERSON><PERSON><PERSON><PERSON> becomes prime minister of the Polish government-in-exile.", "html": "1939 - <a href=\"https://wikipedia.org/wiki/World_War_II\" title=\"World War II\">World War II</a>: General <a href=\"https://wikipedia.org/wiki/W%C5%82adys%C5%82aw_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\">W<PERSON><PERSON><PERSON></a> becomes prime minister of the Polish government-in-exile.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/World_War_II\" title=\"World War II\">World War II</a>: General <a href=\"https://wikipedia.org/wiki/W%C5%82adys%C5%82aw_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\">W<PERSON><PERSON><PERSON></a> becomes prime minister of the Polish government-in-exile.", "links": [{"title": "World War II", "link": "https://wikipedia.org/wiki/World_War_II"}, {"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/W%C5%82adys%C5%82aw_<PERSON><PERSON><PERSON>"}]}, {"year": "1939", "text": "NBC broadcasts the first televised American football game.", "html": "1939 - NBC broadcasts the <a href=\"https://wikipedia.org/wiki/First_televised_American_football_game\" class=\"mw-redirect\" title=\"First televised American football game\">first televised American football game</a>.", "no_year_html": "NBC broadcasts the <a href=\"https://wikipedia.org/wiki/First_televised_American_football_game\" class=\"mw-redirect\" title=\"First televised American football game\">first televised American football game</a>.", "links": [{"title": "First televised American football game", "link": "https://wikipedia.org/wiki/First_televised_American_football_game"}]}, {"year": "1941", "text": "World War II: The Babi Yar massacre comes to an end.", "html": "1941 - World War II: The <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_Yar\" title=\"Babi Yar\"><PERSON>bi Yar</a> massacre comes to an end.", "no_year_html": "World War II: The <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_Yar\" title=\"Babi Yar\"><PERSON><PERSON> Yar</a> massacre comes to an end.", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_Yar"}]}, {"year": "1943", "text": "The United States Merchant Marine Academy is dedicated by President <PERSON>.", "html": "1943 - The <a href=\"https://wikipedia.org/wiki/United_States_Merchant_Marine_Academy\" title=\"United States Merchant Marine Academy\">United States Merchant Marine Academy</a> is dedicated by President <PERSON>.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/United_States_Merchant_Marine_Academy\" title=\"United States Merchant Marine Academy\">United States Merchant Marine Academy</a> is dedicated by President <PERSON>.", "links": [{"title": "United States Merchant Marine Academy", "link": "https://wikipedia.org/wiki/United_States_Merchant_Marine_Academy"}]}, {"year": "1944", "text": "The Germans commence a counter offensive to retake the Nijmegen salient, this having been captured by the allies during Operation Market Garden.", "html": "1944 - The Germans commence a <a href=\"https://wikipedia.org/wiki/Battle_of_the_Nijmegen_salient\" title=\"Battle of the Nijmegen salient\">counter offensive to retake the Nijmegen salient</a>, this having been captured by the allies during <a href=\"https://wikipedia.org/wiki/Operation_Market_Garden\" title=\"Operation Market Garden\">Operation Market Garden</a>.", "no_year_html": "The Germans commence a <a href=\"https://wikipedia.org/wiki/Battle_of_the_Nijmegen_salient\" title=\"Battle of the Nijmegen salient\">counter offensive to retake the Nijmegen salient</a>, this having been captured by the allies during <a href=\"https://wikipedia.org/wiki/Operation_Market_Garden\" title=\"Operation Market Garden\">Operation Market Garden</a>.", "links": [{"title": "Battle of the Nijmegen salient", "link": "https://wikipedia.org/wiki/Battle_of_the_Nijmegen_salient"}, {"title": "Operation Market Garden", "link": "https://wikipedia.org/wiki/Operation_Market_Garden"}]}, {"year": "1945", "text": "The Bourne End rail crash, in Hertfordshire, England, kills 43.", "html": "1945 - The <a href=\"https://wikipedia.org/wiki/Bourne_End_rail_crash\" title=\"Bourne End rail crash\">Bourne End rail crash</a>, in Hertfordshire, England, kills 43.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Bourne_End_rail_crash\" title=\"Bourne End rail crash\">Bourne End rail crash</a>, in Hertfordshire, England, kills 43.", "links": [{"title": "Bourne End rail crash", "link": "https://wikipedia.org/wiki/Bourne_End_rail_crash"}]}, {"year": "1947", "text": "The 1947 World Series begins. It is the first to be televised, to include an African-American player, to exceed $2 million in receipts, to see a pinch-hit home run, and to have six umpires on the field.", "html": "1947 - The <a href=\"https://wikipedia.org/wiki/1947_World_Series\" title=\"1947 World Series\">1947 World Series</a> begins. It is the first to be televised, to include an African-American player, to exceed $2 million in receipts, to see a pinch-hit home run, and to have six umpires on the field.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/1947_World_Series\" title=\"1947 World Series\">1947 World Series</a> begins. It is the first to be televised, to include an African-American player, to exceed $2 million in receipts, to see a pinch-hit home run, and to have six umpires on the field.", "links": [{"title": "1947 World Series", "link": "https://wikipedia.org/wiki/1947_World_Series"}]}, {"year": "1947", "text": "Pakistan joins the United Nations.", "html": "1947 - <a href=\"https://wikipedia.org/wiki/Pakistan\" title=\"Pakistan\">Pakistan</a> joins the United Nations.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Pakistan\" title=\"Pakistan\">Pakistan</a> joins the United Nations.", "links": [{"title": "Pakistan", "link": "https://wikipedia.org/wiki/Pakistan"}]}, {"year": "1949", "text": "The Berlin Airlift ends.", "html": "1949 - The <a href=\"https://wikipedia.org/wiki/Berlin_Blockade#End_of_the_blockade\" title=\"Berlin Blockade\">Berlin Airlift</a> ends.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Berlin_Blockade#End_of_the_blockade\" title=\"Berlin Blockade\">Berlin Airlift</a> ends.", "links": [{"title": "Berlin Blockade", "link": "https://wikipedia.org/wiki/Berlin_Blockade#End_of_the_blockade"}]}, {"year": "1954", "text": "The U.S. Navy submarine USS Nautilus is commissioned as the world's first nuclear-powered vessel.", "html": "1954 - The U.S. Navy submarine <a href=\"https://wikipedia.org/wiki/USS_Nautilus_(SSN-571)\" title=\"USS Nautilus (SSN-571)\">USS <i>Nautilus</i></a> is commissioned as the world's first nuclear-powered vessel.", "no_year_html": "The U.S. Navy submarine <a href=\"https://wikipedia.org/wiki/USS_Nautilus_(SSN-571)\" title=\"USS Nautilus (SSN-571)\">USS <i>Nautilus</i></a> is commissioned as the world's first nuclear-powered vessel.", "links": [{"title": "USS Nautilus (SSN-571)", "link": "https://wikipedia.org/wiki/USS_Nautilus_(SSN-571)"}]}, {"year": "1965", "text": "Six Indonesian Army generals were assassinated by the September 30 Movement. The PKI was blamed for the latter, resulting in mass killings of suspected leftists.", "html": "1965 - Six <a href=\"https://wikipedia.org/wiki/Indonesian_National_Armed_Forces\" title=\"Indonesian National Armed Forces\">Indonesian Army</a> generals were assassinated by the <a href=\"https://wikipedia.org/wiki/30_September_Movement\" title=\"30 September Movement\">September 30 Movement</a>. The <a href=\"https://wikipedia.org/wiki/Communist_Party_of_Indonesia\" title=\"Communist Party of Indonesia\">PKI</a> was blamed for the latter, resulting in <a href=\"https://wikipedia.org/wiki/Indonesian_mass_killings_of_1965%E2%80%9366\" title=\"Indonesian mass killings of 1965-66\">mass killings of suspected leftists</a>.", "no_year_html": "Six <a href=\"https://wikipedia.org/wiki/Indonesian_National_Armed_Forces\" title=\"Indonesian National Armed Forces\">Indonesian Army</a> generals were assassinated by the <a href=\"https://wikipedia.org/wiki/30_September_Movement\" title=\"30 September Movement\">September 30 Movement</a>. The <a href=\"https://wikipedia.org/wiki/Communist_Party_of_Indonesia\" title=\"Communist Party of Indonesia\">PKI</a> was blamed for the latter, resulting in <a href=\"https://wikipedia.org/wiki/Indonesian_mass_killings_of_1965%E2%80%9366\" title=\"Indonesian mass killings of 1965-66\">mass killings of suspected leftists</a>.", "links": [{"title": "Indonesian National Armed Forces", "link": "https://wikipedia.org/wiki/Indonesian_National_Armed_Forces"}, {"title": "30 September Movement", "link": "https://wikipedia.org/wiki/30_September_Movement"}, {"title": "Communist Party of Indonesia", "link": "https://wikipedia.org/wiki/Communist_Party_of_Indonesia"}, {"title": "Indonesian mass killings of 1965-66", "link": "https://wikipedia.org/wiki/Indonesian_mass_killings_of_1965%E2%80%9366"}]}, {"year": "1966", "text": "Bechuanaland declares its independence, and becomes the Republic of Botswana.", "html": "1966 - Bechuanaland declares its independence, and becomes the <a href=\"https://wikipedia.org/wiki/Botswana\" title=\"Botswana\">Republic of Botswana</a>.", "no_year_html": "Bechuanaland declares its independence, and becomes the <a href=\"https://wikipedia.org/wiki/Botswana\" title=\"Botswana\">Republic of Botswana</a>.", "links": [{"title": "Botswana", "link": "https://wikipedia.org/wiki/Botswana"}]}, {"year": "1968", "text": "The Boeing 747 is rolled out and shown to the public for the first time.", "html": "1968 - The <a href=\"https://wikipedia.org/wiki/Boeing_747\" title=\"Boeing 747\">Boeing 747</a> is rolled out and shown to the public for the first time.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Boeing_747\" title=\"Boeing 747\">Boeing 747</a> is rolled out and shown to the public for the first time.", "links": [{"title": "Boeing 747", "link": "https://wikipedia.org/wiki/Boeing_747"}]}, {"year": "1970", "text": "<PERSON> makes a deal with the PFLP for the release of the remaining hostages from the Dawson's Field hijackings.", "html": "1970 - <PERSON> makes a deal with the PFLP for the release of the remaining hostages from the <a href=\"https://wikipedia.org/wiki/Dawson%27s_Field_hijackings\" title=\"Dawson's Field hijackings\"><PERSON>'s Field hijackings</a>.", "no_year_html": "<PERSON> makes a deal with the PFLP for the release of the remaining hostages from the <a href=\"https://wikipedia.org/wiki/Dawson%27s_Field_hijackings\" title=\"Dawson's Field hijackings\"><PERSON>'s Field hijackings</a>.", "links": [{"title": "Dawson's Field hijackings", "link": "https://wikipedia.org/wiki/Dawson%27s_Field_hijackings"}]}, {"year": "1975", "text": "Malév Flight 240 crashes into the Mediterranean Sea while on approach to Beirut International Airport in Beirut, Lebanon, killing 60.", "html": "1975 - <a href=\"https://wikipedia.org/wiki/Mal%C3%A9v_Flight_240\" title=\"Malév Flight 240\">Malév Flight 240</a> crashes into the <a href=\"https://wikipedia.org/wiki/Mediterranean_Sea\" title=\"Mediterranean Sea\">Mediterranean Sea</a> while on approach to <a href=\"https://wikipedia.org/wiki/Beirut%E2%80%93Rafic_Hariri_International_Airport\" title=\"Beirut-Rafic Hariri International Airport\">Beirut International Airport</a> in <a href=\"https://wikipedia.org/wiki/Beirut\" title=\"Beirut\">Beirut</a>, <a href=\"https://wikipedia.org/wiki/Lebanon\" title=\"Lebanon\">Lebanon</a>, killing 60.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Mal%C3%A9v_Flight_240\" title=\"Malév Flight 240\">Malév Flight 240</a> crashes into the <a href=\"https://wikipedia.org/wiki/Mediterranean_Sea\" title=\"Mediterranean Sea\">Mediterranean Sea</a> while on approach to <a href=\"https://wikipedia.org/wiki/Beirut%E2%80%93Rafic_Hariri_International_Airport\" title=\"Beirut-Rafic Hariri International Airport\">Beirut International Airport</a> in <a href=\"https://wikipedia.org/wiki/Beirut\" title=\"Beirut\">Beirut</a>, <a href=\"https://wikipedia.org/wiki/Lebanon\" title=\"Lebanon\">Lebanon</a>, killing 60.", "links": [{"title": "Malév Flight 240", "link": "https://wikipedia.org/wiki/Mal%C3%A9v_Flight_240"}, {"title": "Mediterranean Sea", "link": "https://wikipedia.org/wiki/Mediterranean_Sea"}, {"title": "Beirut-Rafic <PERSON> International Airport", "link": "https://wikipedia.org/wiki/Beirut%E2%80%93Rafic_Hariri_International_Airport"}, {"title": "Beirut", "link": "https://wikipedia.org/wiki/Beirut"}, {"title": "Lebanon", "link": "https://wikipedia.org/wiki/Lebanon"}]}, {"year": "1978", "text": "Finnair Flight 405 is hijacked by <PERSON><PERSON><PERSON> in Oulu, Finland.", "html": "1978 - <a href=\"https://wikipedia.org/wiki/Finnair_Flight_405\" title=\"Finnair Flight 405\">Finnair Flight 405</a> is hijacked by <PERSON><PERSON><PERSON> in <a href=\"https://wikipedia.org/wiki/Oulu\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, <a href=\"https://wikipedia.org/wiki/Finland\" title=\"Finland\">Finland</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Finnair_Flight_405\" title=\"Finnair Flight 405\">Finnair Flight 405</a> is hijacked by <PERSON><PERSON><PERSON> in <a href=\"https://wikipedia.org/wiki/Oulu\" title=\"<PERSON>ulu\"><PERSON><PERSON></a>, <a href=\"https://wikipedia.org/wiki/Finland\" title=\"Finland\">Finland</a>.", "links": [{"title": "Finnair Flight 405", "link": "https://wikipedia.org/wiki/Finnair_Flight_405"}, {"title": "Oulu", "link": "https://wikipedia.org/wiki/Oulu"}, {"title": "Finland", "link": "https://wikipedia.org/wiki/Finland"}]}, {"year": "1980", "text": "Ethernet specifications are published by Xerox working with Intel and Digital Equipment Corporation.", "html": "1980 - <a href=\"https://wikipedia.org/wiki/Ethernet\" title=\"Ethernet\">Ethernet</a> specifications are published by Xerox working with Intel and Digital Equipment Corporation.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Ethernet\" title=\"Ethernet\">Ethernet</a> specifications are published by Xerox working with Intel and Digital Equipment Corporation.", "links": [{"title": "Ethernet", "link": "https://wikipedia.org/wiki/Ethernet"}]}, {"year": "1993", "text": "The 6.2 Mw  Latur earthquake shakes Maharashtra, India with a maximum Mercalli intensity of VIII (Severe) killing 9,748 and injuring 30,000.", "html": "1993 - The 6.2 M<sub>w</sub>  <a href=\"https://wikipedia.org/wiki/1993_Latur_earthquake\" title=\"1993 Latur earthquake\">Latur earthquake</a> shakes Maharashtra, India with a maximum <a href=\"https://wikipedia.org/wiki/Modified_Mercalli_intensity_scale\" title=\"Modified Mercalli intensity scale\">Mercalli intensity</a> of VIII (<i>Severe</i>) killing 9,748 and injuring 30,000.", "no_year_html": "The 6.2 M<sub>w</sub>  <a href=\"https://wikipedia.org/wiki/1993_Latur_earthquake\" title=\"1993 Latur earthquake\">Latur earthquake</a> shakes Maharashtra, India with a maximum <a href=\"https://wikipedia.org/wiki/Modified_Mercalli_intensity_scale\" title=\"Modified Mercalli intensity scale\">Mercalli intensity</a> of VIII (<i>Severe</i>) killing 9,748 and injuring 30,000.", "links": [{"title": "1993 Latur earthquake", "link": "https://wikipedia.org/wiki/1993_Latur_earthquake"}, {"title": "Modified Mercalli intensity scale", "link": "https://wikipedia.org/wiki/Modified_Mercalli_intensity_scale"}]}, {"year": "1994", "text": "Space Shuttle Endeavour is launched on STS-68.", "html": "1994 - <a href=\"https://wikipedia.org/wiki/Space_Shuttle_Endeavour\" title=\"Space Shuttle Endeavour\">Space Shuttle <i>Endeavour</i></a> is launched on <a href=\"https://wikipedia.org/wiki/STS-68\" title=\"STS-68\">STS-68</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Space_Shuttle_Endeavour\" title=\"Space Shuttle Endeavour\">Space Shuttle <i>Endeavour</i></a> is launched on <a href=\"https://wikipedia.org/wiki/STS-68\" title=\"STS-68\">STS-68</a>.", "links": [{"title": "Space Shuttle Endeavour", "link": "https://wikipedia.org/wiki/Space_Shuttle_Endeavour"}, {"title": "STS-68", "link": "https://wikipedia.org/wiki/STS-68"}]}, {"year": "1999", "text": "The Tokaimura nuclear accident causes the deaths of two technicians in Japan's second-worst nuclear accident.", "html": "1999 - The <a href=\"https://wikipedia.org/wiki/Tokaimura_nuclear_accidents\" title=\"Tokaimura nuclear accidents\">Tokaimura nuclear accident</a> causes the deaths of two technicians in Japan's second-worst nuclear accident.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Tokaimura_nuclear_accidents\" title=\"Tokaimura nuclear accidents\">Tokaimura nuclear accident</a> causes the deaths of two technicians in Japan's second-worst nuclear accident.", "links": [{"title": "Tokaimura nuclear accidents", "link": "https://wikipedia.org/wiki/Tokaimura_nuclear_accidents"}]}, {"year": "2000", "text": "Israeli-Palestinian conflict: Twelve-year-old <PERSON> is shot and killed on the second day of the Second Intifada.", "html": "2000 - <a href=\"https://wikipedia.org/wiki/Israeli%E2%80%93Palestinian_conflict\" title=\"Israeli-Palestinian conflict\">Israeli-Palestinian conflict</a>: Twelve-year-old <a href=\"https://wikipedia.org/wiki/Killing_of_<PERSON>_<PERSON>\" title=\"Killing of <PERSON>\"><PERSON></a> is shot and killed on the second day of the <a href=\"https://wikipedia.org/wiki/Second_Intifada\" title=\"Second Intifada\">Second Intifada</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Israeli%E2%80%93Palestinian_conflict\" title=\"Israeli-Palestinian conflict\">Israeli-Palestinian conflict</a>: Twelve-year-old <a href=\"https://wikipedia.org/wiki/Killing_of_<PERSON>_<PERSON>\" title=\"Killing of <PERSON>\"><PERSON></a> is shot and killed on the second day of the <a href=\"https://wikipedia.org/wiki/Second_Intifada\" title=\"Second Intifada\">Second Intifada</a>.", "links": [{"title": "Israeli-Palestinian conflict", "link": "https://wikipedia.org/wiki/Israeli%E2%80%93Palestinian_conflict"}, {"title": "Killing of <PERSON>", "link": "https://wikipedia.org/wiki/Killing_of_<PERSON>_<PERSON>"}, {"title": "Second Intifada", "link": "https://wikipedia.org/wiki/Second_Intifada"}]}, {"year": "2005", "text": "Controversial drawings of <PERSON> are printed in a Danish newspaper.", "html": "2005 - <a href=\"https://wikipedia.org/wiki/Jyllands-Posten_Muhammad_cartoons_controversy\" title=\"Jyllands-Posten Muhammad cartoons controversy\">Controversial drawings of <PERSON></a> are printed in a Danish newspaper.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Jyllands-Posten_Muhammad_cartoons_controversy\" title=\"Jyllands-Posten Muhammad cartoons controversy\">Controversial drawings of <PERSON></a> are printed in a Danish newspaper.", "links": [{"title": "Jyllands-<PERSON><PERSON> cartoons controversy", "link": "https://wikipedia.org/wiki/Jyllands-<PERSON><PERSON>_Muhammad_cartoons_controversy"}]}, {"year": "2009", "text": "The 7.6 Mw  Sumatra earthquake leaves 1,115 people dead.", "html": "2009 - The 7.6 M<sub>w</sub>  <a href=\"https://wikipedia.org/wiki/2009_Sumatra_earthquakes\" title=\"2009 Sumatra earthquakes\">Sumatra earthquake</a> leaves 1,115 people dead.", "no_year_html": "The 7.6 M<sub>w</sub>  <a href=\"https://wikipedia.org/wiki/2009_Sumatra_earthquakes\" title=\"2009 Sumatra earthquakes\">Sumatra earthquake</a> leaves 1,115 people dead.", "links": [{"title": "2009 Sumatra earthquakes", "link": "https://wikipedia.org/wiki/2009_Sumatra_earthquakes"}]}, {"year": "2016", "text": "Hurricane <PERSON> becomes a Category 5 hurricane, making it the strongest hurricane to form in the Caribbean Sea since 2007.", "html": "2016 - <a href=\"https://wikipedia.org/wiki/Hurricane_Matthew\" title=\"Hurricane Matthew\">Hurricane <PERSON></a> becomes a Category 5 hurricane, making it the strongest hurricane to form in the Caribbean Sea since 2007.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Hurricane_Matthew\" title=\"Hurricane Matthew\">Hurricane <PERSON></a> becomes a Category 5 hurricane, making it the strongest hurricane to form in the Caribbean Sea since 2007.", "links": [{"title": "Hurricane Matthew", "link": "https://wikipedia.org/wiki/Hurricane_Matthew"}]}, {"year": "2016", "text": "Two paintings with a combined value of $100 million are recovered after having been stolen from the Van Gogh Museum in 2002.", "html": "2016 - Two paintings with a combined value of $100 million are recovered after having been <a href=\"https://wikipedia.org/wiki/Van_Gogh_Museum#Art_thefts\" title=\"Van Gogh Museum\">stolen from the Van Gogh Museum</a> in 2002.", "no_year_html": "Two paintings with a combined value of $100 million are recovered after having been <a href=\"https://wikipedia.org/wiki/Van_Gogh_Museum#Art_thefts\" title=\"Van Gogh Museum\">stolen from the Van Gogh Museum</a> in 2002.", "links": [{"title": "Van <PERSON> Museum", "link": "https://wikipedia.org/wiki/Van_Gogh_Museum#Art_thefts"}]}], "Births": [{"year": "1207", "text": "<PERSON><PERSON>, Persian mystic and poet (d. 1273)[citation needed]", "html": "1207 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Persian mystic and poet (d. 1273)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Persian mystic and poet (d. 1273)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Rumi"}]}, {"year": "1227", "text": "<PERSON> (d. 1292)", "html": "1227 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_IV\" title=\"Pope Nicholas IV\">Pope <PERSON> IV</a> (d. 1292)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_IV\" title=\"Pope Nicholas IV\">Pope <PERSON> IV</a> (d. 1292)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1530", "text": "<PERSON><PERSON><PERSON>, Italian philologist and physician (d. 1606)", "html": "1530 - <a href=\"https://wikipedia.org/wiki/Girolamo_Mercuriale\" title=\"Girolamo Mercuriale\"><PERSON><PERSON><PERSON></a>, Italian philologist and physician (d. 1606)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Girolamo_Mercuriale\" title=\"Girolamo Mercuriale\"><PERSON><PERSON><PERSON></a>, Italian philologist and physician (d. 1606)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Girolamo_Mercuriale"}]}, {"year": "1550", "text": "<PERSON>, German astronomer and mathematician (d. 1631)", "html": "1550 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German astronomer and mathematician (d. 1631)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German astronomer and mathematician (d. 1631)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1622", "text": "<PERSON>, German composer (d. 1683)", "html": "1622 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German composer (d. 1683)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German composer (d. 1683)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1689", "text": "<PERSON>, French violinist and composer (d. 1753)", "html": "1689 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French violinist and composer (d. 1753)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French violinist and composer (d. 1753)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1700", "text": "<PERSON><PERSON>, Polish monk, poet, and playwright (d. 1773)", "html": "1700 - <a href=\"https://wikipedia.org/wiki/Stanis%C5%82aw_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Polish monk, poet, and playwright (d. 1773)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Stanis%C5%82aw_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Polish monk, poet, and playwright (d. 1773)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Stanis%C5%82aw_<PERSON><PERSON><PERSON>"}]}, {"year": "1710", "text": "<PERSON>, 4th Duke of Bedford, English politician, Lord President of the Council (d. 1771)", "html": "1710 - <a href=\"https://wikipedia.org/wiki/<PERSON>,_4th_Duke_<PERSON>_Bedford\" title=\"<PERSON>, 4th Duke of Bedford\"><PERSON>, 4th Duke of Bedford</a>, English politician, <a href=\"https://wikipedia.org/wiki/Lord_President_of_the_Council\" title=\"Lord President of the Council\">Lord President of the Council</a> (d. 1771)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>,_4th_Duke_<PERSON>_Bedford\" title=\"<PERSON>, 4th Duke of Bedford\"><PERSON>, 4th Duke of Bedford</a>, English politician, <a href=\"https://wikipedia.org/wiki/Lord_President_of_the_Council\" title=\"Lord President of the Council\">Lord President of the Council</a> (d. 1771)", "links": [{"title": "<PERSON>, 4th Duke of Bedford", "link": "https://wikipedia.org/wiki/<PERSON>,_4th_Duke_<PERSON>_Bedford"}, {"title": "Lord President of the Council", "link": "https://wikipedia.org/wiki/Lord_President_of_the_Council"}]}, {"year": "1714", "text": "<PERSON>, French epistemologist and philosopher (d. 1780)", "html": "1714 - <a href=\"https://wikipedia.org/wiki/%C3%89tien<PERSON>_<PERSON>_<PERSON>_<PERSON>di<PERSON>\" title=\"<PERSON> Condillac\"><PERSON></a>, French epistemologist and philosopher (d. 1780)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/%C3%89tien<PERSON>_<PERSON>_<PERSON>_<PERSON>dillac\" title=\"<PERSON> Condillac\"><PERSON></a>, French epistemologist and philosopher (d. 1780)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/%C3%89tien<PERSON>_<PERSON><PERSON>_de_Condillac"}]}, {"year": "1732", "text": "<PERSON>, Swiss-French politician, Prime Minister of France (d. 1804)", "html": "1732 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swiss-French politician, <a href=\"https://wikipedia.org/wiki/List_of_Prime_Ministers_of_France\" class=\"mw-redirect\" title=\"List of Prime Ministers of France\">Prime Minister of France</a> (d. 1804)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swiss-French politician, <a href=\"https://wikipedia.org/wiki/List_of_Prime_Ministers_of_France\" class=\"mw-redirect\" title=\"List of Prime Ministers of France\">Prime Minister of France</a> (d. 1804)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}, {"title": "List of Prime Ministers of France", "link": "https://wikipedia.org/wiki/List_of_Prime_Ministers_of_France"}]}, {"year": "1743", "text": "<PERSON>, German cantor and composer (d. 1813)", "html": "1743 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German cantor and composer (d. 1813)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German cantor and composer (d. 1813)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON><PERSON><PERSON>_<PERSON>g"}]}, {"year": "1765", "text": "<PERSON>, Mexican priest and general (d. 1815)", "html": "1765 - <a href=\"https://wikipedia.org/wiki/Jos%C3%A9_Mar%C3%AD<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Mexican priest and general (d. 1815)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Jos%C3%A9_Mar%C3%AD<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Mexican priest and general (d. 1815)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Jos%C3%A9_Mar%C3%ADa_Morelos"}]}, {"year": "1800", "text": "<PERSON><PERSON>, English architect, designed the Pharos Lighthouse (d. 1881)", "html": "1800 - <a href=\"https://wikipedia.org/wiki/Decimus_Burton\" title=\"Decimus Burton\"><PERSON><PERSON></a>, English architect, designed the <a href=\"https://wikipedia.org/wiki/Pharos_Lighthouse_(Fleetwood)\" class=\"mw-redirect\" title=\"Pharos Lighthouse (Fleetwood)\">Pharos Lighthouse</a> (d. 1881)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Decimus_Burton\" title=\"<PERSON><PERSON> Burton\"><PERSON><PERSON></a>, English architect, designed the <a href=\"https://wikipedia.org/wiki/Pharos_Lighthouse_(Fleetwood)\" class=\"mw-redirect\" title=\"Pharos Lighthouse (Fleetwood)\">Pharos Lighthouse</a> (d. 1881)", "links": [{"title": "Dec<PERSON>", "link": "https://wikipedia.org/wiki/Decimus_Burton"}, {"title": "Pharos Lighthouse (Fleetwood)", "link": "https://wikipedia.org/wiki/Pharos_Lighthouse_(Fleetwood)"}]}, {"year": "1813", "text": "<PERSON>, Scottish physician and explorer (d. 1893)", "html": "1813 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>(explorer)\" title=\"<PERSON> (explorer)\"><PERSON></a>, Scottish physician and explorer (d. 1893)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>(explorer)\" title=\"<PERSON> (explorer)\"><PERSON></a>, Scottish physician and explorer (d. 1893)", "links": [{"title": "<PERSON> (explorer)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>(explorer)"}]}, {"year": "1814", "text": "<PERSON><PERSON>, American feminist, educator, and philanthropist (d. 1900)", "html": "1814 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_Stone\" title=\"<PERSON>inda Hi<PERSON>dale Stone\"><PERSON><PERSON></a>, American feminist, educator, and philanthropist (d. 1900)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_Stone\" title=\"<PERSON><PERSON> Stone\"><PERSON><PERSON></a>, American feminist, educator, and philanthropist (d. 1900)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>ns<PERSON>_Stone"}]}, {"year": "1827", "text": "<PERSON>, American journalist and politician, 20th Treasurer of the United States (d. 1918)", "html": "1827 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American journalist and politician, 20th <a href=\"https://wikipedia.org/wiki/Treasurer_of_the_United_States\" title=\"Treasurer of the United States\">Treasurer of the United States</a> (d. 1918)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American journalist and politician, 20th <a href=\"https://wikipedia.org/wiki/Treasurer_of_the_United_States\" title=\"Treasurer of the United States\">Treasurer of the United States</a> (d. 1918)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "Treasurer of the United States", "link": "https://wikipedia.org/wiki/Treasurer_of_the_United_States"}]}, {"year": "1832", "text": "<PERSON>, American activist, co-founded Mother's Day (d. 1905)", "html": "1832 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American activist, co-founded <a href=\"https://wikipedia.org/wiki/Mother%27s_Day_(United_States)\" title=\"Mother's Day (United States)\">Mother's Day</a> (d. 1905)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American activist, co-founded <a href=\"https://wikipedia.org/wiki/Mother%27s_Day_(United_States)\" title=\"Mother's Day (United States)\">Mother's Day</a> (d. 1905)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Mother's Day (United States)", "link": "https://wikipedia.org/wiki/Mother%27s_Day_(United_States)"}]}, {"year": "1836", "text": "<PERSON><PERSON><PERSON>, Peruvian politician, 56th President of Peru (d. 1894)", "html": "1836 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>%C3%BAdez\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Peruvian politician, 56th <a href=\"https://wikipedia.org/wiki/President_of_Peru\" title=\"President of Peru\">President of Peru</a> (d. 1894)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>%C3%BAdez\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Peruvian politician, 56th <a href=\"https://wikipedia.org/wiki/President_of_Peru\" title=\"President of Peru\">President of Peru</a> (d. 1894)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_Morales_Berm%C3%BAdez"}, {"title": "President of Peru", "link": "https://wikipedia.org/wiki/President_of_Peru"}]}, {"year": "1852", "text": "<PERSON>, Irish composer, conductor, and educator (d. 1924)", "html": "1852 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Irish composer, conductor, and educator (d. 1924)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Irish composer, conductor, and educator (d. 1924)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1861", "text": "<PERSON>, Jr., American businessman, founded Wrigley Company (d. 1932)", "html": "1861 - <a href=\"https://wikipedia.org/wiki/<PERSON>,_Jr.\" class=\"mw-redirect\" title=\"<PERSON>, Jr.\"><PERSON>, Jr.</a>, American businessman, founded <a href=\"https://wikipedia.org/wiki/Wrigley_Company\" title=\"Wrigley Company\">Wrigley Company</a> (d. 1932)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>,_Jr.\" class=\"mw-redirect\" title=\"<PERSON>, Jr.\"><PERSON>, Jr.</a>, American businessman, founded <a href=\"https://wikipedia.org/wiki/Wrigley_Company\" title=\"Wrigley Company\">Wrigley Company</a> (d. 1932)", "links": [{"title": "<PERSON>, Jr.", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Jr."}, {"title": "Wrigley Company", "link": "https://wikipedia.org/wiki/Wrigley_Company"}]}, {"year": "1863", "text": "<PERSON><PERSON><PERSON>, German admiral (d. 1928)", "html": "1863 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, German admiral (d. 1928)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, German admiral (d. 1928)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1870", "text": "<PERSON>, American banker and philanthropist (d. 1948)", "html": "1870 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American banker and philanthropist (d. 1948)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American banker and philanthropist (d. 1948)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "1870", "text": "<PERSON>, French-American physicist and chemist, Nobel Prize laureate (d. 1942)", "html": "1870 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French-American physicist and chemist, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Physics\" title=\"Nobel Prize in Physics\">Nobel Prize</a> laureate (d. 1942)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French-American physicist and chemist, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Physics\" title=\"Nobel Prize in Physics\">Nobel Prize</a> laureate (d. 1942)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "Nobel Prize in Physics", "link": "https://wikipedia.org/wiki/Nobel_Prize_in_Physics"}]}, {"year": "1882", "text": "<PERSON>, German physicist and academic (d. 1945)", "html": "1882 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German physicist and academic (d. 1945)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German physicist and academic (d. 1945)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1883", "text": "<PERSON>, German educator and politician (d. 1945)", "html": "1883 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German educator and politician (d. 1945)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German educator and politician (d. 1945)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1883", "text": "<PERSON>, American civil engineer, architect, and suffragist (d. 1971)", "html": "1883 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, American civil engineer, architect, and suffragist (d. 1971)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>ch Barney\"><PERSON></a>, American civil engineer, architect, and suffragist (d. 1971)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1887", "text": "<PERSON>, Indonesian-German actress (d. 1980)", "html": "1887 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Indonesian-German actress (d. 1980)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Indonesian-German actress (d. 1980)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Lil_<PERSON>gover"}]}, {"year": "1893", "text": "<PERSON><PERSON><PERSON>, American lieutenant, lawyer, and politician (d. 1964)", "html": "1893 - <a href=\"https://wikipedia.org/wiki/Lansdale_Ghiselin_Sasscer\" title=\"Lansdale Ghiselin Sasscer\">Lansdale Ghiselin <PERSON></a>, American lieutenant, lawyer, and politician (d. 1964)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Lansdale_Ghiselin_Sasscer\" title=\"Lansdale Ghiselin Sasscer\">Lansdale Ghiselin Sasscer</a>, American lieutenant, lawyer, and politician (d. 1964)", "links": [{"title": "Lansdale Ghiselin <PERSON>", "link": "https://wikipedia.org/wiki/Lansdale_<PERSON><PERSON><PERSON>_<PERSON>cer"}]}, {"year": "1895", "text": "<PERSON>, Moldovan-American director, producer, and screenwriter (d. 1980)", "html": "1895 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Moldovan-American director, producer, and screenwriter (d. 1980)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Moldovan-American director, producer, and screenwriter (d. 1980)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1897", "text": "<PERSON>, Russian-English soldier and politician (d. 1966)", "html": "1897 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian-English soldier and politician (d. 1966)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian-English soldier and politician (d. 1966)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1897", "text": "<PERSON>, German-English physician and psychotherapist (d. 1986)", "html": "1897 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German-English physician and psychotherapist (d. 1986)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German-English physician and psychotherapist (d. 1986)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1898", "text": "<PERSON><PERSON>, French-American actress (d. 1933)", "html": "1898 - <a href=\"https://wikipedia.org/wiki/Ren%C3%A9e_Ador%C3%A9e\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, French-American actress (d. 1933)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Ren%C3%A9e_Ador%C3%A9e\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, French-American actress (d. 1933)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Ren%C3%A9e_Ador%C3%A9e"}]}, {"year": "1898", "text": "Princess <PERSON>, Duchess of Valentinois (d. 1977)", "html": "1898 - <a href=\"https://wikipedia.org/wiki/Princess_<PERSON>,_Duchess_of_Valentinois\" title=\"Princess <PERSON>, Duchess of Valentinois\">Princess <PERSON>, Duchess of Valentinois</a> (d. 1977)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Princess_<PERSON>,_Duchess_of_Valentinois\" title=\"Princess <PERSON>, Duchess of Valentinois\">Princess <PERSON>, Duchess of Valentinois</a> (d. 1977)", "links": [{"title": "Princess <PERSON>, Duchess of Valentinois", "link": "https://wikipedia.org/wiki/Princess_<PERSON>,_Duchess_of_Vale<PERSON>is"}]}, {"year": "1898", "text": "<PERSON>, German-American author and illustrator (d. 1986)", "html": "1898 - <a href=\"https://wikipedia.org/wiki/<PERSON>g<PERSON>_and_<PERSON>_<PERSON>_<PERSON>%27Aulaire\" title=\"<PERSON><PERSON><PERSON> and <PERSON>\"><PERSON></a>, German-American author and illustrator (d. 1986)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_and_<PERSON>_<PERSON>_<PERSON>%27Aulaire\" title=\"<PERSON><PERSON><PERSON> and <PERSON>\"><PERSON></a>, German-American author and illustrator (d. 1986)", "links": [{"title": "<PERSON><PERSON><PERSON> and <PERSON>", "link": "https://wikipedia.org/wiki/In<PERSON><PERSON>_and_<PERSON>_<PERSON>_<PERSON>%27Aulaire"}]}, {"year": "1901", "text": "<PERSON><PERSON>, American bassist and bandleader (d. 1966)", "html": "1901 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American bassist and bandleader (d. 1966)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American bassist and bandleader (d. 1966)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1904", "text": "<PERSON><PERSON>, Welsh poet and academic (d. 1971)", "html": "1904 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Welsh poet and academic (d. 1971)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Welsh poet and academic (d. 1971)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1905", "text": "<PERSON><PERSON><PERSON>, English physicist and academic, Nobel Prize laureate (d. 1996)", "html": "1905 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"Nevill <PERSON>\"><PERSON><PERSON><PERSON></a>, English physicist and academic, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Physics\" title=\"Nobel Prize in Physics\">Nobel Prize</a> laureate (d. 1996)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"Nevill <PERSON>\"><PERSON><PERSON><PERSON></a>, English physicist and academic, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Physics\" title=\"Nobel Prize in Physics\">Nobel Prize</a> laureate (d. 1996)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Nev<PERSON>_<PERSON>_<PERSON>"}, {"title": "Nobel Prize in Physics", "link": "https://wikipedia.org/wiki/Nobel_Prize_in_Physics"}]}, {"year": "1905", "text": "<PERSON>, English director, producer, and screenwriter (d. 1990)", "html": "1905 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English director, producer, and screenwriter (d. 1990)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English director, producer, and screenwriter (d. 1990)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1906", "text": "<PERSON><PERSON><PERSON>, French singer-songwriter and actress (d. 1996)", "html": "1906 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, French singer-songwriter and actress (d. 1996)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, French singer-songwriter and actress (d. 1996)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1908", "text": "<PERSON>, Ukrainian-Russian violinist and educator (d. 1974)", "html": "1908 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Ukrainian-Russian violinist and educator (d. 1974)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Ukrainian-Russian violinist and educator (d. 1974)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1910", "text": "<PERSON><PERSON>, Finnish captain (d. 1962)", "html": "1910 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Finnish captain (d. 1962)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Finnish captain (d. 1962)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1911", "text": "<PERSON><PERSON>, American psychologist (d. 1977)", "html": "1911 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American psychologist (d. 1977)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American psychologist (d. 1977)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1912", "text": "<PERSON>, American singer and actor (d. 1985)", "html": "1912 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(American_performer)\" title=\"<PERSON> (American performer)\"><PERSON></a>, American singer and actor (d. 1985)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(American_performer)\" title=\"<PERSON> (American performer)\"><PERSON></a>, American singer and actor (d. 1985)", "links": [{"title": "<PERSON> (American performer)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(American_performer)"}]}, {"year": "1913", "text": "<PERSON>, American screenwriter and producer (d. 1975)", "html": "1913 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(producer)\" title=\"<PERSON> (producer)\"><PERSON></a>, American screenwriter and producer (d. 1975)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(producer)\" title=\"<PERSON> (producer)\"><PERSON></a>, American screenwriter and producer (d. 1975)", "links": [{"title": "<PERSON> (producer)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>(producer)"}]}, {"year": "1915", "text": "<PERSON>, American businessman and politician, 75th Governor of Georgia (d. 2003)", "html": "1915 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American businessman and politician, 75th <a href=\"https://wikipedia.org/wiki/Governor_of_Georgia\" title=\"Governor of Georgia\">Governor of Georgia</a> (d. 2003)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American businessman and politician, 75th <a href=\"https://wikipedia.org/wiki/Governor_of_Georgia\" title=\"Governor of Georgia\">Governor of Georgia</a> (d. 2003)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Governor of Georgia", "link": "https://wikipedia.org/wiki/Governor_of_Georgia"}]}, {"year": "1917", "text": "<PERSON>, Russian actor and director (d. 2014)", "html": "1917 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian actor and director (d. 2014)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian actor and director (d. 2014)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1917", "text": "<PERSON>, American drummer, bandleader, and actor (d. 1987)", "html": "1917 - <a href=\"https://wikipedia.org/wiki/<PERSON>_Rich\" title=\"<PERSON> Rich\"><PERSON></a>, American drummer, bandleader, and actor (d. 1987)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_Rich\" title=\"<PERSON> Rich\"><PERSON></a>, American drummer, bandleader, and actor (d. 1987)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1918", "text": "<PERSON>, U.S. Army captain (d. 1995)", "html": "1918 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, U.S. Army captain (d. 1995)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, U.S. Army captain (d. 1995)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1918", "text": "<PERSON>, French historian and economist (d. 2007)", "html": "1918 - <a href=\"https://wikipedia.org/wiki/Ren%C3%A9_R%C3%A9mond\" title=\"<PERSON>\"><PERSON></a>, French historian and economist (d. 2007)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Ren%C3%A9_R%C3%A9<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French historian and economist (d. 2007)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Ren%C3%A9_R%C3%A9mond"}]}, {"year": "1919", "text": "<PERSON>, Argentinian race car driver (d. 1992)", "html": "1919 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Argentinian race car driver (d. 1992)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Argentinian race car driver (d. 1992)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1919", "text": "<PERSON>, Ukrainian-Russian violinist and educator (d. 2008)", "html": "1919 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Ukrainian-Russian violinist and educator (d. 2008)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Ukrainian-Russian violinist and educator (d. 2008)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1919", "text": "<PERSON>, American lieutenant and politician, 26th Governor of North Dakota (d. 2013)", "html": "1919 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lieutenant and politician, 26th <a href=\"https://wikipedia.org/wiki/Governor_of_North_Dakota\" title=\"Governor of North Dakota\">Governor of North Dakota</a> (d. 2013)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lieutenant and politician, 26th <a href=\"https://wikipedia.org/wiki/Governor_of_North_Dakota\" title=\"Governor of North Dakota\">Governor of North Dakota</a> (d. 2013)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "Governor of North Dakota", "link": "https://wikipedia.org/wiki/Governor_of_North_Dakota"}]}, {"year": "1919", "text": "<PERSON>, American soprano and actress (d. 2012)", "html": "1919 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American soprano and actress (d. 2012)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American soprano and actress (d. 2012)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1921", "text": "<PERSON>, Scottish-English actress (d. 2007)", "html": "1921 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish-English actress (d. 2007)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish-English actress (d. 2007)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1921", "text": "<PERSON><PERSON>, Brazilian-American cellist and educator (d. 2018)", "html": "1921 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Brazilian-American cellist and educator (d. 2018)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>do_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Brazilian-American cellist and educator (d. 2018)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Aldo_Parisot"}]}, {"year": "1922", "text": "<PERSON><PERSON>, American actor, director, and producer (d. 2010)", "html": "1922 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American actor, director, and producer (d. 2010)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American actor, director, and producer (d. 2010)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1922", "text": "<PERSON><PERSON><PERSON><PERSON><PERSON>, Indian director, producer, and screenwriter (d. 2006)", "html": "1922 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON><PERSON></a>, Indian director, producer, and screenwriter (d. 2006)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON><PERSON></a>, Indian director, producer, and screenwriter (d. 2006)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1923", "text": "<PERSON>, Welsh-English pianist and composer  (d. 1994)", "html": "1923 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Welsh-English pianist and composer (d. 1994)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Welsh-English pianist and composer (d. 1994)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1924", "text": "<PERSON>, American novelist, playwright, and screenwriter (d. 1984)", "html": "1924 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American novelist, playwright, and screenwriter (d. 1984)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American novelist, playwright, and screenwriter (d. 1984)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1925", "text": "<PERSON><PERSON>, Russian engineer and scientist in the former Soviet space program (d. 1998)", "html": "1925 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Russian engineer and <a href=\"https://wikipedia.org/wiki/Scientist\" title=\"Scientist\">scientist</a> in the former <a href=\"https://wikipedia.org/wiki/Soviet_space_program\" title=\"Soviet space program\">Soviet space program</a> (d. 1998)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Russian engineer and <a href=\"https://wikipedia.org/wiki/Scientist\" title=\"Scientist\">scientist</a> in the former <a href=\"https://wikipedia.org/wiki/Soviet_space_program\" title=\"Soviet space program\">Soviet space program</a> (d. 1998)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}, {"title": "Scientist", "link": "https://wikipedia.org/wiki/Scientist"}, {"title": "Soviet space program", "link": "https://wikipedia.org/wiki/Soviet_space_program"}]}, {"year": "1926", "text": "<PERSON><PERSON>, Estonian basketball player and coach (d. 2012)", "html": "1926 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Estonian basketball player and coach (d. 2012)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Estonian basketball player and coach (d. 2012)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1926", "text": "<PERSON>, American baseball player, coach, and sportscaster (d. 2010)", "html": "1926 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(baseball)\" title=\"<PERSON> (baseball)\"><PERSON></a>, American baseball player, coach, and sportscaster (d. 2010)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(baseball)\" title=\"<PERSON> (baseball)\"><PERSON></a>, American baseball player, coach, and sportscaster (d. 2010)", "links": [{"title": "<PERSON> (baseball)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(baseball)"}]}, {"year": "1927", "text": "<PERSON><PERSON> <PERSON><PERSON>, American poet and translator (d. 2019)", "html": "1927 - <a href=\"https://wikipedia.org/wiki/W._<PERSON><PERSON>_<PERSON><PERSON><PERSON>\" title=\"W. S. Merwin\"><PERSON><PERSON> <PERSON><PERSON> <PERSON></a>, American poet and translator (d. 2019)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/W._<PERSON><PERSON>_<PERSON><PERSON><PERSON>\" title=\"W. S. Merwin\"><PERSON><PERSON> <PERSON><PERSON></a>, American poet and translator (d. 2019)", "links": [{"title": "<PERSON><PERSON> <PERSON><PERSON>", "link": "https://wikipedia.org/wiki/W._<PERSON><PERSON>_<PERSON><PERSON>win"}]}, {"year": "1928", "text": "<PERSON><PERSON>, Romanian-American author, academic, and activist, Nobel Prize laureate (d. 2016)", "html": "1928 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Romanian-American author, academic, and activist, <a href=\"https://wikipedia.org/wiki/Nobel_Peace_Prize\" title=\"Nobel Peace Prize\">Nobel Prize</a> laureate (d. 2016)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Romanian-American author, academic, and activist, <a href=\"https://wikipedia.org/wiki/Nobel_Peace_Prize\" title=\"Nobel Peace Prize\">Nobel Prize</a> laureate (d. 2016)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}, {"title": "Nobel Peace Prize", "link": "https://wikipedia.org/wiki/Nobel_Peace_Prize"}]}, {"year": "1928", "text": "<PERSON>, Canadian-American football player and coach (d. 2013)", "html": "1928 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian-American football player and coach (d. 2013)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian-American football player and coach (d. 2013)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1929", "text": "<PERSON>, American author and illustrator (d. 2002)", "html": "1929 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author and illustrator (d. 2002)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author and illustrator (d. 2002)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1929", "text": "<PERSON><PERSON><PERSON>, Greek seismologist and academic (d. 2022)", "html": "1929 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Greek seismologist and academic (d. 2022)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Greek seismologist and academic (d. 2022)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1929", "text": "<PERSON><PERSON><PERSON>, Filipino politician, diplomat and writer (d. 2017)", "html": "1929 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Filipino politician, diplomat and writer (d. 2017)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Filipino politician, diplomat and writer (d. 2017)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Let<PERSON><PERSON>_<PERSON>"}]}, {"year": "1929", "text": "<PERSON><PERSON><PERSON>, German theologian and author (d. 2003)", "html": "1929 - <a href=\"https://wikipedia.org/wiki/Do<PERSON>ee_S%C3%B6lle\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, German theologian and author (d. 2003)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_S%C3%B6lle\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, German theologian and author (d. 2003)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Dorothee_S%C3%B6lle"}]}, {"year": "1931", "text": "<PERSON>, American actress", "html": "1931 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1931", "text": "<PERSON>, English educator and politician (d. 2015)", "html": "1931 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English educator and politician (d. 2015)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English educator and politician (d. 2015)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1932", "text": "<PERSON><PERSON><PERSON>, Japanese author, playwright, and politician, Governor of Tokyo (d. 2022)", "html": "1932 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese author, playwright, and politician, <a href=\"https://wikipedia.org/wiki/Governor_of_Tokyo\" title=\"Governor of Tokyo\">Governor of Tokyo</a> (d. 2022)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese author, playwright, and politician, <a href=\"https://wikipedia.org/wiki/Governor_of_Tokyo\" title=\"Governor of Tokyo\">Governor of Tokyo</a> (d. 2022)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_Is<PERSON>hara"}, {"title": "Governor of Tokyo", "link": "https://wikipedia.org/wiki/Governor_of_Tokyo"}]}, {"year": "1932", "text": "<PERSON>, American baseball player and coach (d. 2008)", "html": "1932 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player and coach (d. 2008)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player and coach (d. 2008)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1933", "text": "<PERSON><PERSON><PERSON>, American singer (d. 2024)", "html": "1933 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American singer (d. 2024)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American singer (d. 2024)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Cissy_Houston"}]}, {"year": "1934", "text": "<PERSON>, English footballer and manager (d. 2009)", "html": "1934 - <a href=\"https://wikipedia.org/wiki/Alan_A%27Court\" title=\"Alan <PERSON>\"><PERSON></a>, English footballer and manager (d. 2009)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Alan_A%27Court\" title=\"Alan <PERSON>Court\"><PERSON></a>, English footballer and manager (d. 2009)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Alan_A%27Court"}]}, {"year": "1934", "text": "<PERSON><PERSON>, Austrian-Swiss singer-songwriter and pianist (d. 2014)", "html": "1934 - <a href=\"https://wikipedia.org/wiki/Udo_J%C3%BCrgens\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Austrian-Swiss singer-songwriter and pianist (d. 2014)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Udo_J%C3%BCrgens\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Austrian-Swiss singer-songwriter and pianist (d. 2014)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Udo_J%C3%BCrgens"}]}, {"year": "1934", "text": "<PERSON>, Indian-American actress (d. 2015)", "html": "1934 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Indian-American actress (d. 2015)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Indian-American actress (d. 2015)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1935", "text": "<PERSON>, American singer and actor", "html": "1935 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer and actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer and actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1936", "text": "<PERSON>, American lawyer and politician, 6th United States Ambassador to China (d. 2024)", "html": "1936 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and politician, 6th <a href=\"https://wikipedia.org/wiki/United_States_Ambassador_to_China\" class=\"mw-redirect\" title=\"United States Ambassador to China\">United States Ambassador to China</a> (d. 2024)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and politician, 6th <a href=\"https://wikipedia.org/wiki/United_States_Ambassador_to_China\" class=\"mw-redirect\" title=\"United States Ambassador to China\">United States Ambassador to China</a> (d. 2024)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "United States Ambassador to China", "link": "https://wikipedia.org/wiki/United_States_Ambassador_to_China"}]}, {"year": "1936", "text": "<PERSON><PERSON><PERSON>, Turkish author (d. 1976)", "html": "1936 - <a href=\"https://wikipedia.org/wiki/Sev<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Turkish author (d. 1976)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Sev<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Turkish author (d. 1976)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Sevgi_<PERSON>ysal"}]}, {"year": "1937", "text": "<PERSON><PERSON>, Polish-German author (d. 1997)", "html": "1937 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Polish-German author (d. 1997)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Polish-German author (d. 1997)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1937", "text": "<PERSON><PERSON><PERSON>, Ukrainian pianist and composer", "html": "1937 - <a href=\"https://wikipedia.org/wiki/Valentyn_Sylvestrov\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Ukrainian pianist and composer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Valentyn_Sylvestrov\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON>yl<PERSON>trov\"><PERSON><PERSON><PERSON></a>, Ukrainian pianist and composer", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Valentyn_Sylvestrov"}]}, {"year": "1937", "text": "<PERSON>, Rhodesian motorcycle racer (d. 1962)", "html": "1937 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Rhodesian motorcycle racer (d. 1962)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Rhodesian motorcycle racer (d. 1962)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1938", "text": "<PERSON>, English clarinet player and educator (d. 2012)", "html": "1938 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English clarinet player and educator (d. 2012)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English clarinet player and educator (d. 2012)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1939", "text": "<PERSON>, Canadian actor", "html": "1939 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>u"}]}, {"year": "1939", "text": "<PERSON>, English painter and academic (d. 2023)", "html": "1939 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(painter)\" title=\"<PERSON> (painter)\"><PERSON></a>, English painter and academic (d. 2023)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(painter)\" title=\"<PERSON> (painter)\"><PERSON></a>, English painter and academic (d. 2023)", "links": [{"title": "<PERSON> (painter)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(painter)"}]}, {"year": "1939", "text": "<PERSON><PERSON><PERSON>, French chemist and academic, Nobel Prize laureate", "html": "1939 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, French chemist and academic, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Chemistry\" title=\"Nobel Prize in Chemistry\">Nobel Prize</a> laureate", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, French chemist and academic, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Chemistry\" title=\"Nobel Prize in Chemistry\">Nobel Prize</a> laureate", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>"}, {"title": "Nobel Prize in Chemistry", "link": "https://wikipedia.org/wiki/Nobel_Prize_in_Chemistry"}]}, {"year": "1940", "text": "<PERSON>, American philosopher and academic (d. 2015)", "html": "1940 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American philosopher and academic (d. 2015)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Claudia Card\"><PERSON></a>, American philosopher and academic (d. 2015)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1940", "text": "<PERSON>, Canadian sprinter (d. 1982)", "html": "1940 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian sprinter (d. 1982)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian sprinter (d. 1982)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1940", "text": "<PERSON>, Canadian-American drummer (d. 2009)", "html": "1940 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(musician)\" title=\"<PERSON> (musician)\"><PERSON></a>, Canadian-American drummer (d. 2009)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(musician)\" title=\"<PERSON> (musician)\"><PERSON></a>, Canadian-American drummer (d. 2009)", "links": [{"title": "<PERSON> (musician)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>(musician)"}]}, {"year": "1941", "text": "<PERSON>, Jr., American author and educator", "html": "1941 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>,_Jr.\" class=\"mw-redirect\" title=\"<PERSON>, Jr.\"><PERSON>, Jr.</a>, American author and educator", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>,_Jr.\" class=\"mw-redirect\" title=\"<PERSON>, Jr.\"><PERSON>, Jr.</a>, American author and educator", "links": [{"title": "<PERSON>, Jr.", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>,_Jr."}]}, {"year": "1941", "text": "<PERSON><PERSON>, Indian academic and diplomat, 5th Commonwealth Secretary General", "html": "1941 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Indian academic and diplomat, 5th <a href=\"https://wikipedia.org/wiki/Commonwealth_Secretary_General\" class=\"mw-redirect\" title=\"Commonwealth Secretary General\">Commonwealth Secretary General</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Indian academic and diplomat, 5th <a href=\"https://wikipedia.org/wiki/Commonwealth_Secretary_General\" class=\"mw-redirect\" title=\"Commonwealth Secretary General\">Commonwealth Secretary General</a>", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}, {"title": "Commonwealth Secretary General", "link": "https://wikipedia.org/wiki/Commonwealth_Secretary_General"}]}, {"year": "1941", "text": "<PERSON><PERSON>, Swedish race car driver (d. 2022)", "html": "1941 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Swedish race car driver (d. 2022)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Swedish race car driver (d. 2022)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>"}]}, {"year": "1942", "text": "<PERSON>, English record producer (d. 2002)", "html": "1942 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Dudgeon\"><PERSON></a>, English record producer (d. 2002)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Gus Dudgeon\"><PERSON></a>, English record producer (d. 2002)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1942", "text": "<PERSON>, American singer-songwriter (d. 1968)", "html": "1942 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter (d. 1968)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Lymon\"><PERSON></a>, American singer-songwriter (d. 1968)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1943", "text": "<PERSON>, German-American biochemist and biophysicist, Nobel Prize laureate", "html": "1943 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German-American biochemist and biophysicist, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Chemistry\" title=\"Nobel Prize in Chemistry\">Nobel Prize</a> laureate", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German-American biochemist and biophysicist, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Chemistry\" title=\"Nobel Prize in Chemistry\">Nobel Prize</a> laureate", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Nobel Prize in Chemistry", "link": "https://wikipedia.org/wiki/Nobel_Prize_in_Chemistry"}]}, {"year": "1943", "text": "<PERSON>, American singer", "html": "1943 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1943", "text": "<PERSON>, English organist and composer", "html": "1943 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>(organist)\" title=\"<PERSON> (organist)\"><PERSON></a>, English organist and composer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>(organist)\" title=\"<PERSON> (organist)\"><PERSON></a>, English organist and composer", "links": [{"title": "<PERSON> (organist)", "link": "https://wikipedia.org/wiki/<PERSON>(organist)"}]}, {"year": "1943", "text": "<PERSON>, English-American actor, playwright, and author", "html": "1943 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English-American actor, playwright, and author", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English-American actor, playwright, and author", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1944", "text": "<PERSON>, Canadian singer and painter", "html": "1944 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian singer and painter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian singer and painter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1944", "text": "<PERSON>, Scottish footballer (d. 2006)", "html": "1944 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish footballer (d. 2006)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish footballer (d. 2006)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>"}]}, {"year": "1944", "text": "<PERSON>, American basketball player (d. 2009)", "html": "1944 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player (d. 2009)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player (d. 2009)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_Robbins"}]}, {"year": "1945", "text": "<PERSON>, English astronomer and academic (d. 2022)", "html": "1945 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English astronomer and academic (d. 2022)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English astronomer and academic (d. 2022)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1945", "text": "<PERSON><PERSON><PERSON>, Israeli lawyer and politician, 12th Prime Minister of Israel", "html": "1945 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Israeli lawyer and politician, 12th <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Israel\" title=\"Prime Minister of Israel\">Prime Minister of Israel</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Israeli lawyer and politician, 12th <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Israel\" title=\"Prime Minister of Israel\">Prime Minister of Israel</a>", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}, {"title": "Prime Minister of Israel", "link": "https://wikipedia.org/wiki/Prime_Minister_of_Israel"}]}, {"year": "1946", "text": "<PERSON><PERSON>, American actress, singer, and puppeteer", "html": "1946 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American actress, singer, and puppeteer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American actress, singer, and puppeteer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>"}]}, {"year": "1946", "text": "<PERSON><PERSON>, 7th Marquess of Salisbury, English academic and politician, Leader of the House of Lords", "html": "1946 - <a href=\"https://wikipedia.org/wiki/<PERSON>-<PERSON>,_7th_Marquess_of_Salisbury\" title=\"<PERSON>, 7th Marquess of Salisbury\"><PERSON>, 7th Marquess of Salisbury</a>, English academic and politician, <a href=\"https://wikipedia.org/wiki/Leader_of_the_House_of_Lords\" title=\"Leader of the House of Lords\">Leader of the House of Lords</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>-<PERSON>,_7th_Marquess_of_Salisbury\" title=\"<PERSON>, 7th Marquess of Salisbury\"><PERSON>, 7th Marquess of Salisbury</a>, English academic and politician, <a href=\"https://wikipedia.org/wiki/Leader_of_the_House_of_Lords\" title=\"Leader of the House of Lords\">Leader of the House of Lords</a>", "links": [{"title": "<PERSON><PERSON>, 7th Marquess of Salisbury", "link": "https://wikipedia.org/wiki/<PERSON>-<PERSON>,_7th_Marquess_of_Salisbury"}, {"title": "Leader of the House of Lords", "link": "https://wikipedia.org/wiki/Leader_of_the_House_of_Lords"}]}, {"year": "1946", "text": "<PERSON><PERSON><PERSON>, Puerto Rican-American singer-songwriter (d. 1993)", "html": "1946 - <a href=\"https://wikipedia.org/wiki/H%C3%A9<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Puerto Rican-American singer-songwriter (d. 1993)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/H%C3%A9<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Puerto Rican-American singer-songwriter (d. 1993)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/H%C3%A9ctor_<PERSON>voe"}]}, {"year": "1946", "text": "<PERSON><PERSON>, German race car driver", "html": "1946 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, German race car driver", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, German race car driver", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1946", "text": "<PERSON>, Australian cricketer and educator", "html": "1946 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian cricketer and educator", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian cricketer and educator", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1946", "text": "<PERSON>, French journalist, founded Raëlism", "html": "1946 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, French journalist, founded <a href=\"https://wikipedia.org/wiki/Ra%C3%ABlism\" title=\"Raëlism\">Raëlism</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, French journalist, founded <a href=\"https://wikipedia.org/wiki/Ra%C3%ABlism\" title=\"Raëlism\">Raëlism</a>", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Raëlism", "link": "https://wikipedia.org/wiki/Ra%C3%ABlism"}]}, {"year": "1947", "text": "<PERSON>, English singer-songwriter and guitarist (d. 1977)", "html": "1947 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English singer-songwriter and guitarist (d. 1977)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English singer-songwriter and guitarist (d. 1977)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1947", "text": "<PERSON><PERSON>, English actress", "html": "1947 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, English actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, English actress", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_Lenska"}]}, {"year": "1948", "text": "<PERSON>, American baseball player and coach (d. 2006)", "html": "1948 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player and coach (d. 2006)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player and coach (d. 2006)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1949", "text": "<PERSON>, French pilot, engineer, military officer and astronaut", "html": "1949 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French pilot, engineer, military officer and astronaut", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French pilot, engineer, military officer and astronaut", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1950", "text": "<PERSON>, Mexican author and screenwriter", "html": "1950 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Mexican author and screenwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Mexican author and screenwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>vel"}]}, {"year": "1950", "text": "<PERSON>, English actress and dancer", "html": "1950 - <a href=\"https://wikipedia.org/wiki/Victoria_Tennant\" title=\"Victoria Tennant\"><PERSON></a>, English actress and dancer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Victoria_Tennant\" title=\"Victoria Tennant\"><PERSON></a>, English actress and dancer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Victoria_Tennant"}]}, {"year": "1951", "text": "<PERSON>, English screenwriter and producer", "html": "1951 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>(producer)\" title=\"<PERSON> (producer)\"><PERSON></a>, English screenwriter and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>(producer)\" title=\"<PERSON> (producer)\"><PERSON></a>, English screenwriter and producer", "links": [{"title": "<PERSON> (producer)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>(producer)"}]}, {"year": "1951", "text": "<PERSON>, Australian physician and academic, Nobel Prize laureate", "html": "1951 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian physician and academic, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Physiology_or_Medicine\" title=\"Nobel Prize in Physiology or Medicine\">Nobel Prize</a> laureate", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian physician and academic, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Physiology_or_Medicine\" title=\"Nobel Prize in Physiology or Medicine\">Nobel Prize</a> laureate", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Nobel Prize in Physiology or Medicine", "link": "https://wikipedia.org/wiki/Nobel_Prize_in_Physiology_or_Medicine"}]}, {"year": "1951", "text": "<PERSON>, English astrophysicist and academic", "html": "1951 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English astrophysicist and academic", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English astrophysicist and academic", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1952", "text": "<PERSON>, American actor", "html": "1952 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"John Finn\"><PERSON></a>, American actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1952", "text": "<PERSON>, American singer-songwriter and guitarist", "html": "1952 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and guitarist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and guitarist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1953", "text": "<PERSON>, American drummer", "html": "1953 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American drummer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American drummer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>s"}]}, {"year": "1953", "text": "<PERSON>, American country music singer-songwriter, author, and actress", "html": "1953 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American country music singer-songwriter, author, and actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American country music singer-songwriter, author, and actress", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1954", "text": "<PERSON><PERSON>, Polish singer-songwriter and record producer", "html": "1954 - <a href=\"https://wikipedia.org/wiki/Basia\" title=\"Ba<PERSON>\"><PERSON><PERSON></a>, Polish singer-songwriter and record producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Basia\" title=\"Ba<PERSON>\"><PERSON><PERSON></a>, Polish singer-songwriter and record producer", "links": [{"title": "Basia", "link": "https://wikipedia.org/wiki/Basia"}]}, {"year": "1954", "text": "<PERSON>, American basketball player (d. 2022)", "html": "1954 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(basketball)\" title=\"<PERSON> (basketball)\"><PERSON></a>, American basketball player (d. 2022)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(basketball)\" title=\"<PERSON> (basketball)\"><PERSON></a>, American basketball player (d. 2022)", "links": [{"title": "<PERSON> (basketball)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(basketball)"}]}, {"year": "1954", "text": "<PERSON>, American guitarist and composer", "html": "1954 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American guitarist and composer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American guitarist and composer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1954", "text": "<PERSON><PERSON>, American singer-songwriter and producer", "html": "1954 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American singer-songwriter and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American singer-songwriter and producer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>"}]}, {"year": "1954", "text": "<PERSON>, American actor", "html": "1954 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(actor)\" title=\"<PERSON> (actor)\"><PERSON></a>, American actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(actor)\" title=\"<PERSON> (actor)\"><PERSON></a>, American actor", "links": [{"title": "<PERSON> (actor)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(actor)"}]}, {"year": "1955", "text": "<PERSON>, German engineer, co-founded Sun Microsystems", "html": "1955 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German engineer, co-founded <a href=\"https://wikipedia.org/wiki/Sun_Microsystems\" title=\"Sun Microsystems\">Sun Microsystems</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German engineer, co-founded <a href=\"https://wikipedia.org/wiki/Sun_Microsystems\" title=\"Sun Microsystems\">Sun Microsystems</a>", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}, {"title": "Sun Microsystems", "link": "https://wikipedia.org/wiki/Sun_Microsystems"}]}, {"year": "1955", "text": "<PERSON>, Northern Irish flute player  (d. 1994)", "html": "1955 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Northern Irish flute player (d. 1994)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Northern Irish flute player (d. 1994)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1956", "text": "<PERSON>, English footballer and manager", "html": "1956 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(footballer)\" title=\"<PERSON> (footballer)\"><PERSON></a>, English footballer and manager", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>(footballer)\" title=\"<PERSON> (footballer)\"><PERSON></a>, English footballer and manager", "links": [{"title": "<PERSON> (footballer)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>(footballer)"}]}, {"year": "1957", "text": "<PERSON><PERSON>, American actress, producer, and screenwriter", "html": "1957 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American actress, producer, and screenwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American actress, producer, and screenwriter", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1958", "text": "<PERSON>, American singer-songwriter and guitarist", "html": "1958 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and guitarist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and guitarist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1959", "text": "<PERSON><PERSON><PERSON>, Italian basketball player and coach", "html": "1959 - <a href=\"https://wikipedia.org/wiki/Ettore_Messina\" title=\"Ettore Messina\"><PERSON><PERSON><PERSON></a>, Italian basketball player and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Ettore_Me<PERSON>\" title=\"Ettore Messina\"><PERSON><PERSON><PERSON></a>, Italian basketball player and coach", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/E<PERSON>re_<PERSON>a"}]}, {"year": "1960", "text": "<PERSON>, Canadian-English keyboard player, composer, and producer", "html": "1960 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian-English keyboard player, composer, and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian-English keyboard player, composer, and producer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1960", "text": "<PERSON>, English-American author", "html": "1960 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English-American author", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English-American author", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1960", "text": "<PERSON><PERSON>, American singer-songwriter, producer, and actress", "html": "1960 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American singer-songwriter, producer, and actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American singer-songwriter, producer, and actress", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1960", "text": "<PERSON>, American politician", "html": "1960 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American politician", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1961", "text": "<PERSON>, American actress and singer-songwriter", "html": "1961 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Bernard\"><PERSON></a>, American actress and singer-songwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Bernard\"><PERSON></a>, American actress and singer-songwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_Bernard"}]}, {"year": "1961", "text": "<PERSON>, Australian rugby league player", "html": "1961 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian rugby league player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian rugby league player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1961", "text": "<PERSON>, American actor, director, and producer", "html": "1961 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor, director, and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor, director, and producer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1961", "text": "<PERSON>, English politician", "html": "1961 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Mel Stride\"><PERSON></a>, English politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_Stride\" title=\"Mel Stride\"><PERSON></a>, English politician", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Mel_Stride"}]}, {"year": "1961", "text": "<PERSON>, Belgian race car driver", "html": "1961 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Belgian race car driver", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Belgian race car driver", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1962", "text": "<PERSON>, American record producer and rapper", "html": "1962 - <a href=\"https://wikipedia.org/wiki/<PERSON>_Mar<PERSON>\" title=\"<PERSON> Marl\"><PERSON></a>, American record producer and rapper", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Marl\"><PERSON></a>, American record producer and rapper", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>l"}]}, {"year": "1963", "text": "<PERSON>, American bass player and producer", "html": "1963 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American bass player and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American bass player and producer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1964", "text": "<PERSON>, American singer-songwriter, guitarist, and composer", "html": "1964 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter, guitarist, and composer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Anasta<PERSON>\"><PERSON></a>, American singer-songwriter, guitarist, and composer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_Anastasio"}]}, {"year": "1964", "text": "<PERSON>, Italian actress and fashion model", "html": "1964 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian actress and fashion model", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian actress and fashion model", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1965", "text": "<PERSON><PERSON><PERSON>, English comedian, actor, and producer", "html": "1965 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, English comedian, actor, and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, English comedian, actor, and producer", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1966", "text": "<PERSON>, Scottish rugby player", "html": "1966 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(rugby)\" class=\"mw-redirect\" title=\"<PERSON> (rugby)\"><PERSON></a>, Scottish rugby player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(rugby)\" class=\"mw-redirect\" title=\"<PERSON> (rugby)\"><PERSON></a>, Scottish rugby player", "links": [{"title": "<PERSON> (rugby)", "link": "https://wikipedia.org/wiki/<PERSON>_(rugby)"}]}, {"year": "1966", "text": "<PERSON>, German pianist, composer, and educator", "html": "1966 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German pianist, composer, and educator", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German pianist, composer, and educator", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1966", "text": "<PERSON>, Australian politician", "html": "1966 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(politician)\" title=\"<PERSON> (politician)\"><PERSON></a>, Australian politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(politician)\" title=\"<PERSON> (politician)\"><PERSON></a>, Australian politician", "links": [{"title": "<PERSON> (politician)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(politician)"}]}, {"year": "1967", "text": "<PERSON><PERSON>, Swiss-French author and illustrator", "html": "1967 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Swiss-French author and illustrator", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Swiss-French author and illustrator", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1967", "text": "<PERSON><PERSON><PERSON>, American musician, radio personality and painter ", "html": "1967 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American musician, radio personality and painter ", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American musician, radio personality and painter ", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1967", "text": "<PERSON>, Canadian actress", "html": "1967 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian actress", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1969", "text": "<PERSON><PERSON><PERSON><PERSON>, Lithuanian basketball player", "html": "1969 - <a href=\"https://wikipedia.org/wiki/Gintaras_Einikis\" title=\"Gintaras Einikis\"><PERSON><PERSON><PERSON><PERSON></a>, Lithuanian basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Gint<PERSON>s_Einikis\" title=\"Gintaras Einikis\"><PERSON><PERSON><PERSON><PERSON></a>, Lithuanian basketball player", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Gintaras_Einikis"}]}, {"year": "1969", "text": "<PERSON>, American actress", "html": "1969 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1969", "text": "<PERSON>, American actor", "html": "1969 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_(actor)\" title=\"<PERSON> (actor)\"><PERSON></a>, American actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_(actor)\" title=\"<PERSON> (actor)\"><PERSON></a>, American actor", "links": [{"title": "<PERSON> (actor)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_(actor)"}]}, {"year": "1969", "text": "<PERSON>, American wrestler (d. 1991)", "html": "1969 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, American wrestler (d. 1991)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, American wrestler (d. 1991)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1970", "text": "<PERSON>, American actor and producer", "html": "1970 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor and producer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1970", "text": "<PERSON>, Australian footballer and manager", "html": "1970 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian footballer and manager", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian footballer and manager", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1970", "text": "<PERSON>, American basketball player", "html": "1970 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1971", "text": "<PERSON>, American actress and producer", "html": "1971 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress and producer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1972", "text": "<PERSON>, American football player and sportscaster", "html": "1972 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player and sportscaster", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player and sportscaster", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1972", "text": "<PERSON>, Danish-Norwegian author and playwright (d. 2019)", "html": "1972 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Danish-Norwegian author and playwright (d. 2019)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Danish-Norwegian author and playwright (d. 2019)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1972", "text": "<PERSON>, American bass player and songwriter", "html": "1972 - <a href=\"https://wikipedia.org/wiki/<PERSON>(bassist)\" class=\"mw-redirect\" title=\"<PERSON> (bassist)\"><PERSON></a>, American bass player and songwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>(bassist)\" class=\"mw-redirect\" title=\"<PERSON> (bassist)\"><PERSON></a>, American bass player and songwriter", "links": [{"title": "<PERSON> (bassist)", "link": "https://wikipedia.org/wiki/<PERSON>(bassist)"}]}, {"year": "1972", "text": "<PERSON><PERSON>, Japanese singer-songwriter", "html": "1972 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Japanese singer-songwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Japanese singer-songwriter", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1972", "text": "<PERSON>, Dominican baseball player (d. 2010)", "html": "1972 - <a href=\"https://wikipedia.org/wiki/Jos%C3%A9_Lima\" title=\"José <PERSON>\"><PERSON></a>, Dominican baseball player (d. 2010)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Jos%C3%A9_Lima\" title=\"José <PERSON>\"><PERSON></a>, Dominican baseball player (d. 2010)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Jos%C3%A9_Lima"}]}, {"year": "1973", "text": "<PERSON><PERSON><PERSON>, Argentine basketball player", "html": "1973 - <a href=\"https://wikipedia.org/wiki/Rub%C3%A9n_<PERSON><PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Argentine basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Rub%C3%A9n_<PERSON><PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Argentine basketball player", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Rub%C3%A9n_<PERSON><PERSON><PERSON>ski"}]}, {"year": "1974", "text": "<PERSON>, American baseball player (d. 2022)", "html": "1974 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player (d. 2022)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player (d. 2022)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1974", "text": "<PERSON>, English politician", "html": "1974 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English politician", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_Greatrex"}]}, {"year": "1974", "text": "<PERSON>, American actor and singer-songwriter", "html": "1974 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor and singer-songwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor and singer-songwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1974", "text": "<PERSON>, English cricketer", "html": "1974 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(cricketer)\" title=\"<PERSON> (cricketer)\"><PERSON></a>, English cricketer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(cricketer)\" title=\"<PERSON> (cricketer)\"><PERSON></a>, English cricketer", "links": [{"title": "<PERSON> (cricketer)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(cricketer)"}]}, {"year": "1974", "text": "<PERSON>, American-born Hong Kong actor, director, and producer", "html": "1974 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American-born Hong Kong actor, director, and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American-born Hong Kong actor, director, and producer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1975", "text": "<PERSON>, American author", "html": "1975 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1975", "text": "<PERSON><PERSON><PERSON><PERSON><PERSON>, American author and journalist", "html": "1975 - <a href=\"https://wikipedia.org/wiki/Ta-Nehisi_Coates\" title=\"Ta-Nehisi Coates\">Ta-Neh<PERSON> Coates</a>, American author and journalist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Ta-Nehisi_Coates\" title=\"Ta-Nehisi Coates\">Ta-Neh<PERSON> Coates</a>, American author and journalist", "links": [{"title": "Ta-Nehisi Coates", "link": "https://wikipedia.org/wiki/Ta-Nehisi_Coates"}]}, {"year": "1975", "text": "<PERSON>, French actress and singer", "html": "1975 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French actress and singer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French actress and singer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1975", "text": "<PERSON>, Venezuelan baseball player", "html": "1975 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A9n\" title=\"<PERSON>\"><PERSON></a>, Venezuelan baseball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A9n\" title=\"<PERSON>\"><PERSON></a>, Venezuelan baseball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_Guill%C3%A9n"}]}, {"year": "1975", "text": "<PERSON><PERSON>, French skier", "html": "1975 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, French skier", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, French skier", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1975", "text": "<PERSON>, American actor, singer, musician, and composer", "html": "1975 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(actor)\" title=\"<PERSON> (actor)\"><PERSON></a>, American actor, singer, musician, and composer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(actor)\" title=\"<PERSON> (actor)\"><PERSON></a>, American actor, singer, musician, and composer", "links": [{"title": "<PERSON> (actor)", "link": "https://wikipedia.org/wiki/<PERSON>_(actor)"}]}, {"year": "1976", "text": "<PERSON>, British radio and television presenter", "html": "1976 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, British radio and television presenter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, British radio and television presenter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1977", "text": "<PERSON>, Northern Irish goalkeeper and manager", "html": "1977 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Northern Irish goalkeeper and manager", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Northern Irish goalkeeper and manager", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1977", "text": "<PERSON>, American singer-songwriter, guitarist, and producer (d. 2012)", "html": "1977 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter, guitarist, and producer (d. 2012)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter, guitarist, and producer (d. 2012)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1978", "text": "<PERSON><PERSON>gor<PERSON><PERSON>-<PERSON><PERSON><PERSON>, Polish volleyball player", "html": "1978 - <a href=\"https://wikipedia.org/wiki/Ma%C5%82gorzata_Glinka-Mogentale\" title=\"Małgorzata Glinka-Mogentale\">Małgorzata Glinka-Mogentale</a>, Polish volleyball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Ma%C5%82gorzata_Glinka-Mogentale\" title=\"Małgorzata Glinka-Mogentale\">Małgorzata Glinka-Mogentale</a>, Polish volleyball player", "links": [{"title": "Małgorzata Glinka-Mogentale", "link": "https://wikipedia.org/wiki/Ma%C5%82gorzata_Glinka-Mogentale"}]}, {"year": "1978", "text": "<PERSON><PERSON>, American wrestler", "html": "1978 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"Can<PERSON>\"><PERSON><PERSON></a>, American wrestler", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American wrestler", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1978", "text": "<PERSON>, American politician", "html": "1978 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American politician", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1979", "text": "<PERSON>, Australian footballer and coach", "html": "1979 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian footballer and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian footballer and coach", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1979", "text": "<PERSON>, Dutch footballer", "html": "1979 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1980", "text": "<PERSON>, Swiss tennis player", "html": "1980 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swiss tennis player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swiss tennis player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1980", "text": "<PERSON><PERSON><PERSON>, Venezuelan tennis player", "html": "1980 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Venezuelan tennis player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Venezuelan tennis player", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1980", "text": "<PERSON>, American actress", "html": "1980 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Toni Trucks\"><PERSON></a>, American actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Toni Trucks\"><PERSON></a>, American actress", "links": [{"title": "Toni <PERSON>s", "link": "https://wikipedia.org/wiki/<PERSON>_Trucks"}]}, {"year": "1981", "text": "<PERSON><PERSON><PERSON>, Irish author", "html": "1981 - <a href=\"https://wikipedia.org/wiki/Ce<PERSON><PERSON>_Ahern\" title=\"Ce<PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Irish author", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Ce<PERSON><PERSON>_<PERSON>ern\" title=\"Ce<PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Irish author", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Cecelia_Ahern"}]}, {"year": "1981", "text": "<PERSON>, American gymnast", "html": "1981 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American gymnast", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American gymnast", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1982", "text": "<PERSON><PERSON><PERSON>, Ukrainian footballer", "html": "1982 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Ukrainian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Ukrainian footballer", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1982", "text": "<PERSON>, American actress", "html": "1982 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1982", "text": "<PERSON><PERSON>, Canadian ice hockey player", "html": "1982 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Canadian ice hockey player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Canadian ice hockey player", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1982", "text": "<PERSON><PERSON>, American actor", "html": "1982 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American actor", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1982", "text": "<PERSON>, Australian politician", "html": "1982 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian politician", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1982", "text": "<PERSON>, American baseball player", "html": "1982 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1982", "text": "<PERSON>, Canadian-American ice hockey player", "html": "1982 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> St<PERSON>\"><PERSON></a>, Canadian-American ice hockey player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Stastny\"><PERSON></a>, Canadian-American ice hockey player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Yan_<PERSON>astny"}]}, {"year": "1983", "text": "<PERSON><PERSON>, Guinea-Bissau footballer", "html": "1983 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_Forbes\" title=\"<PERSON>iek Forbes\"><PERSON><PERSON></a>, Guinea-Bissau footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_Forbes\" title=\"<PERSON><PERSON> Forbes\"><PERSON><PERSON></a>, Guinea-Bissau footballer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1983", "text": "<PERSON>, American football player and analyst", "html": "1983 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(American_football)\" title=\"<PERSON> (American football)\"><PERSON></a>, American football player and analyst", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(American_football)\" title=\"<PERSON> (American football)\"><PERSON></a>, American football player and analyst", "links": [{"title": "<PERSON> (American football)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(American_football)"}]}, {"year": "1983", "text": "<PERSON><PERSON>, Romanian gymnast", "html": "1983 - <a href=\"https://wikipedia.org/wiki/Andre<PERSON>_R%C4%83ducan\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Romanian gymnast", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Andre<PERSON>_R%C4%83ducan\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Romanian gymnast", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Andreea_R%C4%83ducan"}]}, {"year": "1984", "text": "<PERSON><PERSON>, Greek footballer", "html": "1984 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_Eleftheriou\" title=\"<PERSON><PERSON> Eleftheriou\"><PERSON><PERSON></a>, Greek footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_Eleftheriou\" title=\"<PERSON><PERSON> Eleftheriou\"><PERSON><PERSON></a>, Greek footballer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Georgios_Eleftheriou"}]}, {"year": "1984", "text": "<PERSON><PERSON><PERSON>, American rapper, producer, and actor", "html": "1984 - <a href=\"https://wikipedia.org/wiki/T-Pain\" title=\"T-Pain\"><PERSON><PERSON><PERSON></a>, American rapper, producer, and actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/T-Pain\" title=\"T-Pain\"><PERSON><PERSON><PERSON></a>, American rapper, producer, and actor", "links": [{"title": "T-Pain", "link": "https://wikipedia.org/wiki/T-Pain"}]}, {"year": "1985", "text": "<PERSON>, Australian footballer", "html": "1985 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1985", "text": "<PERSON>, Australian rugby league player", "html": "1985 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(rugby_league)\" title=\"<PERSON> (rugby league)\"><PERSON></a>, Australian rugby league player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(rugby_league)\" title=\"<PERSON> (rugby league)\"><PERSON></a>, Australian rugby league player", "links": [{"title": "<PERSON> (rugby league)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(rugby_league)"}]}, {"year": "1985", "text": "<PERSON><PERSON><PERSON>, Serbian-American author", "html": "1985 - <a href=\"https://wikipedia.org/wiki/T%C3%A9a_<PERSON>\" title=\"T<PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Serbian-American author", "no_year_html": "<a href=\"https://wikipedia.org/wiki/T%C3%A9a_<PERSON>\" title=\"T<PERSON><PERSON>\">T<PERSON><PERSON></a>, Serbian-American author", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/T%C3%A9a_<PERSON>"}]}, {"year": "1985", "text": "<PERSON><PERSON><PERSON>, Uruguayan footballer", "html": "1985 - <a href=\"https://wikipedia.org/wiki/C<PERSON><PERSON>_<PERSON>r%C3%ADguez\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Uruguayan footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>r%C3%ADguez\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Uruguayan footballer", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/C<PERSON>ian_Rodr%C3%ADguez"}]}, {"year": "1986", "text": "<PERSON>, French footballer", "html": "1986 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1986", "text": "<PERSON>, New Zealand cricketer", "html": "1986 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, New Zealand cricketer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, New Zealand cricketer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1986", "text": "<PERSON>, American actor", "html": "1986 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1986", "text": "<PERSON>, Welsh musician and songwriter", "html": "1986 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(British_musician)\" title=\"<PERSON> (British musician)\"><PERSON></a>, Welsh musician and songwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(British_musician)\" title=\"<PERSON> (British musician)\"><PERSON></a>, Welsh musician and songwriter", "links": [{"title": "<PERSON> (British musician)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(British_musician)"}]}, {"year": "1986", "text": "<PERSON><PERSON><PERSON><PERSON>, Colombian footballer", "html": "1986 - <a href=\"https://wikipedia.org/wiki/Cristi%C3%A1n_Zapata\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Colombian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Cristi%C3%A1n_Zapata\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Colombian footballer", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Cristi%C3%A1n_Zapata"}]}, {"year": "1987", "text": "<PERSON><PERSON>, Russian operatic soprano", "html": "1987 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> Garifullina\"><PERSON><PERSON></a>, Russian operatic soprano", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> Garifullina\"><PERSON><PERSON></a>, Russian operatic soprano", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Aida_<PERSON>ari<PERSON>lina"}]}, {"year": "1987", "text": "<PERSON><PERSON>, Curaçaoan baseball player", "html": "1987 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Curaçaoan baseball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Curaçaoan baseball player", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1988", "text": "<PERSON>, Belgian politician", "html": "1988 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Belgian politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Belgian politician", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1988", "text": "<PERSON><PERSON><PERSON><PERSON>, Lithuanian hurdler", "html": "1988 - <a href=\"https://wikipedia.org/wiki/Egl%C4%97_Stai%C5%A1i%C5%ABnait%C4%97\" title=\"Eg<PERSON><PERSON> Staišiūnait<PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Lithuanian hurdler", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Egl%C4%97_Stai%C5%A1i%C5%ABnait%C4%97\" title=\"<PERSON>g<PERSON><PERSON> St<PERSON>š<PERSON>ū<PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Lithuanian hurdler", "links": [{"title": "Eglė <PERSON>", "link": "https://wikipedia.org/wiki/Egl%C4%97_Stai%C5%A1i%C5%ABnait%C4%97"}]}, {"year": "1989", "text": "<PERSON>, American basketball player", "html": "1989 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(basketball)\" title=\"<PERSON> (basketball)\"><PERSON></a>, American basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(basketball)\" title=\"<PERSON> (basketball)\"><PERSON></a>, American basketball player", "links": [{"title": "<PERSON> (basketball)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(basketball)"}]}, {"year": "1989", "text": "<PERSON>, German footballer", "html": "1989 - <a href=\"https://wikipedia.org/wiki/Andr%C3%A9_<PERSON><PERSON>\" title=\"<PERSON>\"><PERSON></a>, German footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Andr%C3%A9_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Andr%C3%A9_<PERSON>s"}]}, {"year": "1991", "text": "<PERSON>, American football player", "html": "1991 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1991", "text": "<PERSON><PERSON><PERSON>, French basketball player", "html": "1991 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, French basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, French basketball player", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1991", "text": "<PERSON>, German javelin thrower", "html": "1991 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%B6hler\" title=\"<PERSON>\"><PERSON></a>, German javelin thrower", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%B6hler\" title=\"<PERSON>\"><PERSON></a>, German javelin thrower", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Thomas_R%C3%B6hler"}]}, {"year": "1992", "text": "<PERSON><PERSON>, French-American basketball player", "html": "1992 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> <PERSON>\"><PERSON><PERSON></a>, French-American basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> <PERSON>\"><PERSON><PERSON></a>, French-American basketball player", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1992", "text": "<PERSON>, American actor and singer", "html": "1992 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor and singer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor and singer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1994", "text": "<PERSON><PERSON>, Russian gymnast", "html": "1994 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Russian gymnast", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Russian gymnast", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1994", "text": "<PERSON>, American basketball player", "html": "1994 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>.\" title=\"<PERSON>.\"><PERSON>.</a>, American basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>.\" title=\"<PERSON>.\"><PERSON>.</a>, American basketball player", "links": [{"title": "<PERSON>.", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>."}]}, {"year": "1996", "text": "<PERSON>, American basketball player", "html": "1996 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1996", "text": "<PERSON>, Australian rugby league player", "html": "1996 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Host\"><PERSON></a>, Australian rugby league player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Host\"><PERSON></a>, Australian rugby league player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1997", "text": "<PERSON><PERSON>, Russian gymnast", "html": "1997 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Russian gymnast", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Russian gymnast", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1997", "text": "<PERSON>, Dutch race car driver", "html": "1997 - <a href=\"https://wikipedia.org/wiki/<PERSON>_V<PERSON>ppen\" title=\"<PERSON> V<PERSON>tappen\"><PERSON></a>, Dutch race car driver", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_V<PERSON>ppen\" title=\"<PERSON> Verstappen\"><PERSON></a>, Dutch race car driver", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_Vers<PERSON>ppen"}]}, {"year": "1998", "text": "<PERSON>, American football player", "html": "1998 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1998", "text": "<PERSON><PERSON>, Japanese actress, singer, model, and television personality", "html": "1998 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Japanese actress, singer, model, and television personality", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Japanese actress, singer, model, and television personality", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON><PERSON><PERSON>"}]}, {"year": "1998", "text": "<PERSON><PERSON><PERSON>, American YouTuber and singer", "html": "1998 - <a href=\"https://wikipedia.org/wiki/Tre<PERSON>_<PERSON>\" title=\"<PERSON>re<PERSON> <PERSON>\"><PERSON><PERSON><PERSON></a>, American YouTuber and singer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Tre<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American YouTuber and singer", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Trevi_<PERSON>"}]}, {"year": "2002", "text": "<PERSON>, Australian actor and model", "html": "2002 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian actor and model", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian actor and model", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2002", "text": "<PERSON>, Croatian tennis player", "html": "2002 - <a href=\"https://wikipedia.org/wiki/Tara_W%C3%BCrth\" title=\"<PERSON>\"><PERSON></a>, Croatian tennis player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Tara_W%C3%BCrth\" title=\"<PERSON>\"><PERSON></a>, Croatian tennis player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Tara_W%C3%BCrth"}]}, {"year": "2002", "text": "<PERSON>, American dancer and actress", "html": "2002 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American dancer and actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American dancer and actress", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}], "Deaths": [{"year": "420", "text": "<PERSON>, Roman priest, theologian, and saint", "html": "420 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Roman priest, theologian, and saint", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Roman priest, theologian, and saint", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "653", "text": "<PERSON><PERSON> of Canterbury, Italian archbishop and saint", "html": "653 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_of_Canterbury\" title=\"<PERSON><PERSON> of Canterbury\"><PERSON><PERSON> of Canterbury</a>, Italian archbishop and saint", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_of_Canterbury\" title=\"<PERSON><PERSON> of Canterbury\"><PERSON><PERSON> of Canterbury</a>, Italian archbishop and saint", "links": [{"title": "<PERSON><PERSON> of Canterbury", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_of_Canterbury"}]}, {"year": "940", "text": "<PERSON>, Chinese general", "html": "940 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Chinese general", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Chinese general", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "954", "text": "<PERSON> of France (b. 920)", "html": "954 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_France\" title=\"<PERSON> IV of France\"><PERSON> of France</a> (b. 920)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_France\" title=\"<PERSON> IV of France\"><PERSON> of France</a> (b. 920)", "links": [{"title": "<PERSON> of France", "link": "https://wikipedia.org/wiki/Louis_IV_of_France"}]}, {"year": "1101", "text": "<PERSON><PERSON><PERSON>, Italian archbishop", "html": "1101 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_IV_(archbishop_of_Milan)\" title=\"<PERSON><PERSON><PERSON> IV (archbishop of Milan)\"><PERSON><PERSON><PERSON> IV</a>, Italian archbishop", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_IV_(archbishop_of_Milan)\" title=\"<PERSON><PERSON><PERSON> IV (archbishop of Milan)\"><PERSON><PERSON><PERSON> IV</a>, Italian archbishop", "links": [{"title": "<PERSON><PERSON><PERSON> (archbishop of Milan)", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_IV_(archbishop_of_Milan)"}]}, {"year": "1246", "text": "<PERSON><PERSON><PERSON> Vladimir (b. 1191)", "html": "1246 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_II_of_Vladimir\" title=\"<PERSON><PERSON><PERSON> II of Vladimir\"><PERSON><PERSON><PERSON> II of Vladimir</a> (b. 1191)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_II_of_Vladimir\" title=\"<PERSON><PERSON><PERSON> II of Vladimir\"><PERSON><PERSON><PERSON> <PERSON> of Vladimir</a> (b. 1191)", "links": [{"title": "<PERSON><PERSON><PERSON> II of Vladimir", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_II_of_Vladimir"}]}, {"year": "1288", "text": "<PERSON><PERSON><PERSON> <PERSON> the <PERSON>, Polish prince, Duke of Łęczyca, Sieradz, Kraków, Sandomierz (b. 1241)", "html": "1288 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_II_the_Black\" title=\"<PERSON><PERSON><PERSON> II the Black\"><PERSON><PERSON><PERSON> <PERSON> the Black</a>, Polish prince, Duke of <a href=\"https://wikipedia.org/wiki/%C5%81%C4%99czyca\" title=\"Łęczyca\">Łęczyca</a>, <a href=\"https://wikipedia.org/wiki/Sieradz\" title=\"Sieradz\">Si<PERSON>dz</a>, <a href=\"https://wikipedia.org/wiki/Krak%C3%B3w\" title=\"Kraków\">Kraków</a>, <a href=\"https://wikipedia.org/wiki/Sandomierz\" title=\"Sandomierz\">Sandomierz</a> (b. 1241)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_II_the_Black\" title=\"<PERSON><PERSON><PERSON> II the Black\"><PERSON><PERSON><PERSON> II the Black</a>, Polish prince, Duke of <a href=\"https://wikipedia.org/wiki/%C5%81%C4%99czyca\" title=\"Łęczyca\">Łęczyca</a>, <a href=\"https://wikipedia.org/wiki/Sieradz\" title=\"Sieradz\">Sieradz</a>, <a href=\"https://wikipedia.org/wiki/Krak%C3%B3w\" title=\"Kraków\">Kraków</a>, <a href=\"https://wikipedia.org/wiki/Sandomierz\" title=\"Sandomierz\">Sandomierz</a> (b. 1241)", "links": [{"title": "Leszek II the Black", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_II_the_Black"}, {"title": "Łęczyca", "link": "https://wikipedia.org/wiki/%C5%81%C4%99czyca"}, {"title": "Sieradz", "link": "https://wikipedia.org/wiki/Sieradz"}, {"title": "Kraków", "link": "https://wikipedia.org/wiki/Krak%C3%B3w"}, {"title": "Sandomierz", "link": "https://wikipedia.org/wiki/Sandomierz"}]}, {"year": "1376", "text": "<PERSON> of Vianden, German countess", "html": "1376 - <a href=\"https://wikipedia.org/wiki/Adelaide_of_Vianden\" title=\"Adelaide of Vianden\"><PERSON> of Vianden</a>, German countess", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Adelaide_of_Vianden\" title=\"Adelaide of Vianden\"><PERSON> of Vianden</a>, German countess", "links": [{"title": "Adelaide of Vianden", "link": "https://wikipedia.org/wiki/Adelaide_of_Vianden"}]}, {"year": "1440", "text": "<PERSON>, 3rd Baron <PERSON>, Welsh soldier and politician (b. 1362)", "html": "1440 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_3rd_Baron_<PERSON>_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>, 3rd Baron <PERSON>\"><PERSON>, 3rd Baron <PERSON></a>, Welsh soldier and politician (b. 1362)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_3rd_Baron_<PERSON>_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>, 3rd Baron <PERSON>\"><PERSON>, 3rd Baron <PERSON></a>, Welsh soldier and politician (b. 1362)", "links": [{"title": "<PERSON>, 3rd Baron <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_3rd_Baron_<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1487", "text": "<PERSON>, 1st Baron <PERSON>, English politician, Lord Lieutenant of Ireland (b. 1400)", "html": "1487 - <a href=\"https://wikipedia.org/wiki/<PERSON>,_1st_Baron_<PERSON>\" title=\"<PERSON>, 1st Baron <PERSON>\"><PERSON>, 1st Baron <PERSON></a>, English politician, <a href=\"https://wikipedia.org/wiki/Lord_Lieutenant_of_Ireland\" title=\"Lord Lieutenant of Ireland\">Lord Lieutenant of Ireland</a> (b. 1400)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>,_1st_Baron_<PERSON>\" title=\"<PERSON>, 1st Baron <PERSON>\"><PERSON>, 1st Baron <PERSON></a>, English politician, <a href=\"https://wikipedia.org/wiki/Lord_Lieutenant_of_Ireland\" title=\"Lord Lieutenant of Ireland\">Lord Lieutenant of Ireland</a> (b. 1400)", "links": [{"title": "<PERSON>, 1st Baron <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>,_1st_Baron_<PERSON>"}, {"title": "Lord Lieutenant of Ireland", "link": "https://wikipedia.org/wiki/Lord_Lieutenant_of_Ireland"}]}, {"year": "1551", "text": "<PERSON><PERSON>, Japanese daimyō (b. 1507)", "html": "1551 - <a href=\"https://wikipedia.org/wiki/%C5%8C<PERSON>_<PERSON>shi<PERSON>ka\" title=\"<PERSON><PERSON>shi<PERSON>\"><PERSON><PERSON></a>, Japanese daimyō (b. 1507)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/%C5%8C<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>shi<PERSON>\"><PERSON><PERSON></a>, Japanese daimyō (b. 1507)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/%C5%8C<PERSON>_<PERSON><PERSON><PERSON>"}]}, {"year": "1560", "text": "<PERSON><PERSON><PERSON>, Spanish theologian (b. 1525)", "html": "1560 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Spanish theologian (b. 1525)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Spanish theologian (b. 1525)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>o"}]}, {"year": "1572", "text": "<PERSON>, 4th Duke of Gandía, Spanish priest and saint, 3rd Superior General of the Society of Jesus (b. 1510)", "html": "1572 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_4th_Duke_of_Gand%C3%ADa\" class=\"mw-redirect\" title=\"<PERSON>, 4th Duke of Gandía\"><PERSON>, 4th Duke of Gandía</a>, Spanish priest and saint, 3rd <a href=\"https://wikipedia.org/wiki/Superior_General_of_the_Society_of_Jesus\" class=\"mw-redirect\" title=\"Superior General of the Society of Jesus\">Superior General of the Society of Jesus</a> (b. 1510)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>,_4th_Duke_of_Gand%C3%ADa\" class=\"mw-redirect\" title=\"<PERSON>, 4th Duke of Gandía\"><PERSON>, 4th Duke of Gandía</a>, Spanish priest and saint, 3rd <a href=\"https://wikipedia.org/wiki/Superior_General_of_the_Society_of_Jesus\" class=\"mw-redirect\" title=\"Superior General of the Society of Jesus\">Superior General of the Society of Jesus</a> (b. 1510)", "links": [{"title": "<PERSON>, 4th Duke of Gandía", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_4th_Duke_of_Gand%C3%ADa"}, {"title": "Superior General of the Society of Jesus", "link": "https://wikipedia.org/wiki/Superior_General_of_the_Society_of_Jesus"}]}, {"year": "1581", "text": "<PERSON>, French diplomat and reformer (b. 1518)", "html": "1581 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French diplomat and reformer (b. 1518)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French diplomat and reformer (b. 1518)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1626", "text": "<PERSON><PERSON><PERSON><PERSON>, Chinese emperor (b. 1559)", "html": "1626 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Chinese emperor (b. 1559)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Chinese emperor (b. 1559)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>ci"}]}, {"year": "1628", "text": "<PERSON><PERSON><PERSON>, 1st Baron <PERSON>, English poet and politician, Chancellor of the Exchequer (b. 1554)", "html": "1628 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>,_1st_Baron_<PERSON>\" title=\"<PERSON><PERSON><PERSON>, 1st Baron <PERSON>\"><PERSON><PERSON><PERSON>, 1st Baron <PERSON></a>, English poet and politician, <a href=\"https://wikipedia.org/wiki/Chancellor_of_the_Exchequer\" title=\"Chancellor of the Exchequer\">Chancellor of the Exchequer</a> (b. 1554)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>,_1st_Baron_<PERSON>\" title=\"<PERSON><PERSON><PERSON>, 1st Baron <PERSON>\"><PERSON><PERSON><PERSON>, 1st Baron <PERSON></a>, English poet and politician, <a href=\"https://wikipedia.org/wiki/Chancellor_of_the_Exchequer\" title=\"Chancellor of the Exchequer\">Chancellor of the Exchequer</a> (b. 1554)", "links": [{"title": "<PERSON><PERSON><PERSON>, 1st Baron <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>,_1st_Baron_<PERSON>"}, {"title": "Chancellor of the Exchequer", "link": "https://wikipedia.org/wiki/Chancellor_of_the_Exchequer"}]}, {"year": "1770", "text": "<PERSON>, 1st Baron <PERSON>, English politician and diplomat, Secretary of State for the Southern Department (b. 1695)", "html": "1770 - <a href=\"https://wikipedia.org/wiki/<PERSON>,_1st_Baron_<PERSON>\" title=\"<PERSON>, 1st Baron <PERSON>\"><PERSON>, 1st Baron <PERSON></a>, English politician and diplomat, <a href=\"https://wikipedia.org/wiki/Secretary_of_State_for_the_Southern_Department\" title=\"Secretary of State for the Southern Department\">Secretary of State for the Southern Department</a> (b. 1695)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>,_1st_Baron_<PERSON>\" title=\"<PERSON>, 1st Baron <PERSON>\"><PERSON>, 1st Baron <PERSON></a>, English politician and diplomat, <a href=\"https://wikipedia.org/wiki/Secretary_of_State_for_the_Southern_Department\" title=\"Secretary of State for the Southern Department\">Secretary of State for the Southern Department</a> (b. 1695)", "links": [{"title": "<PERSON>, 1st Baron <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_1st_Baron_<PERSON>"}, {"title": "Secretary of State for the Southern Department", "link": "https://wikipedia.org/wiki/Secretary_of_State_for_the_Southern_Department"}]}, {"year": "1770", "text": "<PERSON>, English-American priest and theologian (b. 1714)", "html": "1770 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English-American priest and theologian (b. 1714)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English-American priest and theologian (b. 1714)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1865", "text": "<PERSON>, Italian poet and scholar (b. 1800)", "html": "1865 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian poet and scholar (b. 1800)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian poet and scholar (b. 1800)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1866", "text": "<PERSON> <PERSON><PERSON><PERSON>, Swedo-Finnish treasurer of Tavastia province, manor host, and paternal grandfather of President <PERSON><PERSON> <PERSON><PERSON> (b. 1804)", "html": "1866 - <a href=\"https://wikipedia.org/wiki/Per_<PERSON><PERSON><PERSON>_<PERSON>hufvud_af_<PERSON>\" title=\"<PERSON> <PERSON><PERSON><PERSON>vud af <PERSON>\"><PERSON> <PERSON><PERSON><PERSON> a<PERSON></a>, Swedo-Finnish treasurer of <a href=\"https://wikipedia.org/wiki/Tavastia_(historical_province)\" title=\"Tavastia (historical province)\">Tavastia province</a>, manor host, and paternal grandfather of President <a href=\"https://wikipedia.org/wiki/P._E._Svinhufvud\" class=\"mw-redirect\" title=\"P. E. Svinhufvud\"><PERSON><PERSON> <PERSON><PERSON> Svinhufvud</a> (b. 1804)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Per_<PERSON><PERSON><PERSON>_<PERSON>hufvud_af_<PERSON>\" title=\"Per <PERSON><PERSON><PERSON>hufvud af <PERSON>\"><PERSON> <PERSON><PERSON><PERSON> a<PERSON></a>, Swedo-Finnish treasurer of <a href=\"https://wikipedia.org/wiki/Tavastia_(historical_province)\" title=\"Tavastia (historical province)\">Tavastia province</a>, manor host, and paternal grandfather of President <a href=\"https://wikipedia.org/wiki/P._E._Svinhufvud\" class=\"mw-redirect\" title=\"P. E. Svinhufvud\"><PERSON><PERSON> <PERSON><PERSON>hufvud</a> (b. 1804)", "links": [{"title": "<PERSON> <PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Per_<PERSON><PERSON><PERSON>_<PERSON>vinhufvud_af_<PERSON>"}, {"title": "Tavastia (historical province)", "link": "https://wikipedia.org/wiki/Tavastia_(historical_province)"}, {"title": "<PERSON><PERSON> <PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>._<PERSON><PERSON>_Svinhufvud"}]}, {"year": "1891", "text": "<PERSON>, French general and politician, French Minister of War (b. 1837)", "html": "1891 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French general and politician, <a href=\"https://wikipedia.org/wiki/Minister_of_Defence_(France)\" class=\"mw-redirect\" title=\"Minister of Defence (France)\">French Minister of War</a> (b. 1837)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French general and politician, <a href=\"https://wikipedia.org/wiki/Minister_of_Defence_(France)\" class=\"mw-redirect\" title=\"Minister of Defence (France)\">French Minister of War</a> (b. 1837)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Minister of Defence (France)", "link": "https://wikipedia.org/wiki/Minister_of_Defence_(France)"}]}, {"year": "1897", "text": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> of Lisieux, French nun and saint (b. 1873)", "html": "1897 - <a href=\"https://wikipedia.org/wiki/Th%C3%A9r%C3%A8se_of_Lisieux\" title=\"Th<PERSON><PERSON><PERSON><PERSON> of Lisieux\"><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> of Lisieux</a>, French nun and saint (b. 1873)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Th%C3%A9r%C3%A8se_of_Lisieux\" title=\"Th<PERSON><PERSON><PERSON><PERSON> of Lisieux\"><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> of Lisieux</a>, French nun and saint (b. 1873)", "links": [{"title": "Thér<PERSON><PERSON> of Lisieux", "link": "https://wikipedia.org/wiki/Th%C3%A9r%C3%A8se_of_Lisieux"}]}, {"year": "1910", "text": "<PERSON>, French mathematician and engineer (b. 1838)", "html": "1910 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A9vy\" title=\"<PERSON>\"><PERSON></a>, French mathematician and engineer (b. 1838)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A9vy\" title=\"<PERSON>\"><PERSON></a>, French mathematician and engineer (b. 1838)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Maurice_L%C3%A9vy"}]}, {"year": "1921", "text": "<PERSON><PERSON>, Lithuanian Jewish anarchist (b. 1887)", "html": "1921 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Lithuanian Jewish anarchist (b. 1887)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Lithuanian Jewish anarchist (b. 1887)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1942", "text": "<PERSON><PERSON><PERSON>, German captain and pilot (b. 1919)", "html": "1942 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, German captain and pilot (b. 1919)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, German captain and pilot (b. 1919)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1943", "text": "<PERSON>, German-American sociologist and economist (b. 1864)", "html": "1943 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German-American sociologist and economist (b. 1864)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German-American sociologist and economist (b. 1864)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1946", "text": "<PERSON><PERSON><PERSON>, Japanese general and politician, Governor of Hong Kong (b. 1887)", "html": "1946 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese general and politician, <a href=\"https://wikipedia.org/wiki/Governor_of_Hong_Kong\" title=\"Governor of Hong Kong\">Governor of Hong Kong</a> (b. 1887)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese general and politician, <a href=\"https://wikipedia.org/wiki/Governor_of_Hong_Kong\" title=\"Governor of Hong Kong\">Governor of Hong Kong</a> (b. 1887)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}, {"title": "Governor of Hong Kong", "link": "https://wikipedia.org/wiki/Governor_of_Hong_Kong"}]}, {"year": "1955", "text": "<PERSON>, American actor (b. 1931)", "html": "1955 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor (b. 1931)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor (b. 1931)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1959", "text": "<PERSON>, Australian politician, 28th Premier of South Australia (b. 1877)", "html": "1959 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian politician, 28th <a href=\"https://wikipedia.org/wiki/Premier_of_South_Australia\" title=\"Premier of South Australia\">Premier of South Australia</a> (b. 1877)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian politician, 28th <a href=\"https://wikipedia.org/wiki/Premier_of_South_Australia\" title=\"Premier of South Australia\">Premier of South Australia</a> (b. 1877)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Premier of South Australia", "link": "https://wikipedia.org/wiki/Premier_of_South_Australia"}]}, {"year": "1961", "text": "<PERSON><PERSON><PERSON>, Canadian scholar and politician, 20th Lieutenant Governor of Quebec (b. 1888)", "html": "1961 - <a href=\"https://wikipedia.org/wiki/On%C3%A9sime_<PERSON>\" title=\"Onés<PERSON> Gagnon\"><PERSON><PERSON><PERSON></a>, Canadian scholar and politician, 20th <a href=\"https://wikipedia.org/wiki/Lieutenant_Governor_of_Quebec\" title=\"Lieutenant Governor of Quebec\">Lieutenant Governor of Quebec</a> (b. 1888)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/On%C3%A9sime_<PERSON>\" title=\"Onés<PERSON> Gagnon\"><PERSON><PERSON><PERSON></a>, Canadian scholar and politician, 20th <a href=\"https://wikipedia.org/wiki/Lieutenant_Governor_of_Quebec\" title=\"Lieutenant Governor of Quebec\">Lieutenant Governor of Quebec</a> (b. 1888)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/On%C3%A9sime_<PERSON>non"}, {"title": "Lieutenant Governor of Quebec", "link": "https://wikipedia.org/wiki/Lieutenant_Governor_of_Quebec"}]}, {"year": "1973", "text": "<PERSON>, Canadian photographer and author (b. 1902)", "html": "1973 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian photographer and author (b. 1902)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian photographer and author (b. 1902)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1974", "text": "<PERSON>, Chilean general and politician, Chilean Minister of Defense (b. 1915)", "html": "1974 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Chilean general and politician, <a href=\"https://wikipedia.org/wiki/Ministry_of_National_Defense_(Chile)\" title=\"Ministry of National Defense (Chile)\">Chilean Minister of Defense</a> (b. 1915)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Chilean general and politician, <a href=\"https://wikipedia.org/wiki/Ministry_of_National_Defense_(Chile)\" title=\"Ministry of National Defense (Chile)\">Chilean Minister of Defense</a> (b. 1915)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Ministry of National Defense (Chile)", "link": "https://wikipedia.org/wiki/Ministry_of_National_Defense_(Chile)"}]}, {"year": "1977", "text": "<PERSON>, American singer and guitarist (b. 1924)", "html": "1977 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Mary Ford\"><PERSON></a>, American singer and guitarist (b. 1924)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Mary Ford\"><PERSON></a>, American singer and guitarist (b. 1924)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1978", "text": "<PERSON>, American actor and ventriloquist (b. 1903)", "html": "1978 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor and ventriloquist (b. 1903)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor and ventriloquist (b. 1903)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Edgar_<PERSON>"}]}, {"year": "1985", "text": "<PERSON>, American seismologist and physicist (b. 1900)", "html": "1985 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, American seismologist and physicist (b. 1900)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, American seismologist and physicist (b. 1900)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1985", "text": "<PERSON>, French actress (b. 1921)", "html": "1985 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French actress (b. 1921)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French actress (b. 1921)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>ore<PERSON>"}]}, {"year": "1986", "text": "<PERSON>, Hungarian-British economist (b. 1908)", "html": "1986 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Hungarian-British economist (b. 1908)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Hungarian-British economist (b. 1908)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1987", "text": "<PERSON>, American author and screenwriter (b. 1913)", "html": "1987 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author and screenwriter (b. 1913)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON> Bester\"><PERSON></a>, American author and screenwriter (b. 1913)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1988", "text": "<PERSON>, American race car driver (b. 1946)", "html": "1988 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>\" title=\"<PERSON>\"><PERSON></a>, American race car driver (b. 1946)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>\" title=\"<PERSON>\"><PERSON></a>, American race car driver (b. 1946)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1989", "text": "<PERSON>, American LGBT rights activist from Missouri (b. 1936)", "html": "1989 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American LGBT rights activist from Missouri (b. 1936)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American LGBT rights activist from Missouri (b. 1936)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1989", "text": "<PERSON>, American composer and critic (b. 1896)", "html": "1989 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American composer and critic (b. 1896)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American composer and critic (b. 1896)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Virgil_Thomson"}]}, {"year": "1990", "text": "<PERSON>, American race car driver (b. 1968)", "html": "1990 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American race car driver (b. 1968)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>\" title=\"<PERSON>\"><PERSON></a>, American race car driver (b. 1968)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1990", "text": "<PERSON>, Polish-Canadian journalist and author (b. 1930)", "html": "1990 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Polish-Canadian journalist and author (b. 1930)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Polish-Canadian journalist and author (b. 1930)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1990", "text": "<PERSON>, Australian novelist, poet, and playwright, Nobel Prize laureate (b. 1912)", "html": "1990 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian novelist, poet, and playwright, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Literature\" title=\"Nobel Prize in Literature\">Nobel Prize</a> laureate (b. 1912)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian novelist, poet, and playwright, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Literature\" title=\"Nobel Prize in Literature\">Nobel Prize</a> laureate (b. 1912)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Nobel Prize in Literature", "link": "https://wikipedia.org/wiki/Nobel_Prize_in_Literature"}]}, {"year": "1991", "text": "<PERSON><PERSON>, Serbian singer-songwriter (b. 1938)", "html": "1991 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_Zdravkovi%C4%87\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Serbian singer-songwriter (b. 1938)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Tom<PERSON>_Zdravkovi%C4%87\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Serbian singer-songwriter (b. 1938)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Toma_Zdravkovi%C4%87"}]}, {"year": "1994", "text": "<PERSON>, French microbiologist and virologist, Nobel Prize laureate (b. 1902)", "html": "1994 - <a href=\"https://wikipedia.org/wiki/Andr%C3%A<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French microbiologist and virologist, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Physiology_or_Medicine\" title=\"Nobel Prize in Physiology or Medicine\">Nobel Prize</a> laureate (b. 1902)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Andr%C3%A<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French microbiologist and virologist, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Physiology_or_Medicine\" title=\"Nobel Prize in Physiology or Medicine\">Nobel Prize</a> laureate (b. 1902)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Andr%C3%A9_<PERSON>_<PERSON>"}, {"title": "Nobel Prize in Physiology or Medicine", "link": "https://wikipedia.org/wiki/Nobel_Prize_in_Physiology_or_Medicine"}]}, {"year": "1998", "text": "<PERSON>, English actor (b. 1912)", "html": "1998 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Goring\"><PERSON></a>, English actor (b. 1912)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Goring\"><PERSON></a>, English actor (b. 1912)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1998", "text": "<PERSON>, American baseball player and poet (b. 1953)", "html": "1998 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player and poet (b. 1953)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player and poet (b. 1953)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1998", "text": "<PERSON>, American soldier and author (b. 1912)", "html": "1998 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American soldier and author (b. 1912)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American soldier and author (b. 1912)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "2002", "text": "<PERSON><PERSON><PERSON>, Swedish race car driver and mountaineer (b. 1966)", "html": "2002 - <a href=\"https://wikipedia.org/wiki/G%C3%B6<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Swedish race car driver and mountaineer (b. 1966)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/G%C3%B6<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Swedish race car driver and mountaineer (b. 1966)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/G%C3%B6<PERSON>_<PERSON>p"}]}, {"year": "2002", "text": "<PERSON><PERSON><PERSON>, Swiss lawyer and politician, 63rd President of the Swiss Confederation (b. 1913)", "html": "2002 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Swiss lawyer and politician, 63rd <a href=\"https://wikipedia.org/wiki/President_of_the_Swiss_Confederation\" title=\"President of the Swiss Confederation\">President of the Swiss Confederation</a> (b. 1913)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Swiss lawyer and politician, 63rd <a href=\"https://wikipedia.org/wiki/President_of_the_Swiss_Confederation\" title=\"President of the Swiss Confederation\">President of the Swiss Confederation</a> (b. 1913)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>"}, {"title": "President of the Swiss Confederation", "link": "https://wikipedia.org/wiki/President_of_the_Swiss_Confederation"}]}, {"year": "2003", "text": "<PERSON>, American activist, founded Your Black Muslim Bakery (b. 1935)", "html": "2003 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American activist, founded <a href=\"https://wikipedia.org/wiki/Your_Black_Muslim_Bakery\" title=\"Your Black Muslim Bakery\">Your Black Muslim Bakery</a> (b. 1935)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American activist, founded <a href=\"https://wikipedia.org/wiki/Your_Black_Muslim_Bakery\" title=\"Your Black Muslim Bakery\">Your Black Muslim Bakery</a> (b. 1935)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Your Black Muslim Bakery", "link": "https://wikipedia.org/wiki/Your_Black_Muslim_Bakery"}]}, {"year": "2003", "text": "<PERSON>, American singer-songwriter and guitarist (b. 1939)", "html": "2003 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(musician)\" title=\"<PERSON> (musician)\"><PERSON></a>, American singer-songwriter and guitarist (b. 1939)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(musician)\" title=\"<PERSON> (musician)\"><PERSON></a>, American singer-songwriter and guitarist (b. 1939)", "links": [{"title": "<PERSON> (musician)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(musician)"}]}, {"year": "2003", "text": "<PERSON>, American lawyer and businessman (b. 1944)", "html": "2003 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and businessman (b. 1944)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and businessman (b. 1944)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "2004", "text": "<PERSON><PERSON><PERSON>, Sri Lankan actor, director, and politician (b. 1936)", "html": "2004 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON> F<PERSON>\"><PERSON><PERSON><PERSON></a>, Sri Lankan actor, director, and politician (b. 1936)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Sri Lankan actor, director, and politician (b. 1936)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Gamini_Fonseka"}]}, {"year": "2004", "text": "<PERSON>, American director and songwriter (b. 1935)", "html": "2004 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American director and songwriter (b. 1935)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American director and songwriter (b. 1935)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "2004", "text": "<PERSON>, English director, producer, and screenwriter (b. 1915)", "html": "2004 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English director, producer, and screenwriter (b. 1915)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English director, producer, and screenwriter (b. 1915)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2008", "text": "<PERSON><PERSON> <PERSON><PERSON>, Singaporean lawyer and politician (b. 1926)", "html": "2008 - <a href=\"https://wikipedia.org/wiki/J._<PERSON><PERSON>_<PERSON>nam\" title=\"J<PERSON> <PERSON>. <PERSON>\"><PERSON><PERSON> <PERSON><PERSON></a>, Singaporean lawyer and politician (b. 1926)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/J._<PERSON><PERSON>_<PERSON>\" title=\"J<PERSON> <PERSON>. <PERSON>\"><PERSON><PERSON> <PERSON><PERSON></a>, Singaporean lawyer and politician (b. 1926)", "links": [{"title": "<PERSON><PERSON> <PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON><PERSON>nam"}]}, {"year": "2010", "text": "<PERSON>, American screenwriter and producer (b. 1941)", "html": "2010 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American screenwriter and producer (b. 1941)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American screenwriter and producer (b. 1941)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "2011", "text": "<PERSON><PERSON>, American-Yemeni terrorist (b. 1971)", "html": "2011 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American-Yemeni terrorist (b. 1971)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American-Yemeni terrorist (b. 1971)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "2011", "text": "<PERSON>, Canadian-American immunologist and biologist, Nobel Prize laureate (b. 1943)", "html": "2011 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian-American immunologist and biologist, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Physiology_or_Medicine\" title=\"Nobel Prize in Physiology or Medicine\">Nobel Prize</a> laureate (b. 1943)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian-American immunologist and biologist, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Physiology_or_Medicine\" title=\"Nobel Prize in Physiology or Medicine\">Nobel Prize</a> laureate (b. 1943)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "Nobel Prize in Physiology or Medicine", "link": "https://wikipedia.org/wiki/Nobel_Prize_in_Physiology_or_Medicine"}]}, {"year": "2012", "text": "<PERSON><PERSON><PERSON>, Austrian actor and producer (b. 1922)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Austrian actor and producer (b. 1922)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Austrian actor and producer (b. 1922)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "2012", "text": "<PERSON>, American biologist, academic, and politician (b. 1917)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American biologist, academic, and politician (b. 1917)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American biologist, academic, and politician (b. 1917)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2012", "text": "<PERSON>, American wrestler and engineer (b. 1948)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American wrestler and engineer (b. 1948)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American wrestler and engineer (b. 1948)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2012", "text": "<PERSON>, American librarian (b. 1913)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American librarian (b. 1913)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American librarian (b. 1913)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "2012", "text": "<PERSON>, Canadian-American figure skater (b. 1928)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian-American figure skater (b. 1928)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian-American figure skater (b. 1928)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "2012", "text": "<PERSON>, Croatian lawyer and politician, 8th Speaker of the Croatian Parliament (b. 1956)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/<PERSON>_%C5%A0prem\" title=\"<PERSON>\"><PERSON></a>, Croatian lawyer and politician, 8th <a href=\"https://wikipedia.org/wiki/Speaker_of_the_Croatian_Parliament\" title=\"Speaker of the Croatian Parliament\">Speaker of the Croatian Parliament</a> (b. 1956)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_%C5%A0prem\" title=\"<PERSON>\"><PERSON></a>, Croatian lawyer and politician, 8th <a href=\"https://wikipedia.org/wiki/Speaker_of_the_Croatian_Parliament\" title=\"Speaker of the Croatian Parliament\">Speaker of the Croatian Parliament</a> (b. 1956)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Boris_%C5%A0prem"}, {"title": "Speaker of the Croatian Parliament", "link": "https://wikipedia.org/wiki/Speaker_of_the_Croatian_Parliament"}]}, {"year": "2013", "text": "<PERSON>, Australian educator and politician (b. 1942)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian educator and politician (b. 1942)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian educator and politician (b. 1942)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2014", "text": "<PERSON>, American physicist and engineer, Nobel Prize laureate (b. 1927)", "html": "2014 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American physicist and engineer, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Physics\" title=\"Nobel Prize in Physics\">Nobel Prize</a> laureate (b. 1927)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American physicist and engineer, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Physics\" title=\"Nobel Prize in Physics\">Nobel Prize</a> laureate (b. 1927)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "Nobel Prize in Physics", "link": "https://wikipedia.org/wiki/Nobel_Prize_in_Physics"}]}, {"year": "2015", "text": "<PERSON>, Italian-Swiss physicist and academic (b. 1941)", "html": "2015 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian-Swiss physicist and academic (b. 1941)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian-Swiss physicist and academic (b. 1941)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2015", "text": "<PERSON>, French businessman (b. 1951)", "html": "2015 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(businessman)\" title=\"<PERSON> (businessman)\"><PERSON></a>, French businessman (b. 1951)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(businessman)\" title=\"<PERSON> (businessman)\"><PERSON></a>, French businessman (b. 1951)", "links": [{"title": "<PERSON> (businessman)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(businessman)"}]}, {"year": "2015", "text": "<PERSON><PERSON><PERSON>, Swedish author and critic (b. 1947)", "html": "2015 - <a href=\"https://wikipedia.org/wiki/G%C3%B6ran_H%C3%A4gg\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Swedish author and critic (b. 1947)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/G%C3%B6ran_H%C3%A4gg\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Swedish author and critic (b. 1947)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/G%C3%B6ran_H%C3%A4gg"}]}, {"year": "2017", "text": "<PERSON>, American game show host (b. 1921)", "html": "2017 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Monty Hall\"><PERSON></a>, American game show host (b. 1921)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"Monty Hall\"><PERSON></a>, American game show host (b. 1921)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2017", "text": "<PERSON>, Russian-American mathematician (b. 1966)", "html": "2017 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian-American mathematician (b. 1966)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian-American mathematician (b. 1966)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Vladimir_<PERSON>oevodsky"}]}, {"year": "2018", "text": "<PERSON>, Danish rock musician (b. 1945)", "html": "2018 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Danish rock musician (b. 1945)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Danish rock musician (b. 1945)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2018", "text": "<PERSON>, British television presenter and actor (b. 1942)", "html": "2018 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, British television presenter and actor (b. 1942)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, British television presenter and actor (b. 1942)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2018", "text": "<PERSON>, Polish resistance fighter during the Second World War and Holocaust educator. (b. 1925)", "html": "2018 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Polish resistance fighter during the <a href=\"https://wikipedia.org/wiki/World_War_II\" title=\"World War II\">Second World War</a> and <a href=\"https://wikipedia.org/wiki/The_Holocaust\" title=\"The Holocaust\">Holocaust</a> educator. (b. 1925)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Polish resistance fighter during the <a href=\"https://wikipedia.org/wiki/World_War_II\" title=\"World War II\">Second World War</a> and <a href=\"https://wikipedia.org/wiki/The_Holocaust\" title=\"The Holocaust\">Holocaust</a> educator. (b. 1925)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "World War II", "link": "https://wikipedia.org/wiki/World_War_II"}, {"title": "The Holocaust", "link": "https://wikipedia.org/wiki/The_Holocaust"}]}, {"year": "2019", "text": "<PERSON>, British research scientist who proved fish feel pain (b. 1967)", "html": "2019 - <a href=\"https://wikipedia.org/wiki/Victoria_Braithwaite\" title=\"Victoria Braithwaite\"><PERSON></a>, British research scientist who proved fish feel pain (b. 1967)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Victoria_Braithwaite\" title=\"Victoria Braithwaite\"><PERSON></a>, British research scientist who proved fish feel pain (b. 1967)", "links": [{"title": "Victoria Braithwaite", "link": "https://wikipedia.org/wiki/Victoria_Braithwaite"}]}, {"year": "2021", "text": "<PERSON><PERSON>, Japanese composer and orchestrator (b. 1931)", "html": "2021 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Japanese composer and orchestrator (b. 1931)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Japanese composer and orchestrator (b. 1931)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>giyama"}]}, {"year": "2024", "text": "<PERSON>, American actor, singer and songwriter (b. 1976)", "html": "2024 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor, singer and songwriter (b. 1976)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor, singer and songwriter (b. 1976)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>l"}]}, {"year": "2024", "text": "<PERSON><PERSON><PERSON>, Congolese-American basketball player (b. 1966)", "html": "2024 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON> Mutombo\"><PERSON><PERSON><PERSON></a>, Congolese-American basketball player (b. 1966)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON> Mutombo\"><PERSON><PERSON><PERSON></a>, Congolese-American basketball player (b. 1966)", "links": [{"title": "Dikembe Mutombo", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "2024", "text": "<PERSON><PERSON><PERSON>, Nicaraguan military leader (b. 1947)", "html": "2024 - <a href=\"https://wikipedia.org/wiki/Hu<PERSON><PERSON>_Ortega\" title=\"<PERSON><PERSON><PERSON> Ortega\"><PERSON><PERSON><PERSON></a>, Nicaraguan military leader (b. 1947)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Hu<PERSON><PERSON>_Ortega\" title=\"<PERSON><PERSON><PERSON> Ortega\"><PERSON><PERSON><PERSON></a>, Nicaraguan military leader (b. 1947)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Hu<PERSON><PERSON>_Or<PERSON>ga"}]}, {"year": "2024", "text": "<PERSON>, American actor and cabaret singer (b. 1954)", "html": "2024 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor and cabaret singer (b. 1954)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor and cabaret singer (b. 1954)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2024", "text": "<PERSON>, American baseball player and manager (b. 1941)", "html": "2024 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player and manager (b. 1941)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player and manager (b. 1941)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}]}}